-- =====================================================
-- Table: AIM_VENDORS
-- Description: Vendor master table
-- Converted from SQL Server to Oracle
-- =====================================================

-- Drop table if exists
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE AIM_VENDORS CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- Create table
CREATE TABLE AIM_VENDORS (
    VN_ID                   VARCHAR2(12)        NOT NULL,
    VN_DESC                 VARCHAR2(50)        NOT NULL,
    VN_STAT                 VARCHAR2(1)         DEFAULT 'A' NOT NULL,
    VN_TYPE                 VARCHAR2(1)         DEFAULT 'S' NOT NULL,
    ADDRESS1                VARCHAR2(50)        DEFAULT '' NOT NULL,
    ADDRESS2                VARCHAR2(50)        DEFAULT '' NOT NULL,
    CITY                    VARCHAR2(30)        DEFAULT '' NOT NULL,
    STATE                   VARCHAR2(10)        DEFAULT '' NOT NULL,
    ZIP_CODE                VARCHAR2(10)        DEFAULT '' NOT NULL,
    COUNTRY                 VARCHAR2(10)        DEFAULT '' NOT NULL,
    PHONE                   VARCHAR2(20)        DEFAULT '' NOT NULL,
    FAX                     VARCHAR2(20)        DEFAULT '' NOT NULL,
    EMAIL                   VARCHAR2(50)        DEFAULT '' NOT NULL,
    CONTACT_NAME            VARCHAR2(50)        DEFAULT '' NOT NULL,
    PAYMENT_TERMS           VARCHAR2(20)        DEFAULT '' NOT NULL,
    CURRENCY_CODE           VARCHAR2(3)         DEFAULT 'USD' NOT NULL,
    LEAD_TIME               NUMBER(5)           DEFAULT 0 NOT NULL,
    MIN_ORDER_QTY           NUMBER(10)          DEFAULT 0 NOT NULL,
    MIN_ORDER_AMT           NUMBER(12,2)        DEFAULT 0 NOT NULL,
    FREIGHT_TERMS           VARCHAR2(10)        DEFAULT 'FOB' NOT NULL,
    SHIP_VIA                VARCHAR2(20)        DEFAULT '' NOT NULL,
    TAX_ID                  VARCHAR2(20)        DEFAULT '' NOT NULL,
    DUNS_NUMBER             VARCHAR2(15)        DEFAULT '' NOT NULL,
    RATING                  VARCHAR2(1)         DEFAULT 'A' NOT NULL,
    ON_TIME_DELIVERY_PCT    NUMBER(5,2)         DEFAULT 0 NOT NULL,
    QUALITY_RATING          NUMBER(5,2)         DEFAULT 0 NOT NULL,
    COST_RATING             NUMBER(5,2)         DEFAULT 0 NOT NULL,
    SERVICE_RATING          NUMBER(5,2)         DEFAULT 0 NOT NULL,
    OVERALL_RATING          NUMBER(5,2)         DEFAULT 0 NOT NULL,
    LAST_ORDER_DATE         DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    LAST_RECEIPT_DATE       DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    YTD_PURCHASE_AMT        NUMBER(15,2)        DEFAULT 0 NOT NULL,
    LY_PURCHASE_AMT         NUMBER(15,2)        DEFAULT 0 NOT NULL,
    TOTAL_PURCHASE_AMT      NUMBER(15,2)        DEFAULT 0 NOT NULL,
    ACTIVE_ITEMS_COUNT      NUMBER(10)          DEFAULT 0 NOT NULL,
    PREFERRED_VENDOR        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    EDI_CAPABLE             VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DROP_SHIP_VENDOR        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    CONSIGNMENT_VENDOR      VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    BLANKET_PO_VENDOR       VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    AUTO_RECEIVE            VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    PRICE_TOLERANCE_PCT     NUMBER(5,2)         DEFAULT 0 NOT NULL,
    QTY_TOLERANCE_PCT       NUMBER(5,2)         DEFAULT 0 NOT NULL,
    DAYS_EARLY_TOLERANCE    NUMBER(5)           DEFAULT 0 NOT NULL,
    DAYS_LATE_TOLERANCE     NUMBER(5)           DEFAULT 0 NOT NULL,
    BUYER_ID                VARCHAR2(12)        DEFAULT '' NOT NULL,
    PLANNER_ID              VARCHAR2(12)        DEFAULT '' NOT NULL,
    USER_REF1               VARCHAR2(20)        DEFAULT '' NOT NULL,
    USER_REF2               VARCHAR2(20)        DEFAULT '' NOT NULL,
    USER_REF3               VARCHAR2(20)        DEFAULT '' NOT NULL,
    NOTES                   CLOB,
    CREATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    CREATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    UPDATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    UPDATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    VERSION                 NUMBER(10)          DEFAULT 0 NOT NULL
);

-- Primary Key
ALTER TABLE AIM_VENDORS ADD CONSTRAINT PK_AIM_VENDORS PRIMARY KEY (VN_ID);

-- Indexes
CREATE INDEX IX_AIM_VENDORS_VN_STAT ON AIM_VENDORS(VN_STAT);
CREATE INDEX IX_AIM_VENDORS_VN_TYPE ON AIM_VENDORS(VN_TYPE);
CREATE INDEX IX_AIM_VENDORS_BUYER_ID ON AIM_VENDORS(BUYER_ID);
CREATE INDEX IX_AIM_VENDORS_PLANNER_ID ON AIM_VENDORS(PLANNER_ID);
CREATE INDEX IX_AIM_VENDORS_RATING ON AIM_VENDORS(RATING);
CREATE INDEX IX_AIM_VENDORS_PREFERRED ON AIM_VENDORS(PREFERRED_VENDOR);

-- Comments
COMMENT ON TABLE AIM_VENDORS IS 'Vendor master table containing supplier information';
COMMENT ON COLUMN AIM_VENDORS.VN_ID IS 'Vendor ID (Primary Key)';
COMMENT ON COLUMN AIM_VENDORS.VN_DESC IS 'Vendor description/name';
COMMENT ON COLUMN AIM_VENDORS.VN_STAT IS 'Vendor status (A=Active, I=Inactive, H=Hold)';
COMMENT ON COLUMN AIM_VENDORS.VN_TYPE IS 'Vendor type (S=Supplier, M=Manufacturer, D=Distributor)';
COMMENT ON COLUMN AIM_VENDORS.RATING IS 'Vendor rating (A=Excellent, B=Good, C=Fair, D=Poor)';
COMMENT ON COLUMN AIM_VENDORS.ON_TIME_DELIVERY_PCT IS 'On-time delivery percentage';
COMMENT ON COLUMN AIM_VENDORS.PREFERRED_VENDOR IS 'Preferred vendor flag (Y/N)';

-- Check constraints
ALTER TABLE AIM_VENDORS ADD CONSTRAINT CHK_AIM_VENDORS_VN_STAT CHECK (VN_STAT IN ('A', 'I', 'H'));
ALTER TABLE AIM_VENDORS ADD CONSTRAINT CHK_AIM_VENDORS_VN_TYPE CHECK (VN_TYPE IN ('S', 'M', 'D'));
ALTER TABLE AIM_VENDORS ADD CONSTRAINT CHK_AIM_VENDORS_RATING CHECK (RATING IN ('A', 'B', 'C', 'D'));
ALTER TABLE AIM_VENDORS ADD CONSTRAINT CHK_AIM_VENDORS_YN_FLAGS CHECK (
    PREFERRED_VENDOR IN ('Y', 'N') AND
    EDI_CAPABLE IN ('Y', 'N') AND
    DROP_SHIP_VENDOR IN ('Y', 'N') AND
    CONSIGNMENT_VENDOR IN ('Y', 'N') AND
    BLANKET_PO_VENDOR IN ('Y', 'N') AND
    AUTO_RECEIVE IN ('Y', 'N')
);
ALTER TABLE AIM_VENDORS ADD CONSTRAINT CHK_AIM_VENDORS_PERCENTAGES CHECK (
    ON_TIME_DELIVERY_PCT BETWEEN 0 AND 100 AND
    QUALITY_RATING BETWEEN 0 AND 100 AND
    COST_RATING BETWEEN 0 AND 100 AND
    SERVICE_RATING BETWEEN 0 AND 100 AND
    OVERALL_RATING BETWEEN 0 AND 100 AND
    PRICE_TOLERANCE_PCT BETWEEN 0 AND 100 AND
    QTY_TOLERANCE_PCT BETWEEN 0 AND 100
);

-- Trigger for automatic timestamp updates
CREATE OR REPLACE TRIGGER TRG_AIM_VENDORS_UPDATE
    BEFORE UPDATE ON AIM_VENDORS
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/
