-- =====================================================
-- Table: ITEM
-- Description: Core item master table
-- Converted from SQL Server to Oracle
-- =====================================================

-- Drop table if exists
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE ITEM CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- Create table
CREATE TABLE ITEM (
    LCID                    VARCHAR2(12)        NOT NULL,
    ITEM                    VARCHAR2(25)        NOT NULL,
    IT_DESC                 VARCHAR2(50)        NOT NULL,
    IT_STAT                 VARCHAR2(1)         NOT NULL,
    ACT_DATE                DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    INACT_DATE              DATE                DEFAULT DATE '9999-12-31' NOT NULL,
    OPTION_ID               VARCHAR2(6)         DEFAULT 'DEFLT' NOT NULL,
    CLASS1                  VARCHAR2(50)        DEFAULT '' NOT NULL,
    CLASS2                  VARCHAR2(50)        DEFAULT '' NOT NULL,
    CLASS3                  VARCHAR2(50)        DEFAULT '' NOT NULL,
    CLASS4                  VARCHAR2(50)        DEFAULT '' NOT NULL,
    BIN_LOCATION            VARCHAR2(12)        DEFAULT '' NOT NULL,
    BUY_STRAT               VARCHAR2(1)         DEFAULT 'O' NOT NULL,
    VEL_CODE                VARCHAR2(1)         DEFAULT 'C' NOT NULL,
    VN_ID                   VARCHAR2(12)        DEFAULT '' NOT NULL,
    ASSORT                  VARCHAR2(12)        DEFAULT '' NOT NULL,
    BY_ID                   VARCHAR2(12)        DEFAULT '' NOT NULL,
    MDC                     VARCHAR2(12)        DEFAULT '' NOT NULL,
    MDC_FLAG                VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    SA_ID                   VARCHAR2(20)        DEFAULT '' NOT NULL,
    PM_ID                   VARCHAR2(6)         DEFAULT '' NOT NULL,
    UPC                     VARCHAR2(22)        DEFAULT '' NOT NULL,
    WEIGHT                  NUMBER(10,4)        DEFAULT 0 NOT NULL,
    CUBE                    NUMBER(10,4)        DEFAULT 0 NOT NULL,
    LIST_PRICE              NUMBER(10,4)        DEFAULT 0 NOT NULL,
    PRICE                   NUMBER(10,4)        DEFAULT 0 NOT NULL,
    COST                    NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY01                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST01               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY02                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST02               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY03                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST03               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY04                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST04               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY05                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST05               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY06                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST06               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY07                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST07               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY08                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST08               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY09                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST09               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    BK_QTY10                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COST10               NUMBER(10,4)        DEFAULT 0 NOT NULL,
    UOM                     VARCHAR2(6)         DEFAULT 'EA' NOT NULL,
    CONV_FACTOR             NUMBER(10)          DEFAULT 1 NOT NULL,
    BUYING_UOM              VARCHAR2(6)         DEFAULT 'EA' NOT NULL,
    REPLEN_COST2            NUMBER(9,4)         DEFAULT 0 NOT NULL,
    OH                      NUMBER(10)          DEFAULT 0 NOT NULL,
    OO                      NUMBER(10)          DEFAULT 0 NOT NULL,
    COM_STK                 NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_ORDER                NUMBER(10)          DEFAULT 0 NOT NULL,
    BK_COM_STK              NUMBER(10)          DEFAULT 0 NOT NULL,
    LEAD_TIME               NUMBER(5)           DEFAULT 0 NOT NULL,
    PACK_ROUNDING           VARCHAR2(1)         DEFAULT 'R' NOT NULL,
    I_MIN                   NUMBER(10)          DEFAULT 0 NOT NULL,
    I_MAX                   NUMBER(10)          DEFAULT 999999999 NOT NULL,
    C_STOCK                 NUMBER(10)          DEFAULT 0 NOT NULL,
    SS_ADJ                  NUMBER(9,4)         DEFAULT 0 NOT NULL,
    USER_MIN                NUMBER(10)          DEFAULT 0 NOT NULL,
    USER_MAX                NUMBER(10)          DEFAULT 999999999 NOT NULL,
    USER_METHOD             NUMBER(3)           DEFAULT 0 NOT NULL,
    FCST_METHOD             NUMBER(3)           DEFAULT 0 NOT NULL,
    FCST_DEMAND             NUMBER(10,2)        DEFAULT 0 NOT NULL,
    USER_FCST               NUMBER(10,2)        DEFAULT 0 NOT NULL,
    USER_FCST_EXP_DATE      DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    MAE                     NUMBER(10,2)        DEFAULT 0 NOT NULL,
    MSE                     NUMBER(10,2)        DEFAULT 0 NOT NULL,
    TREND                   NUMBER(10,3)        DEFAULT 0 NOT NULL,
    FCST_CYCLES             NUMBER(10)          DEFAULT 0 NOT NULL,
    ZERO_COUNT              NUMBER(10)          DEFAULT 0 NOT NULL,
    MEAN_NZ                 NUMBER(10,2)        DEFAULT 0 NOT NULL,
    STD_DEV_NZ              NUMBER(10,2)        DEFAULT 0 NOT NULL,
    INT_SAFETY_STOCK        NUMBER(10,2)        DEFAULT 0 NOT NULL,
    IS_INTERMITTENT         VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DI_FLAG                 VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DMD_FILTER_FLAG         VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    TRK_SIGNAL_FLAG         VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    USER_DEMAND_FLAG        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    BY_PASS_PCT             NUMBER(4,3)         DEFAULT 0 NOT NULL,
    FCST_UPD_CYC            NUMBER(10)          DEFAULT 0 NOT NULL,
    LTV_FACT                NUMBER(3,2)         DEFAULT 0 NOT NULL,
    PLN_TT                  VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    ZOP_SW                  VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    OUTL_SW                 VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    ZS_STOCK                VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    D_SER                   NUMBER(4,3)         DEFAULT 0.95 NOT NULL,
    FREEZE_BUY_STRAT        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    FREEZE_BYID             VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    FREEZE_LEAD_TIME        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    FREEZE_OPTION_ID        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    FREEZE_D_SER            VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    OLD_ITEM                VARCHAR2(25)        DEFAULT '' NOT NULL,
    ACCUM_LT                NUMBER(5)           DEFAULT 0 NOT NULL,
    REVIEW_TIME             NUMBER(5)           DEFAULT 0 NOT NULL,
    ORDER_PT                NUMBER(10)          DEFAULT 0 NOT NULL,
    ORDER_QTY               NUMBER(10)          DEFAULT 0 NOT NULL,
    SAFETY_STOCK            NUMBER(10,2)        DEFAULT 0 NOT NULL,
    FCST_RT                 NUMBER(10,2)        DEFAULT 0 NOT NULL,
    FCST_LT                 NUMBER(10,2)        DEFAULT 0 NOT NULL,
    FCST_MONTH              NUMBER(10,2)        DEFAULT 0 NOT NULL,
    FCST_QUARTER            NUMBER(10,2)        DEFAULT 0 NOT NULL,
    FCST_YEAR               NUMBER(10,2)        DEFAULT 0 NOT NULL,
    FCST_DATE               DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    VC_AMT                  VARCHAR2(1)         DEFAULT 'C' NOT NULL,
    VC_UNITS_RANKING        NUMBER(10)          DEFAULT 0 NOT NULL,
    VC_AMT_RANKING          NUMBER(10)          DEFAULT 0 NOT NULL,
    VC_DATE                 DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    VEL_CODE_PREV           VARCHAR2(1)         DEFAULT '' NOT NULL,
    VC_AMT_PREV             VARCHAR2(1)         DEFAULT '' NOT NULL,
    ON_PROMOTION            VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    AVG_OH                  NUMBER(10,2)        DEFAULT 0 NOT NULL,
    NEXT_PO_NBR_1           VARCHAR2(12)        DEFAULT '' NOT NULL,
    NEXT_PO_DATE_1          DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    NEXT_PO_QTY_1           NUMBER(10)          DEFAULT 0 NOT NULL,
    NEXT_PO_NBR_2           VARCHAR2(12)        DEFAULT '' NOT NULL,
    NEXT_PO_DATE_2          DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    NEXT_PO_QTY_2           NUMBER(10)          DEFAULT 0 NOT NULL,
    NEXT_PO_NBR_3           VARCHAR2(12)        DEFAULT '' NOT NULL,
    NEXT_PO_DATE_3          DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    NEXT_PO_QTY_3           NUMBER(10)          DEFAULT 0 NOT NULL,
    USER_REF1               VARCHAR2(12)        DEFAULT '' NOT NULL,
    USER_REF2               VARCHAR2(12)        DEFAULT '' NOT NULL,
    USER_REF3               VARCHAR2(12)        DEFAULT '' NOT NULL,
    FREEZE_FORECAST         VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    PRODUCTION_CONSTRAINT   VARCHAR2(1)         DEFAULT 'N',
    ALLOCATABLE_QTY         NUMBER(10)          DEFAULT 0 NOT NULL,
    ALT_VN_FLAG             VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    KIT_BOM_FLAG            VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DEPENDENT_FCST_DEMAND   NUMBER(10,2)        DEFAULT 0 NOT NULL,
    ALLOC_UOM               VARCHAR2(6)         DEFAULT 'EA' NOT NULL,
    ALLOC_CONV_FACTOR       NUMBER(10)          DEFAULT 1 NOT NULL,
    CREATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    CREATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    UPDATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    UPDATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    VERSION                 NUMBER(10)          DEFAULT 0 NOT NULL
);

-- Primary Key
ALTER TABLE ITEM ADD CONSTRAINT PK_ITEM PRIMARY KEY (LCID, ITEM);

-- Indexes
CREATE INDEX IX_ITEM_BY_ID ON ITEM(BY_ID);
CREATE INDEX IX_ITEM_ITEM ON ITEM(ITEM);
CREATE INDEX IX_ITEM_IT_STAT ON ITEM(IT_STAT);
CREATE INDEX IX_ITEM_VN_ID_ASSORT ON ITEM(VN_ID, ASSORT);
CREATE INDEX IX_ITEM_MDC ON ITEM(MDC);
CREATE INDEX IX_ITEM_COVER ON ITEM(CLASS1, CLASS2, CLASS3, CLASS4, VN_ID, ASSORT, BY_ID, SA_ID, PM_ID);

-- Comments
COMMENT ON TABLE ITEM IS 'Core item master table containing all item information';
COMMENT ON COLUMN ITEM.LCID IS 'Location ID';
COMMENT ON COLUMN ITEM.ITEM IS 'Item number';
COMMENT ON COLUMN ITEM.IT_DESC IS 'Item description';
COMMENT ON COLUMN ITEM.IT_STAT IS 'Item status (A=Active, I=Inactive, D=Discontinued)';
COMMENT ON COLUMN ITEM.FCST_METHOD IS 'Forecasting method (1=MA, 2=ES, 3=LR, 4=SD, 5=Croston)';
COMMENT ON COLUMN ITEM.BUY_STRAT IS 'Buy strategy (O=Optimal, M=Min/Max, T=Time Supply)';
COMMENT ON COLUMN ITEM.VEL_CODE IS 'Velocity code (A=Fast, B=Medium, C=Slow, D=Dead)';

-- Check constraints
ALTER TABLE ITEM ADD CONSTRAINT CHK_ITEM_IT_STAT CHECK (IT_STAT IN ('A', 'I', 'D', 'P'));
ALTER TABLE ITEM ADD CONSTRAINT CHK_ITEM_BUY_STRAT CHECK (BUY_STRAT IN ('O', 'M', 'T'));
ALTER TABLE ITEM ADD CONSTRAINT CHK_ITEM_VEL_CODE CHECK (VEL_CODE IN ('A', 'B', 'C', 'D'));
ALTER TABLE ITEM ADD CONSTRAINT CHK_ITEM_YN_FLAGS CHECK (
    MDC_FLAG IN ('Y', 'N') AND
    IS_INTERMITTENT IN ('Y', 'N') AND
    DI_FLAG IN ('Y', 'N') AND
    DMD_FILTER_FLAG IN ('Y', 'N') AND
    TRK_SIGNAL_FLAG IN ('Y', 'N') AND
    USER_DEMAND_FLAG IN ('Y', 'N') AND
    PLN_TT IN ('Y', 'N') AND
    ZOP_SW IN ('Y', 'N') AND
    OUTL_SW IN ('Y', 'N') AND
    ZS_STOCK IN ('Y', 'N') AND
    ON_PROMOTION IN ('Y', 'N') AND
    FREEZE_FORECAST IN ('Y', 'N') AND
    ALT_VN_FLAG IN ('Y', 'N') AND
    KIT_BOM_FLAG IN ('Y', 'N')
);

-- Trigger for automatic timestamp updates
CREATE OR REPLACE TRIGGER TRG_ITEM_UPDATE
    BEFORE UPDATE ON ITEM
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/
