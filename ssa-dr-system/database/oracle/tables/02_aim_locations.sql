-- =====================================================
-- Table: AIM_LOCATIONS
-- Description: Location master table
-- Converted from SQL Server to Oracle
-- =====================================================

-- Drop table if exists
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE AIM_LOCATIONS CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- Create table
CREATE TABLE AIM_LOCATIONS (
    LCID                    VARCHAR2(12)        NOT NULL,
    LC_DESC                 VARCHAR2(50)        NOT NULL,
    LC_STAT                 VARCHAR2(1)         DEFAULT 'A' NOT NULL,
    LC_TYPE                 VARCHAR2(1)         DEFAULT 'S' NOT NULL,
    REGION                  VARCHAR2(12)        DEFAULT '' NOT NULL,
    DIVISION                VARCHAR2(12)        DEFAULT '' NOT NULL,
    USER_DEFINED            VARCHAR2(12)        DEFAULT '' NOT NULL,
    ADDRESS1                VARCHAR2(50)        DEFAULT '' NOT NULL,
    ADDRESS2                VARCHAR2(50)        DEFAULT '' NOT NULL,
    CITY                    VARCHAR2(30)        DEFAULT '' NOT NULL,
    STATE                   VARCHAR2(10)        DEFAULT '' NOT NULL,
    ZIP_CODE                VARCHAR2(10)        DEFAULT '' NOT NULL,
    COUNTRY                 VARCHAR2(10)        DEFAULT '' NOT NULL,
    PHONE                   VARCHAR2(20)        DEFAULT '' NOT NULL,
    FAX                     VARCHAR2(20)        DEFAULT '' NOT NULL,
    EMAIL                   VARCHAR2(50)        DEFAULT '' NOT NULL,
    CONTACT_NAME            VARCHAR2(50)        DEFAULT '' NOT NULL,
    DFT_OPTION_ID           VARCHAR2(6)         DEFAULT 'DEFLT' NOT NULL,
    DFT_BUY_STRAT           VARCHAR2(1)         DEFAULT 'O' NOT NULL,
    DFT_VEL_CODE            VARCHAR2(1)         DEFAULT 'C' NOT NULL,
    DFT_BY_ID               VARCHAR2(12)        DEFAULT '' NOT NULL,
    DFT_SA_ID               VARCHAR2(20)        DEFAULT '' NOT NULL,
    DFT_PM_ID               VARCHAR2(6)         DEFAULT '' NOT NULL,
    DFT_PACK_ROUNDING       VARCHAR2(1)         DEFAULT 'R' NOT NULL,
    DFT_I_MIN               NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_I_MAX               NUMBER(10)          DEFAULT 999999999 NOT NULL,
    DFT_C_STOCK             NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_SS_ADJ              NUMBER(9,4)         DEFAULT 0 NOT NULL,
    DFT_USER_MIN            NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_USER_MAX            NUMBER(10)          DEFAULT 999999999 NOT NULL,
    DFT_USER_METHOD         NUMBER(3)           DEFAULT 0 NOT NULL,
    DFT_FCST_METHOD         NUMBER(3)           DEFAULT 0 NOT NULL,
    DFT_FCST_DEMAND         NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_USER_FCST           NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_USER_FCST_EXP_DATE  DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    DFT_MAE                 NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_MSE                 NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_TREND               NUMBER(10,3)        DEFAULT 0 NOT NULL,
    DFT_FCST_CYCLES         NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_ZERO_COUNT          NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_DI_FLAG             VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_DMD_FILTER_FLAG     VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_TRK_SIGNAL_FLAG     VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_USER_DEMAND_FLAG    VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_BY_PASS_PCT         NUMBER(4,3)         DEFAULT 0 NOT NULL,
    DFT_FCST_UPD_CYC        NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_LTV_FACT            NUMBER(3,2)         DEFAULT 0 NOT NULL,
    DFT_PLN_TT              VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_ZOP_SW              VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_OUTL_SW             VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_ZS_STOCK            VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_D_SER               NUMBER(4,3)         DEFAULT 0.95 NOT NULL,
    DFT_FREEZE_BUY_STRAT    VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_FREEZE_BYID         VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_FREEZE_LEAD_TIME    VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_FREEZE_OPTION_ID    VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_FREEZE_D_SER        VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    DFT_ACCUM_LT            NUMBER(5)           DEFAULT 0 NOT NULL,
    DFT_REVIEW_TIME         NUMBER(5)           DEFAULT 0 NOT NULL,
    DFT_ORDER_PT            NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_ORDER_QTY           NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_SAFETY_STOCK        NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_FCST_RT             NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_FCST_LT             NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_FCST_MONTH          NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_FCST_QUARTER        NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_FCST_YEAR           NUMBER(10,2)        DEFAULT 0 NOT NULL,
    DFT_FCST_DATE           DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    DFT_VC_AMT              VARCHAR2(1)         DEFAULT 'C' NOT NULL,
    DFT_VC_UNITS_RANKING    NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_VC_AMT_RANKING      NUMBER(10)          DEFAULT 0 NOT NULL,
    DFT_VC_DATE             DATE                DEFAULT DATE '1990-01-01' NOT NULL,
    DFT_VEL_CODE_PREV       VARCHAR2(1)         DEFAULT '' NOT NULL,
    DFT_VC_AMT_PREV         VARCHAR2(1)         DEFAULT '' NOT NULL,
    DFT_REPLEN_COST2        NUMBER(9,4)         DEFAULT 0 NOT NULL,
    LEAD_TIME_SOURCE        VARCHAR2(1)         DEFAULT 'I' NOT NULL,
    STK_DATE                DATE                DEFAULT SYSDATE NOT NULL,
    CURRENCY_CODE           VARCHAR2(3)         DEFAULT 'USD' NOT NULL,
    TIME_ZONE               VARCHAR2(50)        DEFAULT 'UTC' NOT NULL,
    CREATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    CREATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    UPDATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    UPDATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    VERSION                 NUMBER(10)          DEFAULT 0 NOT NULL
);

-- Primary Key
ALTER TABLE AIM_LOCATIONS ADD CONSTRAINT PK_AIM_LOCATIONS PRIMARY KEY (LCID);

-- Indexes
CREATE INDEX IX_AIM_LOCATIONS_REGION ON AIM_LOCATIONS(REGION);
CREATE INDEX IX_AIM_LOCATIONS_DIVISION ON AIM_LOCATIONS(DIVISION);
CREATE INDEX IX_AIM_LOCATIONS_LC_STAT ON AIM_LOCATIONS(LC_STAT);
CREATE INDEX IX_AIM_LOCATIONS_LC_TYPE ON AIM_LOCATIONS(LC_TYPE);

-- Comments
COMMENT ON TABLE AIM_LOCATIONS IS 'Location master table containing store/warehouse information';
COMMENT ON COLUMN AIM_LOCATIONS.LCID IS 'Location ID (Primary Key)';
COMMENT ON COLUMN AIM_LOCATIONS.LC_DESC IS 'Location description';
COMMENT ON COLUMN AIM_LOCATIONS.LC_STAT IS 'Location status (A=Active, I=Inactive)';
COMMENT ON COLUMN AIM_LOCATIONS.LC_TYPE IS 'Location type (S=Store, W=Warehouse, D=DC)';
COMMENT ON COLUMN AIM_LOCATIONS.LEAD_TIME_SOURCE IS 'Lead time source (I=Item, V=Vendor)';

-- Check constraints
ALTER TABLE AIM_LOCATIONS ADD CONSTRAINT CHK_AIM_LOCATIONS_LC_STAT CHECK (LC_STAT IN ('A', 'I'));
ALTER TABLE AIM_LOCATIONS ADD CONSTRAINT CHK_AIM_LOCATIONS_LC_TYPE CHECK (LC_TYPE IN ('S', 'W', 'D', 'M'));
ALTER TABLE AIM_LOCATIONS ADD CONSTRAINT CHK_AIM_LOCATIONS_LEAD_TIME_SRC CHECK (LEAD_TIME_SOURCE IN ('I', 'V'));

-- Trigger for automatic timestamp updates
CREATE OR REPLACE TRIGGER TRG_AIM_LOCATIONS_UPDATE
    BEFORE UPDATE ON AIM_LOCATIONS
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/
