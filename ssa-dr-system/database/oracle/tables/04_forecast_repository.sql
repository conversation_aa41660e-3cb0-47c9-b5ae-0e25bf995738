-- =====================================================
-- Table: FORECAST_REPOSITORY
-- Description: Forecast repository header table
-- Converted from SQL Server to Oracle
-- =====================================================

-- Drop table if exists
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE FORECAST_REPOSITORY CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- Create table
CREATE TABLE FORECAST_REPOSITORY (
    REPOSITORY_KEY          NUMBER(18)          NOT NULL,
    REPOSITORY_ID           VARCHAR2(20)        NOT NULL,
    REPOSITORY_DESC         VARCHAR2(100)       NOT NULL,
    REPOSITORY_TYPE         VARCHAR2(1)         DEFAULT 'F' NOT NULL,
    STATUS                  VARCHAR2(1)         DEFAULT 'A' NOT NULL,
    FCST_START_DATE         DATE                NOT NULL,
    FCST_END_DATE           DATE                NOT NULL,
    CREATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    CREATED_BY              VARCHAR2(50)        NOT NULL,
    LOCKED_DATE             DATE,
    LOCKED_BY               VARCHAR2(50),
    PROMOTED_DATE           DATE,
    PROMOTED_BY             VARCHAR2(50),
    ARCHIVED_DATE           DATE,
    ARCHIVED_BY             VARCHAR2(50),
    BASE_REPOSITORY_KEY     NUMBER(18),
    COPY_ACTUALS            VARCHAR2(1)         DEFAULT 'Y' NOT NULL,
    COPY_ADJUSTMENTS        VARCHAR2(1)         DEFAULT 'Y' NOT NULL,
    COPY_OVERRIDES          VARCHAR2(1)         DEFAULT 'Y' NOT NULL,
    AUTO_REFRESH            VARCHAR2(1)         DEFAULT 'N' NOT NULL,
    REFRESH_FREQUENCY       VARCHAR2(1)         DEFAULT 'D' NOT NULL,
    LAST_REFRESH_DATE       DATE,
    NEXT_REFRESH_DATE       DATE,
    ITEM_COUNT              NUMBER(10)          DEFAULT 0 NOT NULL,
    LOCATION_COUNT          NUMBER(10)          DEFAULT 0 NOT NULL,
    PERIOD_COUNT            NUMBER(10)          DEFAULT 0 NOT NULL,
    TOTAL_FORECAST          NUMBER(15,2)        DEFAULT 0 NOT NULL,
    TOTAL_ADJUSTMENTS       NUMBER(15,2)        DEFAULT 0 NOT NULL,
    ACCURACY_MAE            NUMBER(10,4)        DEFAULT 0 NOT NULL,
    ACCURACY_MAPE           NUMBER(10,4)        DEFAULT 0 NOT NULL,
    ACCURACY_BIAS           NUMBER(10,4)        DEFAULT 0 NOT NULL,
    NOTES                   CLOB,
    USER_REF1               VARCHAR2(50)        DEFAULT '' NOT NULL,
    USER_REF2               VARCHAR2(50)        DEFAULT '' NOT NULL,
    USER_REF3               VARCHAR2(50)        DEFAULT '' NOT NULL,
    UPDATED_DATE            DATE                DEFAULT SYSDATE NOT NULL,
    UPDATED_BY              VARCHAR2(50)        DEFAULT 'SYSTEM' NOT NULL,
    VERSION                 NUMBER(10)          DEFAULT 0 NOT NULL
);

-- Primary Key
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT PK_FORECAST_REPOSITORY PRIMARY KEY (REPOSITORY_KEY);

-- Unique constraints
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT UK_FORECAST_REPOSITORY_ID UNIQUE (REPOSITORY_ID);

-- Indexes
CREATE INDEX IX_FORECAST_REPOSITORY_STATUS ON FORECAST_REPOSITORY(STATUS);
CREATE INDEX IX_FORECAST_REPOSITORY_TYPE ON FORECAST_REPOSITORY(REPOSITORY_TYPE);
CREATE INDEX IX_FORECAST_REPOSITORY_CREATED_BY ON FORECAST_REPOSITORY(CREATED_BY);
CREATE INDEX IX_FORECAST_REPOSITORY_DATES ON FORECAST_REPOSITORY(FCST_START_DATE, FCST_END_DATE);
CREATE INDEX IX_FORECAST_REPOSITORY_BASE_KEY ON FORECAST_REPOSITORY(BASE_REPOSITORY_KEY);

-- Comments
COMMENT ON TABLE FORECAST_REPOSITORY IS 'Forecast repository header table';
COMMENT ON COLUMN FORECAST_REPOSITORY.REPOSITORY_KEY IS 'Unique repository key (Primary Key)';
COMMENT ON COLUMN FORECAST_REPOSITORY.REPOSITORY_ID IS 'User-friendly repository ID';
COMMENT ON COLUMN FORECAST_REPOSITORY.REPOSITORY_TYPE IS 'Repository type (F=Forecast, A=Actual, B=Budget)';
COMMENT ON COLUMN FORECAST_REPOSITORY.STATUS IS 'Repository status (A=Active, L=Locked, P=Promoted, X=Archived)';
COMMENT ON COLUMN FORECAST_REPOSITORY.BASE_REPOSITORY_KEY IS 'Base repository for copy operations';
COMMENT ON COLUMN FORECAST_REPOSITORY.REFRESH_FREQUENCY IS 'Auto refresh frequency (D=Daily, W=Weekly, M=Monthly)';

-- Check constraints
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT CHK_FORECAST_REPOSITORY_TYPE CHECK (REPOSITORY_TYPE IN ('F', 'A', 'B'));
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT CHK_FORECAST_REPOSITORY_STATUS CHECK (STATUS IN ('A', 'L', 'P', 'X'));
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT CHK_FORECAST_REPOSITORY_YN_FLAGS CHECK (
    COPY_ACTUALS IN ('Y', 'N') AND
    COPY_ADJUSTMENTS IN ('Y', 'N') AND
    COPY_OVERRIDES IN ('Y', 'N') AND
    AUTO_REFRESH IN ('Y', 'N')
);
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT CHK_FORECAST_REPOSITORY_REFRESH_FREQ CHECK (REFRESH_FREQUENCY IN ('D', 'W', 'M'));
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT CHK_FORECAST_REPOSITORY_DATES CHECK (FCST_END_DATE >= FCST_START_DATE);

-- Foreign Key (self-referencing)
ALTER TABLE FORECAST_REPOSITORY ADD CONSTRAINT FK_FORECAST_REPOSITORY_BASE 
    FOREIGN KEY (BASE_REPOSITORY_KEY) REFERENCES FORECAST_REPOSITORY(REPOSITORY_KEY);

-- Sequence for repository key
CREATE SEQUENCE SEQ_FORECAST_REPOSITORY_KEY
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Trigger for automatic key generation and timestamp updates
CREATE OR REPLACE TRIGGER TRG_FORECAST_REPOSITORY_INSERT
    BEFORE INSERT ON FORECAST_REPOSITORY
    FOR EACH ROW
BEGIN
    IF :NEW.REPOSITORY_KEY IS NULL THEN
        SELECT SEQ_FORECAST_REPOSITORY_KEY.NEXTVAL INTO :NEW.REPOSITORY_KEY FROM DUAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER TRG_FORECAST_REPOSITORY_UPDATE
    BEFORE UPDATE ON FORECAST_REPOSITORY
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/
