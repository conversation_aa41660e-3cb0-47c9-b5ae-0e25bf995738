-- =====================================================
-- Procedure: AIM_DEMAND_UPDATE_SP
-- Description: Updates the demand for an item
-- Converted from SQL Server to Oracle PL/SQL
-- =====================================================

CREATE OR REPLACE PROCEDURE AIM_DEMAND_UPDATE_SP (
    P_LCID                  IN VARCHAR2,
    P_ITEM                  IN VARCHAR2,
    P_IT_STAT               IN VARCHAR2,
    P_BY_ID                 IN VARCHAR2,
    P_LEAD_TIME             IN NUMBER,
    P_FCST_METHOD           IN NUMBER,
    P_FCST_DEMAND           IN NUMBER,
    P_MAE                   IN NUMBER,
    P_MSE                   IN NUMBER,
    P_TREND                 IN NUMBER,
    P_FCST_CYCLES           IN NUMBER,
    P_ZERO_COUNT            IN NUMBER,
    P_DI_FLAG               IN VARCHAR2,
    P_DMD_FILTER_FLAG       IN VARCHAR2,
    P_TRK_SIGNAL_FLAG       IN VARCHAR2,
    P_USER_DEMAND_FLAG      IN VARCHAR2,
    P_BY_PASS_PCT           IN NUMBER,
    P_FCST_UPD_CYC          IN NUMBER,
    P_D_SER                 IN NUMBER,
    P_ACCUM_LT              IN NUMBER,
    P_REVIEW_TIME           IN NUMBER,
    P_ORDER_PT              IN NUMBER,
    P_ORDER_QTY             IN NUMBER,
    P_SAFETY_STOCK          IN NUMBER,
    P_FCST_RT               IN NUMBER,
    P_FCST_LT               IN NUMBER,
    P_FCST_MONTH            IN NUMBER,
    P_FCST_QUARTER          IN NUMBER,
    P_FCST_YEAR             IN NUMBER,
    P_MEAN_NZ               IN NUMBER,
    P_STD_DEV_NZ            IN NUMBER,
    P_INT_SAFETY_STOCK      IN NUMBER,
    P_IS_INTERMITTENT       IN VARCHAR2,
    P_RESULT                OUT NUMBER
) AS
    V_ROW_COUNT NUMBER := 0;
    V_ERROR_CODE NUMBER;
    V_ERROR_MSG VARCHAR2(4000);
BEGIN
    -- Update Item table
    UPDATE ITEM
    SET IT_STAT = P_IT_STAT,
        BY_ID = P_BY_ID,
        LEAD_TIME = P_LEAD_TIME,
        FCST_METHOD = P_FCST_METHOD,
        FCST_DEMAND = P_FCST_DEMAND,
        MAE = P_MAE,
        MSE = P_MSE,
        TREND = P_TREND,
        FCST_CYCLES = P_FCST_CYCLES,
        ZERO_COUNT = P_ZERO_COUNT,
        DI_FLAG = P_DI_FLAG,
        DMD_FILTER_FLAG = P_DMD_FILTER_FLAG,
        TRK_SIGNAL_FLAG = P_TRK_SIGNAL_FLAG,
        USER_DEMAND_FLAG = P_USER_DEMAND_FLAG,
        BY_PASS_PCT = P_BY_PASS_PCT,
        FCST_UPD_CYC = P_FCST_UPD_CYC,
        D_SER = P_D_SER,
        ACCUM_LT = P_ACCUM_LT,
        REVIEW_TIME = P_REVIEW_TIME,
        ORDER_PT = P_ORDER_PT,
        ORDER_QTY = P_ORDER_QTY,
        SAFETY_STOCK = P_SAFETY_STOCK,
        FCST_RT = P_FCST_RT,
        FCST_LT = P_FCST_LT,
        FCST_MONTH = P_FCST_MONTH,
        FCST_QUARTER = P_FCST_QUARTER,
        FCST_YEAR = P_FCST_YEAR,
        FCST_DATE = TRUNC(SYSDATE),
        MEAN_NZ = P_MEAN_NZ,
        STD_DEV_NZ = P_STD_DEV_NZ,
        INT_SAFETY_STOCK = P_INT_SAFETY_STOCK,
        IS_INTERMITTENT = P_IS_INTERMITTENT,
        UPDATED_DATE = SYSDATE,
        UPDATED_BY = USER
    WHERE LCID = P_LCID
      AND ITEM = P_ITEM;

    V_ROW_COUNT := SQL%ROWCOUNT;
    P_RESULT := V_ROW_COUNT;

    -- Commit the transaction
    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        V_ERROR_CODE := SQLCODE;
        V_ERROR_MSG := SQLERRM;
        ROLLBACK;
        
        -- Log error (you can implement error logging here)
        DBMS_OUTPUT.PUT_LINE('Error in AIM_DEMAND_UPDATE_SP: ' || V_ERROR_CODE || ' - ' || V_ERROR_MSG);
        
        P_RESULT := -1;
        RAISE;
END AIM_DEMAND_UPDATE_SP;
/
