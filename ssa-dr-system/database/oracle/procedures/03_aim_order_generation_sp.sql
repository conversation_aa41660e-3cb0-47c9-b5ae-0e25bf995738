-- =====================================================
-- Procedure: AIM_ORDER_GENERATION_SP
-- Description: Generates purchase orders based on demand and inventory
-- Converted from SQL Server to Oracle PL/SQL
-- =====================================================

CREATE OR REPLACE PROCEDURE AIM_ORDER_GENERATION_SP (
    P_BY_ID                 IN VARCHAR2,
    P_VN_ID                 IN VARCHAR2,
    P_ASSORT                IN VARCHAR2,
    P_LCID                  IN VARCHAR2,
    P_SEL_OPTION            IN NUMBER,
    P_RESULT                OUT NUMBER
) AS
    -- Cursor for items to process
    CURSOR C_ITEMS IS
        SELECT I.LCID,
               I.ITEM,
               I.IT_DESC,
               I.BUY_STRAT,
               I.OH,
               I.OO,
               I.COM_STK,
               I.BK_ORDER,
               I.BK_COM_STK,
               I.LEAD_TIME,
               I.REVIEW_TIME,
               I.ORDER_PT,
               I.ORDER_QTY,
               I.SAFETY_STOCK,
               I.FCST_LT,
               I.FCST_RT,
               I.PACK_ROUNDING,
               I.I_MIN,
               I.I_MAX,
               I.COST,
               I.VN_ID,
               I.ASSORT
        FROM ITEM I
        WHERE I.IT_STAT = 'A'
          AND (P_BY_ID IS NULL OR I.BY_ID = P_BY_ID)
          AND (P_VN_ID IS NULL OR I.VN_ID = P_VN_ID)
          AND (P_ASSORT IS NULL OR I.ASSORT = P_ASSORT)
          AND (P_LCID IS NULL OR I.LCID = P_LCID)
        ORDER BY I.VN_ID, I.ASSORT, I.LCID, I.ITEM;

    -- Variables
    V_AVAIL_QTY NUMBER;
    V_RSOQ NUMBER; -- Raw Suggested Order Quantity
    V_SOQ NUMBER;  -- Suggested Order Quantity
    V_VSOQ NUMBER; -- Vendor Suggested Order Quantity
    V_PO_NBR VARCHAR2(20);
    V_ROW_COUNT NUMBER := 0;
    V_ERROR_CODE NUMBER;
    V_ERROR_MSG VARCHAR2(4000);

BEGIN
    -- Validate required parameters
    IF P_SEL_OPTION IS NULL THEN
        P_RESULT := -1;
        RETURN;
    END IF;

    -- Process each item
    FOR REC IN C_ITEMS LOOP
        BEGIN
            -- Calculate available quantity
            V_AVAIL_QTY := REC.OH + REC.OO - REC.COM_STK - REC.BK_ORDER - REC.BK_COM_STK;

            -- Initialize order quantities
            V_RSOQ := 0;
            V_SOQ := 0;
            V_VSOQ := 0;

            -- Calculate Raw Suggested Order Quantity based on buy strategy
            CASE REC.BUY_STRAT
                WHEN 'M' THEN -- Min/Max
                    IF V_AVAIL_QTY < REC.ORDER_PT THEN
                        V_RSOQ := REC.ORDER_QTY + (REC.ORDER_PT - V_AVAIL_QTY);
                    END IF;

                WHEN 'O' THEN -- Optimal
                    IF V_AVAIL_QTY < (REC.FCST_LT + REC.SAFETY_STOCK + (0.5 * REC.FCST_RT)) THEN
                        V_RSOQ := REC.ORDER_QTY + (REC.FCST_LT + REC.SAFETY_STOCK + (0.5 * REC.FCST_RT) - V_AVAIL_QTY);
                    END IF;

                WHEN 'T' THEN -- Time Supply
                    IF V_AVAIL_QTY < REC.ORDER_PT THEN
                        V_RSOQ := REC.ORDER_QTY + (REC.ORDER_PT - V_AVAIL_QTY);
                    END IF;

                ELSE -- Default to Optimal
                    IF V_AVAIL_QTY < (REC.FCST_LT + REC.SAFETY_STOCK + (0.5 * REC.FCST_RT)) THEN
                        V_RSOQ := REC.ORDER_QTY + (REC.FCST_LT + REC.SAFETY_STOCK + (0.5 * REC.FCST_RT) - V_AVAIL_QTY);
                    END IF;
            END CASE;

            -- Apply pack rounding if needed
            IF V_RSOQ > 0 THEN
                V_SOQ := V_RSOQ; -- Simplified - implement pack rounding logic here
                
                -- Apply min/max constraints
                IF V_SOQ < REC.I_MIN THEN
                    V_SOQ := REC.I_MIN;
                ELSIF V_SOQ > REC.I_MAX THEN
                    V_SOQ := REC.I_MAX;
                END IF;

                V_VSOQ := V_SOQ; -- Vendor SOQ same as SOQ for now

                -- Generate PO number
                SELECT 'PO' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(SEQ_PO_NUMBER.NEXTVAL, 6, '0')
                INTO V_PO_NBR
                FROM DUAL;

                -- Insert into PO Detail table (assuming it exists)
                INSERT INTO PO_DETAIL (
                    PO_NBR,
                    LCID,
                    ITEM,
                    VN_ID,
                    ASSORT,
                    BY_ID,
                    RSOQ,
                    SOQ,
                    VSOQ,
                    UNIT_COST,
                    EXTENDED_COST,
                    ORDER_DATE,
                    NEED_DATE,
                    ORD_STATUS,
                    CREATED_DATE,
                    CREATED_BY
                ) VALUES (
                    V_PO_NBR,
                    REC.LCID,
                    REC.ITEM,
                    REC.VN_ID,
                    REC.ASSORT,
                    P_BY_ID,
                    V_RSOQ,
                    V_SOQ,
                    V_VSOQ,
                    REC.COST,
                    V_VSOQ * REC.COST,
                    SYSDATE,
                    SYSDATE + REC.LEAD_TIME,
                    'O',
                    SYSDATE,
                    USER
                );

                V_ROW_COUNT := V_ROW_COUNT + 1;
            END IF;

        EXCEPTION
            WHEN OTHERS THEN
                -- Log error for this item and continue
                DBMS_OUTPUT.PUT_LINE('Error processing item ' || REC.ITEM || ': ' || SQLERRM);
                CONTINUE;
        END;
    END LOOP;

    -- Commit the transaction
    COMMIT;
    
    P_RESULT := V_ROW_COUNT;

EXCEPTION
    WHEN OTHERS THEN
        V_ERROR_CODE := SQLCODE;
        V_ERROR_MSG := SQLERRM;
        ROLLBACK;
        
        -- Log error
        DBMS_OUTPUT.PUT_LINE('Error in AIM_ORDER_GENERATION_SP: ' || V_ERROR_CODE || ' - ' || V_ERROR_MSG);
        
        P_RESULT := -2;
        RAISE;
END AIM_ORDER_GENERATION_SP;
/

-- Create sequence for PO numbers if it doesn't exist
CREATE SEQUENCE SEQ_PO_NUMBER
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;
/
