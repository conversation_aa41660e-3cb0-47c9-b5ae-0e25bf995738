-- =====================================================
-- Procedure: AIM_FORECAST_MASTER_GET_SP
-- Description: Gets AIM Forecast Master Data
-- Converted from SQL Server to Oracle PL/SQL
-- =====================================================

CREATE OR REPLACE PROCEDURE AIM_FORECAST_MASTER_GET_SP (
    P_REPOSITORY_KEY        IN NUMBER,
    P_LCID                  IN VARCHAR2,
    P_ITEM                  IN VARCHAR2,
    P_PERIOD_BEG_DATE       IN DATE,
    P_PERIOD_END_DATE       IN DATE,
    P_CURSOR                OUT SYS_REFCURSOR,
    P_RESULT                OUT NUMBER
) AS
    V_FOUND NUMBER := 0;
    V_ERROR_CODE NUMBER;
    V_ERROR_MSG VARCHAR2(4000);
BEGIN
    -- Validate required parameters
    IF P_LCID IS NULL THEN
        P_RESULT := -1;
        RETURN;
    END IF;

    IF P_ITEM IS NULL THEN
        P_RESULT := -1;
        RETURN;
    END IF;

    -- Open cursor with forecast master data
    OPEN P_CURSOR FOR
        SELECT M.LCID,
               M.ITEM,
               M.PERIOD_BEG_DATE,
               M.PERIOD_END_DATE,
               NVL(F.FCST, 0) AS FCST,
               NVL(M.QTY_ADJ, 0) AS QTY_ADJ,
               M.QTY_ADJ_PCT,
               NVL(M.QTY_ADJ_OVERRIDE, 0) AS QTY_ADJ_OVERRIDE,
               M.ADJ_OVERRIDE
        FROM FORECAST_REPOSITORY_DETAIL F
        INNER JOIN AIM_FCST_MASTER M
            ON F.LCID = M.LCID
           AND F.ITEM = M.ITEM
           AND F.REPOSITORY_KEY = P_REPOSITORY_KEY
           AND M.LCID = P_LCID
           AND M.ITEM = P_ITEM
           AND F.FCST_PD_BEG_DATE = M.PERIOD_BEG_DATE
           AND M.PERIOD_BEG_DATE >= P_PERIOD_BEG_DATE
           AND M.PERIOD_END_DATE <= P_PERIOD_END_DATE
        ORDER BY M.PERIOD_BEG_DATE;

    -- Check if any rows were found by attempting to fetch
    DECLARE
        V_TEST_CURSOR SYS_REFCURSOR;
        V_TEMP_LCID VARCHAR2(12);
    BEGIN
        OPEN V_TEST_CURSOR FOR
            SELECT M.LCID
            FROM FORECAST_REPOSITORY_DETAIL F
            INNER JOIN AIM_FCST_MASTER M
                ON F.LCID = M.LCID
               AND F.ITEM = M.ITEM
               AND F.REPOSITORY_KEY = P_REPOSITORY_KEY
               AND M.LCID = P_LCID
               AND M.ITEM = P_ITEM
               AND F.FCST_PD_BEG_DATE = M.PERIOD_BEG_DATE
               AND M.PERIOD_BEG_DATE >= P_PERIOD_BEG_DATE
               AND M.PERIOD_END_DATE <= P_PERIOD_END_DATE
            ORDER BY M.PERIOD_BEG_DATE;
        
        FETCH V_TEST_CURSOR INTO V_TEMP_LCID;
        IF V_TEST_CURSOR%FOUND THEN
            V_FOUND := 1;
        END IF;
        CLOSE V_TEST_CURSOR;
    END;

    IF V_FOUND > 0 THEN
        P_RESULT := 0;  -- SUCCESSFUL
    ELSE
        P_RESULT := -1; -- NO DATA FOUND
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        V_ERROR_CODE := SQLCODE;
        V_ERROR_MSG := SQLERRM;
        
        -- Close cursor if open
        IF P_CURSOR%ISOPEN THEN
            CLOSE P_CURSOR;
        END IF;
        
        -- Log error
        DBMS_OUTPUT.PUT_LINE('Error in AIM_FORECAST_MASTER_GET_SP: ' || V_ERROR_CODE || ' - ' || V_ERROR_MSG);
        
        P_RESULT := -2; -- SQL ERROR
        RAISE;
END AIM_FORECAST_MASTER_GET_SP;
/
