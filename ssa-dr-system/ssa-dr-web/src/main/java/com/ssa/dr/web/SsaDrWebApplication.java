package com.ssa.dr.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for SSA Demand Replenishment System
 */
@SpringBootApplication(scanBasePackages = "com.ssa.dr")
@EntityScan(basePackages = "com.ssa.dr.entity")
@EnableJpaRepositories(basePackages = "com.ssa.dr.dao")
@EnableTransactionManagement
@EnableCaching
@EnableAsync
@EnableScheduling
public class SsaDrWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(SsaDrWebApplication.class, args);
    }
}
