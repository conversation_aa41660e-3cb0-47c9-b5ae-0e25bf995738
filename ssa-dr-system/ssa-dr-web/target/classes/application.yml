server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: ssa-dr-system
  
  # Oracle Database Configuration
  datasource:
    url: ***********************************
    username: ${DB_USERNAME:ssa_dr}
    password: ${DB_PASSWORD:ssa_dr_password}
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      leak-detection-threshold: 60000

  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.OracleDialect
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  # Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  # RabbitMQ Configuration
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: /

  # Security Configuration
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# Logging Configuration
logging:
  level:
    com.ssa.dr: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ssa-dr-system.log

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# OpenAPI Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Application Specific Configuration
ssa-dr:
  forecast:
    batch-size: 1000
    thread-pool-size: 10
  allocation:
    max-iterations: 100
    tolerance: 0.01
  cache:
    ttl: 3600 # seconds
  security:
    jwt:
      secret: ${JWT_SECRET:mySecretKey}
      expiration: 86400 # 24 hours in seconds
