package com.ssa.dr.common.exception;

import lombok.Getter;

/**
 * Base exception class for SSA DR system
 */
@Getter
public class SsaDrException extends RuntimeException {
    
    private final String errorCode;
    private final Object[] args;
    
    public SsaDrException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.args = null;
    }
    
    public SsaDrException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }
    
    public SsaDrException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.args = null;
    }
    
    public SsaDrException(String errorCode, String message, Throwable cause, Object... args) {
        super(message, cause);
        this.errorCode = errorCode;
        this.args = args;
    }
}

/**
 * Business logic exception
 */
@Getter
public class BusinessException extends SsaDrException {
    
    public BusinessException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public BusinessException(String errorCode, String message, Object... args) {
        super(errorCode, message, args);
    }
}

/**
 * Data access exception
 */
@Getter
public class DataAccessException extends SsaDrException {
    
    public DataAccessException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public DataAccessException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
}

/**
 * Validation exception
 */
@Getter
public class ValidationException extends SsaDrException {
    
    public ValidationException(String errorCode, String message) {
        super(errorCode, message);
    }
    
    public ValidationException(String errorCode, String message, Object... args) {
        super(errorCode, message, args);
    }
}
