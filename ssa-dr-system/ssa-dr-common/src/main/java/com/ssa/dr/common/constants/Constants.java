package com.ssa.dr.common.constants;

/**
 * Application-wide constants
 */
public final class Constants {
    
    private Constants() {
        // Utility class
    }
    
    // Date Formats
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    // Item Status
    public static final class ItemStatus {
        public static final String ACTIVE = "A";
        public static final String INACTIVE = "I";
        public static final String DISCONTINUED = "D";
        public static final String PENDING = "P";
    }
    
    // Velocity Codes
    public static final class VelocityCode {
        public static final String FAST = "A";
        public static final String MEDIUM = "B";
        public static final String SLOW = "C";
        public static final String DEAD = "D";
    }
    
    // Buy Strategy
    public static final class BuyStrategy {
        public static final String OPTIMAL = "O";
        public static final String MIN_MAX = "M";
        public static final String TIME_SUPPLY = "T";
    }
    
    // Forecast Methods
    public static final class ForecastMethod {
        public static final int MOVING_AVERAGE = 1;
        public static final int EXPONENTIAL_SMOOTHING = 2;
        public static final int LINEAR_REGRESSION = 3;
        public static final int SEASONAL_DECOMPOSITION = 4;
        public static final int CROSTON = 5;
    }
    
    // PO Status
    public static final class POStatus {
        public static final String DRAFT = "D";
        public static final String RELEASED = "R";
        public static final String CLOSED = "C";
        public static final String CANCELLED = "X";
    }
    
    // Order Status
    public static final class OrderStatus {
        public static final String OPEN = "O";
        public static final String CLOSED = "C";
        public static final String CANCELLED = "X";
    }
    
    // Document Types
    public static final class DocumentType {
        public static final String SALES_ORDER = "SO";
        public static final String PURCHASE_ORDER = "PO";
        public static final String TRANSFER_ORDER = "TO";
        public static final String ADJUSTMENT = "ADJ";
    }
    
    // Transaction Types
    public static final class TransactionType {
        public static final String ISSUE_RECEIPT = "IR";
        public static final String ISSUE_ADJUSTMENT = "IA";
        public static final String RECEIPT = "RC";
        public static final String ADJUSTMENT = "AD";
    }
    
    // Pack Rounding
    public static final class PackRounding {
        public static final String ROUND = "R";
        public static final String UP = "U";
        public static final String DOWN = "D";
        public static final String NONE = "N";
    }
    
    // Yes/No Flags
    public static final class YesNo {
        public static final String YES = "Y";
        public static final String NO = "N";
    }
    
    // Default Values
    public static final class Defaults {
        public static final String DEFAULT_OPTION_ID = "DEFLT";
        public static final String DEFAULT_UOM = "EA";
        public static final int DEFAULT_CONV_FACTOR = 1;
        public static final double DEFAULT_SERVICE_LEVEL = 0.95;
        public static final String DEFAULT_CURRENCY = "USD";
    }
    
    // Cache Keys
    public static final class CacheKeys {
        public static final String ITEMS = "items";
        public static final String LOCATIONS = "locations";
        public static final String VENDORS = "vendors";
        public static final String FORECASTS = "forecasts";
        public static final String INVENTORY = "inventory";
    }
    
    // Queue Names
    public static final class QueueNames {
        public static final String FORECAST_QUEUE = "forecast.queue";
        public static final String ALLOCATION_QUEUE = "allocation.queue";
        public static final String ORDER_GENERATION_QUEUE = "order.generation.queue";
        public static final String DATA_IMPORT_QUEUE = "data.import.queue";
    }
    
    // Error Codes
    public static final class ErrorCodes {
        public static final String ITEM_NOT_FOUND = "ITEM_001";
        public static final String LOCATION_NOT_FOUND = "LOC_001";
        public static final String VENDOR_NOT_FOUND = "VEN_001";
        public static final String INSUFFICIENT_INVENTORY = "INV_001";
        public static final String INVALID_FORECAST_METHOD = "FCT_001";
        public static final String ALLOCATION_FAILED = "ALL_001";
    }
}
