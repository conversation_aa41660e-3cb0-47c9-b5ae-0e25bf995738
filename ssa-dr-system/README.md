# SSA Demand Replenishment System (Java版)

## 项目概述

这是一个用Java重写的SSA需求预测与库存补货管理系统，原系统基于Visual Basic和SQL Server，现在使用现代Java技术栈和Oracle数据库重新实现。

## 系统功能

### 核心业务模块

1. **需求预测模块 (Demand Forecasting)**
   - 多种预测算法（移动平均、指数平滑、线性回归、季节性分解、Croston等）
   - 季节性调整和促销影响分析
   - 预测精度跟踪和误差分析
   - 预测仓库版本管理

2. **库存管理模块 (Inventory Management)**
   - 实时库存状态跟踪
   - 安全库存计算和优化
   - ABC分析和速度代码管理
   - 多仓库库存管理
   - 库存转移和调拨

3. **采购订单生成模块 (Purchase Order Generation)**
   - 基于预测和库存水平的自动订单生成
   - 多种采购策略（最优订货量、最小最大、时间供应）
   - 供应商管理和评估
   - 订单优化和包装取整

4. **分配管理模块 (Allocation Management)**
   - 智能库存分配引擎
   - 多种分配策略和优先级管理
   - 替代品和配套商品处理
   - 分配结果审核和调整

5. **数据交换模块 (Data Exchange)**
   - 外部数据导入和验证
   - 批量数据处理
   - 数据质量检查

6. **报表分析模块 (Reporting & Analytics)**
   - 买手决策支持报表
   - 商品分析和库存报表
   - 供应商绩效分析
   - 预测准确性分析

## 技术架构

### 技术栈

- **后端框架**: Spring Boot 3.x
- **数据库**: Oracle Database
- **ORM框架**: Spring Data JPA + Hibernate
- **连接池**: HikariCP
- **安全框架**: Spring Security
- **API文档**: OpenAPI 3 (Swagger)
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **构建工具**: Maven
- **Java版本**: 17

### 项目结构

```
ssa-dr-system/
├── ssa-dr-common/          # 公共模块（常量、异常、工具类）
├── ssa-dr-entity/          # 实体类模块（JPA实体和DTO）
├── ssa-dr-dao/             # 数据访问层（Repository接口）
├── ssa-dr-service/         # 业务服务层（待开发）
├── ssa-dr-web/             # Web控制器层（待开发）
├── ssa-dr-forecast/        # 预测引擎模块（待开发）
├── ssa-dr-allocation/      # 分配引擎模块（待开发）
├── ssa-dr-report/          # 报表模块（待开发）
└── database/               # 数据库脚本
    └── oracle/
        ├── tables/         # 表结构脚本
        └── procedures/     # 存储过程脚本
```

## 数据库设计

### 核心表结构

1. **ITEM** - 商品主数据表
   - 包含商品基本信息、库存数量、预测参数等
   - 支持多仓库、多供应商管理

2. **AIM_LOCATIONS** - 地点主数据表
   - 门店、仓库、配送中心信息
   - 地理位置和联系信息

3. **AIM_VENDORS** - 供应商主数据表
   - 供应商基本信息和绩效指标
   - 采购条款和评级信息

4. **FORECAST_REPOSITORY** - 预测仓库表
   - 预测版本管理
   - 预测精度跟踪

### 数据库特性

- **Oracle语法**: 所有表和存储过程都转换为Oracle语法
- **审计字段**: 每个表都包含创建时间、更新时间、版本号等审计字段
- **约束检查**: 完整的数据完整性约束
- **索引优化**: 针对查询性能优化的索引设计

## 已完成功能

### ✅ 项目基础架构
- Maven多模块项目结构
- Spring Boot配置
- Oracle数据库连接配置
- 基础依赖管理

### ✅ 数据库层
- 核心表结构转换（SQL Server → Oracle）
- 关键存储过程转换（PL/SQL）
- 数据类型映射和约束转换

### ✅ 实体层
- JPA实体类设计
- 复合主键支持
- 数据验证注解
- 审计字段自动管理

### ✅ 数据访问层
- Spring Data JPA Repository
- 自定义查询方法
- 分页和排序支持
- 原生SQL查询支持

## 待开发功能

### 🔄 业务服务层
- 预测算法实现
- 库存管理业务逻辑
- 采购订单生成逻辑
- 分配算法实现

### 🔄 Web控制器层
- REST API设计
- 安全认证和授权
- API文档生成
- 异常处理

### 🔄 前端界面
- React + TypeScript
- 现代化用户界面
- 响应式设计
- 数据可视化

## 快速开始

### 环境要求

- Java 17+
- Maven 3.8+
- Oracle Database 19c+
- Redis 6.0+
- RabbitMQ 3.8+

### 数据库设置

1. 创建Oracle数据库用户：
```sql
CREATE USER ssa_dr IDENTIFIED BY ssa_dr_password;
GRANT CONNECT, RESOURCE, DBA TO ssa_dr;
```

2. 执行表结构脚本：
```bash
cd database/oracle/tables
sqlplus ssa_dr/ssa_dr_password@localhost:1521/XE @01_item.sql
sqlplus ssa_dr/ssa_dr_password@localhost:1521/XE @02_aim_locations.sql
sqlplus ssa_dr/ssa_dr_password@localhost:1521/XE @03_aim_vendors.sql
```

3. 执行存储过程脚本：
```bash
cd database/oracle/procedures
sqlplus ssa_dr/ssa_dr_password@localhost:1521/XE @01_aim_demand_update_sp.sql
```

### 应用启动

1. 克隆项目：
```bash
git clone <repository-url>
cd ssa-dr-system
```

2. 配置数据库连接：
```yaml
# 修改 ssa-dr-web/src/main/resources/application.yml
spring:
  datasource:
    url: ***********************************
    username: ssa_dr
    password: ssa_dr_password
```

3. 编译和运行：
```bash
mvn clean install
cd ssa-dr-web
mvn spring-boot:run
```

4. 访问应用：
- 应用地址: http://localhost:8080/api/v1
- API文档: http://localhost:8080/api/v1/swagger-ui.html
- 健康检查: http://localhost:8080/api/v1/actuator/health

## 配置说明

### 应用配置

主要配置文件位于 `ssa-dr-web/src/main/resources/application.yml`：

- 数据库连接配置
- Redis缓存配置
- RabbitMQ消息队列配置
- 日志配置
- 业务参数配置

### 环境变量

支持通过环境变量覆盖配置：

- `DB_USERNAME`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `REDIS_HOST`: Redis主机地址
- `RABBITMQ_HOST`: RabbitMQ主机地址

## 开发指南

### 代码规范

- 使用Lombok减少样板代码
- 实体类使用JPA注解
- 服务类使用@Service注解
- Repository使用Spring Data JPA
- 控制器使用@RestController注解

### 测试

- 单元测试使用JUnit 5
- 集成测试使用TestContainers
- 数据库测试使用@DataJpaTest

### API设计

- RESTful API设计原则
- 统一的响应格式
- 完整的错误处理
- OpenAPI文档

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

本项目基于原SSA_DR系统重新开发，仅供学习和研究使用。

## 联系方式

如有问题或建议，请创建Issue或联系开发团队。
