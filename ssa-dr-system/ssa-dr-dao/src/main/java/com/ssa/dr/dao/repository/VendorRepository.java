package com.ssa.dr.dao.repository;

import com.ssa.dr.entity.Vendor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Vendor entity
 */
@Repository
public interface VendorRepository extends JpaRepository<Vendor, String>, JpaSpecificationExecutor<Vendor> {

    /**
     * Find vendors by status
     */
    List<Vendor> findByVnStat(String vnStat);

    /**
     * Find active vendors
     */
    @Query("SELECT v FROM Vendor v WHERE v.vnStat = 'A'")
    List<Vendor> findActiveVendors();

    /**
     * Find vendors on hold
     */
    @Query("SELECT v FROM Vendor v WHERE v.vnStat = 'H'")
    List<Vendor> findVendorsOnHold();

    /**
     * Find vendors by type
     */
    List<Vendor> findByVnType(String vnType);

    /**
     * Find suppliers (vendor type = 'S')
     */
    @Query("SELECT v FROM Vendor v WHERE v.vnType = 'S' AND v.vnStat = 'A'")
    List<Vendor> findActiveSuppliers();

    /**
     * Find manufacturers (vendor type = 'M')
     */
    @Query("SELECT v FROM Vendor v WHERE v.vnType = 'M' AND v.vnStat = 'A'")
    List<Vendor> findActiveManufacturers();

    /**
     * Find distributors (vendor type = 'D')
     */
    @Query("SELECT v FROM Vendor v WHERE v.vnType = 'D' AND v.vnStat = 'A'")
    List<Vendor> findActiveDistributors();

    /**
     * Find preferred vendors
     */
    @Query("SELECT v FROM Vendor v WHERE v.preferredVendor = 'Y' AND v.vnStat = 'A'")
    List<Vendor> findPreferredVendors();

    /**
     * Find EDI capable vendors
     */
    @Query("SELECT v FROM Vendor v WHERE v.ediCapable = 'Y' AND v.vnStat = 'A'")
    List<Vendor> findEdiCapableVendors();

    /**
     * Find drop ship vendors
     */
    @Query("SELECT v FROM Vendor v WHERE v.dropShipVendor = 'Y' AND v.vnStat = 'A'")
    List<Vendor> findDropShipVendors();

    /**
     * Find consignment vendors
     */
    @Query("SELECT v FROM Vendor v WHERE v.consignmentVendor = 'Y' AND v.vnStat = 'A'")
    List<Vendor> findConsignmentVendors();

    /**
     * Find vendors by rating
     */
    List<Vendor> findByRating(String rating);

    /**
     * Find vendors with rating above threshold
     */
    @Query("SELECT v FROM Vendor v WHERE v.overallRating >= :threshold AND v.vnStat = 'A'")
    List<Vendor> findVendorsWithHighRating(@Param("threshold") BigDecimal threshold);

    /**
     * Find vendors by buyer ID
     */
    List<Vendor> findByBuyerId(String buyerId);

    /**
     * Find vendors by planner ID
     */
    List<Vendor> findByPlannerId(String plannerId);

    /**
     * Find vendors by state
     */
    List<Vendor> findByState(String state);

    /**
     * Find vendors by country
     */
    List<Vendor> findByCountry(String country);

    /**
     * Find vendors by currency code
     */
    List<Vendor> findByCurrencyCode(String currencyCode);

    /**
     * Find vendors by description containing text (case-insensitive)
     */
    @Query("SELECT v FROM Vendor v WHERE UPPER(v.vnDesc) LIKE UPPER(CONCAT('%', :searchText, '%'))")
    List<Vendor> findByDescriptionContaining(@Param("searchText") String searchText);

    /**
     * Find vendors by email
     */
    Optional<Vendor> findByEmail(String email);

    /**
     * Find vendors by phone
     */
    List<Vendor> findByPhone(String phone);

    /**
     * Find vendors by contact name
     */
    List<Vendor> findByContactName(String contactName);

    /**
     * Find vendors by tax ID
     */
    Optional<Vendor> findByTaxId(String taxId);

    /**
     * Find vendors by DUNS number
     */
    Optional<Vendor> findByDunsNumber(String dunsNumber);

    /**
     * Find vendors with on-time delivery above threshold
     */
    @Query("SELECT v FROM Vendor v WHERE v.onTimeDeliveryPct >= :threshold AND v.vnStat = 'A'")
    List<Vendor> findVendorsWithGoodDelivery(@Param("threshold") BigDecimal threshold);

    /**
     * Find vendors with quality rating above threshold
     */
    @Query("SELECT v FROM Vendor v WHERE v.qualityRating >= :threshold AND v.vnStat = 'A'")
    List<Vendor> findVendorsWithGoodQuality(@Param("threshold") BigDecimal threshold);

    /**
     * Find vendors with recent orders
     */
    @Query("SELECT v FROM Vendor v WHERE v.lastOrderDate >= :date AND v.vnStat = 'A'")
    List<Vendor> findVendorsWithRecentOrders(@Param("date") LocalDate date);

    /**
     * Find vendors with high purchase volume
     */
    @Query("SELECT v FROM Vendor v WHERE v.ytdPurchaseAmt >= :amount AND v.vnStat = 'A'")
    List<Vendor> findVendorsWithHighVolume(@Param("amount") BigDecimal amount);

    /**
     * Search vendors by multiple criteria
     */
    @Query("SELECT v FROM Vendor v WHERE " +
           "(:vnStat IS NULL OR v.vnStat = :vnStat) AND " +
           "(:vnType IS NULL OR v.vnType = :vnType) AND " +
           "(:rating IS NULL OR v.rating = :rating) AND " +
           "(:buyerId IS NULL OR v.buyerId = :buyerId) AND " +
           "(:plannerId IS NULL OR v.plannerId = :plannerId) AND " +
           "(:state IS NULL OR v.state = :state) AND " +
           "(:country IS NULL OR v.country = :country)")
    List<Vendor> findVendorsByCriteria(@Param("vnStat") String vnStat,
                                     @Param("vnType") String vnType,
                                     @Param("rating") String rating,
                                     @Param("buyerId") String buyerId,
                                     @Param("plannerId") String plannerId,
                                     @Param("state") String state,
                                     @Param("country") String country);

    /**
     * Find vendors with pagination by status
     */
    Page<Vendor> findByVnStat(String vnStat, Pageable pageable);

    /**
     * Find vendors with pagination by type
     */
    Page<Vendor> findByVnType(String vnType, Pageable pageable);

    /**
     * Update vendor performance metrics
     */
    @Modifying
    @Query("UPDATE Vendor v SET " +
           "v.onTimeDeliveryPct = :onTimeDeliveryPct, " +
           "v.qualityRating = :qualityRating, " +
           "v.costRating = :costRating, " +
           "v.serviceRating = :serviceRating, " +
           "v.overallRating = :overallRating, " +
           "v.updatedDate = CURRENT_TIMESTAMP " +
           "WHERE v.vnId = :vnId")
    int updatePerformanceMetrics(@Param("vnId") String vnId,
                               @Param("onTimeDeliveryPct") BigDecimal onTimeDeliveryPct,
                               @Param("qualityRating") BigDecimal qualityRating,
                               @Param("costRating") BigDecimal costRating,
                               @Param("serviceRating") BigDecimal serviceRating,
                               @Param("overallRating") BigDecimal overallRating);

    /**
     * Update vendor purchase amounts
     */
    @Modifying
    @Query("UPDATE Vendor v SET " +
           "v.ytdPurchaseAmt = :ytdPurchaseAmt, " +
           "v.lyPurchaseAmt = :lyPurchaseAmt, " +
           "v.totalPurchaseAmt = :totalPurchaseAmt, " +
           "v.lastOrderDate = :lastOrderDate, " +
           "v.updatedDate = CURRENT_TIMESTAMP " +
           "WHERE v.vnId = :vnId")
    int updatePurchaseAmounts(@Param("vnId") String vnId,
                            @Param("ytdPurchaseAmt") BigDecimal ytdPurchaseAmt,
                            @Param("lyPurchaseAmt") BigDecimal lyPurchaseAmt,
                            @Param("totalPurchaseAmt") BigDecimal totalPurchaseAmt,
                            @Param("lastOrderDate") LocalDate lastOrderDate);

    /**
     * Count vendors by status
     */
    @Query("SELECT v.vnStat, COUNT(v) FROM Vendor v GROUP BY v.vnStat")
    List<Object[]> countVendorsByStatus();

    /**
     * Count vendors by type
     */
    @Query("SELECT v.vnType, COUNT(v) FROM Vendor v GROUP BY v.vnType")
    List<Object[]> countVendorsByType();

    /**
     * Count vendors by rating
     */
    @Query("SELECT v.rating, COUNT(v) FROM Vendor v GROUP BY v.rating")
    List<Object[]> countVendorsByRating();

    /**
     * Get vendor performance summary
     */
    @Query("SELECT v.vnId, v.vnDesc, v.onTimeDeliveryPct, v.qualityRating, v.overallRating, v.ytdPurchaseAmt " +
           "FROM Vendor v WHERE v.vnStat = 'A' ORDER BY v.overallRating DESC")
    List<Object[]> getVendorPerformanceSummary();

    /**
     * Find top vendors by purchase volume
     */
    @Query("SELECT v FROM Vendor v WHERE v.vnStat = 'A' ORDER BY v.ytdPurchaseAmt DESC")
    List<Vendor> findTopVendorsByVolume(Pageable pageable);

    /**
     * Find vendors by lead time range
     */
    @Query("SELECT v FROM Vendor v WHERE v.leadTime BETWEEN :minLeadTime AND :maxLeadTime AND v.vnStat = 'A'")
    List<Vendor> findVendorsByLeadTimeRange(@Param("minLeadTime") Integer minLeadTime,
                                          @Param("maxLeadTime") Integer maxLeadTime);

    /**
     * Check if vendor exists by ID
     */
    boolean existsByVnId(String vnId);

    /**
     * Find distinct buyer IDs
     */
    @Query("SELECT DISTINCT v.buyerId FROM Vendor v WHERE v.buyerId != '' ORDER BY v.buyerId")
    List<String> findDistinctBuyerIds();

    /**
     * Find distinct planner IDs
     */
    @Query("SELECT DISTINCT v.plannerId FROM Vendor v WHERE v.plannerId != '' ORDER BY v.plannerId")
    List<String> findDistinctPlannerIds();

    /**
     * Find distinct states
     */
    @Query("SELECT DISTINCT v.state FROM Vendor v WHERE v.state != '' ORDER BY v.state")
    List<String> findDistinctStates();

    /**
     * Find distinct countries
     */
    @Query("SELECT DISTINCT v.country FROM Vendor v WHERE v.country != '' ORDER BY v.country")
    List<String> findDistinctCountries();
}
