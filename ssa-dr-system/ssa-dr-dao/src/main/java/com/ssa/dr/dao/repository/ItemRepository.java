package com.ssa.dr.dao.repository;

import com.ssa.dr.entity.Item;
import com.ssa.dr.entity.ItemId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Item entity
 */
@Repository
public interface ItemRepository extends JpaRepository<Item, ItemId>, JpaSpecificationExecutor<Item> {

    /**
     * Find items by location ID
     */
    List<Item> findByIdLcid(String lcid);

    /**
     * Find items by location ID with pagination
     */
    Page<Item> findByIdLcid(String lcid, Pageable pageable);

    /**
     * Find items by status
     */
    List<Item> findByItStat(String itStat);

    /**
     * Find active items by location
     */
    @Query("SELECT i FROM Item i WHERE i.id.lcid = :lcid AND i.itStat = 'A'")
    List<Item> findActiveItemsByLocation(@Param("lcid") String lcid);

    /**
     * Find items by vendor and assortment
     */
    List<Item> findByVnIdAndAssort(String vnId, String assort);

    /**
     * Find items by buyer ID
     */
    List<Item> findByById(String byId);

    /**
     * Find items by velocity code
     */
    List<Item> findByVelCode(String velCode);

    /**
     * Find items that need reorder
     */
    @Query("SELECT i FROM Item i WHERE (i.oh + i.oo - i.comStk - i.bkOrder - i.bkComStk) <= i.orderPt AND i.itStat = 'A'")
    List<Item> findItemsNeedingReorder();

    /**
     * Find items that need reorder by location
     */
    @Query("SELECT i FROM Item i WHERE i.id.lcid = :lcid AND (i.oh + i.oo - i.comStk - i.bkOrder - i.bkComStk) <= i.orderPt AND i.itStat = 'A'")
    List<Item> findItemsNeedingReorderByLocation(@Param("lcid") String lcid);

    /**
     * Find items by class hierarchy
     */
    @Query("SELECT i FROM Item i WHERE " +
           "(:class1 IS NULL OR i.class1 = :class1) AND " +
           "(:class2 IS NULL OR i.class2 = :class2) AND " +
           "(:class3 IS NULL OR i.class3 = :class3) AND " +
           "(:class4 IS NULL OR i.class4 = :class4)")
    List<Item> findByClassHierarchy(@Param("class1") String class1,
                                   @Param("class2") String class2,
                                   @Param("class3") String class3,
                                   @Param("class4") String class4);

    /**
     * Find items with low inventory
     */
    @Query("SELECT i FROM Item i WHERE (i.oh + i.oo - i.comStk - i.bkOrder - i.bkComStk) < i.safetyStock AND i.itStat = 'A'")
    List<Item> findItemsWithLowInventory();

    /**
     * Find items with excess inventory
     */
    @Query("SELECT i FROM Item i WHERE (i.oh + i.oo - i.comStk - i.bkOrder - i.bkComStk) > (i.orderPt + i.orderQty) AND i.itStat = 'A'")
    List<Item> findItemsWithExcessInventory();

    /**
     * Find items by forecast method
     */
    List<Item> findByFcstMethod(Integer fcstMethod);

    /**
     * Find items with forecast errors above threshold
     */
    @Query("SELECT i FROM Item i WHERE i.mae > :threshold AND i.itStat = 'A'")
    List<Item> findItemsWithHighForecastError(@Param("threshold") BigDecimal threshold);

    /**
     * Find items by MDC (Master Distribution Center)
     */
    List<Item> findByMdc(String mdc);

    /**
     * Find items with MDC flag
     */
    List<Item> findByMdcFlag(String mdcFlag);

    /**
     * Find items by UPC
     */
    Optional<Item> findByUpc(String upc);

    /**
     * Find items by description containing text (case-insensitive)
     */
    @Query("SELECT i FROM Item i WHERE UPPER(i.itDesc) LIKE UPPER(CONCAT('%', :searchText, '%'))")
    List<Item> findByDescriptionContaining(@Param("searchText") String searchText);

    /**
     * Find items with forecast date before specified date
     */
    List<Item> findByFcstDateBefore(LocalDate date);

    /**
     * Find items that are intermittent
     */
    List<Item> findByIsIntermittent(String isIntermittent);

    /**
     * Find items with Kit/BOM flag
     */
    List<Item> findByKitBomFlag(String kitBomFlag);

    /**
     * Update forecast information for an item
     */
    @Modifying
    @Query("UPDATE Item i SET " +
           "i.fcstDemand = :fcstDemand, " +
           "i.fcstRt = :fcstRt, " +
           "i.fcstLt = :fcstLt, " +
           "i.mae = :mae, " +
           "i.mse = :mse, " +
           "i.trend = :trend, " +
           "i.fcstDate = :fcstDate, " +
           "i.updatedDate = CURRENT_TIMESTAMP " +
           "WHERE i.id.lcid = :lcid AND i.id.item = :item")
    int updateForecastInfo(@Param("lcid") String lcid,
                          @Param("item") String item,
                          @Param("fcstDemand") BigDecimal fcstDemand,
                          @Param("fcstRt") BigDecimal fcstRt,
                          @Param("fcstLt") BigDecimal fcstLt,
                          @Param("mae") BigDecimal mae,
                          @Param("mse") BigDecimal mse,
                          @Param("trend") BigDecimal trend,
                          @Param("fcstDate") LocalDate fcstDate);

    /**
     * Update inventory quantities for an item
     */
    @Modifying
    @Query("UPDATE Item i SET " +
           "i.oh = :oh, " +
           "i.oo = :oo, " +
           "i.comStk = :comStk, " +
           "i.bkOrder = :bkOrder, " +
           "i.bkComStk = :bkComStk, " +
           "i.updatedDate = CURRENT_TIMESTAMP " +
           "WHERE i.id.lcid = :lcid AND i.id.item = :item")
    int updateInventoryQuantities(@Param("lcid") String lcid,
                                 @Param("item") String item,
                                 @Param("oh") Integer oh,
                                 @Param("oo") Integer oo,
                                 @Param("comStk") Integer comStk,
                                 @Param("bkOrder") Integer bkOrder,
                                 @Param("bkComStk") Integer bkComStk);

    /**
     * Update planning parameters for an item
     */
    @Modifying
    @Query("UPDATE Item i SET " +
           "i.orderPt = :orderPt, " +
           "i.orderQty = :orderQty, " +
           "i.safetyStock = :safetyStock, " +
           "i.leadTime = :leadTime, " +
           "i.reviewTime = :reviewTime, " +
           "i.updatedDate = CURRENT_TIMESTAMP " +
           "WHERE i.id.lcid = :lcid AND i.id.item = :item")
    int updatePlanningParameters(@Param("lcid") String lcid,
                               @Param("item") String item,
                               @Param("orderPt") Integer orderPt,
                               @Param("orderQty") Integer orderQty,
                               @Param("safetyStock") BigDecimal safetyStock,
                               @Param("leadTime") Integer leadTime,
                               @Param("reviewTime") Integer reviewTime);

    /**
     * Count items by status
     */
    @Query("SELECT i.itStat, COUNT(i) FROM Item i GROUP BY i.itStat")
    List<Object[]> countItemsByStatus();

    /**
     * Count items by velocity code
     */
    @Query("SELECT i.velCode, COUNT(i) FROM Item i GROUP BY i.velCode")
    List<Object[]> countItemsByVelocityCode();

    /**
     * Get total inventory value by location
     */
    @Query("SELECT i.id.lcid, SUM(i.oh * i.cost) FROM Item i WHERE i.itStat = 'A' GROUP BY i.id.lcid")
    List<Object[]> getTotalInventoryValueByLocation();

    /**
     * Find items with custom criteria using native query
     */
    @Query(value = "SELECT * FROM ITEM WHERE " +
                   "(:lcid IS NULL OR LCID = :lcid) AND " +
                   "(:vnId IS NULL OR VN_ID = :vnId) AND " +
                   "(:assort IS NULL OR ASSORT = :assort) AND " +
                   "(:byId IS NULL OR BY_ID = :byId) AND " +
                   "IT_STAT = 'A' " +
                   "ORDER BY LCID, ITEM",
           nativeQuery = true)
    List<Item> findItemsWithCriteria(@Param("lcid") String lcid,
                                    @Param("vnId") String vnId,
                                    @Param("assort") String assort,
                                    @Param("byId") String byId);
}
