package com.ssa.dr.dao.repository;

import com.ssa.dr.entity.Location;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Location entity
 */
@Repository
public interface LocationRepository extends JpaRepository<Location, String>, JpaSpecificationExecutor<Location> {

    /**
     * Find locations by status
     */
    List<Location> findByLcStat(String lcStat);

    /**
     * Find active locations
     */
    @Query("SELECT l FROM Location l WHERE l.lcStat = 'A'")
    List<Location> findActiveLocations();

    /**
     * Find locations by type
     */
    List<Location> findByLcType(String lcType);

    /**
     * Find stores (location type = 'S')
     */
    @Query("SELECT l FROM Location l WHERE l.lcType = 'S' AND l.lcStat = 'A'")
    List<Location> findActiveStores();

    /**
     * Find warehouses (location type = 'W')
     */
    @Query("SELECT l FROM Location l WHERE l.lcType = 'W' AND l.lcStat = 'A'")
    List<Location> findActiveWarehouses();

    /**
     * Find distribution centers (location type = 'D')
     */
    @Query("SELECT l FROM Location l WHERE l.lcType = 'D' AND l.lcStat = 'A'")
    List<Location> findActiveDistributionCenters();

    /**
     * Find locations by region
     */
    List<Location> findByRegion(String region);

    /**
     * Find locations by division
     */
    List<Location> findByDivision(String division);

    /**
     * Find locations by region and division
     */
    List<Location> findByRegionAndDivision(String region, String division);

    /**
     * Find locations by state
     */
    List<Location> findByState(String state);

    /**
     * Find locations by country
     */
    List<Location> findByCountry(String country);

    /**
     * Find locations by city
     */
    List<Location> findByCity(String city);

    /**
     * Find locations by zip code
     */
    List<Location> findByZipCode(String zipCode);

    /**
     * Find locations by description containing text (case-insensitive)
     */
    @Query("SELECT l FROM Location l WHERE UPPER(l.lcDesc) LIKE UPPER(CONCAT('%', :searchText, '%'))")
    List<Location> findByDescriptionContaining(@Param("searchText") String searchText);

    /**
     * Find locations by currency code
     */
    List<Location> findByCurrencyCode(String currencyCode);

    /**
     * Find locations by time zone
     */
    List<Location> findByTimeZone(String timeZone);

    /**
     * Find locations by default buyer ID
     */
    List<Location> findByDftById(String dftById);

    /**
     * Find locations with specific default buy strategy
     */
    List<Location> findByDftBuyStrat(String dftBuyStrat);

    /**
     * Find locations with specific default velocity code
     */
    List<Location> findByDftVelCode(String dftVelCode);

    /**
     * Find locations by lead time source
     */
    List<Location> findByLeadTimeSource(String leadTimeSource);

    /**
     * Search locations by multiple criteria
     */
    @Query("SELECT l FROM Location l WHERE " +
           "(:lcStat IS NULL OR l.lcStat = :lcStat) AND " +
           "(:lcType IS NULL OR l.lcType = :lcType) AND " +
           "(:region IS NULL OR l.region = :region) AND " +
           "(:division IS NULL OR l.division = :division) AND " +
           "(:state IS NULL OR l.state = :state) AND " +
           "(:country IS NULL OR l.country = :country)")
    List<Location> findLocationsByCriteria(@Param("lcStat") String lcStat,
                                         @Param("lcType") String lcType,
                                         @Param("region") String region,
                                         @Param("division") String division,
                                         @Param("state") String state,
                                         @Param("country") String country);

    /**
     * Find locations with pagination by status
     */
    Page<Location> findByLcStat(String lcStat, Pageable pageable);

    /**
     * Find locations with pagination by type
     */
    Page<Location> findByLcType(String lcType, Pageable pageable);

    /**
     * Count locations by status
     */
    @Query("SELECT l.lcStat, COUNT(l) FROM Location l GROUP BY l.lcStat")
    List<Object[]> countLocationsByStatus();

    /**
     * Count locations by type
     */
    @Query("SELECT l.lcType, COUNT(l) FROM Location l GROUP BY l.lcType")
    List<Object[]> countLocationsByType();

    /**
     * Count locations by region
     */
    @Query("SELECT l.region, COUNT(l) FROM Location l WHERE l.region != '' GROUP BY l.region")
    List<Object[]> countLocationsByRegion();

    /**
     * Count locations by division
     */
    @Query("SELECT l.division, COUNT(l) FROM Location l WHERE l.division != '' GROUP BY l.division")
    List<Object[]> countLocationsByDivision();

    /**
     * Find locations by contact email
     */
    Optional<Location> findByEmail(String email);

    /**
     * Find locations by contact phone
     */
    List<Location> findByPhone(String phone);

    /**
     * Find locations by contact name
     */
    List<Location> findByContactName(String contactName);

    /**
     * Check if location exists by ID
     */
    boolean existsByLcid(String lcid);

    /**
     * Find distinct regions
     */
    @Query("SELECT DISTINCT l.region FROM Location l WHERE l.region != '' ORDER BY l.region")
    List<String> findDistinctRegions();

    /**
     * Find distinct divisions
     */
    @Query("SELECT DISTINCT l.division FROM Location l WHERE l.division != '' ORDER BY l.division")
    List<String> findDistinctDivisions();

    /**
     * Find distinct states
     */
    @Query("SELECT DISTINCT l.state FROM Location l WHERE l.state != '' ORDER BY l.state")
    List<String> findDistinctStates();

    /**
     * Find distinct countries
     */
    @Query("SELECT DISTINCT l.country FROM Location l WHERE l.country != '' ORDER BY l.country")
    List<String> findDistinctCountries();

    /**
     * Find locations within a geographic area (simplified - you might want to use spatial queries)
     */
    @Query("SELECT l FROM Location l WHERE " +
           "l.state = :state AND " +
           "(:city IS NULL OR l.city = :city) AND " +
           "l.lcStat = 'A'")
    List<Location> findLocationsInArea(@Param("state") String state,
                                     @Param("city") String city);

    /**
     * Find locations by user defined field
     */
    List<Location> findByUserDefined(String userDefined);

    /**
     * Get location hierarchy (region -> division -> location)
     */
    @Query("SELECT l.region, l.division, l.lcid, l.lcDesc FROM Location l " +
           "WHERE l.lcStat = 'A' " +
           "ORDER BY l.region, l.division, l.lcid")
    List<Object[]> getLocationHierarchy();
}
