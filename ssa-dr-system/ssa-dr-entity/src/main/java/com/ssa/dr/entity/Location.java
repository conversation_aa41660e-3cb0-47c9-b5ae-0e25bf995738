package com.ssa.dr.entity;

import com.ssa.dr.common.constants.Constants;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Location entity representing stores, warehouses, and distribution centers
 */
@Entity
@Table(name = "AIM_LOCATIONS", indexes = {
    @Index(name = "IX_AIM_LOCATIONS_REGION", columnList = "region"),
    @Index(name = "IX_AIM_LOCATIONS_DIVISION", columnList = "division"),
    @Index(name = "IX_AIM_LOCATIONS_LC_STAT", columnList = "lcStat"),
    @Index(name = "IX_AIM_LOCATIONS_LC_TYPE", columnList = "lcType")
})
@Data
@EqualsAndHashCode(of = "lcid")
@ToString(exclude = {"createdBy", "updatedBy"})
public class Location {

    @Id
    @Column(name = "LCID", nullable = false, length = 12)
    @NotBlank(message = "Location ID is required")
    @Size(max = 12, message = "Location ID cannot exceed 12 characters")
    private String lcid;

    @Column(name = "LC_DESC", nullable = false, length = 50)
    @NotBlank(message = "Location description is required")
    @Size(max = 50, message = "Location description cannot exceed 50 characters")
    private String lcDesc;

    @Column(name = "LC_STAT", nullable = false, length = 1)
    @Pattern(regexp = "[AI]", message = "Location status must be A or I")
    private String lcStat = "A";

    @Column(name = "LC_TYPE", nullable = false, length = 1)
    @Pattern(regexp = "[SWDM]", message = "Location type must be S, W, D, or M")
    private String lcType = "S";

    @Column(name = "REGION", nullable = false, length = 12)
    private String region = "";

    @Column(name = "DIVISION", nullable = false, length = 12)
    private String division = "";

    @Column(name = "USER_DEFINED", nullable = false, length = 12)
    private String userDefined = "";

    // Address information
    @Column(name = "ADDRESS1", nullable = false, length = 50)
    private String address1 = "";

    @Column(name = "ADDRESS2", nullable = false, length = 50)
    private String address2 = "";

    @Column(name = "CITY", nullable = false, length = 30)
    private String city = "";

    @Column(name = "STATE", nullable = false, length = 10)
    private String state = "";

    @Column(name = "ZIP_CODE", nullable = false, length = 10)
    private String zipCode = "";

    @Column(name = "COUNTRY", nullable = false, length = 10)
    private String country = "";

    // Contact information
    @Column(name = "PHONE", nullable = false, length = 20)
    private String phone = "";

    @Column(name = "FAX", nullable = false, length = 20)
    private String fax = "";

    @Column(name = "EMAIL", nullable = false, length = 50)
    @Email(message = "Invalid email format")
    private String email = "";

    @Column(name = "CONTACT_NAME", nullable = false, length = 50)
    private String contactName = "";

    // Default settings for items at this location
    @Column(name = "DFT_OPTION_ID", nullable = false, length = 6)
    private String dftOptionId = Constants.Defaults.DEFAULT_OPTION_ID;

    @Column(name = "DFT_BUY_STRAT", nullable = false, length = 1)
    @Pattern(regexp = "[OMT]", message = "Default buy strategy must be O, M, or T")
    private String dftBuyStrat = Constants.BuyStrategy.OPTIMAL;

    @Column(name = "DFT_VEL_CODE", nullable = false, length = 1)
    @Pattern(regexp = "[ABCD]", message = "Default velocity code must be A, B, C, or D")
    private String dftVelCode = Constants.VelocityCode.SLOW;

    @Column(name = "DFT_BY_ID", nullable = false, length = 12)
    private String dftById = "";

    @Column(name = "DFT_SA_ID", nullable = false, length = 20)
    private String dftSaId = "";

    @Column(name = "DFT_PM_ID", nullable = false, length = 6)
    private String dftPmId = "";

    @Column(name = "LEAD_TIME_SOURCE", nullable = false, length = 1)
    @Pattern(regexp = "[IV]", message = "Lead time source must be I or V")
    private String leadTimeSource = "I";

    @Column(name = "STK_DATE", nullable = false)
    private LocalDate stkDate = LocalDate.now();

    @Column(name = "CURRENCY_CODE", nullable = false, length = 3)
    private String currencyCode = Constants.Defaults.DEFAULT_CURRENCY;

    @Column(name = "TIME_ZONE", nullable = false, length = 50)
    private String timeZone = "UTC";

    // Audit fields
    @CreationTimestamp
    @Column(name = "CREATED_DATE", nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY", nullable = false, length = 50, updatable = false)
    private String createdBy = "SYSTEM";

    @UpdateTimestamp
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY", nullable = false, length = 50)
    private String updatedBy = "SYSTEM";

    @Version
    @Column(name = "VERSION", nullable = false)
    private Long version = 0L;

    // Constructors
    public Location() {}

    public Location(String lcid) {
        this.lcid = lcid;
    }

    // Convenience methods
    public boolean isActive() {
        return "A".equals(lcStat);
    }

    public boolean isStore() {
        return "S".equals(lcType);
    }

    public boolean isWarehouse() {
        return "W".equals(lcType);
    }

    public boolean isDistributionCenter() {
        return "D".equals(lcType);
    }

    public boolean isManufacturing() {
        return "M".equals(lcType);
    }

    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (!address1.isEmpty()) {
            sb.append(address1);
        }
        if (!address2.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(address2);
        }
        if (!city.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(city);
        }
        if (!state.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(state);
        }
        if (!zipCode.isEmpty()) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(zipCode);
        }
        if (!country.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(country);
        }
        return sb.toString();
    }
}
