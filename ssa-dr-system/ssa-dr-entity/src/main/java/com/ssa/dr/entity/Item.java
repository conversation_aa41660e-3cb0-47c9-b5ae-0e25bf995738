package com.ssa.dr.entity;

import com.ssa.dr.common.constants.Constants;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Item entity representing the core item master table
 */
@Entity
@Table(name = "ITEM", indexes = {
    @Index(name = "IX_ITEM_BY_ID", columnList = "byId"),
    @Index(name = "IX_ITEM_ITEM", columnList = "item"),
    @Index(name = "IX_ITEM_IT_STAT", columnList = "itStat"),
    @Index(name = "IX_ITEM_VN_ID_ASSORT", columnList = "vnId, assort"),
    @Index(name = "IX_ITEM_MDC", columnList = "mdc")
})
@Data
@EqualsAndHashCode(of = {"lcid", "item"})
@ToString(exclude = {"createdBy", "updatedBy"})
public class Item {

    @EmbeddedId
    private ItemId id;

    @Column(name = "IT_DESC", nullable = false, length = 50)
    @NotBlank(message = "Item description is required")
    @Size(max = 50, message = "Item description cannot exceed 50 characters")
    private String itDesc;

    @Column(name = "IT_STAT", nullable = false, length = 1)
    @Pattern(regexp = "[AIDP]", message = "Item status must be A, I, D, or P")
    private String itStat = Constants.ItemStatus.ACTIVE;

    @Column(name = "ACT_DATE", nullable = false)
    private LocalDate actDate = LocalDate.of(1990, 1, 1);

    @Column(name = "INACT_DATE", nullable = false)
    private LocalDate inactDate = LocalDate.of(9999, 12, 31);

    @Column(name = "OPTION_ID", nullable = false, length = 6)
    private String optionId = Constants.Defaults.DEFAULT_OPTION_ID;

    @Column(name = "CLASS1", nullable = false, length = 50)
    private String class1 = "";

    @Column(name = "CLASS2", nullable = false, length = 50)
    private String class2 = "";

    @Column(name = "CLASS3", nullable = false, length = 50)
    private String class3 = "";

    @Column(name = "CLASS4", nullable = false, length = 50)
    private String class4 = "";

    @Column(name = "BIN_LOCATION", nullable = false, length = 12)
    private String binLocation = "";

    @Column(name = "BUY_STRAT", nullable = false, length = 1)
    @Pattern(regexp = "[OMT]", message = "Buy strategy must be O, M, or T")
    private String buyStrat = Constants.BuyStrategy.OPTIMAL;

    @Column(name = "VEL_CODE", nullable = false, length = 1)
    @Pattern(regexp = "[ABCD]", message = "Velocity code must be A, B, C, or D")
    private String velCode = Constants.VelocityCode.SLOW;

    @Column(name = "VN_ID", nullable = false, length = 12)
    private String vnId = "";

    @Column(name = "ASSORT", nullable = false, length = 12)
    private String assort = "";

    @Column(name = "BY_ID", nullable = false, length = 12)
    private String byId = "";

    @Column(name = "MDC", nullable = false, length = 12)
    private String mdc = "";

    @Column(name = "MDC_FLAG", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "MDC flag must be Y or N")
    private String mdcFlag = Constants.YesNo.NO;

    @Column(name = "SA_ID", nullable = false, length = 20)
    private String saId = "";

    @Column(name = "PM_ID", nullable = false, length = 6)
    private String pmId = "";

    @Column(name = "UPC", nullable = false, length = 22)
    private String upc = "";

    @Column(name = "WEIGHT", nullable = false, precision = 10, scale = 4)
    @DecimalMin(value = "0.0", message = "Weight must be non-negative")
    private BigDecimal weight = BigDecimal.ZERO;

    @Column(name = "CUBE", nullable = false, precision = 10, scale = 4)
    @DecimalMin(value = "0.0", message = "Cube must be non-negative")
    private BigDecimal cube = BigDecimal.ZERO;

    @Column(name = "LIST_PRICE", nullable = false, precision = 10, scale = 4)
    @DecimalMin(value = "0.0", message = "List price must be non-negative")
    private BigDecimal listPrice = BigDecimal.ZERO;

    @Column(name = "PRICE", nullable = false, precision = 10, scale = 4)
    @DecimalMin(value = "0.0", message = "Price must be non-negative")
    private BigDecimal price = BigDecimal.ZERO;

    @Column(name = "COST", nullable = false, precision = 10, scale = 4)
    @DecimalMin(value = "0.0", message = "Cost must be non-negative")
    private BigDecimal cost = BigDecimal.ZERO;

    @Column(name = "UOM", nullable = false, length = 6)
    private String uom = Constants.Defaults.DEFAULT_UOM;

    @Column(name = "CONV_FACTOR", nullable = false)
    @Min(value = 1, message = "Conversion factor must be at least 1")
    private Integer convFactor = Constants.Defaults.DEFAULT_CONV_FACTOR;

    @Column(name = "BUYING_UOM", nullable = false, length = 6)
    private String buyingUom = Constants.Defaults.DEFAULT_UOM;

    // Inventory quantities
    @Column(name = "OH", nullable = false)
    @Min(value = 0, message = "On hand quantity must be non-negative")
    private Integer oh = 0;

    @Column(name = "OO", nullable = false)
    @Min(value = 0, message = "On order quantity must be non-negative")
    private Integer oo = 0;

    @Column(name = "COM_STK", nullable = false)
    @Min(value = 0, message = "Committed stock must be non-negative")
    private Integer comStk = 0;

    @Column(name = "BK_ORDER", nullable = false)
    @Min(value = 0, message = "Back order quantity must be non-negative")
    private Integer bkOrder = 0;

    @Column(name = "BK_COM_STK", nullable = false)
    @Min(value = 0, message = "Back order committed stock must be non-negative")
    private Integer bkComStk = 0;

    // Planning parameters
    @Column(name = "LEAD_TIME", nullable = false)
    @Min(value = 0, message = "Lead time must be non-negative")
    private Integer leadTime = 0;

    @Column(name = "PACK_ROUNDING", nullable = false, length = 1)
    @Pattern(regexp = "[RUDN]", message = "Pack rounding must be R, U, D, or N")
    private String packRounding = Constants.PackRounding.ROUND;

    @Column(name = "I_MIN", nullable = false)
    @Min(value = 0, message = "Minimum inventory must be non-negative")
    private Integer iMin = 0;

    @Column(name = "I_MAX", nullable = false)
    @Min(value = 0, message = "Maximum inventory must be non-negative")
    private Integer iMax = 999999999;

    @Column(name = "ORDER_PT", nullable = false)
    @Min(value = 0, message = "Order point must be non-negative")
    private Integer orderPt = 0;

    @Column(name = "ORDER_QTY", nullable = false)
    @Min(value = 0, message = "Order quantity must be non-negative")
    private Integer orderQty = 0;

    @Column(name = "SAFETY_STOCK", nullable = false, precision = 10, scale = 2)
    @DecimalMin(value = "0.0", message = "Safety stock must be non-negative")
    private BigDecimal safetyStock = BigDecimal.ZERO;

    // Forecasting parameters
    @Column(name = "FCST_METHOD", nullable = false)
    @Min(value = 0, message = "Forecast method must be non-negative")
    private Integer fcstMethod = 0;

    @Column(name = "FCST_DEMAND", nullable = false, precision = 10, scale = 2)
    @DecimalMin(value = "0.0", message = "Forecast demand must be non-negative")
    private BigDecimal fcstDemand = BigDecimal.ZERO;

    @Column(name = "FCST_RT", nullable = false, precision = 10, scale = 2)
    @DecimalMin(value = "0.0", message = "Forecast RT must be non-negative")
    private BigDecimal fcstRt = BigDecimal.ZERO;

    @Column(name = "FCST_LT", nullable = false, precision = 10, scale = 2)
    @DecimalMin(value = "0.0", message = "Forecast LT must be non-negative")
    private BigDecimal fcstLt = BigDecimal.ZERO;

    @Column(name = "MAE", nullable = false, precision = 10, scale = 2)
    @DecimalMin(value = "0.0", message = "MAE must be non-negative")
    private BigDecimal mae = BigDecimal.ZERO;

    @Column(name = "MSE", nullable = false, precision = 10, scale = 2)
    @DecimalMin(value = "0.0", message = "MSE must be non-negative")
    private BigDecimal mse = BigDecimal.ZERO;

    @Column(name = "TREND", nullable = false, precision = 10, scale = 3)
    private BigDecimal trend = BigDecimal.ZERO;

    @Column(name = "FCST_DATE", nullable = false)
    private LocalDate fcstDate = LocalDate.of(1990, 1, 1);

    // Service level
    @Column(name = "D_SER", nullable = false, precision = 4, scale = 3)
    @DecimalMin(value = "0.0", message = "Service level must be non-negative")
    @DecimalMax(value = "1.0", message = "Service level cannot exceed 1.0")
    private BigDecimal dSer = new BigDecimal("0.95");

    // Flags
    @Column(name = "IS_INTERMITTENT", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Is intermittent flag must be Y or N")
    private String isIntermittent = Constants.YesNo.NO;

    @Column(name = "FREEZE_FORECAST", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Freeze forecast flag must be Y or N")
    private String freezeForecast = Constants.YesNo.NO;

    @Column(name = "KIT_BOM_FLAG", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Kit BOM flag must be Y or N")
    private String kitBomFlag = Constants.YesNo.NO;

    // Audit fields
    @CreationTimestamp
    @Column(name = "CREATED_DATE", nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY", nullable = false, length = 50, updatable = false)
    private String createdBy = "SYSTEM";

    @UpdateTimestamp
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY", nullable = false, length = 50)
    private String updatedBy = "SYSTEM";

    @Version
    @Column(name = "VERSION", nullable = false)
    private Long version = 0L;

    // Constructors
    public Item() {}

    public Item(String lcid, String item) {
        this.id = new ItemId(lcid, item);
    }

    public Item(ItemId id) {
        this.id = id;
    }

    // Convenience methods
    public String getLcid() {
        return id != null ? id.getLcid() : null;
    }

    public void setLcid(String lcid) {
        if (this.id == null) {
            this.id = new ItemId();
        }
        this.id.setLcid(lcid);
    }

    public String getItem() {
        return id != null ? id.getItem() : null;
    }

    public void setItem(String item) {
        if (this.id == null) {
            this.id = new ItemId();
        }
        this.id.setItem(item);
    }

    /**
     * Calculate available quantity
     */
    public Integer getAvailableQuantity() {
        return oh + oo - comStk - bkOrder - bkComStk;
    }

    /**
     * Check if item is active
     */
    public boolean isActive() {
        return Constants.ItemStatus.ACTIVE.equals(itStat);
    }

    /**
     * Check if item needs reorder
     */
    public boolean needsReorder() {
        return getAvailableQuantity() <= orderPt;
    }
}
