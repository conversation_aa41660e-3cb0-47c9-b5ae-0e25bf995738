package com.ssa.dr.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Composite primary key for Item entity
 */
@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemId implements Serializable {

    @Column(name = "LCID", nullable = false, length = 12)
    @NotBlank(message = "Location ID is required")
    @Size(max = 12, message = "Location ID cannot exceed 12 characters")
    private String lcid;

    @Column(name = "ITEM", nullable = false, length = 25)
    @NotBlank(message = "Item number is required")
    @Size(max = 25, message = "Item number cannot exceed 25 characters")
    private String item;
}
