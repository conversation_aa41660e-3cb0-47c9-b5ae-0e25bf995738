package com.ssa.dr.entity;

import com.ssa.dr.common.constants.Constants;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Vendor entity representing suppliers and manufacturers
 */
@Entity
@Table(name = "AIM_VENDORS", indexes = {
    @Index(name = "IX_AIM_VENDORS_VN_STAT", columnList = "vnStat"),
    @Index(name = "IX_AIM_VENDORS_VN_TYPE", columnList = "vnType"),
    @Index(name = "IX_AIM_VENDORS_BUYER_ID", columnList = "buyerId"),
    @Index(name = "IX_AIM_VENDORS_PLANNER_ID", columnList = "plannerId"),
    @Index(name = "IX_AIM_VENDORS_RATING", columnList = "rating"),
    @Index(name = "IX_AIM_VENDORS_PREFERRED", columnList = "preferredVendor")
})
@Data
@EqualsAndHashCode(of = "vnId")
@ToString(exclude = {"notes", "createdBy", "updatedBy"})
public class Vendor {

    @Id
    @Column(name = "VN_ID", nullable = false, length = 12)
    @NotBlank(message = "Vendor ID is required")
    @Size(max = 12, message = "Vendor ID cannot exceed 12 characters")
    private String vnId;

    @Column(name = "VN_DESC", nullable = false, length = 50)
    @NotBlank(message = "Vendor description is required")
    @Size(max = 50, message = "Vendor description cannot exceed 50 characters")
    private String vnDesc;

    @Column(name = "VN_STAT", nullable = false, length = 1)
    @Pattern(regexp = "[AIH]", message = "Vendor status must be A, I, or H")
    private String vnStat = "A";

    @Column(name = "VN_TYPE", nullable = false, length = 1)
    @Pattern(regexp = "[SMD]", message = "Vendor type must be S, M, or D")
    private String vnType = "S";

    // Address information
    @Column(name = "ADDRESS1", nullable = false, length = 50)
    private String address1 = "";

    @Column(name = "ADDRESS2", nullable = false, length = 50)
    private String address2 = "";

    @Column(name = "CITY", nullable = false, length = 30)
    private String city = "";

    @Column(name = "STATE", nullable = false, length = 10)
    private String state = "";

    @Column(name = "ZIP_CODE", nullable = false, length = 10)
    private String zipCode = "";

    @Column(name = "COUNTRY", nullable = false, length = 10)
    private String country = "";

    // Contact information
    @Column(name = "PHONE", nullable = false, length = 20)
    private String phone = "";

    @Column(name = "FAX", nullable = false, length = 20)
    private String fax = "";

    @Column(name = "EMAIL", nullable = false, length = 50)
    @Email(message = "Invalid email format")
    private String email = "";

    @Column(name = "CONTACT_NAME", nullable = false, length = 50)
    private String contactName = "";

    // Business terms
    @Column(name = "PAYMENT_TERMS", nullable = false, length = 20)
    private String paymentTerms = "";

    @Column(name = "CURRENCY_CODE", nullable = false, length = 3)
    private String currencyCode = Constants.Defaults.DEFAULT_CURRENCY;

    @Column(name = "LEAD_TIME", nullable = false)
    @Min(value = 0, message = "Lead time must be non-negative")
    private Integer leadTime = 0;

    @Column(name = "MIN_ORDER_QTY", nullable = false)
    @Min(value = 0, message = "Minimum order quantity must be non-negative")
    private Integer minOrderQty = 0;

    @Column(name = "MIN_ORDER_AMT", nullable = false, precision = 12, scale = 2)
    @DecimalMin(value = "0.0", message = "Minimum order amount must be non-negative")
    private BigDecimal minOrderAmt = BigDecimal.ZERO;

    @Column(name = "FREIGHT_TERMS", nullable = false, length = 10)
    private String freightTerms = "FOB";

    @Column(name = "SHIP_VIA", nullable = false, length = 20)
    private String shipVia = "";

    @Column(name = "TAX_ID", nullable = false, length = 20)
    private String taxId = "";

    @Column(name = "DUNS_NUMBER", nullable = false, length = 15)
    private String dunsNumber = "";

    // Performance metrics
    @Column(name = "RATING", nullable = false, length = 1)
    @Pattern(regexp = "[ABCD]", message = "Rating must be A, B, C, or D")
    private String rating = "A";

    @Column(name = "ON_TIME_DELIVERY_PCT", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "On-time delivery percentage must be non-negative")
    @DecimalMax(value = "100.0", message = "On-time delivery percentage cannot exceed 100")
    private BigDecimal onTimeDeliveryPct = BigDecimal.ZERO;

    @Column(name = "QUALITY_RATING", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "Quality rating must be non-negative")
    @DecimalMax(value = "100.0", message = "Quality rating cannot exceed 100")
    private BigDecimal qualityRating = BigDecimal.ZERO;

    @Column(name = "COST_RATING", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "Cost rating must be non-negative")
    @DecimalMax(value = "100.0", message = "Cost rating cannot exceed 100")
    private BigDecimal costRating = BigDecimal.ZERO;

    @Column(name = "SERVICE_RATING", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "Service rating must be non-negative")
    @DecimalMax(value = "100.0", message = "Service rating cannot exceed 100")
    private BigDecimal serviceRating = BigDecimal.ZERO;

    @Column(name = "OVERALL_RATING", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "Overall rating must be non-negative")
    @DecimalMax(value = "100.0", message = "Overall rating cannot exceed 100")
    private BigDecimal overallRating = BigDecimal.ZERO;

    // Purchase history
    @Column(name = "LAST_ORDER_DATE", nullable = false)
    private LocalDate lastOrderDate = LocalDate.of(1990, 1, 1);

    @Column(name = "LAST_RECEIPT_DATE", nullable = false)
    private LocalDate lastReceiptDate = LocalDate.of(1990, 1, 1);

    @Column(name = "YTD_PURCHASE_AMT", nullable = false, precision = 15, scale = 2)
    @DecimalMin(value = "0.0", message = "YTD purchase amount must be non-negative")
    private BigDecimal ytdPurchaseAmt = BigDecimal.ZERO;

    @Column(name = "LY_PURCHASE_AMT", nullable = false, precision = 15, scale = 2)
    @DecimalMin(value = "0.0", message = "Last year purchase amount must be non-negative")
    private BigDecimal lyPurchaseAmt = BigDecimal.ZERO;

    @Column(name = "TOTAL_PURCHASE_AMT", nullable = false, precision = 15, scale = 2)
    @DecimalMin(value = "0.0", message = "Total purchase amount must be non-negative")
    private BigDecimal totalPurchaseAmt = BigDecimal.ZERO;

    @Column(name = "ACTIVE_ITEMS_COUNT", nullable = false)
    @Min(value = 0, message = "Active items count must be non-negative")
    private Integer activeItemsCount = 0;

    // Vendor capabilities and flags
    @Column(name = "PREFERRED_VENDOR", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Preferred vendor flag must be Y or N")
    private String preferredVendor = Constants.YesNo.NO;

    @Column(name = "EDI_CAPABLE", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "EDI capable flag must be Y or N")
    private String ediCapable = Constants.YesNo.NO;

    @Column(name = "DROP_SHIP_VENDOR", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Drop ship vendor flag must be Y or N")
    private String dropShipVendor = Constants.YesNo.NO;

    @Column(name = "CONSIGNMENT_VENDOR", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Consignment vendor flag must be Y or N")
    private String consignmentVendor = Constants.YesNo.NO;

    @Column(name = "BLANKET_PO_VENDOR", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Blanket PO vendor flag must be Y or N")
    private String blanketPoVendor = Constants.YesNo.NO;

    @Column(name = "AUTO_RECEIVE", nullable = false, length = 1)
    @Pattern(regexp = "[YN]", message = "Auto receive flag must be Y or N")
    private String autoReceive = Constants.YesNo.NO;

    // Tolerance settings
    @Column(name = "PRICE_TOLERANCE_PCT", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "Price tolerance percentage must be non-negative")
    @DecimalMax(value = "100.0", message = "Price tolerance percentage cannot exceed 100")
    private BigDecimal priceTolerancePct = BigDecimal.ZERO;

    @Column(name = "QTY_TOLERANCE_PCT", nullable = false, precision = 5, scale = 2)
    @DecimalMin(value = "0.0", message = "Quantity tolerance percentage must be non-negative")
    @DecimalMax(value = "100.0", message = "Quantity tolerance percentage cannot exceed 100")
    private BigDecimal qtyTolerancePct = BigDecimal.ZERO;

    @Column(name = "DAYS_EARLY_TOLERANCE", nullable = false)
    @Min(value = 0, message = "Days early tolerance must be non-negative")
    private Integer daysEarlyTolerance = 0;

    @Column(name = "DAYS_LATE_TOLERANCE", nullable = false)
    @Min(value = 0, message = "Days late tolerance must be non-negative")
    private Integer daysLateTolerance = 0;

    // Responsible parties
    @Column(name = "BUYER_ID", nullable = false, length = 12)
    private String buyerId = "";

    @Column(name = "PLANNER_ID", nullable = false, length = 12)
    private String plannerId = "";

    // User references
    @Column(name = "USER_REF1", nullable = false, length = 20)
    private String userRef1 = "";

    @Column(name = "USER_REF2", nullable = false, length = 20)
    private String userRef2 = "";

    @Column(name = "USER_REF3", nullable = false, length = 20)
    private String userRef3 = "";

    @Lob
    @Column(name = "NOTES")
    private String notes;

    // Audit fields
    @CreationTimestamp
    @Column(name = "CREATED_DATE", nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @Column(name = "CREATED_BY", nullable = false, length = 50, updatable = false)
    private String createdBy = "SYSTEM";

    @UpdateTimestamp
    @Column(name = "UPDATED_DATE", nullable = false)
    private LocalDateTime updatedDate;

    @Column(name = "UPDATED_BY", nullable = false, length = 50)
    private String updatedBy = "SYSTEM";

    @Version
    @Column(name = "VERSION", nullable = false)
    private Long version = 0L;

    // Constructors
    public Vendor() {}

    public Vendor(String vnId) {
        this.vnId = vnId;
    }

    // Convenience methods
    public boolean isActive() {
        return "A".equals(vnStat);
    }

    public boolean isOnHold() {
        return "H".equals(vnStat);
    }

    public boolean isPreferred() {
        return Constants.YesNo.YES.equals(preferredVendor);
    }

    public boolean isEdiCapable() {
        return Constants.YesNo.YES.equals(ediCapable);
    }

    public boolean isDropShipVendor() {
        return Constants.YesNo.YES.equals(dropShipVendor);
    }

    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (!address1.isEmpty()) {
            sb.append(address1);
        }
        if (!address2.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(address2);
        }
        if (!city.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(city);
        }
        if (!state.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(state);
        }
        if (!zipCode.isEmpty()) {
            if (sb.length() > 0) sb.append(" ");
            sb.append(zipCode);
        }
        if (!country.isEmpty()) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(country);
        }
        return sb.toString();
    }
}
