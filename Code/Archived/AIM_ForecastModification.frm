VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Begin VB.Form AIM_ForecastModification 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Forecast Modification"
   ClientHeight    =   9495
   ClientLeft      =   975
   ClientTop       =   375
   ClientWidth     =   15165
   FillColor       =   &H00FFFFFF&
   BeginProperty Font 
      Name            =   "Microsoft Sans Serif"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   ForeColor       =   &H8000000F&
   Icon            =   "AIM_ForecastModification.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9495
   ScaleWidth      =   15165
   WindowState     =   2  'Maximized
   Begin VB.Frame fraMetrics 
      Height          =   1970
      Left            =   60
      TabIndex        =   0
      Top             =   2790
      Width           =   15015
      Begin VB.Line Line1 
         X1              =   4080
         X2              =   220
         Y1              =   1880
         Y2              =   1885
      End
      Begin VB.Line Line2 
         X1              =   220
         X2              =   220
         Y1              =   965
         Y2              =   1890
      End
      Begin VB.Label Label69 
         ForeColor       =   &H00FF0000&
         Height          =   285
         Left            =   1920
         TabIndex        =   44
         Top             =   1590
         Width           =   1095
      End
      Begin VB.Label Label67 
         ForeColor       =   &H00FF0000&
         Height          =   285
         Left            =   1920
         TabIndex        =   43
         Top             =   1020
         Width           =   1095
      End
      Begin VB.Label Label3 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   3030
         TabIndex        =   42
         Top             =   1010
         Width           =   1335
      End
      Begin VB.Label Label12 
         Caption         =   "Budget"
         Height          =   280
         Left            =   240
         TabIndex        =   41
         Top             =   1590
         Width           =   1575
      End
      Begin VB.Label Label11 
         Caption         =   "Sales YTD"
         Height          =   280
         Left            =   240
         TabIndex        =   40
         Top             =   1305
         Width           =   1575
      End
      Begin VB.Label Label7 
         Caption         =   "Sales Last Year"
         Height          =   280
         Left            =   240
         TabIndex        =   39
         Top             =   1020
         Width           =   1575
      End
      Begin VB.Label Label1 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "Base Metrics"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   220
         TabIndex        =   38
         Top             =   720
         Width           =   4145
      End
      Begin VB.Label Label4 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   3030
         TabIndex        =   37
         Top             =   1295
         Width           =   1335
      End
      Begin VB.Label Label6 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   320
         Left            =   3030
         TabIndex        =   36
         Top             =   1580
         Width           =   1335
      End
      Begin VB.Label Label68 
         ForeColor       =   &H00FF0000&
         Height          =   285
         Left            =   1920
         TabIndex        =   35
         Top             =   1305
         Width           =   1095
      End
      Begin VB.Label Label70 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   6915
         TabIndex        =   34
         Top             =   1290
         Width           =   1455
      End
      Begin VB.Label Label71 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   6915
         TabIndex        =   33
         Top             =   1005
         Width           =   1455
      End
      Begin VB.Label Label72 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "Trend"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   6915
         TabIndex        =   32
         Top             =   435
         Width           =   1455
      End
      Begin VB.Label Label76 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   6915
         TabIndex        =   31
         Top             =   720
         Width           =   1455
      End
      Begin VB.Label Label77 
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "System Forecast"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   4875
         TabIndex        =   30
         Top             =   720
         Width           =   2055
      End
      Begin VB.Label Label78 
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "Draft"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   4875
         TabIndex        =   29
         Top             =   1005
         Width           =   2055
      End
      Begin VB.Label Label79 
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "Forecast"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   4875
         TabIndex        =   28
         Top             =   1290
         Width           =   2055
      End
      Begin VB.Label Label80 
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "Budget"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   4875
         TabIndex        =   27
         Top             =   1575
         Width           =   2055
      End
      Begin VB.Label Label81 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   8355
         TabIndex        =   26
         Top             =   1290
         Width           =   1455
      End
      Begin VB.Label Label82 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   8355
         TabIndex        =   25
         Top             =   1005
         Width           =   1455
      End
      Begin VB.Label Label83 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "%"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   8355
         TabIndex        =   24
         Top             =   435
         Width           =   1455
      End
      Begin VB.Label Label84 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   8355
         TabIndex        =   23
         Top             =   720
         Width           =   1455
      End
      Begin VB.Label Label85 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   9795
         TabIndex        =   22
         Top             =   1290
         Width           =   1575
      End
      Begin VB.Label Label86 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   9795
         TabIndex        =   21
         Top             =   1005
         Width           =   1575
      End
      Begin VB.Label Label87 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "%"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   9795
         TabIndex        =   20
         Top             =   435
         Width           =   1575
      End
      Begin VB.Label Label88 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   9795
         TabIndex        =   19
         Top             =   720
         Width           =   1575
      End
      Begin VB.Label Label89 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   11880
         TabIndex        =   18
         Top             =   1295
         Width           =   1455
      End
      Begin VB.Label Label90 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   11880
         TabIndex        =   17
         Top             =   1010
         Width           =   1455
      End
      Begin VB.Label Label91 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "Next Year"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   11880
         TabIndex        =   16
         Top             =   440
         Width           =   1455
      End
      Begin VB.Label Label92 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   11880
         TabIndex        =   15
         Top             =   725
         Width           =   1455
      End
      Begin VB.Label Label93 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   13325
         TabIndex        =   14
         Top             =   1295
         Width           =   1455
      End
      Begin VB.Label Label94 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   13325
         TabIndex        =   13
         Top             =   1010
         Width           =   1455
      End
      Begin VB.Label Label95 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         Caption         =   "%"
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   13325
         TabIndex        =   12
         Top             =   440
         Width           =   1455
      End
      Begin VB.Label Label96 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   13325
         TabIndex        =   11
         Top             =   725
         Width           =   1455
      End
      Begin VB.Label Label97 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   11880
         TabIndex        =   10
         Top             =   1580
         Width           =   1455
      End
      Begin VB.Label Label98 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   13325
         TabIndex        =   9
         Top             =   1580
         Width           =   1455
      End
      Begin VB.Label Label74 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   9795
         TabIndex        =   8
         Top             =   1575
         Width           =   1575
      End
      Begin VB.Label Label75 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   8355
         TabIndex        =   7
         Top             =   1575
         Width           =   1455
      End
      Begin VB.Label Label99 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BackColor       =   &H00C0FFFF&
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   6915
         TabIndex        =   6
         Top             =   1575
         Width           =   1455
      End
      Begin VB.Label Label73 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   13325
         TabIndex        =   5
         Top             =   150
         Width           =   1455
      End
      Begin VB.Label Label100 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   11880
         TabIndex        =   4
         Top             =   150
         Width           =   1455
      End
      Begin VB.Label Label101 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   9795
         TabIndex        =   3
         Top             =   150
         Width           =   1575
      End
      Begin VB.Label Label102 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   8355
         TabIndex        =   2
         Top             =   150
         Width           =   1455
      End
      Begin VB.Label Label103 
         Alignment       =   2  'Center
         Appearance      =   0  'Flat
         BorderStyle     =   1  'Fixed Single
         ForeColor       =   &H80000008&
         Height          =   300
         Left            =   6915
         TabIndex        =   1
         Top             =   150
         Width           =   1455
      End
   End
   Begin ActiveToolBars.SSActiveToolBars atbFcstModification 
      Left            =   120
      Top             =   9120
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   11
      Style           =   0
      DisplayContextMenu=   0   'False
      Tools           =   "AIM_ForecastModification.frx":030A
      ToolBars        =   "AIM_ForecastModification.frx":8EC1
   End
   Begin ActiveTabs.SSActiveTabs atModification 
      Height          =   4455
      Left            =   120
      TabIndex        =   57
      Top             =   4920
      Width           =   14955
      _ExtentX        =   26379
      _ExtentY        =   7858
      _Version        =   131083
      TabCount        =   3
      TabOrientation  =   6
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TabMinWidth     =   390
      TabMinHeight    =   0
      Tabs            =   "AIM_ForecastModification.frx":914F
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   4395
         Left            =   30
         TabIndex        =   62
         Top             =   30
         Width           =   14505
         _ExtentX        =   25585
         _ExtentY        =   7752
         _Version        =   131083
         TabGuid         =   "AIM_ForecastModification.frx":9212
         Begin VB.TextBox txtFcstComments 
            Height          =   375
            Left            =   3000
            TabIndex        =   63
            Top             =   3960
            Width           =   10215
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSummary 
            Height          =   3810
            Left            =   120
            TabIndex        =   64
            Top             =   105
            Width           =   14415
            _Version        =   196617
            DataMode        =   1
            AllowUpdate     =   0   'False
            AllowColumnMoving=   2
            AllowColumnSwapping=   0
            SelectTypeCol   =   0
            SelectTypeRow   =   0
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            CaptionAlignment=   0
            SplitterVisible =   -1  'True
            Columns(0).Width=   3200
            Columns(0).DataType=   8
            Columns(0).FieldLen=   4096
            _ExtentX        =   25435
            _ExtentY        =   6720
            _StockProps     =   79
            ForeColor       =   -2147483630
            BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
         End
         Begin VB.Label Label43 
            Caption         =   "Comments"
            Height          =   300
            Left            =   600
            TabIndex        =   65
            Top             =   4035
            Width           =   2265
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel3 
         Height          =   4395
         Left            =   30
         TabIndex        =   58
         Top             =   30
         Width           =   14505
         _ExtentX        =   25585
         _ExtentY        =   7752
         _Version        =   131083
         TabGuid         =   "AIM_ForecastModification.frx":923A
         Begin C1Chart2D8.Chart2D chtForecast 
            Height          =   4270
            Left            =   60
            TabIndex        =   59
            Top             =   60
            Width           =   14420
            _Version        =   524288
            _Revision       =   7
            _ExtentX        =   25435
            _ExtentY        =   7532
            _StockProps     =   0
            ControlProperties=   "AIM_ForecastModification.frx":9262
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   4395
         Left            =   30
         TabIndex        =   60
         Top             =   30
         Width           =   14505
         _ExtentX        =   25585
         _ExtentY        =   7752
         _Version        =   131083
         TabGuid         =   "AIM_ForecastModification.frx":9837
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgItems 
            Height          =   4275
            Left            =   75
            TabIndex        =   61
            Top             =   45
            Width           =   14415
            _Version        =   196617
            DataMode        =   1
            AllowColumnMoving=   2
            AllowColumnSwapping=   0
            SelectTypeCol   =   0
            SelectTypeRow   =   0
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            SplitterVisible =   -1  'True
            Columns(0).Width=   3200
            Columns(0).DataType=   8
            Columns(0).FieldLen=   4096
            _ExtentX        =   25435
            _ExtentY        =   7549
            _StockProps     =   79
            ForeColor       =   -2147483630
            BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
         End
      End
   End
   Begin VB.Frame fraModOptions 
      Height          =   2775
      Left            =   68
      TabIndex        =   45
      Top             =   0
      Width           =   6350
      Begin VB.CheckBox ckGetFromBatch 
         Caption         =   "Use Batch"
         Height          =   315
         Left            =   2040
         TabIndex        =   116
         Top             =   2250
         Width           =   2145
      End
      Begin VB.ListBox lsElement 
         Height          =   1815
         ItemData        =   "AIM_ForecastModification.frx":985F
         Left            =   4320
         List            =   "AIM_ForecastModification.frx":9861
         MultiSelect     =   1  'Simple
         TabIndex        =   47
         Top             =   570
         Width           =   1935
      End
      Begin VB.CheckBox ckNetRqmts 
         Caption         =   "&Net Requirements"
         Height          =   375
         Left            =   165
         TabIndex        =   46
         Top             =   2220
         Width           =   1695
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFcstId 
         Bindings        =   "AIM_ForecastModification.frx":9863
         Height          =   345
         Left            =   2610
         TabIndex        =   48
         Top             =   225
         Width           =   1590
         DataFieldList   =   "fcstid"
         AllowNull       =   0   'False
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2805
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "fcstid"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFcstType 
         Bindings        =   "AIM_ForecastModification.frx":987A
         DataField       =   "CodeId"
         Height          =   345
         Left            =   2625
         TabIndex        =   49
         Top             =   585
         Width           =   1590
         DataFieldList   =   "CodeDesc"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2805
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMLocations 
         Bindings        =   "AIM_ForecastModification.frx":9893
         Height          =   345
         Left            =   2625
         TabIndex        =   50
         Top             =   945
         Width           =   1590
         DataFieldList   =   "Lcid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2805
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Lcid"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMItem 
         Bindings        =   "AIM_ForecastModification.frx":98A8
         Height          =   345
         Left            =   2625
         TabIndex        =   51
         Top             =   1305
         Width           =   1590
         DataFieldList   =   "Item"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2805
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Item"
      End
      Begin TDBText6Ctl.TDBText txtItemDescription 
         Height          =   300
         Left            =   195
         TabIndex        =   112
         TabStop         =   0   'False
         Top             =   1700
         Width           =   4035
         _Version        =   65536
         _ExtentX        =   7108
         _ExtentY        =   529
         Caption         =   "AIM_ForecastModification.frx":98BD
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastModification.frx":9929
         Key             =   "AIM_ForecastModification.frx":9947
         BackColor       =   -2147483644
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   40
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label2 
         Caption         =   "Element"
         Height          =   300
         Left            =   4320
         TabIndex        =   56
         Top             =   240
         Width           =   1935
      End
      Begin VB.Label Label5 
         Caption         =   "Location ID"
         Height          =   300
         Left            =   195
         TabIndex        =   55
         Top             =   990
         Width           =   2325
      End
      Begin VB.Label Label8 
         Caption         =   "Forecast ID"
         Height          =   300
         Left            =   195
         TabIndex        =   54
         Top             =   270
         Width           =   2325
      End
      Begin VB.Label Label9 
         Caption         =   "Item ID"
         Height          =   300
         Left            =   195
         TabIndex        =   53
         Top             =   1350
         Width           =   2325
      End
      Begin VB.Label Label10 
         Caption         =   "Forecast Type"
         Height          =   300
         Left            =   195
         TabIndex        =   52
         Top             =   630
         Width           =   2325
      End
   End
   Begin ActiveTabs.SSActiveTabs atForecastOptions 
      Height          =   2775
      Left            =   6500
      TabIndex        =   66
      Top             =   0
      Width           =   8580
      _ExtentX        =   15134
      _ExtentY        =   4895
      _Version        =   131083
      TabCount        =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TabMinWidth     =   0
      Tabs            =   "AIM_ForecastModification.frx":998B
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel9 
         Height          =   2385
         Left            =   30
         TabIndex        =   67
         Top             =   360
         Width           =   8520
         _ExtentX        =   15028
         _ExtentY        =   4207
         _Version        =   131083
         TabGuid         =   "AIM_ForecastModification.frx":9A23
         Begin VB.Frame fraFilterCrit 
            Height          =   2295
            Left            =   60
            TabIndex        =   68
            Top             =   0
            Width           =   8430
            Begin TDBText6Ctl.TDBText txtLocation 
               Height          =   345
               Left            =   2180
               TabIndex        =   69
               TabStop         =   0   'False
               Top             =   195
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9A4B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9AB7
               Key             =   "AIM_ForecastModification.frx":9AD5
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass1 
               Height          =   345
               Left            =   2180
               TabIndex        =   70
               TabStop         =   0   'False
               Top             =   1275
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9B19
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9B85
               Key             =   "AIM_ForecastModification.frx":9BA3
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItemID 
               Height          =   345
               Left            =   2180
               TabIndex        =   71
               TabStop         =   0   'False
               Top             =   555
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9BE7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9C53
               Key             =   "AIM_ForecastModification.frx":9C71
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass2 
               Height          =   345
               Left            =   6540
               TabIndex        =   72
               TabStop         =   0   'False
               Top             =   1275
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9CB5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9D21
               Key             =   "AIM_ForecastModification.frx":9D3F
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVendorID 
               Height          =   345
               Left            =   2180
               TabIndex        =   73
               TabStop         =   0   'False
               Top             =   915
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9D83
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9DEF
               Key             =   "AIM_ForecastModification.frx":9E0D
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass3 
               Height          =   345
               Left            =   2180
               TabIndex        =   74
               TabStop         =   0   'False
               Top             =   1635
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9E51
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9EBD
               Key             =   "AIM_ForecastModification.frx":9EDB
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtBuyerID 
               Height          =   345
               Left            =   6540
               TabIndex        =   75
               TabStop         =   0   'False
               Top             =   195
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9F1F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":9F8B
               Key             =   "AIM_ForecastModification.frx":9FA9
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass4 
               Height          =   345
               Left            =   6540
               TabIndex        =   76
               TabStop         =   0   'False
               Top             =   1635
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":9FED
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":A059
               Key             =   "AIM_ForecastModification.frx":A077
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItemStatus 
               Height          =   345
               Left            =   6540
               TabIndex        =   77
               TabStop         =   0   'False
               Top             =   555
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":A0BB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":A127
               Key             =   "AIM_ForecastModification.frx":A145
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAssort 
               Height          =   345
               Left            =   6540
               TabIndex        =   78
               TabStop         =   0   'False
               Top             =   915
               Width           =   1785
               _Version        =   65536
               _ExtentX        =   3149
               _ExtentY        =   600
               Caption         =   "AIM_ForecastModification.frx":A189
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":A1F5
               Key             =   "AIM_ForecastModification.frx":A213
               BackColor       =   -2147483644
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   40
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label46 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Classification 2"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4485
               TabIndex        =   88
               Top             =   1305
               Width           =   1965
            End
            Begin VB.Label Label47 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Classification 3"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   87
               Top             =   1665
               Width           =   1965
            End
            Begin VB.Label Label48 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Classification 4"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4485
               TabIndex        =   86
               Top             =   1665
               Width           =   1965
            End
            Begin VB.Label Label50 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Assortment"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4485
               TabIndex        =   85
               Top             =   960
               Width           =   1965
            End
            Begin VB.Label Label45 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Classification 1"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   84
               Top             =   1305
               Width           =   1965
            End
            Begin VB.Label Label53 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Location ID"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   83
               Top             =   240
               Width           =   1965
            End
            Begin VB.Label Label44 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Item ID"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   82
               Top             =   600
               Width           =   1965
            End
            Begin VB.Label Label49 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Status"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4485
               TabIndex        =   81
               Top             =   600
               Width           =   1965
            End
            Begin VB.Label Label51 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Buyer ID"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4485
               TabIndex        =   80
               Top             =   240
               Width           =   1965
            End
            Begin VB.Label Label52 
               Appearance      =   0  'Flat
               BackColor       =   &H80000005&
               BackStyle       =   0  'Transparent
               Caption         =   "Vendor ID"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   79
               Top             =   960
               Width           =   1965
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel4 
         Height          =   2385
         Left            =   30
         TabIndex        =   89
         Top             =   360
         Width           =   8520
         _ExtentX        =   15028
         _ExtentY        =   4207
         _Version        =   131083
         TabGuid         =   "AIM_ForecastModification.frx":A257
         Begin VB.Frame fraUnits 
            Caption         =   "Forecast Unit"
            Height          =   2290
            Left            =   60
            TabIndex        =   106
            Top             =   0
            Width           =   1485
            Begin VB.OptionButton optForecastUnit 
               Caption         =   "Cube"
               Height          =   300
               Index           =   4
               Left            =   120
               TabIndex        =   111
               Top             =   940
               Width           =   1215
            End
            Begin VB.OptionButton optForecastUnit 
               Caption         =   "Units"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   110
               Top             =   230
               Value           =   -1  'True
               Width           =   1215
            End
            Begin VB.OptionButton optForecastUnit 
               Caption         =   "Cost"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   109
               Top             =   1295
               Width           =   1215
            End
            Begin VB.OptionButton optForecastUnit 
               Caption         =   "Price"
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   108
               Top             =   1650
               Width           =   1215
            End
            Begin VB.OptionButton optForecastUnit 
               Caption         =   "Weight"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   107
               Top             =   585
               Width           =   1215
            End
         End
         Begin VB.Frame fraPeriods 
            Height          =   2290
            Left            =   4440
            TabIndex        =   99
            Top             =   0
            Width           =   4020
            Begin VB.CommandButton cmCancelPcnt 
               Caption         =   "&Cancel"
               Height          =   300
               Left            =   2520
               Style           =   1  'Graphical
               TabIndex        =   113
               Top             =   1920
               Visible         =   0   'False
               Width           =   1410
            End
            Begin TDBNumber6Ctl.TDBNumber txtFcstPds 
               Height          =   345
               Left            =   2520
               TabIndex        =   100
               Top             =   585
               Width           =   1410
               _Version        =   65536
               _ExtentX        =   2487
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastModification.frx":A27F
               Caption         =   "AIM_ForecastModification.frx":A29F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":A30B
               Keys            =   "AIM_ForecastModification.frx":A329
               Spin            =   "AIM_ForecastModification.frx":A373
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   104
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2012741633
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjustPct 
               Height          =   345
               Left            =   2520
               TabIndex        =   101
               Top             =   1095
               Width           =   1410
               _Version        =   65536
               _ExtentX        =   2487
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastModification.frx":A39B
               Caption         =   "AIM_ForecastModification.frx":A3BB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":A427
               Keys            =   "AIM_ForecastModification.frx":A445
               Spin            =   "AIM_ForecastModification.frx":A48F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011693061
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBDate6Ctl.TDBDate txtFcstStartDate 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "MM/dd/yyyy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   2520
               TabIndex        =   102
               Top             =   225
               Width           =   1410
               _Version        =   65536
               _ExtentX        =   2487
               _ExtentY        =   609
               Calendar        =   "AIM_ForecastModification.frx":A4B7
               Caption         =   "AIM_ForecastModification.frx":A5CF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastModification.frx":A63B
               Keys            =   "AIM_ForecastModification.frx":A659
               Spin            =   "AIM_ForecastModification.frx":A6B7
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "__/__/____"
               ValidateMode    =   0
               ValueVT         =   1
               Value           =   36443
               CenturyMode     =   0
            End
            Begin VB.CommandButton cmApplyPcnt 
               Caption         =   "&Apply"
               Height          =   300
               Left            =   2535
               Style           =   1  'Graphical
               TabIndex        =   115
               Top             =   1530
               Width           =   1410
            End
            Begin VB.CommandButton cmConfirmPcnt 
               Caption         =   "Con&firm"
               Height          =   300
               Left            =   2520
               Style           =   1  'Graphical
               TabIndex        =   114
               Top             =   1532
               Visible         =   0   'False
               Width           =   1410
            End
            Begin VB.Label Label20 
               Caption         =   "Start Date"
               Height          =   300
               Left            =   120
               TabIndex        =   105
               Top             =   270
               Width           =   2355
            End
            Begin VB.Label Label21 
               Caption         =   "Forecast Periods"
               Height          =   300
               Left            =   120
               TabIndex        =   104
               Top             =   630
               Width           =   2355
            End
            Begin VB.Label Label22 
               Caption         =   "Forecast Adjustment %"
               Height          =   300
               Left            =   120
               TabIndex        =   103
               Top             =   1140
               Width           =   2355
            End
         End
         Begin VB.Frame fraInterval 
            Caption         =   "Forecast Interval"
            Height          =   2290
            Left            =   1590
            TabIndex        =   90
            Top             =   0
            Width           =   2805
            Begin VB.OptionButton optInterval 
               Caption         =   "4-Week"
               Height          =   300
               Index           =   7
               Left            =   1560
               TabIndex        =   98
               Top             =   1316
               Width           =   1095
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "4-4-5"
               Height          =   300
               Index           =   6
               Left            =   1560
               TabIndex        =   97
               Top             =   954
               Width           =   1095
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "4-5-4"
               Height          =   300
               Index           =   5
               Left            =   1560
               TabIndex        =   96
               Top             =   592
               Width           =   1095
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "5-4-4"
               Height          =   300
               Index           =   4
               Left            =   1560
               TabIndex        =   95
               Top             =   230
               Width           =   1095
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Quarters"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   94
               Top             =   1316
               Width           =   1335
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Months"
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   93
               Top             =   954
               Value           =   -1  'True
               Width           =   1335
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Weeks"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   92
               Top             =   592
               Width           =   1335
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Days"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   91
               Top             =   230
               Width           =   1335
            End
         End
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ForecastModification"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'Option Explicit
'
'Public Fcsttype As String
'Public CurrentPd As Integer
'Public FcstDesc As String
'
'Dim Cn As ADODB.Connection
'Dim AIM_Forecast_GetEq_Sp As ADODB.Command
'Dim AIM_ForecastRepository_Save_Sp As ADODB.Command
'Dim AIM_ForecastRepositoryDetail_Save_Sp As ADODB.Command
'Dim AIM_ForecastRepositoryDetail_Update_Sp As ADODB.Command
'Dim AIM_ForecastRepository_Update_Sp As ADODB.Command
'Dim AIM_BatchFcst_Save_Sp As ADODB.Command
'Dim AIM_ForecastFreezePds_Get_Sp As ADODB.Command
'Dim AIM_Item_GetEq_Sp As ADODB.Command
'Dim AIM_ForecastUserAccess_Validate_Sp As ADODB.Command
'Dim AIM_ForecastRepository_List_Sp As ADODB.Command
'Dim AIM_ForecastFreezeItem_GetEq_Sp As ADODB.Command
'Dim AIM_Locations_GetEq_Sp As New ADODB.Command
'Dim rsForecast As ADODB.Recordset
'Dim rsFcstRepos As ADODB.Recordset
'Dim rsFreezeItem As ADODB.Recordset
'Dim rsFcstId As ADODB.Recordset
'Dim rsLcID As ADODB.Recordset
'Dim xForeCast As New XArrayDB
'Dim xForecastNr As New XArrayDB
'Dim xAdjForeCast As New XArrayDB
'Dim xAdjForecastNr As New XArrayDB
'Dim xPlannedrct As New XArrayDB
'Dim xMainArray As New XArrayDB
'Dim xSumArray As New XArrayDB
'Dim xFcstRepos As New XArrayDB
'Dim xFcstNetRepos As New XArrayDB
'Dim xDraftRepos As New XArrayDB
'Dim xDraftNetRepos As New XArrayDB
'Dim xBudgetRepos As New XArrayDB
'Dim xBudgetNetRepos As New XArrayDB
'Dim xSales As New XArrayDB
'Dim xInventory As New XArrayDB
'Dim xRowChange As New XArrayDB
'Dim xNetReq As New XArrayDB
'Dim xStatArray As New XArrayDB
'Dim xFilteredItems As New XArrayDB
'Dim xSalesLY As New XArrayDB
'
'Dim ElementsSelected As Integer
'Dim SalesPosition As Long
'Dim BudgetPosition As Long
'Dim FcstPosition As Long
'Dim DraftPosition As Long
'Dim AdjSysNetReqPosition As Long
'Dim AdjSysFcstPosition As Long
'Dim SysNetReqPosition As Long
'Dim SysFcstPosition As Long
'Dim InventoryPosition As Long
'Dim xLocItem As New XArrayDB
'
''Dim clsAdvForecastMod As New AIMAdvForecastMod 'A.Stocksdale 2004/1/23 - moved functions from .cls to .bas
'
'Dim ForecastUnit As String
'Dim FcstId As String
'Dim iColUnit As Integer
'
'Dim FreezedPds As Integer
'Dim FreezeItem As String
'
'Dim CurrentPdDate As Date
'Dim FcstStartDate As Date
'Dim FcstPds As Integer
'Dim FcstInterval As Integer
'Dim FcstUnit As String
'
'Dim OldFcstInterval As Integer
'Dim OrgFcstInterval As Integer
'
'Dim xPrevForeCast As New XArrayDB
'Dim xPrevForecastNr As New XArrayDB
'Dim xPrevSales As New XArrayDB
'Dim xPrevFcstRepos As New XArrayDB
'Dim xPrevFcstNetRepos As New XArrayDB
'Dim xPrevBudgetRepos As New XArrayDB
'Dim xPrevBudgetNetRepos As New XArrayDB
'Dim xPrevSumArray As New XArrayDB
'Dim xPrevDraftRepos As New XArrayDB
'
''Filter Variables
'Dim m_FcstItemFilter As FCST_ITEM_FILTER
'
'Dim AIMYears() As AIMYEARS_RCD
'Dim AIMDays() As AIMDAYS_RCD
'
'Dim ItStatBookMark As Variant
'Dim GetAdjSysFcstAndNetReq As Boolean
'Dim SortSeq As String
'Dim Whichgrid As String
'Dim AccessType As Integer
'Dim FcstExist As Boolean
'Dim GetSalesLY As Boolean
'Dim NoChange As Boolean
'Dim m_FirstTime As Boolean
''Sri batch fcst begin
'Dim XBatchArray As New XArrayDB
'Dim GetBatchdata As Boolean
'Dim BatchSysFcst As Boolean
'Dim BatchSysNet As Boolean
'Dim BatchAdjSysFcst As Boolean
'Dim BatchAdjSysNet As Boolean
'Dim FcstReposDetailStatus As Boolean
''Sri batch fcst end
'Dim MyArray As New XArrayDB ' for testing
''Sri add code for prodconst start
'Dim xProdConst As New XArrayDB
'Dim ProdConstPosition As Long
''Sri add code for prodconst end
'Dim RunAdjSysNetReq As Boolean
'Dim strMessage As String
'
'
'Private Function GetActShippedOrOrdered(LcID As String, Item As String, Period As Integer, Actual As String) As Double
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'
'
'    RowEndIndex = xSales.UpperBound(1)
'    ColEndIndex = xSales.UpperBound(2)
'
'    For irow = 0 To RowEndIndex
'        If xSales.Value(irow, 0) = LcID And xSales.Value(irow, 1) = Item And UCase(xSales.Value(irow, ColEndIndex - 1)) = UCase(Actual) Then
'            GetActShippedOrOrdered = xSales.Value(irow, 12 + Period)
'            Exit For
'        End If
'    Next irow
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetSysAndReposData)"
'End Function
'
'Private Function SizeForm()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    If m_FirstTime Then
'        Me.Top = 0
'        Me.Left = 0
'        Me.Width = AIM_Main.ScaleWidth
'        Me.Height = AIM_Main.ScaleHeight
'    End If
'
'    If Me.WindowState <> vbMinimized And AIM_Main.WindowState <> vbMinimized Then
'        'dgItems.Height = Me.Height - 5940
'        'dgItems.Height = Me.Height - 6000
'
'        'Size the Forecast Mod Tab
'        'Me.atModification.Top = fraMetrics.Height + 100
'        Me.atModification.Width = Me.ScaleWidth - 50
'        If Me.ScaleHeight - (fraMetrics.Height + fraModOptions.Height + 300) > 0 Then
'            Me.atModification.Height = Me.ScaleHeight - (fraMetrics.Height + fraModOptions.Height + 300)
'        End If
'
'        'Size the Summary grid
'        Me.dgSummary.Top = 60
'        Me.dgSummary.Left = 60
'        If (Me.atModification.ClientWidth - 140) > 0 Then
'            Me.dgSummary.Width = Me.atModification.ClientWidth - 140
'        End If
'        If (Me.atModification.ClientHeight - 600) > 0 Then
'            Me.dgSummary.Height = Me.atModification.ClientHeight - 600
'        End If
'
'        Label43.Top = dgSummary.Height + 90
'        txtFcstComments.Top = dgSummary.Height + 80
'
'        'Size the Items grid
'        Me.dgItems.Top = 60
'        Me.dgItems.Left = 60
'        If (Me.atModification.ClientWidth - 140) > 0 Then
'            Me.dgItems.Width = Me.atModification.ClientWidth - 140
'        End If
'        If (Me.atModification.ClientHeight - 140) > 0 Then
'            Me.dgItems.Height = Me.atModification.ClientHeight - 140
'        End If
'
'        'Size the Graph
'        Me.chtForecast.Left = 60
'        Me.chtForecast.Top = 60
'        If (Me.atModification.ClientWidth - 140) > 0 Then
'            Me.chtForecast.Width = Me.atModification.ClientWidth - 140
'        End If
'        If (Me.atModification.ClientHeight - 140) > 0 Then
'            Me.chtForecast.Height = Me.atModification.ClientHeight - 140
'        End If
'
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SizeForm)"
'End Function
'
'Private Sub GetSysAndReposData()
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim iFRow As Long
'    Dim iCol As Integer
'    Dim Qty As Long
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'
''
'    Dim xTempArray As New XArrayDB
'    Dim RtnCode As Integer
'    Dim iFcol As Integer
'
'
'    '
'    'sri batch fcst begin
'
'    'Set xForeCast = Nothing
'    'orginal code  2 lines commented
''    GetForecastMaint dcFcstId.Text, xForeCast, False, FcstStartdate, FcstInterval, False, "FCST", False
'     If GetBatchdata = True Then
'        Call GetFromBatch
'    'sri
'    Else
'    'Set xForeCast = Nothing
'    GetForecastMaint dcFcstId.Text, xForeCast, False, FcstStartDate, FcstInterval, False, "FCST", False
'    'Sri add code for prodconst start
''    If ProdConstPosition <> -1 Then
''    PopulatePreProdConstData
''        GetProdConstraint xProdConst, FcstPds
''    End If
'   'Sri add code for prodconst end
'
'    End If
'
'   'sri batch fcst end
'    If xForeCast.Count(1) = 0 Then
'        Exit Sub
'    End If
'
'    xForeCast.QuickSort 0, xForeCast.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'
'
'    If SysNetReqPosition <> -1 Then
'
'        'Sri batch fcst begin
'        'Original code 3 lines commented
''        Set xForecastNr = Nothing
''        GetForecastMaint dcFcstId.Text, xForecastNr, True, FcstStartdate, FcstInterval, False, "FCST", False
'
'        If GetBatchdata = False Then
'            Set xForecastNr = Nothing
'            GetForecastMaint dcFcstId.Text, xForecastNr, True, FcstStartDate, FcstInterval, False, "FCST", False
'        End If
'        'Sri batch fcdt end
'
'        If xForecastNr.Count(1) = 0 Then
'            Exit Sub
'        End If
'
'        xForecastNr.QuickSort 0, xForecastNr.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'    End If
'
'
'    Set xSales = Nothing
'    GetSalesAndOrders dcFcstId.Text, xSales, FcstStartDate, FcstInterval
'
'    xSales.QuickSort 0, xSales.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'
'
'    Set xInventory = Nothing
'    GetInitialStock dcFcstId.Text, xInventory
'
'    xInventory.QuickSort 0, xInventory.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING, xInventory.UpperBound(2), XORDER_ASCEND, XTYPE_STRING
'
'
'    Set xFcstRepos = Nothing
'    Set xFcstNetRepos = Nothing
'    Set xBudgetRepos = Nothing
'    Set xBudgetNetRepos = Nothing
'    Set xDraftRepos = Nothing
'    Set xDraftNetRepos = Nothing
'
'    RowEndIndex = xForeCast.UpperBound(1)
'    ColEndIndex = xForeCast.UpperBound(2)
'
'    xFcstRepos.ReDim 0, RowEndIndex, 0, ColEndIndex
'    xFcstNetRepos.ReDim 0, RowEndIndex, 0, ColEndIndex
'    xBudgetRepos.ReDim 0, RowEndIndex, 0, ColEndIndex
'    xBudgetNetRepos.ReDim 0, RowEndIndex, 0, ColEndIndex
'    xDraftRepos.ReDim 0, RowEndIndex, 0, ColEndIndex
'    xDraftNetRepos.ReDim 0, RowEndIndex, 0, ColEndIndex
'
'    For irow = 0 To RowEndIndex
'        For iCol = 0 To 11
'            xFcstRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xFcstNetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xBudgetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xBudgetNetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xDraftRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xDraftNetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'        Next iCol
'        For iCol = ColEndIndex To ColEndIndex - 7 Step -1
'            xFcstRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xFcstNetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xBudgetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xBudgetNetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xDraftRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'            xDraftNetRepos.Value(irow, iCol) = xForeCast.Value(irow, iCol)
'        Next iCol
'    Next irow
'
'
'    FcstExist = False
'    txtFcstComments.Text = ""
'
'    RtnCode = GetBudgetOrForecast(FcstId, "FCST", FcstStartDate, FcstInterval, xTempArray)
'    If RtnCode <> -1 Then
'        If Fcsttype = "FCST" Then
'            FcstExist = True
'            Call PopulateComment
'        End If
'        Call PopulateRepository(xTempArray, xFcstRepos, xFcstNetRepos)
'    End If
'    Set xTempArray = Nothing
'
'    RtnCode = GetBudgetOrForecast(FcstId, "BDGT", FcstStartDate, FcstInterval, xTempArray)
'    If RtnCode <> -1 Then
'        If Fcsttype = "BDGT" Then
'            FcstExist = True
'            Call PopulateComment
'        End If
'        Call PopulateRepository(xTempArray, xBudgetRepos, xBudgetNetRepos)
'    End If
'   Set xTempArray = Nothing
'
'    RtnCode = GetBudgetOrForecast(FcstId, "DRFT", FcstStartDate, FcstInterval, xTempArray)
'    If RtnCode <> -1 Then
'        If Fcsttype = "DRFT" Then
'            FcstExist = True
'            Call PopulateComment
'        End If
'        Call PopulateRepository(xTempArray, xDraftRepos, xDraftNetRepos)
'    End If
'    Set xTempArray = Nothing
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        Err.Raise Err.Number, Err.Source, Err.Description & "(GetSysAndReposData)"
'    End If
'End Sub
'
'Private Function FoundInfilteredItems(pLcID As String, pitem As String, pclass1 As String, pclass2 As String, pclass3 As String, _
'                                      pclass4 As String, pstatus As String, pVnID As String, pAssort As String, pByID As String) As Boolean
'On Error GoTo ErrorHandler
'
'    Dim LcIDOk As Boolean, ItemOk As Boolean
'    Dim Class1Ok As Boolean, Class2Ok As Boolean, Class3Ok As Boolean, Class4Ok As Boolean
'    Dim StatusOk As Boolean, VnIDOk As Boolean, AssortOk As Boolean, ByIDOk As Boolean
'
'    LcIDOk = True
'    ItemOk = True
'    Class1Ok = True
'    Class2Ok = True
'    Class3Ok = True
'    Class4Ok = True
'    StatusOk = True
'    VnIDOk = True
'    AssortOk = True
'    ByIDOk = True
'
'    FoundInfilteredItems = False
'    With m_FcstItemFilter
'        If Trim(dcAIMLocations.Text = "All") And Trim(dcAIMItem.Text = "All") And _
'            (Trim(.SubFilterLcID) = "" Or IsNull(.SubFilterLcID)) And (Trim(.SubFilterItem) = "" Or IsNull(.SubFilterItem) Or .SubFilterItem = rsForecast("Item").Value) And _
'            (Trim(.SubFilterClass1) = "" Or IsNull(.SubFilterClass1) Or .SubFilterClass1 = rsForecast("Class1").Value) And (Trim(.SubFilterClass2) = "" Or IsNull(.SubFilterClass2) Or .SubFilterClass2 = rsForecast("Class2").Value) And _
'            (Trim(.SubFilterClass3) = "" Or IsNull(.SubFilterClass3) Or .SubFilterClass3 = rsForecast("Class3").Value) And (Trim(.SubFilterClass4) = "" Or IsNull(.SubFilterClass4) Or .SubFilterClass4 = rsForecast("Class4").Value) And _
'            (Trim(.SubFilterItemStatus) = "" Or IsNull(.SubFilterItemStatus) Or .SubFilterItemStatus = rsForecast("ItStat").Value) And (Trim(.SubFilterVnId) = "" Or IsNull(.SubFilterVnId) Or .SubFilterVnId = rsForecast("VnID").Value) And _
'            (Trim(.SubFilterAssort) = "" Or IsNull(.SubFilterAssort) Or .SubFilterAssort = rsForecast("Assort").Value) And (Trim(.SubFilterById) = "" Or IsNull(.SubFilterById) Or .SubFilterById = rsForecast("ByID").Value) _
'            Then
'                FoundInfilteredItems = True
'                Exit Function
'        End If
'    End With
'
'    'If Trim(dcAIMLocations.Text <> "") And dcAIMLocations.Text = pLcID Then
'    '        FoundInfilteredItems = True
'    'End If
'
'    If Trim(dcAIMLocations.Text <> "All") Then
'        If dcAIMLocations.Text <> pLcID Then LcIDOk = False
'    End If
'
'    If Trim(dcAIMItem.Text <> "All") Then
'        If dcAIMItem.Text = pitem And LcIDOk Then
'            FoundInfilteredItems = True
'        Else
'            FoundInfilteredItems = False
'        End If
'        Exit Function
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterLcID) <> "" And Not IsNull(m_FcstItemFilter.SubFilterLcID) And Trim(dcAIMLocations.Text) = "All" Then
'        If UCase(m_FcstItemFilter.SubFilterLcID) <> UCase(pLcID) Then LcIDOk = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterItem) <> "" And Not IsNull(m_FcstItemFilter.SubFilterItem) And Trim(dcAIMItem.Text) = "All" Then
'        If UCase(m_FcstItemFilter.SubFilterItem) <> UCase(pitem) Then ItemOk = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterClass1) <> "" And Not IsNull(m_FcstItemFilter.SubFilterClass1) Then
'        If UCase(m_FcstItemFilter.SubFilterClass1) <> UCase(pclass1) Then Class1Ok = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterClass2) <> "" And Not IsNull(m_FcstItemFilter.SubFilterClass2) Then
'        If UCase(m_FcstItemFilter.SubFilterClass2) <> UCase(pclass2) Then Class1Ok = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterClass3) <> "" And Not IsNull(m_FcstItemFilter.SubFilterClass3) Then
'        If UCase(m_FcstItemFilter.SubFilterClass3) <> UCase(pclass3) Then Class1Ok = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterClass4) <> "" And Not IsNull(m_FcstItemFilter.SubFilterClass4) Then
'        If UCase(m_FcstItemFilter.SubFilterClass4) <> UCase(pclass4) Then Class1Ok = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterItemStatus) <> "" And Not IsNull(m_FcstItemFilter.SubFilterItemStatus) Then
'        If UCase(m_FcstItemFilter.SubFilterItemStatus) <> UCase(pstatus) Then StatusOk = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterVnId) <> "" And Not IsNull(m_FcstItemFilter.SubFilterVnId) Then
'        If UCase(m_FcstItemFilter.SubFilterVnId) <> UCase(pVnID) Then VnIDOk = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterAssort) <> "" And Not IsNull(m_FcstItemFilter.SubFilterAssort) Then
'        If UCase(m_FcstItemFilter.SubFilterAssort) <> UCase(pAssort) Then AssortOk = False
'    End If
'
'    If Trim(m_FcstItemFilter.SubFilterById) <> "" And Not IsNull(m_FcstItemFilter.SubFilterById) Then
'        If UCase(m_FcstItemFilter.SubFilterById) <> UCase(pByID) Then ByIDOk = False
'    End If
'
'
'    If LcIDOk And ItemOk And Class1Ok And Class2Ok And Class3Ok And Class4Ok And StatusOk And VnIDOk And AssortOk And ByIDOk Then
'        FoundInfilteredItems = True
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(FoundInFilteredItems)"
'End Function
'
'Private Sub ChangeInterval()
'On Error GoTo ErrorHandler
'
'    Dim OrgInterval As AIM_INTERVALS
'    Dim OldInterval As AIM_INTERVALS
'    Dim CurStartdate As Date
'    Dim Enddate As Date
'    Dim Counter As Long
'    Dim NewPds As Integer
'    Dim FcstTotalDays As Integer
'    Dim Reminder As Integer
'   'Dim i As Integer
'
'
'    If OldFcstInterval = OrgFcstInterval Then
'        NewPds = FcstPds
'    Else
'        CurStartdate = FcstStartDate
'        OrgInterval = OrgFcstInterval
'        OldInterval = OldFcstInterval
'        For Counter = 1 To FcstPds
'            'CurStartdate = AdvForecast.GetNextStartDate(FcstStartDate, CurStartdate, OrgInterval)
'            CurStartdate = GetNextStartDate(FcstStartDate, CurStartdate, OrgInterval)
'        Next Counter
'        Enddate = CurStartdate
'        CurStartdate = FcstStartDate
'        NewPds = 0
'        For Counter = 1 To FcstPds
'            'CurStartdate = AdvForecast.GetNextStartDate(FcstStartDate, CurStartdate, OldInterval)
'            CurStartdate = GetNextStartDate(FcstStartDate, CurStartdate, OldInterval)
'            If CurStartdate > Enddate Then Exit For
'            NewPds = NewPds + 1
'        Next
'    End If
'
'    txtFcstPds.Text = NewPds
'
'    Set xMainArray = Nothing
'    Set xSumArray = Nothing
'
'    FcstInterval = OldFcstInterval
'
'    SetColumns NewPds, _
'                FcstStartDate, _
'                OldFcstInterval, _
'                0
'
'    GetSysAndReposData
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(ChangeInterval)", Err.Description
'End Sub
'
'Private Sub GetBatchStatus()
'On Error GoTo ErrorHandler
''sri batch fcst begin new function
'
'    Dim strSQL As String
'    Dim rsBatchStatus As Recordset
'    Dim IndexCounter As Integer
'
'    If dcFcstId.Text = "" Or dcFcstType.Text = "" Then
'        BatchSysFcst = False
'        BatchSysNet = False
'        BatchAdjSysFcst = False
'        BatchAdjSysNet = False
'        Exit Sub
'        Return
'    End If
'
'    strSQL = "SELECT  top 1 SysFcst,SysNet,ModSysFcst,ModSysNet " & _
'    " From ForecastRepositoryBatch, ForecastRepository " & _
'    "WHERE ForecastRepository.FcstId = N'" & Me.dcFcstId.Text & "' and " & _
'    "ForecastRepository.FcstType = N'" & Me.dcFcstType.Columns(0).Value & "' and " & _
'    "ForecastRepository.RepositoryKey = ForecastRepositoryBatch.RepositoryKey"
'
'    Set rsBatchStatus = New ADODB.Recordset
'    rsBatchStatus.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'
'    If f_IsRecordsetOpenAndPopulated(rsBatchStatus) Then
'        If Not IsNull(rsBatchStatus("SysFcst").Value) Then
'            BatchSysFcst = True
'        Else
'            BatchSysFcst = False
'        End If
'        If Not IsNull(rsBatchStatus("SysNet").Value) Then
'            BatchSysNet = True
'        Else
'            BatchSysNet = False
'        End If
'        If Not IsNull(rsBatchStatus("ModSysFcst").Value) Then
'            BatchAdjSysFcst = True
'        Else
'            BatchAdjSysFcst = False
'        End If
'        If Not IsNull(rsBatchStatus("ModSysNet").Value) Then
'            BatchAdjSysNet = True
'        Else
'            BatchAdjSysNet = False
'        End If
'    Else
'        BatchSysFcst = False
'        BatchSysNet = False
'        BatchAdjSysFcst = False
'        BatchAdjSysNet = False
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsBatchStatus) Then rsBatchStatus.Close
'    Set rsBatchStatus = Nothing
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(GetBatchStatus)", Err.Description
''sri batch fcst end
'End Sub
'
'Private Sub GetForecastRepositoryDetailStatus()
''Sri code change to save only modified  or new rows  end
'Dim strSQL As String
'Dim rsFcstReposDetailStatus As Recordset
'Dim IndexCounter As Integer
'
'On Error GoTo ErrorHandler
'
'If dcFcstId.Text = "" Or dcFcstType.Text = "" Then
'    FcstReposDetailStatus = False
'    Exit Sub
'    Return
'End If
'strSQL = "SELECT  top 1 ForecastRepositoryDetail.RepositoryKey " & _
'" From ForecastRepositoryDetail, ForecastRepository " & _
'"WHERE ForecastRepository.FcstId ='" & dcFcstId.Text & "'and " & _
'"ForecastRepository.FcstType ='" & dcFcstType.Columns(0).Value & "'and " & _
'"ForecastRepository.RepositoryKey = ForecastRepositoryDetail.RepositoryKey"
'
'Set rsFcstReposDetailStatus = New ADODB.Recordset
'rsFcstReposDetailStatus.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'
'If f_IsRecordsetOpenAndPopulated(rsFcstReposDetailStatus) Then
'     If rsFcstReposDetailStatus("RepositoryKey").Value >= 0 Then
'         FcstReposDetailStatus = True
'     Else
'         FcstReposDetailStatus = False
'     End If
'
'Else
'    FcstReposDetailStatus = False
'
'End If
'
'If f_IsRecordsetValidAndOpen(rsFcstReposDetailStatus) Then rsFcstReposDetailStatus.Close
'Set rsFcstReposDetailStatus = Nothing
'
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(GetForecastRepositoryDetailStatus)", Err.Description
''Sri code change to save only modified  or new rows  end
'End Sub
'
'
'
'
'Private Sub ParseElementsList()
'On Error GoTo ErrorHandler
'
'    ElementsSelected = 0
'    If lsElement.Selected(0) = True Then
'        SalesPosition = 0
'        ElementsSelected = ElementsSelected + 1
'    Else
'        SalesPosition = -1
'    End If
'    If lsElement.Selected(1) = True Then
'        BudgetPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    Else
'        BudgetPosition = -1
'    End If
'    If lsElement.Selected(2) = True Then
'        FcstPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    Else
'        FcstPosition = -1
'    End If
'    If lsElement.Selected(3) = True Then
'        DraftPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    Else
'        DraftPosition = -1
'    End If
'
'    If OrgFcstInterval <> OldFcstInterval Then
'        AdjSysNetReqPosition = -1
'    Else
'        AdjSysNetReqPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    End If
'
'    If OrgFcstInterval <> OldFcstInterval Then
'        AdjSysFcstPosition = -1
'    Else
'        AdjSysFcstPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    End If
'
'    'AdjSysFcstPosition = ElementsSelected
'    'ElementsSelected = ElementsSelected + 1
'    'ElementsSelected = ElementsSelected + 2
'
'    If lsElement.Selected(4) = True Then
'        SysNetReqPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    Else
'        SysNetReqPosition = -1
'    End If
'    If lsElement.Selected(5) = True Then
'        SysFcstPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    Else
'        SysFcstPosition = -1
'    End If
'    If lsElement.Selected(6) = True Then
'        InventoryPosition = ElementsSelected
'        ElementsSelected = ElementsSelected + 1
'    Else
'        InventoryPosition = -1
'    End If
'    'Sri add code for prodconst start
'    'Uncomment this if you start using prodconstraint
''    If lsElement.Selected(7) = True Then
''        ProdConstPosition = ElementsSelected
''        ElementsSelected = ElementsSelected + 1
''    Else
''        ProdConstPosition = -1
''    End If
'    'Sri add code for prodconst end
'
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ParseElementList)"
'End Sub
'
'Private Sub ApplyFilter()
'On Error GoTo ErrorHandler
'
'    Dim NewFcstUnit As String
'    Dim NewStartDate As Date
'    Dim NewFcstPds As Integer
'    Dim NewFcstInterval As Integer
'    Dim IndexCounter As Long
'    Dim iCol As Integer
'    Dim strMessage As String
'    Dim RtnCode As Long
'
'    If xRowChange.UpperBound(1) > 0 Then
'        strMessage = getTranslationResource("MSGBOX06900")
'        If StrComp(strMessage, "MSGBOX06900") = 0 Then strMessage = "Abandon changes to current record?"
'        RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
'        If RtnCode = vbNo Then
'            Exit Sub
'        End If
'    End If
'
'    'Change the Forecast Unit
'    For IndexCounter = 0 To 7
'        If optInterval(IndexCounter).Value = True Then
'            NewFcstInterval = IndexCounter
'        End If
'    Next IndexCounter
'
'    If NewFcstInterval <> OldFcstInterval Then
'        OldFcstInterval = NewFcstInterval
'        ChangeInterval
'        GetAdjSysFcstAndNetReq = True
'    Else
'        GetAdjSysFcstAndNetReq = False
'    End If
'
'    If Me.optForecastUnit(0).Value = True Then
'            NewFcstUnit = "Units"
'            iColUnit = 0
'    ElseIf Me.optForecastUnit(1).Value = True Then
'            NewFcstUnit = "Cost"
'            iColUnit = 2
'    ElseIf Me.optForecastUnit(2).Value = True Then
'            NewFcstUnit = "Price"
'            iColUnit = 5
'    ElseIf Me.optForecastUnit(3).Value = True Then
'            NewFcstUnit = "Weight"
'            iColUnit = 6
'    ElseIf Me.optForecastUnit(4).Value = True Then
'            NewFcstUnit = "Cube"
'            iColUnit = 7
'    End If
'    If ForecastUnit <> NewFcstUnit Then
'        ForecastUnit = NewFcstUnit
'    End If
'
'    NewStartDate = Format(txtFcstStartDate.Value, "yyyy/mm/dd")
'    NewFcstPds = txtFcstPds.Value
'    'Sri code change to save only modified and new rows begin
'   ' For iCol = 14 To dgItems.Cols - 3
'    For iCol = 14 To dgItems.Cols - 5
'    'Sri code change to save only modified and new rows end
'        If Format(CDate(dgItems.Columns(iCol).Caption), "yyyy/mm/dd") < NewStartDate Then
'            dgItems.Columns(iCol).Visible = False
'            dgSummary.Columns(iCol - 12).Visible = False
'            NewFcstPds = NewFcstPds + 1
'        Else
'            dgItems.Columns(iCol).Visible = True
'            dgSummary.Columns(iCol - 12).Visible = True
'        End If
'
'        If iCol - 14 >= NewFcstPds Or Format(CDate(dgItems.Columns(iCol).Caption), "yyyy/mm/dd") < NewStartDate Then
'            dgItems.Columns(iCol).Visible = False
'            dgSummary.Columns(iCol - 12).Visible = False
'        Else
'            dgItems.Columns(iCol).Visible = True
'            dgSummary.Columns(iCol - 12).Visible = True
'        End If
'    Next iCol
'
'    ParseElementsList
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "parseElement  end " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "FillGrid  Start " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    FillGrid
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "FillGrid end " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(Apply Filter)"
'End Sub
'Private Function PopulatePreProdConstData() As Integer
''Sri add code for prodconst new function
'On Error GoTo ErrorHandler
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim RowEndIndex As Integer
'    Dim ColEndIndex As Integer
'    PopulatePreProdConstData = -1
'    If xAdjForecastNr.Count(1) = 0 Then
'        Exit Function
'    End If
'    'Initilze the arrays
'   Set xProdConst = Nothing
'    RowEndIndex = xAdjForecastNr.UpperBound(1)
'    ColEndIndex = xAdjForecastNr.UpperBound(2)
'    xProdConst.ReDim 0, RowEndIndex, 0, ColEndIndex
'    For irow = 0 To RowEndIndex
'        For iCol = 0 To ColEndIndex
'            xProdConst.Value(irow, iCol) = xAdjForecastNr.Value(irow, iCol)
'        Next iCol
'    Next irow
'    PopulatePreProdConstData = 1
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(PopulatePreProdConstData)"
''Sri batch fcst end
'End Function
'Private Function GetFromBatch() As Integer
''Sri batch fcst begin
'On Error GoTo ErrorHandler
'    Dim irow As Long
'    Dim iFRow As Long
'    Dim iCol As Integer
'    Dim Qty As Long
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'    Dim RtnCode As Integer
'    Dim iFcol As Integer
'    Dim ForecastTotal As Double
'    Dim ForecastNrTotal As Double
'    Dim AdjForecastTotal As Double
'    Dim AdjForecastNrTotal As Double
'    Dim PlannedRctTotal As Double
'
'
''   'GetSysFcst dcFcstId.Text, "DRFT", FcstStartdate, FcstInterval, xForeCast
'   'Initilze the arrays
'   Set XBatchArray = Nothing
'   Set xForeCast = Nothing
'   Set xForecastNr = Nothing
'   Set xAdjForeCast = Nothing
'   Set xAdjForecastNr = Nothing
'   Set xPlannedrct = Nothing
'
'
'   'call founction to get data from batchfcst table and populate xbatcharray
'    RtnCode = GetBatchFcst(FcstId, Fcsttype, FcstStartDate, FcstInterval, XBatchArray)
'    RowEndIndex = XBatchArray.UpperBound(1)
'    ColEndIndex = XBatchArray.UpperBound(2)
'    xForeCast.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'    xForecastNr.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'    xAdjForeCast.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'    xAdjForecastNr.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'    xPlannedrct.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'     For irow = 0 To RowEndIndex
'        For iCol = 0 To 12
'            xForeCast.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'            xForecastNr.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'            xAdjForeCast.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'            xAdjForecastNr.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'            xPlannedrct.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'        Next iCol
'        iFcol = (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'        For iCol = ColEndIndex To ColEndIndex - 8 Step -1
'
'            xForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'            xForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'            xAdjForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'            xAdjForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'            xPlannedrct.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'            iFcol = iFcol - 1
'        Next iCol
'    Next irow
'    ColEndIndex = XBatchArray.UpperBound(2) - 8
'
'    For irow = 0 To RowEndIndex
'        ForecastTotal = 0
'        ForecastNrTotal = 0
'        AdjForecastTotal = 0
'        AdjForecastNrTotal = 0
'        PlannedRctTotal = 0
'
'        iFcol = 13
'        For iCol = 13 To ColEndIndex Step 5
'            xForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'            ForecastTotal = ForecastTotal + xForeCast.Value(irow, iFcol)
'            xForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 1)
'            ForecastNrTotal = ForecastNrTotal + xForecastNr.Value(irow, iFcol)
'            xAdjForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 2)
'            AdjForecastTotal = AdjForecastTotal + xAdjForeCast.Value(irow, iFcol)
'            xAdjForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 3)
'            AdjForecastNrTotal = AdjForecastNrTotal + xAdjForecastNr.Value(irow, iFcol)
'            xPlannedrct.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 4)
'            PlannedRctTotal = PlannedRctTotal + xPlannedrct.Value(irow, iFcol)
'            iFcol = iFcol + 1
'        Next iCol
'            xForeCast.Value(irow, 12) = ForecastTotal
'            xForecastNr.Value(irow, 12) = ForecastNrTotal
'             'Sri code change to save only modified and new rows begin
''            xAdjForeCast.Value(irow, 12) = AdjForecastTotal
'            xAdjForeCast.Value(irow, 12) = XBatchArray.Value(irow, 12)
'             'Sri code change to save only modified and new rows
'            xAdjForecastNr.Value(irow, 12) = AdjForecastNrTotal
'            xPlannedrct.Value(irow, 12) = PlannedRctTotal
'
'    Next irow
'
'
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(GetFromBatch)"
''Sri batch fcst end
'End Function
'
'Private Sub CalculateStock()
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim WorkingDays As Integer
'    Dim PdWorkingDays As Integer
'    Dim NextPdStartDate As Date
'    Dim Interval As AIM_INTERVALS
'    Dim CurPdStartDate As Date
'    Dim initstock As Double
'    Dim ActDate As Date
'    Dim skipped As Integer
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'    Dim StkDate As Date
''    If xInventory.Count(1) = 0 Then
'    Set xInventory = Nothing
'    GetInitialStock dcFcstId.Text, xInventory
'    'End If
'      'CurrentPdDate = FcstStartdate
'
'    xInventory.QuickSort 0, xInventory.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'
'    'Make both xInventory and xforecast same size
'    xInventory.ReDim 0, xInventory.UpperBound(1), 0, xForeCast.UpperBound(2)
'
'
'    RowEndIndex = xInventory.UpperBound(1)
'    ColEndIndex = xInventory.UpperBound(2)
'
'    AIMCalendar_Load Cn, AIMYears(), AIMDays()
'    Interval = OldFcstInterval
'    'NextPdStartDate = AdvForecast.GetNextStartDate(FcstStartDate, CurrentPdDate, Interval)
'    NextPdStartDate = GetNextStartDate(FcstStartDate, CurrentPdDate, Interval)
'
'     AIM_Locations_GetEq_Sp.Parameters("@LcIDkey").Value = xInventory.Value(irow, 0)
'
'    If rsLcID.State <> adStateOpen Then
'        rsLcID.Open AIM_Locations_GetEq_Sp
'    Else
'        rsLcID.Requery
'    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsLcID) Then
'        'StkDate = Format(Format(CDate("01/" + Format(rsLcID("StkDate").Value, "mm/yyyy")), "dd/mm/yyyy"), "yyyy/mm/dd")
'        StkDate = Format(CDate(Format(rsLcID("StkDate").Value, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'    End If
'
'
'    If CurrentPd <> 0 Then
'        'WorkingDays = GetWorkingDays(Date, NextPdStartDate, AIMDays(), AIMYears())
'        WorkingDays = GetWorkingDays(StkDate, NextPdStartDate, AIMDays(), AIMYears())
'        PdWorkingDays = GetWorkingDays(CurrentPdDate, NextPdStartDate, AIMDays(), AIMYears())
'    End If
'
'    For irow = 0 To RowEndIndex
'
'        xInventory.Value(irow, ColEndIndex) = xForeCast.Value(irow, ColEndIndex)
'        xInventory.Value(irow, ColEndIndex - 1) = xInventory.Value(irow, 16)
'        xInventory.Value(irow, ColEndIndex - 2) = xForeCast.Value(irow, ColEndIndex - 2)
'        xInventory.Value(irow, ColEndIndex - 3) = xForeCast.Value(irow, ColEndIndex - 3)
'        xInventory.Value(irow, ColEndIndex - 4) = xInventory.Value(irow, 15)
'        xInventory.Value(irow, ColEndIndex - 5) = xForeCast.Value(irow, ColEndIndex - 5)
'        xInventory.Value(irow, ColEndIndex - 6) = xInventory.Value(irow, 14)
'        xInventory.Value(irow, ColEndIndex - 7) = xInventory.Value(irow, 13)
'
'    Next irow
'
'    For irow = 0 To RowEndIndex
'        initstock = xInventory.Value(irow, 12)
'        For iCol = 13 To 13 + CurrentPd - 2
'            xInventory.Value(irow, iCol) = 0
'        Next iCol
'        If CurrentPd = 0 Then
'            For iCol = 13 To ColEndIndex - 8
'                'xInventory.Value(irow, iCol) = (xInventory.Value(irow, iCol - 1) - xAdjForeCast.Value(irow, iCol)) + xAdjForecastNr.Value(irow, iCol)
'                xInventory.Value(irow, iCol) = (xInventory.Value(irow, iCol - 1) - xAdjForeCast.Value(irow, iCol)) + xPlannedrct.Value(irow, iCol)
'            Next iCol
'        Else
'            AIM_Item_GetEq_Sp.Parameters("@LcIDkey").Value = xInventory.Value(irow, 0)
'            AIM_Item_GetEq_Sp.Parameters("@ItemKey").Value = xInventory.Value(irow, 1)
'
'
'             If rsFreezeItem.State <> adStateOpen Then
'                    rsFreezeItem.Open AIM_Item_GetEq_Sp
'             Else
'                    rsFreezeItem.Requery
'            End If
'            ActDate = Format("01/01/2002", "yyyy/mm/dd")
'            If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
'                ActDate = rsFreezeItem("ActDate").Value
'            End If
'
'
'
''            If f_IsRecordsetValidAndOpen(rsFreezeItem) Then rsFreezeItem.Close
''            Set rsFreezeItem = Nothing
''            Set rsFreezeItem = New ADODB.Recordset
''            rsFreezeItem.Open AIM_Item_GetEq_Sp
''
''            ActDate = "01/01/2002"
''            If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
''                ActDate = rsFreezeItem("ActDate").Value
''            End If
'            skipped = 0
'
'            For iCol = 13 + CurrentPd - 1 To ColEndIndex - 8
'                If iCol = 13 + CurrentPd - 1 Then
'                    xInventory.Value(irow, iCol) = initstock + WorkingDays / PdWorkingDays * (xPlannedrct.Value(irow, iCol) - xAdjForeCast.Value(irow, iCol))
'                Else
'                    xInventory.Value(irow, iCol) = (xInventory.Value(irow, iCol - 1) - xAdjForeCast.Value(irow, iCol)) + xPlannedrct.Value(irow, iCol)
'                End If
'            Next iCol
'        End If
'    Next irow
'
'    For irow = 0 To RowEndIndex
'        xInventory.Value(irow, 12) = 0
'        For iCol = 13 To ColEndIndex - 8
'            If IsNumeric(xInventory.Value(irow, iCol)) Then
'                xInventory.Value(irow, 12) = xInventory.Value(irow, 12) + xInventory.Value(irow, iCol)
'            End If
'        Next iCol
'    Next irow
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        Err.Raise Err.Number, Err.Source, Err.Description & "(CalculateStock)"
'    End If
'End Sub
'
'
'Private Sub SetStatistic(ByVal xSumFcst As XArrayDB, ByVal Pds As Integer, ByVal StartDate As Date, ByVal Interval As AIM_INTERVALS)
'On Error GoTo ErrorHandler
'
'    Dim TodayDate As Date
'
'    Dim SumArrayCol As Integer
'    Dim lBudget As Long
'    Dim lForecast As Long
'    Dim lSales As Long
'    Dim lRemainFcst As Long
'    Dim lRemainSysFcst As Long
'    Dim lSalesLY As Long
'    Dim lSysFcst As Long
'    Dim lSysNetRq As Long
'    Dim lCurBudget As Long
'    Dim lSalesFcst As Long
'    Dim lSalesSysFcst As Long
'    Dim lPrevRemainFcst As Long
'    Dim lPrevRemainSysFcst As Long
'    Dim lPrevSalesFcst As Long
'    Dim lPrevSalesSysFcst As Long
'    Dim lDraft As Long
'
'    Dim nForecast As Long
'    Dim nSysfcst As Long
'    Dim nDraft As Double
'    Dim nBudget As Double
'    Dim DateCnt As Integer
'    Dim CurStartdate As Date
'    Dim FirstDate As Date
'    Dim NextDate As Date
'    Dim RemainPdCnt As Integer
'    Dim StartPdCnt As Integer
'    Dim EndPdCnt As Integer
'    Dim InputDate As Date
'    Dim PdPass As Integer
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim tmpCnt As Long
'    Dim Qty As Long
'
'    Dim SalesIndex As Long
'    Dim BudgetIndex As Long
'    Dim ForecastIndex As Long
'    Dim SysNetReqIndex As Long
'    Dim SysFcstIndex As Long
'    Dim DraftIndex As Long
'
'    Dim PrevFcstID As String
'    Dim PrevPds As Integer
'    Dim PrevStartDate As Date
'    Dim PrevInterval As Integer
'    Dim PInterval As AIM_INTERVALS
'
'    Dim RtnCode As Integer
'    'Dim xSalesLY As New XArrayDB
'
'    Dim iColYear As Integer
'    Dim ThisFy As Integer
'    Dim PrdFy As Integer
'
'    Dim RowStartIndex As Long
'    Dim RowEndIndex As Long
'    Dim ColStartIndex As Long
'    Dim ColEndIndex As Long
'
'
'
'    TodayDate = Format(Now(), "yyyy/mm/dd")
'
'    'Get all indexes
'    ForecastIndex = -1
'    ForecastIndex = xSumFcst.Find(0, 0, "Forecast", , , XTYPE_STRINGCASESENSITIVE)
'    SysFcstIndex = -1
'    SysFcstIndex = xSumFcst.Find(0, 0, "Sys Fcst", , , XTYPE_STRINGCASESENSITIVE)
'    BudgetIndex = -1
'    BudgetIndex = xSumFcst.Find(0, 0, "Budget", , , XTYPE_STRINGCASESENSITIVE)
'    SysNetReqIndex = -1
'    SysNetReqIndex = xSumFcst.Find(0, 0, "Sys Net Req", , , XTYPE_STRINGCASESENSITIVE)
'    SalesIndex = -1
'    SalesIndex = xSumFcst.Find(0, 0, "Sales", , , XTYPE_STRINGCASESENSITIVE)
'    DraftIndex = -1
'    DraftIndex = xSumFcst.Find(0, 0, "Draft", , , XTYPE_STRINGCASESENSITIVE)
'
'    If GetSalesLY Then
'        'Set Sales LY
''       'GetLastFySales FcstId, xSalesLY
'
'        GetSalesAndOrdersLastYear FcstId, xSalesLY, FcstStartDate, FcstInterval
'        'Me.dgForecast.ReBind
'        GetSalesLY = False
'    End If
'    RowStartIndex = xSalesLY.LowerBound(1)
'    RowEndIndex = xSalesLY.UpperBound(1)
'    ColEndIndex = xSalesLY.UpperBound(2)
'    For irow = RowStartIndex To RowEndIndex
'        If (UCase(xSalesLY.Value(irow, ColEndIndex)) = UCase("Actual Shipped") And ckNetRqmts.Value = 1) _
'                Or (UCase(xSalesLY.Value(irow, ColEndIndex)) = UCase("Actual Ordered") And ckNetRqmts.Value = 0 And xSalesLY.Value(irow, ColEndIndex - 1) = "O") _
'                Or (UCase(xSalesLY.Value(irow, ColEndIndex)) = UCase("Actual Shipped") And ckNetRqmts.Value = 0 And xSalesLY.Value(irow, ColEndIndex - 1) = "S") Then
'            If FoundInfilteredItems(xSalesLY.Value(irow, 0), xSalesLY.Value(irow, 1), xSalesLY.Value(irow, 3) _
'                    , xSalesLY.Value(irow, 4), xSalesLY.Value(irow, 5), xSalesLY.Value(irow, 6) _
'                    , xSalesLY.Value(irow, 7), xSalesLY.Value(irow, 9), xSalesLY.Value(irow, 10), xSalesLY.Value(irow, 11)) Then
'                lSalesLY = lSalesLY + xSalesLY.Value(irow, 12) * IIf(iColUnit = 0, 1, xSalesLY.Value(irow, (ColEndIndex - iColUnit) - 2))
'            End If
'        End If
'    Next irow
'
'    Label3.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lSalesLY, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lSalesLY, 0, vbTrue, vbTrue, vbTrue))
'
'    ThisFy = GetFiscalYear(Now, AIMYears())
'    Label67.Caption = ThisFy - 1
'    If CurrentPd <> 0 Then
'        Label68.Caption = Format(CurrentPdDate, "mmm-yy")
'    Else
'        Label68.Caption = ""
'    End If
'    Label69.Caption = ThisFy
'    Label103.Caption = ThisFy
'    Label102.Caption = "vs " + CStr(ThisFy - 1)
'    Label101.Caption = "vs Bdgt " + CStr(ThisFy)
'    Label100.Caption = CStr(ThisFy + 1)
'    Label73.Caption = "vs " + CStr(ThisFy)
'
'    ColStartIndex = xSumFcst.LowerBound(2)
'    ColEndIndex = xSumFcst.UpperBound(2)
'    lCurBudget = 0
'    For SumArrayCol = ColStartIndex + 1 To ColEndIndex - 3
'        If Format(CDate(dgSummary.Columns(SumArrayCol + 1).Caption), "YYYY") = CStr(ThisFy) Then
'            lCurBudget = lCurBudget + xSumFcst(BudgetIndex, SumArrayCol)
'        End If
'    Next SumArrayCol
'
'    Label6.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lCurBudget, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lCurBudget, 0, vbTrue, vbTrue, vbTrue))
'
'    lSales = 0
'    For SumArrayCol = ColStartIndex + 1 To CurrentPd
'        If IsDate(dgSummary.Columns(SumArrayCol + 1).Caption) Then
'            If Format(CDate(dgSummary.Columns(SumArrayCol + 1).Caption), "YYYY") = CStr(ThisFy) Then
'                lSales = lSales + xSumFcst(SalesIndex, SumArrayCol)
'            End If
'        End If
'    Next SumArrayCol
'
'    Label4.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lSales, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lSales, 0, vbTrue, vbTrue, vbTrue))
'
'    lSales = 0
'    For SumArrayCol = ColStartIndex + 1 To CurrentPd - 1
'        If Format(CDate(dgSummary.Columns(SumArrayCol + 1).Caption), "YYYY") = CStr(ThisFy) Then
'            lSales = lSales + xSumFcst(SalesIndex, SumArrayCol)
'        End If
'    Next SumArrayCol
'
'    lSysFcst = 0
'    lForecast = 0
'    lDraft = 0
'    lBudget = 0
'    For SumArrayCol = IIf(CurrentPd = 0, 1, CurrentPd) To ColEndIndex - 3
'        If Format(CDate(dgSummary.Columns(SumArrayCol + 1).Caption), "YYYY") = CStr(ThisFy) Then
'            lSysFcst = lSysFcst + xSumFcst(SysFcstIndex, SumArrayCol)
'            lForecast = lForecast + xSumFcst(ForecastIndex, SumArrayCol)
'            lDraft = lDraft + xSumFcst(DraftIndex, SumArrayCol)
'            lBudget = lBudget + xSumFcst(BudgetIndex, SumArrayCol)
'        End If
'    Next SumArrayCol
'
'    Label76.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lSales + lSysFcst, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lSales + lSysFcst, 0, vbTrue, vbTrue, vbTrue))
'    Label71.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lSales + lDraft, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lSales + lDraft, 0, vbTrue, vbTrue, vbTrue))
'    Label70.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lSales + lForecast, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lSales + lForecast, 0, vbTrue, vbTrue, vbTrue))
'    Label99.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(lSales + lBudget, 0, vbTrue, vbTrue, vbTrue), FormatNumber(lSales + lBudget, 0, vbTrue, vbTrue, vbTrue))
'
'    If lSalesLY <> 0 Then
'        Label84.Caption = FormatNumber(((lSysFcst + lSales) - lSalesLY) / lSalesLY * 100, 1) & "%"
'        Label82.Caption = FormatNumber(((lDraft + lSales) - lSalesLY) / lSalesLY * 100, 1) & "%"
'        Label81.Caption = FormatNumber(((lForecast + lSales) - lSalesLY) / lSalesLY * 100, 1) & "%"
'        Label75.Caption = FormatNumber(((lBudget + lSales) - lSalesLY) / lSalesLY * 100, 1) & "%"
'    Else
'        Label84.Caption = ""
'        Label82.Caption = ""
'        Label81.Caption = ""
'        Label75.Caption = ""
'    End If
'
'    If lCurBudget <> 0 Then
'        Label88.Caption = FormatNumber(((lSysFcst + lSales) - lCurBudget) / lCurBudget * 100, 1) & "%"
'        Label86.Caption = FormatNumber(((lDraft + lSales) - lCurBudget) / lCurBudget * 100, 1) & "%"
'        Label85.Caption = FormatNumber(((lForecast + lSales) - lCurBudget) / lCurBudget * 100, 1) & "%"
'        Label74.Caption = FormatNumber(((lBudget + lSales) - lCurBudget) / lCurBudget * 100, 1) & "%"
'    Else
'        Label88.Caption = ""
'        Label86.Caption = ""
'        Label85.Caption = ""
'        Label74.Caption = ""
'    End If
'
'    nSysfcst = 0
'    nForecast = 0
'    nDraft = 0
'    nBudget = 0
'    For SumArrayCol = ColStartIndex + 1 To ColEndIndex - 3
'        If Format(CDate(dgSummary.Columns(SumArrayCol + 1).Caption), "YYYY") = CStr(ThisFy + 1) Then
'            nSysfcst = nSysfcst + xSumFcst(SysFcstIndex, SumArrayCol)
'            nForecast = nForecast + xSumFcst(ForecastIndex, SumArrayCol)
'            nDraft = nDraft + xSumFcst(DraftIndex, SumArrayCol)
'            nBudget = nBudget + xSumFcst(BudgetIndex, SumArrayCol)
'        End If
'    Next SumArrayCol
'
'    Label92.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(nSysfcst, 0, vbTrue, vbTrue, vbTrue), FormatNumber(nSysfcst, 0, vbTrue, vbTrue, vbTrue))
'    Label90.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(nDraft, 0, vbTrue, vbTrue, vbTrue), FormatNumber(nDraft, 0, vbTrue, vbTrue, vbTrue))
'    Label89.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(nForecast, 0, vbTrue, vbTrue, vbTrue), FormatNumber(nForecast, 0, vbTrue, vbTrue, vbTrue))
'    Label97.Caption = IIf(iColUnit = 2 Or iColUnit = 5, "$" + FormatNumber(nBudget, 0, vbTrue, vbTrue, vbTrue), FormatNumber(nBudget, 0, vbTrue, vbTrue, vbTrue))
'
'    If (lSysFcst + lSales) <> 0 Then
'        Label96.Caption = FormatNumber((nSysfcst - (lSysFcst + lSales)) / (lSysFcst + lSales) * 100, 1) & "%"
'    Else
'        Label96.Caption = ""
'    End If
'
'    If (lSales + lDraft) <> 0 Then
'        Label94.Caption = FormatNumber((nDraft - (lDraft + lSales)) / (lDraft + lSales) * 100, 1) & "%"
'    Else
'        Label94.Caption = ""
'    End If
'
'    If (lSales + lForecast) <> 0 Then
'        Label93.Caption = FormatNumber((nForecast - (lForecast + lSales)) / (lForecast + lSales) * 100, 1) & "%"
'    Else
'        Label93.Caption = ""
'    End If
'
'    If (lSales + lCurBudget) <> 0 Then
'        Label98.Caption = FormatNumber((nBudget - (lBudget + lSales)) / (lBudget + lSales) * 100, 1) & "%"
'    Else
'        Label98.Caption = ""
'    End If
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SetStatistic)"
'End Sub
'
'Private Function GetFcstLinkId(ByVal FcstId As String) As String
'On Error GoTo ErrorHandler
'
'    Dim strSQL As String
'    Dim rsFcstLinkId As ADODB.Recordset
'
'    strSQL = "select FcstLinkId from AIMForecast where FcstId ='" & FcstId & "'"
'    Set rsFcstLinkId = New ADODB.Recordset
'    rsFcstLinkId.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'
'
'    If f_IsRecordsetOpenAndPopulated(rsFcstLinkId) Then
'        If IsNull(rsFcstLinkId("FcstLinkId").Value) Then
'            GetFcstLinkId = ""
'        Else
'            GetFcstLinkId = rsFcstLinkId("FcstLinkId").Value
'        End If
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsFcstLinkId) Then rsFcstLinkId.Close
'    Set rsFcstLinkId = Nothing
'
'Exit Function
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsFcstLinkId) Then rsFcstLinkId.Close
'    Set rsFcstLinkId = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetFcstLinkId)"
'End Function
'
'Private Function GetYearIndex(ArgColCaption As String, ArgYearIndex As Integer) As Integer
'On Error GoTo ErrorHandler
'Dim ThisFy As Integer
'Dim PrdFy As Integer
'
''ArgYearIndex = 0
''If ForecastUnit = "Cost" Or ForecastUnit = "Price" Then
'    ThisFy = GetFiscalYear(Now, AIMYears())
'    PrdFy = GetFiscalYear(Format(CDate(ArgColCaption), "yyyy/mm/dd"), AIMYears())
'    If ThisFy = PrdFy Then
'        ArgYearIndex = 1
'    ElseIf ThisFy > PrdFy Then
'        ArgYearIndex = 0
'    ElseIf ThisFy < PrdFy Then
'        ArgYearIndex = 2
'    End If
''End If
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetYearIndex)"
'End Function
'Private Function CalculateAdjNetReq() As Integer
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim iCrow As Long
'    'Dim rsFreezeItem As New ADODB.Recordset
'    Dim FreezeItem As String
'    Dim FreezedPds As Integer
'    Dim Percentage As Double
'    Dim TodayDate As Date
'    Dim Pds As Integer
'    Dim StartDate As Date
'    Dim Interval As AIM_INTERVALS
'    Dim FirstDate As Date
'    Dim CurDate As Date
'    Dim NextDate As Date
'    Dim DateCnt As Integer
'    Dim StartPds As Integer
'    Dim ModNetReq() As Double
'    Dim iSRow As Long
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'    Dim NetRowEndIndex As Long
'    Dim NetColEndIndex As Integer
'    Dim xTempPlannedRct As New XArrayDB
'    Dim AdjNetReqSum As Long
''    Dim StatusPercent As Integer
'
'    Screen.MousePointer = vbHourglass
'
'    ReDim Preserve ModNetReq(1 To FcstPds)
'    RowEndIndex = xMainArray.UpperBound(1)
'    ColEndIndex = xMainArray.UpperBound(2)
'
'    Pds = txtFcstPds.Text
'    StartDate = Format(CDate(txtFcstStartDate.Text), "yyyy/mm/dd")
'    Interval = rsForecast("fcstinterval").Value
'    Percentage = txtAdjustPct.Text
'    TodayDate = Format(Now(), "yyyy/mm/dd")
'
'        For irow = AdjSysFcstPosition To RowEndIndex Step ElementsSelected
'
'            'Check for freeze Location/Item
'            AIM_Item_GetEq_Sp.Parameters("@LcIDkey").Value = xMainArray(irow, 0)
'            AIM_Item_GetEq_Sp.Parameters("@ItemKey").Value = xMainArray(irow, 1)
'            If Not f_IsRecordsetValidAndOpen(rsFreezeItem) Then
'                rsFreezeItem.Open AIM_Item_GetEq_Sp
'            Else
'                rsFreezeItem.Requery
'            End If
''            If f_IsRecordsetValidAndOpen(rsFreezeItem) Then rsFreezeItem.Close
''            Set rsFreezeItem = Nothing
''            Set rsFreezeItem = New ADODB.Recordset
''            rsFreezeItem.Open AIM_Item_GetEq_Sp
'
'            If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
'                FreezeItem = rsFreezeItem("Freeze_Forecast").Value
'            End If
'
'            'current period to freeze, then only allow.
'            If UCase(FreezeItem) = "N" Then
'                AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@FcstId").Value = FcstId
'                AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@LcID").Value = xMainArray(irow, 0)
'                AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Item").Value = xMainArray(irow, 1)
'
'                AIM_ForecastFreezeItem_GetEq_Sp.Execute
'
'                If AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Return_Value").Value = -1 Then
'                    'Check for freeze period
'                    AIM_ForecastFreezePds_Get_Sp.Parameters("@FcstId").Value = FcstId
'                    AIM_ForecastFreezePds_Get_Sp.Parameters("@LcID").Value = xMainArray(irow, 0)
'
'                    AIM_ForecastFreezePds_Get_Sp.Execute
'
'                    If AIM_ForecastFreezePds_Get_Sp.Parameters("@Return_Value").Value = 0 Then
'                        FreezedPds = AIM_ForecastFreezePds_Get_Sp.Parameters("@FreezePds").Value
'                    Else
'                        FreezedPds = 0
'                    End If
'
'                    For iCol = 1 To FcstPds
'                        ModNetReq(iCol) = 0
'                    Next iCol
'                    'Sri code change to save only modified and new rows begin
'                    'Set the modified column to Y
'                    xMainArray.Value(irow, ColEndIndex - 2) = "Y"
'                    'Sri code change to save only modified and new rows end
''********************For iCol = 14 + CurrentPd + FreezedPds To ColEndIndex - 1
'                    For iCol = 14 To ColEndIndex - 1
'                        If Me.dgItems.Columns(iCol).Visible = True Then   'And CDate(Me.dgItems.Columns(iCol).Caption) >= TodayDate Then
'                            'Sri code change fixed bug Modnetreq is the diff between Adj_sys_fcst -Sys_fcst
'                            'ModNetReq(iCol - 13) = xMainArray(irow, iCol) - xMainArray(irow + 1, iCol)
'                            ModNetReq(iCol - 13) = xMainArray(irow, iCol) - xMainArray(irow + 2, iCol)
'                        End If
'                    Next iCol
'
'                    GetNetReqForAPeriod FcstId, "", xMainArray.Value(irow, 0), xMainArray.Value(irow, 1), FcstStartDate, FcstInterval, xNetReq, ModNetReq(), True, xTempPlannedRct
'                    strMessage = getTranslationResource("STATMSG06904")
'                    If StrComp(strMessage, "STATMSG06904") = 0 Then strMessage = "Calculating Adjusted Net Requirements..."
''                    StatusPercent = 100 * (irow / RowEndIndex)
''                    strMessage = CStr(StatusPercent) & getTranslationResource("% complete for Adj. Net Req. ") & strMessage
'                    Write_Message strMessage & " " & Trim(xMainArray(irow, 0)) & getTranslationResource("/") & xMainArray(irow, 1)
'                     NetRowEndIndex = xNetReq.UpperBound(1)
'                     NetColEndIndex = xNetReq.UpperBound(2)
'                     AdjNetReqSum = 0
'                    For iSRow = 0 To NetRowEndIndex
''*********************For iCol = 13 + CurrentPd + FreezedPds To NetColEndIndex - 8
'                        For iCol = 13 To NetColEndIndex - 8
'                            xMainArray.Value(irow - 1, iCol + 1) = xNetReq.Value(iSRow, iCol)
'                            xAdjForecastNr.Value(xMainArray(irow, ColEndIndex - 1), iCol) _
'                            = xNetReq.Value(iSRow, iCol)
'                          xPlannedrct.Value(xMainArray(irow, ColEndIndex - 1), iCol) _
'                          = xTempPlannedRct.Value(iSRow, iCol)
'                          AdjNetReqSum = AdjNetReqSum + xNetReq.Value(iSRow, iCol)
'                          'xAdjForecastNr.Value(xMainArray(xRowChange.Value(irow, 0), ColEndIndex - 1), iCol) _
'                            = xNetReq.Value(iSRow, iCol)
'                        Next iCol
'                            xMainArray.Value(irow - 1, 13) = AdjNetReqSum
'                            AdjNetReqSum = 0
'                    Next iSRow
'                End If
'
'                'If f_IsRecordsetValidAndOpen(rsFreezeItem) Then
'                '    rsFreezeItem.Close
'                '    Set rsFreezeItem = Nothing
'                'End If
'            End If
'        Next irow
'
'       Write_Message ""
'
'        dgItems.ReBind
'
'
'    Screen.MousePointer = vbNormal
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(CalculateAdjNetReq)"
'    End If
'
'End Function
'
'Private Function ValidateSales(ArgRow As Long, ArgElement As String) As Boolean
'On Error GoTo ErrorHandler
'Dim EndCol As Integer
'EndCol = xSales.UpperBound(2)
'If ArgElement = "Sales" Then
'    If (UCase(xSales.Value(ArgRow, EndCol - 1)) = UCase("Actual Shipped") And ckNetRqmts.Value = 1) _
'        Or (UCase(xSales.Value(ArgRow, EndCol - 1)) = UCase("Actual Ordered") And ckNetRqmts.Value = 0 And xSales.Value(ArgRow, EndCol) = "O") _
'        Or (UCase(xSales.Value(ArgRow, EndCol - 1)) = UCase("Actual Shipped") And ckNetRqmts.Value = 0 And xSales.Value(ArgRow, EndCol) = "S") Then
'        ValidateSales = True
'    Else
'       ValidateSales = False
'    End If
'Else
'    ValidateSales = True        'It the argelement is other than Sales then it it always true
'End If
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ValidateSales)"
'End Function
'
'Private Function PopulateComment() As Integer
'On Error GoTo ErrorHandler
'
'With AIM_ForecastRepository_List_Sp
'    .Parameters("@FcstID") = FcstId
'    .Parameters("@FcstType") = Fcsttype
'
'    If Not f_IsRecordsetValidAndOpen(rsFcstRepos) Then
'        rsFcstRepos.Open AIM_ForecastRepository_List_Sp
'    Else
'        rsFcstRepos.Requery
'    End If
'
''    If f_IsRecordsetValidAndOpen(rsFcstRepos) Then rsFcstRepos.Close
''    Set rsFcstRepos = Nothing
''    Set rsFcstRepos = New ADODB.Recordset
''    rsFcstRepos.Open AIM_ForecastRepository_List_Sp
''
'    If f_IsRecordsetOpenAndPopulated(rsFcstRepos) Then
'        txtFcstComments.Text = rsFcstRepos("FcstComment").Value
'    End If
'
''    If f_IsRecordsetValidAndOpen(rsFcstRepos) Then
''        rsFcstRepos.Close
''        Set rsFcstRepos = Nothing
''    End If
'End With
'
'Exit Function
'ErrorHandler:
'   If f_IsRecordsetValidAndOpen(rsFcstRepos) Then
'      rsFcstRepos.Close
'       Set rsFcstRepos = Nothing
'    End If
'    Err.Raise Err.Number, Err.Source, Err.Description & "(PopulateComment)"
'End Function
'
'Private Function SaveToRepository(xRepos As XArrayDB, xNetRepos As XArrayDB) As Integer
'On Error GoTo ErrorHandler
'Dim dayCnt As Integer
'Dim weekCnt As Integer
'Dim monthCnt As Integer
'Dim quarterCnt As Integer
'Dim yearCnt As Integer
'Dim dCnt As Integer
'Dim wCnt As Integer
'Dim qCnt As Integer
'Dim mCnt As Integer
'Dim yCnt As Integer
'Dim irow As Long
'Dim iCol As Integer
'Dim tmpLoc As String
'Dim tmpItem As String
'Dim RowEndIndex As Long
'Dim ColEndIndex As Integer
'Dim ReposColEndIndex As Long
'Dim RtnCode As Integer
'
'dayCnt = 0
'weekCnt = 0
'monthCnt = 0
'quarterCnt = 0
'yearCnt = 0
'dCnt = 0
'wCnt = 0
'mCnt = 0
'qCnt = 0
'yCnt = 0
'
'tmpLoc = ""
'tmpItem = ""
'
'RowEndIndex = xMainArray.UpperBound(1)
'ColEndIndex = xMainArray.UpperBound(2)
'Call GetForecastRepositoryDetailStatus
'        For irow = AdjSysFcstPosition To RowEndIndex Step ElementsSelected
'             'Sri code change to save only modified  and new rows begin
''*********** If xMainArray.Value(irow, ColEndIndex - 3) = "Y" Or xMainArray.Value(irow, ColEndIndex - 2) = "Y" Or FcstReposDetailStatus = False Then
'             'Sri code change to save only modified  or new rows  end
'            If tmpLoc <> xFcstRepos(xMainArray.Value(irow, ColEndIndex - 1), 0) Or tmpItem <> xFcstRepos(xMainArray.Value(irow, ColEndIndex - 1), 1) Then
'                Select Case rsForecast("FcstInterval").Value
'                        Case int_Days
'                            dayCnt = 0
'                            weekCnt = 1
'                            monthCnt = 1
'                            quarterCnt = 1
'                            yearCnt = 1
'                            dCnt = 0
'                            wCnt = 0
'                            mCnt = 0
'                            qCnt = 0
'                            yCnt = 0
'                        Case int_Weeks
'                            dayCnt = 0
'                            weekCnt = 0
'                            monthCnt = 1
'                            quarterCnt = 1
'                            yearCnt = 1
'                            dCnt = 0
'                            wCnt = 0
'                            mCnt = 0
'                            qCnt = 0
'                            yCnt = 0
'                        Case int_Months
'                            dayCnt = 0
'                            weekCnt = 0
'                            monthCnt = 0
'                            quarterCnt = 1
'                            yearCnt = 1
'                            dCnt = 0
'                            wCnt = 0
'                            mCnt = 0
'                            qCnt = 0
'                            yCnt = 0
'                End Select
'                tmpLoc = xFcstRepos(xMainArray.Value(irow, ColEndIndex - 1), 0)
'                tmpItem = xFcstRepos(xMainArray.Value(irow, ColEndIndex - 1), 1)
'            End If
'
'            ReposColEndIndex = xRepos.UpperBound(2)
'            For iCol = 13 To ReposColEndIndex - 8
'                Select Case rsForecast("FcstInterval").Value
'                    Case int_Days
'                        dayCnt = dayCnt + 1
'                        If wCnt = 7 Then
'                            weekCnt = weekCnt + 1
'                            wCnt = 0
'                        End If
'                        wCnt = wCnt + 1
'                        If mCnt = 28 Then
'                            monthCnt = monthCnt + 1
'                            mCnt = 0
'                        End If
'                        mCnt = mCnt + 1
'                        If qCnt = 84 Then
'                            quarterCnt = quarterCnt + 1
'                            qCnt = 0
'                        End If
'                        qCnt = qCnt + 1
'                    Case int_Weeks
'                        weekCnt = weekCnt + 1
'                        If mCnt = 4 Then
'                            monthCnt = monthCnt + 1
'                            mCnt = 0
'                        End If
'                        mCnt = mCnt + 1
'                        If qCnt = 16 Then
'                            quarterCnt = quarterCnt + 1
'                            qCnt = 0
'                        End If
'                        qCnt = qCnt + 1
'                    Case int_Months
'                        monthCnt = monthCnt + 1
'                        If qCnt = 3 Then
'                            quarterCnt = quarterCnt + 1
'                            qCnt = 0
'                        End If
'                        qCnt = qCnt + 1
'                        If yCnt = 12 Then
'                            yearCnt = yearCnt + 1
'                            yCnt = 0
'                        End If
'                        yCnt = yCnt + 1
'                    Case int_Quarters
'                        quarterCnt = quarterCnt + 1
'                End Select
'                 If Not IsEmpty(xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)) Then
'                    With AIM_ForecastRepositoryDetail_Save_Sp
'                        .Parameters("@RepositoryKey") = AIM_ForecastRepository_Save_Sp.Parameters("@Repositorykey").Value
'                        .Parameters("@LcID") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 0)
'                        .Parameters("@item") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 1)
'                        .Parameters("@FcstPds") = iCol - 12
'                        .Parameters("@FcstPdStartDate") = Format(CDate(dgItems.Columns(iCol + 1).Caption), "yyyy/mm/dd")
'                        .Parameters("@PdSumYear") = yearCnt
'                        .Parameters("@PdSumQtr") = quarterCnt
'                        .Parameters("@PdSumMonth") = monthCnt
'                        .Parameters("@PdSumWeek") = weekCnt
'                        .Parameters("@PdSumDay") = dayCnt
'                        .Parameters("@QtyFcst") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)
'                        .Parameters("@QtyFcstNetReq") = IIf(IsEmpty(xNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)), 0, xNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol))
'                        .Parameters("@QtyAdjust") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol) - xForeCast.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)
'                        .Parameters("@QtyActualOrdered") = GetActShippedOrOrdered(xFcstRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 0), xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 1), iCol - 12, "Actual Ordered")
'                        .Parameters("@QtyActualShipped") = GetActShippedOrOrdered(xFcstRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 0), xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 1), iCol - 12, "Actual Shipped")
'                        .Parameters("@QtyProjectedInventory") = xInventory.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)
'                        .Execute
'                    End With
'                    'Sri code change to save only modified  and new rows into batch table begin
'                    'Here we are sending adjsysfcst and adjsysnetreq as qtyfcst and plannedrct parameters
'
''****************** If BatchSysFcst = True And (xMainArray.Value(irow, ColEndIndex - 3) = "Y" Or xMainArray.Value(irow, ColEndIndex - 2) = "Y") Then
'                    If BatchSysFcst Then
'                        With AIM_BatchFcst_Save_Sp
'                            .Parameters("@RepositoryKey") = AIM_ForecastRepository_Save_Sp.Parameters("@Repositorykey").Value
'                            .Parameters("@LcID") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 0)
'                            .Parameters("@item") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), 1)
'                            .Parameters("@FcstPds") = iCol - 12
'                            .Parameters("@SysFcst") = 0
'                            .Parameters("@SysNet") = 0
'                            .Parameters("@ModSysFcst") = xRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)
'                            .Parameters("@ModSysNet") = IIf(IsEmpty(xNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)), 0, xNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol))
'                            .Parameters("@PlannedRct") = xPlannedrct.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol)
'                            .Parameters("@FcstType") = "34"
'                            AIM_BatchFcst_Save_Sp.Execute
'                            RtnCode = AIM_BatchFcst_Save_Sp.Parameters(0).Value
'                        End With
'                    End If
'                    'Sri code change to save only modified  and new rows into batch table end
'
'                End If
'            Next iCol
'             'Sri code change to save only modified  and new rows begin
''***********End If
'            ' 'Sri code change to save only modified  and new rows end
'        Next irow
'
'
'       'Sri code change to save only modified  and new rows begin
'       'Reset the flags after saving
'       For irow = AdjSysFcstPosition To RowEndIndex Step ElementsSelected
'             xMainArray.Value(irow, ColEndIndex - 2) = "N" ' this is not necessary but be safe
'             xMainArray.Value(irow, ColEndIndex - 3) = "N"
'            xAdjForeCast.Value(xMainArray.Value(irow, ColEndIndex - 1), 12) = "N"
'        Next irow
'     'Sri code change to save only modified  or new rows  end
'Exit Function
'ErrorHandler:
'If Err.Number = 424 Or Err.Number = 53 Then
'        Resume Next
'End If
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SaveToRepository)"
'End Function
'Private Function PopulateRepository(xTempArray As XArrayDB, xRepos As XArrayDB, xNetRepos As XArrayDB) As Integer
'On Error GoTo ErrorHandler
'Dim irow As Long
'Dim iFRow As Long
'Dim iFcol As Integer
'Dim iCol As Integer
'Dim iTempRowEndIndex As Long
'Dim iReposRowEndIndex As Long
'Dim iTempColEndIndex As Integer
'
'iTempRowEndIndex = xTempArray.UpperBound(1)
'iReposRowEndIndex = xRepos.UpperBound(1)
'iTempColEndIndex = xTempArray.UpperBound(2)
'
'xRepos.QuickSort 0, iReposRowEndIndex, 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'xNetRepos.QuickSort 0, xNetRepos.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'xTempArray.QuickSort 0, iTempRowEndIndex, 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'        For irow = 0 To iTempRowEndIndex
'            For iFRow = 0 To iReposRowEndIndex
'                If xRepos.Value(iFRow, 0) = xTempArray.Value(irow, 0) And xRepos.Value(iFRow, 1) = xTempArray.Value(irow, 1) Then
'                    iFcol = 13
'                    For iCol = 12 To iTempColEndIndex - 9 Step 5
'                        xRepos.Value(iFRow, iFcol) = xTempArray(irow, iCol)
'                        xNetRepos.Value(iFRow, iFcol) = xTempArray(irow, iCol + 1)
'                        iFcol = iFcol + 1
'                    Next iCol
'                End If
'            Next iFRow
'        Next irow
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(PopulateRepository)"
'End Function
'Private Function PopulateMainStatSum(ArgElement As String, ArgXArrayDB As XArrayDB, ArgMainSum As Boolean, ArgStat As Boolean) As Integer
'On Error GoTo ErrorHandler
'Dim iMrow As Long
'Dim iSRow As Long
'Dim irow As Long
'Dim iCol As Integer
'Dim Seq As Integer
'Dim iColYear As Integer
'Dim StatPos As Long
'Dim Element As Boolean
'Dim SumPos As Long
'Dim ColAdj As Integer
'Dim ColADj_1 As Integer
'Dim RowStartIndex As Long
'Dim RowEndIndex As Long
'Dim ColStartIndex As Integer
'Dim ColEndIndex As Integer
'Dim MainColEndIndex As Long
'
'RowStartIndex = ArgXArrayDB.LowerBound(1)
'RowEndIndex = ArgXArrayDB.UpperBound(1)
'ColStartIndex = ArgXArrayDB.LowerBound(2)
'ColEndIndex = ArgXArrayDB.UpperBound(2)
'MainColEndIndex = xMainArray.UpperBound(2)
'
'If ArgElement = "ProdConst" Then
'    iMrow = ProdConstPosition
'    SumPos = ProdConstPosition
'    Seq = 10
'    Element = lsElement.Selected(7)
'
'ElseIf ArgElement = "Inventory" Then
'    iMrow = InventoryPosition
'    SumPos = InventoryPosition
'    Seq = 9
'    StatPos = 6
'    Element = lsElement.Selected(6)
' ElseIf ArgElement = "Sys Fcst" Then
'    iMrow = SysFcstPosition
'    SumPos = SysFcstPosition
'    Seq = 8
'    StatPos = 5
'    Element = lsElement.Selected(5)
'  ElseIf ArgElement = "Sys Net Req" Then
'    iMrow = SysNetReqPosition
'    SumPos = SysNetReqPosition
'    Seq = 7
'    StatPos = 4
'    Element = True 'Only True condition is valid
'  ElseIf ArgElement = "Adj. Sys Fcst" Then
'    iMrow = AdjSysFcstPosition
'    SumPos = AdjSysFcstPosition
'    Seq = 6
'    'StatPos does not write to stat array
'    Element = True 'Only True condition is valid
'    iSRow = 0 'I dont know where it is  used
'  ElseIf ArgElement = "Adj. Sys Net Req" Then
'    iMrow = AdjSysNetReqPosition
'    SumPos = AdjSysNetReqPosition
'    Seq = 5
'    'StatPos does not write to stat array
'    Element = True 'Only True condition is valid
'    iSRow = 0 'I dont know where it is  used
'  ElseIf ArgElement = "Draft" Then
'    iMrow = DraftPosition
'    SumPos = DraftPosition
'    Seq = 4
'    StatPos = 3
'    Element = lsElement.Selected(3)
'    iSRow = 0 'I dont know where it is  used
'  ElseIf ArgElement = "Forecast" Then
'    iMrow = FcstPosition
'    SumPos = FcstPosition
'    Seq = 3
'    StatPos = 2
'    Element = lsElement.Selected(2)
'    iSRow = 0 'I dont know where it is  used
'  ElseIf ArgElement = "Budget" Then
'    iMrow = BudgetPosition
'    SumPos = BudgetPosition
'    Seq = 2
'    StatPos = 1
'    Element = lsElement.Selected(1)
'    iSRow = 0 'I dont know where it is  used
'  ElseIf ArgElement = "Sales" Then
'    iMrow = SalesPosition
'    SumPos = SalesPosition
'    Seq = 1
'    StatPos = 0
'    Element = lsElement.Selected(0)
'End If
'If ArgElement = "Sales" Then
'    ColAdj = 10
'    ColADj_1 = 2
'Else
'    ColAdj = 8
'    ColADj_1 = 0
'End If
'
'If Element Then
'        xSumArray.Value(iMrow, 0) = ArgElement
'        For irow = RowStartIndex To ((RowEndIndex - RowStartIndex))
'            If ValidateSales(irow, ArgElement) Then
'                If FoundInfilteredItems(ArgXArrayDB.Value(irow, 0), ArgXArrayDB.Value(irow, 1), ArgXArrayDB.Value(irow, 3) _
'                        , ArgXArrayDB.Value(irow, 4), ArgXArrayDB.Value(irow, 5), ArgXArrayDB.Value(irow, 6) _
'                        , ArgXArrayDB.Value(irow, 7), ArgXArrayDB.Value(irow, 9), ArgXArrayDB.Value(irow, 10), ArgXArrayDB.Value(irow, 11)) Then
'
'                    'xMainArray.Value(iMRow, 13) = 0
'                    xMainArray.Value(iMrow, MainColEndIndex) = Seq
'                    If ArgElement <> "Inventory" Then xMainArray.Value(iMrow, 13) = 0
'                    If ArgElement = "Adj. Sys Fcst" Then
'                        xMainArray.Value(iMrow, MainColEndIndex - 1) = irow
'                        'Sri code change to save only modified and new rows
'                        'Initilize the Modified column with N initially
'                        'Populate New col with data from col 12
'                        xMainArray.Value(iMrow, MainColEndIndex - 2) = "N"
'                        xMainArray.Value(iMrow, MainColEndIndex - 3) = ArgXArrayDB.Value(irow, 12)
'                    End If
'                    For iCol = ColStartIndex To 2
'                        xMainArray.Value(iMrow, iCol) = ArgXArrayDB.Value(irow, iCol)
'                    Next iCol
'
'                    xMainArray.Value(iMrow, 3) = ArgElement
'
'                    For iCol = 3 To 11
'                        xMainArray.Value(iMrow, iCol + 1) = ArgXArrayDB.Value(irow, iCol)
'                    Next iCol
'
'                    For iCol = 13 To ColEndIndex - ColAdj
'                        iColYear = 0
'                        If ForecastUnit = "Cost" Or ForecastUnit = "Price" Then
'                            GetYearIndex dgItems.Columns(iCol + 1).Caption, iColYear
'                        End If
'                        If IsNumeric(ArgXArrayDB.Value(irow, iCol)) And dgItems.Columns(iCol + 1).Visible Then
'                            xMainArray.Value(iMrow, iCol + 1) = ArgXArrayDB.Value(irow, iCol) * IIf(iColUnit = 0, 1, ArgXArrayDB.Value(irow, (ColEndIndex - iColUnit - ColADj_1) + iColYear))
'                            If ArgMainSum = True Then
'                                xMainArray.Value(iMrow, 13) = xMainArray.Value(iMrow, 13) + (ArgXArrayDB.Value(irow, iCol) * IIf(iColUnit = 0, 1, ArgXArrayDB.Value(irow, (ColEndIndex - iColUnit - ColADj_1) + iColYear)))
'                            End If
'                            xSumArray.Value(SumPos, iCol - 11) = xSumArray.Value(SumPos, iCol - 11) + (ArgXArrayDB.Value(irow, iCol) * IIf(iColUnit = 0, 1, ArgXArrayDB.Value(irow, (ColEndIndex - iColUnit - ColADj_1) + iColYear)))
'                        End If
'                        If ArgStat = True Then
'                            xStatArray.Value(StatPos, iCol - 12) = xStatArray.Value(StatPos, iCol - 12) + (ArgXArrayDB.Value(irow, iCol) * IIf(iColUnit = 0, 1, ArgXArrayDB.Value(irow, (ColEndIndex - iColUnit - ColADj_1) + iColYear)))
'                        End If
'                    Next iCol
'
'                    iMrow = iMrow + ElementsSelected
'                    iSRow = iSRow + 1
'                Else
'                    'xMainArray.ReDim 0, xMainArray.UpperBound(1) - 1, 0, xMainArray.UpperBound(2)
'                End If
'            End If
'        Next irow
'    Else
'        For irow = RowStartIndex To ((RowEndIndex - RowStartIndex))
'            If ValidateSales(irow, ArgElement) Then
'                If FoundInfilteredItems(ArgXArrayDB.Value(irow, 0), ArgXArrayDB.Value(irow, 1), ArgXArrayDB.Value(irow, 3) _
'                        , ArgXArrayDB.Value(irow, 4), ArgXArrayDB.Value(irow, 5), ArgXArrayDB.Value(irow, 6) _
'                        , ArgXArrayDB.Value(irow, 7), ArgXArrayDB.Value(irow, 9), ArgXArrayDB.Value(irow, 10), ArgXArrayDB.Value(irow, 11)) Then
'                    For iCol = 13 To ColEndIndex - ColStartIndex - ColAdj
'                        If IsNumeric(ArgXArrayDB.Value(irow, iCol)) Then
'                            iColYear = 0
'                            If ForecastUnit = "Cost" Or ForecastUnit = "Price" Then
'                                GetYearIndex dgItems.Columns(iCol + 1).Caption, iColYear
'                            End If
'                            If ArgStat = True Then
'                               xStatArray.Value(StatPos, iCol - 12) = xStatArray.Value(StatPos, iCol - 12) + (ArgXArrayDB.Value(irow, iCol) * IIf(iColUnit = 0, 1, ArgXArrayDB.Value(irow, (ColEndIndex - iColUnit - ColADj_1) + iColYear)))
'                            End If
'                        End If
'                    Next iCol
'                End If
'            End If
'        Next irow
'End If
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(PopulateMainStatSum)"
'End Function
'Private Function PopulateReposFromMain(xRepos As XArrayDB, xNetRepos As XArrayDB) As Integer
'On Error GoTo ErrorHandler
'Dim irow As Long
'Dim iSRow As Long
'Dim iCol As Integer
'Dim RowStartIndex As Long
'Dim RowMainEndIndex As Long
'Dim ColMainEndIndex As Integer
'Dim ColReposEndIndex As Integer
'Dim RowReposEndIndex As Long
'
'RowMainEndIndex = xMainArray.UpperBound(1)
'RowReposEndIndex = xRepos.UpperBound(1)
'ColMainEndIndex = xMainArray.UpperBound(2)
'ColReposEndIndex = xRepos.UpperBound(2)
'
'For irow = AdjSysFcstPosition To RowMainEndIndex Step ElementsSelected
'        If Fcsttype = "FCST" Then
'            For iSRow = 0 To RowReposEndIndex
'                If xRepos.Value(iSRow, 0) = xMainArray.Value(irow, 0) _
'                        And xRepos.Value(iSRow, 1) = xMainArray.Value(irow, 1) _
'                        And UCase(xRepos.Value(iSRow, ColReposEndIndex)) = UCase("Units") Then
'
'                    For iCol = 14 To ColMainEndIndex - 1
'                        xRepos.Value(iSRow, iCol - 1) = xMainArray.Value(irow, iCol)
'                        xNetRepos.Value(iSRow, iCol - 1) = xMainArray.Value(irow - 1, iCol)
'                    Next iCol
'                End If
'            Next iSRow
'        End If
'Next irow
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(PopulateReposFromMain)"
'End Function
'Private Function SetColumnFormat(GridObject As SSOleDBGrid, ColIndex As Integer, Name As String, Caption As String, Width As Integer, CaptionAlign As Integer, Align As Integer, Loc As Boolean, Visible As Boolean) As Integer
'On Error GoTo ErrorHandler
'
'    GridObject.Columns(ColIndex).Name = Name
'    GridObject.Columns(ColIndex).Caption = getTranslationResource(Caption)
'    GridObject.Columns(ColIndex).Width = Width
'    GridObject.Columns(ColIndex).CaptionAlignment = CaptionAlign
'    GridObject.Columns(ColIndex).Alignment = Align
'    GridObject.Columns(ColIndex).Locked = Loc
'    GridObject.Columns(ColIndex).Visible = Visible
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(SetColumnFormat)"
'End Function
'
'Private Sub UpdateGraph(ByVal xSumFcst As XArrayDB, ByVal xElementArray As XArrayDB, ByVal Pds As Integer, ByVal BaseDate As Date, ByVal Interval As AIM_INTERVALS)
'On Error GoTo ErrorHandler
''Bar = Inventory, System Net Req, Adj System Net Req
''Plot = Sales, Budget, Forecast, System Forecast, Adj System Forecast, Draft
'
'    Dim BarLineCnt As Integer
'    Dim iBarLine As Integer
'    Dim iPlotLine As Integer
'    Dim RowIndex As Long
'
'    Dim iBarCnt As Integer
'    Dim iPlotCnt As Integer
'    Dim BarGroup As Integer
'    Dim PlotGroup As Integer
'    Dim GraphColCnt As Integer
'    Dim SumArrayCol As Integer
'
'    Dim CurStartdate As Date
'    Dim DateCnt As Integer
'    Dim InputDate As Date
'    Dim FirstDate As Date
'    Dim NextDate As Date
'    Dim StartPdCnt As Integer
'
'    Dim tmpElementCnt As Integer
'    Dim EndPdCnt As Integer
'    Dim MaxPds As Integer
'    Dim RowStartIndex As Long
'    Dim RowEndIndex As Long
'
'    RowStartIndex = xElementArray.LowerBound(1)
'    RowEndIndex = xElementArray.UpperBound(1)
'
'
'    'count of bar and plot line
'    iBarCnt = 0
'    iPlotCnt = 0
'
'    'ChartGroups no for drawing graph
'    BarGroup = 0
'    PlotGroup = 0
'
'    'Sequence no of bar and plot in the graph
'    iBarLine = 0
'    iPlotLine = 0
'
'    'Add Adj items which is not in Element List
'    'Only happend when Interval is not rollup
'
'    'Calculate total number of bar and plot line
'    For BarLineCnt = RowStartIndex To RowEndIndex
'        If xElementArray(BarLineCnt, 1) = "Inventory" Or xElementArray(BarLineCnt, 1) = "Sys Net Req" Or xElementArray(BarLineCnt, 1) = "Adj. Sys Net Req" Then
'            iBarCnt = iBarCnt + 1
'        Else
'            iPlotCnt = iPlotCnt + 1
'        End If
'    Next BarLineCnt
'
'    'Assign ChartGroup dynamically if a Bar or Plot is exist
'    If iBarCnt = 0 And iPlotCnt = 0 Then
'        Exit Sub ' no element selected
'    ElseIf iBarCnt <> 0 And iPlotCnt <> 0 Then
'        PlotGroup = 1
'        BarGroup = 2
'    ElseIf iBarCnt = 0 And iPlotCnt <> 0 Then
'        PlotGroup = 1
'    ElseIf iBarCnt <> 0 And iPlotCnt = 0 Then
'        BarGroup = 1
'    End If
'
'    'Get start period
'    InputDate = txtFcstStartDate.Value
'    CurStartdate = FcstStartDate
'    FirstDate = FcstStartDate
'    For DateCnt = 1 To FcstPds
'        'NextDate = AdvForecast.GetNextStartDate(FcstStartDate, CurStartdate, Interval)
'        NextDate = GetNextStartDate(FcstStartDate, CurStartdate, Interval)
'        If InputDate = FirstDate Then
'            CurStartdate = FirstDate
'            StartPdCnt = DateCnt
'            Exit For
'        ElseIf InputDate > FirstDate And InputDate < NextDate Then
'            CurStartdate = NextDate
'            StartPdCnt = DateCnt + 1 'Start next period instead
'            Exit For
'        End If
'        CurStartdate = NextDate
'        FirstDate = NextDate
'    Next DateCnt
'    If StartPdCnt = 0 Then
'        StartPdCnt = 1
'    End If
'
'    If StartPdCnt + Pds > FcstPds Then
'        EndPdCnt = FcstPds
'        MaxPds = FcstPds - StartPdCnt + 1
'    Else
'        EndPdCnt = StartPdCnt + Pds - 1
'        MaxPds = Pds
'    End If
'
'    With Me.chtForecast
'        .IsBatched = True
'
'        'Remove any old axes labels
'        .ChartArea.Axes("X").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y2").ValueLabels.RemoveAll
'
'        .ChartGroups(1).SeriesLabels.RemoveAll
'        .ChartGroups(2).SeriesLabels.RemoveAll
'        .ChartGroups(1).Styles.RemoveAll
'        .ChartGroups(2).Styles.RemoveAll
'        .ChartGroups(1).PointStyles.RemoveAll
'        .ChartGroups(2).PointStyles.RemoveAll
'
''        'Set chart location and size
''        .ChartArea.Location.Top = 10
''        .ChartArea.Location.Left = 10
''        .ChartArea.Location.Height = 290
''        .ChartArea.Location.Width = 790
'
'        'Set up Header
'        .Header.Text = getTranslationResource("Summary of Forecast Modification")
'        .Border.Type = oc2dBorderPlain
'        .Border.Width = 2
'
'        'Set X axes properties
'        .ChartArea.Axes("X").Title.Text = getTranslationResource("Periods")
'        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
'        .ChartArea.Axes("X").MajorGrid.Spacing = 100
'        .ChartArea.Axes("X").MajorGrid.Style.Pattern = oc2dLineNone
'        .ChartArea.Axes("X").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("X").TimeScale.Unit = oc2dTimeMonths
'        .ChartArea.Axes("X").TimeScale.Format.Value = "%d-%b-%y"
'        .ChartArea.Axes("X").AnnotationPlacement = oc2dAnnotateOrigin
'        .ChartArea.Axes("X").AnnotationRotation = oc2dRotate90Degrees
'        .ChartArea.Axes("X").Max = MaxPds + 0.5 'plus 0.5 to make graph display properly
'
'        'Set Y axes properties
'        .ChartArea.Axes("Y").Title.Text = getTranslationResource("Forecast")
'        .ChartArea.Axes("Y").TitleRotation = oc2dRotate90Degrees
'        .ChartArea.Axes("Y").AnnotationMethod = oc2dAnnotateValues
'        .ChartArea.Axes("Y").MajorGrid.Spacing = 100 '20000
'        .ChartArea.Axes("Y").MajorGrid.Style.Pattern = oc2dLineSolid
'        .ChartArea.Axes("Y").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("Y").Origin = 0
'
'        'Set Y2 axes properties
'        .ChartArea.Axes("Y2").Title.Text = getTranslationResource("Inventory")
'        .ChartArea.Axes("Y2").TitleRotation = oc2dRotate90Degrees
'        .ChartArea.Axes("Y2").AnnotationMethod = oc2dAnnotateValues
'        .ChartArea.Axes("Y2").AxisStyle.LineStyle.Width = 1
'
'        'Set up Legends
'        .Legend.Anchor = oc2dAnchorEast
'        .Legend.IsShowing = True
'        .Legend.Border.Type = oc2dBorderPlain
'        .Legend.Border.Width = 2
'        .Legend.Text = getTranslationResource("Legend")
'
'        GraphColCnt = 0
'        For DateCnt = StartPdCnt To EndPdCnt
'            GraphColCnt = GraphColCnt + 1
'            'Add X Value Lables
'            .ChartArea.Axes("X").ValueLabels.Add GraphColCnt, Format(CurStartdate, "yyyy/mm/dd")
'            '.ChartArea.Axes("X").ValueLabels.Add GraphColCnt, Format(CurStartdate, "dd/mm/yyyy")
'            'Get the Next Start date
'            'CurStartdate = AdvForecast.GetNextStartDate(FcstStartDate, CurStartdate, Interval)
'            CurStartdate = GetNextStartDate(FcstStartDate, CurStartdate, Interval)
'        Next DateCnt
'
'        For BarLineCnt = RowStartIndex To RowEndIndex
'            If xElementArray(BarLineCnt, 1) = "Inventory" Or xElementArray(BarLineCnt, 1) = "Sys Net Req" Or xElementArray(BarLineCnt, 1) = "Adj. Sys Net Req" Then
'                .ChartGroups(BarGroup).SeriesLabels.Add xElementArray(BarLineCnt, 1)
'                iBarLine = iBarLine + 1
'                .ChartGroups(BarGroup).ChartType = oc2dTypeBar
'                .ChartGroups(BarGroup).Data.NumSeries = iBarLine
'                .ChartGroups(BarGroup).Data.NumPoints(iBarLine) = MaxPds
'                If xElementArray(BarLineCnt, 1) = "Adj. Sys Net Req" Then
'                    .ChartGroups(BarGroup).Styles(iBarLine).Line.Color = &H800080   'purple
'                    RowIndex = xSumFcst.Find(0, 0, "Adj. Sys Net Req", , , XTYPE_STRINGCASESENSITIVE)
'                    GraphColCnt = 0
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(BarGroup).Data.Y(iBarLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Sys Net Req" Then
'                    .ChartGroups(BarGroup).Styles(iBarLine).Line.Color = vbRed
'                    RowIndex = xSumFcst.Find(0, 0, "Sys Net Req", , , XTYPE_STRINGCASESENSITIVE)
'                    GraphColCnt = 0
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(BarGroup).Data.Y(iBarLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Inventory" Then
'                    .ChartGroups(BarGroup).Styles(iBarLine).Line.Color = vbBlue
'                    RowIndex = xSumFcst.Find(0, 0, "Inventory", , , XTYPE_STRINGCASESENSITIVE)
'                    GraphColCnt = 0
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(BarGroup).Data.Y(iBarLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                End If
'            Else
'                .ChartGroups(PlotGroup).SeriesLabels.Add xElementArray(BarLineCnt, 1)
'                iPlotLine = iPlotLine + 1
'                .ChartGroups(PlotGroup).ChartType = oc2dTypePlot
'                .ChartGroups(PlotGroup).Data.NumSeries = iPlotLine
'                .ChartGroups(PlotGroup).Data.NumPoints(iPlotLine) = MaxPds
'                .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Width = 2
'
'                If xElementArray(BarLineCnt, 1) = "Sales" Then
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Shape = oc2dShapeDot
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Color = vbGreen
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Color = vbMagenta
'                    RowIndex = xSumFcst.Find(0, 0, "Sales", , , XTYPE_STRINGCASESENSITIVE)
'                    GraphColCnt = 0
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(PlotGroup).Data.Y(iPlotLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Budget" Then
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Shape = oc2dShapeStar
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Color = vbRed
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Color = vbGreen
'                    RowIndex = xSumFcst.Find(0, 0, "Budget", , , XTYPE_STRINGCASESENSITIVE)
'                    GraphColCnt = 0
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(PlotGroup).Data.Y(iPlotLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Forecast" Then
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Shape = oc2dShapeTriangle
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Color = &H808080
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Color = vbYellow
'                    RowIndex = xSumFcst.Find(0, 0, "Forecast", , , XTYPE_STRINGCASESENSITIVE)
'                    GraphColCnt = 0
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(PlotGroup).Data.Y(iPlotLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Sys Fcst" Then
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Shape = oc2dShapeDiamond
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Color = vbMagenta
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Color = vbCyan
'                    GraphColCnt = 0
'                    RowIndex = xSumFcst.Find(0, 0, "Sys Fcst", , , XTYPE_STRINGCASESENSITIVE)
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(PlotGroup).Data.Y(iPlotLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Adj. Sys Fcst" Then
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Shape = oc2dShapeBox
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Color = vbYellow
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Color = &H4080&
'                    GraphColCnt = 0
'                    RowIndex = xSumFcst.Find(0, 0, "Adj. Sys Fcst", , , XTYPE_STRINGCASESENSITIVE)
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(PlotGroup).Data.Y(iPlotLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                ElseIf xElementArray(BarLineCnt, 1) = "Draft" Then
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Shape = oc2dShapeDiagonalCross
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Symbol.Color = vbBlack
'                    .ChartGroups(PlotGroup).Styles(iPlotLine).Line.Color = &HFF8080
'                    GraphColCnt = 0
'                    RowIndex = xSumFcst.Find(0, 0, "Draft", , , XTYPE_STRINGCASESENSITIVE)
'                    'For SumArrayCol = StartPdCnt To EndPdCnt
'                    For SumArrayCol = StartPdCnt + 1 To EndPdCnt + 1
'                        GraphColCnt = GraphColCnt + 1
'                        .ChartGroups(PlotGroup).Data.Y(iPlotLine, GraphColCnt) = xSumFcst(RowIndex, SumArrayCol)
'                    Next SumArrayCol
'                End If
'            End If
'        Next BarLineCnt
'
'        'Finalize Grid Spacing
'        .ChartArea.Axes("Y").MajorGrid.Spacing = .ChartArea.Axes("Y").NumSpacing
'
'        .IsBatched = False
'        .AllowUserChanges = False
'
'        'Redisplay graph
'        .Refresh
'
'    End With
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(UpdateGraph)"
'End Sub
'
'Private Sub FillGrid()
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim iMrow As Long
'    Dim iSRow As Long
'    Dim iFcol As Integer
'    Dim iColYear As Integer
'    Dim ThisFy As Integer
'    Dim PrdFy As Integer
'    Dim xElementItem As New XArrayDB
'    Dim GraphCnt As Long
'    Dim tmpElementCnt As Integer
'    Dim xx As Long
'    Dim SumColEndIndex As Integer
'    Dim StatColEndIndex As Integer
'    Dim SumRowEndIndex As Long
'    Dim MainRowEndIndex As Long
'
'
'    Set xMainArray = Nothing
'    Set xSumArray = Nothing
'
'    'ParseElementsList
'    If xForeCast.Count(1) = 0 Then
'        xMainArray.ReDim 0, 0, 0, 0
'        Exit Sub
'    End If
'
'    'Sri code change to save only modified and new rows
'    'Sri added one more column which will indicated if the data has changed or not
'    'xMainArray.ReDim 0, ((xForeCast.UpperBound(1) + 1) * ElementsSelected) - 1, 0, xForeCast.UpperBound(2) - 5
'    'xSumArray.ReDim 0, ElementsSelected - 1, 0, xMainArray.UpperBound(2) - 11
'    'xStatArray.ReDim 0, 6, 0, xMainArray.UpperBound(2) - 12
'    xMainArray.ReDim 0, ((xForeCast.UpperBound(1) + 1) * ElementsSelected) - 1, 0, xForeCast.UpperBound(2) - 3
'    xSumArray.ReDim 0, ElementsSelected - 1, 0, xMainArray.UpperBound(2) - 13
'    xStatArray.ReDim 0, 6, 0, xMainArray.UpperBound(2) - 14
'    'Sri chnaged end
'
'    If GetAdjSysFcstAndNetReq = True Then
'        'Sri batch fcst begin
'        'commented 3 lines of code
''        GetForecastMaint dcFcstId.Text, xAdjForeCast, False, FcstStartdate, FcstInterval, True, Fcsttype, False
'        If GetBatchdata = False Then
'            Set xAdjForeCast = Nothing
'            GetForecastMaint dcFcstId.Text, xAdjForeCast, False, FcstStartDate, FcstInterval, True, Fcsttype, False
'        End If
'        'Sri batch fcst end
'
'        xAdjForeCast.QuickSort 0, xAdjForeCast.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'
'        'Sri batch fcst begin
'        ' commented 3 lines of code
''        Set xAdjForecastNr = Nothing
''        GetForecastMaint dcFcstId.Text, xAdjForecastNr, True, FcstStartdate, FcstInterval, True, Fcsttype, True, xPlannedrct
'        If GetBatchdata = False Then
'            Set xAdjForecastNr = Nothing
'            GetForecastMaint dcFcstId.Text, xAdjForecastNr, True, FcstStartDate, FcstInterval, True, Fcsttype, True, xPlannedrct
'        End If
'         'Sri batch fcst end
'
'
'        xAdjForecastNr.QuickSort 0, xAdjForecastNr.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'        xPlannedrct.QuickSort 0, xPlannedrct.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'    End If
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before CalcInitial Stock" & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    CalculateStock
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "After CalcInital Stock " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    xStatArray.Value(0, 0) = "Sales"
'    xStatArray.Value(1, 0) = "Budget"
'    xStatArray.Value(2, 0) = "Forecast"
'    xStatArray.Value(3, 0) = "Draft"
'    xStatArray.Value(4, 0) = "Sys Net Req"
'    xStatArray.Value(5, 0) = "Sys Fcst"
'    xStatArray.Value(6, 0) = "Inventory"
'
'    SumColEndIndex = xSumArray.UpperBound(2)
'
'    For irow = 0 To ElementsSelected - 1
'        For iCol = 1 To SumColEndIndex
'            xSumArray.Value(irow, iCol) = 0
'        Next iCol
'    Next irow
'
'    StatColEndIndex = xStatArray.UpperBound(2)
'    For irow = 0 To 6
'        For iCol = 1 To StatColEndIndex
'            xStatArray.Value(irow, iCol) = 0
'        Next iCol
'    Next irow
'
'    For iCol = 13 To dgItems.Cols - 1
'        If ForecastUnit = "Cost" Or ForecastUnit = "Price" Then
'            Me.dgItems.Columns(iCol).NumberFormat = "$#,##0"
'        Else
'            Me.dgItems.Columns(iCol).NumberFormat = "#,##0"
'        End If
'    Next iCol
'
'    For iCol = 1 To dgSummary.Cols - 1
'        If ForecastUnit = "Cost" Or ForecastUnit = "Price" Then
'            Me.dgSummary.Columns(iCol).NumberFormat = "$#,##0"
'        Else
'            Me.dgSummary.Columns(iCol).NumberFormat = "#,##0"
'        End If
'    Next iCol
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 6 " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    PopulateMainStatSum "Inventory", xInventory, False, True
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 5 " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    PopulateMainStatSum "Sys Fcst", xForeCast, True, True
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 4 " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    If lsElement.Selected(4) Then
'         If xForecastNr.Count(1) = 0 Then
'             GetForecastMaint dcFcstId.Text, xForecastNr, True, FcstStartDate, FcstInterval, False, "FCST", False
'
'             If xForecastNr.Count(1) = 0 Then
'                 Exit Sub
'             End If
'             xForecastNr.QuickSort 0, xForecastNr.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'         End If
'        PopulateMainStatSum "Sys Net Req", xForecastNr, True, True
'    End If
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before AdjSysFcstPosition " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    If AdjSysFcstPosition <> -1 Then
'        PopulateMainStatSum "Adj. Sys Fcst", xAdjForeCast, True, False
'    End If
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before AdjsysnetReqPosition " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    If AdjSysNetReqPosition <> -1 Then
'        PopulateMainStatSum "Adj. Sys Net Req", xAdjForecastNr, True, False
'    End If
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 3 " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    'If lsElement.Selected(3) Then
'        If ckNetRqmts.Value = 0 Then
'            PopulateMainStatSum "Draft", xDraftRepos, True, True
'        Else
'            PopulateMainStatSum "Draft", xDraftNetRepos, True, True
'        End If
'    'End If
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 2 " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'
'    'If lsElement.Selected(2) Then
'        If ckNetRqmts.Value = 0 Then
'            PopulateMainStatSum "Forecast", xFcstRepos, True, True
'        Else
'            PopulateMainStatSum "Forecast", xFcstNetRepos, True, True
'        End If
'    'End If
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 1 " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    'If lsElement.Selected(1) Then
'        If ckNetRqmts.Value = 0 Then
'            PopulateMainStatSum "Budget", xBudgetRepos, True, True
'        Else
'            PopulateMainStatSum "Budget", xBudgetNetRepos, True, True
'        End If
'   'End If
'
''    'Sri add code for prodconst start
''    If ProdConstPosition <> -1 Then
''        PopulatePreProdConstData
''        GetProdConstraint xProdConst, FcstPds
''    End If
''    'Sri add code for prodconst end
''
''    'Sri add code for prodconst start
''    If lsElement.Selected(7) Then
''        PopulateMainStatSum "ProdConst", xProdConst, True, False
''    End If
''    'Sri add code for prodconst end
'
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before Element 0" & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    PopulateMainStatSum "Sales", xSales, True, True
'
'    SumRowEndIndex = xSumArray.UpperBound(1)
'    For irow = 0 To SumRowEndIndex
'        If xSumArray.Value(irow, 0) = "Inventory" Then
'            xSumArray.Value(irow, 1) = " "
'        Else
'            For iCol = 2 To SumColEndIndex
'                xSumArray.Value(irow, 1) = xSumArray.Value(irow, 1) + xSumArray.Value(irow, iCol)
'            Next iCol
'        End If
'    Next irow
'
'    'Initialize the rowChange array
'    xRowChange.ReDim 0, 0, 0, 2
'
'    'If ForecastUnit <> "Units" Or OrgFcstInterval <> OldFcstInterval Then
'    '    dgItems.AllowUpdate = False
'    'Else
'    '    dgItems.AllowUpdate = True
'    'End If
'
'    'Security Validation
'    If AccessType = 1 Or ForecastUnit <> "Units" Or OrgFcstInterval <> OldFcstInterval Then 'Read Only
'        dgItems.AllowUpdate = False
'        Me.atbFcstModification.Tools("ID_Save").Enabled = False
'        cmApplyPcnt.Enabled = False
'    ElseIf AccessType = 3 And FcstExist Then
'        dgItems.AllowUpdate = False
'        Me.atbFcstModification.Tools("ID_Save").Enabled = False
'        cmApplyPcnt.Enabled = False
'    ElseIf AccessType = 4 And Not FcstExist Then
'        dgItems.AllowUpdate = False
'        Me.atbFcstModification.Tools("ID_Save").Enabled = False
'        cmApplyPcnt.Enabled = False
'    Else
'        dgItems.AllowUpdate = True
'        Me.atbFcstModification.Tools("ID_Save").Enabled = True
'        cmApplyPcnt.Enabled = True
'    End If
'
'    Me.atbFcstModification.Tools("ID_GoToBookMark").Enabled = False
'
'    MainRowEndIndex = xMainArray.UpperBound(1)
'    For irow = 0 To MainRowEndIndex
'        If IsEmpty(xMainArray.Value(irow, 0)) Then
'            xx = xMainArray.DeleteRows(irow, MainRowEndIndex - irow + 1)
'            Exit For
'        End If
'    Next irow
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before rebind items " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    Me.dgItems.ReBind
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before rebind summary " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    Me.dgSummary.ReBind
'
'    GraphCnt = 0
'    xElementItem.ReDim 0, Me.lsElement.SelCount - 1, 0, 1
'    For irow = 0 To Me.lsElement.ListCount - 1
'        If Me.lsElement.Selected(irow) = True Then
'            If Me.lsElement.List(irow) = "Sales" Then
'                xElementItem(GraphCnt, 0) = 1
'            ElseIf Me.lsElement.List(irow) = "Budget" Then
'                xElementItem(GraphCnt, 0) = 2
'            ElseIf Me.lsElement.List(irow) = "Forecast" Then
'                xElementItem(GraphCnt, 0) = 3
'            ElseIf Me.lsElement.List(irow) = "Draft" Then
'                xElementItem(GraphCnt, 0) = 4
'            ElseIf Me.lsElement.List(irow) = "Sys Fcst" Then
'                xElementItem(GraphCnt, 0) = 6
'            ElseIf Me.lsElement.List(irow) = "Sys Net Req" Then
'                xElementItem(GraphCnt, 0) = 8
'            ElseIf Me.lsElement.List(irow) = "Inventory" Then
'                xElementItem(GraphCnt, 0) = 9
'            End If
'            xElementItem(GraphCnt, 1) = Me.lsElement.List(irow)
'            GraphCnt = GraphCnt + 1
'        End If
'    Next irow
'
'    tmpElementCnt = xElementItem.UpperBound(1)
'    If AdjSysFcstPosition > -1 Then
'        tmpElementCnt = tmpElementCnt + 1
'        xElementItem.InsertRows tmpElementCnt, 1
'        xElementItem(tmpElementCnt, 0) = 5
'        xElementItem(tmpElementCnt, 1) = "Adj. Sys Fcst"
'    End If
'    If AdjSysNetReqPosition > -1 Then
'        tmpElementCnt = tmpElementCnt + 1
'        xElementItem.InsertRows tmpElementCnt, 1
'        xElementItem(tmpElementCnt, 0) = 7
'        xElementItem(tmpElementCnt, 1) = "Adj. Sys Net Req"
'    End If
'
'    xElementItem.QuickSort xElementItem.LowerBound(1), xElementItem.UpperBound(1), 0, XORDER_ASCEND, XTYPE_INTEGER
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before statistics" & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    SetStatistic xStatArray, txtFcstPds.Value, txtFcstStartDate.Value, FcstInterval
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Before updategraph " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'    UpdateGraph xSumArray, xElementItem, txtFcstPds.Value, txtFcstStartDate.Value, FcstInterval
'    'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "Fill Grid end" & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(FillGrid)"
'End Sub
'
''Private Function GetsysFcst(LcID As String, Item As String, Period As Integer) As Integer
''    GetsysFcst = 0
''End Function
'
'Private Sub SaveForecast()
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim iSRow As Long
'    Dim iCounter As Long
'    Dim Percent As Double
'    Dim SaveAll As Boolean
'    Dim ModNetReq() As Double
'    Dim strMessage As String
'    Dim RtnCode As Long
'    Dim RowStartIndex As Long
'    Dim RowEndIndex As Long
'    Dim ColStartIndex As Integer
'    Dim ColEndIndex As Integer
'
'    ItStatBookMark = Me.dgItems.bookmark
'    Me.atbFcstModification.Tools("ID_GoToBookMark").Enabled = True
'
'    On Error Resume Next
'    If dgItems.Row + 2 = dgItems.Rows Then
'        dgItems.Row = dgItems.Row - 1
'    Else
'        dgItems.Row = dgItems.Row + 1
'    End If
'    On Error GoTo ErrorHandler
'
'    If SysFcstPosition = -1 Then 'Or SysNetReqPosition = -1 Then
'        strMessage = getTranslationResource("MSGBOX06901")
'        If StrComp(strMessage, "MSGBOX06901") = 0 Then strMessage = "To save a usable Forecast, at least one valid Element is required. Please select from the given list."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        lsElement.SetFocus
'        Exit Sub
'    End If
'
'    SaveAll = False
'    ReDim Preserve ModNetReq(1 To FcstPds)
'
'    If SaveAll = False Then
'        If RunAdjSysNetReq = True Then
'            CalculateAdjNetReq
'            RunAdjSysNetReq = False
'        End If
'        'Update the Forecast xArray
'        'iSRow = 0
'        RowStartIndex = xMainArray.LowerBound(1)
'        RowEndIndex = xMainArray.UpperBound(1)
'        ColStartIndex = xMainArray.LowerBound(2)
'        ColEndIndex = xMainArray.UpperBound(2)
'
'        For irow = AdjSysFcstPosition To RowEndIndex Step ElementsSelected
'            For iCol = 13 To ColEndIndex - 2
'                If Fcsttype = "FCST" Then
'                    xFcstRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray.Value(irow, iCol)
'                    xFcstNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray.Value(irow - 1, iCol)
'                ElseIf Fcsttype = "BDGT" Then
'                    xBudgetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray.Value(irow, iCol)
'                    xBudgetNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray.Value(irow - 1, iCol)
'                ElseIf Fcsttype = "DRFT" Then
'                    xDraftRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray.Value(irow, iCol)
'                    xDraftNetRepos.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray.Value(irow - 1, iCol)
'                End If
'            Next iCol
'        Next irow
'        'skip_this:
'        'Insert/Update header
'        AIM_ForecastRepository_Save_Sp.Parameters("@FcstId").Value = FcstId
'        AIM_ForecastRepository_Save_Sp.Parameters("@Fcstdesc").Value = FcstDesc
'        AIM_ForecastRepository_Save_Sp.Parameters("@Fcsttype").Value = Fcsttype
'        AIM_ForecastRepository_Save_Sp.Parameters("@Fcststartdate").Value = FcstStartDate
'        AIM_ForecastRepository_Save_Sp.Parameters("@Fcstinterval").Value = FcstInterval
'        AIM_ForecastRepository_Save_Sp.Parameters("@Fcstcomment").Value = txtFcstComments.Text
'        AIM_ForecastRepository_Save_Sp.Parameters("@userid").Value = gUserID
'        AIM_ForecastRepository_Save_Sp.Parameters("@RepositoryKey").Value = "0"
'
'        AIM_ForecastRepository_Save_Sp.Execute
'
'        If Fcsttype = "FCST" Then
'            SaveToRepository xFcstRepos, xFcstNetRepos
'        ElseIf Fcsttype = "BDGT" Then
'            SaveToRepository xBudgetRepos, xBudgetNetRepos
'        ElseIf Fcsttype = "DRFT" Then
'            SaveToRepository xDraftRepos, xDraftNetRepos
'        End If
'
'    ElseIf SaveAll = True Then
'        'The way the function is return it will never enter this section srinivas
'        If Fcsttype = "FCST" Then
'            PopulateReposFromMain xFcstRepos, xFcstNetRepos
'        ElseIf Fcsttype = "BDGT" Then
'            PopulateReposFromMain xBudgetRepos, xBudgetNetRepos
'        ElseIf Fcsttype = "DRFT" Then
'            PopulateReposFromMain xDraftRepos, xDraftNetRepos
'        End If
'
'    End If
'
'    'GetAdjSysFcstAndNetReq = True
'    GetAdjSysFcstAndNetReq = False
'    FillGrid
'    Me.dgItems.bookmark = ItStatBookMark
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        Err.Raise Err.Number, Err.Source, Err.Description & "(SaveForecast)"
'    End If
'End Sub
'
'Private Sub SetLocItem(ByVal xForeCast As XArrayDB, LocCol As Integer, ItemCol As Integer, DescCol As Integer)
'On Error GoTo ErrorHandler
'
'    Dim FcstRow As Long
'    Dim curLoc As String
'    Dim curItem As String
'    Dim curItemDesc As String
'    Dim LocItemRow As Long
'    Dim LocFound As Boolean
'    Dim ItemFound As Boolean
'    Dim LocCnt As Integer
'    Dim ItemCnt As Long
'    Dim ForeRowStartIndex As Long
'    Dim ForeRowEndIndex As Long
'    Dim LocRowStartIndex As Long
'    Dim LocRowEndIndex As Long
'
'    LocCnt = 0
'    ItemCnt = 0
'    Me.dcAIMLocations.RemoveAll
'    Me.dcAIMItem.RemoveAll
'    xLocItem.Clear
'    If xForeCast.Count(1) = 0 Then
'        Exit Sub
'    End If
'    xLocItem.ReDim 0, xForeCast.UpperBound(1), 0, 2
'
'    xForeCast.QuickSort 0, xForeCast.UpperBound(1), DescCol, XORDER_ASCEND, XTYPE_STRING, LocCol, XORDER_ASCEND, XTYPE_STRING
'
'    Me.dcAIMLocations.AddItem "All"
'    Me.dcAIMItem.AddItem "All" + vbTab + "All Items"
'
'    ForeRowStartIndex = xForeCast.LowerBound(1)
'    ForeRowEndIndex = xForeCast.UpperBound(1)
'
'    For FcstRow = ForeRowStartIndex To ForeRowEndIndex
'        curLoc = xForeCast(FcstRow, LocCol)
'        curItem = xForeCast(FcstRow, ItemCol)
'        curItemDesc = xForeCast(FcstRow, DescCol)
'
'        LocFound = False
'        ItemFound = False
'
'        LocRowStartIndex = xLocItem.LowerBound(1)
'        LocRowEndIndex = xLocItem.UpperBound(1)
'
'        For LocItemRow = LocRowStartIndex To LocRowEndIndex
'            If xLocItem(LocItemRow, 0) = curLoc Then
'                LocFound = True
'                Exit For
'            End If
'        Next LocItemRow
'
'         For LocItemRow = LocRowStartIndex To LocRowEndIndex
'            If xLocItem(LocItemRow, 1) = curItem Then
'                ItemFound = True
'                Exit For
'            End If
'        Next LocItemRow
'
'        If Not LocFound Then
'            Me.dcAIMLocations.AddItem curLoc
'            xLocItem(ItemCnt, 0) = curLoc 'add new location to xArray
'            LocCnt = ItemCnt + 1
'        End If
'        If Not ItemFound Then
'            Me.dcAIMItem.AddItem curItem + vbTab + curItemDesc
'            xLocItem(ItemCnt, 1) = curItem 'add new item to xArray
'            xLocItem(ItemCnt, 2) = curItemDesc
'            ItemCnt = ItemCnt + 1
'        End If
'
'    Next FcstRow
'
'    xForeCast.QuickSort 0, xForeCast.UpperBound(1), 0, XORDER_ASCEND, XTYPE_STRING, 1, XORDER_ASCEND, XTYPE_STRING
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SetLocItem)"
'End Sub
'
'Private Sub atbFcstModification_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    'Load Item Data Variables
'    Dim AdjustPct As Double
'    Dim ApplyTrend As Integer
'    Dim FcstLevel As Integer
'    Dim FcstUnit As AIM_FORECASTUNITS
'    Dim i As Long
'    Dim Interval As AIM_INTERVALS
'    Dim NetRqmts As Boolean
'    Dim strSQL As String
'    Dim StartDate As Date
'    'Dim starttime As Double
'    Dim strMessage As String
'    Dim arrItems As New XArrayDB
'    Dim RtnCode As Long
'
'    Screen.MousePointer = vbHourglass
'
'    'Clear messages
'    Write_Message ""
'
'    Select Case Tool.ID
'        Case "ID_Load"
'            strMessage = getTranslationResource("STATMSG06901")
'            If StrComp(strMessage, "STATMSG06901") = 0 Then strMessage = "Loading Item Data for Forecast Modification..."
'            Write_Message strMessage
'
'            If Trim(dcFcstId.Text) = "" Or Trim(dcFcstType.Text) = "" Then
'                strMessage = getTranslationResource("MSGBOX06902")
'                If StrComp(strMessage, "MSGBOX06902") = 0 Then strMessage = "Please provide a valid Forecast ID with a valid Forecast Type."
'                MsgBox strMessage, vbOKOnly, Me.Caption
'                Screen.MousePointer = vbNormal
'                Exit Sub
'            End If
'
'            If xRowChange.UpperBound(1) > 0 Then
'                strMessage = getTranslationResource("MSGBOX06900")
'                If StrComp(strMessage, "MSGBOX06900") = 0 Then strMessage = "Abandon changes to current record?"
'                RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
'                If RtnCode = vbNo Then
'                    Screen.MousePointer = vbNormal
'                    Exit Sub
'                End If
'            End If
'
'            'Sri batch fcst begin
'            '
'            If ckGetFromBatch.Value = vbChecked Then
'                If BatchAdjSysFcst = False Or BatchAdjSysNet = False Then
'                    strMessage = getTranslationResource("MSGBOX06911")
'                    If StrComp(strMessage, "MSGBOX06911") = 0 Then strMessage = "Batch data is not available, do you want to get dynamically?"
'                    RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
'                    If RtnCode = vbNo Then
'                        Screen.MousePointer = vbNormal
'                        Exit Sub
'                    Else
'                        ckGetFromBatch.Value = vbUnchecked
'                    End If
'                End If
'            End If
'            If ckGetFromBatch.Value = vbChecked Then
'                GetBatchdata = True
'            Else
'                GetBatchdata = False
'            End If
'            'Sri batch fcst end
'
'            Fcsttype = dcFcstType.Columns(0).Value
'
'            'Get Access Type for the current User
'            With AIM_ForecastUserAccess_Validate_Sp
'                .Parameters("@UserId").Value = gUserID
'                .Parameters("@FcstId").Value = dcFcstId.Text
'                .Parameters("@FcstType").Value = Fcsttype
'                .Parameters("@AccessType").Value = 0
'
'                .Execute
'
'                If .Parameters("@Return_Value") = 0 Then
'                    AccessType = .Parameters("@AccessType")
'                Else
'                    AccessType = 0
'                End If
'            End With
'
'            If AccessType = 0 Then
'                strMessage = getTranslationResource("MSGBOX06903")
'                If StrComp(strMessage, "MSGBOX06903") = 0 Then strMessage = "Insufficient user access rights to the Forecast. Operation aborted."
'                RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'                Screen.MousePointer = vbNormal
'                Exit Sub
'            End If
'            'Get the Forecast Record
'            Set xMainArray = Nothing
'            Set xSumArray = Nothing
'
'            GetForecastRcd Me.dcFcstId.Text
'
'            Initialize
'            iColUnit = 0
'            ParseElementsList
'            GetSysAndReposData
'            GetAdjSysFcstAndNetReq = True
'            GetSalesLY = True
'            FillGrid
'
'            Me.atbFcstModification.Tools(3).Enabled = True
'            Me.atbFcstModification.Tools("ID_Copy").Enabled = True
'
'            SetLocItem xForeCast, 0, 1, 2
'
'            strMessage = getTranslationResource("STATMSG06902")
'            If StrComp(strMessage, "STATMSG06902") = 0 Then strMessage = " records loaded."
'            Write_Message Format((xMainArray.Count(1) - 1) / ElementsSelected, "0") & _
'                    " " & strMessage
'
'        Case "ID_Save"
'            SaveForecast
'
'        Case "ID_Update"
'            'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "ApplyFilter start " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'            ApplyFilter
'           'Write_Log "C:\AIM_Changes\Batch_Forecasting\Aim4_2_4_4_diff/timing4.4", "ApplyFilter End " & CStr(Format(Now(), "hh:mm:ss")), 1, False, False, False, False
'
'        Case "ID_LookUp"
'            Screen.MousePointer = vbNormal
'            GetForecastRcd Me.dcFcstId.Text
'
'            CallItemFilter Cn, m_FcstItemFilter, True, arrItems _
'                , True  'A.Stocksdale - Remove this once forecast mod has been upgraded/removed
'
'            If Not AIM_ItemFilter.r_CancelFlag Then
'                SetLocItem arrItems, 1, 0, 2
'                Me.txtLocation.Text = m_FcstItemFilter.SubFilterLcID
'                Me.txtItemID.Text = m_FcstItemFilter.SubFilterItem
'                Me.txtVendorID.Text = m_FcstItemFilter.SubFilterVnId
'                Me.txtAssort.Text = m_FcstItemFilter.SubFilterAssort
'                Me.txtBuyerID.Text = m_FcstItemFilter.SubFilterById
'                Me.txtItemStatus.Text = m_FcstItemFilter.SubFilterItemStatus
'                Me.txtClass1.Text = m_FcstItemFilter.SubFilterClass1
'                Me.txtClass2.Text = m_FcstItemFilter.SubFilterClass2
'                Me.txtClass3.Text = m_FcstItemFilter.SubFilterClass3
'                Me.txtClass4.Text = m_FcstItemFilter.SubFilterClass4
'
''                If UCase(m_FcstItemFilter.SubFilterLcID) <> UCase(rsForecast("LcID").Value) _
''                And Trim(rsForecast("LcID").Value) <> "" Then
''                    txtLocation.Text = rsForecast("LcID").Value
''                    m_FcstItemFilter.SubFilterLcID = rsForecast("LcID").Value
''                Else
''                    txtLocation.Text = m_FcstItemFilter.SubFilterLcID
''                    If m_FcstItemFilter.SubFilterLcID <> rsForecast("LcID").Value Then
''                        txtLocation.ForeColor = vbRed
''                    End If
''                End If
''
''                If UCase(m_FcstItemFilter.SubFilterItem) <> UCase(rsForecast("Item").Value) And Trim(rsForecast("Item").Value) <> "" Then
''                    txtItemID.Text = rsForecast("Item").Value
''                    m_FcstItemFilter.SubFilterItem = rsForecast("Item").Value
''                    txtItemID.ForeColor = vbBlue
''                Else
''                    txtItemID.Text = m_FcstItemFilter.SubFilterItem
''                    If m_FcstItemFilter.SubFilterItem <> rsForecast("Item").Value Then
''                        txtItemID.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterVnID) <> UCase(rsForecast("VnID").Value) And Trim(rsForecast("VnID").Value) <> "" Then
''                    txtVendorID.Text = rsForecast("VnID").Value
''                    m_FcstItemFilter.SubFilterVnID = rsForecast("VnID").Value
''                Else
''                    txtVendorID.Text = m_FcstItemFilter.SubFilterVnID
''                    If m_FcstItemFilter.SubFilterVnID <> rsForecast("VnID").Value Then
''                        txtVendorID.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterByID) <> UCase(rsForecast("ByID").Value) And Trim(rsForecast("ByID").Value) <> "" Then
''                    txtBuyerID.Text = rsForecast("ByID").Value
''                    m_FcstItemFilter.SubFilterByID = rsForecast("ByID").Value
''                Else
''                    txtBuyerID.Text = m_FcstItemFilter.SubFilterByID
''                    If m_FcstItemFilter.SubFilterByID <> rsForecast("ByID").Value Then
''                        txtBuyerID.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterItemStatus) <> UCase(rsForecast("ItStat").Value) And Trim(rsForecast("Itstat").Value) <> "" Then
''                    txtItemStatus.Text = rsForecast("ItStat").Value
''                    m_FcstItemFilter.SubFilterItemStatus = rsForecast("ItStat").Value
''                Else
''                    txtItemStatus.Text = m_FcstItemFilter.SubFilterItemStatus
''                    If m_FcstItemFilter.SubFilterItemStatus <> rsForecast("ItStat").Value Then
''                        txtItemStatus.ForeColor = vbRed
''                    End If
''                End If
''
''                If UCase(m_FcstItemFilter.SubFilterClass1) <> UCase(rsForecast("Class1").Value) And Trim(rsForecast("class1").Value) <> "" Then
''                    txtClass1.Text = rsForecast("Class1").Value
''                    m_FcstItemFilter.SubFilterClass1 = rsForecast("Class1").Value
''                Else
''                    txtClass1.Text = m_FcstItemFilter.SubFilterClass1
''                    If m_FcstItemFilter.SubFilterClass1 <> rsForecast("Class1").Value Then
''                        txtClass1.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterClass2) <> UCase(rsForecast("Class2").Value) And Trim(rsForecast("class2").Value) <> "" Then
''                    txtClass2.Text = rsForecast("Class2").Value
''                    m_FcstItemFilter.SubFilterClass2 = rsForecast("Class2").Value
''                Else
''                    txtClass2.Text = m_FcstItemFilter.SubFilterClass2
''                    If m_FcstItemFilter.SubFilterClass2 <> rsForecast("Class2").Value Then
''                        txtClass2.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterClass3) <> UCase(rsForecast("Class3").Value) And Trim(rsForecast("class3").Value) <> "" Then
''                    txtClass3.Text = rsForecast("Class3").Value
''                    m_FcstItemFilter.SubFilterClass3 = rsForecast("Class3").Value
''                Else
''                    txtClass3.Text = m_FcstItemFilter.SubFilterClass3
''                    If m_FcstItemFilter.SubFilterClass3 <> rsForecast("Class3").Value Then
''                        txtClass3.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterClass4) <> UCase(rsForecast("Class4").Value) And Trim(rsForecast("class4").Value) <> "" Then
''                    txtClass4.Text = rsForecast("Class4").Value
''                    m_FcstItemFilter.SubFilterClass4 = rsForecast("Class4").Value
''                Else
''                    txtClass4.Text = m_FcstItemFilter.SubFilterClass4
''                    If m_FcstItemFilter.SubFilterClass4 <> rsForecast("Class4").Value Then
''                        txtClass4.ForeColor = vbRed
''                    End If
''                End If
''                If UCase(m_FcstItemFilter.SubFilterAssort) <> UCase(rsForecast("Assort").Value) And Trim(rsForecast("Assort").Value) <> "" Then
''                    txtAssort.Text = rsForecast("Assort").Value
''                    m_FcstItemFilter.SubFilterAssort = rsForecast("Assort").Value
''                Else
''                    txtAssort.Text = m_FcstItemFilter.SubFilterAssort
''                    If m_FcstItemFilter.SubFilterAssort <> rsForecast("assort").Value Then
''                        txtAssort.ForeColor = vbRed
''                    End If
''                End If
'            Else
'                With m_FcstItemFilter
'                    .LcID = Me.txtLocation.Text
'                    .Item = Me.txtItemID.Text
'                    .VnId = Me.txtVendorID.Text
'                    .ById = Me.txtBuyerID.Text
'                    .ItemStatus = Me.txtItemStatus.Text
'                    .Class1 = Me.txtClass1.Text
'                    .Class2 = Me.txtClass2.Text
'                    .Class3 = Me.txtClass3.Text
'                    .Class4 = Me.txtClass4.Text
'                    .Assort = Me.txtAssort.Text
'
'                    .SubFilterLcID = Me.txtLocation.Text
'                    .SubFilterItem = Me.txtItemID.Text
'                    .SubFilterVnId = Me.txtVendorID.Text
'                    .SubFilterById = Me.txtBuyerID.Text
'                    .SubFilterItemStatus = Me.txtItemStatus.Text
'                    .SubFilterClass1 = Me.txtClass1.Text
'                    .SubFilterClass2 = Me.txtClass2.Text
'                    .SubFilterClass3 = Me.txtClass3.Text
'                    .SubFilterClass4 = Me.txtClass4.Text
'                    .SubFilterAssort = Me.txtAssort.Text
'                End With
'            End If
'
'        Case "ID_Clear"
'            With m_FcstItemFilter
'                .LcID = rsForecast("LcID").Value
'                .Item = rsForecast("Item").Value
'                .VnId = rsForecast("VnID").Value
'                .ById = rsForecast("ByID").Value
'                .ItemStatus = rsForecast("ItStat").Value
'                .Class1 = rsForecast("Class1").Value
'                .Class2 = rsForecast("Class2").Value
'                .Class3 = rsForecast("Class3").Value
'                .Class4 = rsForecast("Class4").Value
'                .Assort = rsForecast("Assort").Value
'
'                .SubFilterLcID = rsForecast("LcID").Value
'                .SubFilterItem = rsForecast("Item").Value
'                .SubFilterVnId = rsForecast("VnID").Value
'                .SubFilterById = rsForecast("ByID").Value
'                .SubFilterItemStatus = rsForecast("ItStat").Value
'                .SubFilterClass1 = rsForecast("Class1").Value
'                .SubFilterClass2 = rsForecast("Class2").Value
'                .SubFilterClass3 = rsForecast("Class3").Value
'                .SubFilterClass4 = rsForecast("Class4").Value
'                .SubFilterAssort = rsForecast("Assort").Value
'
'                Me.txtLocation.Text = .SubFilterLcID
'                Me.txtItemID.Text = .SubFilterItem
'                Me.txtVendorID.Text = .SubFilterVnId
'                Me.txtBuyerID.Text = .SubFilterById
'                Me.txtBuyerID.Text = .SubFilterById
'                Me.txtClass1.Text = .SubFilterClass1
'                Me.txtClass2.Text = .SubFilterClass2
'                Me.txtClass3.Text = .SubFilterClass3
'                Me.txtClass4.Text = .SubFilterClass4
'                Me.txtAssort.Text = .SubFilterAssort
'            End With
'        Case "ID_SetBookMark"
'            ItStatBookMark = Me.dgItems.bookmark
'            Me.atbFcstModification.Tools("ID_GoToBookMark").Enabled = True
'
'        Case "ID_GoToBookMark"
'            Me.dgItems.bookmark = ItStatBookMark
'
'        Case "ID_Copy"
'            AIM_CopyForecast.Show vbModal, AIM_Main
'
'        Case "ID_Close"
'            Unload Me
'            Screen.MousePointer = vbNormal
'            Exit Sub
'        Case "ID_UpdateNet"
'            If RunAdjSysNetReq = True Then
'                CalculateAdjNetReq
'                RunAdjSysNetReq = False
'            End If
'        Case "ID_ClearAdj"
'            ClearAdjustments
'    End Select
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(atbForecast_ToolClick)"
'End Sub
'
'Private Sub ClearAdjustments()
'On Error GoTo ErrorHandler
'
''Kevin (adding new function to allow users to clear any previous adjustments made for the selected
''items and periods on the Item grid
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim iCrow As Long
'    'Dim rsFreezeItem As New ADODB.Recordset
'    Dim FreezeItem As String
'    Dim FreezedPds As Integer
'    Dim Percentage As Double
'    Dim TodayDate As Date
'    Dim Pds As Integer
'    Dim StartDate As Date
'    Dim Interval As AIM_INTERVALS
'    Dim FirstDate As Date
'    Dim CurDate As Date
'    Dim NextDate As Date
'    Dim DateCnt As Integer
'    Dim StartPds As Integer
'    Dim ModNetReq() As Double
'    Dim iSRow As Long
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'    Dim NetRowEndIndex As Long
'    Dim NetColEndIndex As Integer
'    Dim AdjSysFcstSum As Long
'    'Kevin (clearing adjustment variables)
'    Dim sfirow As Long      '(system forecast irow)
'    Dim RtnCode As Integer
'
'    'Kevin (start)
'    'Check to see if there is any data in the XmainArray
'    'If there is no date then there is nothing to delete
'    If xMainArray.Count(1) = 0 Or xMainArray.Count(2) = 0 Then
'      Exit Sub
'    End If
'
'
'    'Firstly put warning in that User is about to clear all forecast adjustments displayed
'    strMessage = getTranslationResource("MSGBOX06909")
'    If StrComp(strMessage, "MSGBOX06909") = 0 Then
'        strMessage = "This will remove all user adjustments and revert to the System Forecast (and System Net Requirements) for all selected items and periods. Do you wish to continue?"
'        RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
'        If RtnCode = vbNo Then
'            Exit Sub
'        End If
'    End If
'
'    'If ok to proceed then check that the SysFcst is displayed
'    If SysFcstPosition < 1 Then
'        'Put error message here that System Forecast must be selected
'        strMessage = getTranslationResource("MSGBOX06910")
'        If StrComp(strMessage, "MSGBOX06910") = 0 Then strMessage = "The Adjustments cannot be cleared unless the System Forecast row is selected."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        Exit Sub
'    End If
'
'    'Get the row of the Sys Fcst so we can use the value in that row (reverting to system fcst)
'    sfirow = SysFcstPosition
'    'Kevin (end)
'
'    Screen.MousePointer = vbHourglass
'    RunAdjSysNetReq = True
'
'    ReDim Preserve ModNetReq(1 To FcstPds)
'
'    RowEndIndex = xMainArray.UpperBound(1)
'    ColEndIndex = xMainArray.UpperBound(2)
'
'    Pds = txtFcstPds.Text
'    StartDate = Format(CDate(txtFcstStartDate.Text), "yyyy/mm/dd")
'    Interval = rsForecast("fcstinterval").Value
'    Percentage = txtAdjustPct.Text
'    TodayDate = Format(Now(), "yyyy/mm/dd")
'
'    For irow = AdjSysFcstPosition To RowEndIndex Step ElementsSelected
'        'Check for freeze Location/Item
'
'        AIM_Item_GetEq_Sp.Parameters("@LcIDkey").Value = xMainArray(irow, 0)
'        AIM_Item_GetEq_Sp.Parameters("@ItemKey").Value = xMainArray(irow, 1)
'
'        If Not f_IsRecordsetValidAndOpen(rsFreezeItem) Then
'            rsFreezeItem.Open AIM_Item_GetEq_Sp
'        Else
'            rsFreezeItem.Requery
'        End If
'
'        If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
'            FreezeItem = rsFreezeItem("Freeze_Forecast").Value
'        End If
'
'        'current period to freeze, then only allow.
'        If UCase(FreezeItem) = "N" Then
'            AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@FcstId").Value = FcstId
'            AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@LcID").Value = xMainArray(irow, 0)
'            AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Item").Value = xMainArray(irow, 1)
'
'            AIM_ForecastFreezeItem_GetEq_Sp.Execute
'
'            If AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Return_Value").Value = -1 Then
'                'Check for freeze period
'                AIM_ForecastFreezePds_Get_Sp.Parameters("@FcstId").Value = FcstId
'                AIM_ForecastFreezePds_Get_Sp.Parameters("@LcID").Value = xMainArray(irow, 0)
'
'                AIM_ForecastFreezePds_Get_Sp.Execute
'
'                If AIM_ForecastFreezePds_Get_Sp.Parameters("@Return_Value").Value = 0 Then
'                    FreezedPds = AIM_ForecastFreezePds_Get_Sp.Parameters("@FreezePds").Value
'                Else
'                    FreezedPds = 0
'                End If
'
'                AdjSysFcstSum = 0
'
'                For iCol = 14 To 14 + CurrentPd + FreezedPds - 2
'                    AdjSysFcstSum = AdjSysFcstSum + xMainArray(irow, iCol)
'                    Next iCol
'
'                    For iCol = 14 + CurrentPd + FreezedPds To ColEndIndex - 1
'
'                    If Me.dgItems.Columns(iCol).Visible = True Then   'And CDate(Me.dgItems.Columns(iCol).Caption) >= TodayDate Then
'                        iCrow = xRowChange.UpperBound(1)
'                        xRowChange.AppendRows (1)
'                        xRowChange.Value(iCrow, 0) = irow
'                        xRowChange.Value(iCrow, 1) = iCol
'                        xRowChange.Value(iCrow, 2) = xMainArray(irow, iCol)
'                        'Kevin (start) - If Adj Sys Fcst <> Sys Fcst then make it equal and update the modified column to 'Y'
'                        If xMainArray(irow, iCol) <> xMainArray(sfirow, iCol) Then
'                            xMainArray(irow, iCol) = xMainArray(sfirow, iCol)
'                            'Set the modified column so need to save this row
'                            xMainArray.Value(irow, ColEndIndex - 2) = "Y"
'                            'Set that we need to recalc the net req also
'                            RunAdjSysNetReq = True
'                        End If
'                        'Kevin (end)
'                        AdjSysFcstSum = AdjSysFcstSum + xMainArray(irow, iCol)
'
'                        xAdjForeCast.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                        If Fcsttype = "FCST" Then
'                            xFcstRepos.Value(xMainArray(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                        ElseIf Fcsttype = "BDGT" Then
'                            xBudgetRepos.Value(xMainArray(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                        ElseIf Fcsttype = "DRFT" Then
'                            xDraftRepos.Value(xMainArray(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                        End If
'
'                    End If
'                Next iCol
'                xMainArray(irow, 13) = AdjSysFcstSum
'                AdjSysFcstSum = 0
'                strMessage = getTranslationResource("STATMSG06904")
'                If StrComp(strMessage, "STATMSG06904") = 0 Then strMessage = "Clearing Forecast Adjustments..."
'                Write_Message strMessage & " " & Trim(xMainArray(irow, 0)) & getTranslationResource("/") & xMainArray(irow, 1)
'
'            End If
'
'        End If
'        'Kevin (start) need to increment SysFcst row to next item
'        sfirow = sfirow + ElementsSelected
'        'Kevin (end)
'    Next irow
'
'    'If have updated the system forecast then we need to recalc the Net Req also
'    If RunAdjSysNetReq = True Then
'        CalculateAdjNetReq
'        RunAdjSysNetReq = False
'    End If
'
'    Write_Message ""
'    dgItems.ReBind
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(ClearAdjustments)"
'    End If
'End Sub
'Private Sub ckGetFromBatch_Click()
''Sri batch fcst begin
''Sri batch fcst end
'End Sub
'
'
'Private Sub cmApplyPcnt_Click()
'On Error GoTo ErrorHandler
'
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim iCrow As Long
'    'Dim rsFreezeItem As New ADODB.Recordset
'    Dim FreezeItem As String
'    Dim FreezedPds As Integer
'    Dim Percentage As Double
'    Dim TodayDate As Date
'    Dim Pds As Integer
'    Dim StartDate As Date
'    Dim Interval As AIM_INTERVALS
'    Dim FirstDate As Date
'    Dim CurDate As Date
'    Dim NextDate As Date
'    Dim DateCnt As Integer
'    Dim StartPds As Integer
'    Dim ModNetReq() As Double
'    Dim iSRow As Long
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'    Dim NetRowEndIndex As Long
'    Dim NetColEndIndex As Integer
'    Dim AdjSysFcstSum As Long
'
'
'    Screen.MousePointer = vbHourglass
'    RunAdjSysNetReq = True
'
'    ReDim Preserve ModNetReq(1 To FcstPds)
'    RowEndIndex = xMainArray.UpperBound(1)
'    ColEndIndex = xMainArray.UpperBound(2)
'
'    Pds = txtFcstPds.Text
'    StartDate = Format(CDate(txtFcstStartDate.Text), "yyyy/mm/dd")
'    Interval = rsForecast("fcstinterval").Value
'    Percentage = txtAdjustPct.Text
'    TodayDate = Format(Now(), "yyyy/mm/dd")
'
'    If Percentage <> 0 Then
'        For irow = AdjSysFcstPosition To RowEndIndex Step ElementsSelected
'            'Check for freeze Location/Item
'
'            AIM_Item_GetEq_Sp.Parameters("@LcIDkey").Value = xMainArray(irow, 0)
'            AIM_Item_GetEq_Sp.Parameters("@ItemKey").Value = xMainArray(irow, 1)
'
'            If Not f_IsRecordsetValidAndOpen(rsFreezeItem) Then
'                rsFreezeItem.Open AIM_Item_GetEq_Sp
'            Else
'                rsFreezeItem.Requery
'            End If
''            If f_IsRecordsetValidAndOpen(rsFreezeItem) Then rsFreezeItem.Close
''            Set rsFreezeItem = Nothing
''            Set rsFreezeItem = New ADODB.Recordset
''            rsFreezeItem.Open AIM_Item_GetEq_Sp
'
'            If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
'                FreezeItem = rsFreezeItem("Freeze_Forecast").Value
'            End If
'
'            'current period to freeze, then only allow.
'            If UCase(FreezeItem) = "N" Then
'                AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@FcstId").Value = FcstId
'                AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@LcID").Value = xMainArray(irow, 0)
'                AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Item").Value = xMainArray(irow, 1)
'
'                AIM_ForecastFreezeItem_GetEq_Sp.Execute
'
'                If AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Return_Value").Value = -1 Then
'                    'Check for freeze period
'                    AIM_ForecastFreezePds_Get_Sp.Parameters("@FcstId").Value = FcstId
'                    AIM_ForecastFreezePds_Get_Sp.Parameters("@LcID").Value = xMainArray(irow, 0)
'
'                    AIM_ForecastFreezePds_Get_Sp.Execute
'
'                    If AIM_ForecastFreezePds_Get_Sp.Parameters("@Return_Value").Value = 0 Then
'                        FreezedPds = AIM_ForecastFreezePds_Get_Sp.Parameters("@FreezePds").Value
'                    Else
'                        FreezedPds = 0
'                    End If
'
''                    For iCol = 1 To FcstPds
''                        ModNetReq(iCol) = 0
''                    Next iCol
'                    'Sri code change to save only modified and new rows begin
'                    'Set the modified column to Y
'                    xMainArray.Value(irow, ColEndIndex - 2) = "Y"
'                    'Sri code change to save only modified and new rows end
'                    AdjSysFcstSum = 0
'                    For iCol = 14 To 14 + CurrentPd + FreezedPds - 1
'                            AdjSysFcstSum = AdjSysFcstSum + xMainArray(irow, iCol)
'                    Next iCol
'
'                     For iCol = 14 + CurrentPd + FreezedPds To ColEndIndex - 1
'
'                        If Me.dgItems.Columns(iCol).Visible = True Then   'And CDate(Me.dgItems.Columns(iCol).Caption) >= TodayDate Then
'                            iCrow = xRowChange.UpperBound(1)
'                            xRowChange.AppendRows (1)
'                            xRowChange.Value(iCrow, 0) = irow
'                            xRowChange.Value(iCrow, 1) = iCol
'                            xRowChange.Value(iCrow, 2) = xMainArray(irow, iCol)
''***************************xMainArray(irow, iCol) = xMainArray(irow, iCol) + (xMainArray(irow, iCol)) * (Percentage / 100)
'                            xMainArray(irow, iCol) = Round(xMainArray(irow, iCol) + (xMainArray(irow, iCol)) * (Percentage / 100), 0)
'                            AdjSysFcstSum = AdjSysFcstSum + xMainArray(irow, iCol)
'
'                            xAdjForeCast.Value(xMainArray.Value(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                            If Fcsttype = "FCST" Then
'                                xFcstRepos.Value(xMainArray(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                            ElseIf Fcsttype = "BDGT" Then
'                                xBudgetRepos.Value(xMainArray(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                            ElseIf Fcsttype = "DRFT" Then
'                                xDraftRepos.Value(xMainArray(irow, ColEndIndex - 1), iCol - 1) = xMainArray(irow, iCol)
'                            End If
'                            'Sri code change fixed bug Modnetreq is the diff between Adj_sys_fcst -Sys_fcst
'                            'ModNetReq(iCol - 13) = xMainArray(irow, iCol) - xMainArray(irow + 1, iCol)
'                            'ModNetReq(iCol - 13) = xMainArray(irow, iCol) - xMainArray(irow + 2, iCol)
'                        End If
'                    Next iCol
'                    xMainArray(irow, 13) = AdjSysFcstSum
'                    AdjSysFcstSum = 0
'                    strMessage = getTranslationResource("STATMSG06903")
'                    If StrComp(strMessage, "STATMSG06903") = 0 Then strMessage = "Applying Percentage Change..."
'                    Write_Message strMessage & " " & Trim(xMainArray(irow, 0)) & getTranslationResource("/") & xMainArray(irow, 1)
'
''                    GetNetReqForAPeriod FcstId, "", xMainArray.Value(irow, 0), xMainArray.Value(irow, 1), FcstStartdate, FcstInterval, xNetReq, ModNetReq()
''
''                     NetRowEndIndex = xNetReq.UpperBound(1)
''                     NetColEndIndex = xNetReq.UpperBound(2)
''                    For iSRow = 0 To NetRowEndIndex
''                        For iCol = 13 + CurrentPd + FreezedPds To NetColEndIndex - 8
''                            xMainArray.Value(irow - 1, iCol + 1) = xNetReq.Value(iSRow, iCol)
''                            xAdjForecastNr.Value(xMainArray(irow, ColEndIndex - 1), iCol) _
''                            = xNetReq.Value(iSRow, iCol)
''                            'xAdjForecastNr.Value(xMainArray(xRowChange.Value(irow, 0), ColEndIndex - 1), iCol) _
''                            = xNetReq.Value(iSRow, iCol)
''                        Next iCol
''                    Next iSRow
'                End If
'
'                'If f_IsRecordsetValidAndOpen(rsFreezeItem) Then
'                '    rsFreezeItem.Close
'                '    Set rsFreezeItem = Nothing
'                'End If
'            End If
'        Next irow
'
'        Write_Message ""
'        txtAdjustPct.Enabled = False
'        cmApplyPcnt.Visible = False
'        cmConfirmPcnt.Visible = True
'        cmCancelPcnt.Visible = True
'        dgSummary.AllowUpdate = False
'        dgItems.AllowUpdate = False
'        Me.atbFcstModification.Tools("ID_Save").Enabled = False
'        Me.atbFcstModification.Tools("ID_Update").Enabled = False
'        Me.atbFcstModification.Tools("ID_Load").Enabled = False
'        dgItems.ReBind
'    End If
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(cmApplyPcnt_Click)"
'    End If
'End Sub
'
'Private Sub cmCancelPcnt_Click()
'On Error GoTo ErrorHandler
'    Dim RevPerc As Double
'
'    RevPerc = (100 - ((100 / (100 + txtAdjustPct.Text)) * 100)) * -1
'
'    txtAdjustPct.Value = RevPerc
'    cmApplyPcnt_Click
'
'    'Dim irow As Integer
'    'Dim iCol As Integer
'    'For irow = xRowChange.LowerBound(1) To xRowChange.UpperBound(1)
'    '    'For iCow = xRowChange.LowerBound(2) To xRowChange.UpperBound(2)
'    '        If Not IsEmpty(xRowChange.Value(irow, 0)) Then
'    '            xMainArray(xRowChange.Value(irow, 0), xRowChange.Value(irow, 1)) = xRowChange.Value(irow, 2)
'    '        End If
'    '    'Next iCow
'    'Next irow
'    Me.dgItems.ReBind
'    txtAdjustPct.Enabled = True
'    txtAdjustPct.Text = "0.00"
'    cmCancelPcnt.Visible = False
'    cmConfirmPcnt.Visible = False
'    cmApplyPcnt.Visible = True
'    dgSummary.AllowUpdate = True
'    dgItems.AllowUpdate = True
'    Me.atbFcstModification.Tools("ID_Save").Enabled = True
'    Me.atbFcstModification.Tools("ID_Update").Enabled = True
'    Me.atbFcstModification.Tools("ID_Load").Enabled = True
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(cmCancelPcnt_Click)"
'End Sub
'
'Private Sub cmConfirmPcnt_Click()
'On Error GoTo ErrorHandler
'
'    txtAdjustPct.Enabled = True
'    txtAdjustPct.Text = "0.00"
'    cmApplyPcnt.Visible = True
'    cmConfirmPcnt.Visible = False
'    cmCancelPcnt.Visible = False
'    dgSummary.AllowUpdate = True
'    dgItems.AllowUpdate = True
'    Me.atbFcstModification.Tools("ID_Save").Enabled = True
'    Me.atbFcstModification.Tools("ID_Update").Enabled = True
'    Me.atbFcstModification.Tools("ID_Load").Enabled = True
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(cmConfirmPcnt_Click)"
'End Sub
'
'Private Sub Command1_Click()
'If RunAdjSysNetReq = True Then
'    CalculateAdjNetReq
'    RunAdjSysNetReq = False
'End If
'End Sub
'
'Private Sub Command2_Click()
'
''
''    Dim CurStartdate As Date
''    Dim IndexCounter As Integer
''    Dim RowCounter As Long
''    Dim ColCounter As Integer
''    Dim ForecastTotal As Double
''    Dim ForecastNrTotal As Double
''    Dim AdjForecastTotal As Double
''    Dim AdjForecastNrTotal As Double
''    Dim PlannedRctTotal As Double
''   ' Set MyArray = xSumArray
''    'Set MyArray = xFcstRepos
''    'GetForecastMaint dcFcstId.Text, xForeCast, False, FcstStartdate, FcstInterval, True, "SysFcst", False
'''    GetSysFcst dcFcstId.Text, "DRFT", FcstStartdate, FcstInterval, xForeCast
''
''    Set MyArray = xForeCast
''    'Set MyArray = xNetReq
''
''
''
''
''    'TestPopulateForeCast
''
''
'''    Me.SSOleDBGrid1.Enabled = True
'''    'Clear any existing columns
'''    Me.SSOleDBGrid1.Columns.RemoveAll
'''    'Set up the Items Grid
'''     For IndexCounter = MyArray.LowerBound(2) To MyArray.UpperBound(2)
'''        SetColumnFormat SSOleDBGrid1, IndexCounter, Format(IndexCounter, "00"), Format(IndexCounter, "00"), 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, False, True
'''    Next IndexCounter
'''    SSOleDBGrid1.ReBind
''
''
'''   For RowCounter = MyArray.LowerBound(1) To MyArray.UpperBound(1)
'''        For ColCounter = MyArray.LowerBound(2) To MyArray.UpperBound(2)
'''            SSOleDBGrid1(RowCounter, ColCounter) = MyArray(RowCounter, ColCounter)
'''        Next
'''    Next
''
''
'''    Dim irow As Integer
'''    Dim iFRow As Integer
'''    Dim iCol As Integer
'''    Dim Qty As Integer
'''    Dim RowEndIndex As Integer
'''    Dim ColEndIndex As Integer
'''    Dim RtnCode As Integer
'''    Dim iFcol As Integer
'''    'sri
'''    Dim BatchColEndIndex As Integer
'''
'''
'''    'sri
''''    If GetBatchData = True Then
'''    RtnCode = GetSysFcst(FcstId, Fcsttype, FcstStartdate, FcstInterval, XBatchArray)
'''    RowEndIndex = XBatchArray.UpperBound(1)
'''    ColEndIndex = XBatchArray.UpperBound(2)
'''    xForeCast.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'''    xForecastNr.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'''    xAdjForeCast.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'''    xAdjForecastNr.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'''    xPlannedrct.ReDim 0, RowEndIndex, 0, (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'''     For irow = 0 To RowEndIndex
'''        For iCol = 0 To 12
'''            xForeCast.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'''            xForecastNr.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'''            xAdjForeCast.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'''            xAdjForecastNr.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'''            xPlannedrct.Value(irow, iCol) = XBatchArray.Value(irow, iCol)
'''        Next iCol
'''        iFcol = (XBatchArray.UpperBound(2) - 12 - 8) / 5 + 8 + 12
'''        For iCol = ColEndIndex To ColEndIndex - 8 Step -1
'''
'''            xForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'''            xForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'''            xAdjForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'''            xAdjForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'''            xPlannedrct.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'''            iFcol = iFcol - 1
'''        Next iCol
'''    Next irow
'''
'''    'ColEndIndex = (XBatchArray.UpperBound(2) - 12 - 8) / 5
'''    ColEndIndex = XBatchArray.UpperBound(2) - 8
'''
'''    For irow = 0 To RowEndIndex
'''            ForecastTotal = 0
'''            ForecastNrTotal = 0
'''            AdjForecastTotal = 0
'''            AdjForecastNrTotal = 0
'''            PlannedRctTotal = 0
'''
''''                If xRepos.Value(iFRow, 0) = xTempArray.Value(irow, 0) And xRepos.Value(iFRow, 1) = xTempArray.Value(irow, 1) Then
'''                    iFcol = 13
'''                    For iCol = 13 To ColEndIndex Step 5
'''                    'For iCol = 13 To ColEndIndex * 5 + 12 Step 5
'''                        xForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol)
'''                        ForecastTotal = ForecastTotal + xForeCast.Value(irow, iFcol)
'''                        xForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 1)
'''                        ForecastNrTotal = ForecastNrTotal + xForecastNr.Value(irow, iFcol)
'''                        xAdjForeCast.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 2)
'''                        AdjForecastTotal = AdjForecastTotal + xAdjForeCast.Value(irow, iFcol)
'''                        xAdjForecastNr.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 3)
'''                        AdjForecastNrTotal = AdjForecastNrTotal + xAdjForecastNr.Value(irow, iFcol)
'''                        xPlannedrct.Value(irow, iFcol) = XBatchArray.Value(irow, iCol + 4)
'''                        PlannedRctTotal = PlannedRctTotal + xPlannedrct.Value(irow, iFcol)
'''                        iFcol = iFcol + 1
'''                    Next iCol
'''                        xForeCast.Value(irow, 12) = ForecastTotal
'''                        xForecastNr.Value(irow, 12) = ForecastNrTotal
'''                        xAdjForeCast.Value(irow, 12) = AdjForecastTotal
'''                        xAdjForecastNr.Value(irow, 12) = AdjForecastNrTotal
'''                        xPlannedrct.Value(irow, 12) = PlannedRctTotal
''''                End If
'''    Next irow
''''    End If
'''
'''
'''
''
''' Set MyArray = xForeCast
''
''
''
''
''    Me.SSOleDBGrid1.Enabled = True
''   ' Clear any existing columns
''    Me.SSOleDBGrid1.Columns.RemoveAll
''   ' Set up the Items Grid
''     For IndexCounter = MyArray.LowerBound(2) To MyArray.UpperBound(2)
''        SetColumnFormat SSOleDBGrid1, IndexCounter, Format(IndexCounter, "00"), Format(IndexCounter, "00"), 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, False, True
''    Next IndexCounter
''    SSOleDBGrid1.ReBind
''
'
'End Sub
'
'
'Private Sub dcAIMItem_Click()
'On Error GoTo ErrorHandler
'    If dcAIMItem.Text <> "All" Then
'        txtItemDescription.Text = xLocItem(xLocItem.Find(0, 1, dcAIMItem.Text, , , XTYPE_STRINGCASESENSITIVE), 2)
'    Else
'        txtItemDescription.Text = "All Items"
'    End If
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAIMItem_Click)"
'End Sub
'
'Private Sub dcAIMItem_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcAIMItem.Columns(0).Name = "Item"
'    Me.dcAIMItem.Columns(0).Caption = getTranslationResource("Item ID")
'    Me.dcAIMItem.Columns(0).Width = 1300
'    Me.dcAIMItem.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcAIMItem.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcAIMItem.Columns(0).DataField = "Item"
'
'    Me.dcAIMItem.Columns(1).Name = "desc"
'    Me.dcAIMItem.Columns(1).Caption = getTranslationResource("Description")
'    Me.dcAIMItem.Columns(1).Width = 2800
'    Me.dcAIMItem.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcAIMItem.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dcAIMItem.Columns(1).DataField = "desc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcAIMItem, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcAIMItem.Columns.Count - 1
''        dcAIMItem.Columns(IndexCounter).HasHeadBackColor = True
''        dcAIMItem.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''   Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAIMItem_InitColumnProps)"
'End Sub
'
'Private Sub dcAIMLocations_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcAIMLocations.Columns(0).Name = "LcID"
'    Me.dcAIMLocations.Columns(0).Caption = getTranslationResource("Location ID")
'    Me.dcAIMLocations.Columns(0).Width = 1200
'    Me.dcAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcAIMLocations.Columns(0).DataField = "LcID"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcAIMLocations, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcAIMLocations.Columns.Count - 1
''       dcAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
''        dcAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAIMLocations_InitColumnProps)"
'End Sub
'
'Private Sub dcFcstId_Click()
'On Error GoTo ErrorHandler
'
'    'sri batch fcst begin
'    GetBatchStatus
'    'sri batch fcst end
'    dcFcstId_Change
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcFcstId_Click)"
'End Sub
'
'Private Sub dcFcstId_Change()
'On Error GoTo ErrorHandler
'
'    'Reset global variables
'    With m_FcstItemFilter
'        .LcID = ""
'        .Item = ""
'        .VnId = ""
'        .ById = ""
'        .ItemStatus = ""
'        .Class1 = ""
'        .Class2 = ""
'        .Class3 = ""
'        .Class4 = ""
'        .Assort = ""
'
'        .SubFilterLcID = ""
'        .SubFilterItem = ""
'        .SubFilterVnId = ""
'        .SubFilterById = ""
'        .SubFilterItemStatus = ""
'        .SubFilterClass1 = ""
'        .SubFilterClass2 = ""
'        .SubFilterClass3 = ""
'        .SubFilterClass4 = ""
'        .SubFilterAssort = ""
'
'        Me.txtLocation.Text = .SubFilterLcID
'        Me.txtItemID.Text = .SubFilterItem
'        Me.txtVendorID.Text = .SubFilterVnId
'        Me.txtBuyerID.Text = .SubFilterById
'        Me.txtItemStatus.Text = .SubFilterItemStatus
'        Me.txtClass1.Text = .SubFilterClass1
'        Me.txtClass2.Text = .SubFilterClass2
'        Me.txtClass3.Text = .SubFilterClass3
'        Me.txtClass4.Text = .SubFilterClass4
'        Me.txtAssort.Text = .SubFilterAssort
'
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcFcstId_Click)"
'End Sub
'
'Private Sub dcFcstId_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcFcstId.Columns(0).Name = "fcstid"
'    Me.dcFcstId.Columns(0).Caption = getTranslationResource("Forecast ID")
'    Me.dcFcstId.Columns(0).Width = 1400
'    Me.dcFcstId.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcFcstId.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcFcstId.Columns(0).DataField = "fcstid"
'
'    Me.dcFcstId.Columns(1).Name = "fcstdesc"
'    Me.dcFcstId.Columns(1).Caption = getTranslationResource("Description")
'    Me.dcFcstId.Columns(1).Width = 2880
'    Me.dcFcstId.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcFcstId.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dcFcstId.Columns(1).DataField = "fcstdesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcFcstId, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcFcstID.Columns.Count - 1
''        dcFcstID.Columns(IndexCounter).HasHeadBackColor = True
''        dcFcstID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcFcstId_InitColumnProps)"
'End Sub
'
'Private Function GetForecastRcd(FcstId As String)
'On Error GoTo ErrorHandler
'    'Get the Forecast Record
'    If Trim(FcstId) <> "" Then
'        AIM_Forecast_GetEq_Sp.Parameters("@FcstId").Value = FcstId
'
'         If rsForecast.State <> adStateOpen Then
'            rsForecast.Open AIM_Forecast_GetEq_Sp
'        Else
'            rsForecast.Requery
'        End If
'
''        If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
''        Set rsForecast = Nothing
''        Set rsForecast = New ADODB.Recordset
''        rsForecast.Open AIM_Forecast_GetEq_Sp
'    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'        FcstId = rsForecast("FcstId").Value
'        FcstDesc = rsForecast("fcstdesc").Value
'        FcstStartDate = rsForecast("FcstStartDate").Value
'        FcstPds = rsForecast("fcstpds").Value
'        'PrevPds = FcstPds
'        FcstInterval = rsForecast("fcstinterval").Value
'        OldFcstInterval = FcstInterval
'        OrgFcstInterval = FcstInterval
'        FcstUnit = rsForecast("FcstUnit").Value
'        With m_FcstItemFilter
'            .LcID = rsForecast("LcID").Value
'            .Item = rsForecast("Item").Value
'            .VnId = rsForecast("VnID").Value
'            .ById = rsForecast("ByID").Value
'            .ItemStatus = rsForecast("ItStat").Value
'            .Class1 = rsForecast("Class1").Value
'            .Class2 = rsForecast("Class2").Value
'            .Class3 = rsForecast("Class3").Value
'            .Class4 = rsForecast("Class4").Value
'            .Assort = rsForecast("Assort").Value
'
'            .SubFilterLcID = IIf(Trim$(.SubFilterLcID) = "", rsForecast("LcID").Value, .SubFilterLcID)
'            .SubFilterItem = IIf(Trim$(.SubFilterItem) = "", rsForecast("Item").Value, .SubFilterItem)
'            .SubFilterVnId = IIf(Trim$(.SubFilterVnId) = "", rsForecast("VnID").Value, .SubFilterVnId)
'            .SubFilterById = IIf(Trim$(.SubFilterById) = "", rsForecast("ByID").Value, .SubFilterById)
'            .SubFilterItemStatus = IIf(Trim$(.SubFilterItemStatus) = "", rsForecast("ItStat").Value, .SubFilterItemStatus)
'            .SubFilterClass1 = IIf(Trim$(.SubFilterClass1) = "", rsForecast("Class1").Value, .SubFilterClass1)
'            .SubFilterClass2 = IIf(Trim$(.SubFilterClass2) = "", rsForecast("Class2").Value, .SubFilterClass2)
'            .SubFilterClass3 = IIf(Trim$(.SubFilterClass3) = "", rsForecast("Class3").Value, .SubFilterClass3)
'            .SubFilterClass4 = IIf(Trim$(.SubFilterClass4) = "", rsForecast("Class4").Value, .SubFilterClass4)
'            .SubFilterAssort = IIf(Trim$(.SubFilterAssort) = "", rsForecast("Assort").Value, .SubFilterAssort)
'
'            Me.txtLocation.Text = .SubFilterLcID
'            Me.txtItemID.Text = .SubFilterItem
'            Me.txtVendorID.Text = .SubFilterVnId
'            Me.txtBuyerID.Text = .SubFilterById
'            Me.txtItemStatus.Text = .SubFilterItemStatus
'            Me.txtClass1.Text = .SubFilterClass1
'            Me.txtClass2.Text = .SubFilterClass2
'            Me.txtClass3.Text = .SubFilterClass3
'            Me.txtClass4.Text = .SubFilterClass4
'            Me.txtAssort.Text = .SubFilterAssort
'        End With
'
'        RefreshForm
'        SetColumns rsForecast("FcstPds").Value, rsForecast("FcstStartDate").Value, rsForecast("fcstinterval").Value, rsForecast("netrqmts").Value
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastRcd)"
'End Function
'Private Function SetColumns(ByVal Pds As Integer, ByVal BaseDate As Date, ByVal Interval As AIM_INTERVALS, NetRqmts As Integer)
'On Error GoTo ErrorHandler
'
'    Dim CurStartdate As Date
'    Dim IndexCounter As Integer
'
'    'Items grid, first
'    Me.dgItems.Enabled = False
'    'Clear any existing columns
'    Me.dgItems.Columns.RemoveAll
'    'Set up the Items Grid
'    SetColumnFormat dgItems, 0, "LcID", "Location", 800, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 1, "Item", "Item", 1440, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 2, "itdesc", "Description", 2880, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 3, "data", "Data", 1440, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 4, "class1", "Class 1", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 5, "class2", "Class 2", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 6, "class3", "Class 3", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 7, "class4", "Class 4", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 8, "status", "Status", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 9, "vc", "VC", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgItems, 10, "VnID", "Vendor ID", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, 11, "assort", "Assort", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, 12, "ByID", "Buyer ID", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, 13, "totals", "Totals", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    Me.dgItems.Columns(13).NumberFormat = "#,##0"
'    Me.dgItems.Columns(13).DataType = vbDouble
'
'    CurStartdate = BaseDate
'    For IndexCounter = 14 To Pds + 13
'        SetColumnFormat dgItems, IndexCounter, "fcst" + Format(IndexCounter, "00"), Format(CurStartdate, gDateFormat), 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, False, True
'        'SetColumnFormat dgItems, IndexCounter, "fcst" + Format(IndexCounter, "00"), Format(CurStartdate, "DD/MM/YYYY"), 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, False, True
'        Me.dgItems.Columns(IndexCounter).NumberFormat = "#,##0"
'        Me.dgItems.Columns(IndexCounter).DataType = vbDouble
'
'        'Get the Next Start date
'        CurStartdate = GetNextStartDate(BaseDate, CurStartdate, Interval)
'    Next IndexCounter
'    'Sri code change to save only modified rows and new rows
'    'Add one more column 14 called New which will have N or Y data
'    'Add one more column 15 called Modified which will have N or Y data
'    'SetColumnFormat dgItems, Pds + 14, "Row", "Row", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    'SetColumnFormat dgItems, Pds + 15, "Seq", "Seq", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, Pds + 14, "New", "New", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, Pds + 15, "Modified", "Modified", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, Pds + 16, "Row", "Row", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    SetColumnFormat dgItems, Pds + 17, "Seq", "Seq", 1000, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, False
'    'Sri changes end
'    CurrentPd = 0
'    For IndexCounter = 14 To Pds + 13
'        If Format(Date, "yyyy/mm/dd") >= Format(CDate(dgItems.Columns(IndexCounter).Caption), "yyyy/mm/dd") Then
'            dgItems.Columns(IndexCounter).Locked = True
'        Else
'            dgItems.Columns(IndexCounter).Locked = False
'        End If
'        If dgItems.Columns(IndexCounter + 1).Caption <> "Row" Then
'            If IsDate(dgItems.Columns(IndexCounter).Caption) _
'            And IsDate(dgItems.Columns(IndexCounter + 1).Caption) _
'            Then
'                If Format(Date, "yyyy/mm/dd") >= Format(CDate(dgItems.Columns(IndexCounter).Caption), "yyyy/mm/dd") _
'                And Format(Date, "yyyy/mm/dd") < Format(CDate(dgItems.Columns(IndexCounter + 1).Caption), "yyyy/mm/dd") _
'                Then
'                    CurrentPd = IndexCounter - 13
'                    CurrentPdDate = Format(CDate(dgItems.Columns(IndexCounter).Caption), "yyyy/mm/dd")
'                    Exit For
'                End If
'            End If
'        End If
'    Next IndexCounter
'
'    If CurrentPd = 0 Then
'       If Format(Date, "yyyy/mm/dd") >= Format(CDate(dgItems.Columns(IndexCounter - 1).Caption), "yyyy/mm/dd") And Format(Date, "yyyy/mm/dd") < Format(CDate(CurStartdate), "yyyy/mm/dd") Then
'  '       If Format(Date, "DD/MM/YYYY") >= CDate(dgItems.Columns(IndexCounter - 1).Caption) And Format(Date, "DD/MM/YYYY") < CDate(Format(CurStartdate, "dd/mm/yyyy")) Then
'            CurrentPd = Pds
'            'Sri code change to save only modified rows and new rows begin
'        'ElseIf Format(Date, "DD/MM/YYYY") > CDate(dgItems.Columns(dgItems.Cols - 3).Caption) Then
'        ElseIf Format(Date, "yyyy/mm/dd") > Format(CDate(dgItems.Columns(dgItems.Cols - 5).Caption), "yyyy/mm/dd") Then
'            'Sri code change to save only modified rows and new rows end
'            CurrentPd = Pds + 1
'             CurrentPdDate = Format(CDate(CurStartdate), "yyyy/mm/dd")
'             'CurrentPdDate = CDate(Format(CurStartdate, "dd/mm/yyyy"))
'        End If
'    End If
'
'    dgItems.StyleSets.RemoveAll
'
'    Me.dgItems.StyleSets.Add "EditLine1"
'
'    Me.dgItems.StyleSets("EditLine1").ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)    '&HC00000
'    Me.dgItems.StyleSets("EditLine").Font.Bold = True
'    Me.dgItems.StyleSets("EditLine1").Font.Size = Me.dgItems.Font.Size
'    Me.dgItems.StyleSets("EditLine1").BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_FORECAST)
'
'
'    Me.dgItems.StyleSets.Add "ActiveLine"
'    Me.dgItems.StyleSets("ActiveLine").ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)    'vbRed
'    Me.dgItems.StyleSets("ActiveLine").Font.Bold = True
'    Me.dgItems.StyleSets("ActiveLine").Font.Size = Me.dgItems.Font.Size
'    Me.dgItems.StyleSets("ActiveLine").BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_BUCKET4)    'vbRed
'
'    Me.dgItems.StyleSets.Add "FreezeLine"
'    Me.dgItems.StyleSets("FreezeLine").ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_INACTIVE)   'vbGrayText
'    Me.dgItems.StyleSets("FreezeLine").Font.Size = Me.dgItems.Font.Size
'    Me.dgItems.StyleSets("FreezeLine").BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_FROZEN)   'vbGrayText
'
'    dgItems.SplitterPos = 4
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgItems, ACW_EXPAND
'    End If
'
'    For IndexCounter = 0 To dgItems.Columns.Count - 1
''        dgItems.Columns(IndexCounter).HasHeadBackColor = True
''        dgItems.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'        If dgItems.Columns(IndexCounter).Locked = False Then dgItems.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'    dgItems.Enabled = True
'
'    'Bind the Grid
'    Me.dgItems.ReBind
'    If xMainArray.Count(1) > 0 Then Me.dgItems.Rows = xMainArray.Count(1)
'
'    'Now for the Summary grid
'    dgSummary.Enabled = False
'    'Clear any existing columns
'    Me.dgSummary.Columns.RemoveAll
'    'Set up the Totals Grid
'    CurStartdate = BaseDate
'
'    SetColumnFormat dgSummary, 0, "Element", "Element", 1400, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    SetColumnFormat dgSummary, 1, "Total", "Totals", 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'    Me.dgSummary.Columns(1).NumberFormat = "#,##0"
'
'    For IndexCounter = 2 To Pds + 1
'        SetColumnFormat dgSummary, IndexCounter, "fcst" + Format(IndexCounter, "00"), Format(CurStartdate, gDateFormat), 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'        'SetColumnFormat dgSummary, IndexCounter, "fcst" + Format(IndexCounter, "00"), Format(CurStartdate, "dd/mm/yyyy"), 1200, ssColCapAlignLeftJustify, ssCaptionAlignmentLeft, True, True
'        Me.dgSummary.Columns(IndexCounter).NumberFormat = "#,##0"
'        Me.dgSummary.Columns(IndexCounter).DataType = vbDouble
'        'Get the Next Start date
'        CurStartdate = GetNextStartDate(BaseDate, CurStartdate, Interval)
'    Next IndexCounter
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgSummary, ACW_EXPAND
'    End If
'
'    For IndexCounter = 0 To dgSummary.Columns.Count - 1
'        If dgSummary.Columns(IndexCounter).Locked = False Then dgSummary.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'    dgSummary.Columns(IndexCounter).Visible = False
'    dgSummary.Enabled = True
'
'    'Bind the grid
'    Me.dgSummary.ReBind
'    dgSummary.SplitterPos = 1
'    If xSumArray.Count(1) > 0 Then Me.dgSummary.Rows = xSumArray.Count(1)
'
'    'Clean Up
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 380 Then
'        Resume Next
'    Else
'        Err.Raise Err.Number, Err.Source, Err.Description & "(SetColumns)"
'    End If
'End Function
'
'Private Function RefreshForm()
'On Error GoTo ErrorHandler
'Dim i As Long
'    'Save the Forecast Table Key
'    FcstId = rsForecast("FcstId").Value
'
'    'Refresh the Form
'    Select Case rsForecast("FcstInterval").Value
'        Case int_Days
'            Me.optInterval(0).Value = True
'        Case int_Weeks
'            Me.optInterval(1).Value = True
'        Case int_Months
'            Me.optInterval(2).Value = True
'        Case int_Quarters
'            Me.optInterval(3).Value = True
'        Case int_544
'            Me.optInterval(4).Value = True
'        Case int_454
'            Me.optInterval(5).Value = True
'        Case int_445
'            Me.optInterval(6).Value = True
'        Case int_4Wks
'            Me.optInterval(7).Value = True
'    End Select
'
'    For i = 0 To OrgFcstInterval - 1
'        Me.optInterval(i).Enabled = False
'    Next i
'
'    For i = OrgFcstInterval To 7
'        Me.optInterval(i).Enabled = True
'    Next i
'
'    Select Case rsForecast("FcstUnit").Value
'        Case FcstUnit
'            Me.optForecastUnit(0).Value = True
'            ForecastUnit = "Units"
'        Case FcstCost
'            Me.optForecastUnit(1).Value = True
'            ForecastUnit = "Cost"
'        Case FcstPrice
'            Me.optForecastUnit(2).Value = True
'            ForecastUnit = "Price"
'        Case FcstWeight
'            Me.optForecastUnit(3).Value = True
'            ForecastUnit = "Weight"
'        Case FcstCube
'            Me.optForecastUnit(4).Value = True
'            ForecastUnit = "Cube"
'    End Select
'
'    Me.txtFcstPds.Value = rsForecast("FcstPds").Value
'    'Me.txtAdjustPct.Value = rsForecast("AdjustPct").Value
'
'    SetTDBDate txtFcstStartDate, rsForecast!FcstStartDate
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(RefreshForm)"
'End Function
'
'Private Sub dcFcstType_Click()
''sri batch fcst begin
'GetBatchStatus
''sri batch fcst end
'End Sub
'
'
'Private Sub dcFcstType_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcFcstType.Columns(0).Name = "CodeId"
'    Me.dcFcstType.Columns(0).Caption = getTranslationResource("Code")
'    Me.dcFcstType.Columns(0).Width = 1650
'    Me.dcFcstType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcFcstType.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcFcstType.Columns(0).DataField = "CodeId"
'
'    Me.dcFcstType.Columns(1).Name = "CodeDesc"
'    Me.dcFcstType.Columns(1).Caption = getTranslationResource("Forecast Type")
'    Me.dcFcstType.Columns(1).Width = 1650
'    Me.dcFcstType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcFcstType.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dcFcstType.Columns(1).DataField = "CodeDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcFcstType, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcFcstType.Columns.Count - 1
''        dcFcstType.Columns(IndexCounter).HasHeadBackColor = True
''        dcFcstType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcFcstType_InitColumnProps)"
'End Sub
'
'Private Sub dgItems_AfterColUpdate(ByVal ColIndex As Integer)
'dgItems.Update
'End Sub
'
'Private Sub dgItems_AfterUpdate(RtnDispErrMsg As Integer)
'    dgItems.Refresh
'End Sub
'
'Private Sub dgItems_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim OrgInterval As AIM_INTERVALS
'    Dim ActDate As Date
'    Dim NextPrdDate As Date
'    Dim strMessage As String
'    Dim RtnCode As Long
'
'    NoChange = True
'
'    If UCase(dgItems.Columns(3).Value) <> UCase("Adj. SYS FCST") And ColIndex > 13 Then
'        strMessage = getTranslationResource("MSGBOX06904")
'        If StrComp(strMessage, "MSGBOX06904") = 0 Then strMessage = "Rows besides the Adjusted System Forecast may not be modified."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        dgItems.Columns(ColIndex).Value = OldValue
'        NoChange = False
'        Exit Sub
'    End If
''    If ColIndex < CurrentPd + 14 Then
''        strMessage = getTranslationResource("MSGBOX06908")
''        If StrComp(strMessage, "MSGBOX06908") = 0 Then strMessage = "The Forecast may not be updated for a Forecast Period in the past."
''        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
''        dgItems.Columns(ColIndex).Value = OldValue
''        Exit Sub
''    End If
'
'    AIM_Item_GetEq_Sp.Parameters("@LcIDkey").Value = dgItems.Columns(0).Value
'    AIM_Item_GetEq_Sp.Parameters("@ItemKey").Value = dgItems.Columns(1).Value
'
'
'    If rsFreezeItem.State <> adStateOpen Then
'        rsFreezeItem.Open AIM_Item_GetEq_Sp
'    Else
'        rsFreezeItem.Requery
'    End If
''    If f_IsRecordsetValidAndOpen(rsFreezeItem) Then rsFreezeItem.Close
''    Set rsFreezeItem = Nothing
''    Set rsFreezeItem = New ADODB.Recordset
''    rsFreezeItem.Open AIM_Item_GetEq_Sp
'
'    If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
'        FreezeItem = rsFreezeItem("Freeze_Forecast").Value
'        ActDate = rsFreezeItem("ActDate").Value
'    End If
'
'    If UCase(FreezeItem) = "Y" Then
'        strMessage = getTranslationResource("MSGBOX06905")
'        If StrComp(strMessage, "MSGBOX06905") = 0 Then strMessage = "The selected Item and/or Location is frozen and may not be edited."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        dgItems.Columns(ColIndex).Value = OldValue
'        NoChange = False
'        Exit Sub
'    End If
'    OrgInterval = OldFcstInterval
'    NextPrdDate = GetNextStartDate(FcstStartDate, Format(CDate(dgItems.Columns(ColIndex).Caption), "yyyy/mm/dd"), OrgInterval)
'    If CDate(dgItems.Columns(ColIndex).Caption) < ActDate And NextPrdDate < ActDate Then
'        strMessage = getTranslationResource("MSGBOX06907")
'        If StrComp(strMessage, "MSGBOX06907") = 0 Then strMessage = "The Forecast may not be updated before the Item's Activation Date."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        dgItems.Columns(ColIndex).Value = OldValue
'        NoChange = False
'        Exit Sub
'    End If
'
'    AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@FcstId").Value = FcstId
'    AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@LcID").Value = dgItems.Columns(0).Value
'    AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Item").Value = dgItems.Columns(1).Value
'
'    AIM_ForecastFreezeItem_GetEq_Sp.Execute
'
'    If AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Return_Value").Value = 0 Then
'        strMessage = getTranslationResource("MSGBOX06905")
'        If StrComp(strMessage, "MSGBOX06905") = 0 Then strMessage = "The selected Item and/or Location is frozen and may not be edited."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        dgItems.Columns(ColIndex).Value = OldValue
'        NoChange = False
'        Exit Sub
'    End If
'
'    AIM_ForecastFreezePds_Get_Sp.Parameters("@FcstId").Value = FcstId
'    AIM_ForecastFreezePds_Get_Sp.Parameters("@LcID").Value = dgItems.Columns(0).Value
'
'    AIM_ForecastFreezePds_Get_Sp.Execute
'
'    If AIM_ForecastFreezePds_Get_Sp.Parameters("@Return_Value").Value = 0 Then
'        FreezedPds = AIM_ForecastFreezePds_Get_Sp.Parameters("@FreezePds").Value
'    Else
'        FreezedPds = 0
'    End If
'
'    If ColIndex <= CurrentPd + FreezedPds + 13 Then
'        strMessage = getTranslationResource("MSGBOX06906")
'        If StrComp(strMessage, "MSGBOX06906") = 0 Then strMessage = "The selected Forecast Period is frozen and may not be edited."
'        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
'        dgItems.Columns(ColIndex).Value = OldValue
'        NoChange = False
'        Exit Sub
'    End If
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(dgItems_BeforeColUpdate)"
'    End If
'End Sub
'
'Private Sub dgItems_HeadClick(ByVal ColIndex As Integer)
'On Error GoTo ErrorHandler
'
'    Dim ColName As String
'    'Dim SortSeq As String
'    Dim RowEndIndex As Long
'    Dim ColEndIndex As Integer
'
'    RowEndIndex = xMainArray.UpperBound(1)
'    ColEndIndex = xMainArray.UpperBound(2)
'
'    'Is the recordset open
'    If xMainArray.Count(1) = 0 Then Exit Sub
'
'    If ColIndex = 3 Or ColIndex > 9 Then Exit Sub
'
'    If SortSeq = "Desc" Then
'        If ColIndex = 0 Then
'            xMainArray.QuickSort 0, RowEndIndex, 0, XORDER_ASCEND, XTYPE_STRING, _
'                                                                1, XORDER_ASCEND, XTYPE_STRING, _
'                                                                ColEndIndex, XORDER_ASCEND, XTYPE_STRING
'        ElseIf ColIndex = 1 Then
'            xMainArray.QuickSort 0, RowEndIndex, 1, XORDER_ASCEND, XTYPE_STRING, _
'                                                                0, XORDER_ASCEND, XTYPE_STRING, _
'                                                                ColEndIndex, XORDER_ASCEND, XTYPE_STRING
'        Else
'            xMainArray.QuickSort 0, RowEndIndex, ColIndex, XORDER_ASCEND, XTYPE_STRING, _
'                                                                0, XORDER_ASCEND, XTYPE_STRING, _
'                                                                1, XORDER_ASCEND, XTYPE_STRING, _
'                                                                ColEndIndex, XORDER_ASCEND, XTYPE_STRING
'        End If
'        SortSeq = "Asc"
'    Else
'        If ColIndex = 0 Then
'            xMainArray.QuickSort 0, RowEndIndex, 0, XORDER_DESCEND, XTYPE_STRING, _
'                                                                1, XORDER_ASCEND, XTYPE_STRING, _
'                                                                ColEndIndex, XORDER_ASCEND, XTYPE_STRING
'        ElseIf ColIndex = 1 Then
'            xMainArray.QuickSort 0, RowEndIndex, 1, XORDER_DESCEND, XTYPE_STRING, _
'                                                                0, XORDER_ASCEND, XTYPE_STRING, _
'                                                                ColEndIndex, XORDER_ASCEND, XTYPE_STRING
'        Else
'            xMainArray.QuickSort 0, RowEndIndex, ColIndex, XORDER_DESCEND, XTYPE_STRING, _
'                                                                0, XORDER_ASCEND, XTYPE_STRING, _
'                                                                1, XORDER_ASCEND, XTYPE_STRING, _
'                                                                ColEndIndex, XORDER_ASCEND, XTYPE_STRING
'        End If
'        SortSeq = "Desc"
'    End If
'
'    Me.dgItems.ReBind
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgItems_HeadClick)"
'End Sub
'
'Private Sub dgItems_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
'On Error GoTo ErrorHandler
'
'    Select Case Button
'    Case vbRightButton
'        Whichgrid = "Items"
'        Me.PopupMenu Me.mnuEdit
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgItems_MouseDown)"
'End Sub
'
'Private Sub dgItems_RowLoaded(ByVal bookmark As Variant)
'On Error GoTo ErrorHandler
'
'    Dim i As Long
'
'    If dgItems.Cols > 3 Then
'        'If UCase(Me.dgItems.Columns(1).Value) = CurrItem Then
'        '    For i = 0 To Me.dgItems.Cols - 1
'        '        Me.dgItems.Columns(i).CellStyleSet ("OddRow")
'        '    Next i
'        'End If
'
'        If UCase(Me.dgItems.Columns(3).Value) = UCase("Adj. Sys Fcst") Then 'getTranslationResource("Totals") Then
'            For i = 0 To Me.dgItems.Cols - 1
'                Me.dgItems.Columns(i).CellStyleSet ("EditLine1")
'            Next i
'        End If
'
'
'
'    '        For i = 0 To 13 + CurrentPd
'    '            Me.dgItems.Columns(i).CellStyleSet ("EditLine1")
'    '        Next i
'    '        For i = 14 + CurrentPd To Me.dgItems.Cols - 1
'    '            Me.dgItems.Columns(i).CellStyleSet ("EditLine2")
'    '        Next i
'
'    '    End If
'
'        If UCase(Me.dgItems.Columns(3).Value) = UCase(dcFcstType.Text) Then  'getTranslationResource("Totals") Then
'            For i = 14 + CurrentPd To Me.dgItems.Cols - 1
'                Me.dgItems.Columns(i).CellStyleSet ("ActiveLine")
'            Next i
'        End If
'
'        AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@FcstId").Value = FcstId
'        AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@LcID").Value = dgItems.Columns(0).Value
'        AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Item").Value = dgItems.Columns(1).Value
'
'        AIM_ForecastFreezeItem_GetEq_Sp.Execute
'
'        If AIM_ForecastFreezeItem_GetEq_Sp.Parameters("@Return_Value").Value = 0 Then
'            For i = 0 To Me.dgItems.Cols - 1
'                Me.dgItems.Columns(i).CellStyleSet ("FreezeLine")
'            Next i
'        End If
'
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgItems_RowLoaded)"
'End Sub
'
'Private Sub dgItems_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim iRBRow As Long
'    Dim iCol As Integer
'    Dim irow As Long
'    Dim iPoint As Long
'    Dim iCount As Long
'    Dim SysFcst As Boolean
'    Dim SysNetReq As Boolean
'    Dim ColStartIndex As Integer
'    Dim ColEndIndex As Integer
'
'
'
'    If dgItems.Enabled = False Then Exit Sub    'This should have been reset in SetColumns() before the dgitems.rebind
'
'    'Test for an empty array
'    On Error Resume Next
'    If xMainArray.Count(1) = 0 Then
'        Exit Sub
'    Else
'        On Error GoTo ErrorHandler
'        iCount = xMainArray.Count(1)
'        irow = 0
'    End If
'    On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            iPoint = iCount
'        Else
'            iPoint = 0
'        End If
'    Else
'        iPoint = StartLocation
'        If ReadPriorRows Then
'            iPoint = iPoint - 1
'        Else
'            iPoint = iPoint + 1
'        End If
'    End If
'
'    ColStartIndex = xMainArray.LowerBound(2)
'    ColEndIndex = xMainArray.UpperBound(2)
'    For iRBRow = 0 To RowBuf.RowCount - 1
'        If iPoint < 0 Or iPoint > iCount - 1 Then Exit For
'
'        For iCol = ColStartIndex To ColEndIndex
'            RowBuf.Value(iRBRow, iCol) = xMainArray.Value(iPoint, iCol)
'        Next iCol
'
'        RowBuf.bookmark(iRBRow) = iPoint
'        If ReadPriorRows Then
'            iPoint = iPoint - 1
'        Else
'            iPoint = iPoint + 1
'        End If
'
'        irow = irow + 1
'
'    Next iRBRow
'
'    RowBuf.RowCount = irow
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgItems_UnboundReadData)"
'End Sub
'
'Private Sub dgItems_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
'On Error GoTo ErrorHandler
'
'    Dim iCol As Integer
'    Dim irow As Long
'    Dim vOldvalue As Variant
'    Dim ModNetReq() As Double
'    Dim xTempPlannedRct As New XArrayDB
'    Dim iSRow As Long
'
'    Dim ColMainEndIndex As Integer
'    Dim RowPlannedEndIndex As Long
'    Dim RowNetEndIndex As Long
'    Dim ColPlannedEndIndex As Integer
'    Dim ColNetEndIndex As Integer
'    Dim AdjNetReqSum As Long
'    Dim AdjSysReqSum As Long
'
'    If NoChange = False Then
'        Exit Sub
'    End If
'    ColMainEndIndex = xMainArray.UpperBound(2)
'
'    xRowChange.ReDim 0, 0, 0, 2
'
'    If UCase(xMainArray(CInt(WriteLocation), 3)) = "ADJ. SYS FCST" Then
'         'Sri code change to save only modified and new rows begin
'         'set the flag in Modified column to "Y"
'        xMainArray(CInt(WriteLocation), ColMainEndIndex - 2) = "Y"
'         'Sri code change to save only modified and new rows end
'        For iCol = 0 To ColMainEndIndex - 1
'            If Not IsNull(RowBuf.Value(0, iCol)) Then
'                vOldvalue = xMainArray(CInt(WriteLocation), iCol)
'                xMainArray(CInt(WriteLocation), iCol) = CStr(RowBuf.Value(0, iCol))
'                xAdjForeCast.Value(xMainArray.Value(CInt(WriteLocation), ColMainEndIndex - 1), iCol - 1) = CStr(RowBuf.Value(0, iCol))
'                If Fcsttype = "FCST" Then
'                    xFcstRepos.Value(xMainArray(CInt(WriteLocation), ColMainEndIndex - 1), iCol - 1) = CStr(RowBuf.Value(0, iCol))
'                ElseIf Fcsttype = "BDGT" Then
'                    xBudgetRepos.Value(xMainArray(CInt(WriteLocation), ColMainEndIndex - 1), iCol - 1) = CStr(RowBuf.Value(0, iCol))
'                ElseIf Fcsttype = "DRFT" Then
'                    xDraftRepos.Value(xMainArray(CInt(WriteLocation), ColMainEndIndex - 1), iCol - 1) = CStr(RowBuf.Value(0, iCol))
'                End If
'
'                irow = xRowChange.UpperBound(1)
'                xRowChange.AppendRows (1)
'                xRowChange.Value(irow, 0) = CInt(WriteLocation)
'                xRowChange.Value(irow, 1) = iCol
'                xRowChange.Value(irow, 2) = vOldvalue
'            End If
'        Next iCol
'    End If
'
'    ReDim Preserve ModNetReq(1 To FcstPds)
'
'    For irow = 0 To xRowChange.UpperBound(1) - 1
'        'Sri code change to save only modified and new  rows begin
'        'For iCol = 14 To ColMainEndIndex - 2
'        AdjSysReqSum = 0
'        For iCol = 14 To ColMainEndIndex - 4
'         'Sri code change to save only modified and new rows end
'         'Sri code change fixed bug Modnetreq is the diff between Adj_sys_fcst -Sys_fcst
'            'ModNetReq(iCol - 13) = xMainArray.Value(xRowChange.Value(irow, 0), iCol) - xMainArray.Value(xRowChange.Value(irow, 0) + 1, iCol)
'            ModNetReq(iCol - 13) = xMainArray.Value(xRowChange.Value(irow, 0), iCol) - xMainArray.Value(xRowChange.Value(irow, 0) + 2, iCol)
'            AdjSysReqSum = AdjSysReqSum + xMainArray.Value(xRowChange.Value(irow, 0), iCol)
'        Next iCol
'            xMainArray.Value(xRowChange.Value(irow, 0), 13) = AdjSysReqSum
'        Set xTempPlannedRct = Nothing
'        'Annalakshmi S -- Oct-7-2002 -- Commented out last two args per Srinivas, since AIMAdvForecastMod does not match parameter list here
'        GetNetReqForAPeriod FcstId, "", xMainArray.Value(xRowChange.Value(irow, 0), 0), xMainArray.Value(xRowChange.Value(irow, 0), 1), FcstStartDate, FcstInterval, xNetReq, ModNetReq(), True, xTempPlannedRct
'
'        RowPlannedEndIndex = xTempPlannedRct.UpperBound(1)
'        ColPlannedEndIndex = xTempPlannedRct.UpperBound(2)
'        RowNetEndIndex = xNetReq.UpperBound(1)
'        ColNetEndIndex = xNetReq.UpperBound(2)
'
'        For iSRow = 0 To RowPlannedEndIndex
'            For iCol = 0 To ColPlannedEndIndex
'                'MsgBox CStr(xTempPlannedRct.Value(iSrow, icol))
'                xPlannedrct.Value(xMainArray(xRowChange.Value(irow, 0), ColMainEndIndex - 1), iCol) _
'                = xTempPlannedRct.Value(iSRow, iCol)
'            Next iCol
'        Next iSRow
'
'        For iSRow = 0 To RowNetEndIndex
'            If xNetReq.Value(iSRow, 0) = xMainArray.Value(xRowChange.Value(irow, 0), 0) _
'                    And xNetReq.Value(iSRow, 1) = xMainArray.Value(xRowChange.Value(irow, 0), 1) Then
'
'
''                 AIM_Item_GetEq_Sp.Parameters("@LcIDkey").Value = xMainArray.Value(xRowChange.Value(irow, 0), 0)
''                 AIM_Item_GetEq_Sp.Parameters("@ItemKey").Value = xMainArray.Value(xRowChange.Value(irow, 0), 1)
'
''                If rsFreezeItem.State <> adStateOpen Then
''                    rsFreezeItem.Open AIM_Item_GetEq_Sp
''                Else
''                    rsFreezeItem.Requery
''                End If
'
''                If f_IsRecordsetValidAndOpen(rsFreezeItem) Then rsFreezeItem.Close
''                Set rsFreezeItem = Nothing
''                Set rsFreezeItem = New ADODB.Recordset
''                rsFreezeItem.Open AIM_Item_GetEq_Sp
'
''                FreezeItem = "N"
''                If f_IsRecordsetOpenAndPopulated(rsFreezeItem) Then
''                    FreezeItem = rsFreezeItem("Freeze_Forecast").Value
''                End If
'
''                If UCase(FreezeItem) <> "Y" Then
'                    AIM_ForecastFreezePds_Get_Sp.Parameters("@FcstId").Value = FcstId
'                    AIM_ForecastFreezePds_Get_Sp.Parameters("@LcID").Value = xMainArray.Value(xRowChange.Value(irow, 0), 0)
'
'                    AIM_ForecastFreezePds_Get_Sp.Execute
'
'                    If AIM_ForecastFreezePds_Get_Sp.Parameters("@Return_Value").Value = 0 Then
'                        FreezedPds = AIM_ForecastFreezePds_Get_Sp.Parameters("@FreezePds").Value
'                    Else
'                        FreezedPds = 0
'                    End If
'
''********************For iCol = 13 + CurrentPd + FreezedPds To ColNetEndIndex - 8
'                    AdjNetReqSum = 0
'                    For iCol = 13 To ColNetEndIndex - 8
'                        xMainArray.Value(xRowChange.Value(irow, 0) - 1, iCol + 1) = xNetReq.Value(iSRow, iCol)
'                        xAdjForecastNr.Value(xMainArray(xRowChange.Value(irow, 0), ColMainEndIndex - 1), iCol) _
'                        = xNetReq.Value(iSRow, iCol)
'                        AdjNetReqSum = AdjNetReqSum + xNetReq.Value(iSRow, iCol)
'                    Next iCol
'                        xMainArray.Value(xRowChange.Value(irow, 0) - 1, 13) = AdjNetReqSum
''                End If
'            End If
'        Next iSRow
'    Next irow
'    Write_Message ""
'
''    dgItems.ReBind
'
'
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(dgItems_UnboundWriteData)"
'    End If
'End Sub
'
'Private Sub dgSummary_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
'On Error GoTo ErrorHandler
'
'    Select Case Button
'    Case vbRightButton
'        Whichgrid = "Summary"
'        Me.PopupMenu Me.mnuEdit
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgItems_MouseDown)"
'
'End Sub
'
'Private Sub dgSummary_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim iRBRow As Long
'    Dim iCol As Integer
'    Dim irow As Long
'    Dim iPoint As Long
'    Dim iCount As Long
'
'    If dgSummary.Enabled = False Then Exit Sub    'This should have been reset in SetColumns() before the dgitems.rebind
'
'    'Test for an empty array
'    On Error Resume Next
'    If xSumArray.Count(1) = 0 Then
'        Exit Sub
'    Else
'        On Error GoTo ErrorHandler
'        iCount = xSumArray.Count(1)
'        irow = 0
'    End If
'    On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            iPoint = iCount
'        Else
'            iPoint = 0
'        End If
'    Else
'        iPoint = StartLocation
'        If ReadPriorRows Then
'            iPoint = iPoint - 1
'        Else
'            iPoint = iPoint + 1
'        End If
'    End If
'
'    For iRBRow = 0 To RowBuf.RowCount - 1
'        If iPoint < 0 Or iPoint > iCount - 1 Then Exit For
'
'        'For iCol = xSumArray.LowerBound(2) To xSumArray.UpperBound(2)
'        For iCol = 0 To (RowBuf.ColumnCount - 1)
'            RowBuf.Value(iRBRow, iCol) = xSumArray.Value(iPoint, iCol)
'        Next iCol
'
'        RowBuf.bookmark(iRBRow) = iPoint
'        If ReadPriorRows Then
'            iPoint = iPoint - 1
'        Else
'            iPoint = iPoint + 1
'        End If
'
'        irow = irow + 1
'
'    Next iRBRow
'
'    RowBuf.RowCount = irow
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgSummary_UnboundReadData)"
'End Sub
'
'Private Sub Initialize()
'On Error GoTo ErrorHandler
'
'    txtAdjustPct.Text = "0.00"
'    cmCancelPcnt.Visible = False
'    cmConfirmPcnt.Visible = False
'    cmApplyPcnt.Visible = True
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(Initialize)"
'End Sub
'
'Private Sub Form_Activate()
'On Error GoTo ErrorHandler
'
'    'Check for an open connection
'    If Cn.State <> adStateOpen Then
'        Unload Me
'        Exit Sub
'    End If
'
'    'Set State of window list State Button
'    gbUpdating = True
'    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
'    gbUpdating = False
'    'Sri batch fcst begin
'    GetBatchdata = False
'    'Sri batch fcst end
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Activate)"
'End Sub
'
'Private Sub Form_Load()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strSQL As String
'    Dim strMessage As String
'    Dim strMessage1 As String
'    Dim rsFcstType As ADODB.Recordset
'    Dim rsElement As ADODB.Recordset
'    Dim IndexCounter As Long
'    'Housekeeping
'    Screen.MousePointer = vbHourglass
'    strMessage = getTranslationResource("STATMSG06900")
'    If StrComp(strMessage, "STATMSG06900") = 0 Then strMessage = "Initializing Forecast Modification..."
'    Write_Message strMessage
'
'    GetTranslatedCaptions Me
''    InitializeChart
'
'    SetColors
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
'    If RtnCode <> SUCCEED Then Exit Sub
'
'    'Build/Bind the Forecast Type Drop Down
'    strSQL = "SELECT CodeID,CodeDesc FROM AIMCodeLookUp " & _
'            " WHERE CodeType='" & g_CODETYPE_FCSTTYPE & "'" & _
'            " AND LangID='" & gLangID & "' " & _
'            " ORDER BY CodeID, CodeDesc"
'    Set rsFcstType = New ADODB.Recordset
'    rsFcstType.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'
'    If f_IsRecordsetOpenAndPopulated(rsFcstType) Then
'        Do Until rsFcstType.eof
'            Me.dcFcstType.AddItem rsFcstType("CodeId").Value + vbTab + rsFcstType("CodeDesc").Value
'            rsFcstType.MoveNext
'        Loop
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'    Set rsFcstType = Nothing
'
'    'Make the spin button visible
'    Me.txtFcstPds.Spin.Visible = 1
'    Me.txtAdjustPct.Spin.Visible = 1
'
'    strSQL = "SELECT CodeID,CodeDesc FROM AIMCodeLookUp " & _
'            " WHERE CodeType='" & g_CODETYPE_FCSTELEMENT & "'" & _
'            " AND LangID='" & gLangID & "' " & _
'            " ORDER BY CodeID, CodeDesc"
'    Set rsElement = New ADODB.Recordset
'    rsElement.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'    If f_IsRecordsetOpenAndPopulated(rsElement) Then
'        Do Until rsElement.eof
'            'Me.lsElement.AddItem rsElement("CodeId").Value + vbTab + rsElement("CodeDesc").Value
'            Me.lsElement.AddItem rsElement("CodeDesc").Value
'            rsElement.MoveNext
'        Loop
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsElement) Then rsElement.Close
'    Set rsElement = Nothing
'
'    For IndexCounter = 0 To lsElement.ListCount - 1
'        If Me.lsElement.List(IndexCounter) = "Sys Fcst" Then
'            lsElement.Selected(IndexCounter) = True
'        Else
'            lsElement.Selected(IndexCounter) = False
'        End If
'    Next IndexCounter
'
'    'Initialize Stored Procedures
'    Set AIM_Forecast_GetEq_Sp = New ADODB.Command
'    With AIM_Forecast_GetEq_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_Forecast_GetEq_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastRepository_Save_Sp = New ADODB.Command
'    With AIM_ForecastRepository_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepository_Save_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastRepositoryDetail_Save_Sp = New ADODB.Command
'    With AIM_ForecastRepositoryDetail_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepositoryDetail_Save_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastRepositoryDetail_Update_Sp = New ADODB.Command
'    With AIM_ForecastRepositoryDetail_Update_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepositoryDetail_Update_Sp"
'        .Parameters.Refresh
'    End With
'
'     Set AIM_ForecastRepository_Update_Sp = New ADODB.Command
'    With AIM_ForecastRepository_Update_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepository_Update_Sp"
'        .Parameters.Refresh
'    End With
'     Set AIM_BatchFcst_Save_Sp = New ADODB.Command
'    With AIM_BatchFcst_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_BatchFcst_Save_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastFreezePds_Get_Sp = New ADODB.Command
'    With AIM_ForecastFreezePds_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastFreezePds_Get_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_Item_GetEq_Sp = New ADODB.Command
'    With AIM_Item_GetEq_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_Item_GetEq_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastUserAccess_Validate_Sp = New ADODB.Command
'    With AIM_ForecastUserAccess_Validate_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastUserAccess_Validate_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastRepository_List_Sp = New ADODB.Command
'    With AIM_ForecastRepository_List_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepository_List_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastFreezeItem_GetEq_Sp = New ADODB.Command
'    With AIM_ForecastFreezeItem_GetEq_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastFreezeItem_GetEq_Sp"
'        .Parameters.Refresh
'    End With
'
'    With AIM_Locations_GetEq_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMLocations_GetEq_Sp"
'        .Parameters.Refresh
'  End With
'
'
'    'Initialize the AIM Forecast Table Recordset
'    Set rsForecast = New ADODB.Recordset
'    With rsForecast
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockOptimistic
'    End With
'
'    Set rsFcstRepos = New ADODB.Recordset
'    With rsFcstRepos
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockOptimistic
'    End With
'
'    Set rsLcID = New ADODB.Recordset
'    With rsLcID
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockOptimistic
'    End With
'
'    Set rsFreezeItem = New ADODB.Recordset
'    With rsFreezeItem
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockOptimistic
'    End With
'
'    'Build/Bind the Forecast Id Drop Down
'    'Get only active status fcstsids
'    strSQL = "SELECT Fcstid, FcstDesc FROM AIMForecast " & _
'            " Where FcstStatus =1  ORDER BY Fcstid"
'    Set rsFcstId = New ADODB.Recordset
'    rsFcstId.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'
'    Set dcFcstId.DataSourceList = rsFcstId
'
'    'If f_IsRecordsetOpenAndPopulated(rsFcstId) Then
'    '    Do Until rsFcstId.eof
'    '        Me.dcFcstId.AddItem rsFcstId("Fcstid").Value + vbTab + rsFcstId("fcstdesc").Value
'    '        rsFcstId.MoveNext
'    '    Loop
'    'End If
'
''    If rsFcstId.State <> adStateClosed Then
''        rsFcstId.Close
''        Set rsFcstId = Nothing
''    End If
'
'    'Initialize the rowChange array
'    xRowChange.ReDim 0, 0, 0, 2
'    RunAdjSysNetReq = False
'    Me.txtFcstStartDate.DropDown.Visible = 1
'
'    dcAIMItem.Text = "All"
'    dcAIMLocations.Text = "All"
'
'    'Add to Windows List
'    AddToWindowList Me.Caption
'
'    Me.atbFcstModification.Tools(3).Enabled = False
'    Me.atbFcstModification.Tools("ID_Copy").Enabled = False
'    Me.cmApplyPcnt.Enabled = False
'
'    ckGetFromBatch.Value = vbChecked
'
'    'Size the form
'    m_FirstTime = True
'    SizeForm
'    m_FirstTime = False
'
'    'Windup
'    Write_Message ""
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'    Set rsFcstType = Nothing
'    If f_IsRecordsetValidAndOpen(rsElement) Then rsElement.Close
'    Set rsElement = Nothing
'    f_HandleErr Me.Caption & "(Form_Load)"
'End Sub
'
'Private Sub Form_Resize()
'On Error GoTo ErrorHandler
'
'    If m_FirstTime = False Then
'        SizeForm
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Resize)"
'End Sub
'
'Private Sub Form_Unload(Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    Write_Message ""
'
'    If Not (AIM_Forecast_GetEq_Sp Is Nothing) Then Set AIM_Forecast_GetEq_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetEq_Sp = Nothing
'
'    If Not (AIM_ForecastRepository_Save_Sp Is Nothing) Then Set AIM_ForecastRepository_Save_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_Save_Sp = Nothing
'
'    If Not (AIM_ForecastRepositoryDetail_Save_Sp Is Nothing) Then Set AIM_ForecastRepositoryDetail_Save_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepositoryDetail_Save_Sp = Nothing
'
'    If Not (AIM_ForecastRepositoryDetail_Update_Sp Is Nothing) Then Set AIM_ForecastRepositoryDetail_Update_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepositoryDetail_Update_Sp = Nothing
'
'
'    If Not (AIM_ForecastRepository_Update_Sp Is Nothing) Then Set AIM_ForecastRepository_Update_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_Update_Sp = Nothing
'
'     If Not (AIM_BatchFcst_Save_Sp Is Nothing) Then Set AIM_BatchFcst_Save_Sp.ActiveConnection = Nothing
'    Set AIM_BatchFcst_Save_Sp = Nothing
'
'    If Not (AIM_ForecastFreezePds_Get_Sp Is Nothing) Then Set AIM_ForecastFreezePds_Get_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastFreezePds_Get_Sp = Nothing
'
'    If Not (AIM_Item_GetEq_Sp Is Nothing) Then Set AIM_Item_GetEq_Sp.ActiveConnection = Nothing
'    Set AIM_Item_GetEq_Sp = Nothing
'
'    If Not (AIM_ForecastUserAccess_Validate_Sp Is Nothing) Then Set AIM_ForecastUserAccess_Validate_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastUserAccess_Validate_Sp = Nothing
'
'    If Not (AIM_ForecastRepository_List_Sp Is Nothing) Then Set AIM_ForecastRepository_List_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_List_Sp = Nothing
'
'    If Not (AIM_ForecastFreezeItem_GetEq_Sp Is Nothing) Then Set AIM_ForecastFreezeItem_GetEq_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastFreezeItem_GetEq_Sp = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
'    Set rsForecast = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsFcstId) Then rsFcstId.Close
'    Set rsFcstId = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsFcstRepos) Then rsFcstRepos.Close
'    Set rsFcstRepos = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsFreezeItem) Then rsFreezeItem.Close
'    Set rsFreezeItem = Nothing
'
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
'    Set Cn = Nothing
'
'    Set xForeCast = Nothing
'    Set xForecastNr = Nothing
'    Set xAdjForeCast = Nothing
'    Set xAdjForecastNr = Nothing
'    Set xPlannedrct = Nothing
'    Set xMainArray = Nothing
'    Set xSumArray = Nothing
'    Set xFcstRepos = Nothing
'    Set xFcstNetRepos = Nothing
'    Set xDraftRepos = Nothing
'    Set xDraftNetRepos = Nothing
'    Set xBudgetRepos = Nothing
'    Set xBudgetNetRepos = Nothing
'    Set xSales = Nothing
'    Set xInventory = Nothing
'    Set xRowChange = Nothing
'    Set xNetReq = Nothing
'    Set xStatArray = Nothing
'    Set xFilteredItems = Nothing
'    Set xSalesLY = Nothing
'
'
'    'Remove form from window list
'    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        'Ignore
'    Else
'        f_HandleErr Me.Caption & "(Form_Unload)"
'    End If
'    Resume Next
'End Sub
'
'
'Private Sub mnuEditOpt_Click(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim FileName As String
'    Dim FormatOption As Integer
'    Dim i As Long
'    Dim j As Long
'    Dim s1 As String
'    Dim s2 As String
'    Dim s3 As String
'    Dim s() As String
'    Dim RowStartIndex As Long
'    Dim RowEndIndex As Long
'    Dim ColStartIndex As Integer
'    Dim ColEndIndex As Integer
'
'    Me.MousePointer = vbHourglass
'
'    Select Case Index
'    Case 0          'Copy
'        If Whichgrid = "Items" Then
'            'Check for an empty set
'            If Me.dgItems.Rows = 0 Then
'                Screen.MousePointer = vbNormal
'                Exit Sub
'            End If
'
'            Clipboard.Clear
'
'            For i = 0 To Me.dgItems.Columns.Count - 1
'                s1 = s1 + Me.dgItems.Columns(i).Caption + vbTab
'            Next i
'
'            s1 = s1 + vbCrLf
'
'            ReDim s(xMainArray.Count(1))
'
'            RowStartIndex = xMainArray.LowerBound(1)
'            RowEndIndex = xMainArray.UpperBound(1)
'            ColStartIndex = xMainArray.LowerBound(2)
'            ColEndIndex = xMainArray.UpperBound(2)
'
'            For i = RowStartIndex To RowEndIndex
'                For j = ColStartIndex To ColEndIndex
'                    s(i) = s(i) + CStr(xMainArray.Value(i, j)) + vbTab
'                Next j
'
'            Next i
'
'            Clipboard.SetText s1 + Join(s, vbCrLf), vbCFText
'
'            Erase s
'        ElseIf Whichgrid = "Summary" Then
'            'Check for an empty set
'            If Me.dgSummary.Rows = 0 Then
'                Screen.MousePointer = vbNormal
'                Exit Sub
'            End If
'
'            Clipboard.Clear
'
'            For i = 0 To Me.dgSummary.Columns.Count - 1
'                s1 = s1 + Me.dgSummary.Columns(i).Caption + vbTab
'            Next i
'
'            s1 = s1 + vbCrLf
'
'            ReDim s(xSumArray.Count(1))
'
'            RowStartIndex = xSumArray.LowerBound(1)
'            RowEndIndex = xSumArray.UpperBound(1)
'            ColStartIndex = xSumArray.LowerBound(2)
'            ColEndIndex = xSumArray.UpperBound(2)
'
'            For i = RowStartIndex To RowEndIndex
'                For j = ColStartIndex To ColEndIndex
'                    s(i) = s(i) + CStr(xSumArray.Value(i, j)) + vbTab
'                Next j
'
'            Next i
'
'            Clipboard.SetText s1 + Join(s, vbCrLf), vbCFText
'
'            Erase s
'        End If
'
'    Case 1          'Print
'        If Whichgrid = "Items" Then
'            Me.dgItems.PrintData ssPrintAllRows, False, True
'        ElseIf Whichgrid = "Summary" Then
'            Me.dgSummary.PrintData ssPrintAllRows, False, True
'        End If
'
'    End Select
'
'    Me.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
'End Sub
'Private Function InitializeChart()
'On Error GoTo ErrorHandler
'
'    Dim Counter As Long
'
'    With Me.chtForecast
'        .IsBatched = True
'        .AllowUserChanges = False
'
'        'Set up Header
'        .Header.Text = ""
'
'        .Border.Type = oc2dBorderPlain
'        .Border.Width = 2
'
'        'Set Width of X and Y Axes
'        .ChartArea.Axes("X").Title.Text = ""
'        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
'        .ChartArea.Axes("X").MajorGrid.Spacing = 100
'        .ChartArea.Axes("X").MajorGrid.Style.Pattern = oc2dLineNone
'        .ChartArea.Axes("X").Max = 10
'        .ChartArea.Axes("X").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("X").TimeScale.Unit = oc2dTimeMonths
'        .ChartArea.Axes("X").TimeScale.Format.Value = "%d-%b-%y"
'        .ChartArea.Axes("X").AnnotationPlacement = oc2dAnnotateOrigin
'        .ChartArea.Axes("X").AnnotationRotation = oc2dRotate90Degrees
'
'        .ChartArea.Axes("Y").Title.Text = ""
'        .ChartArea.Axes("Y").TitleRotation = oc2dRotate90Degrees
'        .ChartArea.Axes("Y").AnnotationMethod = oc2dAnnotateValues
'        .ChartArea.Axes("Y").MajorGrid.Spacing = 100 '20000
'        .ChartArea.Axes("Y").MajorGrid.Style.Pattern = oc2dLineSolid
'        .ChartArea.Axes("Y").Origin = 0
'        .ChartArea.Axes("Y").AxisStyle.LineStyle.Width = 1
'
'        'Set Y2 axes properties
'        .ChartArea.Axes("Y2").Title.Text = ""
'        .ChartArea.Axes("Y2").TitleRotation = oc2dRotate90Degrees
'        .ChartArea.Axes("Y2").AnnotationMethod = oc2dAnnotateValues
'        .ChartArea.Axes("Y2").AxisStyle.LineStyle.Width = 1
'
'        'Remove any old x-axis labels
'        .ChartArea.Axes("X").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y2").ValueLabels.RemoveAll
'
'        'Set up Legends
'        .Legend.Anchor = oc2dAnchorEast
'        .Legend.IsShowing = True
'        .Legend.Border.Type = oc2dBorderPlain
'        .Legend.Border.Width = 2
'        .Legend.Text = ""
'
'        .ChartGroups(1).SeriesLabels.RemoveAll
'        .ChartGroups(2).SeriesLabels.RemoveAll
'        .ChartGroups(1).Styles.RemoveAll
'        .ChartGroups(2).Styles.RemoveAll
'        .ChartGroups(1).PointStyles.RemoveAll
'        .ChartGroups(2).PointStyles.RemoveAll
'
'        'Finalize Grid Spacing
'        .ChartArea.Axes("Y").MajorGrid.Spacing = .ChartArea.Axes("Y").NumSpacing
'
'        .IsBatched = False
'    End With
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(InitializeChart)", Err.Description
'End Function
'
'Private Function SetColors()
'On Error GoTo ErrorHandler
'
'    'Even
'    Label3.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label6.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label76.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label84.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label88.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label70.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label81.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label85.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label92.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label96.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label89.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label93.BackColor = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'
'    'Odd
'    Label4.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label71.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label82.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label86.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label99.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label75.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label74.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label90.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label94.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label97.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'    Label98.BackColor = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
'
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(SetColors)", Err.Description
'End Function
'
'
'
'
'Private Sub SSOleDBGrid1_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'
'    Dim iRBRow As Long
'    Dim iCol As Integer
'    Dim irow As Long
'    Dim iPoint As Long
'    Dim iCount As Long
'
'    If dgSummary.Enabled = False Then Exit Sub    'This should have been reset in SetColumns() before the dgitems.rebind
'
'    'Test for an empty array
'    On Error Resume Next
'    If MyArray.Count(1) = 0 Then
'        Exit Sub
'    Else
'        On Error GoTo ErrorHandler
'        iCount = MyArray.Count(1)
'        irow = 0
'    End If
'    On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            iPoint = iCount
'        Else
'            iPoint = 0
'        End If
'    Else
'        iPoint = StartLocation
'        If ReadPriorRows Then
'            iPoint = iPoint - 1
'        Else
'            iPoint = iPoint + 1
'        End If
'    End If
'
'    For iRBRow = 0 To RowBuf.RowCount - 1
'        If iPoint < 0 Or iPoint > iCount - 1 Then Exit For
'
'        'For iCol = Array.LowerBound(2) To Array.UpperBound(2)
'        For iCol = 0 To (RowBuf.ColumnCount - 1)
'            RowBuf.Value(iRBRow, iCol) = MyArray.Value(iPoint, iCol)
'        Next iCol
'
'        RowBuf.bookmark(iRBRow) = iPoint
'        If ReadPriorRows Then
'            iPoint = iPoint - 1
'        Else
'            iPoint = iPoint + 1
'        End If
'
'        irow = irow + 1
'
'    Next iRBRow
'
'    RowBuf.RowCount = irow
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgSummary_UnboundReadData)"
'
'End Sub
'
'Private Sub SSActiveTabPanel4_DragDrop(Source As Control, X As Single, Y As Single)
'
'End Sub
'
'Private Sub txtLocation_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.LcID, .SubFilterLcID, vbTextCompare) <> 0 Then
'            Me.txtLocation.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtLocation_Change)"
'End Sub
'
'Private Sub txtItemID_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.Item, .SubFilterItem, vbTextCompare) <> 0 Then
'            Me.txtItemID.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtItemID_Change)"
'End Sub
'
'Private Sub txtVendorID_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.VnId, .SubFilterVnId, vbTextCompare) <> 0 Then
'            Me.txtVendorID.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtVendorID_Change)"
'End Sub
'
'Private Sub txtClass1_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.Class1, .SubFilterClass1, vbTextCompare) <> 0 Then
'            Me.txtClass1.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtClass1_Change)"
'End Sub
'
'Private Sub txtClass2_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.Class2, .SubFilterClass2, vbTextCompare) <> 0 Then
'            Me.txtClass2.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtClass2_Change)"
'End Sub
'
'Private Sub txtClass3_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.Class3, .SubFilterClass3, vbTextCompare) <> 0 Then
'            Me.txtClass3.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtClass3_Change)"
'End Sub
'
'Private Sub txtClass4_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.Class4, .SubFilterClass4, vbTextCompare) <> 0 Then
'            Me.txtClass4.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtClass4_Change)"
'End Sub
'
'
'Private Sub txtAssort_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.Assort, .SubFilterAssort, vbTextCompare) <> 0 Then
'            Me.txtAssort.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtAssort_Change)"
'End Sub
'
'Private Sub txtItemStatus_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.ItemStatus, .SubFilterItemStatus, vbTextCompare) <> 0 Then
'            Me.txtItemStatus.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtItemStatus_Change)"
'End Sub
'
'Private Sub txtBuyerID_Change()
'On Error GoTo ErrorHandler
'
'    With m_FcstItemFilter
'        If StrComp(.ById, .SubFilterById, vbTextCompare) <> 0 Then
'            Me.txtBuyerID.ForeColor = vbRed
'        End If
'    End With
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtBuyerID_Change)"
'End Sub
'
'
'
Private Sub dgSummary_InitColumnProps()

End Sub


Private Sub Form_Load()

End Sub


