Attribute VB_Name = "AIMForecast"
'''******************************************************************
'''   This module created for the functions that are
'''   directly related to the Forecast Generation/Modification features,
'''
'''   Created Jun. 26, 2002. Annalakshmi Stocksdale
'''   Modified Dec. 10, 2003. Annalakshmi Stocksdale
'''******************************************************************
''Option Explicit
''
'''**********COPIED FROM AIMAdvForecastMod.cls**********'
''Dim AC As New AIMAdvCalendar
''Dim Cn As New ADODB.Connection
''Dim rsVnItemList As New ADODB.Recordset
''Dim rsFcstMaint As New ADODB.Recordset
''Dim rsSysCtrl As New ADODB.Recordset
'''Dim rsGetPrice As New ADODB.Recordset
''Dim AIM_GetSaVersion_Sp As New ADODB.Command
''Dim AIM_SysCtrl_Get_Sp As New ADODB.Command
''Dim AIM_CostPriceListPrice_Get_Sp As ADODB.Command
''Dim AIM_FySales_Get_Sp As ADODB.Command
'''Sri add code for prodconst start
''Dim AIM_ProdConstraintTemp_Save_Sp As New ADODB.Command
''Dim AIM_ProductionConstraintCtrl_Sp As New ADODB.Command
''Dim AIM_ProdConstraintTemp_Get_Sp As New ADODB.Command
''Dim AIM_GuId_Get_SP As New ADODB.Command
''Dim cmdDelete As New ADODB.Command
''Dim rsGetProdConstData As New Recordset
''Dim rsUniqueId As New Recordset
'''Sri add code for prodconst end
''Dim prm As ADODB.Parameter
''Dim FcstId As String
''Dim Assort As String
''Dim SAVersion As Integer
''Dim AIMYears() As AIMYEARS_RCD
''Dim AIMDays() As AIMDAYS_RCD
''Dim ActualSalesAndOrders() As ACTUALSALEORDER_RCD
''Dim BudgetOrForecast()  As BUDGETFCST_RCD
''
''Private Type ACTUALSALEORDER_RCD
''    Fy As Integer
''    Sale As Double
''    Order As Double
''    Pd As Integer
''    StartDate As Date
''    Enddate As Date
''End Type
''
''Private Type BUDGETFCST_RCD
''    Fy As Integer
''    QtyFcst As Double
''    QtyFcstNetReq As Double
''    QtyAdjust As Double
''    QtyActualOrdered As Double
''    QtyActualShipped As Double
''    Pd As Integer
''    StartDate As Date
''    Enddate As Date
''End Type
''
''Private Type FCSTORNETREQ_RCD
''    Fy As Integer
''    SysFcst As Double
''    SysNet As Double
''    ModSysFcst As Double
''    ModSysNet As Double
''    PlannedRct As Double
''    Pd As Integer
''    StartDate As Date
''    Enddate As Date
''End Type
''Private Type FCSTORNETREQ_QTY
''    SysFcst As Double
''    SysNet As Double
''    ModSysFcst As Double
''    ModSysNet As Double
''    PlannedRct As Double
''End Type
''
''Private Type BFQtyPiceCost_RCD
''    QtyFcst As Double
''    QtyFcstNetReq As Double
''    QtyAdjust As Double
''    QtyActualOrdered As Double
''    QtyActualShipped As Double
''End Type
''
''Dim BFRecord As BFQtyPiceCost_RCD
''Dim FcstOrNetReqRecord() As FCSTORNETREQ_RCD
''Dim FcstOrNetReqQty As FCSTORNETREQ_QTY
''
''Dim VnId As String      'populated from the data send by user
''Dim LcId As String      'populated from the data send by user
''Dim ItemId As String    'populated from the data send by user
''Dim FcstStartDate As Date   'populated from the data send by user
'''Dim FcstAdjPct As Double    'populated from the data send by user
''Dim NetRqmts As Boolean      'populated from the data send by user
''
'''Osama Riyahi - Start
''Dim PlannedRct As Boolean
'''Osama Riyahi - End
''
''Dim FcstInterval As AIM_INTERVALS 'populated from the data send by user
''Dim Modification As Boolean
''Dim modnetreqloc() As Double
''Dim Fcsttype As String
''Dim FcstKey As Long
''Dim SysFcstKey As Long
''Dim NetFcstKey As Long
''Dim ModificationChangeYN As Boolean 'Used to store wether the user is sending an array of forecast values or not
''Private LastFYPrice As Double   'Store Price of the item in Last Fiscal Year
''Private LastFYCost As Double    'Store Cost of the item in Last Fiscal Year
''Private ThisFYPrice As Double   'Store Price of the item in This Fiscal Year
''Private ThisFYCost As Double    'Store Cost of the item in This Fiscal Year
''Private NextFYPrice As Double   'Store Price of the item in Next Fiscal Year
''Private NextFYCost As Double    'Store Cost of the item in Next Fiscal Year
''Private strMessage As String
'''Sri add code for prodconst start
''Private ProdConstraintCalc As Boolean 'Used to find if Prodconstraint need to be applied or not
''
''Private DpCurrentStartDate As Date
''Private DpHistoricalStartDate As Date
''Private DpHistPds As Integer
''Private DpFuturePds As Integer
''
'
''******************************************************************
''   This module created for the functions that are
''   directly related to the Forecast Generation/Modification features,
''
''   Created Jun. 26, 2002. Annalakshmi Stocksdale
''   Modified Dec. 10, 2003. Annalakshmi Stocksdale
''******************************************************************
'Option Explicit
'
''**********COPIED FROM AIMAdvForecastMod.cls**********'
'Private AC As New AIMAdvCalendar
'Private Cn As New ADODB.Connection
'Private rsVnItemList As New ADODB.Recordset
'Private rsFcstMaint As New ADODB.Recordset
'Private rsSysCtrl As New ADODB.Recordset
''Private rsGetPrice As New ADODB.Recordset
'Private AIM_GetSaVersion_Sp As New ADODB.Command
'Private AIM_SysCtrl_Get_Sp As New ADODB.Command
'Private AIM_CostPriceListPrice_Get_Sp As ADODB.Command
'Private AIM_FySales_Get_Sp As ADODB.Command
''Sri add code for prodconst start
'Private AIM_ProdConstraintTemp_Save_Sp As New ADODB.Command
'Private AIM_ProductionConstraintCtrl_Sp As New ADODB.Command
'Private AIM_ProdConstraintTemp_Get_Sp As New ADODB.Command
'Private AIM_GuId_Get_SP As New ADODB.Command
'Private cmdDelete As New ADODB.Command
'Private rsGetProdConstData As New Recordset
'Private rsUniqueId As New Recordset
''Sri add code for prodconst end
'Private prm As ADODB.Parameter
'Private FcstId As String
'Private Assort As String
'Private SAVersion As Integer
'Private AIMYears() As AIMYEARS_RCD
'Private AIMDays() As AIMDAYS_RCD
'Private ActualSalesAndOrders() As ACTUALSALEORDER_RCD
'Private BudgetOrForecast()  As BUDGETFCST_RCD
'
'Private Type ACTUALSALEORDER_RCD
'    Fy As Integer
'    Sale As Double
'    Order As Double
'    Pd As Integer
'    StartDate As Date
'    Enddate As Date
'End Type
'
'Private Type BUDGETFCST_RCD
'    Fy As Integer
'    QtyFcst As Double
'    QtyFcstNetReq As Double
'    QtyAdjust As Double
'    QtyActualOrdered As Double
'    QtyActualShipped As Double
'    Pd As Integer
'    StartDate As Date
'    Enddate As Date
'End Type
'
'Private Type FCSTORNETREQ_RCD
'    Fy As Integer
'    SysFcst As Double
'    SysNet As Double
'    ModSysFcst As Double
'    ModSysNet As Double
'    PlannedRct As Double
'    Pd As Integer
'    StartDate As Date
'    Enddate As Date
'End Type
'Private Type FCSTORNETREQ_QTY
'    SysFcst As Double
'    SysNet As Double
'    ModSysFcst As Double
'    ModSysNet As Double
'    PlannedRct As Double
'End Type
'
'Private Type BFQtyPiceCost_RCD
'    QtyFcst As Double
'    QtyFcstNetReq As Double
'    QtyAdjust As Double
'    QtyActualOrdered As Double
'    QtyActualShipped As Double
'End Type
'
'Private BFRecord As BFQtyPiceCost_RCD
'Private FcstOrNetReqRecord() As FCSTORNETREQ_RCD
'Private FcstOrNetReqQty As FCSTORNETREQ_QTY
'
'Private VnId As String      'populated from the data send by user
'Private LcId As String      'populated from the data send by user
'Private ItemId As String    'populated from the data send by user
'Private FcstStartDate As Date   'populated from the data send by user
''Private FcstAdjPct As Double    'populated from the data send by user
'Private NetRqmts As Boolean      'populated from the data send by user
'
''Osama Riyahi - Start
'Private PlannedRct As Boolean
''Osama Riyahi - End
'
'Private FcstInterval As AIM_INTERVALS 'populated from the data send by user
'Private Modification As Boolean
'Private modnetreqloc() As Double
'Private Fcsttype As String
'Private FcstKey As Long
'Private SysFcstKey As Long
'Private NetFcstKey As Long
'Private ModificationChangeYN As Boolean 'Used to store wether the user is sending an array of forecast values or not
'Private LastFYPrice As Double   'Store Price of the item in Last Fiscal Year
'Private LastFYCost As Double    'Store Cost of the item in Last Fiscal Year
'Private ThisFYPrice As Double   'Store Price of the item in This Fiscal Year
'Private ThisFYCost As Double    'Store Cost of the item in This Fiscal Year
'Private NextFYPrice As Double   'Store Price of the item in Next Fiscal Year
'Private NextFYCost As Double    'Store Cost of the item in Next Fiscal Year
'Private strMessage As String
''Sri add code for prodconst start
'Private ProdConstraintCalc As Boolean 'Used to find if Prodconstraint need to be applied or not
'
'Private DpCurrentStartDate As Date
'Private DpHistoricalStartDate As Date
'Private DpHistPds As Integer
'Private DpFuturePds As Integer
'
'
'Public Function AIMAdvCalendar_Load( _
'    Cn As ADODB.Connection, _
'    p_AC As AIMAdvCalendar _
') As Integer
'On Error GoTo ErrorHandler
''>>> Check against AIM_DBIO.AIMCalendar_Load for possible duplication
'    Dim AIM_AIMDays_Load_Sp As ADODB.Command
'    Dim AIM_AIMYears_Load_Sp As ADODB.Command
'
'    Dim rsTemp As ADODB.Recordset
'
'    Dim Row As Long
'
'    'Define the Stored Procedures
'    Set AIM_AIMDays_Load_Sp = New ADODB.Command
'    With AIM_AIMDays_Load_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMDays_Load_Sp"
'        .Parameters(0).Direction = adParamReturnValue
'    End With
'
'    Set AIM_AIMYears_Load_Sp = New ADODB.Command
'    With AIM_AIMYears_Load_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMYears_Load_Sp"
'        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
'            "RETURN", adInteger, adParamReturnValue, , 0)
'        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
'            "@FiscalYear", adInteger, adParamInput, , 0)
'    End With
'
'    'Load the AIM Calendar/AIM Years
'    Set rsTemp = New ADODB.Recordset
'    With rsTemp
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Load the year collection
'    rsTemp.Open AIM_AIMYears_Load_Sp
'    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
'        Do Until rsTemp.eof
'            Row = p_AC.AddYear(rsTemp("FiscalYear").Value, rsTemp("FYStartDate").Value, rsTemp("FYEndDate").Value)
'            rsTemp.MoveNext
'        Loop
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'    Set rsTemp = New ADODB.Recordset
'    With rsTemp
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Load the days collection
'    rsTemp.Open AIM_AIMDays_Load_Sp
'
'    Row = 0
'    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
'        Do Until rsTemp.eof
'            Row = p_AC.SetDayStatus(rsTemp("FYDate").Value, rsTemp("DayStatus").Value)
'            rsTemp.MoveNext
'        Loop
'    End If
'    AIMAdvCalendar_Load = rsTemp.RecordCount
'
'    'Clean Up
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
'    Set AIM_AIMYears_Load_Sp = Nothing
'    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
'    Set AIM_AIMDays_Load_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
'    Set AIM_AIMYears_Load_Sp = Nothing
'    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
'    Set AIM_AIMDays_Load_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMForecast.AIMAdvCalendar_Load)"
'End Function
'
'Public Function GetNextStartDate( _
'    BaseDate As Date, _
'    LastStartDate As Date, _
'    Interval As AIM_INTERVALS _
') As Date
''This function was duplicated in AIMAdvForecast.cls and AIMAdvForecastMod.cls
''... but need not be inside a class module at all. Hence moved out to .bas
'On Error GoTo ErrorHandler
'
'    Dim NbrWks As Integer
'
'    'Calculate Number of Weeks between Base Date and Last Start Date
'    NbrWks = DateDiff("ww", BaseDate, LastStartDate)
'    Do Until NbrWks < 52
'        NbrWks = NbrWks - 52
'    Loop
'
'    Select Case Interval
'    Case int_Days
'        GetNextStartDate = LastStartDate + 1
'
'    Case int_Weeks
'        GetNextStartDate = DateAdd("ww", 1, LastStartDate)
'
'    Case int_Months
'        GetNextStartDate = DateAdd("m", 1, LastStartDate)
'
'    Case int_Quarters
'        GetNextStartDate = DateAdd("q", 1, LastStartDate)
'
'    Case int_544
'        Select Case NbrWks
'        Case 0, 13, 26, 39
'            GetNextStartDate = DateAdd("ww", 5, LastStartDate)
'        Case Else
'            GetNextStartDate = DateAdd("ww", 4, LastStartDate)
'        End Select
'
'    Case int_454
'        Select Case NbrWks
'        Case 4, 17, 30, 43
'            GetNextStartDate = DateAdd("ww", 5, LastStartDate)
'        Case Else
'            GetNextStartDate = DateAdd("ww", 4, LastStartDate)
'        End Select
'
'    Case int_445
'        Select Case NbrWks
'        Case 8, 21, 34, 47
'            GetNextStartDate = DateAdd("ww", 5, LastStartDate)
'        Case Else
'            GetNextStartDate = DateAdd("ww", 4, LastStartDate)
'        End Select
'
'    Case int_4Wks
'        GetNextStartDate = DateAdd("ww", 4, LastStartDate)
'
'    Case Else
'        GetNextStartDate = LastStartDate + 1
'
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMForecast.GetNextStartDate)"
'End Function
'
''**********COPIED FROM AIMAdvForecastMod.cls**********'
'
'Private Function GetNumPdsToFcst( _
'    OrgInterval As AIM_INTERVALS, _
'    OrgStartDate As Date, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim StartDate As Date
'    'Interval As AIM_INTERVALS
'    Dim CurStartdate As Date
'    Dim Enddate As Date
'    Dim NbrPdsToFcst As Integer
'    Dim Counter As Long
'
'    StartDate = OrgStartDate
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, OrgInterval)
'    Next Counter
'
'    'Date till we need to forecast
'    Enddate = CurStartdate
'    'Loop to cal number of Pds to cal with new interval
'    CurStartdate = FcstStartDate
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(FcstStartDate, CurStartdate, FcstInterval)
'        If CurStartdate > Enddate Then Exit For
'        NbrPdsToFcst = NbrPdsToFcst + 1
'    Next
'
'    GetNumPdsToFcst = NbrPdsToFcst
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetNumPdsToFcst)"
'End Function
'
'Function GetTrendOption( _
'    OptArg As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    If OptArg = 0 Then
'        GetTrendOption = 0
'    ElseIf OptArg = 1 Then
'        GetTrendOption = 1
'    Else
'        GetTrendOption = 2
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetTrendOption)"
'End Function
'
'Private Function UpdateForecast( _
'    StartDate As Date, _
'    Rows As Long, _
'    Nbrpds As Integer, _
'    Interval As AIM_INTERVALS, _
'    ApplyTrend As Integer, _
'    FcstUnit As AIM_FORECASTUNITS, _
'    FcstLevel As Integer, _
'    AdjustPct As Double, _
'    NetRqmts As Boolean, _
'    rs As ADODB.Recordset, _
'    xForeCast As XArrayDB, _
'    Optional PlannedRct As Boolean, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
''Changed by Osama Riyahi to Populate the PlannedRct array 28/09/2002
'' -- added parameters "PlannedRct" and "xPlannedrct")
'
'On Error GoTo ErrorHandler
'    Dim DisplayOption As Integer
'    Dim FcstCount As Long
'    Dim FiscalYear As Long
'    Dim Pd As Long
'    Dim I As Long
'    Dim Row As Long
'    Dim j As Long
'    Dim DataYes As Boolean
'
'    'Control Variables
'    Dim PrevItem As String
'
'    'Group Totals
'    Dim Items As Integer
'    Dim Units As Long
'    Dim Weight As Double
'    Dim Cost As Double
'    Dim Cube As Double
'    Dim AdvForecast As AIMAdvForecast
'    Dim tempFcst As Long
'    Dim CurStartdate As Date
'    Dim RowsUnitStart As Long
'    Dim ForecastLoop As Long
'    Dim PdStartDate As Date
'    Dim PdEndDate As Date
'    Dim OrgNbrPds As Integer
'    Dim AdjUnit As Double
'    Dim AdjCost As Double
'    Dim AdjCube As Double
'    Dim AdjWeight As Double
'    Dim AdjPrice As Double
'
'
'    OrgNbrPds = Nbrpds
'    'Use the stardate supplied by the user
'    'send the original values to this function to get the updated periods
'    Nbrpds = GetNumPdsToFcst(Interval, StartDate, Nbrpds)
'    'Now change the startdate and interval to the values we want the forecast
'    'StartDate = FcstStartdate
'    'Interval = FcstInterval
'    'Load the AIM Calendar
'    AIMAdvCalendar_Load Cn, AC
'    RowsUnitStart = 0               'Starting position for Unit data
'
'    If Modification = True _
'    And ModificationChangeYN = False Then
'        GetRepositoryKey
'    End If
'
'    'Load AIMYears Record array
'    AC_LoadAIMYears Cn, AIMYears()  'LoadAIMYears
'    'Redimension the Forecast Array
'    xForeCast.ReDim 0, (RowsUnitStart + Rows - 1), 0, (Nbrpds + 20)
'
'    'Osama Riyahi - Begin
'    If PlannedRct Then
'        xPlannedrct.ReDim 0, (RowsUnitStart + Rows - 1), 0, (Nbrpds + 20)
'    End If
'    'Osama Riyahi- End
'
'    For Row = 0 To (Rows - 1)
'        'Position the recordset
'        PdStartDate = StartDate
'        PdEndDate = StartDate
'        rs.AbsolutePosition = Row + 1
'
'        'Reintialize the forecast object
'        Set AdvForecast = New AIMAdvForecast
'        'Set the Calendar
'        Set AdvForecast.AIMAdvCalendar = AC
'
'        'Get the Base Date
'        FiscalYear = rs("fcstupdcyc").Value \ 100  'Forecast Update Period
'        Pd = rs("fcstupdcyc").Value - (FiscalYear * 100)
'        AdvForecast.ApplySeasonsIndex = IIf(rs("applyseasonsindex").Value = "Y", True, False)
'        AdvForecast.ApplyTrend = ApplyTrend
'        AdvForecast.BaseDate = AC.GetEndDateFmPeriod(CInt(Pd), CInt(FiscalYear)) + 1
'        AdvForecast.Cost = rs("cost").Value
'        AdvForecast.Cube = rs("cube").Value
'        AdvForecast.DmdScalingFactor = rs("dmdscalingfactor").Value
'        AdvForecast.ScalingEffUntil = rs("scalingeffuntil").Value
'        AdvForecast.FcstDemand = rs("fcstdemand").Value
'        AdvForecast.FcstUnit = FcstUnit
'        AdvForecast.HiTrndDemandLimit = rs("hitrndl").Value
'        AdvForecast.Item = rs("item").Value
'        AdvForecast.ItStat = rs("itstat").Value
'        AdvForecast.InActDate = rs("inactdate").Value
'        AdvForecast.LcId = rs("lcid").Value
'        AdvForecast.PlnTT = rs("plntt").Value
'        AdvForecast.UserFcst = rs("userfcst").Value
'        AdvForecast.UserFcstExpDate = rs("userfcstexpdate").Value
'        AdvForecast.MAE = rs("mae").Value
'        AdvForecast.Trend = rs("trend").Value
'        AdvForecast.Weight = rs("weight").Value
'        AdvForecast.AdjustPct = AdjustPct
'        AdvForecast.ActDate = rs("actdate").Value
'
'        'Set Availability Variables
'        AdvForecast.UOM = rs("uom").Value
'        AdvForecast.ConvFactor = rs("convfactor").Value
'        AdvForecast.BuyingUOM = rs("buyinguom").Value
'        AdvForecast.Oh = rs("oh").Value
'        AdvForecast.Oo = rs("oo").Value
'        AdvForecast.ComStk = rs("comstk").Value
'        AdvForecast.BkOrder = rs("bkorder").Value
'        AdvForecast.BkComStk = rs("bkcomstk").Value
'
'        'Set Order Policy Variables
'        'Osama 10/08/2002 Buyer Strategy for the Net Req-Start
'        AdvForecast.BuyStrat = rs("BuyStrat").Value
'        AdvForecast.UserMin = rs("UserMin").Value
'        AdvForecast.UserMax = rs("UserMax").Value
'        AdvForecast.DIFlag = rs("DIFlag").Value
'        AdvForecast.DIMADP = rs("DIMADP").Value
'        'Osama 10/08/2002 Buyer Strategy for the Net Req-End
'        AdvForecast.Accum_Lt = rs("accum_lt").Value
'        AdvForecast.ReviewTime = rs("reviewtime").Value
'        AdvForecast.PackRounding = rs("packrounding").Value
'        AdvForecast.IMin = rs("imin").Value
'        AdvForecast.IMax = rs("imax").Value
'        AdvForecast.CStock = rs("cstock").Value
'        AdvForecast.IntSafetyStock = rs("intsafetystock").Value
'        AdvForecast.IsIntermittent = IIf(rs("isintermittent").Value = "Y", True, False)
'        AdvForecast.Mean_NZ = rs("mean_nz").Value
'        AdvForecast.StdDev_NZ = rs("stddev_nz").Value
'        AdvForecast.ReplenCost2 = rs("replencost2").Value
'        AdvForecast.SSAdj = rs("ssadj").Value
'        AdvForecast.ZSStock = rs("zsstock").Value
'        AdvForecast.LtvFact = rs("ltvfact").Value
'        AdvForecast.DSer = rs("dser").Value
'        AdvForecast.HiMADP = rs("himadp").Value
'        AdvForecast.LoMADP = rs("lomadp").Value
'        AdvForecast.MADExK = rs("madexk").Value
'        AdvForecast.ReplenCost = rs("replencost").Value
'        AdvForecast.Int_Enabled = rsSysCtrl("Int_Enabled").Value
'        AdvForecast.Int_LookBackPds = rsSysCtrl("Int_LookBackPds").Value
'        AdvForecast.KFactor = rsSysCtrl("KFactor").Value
'        AdvForecast.CarryFwdRoundingOption = IIf(rsSysCtrl("CarryForwardRounding").Value = "Y", True, False)
'
'        'Set Review Cycle Parameters
'        AdvForecast.NextDateTime = rs("nextdatetime").Value
'        AdvForecast.RevFreq = rs("revfreq").Value
'        AdvForecast.RevInterval = rs("revinterval").Value
'        AdvForecast.RevSunday = rs("revsunday").Value
'        AdvForecast.RevMonday = rs("revmonday").Value
'        AdvForecast.RevTuesday = rs("revtuesday").Value
'        AdvForecast.RevWednesday = rs("revwednesday").Value
'        AdvForecast.RevThursday = rs("revthursday").Value
'        AdvForecast.RevFriday = rs("revfriday").Value
'        AdvForecast.RevSaturday = rs("revsaturday").Value
'        AdvForecast.WeekQualifier = rs("weekqualifier").Value
'        AdvForecast.RevStartDate = rs("revstartdate").Value
'        AdvForecast.RevEndDate = rs("revenddate").Value
'        AdvForecast.InitBuyPct = rs("initbuypct").Value
'        AdvForecast.InitRevDate = rs("initrevdate").Value
'        AdvForecast.ReviewTime = rs("reviewtime").Value
'        AdvForecast.Dft_TurnHigh = rs("dft_turnhigh").Value
'        AdvForecast.Dft_TurnLow = rs("dft_turnlow").Value
'        AdvForecast.StkDate = rs("stkdate").Value
'
'        'Set Promotional Data
'        If rs("pmstatus").Value Then
'            AdvForecast.PmStatus = rs("pmstatus").Value
'            AdvForecast.PmStartDate = rs("pmstartdate").Value
'            AdvForecast.PmEndDate = rs("pmenddate").Value
'            For I = 1 To 52
'                AdvForecast.PmAdj(I) = rs(102 + I).Value
'            Next I
'        End If
'        For I = 1 To 52
'            AdvForecast.Bi(I) = rs(44 + I).Value
'        Next I
'        'Set Item Price Breaks
'        For I = 1 To 10
'            AdvForecast.BkQty(I) = rs(190 + (2 * I)).Value
'            AdvForecast.BkCost(I) = rs(191 + (2 * I)).Value
'        Next I
'
'        'Set Net Requirements
'        AdvForecast.NetRqmtsOpt = NetRqmts
'        If Modification = True Then
'            AdvForecast.Modification = True
'            AdvForecast.FcstKey = FcstKey
'        End If
'        AdvForecast.ModificationChangeYN = ModificationChangeYN
'        strMessage = getTranslationResource("STATMSG90800")
'        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
'        Write_Message strMessage & " " & Trim$(AdvForecast.LcId) & getTranslationResource("/") & AdvForecast.Item
'        If Modification = True And NetRqmts = False Then
'            'Populate the BudgetFcst record with data
'            DataYes = PopulateBudgetFcstRcdQty(FcstKey, rs("lcid").Value, rs("item").Value, Nbrpds)
'        End If
'
'        'populate the original values of interval and startdate into new variables
'        AdvForecast.OrgStartDate = StartDate
'        AdvForecast.OrgInterval = Interval
'        AdvForecast.OrgNbrPds = OrgNbrPds
'        'If netreq for a function is called then send the array data to AdvForecast class
'        If ModificationChangeYN = True Then
'            AdvForecast.GetModNetReq modnetreqloc()
'        End If
'        FcstCount = AdvForecast.GenerateForecast(FcstStartDate, Nbrpds, FcstInterval)
'
'        xForeCast.Value(Row, 0) = rs("lcid").Value
'        xForeCast.Value(Row, 1) = rs("item").Value
'        xForeCast.Value(Row, 2) = rs("itdesc").Value
'        xForeCast.Value(Row, 3) = rs("Class1").Value
'        xForeCast.Value(Row, 4) = rs("Class2").Value
'        xForeCast.Value(Row, 5) = rs("Class3").Value
'        xForeCast.Value(Row, 6) = rs("Class4").Value
'        xForeCast.Value(Row, 7) = rs("ItStat").Value
'        xForeCast.Value(Row, 8) = rs("VelCode").Value
'        xForeCast.Value(Row, 9) = rs("VnId").Value
'        xForeCast.Value(Row, 10) = rs("Assort").Value
'        xForeCast.Value(Row, 11) = rs("ById").Value
'
'        'Osama Riyahi - Begin plannedrct
'        If PlannedRct Then
'            xPlannedrct.Value(Row, 0) = rs("lcid").Value
'            xPlannedrct.Value(Row, 1) = rs("item").Value
'            xPlannedrct.Value(Row, 2) = rs("itdesc").Value
'            xPlannedrct.Value(Row, 3) = rs("Class1").Value
'            xPlannedrct.Value(Row, 4) = rs("Class2").Value
'            xPlannedrct.Value(Row, 5) = rs("Class3").Value
'            xPlannedrct.Value(Row, 6) = rs("Class4").Value
'            xPlannedrct.Value(Row, 7) = rs("ItStat").Value
'            xPlannedrct.Value(Row, 8) = rs("VelCode").Value
'            xPlannedrct.Value(Row, 9) = rs("VnId").Value
'            xPlannedrct.Value(Row, 10) = rs("Assort").Value
'            xPlannedrct.Value(Row, 11) = rs("ById").Value
'        End If
'        'Osama Riyahi - End
'
'        'Update Totals
'        'Units
'        xForeCast.Value(RowsUnitStart, 12) = IIf(NetRqmts, AdvForecast.xTotalUnits, AdvForecast.TotalUnits)
'
'        PdStartDate = StartDate
'        GetFYPriceCost rs("lcid").Value, rs("Item").Value
'        For I = 13 To (Nbrpds + 12)
'            If NetRqmts Then
'                'Osama 08/10/2002 check the ActDate of the Item
'                PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, FcstInterval)
'                'If (rs("ActDate") - rs("accum_lt").Value <= PdStartDate Or (rs("ActDate") - rs("accum_lt").Value > PdStartDate And rs("ActDate") - rs("accum_lt").Value < PdEndDate)) Then
'    '***************xForeCast.Value(RowsUnitStart, i) = AdvForecast.NetRqmt(i - 12, FcstUnits)
'                    xForeCast.Value(RowsUnitStart, I) = Round(AdvForecast.NetRqmt(I - 12, FcstUnits), 0)
'                'Else
'                '    xForeCast.Value(RowsUnitStart, i) = 0
'                'End If
'                'Osama Riyahi - Start
'                If PlannedRct Then
'                'If (rs("ActDate") <= PdStartDate Or (rs("ActDate") - rs("accum_lt").Value > PdStartDate And rs("ActDate") - rs("accum_lt").Value < PdEndDate)) Then
'                '*******************xPlannedrct.Value(RowsUnitStart, i) =AdvForecast.PlannedRcpts(i - 12, FcstUnits)
'                    xPlannedrct.Value(RowsUnitStart, I) = Round(AdvForecast.PlannedRcpts(I - 12, FcstUnits), 0)
'                'Else
'                '    xPlannedRct.Value(RowsUnitStart, i) = 0
'                'End If
'                End If
'                'Osama Riyahi - end
'                'PdEndDate = AdvForecast.GetNextStartDate(PdStartDate, PdEndDate, FcstInterval)
'                PdStartDate = PdEndDate
'            Else
'                PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, FcstInterval)
'                If DataYes = True Then
'                    If OrgNbrPds = Nbrpds Then
'                        BFRecord.QtyAdjust = BudgetOrForecast(I - 12).QtyAdjust
'                    Else
'                        BudgetFcstBetweenDates PdStartDate, PdEndDate - 1, rs("lcid").Value, rs("item").Value
'                    End If
'                Else
'                    BFRecord.QtyAdjust = 0#
'                End If
'    '************code difference between 4.2 and 4.4 begin
'    '            If rs("ActDate") <= PdStartDate Or (rs("ActDate") > PdStartDate And rs("ActDate") < PdEndDate) Then
'    '                xForeCast.Value(RowsUnitStart, i) = AdvForecast.Fcst(i - 12, FcstUnits) + BFRecord.QtyAdjust
'    '            Else
'    '                xForeCast.Value(RowsUnitStart, i) = 0
'    '            End If
'                If (rs("ActDate") <= PdStartDate Or (rs("ActDate") > PdStartDate And rs("ActDate") < PdEndDate)) And rs("DmdUpd") = "Y" Then
'                    'xForeCast.Value(RowsUnitStart, i) = AdvForecast.Fcst(i - 12, FcstUnits) + BFRecord.QtyAdjust
'                    xForeCast.Value(RowsUnitStart, I) = AdvForecast.Fcst(I - 12, FcstUnits)
'                Else
'                    xForeCast.Value(RowsUnitStart, I) = 0
'                End If
'    '************xForeCast.Value(RowsUnitStart, i) = (xForeCast.Value(RowsUnitStart, i) + BFRecord.QtyAdjust)
'                xForeCast.Value(RowsUnitStart, I) = Round((xForeCast.Value(RowsUnitStart, I) + BFRecord.QtyAdjust), 0)
'    '************code difference between 4.2 and 4.4 end
'
'                PdStartDate = PdEndDate
'                If NetRqmts = False And Modification = True Then
'                    AdjUnit = AdjUnit + BFRecord.QtyAdjust
'                End If
'            End If
'            ' Populate the last column with ForecastUnit
'            xForeCast.Value(RowsUnitStart, Nbrpds + 13) = AdvForecast.Cube
'            xForeCast.Value(RowsUnitStart, Nbrpds + 14) = AdvForecast.Weight
'            xForeCast.Value(RowsUnitStart, Nbrpds + 15) = LastFYPrice
'            xForeCast.Value(RowsUnitStart, Nbrpds + 16) = ThisFYPrice
'            xForeCast.Value(RowsUnitStart, Nbrpds + 17) = NextFYPrice
'            xForeCast.Value(RowsUnitStart, Nbrpds + 18) = LastFYCost
'            xForeCast.Value(RowsUnitStart, Nbrpds + 19) = ThisFYCost
'            xForeCast.Value(RowsUnitStart, Nbrpds + 20) = NextFYCost
'
'            'Osama Riyahi - Start
'            If PlannedRct Then
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 13) = AdvForecast.Cube
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 14) = AdvForecast.Weight
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 15) = LastFYPrice
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 16) = ThisFYPrice
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 17) = NextFYPrice
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 18) = LastFYCost
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 19) = ThisFYCost
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 20) = NextFYCost
'            End If
'            'Osama Riyahi - End
'        Next I
'
'        'Sri code change to save only modified rows start
'        ' For Modification = True And NetRqmts = False i.e for adjsysfcst case
'        'Populate col 12 i.e Totalunits column with a flag
'        'which indicates if the item exists in reposiotry or not
'        If NetRqmts = False And Modification = True Then
'            'xForeCast.Value(RowsUnitStart, 12) = AdvForecast.TotalUnits + AdjUnit
'            If DataYes = True Then
'                'Not a new item
'                xForeCast.Value(RowsUnitStart, 12) = "N"
'            Else
'                'New Item
'                xForeCast.Value(RowsUnitStart, 12) = "Y"
'            End If
'        End If
'        'Sri code change to save only modified rows end
'        RowsUnitStart = RowsUnitStart + 1
'        AdjUnit = 0
'        'Get rid of the forecast object
'        Set AdvForecast = Nothing
'    Next Row
'
'    'Sri add code for prodconst start
'    If ProdConstraintCalc = True Then
'        RunProdConst xForeCast, Nbrpds
'    End If
'    'Sri add code for prodconst end
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.UpdateForecast)"
'End Function
'
'Private Function DPUpdateForecast( _
'    StartDate As Date, _
'    Rows As Long, _
'    NbrPds_Future As Integer, _
'    NbrPds_Hist As Integer, _
'    Interval As AIM_INTERVALS, _
'    ApplyTrend As Integer, _
'    FcstUnit As AIM_FORECASTUNITS, _
'    FcstLevel As Integer, _
'    AdjustPct As Double, _
'    NetRqmts As Boolean, _
'    rs As ADODB.Recordset, _
'    xForeCast As XArrayDB, _
'    Optional PlannedRct As Boolean, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
'
'On Error GoTo ErrorHandler
'    Dim DisplayOption As Integer
'    Dim FcstCount As Long
'    Dim FiscalYear As Long
'    Dim Pd As Long
'    Dim I As Long
'    Dim Row As Long
'    Dim j As Long
'    Dim DataYes As Boolean
'
'    'Control Variables
'    Dim PrevItem As String
'
'    'Group Totals
'    Dim Items As Integer
'    Dim Units As Long
'    Dim Weight As Double
'    Dim Cost As Double
'    Dim Cube As Double
'    Dim AdvForecast As AIMAdvForecast
'    Dim tempFcst As Long
'    Dim CurStartdate As Date
'    Dim RowsUnitStart As Long
'    Dim ForecastLoop As Long
'    Dim PdStartDate As Date
'    Dim PdEndDate As Date
'    Dim OrgNbrPds As Integer
'    Dim AdjUnit As Double
'    Dim AdjCost As Double
'    Dim AdjCube As Double
'    Dim AdjWeight As Double
'    Dim AdjPrice As Double
'    Dim Nbrpds As Integer
'    Nbrpds = NbrPds_Future + NbrPds_Hist
'
'    OrgNbrPds = Nbrpds
'    StartDate = DpHistoricalStartDate
'    Nbrpds = GetNumPdsToFcst(FcstInterval, StartDate, Nbrpds)
'    'Load the AIM Calendar
'    AIMAdvCalendar_Load Cn, AC
'    RowsUnitStart = 0
'    If Modification = True _
'    And ModificationChangeYN = False Then
'        GetRepositoryKey
'    End If
'    'Load AIMYears Record array
'    AC_LoadAIMYears Cn, AIMYears()  'LoadAIMYears
'    'Redimension the Forecast Array
'    xForeCast.ReDim 0, (RowsUnitStart + Rows - 1), 0, (Nbrpds + 20)
''    DPGetRollingFcstStartDate StartDate, Interval
'    'Osama Riyahi - Begin
'    If PlannedRct Then
'        xPlannedrct.ReDim 0, (RowsUnitStart + Rows - 1), 0, (Nbrpds + 20)
'    End If
'    'Osama Riyahi- End
'
'    For Row = 0 To (Rows - 1)
'        'Position the recordset
'        PdStartDate = StartDate
'        PdEndDate = StartDate
'        rs.AbsolutePosition = Row + 1
'
'        'Reintialize the forecast object
'        Set AdvForecast = New AIMAdvForecast
'        'Set the Calendar
'        Set AdvForecast.AIMAdvCalendar = AC
'
'        'Get the Base Date
'        FiscalYear = rs("fcstupdcyc").Value \ 100  'Forecast Update Period
'        Pd = rs("fcstupdcyc").Value - (FiscalYear * 100)
'        AdvForecast.ApplySeasonsIndex = IIf(rs("applyseasonsindex").Value = "Y", True, False)
'        AdvForecast.ApplyTrend = ApplyTrend
'        AdvForecast.BaseDate = AC.GetEndDateFmPeriod(CInt(Pd), CInt(FiscalYear)) + 1
'        AdvForecast.Cost = rs("cost").Value
'        AdvForecast.Cube = rs("cube").Value
'        AdvForecast.DmdScalingFactor = rs("dmdscalingfactor").Value
'        AdvForecast.ScalingEffUntil = rs("scalingeffuntil").Value
'        AdvForecast.FcstDemand = rs("fcstdemand").Value
'        AdvForecast.FcstUnit = FcstUnit
'        AdvForecast.HiTrndDemandLimit = rs("hitrndl").Value
'        AdvForecast.Item = rs("item").Value
'        AdvForecast.ItStat = rs("itstat").Value
'        AdvForecast.InActDate = rs("inactdate").Value
'        AdvForecast.LcId = rs("lcid").Value
'        AdvForecast.PlnTT = rs("plntt").Value
'        AdvForecast.UserFcst = rs("userfcst").Value
'        AdvForecast.UserFcstExpDate = rs("userfcstexpdate").Value
'        AdvForecast.MAE = rs("mae").Value
'        AdvForecast.Trend = rs("trend").Value
'        AdvForecast.Weight = rs("weight").Value
'        AdvForecast.AdjustPct = AdjustPct
'        AdvForecast.ActDate = rs("actdate").Value
'
'        'Set Availability Variables
'        AdvForecast.UOM = rs("uom").Value
'        AdvForecast.ConvFactor = rs("convfactor").Value
'        AdvForecast.BuyingUOM = rs("buyinguom").Value
'        AdvForecast.Oh = rs("oh").Value
'        AdvForecast.Oo = rs("oo").Value
'        AdvForecast.ComStk = rs("comstk").Value
'        AdvForecast.BkOrder = rs("bkorder").Value
'        AdvForecast.BkComStk = rs("bkcomstk").Value
'
'        'Set Order Policy Variables
'        AdvForecast.BuyStrat = rs("BuyStrat").Value
'        AdvForecast.UserMin = rs("UserMin").Value
'        AdvForecast.UserMax = rs("UserMax").Value
'        AdvForecast.DIFlag = rs("DIFlag").Value
'        AdvForecast.DIMADP = rs("DIMADP").Value
'        'Osama 10/08/2002 Buyer Strategy for the Net Req-End
'        AdvForecast.Accum_Lt = rs("accum_lt").Value
'        AdvForecast.ReviewTime = rs("reviewtime").Value
'        AdvForecast.PackRounding = rs("packrounding").Value
'        AdvForecast.IMin = rs("imin").Value
'        AdvForecast.IMax = rs("imax").Value
'        AdvForecast.CStock = rs("cstock").Value
'        AdvForecast.IntSafetyStock = rs("intsafetystock").Value
'        AdvForecast.IsIntermittent = IIf(rs("isintermittent").Value = "Y", True, False)
'        AdvForecast.Mean_NZ = rs("mean_nz").Value
'        AdvForecast.StdDev_NZ = rs("stddev_nz").Value
'        AdvForecast.ReplenCost2 = rs("replencost2").Value
'        AdvForecast.SSAdj = rs("ssadj").Value
'        AdvForecast.ZSStock = rs("zsstock").Value
'        AdvForecast.LtvFact = rs("ltvfact").Value
'        AdvForecast.DSer = rs("dser").Value
'        AdvForecast.HiMADP = rs("himadp").Value
'        AdvForecast.LoMADP = rs("lomadp").Value
'        AdvForecast.MADExK = rs("madexk").Value
'        AdvForecast.ReplenCost = rs("replencost").Value
'        AdvForecast.Int_Enabled = rsSysCtrl("Int_Enabled").Value
'        AdvForecast.Int_LookBackPds = rsSysCtrl("Int_LookBackPds").Value
'        AdvForecast.KFactor = rsSysCtrl("KFactor").Value
'        AdvForecast.CarryFwdRoundingOption = IIf(rsSysCtrl("CarryForwardRounding").Value = "Y", True, False)
'
'        'Set Review Cycle Parameters
'        AdvForecast.NextDateTime = rs("nextdatetime").Value
'        AdvForecast.RevFreq = rs("revfreq").Value
'        AdvForecast.RevInterval = rs("revinterval").Value
'        AdvForecast.RevSunday = rs("revsunday").Value
'        AdvForecast.RevMonday = rs("revmonday").Value
'        AdvForecast.RevTuesday = rs("revtuesday").Value
'        AdvForecast.RevWednesday = rs("revwednesday").Value
'        AdvForecast.RevThursday = rs("revthursday").Value
'        AdvForecast.RevFriday = rs("revfriday").Value
'        AdvForecast.RevSaturday = rs("revsaturday").Value
'        AdvForecast.WeekQualifier = rs("weekqualifier").Value
'        AdvForecast.RevStartDate = rs("revstartdate").Value
'        AdvForecast.RevEndDate = rs("revenddate").Value
'        AdvForecast.InitBuyPct = rs("initbuypct").Value
'        AdvForecast.InitRevDate = rs("initrevdate").Value
'        AdvForecast.ReviewTime = rs("reviewtime").Value
'        AdvForecast.Dft_TurnHigh = rs("dft_turnhigh").Value
'        AdvForecast.Dft_TurnLow = rs("dft_turnlow").Value
'        AdvForecast.StkDate = rs("stkdate").Value
'
'        'Set Promotional Data
'        If rs("pmstatus").Value Then
'            AdvForecast.PmStatus = rs("pmstatus").Value
'            AdvForecast.PmStartDate = rs("pmstartdate").Value
'            AdvForecast.PmEndDate = rs("pmenddate").Value
'            For I = 1 To 52
'                AdvForecast.PmAdj(I) = rs(102 + I).Value
'            Next I
'        End If
'        For I = 1 To 52
'            AdvForecast.Bi(I) = rs(44 + I).Value
'        Next I
'        'Set Item Price Breaks
'        For I = 1 To 10
'            AdvForecast.BkQty(I) = rs(190 + (2 * I)).Value
'            AdvForecast.BkCost(I) = rs(191 + (2 * I)).Value
'        Next I
'
'        'Set Net Requirements
'        AdvForecast.NetRqmtsOpt = NetRqmts
'        If Modification = True Then
'            AdvForecast.Modification = True
'            AdvForecast.FcstKey = FcstKey
'        End If
'        AdvForecast.ModificationChangeYN = ModificationChangeYN
'        strMessage = getTranslationResource("STATMSG90800")
'        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
'        Write_Message strMessage & " " & Trim$(AdvForecast.LcId) & getTranslationResource("/") & AdvForecast.Item
'        'If Modification = True And NetRqmts = False Then
'            DataYes = DpPopulateBudgetFcstRcdQty(FcstKey, rs("lcid").Value, rs("item").Value, Nbrpds, Interval)
'        'End If
'
'        'populate the original values of interval and startdate into new variables
'        AdvForecast.OrgStartDate = StartDate
'        AdvForecast.OrgInterval = Interval
'        AdvForecast.OrgNbrPds = OrgNbrPds
'        'If netreq for a function is called then send the array data to AdvForecast class
'        If ModificationChangeYN = True Then
'            AdvForecast.GetModNetReq modnetreqloc()
'        End If
'        FcstCount = AdvForecast.GenerateForecast(FcstStartDate, Nbrpds, FcstInterval)
'
'        xForeCast.Value(Row, 0) = rs("lcid").Value
'        xForeCast.Value(Row, 1) = rs("item").Value
'        xForeCast.Value(Row, 2) = rs("itdesc").Value
'        xForeCast.Value(Row, 3) = rs("Class1").Value
'        xForeCast.Value(Row, 4) = rs("Class2").Value
'        xForeCast.Value(Row, 5) = rs("Class3").Value
'        xForeCast.Value(Row, 6) = rs("Class4").Value
'        xForeCast.Value(Row, 7) = rs("ItStat").Value
'        xForeCast.Value(Row, 8) = rs("VelCode").Value
'        xForeCast.Value(Row, 9) = rs("VnId").Value
'        xForeCast.Value(Row, 10) = rs("Assort").Value
'        xForeCast.Value(Row, 11) = rs("ById").Value
'
'        'Osama Riyahi - Begin plannedrct
'        If PlannedRct Then
'            xPlannedrct.Value(Row, 0) = rs("lcid").Value
'            xPlannedrct.Value(Row, 1) = rs("item").Value
'            xPlannedrct.Value(Row, 2) = rs("itdesc").Value
'            xPlannedrct.Value(Row, 3) = rs("Class1").Value
'            xPlannedrct.Value(Row, 4) = rs("Class2").Value
'            xPlannedrct.Value(Row, 5) = rs("Class3").Value
'            xPlannedrct.Value(Row, 6) = rs("Class4").Value
'            xPlannedrct.Value(Row, 7) = rs("ItStat").Value
'            xPlannedrct.Value(Row, 8) = rs("VelCode").Value
'            xPlannedrct.Value(Row, 9) = rs("VnId").Value
'            xPlannedrct.Value(Row, 10) = rs("Assort").Value
'            xPlannedrct.Value(Row, 11) = rs("ById").Value
'        End If
'        'Osama Riyahi - End
'
'        'Update Totals
'        'Units
'        xForeCast.Value(RowsUnitStart, 12) = IIf(NetRqmts, AdvForecast.xTotalUnits, AdvForecast.TotalUnits)
'
'        PdStartDate = StartDate
'        GetFYPriceCost rs("lcid").Value, rs("Item").Value
'        For I = 13 To (Nbrpds + 12)
'            If NetRqmts Then
'                'Osama 08/10/2002 check the ActDate of the Item
'                PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, FcstInterval)
'                    xForeCast.Value(RowsUnitStart, I) = Round(AdvForecast.NetRqmt(I - 12, FcstUnits), 0)
'                If PlannedRct Then
'                    xPlannedrct.Value(RowsUnitStart, I) = Round(AdvForecast.PlannedRcpts(I - 12, FcstUnits), 0)
'                End If
'                PdStartDate = PdEndDate
'            Else
'                PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, FcstInterval)
'                If DataYes = True Then
''                    If OrgNbrPds = NbrPds Then
''                        BFRecord.QtyAdjust = BudgetOrForecast(I - 12).QtyAdjust
''                    Else
'                        BudgetFcstBetweenDates PdStartDate, PdEndDate - 1, rs("lcid").Value, rs("item").Value
''                    End If
'                Else
'                    BFRecord.QtyAdjust = 0#
'                End If
'                If I < 13 + DpHistPds Then
'
'                    xForeCast.Value(RowsUnitStart, I) = BFRecord.QtyFcst
'                Else
'
'
'                    If (rs("ActDate") <= PdStartDate Or (rs("ActDate") > PdStartDate And rs("ActDate") < PdEndDate)) And rs("DmdUpd") = "Y" Then
'                        'xForeCast.Value(RowsUnitStart, i) = AdvForecast.Fcst(i - 12, FcstUnits) + BFRecord.QtyAdjust
'                        xForeCast.Value(RowsUnitStart, I) = AdvForecast.Fcst(I - 12, FcstUnits)
'                    Else
'                        xForeCast.Value(RowsUnitStart, I) = 0
'                    End If
'                End If
'
'                xForeCast.Value(RowsUnitStart, I) = Round((xForeCast.Value(RowsUnitStart, I) + BFRecord.QtyAdjust), 0)
'                PdStartDate = PdEndDate
'                If NetRqmts = False And Modification = True Then
'                    AdjUnit = AdjUnit + BFRecord.QtyAdjust
'                End If
'            End If
'            ' Populate the last column with ForecastUnit
'            xForeCast.Value(RowsUnitStart, Nbrpds + 13) = AdvForecast.Cube
'            xForeCast.Value(RowsUnitStart, Nbrpds + 14) = AdvForecast.Weight
'            xForeCast.Value(RowsUnitStart, Nbrpds + 15) = LastFYPrice
'            xForeCast.Value(RowsUnitStart, Nbrpds + 16) = ThisFYPrice
'            xForeCast.Value(RowsUnitStart, Nbrpds + 17) = NextFYPrice
'            xForeCast.Value(RowsUnitStart, Nbrpds + 18) = LastFYCost
'            xForeCast.Value(RowsUnitStart, Nbrpds + 19) = ThisFYCost
'            xForeCast.Value(RowsUnitStart, Nbrpds + 20) = NextFYCost
'
'            'Osama Riyahi - Start
'            If PlannedRct Then
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 13) = AdvForecast.Cube
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 14) = AdvForecast.Weight
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 15) = LastFYPrice
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 16) = ThisFYPrice
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 17) = NextFYPrice
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 18) = LastFYCost
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 19) = ThisFYCost
'                xPlannedrct.Value(RowsUnitStart, Nbrpds + 20) = NextFYCost
'            End If
'            'Osama Riyahi - End
'        Next I
'
'        If NetRqmts = False And Modification = True Then
'            'xForeCast.Value(RowsUnitStart, 12) = AdvForecast.TotalUnits + AdjUnit
'            If DataYes = True Then
'                'Not a new item
'                xForeCast.Value(RowsUnitStart, 12) = "N"
'            Else
'                'New Item
'                xForeCast.Value(RowsUnitStart, 12) = "Y"
'            End If
'        End If
'        RowsUnitStart = RowsUnitStart + 1
'        AdjUnit = 0
'        'Get rid of the forecast object
'        Set AdvForecast = Nothing
'    Next Row
'
'    If ProdConstraintCalc = True Then
'        RunProdConst xForeCast, Nbrpds
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.DPUpdateForecast)"
'End Function
'
'
'Private Function f_AIMForecast_GetKey( _
'    ByRef rs As ADODB.Recordset, _
'    p_FcstId As String, _
'    ByVal Action As SQL_ACTIONS _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_Forecast_GetKey_Sp As ADODB.Command
'
'    Set AIM_Forecast_GetKey_Sp = New ADODB.Command
'    With AIM_Forecast_GetKey_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_Forecast_GetKey_Sp"
'    End With
'
'    'Destroy any existing stuff
'    On Error Resume Next
'    If f_IsRecordsetValidAndOpen(rs) Then rs.Close
'        If Err.Number = 3219 Then
'            'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
'            'Discard the rs and move on.
'            Set rs = Nothing
'        End If
'    Set rs = Nothing
'    On Error GoTo ErrorHandler
'
'    'Re-create the recordset from scratch
'    Set rs = New ADODB.Recordset
'
'    'Set cursor and lock
'    With rs
'        .CursorLocation = adUseClient
'        .CursorType = adOpenDynamic
'        .LockType = adLockOptimistic
'    End With
'
'    'Get the Seasonality Data
'    With AIM_Forecast_GetKey_Sp
'        .Parameters.Refresh
'        .Parameters("@FcstId").Value = p_FcstId
'        .Parameters("@Action").Value = Action
'        .Parameters(0).Value = 0
'
'        'Fetch in recordset
'        rs.Open AIM_Forecast_GetKey_Sp
'
'        RtnCode = .Parameters(0).Value
'        If f_IsRecordsetOpenAndPopulated(rs) Then
'            p_FcstId = rs!FcstId
'        Else
'            p_FcstId = ""
'            f_AIMForecast_GetKey = RtnCode
'            Exit Function
'        End If
'    End With
'
'    f_AIMForecast_GetKey = SUCCEED
'
'    If Not (AIM_Forecast_GetKey_Sp Is Nothing) Then Set AIM_Forecast_GetKey_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetKey_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Forecast_GetKey_Sp Is Nothing) Then Set AIM_Forecast_GetKey_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetKey_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.f_AIMForecast_GetKey)"
'End Function
'
'Private Function GenerateInitialStock( _
'    StartDate As Date, Rows As Long, _
'    Nbrpds As Integer, Interval As AIM_INTERVALS, _
'    ApplyTrend As Integer, FcstUnit As AIM_FORECASTUNITS, _
'    FcstLevel As Integer, AdjustPct As Double, _
'    NetRqmts As Boolean, rs As ADODB.Recordset, _
'    xForeCast As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'    Dim DisplayOption As Integer
'    Dim FcstCount As Long
'    Dim FiscalYear As Long
'    Dim Pd As Long
'    Dim I As Long
'    Dim Row As Long
'    Dim strMessage As String
'    Dim j As Long
'    'Control Variables
'    Dim PrevItem As String
'    'Group Totals
'    Dim Items As Integer
'    Dim Units As Long
'    Dim Weight As Double
'    Dim Cost As Double
'    Dim Cube As Double
'    Dim AdvForecast As AIMAdvForecast
'    Dim tempFcst As Long
'    Dim CurStartdate As Date
'    Dim RowsUnitStart As Long
'    Dim ForecastLoop As Long
'
'    'Load the AIM Calendar
'    AIMAdvCalendar_Load Cn, AC
'    RowsUnitStart = 0               'Starting position for Unit data
'    AC_LoadAIMYears Cn, AIMYears()  'LoadAIMYears
'    'Redimension the Forecast Array
'    xForeCast.ReDim 0, (RowsUnitStart + Rows - 1), 0, 16
'    For Row = 0 To (Rows - 1)
'        'Position the recordset
'        rs.AbsolutePosition = Row + 1
'        'Reintialize the forecast object
'        Set AdvForecast = New AIMAdvForecast
'        'Set the Calendar
'        Set AdvForecast.AIMAdvCalendar = AC
'        'Get the Base Date
'        FiscalYear = rs("fcstupdcyc").Value \ 100               'Forecast Update Period
'        Pd = rs("fcstupdcyc").Value - (FiscalYear * 100)
'        AdvForecast.ApplySeasonsIndex = IIf(rs("applyseasonsindex").Value = "Y", True, False)
'        AdvForecast.ApplyTrend = ApplyTrend
'        AdvForecast.BaseDate = AC.GetEndDateFmPeriod(CInt(Pd), CInt(FiscalYear)) + 1
'        AdvForecast.Cost = rs("cost").Value
'        AdvForecast.Cube = rs("cube").Value
'        AdvForecast.DmdScalingFactor = rs("dmdscalingfactor").Value
'        AdvForecast.ScalingEffUntil = rs("scalingeffuntil").Value
'        AdvForecast.FcstDemand = rs("fcstdemand").Value
'        AdvForecast.FcstUnit = FcstUnit
'        AdvForecast.HiTrndDemandLimit = rs("hitrndl").Value
'        AdvForecast.Item = rs("item").Value
'        AdvForecast.ItStat = rs("itstat").Value
'        AdvForecast.InActDate = rs("inactdate").Value
'        AdvForecast.LcId = rs("lcid").Value
'        'AdvForecast.PlnTT = rs("plntt").Value
'        AdvForecast.UserFcst = rs("userfcst").Value
'        AdvForecast.UserFcstExpDate = rs("userfcstexpdate").Value
'        AdvForecast.MAE = rs("mae").Value
'        AdvForecast.Trend = rs("trend").Value
'        AdvForecast.Weight = rs("weight").Value
'        AdvForecast.AdjustPct = AdjustPct
'
'        'Set Availability Variables
'        AdvForecast.UOM = rs("uom").Value
'        AdvForecast.ConvFactor = rs("convfactor").Value
'        AdvForecast.BuyingUOM = rs("buyinguom").Value
'        AdvForecast.Oh = rs("oh").Value
'        AdvForecast.Oo = rs("oo").Value
'        AdvForecast.ComStk = rs("comstk").Value
'        AdvForecast.BkOrder = rs("bkorder").Value
'        AdvForecast.BkComStk = rs("bkcomstk").Value
'
'        'Set Order Policy Variables
'        AdvForecast.Accum_Lt = rs("accum_lt").Value
'        AdvForecast.ReviewTime = rs("reviewtime").Value
'        AdvForecast.CStock = rs("cstock").Value
'        AdvForecast.IntSafetyStock = rs("intsafetystock").Value
'        AdvForecast.Int_Enabled = rsSysCtrl("Int_Enabled").Value
'        AdvForecast.Int_LookBackPds = rsSysCtrl("Int_LookBackPds").Value
'        AdvForecast.KFactor = rsSysCtrl("KFactor").Value
'        AdvForecast.CarryFwdRoundingOption = IIf(rsSysCtrl("CarryForwardRounding").Value = "Y", True, False)
'
'        AdvForecast.StkDate = rs("stkdate").Value
'
'        AdvForecast.ActDate = rs("actdate").Value
'
'        'Set Promotional Data
'        If rs("pmstatus").Value Then
'        AdvForecast.PmStatus = rs("pmstatus").Value
'        AdvForecast.PmStartDate = rs("pmstartdate").Value
'        AdvForecast.PmEndDate = rs("pmenddate").Value
'
'        For I = 1 To 52
'            AdvForecast.PmAdj(I) = rs(102 + I).Value
'            Next I
'        End If
'
'        For I = 1 To 52
'            AdvForecast.Bi(I) = rs(44 + I).Value
'        Next I
'        'Set Net Requirements
'        AdvForecast.NetRqmtsOpt = NetRqmts
'
'        'GenerateInitialStock
'        FcstCount = AdvForecast.CalcInitialStock(StartDate)
'        'Populate the initalfileds
'        xForeCast.Value(Row, 0) = rs("lcid").Value
'        xForeCast.Value(Row, 1) = rs("item").Value
'        xForeCast.Value(Row, 2) = rs("itdesc").Value
'        xForeCast.Value(Row, 3) = rs("Class1").Value
'        xForeCast.Value(Row, 4) = rs("Class2").Value
'        xForeCast.Value(Row, 5) = rs("Class3").Value
'        xForeCast.Value(Row, 6) = rs("Class4").Value
'        xForeCast.Value(Row, 7) = rs("ItStat").Value
'        xForeCast.Value(Row, 8) = rs("VelCode").Value
'        xForeCast.Value(Row, 9) = rs("VnId").Value
'        xForeCast.Value(Row, 10) = rs("Assort").Value
'        xForeCast.Value(Row, 11) = rs("ById").Value
'
'        'Update Initial Stock
'        'Units
'        xForeCast.Value(RowsUnitStart, 12) = AdvForecast.TotalUnits
'        'Get the price and cost for the item
'        GetFYPriceCost rs("lcid").Value, rs("Item").Value
'        xForeCast.Value(RowsUnitStart, 13) = AdvForecast.Cube
'        xForeCast.Value(RowsUnitStart, 14) = AdvForecast.Weight
'        xForeCast.Value(RowsUnitStart, 15) = ThisFYPrice
'        xForeCast.Value(RowsUnitStart, 16) = ThisFYCost
'        RowsUnitStart = RowsUnitStart + 1
'        'Get rid of the forecast object
'        Set AdvForecast = Nothing
'        Set AC = Nothing
'    Next Row
'
'Exit Function
'ErrorHandler:
'    Set AdvForecast = Nothing
'    Set AC = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GenerateInitialStock)"
'End Function
'
'Private Function GenerateLastFySales( _
'    StartDate As Date, _
'    rs As ADODB.Recordset, _
'    xLastFySales As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'    Dim Rows As Long
'    Dim RowCount As Long
'    Dim TotalSales As Double
'    Dim TotalPrice As Double
'    Dim TotalCost As Double
'    Dim RowsUnitStart As Long
'    Dim RowsCostStart As Long
'    Dim RowsCubeStart As Long
'    Dim RowsWeightStart As Long
'    Dim RowsPriceStart As Long
'    Dim TotalRows As Long
'    Dim Typestart As Long
'    Dim I As Long
'
'    'Get total number of rows to process
'    Rows = rs.RecordCount
'    RowsUnitStart = 0               'Starting position for Unit data
'    RowsCostStart = 1 * (Rows)      'Starting position for Cost data
'    RowsCubeStart = 2 * (Rows)      'Starting position for Cube data
'    RowsWeightStart = 3 * (Rows)    'Starting position for Weight data
'    RowsPriceStart = 4 * (Rows)     'Starting position for Sales data
'    TotalRows = (Rows * 5 - 1)  ' 5 is number of types like cast,cube etc
'    'Redimension the Forecast Array
'    xLastFySales.ReDim 0, (TotalRows), 0, 14
'    For RowCount = 0 To Rows - 1
'        rs.AbsolutePosition = RowCount + 1
'        'Initialize Stored Procedures
'        Set AIM_FySales_Get_Sp = New ADODB.Command
'        With AIM_FySales_Get_Sp
'            Set .ActiveConnection = Cn
'            .CommandType = adCmdStoredProc
'            .CommandText = "AIM_FySales_Get_Sp"
'        End With
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@Lcid", adVarChar, adParamInput, 12, rs("lcid").Value)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@Item", adVarChar, adParamInput, 25, rs("item").Value)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@Startdate", adDate, adParamInput, , StartDate)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@TotalSales", adDouble, adParamOutput, 12.2, 0)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@TotalPrice", adDouble, adParamOutput, 12.2, 0)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@TotalCost", adDouble, adParamOutput, 12.2, 0)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        Set prm = AIM_FySales_Get_Sp.CreateParameter("@TotalListPrice", adDouble, adParamOutput, 12.2, 0)
'        AIM_FySales_Get_Sp.Parameters.Append prm
'        AIM_FySales_Get_Sp.Execute
'
'        For I = 1 To 5 'five types of inital values
'            'Update data for Units
'            Typestart = (I - 1) * Rows + RowsUnitStart
'            'Update data for item
'            xLastFySales.Value(Typestart, 0) = rs("lcid").Value
'            xLastFySales.Value(Typestart, 1) = rs("item").Value
'            xLastFySales.Value(Typestart, 2) = rs("itdesc").Value
'            xLastFySales.Value(Typestart, 3) = rs("Class1").Value
'            xLastFySales.Value(Typestart, 4) = rs("Class2").Value
'            xLastFySales.Value(Typestart, 5) = rs("Class3").Value
'            xLastFySales.Value(Typestart, 6) = rs("Class4").Value
'            xLastFySales.Value(Typestart, 7) = rs("ItStat").Value
'            xLastFySales.Value(Typestart, 8) = rs("VelCode").Value
'            xLastFySales.Value(Typestart, 9) = rs("VnId").Value
'            xLastFySales.Value(Typestart, 10) = rs("Assort").Value
'            xLastFySales.Value(Typestart, 11) = rs("ById").Value
'            xLastFySales.Value(Typestart, 14) = "LYSales"
'        Next I
'
'        TotalSales = AIM_FySales_Get_Sp("@TotalSales")
'        TotalCost = AIM_FySales_Get_Sp("@TotalCost")
'        TotalPrice = AIM_FySales_Get_Sp("@TotalPrice")
'        xLastFySales.Value(RowsUnitStart, 12) = TotalSales
'        xLastFySales.Value(RowsCostStart, 12) = TotalCost
'        xLastFySales.Value(RowsCubeStart, 12) = TotalSales * rs("Cube").Value
'        xLastFySales.Value(RowsWeightStart, 12) = TotalSales * rs("Weight").Value
'        xLastFySales.Value(RowsPriceStart, 12) = TotalPrice
'        xLastFySales.Value(RowsUnitStart, 13) = "Units"
'        xLastFySales.Value(RowsCostStart, 13) = "Cost"
'        xLastFySales.Value(RowsCubeStart, 13) = "Cube"
'        xLastFySales.Value(RowsWeightStart, 13) = "Weight"
'        xLastFySales.Value(RowsPriceStart, 13) = "Price"
'        RowsUnitStart = RowsUnitStart + 1
'        RowsCostStart = RowsCostStart + 1
'        RowsCubeStart = RowsCubeStart + 1
'        RowsWeightStart = RowsWeightStart + 1
'        RowsPriceStart = RowsPriceStart + 1
'        If Not (AIM_FySales_Get_Sp Is Nothing) Then
'            Set AIM_FySales_Get_Sp.ActiveConnection = Nothing
'        End If
'        Set AIM_FySales_Get_Sp = Nothing
'    Next
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_FySales_Get_Sp Is Nothing) Then
'        Set AIM_FySales_Get_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_FySales_Get_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GenerateLastFySales)"
'End Function
'
'Function GetForecastLevel( _
'    LevelArg As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    If LevelArg = 0 Then            'vbUnChecked
'        GetForecastLevel = 0        'Location/Item
'    ElseIf LevelArg = 1 Then        'vbChecked
'        GetForecastLevel = 1        'Item
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetForecastLevel)"
'End Function
'
'Function GetForecastUnit( _
'    arg As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    Select Case arg
'    Case 0
'        GetForecastUnit = FcstUnits
'    Case 1
'        GetForecastUnit = FcstCost
'    Case 2
'        GetForecastUnit = FcstWeight
'    Case 3
'        GetForecastUnit = FcstCube
'    Case 4
'        GetForecastUnit = FcstPrice
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetForecastUnit)"
'End Function
'
'Function CleanUpMain() As Integer
'On Error GoTo ErrorHandler
'    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then
'        Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_SysCtrl_Get_Sp = Nothing
'    If Not (AIM_ProdConstraintTemp_Save_Sp Is Nothing) Then
'        Set AIM_ProdConstraintTemp_Save_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_ProdConstraintTemp_Save_Sp = Nothing
'    If Not (AIM_ProductionConstraintCtrl_Sp Is Nothing) Then
'        Set AIM_ProductionConstraintCtrl_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_ProductionConstraintCtrl_Sp = Nothing
'    If Not (AIM_ProdConstraintTemp_Get_Sp Is Nothing) Then
'        Set AIM_ProdConstraintTemp_Get_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_ProdConstraintTemp_Get_Sp = Nothing
'    If Not (AIM_GuId_Get_SP Is Nothing) Then
'        Set AIM_GuId_Get_SP.ActiveConnection = Nothing
'    End If
'    Set AIM_GuId_Get_SP = Nothing
'    If Not (cmdDelete Is Nothing) Then
'        Set cmdDelete.ActiveConnection = Nothing
'    End If
'    Set cmdDelete = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
'    Set rsSysCtrl = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsGetProdConstData) Then rsGetProdConstData.Close
'    Set rsGetProdConstData = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
'    Set rsFcstMaint = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsVnItemList) Then rsVnItemList.Close
'    Set rsVnItemList = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsUniqueId) Then rsUniqueId.Close
'    Set rsUniqueId = Nothing
'
'
'    Set AC = Nothing
'    ReDim AIMYears(0)
'    ReDim AIMDays(0)
'    'ReDim ActualSalesAndOrders(0)
'    ReDim BudgetOrForecast(0)
'    'Close the SQL Connection if not closed yet
'    SQLConnection Cn, CONNECTION_CLOSE, False
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.CleanUpMain)"
'End Function
'
''Private Sub Class_Terminate()
''On Error GoTo ErrorHandler
''
''    CleanUpMain
''
''ErrorHandler:
''    Resume Next
''End Sub
'
'Function GetLastFySales( _
'    FcstId As String, _
'    xForeCast As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'    Dim RtnCode As Integer
'
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "LastFySales")
'    GetLastFySales = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetLastFySales)"
'End Function
'
'Function GetInitialStock( _
'    FcstId As String, _
'    xForeCast As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "InitialStock")
'    GetInitialStock = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetInitalStock)"
'End Function
'
'Function GetBudgetOrForecast( _
'    FcstId As String, _
'    Fcsttype As String, _
'    FcstStartDate As Date, _
'    IntToFcst As Integer, _
'    xForeCast As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
''    RtnCode = AdvForecastMain(FcstId, xForeCast, "BudgetOrForecast", IntToFcst, Fcsttype, FcstStartDate)
'    GetBudgetOrForecast = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetBudgetOrForecast)"
'End Function
'
'Function GetBatchFcst( _
'    FcstId As String, _
'    Fcsttype As String, _
'    FcstStartDate As Date, _
'    IntToFcst As Integer, _
'    xBatchCast As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    'RtnCode = AdvForecastMain(FcstId, xBatchCast, "BatchFcst", IntToFcst, Fcsttype, FcstStartDate)
'    GetBatchFcst = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetBatchFcst)"
'End Function
'
'Public Function DPGetForecastMaint( _
'    FcstIdArg As String, _
'    xForeCast As XArrayDB, _
'    NetRqmtsYNArg As Boolean, _
'    FcstStartdateArg As Date, _
'    IntToFcstArg As AIM_INTERVALS, _
'    ModificationYNArg As Boolean, _
'    FcsttypeArg As String, _
'    PlannedRctYNArg As Boolean, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
'
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    NetRqmts = NetRqmtsYNArg 'Populate the class variable
'    PlannedRct = PlannedRctYNArg
'    'Populate the values of FcstStartdate and FcstInterval value with the arg values so that we do the
'    'calculations for the values supplied rather than the values in the forecastmaint table
'    FcstStartDate = FcstStartdateArg
'    FcstInterval = GetForecastInterval(IntToFcstArg)
'    'populate modification variable so that it will be used to calculate modifications array
'    Modification = ModificationYNArg
'    Fcsttype = FcsttypeArg
'    FcstId = FcstIdArg
'    'Call the AdvForecastMain function
'    RtnCode = DPAdvForecastMain(FcstId, xForeCast, "ForeCast", FcstInterval, , , xPlannedrct)
'    DPGetForecastMaint = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.DPGetForecastMaint)"
'End Function
'Public Function GetForecastMaint( _
'    FcstIdArg As String, _
'    xForeCast As XArrayDB, _
'    NetRqmtsYNArg As Boolean, _
'    FcstStartdateArg As Date, _
'    IntToFcstArg As AIM_INTERVALS, _
'    ModificationYNArg As Boolean, _
'    FcsttypeArg As String, _
'    PlannedRctYNArg As Boolean, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
' 'Changed by Osama Riyahi to Populate the PlannedRct array 28/09/2002
' '-- added parameters "PlannedRctYNArg" and "xPlannedRct"
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    NetRqmts = NetRqmtsYNArg 'Populate the class variable
'
'    'Osama Riyahi - Begin
'    PlannedRct = PlannedRctYNArg
'    'Osama Riyahi - End
'
'    'Populate the values of FcstStartdate and FcstInterval value with the arg values so that we do the
'    'calculations for the values supplied rather than the values in the forecastmaint table
'    FcstStartDate = FcstStartdateArg
'    FcstInterval = GetForecastInterval(IntToFcstArg)
'    'populate modification variable so that it will be used to calculate modifications array
'    Modification = ModificationYNArg
'    Fcsttype = FcsttypeArg
'    FcstId = FcstIdArg
'
'    'Call the AdvForecastMain function
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "ForeCast", , , , xPlannedrct)
'    GetForecastMaint = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetForecastMaint)"
'End Function
'
'
'Public Function GetProdConstraint( _
'    xProdConst As XArrayDB, _
'    Nbrpds As Integer _
') As Integer
''Sri add code for prodconst new function
''Call this function if we are sending the xProdConst array with vlues that are to be Production
''Constrained
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    'Connect to the database
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , "AIMForecast.bas")
'    If RtnCode <> SUCCEED Then
'        GetProdConstraint = RtnCode
'        Exit Function
'    End If
'
'    RunProdConst xProdConst, Nbrpds
'    GetProdConstraint = RtnCode
'
'    'Disconnect from the database
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , "AIMForecast.bas")
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetProdConstraint)"
'End Function
'
'Public Function GetProdConstForGenerator( _
'    xProdConst As XArrayDB, _
'    Nbrpds As Integer _
') As Integer
''Sri add code for prodconst new function
''Call this function for forccastgenerator to calc prodconst
''Constrained
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    'Connect to the database
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , "AIMForecast.bas")
'    If RtnCode <> SUCCEED Then
'        GetProdConstForGenerator = RtnCode
'        Exit Function
'    End If
'
'    RunProdConstForGenerator xProdConst, Nbrpds
'    GetProdConstForGenerator = RtnCode
'
'    'Disconnect from the database
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , "AIMForecast.bas")
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetProdConstraint)"
'End Function
'
'Public Function GenerateProdConstraint( _
'    FcstIdArg As String, _
'    xForeCast As XArrayDB, _
'    FcstStartdateArg As Date, _
'    IntToFcstArg As AIM_INTERVALS, _
'    FcsttypeArg As String _
') As Integer
''Sri add code for prodconst new function
''This fucntion first runs the forecating and then for the forecasted values it applies
''Production constriants
''currently not used may use in future
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    'Populate the values of FcstStartdate and FcstInterval value
'    'with the arg values so that we do the calculations for the values supplied
'    'rather than the values in the forecastmaint table
'    FcstStartDate = FcstStartdateArg
'    FcstInterval = GetForecastInterval(IntToFcstArg)
'    Fcsttype = FcsttypeArg
'    FcstId = FcstIdArg
'    'Set this flag which will tell that ProdConstraint should be applied
'    ProdConstraintCalc = True
'
'    'Call the AdvForecastMain function
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "ForeCast")
'    GenerateProdConstraint = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GenerateProdConstraint)"
'End Function
'
'Private Function GetSAVersion() As Integer
'On Error GoTo ErrorHandler
'
'    'Initialize Stored Procedures
'    With AIM_GetSaVersion_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GetSaVersion_Sp"
'        .Parameters.Refresh
'    End With
'
'    'Get the Seasonality Table Version Number
'    AIM_GetSaVersion_Sp.Execute , , adExecuteNoRecords
'    'Populate the SAVersion variable
'    SAVersion = AIM_GetSaVersion_Sp.Parameters("@RETURN_VALUE").Value
'    If Not (AIM_GetSaVersion_Sp Is Nothing) Then
'        Set AIM_GetSaVersion_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_GetSaVersion_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetSAVersion)"
'End Function
'
'Private Function GetFYPriceCost( _
'    LcId As String, _
'    Item As String _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim CurDate As Date
'    Dim FyYear As Integer
'    Dim Row As Long
'
'    CurDate = Date
'    FyYear = GetFiscalYear(CurDate, AIMYears())
'    For Row = LBound(AIMYears()) To UBound(AIMYears())
'        If AIMYears(Row).FiscalYear = FyYear Then
'            GetPrice AIMYears(Row).FYStartDate, AIMYears(Row).FYEndDate, LcId, Item, ThisFYPrice, ThisFYCost
'        ElseIf AIMYears(Row).FiscalYear = FyYear - 1 Then
'            GetPrice AIMYears(Row).FYStartDate, AIMYears(Row).FYEndDate, LcId, Item, LastFYPrice, LastFYCost
'        ElseIf AIMYears(Row).FiscalYear = FyYear + 1 Then
'            GetPrice AIMYears(Row).FYStartDate, AIMYears(Row).FYEndDate, LcId, Item, NextFYPrice, NextFYCost
'        End If
'    Next
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetFYPriceCost)"
'End Function
'
'Private Function BudgetFcstBetweenDates( _
'    PdStartDate As Date, _
'    PdEndDate As Date, _
'    LcId As String, _
'    ItemId As String _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim Counter As Long
'    Dim Ratio As Double
'    'Initialize the values
'    Ratio = 0#
'    BFRecord.QtyFcst = 0#
'    BFRecord.QtyFcstNetReq = 0#
'    BFRecord.QtyAdjust = 0#
'    BFRecord.QtyActualOrdered = 0#
'    BFRecord.QtyActualShipped = 0#
'
'    For Counter = 1 To UBound(BudgetOrForecast())
'        If BudgetOrForecast(Counter).StartDate > PdEndDate Then Exit For
'        If BudgetOrForecast(Counter).Enddate >= PdStartDate And BudgetOrForecast(Counter).StartDate <= PdEndDate Then
'            If BudgetOrForecast(Counter).StartDate <= PdStartDate And BudgetOrForecast(Counter).Enddate >= PdStartDate Then
'                Ratio = (GetWorkingDays(PdStartDate, BudgetOrForecast(Counter).Enddate, AIMDays(), AIMYears()) / _
'                GetWorkingDays(BudgetOrForecast(Counter).StartDate, BudgetOrForecast(Counter).Enddate, AIMDays(), AIMYears()))
'            ElseIf BudgetOrForecast(Counter).StartDate <= PdEndDate And BudgetOrForecast(Counter).Enddate >= PdEndDate Then
'                Ratio = (GetWorkingDays(BudgetOrForecast(Counter).StartDate, PdEndDate, AIMDays(), AIMYears()) / _
'                GetWorkingDays(BudgetOrForecast(Counter).StartDate, BudgetOrForecast(Counter).Enddate, AIMDays(), AIMYears()))
'            Else
'                Ratio = 1#
'            End If
'            BFRecord.QtyFcst = BFRecord.QtyFcst + BudgetOrForecast(Counter).QtyFcst * Ratio
'            BFRecord.QtyFcstNetReq = BFRecord.QtyFcstNetReq + BudgetOrForecast(Counter).QtyFcstNetReq * Ratio
'            BFRecord.QtyAdjust = BFRecord.QtyAdjust + BudgetOrForecast(Counter).QtyAdjust * Ratio
'            BFRecord.QtyActualOrdered = BFRecord.QtyActualOrdered + BudgetOrForecast(Counter).QtyActualOrdered * Ratio
'            BFRecord.QtyActualShipped = BFRecord.QtyActualShipped + BudgetOrForecast(Counter).QtyActualShipped * Ratio
'        End If
'        Ratio = 0#
'    Next
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.BudgetFcstBetweenDates)"
'End Function
'
'Private Function FcstOrNetReqBetweenDates( _
'    PdStartDate As Date, _
'    PdEndDate As Date, _
'    LcId As String, _
'    ItemId As String _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim Counter As Long
'    Dim Ratio As Double
'    'Initialize the values
'    Ratio = 0#
'    FcstOrNetReqQty.SysFcst = 0#
'    FcstOrNetReqQty.SysNet = 0#
'    FcstOrNetReqQty.ModSysFcst = 0#
'    FcstOrNetReqQty.ModSysNet = 0#
'    FcstOrNetReqQty.PlannedRct = 0#
'
'    For Counter = 1 To UBound(FcstOrNetReqRecord())
'        If FcstOrNetReqRecord(Counter).StartDate > PdEndDate Then Exit For
'        If FcstOrNetReqRecord(Counter).Enddate >= PdStartDate And FcstOrNetReqRecord(Counter).StartDate <= PdEndDate Then
'            If FcstOrNetReqRecord(Counter).StartDate <= PdStartDate And FcstOrNetReqRecord(Counter).Enddate >= PdStartDate Then
'                Ratio = (GetWorkingDays(PdStartDate, FcstOrNetReqRecord(Counter).Enddate, AIMDays(), AIMYears()) / _
'                GetWorkingDays(FcstOrNetReqRecord(Counter).StartDate, FcstOrNetReqRecord(Counter).Enddate, AIMDays(), AIMYears()))
'            ElseIf FcstOrNetReqRecord(Counter).StartDate <= PdEndDate And FcstOrNetReqRecord(Counter).Enddate >= PdEndDate Then
'                Ratio = (GetWorkingDays(FcstOrNetReqRecord(Counter).StartDate, PdEndDate, AIMDays(), AIMYears()) / _
'                GetWorkingDays(FcstOrNetReqRecord(Counter).StartDate, FcstOrNetReqRecord(Counter).Enddate, AIMDays(), AIMYears()))
'            Else
'                Ratio = 1#
'            End If
'            FcstOrNetReqQty.SysFcst = FcstOrNetReqQty.SysFcst + FcstOrNetReqRecord(Counter).SysFcst * Ratio
'            FcstOrNetReqQty.SysNet = FcstOrNetReqQty.SysNet + FcstOrNetReqRecord(Counter).SysNet * Ratio
'            FcstOrNetReqQty.ModSysFcst = FcstOrNetReqQty.ModSysFcst + FcstOrNetReqRecord(Counter).ModSysFcst * Ratio
'            FcstOrNetReqQty.ModSysNet = FcstOrNetReqQty.ModSysNet + FcstOrNetReqRecord(Counter).ModSysNet * Ratio
'            FcstOrNetReqQty.PlannedRct = FcstOrNetReqQty.PlannedRct + FcstOrNetReqRecord(Counter).PlannedRct * Ratio
'        End If
'        Ratio = 0#
'    Next
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.FcstOrNetReqBetweenDates)"
'End Function
'
'Private Function DpPopulateBudgetFcstRcdDates( _
'    StartDate As Date, _
'    Interval As AIM_INTERVALS, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim CurStartdate As Date
'    Dim Enddate As Date
'    Dim Counter As Long
'
'
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        Enddate = GetNextStartDate(StartDate, CurStartdate, Interval)
'        BudgetOrForecast(Counter).StartDate = CurStartdate
'        BudgetOrForecast(Counter).Enddate = Enddate - 1
'        CurStartdate = Enddate
'        BudgetOrForecast(Counter).Fy = GetFiscalYear(StartDate, AIMYears())
'    Next Counter
'    DpPopulateBudgetFcstRcdDates = 0
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.DpPopulateBudgetFcstRcdDates)"
'End Function
'
'Private Function PopulateBudgetFcstRcdDates( _
'    StartDate As Date, _
'    Interval As AIM_INTERVALS, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim CurStartdate As Date
'    Dim Enddate As Date
'    Dim Counter As Long
'
'
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        Enddate = GetNextStartDate(StartDate, CurStartdate, Interval)
'        BudgetOrForecast(Counter).StartDate = CurStartdate
'        BudgetOrForecast(Counter).Enddate = Enddate - 1
'        CurStartdate = Enddate
'        BudgetOrForecast(Counter).Fy = GetFiscalYear(StartDate, AIMYears())
'    Next Counter
'    PopulateBudgetFcstRcdDates = 0
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateBudgetFcstRcdDates)"
'End Function
'
'Private Function PopulateFcstOrNetReqRcdDates( _
'    StartDate As Date, _
'    Interval As AIM_INTERVALS, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
''used to get data from the repository table directly
'    Dim RtnCode As Integer
'    Dim CurStartdate As Date
'    Dim Enddate As Date
'    Dim Counter As Long
'
'    ReDim FcstOrNetReqRecord(1 To Nbrpds)
'
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        Enddate = GetNextStartDate(StartDate, CurStartdate, Interval)
'        FcstOrNetReqRecord(Counter).StartDate = CurStartdate
'        FcstOrNetReqRecord(Counter).Enddate = Enddate - 1
'        CurStartdate = Enddate
'        FcstOrNetReqRecord(Counter).Fy = GetFiscalYear(StartDate, AIMYears())
'    Next Counter
'    PopulateFcstOrNetReqRcdDates = 0
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateFcstOrNetReqRcdDates)"
'End Function
'
'Private Function DpPopulateFcstOrNetReqRcdDates( _
'    StartDate As Date, _
'    Interval As AIM_INTERVALS, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
''used to get data from the repository table directly
'    Dim RtnCode As Integer
'    Dim CurStartdate As Date
'    Dim Enddate As Date
'    Dim Counter As Long
'
'    ReDim FcstOrNetReqRecord(1 To Nbrpds)
'
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        Enddate = GetNextStartDate(StartDate, CurStartdate, Interval)
'        FcstOrNetReqRecord(Counter).StartDate = CurStartdate
'        FcstOrNetReqRecord(Counter).Enddate = Enddate - 1
'        CurStartdate = Enddate
'        FcstOrNetReqRecord(Counter).Fy = GetFiscalYear(StartDate, AIMYears())
'    Next Counter
'    DpPopulateFcstOrNetReqRcdDates = 0
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.DpPopulateFcstOrNetReqRcdDates)"
'End Function
'Private Function GenerateBudgetOrForecast( _
'    FcstId As String, _
'    IntToFcst As Integer, _
'    xBudgetFcst As XArrayDB, _
'    Fcsttype As String, _
'    FcstStartDate As Date _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim AIM_GenerateForecastRepository_List_Sp As New Command
'    Dim rsBudgerOrForecast As New ADODB.Recordset
'    Dim RowsUnitStart As Long
'    Dim Enddate As Date
'    Dim Counter As Long
'    Dim CurStartdate As Date
'    Dim PdStartDate As Date
'    Dim PdEndDate As Date
'    Dim RtnCode As Integer
'    'Dim Qty As Long
'    Dim Row As Long
'    Dim I As Long
'    Dim j As Long
'    Dim Price As Double
'    Dim Cost As Double
'    Dim JLoop As Long
'    Dim KLoop As Long
'    Dim DataYes  As Boolean
'    Dim TotalRows As Long
'    Dim NbrPdsToFcst As Integer
'    Dim IntervalToFcst As AIM_INTERVALS
'    Dim Rows As Long
'    Dim StartDate As Date
'    Dim Interval As AIM_INTERVALS
'    Dim Nbrpds As Integer
'    Dim Typestart As Long
'    Dim ColPos As Long
'    Dim Cube As Double
'    Dim Weight As Double
'
'    With AIM_GenerateForecastRepository_List_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GenerateForecastRepository_List_Sp"
'        .Parameters.Refresh
'        .Parameters(0).Value = 0
'        .Parameters("@FcstId").Value = FcstId
'        .Parameters("@FcstType").Value = Fcsttype
'    End With
'    With rsBudgerOrForecast
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'        .Open AIM_GenerateForecastRepository_List_Sp
'    End With
'    RtnCode = AIM_GenerateForecastRepository_List_Sp(0).Value
'    If RtnCode < 0 Then
'        'Do clean up
'        If Not (AIM_GenerateForecastRepository_List_Sp Is Nothing) Then
'            Set AIM_GenerateForecastRepository_List_Sp.ActiveConnection = Nothing
'        End If
'        Set AIM_GenerateForecastRepository_List_Sp = Nothing
'        If f_IsRecordsetValidAndOpen(rsBudgerOrForecast) Then rsBudgerOrForecast.Close
'        Set rsBudgerOrForecast = Nothing
'        GenerateBudgetOrForecast = RtnCode
'        Exit Function
'    End If
'
'    Rows = rsBudgerOrForecast.RecordCount
'    'Initialize Loop Counters
'    RowsUnitStart = 0  'Starting position for Unit data
'    'Use startdate the date supplied by the user
'    'Startdate = rsFcstMaint("FcstStartDate").Value
'    StartDate = FcstStartDate
'    Interval = GetForecastInterval(rsFcstMaint("FcstInterval").Value)
'    Nbrpds = rsFcstMaint("FcstPds").Value
'    IntervalToFcst = GetForecastInterval(IntToFcst)
'    CurStartdate = StartDate
'    'Find the end date for the forecast
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, Interval)
'    Next Counter
'    'Date till we need to forecast
'    Enddate = CurStartdate
'    'Loop to cal number of Pds to cal with new interval
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, IntervalToFcst)
'        If CurStartdate > Enddate Then Exit For
'         NbrPdsToFcst = NbrPdsToFcst + 1
'    Next
'    PdStartDate = StartDate
'    TotalRows = (Rows - 1)
'    'Redimension the Forecast Array
'    xBudgetFcst.ReDim 0, TotalRows, 0, (NbrPdsToFcst * 5 + 20)
'
'    For Row = 0 To (Rows - 1)
'        'Position the recordset
'        rsBudgerOrForecast.AbsolutePosition = Row + 1
'        'Populate the BudgetFcst record with data
'        DataYes = PopulateBudgetFcstRcdQty(rsBudgerOrForecast("RepositoryKey").Value, _
'        rsBudgerOrForecast("lcid").Value, rsBudgerOrForecast("item").Value, Nbrpds)
'        'Show what item we are processing to the user
'        strMessage = getTranslationResource("STATMSG90800")
'        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
'        Write_Message strMessage & " " & Trim$(rsBudgerOrForecast("lcid").Value) & _
'            getTranslationResource("/") & rsBudgerOrForecast("item").Value
'        'Update data for Units
'        Typestart = (I - 1) * Rows + RowsUnitStart
'        'Update data for item
'        xBudgetFcst.Value(Row, 0) = rsBudgerOrForecast("lcid").Value
'        xBudgetFcst.Value(Row, 1) = rsBudgerOrForecast("item").Value
'        xBudgetFcst.Value(Row, 2) = rsBudgerOrForecast("itdesc").Value
'        xBudgetFcst.Value(Row, 3) = rsBudgerOrForecast("Class1").Value
'        xBudgetFcst.Value(Row, 4) = rsBudgerOrForecast("Class2").Value
'        xBudgetFcst.Value(Row, 5) = rsBudgerOrForecast("Class3").Value
'        xBudgetFcst.Value(Row, 6) = rsBudgerOrForecast("Class4").Value
'        xBudgetFcst.Value(Row, 7) = rsBudgerOrForecast("ItStat").Value
'        xBudgetFcst.Value(Row, 8) = rsBudgerOrForecast("VelCode").Value
'        xBudgetFcst.Value(Row, 9) = rsBudgerOrForecast("VnId").Value
'        xBudgetFcst.Value(Row, 10) = rsBudgerOrForecast("Assort").Value
'        xBudgetFcst.Value(Row, 11) = rsBudgerOrForecast("ById").Value
'        xBudgetFcst.Value(Row, NbrPdsToFcst * 5 + 20) = Fcsttype
'        ColPos = 12
'        If IntToFcst = Interval Then
'            For I = 12 To (NbrPdsToFcst + 11)
'                Price = 0
'                Cost = 0
'                For j = 1 To 5
'                    If j = 1 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BudgetOrForecast(I - 11).QtyFcst
'                    ElseIf j = 2 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BudgetOrForecast(I - 11).QtyFcstNetReq
'                    ElseIf j = 3 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BudgetOrForecast(I - 11).QtyAdjust
'                    ElseIf j = 4 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BudgetOrForecast(I - 11).QtyActualOrdered
'                    ElseIf j = 5 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BudgetOrForecast(I - 11).QtyActualShipped
'                    End If
'                    ColPos = ColPos + 1
'                Next j
'            Next I
'        Else
'            For I = 12 To (NbrPdsToFcst + 11)
'                Price = 0
'                Cost = 0
'                PdEndDate = GetNextStartDate(StartDate, PdStartDate, IntervalToFcst)
'                'Calculate cost,price
'                If DataYes = True Then
'                    BudgetFcstBetweenDates PdStartDate, PdEndDate - 1, rsBudgerOrForecast("lcid").Value, _
'                    rsBudgerOrForecast("item").Value
'                Else
'                    Price = 0
'                    Cost = 0
'                End If
'                PdStartDate = PdEndDate
'
'                For j = 1 To 5
'                    If j = 1 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BFRecord.QtyFcst
'                    ElseIf j = 2 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BFRecord.QtyFcstNetReq
'                    ElseIf j = 3 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BFRecord.QtyAdjust
'                    ElseIf j = 4 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BFRecord.QtyActualOrdered
'                    ElseIf j = 5 Then
'                        xBudgetFcst.Value(RowsUnitStart, ColPos) = BFRecord.QtyActualShipped
'                    End If
'                        ColPos = ColPos + 1
'                Next j
'            Next I
'        End If
'
'        GetFYPriceCost rsBudgerOrForecast("lcid").Value, rsBudgerOrForecast("Item").Value
'        Cube = rsBudgerOrForecast("Cube")
'        Weight = rsBudgerOrForecast("Weight")
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 12) = Cube
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 13) = Weight
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 14) = LastFYPrice
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 15) = ThisFYPrice
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 16) = NextFYPrice
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 17) = LastFYCost
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 18) = ThisFYCost
'        xBudgetFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 19) = NextFYCost
'        PdStartDate = StartDate
'        RowsUnitStart = RowsUnitStart + 1
'    Next Row
'
'    GenerateBudgetOrForecast = 0
'
'    'clean up
'    If Not (AIM_GenerateForecastRepository_List_Sp Is Nothing) Then
'        Set AIM_GenerateForecastRepository_List_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_GenerateForecastRepository_List_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgerOrForecast) Then rsBudgerOrForecast.Close
'    Set rsBudgerOrForecast = Nothing
'
'Exit Function
'ErrorHandler:
'    'clean up
'    If Not (AIM_GenerateForecastRepository_List_Sp Is Nothing) Then
'        Set AIM_GenerateForecastRepository_List_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_GenerateForecastRepository_List_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgerOrForecast) Then rsBudgerOrForecast.Close
'    Set rsBudgerOrForecast = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GenerateBudgetOrForecast)"
'End Function
'
'Private Function GenerateBatchFcst( _
'    StartDate As Date, _
'    Rows As Long, _
'    rs As ADODB.Recordset, _
'    FcstIdArg As String, _
'    IntToFcst As AIM_INTERVALS, _
'    xBatchFcst As XArrayDB, _
'    FcsttypeArg As String, _
'    FcstStartDate As Date _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RowsUnitStart As Long
'    Dim Enddate As Date
'    Dim Counter As Long
'    Dim CurStartdate As Date
'    Dim PdStartDate As Date
'    Dim PdEndDate As Date
'    Dim RtnCode As Integer
'    'Dim Qty As Long
'    Dim Row As Long
'    Dim I As Long
'    Dim j As Long
'    Dim Price As Double
'    Dim Cost As Double
'    Dim JLoop As Long
'    Dim KLoop As Long
'    Dim DataYes  As Boolean
'    Dim TotalRows As Long
'    Dim NbrPdsToFcst As Integer
'    Dim IntervalToFcst As AIM_INTERVALS
'    Dim Interval As AIM_INTERVALS
'    Dim Nbrpds As Integer
'    Dim Typestart As Long
'    Dim ColPos As Long
'    Dim Cube As Double
'    Dim Weight As Double
'
'    Fcsttype = FcsttypeArg
'    FcstId = FcstIdArg
'    GetRepositoryKey
'
'    'Initialize Loop Counters
'    RowsUnitStart = 0  'Starting position for Unit data
'    'Use startdate the date supplied by the user
'    'Startdate = rsFcstMaint("FcstStartDate").Value
'    StartDate = FcstStartDate
'    Interval = GetForecastInterval(rsFcstMaint("FcstInterval").Value)
'    Nbrpds = rsFcstMaint("FcstPds").Value
'    IntervalToFcst = GetForecastInterval(IntToFcst)
'    CurStartdate = StartDate
'    'Find the end date for the forecast
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, Interval)
'    Next Counter
'    'Date till we need to forecast
'    Enddate = CurStartdate
'    'Loop to cal number of Pds to cal with new interval
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, IntervalToFcst)
'        If CurStartdate > Enddate Then Exit For
'        NbrPdsToFcst = NbrPdsToFcst + 1
'    Next
'
'    PdStartDate = StartDate
'    TotalRows = (Rows - 1)
'    'Redimension the Forecast Array
'    xBatchFcst.ReDim 0, TotalRows, 0, (NbrPdsToFcst * 5 + 20)
'    For Row = 0 To (Rows - 1)
'        'Position the recordset
'        rs.AbsolutePosition = Row + 1
'        'Populate the BudgetFcst record with data
'        DataYes = PopulateFcstOrNetReqRcdQty(FcstKey, rs("lcid").Value, rs("item").Value, Nbrpds)
'        'Show what item we are processing to the user
'        strMessage = getTranslationResource("STATMSG90800")
'        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
'        Write_Message strMessage & " " & Trim$(rs("lcid").Value) & _
'            getTranslationResource("/") & rs("item").Value
'        'Update data for Units
'        Typestart = (I - 1) * Rows + RowsUnitStart
'        'Update data for item
'        xBatchFcst.Value(Row, 0) = rs("lcid").Value
'        xBatchFcst.Value(Row, 1) = rs("item").Value
'        xBatchFcst.Value(Row, 2) = rs("itdesc").Value
'        xBatchFcst.Value(Row, 3) = rs("Class1").Value
'        xBatchFcst.Value(Row, 4) = rs("Class2").Value
'        xBatchFcst.Value(Row, 5) = rs("Class3").Value
'        xBatchFcst.Value(Row, 6) = rs("Class4").Value
'        xBatchFcst.Value(Row, 7) = rs("ItStat").Value
'        xBatchFcst.Value(Row, 8) = rs("VelCode").Value
'        xBatchFcst.Value(Row, 9) = rs("VnId").Value
'        xBatchFcst.Value(Row, 10) = rs("Assort").Value
'        xBatchFcst.Value(Row, 11) = rs("ById").Value
'
'        'Sri code change to save only modified rows start
'        'For items that are not in  ForecastrepositoryBatch populate the col 12 with
'        ' Y indicating new row else N old row
'        If DataYes = True Then
'            'xBatchFcst.Value(Row, 12) = 0 'Total filed not required
'            xBatchFcst.Value(Row, 12) = "N"
'        Else
'            xBatchFcst.Value(Row, 12) = "Y"
'        End If
'        'Sri code change to save only modified rows end
'
'        If IntToFcst = Interval Then
'            ColPos = 13
'            For I = 13 To (NbrPdsToFcst + 12)
'                Price = 0
'                Cost = 0
'                For j = 1 To 5
'                    If j = 1 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqRecord(I - 12).SysFcst
'                    ElseIf j = 2 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqRecord(I - 12).SysNet
'                    ElseIf j = 3 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqRecord(I - 12).ModSysFcst
'                    ElseIf j = 4 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqRecord(I - 12).ModSysNet
'                    ElseIf j = 5 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqRecord(I - 12).PlannedRct
'                    End If
'                    ColPos = ColPos + 1
'                Next j
'            Next I
'        Else
'            ColPos = 13
'            For I = 13 To (NbrPdsToFcst + 12)
'                Price = 0
'                Cost = 0
'                PdEndDate = GetNextStartDate(StartDate, PdStartDate, IntervalToFcst)
'                'Calculate cost,price
'                If DataYes = True Then
'                    FcstOrNetReqBetweenDates PdStartDate, PdEndDate - 1, rs("lcid").Value, _
'                    rs("item").Value
'                Else
'                    Price = 0
'                    Cost = 0
'                End If
'                PdStartDate = PdEndDate
'                For j = 1 To 5
'                    If j = 1 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqQty.SysFcst
'                    ElseIf j = 2 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqQty.SysNet
'                    ElseIf j = 3 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqQty.ModSysFcst
'                    ElseIf j = 4 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqQty.ModSysNet
'                    ElseIf j = 5 Then
'                        xBatchFcst.Value(RowsUnitStart, ColPos) = FcstOrNetReqQty.PlannedRct
'                    End If
'                    ColPos = ColPos + 1
'                Next j
'            Next I
'        End If
'
'        GetFYPriceCost rs("lcid").Value, rs("Item").Value
'        Cube = rs("Cube")
'        Weight = rs("Weight")
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 13) = Cube
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 14) = Weight
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 15) = LastFYPrice
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 16) = ThisFYPrice
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 17) = NextFYPrice
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 18) = LastFYCost
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 19) = ThisFYCost
'        xBatchFcst.Value(RowsUnitStart, NbrPdsToFcst * 5 + 20) = NextFYCost
'        PdStartDate = StartDate
'        RowsUnitStart = RowsUnitStart + 1
'    Next Row
'
'    'clean up
'    GenerateBatchFcst = 0
'
'Exit Function
'ErrorHandler:
''clean up
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GenerateBatchFcst)"
'End Function
'
''A.Stocksdale Jan 21 2004 - This was called "Main" in AIMAdvForecastMod.cls
'
'Private Function AdvForecastMain( _
'    FcstId As String, _
'    xForeCast As XArrayDB, _
'    task As String, _
'    Optional FcstInterval As AIM_INTERVALS, _
'    Optional Fcsttype As String, _
'    Optional FcstStartDate As Date, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim SqlStmt As String
'    Dim RtnCode As Integer
'    Dim StartDate As Date
'    Dim Rows As Long
'    Dim Nbrpds As Integer
'    Dim Interval As AIM_INTERVALS
'    Dim ApplyTrend As Integer
'    Dim FcstUnit As AIM_FORECASTUNITS
'    Dim FcstLevel As Integer
'    Dim AdjustPct As Double
'    Dim Enddate As Date
'    'Dim NetRqmts As Boolean
'
'    'Connect to the database
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , "AIMForecast.bas")
'    If RtnCode <> SUCCEED Then
'        AdvForecastMain = RtnCode
'        Exit Function
'    End If
'    'Get the Seasonality Table Version Number
'    GetSAVersion
'    With AIM_SysCtrl_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_SysCtrl_Get_Sp"
'        .Parameters.Refresh
'    End With
'
'    'Initialize the System Control Recordset
'    With rsSysCtrl
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'        .Open AIM_SysCtrl_Get_Sp
'    End With
'    'Initialize the ForeCastMaint Item List Resultset -- fire hose
'    With rsFcstMaint
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenForwardOnly
'    End With
'    'Set the SQL Statement
'    SqlStmt = SqlStmt + "Select VnId,Assort,LcId,Item,Class1,Class2,Class3,Class4,ById,ItStat, " & _
'            " LDivision, LStatus, LRegion, LUserDefined, "
'    SqlStmt = SqlStmt + "FcstStartDate,FcstInterval,ApplyTrend,FcstLevel,FcstUnit,"
'    SqlStmt = SqlStmt + "AdjustPct,FcstPds,NetRqmts From AimForecast where FcstId = N'" & FcstId & "'"
'
'    'Retrieve the data
'    If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
'    rsFcstMaint.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
'    If f_IsRecordsetOpenAndPopulated(rsFcstMaint) Then
'        If task = "ForeCast" _
'        Or task = "SalesAndOrders" _
'        Or task = "SalesAndOrdersLastYear" _
'        Or task = "InitialStock" _
'        Or task = "LastFySales" _
'        Or task = "BatchFcst" _
'        Then
'            SqlStmt = BldForecastSQL(rsFcstMaint("VnId").Value, rsFcstMaint("Assort").Value, _
'            rsFcstMaint("LcId").Value, rsFcstMaint("Item").Value, rsFcstMaint("Class1").Value, _
'            rsFcstMaint("Class2").Value, rsFcstMaint("Class3").Value, _
'            rsFcstMaint("Class4").Value, rsFcstMaint("Byid").Value, rsFcstMaint("ItStat").Value, SAVersion, _
'            rsFcstMaint("LDivision").Value, rsFcstMaint("LRegion").Value, rsFcstMaint("LStatus").Value, rsFcstMaint("LUserDefined").Value)
'        ElseIf task = "NetReqForAPeriod" Then
'            SqlStmt = BldForecastSQL(VnId, "", LcId, ItemId, "", "", "", "", "", "", SAVersion, "", "", "", "")
'        End If
'    End If
'
'    'Initialize the Vendor Item List Resultset -- fire hose
'    With rsVnItemList
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenForwardOnly
'    End With
'    rsVnItemList.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
'
''    If f_IsRecordsetOpenAndPopulated(rsFcstMaint) Then
''        If task = "ForeCast" _
''        Or task = "SalesAndOrders" _
''        Or task = "SalesAndOrdersLastYear" _
''        Or task = "InitialStock" _
''        Or task = "LastFySales" _
''        Or task = "BatchFcst" _
''        Then
''            RtnCode = BldForecastSQL_New( _
''                Cn, _
''                rsFcstMaint("VnId").Value, rsFcstMaint("Assort").Value, _
''                rsFcstMaint("LcId").Value, rsFcstMaint("Item").Value, rsFcstMaint("Class1").Value, _
''                rsFcstMaint("Class2").Value, rsFcstMaint("Class3").Value, _
''                rsFcstMaint("Class4").Value, rsFcstMaint("Byid").Value, rsFcstMaint("ItStat").Value, SAVersion, _
''                rsVnItemList)
''        ElseIf task = "NetReqForAPeriod" Then
''            RtnCode = BldForecastSQL_New( _
''                Cn, VnId, "", LcId, ItemId, "", "", "", "", "", "", SAVersion, _
''                rsVnItemList)
''        End If
''    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsVnItemList) Then
'        'Populate the local variables from the rsFcstMaint record set
'        StartDate = rsFcstMaint("FcstStartDate").Value
'        Interval = GetForecastInterval(rsFcstMaint("FcstInterval").Value)
'        ApplyTrend = rsFcstMaint("ApplyTrend").Value
'        FcstLevel = rsFcstMaint("FcstLevel").Value
'        FcstUnit = GetForecastUnit(rsFcstMaint("FcstUnit").Value)
'        AdjustPct = rsFcstMaint("AdjustPct").Value
'        'Use the NetRqmts that is passed as argument
'        'NetRqmts = IIf(rsFcstMaint("NetRqmts").Value = 0, False, True)
'        Nbrpds = rsFcstMaint("FcstPds").Value
'        Rows = rsVnItemList.RecordCount
'        If task = "ForeCast" Then
'            PopulateFcstOrNetReqRcdDates StartDate, Interval, Nbrpds
'            'Call updateforecast to populate xforcast with data
'            If Modification = True And NetRqmts = False Then
'                'we populate the Rcd if it  not netrqmts here else we populate in AIMADVForecast class
'                ReDim BudgetOrForecast(1 To Nbrpds)
'                If FcstInterval = Interval Then
'                    PopulateBudgetFcstRcdDates StartDate, Interval, Nbrpds
'                End If
'            End If
'            'UpdateForecast StartDate, Rows, Nbrpds, Interval, ApplyTrend, _
'            FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast
'            'Osama Riyahi - Start
'            If PlannedRct Then
'                UpdateForecast StartDate, Rows, Nbrpds, Interval, ApplyTrend, _
'                FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast, PlannedRct, xPlannedrct
'            Else
'                UpdateForecast StartDate, Rows, Nbrpds, Interval, ApplyTrend, _
'                FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast, PlannedRct
'            End If
'            'Osama Riyahi - end
'        ElseIf task = "SalesAndOrders" Then
'            'Call PopulateSlesAndOrders to populate xSalesAndOrders with data
'            PopulateSalesAndOrders StartDate, Rows, Nbrpds, Interval, rsVnItemList, xForeCast
'        ElseIf task = "SalesAndOrdersLastYear" Then
'            'Call PopulateSlesAndOrdersLastYear to populate xSalesAndOrders with data
'            PopulateSalesAndOrdersLastYear StartDate, Rows, Nbrpds, Interval, rsVnItemList, xForeCast
'        ElseIf task = "InitialStock" Then
'            GenerateInitialStock StartDate, Rows, Nbrpds, Interval, ApplyTrend, _
'            FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast
'        ElseIf task = "NetReqForAPeriod" Then
'            If PlannedRct Then
'                'Use class level values FcstStartDate,FcstAdjPct set rows =1 and nbrPds =1
'                UpdateForecast StartDate, Rows, Nbrpds, Interval, ApplyTrend, _
'                FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast, PlannedRct, xPlannedrct
'            Else
'                'Use class level values FcstStartDate,FcstAdjPct set rows =1 and nbrPds =1
'                UpdateForecast StartDate, Rows, Nbrpds, Interval, ApplyTrend, _
'                FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast, PlannedRct
'            End If
'        ElseIf task = "LastFySales" Then
'            GenerateLastFySales StartDate, rsVnItemList, xForeCast
'        ElseIf task = "BudgetOrForecast" Then
'            'GenerateBudgetOrForecast xForeCast
'            ReDim BudgetOrForecast(1 To Nbrpds)
'
'            If FcstInterval = Interval Then
'                RtnCode = GenerateBudgetOrForecast(FcstId, FcstInterval, xForeCast, Fcsttype, FcstStartDate)
'            Else
'                PopulateBudgetFcstRcdDates StartDate, Interval, Nbrpds
'                RtnCode = GenerateBudgetOrForecast(FcstId, FcstInterval, xForeCast, Fcsttype, FcstStartDate)
'            End If
'            AdvForecastMain = RtnCode
'        ElseIf task = "BatchFcst" Then
'            PopulateFcstOrNetReqRcdDates StartDate, Interval, Nbrpds
'            RtnCode = GenerateBatchFcst(StartDate, Rows, rsVnItemList, FcstId, FcstInterval, xForeCast, Fcsttype, FcstStartDate)
'            AdvForecastMain = RtnCode
'        End If
'    End If
'
'    'Clean up the objects
'    CleanUpMain
'
'    'Disconnect from the database
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , "AIMForecast.bas")
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.AdvForecastMain)"
'End Function
'
'Private Function DPAdvForecastMain( _
'    FcstId As String, _
'    xForeCast As XArrayDB, _
'    task As String, _
'    Optional FcstInterval As AIM_INTERVALS, _
'    Optional Fcsttype As String, _
'    Optional FcstStartDate As Date, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim SqlStmt As String
'    Dim RtnCode As Integer
'    Dim StartDate As Date
'    Dim Rows As Long
'    Dim NbrPds_Future As Integer
'    Dim NbrPds_Hist  As Integer
'    Dim Interval As AIM_INTERVALS
'    Dim ApplyTrend As Integer
'    Dim FcstUnit As AIM_FORECASTUNITS
'    Dim FcstLevel As Integer
'    Dim AdjustPct As Double
'    Dim Enddate As Date
'    'Dim NetRqmts As Boolean
'
'    'Connect to the database
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , "AIMForecast.bas")
'    If RtnCode <> SUCCEED Then
'        DPAdvForecastMain = RtnCode
'        Exit Function
'    End If
'    'Get the Seasonality Table Version Number
'    GetSAVersion
'    With AIM_SysCtrl_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_SysCtrl_Get_Sp"
'        .Parameters.Refresh
'    End With
'
'    'Initialize the System Control Recordset
'    With rsSysCtrl
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'        .Open AIM_SysCtrl_Get_Sp
'    End With
'    'Initialize the ForeCastMaint Item List Resultset -- fire hose
'    With rsFcstMaint
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenForwardOnly
'    End With
'    'Set the SQL Statement
'    SqlStmt = SqlStmt + "Select VnId,Assort,LcId,Item,Class1,Class2,Class3,Class4,ById,ItStat, " & _
'            " LDivision, LStatus, LRegion, LUserDefined, "
'    SqlStmt = SqlStmt + "FcstStartDate,FcstInterval,ApplyTrend,FcstUnit,"
'    SqlStmt = SqlStmt + " FcstPds_Future,FcstPds_Historical From AimFcstSetUp where FcstId = N'" & FcstId & "'"
'
'    'Retrieve the data
'    If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
'    rsFcstMaint.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
'    If f_IsRecordsetOpenAndPopulated(rsFcstMaint) Then
'        If task = "ForeCast" Then
'            SqlStmt = BldForecastSQL(rsFcstMaint("VnId").Value, rsFcstMaint("Assort").Value, _
'            rsFcstMaint("LcId").Value, rsFcstMaint("Item").Value, rsFcstMaint("Class1").Value, _
'            rsFcstMaint("Class2").Value, rsFcstMaint("Class3").Value, _
'            rsFcstMaint("Class4").Value, rsFcstMaint("Byid").Value, rsFcstMaint("ItStat").Value, SAVersion, _
'            rsFcstMaint("LDivision").Value, rsFcstMaint("LRegion").Value, rsFcstMaint("LStatus").Value, rsFcstMaint("LUserDefined").Value)
'        End If
'
'    'Initialize the Vendor Item List Resultset -- fire hose
'    With rsVnItemList
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenForwardOnly
'    End With
'    rsVnItemList.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
'    If f_IsRecordsetOpenAndPopulated(rsVnItemList) Then
'        'Populate the local variables from the rsFcstMaint record set
'        StartDate = rsFcstMaint("FcstStartDate").Value
'        Interval = GetForecastInterval(rsFcstMaint("FcstInterval").Value)
'        ApplyTrend = rsFcstMaint("ApplyTrend").Value
'        'FcstLevel = rsFcstMaint("FcstLevel").Value
'        FcstLevel = 0
'        FcstUnit = GetForecastUnit(rsFcstMaint("FcstUnit").Value)
'        'AdjustPct = rsFcstMaint("AdjustPct").Value
'        AdjustPct = 0
'        'Use the NetRqmts that is passed as argument
'        'NetRqmts = IIf(rsFcstMaint("NetRqmts").Value = 0, False, True)
'        NbrPds_Future = rsFcstMaint("FcstPds_Future").Value
'        NbrPds_Hist = rsFcstMaint("FcstPds_Historical").Value
'        DpFuturePds = rsFcstMaint("FcstPds_Future").Value
'       DpHistPds = rsFcstMaint("FcstPds_Historical").Value
'
'        Rows = rsVnItemList.RecordCount
'        'call this fuction here so that we need not do every where else
'        AIMCalendar_Load Cn, AIMYears(), AIMDays()
'        DpCurrentStartDate = DPGetRollingFcstStartDate(StartDate, FcstInterval)
'         DpHistoricalStartDate = DPGetHistoricalFcstStartDate(StartDate, NbrPds_Hist, FcstInterval)
'        If task = "ForeCast" Then
'            'used to get data from the repository table directly we call this populaefcstornetreqrcddates
'            DpPopulateFcstOrNetReqRcdDates DpHistoricalStartDate, FcstInterval, NbrPds_Hist + NbrPds_Future
'            'Call updateforecast to populate xforcast with data
'           'If Modification = True And NetRqmts = False Then
'                'we populate the Rcd if it  not netrqmts here else we populate in AIMADVForecast class
''                ReDim BudgetOrForecast(1 To NbrPds_Future + NbrPds_Hist)
''
''                If FcstInterval = Interval Then
''                    PopulateBudgetFcstRcdDates DpHistoricalStartDate, Interval, NbrPds_Future + NbrPds_Hist
''                End If
'           ' End If
'            If PlannedRct Then
'                DPUpdateForecast StartDate, Rows, NbrPds_Future, NbrPds_Hist, Interval, ApplyTrend, _
'                FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast, PlannedRct, xPlannedrct
'            Else
'                DPUpdateForecast DpHistoricalStartDate, Rows, NbrPds_Future, NbrPds_Hist, Interval, ApplyTrend, _
'                FcstUnit, FcstLevel, AdjustPct, NetRqmts, rsVnItemList, xForeCast, PlannedRct
'            End If
'        ElseIf task = "BatchFcst" Then
'            PopulateFcstOrNetReqRcdDates StartDate, Interval, NbrPds_Future
'            RtnCode = GenerateBatchFcst(StartDate, Rows, rsVnItemList, FcstId, FcstInterval, xForeCast, Fcsttype, FcstStartDate)
'            DPAdvForecastMain = RtnCode
'        End If
'    End If
'    End If
'    'Clean up the objects
'    CleanUpMain
'
'    'Disconnect from the database
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , "AIMForecast.bas")
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.DPAdvForecastMain)"
'End Function
'
'
'Private Function GetForecastInterval(arg As AIM_INTERVALS) As Integer
'On Error GoTo ErrorHandler
'
'    If arg = 0 Then
'        GetForecastInterval = int_Days
'    End If
'    If arg = 1 Then
'        GetForecastInterval = int_Weeks
'    End If
'    If arg = 2 Then
'        GetForecastInterval = int_Months
'    End If
'    If arg = 3 Then
'        GetForecastInterval = int_Quarters
'    End If
'    If arg = 4 Then
'        GetForecastInterval = int_544
'    End If
'    If arg = 5 Then
'        GetForecastInterval = int_454
'    End If
'    If arg = 6 Then
'        GetForecastInterval = int_445
'    End If
'    If arg = 7 Then
'        GetForecastInterval = int_4Wks
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetForecastInterval)"
'End Function
'
'Function DPGetHistoricalFcstStartDate(StartDate As Date, HistoricalPds As Integer, Interval As AIM_INTERVALS) As Date
'On Error GoTo ErrorHandler
''This function should be rewritten similer to getnextstartdate with modifications
''This works only for days weeks and months,4wks and quarters
'Select Case Interval
'Case int_Days
'    DPGetHistoricalFcstStartDate = StartDate - HistoricalPds
'Case int_Weeks
'    DPGetHistoricalFcstStartDate = DateAdd("ww", -HistoricalPds, StartDate)
'Case int_Months
'    DPGetHistoricalFcstStartDate = DateAdd("m", -HistoricalPds, StartDate)
'Case int_Quarters
'    DPGetHistoricalFcstStartDate = DateAdd("q", -HistoricalPds, StartDate)
'Case int_4Wks
'    DPGetHistoricalFcstStartDate = DateAdd("ww", 4 * HistoricalPds, StartDate)
'
'End Select
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(DPGetHistoricalFcstStartDate)"
'End Function
'Function DPGetRollingFcstStartDate(StartDate As Date, Interval As AIM_INTERVALS) As Date
'On Error GoTo ErrorHandler
'Dim CurrentMonth As Integer
'Dim CurrentDay As Integer
'Dim CurrentWeek As Integer
'Dim currentYear As Integer
'Dim Today As Date
'Dim WeekStartDate As Integer
'Dim MonthStartDay As Integer
'
'Dim I As Integer
'Today = CDate(#5/14/2004#)
'
'WeekStartDate = vbMonday
'currentYear = Year(Today)
'MonthStartDay = 15
'
'
'If Interval = int_Weeks Then
'  For I = 1 To 7
'    If Weekday(Today) = WeekStartDate Then
'        StartDate = Today
'        DPGetRollingFcstStartDate = StartDate
'
'        Exit Function
'    Else
'        Today = Today - 1
'    End If
'  Next
'
'ElseIf Interval = int_Months Then
'    CurrentMonth = Month(Today)
'   ' StartDate = CDate(CStr(MonthStartDay) + "-" + CStr(CurrentMonth) + "-" + CStr(currentYear))
'    StartDate = CDate(CStr(CurrentMonth) + "-" + CStr(MonthStartDay) + "-" + CStr(currentYear))
'    If StartDate > Today Then
'        If CurrentMonth <> 1 Then
'            StartDate = CDate(CStr(CurrentMonth - 1) + "-" + CStr(MonthStartDay) + "-" + CStr(currentYear))
'        Else
'            StartDate = CDate(CStr(12) + "-" + CStr(MonthStartDay) + "-" + CStr(currentYear - 1))
'        End If
'    End If
'    DPGetRollingFcstStartDate = StartDate
'End If
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(DPGetRollingFcstStartDate)"
'End Function
'
'Function GetPrice( _
'    StartDate As Date, _
'    Enddate As Date, _
'    LcId As String, _
'    Item As String, _
'    Price As Double, _
'    Cost As Double _
') As Double
'On Error GoTo ErrorHandler
'
'    Price = 0
'    Cost = 0
'    'Initialize the Update Stored Procedure
'    Set AIM_CostPriceListPrice_Get_Sp = New ADODB.Command
'    With AIM_CostPriceListPrice_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_CostPriceListPrice_Get_Sp"
'        .CommandType = adCmdStoredProc
'        '.Parameters.Refresh
'    End With
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Lcid", adVarChar, adParamInput, 12, LcId)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@ItemId", adVarChar, adParamInput, 25, Item)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Startdate", adDate, adParamInput, , StartDate)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Enddate", adDate, adParamInput, , Enddate)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Cost", adDouble, adParamOutput, 10.4, 0)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Price", adDouble, adParamOutput, 10.4, 0)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@ListPrice", adDouble, adParamOutput, 10.4, 0)
'    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
'
'    AIM_CostPriceListPrice_Get_Sp.Execute
'
'    Price = AIM_CostPriceListPrice_Get_Sp("@price")
'    Cost = AIM_CostPriceListPrice_Get_Sp("@Cost")
'    If Not (AIM_CostPriceListPrice_Get_Sp Is Nothing) Then
'        Set AIM_CostPriceListPrice_Get_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_CostPriceListPrice_Get_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_CostPriceListPrice_Get_Sp Is Nothing) Then
'        Set AIM_CostPriceListPrice_Get_Sp.ActiveConnection = Nothing
'    End If
'    Set AIM_CostPriceListPrice_Get_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetPrice)"
'End Function
'
'
'Function GetNetReqForAPeriod( _
'    FcstIdArg As String, _
'    VnIdArg As String, _
'    LcIdArg As String, _
'    ItemIdArg As String, _
'    FcstStartdateArg As Date, _
'    IntToFcstArg As Integer, _
'    xForeCast As XArrayDB, _
'    ModNetReq() As Double, _
'    Optional PlannedRctYNArg As Boolean, _
'    Optional xPlannedrct As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    'Osama Riyahi - Begin
'    PlannedRct = PlannedRctYNArg
'    'Osama Riyahi - End
'
'    Modification = True
'    ModificationChangeYN = True
'    FcstId = FcstIdArg
'    FcstStartDate = FcstStartdateArg
'    FcstInterval = GetForecastInterval(IntToFcstArg)
'    If UBound(ModNetReq) <= 0 Then
'        'We need an array of data for this case
'        GetNetReqForAPeriod = -1
'        Return
'    Else
'        modnetreqloc() = ModNetReq()
'    End If
'    NetRqmts = True 'Populate the class variable
'    VnId = VnIdArg
'    LcId = LcIdArg
'    ItemId = ItemIdArg
'    'FcstStartdate = FcstStartdateArg
'    'FcstAdjPct = FcstAdjPctArg
'
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "NetReqForAPeriod", , , , xPlannedrct)
'    GetNetReqForAPeriod = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetNetReqForAPeriod)"
'End Function
'
'Public Function LoadAimCalendar( _
'    LcId As String, _
'    ItemId As String, _
'    StartDate As Date, _
'    Enddate As Date _
') As Boolean
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_ItemHistory_GetEq_Sp As New ADODB.Command
'    Dim rsItemHistory As New ADODB.Recordset
'    Dim FyStartYear As Integer
'    Dim FyEndYear As Integer
'    Dim DemandSource As String
'    Dim Pd As Integer
'    Dim Counter As Integer
'    Dim TotalRec As Integer
'    Dim Sale As Double
'    Dim Order As Double
'
'    FyStartYear = GetFiscalYear(StartDate, AIMYears())
'    FyEndYear = GetFiscalYear(Enddate, AIMYears())
'    With AIM_ItemHistory_GetEq_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ItemHistory_GetEq_Sp"
'        .Parameters.Refresh
'    End With
'
'    rsItemHistory.CursorLocation = adUseClient
'    rsItemHistory.LockType = adLockReadOnly
'
'    RtnCode = ItemHistory_GetEq( _
'        AIM_ItemHistory_GetEq_Sp, _
'        rsItemHistory, _
'        LcId, _
'        ItemId, _
'        FyStartYear, _
'        FyEndYear, _
'        "S")
''>>> check status here
'    TotalRec = rsItemHistory.RecordCount * 52
'    If TotalRec = 0 Then
'        LoadAimCalendar = False
'        If Not (AIM_ItemHistory_GetEq_Sp Is Nothing) Then
'            Set AIM_ItemHistory_GetEq_Sp.ActiveConnection = Nothing
'        End If
'        Set AIM_ItemHistory_GetEq_Sp = Nothing
'        If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
'        Set rsItemHistory = Nothing
'        Exit Function
'    End If
'
'    ReDim Preserve ActualSalesAndOrders(1 To TotalRec)
'    If RtnCode <> FAIL _
'    And f_IsRecordsetOpenAndPopulated(rsItemHistory) _
'    Then
'        'Load the Demand History
'        Counter = 1
'        Do Until rsItemHistory.eof
'            For Pd = 1 To 52 Step 1
'                'Get the Demand History Period Number
'                ActualSalesAndOrders(Counter).Fy = rsItemHistory("HisYear").Value
'                ActualSalesAndOrders(Counter).Sale = rsItemHistory("dmd" + Format(Pd, "00")).Value
'                ActualSalesAndOrders(Counter).Pd = Pd
'                ActualSalesAndOrders(Counter).StartDate = GetStartDateFmPeriod(Pd, ActualSalesAndOrders(Pd).Fy, AIMYears())
'                ActualSalesAndOrders(Counter).Enddate = GetEndDateFmPeriod(Pd, ActualSalesAndOrders(Pd).Fy, AIMYears())
'                Counter = Counter + 1
'            Next Pd
'            rsItemHistory.MoveNext
'        Loop
'        If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
'    End If
'
'    RtnCode = ItemHistory_GetEq( _
'        AIM_ItemHistory_GetEq_Sp, _
'        rsItemHistory, _
'        LcId, _
'        ItemId, _
'        FyStartYear, _
'        FyEndYear, _
'        "O")
'    If RtnCode <> FAIL _
'    And f_IsRecordsetOpenAndPopulated(rsItemHistory) _
'    Then
'        'Load the Demand History
'        Counter = 1
'        Do Until rsItemHistory.eof
'            For Pd = 1 To 52 Step 1
'                ActualSalesAndOrders(Counter).Order = rsItemHistory("dmd" + Format(Pd, "00")).Value
'                Counter = Counter + 1
'            Next Pd
'            rsItemHistory.MoveNext
'        Loop
'    End If
'
'    LoadAimCalendar = True
'
'    If Not (AIM_ItemHistory_GetEq_Sp Is Nothing) Then Set AIM_ItemHistory_GetEq_Sp.ActiveConnection = Nothing
'    Set AIM_ItemHistory_GetEq_Sp = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
'    Set rsItemHistory = Nothing
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.LoadAimCalendar)"
'End Function
'
'Public Function DpPopulateBudgetFcstRcdQty( _
'    RepositoryKey As Long, _
'    LcId As String, _
'    ItemId As String, _
'    Nbrpds As Integer, _
'    Interval As AIM_INTERVALS _
') As Boolean
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_Repository_Get_Sp As New ADODB.Command
'    Dim rsBudgetFcst As New ADODB.Recordset
'    Dim Pd As Integer
'    Dim Counter As Integer
'    Dim TotalRec As Integer
'
'    With AIM_Repository_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_Repository_Get_Sp"
'        .Parameters.Refresh
'        .Parameters(0).Value = 0
'    End With
'
'    rsBudgetFcst.CursorLocation = adUseClient
'    rsBudgetFcst.LockType = adLockReadOnly
'    RtnCode = BudgetFcst_GetEq( _
'        AIM_Repository_Get_Sp, _
'        rsBudgetFcst, _
'        RepositoryKey, _
'        LcId, _
'        ItemId)
'    If RtnCode < 0 Then
'        DpPopulateBudgetFcstRcdQty = False
'        GoTo CleanUp
'        Exit Function
'    End If
'
'' Dim RtnCode As Integer
''    Dim CurStartdate As Date
''    Dim Enddate As Date
''CurStartdate = StartDate
''    For Counter = 1 To NbrPds
''        Enddate = GetNextStartDate(StartDate, CurStartdate, Interval)
''        BudgetOrForecast(Counter).StartDate = CurStartdate
''        BudgetOrForecast(Counter).Enddate = Enddate - 1
''        CurStartdate = Enddate
''        BudgetOrForecast(Counter).Fy = GetFiscalYear(StartDate, AIMYears())
''    Next Counter
'
'    If f_IsRecordsetOpenAndPopulated(rsBudgetFcst) Then
'        TotalRec = rsBudgetFcst.RecordCount
'        If TotalRec = 0 Then
'            DpPopulateBudgetFcstRcdQty = False
'            GoTo CleanUp
'            Exit Function
'        End If
'        'Load the Budget or Forecast data
'        ReDim BudgetOrForecast(1 To TotalRec)
'        For Counter = 1 To TotalRec
'            BudgetOrForecast(Counter).QtyFcst = 0
'            BudgetOrForecast(Counter).QtyFcstNetReq = 0
'            BudgetOrForecast(Counter).QtyAdjust = 0
'            BudgetOrForecast(Counter).QtyActualOrdered = 0
'            BudgetOrForecast(Counter).QtyActualShipped = 0
'            BudgetOrForecast(Counter).StartDate = #1/1/1900#
'            BudgetOrForecast(Counter).Enddate = #1/1/1900#
'            BudgetOrForecast(Counter).Fy = 1900
'        Next Counter
'        Counter = 1
'        Do Until rsBudgetFcst.eof
'            'Get the Demand History Period Number
'
'                BudgetOrForecast(Counter).QtyFcst = rsBudgetFcst("QtyFcst").Value
'                BudgetOrForecast(Counter).QtyFcstNetReq = rsBudgetFcst("QtyFcstNetReq").Value
'                BudgetOrForecast(Counter).QtyAdjust = rsBudgetFcst("QtyAdjust").Value
'                BudgetOrForecast(Counter).QtyActualOrdered = rsBudgetFcst("QtyActualOrdered").Value
'                BudgetOrForecast(Counter).QtyActualShipped = rsBudgetFcst("QtyActualShipped").Value
'                BudgetOrForecast(Counter).StartDate = rsBudgetFcst("FcstPdStartDate").Value
'                BudgetOrForecast(Counter).Enddate = GetNextStartDate(rsBudgetFcst("FcstPdStartDate").Value, rsBudgetFcst("FcstPdStartDate").Value, Interval) - 1
'                BudgetOrForecast(Counter).Fy = GetFiscalYear(rsBudgetFcst("FcstPdStartDate").Value, AIMYears())
'            Counter = Counter + 1
'            rsBudgetFcst.MoveNext
'        Loop
'    End If
'
'    DpPopulateBudgetFcstRcdQty = True
'
'CleanUp:
'    If Not (AIM_Repository_Get_Sp Is Nothing) Then Set AIM_Repository_Get_Sp.ActiveConnection = Nothing
'    Set AIM_Repository_Get_Sp = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Repository_Get_Sp Is Nothing) Then Set AIM_Repository_Get_Sp.ActiveConnection = Nothing
'    Set AIM_Repository_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(dpAIMAdvForecastMod.DpPopulateBudgetFcstRcdQty)"
'End Function
'Public Function PopulateBudgetFcstRcdQty( _
'    RepositoryKey As Long, _
'    LcId As String, _
'    ItemId As String, _
'    Nbrpds As Integer _
') As Boolean
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_Repository_Get_Sp As New ADODB.Command
'    Dim rsBudgetFcst As New ADODB.Recordset
'    Dim Pd As Integer
'    Dim Counter As Integer
'    Dim TotalRec As Integer
'
'    With AIM_Repository_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_Repository_Get_Sp"
'        .Parameters.Refresh
'        .Parameters(0).Value = 0
'    End With
'
'    rsBudgetFcst.CursorLocation = adUseClient
'    rsBudgetFcst.LockType = adLockReadOnly
'    RtnCode = BudgetFcst_GetEq( _
'        AIM_Repository_Get_Sp, _
'        rsBudgetFcst, _
'        RepositoryKey, _
'        LcId, _
'        ItemId)
'    If RtnCode < 0 Then
'        PopulateBudgetFcstRcdQty = False
'        GoTo CleanUp
'        Exit Function
'    End If
'
'
'    If f_IsRecordsetOpenAndPopulated(rsBudgetFcst) Then
'        TotalRec = rsBudgetFcst.RecordCount
'        If TotalRec = 0 Then
'            PopulateBudgetFcstRcdQty = False
'            GoTo CleanUp
'            Exit Function
'        End If
'        'Load the Budget or Forecast data
'        For Counter = 1 To Nbrpds
'            BudgetOrForecast(Counter).QtyFcst = 0
'            BudgetOrForecast(Counter).QtyFcstNetReq = 0
'            BudgetOrForecast(Counter).QtyAdjust = 0
'            BudgetOrForecast(Counter).QtyActualOrdered = 0
'            BudgetOrForecast(Counter).QtyActualShipped = 0
'        Next Counter
'        Counter = 1
'        Do Until rsBudgetFcst.eof
'            'Get the Demand History Period Number
'            If BudgetOrForecast(Counter).StartDate = rsBudgetFcst("FcstPdStartDate").Value Then
'                BudgetOrForecast(Counter).QtyFcst = rsBudgetFcst("QtyFcst").Value
'                BudgetOrForecast(Counter).QtyFcstNetReq = rsBudgetFcst("QtyFcstNetReq").Value
'                BudgetOrForecast(Counter).QtyAdjust = rsBudgetFcst("QtyAdjust").Value
'                BudgetOrForecast(Counter).QtyActualOrdered = rsBudgetFcst("QtyActualOrdered").Value
'                BudgetOrForecast(Counter).QtyActualShipped = rsBudgetFcst("QtyActualShipped").Value
'            End If
'            Counter = Counter + 1
'            rsBudgetFcst.MoveNext
'        Loop
'    End If
'
'    PopulateBudgetFcstRcdQty = True
'
'CleanUp:
'    If Not (AIM_Repository_Get_Sp Is Nothing) Then Set AIM_Repository_Get_Sp.ActiveConnection = Nothing
'    Set AIM_Repository_Get_Sp = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Repository_Get_Sp Is Nothing) Then Set AIM_Repository_Get_Sp.ActiveConnection = Nothing
'    Set AIM_Repository_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateBudgetFcstRcdQty)"
'End Function
'
'
'Public Function PopulateFcstOrNetReqRcdQty( _
'    RepositoryKey As Long, _
'    LcId As String, _
'    ItemId As String, _
'    Nbrpds As Integer _
') As Boolean
'On Error GoTo ErrorHandler
''used to get data from the repository table directly
'    Dim RtnCode As Integer
'    Dim AIM_BatchFcst_Get_Sp As New ADODB.Command
'    Dim rsBudgetFcst As New ADODB.Recordset
'    Dim Pd As Integer
'    Dim Counter As Integer
'    Dim TotalRec As Integer
'
'    With AIM_BatchFcst_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_BatchFcst_Get_Sp"
'        .Parameters.Refresh
'        .Parameters(0).Value = 0
'    End With
'
'    rsBudgetFcst.CursorLocation = adUseClient
'    rsBudgetFcst.LockType = adLockReadOnly
'    RtnCode = BatchFcst_GetEq( _
'        AIM_BatchFcst_Get_Sp, _
'        rsBudgetFcst, _
'        RepositoryKey, _
'        LcId, _
'        ItemId)
'
'    If RtnCode < 0 Then
'        PopulateFcstOrNetReqRcdQty = False
'        GoTo CleanUp
'        Exit Function
'    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsBudgetFcst) Then
'        TotalRec = rsBudgetFcst.RecordCount
'        If TotalRec = 0 Then
'            PopulateFcstOrNetReqRcdQty = False
'            GoTo CleanUp
'            Exit Function
'        End If
'    'Load the Budget or Forecast data
'        For Counter = 1 To Nbrpds
'            FcstOrNetReqRecord(Counter).SysFcst = 0
'            FcstOrNetReqRecord(Counter).SysNet = 0
'            FcstOrNetReqRecord(Counter).ModSysFcst = 0
'            FcstOrNetReqRecord(Counter).ModSysNet = 0
'            FcstOrNetReqRecord(Counter).PlannedRct = 0
'        Next Counter
'        Counter = 1
'        Do Until rsBudgetFcst.eof
'            'Get the Demand History Period Number
'            FcstOrNetReqRecord(Counter).SysFcst = rsBudgetFcst("SysFcst").Value
'            FcstOrNetReqRecord(Counter).SysNet = rsBudgetFcst("SysNet").Value
'            FcstOrNetReqRecord(Counter).ModSysFcst = rsBudgetFcst("ModSysFcst").Value
'            FcstOrNetReqRecord(Counter).ModSysNet = rsBudgetFcst("ModSysNet").Value
'            FcstOrNetReqRecord(Counter).PlannedRct = rsBudgetFcst("PlannedRct").Value
'            Counter = Counter + 1
'            rsBudgetFcst.MoveNext
'        Loop
'    End If
'
'    PopulateFcstOrNetReqRcdQty = True
'
'CleanUp:
'    If Not (AIM_BatchFcst_Get_Sp Is Nothing) Then Set AIM_BatchFcst_Get_Sp.ActiveConnection = Nothing
'    Set AIM_BatchFcst_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_BatchFcst_Get_Sp Is Nothing) Then Set AIM_BatchFcst_Get_Sp.ActiveConnection = Nothing
'    Set AIM_BatchFcst_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateFcstOrNetReqRcdQty)"
'End Function
'
'Private Function GetRepositoryKey() As Boolean
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_ForecastRepository_GetKey_Sp As ADODB.Command
'
'    Set AIM_ForecastRepository_GetKey_Sp = New ADODB.Command
'    With AIM_ForecastRepository_GetKey_Sp
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_ForecastRepository_GetKey_Sp"
'        .CommandType = adCmdStoredProc
'        '.Parameters.Refresh
'    End With
'
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@Return", adInteger, adParamReturnValue, , 0)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@FcstID", adVarChar, adParamInput, 12, FcstId)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@FcstType", adVarChar, adParamInput, 30, Fcsttype)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@RepositoryKey", adInteger, adParamOutput, , 0)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@EnableFcstVersions ", adVarChar, adParamOutput, 1, "N")
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'
'    AIM_ForecastRepository_GetKey_Sp.Execute
'    RtnCode = AIM_ForecastRepository_GetKey_Sp("@Return")
'    If RtnCode < 0 Then
'        FcstKey = 0
'    Else
'        FcstKey = AIM_ForecastRepository_GetKey_Sp("@RepositoryKey")
'    End If
'
'    If Not (AIM_ForecastRepository_GetKey_Sp Is Nothing) Then Set AIM_ForecastRepository_GetKey_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_GetKey_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_ForecastRepository_GetKey_Sp Is Nothing) Then Set AIM_ForecastRepository_GetKey_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_GetKey_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetRepositoryKey)"
'End Function
'
'Private Function SaleBetweenDates( _
'    StartDate As Date, _
'    Enddate As Date, _
'    Sale As Double, _
'    Order As Double, _
'    LcId As String, _
'    ItemId As String _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim Counter As Integer
'    Dim EndWeekDays As Integer
'    Dim EndWeekPct As Double
'    Dim FirstStartDate As Date
'    Dim LastStartDays As Integer
'    Dim StartWeekDays As Integer
'    Dim StartWeekPct As Double
'    Dim LastStartDate As Date
'    Dim Price As Double
'    Dim Cost As Double
'    Dim DailyPct As Double
'
'    Sale = 0
'    Order = 0
'    'Calculate the First Date of the First Period
'    FirstStartDate = GetStartDateFmDate(StartDate, AIMYears())
'    LastStartDate = GetStartDateFmDate(Enddate, AIMYears())
'    If FirstStartDate = 0 _
'    Or LastStartDate = 0 _
'    Then
'        Exit Function
'    End If
'
'    'Calculate the number of working days in the first period
'    StartWeekDays = GetWorkingDaysInWeek(StartDate, AIMDays(), AIMYears())
'    'Calculate the number of working days in the last period
'    EndWeekDays = GetWorkingDaysInWeek(Enddate, AIMDays(), AIMYears())
'    'If the Startdate and Enddate are the same then it is forecast interval is days
'    If StartDate = Enddate Then
'        DailyPct = GetWorkingDays(StartDate, Enddate, AIMDays(), AIMYears()) / EndWeekDays
'        StartWeekPct = 0
'        EndWeekPct = 0
'    Else
'        'Calculate the Start Week Percentages
'        If StartWeekDays > 0 Then
'            StartWeekPct = GetWorkingDays(StartDate, FirstStartDate + 6, AIMDays(), AIMYears()) / StartWeekDays
'        Else
'            StartWeekPct = 0
'        End If
'        'Calculate the End Week Percentages
'        If EndWeekDays > 0 Then
'            EndWeekPct = GetWorkingDays(LastStartDate, Enddate, AIMDays(), AIMYears()) / EndWeekDays
'        Else
'            EndWeekPct = 0
'        End If
'    End If
'    If StartDate = Enddate Then
'        For Counter = 1 To UBound(ActualSalesAndOrders())
'            If ActualSalesAndOrders(Counter).StartDate > StartDate - 7 _
'            And ActualSalesAndOrders(Counter).Enddate < Enddate + 7 _
'            Then
'                Sale = Sale + ActualSalesAndOrders(Counter).Sale
'                Order = Order + ActualSalesAndOrders(Counter).Order
'            End If
'        Next Counter
'    Else
'        For Counter = 1 To UBound(ActualSalesAndOrders())
'        If ActualSalesAndOrders(Counter).StartDate > StartDate - 7 _
'            And ActualSalesAndOrders(Counter).Enddate < Enddate + 7 _
'            Then
'                If ActualSalesAndOrders(Counter).StartDate <= StartDate Then
'                    Sale = Sale + ActualSalesAndOrders(Counter).Sale * StartWeekPct
'                    Order = Order + ActualSalesAndOrders(Counter).Order * StartWeekPct
'                ElseIf ActualSalesAndOrders(Counter).StartDate + 7 > Enddate Then
'                    Sale = Sale + ActualSalesAndOrders(Counter).Sale * EndWeekPct
'                    Order = Order + ActualSalesAndOrders(Counter).Order * EndWeekPct
'                Else
'                    Sale = Sale + ActualSalesAndOrders(Counter).Sale
'                    Order = Order + ActualSalesAndOrders(Counter).Order
'                End If
'            End If
'        Next Counter
'    End If
'    If StartDate = Enddate Then
'        Sale = Sale * DailyPct
'        Order = Order * DailyPct
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.SaleBetweenDates)"
'End Function
'
'Public Function PopulateModificationRcdQtyTEST( _
'    RepositoryKey As Long, _
'    LcId As String, _
'    ItemId As String _
'    , Nbrpds As Integer _
') As Boolean
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_ForecastRepository_Get_Sp As New ADODB.Command
'    Dim rsBudgetFcst As New ADODB.Recordset
'    Dim Pd As Integer
'    Dim Counter As Integer
'    Dim TotalRec As Integer
'
'    With AIM_ForecastRepository_Get_Sp
'    Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepository_Get_Sp"
'        .Parameters.Refresh
'        .Parameters(0).Value = 0
'    End With
'
'    rsBudgetFcst.CursorLocation = adUseClient
'    rsBudgetFcst.LockType = adLockReadOnly
'    RtnCode = BudgetFcst_GetEq( _
'        AIM_ForecastRepository_Get_Sp, _
'        rsBudgetFcst, _
'        RepositoryKey, _
'        LcId, _
'        ItemId)
'
'    If RtnCode < 0 Then
'        PopulateModificationRcdQtyTEST = False
'        GoTo CleanUp
'        Exit Function
'    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsBudgetFcst) Then
'        TotalRec = rsBudgetFcst.RecordCount
'        If TotalRec = 0 Then
'            PopulateModificationRcdQtyTEST = False
'            GoTo CleanUp
'            Exit Function
'        End If
'        For Counter = 1 To Nbrpds
'            BudgetOrForecast(Counter).QtyFcst = 0
'            BudgetOrForecast(Counter).QtyFcstNetReq = 0
'            BudgetOrForecast(Counter).QtyAdjust = 0
'            BudgetOrForecast(Counter).QtyActualOrdered = 0
'            BudgetOrForecast(Counter).QtyActualShipped = 0
'        Next Counter
'        'Load the Budget or Forecast data
'        Counter = 1
'        Do Until rsBudgetFcst.eof
'           'Get the Demand History Period Number
'            BudgetOrForecast(Counter).QtyFcst = rsBudgetFcst("QtyFcst").Value
'            BudgetOrForecast(Counter).QtyFcstNetReq = rsBudgetFcst("QtyFcstNetReq").Value
'            BudgetOrForecast(Counter).QtyAdjust = rsBudgetFcst("QtyAdjust").Value
'            BudgetOrForecast(Counter).QtyActualOrdered = rsBudgetFcst("QtyActualOrdered").Value
'            BudgetOrForecast(Counter).QtyActualShipped = rsBudgetFcst("QtyActualShipped").Value
'            Counter = Counter + 1
'            rsBudgetFcst.MoveNext
'        Loop
'    End If
'
'    PopulateModificationRcdQtyTEST = True
'
'CleanUp:
'    If Not (AIM_ForecastRepository_Get_Sp Is Nothing) Then Set AIM_ForecastRepository_Get_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_ForecastRepository_Get_Sp Is Nothing) Then Set AIM_ForecastRepository_Get_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsBudgetFcst) Then rsBudgetFcst.Close
'    Set rsBudgetFcst = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateModificationRcdQtyTEST)"
'End Function
'
'Private Function BatchFcst_GetEq( _
'    AIM_BatchFcst_Get_Sp As ADODB.Command, _
'    rs As ADODB.Recordset, _
'    RepKey As Long, _
'    LcIdKey As String, _
'    ItemKey As String _
') As Integer
'On Error GoTo ErrorHandler
'
'    'Get the Location Data
'    With AIM_BatchFcst_Get_Sp
'        .Parameters(0).Value = 0
'        .Parameters("@RepositoryKey").Value = RepKey
'        .Parameters("@LcId").Value = LcIdKey
'        .Parameters("@Item").Value = ItemKey
'    End With
'
'    If Not f_IsRecordsetValidAndOpen(rs) Then
'        rs.Open AIM_BatchFcst_Get_Sp
'    Else
'        If rs.EditMode = adEditInProgress Then
'            rs.CancelUpdate
'        End If
'
'        rs.Requery
'    End If
'
'    BatchFcst_GetEq = AIM_BatchFcst_Get_Sp(0).Value
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.BatchFcst_GetEq)"
'End Function
'
'Private Function RunProdConst( _
'    XFcstArray As XArrayDB, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    'Sri add code for prodconst start new function
'    Dim I As Integer
'    Dim j As Integer
'    Dim Rtn As Integer
'
'    Dim xProdConstData As New XArrayDB
'    Dim UniqueId As String
'    Dim Jobdate As Date
'
'    Jobdate = Now()
'    'Initilize the variables used in prodconst calc
'    Initilize_ProdConst
'    'Get unique id  and delete date for the unique id if any thing is left from old job
'    UniqueId = GetUnique_Id
'
'    'For each Period data populate the Lcid Item and Period data to the table
'    'Run ProdConstSizing Procedure Get data back and populate the changed data
'    'back repeat the process for all the period data
'
'    For j = 13 To 13 + Nbrpds - 1
'        For I = XFcstArray.LowerBound(1) To XFcstArray.UpperBound(1)
'            Populate_Temp _
'                UniqueId, _
'                XFcstArray.Value(I, 0), _
'                XFcstArray.Value(I, 1), _
'                XFcstArray.Value(I, j)
'        Next
'        'Run actual ProdcutionConstrint logic
'        'AIM_ProductionConstraintCtrl_Sp
'
'        With AIM_ProductionConstraintCtrl_Sp
'            .Parameters("@UniqueJobId").Value = UniqueId
'            .Execute
'        End With
'
'        'Get back date from the table
'        With AIM_ProdConstraintTemp_Get_Sp
'            .Parameters("@UniqueJobId").Value = UniqueId
'        End With
'        rsGetProdConstData.Open AIM_ProdConstraintTemp_Get_Sp
'
'        'Populate the recordset into an xarray
'        xProdConstData.LoadRows (rsGetProdConstData.GetRows(rsGetProdConstData.RecordCount))
'        rsGetProdConstData.Close
'
'        'Delete the data we inserted into the table we are done
'        cmdDelete.Execute
'
'        'Populate the ProdConstrainedData to the approprate location
'        For I = XFcstArray.LowerBound(1) To XFcstArray.UpperBound(1)
'            XFcstArray.Value(I, j) = xProdConstData(I, 4)
'        Next
'    Next
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.RunProdConst)"
'End Function
'
'Private Function RunProdConstForGenerator( _
'    XFcstArray As XArrayDB, _
'    Nbrpds As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    'Sri add code for prodconst start new function
'    Dim I As Integer
'    Dim j As Integer
'    Dim Rtn As Integer
'
'    Dim xProdConstData As New XArrayDB
'    Dim UniqueId As String
'    Dim Jobdate As Date
'
'    Jobdate = Now()
'    'Initilize the variables used in prodconst calc
'    Initilize_ProdConst
'    'Get unique id  and delete date for the unique id if any thing is left from old job
'    UniqueId = GetUnique_Id
'
'    'For each Period data populate the Lcid Item and Period data to the table
'    'Run ProdConstSizing Procedure Get data back and populate the changed data
'    'back repeat the process for all the period data
'
'    For j = 4 To 4 + Nbrpds 'For j = 13 To 13 + Nbrpds - 1
'        For I = XFcstArray.LowerBound(1) To XFcstArray.UpperBound(1)
'            'Populate_Temp UniqueId, XFcstArray.Value(i, 0), XFcstArray.Value(i, 1), XFcstArray.Value(i, j)
'            Populate_Temp _
'                UniqueId, _
'                XFcstArray.Value(I, 1), _
'                XFcstArray.Value(I, 0), _
'                XFcstArray.Value(I, j)
'        Next
'        'Run actual ProdcutionConstrint logic
'        'AIM_ProductionConstraintCtrl_Sp
'
'        With AIM_ProductionConstraintCtrl_Sp
'            .Parameters("@UniqueJobId").Value = UniqueId
'            .Execute
'        End With
'
'        'Get back date from the table
'        With AIM_ProdConstraintTemp_Get_Sp
'            .Parameters("@UniqueJobId").Value = UniqueId
'        End With
'        rsGetProdConstData.Open AIM_ProdConstraintTemp_Get_Sp
'
'        'Populate the recordset into an xarray
'        xProdConstData.LoadRows (rsGetProdConstData.GetRows(rsGetProdConstData.RecordCount))
'        rsGetProdConstData.Close
'
'        'Delete the data we inserted into the table we are done
'        cmdDelete.Execute
'
'        'Populate the ProdConstrainedData to the approprate location
'        For I = XFcstArray.LowerBound(1) To XFcstArray.UpperBound(1)
'            XFcstArray.Value(I, j) = xProdConstData(I, 4)
'        Next
'    Next
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.RunProdConstForGenerator)"
'End Function
'
'Private Function Populate_Temp( _
'    UniqueJobId As String, _
'    LcIdKey As String, _
'    ItemKey As String, _
'    Qty As Long _
') As Integer
'On Error GoTo ErrorHandler
'
'    'Sri add code for prodconst start new function
'     With AIM_ProdConstraintTemp_Save_Sp
'        .Parameters("@UniqueJobId") = UniqueJobId
'        .Parameters("@lcid") = LcIdKey
'        .Parameters("@item") = ItemKey
'        .Parameters("@Qty") = Qty
'        .Execute
'    End With
'    Populate_Temp = AIM_ProdConstraintTemp_Save_Sp.Parameters(0).Value
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.Populate_Temp)"
'End Function
'
'Private Function GetUnique_Id() As String
'On Error GoTo ErrorHandler
'
'    'Sri add code for prodconst start new function
'    'Get UniqueId so that we can identify the data we inserted into the table
'    Dim UniqueId As String
'
'    rsUniqueId.Open AIM_GuId_Get_SP
'    UniqueId = rsUniqueId(0).Value
'
'    'Make sure that the data is not there in the table initially just a precaution
'    cmdDelete.ActiveConnection = Cn
'    cmdDelete.CommandText = "Delete from ProdConstraintTemp  where  UniqueJobId = N'" & UniqueId & "'"
'    cmdDelete.CommandType = adCmdText
'    cmdDelete.Execute
'
'    GetUnique_Id = UniqueId
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetUnique_Id)"
'End Function
'
'Private Function Initilize_ProdConst() As Integer
'On Error GoTo ErrorHandler
'
'    Initilize_ProdConst = -1
'    'Sri add code for prodconst start new function
'    With AIM_ProductionConstraintCtrl_Sp
'        .CommandType = adCmdStoredProc
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_ProductionConstraintCtrl_Sp"
'        .Parameters.Refresh
'    End With
'
'    With AIM_ProdConstraintTemp_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ProdConstraintTemp_Get_Sp"
'        .Parameters.Refresh
'    End With
'
'    With rsGetProdConstData
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'    End With
'
'    With AIM_GuId_Get_SP
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GuId_Get_SP"
'        .Parameters.Refresh
'    End With
'
'    With AIM_ProdConstraintTemp_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ProdConstraintTemp_Save_Sp"
'        .Parameters.Refresh
'    End With
'
'    With rsUniqueId
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'    End With
'
'    Initilize_ProdConst = 1
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.Initilize_ProdConst)"
'End Function
'
'Public Function GetSalesAndOrders( _
'    FcstId As String, _
'    xForeCast As XArrayDB, _
'    FcstStartdateArg As Date, _
'    IntToFcstArg As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    FcstStartDate = FcstStartdateArg
'    FcstInterval = GetForecastInterval(IntToFcstArg)
'
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "SalesAndOrders")
'
'    GetSalesAndOrders = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetSalesAndOrders)"
'End Function
'
'Public Function GetSalesAndOrdersLastYear( _
'    FcstId As String, _
'    xForeCast As XArrayDB, _
'    FcstStartdateArg As Date, _
'    IntToFcstArg As Integer _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    FcstStartDate = FcstStartdateArg
'    FcstInterval = GetForecastInterval(IntToFcstArg)
'    RtnCode = AdvForecastMain(FcstId, xForeCast, "SalesAndOrdersLastYear")
'    GetSalesAndOrdersLastYear = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetSalesAndOrdersLastYear)"
'End Function
'
'Private Function PopulateSalesAndOrders( _
'    StartDate As Date, _
'    Rows As Long, _
'    Nbrpds As Integer, _
'    Interval As AIM_INTERVALS, _
'    rs As ADODB.Recordset, _
'    xSalesAndOrders As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RowsUnitStart As Long
'    Dim ForecastLoop As Long
'    Dim Enddate As Date
'    Dim Counter As Long
'    Dim CurStartdate As Date
'    Dim PdStartDate As Date
'    Dim PdEndDate As Date
'    Dim RtnCode As Integer
'    Dim Sales As Double
'    Dim Order As Double
'    Dim Row As Long
'    Dim I As Long
'    Dim RowsOrderStart As Long
'    Dim SalePrice As Double
'    Dim OrderPrice As Double
'    Dim SaleCost As Double
'    Dim OrderCost As Double
'    Dim JLoop As Long
'    Dim KLoop As Long
'    Dim TypeInc As Long
'    Dim SalesYes  As Boolean 'Flag to see if there is sale and order history data
'    Dim TotalSaleUnits As Double
'    Dim TotalSaleCost As Double
'    Dim TotalSaleCube As Double
'    Dim TotalSaleWeight As Double
'    Dim TotalSalePrice As Double
'    Dim TotalOrderUnits As Double
'    Dim TotalOrderCost As Double
'    Dim TotalOrderCube As Double
'    Dim TotalOrderWeight As Double
'    Dim TotalOrderPrice As Double
'    Dim TotalRows As Long
'    Dim Cube As Double
'    Dim Weight As Double
'
'    'Initialize Loop Counters
'    RowsUnitStart = 0               'Starting position for Unit data
'    RowsOrderStart = Rows * 1       'Starting position for Order data
'    'Cal the number of periods will be there for the inputed values of startdate and interval
'    Nbrpds = GetNumPdsToFcst(Interval, StartDate, Nbrpds)
'    ' Now change the startdate and interval to the values we want the forecast
'    StartDate = FcstStartDate
'    Interval = FcstInterval
'    'Load the Calender
'
'    CurStartdate = StartDate
'    For Counter = 1 To Nbrpds + 1
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, Interval)
'    Next Counter
'    Enddate = CurStartdate
'    PdStartDate = StartDate
'    TotalRows = (Rows * 2 - 1) '  2 is sales and orders
'    'Redimension the Forecast Array
'    xSalesAndOrders.ReDim 0, TotalRows, 0, (Nbrpds + 22)
'    For Row = 0 To (Rows - 1)
'        'Initialize Totals
'        TotalSaleUnits = 0
'        TotalSaleCost = 0
'        TotalSaleCube = 0
'        TotalSaleWeight = 0
'        TotalSalePrice = 0
'        TotalOrderUnits = 0
'        TotalOrderCost = 0
'        TotalOrderCube = 0
'        TotalOrderWeight = 0
'        TotalOrderPrice = 0
'        'Position the recordset
'        rs.AbsolutePosition = Row + 1
'        'Loads the ActualSalesAndOrders array
'        SalesYes = LoadAimCalendar(rs("lcid").Value, rs("item").Value, StartDate, Enddate)
'        'Sales date
'        TypeInc = RowsUnitStart
'         GetFYPriceCost rs("lcid").Value, rs("Item").Value
'        Cube = rs("Cube")
'        Weight = rs("Weight")
'        strMessage = getTranslationResource("STATMSG90800")
'        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
'        Write_Message strMessage & " " & Trim$(rs("lcid").Value) & _
'            getTranslationResource("/") & rs("item").Value
'        For JLoop = 1 To 2
'            'Update data for slae and orders
'            xSalesAndOrders.Value(TypeInc, 0) = rs("lcid").Value
'            xSalesAndOrders.Value(TypeInc, 1) = rs("item").Value
'            xSalesAndOrders.Value(TypeInc, 2) = rs("itdesc").Value
'            xSalesAndOrders.Value(TypeInc, 3) = rs("Class1").Value
'            xSalesAndOrders.Value(TypeInc, 4) = rs("Class2").Value
'            xSalesAndOrders.Value(TypeInc, 5) = rs("Class3").Value
'            xSalesAndOrders.Value(TypeInc, 6) = rs("Class4").Value
'            xSalesAndOrders.Value(TypeInc, 7) = rs("ItStat").Value
'            xSalesAndOrders.Value(TypeInc, 8) = rs("VelCode").Value
'            xSalesAndOrders.Value(TypeInc, 9) = rs("VnId").Value
'            xSalesAndOrders.Value(TypeInc, 10) = rs("Assort").Value
'            xSalesAndOrders.Value(TypeInc, 11) = rs("ById").Value
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 13) = rs("Cube").Value
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 14) = rs("Weight").Value
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 15) = LastFYPrice
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 16) = ThisFYPrice
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 17) = NextFYPrice
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 18) = LastFYCost
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 19) = ThisFYCost
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 20) = NextFYCost
'            xSalesAndOrders.Value(TypeInc, Nbrpds + 22) = rs("DemandSource").Value
'            TypeInc = TypeInc + Rows
'        Next JLoop
'        For JLoop = 0 To TotalRows
'            ' Populate the last column with ForecastUnit for Sale and Order
'            If JLoop < RowsOrderStart Then
'                xSalesAndOrders.Value(JLoop, Nbrpds + 21) = "Actual Shipped"
'            Else
'                xSalesAndOrders.Value(JLoop, Nbrpds + 21) = "Actual Ordered"
'            End If
'        Next JLoop
'        For I = 13 To (Nbrpds + 12)
'            Sales = 0
'            Order = 0
'            SalePrice = 0
'            OrderPrice = 0
'            PdEndDate = GetNextStartDate(StartDate, PdStartDate, Interval)
'            'Calculate sales,order,SalePrice,OrderPrice
'            If SalesYes = True Then
'                SaleBetweenDates PdStartDate, PdEndDate - 1, Sales, Order, rs("lcid").Value, rs("item").Value
'            Else
'                Sales = 0
'                Order = 0
'                SalePrice = 0
'                OrderPrice = 0
'            End If
'            PdStartDate = PdEndDate
'            'Sales
'            xSalesAndOrders.Value(RowsUnitStart, I) = Sales
'            'Orders
'            xSalesAndOrders.Value(RowsOrderStart + RowsUnitStart, I) = Order
'            TotalSaleUnits = TotalSaleUnits + Sales
'            TotalOrderUnits = TotalOrderUnits + Order
'        Next I
'        xSalesAndOrders.Value(RowsUnitStart, 12) = TotalSaleUnits
'        xSalesAndOrders.Value(RowsOrderStart + RowsUnitStart, 12) = TotalOrderUnits
'        'Reset the PdStartDate
'        PdStartDate = StartDate
'
'        RowsUnitStart = RowsUnitStart + 1
'    Next Row
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateSalesAndOrders)"
'End Function
'
'Private Function PopulateSalesAndOrdersLastYear( _
'    StartDate As Date, _
'    Rows As Long, _
'    Nbrpds As Integer, _
'    Interval As AIM_INTERVALS, _
'    rs As ADODB.Recordset, _
'    xSalesAndOrders As XArrayDB _
') As Integer
'On Error GoTo ErrorHandler
'
'    Dim RowsUnitStart As Long
'    Dim ForecastLoop As Long
'    Dim Enddate As Date
'    Dim Counter As Long
'    Dim CurStartdate As Date
'    Dim PdStartDate As Date
'    Dim PdEndDate As Date
'    Dim RtnCode As Integer
'    Dim Sales As Double
'    Dim Order As Double
'    Dim Row As Long
'    Dim I As Long
'    Dim RowsOrderStart As Long
'    Dim SalePrice As Double
'    Dim OrderPrice As Double
'    Dim SaleCost As Double
'    Dim OrderCost As Double
'    Dim JLoop As Long
'    Dim KLoop As Long
'    Dim TypeInc As Long
'    Dim SalesYes  As Boolean 'Flag to see if there is sale and order history data
'    Dim TotalSaleUnits As Double
'    Dim TotalSaleCost As Double
'    Dim TotalSaleCube As Double
'    Dim TotalSaleWeight As Double
'    Dim TotalSalePrice As Double
'    Dim TotalOrderUnits As Double
'    Dim TotalOrderCost As Double
'    Dim TotalOrderCube As Double
'    Dim TotalOrderWeight As Double
'    Dim TotalOrderPrice As Double
'    Dim TotalRows As Long
'    Dim Cube As Double
'    Dim Weight As Double
'
'    'Initialize Loop Counters
'    RowsUnitStart = 0               'Starting position for Unit data
'    RowsOrderStart = Rows * 1       'Starting position for Order data
'    ' Now change the startdate and interval to the values we want the forecast
'    StartDate = FcstStartDate
'    Interval = FcstInterval
'    'Load the Calender
'
'    'Change the startdate to a date which is one year before this startdate
'    StartDate = DateAdd("yyyy", -1, StartDate)
'    CurStartdate = StartDate
'
'    'Get the forecast end date
'    For Counter = 1 To Nbrpds
'        CurStartdate = GetNextStartDate(StartDate, CurStartdate, Interval)
'        If CurStartdate >= FcstStartDate Then
'            Exit For
'        End If
'    Next Counter
'    Enddate = CurStartdate
'    PdStartDate = StartDate
'    TotalRows = (Rows * 2 - 1) '  2 is sales and orders
'    'Redimension the Forecast Array
'    'We will get the total sales and order for the whole of the forecast period so set nbrpds =1
'    Nbrpds = 1
'    xSalesAndOrders.ReDim 0, TotalRows, 0, 22
'    For Row = 0 To (Rows - 1)
'        'Initialize Totals
'        TotalSaleUnits = 0
'        TotalSaleCost = 0
'        TotalSaleCube = 0
'        TotalSaleWeight = 0
'        TotalSalePrice = 0
'        TotalOrderUnits = 0
'        TotalOrderCost = 0
'        TotalOrderCube = 0
'        TotalOrderWeight = 0
'        TotalOrderPrice = 0
'        'Position the recordset
'        rs.AbsolutePosition = Row + 1
'        'Loads the ActualSalesAndOrders array
'        SalesYes = LoadAimCalendar(rs("lcid").Value, rs("item").Value, StartDate, Enddate - 1)
'        'Sales date
'        TypeInc = RowsUnitStart
'
'        GetFYPriceCost _
'            rs("lcid").Value, _
'            rs("Item").Value
'        Cube = rs("Cube")
'        Weight = rs("Weight")
'        strMessage = getTranslationResource("STATMSG90800")
'        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
'        Write_Message strMessage & " " & Trim$(rs("lcid").Value) & _
'            getTranslationResource("/") & rs("item").Value
'        For JLoop = 1 To 2
'            'Update data for slae and orders
'            xSalesAndOrders.Value(TypeInc, 0) = rs("lcid").Value
'            xSalesAndOrders.Value(TypeInc, 1) = rs("item").Value
'            xSalesAndOrders.Value(TypeInc, 2) = rs("itdesc").Value
'            xSalesAndOrders.Value(TypeInc, 3) = rs("Class1").Value
'            xSalesAndOrders.Value(TypeInc, 4) = rs("Class2").Value
'            xSalesAndOrders.Value(TypeInc, 5) = rs("Class3").Value
'            xSalesAndOrders.Value(TypeInc, 6) = rs("Class4").Value
'            xSalesAndOrders.Value(TypeInc, 7) = rs("ItStat").Value
'            xSalesAndOrders.Value(TypeInc, 8) = rs("VelCode").Value
'            xSalesAndOrders.Value(TypeInc, 9) = rs("VnId").Value
'            xSalesAndOrders.Value(TypeInc, 10) = rs("Assort").Value
'            xSalesAndOrders.Value(TypeInc, 11) = rs("ById").Value
'            xSalesAndOrders.Value(TypeInc, 13) = rs("Cube").Value
'            xSalesAndOrders.Value(TypeInc, 14) = rs("Weight").Value
'            xSalesAndOrders.Value(TypeInc, 15) = LastFYPrice
'            xSalesAndOrders.Value(TypeInc, 16) = ThisFYPrice
'            xSalesAndOrders.Value(TypeInc, 17) = NextFYPrice
'            xSalesAndOrders.Value(TypeInc, 18) = LastFYCost
'            xSalesAndOrders.Value(TypeInc, 19) = ThisFYCost
'            xSalesAndOrders.Value(TypeInc, 20) = NextFYCost
'            xSalesAndOrders.Value(TypeInc, 21) = rs("DemandSource").Value
'            TypeInc = TypeInc + Rows
'        Next JLoop
'        For JLoop = 0 To TotalRows
'            ' Populate the last column with ForecastUnit for Sale and Order
'            If JLoop < RowsOrderStart Then
'                xSalesAndOrders.Value(JLoop, 22) = "Actual Shipped"
'            Else
'                xSalesAndOrders.Value(JLoop, 22) = "Actual Ordered"
'            End If
'        Next JLoop
'        Sales = 0
'        Order = 0
'        SalePrice = 0
'        OrderPrice = 0
'        'PdEndDate = GetNextStartDate(Startdate, PdStartDate, Interval)
'        ' set the pdenddate to enddate so we get the sales and orders between startdate and enddate
'        PdEndDate = Enddate
'        'Calculate sales,order,SalePrice,OrderPrice
'        If SalesYes = True Then
'            SaleBetweenDates _
'                PdStartDate, _
'                PdEndDate - 1, _
'                Sales, _
'                Order, _
'                rs("lcid").Value, _
'                rs("item").Value
'        Else
'            Sales = 0
'            Order = 0
'            SalePrice = 0
'            OrderPrice = 0
'        End If
'        'Sales
'        xSalesAndOrders.Value(RowsUnitStart, 12) = Sales
'        'Orders
'        xSalesAndOrders.Value(RowsOrderStart + RowsUnitStart, 12) = Order
'
'        'Reset the PdStartDate
'        PdStartDate = StartDate
'
'        RowsUnitStart = RowsUnitStart + 1
'    Next Row
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.PopulateSalesAndOrdersLastYear)"
'End Function
'
