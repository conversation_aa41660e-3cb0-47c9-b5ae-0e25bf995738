Attribute VB_Name = "AIMDemandPlanning"
''*****************************************************************************
'' Copyright (c) 2004 SSA Global. All rights reserved.
''*****************************************************************************
''
''   AIM_DemandPlanning.bas
''
''   Version Number - 1.0
''   Last Updated   - 2004/01/28
''   Updated By     - Annalakshmi Stocksdale
''
''   This replaces the former AIM*Forecast* files in allowing for the creation of forecast data.
''
''   The "forecast generator/modification" process is being morphed
''   into demand planning, hence the options required for forecast generator
''   are being phased out, and additional options for demand planning
''   are to be created, as of version 4.5
''   See related updates to AIM_ForecastModification.
''
''*****************************************************************************
'' This file contains trade secrets of SSA Global. No part
'' may be reproduced or transmitted in any form by any means or for any purpose
'' without the express written permission of SSA Global.
''*****************************************************************************
'Option Explicit
'Option Base 1   'Sets default for all arrays on this page to start at 1
'
'Public Enum TREND_ACTIONS   'Apply Trend to Forecast
'    TA_NOTREND = 0
'    TA_ITEMPLANNEDTRENDTYPE = 1
'    TA_ALLITEMS = 2
'End Enum
'
'Public Enum ADJUST_TYPE   'Forecast qty adjustment factor
'    ADJ_OVERRIDE = 0
'    ADJ_UNITS = 1
'    ADJ_PERCENT = 2
'    ADJ_REMOVEOVERRIDE = 3
'    ADJ_REMOVEMASTEROVERRIDE = 4
'End Enum
'
''AIMCalendar Action Codes
'Public Enum CAL_ACTIONS
'    CA_PERIOD = 1
'    CA_FISCALYEAR = 2
'    CA_STARTDATEFMDATE = 3
'    CA_ENDDATEFMDATE = 4
'    CA_WORKINGDAYSINWEEK = 5
'    CA_WORKINGDAYS = 11
'    CA_STARTDATEFROMPERIOD = 21
'    CA_ENDDATEFROMPERIOD = 22
'End Enum
'
'Public Enum FCST_ACCESS
'    CODE_FullControl = 0
'    CODE_ModifyForecastSetup = 1
'    CODE_ModifyDemandPlan = 2
'    CODE_Read = 3
'    CODE_ControlAccess = 4
'    CODE_Promote = 5
'End Enum
'
'Public Type FORECAST_CRITERIA
'    FcstSetupKey As String
'    FcstId As String
'    FcstDesc As String
'    FcstLocked As Boolean
'    FcstEnabled As Boolean
'    FcstHierarchy As Long
'    FcstStartDate As Date
'    FcstRolling As Boolean
'    FcstPeriods_Future As Long
'    FcstHistory As Boolean
'    FcstPeriods_Historical As Long
'    ApplyTrend As Long
'    FcstInterval As Long
'    FcstUnit As Long
'    Calc_SysFcst As Boolean
'    Calc_Netreq As Boolean
'    Calc_ProjInv As Boolean
'    Calc_HistDmd As Boolean
'    Calc_MasterFcstAdj As Boolean
'    Calc_FcstAdj As Boolean
'    Calc_AdjNetReq As Boolean
'    'Filters
'    Item As String
'    ItStat As String
'    VnId As String
'    Assort As String
'    ById As String
'    Class1 As String
'    Class2 As String
'    Class3 As String
'    Class4 As String
'    LcId As String
'    LStatus As String
'    LDivision As String
'    LRegion As String
'    LUserDefined As String
'End Type
'
'Public Type FORECAST_ADJUST_LOG
'    FcstStoreKey() As Long
'    LcId() As String
'    Item() As String
'    AdjustQty() As Double
'    AdjustType() As ADJUST_TYPE
'    AdjustStartDate() As Date
'    AdjustEndDate() As Date
'    AdjustDesc() As String
'    AdjustUserID() As String
'    AdjustDateTime() As Date
'    IsNew() As Boolean
'End Type
'
'Public Type FORECAST_DATA
''RepositoryData
'    Item() As String
'    ItDesc() As String
'    LcId() As String
'    BaseDate() As Date
''Forecast results
'    PeriodStartDate() As Date
'    PeriodEndDate() As Date
'    DataCalc_Value() As Double
'End Type
'
'Public Enum DATA_CALC_TYPE
'    DCT_SysRqmt = 1
'    DCT_AdjSysRqmt = 2
'    DCT_NetRqmt = 3
'    DCT_AdjNetRqmt = 4
'    DCT_NetRqmtAndPrdCons = 5
'    DCT_NetRqmtAndPlnRcpt = 6
'    DCT_PrjInvt = 7
'    DCT_PrjInvtAndPrdCons = 8
'    DCT_HstDmnd = 9
'    DCT_CostAdjmt = 10
'    DCT_PriceAdjmt = 11
'    DCT_MAXVALUE = 12       'Used for loop boundaries et al. This should be the last and greatest in this set.
'End Enum
'
'Private mItemRecord As ADODB.Recordset
'Private mLogID As Long
'Private AIM_JobLog_Insert_Sp As ADODB.Command
''
''*****************************************************************************
''   FUNCTIONS
''*****************************************************************************
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Gets data from the AIMDmdPlanFcstSetup table to populate the ForecastID dropdown in the UI
'' .......................................................................
''
''   Returns false with an empty XArray if there are no records to fetch,
''   else true with an active XArray.
''
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Public Function gList_FcstIDs( _
'    p_Cn As ADODB.Connection, _
'    r_ForecastList As ADODB.Recordset, _
'    strFormCaption As String _
') As Boolean
'On Error GoTo ErrorHandler
'
'    Dim rsForecastList As ADODB.Recordset
'    Dim AIM_DmdPlanFcstID_Load_Sp As ADODB.Command
'    Dim RtnCode As Long
'    Dim Cn As ADODB.Connection
'    Dim ColCounter As Long
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    'Default to failure until the process returns its result.
'    gList_FcstIDs = False
'
'    'Get Forecast IDs and Descriptions from AIMForecast
'    Set AIM_DmdPlanFcstID_Load_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstID_Load_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstID_Load_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@FcstEnabled", adBoolean, adParamInput, , Null)
'        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12, gUserID)
'    'Output
'        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@RecordCount", adInteger, adParamOutput, , 0)
'    End With
'
'    Set rsForecastList = New ADODB.Recordset
'    With rsForecastList
'        .CursorLocation = adUseClient
'        .CursorType = adOpenForwardOnly
'        .LockType = adLockReadOnly
'        .Open AIM_DmdPlanFcstID_Load_Sp
'    End With
'
'    If AIM_DmdPlanFcstID_Load_Sp("@RecordCount").Value > 0 _
'    And f_IsRecordsetOpenAndPopulated(rsForecastList) _
'    Then
'        'Set return value
'        gList_FcstIDs = True
'
'        Set r_ForecastList = New ADODB.Recordset
'        r_ForecastList.Fields.Append "FcstID", adVarWChar, 12
'        r_ForecastList.Fields.Append "FcstDesc", adVarWChar, 40
'        'Populate
'        r_ForecastList.Open
'        rsForecastList.MoveFirst
'        Do Until rsForecastList.eof
'            'Set display to match recordset(s)
'            With r_ForecastList
'                .AddNew
'                For ColCounter = 0 To rsForecastList.Fields.Count - 1
'                    .Fields(ColCounter) = rsForecastList.Fields(ColCounter)
'                Next
'            End With
'            rsForecastList.MoveNext
'        Loop
'        r_ForecastList.MoveFirst
'    Else
'        'Set return value
'        gList_FcstIDs = False
'    End If
'
'CleanUp:
'    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
'    Set rsForecastList = Nothing
'    If Not (AIM_DmdPlanFcstID_Load_Sp Is Nothing) Then Set AIM_DmdPlanFcstID_Load_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstID_Load_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
'    Set rsForecastList = Nothing
'    If Not (AIM_DmdPlanFcstID_Load_Sp Is Nothing) Then Set AIM_DmdPlanFcstID_Load_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstID_Load_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(gList_FcstIDs)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Gets data from the AIMDmdPlanFcstSetup table for the modular given ID.
'' .......................................................................
''
''   Parameters:
''       p_FcstID -- the forecast record to retrieve
''       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
''       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
''       r_rsCompound -- If p_Related was true, then there will be more than one recordset, else
''           there will be just the recordset for AIMDmdPlanFcstSetup.
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Public Function gFetch_ForecastSetup( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, _
'    p_UserID As String, _
'    p_Action As SQL_ACTIONS, _
'    p_Related As Boolean, _
'    r_rsCompound As ADODB.Recordset _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstSetup_Fetch_Sp As ADODB.Command
'    Dim intCount As Long
'
'    Dim DBugCounter As Long
'    Dim DBugText As String
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstSetup_Fetch_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstSetup_Fetch_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstSetup_Fetch_Sp"
'
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanFcstSetup_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstSetup_Fetch_Sp.CreateParameter("@FcstID", adVarWChar, adParamInput, 12, p_FcstId)
'        .Parameters.Append AIM_DmdPlanFcstSetup_Fetch_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12, p_UserID)
'        .Parameters.Append AIM_DmdPlanFcstSetup_Fetch_Sp.CreateParameter("@Action", adTinyInt, adParamInput, , p_Action)
'        .Parameters.Append AIM_DmdPlanFcstSetup_Fetch_Sp.CreateParameter("@Related", adBoolean, adParamInput, , p_Related)
'        .Parameters.Append AIM_DmdPlanFcstSetup_Fetch_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 20, gLangID)
'    End With
'
'    'Init. the recordset
'    If f_IsRecordsetValidAndOpen(r_rsCompound) Then r_rsCompound.Close
'    Set r_rsCompound = Nothing
'    Set r_rsCompound = New ADODB.Recordset
'    With r_rsCompound
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
'    r_rsCompound.Open AIM_DmdPlanFcstSetup_Fetch_Sp
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_DmdPlanFcstSetup_Fetch_Sp.Parameters(0).Value
'
'    'Set return value
'    gFetch_ForecastSetup = RtnCode
'
'    If Not (AIM_DmdPlanFcstSetup_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstSetup_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstSetup_Fetch_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlanFcstSetup_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstSetup_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstSetup_Fetch_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_ForecastSetup)"
'End Function
'
'
'Public Function gFetch_ForecastAccess( _
'    p_Cn As ADODB.Connection, _
'    p_FcstSetupKey As Long, _
'    r_rsAccess As ADODB.Recordset, _
'    Optional p_UserID As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim AIM_DmdPlanFcstAccess_Fetch_Sp As ADODB.Command
'    Dim rsAccessControl As ADODB.Recordset
'    Dim RtnCode As Long
'    Dim ColCounter As Long
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    If IsNull(p_FcstSetupKey) Or p_FcstSetupKey = -1 Then Exit Function
'
'    'Clear out any existing data
'    If f_IsRecordsetValidAndOpen(r_rsAccess) Then r_rsAccess.Close
'    Set r_rsAccess = Nothing
'
'    'Create command object
'    Set AIM_DmdPlanFcstAccess_Fetch_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstAccess_Fetch_Sp
'        .CommandType = adCmdStoredProc
'        Set .ActiveConnection = p_Cn
'        .CommandText = "AIM_DmdPlanFcstAccess_Fetch_Sp"
'        'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanFcstAccess_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'        .Parameters.Append AIM_DmdPlanFcstAccess_Fetch_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstAccess_Fetch_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'
'        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
'        .Parameters("@UserID").Value = p_UserID
'    End With
'
'    Set rsAccessControl = New ADODB.Recordset
'    With rsAccessControl
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    rsAccessControl.Open AIM_DmdPlanFcstAccess_Fetch_Sp
'
'    RtnCode = AIM_DmdPlanFcstAccess_Fetch_Sp.Parameters(0).Value
'    Select Case RtnCode
'    Case Is > 0 'Success
'        If Not f_IsRecordsetOpenAndPopulated(rsAccessControl) Then
'            'Fail
'            gFetch_ForecastAccess = -1
'            GoTo CleanUp
'        Else
'            'Associate as a disconnected recordset
'            Set r_rsAccess = New ADODB.Recordset
'            r_rsAccess.Fields.Append "FcstSetupKey", adInteger
'            r_rsAccess.Fields.Append "UserID", adVarWChar, 12
'            r_rsAccess.Fields.Append "UserName", adVarWChar, 30
'            r_rsAccess.Fields.Append "AccessCode", adTinyInt
'            'Populate
'            r_rsAccess.Open
'            rsAccessControl.MoveFirst
'            Do Until rsAccessControl.eof
'                'Set display to match recordset(s)
'                With r_rsAccess
'                    .AddNew
'                    For ColCounter = 0 To rsAccessControl.Fields.Count - 1
'                        .Fields(ColCounter) = rsAccessControl.Fields(ColCounter)
'                    Next
'                End With
'                rsAccessControl.MoveNext 'There should be no more for the first recordset
'            Loop
'            r_rsAccess.MoveFirst
'        End If
'        If rsAccessControl.RecordCount > 0 Then gFetch_ForecastAccess = 1   'Success
'    End Select
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstAccess_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstAccess_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstAccess_Fetch_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
'    Set rsAccessControl = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    If Not (AIM_DmdPlanFcstAccess_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstAccess_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstAccess_Fetch_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
'    Set rsAccessControl = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(gFetch_ForecastAccess)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Retrieves miscellaneous values from the AIMCalendar
'' .......................................................................
''
''   Derived from AIMAdvCalendar.cls
''   This procedure is meant to retrieve misc. values from the AIMCalendar,
''       depending upon the following parameters (and related dependencies):
''   p_Action - determines the type of data to be returned. Valid values and meanings:
''   Depends on parameter(s): p_TargetDate (Forecast Start Date, typically)
''       p_Action = 1 => Period
''       p_Action = 2 => StartDate[FromDate]
''       p_Action = 3 => EndDate[FromDate]
''       p_Action = 4 => WorkingDaysInWeek[Period]
''    Depends on parameter(s): p_LowerDate; p_UpperDate (Start and End dates for a period)
''       p_Action = 11 => WorkingDays
''    Depends on parameter(s): p_TargetPeriod; p_TargetFiscalYear (A given period within a given fiscal year)
''       p_Action = 21 => StartDateFromPeriod
''       p_Action = 22 => EndDateFromPeriod (This is used to calculate Base Date)
''   Returns: p_RETURNDate and/or p_RETURNNUMBER
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Public Function gCalc_AIMCalendar( _
'    p_Cn As ADODB.Connection, _
'    r_RETURNDATE As Date, _
'    r_RETURNNUMBER As Long, _
'    p_Action As CAL_ACTIONS, _
'    p_FcstInterval As Long, _
'    Optional p_TargetDate As Date, _
'    Optional p_LowerDate As Date, _
'    Optional p_UpperDate As Date, _
'    Optional p_TargetPeriod As Long, _
'    Optional p_TargetFiscalYear As Long _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_AIMCalendar_Calcs_SP As ADODB.Command
'
'    'Init. the command object
'    Set AIM_AIMCalendar_Calcs_SP = New ADODB.Command
'    With AIM_AIMCalendar_Calcs_SP
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMCalendar_Calcs_SP"
'
'    'Set parameters and their values
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@Action", adTinyInt, adParamInput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@FcstInterval", adTinyInt, adParamInput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@TargetDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@LowerDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@UpperDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@TargetPeriod", adInteger, adParamInput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@TargetFiscalYear", adInteger, adParamInput)
'    'Output
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@RETURNDATE", adDBDate, adParamOutput)
'        .Parameters.Append AIM_AIMCalendar_Calcs_SP.CreateParameter("@RETURNNUMBER", adInteger, adParamOutput)
'    'Set values
'        .Parameters("@Action").Value = p_Action
'        .Parameters("@FcstInterval").Value = p_FcstInterval
'        .Parameters("@TargetDate").Value = IIf(Trim$(p_TargetDate) = "12:00:00 AM", Null, Trim$(p_TargetDate))
'        .Parameters("@LowerDate").Value = IIf(Trim$(p_LowerDate) = "12:00:00 AM", Null, Trim$(p_LowerDate))
'        .Parameters("@UpperDate").Value = IIf(Trim$(p_UpperDate) = "12:00:00 AM", Null, Trim$(p_UpperDate))
'        .Parameters("@TargetPeriod").Value = p_TargetPeriod
'        .Parameters("@TargetFiscalYear").Value = p_TargetFiscalYear
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_AIMCalendar_Calcs_SP.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        'Replace nulls
'        r_RETURNDATE = IIf(IsNull(AIM_AIMCalendar_Calcs_SP.Parameters("@RETURNDATE").Value), 0, Format(AIM_AIMCalendar_Calcs_SP.Parameters("@RETURNDATE").Value, gDateFormat))
'        r_RETURNNUMBER = IIf(IsNull(AIM_AIMCalendar_Calcs_SP.Parameters("@RETURNNUMBER").Value), 0, AIM_AIMCalendar_Calcs_SP.Parameters("@RETURNNUMBER").Value)
'
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    'Set return value
'    gCalc_AIMCalendar = RtnCode
'
'CleanUp:
'    If Not (AIM_AIMCalendar_Calcs_SP Is Nothing) Then Set AIM_AIMCalendar_Calcs_SP.ActiveConnection = Nothing
'    Set AIM_AIMCalendar_Calcs_SP = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_AIMCalendar_Calcs_SP Is Nothing) Then Set AIM_AIMCalendar_Calcs_SP.ActiveConnection = Nothing
'    Set AIM_AIMCalendar_Calcs_SP = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(gCalc_AIMCalendar)"
'End Function
'
'Public Function gFetch_FilteredItems( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, r_mItemRecord As ADODB.Recordset, _
'    Optional p_VnId As String, Optional p_Assort As String, _
'    Optional p_LcID As String, Optional p_Item As String, _
'    Optional p_ItStat As String, Optional p_Class1 As String, _
'    Optional p_Class2 As String, Optional p_Class3 As String, _
'    Optional p_Class4 As String, Optional p_ById As String, _
'    Optional p_LStatus As String, Optional p_LDivision As String, _
'    Optional p_LRegion As String, Optional p_LUserDefined As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_ItemList_Sp As ADODB.Command
'
'    'Init. the command object
'    Set AIM_DmdPlan_ItemList_Sp = New ADODB.Command
'    With AIM_DmdPlan_ItemList_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_ItemList_Sp"
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@FcstID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@VnId", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@Assort", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@ItStat", adVarWChar, adParamInput, 1)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@Class1", adVarWChar, adParamInput, 50)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@Class2", adVarWChar, adParamInput, 50)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@Class3", adVarWChar, adParamInput, 50)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@Class4", adVarWChar, adParamInput, 50)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@ByID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@LStatus", adVarWChar, adParamInput, 1)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@LDivision", adVarWChar, adParamInput, 20)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@LRegion", adVarWChar, adParamInput, 20)
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@LUserDefined", adVarWChar, adParamInput, 30)
'    'Output
'        .Parameters.Append AIM_DmdPlan_ItemList_Sp.CreateParameter("@RowCounter", adInteger, adParamOutput)
'    'Set values
'        .Parameters("@FcstID").Value = IIf(Trim$(p_FcstId) = "", Null, Trim$(p_FcstId))
'        .Parameters("@VnID").Value = IIf(Trim$(p_VnId) = "", Null, Trim$(p_VnId))
'        .Parameters("@Assort").Value = IIf(Trim$(p_Assort) = "", Null, Trim$(p_Assort))
'        .Parameters("@Lcid").Value = IIf(Trim$(p_LcID) = "", Null, Trim$(p_LcID))
'        .Parameters("@Item").Value = IIf(Trim$(p_Item) = "", Null, Trim$(p_Item))
'        .Parameters("@ItStat").Value = IIf(Trim$(p_ItStat) = "", Null, Trim$(p_ItStat))
'        .Parameters("@Class1").Value = IIf(Trim$(p_Class1) = "", Null, Trim$(p_Class1))
'        .Parameters("@Class2").Value = IIf(Trim$(p_Class2) = "", Null, Trim$(p_Class2))
'        .Parameters("@Class3").Value = IIf(Trim$(p_Class3) = "", Null, Trim$(p_Class3))
'        .Parameters("@Class4").Value = IIf(Trim$(p_Class4) = "", Null, Trim$(p_Class4))
'        .Parameters("@ById").Value = IIf(Trim$(p_ById) = "", Null, Trim$(p_ById))
'        .Parameters("@LStatus").Value = IIf(Trim$(p_LStatus) = "", Null, Trim$(p_LStatus))
'        .Parameters("@LDivision").Value = IIf(Trim$(p_LDivision) = "", Null, Trim$(p_LDivision))
'        .Parameters("@LRegion").Value = IIf(Trim$(p_LRegion) = "", Null, Trim$(p_LRegion))
'        .Parameters("@LUserDefined").Value = IIf(Trim$(p_LUserDefined) = "", Null, Trim$(p_LUserDefined))
'    End With
'
'    'Init. the recordset
'    If f_IsRecordsetValidAndOpen(r_mItemRecord) Then r_mItemRecord.Close
'    Set r_mItemRecord = Nothing
'    Set r_mItemRecord = New ADODB.Recordset
'    With r_mItemRecord
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in the list of items affected by the forecast filter(s)
'    r_mItemRecord.Open AIM_DmdPlan_ItemList_Sp
'
'    RtnCode = AIM_DmdPlan_ItemList_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        gFetch_FilteredItems = AIM_DmdPlan_ItemList_Sp.Parameters("@RowCounter").Value
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'CleanUp:
'    If Not (AIM_DmdPlan_ItemList_Sp Is Nothing) Then Set AIM_DmdPlan_ItemList_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_ItemList_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_ItemList_Sp Is Nothing) Then Set AIM_DmdPlan_ItemList_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_ItemList_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_FilteredItems)"
'End Function
'
'Private Function mFetch_Pricing( _
'    p_Cn As ADODB.Connection, r_FiscalYear As Long, _
'    r_FY_Cost As Double, r_FY_Price As Double, r_FY_ListPrice As Double, _
'    r_PFY_Cost As Double, r_PFY_Price As Double, r_PFY_ListPrice As Double, _
'    r_NFY_Cost As Double, r_NFY_Price As Double, r_NFY_ListPrice As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_ItemPricing_Sp As ADODB.Command
'
'    'Init. the command object
'    Set AIM_DmdPlan_ItemPricing_Sp = New ADODB.Command
'    With AIM_DmdPlan_ItemPricing_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_ItemPricing_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12, mItemRecord!LcId)
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25, mItemRecord!Item)
'    'Output
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@FiscalYear", adInteger, adParamOutput)
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@FY_Cost", adDecimal, adParamOutput)
'        .Parameters("@FY_Cost").Precision = 10
'        .Parameters("@FY_Cost").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@FY_Price", adDecimal, adParamOutput)
'        .Parameters("@FY_Price").Precision = 10
'        .Parameters("@FY_Price").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@FY_ListPrice", adDecimal, adParamOutput)
'        .Parameters("@FY_ListPrice").Precision = 10
'        .Parameters("@FY_ListPrice").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@PFY_Cost", adDecimal, adParamOutput)
'        .Parameters("@PFY_Cost").Precision = 10
'        .Parameters("@PFY_Cost").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@PFY_Price", adDecimal, adParamOutput)
'        .Parameters("@PFY_Price").Precision = 10
'        .Parameters("@PFY_Price").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@PFY_ListPrice", adDecimal, adParamOutput)
'        .Parameters("@PFY_ListPrice").Precision = 10
'        .Parameters("@PFY_ListPrice").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@NFY_Cost", adDecimal, adParamOutput)
'        .Parameters("@NFY_Cost").Precision = 10
'        .Parameters("@NFY_Cost").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@NFY_Price", adDecimal, adParamOutput)
'        .Parameters("@NFY_Price").Precision = 10
'        .Parameters("@NFY_Price").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_ItemPricing_Sp.CreateParameter("@NFY_ListPrice", adDecimal, adParamOutput)
'        .Parameters("@NFY_ListPrice").Precision = 10
'        .Parameters("@NFY_ListPrice").NumericScale = 4
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlan_ItemPricing_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        r_FiscalYear = AIM_DmdPlan_ItemPricing_Sp.Parameters("@FiscalYear").Value
'        r_FY_Cost = AIM_DmdPlan_ItemPricing_Sp.Parameters("@FY_Cost").Value
'        r_FY_Price = AIM_DmdPlan_ItemPricing_Sp.Parameters("@FY_Price").Value
'        r_FY_ListPrice = AIM_DmdPlan_ItemPricing_Sp.Parameters("@FY_ListPrice").Value
'        r_PFY_Cost = AIM_DmdPlan_ItemPricing_Sp.Parameters("@PFY_Cost").Value
'        r_PFY_Price = AIM_DmdPlan_ItemPricing_Sp.Parameters("@PFY_Price").Value
'        r_PFY_ListPrice = AIM_DmdPlan_ItemPricing_Sp.Parameters("@PFY_ListPrice").Value
'        r_NFY_Cost = AIM_DmdPlan_ItemPricing_Sp.Parameters("@NFY_Cost").Value
'        r_NFY_Price = AIM_DmdPlan_ItemPricing_Sp.Parameters("@NFY_Price").Value
'        r_NFY_ListPrice = AIM_DmdPlan_ItemPricing_Sp.Parameters("@NFY_ListPrice").Value
'
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    mFetch_Pricing = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlan_ItemPricing_Sp Is Nothing) Then Set AIM_DmdPlan_ItemPricing_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_ItemPricing_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_ItemPricing_Sp Is Nothing) Then Set AIM_DmdPlan_ItemPricing_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_ItemPricing_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(mFetch_Pricing)"
'End Function
'Public Function gFetch_BatchData( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, p_FcstBegDate As Date, r_mItemRecord As ADODB.Recordset, _
'    Optional p_VnId As String, Optional p_Assort As String, _
'    Optional p_LcID As String, Optional p_Item As String, _
'    Optional p_ItStat As String, Optional p_Class1 As String, _
'    Optional p_Class2 As String, Optional p_Class3 As String, _
'    Optional p_Class4 As String, Optional p_ById As String, _
'    Optional p_LStatus As String, Optional p_LDivision As String, _
'    Optional p_LRegion As String, Optional p_LUserDefined As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim DPAIM_DmdPlan_BatchFcst_Sp As ADODB.Command
'
'    'Init. the command object
'    Set DPAIM_DmdPlan_BatchFcst_Sp = New ADODB.Command
'    With DPAIM_DmdPlan_BatchFcst_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "DPAIM_DmdPlan_BatchFcst_Sp"
'    'Declare parameters
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@FcstID", adVarWChar, adParamInput, 12)
'
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@FcstBegDate", adDate, adParamInput)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@VnId", adVarWChar, adParamInput, 12)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@Assort", adVarWChar, adParamInput, 12)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@ItStat", adVarWChar, adParamInput, 1)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@Class1", adVarWChar, adParamInput, 50)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@Class2", adVarWChar, adParamInput, 50)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@Class3", adVarWChar, adParamInput, 50)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@Class4", adVarWChar, adParamInput, 50)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@ByID", adVarWChar, adParamInput, 12)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@LStatus", adVarWChar, adParamInput, 1)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@LDivision", adVarWChar, adParamInput, 20)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@LRegion", adVarWChar, adParamInput, 20)
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@LUserDefined", adVarWChar, adParamInput, 30)
'    'Output
'        .Parameters.Append DPAIM_DmdPlan_BatchFcst_Sp.CreateParameter("@RowCounter", adInteger, adParamOutput)
'    'Set values
'        .Parameters("@FcstID").Value = IIf(Trim$(p_FcstId) = "", Null, Trim$(p_FcstId))
'        .Parameters("@FcstBegDate").Value = IIf(Trim$(p_FcstBegDate) = "", Null, Trim$(p_FcstBegDate))
'        .Parameters("@VnID").Value = IIf(Trim$(p_VnId) = "", Null, Trim$(p_VnId))
'        .Parameters("@Assort").Value = IIf(Trim$(p_Assort) = "", Null, Trim$(p_Assort))
'        .Parameters("@Lcid").Value = IIf(Trim$(p_LcID) = "", Null, Trim$(p_LcID))
'        .Parameters("@Item").Value = IIf(Trim$(p_Item) = "", Null, Trim$(p_Item))
'        .Parameters("@ItStat").Value = IIf(Trim$(p_ItStat) = "", Null, Trim$(p_ItStat))
'        .Parameters("@Class1").Value = IIf(Trim$(p_Class1) = "", Null, Trim$(p_Class1))
'        .Parameters("@Class2").Value = IIf(Trim$(p_Class2) = "", Null, Trim$(p_Class2))
'        .Parameters("@Class3").Value = IIf(Trim$(p_Class3) = "", Null, Trim$(p_Class3))
'        .Parameters("@Class4").Value = IIf(Trim$(p_Class4) = "", Null, Trim$(p_Class4))
'        .Parameters("@ById").Value = IIf(Trim$(p_ById) = "", Null, Trim$(p_ById))
'        .Parameters("@LStatus").Value = IIf(Trim$(p_LStatus) = "", Null, Trim$(p_LStatus))
'        .Parameters("@LDivision").Value = IIf(Trim$(p_LDivision) = "", Null, Trim$(p_LDivision))
'        .Parameters("@LRegion").Value = IIf(Trim$(p_LRegion) = "", Null, Trim$(p_LRegion))
'        .Parameters("@LUserDefined").Value = IIf(Trim$(p_LUserDefined) = "", Null, Trim$(p_LUserDefined))
'    End With
'
'    'Init. the recordset
'    If f_IsRecordsetValidAndOpen(r_mItemRecord) Then r_mItemRecord.Close
'    Set r_mItemRecord = Nothing
'    Set r_mItemRecord = New ADODB.Recordset
'    With r_mItemRecord
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in the list of items affected by the forecast filter(s)
'    r_mItemRecord.Open DPAIM_DmdPlan_BatchFcst_Sp
'
'    RtnCode = DPAIM_DmdPlan_BatchFcst_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        gFetch_BatchData = DPAIM_DmdPlan_BatchFcst_Sp.Parameters("@RowCounter").Value
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'CleanUp:
'    If Not (DPAIM_DmdPlan_BatchFcst_Sp Is Nothing) Then Set DPAIM_DmdPlan_BatchFcst_Sp.ActiveConnection = Nothing
'    Set DPAIM_DmdPlan_BatchFcst_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (DPAIM_DmdPlan_BatchFcst_Sp Is Nothing) Then Set DPAIM_DmdPlan_BatchFcst_Sp.ActiveConnection = Nothing
'    Set DPAIM_DmdPlan_BatchFcst_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_BatchData)"
'End Function
'
'
'Public Function gDate_NextPeriodStart( _
'    p_FYStartDate As Date, _
'    p_PeriodStart As Date, _
'    p_FcstInterval As Long _
') As Date
'' Replace this with gDate_DateBoundaries
'On Error GoTo ErrorHandler
'
'    Dim NbrWks As Long
'
'    If p_FcstInterval > int_Quarters Then
'        'Calculate number of weeks between base-date and given period-start (used for the last four types of interval)
'        NbrWks = DateDiff("ww", p_FYStartDate, p_PeriodStart)
'        Do Until NbrWks < 52
'            NbrWks = NbrWks - 52
'        Loop
'    End If
'
'    Select Case p_FcstInterval
'    Case int_Days
'        gDate_NextPeriodStart = p_PeriodStart + 1
'
'    Case int_Weeks
'        gDate_NextPeriodStart = DateAdd("ww", 1#, p_PeriodStart)
'
'    Case int_Months
'        gDate_NextPeriodStart = DateAdd("m", 1#, p_PeriodStart)
'
'    Case int_Quarters
'        gDate_NextPeriodStart = DateAdd("q", 1#, p_PeriodStart)
'
'    Case int_544
'        Select Case NbrWks
'        Case 0, 13, 26, 39
'            gDate_NextPeriodStart = DateAdd("ww", 5#, p_PeriodStart)
'        Case Else
'            gDate_NextPeriodStart = DateAdd("ww", 4#, p_PeriodStart)
'        End Select
'
'    Case int_454
'        Select Case NbrWks
'        Case 4, 17, 30, 43
'            gDate_NextPeriodStart = DateAdd("ww", 5#, p_PeriodStart)
'        Case Else
'            gDate_NextPeriodStart = DateAdd("ww", 4#, p_PeriodStart)
'        End Select
'
'    Case int_445
'        Select Case NbrWks
'        Case 8, 21, 34, 47
'            gDate_NextPeriodStart = DateAdd("ww", 5#, p_PeriodStart)
'        Case Else
'            gDate_NextPeriodStart = DateAdd("ww", 4#, p_PeriodStart)
'        End Select
'
'    Case int_4Wks
'        gDate_NextPeriodStart = DateAdd("ww", 4#, p_PeriodStart)
'
'    Case Else
'        gDate_NextPeriodStart = p_PeriodStart + 1
'
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(gDate_NextPeriodStart)"
'End Function
'
'Public Function gDate_Boundaries( _
'    p_Cn As ADODB.Connection, _
'    p_TargetDate As Date, _
'    p_FcstInterval As Long, _
'    r_PeriodStartDate As Date, _
'    r_PeriodEndDate As Date, _
'    r_PeriodIndex As Long _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim AIM_AIMCalendar_BoundaryDates_Sp As ADODB.Command
'    Dim RtnCode As Long
'
'    Set AIM_AIMCalendar_BoundaryDates_Sp = New ADODB.Command
'    With AIM_AIMCalendar_BoundaryDates_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMCalendar_BoundaryDates_Sp"
'    'Declare parameters
'        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append .CreateParameter("@TargetDate", adDBDate, adParamInput)
'        .Parameters.Append .CreateParameter("@PeriodInterval", adTinyInt, adParamInput)
'    'Output
'        .Parameters.Append .CreateParameter("@PeriodStartDate", adDBDate, adParamOutput)
'        .Parameters.Append .CreateParameter("@PeriodEndDate", adDBDate, adParamOutput)
'        .Parameters.Append .CreateParameter("@PeriodIndex", adInteger, adParamOutput)
'    'Set values
'        .Parameters("@TargetDate").Value = p_TargetDate
'        .Parameters("@PeriodInterval").Value = p_FcstInterval
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        r_PeriodStartDate = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(3).Value
'        r_PeriodEndDate = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(4).Value
'        r_PeriodIndex = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(5).Value
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    gDate_Boundaries = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(gDate_Boundaries)"
'End Function
'
'
'Public Function gGen_ForecastData( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, _
'    p_UseForecastCriteria As Boolean, _
'    ForecastCriteria As FORECAST_CRITERIA, _
'    ForecastData As FORECAST_DATA _
') As Long
'
'On Error GoTo ErrorHandler
'
'    Dim StatusPercent As Long, LoopCounter As Long
'
'    Dim RtnCode As Long
'    Dim ColIdx As Long, RowIdx As Long, RowCount As Long
'    Dim TotalFcstPeriods As Long
'    Dim IndexCounter As Long
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'    Dim tempInt As Long, tempString As String
'    Dim rsTemp As ADODB.Recordset
'
'    Dim AIM_DmdPlan_FcstStoreKey_Fetch_Sp As ADODB.Command
'    Dim rsFcstStore As ADODB.Recordset
'    Dim FcstStoreKey As Long
'
'    Dim FcstDemand As Double
'    'Generate Orders Variables
'    Dim BkQty(1 To 10) As Long            'Price Break Quantities
'    Dim BkCost(1 To 10) As Double         'Price Breaks
'
'    Dim TargetPeriod As Long
'    Dim TargetFiscalYear As Long
'    Dim TargetFYStartDate As Date
'    Dim TargetFYEndDate As Date
'    Dim PMAdjustment As Double
'    Dim LastUpdated As Date
'
'    Dim BaseDate As Date
'
'    Dim PeriodStart As Date
'    Dim PeriodEnd As Date
'
'    Dim TargetCost As Double
'    Dim TargetPrice As Double
'    Dim TargetListPrice As Double
'
'    Dim Int_Enabled As String                'Intermittent Processing Enabled (Y/N)
'    Dim Int_LookBackPds As Long           'Intermittent Demand Look Back Periods
'    Dim KFactor As Double                    'Inventory Carrying Cost
'    Dim CarryForwardRounding As Boolean
'    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
'    Dim rsSysCtrl As ADODB.Recordset
'
'    'AIMOptions
'    Dim OP_HiTrndL As Double
'    Dim OP_Dft_TurnHigh As Double
'    Dim OP_Dft_TurnLow As Double
'    Dim OP_HiMadP As Double
'    Dim OP_LoMadP  As Double
'    Dim OP_MadExK As Double
'    Dim OP_DIMADP As Double
'    'AIMLocations
'    Dim LC_DmdScalingFactor As Double
'    Dim LC_ScalingEffUntil As Date
'    Dim LC_ReplenCost As Double
'    Dim LC_DemandSource As String
'    Dim LC_StkDate As Date
'
'    Dim FiscalYear As Long
'    Dim FY_Cost As Double
'    Dim FY_Price As Double
'    Dim FY_ListPrice As Double
'    Dim PFY_Cost As Double
'    Dim PFY_Price As Double
'    Dim PFY_ListPrice As Double
'    Dim NFY_Cost As Double
'    Dim NFY_Price As Double
'    Dim NFY_ListPrice As Double
'
'    Dim ReplenCostAll As Double
'
'    Dim AIM_DmdPlanFcstMaster_Fetch_Sp As ADODB.Command
'
'    'Fetch ForecastSetup Info.
'    RtnCode = gFetch_ForecastSetup(p_Cn, p_FcstId, gUserID, SQL_GetEq, False, rsTemp)
'    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
'        ForecastCriteria.FcstId = rsTemp!FcstId
'        ForecastCriteria.FcstSetupKey = rsTemp!FcstSetupKey
'        If p_UseForecastCriteria = False Then
'            'Fetch Itemdata using forecastsetup record
'            ForecastCriteria.FcstStartDate = rsTemp!FcstStartDate
'            ForecastCriteria.FcstRolling = rsTemp!FcstRolling
'            ForecastCriteria.FcstPeriods_Future = rsTemp!FcstPds_Future
'            ForecastCriteria.FcstHistory = rsTemp!FcstHistory
'            ForecastCriteria.FcstPeriods_Historical = rsTemp!FcstPds_Historical
'            ForecastCriteria.ApplyTrend = rsTemp!ApplyTrend
'            ForecastCriteria.FcstInterval = rsTemp!FcstInterval
'            ForecastCriteria.FcstUnit = rsTemp!FcstUnit
'            ForecastCriteria.Calc_SysFcst = rsTemp!Calc_SysFcst
'            ForecastCriteria.Calc_Netreq = rsTemp!Calc_NetRqmt
'            ForecastCriteria.Calc_HistDmd = rsTemp!Calc_PrjInvt
'            ForecastCriteria.Calc_MasterFcstAdj = rsTemp!Calc_HstDmnd
'            ForecastCriteria.Calc_FcstAdj = rsTemp!Calc_NetRqmtAndPlnRcpt
'            ForecastCriteria.Calc_AdjNetReq = rsTemp!Calc_NetRqmtAndPrdCons
'            ForecastCriteria.Calc_ProjInv = rsTemp!Calc_PrjInvtAndPrdCons
'            ForecastCriteria.Item = rsTemp!Item
'            ForecastCriteria.ItStat = rsTemp!ItStat
'            ForecastCriteria.VnId = rsTemp!VnId
'            ForecastCriteria.Assort = rsTemp!Assort
'            ForecastCriteria.ById = rsTemp!ById
'            ForecastCriteria.Class1 = rsTemp!Class1
'            ForecastCriteria.Class2 = rsTemp!Class2
'            ForecastCriteria.Class3 = rsTemp!Class3
'            ForecastCriteria.Class4 = rsTemp!Class4
'            ForecastCriteria.LcId = rsTemp!LcId
'            ForecastCriteria.LStatus = rsTemp!LStatus
'            ForecastCriteria.LDivision = rsTemp!LDivision
'            ForecastCriteria.LRegion = rsTemp!LRegion
'            ForecastCriteria.LUserDefined = rsTemp!LUserDefined
'        'Else, fetch using the variables from the UI
'        End If  'p_UseForecastCriteria
'    End If
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'
'    'Fetch Itemdata using criteria
'    RtnCode = gFetch_FilteredItems(p_Cn, p_FcstId, mItemRecord, ForecastCriteria.VnId, _
'            ForecastCriteria.Assort, ForecastCriteria.LcId, _
'            ForecastCriteria.Item, ForecastCriteria.ItStat, _
'            ForecastCriteria.Class1, ForecastCriteria.Class2, _
'            ForecastCriteria.Class3, ForecastCriteria.Class4, _
'            ForecastCriteria.ById, ForecastCriteria.LStatus, _
'            ForecastCriteria.LDivision, ForecastCriteria.LRegion, _
'            ForecastCriteria.LUserDefined)
'    If RtnCode <= 0 Then
'        gGen_ForecastData = RtnCode
'        Exit Function
'    End If
'
''    Set AIM_JobLog_Insert_Sp = New ADODB.Command
''    With AIM_JobLog_Insert_Sp
''        Set .ActiveConnection = p_Cn
''        .CommandTimeout = 0
''        .CommandText = "AIM_JobLog_Insert_Sp"
''        .CommandType = adCmdStoredProc
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@LogJobType", adVarWChar, adParamInput, 50)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@LogSource", adVarWChar, adParamInput, 255)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@LogSeverity", adTinyInt, adParamInput)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@LogMessage", adVarWChar, adParamInput, 1000)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@LogOutputFile", adVarWChar, adParamInput, 255)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@LogMsgVariables", adVarWChar, adParamInput, 1000)
''        .Parameters.Append AIM_JobLog_Insert_Sp.CreateParameter("@JobLogID", adInteger, adParamInput)
''    End With
'
'    'Fetch SysCtrl variables
'    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
'    With AIM_SysCtrl_Get_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_SysCtrl_Get_Sp"
'        .Parameters.Append AIM_SysCtrl_Get_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    End With
'    'Initialize the System Control Recordset
'    Set rsSysCtrl = New ADODB.Recordset
'    With rsSysCtrl
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'        .Open AIM_SysCtrl_Get_Sp
'    End With
'    If AIM_SysCtrl_Get_Sp.Parameters(0).Value > 0 Then
'        Int_Enabled = rsSysCtrl!Int_Enabled
'        Int_LookBackPds = rsSysCtrl!Int_LookBackPds
'        KFactor = rsSysCtrl!KFactor
'        CarryForwardRounding = IIf(UCase$(rsSysCtrl!CarryForwardRounding) = "Y", True, False)
'    End If
'    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
'    Set AIM_SysCtrl_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
'    Set rsSysCtrl = Nothing
'
'    'Fetch existing repository key, if any
'    Set AIM_DmdPlan_FcstStoreKey_Fetch_Sp = New ADODB.Command
'    With AIM_DmdPlan_FcstStoreKey_Fetch_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_FcstStoreKey_Fetch_Sp"
'        .Parameters.Append AIM_DmdPlan_FcstStoreKey_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'        .Parameters.Append AIM_DmdPlan_FcstStoreKey_Fetch_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput, , ForecastCriteria.FcstSetupKey)
'        .Parameters.Append AIM_DmdPlan_FcstStoreKey_Fetch_Sp.CreateParameter("@UserElement", adBoolean, adParamInput, , 0)
'        .Parameters.Append AIM_DmdPlan_FcstStoreKey_Fetch_Sp.CreateParameter("@FetchComment", adBoolean, adParamInput, , 0)
'    End With
'    Set rsFcstStore = New ADODB.Recordset
'    With rsFcstStore
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'    rsFcstStore.Open AIM_DmdPlan_FcstStoreKey_Fetch_Sp
'    If AIM_DmdPlan_FcstStoreKey_Fetch_Sp.Parameters(0).Value > 0 Then
'        If rsFcstStore.RecordCount > 0 Then FcstStoreKey = CLng(rsFcstStore!FcstStoreKey)
'    End If
'    If f_IsRecordsetValidAndOpen(rsFcstStore) Then rsFcstStore.Close
'    Set rsFcstStore = Nothing
'    If Not (AIM_DmdPlan_FcstStoreKey_Fetch_Sp Is Nothing) Then Set AIM_DmdPlan_FcstStoreKey_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_FcstStoreKey_Fetch_Sp = Nothing
'
'    'Set the forecastdata.rowidx = number of items in list
'    ' and forecastdata.colidx = number of periods to calc.
'    RowCount = mItemRecord.RecordCount
'    TotalFcstPeriods = ForecastCriteria.FcstPeriods_Future
'
'    AIMJobLog "gGen_ForecastData", 0, "Number of records in mItemRecord: " & CStr(RowCount) & " and Number of periods: " & CStr(TotalFcstPeriods)
'    With ForecastData
'        ReDim .Item(RowCount) As String
'        ReDim .ItDesc(RowCount) As String
'        ReDim .LcId(RowCount) As String
'        ReDim .BaseDate(RowCount) As Date
'        ReDim .PeriodStartDate(RowCount, TotalFcstPeriods) As Date
'        ReDim .PeriodEndDate(RowCount, TotalFcstPeriods) As Date
'        ReDim .DataCalc_Value(RowCount, TotalFcstPeriods, DATA_CALC_TYPE.DCT_MAXVALUE - 1) As Double
'    End With
'
'    'Start at first indices
'    RowIdx = 1
'    ColIdx = 1
'    AIMJobLog "gGen_ForecastData", 0, "Starting loop - for rowidx = 1 to mItemRecord.RecordCount..."
'    For RowIdx = 1 To mItemRecord.RecordCount
'        With ForecastData
'            .Item(RowIdx) = mItemRecord!Item
'            .ItDesc(RowIdx) = mItemRecord!ItDesc
'            .LcId(RowIdx) = mItemRecord!LcId
''            AIMJobLog "gGen_ForecastData", 0, "Processing Item:" & CStr(.Item(RowIdx)) & " and LcID: " & CStr(.LcId(RowIdx))
'        End With
'
'        'Set arrays for breakqty and price
'        For tempInt = LBound(BkQty) To UBound(BkQty)
'            tempString = "BkQty" & CStr(Format(tempInt, "00"))
'            BkQty(tempInt) = mItemRecord(tempString).Value
'            tempString = "BkCost" & CStr(Format(tempInt, "00"))
'            BkCost(tempInt) = mItemRecord(tempString).Value
'        Next
'
'        'Fetch AIMOptions
'        RtnCode = mFetch_OptionAttribs(p_Cn, _
'            OP_HiTrndL, OP_Dft_TurnHigh, OP_Dft_TurnLow, _
'            OP_HiMadP, OP_LoMadP, OP_MadExK, OP_DIMADP)
'
'        'Fetch AIMLocations
'        RtnCode = mFetch_LocationAttribs(p_Cn, _
'            LC_DmdScalingFactor, LC_ScalingEffUntil, LC_ReplenCost, LC_DemandSource, LC_StkDate)
'
'        If RtnCode >= 1 Then ReplenCostAll = LC_ReplenCost + mItemRecord!ReplenCost2
'
'        'Get Fiscal Year PriceCost for given Item/Location -- this should set PFY_*, FY_* and NFY_*
'        RtnCode = mFetch_Pricing(p_Cn, FiscalYear, FY_Cost, FY_Price, _
'                FY_ListPrice, PFY_Cost, PFY_Price, PFY_ListPrice, _
'                NFY_Cost, NFY_Price, NFY_ListPrice)
'
'        'GetBaseDate
'        TargetFiscalYear = mItemRecord!FcstUpdCyc \ 100
'        TargetPeriod = mItemRecord!FcstUpdCyc - (TargetFiscalYear * 100)
'        RtnCode = gCalc_AIMCalendar(p_Cn, BaseDate, 0, CA_ENDDATEFROMPERIOD, ForecastCriteria.FcstInterval, , , , TargetPeriod, TargetFiscalYear)
'        If IsDate(BaseDate) And Trim$(BaseDate) <> "12:00:00 AM" Then ForecastData.BaseDate(RowIdx) = BaseDate
'
'        'Establish the FY Start and End Dates
'        TargetFYStartDate = ForecastCriteria.FcstStartDate
'        'Start calcs from forecast startdate
'        PeriodStart = TargetFYStartDate
'
''        AIMJobLog "gGen_ForecastData", 0, "Starting loop - calc forecast for each period."
'        'Calculate Forecast for each period
'        For ColIdx = 1 To TotalFcstPeriods
'            'Get the end date for the given period
'            PeriodEnd = gDate_NextPeriodStart(TargetFYStartDate, PeriodStart, ForecastCriteria.FcstInterval)
'            PeriodEnd = DateAdd("d", -1#, PeriodEnd)
'            With ForecastData
'                .PeriodStartDate(RowIdx, ColIdx) = PeriodStart
'                .PeriodEndDate(RowIdx, ColIdx) = PeriodEnd
'            End With
'
'            If ForecastCriteria.Calc_SysFcst Then
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_SysRqmt) = mCalc_SysRqmt(p_Cn, FcstStoreKey, ForecastData.LcId(RowIdx), ForecastData.Item(RowIdx), _
'                    ForecastCriteria.FcstInterval, ForecastCriteria.ApplyTrend, _
'                    PeriodStart, PeriodEnd, BaseDate, _
'                    OP_HiTrndL, LC_DmdScalingFactor, LC_ScalingEffUntil, _
'                    PMAdjustment, FcstDemand, _
'                    ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_AdjSysRqmt))
'            End If
'
'            If ForecastCriteria.Calc_HistDmd Then
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_HstDmnd) = mCalc_HstDmnd(p_Cn, ForecastCriteria.FcstInterval, PeriodStart, PeriodEnd, LC_DemandSource)
'            End If
'
'            If ForecastCriteria.Calc_ProjInv _
'            Or ForecastCriteria.Calc_ProjInv _
'            Then
'                'TO DO CalcInitialStock here
'                'ForecastData.DataCalc_Value(RowIdx, Colidx, DATA_CALC_TYPE.DCT_PrjInvt) = mCalc_PrjInvt
'            End If
'
'            'Determine FY and get price/cost (PFY_, FY_ or NFY_)
'            Select Case Year(PeriodStart)
'            Case Is = TargetFiscalYear - 1
'                'Previous Fiscal Year
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_PriceAdjmt) = PFY_Price
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_CostAdjmt) = PFY_Cost
'            Case Is = TargetFiscalYear
'                'Fiscal Year (current)
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_PriceAdjmt) = FY_Price
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_CostAdjmt) = FY_Cost
'            Case Is = TargetFiscalYear + 1
'                'Next Fiscal Year
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_PriceAdjmt) = NFY_Price
'                ForecastData.DataCalc_Value(RowIdx, ColIdx, DATA_CALC_TYPE.DCT_CostAdjmt) = NFY_Cost
'            End Select
'
'            ''''Update Totals
'            '''m_TotalFcst = m_TotalFcst + m_Fcst(Pd)
'            '''m_TotalPrice = m_TotalPrice + m_Fcst(Pd) * Price
'            '''m_TotalCost = m_TotalCost + m_Fcst(Pd) * Cost
'
'            'Increment Start Date
'            PeriodStart = DateAdd("d", 1#, PeriodEnd)
''            AIMJobLog "gGen_ForecastData", 0, "Incremented startdate -> " & CStr(PeriodStart)
''            AIMJobLog "gGen_ForecastData", 0, "Next - calc forecast for each period..."
'
'            LoopCounter = LoopCounter + 1
'            StatusPercent = 100 * (LoopCounter / (RowCount * TotalFcstPeriods))
'            Write_Message CStr(Format(StatusPercent, "##0.00")) & "% complete... " & _
'                " Processing Item:" & CStr(ForecastData.Item(RowIdx)) & _
'                " and LcID: " & CStr(ForecastData.LcId(RowIdx)) & _
'                " for Period: " & CStr(ColIdx)
'        Next 'ColIdx/Target Period
'        TargetFYEndDate = PeriodEnd
'
'        'NetRequirements
'        If p_UseForecastCriteria = True Then
'            'Calc net reqt only on request
'            If ForecastCriteria.Calc_Netreq _
'            Or ForecastCriteria.Calc_Netreq _
'            Or ForecastCriteria.Calc_Netreq _
'            Then
'                Select Case Year(PeriodStart)
'                Case Is = TargetFiscalYear - 1
'                    'Previous Fiscal Year
'                    TargetPrice = PFY_Price
'                    TargetCost = PFY_Cost
'                Case Is = TargetFiscalYear
'                    'Fiscal Year (current)
'                    TargetPrice = FY_Price
'                    TargetCost = FY_Cost
'                Case Is = TargetFiscalYear + 1
'                    'Next Fiscal Year
'                    TargetPrice = NFY_Price
'                    TargetCost = NFY_Cost
'                End Select
'
'                StatusPercent = 100 * (RowIdx / RowCount)
'                Write_Message CStr(Format(StatusPercent, "##0.00")) & "% complete... " & _
'                    " Processing Net requirements for " & _
'                    " Item:" & CStr(ForecastData.Item(RowIdx)) & _
'                    " and LcID: " & CStr(ForecastData.LcId(RowIdx))
'
'                RtnCode = mCalc_NetRqmt(p_Cn, FcstStoreKey, ForecastData.LcId(RowIdx), ForecastData.Item(RowIdx), RowIdx, TotalFcstPeriods, _
'                    TargetFYStartDate, TargetFYEndDate, TargetFiscalYear, _
'                    BaseDate, OP_HiTrndL, OP_Dft_TurnHigh, OP_Dft_TurnLow, _
'                    OP_HiMadP, OP_LoMadP, OP_MadExK, OP_DIMADP, _
'                    KFactor, Int_Enabled, mItemRecord!IntSafetyStock, LC_StkDate, _
'                    ForecastCriteria.FcstInterval, ForecastCriteria.ApplyTrend, _
'                    PMAdjustment, LC_DmdScalingFactor, LC_ScalingEffUntil, _
'                    BkQty(), BkCost(), ReplenCostAll, TargetCost, TargetPrice, _
'                    ForecastData)
'            End If
''''            'Production Constraints
''''            If ForecastCriteria.Calc_NetRqmtAndPrdCons _
''''            Or ForecastCriteria.Calc_PrjInvtAndPrdCons _
''''            Then
''''                    'TO DO ProdConstraints here
''''                    '... so on and so forth
''''            End If
'        End If
'
''        AIMJobLog "gGen_ForecastData", 0, "Next - for rowidx = 1 to mItemRecord.RecordCount..."
'        'Increment array index
'        mItemRecord.MoveNext
'    Next 'Item
'
'    AIMJobLog "gGen_ForecastData", 0, "Saving data to FcstStore..."
'    'RtnCode = gSave_ForecastStore(p_Cn, False, ForecastCriteria.FcstSetupKey, CStr(ForecastCriteria.FcstSetupKey), ForecastCriteria, ForecastData, "")
'    If RtnCode >= 1 _
'    Then
'        If ForecastCriteria.FcstRolling = True Then
'            AIMJobLog "gGen_ForecastData", 0, "FcstRolling is True, saving data to FcstStore..."
'            'Update the forecastsetup,
'            LastUpdated = ForecastCriteria.FcstStartDate
'            ForecastCriteria.FcstStartDate = DateAdd("d", 1#, TargetFYEndDate)
'            RtnCode = gSave_ForecastSetup(p_Cn, ForecastCriteria, True, LastUpdated)
'        End If
'
'    End If
'    AIMJobLog "gGen_ForecastData", 0, "Exiting gGen_ForecastData"
'
'    gGen_ForecastData = RtnCode
'
'CleanUp:
'    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
'    Set AIM_SysCtrl_Get_Sp = Nothing
'    If Not (AIM_JobLog_Insert_Sp Is Nothing) Then Set AIM_JobLog_Insert_Sp.ActiveConnection = Nothing
'    Set AIM_JobLog_Insert_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
'    Set AIM_SysCtrl_Get_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
'    Set rsSysCtrl = Nothing
'    If Not (AIM_JobLog_Insert_Sp Is Nothing) Then Set AIM_JobLog_Insert_Sp.ActiveConnection = Nothing
'    Set AIM_JobLog_Insert_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(gGen_ForecastData)"
'End Function
'
'Private Function mCalc_SysRqmt( _
'    p_Cn As ADODB.Connection, p_FcstStoreKey As Long, p_LcID As String, p_Item As String, _
'    p_FcstInterval As Long, p_ApplyTrend As Long, _
'    p_PeriodStart As Date, p_PeriodEnd As Date, p_BaseDate As Date, _
'    p_HiTrndL As Double, p_DmdScalingFactor As Double, p_ScalingEffUntil As Date, _
'    r_PMAdjustment As Double, r_FcstDemand As Double, _
'    r_AdjustedCalc As Double _
') As Double
'On Error GoTo ErrorHandler
'
''    Dim RtnCode As Long
''    Dim FcstForPeriod As Double
''    Dim StartFcstDate As Date
''    Dim EndFcstdate As Date
''    Dim ErrNumber As Long
''    Dim ErrSource As String
''    Dim ErrDesc As String
''    Dim tempInt As Long
''    Dim rsForecastAdjust As ADODB.Recordset
''
''    'Check for invalid Item
''    Select Case mItemRecord!ItStat
''    Case "I", "P", "X" 'Inactive, Purge, Discontinued
''        mCalc_SysRqmt = 0
''        Exit Function
''    End Select
''
''    'Check the InmItemRecord!ActDate Regardless of ItStat
''    If p_PeriodEnd > mItemRecord!InActDate Then
''        p_PeriodEnd = mItemRecord!InActDate
'''        AIMJobLog "mCalc_SysRqmt", 0, "In mCalc_SysRqmt, reset p_PeriodEnd with InActDate: " & CStr(p_PeriodEnd)
''    End If
''
''    'Check for invalid start and end dates
''    If p_PeriodEnd < p_PeriodStart Then
''        mCalc_SysRqmt = 0
'''        AIMJobLog "mCalc_SysRqmt", 0, "In mCalc_SysRqmt, p_PeriodEnd < p_PeriodStart! Exiting function."
''        Exit Function
''    End If
''
''    'Check for a User Override on Forecast Demand - No Seasonality or Trend is applied to User Overrides
'''    AIMJobLog "mCalc_SysRqmt", 0, "In mCalc_SysRqmt, checking for user override... UserFcst: " & CStr(mItemRecord!UserFcst) & " and UserFcstExpDate: " & CStr(mItemRecord!UserFcstExpDate)
''    If mItemRecord!UserFcst > 0 _
''    And mItemRecord!UserFcstExpDate > p_PeriodStart _
''    Then
''        If p_PeriodEnd < mItemRecord!UserFcstExpDate Then
''            AIMJobLog "mCalc_SysRqmt", 0, "Case 1 - override found: CalcForecastForPeriod with no seas and no trend"
''            r_FcstDemand = mItemRecord!UserFcst
''            StartFcstDate = p_PeriodStart
''            EndFcstdate = p_PeriodEnd
''            FcstForPeriod = mCalc_ForecastForPeriod(p_Cn, p_FcstInterval, StartFcstDate, EndFcstdate, _
''                p_BaseDate, r_PMAdjustment, r_FcstDemand, TA_NOTREND, False, p_HiTrndL)
''        Else
''            'Calc User Forecast for the period until user-specified expiration date
''            ' and add the regular forecast for period after user-spec. exp. date to the enddate
''            AIMJobLog "mCalc_SysRqmt", 0, "Case 2 - override plus extra: CalcForecastForPeriod with part. seas and part. trend"
''            r_FcstDemand = mItemRecord!UserFcst
''            StartFcstDate = p_PeriodStart
''            EndFcstdate = mItemRecord!UserFcstExpDate
''            FcstForPeriod = mCalc_ForecastForPeriod(p_Cn, p_FcstInterval, StartFcstDate, EndFcstdate, _
''                p_BaseDate, r_PMAdjustment, r_FcstDemand, TA_NOTREND, False, p_HiTrndL)
''
''            r_FcstDemand = mItemRecord!FcstDemand
''            StartFcstDate = (mItemRecord!UserFcstExpDate + 1)
''            EndFcstdate = p_PeriodEnd
''            FcstForPeriod = FcstForPeriod + _
''                        mCalc_ForecastForPeriod(p_Cn, p_FcstInterval, StartFcstDate, EndFcstdate, _
''                            p_BaseDate, r_PMAdjustment, r_FcstDemand, p_ApplyTrend, True, p_HiTrndL)
''        End If
''    Else
''        AIMJobLog "mCalc_SysRqmt", 0, "Case 3 - No user override: CalcForecastForPeriod with seas and trend"
''
''        'Calc forecast with Seasonality and Trend
''        r_FcstDemand = mItemRecord!FcstDemand
''        StartFcstDate = p_PeriodStart
''        EndFcstdate = p_PeriodEnd
''
'''        AIMJobLog "mCalc_SysRqmt", 0, "Calling mCalc_ForecastForPeriod"
''        FcstForPeriod = mCalc_ForecastForPeriod(p_Cn, p_FcstInterval, StartFcstDate, EndFcstdate, _
''                            p_BaseDate, r_PMAdjustment, r_FcstDemand, p_ApplyTrend, True, p_HiTrndL)
'''        AIMJobLog "mCalc_SysRqmt", 0, "After calling mCalc_ForecastForPeriod, FcstForPeriod:" & CStr(FcstForPeriod)
''    End If
''
''    'Adjust for negative forecast
''    FcstForPeriod = IIf(FcstForPeriod >= 0, FcstForPeriod, 0)
''
''    'Adjust Forecast for Location Demand Scaling
''    If Date <= p_ScalingEffUntil Then
'''        AIMJobLog "mCalc_SysRqmt", 0, "In mCalc_SysRqmt(), applying DmdScalingFactor(" & CStr(p_DmdScalingFactor) & ") to fcstforperiod(" & CStr(FcstForPeriod) & ")"
''        FcstForPeriod = FcstForPeriod * p_DmdScalingFactor
''    End If
''
''    'Return Forecast
''    mCalc_SysRqmt = Round(FcstForPeriod, 2)
''
''    RtnCode = gFetch_ForecastAdjustments(p_Cn, p_FcstStoreKey, _
''            p_LcID, p_Item, _
''            p_PeriodStart, p_PeriodEnd, _
''            rsForecastAdjust)
''    'Set default adjusted calc to equal sys requirements
''    r_AdjustedCalc = mCalc_SysRqmt
''    If RtnCode > 0 Then
''        'Increment using adjustments
''        rsForecastAdjust.MoveFirst
''        For tempInt = 0 To rsForecastAdjust.RecordCount - 1
''            If p_PeriodStart >= rsForecastAdjust!AdjustStartDate _
''            And p_PeriodEnd <= rsForecastAdjust!AdjustEndDate _
''            Then
''                'Update any adjustment for the period
''                Select Case rsForecastAdjust!AdjustType
''                Case ADJ_OVERRIDE
''                    r_AdjustedCalc = _
''                        rsForecastAdjust!AdjustQty
''                    Exit For
''                Case ADJ_PERCENT
''                    r_AdjustedCalc = r_AdjustedCalc * (1# + (rsForecastAdjust!AdjustQty \ 100#))
''                Case ADJ_UNITS
''                    r_AdjustedCalc = r_AdjustedCalc + rsForecastAdjust!AdjustQty
''                End Select
''            End If
''            rsForecastAdjust.MoveNext
''        Next
''    End If
''
''    If f_IsRecordsetValidAndOpen(rsForecastAdjust) Then rsForecastAdjust.Close
''    Set rsForecastAdjust = Nothing
'
''    AIMJobLog "mCalc_SysRqmt", 0, "Before exiting, FcstForPeriod:" & CStr(FcstForPeriod)
'
'Exit Function
'ErrorHandler:
''    ErrNumber = Err.Number
''    ErrSource = Err.source
''    ErrDesc = Err.Description
''
''    If f_IsRecordsetValidAndOpen(rsForecastAdjust) Then rsForecastAdjust.Close
''    Set rsForecastAdjust = Nothing
''
''    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mCalc_SysRqmt)"
'End Function
'
'Private Function mCalc_ForecastForPeriod( _
'    p_Cn As ADODB.Connection, p_FcstInterval As Long, _
'    p_PeriodStart As Date, p_PeriodEnd As Date, p_BaseDate As Date, _
'    r_PMAdjustment As Double, p_FcstDemand As Double, _
'    p_ApplyTrend As Long, p_ApplySeasonsIndex As Boolean, _
'    p_HiTrndL As Double _
') As Double
'On Error GoTo ErrorHandler
''    AIMJobLog "mCalc_ForecastForPeriod", 0, "In mCalc_ForecastForPeriod. Parameters: " & _
'        " fcstinterval=" & CStr(p_FcstInterval) & ", periodstart=" & CStr(p_PeriodStart) & ", periodend=" & CStr(p_PeriodEnd) & _
'        " basedate=" & CStr(p_BaseDate) & ", p_ApplySeasonsIndex=" & CStr(p_ApplySeasonsIndex) & ", p_ApplyTrend=" & CStr(p_ApplyTrend) & _
'        " p_HiTrndL=" & CStr(p_HiTrndL)
'
'    Dim RtnCode As Long
'
'    Dim FirstStartDate As Date
'    Dim LastStartDate As Date
'    Dim StartWeekPct As Double
'    Dim EndWeekPct As Double
'    Dim DailyPct As Double
'    Dim tempInt As Long
'    Dim tempDate As Date
'
'    Dim TargetPeriod As Long
'    Dim PreviousPeriod As Long
'    Dim TargetDate As Date
'    Dim FcstForPeriod As Double
'    Dim TrendPeriods As Long
'    Dim FcstTrend As Double
'    Dim Demand As Double
'    'Define item attributes
'    'AIMSeasons
'    Dim SI_BaseIndex(1 To 52) As Double
'    'AIMPromotions
'    Dim PM_PmStatus As Boolean
'    Dim PM_PmStartDate As Date
'    Dim PM_PmEndDate As Date
'    Dim PM_Adjust(1 To 52) As Double
'    'End Item Attributes
'
'
'    RtnCode = mCalc_PartialPercentages(p_Cn, p_FcstInterval, p_PeriodStart, p_PeriodEnd, _
'                    FirstStartDate, LastStartDate, StartWeekPct, EndWeekPct, DailyPct)
'
'    'Forecast from the Start Date through the End Date
'    TargetDate = FirstStartDate
'    FcstForPeriod = 0
'
'    'Get misc. attributes for the item
'    RtnCode = mFetch_SeasonsAttribs(p_Cn, SI_BaseIndex())
'    RtnCode = mFetch_PromotionAttribs(p_Cn, PM_PmStatus, PM_PmStartDate, PM_PmEndDate, PM_Adjust())
'
'    AIMJobLog "mCalc_ForecastForPeriod", 0, "Start Loop - forecast from the Start Date(" & CStr(p_PeriodStart) & ") through the End Date(" & CStr(p_PeriodEnd) & ")..."
'    Do Until TargetDate >= p_PeriodEnd
'        RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, TargetPeriod, CA_PERIOD, p_FcstInterval, TargetDate)
'        TargetPeriod = IIf(TargetPeriod = PreviousPeriod, 0, TargetPeriod)
'        'Check for an invalid period
'        If TargetPeriod <> 0 Then
'            'Apply Trend if appropriate
'            If p_ApplyTrend = TA_ALLITEMS _
'            Or (p_ApplyTrend = TA_ITEMPLANNEDTRENDTYPE And UCase$(mItemRecord!PlnTT) = "Y") _
'            Then
'                TrendPeriods = DateDiff("d", p_BaseDate, TargetDate) \ 7
'                FcstTrend = TrendPeriods * mItemRecord!Trend
''                AIMJobLog "mCalc_ForecastForPeriod", 0, "TargetDate:" & CStr(TargetDate) & _
'                    "TargetPeriod:" & CStr(TargetPeriod) & _
'                    "TrendPeriods:" & CStr(TrendPeriods) & _
'                    "FcstTrend:" & CStr(FcstTrend)
'
'                'Check for trend limit exception
'                If Abs(FcstTrend) > Abs(p_HiTrndL * p_FcstDemand) Then
'                    If FcstTrend >= 0 Then
'                        FcstTrend = p_FcstDemand * p_HiTrndL
'                    Else
'                        FcstTrend = p_FcstDemand * p_HiTrndL * -1
'                    End If
''                AIMJobLog "mCalc_ForecastForPeriod", 0, "TrendLimitException (" & CStr(p_HiTrndL) & ") times p_FcstDemand (" & CStr(p_FcstDemand) & ")gives:" & CStr(FcstTrend)
'                End If
'            Else
'                TrendPeriods = 0
'                FcstTrend = 0
'            End If
'            If p_ApplySeasonsIndex Then
'                'Apply the Seasonality Profile; if required
'                Demand = IIf(mItemRecord!ApplySeasonsIndex, _
'                            (p_FcstDemand + FcstTrend) * SI_BaseIndex(TargetPeriod), _
'                            (p_FcstDemand + FcstTrend))
'
'                If mItemRecord!ApplySeasonsIndex Then
''                AIMJobLog "mCalc_ForecastForPeriod", 0, "Methods.ApplySeasons is true. Demand=" & CStr(Demand) & ", given SI_BaseIndex(" & CStr(TargetPeriod) & ")=" & CStr(SI_BaseIndex(TargetPeriod))
'                Else
''                AIMJobLog "mCalc_ForecastForPeriod", 0, "Methods.ApplySeasons is false. Demand (=p_FcstDemand+FcstTrend)=" & CStr(Demand)
'                End If
'
'            End If
'
'            'Check for an active promotion
'            'Promotion is Enabled, and
'            '[Current Start Date] >= [Promotion Start Date] and [Current Start Date] <= [Promotion End Date]
'            If PM_PmStatus _
'            And TargetDate >= PM_PmStartDate _
'            And TargetDate <= PM_PmEndDate _
'            Then
''                AIMJobLog "mCalc_ForecastForPeriod", 0, "PM.PmStatus is true. adjustment given PM_Adjust(" & CStr(TargetPeriod) & ")=" & CStr(PM_Adjust(TargetPeriod))
'                Demand = Demand * PM_Adjust(TargetPeriod)
'                r_PMAdjustment = r_PMAdjustment + PM_Adjust(TargetPeriod)
''                AIMJobLog "mCalc_ForecastForPeriod", 0, "...demand=demand * pm_adjust==" & CStr(Demand) & _
'                    " and r_PMAdjustment=" & CStr(r_PMAdjustment)
'            End If
'
'            'Update Demand for Period
'            If p_FcstInterval = int_Days Then
'                FcstForPeriod = Demand * DailyPct
'            Else
'                If TargetDate = FirstStartDate _
'                And TargetDate = LastStartDate Then
'                    'True and True
'                    FcstForPeriod = Demand * (StartWeekPct)
'                ElseIf TargetDate <> FirstStartDate _
'                And TargetDate <> LastStartDate Then
'                    'False and False
'                    FcstForPeriod = FcstForPeriod + Demand
'                ElseIf TargetDate = FirstStartDate _
'                And TargetDate <> LastStartDate Then
'                    'True and False
'                    FcstForPeriod = Demand * StartWeekPct
'                ElseIf TargetDate <> FirstStartDate _
'                And TargetDate = LastStartDate Then
'                    'False and True
'                    FcstForPeriod = FcstForPeriod + (Demand * EndWeekPct)
'                    Exit Do
'                End If
'            End If
'            AIMJobLog "mCalc_ForecastForPeriod", 0, "Updated demand (using start/daily pct):" & CStr(FcstForPeriod)
'        End If
'
'        'Next Week
'        If TargetPeriod = 0 _
'        And PreviousPeriod = 52 Then
'            If DatePart("yyyy", TargetDate) Mod 4 = 0 Then
'                TargetDate = TargetDate + 2
'            Else
'                TargetDate = TargetDate + 1
'            End If
'        Else
'            TargetDate = TargetDate + 7
'        End If
'        PreviousPeriod = TargetPeriod
''        AIMJobLog "mCalc_ForecastForPeriod", 0, "Next - forecast from the Start Date(" & CStr(p_PeriodStart) & ") through the End Date(" & CStr(p_PeriodEnd) & ")..."
'    Loop
'
'    mCalc_ForecastForPeriod = FcstForPeriod
''    AIMJobLog "mCalc_ForecastForPeriod", 0, "Before exiting mCalc_ForecastForPeriod, FcstForPeriod = " & CStr(FcstForPeriod)
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_ForecastForPeriod)"
'End Function
'
'Private Function mCalc_PartialPercentages( _
'    p_Cn As ADODB.Connection, p_FcstInterval As Long, _
'    p_PeriodStart As Date, p_PeriodEnd As Date, _
'    r_FirstStartDate As Date, r_LastStartDate As Date, _
'    r_StartWeekPct As Double, r_EndWeekPct As Double, r_DailyPct As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim StartWeekDays As Long
'    Dim EndWeekDays As Long
'    Dim tempInt As Long
'    Dim tempDate As Date
'    Dim RtnCode As Long
'
'    'move this off to a stored procedure so that this can be used for adjusting adjustments by different dates)
'
'    'Default to failure
'    mCalc_PartialPercentages = -1
'
'    'Calculate the First Date of the First Period
'    RtnCode = gCalc_AIMCalendar(p_Cn, r_FirstStartDate, 0, CA_STARTDATEFMDATE, p_FcstInterval, p_PeriodStart)
'    If r_FirstStartDate = "12:00:00 AM" Then
'        mCalc_PartialPercentages = 0
'        Exit Function
'    End If
'
'    'Calculate the First Date of the Last Period
'    RtnCode = gCalc_AIMCalendar(p_Cn, r_LastStartDate, 0, CA_STARTDATEFMDATE, p_FcstInterval, p_PeriodEnd)
'    If r_LastStartDate = "12:00:00 AM" Then
'        mCalc_PartialPercentages = 0
'        Exit Function
'    End If
'
'    'Calculate the number of working days in the first week
'    RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, StartWeekDays, CA_WORKINGDAYSINWEEK, p_FcstInterval, p_PeriodStart)
'
'    'Calculate the number of working days in the last period
'    RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, EndWeekDays, CA_WORKINGDAYSINWEEK, p_FcstInterval, p_PeriodEnd)
'
'    If p_FcstInterval = int_Days Then
'        RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, tempInt, CA_WORKINGDAYS, p_FcstInterval, , p_PeriodStart, p_PeriodEnd)
'        r_DailyPct = IIf(EndWeekDays > 0, tempInt / EndWeekDays, 0)
'        r_StartWeekPct = 0
'        r_EndWeekPct = 0
'    Else
'        'Calculate the Start Week Percentages
'        If StartWeekDays > 0 Then
'            'If the lead time is less than 7 days then we need to calculate the
'            'Pcnt from the startdate to the enddate
'            If p_PeriodEnd < (r_FirstStartDate + 6) _
'            Then
'                RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, tempInt, CA_WORKINGDAYS, p_FcstInterval, , p_PeriodStart, p_PeriodEnd)
'                r_StartWeekPct = tempInt / StartWeekDays
'            Else
'                RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, tempInt, CA_WORKINGDAYS, p_FcstInterval, , p_PeriodStart, (r_FirstStartDate + 6))
'                r_StartWeekPct = tempInt / StartWeekDays
'            End If
'        Else
'            r_StartWeekPct = 0
'        End If
'
'        'Calculate the End Week Percentages
'        If EndWeekDays > 0 Then
'            RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, tempInt, CA_WORKINGDAYS, p_FcstInterval, , r_LastStartDate, p_PeriodEnd)
'            r_EndWeekPct = tempInt / EndWeekDays
'        Else
'            r_EndWeekPct = 0
'        End If
'    End If
'
'    'Return success
'    mCalc_PartialPercentages = 1
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_PartialPercentages)"
'End Function
''Key data elements for Net Requirements
''
''m_Fcst(1 To 104) As Double         'Forecast Results by Period
''m_PMAdj(1 To 52) As Double         'Promotional Adjustments
''m_PlannedRcpts(1 To 104) As Long   'Planned Purchase Order Receipts
''AvailInv                           'Available Inventory
''Burn                               'Forecast demand between the Stock Date and Forecast StartDate
''EstOnOrder                         'Estimated Open Orders (Orders Generated between the StockDate and Forecast Start Date
''Estimate Planned Receipts between the Stock Date and the Forecast End Date
'Private Function mCalc_NetRqmt( _
'    p_Cn As ADODB.Connection, p_FcstStoreKey As Long, p_LcID As String, p_Item As String, _
'    p_RowIdx As Long, p_TotalFcstPeriods As Long, _
'    p_TargetFYStartDate As Date, p_TargetFYEndDate As Date, p_TargetFiscalYear As Long, _
'    p_BaseDate As Date, p_HiTrndL As Double, p_Dft_TurnHigh As Double, p_Dft_TurnLow As Double, _
'    p_HiMadP As Double, p_LoMadP As Double, p_MadExK As Double, p_DIMADP As Double, _
'    p_KFactor As Double, p_Int_Enabled As String, p_IntSafetyStock As Double, p_StkDate As Date, _
'    p_FcstInterval As Long, p_ApplyTrend As Long, p_PMAdjustment As Double, _
'    p_DmdScalingFactor As Double, p_ScalingEffUntil As Date, _
'    p_BkQty() As Long, p_BkCost() As Double, p_ReplenCostAll As Double, p_Cost As Double, p_Price As Double _
'    , r_ForecastData As FORECAST_DATA _
') As Double
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim w_StkDate As Date
'    Dim AvailInv As Double
'    Dim W_AvailInv As Double
'    Dim Burn As Long
'    Dim CountReviewDays As Long
'    Dim AccumFcst As Long
'    Dim FcstDemand As Double
'    Dim SysForecast As Double   '() As Double
'    Dim ReviewCounter As Long
'    Dim TargetPeriod As Long
'    Dim ReviewDays() As Date
'    Dim ReviewTime As Long
'
'    Dim SOQ As Double
'    Dim EstOnOrder As Long  'Estimated Open Orders -- Inventory consumed between the Stock Date and Start of the Forecast
'    Dim OrderPoint As Long
'    Dim OrderQty As Long
'
'    Dim tempDate As Date
'    Dim NetReqAdj As Double
'    Dim SafetyStock As Long
'
'    Dim ColIdx As Long
'    Dim PeriodStart As Date
'    Dim PeriodEnd As Date
'    Dim CalcValue_NetRqmt As Double, CalcValue_PlnRcpt As Double
'
'    If p_StkDate < mItemRecord!ActDate - mItemRecord!Accum_Lt Then
'        p_StkDate = mItemRecord!ActDate - mItemRecord!Accum_Lt
'    End If
'
'    ''Calculate the Burn (Demand Between the Stock Date and the Forecast Start Date)
'    'Burn = Forecast(StkDate, (FcstStartdate - 1), Adj)
'    ''Use stkdate in place of fcststartdate so make Burn =0
'    Burn = 0
'    w_StkDate = IIf(p_StkDate >= p_TargetFYStartDate, p_TargetFYStartDate, p_StkDate)
''    'AIMJobLog "mCalc_NetRqmt", 0, "p_StkDate=" & CStr(p_StkDate) & _
'    " and p_TargetFYStartDate=" & CStr(p_TargetFYStartDate) & _
'    " so, w_stkdate=" & CStr(w_StkDate)
'
'    'Calculate the available inventory
'    AvailInv = mItemRecord!Oh + mItemRecord!Oo - mItemRecord!ComStk - mItemRecord!BkOrder - mItemRecord!BkComStk - Burn
''    AIMJobLog "mCalc_NetRqmt", 0, "AvailInv=" & CStr(AvailInv)
'
'    'Build the Review Dates
'    CountReviewDays = mInit_ReviewDays(p_Cn, p_TargetFYEndDate, ReviewDays(), w_StkDate, ReviewTime)
'    'AIMJobLog "mCalc_NetRqmt", 0, "CountReviewDays=" & CStr(CountReviewDays)
'
'    'Check for no review days
'    If CountReviewDays = 0 Then
'        Exit Function
'    End If
'
''    ReDim SysForecast(CountReviewDays)
'    SysForecast = 0
'    'For ReviewCounter = LBound(ReviewDays) To m_NbrReviewDays - 1
'    AIMJobLog "mCalc_NetRqmt", 0, "Start loop - For ReviewCounter equals " & CStr(LBound(ReviewDays)) & " to " & CStr(CountReviewDays)
'    For ReviewCounter = LBound(ReviewDays) To CountReviewDays
'        'Update the Accumulate Forecast
'        If ReviewCounter = 1 Then
'            If mItemRecord!ActDate > w_StkDate Then
''''''                AccumFcst = AccumFcst + Forecast(StkDate, ReviewDays(ReviewCounter), Adj)
''                AIMJobLog "mCalc_NetRqmt", 0, "RevCounter is 0 and mItemRecord!ActDate(" & CStr(mItemRecord!ActDate) & ") > w_StkDate... calling mCalc_SysRqmt..."
'                SysForecast = mCalc_SysRqmt(p_Cn, p_FcstStoreKey, p_LcID, p_Item, p_FcstInterval, p_ApplyTrend, _
'                    w_StkDate, ReviewDays(ReviewCounter), p_BaseDate, _
'                    p_HiTrndL, p_DmdScalingFactor, p_ScalingEffUntil, _
'                    p_PMAdjustment, FcstDemand, NetReqAdj)
'                AccumFcst = AccumFcst + SysForecast
''                AIMJobLog "mCalc_NetRqmt", 0, "AccumFcst" & CStr(AccumFcst)
'            End If
'        Else
'            If mItemRecord!ActDate <= ReviewDays(ReviewCounter) Then
'''''                AccumFcst = AccumFcst + Round(Forecast(ReviewDays(ReviewCounter - 1), ReviewDays(ReviewCounter) - 1, Adj), 0)
''                AIMJobLog "mCalc_NetRqmt", 0, "RevCounter is NOT 0 and mItemRecord!ActDate(" & CStr(mItemRecord!ActDate) & ") <= ReviewDays(RevCounter) [which is " & CStr(ReviewDays(ReviewCounter)) & "]... calling mCalc_SysRqmt..."
'                SysForecast = mCalc_SysRqmt(p_Cn, p_FcstStoreKey, p_LcID, p_Item, _
'                    p_FcstInterval, p_ApplyTrend, _
'                    ReviewDays(ReviewCounter - 1), ReviewDays(ReviewCounter) - 1, p_BaseDate, _
'                    p_HiTrndL, p_DmdScalingFactor, p_ScalingEffUntil, _
'                    p_PMAdjustment, FcstDemand, NetReqAdj)
'                AccumFcst = AccumFcst + Round(SysForecast, 0)
''                AIMJobLog "mCalc_NetRqmt", 0, "AccumFcst" & CStr(AccumFcst)
'            End If
'        End If
'
'        'Update Order Policies *****
''        AIMJobLog "mCalc_NetRqmt", 0, "Calling mCalc_OrderPolicyUpdate..."
'        RtnCode = mCalc_OrderPolicyUpdate(p_Cn, p_FcstStoreKey, p_LcID, p_Item, ReviewDays(ReviewCounter), False, _
'            p_KFactor, p_Int_Enabled, p_IntSafetyStock, p_Dft_TurnHigh, p_Dft_TurnLow, _
'            p_LoMadP, p_HiMadP, p_MadExK, p_DIMADP, _
'            p_FcstInterval, p_ApplyTrend, p_BaseDate, _
'            p_HiTrndL, p_DmdScalingFactor, p_ScalingEffUntil, _
'            ReviewTime, FcstDemand, p_BkQty(), p_BkCost(), p_ReplenCostAll, p_Cost, _
'            OrderPoint, OrderQty, SafetyStock, _
'            p_PMAdjustment, NetReqAdj)
'
'        'Update inventory availability ******
'        W_AvailInv = AvailInv - AccumFcst
''        AIMJobLog "mCalc_NetRqmt", 0, "After mCalc_OrderPolicyUpdate, W_AvailInv (AvailInv - AccumFcst): " & CStr(W_AvailInv)
'        SOQ = 0
'        'Test the Order Point
'        If W_AvailInv + EstOnOrder <= OrderPoint Then
''            AIMJobLog "mCalc_NetRqmt", 0, "Test the order point is True"
'            SOQ = OrderPoint - (W_AvailInv + EstOnOrder) + OrderQty
'
'            'Pack round the planned requirement
'            If SOQ <> 0 Then
'                'AIMJobLog "mCalc_NetRqmt", 0, "PackRound the planned requirement"
'                SOQ = PackRound(CDbl(SOQ), mItemRecord!UOM, mItemRecord!ConvFactor, mItemRecord!BuyingUOM, _
'                    mItemRecord!PackRounding, mItemRecord!IMin, mItemRecord!IMax)
'            End If
'            'Increment the Estimated Order Quantity
'            'AIMJobLog "mCalc_NetRqmt", 0, "Increment the estimated order quantity: EstOnOrder = EstOnOrder + SOQ... " & _
'                " which is " & CStr(EstOnOrder) & " = " & CStr(EstOnOrder) & " + " & CStr(SOQ)
'            EstOnOrder = EstOnOrder + SOQ
'
'            'Update the Net Requirements Array
'            If ReviewDays(ReviewCounter) >= p_TargetFYStartDate _
'            And ReviewDays(ReviewCounter) <= p_TargetFYEndDate _
'            Then
'                'Determine the period associated with the review day
'                PeriodStart = p_TargetFYStartDate
'                For ColIdx = 1 To p_TotalFcstPeriods
'                    'Get the end date for the given period
'                    PeriodEnd = gDate_NextPeriodStart(p_TargetFYStartDate, PeriodStart, p_FcstInterval)
'                    PeriodEnd = DateAdd("d", -1#, PeriodEnd)
'                    If ReviewDays(ReviewCounter) >= PeriodStart _
'                    And ReviewDays(ReviewCounter) <= PeriodEnd _
'                    Then
''                        AIMJobLog "mCalc_NetRqmt", 0, "Update the Net Requirements Array..."
'                        CalcValue_NetRqmt = CalcValue_NetRqmt + SOQ
'                        r_ForecastData.DataCalc_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_NetRqmt) = CalcValue_NetRqmt
'                    End If
'                    'Update the Planned Receipts Array
'                    If ReviewDays(ReviewCounter) + mItemRecord!Accum_Lt >= PeriodStart _
'                    And ReviewDays(ReviewCounter) + mItemRecord!Accum_Lt <= PeriodEnd _
'                    Then
''                        AIMJobLog "mCalc_NetRqmt", 0, "Update the planned receipts array... given Accum_Lt=" & CStr(mItemRecord!Accum_Lt) & _
'                            " Compare ReviewDays(ReviewCounter) + Accum_Lt [" & CStr(ReviewDays(ReviewCounter) + mItemRecord!Accum_Lt) & "]; p_TargetFYStartDate [" & CStr(p_TargetFYStartDate) & "]; p_TargetFYEndDate [" & CStr(p_TargetFYEndDate) & "]"
'
'                        CalcValue_PlnRcpt = CalcValue_PlnRcpt + SOQ
'                        r_ForecastData.DataCalc_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt) = CalcValue_PlnRcpt
'
''                        AIMJobLog "mCalc_NetRqmt", 0, "CalcValue_PlnRcpt = " & CStr(CalcValue_PlnRcpt)
'                    End If
'
''>>> TO DO
'                    'Determine FY and get price/cost (PFY_, FY_ or NFY_)
'''''                GetCurrentPriceCost ReviewDays(ReviewCounter), Price, Cost
'                    'AIMJobLog "mCalc_NetRqmt", 0, "<TO DO> GetCurrenPriceCost for date: " & CStr(ReviewDays(ReviewCounter))
'                    Select Case Year(ReviewDays(ReviewCounter))
'                    Case Is = p_TargetFiscalYear - 1
'                        'Previous Fiscal Year
'    '                    ForecastData.DCT_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_PriceAdjmt) = PFY_Price
'    '                    ForecastData.DCT_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_CostAdjmt) = PFY_Cost
'                    Case Is = p_TargetFiscalYear
'                        'Fiscal Year (current)
'    '                    ForecastData.DCT_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_PriceAdjmt) = FY_Price
'    '                    ForecastData.DCT_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_CostAdjmt) = FY_Cost
'                    Case Is = p_TargetFiscalYear + 1
'                        'Next Fiscal Year
'    '                    ForecastData.DCT_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_PriceAdjmt) = NFY_Price
'    '                    ForecastData.DCT_Value(p_RowIdx, ColIdx, DATA_CALC_TYPE.DCT_CostAdjmt) = NFY_Cost
'                    End Select
'    '                Price = Price * SOQ
'    '                Cost = Cost * SOQ
'    '                m_xTotalPrice = m_xTotalPrice + Price
'    '                m_xTotalCost = m_xTotalCost + Cost
'    '                m_xPriceAdj(Pd) = m_xPriceAdj(Pd) + Price
'    '                m_xCostAdj(Pd) = m_xCostAdj(Pd) + Cost
'    '                m_xTotalFcst = m_xTotalFcst + SOQ
'    '                Price = 0
'                    PeriodStart = DateAdd("d", 1#, PeriodEnd)
'                Next
'            End If
'        End If
''        AIMJobLog "mCalc_NetRqmt", 0, "Next - For ReviewCounter equals " & CStr(LBound(ReviewDays)) & " to " & CStr(CountReviewDays)
'    Next ReviewCounter
'
'    mCalc_NetRqmt = RtnCode
''    AIMJobLog "mCalc_NetRqmt", 0, "Exiting mCalc_NetRqmt"
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_NetRqmt)"
'End Function
'
'Private Function mCalc_HstDmnd( _
'    p_Cn As ADODB.Connection, p_FcstInterval As Long, _
'    p_PeriodStart As Date, p_PeriodEnd As Date, _
'    p_DemandSource As String _
') As Double
'On Error GoTo ErrorHandler
'
'    Dim AIM_DmdPlan_HstDmnd_Sp As ADODB.Command
'    Dim rsHstDmnd As ADODB.Recordset
'    Dim FirstStartDate As Date
'    Dim LastStartDate As Date
'    Dim StartWeekPct As Double
'    Dim EndWeekPct As Double
'    Dim DailyPct As Double
'
'    Dim Sales As Double
'    Dim Orders As Double
'
'    Dim RtnCode As Long
'
'    Set AIM_DmdPlan_HstDmnd_Sp = New ADODB.Command
'    With AIM_DmdPlan_HstDmnd_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_HstDmnd_Sp"
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlan_HstDmnd_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_HstDmnd_Sp.CreateParameter("@LcId", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_HstDmnd_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'        .Parameters.Append AIM_DmdPlan_HstDmnd_Sp.CreateParameter("@PeriodStart", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlan_HstDmnd_Sp.CreateParameter("@PeriodEnd", adDBDate, adParamInput)
'    'Set values
'        .Parameters("@LcId").Value = mItemRecord!LcId
'        .Parameters("@Item").Value = mItemRecord!Item
'        .Parameters("@PeriodStart").Value = p_PeriodStart
'        .Parameters("@PeriodEnd").Value = p_PeriodEnd
'    End With
'
'    'Fetch results
'    Set rsHstDmnd = New ADODB.Recordset
'    With rsHstDmnd
'        .CursorLocation = adUseClient
'        .CursorType = adOpenForwardOnly
'        .LockType = adLockReadOnly
'        .Open AIM_DmdPlan_HstDmnd_Sp
'    End With
'
'    RtnCode = AIM_DmdPlan_HstDmnd_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        'TO DO
'        RtnCode = mCalc_PartialPercentages(p_Cn, p_FcstInterval, p_PeriodStart, p_PeriodEnd, _
'                        FirstStartDate, LastStartDate, StartWeekPct, EndWeekPct, DailyPct)
'
'        rsHstDmnd.MoveFirst
'        Do While Not rsHstDmnd.eof
'            If rsHstDmnd!FYPeriodStartDate > (p_PeriodStart - 7) _
'            And rsHstDmnd!FYPeriodStartDate < (p_PeriodEnd + 7) _
'            Then
'                If p_PeriodStart = p_PeriodEnd Then
'                    'Daily
'                    Sales = Sales + IIf(IsNull(rsHstDmnd!PeriodSales), 0, rsHstDmnd!PeriodSales)
'                    Orders = Orders + IIf(IsNull(rsHstDmnd!PeriodOrders), 0, rsHstDmnd!PeriodOrders)
'                Else
'                    'Weekly
'                    If rsHstDmnd!FYPeriodStartDate <= p_PeriodStart Then
'                        Sales = Sales + IIf(IsNull(rsHstDmnd!PeriodSales), 0, rsHstDmnd!PeriodSales) * StartWeekPct
'                        Orders = Orders + IIf(IsNull(rsHstDmnd!PeriodOrders), 0, rsHstDmnd!PeriodOrders) * StartWeekPct
'                    ElseIf (rsHstDmnd!FYPeriodStartDate + 7) > p_PeriodEnd Then
'                        Sales = Sales + IIf(IsNull(rsHstDmnd!PeriodSales), 0, rsHstDmnd!PeriodSales) * EndWeekPct
'                        Orders = Orders + IIf(IsNull(rsHstDmnd!PeriodOrders), 0, rsHstDmnd!PeriodOrders) * EndWeekPct
'                    Else
'                        Sales = Sales + IIf(IsNull(rsHstDmnd!PeriodSales), 0, rsHstDmnd!PeriodSales)
'                        Orders = Orders + IIf(IsNull(rsHstDmnd!PeriodOrders), 0, rsHstDmnd!PeriodOrders)
'                    End If
'                End If
'            End If
'            rsHstDmnd.MoveNext
'        Loop
'        If p_PeriodStart = p_PeriodEnd Then
'            Sales = Sales * DailyPct
'            Orders = Orders * DailyPct
'        End If
'
'        If p_DemandSource = "O" Then    'Qty Ordered
'            mCalc_HstDmnd = Orders
'        ElseIf p_DemandSource = "S" Then    'Qty Shipped
'            mCalc_HstDmnd = Sales
'        End If
'
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'CleanUp:
'    If Not (AIM_DmdPlan_HstDmnd_Sp Is Nothing) Then Set AIM_DmdPlan_HstDmnd_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_HstDmnd_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_HstDmnd_Sp Is Nothing) Then Set AIM_DmdPlan_HstDmnd_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_HstDmnd_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_HstDmnd)"
'End Function
'
'Private Function mCalc_PrjInvt( _
') As Double
'On Error GoTo ErrorHandler
'
'    'FcstStartdate will be today's date which is the default
'    'Calculate the available inventory
'    mCalc_PrjInvt = mItemRecord!Oh _
'                    + mItemRecord!Oo _
'                    - mItemRecord!ComStk _
'                    - mItemRecord!BkOrder _
'                    - mItemRecord!BkComStk
'
'
'    If mCalc_PrjInvt < 0# Then mCalc_PrjInvt = 0#
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_PrjInvt)"
'End Function
'
'Private Function mCalc_PrdCons( _
') As Double
'On Error GoTo ErrorHandler
'
''check which ones to update of net rqmts and prj invt
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_PrdCons)"
'End Function
'
'Private Function mFetch_OptionAttribs( _
'    p_Cn As ADODB.Connection, _
'    r_HiTrndL As Double, _
'    r_Dft_TurnHigh As Double, r_Dft_TurnLow As Double, _
'    r_HiMadP As Double, r_LoMadP As Double, _
'    r_MadExK As Double, r_DIMADP As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_OptionAttribs_Sp As ADODB.Command
'
'    'Init. the command object
'    Set AIM_DmdPlan_OptionAttribs_Sp = New ADODB.Command
'    With AIM_DmdPlan_OptionAttribs_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_OptionAttribs_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@DPIL_OptionID", adVarWChar, adParamInput, 6, mItemRecord!OptionId)
'    'Output
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@HiTrndL", adDouble, adParamOutput, 3.2, 0#)
'        .Parameters("@HiTrndL").Precision = 3
'        .Parameters("@HiTrndL").NumericScale = 2
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@Dft_TurnHigh", adDouble, adParamOutput, 4.1, 0#)
'        .Parameters("@Dft_TurnHigh").Precision = 4
'        .Parameters("@Dft_TurnHigh").NumericScale = 1
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@Dft_TurnLow", adDouble, adParamOutput, 4.1, 0#)
'        .Parameters("@Dft_TurnLow").Precision = 4
'        .Parameters("@Dft_TurnLow").NumericScale = 1
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@HiMadP", adDouble, adParamOutput, 3.2, 0#)
'        .Parameters("@HiMadP").Precision = 3
'        .Parameters("@HiMadP").NumericScale = 2
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@LoMadP", adDouble, adParamOutput, 3.2, 0#)
'        .Parameters("@LoMadP").Precision = 3
'        .Parameters("@LoMadP").NumericScale = 2
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@MadExK", adDouble, adParamOutput, 2.1, 0#)
'        .Parameters("@MadExK").Precision = 2
'        .Parameters("@MadExK").NumericScale = 1
'        .Parameters.Append AIM_DmdPlan_OptionAttribs_Sp.CreateParameter("@DIMADP", adDouble, adParamOutput, 2.1, 0#)
'        .Parameters("@DIMADP").Precision = 2
'        .Parameters("@DIMADP").NumericScale = 1
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlan_OptionAttribs_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        r_HiTrndL = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@HiTrndL").Value
'        r_Dft_TurnHigh = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@Dft_TurnHigh").Value
'        r_Dft_TurnLow = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@Dft_TurnLow").Value
'        r_DIMADP = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@DIMADP").Value
'        r_HiMadP = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@HiMadP").Value
'        r_LoMadP = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@LoMadP").Value
'        r_MadExK = AIM_DmdPlan_OptionAttribs_Sp.Parameters("@MadExK").Value
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    mFetch_OptionAttribs = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlan_OptionAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_OptionAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_OptionAttribs_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_OptionAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_OptionAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_OptionAttribs_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(mFetch_OptionAttribs)"
'End Function
'
'Private Function mFetch_SeasonsAttribs( _
'    p_Cn As ADODB.Connection, _
'    r_BaseIndex() As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_SeasonsAttribs_Sp As ADODB.Command
'    Dim BaseIndexName As String
'    Dim IndexCounter As Long
'
'    'Init. the command object
'    Set AIM_DmdPlan_SeasonsAttribs_Sp = New ADODB.Command
'    With AIM_DmdPlan_SeasonsAttribs_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_SeasonsAttribs_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlan_SeasonsAttribs_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_SeasonsAttribs_Sp.CreateParameter("@DPIL_SAID", adVarWChar, adParamInput, 20, mItemRecord!SaId)
'    'Output
'        For IndexCounter = 1 To 52
'            BaseIndexName = "@BI" & CStr(Format(IndexCounter, "00"))
'            .Parameters.Append AIM_DmdPlan_SeasonsAttribs_Sp.CreateParameter(BaseIndexName, adDouble, adParamOutput, 5.3, 0#)
'            .Parameters(BaseIndexName).Precision = 5
'            .Parameters(BaseIndexName).NumericScale = 3
'        Next
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlan_SeasonsAttribs_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        For IndexCounter = 1 To 52
'            BaseIndexName = "@BI" & CStr(Format(IndexCounter, "00"))
'            r_BaseIndex(IndexCounter) = AIM_DmdPlan_SeasonsAttribs_Sp.Parameters(BaseIndexName).Value
'        Next
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    mFetch_SeasonsAttribs = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlan_SeasonsAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_SeasonsAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_SeasonsAttribs_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_SeasonsAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_SeasonsAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_SeasonsAttribs_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(mFetch_SeasonsAttribs)"
'End Function
'
'Private Function mFetch_PromotionAttribs( _
'    p_Cn As ADODB.Connection, _
'    r_PmStatus As Boolean, _
'    r_PmStartDate As Date, _
'    r_PmEndDate As Date, _
'    r_PmAdj() As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_PromotionAttribs_Sp As ADODB.Command
'    Dim PmAdjName As String
'    Dim IndexCounter As Long
'
'    'Init. the command object
'    Set AIM_DmdPlan_PromotionAttribs_Sp = New ADODB.Command
'    With AIM_DmdPlan_PromotionAttribs_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_PromotionAttribs_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlan_PromotionAttribs_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_PromotionAttribs_Sp.CreateParameter("@DPIL_PmID", adVarWChar, adParamInput, 20, mItemRecord!PmId)
'    'Output
'        .Parameters.Append AIM_DmdPlan_PromotionAttribs_Sp.CreateParameter("@PmStatus", adBoolean, adParamOutput)
'        .Parameters.Append AIM_DmdPlan_PromotionAttribs_Sp.CreateParameter("@PmStartDate", adDBDate, adParamOutput)
'        .Parameters.Append AIM_DmdPlan_PromotionAttribs_Sp.CreateParameter("@PmEndDate", adDBDate, adParamOutput)
'        For IndexCounter = 1 To 52
'            PmAdjName = "@PmAdj" & CStr(Format(IndexCounter, "00"))
'            .Parameters.Append AIM_DmdPlan_PromotionAttribs_Sp.CreateParameter(PmAdjName, adDouble, adParamOutput, 5.3, 0#)
'            .Parameters(PmAdjName).Precision = 5
'            .Parameters(PmAdjName).NumericScale = 3
'        Next
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlan_PromotionAttribs_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'        For IndexCounter = 1 To 52
'            PmAdjName = "@PmAdj" & CStr(Format(IndexCounter, "00"))
'            r_PmAdj(IndexCounter) = AIM_DmdPlan_PromotionAttribs_Sp.Parameters(PmAdjName).Value
'        Next
'        r_PmStartDate = AIM_DmdPlan_PromotionAttribs_Sp.Parameters("@PmStartDate").Value
'        r_PmEndDate = AIM_DmdPlan_PromotionAttribs_Sp.Parameters("@PmEndDate").Value
'        r_PmStatus = AIM_DmdPlan_PromotionAttribs_Sp.Parameters("@PmStatus").Value
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    mFetch_PromotionAttribs = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlan_PromotionAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_PromotionAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_PromotionAttribs_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_PromotionAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_PromotionAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_PromotionAttribs_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(mFetch_PromotionAttribs)"
'End Function
'
'Private Function mFetch_LocationAttribs( _
'    p_Cn As ADODB.Connection, _
'    r_DmdScalingFactor As Double, _
'    r_ScalingEffUntil As Date, _
'    r_ReplenCost As Double, _
'    r_DemandSource As String, _
'    r_StkDate As Date _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_LocationAttribs_Sp As ADODB.Command
'
'    'Init. the command object
'    Set AIM_DmdPlan_LocationAttribs_Sp = New ADODB.Command
'    With AIM_DmdPlan_LocationAttribs_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_LocationAttribs_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("@DPIL_LcID", adVarWChar, adParamInput, 12, mItemRecord!LcId)
'    'Output
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("@DmdScalingFactor", adDecimal, adParamOutput)
'        .Parameters("@DmdScalingFactor").Precision = 4
'        .Parameters("@DmdScalingFactor").NumericScale = 3
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("@ScalingEffUntil", adDBDate, adParamOutput)
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("@ReplenCost", adDecimal, adParamOutput)
'        .Parameters("@ReplenCost").Precision = 11
'        .Parameters("@ReplenCost").NumericScale = 4
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("@DemandSource", adVarWChar, adParamOutput, 1)
'        .Parameters.Append AIM_DmdPlan_LocationAttribs_Sp.CreateParameter("@StkDate", adDBDate, adParamOutput)
'    'Fetch results
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlan_LocationAttribs_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case 1  'Success
'    r_DmdScalingFactor = AIM_DmdPlan_LocationAttribs_Sp.Parameters("@DmdScalingFactor").Value
'    r_ScalingEffUntil = AIM_DmdPlan_LocationAttribs_Sp.Parameters("@ScalingEffUntil").Value
'    r_ReplenCost = AIM_DmdPlan_LocationAttribs_Sp.Parameters("@ReplenCost").Value
'    r_DemandSource = AIM_DmdPlan_LocationAttribs_Sp.Parameters("@DemandSource").Value
'    r_StkDate = AIM_DmdPlan_LocationAttribs_Sp.Parameters("@StkDate").Value
'
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    mFetch_LocationAttribs = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlan_LocationAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_LocationAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_LocationAttribs_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlan_LocationAttribs_Sp Is Nothing) Then Set AIM_DmdPlan_LocationAttribs_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_LocationAttribs_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(mFetch_LocationAttribs)"
'End Function
'
''**********************************************************************
''DERIVED FROM AIMADVFORECAST.InitReviewDays
'' Function Name:    mInit_ReviewDays()
'' Description:      This function initializes the reviewdays array based on the following rules:
''    If the Review Frequency is Monthly and Review time is 30 set the first
''    Review Day to the first day of the month from the Stock Date, then
''    the next Review Day will be the first day of the next month and so on.
''    e.g. : Stock Date = 13/10/2002, First Review Days will be 01/10/2002 .
'' Parameters:
'' Returns:          record count for the number of review days
''**********************************************************************
'Private Function mInit_ReviewDays( _
'    p_Cn As ADODB.Connection, _
'    p_TargetFYEndDate As Date, p_ReviewDays() As Date, _
'    p_StkDate As Date _
'    , r_ReviewTime As Long _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RvPtr As Long
'    Dim RvDate As Date
'    Dim w_NextDateTime As Date
'
'    Dim EndOfMonth As Date
'    Dim IntervalTest As Long
'    Dim LastWeek As Boolean 'bit
'    Dim StartofMonth As Date
'    Dim TrialRvDate As Date
'    Dim WeekOfMonth As Long
'    Dim RtnCode As Long
'
'    Dim RevSunday As Boolean
'    Dim RevMonday As Boolean
'    Dim RevTuesday As Boolean
'    Dim RevWednesday As Boolean
'    Dim RevThursday As Boolean
'    Dim RevFriday As Boolean
'    Dim RevSaturday As Boolean
'
'    'Dimension the Review Days Array
'    ReDim p_ReviewDays(1)
'
'    If mItemRecord!NextDateTime = p_StkDate Then
'        If mItemRecord!RevFreq = 3 And mItemRecord!ReviewTime = 30 Then
'            p_ReviewDays(1) = Format(CDate(Format(mItemRecord!NextDateTime, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'            w_NextDateTime = Format(CDate(Format(mItemRecord!NextDateTime, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'        Else
'            p_ReviewDays(1) = mItemRecord!NextDateTime
'            w_NextDateTime = mItemRecord!NextDateTime
'        End If
'    ElseIf mItemRecord!NextDateTime < p_StkDate Then
'        If mItemRecord!RevFreq = 3 And mItemRecord!ReviewTime = 30 Then
'             p_ReviewDays(1) = Format(CDate(Format(p_StkDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'            w_NextDateTime = Format(CDate(Format(p_StkDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'        Else
'            p_ReviewDays(1) = p_StkDate - 1
'            w_NextDateTime = p_StkDate - 1
'        End If
'    ElseIf mItemRecord!NextDateTime > p_StkDate Then
'        If mItemRecord!RevFreq = 3 And mItemRecord!ReviewTime = 30 Then
'             p_ReviewDays(1) = Format(CDate(Format(p_StkDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'            w_NextDateTime = Format(CDate(Format(p_StkDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
'        Else
'            p_ReviewDays(1) = p_StkDate - 1
'            w_NextDateTime = p_StkDate - 1
'        End If
'    End If
'
'    'Determine the review points between the Stock Date and the Forecast End Date
'    RvDate = p_ReviewDays(1)
'
'    'Initialize the Review Date Pointer
'    RvPtr = LBound(p_ReviewDays) + 1
'
'    Do Until RvDate > p_TargetFYEndDate
'        'Get the next review date
''        RvDate = GetNextRevDate(RvDate, w_NextDateTime, mitemrecord!RevFreq, mitemrecord!RevInterval, mitemrecord!RevSunday, mitemrecord!RevMonday, _
'            mitemrecord!RevTuesday, mitemrecord!RevWednesday, mitemrecord!RevThursday, mitemrecord!RevFriday, mitemrecord!RevSaturday, mitemrecord!WeekQualifier, _
'            mitemrecord!RevStartDate, mitemrecord!RevEndDate, , , mitemrecord!ReviewTime)
'
'            'Initialize [Trial Review Date] to [LastSchReview]
'            TrialRvDate = w_NextDateTime
'            Do Until TrialRvDate > RvDate
'                Select Case mItemRecord!RevFreq
'                Case 0                  'Dynamic
'                    TrialRvDate = DateAdd("d", 1#, TrialRvDate)
'                Case 1                  'Daily
'                    TrialRvDate = DateAdd("d", CDbl(mItemRecord!RevInterval), TrialRvDate)
'                Case 2                  'Weekly
'                    'Check for no day selected
'                    RevSunday = IIf(mItemRecord!RevSunday = 0, False, True)
'                    RevMonday = IIf(mItemRecord!RevMonday = 0, False, True)
'                    RevTuesday = IIf(mItemRecord!RevTuesday = 0, False, True)
'                    RevWednesday = IIf(mItemRecord!RevWednesday = 0, False, True)
'                    RevThursday = IIf(mItemRecord!RevThursday = 0, False, True)
'                    RevFriday = IIf(mItemRecord!RevFriday = 0, False, True)
'                    RevSaturday = IIf(mItemRecord!RevSunday = 0, False, True)
'
'                    If Not (RevSunday Or RevMonday Or RevTuesday _
'                        Or RevWednesday Or RevThursday Or RevFriday _
'                        Or RevSaturday) Then
'                        Exit Do
'                    End If
'                    Do
'                        'Increment date by one day
'                        TrialRvDate = DateAdd("d", 1#, TrialRvDate)
'                        'Test for Weekly Interval
'                        IntervalTest = (DateDiff("d", mItemRecord!RevStartDate, TrialRvDate) / 7) Mod mItemRecord!RevInterval
'                        If Weekday(TrialRvDate) = 1 And mItemRecord!RevSunday And IntervalTest = 0 Then Exit Do
'                        If Weekday(TrialRvDate) = 2 And mItemRecord!RevMonday And IntervalTest = 0 Then Exit Do
'                        If Weekday(TrialRvDate) = 3 And mItemRecord!RevTuesday And IntervalTest = 0 Then Exit Do
'                        If Weekday(TrialRvDate) = 4 And mItemRecord!RevWednesday And IntervalTest = 0 Then Exit Do
'                        If Weekday(TrialRvDate) = 5 And mItemRecord!RevThursday And IntervalTest = 0 Then Exit Do
'                        If Weekday(TrialRvDate) = 6 And mItemRecord!RevFriday And IntervalTest = 0 Then Exit Do
'                        If Weekday(TrialRvDate) = 7 And mItemRecord!RevSaturday And IntervalTest = 0 Then Exit Do
'                    Loop
'                Case 3      'Monthly
'                    TrialRvDate = DateAdd("m", CDbl(mItemRecord!RevInterval), TrialRvDate)
'                Case 4      'Week of Month
'                    Do
'                        TrialRvDate = DateAdd("d", 1#, TrialRvDate)
'                        'Test for Week of the Month
'                        StartofMonth = DateSerial(Year(TrialRvDate), Month(TrialRvDate), 1)
'                        EndOfMonth = DateAdd("m", 1#, StartofMonth) - 1
'                        WeekOfMonth = Int(DateDiff("d", StartofMonth, TrialRvDate) \ 7) + 1
'
'                        'Is this the last week ???
'                        If DateDiff("d", TrialRvDate, EndOfMonth) \ 7 = 0 Then
'                            LastWeek = True
'                        Else
'                            LastWeek = False
'                        End If
'
'                        If Weekday(TrialRvDate) = 1 And mItemRecord!RevSunday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                        If Weekday(TrialRvDate) = 2 And mItemRecord!RevMonday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                        If Weekday(TrialRvDate) = 3 And mItemRecord!RevTuesday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                        If Weekday(TrialRvDate) = 4 And mItemRecord!RevWednesday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                        If Weekday(TrialRvDate) = 5 And mItemRecord!RevThursday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                        If Weekday(TrialRvDate) = 6 And mItemRecord!RevFriday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                        If Weekday(TrialRvDate) = 7 And mItemRecord!RevSaturday _
'                            And (WeekOfMonth = mItemRecord!WeekQualifier Or (LastWeek And mItemRecord!WeekQualifier = 5)) Then Exit Do
'                    Loop
'                End Select
'            Loop        'End of Control Loop
'
'        w_NextDateTime = RvDate
'        RvDate = TrialRvDate
'
'        'If the review date is outside the forecast period -- quit
'        If RvDate > p_TargetFYEndDate Then
'            Exit Do
'        End If
'
'        'Add another date to the Review
'        If RvPtr > UBound(p_ReviewDays) Then
'            ReDim Preserve p_ReviewDays(RvPtr + 20)
'        End If
'
'        p_ReviewDays(RvPtr) = RvDate
'        RvPtr = RvPtr + 1
'    Loop
'
'    If RvPtr > 0 Then
'        mInit_ReviewDays = (RvPtr - 1)
'    Else
'        mInit_ReviewDays = 0
'    End If
'
'    'Set return parameters
'    r_ReviewTime = mItemRecord!ReviewTime
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mInit_ReviewDays)"
'End Function
'
''**********************************************************************
'' Function Name:    mCalc_OrderPolicyUpdate()
'' Description:      This function updates the order policy data elements
''                   in an item record. Derived from AIMAdvForecast.OrderPolicyUpdate
'' Parameters:       PolicyDate          'Base Date for Order Policy Calculations
''                   UpdatePeriodFcsts   'Update Forecasts for Month, Quarter, and Year
'' Returns:          FAIL
''                   SUCCEED
''**********************************************************************
'Private Function mCalc_OrderPolicyUpdate( _
'    p_Cn As ADODB.Connection, p_FcstStoreKey As Long, p_LcID As String, p_Item As String, _
'    PolicyDate As Date, UpdatePeriodFcsts As Boolean, _
'    KFactor As Double, Int_Enabled As String, IntSafetyStock As Double, _
'    Dft_TurnHigh As Double, Dft_TurnLow As Double, _
'    LoMADP As Double, HiMADP As Double, _
'    MADExK As Double, DIMADP As Double, _
'    p_FcstInterval As Long, p_ApplyTrend As Long, _
'    p_BaseDate As Date, _
'    p_HiTrndL As Double, p_DmdScalingFactor As Double, p_ScalingEffUntil As Date, _
'    p_ReviewTime As Long, p_FcstDemand As Double, _
'    p_BkQty() As Long, p_BkCost() As Double, p_ReplenCostAll As Double, _
'    p_Cost As Double _
'    , r_OrderPoint As Long, r_OrderQty As Long, r_SafetyStock As Long _
'    , p_PMAdjustment As Double, r_NetReqAdj As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim Adj As Double               'Forecast Adjustment
'    Dim Demand As Double            'Forecast Demand
'    Dim Turns As Double             'Estimated Turns
'    Dim WorkMAE As Double           'Mean Absolute Error
'    Dim ForecastLeadTime As Double  'Forecast for the Lead Time
'    Dim ForecastRevTime As Double   'Forecast for the Review Time
'    Dim PeriodStart As Date
'    Dim PeriodEnd As Date
'
'    'Check the ActDate
'    ForecastLeadTime = -1
'    If PolicyDate + mItemRecord!Accum_Lt <= mItemRecord!ActDate Then
'        ForecastLeadTime = 0
'    ElseIf PolicyDate + mItemRecord!Accum_Lt > mItemRecord!ActDate _
'    And PolicyDate < mItemRecord!ActDate _
'    Then
'        'Determine forecast lead time as system forecast using ActDate as startdate
'        PeriodStart = mItemRecord!ActDate
'    Else
'        'Determine forecast lead time as system forecast using PolicyDate as startdate
'        PeriodStart = PolicyDate
'    End If
'    'Set the end date for use in the system forecast to determine LeadTime
'    PeriodEnd = PolicyDate + (mItemRecord!Accum_Lt - 1)
'    If ForecastLeadTime = -1 Then
'        ForecastLeadTime = mCalc_SysRqmt(p_Cn, p_FcstStoreKey, p_LcID, p_Item, _
'            p_FcstInterval, p_ApplyTrend, _
'            PeriodStart, PeriodEnd, p_BaseDate, _
'            p_HiTrndL, p_DmdScalingFactor, p_ScalingEffUntil, _
'            p_PMAdjustment, p_FcstDemand, r_NetReqAdj)
'    End If
'
'    'Calculate the Forecast for the Item's Review Time
'    'Check the mItemRecord!ActDate
'    ForecastRevTime = -1
'    If mItemRecord!ItStat = "O" Then
'        ForecastRevTime = 0
'    Else
'        If PolicyDate + mItemRecord!Accum_Lt <= mItemRecord!ActDate Then
'            ForecastRevTime = 0
'        ElseIf PolicyDate + mItemRecord!Accum_Lt > mItemRecord!ActDate _
'        And PolicyDate < mItemRecord!ActDate _
'        Then
'            'Determine forecast review time as system forecast using ActDate + Accum_Lt as startdate
'            PeriodStart = mItemRecord!ActDate + mItemRecord!Accum_Lt
'        Else
'            'Determine forecast review time as system forecast using PolicyDate + Accum_Lt as startdate
'            PeriodStart = PolicyDate + mItemRecord!Accum_Lt
'        End If
'    End If
'    'Set the end date for use in the system forecast to determine LeadTime
'    PeriodEnd = PolicyDate + (mItemRecord!Accum_Lt + p_ReviewTime - 1)
'    If ForecastRevTime = -1 Then
'        ForecastRevTime = mCalc_SysRqmt(p_Cn, p_FcstStoreKey, p_LcID, p_Item, _
'            p_FcstInterval, p_ApplyTrend, _
'            PeriodStart, PeriodEnd, p_BaseDate, _
'            p_HiTrndL, p_DmdScalingFactor, p_ScalingEffUntil, _
'            p_PMAdjustment, p_FcstDemand, r_NetReqAdj)
'    End If
'
''    AIMJobLog "mCalc_OrderPolicyUpdate", 0, "ForecastLeadTime: " & CStr(ForecastLeadTime) & " and ForecastRevTime: " & CStr(ForecastRevTime)
'
'    'Apply High and Low Mean Absolute Error Filters
'    WorkMAE = mItemRecord!MAE
'
'    If p_FcstDemand > 0 Then
'        If mItemRecord!MAE / p_FcstDemand < LoMADP Then
'            WorkMAE = LoMADP * p_FcstDemand
'        End If
'
'        If mItemRecord!MAE / p_FcstDemand > HiMADP Then
'            WorkMAE = HiMADP * p_FcstDemand
'        End If
'    End If
'
'    'Check forecast for a user override
'    If mItemRecord!UserFcst > 0 _
'    And mItemRecord!UserFcstExpDate > PolicyDate _
'    Then
'        Demand = mItemRecord!UserFcst
'        WorkMAE = 0.4 * mItemRecord!UserFcst
'    Else
'        Demand = p_FcstDemand
'    End If
'
'    'Is this a dying item -- If so restrict the Mean Absolute Error
'    If mItemRecord!DIFlag = "Y" _
'    And p_FcstDemand > 0 _
'    Then
'        If (WorkMAE / p_FcstDemand) > DIMADP Then
'            WorkMAE = DIMADP * p_FcstDemand
'        End If
'    End If
'
'    'Buyer strategy -Start
'    If UCase$(mItemRecord!BuyStrat) = "M" _
'    And mItemRecord!UserMax <> 999999999 _
'    Then
'        r_OrderPoint = mItemRecord!UserMin
'        r_OrderQty = mItemRecord!UserMax - mItemRecord!UserMin
'        r_SafetyStock = mItemRecord!UserMin
'
'        'Perform counter stock test
'        If r_SafetyStock < mItemRecord!CStock _
'        And mItemRecord!ZSStock <> "Y" _
'        Then
'            r_SafetyStock = mItemRecord!CStock
'        End If
'
'        'Check zero safety stock switch
'        If mItemRecord!ZSStock = "Y" Then
'            r_SafetyStock = 0
'        End If
'
'    ElseIf UCase$(mItemRecord!BuyStrat) = "T" Then
'        r_OrderPoint = Round((mItemRecord!UserMin * (ForecastLeadTime / mItemRecord!Accum_Lt)) + ForecastRevTime, 0)
'        r_SafetyStock = Round(mItemRecord!UserMin * (ForecastLeadTime / mItemRecord!Accum_Lt), 0)
'
'        'Perform counter stock test
'        If r_SafetyStock < mItemRecord!CStock _
'        And mItemRecord!ZSStock <> "Y" _
'        Then
'            r_SafetyStock = mItemRecord!CStock
'        End If
'
'        'Check zero safety stock switch
'        If mItemRecord!ZSStock = "Y" Then
'            r_SafetyStock = 0
'        End If
'
'        If r_OrderPoint < ForecastLeadTime Then
'            r_OrderPoint = ForecastLeadTime
'        End If
'
'        If mItemRecord!UserMax <> 0 Then
'            r_OrderQty = Round((mItemRecord!UserMax * (ForecastLeadTime / mItemRecord!Accum_Lt)) - r_OrderPoint, 0)
'        Else
'            If p_BkQty(1) = 0 Then
'                r_OrderQty = g_EOQ(p_ReplenCostAll, KFactor, p_FcstDemand, p_Cost)
'            Else
'                r_OrderQty = g_QuantityBreakEOQ(p_FcstDemand, KFactor, p_ReplenCostAll, _
'                    p_BkQty(), p_BkCost())
'            End If
'        End If
'        'Buyer strategy -End
'    Else
'
'        'Calculate Safety Stock
'        r_SafetyStock = mCalc_SafetyStock(ForecastLeadTime + ForecastRevTime, Demand, WorkMAE, _
'            mItemRecord!Accum_Lt + p_ReviewTime, mItemRecord!DSer, MADExK)
'
'        'Apply Intermittent Safety Stock; if applicable
'        If mItemRecord!IsIntermittent = "Y" _
'        And UCase$(Int_Enabled) = "Y" _
'        And IntSafetyStock > CDbl(r_SafetyStock) _
'        Then
'            r_SafetyStock = IntSafetyStock
'        End If
'
'        'Apply Safety Stock Adjustments
'        r_SafetyStock = r_SafetyStock * (1 + mItemRecord!LtvFact) * (1 + mItemRecord!SSAdj)
'
'        'Perform counter stock test
'        If r_SafetyStock < mItemRecord!CStock _
'        And UCase$(mItemRecord!ZSStock) <> "Y" _
'        Then
'            r_SafetyStock = mItemRecord!CStock
'        End If
'
'        'Check zero safety stock switch
'        If mItemRecord!ZSStock = "Y" Then
'            r_SafetyStock = 0
'        End If
'
'        'Calculate the order point; round the results
'        r_OrderPoint = Round(ForecastRevTime + ForecastLeadTime + r_SafetyStock, 0)
'
'        'Calculate the Economic Order Quantity for an item
'        'Check the mItemRecord!ActDate
'        If PolicyDate < (mItemRecord!ActDate - mItemRecord!Accum_Lt) Then
'            r_OrderQty = 0
'        Else
'            If p_BkQty(1) = 0 Then
'                r_OrderQty = g_EOQ(p_ReplenCostAll, KFactor, p_FcstDemand, p_Cost)
'            Else
'                r_OrderQty = g_QuantityBreakEOQ(p_FcstDemand, KFactor, p_ReplenCostAll, _
'                    p_BkQty(), p_BkCost())
'            End If
'        End If
'    End If
'
'   If p_FcstDemand > 0 Then
'        'Test against the high and low turn limits
'        If ((r_OrderQty + ForecastRevTime) / 2 + r_SafetyStock) > 0 Then
'            Turns = (p_FcstDemand * 52) / ((r_OrderQty + ForecastRevTime) / 2 + r_SafetyStock)
'        Else
'            Turns = 0
'        End If
'
'        If Turns > Dft_TurnHigh Then
'            If Dft_TurnHigh <> 0 Then
'                r_OrderQty = 2 * (((p_FcstDemand * 52) / Dft_TurnHigh) - r_SafetyStock) - ForecastRevTime
'            Else
'                r_OrderQty = 2 * (((p_FcstDemand * 52) / 1) - r_SafetyStock) - ForecastRevTime
'            End If
'
'        ElseIf Turns < Dft_TurnLow Then
'            If Dft_TurnLow <> 0 Then
'                r_OrderQty = 2 * (((p_FcstDemand * 52) / Dft_TurnLow) - r_SafetyStock) - ForecastRevTime
'            Else
'                r_OrderQty = 2 * (((p_FcstDemand * 52) / 1) - r_SafetyStock) - ForecastRevTime
'            End If
'
'        End If
'
'    Else
'        If UCase$(mItemRecord!BuyStrat) <> "M" _
'        And UCase$(mItemRecord!BuyStrat) <> "T" _
'        Then
'            r_OrderQty = 0
'        End If
'    End If
'
'    'Test for Maximum Order Quantity (1 year)
'    If r_OrderQty > p_FcstDemand * 52 _
'    And UCase$(mItemRecord!BuyStrat) <> "M" _
'    And UCase$(mItemRecord!BuyStrat) <> "T" Then
'        r_OrderQty = p_FcstDemand * 52
'    End If
'
'    'Test for Minimum Order Quantity (1 unit)
'    If r_OrderQty < 1 And r_OrderQty > 0 Then
'        r_OrderQty = 1
'    End If
'
''    AIMJobLog "mCalc_OrderPolicyUpdate", 0, "OrderQty: " & CStr(r_OrderQty) & "; OrderPoint: " & CStr(r_OrderPoint) & "; SafetyStock: " & CStr(r_SafetyStock)
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_OrderPolicyUpdate)"
'End Function
'
'
''**********************************************************************
''DERIVED FROM AIMADVFORECAST.CalcSafetyStock
'' Function Name:    mCalc_SafetyStock(FcstLT, FcstDemand, MAE, LeadTime,
''                   ServiceLvl, MADExk)
'' Description:      Calculates Safety Stock for an item based on the
''                   replenishment lead time, deseasonalized demand,
''                   Mean Absolute Deviation -- Safety Stock, and the
''                   desired Service Level.
'' Parameters:       FcstLT              Forecast for Lead Time
''                   FcstDemand          Forecast for One Period
''                   MAE                 Mean Absolute Error
''                   LeadTime            Accumulative Lead Time in Days
''                   ServiceLvl          Service Level
''                   MADExk              Mean Absolute Error Extrapolation Constant
'' Returns:          Safety Stock Quantity
'' By: RES          Date: 09/23/1999
''    Calculate Safety Stock
''     [Average Weekly Forecast for the Lead Time Period] * [SafetyFactor(ServiceLvl)]
''       * [Mean Absolute Error] / [Weekly Forecast Demand] * 1.25
''       * SquareRoot([LeadTime / 7])
''
''    The Safety Factor is the number of standard deviation required to achieve a Desired
''    Service Level
''
''    The SquareRoot of the [Lead Time] / 7 adjusts safety stock for the difference in length between
''    the forecast period and the lead time. Intuitively as lead time increased, the proportional
''    requirement for safety stock diminishes. For example, a three week lead time would adjust safety
''    stock by SquareRoot(3) or 1.732 rather than 3. A ten week lead time would adjust safety stock
''    by 3.162 rather than 10. Conversely very short lead times would result in a proportionally higher
''    safety stock level. For example, a lead time of 2 days would adjust safety stock by .535 rather
''    than the 2/7 or .286.
''
''    Key factors in safety stock:
''     Forecast Demand
''     Mean Absolute Error
''     Desired Service Level
''     Lead Time
''**********************************************************************
'Private Function mCalc_SafetyStock( _
'    FcstLT As Double, _
'    p_FcstDemand As Double, _
'    MAE As Double, _
'    LeadTime As Long, _
'    ServiceLvl As Double, _
'    MADExK As Double _
') As Long
'On Error GoTo ErrorHandler
'
'    'Check for a zero deseasonalized demand or a
'    'zero lead time
'    If p_FcstDemand <= 0 Or LeadTime <= 0 Then
'        mCalc_SafetyStock = 0
'        Exit Function
'    End If
'
'    mCalc_SafetyStock = ((FcstLT * 7) / LeadTime) _
'                    * mCalc_SafetyFactor(ServiceLvl) _
'                    * ((MAE / p_FcstDemand) * 1.25) _
'                    * ((LeadTime / 7) ^ MADExK)
'
'    'AIM 3.2 Calculation
'    'SafetyStock = mCalc_SafetyFactor(ServiceLvl) _
'                    * ((LTForecast / LT * FcstInterval) _
'                    * (MADSS / dd * 1.25)) _
'                    * ((LT / FcstInterval) ^ 0.75)
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_SafetyStock)"
'End Function
'
''**********************************************************************
''DERIVED FROM AIMADVFORECAST.SafetyFactor
'' Function Name:    mCalc_SafetyFactor(ServiceFunction)
'' Description:      Calculates an approximation of the Inverse Cumulative
''                   Normal Distribution based on an algorith from Abramowitz
''                   and Stegun.
'' Parameters:       ServiceLvl      Service Level
'' Returns:          Safety Factor
'' By: RES           Date: 02/22/2001
'' Copyright (c) Technology Advantage, Alpharetta, GA  1999 - 2001
''**********************************************************************
'Private Function mCalc_SafetyFactor( _
'    ByVal ServiceLvl As Double _
') As Double
'On Error GoTo ErrorHandler
'
'    Dim t As Double
'
'    Select Case ServiceLvl
'    Case Is < 0.0000002867
'        mCalc_SafetyFactor = -5
'
'    Case Is > 0.9999997133
'        mCalc_SafetyFactor = 5
'
'    Case Is > 0.5
'        ServiceLvl = 1 - ServiceLvl
'        t = Sqr(Log(1 / ServiceLvl ^ 2))
'        mCalc_SafetyFactor = Round(t - ((2.515517 + (0.802853 * t) + (0.010328 * t ^ 2)) _
'                                  / (1 + (1.432788 * t) + (0.189269 * t ^ 2) + (0.001308 * t ^ 3))) _
'                            , 4)
'
'    Case Is <= 0.5
'        t = Sqr(Log(1 / ServiceLvl ^ 2))
'        mCalc_SafetyFactor = -1 * Round(t - ((2.515517 + (0.802853 * t) + (0.010328 * t ^ 2)) _
'                                    / (1 + (1.432788 * t) + (0.189269 * t ^ 2) + (0.001308 * t ^ 3))) _
'                            , 4)
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mCalc_SafetyFactor)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Saves data to the AIMForecastSetup table
''   for the given Forecast ID.
'' .......................................................................
''   Parameters:
''       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
''       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
''       r_rsCompound -- If p_Related was true, then there will be more than one recordset, else
''           there will be just the recordset for AIMForecastSetup.
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Public Function gSave_ForecastSetup( _
'    p_Cn As ADODB.Connection, _
'    ForecastCriteria As FORECAST_CRITERIA, _
'    Optional p_UpdateForRolling As Boolean, Optional p_LastUpdated As Date _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstSetup_Save_Sp As ADODB.Command
'    Dim intCount As Long
'
'    Dim IndexVar As Long
'    Dim IndexCounter As Long
'
'    p_LastUpdated = IIf(Trim$(p_LastUpdated) = "12:00:00 AM", ForecastCriteria.FcstStartDate, p_LastUpdated)
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstSetup_Save_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstSetup_Save_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstSetup_Save_Sp"
'
'        'Set parameters and their values
'        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
'    '-- Conditions
'        .Parameters.Append .CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append .CreateParameter("@UpdateForRolling", adBoolean, adParamInput)
'    '-- Fields
'        .Parameters.Append .CreateParameter("@FcstID", adVarWChar, adParamInput, 12)
'        .Parameters.Append .CreateParameter("@FcstDesc", adVarWChar, adParamInput, 40)
'        .Parameters.Append .CreateParameter("@FcstHierarchy", adTinyInt, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstEnabled", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstLocked", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstStartDate", adDate, adParamInput)
'        .Parameters.Append .CreateParameter("@LastUpdated", adDate, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstRolling", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstHistory", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstPds_Future", adTinyInt, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstPds_Historical", adTinyInt, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstInterval", adTinyInt, adParamInput)
'        .Parameters.Append .CreateParameter("@FcstUnit", adTinyInt, adParamInput)
'        .Parameters.Append .CreateParameter("@ApplyTrend", adTinyInt, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_SysFcst", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_NetRqmt", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_ProjInv", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_HistDmnd", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_MasterFcstAdj", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_FcstAdj", adBoolean, adParamInput)
'        .Parameters.Append .CreateParameter("@Calc_AdjNetReq", adBoolean, adParamInput)
'    '-- Item filter criteria
'        .Parameters.Append .CreateParameter("@Item", adVarWChar, adParamInput, 25)
'        .Parameters.Append .CreateParameter("@ItStat", adVarWChar, adParamInput, 1)
'        .Parameters.Append .CreateParameter("@VnId", adVarWChar, adParamInput, 12)
'        .Parameters.Append .CreateParameter("@Assort", adVarWChar, adParamInput, 12)
'        .Parameters.Append .CreateParameter("@ById", adVarWChar, adParamInput, 12)
'        .Parameters.Append .CreateParameter("@Class1", adVarWChar, adParamInput, 50)
'        .Parameters.Append .CreateParameter("@Class2", adVarWChar, adParamInput, 50)
'        .Parameters.Append .CreateParameter("@Class3", adVarWChar, adParamInput, 50)
'        .Parameters.Append .CreateParameter("@Class4", adVarWChar, adParamInput, 50)
'        .Parameters.Append .CreateParameter("@LcId", adVarWChar, adParamInput, 12)
'        .Parameters.Append .CreateParameter("@LStatus", adVarWChar, adParamInput, 1)
'        .Parameters.Append .CreateParameter("@LDivision", adVarWChar, adParamInput, 20)
'        .Parameters.Append .CreateParameter("@LRegion", adVarWChar, adParamInput, 20)
'        .Parameters.Append .CreateParameter("@LUserDefined", adVarWChar, adParamInput, 30)
'    '-- Return parameters
'        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInputOutput)
'    'Set values
'        '-- Conditions
'        .Parameters("@UserID").Value = gUserID
'        .Parameters("@UpdateForRolling").Value = p_UpdateForRolling
'        '-- Fields
'        .Parameters("@FcstID").Value = ForecastCriteria.FcstId
'        .Parameters("@FcstDesc").Value = ForecastCriteria.FcstDesc
'        .Parameters("@FcstHierarchy").Value = ForecastCriteria.FcstHierarchy
'        .Parameters("@FcstEnabled").Value = ForecastCriteria.FcstEnabled
'        .Parameters("@FcstLocked").Value = ForecastCriteria.FcstLocked
'        .Parameters("@FcstStartDate").Value = ForecastCriteria.FcstStartDate
'        .Parameters("@LastUpdated").Value = p_LastUpdated
'        .Parameters("@FcstRolling").Value = ForecastCriteria.FcstRolling
'        .Parameters("@FcstHistory").Value = ForecastCriteria.FcstHistory
'        .Parameters("@FcstPds_Future").Value = ForecastCriteria.FcstPeriods_Future
'        .Parameters("@FcstPds_Historical").Value = ForecastCriteria.FcstPeriods_Historical
'        .Parameters("@FcstInterval").Value = ForecastCriteria.FcstInterval
'        .Parameters("@FcstUnit").Value = ForecastCriteria.FcstUnit
'        .Parameters("@ApplyTrend").Value = ForecastCriteria.ApplyTrend
'        .Parameters("@Calc_SysFcst").Value = ForecastCriteria.Calc_SysFcst
'        .Parameters("@Calc_NetRqmt").Value = ForecastCriteria.Calc_Netreq
'        .Parameters("@Calc_ProjInv").Value = ForecastCriteria.Calc_ProjInv
'        .Parameters("@Calc_HistDmnd").Value = ForecastCriteria.Calc_HistDmd
'        .Parameters("@Calc_MasterFcstAdj").Value = ForecastCriteria.Calc_MasterFcstAdj
'        .Parameters("@Calc_FcstAdj").Value = ForecastCriteria.Calc_FcstAdj
'        .Parameters("@Calc_AdjNetReq").Value = ForecastCriteria.Calc_AdjNetReq
'        '-- Item filter criteria
'        .Parameters("@Item").Value = ForecastCriteria.Item
'        .Parameters("@ItStat").Value = ForecastCriteria.ItStat
'        .Parameters("@VnId").Value = ForecastCriteria.VnId
'        .Parameters("@Assort").Value = ForecastCriteria.Assort
'        .Parameters("@ById").Value = ForecastCriteria.ById
'        .Parameters("@Class1").Value = ForecastCriteria.Class1
'        .Parameters("@Class2").Value = ForecastCriteria.Class2
'        .Parameters("@Class3").Value = ForecastCriteria.Class3
'        .Parameters("@Class4").Value = ForecastCriteria.Class4
'        .Parameters("@LcId").Value = ForecastCriteria.LcId
'        .Parameters("@LStatus").Value = ForecastCriteria.LStatus
'        .Parameters("@LDivision").Value = ForecastCriteria.LDivision
'        .Parameters("@LRegion").Value = ForecastCriteria.LRegion
'        .Parameters("@LUserDefined").Value = ForecastCriteria.LUserDefined
'        '-- Return parameters
'        '.Parameters("@FcstSetupKey").Value = ForecastCriteria.FcstSetupKey '  commented out since this is giving error sri Aug-16-04
'        .Parameters("@FcstSetupKey").Value = 0 ' Added this line in place of the abouve line sri
'    '-- Save
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlanFcstSetup_Save_Sp.Parameters(0).Value
'
'    If RtnCode = 1 Then
'        ForecastCriteria.FcstSetupKey = AIM_DmdPlanFcstSetup_Save_Sp.Parameters("@FcstSetupKey").Value
'        'Set return value
'        gSave_ForecastSetup = RtnCode
'    End If
'
'    If Not (AIM_DmdPlanFcstSetup_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstSetup_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstSetup_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlanFcstSetup_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstSetup_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstSetup_Save_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(gSave_ForecastSetup)"
'End Function
'
'Public Function gSave_ForecastStore(p_Cn As ADODB.Connection, _
'    p_UserElement As Boolean, p_FcstSetupKey As Long, p_FcstStoreID As String, _
'    p_ForecastCriteria As FORECAST_CRITERIA, _
'    p_ForecastData As FORECAST_DATA, _
'    Optional p_FcstComment As String _
') As Long
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstStore_Save_Sp As ADODB.Command
'    Dim AIM_DmdPlanFcstComment_Save_Sp As ADODB.Command
'    Dim FcstStoreKey As Long
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstStore_Save_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstStore_Save_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstStore_Save_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanFcstStore_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstStore_Save_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstStore_Save_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStore_Save_Sp.CreateParameter("@FcstStoreID", adVarWChar, adParamInput, 50)
'        .Parameters.Append AIM_DmdPlanFcstStore_Save_Sp.CreateParameter("@UserElement", adBoolean, adParamInput)
'    'Output
'        .Parameters.Append AIM_DmdPlanFcstStore_Save_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamOutput)
'    End With
'
'    With AIM_DmdPlanFcstStore_Save_Sp
'        'Set values
'        .Parameters("@UserID").Value = gUserID
'        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
'        .Parameters("@FcstStoreID").Value = p_FcstStoreID
'        .Parameters("@UserElement").Value = p_UserElement
'        'Output
'        .Parameters("@FcstStoreKey").Value = Null
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlanFcstStore_Save_Sp.Parameters(0).Value
'    If RtnCode > 0 Then
'
'        FcstStoreKey = AIM_DmdPlanFcstStore_Save_Sp.Parameters("@FcstStoreKey").Value
'
'        If Trim$(p_FcstComment) <> "" Then
'            'save fcstcomment
'            'Init. the command object
'            Set AIM_DmdPlanFcstComment_Save_Sp = New ADODB.Command
'            With AIM_DmdPlanFcstComment_Save_Sp
'                Set .ActiveConnection = p_Cn
'                .CommandType = adCmdStoredProc
'                .CommandText = "AIM_DmdPlanFcstComment_Save_Sp"
'            'Set parameters and their values
'                .Parameters.Append AIM_DmdPlanFcstComment_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'            'Input
'                .Parameters.Append AIM_DmdPlanFcstComment_Save_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamOutput)
'                .Parameters.Append AIM_DmdPlanFcstComment_Save_Sp.CreateParameter("@FcstComment", adVarWChar, adParamInput, 4000)
'            End With
'
'            'start a loop to save off the comment in chunks         'TO DO - chunk-hack to fit ntext
'                With AIM_DmdPlanFcstComment_Save_Sp
'                'Set values
'                    .Parameters("@FcstStoreKey").Value = FcstStoreKey
'                    .Parameters("@FcstComment").Value = p_FcstComment
'                'Fetch results
'                    .Execute
'                End With
'            'end save loop
'            If Not (AIM_DmdPlanFcstComment_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstComment_Save_Sp.ActiveConnection = Nothing
'            Set AIM_DmdPlanFcstComment_Save_Sp = Nothing
'        End If
'
''       If p_UserElement = False Then
'        'Check to see that when userelement is true, this saves data from p_ForecastData into a new record associated with the user element's key
'            RtnCode = gSave_ForecastStoreDetail(p_Cn, FcstStoreKey, p_ForecastCriteria, p_ForecastData)
''        Else
''
''        End If
'    End If
'
'    gSave_ForecastStore = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstStore_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStore_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStore_Save_Sp = Nothing
'    If Not (AIM_DmdPlanFcstComment_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstComment_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstComment_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlanFcstStore_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStore_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStore_Save_Sp = Nothing
'    If Not (AIM_DmdPlanFcstComment_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstComment_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstComment_Save_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(gSave_ForecastStore)"
'End Function
'
'Public Function gSave_ForecastStoreDetail( _
'    p_Cn As ADODB.Connection, _
'    FcstStoreKey As Long, _
'    ForecastCriteria As FORECAST_CRITERIA, _
'    ForecastData As FORECAST_DATA _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstStoreDetail_Save_Sp As ADODB.Command
'    Dim ItemCount As Long
'    Dim PeriodCount As Long
'    Dim RowIdx As Long, ColIdx As Long, CalcIdx As Long
'    Dim tempInt As Long
'    Dim FYPeriod As Long
'    Dim tempDate As Date
'
'    ItemCount = UBound(ForecastData.PeriodStartDate, 1)
'    PeriodCount = UBound(ForecastData.PeriodStartDate, 2)
'
'    Set AIM_DmdPlanFcstStoreDetail_Save_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstStoreDetail_Save_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstStoreDetail_Save_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'    'Conditions
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamInput)
'    'Fields
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@PeriodStartDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@DataCalc_Type", adTinyInt, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@DataCalc_Value", adDecimal, adParamInput)
'            .Parameters("@DataCalc_Value").Precision = 10
'            .Parameters("@DataCalc_Value").NumericScale = 2
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@PeriodEndDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreDetail_Save_Sp.CreateParameter("@TargetPeriod", adInteger, adParamInput)
'
'    End With
'
'    With AIM_DmdPlanFcstStoreDetail_Save_Sp
'        For RowIdx = 1 To ItemCount
'            .Parameters("@UserID").Value = gUserID
'            .Parameters("@FcstSetupKey").Value = ForecastCriteria.FcstSetupKey
'            .Parameters("@FcstStoreKey").Value = FcstStoreKey
'
'            .Parameters("@LcID").Value = ForecastData.LcId(RowIdx)
'            .Parameters("@Item").Value = ForecastData.Item(RowIdx)
'            For ColIdx = 1 To PeriodCount
'                .Parameters("@PeriodStartDate").Value = ForecastData.PeriodStartDate(RowIdx, ColIdx)
'                .Parameters("@PeriodEndDate").Value = ForecastData.PeriodEndDate(RowIdx, ColIdx)
'                RtnCode = gCalc_AIMCalendar(p_Cn, tempDate, FYPeriod, CA_PERIOD, ForecastCriteria.FcstInterval, ForecastData.PeriodStartDate(RowIdx, ColIdx))
'                .Parameters("@TargetPeriod").Value = FYPeriod
'                For CalcIdx = 1 To DATA_CALC_TYPE.DCT_MAXVALUE - 1
'                    .Parameters("@DataCalc_Type").Value = CalcIdx
'                    .Parameters("@DataCalc_Value").Value = ForecastData.DataCalc_Value(RowIdx, ColIdx, CalcIdx)
'                    'Insert/Update
'                    .Execute
'                Next CalcIdx
'            Next ColIdx 'PeriodCount
'
'            RtnCode = AIM_DmdPlanFcstStoreDetail_Save_Sp.Parameters(0).Value
'        Next RowIdx 'ItemCount
'    End With
'    'Check return code -- 0 = fail; 1 = succeed
'    If Not (AIM_DmdPlanFcstStoreDetail_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStoreDetail_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStoreDetail_Save_Sp = Nothing
'
'    gSave_ForecastStoreDetail = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstStoreDetail_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStoreDetail_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStoreDetail_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlanFcstStoreDetail_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStoreDetail_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStoreDetail_Save_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(gSave_ForecastStoreDetail)"
'End Function
'
'Public Function gFetch_ForecastAdjustments( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, p_LcID As String, p_Item As String, _
'    p_AdjustStartDate As Date, p_AdjustEndDate As Date, _
'    r_rsForecastAdjustments As ADODB.Recordset _
' ) As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlan_Adjustments_Fetch_Sp As ADODB.Command
'    Dim RowCount As Long
'    Dim RowIdx As Long
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'    Dim rsCompound As ADODB.Recordset
'    Dim RSCounter As Long, ColCounter As Long, RowCounter As Long
'
'    'Clear out any existing data
'    If f_IsRecordsetValidAndOpen(r_rsForecastAdjustments) Then r_rsForecastAdjustments.Close
'    Set r_rsForecastAdjustments = Nothing
'
'    'Init. the command object
'    Set AIM_DmdPlan_Adjustments_Fetch_Sp = New ADODB.Command
'    With AIM_DmdPlan_Adjustments_Fetch_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlan_Adjustments_Fetch_Sp"
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@FcstId", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@AdjustBegDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@AdjustEndDate", adDBDate, adParamInput)
'    'Set values
'        .Parameters("@FcstId").Value = p_FcstId
'        .Parameters("@LcID").Value = IIf(Trim$(p_LcID) = "", Null, Trim$(p_LcID))
'        .Parameters("@Item").Value = IIf(Trim$(p_Item) = "", Null, Trim$(p_Item))
'        .Parameters("@AdjustBegDate").Value = IIf(Trim$(p_AdjustStartDate) = "12:00:00 AM", Null, Trim$(Format(p_AdjustStartDate, g_ISO_DATE_FORMAT)))
'        .Parameters("@AdjustEndDate").Value = IIf(Trim$(p_AdjustEndDate) = "12:00:00 AM", Null, Trim$(Format(p_AdjustEndDate, g_ISO_DATE_FORMAT)))
'    End With
'
'    'Init. the recordset
'    Set rsCompound = New ADODB.Recordset
'    With rsCompound
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
'    rsCompound.Open AIM_DmdPlan_Adjustments_Fetch_Sp
'
'    RtnCode = AIM_DmdPlan_Adjustments_Fetch_Sp.Parameters(0).Value
'    'Check return code -- 0 = fail; 1 = succeed
'    If RtnCode <= 0 Then
'        'FAIL
'        GoTo CleanUp
'        Exit Function
'    End If
'    If f_IsRecordsetValidAndOpen(rsCompound) Then
'        'Associate as a disconnected recordset
'        Set r_rsForecastAdjustments = New ADODB.Recordset
'        r_rsForecastAdjustments.Fields.Append "LcID", adVarWChar, 12
'        r_rsForecastAdjustments.Fields.Append "Item", adVarWChar, 25
'        r_rsForecastAdjustments.Fields.Append "AdjustType", adInteger
'        r_rsForecastAdjustments.Fields.Append "AdjustQty", adDecimal
'        r_rsForecastAdjustments.Fields("AdjustQty").Precision = 10
'        r_rsForecastAdjustments.Fields("AdjustQty").NumericScale = 2
'        r_rsForecastAdjustments.Fields.Append "AdjustUserID", adVarWChar, 12
'        r_rsForecastAdjustments.Fields.Append "AdjustDesc", adVarWChar, 255
'        r_rsForecastAdjustments.Fields.Append "AdjustDateTime", adDBDate
'        r_rsForecastAdjustments.Fields.Append "AdjustBegDate", adDBDate
'        r_rsForecastAdjustments.Fields.Append "AdjustEndDate", adDBDate
'        'Populate
'        r_rsForecastAdjustments.Open
'
'        RSCounter = 1
'        Do Until rsCompound Is Nothing
'            Do Until rsCompound.eof
'                'Set display to match recordset(s)
'                With r_rsForecastAdjustments
'                    .AddNew
'                    For ColCounter = 0 To rsCompound.Fields.Count - 1
'                        .Fields(ColCounter) = rsCompound.Fields(ColCounter)
'                    Next
'                End With
'                rsCompound.MoveNext 'There should be no more for the first recordset
'            Loop
'
'            Set rsCompound = rsCompound.NextRecordset
'            RSCounter = RSCounter + 1
'        Loop
'    Else
'        RtnCode = -1
'    End If
'
'CleanUp:
'    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
'    Set rsCompound = Nothing
'
'    If Not (AIM_DmdPlan_Adjustments_Fetch_Sp Is Nothing) Then Set AIM_DmdPlan_Adjustments_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_Adjustments_Fetch_Sp = Nothing
'
'    If f_IsRecordsetOpenAndPopulated(r_rsForecastAdjustments) Then
'        RtnCode = 1
'    Else
'        RtnCode = -1
'    End If
'    gFetch_ForecastAdjustments = RtnCode
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    If Not (AIM_DmdPlan_Adjustments_Fetch_Sp Is Nothing) Then Set AIM_DmdPlan_Adjustments_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlan_Adjustments_Fetch_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
'    Set rsCompound = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(gFetch_ForecastAdjustments)"
'End Function
'Public Function gFetch_OverRideItems( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, _
'    r_rsOverRideItems As ADODB.Recordset _
' ) As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_RepositoryOverRide_Get_Sp As ADODB.Command
'
'    'Clear out any existing data
'    If f_IsRecordsetValidAndOpen(r_rsOverRideItems) Then r_rsOverRideItems.Close
'    Set r_rsOverRideItems = Nothing
'
'    'Init. the command object
'    Set AIM_RepositoryOverRide_Get_Sp = New ADODB.Command
'    With AIM_RepositoryOverRide_Get_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_RepositoryOverRide_Get_Sp"
'    'Declare parameters
'        .Parameters.Append AIM_RepositoryOverRide_Get_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_RepositoryOverRide_Get_Sp.CreateParameter("@FcstId", adVarWChar, adParamInput, 12)
'    'Set values
'        .Parameters("@FcstId").Value = p_FcstId
'    End With
'
'    'Init. the recordset
'    Set r_rsOverRideItems = New ADODB.Recordset
'    With r_rsOverRideItems
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
'    r_rsOverRideItems.Open AIM_RepositoryOverRide_Get_Sp
'
'    RtnCode = AIM_RepositoryOverRide_Get_Sp.Parameters(0).Value
'
'
'CleanUp:
'    If Not (AIM_RepositoryOverRide_Get_Sp Is Nothing) Then Set AIM_RepositoryOverRide_Get_Sp.ActiveConnection = Nothing
'    Set AIM_RepositoryOverRide_Get_Sp = Nothing
'
'    If f_IsRecordsetOpenAndPopulated(r_rsOverRideItems) Then
'        RtnCode = 1
'    Else
'        RtnCode = -1
'    End If
'    gFetch_OverRideItems = RtnCode
'
'Exit Function
'ErrorHandler:
'
'   If Not (AIM_RepositoryOverRide_Get_Sp Is Nothing) Then Set AIM_RepositoryOverRide_Get_Sp.ActiveConnection = Nothing
'    Set AIM_RepositoryOverRide_Get_Sp = Nothing
'
'    If f_IsRecordsetValidAndOpen(r_rsOverRideItems) Then r_rsOverRideItems.Close
'    Set r_rsOverRideItems = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_OverRideItems)"
'End Function
'
'Public Function gFetch_ItemAdjustments( _
'    p_Cn As ADODB.Connection, _
'    p_FcstId As String, _
'    p_LcID As String, _
'    p_Item As String, _
'    r_rsItemAdj As ADODB.Recordset _
' ) As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_RepositoryItemAdj_Get_Sp As ADODB.Command
'
'    'Clear out any existing data
'    If f_IsRecordsetValidAndOpen(r_rsItemAdj) Then r_rsItemAdj.Close
'    Set r_rsItemAdj = Nothing
'
'    'Init. the command object
'    Set AIM_RepositoryItemAdj_Get_Sp = New ADODB.Command
'    With AIM_RepositoryItemAdj_Get_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_RepositoryItemAdj_Get_Sp"
'    'Declare parameters
'        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("@FcstId", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("@Lcid", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'    'Set values
'        .Parameters("@FcstId").Value = p_FcstId
'        .Parameters("@Lcid").Value = p_LcID
'        .Parameters("@Item").Value = p_Item
'    End With
'
'    'Init. the recordset
'    Set r_rsItemAdj = New ADODB.Recordset
'    With r_rsItemAdj
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
'    r_rsItemAdj.Open AIM_RepositoryItemAdj_Get_Sp
'
'    RtnCode = AIM_RepositoryItemAdj_Get_Sp.Parameters(0).Value
'
'
'CleanUp:
'    If Not (AIM_RepositoryItemAdj_Get_Sp Is Nothing) Then Set AIM_RepositoryItemAdj_Get_Sp.ActiveConnection = Nothing
'    Set AIM_RepositoryItemAdj_Get_Sp = Nothing
'
'    If f_IsRecordsetOpenAndPopulated(r_rsItemAdj) Then
'        RtnCode = 1
'    Else
'        RtnCode = -1
'    End If
'    gFetch_ItemAdjustments = RtnCode
'
'Exit Function
'ErrorHandler:
'
'   If Not (AIM_RepositoryItemAdj_Get_Sp Is Nothing) Then Set AIM_RepositoryItemAdj_Get_Sp.ActiveConnection = Nothing
'    Set AIM_RepositoryItemAdj_Get_Sp = Nothing
'
'    If f_IsRecordsetValidAndOpen(r_rsItemAdj) Then r_rsItemAdj.Close
'    Set r_rsItemAdj = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_ItemAdjustments)"
'End Function
'
'
'
'Public Function gSave_Adjustments( _
'    p_Cn As ADODB.Connection, _
'    p_RepositoryKey As String, p_FcstStoreKey As Long, _
'    p_LcID As String, p_Item As String, _
'    p_AdjustStartDate As Date, p_AdjustEndDate As Date, _
'    p_AdjustType As Long, p_AdjustQty As Double, _
'    p_OverRideEnabled As Integer, _
'    p_AdjustDesc As String, p_AdjustUserID As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstStoreAdjustLog_Save_Sp As ADODB.Command
'
'    Dim DBugCounter As Long
'    Dim DBugText As String
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'
'    Dim IndexCounter As Long
'    Dim RowCount As Long
'    Dim RowIdx As Long
'    Dim AddItemString As String
'    Dim BkMark As Variant
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstStoreAdjustLog_Save_Sp"
'
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@RepositoryKey", adVarWChar, adParamInput, 12)
''        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustType", adTinyInt, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustQty", adDecimal, adParamInput)
'        .Parameters("@AdjustQty").Precision = 10
'        .Parameters("@AdjustQty").NumericScale = 2
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@OverRideEnabled", adTinyInt, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustBegDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustEndDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustUserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustDesc", adVarWChar, adParamInput, 255)
'    'Set values
'        .Parameters("@RepositoryKey").Value = p_RepositoryKey
'       ' .Parameters("@FcstStoreKey").Value = p_FcstStoreKey
'        .Parameters("@LcID").Value = Trim$(p_LcID)
'        .Parameters("@Item").Value = IIf(Trim$(p_Item) = "", Null, Trim$(p_Item))
'        .Parameters("@AdjustType").Value = p_AdjustType
'        .Parameters("@AdjustQty").Value = p_AdjustQty
'        .Parameters("@OverRideEnabled").Value = p_OverRideEnabled
'        .Parameters("@AdjustBegDate").Value = Format(p_AdjustStartDate, g_ISO_DATE_FORMAT)
'        .Parameters("@AdjustEndDate").Value = Format(p_AdjustEndDate, g_ISO_DATE_FORMAT)
'        .Parameters("@AdjustUserID").Value = p_AdjustUserID
'        .Parameters("@AdjustDesc").Value = Trim$(p_AdjustDesc)
'    'Run
'        .Execute
'    End With
'
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.Parameters(0).Value
'
'    'Set return value
'    gSave_Adjustments = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstStoreAdjustLog_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    If Not (AIM_DmdPlanFcstStoreAdjustLog_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstAdjustments_Fetch)"
'End Function
'
'Private Function AIMJobLog( _
'    p_LogSource As String _
'    , p_LogSeverity As Long _
'    , p_LogMessage As String _
'    , Optional p_LogOutputFile As String _
'    , Optional p_LogMsgVariables As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Debug.Print Trim$(p_LogSource) & "; " & CStr(p_LogSeverity) & "; " & Trim$(p_LogMessage) & "; " & Trim$(p_LogMsgVariables)
'
'
''    With AIM_JobLog_Insert_Sp
''    'Input
''        .Parameters("@LogJobType").Value = "Demand Plan"
''        .Parameters("@LogSource").Value = p_LogSource
''        .Parameters("@LogSeverity").Value = p_LogSeverity
''        .Parameters("@LogMessage").Value = p_LogMessage
''        .Parameters("@LogOutputFile").Value = p_LogOutputFile
''        .Parameters("@LogMsgVariables").Value = p_LogMsgVariables
''        .Parameters("@JobLogID").Value = mLogID
''
''        .Execute
''    End With
''
''    AIMJobLog = AIM_JobLog_Insert_Sp.Parameters(0).Value
''    mLogID = AIM_JobLog_Insert_Sp.Parameters("@JobLogID").Value
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMJobLog)"
'End Function
'
'Public Function gCheckUserAccess( _
'    p_Cn As ADODB.Connection, _
'    p_FcstSetupKey As Long, _
'    p_UserID As String, _
'    p_AccessCode As FCST_ACCESS _
') As Boolean
'On Error GoTo ErrorHandler
'
'    Dim AIM_DmdPlanFcstUser_CheckAccess_Sp As ADODB.Command
'    Dim RtnCode As Long
'
'    Set AIM_DmdPlanFcstUser_CheckAccess_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstUser_CheckAccess_Sp
'        Set .ActiveConnection = p_Cn
'        .CommandTimeout = 0
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstUser_CheckAccess_Sp"
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@AccessCode", adInteger, adParamInput)
'    'Output
'        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@HasAccess", adBoolean, adParamOutput)
'
'    'SEt values
'        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
'        .Parameters("@UserID").Value = p_UserID
'        .Parameters("@AccessCode").Value = p_AccessCode
'        .Parameters("@HasAccess").Value = Null
'
'        .Execute
'    End With
'
'    RtnCode = AIM_DmdPlanFcstUser_CheckAccess_Sp(0).Value
'    If RtnCode > 0 Then
'        gCheckUserAccess = AIM_DmdPlanFcstUser_CheckAccess_Sp.Parameters("@HasAccess").Value
'    Else
'        gCheckUserAccess = False
'    End If
'
'    If Not (AIM_DmdPlanFcstUser_CheckAccess_Sp Is Nothing) Then Set AIM_DmdPlanFcstUser_CheckAccess_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstUser_CheckAccess_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlanFcstUser_CheckAccess_Sp Is Nothing) Then Set AIM_DmdPlanFcstUser_CheckAccess_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstUser_CheckAccess_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(gCheckUserAccess  )"
'End Function
