VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ForecastMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Forecast Maintenance"
   ClientHeight    =   9225
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11010
   Icon            =   "AIM_ForecastMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   9225
   ScaleWidth      =   11010
   ShowInTaskbar   =   0   'False
   Begin ActiveTabs.SSActiveTabs atForecastCriteria 
      Height          =   7680
      Left            =   45
      TabIndex        =   5
      Top             =   1080
      Width           =   10830
      _ExtentX        =   19103
      _ExtentY        =   13547
      _Version        =   131083
      TabCount        =   4
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TagVariant      =   ""
      Tabs            =   "AIM_ForecastMaintenance.frx":030A
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   7290
         Left            =   -99969
         TabIndex        =   98
         Top             =   360
         Width           =   10770
         _ExtentX        =   18997
         _ExtentY        =   12859
         _Version        =   131083
         TabGuid         =   "AIM_ForecastMaintenance.frx":041F
         Begin VB.Frame fraForecastFilters 
            Height          =   3735
            Left            =   120
            TabIndex        =   53
            Top             =   0
            Width           =   10575
            Begin TDBText6Ctl.TDBText txtVnId 
               Height          =   345
               Left            =   2865
               TabIndex        =   58
               TabStop         =   0   'False
               Top             =   1004
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0447
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":04B3
               Key             =   "AIM_ForecastMaintenance.frx":04D1
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAssort 
               Height          =   345
               Left            =   2865
               TabIndex        =   60
               TabStop         =   0   'False
               Top             =   1386
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0515
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0581
               Key             =   "AIM_ForecastMaintenance.frx":059F
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLcId 
               Height          =   345
               Left            =   7905
               TabIndex        =   55
               TabStop         =   0   'False
               Top             =   240
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":05E3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":064F
               Key             =   "AIM_ForecastMaintenance.frx":066D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItem 
               Height          =   345
               Left            =   2865
               TabIndex        =   54
               TabStop         =   0   'False
               Top             =   240
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":06B1
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":071D
               Key             =   "AIM_ForecastMaintenance.frx":073B
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   25
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtById 
               Height          =   345
               Left            =   2865
               TabIndex        =   62
               TabStop         =   0   'False
               Top             =   1768
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":077F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":07EB
               Key             =   "AIM_ForecastMaintenance.frx":0809
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   1
               Left            =   2865
               TabIndex        =   64
               TabStop         =   0   'False
               Top             =   2150
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":084D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":08B9
               Key             =   "AIM_ForecastMaintenance.frx":08D7
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   2
               Left            =   2865
               TabIndex        =   66
               TabStop         =   0   'False
               Top             =   2533
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":091B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0987
               Key             =   "AIM_ForecastMaintenance.frx":09A5
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   3
               Left            =   2880
               TabIndex        =   68
               TabStop         =   0   'False
               Top             =   2916
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":09E9
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0A55
               Key             =   "AIM_ForecastMaintenance.frx":0A73
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItemStatus 
               Height          =   345
               Left            =   2865
               TabIndex        =   56
               TabStop         =   0   'False
               Top             =   622
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0AB7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0B23
               Key             =   "AIM_ForecastMaintenance.frx":0B41
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   1
               Left            =   4860
               TabIndex        =   65
               TabStop         =   0   'False
               Top             =   2145
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0B85
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0BF1
               Key             =   "AIM_ForecastMaintenance.frx":0C0F
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   2
               Left            =   4860
               TabIndex        =   67
               TabStop         =   0   'False
               Top             =   2535
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0C53
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0CBF
               Key             =   "AIM_ForecastMaintenance.frx":0CDD
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   3
               Left            =   4860
               TabIndex        =   69
               TabStop         =   0   'False
               Top             =   2910
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0D21
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0D8D
               Key             =   "AIM_ForecastMaintenance.frx":0DAB
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLDivision 
               Height          =   345
               Left            =   7905
               TabIndex        =   59
               Top             =   1004
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0DEF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0E5B
               Key             =   "AIM_ForecastMaintenance.frx":0E79
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLUserDefined 
               Height          =   345
               Left            =   7905
               TabIndex        =   63
               Top             =   1768
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0EBD
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0F29
               Key             =   "AIM_ForecastMaintenance.frx":0F47
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLStatus 
               Height          =   345
               Left            =   7905
               TabIndex        =   57
               Top             =   622
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":0F8B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":0FF7
               Key             =   "AIM_ForecastMaintenance.frx":1015
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   4
               Left            =   2880
               TabIndex        =   70
               TabStop         =   0   'False
               Top             =   3300
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":1059
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":10C5
               Key             =   "AIM_ForecastMaintenance.frx":10E3
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   4
               Left            =   4860
               TabIndex        =   71
               TabStop         =   0   'False
               Top             =   3300
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":1127
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1193
               Key             =   "AIM_ForecastMaintenance.frx":11B1
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLRegion 
               Height          =   345
               Left            =   7905
               TabIndex        =   61
               Top             =   1386
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":11F5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1261
               Key             =   "AIM_ForecastMaintenance.frx":127F
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 4"
               Height          =   300
               Index           =   9
               Left            =   120
               TabIndex        =   112
               Top             =   3345
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 3"
               Height          =   300
               Index           =   8
               Left            =   120
               TabIndex        =   111
               Top             =   2959
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 2"
               Height          =   300
               Index           =   7
               Left            =   120
               TabIndex        =   110
               Top             =   2577
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 1"
               Height          =   300
               Index           =   6
               Left            =   120
               TabIndex        =   109
               Top             =   2195
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Item"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   108
               Top             =   285
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Buyer ID"
               Height          =   300
               Index           =   5
               Left            =   120
               TabIndex        =   107
               Top             =   1853
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Vendor ID"
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   106
               Top             =   1069
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Assortment"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   105
               Top             =   1461
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Location ID"
               Height          =   300
               Index           =   4
               Left            =   5160
               TabIndex        =   104
               Top             =   285
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Item Status"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   103
               Top             =   677
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "User-defined"
               Height          =   300
               Index           =   5
               Left            =   5160
               TabIndex        =   102
               Top             =   1813
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Region"
               Height          =   300
               Index           =   4
               Left            =   5160
               TabIndex        =   101
               Top             =   1431
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Division"
               Height          =   300
               Index           =   3
               Left            =   5160
               TabIndex        =   100
               Top             =   1049
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Status"
               Height          =   300
               Index           =   1
               Left            =   5160
               TabIndex        =   99
               Top             =   667
               Width           =   2595
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpForecastHeader 
         Height          =   7290
         Left            =   30
         TabIndex        =   86
         Top             =   360
         Width           =   10770
         _ExtentX        =   18997
         _ExtentY        =   12859
         _Version        =   131083
         TabGuid         =   "AIM_ForecastMaintenance.frx":12C3
         Begin VB.Frame fraForecastCriteria 
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4800
            Index           =   0
            Left            =   105
            TabIndex        =   6
            Top             =   30
            Width           =   10575
            Begin VB.Frame fraForecastInterval 
               Caption         =   "Forecast Interval"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   3360
               Left            =   180
               TabIndex        =   11
               Top             =   1200
               Width           =   1935
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "Days"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   12
                  Top             =   212
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "Weeks"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   13
                  Top             =   586
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "Months"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   14
                  Top             =   960
                  Value           =   -1  'True
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "5-4-4"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   4
                  Left            =   120
                  TabIndex        =   16
                  Top             =   1708
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "4-5-4"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   5
                  Left            =   120
                  TabIndex        =   17
                  Top             =   2082
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "4-4-5"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   6
                  Left            =   120
                  TabIndex        =   18
                  Top             =   2456
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "4-Week"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   7
                  Left            =   120
                  TabIndex        =   19
                  Top             =   2835
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstInterval 
                  Caption         =   "Quarters"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   3
                  Left            =   120
                  TabIndex        =   15
                  Top             =   1334
                  Width           =   1500
               End
            End
            Begin VB.Frame fraForecastUnit 
               Caption         =   "Forecast Unit"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   2115
               Left            =   2490
               TabIndex        =   20
               Top             =   1080
               Width           =   1905
               Begin VB.OptionButton opFcstUnit 
                  Caption         =   "Units"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   21
                  Top             =   212
                  Value           =   -1  'True
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstUnit 
                  Caption         =   "Cost"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   22
                  Top             =   586
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstUnit 
                  Caption         =   "Weight"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   23
                  Top             =   960
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstUnit 
                  Caption         =   "Cube"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   3
                  Left            =   120
                  TabIndex        =   24
                  Top             =   1334
                  Width           =   1500
               End
               Begin VB.OptionButton opFcstUnit 
                  Caption         =   "Price"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   4
                  Left            =   120
                  TabIndex        =   25
                  Top             =   1708
                  Width           =   1500
               End
            End
            Begin VB.Frame fraFcstModCriteria 
               Caption         =   "Forecast Modification Criteria"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   2280
               Index           =   1
               Left            =   4800
               TabIndex        =   34
               Top             =   2400
               Width           =   5295
               Begin VB.CheckBox ckFuturePdsUpdate 
                  Caption         =   "Update Future Periods only"
                  Height          =   340
                  Left            =   120
                  TabIndex        =   39
                  Top             =   1516
                  Width           =   4575
               End
               Begin VB.CheckBox ckFcstOutput 
                  Caption         =   "Save Forecast Output to File"
                  Height          =   340
                  Left            =   120
                  TabIndex        =   40
                  Top             =   1905
                  Width           =   4575
               End
               Begin VB.CheckBox ckEnableFcstVersions 
                  Caption         =   "Enable Forecast Versioning"
                  Enabled         =   0   'False
                  Height          =   340
                  Left            =   120
                  TabIndex        =   38
                  Top             =   1129
                  Visible         =   0   'False
                  Width           =   4575
               End
               Begin TDBText6Ctl.TDBText txtFcstLinkDesc 
                  Height          =   345
                  Left            =   120
                  TabIndex        =   37
                  TabStop         =   0   'False
                  Top             =   735
                  Width           =   5025
                  _Version        =   65536
                  _ExtentX        =   8864
                  _ExtentY        =   609
                  Caption         =   "AIM_ForecastMaintenance.frx":12EB
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_ForecastMaintenance.frx":1357
                  Key             =   "AIM_ForecastMaintenance.frx":1375
                  BackColor       =   -2147483644
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   0
                  BorderStyle     =   1
                  AlignHorizontal =   0
                  AlignVertical   =   0
                  MultiLine       =   0
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   40
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcForecast 
                  Bindings        =   "AIM_ForecastMaintenance.frx":13B9
                  Height          =   345
                  Index           =   1
                  Left            =   2760
                  TabIndex        =   36
                  Top             =   345
                  Width           =   2355
                  DataFieldList   =   "FcstID"
                  AllowInput      =   0   'False
                  _Version        =   196617
                  DataMode        =   2
                  Cols            =   2
                  ForeColorEven   =   0
                  BackColorOdd    =   12648447
                  Columns(0).Width=   3200
                  _ExtentX        =   4154
                  _ExtentY        =   609
                  _StockProps     =   93
                  ForeColor       =   -2147483640
                  BackColor       =   -2147483643
                  DataFieldToDisplay=   "FcstID"
               End
               Begin VB.Label lblFcstModCriteria 
                  Caption         =   "Forecast Link ID"
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   35
                  Top             =   390
                  Width           =   2475
               End
            End
            Begin VB.Frame fraApplyTrend 
               Caption         =   "Apply Trend"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   1395
               Left            =   2490
               TabIndex        =   26
               Top             =   3285
               Width           =   1905
               Begin VB.OptionButton optApplyTrend 
                  Caption         =   "No"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   28
                  Top             =   586
                  Value           =   -1  'True
                  Width           =   1500
               End
               Begin VB.OptionButton optApplyTrend 
                  Caption         =   "By Item"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   29
                  Top             =   960
                  Width           =   1500
               End
               Begin VB.OptionButton optApplyTrend 
                  Caption         =   "Yes"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   27
                  Top             =   212
                  Width           =   1500
               End
            End
            Begin VB.CheckBox ckNetRqmts 
               Caption         =   "Net Requirements"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   4920
               TabIndex        =   32
               Top             =   1080
               Width           =   4215
            End
            Begin VB.CheckBox ckFcstLevel 
               Caption         =   "Summarize by Item"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   4920
               TabIndex        =   33
               Top             =   1440
               Width           =   4215
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjustPct 
               Height          =   345
               Left            =   7725
               TabIndex        =   31
               Top             =   225
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastMaintenance.frx":13DA
               Caption         =   "AIM_ForecastMaintenance.frx":13FA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1466
               Keys            =   "AIM_ForecastMaintenance.frx":1484
               Spin            =   "AIM_ForecastMaintenance.frx":14CE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin TDBNumber6Ctl.TDBNumber txtFcstPds 
               Height          =   345
               Left            =   2925
               TabIndex        =   10
               Top             =   600
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastMaintenance.frx":14F6
               Caption         =   "AIM_ForecastMaintenance.frx":1516
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1582
               Keys            =   "AIM_ForecastMaintenance.frx":15A0
               Spin            =   "AIM_ForecastMaintenance.frx":15EA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   104
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   12
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBDate6Ctl.TDBDate txtFcstStartDate 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "M/d/yyyy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   2925
               TabIndex        =   8
               Top             =   225
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calendar        =   "AIM_ForecastMaintenance.frx":1612
               Caption         =   "AIM_ForecastMaintenance.frx":172A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1796
               Keys            =   "AIM_ForecastMaintenance.frx":17B4
               Spin            =   "AIM_ForecastMaintenance.frx":1812
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "10/15/2001"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   37179
               CenturyMode     =   0
            End
            Begin VB.Label lblForecastCriteria 
               Caption         =   "Forecast Adjustment %"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   4920
               TabIndex        =   30
               Top             =   270
               Width           =   2685
            End
            Begin VB.Label lblForecastCriteria 
               Caption         =   "Forecast Periods"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   180
               TabIndex        =   9
               Top             =   645
               Width           =   2685
            End
            Begin VB.Label lblForecastCriteria 
               Caption         =   "Start Date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   180
               TabIndex        =   7
               Top             =   265
               Width           =   2685
            End
         End
         Begin VB.Frame fraForecastRevCycle 
            Caption         =   "Review Cycle"
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   1935
            Left            =   105
            TabIndex        =   41
            Top             =   4935
            Width           =   10575
            Begin VB.Frame fraReviewFrequency 
               Caption         =   "Review Frequency"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   735
               Left            =   120
               TabIndex        =   46
               Top             =   1080
               Width           =   4275
               Begin VB.OptionButton opRevFreq 
                  Caption         =   "Monthly"
                  Height          =   340
                  Index           =   1
                  Left            =   2280
                  TabIndex        =   48
                  Top             =   240
                  Width           =   1750
               End
               Begin VB.OptionButton opRevFreq 
                  Caption         =   "Weekly"
                  Height          =   340
                  Index           =   0
                  Left            =   100
                  TabIndex        =   47
                  Top             =   200
                  Value           =   -1  'True
                  Width           =   1750
               End
            End
            Begin TDBNumber6Ctl.TDBNumber txtRevInterval 
               Height          =   345
               Left            =   2775
               TabIndex        =   43
               Top             =   285
               Width           =   1590
               _Version        =   65536
               _ExtentX        =   2805
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastMaintenance.frx":183A
               Caption         =   "AIM_ForecastMaintenance.frx":185A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":18C6
               Keys            =   "AIM_ForecastMaintenance.frx":18E4
               Spin            =   "AIM_ForecastMaintenance.frx":192E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   1
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin TDBDate6Ctl.TDBDate txtNextRevDate 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "M/d/yyyy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   2775
               TabIndex        =   45
               Top             =   675
               Width           =   1590
               _Version        =   65536
               _ExtentX        =   2805
               _ExtentY        =   609
               Calendar        =   "AIM_ForecastMaintenance.frx":1956
               Caption         =   "AIM_ForecastMaintenance.frx":1A6E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1ADA
               Keys            =   "AIM_ForecastMaintenance.frx":1AF8
               Spin            =   "AIM_ForecastMaintenance.frx":1B56
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "10/15/2001"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   37179
               CenturyMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtFcstContact 
               Height          =   345
               Left            =   7065
               TabIndex        =   50
               Top             =   285
               Width           =   3060
               _Version        =   65536
               _ExtentX        =   5397
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":1B7E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1BEA
               Key             =   "AIM_ForecastMaintenance.frx":1C08
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtFcstEMail 
               Height          =   345
               Left            =   7065
               TabIndex        =   52
               Top             =   675
               Width           =   3060
               _Version        =   65536
               _ExtentX        =   5397
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":1C4C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1CB8
               Key             =   "AIM_ForecastMaintenance.frx":1CD6
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblForecastRevCycle 
               Caption         =   "Email"
               Height          =   300
               Index           =   3
               Left            =   4785
               TabIndex        =   51
               Top             =   720
               Width           =   1995
            End
            Begin VB.Label lblForecastRevCycle 
               Caption         =   "Contact"
               Height          =   300
               Index           =   2
               Left            =   4785
               TabIndex        =   49
               Top             =   330
               Width           =   1995
            End
            Begin VB.Label lblForecastRevCycle 
               Caption         =   "Next Review Date"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   44
               Top             =   720
               Width           =   2475
            End
            Begin VB.Label lblForecastRevCycle 
               Caption         =   "Forecast Interval"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   42
               Top             =   330
               Width           =   2475
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpUserAccess 
         Height          =   7290
         Left            =   -99969
         TabIndex        =   87
         Top             =   360
         Width           =   10770
         _ExtentX        =   18997
         _ExtentY        =   12859
         _Version        =   131083
         TabGuid         =   "AIM_ForecastMaintenance.frx":1D1A
         Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAccessType 
            Height          =   945
            Left            =   7680
            TabIndex        =   80
            Top             =   3120
            Width           =   1365
            DataFieldList   =   "CodeID"
            _Version        =   196617
            Cols            =   1
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            Columns(0).Width=   3200
            _ExtentX        =   2408
            _ExtentY        =   1667
            _StockProps     =   77
            DataFieldToDisplay=   "CodeDesc"
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMUsers 
            Height          =   945
            Left            =   4800
            TabIndex        =   79
            Top             =   3120
            Width           =   1725
            DataFieldList   =   "userid"
            _Version        =   196617
            Cols            =   1
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            Columns(0).Width=   3200
            _ExtentX        =   3043
            _ExtentY        =   1667
            _StockProps     =   77
            DataFieldToDisplay=   "userid"
         End
         Begin VB.Frame fraUserAccess 
            Caption         =   "Access to Forecast Maintenance"
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3450
            Index           =   1
            Left            =   105
            TabIndex        =   77
            Top             =   30
            Width           =   10575
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgMainUserAccess 
               Height          =   3045
               Left            =   120
               TabIndex        =   78
               Top             =   240
               Width           =   10320
               _Version        =   196617
               DataMode        =   1
               AllowAddNew     =   -1  'True
               AllowDelete     =   -1  'True
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               _ExtentX        =   18203
               _ExtentY        =   5371
               _StockProps     =   79
            End
         End
         Begin VB.Frame fraUserAccess 
            Caption         =   "Access to Forecast Modification (Per Forecast Type)"
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3450
            Index           =   0
            Left            =   105
            TabIndex        =   81
            Top             =   3780
            Width           =   10575
            Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddFcstType 
               Height          =   945
               Left            =   2880
               TabIndex        =   83
               Top             =   600
               Width           =   1725
               DataFieldList   =   "CodeID"
               _Version        =   196617
               Cols            =   1
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               Columns(0).Width=   3200
               _ExtentX        =   3043
               _ExtentY        =   1667
               _StockProps     =   77
               DataFieldToDisplay=   "CodeDesc"
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgUserAccess 
               Height          =   3045
               Left            =   120
               TabIndex        =   82
               Top             =   240
               Width           =   10320
               _Version        =   196617
               DataMode        =   1
               AllowAddNew     =   -1  'True
               AllowDelete     =   -1  'True
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               _ExtentX        =   18203
               _ExtentY        =   5371
               _StockProps     =   79
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpFreezeForecast 
         Height          =   7290
         Left            =   -99969
         TabIndex        =   88
         Top             =   360
         Width           =   10770
         _ExtentX        =   18997
         _ExtentY        =   12859
         _Version        =   131083
         TabGuid         =   "AIM_ForecastMaintenance.frx":1D42
         Begin VB.Frame fraFreezeForecast 
            Caption         =   "By Location"
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3930
            Index           =   1
            Left            =   100
            TabIndex        =   91
            Top             =   3300
            Width           =   10575
            Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMLocations 
               Height          =   945
               Left            =   2760
               TabIndex        =   84
               Top             =   1800
               Width           =   1725
               DataFieldList   =   "LcID"
               _Version        =   196617
               Cols            =   1
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               Columns(0).Width=   3200
               _ExtentX        =   3043
               _ExtentY        =   1667
               _StockProps     =   77
               DataFieldToDisplay=   "LcID"
            End
            Begin TDBNumber6Ctl.TDBNumber txtFFPForecastPds 
               Height          =   345
               Left            =   2685
               TabIndex        =   92
               Top             =   600
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastMaintenance.frx":1D6A
               Caption         =   "AIM_ForecastMaintenance.frx":1D8A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":1DF6
               Keys            =   "AIM_ForecastMaintenance.frx":1E14
               Spin            =   "AIM_ForecastMaintenance.frx":1E5E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   0
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   104
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   -1
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   12
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBDate6Ctl.TDBDate txtFFPStartDate 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "M/d/yyyy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   2685
               TabIndex        =   93
               Top             =   240
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calendar        =   "AIM_ForecastMaintenance.frx":1E86
               Caption         =   "AIM_ForecastMaintenance.frx":1F9E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":200A
               Keys            =   "AIM_ForecastMaintenance.frx":2028
               Spin            =   "AIM_ForecastMaintenance.frx":2086
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   0
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   -1
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "10/15/2001"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   37179
               CenturyMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtFFPForecastInterval 
               Height          =   345
               Left            =   6945
               TabIndex        =   94
               Top             =   240
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   609
               Caption         =   "AIM_ForecastMaintenance.frx":20AE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastMaintenance.frx":211A
               Key             =   "AIM_ForecastMaintenance.frx":2138
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgFreezePds 
               Height          =   2565
               Left            =   120
               TabIndex        =   76
               Top             =   1200
               Width           =   10320
               _Version        =   196617
               DataMode        =   1
               AllowAddNew     =   -1  'True
               AllowDelete     =   -1  'True
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               _ExtentX        =   18203
               _ExtentY        =   4524
               _StockProps     =   79
            End
            Begin VB.Label lblFreezePds 
               Caption         =   "Forecast Periods"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   97
               Top             =   645
               Width           =   2475
            End
            Begin VB.Label lblFreezePds 
               Caption         =   "Start Date"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   96
               Top             =   285
               Width           =   2475
            End
            Begin VB.Label lblFreezePds 
               Caption         =   "Forecast Interval"
               Height          =   300
               Index           =   2
               Left            =   4440
               TabIndex        =   95
               Top             =   285
               Width           =   2355
            End
         End
         Begin VB.Frame fraFreezeForecast 
            Caption         =   "By Item "
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3210
            Index           =   0
            Left            =   100
            TabIndex        =   72
            Top             =   30
            Width           =   10575
            Begin VB.CommandButton cmdFilterItemDropdown 
               Caption         =   "Filter Ite&ms"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   345
               Left            =   120
               Style           =   1  'Graphical
               TabIndex        =   74
               Top             =   2760
               Width           =   2235
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddItems 
               Height          =   945
               Left            =   2640
               TabIndex        =   75
               Top             =   840
               Width           =   1845
               _Version        =   196617
               Cols            =   1
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               Columns(0).Width=   3200
               _ExtentX        =   3254
               _ExtentY        =   1667
               _StockProps     =   77
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgForecastDetail 
               Height          =   2445
               Left            =   120
               TabIndex        =   73
               Top             =   240
               Width           =   10320
               _Version        =   196617
               DataMode        =   1
               AllowAddNew     =   -1  'True
               AllowDelete     =   -1  'True
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   18203
               _ExtentY        =   4313
               _StockProps     =   79
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
      End
   End
   Begin VB.Frame fraForecastInfo 
      Height          =   970
      Left            =   45
      TabIndex        =   90
      Top             =   -30
      Width           =   10830
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcForecast 
         Bindings        =   "AIM_ForecastMaintenance.frx":217C
         Height          =   345
         Index           =   0
         Left            =   2985
         TabIndex        =   1
         Top             =   165
         Width           =   2355
         DataFieldList   =   "FcstID"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   4154
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "FcstID"
      End
      Begin VB.CheckBox ckFcstStatus 
         Caption         =   "Enabled"
         Height          =   340
         Left            =   6960
         TabIndex        =   4
         Top             =   552
         Width           =   3615
      End
      Begin VB.CheckBox ckFcstLocked 
         Caption         =   "Forecast Locked"
         CausesValidation=   0   'False
         Enabled         =   0   'False
         Height          =   340
         Left            =   6960
         TabIndex        =   2
         TabStop         =   0   'False
         Top             =   167
         Width           =   3615
      End
      Begin TDBText6Ctl.TDBText txtFcstDesc 
         Height          =   345
         Left            =   2985
         TabIndex        =   3
         Top             =   555
         Width           =   3705
         _Version        =   65536
         _ExtentX        =   6535
         _ExtentY        =   609
         Caption         =   "AIM_ForecastMaintenance.frx":219D
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastMaintenance.frx":2209
         Key             =   "AIM_ForecastMaintenance.frx":2227
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   40
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtFcstId 
         Height          =   345
         Left            =   2985
         TabIndex        =   0
         Top             =   165
         Visible         =   0   'False
         Width           =   2355
         _Version        =   65536
         _ExtentX        =   4154
         _ExtentY        =   609
         Caption         =   "AIM_ForecastMaintenance.frx":226B
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastMaintenance.frx":22D7
         Key             =   "AIM_ForecastMaintenance.frx":22F5
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label lblForecastInfo 
         Caption         =   "Description"
         Height          =   300
         Index           =   1
         Left            =   180
         TabIndex        =   89
         Top             =   600
         Width           =   2715
      End
      Begin VB.Label lblForecastInfo 
         Caption         =   "Forecast ID"
         Height          =   300
         Index           =   0
         Left            =   180
         TabIndex        =   85
         Top             =   195
         Width           =   2715
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   8760
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_ForecastMaintenance.frx":2339
      ToolBars        =   "AIM_ForecastMaintenance.frx":C870
   End
End
Attribute VB_Name = "AIM_ForecastMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'Option Explicit
'
'Dim Cn As ADODB.Connection
'
'Dim rsForecast As ADODB.Recordset
'
'Dim rsUserAccess As ADODB.Recordset
'Dim rsMainUserAccess As ADODB.Recordset
'Dim rsAIMUsers As ADODB.Recordset
'Dim rsFcstType As ADODB.Recordset
'Dim rsAccessType As ADODB.Recordset
'
'Dim rsForecastDetail As ADODB.Recordset
'Dim rsItems As ADODB.Recordset
'
'Dim rsFreezePds As ADODB.Recordset
'Dim rsAIMLocations As ADODB.Recordset
'
'Dim vBookMark_UserAccess As Variant
'Dim vBookMark_ForecastDetail As Variant
'Dim vBookMark_FreezePds As Variant
'
'Dim FcstId As String
'Dim FcstIdBookMark As String
'Dim SAVersion As Integer
'
'Dim m_NewFcst As Boolean
'Dim m_ActiveGridID As String
'Dim xForecastLinkList As XArrayDB
'Dim IsFilteringEnabled As Boolean
'
'Const ACTIVEGRID_ForecastDetail As String = "FORECASTDETAIL"
'Const ACTIVEGRID_FreezePds As String = "FREEZEPDS"
'Const ACTIVEGRID_MainUserAccess As String = "MAINUSERACCESS"
'Const ACTIVEGRID_UserAccess As String = "USERACCESS"
'
'Const IDX_DCFORECAST_FCSTID As Integer = 0
'Const IDX_DCFORECAST_FCSTLINKID As Integer = 1
'
'Private Sub atForecastCriteria_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    Screen.MousePointer = vbHourglass
'
'    Me.tbNavigation.Tools("ID_Save").Enabled = True
'    Me.tbNavigation.Tools("ID_Delete").Enabled = True
'    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
'    vBookMark_ForecastDetail = Null
'    vBookMark_FreezePds = Null
'    vBookMark_UserAccess = Null
'
'    f_AIMForecast_Commit True
'
'    'Clean up other recordsets, first
'    If atForecastCriteria.Tabs(3).Selected = True Then
'        DestroyDetailRecordsets 2
'
'    ElseIf atForecastCriteria.Tabs(4).Selected = True Then
'        DestroyDetailRecordsets 3
'
'    End If
'
'    'Get recordsets for target tab
'    Select Case NewTab.Caption
'        Case Is = Me.atForecastCriteria.Tabs(3).Caption 'FreezeForecasts
'            GetDetailRecordsets 2
'
'        Case Is = Me.atForecastCriteria.Tabs(4).Caption 'Security
'            GetDetailRecordsets 3
'
'    End Select
'
'    'Disable header information if not on "header" tab
'    If StrComp(NewTab.Caption, atForecastCriteria.Tabs(1).Caption, vbTextCompare) = 0 Then
'        ForecastInfoToggle True
'    Else
'        ForecastInfoToggle False
'    End If
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(atForecastCriteria_beforeTabClick)"
'End Sub
'
'Private Sub cmdFilterItemDropdown_Click()
'On Error GoTo ErrorHandler
'
'    Dim arrItems As New XArrayDB
'
'    Dim FcstItemFilter As FCST_ITEM_FILTER
'
'    Dim RowCounter As Long
'    Dim ColCounter As Long
'
'    With FcstItemFilter
'        'Set parameters to existing fields
'        .Item = txtItem.Text
'        .ItemStatus = txtItemStatus.Text
'        .VnId = txtVnId.Text
'        .Assort = txtAssort.Text
'        .LcId = txtLcId.Text
'        .ById = txtById.Text
'        .Class1 = txtClass(1).Text
'        .Class2 = txtClass(2).Text
'        .Class3 = txtClass(3).Text
'        .Class4 = txtClass(4).Text
'        .LDivision = txtLDivision.Text
'        .LRegion = txtLRegion.Text
'        .LStatus = txtLStatus.Text
'        .LUserDefined = txtLUserDefined.Text
'
'        CallItemFilter Cn, FcstItemFilter, False, arrItems
'
'    End With
'
'    'Associate with a disconnected recordset
'    If f_IsRecordsetValidAndOpen(rsItems) Then rsItems.Close
'    Set rsItems = Nothing
'    Set rsItems = New ADODB.Recordset
'    rsItems.Fields.Append "Item", adBSTR, 25
'    rsItems.Fields.Append "LcID", adBSTR, 25
'    rsItems.Fields.Append "ItDesc", adBSTR, 30
'    rsItems.Fields.Append "ItStat", adBSTR, 1
'    rsItems.Open
'    For RowCounter = arrItems.LowerBound(1) To arrItems.UpperBound(1)
'        With rsItems
'            .AddNew
'            For ColCounter = arrItems.LowerBound(2) To 3
'                .Fields(ColCounter) = arrItems(RowCounter, ColCounter)
'            Next
'        End With
'    Next
'
'    Set ddItems.DataSource = rsItems
'
'    Set arrItems = Nothing
'
'Exit Sub
'ErrorHandler:
'    Set arrItems = Nothing
'    If Err.Number = 9 Then
'        'The array is empty, exit quietly
'        Exit Sub
'    Else
'        f_HandleErr Me.Caption & "(cmdFilterItemDropdown_Click)"
'    End If
'End Sub
'
'Private Sub ShowItemFilter()
'On Error GoTo ErrorHandler
'
'    Dim arrItems As New XArrayDB
'    Dim FcstItemFilter As FCST_ITEM_FILTER
'    Dim RowCounter As Long
'    Dim ColCounter As Long
'    Dim RtnCode As Integer
'
'    With FcstItemFilter
'        'Set parameters to existing fields
'        .Item = txtItem.Text
'        .ItemStatus = txtItemStatus.Text
'        .VnId = txtVnId.Text
'        .Assort = txtAssort.Text
'        .LcId = txtLcId.Text
'        .ById = txtById.Text
'        .Class1 = txtClass(1).Text
'        .Class2 = txtClass(2).Text
'        .Class3 = txtClass(3).Text
'        .Class4 = txtClass(4).Text
'        .LDivision = txtLDivision.Text
'        .LRegion = txtLRegion.Text
'        .LStatus = txtLStatus.Text
'        .LUserDefined = txtLUserDefined.Text
'
'         RtnCode = CallItemFilter(Cn, FcstItemFilter, Not IsFilteringEnabled, arrItems)
'        If RtnCode <> -1 Then   'when the user hits cancel do not set these values
'
'            'Set display to returned values
'            txtItem.Text = .SubFilterItem
'            txtItemStatus.Text = .SubFilterItemStatus
'            txtVnId.Text = .SubFilterVnId
'            txtAssort.Text = .SubFilterAssort
'            txtLcId.Text = .SubFilterLcID
'            txtById.Text = .SubFilterById
'            txtClass(1).Text = .SubFilterClass1
'            txtClass(2).Text = .SubFilterClass2
'            txtClass(3).Text = .SubFilterClass3
'            txtClass(4).Text = .SubFilterClass4
'            txtLDivision.Text = .SubFilterLDivision
'            txtLRegion.Text = .SubFilterLRegion
'            txtLStatus.Text = .SubFilterLStatus
'            txtLUserDefined.Text = .SubFilterLUserDefined
'        End If
'    End With
'
'    'Don't use arritem here, so discard it
'    Set arrItems = Nothing
'
'
'Exit Sub
'ErrorHandler:
'    Set arrItems = Nothing
'    If Err.Number = 9 Then
'        'The array is empty, exit quietly
'        Exit Sub
'    Else
'        f_HandleErr Me.Caption & "(ShowItemFilter)"
'    End If
'End Sub
'
'Private Sub dcForecast_Change(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'    Dim lngRecordIndex As Long
'
'    Select Case Index
'        Case IDX_DCFORECAST_FCSTID
'            'Navigate to the selected forecast ID
'            If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'                RtnCode = verify_FcstID
'                If RtnCode = SUCCEED Then
'                    RtnCode = FetchGivenFcstID
'                    f_AIMForecast_Refresh
'                    'Set background value (used to add/update)
'                    txtFcstId.Text = dcForecast(IDX_DCFORECAST_FCSTID).Text
'                Else
'                    txtFcstId.Text = rsForecast!FcstId
'                End If
'            End If
'
'        Case IDX_DCFORECAST_FCSTLINKID
'            If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'            If StrComp(dcForecast(IDX_DCFORECAST_FCSTLINKID).Text, rsForecast!FcstId) = 0 Then
'                strMessage = getTranslationResource("MSGBOX07312")
'                If StrComp(strMessage, "MSGBOX07312") = 0 Then _
'                        strMessage = "Forecast Link ID cannot be the same as the current Forecast ID. Please select a valid value from the given list."
'                MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'                dcForecast(IDX_DCFORECAST_FCSTLINKID).Text = ""
'                Exit Sub
'            Else
'                If dcForecast(IDX_DCFORECAST_FCSTLINKID).Text = "" Then
'                    txtFcstLinkDesc.Text = ""
'                Else
'                    On Error Resume Next
'                    lngRecordIndex = xForecastLinkList.Find(0, 0, dcForecast(IDX_DCFORECAST_FCSTLINKID).Text, , , XTYPE_STRINGCASESENSITIVE)
'                    If Err.Number = 91 _
'                    Or Err.Number = 9 Then
'                        txtFcstLinkDesc.Text = ""
'                    ElseIf Err.Number <> 0 Then
'                        GoTo ErrorHandler
'                    End If
'                    On Error GoTo ErrorHandler
'                    '0 is a valid index, for the very first row, so search for negatives
'                    If lngRecordIndex >= 0 Then
'                        'set return value = the matching text
'                        txtFcstLinkDesc.Text = xForecastLinkList(lngRecordIndex, 1)
'                    Else
'                        txtFcstLinkDesc.Text = ""
'                    End If
'                End If
'            End If
'
'        Case Else
'            'Undefined!
'
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecast(" & Index & ")_Change)"
'End Sub
'
'Private Sub dcForecast_Click(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    Select Case Index
'        Case IDX_DCFORECAST_FCSTID
'            'Check for updates, first
'            f_AIMForecast_Commit True
'
'            'Navigate to the selected forecast ID
'            If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'                'la here. decide if it's the dctext or the txt text you want to compare here!
'                If StrComp(rsForecast!FcstId, dcForecast(IDX_DCFORECAST_FCSTID).Text, vbTextCompare) <> 0 Then
'                    RtnCode = FetchGivenFcstID
'                    If RtnCode = SUCCEED Then
'                        'Set background value (used to add/update)
'                        txtFcstId.Text = dcForecast(IDX_DCFORECAST_FCSTID).Text
'                    End If
'                    f_AIMForecast_Refresh
'                Else
'                    txtFcstId.Text = rsForecast!FcstId
'                End If
'            End If
'
'        Case IDX_DCFORECAST_FCSTLINKID
'            If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'            If StrComp(dcForecast(IDX_DCFORECAST_FCSTLINKID).Text, rsForecast!FcstId) = 0 Then
'                strMessage = getTranslationResource("MSGBOX07312")
'                If StrComp(strMessage, "MSGBOX07312") = 0 Then _
'                        strMessage = "Forecast Link ID cannot be the same as the current Forecast ID. Please select a valid value from the given list."
'                MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'                dcForecast(IDX_DCFORECAST_FCSTLINKID).Text = ""
'                Exit Sub
'            Else
'                If dcForecast(IDX_DCFORECAST_FCSTLINKID).Text = "" Then
'                    txtFcstLinkDesc = ""
'                Else
'                    txtFcstLinkDesc.Text = dcForecast(IDX_DCFORECAST_FCSTLINKID).Columns(1).Value
'                End If
'            End If
'
'        Case Else
'            'Undefined!
'
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecast(" & Index & ")_Click)"
'End Sub
'
'Private Sub dcForecast_InitColumnProps(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcForecast(Index).Columns(0).Caption = getTranslationResource("ID")
'    Me.dcForecast(Index).Columns(0).Width = 1500
'    Me.dcForecast(Index).Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcForecast(Index).Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcForecast(Index).Columns(0).DataField = "FcstID"
'
'    Me.dcForecast(Index).Columns(1).Caption = getTranslationResource("Description")
'    Me.dcForecast(Index).Columns(1).Width = 2880
'    Me.dcForecast(Index).Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcForecast(Index).Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dcForecast(Index).Columns(1).DataField = "FcstDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcForecast(Index), ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcForecast(Index).Columns.Count - 1
''        dcForecast(Index).Columns(IndexCounter).HasHeadBackColor = True
''        dcForecast(Index).Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecast(" & Index & ")_InitColumnProps)"
'End Sub
'
'Private Function verify_FcstID() As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strMessage As String
'    Dim lngRecordIndex As Long
'
'    If m_NewFcst Then
'        'la here
'        Exit Function
'    End If
'    'Set defaults to failure
'    verify_FcstID = -1
'    lngRecordIndex = -1
'
'    On Error Resume Next
'    lngRecordIndex = xForecastLinkList.Find(0, 0, dcForecast(IDX_DCFORECAST_FCSTID).Text, , , XTYPE_STRINGCASESENSITIVE)
'    If Err.Number = 91 _
'    Or Err.Number = 9 Then
'        'xarray is nothing, Keep processing
'    ElseIf Err.Number <> 0 Then
'        GoTo ErrorHandler
'    End If
'    On Error GoTo ErrorHandler
'
'    '0 is a valid index, for the very first row, so search for negatives
'    If lngRecordIndex >= 0 Then
'        'set return value = the matching text
'        verify_FcstID = SUCCEED
'    Else
'        RtnCode = FetchGivenFcstID
'        verify_FcstID = RtnCode
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(verify_FcstID)", Err.Description
'End Function
'
'Private Sub dcForecast_Validate(Index As Integer, Cancel As Boolean)
'On Error GoTo ErrorHandler
'    Dim RtnCode As Integer
'    Dim strMessage As String
'
'    Select Case Index
'        Case IDX_DCFORECAST_FCSTID
'            RtnCode = verify_FcstID
'            If RtnCode < 0 Then
'                strMessage = getTranslationResource("MSGBOX07315")
'                If StrComp(strMessage, "MSGBOX07315") = 0 Then _
'                        strMessage = "'" & dcForecast(IDX_DCFORECAST_FCSTID).Text & _
'                                     "' is an invalid value for Forecast ID. Please choose from the given list."
'                MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'                dcForecast(IDX_DCFORECAST_FCSTID).SetFocus
'                Cancel = True
'                Exit Sub
'            End If
'
'        Case IDX_DCFORECAST_FCSTLINKID
'            'to be done
'    End Select
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(dcForecast(" & Index & ")_Validate)", Err.Description
'End Sub
'
'Private Sub ddAIMLocations_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.ddAIMLocations.Columns(0).Name = "lcid"
'    Me.ddAIMLocations.Columns(0).Caption = getTranslationResource("Location")
'    Me.ddAIMLocations.Columns(0).Width = 1000
'    Me.ddAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.ddAIMLocations.Columns(0).DataField = "lcid"
'
'    Me.ddAIMLocations.Columns(1).Name = "lname"
'    Me.ddAIMLocations.Columns(1).Caption = getTranslationResource("Location Name")
'    Me.ddAIMLocations.Columns(1).Width = 2880
'    Me.ddAIMLocations.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.ddAIMLocations.Columns(1).DataField = "lname"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths ddAIMLocations, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To ddAIMLocations.Columns.Count - 1
''        ddAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
''        ddAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ddAIMLocations_InitColumnProps)"
'End Sub
'
'Private Sub ddItems_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.ddItems.DataFieldList = "Item"
'    Me.ddItems.DataFieldToDisplay = "Item"
'
'    Me.ddItems.Columns(0).DataField = "Item"
'    Me.ddItems.Columns(0).Name = "Item"
'    Me.ddItems.Columns(0).Caption = getTranslationResource("Item")
'    Me.ddItems.Columns(0).Width = 2880
'    Me.ddItems.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(0).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(1).DataField = "LcID"
'    Me.ddItems.Columns(1).Name = "LcID"
'    Me.ddItems.Columns(1).Caption = getTranslationResource("Location")
'    Me.ddItems.Columns(1).Width = 2000
'    Me.ddItems.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(1).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(2).DataField = "ItDesc"
'    Me.ddItems.Columns(2).Name = "ItDesc"
'    Me.ddItems.Columns(2).Caption = getTranslationResource("Item Description")
'    Me.ddItems.Columns(2).Width = 2880
'    Me.ddItems.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(2).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(3).DataField = "ItStat"
'    Me.ddItems.Columns(3).Name = "ItStat"
'    Me.ddItems.Columns(3).Caption = getTranslationResource("Item Status")
'    Me.ddItems.Columns(3).Width = 1000
'    Me.ddItems.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(3).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(4).DataField = "VnID"
'    Me.ddItems.Columns(4).Name = "VnID"
'    Me.ddItems.Columns(4).Caption = getTranslationResource("Vendor ID")
'    Me.ddItems.Columns(4).Width = 1500
'    Me.ddItems.Columns(4).Style = ssStyleEdit
'    Me.ddItems.Columns(4).Locked = True
'    Me.ddItems.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(4).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(5).DataField = "Assort"
'    Me.ddItems.Columns(5).Name = "Assort"
'    Me.ddItems.Columns(5).Caption = getTranslationResource("Assortment")
'    Me.ddItems.Columns(5).Width = 1500
'    Me.ddItems.Columns(5).Style = ssStyleEdit
'    Me.ddItems.Columns(5).Locked = True
'    Me.ddItems.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(5).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(6).DataField = "ByID"
'    Me.ddItems.Columns(6).Name = "ByID"
'    Me.ddItems.Columns(6).Caption = getTranslationResource("Buyer ID")
'    Me.ddItems.Columns(6).Width = 1500
'    Me.ddItems.Columns(6).Style = ssStyleEdit
'    Me.ddItems.Columns(6).Locked = True
'    Me.ddItems.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(6).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(7).DataField = "Class1"
'    Me.ddItems.Columns(7).Name = "Class1"
'    Me.ddItems.Columns(7).Caption = getTranslationResource("Classification 1")
'    Me.ddItems.Columns(7).Width = 1500
'    Me.ddItems.Columns(7).Style = ssStyleEdit
'    Me.ddItems.Columns(7).Locked = True
'    Me.ddItems.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(7).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(8).DataField = "Class2"
'    Me.ddItems.Columns(8).Name = "Class2"
'    Me.ddItems.Columns(8).Caption = getTranslationResource("Classification 2")
'    Me.ddItems.Columns(8).Width = 1500
'    Me.ddItems.Columns(8).Style = ssStyleEdit
'    Me.ddItems.Columns(8).Locked = True
'    Me.ddItems.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(8).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(9).DataField = "Class3"
'    Me.ddItems.Columns(9).Name = "Class3"
'    Me.ddItems.Columns(9).Caption = getTranslationResource("Classification 3")
'    Me.ddItems.Columns(9).Width = 1500
'    Me.ddItems.Columns(9).Style = ssStyleEdit
'    Me.ddItems.Columns(9).Locked = True
'    Me.ddItems.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(9).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(10).DataField = "Class4"
'    Me.ddItems.Columns(10).Name = "Class4"
'    Me.ddItems.Columns(10).Caption = getTranslationResource("Classification 4")
'    Me.ddItems.Columns(10).Width = 1500
'    Me.ddItems.Columns(10).Style = ssStyleEdit
'    Me.ddItems.Columns(10).Locked = True
'    Me.ddItems.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddItems.Columns(10).Alignment = ssCaptionAlignmentLeft
'
'    Me.ddItems.Columns(4).Visible = False
'    Me.ddItems.Columns(5).Visible = False
'    Me.ddItems.Columns(6).Visible = False
'    Me.ddItems.Columns(7).Visible = False
'    Me.ddItems.Columns(8).Visible = False
'    Me.ddItems.Columns(9).Visible = False
'    Me.ddItems.Columns(10).Visible = False
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths ddItems, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To ddItems.Columns.Count - 1
''        ddItems.Columns(IndexCounter).HasHeadBackColor = True
''        ddItems.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ddItems_InitColumnProps)"
'End Sub
'
'Private Sub dgForecastDetail_AfterColUpdate(ByVal ColIndex As Integer)
'On Error GoTo ErrorHandler
'
'    If ColIndex = 0 Then
'        dgForecastDetail.Columns(1).Value = ddItems.Columns(1).Value
'        dgForecastDetail.Columns(2).Value = vbChecked
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_AfterColUpdate)"
'End Sub
'
'Private Sub dgForecastDetail_AfterInsert(RtnDispErrMsg As Integer)
'On Error GoTo ErrorHandler
'
'    If Cn.Errors.Count > 0 Then
'        RtnDispErrMsg = True
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_AfterInsert)"
'End Sub
'
'Private Sub dgForecastDetail_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim InvalidData As Boolean
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    InvalidData = UserInputValidation(dgForecastDetail.Name, strMessage, ColIndex)
'    If InvalidData = True Then
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        dgForecastDetail.DataChanged = False
'    End If
'
'    Cancel = InvalidData
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_BeforeColUpdate)"
'End Sub
'
'Private Sub dgForecastDetail_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
'On Error GoTo ErrorHandler
'
'    Dim Result As Long
'    Dim strMessage As String
'    Dim strMessage1 As String
'
'    'Cancel the default prompt
'    DispPromptMsg = False
'
'    If gAccessLvl = g_ACCESS_CREATEMODIFY _
'    And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'    Then
'        'Display a confirmation msgbox
'        strMessage = getTranslationResource("MSGBOX07304")
'        If StrComp(strMessage, "MSGBOX07304") = 0 Then strMessage = "Delete the"
'
'        strMessage = strMessage + " " + str(dgForecastDetail.SelBookmarks.Count) + " "
'        strMessage1 = getTranslationResource("MSGBOX07306")
'        If StrComp(strMessage1, "MSGBOX07306") = 0 Then strMessage1 = "selected Forecast Detail records?"
'        strMessage = strMessage + strMessage1
'
'        Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ForecastMaintenance.Caption))
'
'        Select Case Result
'        Case vbYes
'            'User chose to delete. Do nothing
'        Case vbNo
'            'User chose to Cancel
'            Cancel = True
'        End Select
'    Else
'        'Cancel the delete request
'        Cancel = True
'
'        strMessage = getTranslationResource("MSGBOX07311")
'        If StrComp(strMessage, "MSGBOX07311") = 0 Then strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_BeforeDelete)"
'End Sub
'
'Private Sub dgForecastDetail_BtnClick()
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    If ddItems.DataSource Is Nothing Then
'        strMessage = getTranslationResource("MSGBOX07316")
'        If StrComp(strMessage, "MSGBOX07316") = 0 Then strMessage = "Please apply 'Filter Items' to enable this option."
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        cmdFilterItemDropdown.SetFocus
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_BtnClick)"
'End Sub
'
'Private Sub dgForecastDetail_Click()
'On Error GoTo ErrorHandler
'
'    m_ActiveGridID = ACTIVEGRID_ForecastDetail
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_Click)"
'End Sub
'
'Private Sub dgForecastDetail_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dgForecastDetail.Columns(0).Name = "Item"
'    Me.dgForecastDetail.Columns(0).Caption = getTranslationResource("Item")
'    Me.dgForecastDetail.Columns(0).Style = ssStyleEdit
'    Me.dgForecastDetail.Columns(0).ButtonsAlways = True
'    Me.dgForecastDetail.Columns(0).DropDownHwnd = Me.ddItems.hwnd
'    Me.dgForecastDetail.Columns(0).Width = 2880
'    Me.dgForecastDetail.Columns(0).Locked = False
'    Me.dgForecastDetail.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgForecastDetail.Columns(0).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgForecastDetail.Columns(1).Name = "Lcid"
'    Me.dgForecastDetail.Columns(1).Caption = getTranslationResource("Location")
'    Me.dgForecastDetail.Columns(1).Style = ssStyleEdit
'    Me.dgForecastDetail.Columns(1).Width = 2880
'    Me.dgForecastDetail.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgForecastDetail.Columns(1).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgForecastDetail.Columns(2).Name = "ItemLocked"
'    Me.dgForecastDetail.Columns(2).Caption = getTranslationResource("Freeze")
'    Me.dgForecastDetail.Columns(2).Style = ssStyleCheckBox
'    Me.dgForecastDetail.Columns(2).Width = 1100
'    Me.dgForecastDetail.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgForecastDetail, ACW_EXPAND
'    End If
'
'    Me.dgForecastDetail.RowNavigation = ssRowNavigationUDLock
'
'    For IndexCounter = 0 To dgForecastDetail.Columns.Count - 1
''        dgForecastDetail.Columns(IndexCounter).HasHeadBackColor = True
''        dgForecastDetail.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'        If dgForecastDetail.Columns(IndexCounter).Locked = False Then dgForecastDetail.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_InitColumnProps)"
'End Sub
'
'Private Sub dgForecastDetail_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    If Button = vbLeftButton Then
'        If ddItems.DataSource Is Nothing Then
'            strMessage = getTranslationResource("MSGBOX07316")
'            If StrComp(strMessage, "MSGBOX07316") = 0 Then strMessage = "Please apply 'Filter Items' to enable this option."
'            MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'            cmdFilterItemDropdown.SetFocus
'        End If
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ddItems_Click)"
'End Sub
'
'
'Private Sub dgForecastDetail_RowLoaded(ByVal bookmark As Variant)
'On Error GoTo ErrorHandler
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_RowLoaded)"
'End Sub
'
'Private Sub dgForecastDetail_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As String
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgForecastDetail.Name, strMessage) Then
'        rsForecastDetail.AddNew
'
'        rsForecastDetail!FcstId = rsForecast!FcstId
'
'        'RowBuf (n, 1) = ItemFilter button
'
'        If Not IsNull(RowBuf.Value(0, 0)) Then
'            rsForecastDetail!Item = RowBuf.Value(0, 0)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) Then
'            rsForecastDetail!LcId = RowBuf.Value(0, 1)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 2)) Then
'            rsForecastDetail!ItemLocked = IIf(RowBuf.Value(0, 2) = True, "Y", "N")
'        End If
'
'        f_ChildTable_Update dgForecastDetail.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
''        rsForecastDetail.CancelUpdate
'        Exit Sub
'    End If
'
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07303")
'        If StrComp(strMessage, "ERRMSG07303") = 0 Then strMessage = "Error editing AIM Forecast Detail record."
'        ADOErrorHandler Cn, strMessage
'        rsForecastDetail.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07303")
'        If StrComp(strMessage, "STATMSG07303") = 0 Then strMessage = "AIM Forecast Detail record successfully updated."
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_UnboundAddData)"
'    rsForecastDetail.CancelUpdate
'End Sub
'
'Private Sub dgForecastDetail_UnboundDeleteRow(bookmark As Variant)
'On Error GoTo ErrorHandler
'
'    rsForecastDetail.bookmark = bookmark
'    rsForecastDetail.Delete
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_UnboundDeleteRow)"
'End Sub
'
'Private Sub dgForecastDetail_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
'On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        'Going up or down?
'        If NumberOfRowsToMove = 0 Then
'            Exit Sub
'        ElseIf NumberOfRowsToMove < 0 Then
'            rsForecastDetail.MoveLast
'        Else
'            rsForecastDetail.MoveFirst
'        End If
'
'    Else
'        'Line up the bookmark of the recordset
'        'with the grid's StartLocation
'        rsForecastDetail.bookmark = StartLocation
'
'    End If
'
'    'Note: Do not use StartLocation because it could be null
'    rsForecastDetail.Move NumberOfRowsToMove
'
'    'Set the new location in the grid
'    NewLocation = rsForecastDetail.bookmark
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_UnboundPositionData)"
'End Sub
'
'Private Sub dgForecastDetail_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RowCounter As Integer
'    Dim RowIndex As Integer
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecastDetail) Then Exit Sub
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            rsForecastDetail.MoveLast
'        Else
'            rsForecastDetail.MoveFirst
'        End If
'
'    Else
'        rsForecastDetail.bookmark = StartLocation
'        If ReadPriorRows Then
'            rsForecastDetail.MovePrevious
'        Else
'            rsForecastDetail.MoveNext
'        End If
'
'    End If
'
'    For RowIndex = 0 To RowBuf.RowCount - 1
'
'        If rsForecastDetail.BOF Or rsForecastDetail.eof Then Exit For
'
'        Select Case RowBuf.ReadType
'            Case 0  'All data must be read
'                RowBuf.Value(RowIndex, 0) = rsForecastDetail!Item
'                RowBuf.Value(RowIndex, 1) = rsForecastDetail!LcId
'                'Assuming "Y" = true and "N" = false
'                RowBuf.Value(RowIndex, 2) = IIf(rsForecastDetail!ItemLocked = "Y", True, False)
'
'            Case 1  'Only bookmarks must be read
'                'That is done anyway, so leave it after end select
'            Case Else
'                'Cases 2 and 3 are not used by DBGrid... yet?
'        End Select
'        RowBuf.bookmark(RowIndex) = rsForecastDetail.bookmark
'
'        If ReadPriorRows Then
'            rsForecastDetail.MovePrevious
'        Else
'            rsForecastDetail.MoveNext
'        End If
'
'        RowCounter = RowCounter + 1
'    Next RowIndex
'
'    RowBuf.RowCount = RowCounter
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_UnboundReadData)"
'End Sub
'
'Private Sub dgForecastDetail_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As String
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    rsForecastDetail.bookmark = WriteLocation
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgForecastDetail.Name, strMessage) Then
'        rsForecastDetail!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) Then
'            rsForecastDetail!LcId = RowBuf.Value(0, 0)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) Then
'            rsForecastDetail!Item = RowBuf.Value(0, 1)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 2)) Then
'            rsForecastDetail!ItemLocked = IIf(RowBuf.Value(0, 2) = True, "Y", "N")
'        End If
'
'        'Update the Forecast User Access Table
'        f_ChildTable_Update dgForecastDetail.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        'Cancel update
'        f_ChildTable_Update dgForecastDetail.Name, True
'    End If
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07303")
'        If StrComp(strMessage, "ERRMSG07303") = 0 Then strMessage = "Error editing AIM Forecast Detail record."
'        ADOErrorHandler Cn, strMessage
'        rsForecastDetail.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07303")
'        If StrComp(strMessage, "STATMSG07303") = 0 Then strMessage = "AIM Forecast Detail record successfully updated."
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_UnboundWriteData)"
'    rsForecastDetail.CancelUpdate
'End Sub
'
'Private Sub ddAccessType_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.ddAccessType.DataFieldList = "CodeID"
'    Me.ddAccessType.DataFieldToDisplay = "CodeID"
'
'    Me.ddAccessType.Columns(0).Name = "AccessType"
'    Me.ddAccessType.Columns(0).Caption = getTranslationResource("Access Type")
'    Me.ddAccessType.Columns(0).Width = 1000
'    Me.ddAccessType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddAccessType.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.ddAccessType.Columns(0).DataField = "CodeID"
'
'    Me.ddAccessType.Columns(1).Name = "Description"
'    Me.ddAccessType.Columns(1).Caption = getTranslationResource("Access Type Description")
'    Me.ddAccessType.Columns(1).Width = 2880
'    Me.ddAccessType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddAccessType.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.ddAccessType.Columns(1).DataField = "CodeDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths ddAccessType, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To ddAccessType.Columns.Count - 1
''        ddAccessType.Columns(IndexCounter).HasHeadBackColor = True
''        ddAccessType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ddAccessType_InitColumnProps)"
'End Sub
'
'Private Sub ddAIMUsers_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.ddAIMUsers.DataFieldList = "UserID"
'    Me.ddAIMUsers.DataFieldToDisplay = "UserID"
'
'    Me.ddAIMUsers.Columns(0).Name = "UserID"
'    Me.ddAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
'    Me.ddAIMUsers.Columns(0).Width = 1000
'    Me.ddAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.ddAIMUsers.Columns(0).DataField = "UserID"
'
'    Me.ddAIMUsers.Columns(1).Name = "UserName"
'    Me.ddAIMUsers.Columns(1).Caption = getTranslationResource("Name")
'    Me.ddAIMUsers.Columns(1).Width = 2880
'    Me.ddAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.ddAIMUsers.Columns(1).DataField = "UserName"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths ddAIMUsers, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To ddAIMUsers.Columns.Count - 1
''        ddAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
''        ddAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ddAIMUsers_InitColumnProps)"
'End Sub
'
'Private Sub ddFcstType_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.ddFcstType.DataFieldList = "CodeID"
'    Me.ddFcstType.DataFieldToDisplay = "CodeID"
'
'    Me.ddFcstType.Columns(0).Name = "FcstType"
'    Me.ddFcstType.Columns(0).Caption = getTranslationResource("Forecast Type")
'    Me.ddFcstType.Columns(0).Width = 1000
'    Me.ddFcstType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddFcstType.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.ddFcstType.Columns(0).DataField = "CodeID"
'
'    Me.ddFcstType.Columns(1).Name = "Description"
'    Me.ddFcstType.Columns(1).Caption = getTranslationResource("Forecast Type Description")
'    Me.ddFcstType.Columns(1).Width = 2880
'    Me.ddFcstType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.ddFcstType.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.ddFcstType.Columns(1).DataField = "CodeDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths ddFcstType, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To ddFcstType.Columns.Count - 1
''        ddFcstType.Columns(IndexCounter).HasHeadBackColor = True
''        ddFcstType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ddFcstType_InitColumnProps)"
'End Sub
'
'
'Private Sub dgFreezePds_AfterColUpdate(ByVal ColIndex As Integer)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    If ColIndex = 1 Then
'        If dgFreezePds.Columns("FreezePds").Value > txtFFPForecastPds.Value Then
'            'ErrorMessage
'            strMessage = getTranslationResource("MSGBOX07309")
'            If StrComp(strMessage, "MSGBOX07309") = 0 Then _
'                    strMessage = "Freeze Period cannot be greater than Forecast Period. Please select a valid number from the given list."
'            MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'            dgFreezePds.Columns("FreezePds").Value = 0
'            dgFreezePds.Columns("FreezePds").Text = 0
'            Exit Sub
'        End If
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_AfterColUpdate)"
'End Sub
'
'Private Sub dgFreezePds_AfterInsert(RtnDispErrMsg As Integer)
'On Error GoTo ErrorHandler
'
'    If Cn.Errors.Count > 0 Then
'        RtnDispErrMsg = True
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_AfterInsert)"
'End Sub
'
'Private Sub dgFreezePds_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim InvalidData As Boolean
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    InvalidData = UserInputValidation(dgFreezePds.Name, strMessage, ColIndex)
'
'    If InvalidData = True Then
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        dgFreezePds.DataChanged = False
'    End If
'
'    Cancel = InvalidData
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_BeforeColUpdate)"
'End Sub
'
'Private Sub dgFreezePds_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
'On Error GoTo ErrorHandler
'
'    Dim Result As Long
'    Dim strMessage As String
'    Dim strMessage1 As String
'
'    'Cancel the default prompt
'    DispPromptMsg = False
'
'    If gAccessLvl = g_ACCESS_CREATEMODIFY _
'    And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'    Then
'        'Display a confirmation msgbox
'        strMessage = getTranslationResource("MSGBOX07304")
'        If StrComp(strMessage, "MSGBOX07304") = 0 Then strMessage = "Delete the"
'
'        strMessage = strMessage + " " + str(dgFreezePds.SelBookmarks.Count) + " "
'        strMessage1 = getTranslationResource("MSGBOX07307")
'        If StrComp(strMessage1, "MSGBOX07307") = 0 Then strMessage1 = "selected Forecast Freeze Period records?"
'        strMessage = strMessage + strMessage1
'
'        Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ForecastMaintenance.Caption))
'
'        Select Case Result
'        Case vbYes
'            'User chose to delete. Do nothing
'        Case vbNo
'            'User chose to Cancel
'            Cancel = True
'        End Select
'    Else
'        'Cancel the delete request
'        Cancel = True
'
'        strMessage = getTranslationResource("MSGBOX07311")
'        If StrComp(strMessage, "MSGBOX07311") = 0 Then strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_BeforeDelete)"
'End Sub
'
'Private Sub dgFreezePds_Click()
'On Error GoTo ErrorHandler
'
'    m_ActiveGridID = ACTIVEGRID_FreezePds
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_Click)"
'
'End Sub
'
'Private Sub dgFreezePds_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dgFreezePds.Columns(0).Name = "Lcid"
'    Me.dgFreezePds.Columns(0).Caption = getTranslationResource("Location")
'    Me.dgFreezePds.Columns(0).Width = 2880
'    Me.dgFreezePds.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgFreezePds.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dgFreezePds.Columns(0).Style = ssStyleEdit
'    Me.dgFreezePds.Columns(0).FieldLen = 12
'    Me.dgFreezePds.Columns(0).DataType = vbString
'    Me.dgFreezePds.Columns(0).DropDownHwnd = Me.ddAIMLocations.hwnd
'
'    Me.dgFreezePds.Columns(1).Name = "FreezePds"
'    Me.dgFreezePds.Columns(1).Caption = getTranslationResource("Freeze Period")
'    Me.dgFreezePds.Columns(1).DataType = vbInteger
'    Me.dgFreezePds.Columns(1).Width = 2880
'    Me.dgFreezePds.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgFreezePds.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dgFreezePds.Columns(1).Style = ssStyleComboBox
'
'    If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'        If rsForecast!FcstPds >= 1 Then
'            For IndexCounter = 1 To rsForecast!FcstPds
'                Me.dgFreezePds.Columns(1).AddItem IndexCounter
'            Next
'        End If
'    End If
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgFreezePds, ACW_EXPAND
'    End If
'
'    Me.dgFreezePds.RowNavigation = ssRowNavigationUDLock
'
'    For IndexCounter = 0 To dgFreezePds.Columns.Count - 1
''        dgFreezePds.Columns(IndexCounter).HasHeadBackColor = True
''        dgFreezePds.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'        If dgFreezePds.Columns(IndexCounter).Locked = False Then dgFreezePds.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_InitColumnProps)"
'End Sub
'
'
'Private Sub dgFreezePds_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgFreezePds.Name, strMessage) Then
'        rsFreezePds.AddNew
'
'        rsFreezePds!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) _
'        And Trim(RowBuf.Value(0, 0)) <> "" Then
'            rsFreezePds!LcId = RowBuf.Value(0, 0)
'        Else
'            If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
'                rsAIMLocations.MoveFirst
'                rsFreezePds!LcId = rsAIMLocations!LcId
'            Else
'                rsFreezePds!LcId = ""
'            End If
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) _
'        And Trim(RowBuf.Value(0, 1)) <> "" Then
'            rsFreezePds!FreezePds = RowBuf.Value(0, 1)
'        Else
'            rsFreezePds!FreezePds = 0
'        End If
'
'        f_ChildTable_Update dgFreezePds.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
''        rsFreezePds.CancelUpdate
'        Exit Sub
'    End If
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07304")
'        If StrComp(strMessage, "ERRMSG07304") = 0 Then strMessage = "Error editing AIM Forecast Freeze Periods record."
'        ADOErrorHandler Cn, strMessage
'        rsFreezePds.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07304")
'        If StrComp(strMessage, "STATMSG07304") = 0 Then strMessage = "AIM Forecast Freeze Periods record successfully updated."
'        Write_Message strMessage
'    End If
'
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_UnboundAddData)"
'    rsFreezePds.CancelUpdate
'End Sub
'
'Private Sub dgFreezePds_UnboundDeleteRow(bookmark As Variant)
'On Error GoTo ErrorHandler
'
'    rsFreezePds.bookmark = bookmark
'    rsFreezePds.Delete
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_UnboundDeleteRow)"
'End Sub
'
'Private Sub dgFreezePds_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
'On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        'Going up or down?
'        If NumberOfRowsToMove = 0 Then
'            Exit Sub
'        ElseIf NumberOfRowsToMove < 0 Then
'            rsFreezePds.MoveLast
'        Else
'            rsFreezePds.MoveFirst
'        End If
'
'    Else
'        'Line up the bookmark of the recordset
'        'with the grid's StartLocation
'        rsFreezePds.bookmark = StartLocation
'
'    End If
'
'    'Note: Do not use StartLocation because it could be null
'    rsFreezePds.Move NumberOfRowsToMove
'
'    'Set the new location in the grid
'    NewLocation = rsFreezePds.bookmark
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_UnboundPositionData)"
'End Sub
'
'Private Sub dgFreezePds_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RowCounter As Integer
'    Dim RowIndex As Integer
'
'    If Not f_IsRecordsetOpenAndPopulated(rsFreezePds) Then Exit Sub
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            rsFreezePds.MoveLast
'        Else
'            rsFreezePds.MoveFirst
'        End If
'
'    Else
'        rsFreezePds.bookmark = StartLocation
'        If ReadPriorRows Then
'            rsFreezePds.MovePrevious
'        Else
'            rsFreezePds.MoveNext
'        End If
'
'    End If
'
'    For RowIndex = 0 To RowBuf.RowCount - 1
'
'        If rsFreezePds.BOF Or rsFreezePds.eof Then Exit For
'
'        Select Case RowBuf.ReadType
'            Case 0  'All data must be read
'                RowBuf.Value(RowIndex, 0) = rsFreezePds!LcId
'                RowBuf.Value(RowIndex, 1) = rsFreezePds!FreezePds
'
'            Case 1  'Only bookmarks must be read
'                'That is done anyway, so leave it after end select
'            Case Else
'                'Cases 2 and 3 are not used by DBGrid... yet?
'        End Select
'        RowBuf.bookmark(RowIndex) = rsFreezePds.bookmark
'
'        If ReadPriorRows Then
'            rsFreezePds.MovePrevious
'        Else
'            rsFreezePds.MoveNext
'        End If
'
'        RowCounter = RowCounter + 1
'    Next RowIndex
'
'    RowBuf.RowCount = RowCounter
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_UnboundReadData)"
'End Sub
'
'Private Sub dgFreezePds_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    rsFreezePds.bookmark = WriteLocation
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgFreezePds.Name, strMessage) Then
'        rsFreezePds!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) Then
'            rsFreezePds!LcId = RowBuf.Value(0, 0)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) Then
'            rsFreezePds!FreezePds = RowBuf.Value(0, 1)
'        End If
'
'        'Update the Forecast Freeze Pds Table
'        f_ChildTable_Update dgFreezePds.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        'Cancel update
'        f_ChildTable_Update dgFreezePds.Name, True
'    End If
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07304")
'        If StrComp(strMessage, "ERRMSG07304") = 0 Then strMessage = "Error editing AIM Forecast Freeze Periods record."
'        ADOErrorHandler Cn, strMessage
'        rsFreezePds.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07304")
'        If StrComp(strMessage, "STATMSG07304") = 0 Then strMessage = "AIM Forecast Freeze Periods record successfully updated."
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreezePds_UnboundWriteData)"
'    rsFreezePds.CancelUpdate
'End Sub
'
'Private Sub dgMainUserAccess_AfterInsert(RtnDispErrMsg As Integer)
'On Error GoTo ErrorHandler
'
'    If Cn.Errors.Count > 0 Then
'        RtnDispErrMsg = True
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_AfterInsert)"
'End Sub
'
'Private Sub dgMainUserAccess_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim InvalidData As Boolean
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    InvalidData = UserInputValidation(dgMainUserAccess.Name, strMessage, ColIndex)
'
'    If InvalidData = True Then
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        dgMainUserAccess.DataChanged = False
'    End If
'
'    Cancel = InvalidData
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_BeforeColUpdate)"
'End Sub
'
'Private Sub dgMainUserAccess_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
'On Error GoTo ErrorHandler
'
'    Dim Result As Long
'    Dim strMessage As String
'    Dim strMessage1 As String
'
'    'Cancel the default prompt
'    DispPromptMsg = False
'
'    If gAccessLvl = g_ACCESS_CREATEMODIFY _
'    And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'    Then
'        'Display a confirmation msgbox
'        strMessage = getTranslationResource("MSGBOX07304")
'        If StrComp(strMessage, "MSGBOX07304") = 0 Then strMessage = "Delete the"
'
'        strMessage = strMessage + " " + str(dgMainUserAccess.SelBookmarks.Count) + " "
'        strMessage1 = getTranslationResource("MSGBOX07310")
'        If StrComp(strMessage1, "MSGBOX07310") = 0 Then strMessage1 = "selected Forecast Maintenance User Access records?"
'        strMessage = strMessage + strMessage1
'
'        Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ForecastMaintenance.Caption))
'
'        Select Case Result
'        Case vbYes
'            'User chose to delete. Do nothing
'        Case vbNo
'            'User chose to Cancel
'            Cancel = True
'        End Select
'    Else
'        'Cancel the delete request
'        Cancel = True
'
'        strMessage = getTranslationResource("MSGBOX07311")
'        If StrComp(strMessage, "MSGBOX07311") = 0 Then strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_BeforeDelete )"
'End Sub
'
'Private Sub dgMainUserAccess_Click()
'On Error GoTo ErrorHandler
'
'    m_ActiveGridID = ACTIVEGRID_MainUserAccess
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_Click)"
'
'End Sub
'
'Private Sub dgMainUserAccess_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    'Define Columns
'    Me.dgMainUserAccess.Columns(0).Name = "UserID"
'    Me.dgMainUserAccess.Columns(0).Caption = getTranslationResource("User ID")
'    Me.dgMainUserAccess.Columns(0).Width = 3000
'    Me.dgMainUserAccess.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgMainUserAccess.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dgMainUserAccess.Columns(0).Style = ssStyleEdit
'    Me.dgMainUserAccess.Columns(0).FieldLen = 12
'    Me.dgMainUserAccess.Columns(0).DataType = vbString
'    Me.dgMainUserAccess.Columns(0).DropDownHwnd = Me.ddAIMUsers.hwnd
'
'    Me.dgMainUserAccess.Columns(1).Name = "AccessType"
'    Me.dgMainUserAccess.Columns(1).Caption = getTranslationResource("Access Type")
'    Me.dgMainUserAccess.Columns(1).Width = 2000
'    Me.dgMainUserAccess.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgMainUserAccess.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dgMainUserAccess.Columns(1).Style = ssStyleEdit
'    Me.dgMainUserAccess.Columns(1).FieldLen = 30
'    Me.dgMainUserAccess.Columns(1).DataType = vbString
'    Me.dgMainUserAccess.Columns(1).DropDownHwnd = Me.ddAccessType.hwnd
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgMainUserAccess, ACW_EXPAND
'    End If
'
'    Me.dgMainUserAccess.RowNavigation = ssRowNavigationUDLock
'
'    For IndexCounter = 0 To dgMainUserAccess.Columns.Count - 1
''        dgMainUserAccess.Columns(IndexCounter).HasHeadBackColor = True
''        dgMainUserAccess.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'        If dgMainUserAccess.Columns(IndexCounter).Locked = False Then dgMainUserAccess.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_InitColumnProps)"
'End Sub
'
'Private Sub dgMainUserAccess_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
'On Error GoTo ErrorHandler
'
'    ssPrintInfo.Portrait = False
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_PrintBegin)"
'End Sub
'
'Private Sub dgMainUserAccess_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgMainUserAccess.Name, strMessage) Then
'        rsMainUserAccess.AddNew
'
'        rsMainUserAccess!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) _
'        And Trim(RowBuf.Value(0, 0)) <> "" Then
'            rsMainUserAccess!UserId = RowBuf.Value(0, 0)
'        Else
'            If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
'                rsAIMUsers.MoveFirst
'                rsMainUserAccess!UserId = rsAIMUsers!UserId
'            Else
'                rsMainUserAccess!UserId = ""
'            End If
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) _
'        And Trim(RowBuf.Value(0, 1)) <> "" Then
'            rsMainUserAccess!AccessType = RowBuf.Value(0, 1)
'        Else
'            rsMainUserAccess!AccessType = g_ACCESS_NONE   'Default to No Access
'        End If
'
'        f_ChildTable_Update dgMainUserAccess.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
''        rsMainUserAccess.CancelUpdate
'        Exit Sub
'    End If
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07301")
'        If StrComp(strMessage, "ERRMSG07301") = 0 Then strMessage = "Error adding AIM Forecast Maintenance User Access Record."
'        ADOErrorHandler Cn, strMessage
'        rsMainUserAccess.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07301")
'        If StrComp(strMessage, "STATMSG07301") = 0 Then strMessage = "AIM Forecast Maintenance User Access record successfully updated."
'        Write_Message strMessage
'        rsMainUserAccess.MoveLast
'        NewRowBookmark = rsMainUserAccess.bookmark
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_UnboundAddData)"
'    rsMainUserAccess.CancelUpdate
'End Sub
'
'Private Sub dgMainUserAccess_UnboundDeleteRow(bookmark As Variant)
'On Error GoTo ErrorHandler
'
'    rsMainUserAccess.bookmark = bookmark
'    rsMainUserAccess.Delete
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_UnboundDeleteRow)"
'End Sub
'
'Private Sub dgMainUserAccess_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
'On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        'Going up or down?
'        If NumberOfRowsToMove = 0 Then
'            Exit Sub
'        ElseIf NumberOfRowsToMove < 0 Then
'            rsMainUserAccess.MoveLast
'        Else
'            rsMainUserAccess.MoveFirst
'        End If
'
'    Else
'        'Line up the bookmark of the recordset
'        'with the grid's StartLocation
'        rsMainUserAccess.bookmark = StartLocation
'
'    End If
'
'    'Note: Do not use StartLocation because it could be null
'    rsMainUserAccess.Move NumberOfRowsToMove
'
'    'Set the new location in the grid
'    NewLocation = rsMainUserAccess.bookmark
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_UnboundPositionData)"
'End Sub
'
'Private Sub dgMainUserAccess_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RowCounter As Integer
'    Dim RowIndex As Integer
'
'    If Not f_IsRecordsetOpenAndPopulated(rsMainUserAccess) Then Exit Sub
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            rsMainUserAccess.MoveLast
'        Else
'            rsMainUserAccess.MoveFirst
'        End If
'
'    Else
'        rsMainUserAccess.bookmark = StartLocation
'        If ReadPriorRows Then
'            rsMainUserAccess.MovePrevious
'        Else
'            rsMainUserAccess.MoveNext
'        End If
'
'    End If
'
'    For RowIndex = 0 To RowBuf.RowCount - 1
'
'        If rsMainUserAccess.BOF Or rsMainUserAccess.eof Then Exit For
'        Select Case RowBuf.ReadType
'            Case 0  'All data must be read
'                RowBuf.Value(RowIndex, 0) = rsMainUserAccess!UserId
'                RowBuf.Value(RowIndex, 1) = rsMainUserAccess!AccessType
'
'            Case 1  'Only bookmarks must be read
'                'That is done anyway, so leave it after end select
'            Case Else
'                'Cases 2 and 3 are not used by DBGrid... yet?
'        End Select
'
'        RowBuf.bookmark(RowIndex) = rsMainUserAccess.bookmark
'
'        If ReadPriorRows Then
'            rsMainUserAccess.MovePrevious
'        Else
'            rsMainUserAccess.MoveNext
'        End If
'
'        RowCounter = RowCounter + 1
'    Next RowIndex
'
'    RowBuf.RowCount = RowCounter
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_UnboundReadData)"
'End Sub
'
'Private Sub dgMainUserAccess_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    rsMainUserAccess.bookmark = WriteLocation
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgMainUserAccess.Name, strMessage) Then
'        rsMainUserAccess!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) Then
'            rsMainUserAccess!UserId = RowBuf.Value(0, 0)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) _
'        And Trim(RowBuf.Value(0, 1)) <> "" Then
'            rsMainUserAccess!AccessType = RowBuf.Value(0, 1)
'        Else
'            rsMainUserAccess!AccessType = g_ACCESS_NONE   'Default to No Access
'        End If
'
'        'Update the Forecast User Access Table
'        f_ChildTable_Update dgMainUserAccess.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        'Cancel update
'        f_ChildTable_Update dgMainUserAccess.Name, True
'    End If
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07301")
'        If StrComp(strMessage, "ERRMSG07301") = 0 Then strMessage = "Error adding AIM Forecast Maintenance User Access Record."
'        ADOErrorHandler Cn, strMessage
'        rsMainUserAccess.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07301")
'        If StrComp(strMessage, "STATMSG07301") = 0 Then strMessage = "AIM Forecast Maintenance User Access record successfully updated."
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgMainUserAccess_UnboundWriteData)"
'    rsMainUserAccess.CancelUpdate
'End Sub
'Private Sub dgUserAccess_AfterInsert(RtnDispErrMsg As Integer)
'On Error GoTo ErrorHandler
'
'    If Cn.Errors.Count > 0 Then
'        RtnDispErrMsg = True
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_AfterInsert)"
'End Sub
'
'Private Sub dgUserAccess_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim InvalidData As Boolean
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    InvalidData = UserInputValidation(dgUserAccess.Name, strMessage, ColIndex)
'
'    If InvalidData = True Then
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        dgUserAccess.DataChanged = False
'    End If
'
'    Cancel = InvalidData
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_BeforeColUpdate)"
'End Sub
'
'Private Sub dgUserAccess_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
'On Error GoTo ErrorHandler
'
'    Dim Result As Long
'    Dim strMessage As String
'    Dim strMessage1 As String
'
'    'Cancel the default prompt
'    DispPromptMsg = False
'
'    If gAccessLvl = g_ACCESS_CREATEMODIFY _
'    And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'    Then
'        'Display a confirmation msgbox
'        strMessage = getTranslationResource("MSGBOX07304")
'        If StrComp(strMessage, "MSGBOX07304") = 0 Then strMessage = "Delete the"
'
'        strMessage = strMessage + " " + str(dgUserAccess.SelBookmarks.Count) + " "
'        strMessage1 = getTranslationResource("MSGBOX07305")
'        If StrComp(strMessage1, "MSGBOX07305") = 0 Then strMessage1 = "selected Forecast User Access records?"
'        strMessage = strMessage + strMessage1
'
'        Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ForecastMaintenance.Caption))
'
'        Select Case Result
'        Case vbYes
'            'User chose to delete. Do nothing
'        Case vbNo
'            'User chose to Cancel
'            Cancel = True
'        End Select
'    Else
'        'Cancel the delete request
'        Cancel = True
'
'        strMessage = getTranslationResource("MSGBOX07311")
'        If StrComp(strMessage, "MSGBOX07311") = 0 Then strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
'    End If
'
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_BeforeDelete )"
'End Sub
'
'Private Sub dgUserAccess_Click()
'On Error GoTo ErrorHandler
'
'    m_ActiveGridID = ACTIVEGRID_UserAccess
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecastDetail_Click)"
'End Sub
'
'Private Sub dgUserAccess_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    'Define Columns
'    Me.dgUserAccess.Columns(0).Name = "UserID"
'    Me.dgUserAccess.Columns(0).Caption = getTranslationResource("User ID")
'    Me.dgUserAccess.Columns(0).Width = 3000
'    Me.dgUserAccess.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgUserAccess.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dgUserAccess.Columns(0).Style = ssStyleEdit
'    Me.dgUserAccess.Columns(0).FieldLen = 12
'    Me.dgUserAccess.Columns(0).DataType = vbString
'    Me.dgUserAccess.Columns(0).DropDownHwnd = Me.ddAIMUsers.hwnd
'
'    Me.dgUserAccess.Columns(1).Name = "FcstType"
'    Me.dgUserAccess.Columns(1).Caption = getTranslationResource("Forecast Type")
'    Me.dgUserAccess.Columns(1).Width = 3000
'    Me.dgUserAccess.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgUserAccess.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dgUserAccess.Columns(1).Style = ssStyleEdit
'    Me.dgUserAccess.Columns(1).FieldLen = 30
'    Me.dgUserAccess.Columns(1).DataType = vbString
'    Me.dgUserAccess.Columns(1).DropDownHwnd = Me.ddFcstType.hwnd
'
'    Me.dgUserAccess.Columns(2).Name = "AccessType"
'    Me.dgUserAccess.Columns(2).Caption = getTranslationResource("Access Type")
'    Me.dgUserAccess.Columns(2).Width = 2000
'    Me.dgUserAccess.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgUserAccess.Columns(2).Alignment = ssCaptionAlignmentLeft
'    Me.dgUserAccess.Columns(2).Style = ssStyleEdit
'    Me.dgUserAccess.Columns(2).FieldLen = 30
'    Me.dgUserAccess.Columns(2).DataType = vbString
'    Me.dgUserAccess.Columns(2).DropDownHwnd = Me.ddAccessType.hwnd
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgUserAccess, ACW_EXPAND
'    End If
'
'    Me.dgUserAccess.RowNavigation = ssRowNavigationUDLock
'
'    For IndexCounter = 0 To dgUserAccess.Columns.Count - 1
''        dgUserAccess.Columns(IndexCounter).HasHeadBackColor = True
''        dgUserAccess.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'        If dgUserAccess.Columns(IndexCounter).Locked = False Then dgUserAccess.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_InitColumnProps)"
'End Sub
'
'Private Sub dgUserAccess_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
'On Error GoTo ErrorHandler
'
'    ssPrintInfo.Portrait = False
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_PrintBegin)"
'End Sub
'
'Private Sub dgUserAccess_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim strMessage1 As String
'    Dim strMessage2 As String
'
'    'Set printer properties
'    strMessage = getTranslationResource("RPTMSG07300")
'    If StrComp(strMessage, "RPTMSG07300") = 0 Then strMessage = "Date:"
'    strMessage1 = getTranslationResource("RPTMSG07301")
'    If StrComp(strMessage1, "RPTMSG07301") = 0 Then strMessage = "AIM Forecast User Access"
'    strMessage2 = getTranslationResource("RPTMSG07302")
'    If StrComp(strMessage2, "RPTMSG07302") = 0 Then strMessage = "Page:"
'
'    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + strMessage1 + _
'                            vbTab + strMessage2 + " <page number>"
'
'    ssPrintInfo.Portrait = False
'    ssPrintInfo.PrintHeaders = ssTopOfPage
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_PrintInitialize)"
'End Sub
'
'
'Private Sub dgUserAccess_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgUserAccess.Name, strMessage) Then
'        rsUserAccess.AddNew
'
'        rsUserAccess!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) _
'        And Trim(RowBuf.Value(0, 0)) <> "" Then
'            rsUserAccess!UserId = RowBuf.Value(0, 0)
'        Else
'            If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
'                rsAIMUsers.MoveFirst
'                rsUserAccess!UserId = rsAIMUsers!UserId
'            Else
'                rsUserAccess!UserId = ""
'            End If
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) _
'        And Trim(RowBuf.Value(0, 1)) <> "" Then
'            rsUserAccess!FcstType = RowBuf.Value(0, 1)
'        Else
'            If f_IsRecordsetOpenAndPopulated(rsFcstType) Then
'                rsFcstType.MoveFirst
'                rsUserAccess!FcstType = rsFcstType!FcstType
'            Else
'                rsUserAccess!FcstType = ""
'            End If
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 2)) _
'        And Trim(RowBuf.Value(0, 2)) <> "" Then
'            rsUserAccess!AccessType = RowBuf.Value(0, 2)
'        Else
'            rsUserAccess!AccessType = g_ACCESS_NONE   'Default to No Access
'        End If
'
'        f_ChildTable_Update dgUserAccess.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
''        rsUserAccess.CancelUpdate
'        Exit Sub
'    End If
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07302")
'        If StrComp(strMessage, "ERRMSG07302") = 0 Then strMessage = "Error editing AIM Forecast User Access record."
'        ADOErrorHandler Cn, strMessage
'        rsUserAccess.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07302")
'        If StrComp(strMessage, "STATMSG07302") = 0 Then strMessage = "AIM Forecast User Access record successfully updated."
'        Write_Message strMessage
'        rsUserAccess.MoveLast
'        NewRowBookmark = rsUserAccess.bookmark
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_UnboundAddData)"
'    rsUserAccess.CancelUpdate
'End Sub
'
'Private Sub dgUserAccess_UnboundDeleteRow(bookmark As Variant)
'On Error GoTo ErrorHandler
'
'    rsUserAccess.bookmark = bookmark
'    rsUserAccess.Delete
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_UnboundDeleteRow)"
'End Sub
'
'Private Sub dgUserAccess_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
'On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        'Going up or down?
'        If NumberOfRowsToMove = 0 Then
'            Exit Sub
'        ElseIf NumberOfRowsToMove < 0 Then
'            rsUserAccess.MoveLast
'        Else
'            rsUserAccess.MoveFirst
'        End If
'
'    Else
'        'Line up the bookmark of the recordset
'        'with the grid's StartLocation
'        rsUserAccess.bookmark = StartLocation
'
'    End If
'
'    'Note: Do not use StartLocation because it could be null
'    rsUserAccess.Move NumberOfRowsToMove
'
'    'Set the new location in the grid
'    NewLocation = rsUserAccess.bookmark
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_UnboundPositionData)"
'End Sub
'
'Private Sub dgUserAccess_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RowCounter As Integer
'    Dim RowIndex As Integer
'
'    If Not f_IsRecordsetOpenAndPopulated(rsUserAccess) Then Exit Sub
'
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            rsUserAccess.MoveLast
'        Else
'            rsUserAccess.MoveFirst
'        End If
'
'    Else
'        rsUserAccess.bookmark = StartLocation
'        If ReadPriorRows Then
'            rsUserAccess.MovePrevious
'        Else
'            rsUserAccess.MoveNext
'        End If
'
'    End If
'
'    For RowIndex = 0 To RowBuf.RowCount - 1
'
'        If rsUserAccess.BOF Or rsUserAccess.eof Then Exit For
'
'        Select Case RowBuf.ReadType
'            Case 0  'All data must be read
'                RowBuf.Value(RowIndex, 0) = rsUserAccess!UserId
'                RowBuf.Value(RowIndex, 1) = rsUserAccess!FcstType
'                RowBuf.Value(RowIndex, 2) = rsUserAccess!AccessType
'
'            Case 1  'Only bookmarks must be read
'                'That is done anyway, so leave it after end select
'            Case Else
'                'Cases 2 and 3 are not used by DBGrid... yet?
'        End Select
'
'        RowBuf.bookmark(RowIndex) = rsUserAccess.bookmark
'
'        If ReadPriorRows Then
'            rsUserAccess.MovePrevious
'        Else
'            rsUserAccess.MoveNext
'        End If
'
'        RowCounter = RowCounter + 1
'    Next RowIndex
'
'    RowBuf.RowCount = RowCounter
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_UnboundReadData)"
'End Sub
'
'Private Sub dgUserAccess_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear residual errors from the Connection object
'    Cn.Errors.Clear
'
'    rsUserAccess.bookmark = WriteLocation
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Sub
'
'    If Not UserInputValidation(dgUserAccess.Name, strMessage) Then
'        rsUserAccess!FcstId = rsForecast!FcstId
'
'        If Not IsNull(RowBuf.Value(0, 0)) Then
'            rsUserAccess!UserId = RowBuf.Value(0, 0)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 1)) Then
'            rsUserAccess!FcstType = RowBuf.Value(0, 1)
'        End If
'
'        If Not IsNull(RowBuf.Value(0, 2)) _
'        And Trim(RowBuf.Value(0, 2)) <> "" Then
'            rsUserAccess!AccessType = RowBuf.Value(0, 2)
'        Else
'            rsUserAccess!AccessType = g_ACCESS_NONE   'Default to No Access
'        End If
'
'        f_ChildTable_Update dgUserAccess.Name, False
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        'Cancel update
'        f_ChildTable_Update dgUserAccess.Name, True
'    End If
'
'
'    If Cn.Errors.Count > 0 Then
'        strMessage = getTranslationResource("ERRMSG07302")
'        If StrComp(strMessage, "ERRMSG07302") = 0 Then strMessage = "Error editing AIM Forecast User Access record."
'        ADOErrorHandler Cn, strMessage
'        rsUserAccess.CancelUpdate
'    Else
'        strMessage = getTranslationResource("STATMSG07302")
'        If StrComp(strMessage, "STATMSG07302") = 0 Then strMessage = "AIM Forecast User Access record successfully updated."
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserAccess_UnboundWriteData)"
'    rsUserAccess.CancelUpdate
'End Sub
'
'Private Sub Form_Activate()
'On Error GoTo ErrorHandler
'
'    Screen.MousePointer = vbHourglass
'
'    'Check for an open connection
'    If Cn.State <> adStateOpen Then
'        Unload Me
'        Exit Sub
'    End If
'
'    'Set State of window list State Button
'    gbUpdating = True
'    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
'    gbUpdating = False
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Activate)"
'End Sub
'
'Private Sub Form_Load()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strMessage As String
'    Dim AIM_GetSaVersion_Sp As ADODB.Command
'
'    Screen.MousePointer = vbHourglass
'
'    GetTranslatedCaptions Me
'
'    'Housekeeping
'    strMessage = getTranslationResource("STATMSG07300")
'    If StrComp(strMessage, "STATMSG07300") = 0 Then strMessage = "Initializing AIM Forecast Table Maintenenace..."
'    Write_Message strMessage
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
'    If RtnCode <> SUCCEED Then Exit Sub
'
'    'Initialize Commands and Recordsets
'    Set AIM_GetSaVersion_Sp = New ADODB.Command
'    With AIM_GetSaVersion_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GetSaVersion_Sp"
'        .Parameters.Refresh
'    End With
'    'Get the Seasonality Table Version Number
'    AIM_GetSaVersion_Sp.Execute , , adExecuteNoRecords
'    SAVersion = AIM_GetSaVersion_Sp.Parameters("@RETURN_VALUE").Value
'
'    'Open the Recordset
'    RtnCode = f_AIMForecast_GetKey(rsForecast, FcstId, SQL_GetFirst)
'    'If there are no records, set to add new mode
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then
'        f_AIMForecast_InitializeNew
'        atForecastCriteria.Tabs(3).Enabled = False
'        atForecastCriteria.Tabs(4).Enabled = False
'
'    Else
'        GetForecastInformation IDX_DCFORECAST_FCSTID
'        Me.dcForecast(IDX_DCFORECAST_FCSTID).Text = rsForecast!FcstId   'this will trigger refresh
'    End If
'
'    'Add to Windows List
'    AddToWindowList Me.Caption
'
'    '************************************************************
'    'Mar 07 2002 - Component Conversion Additions
'    'Make the spin button visible
'    Me.txtAdjustPct.Spin.Visible = 1
'    Me.txtFcstPds.Spin.Visible = 1
'    Me.txtRevInterval.Spin.Visible = 1
'    Me.txtFcstStartDate.DropDown.Visible = 1
'    Me.txtNextRevDate.DropDown.Visible = 1
'    '************************************************************
'
'    'Windup
'    FcstIdBookMark = ""
'    Write_Message ""
'    Screen.MousePointer = vbNormal
'
'    If Not (AIM_GetSaVersion_Sp Is Nothing) Then Set AIM_GetSaVersion_Sp.ActiveConnection = Nothing
'    Set AIM_GetSaVersion_Sp = Nothing
'
'Exit Sub
'ErrorHandler:
'    If Not (AIM_GetSaVersion_Sp Is Nothing) Then Set AIM_GetSaVersion_Sp.ActiveConnection = Nothing
'    Set AIM_GetSaVersion_Sp = Nothing
'    f_HandleErr Me.Caption & "(Form_Load)"
'End Sub
'
'
'Private Sub Form_Unload(Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    'Check the recordset if they are not valid then exit this function
'     If Not f_IsRecordsetValidAndOpen(rsForecast) Then
'        Exit Sub
'    End If
'    'Save unsaved updates, first
'    f_AIMForecast_Commit True
'    If dgForecastDetail.DataChanged Then dgForecastDetail.Update
'    If dgFreezePds.DataChanged Then dgFreezePds.Update
'    If dgUserAccess.DataChanged Then dgUserAccess.Update
'    If dgMainUserAccess.DataChanged Then dgMainUserAccess.Update
'
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
'    Set Cn = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
'    Set rsForecast = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsUserAccess) Then rsUserAccess.Close
'    Set rsUserAccess = Nothing
'    If f_IsRecordsetValidAndOpen(rsMainUserAccess) Then rsMainUserAccess.Close
'    Set rsMainUserAccess = Nothing
'    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
'    Set rsAIMUsers = Nothing
'    If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'    Set rsFcstType = Nothing
'    If f_IsRecordsetValidAndOpen(rsAccessType) Then rsAccessType.Close
'    Set rsAccessType = Nothing
'
'
'    If f_IsRecordsetValidAndOpen(rsForecastDetail) Then rsForecastDetail.Close
'    Set rsForecastDetail = Nothing
'    If f_IsRecordsetValidAndOpen(rsItems) Then rsItems.Close
'    Set rsItems = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsFreezePds) Then rsFreezePds.Close
'    Set rsFreezePds = Nothing
'    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
'    Set rsAIMLocations = Nothing
'
'    Set xForecastLinkList = Nothing
'
'    'Remove form from window list
'    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        'Ignore
'    Else
'        f_HandleErr Me.Caption & "(Form_Unload)"
'    End If
'    Resume Next
'End Sub
'
'Private Sub fraForecastRevCycle_DragDrop(Source As Control, X As Single, Y As Single)
'
'End Sub
'
'Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Clear Errors
'    Cn.Errors.Clear
'
'    Write_Message ""
'
'    'Alert user to possible change
'    Select Case UCase$(Tool.ID)
'    Case UCase$("ID_AddNew"), _
'        UCase$("ID_Close"), _
'        UCase$("ID_GetFirst"), _
'        UCase$("ID_GetLast"), _
'        UCase$("ID_GetNext"), _
'        UCase$("ID_GetPrev"), _
'        UCase$("ID_GoToBookMark"), _
'        UCase$("ID_LookUp")
'
'        f_AIMForecast_Commit True
'
'    End Select
'
'    Select Case UCase$(Tool.ID)
'    Case UCase$("ID_Close")
'        DestroyDetailRecordsets 2
'        DestroyDetailRecordsets 3
'        Unload Me
'        Exit Sub
'    Case UCase$("ID_ItemFilter")
'        ShowItemFilter
'
'    End Select
'
'    If atForecastCriteria.Tabs(1).Selected _
'    Or atForecastCriteria.Tabs(2).Selected _
'    Then
'        'Forecast Header/Criteria
'        ToolbarOptions_ForecastCriteria Tool
'
'    ElseIf atForecastCriteria.Tabs(3).Selected Then
'        'FreezeForecast
'        ToolbarOptions_FreezeForecast Tool
'
'    ElseIf atForecastCriteria.Tabs(4).Selected Then
'        'User Access
'        ToolbarOptions_UserAccess Tool
'
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
'End Sub
'
'Private Function ToolbarOptions_ForecastCriteria(Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strMessage As String
'
'    'Navigate
'    Select Case UCase$(Tool.ID)
'    Case UCase$("ID_AddNew")
'        f_AIMForecast_InitializeNew
'        Exit Function
'
'    Case UCase$("ID_Delete")
'        If gAccessLvl = g_ACCESS_CREATEMODIFY _
'        And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'        Then
'            If ckFcstLocked.Value = vbChecked Then
'                strMessage = getTranslationResource("MSGBOX07308")
'                If StrComp(strMessage, "MSGBOX07308") = 0 Then _
'                        strMessage = "This forecast is in use within the Forecast Repository. Modification not allowed "
'                MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'                Exit Function
'            Else
'                strMessage = getTranslationResource("MSGBOX07301")
'                If StrComp(strMessage, "MSGBOX07301") = 0 Then strMessage = "Delete the current Forecast?"
'                RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, getTranslationResource(Me.Caption))
'                If RtnCode = vbYes Then
'                    RtnCode = f_AIMForecast_Delete
'                End If
'            End If
'        Else
'            'la here -- anything to be cancelled?
'            strMessage = getTranslationResource("MSGBOX07311")
'            If StrComp(strMessage, "MSGBOX07311") = 0 Then strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'            MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
'        End If
'
'    Case UCase$("ID_GetFirst")
'        f_AIMForecast_GetKey rsForecast, FcstId, SQL_GetFirst
'
'    Case UCase$("ID_GetPrev")
'        f_AIMForecast_GetKey rsForecast, FcstId, SQL_Getlt
'
'    Case UCase$("ID_GetNext")
'        f_AIMForecast_GetKey rsForecast, FcstId, SQL_GetGT
'
'    Case UCase$("ID_GetLast")
'        f_AIMForecast_GetKey rsForecast, FcstId, SQL_GetLast
'
'    Case UCase$("ID_Save")
'        f_AIMForecast_Commit False
'
'    Case UCase$("ID_SetBookMark")
'        If FcstIdBookMark <> "" Then
'            FcstIdBookMark = ""
'            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
'        Else
'            FcstIdBookMark = FcstId
'            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
'        End If
'
'    Case UCase$("ID_GoToBookMark")
'        FcstId = FcstIdBookMark
'        f_AIMForecast_GetKey rsForecast, FcstId, SQL_GetEq
'
'    End Select
'
'    'Refresh
'    Select Case UCase$(Tool.ID)
'        Case UCase$("ID_GETFIRST"), _
'            UCase$("ID_GETPREV"), _
'            UCase$("ID_GETNEXT"), _
'            UCase$("ID_GETLAST"), _
'            UCase$("ID_GOTOBOOKMARK"), _
'            UCase$("ID_DELETE")
'            If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'                Me.dcForecast(IDX_DCFORECAST_FCSTID).Text = rsForecast!FcstId
'                m_NewFcst = False
'                'Show add, delete and the dropdown. Hide the text box
'                Me.tbNavigation.Tools("ID_AddNew").Enabled = True
'                tbNavigation.Tools("ID_Delete").Enabled = True
'                dcForecast(IDX_DCFORECAST_FCSTID).Visible = True
'                txtFcstId.Visible = False
'                txtFcstId.Text = rsForecast!FcstId
'                'f_AIMForecast_Refresh
'            Else
'                m_NewFcst = True
'                'Show add, delete and the dropdown. Hide the text box
'                Me.tbNavigation.Tools("ID_AddNew").Enabled = False
'                tbNavigation.Tools("ID_Delete").Enabled = False
'                dcForecast(IDX_DCFORECAST_FCSTID).Visible = False
'                txtFcstId.Visible = True
'            End If
'        Case Else
'
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ToolbarOptions_ForecastCriteria)"
'End Function
'
'Private Function ToolbarOptions_UserAccess(Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strMessage As String
'
'    If dgUserAccess.SelBookmarks.Count >= 1 _
'    Or m_ActiveGridID = ACTIVEGRID_UserAccess _
'    Then
'        'Save unsaved updates, first
'        If dgUserAccess.DataChanged Then dgUserAccess.Update
'
'        'Navigate
'        Select Case UCase$(Tool.ID)
'            Case UCase$("ID_GetFirst")
'                dgUserAccess.MoveFirst
'
'            Case UCase$("ID_GetPrev")
'                dgUserAccess.MovePrevious
'
'            Case UCase$("ID_GetNext")
'                dgUserAccess.MoveNext
'
'            Case UCase$("ID_GetLast")
'                dgUserAccess.MoveLast
'
'            Case UCase$("ID_AddNew")
'                dgUserAccess.AddNew
'
'            Case UCase$("ID_Delete")
'                Me.dgUserAccess.DeleteSelected
'
'            Case UCase$("ID_Save")
'                If dgUserAccess.DataChanged Then dgUserAccess.Update
'
'            Case UCase$("ID_SetBookMark")
'                If IsEmpty(vBookMark_UserAccess) _
'                Or IsNull(vBookMark_UserAccess) _
'                Then
'                    vBookMark_UserAccess = dgUserAccess.bookmark
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
'                Else
'                    vBookMark_UserAccess = Null
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
'                End If
'
'            Case UCase$("ID_GoToBookMark")
'                If Not IsEmpty(vBookMark_UserAccess) _
'                Or Not IsNull(vBookMark_UserAccess) _
'                Then
'                    dgUserAccess.bookmark = vBookMark_UserAccess
'                End If
'        End Select
'
'    ElseIf dgMainUserAccess.SelBookmarks.Count >= 1 _
'    Or m_ActiveGridID = ACTIVEGRID_MainUserAccess _
'    Then
'        'Save unsaved updates, first
'        If dgMainUserAccess.DataChanged Then dgMainUserAccess.Update
'
'        'Navigate
'        Select Case UCase$(Tool.ID)
'            Case UCase$("ID_GetFirst")
'                dgMainUserAccess.MoveFirst
'
'            Case UCase$("ID_GetPrev")
'                dgMainUserAccess.MovePrevious
'
'            Case UCase$("ID_GetNext")
'                dgMainUserAccess.MoveNext
'
'            Case UCase$("ID_GetLast")
'                dgMainUserAccess.MoveLast
'
'            Case UCase$("ID_AddNew")
'                dgMainUserAccess.AddNew
'
'            Case UCase$("ID_Delete")
'                Me.dgMainUserAccess.DeleteSelected
'
'            Case UCase$("ID_Save")
'                If dgMainUserAccess.DataChanged Then dgMainUserAccess.Update
'
'            Case UCase$("ID_SetBookMark")
'                If IsEmpty(vBookMark_UserAccess) _
'                Or IsNull(vBookMark_UserAccess) _
'                Then
'                    vBookMark_UserAccess = dgMainUserAccess.bookmark
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
'                Else
'                    vBookMark_UserAccess = Null
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
'                End If
'
'            Case UCase$("ID_GoToBookMark")
'                If Not IsEmpty(vBookMark_UserAccess) _
'                Or Not IsNull(vBookMark_UserAccess) _
'                Then
'                    dgMainUserAccess.bookmark = vBookMark_UserAccess
'                End If
'        End Select
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ToolbarOptions_UserAccess)"
'End Function
'
'Private Function ToolbarOptions_FreezeForecast(Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strMessage As String
'
'    'Navigate
'    If dgForecastDetail.SelBookmarks.Count >= 1 _
'    Or m_ActiveGridID = ACTIVEGRID_ForecastDetail _
'    Then
'        'Save unsaved updates, first
'        If dgForecastDetail.DataChanged Then dgForecastDetail.Update
'
'        Select Case UCase$(Tool.ID)
'            Case UCase$("ID_GetFirst")
'                dgForecastDetail.MoveFirst
'
'            Case UCase$("ID_GetPrev")
'                dgForecastDetail.MovePrevious
'
'            Case UCase$("ID_GetNext")
'                dgForecastDetail.MoveNext
'
'            Case UCase$("ID_GetLast")
'                dgForecastDetail.MoveLast
'
'            Case UCase$("ID_AddNew")
'                dgForecastDetail.AddNew
'
'            Case UCase$("ID_Delete")
'                Me.dgForecastDetail.DeleteSelected
'
'            Case UCase$("ID_Save")
'                If dgForecastDetail.DataChanged Then dgForecastDetail.Update
'
'            Case UCase$("ID_SetBookMark")
'                If IsEmpty(vBookMark_ForecastDetail) _
'                Or IsNull(vBookMark_ForecastDetail) _
'                Then
'                    vBookMark_ForecastDetail = dgForecastDetail.bookmark
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
'                Else
'                    vBookMark_ForecastDetail = Null
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
'                End If
'
'            Case UCase$("ID_GoToBookMark")
'                If Not IsEmpty(vBookMark_ForecastDetail) _
'                Or Not IsNull(vBookMark_ForecastDetail) _
'                Then
'                    dgForecastDetail.bookmark = vBookMark_ForecastDetail
'                End If
'
'        End Select
'
'    ElseIf dgFreezePds.SelBookmarks.Count >= 1 _
'    Or m_ActiveGridID = ACTIVEGRID_FreezePds _
'    Then
'        'Save unsaved updates, first
'        If dgFreezePds.DataChanged Then dgFreezePds.Update
'
'        Select Case UCase$(Tool.ID)
'            Case UCase$("ID_GetFirst")
'                dgFreezePds.MoveFirst
'
'            Case UCase$("ID_GetPrev")
'                dgFreezePds.MovePrevious
'
'            Case UCase$("ID_GetNext")
'                dgFreezePds.MoveNext
'
'            Case UCase$("ID_GetLast")
'                dgFreezePds.MoveLast
'
'            Case UCase$("ID_AddNew")
'                dgFreezePds.AddNew
'
'            Case UCase$("ID_Delete")
'                dgFreezePds.DeleteSelected
'
'            Case UCase$("ID_Save")
'                If dgFreezePds.DataChanged Then dgFreezePds.Update
'
'            Case UCase$("ID_SetBookMark")
'                If IsEmpty(vBookMark_FreezePds) _
'                Or IsNull(vBookMark_FreezePds) _
'                Then
'                    vBookMark_FreezePds = dgFreezePds.bookmark
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
'                Else
'                    vBookMark_FreezePds = Null
'                    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
'                End If
'
'            Case UCase$("ID_GoToBookMark")
'                If Not IsEmpty(vBookMark_FreezePds) _
'                Or Not IsNull(vBookMark_FreezePds) _
'                Then
'                    dgFreezePds.bookmark = vBookMark_FreezePds
'                End If
'
'        End Select
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ToolbarOptions_FreezeForecast)"
'End Function
'
'Private Function UpdateForecastDetail(p_FcstId As String, p_Item As String, p_LcID As String, p_ItemLocked As String)
'On Error GoTo ErrorHandler
'
'    Dim AIM_ForecastDetail_Save_Sp As ADODB.Command
'
'    Set AIM_ForecastDetail_Save_Sp = New ADODB.Command
'    With AIM_ForecastDetail_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastDetail_Save_Sp"
'        .Parameters.Refresh
'    End With
'
'    'Get the Seasonality Data
'    With AIM_ForecastDetail_Save_Sp
'
'        .Parameters("@FcstId").Value = p_FcstId
'        .Parameters("@LcID").Value = p_LcID
'        .Parameters("@Item").Value = p_Item
'        .Parameters("@ItemLocked").Value = p_ItemLocked
'
'        AIM_ForecastDetail_Save_Sp.Execute , , adExecuteNoRecords
'
'    End With
'
'    If Not (AIM_ForecastDetail_Save_Sp Is Nothing) Then Set AIM_ForecastDetail_Save_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastDetail_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_ForecastDetail_Save_Sp Is Nothing) Then Set AIM_ForecastDetail_Save_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastDetail_Save_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(UpdateForecastDetail)"
'End Function
'
'Private Function GetDetailRecordsets(p_Index As Long)
'On Error GoTo ErrorHandler
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'
'    'Close the recordsets, if open
'    'Get that specific one
'    Select Case p_Index
'        Case 2
'            BuildFreezePdsGrid
'
'            BuildForecastDetailGrid
'
'            'Set read-only fields for quick reference
'            SetTDBDate txtFFPStartDate, rsForecast!FcstStartDate
'            Me.txtFFPForecastPds.Value = IIf(rsForecast!FcstPds < txtFFPForecastPds.MinValue, _
'                            txtFFPForecastPds.MinValue, _
'                            (IIf(rsForecast!FcstPds > txtFFPForecastPds.MaxValue, txtFFPForecastPds.MaxValue, rsForecast!FcstPds)))
'            txtFFPForecastInterval.Text = Me.opFcstInterval(rsForecast!FcstInterval).Caption
'
'
'        Case 3
'            BuildUserAccessGrid
'
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetDetailRecordsets)"
'End Function
'
'Private Function DestroyDetailRecordsets(p_Index As Long)
'On Error GoTo ErrorHandler
'
'    Select Case p_Index
'        Case 2
'            If dgForecastDetail.DataChanged Then dgForecastDetail.Update
'            If dgFreezePds.DataChanged Then dgFreezePds.Update
'            If f_IsRecordsetValidAndOpen(rsFreezePds) Then rsFreezePds.Close
'            If f_IsRecordsetValidAndOpen(rsForecastDetail) Then rsForecastDetail.Close
'            dgForecastDetail.Reset
'            dgFreezePds.Reset
'
'            If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
'            If f_IsRecordsetValidAndOpen(rsItems) Then rsItems.Close
'            ddItems.Reset
'            ddAIMLocations.Reset
'
'        Case 3
'            If dgUserAccess.DataChanged Then dgUserAccess.Update
'            If dgMainUserAccess.DataChanged Then dgMainUserAccess.Update
'            If f_IsRecordsetValidAndOpen(rsUserAccess) Then rsUserAccess.Close
'            If f_IsRecordsetValidAndOpen(rsMainUserAccess) Then rsMainUserAccess.Close
'            dgUserAccess.Reset
'            dgMainUserAccess.Reset
'
'            If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
'            If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'            If f_IsRecordsetValidAndOpen(rsAccessType) Then rsAccessType.Close
'            ddAccessType.Reset
'            ddFcstType.Reset
'            ddAIMUsers.Reset
'
'    End Select
'
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(DestroyDetailRecordsets)"
'End Function
'
'Private Function BuildUserAccessGrid()
'On Error GoTo ErrorHandler
'
'    Dim AIM_UserAccess_Get_Sp As ADODB.Command
'    Dim AIM_MainUA_Get_Sp As ADODB.Command
'    Dim AIM_UserList_Get_Sp As ADODB.Command
'    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'
'    '************************************************************
'    'Create command object
'    Set AIM_UserAccess_Get_Sp = New ADODB.Command
'    With AIM_UserAccess_Get_Sp
'        .CommandType = adCmdStoredProc
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_ForecastUserAccess_Get_Sp"
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'    End With
'
'    'Build the UserAccess recordset
'    If f_IsRecordsetValidAndOpen(rsUserAccess) Then rsUserAccess.Close
'    Set rsUserAccess = Nothing
'    Set rsUserAccess = New ADODB.Recordset
'    With rsUserAccess
'        .CursorLocation = adUseClient
'        .CursorType = adOpenDynamic
'        .LockType = adLockOptimistic
'    End With
'    rsUserAccess.Open AIM_UserAccess_Get_Sp
'
'    dgUserAccess.ReBind
'    If f_IsRecordsetOpenAndPopulated(rsUserAccess) Then dgUserAccess.Rows = rsUserAccess.RecordCount
'
'    'Clean up
'    If Not (AIM_UserAccess_Get_Sp Is Nothing) Then Set AIM_UserAccess_Get_Sp.ActiveConnection = Nothing
'    Set AIM_UserAccess_Get_Sp = Nothing
'
'    '************************************************************
'    'Create command object
'    Set AIM_MainUA_Get_Sp = New ADODB.Command
'    With AIM_MainUA_Get_Sp
'        .CommandType = adCmdStoredProc
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_ForecastMainUserAccess_Get_Sp"
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'    End With
'
'    'Build the MainUserAccess recordset
'    If f_IsRecordsetValidAndOpen(rsMainUserAccess) Then rsMainUserAccess.Close
'    Set rsMainUserAccess = Nothing
'    Set rsMainUserAccess = New ADODB.Recordset
'    With rsMainUserAccess
'        .CursorLocation = adUseClient
'        .CursorType = adOpenDynamic
'        .LockType = adLockOptimistic
'    End With
'    rsMainUserAccess.Open AIM_MainUA_Get_Sp
'
'    dgMainUserAccess.ReBind
'    If f_IsRecordsetOpenAndPopulated(rsMainUserAccess) Then dgMainUserAccess.Rows = rsMainUserAccess.RecordCount
'
'    'Clean up
'    If Not (AIM_MainUA_Get_Sp Is Nothing) Then Set AIM_MainUA_Get_Sp.ActiveConnection = Nothing
'    Set AIM_MainUA_Get_Sp = Nothing
'
'    '************************************************************
'    'Create command object
'    Set AIM_UserList_Get_Sp = New ADODB.Command
'    With AIM_UserList_Get_Sp
'        .CommandType = adCmdStoredProc
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_AIMUser_List_Sp"
'        'No Parameters
'    End With
'
'    'Build/Bind the AIM User Drop Down
'    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
'    Set rsAIMUsers = Nothing
'    Set rsAIMUsers = New ADODB.Recordset
'    With rsAIMUsers
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'    rsAIMUsers.Open AIM_UserList_Get_Sp
'    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
'        Set Me.ddAIMUsers.DataSource = rsAIMUsers
'    End If
'
'    'Clean up
'    If Not (AIM_UserList_Get_Sp Is Nothing) Then Set AIM_UserList_Get_Sp.ActiveConnection = Nothing
'    Set AIM_UserList_Get_Sp = Nothing
'
'    '************************************************************
'    'Create command object
'    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
'    With AIM_CodeLookup_Get_Sp
'        .CommandType = adCmdStoredProc
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_CodeLookup_Get_Sp"
'        .Parameters("@CodeType").Value = g_CODETYPE_FCSTTYPE
'        .Parameters("@LangID").Value = gLangID
'    End With
'
'    'Build/Bind the Forecast Type Drop Down
'    If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'    Set rsFcstType = Nothing
'    Set rsFcstType = New ADODB.Recordset
'    With rsFcstType
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'    rsFcstType.Open AIM_CodeLookup_Get_Sp
'
'    If f_IsRecordsetOpenAndPopulated(rsFcstType) Then
'        Set Me.ddFcstType.DataSource = rsFcstType
'    End If
'
'    'Build/Bind the Access Type Drop Down
'    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
'    With AIM_CodeLookup_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_CodeLookup_Get_Sp"
'
'        .Parameters("@CodeType").Value = g_CODETYPE_ACCESSTYPE
'        .Parameters("@LangID").Value = gLangID
'    End With
'
'    If f_IsRecordsetValidAndOpen(rsAccessType) Then rsAccessType.Close
'    Set rsAccessType = Nothing
'    Set rsAccessType = New ADODB.Recordset
'    With rsAccessType
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'    rsAccessType.Open AIM_CodeLookup_Get_Sp
'
'    If f_IsRecordsetOpenAndPopulated(rsAccessType) Then
'        Set Me.ddAccessType.DataSource = rsAccessType
'    End If
'
'    'Clean up
'    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
'    Set AIM_CodeLookup_Get_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        If Not (AIM_UserAccess_Get_Sp Is Nothing) Then Set AIM_UserAccess_Get_Sp.ActiveConnection = Nothing
'        Set AIM_UserAccess_Get_Sp = Nothing
'        If Not (AIM_MainUA_Get_Sp Is Nothing) Then Set AIM_MainUA_Get_Sp.ActiveConnection = Nothing
'        Set AIM_MainUA_Get_Sp = Nothing
'        If Not (AIM_UserList_Get_Sp Is Nothing) Then Set AIM_UserList_Get_Sp.ActiveConnection = Nothing
'        Set AIM_UserList_Get_Sp = Nothing
'        If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
'        Set AIM_CodeLookup_Get_Sp = Nothing
'
'        Err.Raise Err.Number, Err.Source, Err.Description & "(BuildUserAccessGrid)"
'    End If
'End Function
'
'Private Function BuildForecastDetailGrid()
'On Error GoTo ErrorHandler
'
'    Dim AIM_ForecastDetail_Get_Sp As ADODB.Command
''    Dim AIM_ItemFilter_List_Sp As ADODB.Command
'
'    Dim rsTemp As ADODB.Recordset
'    Dim RowCounter As Long
'    Dim ColCounter As Long
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'
'    'Create command object
'    Set AIM_ForecastDetail_Get_Sp = New ADODB.Command
'    With AIM_ForecastDetail_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastDetail_Get_Sp"
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'    End With
'
'    '************************************************************
'    'Build the Forecast Detail recordset
'    If f_IsRecordsetValidAndOpen(rsForecastDetail) Then rsForecastDetail.Close
'    Set rsForecastDetail = Nothing
'    Set rsForecastDetail = New ADODB.Recordset
'    With rsForecastDetail
'        .CursorLocation = adUseClient
'        .CursorType = adOpenDynamic
'        .LockType = adLockOptimistic
'    End With
'    rsForecastDetail.Open AIM_ForecastDetail_Get_Sp
'
'    dgForecastDetail.ReBind
'    If f_IsRecordsetOpenAndPopulated(rsForecastDetail) Then dgForecastDetail.Rows = rsForecastDetail.RecordCount
'
'    'Clean up
'    If Not (AIM_ForecastDetail_Get_Sp Is Nothing) Then Set AIM_ForecastDetail_Get_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastDetail_Get_Sp = Nothing
'
'    '************************************************************
'    'Create command object
''    Set AIM_ItemFilter_List_Sp = New ADODB.Command
''    With AIM_ItemFilter_List_Sp
''        Set .ActiveConnection = Cn
''        .CommandType = adCmdStoredProc
''        .CommandText = "AIM_ItemFilter_List_Sp"
''
''        .CommandTimeout = 600000
''        .Parameters("@Item").Value = IIf(IsNull(rsForecast!Item) = True _
''            Or Trim(rsForecast!Item) = "", Null, rsForecast!Item)
''        .Parameters("@VnID").Value = IIf(IsNull(rsForecast!VnId) = True _
''            Or Trim(rsForecast!VnId) = "", Null, rsForecast!VnId)
''        .Parameters("@Assort").Value = IIf(IsNull(rsForecast!Assort) = True _
''            Or Trim(rsForecast!Assort) = "", Null, rsForecast!Assort)
''        .Parameters("@LcID").Value = IIf(IsNull(rsForecast!LcId) = True _
''            Or Trim(rsForecast!LcId) = "", Null, rsForecast!LcId)
''        .Parameters("@Class1").Value = IIf(IsNull(rsForecast!Class1) = True _
''            Or Trim(rsForecast!Class1) = "", Null, rsForecast!Class1)
''        .Parameters("@Class2").Value = IIf(IsNull(rsForecast!Class2) = True _
''            Or Trim(rsForecast!Class2) = "", Null, rsForecast!Class2)
''        .Parameters("@Class3").Value = IIf(IsNull(rsForecast!Class3) = True _
''            Or Trim(rsForecast!Class3) = "", Null, rsForecast!Class3)
''        .Parameters("@Class4").Value = IIf(IsNull(rsForecast!Class4) = True _
''            Or Trim(rsForecast!Class4) = "", Null, rsForecast!Class4)
''        .Parameters("@ByID").Value = IIf(IsNull(rsForecast!ById) = True _
''            Or Trim(rsForecast!ById) = "", Null, rsForecast!ById)
''        .Parameters("@ItStat").Value = IIf(IsNull(rsForecast!ItStat) = True _
''            Or Trim(rsForecast!ItStat) = "", Null, rsForecast!ItStat)
''    End With
''
''    'Build/Bind the Items Drop Down
''    Set rsTemp = New ADODB.Recordset
''    With rsTemp
''        .CursorLocation = adUseClient
''        .CursorType = adOpenStatic
''        .LockType = adLockReadOnly
''    End With
''
''    rsTemp.Open AIM_ItemFilter_List_Sp
''
''    If f_IsRecordsetOpenAndPopulated(rsTemp) Then Set rsTemp = rsTemp.NextRecordset
''    If f_IsRecordsetOpenAndPopulated(rsTemp) _
''    Then
''        'Re-create rsItems
''        If f_IsRecordsetValidAndOpen(rsItems) Then rsItems.Close
''        Set rsItems = Nothing
''        Set rsItems = New ADODB.Recordset
''        rsItems.Fields.Append "Item", adBSTR, 25
''        rsItems.Fields.Append "LcID", adBSTR, 25
''        rsItems.Fields.Append "ItDesc", adBSTR, 30
''        rsItems.Fields.Append "ItStat", adBSTR, 1
''        rsItems.Fields.Append "VnID", adBSTR, 25
''        rsItems.Fields.Append "Assort", adBSTR, 25
''        rsItems.Fields.Append "ByID", adBSTR, 30
''        rsItems.Fields.Append "Class1", adBSTR, 50
''        rsItems.Fields.Append "Class2", adBSTR, 50
''        rsItems.Fields.Append "Class3", adBSTR, 50
''        rsItems.Fields.Append "Class4", adBSTR, 50
''
''        rsItems.Open
''        'Associate with data as a disconnected recordset
''        rsTemp.MoveFirst
''        While Not rsTemp.eof
''            rsItems.AddNew
''            For ColCounter = 0 To (rsTemp.Fields.Count - 1)
''                rsItems.Fields(ColCounter).Value = rsTemp.Fields(ColCounter).Value
''            Next
''            rsTemp.MoveNext
''        Wend
''    Else
''        'Investigate
''    End If
''
'''    If f_IsRecordsetValidAndOpen(rsItems) Then Set ddItems.DataSource = rsItems
''
''    'Clean Up
''    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
''    Set rsTemp = Nothing
''
''    If Not (AIM_ItemFilter_List_Sp Is Nothing) Then Set AIM_ItemFilter_List_Sp.ActiveConnection = Nothing
''    Set AIM_ItemFilter_List_Sp = Nothing
'    Set ddItems.DataSource = Nothing
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'        Set rsTemp = Nothing
'        If Not (AIM_ForecastDetail_Get_Sp Is Nothing) Then Set AIM_ForecastDetail_Get_Sp.ActiveConnection = Nothing
'        Set AIM_ForecastDetail_Get_Sp = Nothing
''        If Not (AIM_ItemFilter_List_Sp Is Nothing) Then Set AIM_ItemFilter_List_Sp.ActiveConnection = Nothing
''        Set AIM_ItemFilter_List_Sp = Nothing
'        Err.Raise Err.Number, Err.Source, Err.Description & "(BuildForecastDetailGrid)"
'    End If
'End Function
'
'Private Function BuildFreezePdsGrid()
'On Error GoTo ErrorHandler
'
'    Dim AIM_ForecastFreezePds_Get_Sp As ADODB.Command
'    Dim AIM_LocationsFilter_List_Sp As ADODB.Command
'    Dim rsTemp As ADODB.Recordset
'    Dim ColCounter As Long
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'
'    '************************************************************
'    'Create the command object
'    Set AIM_ForecastFreezePds_Get_Sp = New ADODB.Command
'    With AIM_ForecastFreezePds_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastFreezePds_Get_Sp"
'
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'        .Parameters("@LcID").Value = "All"
'        .Parameters("@FreezePds").Value = Null
'    End With
'
'    '************************************************************
'    'Build the Forecast Freeze Pds recordset
'    If f_IsRecordsetValidAndOpen(rsFreezePds) Then rsFreezePds.Close
'    Set rsFreezePds = Nothing
'    Set rsFreezePds = New ADODB.Recordset
'    With rsFreezePds
'        .CursorLocation = adUseClient
'        .CursorType = adOpenDynamic
'        .LockType = adLockOptimistic
'    End With
'
'    rsFreezePds.Open AIM_ForecastFreezePds_Get_Sp
'
'    dgFreezePds.ReBind
'    If f_IsRecordsetOpenAndPopulated(rsFreezePds) Then dgFreezePds.Rows = rsFreezePds.RecordCount
'
'    'Clean Up
'    If Not (AIM_ForecastFreezePds_Get_Sp Is Nothing) Then Set AIM_ForecastFreezePds_Get_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastFreezePds_Get_Sp = Nothing
'
'    '************************************************************
'    'Build/Bind the Locations Drop Down
'    'Create the command object
'    Set AIM_LocationsFilter_List_Sp = New ADODB.Command
'    With AIM_LocationsFilter_List_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_LocationsFilter_List_Sp"
'
'        .CommandTimeout = 600000
'        .Parameters("@Item").Value = IIf(IsNull(rsForecast!Item) = True _
'            Or Trim(rsForecast!Item) = "", Null, rsForecast!Item)
'        .Parameters("@VnID").Value = IIf(IsNull(rsForecast!VnId) = True _
'            Or Trim(rsForecast!VnId) = "", Null, rsForecast!VnId)
'        .Parameters("@Assort").Value = IIf(IsNull(rsForecast!Assort) = True _
'            Or Trim(rsForecast!Assort) = "", Null, rsForecast!Assort)
'        .Parameters("@LcID").Value = IIf(IsNull(rsForecast!LcId) = True _
'            Or Trim(rsForecast!LcId) = "", Null, rsForecast!LcId)
'        .Parameters("@Class1").Value = IIf(IsNull(rsForecast!Class1) = True _
'            Or Trim(rsForecast!Class1) = "", Null, rsForecast!Class1)
'        .Parameters("@Class2").Value = IIf(IsNull(rsForecast!Class2) = True _
'            Or Trim(rsForecast!Class2) = "", Null, rsForecast!Class2)
'        .Parameters("@Class3").Value = IIf(IsNull(rsForecast!Class3) = True _
'            Or Trim(rsForecast!Class3) = "", Null, rsForecast!Class3)
'        .Parameters("@Class4").Value = IIf(IsNull(rsForecast!Class4) = True _
'            Or Trim(rsForecast!Class4) = "", Null, rsForecast!Class4)
'        .Parameters("@ByID").Value = IIf(IsNull(rsForecast!ById) = True _
'            Or Trim(rsForecast!ById) = "", Null, rsForecast!ById)
'        .Parameters("@ItStat").Value = IIf(IsNull(rsForecast!ItStat) = True _
'            Or Trim(rsForecast!ItStat) = "", Null, rsForecast!ItStat)
'    End With
'
'    Set rsTemp = New ADODB.Recordset
'    With rsTemp
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    rsTemp.Open AIM_LocationsFilter_List_Sp
'
'    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
'        Set rsTemp = rsTemp.NextRecordset
'        If f_IsRecordsetOpenAndPopulated(rsTemp) Then
'            'Re-create rsAIMLocations
'            If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
'            Set rsAIMLocations = Nothing
'            Set rsAIMLocations = New ADODB.Recordset
'            rsAIMLocations.Fields.Append "LcID", adBSTR, 25
'            rsAIMLocations.Fields.Append "LName", adBSTR, 30
'
'            rsAIMLocations.Open
'            'Associate with data as a disconnected recordset
'            rsTemp.MoveFirst
'            While Not rsTemp.eof
'                rsAIMLocations.AddNew
'                For ColCounter = 0 To (rsTemp.Fields.Count - 1)
'                    rsAIMLocations.Fields(ColCounter).Value = rsTemp.Fields(ColCounter).Value
'                Next
'                rsTemp.MoveNext
'            Wend
'        Else
'            'Failed for some reason. Investigate.
'        End If
'    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
'        Set ddAIMLocations.DataSource = rsAIMLocations
'    End If
'
'    'Clean Up
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'
'    If Not (AIM_LocationsFilter_List_Sp Is Nothing) Then Set AIM_LocationsFilter_List_Sp.ActiveConnection = Nothing
'    Set AIM_LocationsFilter_List_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'        Set rsTemp = Nothing
'        If Not (AIM_ForecastFreezePds_Get_Sp Is Nothing) Then Set AIM_ForecastFreezePds_Get_Sp.ActiveConnection = Nothing
'        Set AIM_ForecastFreezePds_Get_Sp = Nothing
'        If Not (AIM_LocationsFilter_List_Sp Is Nothing) Then Set AIM_LocationsFilter_List_Sp.ActiveConnection = Nothing
'        Set AIM_LocationsFilter_List_Sp = Nothing
'
'        Err.Raise Err.Number, Err.Source, Err.Description & "(BuildFreezePdsGrid)"
'    End If
'End Function
'Private Function ValidateMainUserAccess() As Long
'On Error GoTo ErrorHandler
'
'    Dim AIM_Validate_StoredProcs As ADODB.Command
'    Dim MaintAccess As Long
'    Dim RtnCode As Integer
'
'    If m_NewFcst = True Then
'        ValidateMainUserAccess = g_ACCESS_CREATEMODIFY
'        Exit Function
'    End If
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'
'    '************************************************************
'    'Prepare the command obj for the stored proc to fetch DropDown lists.
'    '************************************************************
'    Set AIM_Validate_StoredProcs = New ADODB.Command
'    With AIM_Validate_StoredProcs
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastMainUserAccess_Validate_Sp"
'        .Parameters("@UserId").Value = gUserID
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'        .Parameters("@AccessType").Value = ""
'
'        .Execute , , adExecuteNoRecords
'    End With
'
'    RtnCode = AIM_Validate_StoredProcs.Parameters(0).Value
'    If RtnCode < 0 Then ValidateMainUserAccess = g_ACCESS_READ  'la here
'
'    MaintAccess = CLng(AIM_Validate_StoredProcs.Parameters("@AccessType").Value)
'
'    Select Case MaintAccess
'        Case g_ACCESS_READ
'            ValidateMainUserAccess = g_ACCESS_READ
'
'        Case g_ACCESS_CREATEMODIFY
'            ValidateMainUserAccess = g_ACCESS_CREATEMODIFY
'
'        Case g_ACCESS_CREATE
'            ValidateMainUserAccess = g_ACCESS_CREATE
'
'        Case g_ACCESS_MODIFY
'            ValidateMainUserAccess = g_ACCESS_MODIFY
'
'        Case Else
'            'None/undefined
'            ValidateMainUserAccess = g_ACCESS_NONE
'    End Select
'
'    'Clean Up
'    If Not (AIM_Validate_StoredProcs Is Nothing) Then Set AIM_Validate_StoredProcs.ActiveConnection = Nothing
'    Set AIM_Validate_StoredProcs = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Validate_StoredProcs Is Nothing) Then Set AIM_Validate_StoredProcs.ActiveConnection = Nothing
'    Set AIM_Validate_StoredProcs = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ValidateMainUserAccess)"
'End Function
'
'Private Function SaveMainUserAccess() As Long
'On Error GoTo ErrorHandler
'
'    Dim AIM_Save_StoredProcs As ADODB.Command
'    Dim RtnCode As Integer
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'    '************************************************************
'    'Prepare the command obj for the stored proc to fetch DropDown lists.
'    '************************************************************
'
'    Set AIM_Save_StoredProcs = New ADODB.Command
'    With AIM_Save_StoredProcs
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastMainUserAccess_Save_Sp"
'        .Parameters("@UserId").Value = gUserID
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'        .Parameters("@AccessType").Value = g_ACCESS_CREATEMODIFY
'
'        .Execute , , adExecuteNoRecords
'    End With
'
'    RtnCode = AIM_Save_StoredProcs.Parameters(0).Value
'
'    'Clean Up
'    If Not (AIM_Save_StoredProcs Is Nothing) Then Set AIM_Save_StoredProcs.ActiveConnection = Nothing
'    Set AIM_Save_StoredProcs = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Save_StoredProcs Is Nothing) Then Set AIM_Save_StoredProcs.ActiveConnection = Nothing
'    Set AIM_Save_StoredProcs = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SaveMainUserAccess)"
'End Function
'
'Private Function UserInputValidation(p_ControlName As String, p_Message As String, Optional p_ColIndex As Integer = -1) As Boolean
'On Error GoTo ErrorHandler
'
'    Dim lngCounter As Integer
'    Dim LoopUpperBound As Integer
'    Dim LoopLowerBound As Integer
'
'    Dim RtnCode As Integer
'
'    If gAccessLvl = g_ACCESS_CREATEMODIFY _
'    And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'    Then
'        'ErrorMessage
'        p_Message = getTranslationResource("MSGBOX06602")
'        If StrComp(p_Message, "MSGBOX06602") = 0 Then _
'            p_Message = "Some required fields are missing. Please review before moving to another row."
'
'        'Check if it's just one field, or all.
'        If p_ColIndex >= 0 Then
'            LoopLowerBound = p_ColIndex
'            LoopUpperBound = p_ColIndex
'        Else
'            LoopLowerBound = 0
'
'            Select Case p_ControlName
'                Case dgForecastDetail.Name
'                    LoopLowerBound = 1
'                    LoopUpperBound = dgForecastDetail.Columns.Count - 1
'                Case dgFreezePds.Name
'                    LoopUpperBound = dgFreezePds.Columns.Count - 1
'                Case dgMainUserAccess.Name
'                    LoopUpperBound = dgMainUserAccess.Columns.Count - 1
'                Case dgUserAccess.Name
'                    LoopUpperBound = dgUserAccess.Columns.Count - 1
'            End Select
'        End If
'
'        Select Case p_ControlName
'            Case dgForecastDetail.Name
'                For lngCounter = LoopLowerBound To LoopUpperBound
'                    If lngCounter <> 2 Then
'                        'Not the freeze option, which is a toggle.
'                        If dgForecastDetail.Columns(lngCounter).Value = "" _
'                        Or IsNull(dgForecastDetail.Columns(lngCounter).Value) _
'                        Then
'                            'Cancel = True
'                            UserInputValidation = True
'                            Exit Function
'                        End If
'                    End If
'                Next
'
'            Case dgFreezePds.Name
'                For lngCounter = LoopLowerBound To LoopUpperBound
'                    If dgFreezePds.Columns(lngCounter).Value = "" _
'                    Or IsNull(dgFreezePds.Columns(lngCounter).Value) _
'                    Then
'                        'Cancel = True
'                        UserInputValidation = True
'                        Exit Function
'                    End If
'                Next
'
'            Case dgMainUserAccess.Name
'                For lngCounter = LoopLowerBound To LoopUpperBound
'                    If dgMainUserAccess.Columns(lngCounter).Value = "" _
'                    Or IsNull(dgMainUserAccess.Columns(lngCounter).Value) _
'                    Then
'                        'Cancel = True
'                        UserInputValidation = True
'                        Exit Function
'                    End If
'                Next
'
'            Case dgUserAccess.Name
'                For lngCounter = LoopLowerBound To LoopUpperBound
'                    If dgUserAccess.Columns(lngCounter).Value = "" _
'                    Or IsNull(dgUserAccess.Columns(lngCounter).Value) _
'                    Then
'                        'Cancel = True
'                        UserInputValidation = True
'                        Exit Function
'                    End If
'                Next
'
'        End Select
'
'    Else
'        'No access, inform and exit
'        p_Message = getTranslationResource("MSGBOX07311")
'        If StrComp(p_Message, "MSGBOX07311") = 0 Then p_Message = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'        'Cancel = True
'        UserInputValidation = True
'        Exit Function
'
'    End If
'
'    'Cancel = false
'    UserInputValidation = False
'
'Exit Function
'ErrorHandler:
'   Err.Raise Err.Number, Err.Source, Err.Description & "(UserInputValidation(" & p_ControlName & "))"
'End Function
'
'Private Function GetForecastInformation(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim rsForecastInfo As ADODB.Recordset
'    Dim AIM_GetList_FcstID_Sp As ADODB.Command
'    Dim RtnCode As Integer
'    Dim CurrentFcstID As String
'
'    'Get Forecast IDs and Descriptions from AIMForecast
'    Set AIM_GetList_FcstID_Sp = New ADODB.Command
'    With AIM_GetList_FcstID_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GetList_FcstID_Sp"
'    End With
'    With AIM_GetList_FcstID_Sp.Parameters
'        .Append AIM_GetList_FcstID_Sp.CreateParameter("@RecordCount", adInteger, adParamOutput)
'    End With
'
'    Set rsForecastInfo = New ADODB.Recordset
'    Set rsForecastInfo = AIM_GetList_FcstID_Sp.Execute
'
'    If rsForecastInfo.eof Then GoTo CleanUp
'
'    'Build the Forecast List Drop Down
'    If f_IsRecordsetOpenAndPopulated(rsForecastInfo) Then
'        dcForecast(Index).RemoveAll
'        dcForecast(Index).Reset
'        Set xForecastLinkList = Nothing
'        Set xForecastLinkList = New XArrayDB
'        xForecastLinkList.LoadRows rsForecastInfo.GetRows(adGetRowsRest)
'        If xForecastLinkList.UpperBound(1) > 0 Then
'            If f_IsRecordsetOpenAndPopulated(rsForecast) Then CurrentFcstID = rsForecast!FcstId
'
'            For RtnCode = xForecastLinkList.LowerBound(1) To xForecastLinkList.UpperBound(1)
'                If Index = IDX_DCFORECAST_FCSTLINKID Then
'                    'Check for and skip current forecast ID for the Link List dropdown
'                    If StrComp(xForecastLinkList(RtnCode, 0), CurrentFcstID, vbTextCompare) <> 0 Then
'                        dcForecast(Index).AddItem xForecastLinkList(RtnCode, 0) & vbTab & xForecastLinkList(RtnCode, 1)
'                    End If
'                Else
'                    dcForecast(Index).AddItem xForecastLinkList(RtnCode, 0) & vbTab & xForecastLinkList(RtnCode, 1)
'                End If
'            Next
'        End If
'        dcForecast_InitColumnProps (Index)
'    End If
'
'CleanUp:
'    If f_IsRecordsetValidAndOpen(rsForecastInfo) Then rsForecastInfo.Close
'    Set rsForecastInfo = Nothing
'    If Not (AIM_GetList_FcstID_Sp Is Nothing) Then Set AIM_GetList_FcstID_Sp.ActiveConnection = Nothing
'    Set AIM_GetList_FcstID_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsForecastInfo) Then rsForecastInfo.Close
'    Set rsForecastInfo = Nothing
'    If Not (AIM_GetList_FcstID_Sp Is Nothing) Then Set AIM_GetList_FcstID_Sp.ActiveConnection = Nothing
'    Set AIM_GetList_FcstID_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastInformation)"
'End Function
'
'Private Function f_AIMForecast_Changed() As Boolean
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Integer
'
'    'Set default value to unchanged
'    f_AIMForecast_Changed = False
'
'    If ckFcstLocked.Value = vbChecked Then
'        Exit Function
'    ElseIf m_NewFcst Then
'        f_AIMForecast_Changed = True
'        Exit Function
'    End If
'
'    'This needs more work 'la here
'    If StrComp(txtFcstId.Text, rsForecast!FcstId, vbTextCompare) <> 0 Then
'        strMessage = getTranslationResource("MSGBOX07314")
'        If StrComp(strMessage, "MSGBOX07314") = 0 Then strMessage = "Changes to key reference field (Forecast ID) are not allowed!"
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        f_AIMForecast_Changed = False
'        dcForecast(IDX_DCFORECAST_FCSTID).Text = rsForecast!FcstId
'        Exit Function
'    End If
'
'    If txtFcstDesc.Text <> rsForecast!FcstDesc Then f_AIMForecast_Changed = True
'    If ckFcstStatus.Value <> rsForecast!FcstStatus Then f_AIMForecast_Changed = True
'
'    If Format(rsForecast!FcstStartDate, gDateFormat) <> Format(txtFcstStartDate.Text, gDateFormat) Then f_AIMForecast_Changed = True
'
'    If txtFcstPds.Value <> rsForecast!FcstPds Then f_AIMForecast_Changed = True
'    If txtAdjustPct.Value <> rsForecast!AdjustPct Then f_AIMForecast_Changed = True
'
'    If ckFcstLevel.Value <> rsForecast!FcstLevel Then f_AIMForecast_Changed = True
'    If ckNetRqmts.Value <> rsForecast!NetRqmts Then f_AIMForecast_Changed = True
'
'    If optApplyTrend(rsForecast!ApplyTrend).Value = False Then f_AIMForecast_Changed = True
'    If opFcstUnit(rsForecast!FcstUnit).Value = False Then f_AIMForecast_Changed = True
'    If opFcstInterval(rsForecast!FcstInterval).Value = False Then f_AIMForecast_Changed = True
'
'    If dcForecast(IDX_DCFORECAST_FCSTLINKID).Text <> rsForecast!FcstLinkID Then f_AIMForecast_Changed = True
'
'    If (ckEnableFcstVersions.Value = vbChecked And rsForecast!EnableFcstVersions <> "Y") _
'    Or (ckEnableFcstVersions.Value = vbUnchecked And rsForecast!EnableFcstVersions = "Y") _
'    Then
'        f_AIMForecast_Changed = True
'    End If
'    If (ckFuturePdsUpdate.Value = vbChecked And rsForecast!FuturePdsUpdate <> "Y") _
'    Or (ckFuturePdsUpdate.Value = vbUnchecked And rsForecast!FuturePdsUpdate = "Y") _
'    Then
'        f_AIMForecast_Changed = True
'
'    End If
'    If (ckFcstOutput.Value = vbChecked And rsForecast!FcstOutput <> "Y") _
'    Or (ckFcstOutput.Value = vbUnchecked And rsForecast!FcstOutput = "Y") _
'    Then
'        f_AIMForecast_Changed = True
'    End If
'
'    If StrComp(txtItem.Text, rsForecast!Item) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtItemStatus.Text, rsForecast!ItStat) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtVnId.Text, rsForecast!VnId) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtAssort.Text, rsForecast!Assort) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtLcId.Text, rsForecast!LcId) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtById.Text, rsForecast!ById) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtClass(1).Text, rsForecast!Class1) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtClass(2).Text, rsForecast!Class2) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtClass(3).Text, rsForecast!Class3) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtClass(4).Text, rsForecast!Class4) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtLDivision.Text, Trim(rsForecast!LDivision)) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtLRegion.Text, Trim(rsForecast!LRegion)) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtLStatus.Text, Trim(rsForecast!LStatus)) <> 0 Then f_AIMForecast_Changed = True
'    If StrComp(txtLUserDefined.Text, Trim(rsForecast!LUserDefined)) <> 0 Then f_AIMForecast_Changed = True
'
''    If opRevFreq(rsForecast!RevFreq).Value = False Then f_AIMForecast_Changed = True
'    Select Case rsForecast!RevFreq
'    Case 2  'Weekly
'        If Me.opRevFreq(0).Value = False Then f_AIMForecast_Changed = True
'    Case 3  'Monthly
'        If Me.opRevFreq(1).Value = False Then f_AIMForecast_Changed = True
'    End Select
'
'    If txtRevInterval.Value <> rsForecast!RevInterval Then f_AIMForecast_Changed = True
'    If Format(rsForecast!NextRevdate, gDateFormat) <> Format(txtNextRevDate.Text, gDateFormat) Then f_AIMForecast_Changed = True
'    If txtFcstContact.Text <> rsForecast!FcstContact Then f_AIMForecast_Changed = True
'    If txtFcstEMail.Text <> rsForecast!FcstEmail Then f_AIMForecast_Changed = True
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_Changed)"
'End Function
'
'Private Function f_AIMForecast_Commit(Optional p_Prompt As Boolean = False)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim strMessage1 As String
'    Dim RtnCode As Integer
'
'    RtnCode = verify_FcstID
'    If RtnCode < 0 Then
'        strMessage = getTranslationResource("MSGBOX07315")
'        If StrComp(strMessage, "MSGBOX07315") = 0 Then _
'                strMessage = "'" & dcForecast(IDX_DCFORECAST_FCSTID).Text & _
'                             "' is an invalid value for Forecast ID. Please choose from the given list."
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        dcForecast(IDX_DCFORECAST_FCSTID).SetFocus
'        Exit Function
'    End If
'
'    'Check for updates
'    If f_AIMForecast_Changed Then
'        'Validate permissions; Check prompt; Then Save
'        If gAccessLvl = g_ACCESS_CREATEMODIFY _
'        And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
'        Then
'            If p_Prompt = True Then
'                strMessage = getTranslationResource("MSGBOX07300")
'                If StrComp(strMessage, "MSGBOX07300") = 0 Then strMessage = "Do you wish to save the changes made to the current record?"
'                RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, getTranslationResource(Me.Caption))
'                If RtnCode = vbYes Then
'                    'User selected yes, Save
'                    RtnCode = f_AIMForecast_Save
'                Else
'                    'CancelUpdate
'                    If m_NewFcst = True Then
'                        m_NewFcst = False
'                        'Show add, delete and the dropdown. Hide the text box
'                        Me.tbNavigation.Tools("ID_AddNew").Enabled = True
'                        tbNavigation.Tools("ID_Delete").Enabled = True
'                        dcForecast(IDX_DCFORECAST_FCSTID).Visible = True
'                        txtFcstId.Visible = False
'                    End If
'                    Exit Function
'                End If
'
'            Else
'                'User has clicked the save icon, no prompt required, save directly.
'                RtnCode = f_AIMForecast_Save
'            End If
'        Else
'            'No access = no action
'            strMessage = getTranslationResource("MSGBOX07311")
'            If StrComp(strMessage, "MSGBOX07311") = 0 Then strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
'            MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
'        End If
'    End If
'
'    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_Commit)"
'End Function
'
'Private Function f_AIMForecast_Delete() As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_AIMForecast_Delete_Sp As ADODB.Command
'    Dim strMessage As String
'
'    'Validate the data
'    If ckFcstLocked.Value = vbChecked Then
'        strMessage = getTranslationResource("MSGBOX07308")
'        If StrComp(strMessage, "MSGBOX07308") = 0 Then _
'                strMessage = "This forecast is in use within the Forecast Repository. Modification not allowed "
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        Exit Function
'    End If
'
'    'Start writing to database
'    Set AIM_AIMForecast_Delete_Sp = New ADODB.Command
'    With AIM_AIMForecast_Delete_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMForecast_Delete_Sp"
'
'        .Parameters.Refresh
'        .Parameters("@FcstID").Value = rsForecast!FcstId
'        .Parameters("@UserID").Value = gUserID
'
'        'Run the Stored Procedure
'        .Execute , , adExecuteNoRecords
'    End With
'
'    RtnCode = AIM_AIMForecast_Delete_Sp.Parameters(0).Value
'    If RtnCode < 0 Then
'        strMessage = getTranslationResource("MSGBOX07313")
'        If StrComp(strMessage, "MSGBOX07313") = 0 Then strMessage = "Failed to delete the current Forecast record."
'        If Cn.Errors.Count > 0 Then
'            ADOErrorHandler Cn, strMessage
'        End If
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        f_AIMForecast_Delete = FAIL
'
'    Else
'        f_AIMForecast_Delete = SUCCEED
'        f_AIMForecast_GetKey rsForecast, dcForecast(IDX_DCFORECAST_FCSTID).Text, SQL_GetFirst
'
'        GetForecastInformation IDX_DCFORECAST_FCSTID
'        Me.dcForecast(IDX_DCFORECAST_FCSTID).Text = rsForecast!FcstId   'This will trigger refresh
'    End If
'
'    'Clean Up
'    If Not (AIM_AIMForecast_Delete_Sp Is Nothing) Then Set AIM_AIMForecast_Delete_Sp.ActiveConnection = Nothing
'    Set AIM_AIMForecast_Delete_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_AIMForecast_Delete_Sp Is Nothing) Then Set AIM_AIMForecast_Delete_Sp.ActiveConnection = Nothing
'    Set AIM_AIMForecast_Delete_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_Delete)"
'End Function
'
'Private Function f_AIMForecast_GetKey(ByRef rs As ADODB.Recordset, p_FcstId As String, ByVal Action As SQL_ACTIONS) As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim AIM_Forecast_GetKey_Sp As ADODB.Command
'
'    Set AIM_Forecast_GetKey_Sp = New ADODB.Command
'    With AIM_Forecast_GetKey_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_Forecast_GetKey_Sp"
'    End With
'
'    'Destroy any existing stuff
'    On Error Resume Next
'    If f_IsRecordsetValidAndOpen(rs) Then rs.Close
'        If Err.Number = 3219 Then
'            'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
'            'Discard the rs and move on.
'            Set rs = Nothing
'        End If
'    Set rs = Nothing
'    On Error GoTo ErrorHandler
'
'    'Re-create the recordset from scratch
'    Set rs = New ADODB.Recordset
'
'    'Set cursor and lock
'    With rs
'        .CursorLocation = adUseClient
'        .CursorType = adOpenDynamic
'        .LockType = adLockOptimistic
'    End With
'
'    'Get the Seasonality Data
'    With AIM_Forecast_GetKey_Sp
'        .Parameters.Refresh
'        .Parameters("@FcstId").Value = p_FcstId
'        .Parameters("@Action").Value = Action
'        .Parameters(0).Value = 0
'
'        'Fetch in recordset
'        rs.Open AIM_Forecast_GetKey_Sp
'
'        RtnCode = .Parameters(0).Value
'        If f_IsRecordsetOpenAndPopulated(rs) Then
'            p_FcstId = rs!FcstId
'        Else
'            p_FcstId = ""
'            f_AIMForecast_GetKey = RtnCode
'            Exit Function
'        End If
'    End With
'
'    f_AIMForecast_GetKey = SUCCEED
'
'    If Not (AIM_Forecast_GetKey_Sp Is Nothing) Then Set AIM_Forecast_GetKey_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetKey_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Forecast_GetKey_Sp Is Nothing) Then Set AIM_Forecast_GetKey_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetKey_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_GetKey)"
'End Function
'
'Private Function f_AIMForecast_InitializeNew()
'On Error GoTo ErrorHandler
'
'    'Set flags
'    m_NewFcst = True
'
'    'Clear current recordset
'    If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
'    Set rsForecast = Nothing
'
'    'Set default values
'    txtFcstId.Text = getTranslationResource("New Fcst")
'    txtFcstDesc.Text = getTranslationResource("New Fcst Description")
'    ckFcstLocked.Value = vbUnchecked
'    ckFcstStatus.Value = vbUnchecked
'
'    txtFcstStartDate.Text = Format(Date, gDateFormat)
'    txtFcstPds.Text = 12
'    txtAdjustPct.Text = "0.00"
'
'    ckFcstLevel.Value = vbUnchecked
'    ckNetRqmts.Value = vbUnchecked
'
'    optApplyTrend(1).Value = True
'    opFcstUnit(0).Value = True
'    opFcstInterval(2).Value = True
'
'    GetForecastInformation IDX_DCFORECAST_FCSTLINKID
'    dcForecast(IDX_DCFORECAST_FCSTLINKID).Text = ""
'    txtFcstLinkDesc.Text = ""
'    ckEnableFcstVersions.Value = vbUnchecked
'    ckFuturePdsUpdate.Value = vbChecked
'    ckFcstOutput.Value = vbUnchecked
'
'    txtItem.Text = ""
'    txtItemStatus.Text = ""
'    txtVnId.Text = ""
'    txtAssort.Text = ""
'    txtLcId.Text = ""
'    txtById.Text = ""
'    txtClass(1).Text = ""
'    txtClass(2).Text = ""
'    txtClass(3).Text = ""
'    txtClass(4).Text = ""
'
'    opRevFreq(0).Value = True
'    txtRevInterval.Text = 1
'    txtNextRevDate.Text = Format(Date, gDateFormat)
'    txtFcstContact.Text = ""
'    txtFcstEMail.Text = ""
'
'    'Enable all fields
'    ForecastFieldsToggle True
'
'    'Hide add, delete and the dropdown
'    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
'    tbNavigation.Tools("ID_Delete").Enabled = False
'    txtFcstId.Visible = True
'    dcForecast(IDX_DCFORECAST_FCSTID).Visible = False
'    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_InitializeNew)"
'End Function
'
'Private Function f_AIMForecast_Refresh()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim tempDate As String
'
'    If Not f_IsRecordsetOpenAndPopulated(rsForecast) Then Exit Function
'    If m_NewFcst = True Then
'        Exit Function
'    Else
'        'Enable/Disable Update
'        If gAccessLvl = g_ACCESS_READ Then
'            Me.tbNavigation.Tools("ID_Save").Enabled = False
'            Me.tbNavigation.Tools("ID_Delete").Enabled = False
'            Me.tbNavigation.Tools("ID_AddNew").Enabled = False
'        Else
'            Me.tbNavigation.Tools("ID_Save").Enabled = True
'            Me.tbNavigation.Tools("ID_Delete").Enabled = True
'            Me.tbNavigation.Tools("ID_AddNew").Enabled = True
'        End If
'        dcForecast(IDX_DCFORECAST_FCSTID).Visible = True
'        txtFcstId.Visible = False
'        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
'    End If
'
'    '************************************************************
'    'Validate the user's access to this forecast
'    'SA SHOULD HAVE COMPLETE ACCESS TO EVERYTHING.
'    '************************************************************
'    RtnCode = ValidateMainUserAccess
'    Select Case RtnCode
'        Case g_ACCESS_CREATE, g_ACCESS_MODIFY, g_ACCESS_CREATEMODIFY
'            '************************************************************
'            'Check if the Forecast has been entered into the Repository.
'            'NOTE: THIS FIELD IS TO BE UPDATED FROM THE FORECAST MODIFICATION SCREEN
'            '************************************************************
'            If rsForecast!FcstLocked = "Y" Then
'                'Lock all controls
'                ForecastFieldsToggle False
'            Else
'                'Make them editable
'                ForecastFieldsToggle True
'            End If
'
'        Case g_ACCESS_READ, g_ACCESS_NONE
'            ForecastFieldsToggle False
'    End Select
''    'End Validation
'
'    'Set the values, anyway
'    Me.txtFcstDesc.Text = rsForecast!FcstDesc
'    Me.ckFcstLocked.Value = IIf(rsForecast!FcstLocked = "Y", vbChecked, vbUnchecked)
'    IsFilteringEnabled = IIf(Me.ckFcstLocked.Value = vbChecked, False, True)
'    Me.ckFcstStatus.Value = rsForecast!FcstStatus
'
'    GetForecastInformation IDX_DCFORECAST_FCSTLINKID
'    Me.dcForecast(IDX_DCFORECAST_FCSTLINKID).Value = IIf(IsNull(rsForecast!FcstLinkID), "", rsForecast!FcstLinkID)
'    Me.ckEnableFcstVersions.Value = IIf(rsForecast!EnableFcstVersions = "Y", vbChecked, vbUnchecked)
'    Me.ckFuturePdsUpdate.Value = IIf(rsForecast!FuturePdsUpdate = "Y", vbChecked, vbUnchecked)
'    Me.ckFcstOutput.Value = IIf(rsForecast!FcstOutput = "Y", vbChecked, vbUnchecked)
'
'    Me.txtAdjustPct.Value = rsForecast!AdjustPct
'    Me.txtFcstPds.Value = IIf(rsForecast!FcstPds < txtFcstPds.MinValue, _
'                            txtFcstPds.MinValue, _
'                            (IIf(rsForecast!FcstPds > txtFcstPds.MaxValue, txtFcstPds.MaxValue, rsForecast!FcstPds)))
'    Me.ckFcstLevel.Value = rsForecast!FcstLevel
'    Me.ckNetRqmts.Value = rsForecast!NetRqmts
'    Me.optApplyTrend(rsForecast!ApplyTrend) = True
'    Me.opFcstInterval(rsForecast!FcstInterval).Value = True
'    Me.opFcstUnit(rsForecast!FcstUnit).Value = True
'
'    Me.txtItem.Text = rsForecast!Item
'    Me.txtItemStatus.Text = rsForecast!ItStat
'    Me.txtVnId.Text = rsForecast!VnId
'    Me.txtById.Text = rsForecast!ById
'    Me.txtAssort.Text = rsForecast!Assort
'    Me.txtLcId.Text = rsForecast!LcId
'    Me.txtClass(1).Text = rsForecast!Class1
'    Me.txtClass(2).Text = rsForecast!Class2
'    Me.txtClass(3).Text = rsForecast!Class3
'    Me.txtClass(4).Text = rsForecast!Class4
'
'    Select Case rsForecast!RevFreq
'    Case 2  'Weekly
'        Me.opRevFreq(0).Value = True
'    Case 3  'Monthly
'        Me.opRevFreq(1).Value = True
'    End Select
'
'    Me.txtRevInterval.Value = IIf(rsForecast!RevInterval < txtRevInterval.MinValue, _
'                            txtRevInterval.MinValue, _
'                            (IIf(rsForecast!RevInterval > txtRevInterval.MaxValue, txtRevInterval.MaxValue, rsForecast!RevInterval)))
'    Me.txtFcstContact.Text = rsForecast!FcstContact
'    Me.txtFcstEMail.Text = rsForecast!FcstEmail
'
'    SetTDBDate txtFcstStartDate, rsForecast!FcstStartDate
'
'    SetTDBDate txtNextRevDate, rsForecast!NextRevdate
'
'Exit Function
'ErrorHandler:
''    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
''    Set rsTemp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_Refresh)"
'End Function
'
'Private Function f_AIMForecast_Save() As Integer
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim SqlStmt As String
'    Dim AIM_AIMForecast_Save_Sp As ADODB.Command
'    Dim strMessage As String
'
'    'Validate the data
'    RtnCode = f_AIMForecast_Validate
'    If RtnCode <> SUCCEED Then
'        f_AIMForecast_Save = RtnCode
'        Exit Function
'    End If
'    If ckFcstLocked.Value = vbChecked Then
'        strMessage = getTranslationResource("MSGBOX07308")
'        If StrComp(strMessage, "MSGBOX07308") = 0 Then _
'                strMessage = "This forecast is in use within the Forecast Repository. Modification not allowed "
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        Exit Function
'    End If
'
'    'Start writing to database
'    Set AIM_AIMForecast_Save_Sp = New ADODB.Command
'    With AIM_AIMForecast_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_AIMForecast_Save_Sp"
'
'        .Parameters.Refresh
'        .Parameters("@FcstID").Value = txtFcstId.Text       'la here
'        .Parameters("@FcstDesc").Value = txtFcstDesc.Text
'        .Parameters("@FcstLocked").Value = IIf(ckFcstLocked.Value = vbChecked, "Y", "N")
'        .Parameters("@FcstStatus").Value = IIf(ckFcstStatus.Value = vbChecked, VALUETRUE, VALUEFALSE)
'
'        .Parameters("@FcstStartDate").Value = txtFcstStartDate.Text
'        .Parameters("@FcstPds").Value = txtFcstPds.Value
'        .Parameters("@AdjustPct").Value = txtAdjustPct.Text
'
'        .Parameters("@FcstLevel").Value = IIf(ckFcstLevel.Value = vbChecked, VALUETRUE, VALUEFALSE)
'        .Parameters("@NetRqmts").Value = IIf(Me.ckNetRqmts.Value = vbChecked, VALUETRUE, VALUEFALSE)
'
'        If Me.optApplyTrend(0).Value = True Then
'            .Parameters("@ApplyTrend").Value = 0
'        ElseIf Me.optApplyTrend(1).Value = True Then
'            .Parameters("@ApplyTrend").Value = 1
'        Else
'            .Parameters("@ApplyTrend").Value = 2
'        End If
'
'
'        If Me.opFcstUnit(0).Value Then
'            .Parameters("@FcstUnit").Value = 0
'        ElseIf Me.opFcstUnit(1).Value Then
'            .Parameters("@FcstUnit").Value = 1
'        ElseIf Me.opFcstUnit(2).Value Then
'            .Parameters("@FcstUnit").Value = 2
'        ElseIf Me.opFcstUnit(3).Value Then
'            .Parameters("@FcstUnit").Value = 3
'        ElseIf Me.opFcstUnit(4).Value Then
'            .Parameters("@FcstUnit").Value = 4
'        End If
'
'
'        If Me.opFcstInterval(0).Value Then
'            .Parameters("@FcstInterval").Value = 0
'        ElseIf Me.opFcstInterval(1).Value Then
'            .Parameters("@FcstInterval").Value = 1
'        ElseIf Me.opFcstInterval(2).Value Then
'            .Parameters("@FcstInterval").Value = 2
'        ElseIf Me.opFcstInterval(3).Value Then
'            .Parameters("@FcstInterval").Value = 3
'        ElseIf Me.opFcstInterval(4).Value Then
'            .Parameters("@FcstInterval").Value = 4
'        ElseIf Me.opFcstInterval(5).Value Then
'            .Parameters("@FcstInterval").Value = 5
'        ElseIf Me.opFcstInterval(6).Value Then
'            .Parameters("@FcstInterval").Value = 6
'        ElseIf Me.opFcstInterval(7).Value Then
'            .Parameters("@FcstInterval").Value = 7
'        End If
'
'        .Parameters("@FcstLinkID").Value = dcForecast(IDX_DCFORECAST_FCSTLINKID).Text
'        .Parameters("@EnableFcstVersions").Value = IIf(ckEnableFcstVersions.Value = vbChecked, "Y", "N")
'        .Parameters("@FuturePdsUpdate").Value = IIf(ckFuturePdsUpdate.Value = vbChecked, "Y", "N")
'        .Parameters("@FcstOutput").Value = IIf(ckFcstOutput.Value = vbChecked, "Y", "N")
'
'        .Parameters("@Item").Value = Me.txtItem.Text
'        .Parameters("@ItStat").Value = Me.txtItemStatus.Text
'        .Parameters("@VnId").Value = Me.txtVnId.Text
'        .Parameters("@Assort").Value = Me.txtAssort.Text
'        .Parameters("@LcId").Value = Me.txtLcId.Text
'        .Parameters("@ById").Value = Me.txtById.Text
'        .Parameters("@Class1").Value = Me.txtClass(1).Text
'        .Parameters("@Class2").Value = Me.txtClass(2).Text
'        .Parameters("@Class3").Value = Me.txtClass(3).Text
'        .Parameters("@Class4").Value = Me.txtClass(4).Text
'        .Parameters("@LDivision").Value = Me.txtLDivision.Text
'        .Parameters("@LStatus").Value = Me.txtLStatus.Text
'        .Parameters("@LRegion").Value = Me.txtLRegion.Text
'        .Parameters("@LUserDefined").Value = Me.txtLUserDefined.Text
'        If Me.opRevFreq(0) Then
'            .Parameters("@RevFreq").Value = 2
'        ElseIf Me.opRevFreq(1) Then
'            .Parameters("@RevFreq").Value = 3
'        End If
'
'        .Parameters("@RevInterval").Value = Me.txtRevInterval.Value
'        .Parameters("@NextRevDate").Value = Me.txtNextRevDate.Text
'        .Parameters("@FcstContact").Value = Me.txtFcstContact.Text
'        .Parameters("@FcstEMail").Value = Me.txtFcstEMail.Text
'
'        If m_NewFcst = True Then
'            'Insert
'            .Parameters("@InsertUpdate").Value = "I"
'        Else
'            'Update
'            .Parameters("@InsertUpdate").Value = "U"
'        End If
'
'        'Run the Stored Procedure
'        .Execute , , adExecuteNoRecords
'    End With
'    RtnCode = AIM_AIMForecast_Save_Sp.Parameters(0).Value
'    If RtnCode < 0 Then
'        f_AIMForecast_Save = FAIL
'        strMessage = getTranslationResource("ERRMSG07305")
'        If StrComp(strMessage, "ERRMSG07305") = 0 Then strMessage = "Error editing AIM Forecast record."
'        Write_Message strMessage
'    Else
'        f_AIMForecast_Save = SUCCEED
'
'        f_AIMForecast_GetKey rsForecast, txtFcstId.Text, SQL_GetEq
'        GetForecastInformation IDX_DCFORECAST_FCSTID
'        Me.dcForecast(IDX_DCFORECAST_FCSTID).Text = rsForecast!FcstId       'this will trigger refresh
'
'        'Add to UserAccess if new record.
'        If m_NewFcst Then
'            'Save User ID to Forecast Maintenance User Access
'            SaveMainUserAccess
'            'Set the Insert flag to false
'            m_NewFcst = False
'            'Show add, delete and the dropdown. Hide the text box
'            Me.tbNavigation.Tools("ID_AddNew").Enabled = True
'            tbNavigation.Tools("ID_Delete").Enabled = True
'            dcForecast(IDX_DCFORECAST_FCSTID).Visible = True
'            txtFcstId.Visible = False
'        End If
'
'        strMessage = getTranslationResource("STATMSG07305")
'        If StrComp(strMessage, "STATMSG07305") = 0 Then strMessage = "AIM Forecast record successfully updated."
'        Write_Message strMessage
'    End If
'
'    'Clean Up
'    If Not (AIM_AIMForecast_Save_Sp Is Nothing) Then Set AIM_AIMForecast_Save_Sp.ActiveConnection = Nothing
'    Set AIM_AIMForecast_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_AIMForecast_Save_Sp Is Nothing) Then Set AIM_AIMForecast_Save_Sp.ActiveConnection = Nothing
'    Set AIM_AIMForecast_Save_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_Save)"
'End Function
'
'Private Function f_AIMForecast_Validate() As Long
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim MessageRequired As Boolean
'
'    Dim RtnCode As String
'
'    strMessage = getTranslationResource("MSGBOX07312")
'    If StrComp(strMessage, "MSGBOX07312") = 0 Then _
'                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
'
'    'Start validating
'    If txtFcstId.Text = "" _
'    Or StrComp(txtFcstId.Text, getTranslationResource("New Fcst")) = 0 _
'    Then
'        strMessage = strMessage & vbCrLf & _
'                    lblForecastInfo(0).Caption  'ForecastID
'        If txtFcstId.Visible = False Then
'            dcForecast(IDX_DCFORECAST_FCSTID).SetFocus
'        Else
'            txtFcstId.SetFocus
'        End If
'        MessageRequired = True
'    End If
'
'    If Me.txtFcstDesc.Text = "" _
'    Or StrComp(txtFcstDesc.Text, getTranslationResource("New Fcst Description")) = 0 _
'    Then
'    strMessage = strMessage & vbCrLf & _
'                    lblForecastInfo(1).Caption  'Description
'        txtFcstDesc.SetFocus
'        MessageRequired = True
'    End If
'
'    If Not (IsDate(txtFcstStartDate.Text)) Then
'        strMessage = strMessage & vbCrLf & _
'                    lblForecastCriteria(0).Caption  'Start Date
'        txtFcstStartDate.SetFocus
'        MessageRequired = True
'    End If
'
'    If Not (IsNumeric(txtFcstPds.Value)) _
'    Or (txtFcstPds.Value < 0) Then
'        strMessage = strMessage & vbCrLf & _
'                    lblForecastCriteria(1).Caption  'Forecast Periods
'        txtFcstPds.SetFocus
'        MessageRequired = True
'    End If
'
'    If Me.txtAdjustPct.Value < 0 Then
'        strMessage = strMessage & vbCrLf & _
'                    lblForecastCriteria(2).Caption  'Forecast Adj Pct
'        txtAdjustPct.SetFocus
'        MessageRequired = True
'    End If
'
'    If MessageRequired Then
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        f_AIMForecast_Validate = FAIL
'    Else
'        f_AIMForecast_Validate = SUCCEED
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(f_AIMForecast_Validate)"
'End Function
'
'Private Function f_ChildTable_Update(p_ControlName As String, Cancel As Boolean)
'On Error GoTo ErrorHandler
'
'    Select Case p_ControlName
'        Case dgForecastDetail.Name
'            If Cancel = True Then
'                rsForecastDetail.CancelUpdate
'            Else
'                rsForecastDetail.Update
'            End If
'
'            dgForecastDetail.Redraw = True
'
'        Case dgFreezePds.Name
'            If Cancel = True Then
'                rsFreezePds.CancelUpdate
'            Else
'                rsFreezePds.Update
'            End If
'
'            dgFreezePds.Redraw = True
'
'        Case dgMainUserAccess.Name
'            If Cancel = True Then
'                rsMainUserAccess.CancelUpdate
'            Else
'                rsMainUserAccess.Update
'            End If
'
'            dgMainUserAccess.Redraw = True
'
'        Case dgUserAccess.Name
'            If Cancel = True Then
'                rsUserAccess.CancelUpdate
'            Else
'                rsUserAccess.Update
'            End If
'
'            dgUserAccess.Redraw = True
'
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source & "(f_ChildTable_Update)", Err.Description
'End Function
'
'Private Function ForecastInfoToggle(p_Enabled As Boolean)
'On Error GoTo ErrorHandler
'
'    Me.txtFcstId.Enabled = p_Enabled
'    Me.dcForecast(IDX_DCFORECAST_FCSTID).Enabled = p_Enabled
'    Me.txtFcstDesc.Enabled = p_Enabled
'    Me.ckFcstStatus.Enabled = p_Enabled
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ForecastInfoToggle)"
'End Function
'
'Private Function ForecastFieldsToggle(p_Enabled As Boolean)
'On Error GoTo ErrorHandler
'    Dim lngCounter As Long
'
'    If atForecastCriteria.Tabs(1).Selected Then
'        Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
'        Me.tbNavigation.Tools("ID_Delete").Enabled = p_Enabled
'    Else
'        Me.tbNavigation.Tools("ID_Save").Enabled = True
'        Me.tbNavigation.Tools("ID_Delete").Enabled = True
'    End If
'
'    Me.tbNavigation.Tools("ID_ItemFilter").Enabled = p_Enabled
'    Me.txtFcstStartDate.Enabled = p_Enabled
'    Me.txtFcstPds.Enabled = p_Enabled
'    Me.txtAdjustPct.Enabled = p_Enabled
'
'    Me.dcForecast(IDX_DCFORECAST_FCSTLINKID).Enabled = p_Enabled
'    Me.ckFuturePdsUpdate.Enabled = p_Enabled
'    Me.ckFcstOutput.Enabled = p_Enabled
'    Me.ckEnableFcstVersions.Enabled = p_Enabled
'    Me.ckFcstLevel.Enabled = p_Enabled
'    Me.ckNetRqmts.Enabled = p_Enabled
'
'    For lngCounter = 0 To Me.optApplyTrend.Count - 1
'        Me.optApplyTrend(lngCounter).Enabled = p_Enabled
'    Next
'
'    For lngCounter = 0 To Me.opFcstInterval.Count - 1
'        Me.opFcstInterval(lngCounter).Enabled = p_Enabled
'    Next
'
'    For lngCounter = 0 To Me.opFcstUnit.Count - 1
'        Me.opFcstUnit(lngCounter).Enabled = p_Enabled
'    Next
'
'    For lngCounter = 0 To Me.opRevFreq.Count - 1
'        Me.opRevFreq(lngCounter).Enabled = p_Enabled
'    Next
'
'    Me.txtRevInterval.Enabled = p_Enabled
'    Me.txtNextRevDate.Enabled = p_Enabled
'
'    Me.txtFcstContact.Enabled = p_Enabled
'    Me.txtFcstEMail.Enabled = p_Enabled
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(ForecastFieldsToggle)"
'End Function
'
'Private Sub txtClass_Change(Index As Integer)
'On Error GoTo ErrorHandler
'    If IsNull(txtClass(Index).Text) _
'    Or Trim(txtClass(Index).Text) = "" _
'    Then
'        txtClassDesc(Index).Text = ""
'    Else
'        txtClassDesc(Index).Text = GetClassDescription(Cn, txtClass(Index).Text, (Index))
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtClass(" & Index & ")_Change)"
'End Sub
'
'Private Function FetchGivenFcstID() As Integer
'On Error GoTo ErrorHandler
''If successful, this function will overwrite rsForecast, else not.
'
'    Dim RtnCode As Integer
'    Dim strMessage As String
'    Dim rsTemp As ADODB.Recordset
'
'    'Set default to failure
'    FetchGivenFcstID = -1
'
'    Set rsTemp = New ADODB.Recordset
'    RtnCode = f_AIMForecast_GetKey(rsTemp, dcForecast(IDX_DCFORECAST_FCSTID).Text, SQL_GetEq)
'
'    If RtnCode = SUCCEED Then
'        'Update rsForecast
'        On Error Resume Next
'        If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
'            If Err.Number = 3219 Then
'                'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
'                'Ignore this error, discard the rs and move on.
'                Set rsForecast = Nothing
'            End If
'        Set rsForecast = Nothing
'        On Error GoTo ErrorHandler
'        'Re-create the recordset from scratch
'        Set rsForecast = New ADODB.Recordset
'        Set rsForecast.ActiveConnection = Cn
'        Set rsForecast = rsTemp.Clone
'        FetchGivenFcstID = SUCCEED
'    Else
'        'Leave rsForecast as-is.
'        FetchGivenFcstID = -1   'failed
'    End If
'
'    'Clean up
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'
'Exit Function
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
'    Set rsTemp = Nothing
'    Err.Raise Err.Number, Err.Source & "(FetchGivenFcstID)", Err.Description
'End Function
