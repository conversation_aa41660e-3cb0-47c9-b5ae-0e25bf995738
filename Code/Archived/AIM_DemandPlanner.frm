VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{E8671A8B-E5DD-11CD-836C-0000C0C14E92}#1.0#0"; "SSCALA32.OCX"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Begin VB.Form AIM_DemandPlanner 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Forecast Planner"
   ClientHeight    =   10005
   ClientLeft      =   975
   ClientTop       =   375
   ClientWidth     =   14685
   FillColor       =   &H00FFFFFF&
   BeginProperty Font 
      Name            =   "Microsoft Sans Serif"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   ForeColor       =   &H8000000F&
   Icon            =   "AIM_DemandPlanner.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9838.754
   ScaleMode       =   0  'User
   ScaleWidth      =   14655.92
   WindowState     =   2  'Maximized
   Begin VB.Frame fraCommon 
      Height          =   4965
      Left            =   105
      TabIndex        =   0
      Top             =   122
      Width           =   14475
      Begin TDBText6Ctl.TDBText txtFcstComments 
         Height          =   705
         Left            =   9840
         TabIndex        =   10
         Top             =   220
         Width           =   4470
         _Version        =   65536
         _ExtentX        =   7885
         _ExtentY        =   1244
         Caption         =   "AIM_DemandPlanner.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_DemandPlanner.frx":037D
         Key             =   "AIM_DemandPlanner.frx":039B
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBDate6Ctl.TDBDate txtFcstStartDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   2535
         TabIndex        =   4
         Top             =   580
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Calendar        =   "AIM_DemandPlanner.frx":03DF
         Caption         =   "AIM_DemandPlanner.frx":04F7
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_DemandPlanner.frx":0563
         Keys            =   "AIM_DemandPlanner.frx":0581
         Spin            =   "AIM_DemandPlanner.frx":05DF
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   -1
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "__/__/____"
         ValidateMode    =   0
         ValueVT         =   1
         Value           =   36443
         CenturyMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtFcstPeriods_Future 
         Height          =   345
         Left            =   6810
         TabIndex        =   6
         Top             =   200
         Width           =   990
         _Version        =   65536
         _ExtentX        =   1746
         _ExtentY        =   609
         Calculator      =   "AIM_DemandPlanner.frx":0607
         Caption         =   "AIM_DemandPlanner.frx":0627
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_DemandPlanner.frx":0693
         Keys            =   "AIM_DemandPlanner.frx":06B1
         Spin            =   "AIM_DemandPlanner.frx":06FB
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   255
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2010578945
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtFcstPeriods_Historical 
         Height          =   345
         Left            =   6810
         TabIndex        =   8
         Top             =   580
         Width           =   990
         _Version        =   65536
         _ExtentX        =   1746
         _ExtentY        =   609
         Calculator      =   "AIM_DemandPlanner.frx":0723
         Caption         =   "AIM_DemandPlanner.frx":0743
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_DemandPlanner.frx":07AF
         Keys            =   "AIM_DemandPlanner.frx":07CD
         Spin            =   "AIM_DemandPlanner.frx":0817
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   255
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   4390913
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcForecastID 
         Height          =   345
         Left            =   2535
         TabIndex        =   2
         Top             =   200
         Width           =   1590
         DataFieldList   =   "FcstID"
         ListAutoValidate=   0   'False
         ListAutoPosition=   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2805
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "FcstID"
      End
      Begin C1Chart2D8.Chart2D chtForecast 
         Height          =   3885
         Left            =   120
         TabIndex        =   11
         Top             =   960
         Width           =   14175
         _Version        =   524288
         _Revision       =   7
         _ExtentX        =   25003
         _ExtentY        =   6853
         _StockProps     =   0
         ControlProperties=   "AIM_DemandPlanner.frx":083F
      End
      Begin VB.Label lblComments 
         Caption         =   "Comments"
         Height          =   300
         Left            =   8040
         TabIndex        =   9
         Top             =   240
         Width           =   1755
      End
      Begin VB.Label lblFcstPeriods_Future 
         Caption         =   "Future Periods"
         Height          =   300
         Left            =   4680
         TabIndex        =   5
         Top             =   245
         Width           =   1995
      End
      Begin VB.Label lblFcstPeriods_Historical 
         Caption         =   "Historical Periods"
         Height          =   300
         Left            =   4680
         TabIndex        =   7
         Top             =   625
         Width           =   1995
      End
      Begin VB.Label Label20 
         Caption         =   "Last updated"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   3
         Top             =   625
         Width           =   2325
      End
      Begin VB.Label Label8 
         Caption         =   "Forecast ID"
         Height          =   300
         Left            =   120
         TabIndex        =   1
         Top             =   245
         Width           =   2325
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbFcstModification 
      Left            =   120
      Top             =   9600
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   14
      Style           =   0
      DisplayContextMenu=   0   'False
      Tools           =   "AIM_DemandPlanner.frx":0E52
      ToolBars        =   "AIM_DemandPlanner.frx":CD6C
   End
   Begin ActiveTabs.SSActiveTabs atModification 
      Height          =   4335
      Left            =   150
      TabIndex        =   12
      Top             =   5160
      Width           =   14475
      _ExtentX        =   25532
      _ExtentY        =   7646
      _Version        =   131083
      TabCount        =   5
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TabMinWidth     =   390
      TabMinHeight    =   0
      Tabs            =   "AIM_DemandPlanner.frx":D0C7
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel7 
         Height          =   3960
         Left            =   30
         TabIndex        =   73
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   6985
         _Version        =   131083
         TabGuid         =   "AIM_DemandPlanner.frx":D20B
         Begin VB.Frame fraOptions 
            Height          =   3615
            Left            =   120
            TabIndex        =   110
            Top             =   0
            Width           =   14175
            Begin VB.Frame Frame1 
               Caption         =   "User Elements"
               Height          =   3255
               Left            =   9120
               TabIndex        =   69
               Top             =   200
               Width           =   4980
               Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgUserElements 
                  Height          =   2925
                  Left            =   120
                  TabIndex        =   70
                  Top             =   240
                  Width           =   4680
                  _Version        =   196617
                  DataMode        =   1
                  AllowAddNew     =   -1  'True
                  AllowDelete     =   -1  'True
                  AllowUpdate     =   0   'False
                  AllowColumnSwapping=   0
                  UseExactRowCount=   0   'False
                  ForeColorEven   =   0
                  BackColorOdd    =   12648447
                  RowHeight       =   423
                  CaptionAlignment=   0
                  Columns(0).Width=   3200
                  _ExtentX        =   8255
                  _ExtentY        =   5159
                  _StockProps     =   79
                  ForeColor       =   -2147483630
               End
            End
            Begin VB.Frame fraInterval 
               Caption         =   "Forecast Interval"
               Height          =   1815
               Left            =   90
               TabIndex        =   52
               Top             =   1680
               Width           =   4020
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Days"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   53
                  Top             =   230
                  Width           =   1335
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Weeks"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   1
                  Left            =   120
                  TabIndex        =   54
                  Top             =   592
                  Width           =   1335
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Months"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   2
                  Left            =   120
                  TabIndex        =   55
                  Top             =   954
                  Value           =   -1  'True
                  Width           =   1335
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Quarters"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   3
                  Left            =   120
                  TabIndex        =   56
                  Top             =   1316
                  Width           =   1335
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "5-4-4"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   4
                  Left            =   1800
                  TabIndex        =   57
                  Top             =   230
                  Width           =   1095
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-5-4"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   5
                  Left            =   1800
                  TabIndex        =   58
                  Top             =   592
                  Width           =   1095
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-4-5"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   6
                  Left            =   1800
                  TabIndex        =   59
                  Top             =   954
                  Width           =   1095
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-Week"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   7
                  Left            =   1800
                  TabIndex        =   60
                  Top             =   1316
                  Width           =   1095
               End
            End
            Begin VB.Frame fraDataElements 
               Caption         =   "Data Elements"
               Height          =   3255
               Left            =   4200
               TabIndex        =   61
               Top             =   200
               Width           =   4860
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Planned receipts"
                  Enabled         =   0   'False
                  Height          =   340
                  Index           =   6
                  Left            =   480
                  TabIndex        =   64
                  Top             =   1380
                  Width           =   4215
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Show with production constraints"
                  Enabled         =   0   'False
                  Height          =   340
                  Index           =   8
                  Left            =   480
                  TabIndex        =   67
                  Top             =   2140
                  Width           =   4215
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Show with production constraints"
                  Enabled         =   0   'False
                  Height          =   340
                  Index           =   5
                  Left            =   480
                  TabIndex        =   65
                  Top             =   1000
                  Width           =   4215
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Projected inventory"
                  Height          =   340
                  Index           =   7
                  Left            =   120
                  TabIndex        =   66
                  Top             =   1760
                  Width           =   4575
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Historical demand"
                  Height          =   340
                  Index           =   9
                  Left            =   120
                  TabIndex        =   68
                  Top             =   2520
                  Width           =   4575
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "System/Gross requirements"
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   62
                  Top             =   240
                  Width           =   4575
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Net requirements"
                  Height          =   340
                  Index           =   3
                  Left            =   120
                  TabIndex        =   63
                  Top             =   620
                  Width           =   4575
               End
            End
            Begin VB.Frame fraUnits 
               Caption         =   "Forecast Unit"
               Height          =   1455
               Left            =   90
               TabIndex        =   46
               Top             =   200
               Width           =   4020
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Cube"
                  Height          =   300
                  Index           =   4
                  Left            =   1800
                  TabIndex        =   51
                  Top             =   585
                  Width           =   1455
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Units"
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   47
                  Top             =   230
                  Value           =   -1  'True
                  Width           =   1455
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Cost"
                  Height          =   300
                  Index           =   1
                  Left            =   120
                  TabIndex        =   48
                  Top             =   585
                  Width           =   1455
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Price"
                  Height          =   300
                  Index           =   2
                  Left            =   120
                  TabIndex        =   49
                  Top             =   940
                  Width           =   1455
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Weight"
                  Height          =   300
                  Index           =   3
                  Left            =   1800
                  TabIndex        =   50
                  Top             =   230
                  Width           =   1455
               End
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel6 
         Height          =   3960
         Left            =   30
         TabIndex        =   72
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   6985
         _Version        =   131083
         TabGuid         =   "AIM_DemandPlanner.frx":D233
         Begin VB.Frame fraForecastFilters 
            Height          =   3735
            Left            =   120
            TabIndex        =   134
            Top             =   0
            Width           =   10575
            Begin TDBText6Ctl.TDBText txtVnId 
               Height          =   345
               Left            =   2865
               TabIndex        =   135
               TabStop         =   0   'False
               Top             =   1004
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D25B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D2C7
               Key             =   "AIM_DemandPlanner.frx":D2E5
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAssort 
               Height          =   345
               Left            =   2865
               TabIndex        =   136
               TabStop         =   0   'False
               Top             =   1386
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D329
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D395
               Key             =   "AIM_DemandPlanner.frx":D3B3
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLcId 
               Height          =   345
               Left            =   7905
               TabIndex        =   137
               TabStop         =   0   'False
               Top             =   240
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D3F7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D463
               Key             =   "AIM_DemandPlanner.frx":D481
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItem 
               Height          =   345
               Left            =   2865
               TabIndex        =   138
               TabStop         =   0   'False
               Top             =   240
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D4C5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D531
               Key             =   "AIM_DemandPlanner.frx":D54F
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   25
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtById 
               Height          =   345
               Left            =   2865
               TabIndex        =   139
               TabStop         =   0   'False
               Top             =   1768
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D593
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D5FF
               Key             =   "AIM_DemandPlanner.frx":D61D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   1
               Left            =   2865
               TabIndex        =   140
               TabStop         =   0   'False
               Top             =   2150
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D661
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D6CD
               Key             =   "AIM_DemandPlanner.frx":D6EB
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   2
               Left            =   2865
               TabIndex        =   141
               TabStop         =   0   'False
               Top             =   2532
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D72F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D79B
               Key             =   "AIM_DemandPlanner.frx":D7B9
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   3
               Left            =   2865
               TabIndex        =   142
               TabStop         =   0   'False
               Top             =   2914
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D7FD
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D869
               Key             =   "AIM_DemandPlanner.frx":D887
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItemStatus 
               Height          =   345
               Left            =   2865
               TabIndex        =   143
               TabStop         =   0   'False
               Top             =   622
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D8CB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":D937
               Key             =   "AIM_DemandPlanner.frx":D955
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   1
               Left            =   4860
               TabIndex        =   144
               TabStop         =   0   'False
               Top             =   2145
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":D999
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DA05
               Key             =   "AIM_DemandPlanner.frx":DA23
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   2
               Left            =   4860
               TabIndex        =   145
               TabStop         =   0   'False
               Top             =   2535
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DA67
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DAD3
               Key             =   "AIM_DemandPlanner.frx":DAF1
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   3
               Left            =   4860
               TabIndex        =   146
               TabStop         =   0   'False
               Top             =   2910
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DB35
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DBA1
               Key             =   "AIM_DemandPlanner.frx":DBBF
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLDivision 
               Height          =   345
               Left            =   7905
               TabIndex        =   147
               Top             =   1004
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DC03
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DC6F
               Key             =   "AIM_DemandPlanner.frx":DC8D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLUserDefined 
               Height          =   345
               Left            =   7905
               TabIndex        =   148
               Top             =   1768
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DCD1
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DD3D
               Key             =   "AIM_DemandPlanner.frx":DD5B
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLStatus 
               Height          =   345
               Left            =   7905
               TabIndex        =   149
               Top             =   622
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DD9F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DE0B
               Key             =   "AIM_DemandPlanner.frx":DE29
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   4
               Left            =   2880
               TabIndex        =   150
               TabStop         =   0   'False
               Top             =   3300
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DE6D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DED9
               Key             =   "AIM_DemandPlanner.frx":DEF7
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   4
               Left            =   4860
               TabIndex        =   151
               TabStop         =   0   'False
               Top             =   3300
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":DF3B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":DFA7
               Key             =   "AIM_DemandPlanner.frx":DFC5
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLRegion 
               Height          =   345
               Left            =   7905
               TabIndex        =   152
               Top             =   1386
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":E009
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":E075
               Key             =   "AIM_DemandPlanner.frx":E093
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 4"
               Height          =   300
               Index           =   9
               Left            =   120
               TabIndex        =   166
               Top             =   3345
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 3"
               Height          =   300
               Index           =   8
               Left            =   120
               TabIndex        =   165
               Top             =   2959
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 2"
               Height          =   300
               Index           =   7
               Left            =   120
               TabIndex        =   164
               Top             =   2577
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 1"
               Height          =   300
               Index           =   6
               Left            =   120
               TabIndex        =   163
               Top             =   2195
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Item"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   162
               Top             =   285
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Buyer ID"
               Height          =   300
               Index           =   5
               Left            =   120
               TabIndex        =   161
               Top             =   1853
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Vendor ID"
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   160
               Top             =   1069
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Assortment"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   159
               Top             =   1461
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Location ID"
               Height          =   300
               Index           =   4
               Left            =   5160
               TabIndex        =   158
               Top             =   285
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Item Status"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   157
               Top             =   677
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "User-defined"
               Height          =   300
               Index           =   5
               Left            =   5160
               TabIndex        =   156
               Top             =   1813
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Region"
               Height          =   300
               Index           =   4
               Left            =   5160
               TabIndex        =   155
               Top             =   1431
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Division"
               Height          =   300
               Index           =   3
               Left            =   5160
               TabIndex        =   154
               Top             =   1049
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Status"
               Height          =   300
               Index           =   1
               Left            =   5160
               TabIndex        =   153
               Top             =   667
               Width           =   2595
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel5 
         Height          =   3960
         Left            =   30
         TabIndex        =   71
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   6985
         _Version        =   131083
         TabGuid         =   "AIM_DemandPlanner.frx":E0D7
         Begin VB.Frame fraMetrics 
            Height          =   3855
            Left            =   120
            TabIndex        =   103
            Top             =   0
            Width           =   14175
            Begin VB.Frame Frame5 
               Caption         =   "Item Statistics"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   1755
               Left            =   10560
               TabIndex        =   123
               Top             =   1800
               Width           =   3375
               Begin TDBText6Ctl.TDBText txtFcstDemand 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   124
                  TabStop         =   0   'False
                  Top             =   240
                  Width           =   1200
                  _Version        =   65536
                  _ExtentX        =   2117
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E0FF
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E16B
                  Key             =   "AIM_DemandPlanner.frx":E189
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtMAE 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   125
                  TabStop         =   0   'False
                  Top             =   600
                  Width           =   1200
                  _Version        =   65536
                  _ExtentX        =   2117
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E1CD
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E239
                  Key             =   "AIM_DemandPlanner.frx":E257
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtTrend 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   126
                  TabStop         =   0   'False
                  Top             =   960
                  Width           =   1200
                  _Version        =   65536
                  _ExtentX        =   2117
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E29B
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E307
                  Key             =   "AIM_DemandPlanner.frx":E325
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtUOM 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   127
                  TabStop         =   0   'False
                  Top             =   1320
                  Width           =   690
                  _Version        =   65536
                  _ExtentX        =   1217
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E369
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E3D5
                  Key             =   "AIM_DemandPlanner.frx":E3F3
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   0
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin VB.Label Label21 
                  Caption         =   "Demand"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Left            =   120
                  TabIndex        =   131
                  Top             =   285
                  Width           =   1815
               End
               Begin VB.Label Label13 
                  Caption         =   "MAE"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Left            =   120
                  TabIndex        =   130
                  Top             =   645
                  Width           =   1815
               End
               Begin VB.Label Label10 
                  Caption         =   "Trend"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Left            =   120
                  TabIndex        =   129
                  Top             =   1005
                  Width           =   1815
               End
               Begin VB.Label Label2 
                  Caption         =   "UOM"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Left            =   120
                  TabIndex        =   128
                  Top             =   1365
                  Width           =   1815
               End
            End
            Begin VB.Frame Frame4 
               Caption         =   "Forecast Summary"
               Height          =   1410
               Left            =   7080
               TabIndex        =   104
               Top             =   120
               Width           =   6855
               Begin TDBText6Ctl.TDBText txtItems 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   111
                  TabStop         =   0   'False
                  Top             =   240
                  Width           =   1305
                  _Version        =   65536
                  _ExtentX        =   2302
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E437
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E4A3
                  Key             =   "AIM_DemandPlanner.frx":E4C1
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtUnits 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   112
                  TabStop         =   0   'False
                  Top             =   600
                  Width           =   1305
                  _Version        =   65536
                  _ExtentX        =   2302
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E505
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E571
                  Key             =   "AIM_DemandPlanner.frx":E58F
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtWeight 
                  Height          =   345
                  Left            =   2040
                  TabIndex        =   113
                  TabStop         =   0   'False
                  Top             =   960
                  Width           =   1305
                  _Version        =   65536
                  _ExtentX        =   2302
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E5D3
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E63F
                  Key             =   "AIM_DemandPlanner.frx":E65D
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtCost 
                  Height          =   345
                  Left            =   5520
                  TabIndex        =   114
                  TabStop         =   0   'False
                  Top             =   600
                  Width           =   1185
                  _Version        =   65536
                  _ExtentX        =   2090
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E6A1
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E70D
                  Key             =   "AIM_DemandPlanner.frx":E72B
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   0
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtCube 
                  Height          =   345
                  Left            =   5520
                  TabIndex        =   115
                  TabStop         =   0   'False
                  Top             =   240
                  Width           =   1185
                  _Version        =   65536
                  _ExtentX        =   2090
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E76F
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E7DB
                  Key             =   "AIM_DemandPlanner.frx":E7F9
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText TxtPrice 
                  Height          =   345
                  Left            =   5520
                  TabIndex        =   116
                  TabStop         =   0   'False
                  Top             =   960
                  Width           =   1185
                  _Version        =   65536
                  _ExtentX        =   2090
                  _ExtentY        =   609
                  Caption         =   "AIM_DemandPlanner.frx":E83D
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "MS Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_DemandPlanner.frx":E8A9
                  Key             =   "AIM_DemandPlanner.frx":E8C7
                  BackColor       =   14737632
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   -1
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   -1
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  MultiLine       =   0
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   255
                  LengthAsByte    =   0
                  Text            =   ""
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin VB.Label Label15 
                  Caption         =   "Items"
                  Height          =   300
                  Left            =   105
                  TabIndex        =   122
                  Top             =   285
                  Width           =   1815
               End
               Begin VB.Label Label16 
                  Caption         =   "Units"
                  Height          =   300
                  Left            =   105
                  TabIndex        =   121
                  Top             =   645
                  Width           =   1815
               End
               Begin VB.Label Label17 
                  Caption         =   "Weight"
                  Height          =   300
                  Left            =   105
                  TabIndex        =   120
                  Top             =   1005
                  Width           =   1815
               End
               Begin VB.Label Label18 
                  Caption         =   "Cost"
                  Height          =   300
                  Left            =   3585
                  TabIndex        =   119
                  Top             =   645
                  Width           =   1815
               End
               Begin VB.Label Label19 
                  Caption         =   "Cube"
                  Height          =   300
                  Left            =   3585
                  TabIndex        =   118
                  Top             =   285
                  Width           =   1815
               End
               Begin VB.Label Label23 
                  Caption         =   "Price"
                  Height          =   300
                  Left            =   3585
                  TabIndex        =   117
                  Top             =   1005
                  Width           =   1815
               End
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgBaseMetrics 
               Height          =   1290
               Left            =   120
               TabIndex        =   133
               Top             =   180
               Width           =   5415
               ScrollBars      =   0
               _Version        =   196617
               DataMode        =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               RecordSelectors =   0   'False
               ColumnHeaders   =   0   'False
               DividerStyle    =   0
               AllowUpdate     =   0   'False
               AllowColumnMoving=   0
               AllowColumnSwapping=   0
               UseExactRowCount=   0   'False
               SelectTypeCol   =   0
               SelectTypeRow   =   0
               MaxSelectedRows =   3
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               ExtraHeight     =   185
               Columns(0).Width=   3200
               Columns(0).Name =   "Title"
               Columns(0).DataField=   "Column 0"
               Columns(0).DataType=   8
               Columns(0).FieldLen=   256
               Columns(0).HasBackColor=   -1  'True
               Columns(0).BackColor=   -2147483637
               _ExtentX        =   9551
               _ExtentY        =   2275
               _StockProps     =   79
               Caption         =   "Base Metrics"
               ForeColor       =   -2147483630
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
            Begin VB.Label Label103 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   2625
               TabIndex        =   109
               Top             =   1800
               Width           =   1455
            End
            Begin VB.Label Label102 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4065
               TabIndex        =   108
               Top             =   1800
               Width           =   1455
            End
            Begin VB.Label Label101 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   5505
               TabIndex        =   107
               Top             =   1800
               Width           =   1575
            End
            Begin VB.Label Label100 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   7065
               TabIndex        =   106
               Top             =   1800
               Width           =   1455
            End
            Begin VB.Label Label73 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   8505
               TabIndex        =   105
               Top             =   1800
               Width           =   1455
            End
            Begin VB.Label Label99 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   2625
               TabIndex        =   102
               Top             =   3225
               Width           =   1455
            End
            Begin VB.Label Label75 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4065
               TabIndex        =   101
               Top             =   3225
               Width           =   1455
            End
            Begin VB.Label Label74 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   5505
               TabIndex        =   100
               Top             =   3225
               Width           =   1575
            End
            Begin VB.Label Label98 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   8505
               TabIndex        =   99
               Top             =   3225
               Width           =   1455
            End
            Begin VB.Label Label97 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   7065
               TabIndex        =   98
               Top             =   3225
               Width           =   1455
            End
            Begin VB.Label Label96 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   8505
               TabIndex        =   97
               Top             =   2370
               Width           =   1455
            End
            Begin VB.Label Label95 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "%"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   8505
               TabIndex        =   96
               Top             =   2085
               Width           =   1455
            End
            Begin VB.Label Label94 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   8505
               TabIndex        =   95
               Top             =   2655
               Width           =   1455
            End
            Begin VB.Label Label93 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   8505
               TabIndex        =   94
               Top             =   2940
               Width           =   1455
            End
            Begin VB.Label Label92 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   7065
               TabIndex        =   93
               Top             =   2370
               Width           =   1455
            End
            Begin VB.Label Label91 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "Next Year"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   7065
               TabIndex        =   92
               Top             =   2085
               Width           =   1455
            End
            Begin VB.Label Label90 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   7065
               TabIndex        =   91
               Top             =   2655
               Width           =   1455
            End
            Begin VB.Label Label89 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   7065
               TabIndex        =   90
               Top             =   2940
               Width           =   1455
            End
            Begin VB.Label Label88 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   5505
               TabIndex        =   89
               Top             =   2370
               Width           =   1575
            End
            Begin VB.Label Label87 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "%"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   5505
               TabIndex        =   88
               Top             =   2085
               Width           =   1575
            End
            Begin VB.Label Label86 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   5505
               TabIndex        =   87
               Top             =   2655
               Width           =   1575
            End
            Begin VB.Label Label85 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   5505
               TabIndex        =   86
               Top             =   2940
               Width           =   1575
            End
            Begin VB.Label Label84 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4065
               TabIndex        =   85
               Top             =   2370
               Width           =   1455
            End
            Begin VB.Label Label83 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "%"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4065
               TabIndex        =   84
               Top             =   2085
               Width           =   1455
            End
            Begin VB.Label Label82 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4065
               TabIndex        =   83
               Top             =   2655
               Width           =   1455
            End
            Begin VB.Label Label81 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   4065
               TabIndex        =   82
               Top             =   2940
               Width           =   1455
            End
            Begin VB.Label Label80 
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "User Element 3"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   81
               Top             =   3225
               Width           =   2520
            End
            Begin VB.Label Label79 
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "User Element 2"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   80
               Top             =   2940
               Width           =   2520
            End
            Begin VB.Label Label78 
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "User Element 1"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   79
               Top             =   2655
               Width           =   2520
            End
            Begin VB.Label Label77 
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "System Forecast"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   120
               TabIndex        =   78
               Top             =   2370
               Width           =   2520
            End
            Begin VB.Label Label76 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   2625
               TabIndex        =   77
               Top             =   2370
               Width           =   1455
            End
            Begin VB.Label Label72 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BorderStyle     =   1  'Fixed Single
               Caption         =   "Trend"
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   2625
               TabIndex        =   76
               Top             =   2085
               Width           =   1455
            End
            Begin VB.Label Label71 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00C0FFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   2625
               TabIndex        =   75
               Top             =   2655
               Width           =   1455
            End
            Begin VB.Label Label70 
               Alignment       =   2  'Center
               Appearance      =   0  'Flat
               BackColor       =   &H00FFFFFF&
               BorderStyle     =   1  'Fixed Single
               ForeColor       =   &H80000008&
               Height          =   300
               Left            =   2625
               TabIndex        =   74
               Top             =   2940
               Width           =   1455
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   3960
         Left            =   30
         TabIndex        =   30
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   6985
         _Version        =   131083
         TabGuid         =   "AIM_DemandPlanner.frx":E90B
         Begin VB.Frame fraAdj_Smr 
            Height          =   1095
            Left            =   120
            TabIndex        =   13
            Top             =   0
            Width           =   14175
            Begin VB.CheckBox ckToggleAdjDisplay 
               Caption         =   "Show available adjustments"
               Enabled         =   0   'False
               Height          =   375
               Index           =   0
               Left            =   120
               TabIndex        =   16
               Top             =   620
               Visible         =   0   'False
               Width           =   3735
            End
            Begin VB.OptionButton optAdjType_Sum 
               Caption         =   "Override"
               Enabled         =   0   'False
               Height          =   345
               Index           =   0
               Left            =   12120
               TabIndex        =   27
               Top             =   650
               Width           =   1935
            End
            Begin VB.OptionButton optAdjType_Sum 
               Caption         =   "+/- Units"
               Enabled         =   0   'False
               Height          =   345
               Index           =   1
               Left            =   9960
               TabIndex        =   26
               Top             =   650
               Width           =   1935
            End
            Begin VB.OptionButton optAdjType_Sum 
               Caption         =   "+/- Percentage"
               Height          =   345
               Index           =   2
               Left            =   7800
               TabIndex        =   25
               Top             =   650
               Value           =   -1  'True
               Width           =   1935
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjQty_Smr 
               Height          =   345
               Left            =   9360
               TabIndex        =   22
               Top             =   240
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_DemandPlanner.frx":E933
               Caption         =   "AIM_DemandPlanner.frx":E953
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":E9BF
               Keys            =   "AIM_DemandPlanner.frx":E9DD
               Spin            =   "AIM_DemandPlanner.frx":EA27
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1179653
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin SSCalendarWidgets_A.SSDateCombo dcAdjStart_Smr 
               Height          =   345
               Left            =   5760
               TabIndex        =   18
               Top             =   240
               Width           =   1860
               _Version        =   65543
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   16777215
               BeginProperty DropDownFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ShowCentury     =   -1  'True
            End
            Begin SSCalendarWidgets_A.SSDateCombo dcAdjEnd_Smr 
               Height          =   345
               Left            =   5760
               TabIndex        =   20
               Top             =   650
               Width           =   1860
               _Version        =   65543
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   16777215
               BeginProperty DropDownFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ShowCentury     =   -1  'True
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcGroupBy 
               Height          =   345
               Left            =   2160
               TabIndex        =   15
               Top             =   240
               Width           =   1830
               DataFieldList   =   "Column 1"
               ListAutoValidate=   0   'False
               ListAutoPosition=   0   'False
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   3228
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "Column 0"
            End
            Begin TDBText6Ctl.TDBText txtAdjDesc_Smr 
               Height          =   345
               Left            =   12120
               TabIndex        =   24
               Top             =   240
               Width           =   1935
               _Version        =   65536
               _ExtentX        =   3413
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":EA4F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":EAC2
               Key             =   "AIM_DemandPlanner.frx":EAE0
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Reason"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   10800
               TabIndex        =   23
               Top             =   285
               Width           =   1215
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "End date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   4200
               TabIndex        =   19
               Top             =   695
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Start date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   4200
               TabIndex        =   17
               Top             =   285
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Adjustment"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   7800
               TabIndex        =   21
               Top             =   285
               Width           =   1455
            End
            Begin VB.Label lblGroupBy 
               Caption         =   "Group by"
               Height          =   300
               Index           =   0
               Left            =   360
               TabIndex        =   14
               Top             =   285
               Width           =   1605
            End
         End
         Begin VB.Frame fraSummary 
            Height          =   2295
            Left            =   120
            TabIndex        =   28
            Top             =   1080
            Width           =   14175
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSummary 
               Height          =   2010
               Left            =   120
               TabIndex        =   29
               Top             =   195
               Width           =   13815
               _Version        =   196617
               DataMode        =   1
               AllowUpdate     =   0   'False
               AllowColumnMoving=   2
               AllowColumnSwapping=   0
               UseExactRowCount=   0   'False
               SelectTypeCol   =   3
               SelectTypeRow   =   3
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               SplitterVisible =   -1  'True
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   24368
               _ExtentY        =   3545
               _StockProps     =   79
               ForeColor       =   -2147483630
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
         Begin ActiveToolBars.SSActiveToolBars tbAdjustments 
            Index           =   0
            Left            =   120
            Top             =   3480
            _ExtentX        =   582
            _ExtentY        =   582
            _Version        =   131083
            ToolBarsCount   =   1
            ToolsCount      =   6
            Style           =   0
            Tools           =   "AIM_DemandPlanner.frx":EB24
            ToolBars        =   "AIM_DemandPlanner.frx":13797
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   3960
         Left            =   30
         TabIndex        =   132
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   6985
         _Version        =   131083
         TabGuid         =   "AIM_DemandPlanner.frx":13974
         Begin VB.Frame fraItems 
            Height          =   2295
            Left            =   120
            TabIndex        =   44
            Top             =   1080
            Width           =   14175
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgDetail 
               Height          =   2010
               Left            =   60
               TabIndex        =   45
               Top             =   120
               Width           =   13935
               _Version        =   196617
               DataMode        =   1
               AllowUpdate     =   0   'False
               AllowColumnMoving=   2
               AllowColumnSwapping=   0
               UseExactRowCount=   0   'False
               SelectTypeCol   =   3
               SelectTypeRow   =   3
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               SplitterVisible =   -1  'True
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   24580
               _ExtentY        =   3545
               _StockProps     =   79
               ForeColor       =   -2147483630
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
         Begin VB.Frame fraAdj_Dtl 
            Height          =   1095
            Left            =   120
            TabIndex        =   31
            Top             =   0
            Width           =   14175
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "+/- Percentage"
               Height          =   345
               Index           =   2
               Left            =   7800
               TabIndex        =   41
               Top             =   650
               Value           =   -1  'True
               Width           =   1935
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "+/- Units"
               Height          =   345
               Index           =   1
               Left            =   9960
               TabIndex        =   42
               Top             =   650
               Width           =   1935
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "Override"
               Height          =   345
               Index           =   0
               Left            =   12120
               TabIndex        =   43
               Top             =   650
               Width           =   1935
            End
            Begin VB.CheckBox ckToggleAdjDisplay 
               Caption         =   "Show available adjustments"
               Height          =   375
               Index           =   1
               Left            =   120
               TabIndex        =   32
               Top             =   210
               Width           =   3735
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjQty_Dtl 
               Height          =   345
               Left            =   9360
               TabIndex        =   38
               Top             =   240
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_DemandPlanner.frx":1399C
               Caption         =   "AIM_DemandPlanner.frx":139BC
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":13A28
               Keys            =   "AIM_DemandPlanner.frx":13A46
               Spin            =   "AIM_DemandPlanner.frx":13A90
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin SSCalendarWidgets_A.SSDateCombo dcAdjStart_Dtl 
               Height          =   345
               Left            =   5760
               TabIndex        =   34
               Top             =   240
               Width           =   1860
               _Version        =   65543
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   16777215
               BeginProperty DropDownFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ShowCentury     =   -1  'True
            End
            Begin SSCalendarWidgets_A.SSDateCombo dcAdjEnd_Dtl 
               Height          =   345
               Left            =   5760
               TabIndex        =   36
               Top             =   650
               Width           =   1860
               _Version        =   65543
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   16777215
               BeginProperty DropDownFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               ShowCentury     =   -1  'True
            End
            Begin TDBText6Ctl.TDBText txtAdjDesc_Dtl 
               Height          =   345
               Left            =   12120
               TabIndex        =   40
               Top             =   240
               Width           =   1935
               _Version        =   65536
               _ExtentX        =   3413
               _ExtentY        =   609
               Caption         =   "AIM_DemandPlanner.frx":13AB8
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_DemandPlanner.frx":13B2B
               Key             =   "AIM_DemandPlanner.frx":13B49
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Adjustment"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   7800
               TabIndex        =   37
               Top             =   285
               Width           =   1455
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Start date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   4200
               TabIndex        =   33
               Top             =   285
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "End date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   4200
               TabIndex        =   35
               Top             =   695
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Reason"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   10800
               TabIndex        =   39
               Top             =   285
               Width           =   1215
            End
         End
         Begin ActiveToolBars.SSActiveToolBars tbAdjustments 
            Index           =   1
            Left            =   720
            Top             =   3480
            _ExtentX        =   582
            _ExtentY        =   582
            _Version        =   131083
            ToolBarsCount   =   1
            ToolsCount      =   6
            Style           =   0
            Tools           =   "AIM_DemandPlanner.frx":13B8D
            ToolBars        =   "AIM_DemandPlanner.frx":18800
         End
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_DemandPlanner"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
' '*****************************************************************************
'' Copyright (c) 2004 SSA Global. All rights reserved.
''*****************************************************************************
''
''   AIM_DemandPlanner.frm
''
''   Version Number - 1.0
''   Last Updated   - 2004/01/01
''   Updated By     - Annalakshmi Stocksdale
''
''   This replaces the former AIM_ForecastModification screen in
''       allowing creation and modification of forecast data.
''
''   The "forecast generator/modification" process is being morphed
''   into demand planning, hence the options required for forecast generator
''   are being phased out, and additional options for demand planning
''   are to be created, as of version 4.5
''   See related updates to AIM_ForecastModification.
''
''*****************************************************************************
'' This file contains trade secrets of SSA Global. No part
'' may be reproduced or transmitted in any form by any means or for any purpose
'' without the express written permission of SSA Global.
''*****************************************************************************
'Option Explicit
'Option Base 1   'Sets default for all arrays on this page to start at 1
'
'Dim Cn As ADODB.Connection
'Dim m_rsGridSummary As ADODB.Recordset  'Disconnected recordset
'Dim m_rsGridDetail As ADODB.Recordset   'Disconnected recordset
'Dim m_rsUserElements As ADODB.Recordset 'Disconnected recordset
'
''Flags
'Dim m_IsInitialLoad As Boolean
'Dim m_IsRunning As Boolean
'Dim m_IsGridSummaryLoaded As Boolean
'Dim m_IsGridDetailLoaded As Boolean
'Dim m_IsChartVisible As Boolean
'Dim MinCalcType As Integer
'
''Modular Forecast Setup Criteria
'Dim m_FcstId As String
'Dim m_FcstSetupKey As Long
'Dim m_FcstStoreKey_Main As Long
'Dim m_FcstStartDate As Date
'Dim m_FcstLocked As Boolean
'Dim m_FcstEnabled As Boolean
'Dim m_FcstHierarchy As Long
'Dim m_FcstRolling As Boolean
'Dim m_FcstHistory As Boolean
'Dim m_ApplyTrend As Long
'Dim m_FcstInterval As Long
'Dim m_FcstUnit As Long
'Dim m_FirstPeriod As Date, m_FirstHistoricalPeriod As Date, m_LastPeriod As Date
'Dim m_DateIncrement As String
'Dim m_TotalPeriods As Long, m_PeriodCount As Long
'
'Dim m_PeriodCaption() As Date
'
'Dim m_FcstItemFilter As FCST_ITEM_FILTER    'Track existing values and user input for item filters
'Dim m_ForecastAdjust As FORECAST_ADJUST_LOG 'Track existing values and user input for adjustments
'Dim m_GroupByFieldName As String  ', GroupByFieldValue As String
'
'Dim m_AccessCode As Long
'Dim m_AccessDesc As String
'
''Column indices for the Summary and Detail grids)
'Dim m_SmrCol_GroupBy As Long
'Dim m_SmrCol_DataType As Long
'Dim m_SmrCol_DataTypeDesc As Long
'Dim m_SmrCol_Totals As Long
'Dim m_SmrCol_FirstPeriod As Long
'
'Dim m_DtlCol_LcID As Long
'Dim m_DtlCol_Item As Long
'Dim m_DtlCol_ItDesc As Long
'Dim m_DtlCol_ItStat As Long
'Dim m_DtlCol_DataType As Long
'Dim m_DtlCol_DataDesc As Long
'Dim m_DtlCol_Totals As Long
'Dim m_DtlCol_FirstPeriod As Long
'Dim m_DtlCol_Class1 As Long
'Dim m_DtlCol_Class2 As Long
'Dim m_DtlCol_Class3 As Long
'Dim m_DtlCol_Class4 As Long
'Dim m_DtlCol_VelCode As Long
'Dim m_DtlCol_VnID As Long
'Dim m_DtlCol_Assort As Long
'Dim m_DtlCol_ByID As Long
'Dim m_DtlCol_LStatus As Long
'Dim m_DtlCol_LDivision As Long
'Dim m_DtlCol_LRegion As Long
'Dim m_DtlCol_LUserDefined As Long
''End Column indices
''Current row bookmarks for the grids
'Dim m_SumBookMark As Variant
'Dim m_DtlBookMark As Variant
''End Current row bookmarks for the grids
'
''CONSTANTS
'Private Const GB_ASSORT As String = "Item.Assort"
'Private Const GB_BYID As String = "Item.ByID"
'Private Const GB_CLASS1 As String = "Item.Class1"
'Private Const GB_CLASS2 As String = "Item.Class2"
'Private Const GB_CLASS3 As String = "Item.Class3"
'Private Const GB_CLASS4 As String = "Item.Class4"
'Private Const GB_ITEM As String = "Item.Item"
'Private Const GB_LCID As String = "Item.LcID"
'Private Const GB_LDIVISION As String = "AIMLocations.LDivision"
'Private Const GB_LREGION As String = "AIMLocations.LRegion"
'Private Const GB_LSTATUS As String = "AIMLocations.LStatus"
'Private Const GB_LUSERDEFINED As String = "AIMLocations.LUserDefined"
'Private Const GB_VELCODE As String = "Item.VelCode"
'Private Const GB_VNID As String = "Item.VnID"
'
''SSTabs.Keys
'Private Const TAB_SUMMARY As String = "FORECASTSUMMARY"
'Private Const TAB_DETAIL As String = "FORECASTDETAIL"
'Private Const TAB_OPTIONS As String = "OPTIONS"
'Private Const TAB_FILTERS As String = "FILTERS"
'Private Const TAB_METRICS As String = "METRICS"
'
''SSToolbars.ToolIDs
'Private Const TB_ID_LOAD As String = "ID_LOAD"
'Private Const TB_ID_CHART_SYNC As String = "ID_CHART_SYNC"
'Private Const TB_ID_CHART_TOGGLESIZE As String = "ID_CHART_TOGGLESIZE"
'Private Const TB_ID_ITEMFILTER As String = "ID_ITEMFILTER"
'Private Const TB_ID_CLEARFILTER As String = "ID_CLEARFILTER"
'Private Const TB_ID_RECALCCURRENT As String = "ID_RECALCCURRENT"
'Private Const TB_ID_RUNFORECAST As String = "ID_RUNFORECAST"
'Private Const TB_ID_FORECASTSETUP As String = "ID_FORECASTSETUP"
'Private Const TB_ID_COPY As String = "ID_COPY"
'Private Const TB_ID_SAVEFORECAST As String = "ID_SAVEFORECAST"
'Private Const TB_ID_PROMOTE As String = "ID_PROMOTE"
'Private Const TB_ID_CLOSE As String = "ID_CLOSE"
'
'Private Const TB_ID_SETBOOKMARK As String = "ID_SETBOOKMARK"
'Private Const TB_ID_GOTOBOOKMARK As String = "ID_GOTOBOOKMARK"
'
'Private Sub atModification_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim ShowAdjustments As Boolean
'
'    Me.tbFcstModification.Redraw = False
'    Screen.MousePointer = vbHourglass
'
'    Select Case UCase$(NewTab.Key)
'    Case TAB_SUMMARY
'        If Not IsEmpty(m_SumBookMark) _
'        Or Not IsNull(m_SumBookMark) _
'        Then
'            m_SumBookMark = Null
'            Me.tbAdjustments(0).Tools(TB_ID_GOTOBOOKMARK).Enabled = False
'        End If
'        If m_IsGridSummaryLoaded = False Then
'            RtnCode = mGridSummary_Fetch
'        Else
'            RtnCode = mGridSummary_Set
'        End If
'        If RtnCode > 0 Then
'            RtnCode = mChart_Refresh(UCase$(NewTab.Key))
'        End If
'    Case TAB_DETAIL
'        If Not IsEmpty(m_DtlBookMark) _
'        Or Not IsNull(m_DtlBookMark) _
'        Then
'            m_DtlBookMark = Null
'            Me.tbAdjustments(1).Tools(TB_ID_GOTOBOOKMARK).Enabled = False
'        End If
'        If m_IsGridDetailLoaded = False Then
'            RtnCode = mGridDetail_Fetch
'        Else
'            If Me.ckToggleAdjDisplay(1).Value = vbChecked Then ShowAdjustments = True
'            RtnCode = mGridDetail_Set(ShowAdjustments)
'        End If
'        If RtnCode > 0 Then
'            RtnCode = mChart_Refresh(UCase$(NewTab.Key))
'        End If
'
'    Case TAB_METRICS
''        RtnCode = mItemStats_Set
'
'    End Select
'
'    Me.tbFcstModification.Redraw = True
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    Me.tbFcstModification.Redraw = True
'    Screen.MousePointer = vbNormal
'    f_HandleErr Me.Caption & "(atModification_BeforeTabClick)"
'End Sub
'
'Private Sub chtForecast_DblClick()
'On Error GoTo ErrorHandler
'
'    Me.chtForecast.IsBatched = True 'Disable the control from repainting until the process has finished
'
'    mChart_ToggleSize
'
'    Me.chtForecast.IsBatched = False    'Process is done. Repaint control
'
'Exit Sub
'ErrorHandler:
'    Me.chtForecast.IsBatched = False
'    f_HandleErr Me.Caption & "(chtForecast_DblClick)"
'End Sub
'
'Private Sub ckDataCalc_Type_Click(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'
'    Select Case Index
'    Case DATA_CALC_TYPE.DCT_NetRqmt
'        If Me.ckDataCalc_Type(Index).Value = vbChecked Then
'            Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Enabled = True
'            Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Enabled = True
'        Else
'    '        Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Value = vbUnchecked
'    '        Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Enabled = False
'            Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Value = vbUnchecked
'            Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Enabled = False
'        End If
'
'    Case DATA_CALC_TYPE.DCT_PrjInvt
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt).Value = vbChecked Then
'    '        Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtPrdCons).Enabled = True
'        Else
'    '        Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtPrdCons).Value = vbUnchecked
'    '        Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtPrdCons).Enabled = False
'        End If
'    End Select
'
'    If m_IsInitialLoad Then
'        If Me.ckDataCalc_Type(Index).Value = vbChecked Then MinCalcType = MinCalcType + 1
'    Else
'        If MinCalcType > 1 Then
'            If Me.ckDataCalc_Type(Index).Value = vbUnchecked Then MinCalcType = MinCalcType - 1 Else MinCalcType = MinCalcType + 1
'        Else
'            strMessage = getTranslationResource("MSGBOX00000")
'            If StrComp(strMessage, "MSGBOX00000") = 0 Then strMessage = "Forecast Planning requires one or more data elements to be active."
'            MsgBox strMessage, vbOKOnly + vbInformation, Me.Caption & " - " & Me.fraDataElements.Caption
'            Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = vbChecked
'        End If
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ckDataCalc_Type(" & CStr(Index) & ")_Click)"
'End Sub
'
'Private Sub ckToggleAdjDisplay_Click(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim ShowAdjustments As Boolean
'
'    Select Case Index
'    Case 0   'Summary
'        RtnCode = mGridSummary_Fetch
'    Case 1   'Detail
'        RtnCode = mGridDetail_Fetch
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(ckToggleAdjDisplay_Click)"
'End Sub
'
'Private Sub dcAdjEnd_Dtl_Validate(Cancel As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim FcstInterval As Long, IndexCounter As Long
'    Dim UserDate As Date, PeriodStart As Date, PeriodEnd As Date, PeriodIndex As Long
'    Dim strMessage As String
'
'    If Cn Is Nothing Then Exit Sub
'    Write_Message ""
'
'    'Find the Start and End boundaries for the specified forecast period.
'
'    'Using the date and forecast interval specified by the user...
'    UserDate = Me.dcAdjEnd_Dtl.Text
'    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
'        FcstInterval = IIf(Me.optFcstInterval(IndexCounter).Value = True, IndexCounter, FcstInterval)
'    Next
'    '...fetch boundary dates
'    RtnCode = gDate_Boundaries(Cn, UserDate, FcstInterval, PeriodStart, PeriodEnd, PeriodIndex)
'
'    If PeriodStart <> UserDate Then
'        Me.dcAdjEnd_Dtl.Text = Format(PeriodEnd, gDateFormat)
'
'        strMessage = getTranslationResource("STATMSG99999")
'        If StrComp(strMessage, "STATMSG99999") = 0 Then strMessage = "The selected date has been mapped to the interval's end date, as dictated by the AIM Calendar."
'        strMessage = Me.lblAdj_Sum(2).Caption & " " & strMessage
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAdjEnd_Dtl_Validate)"
'End Sub
'
'Private Sub dcAdjEnd_Smr_Validate(Cancel As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim FcstInterval As Long, IndexCounter As Long
'    Dim UserDate As Date, PeriodStart As Date, PeriodEnd As Date, PeriodIndex As Long
'    Dim strMessage As String
'
'    If Cn Is Nothing Then Exit Sub
'    Write_Message ""
'
'    'Find the Start and End boundaries for the specified forecast period.
'
'    'Using the date and forecast interval specified by the user...
'    UserDate = Me.dcAdjEnd_Smr.Text
'    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
'        FcstInterval = IIf(Me.optFcstInterval(IndexCounter).Value = True, IndexCounter, FcstInterval)
'    Next
'    '...fetch boundary dates
'    RtnCode = gDate_Boundaries(Cn, UserDate, FcstInterval, PeriodStart, PeriodEnd, PeriodIndex)
'
'    If PeriodStart <> UserDate Then
'        Me.dcAdjEnd_Smr.Text = Format(PeriodEnd, gDateFormat)
'
'        strMessage = getTranslationResource("STATMSG99999")
'        If StrComp(strMessage, "STATMSG99999") = 0 Then strMessage = "The selected date has been mapped to the interval's end date, as dictated by the AIM Calendar."
'        strMessage = Me.lblAdj_Sum(5).Caption & " " & strMessage
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAdjEnd_Smr_Validate)"
'End Sub
'
'Private Sub dcAdjStart_Dtl_Validate(Cancel As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim FcstInterval As Long, IndexCounter As Long
'    Dim UserDate As Date, PeriodStart As Date, PeriodEnd As Date, PeriodIndex As Long
'    Dim strMessage As String
'
'    If Cn Is Nothing Then Exit Sub
'    Write_Message ""
'
'    'Find the Start and End boundaries for the specified forecast period.
'
'    'Using the date and forecast interval specified by the user...
'    UserDate = Me.dcAdjStart_Dtl.Text
'    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
'        FcstInterval = IIf(Me.optFcstInterval(IndexCounter).Value = True, IndexCounter, FcstInterval)
'    Next
'    '...fetch boundary dates
'    RtnCode = gDate_Boundaries(Cn, UserDate, FcstInterval, PeriodStart, PeriodEnd, PeriodIndex)
'
'    If PeriodStart <> UserDate Then
'        Me.dcAdjStart_Dtl.Text = Format(PeriodStart, gDateFormat)
'
'        strMessage = getTranslationResource("STATMSG99998")
'        If StrComp(strMessage, "STATMSG99998") = 0 Then strMessage = "The selected date has been mapped to the interval's start date, as dictated by the AIM Calendar."
'        strMessage = Me.lblAdj_Sum(1).Caption & " " & strMessage
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAdjStart_Dtl_Validate)"
'End Sub
'
'Private Sub dcAdjStart_Smr_Validate(Cancel As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim FcstInterval As Long, IndexCounter As Long
'    Dim UserDate As Date, PeriodStart As Date, PeriodEnd As Date, PeriodIndex As Long
'    Dim strMessage As String
'
'    If Cn Is Nothing Then Exit Sub
'    Write_Message ""
'
'    'Find the Start and End boundaries for the specified forecast period.
'
'    'Using the date and forecast interval specified by the user...
'    UserDate = Me.dcAdjStart_Smr.Text
'    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
'        FcstInterval = IIf(Me.optFcstInterval(IndexCounter).Value = True, IndexCounter, FcstInterval)
'    Next
'    '...fetch boundary dates
'    RtnCode = gDate_Boundaries(Cn, UserDate, FcstInterval, PeriodStart, PeriodEnd, PeriodIndex)
'
'    If PeriodStart <> UserDate Then
'        Me.dcAdjStart_Smr.Text = Format(PeriodStart, gDateFormat)
'
'        strMessage = getTranslationResource("STATMSG99998")
'        If StrComp(strMessage, "STATMSG99998") = 0 Then strMessage = "The selected date has been mapped to the interval's start date, as dictated by the AIM Calendar."
'        strMessage = Me.lblAdj_Sum(4).Caption & " " & strMessage
'        Write_Message strMessage
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcAdjStart_Smr_Validate)"
'End Sub
'
'Private Sub dcForecastID_Change()
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim FetchSuccess As Boolean
'    Dim lngRecordIndex As Long
'
'    'Check for updates, first
''    If Not m_InitialLoad Then f_AIMForecast_Commit True
'    'Navigate to the selected forecast ID
'    If StrComp(m_FcstId, Me.dcForecastID.Text, vbTextCompare) <> 0 Then
'        Me.dcForecastID.Redraw = False  'Disable the control from repainting until the process has finished.
'        Screen.MousePointer = vbHourglass
'        m_FcstId = dcForecastID.Text
'        FetchSuccess = mFcstSetup_Fetch(SQL_GetEq, True)
'
'        Me.dcForecastID.Redraw = True   'Process is done. Repaint control
'        Screen.MousePointer = vbNormal
'    End If
'
'
'Exit Sub
'ErrorHandler:
'    Me.dcForecastID.Redraw = True
'    Screen.MousePointer = vbNormal
'    f_HandleErr Me.Caption & "(dcForecastID_Change)"
'End Sub
'
'Private Sub dcForecastID_Click()
'On Error GoTo ErrorHandler
''same as dcForecastID_Change
'
'    dcForecastID_Change
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecastID_Click)"
'End Sub
'
'Private Sub dcForecastID_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'    Dim EBFcstId As SSOleDBCombo   'Early-binding is faster than late-binding a COM control
'    Set EBFcstId = Me.dcForecastID
'
'    EBFcstId.Redraw = False  'Disable the control from repainting until the process has finished.
'    Screen.MousePointer = vbHourglass
'
'    EBFcstId.Columns(0).Caption = getTranslationResource("Forecast ID")
'    EBFcstId.Columns(0).Width = 1500
'    EBFcstId.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    EBFcstId.Columns(0).Alignment = ssCaptionAlignmentLeft
'    EBFcstId.Columns(0).DataField = "FcstID"
'
'    EBFcstId.Columns(1).Caption = getTranslationResource("Description")
'    EBFcstId.Columns(1).Width = 2880
'    EBFcstId.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    EBFcstId.Columns(1).Alignment = ssCaptionAlignmentLeft
'    EBFcstId.Columns(1).DataField = "FcstDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths EBFcstId, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To EBFcstID.Columns.Count - 1
''        EBFcstID.Columns(IndexCounter).HasHeadBackColor = True
''        EBFcstID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'    EBFcstId.Redraw = True   'Process is done. Repaint control.
'    Screen.MousePointer = vbNormal
'Exit Sub
'ErrorHandler:
'    EBFcstId.Redraw = True
'    Screen.MousePointer = vbNormal
'    Set EBFcstId = Nothing
'
'    f_HandleErr Me.Caption & "(dcForecastID_InitColumnProps)"
'End Sub
'
'Private Sub dcGroupBy_Change()
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Long
'    Dim lngRecordIndex As Long
'
'    If m_IsGridSummaryLoaded = False Then Exit Sub
'
'    If IsNull(dcGroupBy.Text) _
'    Or Trim(dcGroupBy.Text) = "" _
'    Then
'        Me.dcGroupBy.Text = GB_LCID
'    End If
'    m_GroupByFieldName = Me.dcGroupBy.Columns(1).Value
'
'    Me.dcGroupBy.Redraw = False 'Disable the control from repainting until the process has finished.
'    Screen.MousePointer = vbHourglass
'    'Check for updates, first
''    If Not m_InitialLoad Then f_AIMForecast_Commit True
'    'Navigate to the selected forecast ID
'    RtnCode = mGridSummary_Fetch
'
'    Me.dcGroupBy.Redraw = True  'Process is done. Repaint control.
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    Me.dcGroupBy.Redraw = True
'    Screen.MousePointer = vbNormal
'    f_HandleErr Me.Caption & "(dcForecastID_Change)"
'End Sub
'
'Private Sub dcGroupBy_Click()
'On Error GoTo ErrorHandler
'
'    dcGroupBy_Change
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcGroupBy_Click)"
'End Sub
'
'Private Sub dcGroupBy_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'    Dim EBGroupBy As SSOleDBCombo   'Early-binding is faster than late-binding a COM control
'
'    Set EBGroupBy = Me.dcGroupBy
'
'    EBGroupBy.Redraw = False     'Disable the control from repainting until the process has finished.
'    Screen.MousePointer = vbHourglass
'    'Clear any existing columns
'    EBGroupBy.Columns.RemoveAll
'
'    IndexCounter = 0
'    'Set up the list
'    With EBGroupBy
'        .Columns.Add IndexCounter
'        .Columns(IndexCounter).Name = "ColumnName"
'        .Columns(IndexCounter).Caption = getTranslationResource("Column Name")
'        .Columns(IndexCounter).Width = EBGroupBy.Width
'        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        IndexCounter = IndexCounter + 1
'
'        .Columns.Add IndexCounter
'        .Columns(IndexCounter).Name = "ColumnID"
'        .Columns(IndexCounter).Caption = getTranslationResource("Column ID")
'        .Columns(IndexCounter).Width = 1440
'        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        .Columns(IndexCounter).Visible = False
'        IndexCounter = IndexCounter + 1
'
'        .AddItem getTranslationResource(GB_ASSORT) & vbTab & GB_ASSORT
'        .AddItem getTranslationResource(GB_BYID) & vbTab & GB_BYID
'        .AddItem getTranslationResource(GB_CLASS1) & vbTab & GB_CLASS1
'        .AddItem getTranslationResource(GB_CLASS2) & vbTab & GB_CLASS2
'        .AddItem getTranslationResource(GB_CLASS3) & vbTab & GB_CLASS3
'        .AddItem getTranslationResource(GB_CLASS4) & vbTab & GB_CLASS4
'        .AddItem getTranslationResource(GB_ITEM) & vbTab & GB_ITEM
'        .AddItem getTranslationResource(GB_LCID) & vbTab & GB_LCID
'        .AddItem getTranslationResource(GB_LDIVISION) & vbTab & GB_LDIVISION
'        .AddItem getTranslationResource(GB_LREGION) & vbTab & GB_LREGION
'        .AddItem getTranslationResource(GB_LSTATUS) & vbTab & GB_LSTATUS
'        .AddItem getTranslationResource(GB_LSTATUS) & vbTab & GB_LUSERDEFINED
'        .AddItem getTranslationResource(GB_VELCODE) & vbTab & GB_VELCODE
'        .AddItem getTranslationResource(GB_VNID) & vbTab & GB_VNID
'    End With
'
'    EBGroupBy.Text = GB_LCID
'
'    EBGroupBy.Redraw = True      'Process is done. Repaint control.
'    Screen.MousePointer = vbNormal
'    Set EBGroupBy = Nothing
'
'Exit Sub
'ErrorHandler:
'    EBGroupBy.Redraw = True
'    Screen.MousePointer = vbNormal
'    Set EBGroupBy = Nothing
'    f_HandleErr Me.Caption & "(dcGroupBy_InitColumnProps)"
'End Sub
'
'
'Private Sub dgDetail_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RowCounter As Long
'    Dim RowIndex As Long, ColIndex As Long, PeriodIdx As Long
'
'    If m_IsGridDetailLoaded = False Then Exit Sub
'
'    'Check recordsets
'    If Not f_IsRecordsetOpenAndPopulated(m_rsGridDetail) Then Exit Sub
'
'    'Align the recordset
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            m_rsGridDetail.MoveLast
'        Else
'            m_rsGridDetail.MoveFirst
'        End If
'    Else
'        m_rsGridDetail.bookmark = StartLocation
'        If ReadPriorRows Then
'            m_rsGridDetail.MovePrevious
'        Else
'            m_rsGridDetail.MoveNext
'        End If
'    End If
'
'    'Fetch data into grid
'    For RowIndex = 0 To RowBuf.RowCount - 1
'        If m_rsGridDetail.BOF Or m_rsGridDetail.eof Then Exit For
'
'        Select Case RowBuf.ReadType
'            Case 0  'All data must be read
'                For ColIndex = 0 To m_rsGridDetail.Fields.Count - 1
'                    RowBuf.Value(RowIndex, ColIndex) = m_rsGridDetail.Fields(ColIndex).Value
'                Next ColIndex
'
'            Case 1  'Only bookmarks must be read
'                'That is done anyway, so leave it after end select
'            Case Else
'                'Cases 2 and 3 are not used by DBGrid... yet?
'        End Select
'        RowBuf.bookmark(RowIndex) = m_rsGridDetail.bookmark
'
'        If ReadPriorRows Then
'            m_rsGridDetail.MovePrevious
'        Else
'            m_rsGridDetail.MoveNext
'        End If
'
'        RowCounter = RowCounter + 1
'    Next RowIndex
'
'    RowBuf.RowCount = RowCounter
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgDetail_UnboundReadData)"
'End Sub
'
'Private Sub dgSummary_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim RowCounter As Long
'    Dim RowIndex As Long, ColIndex As Long, PeriodIdx As Long
'
'    If m_IsGridSummaryLoaded = False Then Exit Sub
'
'    'Check recordsets
'    If Not f_IsRecordsetOpenAndPopulated(m_rsGridSummary) Then Exit Sub
'
'    'Align the recordset
'    If IsNull(StartLocation) Then
'        If ReadPriorRows Then
'            m_rsGridSummary.MoveLast
'        Else
'            m_rsGridSummary.MoveFirst
'        End If
'    Else
'        m_rsGridSummary.bookmark = StartLocation
'        If ReadPriorRows Then
'            m_rsGridSummary.MovePrevious
'        Else
'            m_rsGridSummary.MoveNext
'        End If
'    End If
'
'    'Fetch data into grid
'    For RowIndex = 0 To RowBuf.RowCount - 1
'        If m_rsGridSummary.BOF Or m_rsGridSummary.eof Then Exit For
'
'        Select Case RowBuf.ReadType
'            Case 0  'All data must be read
'                For ColIndex = 0 To m_rsGridSummary.Fields.Count - 1
'                    'Some of these periods may have no data.
'                    RowBuf.Value(RowIndex, ColIndex) = m_rsGridSummary.Fields(ColIndex).Value
'                Next ColIndex
'
'            Case 1  'Only bookmarks must be read
'                'That is done anyway, so leave it after end select
'            Case Else
'                'Cases 2 and 3 are not used by DBGrid... yet?
'        End Select
'        RowBuf.bookmark(RowIndex) = m_rsGridSummary.bookmark
'
'        If ReadPriorRows Then
'            m_rsGridSummary.MovePrevious
'        Else
'            m_rsGridSummary.MoveNext
'        End If
'
'        RowCounter = RowCounter + 1
'    Next RowIndex
'
'    RowBuf.RowCount = RowCounter
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgSummary_UnboundReadData)"
'End Sub
'
'
'Private Sub tbAdjustments_ToolClick(Index As Integer, ByVal Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim MustRefresh As Boolean
'
'    MustRefresh = False
'
'    Select Case UCase$(Tool.ID)
'    Case "ID_ADJLOG"
'        Select Case Index
'        Case 0  'Summary
'            RtnCode = mShowAdjLog(TAB_SUMMARY)
'        Case 1  'Detail
'            RtnCode = mShowAdjLog(TAB_DETAIL)
'        End Select
'        MustRefresh = True
'
'    Case "ID_APPLY"
'        Select Case Index
'        Case 0  'Summary
'            RtnCode = mApplyMassAdjustments(TAB_SUMMARY)
'        Case 1  'Detail
'            RtnCode = mApplyMassAdjustments(TAB_DETAIL)
'        End Select
'        MustRefresh = True
'        Me.tbAdjustments(Index).Tools("ID_ADJLOG").Enabled = False  'We don't want them wandering off after applying and before saving/cancelling
'        Me.tbAdjustments(Index).Tools("ID_SAVE").Enabled = True
'        Me.tbAdjustments(Index).Tools("ID_CANCEL").Enabled = True
'
'    Case "ID_CANCEL", "ID_SAVE"
'        Select Case Index
'        Case 0  'Summary
'            If UCase$(Tool.ID) = "ID_SAVE" Then RtnCode = mSave_ForecastAdjustments(TAB_SUMMARY)
'        Case 1  'Detail
'            If UCase$(Tool.ID) = "ID_SAVE" Then RtnCode = mSave_ForecastAdjustments(TAB_SUMMARY)
'        End Select
'        MustRefresh = True
'        Me.tbAdjustments(Index).Tools("ID_ADJLOG").Enabled = True
'        Me.tbAdjustments(Index).Tools("ID_SAVE").Enabled = False
'        Me.tbAdjustments(Index).Tools("ID_CANCEL").Enabled = False
'
'    Case TB_ID_SETBOOKMARK
'        Select Case UCase$(atModification.SelectedTab.Key)
'        Case TAB_SUMMARY
'            If IsEmpty(m_SumBookMark) _
'            Or IsNull(m_SumBookMark) _
'            Then
'                m_SumBookMark = Me.dgSummary.bookmark
'                Me.tbAdjustments(0).Tools(TB_ID_GOTOBOOKMARK).Enabled = True
'            Else
'                m_SumBookMark = Null
'                Me.tbAdjustments(0).Tools(TB_ID_GOTOBOOKMARK).Enabled = False
'            End If
'        Case TAB_DETAIL
'            If IsEmpty(m_DtlBookMark) _
'            Or IsNull(m_DtlBookMark) _
'            Then
'                m_DtlBookMark = Me.dgDetail.bookmark
'                Me.tbAdjustments(1).Tools(TB_ID_GOTOBOOKMARK).Enabled = True
'            Else
'                m_DtlBookMark = Null
'                Me.tbAdjustments(1).Tools(TB_ID_GOTOBOOKMARK).Enabled = False
'            End If
'        End Select
'        MustRefresh = False
'
'    Case TB_ID_GOTOBOOKMARK
'        Select Case UCase$(atModification.SelectedTab.Key)
'        Case TAB_SUMMARY
'            If Not IsEmpty(m_SumBookMark) _
'            Or Not IsNull(m_SumBookMark) _
'            Then
'                Me.dgSummary.bookmark = m_SumBookMark
'            End If
'        Case TAB_DETAIL
'            If Not IsEmpty(m_DtlBookMark) _
'            Or Not IsNull(m_DtlBookMark) _
'            Then
'                Me.dgDetail.bookmark = m_DtlBookMark
'            End If
'        End Select
'        MustRefresh = False
'
'    End Select
'
'    If MustRefresh = True Then
'        Select Case UCase$(atModification.SelectedTab.Key)
'        Case TAB_SUMMARY
'            RtnCode = mGridSummary_Fetch
'        Case TAB_DETAIL
'            RtnCode = mGridDetail_Fetch
'        End Select
'        If RtnCode > 0 Then
'            RtnCode = mChart_Refresh(UCase$(atModification.SelectedTab.Key))
'        End If
'    End If
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(tbAdjustments_ToolClick)"
'End Sub
'
'Private Sub tbFcstModification_ToolClick( _
'    ByVal Tool As ActiveToolBars.SSTool _
')
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Long
'    Dim MustRefresh As Boolean
'    Screen.MousePointer = vbHourglass
'    Dim CheckTime As Date
'
'    'Clear messages
'    Write_Message ""
'
'    Me.tbFcstModification.Redraw = False
'    Screen.MousePointer = vbHourglass
'
'    MustRefresh = False
'
'    Select Case UCase$(Tool.ID)
'    Case TB_ID_LOAD
'        'Check the refresh case at the end of this select
'        MustRefresh = True
'
'    Case TB_ID_SAVEFORECAST
'        RtnCode = mSaveForecast(False)
'
'    Case TB_ID_RUNFORECAST
'        CheckTime = Now()
'        RtnCode = mForecast_Run(0)
'        Debug.Print "Total time for forecast run: " & DateDiff("s", CheckTime, Now())
'        'Check the refresh case at the end of this select
'        Select Case RtnCode
'        Case 1  'Success
'            MustRefresh = True
'        Case -1 'No Data Found
'            'Msg here
'        Case -2 'SQL Error
'            'Msg here
'        Case -3 'Invalid parameter
'            'Msg here
'        End Select
'
'    Case TB_ID_ITEMFILTER
'        ShowItemFilter
'        're-fetch data using criteria
'        'Check the refresh case at the end of this select
'        MustRefresh = True
'
'    Case TB_ID_CLEARFILTER
'        're-fetch from setup onward
'        RtnCode = mFcstSetup_Fetch(SQL_GetEq, True)
'        're-fetch data using criteria
'        'Check the refresh case at the end of this select
'        MustRefresh = True
'
'    Case TB_ID_FORECASTSETUP
'        AIM_ForecastSetup.Show vbModeless
'
'    Case TB_ID_COPY
'        'TO DO
'        'Call RtnCode = mSaveForecast(True, FcstComment.TXT)
'
'    Case TB_ID_CLOSE
'        Unload Me
'        Screen.MousePointer = vbNormal
'        Exit Sub
'
'    Case TB_ID_RECALCCURRENT
'        CheckTime = Now()
'        RtnCode = mForecast_Run(1)
'        Debug.Print "Total time for forecast run: " & DateDiff("s", CheckTime, Now())
'        'Check the refresh case at the end of this select
'        Select Case RtnCode
'        Case 1  'Success
'            MustRefresh = True
'        Case -1 'No Data Found
'            'Msg here
'        Case -2 'SQL Error
'            'Msg here
'        Case -3 'Invalid parameter
'            'Msg here
'        End Select
'
'    Case TB_ID_CHART_TOGGLESIZE
'        mChart_ToggleSize
'
'    Case TB_ID_CHART_SYNC
'        RtnCode = mChart_Refresh(UCase$(atModification.SelectedTab.Key))
'
'    Case TB_ID_PROMOTE
'        RtnCode = mPromoteToMaster
'        'Check the refresh case at the end of this select
'        MustRefresh = True
'
'    End Select
'
'    'Refresh screen for the actions that require it.
'    If MustRefresh Then
'        Select Case UCase$(atModification.SelectedTab.Key)
'        Case TAB_SUMMARY
'            RtnCode = mGridSummary_Fetch
'        Case TAB_DETAIL
'            RtnCode = mGridDetail_Fetch
'        End Select
'        If RtnCode > 0 Then
'            RtnCode = mChart_Refresh(UCase$(atModification.SelectedTab.Key))
'        End If
'    End If
'
'    Me.tbFcstModification.Redraw = True
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    Me.tbFcstModification.Redraw = True
'    Screen.MousePointer = vbNormal
'    f_HandleErr Me.Caption & "(atbForecast_ToolClick)"
'End Sub
'
'
'Private Sub txtFcstPeriods_Future_Change()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'
'    If Not IsNull(Me.txtFcstPeriods_Future.Value) And Not IsNull(Me.txtFcstPeriods_Historical.Value) Then
'        'Update form variables to reflect date ranges
'        mSetPeriodBounds
'    End If
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtFcstPeriods_Future_Change)"
'End Sub
'
'Private Sub txtFcstPeriods_Historical_Change()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'
'    If Not IsNull(Me.txtFcstPeriods_Future.Value) And Not IsNull(Me.txtFcstPeriods_Historical.Value) Then
'        'Update form variables to reflect date ranges
'        mSetPeriodBounds
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtFcstPeriods_Historical_Change)"
'End Sub
'
'Private Sub Form_Activate()
'On Error GoTo ErrorHandler
'
'    'Check for an open connection
'    If Cn.State <> adStateOpen Then
'        Unload Me
'        Exit Sub
'    End If
'
'    'Set State of window list State Button
'    gbUpdating = True
'    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
'    gbUpdating = False
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Activate)"
'End Sub
'
'Private Sub Form_Load()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim strSQL As String
'    Dim strMessage As String
'    Dim strMessage1 As String
'    Dim IndexCounter As Long
'
'    'Housekeeping
'    Screen.MousePointer = vbHourglass
'    m_IsInitialLoad = True
'
'    strMessage = getTranslationResource("STATMSG06900")
'    If StrComp(strMessage, "STATMSG06900") = 0 Then strMessage = "Initializing Forecast Planner..."""
'    Write_Message strMessage
'
'    GetTranslatedCaptions Me
'
'    Me.tbFcstModification.Redraw = False    'Disable the control from repainting until the process has finished.
'    Screen.MousePointer = vbNormal
''************************************************************
'' -- la here remove after coding for this button.
'Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons).Enabled = False
'Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Enabled = False
''************************************************************
'
'    m_FcstSetupKey = -1
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
'    If RtnCode <> SUCCEED Then Exit Sub
'
'    'Populate the dropdown(s)
'    If mFcstID_Load = False Then
'        'Disable options.
'        Me.tbFcstModification.Tools(TB_ID_LOAD).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_RUNFORECAST).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_RECALCCURRENT).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_COPY).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_CHART_SYNC).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_ITEMFILTER).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_CLEARFILTER).Enabled = False
'        Me.tbFcstModification.Tools(TB_ID_PROMOTE).Enabled = False
'    Else
'        'mFcstID_Load should have triggered fetching forecast data for the first record.
'    End If
'
'    'Make the spin button(s) visible
'    txtFcstPeriods_Future.Spin.Visible = dbiShowAlways
'    txtFcstPeriods_Historical.Spin.Visible = dbiShowAlways
'    txtAdjQty_Smr.Spin.Visible = dbiShowAlways
'    txtAdjQty_Dtl.Spin.Visible = dbiShowAlways
'
'    'Initialize adjustment fields
'    txtAdjQty_Dtl.Value = 0
'    txtAdjQty_Smr.Value = 0
'    optAdjType_Dtl(ADJ_UNITS).Value = True
'    optAdjType_Sum(ADJ_PERCENT).Value = True
'    optAdjType_Sum(ADJ_UNITS).Enabled = False
'    optAdjType_Sum(ADJ_UNITS).Visible = False
'    optAdjType_Sum(ADJ_OVERRIDE).Enabled = False
'    optAdjType_Sum(ADJ_OVERRIDE).Visible = False
'    dcAdjStart_Dtl.Text = Format(Date, gDateFormat)
'    dcAdjEnd_Dtl.Text = Format(Date, gDateFormat)
'    dcAdjStart_Smr.Text = Format(Date, gDateFormat)
'    dcAdjEnd_Smr.Text = Format(Date, gDateFormat)
'
'    mChart_Initialize
'
'    'Size the form
'    mForm_Rearrange
'    m_IsInitialLoad = False
'
'    'Add to Windows List
'    AddToWindowList Me.Caption
'
'    'Windup
'    Write_Message ""
'    Screen.MousePointer = vbNormal
'
'    Me.tbFcstModification.Redraw = True     'Process is done. Repaint control
'    Screen.MousePointer = vbNormal
'Exit Sub
'ErrorHandler:
'    Me.tbFcstModification.Redraw = True
'    Screen.MousePointer = vbNormal
'    f_HandleErr Me.Caption & "(Form_Load)"
'End Sub
'
'Private Sub Form_Resize()
'On Error GoTo ErrorHandler
'
'    If m_IsInitialLoad = False Then
'        mForm_Rearrange
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Resize)"
'End Sub
'
'Private Sub Form_Unload(Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'
'    Write_Message ""
'
'    'Clear any existing columns
'    Me.dgSummary.Columns.RemoveAll
'    Me.dgDetail.Columns.RemoveAll
'
'    If f_IsRecordsetValidAndOpen(m_rsGridSummary) Then m_rsGridSummary.Close
'    Set m_rsGridSummary = Nothing
'
'    If f_IsRecordsetValidAndOpen(m_rsGridDetail) Then m_rsGridDetail.Close
'    Set m_rsGridDetail = Nothing
'
'    If f_IsRecordsetValidAndOpen(m_rsUserElements) Then m_rsUserElements.Close
'    Set m_rsUserElements = Nothing
'
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, True, , , Me.Caption)
'    Set Cn = Nothing
'
'    'Remove form from window list
'    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        'Ignore
'    Else
'        f_HandleErr Me.Caption & "(Form_Unload)"
'    End If
'    Resume Next
'End Sub
'
'Private Function mForm_Rearrange()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'    Dim setHeight As Long
'    Dim setWidth As Long
'    Dim setTop As Long
'    Dim setLeft As Long
'    Dim tempUnit As Long
'
'    If m_IsInitialLoad Then
'        Me.Top = 0
'        Me.Left = 0
'        Me.Width = AIM_Main.ScaleWidth
'        Me.Height = AIM_Main.ScaleHeight
'
'        Me.fraCommon.Height = 5000
'        Me.fraCommon.Top = 60
'        Me.fraCommon.Left = 60
'        Me.chtForecast.Height = 4000
'        Me.chtForecast.Top = 960
'        Me.chtForecast.Left = 120
'    End If
'
'    If Me.WindowState <> vbMinimized _
'    And AIM_Main.WindowState <> vbMinimized _
'    Then
'        'Set the common set's stats
'        setWidth = Me.Width - 400
'        'check for errors and correct to defaults
'        setWidth = IIf(setWidth > 0, setWidth, 14925)
'        Me.fraCommon.Width = setWidth
'        Me.chtForecast.Width = IIf(fraCommon.Width >= 400, fraCommon.Width - 300, 14655)
'
'        'SSTabs
'        'Use the left and width of the common frame above
'        atModification.Left = Me.fraCommon.Left
'        atModification.Width = Me.fraCommon.Width
'        'Set relative top
'        setTop = (Me.fraCommon.Top + Me.fraCommon.Height + 60)
'        atModification.Top = IIf(setTop >= 5192, 5192, setTop)
'        'Set relative height
'        setHeight = Me.Height - (Me.fraCommon.Height + 1200)
'        If atModification.Top < 5000 _
'        Then
'            atModification.Height = IIf(setHeight > 0, setHeight, 4026)
'        ElseIf setHeight <= 4126 _
'        Then
'            atModification.Height = 4126
'        Else
'            atModification.Height = 4126
'        End If
'
'        'Frames within the tab
'        setLeft = 120
'        'set relative width
'        setWidth = Me.atModification.ClientWidth - 200
'        setTop = 0
'        'set relative height
'        setHeight = Me.atModification.ClientHeight - 50
'        'check for errors and correct to defaults
'        setWidth = IIf(setWidth > 0, setWidth, 14175)
'        setHeight = IIf(setHeight > 0, setHeight, 3615)
'
'        'Now, position the frames and grids accordingly
'        Me.fraAdj_Smr.Top = 0
'        Me.fraAdj_Dtl.Top = 0
'        Me.fraAdj_Smr.Left = 120
'        Me.fraAdj_Dtl.Left = 120
'
'        Me.FraSummary.Top = (Me.fraAdj_Smr.Top + Me.fraAdj_Smr.Height + 60)
'        Me.FraSummary.Left = setLeft
'        Me.FraSummary.Width = setWidth
'        'Set relative height
'        tempUnit = (Me.atModification.ClientHeight - 50) - (Me.fraAdj_Dtl.Height + 300)
'        Me.FraSummary.Height = IIf(tempUnit > 0, tempUnit, 2010)
'
'        Me.FraItems.Top = Me.FraSummary.Top
'        Me.FraItems.Left = Me.FraSummary.Left
'        Me.FraItems.Height = Me.FraSummary.Height
'        Me.FraItems.Width = Me.FraSummary.Width
'
'        Me.dgSummary.Top = 200
'        Me.dgSummary.Left = 120
'        tempUnit = FraSummary.Height - (400)
'        Me.dgSummary.Height = IIf(tempUnit > 0, tempUnit, 2730)
'        tempUnit = (FraSummary.Width - 300)
'        Me.dgSummary.Width = IIf(tempUnit > 0, tempUnit, 13815)
'
'        Me.dgDetail.Top = Me.dgSummary.Top
'        Me.dgDetail.Left = Me.dgSummary.Left
'        Me.dgDetail.Width = Me.dgSummary.Width
'        Me.dgDetail.Height = Me.dgSummary.Height
'
'        'Size the filters
'        fraForecastFilters.Top = setTop
'        fraForecastFilters.Left = setLeft
'        fraForecastFilters.Height = setHeight
'        fraForecastFilters.Width = setWidth
'
'        'Size the metrics
'        fraMetrics.Top = setTop
'        fraMetrics.Left = setLeft
'        fraMetrics.Height = setHeight
'        fraMetrics.Width = setWidth
'
'        'Size the options
'        fraOptions.Top = setTop
'        fraOptions.Left = setLeft
'        fraOptions.Height = setHeight
'        fraOptions.Width = setWidth
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mForm_Rearrange)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Gets data from the AIMForecast table to populate the ForecastID dropdown
'' .......................................................................
''   Returns false if there are no records to fetch,
''   else true after setting the display to the first record.
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mFcstID_Load() As Boolean
'On Error GoTo ErrorHandler
'
'    Dim rsForecastList As ADODB.Recordset
'    Dim RtnCode As Boolean
'    Dim RowCounter As Long, ColCounter As Long
'    Dim AddItemString As String
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    Dim EBFcstId As SSOleDBCombo    'Early-binding is faster than late-binding a COM control
'
'    Set EBFcstId = Me.dcForecastID
'
'    EBFcstId.Redraw = False
'    Screen.MousePointer = vbHourglass
'
'    'Default to false until the process returns it's result.
'    mFcstID_Load = False
'
'    'Get Forecast IDs and Descriptions from AIMForecast
'    RtnCode = gList_FcstIDs(Cn, rsForecastList, Me.Caption)
'    If RtnCode = True Then
'        EBFcstId.RemoveAll
'        EBFcstId.Reset
'
'        m_FcstId = IIf(Trim$(m_FcstId) = "", rsForecastList(0).Value, m_FcstId)
'        For RowCounter = 0 To rsForecastList.RecordCount - 1
'            For ColCounter = 0 To rsForecastList.Fields.Count - 1
'                AddItemString = AddItemString & rsForecastList.Fields(ColCounter).Value & vbTab
'            Next
'            EBFcstId.AddItem AddItemString
'            AddItemString = ""
'            rsForecastList.MoveNext
'        Next
'
'        'Get the forecast data to display in the screen's controls
'        mFcstSetup_Fetch SQL_GetEq, True
'
'        mFcstID_Load = True
'    End If
'
'CleanUp:
'    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
'    Set rsForecastList = Nothing
'    EBFcstId.Redraw = True
'    Set EBFcstId = Nothing
'    Screen.MousePointer = vbNormal
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
'    Set rsForecastList = Nothing
'    EBFcstId.Redraw = True
'    Set EBFcstId = Nothing
'    Screen.MousePointer = vbNormal
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstID_Load)"
'End Function
'
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Gets data from the AIMForecastSetup table for the modular forecast ID.
'' .......................................................................
''   Parameters:
''       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
''       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mFcstSetup_Fetch( _
'    p_Action As SQL_ACTIONS, _
'    p_Related As Boolean _
'    ) As Boolean
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim intCount As Long
'    Dim rsCompound As ADODB.Recordset
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    'Default to false until process returns it's result
'    mFcstSetup_Fetch = False
'
'    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
'    RtnCode = gFetch_ForecastSetup(Cn, m_FcstId, gUserID, p_Action, p_Related, rsCompound)
'
'    'Check return code -- 0 = fail; 1 = succeed
'    If RtnCode = 0 Then
'        'FAIL
'        Exit Function
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsCompound) Then
'        ' Display results from each SELECT statement
'        dgUserElements.Redraw = False
'        Screen.MousePointer = vbHourglass
'
'        intCount = 1
'        Do Until rsCompound Is Nothing
'            Do Until rsCompound.eof
'                'Set display to match recordset(s)
'                Select Case intCount
'                Case 1  'AIMForecastSetup
'                    'Get recordset values
'                    m_FcstId = rsCompound!FcstId
'                    m_FcstSetupKey = rsCompound!FcstSetupKey
'
'                    RtnCode = mFcstSetup_RefreshUI(rsCompound!FcstId, rsCompound!FcstDesc, _
'                        rsCompound!FcstLocked, rsCompound!FcstEnabled, _
'                        rsCompound!FcstHierarchy, rsCompound!FcstStartDate, rsCompound!LastUpdated, _
'                        rsCompound!FcstRolling, rsCompound!FcstPds_Future, _
'                        rsCompound!FcstHistory, rsCompound!FcstPds_Historical, _
'                        rsCompound!ApplyTrend, rsCompound!Calc_SysRqmt, _
'                        rsCompound!Calc_NetRqmt, rsCompound!Calc_PrjInvt, _
'                        rsCompound!Calc_HstDmnd, rsCompound!Calc_NetRqmtAndPlnRcpt, rsCompound!Calc_NetRqmtAndPrdCons, _
'                        rsCompound!Calc_PrjInvtAndPrdCons, rsCompound!FcstInterval, _
'                        rsCompound!FcstUnit, rsCompound!Item, rsCompound!ItStat, _
'                        rsCompound!VnId, rsCompound!Assort, rsCompound!ById, _
'                        rsCompound!Class1, rsCompound!Class2, rsCompound!Class3, rsCompound!Class4, _
'                        rsCompound!LcId, rsCompound!LStatus, rsCompound!LDivision, rsCompound!LRegion, rsCompound!LUserDefined)
'                Case 2  'ForecastAccess
''                    RtnCode = RefreshForecastAccess(rsCompound)
'
'                Case 3  'UserElements
''                    RtnCode = RefreshUserElements(rsCompound!FcstRepositoryID, rsCompound!RecordDesc)
'
'                End Select
'                rsCompound.MoveNext 'There should be no more for the first recordset
'            Loop
'
'            If p_Related = True Then
'                Set rsCompound = rsCompound.NextRecordset
'                intCount = intCount + 1
'            Else
'                Exit Do
'            End If
'        Loop
'
'        dgUserElements.Redraw = True
'        Screen.MousePointer = vbNormal
'
'    End If
'
'    mFcstSetup_Fetch = True
'
'CleanUp:
'    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
'    Set rsCompound = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    dgUserElements.Redraw = True
'    Screen.MousePointer = vbNormal
'    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
'    Set rsCompound = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstSetup_Fetch)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Gets data from the recordset and sets values for screen controls.
''   The recordset is to be discarded by the calling function.
'' .......................................................................
''   Parameters:
''       AIMForecastSetup.*
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mFcstSetup_RefreshUI( _
'    p_FcstId As String, p_FcstDesc As String, _
'    p_FcstLocked As Boolean, p_FcstEnabled As Boolean, _
'    p_FcstHierarchy As Long, p_FcstStartDate As Date, p_LastUpdated As Date, _
'    p_FcstRolling As Boolean, p_FcstPeriods_Future As Long, _
'    p_FcstHistory As Boolean, p_FcstPeriods_Historical As Long, _
'    p_ApplyTrend As Long, p_Calc_SysRqmt As Boolean, _
'    p_Calc_NetRqmt As Boolean, p_Calc_PrjInvt As Boolean, _
'    p_Calc_HstDmnd As Boolean, p_Calc_NetRqmtAndPlnRcpt As Boolean, p_Calc_NetRqmtAndPrdCons As Boolean, _
'    p_Calc_PrjInvtAndPrdCons As Boolean, p_FcstInterval As Long, _
'    p_FcstUnit As Long, p_Item As String, p_ItStat As String, _
'    p_VnId As String, p_Assort As String, _
'    p_ById As String, p_Class1 As String, _
'    p_Class2 As String, p_Class3 As String, _
'    p_Class4 As String, p_LcID As String, _
'    p_LStatus As String, p_LDivision As String, _
'    p_LRegion As String, p_LUserDefined As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim IndexVar As Long, IndexCounter As Long
'    Dim RtnCode As Long
'
'    Me.dcForecastID.Text = p_FcstId
'    m_FcstLocked = IIf(p_FcstLocked = False, vbUnchecked, vbChecked)
'    m_FcstEnabled = IIf(p_FcstEnabled = False, vbUnchecked, vbChecked)
'
'    m_FcstHierarchy = p_FcstHierarchy
'
'    m_FcstStartDate = p_FcstStartDate
'    Me.txtFcstStartDate.Text = Format(p_LastUpdated, gDateFormat)  'The Forecast Planner needs to show what the last updated was.
'
'    m_ApplyTrend = p_ApplyTrend
'
'    IndexVar = p_FcstInterval
'    For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
'        Me.optFcstInterval(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
'    Next
'    m_FcstInterval = p_FcstInterval
'    m_FcstRolling = p_FcstRolling
'    Me.txtFcstPeriods_Future.MinValue = 0
'    Me.txtFcstPeriods_Future.MaxValue = p_FcstPeriods_Future
'    Me.txtFcstPeriods_Future.Text = Format(p_FcstPeriods_Future, "##0")
'
'    m_FcstHistory = p_FcstHistory
'    Me.txtFcstPeriods_Historical.MinValue = 0
'    Me.txtFcstPeriods_Historical.MaxValue = p_FcstPeriods_Historical
'    Me.txtFcstPeriods_Historical.Text = Format(p_FcstPeriods_Historical, "##0")
'
'    IndexVar = p_FcstUnit
'    For IndexCounter = optFcstUnit.LBound To optFcstUnit.UBound
'        Me.optFcstUnit(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
'    Next
'    m_FcstUnit = p_FcstUnit
'
'    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = IIf(p_Calc_SysRqmt = False, vbUnchecked, vbChecked)
'    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt).Value = IIf(p_Calc_NetRqmt = False, vbUnchecked, vbChecked)
'    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt).Value = IIf(p_Calc_PrjInvt = False, vbUnchecked, vbChecked)
'    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_HstDmnd).Value = IIf(p_Calc_HstDmnd = False, vbUnchecked, vbChecked)
'    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Value = IIf(p_Calc_NetRqmtAndPlnRcpt = False, vbUnchecked, vbChecked)
''    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Value = IIf(p_Calc_NetRqmtAndPrdCons = False, vbUnchecked, vbChecked)
''    Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons).Value = IIf(p_Calc_PrjInvtAndPrdCons = False, vbUnchecked, vbChecked)
'
'    Me.txtItem.Text = p_Item
'    Me.txtItemStatus.Text = p_ItStat
'    Me.txtVnId.Text = p_VnId
'    Me.txtAssort.Text = p_Assort
'    Me.txtById.Text = p_ById
'    Me.txtClass(1).Text = p_Class1
'    Me.txtClass(2).Text = p_Class2
'    Me.txtClass(3).Text = p_Class3
'    Me.txtClass(4).Text = p_Class4
'    Me.txtLcId.Text = p_LcID
'    Me.txtLStatus.Text = p_LStatus
'    Me.txtLDivision.Text = p_LDivision
'    Me.txtLRegion.Text = p_LRegion
'    Me.txtLUserDefined.Text = p_LUserDefined
'
'    'Set main filters to those retrieved from th table
'    With m_FcstItemFilter
'        'Set parameters to existing fields
'        .Item = Me.txtItem.Text
'        .ItemStatus = Me.txtItemStatus.Text
'        .VnId = Me.txtVnId.Text
'        .Assort = Me.txtAssort.Text
'        .LcId = Me.txtLcId.Text
'        .ById = Me.txtById.Text
'        .Class1 = Me.txtClass(1).Text
'        .Class2 = Me.txtClass(2).Text
'        .Class3 = Me.txtClass(3).Text
'        .Class4 = Me.txtClass(4).Text
'        .LDivision = Me.txtLDivision.Text
'        .LRegion = Me.txtLRegion.Text
'        .LStatus = Me.txtLStatus.Text
'        .LUserDefined = Me.txtLUserDefined.Text
'
'        'Clear any additional subfilters
'        .SubFilterItem = Me.txtItem.Text
'        .SubFilterItemStatus = Me.txtItemStatus.Text
'        .SubFilterVnId = Me.txtVnId.Text
'        .SubFilterAssort = Me.txtAssort.Text
'        .SubFilterLcID = Me.txtLcId.Text
'        .SubFilterById = Me.txtById.Text
'        .SubFilterClass1 = Me.txtClass(1).Text
'        .SubFilterClass2 = Me.txtClass(2).Text
'        .SubFilterClass3 = Me.txtClass(3).Text
'        .SubFilterClass4 = Me.txtClass(4).Text
'        .SubFilterLDivision = Me.txtLDivision.Text
'        .SubFilterLRegion = Me.txtLRegion.Text
'        .SubFilterLStatus = Me.txtLStatus.Text
'        .SubFilterLUserDefined = Me.txtLUserDefined.Text
'    End With
'
'    'Update form variables to reflect date ranges
'    mSetPeriodBounds
'
'    'Clear out the grids, if they have any data
'    Me.dgSummary.Columns.RemoveAll
'    Me.dgSummary.Reset
'    Me.dgSummary.Refresh
'
'    Me.dgDetail.Columns.RemoveAll
'    Me.dgDetail.Reset
'    Me.dgDetail.Refresh
'
'    'Enable toolbar based on security access control
'    mToggleToolbarAccess
'
'    'Return success
'    mFcstSetup_RefreshUI = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mFcstSetup_RefreshUI)"
'End Function
'
'Private Sub mSetPeriodBounds()
'On Error GoTo ErrorHandler
'
'    Dim PdsFuture As Long, PdsHistorical As Long
'
'    'Calc Historical Start, Forecast Start and Forecast End, based on the number of periods - future and historical
'    PdsFuture = IIf(IsNull(Me.txtFcstPeriods_Future.Value), 0, Me.txtFcstPeriods_Future.Value)
'    PdsHistorical = IIf(IsNull(Me.txtFcstPeriods_Historical.Value), 0, Me.txtFcstPeriods_Historical.Value)
'    m_TotalPeriods = PdsFuture + PdsHistorical
'
'    Select Case m_FcstInterval
'    Case int_Days
'        m_DateIncrement = "d"
'    Case int_Weeks, int_544, int_454, int_445, int_4Wks
'        m_DateIncrement = "ww"
'    Case int_Months
'        m_DateIncrement = "m"
'    Case int_Quarters
'        m_DateIncrement = "q"
'    End Select
'    m_FirstPeriod = Me.txtFcstStartDate.Text
'    m_LastPeriod = DateAdd(Trim$(m_DateIncrement), CDbl(PdsFuture), m_FirstPeriod)
'    m_LastPeriod = DateAdd("d", -1, m_LastPeriod)
'    m_FirstHistoricalPeriod = DateAdd(Trim$(m_DateIncrement), -CDbl(PdsHistorical), Me.txtFcstStartDate.Text)
'
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mSetPeriodBounds)"
'End Sub
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Kicks off the forecast generation process for all the records in the given FcstID/FcstSetupKey
'' .......................................................................
''   Returns false if the process failed
''   else true after saving data to the AIMFcstStore* tables
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mForecast_Run( _
'    p_CalcScope As Long _
') As Long
'
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim ForecastCriteria As FORECAST_CRITERIA
'    Dim ForecastData As FORECAST_DATA
'    Dim IndexVar As Long
'    Dim IndexCounter As Long
'    Dim tempInt As Long
'    Dim BkMark As Variant
'    Dim RowCount As Long, RowIdx As Long
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'
'    'Set flag to busy
'    m_IsRunning = True
'
'    RtnCode = mSetForecastCriteria(ForecastCriteria)
'
'    Select Case p_CalcScope
'    Case 0  'All
'        'Do nothing, call the generation function with current set of parameters
'        '''    'For adjustments, start off with defaults, then fill in from current adjustment source. 'TO DO 'la here
'        RtnCode = gGen_ForecastData(Cn, dcForecastID.Text, True, ForecastCriteria, ForecastData)
'    Case 1  'Selected
'        'For each selected record, get current item/location information and generate with that filter
'        If UCase$(Me.atModification.SelectedTab.Key) = TAB_SUMMARY Then
'            With Me.dgSummary
'                If .Rows <= 0 Then
'                    '''    'For adjustments, start off with defaults, then fill in from current adjustment source. 'TO DO 'la here
'                    RtnCode = gGen_ForecastData(Cn, dcForecastID.Text, True, ForecastCriteria, ForecastData)
'                Else
'                    RowCount = .SelBookmarks.Count
'                    Do Until RowIdx > RowCount
'                        If RowCount = 0 Then
'                            BkMark = .bookmark
'                        Else
'                            BkMark = .SelBookmarks(RowIdx)
'                        End If
'                        Select Case Me.dcGroupBy.Text
'                        Case getTranslationResource(GB_ASSORT)
'                            ForecastCriteria.Assort = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_BYID)
'                            ForecastCriteria.ById = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_CLASS1)
'                            ForecastCriteria.Class1 = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_CLASS2)
'                            ForecastCriteria.Class2 = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_CLASS3)
'                            ForecastCriteria.Class3 = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_CLASS4)
'                            ForecastCriteria.Class4 = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_ITEM)
'                            ForecastCriteria.Item = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_LCID)
'                            ForecastCriteria.LcId = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_LDIVISION)
'                            ForecastCriteria.LDivision = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_LREGION)
'                            ForecastCriteria.LRegion = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_LSTATUS)
'                            ForecastCriteria.LStatus = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_LUSERDEFINED)
'                            ForecastCriteria.LUserDefined = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        Case getTranslationResource(GB_VELCODE)
'                            'Not available. -- Message here
'                        Case getTranslationResource(GB_VNID)
'                            ForecastCriteria.VnId = .Columns(m_SmrCol_GroupBy).CellText(BkMark)
'                        End Select
'                        '''    'For adjustments, start off with defaults, then fill in from current adjustment source. 'TO DO 'la here
'                        RtnCode = gGen_ForecastData(Cn, dcForecastID.Text, True, ForecastCriteria, ForecastData)
'                        RowIdx = RowIdx + 1
'                    Loop
'                    Exit Function
'                End If
'            End With
'        ElseIf UCase$(Me.atModification.SelectedTab.Key) = TAB_DETAIL Then
'            With Me.dgDetail
'                If .Rows <= 0 Then
'                    '''    'For adjustments, start off with defaults, then fill in from current adjustment source. 'TO DO 'la here
'                    RtnCode = gGen_ForecastData(Cn, dcForecastID.Text, True, ForecastCriteria, ForecastData)
'                Else
'                    RowCount = .SelBookmarks.Count
'                    Do Until RowIdx > RowCount
'                        If RowCount = 0 Then
'                            BkMark = .bookmark
'                        Else
'                            BkMark = .SelBookmarks(RowIdx)
'                        End If
'                        ForecastCriteria.Item = .Columns(m_DtlCol_Item).CellValue(BkMark)
'                        ForecastCriteria.LcId = .Columns(m_DtlCol_LcID).CellText(BkMark)
'                        '''    'For adjustments, start off with defaults, then fill in from current adjustment source. 'TO DO 'la here
'                        RtnCode = gGen_ForecastData(Cn, dcForecastID.Text, True, ForecastCriteria, ForecastData)
'                    Loop
'                End If
'            End With
'            Exit Function
'        End If
'    End Select
'
'    mForecast_Run = RtnCode
'
'    'Set flag to done
'    m_IsRunning = False
'
'Exit Function
'ErrorHandler:
'    'Set flag to done
'    m_IsRunning = False
'    Err.Raise Err.Number, Err.source, Err.Description & "(mForecast_Run)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Fetches data from the AIMFcstStore* tables for the given FcstID/FcstSetupKey
''   , based on the "Group By" selection
'' .......................................................................
''   Returns false if the process failed
''   else true after redrawing the summary grid
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mGridSummary_Fetch( _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim QueryForSummary As ADODB.Command, rsSummary As ADODB.Recordset
'
'    Dim RowCount As Long, ElementCount As Long
'    Dim RowIdx As Long, ColIdx As Long, AdjIdx As Long, PeriodIdx As Long
'    Dim QueryText As String
'
'    Dim StatusPercent As Long, strMessage As String
'    Dim HasAccess As Boolean
'    Dim PeriodStartDate As Date
'    Dim IsKeyRowSet As Boolean
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    Dim CheckTime As Date
'
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    If IsNull(m_FcstSetupKey) Or m_FcstSetupKey = -1 Then Exit Function
'
'    'Set flag to busy
'    m_IsGridSummaryLoaded = False
'    Screen.MousePointer = vbHourglass
'
'    'BuildQueryText
'    QueryText = mGridQueryBuilder(TAB_SUMMARY)
'
'    'Init. the command object
'    Set QueryForSummary = New ADODB.Command
'    With QueryForSummary
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdText
'        .CommandText = QueryText
'    End With
'
'    'Init. the recordset
'    Set rsSummary = New ADODB.Recordset
'    With rsSummary
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in the data
'    CheckTime = Now()
'    rsSummary.Open QueryForSummary
'    Debug.Print "Total time for retrieving query: " & DateDiff("s", CheckTime, Now())
'
'    If f_IsRecordsetOpenAndPopulated(rsSummary) Then
'        'Set the main forecaststorekey
'        m_FcstStoreKey_Main = rsSummary!FcstStoreKey
'        RowCount = rsSummary.RecordCount
'        'Associate as a disconnected recordset
'        If f_IsRecordsetValidAndOpen(m_rsGridSummary) Then m_rsGridSummary.Close
'        Set m_rsGridSummary = Nothing
'        Set m_rsGridSummary = New ADODB.Recordset
'        m_rsGridSummary.Fields.Append m_GroupByFieldName, adVarWChar, 255         'la here
'        m_rsGridSummary.Fields.Append "DataCalc_Type", adTinyInt
'        m_rsGridSummary.Fields.Append "DataCalcDesc", adVarWChar, 255
'        m_rsGridSummary.Fields.Append "Totals", adDecimal, 10.2
'        m_rsGridSummary.Fields("Totals").Precision = 10
'        m_rsGridSummary.Fields("Totals").NumericScale = 2
'        For PeriodIdx = 1 To m_TotalPeriods
'            m_rsGridSummary.Fields.Append "Period" & CStr(PeriodIdx), adDecimal
'            m_rsGridSummary.Fields("Period" & CStr(PeriodIdx)).Precision = 10
'            m_rsGridSummary.Fields("Period" & CStr(PeriodIdx)).NumericScale = 2
'        Next
''        m_rsGridSummary.Fields.Append "ElementRowIndex", adInteger       'Used for tracking the "element" type
''        m_rsGridSummary.Fields.Append "KeyRow", adBoolean                'Used for adjustments and grouping
'
'        'Populate
'        rsSummary.MoveFirst
'        RowIdx = 0
'        'ElementCount = DATA_CALC_TYPE.DCT_MAXVALUE - 1
'        m_rsGridSummary.Open
'        rsSummary.MoveFirst
'        Do Until rsSummary.eof
'            'Set display to match recordset(s)
'            With m_rsGridSummary
'                .AddNew
'                For ColIdx = 0 To m_rsGridSummary.Fields.Count - 1
'                    .Fields(ColIdx) = rsSummary.Fields(ColIdx + 1)
'                Next
'            End With
'            rsSummary.MoveNext
'        Loop
'        m_rsGridSummary.MoveFirst
'
'        'Display
'        RtnCode = mGridSummary_Set()
'
'        'Reset adjustment fields
'        Me.txtAdjQty_Smr.Value = 0
'        Me.dcAdjStart_Smr.MinDate = m_FirstPeriod
'        Me.dcAdjStart_Smr.MaxDate = m_LastPeriod
'        Me.dcAdjStart_Smr.dateValue = Format(m_FirstPeriod, g_ISO_DATE_FORMAT)
'        Me.dcAdjEnd_Smr.MinDate = m_FirstPeriod
'        Me.dcAdjEnd_Smr.MaxDate = m_LastPeriod
'        Me.dcAdjEnd_Smr.dateValue = Format(m_LastPeriod, g_ISO_DATE_FORMAT)
'
'        Me.txtAdjDesc_Smr = ""
'
'    Else
'        strMessage = getTranslationResource("TEXTMSG05103")
'        If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
'        Write_Message strMessage
'    End If
'
'    'Reset toolbar
'    Me.tbAdjustments(0).Tools("ID_ADJLOG").Enabled = True
'    Me.tbAdjustments(0).Tools("ID_SAVE").Enabled = False
'    Me.tbAdjustments(0).Tools("ID_CANCEL").Enabled = False
'
'    'Set return value
'    mGridSummary_Fetch = RtnCode
'
'CleanUp:
'    Screen.MousePointer = vbNormal
'    If Not (QueryForSummary Is Nothing) Then Set QueryForSummary.ActiveConnection = Nothing
'    Set QueryForSummary = Nothing
'    If f_IsRecordsetValidAndOpen(rsSummary) Then rsSummary.Close
'    Set rsSummary = Nothing
'    Write_Message ""
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 380 Then
'        'Invalid property value when setting column width. Check why that errors out, but resume next since it's not a critical error
'        Resume Next
'    Else
'        ErrNumber = Err.Number
'        ErrSource = Err.source
'        ErrDesc = Err.Description
'
'        Screen.MousePointer = vbNormal
'        If Not (QueryForSummary Is Nothing) Then Set QueryForSummary.ActiveConnection = Nothing
'        Set QueryForSummary = Nothing
'        If f_IsRecordsetValidAndOpen(rsSummary) Then rsSummary.Close
'        Set rsSummary = Nothing
'
'        Err.Raise ErrNumber, ErrSource, ErrDesc & "(mGridSummary_Fetch)"
'    End If
'End Function
'
'Private Function mGridSummary_Set() As Long
'On Error GoTo ErrorHandler
'
'    Dim StatusPercent As Long
'    Dim IndexCounter As Long, RowCount As Long, ElementCount As Long
'    Dim RowIdx As Long, ColIdx As Long, PeriodIdx As Long, LoopCounter As Long
'    Dim EBSummary As SSOleDBGrid    'Early-binding is faster than late-binding a COM control
'    Dim BoolReturn As Boolean
'
'    mGridSummary_Set = -1
'
'    If Not f_IsRecordsetOpenAndPopulated(m_rsGridSummary) Then Exit Function
'
'    Set EBSummary = Me.dgSummary
'    'Clear any existing columns
'    EBSummary.Columns.RemoveAll
'    EBSummary.Reset
'    EBSummary.Refresh
'    EBSummary.Redraw = False
'    EBSummary.Visible = False
'
'    EBSummary.Reset
'    EBSummary.Refresh
'
'    'Set up the Grid
''Attribs/Filters
'    IndexCounter = 0
'    EBSummary.Columns.Add IndexCounter
'    EBSummary.Columns(IndexCounter).Name = m_GroupByFieldName
'    EBSummary.Columns(IndexCounter).Caption = getTranslationResource(m_GroupByFieldName)
'    EBSummary.Columns(IndexCounter).Width = 2000
'    EBSummary.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBSummary.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    m_SmrCol_GroupBy = IndexCounter
'
''Data
'    IndexCounter = IndexCounter + 1
'    EBSummary.Columns.Add IndexCounter
'    EBSummary.Columns(IndexCounter).Name = "DataCalc_Type"
'    EBSummary.Columns(IndexCounter).Width = 1600
'    EBSummary.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBSummary.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBSummary.Columns(IndexCounter).Visible = False '<<<
'    m_SmrCol_DataType = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBSummary.Columns.Add IndexCounter
'    EBSummary.Columns(IndexCounter).Name = "DataCalcDesc"
'    EBSummary.Columns(IndexCounter).Caption = getTranslationResource("Data Type")
'    EBSummary.Columns(IndexCounter).Width = 1600
'    EBSummary.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBSummary.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    m_SmrCol_DataTypeDesc = IndexCounter
'
''Totals
'    IndexCounter = IndexCounter + 1
'    EBSummary.Columns.Add IndexCounter
'    EBSummary.Columns(IndexCounter).Name = "Totals"
'    EBSummary.Columns(IndexCounter).Caption = getTranslationResource("Totals")
'    EBSummary.Columns(IndexCounter).Width = 1000
'    EBSummary.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBSummary.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
'    EBSummary.Columns(IndexCounter).NumberFormat = "###############0.00"
'    EBSummary.Columns(IndexCounter).DataType = vbDouble
'    m_SmrCol_Totals = IndexCounter
'
'    'Do not increment IndexCounter before periods
'    m_SmrCol_FirstPeriod = IIf(PeriodIdx > 0, PeriodIdx, 1) + IndexCounter
'
''Period dates
'    ColIdx = m_SmrCol_FirstPeriod
'    For PeriodIdx = 1 To m_TotalPeriods     'What was selected for viewing, as opposed to what actually came from the database
'        EBSummary.Columns.Add ColIdx
'        EBSummary.Columns(ColIdx).Name = "Period" & CStr(PeriodIdx)
'        EBSummary.Columns(ColIdx).Caption = CStr(Format(m_PeriodCaption(PeriodIdx), gDateFormat))
'        EBSummary.Columns(ColIdx).Width = 1200
'        EBSummary.Columns(ColIdx).CaptionAlignment = ssColCapAlignLeftJustify
'        EBSummary.Columns(ColIdx).Alignment = ssCaptionAlignmentRight
'        EBSummary.Columns(ColIdx).NumberFormat = "###############0.00"
'        EBSummary.Columns(ColIdx).DataType = vbDouble
'        ColIdx = ColIdx + 1
'    Next
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths EBSummary, ACW_EXPAND
'    End If
'
'    'Set flag to done
'    m_IsGridSummaryLoaded = True
'
'    'Display data
'    EBSummary.ReBind
'
'    EBSummary.SplitterPos = m_SmrCol_Totals
'    EBSummary.MoveFirst
'    EBSummary.Redraw = True
'    EBSummary.Visible = True
'    'Set this grid to read-only
'    EBSummary.AllowUpdate = False
'    Set EBSummary = Nothing
'
'    mGridSummary_Set = 1
'
'    Write_Message ""
'
'Exit Function
'ErrorHandler:
'    EBSummary.Redraw = True
'    EBSummary.Visible = True
'    Screen.MousePointer = vbNormal
'    Set EBSummary = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(mGridSummary_Set)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Fetches data from the AIMFcstStore* tables for the given FcstID/FcstSetupKey
'' .......................................................................
''   Returns false if the process failed
''   else true after redrawing the items grid
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mGridDetail_Fetch( _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim QueryForDetail As ADODB.Command, rsDetail As ADODB.Recordset
'    Dim QueryText As String, strMessage As String
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    Dim intCount As Long
'    Dim ShowAdjustments As Boolean
'    Dim HasAccess As Boolean
'
'    Dim CurrentItem As String, CurrentLcID As String
'    Dim ItStat As String, VnId As String, Assort As String, ById As String
'    Dim Class1 As String, Class2 As String
'    Dim Class3 As String, Class4 As String
'    Dim VelCode As String, LStatus As String
'    Dim LDivision As String, LRegion As String, LUserDefined As String
'    Dim FcstDemand As String, FcstMethod As Long
'    Dim MAE As Double, MSE As Double
'    Dim Trend As Double, TrkSignalFlag As String, UOM As String
'    Dim AddAdjustments() As String
'
'    Dim AdjustedCalc As Double
'    Dim RowIdx As Long, ColIdx As Long, PeriodIdx As Long, AdjIdx As Long, LoopCounter As Long
'    Dim RowCount As Long, ElementCount As Long, IndexCounter As Long
'    Dim StatusPercent As Long
'    Dim IsKeyRowSet As Boolean
'
'    Dim CheckTime As Date
'
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    If IsNull(m_FcstSetupKey) Or m_FcstSetupKey = -1 Then Exit Function
'
'    'Set flag to busy
'    m_IsGridDetailLoaded = False
'    Screen.MousePointer = vbHourglass
'
'    If Me.ckToggleAdjDisplay(1).Value = vbChecked Then ShowAdjustments = True
'
'    'BuildQueryText
'    QueryText = mGridQueryBuilder(TAB_DETAIL, ShowAdjustments)
'
'    'Init. the command object
'    Set QueryForDetail = New ADODB.Command
'    With QueryForDetail
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdText
'        .CommandText = QueryText
'    End With
'
'    'Init. the recordset
'    Set rsDetail = New ADODB.Recordset
'    With rsDetail
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in the data
'    CheckTime = Now()
'    rsDetail.Open QueryForDetail
'    Debug.Print "Total time for retrieving query: " & DateDiff("s", CheckTime, Now())
'
'    If f_IsRecordsetOpenAndPopulated(rsDetail) Then
'        'Set the main forecaststorekey
'        m_FcstStoreKey_Main = rsDetail!FcstStoreKey
'        RowCount = rsDetail.RecordCount
'        'Associate as a disconnected recordset
'        If f_IsRecordsetValidAndOpen(m_rsGridDetail) Then m_rsGridDetail.Close
'        Set m_rsGridDetail = Nothing
'        Set m_rsGridDetail = New ADODB.Recordset
'        m_rsGridDetail.Fields.Append "LcID", adVarWChar, 12
'        m_rsGridDetail.Fields.Append "Item", adVarWChar, 25
'        m_rsGridDetail.Fields.Append "ItDesc", adVarWChar, 50
'        m_rsGridDetail.Fields.Append "DataCalc_Type", adTinyInt
'        m_rsGridDetail.Fields.Append "DataCalcDesc", adVarWChar, 255
'        m_rsGridDetail.Fields.Append "Totals", adDecimal, 10.2
'        m_rsGridDetail.Fields("Totals").Precision = 10
'        m_rsGridDetail.Fields("Totals").NumericScale = 2
'        For PeriodIdx = 1 To m_TotalPeriods     'What was selected for viewing, as opposed to what actually came from the database
'            'Some of these periods may have no data.
'            m_rsGridDetail.Fields.Append "Period" & CStr(PeriodIdx), adDecimal
'            m_rsGridDetail.Fields("Period" & CStr(PeriodIdx)).Precision = 10
'            m_rsGridDetail.Fields("Period" & CStr(PeriodIdx)).NumericScale = 2
'        Next
'        m_rsGridDetail.Fields.Append "ItStat", adVarWChar, 1
'        m_rsGridDetail.Fields.Append "Class1", adVarWChar, 50
'        m_rsGridDetail.Fields.Append "Class2", adVarWChar, 50
'        m_rsGridDetail.Fields.Append "Class3", adVarWChar, 50
'        m_rsGridDetail.Fields.Append "Class4", adVarWChar, 50
'        m_rsGridDetail.Fields.Append "VelCode", adVarWChar, 1
'        m_rsGridDetail.Fields.Append "VnId", adVarWChar, 12
'        m_rsGridDetail.Fields.Append "Assort", adVarWChar, 12
'        m_rsGridDetail.Fields.Append "ByID", adVarWChar, 12
'        m_rsGridDetail.Fields.Append "LStatus", adVarWChar, 1
'        m_rsGridDetail.Fields.Append "LDivision", adVarWChar, 20
'        m_rsGridDetail.Fields.Append "LRegion", adVarWChar, 20
'        m_rsGridDetail.Fields.Append "LUserDefined", adVarWChar, 30
''        m_rsGridDetail.Fields.Append "ElementRowIndex", adInteger       'Used for tracking the "element" type
''        m_rsGridDetail.Fields.Append "KeyRow", adBoolean                'Used for adjustments and grouping
'
'        'Populate
'        rsDetail.MoveFirst
'        RowIdx = 0
'        'ElementCount = DATA_CALC_TYPE.DCT_MAXVALUE - 1
'        m_rsGridDetail.Open
'        rsDetail.MoveFirst
'        Do Until rsDetail.eof
'            'Set display to match recordset(s)
'            With m_rsGridDetail
'                .AddNew
'                AdjIdx = 2
'                For ColIdx = 0 To m_rsGridDetail.Fields.Count - 1
'                    If ColIdx = 4 Then  'DataCalcType or AdjustType
'                        If Not IsNull(rsDetail.Fields(AdjIdx + 1).Value) Then
'                            AdjIdx = AdjIdx + 2
'                        End If
'                        .Fields(ColIdx) = rsDetail.Fields(AdjIdx)
'                        AdjIdx = IIf(AdjIdx = 6, AdjIdx + 3, AdjIdx + 1)
'                    Else
'                        .Fields(ColIdx) = rsDetail.Fields(AdjIdx)
'                        AdjIdx = AdjIdx + 1
'                    End If
'                Next
'            End With
'            rsDetail.MoveNext
'        Loop
'        m_rsGridDetail.MoveFirst
'
'        'Display
'        RtnCode = mGridDetail_Set(ShowAdjustments)
'
'        'Reset adjustment fields
'        Me.txtAdjQty_Dtl.Value = 0
'        Me.optAdjType_Dtl(ADJ_UNITS).Value = True
'
'        Me.dcAdjStart_Dtl.MinDate = m_FirstPeriod
'        Me.dcAdjStart_Dtl.MaxDate = m_LastPeriod
'        Me.dcAdjStart_Dtl.dateValue = Format(m_FirstPeriod, g_ISO_DATE_FORMAT)
'
'        Me.dcAdjEnd_Dtl.MinDate = m_FirstPeriod
'        Me.dcAdjEnd_Dtl.MaxDate = m_LastPeriod
'        Me.dcAdjEnd_Dtl.dateValue = Format(m_LastPeriod, g_ISO_DATE_FORMAT)
'
'        Me.txtAdjDesc_Dtl.Text = ""
'    Else
'        strMessage = getTranslationResource("TEXTMSG05103")
'        If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
'        Write_Message strMessage
'    End If
'
'    'Reset toolbar
'    Me.tbAdjustments(1).Tools("ID_ADJLOG").Enabled = True
'    Me.tbAdjustments(1).Tools("ID_SAVE").Enabled = False
'    Me.tbAdjustments(1).Tools("ID_CANCEL").Enabled = False
'
'    mGridDetail_Fetch = RtnCode
'
'CleanUp:
'    Screen.MousePointer = vbNormal
'    If Not (QueryForDetail Is Nothing) Then Set QueryForDetail.ActiveConnection = Nothing
'    Set QueryForDetail = Nothing
'    If f_IsRecordsetValidAndOpen(rsDetail) Then rsDetail.Close
'    Set rsDetail = Nothing
'    Write_Message ""
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 380 Then
'        'Invalid property value when setting column width. Check why that errors out, but resume next since it's not a critical error
'        Resume Next
'    Else
'        ErrNumber = Err.Number
'        ErrSource = Err.source
'        ErrDesc = Err.Description
'
'        Screen.MousePointer = vbNormal
'        If Not (QueryForDetail Is Nothing) Then Set QueryForDetail.ActiveConnection = Nothing
'        Set QueryForDetail = Nothing
'        If f_IsRecordsetValidAndOpen(rsDetail) Then rsDetail.Close
'        Set rsDetail = Nothing
'
'        Err.Raise Err.Number, Err.source, Err.Description & "(mGridDetail_Fetch)"
'    End If
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Called from mGridDetail_Fetch -- redraws the item grid with given data
'' .......................................................................
''   Returns false if the process failed
''   else true after redrawing the items grid
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mGridDetail_Set( _
'    p_ShowAdjustments As Boolean _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim FilterData As String
'    Dim RowCount As Long
'    Dim IndexCounter As Long, PeriodIdx As Long, ColIdx As Long
'    Dim BoolReturn As Boolean
'
'    Dim EBDetail As SSOleDBGrid    'Early-binding is faster than late-binding a COM control
'
'    If Not f_IsRecordsetOpenAndPopulated(m_rsGridDetail) Then
'        Exit Function
'    End If
'
'    RowCount = m_rsGridDetail.RecordCount
'
'    Set EBDetail = Me.dgDetail
'    'Clear any existing columns
'    EBDetail.Columns.RemoveAll
'    EBDetail.Reset
'    EBDetail.Refresh
'    EBDetail.Redraw = False
'    EBDetail.Visible = False
'    Screen.MousePointer = vbHourglass
'
'    'Set up the Grid
'    'Attribs/Filters
'    IndexCounter = 0
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "LcID"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Location")
'    EBDetail.Columns(IndexCounter).Width = 1600
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_LcID = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Item"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Item")
'    EBDetail.Columns(IndexCounter).Width = 1600
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    m_DtlCol_Item = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "ItDesc"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Description")
'    EBDetail.Columns(IndexCounter).Width = 1600
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    m_DtlCol_ItDesc = IndexCounter
'
'    'Data
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "DataCalc_Type"
'    EBDetail.Columns(IndexCounter).Width = 1600
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    EBDetail.Columns(IndexCounter).Visible = False
'    m_DtlCol_DataType = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "DataCalcDesc"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Data Type")
'    EBDetail.Columns(IndexCounter).Width = 1600
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_DataDesc = IndexCounter
'
'    'Totals
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Totals"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Totals")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
'    EBDetail.Columns(IndexCounter).NumberFormat = "###############0.00"
'    EBDetail.Columns(IndexCounter).DataType = vbDouble
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_Totals = IndexCounter
'
'    'Do not increment index counter before periods.
'    m_DtlCol_FirstPeriod = IIf(PeriodIdx > 0, PeriodIdx, 1) + IndexCounter
'
'    'Period dates
'    ColIdx = m_DtlCol_FirstPeriod
'    For PeriodIdx = 1 To m_TotalPeriods     'What was selected for viewing, as opposed to what actually came from the database
'        EBDetail.Columns.Add ColIdx
'        EBDetail.Columns(ColIdx).Name = "Period" & CStr(PeriodIdx)
'        EBDetail.Columns(ColIdx).Caption = CStr(Format(m_PeriodCaption(PeriodIdx), gDateFormat))
'        EBDetail.Columns(ColIdx).Width = 1200
'        EBDetail.Columns(ColIdx).CaptionAlignment = ssColCapAlignLeftJustify
'        EBDetail.Columns(ColIdx).Alignment = ssCaptionAlignmentRight
'        EBDetail.Columns(ColIdx).NumberFormat = "###############0.00"
'        EBDetail.Columns(ColIdx).DataType = vbDouble
'        EBDetail.Columns(ColIdx).Locked = True
'        ColIdx = ColIdx + 1
'    Next
'
'    IndexCounter = ColIdx
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "ItStat"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Item Status")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_ItStat = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Class1"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Class 1")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_Class1 = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Class2"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Class 2")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_Class2 = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Class3"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Class 3")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_Class3 = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Class4"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Class 4")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_Class4 = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "VelCode"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Velocity Code")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_VelCode = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "VnID"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Vendor ID")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_VnID = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "Assort"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Assort")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_Assort = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "ByID"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Buyer")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_ByID = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "LStatus"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Location Status")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_LStatus = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "LDivision"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Division")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_LDivision = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "LRegion"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("Region")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_LRegion = IndexCounter
'
'    IndexCounter = IndexCounter + 1
'    EBDetail.Columns.Add IndexCounter
'    EBDetail.Columns(IndexCounter).Name = "LUserDefined"
'    EBDetail.Columns(IndexCounter).Caption = getTranslationResource("UserDefined")
'    EBDetail.Columns(IndexCounter).Width = 1200
'    EBDetail.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    EBDetail.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    EBDetail.Columns(IndexCounter).Locked = True
'    m_DtlCol_LUserDefined = IndexCounter
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths EBDetail, ACW_EXPAND
'    End If
'
'    'Set flag to done
'    m_IsGridDetailLoaded = True
'
'    'Bind the grid
'    EBDetail.ReBind
'
'    EBDetail.SplitterPos = m_DtlCol_Totals
'    EBDetail.MoveFirst
'    EBDetail.Redraw = True
'    EBDetail.Visible = True
'    'Set this grid to read-only
'    EBDetail.AllowUpdate = False
'    Set EBDetail = Nothing
'
'    mGridDetail_Set = 1
'
'    Write_Message ""
'
'Exit Function
'ErrorHandler:
'    If Err.Number = 380 Then
'        'Invalid property value when setting column width. Check why that errors out, but resume next since it's not a critical error
'        Resume Next
'    Else
'        Set EBDetail = Nothing
'        Err.Raise Err.Number, Err.source, Err.Description & "(mGridDetail_Set)"
'    End If
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Summarizes item statistics for display
'' .......................................................................
''   Returns false if the process failed
''   else true after redrawing the metrics tab
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mItemStats_Set( _
'    p_Item As String, p_LcID As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'    Dim RtnCode As Long
'    Dim EBBaseMetrics As SSOleDBGrid
'
'    RtnCode = -1
'
'    Set EBBaseMetrics = Me.dgBaseMetrics
'    EBBaseMetrics.Redraw = False
'    EBBaseMetrics.Visible = False
'    EBBaseMetrics.Columns.RemoveAll
'    EBBaseMetrics.Reset
'    EBBaseMetrics.Refresh
'
'    With EBBaseMetrics
'        IndexCounter = 0
'        .Columns.Add IndexCounter
'        .Columns(IndexCounter).Name = "Titles"
'        .Columns(IndexCounter).Caption = ""
'        .Columns(IndexCounter).BackColor = &H8000000F
'        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'
'        IndexCounter = IndexCounter + 1
'        .Columns.Add IndexCounter
'        .Columns(IndexCounter).Name = "Dates"
'        .Columns(IndexCounter).Caption = ""
'        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        IndexCounter = IndexCounter + 1
'
'        IndexCounter = IndexCounter + 1
'        .Columns.Add IndexCounter
'        .Columns(IndexCounter).Name = "Dates"
'        .Columns(IndexCounter).Caption = ""
'        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        IndexCounter = IndexCounter + 1
'    End With
'
'
'
'
'    mItemStats_Set = RtnCode
'
'    EBBaseMetrics.Redraw = True
'    EBBaseMetrics.Visible = True
'    Set EBBaseMetrics = Nothing
'
'Exit Function
'ErrorHandler:
'    EBBaseMetrics.Redraw = True
'    EBBaseMetrics.Visible = True
'    Set EBBaseMetrics = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(mItemStats_Set)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Sets default values for the chart/graph
'' .......................................................................
''   Returns false if the process failed
''   else true after redrawing the chart
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mChart_Initialize()
'On Error GoTo ErrorHandler
'
'    Dim Counter As Long
'
'    With Me.chtForecast
'        .IsBatched = True       'Disable the control from repainting until the process has finished.
'
'        .AllowUserChanges = False
'
'        'Set up Header
'        .Header.Text = ""
'        .Header.Font.Bold = True
'        .Border.Type = oc2dBorderPlain
'        .Border.Width = 2
'
'        .Border.Type = oc2dBorderPlain
'        .Border.Width = 2
'
'        'Set up Legends
'        .Legend.Anchor = oc2dAnchorEast
'        .Legend.IsShowing = True
'        .Legend.Border.Type = oc2dBorderPlain
'        .Legend.Border.Width = 2
'        .Legend.Text = ""
'
'        'Set Width of X and Y Axes
'        .ChartArea.Axes("X").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("X").Title.Text = ""
'        .ChartArea.Axes("X").Font.Bold = False
'        .ChartArea.Axes("X").MajorGrid.Spacing = 100
'        .ChartArea.Axes("X").MajorGrid.Style.Pattern = oc2dLineNone
'        .ChartArea.Axes("X").Max = 10
'        .ChartArea.Axes("X").AnnotationPlacement = oc2dAnnotateOrigin
'        .ChartArea.Axes("X").AnnotationRotation = oc2dRotateNone
'        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateTimeLabelsEvent
'        .ChartArea.Axes("X").TimeScale.Base = Format(txtFcstStartDate.Text, gDateFormat)
'        If optFcstInterval(int_Days).Value = True Then
'            .ChartArea.Axes("X").TimeScale.Unit = oc2dTimeDays
'            .ChartArea.Axes("X").TimeScale.Format.Value = "%d"
'        ElseIf optFcstInterval(int_Weeks).Value = True Then
'            .ChartArea.Axes("X").TimeScale.Unit = oc2dTimeWeeks
'            .ChartArea.Axes("X").TimeScale.Format.Value = "%d-%b-%y"
'        ElseIf optFcstInterval(int_Months).Value = True Then
'            .ChartArea.Axes("X").TimeScale.Unit = oc2dTimeMonths
'            .ChartArea.Axes("X").TimeScale.Format.Value = "%b-%y"
'        ElseIf optFcstInterval(int_Quarters).Value = True Then
'            .ChartArea.Axes("X").TimeScale.Unit = oc2dTimeMonths
'            .ChartArea.Axes("X").TimeScale.Format.Value = "%x"
'        End If
'
'        .ChartArea.Axes("Y").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("Y").Title.Text = ""
'        .ChartArea.Axes("Y").Font.Bold = True
'        .ChartArea.Axes("Y").MajorGrid.Spacing = 10
'        .ChartArea.Axes("Y").MajorGrid.Style.Pattern = oc2dLineSolid
'        .ChartArea.Axes("Y").AnnotationMethod = oc2dAnnotateValues
'        .ChartArea.Axes("Y").TitleRotation = oc2dRotate90Degrees
'        .ChartArea.Axes("Y").Origin = 0
'
'        'Remove any old x-axis labels
'        .ChartArea.Axes("X").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y").ValueLabels.RemoveAll
'
'        'Set up Legends
'        .Legend.Anchor = oc2dAnchorEast
'        .Legend.IsShowing = True
'        .Legend.Border.Type = oc2dBorderPlain
'        .Legend.Border.Width = 2
'        .Legend.Text = ""
'
'        .ChartGroups(1).SeriesLabels.RemoveAll
'        .ChartGroups(1).Styles.RemoveAll
'        .ChartGroups(1).PointStyles.RemoveAll
'
''        .ChartGroups(1).IsStacked = True
''        .ChartGroups(1).Data.Layout = oc2dDataGeneral
''        .ChartGroups(2).Data.Layout = oc2dDataGeneral
'
'
'        'Finalize Grid Spacing
'        .ChartArea.Axes("Y").MajorGrid.Spacing = .ChartArea.Axes("Y").NumSpacing
'    End With
'
'    Me.chtForecast.IsBatched = False  'Process is done. Repaint control.
'    m_IsChartVisible = True
'
'Exit Function
'ErrorHandler:
'    Me.chtForecast.IsBatched = False  'Process is done. Repaint control.
'    'Set default visibility to true
'    m_IsChartVisible = True
'
'    Err.Raise Err.Number, Err.source & "(mChart_Initialize)", Err.Description
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Show/hide chart control based on current state.
'' .......................................................................
''   Returns false if the process failed
''   else true after resizing the chart
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mChart_ToggleSize()
'On Error GoTo ErrorHandler
'
'    If Me.chtForecast.Height > 75 Then
'        Me.chtForecast.Height = 75
'        Me.fraCommon.Height = 1076
'        'Set visibility flag
'        m_IsChartVisible = False
'
'    Else
'        Me.chtForecast.Height = 4000
'        Me.fraCommon.Height = 5000
'        'Set visibility flag
'        m_IsChartVisible = True
'
'    End If
'
'    mForm_Rearrange
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(SizeChart)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Sets data values for the chart/graph
'' .......................................................................
''   Returns false if the process failed
''   else true after redrawing the chart
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mChart_Refresh(p_Selectedtab As String) As Long
'On Error GoTo ErrorHandler
'
'    Dim RowIdx As Long, SeriesIndex As Long, PeriodIdx As Long
'    Dim TotalPeriods As Long
'    Dim RowCount As Long, SeriesCount As Long
'    Dim IndexCounter As Long
'    Dim BkMark, currentBkMark As Variant
'    Dim FilterData As String
'
'    Dim lngCounter As Long
'    Dim LoopUpperBound As Long
'    Dim LoopLowerBound As Long
'    Dim Calc_Val As String
'    Dim HasAccess As Boolean
'    Dim NullString As String
'
'    'Check if data exists
'    If p_Selectedtab = TAB_DETAIL Then
'        If m_IsGridDetailLoaded = False Then
'            Exit Function
'        End If
'    ElseIf m_IsGridSummaryLoaded = False Then
'        Exit Function
'    End If
'
'    NullString = getTranslationResource("NULL")
'
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    If m_IsChartVisible = False Then Exit Function   'save some on performance by refreshing chart only when it is visible.
'
'    'Disable the controls from repainting until the process has finished.
'    Me.chtForecast.IsBatched = True
'    Me.dgSummary.Redraw = False
'    Me.dgDetail.Redraw = False
'    Screen.MousePointer = vbHourglass
'
'    TotalPeriods = m_TotalPeriods
'
'    'Check source
'    If p_Selectedtab = TAB_DETAIL Then
'        BkMark = Me.dgDetail.bookmark
'        m_rsGridDetail.bookmark = BkMark
'
'        FilterData = "Item = '" & m_rsGridDetail.Fields(m_DtlCol_Item).Value & "'"
'        FilterData = FilterData & " AND LcID = '" & m_rsGridDetail.Fields(m_DtlCol_LcID).Value & "'"
'        m_rsGridDetail.Filter = FilterData
'
'        RowCount = m_rsGridDetail.RecordCount
'        Me.chtForecast.Header.Text = m_rsGridDetail.Fields(m_DtlCol_Item).Value & " / " & m_rsGridDetail.Fields(m_DtlCol_LcID).Value
'    Else
'        BkMark = Me.dgSummary.bookmark
'        m_rsGridSummary.bookmark = BkMark
'
'        FilterData = m_GroupByFieldName & " = '" & m_rsGridSummary.Fields(m_SmrCol_GroupBy).Value & "'"
'        m_rsGridSummary.Filter = FilterData
'
'        RowCount = m_rsGridSummary.RecordCount
'        Me.chtForecast.Header.Text = m_rsGridSummary.Fields(0).Value
'    End If
'
'    With Me.chtForecast
'        .AllowUserChanges = False
'
'        .ChartArea.Axes("X").Title.Text = getTranslationResource("Periods")
'        '.ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
'        '.ChartArea.Axes("X").MajorGrid.Spacing = 2
'        .ChartArea.Axes("X").Max = TotalPeriods
'
'        For IndexCounter = Me.optFcstUnit.LBound To Me.optFcstUnit.UBound
'            If Me.optFcstUnit(IndexCounter).Value = True Then
'                .ChartArea.Axes("Y").Title.Text = Me.optFcstUnit(IndexCounter).Caption
'            End If
'        Next
'        'Remove any old x-axis labels
'        .ChartArea.Axes("X").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y").ValueLabels.RemoveAll
'
'        .ChartGroups(1).SeriesLabels.RemoveAll
'
'        'Set up Data Series
'        .ChartGroups(1).ChartType = oc2dTypePlot
'
'        'Set the Line Properties
'        'Initialize series count
'        SeriesCount = 0
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = vbChecked Then
'            SeriesCount = SeriesCount + 1
'            .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_SysRqmt")
'            .ChartGroups(1).Data.NumSeries = SeriesCount
'            .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'            .ChartGroups(1).Styles(SeriesCount).Line.Color = &HB9D071
'            .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'
'            SeriesCount = SeriesCount + 1
'            .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_AdjSysRqmt")
'            .ChartGroups(1).Data.NumSeries = SeriesCount
'            .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'            .ChartGroups(1).Styles(SeriesCount).Line.Color = &HBFAB57
'            .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'        End If
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt).Value = vbChecked Then
'            SeriesCount = SeriesCount + 1
'            .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_NetRqmt")
'            .ChartGroups(1).Data.NumSeries = SeriesCount
'            .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'            .ChartGroups(1).Styles(SeriesCount).Line.Color = &HFDC958
'            .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'
'            SeriesCount = SeriesCount + 1
'            .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_AdjNetRqmt")
'            .ChartGroups(1).Data.NumSeries = SeriesCount
'            .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'            .ChartGroups(1).Styles(SeriesCount).Line.Color = &HCAA046
'            .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'            If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Value = vbChecked Then
'                SeriesCount = SeriesCount + 1
'                .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_NetRqmtAndPlnRcpt")
'                .ChartGroups(1).Data.NumSeries = SeriesCount
'                .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'                .ChartGroups(1).Styles(SeriesCount).Line.Color = &HCAA045
'                .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'            End If
'            If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Value = vbChecked Then
'                SeriesCount = SeriesCount + 1
'                .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_NetRqmtAndPrdCons")
'                .ChartGroups(1).Data.NumSeries = SeriesCount
'                .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'                .ChartGroups(1).Styles(SeriesCount).Line.Color = &HCAA044
'                .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'            End If
'        End If
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_HstDmnd).Value = vbChecked Then
'            SeriesCount = SeriesCount + 1
'            .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_HstDmnd")
'            .ChartGroups(1).Data.NumSeries = SeriesCount
'            .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'            .ChartGroups(1).Styles(SeriesCount).Line.Color = &HF5FFB0
'            .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'
'        End If
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt).Value = vbChecked Then
'            SeriesCount = SeriesCount + 1
'            .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_PrjInvt")
'            .ChartGroups(1).Data.NumSeries = SeriesCount
'            .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'            .ChartGroups(1).Styles(SeriesCount).Line.Color = &HFFDB8C
'            .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'            If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons).Value = vbChecked Then
'                SeriesCount = SeriesCount + 1
'                .ChartGroups(1).SeriesLabels.Add getTranslationResource("Data_PrjInvtAndPrdCons")
'                .ChartGroups(1).Data.NumSeries = SeriesCount
'                .ChartGroups(1).Data.NumPoints(SeriesCount) = TotalPeriods
'                .ChartGroups(1).Styles(SeriesCount).Line.Color = &HCAA044
'                .ChartGroups(1).Styles(SeriesCount).Line.Width = 2
'            End If
'        End If
'
'        'Load Data
'        Do While RowIdx < RowCount
'            If p_Selectedtab = TAB_DETAIL Then
'                If m_rsGridDetail.eof Then Exit Do
'                currentBkMark = m_rsGridDetail.bookmark
'                For SeriesIndex = 1 To MinCalcType
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = vbChecked Then
'                        currentBkMark = m_rsGridDetail.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridDetail.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        currentBkMark = m_rsGridDetail.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridDetail.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'                    End If
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt).Value = vbChecked Then
'                        currentBkMark = m_rsGridDetail.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridDetail.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        currentBkMark = m_rsGridDetail.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridDetail.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Value = vbChecked Then
'                            currentBkMark = m_rsGridDetail.bookmark
'                            For PeriodIdx = 1 To TotalPeriods
'                                Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                                If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                                .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                            Next PeriodIdx
'                            m_rsGridDetail.MoveNext
'                            RowIdx = RowIdx + 1
'                            SeriesIndex = SeriesIndex + 1
'                        End If
'                        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Value = vbChecked Then
'                            currentBkMark = m_rsGridDetail.bookmark
'                            For PeriodIdx = 1 To TotalPeriods
'                                Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                                If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                                .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                            Next PeriodIdx
'                            m_rsGridDetail.MoveNext
'                            RowIdx = RowIdx + 1
'                            SeriesIndex = SeriesIndex + 1
'                        End If
'                    End If
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_HstDmnd).Value = vbChecked Then
'                        currentBkMark = m_rsGridDetail.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridDetail.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'                    End If
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt).Value = vbChecked Then
'                        currentBkMark = m_rsGridDetail.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridDetail.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons).Value = vbChecked Then
'                            currentBkMark = m_rsGridDetail.bookmark
'                            For PeriodIdx = 1 To TotalPeriods
'                                Calc_Val = m_rsGridDetail.Fields(m_DtlCol_Totals + PeriodIdx).Value
'                                If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                                .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                            Next PeriodIdx
'                            m_rsGridDetail.MoveNext
'                            RowIdx = RowIdx + 1
'                            SeriesIndex = SeriesIndex + 1
'                        End If
'                    End If
'                Next SeriesIndex
'            Else
'                If m_rsGridSummary.eof Then Exit Do
'                currentBkMark = m_rsGridSummary.bookmark
'                For SeriesIndex = 1 To MinCalcType
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = vbChecked Then
'                        currentBkMark = m_rsGridSummary.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridSummary.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        currentBkMark = m_rsGridSummary.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridSummary.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'                    End If
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt).Value = vbChecked Then
'                        currentBkMark = m_rsGridSummary.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridSummary.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        currentBkMark = m_rsGridSummary.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridSummary.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'
'                        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Value = vbChecked Then
'                            currentBkMark = m_rsGridSummary.bookmark
'                            For PeriodIdx = 1 To TotalPeriods
'                                Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                                If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                                .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                            Next PeriodIdx
'                            m_rsGridSummary.MoveNext
'                            RowIdx = RowIdx + 1
'                            SeriesIndex = SeriesIndex + 1
'                        End If
'                        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Value = vbChecked Then
'                            currentBkMark = m_rsGridSummary.bookmark
'                            For PeriodIdx = 1 To TotalPeriods
'                                Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                                If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                                .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                            Next PeriodIdx
'                            m_rsGridSummary.MoveNext
'                            RowIdx = RowIdx + 1
'                            SeriesIndex = SeriesIndex + 1
'                        End If
'                    End If
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_HstDmnd).Value = vbChecked Then
'                        currentBkMark = m_rsGridSummary.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridSummary.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'                    End If
'                    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt).Value = vbChecked Then
'                        currentBkMark = m_rsGridSummary.bookmark
'                        For PeriodIdx = 1 To TotalPeriods
'                            Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                            If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                            .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                        Next PeriodIdx
'                        m_rsGridSummary.MoveNext
'                        RowIdx = RowIdx + 1
'                        SeriesIndex = SeriesIndex + 1
'                        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons).Value = vbChecked Then
'                            currentBkMark = m_rsGridSummary.bookmark
'                            For PeriodIdx = 1 To TotalPeriods
'                                Calc_Val = m_rsGridSummary.Fields(m_SmrCol_Totals + PeriodIdx).Value
'                                If IsNull(Calc_Val) Or Trim$(Calc_Val) = NullString Or Trim$(Calc_Val) = "" Then Calc_Val = 0
'                                .ChartGroups(1).Data.Y(SeriesIndex, PeriodIdx) = CDbl(Calc_Val)
'                            Next PeriodIdx
'                            m_rsGridSummary.MoveNext
'                            RowIdx = RowIdx + 1
'                            SeriesIndex = SeriesIndex + 1
'                        End If
'                    End If
'                Next SeriesIndex
'            End If
'        Loop
'
'        'Finalize Grid Spacing
'        .ChartArea.Axes("Y").MajorGrid.Spacing = .ChartArea.Axes("Y").NumSpacing
'    End With
'
'    If p_Selectedtab = TAB_SUMMARY Then
'        m_rsGridSummary.Filter = ""
'        m_rsGridSummary.bookmark = BkMark
'    ElseIf p_Selectedtab = TAB_DETAIL Then
'        m_rsGridDetail.Filter = ""
'        m_rsGridSummary.bookmark = BkMark
'    End If
'
'    'Process is done. Repaint controls
'    Me.chtForecast.IsBatched = False
'    Me.dgSummary.Redraw = True
'    Me.dgDetail.Redraw = True
'    Screen.MousePointer = vbNormal
'
'Exit Function
'ErrorHandler:
'    'Process is done. Repaint controls
'    Me.chtForecast.IsBatched = False
'    Me.dgSummary.Redraw = True
'    Me.dgDetail.Redraw = True
'    Screen.MousePointer = vbNormal
'    Err.Raise Err.Number, Err.source, Err.Description & "(mChart_Refresh)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Called from mGridDetail_Set -- fetches given item's attributes from the database.
'' .......................................................................
''   Returns false if the process failed
''   else true after setting the attributes return-parameters
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mItemAttribs_Fetch( _
'    p_Item As String, p_LcID As String, _
'    r_ItStat As String, r_VnId As String, _
'    r_Assort As String, r_ById As String, _
'    r_Class1 As String, r_Class2 As String, _
'    r_Class3 As String, r_Class4 As String, _
'    r_VelCode As String, _
'    r_LStatus As String, r_LDivision As String, _
'    r_LRegion As String, r_LUserDefined As String, _
'    r_FcstDemand As String, r_MAE As Double, _
'    r_Trend As Double, r_UOM As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_ItemAttributes_Fetch_Sp As ADODB.Command
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    'Init. the command object
'    Set AIM_ItemAttributes_Fetch_Sp = New ADODB.Command
'    With AIM_ItemAttributes_Fetch_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ItemAttributes_Fetch_Sp"
'
'    'Set parameters and their values
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25, p_Item)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12, p_LcID)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@ItDesc", adVarWChar, adParamOutput, 50)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@ItStat", adVarWChar, adParamOutput, 1)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Class1", adVarWChar, adParamOutput, 50)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Class2", adVarWChar, adParamOutput, 50)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Class3", adVarWChar, adParamOutput, 50)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Class4", adVarWChar, adParamOutput, 50)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@VelCode", adVarWChar, adParamOutput, 1)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@VnId", adVarWChar, adParamOutput, 12)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Assort", adVarWChar, adParamOutput, 12)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@ByID", adVarWChar, adParamOutput, 12)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@LStatus", adVarWChar, adParamOutput, 1)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@LDivision", adVarWChar, adParamOutput, 20)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@LRegion", adVarWChar, adParamOutput, 20)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@LUserDefined", adVarWChar, adParamOutput, 30)
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@FcstDemand", adDecimal, adParamOutput)
'        .Parameters("@FcstDemand").Precision = 10
'        .Parameters("@FcstDemand").NumericScale = 2
'
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@MAE", adDecimal, adParamOutput)
'        .Parameters("@MAE").Precision = 10
'        .Parameters("@MAE").NumericScale = 2
'
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@Trend", adDecimal, adParamOutput)
'        .Parameters("@Trend").Precision = 10
'        .Parameters("@Trend").NumericScale = 3
'        .Parameters.Append AIM_ItemAttributes_Fetch_Sp.CreateParameter("@UOM", adVarWChar, adParamOutput, 6)
'
'    'Fetch results
'        .Execute
'    End With
'
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_ItemAttributes_Fetch_Sp.Parameters(0).Value
'    Select Case RtnCode
'    Case Is > 0 'Success
'        With AIM_ItemAttributes_Fetch_Sp
'             r_ItStat = .Parameters("@ItStat").Value
'             r_VnId = .Parameters("@VnId").Value
'             r_Assort = .Parameters("@Assort").Value
'             r_ById = .Parameters("@ByID").Value
'             r_Class1 = .Parameters("@Class1").Value
'             r_Class2 = .Parameters("@Class2").Value
'             r_Class3 = .Parameters("@Class3").Value
'             r_Class4 = .Parameters("@Class4").Value
'             r_VelCode = .Parameters("@VelCode").Value
'             r_LStatus = .Parameters("@LStatus").Value
'             r_LDivision = .Parameters("@LDivision").Value
'             r_LRegion = .Parameters("@LRegion").Value
'             r_LUserDefined = .Parameters("@LUserDefined").Value
'             r_FcstDemand = .Parameters("@FcstDemand").Value
'             r_MAE = .Parameters("@MAE").Value
'             r_Trend = .Parameters("@Trend").Value
'             r_UOM = .Parameters("@UOM").Value
'        End With
'
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    'Set return value
'    mItemAttribs_Fetch = RtnCode
'
'CleanUp:
'    If Not (AIM_ItemAttributes_Fetch_Sp Is Nothing) Then Set AIM_ItemAttributes_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_ItemAttributes_Fetch_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_ItemAttributes_Fetch_Sp Is Nothing) Then Set AIM_ItemAttributes_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_ItemAttributes_Fetch_Sp = Nothing
'
'    Err.Raise Err.Number, Err.source, Err.Description & "(mItemAttribs_Fetch)"
'End Function
'
'
'Private Function mApplyMassAdjustments(p_Selectedtab As String) As Long
'On Error GoTo ErrorHandler
'
'    Dim BkMark As Variant, LoopBkMark As Variant
'    Dim AddRowCount As Long, PrevRowCount As Long, NewRowCount As Long
'    Dim ColIdx As Long, RowIdx As Long, GridRowIdx As Long
'    Dim IndexCounter As Long, tempInt As Long
'    Dim Calc_Val As String
'
'    Dim FcstStartDate As Date
'    Dim AdjustStartDate As Date, AdjustEndDate As Date
'    Dim FcstInterval As Long
'
'    Dim AIM_DmdPlanAdjust_FetchGroupByItems_Sp As ADODB.Command
'    Dim rsGroupByItems As ADODB.Recordset
'    Dim GroupBy_FieldName As String
'    Dim GroupBy_FieldValue As String
'
'    Dim RtnCode As Long
'    Dim HasAccess As Boolean
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_ModifyDemandPlan)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    Screen.MousePointer = vbHourglass
'
'    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
'        tempInt = IIf(Me.optFcstInterval(IndexCounter).Value = True, IndexCounter, tempInt)
'    Next
'    FcstInterval = tempInt
'
'    Select Case p_Selectedtab
'    Case TAB_SUMMARY
'        'Check if the load process has finished processing.
'        If Not m_IsGridSummaryLoaded Then Exit Function
'        With Me.dgSummary
'            .Redraw = False   'Disable the control from repainting until the process has finished.
'            BkMark = .bookmark
'        End With
'        AdjustStartDate = Me.dcAdjStart_Smr.Text
'        AdjustEndDate = Me.dcAdjEnd_Smr.Text
'        GroupBy_FieldName = Me.dcGroupBy.Text
'
'    Case TAB_DETAIL
'        'Check if the load process has finished processing.
'        If Not m_IsGridDetailLoaded Then Exit Function
'        With Me.dgDetail
'            .Redraw = False   'Disable the control from repainting until the process has finished.
'            BkMark = .bookmark
'        End With
'        AdjustStartDate = Me.dcAdjStart_Dtl.Text
'        AdjustEndDate = Me.dcAdjEnd_Dtl.Text
'
'    End Select
'
'    RowIdx = 1 ' since option base is 1
'    Select Case p_Selectedtab
'    Case TAB_SUMMARY
'        Set AIM_DmdPlanAdjust_FetchGroupByItems_Sp = New ADODB.Command
'        With AIM_DmdPlanAdjust_FetchGroupByItems_Sp
'            Set .ActiveConnection = Cn
'            .CommandType = adCmdStoredProc
'            .CommandText = "AIM_DmdPlanAdjust_FetchGroupByItems_Sp"
'
'            'Set parameters and their values
'            .Parameters.Append AIM_DmdPlanAdjust_FetchGroupByItems_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'            'Input
'            .Parameters.Append AIM_DmdPlanAdjust_FetchGroupByItems_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'            .Parameters.Append AIM_DmdPlanAdjust_FetchGroupByItems_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamInput)
'            .Parameters.Append AIM_DmdPlanAdjust_FetchGroupByItems_Sp.CreateParameter("@GroupBy_FieldName", adVarWChar, adParamInput, 255)
'            .Parameters.Append AIM_DmdPlanAdjust_FetchGroupByItems_Sp.CreateParameter("@GroupBy_FieldValue", adVarWChar, adParamInput, 255)
'            .Parameters.Append AIM_DmdPlanAdjust_FetchGroupByItems_Sp.CreateParameter("@RecordCount", adInteger, adParamOutput)
'        End With
'        With Me.dgSummary
'            .MoveLast
'            AddRowCount = .Rows
'            .MoveFirst
'            GridRowIdx = 0
'            Do While GridRowIdx < AddRowCount
'                LoopBkMark = .bookmark
'                GroupBy_FieldValue = .Columns(m_SmrCol_GroupBy).CellText(LoopBkMark)
'                With AIM_DmdPlanAdjust_FetchGroupByItems_Sp
'                    'Set values
'                    .Parameters("@FcstSetupKey").Value = m_FcstSetupKey
'                    .Parameters("@FcstStoreKey").Value = m_FcstStoreKey_Main
'                    .Parameters("@GroupBy_FieldName").Value = GroupBy_FieldName
'                    .Parameters("@GroupBy_FieldValue").Value = GroupBy_FieldValue
'                    .Parameters("@RecordCount").Value = Null
'                End With
'                If f_IsRecordsetValidAndOpen(rsGroupByItems) Then rsGroupByItems.Close
'                Set rsGroupByItems = Nothing
'                Set rsGroupByItems = New ADODB.Recordset
'                With rsGroupByItems
'                    .CursorLocation = adUseClient
'                    .CursorType = adOpenStatic
'                    .LockType = adLockReadOnly
'                End With
'
'                'Fetch
'                rsGroupByItems.Open AIM_DmdPlanAdjust_FetchGroupByItems_Sp
'                RtnCode = AIM_DmdPlanAdjust_FetchGroupByItems_Sp.Parameters(0).Value
'                Select Case RtnCode
'                Case Is > 0 'Success
'                    AddRowCount = AIM_DmdPlanAdjust_FetchGroupByItems_Sp.Parameters("@RecordCount").Value
'                    If AddRowCount > 0 Then
'                        PrevRowCount = UBound(m_ForecastAdjust.IsNew, 1)
'                        NewRowCount = PrevRowCount + AddRowCount
'                        With m_ForecastAdjust
'                            ReDim Preserve .LcId(NewRowCount)
'                            ReDim Preserve .Item(NewRowCount)
'                            ReDim Preserve .AdjustQty(NewRowCount)
'                            ReDim Preserve .AdjustType(NewRowCount)
'                            ReDim Preserve .AdjustStartDate(NewRowCount)
'                            ReDim Preserve .AdjustEndDate(NewRowCount)
'                            ReDim Preserve .AdjustDesc(NewRowCount)
'                            ReDim Preserve .AdjustUserID(NewRowCount)
'                            ReDim Preserve .AdjustDateTime(NewRowCount)
'                            ReDim Preserve .IsNew(NewRowCount)
'                        End With
'                        RowIdx = PrevRowCount + 1
'                        rsGroupByItems.MoveFirst
'                        Do While Not rsGroupByItems.eof
'                            m_ForecastAdjust.LcId(RowIdx) = rsGroupByItems!LcId
'                            m_ForecastAdjust.Item(RowIdx) = rsGroupByItems!Item
'
'                            For IndexCounter = Me.optAdjType_Sum.LBound To Me.optAdjType_Sum.UBound
'                                tempInt = IIf(Me.optAdjType_Sum(IndexCounter).Value = True, IndexCounter, tempInt)
''                                Exit For
'                            Next
'                            m_ForecastAdjust.AdjustType(RowIdx) = tempInt
'                            m_ForecastAdjust.AdjustQty(RowIdx) = CDbl(Me.txtAdjQty_Smr.Text)
'                            m_ForecastAdjust.AdjustStartDate(RowIdx) = Format(Me.dcAdjStart_Smr.Text, g_ISO_DATE_FORMAT)
'                            m_ForecastAdjust.AdjustEndDate(RowIdx) = Format(Me.dcAdjEnd_Smr.Text, g_ISO_DATE_FORMAT)
'                            m_ForecastAdjust.AdjustDesc(RowIdx) = Me.txtAdjDesc_Smr.Text
'                            m_ForecastAdjust.AdjustUserID(RowIdx) = gUserID
'                            m_ForecastAdjust.AdjustDateTime(RowIdx) = Format(Now(), g_ISO_DATE_FORMAT)
'                            m_ForecastAdjust.IsNew(RowIdx) = True
'
'                            rsGroupByItems.MoveNext
'                            RowIdx = RowIdx + 1
'                        Loop    'for each item in the summary
'                    End If
'                End Select
'                .MoveNext
'                GridRowIdx = GridRowIdx + 1
'            Loop    'for each row in the grid
'        End With
'        If f_IsRecordsetValidAndOpen(rsGroupByItems) Then rsGroupByItems.Close
'        Set rsGroupByItems = Nothing
'        If Not (AIM_DmdPlanAdjust_FetchGroupByItems_Sp Is Nothing) Then Set AIM_DmdPlanAdjust_FetchGroupByItems_Sp.ActiveConnection = Nothing
'        Set AIM_DmdPlanAdjust_FetchGroupByItems_Sp = Nothing
'
'        Me.dgSummary.bookmark = BkMark
'        'Recalc AdjSysRqmt to show new adjustments
'        Me.dgSummary.Redraw = True    'Process is done. Repaint control.
'
'    Case TAB_DETAIL
'        With Me.dgDetail
'            .MoveLast
'            AddRowCount = .Rows
'            PrevRowCount = UBound(m_ForecastAdjust.IsNew, 1)
'            NewRowCount = PrevRowCount + AddRowCount
'            With m_ForecastAdjust
'                ReDim Preserve .LcId(NewRowCount)
'                ReDim Preserve .Item(NewRowCount)
'                ReDim Preserve .AdjustQty(NewRowCount)
'                ReDim Preserve .AdjustType(NewRowCount)
'                ReDim Preserve .AdjustStartDate(NewRowCount)
'                ReDim Preserve .AdjustEndDate(NewRowCount)
'                ReDim Preserve .AdjustDesc(NewRowCount)
'                ReDim Preserve .AdjustUserID(NewRowCount)
'                ReDim Preserve .AdjustDateTime(NewRowCount)
'                ReDim Preserve .IsNew(NewRowCount)
'            End With
'            RowIdx = PrevRowCount + 1
'
'            .MoveFirst
'            GridRowIdx = 0
'            Do While GridRowIdx < AddRowCount
'                LoopBkMark = .bookmark
'                m_ForecastAdjust.LcId(RowIdx) = .Columns(m_DtlCol_LcID).CellText(LoopBkMark)
'                m_ForecastAdjust.Item(RowIdx) = .Columns(m_DtlCol_Item).CellText(LoopBkMark)
'                For IndexCounter = Me.optAdjType_Dtl.LBound To Me.optAdjType_Dtl.UBound
'                    tempInt = IIf(Me.optAdjType_Dtl(IndexCounter).Value = True, IndexCounter, tempInt)
''                    Exit For
'                Next
'                m_ForecastAdjust.AdjustType(RowIdx) = tempInt
'
'                m_ForecastAdjust.AdjustQty(RowIdx) = CDbl(Me.txtAdjQty_Dtl.Text)
'                m_ForecastAdjust.AdjustStartDate(RowIdx) = Format(Me.dcAdjStart_Dtl.Text, g_ISO_DATE_FORMAT)
'                m_ForecastAdjust.AdjustEndDate(RowIdx) = Format(Me.dcAdjEnd_Dtl.Text, g_ISO_DATE_FORMAT)
'                m_ForecastAdjust.AdjustDesc(RowIdx) = Me.txtAdjDesc_Dtl.Text
'                m_ForecastAdjust.AdjustUserID(RowIdx) = gUserID
'                m_ForecastAdjust.AdjustDateTime(RowIdx) = Format(Now(), g_ISO_DATE_FORMAT)
'                m_ForecastAdjust.IsNew(RowIdx) = True
'
'                .MoveNext
'                GridRowIdx = GridRowIdx + 1
'            Loop
'            .bookmark = BkMark
'            .Redraw = True    'Process is done. Repaint control.
'        End With
'    End Select
'
'    Screen.MousePointer = vbNormal
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    Me.dgSummary.Redraw = True
'    Me.dgDetail.Redraw = True
'    Screen.MousePointer = vbNormal
'    If f_IsRecordsetValidAndOpen(rsGroupByItems) Then rsGroupByItems.Close
'    Set rsGroupByItems = Nothing
'    If Not (AIM_DmdPlanAdjust_FetchGroupByItems_Sp Is Nothing) Then Set AIM_DmdPlanAdjust_FetchGroupByItems_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanAdjust_FetchGroupByItems_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mApplyMassAdjustments)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Saves current set of adjustments to AIMFcstStoreAdjustLog
'' .......................................................................
''   Returns false if the process failed
''   else true after saving to the database
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mSave_ForecastAdjustments(p_Selectedtab As String)
'On Error GoTo ErrorHandler
'
'    Dim RowIdx As Long, RowCount As Long
'    Dim IndexCounter As Long
'    Dim Calc_Val As String
'    Dim tempInt As Long
'
'    Dim RtnCode As Long
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_ModifyDemandPlan)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    'For each record in adjust_log
'    With m_ForecastAdjust
'        RowCount = UBound(m_ForecastAdjust.IsNew, 1)
'        'check is new -- save if true
'        For IndexCounter = 1 To RowCount
'            If .IsNew(IndexCounter) Then
''                RtnCode = gSave_Adjustments(Cn, m_FcstSetupKey, m_FcstStoreKey_Main, _
''                    .LcId(IndexCounter), _
''                    .Item(IndexCounter), _
''                    .AdjustStartDate(IndexCounter), _
''                    .AdjustEndDate(IndexCounter), _
''                    CInt(.AdjustType(IndexCounter)), _
''                    .AdjustQty(IndexCounter), _
''                    .AdjustDesc(IndexCounter), _
''                    .AdjustUserID(IndexCounter))
'            End If
'
'        Next IndexCounter
'    End With
'
'    'next
'    'Set return value
'    mSave_ForecastAdjustments = RtnCode
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mSave_ForecastAdjustments)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Opens modal dialog for showing detailed adjustments log and adding more adjustments
'' .......................................................................
''   Returns false if the process failed
''   else true after closing modal dialog
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mShowAdjLog(p_Selectedtab As String) As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim ShowAdjustments As Boolean
'    Dim IndexCounter As Long
'
'    Dim LcId As String
'    Dim Item As String
'    Dim BkMark As Variant
'    Dim AdjustStart As Date
'    Dim AdjustEnd As Date
'    Dim LastPeriod  As Date
'    Dim TotalPeriods As Long
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_ModifyDemandPlan)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    Dim rsAdjustments As ADODB.Recordset
'
'    TotalPeriods = m_TotalPeriods
'
'    Select Case p_Selectedtab
'    Case TAB_SUMMARY
'        If m_IsGridSummaryLoaded Then
'            BkMark = Me.dgSummary.bookmark
'            If Me.dcGroupBy.Text = GB_LCID Then LcId = Me.dgSummary.Columns(0).CellText(BkMark)
'            If Me.dcGroupBy.Text = GB_ITEM Then Item = Me.dgSummary.Columns(0).CellText(BkMark)
'        End If
'
'    Case TAB_DETAIL
'        If m_IsGridDetailLoaded Then
'            BkMark = Me.dgDetail.bookmark
'            LcId = Me.dgDetail.Columns(m_DtlCol_LcID).CellText(BkMark)
'            Item = Me.dgDetail.Columns(m_DtlCol_Item).CellText(BkMark)
'        End If
'    End Select
'    AdjustStart = Format(m_FirstHistoricalPeriod, g_ISO_DATE_FORMAT)
'    AdjustEnd = Format(m_LastPeriod, g_ISO_DATE_FORMAT)
'
'    With AIM_DmdPlan_Adjustments
'        .p_FcstSetupKey = m_FcstSetupKey
'        For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
'            .p_FcstInterval = IIf(optFcstInterval(IndexCounter).Value = True, IndexCounter, .p_FcstInterval)
'        Next
'        .p_FcstStoreKey = m_FcstStoreKey_Main
'
'        .p_FcstItem = Item
'        .p_FcstLcID = LcId
'        .p_AdjustStartDate = AdjustStart
'        .p_AdjustEndDate = AdjustEnd
'
'        .p_FilterItem = txtItem.Text
'        .p_FilterItemStatus = txtItemStatus.Text
'        .p_FilterVendorID = txtVnId.Text
'        .p_FilterAssortment = txtAssort.Text
'        .p_FilterLocationID = txtLcId.Text
'        .p_FilterBuyerID = txtById.Text
'        .p_FilterClass1 = txtClass(1).Text
'        .p_FilterClass2 = txtClass(2).Text
'        .p_FilterClass3 = txtClass(3).Text
'        .p_FilterClass4 = txtClass(4).Text
'        .p_FilterLDivision = txtLDivision.Text
'        .p_FilterLRegion = txtLRegion.Text
'        .p_FilterLStatus = txtLStatus.Text
'        .p_FilterLUserDefined = txtLUserDefined.Text
'
'        .Show vbModal
'    End With
'
'    'Check for new adjustments
''    RtnCode = gFetch_ForecastAdjustments(Cn, m_FcstStoreKey_Main, LcId, Item, _
''                AdjustStart, AdjustEnd, rsAdjustments)
'
'    'Check return code -- 0 = fail; 1 = succeed
'    Select Case RtnCode
'    Case Is > 0 'Success
'        IndexCounter = rsAdjustments.RecordCount
'        With m_ForecastAdjust
'            ReDim .LcId(IndexCounter)
'            ReDim .Item(IndexCounter)
'            ReDim .AdjustQty(IndexCounter)
'            ReDim .AdjustType(IndexCounter)
'            ReDim .AdjustStartDate(IndexCounter)
'            ReDim .AdjustEndDate(IndexCounter)
'            ReDim .AdjustDesc(IndexCounter)
'            ReDim .AdjustUserID(IndexCounter)
'            ReDim .AdjustDateTime(IndexCounter)
'            ReDim .IsNew(IndexCounter)
'        End With
'        IndexCounter = 1
'        Do Until rsAdjustments.eof
'            With m_ForecastAdjust
'                .LcId(IndexCounter) = rsAdjustments!LcId
'                .Item(IndexCounter) = rsAdjustments!Item
'                .AdjustQty(IndexCounter) = rsAdjustments!AdjustQty
'                .AdjustType(IndexCounter) = rsAdjustments!AdjustType
'                .AdjustStartDate(IndexCounter) = rsAdjustments!AdjustStartDate
'                .AdjustEndDate(IndexCounter) = rsAdjustments!AdjustEndDate
'                .AdjustDesc(IndexCounter) = rsAdjustments!AdjustDesc
'                .AdjustUserID(IndexCounter) = rsAdjustments!AdjustUserID
'                .AdjustDateTime(IndexCounter) = rsAdjustments!AdjustDateTime
'                .IsNew(IndexCounter) = False
'            End With
'            IndexCounter = IndexCounter + 1
'            rsAdjustments.MoveNext
'        Loop
'
'    Case Else
'        Me.ckToggleAdjDisplay(0).Value = vbUnchecked
''        Me.ckToggleAdjDisplay(0).Enabled = False
'        Me.ckToggleAdjDisplay(1).Value = vbUnchecked
''        Me.ckToggleAdjDisplay(1).Enabled = False
'
'    End Select
'
'CleanUp:
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mShowAdjLog)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Saves current set of forecast quantities to AIMFcstStore* tables
'' .......................................................................
''   Returns false if the process failed
''   else true after saving to the database
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mSaveForecast( _
'    p_UserElement As Boolean _
'    , Optional p_FcstComment As String _
'    ) As Long
'On Error GoTo ErrorHandler
'
'    Dim ForecastCriteria As FORECAST_CRITERIA
'    Dim ForecastData As FORECAST_DATA
'    Dim IndexVar As Long
'    Dim IndexCounter As Long
'    Dim RowIdx As Long, ColIdx As Long
'    Dim RowCount As Long, ElementCount As Long
'    Dim TotalPeriods As Long
'    Dim BkMark As Variant
'    Dim PeriodStart As Date
'    Dim PeriodEnd As Date
'    Dim RtnCode As Long
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_ModifyDemandPlan)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    RtnCode = mSetForecastCriteria(ForecastCriteria)
'
'    Me.dgDetail.MoveLast
'
'    RowCount = Me.dgDetail.Rows
'    TotalPeriods = m_TotalPeriods
'
'    With ForecastData
'        ReDim .Item(RowCount) As String
'        ReDim .LcId(RowCount) As String
'        ReDim .PeriodStartDate(RowCount, TotalPeriods) As Date
'        ReDim .PeriodEndDate(RowCount, TotalPeriods) As Date
'        ReDim .DataCalc_Value(RowCount, TotalPeriods, DATA_CALC_TYPE.DCT_MAXVALUE) As Double
'    End With
'
'    RowIdx = 1
'    ColIdx = 1
'    Me.dgDetail.MoveFirst
'    BkMark = Me.dgDetail.bookmark
'
''    RtnCode = gSave_ForecastStore(Cn, p_UserElement, ForecastCriteria.FcstSetupKey, _
''                CStr(ForecastCriteria.FcstSetupKey), _
''                ForecastCriteria, ForecastData, p_FcstComment)
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mSaveForecast)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Sets the ForecastCriteria type-variable with values on screen.
''   These may be different from those stored in AIMFcstSetup
'' .......................................................................
''   Returns false if the process failed
''   else true after setting return variable.
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mSetForecastCriteria(r_ForecastCriteria As FORECAST_CRITERIA) As Long
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'    Dim tempInt As Long
'
'    r_ForecastCriteria.FcstSetupKey = m_FcstSetupKey
'    r_ForecastCriteria.FcstId = Me.dcForecastID.Text
'    r_ForecastCriteria.FcstStartDate = Format(m_FcstStartDate, g_ISO_DATE_FORMAT)
'    r_ForecastCriteria.FcstRolling = m_FcstRolling
'    r_ForecastCriteria.FcstPeriods_Future = txtFcstPeriods_Future.Value
'    r_ForecastCriteria.FcstHistory = m_FcstHistory
'    r_ForecastCriteria.FcstPeriods_Historical = txtFcstPeriods_Historical.Value
'    r_ForecastCriteria.ApplyTrend = m_ApplyTrend
'    For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
'        tempInt = IIf(optFcstInterval(IndexCounter).Value = True, IndexCounter, tempInt)
'    Next
'    r_ForecastCriteria.FcstInterval = tempInt
'    For IndexCounter = optFcstUnit.LBound To optFcstUnit.UBound
'        tempInt = IIf(optFcstUnit(IndexCounter).Value = True, IndexCounter, tempInt)
'    Next
'    r_ForecastCriteria.FcstUnit = tempInt
'    r_ForecastCriteria.Calc_SysFcst = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt) = vbChecked, True, False)
'    r_ForecastCriteria.Calc_Netreq = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt) = vbChecked, True, False)
'    r_ForecastCriteria.Calc_ProjInv = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt) = vbChecked, True, False)
'    r_ForecastCriteria.Calc_HistDmd = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_HstDmnd) = vbChecked, True, False)
'    r_ForecastCriteria.Calc_Netreq = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt) = vbChecked, True, False)
''    r_ForecastCriteria.Calc_NetRqmtAndPrdCons = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons) = vbChecked, True, False)
''    r_ForecastCriteria.Calc_PrjInvtAndPrdCons = IIf(Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons) = vbChecked, True, False)
'    r_ForecastCriteria.Item = txtItem.Text
'    r_ForecastCriteria.ItStat = txtItemStatus.Text
'    r_ForecastCriteria.VnId = txtVnId.Text
'    r_ForecastCriteria.Assort = txtAssort.Text
'    r_ForecastCriteria.ById = txtById.Text
'    r_ForecastCriteria.Class1 = txtClass(1).Text
'    r_ForecastCriteria.Class2 = txtClass(2).Text
'    r_ForecastCriteria.Class3 = txtClass(3).Text
'    r_ForecastCriteria.Class4 = txtClass(4).Text
'    r_ForecastCriteria.LcId = txtLcId.Text
'    r_ForecastCriteria.LStatus = txtLStatus.Text
'    r_ForecastCriteria.LDivision = txtLDivision.Text
'    r_ForecastCriteria.LRegion = txtLRegion.Text
'    r_ForecastCriteria.LUserDefined = txtLUserDefined.Text
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mSetr_ForecastCriteria)"
'End Function
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Opens modal dialog for updating item filters
'' .......................................................................
''   Returns false if the process failed
''   else true after closing modal dialog
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Sub ShowItemFilter()
'On Error GoTo ErrorHandler
'
'    Dim arrItems As XArrayDB
'
'    Dim RowCounter As Long
'    Dim ColCounter As Long
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
'    If HasAccess = False Then
'        'Message here
'        Exit Sub
'    End If
'
'    With m_FcstItemFilter
'        'Set subfilter parameters to existing fields
'        .SubFilterItem = Me.txtItem.Text
'        .SubFilterItemStatus = Me.txtItemStatus.Text
'        .SubFilterVnId = Me.txtVnId.Text
'        .SubFilterAssort = Me.txtAssort.Text
'        .SubFilterLcID = Me.txtLcId.Text
'        .SubFilterById = Me.txtById.Text
'        .SubFilterClass1 = Me.txtClass(1).Text
'        .SubFilterClass2 = Me.txtClass(2).Text
'        .SubFilterClass3 = Me.txtClass(3).Text
'        .SubFilterClass4 = Me.txtClass(4).Text
'        .SubFilterLDivision = Me.txtLDivision.Text
'        .SubFilterLRegion = Me.txtLRegion.Text
'        .SubFilterLStatus = Me.txtLStatus.Text
'        .SubFilterLUserDefined = Me.txtLUserDefined.Text
'
'        CallItemFilter Cn, m_FcstItemFilter, True, arrItems
'
'        'Set display to returned values
'        Me.txtItem.Text = .SubFilterItem
'        Me.txtItemStatus.Text = .SubFilterItemStatus
'        Me.txtVnId.Text = .SubFilterVnId
'        Me.txtAssort.Text = .SubFilterAssort
'        Me.txtLcId.Text = .SubFilterLcID
'        Me.txtById.Text = .SubFilterById
'        Me.txtClass(1).Text = .SubFilterClass1
'        Me.txtClass(2).Text = .SubFilterClass2
'        Me.txtClass(3).Text = .SubFilterClass3
'        Me.txtClass(4).Text = .SubFilterClass4
'        Me.txtLDivision.Text = .SubFilterLDivision
'        Me.txtLRegion.Text = .SubFilterLRegion
'        Me.txtLStatus.Text = .SubFilterLStatus
'        Me.txtLUserDefined.Text = .SubFilterLUserDefined
'    End With
'
'    Set arrItems = New XArrayDB
'
'    'Don't use arritem here, so discard it
'    Set arrItems = Nothing
'
'Exit Sub
'ErrorHandler:
'    Set arrItems = Nothing
'    If Err.Number = 9 Then
'        'The array is empty, exit sub
'        Exit Sub
'    Else
'        Err.Raise Err.Number, Err.source, Err.Description & "(ShowItemFilter)"
'    End If
'End Sub
'
'
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Updates the AIMFcstMaster table using all available adjustments for the given FcstID/FcstSetupKey
''   that have a current status of IsPromoted = False (0)
'' .......................................................................
''   Returns false if the process failed
''   else true after saving to the database.
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'Private Function mPromoteToMaster() As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanAdjust_PromoteToMaster_Sp As ADODB.Command
'    Dim HasAccess As Boolean
'    'Check access
'    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Promote)
'    If HasAccess = False Then
'        'Message here
'        Exit Function
'    End If
'
'    'Init. the command object
'    Set AIM_DmdPlanAdjust_PromoteToMaster_Sp = New ADODB.Command
'    With AIM_DmdPlanAdjust_PromoteToMaster_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanAdjust_PromoteToMaster_Sp"
'
'    'Set parameters and their values
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
'
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@AdjustType", adTinyInt, adParamInput)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@AdjustQty", adDecimal, adParamInput)
'        .Parameters("@AdjustQty").Precision = 10
'        .Parameters("@AdjustQty").NumericScale = 2
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@AdjustStartDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@AdjustEndDate", adDBDate, adParamInput)
'        .Parameters.Append AIM_DmdPlanAdjust_PromoteToMaster_Sp.CreateParameter("@AdjustUserID", adVarWChar, adParamInput, 12)
'    'Set values
'        .Parameters("@UserID").Value = gUserID
'        .Parameters("@FcstSetupKey").Value = m_FcstSetupKey
'        .Parameters("@FcstStoreKey").Value = m_FcstStoreKey_Main
'        .Parameters("@LcID").Value = Null   'To be used when doing selective promotions
'        .Parameters("@Item").Value = Null   'To be used when doing selective promotions
'        .Parameters("@AdjustType").Value = Null   'To be used when doing selective promotions
'        .Parameters("@AdjustQty").Value = Null   'To be used when doing selective promotions
'        .Parameters("@AdjustStartDate").Value = Null   'To be used when doing selective promotions
'        .Parameters("@AdjustEndDate").Value = Null   'To be used when doing selective promotions
'        .Parameters("@AdjustUserID").Value = Null   'To be used when doing selective promotions
'    'Fetch results
'        .Execute
'    End With
'
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_DmdPlanAdjust_PromoteToMaster_Sp.Parameters(0).Value
'
'    'Set return value
'    mPromoteToMaster = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlanAdjust_PromoteToMaster_Sp Is Nothing) Then Set AIM_DmdPlanAdjust_PromoteToMaster_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanAdjust_PromoteToMaster_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_DmdPlanAdjust_PromoteToMaster_Sp Is Nothing) Then Set AIM_DmdPlanAdjust_PromoteToMaster_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanAdjust_PromoteToMaster_Sp = Nothing
'    Err.Raise Err.Number, Err.source, Err.Description & "(mPromoteToMaster)"
'End Function
'
'Private Sub mToggleToolbarAccess()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim rsAccess As ADODB.Recordset 'This is initialized and returned in the gFetch_ForecastAccess procedure
'    Dim EBFcstMod As SSActiveToolBars
'    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
'
'    Set EBFcstMod = Me.tbFcstModification
'    EBFcstMod.Redraw = False
'
'    'Start with a default of no access
'    EBFcstMod.Tools(TB_ID_LOAD).Enabled = False
'    EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = False
'    EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = False
'    EBFcstMod.Tools(TB_ID_COPY).Enabled = False
'    EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = False
'    EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = False
'    EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = False
'    EBFcstMod.Tools(TB_ID_PROMOTE).Enabled = False
'
'    'Get Access Type
'    RtnCode = gFetch_ForecastAccess(Cn, m_FcstSetupKey, rsAccess, gUserID)
'
'    'Enable based on permissions
'    If RtnCode = 1 Then
'        rsAccess.MoveFirst
'        Do Until rsAccess.eof
'            Select Case CLng(rsAccess!AccessCode)
'            Case FCST_ACCESS.CODE_FullControl
'                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
'                EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = True
'                EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = True
'                EBFcstMod.Tools(TB_ID_COPY).Enabled = True
'                EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = True
'                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
'                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
'                EBFcstMod.Tools(TB_ID_PROMOTE).Enabled = True
'
'                'All done, here. Exit
'                Exit Do
'            Case FCST_ACCESS.CODE_ModifyForecastSetup     '1
'                'Not relevant to the demand planner, ignore
'            Case FCST_ACCESS.CODE_ModifyDemandPlan     '2
'                'All except Promote
'                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
'                EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = True
'                EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = True
'                EBFcstMod.Tools(TB_ID_COPY).Enabled = True
'                EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = True
'                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
'                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
'            Case FCST_ACCESS.CODE_Read     '3
'                'Limited access
'                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
'                EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = False      'NOT ALLOWED
'                EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = False      'NOT ALLOWED
'                EBFcstMod.Tools(TB_ID_COPY).Enabled = False      'NOT ALLOWED
'                EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = False      'NOT ALLOWED
'                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
'                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
'            Case FCST_ACCESS.CODE_ControlAccess     '4
'                'Not relevant to the demand planner, ignore
'            Case FCST_ACCESS.CODE_Promote     '5
'                'Read and promote
'                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
'                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
'                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
'                EBFcstMod.Tools(TB_ID_PROMOTE).Enabled = True
'            End Select
'            rsAccess.MoveNext
'        Loop
'    End If
'
'    EBFcstMod.Redraw = True
'    Set EBFcstMod = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsAccess) Then rsAccess.Close
'    Set rsAccess = Nothing
'
'Exit Sub
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.source
'    ErrDesc = Err.Description
'
'    EBFcstMod.Redraw = True
'    Set EBFcstMod = Nothing
'    If f_IsRecordsetValidAndOpen(rsAccess) Then rsAccess.Close
'    Set rsAccess = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mToggleToolbarAccess)"
'
'End Sub
'
'Private Function mGridQueryBuilder( _
'    p_Selectedtab As String _
'    , Optional p_ShowAdjustments As Boolean _
') As String
'On Error GoTo ErrorHandler
'
'    Dim QueryText As String, UnionQueryText As String
'    Dim FieldList As String, FieldList_Detail As String
'    Dim PeriodFields As String, PickDataCalcTypes As String
'    Dim DynWhere As String
'    Dim GivenPeriodStart As Date, GivenPeriodEnd As Date
'    Dim IndexCounter As Long
'
'    'Update form variables to reflect date ranges
'    mSetPeriodBounds
'    If Trim$(m_GroupByFieldName) = "" Then m_GroupByFieldName = GB_LCID
'    ReDim m_PeriodCaption(m_TotalPeriods) 'We want the grid to show the periods selected for viewing, with nulls if the period count is lesser
'
'    'Concatenate PivotTable QueryText
'    FieldList = ""
'    FieldList_Detail = ""
'    PeriodFields = ""
'    QueryText = ""
'    PickDataCalcTypes = ""
'
'    FieldList = vbCrLf & vbTab & "AIMFcstStore.FcstStoreKey,"
'    Select Case p_Selectedtab
'    Case TAB_SUMMARY
'        FieldList = FieldList & vbCrLf & vbTab & Trim$(m_GroupByFieldName)
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type"
'        FieldList = FieldList & ", " & "'DataCalcDesc' = CODE_DCT.CodeDesc"
'    Case TAB_DETAIL
'        FieldList = FieldList & vbCrLf & vbTab & "AIMFcstStore.FcstStoreID"
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "AIMFcstStoreDetail.LcID"
'        FieldList = FieldList & ", " & "AIMFcstStoreDetail.Item"
'        FieldList = FieldList & ", " & "Item.ItDesc"
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type"
'        FieldList = FieldList & ", " & "'DataCalcDesc' = CODE_DCT.CodeDesc"
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "'AdjustType' = NULL"
'        FieldList = FieldList & ", " & "'AdjustTypeDesc' = ''"
'
'        FieldList_Detail = FieldList_Detail & vbCrLf & vbTab & ", " & "Item.ItStat"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.Class1"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.Class2"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.Class3"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.Class4"
'        FieldList_Detail = FieldList_Detail & vbCrLf & vbTab & ", " & "Item.VelCode"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.VnID"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.Assort"
'        FieldList_Detail = FieldList_Detail & ", " & "Item.ByID"
'        FieldList_Detail = FieldList_Detail & vbCrLf & vbTab & ", " & "AIMLocations.LStatus"
'        FieldList_Detail = FieldList_Detail & ", " & "AIMLocations.LDivision"
'        FieldList_Detail = FieldList_Detail & ", " & "AIMLocations.LRegion"
'        FieldList_Detail = FieldList_Detail & ", " & "AIMLocations.LUserDefined"
'    End Select
'
'    'Spread periods into columns, for given range
'    GivenPeriodStart = m_FirstHistoricalPeriod
'    GivenPeriodEnd = Format(DateAdd(Trim$(m_DateIncrement), m_TotalPeriods, GivenPeriodStart), g_ISO_DATE_FORMAT)
'    PeriodFields = PeriodFields & vbCrLf & vbTab & ", 'Totals' = SUM (CASE WHEN"
'    PeriodFields = PeriodFields & vbCrLf & vbTab & "AIMFcstStoreDetail.PeriodStartDate >= '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & "'"
'    PeriodFields = PeriodFields & vbCrLf & vbTab & "AND AIMFcstStoreDetail.PeriodEndDate < '" & Format(GivenPeriodEnd, g_ISO_DATE_FORMAT) & "'"
'    PeriodFields = PeriodFields & vbCrLf & vbTab & "THEN DataCalc_Value ELSE 0 END)"
'    For IndexCounter = 1 To m_TotalPeriods
'        'Determine upper limit
'        GivenPeriodEnd = Format(DateAdd(Trim$(m_DateIncrement), 1#, GivenPeriodStart), g_ISO_DATE_FORMAT)
'        'Formulate query clause
'        PeriodFields = PeriodFields & vbCrLf & vbTab & ", '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & _
'            "' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & "'" & _
'            " AND AIMFcstStoreDetail.PeriodEndDate < '" & Format(GivenPeriodEnd, g_ISO_DATE_FORMAT) & "'"
'        PeriodFields = PeriodFields & "THEN DataCalc_Value ELSE 0 END)"
'
'        'Set column caption for use with the GUI
'        m_PeriodCaption(IndexCounter) = GivenPeriodStart
'
'        'Update the period for the next iteration
'        GivenPeriodStart = Format(DateAdd(Trim$(m_DateIncrement), 1#, GivenPeriodStart), g_ISO_DATE_FORMAT)
'    Next IndexCounter
'
'    'Filter elements by selected values
'    PickDataCalcTypes = ""
'    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = vbChecked Then
'        PickDataCalcTypes = IIf(Trim$(PickDataCalcTypes) = "", _
'                        " DataCalc_Type IN (" & DATA_CALC_TYPE.DCT_SysRqmt, _
'                        PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_SysRqmt)
'        PickDataCalcTypes = PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_AdjSysRqmt
'    End If
'    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt).Value = vbChecked Then
'        PickDataCalcTypes = IIf(Trim$(PickDataCalcTypes) = "", _
'                    " DataCalc_Type IN (" & DATA_CALC_TYPE.DCT_NetRqmt, _
'                    PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_NetRqmt)
'        PickDataCalcTypes = PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_AdjNetRqmt
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt).Value = vbChecked Then
'            PickDataCalcTypes = PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_NetRqmtAndPlnRcpt
'        End If
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons).Value = vbChecked Then
'            PickDataCalcTypes = PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_NetRqmtAndPrdCons
'        End If
'    End If
'    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_HstDmnd).Value = vbChecked Then
'        PickDataCalcTypes = IIf(Trim$(PickDataCalcTypes) = "", _
'                        " DataCalc_Type IN (" & DATA_CALC_TYPE.DCT_HstDmnd, _
'                        PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_HstDmnd)
'    End If
'    If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvt).Value = vbChecked Then
'        PickDataCalcTypes = IIf(Trim$(PickDataCalcTypes) = "", _
'                        " DataCalc_Type IN (" & DATA_CALC_TYPE.DCT_PrjInvt, _
'                        PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_PrjInvt)
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons).Value = vbChecked Then
'            PickDataCalcTypes = PickDataCalcTypes & ", " & DATA_CALC_TYPE.DCT_PrjInvtAndPrdCons
'        End If
'        PickDataCalcTypes = IIf(Trim$(PickDataCalcTypes) = "", " DataCalc_Type IN (", PickDataCalcTypes & ")")
'    End If
'    If Trim$(PickDataCalcTypes) <> "" Then PickDataCalcTypes = PickDataCalcTypes & " )"
'
'    'Start building the query text
'
'    Select Case p_Selectedtab
'    Case TAB_SUMMARY
'        QueryText = "SELECT " & FieldList & PeriodFields
'    Case TAB_DETAIL
'        QueryText = "SELECT " & FieldList & PeriodFields & FieldList_Detail
'    End Select
'
'    QueryText = QueryText & vbCrLf & "FROM AIMFcstStore"
'    QueryText = QueryText & vbCrLf & "INNER JOIN AIMFcstStoreDetail"
'    QueryText = QueryText & " ON AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey"
'    QueryText = QueryText & vbCrLf & "INNER JOIN ITEM"
'    QueryText = QueryText & " ON AIMFcstStoreDetail.Item = Item.Item AND AIMFcstStoreDetail.LcId = Item.LcId"
'    QueryText = QueryText & vbCrLf & "INNER JOIN AIMLocations"
'    QueryText = QueryText & " ON AIMFcstStoreDetail.LcId = AIMLocations.LcID"
'    QueryText = QueryText & vbCrLf & "INNER JOIN AIMCodeLookup CODE_DCT"
'    QueryText = QueryText & " ON AIMFcstStoreDetail.DataCalc_Type = CODE_DCT.CodeID"
'
'    QueryText = QueryText & vbCrLf & "WHERE AIMFcstStore.FcstSetupKey = " & CStr(m_FcstSetupKey)
'    QueryText = QueryText & vbCrLf & vbTab & "AND AIMFcstStore.UserElement = 0"
'    QueryText = QueryText & vbCrLf & vbTab & "AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')"
'    QueryText = QueryText & vbCrLf & vbTab & "AND AIMFcstStoreDetail.PeriodStartDate >= " & _
'        IIf(Trim$(m_FirstHistoricalPeriod) = "12:00:00 AM", "AIMFcstStoreDetail.PeriodStartDate", "'" & Format(m_FirstHistoricalPeriod, g_ISO_DATE_FORMAT) & "'")
'    QueryText = QueryText & vbCrLf & vbTab & "AND AIMFcstStoreDetail.PeriodEndDate <= " & _
'        IIf(Trim$(m_LastPeriod) = "12:00:00 AM", "AIMFcstStoreDetail.PeriodEndDate", "'" & Format(m_LastPeriod, g_ISO_DATE_FORMAT) & "'")
'    QueryText = QueryText & vbCrLf & vbTab & "AND CODE_DCT.LangID = '" & Trim$(gLangID) & "' AND CODE_DCT.CodeType = 'DATACALCTYPE'"
'
'    DynWhere = DynWhere & IIf(Trim$(Me.txtItem.Text) <> "", _
'                vbCrLf & vbTab & "AND Item.Item = N'" & Trim$(Me.txtItem.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtAssort.Text) <> "", _
'                vbCrLf & vbTab & "AND Item.Assort = N'" & Trim$(Me.txtAssort.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtLcId.Text) <> "", _
'                vbCrLf & vbTab & "AND Item.LcID = N'" & Trim$(Me.txtLcId.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtVnId.Text) <> "", _
'                vbCrLf & vbTab & "AND Item.VnId = N'" & Trim$(Me.txtVnId.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtItemStatus.Text) <> "", _
'                vbCrLf & vbTab & "AND Item.ItStat = N'" & Trim$(Me.txtItemStatus.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtClass(1).Text) <> "", _
'                vbCrLf & vbTab & "AND Item.Class1 = N'" & Trim$(Me.txtClass(1).Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtClass(2).Text) <> "", _
'                vbCrLf & vbTab & "AND Item.Class2 = N'" & Trim$(Me.txtClass(2).Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtClass(3).Text) <> "", _
'                vbCrLf & vbTab & "AND Item.Class3 = N'" & Trim$(Me.txtClass(3).Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtClass(4).Text) <> "", _
'                vbCrLf & vbTab & "AND Item.Class4 = N'" & Trim$(Me.txtClass(4).Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtById.Text) <> "", _
'                vbCrLf & vbTab & "AND Item.ByID = N'" & Trim$(Me.txtById.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtLStatus.Text) <> "", _
'                vbCrLf & vbTab & "AND AIMLocations.LStatus = N'" & Trim$(Me.txtLStatus.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtLDivision.Text) <> "", _
'                vbCrLf & vbTab & "AND AIMLocations.LDivision = N'" & Trim$(Me.txtLDivision.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtLRegion.Text) <> "", _
'                vbCrLf & vbTab & "AND AIMLocations.LRegion = N'" & Trim$(Me.txtLRegion.Text) & "'", "")
'    DynWhere = DynWhere & IIf(Trim$(Me.txtLUserDefined.Text) <> "", _
'                vbCrLf & vbTab & "AND AIMLocations.LUserDefined = N'" & Trim$(Me.txtLUserDefined.Text) & "'", "")
'
'    QueryText = QueryText & vbCrLf & vbTab & "AND " & PickDataCalcTypes
'    QueryText = QueryText & DynWhere
'
'    Select Case p_Selectedtab
'    Case TAB_SUMMARY
'        QueryText = QueryText & vbCrLf & "GROUP BY "
'        QueryText = QueryText & vbCrLf & vbTab & "AIMFcstStore.FcstStoreKey, " & Trim$(m_GroupByFieldName)
'        QueryText = QueryText & ", " & "AIMFcstStoreDetail.DataCalc_Type"
'        QueryText = QueryText & ", " & "CODE_DCT.CodeDesc"
'
'    Case TAB_DETAIL
'        QueryText = QueryText & vbCrLf & "GROUP BY "
'        QueryText = QueryText & vbCrLf & vbTab & "AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID"
'        QueryText = QueryText & ", " & "AIMFcstStoreDetail.LcID"
'        QueryText = QueryText & ", " & "AIMFcstStoreDetail.Item"
'        QueryText = QueryText & vbCrLf & vbTab & ", " & "Item.ItDesc"
'        QueryText = QueryText & ", " & "Item.ItStat"
'        QueryText = QueryText & vbCrLf & vbTab & ", " & "AIMFcstStoreDetail.DataCalc_Type"
'        QueryText = QueryText & ", " & "CODE_DCT.CodeDesc"
'        QueryText = QueryText & FieldList_Detail
'    End Select
'
'    If p_ShowAdjustments Then
'        FieldList = ""
'        FieldList_Detail = ""
'        PeriodFields = ""
'        PickDataCalcTypes = ""
'
'        FieldList = FieldList & vbCrLf & vbTab & "AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID"
'        FieldList = FieldList & ", " & "AIMFcstStoreDetail.LcID"
'        FieldList = FieldList & ", " & "AIMFcstStoreDetail.Item"
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "''"        '"Item.ItDesc"
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type"
'        FieldList = FieldList & ", " & "'DataCalcDesc' = CODE_DCT.CodeDesc"
'        FieldList = FieldList & vbCrLf & vbTab & ", " & "'AdjustType' = VW_Adj.AdjustType"
'        FieldList = FieldList & ", " & "'AdjustTypeDesc' = CODE_AT.CodeDesc"
'
'        'Spread periods into columns, for given range
'        GivenPeriodStart = m_FirstHistoricalPeriod
'        GivenPeriodEnd = Format(DateAdd(Trim$(m_DateIncrement), m_TotalPeriods, GivenPeriodStart), g_ISO_DATE_FORMAT)
'        PeriodFields = ", 'Totals' = CASE WHEN AdjustType <> 0 THEN"
'        PeriodFields = PeriodFields & vbCrLf & vbTab & vbTab & _
'            "SUM(CASE WHEN VW_Adj.AdjustStartDate >= '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & "'" & _
'            " AND VW_Adj.AdjustEndDate < '" & Format(GivenPeriodEnd, g_ISO_DATE_FORMAT) & "'" & _
'            " THEN AdjustQty ELSE 0 END)"
'        PeriodFields = PeriodFields & vbCrLf & vbTab & _
'            "ELSE MAX(CASE WHEN VW_Adj.AdjustStartDate >= '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & "'" & _
'            " AND VW_Adj.AdjustEndDate < '" & Format(GivenPeriodEnd, g_ISO_DATE_FORMAT) & "'" & _
'            " THEN AdjustQty ELSE 0 END)"
'        PeriodFields = PeriodFields & vbCrLf & vbTab & "END"
'
'        For IndexCounter = 1 To m_TotalPeriods
'            'Determine upper limit
'            GivenPeriodEnd = Format(DateAdd(Trim$(m_DateIncrement), 1#, GivenPeriodStart), g_ISO_DATE_FORMAT)
'            'Formulate query clause
'            PeriodFields = PeriodFields & vbCrLf & vbTab & ", '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & _
'                "' = CASE WHEN AdjustType <> 0 THEN "
'            PeriodFields = PeriodFields & vbCrLf & vbTab & vbTab & _
'                "SUM (CASE WHEN VW_Adj.AdjustStartDate >= '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & "'" & _
'                " AND VW_Adj.AdjustEndDate < '" & Format(GivenPeriodEnd, g_ISO_DATE_FORMAT) & "'" & _
'                " THEN VW_Adj.AdjustQty ELSE 0 END)"
'            PeriodFields = PeriodFields & vbCrLf & vbTab & _
'                "ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '" & Format(GivenPeriodStart, g_ISO_DATE_FORMAT) & "'" & _
'                " AND VW_Adj.AdjustEndDate < '" & Format(GivenPeriodEnd, g_ISO_DATE_FORMAT) & "'" & _
'                " THEN VW_Adj.AdjustQty  ELSE 0 END)"
'            PeriodFields = PeriodFields & vbCrLf & vbTab & "END"
'
'            'Set column caption for use with the GUI
'            m_PeriodCaption(IndexCounter) = GivenPeriodStart
'
'            'Update the period for the next iteration
'            GivenPeriodStart = Format(DateAdd(Trim$(m_DateIncrement), 1#, GivenPeriodStart), g_ISO_DATE_FORMAT)
'        Next IndexCounter
'
'        FieldList_Detail = FieldList_Detail & vbCrLf & vbTab & ", '', '', '', '', '', '', '', '', '', '', '', '', ''"
''        , Item.ItStat, Item.Class1, Item.Class2, Item.Class3, Item.Class4, Item.VelCode, Item.VnID, Item.Assort, Item.ByID
''        , AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined
'
'        UnionQueryText = "UNION ALL" & vbCrLf & "SELECT " & FieldList & PeriodFields & FieldList_Detail
'        UnionQueryText = UnionQueryText & vbCrLf & "FROM AIMFcstStore"
'        UnionQueryText = UnionQueryText & vbCrLf & "INNER JOIN AIMFcstStoreDetail"
'        UnionQueryText = UnionQueryText & " ON AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey"
'        UnionQueryText = UnionQueryText & vbCrLf & "INNER JOIN AIM_DmdPlan_Adjustments VW_Adj"
'        UnionQueryText = UnionQueryText & " ON AIMFcstStoreDetail.FcstStoreKey = VW_Adj.FcstStoreKey"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND AIMFcstStoreDetail.LcID = VW_Adj.LcID"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND AIMFcstStoreDetail.Item = VW_Adj.Item"
'        UnionQueryText = UnionQueryText & vbCrLf & "INNER JOIN ITEM"
'        UnionQueryText = UnionQueryText & " ON AIMFcstStoreDetail.Item = Item.Item AND AIMFcstStoreDetail.LcId = Item.LcId"
'        UnionQueryText = UnionQueryText & vbCrLf & "INNER JOIN AIMLocations"
'        UnionQueryText = UnionQueryText & " ON AIMFcstStoreDetail.LcId = AIMLocations.LcID"
'        UnionQueryText = UnionQueryText & vbCrLf & "INNER JOIN AIMCodeLookup CODE_DCT"
'        UnionQueryText = UnionQueryText & " ON AIMFcstStoreDetail.DataCalc_Type = CODE_DCT.CodeID"
'        UnionQueryText = UnionQueryText & vbCrLf & "INNER JOIN AIMCodeLookup CODE_AT"
'        UnionQueryText = UnionQueryText & " ON VW_Adj.AdjustType = CODE_AT.CodeID"
'
'        UnionQueryText = UnionQueryText & vbCrLf & "WHERE AIMFcstStore.FcstSetupKey = " & CStr(m_FcstSetupKey)
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND AIMFcstStore.UserElement = 0"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND AIMFcstStoreDetail.PeriodStartDate >= " & _
'           IIf(Trim$(m_FirstHistoricalPeriod) = "12:00:00 AM", "AIMFcstStoreDetail.PeriodStartDate", "'" & Format(m_FirstHistoricalPeriod, g_ISO_DATE_FORMAT) & "'")
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND AIMFcstStoreDetail.PeriodEndDate <= " & _
'           IIf(Trim$(m_LastPeriod) = "12:00:00 AM", "AIMFcstStoreDetail.PeriodEndDate", "'" & Format(m_LastPeriod, g_ISO_DATE_FORMAT) & "'")
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND VW_Adj.AdjustStartDate >= " & _
'           IIf(Trim$(m_FirstHistoricalPeriod) = "12:00:00 AM", "VW_Adj.AdjustStartDate", "'" & Format(m_FirstHistoricalPeriod, g_ISO_DATE_FORMAT) & "'")
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND VW_Adj.AdjustEndDate <= " & _
'           IIf(Trim$(m_LastPeriod) = "12:00:00 AM", "VW_Adj.AdjustEndDate", "'" & Format(m_LastPeriod, g_ISO_DATE_FORMAT) & "'")
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND CODE_DCT.LangID = '" & Trim$(gLangID) & "' AND CODE_DCT.CodeType = '" & g_CODETYPE_DATACALCTYPE & "'"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND CODE_AT.LangID = '" & Trim$(gLangID) & "' AND CODE_AT.CodeType = '" & g_CODETYPE_ADJUSTTYPE & "'"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND VW_Adj.EditStamp = CASE WHEN VW_Adj.AdjustType = 0 THEN (SELECT MAX(VW_Adj_Time.EditStamp)"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "FROM AIM_DmdPlan_Adjustments VW_Adj_Time"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "WHERE VW_Adj_Time.FcstStoreKey = VW_Adj.FcstStoreKey"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "AND VW_Adj_Time.LcID = VW_Adj.LcID"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "AND VW_Adj_Time.Item = VW_Adj.Item"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "AND VW_Adj_Time.AdjustStartDate = VW_Adj.AdjustStartDate"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "AND VW_Adj_Time.AdjustEndDate = VW_Adj.AdjustEndDate"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & vbTab & "AND VW_Adj_Time.AdjustType = 0)"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "ELSE VW_Adj.EditStamp END"
'
'        'Show Adjustments only if either sys or net requirements is selected.
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_SysRqmt).Value = vbChecked Then
'            PickDataCalcTypes = " DataCalc_Type = " & DATA_CALC_TYPE.DCT_AdjSysRqmt
'        End If
'        If Me.ckDataCalc_Type(DATA_CALC_TYPE.DCT_NetRqmt).Value = vbChecked Then
'            PickDataCalcTypes = IIf(Trim$(PickDataCalcTypes) = "", _
'                        " DataCalc_Type = " & DATA_CALC_TYPE.DCT_AdjNetRqmt, _
'                        PickDataCalcTypes)
'        End If
'
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AND " & PickDataCalcTypes
'        UnionQueryText = UnionQueryText & DynWhere
'
'        UnionQueryText = UnionQueryText & vbCrLf & "GROUP BY "
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID"
'        UnionQueryText = UnionQueryText & ", " & "AIMFcstStoreDetail.LcID"
'        UnionQueryText = UnionQueryText & ", " & "AIMFcstStoreDetail.Item"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & ", " & "AIMFcstStoreDetail.DataCalc_Type"
'        UnionQueryText = UnionQueryText & ", " & "VW_Adj.AdjustType"
'        UnionQueryText = UnionQueryText & ", " & "CODE_DCT.CodeDesc"
'        UnionQueryText = UnionQueryText & ", " & "CODE_AT.CodeDesc"
'        UnionQueryText = UnionQueryText & vbCrLf & "ORDER BY"
'        UnionQueryText = UnionQueryText & vbCrLf & vbTab & "AIMFcstStore.FcstStoreID"
'        UnionQueryText = UnionQueryText & ", " & "AIMFcstStoreDetail.LcID"
'        UnionQueryText = UnionQueryText & ", " & "AIMFcstStoreDetail.Item"
'        UnionQueryText = UnionQueryText & ", " & "AIMFcstStoreDetail.DataCalc_Type"
'        UnionQueryText = UnionQueryText & ", " & "VW_Adj.AdjustType"
'
'        mGridQueryBuilder = QueryText & vbCrLf & UnionQueryText
'        Exit Function
'    End If
'
'    'Return string -- for adjustments, (see above ^^^) the function should have returned from within the if condition.
'    mGridQueryBuilder = QueryText
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(mPromoteToMaster)"
'End Function
'
Private Sub atModification_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)

End Sub

Private Sub fraAdj_Smr_DragDrop(Source As Control, X As Single, Y As Single)

End Sub


