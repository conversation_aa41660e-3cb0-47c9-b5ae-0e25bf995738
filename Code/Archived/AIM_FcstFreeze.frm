VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_FcstFreeze 
   Caption         =   "SSA AIM Forecast Freeze Criteria"
   ClientHeight    =   4920
   ClientLeft      =   165
   ClientTop       =   735
   ClientWidth     =   8460
   LinkTopic       =   "Form1"
   ScaleHeight     =   4920
   ScaleWidth      =   8460
   StartUpPosition =   3  'Windows Default
   Begin VB.Frame frmFreezeEdit 
      Caption         =   "Edit Criterion"
      Height          =   1410
      Left            =   120
      TabIndex        =   1
      Top             =   2970
      Width           =   8325
      Begin VB.CommandButton cmdItemFilter 
         Height          =   310
         Left            =   5205
         MouseIcon       =   "AIM_FcstFreeze.frx":0000
         MousePointer    =   99  'Custom
         Picture         =   "AIM_FcstFreeze.frx":0152
         Style           =   1  'Graphical
         TabIndex        =   8
         Top             =   977
         Width           =   310
      End
      Begin VB.CommandButton cmdLocationLookup 
         Height          =   310
         Left            =   5205
         MouseIcon       =   "AIM_FcstFreeze.frx":029C
         MousePointer    =   99  'Custom
         Picture         =   "AIM_FcstFreeze.frx":03EE
         Style           =   1  'Graphical
         TabIndex        =   2
         Top             =   635
         Width           =   310
      End
      Begin TDBText6Ctl.TDBText txtLcId 
         Height          =   345
         Left            =   3030
         TabIndex        =   3
         Top             =   600
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_FcstFreeze.frx":0538
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_FcstFreeze.frx":05A4
         Key             =   "AIM_FcstFreeze.frx":05C2
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtItem 
         Height          =   345
         Left            =   3030
         TabIndex        =   7
         Top             =   960
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_FcstFreeze.frx":0606
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_FcstFreeze.frx":0672
         Key             =   "AIM_FcstFreeze.frx":0690
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtFreezePeriods 
         Height          =   345
         Left            =   3030
         TabIndex        =   9
         Top             =   960
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Calculator      =   "AIM_FcstFreeze.frx":06D4
         Caption         =   "AIM_FcstFreeze.frx":06F4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_FcstFreeze.frx":0760
         Keys            =   "AIM_FcstFreeze.frx":077E
         Spin            =   "AIM_FcstFreeze.frx":07C8
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   104
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFreezeType 
         Height          =   345
         Left            =   3030
         TabIndex        =   10
         Top             =   240
         Width           =   2100
         _Version        =   196617
         DataMode        =   2
         UseExactRowCount=   0   'False
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin VB.Label lblEditCrit 
         Caption         =   "Freeze Limit"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   2
         Left            =   120
         TabIndex        =   6
         Top             =   1005
         Width           =   2805
      End
      Begin VB.Label lblEditCrit 
         Caption         =   "Freeze Type"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   5
         Top             =   285
         Width           =   2805
      End
      Begin VB.Label lblEditCrit 
         Caption         =   "Location ID"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   0
         Left            =   120
         TabIndex        =   4
         Top             =   645
         Width           =   2805
      End
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgFreeze 
      Height          =   2715
      Left            =   75
      TabIndex        =   0
      Top             =   120
      Width           =   8325
      _Version        =   196617
      DataMode        =   2
      Cols            =   6
      Col.Count       =   6
      AllowUpdate     =   0   'False
      MaxSelectedRows =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   14684
      _ExtentY        =   4789
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   4440
      _ExtentX        =   582
      _ExtentY        =   582
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   14
      Style           =   0
      Tools           =   "AIM_FcstFreeze.frx":07F0
      ToolBars        =   "AIM_FcstFreeze.frx":B9B5
   End
   Begin VB.Menu mnuFcstFreeze 
      Caption         =   "Forecast Freeze Criteria"
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "&Add new"
         Index           =   0
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "&Edit current"
         Index           =   1
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "-"
         Index           =   2
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "&Delete current"
         Index           =   3
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "&Cancel updates"
         Index           =   4
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "-"
         Index           =   5
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "&Save updates"
         Index           =   6
      End
      Begin VB.Menu mnuFcstFreeze_Opt 
         Caption         =   "&Return"
         Index           =   7
      End
   End
End
Attribute VB_Name = "AIM_FcstFreeze"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
''*****************************************************************************
'' Copyright (c) 2004 SSA Global. All rights reserved.
''*****************************************************************************
''
''   AIM_FcstFreeze.frm
''
''   Version Number - 1.0
''   Last Updated   - 2004/02/24
''   Updated By     - Annalakshmi Stocksdale
''
''   This replaces the former AIM_ForecastMaintenance/Freeze tab
''       allowing creation and modification of forecast freeze criteria.
''
''   The "forecast generator/modification" process is being morphed
''   into demand planning, hence the options required for forecast generator
''   are being phased out, and additional options for demand planning
''   are to be created, as of version 4.5
''   See related updates to AIM_ForecastModification.
''
''*****************************************************************************
'' This file contains trade secrets of SSA Global. No part
'' may be reproduced or transmitted in any form by any means or for any purpose
'' without the express written permission of SSA Global.
''*****************************************************************************
'Option Explicit
'
'Public p_FcstSetupKey As Long
'Public p_FcstIntervalCaption As String
'
'Public p_Item As String
'Public p_ItemStatus As String
'Public p_VendorID As String
'Public p_Assortment As String
'Public p_LocationID As String
'Public p_BuyerID As String
'Public p_Class1 As String
'Public p_Class2 As String
'Public p_Class3 As String
'Public p_Class4 As String
'Public p_LStatus As String
'Public p_LDivision As String
'Public p_LRegion As String
'Public p_LUserDefined As String
'
'Dim m_Cn As ADODB.Connection
'
'Dim m_ColIdx_FcstFreezeKey As Integer
'Dim m_ColIdx_FcstSetupKey As Integer
'Dim m_ColIdx_FreezeType As Integer
'Dim m_ColIdx_LcID As Integer
'Dim m_ColIdx_FreezeLimit As Integer
'Dim m_ColIdx_ItemDesc As Integer
'
'Private Const MENU_ADD As Integer = 0
'Private Const MENU_EDIT As Integer = 1
'Private Const MENU_DELETE As Integer = 3
'Private Const MENU_CANCEL As Integer = 4
'Private Const MENU_SAVE As Integer = 6
'Private Const MENU_RETURN As Integer = 7
'
'Private Sub cmdItemFilter_Click()
'On Error GoTo ErrorHandler
'
'    Dim arrItems As XArrayDB
'
'    Dim RowCounter As Long
'    Dim ColCounter As Long
'    Dim FcstItemFilter As FCST_ITEM_FILTER
'
'    With FcstItemFilter
'        .Assort = p_Assortment
'        .ById = p_BuyerID
'        .Class1 = p_Class1
'        .Class2 = p_Class2
'        .Class3 = p_Class3
'        .Class4 = p_Class4
'        .Item = IIf(IsNull(p_Item) Or Trim$(p_Item) = "", Me.txtItem.Text, p_Item)
'        .ItemStatus = p_ItemStatus
'        .LcId = IIf(IsNull(p_LocationID) Or Trim$(p_LocationID) = "", Me.txtLcId.Text, p_LocationID)
'        .VnId = p_VendorID
'        .LDivision = p_LDivision
'        .LRegion = p_LRegion
'        .LStatus = p_LStatus
'        .LUserDefined = p_LUserDefined
'
'        CallItemFilter m_Cn, FcstItemFilter, True, arrItems
'
'        'Set display to returned values
'        txtItem.Text = .Item
'
'    End With
'
'    Set arrItems = Nothing
'
'Exit Sub
'ErrorHandler:
'    Set arrItems = Nothing
'    If Err.Number = 9 Then
'        'The array is empty, exit quietly
'        Exit Sub
'    Else
'        f_HandleErr Me.Caption & "(cmdItemFilter_Click)"
'    End If
'End Sub
'
'Private Sub cmdLocationLookup_Click()
'On Error GoTo ErrorHandler
'
'    Screen.MousePointer = vbHourglass
'
'    If Me.txtLcId.Text = "" Then
'        AIM_LocationLookUp.LcId = getTranslationResource("All")
'    Else
'        AIM_LocationLookUp.LcId = Me.txtLcId.Text
'    End If
'
'    Set AIM_LocationLookUp.Cn = m_Cn
'
'    AIM_LocationLookUp.Show vbModal
'
'    If Not AIM_LocationLookUp.CancelFlag Then
'        Me.txtLcId.Text = IIf(AIM_LocationLookUp.LcId = getTranslationResource("All"), "", AIM_LocationLookUp.LcId)
'    End If
'
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(cmdLocationLookup_Click)"
'End Sub
'
'Private Sub dcFreezeType_Change()
'On Error GoTo ErrorHandler
'
'    dcFreezeType.Redraw = False
'
'    If IsEmpty(dcFreezeType.Value) Then Exit Sub
'
'    Select Case CInt(dcFreezeType.Value)
'    Case 0  'Apply to locations
'        Me.txtFreezePeriods.Visible = True
'        Me.txtItem.Visible = False
'        Me.cmdItemFilter.Visible = False
'    Case 1  'Apply to items
'        Me.txtFreezePeriods.Visible = False
'        Me.txtItem.Visible = True
'        Me.cmdItemFilter.Visible = True
'    End Select
'
'    dcFreezeType.Redraw = True
'    frmFreezeEdit.Refresh
'
'Exit Sub
'ErrorHandler:
'    dcFreezeType.Redraw = True
'    f_HandleErr Me.Caption & "(dcFreezeType_Change)"
'End Sub
'
'Private Sub dcFreezeType_Click()
'On Error GoTo ErrorHandler
'
'    dcFreezeType_Change
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcFreezeType_Change)"
'End Sub
'
'Private Sub dcFreezeType_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim AddItemString As String
'    Dim IndexCounter As Integer
'
'    Me.dcFreezeType.Redraw = False
'
'    IndexCounter = 0
'    Me.dcFreezeType.AddItem IndexCounter
'    Me.dcFreezeType.Columns(IndexCounter).Caption = getTranslationResource("Freeze Type")
'    Me.dcFreezeType.Columns(IndexCounter).Width = 1500
'    Me.dcFreezeType.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcFreezeType.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    Me.dcFreezeType.Columns(IndexCounter).DataField = "FcstID"
'
'    IndexCounter = IndexCounter + 1
'    Me.dcFreezeType.AddItem IndexCounter
'    Me.dcFreezeType.Columns(IndexCounter).Caption = getTranslationResource("Description")
'    Me.dcFreezeType.Columns(IndexCounter).Width = 2880
'    Me.dcFreezeType.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcFreezeType.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'    Me.dcFreezeType.Columns(IndexCounter).DataField = "FcstDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcFreezeType, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcFreezeType.Columns.Count - 1
''        dcFreezeType.Columns(IndexCounter).HasHeadBackColor = True
''        dcFreezeType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'    Me.dcFreezeType.Redraw = True
'
'Exit Sub
'ErrorHandler:
'    Me.dcFreezeType.Redraw = True
'    f_HandleErr Me.Caption & "(dcFreezeType_InitColumnProps)"
'End Sub
'
'Private Sub dgFreeze_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
'On Error GoTo ErrorHandler
'
'    Select Case Button
'    Case vbRightButton
'        Me.PopupMenu Me.mnuFcstFreeze
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgFreeze_MouseDown)"
'End Sub
'
'Private Sub Form_Load()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim strMessage As String
'    Dim strMessage1 As String
'    Dim IndexCounter As Long
'
'    'Housekeeping
'    Screen.MousePointer = vbHourglass
''    strMessage = getTranslationResource("STATMSG06900")
''    If StrComp(strMessage, "STATMSG06900") = 0 Then strMessage = "Initializing Forecast Freeze..."""
''    Write_Message strMessage
'
'    GetTranslatedCaptions Me
'
'    'Open a connection to the Server
'    Set m_Cn = New ADODB.Connection
'    RtnCode = SQLConnection(m_Cn, CONNECTION_OPEN, True, , , Me.Caption)
'    If RtnCode <> SUCCEED Then Exit Sub
'
'    'Populate the dropdown(s)
'    RtnCode = mFetch_CodeLookups
'
'    'Make the spin button(s) visible
'    Me.txtFreezePeriods.Spin.Visible = dbiShowAlways
'
'    'Initialize other fields
'    Me.txtItem.Text = p_Item
'    Me.txtLcId.Text = p_LocationID
'
'    'Arrange the controls in the form
'    RtnCode = mForm_Rearrange(False)
'
'    RtnCode = mFcstFreeze_Fetch
'    'Windup
''    Write_Message ""
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Load)"
'End Sub
'
'Private Sub Form_Unload(Cancel As Integer)
'    Dim RtnCode As Long
'
''>>>
'    'Save pending updates, first
'    '...
'
'    'Destroy objects
'
'    'Destroy connection and object
'    RtnCode = SQLConnection(m_Cn, CONNECTION_CLOSE, False)
'    Set m_Cn = Nothing
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        'Ignore
'    Else
'        f_HandleErr Me.Caption & "(Form_Unload)"
'    End If
'    Resume Next
'End Sub
'
'Private Sub lblEditCrit_Click(Index As Integer)
'
'End Sub
'
'Private Sub mnuFcstFreeze_Opt_Click(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim p_ToolID As String
'    Dim BkMark As Variant
'    Dim FreezeKey As Integer
'    Dim FreezeType As Integer
'    Dim LcId As String
'    Dim FreezeLimit As String
'
'    Select Case Index
'    Case MENU_ADD, MENU_EDIT
'        p_ToolID = IIf(Index = MENU_ADD, "ID_ADDNEW", "ID_EDIT")
'        RtnCode = mSet_EditMode(p_ToolID)
'        Exit Sub
'
'    Case MENU_DELETE
'        BkMark = dgFreeze.bookmark
'        FreezeKey = dgFreeze.Columns(m_ColIdx_FcstFreezeKey).CellValue(BkMark)
'        RtnCode = mFcstFreeze_Delete(FreezeKey)
'    '>>> verify here
'
'    Case MENU_CANCEL
'        RtnCode = mFcstFreeze_Fetch
'        RtnCode = mForm_Rearrange(False)
'
'    Case MENU_SAVE
'        LcId = txtLcId.Text
'        FreezeType = dcFreezeType.Text
'        FreezeLimit = IIf(FreezeType = 0, txtFreezePeriods.Value, txtItem.Text)
'        RtnCode = mFcstFreeze_Save(FreezeType, LcId, FreezeLimit)
'        If RtnCode = 1 Then
'            RtnCode = mFcstFreeze_Fetch
'        End If
'
'    Case MENU_RETURN
''>>> Check for unsaved records
'        Unload Me
'        Exit Sub
'
'    End Select
'
'    Me.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(mnuFcstFreeze_Opt_Click)"
'End Sub
'
'Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim BoolReturn As Boolean
'
'    Select Case UCase$(Tool.ID)
'    Case "ID_CLOSE"
'        mnuFcstFreeze_Opt_Click (MENU_RETURN)
'
'    Case "ID_ADDNEW"
'        mnuFcstFreeze_Opt_Click (MENU_ADD)
'
'    Case "ID_EDIT"
'        mnuFcstFreeze_Opt_Click (MENU_EDIT)
'
'    Case "ID_DELETE"
'        mnuFcstFreeze_Opt_Click (MENU_DELETE)
'
'    Case "ID_CANCEL"
'        mnuFcstFreeze_Opt_Click (MENU_CANCEL)
'
'    Case "ID_SAVE"
'        mnuFcstFreeze_Opt_Click (MENU_SAVE)
'
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
'End Sub
'
'Private Function mForm_Rearrange(p_EditMode As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'    Dim setHeight As Integer
'    Dim setWidth As Integer
'    Dim setTop As Integer
'    Dim setLeft As Integer
'    Dim tempUnit As Integer
'
'    If p_EditMode Then
'        'Contract the grid to uncover the edit criterion frame
'        Me.dgFreeze.Height = 2715
'        Me.frmFreezeEdit.Visible = True
'        'Disable record manipulation and enable save.
'        Me.tbNavigation.Tools("ID_AddNew").Enabled = False
'        Me.tbNavigation.Tools("ID_Edit").Enabled = False
'        Me.tbNavigation.Tools("ID_Delete").Enabled = False
'        'Save
'        Me.tbNavigation.Tools("ID_Save").Enabled = True
'    Else
'        Me.tbNavigation.Tools("ID_Mode").ChangeAll ssChangeAllName, getTranslationResource("View")
'        'Expand the grid to cover the edit criterion frame
'        Me.dgFreeze.Height = Me.dgFreeze.Height + frmFreezeEdit.Height + 200
'        Me.frmFreezeEdit.Visible = False
'        'Enable record manipulation and disable save.
'        Me.tbNavigation.Tools("ID_AddNew").Enabled = True
'        Me.tbNavigation.Tools("ID_Edit").Enabled = True
'        Me.tbNavigation.Tools("ID_Delete").Enabled = True
'        'Save
'        Me.tbNavigation.Tools("ID_Save").Enabled = False
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(mForm_Rearrange)"
'End Function
'
'Private Function mFetch_CodeLookups() As Long
'On Error GoTo ErrorHandler
'
'    Dim rsCodeLookup As ADODB.Recordset
'    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
'    Dim IndexCounter As Long
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'    Dim AddItemString As String
'
'    'Set default to failure
'    mFetch_CodeLookups = -1
'
'    '************************************************************
'    'Build/Bind the Location Status dropdown
'    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
'    With AIM_CodeLookup_Get_Sp
'        Set .ActiveConnection = m_Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_CodeLookup_Get_Sp"
'    'Declare parameters
'        .Parameters.Append AIM_CodeLookup_Get_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_CodeLookup_Get_Sp.CreateParameter("@CodeType", adVarWChar, adParamInput, 30)
'        .Parameters.Append AIM_CodeLookup_Get_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 10)
'    'Set values
'        .Parameters("@CodeType").Value = g_CODETYPE_FREEZETYPE
'        .Parameters("@LangID").Value = gLangID
'    End With
'    Set rsCodeLookup = New ADODB.Recordset
'    With rsCodeLookup
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
'    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
'        Me.dcFreezeType.RemoveAll
'        Me.dcFreezeType.Reset
'        rsCodeLookup.MoveFirst
'        Do Until rsCodeLookup.eof
'            AddItemString = CStr(rsCodeLookup!CodeID) & vbTab & CStr(rsCodeLookup!CodeDesc)
'            Me.dcFreezeType.AddItem AddItemString
'            rsCodeLookup.MoveNext
'        Loop
'        Me.dcFreezeType.DataFieldList = "CodeID"
'        Me.dcFreezeType.DataFieldList = "CodeDesc"
'    End If
'    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
'    Set rsCodeLookup = Nothing
'    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
'    Set AIM_CodeLookup_Get_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.Source
'    ErrDesc = Err.Description
'
'    Me.dcFreezeType.Redraw = True
'    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
'    Set rsCodeLookup = Nothing
'    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
'    Set AIM_CodeLookup_Get_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFetch_CodeLookups)"
'End Function
'
'Private Function mFcstFreeze_Fetch() As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstFreeze_Fetch_Sp As ADODB.Command
'    Dim rsFcstFreeze As ADODB.Recordset
'
'    Dim DBugCounter As Integer
'    Dim DBugText As String
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'
'    Dim IndexCounter As Long
'    Dim RowCount As Long
'    Dim RowIdx As Long
'    Dim AddItemString As String
'    Dim BkMark As Variant
'
'    dgFreeze.Redraw = False
'
''check bookmark here
'    BkMark = IIf(IsEmpty(Me.dgFreeze.bookmark), Null, Me.dgFreeze.bookmark)
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstFreeze_Fetch_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstFreeze_Fetch_Sp
'        Set .ActiveConnection = m_Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstFreeze_Fetch_Sp"
'
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Fetch_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Fetch_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Fetch_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 10)
'    'Set values
'        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
'        .Parameters("@UserID").Value = gUserID
'        .Parameters("@LangID").Value = gLangID
'    End With
'
'    'Init. the recordset
'    Set rsFcstFreeze = New ADODB.Recordset
'    With rsFcstFreeze
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
'
'    'Fetch in data
'    rsFcstFreeze.Open AIM_DmdPlanFcstFreeze_Fetch_Sp
'
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_DmdPlanFcstFreeze_Fetch_Sp.Parameters(0).Value
'    Select Case RtnCode
'    Case Is > 0 'Success
'        'Clear any existing columns
'        Me.dgFreeze.Columns.RemoveAll
'        RowCount = rsFcstFreeze.RecordCount
'
'        IndexCounter = 0
'        'Set up the Grid
'
'        Me.dgFreeze.Columns.Add IndexCounter
'        Me.dgFreeze.Columns(IndexCounter).Name = "FcstFreezeKey"
'        Me.dgFreeze.Columns(IndexCounter).Caption = "FcstFreezeKey"
'        Me.dgFreeze.Columns(IndexCounter).Width = 500
'        Me.dgFreeze.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        Me.dgFreeze.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        Me.dgFreeze.Columns(IndexCounter).Visible = False
'        m_ColIdx_FcstFreezeKey = IndexCounter
'
'        IndexCounter = IndexCounter + 1
'        Me.dgFreeze.Columns.Add IndexCounter
'        Me.dgFreeze.Columns(IndexCounter).Name = "FreezeType"
'        Me.dgFreeze.Columns(IndexCounter).Caption = getTranslationResource("Freeze Type")
'        Me.dgFreeze.Columns(IndexCounter).Width = 1440
'        Me.dgFreeze.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        Me.dgFreeze.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        m_ColIdx_FreezeType = IndexCounter
'
'        IndexCounter = IndexCounter + 1
'        Me.dgFreeze.Columns.Add IndexCounter
'        Me.dgFreeze.Columns(IndexCounter).Name = "LcID"
'        Me.dgFreeze.Columns(IndexCounter).Caption = getTranslationResource("Location ID")
'        Me.dgFreeze.Columns(IndexCounter).Width = 1440
'        Me.dgFreeze.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        Me.dgFreeze.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        m_ColIdx_LcID = IndexCounter
'
'        IndexCounter = IndexCounter + 1
'        Me.dgFreeze.Columns.Add IndexCounter
'        Me.dgFreeze.Columns(IndexCounter).Name = "FreezeLimit"
'        Me.dgFreeze.Columns(IndexCounter).Caption = getTranslationResource("Freeze Limit")
'        Me.dgFreeze.Columns(IndexCounter).Width = 1440
'        Me.dgFreeze.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        Me.dgFreeze.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        m_ColIdx_FreezeLimit = IndexCounter
'
'        IndexCounter = IndexCounter + 1
'        Me.dgFreeze.Columns.Add IndexCounter
'        Me.dgFreeze.Columns(IndexCounter).Name = "FreezeDesc"
'        Me.dgFreeze.Columns(IndexCounter).Caption = getTranslationResource("Description")
'        Me.dgFreeze.Columns(IndexCounter).Width = 2000
'        Me.dgFreeze.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
'        Me.dgFreeze.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        m_ColIdx_ItemDesc = IndexCounter
'
'        If gGridAutoSizeOption Then
'            'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'            AdjustColumnWidths dgFreeze, ACW_EXPAND
'        End If
'
'        If f_IsRecordsetOpenAndPopulated(rsFcstFreeze) Then
'            Do Until rsFcstFreeze.eof
'                AddItemString = ""
'                AddItemString = rsFcstFreeze!FcstFreezeKey
'                AddItemString = AddItemString & vbTab & rsFcstFreeze!FreezeType
'                AddItemString = AddItemString & vbTab & rsFcstFreeze!LcId
'                AddItemString = AddItemString & vbTab & rsFcstFreeze!FreezeLimit
'                If rsFcstFreeze!FreezeType = 0 Then
'                    AddItemString = AddItemString & vbTab & p_FcstIntervalCaption
'                Else
'                    AddItemString = AddItemString & vbTab & rsFcstFreeze!FreezeDescription
'                End If
'                Me.dgFreeze.AddItem AddItemString
'                rsFcstFreeze.MoveNext
'            Loop
'        End If
'
'    Case -1 'No Data Found
'        'Msg here
'    Case -2 'SQL Error
'        'Msg here
'    Case -3 'Invalid parameter
'        'Msg here
'    End Select
'
'    'Set return value
'    mFcstFreeze_Fetch = RtnCode
'
'    Me.dgFreeze.bookmark = BkMark
'
'    dgFreeze.Redraw = True
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstFreeze_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstFreeze_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstFreeze_Fetch_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsFcstFreeze) Then rsFcstFreeze.Close
'    Set rsFcstFreeze = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.Source
'    ErrDesc = Err.Description
'
'    dgFreeze.Redraw = True
'    If Not (AIM_DmdPlanFcstFreeze_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstFreeze_Fetch_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstFreeze_Fetch_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsFcstFreeze) Then rsFcstFreeze.Close
'    Set rsFcstFreeze = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstFreeze_Fetch)"
'End Function
'
'Private Function mFcstFreeze_Save( _
'    p_FcstFreezeType As Integer, _
'    p_LcID As String, _
'    p_FreezeLimit As String _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstFreeze_Save_Sp As ADODB.Command
'
'    Dim DBugCounter As Integer
'    Dim DBugText As String
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'
'    Dim IndexCounter As Long
'    Dim RowCount As Long
'    Dim RowIdx As Long
'    Dim AddItemString As String
'    Dim BkMark As Variant
'
'    BkMark = IIf(IsEmpty(Me.dgFreeze.bookmark), Null, Me.dgFreeze.bookmark)
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstFreeze_Save_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstFreeze_Save_Sp
'        Set .ActiveConnection = m_Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstFreeze_Save_Sp"
'
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("@FreezeType", adTinyInt, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("@FreezeLimit", adVarWChar, adParamInput, 255)
'    'Output
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Save_Sp.CreateParameter("@FcstFreezeKey", adInteger, adParamOutput)
'    'Set values
'        .Parameters("@UserID").Value = gUserID
'        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
'        .Parameters("@FreezeType").Value = p_FcstFreezeType
'        .Parameters("@LcID").Value = p_LcID
'        .Parameters("@FreezeLimit").Value = p_FreezeLimit
'    'Execute
'        .Execute
'    End With
'
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_DmdPlanFcstFreeze_Save_Sp.Parameters(0).Value
'
'    'Set return value
'    mFcstFreeze_Save = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstFreeze_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstFreeze_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstFreeze_Save_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.Source
'    ErrDesc = Err.Description
'
'    If Not (AIM_DmdPlanFcstFreeze_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstFreeze_Save_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstFreeze_Save_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstFreeze_Save)"
'End Function
'
'Private Function mFcstFreeze_Delete( _
'    p_FcstFreezeKey As Integer _
') As Long
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim AIM_DmdPlanFcstFreeze_Delete_Sp As ADODB.Command
'
'    Dim DBugCounter As Integer
'    Dim DBugText As String
'    Dim ErrNumber As Long
'    Dim ErrSource As String
'    Dim ErrDesc As String
'
'    Dim IndexCounter As Long
'    Dim RowCount As Long
'    Dim RowIdx As Long
'    Dim AddItemString As String
'    Dim BkMark As Variant
'
'    BkMark = IIf(IsEmpty(Me.dgFreeze.bookmark), Null, Me.dgFreeze.bookmark)
'
'    'Init. the command object
'    Set AIM_DmdPlanFcstFreeze_Delete_Sp = New ADODB.Command
'    With AIM_DmdPlanFcstFreeze_Delete_Sp
'        Set .ActiveConnection = m_Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_DmdPlanFcstFreeze_Delete_Sp"
'
'    'Declare parameters
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Delete_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
'    'Input
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Delete_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Delete_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstFreeze_Delete_Sp.CreateParameter("@FcstFreezeKey", adInteger, adParamInput)
'    'Set values
'        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
'        .Parameters("@UserID").Value = gUserID
'        .Parameters("@FcstFreezeKey").Value = p_FcstFreezeKey
'    'Execute
'        .Execute
'    End With
'
'    'Check return code -- 0 = fail; 1 = succeed
'    RtnCode = AIM_DmdPlanFcstFreeze_Delete_Sp.Parameters(0).Value
'
'    'Set return value
'    mFcstFreeze_Delete = RtnCode
'
'CleanUp:
'    If Not (AIM_DmdPlanFcstFreeze_Delete_Sp Is Nothing) Then Set AIM_DmdPlanFcstFreeze_Delete_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstFreeze_Delete_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    ErrNumber = Err.Number
'    ErrSource = Err.Source
'    ErrDesc = Err.Description
'
'    If Not (AIM_DmdPlanFcstFreeze_Delete_Sp Is Nothing) Then Set AIM_DmdPlanFcstFreeze_Delete_Sp.ActiveConnection = Nothing
'    Set AIM_DmdPlanFcstFreeze_Delete_Sp = Nothing
'
'    Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstFreeze_Delete)"
'End Function
'
'Private Function mSet_EditMode(p_ToolID As String)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Long
'    Dim p_EditMode As Boolean
'
'    Dim tempString As String
'    Dim BkMark As Variant
'    Dim exitloop As Boolean
'    Dim frtypecode As Integer
'
'    dcFreezeType.Redraw = False
'
'    p_EditMode = True
'    RtnCode = mForm_Rearrange(p_EditMode)
'
'    Select Case UCase$(p_ToolID)
'    Case "ID_ADDNEW"
'        Me.tbNavigation.Tools("ID_Mode").ChangeAll ssChangeAllName, getTranslationResource("Add")
'        Me.txtLcId.Text = ""
'
'dcFreezeType.MoveFirst
'Do While Not exitloop
'    BkMark = dcFreezeType.bookmark
'    If CStr(dcFreezeType.Columns(0).CellText(BkMark)) = "1" Then
'        frtypecode = CInt(dcFreezeType.Columns(0).CellText(BkMark))
'        tempString = CStr(dcFreezeType.Columns(1).CellText(BkMark))
'        exitloop = True
'    Else
'        dcFreezeType.MoveNext
'    End If
'
'Loop
'
'dcFreezeType.Value = frtypecode  'Apply to items
'dcFreezeType.Text = tempString
'
'Debug.Print "dcFreezeType.Text: " & CStr(dcFreezeType.Text)
'Debug.Print "dcFreezeType.value: " & CStr(dcFreezeType.Value)
'
'        Me.txtFreezePeriods.Text = 0
'        Me.txtItem.Text = ""
'    Case "ID_EDIT"
'        Me.tbNavigation.Tools("ID_Mode").ChangeAll ssChangeAllName, getTranslationResource("Edit")
'        BkMark = Me.dgFreeze.bookmark
'
'        Me.txtLcId.Text = Me.dgFreeze.Columns(m_ColIdx_LcID).CellText(BkMark)
'tempString = CStr(Me.dgFreeze.Columns(m_ColIdx_FreezeType).CellText(BkMark))
'Debug.Print "b:" & tempString
'dcFreezeType.MoveFirst
'Do While Not exitloop
'    BkMark = dcFreezeType.bookmark
'    If CStr(dcFreezeType.Columns(0).CellText(BkMark)) = tempString Then
'        frtypecode = CInt(dcFreezeType.Columns(0).CellText(BkMark))
'        tempString = CStr(dcFreezeType.Columns(1).CellText(BkMark))
'        exitloop = True
'    Else
'        dcFreezeType.MoveNext
'    End If
'Loop
'
'dcFreezeType.Value = frtypecode
'dcFreezeType.Text = tempString
'
'Debug.Print "dcFreezeType.Text: " & CStr(dcFreezeType.Text)
'Debug.Print "dcFreezeType.value: " & CStr(dcFreezeType.Value)
'
'        If Me.dgFreeze.Columns(m_ColIdx_FreezeType).CellText(BkMark) = 0 Then  'locations
'            Me.txtFreezePeriods.Text = Me.dgFreeze.Columns(m_ColIdx_FreezeLimit).CellText(BkMark)
'        Else    'items
'            Me.txtItem.Text = Me.dgFreeze.Columns(m_ColIdx_FreezeLimit).CellText(BkMark)
'        End If
'    End Select
'
'    Me.dcFreezeType.SetFocus
'
'    dcFreezeType.Redraw = True
'
'Exit Function
'ErrorHandler:
'    dcFreezeType.Redraw = True
'    dgFreeze.Redraw = True
'    Err.Raise Err.Source, Err.Number, Err.Description & "(mSet_EditMode)"
'End Function
'
