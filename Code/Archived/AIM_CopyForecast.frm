VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_CopyForecast 
   Caption         =   "SSA AIM Copy Forecast"
   ClientHeight    =   2805
   ClientLeft      =   2040
   ClientTop       =   1800
   ClientWidth     =   11640
   LinkTopic       =   "Form1"
   ScaleHeight     =   2805
   ScaleWidth      =   11640
   Begin VB.CommandButton cmdCopy 
      Caption         =   "&Copy"
      Default         =   -1  'True
      Enabled         =   0   'False
      Height          =   345
      Left            =   9353
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   2340
      Width           =   2175
   End
   Begin VB.CommandButton cmdExit 
      Caption         =   "E&xit"
      Height          =   345
      Left            =   7080
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   2340
      Width           =   2175
   End
   Begin VB.Frame Frame1 
      Height          =   2100
      Left            =   113
      TabIndex        =   12
      Top             =   75
      Width           =   11415
      Begin VB.TextBox txtFromFcstType 
         Height          =   345
         Left            =   2750
         Locked          =   -1  'True
         TabIndex        =   1
         Top             =   735
         Width           =   2655
      End
      Begin VB.TextBox txtToFcstComments 
         Height          =   345
         Left            =   2750
         TabIndex        =   4
         Top             =   1635
         Width           =   8535
      End
      Begin VB.TextBox txtFromFcstComments 
         Height          =   345
         Left            =   2745
         TabIndex        =   3
         Top             =   1245
         Width           =   8535
      End
      Begin VB.TextBox txtFcstId 
         Height          =   345
         Left            =   2750
         Locked          =   -1  'True
         TabIndex        =   0
         Top             =   220
         Width           =   2655
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcToFcstType 
         Bindings        =   "AIM_CopyForecast.frx":0000
         DataField       =   "CodeId"
         Height          =   345
         Left            =   8625
         TabIndex        =   2
         Top             =   735
         Width           =   2655
         DataFieldList   =   "CodeDesc"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   4683
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin VB.Label Label2 
         Caption         =   "Destination Comments"
         Height          =   300
         Left            =   120
         TabIndex        =   11
         Top             =   1680
         Width           =   2535
      End
      Begin VB.Label Label43 
         Caption         =   "Source Comments"
         Height          =   300
         Left            =   120
         TabIndex        =   10
         Top             =   1290
         Width           =   2535
      End
      Begin VB.Label Label1 
         Caption         =   "To Forecast Type"
         Height          =   300
         Left            =   5985
         TabIndex        =   9
         Top             =   780
         Width           =   2535
      End
      Begin VB.Label Label10 
         Caption         =   "From Forecast Type"
         Height          =   300
         Left            =   120
         TabIndex        =   8
         Top             =   780
         Width           =   2535
      End
      Begin VB.Label Label8 
         Caption         =   "Forecast ID"
         Height          =   300
         Left            =   120
         TabIndex        =   7
         Top             =   265
         Width           =   2535
      End
   End
End
Attribute VB_Name = "AIM_CopyForecast"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'Option Explicit
'
'Dim Cn As ADODB.Connection
'Dim AIM_ForecastRepository_Copy_Sp As ADODB.Command
'Dim AIM_ForecastUserAccess_Validate_Sp As ADODB.Command
'Dim AIM_ForecastRepository_List_Sp As ADODB.Command
'Dim AccessType As Integer
'
'Private Sub cmdCopy_Click()
'On Error GoTo ErrorHandler
'
'    Dim strMessage As String
'    Dim RtnCode As Long
'
'    With AIM_ForecastRepository_Copy_Sp
'        .Parameters("@FcstID").Value = txtFcstId.Text
'        .Parameters("@FcstTypeFrom").Value = AIM_ForecastModification.Fcsttype
'        .Parameters("@FcstTypeTo").Value = dcToFcstType.Columns(0).Value
'        .Parameters("@CurrentPd").Value = AIM_ForecastModification.CurrentPd
'        .Parameters("@FcstDesc").Value = AIM_ForecastModification.FcstDesc
'        .Parameters("@FcstComment").Value = txtToFcstComments.Text
'        .Parameters("@UserID").Value = gUserID
'
'        .Execute
'
'        If .Parameters("@Return_Value") = 0 Or .Parameters("@Return_Value") = 1 Then
'            strMessage = getTranslationResource("MSGBOX07000")
'            If StrComp(strMessage, "MSGBOX07000") = 0 Then _
'                    strMessage = "The copied forecast type has been saved successfully."
'        Else
'            strMessage = getTranslationResource("MSGBOX07001")
'            If StrComp(strMessage, "MSGBOX07001") = 0 Then _
'                    strMessage = "Error encountered while attempting to copy to the selected forecast type. " & _
'                                 "Please ensure that the Source forecast type has been saved to the repository, " & _
'                                 "and/or that the Target forecast type does not exist."
'        End If
'
'        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
'
'    End With
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(cmdCopy_Click)"
'End Sub
'
'Private Sub cmdExit_Click()
'    Unload Me
'End Sub
'
'Private Sub dcToFcstType_Click()
'On Error GoTo ErrorHandler
'
'    Dim rsFcstRepos As ADODB.Recordset
'
'    If Trim(txtFromFcstType.Text) = Trim(dcToFcstType.Text) Then
'        cmdCopy.Enabled = False
'        txtToFcstComments.Text = ""
'        Exit Sub
'    End If
'
'    If Trim(dcToFcstType.Text <> "") Then
'        With AIM_ForecastUserAccess_Validate_Sp
'            .Parameters("@UserID").Value = gUserID
'            .Parameters("@FcstID").Value = txtFcstId.Text
'            .Parameters("@FcstType").Value = dcToFcstType.Columns(0).Value
'            .Parameters("@AccessType").Value = 0
'
'            .Execute
'
'            If .Parameters("@Return_Value") = 0 Then
'                AccessType = .Parameters("@AccessType")
'            Else
'                AccessType = 0
'            End If
'        End With
'
'        With AIM_ForecastRepository_List_Sp
'            .Parameters("@FcstID") = txtFcstId.Text
'            .Parameters("@FcstType") = dcToFcstType.Columns(0).Value
'        End With
'
'        Set rsFcstRepos = New ADODB.Recordset
'        rsFcstRepos.Open AIM_ForecastRepository_List_Sp
'
'        If f_IsRecordsetOpenAndPopulated(rsFcstRepos) Then
'            txtToFcstComments.Text = Trim(rsFcstRepos("FcstComment").Value)
'        End If
'
'        If f_IsRecordsetValidAndOpen(rsFcstRepos) Then rsFcstRepos.Close
'        Set rsFcstRepos = Nothing
'
'        If AccessType = 0 Or AccessType = 1 Then
'            cmdCopy.Enabled = False
'        ElseIf AccessType = 2 Then
'            cmdCopy.Enabled = True
'        ElseIf AccessType = 3 Then
'            If AIM_ForecastRepository_List_Sp.Parameters("@Return_Value") = -1 Then 'No Data Found
'                cmdCopy.Enabled = True
'                txtToFcstComments.Text = " "
'            ElseIf AIM_ForecastRepository_List_Sp.Parameters("@Return_Value") = 0 Then 'Successful and Data Found
'                cmdCopy.Enabled = False
'            ElseIf AIM_ForecastRepository_List_Sp.Parameters("@Return_Value") = -2 Then 'SQL Error
'                cmdCopy.Enabled = False
'            End If
'        ElseIf AccessType = 4 Then
'            If AIM_ForecastRepository_List_Sp.Parameters("@Return_Value") = -1 Then 'No Data Found
'                cmdCopy.Enabled = False
'                txtToFcstComments.Text = " "
'            ElseIf AIM_ForecastRepository_List_Sp.Parameters("@Return_Value") = 0 Then 'Successful and Data Found
'                cmdCopy.Enabled = True
'                txtToFcstComments.Text = " "
'            ElseIf AIM_ForecastRepository_List_Sp.Parameters("@Return_Value") = -2 Then 'SQL Error
'                cmdCopy.Enabled = False
'                txtToFcstComments.Text = " "
'            End If
'        End If
'    End If
'
'Exit Sub
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsFcstRepos) Then rsFcstRepos.Close
'    Set rsFcstRepos = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(dcToFcstType_Click)"
'End Sub
'
'Private Sub dcToFcstType_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcToFcstType.Columns(0).Name = "CodeID"
'    Me.dcToFcstType.Columns(0).Caption = getTranslationResource("Code")
'    Me.dcToFcstType.Columns(0).Width = 1650
'    Me.dcToFcstType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcToFcstType.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcToFcstType.Columns(0).DataField = "CodeID"
'
'    Me.dcToFcstType.Columns(1).Name = "CodeDesc"
'    Me.dcToFcstType.Columns(1).Caption = getTranslationResource("Forecast Type")
'    Me.dcToFcstType.Columns(1).Width = 1650
'    Me.dcToFcstType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcToFcstType.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dcToFcstType.Columns(1).DataField = "CodeDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcToFcstType, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcToFcstType.Columns.Count - 1
''        dcToFcstType.Columns(IndexCounter).HasHeadBackColor = True
''        dcToFcstType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(dcToFcstType_InitColumnProps)"
'End Sub
'
'Private Sub Form_Activate()
'On Error GoTo ErrorHandler
'
'    'Check for an open connection
'    If Cn.State <> adStateOpen Then
'        Unload Me
'        Exit Sub
'    End If
'
'    'Set State of window list State Button
'    gbUpdating = True
'    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
'    gbUpdating = False
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(Form_Activate)"
'End Sub
'
'Private Sub Form_Load()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strSQL As String
'    Dim strMessage As String
'    Dim strMessage1 As String
'
'    Dim rsFcstType As ADODB.Recordset
'
'    'Housekeeping
'    Screen.MousePointer = vbHourglass
'    strMessage = getTranslationResource("STATMSG07000")
'    If StrComp(strMessage, "STATMSG07000") = 0 Then strMessage = "Initializing Copy Forecast..."
'    Write_Message strMessage
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
'    If RtnCode <> SUCCEED Then Exit Sub
'
'    GetTranslatedCaptions Me
'
'    txtFcstId.Text = AIM_ForecastModification.dcFcstId.Text
'    txtFromFcstComments.Text = Trim(AIM_ForecastModification.txtFcstComments)
'    txtFromFcstType.Text = Trim(AIM_ForecastModification.dcFcstType.Text)
'
'    strSQL = "SELECT CodeID, CodeDesc"
'    strSQL = strSQL & vbCrLf & "FROM AIMCodeLookUp"
'    strSQL = strSQL & vbCrLf & "WHERE CodeType = N'FORECASTTYPE'"
'    strSQL = strSQL & vbCrLf & "AND LangID = N'" & gLangID & "'"
'    strSQL = strSQL & vbCrLf & "ORDER BY CodeID, CodeDesc "
'
'    Set rsFcstType = New ADODB.Recordset
'    rsFcstType.Open strSQL, Cn, adOpenStatic, adLockReadOnly
'    If f_IsRecordsetOpenAndPopulated(rsFcstType) Then
'        rsFcstType.MoveFirst
'        Do Until rsFcstType.eof
'            Me.dcToFcstType.AddItem rsFcstType("CodeId").Value + vbTab + rsFcstType("CodeDesc").Value
'            rsFcstType.MoveNext
'        Loop
'    End If
'
'    If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'    Set rsFcstType = Nothing
'
'    Set AIM_ForecastRepository_Copy_Sp = New ADODB.Command
'    With AIM_ForecastRepository_Copy_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepository_Copy_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastUserAccess_Validate_Sp = New ADODB.Command
'    With AIM_ForecastUserAccess_Validate_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastUserAccess_Validate_Sp"
'        .Parameters.Refresh
'    End With
'
'    Set AIM_ForecastRepository_List_Sp = New ADODB.Command
'    With AIM_ForecastRepository_List_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_ForecastRepository_List_Sp"
'        .Parameters.Refresh
'    End With
'
'    'Add to Windows List
'    AddToWindowList Me.Caption
'
'    'Windup
'    Write_Message ""
'    Screen.MousePointer = vbNormal
'Exit Sub
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsFcstType) Then rsFcstType.Close
'    Set rsFcstType = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(Form_Load)"
'End Sub
'
'Private Sub Form_Unload(Cancel As Integer)
'On Error GoTo ErrorHandler
'    Dim RtnCode As Integer
'
'    If Not (AIM_ForecastRepository_Copy_Sp Is Nothing) Then Set AIM_ForecastRepository_Copy_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_Copy_Sp = Nothing
'
'    If Not (AIM_ForecastUserAccess_Validate_Sp Is Nothing) Then Set AIM_ForecastUserAccess_Validate_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastUserAccess_Validate_Sp = Nothing
'
'    If Not (AIM_ForecastRepository_List_Sp Is Nothing) Then Set AIM_ForecastRepository_List_Sp.ActiveConnection = Nothing
'    Set AIM_ForecastRepository_List_Sp = Nothing
'
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
'    Set Cn = Nothing
'
'    'Remove form from window list
'    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(Form_Unload)"
'End Sub
