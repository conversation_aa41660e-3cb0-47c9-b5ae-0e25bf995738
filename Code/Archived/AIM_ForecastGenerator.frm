VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Begin VB.Form AIM_ForecastGenerator 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Forecast Generator"
   ClientHeight    =   9450
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   14850
   BeginProperty Font 
      Name            =   "MS Sans Serif"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "AIM_ForecastGenerator.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9450
   ScaleWidth      =   14850
   WindowState     =   2  'Maximized
   Begin ActiveToolBars.SSActiveToolBars tbForecast 
      Left            =   180
      Top             =   9000
      _ExtentX        =   741
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   5
      DisplayContextMenu=   0   'False
      Tools           =   "AIM_ForecastGenerator.frx":030A
      ToolBars        =   "AIM_ForecastGenerator.frx":4F1A
   End
   Begin ActiveTabs.SSActiveTabs atForecastHeader 
      Height          =   4155
      Left            =   4110
      TabIndex        =   2
      Top             =   0
      Width           =   10725
      _ExtentX        =   18918
      _ExtentY        =   7329
      _Version        =   131083
      TabCount        =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Arial"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TagVariant      =   ""
      Tabs            =   "AIM_ForecastGenerator.frx":50C1
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   3765
         Left            =   -99969
         TabIndex        =   30
         Top             =   360
         Width           =   10665
         _ExtentX        =   18812
         _ExtentY        =   6641
         _Version        =   131083
         TabGuid         =   "AIM_ForecastGenerator.frx":5152
         Begin VB.Frame fraForecastFilters 
            BeginProperty Font 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3735
            Left            =   0
            TabIndex        =   66
            Top             =   0
            Width           =   10575
            Begin TDBText6Ctl.TDBText txtVnId 
               Height          =   345
               Left            =   2865
               TabIndex        =   67
               TabStop         =   0   'False
               Top             =   1004
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":517A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":51E6
               Key             =   "AIM_ForecastGenerator.frx":5204
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAssort 
               Height          =   345
               Left            =   2865
               TabIndex        =   68
               TabStop         =   0   'False
               Top             =   1386
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5248
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":52B4
               Key             =   "AIM_ForecastGenerator.frx":52D2
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLcId 
               Height          =   345
               Left            =   7905
               TabIndex        =   69
               TabStop         =   0   'False
               Top             =   240
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5316
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5382
               Key             =   "AIM_ForecastGenerator.frx":53A0
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItem 
               Height          =   345
               Left            =   2865
               TabIndex        =   70
               TabStop         =   0   'False
               Top             =   240
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":53E4
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5450
               Key             =   "AIM_ForecastGenerator.frx":546E
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   25
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtById 
               Height          =   345
               Left            =   2865
               TabIndex        =   71
               TabStop         =   0   'False
               Top             =   1768
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":54B2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":551E
               Key             =   "AIM_ForecastGenerator.frx":553C
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   1
               Left            =   2865
               TabIndex        =   72
               TabStop         =   0   'False
               Top             =   2150
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5580
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":55EC
               Key             =   "AIM_ForecastGenerator.frx":560A
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   2
               Left            =   2865
               TabIndex        =   73
               TabStop         =   0   'False
               Top             =   2532
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":564E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":56BA
               Key             =   "AIM_ForecastGenerator.frx":56D8
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   3
               Left            =   2865
               TabIndex        =   74
               TabStop         =   0   'False
               Top             =   2914
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":571C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5788
               Key             =   "AIM_ForecastGenerator.frx":57A6
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtItemStatus 
               Height          =   345
               Left            =   2865
               TabIndex        =   75
               TabStop         =   0   'False
               Top             =   622
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":57EA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5856
               Key             =   "AIM_ForecastGenerator.frx":5874
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   1
               Left            =   4860
               TabIndex        =   76
               TabStop         =   0   'False
               Top             =   2145
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":58B8
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5924
               Key             =   "AIM_ForecastGenerator.frx":5942
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   2
               Left            =   4860
               TabIndex        =   77
               TabStop         =   0   'False
               Top             =   2535
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5986
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":59F2
               Key             =   "AIM_ForecastGenerator.frx":5A10
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   3
               Left            =   4860
               TabIndex        =   78
               TabStop         =   0   'False
               Top             =   2910
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5A54
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5AC0
               Key             =   "AIM_ForecastGenerator.frx":5ADE
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLDivision 
               Height          =   345
               Left            =   7905
               TabIndex        =   79
               Top             =   1004
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5B22
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5B8E
               Key             =   "AIM_ForecastGenerator.frx":5BAC
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLUserDefined 
               Height          =   345
               Left            =   7905
               TabIndex        =   80
               Top             =   1768
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5BF0
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5C5C
               Key             =   "AIM_ForecastGenerator.frx":5C7A
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLStatus 
               Height          =   345
               Left            =   7905
               TabIndex        =   81
               Top             =   622
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5CBE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5D2A
               Key             =   "AIM_ForecastGenerator.frx":5D48
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClass 
               Height          =   345
               Index           =   4
               Left            =   2880
               TabIndex        =   82
               TabStop         =   0   'False
               Top             =   3300
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5D8C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5DF8
               Key             =   "AIM_ForecastGenerator.frx":5E16
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtClassDesc 
               Height          =   345
               Index           =   4
               Left            =   4860
               TabIndex        =   83
               TabStop         =   0   'False
               Top             =   3300
               Width           =   5100
               _Version        =   65536
               _ExtentX        =   8996
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5E5A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5EC6
               Key             =   "AIM_ForecastGenerator.frx":5EE4
               BackColor       =   -2147483638
               EditMode        =   2
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   0
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLRegion 
               Height          =   345
               Left            =   7905
               TabIndex        =   84
               Top             =   1386
               Width           =   1980
               _Version        =   65536
               _ExtentX        =   3492
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":5F28
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":5F94
               Key             =   "AIM_ForecastGenerator.frx":5FB2
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Status"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   5160
               TabIndex        =   98
               Top             =   667
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Division"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   5160
               TabIndex        =   97
               Top             =   1049
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Region"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   5160
               TabIndex        =   96
               Top             =   1431
               Width           =   2595
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "User-defined"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   5160
               TabIndex        =   95
               Top             =   1813
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Item Status"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   94
               Top             =   677
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Location ID"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   5160
               TabIndex        =   93
               Top             =   285
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Assortment"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   92
               Top             =   1461
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Vendor ID"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   91
               Top             =   1069
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Buyer ID"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   120
               TabIndex        =   90
               Top             =   1853
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Item"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   89
               Top             =   285
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 1"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   120
               TabIndex        =   88
               Top             =   2195
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 2"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   120
               TabIndex        =   87
               Top             =   2577
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 3"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   120
               TabIndex        =   86
               Top             =   2959
               Width           =   2595
            End
            Begin VB.Label lblForecastFilters 
               Caption         =   "Classification 4"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   9
               Left            =   120
               TabIndex        =   85
               Top             =   3345
               Width           =   2595
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   3765
         Left            =   30
         TabIndex        =   31
         Top             =   360
         Width           =   10665
         _ExtentX        =   18812
         _ExtentY        =   6641
         _Version        =   131083
         TabGuid         =   "AIM_ForecastGenerator.frx":5FF6
         Begin VB.Frame Frame3 
            BeginProperty Font 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3510
            Left            =   3240
            TabIndex        =   59
            Top             =   195
            Width           =   4000
            Begin VB.OptionButton optProductionConstraint 
               Caption         =   "Production Constraint"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   270
               Left            =   210
               TabIndex        =   65
               Top             =   2340
               Width           =   3660
            End
            Begin VB.OptionButton optNetRequirements 
               Caption         =   "Net Requirements"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   285
               Left            =   210
               TabIndex        =   64
               Top             =   1980
               Width           =   3660
            End
            Begin VB.OptionButton optForecast 
               Caption         =   "Forecast"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   270
               Left            =   210
               TabIndex        =   63
               Top             =   1650
               Width           =   3660
            End
            Begin VB.CheckBox ckForecastLevel 
               Caption         =   "&Summarize by Item"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   210
               TabIndex        =   6
               Top             =   2940
               Width           =   3585
            End
            Begin TDBDate6Ctl.TDBDate txtFcstStartDate 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "M/d/yyyy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   2460
               TabIndex        =   3
               Top             =   225
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calendar        =   "AIM_ForecastGenerator.frx":601E
               Caption         =   "AIM_ForecastGenerator.frx":6136
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":61A2
               Keys            =   "AIM_ForecastGenerator.frx":61C0
               Spin            =   "AIM_ForecastGenerator.frx":621E
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   -1
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "10/15/2001"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   37179
               CenturyMode     =   0
            End
            Begin TDBNumber6Ctl.TDBNumber txtFcstPds 
               Height          =   345
               Left            =   2460
               TabIndex        =   4
               Top             =   600
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastGenerator.frx":6246
               Caption         =   "AIM_ForecastGenerator.frx":6266
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":62D2
               Keys            =   "AIM_ForecastGenerator.frx":62F0
               Spin            =   "AIM_ForecastGenerator.frx":633A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   104
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   12
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjustPct 
               Height          =   345
               Left            =   2460
               TabIndex        =   5
               Top             =   1170
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastGenerator.frx":6362
               Caption         =   "AIM_ForecastGenerator.frx":6382
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":63EE
               Keys            =   "AIM_ForecastGenerator.frx":640C
               Spin            =   "AIM_ForecastGenerator.frx":6456
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00;(##0.00)"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1179653
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label20 
               Caption         =   "Start Date"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   62
               Top             =   265
               Width           =   2295
            End
            Begin VB.Label Label21 
               Caption         =   "Forecast Periods"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   61
               Top             =   645
               Width           =   2295
            End
            Begin VB.Label Label22 
               Caption         =   "Forecast Adjustment %"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   60
               Top             =   1215
               Width           =   2295
            End
         End
         Begin VB.Frame fraApplyTrend 
            Caption         =   "Apply Trend"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   975
            Left            =   30
            TabIndex        =   58
            Top             =   2720
            Width           =   3135
            Begin VB.OptionButton optApplyTrend 
               Caption         =   "No"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   0
               Left            =   1605
               TabIndex        =   8
               Top             =   285
               Value           =   -1  'True
               Width           =   1500
            End
            Begin VB.OptionButton optApplyTrend 
               Caption         =   "By Item"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   1
               Left            =   105
               TabIndex        =   9
               Top             =   570
               Width           =   1500
            End
            Begin VB.OptionButton optApplyTrend 
               Caption         =   "Yes"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   2
               Left            =   120
               TabIndex        =   7
               Top             =   270
               Width           =   1500
            End
         End
         Begin VB.Frame Frame2 
            Caption         =   "Forecast Unit"
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3540
            Left            =   9030
            TabIndex        =   53
            Top             =   200
            Width           =   1575
            Begin VB.OptionButton OptForecastUnit 
               Caption         =   "Price"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   4
               Left            =   120
               TabIndex        =   22
               Top             =   1920
               Width           =   1335
            End
            Begin VB.OptionButton OptForecastUnit 
               Caption         =   "Cube"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   3
               Left            =   120
               TabIndex        =   21
               Top             =   1500
               Width           =   1335
            End
            Begin VB.OptionButton OptForecastUnit 
               Caption         =   "Weight"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   2
               Left            =   120
               TabIndex        =   20
               Top             =   1125
               Width           =   1335
            End
            Begin VB.OptionButton OptForecastUnit 
               Caption         =   "Cost"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   1
               Left            =   120
               TabIndex        =   19
               Top             =   750
               Width           =   1335
            End
            Begin VB.OptionButton OptForecastUnit 
               Caption         =   "Units"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   0
               Left            =   120
               TabIndex        =   18
               Top             =   375
               Value           =   -1  'True
               Width           =   1335
            End
         End
         Begin VB.Frame Frame1 
            Caption         =   "Interval"
            BeginProperty Font 
               Name            =   "Verdana"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   3525
            Left            =   7275
            TabIndex        =   52
            Top             =   200
            Width           =   1680
            Begin VB.OptionButton optInterval 
               Caption         =   "4-Week"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   7
               Left            =   120
               TabIndex        =   17
               Top             =   2805
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "4-4-5"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   6
               Left            =   120
               TabIndex        =   16
               Top             =   2439
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "4-5-4"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   5
               Left            =   120
               TabIndex        =   15
               Top             =   2075
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "5-4-4"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   4
               Left            =   120
               TabIndex        =   14
               Top             =   1711
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Quarters"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   3
               Left            =   120
               TabIndex        =   13
               Top             =   1347
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Months"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   2
               Left            =   120
               TabIndex        =   12
               Top             =   983
               Value           =   -1  'True
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Weeks"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   1
               Left            =   120
               TabIndex        =   11
               Top             =   619
               Width           =   1455
            End
            Begin VB.OptionButton optInterval 
               Caption         =   "Days"
               BeginProperty Font 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   0
               Left            =   120
               TabIndex        =   10
               Top             =   255
               Width           =   1455
            End
         End
         Begin VB.Frame Frame5 
            Caption         =   "Item Statistics"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   2355
            Left            =   30
            TabIndex        =   41
            Top             =   200
            Width           =   3135
            Begin TDBText6Ctl.TDBText txtFcstDemand 
               Height          =   345
               Left            =   1800
               TabIndex        =   42
               TabStop         =   0   'False
               Top             =   200
               Width           =   1200
               _Version        =   65536
               _ExtentX        =   2117
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":647E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":64EA
               Key             =   "AIM_ForecastGenerator.frx":6508
               BackColor       =   14737632
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   2
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtMAE 
               Height          =   345
               Left            =   1800
               TabIndex        =   43
               TabStop         =   0   'False
               Top             =   600
               Width           =   1200
               _Version        =   65536
               _ExtentX        =   2117
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":654C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":65B8
               Key             =   "AIM_ForecastGenerator.frx":65D6
               BackColor       =   14737632
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   2
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtTrend 
               Height          =   345
               Left            =   1800
               TabIndex        =   44
               TabStop         =   0   'False
               Top             =   1000
               Width           =   1200
               _Version        =   65536
               _ExtentX        =   2117
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":661A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":6686
               Key             =   "AIM_ForecastGenerator.frx":66A4
               BackColor       =   14737632
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   2
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtUOM 
               Height          =   345
               Left            =   1800
               TabIndex        =   45
               TabStop         =   0   'False
               Top             =   1400
               Width           =   690
               _Version        =   65536
               _ExtentX        =   1217
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":66E8
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":6754
               Key             =   "AIM_ForecastGenerator.frx":6772
               BackColor       =   14737632
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   2
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAvail 
               Height          =   345
               Left            =   1800
               TabIndex        =   46
               TabStop         =   0   'False
               Top             =   1800
               Width           =   1200
               _Version        =   65536
               _ExtentX        =   2117
               _ExtentY        =   609
               Caption         =   "AIM_ForecastGenerator.frx":67B6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastGenerator.frx":6822
               Key             =   "AIM_ForecastGenerator.frx":6840
               BackColor       =   14737632
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   2
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label14 
               Caption         =   "Availability"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   51
               Top             =   1845
               Width           =   1620
            End
            Begin VB.Label Label4 
               Caption         =   "UOM"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   50
               Top             =   1445
               Width           =   1620
            End
            Begin VB.Label Label3 
               Caption         =   "Trend"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   49
               Top             =   1045
               Width           =   1620
            End
            Begin VB.Label Label2 
               Caption         =   "MAE"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   48
               Top             =   645
               Width           =   1620
            End
            Begin VB.Label Label1 
               Caption         =   "Demand"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   90
               TabIndex        =   47
               Top             =   245
               Width           =   1620
            End
         End
      End
   End
   Begin VB.Frame frmForecast 
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   1110
      Left            =   30
      TabIndex        =   28
      Top             =   0
      Width           =   3975
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcForecast 
         Bindings        =   "AIM_ForecastGenerator.frx":6884
         Height          =   340
         Left            =   90
         TabIndex        =   0
         Top             =   200
         Width           =   1665
         DataFieldList   =   "FcstID"
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2937
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DataFieldToDisplay=   "FcstID"
      End
      Begin TDBText6Ctl.TDBText txtFcstDesc 
         Height          =   340
         Left            =   75
         TabIndex        =   1
         Top             =   660
         Width           =   3810
         _Version        =   65536
         _ExtentX        =   6720
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":68A1
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":690D
         Key             =   "AIM_ForecastGenerator.frx":692B
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   40
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label txtTime 
         Alignment       =   1  'Right Justify
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   2445
         TabIndex        =   29
         Top             =   220
         Width           =   1020
      End
   End
   Begin ActiveTabs.SSActiveTabs atForecast 
      Height          =   4755
      Left            =   30
      TabIndex        =   23
      Top             =   4230
      Width           =   14730
      _ExtentX        =   25982
      _ExtentY        =   8387
      _Version        =   131083
      TabCount        =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Arial"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TagVariant      =   ""
      Tabs            =   "AIM_ForecastGenerator.frx":696F
      Begin ActiveTabs.SSActiveTabPanel atpGraph 
         Height          =   4365
         Left            =   -99969
         TabIndex        =   26
         Top             =   360
         Width           =   14670
         _ExtentX        =   25876
         _ExtentY        =   7699
         _Version        =   131083
         TabGuid         =   "AIM_ForecastGenerator.frx":6A0E
         Begin C1Chart2D8.Chart2D chtForecast 
            Height          =   4155
            Left            =   60
            TabIndex        =   25
            Top             =   120
            Width           =   9795
            _Version        =   524288
            _Revision       =   7
            _ExtentX        =   17277
            _ExtentY        =   7329
            _StockProps     =   0
            ControlProperties=   "AIM_ForecastGenerator.frx":6A36
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpForecast 
         Height          =   4365
         Left            =   30
         TabIndex        =   27
         Top             =   360
         Width           =   14670
         _ExtentX        =   25876
         _ExtentY        =   7699
         _Version        =   131083
         TabGuid         =   "AIM_ForecastGenerator.frx":715A
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgForecast 
            Height          =   4410
            Left            =   75
            TabIndex        =   24
            Top             =   90
            Width           =   14520
            _Version        =   196617
            DataMode        =   1
            BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            AllowUpdate     =   0   'False
            AllowColumnMoving=   2
            AllowColumnSwapping=   0
            SelectTypeCol   =   0
            SelectTypeRow   =   0
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            CaptionAlignment=   0
            Columns(0).Width=   3200
            _ExtentX        =   25612
            _ExtentY        =   7779
            _StockProps     =   79
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
         End
      End
   End
   Begin VB.Frame Frame4 
      Caption         =   "Forecast Summary"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   2970
      Left            =   45
      TabIndex        =   32
      Top             =   1200
      Width           =   3975
      Begin TDBText6Ctl.TDBText txtItems 
         Height          =   345
         Left            =   2400
         TabIndex        =   33
         TabStop         =   0   'False
         Top             =   240
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":7182
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":71EE
         Key             =   "AIM_ForecastGenerator.frx":720C
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtUnits 
         Height          =   345
         Left            =   2400
         TabIndex        =   34
         TabStop         =   0   'False
         Top             =   600
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":7250
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":72BC
         Key             =   "AIM_ForecastGenerator.frx":72DA
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtWeight 
         Height          =   345
         Left            =   2400
         TabIndex        =   35
         TabStop         =   0   'False
         Top             =   960
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":731E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":738A
         Key             =   "AIM_ForecastGenerator.frx":73A8
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtCost 
         Height          =   345
         Left            =   2400
         TabIndex        =   54
         TabStop         =   0   'False
         Top             =   1320
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":73EC
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":7458
         Key             =   "AIM_ForecastGenerator.frx":7476
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtCube 
         Height          =   345
         Left            =   2400
         TabIndex        =   55
         TabStop         =   0   'False
         Top             =   1680
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":74BA
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":7526
         Key             =   "AIM_ForecastGenerator.frx":7544
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText TxtPrice 
         Height          =   345
         Left            =   2400
         TabIndex        =   56
         TabStop         =   0   'False
         Top             =   2040
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_ForecastGenerator.frx":7588
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastGenerator.frx":75F4
         Key             =   "AIM_ForecastGenerator.frx":7612
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label23 
         Caption         =   "Price"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   105
         TabIndex        =   57
         Top             =   2085
         Width           =   2055
      End
      Begin VB.Label Label19 
         Caption         =   "Cube"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   105
         TabIndex        =   40
         Top             =   1725
         Width           =   2055
      End
      Begin VB.Label Label18 
         Caption         =   "Cost"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   105
         TabIndex        =   39
         Top             =   1365
         Width           =   2055
      End
      Begin VB.Label Label17 
         Caption         =   "Weight"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   105
         TabIndex        =   38
         Top             =   1005
         Width           =   2055
      End
      Begin VB.Label Label16 
         Caption         =   "Units"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   105
         TabIndex        =   37
         Top             =   645
         Width           =   2055
      End
      Begin VB.Label Label15 
         Caption         =   "Items"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   105
         TabIndex        =   36
         Top             =   285
         Width           =   2055
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ForecastGenerator"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'Option Explicit
'
'Dim clsAIMAdvCalendar As AIMAdvCalendar
'
'Dim Cn As ADODB.Connection
'
'Dim rsForecast As ADODB.Recordset
'Dim rsVnItemList As ADODB.Recordset
'Dim rsSysCtrl As ADODB.Recordset
'
'Dim xForeCast As XArrayDB
'Dim xForecastItem As XArrayDB
''Sri add code for ProdConst start
'Private AIMYears() As AIMYEARS_RCD
''Sri add code for ProdConst end
'Dim FcstId As String
'Dim VnId As String
'Dim Assort As String
'Dim SAVersion As Integer
'Dim NetRqmts As Boolean
'
'
'Private Function CalcNetRqmts(xNet As XArrayDB, xIt As XArrayDB, xFcst As XArrayDB)
'On Error GoTo ErrorHandler
'
'    Dim AvailQty As Long
'    Dim BuyingUOM As String
'    Dim ConvFactor As Double
'    Dim IMin As Long
'    Dim IMax As Long
'    Dim IPos As Long
'    Dim OrderPt As Long
'    Dim OrderQty As Double
'    Dim PackRounding As String
'    Dim Pd As Integer
'    Dim PlnOrder As Long
'    Dim SOQ As Long
'    Dim UOM As String
'
'    'Size the Availability Array
'    For IPos = xIt.LowerBound(1) To xIt.UpperBound(1)
'        'Initialize availability/planned order quantity
'        AvailQty = xIt.Value(IPos, 21) + xIt.Value(IPos, 22) - xIt.Value(IPos, 23) - xIt.Value(IPos, 24) - xIt.Value(IPos, 25)
'        BuyingUOM = xIt.Value(IPos, 20)
'        ConvFactor = xIt.Value(IPos, 19)
'        IMin = xIt.Value(IPos, 158)
'        IMax = xIt.Value(IPos, 159)
'        OrderPt = xIt.Value(IPos, 36)
'        OrderQty = xIt.Value(IPos, 37)
'        PackRounding = xIt.Value(IPos, 157)
'        PlnOrder = 0
'
'        UOM = xIt.Value(IPos, 18)
'
'        'Calculate Net Requirements for each of the planning periods
'        For Pd = 4 To xFcst.UpperBound(2)
'            SOQ = 0
'
'            'Update the Availability
'            'AvailQty(Pd-1) - Forecast(Pd)
'
'            AvailQty = AvailQty - xFcst.Value(IPos, Pd)
'
'            'Test the Order Point
'            If AvailQty + PlnOrder <= OrderPt And Pd <= xFcst.UpperBound(2) Then
'                SOQ = OrderPt - (AvailQty + PlnOrder) + OrderQty
'
'                'Pack round the planned requirement
'                SOQ = PackRound(CDbl(SOQ), UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
'
'                xNet.Value(IPos, Pd) = SOQ
'
'                'Increment the Planned Order Quantity
'                PlnOrder = PlnOrder + SOQ
'
'            End If
'
'            'Debug.Print Pd, AvailQty, xFcst.Value(IPos, Pd), SOQ, PlnOrder
'
'        Next Pd
'
'        'Update the Net Requirements Value
'        xNet.Value(IPos, 0) = rsVnItemList("item").Value
'        xNet.Value(IPos, 1) = rsVnItemList("lcid").Value
'        xNet.Value(IPos, 2) = rsVnItemList("itdesc").Value
'        xNet.Value(IPos, 3) = PlnOrder
'
'    Next IPos
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(CalcNetRqmts)"
'End Function
'
'Private Function GetDisplayOption()
'On Error GoTo ErrorHandler
'
'    Select Case True
'        'Case Me.ckForecastLevel.Value = vbUnchecked And Me.ckNetRqmts.Value = vbUnchecked
'        Case Me.ckForecastLevel.Value = vbUnchecked And Me.optNetRequirements.Value = False
'            'Do not summarise by item
'            GetDisplayOption = 0
'
'        'Case Me.ckForecastLevel.Value = vbChecked And Me.ckNetRqmts.Value = vbUnchecked
'        Case Me.ckForecastLevel.Value = vbChecked And Me.optNetRequirements.Value = False
'            'Summarize by item
'            GetDisplayOption = 1
'        'Case Me.ckForecastLevel.Value = vbUnchecked And Me.ckNetRqmts.Value = vbChecked
'        Case Me.ckForecastLevel.Value = vbUnchecked And Me.optNetRequirements.Value = True
'            'Do not summarize by item??
'            GetDisplayOption = 2
'        Case Else
'            'Do not summarize by item???
'            GetDisplayOption = 0
'    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetDisplayOption)"
'End Function
'
'Private Function GetForecastInterval() As Integer
'On Error GoTo ErrorHandler
'
'    If Me.optInterval(0).Value = True Then
'        GetForecastInterval = int_Days
'    ElseIf Me.optInterval(1).Value = True Then
'        GetForecastInterval = int_Weeks
'    ElseIf Me.optInterval(2).Value = True Then
'        GetForecastInterval = int_Months
'    ElseIf Me.optInterval(3).Value = True Then
'        GetForecastInterval = int_Quarters
'    ElseIf Me.optInterval(4).Value = True Then
'        GetForecastInterval = int_544
'    ElseIf Me.optInterval(5).Value = True Then
'        GetForecastInterval = int_454
'    ElseIf Me.optInterval(6).Value = True Then
'        GetForecastInterval = int_445
'    ElseIf Me.optInterval(7).Value = True Then
'        GetForecastInterval = int_4Wks
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastInterval)"
'End Function
'
'Private Function GetForecastLevel() As Integer
'On Error GoTo ErrorHandler
'
'    If Me.ckForecastLevel.Value = vbUnchecked Then
'        GetForecastLevel = 0        'Location/Item
'    ElseIf Me.ckForecastLevel.Value = vbChecked Then
'        GetForecastLevel = 1        'Item
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastLevel)"
'End Function
'
'Private Function GetForecastRcd(FcstId As String)
'On Error GoTo ErrorHandler
'
'    Dim AIM_Forecast_GetEq_Sp As ADODB.Command
'    Dim RtnCode As Integer
'
'    'Get the Forecast Record
'    If Trim(FcstId) <> "" Then
'        Set AIM_Forecast_GetEq_Sp = New ADODB.Command
'        With AIM_Forecast_GetEq_Sp
'            Set .ActiveConnection = Cn
'            .CommandType = adCmdStoredProc
'            .CommandText = "AIM_Forecast_GetEq_Sp"
'        End With
'
'        '@FcstId nvarchar(12)
'        With AIM_Forecast_GetEq_Sp.Parameters
'            .Append AIM_Forecast_GetEq_Sp.CreateParameter("@Return", adInteger, adParamReturnValue)
'            .Append AIM_Forecast_GetEq_Sp.CreateParameter("@FcstID", adVarChar, adParamInput, 12, FcstId)
'        End With
'
'        If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
'        Set rsForecast = Nothing
'        Set rsForecast = New ADODB.Recordset
'        rsForecast.Open AIM_Forecast_GetEq_Sp
'    End If
'
'    If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'        FcstId = rsForecast!FcstId
'        RefreshForm
'        SetColumns rsForecast!FcstPds, rsForecast!FcstStartDate, rsForecast!FcstInterval, rsForecast!NetRqmts
'    End If
'
'    If Not (AIM_Forecast_GetEq_Sp Is Nothing) Then Set AIM_Forecast_GetEq_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetEq_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_Forecast_GetEq_Sp Is Nothing) Then Set AIM_Forecast_GetEq_Sp.ActiveConnection = Nothing
'    Set AIM_Forecast_GetEq_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastRcd)"
'End Function
'
'Private Function GetForecastUnit() As Integer
'On Error GoTo ErrorHandler
'
'    'See definition of Public Enum AIM_FORECASTUNITS in AIMForecast.bas
'    If Me.OptForecastUnit(0).Value = True Then
'        GetForecastUnit = FcstUnits
'    ElseIf Me.OptForecastUnit(1).Value = True Then
'        GetForecastUnit = FcstCost
'    ElseIf Me.OptForecastUnit(2).Value = True Then
'        GetForecastUnit = FcstWeight
'    ElseIf Me.OptForecastUnit(3).Value = True Then
'        GetForecastUnit = FcstCube
'    ElseIf Me.OptForecastUnit(4).Value = True Then
'        GetForecastUnit = FcstPrice
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastUnit)"
'End Function
'
'Private Function GetTrendOption() As Integer
'On Error GoTo ErrorHandler
'
'    If Me.optApplyTrend(0).Value = True Then
'        GetTrendOption = 0
'    ElseIf Me.optApplyTrend(1).Value = True Then
'        GetTrendOption = 1
'    Else
'        GetTrendOption = 2
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetTrendOption)"
'End Function
'
'Private Function RefreshForm()
'On Error GoTo ErrorHandler
'
'    'Save the Forecast Table Key
'    FcstId = rsForecast("FcstId").Value
'
'    'Refresh the Form
'    Me.txtFcstDesc.Text = rsForecast("FcstDesc").Value
'    Me.ckForecastLevel.Value = CInt(rsForecast("FcstLevel").Value)
'    'Me.ckNetRqmts.Value = rsForecast("NetRqmts").Value
'    Me.optForecast.Value = True
'    Me.optNetRequirements.Value = rsForecast("NetRqmts").Value
'    Select Case rsForecast("ApplyTrend").Value
'        Case 0
'            Me.optApplyTrend(0).Value = True
'        Case 1
'            Me.optApplyTrend(1).Value = True
'        Case 2
'            Me.optApplyTrend(2).Value = True
'    End Select
'
'    Select Case rsForecast("FcstInterval").Value
'        Case int_Days
'            Me.optInterval(0).Value = True
'        Case int_Weeks
'            Me.optInterval(1).Value = True
'        Case int_Months
'            Me.optInterval(2).Value = True
'        Case int_Quarters
'            Me.optInterval(3).Value = True
'    End Select
'
'    Select Case rsForecast("FcstUnit").Value
'        Case FcstUnits
'            Me.OptForecastUnit(0).Value = True
'        Case FcstCost
'            Me.OptForecastUnit(1).Value = True
'        Case FcstWeight
'            Me.OptForecastUnit(2).Value = True
'        Case FcstCube
'            Me.OptForecastUnit(3).Value = True
'        Case FcstPrice
'            Me.OptForecastUnit(4).Value = True
'    End Select
'
'    Me.txtFcstPds.Value = rsForecast("FcstPds").Value
'    Me.txtVnId.Text = rsForecast("VnId").Value
'    Me.txtAssort.Text = rsForecast("Assort").Value
'    Me.txtLcId.Text = rsForecast("LcId").Value
'    Me.txtItem.Text = rsForecast("Item").Value
'    Me.txtItemStatus.Text = rsForecast!ItStat
'    Me.txtClass(1).Text = rsForecast("Class1").Value
'    Me.txtClass(2).Text = rsForecast("Class2").Value
'    Me.txtClass(3).Text = rsForecast("Class3").Value
'    Me.txtClass(4).Text = rsForecast("Class4").Value
'    Me.txtById.Text = rsForecast("ById").Value
'    Me.txtAdjustPct.Value = rsForecast("AdjustPct").Value
'
'    SetTDBDate txtFcstStartDate, rsForecast!FcstStartDate
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(RefreshForm)"
'End Function
'
'Private Function SaveForecast()
'On Error GoTo ErrorHandler
'
'    'Refresh the Form
'    Cn.Errors.Clear
'
'    If Trim(Me.dcForecast.Text) = "" Then
'        Exit Function
'    End If
'
'    rsForecast("FcstDesc").Value = Me.txtFcstDesc.Text
'    rsForecast("FcstLevel").Value = Me.ckForecastLevel.Value
'    'rsForecast("NetRqmts").Value = Me.ckNetRqmts.Value
'    rsForecast("NetRqmts").Value = Me.optNetRequirements.Value
'    rsForecast("FcstInterval").Value = GetForecastInterval()
'    rsForecast("FcstUnit").Value = GetForecastUnit()
'    rsForecast("FcstPds").Value = Me.txtFcstPds.Value
'    rsForecast("VnId").Value = Me.txtVnId.Text
'    rsForecast("Assort").Value = Me.txtAssort.Text
'    rsForecast("LcId").Value = Me.txtLcId.Text
'    rsForecast("Item").Value = Me.txtItem.Text
'    rsForecast("ItStat").Value = Me.txtItemStatus.Text
'    rsForecast("Class1").Value = Me.txtClass(1).Text
'    rsForecast("Class2").Value = Me.txtClass(2).Text
'    rsForecast("Class3").Value = Me.txtClass(3).Text
'    rsForecast("Class4").Value = Me.txtClass(4).Text
'    rsForecast("ById").Value = Me.txtById.Text
'    rsForecast("FcstStartDate").Value = Format(Me.txtFcstStartDate.Text, gDateFormat)
'    rsForecast("AdjustPct").Value = Me.txtAdjustPct.Value
'    rsForecast("ApplyTrend").Value = GetTrendOption()
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SaveForecast)"
'End Function
'
'Private Function SizeForm(FirstTime As Boolean)
'On Error GoTo ErrorHandler
'
'    If FirstTime Then
'        Me.Top = 0
'        Me.Left = 0
'        Me.Width = AIM_Main.ScaleWidth
'        Me.Height = AIM_Main.ScaleHeight
'    End If
'
'    If Me.WindowState <> vbMinimized Then
'        'Size Forecast Tab
'        Me.atForecast.Left = Me.frmForecast.Left
'        Me.atForecast.Top = Me.atForecastHeader.Height + 100
'        If (Me.Width - 150) > 0 Then
'            Me.atForecast.Width = Me.Width - 150
'        End If
'        If ((Me.ScaleHeight - Me.atForecastHeader.Height) - 150) > 0 Then
'            Me.atForecast.Height = (Me.ScaleHeight - Me.atForecastHeader.Height) - 150
'        End If
'
'        'Size the Grid
'        If (Me.Width - 250) > 0 Then
'            Me.dgForecast.Width = Me.Width - 250
'        End If
'        If (Me.atpForecast.Height - 100) > 0 Then
'            Me.dgForecast.Height = Me.atpForecast.Height - 100
'        End If
'
'        'Size the Graph
'        If (Me.atpGraph.Width - (2 * Me.chtForecast.Left)) > 0 Then
'            Me.chtForecast.Width = Me.atpGraph.Width - (2 * Me.chtForecast.Left)
'        End If
'        If (Me.atpGraph.Height - (2 * Me.chtForecast.Top)) > 0 Then
'            Me.chtForecast.Height = Me.atpGraph.Height - (2 * Me.chtForecast.Top)
'        End If
'    End If
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SizeForm)"
'End Function
'
'Private Function SummarizeByItem()
'On Error GoTo ErrorHandler
'
'    Dim i As Integer
'    Dim p As Long
'    Dim PrevItem As String
'    Dim Rows As Long
'
'    'Determine number of rows, and size Item Summary Array
'    PrevItem = ""
'    For p = xForeCast.LowerBound(1) To xForeCast.UpperBound(1)
'        If PrevItem <> xForeCast.Value(p, 0) Then
'            Rows = Rows + 1
'            PrevItem = xForeCast.Value(p, 0)
'        End If
'    Next p
'
'    xForecastItem.Clear
'    xForecastItem.ReDim 0, Rows - 1, xForeCast.LowerBound(2), xForeCast.UpperBound(2)
'
'    'Update the Totals
'    PrevItem = ""
'    Rows = 0
'    For p = xForeCast.LowerBound(1) To xForeCast.UpperBound(1)
'        Select Case PrevItem                                            'Item Number
'            Case Is = ""
'                 'Update Header Data
'                xForecastItem.Value(Rows, 0) = xForeCast.Value(p, 0)        'Item Number
'                xForecastItem.Value(Rows, 1) = getTranslationResource("All")                        'Location Id
'                xForecastItem.Value(Rows, 2) = xForeCast.Value(p, 2)        'Description
'
'                'Initialize Demand Data
'                For i = 3 To xForeCast.UpperBound(2)
'                    xForecastItem(Rows, i) = xForeCast(p, i)
'                Next i
'
'            Case Is <> xForeCast.Value(p, 0)
'                Rows = Rows + 1
'
'                'Update Header Data
'                xForecastItem.Value(Rows, 0) = xForeCast.Value(p, 0)        'Item Number
'                xForecastItem.Value(Rows, 1) = getTranslationResource("All")                        'Location Id
'                xForecastItem.Value(Rows, 2) = xForeCast.Value(p, 2)        'Description
'
'                'Initialize Demand Data
'                For i = 3 To xForeCast.UpperBound(2)
'                    xForecastItem(Rows, i) = xForeCast(p, i)
'                Next i
'
'            Case Is = xForeCast.Value(p, 0)
'                For i = 3 To xForeCast.UpperBound(2)
'                    xForecastItem.Value(Rows, i) = xForecastItem.Value(Rows, i) + xForeCast.Value(p, i)
'                Next i
'
'        End Select
'
'        PrevItem = xForeCast.Value(p, 0)
'
'    Next p
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SummarizeByItem)"
'End Function
'
'Private Function UpdateFooters()
'On Error GoTo ErrorHandler
'
''    Dim col As Integer
''    Dim row As Integer
''    Dim Total As Double
''
''    Me.dbgForecast.Columns(0).FooterText = getTranslationResource("All")
''    Me.dbgForecast.Columns(1).FooterText = getTranslationResource("All")
''    Me.dbgForecast.Columns(2).FooterText = getTranslationResource("Totals")
''
''    Select Case GetDisplayOption
''    Case 0      'Location/Item
''
''        For col = 3 To xForecast.UpperBound(2)
''
''            Total = 0
''            For row = xForecast.LowerBound(1) To xForecast.UpperBound(1)
''                Total = Total + xForecast.Value(row, col)
''            Next row
''
''            Me.dbgForecast.Columns(col).FooterText = Format(Total, "#,##0")
''
''        Next col
''
''    Case 1      'Item
''
''        For col = 3 To xForecastItem.UpperBound(2)
''
''            Total = 0
''            For row = xForecastItem.LowerBound(1) To xForecastItem.UpperBound(1)
''                Total = Total + xForecastItem.Value(row, col)
''            Next row
''
''            Me.dbgForecast.Columns(col).FooterText = Format(Total, "#,##0")
''
''        Next col
''
''    Case 2      'Net Requirements
''
''        For col = 3 To xNetRqmts.UpperBound(2)
''
''            Total = 0
''            For row = xNetRqmts.LowerBound(1) To xNetRqmts.UpperBound(1)
''                Total = Total + xNetRqmts.Value(row, col)
''            Next row
''
''            Me.dbgForecast.Columns(col).FooterText = Format(Total, "#,##0")
''
''        Next col
''
''    End Select
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(UpdateFooters)"
'End Function
'
'Private Function UpdateGraph(Row As Long, Nbrpds As Integer, DisplayOption As Integer)
'On Error GoTo ErrorHandler
'
'    'Display Option
'    '0=Forecast by Item/Location
'    '1=Forecast by Item
'    '2=Net Requirements
'
'    Dim i As Integer
'    Dim AverageInventory As Double
'
'    Me.chtForecast.IsBatched = True
'
'    With Me.chtForecast
'        .AllowUserChanges = False
'
'        'Set up Header
'        Select Case DisplayOption
'            Case 0
'                .Header.Text = Trim(xForeCast.Value(Row, 1)) & getTranslationResource("/") & _
'                        xForeCast.Value(Row, 0) & getTranslationResource(" - ") & xForeCast.Value(Row, 2)
'            Case 1
'                .Header.Text = Trim(xForecastItem.Value(Row, 1)) & getTranslationResource("/") & _
'                        xForecastItem.Value(Row, 0) & getTranslationResource(" - ") & xForecastItem.Value(Row, 2)
'            Case 2
'            'la here -- why? exactly the same as case 0
'                .Header.Text = Trim(xForeCast.Value(Row, 1)) & getTranslationResource("/") & _
'                        xForeCast.Value(Row, 0) & getTranslationResource(" - ") & xForeCast.Value(Row, 2)
'        End Select
'
'        'Set X and Y Axes
'        .ChartArea.Axes("X").Title.Text = getTranslationResource("Periods")
'        .ChartArea.Axes("X").Max = Nbrpds
'
'        .ChartArea.Axes("Y").Title.Text = ""
'
'        'Remove any old x-axis labels
'        .ChartArea.Axes("X").ValueLabels.RemoveAll
'
'        'Set up Legends
'        .Legend.Text = ""
'
'        .ChartGroups(1).SeriesLabels.RemoveAll
''        .ChartGroups(1).SeriesLabels.Add ("On Hand")
''        .ChartGroups(1).SeriesLabels.Add ("Safety Stock")
''        .ChartGroups(1).SeriesLabels.Add ("Avg Inventory")
'
'        'Set up Data Series
'        .ChartGroups(1).Data.NumSeries = 1
'        .ChartGroups(1).Data.NumPoints(1) = Nbrpds
'
'        'Load Data
'         For i = 1 To Nbrpds
'            'Set the x axes label
'            .ChartArea.Axes("X").ValueLabels.Add i, Format(i, "#0")
'
'            'Set the data value
'            Select Case DisplayOption
'            Case 0          'Location/Item
'                .ChartGroups(1).Data.Y(1, i) = xForeCast.Value(Row, i + 3)
'            Case 1
'                .ChartGroups(1).Data.Y(1, i) = xForecastItem.Value(Row, i + 3)
'            Case 2
'            'la here -- why? exactly the same as case 0
'                .ChartGroups(1).Data.Y(1, i) = xForeCast.Value(Row, i + 3)
'            End Select
'        Next i
'
'        'Finalize Grid Spacing
'        .ChartArea.Axes("Y").MajorGrid.Spacing = .ChartArea.Axes("Y").NumSpacing
'    End With
'
'    'Redisplay graph
'    Me.chtForecast.IsBatched = False
'
'Exit Function
'ErrorHandler:
'    Me.chtForecast.IsBatched = False
'    Err.Raise Err.Number, Err.Source, Err.Description & "(UpdateGraph)"
'End Function
'
'Private Function SetColumns(ByVal Pds As Integer, ByVal BaseDate As Date, ByVal Interval As AIM_INTERVALS, NetRqmts As Integer)
'On Error GoTo ErrorHandler
''    Dim AdvForecast As AIMAdvForecast      'A.Stocksdale 2003/12/10
'
'    Dim CurStartdate As Date
'    Dim i As Integer
'
''    Set AdvForecast = New AIMAdvForecast   'A.Stocksdale 2003/12/10
'
'    Me.dgForecast.Enabled = False
'
'    'Clear any existing columns
'    Me.dgForecast.Columns.RemoveAll
'    'Me.dgForecast.Reset
'
'    'Set up the Grid
'    Me.dgForecast.Columns.Add 0
'    Me.dgForecast.Columns(0).Name = "item"
'    Me.dgForecast.Columns(0).Caption = getTranslationResource("Item")
'    Me.dgForecast.Columns(0).Width = 1440
'    Me.dgForecast.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgForecast.Columns(0).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgForecast.Columns.Add 1
'    Me.dgForecast.Columns(1).Name = "lcid"
'    Me.dgForecast.Columns(1).Caption = getTranslationResource("Location")
'    Me.dgForecast.Columns(1).Width = 800
'    Me.dgForecast.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgForecast.Columns(1).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgForecast.Columns.Add 2
'    Me.dgForecast.Columns(2).Name = "itdesc"
'    Me.dgForecast.Columns(2).Caption = getTranslationResource("Description")
'    Me.dgForecast.Columns(2).Width = 2880
'    Me.dgForecast.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgForecast.Columns(2).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgForecast.Columns.Add 3
'    Me.dgForecast.Columns(3).Name = "totals"
'    Me.dgForecast.Columns(3).Caption = getTranslationResource("Totals")
'    Me.dgForecast.Columns(3).Width = 1000
'    Me.dgForecast.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dgForecast.Columns(3).Alignment = ssCaptionAlignmentRight
'    Me.dgForecast.Columns(3).NumberFormat = "#,##0"
'    Me.dgForecast.Columns(3).DataType = vbDouble
'
'    CurStartdate = BaseDate
'    For i = 4 To Pds + 3
'        Me.dgForecast.Columns.Add i
'        Me.dgForecast.Columns(i).Name = "fcst" + Format(i, "00")
'        Me.dgForecast.Columns(i).Caption = Format(CurStartdate, gDateFormat)
'        Me.dgForecast.Columns(i).Width = 1200
'        Me.dgForecast.Columns(i).CaptionAlignment = ssColCapAlignLeftJustify
'        Me.dgForecast.Columns(i).Alignment = ssCaptionAlignmentRight
'        Me.dgForecast.Columns(i).NumberFormat = "#,##0"
'        Me.dgForecast.Columns(i).DataType = vbDouble
'
'        'Get the Next Start date
'        CurStartdate = GetNextStartDate(BaseDate, CurStartdate, Interval)
'    Next i
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgForecast, ACW_EXPAND
'    End If
'
'    'Clean Up
''    Set AdvForecast = Nothing      'A.Stocksdale 2003/12/10
'
'    'Bind the Grid
'    If ckForecastLevel.Value = vbChecked Then
'        Me.dgForecast.Rows = xForecastItem.Count(1)
'    Else
'        Me.dgForecast.Rows = xForeCast.Count(1)
'    End If
'
'    Me.dgForecast.Enabled = True
'
'    Me.dgForecast.ReBind
'
'Exit Function
'ErrorHandler:
'    'Set AdvForecast = Nothing      'A.Stocksdale 2003/12/10
'    Err.Raise Err.Number, Err.Source, Err.Description & "(SetColumns)"
'End Function
'
'Private Function UpdateForecast(StartDate As Date, Rows As Long, Nbrpds As Integer, _
'    Interval As AIM_INTERVALS, ApplyTrend As Integer, FcstUnit As AIM_FORECASTUNITS, _
'    FcstLevel As Integer, AdjustPct As Double, NetRqmts As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim DisplayOption As Integer
'    Dim FcstCount As Long
'    Dim FiscalYear As Long
'    Dim Pd As Long
'    Dim i As Integer
'    Dim Row As Long
'    Dim strMessage As String
'
'    'Control Variables
'    Dim PrevItem As String
'
'    'Group Totals
'    Dim Items As Integer
'    Dim Units As Long
'    Dim Weight As Double
'    Dim Cost As Double
'    Dim Cube As Double
'    Dim Price As Double
'
'    Dim tempFcst As Long
'    'Sri add code for ProdConst start
'    Dim TempFcstUnit As Long
'    Dim ThisFYPrice As Double
'    Dim ThisFYCost As Double
'    Dim LastFYPrice As Double
'    Dim LastFYCost As Double
'    Dim NextFYPrice As Double
'    Dim NextFYCost As Double
'    Dim FyYear As Integer
'    Dim CurDate As Date
'    Dim StartYear As Integer
'    Dim CurrentPrice As Double
'    Dim CurrentCost As Double
'    Dim CurrentWeight As Double
'    Dim CurrentCube As Double
'    Dim ItemSumUnits As Double
'    Dim ItemSumCost As Double
'    Dim ItemSumPrice As Double
'
'    Dim xProdConst As XArrayDB
'    Dim xForeCastUnit As XArrayDB
'   'Sri add code for ProdConst end
'    Dim AdvForecast As AIMAdvForecast
''    Dim clsAdvForecastMod As AIMAdvForecastMod
'    Dim StatusPercent As Integer
'
'    Set xProdConst = New XArrayDB
'    Set xForeCastUnit = New XArrayDB
'
''    Set AdvForecast = New AIMAdvForecast
''    Set clsAdvForecastMod = New AIMAdvForecastMod
'
''    'Get the display option
''    DisplayOption = GetDisplayOption()
'
'    'Redimension the Forecast Array
'
'    'Sri add code for ProdConst start
'    'If Check1.Value = vbUnchecked Then
'     If Me.optProductionConstraint.Value = False Then
'        xForeCast.ReDim 0, (Rows - 1), 0, (Nbrpds + 3)
'    Else
'        'Sri add 8 new columns at the end of the array
'        xForeCast.ReDim 0, (Rows - 1), 0, (Nbrpds + 11)
'        xForeCastUnit.ReDim 0, (Rows - 1), 0, (Nbrpds + 11)
'        xProdConst.ReDim 0, (Rows - 1), 0, (Nbrpds + 11)
'        AC_LoadAIMYears Cn, AIMYears()  'LoadAIMYears
'        CurDate = Date
'        FyYear = GetFiscalYear(CurDate, AIMYears())
'    End If
'
'    'Sri add code for ProdConst end
'    For Row = 0 To (Rows - 1)
'        'Position the recordset
'        rsVnItemList.AbsolutePosition = Row + 1
'
'        'Reinitialize the forecast object
'        Set AdvForecast = New AIMAdvForecast
'
'        'Set the Calendar
'        Set AdvForecast.AIMAdvCalendar = clsAIMAdvCalendar
'
'        'Update Status
'        xForeCast.Value(Row, 0) = rsVnItemList("item").Value
'        xForeCast.Value(Row, 1) = rsVnItemList("lcid").Value
'        xForeCast.Value(Row, 2) = rsVnItemList("itdesc").Value
'        'Sri add code for ProdConst start
'        'If Check1.Value = vbChecked Then
'        If Me.optProductionConstraint.Value = True Then
'            xForeCastUnit.Value(Row, 0) = rsVnItemList("item").Value
'            xForeCastUnit.Value(Row, 1) = rsVnItemList("lcid").Value
'            xForeCastUnit.Value(Row, 2) = rsVnItemList("itdesc").Value
'        End If
'       'Sri add code for ProdConst end
'        'Get the Base Date
'        FiscalYear = rsVnItemList("fcstupdcyc").Value \ 100               'Forecast Update Period
'        Pd = rsVnItemList("fcstupdcyc").Value - (FiscalYear * 100)
'
'        AdvForecast.ApplySeasonsIndex = IIf(rsVnItemList("applyseasonsindex").Value = "Y", True, False)
'        AdvForecast.ApplyTrend = ApplyTrend
'        AdvForecast.BaseDate = clsAIMAdvCalendar.GetEndDateFmPeriod(CInt(Pd), CInt(FiscalYear)) + 1
'        AdvForecast.Cost = rsVnItemList("cost").Value
'        AdvForecast.Cube = rsVnItemList("cube").Value
'        AdvForecast.Price = rsVnItemList("price").Value
'        AdvForecast.DmdScalingFactor = rsVnItemList("dmdscalingfactor").Value
'        AdvForecast.ScalingEffUntil = rsVnItemList("scalingeffuntil").Value
'        AdvForecast.FcstDemand = rsVnItemList("fcstdemand").Value
'        AdvForecast.FcstUnit = FcstUnit
'        AdvForecast.HiTrndDemandLimit = rsVnItemList("hitrndl").Value
'        AdvForecast.Item = rsVnItemList("item").Value
'        AdvForecast.ItStat = rsVnItemList("itstat").Value
'        AdvForecast.InActDate = rsVnItemList("inactdate").Value
'        AdvForecast.LcId = rsVnItemList("lcid").Value
'        AdvForecast.PlnTT = rsVnItemList("plntt").Value
'        AdvForecast.UserFcst = rsVnItemList("userfcst").Value
'        AdvForecast.UserFcstExpDate = rsVnItemList("userfcstexpdate").Value
'        AdvForecast.MAE = rsVnItemList("mae").Value
'        AdvForecast.Trend = rsVnItemList("trend").Value
'        AdvForecast.Weight = rsVnItemList("weight").Value
'        AdvForecast.AdjustPct = AdjustPct
'
'        'Set Availability Variables
'        AdvForecast.UOM = rsVnItemList("uom").Value
'        AdvForecast.ConvFactor = rsVnItemList("convfactor").Value
'        AdvForecast.BuyingUOM = rsVnItemList("buyinguom").Value
'        AdvForecast.Oh = rsVnItemList("oh").Value
'        AdvForecast.Oo = rsVnItemList("oo").Value
'        AdvForecast.ComStk = rsVnItemList("comstk").Value
'        AdvForecast.BkOrder = rsVnItemList("bkorder").Value
'        AdvForecast.BkComStk = rsVnItemList("bkcomstk").Value
'
'        'Set Order Policy Variables
'        AdvForecast.Accum_Lt = rsVnItemList("accum_lt").Value
'        AdvForecast.ReviewTime = rsVnItemList("reviewtime").Value
'        'AdvForecast.OrderPt = rsVnItemList("orderpt").Value
'        'AdvForecast.OrderQty = rsVnItemList("orderqty").Value
'        'AdvForecast.SafetyStock = rsVnItemList("safetystock").Value
'        'AdvForecast.FcstRT = rsVnItemList("fcstrt").Value
'        'AdvForecast.FcstLt = rsVnItemList("fcstlt").Value
'        'AdvForecast.Fcst_Month = rsVnItemList("fcst_month").Value
'        'AdvForecast.Fcst_Quarter = rsVnItemList("fcst_quarter").Value
'        'AdvForecast.Fcst_Year = rsVnItemList("fcst_year").Value
'        AdvForecast.PackRounding = rsVnItemList("packrounding").Value
'        AdvForecast.IMin = rsVnItemList("imin").Value
'        AdvForecast.IMax = rsVnItemList("imax").Value
'        AdvForecast.CStock = rsVnItemList("cstock").Value
'        AdvForecast.IntSafetyStock = rsVnItemList("intsafetystock").Value
'        AdvForecast.IsIntermittent = IIf(rsVnItemList("isintermittent").Value = "Y", True, False)
'        AdvForecast.Mean_NZ = rsVnItemList("mean_nz").Value
'        AdvForecast.StdDev_NZ = rsVnItemList("stddev_nz").Value
'        AdvForecast.ReplenCost2 = rsVnItemList("replencost2").Value
'        AdvForecast.SSAdj = rsVnItemList("ssadj").Value
'        AdvForecast.ZSStock = rsVnItemList("zsstock").Value
'        AdvForecast.LtvFact = rsVnItemList("ltvfact").Value
'        AdvForecast.DSer = rsVnItemList("dser").Value
'        AdvForecast.HiMADP = rsVnItemList("himadp").Value
'        AdvForecast.LoMADP = rsVnItemList("lomadp").Value
'        AdvForecast.MADExK = rsVnItemList("madexk").Value
'        AdvForecast.ReplenCost = rsVnItemList("replencost").Value
'
'        AdvForecast.Int_Enabled = rsSysCtrl("Int_Enabled").Value
'        AdvForecast.Int_LookBackPds = rsSysCtrl("Int_LookBackPds").Value
'        AdvForecast.KFactor = rsSysCtrl("KFactor").Value
'        AdvForecast.CarryFwdRoundingOption = IIf(rsSysCtrl("CarryForwardRounding").Value = "Y", True, False)
'
'        'Set Review Cycle Parameters
'        AdvForecast.NextDateTime = rsVnItemList("nextdatetime").Value
'        AdvForecast.RevFreq = rsVnItemList("revfreq").Value
'        AdvForecast.RevInterval = rsVnItemList("revinterval").Value
'        AdvForecast.RevSunday = rsVnItemList("revsunday").Value
'        AdvForecast.RevMonday = rsVnItemList("revmonday").Value
'        AdvForecast.RevTuesday = rsVnItemList("revtuesday").Value
'        AdvForecast.RevWednesday = rsVnItemList("revwednesday").Value
'        AdvForecast.RevThursday = rsVnItemList("revthursday").Value
'        AdvForecast.RevFriday = rsVnItemList("revfriday").Value
'        AdvForecast.RevSaturday = rsVnItemList("revsaturday").Value
'        AdvForecast.WeekQualifier = rsVnItemList("weekqualifier").Value
'        AdvForecast.RevStartDate = rsVnItemList("revstartdate").Value
'        AdvForecast.RevEndDate = rsVnItemList("revenddate").Value
'        AdvForecast.InitBuyPct = rsVnItemList("initbuypct").Value
'        AdvForecast.InitRevDate = rsVnItemList("initrevdate").Value
'        AdvForecast.ReviewTime = rsVnItemList("reviewtime").Value
'        AdvForecast.Dft_TurnHigh = rsVnItemList("dft_turnhigh").Value
'        AdvForecast.Dft_TurnLow = rsVnItemList("dft_turnlow").Value
'        AdvForecast.StkDate = rsVnItemList("stkdate").Value
'        'Sri add code for ProdConst start
'        AdvForecast.BuyStrat = rsVnItemList("BuyStrat").Value
'        AdvForecast.UserMin = rsVnItemList("UserMin").Value
'        AdvForecast.UserMax = rsVnItemList("UserMax").Value
'        AdvForecast.DIFlag = rsVnItemList("DIFlag").Value
'        AdvForecast.DIMADP = rsVnItemList("DIMADP").Value
'        'Sri add code for ProdConst end
'        'Set Promotional Data
'        If rsVnItemList("pmstatus").Value Then
'            AdvForecast.PmStatus = rsVnItemList("pmstatus").Value
'            AdvForecast.PmStartDate = rsVnItemList("pmstartdate").Value
'            AdvForecast.PmEndDate = rsVnItemList("pmenddate").Value
'
'            For i = 1 To 52
'                AdvForecast.PmAdj(i) = rsVnItemList(102 + i).Value
'            Next i
'        End If
'
'        For i = 1 To 52
'            AdvForecast.Bi(i) = rsVnItemList(44 + i).Value
'        Next i
'
'        'Set Item Price Breaks
'        For i = 1 To 10
'            AdvForecast.BkQty(i) = rsVnItemList(190 + (2 * i)).Value
'            AdvForecast.BkCost(i) = rsVnItemList(191 + (2 * i)).Value
'        Next i
'
'        'Set Net Requirements
'        AdvForecast.NetRqmtsOpt = NetRqmts
'
'        strMessage = getTranslationResource("STATMSG07203")
'        If StrComp(strMessage, "STATMSG07203") = 0 Then strMessage = "Updating Forecast..."
'        StatusPercent = 100 * (Row / Rows)
'        strMessage = CStr(StatusPercent) & getTranslationResource("% complete. ") & strMessage
'        Write_Message strMessage & " " & Trim(AdvForecast.LcId) & getTranslationResource("/") & AdvForecast.Item
'
'        'Update forecast
'        FcstCount = AdvForecast.GenerateForecast(StartDate, Nbrpds, Interval)
'        'Sri add code for ProdConst start
'        'If Check1.Value = vbUnchecked Then
'        If Me.optProductionConstraint.Value = False Then
'        'Sri add code for ProdConst end
'            'Update Totals
'            Select Case FcstUnit
'                Case 0      'Units
'                    xForeCast.Value(Row, 3) = IIf(NetRqmts, AdvForecast.xTotalUnits, AdvForecast.TotalUnits)
'                Case 1      'Cost
'                    xForeCast.Value(Row, 3) = IIf(NetRqmts, AdvForecast.xTotalCost, AdvForecast.TotalCost)
'                Case 2      'Cube
'                    xForeCast.Value(Row, 3) = IIf(NetRqmts, AdvForecast.xTotalCube, AdvForecast.TotalCube)
'                Case 3      'Weight
'                    xForeCast.Value(Row, 3) = IIf(NetRqmts, AdvForecast.xTotalWeight, AdvForecast.TotalWeight)
'                 Case 4      'Price
'                    xForeCast.Value(Row, 3) = IIf(NetRqmts, AdvForecast.xTotalPrice, AdvForecast.TotalPrice)
'            End Select
'
'            For i = 4 To (Nbrpds + 3)
'                If NetRqmts Then
'                    xForeCast.Value(Row, i) = AdvForecast.NetRqmt(i - 3, FcstUnit)
'                Else
'                    tempFcst = AdvForecast.Fcst(i - 3, FcstUnit)
'                    If tempFcst <> -1 Then
'                        xForeCast.Value(Row, i) = tempFcst
'                    Else
'                        Exit For
'                    End If
'                End If
'            Next i
'
'            If NetRqmts Then
'                Items = Items + 1
'                Units = Units + AdvForecast.xTotalUnits
'                Weight = Weight + AdvForecast.xTotalWeight
'                Cost = Cost + AdvForecast.xTotalCost
'                Cube = Cube + AdvForecast.xTotalCube
'                Price = Price + AdvForecast.xTotalPrice
'            Else
'                Items = Items + 1
'                Units = Units + AdvForecast.TotalUnits
'                Weight = Weight + AdvForecast.TotalWeight
'                Cost = Cost + AdvForecast.TotalCost
'                Cube = Cube + AdvForecast.TotalCube
'                Price = Price + AdvForecast.TotalPrice
'            End If
'        End If 'unchecked
'
'        'Sri add code for ProdConst start
'        'If Check1.Value = vbChecked Then
'        If Me.optProductionConstraint.Value = True Then
'            xForeCastUnit.Value(Row, Nbrpds + 4) = rsVnItemList("cube").Value
'            xForeCastUnit.Value(Row, Nbrpds + 5) = rsVnItemList("weight").Value
'            xForeCastUnit.Value(Row, Nbrpds + 6) = AdvForecast.LastYearPrice
'            xForeCastUnit.Value(Row, Nbrpds + 7) = AdvForecast.ThisYearPrice
'            xForeCastUnit.Value(Row, Nbrpds + 8) = AdvForecast.NextYearPrice
'            xForeCastUnit.Value(Row, Nbrpds + 9) = AdvForecast.LastYearCost
'            xForeCastUnit.Value(Row, Nbrpds + 10) = AdvForecast.ThisYearCost
'            xForeCastUnit.Value(Row, Nbrpds + 11) = AdvForecast.NextYearCost
'
'            For i = 4 To (Nbrpds + 3)
'                xForeCastUnit.Value(Row, i) = AdvForecast.NetRqmt(i - 3, FcstUnits)
'            Next i
'        End If
'        'Sri add code for ProdConst end
'
'        'Get rid of the current forecast object
'        Set AdvForecast = Nothing
'
'    Next Row
'    'Sri add code for ProdConst start
'    'If Check1.Value = vbChecked Then
'    If Me.optProductionConstraint.Value = True Then
'        'Populate xForecastUnit array into xProdConst array
'        Call PopulateFromArrayToToArray(xForeCastUnit, xProdConst)
'        'Apply ProdConst logic to XProdConst array
'        'Populate the xProdConst array into xForeCast array
'        Call PopulateFromArrayToToArray(xProdConst, xForeCast)
'
'        For Row = 0 To (Rows - 1)
'        'Initilize Item sum
'            ItemSumUnits = 0
'            ItemSumCost = 0
'            ItemSumPrice = 0
'            For i = 4 To (Nbrpds + 3)
'                'Initilize CurrentPrice and CurrentCost
'                 CurrentPrice = 0
'                 CurrentCost = 0
'
'                'GetCurrentPriceCost CDate(Me.dgForecast.Columns(i).Caption), CurrentPrice, CurrentCost
'
'                StartYear = GetFiscalYear(CDate(Me.dgForecast.Columns(i).Caption), AIMYears())
'
'                If StartYear = FyYear Then
'                    CurrentPrice = xForeCast.Value(Row, Nbrpds + 7)
'                    CurrentCost = xForeCast.Value(Row, Nbrpds + 10)
'                ElseIf StartYear < FyYear Then
'                    CurrentPrice = xForeCast.Value(Row, Nbrpds + 6)
'                    CurrentCost = xForeCast.Value(Row, Nbrpds + 9)
'                ElseIf StartYear > FyYear Then
'                    CurrentPrice = xForeCast.Value(Row, Nbrpds + 8)
'                    CurrentCost = xForeCast.Value(Row, Nbrpds + 11)
'                End If
'                CurrentCube = xForeCast.Value(Row, Nbrpds + 4)
'                CurrentWeight = xForeCast.Value(Row, Nbrpds + 5)
'
'                ItemSumUnits = ItemSumUnits + xForeCast.Value(Row, i)
'                ItemSumCost = ItemSumCost + xForeCast.Value(Row, i) * CurrentCost
'                ItemSumPrice = ItemSumPrice + xForeCast.Value(Row, i) * CurrentPrice
'
'                Select Case FcstUnit
'                    Case 0      'Units
'                        xForeCast.Value(Row, i) = xForeCast.Value(Row, i)
'                    Case 1      'Cost
'                        xForeCast.Value(Row, i) = xForeCast.Value(Row, i) * CurrentCost
'
'                    Case 2      'Cube
'                        xForeCast.Value(Row, i) = xForeCast.Value(Row, i) * CurrentCube
'
'                    Case 3      'Weight
'                        xForeCast.Value(Row, i) = xForeCast.Value(Row, i) * CurrentWeight
'
'                     Case 4      'Price
'                        xForeCast.Value(Row, i) = xForeCast.Value(Row, i) * CurrentPrice
'
'                End Select
'
'            Next i
'            Select Case FcstUnit
'                Case 0      'Units
'                    xForeCast.Value(Row, 3) = ItemSumUnits
'
'                Case 1      'Cost
'                    xForeCast.Value(Row, 3) = ItemSumCost
'                Case 2      'Cube
'                    xForeCast.Value(Row, 3) = ItemSumUnits * CurrentCube
'                Case 3      'Weight
'                   xForeCast.Value(Row, 3) = ItemSumUnits * CurrentWeight
'                 Case 4      'Price
'                    xForeCast.Value(Row, 3) = ItemSumPrice
'            End Select
'            Items = Items + 1
'            Units = Units + ItemSumUnits
'            Weight = Weight + ItemSumUnits * CurrentWeight
'            Cost = Cost + ItemSumCost
'            Cube = Cube + ItemSumUnits * CurrentCube
'            Price = Price + ItemSumPrice
'       Next Row
'       'Remove the added 8 columns from the xforecast,xforecastunit,xprodconst arrays
'       xForeCast.DeleteColumns Nbrpds + 4, 8
'       xForeCastUnit.DeleteColumns Nbrpds + 4, 8
'       xProdConst.DeleteColumns Nbrpds + 4, 8
'    End If
'    'Sri add code for ProdConst end
'
'    'Calculate Totals by Item; if requested
'    If FcstLevel = 1 Then      'Item Summary
'        SummarizeByItem
'        Me.dgForecast.Rows = xForecastItem.Count(1)
'    Else
'        Me.dgForecast.Rows = xForeCast.Count(1)
'    End If
'
'    Me.dgForecast.ReBind
'
'    'Update the Totals
'    Me.txtItems.Text = Format(Items, "#,##0")
'    Me.txtUnits.Text = Format(Units, "#,##0")
'    Me.txtWeight.Text = Format(Weight, "#,##0.00")
'    Me.txtCost.Text = Format(Cost, "#,##0.00")
'    Me.txtCube.Text = Format(Cube, "#,##0.00")
'    Me.TxtPrice.Text = Format(Price, "#,##0.00")
'    'Update the footers
'    'UpdateFooters
'    'Update the graph
'    If Not Me.dgForecast.bookmark = vbNull Then
'        Row = CLng(Me.dgForecast.bookmark)
'        UpdateGraph Row, Nbrpds, GetDisplayOption()
'
'    End If
'
'    Write_Message ""
'
'Exit Function
'ErrorHandler:
'    Set xProdConst = Nothing
'    Set xForeCastUnit = Nothing
'
'    Set AdvForecast = Nothing
''    Set clsAdvForecastMod = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(UpdateForecast)"
'End Function
'
'Private Function PopulateFromArrayToToArray(ArgFromArray As XArrayDB, ArgToArray As XArrayDB) As Integer
''Sri add code for prodconst new function
'On Error GoTo ErrorHandler
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim RowEndIndex As Integer
'    Dim ColEndIndex As Integer
'    PopulateFromArrayToToArray = -1
'    If ArgFromArray.Count(1) = 0 Then
'        Exit Function
'    End If
'    'Initilze the arrays
'   'Set ArgToArray = Nothing
'    RowEndIndex = ArgFromArray.UpperBound(1)
'    ColEndIndex = ArgFromArray.UpperBound(2)
'    'ArgToArray.ReDim 0, RowEndIndex, 0, ColEndIndex
'    For irow = 0 To RowEndIndex
'        For iCol = 0 To ColEndIndex
'            ArgToArray.Value(irow, iCol) = ArgFromArray.Value(irow, iCol)
'        Next iCol
'    Next irow
'    PopulateFromArrayToToArray = 1
'Exit Function
'ErrorHandler:
'Err.Raise Err.Number, Err.Source, Err.Description & "(PopulateFromArrayToToArray)"
'
'End Function
'
'Private Function UpdatePeriods()
'On Error GoTo ErrorHandler
'
'    Dim StartDate As Date
'    Dim Nbrpds As Integer
'    Dim Interval As AIM_INTERVALS
'
'    StartDate = Me.txtFcstStartDate.Text
'
'    Nbrpds = Me.txtFcstPds.Value
'
'    Interval = GetForecastInterval
'
'    'SetColumns Nbrpds, StartDate, Interval, Me.ckNetRqmts.Value
'    SetColumns Nbrpds, StartDate, Interval, Me.optNetRequirements.Value
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(UpdatePeriods)"
'End Function
'
'
'Private Function WriteToFile() As Integer
'On Error GoTo ErrorHandler
''Sri add code for ProdConst start new function
'    Dim FileSystemObject As Object
'    Dim FileToWrite As Object
'    Dim strFileText As String
'    Dim strMessage As String
'    Dim FileFormat As Long
'    Dim LogFile As String
'    Dim irow As Long
'    Dim iCol As Integer
'    Dim RowEndIndex As Integer
'    Dim ColEndIndex As Integer
'    Dim tempString As String
'    Dim RtnCode As Integer
'    Dim ErrorMessage As String
'
'    Const FOR_READING = 1
'    Const FOR_WRITING = 2
'    Const FOR_APPENDING = 8
'
'    Const TRISTATE_FALSE = 0
'    Const TRISTATE_TRUE = -1
'    Const TRISTATE_USEDEFAULT = -2
'
'    'Set default return value
'    WriteToFile = FAIL
'
'    'Determine format in which to write the file
'    If gLocaleID = 0 Then gLocaleID = g_LOCALEID_EN_US
'
'    Select Case gLocaleID
'    Case g_LOCALEID_ZH_TW ' Traditional Chinese
'        FileFormat = TRISTATE_TRUE
'
'    Case g_LOCALEID_JA ' Japan
'        FileFormat = TRISTATE_TRUE
'
'    Case g_LOCALEID_KO 'Korea UserLCID
'        FileFormat = TRISTATE_TRUE
'
'    Case g_LOCALEID_ZH_CN ' Simplified Chinese
'        FileFormat = TRISTATE_TRUE
'
'    Case Else   ' The other countries
'        FileFormat = TRISTATE_USEDEFAULT
'
'    End Select
'    If xForeCast.Count(1) = 0 Or xForeCast.Count(2) = 0 Then
'            strMessage = getTranslationResource("ERRMSG72002")
'            If StrComp(strMessage, "ERRMSG72002") = 0 Then strMessage = "There is no data to write to a file"
'            ErrorMessage = strMessage
'            Screen.MousePointer = vbNormal
'            MsgBox ErrorMessage, vbOKOnly, ""
'            Exit Function
'     End If
'    If Trim(LogFile) = "" Then
'        LogFile = App.Path + "\" + CStr(FcstId) + "-" + CStr(Format(Now(), "MMDDYYYYHHMM")) + ".Log"
'    End If
'
'    'Create the File System object
'    Set FileSystemObject = CreateObject("Scripting.FileSystemObject")
'    Set FileToWrite = FileSystemObject.OpenTextFile(LogFile, FOR_APPENDING, True, FileFormat)
'
'    'Write ForecastId in the first line
'    FileToWrite.writeline CStr(FcstId)
'
'    RowEndIndex = xForeCast.UpperBound(1)
'    ColEndIndex = xForeCast.UpperBound(2)
'
'    For irow = 0 To RowEndIndex
'        tempString = ""
'        For iCol = 0 To ColEndIndex
'            tempString = tempString + CStr(xForeCast(irow, iCol)) + vbTab
'
'
'        Next iCol
'        tempString = tempString
'        FileToWrite.writeline tempString
'
'    Next irow
'
'    WriteToFile = SUCCEED
'
'    'Clean Up
'    FileToWrite.Close
'    Set FileToWrite = Nothing
'    Set FileSystemObject = Nothing
'
'Exit Function
'ErrorHandler:
'    Set FileToWrite = Nothing
'    Set FileSystemObject = Nothing
'    WriteToFile = FAIL
'    If Err.Number = 76 Then
'        'Path not found -- give logfile name for ref.
'        Err.Source = "(OpenFileForAppending)"
'        Err.Description = Err.Description & "-- '" & LogFile & "'"
'    End If
'    Err.Raise Err.Number, Err.Source & "(WriteToFile)", Err.Description
'End Function
'
'Private Sub dcForecast_LostFocus()
'On Error GoTo ErrorHandler
'
'    'Get the Forecast Record
'    GetForecastRcd Me.dcForecast.Text
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecast_LostFocus)"
'End Sub
'
'Private Sub tbForecast_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
'On Error GoTo ErrorHandler
'
'    'Load Item Data Variables
'    Dim AdjustPct As Double
'    Dim ApplyTrend As Integer
'    Dim FcstLevel As Integer
'    Dim FcstUnit As AIM_FORECASTUNITS
'    Dim i As Integer
'    Dim Interval As AIM_INTERVALS
'    Dim NetRqmts As Boolean
'    Dim strSQL As String
'    Dim StartDate As Date
'    Dim StartTime As Double
'    Dim strMessage As String
'    Dim RtnCode As Integer
'    Dim ErrorMessage As String
'
'    Screen.MousePointer = vbHourglass
'
'    'Clear messages
'    Write_Message ""
'    dgForecast.Redraw = False
'
'    Select Case Tool.ID
'        Case "ID_Load"
'            If Me.dcForecast.Text = "" Then
'            strMessage = getTranslationResource("ERRMSG72001")
'            If StrComp(strMessage, "ERRMSG72001") = 0 Then strMessage = "Please Select a vaild FcstId"
'            ErrorMessage = strMessage
'            Screen.MousePointer = vbNormal
'            MsgBox ErrorMessage, vbOKOnly, ""
'
'            Exit Sub
'         End If
'
'            strMessage = getTranslationResource("STATMSG07201")
'            If StrComp(strMessage, "STATMSG07201") = 0 Then strMessage = "Loading Item Data for Forecast..."
'            Write_Message strMessage
'
'            'Update the column headers
'            UpdatePeriods
'
'            'Retrieve the data
'            RtnCode = BldForecastSQL_New( _
'                Cn, _
'                Me.txtVnId.Text, Me.txtAssort.Text, Me.txtLcId.Text, _
'                Me.txtItem.Text, Me.txtClass(1).Text, Me.txtClass(2).Text, Me.txtClass(3).Text, _
'                Me.txtClass(4).Text, Me.txtById.Text, Me.txtItemStatus.Text, _
'                Me.txtLDivision.Text, Me.txtLRegion.Text, Me.txtLStatus.Text, Me.txtLUserDefined.Text, _
'                SAVersion, _
'                rsVnItemList)
'
'            If f_IsRecordsetOpenAndPopulated(rsVnItemList) Then
'                'Update Parameters
'                StartDate = Me.txtFcstStartDate.Text
'                Interval = GetForecastInterval
'                ApplyTrend = GetTrendOption
'                FcstLevel = GetForecastLevel
'                FcstUnit = GetForecastUnit
'                AdjustPct = Me.txtAdjustPct.Value
'
'                'NetRqmts = IIf(Me.ckNetRqmts.Value = vbChecked, True, False)
'                NetRqmts = IIf(Me.optNetRequirements.Value = True, True, False)
'                'Sri add code for ProdConst start
'                'If Check1.Value = vbChecked Then
'                If Me.optProductionConstraint.Value = True Then
'                    NetRqmts = True
'                End If
'                'Sri add code for ProdConst end
'                'Update the Forecast (rebinds grid, too)
'                UpdateForecast StartDate, rsVnItemList.RecordCount, CInt(Me.txtFcstPds.Value), _
'                    Interval, ApplyTrend, FcstUnit, FcstLevel, AdjustPct, NetRqmts
'
'                'Refresh Item statistics
'                Me.txtFcstDemand.Text = Format(rsVnItemList("fcstdemand").Value, "#,##0.00")
'                Me.txtMAE.Text = Format(rsVnItemList("mae").Value, "#,##0.00")
'                Me.txtTrend.Text = Format(rsVnItemList("trend").Value, "#,##0.00")
'                Me.txtUOM.Text = rsVnItemList("uom").Value
'                Me.txtAvail.Text = Format(rsVnItemList("oh").Value + rsVnItemList("oo").Value _
'                    - rsVnItemList("comstk").Value - rsVnItemList("bkorder").Value - rsVnItemList("bkcomstk").Value, "#,##0")
'
'            End If
'
'            'Clean Up
'            strMessage = getTranslationResource("STATMSG07202")
'            If StrComp(strMessage, "STATMSG07202") = 0 Then strMessage = " record(s) loaded."
'            Write_Message Format(rsVnItemList.RecordCount, "0") & _
'                    " " & strMessage
'
'        Case "ID_Update"
'            'Check for an empty recordset
'            If Not f_IsRecordsetValidAndOpen(rsVnItemList) Then
'                strMessage = getTranslationResource("TEXTMSG05103")
'                If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
'                Write_Message strMessage
'            Else
'                strMessage = getTranslationResource("STATMSG07203")
'                If StrComp(strMessage, "STATMSG07203") = 0 Then strMessage = "Updating Forecast..."
'                Write_Message strMessage
'
'                'Update column headers
'                UpdatePeriods
'
'                'Record the start time
'                StartTime = Timer
'
'                'Get the Forecast Parameters
'                StartDate = Me.txtFcstStartDate.Text
'                Interval = GetForecastInterval()
'                ApplyTrend = GetTrendOption()
'                FcstLevel = GetForecastLevel()
'                FcstUnit = GetForecastUnit()
'                AdjustPct = Me.txtAdjustPct.Value
'                'NetRqmts = IIf(Me.ckNetRqmts.Value = vbChecked, True, False)
'                NetRqmts = IIf(Me.optNetRequirements.Value = True, True, False)
'                'A.Stocksdale 2003/12/10 - what about prod. constraints, as in the "ID_Load"?
'                'The rest for this ID is the same, except for the timer!
'                If Me.optProductionConstraint.Value = True Then
'                    NetRqmts = True
'                End If
'
'                'Update the Forecast
'                UpdateForecast StartDate, rsVnItemList.RecordCount, CInt(Me.txtFcstPds.Value), _
'                    Interval, ApplyTrend, FcstUnit, FcstLevel, AdjustPct, NetRqmts
'
'                'Display Elapsed Time
'                Me.txtTime = Format(Timer - StartTime, "000.00")
'
'    '            'A.Stocksdale 2003/12/10 - what about refreshing the item stats?
'    '            'Refresh Item statistics
'    '            Me.txtFcstDemand.Text = Format(rsVnItemList("fcstdemand").Value, "#,##0.00")
'    '            Me.txtMAE.Text = Format(rsVnItemList("mae").Value, "#,##0.00")
'    '            Me.txtTrend.Text = Format(rsVnItemList("trend").Value, "#,##0.00")
'    '            Me.txtUOM.Text = rsVnItemList("uom").Value
'    '            Me.txtAvail.Text = Format(rsVnItemList("oh").Value + rsVnItemList("oo").Value _
'    '                - rsVnItemList("comstk").Value - rsVnItemList("bkorder").Value - rsVnItemList("bkcomstk").Value, "#,##0")
'
'                Write_Message ""
'            End If
'
'        Case "ID_ItemFilter"
'            ShowItemFilter
'
'        Case "ID_Save"
'            SaveForecast
'
'            If f_IsRecordsetOpenAndPopulated(rsForecast) Then rsForecast.Update
'
'        Case "ID_Close"
'            Unload Me
'            Screen.MousePointer = vbNormal
'            Exit Sub
'
'        Case "ID_Export"
'             If Me.dcForecast.Text = "" Then
'                strMessage = getTranslationResource("ERRMSG72001")
'                If StrComp(strMessage, "ERRMSG72001") = 0 Then strMessage = "Please Select a vaild FcstId"
'                ErrorMessage = strMessage
'                Screen.MousePointer = vbNormal
'                MsgBox ErrorMessage, vbOKOnly, ""
'                 Exit Sub
'             End If
'            WriteToFile
'
'    End Select
'
'    dgForecast.Redraw = True
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        Resume Next
'    Else
'        dgForecast.Redraw = True
'        Screen.MousePointer = vbNormal
'        f_HandleErr Me.Caption & "(tbForecast_ToolClick)"
'    End If
'End Sub
'
'Private Sub atForecast_TabClick(ByVal NewTab As ActiveTabs.SSTab)
'On Error GoTo ErrorHandler
'
'    Dim Nbrpds As Integer
'    Dim Row As Long
'
'    If NewTab.Key = "ForecastGraph" And xForeCast.Count(1) > 0 Then
'        Nbrpds = Me.txtFcstPds.Value
'        Row = CLng(Me.dgForecast.bookmark)
'        UpdateGraph Row, Nbrpds, GetDisplayOption()
'
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(atForecast_TabClick)"
'End Sub
''Private Function GetCurrentPriceCost(StartDate As Date, Price As Double, Cost As Double) As Integer
''
''On Error GoTo ErrorHandler
''Dim StartYear As Integer
''StartYear = GetFiscalYear(StartDate, AIMYears())
''
''If StartYear = FyYear Then
''    Price = ThisFYPrice
''    Cost = ThisFYCost
''ElseIf StartYear < FyYear Then
''    Price = LastFYPrice
''    Cost = LastFYCost
''ElseIf StartYear > FyYear Then
''    Price = NextFYPrice
''    Cost = NextFYCost
''End If
''
''Exit Function
''ErrorHandler:
''    Err.Raise Err.Number, Err.Source, Err.Description & "(GetCurrentPriceCost)"
''End Function
'
'
'
'Private Sub ShowItemFilter()
'On Error GoTo ErrorHandler
'
'    Dim arrItems As XArrayDB
'    Dim FcstItemFilter As FCST_ITEM_FILTER
'
'    Set arrItems = New XArrayDB
'
'    With FcstItemFilter
'        'Set parameters to existing fields
'        .Item = txtItem.Text
'        .ItemStatus = txtItemStatus.Text
'        .VnId = txtVnId.Text
'        .Assort = txtAssort.Text
'        .LcId = txtLcId.Text
'        .ById = txtById.Text
'        .Class1 = txtClass(1).Text
'        .Class2 = txtClass(2).Text
'        .Class3 = txtClass(3).Text
'        .Class4 = txtClass(4).Text
'        .LDivision = txtLDivision.Text
'        .LRegion = txtLRegion.Text
'        .LStatus = txtLStatus.Text
'        .LUserDefined = txtLUserDefined.Text
'
'        CallItemFilter Cn, FcstItemFilter, False, arrItems
'
'        'Set display to returned values
'        txtItem.Text = .SubFilterItem
'        txtItemStatus.Text = .SubFilterItemStatus
'        txtVnId.Text = .SubFilterVnId
'        txtAssort.Text = .SubFilterAssort
'        txtLcId.Text = .SubFilterLcID
'        txtById.Text = .SubFilterById
'        txtClass(1).Text = .SubFilterClass1
'        txtClass(2).Text = .SubFilterClass2
'        txtClass(3).Text = .SubFilterClass3
'        txtClass(4).Text = .SubFilterClass4
'        txtLDivision.Text = .SubFilterLDivision
'        txtLRegion.Text = .SubFilterLRegion
'        txtLStatus.Text = .SubFilterLStatus
'        txtLUserDefined.Text = .SubFilterLUserDefined
'    End With
'
'    'Don't use arritem here, so discard it
'    Set arrItems = Nothing
'
'Exit Sub
'ErrorHandler:
'    Set arrItems = Nothing
'    If Err.Number = 9 Then
'        'The array is empty, exit quietly
'        Exit Sub
'    Else
'        f_HandleErr Me.Caption & "(ShowItemFilter)"
'    End If
'End Sub
'
'Private Sub dcForecast_CloseUp()
'On Error GoTo ErrorHandler
'
'    'Get the Forecast Record
'    GetForecastRcd Me.dcForecast.Text
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecast_CloseUp)"
'End Sub
'
'Private Sub dcForecast_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcForecast.Columns(0).Caption = getTranslationResource("ID")
'    Me.dcForecast.Columns(0).Width = 1000
'    Me.dcForecast.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcForecast.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcForecast.Columns(0).DataField = "FcstID"
'
'    Me.dcForecast.Columns(1).Caption = getTranslationResource("Description")
'    Me.dcForecast.Columns(1).Width = 2880
'    Me.dcForecast.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
'    Me.dcForecast.Columns(1).Alignment = ssCaptionAlignmentLeft
'    Me.dcForecast.Columns(1).DataField = "FcstDesc"
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcForecast, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcForecast.Columns.Count - 1
''        dcForecast.Columns(IndexCounter).HasHeadBackColor = True
''        dcForecast.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''   Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dcForecast_InitColumnProps)"
'End Sub
'
'Private Sub dgForecast_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
'On Error GoTo ErrorHandler
'
'    Select Case Button
'    Case vbRightButton
'        Me.PopupMenu Me.mnuEdit
'    End Select
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecast_MouseDown)"
'End Sub
'
'Private Sub dgForecast_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
'On Error GoTo ErrorHandler
'
'    'Check for an empty recordset
'    If Not f_IsRecordsetOpenAndPopulated(rsVnItemList) Then Exit Sub
'
'    'Check for null
'    If IsNull(Me.dgForecast.bookmark) Then
'        Exit Sub
'    Else
'        rsVnItemList.AbsolutePosition = CLng(Me.dgForecast.bookmark) + 1
'    End If
'
'    'Refresh Item statistics
'    Me.txtFcstDemand.Text = Format(rsVnItemList("fcstdemand").Value, "#,##0.00")
'    Me.txtMAE.Text = Format(rsVnItemList("mae").Value, "#,##0.00")
'    Me.txtTrend.Text = Format(rsVnItemList("trend").Value, "#,##0.00")
'    Me.txtUOM.Text = rsVnItemList("uom").Value
'    Me.txtAvail.Text = Format(rsVnItemList("oh").Value + rsVnItemList("oo").Value _
'        - rsVnItemList("comstk").Value - rsVnItemList("bkorder").Value - rsVnItemList("bkcomstk").Value, "#,##0")
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecast_RowColChange)"
'End Sub
'
'Private Sub dgForecast_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
'On Error GoTo ErrorHandler
'
'    If IsNull(StartLocation) Then
'        StartLocation = 0
'    End If
'
'    NewLocation = CLng(StartLocation) + NumberOfRowsToMove
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgForecast_UnboundPositionData)"
'End Sub
'
'Private Sub dgForecast_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'
'    Dim iRBRow As Integer
'    Dim iCol As Integer
'    Dim irow As Integer
'    Dim iPoint As Integer
'    Dim iCount As Integer
'
'    Cn.Errors.Clear
'    If dgForecast.Enabled = False Then Exit Sub
'
'    'Detail or Summary Display
'    If Me.ckForecastLevel.Value = vbUnchecked Then
'        'Test for an empty array
'        If xForeCast.Count(1) = 0 Then
'            Exit Sub
'        Else
'            iCount = xForeCast.Count(1)
'            irow = 0
'        End If
'
'        If RowBuf.ColumnCount <> (xForeCast.UpperBound(2) + 1) Then Exit Sub
'
'        If IsNull(StartLocation) Then
'            If ReadPriorRows Then
'                iPoint = iCount
'            Else
'                iPoint = 0
'            End If
'        Else
'            iPoint = StartLocation
'            If ReadPriorRows Then
'                iPoint = iPoint - 1
'            Else
'                iPoint = iPoint + 1
'            End If
'        End If
'
'        For iRBRow = 0 To RowBuf.RowCount - 1
'            If iPoint < 0 Or iPoint > iCount - 1 Then Exit For
'            For iCol = xForeCast.LowerBound(2) To xForeCast.UpperBound(2)
'            RowBuf.Value(iRBRow, iCol) = xForeCast.Value(iPoint, iCol)
'            Next iCol
'            RowBuf.bookmark(iRBRow) = iPoint
'            If ReadPriorRows Then
'                iPoint = iPoint - 1
'            Else
'                iPoint = iPoint + 1
'            End If
'
'            irow = irow + 1
'
'        Next iRBRow
'
'        RowBuf.RowCount = irow
'
'    Else
'
'        'Test for an empty array
'        If xForecastItem.Count(1) = 0 Then
'            Exit Sub
'        Else
'            iCount = xForecastItem.Count(1)
'            irow = 0
'        End If
'
'        If IsNull(StartLocation) Then
'            If ReadPriorRows Then
'                iPoint = iCount
'            Else
'                iPoint = 0
'            End If
'        Else
'            iPoint = StartLocation
'            If ReadPriorRows Then
'                iPoint = iPoint - 1
'            Else
'                iPoint = iPoint + 1
'            End If
'        End If
'
'        For iRBRow = 0 To RowBuf.RowCount - 1
'
'            If iPoint < 0 Or iPoint > iCount - 1 Then Exit For
'
'            For iCol = xForecastItem.LowerBound(2) To xForecastItem.UpperBound(2)
'                RowBuf.Value(iRBRow, iCol) = xForecastItem.Value(iPoint, iCol)
'            Next iCol
'
'            RowBuf.bookmark(iRBRow) = iPoint
'            If ReadPriorRows Then
'                iPoint = iPoint - 1
'            Else
'                iPoint = iPoint + 1
'            End If
'
'            irow = irow + 1
'
'        Next iRBRow
'
'        RowBuf.RowCount = irow
'
'    End If
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 30403 Then
'        'The Rowbuf. Column Count does not match the number in xForecast. Try ignoring this error and see...
'        '*grumble about data widgets and its shaky events*
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(dgForecast_UnboundReadData)"
'    End If
'End Sub
'
'Private Sub Form_Activate()
'On Error GoTo ErrorHandler
'
'    'Check for an open connection
'    If Cn.State <> adStateOpen Then
'        Unload Me
'        Exit Sub
'    End If
'
'    'Set State of window list State Button
'    gbUpdating = True
'    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
'    gbUpdating = False
'
'    RemoveSemicolons
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Activate)"
'End Sub
'
'Private Sub Form_Load()
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'    Dim strSQL As String
'    Dim strMessage As String
'
'    'Housekeeping
'    Screen.MousePointer = vbHourglass
'    strMessage = getTranslationResource("STATMSG07204")
'    If StrComp(strMessage, "STATMSG07204") = 0 Then strMessage = "Initializing AIM Forecast Generator..."
'    Write_Message strMessage
'
'    GetTranslatedCaptions Me
'
'    Set xForeCast = New XArrayDB
'    Set xForecastItem = New XArrayDB
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
'    If RtnCode <> SUCCEED Then Exit Sub
'
'    'Get the Seasonality Table Version Number
'    SAVersion = GetSAVersion
'
'    'Get the system control record
'    GetSysCtrlRec
'
'    'Initialize the AIM Forecast Table Recordset
'    Set rsForecast = New ADODB.Recordset
'    With rsForecast
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockOptimistic
'    End With
'
'    'Initialize the Vendor Item List Resultset -- fire hose
'    Set rsVnItemList = New ADODB.Recordset
'    With rsVnItemList
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenForwardOnly
'    End With
'
'    'Build the Forecast List Drop Down
'    GetForecastInformation
'
'    'Initialize the vendor/assortment keys
'    VnId = ""
'    Assort = ""
'
'    'Load the AIM Calendar
'    Set clsAIMAdvCalendar = New AIMAdvCalendar
'    RtnCode = AIMAdvCalendar_Load(Cn, clsAIMAdvCalendar)
'
'    'Set chart properties
'    InitializeChart
'
'    'Add to Windows List
'    AddToWindowList Me.Caption
'
'    'Make the spin button visible
'    Me.txtAdjustPct.Spin.Visible = 1
'    Me.txtFcstPds.Spin.Visible = 1
'    Me.txtFcstStartDate.DropDown.Visible = 1
'
'    'Windup
'    Write_Message ""
'    Screen.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Load)"
'End Sub
'
'
'Private Sub Form_Resize()
'On Error GoTo ErrorHandler
'
'    SizeForm False
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(Form_Resize)"
'End Sub
'
'Private Sub Form_Unload(Cancel As Integer)
'On Error GoTo ErrorHandler
'
'    Dim RtnCode As Integer
'
'    If f_IsRecordsetValidAndOpen(rsVnItemList) Then rsVnItemList.Close
'    Set rsVnItemList = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsForecast) Then rsForecast.Close
'    Set rsForecast = Nothing
'
'    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
'    Set rsSysCtrl = Nothing
'
'    Set clsAIMAdvCalendar = Nothing
'
'    Set xForeCast = Nothing
'    Set xForecastItem = Nothing
'
'    'Close the SQL Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
'    Set Cn = Nothing
'
'    'Remove form from window list
'    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
'
'    'Clear messages
'    Write_Message ""
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 3219 Then
'        'Ignore
'    Else
'        f_HandleErr Me.Caption & "(Form_Unload)"
'    End If
'    Resume Next
'End Sub
'
'Private Sub mnuEditOpt_Click(Index As Integer)
'On Error GoTo ErrorHandler
'
'    Dim FileName As String
'    Dim FormatOption As Integer
'    Dim i As Integer
'    Dim j As Integer
'    Dim s1 As String
'    Dim s2 As String
'    Dim s3 As String
'    Dim s() As String
'
'    Me.MousePointer = vbHourglass
'
'    Select Case Index
'
'    Case 0          'Copy
'
'        'Check for an empty set
'        If Me.dgForecast.Rows = 0 Then
'            Screen.MousePointer = vbNormal
'            Exit Sub
'        End If
'
'        Clipboard.Clear
'
'        For i = 0 To Me.dgForecast.Columns.Count - 1
'            s1 = s1 + Me.dgForecast.Columns(i).Caption + vbTab
'        Next i
'
'        s1 = s1 + vbCrLf
'
'        If Me.ckForecastLevel.Value = vbUnchecked Then
'            ReDim s(xForeCast.Count(1))
'
'            For i = xForeCast.LowerBound(1) To xForeCast.UpperBound(1)
'                For j = xForeCast.LowerBound(2) To xForeCast.UpperBound(2)
'                    s(i) = s(i) + CStr(xForeCast.Value(i, j)) + vbTab
'                Next j
'
'            Next i
'
'            Clipboard.SetText s1 + Join(s, vbCrLf), vbCFText
'
'        Else
'            ReDim s(xForecastItem.Count(1))
'
'            For i = xForecastItem.LowerBound(1) To xForecastItem.UpperBound(1)
'                For j = xForecastItem.LowerBound(2) To xForecastItem.UpperBound(2)
'                    s(i) = s(i) + CStr(xForecastItem.Value(i, j)) + vbTab
'                Next j
'
'            Next i
'
'            Clipboard.SetText s1 + Join(s, vbCrLf), vbCFText
'
'        End If
'
'        Erase s
'
'    Case 1          'Print
'        Me.dgForecast.PrintData ssPrintAllRows, False, True
'
'    End Select
'
'    Me.MousePointer = vbNormal
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
'End Sub
'
'Private Sub txtClass_Change(Index As Integer)
'On Error GoTo ErrorHandler
'
'    If IsNull(txtClass(Index).Text) _
'    Or Trim(txtClass(Index).Text) = "" _
'    Then
'        txtClassDesc(Index).Text = ""
'    Else
'        txtClassDesc(Index).Text = GetClassDescription(Cn, txtClass(Index).Text, (Index))
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(txtClass(" & Index & ")_Change)"
'End Sub
'
'Private Sub txtFcstPds_Change()
'On Error GoTo ErrorHandler
'    Dim strMessage As String
'    Dim ErrorMessage As String
'
'    If txtFcstPds.Value > txtFcstPds.MaxValue _
'    Then
'        strMessage = getTranslationResource("ERRMSG72000")
'        If StrComp(strMessage, "ERRMSG72000") = 0 Then strMessage = "Invalid Property Value. The Property value is out of range or incorrect."
'
'        ErrorMessage = Label21.Caption & " -- " & strMessage
'        MsgBox ErrorMessage, vbOKOnly, ""
'
'        txtFcstPds.SetFocus
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "txtFcstPds_LostFocus"
'End Sub
'
'Private Function RemoveSemicolons()
''Remove semi colons from static text
'On Error GoTo ErrorHandler
'
'    If Right(txtTime.Caption, 1) = ":" Then txtTime.Caption = Left(txtTime.Caption, Len(txtTime.Caption) - 1)
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description
'End Function
'
'Private Function InitializeChart()
'On Error GoTo ErrorHandler
'
'    Dim Counter As Long
'
'    With Me.chtForecast
'        .AllowUserChanges = False
'
'        'Set up Header
'        .Header.Text = ""
'
'        .Border.Type = oc2dBorderPlain
'        .Border.Width = 2
'
'        'Set Width of X and Y Axes
'        .ChartArea.Axes("X").Title.Text = ""
'        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
'        .ChartArea.Axes("X").MajorGrid.Spacing = 2
'        .ChartArea.Axes("X").Max = Me.txtFcstPds.Value
'        .ChartArea.Axes("X").AxisStyle.LineStyle.Width = 2
'
'        .ChartArea.Axes("Y").Title.Text = ""
'        .ChartArea.Axes("Y").MajorGrid.Spacing = 10
'        .ChartArea.Axes("Y").Origin = 0
'        .ChartArea.Axes("Y").AxisStyle.LineStyle.Width = 2
'
'        'Remove any old x-axis labels
'        .ChartArea.Axes("X").ValueLabels.RemoveAll
'        .ChartArea.Axes("Y").ValueLabels.RemoveAll
'
'        'Set up Legends
'        .Legend.Anchor = oc2dAnchorEast
'        .Legend.IsShowing = True
'        .Legend.Border.Type = oc2dBorderPlain
'        .Legend.Border.Width = 2
'        .Legend.Text = ""
'
'        .ChartGroups(1).SeriesLabels.RemoveAll
'        'Set up Data Series
'        .ChartGroups(1).ChartType = oc2dTypePlot
'        .ChartGroups(1).Data.NumSeries = 1
'        .ChartGroups(1).Data.NumPoints(1) = Me.txtFcstPds.Value
'
'        'Set the Line Properties
'        .ChartGroups(1).Styles(1).Symbol.Shape = oc2dShapeNone
'        .ChartGroups(1).Styles(1).Line.Color = vbGreen
'        .ChartGroups(1).Styles(1).Line.Width = 2
'
'        'Set the data value
'        For Counter = 1 To .ChartGroups(1).Data.NumPoints(1)
'            .ChartGroups(1).Data.Y(1, Counter) = 0
'        Next
'
'        'Finalize Grid Spacing
'        .ChartArea.Axes("Y").MajorGrid.Spacing = .ChartArea.Axes("Y").NumSpacing
'
'        .IsBatched = False
'    End With
'
'Exit Function
'ErrorHandler:
'    Me.chtForecast.IsBatched = False
'    Err.Raise Err.Number, Err.Source & "(InitializeChart)", Err.Description
'End Function
'
'Private Function GetForecastInformation()
'On Error GoTo ErrorHandler
'
'    Dim rsForecastInfo As ADODB.Recordset
'    Dim AIM_GetList_FcstID_Sp As ADODB.Command
'    Dim RtnCode As Integer
'    Dim CurrentFcstID As String
'
'    'Get Forecast IDs and Descriptions from AIMForecast
'    Set AIM_GetList_FcstID_Sp = New ADODB.Command
'    With AIM_GetList_FcstID_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GetList_FcstID_Sp"
'    End With
'
'    With AIM_GetList_FcstID_Sp.Parameters
'        .Append AIM_GetList_FcstID_Sp.CreateParameter("@RecordCount", adInteger, adParamOutput)
'    End With
'
'    Set rsForecastInfo = New ADODB.Recordset
'
'    With rsForecastInfo
'        .CursorLocation = adUseClient
'        .Open AIM_GetList_FcstID_Sp
'    End With
'
'    'Set dcForecast.DataSourceList = rsForecastInfo
'    dcForecast.RemoveAll
'    dcForecast.Reset
'    If f_IsRecordsetOpenAndPopulated(rsForecastInfo) Then
'        CurrentFcstID = rsForecastInfo!FcstId
'        Do Until rsForecastInfo.eof
'            dcForecast.AddItem rsForecastInfo!FcstId & vbTab & rsForecastInfo!FcstDesc
'            rsForecastInfo.MoveNext
'        Loop
'        'Get the Forecast Record
'        GetForecastRcd Me.dcForecast.Text
'       ' Me.dcForecast.Text = CurrentFcstID
'    Else
'        SetColumns 12, Date, int_Months, 0
'    End If
'
'CleanUp:
'    If f_IsRecordsetValidAndOpen(rsForecastInfo) Then rsForecastInfo.Close
'    Set rsForecastInfo = Nothing
'    If Not (AIM_GetList_FcstID_Sp Is Nothing) Then Set AIM_GetList_FcstID_Sp.ActiveConnection = Nothing
'    Set AIM_GetList_FcstID_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsForecastInfo) Then rsForecastInfo.Close
'    Set rsForecastInfo = Nothing
'    If Not (AIM_GetList_FcstID_Sp Is Nothing) Then Set AIM_GetList_FcstID_Sp.ActiveConnection = Nothing
'    Set AIM_GetList_FcstID_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetForecastInformation)"
'End Function
'
'Private Function GetSAVersion() As Integer
'On Error GoTo ErrorHandler
'
'    Dim AIM_GetSaVersion_Sp As ADODB.Command
'
'    'Set default to failure
'    GetSAVersion = -1
'
'    'Fetch from database
'    Set AIM_GetSaVersion_Sp = New ADODB.Command
'    With AIM_GetSaVersion_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_GetSaVersion_Sp"
'        .Parameters.Refresh
'        .Execute , , adExecuteNoRecords
'    End With
'
'    GetSAVersion = AIM_GetSaVersion_Sp.Parameters("@RETURN_VALUE").Value
'
'    'CleanUp
'    If Not (AIM_GetSaVersion_Sp Is Nothing) Then Set AIM_GetSaVersion_Sp.ActiveConnection = Nothing
'    Set AIM_GetSaVersion_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_GetSaVersion_Sp Is Nothing) Then Set AIM_GetSaVersion_Sp.ActiveConnection = Nothing
'    Set AIM_GetSaVersion_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetSAVersion)"
'End Function
'
'Private Function GetSysCtrlRec() As Integer
'On Error GoTo ErrorHandler
'
'    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
'
'    'Set default to failure
'    GetSysCtrlRec = -1
'
'    'Fetch from database
'    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
'    With AIM_SysCtrl_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_SysCtrl_Get_Sp"
'    End With
'
'    'Initialize the System Control Recordset
'    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
'    Set rsSysCtrl = Nothing
'    Set rsSysCtrl = New ADODB.Recordset
'    With rsSysCtrl
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'
'        .Open AIM_SysCtrl_Get_Sp
'    End With
'
'    GetSysCtrlRec = AIM_SysCtrl_Get_Sp.Parameters("@RETURN_VALUE").Value
'
'    'CleanUp
'    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
'    Set AIM_SysCtrl_Get_Sp = Nothing
'
'Exit Function
'ErrorHandler:
'    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
'    Set AIM_SysCtrl_Get_Sp = Nothing
'    Err.Raise Err.Number, Err.Source, Err.Description & "(GetSAVersion)"
'End Function
'
'
