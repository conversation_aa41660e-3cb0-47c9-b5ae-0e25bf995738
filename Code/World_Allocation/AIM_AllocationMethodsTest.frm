VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_AllocationMethodsTest 
   Caption         =   "EXceed AIM Allocation Methods Test"
   ClientHeight    =   7575
   ClientLeft      =   60
   ClientTop       =   630
   ClientWidth     =   10725
   Icon            =   "AIM_AllocationMethodsTest.frx":0000
   LinkTopic       =   "Form1"
   LockControls    =   -1  'True
   ScaleHeight     =   7575
   ScaleWidth      =   10725
   StartUpPosition =   1  'CenterOwner
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgStores 
      Height          =   4095
      Left            =   15
      TabIndex        =   6
      Top             =   2760
      Width           =   10695
      _Version        =   196617
      DataMode        =   1
      ForeColorEven   =   0
      BackColorOdd    =   8454143
      RowHeight       =   423
      SplitterVisible =   -1  'True
      Columns(0).Width=   3200
      _ExtentX        =   18865
      _ExtentY        =   7223
      _StockProps     =   79
      Caption         =   "Store Requirements"
   End
   Begin VB.Frame Frame3 
      Height          =   675
      Left            =   0
      TabIndex        =   9
      Top             =   6900
      Width           =   10695
      Begin VB.CommandButton cmdTEst 
         Caption         =   "David's Test"
         Height          =   375
         Left            =   2460
         TabIndex        =   13
         Top             =   180
         Width           =   4035
      End
      Begin VB.CommandButton cmdAllocate 
         Caption         =   "&Allocate..."
         Height          =   375
         Left            =   525
         TabIndex        =   7
         Top             =   180
         Width           =   1035
      End
      Begin VB.CommandButton cmdExit 
         Caption         =   "E&xit"
         Height          =   375
         Left            =   9165
         TabIndex        =   8
         Top             =   180
         Width           =   1035
      End
   End
   Begin VB.Frame Frame2 
      Height          =   2715
      Left            =   0
      TabIndex        =   10
      Top             =   0
      Width           =   10695
      Begin VB.TextBox txtScenarioDesc 
         Enabled         =   0   'False
         Height          =   675
         Left            =   120
         MultiLine       =   -1  'True
         TabIndex        =   12
         TabStop         =   0   'False
         Top             =   1920
         Width           =   10455
      End
      Begin VB.CheckBox ckSourceOpt 
         Caption         =   "Source Allocation"
         Height          =   495
         Left            =   180
         TabIndex        =   3
         Top             =   1515
         Value           =   1  'Checked
         Width           =   2355
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgDC 
         Height          =   1755
         Left            =   2880
         TabIndex        =   4
         Top             =   120
         Width           =   4395
         _Version        =   196617
         DataMode        =   1
         SelectTypeCol   =   0
         SelectTypeRow   =   0
         ForeColorEven   =   0
         BackColorOdd    =   8454143
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   7752
         _ExtentY        =   3096
         _StockProps     =   79
         Caption         =   "Inventory Availability"
      End
      Begin VB.Frame Frame1 
         Caption         =   "Allocation Method"
         Height          =   975
         Left            =   60
         TabIndex        =   11
         Top             =   120
         Width           =   2775
         Begin VB.OptionButton optAllocMethod 
            Caption         =   "Proportional to Need"
            Height          =   255
            Index           =   0
            Left            =   120
            TabIndex        =   0
            Top             =   240
            Width           =   2535
         End
         Begin VB.OptionButton optAllocMethod 
            Caption         =   "Balance Relative Inventory"
            Height          =   255
            Index           =   1
            Left            =   120
            TabIndex        =   1
            Top             =   600
            Width           =   2535
         End
      End
      Begin VB.CheckBox ckRound 
         Caption         =   "Round Results"
         Height          =   495
         Left            =   180
         TabIndex        =   2
         Top             =   1140
         Value           =   1  'Checked
         Width           =   2355
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgScenario 
         Height          =   1755
         Left            =   7320
         TabIndex        =   5
         Top             =   120
         Width           =   3300
         _Version        =   196617
         DataMode        =   2
         Col.Count       =   0
         AllowUpdate     =   0   'False
         SelectTypeCol   =   0
         SelectTypeRow   =   0
         ForeColorEven   =   0
         BackColorOdd    =   8454143
         RowHeight       =   423
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   5821
         _ExtentY        =   3096
         _StockProps     =   79
         Caption         =   "Scenario"
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "Edit"
      Enabled         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
   End
End
Attribute VB_Name = "AIM_AllocationMethodsTest"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Type AllocRcd

    LcId As String
    Priority As Integer
    AvailQty As Double
    MinQty As Double
    Need As Double
    SugAlloc As Double
    Alloc As Double
    AllocFlag As Boolean
    FillPath As String
    FillFlag As Boolean
    FillLcId(1 To 9) As String
    TotalFillQty As Double
    FillQty(1 To 9) As Double
    FillKey As String
    
End Type

Private Type FillFromRcd

    LcId As String
    AvailQty As Double
    AllocQty As Double
    FillQty As Double

End Type

Dim dData() As FillFromRcd
Dim sData() As AllocRcd

Dim NbrDCs As Integer
Function Scenario_01()

    Dim i As Integer
    
    ReDim dData(0 To 8)
    ReDim sData(0 To 10)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 100     'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    For i = 1 To 8
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 0
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 1               'Priority
    sData(0).AvailQty = 0               'Available Quantity
    sData(0).MinQty = 10                'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "100000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
    
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 0               'Available Quantity
    sData(1).MinQty = 20                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "100000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 1               'Priority
    sData(2).AvailQty = 0               'Available Quantity
    sData(2).MinQty = 20                'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 0               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "100000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = 4                   'Store
    sData(3).Priority = 1               'Priority
    sData(3).AvailQty = 0               'Available Quantity
    sData(3).MinQty = 10                'Minimum Quantity
    sData(3).Need = 0                   'Need
    sData(3).SugAlloc = 0               'Suggested Allocation
    sData(3).Alloc = 0                  'Allocation
    sData(3).FillPath = "100000000"     'Fill Flags
    sData(3).FillQty(1) = 0             'DC01
    sData(3).FillQty(2) = 0             'DC02
    sData(3).FillQty(3) = 0             'DC03
    sData(3).FillQty(4) = 0             'DC04
    sData(3).FillQty(5) = 0             'DC05
    sData(3).FillQty(6) = 0             'DC06
    sData(3).FillQty(7) = 0             'DC07
    sData(3).FillQty(8) = 0             'DC08
    sData(3).FillQty(9) = 0             'DC09
    
    sData(4).LcId = 5                   'Store
    sData(4).Priority = 1               'Priority
    sData(4).AvailQty = 0               'Available Quantity
    sData(4).MinQty = 20                'Minimum Quantity
    sData(4).Need = 0                   'Need
    sData(4).SugAlloc = 0               'Suggested Allocation
    sData(4).Alloc = 0                  'Allocation
    sData(4).FillPath = "100000000"     'Fill Flags
    sData(4).FillQty(1) = 0             'DC01
    sData(4).FillQty(2) = 0             'DC02
    sData(4).FillQty(3) = 0             'DC03
    sData(4).FillQty(4) = 0             'DC04
    sData(4).FillQty(5) = 0             'DC05
    sData(4).FillQty(6) = 0             'DC06
    sData(4).FillQty(7) = 0             'DC07
    sData(4).FillQty(8) = 0             'DC08
    sData(4).FillQty(9) = 0             'DC09
    
    sData(5).LcId = 6                   'Store
    sData(5).Priority = 1               'Priority
    sData(5).AvailQty = 0               'Available Quantity
    sData(5).MinQty = 10                'Minimum Quantity
    sData(5).Need = 0                   'Need
    sData(5).SugAlloc = 0               'Suggested Allocation
    sData(5).Alloc = 0                  'Allocation
    sData(5).FillPath = "100000000"     'Fill Flags
    sData(5).FillQty(1) = 0             'DC01
    sData(5).FillQty(2) = 0             'DC02
    sData(5).FillQty(3) = 0             'DC03
    sData(5).FillQty(4) = 0             'DC04
    sData(5).FillQty(5) = 0             'DC05
    sData(5).FillQty(6) = 0             'DC06
    sData(5).FillQty(7) = 0             'DC07
    sData(5).FillQty(8) = 0             'DC08
    sData(5).FillQty(9) = 0             'DC09
    
    sData(6).LcId = 7                   'Store
    sData(6).Priority = 1               'Priority
    sData(6).AvailQty = 0               'Available Quantity
    sData(6).MinQty = 20                'Minimum Quantity
    sData(6).Need = 0                   'Need
    sData(6).SugAlloc = 0               'Suggested Allocation
    sData(6).Alloc = 0                  'Allocation
    sData(6).FillPath = "100000000"     'Fill Flags
    sData(6).FillQty(1) = 0             'DC01
    sData(6).FillQty(2) = 0             'DC02
    sData(6).FillQty(3) = 0             'DC03
    sData(6).FillQty(4) = 0             'DC04
    sData(6).FillQty(5) = 0             'DC05
    sData(6).FillQty(6) = 0             'DC06
    sData(6).FillQty(7) = 0             'DC07
    sData(6).FillQty(8) = 0             'DC08
    sData(6).FillQty(9) = 0             'DC09
    
    sData(7).LcId = 8                   'Store
    sData(7).Priority = 1               'Priority
    sData(7).AvailQty = 0               'Available Quantity
    sData(7).MinQty = 5                'Minimum Quantity
    sData(7).Need = 0                   'Need
    sData(7).SugAlloc = 0               'Suggested Allocation
    sData(7).Alloc = 0                  'Allocation
    sData(7).FillPath = "100000000"     'Fill Flags
    sData(7).FillQty(1) = 0             'DC01
    sData(7).FillQty(2) = 0             'DC02
    sData(7).FillQty(3) = 0             'DC03
    sData(7).FillQty(4) = 0             'DC04
    sData(7).FillQty(5) = 0             'DC05
    sData(7).FillQty(6) = 0             'DC06
    sData(7).FillQty(7) = 0             'DC07
    sData(7).FillQty(8) = 0             'DC08
    sData(7).FillQty(9) = 0             'DC09
    
    sData(8).LcId = 9                   'Store
    sData(8).Priority = 1               'Priority
    sData(8).AvailQty = 0               'Available Quantity
    sData(8).MinQty = 10                'Minimum Quantity
    sData(8).Need = 0                   'Need
    sData(8).SugAlloc = 0               'Suggested Allocation
    sData(8).Alloc = 0                  'Allocation
    sData(8).FillPath = "100000000"     'Fill Flags
    sData(8).FillQty(1) = 0             'DC01
    sData(8).FillQty(2) = 0             'DC02
    sData(8).FillQty(3) = 0             'DC03
    sData(8).FillQty(4) = 0             'DC04
    sData(8).FillQty(5) = 0             'DC05
    sData(8).FillQty(6) = 0             'DC06
    sData(8).FillQty(7) = 0             'DC07
    sData(8).FillQty(8) = 0             'DC08
    sData(8).FillQty(9) = 0             'DC09
    
    sData(9).LcId = 10                  'Store
    sData(9).Priority = 1               'Priority
    sData(9).AvailQty = 0               'Available Quantity
    sData(9).MinQty = 10                'Minimum Quantity
    sData(9).Need = 0                   'Need
    sData(9).SugAlloc = 0               'Suggested Allocation
    sData(9).Alloc = 0                  'Allocation
    sData(9).FillPath = "100000000"     'Fill Flags
    sData(9).FillQty(1) = 0             'DC01
    sData(9).FillQty(2) = 0             'DC02
    sData(9).FillQty(3) = 0             'DC03
    sData(9).FillQty(4) = 0             'DC04
    sData(9).FillQty(5) = 0             'DC05
    sData(9).FillQty(6) = 0             'DC06
    sData(9).FillQty(7) = 0             'DC07
    sData(9).FillQty(8) = 0             'DC08
    sData(9).FillQty(9) = 0             'DC09
    
    sData(10).LcId = "Total"            'Store
    sData(10).Priority = 0              'Priority
    sData(10).AvailQty = 0              'Available Quantity
    sData(10).MinQty = 0                'Minimum Quantity
    sData(10).Need = 0                  'Need
    sData(10).SugAlloc = 0              'Suggested Allocation
    sData(10).Alloc = 0                 'Allocation
    sData(10).FillKey = ""              'Fill Flags
    sData(10).FillQty(1) = 0            'DC01
    sData(10).FillQty(2) = 0            'DC02
    sData(10).FillQty(3) = 0            'DC03
    sData(10).FillQty(4) = 0            'DC04
    sData(10).FillQty(5) = 0            'DC05
    sData(10).FillQty(6) = 0            'DC06
    sData(10).FillQty(7) = 0            'DC07
    sData(10).FillQty(8) = 0            'DC08
    sData(10).FillQty(9) = 0            'DC09
    
    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True
    
    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Proportional To Need' Allocation Method. Sourcing is from a single distribution center"
    
    'Set Number of DC's
    
    NbrDCs = 1
    
End Function
Function Scenario_02()

    Dim i As Integer
    
    ReDim dData(0 To 8)
    ReDim sData(0 To 10)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 100     'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    For i = 1 To 8
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 0
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 1               'Priority
    sData(0).AvailQty = 190             'Available Quantity
    sData(0).MinQty = 200               'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "100000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
   
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 20              'Available Quantity
    sData(1).MinQty = 40                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "100000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 1               'Priority
    sData(2).AvailQty = 150             'Available Quantity
    sData(2).MinQty = 170               'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 0               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "100000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = 4                   'Store
    sData(3).Priority = 1               'Priority
    sData(3).AvailQty = 10              'Available Quantity
    sData(3).MinQty = 20                'Minimum Quantity
    sData(3).Need = 0                   'Need
    sData(3).SugAlloc = 0               'Suggested Allocation
    sData(3).Alloc = 0                  'Allocation
    sData(3).FillPath = "100000000"     'Fill Flags
    sData(3).FillQty(1) = 0             'DC01
    sData(3).FillQty(2) = 0             'DC02
    sData(3).FillQty(3) = 0             'DC03
    sData(3).FillQty(4) = 0             'DC04
    sData(3).FillQty(5) = 0             'DC05
    sData(3).FillQty(6) = 0             'DC06
    sData(3).FillQty(7) = 0             'DC07
    sData(3).FillQty(8) = 0             'DC08
    sData(3).FillQty(9) = 0             'DC09
    
    sData(4).LcId = 5                   'Store
    sData(4).Priority = 1               'Priority
    sData(4).AvailQty = 50              'Available Quantity
    sData(4).MinQty = 70                'Minimum Quantity
    sData(4).Need = 0                   'Need
    sData(4).SugAlloc = 0               'Suggested Allocation
    sData(4).Alloc = 0                  'Allocation
    sData(4).FillPath = "100000000"     'Fill Flags
    sData(4).FillQty(1) = 0             'DC01
    sData(4).FillQty(2) = 0             'DC02
    sData(4).FillQty(3) = 0             'DC03
    sData(4).FillQty(4) = 0             'DC04
    sData(4).FillQty(5) = 0             'DC05
    sData(4).FillQty(6) = 0             'DC06
    sData(4).FillQty(7) = 0             'DC07
    sData(4).FillQty(8) = 0             'DC08
    sData(4).FillQty(9) = 0             'DC09
    
    sData(5).LcId = 6                   'Store
    sData(5).Priority = 1               'Priority
    sData(5).AvailQty = 0               'Available Quantity
    sData(5).MinQty = 10                'Minimum Quantity
    sData(5).Need = 0                   'Need
    sData(5).SugAlloc = 0               'Suggested Allocation
    sData(5).Alloc = 0                  'Allocation
    sData(5).FillPath = "100000000"     'Fill Flags
    sData(5).FillQty(1) = 0             'DC01
    sData(5).FillQty(2) = 0             'DC02
    sData(5).FillQty(3) = 0             'DC03
    sData(5).FillQty(4) = 0             'DC04
    sData(5).FillQty(5) = 0             'DC05
    sData(5).FillQty(6) = 0             'DC06
    sData(5).FillQty(7) = 0             'DC07
    sData(5).FillQty(8) = 0             'DC08
    sData(5).FillQty(9) = 0             'DC09
    
    sData(6).LcId = 7                   'Store
    sData(6).Priority = 1               'Priority
    sData(6).AvailQty = 20              'Available Quantity
    sData(6).MinQty = 40                'Minimum Quantity
    sData(6).Need = 0                   'Need
    sData(6).SugAlloc = 0               'Suggested Allocation
    sData(6).Alloc = 0                  'Allocation
    sData(6).FillPath = "100000000"     'Fill Flags
    sData(6).FillQty(1) = 0             'DC01
    sData(6).FillQty(2) = 0             'DC02
    sData(6).FillQty(3) = 0             'DC03
    sData(6).FillQty(4) = 0             'DC04
    sData(6).FillQty(5) = 0             'DC05
    sData(6).FillQty(6) = 0             'DC06
    sData(6).FillQty(7) = 0             'DC07
    sData(6).FillQty(8) = 0             'DC08
    sData(6).FillQty(9) = 0             'DC09
    
    sData(7).LcId = 8                   'Store
    sData(7).Priority = 1               'Priority
    sData(7).AvailQty = 0               'Available Quantity
    sData(7).MinQty = 5                'Minimum Quantity
    sData(7).Need = 0                   'Need
    sData(7).SugAlloc = 0               'Suggested Allocation
    sData(7).Alloc = 0                  'Allocation
    sData(7).FillPath = "100000000"     'Fill Flags
    sData(7).FillQty(1) = 0             'DC01
    sData(7).FillQty(2) = 0             'DC02
    sData(7).FillQty(3) = 0             'DC03
    sData(7).FillQty(4) = 0             'DC04
    sData(7).FillQty(5) = 0             'DC05
    sData(7).FillQty(6) = 0             'DC06
    sData(7).FillQty(7) = 0             'DC07
    sData(7).FillQty(8) = 0             'DC08
    sData(7).FillQty(9) = 0             'DC09
    
    sData(8).LcId = 9                   'Store
    sData(8).Priority = 1               'Priority
    sData(8).AvailQty = 10              'Available Quantity
    sData(8).MinQty = 20                'Minimum Quantity
    sData(8).Need = 0                   'Need
    sData(8).SugAlloc = 0               'Suggested Allocation
    sData(8).Alloc = 0                  'Allocation
    sData(8).FillPath = "100000000"     'Fill Flags
    sData(8).FillQty(1) = 0             'DC01
    sData(8).FillQty(2) = 0             'DC02
    sData(8).FillQty(3) = 0             'DC03
    sData(8).FillQty(4) = 0             'DC04
    sData(8).FillQty(5) = 0             'DC05
    sData(8).FillQty(6) = 0             'DC06
    sData(8).FillQty(7) = 0             'DC07
    sData(8).FillQty(8) = 0             'DC08
    sData(8).FillQty(9) = 0             'DC09
    
    sData(9).LcId = 10                  'Store
    sData(9).Priority = 1               'Priority
    sData(9).AvailQty = 10              'Available Quantity
    sData(9).MinQty = 20                'Minimum Quantity
    sData(9).Need = 0                   'Need
    sData(9).SugAlloc = 0               'Suggested Allocation
    sData(9).Alloc = 0                  'Allocation
    sData(9).FillPath = "100000000"     'Fill Flags
    sData(9).FillQty(1) = 0             'DC01
    sData(9).FillQty(2) = 0             'DC02
    sData(9).FillQty(3) = 0             'DC03
    sData(9).FillQty(4) = 0             'DC04
    sData(9).FillQty(5) = 0             'DC05
    sData(9).FillQty(6) = 0             'DC06
    sData(9).FillQty(7) = 0             'DC07
    sData(9).FillQty(8) = 0             'DC08
    sData(9).FillQty(9) = 0             'DC09
    
    sData(10).LcId = "Total"            'Store
    sData(10).Priority = 0              'Priority
    sData(10).AvailQty = 0              'Available Quantity
    sData(10).MinQty = 0                'Minimum Quantity
    sData(10).Need = 0                  'Need
    sData(10).SugAlloc = 0              'Suggested Allocation
    sData(10).Alloc = 0                 'Allocation
    sData(10).FillPath = ""             'Fill Flags
    sData(10).FillQty(1) = 0            'DC01
    sData(10).FillQty(2) = 0            'DC02
    sData(10).FillQty(3) = 0            'DC03
    sData(10).FillQty(4) = 0            'DC04
    sData(10).FillQty(5) = 0            'DC05
    sData(10).FillQty(6) = 0            'DC06
    sData(10).FillQty(7) = 0            'DC07
    sData(10).FillQty(8) = 0            'DC08
    sData(10).FillQty(9) = 0            'DC09

    'Set Allocation Rule
    
    Me.optAllocMethod(1).Value = True

    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Balance Relative Inventory' Allocation Method. Sourcing is from a single distribution center"
    
    'Set Number of DC's
    
    NbrDCs = 1

End Function

Function Scenario_03()

    Dim i As Integer
    
    ReDim dData(0 To 8)
    ReDim sData(0 To 10)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 100     'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    For i = 1 To 8
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 0
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 2               'Priority
    sData(0).AvailQty = 0               'Available Quantity
    sData(0).MinQty = 10                'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "100000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
    
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 0               'Available Quantity
    sData(1).MinQty = 20                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "100000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 1               'Priority
    sData(2).AvailQty = 0               'Available Quantity
    sData(2).MinQty = 20                'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 0               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "100000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = 4                   'Store
    sData(3).Priority = 2               'Priority
    sData(3).AvailQty = 0               'Available Quantity
    sData(3).MinQty = 10                'Minimum Quantity
    sData(3).Need = 0                   'Need
    sData(3).SugAlloc = 0               'Suggested Allocation
    sData(3).Alloc = 0                  'Allocation
    sData(3).FillPath = "100000000"     'Fill Flags
    sData(3).FillQty(1) = 0             'DC01
    sData(3).FillQty(2) = 0             'DC02
    sData(3).FillQty(3) = 0             'DC03
    sData(3).FillQty(4) = 0             'DC04
    sData(3).FillQty(5) = 0             'DC05
    sData(3).FillQty(6) = 0             'DC06
    sData(3).FillQty(7) = 0             'DC07
    sData(3).FillQty(8) = 0             'DC08
    sData(3).FillQty(9) = 0             'DC09
    
    sData(4).LcId = 5                   'Store
    sData(4).Priority = 2               'Priority
    sData(4).AvailQty = 0               'Available Quantity
    sData(4).MinQty = 20                'Minimum Quantity
    sData(4).Need = 0                   'Need
    sData(4).SugAlloc = 0               'Suggested Allocation
    sData(4).Alloc = 0                  'Allocation
    sData(4).FillPath = "100000000"     'Fill Flags
    sData(4).FillQty(1) = 0             'DC01
    sData(4).FillQty(2) = 0             'DC02
    sData(4).FillQty(3) = 0             'DC03
    sData(4).FillQty(4) = 0             'DC04
    sData(4).FillQty(5) = 0             'DC05
    sData(4).FillQty(6) = 0             'DC06
    sData(4).FillQty(7) = 0             'DC07
    sData(4).FillQty(8) = 0             'DC08
    sData(4).FillQty(9) = 0             'DC09
    
    sData(5).LcId = 6                   'Store
    sData(5).Priority = 2               'Priority
    sData(5).AvailQty = 0               'Available Quantity
    sData(5).MinQty = 10                'Minimum Quantity
    sData(5).Need = 0                   'Need
    sData(5).SugAlloc = 0               'Suggested Allocation
    sData(5).Alloc = 0                  'Allocation
    sData(5).FillPath = "100000000"     'Fill Flags
    sData(5).FillQty(1) = 0             'DC01
    sData(5).FillQty(2) = 0             'DC02
    sData(5).FillQty(3) = 0             'DC03
    sData(5).FillQty(4) = 0             'DC04
    sData(5).FillQty(5) = 0             'DC05
    sData(5).FillQty(6) = 0             'DC06
    sData(5).FillQty(7) = 0             'DC07
    sData(5).FillQty(8) = 0             'DC08
    sData(5).FillQty(9) = 0             'DC09
    
    sData(6).LcId = 7                   'Store
    sData(6).Priority = 1               'Priority
    sData(6).AvailQty = 0               'Available Quantity
    sData(6).MinQty = 20                'Minimum Quantity
    sData(6).Need = 0                   'Need
    sData(6).SugAlloc = 0               'Suggested Allocation
    sData(6).Alloc = 0                  'Allocation
    sData(6).FillPath = "100000000"     'Fill Flags
    sData(6).FillQty(1) = 0             'DC01
    sData(6).FillQty(2) = 0             'DC02
    sData(6).FillQty(3) = 0             'DC03
    sData(6).FillQty(4) = 0             'DC04
    sData(6).FillQty(5) = 0             'DC05
    sData(6).FillQty(6) = 0             'DC06
    sData(6).FillQty(7) = 0             'DC07
    sData(6).FillQty(8) = 0             'DC08
    sData(6).FillQty(9) = 0             'DC09
    
    sData(7).LcId = 8                   'Store
    sData(7).Priority = 1               'Priority
    sData(7).AvailQty = 0               'Available Quantity
    sData(7).MinQty = 5                'Minimum Quantity
    sData(7).Need = 0                   'Need
    sData(7).SugAlloc = 0               'Suggested Allocation
    sData(7).Alloc = 0                  'Allocation
    sData(7).FillPath = "100000000"     'Fill Flags
    sData(7).FillQty(1) = 0             'DC01
    sData(7).FillQty(2) = 0             'DC02
    sData(7).FillQty(3) = 0             'DC03
    sData(7).FillQty(4) = 0             'DC04
    sData(7).FillQty(5) = 0             'DC05
    sData(7).FillQty(6) = 0             'DC06
    sData(7).FillQty(7) = 0             'DC07
    sData(7).FillQty(8) = 0             'DC08
    sData(7).FillQty(9) = 0             'DC09
    
    sData(8).LcId = 9                   'Store
    sData(8).Priority = 2               'Priority
    sData(8).AvailQty = 0               'Available Quantity
    sData(8).MinQty = 10                'Minimum Quantity
    sData(8).Need = 0                   'Need
    sData(8).SugAlloc = 0               'Suggested Allocation
    sData(8).Alloc = 0                  'Allocation
    sData(8).FillPath = "100000000"     'Fill Flags
    sData(8).FillQty(1) = 0             'DC01
    sData(8).FillQty(2) = 0             'DC02
    sData(8).FillQty(3) = 0             'DC03
    sData(8).FillQty(4) = 0             'DC04
    sData(8).FillQty(5) = 0             'DC05
    sData(8).FillQty(6) = 0             'DC06
    sData(8).FillQty(7) = 0             'DC07
    sData(8).FillQty(8) = 0             'DC08
    sData(8).FillQty(9) = 0             'DC09
    
    sData(9).LcId = 10                  'Store
    sData(9).Priority = 1               'Priority
    sData(9).AvailQty = 0               'Available Quantity
    sData(9).MinQty = 10                'Minimum Quantity
    sData(9).Need = 0                   'Need
    sData(9).SugAlloc = 0               'Suggested Allocation
    sData(9).Alloc = 0                  'Allocation
    sData(9).FillPath = "100000000"     'Fill Flags
    sData(9).FillQty(1) = 0             'DC01
    sData(9).FillQty(2) = 0             'DC02
    sData(9).FillQty(3) = 0             'DC03
    sData(9).FillQty(4) = 0             'DC04
    sData(9).FillQty(5) = 0             'DC05
    sData(9).FillQty(6) = 0             'DC06
    sData(9).FillQty(7) = 0             'DC07
    sData(9).FillQty(8) = 0             'DC08
    sData(9).FillQty(9) = 0             'DC09
    
    sData(10).LcId = "Total"            'Store
    sData(10).Priority = 0              'Priority
    sData(10).AvailQty = 0              'Available Quantity
    sData(10).MinQty = 0                'Minimum Quantity
    sData(10).Need = 0                  'Need
    sData(10).SugAlloc = 0              'Suggested Allocation
    sData(10).Alloc = 0                 'Allocation
    sData(10).FillKey = ""              'Fill Flags
    sData(10).FillQty(1) = 0            'DC01
    sData(10).FillQty(2) = 0            'DC02
    sData(10).FillQty(3) = 0            'DC03
    sData(10).FillQty(4) = 0            'DC04
    sData(10).FillQty(5) = 0            'DC05
    sData(10).FillQty(6) = 0            'DC06
    sData(10).FillQty(7) = 0            'DC07
    sData(10).FillQty(8) = 0            'DC08
    sData(10).FillQty(9) = 0            'DC09

    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True

    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Proportional To Need' Allocation Methods. Stores are divided into two priority groups. Priority 1 items are allocated before priority 2 items. Sourcing is from a single distribution center"

    'Set Number of DC's
    
    NbrDCs = 1

End Function


Function Scenario_04()

    Dim i As Integer
    
    ReDim dData(0 To 8)
    ReDim sData(0 To 10)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 50      'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    dData(1).LcId = "2"         'DC Id
    dData(1).AvailQty = 50      'Available Quantity
    dData(1).AllocQty = 0       'Allocated Quantity
    
    For i = 2 To 8
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 0
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 1               'Priority
    sData(0).AvailQty = 0               'Available Quantity
    sData(0).MinQty = 10                'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "120000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
    
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 0               'Available Quantity
    sData(1).MinQty = 20                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "120000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 1               'Priority
    sData(2).AvailQty = 0               'Available Quantity
    sData(2).MinQty = 20                'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 0               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "120000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = 4                   'Store
    sData(3).Priority = 1               'Priority
    sData(3).AvailQty = 0               'Available Quantity
    sData(3).MinQty = 10                'Minimum Quantity
    sData(3).Need = 0                   'Need
    sData(3).SugAlloc = 0               'Suggested Allocation
    sData(3).Alloc = 0                  'Allocation
    sData(3).FillPath = "120000000"     'Fill Flags
    sData(3).FillQty(1) = 0             'DC01
    sData(3).FillQty(2) = 0             'DC02
    sData(3).FillQty(3) = 0             'DC03
    sData(3).FillQty(4) = 0             'DC04
    sData(3).FillQty(5) = 0             'DC05
    sData(3).FillQty(6) = 0             'DC06
    sData(3).FillQty(7) = 0             'DC07
    sData(3).FillQty(8) = 0             'DC08
    sData(3).FillQty(9) = 0             'DC09
    
    sData(4).LcId = 5                   'Store
    sData(4).Priority = 1               'Priority
    sData(4).AvailQty = 0               'Available Quantity
    sData(4).MinQty = 20                'Minimum Quantity
    sData(4).Need = 0                   'Need
    sData(4).SugAlloc = 0               'Suggested Allocation
    sData(4).Alloc = 0                  'Allocation
    sData(4).FillPath = "120000000"     'Fill Flags
    sData(4).FillQty(1) = 0             'DC01
    sData(4).FillQty(2) = 0             'DC02
    sData(4).FillQty(3) = 0             'DC03
    sData(4).FillQty(4) = 0             'DC04
    sData(4).FillQty(5) = 0             'DC05
    sData(4).FillQty(6) = 0             'DC06
    sData(4).FillQty(7) = 0             'DC07
    sData(4).FillQty(8) = 0             'DC08
    sData(4).FillQty(9) = 0             'DC09
    
    sData(5).LcId = 6                   'Store
    sData(5).Priority = 1               'Priority
    sData(5).AvailQty = 0               'Available Quantity
    sData(5).MinQty = 10                'Minimum Quantity
    sData(5).Need = 0                   'Need
    sData(5).SugAlloc = 0               'Suggested Allocation
    sData(5).Alloc = 0                  'Allocation
    sData(5).FillPath = "210000000"     'Fill Flags
    sData(5).FillQty(1) = 0             'DC01
    sData(5).FillQty(2) = 0             'DC02
    sData(5).FillQty(3) = 0             'DC03
    sData(5).FillQty(4) = 0             'DC04
    sData(5).FillQty(5) = 0             'DC05
    sData(5).FillQty(6) = 0             'DC06
    sData(5).FillQty(7) = 0             'DC07
    sData(5).FillQty(8) = 0             'DC08
    sData(5).FillQty(9) = 0             'DC09
    
    sData(6).LcId = 7                   'Store
    sData(6).Priority = 1               'Priority
    sData(6).AvailQty = 0               'Available Quantity
    sData(6).MinQty = 20                'Minimum Quantity
    sData(6).Need = 0                   'Need
    sData(6).SugAlloc = 0               'Suggested Allocation
    sData(6).Alloc = 0                  'Allocation
    sData(6).FillPath = "210000000"     'Fill Flags
    sData(6).FillQty(1) = 0             'DC01
    sData(6).FillQty(2) = 0             'DC02
    sData(6).FillQty(3) = 0             'DC03
    sData(6).FillQty(4) = 0             'DC04
    sData(6).FillQty(5) = 0             'DC05
    sData(6).FillQty(6) = 0             'DC06
    sData(6).FillQty(7) = 0             'DC07
    sData(6).FillQty(8) = 0             'DC08
    sData(6).FillQty(9) = 0             'DC09
    
    sData(7).LcId = 8                   'Store
    sData(7).Priority = 1               'Priority
    sData(7).AvailQty = 0               'Available Quantity
    sData(7).MinQty = 5                'Minimum Quantity
    sData(7).Need = 0                   'Need
    sData(7).SugAlloc = 0               'Suggested Allocation
    sData(7).Alloc = 0                  'Allocation
    sData(7).FillPath = "210000000"     'Fill Flags
    sData(7).FillQty(1) = 0             'DC01
    sData(7).FillQty(2) = 0             'DC02
    sData(7).FillQty(3) = 0             'DC03
    sData(7).FillQty(4) = 0             'DC04
    sData(7).FillQty(5) = 0             'DC05
    sData(7).FillQty(6) = 0             'DC06
    sData(7).FillQty(7) = 0             'DC07
    sData(7).FillQty(8) = 0             'DC08
    sData(7).FillQty(9) = 0             'DC09
    
    sData(8).LcId = 9                   'Store
    sData(8).Priority = 1               'Priority
    sData(8).AvailQty = 0               'Available Quantity
    sData(8).MinQty = 10                'Minimum Quantity
    sData(8).Need = 0                   'Need
    sData(8).SugAlloc = 0               'Suggested Allocation
    sData(8).Alloc = 0                  'Allocation
    sData(8).FillPath = "210000000"     'Fill Flags
    sData(8).FillQty(1) = 0             'DC01
    sData(8).FillQty(2) = 0             'DC02
    sData(8).FillQty(3) = 0             'DC03
    sData(8).FillQty(4) = 0             'DC04
    sData(8).FillQty(5) = 0             'DC05
    sData(8).FillQty(6) = 0             'DC06
    sData(8).FillQty(7) = 0             'DC07
    sData(8).FillQty(8) = 0             'DC08
    sData(8).FillQty(9) = 0             'DC09
    
    sData(9).LcId = 10                  'Store
    sData(9).Priority = 1               'Priority
    sData(9).AvailQty = 0               'Available Quantity
    sData(9).MinQty = 10                'Minimum Quantity
    sData(9).Need = 0                   'Need
    sData(9).SugAlloc = 0               'Suggested Allocation
    sData(9).Alloc = 0                  'Allocation
    sData(9).FillPath = "210000000"     'Fill Flags
    sData(9).FillQty(1) = 0             'DC01
    sData(9).FillQty(2) = 0             'DC02
    sData(9).FillQty(3) = 0             'DC03
    sData(9).FillQty(4) = 0             'DC04
    sData(9).FillQty(5) = 0             'DC05
    sData(9).FillQty(6) = 0             'DC06
    sData(9).FillQty(7) = 0             'DC07
    sData(9).FillQty(8) = 0             'DC08
    sData(9).FillQty(9) = 0             'DC09
    
    sData(10).LcId = "Total"            'Store
    sData(10).Priority = 0              'Priority
    sData(10).AvailQty = 0              'Available Quantity
    sData(10).MinQty = 0                'Minimum Quantity
    sData(10).Need = 0                  'Need
    sData(10).SugAlloc = 0              'Suggested Allocation
    sData(10).Alloc = 0                 'Allocation
    sData(10).FillKey = ""              'Fill Flags
    sData(10).FillQty(1) = 0            'DC01
    sData(10).FillQty(2) = 0            'DC02
    sData(10).FillQty(3) = 0            'DC03
    sData(10).FillQty(4) = 0            'DC04
    sData(10).FillQty(5) = 0            'DC05
    sData(10).FillQty(6) = 0            'DC06
    sData(10).FillQty(7) = 0            'DC07
    sData(10).FillQty(8) = 0            'DC08
    sData(10).FillQty(9) = 0            'DC09
    
    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True

    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Proportional To Need' Allocation Methods. Sourcing is from two distribution center. There are no filling locations constraints."

    'Set Number of DC's
    
    NbrDCs = 2

End Function

Function Scenario_05()

    Dim i As Integer
    
    ReDim dData(0 To 8)
    ReDim sData(0 To 10)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 10      'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    dData(1).LcId = "2"         'DC Id
    dData(1).AvailQty = 90      'Available Quantity
    dData(1).AllocQty = 0       'Allocated Quantity
    
    For i = 2 To 8
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 0
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 1               'Priority
    sData(0).AvailQty = 0               'Available Quantity
    sData(0).MinQty = 10                'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "100000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
    
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 0               'Available Quantity
    sData(1).MinQty = 20                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "100000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 1               'Priority
    sData(2).AvailQty = 0               'Available Quantity
    sData(2).MinQty = 20                'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 0               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "100000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = 4                   'Store
    sData(3).Priority = 1               'Priority
    sData(3).AvailQty = 0               'Available Quantity
    sData(3).MinQty = 10                'Minimum Quantity
    sData(3).Need = 0                   'Need
    sData(3).SugAlloc = 0               'Suggested Allocation
    sData(3).Alloc = 0                  'Allocation
    sData(3).FillPath = "100000000"     'Fill Flags
    sData(3).FillQty(1) = 0             'DC01
    sData(3).FillQty(2) = 0             'DC02
    sData(3).FillQty(3) = 0             'DC03
    sData(3).FillQty(4) = 0             'DC04
    sData(3).FillQty(5) = 0             'DC05
    sData(3).FillQty(6) = 0             'DC06
    sData(3).FillQty(7) = 0             'DC07
    sData(3).FillQty(8) = 0             'DC08
    sData(3).FillQty(9) = 0             'DC09
    
    sData(4).LcId = 5                   'Store
    sData(4).Priority = 1               'Priority
    sData(4).AvailQty = 0               'Available Quantity
    sData(4).MinQty = 20                'Minimum Quantity
    sData(4).Need = 0                   'Need
    sData(4).SugAlloc = 0               'Suggested Allocation
    sData(4).Alloc = 0                  'Allocation
    sData(4).FillPath = "100000000"     'Fill Flags
    sData(4).FillQty(1) = 0             'DC01
    sData(4).FillQty(2) = 0             'DC02
    sData(4).FillQty(3) = 0             'DC03
    sData(4).FillQty(4) = 0             'DC04
    sData(4).FillQty(5) = 0             'DC05
    sData(4).FillQty(6) = 0             'DC06
    sData(4).FillQty(7) = 0             'DC07
    sData(4).FillQty(8) = 0             'DC08
    sData(4).FillQty(9) = 0             'DC09
    
    sData(5).LcId = 6                   'Store
    sData(5).Priority = 1               'Priority
    sData(5).AvailQty = 0               'Available Quantity
    sData(5).MinQty = 10                'Minimum Quantity
    sData(5).Need = 0                   'Need
    sData(5).SugAlloc = 0               'Suggested Allocation
    sData(5).Alloc = 0                  'Allocation
    sData(5).FillPath = "210000000"     'Fill Flags
    sData(5).FillQty(1) = 0             'DC01
    sData(5).FillQty(2) = 0             'DC02
    sData(5).FillQty(3) = 0             'DC03
    sData(5).FillQty(4) = 0             'DC04
    sData(5).FillQty(5) = 0             'DC05
    sData(5).FillQty(6) = 0             'DC06
    sData(5).FillQty(7) = 0             'DC07
    sData(5).FillQty(8) = 0             'DC08
    sData(5).FillQty(9) = 0             'DC09
    
    sData(6).LcId = 7                   'Store
    sData(6).Priority = 1               'Priority
    sData(6).AvailQty = 0               'Available Quantity
    sData(6).MinQty = 20                'Minimum Quantity
    sData(6).Need = 0                   'Need
    sData(6).SugAlloc = 0               'Suggested Allocation
    sData(6).Alloc = 0                  'Allocation
    sData(6).FillPath = "210000000"     'Fill Flags
    sData(6).FillQty(1) = 0             'DC01
    sData(6).FillQty(2) = 0             'DC02
    sData(6).FillQty(3) = 0             'DC03
    sData(6).FillQty(4) = 0             'DC04
    sData(6).FillQty(5) = 0             'DC05
    sData(6).FillQty(6) = 0             'DC06
    sData(6).FillQty(7) = 0             'DC07
    sData(6).FillQty(8) = 0             'DC08
    sData(6).FillQty(9) = 0             'DC09
    
    sData(7).LcId = 8                   'Store
    sData(7).Priority = 1               'Priority
    sData(7).AvailQty = 0               'Available Quantity
    sData(7).MinQty = 5                'Minimum Quantity
    sData(7).Need = 0                   'Need
    sData(7).SugAlloc = 0               'Suggested Allocation
    sData(7).Alloc = 0                  'Allocation
    sData(7).FillPath = "210000000"     'Fill Flags
    sData(7).FillQty(1) = 0             'DC01
    sData(7).FillQty(2) = 0             'DC02
    sData(7).FillQty(3) = 0             'DC03
    sData(7).FillQty(4) = 0             'DC04
    sData(7).FillQty(5) = 0             'DC05
    sData(7).FillQty(6) = 0             'DC06
    sData(7).FillQty(7) = 0             'DC07
    sData(7).FillQty(8) = 0             'DC08
    sData(7).FillQty(9) = 0             'DC09
    
    sData(8).LcId = 9                   'Store
    sData(8).Priority = 1               'Priority
    sData(8).AvailQty = 0               'Available Quantity
    sData(8).MinQty = 10                'Minimum Quantity
    sData(8).Need = 0                   'Need
    sData(8).SugAlloc = 0               'Suggested Allocation
    sData(8).Alloc = 0                  'Allocation
    sData(8).FillPath = "210000000"     'Fill Flags
    sData(8).FillQty(1) = 0             'DC01
    sData(8).FillQty(2) = 0             'DC02
    sData(8).FillQty(3) = 0             'DC03
    sData(8).FillQty(4) = 0             'DC04
    sData(8).FillQty(5) = 0             'DC05
    sData(8).FillQty(6) = 0             'DC06
    sData(8).FillQty(7) = 0             'DC07
    sData(8).FillQty(8) = 0             'DC08
    sData(8).FillQty(9) = 0             'DC09
    
    sData(9).LcId = 10                  'Store
    sData(9).Priority = 1               'Priority
    sData(9).AvailQty = 0               'Available Quantity
    sData(9).MinQty = 10                'Minimum Quantity
    sData(9).Need = 0                   'Need
    sData(9).SugAlloc = 0               'Suggested Allocation
    sData(9).Alloc = 0                  'Allocation
    sData(9).FillPath = "210000000"     'Fill Flags
    sData(9).FillQty(1) = 0             'DC01
    sData(9).FillQty(2) = 0             'DC02
    sData(9).FillQty(3) = 0             'DC03
    sData(9).FillQty(4) = 0             'DC04
    sData(9).FillQty(5) = 0             'DC05
    sData(9).FillQty(6) = 0             'DC06
    sData(9).FillQty(7) = 0             'DC07
    sData(9).FillQty(8) = 0             'DC08
    sData(9).FillQty(9) = 0             'DC09
    
    sData(10).LcId = "Total"            'Store
    sData(10).Priority = 0              'Priority
    sData(10).AvailQty = 0              'Available Quantity
    sData(10).MinQty = 0                'Minimum Quantity
    sData(10).Need = 0                  'Need
    sData(10).SugAlloc = 0              'Suggested Allocation
    sData(10).Alloc = 0                 'Allocation
    sData(10).FillKey = ""              'Fill Flags
    sData(10).FillQty(1) = 0            'DC01
    sData(10).FillQty(2) = 0            'DC02
    sData(10).FillQty(3) = 0            'DC03
    sData(10).FillQty(4) = 0            'DC04
    sData(10).FillQty(5) = 0            'DC05
    sData(10).FillQty(6) = 0            'DC06
    sData(10).FillQty(7) = 0            'DC07
    sData(10).FillQty(8) = 0            'DC08
    sData(10).FillQty(9) = 0            'DC09
    
    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True

    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Proportional To Need' Allocation Methods. Sourcing is from two distribution center. Distribution Center 1 is constrainted in that it cannot fill the raw inventory allocation."

    'Set Number of DC's
    
    NbrDCs = 2

End Function



Function Scenario_06()

    Dim i As Integer
    
    ReDim dData(0 To 8)
    ReDim sData(0 To 10)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 20      'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    dData(1).LcId = "2"         'DC Id
    dData(1).AvailQty = 20      'Available Quantity
    dData(1).AllocQty = 0       'Allocated Quantity
    
    dData(2).LcId = "3"         'DC Id
    dData(2).AvailQty = 40      'Available Quantity
    dData(2).AllocQty = 0       'Allocated Quantity
    
    For i = 3 To 8
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 0
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 1               'Priority
    sData(0).AvailQty = 0               'Available Quantity
    sData(0).MinQty = 10                'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "100000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
    
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 0               'Available Quantity
    sData(1).MinQty = 20                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "120000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 1               'Priority
    sData(2).AvailQty = 0               'Available Quantity
    sData(2).MinQty = 30                'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 0               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "200000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = 4                   'Store
    sData(3).Priority = 1               'Priority
    sData(3).AvailQty = 0               'Available Quantity
    sData(3).MinQty = 20                'Minimum Quantity
    sData(3).Need = 0                   'Need
    sData(3).SugAlloc = 0               'Suggested Allocation
    sData(3).Alloc = 0                  'Allocation
    sData(3).FillPath = "230000000"     'Fill Flags
    sData(3).FillQty(1) = 0             'DC01
    sData(3).FillQty(2) = 0             'DC02
    sData(3).FillQty(3) = 0             'DC03
    sData(3).FillQty(4) = 0             'DC04
    sData(3).FillQty(5) = 0             'DC05
    sData(3).FillQty(6) = 0             'DC06
    sData(3).FillQty(7) = 0             'DC07
    sData(3).FillQty(8) = 0             'DC08
    sData(3).FillQty(9) = 0             'DC09
    
    sData(4).LcId = 5                   'Store
    sData(4).Priority = 1               'Priority
    sData(4).AvailQty = 0               'Available Quantity
    sData(4).MinQty = 10                'Minimum Quantity
    sData(4).Need = 0                   'Need
    sData(4).SugAlloc = 0               'Suggested Allocation
    sData(4).Alloc = 0                  'Allocation
    sData(4).FillPath = "300000000"     'Fill Flags
    sData(4).FillQty(1) = 0             'DC01
    sData(4).FillQty(2) = 0             'DC02
    sData(4).FillQty(3) = 0             'DC03
    sData(4).FillQty(4) = 0             'DC04
    sData(4).FillQty(5) = 0             'DC05
    sData(4).FillQty(6) = 0             'DC06
    sData(4).FillQty(7) = 0             'DC07
    sData(4).FillQty(8) = 0             'DC08
    sData(4).FillQty(9) = 0             'DC09
    
    sData(5).LcId = 6                   'Store
    sData(5).Priority = 1               'Priority
    sData(5).AvailQty = 0               'Available Quantity
    sData(5).MinQty = -0                'Minimum Quantity
    sData(5).Need = 0                   'Need
    sData(5).SugAlloc = 0               'Suggested Allocation
    sData(5).Alloc = 0                  'Allocation
    sData(5).FillPath = "000000000"     'Fill Flags
    sData(5).FillQty(1) = 0             'DC01
    sData(5).FillQty(2) = 0             'DC02
    sData(5).FillQty(3) = 0             'DC03
    sData(5).FillQty(4) = 0             'DC04
    sData(5).FillQty(5) = 0             'DC05
    sData(5).FillQty(6) = 0             'DC06
    sData(5).FillQty(7) = 0             'DC07
    sData(5).FillQty(8) = 0             'DC08
    sData(5).FillQty(9) = 0             'DC09
    
    sData(6).LcId = 7                   'Store
    sData(6).Priority = 1               'Priority
    sData(6).AvailQty = 0               'Available Quantity
    sData(6).MinQty = 0                 'Minimum Quantity
    sData(6).Need = 0                   'Need
    sData(6).SugAlloc = 0               'Suggested Allocation
    sData(6).Alloc = 0                  'Allocation
    sData(6).FillPath = "000000000"     'Fill Flags
    sData(6).FillQty(1) = 0             'DC01
    sData(6).FillQty(2) = 0             'DC02
    sData(6).FillQty(3) = 0             'DC03
    sData(6).FillQty(4) = 0             'DC04
    sData(6).FillQty(5) = 0             'DC05
    sData(6).FillQty(6) = 0             'DC06
    sData(6).FillQty(7) = 0             'DC07
    sData(6).FillQty(8) = 0             'DC08
    sData(6).FillQty(9) = 0             'DC09
    
    sData(7).LcId = 8                   'Store
    sData(7).Priority = 1               'Priority
    sData(7).AvailQty = 0               'Available Quantity
    sData(7).MinQty = 0                'Minimum Quantity
    sData(7).Need = 0                   'Need
    sData(7).SugAlloc = 0               'Suggested Allocation
    sData(7).Alloc = 0                  'Allocation
    sData(7).FillPath = "000000000"     'Fill Flags
    sData(7).FillQty(1) = 0             'DC01
    sData(7).FillQty(2) = 0             'DC02
    sData(7).FillQty(3) = 0             'DC03
    sData(7).FillQty(4) = 0             'DC04
    sData(7).FillQty(5) = 0             'DC05
    sData(7).FillQty(6) = 0             'DC06
    sData(7).FillQty(7) = 0             'DC07
    sData(7).FillQty(8) = 0             'DC08
    sData(7).FillQty(9) = 0             'DC09
    
    sData(8).LcId = 9                   'Store
    sData(8).Priority = 1               'Priority
    sData(8).AvailQty = 0               'Available Quantity
    sData(8).MinQty = 0                 'Minimum Quantity
    sData(8).Need = 0                   'Need
    sData(8).SugAlloc = 0               'Suggested Allocation
    sData(8).Alloc = 0                  'Allocation
    sData(8).FillPath = "000000000"     'Fill Flags
    sData(8).FillQty(1) = 0             'DC01
    sData(8).FillQty(2) = 0             'DC02
    sData(8).FillQty(3) = 0             'DC03
    sData(8).FillQty(4) = 0             'DC04
    sData(8).FillQty(5) = 0             'DC05
    sData(8).FillQty(6) = 0             'DC06
    sData(8).FillQty(7) = 0             'DC07
    sData(8).FillQty(8) = 0             'DC08
    sData(8).FillQty(9) = 0             'DC09
    
    sData(9).LcId = 10                  'Store
    sData(9).Priority = 1               'Priority
    sData(9).AvailQty = 0               'Available Quantity
    sData(9).MinQty = 0                 'Minimum Quantity
    sData(9).Need = 0                   'Need
    sData(9).SugAlloc = 0               'Suggested Allocation
    sData(9).Alloc = 0                  'Allocation
    sData(9).FillPath = "000000000"     'Fill Flags
    sData(9).FillQty(1) = 0             'DC01
    sData(9).FillQty(2) = 0             'DC02
    sData(9).FillQty(3) = 0             'DC03
    sData(9).FillQty(4) = 0             'DC04
    sData(9).FillQty(5) = 0             'DC05
    sData(9).FillQty(6) = 0             'DC06
    sData(9).FillQty(7) = 0             'DC07
    sData(9).FillQty(8) = 0             'DC08
    sData(9).FillQty(9) = 0             'DC09
    
    sData(10).LcId = "Total"            'Store
    sData(10).Priority = 0              'Priority
    sData(10).AvailQty = 0              'Available Quantity
    sData(10).MinQty = 0                'Minimum Quantity
    sData(10).Need = 0                  'Need
    sData(10).SugAlloc = 0              'Suggested Allocation
    sData(10).Alloc = 0                 'Allocation
    sData(10).FillKey = ""              'Fill Flags
    sData(10).FillQty(1) = 0            'DC01
    sData(10).FillQty(2) = 0            'DC02
    sData(10).FillQty(3) = 0            'DC03
    sData(10).FillQty(4) = 0            'DC04
    sData(10).FillQty(5) = 0            'DC05
    sData(10).FillQty(6) = 0            'DC06
    sData(10).FillQty(7) = 0            'DC07
    sData(10).FillQty(8) = 0            'DC08
    sData(10).FillQty(9) = 0            'DC09
    
    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True

    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Proportional To Need' Allocation Methods. Sourcing is from three distribution center with varying availability."

    'Set Number of DC's
    
    NbrDCs = 3

End Function


Function Scenario_07()
    Dim i As Integer
    
    ReDim dData(0 To 10)
    ReDim sData(0 To 900)
    
    dData(0).LcId = Format(1, "0")
    dData(0).AvailQty = 500
    dData(0).AllocQty = 0
    
    dData(1).LcId = Format(2, "0")
    dData(1).AvailQty = 500
    dData(1).AllocQty = 0
    
    For i = 2 To 10
    
        dData(i).LcId = Format(1 + i, "0")
        dData(i).AvailQty = 1000
        dData(i).AllocQty = 0
        
    Next i
    
    'Initialize Store Data
    
    For i = 0 To 99
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "120000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 100 To 199
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "234568891"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 200 To 299
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "300000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 300 To 399
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "400000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 400 To 499
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "500000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 500 To 599
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "600000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 600 To 699
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "700000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 700 To 799
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "800000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    For i = 800 To 899
    
        sData(i).LcId = Format(i + 1, "0")  'Store
        sData(i).Priority = 1               'Priority
        sData(i).AvailQty = 0               'Available Quantity
        sData(i).MinQty = 10                'Minimum Quantity
        sData(i).Need = 0                   'Need
        sData(i).SugAlloc = 0               'Suggested Allocation
        sData(i).Alloc = 0                  'Allocation
        sData(i).FillPath = "900000000"     'Fill Flags
        sData(i).FillQty(1) = 0             'DC01
        sData(i).FillQty(2) = 0             'DC02
        sData(i).FillQty(3) = 0             'DC03
        sData(i).FillQty(4) = 0             'DC04
        sData(i).FillQty(5) = 0             'DC05
        sData(i).FillQty(6) = 0             'DC06
        sData(i).FillQty(7) = 0             'DC07
        sData(i).FillQty(8) = 0             'DC08
        sData(i).FillQty(9) = 0             'DC09
    
    Next i
    
    sData(900).LcId = "Total"            'Store
    sData(900).Priority = 0              'Priority
    sData(900).AvailQty = 0              'Available Quantity
    sData(900).MinQty = 0                'Minimum Quantity
    sData(900).Need = 0                  'Need
    sData(900).SugAlloc = 0              'Suggested Allocation
    sData(900).Alloc = 0                 'Allocation
    sData(900).FillKey = ""              'Fill Flags
    sData(900).FillQty(1) = 0            'DC01
    sData(900).FillQty(2) = 0            'DC02
    sData(900).FillQty(3) = 0            'DC03
    sData(900).FillQty(4) = 0            'DC04
    sData(900).FillQty(5) = 0            'DC05
    sData(900).FillQty(6) = 0            'DC06
    sData(900).FillQty(7) = 0            'DC07
    sData(900).FillQty(8) = 0            'DC08
    sData(900).FillQty(9) = 0            'DC09
    
    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True
    
    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Requirements for 900 stores are filled from 9 distribution centers."

    'Set Number of DC's
    
    NbrDCs = 9

End Function


Function Scenario_08()
    Dim i As Integer
    
    ReDim dData(0 To 1)
    ReDim sData(0 To 3)
    
    'Initialize DC Data
    
    dData(0).LcId = "1"         'DC Id
    dData(0).AvailQty = 9       'Available Quantity
    dData(0).AllocQty = 0       'Allocated Quantity
    
    dData(1).LcId = "2"         'DC Id
    dData(1).AvailQty = 5       'Available Quantity
    dData(1).AllocQty = 0       'Allocated Quantity
       
    'Initialize Store Data
    
    sData(0).LcId = 1                   'Store
    sData(0).Priority = 1               'Priority
    sData(0).AvailQty = 0               'Available Quantity
    sData(0).MinQty = 20                'Minimum Quantity
    sData(0).Need = 0                   'Need
    sData(0).SugAlloc = 0               'Suggested Allocation
    sData(0).Alloc = 0                  'Allocation
    sData(0).FillPath = "100000000"     'Fill Flags
    sData(0).FillQty(1) = 0             'DC01
    sData(0).FillQty(2) = 0             'DC02
    sData(0).FillQty(3) = 0             'DC03
    sData(0).FillQty(4) = 0             'DC04
    sData(0).FillQty(5) = 0             'DC05
    sData(0).FillQty(6) = 0             'DC06
    sData(0).FillQty(7) = 0             'DC07
    sData(0).FillQty(8) = 0             'DC08
    sData(0).FillQty(9) = 0             'DC09
    
    sData(1).LcId = 2                   'Store
    sData(1).Priority = 1               'Priority
    sData(1).AvailQty = 0               'Available Quantity
    sData(1).MinQty = 25                'Minimum Quantity
    sData(1).Need = 0                   'Need
    sData(1).SugAlloc = 0               'Suggested Allocation
    sData(1).Alloc = 0                  'Allocation
    sData(1).FillPath = "100000000"     'Fill Flags
    sData(1).FillQty(1) = 0             'DC01
    sData(1).FillQty(2) = 0             'DC02
    sData(1).FillQty(3) = 0             'DC03
    sData(1).FillQty(4) = 0             'DC04
    sData(1).FillQty(5) = 0             'DC05
    sData(1).FillQty(6) = 0             'DC06
    sData(1).FillQty(7) = 0             'DC07
    sData(1).FillQty(8) = 0             'DC08
    sData(1).FillQty(9) = 0             'DC09
    
    sData(2).LcId = 3                   'Store
    sData(2).Priority = 2               'Priority
    sData(2).AvailQty = 0               'Available Quantity
    sData(2).MinQty = 30                'Minimum Quantity
    sData(2).Need = 0                   'Need
    sData(2).SugAlloc = 1               'Suggested Allocation
    sData(2).Alloc = 0                  'Allocation
    sData(2).FillPath = "120000000"     'Fill Flags
    sData(2).FillQty(1) = 0             'DC01
    sData(2).FillQty(2) = 0             'DC02
    sData(2).FillQty(3) = 0             'DC03
    sData(2).FillQty(4) = 0             'DC04
    sData(2).FillQty(5) = 0             'DC05
    sData(2).FillQty(6) = 0             'DC06
    sData(2).FillQty(7) = 0             'DC07
    sData(2).FillQty(8) = 0             'DC08
    sData(2).FillQty(9) = 0             'DC09
    
    sData(3).LcId = "Total"            'Store
    sData(3).Priority = 0              'Priority
    sData(3).AvailQty = 0              'Available Quantity
    sData(3).MinQty = 0                'Minimum Quantity
    sData(3).Need = 0                  'Need
    sData(3).SugAlloc = 0              'Suggested Allocation
    sData(3).Alloc = 0                 'Allocation
    sData(3).FillKey = ""              'Fill Flags
    sData(3).FillQty(1) = 0            'DC01
    sData(3).FillQty(2) = 0            'DC02
    sData(3).FillQty(3) = 0            'DC03
    sData(3).FillQty(4) = 0            'DC04
    sData(3).FillQty(5) = 0            'DC05
    sData(3).FillQty(6) = 0            'DC06
    sData(3).FillQty(7) = 0            'DC07
    sData(3).FillQty(8) = 0            'DC08
    sData(3).FillQty(9) = 0            'DC09
    
    'Set Allocation Rule
    
    Me.optAllocMethod(0).Value = True
    
    'Set Scenario Description
    
    Me.txtScenarioDesc.Text = "Needs are filled using the 'Proportional To Need' Allocation Method. Priorities vary by store. Sourcing is from a two distribution centers."
    
    'Set Number of DC's
    
    NbrDCs = 2
    
End Function

Function SetDefaultValues(Scenario As Integer)

    Select Case Scenario
    Case 1
        Scenario_01
    Case 2
        Scenario_02
    Case 3
        Scenario_03
    Case 4
        Scenario_04
    Case 5
        Scenario_05
    Case 6
        Scenario_06
    Case 7
        Scenario_07
    Case 8
        Scenario_08
    End Select
  
End Function

Private Sub cmdAllocate_Click()

    Dim i As Integer
    Dim j As Integer
    Dim oAlloc As clsAllocation
    Dim Store As String
    
    'Reload the data
    
    SetDefaultValues CInt(Me.dgScenario.Columns(0).Value)
    
    Set oAlloc = New clsAllocation
    
    'Set rounding option
    
    oAlloc.RoundOpt = Me.ckRound.Value
    
    'Set sourcing option
    
    oAlloc.SourceOpt = Me.ckSourceOpt.Value
    
    'Set Allocation Method
    
    If Me.optAllocMethod(0).Value = True Then
        oAlloc.AllocMethod = ProportionalToNeed
    Else
        oAlloc.AllocMethod = BalanceRelativeInv
    End If
    
    'Set properties for Allocate From Locations
    
    For i = LBound(dData) To NbrDCs - 1
        oAlloc.AddFillingLocation Format(dData(i).LcId, "0"), dData(i).AvailQty
    Next i
    
    'Set properties for Allocate To Locations
    
    For i = LBound(sData) To UBound(sData) - 1
        
        oAlloc.AddAllocLocation Format(sData(i).LcId, "0"), sData(i).Priority, sData(i).AvailQty, sData(i).MinQty, _
            sData(i).SugAlloc, Mid(sData(i).FillPath, 1, 1), Mid(sData(i).FillPath, 2, 1), Mid(sData(i).FillPath, 3, 1), _
            Mid(sData(i).FillPath, 4, 1), Mid(sData(i).FillPath, 5, 1), Mid(sData(i).FillPath, 6, 1), _
            Mid(sData(i).FillPath, 7, 1), Mid(sData(i).FillPath, 8, 1), Mid(sData(i).FillPath, 9, 1)
    
    Next i
    
    'Allocate
    
    oAlloc.Allocate
    
    'Update Store Array
    
    For i = LBound(sData) To UBound(sData) - 1
    
        Store = sData(i).LcId
        
        sData(i).Need = oAlloc.Need(Store)
        sData(i).SugAlloc = oAlloc.SugAlloc(Store)
        sData(i).Alloc = oAlloc.Alloc(Store)
        
        For j = 1 To 9
            sData(i).FillQty(j) = oAlloc.FillQty(Store, j)
        Next j
    
    Next i
    
    'Update Distribution Center Array
    
    For i = LBound(dData) To UBound(dData)
        dData(i).AllocQty = oAlloc.QtyAlloc(dData(i).LcId)
    Next i
    
    'Update Store Totals
    
    j = UBound(sData)
    
    sData(j).Need = 0
    sData(j).SugAlloc = 0
    sData(j).Alloc = 0
    sData(j).MinQty = 0
    
    For i = 1 To 9
        sData(j).FillQty(i) = 0
    Next i
    
    For i = LBound(sData) To UBound(sData) - 1
    
        sData(j).AvailQty = sData(j).AvailQty + sData(i).AvailQty
        sData(j).MinQty = sData(j).MinQty + sData(i).MinQty
        sData(j).Need = sData(j).Need + sData(i).Need
        sData(j).SugAlloc = sData(j).SugAlloc + sData(i).SugAlloc
        sData(j).Alloc = sData(j).Alloc + sData(i).Alloc
        sData(j).FillQty(1) = sData(j).FillQty(1) + sData(i).FillQty(1)
        sData(j).FillQty(2) = sData(j).FillQty(2) + sData(i).FillQty(2)
        sData(j).FillQty(3) = sData(j).FillQty(3) + sData(i).FillQty(3)
        sData(j).FillQty(4) = sData(j).FillQty(4) + sData(i).FillQty(4)
        sData(j).FillQty(5) = sData(j).FillQty(5) + sData(i).FillQty(5)
        sData(j).FillQty(6) = sData(j).FillQty(6) + sData(i).FillQty(6)
        sData(j).FillQty(7) = sData(j).FillQty(7) + sData(i).FillQty(7)
        sData(j).FillQty(8) = sData(j).FillQty(8) + sData(i).FillQty(8)
        sData(j).FillQty(9) = sData(j).FillQty(9) + sData(i).FillQty(9)
        
    Next i
    
    'Refresh the grid
    
    Me.dgDC.ReBind
    Me.dgStores.ReBind
    
    Me.dgStores.Rows = UBound(sData) + 1
    
End Sub

Private Sub cmdExit_Click()

    Unload Me
    
End Sub

Private Sub cmdTEst_Click()

    Dim clsAllocate As clsAllocation
    
    Set clsAllocate = New clsAllocation
    
    clsAllocate.AllocMethod = ProportionalToNeed
    clsAllocate.RoundOpt = True
    clsAllocate.SourceOpt = True
    
    clsAllocate.AddFillingLocation "00001", 1000
    clsAllocate.AddFillingLocation "00002", 10
    clsAllocate.AddFillingLocation "00003", 10
    
    clsAllocate.AddAllocLocation "00001", 1, 0, 20, 0, "00001", "0", "0", "0", "0", "0", "0", "0", ""
    clsAllocate.AddAllocLocation "00002", 1, 0, 30, 0, "00002", "00001", "0", "0", "0", "0", "0", "0", ""
    
    clsAllocate.Allocate
    
    'Seems ok here
    Debug.Print "---------------------------------"
    Debug.Print clsAllocate.FillQty("00001", 1)
    
    'Total here comes out to thirty greater than req's of 20 for this location
    Debug.Print "---------------------------------"
    Debug.Print clsAllocate.FillQty("00002", 1)
    Debug.Print clsAllocate.FillQty("00002", 2)
    
    'Total allocated comes out at 50 which is greater than total requirements
    Debug.Print "---------------------------------"
    Debug.Print clsAllocate.QtyAlloc("00001")
    Debug.Print clsAllocate.QtyAlloc("00002")
    
    
    Set clsAllocate = Nothing

End Sub

Private Sub dgDC_InitColumnProps()

    Me.dgDC.Columns(0).Name = "lcid"
    Me.dgDC.Columns(0).Caption = "DC"
    Me.dgDC.Columns(0).Locked = True
    Me.dgDC.Columns(0).Width = 800
    
    Me.dgDC.Columns(1).Name = "availqty"
    Me.dgDC.Columns(1).Caption = "Avail Qty"
    Me.dgDC.Columns(1).Locked = False
    Me.dgDC.Columns(1).Width = 1000
    Me.dgDC.Columns(1).DataType = vbLong
    Me.dgDC.Columns(1).NumberFormat = "#,##0"
    Me.dgDC.Columns(1).Alignment = ssCaptionAlignmentRight
    
    Me.dgDC.Columns(2).Name = "allocqty"
    Me.dgDC.Columns(2).Caption = "Alloc Qty"
    Me.dgDC.Columns(2).Locked = True
    Me.dgDC.Columns(2).Width = 1000
    Me.dgDC.Columns(2).DataType = vbLong
    Me.dgDC.Columns(2).NumberFormat = "#,##0"
    Me.dgDC.Columns(2).Alignment = ssCaptionAlignmentRight
    
    Me.dgDC.Columns(3).Name = "balance"
    Me.dgDC.Columns(3).Caption = "Balance"
    Me.dgDC.Columns(3).Locked = True
    Me.dgDC.Columns(3).Width = 1000
    Me.dgDC.Columns(3).DataType = vbLong
    Me.dgDC.Columns(3).NumberFormat = "#,##0"
    Me.dgDC.Columns(3).Alignment = ssCaptionAlignmentRight
    
End Sub


Private Sub dgDC_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

End Sub


Private Sub dgDC_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)

    Dim iRBRow As Integer
    Dim iRow As Integer
    Dim ipoint As Integer
    
    iRow = 0

    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            ipoint = UBound(dData, 2)
        Else
            ipoint = 0
        End If
    Else
        ipoint = StartLocation
        If ReadPriorRows Then
            ipoint = ipoint - 1
        Else
            ipoint = ipoint + 1
        End If
    End If

    For iRBRow = 0 To RowBuf.RowCount - 1
    
        If ipoint < 0 Or ipoint > UBound(dData, 1) Then Exit For

        RowBuf.Value(iRBRow, 0) = dData(ipoint).LcId        'Store
        RowBuf.Value(iRBRow, 1) = dData(ipoint).AvailQty    'Avail Qty
        RowBuf.Value(iRBRow, 2) = dData(ipoint).AllocQty    'Allocated Qty
        RowBuf.Value(iRBRow, 3) = dData(ipoint).AvailQty - dData(ipoint).AllocQty
 
        RowBuf.Bookmark(iRBRow) = ipoint
        If ReadPriorRows Then
            ipoint = ipoint - 1
        Else
            ipoint = ipoint + 1
        End If
        
        iRow = iRow + 1
        
    Next iRBRow

    RowBuf.RowCount = iRow

End Sub


Private Sub dgScenario_InitColumnProps()

    Me.dgScenario.Columns(0).Name = "id"
    Me.dgScenario.Columns(0).Caption = "Id"
    Me.dgScenario.Columns(0).Width = 400
 
    Me.dgScenario.Columns(1).Name = "desc"
    Me.dgScenario.Columns(1).Caption = "Description"
    Me.dgScenario.Columns(1).Width = 2350
    
    Me.dgScenario.AddItem "1" + vbTab + "Proportional Example"
    Me.dgScenario.AddItem "2" + vbTab + "Relative Inv Level Example"
    Me.dgScenario.AddItem "3" + vbTab + "Store Ranking Example"
    Me.dgScenario.AddItem "4" + vbTab + "Simple Sourcing Example"
    Me.dgScenario.AddItem "5" + vbTab + "DC1 Shortage, DC2 Adequate Supply"
    Me.dgScenario.AddItem "6" + vbTab + "Jeremie's Example"
    Me.dgScenario.AddItem "7" + vbTab + "Richard's Example"
    Me.dgScenario.AddItem "8" + vbTab + "David's Example"
    
End Sub

Private Sub dgScenario_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)

    SetDefaultValues CInt(Me.dgScenario.Columns(0).Value)
    
    'Rebind the grids
    
    Me.dgDC.ReBind
    Me.dgStores.ReBind
    Me.dgStores.Rows = UBound(sData) + 1

End Sub


Private Sub dgStores_InitColumnProps()

    Dim i As Integer

    Me.dgStores.Columns(0).Name = "lcid"
    Me.dgStores.Columns(0).Caption = "Store"
    Me.dgStores.Columns(0).Locked = True
    Me.dgStores.Columns(0).Width = 800

    Me.dgStores.Columns(1).Name = "priority"
    Me.dgStores.Columns(1).Caption = "Priority"
    Me.dgStores.Columns(1).Locked = False
    Me.dgStores.Columns(1).Width = 800
    Me.dgStores.Columns(1).Alignment = ssCaptionAlignmentRight

    Me.dgStores.Columns(2).Name = "availqty"
    Me.dgStores.Columns(2).Caption = "Avail Qty"
    Me.dgStores.Columns(2).Locked = False
    Me.dgStores.Columns(2).Width = 800
    Me.dgStores.Columns(2).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(2).NumberFormat = "#,##0"

    Me.dgStores.Columns(3).Name = "minqty"
    Me.dgStores.Columns(3).Caption = "Min Qty"
    Me.dgStores.Columns(3).Locked = False
    Me.dgStores.Columns(3).Width = 800
    Me.dgStores.Columns(3).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(3).NumberFormat = "#,##0"

    Me.dgStores.Columns(4).Name = "need"
    Me.dgStores.Columns(4).Caption = "Need"
    Me.dgStores.Columns(4).Locked = True
    Me.dgStores.Columns(4).Width = 800
    Me.dgStores.Columns(4).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(4).NumberFormat = "#,##0"

    Me.dgStores.Columns(5).Name = "trialalloc"
    Me.dgStores.Columns(5).Caption = "Trial Alloc"
    Me.dgStores.Columns(5).Locked = True
    Me.dgStores.Columns(5).Width = 800
    Me.dgStores.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(5).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(6).Name = "alloc"
    Me.dgStores.Columns(6).Caption = "Alloc"
    Me.dgStores.Columns(6).Locked = True
    Me.dgStores.Columns(6).Width = 800
    Me.dgStores.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(6).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(7).Name = "fillflags"
    Me.dgStores.Columns(7).Caption = "Fill Path"
    Me.dgStores.Columns(7).Locked = True
    Me.dgStores.Columns(7).Width = 1100

    Me.dgStores.Columns(8).Name = "dc01"
    Me.dgStores.Columns(8).Caption = "DC01"
    Me.dgStores.Columns(8).Locked = True
    Me.dgStores.Columns(8).Width = 800
    Me.dgStores.Columns(8).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(8).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(9).Name = "dc02"
    Me.dgStores.Columns(9).Caption = "DC02"
    Me.dgStores.Columns(9).Locked = True
    Me.dgStores.Columns(9).Width = 800
    Me.dgStores.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(9).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(10).Name = "dc03"
    Me.dgStores.Columns(10).Caption = "DC03"
    Me.dgStores.Columns(10).Locked = True
    Me.dgStores.Columns(10).Width = 800
    Me.dgStores.Columns(10).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(10).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(11).Name = "dc04"
    Me.dgStores.Columns(11).Caption = "DC04"
    Me.dgStores.Columns(11).Locked = True
    Me.dgStores.Columns(11).Width = 800
    Me.dgStores.Columns(11).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(11).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(12).Name = "dc05"
    Me.dgStores.Columns(12).Caption = "DC05"
    Me.dgStores.Columns(12).Locked = True
    Me.dgStores.Columns(12).Width = 800
    Me.dgStores.Columns(12).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(12).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(13).Name = "dc06"
    Me.dgStores.Columns(13).Caption = "DC06"
    Me.dgStores.Columns(13).Locked = True
    Me.dgStores.Columns(13).Width = 800
    Me.dgStores.Columns(13).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(13).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(14).Name = "dc07"
    Me.dgStores.Columns(14).Caption = "DC07"
    Me.dgStores.Columns(14).Locked = True
    Me.dgStores.Columns(14).Width = 800
    Me.dgStores.Columns(14).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(14).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(15).Name = "dc08"
    Me.dgStores.Columns(15).Caption = "DC08"
    Me.dgStores.Columns(15).Locked = True
    Me.dgStores.Columns(15).Width = 800
    Me.dgStores.Columns(15).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(15).NumberFormat = "#,##0.0"

    Me.dgStores.Columns(16).Name = "dc09"
    Me.dgStores.Columns(16).Caption = "DC09"
    Me.dgStores.Columns(16).Locked = True
    Me.dgStores.Columns(16).Width = 800
    Me.dgStores.Columns(16).Alignment = ssCaptionAlignmentRight
    Me.dgStores.Columns(16).NumberFormat = "#,##0.0"

    Me.dgStores.SplitterPos = 1

End Sub

Private Sub dgStores_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    End Select

End Sub


Private Sub dgStores_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

End Sub


Private Sub dgStores_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)

    Dim iRBRow As Integer
    Dim iRow As Integer
    Dim ipoint As Integer
    
    iRow = 0

    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            ipoint = UBound(sData, 2)
        Else
            ipoint = 0
        End If
    Else
        ipoint = StartLocation
        If ReadPriorRows Then
            ipoint = ipoint - 1
        Else
            ipoint = ipoint + 1
        End If
    End If

    For iRBRow = 0 To RowBuf.RowCount - 1
    
        If ipoint < 0 Or ipoint > UBound(sData, 1) Then Exit For

        RowBuf.Value(iRBRow, 0) = sData(ipoint).LcId
        RowBuf.Value(iRBRow, 1) = sData(ipoint).Priority
        RowBuf.Value(iRBRow, 2) = sData(ipoint).AvailQty
        RowBuf.Value(iRBRow, 3) = sData(ipoint).MinQty
        RowBuf.Value(iRBRow, 4) = sData(ipoint).Need
        RowBuf.Value(iRBRow, 5) = sData(ipoint).SugAlloc
        RowBuf.Value(iRBRow, 6) = sData(ipoint).Alloc
        RowBuf.Value(iRBRow, 7) = sData(ipoint).FillPath
        RowBuf.Value(iRBRow, 8) = sData(ipoint).FillQty(1)
        RowBuf.Value(iRBRow, 9) = sData(ipoint).FillQty(2)
        RowBuf.Value(iRBRow, 10) = sData(ipoint).FillQty(3)
        RowBuf.Value(iRBRow, 11) = sData(ipoint).FillQty(4)
        RowBuf.Value(iRBRow, 12) = sData(ipoint).FillQty(5)
        RowBuf.Value(iRBRow, 13) = sData(ipoint).FillQty(6)
        RowBuf.Value(iRBRow, 14) = sData(ipoint).FillQty(7)
        RowBuf.Value(iRBRow, 15) = sData(ipoint).FillQty(8)
        RowBuf.Value(iRBRow, 16) = sData(ipoint).FillQty(9)
 
        RowBuf.Bookmark(iRBRow) = ipoint
        If ReadPriorRows Then
            ipoint = ipoint - 1
        Else
            ipoint = ipoint + 1
        End If
        
        iRow = iRow + 1
        
    Next iRBRow

    RowBuf.RowCount = iRow

End Sub


Private Sub dgStores_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)

    Dim p As Integer
    
    p = CInt(WriteLocation)
    
    If Not IsNull(RowBuf.Value(0, 0)) Then
        sData(p).LcId = CStr(RowBuf.Value(0, 0))
    End If

    If Not IsNull(RowBuf.Value(0, 1)) Then
        sData(p).Priority = RowBuf.Value(0, 1)
    End If
    
    If Not IsNull(RowBuf.Value(0, 2)) Then
        sData(p).AvailQty = RowBuf.Value(0, 2)
    End If
    
    If Not IsNull(RowBuf.Value(0, 3)) Then
        sData(p).MinQty = RowBuf.Value(0, 3)
    End If
    
End Sub

Private Sub Form_Load()

    'Initialize the Store Data
    
    SetDefaultValues 1
    
End Sub


Private Sub mnuEditOpt_Click(Index As Integer)

    Dim i As Integer
    Dim s1 As String
    Dim s2 As String

    Select Case Index
    Case 0          'Copy
    
        'Clear the clipboard
        
        Clipboard.Clear
        
        'Build the headings
        
        s1 = "Location" + vbTab
        s1 = s1 + "Priority" + vbTab
        s1 = s1 + "AvailQty" + vbTab
        s1 = s1 + "MinQty" + vbTab
        s1 = s1 + "Need" + vbTab
        s1 = s1 + "SugAlloc" + vbTab
        s1 = s1 + "Alloc" + vbTab
        s1 = s1 + "AllocFlag" + vbTab
        s1 = s1 + "FillPath" + vbTab
        s1 = s1 + "FillFlag" + vbTab
        s1 = s1 + "TotalFillQty" + vbTab
        s1 = s1 + "FillQty_01" + vbTab
        s1 = s1 + "FillQty_02" + vbTab
        s1 = s1 + "FillQty_03" + vbTab
        s1 = s1 + "FillQty_04" + vbTab
        s1 = s1 + "FillQty_05" + vbTab
        s1 = s1 + "FillQty_06" + vbTab
        s1 = s1 + "FillQty_07" + vbTab
        s1 = s1 + "FillQty_08" + vbTab
        s1 = s1 + "FillQty_09" + vbCrLf
        
        'Write the Data
        
        s2 = ""
        
        For i = LBound(sData) To UBound(sData)
        
            s2 = s2 + sData(i).LcId + vbTab
            s2 = s2 + CStr(sData(i).Priority) + vbTab
            s2 = s2 + CStr(sData(i).AvailQty) + vbTab
            s2 = s2 + CStr(sData(i).MinQty) + vbTab
            s2 = s2 + CStr(sData(i).Need) + vbTab
            s2 = s2 + Format(sData(i).SugAlloc, "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).Alloc, "#,##0.00") + vbTab
            s2 = s2 + CStr(sData(i).AllocFlag) + vbTab
            s2 = s2 + sData(i).FillPath + vbTab
            s2 = s2 + CStr(sData(i).FillFlag) + vbTab
            s2 = s2 + Format(sData(i).TotalFillQty, "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(1), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(2), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(3), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(4), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(5), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(6), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(7), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(8), "#,##0.00") + vbTab
            s2 = s2 + Format(sData(i).FillQty(9), "#,##0.00") + vbCrLf
        
        Next i

        Clipboard.SetText s1 + s2, vbCFText
        
        'Clean Up
        
        s1 = ""
        s2 = ""
    
    End Select
    
End Sub


