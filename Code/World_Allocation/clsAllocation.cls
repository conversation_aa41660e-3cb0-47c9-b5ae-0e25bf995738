VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsAllocation"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Option Explicit

Public AllocMethod As AIM_AllocMethod
Public RoundOpt As Boolean
Public SourceOpt As Boolean

Public Enum AIM_AllocMethod

    ProportionalToNeed = 0
    BalanceRelativeInv = 1
    
End Enum

Private Type StoreRcd

    LcId As String
    Priority As Integer
    AvailQty As Double
    MinQty As Double
    Need As Double
    SugAlloc As Double
    Alloc As Double
    AllocFlag As Boolean
    FillFlag As Boolean
    FillLcId(1 To 9) As String
    TotalFillQty As Double
    FillAlloc() As Double
    FillQty() As Double
    FillKey As String
    
End Type

Private Type DCRcd

    LcId As String
    AvailQty As Double          'Available Inventory
    FillQty As Double           'Quantity Filled
    SourceQty As Double         'Sourcing Quantity
    AvailToAlloc As Boolean     'Available To Allocate

End Type

Private Type FillDetailRcd

    DC As String
    Store As String
    FillAlloc As Double
    FillQty As Double

End Type

Private m_AllocQty As Double        'Total Quantity Allocated
Private m_FirstTime As Boolean
Private m_NbrPriorities As Integer
Private m_NbrStores As Integer
Private m_NbrDCs As Integer

Private m_Stores() As StoreRcd
Private m_PriorityList() As Variant

Private m_DC() As DCRcd

Public Function Allocate()

    Dim i As Integer
    Dim j As Integer
    Dim StartLcId As Integer
    Dim EndLcId As Integer
    
    Dim CurPriority As Integer
    
    'Sort the records by Priority
    
    QuickSortPriority m_Stores, -1, (m_NbrStores - 1)
    
    'Initialize the Priority List
    
    ReDim m_PriorityList(10, 2)

    CurPriority = m_Stores(0).Priority
    m_PriorityList(0, 0) = CurPriority
    m_PriorityList(0, 1) = LBound(m_Stores, 1)
    m_PriorityList(0, 2) = m_NbrStores - 1
    m_NbrPriorities = 1
    
    'Determine the number of priority groups
    
    For j = LBound(m_Stores) + 1 To (m_NbrStores - 1)
    
        If m_Stores(j).Priority <> CurPriority Then
        
            'Update the last priority row
            
            m_PriorityList(m_NbrPriorities - 1, 2) = j - 1
            
            'Increment the priority counter
            
            m_NbrPriorities = m_NbrPriorities + 1
            
            If m_NbrPriorities > UBound(m_PriorityList) Then
                ReDim Preserve m_PriorityList(UBound(m_PriorityList) + 10)
            End If
            
            CurPriority = m_Stores(j).Priority
            m_PriorityList(m_NbrPriorities - 1, 0) = CurPriority
            m_PriorityList(m_NbrPriorities - 1, 1) = j
            m_PriorityList(m_NbrPriorities - 1, 2) = m_NbrStores - 1
             
        End If
    
    Next j
    
    'Allocate each priority group
    
    For j = LBound(m_PriorityList) To m_NbrPriorities - 1
    
        'Get the Starting Location and Ending Location
        
        StartLcId = CInt(m_PriorityList(j, 1))
        EndLcId = CInt(m_PriorityList(j, 2))
    
        'Calcualte the Need for each Allocate From Location
        
        For i = StartLcId To EndLcId
        
            m_Stores(i).Need = IIf(m_Stores(i).MinQty - m_Stores(i).AvailQty > 0, m_Stores(i).MinQty - m_Stores(i).AvailQty, 0)
            
            'If a postive suggested allocation is provided by the user, override the calculated need
            
            If m_Stores(i).SugAlloc > 0 Then
                m_Stores(i).Need = m_Stores(i).SugAlloc
            End If
            
        Next i
        
        'Call the appropriate Allocation Method
        
        Select Case AllocMethod
        Case AIM_AllocMethod.ProportionalToNeed
            AllocateNeed StartLcId, EndLcId
        Case AIM_AllocMethod.BalanceRelativeInv
            AllocateRelativeInv StartLcId, EndLcId
        End Select

        'Source the Allocated Quanties
        
        If SourceOpt Then
            SourceFillingLcId StartLcId, EndLcId
        End If
    
        'Initialize Allocation Quantites
        
        For i = StartLcId To EndLcId
            m_Stores(i).Alloc = m_Stores(i).SugAlloc
        Next i
        
    Next j
    
    'Recalculate Fill Quantities
    
    For i = LBound(m_DC) To m_NbrDCs
        m_DC(i).FillQty = m_DC(i).SourceQty
    Next i
    
    'Round the results
    
    If RoundOpt Then
        RoundAlloc LBound(m_Stores), EndLcId
    End If
    
End Function


Public Property Get FillQty(ByVal Store As String, ByVal FillFrom As Integer) As Double
    
    Dim StorePtr As Integer
    
    StorePtr = GetStorePtr(Store)
    
    'Check for an invalid dc
    
    If FillFrom < 1 Or FillFrom > m_NbrDCs Then
        FillQty = 0
        Exit Sub
    End If

    If StorePtr <> 9999 Then
        FillQty = m_Stores(StorePtr).FillQty(FillFrom)
    Else
        FillQty = 0
    End If

End Property

Private Function GetFillingLcIdPtr(FillingLcId As String)

' Given a store's filling location id, this functions
' returns a pointer to the filling location array
    
    Dim i As Integer
    
    For i = LBound(m_DC) To UBound(m_DC)
    
        If m_DC(i).LcId = FillingLcId Then
            GetFillingLcIdPtr = i
            Exit Function
        End If
    
    Next i
    
    GetFillingLcIdPtr = 9999

End Function




Private Function GetStorePtr(LcId As String)

    Dim i As Integer
    
    For i = LBound(m_Stores) To UBound(m_Stores)
    
        If m_Stores(i).LcId = LcId Then
            GetStorePtr = i
            Exit Function
        End If
    
    Next i
    
    GetStorePtr = 9999
    
End Function
Public Function QtyAlloc(FillingLcId As String)

    Dim p As Integer
    
    p = GetFillingLcIdPtr(FillingLcId)
    
    If p < 9999 Then
        QtyAlloc = m_DC(p).FillQty
    Else
        QtyAlloc = 0
    End If

End Function


Private Function QuickSortFillKey(varArray() As StoreRcd, _
                    Optional lngFirst As Long = -1, _
                    Optional lngLast As Long = -1) As Variant
                    
    ' QuickSort algorithm used to sort the items
    ' in the varArray array.
    
    Dim lngLow      As Long
    Dim lngHigh     As Long
    Dim lngMiddle   As Long
    Dim varTempVal  As StoreRcd
    Dim varTestVal  As String
    
    If lngFirst = -1 Then lngFirst = LBound(varArray)
    If lngLast = -1 Then lngLast = UBound(varArray)
        
    If lngFirst < lngLast Then
        
        lngMiddle = (lngFirst + lngLast) / 2
        varTestVal = varArray(lngMiddle).FillKey
        lngLow = lngFirst
        lngHigh = lngLast
        
        Do
            
            Do While varArray(lngLow).FillKey < varTestVal
                lngLow = lngLow + 1
            Loop
            Do While varArray(lngHigh).FillKey > varTestVal
                lngHigh = lngHigh - 1
            Loop
            If (lngLow <= lngHigh) Then
                varTempVal = varArray(lngLow)
                varArray(lngLow) = varArray(lngHigh)
                varArray(lngHigh) = varTempVal
                lngLow = lngLow + 1
                lngHigh = lngHigh - 1
            End If
        
        Loop While (lngLow <= lngHigh)
        
        If lngFirst < lngHigh Then QuickSortFillKey varArray, lngFirst, lngHigh
        If lngLow < lngLast Then QuickSortFillKey varArray, lngLow, lngLast
    
    End If
                    
End Function

Private Function QuickSortPriority(varArray() As StoreRcd, _
                        Optional lngFirst As Long = -1, _
                        Optional lngLast As Long = -1) As Variant
                            
    ' QuickSort algorithm used to sort the items
    ' in the varArray array.
    
    Dim lngLow      As Long
    Dim lngHigh     As Long
    Dim lngMiddle   As Long
    Dim varTempVal  As StoreRcd
    Dim varTestVal  As Integer
    
    If lngFirst = -1 Then lngFirst = LBound(varArray)
    If lngLast = -1 Then lngLast = UBound(varArray)
        
    If lngFirst < lngLast Then
        
        lngMiddle = (lngFirst + lngLast) / 2
        varTestVal = varArray(lngMiddle).Priority
        lngLow = lngFirst
        lngHigh = lngLast
        
        Do
            
            Do While varArray(lngLow).Priority < varTestVal
                lngLow = lngLow + 1
            Loop
            Do While varArray(lngHigh).Priority > varTestVal
                lngHigh = lngHigh - 1
            Loop
            If (lngLow <= lngHigh) Then
                varTempVal = varArray(lngLow)
                varArray(lngLow) = varArray(lngHigh)
                varArray(lngHigh) = varTempVal
                lngLow = lngLow + 1
                lngHigh = lngHigh - 1
            End If
        
        Loop While (lngLow <= lngHigh)
        
        If lngFirst < lngHigh Then QuickSortPriority varArray, lngFirst, lngHigh
        If lngLow < lngLast Then QuickSortPriority varArray, lngLow, lngLast
    
    End If
    
End Function

Private Function AllocateNeed(StartLcId As Integer, EndLcId As Integer)

    Dim AllocRatio As Double
    Dim LcIdPtr As Integer
    Dim i As Integer
    Dim j As Integer
    Dim TotalAvailQty As Double
    Dim TotalRqmts As Double
    Dim Filling_Dc 's
    
    'Determine which DC's can allocate to this group
    
    For i = LBound(m_DC) To UBound(m_DC)
        m_DC(i).AvailToAlloc = False
    Next i
    
    For i = StartLcId To EndLcId
    
        For j = 1 To 9
        
            LcIdPtr = GetFillingLcIdPtr(m_Stores(i).FillLcId(j))
            If LcIdPtr <> 9999 Then
                m_DC(LcIdPtr).AvailToAlloc = True
            End If
        
        Next j
    
    Next i
    
    'Calculate the Total Quantity Available to Allocate
    
    For i = LBound(m_DC) To m_NbrDCs
        If m_DC(i).AvailToAlloc Then
            TotalAvailQty = TotalAvailQty + m_DC(i).AvailQty
        End If
    Next i
    
    TotalAvailQty = TotalAvailQty - m_AllocQty
    
    'Calcualte the Total Requirements
    
    For i = StartLcId To EndLcId
        TotalRqmts = TotalRqmts + m_Stores(i).Need
    Next i
    
    'If there are no requirements -- work is done.
    
    If TotalRqmts <= 0 Then
        Exit Function
    End If
    
    'Perform the initial allocation
    
    If TotalAvailQty < TotalRqmts Then
        AllocRatio = TotalAvailQty / TotalRqmts
    Else
        AllocRatio = 1
    End If
    
    'AllocRatio = IIf((TotalAvailQty < TotalRqmts), (TotalAvailQty / TotalRqmts), 1)
    
    For i = StartLcId To EndLcId
        m_Stores(i).SugAlloc = m_Stores(i).Need * AllocRatio
        m_AllocQty = m_AllocQty + m_Stores(i).SugAlloc
    Next i
    
End Function
Public Property Get Alloc(ByVal Store As String) As Double

    Dim StorePtr As Integer
    
    StorePtr = GetStorePtr(Store)

    If StorePtr <> 9999 Then
        Alloc = m_Stores(StorePtr).Alloc
    Else
        Alloc = 0
    End If
    
End Property

Public Function AddAllocLocation(LcId As String, _
    Priority As Integer, AvailQty As Double, _
    MinQty As Double, SugAlloc As Double, _
    FillLcId01 As String, FillLcId02 As String, _
    FillLcId03 As String, FillLcId04 As String, _
    FillLcId05 As String, FillLcId06 As String, _
    FillLcId07 As String, FillLcId08 As String, _
    FillLcId09 As String) As Boolean
    
    Dim i As Integer
    
    'Check for the number of DC's. If no DC's have been set up abort processing
    
    If m_NbrDCs <= 0 Then
        AddAllocLocation = False
        Exit Function
    End If
    
    'Increment the Number of locations to allocate to
    
    m_NbrStores = m_NbrStores + 1
    
    'Check for overflow in the Store Array
    
    If m_NbrStores > UBound(m_Stores) Then
        ReDim Preserve m_Stores(UBound(m_Stores) + 10)
    End If
    
    'Redim the FillQty and FillAlloc arrays to match the number of filling locations
    
    ReDim Preserve m_Stores(m_NbrStores - 1).FillAlloc(1 To m_NbrDCs) As Double
    ReDim Preserve m_Stores(m_NbrStores - 1).FillQty(1 To m_NbrDCs) As Double
        
    m_Stores(m_NbrStores - 1).LcId = LcId
    m_Stores(m_NbrStores - 1).Priority = Priority
    m_Stores(m_NbrStores - 1).AvailQty = AvailQty
    m_Stores(m_NbrStores - 1).MinQty = MinQty
    m_Stores(m_NbrStores - 1).SugAlloc = SugAlloc
    m_Stores(m_NbrStores - 1).Alloc = 0
    m_Stores(m_NbrStores - 1).AllocFlag = True
    m_Stores(m_NbrStores - 1).FillFlag = False
    m_Stores(m_NbrStores - 1).FillLcId(1) = FillLcId01
    m_Stores(m_NbrStores - 1).FillLcId(2) = FillLcId02
    m_Stores(m_NbrStores - 1).FillLcId(3) = FillLcId03
    m_Stores(m_NbrStores - 1).FillLcId(4) = FillLcId04
    m_Stores(m_NbrStores - 1).FillLcId(5) = FillLcId05
    m_Stores(m_NbrStores - 1).FillLcId(6) = FillLcId06
    m_Stores(m_NbrStores - 1).FillLcId(7) = FillLcId07
    m_Stores(m_NbrStores - 1).FillLcId(8) = FillLcId08
    m_Stores(m_NbrStores - 1).FillLcId(9) = FillLcId09
    
    For i = LBound(m_Stores(m_NbrStores - 1).FillQty) To UBound(m_Stores(m_NbrStores - 1).FillQty)
        m_Stores(m_NbrStores - 1).FillQty(i) = 0
        m_Stores(m_NbrStores - 1).FillAlloc(i) = 0
    Next i
    
    'Build the Fill Key
    
    m_Stores(m_NbrStores - 1).FillKey = FillLcId01 + "." + FillLcId02 + "." + FillLcId03 + "." _
        + FillLcId04 + "." + FillLcId05 + "." + FillLcId06 + "." _
        + FillLcId07 + "." + FillLcId08 + "." + FillLcId09 + "." + LcId
        
    AddAllocLocation = True
    
End Function
    
Public Function AddFillingLocation(LcId As String, AvailQty As Double)

    'Increment the Number of Filling Locations
    
    m_NbrDCs = m_NbrDCs + 1
    
    'Check for overflow in the DC Array
    
    If m_NbrDCs > UBound(m_DC) Then
        ReDim Preserve m_DC(1 To UBound(m_DC) + 10)
    End If
    
    m_DC(m_NbrDCs).LcId = LcId
    m_DC(m_NbrDCs).AvailQty = AvailQty
    m_DC(m_NbrDCs).FillQty = 0
    m_DC(m_NbrDCs).SourceQty = 0
        
End Function


Private Function AllocateRelativeInv(StartLcId As Integer, EndLcId As Integer)

    Dim AllocExcpt As Integer
    Dim AllocRatio As Double
    Dim AvailToAlloc As Long
    Dim i As Integer
    Dim j As Integer
    Dim LcIdPtr As Integer
    Dim TotalOh As Long
    Dim TotalRqmts As Double
    
    'Initialize the Allocation Ration
    
    AllocRatio = 1
    AllocExcpt = 0
    
    'Determine which DC's can allocate to this group
    
    For i = LBound(m_DC) To UBound(m_DC)
        m_DC(i).AvailToAlloc = False
    Next i
    
    For i = StartLcId To EndLcId
    
        For j = 1 To 9
        
            LcIdPtr = GetFillingLcIdPtr(m_Stores(i).FillLcId(j))
            If LcIdPtr <> 9999 Then
                m_DC(LcIdPtr).AvailToAlloc = True
            End If
        
        Next j
    
    Next i
    
    'Calculate the Total Quantity Available to Allocate
    
    For i = LBound(m_DC) To UBound(m_DC)
        If m_DC(i).AvailToAlloc Then
            AvailToAlloc = AvailToAlloc + (m_DC(i).AvailQty - m_AllocQty)
        End If
    Next i
        
    'Calcualte the Total Requirements and Total On-Hand
    
    For i = StartLcId To EndLcId
        TotalOh = TotalOh + m_Stores(i).AvailQty
        TotalRqmts = TotalRqmts + m_Stores(i).Need
        m_Stores(i).AllocFlag = True
    Next i
        
    'Check for an exception -- No Inventory Available
    
    If TotalOh + AvailToAlloc <= 0 Then
        Exit Function
    End If
    
    'Calculate the Initial Allocation Ratio
    
    AllocRatio = IIf((TotalOh + AvailToAlloc) / (TotalOh + TotalRqmts) > 1, 1, (TotalOh + AvailToAlloc) / (TotalOh + TotalRqmts))
        
    Do
        
        'Initialize Loop Variables
        
        TotalRqmts = 0
        TotalOh = 0
        AllocExcpt = 0
        
        'Should this location be included in the allocation
        
        For i = StartLcId To EndLcId
        
            If m_Stores(i).MinQty > 0 Then
            
                If m_Stores(i).AvailQty / m_Stores(i).MinQty <= AllocRatio Then
                    m_Stores(i).AllocFlag = True
                    TotalRqmts = TotalRqmts + m_Stores(i).Need
                    TotalOh = TotalOh + m_Stores(i).AvailQty
                Else
                    
                    If m_Stores(i).AllocFlag Then
                        m_Stores(i).AllocFlag = False
                        AllocExcpt = AllocExcpt + 1
                    End If
                    
                End If
            
            End If
        
        Next i
    
        'Revise the Allocation Ratio
        
        AllocRatio = IIf((TotalOh + AvailToAlloc) / (TotalOh + TotalRqmts) > 1, 1, (TotalOh + AvailToAlloc) / (TotalOh + TotalRqmts))
        
    Loop While AllocExcpt > 0
    
    For i = StartLcId To EndLcId
        If m_Stores(i).AllocFlag Then
            m_Stores(i).SugAlloc = (m_Stores(i).MinQty * AllocRatio) - m_Stores(i).AvailQty
            m_AllocQty = m_AllocQty + m_Stores(i).SugAlloc
        End If
    Next i

End Function

Public Property Get Need(ByVal Store As String) As Double

    Dim StorePtr As Integer
    
    StorePtr = GetStorePtr(Store)

    If StorePtr <> 9999 Then
        Need = m_Stores(StorePtr).Need
    Else
        Need = 0
    End If
    
End Property

Function Reset()

    Dim i As Integer

    'Initialize Locations and Available Quantities
    
    For i = LBound(m_Stores) To UBound(m_Stores)
        m_Stores(i).Alloc = 0
        m_Stores(i).AllocFlag = True
    Next i

End Function

Private Function RoundAlloc(StartLcId As Integer, EndLcId As Integer)

    Dim Balance As Double
    Dim CurDc As Integer
    Dim FirstLcId As Integer
    Dim LcId As Integer
    Dim NbrLcIds As Integer
    Dim OriginalAlloc As Double
    
    'Generate a random number
    
    Randomize
    FirstLcId = Rnd() * (EndLcId - StartLcId) + 1
    
    If SourceOpt Then
    
        'Round each Distribution Center Allocation
        
        For CurDc = LBound(m_DC) To m_NbrDCs
        
            Balance = 0
            
            'Round from Random Starting Point Forward
            
            For LcId = FirstLcId To EndLcId
            
                OriginalAlloc = m_Stores(LcId).FillQty(CurDc)
                m_Stores(LcId).FillQty(CurDc) = Round(OriginalAlloc + Balance, 0)
                Balance = Balance + (OriginalAlloc - m_Stores(LcId).FillQty(CurDc))
                
                NbrLcIds = NbrLcIds + 1
            
            Next LcId
        
            'Round from First Starting Point to [Random Starting Point - 1]
            
            For LcId = StartLcId To (FirstLcId - 1)
                
                OriginalAlloc = m_Stores(LcId).FillQty(CurDc)
                m_Stores(LcId).FillQty(CurDc) = Round(OriginalAlloc + Balance, 0)
                Balance = Balance + (OriginalAlloc - m_Stores(LcId).FillQty(CurDc))
                
                NbrLcIds = NbrLcIds + 1
                
            Next LcId
    
        Next CurDc
    
    Else
    
        'Round from Random Starting Point Forward
        
        For LcId = FirstLcId To EndLcId
        
            OriginalAlloc = m_Stores(LcId).Alloc
            m_Stores(LcId).Alloc = Round(m_Stores(LcId).Alloc + Balance, 0)
            Balance = Balance + (OriginalAlloc - m_Stores(LcId).Alloc)
            
            NbrLcIds = NbrLcIds + 1
        
        Next LcId
        
        'Round from First Starting Point to [Random Starting Point - 1]
        
        For LcId = StartLcId To (FirstLcId - 1)
            
            OriginalAlloc = m_Stores(LcId).Alloc
            m_Stores(LcId).Alloc = Round(m_Stores(LcId).Alloc + Balance, 0)
            Balance = Balance + (OriginalAlloc - m_Stores(LcId).Alloc)
            
            NbrLcIds = NbrLcIds + 1
            
        Next LcId
    
    End If
    
    RoundAlloc = NbrLcIds

End Function

Private Function SourceFillingLcId(StartLcId As Integer, EndLcId As Integer)

    Dim AccumFill As Double
    Dim Balance As Double
    Dim FillPtr As Integer
    Dim i As Integer
    Dim InvPos As Double
    Dim LPtr As Integer
    Dim NbrFillLcids As Integer
    Dim NextLPtr As Integer
    Dim ReAlloc As Double
    Dim Reqmts As Double
    Dim Store As Integer

    'Sort Allocation Records by Filling Path

    QuickSortFillKey m_Stores, CLng(StartLcId), CLng(EndLcId)
    
    'Source in Filling Path Sequence
    
    For Store = StartLcId To EndLcId
    
        'Determine the number of filling locations
        
        For i = LBound(m_Stores(Store).FillLcId()) To UBound(m_Stores(Store).FillLcId())
        
            If Trim(m_Stores(Store).FillLcId(i)) = "0" Then
                NbrFillLcids = i - 1
                Exit For
            End If
        
        Next i
        
        'Source Allocation
        
        For i = LBound(m_Stores(Store).FillLcId()) To NbrFillLcids
        
            'Get a pointer to the filling location
            
            LPtr = GetFillingLcIdPtr(m_Stores(Store).FillLcId(i))
            
            'Check for an invalid pointer
            
            If LPtr <> 9999 And (m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty) > 0 Then
                
                Select Case Round(m_DC(LPtr).AvailQty - m_DC(LPtr).FillQty, 1)
                    Case Is <= 0                                                        'No Inventory Available to Fill Orders
                        
                        'Update Fill Allocation Value
                        
                        NextLPtr = GetFillingLcIdPtr(m_Stores(Store).FillLcId(i + 1))
                        
                        If NextLPtr <> 9999 Then
                           
                            'Another filling source exists in the sourcing path
                            
                            m_Stores(Store).FillAlloc(LPtr) = 0
                            m_Stores(Store).FillAlloc(NextLPtr) = (m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty)
                        
                        Else
                            
                            'Default to the primary filling source
                            
                            LPtr = GetFillingLcIdPtr(m_Stores(Store).FillLcId(1))
                            m_Stores(Store).FillAlloc(LPtr) = m_Stores(Store).FillAlloc(LPtr) + (m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty)
                        
                        End If
                    
                    Case Is < Round((m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty), 1)      'Partial Fill
                    
                        Reqmts = (m_DC(LPtr).AvailQty - m_DC(LPtr).FillQty)
                        m_Stores(Store).FillQty(LPtr) = Reqmts
                        m_Stores(Store).TotalFillQty = m_Stores(Store).TotalFillQty + Reqmts
                        m_DC(LPtr).FillQty = m_DC(LPtr).AvailQty
                        
                        'Update Fill Allocation Value
                        
                        m_Stores(Store).FillAlloc(LPtr) = Reqmts
                        NextLPtr = GetFillingLcIdPtr(m_Stores(Store).FillLcId(i + 1))
                        
                        If NextLPtr <> 9999 Then
                         
                            'Another filling source exists in the sourcing path
                            
                            m_Stores(Store).FillAlloc(NextLPtr) = Reqmts
                            
                        Else
                        
                            'Default to the primary filling source
                            
                            LPtr = GetFillingLcIdPtr(m_Stores(Store).FillLcId(1))
                            m_Stores(Store).FillAlloc(LPtr) = m_Stores(Store).FillAlloc(LPtr) + Reqmts
                            
                        End If
                        
                    Case Is >= Round((m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty), 1)     'Complete Fill
                    
                        m_Stores(Store).FillQty(LPtr) = (m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty)
                        m_DC(LPtr).FillQty = m_DC(LPtr).FillQty + (m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty)
                        m_Stores(Store).TotalFillQty = m_Stores(Store).TotalFillQty + (m_Stores(Store).SugAlloc - m_Stores(Store).TotalFillQty)
                
                        'Update Fill Allocation Value
                        
                        m_Stores(Store).FillAlloc(LPtr) = m_Stores(Store).FillQty(LPtr)
                
                End Select
                    
            End If
            
        Next i
    
    Next Store
    
    'Reallocate the Distribution Center to ensure each allocation is fair
    
    For LPtr = 1 To m_NbrDCs

        'Sum the raw allocations for the filling location

        ReAlloc = 0
        For Store = StartLcId To EndLcId
            ReAlloc = ReAlloc + m_Stores(Store).FillAlloc(LPtr)
        Next Store

        'Allocate on the basis of raw allocation

        If ReAlloc > 0 Then

            'Initilaize the DC Fill Quantity and DC Balance

            Balance = m_DC(LPtr).AvailQty - m_DC(LPtr).SourceQty

            For Store = StartLcId To EndLcId
                
                'Calculate the Accumulated Fill for the Store from DC other
                'than the current DC
                
                AccumFill = 0

                For i = 1 To m_NbrDCs
                    If i <> LPtr Then
                        AccumFill = AccumFill + m_Stores(Store).FillQty(i)
                    End If
                Next i
                
                'Reallocate DC Inventory on the basis of the Fill Allocation
            
                m_Stores(Store).FillQty(LPtr) = Balance * (m_Stores(Store).FillAlloc(LPtr) / ReAlloc)
                
                'Check to see if additional inventory can be allocated
                
                If m_Stores(Store).FillQty(LPtr) > m_Stores(Store).Need - AccumFill Then
                    m_Stores(Store).FillQty(LPtr) = m_Stores(Store).Need - AccumFill
                End If
                
                'Update the DC's Sourced Quantity
                
                m_DC(LPtr).SourceQty = m_DC(LPtr).SourceQty + m_Stores(Store).FillQty(LPtr)
            
            Next Store

        End If

    Next LPtr
    
'    For i = LBound(m_Stores) To UBound(m_Stores) - 1
'        Debug.Print i, m_Stores(i).LcId, m_Stores(i).Need, m_Stores(i).FillAlloc(1), m_Stores(i).FillAlloc(2)
'    Next i

End Function

Public Property Get SugAlloc(ByVal Store As String) As Double

    Dim StorePtr As Integer
    
    StorePtr = GetStorePtr(Store)
    
    If StorePtr <> 9999 Then
        SugAlloc = m_Stores(StorePtr).SugAlloc
    Else
        SugAlloc = 0
    End If
    
End Property

Private Sub Class_Initialize()

    Dim i As Integer
    
    'Initialize Rounding / Source Options Option
    
    RoundOpt = False
    SourceOpt = False
    AllocMethod = ProportionalToNeed
    m_AllocQty = 0
    
    'Initialize Needs Array
    
    ReDim m_Stores(10)
    
    'Initialize number of Stores and DC's
    
    m_NbrStores = 0
    m_NbrDCs = 0
    
    'Initialize Locations and Available Quantities
    
    ReDim m_DC(1 To 10)
    
End Sub
