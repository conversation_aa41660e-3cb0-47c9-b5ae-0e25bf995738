Type=OleDll
Reference=*\G{00020430-0000-0000-C000-000000000046}#2.0#0#..\..\..\..\..\WINNT\System32\stdole2.tlb#OLE Automation
Reference=*\G{420B2830-E718-11CF-893D-00A0C9054228}#1.0#0#..\..\..\..\..\WINNT\System32\scrrun.dll#Microsoft Scripting Runtime
Class=clsAllocSubset; clsAllocSubset.cls
Class=clsFillLocation; clsFillLocation.cls
Class=clsAllocResults; clsAllocResults.cls
Class=clsAllocResult; clsAllocResult.cls
Module=modAllocObjects; modAllocObjects.bas
Class=clsVisAllocation; clsVisAllocation.cls
Class=clsAllocLocation; clsAllocLocation.cls
Module=modAllocFunctions; modAllocFunctions.bas
Class=clsPriority; clsPriority.cls
Startup="(None)"
HelpFile=""
Title="VisAllocation"
ExeName32="VisAllocation.dll"
Command32=""
Name="VisAllocation"
HelpContextID="0"
CompatibleMode="2"
CompatibleEXE32="VisAllocation.dll"
VersionCompatible32="1"
MajorVer=1
MinorVer=0
RevisionVer=0
AutoIncrementVer=0
ServerSupportFiles=0
VersionCompanyName="EXE Technologies"
CompilationType=0
OptimizationType=0
FavorPentiumPro(tm)=0
CodeViewDebugInfo=0
NoAliasing=0
BoundsCheck=0
OverflowCheck=0
FlPointCheck=0
FDIVCheck=0
UnroundedFP=0
StartMode=1
Unattended=0
Retained=0
ThreadPerObject=0
MaxNumberOfThreads=1
ThreadingModel=1
DebugStartupOption=0

[MS Transaction Server]
AutoRefresh=1
