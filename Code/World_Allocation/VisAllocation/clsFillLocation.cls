VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsFillLocation"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsFillLocation
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   <One-line description of module purpose>
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsFillLocation."

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************

'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************
Private m_lngNumberAllocLocation                            As Long

'*****************************************************************************
'ARRAYS
'*****************************************************************************
Private m_arrAllocLocationName()                           As String
Private m_arrAllocLocationQuantity()                        As Double

'*****************************************************************************
'PROPERTIES
'*****************************************************************************
Private m_strLocationName                                   As String
Private m_dblAvailableQuantity                              As Double
Private m_dblRemainingQuantity                              As Double

'Property->: AllocLocationName----------------------------------
Public Property Get AllocLocationName(Index As Long) As String
Const PROPERTY_NAME = "pg_AllocLocationQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
AllocLocationName = m_arrAllocLocationName(Index)
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property->: AllocLocationQuantity----------------------------------
Public Property Get AllocLocationQuantity(Index As Long) As Double
Const PROPERTY_NAME = "pg_AllocLocationQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
AllocLocationQuantity = m_arrAllocLocationQuantity(Index)
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: NumberAllocLocation----------------------------------
Public Property Get NumberAllocLocation() As Long
Const PROPERTY_NAME = "pg_NumberAllocLocation."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
NumberAllocLocation = m_lngNumberAllocLocation
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property<-: AllocLocationQuantity----------------------------------
Public Property Let AllocLocationQuantity(Index As Long, ByVal AllocLocationQuantity As Double)
Const PROPERTY_NAME = "pl_AllocLocationQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_arrAllocLocationQuantity(Index) = AllocLocationQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property->: AllocatedQuantity----------------------------------
Public Property Get RemainingQuantity() As Double
Const PROPERTY_NAME = "pg_RemainingQuantity  ."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
RemainingQuantity = m_dblRemainingQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: AllocatedQuantity----------------------------------
Public Property Let RemainingQuantity(ByVal RemainingQuantity As Double)
Const PROPERTY_NAME = "pl_RemainingQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblRemainingQuantity = RemainingQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: LocationName----------------------------------
Public Property Get LocationName() As String
Const PROPERTY_NAME = "pg_LocationName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
LocationName = m_strLocationName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: LocationName----------------------------------
Public Property Let LocationName(ByVal LocationName As String)
Const PROPERTY_NAME = "pl_LocationName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_strLocationName = LocationName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: AvailableQuantity----------------------------------
Public Property Get AvailableQuantity() As Double
Const PROPERTY_NAME = "pg_AvailableQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
AvailableQuantity = m_dblAvailableQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: AvailableQuantity----------------------------------
Public Property Let AvailableQuantity(ByVal AvailableQuantity As Double)
Const PROPERTY_NAME = "pl_AvailableQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblAvailableQuantity = AvailableQuantity
m_dblRemainingQuantity = AvailableQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
'*****************************************************************************
'Sub(): AddAllocLocation----------------------------------
Public Sub AddAllocLocation(LocationName As String)
Const SUB_NAME = "s_AddAllocLocation."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

If g_LU.Exists(LU_FILL_LOCATION & m_strLocationName & LU_SUF_ALLC_LOCATION & LocationName) = False Then

    'Variables
    ReDim Preserve m_arrAllocLocationName(m_lngNumberAllocLocation) As String
    m_arrAllocLocationName(m_lngNumberAllocLocation) = LocationName
    
    ReDim m_arrAllocLocationQuantity(m_lngNumberAllocLocation) As Double
    
    'Add Reference to lookup object
    g_LU.Add LU_FILL_LOCATION & m_strLocationName & LU_SUF_ALLC_LOCATION & LocationName, m_lngNumberAllocLocation
    
    'Increment Counter
    m_lngNumberAllocLocation = m_lngNumberAllocLocation + 1

Else

    Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data [" & LU_FILL_LOCATION & m_strLocationName & LU_SUF_ALLC_LOCATION & LocationName & "] already exists"

End If

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************

'Sub(): Class_Initialize----------------------------------
Private Sub Class_Initialize()
Const SUB_NAME = "s_Class_Initialize."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
m_lngNumberAllocLocation = 0

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub
