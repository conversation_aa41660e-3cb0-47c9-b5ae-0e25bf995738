Attribute VB_Name = "modAllocFunctions"
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   modAllocFunctions
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   Contains declarations for common functions formerly held in the following class modules:
'       clsSort -- Sort function for priority indices
'       clsMathHelper   -- Contains arithmetic functions with data type 'smoothers'
'
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'Local -- CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const MOD_NAME = "modAllocFunctions."

'*****************************************************************************
'clsMathHelper -- CONSTANTS
'*****************************************************************************
Const FLOAT_PRECISION As Double = 0.0001
Const FLOAT_ROUND As Integer = 4

'*****************************************************************************
'clsMathHelper -- ENUMERATIONS
'*****************************************************************************
Public Enum MA_Round_Direction
    Round_Up = 0
    Round_Down = 1
End Enum

'*****************************************************************************
'clsSort -- FUNCTIONS & SUB PROCEDURES
'g_QuickSortPriority
'*****************************************************************************
Public Sub g_QuickSortPriority(lngFirstIndex As Long, lngLastIndex As Long)
    Const SUB_NAME = "s_QuickSortPriority."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If

    'Objects

    Dim lngMiddleIndex                  As Long
    Dim lngTestPriority                 As Long
    Dim lngLowIndex                     As Long
    Dim lngHighIndex                    As Long
    Dim objPriority                     As clsPriority
    
    If lngFirstIndex = -1 Then lngFirstIndex = 0
    If lngLastIndex = -1 Then lngLastIndex = UBound(g_PRI, 1)
    
    If lngFirstIndex < lngLastIndex Then
        lngMiddleIndex = ((lngFirstIndex + lngLastIndex) / 2)
        lngTestPriority = g_PRI(lngMiddleIndex).Priority
        lngLowIndex = lngFirstIndex
        lngHighIndex = lngLastIndex
        
        Do
            Do While g_PRI(lngLowIndex).Priority < lngTestPriority
                lngLowIndex = lngLowIndex + 1
            Loop
            
            Do While g_PRI(lngHighIndex).Priority > lngTestPriority
                lngHighIndex = lngHighIndex - 1
            Loop
            
            If lngLowIndex <= lngHighIndex Then
                'Shuffle the Hash Table Look up object
                g_LU.Item(LU_PRIORITY & CStr(g_PRI(lngLowIndex).Priority)) = lngHighIndex
                g_LU.Item(LU_PRIORITY & CStr(g_PRI(lngHighIndex).Priority)) = lngLowIndex

                'Shuffle the priority object
                Set objPriority = g_PRI(lngLowIndex)
                Set g_PRI(lngLowIndex) = g_PRI(lngHighIndex)
                Set g_PRI(lngHighIndex) = objPriority
            
                lngLowIndex = lngLowIndex + 1
                lngHighIndex = lngHighIndex - 1
            End If
        Loop While (lngLowIndex <= lngHighIndex)
            
        If lngFirstIndex < lngHighIndex Then g_QuickSortPriority lngFirstIndex, lngHighIndex
        If lngLowIndex < lngLastIndex Then g_QuickSortPriority lngLowIndex, lngLastIndex
    End If

    Set objPriority = Nothing
    
Exit Sub
'Error Handler
s_trap:
    Set objPriority = Nothing
    Err.Raise Err.Number, MOD_NAME & SUB_NAME & Err.Source, Err.Description
End Sub
'*****************************************************************************
'END -- clsSort -- FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'*****************************************************************************
'clsMathHelper -- FUNCTIONS & SUB PROCEDURES
'g_NotEquals()
'g_Equals()
'g_LessThanEqualTo()
'g_GreaterThanEqualTo()
'g_LessThan()
'g_GreaterThan()
'g_NeverLessThanZero()
'g_Remainder()
'g_IsWholeNumber()
'*****************************************************************************
Public Function g_NotEquals(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_NotEquals."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        g_NotEquals = False
    Else
        g_NotEquals = True
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_Equals(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_Equals."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        g_Equals = True
    Else
        g_Equals = False
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_LessThanEqualTo(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_LessThanEqualTo."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If dblNumber1 < dblNumber2 Then
        g_LessThanEqualTo = True
    ElseIf Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        g_LessThanEqualTo = True
    Else
        g_LessThanEqualTo = False
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_GreaterThanEqualTo(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_GreaterThanEqualTo."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Variables
    If dblNumber1 > dblNumber2 Then
        g_GreaterThanEqualTo = True
    ElseIf Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        g_GreaterThanEqualTo = True
    Else
        g_GreaterThanEqualTo = False
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_LessThan(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_LessThan."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        g_LessThan = False
    ElseIf dblNumber1 < dblNumber2 Then
        g_LessThan = True
    Else
        g_LessThan = False
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_GreaterThan(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_GreaterThan."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        g_GreaterThan = False
    ElseIf dblNumber1 > dblNumber2 Then
        g_GreaterThan = True
    Else
        g_GreaterThan = False
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_NeverLessThanZero(ByRef dblValue As Double, ByRef blnUseNeverLessThanZero) As Double
    Const FUNCTION_NAME = "f_NeverLessThanZero."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If dblValue < 0 And blnUseNeverLessThanZero = True Then
        g_NeverLessThanZero = 0
    Else
        g_NeverLessThanZero = dblValue
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_Remainder(ByVal dblNumber As Double, ByVal Round_Direction As MA_Round_Direction) As Double
    Const FUNCTION_NAME = "f_Remainder."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Clean any precision problems off the number
    dblNumber = Round(dblNumber, FLOAT_ROUND)
    
    If CDbl(CLng(dblNumber)) <> dblNumber Then
    
        If Round_Direction = Round_Up Then
            g_Remainder = IIf(CDbl(CLng(dblNumber)) > dblNumber, CDbl(CLng(dblNumber)) - dblNumber, 1 - (dblNumber - CDbl(CLng(dblNumber))))
        Else
            g_Remainder = IIf(CDbl(CLng(dblNumber)) > dblNumber, 1 - (CDbl(CLng(dblNumber)) - dblNumber), dblNumber - CDbl(CLng(dblNumber)))
        End If
    Else
        g_Remainder = 0
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

Public Function g_IsWholeNumber(ByVal dblNumber As Double) As Boolean
    Const FUNCTION_NAME = "f_IsWholeNumber."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(CDbl(CLng(dblNumber)) - dblNumber) < FLOAT_PRECISION Then
        g_IsWholeNumber = True
    Else
        g_IsWholeNumber = False
    End If

Exit Function
'Error Handler
f_trap:
    Err.Raise Err.Number, MOD_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'*****************************************************************************
'END -- clsMathHelper -- FUNCTIONS & SUB PROCEDURES
'*****************************************************************************
