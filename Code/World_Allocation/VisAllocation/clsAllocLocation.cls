VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsAllocLocation"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsAllocLocation
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   <One-line description of module purpose>
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsAllocLocation."

'*****************************************************************************
'ARRAYS
'*****************************************************************************
Private m_arrAllocSubset()                                  As clsAllocSubset
Private m_arrFillFromLocationNames()                        As String
Private m_arrFillFromLocationQuantity()                     As Double

'*****************************************************************************
'VARIABLES
'*****************************************************************************
Private m_lngNumberAllocSubset                              As Long
Private m_lngNumberFillFromLocation                         As Long

Private m_strLocationName                                   As String
Private m_dblAvailableQuantity                              As Double
Private m_dblNeedRatio                                      As Double
Private m_dblAllocatedQuantity                              As Double
Private m_lngPriority                                       As Long
Private m_dblTargetQuantity                                 As Double
Private m_blnFullyAllocatedFlag                             As Boolean

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
Private Sub Class_Initialize()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Class_Initialize() -- Initialize objects and dependencies
' .......................................................................
'
'   Initialize all local objects and dependencies. Initialize specific global objects.
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_Class_Initialize."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    m_lngNumberAllocSubset = 0
    m_lngNumberFillFromLocation = 0

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Private Sub Class_Terminate()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Class_Terminate() -- Destroy objects and dependencies
' .......................................................................
'
'   Destroy all local objects and dependencies. Destroy specific global objects.
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_Class_Terminate."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim lngCount                As Long
    For lngCount = 0 To (m_lngNumberAllocSubset - 1)
        Set m_arrAllocSubset(lngCount) = Nothing
    Next

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'*****************************************************************************
'PROPERTIES
' AllocatedQuantity -- Get/Let
' AvailableQuantity -- Get
' FillFromLocationName -- Get
' FillFromLocationQuantity -- Get/Let
' FullyAllocatedFlag -- Get/Let
' LocationName -- Get/Let
' NeedRatio -- Get/Let
' NumberFillFromLocation -- Get
' Priority -- Get/Let
' TargetQuantity -- Get
' TotalQuantity -- Get
'*****************************************************************************
Public Property Get AllocatedQuantity() As Double
    Const PROPERTY_NAME = "pg_AllocatedQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    AllocatedQuantity = m_dblAllocatedQuantity

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let AllocatedQuantity(ByVal AllocatedQuantity As Double)
    Const PROPERTY_NAME = "pl_AllocatedQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_dblAllocatedQuantity = AllocatedQuantity

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property 'Property->: AvailableQuantity----------------------------------

Public Property Get AvailableQuantity() As Double
    Const PROPERTY_NAME = "pg_AvailableQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    AvailableQuantity = m_dblAvailableQuantity

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get FillFromLocationName(Index As Long) As String
    Const PROPERTY_NAME = "pg_FillFromLocationName."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    FillFromLocationName = m_arrFillFromLocationNames(Index)

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get FillFromLocationQuantity(Index As Long) As Double
    Const PROPERTY_NAME = "pg_FillFromLocationQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    FillFromLocationQuantity = m_arrFillFromLocationQuantity(Index)

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let FillFromLocationQuantity(Index As Long, ByVal FillFromLocationQuantity As Double)
    Const PROPERTY_NAME = "pl_FillFromLocationQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_arrFillFromLocationQuantity(Index) = FillFromLocationQuantity

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get FullyAllocatedFlag() As Boolean
    Const PROPERTY_NAME = "pg_FullyAllocatedFlag."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    FullyAllocatedFlag = m_blnFullyAllocatedFlag

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let FullyAllocatedFlag(ByVal FullyAllocatedFlag As Boolean)
    Const PROPERTY_NAME = "pl_FullyAllocatedFlag."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_blnFullyAllocatedFlag = FullyAllocatedFlag

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get LocationName() As String
    Const PROPERTY_NAME = "pg_LocationName."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    LocationName = m_strLocationName

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let LocationName(ByVal LocationName As String)
    Const PROPERTY_NAME = "pl_LocationName."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_strLocationName = LocationName

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get NeedRatio() As Double
    Const PROPERTY_NAME = "pg_NeedRatio."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    NeedRatio = m_dblNeedRatio

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let NeedRatio(ByVal NeedRatio As Double)
    Const PROPERTY_NAME = "pl_NeedRatio."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_dblNeedRatio = NeedRatio

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get NumberFillFromLocation() As Long
    Const PROPERTY_NAME = "pg_NumberFillFromLocation."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    NumberFillFromLocation = m_lngNumberFillFromLocation

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get Priority() As Long
    Const PROPERTY_NAME = "pg_Priority."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    Priority = m_lngPriority

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let Priority(ByVal Priority As Long)
    Const PROPERTY_NAME = "pl_Priority."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_lngPriority = Priority

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get TargetQuantity() As Long
    Const PROPERTY_NAME = "pg_TargetQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    TargetQuantity = m_dblTargetQuantity

Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get TotalQuantity() As Double
    Const PROPERTY_NAME = "pg_TotalQuantity."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    TotalQuantity = m_dblAvailableQuantity + m_dblAllocatedQuantity

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
' AddAllocSubset
' AddFillFromLocation
' AllocateSubset
' CalcNeedRatio
' LinkFillFromLocations
' QuickSortAllocSubSet
' SetQuantities
'*****************************************************************************
Public Sub AllocateSubSet()
    Const SUB_NAME = "s_AllocateSubSet."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim lngAllocSubSetCount                         As Long
    Dim lngFillLocationCount                        As Long
    Dim dblRemainingQuantity                        As Double
    Dim dblAllocQuantity                            As Double
    Dim dblAllocQuantitySubset                      As Double
    Dim arrFillFromLocationQuantity()               As Double
    
    dblRemainingQuantity = m_dblAllocatedQuantity
    arrFillFromLocationQuantity = m_arrFillFromLocationQuantity
    
    If g_GreaterThan(dblRemainingQuantity, 0) = True Then
        Do Until g_Equals(dblRemainingQuantity, 0) = True
            For lngAllocSubSetCount = 0 To (m_lngNumberAllocSubset - 1)
                For lngFillLocationCount = 0 To (m_lngNumberFillFromLocation - 1)
                    dblAllocQuantity = arrFillFromLocationQuantity(lngFillLocationCount)
                    If g_GreaterThanEqualTo(dblAllocQuantity, (m_arrAllocSubset(lngAllocSubSetCount).TargetQuantity - m_arrAllocSubset(lngAllocSubSetCount).AvailableQuantity)) = True Then
                        'Get the allocated amount
                        dblAllocQuantitySubset = (m_arrAllocSubset(lngAllocSubSetCount).TargetQuantity - m_arrAllocSubset(lngAllocSubSetCount).AvailableQuantity)
                        
                        'Add the result to the global result set
                        g_RES.AddAllocResult m_arrFillFromLocationNames(lngFillLocationCount), m_strLocationName, m_arrAllocSubset(lngAllocSubSetCount).SubSetName, dblAllocQuantitySubset
                        
                        'Subtract the remaining quantity
                        dblRemainingQuantity = dblRemainingQuantity - dblAllocQuantitySubset
                
                    Else
                        'Get the allocated amount
                        dblAllocQuantitySubset = dblAllocQuantity
                        
                        'Add the result to the global result set
                        g_RES.AddAllocResult m_arrFillFromLocationNames(lngFillLocationCount), m_strLocationName, m_arrAllocSubset(lngAllocSubSetCount).SubSetName, dblAllocQuantitySubset
                        
                        'Subtract the remaining quantity
                        dblRemainingQuantity = dblRemainingQuantity - dblAllocQuantitySubset
                    End If
                    
                    'Subtract available amount from the array
                    arrFillFromLocationQuantity(lngFillLocationCount) = dblAllocQuantity - dblAllocQuantitySubset
                Next lngFillLocationCount
            Next lngAllocSubSetCount
        Loop
    
    Else
        For lngAllocSubSetCount = 0 To (m_lngNumberAllocSubset - 1)
            For lngFillLocationCount = 0 To (m_lngNumberFillFromLocation - 1)
                g_RES.AddAllocResult m_arrFillFromLocationNames(lngFillLocationCount), m_strLocationName, m_arrAllocSubset(lngAllocSubSetCount).SubSetName, 0
            Next lngFillLocationCount
        Next lngAllocSubSetCount
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub SetQuantities(Target_Quantity As XP_TargetQuantity)
    Const SUB_NAME = "s_SetQuantities."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim lngCount            As Long
    
    'Init Target Quantity
    m_dblTargetQuantity = 0
    
    'Init Available Quantity
    m_dblAvailableQuantity = 0
    
    For lngCount = 0 To (m_lngNumberAllocSubset - 1)
        m_arrAllocSubset(lngCount).SetQuantity Target_Quantity
        m_dblTargetQuantity = m_dblTargetQuantity + m_arrAllocSubset(lngCount).TargetQuantity
        m_dblAvailableQuantity = m_dblAvailableQuantity + m_arrAllocSubset(lngCount).AvailableQuantity
    Next

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub CalcNeedRatio()
    Const SUB_NAME = "s_CalcNeedRatio."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Calc Need ratio
    If g_LessThan((m_dblAvailableQuantity + m_dblAllocatedQuantity), m_dblTargetQuantity) Then
        m_dblNeedRatio = ((m_dblAvailableQuantity + m_dblAllocatedQuantity) / m_dblTargetQuantity)
    Else
        m_dblNeedRatio = 1
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub LinkFillFromLocations()
    Const SUB_NAME = "s_LinkFillFromLocations."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim lngCount                As Long
    
    For lngCount = 0 To (m_lngNumberFillFromLocation - 1)
        If g_LU.Exists(LU_FILL_LOCATION & m_arrFillFromLocationNames(lngCount)) = True Then
            g_FIL(CLng(g_LU.Item(LU_FILL_LOCATION & m_arrFillFromLocationNames(lngCount)))).AddAllocLocation m_strLocationName
        Else
            Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data [" & LU_FILL_LOCATION & m_arrFillFromLocationNames(lngCount) & "] does not exist"
        End If
    Next

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub AddFillFromLocation(Optional LocationName As Variant)
    Const SUB_NAME = "s_AddFillFromLocation."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    If Not IsMissing(LocationName) Then
        If LocationName <> "" Then
            If g_LU.Exists(LU_ALLC_LOCATION & m_strLocationName & LU_SUF_FILL_LOCATION & LocationName) = False And LocationName <> m_strLocationName Then
                'Variables
                ReDim Preserve m_arrFillFromLocationNames(m_lngNumberFillFromLocation) As String
                m_arrFillFromLocationNames(m_lngNumberFillFromLocation) = LocationName
                
                'Increment Fill From Quantity Array
                ReDim m_arrFillFromLocationQuantity(m_lngNumberFillFromLocation) As Double
                
                'Add Reference to global look up map
                g_LU.Add LU_ALLC_LOCATION & m_strLocationName & LU_SUF_FILL_LOCATION & LocationName, m_lngNumberFillFromLocation
                
                m_lngNumberFillFromLocation = m_lngNumberFillFromLocation + 1
            ElseIf LocationName = m_strLocationName Then
                Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data [" & m_strLocationName & "] cannot be sourced by [" & LocationName & "]"
            Else
                Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data [" & LU_ALLC_LOCATION & m_strLocationName & LU_SUF_FILL_LOCATION & LocationName & "] already exists"
            End If
        End If
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub AddAllocSubset(SubSetName As String, AvailableQuantity As Double, TargetQuantity1 As Double, TargetQuantity2 As Double, TargetQuantity3 As Double)
    Const SUB_NAME = "s_AddAllocSubset."
    #If DEBUG_MODE = False Then
    On Error GoTo s_trap:
    #End If
    
    'Objects
    If g_LU.Exists(LU_ALLC_LOCATION & m_strLocationName & LU_SUF_ALLC_SUBSET & SubSetName) = False Then
        'Variables
        ReDim Preserve m_arrAllocSubset(m_lngNumberAllocSubset) As clsAllocSubset
        Set m_arrAllocSubset(m_lngNumberAllocSubset) = New clsAllocSubset
        
        'Add Reference
        g_LU.Add LU_ALLC_LOCATION & m_strLocationName & LU_SUF_ALLC_SUBSET & SubSetName, m_lngNumberAllocSubset
        
        'Set properties
        m_arrAllocSubset(m_lngNumberAllocSubset).SubSetName = SubSetName
        m_arrAllocSubset(m_lngNumberAllocSubset).AvailableQuantity = AvailableQuantity
        m_arrAllocSubset(m_lngNumberAllocSubset).AltTargetQuantity1 = TargetQuantity1
        m_arrAllocSubset(m_lngNumberAllocSubset).AltTargetQuantity2 = TargetQuantity2
        m_arrAllocSubset(m_lngNumberAllocSubset).AltTargetQuantity3 = TargetQuantity3
        
        m_lngNumberAllocSubset = m_lngNumberAllocSubset + 1
    
    Else
        Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data [" & LU_ALLC_LOCATION & m_strLocationName & LU_SUF_ALLC_SUBSET & SubSetName & "] already exists"
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub QuickSortAllocSubSet(Optional lngFirstIndex As Long = -1, Optional lngLastIndex As Long = -1)
    Const SUB_NAME = "s_QuickSortAllocSubSet."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If

    'Objects

    Dim lngMiddleIndex                  As Long
    Dim strTestName                     As String
    Dim lngLowIndex                     As Long
    Dim lngHighIndex                    As Long
    Dim objAllocSubset                  As clsAllocSubset
        
    If lngFirstIndex = -1 Then lngFirstIndex = 0
    If lngLastIndex = -1 Then lngLastIndex = UBound(m_arrAllocSubset, 1)
    
    If lngFirstIndex < lngLastIndex Then
        lngMiddleIndex = ((lngFirstIndex + lngLastIndex) / 2)
        strTestName = m_arrAllocSubset(lngMiddleIndex).SubSetName
        lngLowIndex = lngFirstIndex
        lngHighIndex = lngLastIndex
        
        Do
            Do While m_arrAllocSubset(lngLowIndex).SubSetName < strTestName
                lngLowIndex = lngLowIndex + 1
            Loop
            
            Do While m_arrAllocSubset(lngHighIndex).SubSetName > strTestName
                lngHighIndex = lngHighIndex - 1
            Loop
            
            If lngLowIndex <= lngHighIndex Then
                Set objAllocSubset = m_arrAllocSubset(lngLowIndex)
                Set m_arrAllocSubset(lngLowIndex) = m_arrAllocSubset(lngHighIndex)
                Set m_arrAllocSubset(lngHighIndex) = objAllocSubset
            
                lngLowIndex = lngLowIndex + 1
                lngHighIndex = lngHighIndex - 1
            End If
            
        Loop While (lngLowIndex <= lngHighIndex)
            
        If lngFirstIndex < lngHighIndex Then QuickSortAllocSubSet lngFirstIndex, lngHighIndex
        If lngLowIndex < lngLastIndex Then QuickSortAllocSubSet lngLowIndex, lngLastIndex
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub
