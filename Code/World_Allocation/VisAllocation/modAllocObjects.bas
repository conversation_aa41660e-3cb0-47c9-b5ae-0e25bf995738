Attribute VB_Name = "modAllocObjects"
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   modAllocObjects
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   Contains declarations for global constants, variables and objects
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const MOD_NAME = "modAllocObjects."

'*****************************************************************************
'MAIN OBJECT ARRAY POINTERS
'*****************************************************************************
Public Const LU_ALLC_LOCATION = "10DBDB05-2DCA-4700-A61D-415F22E107B8"
Public Const LU_FILL_LOCATION = "DE30C559-7016-4C24-9C3A-310490BE3552"
Public Const LU_PRIORITY = "389154CB-F7FA-4710-B180-DC8FE56C42D6"

'*****************************************************************************
'SUB ARRAY POINTERS
'*****************************************************************************
Public Const LU_SUF_ALLC_LOCATION = "1706C748-B316-4262-9F48-3C4B94335631"
Public Const LU_SUF_FILL_LOCATION = "6B1E9104-A9EF-4F50-9CD0-72F8DD33D36A"
Public Const LU_SUF_PRIORITY = "477ED326-4B37-4454-99EC-DEB4D5B37277"
Public Const LU_SUF_ALLC_SUBSET = "DBA275A7-4679-4896-AC48-BB4B101257A1"

'*****************************************************************************
'ERROR NUMBER
'*****************************************************************************
Public Const ALLOC_ERR_VALIDATION   As Long = -2147221503
Public Const ALLOC_ERR_UNKNOWN      As Long = -2147221504
Public Const ALLOC_ERR_CALCULATE    As Long = -2147221502

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************
Public Enum XP_TargetQuantity
    XP_AltTargetQuantity1 = 100
    XP_AltTargetQuantity2 = 200
    XP_AltTargetQuantity3 = 300
End Enum

'*****************************************************************************
'OBJECTS
'*****************************************************************************
Public g_LU     As Scripting.Dictionary 'Use this to store data key item pairs.
'Public g_MT     As clsMathHelper
'Public g_SRT    As clsSort
Public g_RES    As clsAllocResults
Public g_ALC()  As clsAllocLocation
Public g_FIL()  As clsFillLocation
Public g_PRI()  As clsPriority

