VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsMathHelper"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsMathHelper
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   REMOVED FROM VISALLOCATION. CONTENTS MOVED TO modALLOCFUNCTIONS.BAS
'
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "MathHelper."

Const FLOAT_PRECISION   As Double = 0.0001
Const FLOAT_ROUND       As Integer = 4

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************
Public Enum MA_Round_Direction
    Round_Up = 0
    Round_Down = 1
End Enum

'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************

'*****************************************************************************
'PROPERTIES
'*****************************************************************************

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
'NotEquals()
'
'*****************************************************************************
'Function(): NotEquals----------------------------------
Public Function NotEquals(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_NotEquals."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        NotEquals = False
    Else
        NotEquals = True
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): Equals----------------------------------
Public Function Equals(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_Equals."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        Equals = True
    Else
        Equals = False
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): LessThanEqualTo----------------------------------
Public Function LessThanEqualTo(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_LessThanEqualTo."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If dblNumber1 < dblNumber2 Then
        LessThanEqualTo = True
    ElseIf Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        LessThanEqualTo = True
    Else
        LessThanEqualTo = False
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): GreaterThanEqualTo----------------------------------
Public Function GreaterThanEqualTo(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_GreaterThanEqualTo."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Variables
    If dblNumber1 > dblNumber2 Then
        GreaterThanEqualTo = True
    ElseIf Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        GreaterThanEqualTo = True
    Else
        GreaterThanEqualTo = False
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): LessThan----------------------------------
Public Function LessThan(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_LessThan."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        LessThan = False
    ElseIf dblNumber1 < dblNumber2 Then
        LessThan = True
    Else
        LessThan = False
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): GreaterThan----------------------------------
Public Function GreaterThan(ByRef dblNumber1 As Double, ByRef dblNumber2 As Double) As Boolean
    Const FUNCTION_NAME = "f_GreaterThan."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(dblNumber1 - dblNumber2) <= FLOAT_PRECISION Then
        GreaterThan = False
    ElseIf dblNumber1 > dblNumber2 Then
        GreaterThan = True
    Else
        GreaterThan = False
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): NeverLessThanZero----------------------------------
Public Function NeverLessThanZero(ByRef dblValue As Double, ByRef blnUseNeverLessThanZero) As Double
    Const FUNCTION_NAME = "f_NeverLessThanZero."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If dblValue < 0 And blnUseNeverLessThanZero = True Then
        NeverLessThanZero = 0
    Else
        NeverLessThanZero = dblValue
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): Remainder----------------------------------
Public Function Remainder(ByVal dblNumber As Double, ByVal Round_Direction As MA_Round_Direction) As Double
    Const FUNCTION_NAME = "f_Remainder."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Clean any precision problems off the number
    dblNumber = Round(dblNumber, FLOAT_ROUND)
    
    If CDbl(CLng(dblNumber)) <> dblNumber Then
    
        If Round_Direction = Round_Up Then
            Remainder = IIf(CDbl(CLng(dblNumber)) > dblNumber, CDbl(CLng(dblNumber)) - dblNumber, 1 - (dblNumber - CDbl(CLng(dblNumber))))
        Else
            Remainder = IIf(CDbl(CLng(dblNumber)) > dblNumber, 1 - (CDbl(CLng(dblNumber)) - dblNumber), dblNumber - CDbl(CLng(dblNumber)))
        End If
    Else
        Remainder = 0
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): IsWholeNumber----------------------------------
Public Function IsWholeNumber(ByVal dblNumber As Double) As Boolean
    Const FUNCTION_NAME = "f_IsWholeNumber."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    If Abs(CDbl(CLng(dblNumber)) - dblNumber) < FLOAT_PRECISION Then
        IsWholeNumber = True
    Else
        IsWholeNumber = False
    End If

Exit Function
'Error Handler-----------------------
f_trap:
    Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
