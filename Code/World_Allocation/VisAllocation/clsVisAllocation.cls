VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsVisAllocation"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = True
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsAllocate
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   Contains the entry points to the Allocation module
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsVisAllocation."

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************
Public Enum XP_Alloc_Increment
    XP_Alloc_Increment_PT_ONE = 10
    XP_Alloc_Increment_PT_TWO = 20
    XP_Alloc_Increment_PT_THREE = 30
    XP_Alloc_Increment_PT_FOUR = 40
    XP_Alloc_Increment_PT_FIVE = 50
    XP_Alloc_Increment_PT_SIX = 60
    XP_Alloc_Increment_PT_SEVEN = 70
    XP_Alloc_Increment_PT_EIGHT = 80
    XP_Alloc_Increment_PT_NINE = 90
    XP_Alloc_Increment_ONE = 100
End Enum

Public Enum XP_Alloc_Rounding
    XP_Alloc_Rounding_On = 100
    XP_Alloc_Rounding_Off = 200
End Enum

Public Enum XP_Alloc_Less_Than_Zero
    XP_Alloc_Less_Than_Zero_On = 100
    XP_Alloc_Less_Than_Zero_Off = 200
End Enum

Public Enum XP_Exclude_Zero_Result
    XP_Exclude_Zero_Result_On = 100
    XP_Exclude_Zero_Result_Off = 200
End Enum

Public Enum XP_Alloc_Location_Type
    XP_Alloc_Location_Type_1 = 100
    XP_Alloc_Location_Type_2 = 200
    XP_Alloc_Location_Type_3 = 300
    XP_Alloc_Location_Type_4 = 400
End Enum

'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************
Private m_lngNumberAllocLocation            As Long
Private m_lngNumberFillLocation             As Long
Private m_lngNumberPriority                 As Long

Private m_blnUseTargetQuantity1             As Boolean
Private m_blnUseTargetQuantity2             As Boolean
Private m_blnUseTargetQuantity3             As Boolean

Private m_dbAllocIncrement                  As Double

Private m_XP_Alloc_Rounding                 As XP_Alloc_Rounding
Private m_XP_Alloc_Less_Than_Zero           As XP_Alloc_Less_Than_Zero

'*****************************************************************************
'PROPERTIES
'*****************************************************************************

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
Private Sub Class_Initialize()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Class_Initialize() -- Initialize objects and dependencies
' .......................................................................
'
'   Initialize all local objects and dependencies. Initialize specific global objects.
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_Class_Initialize."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Init Global Objects
    Set g_LU = New Scripting.Dictionary
    'Set g_MT = New clsMathHelper
    'Set g_SRT = New clsSort
    
    'Init array counters
    m_lngNumberAllocLocation = 0
    m_lngNumberFillLocation = 0
    m_lngNumberPriority = 0
    
    'Init flags
    m_blnUseTargetQuantity1 = False
    m_blnUseTargetQuantity2 = False
    m_blnUseTargetQuantity3 = False

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Private Sub Class_Terminate()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Class_Terminate() -- Destroy objects and dependencies
' .......................................................................
'
'   Destroy all local objects and dependencies. Destroy specific global objects.
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_Class_Terminate."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    Dim lngCount                As Long
    
    'Terminate Object references
    For lngCount = 0 To (m_lngNumberAllocLocation - 1)
        Set g_ALC(lngCount) = Nothing
    Next
    For lngCount = 0 To (m_lngNumberFillLocation - 1)
        Set g_FIL(lngCount) = Nothing
    Next
    For lngCount = 0 To (m_lngNumberPriority - 1)
        Set g_PRI(lngCount) = Nothing
    Next
    
    'Terminate Global Objects
    Set g_LU = Nothing
    'Set g_MT = Nothing
    'Set g_SRT = Nothing

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
' Reset() -- sets defaults values to the objects and variables used by Allocation
' SetProperties() -- initializes the allocation variables using named constants
' AddAllocLocation() -- Adds a reference to the given location into g_LU and g_ALC
' AddFillLocation() -- Adds a reference to the given location into g_LU and g_FIL
' Allocate() -- Processes the actual distribution of inventory.
' GetResults() -- Returns a string with a summary of g_ALC and g_FIL qties
'*****************************************************************************

Public Sub Reset()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Reset() -- Sets defaults values for the objects and variables used by Allocation
' .......................................................................
'
'   Sets defaults values for the objects and variables used by Allocation.
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_Reset."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim lngCount                    As Long
    
    'Empty the lookup object
    g_LU.RemoveAll
    
    'Clear out Arrays
    For lngCount = 0 To (m_lngNumberAllocLocation - 1)
        Set g_ALC(lngCount) = Nothing
    Next
    For lngCount = 0 To (m_lngNumberFillLocation - 1)
        Set g_FIL(lngCount) = Nothing
    Next
    For lngCount = 0 To (m_lngNumberPriority - 1)
        Set g_PRI(lngCount) = Nothing
    Next
    
    'reset flags and counters
    m_lngNumberAllocLocation = 0
    m_lngNumberFillLocation = 0
    m_lngNumberPriority = 0
    
    m_blnUseTargetQuantity1 = False
    m_blnUseTargetQuantity2 = False
    m_blnUseTargetQuantity3 = False
    
    'Check results object
    If IsObject(g_RES) = True Then
        Set g_RES = Nothing
    End If
    
'''    'Reset all module counters and array pointers
'''    m_lngNumberAllocLocation = 0
'''    m_lngNumberFillLocation = 0
'''    m_lngUboundPriority = 0
'''
'''    'Reset flags
'''    m_blnUseNeedQuantityOverRide = False
'''
'''    'Redim the arrays
'''
'''    'Priority array
'''    ReDim m_arrPriority(m_lngUboundPriority) As typPriority
'''
'''    'Alloc Location Array
'''    ReDim m_arrAllocLocation(m_lngNumberAllocLocation) As typAllocLocation
'''
'''    'Fill Location Array
'''    ReDim m_arrFillLocation(m_lngNumberFillLocation) As typFillLocation
'''
'''    'Reset dictionary object
'''    m_dicLookupHelper.RemoveAll

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub SetProperties(XP_Alloc_Increment As XP_Alloc_Increment, _
                        XP_Alloc_Rounding As XP_Alloc_Rounding, _
                        XP_Alloc_Less_Than_Zero As XP_Alloc_Less_Than_Zero)
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' SetProperties() -- initializes the allocation variables using named constants
' .......................................................................
'
'   Initializes the allocation variables using named constants.
'
'   Arguments:
'       XP_Alloc_Increment
'       -- sets the value with which to increment allocated quantity
'       XP_Alloc_Rounding
'       -- sets the global variable identifying the need for rounding off calculations
'       XP_Alloc_Less_Than_Zero
'       -- sets the global variable identifying actions to be taken
'           if quantity value is less than zero. If this variable is true,
'           then set quantity to zero, else leave as is.
'
'   Return values: None
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_SetProperties."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If

    'Variables
    m_dbAllocIncrement = CDbl(CDbl(XP_Alloc_Increment) / 100)
    m_XP_Alloc_Rounding = XP_Alloc_Rounding
    m_XP_Alloc_Less_Than_Zero = XP_Alloc_Less_Than_Zero

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub AddAllocLocation(LocationName As String, _
    Priority As Long, _
    AvailableQuantity As Double, _
    SubSetName As String, _
    TargetQuantity1 As Double, TargetQuantity2 As Double, TargetQuantity3 As Double, _
    FillLocation1 As Variant, _
    Optional FillLocation2 As Variant, Optional FillLocation3 As Variant, _
    Optional FillLocation4 As Variant, Optional FillLocation5 As Variant, _
    Optional FillLocation6 As Variant, Optional FillLocation7 As Variant, _
    Optional FillLocation8 As Variant, Optional FillLocation9 As Variant, _
    Optional FillLocation10 As Variant)
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' AddAllocLocation() -- Adds a reference to the given location into g_LU and g_ALC
' .......................................................................
'
'   If this location name does not exist then
'       Increment global alloc location array and instantiate object
'       Add a lookup reference to this location into g_LU
'       Set target and available qties for this loc
'       Set properties in g_ALC -- location name and priority
'       Add alloc subset to g_ALC
'       Set target quantity use flags
'       Add priority if not existent in g_LU
'    Else
'       Check for invalid priority setting -- raise error if found
'       Add alloc subset to g_ALC
'       Set target quantity use flags

'   Arguments:
'        LocationName
'        Priority
'        AvailableQuantity
'        SubSetName
'        TargetQuantity1 to TargetQuantity3
'        FillLocation1
'        Optional FillLocation2 to FillLocation10

'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_AddAllocLocation."
    #If DEBUG_MODE = False Then
    On Error GoTo s_trap:
    #End If
    
    'Check to see if this alloc location exists
    If g_LU.Exists(LU_ALLC_LOCATION & LocationName) = False Then
         'Increment global alloc location array and instantiate object
         ReDim Preserve g_ALC(m_lngNumberAllocLocation) As clsAllocLocation
         Set g_ALC(m_lngNumberAllocLocation) = New clsAllocLocation
         
         'Add Reference
         g_LU.Add LU_ALLC_LOCATION & LocationName, m_lngNumberAllocLocation
         
         'Set to zero if needed for quantities
         TargetQuantity1 = IIf(m_XP_Alloc_Less_Than_Zero = XP_Alloc_Less_Than_Zero_On _
                                And TargetQuantity1 < 0, 0, TargetQuantity1)
         TargetQuantity2 = IIf(m_XP_Alloc_Less_Than_Zero = XP_Alloc_Less_Than_Zero_On _
                                And TargetQuantity2 < 0, 0, TargetQuantity2)
         TargetQuantity3 = IIf(m_XP_Alloc_Less_Than_Zero = XP_Alloc_Less_Than_Zero_On _
                                And TargetQuantity3 < 0, 0, TargetQuantity3)
         AvailableQuantity = IIf(m_XP_Alloc_Less_Than_Zero = XP_Alloc_Less_Than_Zero_On _
                                And AvailableQuantity < 0, 0, AvailableQuantity)
         
         'Set properties
         g_ALC(m_lngNumberAllocLocation).LocationName = LocationName
         g_ALC(m_lngNumberAllocLocation).Priority = Priority
         
         'Add Fill Location Names
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation1
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation2
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation3
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation4
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation5
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation6
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation7
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation8
         g_ALC(m_lngNumberAllocLocation).AddFillFromLocation FillLocation10
         
         'Add alloc subset
         g_ALC(m_lngNumberAllocLocation).AddAllocSubset SubSetName, _
                                                        AvailableQuantity, _
                                                        TargetQuantity1, _
                                                        TargetQuantity2, _
                                                        TargetQuantity3
         
         'Set target quantity use flags
         If g_NotEquals(TargetQuantity1, 0) Then m_blnUseTargetQuantity1 = True
         If g_NotEquals(TargetQuantity2, 0) Then m_blnUseTargetQuantity2 = True
         If g_NotEquals(TargetQuantity3, 0) Then m_blnUseTargetQuantity3 = True
         
         'Add Priority if not exists
         If g_LU.Exists(LU_PRIORITY & CStr(Priority)) = False Then
         
             'Redim and instantiate
             ReDim Preserve g_PRI(m_lngNumberPriority) As clsPriority
             Set g_PRI(m_lngNumberPriority) = New clsPriority
         
             'add reference
             g_LU.Add LU_PRIORITY & CStr(Priority), m_lngNumberPriority
         
             'Set properties
             g_PRI(m_lngNumberPriority).Priority = Priority
             g_PRI(m_lngNumberPriority).AddAllocLocation LocationName
         
             'Increment Counter
             m_lngNumberPriority = m_lngNumberPriority + 1
         
         Else
             'Add name to priority
             g_PRI(CLng(g_LU.Item(LU_PRIORITY & CStr(Priority)))).AddAllocLocation LocationName
         
         End If
         
         'Increment Counter
         m_lngNumberAllocLocation = m_lngNumberAllocLocation + 1
    
    Else
         'Check for invalid priority setting
         If g_ALC(CLng(g_LU.Item(LU_ALLC_LOCATION & LocationName))).Priority <> Priority Then
             Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid priority [" & Priority & "]"
         End If
     
         'Add alloc subset
         g_ALC(CLng(g_LU.Item(LU_ALLC_LOCATION & LocationName))).AddAllocSubset SubSetName, _
                                                                                AvailableQuantity, _
                                                                                TargetQuantity1, _
                                                                                TargetQuantity2, _
                                                                                TargetQuantity3
         
         'Set target quantity use flags
         If g_NotEquals(TargetQuantity1, 0) Then m_blnUseTargetQuantity1 = True
         If g_NotEquals(TargetQuantity2, 0) Then m_blnUseTargetQuantity2 = True
         If g_NotEquals(TargetQuantity3, 0) Then m_blnUseTargetQuantity3 = True
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise ALLOC_ERR_UNKNOWN, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub AddFillLocation(LocationName As String, AvailableQuantity As Double)
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' AddFillLocation() -- Adds a reference to the given location into g_LU and g_ALC
' .......................................................................
'
'   If this location name does not exist then
'       Add a lookup reference to this location into g_LU
'       Set available qty for this loc
'       Set properties in g_FIL -- location name and priority
'       Set target quantity use flags
'       Add priority if not existent in g_LU
'    Else
'       raise error for invalud data -- location already exists
'
'   Arguments:
'        LocationName
'        AvailableQuantity
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_AddFillLocation."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects

    'Variables
    
    'Check to see if this location exists
    If g_LU.Exists(LU_FILL_LOCATION & LocationName) = False Then
        'Redim array and instantiate
        ReDim Preserve g_FIL(m_lngNumberFillLocation) As clsFillLocation
        Set g_FIL(m_lngNumberFillLocation) = New clsFillLocation
        
        'Add Lookup reference
        g_LU.Add LU_FILL_LOCATION & LocationName, m_lngNumberFillLocation
        
        'Zero the quantities if needed
        AvailableQuantity = IIf(m_XP_Alloc_Less_Than_Zero = XP_Alloc_Less_Than_Zero_On _
                                And AvailableQuantity < 0, 0, AvailableQuantity)
        
        'Set properties
        g_FIL(m_lngNumberFillLocation).LocationName = LocationName
        g_FIL(m_lngNumberFillLocation).AvailableQuantity = AvailableQuantity
    
        'Increment counter
        m_lngNumberFillLocation = m_lngNumberFillLocation + 1
    
    Else
        Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data [" & LocationName & "] already exists"
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise ALLOC_ERR_UNKNOWN, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Function Allocate() As clsAllocResults
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Allocate() -- Processes the actual distribution of inventory.
' .......................................................................
'
'   This is primarily a control function that calls on internal procedures.
'       Call PrepareData
'       Call AllocatePriority() to cycle through the priority groups,
'           allocating As applicable
'       Call g_ALC.AllocateSubset to create the AllocResults set
'       Return the results set
'
'   Arguments: None
'
'   Return values: AllocResults
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_Allocate."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If

    'Variables
    Dim lngCount            As Long
    
    'Prepare the data
    PrepareData
    
    'Cycle through the priority groups allocating
    For lngCount = 0 To (m_lngNumberPriority - 1)
        
        'Randomize the start point
        'g_PRI(lngCount).RandomizeStartPoint
        
        'Allocate the priority
        AllocatePriority g_PRI(lngCount).Priority
     
    Next lngCount
    
    'Instantiate results object
    Set g_RES = New clsAllocResults
    
    For lngCount = 0 To (m_lngNumberAllocLocation - 1)
        g_ALC(lngCount).AllocateSubSet
    Next
    
    'Return Results Set
    Set Allocate = g_RES
    
    Set g_RES = Nothing
    
Exit Function
'Error Handler
s_trap:
    Err.Raise ALLOC_ERR_UNKNOWN, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Function

Public Function GetResults() As String
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' GetResults() -- Returns a string with a summary of allocation results
' .......................................................................
'
'   Returns a string with a summary of allocation results
'
'   Arguments: None
'
'   Return values: one-string summary of of g_ALC and g_FIL quantities
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const FUNCTION_NAME = "f_GetResults."
    #If DEBUG_MODE = False Then
       On Error GoTo f_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim arrString()                     As String
    Dim lngNumberElements               As Long
    
    Dim lngCountAllocLocation           As Long
    Dim lngCountAllocLocationPointer    As Long
    Dim lngCountFillLocation            As Long
    
    ReDim Preserve arrString(3) As String
    lngNumberElements = 4
    
    arrString(0) = "----------------------------------------------------------------" & vbCrLf
    arrString(1) = "----------------------------------------------------------------" & vbCrLf
    arrString(2) = "----------------------------------------------------------------" & vbCrLf
    arrString(3) = "----------------------------------------------------------------" & vbCrLf
    
    For lngCountAllocLocation = 0 To (m_lngNumberAllocLocation - 1)
        ReDim Preserve arrString(lngNumberElements + 8) As String
    
        arrString(lngNumberElements) = vbCrLf
        arrString(lngNumberElements + 1) = "----------------------------------------------------------------" & vbCrLf
        arrString(lngNumberElements + 2) = "ALLOC LOCATION:" & vbTab & vbTab & g_ALC(lngCountAllocLocation).LocationName & vbCrLf
        arrString(lngNumberElements + 3) = "----------------------------------------------------------------" & vbCrLf
        arrString(lngNumberElements + 4) = vbCrLf
        arrString(lngNumberElements + 5) = "AVAIL QUANTITY:" & vbTab & vbTab & CStr(g_ALC(lngCountAllocLocation).AvailableQuantity) & vbCrLf
        arrString(lngNumberElements + 6) = "TARGET QUANTITY:" & vbTab & vbTab & CStr(g_ALC(lngCountAllocLocation).TargetQuantity) & vbCrLf
        arrString(lngNumberElements + 7) = "TOTAL QUANTITY:" & vbTab & vbTab & CStr(g_ALC(lngCountAllocLocation).TotalQuantity) & vbCrLf
        arrString(lngNumberElements + 8) = "ALLOC QUANTITY:" & vbTab & vbTab & CStr(g_ALC(lngCountAllocLocation).AllocatedQuantity) & vbCrLf
    
        lngNumberElements = lngNumberElements + 9
    
        For lngCountFillLocation = 0 To (g_ALC(lngCountAllocLocation).NumberFillFromLocation - 1)
            ReDim Preserve arrString(lngNumberElements + 2) As String
    
            arrString(lngNumberElements) = vbCrLf
            arrString(lngNumberElements + 1) = "FILL LOCATION:" & vbTab & vbTab & vbTab & g_ALC(lngCountAllocLocation).FillFromLocationName(lngCountFillLocation) & vbCrLf
            arrString(lngNumberElements + 2) = "ALLOC QUANTITY:" & vbTab & vbTab & CStr(g_ALC(lngCountAllocLocation).FillFromLocationQuantity(lngCountFillLocation)) & vbCrLf
    
            lngNumberElements = lngNumberElements + 3
        Next
    Next
    
    ReDim Preserve arrString(lngNumberElements + 4) As String
    
    arrString(lngNumberElements) = "----------------------------------------------------------------" & vbCrLf
    arrString(lngNumberElements + 1) = "----------------------------------------------------------------" & vbCrLf
    arrString(lngNumberElements + 2) = "----------------------------------------------------------------" & vbCrLf
    arrString(lngNumberElements + 3) = "----------------------------------------------------------------" & vbCrLf
    arrString(lngNumberElements + 4) = vbCrLf
    
    lngNumberElements = lngNumberElements + 5
    
    For lngCountFillLocation = 0 To (m_lngNumberFillLocation - 1)
        ReDim Preserve arrString(lngNumberElements + 9) As String
        
        arrString(lngNumberElements) = vbCrLf
        arrString(lngNumberElements + 1) = "----------------------------------------------------------------" & vbCrLf
        arrString(lngNumberElements + 2) = "FILL LOCATION:" & vbTab & vbTab & g_FIL(lngCountFillLocation).LocationName & vbCrLf
        arrString(lngNumberElements + 3) = "----------------------------------------------------------------" & vbCrLf
        arrString(lngNumberElements + 4) = "AVAIL QUANTITY:" & vbTab & vbTab & CStr(g_FIL(lngCountFillLocation).AvailableQuantity) & vbCrLf
        arrString(lngNumberElements + 5) = "ALLOC QUANTITY:" & vbTab & vbTab & CStr(g_FIL(lngCountFillLocation).AvailableQuantity - g_FIL(lngCountFillLocation).RemainingQuantity) & vbCrLf
        arrString(lngNumberElements + 6) = vbCrLf
    
        arrString(lngNumberElements + 7) = "----------------------------------------------------------------" & vbCrLf
        arrString(lngNumberElements + 8) = "LINKED ALLOC LOCATIONS" & vbCrLf
        arrString(lngNumberElements + 9) = "----------------------------------------------------------------" & vbCrLf
    
        lngNumberElements = lngNumberElements + 10
    
        For lngCountAllocLocationPointer = 0 To (g_FIL(lngCountFillLocation).NumberAllocLocation - 1)
            ReDim Preserve arrString(lngNumberElements + 1) As String
            
            arrString(lngNumberElements) = "ALLOC LOCATION:" & vbTab & vbTab & g_FIL(lngCountFillLocation).AllocLocationName(lngCountAllocLocationPointer) & vbCrLf
            arrString(lngNumberElements + 1) = "ALLOC QUANTITY:" & vbTab & vbTab & g_FIL(lngCountFillLocation).AllocLocationQuantity(lngCountAllocLocationPointer) & vbCrLf & vbCrLf
        
            lngNumberElements = lngNumberElements + 2
        Next
    Next
    
    GetResults = Join(arrString)
    
Exit Function
'Error Handler
f_trap:
    Err.Raise ALLOC_ERR_UNKNOWN, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
' PrepareData() -- Sets the target quantity to be used in allocation.
' AllocatePriority() -- Distributes inventory based on priority
' AllocateAmount() -- adjusts the quantities in the fill and alloc locations based on allocation decision.
'*****************************************************************************

Private Sub PrepareData()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' PrepareData() -- Sets the target quantity to be used in allocation.
' .......................................................................
'
'   Sets the target quantity to be used in allocation.
'       Also sorts priority groups
'
'   Arguments: None
'
'   Return values: one-string summary of of g_ALC and g_FIL quantities
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_PrepareData."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim enmTargetQuantity           As XP_TargetQuantity
    Dim lngCount                    As Long
    
    lngCount = 0
    
    'Validate Target Quantity based on flags set in AddAllocLocation
    If m_blnUseTargetQuantity1 = True Then
        lngCount = lngCount + 1
        enmTargetQuantity = XP_AltTargetQuantity1
    End If
    If m_blnUseTargetQuantity2 = True Then
        lngCount = lngCount + 1
        enmTargetQuantity = XP_AltTargetQuantity2
    End If
    If m_blnUseTargetQuantity3 = True Then
        lngCount = lngCount + 1
        enmTargetQuantity = XP_AltTargetQuantity3
    End If
    
    'There should only be one of the options that's selected (what if none are?)
    If lngCount > 1 Then    'Shouldn't this be lngCount <> 1
        Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid data cannot set target quantity"
    End If
    
    For lngCount = 0 To (m_lngNumberAllocLocation - 1)
        g_ALC(lngCount).LinkFillFromLocations
        g_ALC(lngCount).SetQuantities enmTargetQuantity
        g_ALC(lngCount).QuickSortAllocSubSet
    Next
    
    'Sort Priority Groups
    'g_SRT.QuickSortPriority 0, (m_lngNumberPriority - 1)
    g_QuickSortPriority 0, (m_lngNumberPriority - 1)

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Private Sub AllocatePriority(Priority As Long)
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' AllocatePriority() -- Distributes inventory based on priority
' .......................................................................
'
'    For each fill location:
'       Calculate the initial need ratio per alloc. loc,
'       and the initial need ratio average.
'       Check if the allocation location can be given inventory.
'       Check if there is inventory available in the fill location.
'       Call AllocateAmount() distribute inventory.
'       Recalc need ratio and average per iteration.
'
'   Arguments: Priority
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_AllocatePriority."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Counters
    Dim lngFillFromCount                        As Long
    Dim lngAllocLocationCount                   As Long
    
    'RowCount
    Dim lngNumberFillFromLocation               As Long
    Dim lngNumberAllocLocation                  As Long
    
    'Index place holders
    Dim lngPriorityIndex                        As Long
    Dim lngAllocIndex                           As Long
    
    'Calculation Variables
    Dim dblNeedRatioAverageTotal                As Double
    Dim dblNeedRatioAverage                     As Double
    Dim dblAllocatedAmount                      As Double
    Dim dblAllocatedAmountCompare               As Double
    
    'Set some pointers
    lngPriorityIndex = CLng(g_LU.Item(LU_PRIORITY & Priority))
    lngNumberFillFromLocation = g_PRI(lngPriorityIndex).NumberFillFromLocation
    lngNumberAllocLocation = g_PRI(lngPriorityIndex).NumberAllocLocation
    
    'For each fill location
    For lngFillFromCount = 0 To (lngNumberFillFromLocation - 1)
        'Set values
        dblNeedRatioAverageTotal = 0
        dblAllocatedAmount = 0
        dblAllocatedAmountCompare = -1
    
        'Calculate Need Ratio Average at this level
        For lngAllocLocationCount = 0 To (lngNumberAllocLocation - 1)
            lngAllocIndex = g_LU.Item(LU_ALLC_LOCATION & g_PRI(lngPriorityIndex).AllocLocationName(lngAllocLocationCount))
            
            If g_ALC(lngAllocIndex).NumberFillFromLocation >= (lngFillFromCount + 1) Then
                g_ALC(lngAllocIndex).CalcNeedRatio
                dblNeedRatioAverageTotal = dblNeedRatioAverageTotal + g_ALC(lngAllocIndex).NeedRatio
            Else
                dblNeedRatioAverageTotal = dblNeedRatioAverageTotal + 1
            End If
            
            'Set fully allocated flag
            g_ALC(lngAllocIndex).FullyAllocatedFlag = False
        Next lngAllocLocationCount
    
        'Calc the average
        dblNeedRatioAverage = dblNeedRatioAverageTotal / CDbl(lngNumberAllocLocation)
        Do Until dblAllocatedAmount = dblAllocatedAmountCompare
            dblAllocatedAmountCompare = dblAllocatedAmount
            For lngAllocLocationCount = 0 To (lngNumberAllocLocation - 1)
                lngAllocIndex = g_LU.Item(LU_ALLC_LOCATION & g_PRI(lngPriorityIndex).AllocLocationName(lngAllocLocationCount))
                
                'Check if the allocation location can be given inventory
                If (g_ALC(lngAllocIndex).FullyAllocatedFlag = False) _
                And (g_LessThanEqualTo(g_ALC(lngAllocIndex).NeedRatio, dblNeedRatioAverage) = True) _
                And (g_ALC(lngAllocIndex).NumberFillFromLocation >= (lngFillFromCount + 1)) Then
                    'Check if there is inventory to be allocated from the fill locations
                    If AllocateAmount(g_ALC(lngAllocIndex).FillFromLocationName(lngFillFromCount), g_ALC(lngAllocIndex).LocationName) > 0 Then
                        'Add to the comparison variable
                        dblAllocatedAmount = dblAllocatedAmount + m_dbAllocIncrement
                        'Subtract old need ratio from total
                        dblNeedRatioAverageTotal = dblNeedRatioAverageTotal - g_ALC(lngAllocIndex).NeedRatio
                        'Recalc Need ratio for this alloc location
                        g_ALC(lngAllocIndex).CalcNeedRatio
                        'Add back
                        dblNeedRatioAverageTotal = dblNeedRatioAverageTotal + g_ALC(lngAllocIndex).NeedRatio
                        'Recalc the average
                         dblNeedRatioAverage = dblNeedRatioAverageTotal / CDbl(lngNumberAllocLocation)
                                        
                    Else
                        'Force another compare
                        dblAllocatedAmountCompare = -1
                        'Set the fully allocated flag
                        g_ALC(lngAllocIndex).FullyAllocatedFlag = True
                        'Subtract need ratio from total
                        dblNeedRatioAverageTotal = dblNeedRatioAverageTotal - g_ALC(lngAllocIndex).NeedRatio
                        'Add one to the need ratio average total
                        dblNeedRatioAverageTotal = dblNeedRatioAverageTotal + 1
                        'Recalc the average
                         dblNeedRatioAverage = dblNeedRatioAverageTotal / CDbl(lngNumberAllocLocation)
                    End If
                End If
            Next lngAllocLocationCount
        Loop
    Next lngFillFromCount

Exit Sub
'Error Handler
s_trap:
    Err.Raise ALLOC_ERR_CALCULATE, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Private Function AllocateAmount(FillLocation As String, AllocLocation As String) As Double
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' AllocateAmount() -- adjusts the quantities in the fill and alloc locations based on allocation decision.
' .......................................................................
'
'   Given a fill location and an allocation location:
'       If the quantity remaining in the fill location is acceptable for the request
'       then deduct from remaining and increment allocated quantity
'
'   Arguments:
'       FillLocation
'       AllocLocation
'
'   Return values: the actual adjustment applied
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_AllocateAmount."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    Dim lngFillLocationIndex                    As Long
    Dim lngAllocLocationIndex                   As Long
    
    Dim lngFillFromLocationIndex                As Long
    
    Dim dblAllocateAmount                       As Double
    
    Dim dblRemainingQuantity                    As Double
    Dim dblAllocatedQuantity                    As Double
    Dim dblAvailableQuantity                    As Double
    Dim dblTargetQuantity                       As Double
    Dim dblDifference                           As Double
    
    'default alloc amount to 0
    dblAllocateAmount = 0
    
    'Check if this is an acceptable allocation
    If g_LU.Exists(LU_FILL_LOCATION & FillLocation & LU_SUF_ALLC_LOCATION & AllocLocation) = True Then
        'Set indexes to avoid look up overhead
        lngFillLocationIndex = CLng(g_LU.Item(LU_FILL_LOCATION & FillLocation))
        dblRemainingQuantity = g_FIL(lngFillLocationIndex).RemainingQuantity
    
        'Check if remaining quantity is acceptable
        If g_GreaterThanEqualTo((dblRemainingQuantity - m_dbAllocIncrement), 0) = True Then
    
            lngAllocLocationIndex = CLng(g_LU.Item(LU_ALLC_LOCATION & AllocLocation))
            
            dblAllocatedQuantity = g_ALC(lngAllocLocationIndex).AllocatedQuantity
            dblAvailableQuantity = g_ALC(lngAllocLocationIndex).AvailableQuantity
            dblTargetQuantity = g_ALC(lngAllocLocationIndex).TargetQuantity
            
            dblDifference = dblTargetQuantity - (dblAllocatedQuantity + dblAvailableQuantity)
        
            If g_GreaterThanEqualTo(dblDifference, m_dbAllocIncrement) = True Then
        
                g_FIL(lngFillLocationIndex).RemainingQuantity = dblRemainingQuantity - m_dbAllocIncrement
                g_ALC(lngAllocLocationIndex).AllocatedQuantity = dblAllocatedQuantity + m_dbAllocIncrement
                
                'Procure the reverse index for the alloc -> fill Direction
                lngFillFromLocationIndex = CLng(g_LU.Item(LU_ALLC_LOCATION & AllocLocation & LU_SUF_FILL_LOCATION & FillLocation))
                g_ALC(lngAllocLocationIndex).FillFromLocationQuantity(lngFillFromLocationIndex) = g_ALC(lngAllocLocationIndex).FillFromLocationQuantity(lngFillFromLocationIndex) + m_dbAllocIncrement
                
                'Procure the reverse index for the fill -> alloc location
                lngAllocLocationIndex = CLng(g_LU.Item(LU_FILL_LOCATION & FillLocation & LU_SUF_ALLC_LOCATION & AllocLocation))
                g_FIL(lngFillLocationIndex).AllocLocationQuantity(lngAllocLocationIndex) = g_FIL(lngFillLocationIndex).AllocLocationQuantity(lngAllocLocationIndex) + m_dbAllocIncrement
                
                dblAllocateAmount = m_dbAllocIncrement
        
            End If
        End If
    End If
    
    AllocateAmount = dblAllocateAmount

Exit Function
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Function
