VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsPriority"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsPriority
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   Stores priority indices for allocation locations in an array
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsPriority."

'*****************************************************************************
'VARIABLES
'*****************************************************************************
Private m_lngNumberAllocLocation As Long

Private m_lngPriority As Long
Private m_lngNumberFillFromLocation As Long

'*****************************************************************************
'ARRAYS
'*****************************************************************************
Private m_arrAllocLocationNames() As String

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' Class_Initialize() -- Initialize objects and dependencies
' .......................................................................
'
'   Initialize all local objects and dependencies. Initialize specific global objects.
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Sub Class_Initialize()
    Const SUB_NAME = "s_Class_Initialize."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    m_lngNumberAllocLocation = 0
    m_lngNumberFillFromLocation = 0

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'*****************************************************************************
'PROPERTIES
' AllocLocationName -- Get
' NumberAllocLocation -- Get
' NumberFillFromLocation -- Get
' Priority -- Get/Let
'*****************************************************************************
Public Property Get AllocLocationName(Index As Long) As String
    Const PROPERTY_NAME = "pg_AllocLocationName."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    AllocLocationName = m_arrAllocLocationNames(Index)

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get NumberAllocLocation() As Long
    Const PROPERTY_NAME = "pg_NumberAllocLocation."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    NumberAllocLocation = m_lngNumberAllocLocation

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get NumberFillFromLocation() As Long
    Const PROPERTY_NAME = "pg_NumberFillFromLocation."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    NumberFillFromLocation = m_lngNumberFillFromLocation

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Get Priority() As Long
    Const PROPERTY_NAME = "pg_Priority."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    Priority = m_lngPriority

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

Public Property Let Priority(ByVal Priority As Long)
    Const PROPERTY_NAME = "pl_Priority."
    #If DEBUG_MODE = False Then
       On Error GoTo p_trap:
    #End If
    
    m_lngPriority = Priority

Exit Property
'Error Handler
p_trap:
    Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
' AddAllocLocation()
' RandomizeStartPoint()
'*****************************************************************************
Public Sub AddAllocLocation(LocationName As String)
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' AddAllocLocation() -- Adds a reference to the given location into the global lookup map
' .......................................................................
'
'   Adds a reference to the given location into the global lookup map
'   Increments NumberFillFromLocation if that in g_ALC is greater than the global var.
'
'   Arguments: LocationName
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_AddAllocLocation."
    #If DEBUG_MODE = False Then
    On Error GoTo s_trap:
    #End If
    
    'Objects
    
    'Variables
    
    If g_LU.Exists(LU_PRIORITY & CStr(m_lngPriority) & LU_SUF_ALLC_LOCATION & LocationName) = False Then
        ReDim Preserve m_arrAllocLocationNames(m_lngNumberAllocLocation) As String
        m_arrAllocLocationNames(m_lngNumberAllocLocation) = LocationName
        
        'Add Reference to global look up map
        g_LU.Add LU_PRIORITY & CStr(m_lngPriority) & LU_SUF_ALLC_LOCATION & LocationName, m_lngNumberAllocLocation
        
        m_lngNumberAllocLocation = m_lngNumberAllocLocation + 1
        
        'Check number Fill From locations and increment max if needed
        If g_ALC(CLng(g_LU.Item(LU_ALLC_LOCATION & LocationName))).NumberFillFromLocation _
        > m_lngNumberFillFromLocation Then
            m_lngNumberFillFromLocation = g_ALC(CLng(g_LU.Item(LU_ALLC_LOCATION & LocationName))).NumberFillFromLocation
        End If
    
    Else
        Err.Raise ALLOC_ERR_VALIDATION, SUB_NAME, "Invalid Data [" & LU_PRIORITY & CStr(m_lngPriority) & LU_SUF_ALLC_LOCATION & LocationName & "] already exists"
    End If

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

Public Sub RandomizeStartPoint()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
' RandomizeStartPoint() -- Shuffles the starting point in the AllocLocName array
' .......................................................................
'
'   Shuffles the starting point in the AllocLocName array (why?)
'
'   Arguments: None
'
'   Return values: None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Const SUB_NAME = "s_RandomizeStartPoint."
    #If DEBUG_MODE = False Then
       On Error GoTo s_trap:
    #End If
    
    'Variables
    Dim lngRandomIndex              As Long
    Dim strTempString               As String
    
    Randomize
    
    'Find Random index
    lngRandomIndex = (Int((m_lngNumberAllocLocation * Rnd) + 1) - 1)
    
    If lngRandomIndex <> 0 Then
        'Get value for temp string
        strTempString = m_arrAllocLocationNames(0)
        
        'set the 0 element to the random index
        g_LU(LU_PRIORITY & CStr(m_lngPriority) & LU_SUF_ALLC_LOCATION & strTempString) = lngRandomIndex
        
        'set the random element to the 0 index
        g_LU(LU_PRIORITY & CStr(m_lngPriority) & LU_SUF_ALLC_LOCATION & m_arrAllocLocationNames(lngRandomIndex)) = 0
        
        'set the 0 element in the array to the random name string
        m_arrAllocLocationNames(0) = m_arrAllocLocationNames(lngRandomIndex)
        
        'set the random element in the array to the temp string
        m_arrAllocLocationNames(lngRandomIndex) = strTempString
    End If

Exit Sub
'Error Handler
s_trap:
    Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
'*****************************************************************************
