VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsSort"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsSort
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   REMOVED FROM VISALLOCATION. CONTENTS MOVED TO modALLOCFUNCTIONS.BAS
'
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsSort."

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************

'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************

'*****************************************************************************
'PROPERTIES
'*****************************************************************************

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'Sub(): QuickSortPriority----------------------------------
Public Sub QuickSortPriority(lngFirstIndex As Long, lngLastIndex As Long)
Const SUB_NAME = "s_QuickSortPriority."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

    'Objects

    Dim lngMiddleIndex                  As Long
    Dim lngTestPriority                 As Long
    Dim lngLowIndex                     As Long
    Dim lngHighIndex                    As Long
    Dim objPriority                     As clsPriority
    
    If lngFirstIndex = -1 Then lngFirstIndex = 0
    If lngLastIndex = -1 Then lngLastIndex = UBound(g_PRI, 1)
    
    If lngFirstIndex < lngLastIndex Then
    
    
        lngMiddleIndex = ((lngFirstIndex + lngLastIndex) / 2)
        lngTestPriority = g_PRI(lngMiddleIndex).Priority
        lngLowIndex = lngFirstIndex
        lngHighIndex = lngLastIndex
        
        Do
        
            Do While g_PRI(lngLowIndex).Priority < lngTestPriority
                lngLowIndex = lngLowIndex + 1
            Loop
            Do While g_PRI(lngHighIndex).Priority > lngTestPriority
                lngHighIndex = lngHighIndex - 1
            Loop
            
            If lngLowIndex <= lngHighIndex Then
            

                'Shuffle the Hash Table Look up object
                g_LU.Item(LU_PRIORITY & CStr(g_PRI(lngLowIndex).Priority)) = lngHighIndex
                g_LU.Item(LU_PRIORITY & CStr(g_PRI(lngHighIndex).Priority)) = lngLowIndex


                'Shuffle the priority object
                Set objPriority = g_PRI(lngLowIndex)
                Set g_PRI(lngLowIndex) = g_PRI(lngHighIndex)
                Set g_PRI(lngHighIndex) = objPriority
            
            
                lngLowIndex = lngLowIndex + 1
                lngHighIndex = lngHighIndex - 1
            
            End If
            
        Loop While (lngLowIndex <= lngHighIndex)
            
        If lngFirstIndex < lngHighIndex Then QuickSortPriority lngFirstIndex, lngHighIndex
        If lngLowIndex < lngLastIndex Then QuickSortPriority lngLowIndex, lngLastIndex
            
    End If

Exit Sub
'Error Handler-----------------------
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub





'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
