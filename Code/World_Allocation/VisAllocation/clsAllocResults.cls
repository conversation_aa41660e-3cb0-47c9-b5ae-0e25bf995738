VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsAllocResults"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = True
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsAllocResults
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   <One-line description of module purpose>
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsAllocResults."

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************
Public Enum XP_Sort_By
    XP_Location_From_Name = 100
    XP_Location_To_Name = 200
    XP_Location_Subset_Name = 300
End Enum
'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************
Private m_arrAllocResult()                              As clsAllocResult
Private m_arrFilterPointer()                            As Long

'*****************************************************************************
'PROPERTIES
'*****************************************************************************
Private m_lngNumberResults                              As Long
Private m_lngNumberFilterResults                        As Long
Private m_lngRecordPointer                              As Long
Private m_blnFilterZeroResult                           As Boolean


'Property->: FilterZeroResult----------------------------------
Public Property Get FilterZeroResult() As Boolean
Const PROPERTY_NAME = "pg_FilterZeroResult."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
FilterZeroResult = m_blnFilterZeroResult
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: FilterZeroResult----------------------------------
Public Property Let FilterZeroResult(ByVal FilterZeroResult As Boolean)
Const PROPERTY_NAME = "pl_FilterZeroResult."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If

Static blnFiltered              As Boolean

m_blnFilterZeroResult = FilterZeroResult

If blnFiltered = False Then
    ApplyFilter
    blnFiltered = True
End If

m_lngRecordPointer = 0

Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: Quantity----------------------------------
Public Property Get Quantity() As Double
Const PROPERTY_NAME = "pg_Quantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
If m_blnFilterZeroResult = False Then
    Quantity = m_arrAllocResult(m_lngRecordPointer).Quantity
Else
    Quantity = m_arrAllocResult(m_arrFilterPointer(m_lngRecordPointer)).Quantity
End If
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: LocationToName----------------------------------
Public Property Get LocationToName() As String
Const PROPERTY_NAME = "pg_LocationToName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
If m_blnFilterZeroResult = False Then
    LocationToName = m_arrAllocResult(m_lngRecordPointer).LocationToName
Else
    LocationToName = m_arrAllocResult(m_arrFilterPointer(m_lngRecordPointer)).LocationToName
End If
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: LocationFromName----------------------------------
Public Property Get LocationFromName() As String
Const PROPERTY_NAME = "pg_LocationFromName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
If m_blnFilterZeroResult = False Then
    LocationFromName = m_arrAllocResult(m_lngRecordPointer).LocationFromName
Else
    LocationFromName = m_arrAllocResult(m_arrFilterPointer(m_lngRecordPointer)).LocationFromName
End If
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: SubSetName----------------------------------
Public Property Get SubSetName() As String
Const PROPERTY_NAME = "pg_SubSetName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
If m_blnFilterZeroResult = False Then
    SubSetName = m_arrAllocResult(m_lngRecordPointer).SubSetName
Else
    SubSetName = m_arrAllocResult(m_arrFilterPointer(m_lngRecordPointer)).SubSetName
End If
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: NumberResults----------------------------------
Public Property Get NumberResults() As Long
Const PROPERTY_NAME = "pg_NumberResults."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
NumberResults = IIf(m_blnFilterZeroResult = True, m_lngNumberFilterResults, m_lngNumberResults)
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'Sub(): SortResults----------------------------------
Public Sub SortResults(SortBy As XP_Sort_By)
Const SUB_NAME = "s_SortResults."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
If m_lngNumberResults > 0 Then
    QuickSort -1, -1, SortBy
End If

If m_blnFilterZeroResult = True Then
    ApplyFilter
End If

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): MoveAbsolutePosition----------------------------------
Public Sub MoveAbsolutePosition(Index As Long)
Const SUB_NAME = "s_MoveAbsolutePosition."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
m_lngRecordPointer = Index

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): MoveNext----------------------------------
Public Sub MoveNext()
Const SUB_NAME = "s_MoveNext."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
m_lngRecordPointer = m_lngRecordPointer + 1

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): MovePrevious----------------------------------
Public Sub MovePrevious()
Const SUB_NAME = "s_MovePrevious."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
m_lngRecordPointer = m_lngRecordPointer - 1

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Function(): EOF----------------------------------
Public Function EOF() As Boolean
Const FUNCTION_NAME = "f_EOF."
#If DEBUG_MODE = False Then
   On Error GoTo f_trap:
#End If

'Objects

'Variables
EOF = IIf(((m_lngRecordPointer = m_lngNumberResults) And m_blnFilterZeroResult = False) Or ((m_lngRecordPointer = m_lngNumberFilterResults) And m_blnFilterZeroResult = True), True, False)

Exit Function
'Error Handler
f_trap:
Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Function(): BOF----------------------------------
Public Function BOF() As Boolean
Const FUNCTION_NAME = "f_BOF."
#If DEBUG_MODE = False Then
   On Error GoTo f_trap:
#End If

'Objects

'Variables
BOF = IIf(m_lngRecordPointer < 0, True, False)

Exit Function
'Error Handler
f_trap:
Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

'Sub(): MoveLast----------------------------------
Public Sub MoveLast()
Const SUB_NAME = "s_MoveLast."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
If m_blnFilterZeroResult = False Then
    m_lngRecordPointer = (m_lngNumberResults - 1)
Else
    m_lngRecordPointer = (m_lngNumberFilterResults - 1)
End If

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): MoveFirst----------------------------------
Public Sub MoveFirst()
Const SUB_NAME = "s_MoveFirst."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
m_lngRecordPointer = 0

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): AddAllocResult----------------------------------
Friend Sub AddAllocResult(LocationFromName As String, LocationToName As String, SubSetName As String, Quantity As Double)
Const SUB_NAME = "s_AddAllocResult."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
ReDim Preserve m_arrAllocResult(m_lngNumberResults) As clsAllocResult
Set m_arrAllocResult(m_lngNumberResults) = New clsAllocResult

m_arrAllocResult(m_lngNumberResults).LocationFromName = LocationFromName
m_arrAllocResult(m_lngNumberResults).LocationToName = LocationToName
m_arrAllocResult(m_lngNumberResults).SubSetName = SubSetName
m_arrAllocResult(m_lngNumberResults).Quantity = Quantity



m_lngNumberResults = m_lngNumberResults + 1

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub
'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'Sub(): ApplyFilter----------------------------------
Private Sub ApplyFilter()
Const SUB_NAME = "s_ApplyFilter."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
Dim lngCount                As Long

'Set the variable
m_lngNumberFilterResults = 0

For lngCount = 0 To (m_lngNumberResults - 1)
    If g_GreaterThan(m_arrAllocResult(lngCount).Quantity, 0) = True Then
        
        ReDim Preserve m_arrFilterPointer(m_lngNumberFilterResults) As Long
        m_arrFilterPointer(m_lngNumberFilterResults) = lngCount
        
        m_lngNumberFilterResults = m_lngNumberFilterResults + 1
        
    End If
Next

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): QuickSort----------------------------------
Private Sub QuickSort(lngFirstIndex As Long, lngLastIndex As Long, SortBy As XP_Sort_By)
Const SUB_NAME = "s_QuickSort."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

    'Objects

    Dim lngMiddleIndex                  As Long
    Dim strTestValue                    As String
    Dim lngLowIndex                     As Long
    Dim lngHighIndex                    As Long
    Dim objAllocResult                  As clsAllocResult
    
    If lngFirstIndex = -1 Then lngFirstIndex = 0
    If lngLastIndex = -1 Then lngLastIndex = UBound(m_arrAllocResult, 1)
    
    If lngFirstIndex < lngLastIndex Then
    
    
        lngMiddleIndex = ((lngFirstIndex + lngLastIndex) / 2)
        
        Select Case SortBy
        Case XP_Location_From_Name
            strTestValue = m_arrAllocResult(lngMiddleIndex).LocationFromName
        Case XP_Location_Subset_Name
            strTestValue = m_arrAllocResult(lngMiddleIndex).SubSetName
        Case XP_Location_To_Name
            strTestValue = m_arrAllocResult(lngMiddleIndex).LocationToName
        End Select
                
        lngLowIndex = lngFirstIndex
        lngHighIndex = lngLastIndex
        
        Do
            Select Case SortBy
            Case XP_Location_From_Name
                
                Do While m_arrAllocResult(lngLowIndex).LocationFromName < strTestValue
                    lngLowIndex = lngLowIndex + 1
                Loop
                Do While m_arrAllocResult(lngHighIndex).LocationFromName > strTestValue
                    lngHighIndex = lngHighIndex - 1
                Loop

            Case XP_Location_Subset_Name
            
                Do While m_arrAllocResult(lngLowIndex).SubSetName < strTestValue
                    lngLowIndex = lngLowIndex + 1
                Loop
                Do While m_arrAllocResult(lngHighIndex).SubSetName > strTestValue
                    lngHighIndex = lngHighIndex - 1
                Loop
            
            Case XP_Location_To_Name
                
                Do While m_arrAllocResult(lngLowIndex).LocationToName < strTestValue
                    lngLowIndex = lngLowIndex + 1
                Loop
                Do While m_arrAllocResult(lngHighIndex).LocationToName > strTestValue
                    lngHighIndex = lngHighIndex - 1
                Loop
            
            End Select
    

            If lngLowIndex <= lngHighIndex Then
            
                Set objAllocResult = m_arrAllocResult(lngLowIndex)
                Set m_arrAllocResult(lngLowIndex) = m_arrAllocResult(lngHighIndex)
                Set m_arrAllocResult(lngHighIndex) = objAllocResult
            
                lngLowIndex = lngLowIndex + 1
                lngHighIndex = lngHighIndex - 1
            
            End If
            
        Loop While (lngLowIndex <= lngHighIndex)
            
        If lngFirstIndex < lngHighIndex Then QuickSort lngFirstIndex, lngHighIndex, SortBy
        If lngLowIndex < lngLastIndex Then QuickSort lngLowIndex, lngLastIndex, SortBy
            
    End If

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub


'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
'Sub(): Class_Initialize----------------------------------
Private Sub Class_Initialize()
Const SUB_NAME = "s_Class_Initialize."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
m_lngNumberResults = 0
m_lngRecordPointer = 0
m_lngNumberFilterResults = 0
 
Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): Class_Terminate----------------------------------
Private Sub Class_Terminate()
Const SUB_NAME = "s_Class_Terminate."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
Dim lngCount    As Long

For lngCount = 0 To (m_lngNumberResults - 1)
    Set m_arrAllocResult(lngCount) = Nothing
Next

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub
