VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsAllocResult"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsAllocResult
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   <One-line description of module purpose>
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsAllocResult."

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************

'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************

'*****************************************************************************
'PROPERTIES
'*****************************************************************************
Private m_strLocationFromName                               As String
Private m_strLocationToName                                 As String
Private m_dblQuantity                                       As Double
Private m_strSubSetName                                     As String

'Property->: SubSetName----------------------------------
Public Property Get SubSetName() As String
Const PROPERTY_NAME = "pg_SubSetName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
SubSetName = m_strSubSetName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: SubSetName----------------------------------
Public Property Let SubSetName(ByVal SubSetName As String)
Const PROPERTY_NAME = "pl_SubSetName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_strSubSetName = SubSetName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property



'Property->: Quantity----------------------------------
Public Property Get Quantity() As Double
Const PROPERTY_NAME = "pg_Quantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
Quantity = m_dblQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: Quantity----------------------------------
Public Property Let Quantity(ByVal Quantity As Double)
Const PROPERTY_NAME = "pl_Quantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblQuantity = Quantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property->: LocationFromName----------------------------------
Public Property Get LocationFromName() As String
Const PROPERTY_NAME = "pg_LocationFromName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
LocationFromName = m_strLocationFromName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: LocationFromName----------------------------------
Public Property Let LocationFromName(ByVal LocationFromName As String)
Const PROPERTY_NAME = "pl_LocationFromName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_strLocationFromName = LocationFromName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property->: LocationToName----------------------------------
Public Property Get LocationToName() As String
Const PROPERTY_NAME = "pg_LocationToName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
LocationToName = m_strLocationToName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: LocationToName----------------------------------
Public Property Let LocationToName(ByVal LocationToName As String)
Const PROPERTY_NAME = "pl_LocationToName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_strLocationToName = LocationToName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
