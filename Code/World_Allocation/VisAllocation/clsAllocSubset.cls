VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsAllocSubset"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
'
'   clsAllocSubset
'
'   Version Number - 1.0
'   Last Updated   - 2003-05-06
'   Updated By     - Annalakshmi Stocksdale
'
'   <One-line description of module purpose>
'
'   <Low-level design, physical design discussions, build dependencies,
'   assumptions, implementation issues, notes, etc.>
'
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
Option Explicit

'*****************************************************************************
'CONSTANTS
'*****************************************************************************
#Const DEBUG_MODE = False
Const CLASS_NAME = "clsAllocSubset."

'*****************************************************************************
'ENUMERATIONS
'*****************************************************************************


'*****************************************************************************
'OBJECTS
'*****************************************************************************

'*****************************************************************************
'VARIABLES
'*****************************************************************************

'*****************************************************************************
'PROPERTIES
'*****************************************************************************
Private m_strSubSetName                                         As String
Private m_dblAltTargetQuantity1                                 As Double
Private m_dblAltTargetQuantity2                                 As Double
Private m_dblAltTargetQuantity3                                 As Double
Private m_dblTargetQuantity                                     As Double
Private m_dblAvailableQuantity                                  As Double

'Property->: AvailableQuantity----------------------------------
Public Property Get AvailableQuantity() As Double
Const PROPERTY_NAME = "pg_AvailableQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
AvailableQuantity = m_dblAvailableQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: AvailableQuantity----------------------------------
Public Property Let AvailableQuantity(ByVal AvailableQuantity As Double)
Const PROPERTY_NAME = "pl_AvailableQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblAvailableQuantity = AvailableQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property<-: AltTargetQuantity1----------------------------------
Public Property Let AltTargetQuantity1(ByVal AltTargetQuantity1 As Double)
Const PROPERTY_NAME = "pl_AltTargetQuantity1."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblAltTargetQuantity1 = AltTargetQuantity1
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: AltTargetQuantity2----------------------------------
Public Property Let AltTargetQuantity2(ByVal AltTargetQuantity2 As Double)
Const PROPERTY_NAME = "pl_AltTargetQuantity2."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblAltTargetQuantity2 = AltTargetQuantity2
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'Property<-: AltTargetQuantity3----------------------------------
Public Property Let AltTargetQuantity3(ByVal AltTargetQuantity3 As Double)
Const PROPERTY_NAME = "pl_AltTargetQuantity3."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_dblAltTargetQuantity3 = AltTargetQuantity3
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: TargetQuantity----------------------------------
Public Property Get TargetQuantity() As Double
Const PROPERTY_NAME = "pg_TargetQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
TargetQuantity = m_dblTargetQuantity
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property->: SubsetName----------------------------------
Public Property Get SubSetName() As String
Const PROPERTY_NAME = "pg_SubsetName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
SubSetName = m_strSubSetName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: SubsetName----------------------------------
Public Property Let SubSetName(ByVal SubSetName As String)
Const PROPERTY_NAME = "pl_SubsetName."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_strSubSetName = SubSetName
Exit Property
'Error Handler
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property


'*****************************************************************************
'PUBLIC FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'Sub(): SetQuantity----------------------------------
Public Sub SetQuantity(Target_Quantity As XP_TargetQuantity)
Const SUB_NAME = "s_SetQuantity."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

Dim dblTargetQuantity                   As Double

'Objects
Select Case Target_Quantity
Case XP_AltTargetQuantity1
    dblTargetQuantity = m_dblAltTargetQuantity1
Case XP_AltTargetQuantity2
    dblTargetQuantity = m_dblAltTargetQuantity2
Case XP_AltTargetQuantity3
    dblTargetQuantity = m_dblAltTargetQuantity3
End Select

m_dblTargetQuantity = IIf(g_GreaterThan(dblTargetQuantity, m_dblAvailableQuantity), dblTargetQuantity, 0)
m_dblAvailableQuantity = IIf(g_GreaterThan(dblTargetQuantity, m_dblAvailableQuantity), m_dblAvailableQuantity, 0)

'Variables

Exit Sub
'Error Handler
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'*****************************************************************************
'PRIVATE FUNCTIONS & SUB PROCEDURES
'*****************************************************************************

'*****************************************************************************
'CLASS INIT/TERMINATE
'*****************************************************************************
