VERSION 5.00
Object = "{5E9E78A0-531B-11CF-91F6-C2863C385E30}#1.0#0"; "msflxgrd.ocx"
Begin VB.Form frmMain 
   BackColor       =   &H00FFFFFF&
   BorderStyle     =   1  'Fixed Single
   Caption         =   "Allocation"
   ClientHeight    =   9345
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   12660
   Icon            =   "frmMain.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   9345
   ScaleWidth      =   12660
   StartUpPosition =   3  'Windows Default
   Begin VB.Timer Timer5 
      Enabled         =   0   'False
      Interval        =   600
      Left            =   4650
      Top             =   3510
   End
   Begin VB.Timer Timer4 
      Enabled         =   0   'False
      Interval        =   600
      Left            =   4110
      Top             =   3510
   End
   Begin VB.CommandButton cmd7 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "7"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   11730
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   15
      Top             =   270
      Width           =   735
   End
   Begin VB.CommandButton cmd6 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "6"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   10875
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   14
      Top             =   270
      Width           =   735
   End
   Begin VB.Timer Timer3 
      Enabled         =   0   'False
      Interval        =   250
      Left            =   5190
      Top             =   3510
   End
   Begin VB.Timer Timer2 
      Enabled         =   0   'False
      Interval        =   600
      Left            =   3120
      Top             =   3510
   End
   Begin VB.Timer Timer1 
      Enabled         =   0   'False
      Interval        =   600
      Left            =   3600
      Top             =   3510
   End
   Begin VB.CommandButton cmd5 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "5"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   10020
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   9
      Top             =   270
      Width           =   735
   End
   Begin VB.CommandButton cmd4 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "4"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   9165
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   8
      Top             =   270
      Width           =   735
   End
   Begin VB.CommandButton cmd3 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "3"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   8310
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   270
      Width           =   735
   End
   Begin VB.CommandButton cmd2 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "2"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   7455
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   270
      Width           =   735
   End
   Begin VB.CommandButton cmd1 
      Appearance      =   0  'Flat
      BackColor       =   &H00FFFFFF&
      Caption         =   "1"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   14.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   645
      Left            =   6600
      MaskColor       =   &H8000000D&
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   270
      Width           =   735
   End
   Begin MSFlexGridLib.MSFlexGrid grdDC 
      Height          =   2745
      Left            =   150
      TabIndex        =   1
      Top             =   6270
      Visible         =   0   'False
      Width           =   3975
      _ExtentX        =   7011
      _ExtentY        =   4842
      _Version        =   393216
      Cols            =   1
      FixedCols       =   0
      BackColor       =   8454143
      GridLinesFixed  =   1
      AllowUserResizing=   3
      Appearance      =   0
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin MSFlexGridLib.MSFlexGrid grdAlloc 
      Height          =   2745
      Left            =   4365
      TabIndex        =   2
      Top             =   6270
      Visible         =   0   'False
      Width           =   3975
      _ExtentX        =   7011
      _ExtentY        =   4842
      _Version        =   393216
      Cols            =   1
      FixedCols       =   0
      BackColor       =   8454143
      GridLinesFixed  =   1
      AllowUserResizing=   3
      Appearance      =   0
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin MSFlexGridLib.MSFlexGrid grdOrders 
      Height          =   2745
      Left            =   8580
      TabIndex        =   3
      Top             =   6270
      Visible         =   0   'False
      Width           =   3975
      _ExtentX        =   7011
      _ExtentY        =   4842
      _Version        =   393216
      Cols            =   1
      FixedCols       =   0
      BackColor       =   8454143
      GridLinesFixed  =   1
      Appearance      =   0
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin VB.Image img7 
      Height          =   480
      Left            =   10710
      Picture         =   "frmMain.frx":030A
      Top             =   3000
      Visible         =   0   'False
      Width           =   480
   End
   Begin VB.Image img6 
      Height          =   480
      Left            =   7590
      Picture         =   "frmMain.frx":074C
      Top             =   3810
      Visible         =   0   'False
      Width           =   480
   End
   Begin VB.Label lbl4 
      BackColor       =   &*********&
      Caption         =   "4) Inventory data read from DC and Store"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   2910
      TabIndex        =   13
      Top             =   2910
      Visible         =   0   'False
      Width           =   3645
   End
   Begin VB.Label lbl3 
      BackColor       =   &*********&
      Caption         =   "3) Store Planning Data Read "
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   2910
      TabIndex        =   12
      Top             =   2490
      Visible         =   0   'False
      Width           =   3645
   End
   Begin VB.Label lbl2 
      BackColor       =   &*********&
      Caption         =   "2) Order Sourcing Data Read "
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   285
      Left            =   2910
      TabIndex        =   11
      Top             =   2100
      Visible         =   0   'False
      Width           =   3645
   End
   Begin VB.Label lbl1 
      BackColor       =   &*********&
      Caption         =   "1) Inventory Received at DC"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   315
      Left            =   210
      TabIndex        =   10
      Top             =   2580
      Visible         =   0   'False
      Width           =   2535
   End
   Begin VB.Image img2 
      Height          =   480
      Left            =   4560
      Picture         =   "frmMain.frx":0B8E
      Top             =   3750
      Visible         =   0   'False
      Width           =   480
   End
   Begin VB.Image img1 
      Height          =   480
      Left            =   1260
      Picture         =   "frmMain.frx":0FD0
      Top             =   3030
      Visible         =   0   'False
      Width           =   480
   End
   Begin VB.Image imgArrow 
      Appearance      =   0  'Flat
      Height          =   480
      Left            =   6900
      Picture         =   "frmMain.frx":1412
      Top             =   1110
      Visible         =   0   'False
      Width           =   480
   End
   Begin VB.Label lblDescription 
      BackColor       =   &*********&
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   9.75
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   1755
      Left            =   6630
      TabIndex        =   5
      Top             =   1680
      Width           =   5895
   End
   Begin VB.Image Image5 
      Height          =   1455
      Left            =   6660
      Picture         =   "frmMain.frx":1854
      Top             =   4500
      Width           =   2400
   End
   Begin VB.Image imgAllocation 
      Height          =   1605
      Left            =   3615
      Picture         =   "frmMain.frx":2925
      Top             =   4410
      Width           =   2400
   End
   Begin VB.Image Image3 
      Height          =   2400
      Left            =   9750
      Picture         =   "frmMain.frx":3BC1
      Top             =   3600
      Width           =   2370
   End
   Begin VB.Label Label1 
      Appearance      =   0  'Flat
      BackColor       =   &H80000005&
      Caption         =   "EXE Technologies Allocation System Overview"
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   18
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H80000008&
      Height          =   2325
      Left            =   2940
      TabIndex        =   0
      Top             =   180
      Width           =   3435
   End
   Begin VB.Image Image2 
      Height          =   2265
      Left            =   270
      Picture         =   "frmMain.frx":47B5
      Top             =   180
      Width           =   2400
   End
   Begin VB.Image Image1 
      Height          =   2400
      Left            =   600
      Picture         =   "frmMain.frx":7C26
      Top             =   3600
      Width           =   2220
   End
End
Attribute VB_Name = "frmMain"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private m_recDC As ADOR.Recordset
Private m_recOS As ADOR.Recordset
Private m_recSP As ADOR.Recordset
Private m_recIN As ADOR.Recordset
Private m_recSKU As ADOR.Recordset



Const CST_CONNECT = "SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"

Private Sub cmd1_Click()

    'Move the arrow
    
    imgArrow.Left = cmd1.Left + 350
    imgArrow.Visible = True
    Timer1.Enabled = True
    lbl1.Visible = True

    

    lblDescription.Caption = "Three Distribution centers each receive 25 SKU's varying in quantity between 5 and 20" & _
    vbCrLf & vbCrLf & _
    "DC Dallas 25 SKU's" & vbCrLf & _
        "DC New York 25 SKU's" & vbCrLf & _
            "DC Atlanta 25 SKU's" & vbCrLf
    
    LoadDC

End Sub

Private Sub cmd2_Click()

    Timer1.Enabled = False
    img1.Visible = True
    lbl2.Visible = True

    Timer2.Enabled = True

    imgArrow.Left = cmd2.Left + 350
    imgArrow.Visible = True
    img2.Visible = True


    lblDescription.Caption = "Order sourcing data is loaded from the order sourcing data repository" & _
                            vbCrLf & "This data is used to instruct the allocation module in which order the DC's should replenish a given store" & _
                            vbCrLf & "This same data contains store ranking information instructing the allocation module the order in which stores should be favoured during the allocation process"

    LoadOrderSourcing

End Sub


Private Sub LoadOrderSourcing()

    Dim adoHelper As clsAdoHelper
    Dim strConnect As String
    Dim intCount As Integer
    
    Set adoHelper = New clsAdoHelper

    adoHelper.OpenConnection (CST_CONNECT)

    Set m_recOS = adoHelper.ExecuteProcedure("sp_GetOrderSourcing", Nothing)

    adoHelper.CloseConnection
    
    Set adoHelper = Nothing
    
    'Set the grid props
    grdAlloc.Clear
    
    grdAlloc.Cols = 5
    grdAlloc.Rows = m_recOS.RecordCount + 1
    grdAlloc.TextMatrix(0, 0) = "Store Name"
    grdAlloc.TextMatrix(0, 1) = "Store Rank"
    grdAlloc.TextMatrix(0, 2) = "DC 1"
    grdAlloc.TextMatrix(0, 3) = "DC 2"
    grdAlloc.TextMatrix(0, 4) = "DC 3"
    
    
    grdAlloc.ColWidth(0) = 2200
    
    grdAlloc.ColWidth(2) = 1500
    grdAlloc.ColWidth(3) = 1500
    grdAlloc.ColWidth(4) = 1500
    
    grdAlloc.Visible = True
    intCount = 1
    
    
    Do While m_recOS.EOF = False
    
        grdAlloc.TextMatrix(intCount, 0) = m_recOS("ST_Name")
        grdAlloc.TextMatrix(intCount, 1) = m_recOS("ST_Rank")
        grdAlloc.TextMatrix(intCount, 2) = NeverNull(m_recOS("DC1"))
        grdAlloc.TextMatrix(intCount, 3) = NeverNull(m_recOS("DC2"))
        grdAlloc.TextMatrix(intCount, 4) = NeverNull(m_recOS("DC3"))
            
        intCount = intCount + 1
    
        m_recOS.MoveNext
    Loop

    

End Sub

Private Function NeverNull(p_val As Variant) As Variant

    If IsNull(p_val) = True Then
        NeverNull = ""
    Else
        NeverNull = p_val
    End If

End Function

Private Sub LoadInventoryData()

    Dim adoHelper As clsAdoHelper
    Dim strConnect As String
    Dim intCount As Integer
    
    Set adoHelper = New clsAdoHelper

    adoHelper.OpenConnection (CST_CONNECT)

    Set m_recIN = adoHelper.ExecuteProcedure("sp_getDCSToreInventory", Nothing)

    adoHelper.CloseConnection
    
    Set adoHelper = Nothing
    
    'Set the grid props
    grdAlloc.Clear
    
    grdAlloc.Cols = 3
    grdAlloc.Rows = m_recIN.RecordCount + 1
    grdAlloc.TextMatrix(0, 0) = "SKU"
    grdAlloc.TextMatrix(0, 1) = "Location"
    grdAlloc.TextMatrix(0, 2) = "Quantity"
        
    grdAlloc.ColWidth(0) = 1000
    grdAlloc.ColWidth(1) = 2000
    grdAlloc.ColWidth(2) = 1000
        
        
    grdAlloc.Visible = True
    intCount = 1
    
    
    Do While m_recIN.EOF = False
    
        grdAlloc.TextMatrix(intCount, 0) = m_recIN("PR_Product_SKU")
        grdAlloc.TextMatrix(intCount, 1) = m_recIN("Location")
        grdAlloc.TextMatrix(intCount, 2) = m_recIN("Quantity")
        
           
        intCount = intCount + 1
    
        m_recIN.MoveNext
    Loop

End Sub
Private Sub LoadDC()

    Dim adoHelper As clsAdoHelper
    Dim strConnect As String
    Dim intCount As Integer
    
    Set adoHelper = New clsAdoHelper

    adoHelper.OpenConnection (CST_CONNECT)

    Set m_recDC = adoHelper.ExecuteProcedure("sp_getDCInventory", Nothing)

    adoHelper.CloseConnection
    
    Set adoHelper = Nothing
    
    'Set the grid props
    grdDC.Clear
    
    grdDC.Cols = 5
    grdDC.Rows = m_recDC.RecordCount + 1
    grdDC.TextMatrix(0, 0) = "DC Name"
    grdDC.TextMatrix(0, 1) = "SKU"
    grdDC.TextMatrix(0, 2) = "Description"
    grdDC.TextMatrix(0, 3) = "Quantity"
    grdDC.TextMatrix(0, 4) = "State"
    
    
    grdDC.Visible = True
    intCount = 1
    
    
    Do While m_recDC.EOF = False
    
        grdDC.TextMatrix(intCount, 0) = m_recDC("DC_Name")
        grdDC.TextMatrix(intCount, 1) = m_recDC("PR_Product_SKU")
        grdDC.TextMatrix(intCount, 2) = m_recDC("PR_Description")
        grdDC.TextMatrix(intCount, 3) = m_recDC("IN_Quantity")
        grdDC.TextMatrix(intCount, 4) = m_recDC("IN_State")
            
        intCount = intCount + 1
    
        m_recDC.MoveNext
    Loop

End Sub


Private Sub LoadStorePlanning()

    Dim adoHelper As clsAdoHelper
    Dim strConnect As String
    Dim intCount As Integer
    
    Set adoHelper = New clsAdoHelper

    adoHelper.OpenConnection (CST_CONNECT)

    Set m_recSP = adoHelper.ExecuteProcedure("sp_getStorePlanning", Nothing)

    adoHelper.CloseConnection
    
    Set adoHelper = Nothing
    
    'Set the grid props
    grdAlloc.Clear
    
    grdAlloc.Cols = 3
    grdAlloc.Rows = m_recSP.RecordCount + 1
    grdAlloc.TextMatrix(0, 0) = "Store Name"
    grdAlloc.TextMatrix(0, 1) = "SKU"
    grdAlloc.TextMatrix(0, 2) = "Standard Quantity"
    
    grdAlloc.ColWidth(0) = 2000
        grdAlloc.ColWidth(1) = 1000
            grdAlloc.ColWidth(2) = 1500
    
    grdAlloc.Visible = True
    intCount = 1
    
    
    Do While m_recSP.EOF = False
    
        grdAlloc.TextMatrix(intCount, 0) = m_recSP("ST_Name")
        grdAlloc.TextMatrix(intCount, 1) = m_recSP("PR_Product_SKU")
        grdAlloc.TextMatrix(intCount, 2) = m_recSP("SP_Standard_Quantity")
            
        intCount = intCount + 1
    
        m_recSP.MoveNext
    Loop

End Sub



Private Sub cmd3_Click()

    lbl3.Visible = True


    imgArrow.Left = cmd3.Left + 350
    imgArrow.Visible = True


    lblDescription.Caption = "Store Planning Data is then read from the store planning data repository" & _
                            vbCrLf & "This data contains the standard or target quantity of inventory that each store should have on hand for a given sku"


LoadStorePlanning

End Sub

Private Sub cmd4_Click()

    lbl4.Visible = True


    imgArrow.Left = cmd4.Left + 350
    imgArrow.Visible = True

    lblDescription.Caption = "On hand inventory balance data is then read from the DC in the Available state" & _
                            vbCrLf & "On hand Inventory balance is then read from the store in the Available state"



    LoadInventoryData
    

End Sub


Private Sub Allocate()

    Dim alcAllocate As VisAllocation.clsVisAllocation
    Dim adoHelper As clsAdoHelper
    Dim varParams() As Variant

    Dim recFillLocations As ADOR.Recordset
    Dim recAllocLocations As ADOR.Recordset
    Dim intResultCount As Integer

    Set alcAllocate = New clsVisAllocation
    Set adoHelper = New clsAdoHelper
    adoHelper.OpenConnection (CST_CONNECT)

    Dim alcResultSet As clsAllocResults
    
    Set m_recSKU = adoHelper.ExecuteProcedure("sp_GetSku", Nothing)
    'Set alcAllocate props
    alcAllocate.SetProperties XP_Alloc_Increment_ONE, XP_Alloc_Rounding_Off, XP_Alloc_Less_Than_Zero_On


    'Set the grdAlloc grid
    grdOrders.Clear
    grdOrders.Cols = 4
    intResultCount = 1
    
    grdOrders.TextMatrix(0, 0) = "SKU"
    grdOrders.TextMatrix(0, 1) = "From Location"
    grdOrders.TextMatrix(0, 2) = "To Location"
    grdOrders.TextMatrix(0, 3) = "Quantity"
    grdOrders.Visible = True

    grdOrders.ColWidth(0) = 1000
    grdOrders.ColWidth(1) = 1500
    grdOrders.ColWidth(2) = 1500
    grdOrders.ColWidth(3) = 1000
       

    Do While m_recSKU.EOF = False
    
        ReDim varParams(0)
    
    
        'Get Fill Locations for this sku
        varParams(0) = m_recSKU("PR_Product_Key")
        
        Set recFillLocations = adoHelper.ExecuteProcedure("{call sp_GetFillLocation(?)}", varParams)
        Set recAllocLocations = adoHelper.ExecuteProcedure("{call sp_GetAllocLocation(?)}", varParams)
        
        alcAllocate.Reset
        
        'Fill Locations
        Do While recFillLocations.EOF = False
            alcAllocate.AddFillLocation recFillLocations("DC_Name"), recFillLocations("IN_Quantity")
            recFillLocations.MoveNext
        Loop
    
        'Alloc Locations
        Do While recAllocLocations.EOF = False
            alcAllocate.AddAllocLocation recAllocLocations("ST_NAME"), recAllocLocations("ST_RANK"), recAllocLocations("IN_Quantity"), "NOTSET", recAllocLocations("SP_Standard_Quantity"), 0, 0, recAllocLocations("DC1"), recAllocLocations("DC2"), recAllocLocations("DC3")
            recAllocLocations.MoveNext
        Loop
        
        'Allocate and get the result set
        Set alcResultSet = alcAllocate.Allocate
        
        alcResultSet.FilterZeroResult = True
        
        alcResultSet.SortResults XP_Location_From_Name
        
        alcResultSet.MoveFirst
    
        Do While alcResultSet.EOF = False
        
            grdOrders.Rows = intResultCount + 1
        
            grdOrders.TextMatrix(intResultCount, 0) = m_recSKU("PR_Product_SKU")
            grdOrders.TextMatrix(intResultCount, 1) = alcResultSet.LocationFromName
            grdOrders.TextMatrix(intResultCount, 2) = alcResultSet.LocationToName
            grdOrders.TextMatrix(intResultCount, 3) = alcResultSet.Quantity
        
            
        
            alcResultSet.MoveNext
            
            intResultCount = intResultCount + 1
            
        Loop
    
        DoEvents
    
        m_recSKU.MoveNext
    Loop
    
    If IsObject(recFillLocations) Then
        Set recFillLocations = Nothing
    End If
    If IsObject(recAllocLocations) Then
        Set recAllocLocations = Nothing
    End If

    If IsObject(alcResultSet) Then
        Set alcResultSet = Nothing
    End If
    If IsObject(alcAllocate) Then
        Set alcAllocate = Nothing
    End If

End Sub

Private Sub cmd5_Click()

    Timer3.Enabled = True


    img2.Visible = True

    imgArrow.Left = cmd5.Left + 350
    imgArrow.Visible = True

    lblDescription.Caption = "Inventory data, store planning data, and order sourcing data are read into the allocation module and the allocation process runs"
                            
    Allocate

End Sub

Private Sub cmd6_Click()

    imgArrow.Left = cmd6.Left + 350
    imgArrow.Visible = True

    Timer2.Enabled = False
    img2.Visible = True

    Timer3.Enabled = False
    
    Timer4.Enabled = True
    imgAllocation.Picture = LoadPicture(App.Path & "\allocation.gif")



    lblDescription.Caption = "Orders are generated and available for review"
    

End Sub

Private Sub cmd7_Click()
    
    imgArrow.Left = cmd7.Left + 350
    imgArrow.Visible = True


    
    Timer4.Enabled = False
    img6.Visible = True
    

    Timer5.Enabled = True
    
    lblDescription.Caption = "Inventory is picked at the DC and product is shipped to the stores"

End Sub

Private Sub Form_Unload(Cancel As Integer)

    If IsObject(m_recDC) Then
        Set m_recDC = Nothing
    End If
    If IsObject(m_recOS) Then
        Set m_recOS = Nothing
    End If
    If IsObject(m_recSP) Then
        Set m_recSP = Nothing
    End If
    If IsObject(m_recIN) Then
        Set m_recIN = Nothing
    End If
    If IsObject(m_recSKU) Then
        Set m_recSKU = Nothing
    End If

End Sub

Private Sub Timer1_Timer()
 If img1.Visible = True Then
    img1.Visible = False
 Else
    img1.Visible = True
 End If
    
End Sub

Private Sub Timer2_Timer()

If img2.Visible = True Then
img2.Visible = False
Else
img2.Visible = True
End If

End Sub

Private Sub Timer3_Timer()

    Static intPicCount As Integer
    
    intPicCount = intPicCount + 1
    
    If intPicCount = 7 Then
        intPicCount = 1
    End If
    
    Randomize

    'Find Random index
    intPicCount = Int((6 * Rnd) + 1)

    
    
    imgAllocation.Picture = LoadPicture(CStr(App.Path & "\Allocation" & CStr(intPicCount) & ".gif"))

End Sub

Private Sub Timer4_Timer()

    If img6.Visible = True Then
        img6.Visible = False
    Else
        img6.Visible = True
    End If

End Sub

Private Sub Timer5_Timer()
    If img7.Visible = True Then
        img7.Visible = False
    Else
        img7.Visible = True
    End If
    

End Sub
