
TRUNCATE TABLE store_planning

DECLARE @intProductKey 	int
DECLARE @intQuantity 	INT

SET @intQuantity = 15

DECLARE products_cursor CURSOR FOR 
SELECT PR_Product_Key from Products

OPEN products_cursor 

FETCH NEXT FROM products_cursor 
INTO @intProductKey 

WHILE @@FETCH_STATUS = 0
BEGIN


--1	Macy's New York
	INSERT INTO store_planning(
	SP_Store_Key,
	SP_Sku_Key,
	SP_Standard_Quantity)
	VALUES
	(100,@intProductKey,@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 

--2	<PERSON>'s New Jersey
	INSERT INTO store_planning(
	SP_Store_Key,
	SP_Sku_Key,
	SP_Standard_Quantity)
	VALUES
	(200,@intProductKey,@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 

--3	Bloomingdale's Virginia
	INSERT INTO store_planning(
	SP_Store_Key,
	SP_Sku_Key,
	SP_Standard_Quantity)
	VALUES
	(300,@intProductKey,@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 

--4	Bloomingdale's Maryland 
	INSERT INTO store_planning(
	SP_Store_Key,
	SP_Sku_Key,
	SP_Standard_Quantity)
	VALUES
	(400,@intProductKey,@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 

-- 5	The Bon Marche Idaho
	INSERT INTO store_planning(
	SP_Store_Key,
	SP_Sku_Key,
	SP_Standard_Quantity)
	VALUES
	(500,@intProductKey,@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 

--6	The Bon Marche Washington
	INSERT INTO store_planning(
	SP_Store_Key,
	SP_Sku_Key,
	SP_Standard_Quantity)
	VALUES
	(600,@intProductKey,@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


FETCH NEXT FROM products_cursor 
INTO @intProductKey 
END

DEALLOCATE products_cursor 

