TRUNCATE TABLE order_sourcing

--1	<PERSON>'s New York

Insert into order_sourcing( 
OD_Store_Key,
OD_DC_Key_1,
OD_DC_Key_2,
OD_DC_Key_3)

values

(100,1,3,2)

--2	<PERSON>'s New Jersey
Insert into order_sourcing( 
OD_Store_Key,
OD_DC_Key_1,
OD_DC_Key_2,
OD_DC_Key_3)

values
(200,1,2,3)

--3	Bloomingdale's Virginia

Insert into order_sourcing( 
OD_Store_Key,
OD_DC_Key_1,
OD_DC_Key_2)

values
(300,1,2)


--4	Bloomingdale's Maryland 

Insert into order_sourcing( 
OD_Store_Key,
OD_DC_Key_1,
OD_DC_Key_2)

values
(400,2,1)


--5	The Bon Marche Idaho

Insert into order_sourcing( 
OD_Store_Key,
OD_DC_Key_1,
OD_DC_Key_2)

values
(500,1,3)



--6	The Bon Marche Washington
Insert into order_sourcing( 
OD_Store_Key,
OD_DC_Key_1,
OD_DC_Key_2,
OD_DC_Key_3)

values
(600,1,2,3)


