TRUNCATE TABLE INVENTORY

DECLARE @intProductKey int
DECLARE @intQuantity INT

SET @intQuantity = 5

DECLARE products_cursor CURSOR FOR 
SELECT PR_Product_Key from Products

OPEN products_cursor 

FETCH NEXT FROM products_cursor 
INTO @intProductKey 

WHILE @@FETCH_STATUS = 0
BEGIN

	--DC 1
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(1,@intProductKey,@intQuantity,'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 

	--DC 2
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(2,@intProductKey,@intQuantity,'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


	--DC 3
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(3,@intProductKey,@intQuantity,'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 
	
	
	--1	Macy's New York
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(100,@intProductKey,(@intQuantity-5),'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 



--2	Macy's New Jersey
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(200,@intProductKey,(@intQuantity-5),'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


--3-	Bloomingdale's Virginia
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(300,@intProductKey,(@intQuantity-5),'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 

--4	Bloomingdale's Maryland 
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(400,@intProductKey,(@intQuantity-5),'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 

--5	The Bon Marche Idaho
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(500,@intProductKey,(@intQuantity-5),'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 

--6	The Bon Marche Washington
	INSERT INTO INVENTORY(IN_Location_Key,IN_Product_Key,IN_Quantity,IN_State)

	VALUES 

	(600,@intProductKey,(@intQuantity-5),'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 




FETCH NEXT FROM products_cursor 
INTO @intProductKey 
END

DEALLOCATE products_cursor 

