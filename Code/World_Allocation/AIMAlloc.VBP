Type=Exe
Reference=*\G{00020430-0000-0000-C000-000000000046}#2.0#0#..\..\WINNT\SYSTEM32\STDOLE2.TLB#OLE Automation
Object={4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0; ssdw3bo.ocx
Form=AIM_AllocationMethodsTest.frm
Class=clsAllocation; clsAllocation.cls
Startup="AIM_AllocationMethodsTest"
HelpFile=""
Title="FPtConv"
ExeName32="AIMAlloc.exe"
Command32=""
Name="AIMAlloc"
HelpContextID="0"
Description="Far Point Conversion Utility"
CompatibleMode="0"
MajorVer=1
MinorVer=0
RevisionVer=2
AutoIncrementVer=1
ServerSupportFiles=0
VersionCompanyName="EXE TECHNOLOGIES"
CompilationType=0
OptimizationType=0
FavorPentiumPro(tm)=-1
CodeViewDebugInfo=0
NoAliasing=0
BoundsCheck=0
OverflowCheck=0
FlPointCheck=0
FDIVCheck=0
UnroundedFP=0
StartMode=0
Unattended=0
Retained=0
ThreadPerObject=0
MaxNumberOfThreads=1

[MS Transaction Server]
AutoRefresh=1
