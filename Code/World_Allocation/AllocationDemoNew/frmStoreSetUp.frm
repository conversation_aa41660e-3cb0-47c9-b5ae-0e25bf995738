VERSION 5.00
Object = "{67397AA1-7FB1-11D0-B148-00A0C922E820}#6.0#0"; "Msadodc.ocx"
Object = "{CDE57A40-8B86-11D0-B3C6-00A0C90AEA82}#1.0#0"; "MSDATGRD.OCX"
Object = "{F0D2F211-CCB0-11D0-A316-00AA00688B10}#1.0#0"; "MSDATLST.OCX"
Begin VB.Form frmStoreSetUp 
   Caption         =   "Store Set Up"
   ClientHeight    =   7995
   ClientLeft      =   60
   ClientTop       =   345
   ClientWidth     =   9810
   Icon            =   "frmStoreSetUp.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   7995
   ScaleWidth      =   9810
   Begin MSDataGridLib.DataGrid grdStoreSetup 
      Bindings        =   "frmStoreSetUp.frx":0442
      Height          =   5925
      Left            =   30
      TabIndex        =   0
      Top             =   30
      Width           =   9315
      _ExtentX        =   16431
      _ExtentY        =   10451
      _Version        =   393216
      AllowUpdate     =   -1  'True
      BackColor       =   8454143
      HeadLines       =   1
      RowHeight       =   15
      FormatLocked    =   -1  'True
      AllowAddNew     =   -1  'True
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnCount     =   4
      BeginProperty Column00 
         DataField       =   "SP_Store_Planning_Key"
         Caption         =   "SP_Store_Planning_Key"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column01 
         DataField       =   "SP_Store"
         Caption         =   "Store Name"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column02 
         DataField       =   "SP_Sku"
         Caption         =   "Sku"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column03 
         DataField       =   "SP_Standard_Quantity"
         Caption         =   "Standard Quantity"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      SplitCount      =   1
      BeginProperty Split0 
         BeginProperty Column00 
            Object.Visible         =   0   'False
            ColumnWidth     =   1814.74
         EndProperty
         BeginProperty Column01 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column02 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column03 
            ColumnWidth     =   1695.118
         EndProperty
      EndProperty
   End
   Begin MSAdodcLib.Adodc datStoreSetUp 
      Height          =   360
      Left            =   30
      Top             =   7620
      Width           =   7335
      _ExtentX        =   12938
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   0
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   "sa"
      Password        =   "sql"
      RecordSource    =   "Store_Planning"
      Caption         =   "Store Set Up"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSAdodcLib.Adodc datStore 
      Height          =   525
      Left            =   4230
      Top             =   6900
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Store"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstStore 
      Bindings        =   "frmStoreSetUp.frx":045E
      DataField       =   "SP_Store"
      DataSource      =   "datStoreSetUp"
      Height          =   255
      Left            =   6420
      TabIndex        =   1
      Top             =   6930
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "ST_Code"
      BoundColumn     =   "ST_Code"
   End
   Begin MSAdodcLib.Adodc datSKU 
      Height          =   525
      Left            =   2610
      Top             =   6660
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Products"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstSKU 
      Bindings        =   "frmStoreSetUp.frx":0483
      DataField       =   "SP_Sku"
      DataSource      =   "datStoreSetUp"
      Height          =   255
      Left            =   6090
      TabIndex        =   2
      Top             =   6420
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "PR_Product_SKU"
      BoundColumn     =   "PR_Product_SKU"
   End
End
Attribute VB_Name = "frmStoreSetUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit




Private Sub Form_Resize()

    If (Me.Height > 2000) And (Me.Width > 2000) Then
    
        grdStoreSetup.Width = Me.Width - 200
        datStoreSetUp.Width = Me.Width - 200
        grdStoreSetup.Height = Me.Height - 855
        datStoreSetUp.Top = Me.Height - 780
        
        lstSKU.Visible = False
        lstStore.Visible = False
        
    End If

End Sub



Private Sub grdStoreSetup_ButtonClick(ByVal ColIndex As Integer)

   Select Case ColIndex
    
    Case 1

    lstStore.Top = grdStoreSetup.Top + grdStoreSetup.RowTop(grdStoreSetup.Row) + grdStoreSetup.RowHeight
    lstStore.Left = grdStoreSetup.Left + grdStoreSetup.Columns(ColIndex).Left
    lstStore.Visible = Not lstStore.Visible
    lstStore.ZOrder
    lstStore.Width = grdStoreSetup.Columns(ColIndex).Width
    lstStore.Height = 1440
    
    Case 2
    lstSKU.Top = grdStoreSetup.Top + grdStoreSetup.RowTop(grdStoreSetup.Row) + grdStoreSetup.RowHeight
    lstSKU.Left = grdStoreSetup.Left + grdStoreSetup.Columns(ColIndex).Left
    lstSKU.Visible = Not lstSKU.Visible
    lstSKU.ZOrder
    lstSKU.Width = grdStoreSetup.Columns(ColIndex).Width
    lstSKU.Height = 1440
    
    End Select

End Sub

Private Sub lstSKU_LostFocus()
lstSKU.Visible = False
End Sub

Private Sub lstStore_Click()

grdStoreSetup.Text = lstStore.Text
lstStore.Visible = False
    
End Sub

Private Sub lstSku_Click()

grdStoreSetup.Text = lstSKU.Text
lstSKU.Visible = False

    
End Sub

Private Sub lstStore_LostFocus()
lstStore.Visible = False
End Sub
