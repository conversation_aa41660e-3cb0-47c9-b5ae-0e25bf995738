VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsAdoHelper"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Option Explicit

'--------------------------------------------
'Constants
'--------------------------------------------
    Const CLASS_NAME = "clsAdoHelper."

'--------------------------------------------
'Class Level Variables
'--------------------------------------------
    Private m_conConnection                             As ADODB.Connection
    Private m_lngTimeout                                As Long

'--------------------------------------------
'Properties
'--------------------------------------------

'Property->: Timeout----------------------------------
Public Property Get Timeout() As Long
Const PROPERTY_NAME = "pg_Timeout."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
Timeout = m_lngTimeout
Exit Property
'Error Handler-----------------------
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property

'Property<-: Timeout----------------------------------
Public Property Let Timeout(ByVal Timeout As Long)
Const PROPERTY_NAME = "pl_Timeout."
#If DEBUG_MODE = False Then
   On Error GoTo p_trap:
#End If
m_lngTimeout = Timeout
Exit Property
'Error Handler-----------------------
p_trap:
Err.Raise Err.Number, CLASS_NAME & PROPERTY_NAME & Err.Source, Err.Description
End Property



'--------------------------------------------
'Sub Procedures
'--------------------------------------------

'Sub(): BeginTransaction----------------------------------
Public Sub BeginTransaction()
Const SUB_NAME = "s_BeginTransaction."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
    m_conConnection.BeginTrans
    

Exit Sub
'Error Handler-----------------------
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): RollbackTransaction----------------------------------
Public Sub RollbackTransaction()
Const SUB_NAME = "s_RollbackTransaction."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
    m_conConnection.RollbackTrans

Exit Sub
'Error Handler-----------------------
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): CommitTransaction----------------------------------
Public Sub CommitTransaction()
Const SUB_NAME = "s_CommitTransaction."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables
    m_conConnection.CommitTrans

Exit Sub
'Error Handler-----------------------
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): OpenConnection----------------------------------
Public Sub OpenConnection(ByVal strConnectionString As String)
Const SUB_NAME = "s_OpenConnection."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables

'Process
Set m_conConnection = New ADODB.Connection
    m_conConnection.CursorLocation = adUseClient
    m_conConnection.ConnectionString = strConnectionString
    m_conConnection.Open

Exit Sub
'Error Handler-----------------------
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'Sub(): CloseConnection----------------------------------
Public Sub CloseConnection()
Const SUB_NAME = "s_CloseConnection."
#If DEBUG_MODE = False Then
   On Error GoTo s_trap:
#End If

'Objects

'Variables

'Process
    m_conConnection.Close
Set m_conConnection = Nothing
                

Exit Sub
'Error Handler-----------------------
s_trap:
Err.Raise Err.Number, CLASS_NAME & SUB_NAME & Err.Source, Err.Description
End Sub

'--------------------------------------------
'Functions
'--------------------------------------------

'Function(): ExecuteProcedureNoRecords----------------------------------
Public Function ExecuteProcedureNoRecords(ByVal strSQL As String, ByVal varParams As Variant, _
    ByVal intOutParamIndex As Integer, Optional ByVal intOutParam2Index As Variant, _
    Optional ByRef varOutParam2 As Variant, Optional ByVal intOutParam3Index As Variant, _
    Optional ByRef varOutParam3 As Variant, Optional ByVal intOutParam4Index As Variant, _
    Optional ByRef varOutParam4 As Variant) As Variant
Const FUNCTION_NAME = "f_ExecuteProcedureNoRecords."
#If DEBUG_MODE = False Then
   On Error GoTo f_trap:
#End If

'Objects
Dim cmdCommand      As ADODB.Command

'Variables
Dim intCount        As Integer
Dim varReturn       As Variant

'Process
Set cmdCommand = New ADODB.Command
    cmdCommand.CommandTimeout = m_lngTimeout

Set cmdCommand.ActiveConnection = m_conConnection
        
  '  cmdCommand.CommandType = adCmdStoredProc
    cmdCommand.CommandText = strSQL
    'cmdCommand.Parameters.Refresh
        
        
    If Not IsEmpty(varParams) Then
        For intCount = 0 To cmdCommand.Parameters.Count - 1

                                            ' Handle a second & third arg.
            ' Default to input parameter
            cmdCommand.Parameters(intCount).Direction = adParamInput
            
            If intCount = intOutParamIndex Then
                cmdCommand.Parameters(intCount).Direction = adParamOutput
            End If
            If Not IsMissing(intOutParam2Index) Then
                If intCount = Int(intOutParam2Index) Then
                    cmdCommand.Parameters(intCount).Direction = adParamOutput
                End If
            End If
            If Not IsMissing(intOutParam3Index) Then
                If intCount = Int(intOutParam3Index) Then
                    cmdCommand.Parameters(intCount).Direction = adParamOutput
                End If
            End If
            If Not IsMissing(intOutParam4Index) Then
                If intCount = Int(intOutParam4Index) Then
                    cmdCommand.Parameters(intCount).Direction = adParamOutput
                End If
            End If
            
            cmdCommand.Parameters(intCount).Value = varParams(intCount)
        
        Next
    End If
    
    cmdCommand.Execute , , adExecuteNoRecords
    
    If intOutParamIndex < 0 Then
        varReturn = ""
    ElseIf IsNull(cmdCommand.Parameters(intOutParamIndex).Value) = True Then
        varReturn = "NULL"
    Else
        varReturn = cmdCommand.Parameters(intOutParamIndex).Value
    End If

    ' Return the second arg
    If Not IsMissing(intOutParam2Index) Then
        If IsNull(cmdCommand.Parameters(intOutParam2Index).Value) = True Then
            varOutParam2 = "NULL"
        Else
            varOutParam2 = cmdCommand.Parameters(intOutParam2Index).Value
        End If
    End If

    ' Return the third arg
    If Not IsMissing(intOutParam3Index) Then
        If IsNull(cmdCommand.Parameters(intOutParam3Index).Value) = True Then
            varOutParam3 = "NULL"
        Else
            varOutParam3 = cmdCommand.Parameters(intOutParam3Index).Value
        End If
    End If
    
    ' Return the fourth arg
    If Not IsMissing(intOutParam4Index) Then
        If IsNull(cmdCommand.Parameters(intOutParam4Index).Value) = True Then
            varOutParam4 = "NULL"
        Else
            varOutParam4 = cmdCommand.Parameters(intOutParam4Index).Value
        End If
    End If
    
Set cmdCommand.ActiveConnection = Nothing
Set cmdCommand = Nothing

ExecuteProcedureNoRecords = varReturn

Exit Function
'Error Handler-----------------------
f_trap:
Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function


'Function(): ExecuteProcedure----------------------------------
Public Function ExecuteProcedure(ByVal strSQL As String, ByVal varParams As Variant) As ADODB.Recordset
Const FUNCTION_NAME = "f_ExecuteProcedure."
#If DEBUG_MODE = False Then
   On Error GoTo f_trap:
#End If

'Objects
Dim cmdCommand      As ADODB.Command
Dim recReturn       As ADODB.Recordset

'Variables
Dim intCount        As Integer

'Process
Set cmdCommand = New ADODB.Command
    cmdCommand.CommandTimeout = m_lngTimeout
    
Set cmdCommand.ActiveConnection = m_conConnection
    
    'cmdCommand.CommandType = adCmdStoredProc
    cmdCommand.CommandText = strSQL
    
Set recReturn = New ADODB.Recordset
    recReturn.CursorLocation = adUseClient
    recReturn.CursorType = adOpenForwardOnly
    recReturn.LockType = adLockReadOnly
    
    

    If Not IsEmpty(varParams) Then
        For intCount = 0 To cmdCommand.Parameters.Count - 1
            cmdCommand.Parameters(intCount).Value = varParams(intCount)
        Next
    End If
    
    
    
Set recReturn = cmdCommand.Execute
Set recReturn.ActiveConnection = Nothing
Set cmdCommand.ActiveConnection = Nothing
Set ExecuteProcedure = recReturn
    
Set recReturn = Nothing
Set cmdCommand = Nothing

Exit Function
'Error Handler-----------------------
f_trap:
Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function


'Function(): ExecuteSQL----------------------------------
Public Function ExecuteSQL(ByVal strSQL As String, ByVal varParams As Variant) As Long
Const FUNCTION_NAME = "f_ExecuteSQL."
#If DEBUG_MODE = False Then
   On Error GoTo f_trap:
#End If

'Objects
'Dim cmdCommand      As ADODB.Command
'Dim recReturn       As ADOR.Recordset

'Variables
Dim intCount        As Integer

'Process
'Set cmdCommand = New ADODB.Command
'    cmdCommand.CommandTimeout = m_lngTimeout

'Set cmdCommand.ActiveConnection = m_conConnection

'    cmdCommand.CommandType = adCmdText
'    cmdCommand.CommandText = strSQL

'    If Not IsEmpty(varParams) Then
'        For intCount = 0 To cmdCommand.Parameters.Count - 1
'            cmdCommand.Parameters(intCount).Value = varParams(intCount)
'        Next
'    End If

' Get the rowcount for return from function
'cmdCommand.Execute (intCount)

Call m_conConnection.Execute(strSQL, intCount)

ExecuteSQL = intCount

'Set cmdCommand.ActiveConnection = Nothing

'Set cmdCommand = Nothing

Exit Function
'Error Handler-----------------------
f_trap:
Err.Raise Err.Number, CLASS_NAME & FUNCTION_NAME & Err.Source, Err.Description
End Function

