SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO



/****** Object:  Stored Procedure dbo.sp_GetAllocResultsStoreSummary    Script Date: 11/1/2002 1:21:11 PM ******/

/****** Object:  Stored Procedure dbo.sp_GetAllocResultsStoreSummary    Script Date: 10/31/2002 6:43:55 PM ******/




/****** Object:  Stored Procedure dbo.sp_GetAllocLocation    Script Date: 10/29/2002 6:42:00 PM ******/


ALTER      Procedure sp_GetAllocResultsStoreSummary 


as

SELECT 		ST_Code,
			ST_NAME,
			PR_Product_SKU,
			PR_Description,
			IN_Quantity,
			dbo.CNeverNegative((SP_Standard_Quantity - IN_Quantity)) as SP_Standard_Quantity,
			Sum(AR_Quantity) as 'Alloc_Qty'
			

FROM 			alloc_results
			
		INNER JOIN STORE
		ON AR_To_Location = ST_Code

		INNER JOIN PRODUCTS
		ON AR_Sku = PR_Product_SKU

		INNER JOIN Inventory
		ON IN_Location = ST_CODE
		AND IN_Sku = PR_Product_SKU
		AND IN_State = 'Available'

		INNER JOIN Store_Planning
		ON SP_Store = ST_Code
		AND SP_Sku = PR_Product_SKU



GRoup By 		ST_Code,
			ST_NAME,
			PR_Product_SKU,
			PR_Description,
			IN_Quantity,
			SP_Standard_Quantity

order by ST_Code,PR_Product_SKU


		
















GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

