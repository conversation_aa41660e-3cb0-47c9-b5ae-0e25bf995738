
Set NOCOUNT ON

truncate table alloc_option
truncate table Alloc_Results
truncate table DC
truncate table Inventory
truncate table Order_Sourcing
truncate table Products
truncate table store
truncate table Store_Planning

Insert Into alloc_option(AO_Value) Values ('True')
Insert Into alloc_option(AO_Value) Values ('False')

SET IDENTITY_INSERT DC ON 

Insert Into DC(DC_Distribution_Center_Key,
		DC_Code,
		DC_Name)
		Values
		(1,'DC0001','DC Dallas')

Insert Into DC(DC_Distribution_Center_Key,
		DC_Code,
		DC_Name)

		Values
		(2,'DC0002','DC New York')

Insert Into DC(DC_Distribution_Center_Key,
		DC_Code,
		DC_Name)

		Values
		(3,'DC0003','DC Atlanta')


SET IDENTITY_INSERT DC OFF



INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00001','Pillow')
INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00002','Sheet')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00003','Blanket')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00004','Socks')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00005','Pants')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00006','Shoes')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00007','Watch')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00008','Belt')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00009','Umbrella')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00010','Ball Cap')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00011','Back Pack')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00012','Necklace')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00013','Boots')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00014','Sweater')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00015','VCR')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00016','Television')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00017','Stereo')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00018','DVD Player')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00019','Pajamas')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00020','Skirt')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00021','Blouse')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00022','Sunglasses')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00023','Ring')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00024','Earings')

INSERT INTO products(PR_Product_SKU,PR_Description)
		VALUES
		('00025','Perfume')


Update products SET PR_Allocate = 'True'

SET IDENTITY_INSERT Store ON

Insert Into Store(ST_Store_Key,ST_Code,ST_Name) 
VALUES (100,'ST0001','Macy''s New York') 

Insert Into Store(ST_Store_Key,ST_Code,ST_Name) 
VALUES (200,'ST0002','Macy''s New Jersey') 

Insert Into Store(ST_Store_Key,ST_Code,ST_Name) 
VALUES (300,'ST0003','Bloomingdale''s Virginia') 

Insert Into Store(ST_Store_Key,ST_Code,ST_Name) 
VALUES (400,'ST0004','Bloomingdale''s Maryland ') 

Insert Into Store(ST_Store_Key,ST_Code,ST_Name) 
VALUES (500,'ST0005','The Bon Marche Idaho') 

Insert Into Store(ST_Store_Key,ST_Code,ST_Name) 
VALUES (600,'ST0006','The Bon Marche Washington') 

SET IDENTITY_INSERT Store OFF




DECLARE @vcrProductSKU	varchar(50)
DECLARE @intQuantity 	INT

SET @intQuantity = 15

DECLARE products_cursor CURSOR FOR 
SELECT PR_Product_Sku from Products

OPEN products_cursor 

FETCH NEXT FROM products_cursor 
INTO @vcrProductSKU	

WHILE @@FETCH_STATUS = 0
BEGIN




	INSERT INTO store_planning(
	SP_Store,
	SP_Sku,
	SP_Standard_Quantity)
	
	VALUES

	('ST0001',
	@vcrProductSKU,
	@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


	INSERT INTO store_planning(
	SP_Store,
	SP_Sku,
	SP_Standard_Quantity)
	
	VALUES

	('ST0002',
	@vcrProductSKU,
	@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


	INSERT INTO store_planning(
	SP_Store,
	SP_Sku,
	SP_Standard_Quantity)
	
	VALUES

	('ST0003',
	@vcrProductSKU,
	@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


	INSERT INTO store_planning(
	SP_Store,
	SP_Sku,
	SP_Standard_Quantity)
	
	VALUES

	('ST0004',
	@vcrProductSKU,
	@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


	INSERT INTO store_planning(
	SP_Store,
	SP_Sku,
	SP_Standard_Quantity)
	
	VALUES

	('ST0005',
	@vcrProductSKU,
	@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


	INSERT INTO store_planning(
	SP_Store,
	SP_Sku,
	SP_Standard_Quantity)
	
	VALUES

	('ST0006',
	@vcrProductSKU,
	@intQuantity)

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 15
	END 


FETCH NEXT FROM products_cursor 
INTO @vcrProductSKU	

END

DEALLOCATE products_cursor 



DECLARE @vcrSku varchar(50)


SET @intQuantity = 5

DECLARE products_cursor CURSOR FOR 
SELECT PR_Product_Sku from Products

OPEN products_cursor 

FETCH NEXT FROM products_cursor 
INTO @vcrSku 

WHILE @@FETCH_STATUS = 0
BEGIN



Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('DC0001',
@vcrSku, 
@intQuantity,
'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('DC0002',
@vcrSku, 
@intQuantity,
'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('DC0003',
@vcrSku, 
@intQuantity,
'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('ST0001',
@vcrSku, 
@intQuantity-5,
'Available')

	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 



Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('ST0002',
@vcrSku, 
@intQuantity-5,
'Available')


SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('ST0003',
@vcrSku, 
@intQuantity-5,
'Available')

SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 



Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('ST0004',
@vcrSku, 
@intQuantity-5,
'Available')


SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 


Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('ST0005',
@vcrSku, 
@intQuantity-5,
'Available')


SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 



Insert into inventory(
IN_Location,
IN_Sku,
IN_Quantity,
IN_State)
Values
('ST0006',
@vcrSku, 
@intQuantity-5,
'Available')





	SET @intQuantity = @intQuantity + 1
	IF @intQuantity = 20 
	BEGIN 
		SET @intQuantity = 5
	END 



FETCH NEXT FROM products_cursor 
INTO @vcrSku
END

DEALLOCATE products_cursor 



Insert Into Inventory_Status
(IS_Inventory_Status)
Values
('Available')

Insert Into Inventory_Status
(IS_Inventory_Status)
Values
('Pending')

Insert Into Inventory_Status
(IS_Inventory_Status)
Values
('Committed')



--1	Macy's New York


Insert into order_sourcing( 
OD_Store,
OD_Rank,
OD_DC_1,
OD_DC_2,
OD_DC_3)

values

('ST0001',
1,
'DC0001',
'DC0002',
'DC0003')




--2	Macy's New Jersey
Insert into order_sourcing( 
OD_Store,
OD_Rank,
OD_DC_1,
OD_DC_2,
OD_DC_3)

values

('ST0002',
2,
'DC0002',
'DC0003',
'DC0001')


--3	Bloomingdale's Virginia

Insert into order_sourcing( 
OD_Store,
OD_Rank,
OD_DC_1,
OD_DC_2,
OD_DC_3)

values

('ST0003',
1,
'DC0002',
'DC0003',
'DC0001')


--4	Bloomingdale's Maryland 

Insert into order_sourcing( 
OD_Store,
OD_Rank,
OD_DC_1,
OD_DC_2,
OD_DC_3)

values

('ST0004',
2,
'DC0002',
'DC0003',
'DC0001')



--5	The Bon Marche Idaho


Insert into order_sourcing( 
OD_Store,
OD_Rank,
OD_DC_1,
OD_DC_2,
OD_DC_3)

values

('ST0005',
1,
'DC0003',
'DC0001',
'DC0002')





--6	The Bon Marche Washington
Insert into order_sourcing( 
OD_Store,
OD_Rank,
OD_DC_1,
OD_DC_2,
OD_DC_3)

values

('ST0006',
3,
'DC0001',
'DC0002',
'DC0003')



