VERSION 5.00
Begin VB.Form frmSQL 
   Appearance      =   0  'Flat
   BackColor       =   &H80000005&
   ClientHeight    =   7725
   ClientLeft      =   60
   ClientTop       =   345
   ClientWidth     =   7860
   Icon            =   "frmSQL.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   7725
   ScaleWidth      =   7860
   Begin VB.CommandButton cmdOk 
      Appearance      =   0  'Flat
      BackColor       =   &H80000009&
      Caption         =   "&Ok"
      Height          =   495
      Left            =   6330
      Style           =   1  'Graphical
      TabIndex        =   1
      Top             =   7170
      Width           =   1485
   End
   Begin VB.TextBox txtSQL 
      Appearance      =   0  'Flat
      BackColor       =   &H80000007&
      BeginProperty Font 
         Name            =   "Tahoma"
         Size            =   12
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H00FFFFFF&
      Height          =   7065
      Left            =   30
      MultiLine       =   -1  'True
      ScrollBars      =   2  'Vertical
      TabIndex        =   0
      Top             =   30
      Width           =   7785
   End
End
Attribute VB_Name = "frmSQL"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdOK_Click()

    Unload Me

End Sub

Private Sub Form_Resize()


    If (Me.Height > 2000) And (Me.Width > 2000) Then

        txtSQL.Height = Me.Height - 1065
        txtSQL.Width = Me.Width - 200
    
        cmdOK.Top = Me.Height - 960
        cmdOK.Left = Me.Width - 1650
    
    End If

End Sub


Public Property Let ProcName(ByVal p_strProcName As String)

    Dim adoHelper As clsAdoHelper
    Dim rstRecordSet As ADOR.Recordset
    Dim strProcText As String
    
    Set adoHelper = New clsAdoHelper
    
    adoHelper.OpenConnection (CST_CONNECT)
    
    Set rstRecordSet = adoHelper.ExecuteProcedure("sp_helptext " & p_strProcName, Nothing)
    
    Do While rstRecordSet.EOF = False
        
        strProcText = strProcText & rstRecordSet.Fields(0).Value & vbCrLf
        
        rstRecordSet.MoveNext
    Loop
        
    Set rstRecordSet = Nothing
    
    adoHelper.CloseConnection
    
    Set adoHelper = Nothing

    Me.Caption = "Procedure: " & p_strProcName
    txtSQL.Text = strProcText

End Property
