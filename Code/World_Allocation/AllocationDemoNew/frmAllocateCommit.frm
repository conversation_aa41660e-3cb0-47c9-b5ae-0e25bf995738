VERSION 5.00
Object = "{67397AA1-7FB1-11D0-B148-00A0C922E820}#6.0#0"; "Msadodc.ocx"
Object = "{CDE57A40-8B86-11D0-B3C6-00A0C90AEA82}#1.0#0"; "MSDATGRD.OCX"
Object = "{F0D2F211-CCB0-11D0-A316-00AA00688B10}#1.0#0"; "MSDATLST.OCX"
Begin VB.Form frmAllocateCommit 
   Appearance      =   0  'Flat
   BackColor       =   &H80000005&
   Caption         =   "Commit Allocation"
   ClientHeight    =   7875
   ClientLeft      =   60
   ClientTop       =   345
   ClientWidth     =   7515
   Icon            =   "frmAllocateCommit.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   7875
   ScaleWidth      =   7515
   Begin VB.CommandButton cmdCommitAllocation 
      BackColor       =   &H80000009&
      Caption         =   "&Commit Allocation"
      Height          =   360
      Left            =   2490
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   7500
      Width           =   1635
   End
   Begin MSDataListLib.DataList lstOption 
      Bindings        =   "frmAllocateCommit.frx":0442
      DataField       =   "AR_Commit"
      DataSource      =   "datCommitAllocation"
      Height          =   450
      Left            =   2940
      TabIndex        =   3
      Top             =   6420
      Visible         =   0   'False
      Width           =   525
      _ExtentX        =   926
      _ExtentY        =   794
      _Version        =   393216
      Appearance      =   0
      ListField       =   "AO_Value"
   End
   Begin MSAdodcLib.Adodc Adodc1 
      Height          =   360
      Left            =   510
      Top             =   6900
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Alloc_Option"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin VB.CommandButton cmdCommitNone 
      BackColor       =   &H80000009&
      Caption         =   "Commit &None"
      Height          =   360
      Left            =   5850
      Style           =   1  'Graphical
      TabIndex        =   2
      Top             =   7500
      Width           =   1635
   End
   Begin VB.CommandButton cmdCommitAll 
      BackColor       =   &H80000009&
      Caption         =   "Commit &All"
      Height          =   360
      Left            =   4170
      Style           =   1  'Graphical
      TabIndex        =   1
      Top             =   7500
      Width           =   1635
   End
   Begin MSAdodcLib.Adodc datCommitAllocation 
      Height          =   360
      Left            =   30
      Top             =   7500
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   0
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   "sa"
      Password        =   "sql"
      RecordSource    =   "Alloc_Results"
      Caption         =   "Commit Allocation"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataGridLib.DataGrid grdCommit 
      Bindings        =   "frmAllocateCommit.frx":0457
      Height          =   5775
      Left            =   30
      TabIndex        =   0
      Top             =   30
      Width           =   7455
      _ExtentX        =   13150
      _ExtentY        =   10186
      _Version        =   393216
      AllowUpdate     =   -1  'True
      BackColor       =   8454143
      HeadLines       =   1
      RowHeight       =   15
      FormatLocked    =   -1  'True
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnCount     =   6
      BeginProperty Column00 
         DataField       =   "AR_Alloc_Results_Key"
         Caption         =   "AR_Alloc_Results_Key"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column01 
         DataField       =   "AR_From_Location"
         Caption         =   "DC Code"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column02 
         DataField       =   "AR_To_Location"
         Caption         =   "Store Code"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column03 
         DataField       =   "AR_Quantity"
         Caption         =   "Allocation Quantity"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column04 
         DataField       =   "AR_Sku"
         Caption         =   "SKU"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column05 
         DataField       =   "AR_Commit"
         Caption         =   "Commit "
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      SplitCount      =   1
      BeginProperty Split0 
         BeginProperty Column00 
            Object.Visible         =   0   'False
            ColumnWidth     =   1709.858
         EndProperty
         BeginProperty Column01 
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column02 
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column03 
            ColumnWidth     =   975.118
         EndProperty
         BeginProperty Column04 
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column05 
            Button          =   -1  'True
            ColumnWidth     =   1140.095
         EndProperty
      EndProperty
   End
End
Attribute VB_Name = "frmAllocateCommit"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdCommitAllocation_Click()


    
    Dim recResults As ADOR.Recordset
    Dim recSave As ADOR.Recordset
    Dim adoHelper As clsAdoHelper
    Dim varParams(3) As Variant
    Dim intResponse As Integer
    


    intResponse = MsgBox("This operation will commit the allocation and permanently affect inventory. Continue ?", vbYesNo, "Filter ...")

    If intResponse = vbYes Then

    Set adoHelper = New clsAdoHelper
    adoHelper.OpenConnection (CST_CONNECT)

    Set recResults = adoHelper.ExecuteProcedure("sp_GetAllocationRowsToCommit", Nothing)
    
    Do While recResults.EOF = False
    
        
        varParams(0) = recResults("AR_From_Location")
        varParams(1) = recResults("AR_To_Location")
        varParams(2) = recResults("AR_Quantity")
        varParams(3) = recResults("AR_Sku")
    
        Debug.Print varParams(0)
        Debug.Print varParams(1)
        Debug.Print varParams(2)
        Debug.Print varParams(3)
        
    
        Set recSave = adoHelper.ExecuteProcedure("{call sp_CommitAllocation(?,?,?,?)}", varParams)
         
        recResults.MoveNext
    Loop
        
    
    adoHelper.CloseConnection

    If IsObject(recSave) = True Then
        Set recSave = Nothing
    End If
    If IsObject(recResults) = True Then
        Set recResults = Nothing
    End If
    If IsObject(adoHelper) = True Then
        Set adoHelper = Nothing
    End If


    MsgBox "The allocation is now comitted." & vbCrLf & "Please check the Reports to view the results.", vbInformation, "Allocation Commit Complete ..."

    End If

End Sub

Private Sub lstOption_Click()



    grdCommit.Text = lstOption.Text
    lstOption.Visible = False

End Sub

Private Sub lstOption_LostFocus()

    lstOption.Visible = False

End Sub

Private Sub cmdCommitAll_Click()

    
    Dim lngRowCount As Long
    Dim adoRecSet As ADOR.Recordset
    Dim lngCount As Long
    
    Set adoRecSet = datCommitAllocation.Recordset
    adoRecSet.MoveFirst
    lngRowCount = adoRecSet.RecordCount
    Set adoRecSet = Nothing

    grdCommit.Row = 0


    For lngCount = 0 To (lngRowCount - 1)
        grdCommit.Col = 5
        grdCommit.Text = "True"
        grdCommit.Scroll 0, 1
        DoEvents
        If (lngCount <> lngRowCount - 1) Then
        grdCommit.Row = grdCommit.Row + 1
        End If
    Next
    
    datCommitAllocation.Recordset.UpdateBatch
    
End Sub

Private Sub cmdCommitNone_Click()


    
    Dim lngRowCount As Long
    Dim adoRecSet As ADOR.Recordset
    Dim lngCount As Long
    
    Set adoRecSet = datCommitAllocation.Recordset
    adoRecSet.MoveFirst
    lngRowCount = adoRecSet.RecordCount
    Set adoRecSet = Nothing

    grdCommit.Row = 0


    For lngCount = 0 To (lngRowCount - 1)
        grdCommit.Col = 5
        grdCommit.Text = "False"
        grdCommit.Scroll 0, 1
        DoEvents
        If (lngCount <> lngRowCount - 1) Then
        grdCommit.Row = grdCommit.Row + 1
        End If
    Next
    
    datCommitAllocation.Recordset.UpdateBatch

End Sub

Private Sub Form_Resize()
 If (Me.Height > 2000) And (Me.Width > 6000) Then
    
        grdCommit.Width = Me.Width - 200
        datCommitAllocation.Width = Me.Width - 5220
        grdCommit.Height = Me.Height - 855
        datCommitAllocation.Top = Me.Height - 780
        
        cmdCommitAll.Top = Me.Height - 780
        cmdCommitNone.Top = Me.Height - 780
       cmdCommitAllocation.Top = Me.Height - 780
       
        cmdCommitAll.Left = Me.Width - 3465
        cmdCommitNone.Left = Me.Width - 1785
        cmdCommitAllocation.Left = Me.Width - 5145
       
    End If
End Sub

Private Sub grdCommit_ButtonClick(ByVal ColIndex As Integer)

If ColIndex = 5 Then

    lstOption.Top = grdCommit.Top + grdCommit.RowTop(grdCommit.Row) + grdCommit.RowHeight
    lstOption.Left = grdCommit.Left + grdCommit.Columns(ColIndex).Left
    lstOption.Visible = Not lstOption.Visible
    lstOption.ZOrder
    lstOption.Width = grdCommit.Columns(ColIndex).Width
    lstOption.Height = 500

End If

End Sub

