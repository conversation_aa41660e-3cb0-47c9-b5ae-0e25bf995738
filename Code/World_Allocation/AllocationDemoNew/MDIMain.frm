VERSION 5.00
Begin VB.MDIForm MDIMain 
   BackColor       =   &H8000000C&
   Caption         =   "Allocation Manager"
   ClientHeight    =   5295
   ClientLeft      =   165
   ClientTop       =   165
   ClientWidth     =   8655
   Icon            =   "MDIMain.frx":0000
   LinkTopic       =   "MDIForm1"
   StartUpPosition =   2  'CenterScreen
   WindowState     =   2  'Maximized
   Begin VB.Menu mnuFile 
      Caption         =   "&File"
      Begin VB.Menu mnuAllocateSku 
         Caption         =   "&Allocate Sku"
      End
      Begin VB.Menu mnuCommit 
         Caption         =   "&Commit Allocation"
      End
      Begin VB.Menu mnuResetDatabase 
         Caption         =   "&Reset Database"
      End
      Begin VB.Menu mnuExit 
         Caption         =   "&Exit"
      End
   End
   Begin VB.Menu mnuTableMaintenance 
      Caption         =   "&Table Maintenance"
      Begin VB.Menu mnuDistributionCenter 
         Caption         =   "S&ourcing Options"
      End
      Begin VB.Menu mnuStoreS 
         Caption         =   "&Stores"
      End
      Begin VB.Menu mnuProducts 
         Caption         =   "&Products"
      End
   End
   Begin VB.Menu mnuAllocationSetup 
      Caption         =   "Allocation &Setup"
      Begin VB.Menu mnuStoreSetup 
         Caption         =   "Store Set&up"
      End
      Begin VB.Menu mnuLocationPriority 
         Caption         =   "&Location Priority"
      End
      Begin VB.Menu mnuOnHand 
         Caption         =   "&On Hand Inventory"
      End
   End
   Begin VB.Menu mnuAllocationLogic 
      Caption         =   "&Allocation Logic"
      Begin VB.Menu mnusp_GetAllocLocation 
         Caption         =   "Get Allocation &Locations"
      End
      Begin VB.Menu mnuGetFillLocations 
         Caption         =   "Get &Fill Locations"
      End
      Begin VB.Menu mnuGetSku 
         Caption         =   "Get &Skus"
      End
      Begin VB.Menu mnuGetRows 
         Caption         =   "Get Allocation &Rows To Commit"
      End
      Begin VB.Menu mnuCommitSP 
         Caption         =   "&Commit Allocation"
      End
   End
   Begin VB.Menu mnuReports 
      Caption         =   "&Reports"
      Begin VB.Menu mnuResultsDetail 
         Caption         =   "Results &Detail"
      End
      Begin VB.Menu mnuResultsItemSummary 
         Caption         =   "Results Item &Summary"
      End
      Begin VB.Menu mnuResultsStoreSummary 
         Caption         =   "Results St&ore Summary"
      End
      Begin VB.Menu mnuUnfulfilled 
         Caption         =   "Results Store Summary &Unfulfilled"
      End
   End
   Begin VB.Menu mnuWindow 
      Caption         =   "&Window"
      Begin VB.Menu mnuHorizontal 
         Caption         =   "Tile &Horizontally"
      End
      Begin VB.Menu mnuVertical 
         Caption         =   "Tile &Vertically"
      End
      Begin VB.Menu mnuCascade 
         Caption         =   "&Cascade"
      End
      Begin VB.Menu mnuArrange 
         Caption         =   "&Arrange Icons"
      End
   End
   Begin VB.Menu mnuHelp 
      Caption         =   "&Help"
      Begin VB.Menu mnuDemo 
         Caption         =   "&Demo"
      End
      Begin VB.Menu mnuAbout 
         Caption         =   "&About"
      End
   End
End
Attribute VB_Name = "MDIMain"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit



Private Sub mnuAbout_Click()

    Load frmAbout
    frmAbout.Show
    frmAbout.ZOrder

End Sub

Private Sub mnuAllocateSku_Click()

    Load frmAllocateSku
    frmAllocateSku.Show
    frmAllocateSku.ZOrder
    
    
End Sub

Private Sub mnuArrange_Click()

    Me.Arrange 3

End Sub

Private Sub mnuCascade_Click()
    
    Me.Arrange 0

End Sub

Private Sub mnuCommit_Click()

    Load frmAllocateCommit
    frmAllocateCommit.Show
    frmAllocateCommit.ZOrder

End Sub

Private Sub mnuCommitSP_Click()

    Load frmSQL
    frmSQL.Show
    frmSQL.ZOrder
    frmSQL.ProcName = "sp_CommitAllocation"
    
End Sub

Private Sub mnuDemo_Click()

    Load frmMain
    frmMain.Show
    frmMain.ZOrder

End Sub

Private Sub mnuDistributionCenter_Click()

    Load frmDC
    frmDC.Show
    frmDC.ZOrder

End Sub

Private Sub mnuExit_Click()
    
    Unload Me

End Sub

Private Sub mnuGetFillLocations_Click()

    Load frmSQL
    frmSQL.Show
    frmSQL.ZOrder
    frmSQL.ProcName = "sp_GetFillLocation"

End Sub

Private Sub mnuGetRows_Click()

        Load frmSQL
    frmSQL.Show
    frmSQL.ZOrder
    frmSQL.ProcName = "sp_GetAllocationRowsToCommit"


End Sub

Private Sub mnuGetSku_Click()

    Load frmSQL
    frmSQL.Show
    frmSQL.ZOrder
    frmSQL.ProcName = "sp_GetAllocSku"

End Sub

Private Sub mnuHorizontal_Click()

    Me.Arrange 1

End Sub

Private Sub mnuLocationPriority_Click()

    Load frmLocationPriority
    frmLocationPriority.Show
    frmLocationPriority.ZOrder

End Sub

Private Sub mnuOnHand_Click()

    Load frmInventory
    frmInventory.Show
    frmInventory.ZOrder
    
End Sub

Private Sub mnuProducts_Click()

    Load frmProducts
    frmProducts.Show
    frmProducts.ZOrder

End Sub

Private Sub mnuResetDatabase_Click()

    Dim intResponse As Integer
    Dim strCmd
    
    strCmd = "isql -S localhost -d alloc_demo -U sa -P sql -i " & App.Path & "\all_data.sql"
    
    intResponse = MsgBox("This will completely reset the database." & vbCrLf & "All user defined data will be permanently lost." & vbCrLf & "Continue ?", vbYesNo, "Reset Database ...")
    
    If intResponse = vbYes Then
    
        Shell strCmd, vbMinimizedNoFocus
    
    End If

End Sub

Private Sub mnuResultsDetail_Click()

    Load frmAllocResultsDetail
    frmAllocResultsDetail.Show
    frmAllocResultsDetail.ZOrder
    
End Sub

Private Sub mnuResultsItemSummary_Click()

    Load frmAllocResultsItemSummary
    frmAllocResultsItemSummary.Show
    frmAllocResultsItemSummary.ZOrder

End Sub

Private Sub mnuResultsStoreSummary_Click()

    Load frmAllocResultsStoreSummary
    frmAllocResultsStoreSummary.Show
    frmAllocResultsStoreSummary.ZOrder

End Sub

Private Sub mnusp_GetAllocLocation_Click()

    Load frmSQL
    frmSQL.Show
    frmSQL.ZOrder
    frmSQL.ProcName = "sp_GetAllocLocation"

End Sub

Private Sub mnuStores_Click()

    Load frmStore
    frmStore.Show
    frmStore.ZOrder

End Sub

Private Sub mnuStoreSetup_Click()

    Load frmStoreSetUp
    frmStoreSetUp.Show
    frmStoreSetUp.ZOrder

End Sub

Private Sub mnuUnfulfilled_Click()

    Load frmAllocResultsStoreSummaryUnFullfilled
    frmAllocResultsStoreSummaryUnFullfilled.Show
    frmAllocResultsStoreSummaryUnFullfilled.ZOrder

End Sub

Private Sub mnuVertical_Click()

    Me.Arrange 2

End Sub
