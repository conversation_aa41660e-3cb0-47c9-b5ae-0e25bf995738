VERSION 5.00
Object = "{67397AA1-7FB1-11D0-B148-00A0C922E820}#6.0#0"; "Msadodc.ocx"
Object = "{CDE57A40-8B86-11D0-B3C6-00A0C90AEA82}#1.0#0"; "MSDATGRD.OCX"
Object = "{F0D2F211-CCB0-11D0-A316-00AA00688B10}#1.0#0"; "MSDATLST.OCX"
Begin VB.Form frmAllocateSku 
   Appearance      =   0  'Flat
   BackColor       =   &H80000005&
   Caption         =   "Allocate Skus"
   ClientHeight    =   7875
   ClientLeft      =   60
   ClientTop       =   345
   ClientWidth     =   7515
   Icon            =   "frmAllocateSku.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   7875
   ScaleWidth      =   7515
   Begin VB.CommandButton cmdRunAllocation 
      BackColor       =   &H80000009&
      Caption         =   "&Run Allocation"
      Height          =   360
      Left            =   2490
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   7500
      Width           =   1635
   End
   Begin MSDataListLib.DataList lstOption 
      Bindings        =   "frmAllocateSku.frx":0442
      DataField       =   "PR_Allocate"
      DataSource      =   "datSku"
      Height          =   450
      Left            =   2940
      TabIndex        =   3
      Top             =   6420
      Visible         =   0   'False
      Width           =   525
      _ExtentX        =   926
      _ExtentY        =   794
      _Version        =   393216
      Appearance      =   0
      ListField       =   "AO_Value"
   End
   Begin MSAdodcLib.Adodc Adodc1 
      Height          =   360
      Left            =   510
      Top             =   6900
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Alloc_Option"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin VB.CommandButton cmdAllocateNone 
      BackColor       =   &H80000009&
      Caption         =   "Allocate &None"
      Height          =   360
      Left            =   5850
      Style           =   1  'Graphical
      TabIndex        =   2
      Top             =   7500
      Width           =   1635
   End
   Begin VB.CommandButton cmdAllocateAll 
      BackColor       =   &H80000009&
      Caption         =   "Allocate &All"
      Height          =   360
      Left            =   4170
      Style           =   1  'Graphical
      TabIndex        =   1
      Top             =   7500
      Width           =   1635
   End
   Begin MSAdodcLib.Adodc datSku 
      Height          =   360
      Left            =   30
      Top             =   7500
      Width           =   2415
      _ExtentX        =   4260
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   0
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   "sa"
      Password        =   "sql"
      RecordSource    =   "Products"
      Caption         =   "Allocate Sku"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataGridLib.DataGrid grdSku 
      Bindings        =   "frmAllocateSku.frx":0457
      Height          =   5775
      Left            =   30
      TabIndex        =   0
      Top             =   30
      Width           =   7455
      _ExtentX        =   13150
      _ExtentY        =   10186
      _Version        =   393216
      AllowUpdate     =   -1  'True
      BackColor       =   8454143
      HeadLines       =   1
      RowHeight       =   15
      FormatLocked    =   -1  'True
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnCount     =   4
      BeginProperty Column00 
         DataField       =   "PR_Product_Key"
         Caption         =   "PR_Product_Key"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column01 
         DataField       =   "PR_Product_SKU"
         Caption         =   "Sku"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column02 
         DataField       =   "PR_Description"
         Caption         =   "Description"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column03 
         DataField       =   "PR_Allocate"
         Caption         =   "Allocate"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      SplitCount      =   1
      BeginProperty Split0 
         BeginProperty Column00 
            Object.Visible         =   0   'False
            ColumnWidth     =   1305.071
         EndProperty
         BeginProperty Column01 
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column02 
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column03 
            Button          =   -1  'True
            ColumnWidth     =   959.811
         EndProperty
      EndProperty
   End
End
Attribute VB_Name = "frmAllocateSku"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdRunAllocation_Click()


    Dim alcAllocate As VisAllocation.clsVisAllocation
    Dim adoHelper As clsAdoHelper
    Dim varParams() As Variant
    Dim varParams2(3) As Variant
    Dim lngResult As Long

    Dim recFillLocations As ADOR.Recordset
    Dim recAllocLocations As ADOR.Recordset
    Dim recSave As ADOR.Recordset
    Dim intResultCount As Integer

    Dim blnHasFillLocation As Boolean
    Dim blnHasAllocLocation As Boolean

    Dim m_recSKU As ADOR.Recordset
    Dim intResponse As Integer

    Set alcAllocate = New clsVisAllocation
    Set adoHelper = New clsAdoHelper
    adoHelper.OpenConnection (CST_CONNECT)


    intResponse = MsgBox("Would you like to filter out results where the allocated quantity is zero ?", vbYesNo, "Filter ...")

    'Clear the results
  Set recFillLocations = adoHelper.ExecuteProcedure("sp_ClearAllocResults", Nothing)

    Dim alcResultSet As clsAllocResults
    
    Set m_recSKU = adoHelper.ExecuteProcedure("sp_GetAllocSku", Nothing)
    'Set alcAllocate props
    alcAllocate.SetProperties XP_Alloc_Increment_ONE, XP_Alloc_Rounding_Off, XP_Alloc_Less_Than_Zero_On

        Do While m_recSKU.EOF = False
    
        ReDim varParams(0)
    
        blnHasFillLocation = False
        blnHasAllocLocation = False
    
        'Get Fill Locations for this sku
        varParams(0) = m_recSKU("PR_Product_SKU")
        
        Set recFillLocations = adoHelper.ExecuteProcedure("{call sp_GetFillLocation(?)}", varParams)
        Set recAllocLocations = adoHelper.ExecuteProcedure("{call sp_GetAllocLocation(?)}", varParams)
        
        alcAllocate.Reset
        
        'Fill Locations
        Do While recFillLocations.EOF = False
            alcAllocate.AddFillLocation recFillLocations("DC_Code"), recFillLocations("IN_Quantity")
            recFillLocations.MoveNext
            blnHasFillLocation = True
    
        Loop
    
        'Alloc Locations
        Do While recAllocLocations.EOF = False
            alcAllocate.AddAllocLocation recAllocLocations("ST_Code"), recAllocLocations("OD_Rank"), recAllocLocations("IN_Quantity"), "NOTSET", recAllocLocations("SP_Standard_Quantity"), 0, 0, recAllocLocations("DC1"), recAllocLocations("DC2"), recAllocLocations("DC3")
            recAllocLocations.MoveNext
            blnHasAllocLocation = True
        Loop
        
        If blnHasFillLocation = True And blnHasAllocLocation = True Then
        
        'Allocate and get the result set
        Set alcResultSet = alcAllocate.Allocate
        
        If intResponse = vbYes Then
            alcResultSet.FilterZeroResult = True
        End If
        
        alcResultSet.SortResults XP_Location_From_Name
        
        alcResultSet.MoveFirst
    
            Do While alcResultSet.EOF = False
            
                varParams2(0) = alcResultSet.LocationFromName
                varParams2(1) = alcResultSet.LocationToName
                varParams2(2) = alcResultSet.Quantity
                varParams2(3) = m_recSKU("PR_Product_SKU")
            
                Set recSave = adoHelper.ExecuteProcedure("{call sp_SaveAllocResult(?,?,?,?)}", varParams2)
            
                alcResultSet.MoveNext
            Loop
    
        End If
    
    
        DoEvents
    
        m_recSKU.MoveNext
    Loop
    


    If IsObject(recFillLocations) Then
        Set recFillLocations = Nothing
    End If
    If IsObject(recAllocLocations) Then
        Set recAllocLocations = Nothing
    End If

    If IsObject(alcResultSet) Then
        Set alcResultSet = Nothing
    End If
    If IsObject(alcAllocate) Then
        Set alcAllocate = Nothing
    End If
    If IsObject(m_recSKU) Then
        Set m_recSKU = Nothing
    End If

    adoHelper.CloseConnection

    If IsObject(adoHelper) Then
        Set adoHelper = Nothing
    End If

    MsgBox "Allocation is now completed." & vbCrLf & "Please check the Reports to view the results." & vbCrLf & " Please use Commit Allocation to commit this allocation.", vbInformation, "Allocation Complete ..."

End Sub

Private Sub lstOption_Click()



    grdSku.Text = lstOption.Text
    lstOption.Visible = False

End Sub

Private Sub lstOption_LostFocus()

    lstOption.Visible = False

End Sub

Private Sub cmdAllocateAll_Click()

    
    
    
    Dim lngRowCount As Long
    Dim adoRecSet As ADOR.Recordset
    Dim lngCount As Long
    
    Set adoRecSet = datSku.Recordset
    adoRecSet.MoveFirst
    lngRowCount = adoRecSet.RecordCount
    Set adoRecSet = Nothing

    grdSku.Row = 0


     For lngCount = 0 To (lngRowCount - 1)
        grdSku.Col = 3
        grdSku.Text = "True"
        grdSku.Scroll 0, 1
        DoEvents
        If (lngCount <> lngRowCount - 1) Then
        grdSku.Row = grdSku.Row + 1
        End If
    Next

    datSku.Recordset.UpdateBatch

    
End Sub

Private Sub cmdAllocateNone_Click()


    
  
    
    Dim lngRowCount As Long
    Dim adoRecSet As ADOR.Recordset
    Dim lngCount As Long
    
    Set adoRecSet = datSku.Recordset
    adoRecSet.MoveFirst
    lngRowCount = adoRecSet.RecordCount
    Set adoRecSet = Nothing

    grdSku.Row = 0


    For lngCount = 0 To (lngRowCount - 1)
        grdSku.Col = 3
        grdSku.Text = "False"
        grdSku.Scroll 0, 1
        DoEvents
        If (lngCount <> lngRowCount - 1) Then
        grdSku.Row = grdSku.Row + 1
        End If
    Next

    datSku.Recordset.UpdateBatch

End Sub

Private Sub Form_Resize()
 If (Me.Height > 2000) And (Me.Width > 6000) Then
    
        grdSku.Width = Me.Width - 200
        datSku.Width = Me.Width - 5220
        grdSku.Height = Me.Height - 855
        datSku.Top = Me.Height - 780
        
        cmdAllocateAll.Top = Me.Height - 780
        cmdAllocateNone.Top = Me.Height - 780
       cmdRunAllocation.Top = Me.Height - 780
       
        cmdAllocateAll.Left = Me.Width - 3465
        cmdAllocateNone.Left = Me.Width - 1785
        cmdRunAllocation.Left = Me.Width - 5145
       
    End If
End Sub

Private Sub grdSku_ButtonClick(ByVal ColIndex As Integer)

If ColIndex = 3 Then

    lstOption.Top = grdSku.Top + grdSku.RowTop(grdSku.Row) + grdSku.RowHeight
    lstOption.Left = grdSku.Left + grdSku.Columns(ColIndex).Left
    lstOption.Visible = Not lstOption.Visible
    lstOption.ZOrder
    lstOption.Width = grdSku.Columns(ColIndex).Width
    lstOption.Height = 500

End If

End Sub

