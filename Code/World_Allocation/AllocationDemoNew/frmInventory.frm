VERSION 5.00
Object = "{67397AA1-7FB1-11D0-B148-00A0C922E820}#6.0#0"; "Msadodc.ocx"
Object = "{CDE57A40-8B86-11D0-B3C6-00A0C90AEA82}#1.0#0"; "MSDATGRD.OCX"
Object = "{F0D2F211-CCB0-11D0-A316-00AA00688B10}#1.0#0"; "MSDATLST.OCX"
Begin VB.Form frmInventory 
   Appearance      =   0  'Flat
   BackColor       =   &*********&
   Caption         =   "Location On Hand"
   ClientHeight    =   7005
   ClientLeft      =   60
   ClientTop       =   345
   ClientWidth     =   9600
   Icon            =   "frmInventory.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   7005
   ScaleWidth      =   9600
   Begin MSDataGridLib.DataGrid grdLocationOnHand 
      Bindings        =   "frmInventory.frx":0442
      Height          =   5775
      Left            =   30
      TabIndex        =   0
      Top             =   30
      Width           =   9435
      _ExtentX        =   16642
      _ExtentY        =   10186
      _Version        =   393216
      AllowUpdate     =   -1  'True
      BackColor       =   8454143
      HeadLines       =   1
      RowHeight       =   15
      FormatLocked    =   -1  'True
      AllowAddNew     =   -1  'True
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnCount     =   5
      BeginProperty Column00 
         DataField       =   "IN_Inventory_Key"
         Caption         =   "IN_Inventory_Key"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column01 
         DataField       =   "IN_Location"
         Caption         =   "Location"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column02 
         DataField       =   "IN_Sku"
         Caption         =   "Sku"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column03 
         DataField       =   "IN_Quantity"
         Caption         =   "Quantity"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column04 
         DataField       =   "IN_State"
         Caption         =   "State"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      SplitCount      =   1
      BeginProperty Split0 
         BeginProperty Column00 
            Object.Visible         =   0   'False
            ColumnWidth     =   1349.858
         EndProperty
         BeginProperty Column01 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column02 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column03 
            ColumnWidth     =   915.024
         EndProperty
         BeginProperty Column04 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
      EndProperty
   End
   Begin MSAdodcLib.Adodc datInventory 
      Height          =   360
      Left            =   30
      Top             =   6630
      Width           =   7335
      _ExtentX        =   12938
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   0
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   "sa"
      Password        =   "sql"
      RecordSource    =   "Inventory"
      Caption         =   "Location On Hand"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSAdodcLib.Adodc datStores 
      Height          =   525
      Left            =   3690
      Top             =   5910
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   8
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Select ST_Code from Store union all Select DC_Code from DC"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstStores 
      Bindings        =   "frmInventory.frx":045D
      DataField       =   "IN_Location"
      DataSource      =   "datInventory"
      Height          =   255
      Left            =   6150
      TabIndex        =   1
      Top             =   6270
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "ST_Code"
      BoundColumn     =   "ST_Code"
   End
   Begin MSDataListLib.DataList lstSKU 
      Bindings        =   "frmInventory.frx":0483
      DataField       =   "IN_Sku"
      DataSource      =   "datInventory"
      Height          =   255
      Left            =   1800
      TabIndex        =   2
      Top             =   6060
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "PR_Product_SKU"
      BoundColumn     =   "PR_Product_SKU"
   End
   Begin MSAdodcLib.Adodc datSKU 
      Height          =   525
      Left            =   7440
      Top             =   6060
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Products"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSAdodcLib.Adodc datStatus 
      Height          =   525
      Left            =   240
      Top             =   5940
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Inventory_Status"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstStatus 
      Bindings        =   "frmInventory.frx":04A6
      DataField       =   "IN_State"
      DataSource      =   "datInventory"
      Height          =   255
      Left            =   5760
      TabIndex        =   3
      Top             =   5970
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "IS_Inventory_Status"
      BoundColumn     =   "IS_Inventory_Status"
   End
End
Attribute VB_Name = "frmInventory"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Form_Resize()
 If (Me.Height > 2000) And (Me.Width > 2000) Then
    
        grdLocationOnHand.Width = Me.Width - 200
        datInventory.Width = Me.Width - 200
        grdLocationOnHand.Height = Me.Height - 855
        datInventory.Top = Me.Height - 780
        lstStores.Visible = False
        lstSKU.Visible = False
        lstStatus.Visible = False
    
    End If
End Sub

Private Sub grdLocationOnHand_ButtonClick(ByVal ColIndex As Integer)


   Select Case ColIndex
    
    Case 1

    lstStores.Top = grdLocationOnHand.Top + grdLocationOnHand.RowTop(grdLocationOnHand.Row) + grdLocationOnHand.RowHeight
    lstStores.Left = grdLocationOnHand.Left + grdLocationOnHand.Columns(ColIndex).Left
    lstStores.Visible = Not lstStores.Visible
    lstStores.ZOrder
    lstStores.Width = grdLocationOnHand.Columns(ColIndex).Width
    lstStores.Height = 1440


    Case 2

    lstSKU.Top = grdLocationOnHand.Top + grdLocationOnHand.RowTop(grdLocationOnHand.Row) + grdLocationOnHand.RowHeight
    lstSKU.Left = grdLocationOnHand.Left + grdLocationOnHand.Columns(ColIndex).Left
    lstSKU.Visible = Not lstSKU.Visible
    lstSKU.ZOrder
    lstSKU.Width = grdLocationOnHand.Columns(ColIndex).Width
    lstSKU.Height = 1440

    Case 4
    
    lstStatus.Top = grdLocationOnHand.Top + grdLocationOnHand.RowTop(grdLocationOnHand.Row) + grdLocationOnHand.RowHeight
    lstStatus.Left = grdLocationOnHand.Left + grdLocationOnHand.Columns(ColIndex).Left
    lstStatus.Visible = Not lstStatus.Visible
    lstStatus.ZOrder
    lstStatus.Width = grdLocationOnHand.Columns(ColIndex).Width
    lstStatus.Height = 1440
    

    End Select

End Sub


Private Sub lstStores_Click()

grdLocationOnHand.Text = lstStores.Text
lstStores.Visible = False
    
End Sub

Private Sub lstSku_Click()

grdLocationOnHand.Text = lstSKU.Text
lstSKU.Visible = False
    
End Sub


Private Sub lstStatus_Click()

grdLocationOnHand.Text = lstStatus.Text
lstStatus.Visible = False
    
End Sub


Private Sub lstStores_LostFocus()

    lstStores.Visible = False

End Sub
Private Sub lstSKU_LostFocus()

    lstSKU.Visible = False

End Sub


Private Sub lstStatus_LostFocus()

    lstStatus.Visible = False

End Sub

