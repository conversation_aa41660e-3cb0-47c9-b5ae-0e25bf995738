VERSION 5.00
Object = "{67397AA1-7FB1-11D0-B148-00A0C922E820}#6.0#0"; "Msadodc.ocx"
Object = "{CDE57A40-8B86-11D0-B3C6-00A0C90AEA82}#1.0#0"; "MSDATGRD.OCX"
Object = "{F0D2F211-CCB0-11D0-A316-00AA00688B10}#1.0#0"; "MSDATLST.OCX"
Begin VB.Form frmLocationPriority 
   Appearance      =   0  'Flat
   BackColor       =   &H80000005&
   Caption         =   "Location Priority"
   ClientHeight    =   8715
   ClientLeft      =   60
   ClientTop       =   345
   ClientWidth     =   10200
   Icon            =   "frmLocationPriority.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   8715
   ScaleWidth      =   10200
   Begin MSAdodcLib.Adodc datStores 
      Height          =   525
      Left            =   1290
      Top             =   6180
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "Store"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstStores 
      Bindings        =   "frmLocationPriority.frx":0442
      DataField       =   "OD_Store"
      DataSource      =   "datLocationPriority"
      Height          =   255
      Left            =   6930
      TabIndex        =   1
      Top             =   6930
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "ST_Code"
   End
   Begin MSDataGridLib.DataGrid grdLocationPri 
      Bindings        =   "frmLocationPriority.frx":0468
      Height          =   5775
      Left            =   30
      TabIndex        =   0
      Top             =   30
      Width           =   9435
      _ExtentX        =   16642
      _ExtentY        =   10186
      _Version        =   393216
      AllowUpdate     =   -1  'True
      BackColor       =   8454143
      HeadLines       =   1
      RowHeight       =   15
      FormatLocked    =   -1  'True
      AllowAddNew     =   -1  'True
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnCount     =   6
      BeginProperty Column00 
         DataField       =   "OD_Order_Sourcing_Key"
         Caption         =   "OD_Order_Sourcing_Key"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column01 
         DataField       =   "OD_Store"
         Caption         =   "Store"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column02 
         DataField       =   "OD_Rank"
         Caption         =   "Rank"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column03 
         DataField       =   "OD_DC_1"
         Caption         =   "DC 1"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column04 
         DataField       =   "OD_DC_2"
         Caption         =   "DC 2"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      BeginProperty Column05 
         DataField       =   "OD_DC_3"
         Caption         =   "DC 3"
         BeginProperty DataFormat {6D835690-900B-11D0-9484-00A0C91110ED} 
            Type            =   0
            Format          =   ""
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
      EndProperty
      SplitCount      =   1
      BeginProperty Split0 
         BeginProperty Column00 
            Button          =   -1  'True
            Object.Visible         =   0   'False
            ColumnWidth     =   1874.835
         EndProperty
         BeginProperty Column01 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column02 
            ColumnWidth     =   915.024
         EndProperty
         BeginProperty Column03 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column04 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
         BeginProperty Column05 
            Button          =   -1  'True
            ColumnWidth     =   1739.906
         EndProperty
      EndProperty
   End
   Begin MSAdodcLib.Adodc datLocationPriority 
      Height          =   360
      Left            =   30
      Top             =   8340
      Width           =   7335
      _ExtentX        =   12938
      _ExtentY        =   635
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   0
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   "sa"
      Password        =   "sql"
      RecordSource    =   "Order_Sourcing"
      Caption         =   "Location Priority"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSAdodcLib.Adodc datDC 
      Height          =   525
      Left            =   3210
      Top             =   6180
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "DC"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstDC1 
      Bindings        =   "frmLocationPriority.frx":048A
      DataField       =   "OD_DC_1"
      DataSource      =   "datLocationPriority"
      Height          =   255
      Left            =   5520
      TabIndex        =   2
      Top             =   6660
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "DC_Code"
      BoundColumn     =   "DC_Code"
   End
   Begin MSAdodcLib.Adodc datDC2 
      Height          =   525
      Left            =   4830
      Top             =   7170
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "DC"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstDC2 
      Bindings        =   "frmLocationPriority.frx":04AC
      DataField       =   "OD_DC_2"
      DataSource      =   "datLocationPriority"
      Height          =   255
      Left            =   7350
      TabIndex        =   3
      Top             =   6300
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "DC_Code"
      BoundColumn     =   "DC_Code"
   End
   Begin MSAdodcLib.Adodc datDC3 
      Height          =   525
      Left            =   4770
      Top             =   5940
      Visible         =   0   'False
      Width           =   1200
      _ExtentX        =   2117
      _ExtentY        =   926
      ConnectMode     =   0
      CursorLocation  =   3
      IsolationLevel  =   -1
      ConnectionTimeout=   15
      CommandTimeout  =   30
      CursorType      =   3
      LockType        =   3
      CommandType     =   2
      CursorOptions   =   0
      CacheSize       =   50
      MaxRecords      =   0
      BOFAction       =   0
      EOFAction       =   0
      ConnectStringType=   1
      Appearance      =   1
      BackColor       =   -**********
      ForeColor       =   -**********
      Orientation     =   0
      Enabled         =   -1
      Connect         =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBString     =   """SERVER=LOCALHOST;DATABASE=ALLOC_DEMO;PWD=***;UID=SA;PROVIDER=***OLEDB.1"""
      OLEDBFile       =   ""
      DataSourceName  =   ""
      OtherAttributes =   ""
      UserName        =   ""
      Password        =   ""
      RecordSource    =   "DC"
      Caption         =   "Adodc1"
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      _Version        =   393216
   End
   Begin MSDataListLib.DataList lstDC3 
      Bindings        =   "frmLocationPriority.frx":04CF
      DataField       =   "OD_DC_3"
      DataSource      =   "datLocationPriority"
      Height          =   255
      Left            =   6990
      TabIndex        =   4
      Top             =   5910
      Visible         =   0   'False
      Width           =   975
      _ExtentX        =   1720
      _ExtentY        =   450
      _Version        =   393216
      Appearance      =   0
      ListField       =   "DC_Code"
      BoundColumn     =   "DC_Code"
   End
End
Attribute VB_Name = "frmLocationPriority"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Form_Resize()

    If (Me.Height > 2000) And (Me.Width > 2000) Then
    
        grdLocationPri.Width = Me.Width - 200
        datLocationPriority.Width = Me.Width - 200
        grdLocationPri.Height = Me.Height - 855
        datLocationPriority.Top = Me.Height - 780
        lstStores.Visible = False
        lstDC1.Visible = False
        lstDC2.Visible = False
        lstDC3.Visible = False
    
    End If

End Sub

    
Private Sub grdLocationPri_ButtonClick(ByVal ColIndex As Integer)

    Select Case ColIndex
    
    Case 1

    lstStores.Top = grdLocationPri.Top + grdLocationPri.RowTop(grdLocationPri.Row) + grdLocationPri.RowHeight
    lstStores.Left = grdLocationPri.Left + grdLocationPri.Columns(ColIndex).Left
    lstStores.Visible = Not lstStores.Visible
    lstStores.ZOrder
    lstStores.Width = grdLocationPri.Columns(ColIndex).Width
    lstStores.Height = 1440

    Case 3
    
    lstDC1.Top = grdLocationPri.Top + grdLocationPri.RowTop(grdLocationPri.Row) + grdLocationPri.RowHeight
    lstDC1.Left = grdLocationPri.Left + grdLocationPri.Columns(ColIndex).Left
    lstDC1.Visible = Not lstDC1.Visible
    lstDC1.ZOrder
    lstDC1.Width = grdLocationPri.Columns(ColIndex).Width
    lstDC1.Height = 1440
    
    Case 4
    
    lstDC2.Top = grdLocationPri.Top + grdLocationPri.RowTop(grdLocationPri.Row) + grdLocationPri.RowHeight
    lstDC2.Left = grdLocationPri.Left + grdLocationPri.Columns(ColIndex).Left
    lstDC2.Visible = Not lstDC2.Visible
    lstDC2.ZOrder
    lstDC2.Width = grdLocationPri.Columns(ColIndex).Width
    lstDC2.Height = 1440
    
    Case 5
    
    lstDC3.Top = grdLocationPri.Top + grdLocationPri.RowTop(grdLocationPri.Row) + grdLocationPri.RowHeight
    lstDC3.Left = grdLocationPri.Left + grdLocationPri.Columns(ColIndex).Left
    lstDC3.Visible = Not lstDC3.Visible
    lstDC3.ZOrder
    lstDC3.Width = grdLocationPri.Columns(ColIndex).Width
    lstDC3.Height = 1440
    
    
    End Select

End Sub



Private Sub lstDC1_Click()

     grdLocationPri.Text = lstDC1.Text
    lstDC1.Visible = False

End Sub

Private Sub lstDC1_LostFocus()
lstDC1.Visible = False

    
End Sub

Private Sub lstDC2_LostFocus()
lstDC2.Visible = False

    
End Sub
Private Sub lstDC3_LostFocus()
lstDC3.Visible = False

    
End Sub


Private Sub lstDC2_Click()

     grdLocationPri.Text = lstDC2.Text
    lstDC2.Visible = False

End Sub

Private Sub lstDC3_Click()

     grdLocationPri.Text = lstDC3.Text
    lstDC3.Visible = False

End Sub


Private Sub lstStores_Click()

    grdLocationPri.Text = lstStores.Text
    lstStores.Visible = False

End Sub

Private Sub lstStores_LostFocus()
lstStores.Visible = False
End Sub
