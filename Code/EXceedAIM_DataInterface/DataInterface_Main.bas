Attribute VB_Name = "DataInterface_Main"
Option Explicit

'Declare WINAPI Exit Process
Public Declare Sub ExitProcess Lib "kernel32" (ByVal uExitCode As Long)

Dim m_LogFile As String
Dim IsDevEnvironment As Boolean
    
Enum DuGet
    du_LcId = 0
    du_item = 1
    du_ItDesc = 2
    du_ItStat = 3
    du_ActDate = 4
    du_InActDate = 5
    du_OptionId = 6
    du_Class1 = 7
    du_Class2 = 8
    du_Class3 = 9
    du_Class4 = 10
    du_BinLocation = 11
    du_BuyStrat = 12
    du_VelCode = 13
    du_VnId = 14
    du_Assort = 15
    du_ById = 16
    du_MDC = 17
    du_MDCFlag = 18
    du_SaId = 19
    du_PmId = 20
    du_UPC = 21
    du_weight = 22
    du_cube = 23
    du_price = 24
    du_cost = 25
    du_bkqty01 = 26
    du_bkcost01 = 27
    du_bkqty02 = 28
    du_bkcost02 = 29
    du_bkqty03 = 30
    du_bkcost03 = 31
    du_bkqty04 = 32
    du_bkcost04 = 33
    du_bkqty05 = 34
    du_bkcost05 = 35
    du_bkqty06 = 36
    du_bkcost06 = 37
    du_bkqty07 = 38
    du_bkcost07 = 39
    du_bkqty08 = 40
    du_bkcost08 = 41
    du_bkqty09 = 42
    du_bkcost09 = 43
    du_bkqty10 = 44
    du_bkcost10 = 45
    du_uom = 46
    du_convfactor = 47
    du_buyingUOM = 48
    du_ReplenCost2 = 49
    du_Oh = 50
    du_oo = 51
    du_comstk = 52
    du_bkorder = 53
    du_bkcomstk = 54
    du_LeadTime = 55
    du_PackRounding = 56
    du_IMin = 57
    du_IMax = 58
    du_CStock = 59
    du_ssadj = 60
    du_UserMin = 61
    du_UserMax = 62
    du_UserMethod = 63
    du_FcstMethod = 64
    du_FcstDemand = 65
    du_UserFcst = 66
    du_UserFcstExpDate = 67
    du_MAE = 68
    du_MSE = 69
    du_trend = 70
    du_fcstcycles = 71
    du_zerocount = 72
    du_diflag = 73
    du_DmdFilterFlag = 74
    du_TrkSignalFlag = 75
    du_UserDemandFlag = 76
    du_BypassPct = 77
    du_FcstUpdCyc = 78
    du_ltvfact = 79
    du_plntt = 80
    du_zopsw = 81
    du_outlsw = 82
    du_zsstock = 83
    du_dser = 84
    du_freeze_buystrat = 85
    du_freeze_byid = 86
    du_freeze_leadtime = 87
    du_freeze_optionid = 88
    du_freeze_dser = 89
    du_olditem = 90
    du_accum_lt = 91
    du_reviewtime = 92
    du_orderpt = 93
    du_orderqty = 94
    du_SafetyStock = 95
    du_fcstrt = 96
    du_fcstlt = 97
    du_fcst_month = 98
    du_fcst_quarter = 99
    du_fcst_year = 100
    du_fcstdate = 101
    du_vc_amt = 102
    du_vc_units_ranking = 103
    du_vc_amt_ranking = 104
    du_vc_date = 105
    du_velcode_prev = 106
    du_vc_amt_prev = 107
    du_OpDesc = 108
    du_IDFL = 109
    du_BypIDFL = 110
    du_CADL = 111
    du_HiDFL = 112
    du_LoDFL = 113
    du_NDFL = 114
    du_DDMAP = 115
    du_DAMP = 116
    du_MinSmk = 117
    du_MaxSmk = 118
    du_IntSmk = 119
    du_TSL = 120
    du_TSSmK = 121
    du_BypDDU = 122
    du_ApplyDmdFilter = 123
    du_BypZSl = 124
    du_BypTFP = 125
    du_BypZOH = 126
    du_BypBef = 127
    du_BypAft = 128
    du_MinBi = 129
    du_HiPct = 130
    du_LoPct = 131
    du_HiMADP = 132
    du_LoMADP = 133
    du_MADSmk = 134
    du_MADExk = 135
    du_TrnSmk = 136
    du_DIDDL = 137
    du_DiTrnDL = 138
    du_DITrps = 139
    du_DIMADP = 140
    du_HiTrndL = 141
    du_FilterSmk = 142
    du_LumpyFilterPct = 143
    du_Dft_TurnHigh = 144
    du_Dft_TurnLow = 145
    du_Dft_Dser = 146
    du_bi01 = 147
    du_BI02 = 148
    du_BI03 = 149
    du_BI04 = 150
    du_BI05 = 151
    du_BI06 = 152
    du_BI07 = 153
    du_BI08 = 154
    du_BI09 = 155
    du_BI10 = 156
    du_BI11 = 157
    du_BI12 = 158
    du_BI13 = 159
    du_BI14 = 160
    du_BI15 = 161
    du_BI16 = 162
    du_BI17 = 163
    du_BI18 = 164
    du_BI19 = 165
    du_BI20 = 166
    du_BI21 = 167
    du_BI22 = 168
    du_BI23 = 169
    du_BI24 = 170
    du_BI25 = 171
    du_BI26 = 172
    du_BI27 = 173
    du_BI28 = 174
    du_BI29 = 175
    du_BI30 = 176
    du_BI31 = 177
    du_BI32 = 178
    du_BI33 = 179
    du_BI34 = 180
    du_BI35 = 181
    du_BI36 = 182
    du_BI37 = 183
    du_BI38 = 184
    du_BI39 = 185
    du_BI40 = 186
    du_BI41 = 187
    du_BI42 = 188
    du_BI43 = 189
    du_BI44 = 190
    du_BI45 = 191
    du_BI46 = 192
    du_BI47 = 193
    du_BI48 = 194
    du_BI49 = 195
    du_BI50 = 196
    du_BI51 = 197
    du_BI52 = 198
    du_Dft_ById = 199
    du_RevCycle = 200
    du_Dft_LeadTime = 201
    du_RevCycles_ReviewTime = 202
    du_HisYear = 203
    du_cps01 = 204
    du_CPS02 = 205
    du_CPS03 = 206
    du_CPS04 = 207
    du_CPS05 = 208
    du_CPS06 = 209
    du_CPS07 = 210
    du_CPS08 = 211
    du_CPS09 = 212
    du_CPS10 = 213
    du_CPS11 = 214
    du_CPS12 = 215
    du_CPS13 = 216
    du_CPS14 = 217
    du_CPS15 = 218
    du_CPS16 = 219
    du_CPS17 = 220
    du_CPS18 = 221
    du_CPS19 = 222
    du_CPS20 = 223
    du_CPS21 = 224
    du_CPS22 = 225
    du_CPS23 = 226
    du_CPS24 = 227
    du_CPS25 = 228
    du_CPS26 = 229
    du_CPS27 = 230
    du_CPS28 = 231
    du_CPS29 = 232
    du_CPS30 = 233
    du_CPS31 = 234
    du_CPS32 = 235
    du_CPS33 = 236
    du_CPS34 = 237
    du_CPS35 = 238
    du_CPS36 = 239
    du_CPS37 = 240
    du_CPS38 = 241
    du_CPS39 = 242
    du_CPS40 = 243
    du_CPS41 = 244
    du_CPS42 = 245
    du_CPS43 = 246
    du_CPS44 = 247
    du_CPS45 = 248
    du_CPS46 = 249
    du_CPS47 = 250
    du_CPS48 = 251
    du_CPS49 = 252
    du_CPS50 = 253
    du_CPS51 = 254
    du_CPS52 = 255

End Enum

Private Function BldMDCSummary()
On Error GoTo ErrorHandler

    Dim Cn1 As ADODB.Connection
    Dim Cn2 As ADODB.Connection
    
    Dim rsMDCSummary As ADODB.Recordset
    
    Dim AIM_MDCSummary_Sp As ADODB.Command
    Dim AIM_MDCSummary_Upd_Sp As ADODB.Command
    
    Dim i As Integer
    Dim Pd As Integer
    Dim PartWeek As Double
    Dim RtnCode As Integer
    
    Dim BkQty(1 To 10) As Long
    Dim BkCost(1 To 10) As Double
    Dim FcstRT As Double
    Dim FcstLT As Double
    Dim Fcst_Month As Double
    Dim Fcst_Quarter As Double
    Dim Fcst_Year As Double
    Dim MAE As Double
    Dim OrderPt As Long
    Dim OrderQty As Long
    Dim RqQty(1 To 26) As Long
    Dim SStock As Double
    Dim Turns As Double
    
    'Open connections to the server
    Set Cn1 = New ADODB.Connection
    RtnCode = SQLConnection(Cn1, CONNECTION_OPEN, False)
    
    Set Cn2 = New ADODB.Connection
    RtnCode = SQLConnection(Cn2, CONNECTION_OPEN, False)
    
    'Initialize the MDC Summary Update Stored Procedure
    Set AIM_MDCSummary_Upd_Sp = New ADODB.Command
    With AIM_MDCSummary_Upd_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_MDCSummary_Upd_Sp"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    
    'Build the MDC Summary Cursor
    Set AIM_MDCSummary_Sp = New ADODB.Command
    With AIM_MDCSummary_Sp
        Set .ActiveConnection = Cn1
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_MDCSummary_Sp"
    End With
    
    Set rsMDCSummary = New ADODB.Recordset
    With rsMDCSummary
        .CursorLocation = adUseServer
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
        .CacheSize = 1000
    End With
    
    rsMDCSummary.Open AIM_MDCSummary_Sp
    If Not f_IsRecordsetOpenAndPopulated(rsMDCSummary) Then
        Exit Function
    ElseIf (rsMDCSummary.BOF And rsMDCSummary.EOF) Then
        Exit Function
    Else
        rsMDCSummary.MoveFirst
    End If
    
    'Cursor Control Loop
    Do Until rsMDCSummary.EOF
        'Initialize Forecast for Year
        Fcst_Year = 0
        FcstLT = 0
        FcstRT = 0
        
        'Initialize demand array
        For i = LBound(RqQty) To UBound(RqQty)
            RqQty(i) = rsMDCSummary("RqQty" & Format(i, "00"))
            Fcst_Year = Fcst_Year + RqQty(i)
        Next i
        
        'Initialize Quantity Break Values
        For i = LBound(BkQty) To UBound(BkQty)
            BkQty(i) = rsMDCSummary("BkQty" & Format(i, "00"))
            BkCost(i) = rsMDCSummary("BkCost" & Format(i, "00"))
        Next i
         
        'Calculate the key variables
        Fcst_Quarter = Fcst_Year / 2
        Fcst_Year = Fcst_Year * 2
        Fcst_Month = RqQty(1) + RqQty(2) + RqQty(3) + RqQty(4) + (RqQty(5) / 3)
        
        'Calculate Forecast for the Accumulative Lead Time
        PartWeek = rsMDCSummary("Accum_LT").Value / 7
        Pd = CInt(PartWeek)
        PartWeek = PartWeek - Pd
                 
        For i = 1 To Pd
            FcstLT = FcstLT + RqQty(i)
        Next i
        
        FcstLT = FcstLT + PartWeek * RqQty(i)
        
        'Calculate Forecast for the Review Time
        PartWeek = (rsMDCSummary("Accum_LT").Value + rsMDCSummary("ReviewTime").Value) / 7
        Pd = CInt(PartWeek)
        PartWeek = PartWeek - Pd
                  
        For i = 1 To Pd
            FcstRT = FcstRT + RqQty(i)
        Next i
        
        FcstRT = FcstRT + PartWeek * RqQty(i)
        
        FcstRT = FcstRT - FcstLT
        
        'Apply High and Low Mean Absolute Error Filters
        MAE = rsMDCSummary("MAE").Value
        
        If rsMDCSummary("FcstDemand").Value > 0 Then
            If MAE / rsMDCSummary("FcstDemand").Value < rsMDCSummary("LoMADP").Value Then
                MAE = rsMDCSummary("LoMADP").Value * rsMDCSummary("FcstDemand").Value
            End If
            
            If MAE / rsMDCSummary("FcstDemand").Value > rsMDCSummary("HiMADP").Value Then
                MAE = rsMDCSummary("HiMADP").Value * rsMDCSummary("FcstDemand").Value
            End If
        
        End If
        
        'Calculate the Safety Stock
        SStock = CalcSafetyStock(FcstLT, rsMDCSummary("FcstDemand").Value, MAE, _
                rsMDCSummary("Accum_LT").Value, rsMDCSummary("Dser").Value, rsMDCSummary("MADExk").Value)
                
        'Apply Safety Stock Adjustments
        SStock = SStock * (1 + rsMDCSummary("LtvFact").Value) * (1 + rsMDCSummary("SSAdj").Value)
    
        'Perform counter stock test
        If SStock < rsMDCSummary("CStock").Value And rsMDCSummary("ZSStock").Value <> "Y" Then
            SStock = rsMDCSummary("CStock").Value
        End If
    
        'Check zero safety stock switch
        If rsMDCSummary("ZSStock").Value = "Y" Then
            SStock = 0
        End If
        
        'Calculate the Order Point
        OrderPt = Round(FcstLT + FcstRT + SStock, 0)
       
        'Calculate the Economic Order Quantity for an item
        If BkQty(1) = 0 Then
            OrderQty = g_EOQ(rsMDCSummary("ReplenCost").Value + rsMDCSummary("ReplenCost2").Value, _
                rsMDCSummary("KFactor").Value, rsMDCSummary("FcstDemand").Value, rsMDCSummary("Cost").Value)
                
        Else
            OrderQty = g_QuantityBreakEOQ(rsMDCSummary("FcstDemand").Value, _
                rsMDCSummary("KFactor").Value, rsMDCSummary("ReplenCost").Value + rsMDCSummary("ReplenCost2").Value, _
                BkQty(), BkCost())
    
        End If
        
        If rsMDCSummary("FcstDemand").Value > 0 Then
            'Test against the high and low turn limits
            If ((OrderQty + FcstRT) / 2 + SStock) > 0 Then
                Turns = (rsMDCSummary("FcstDemand").Value * 52) _
                    / ((OrderQty + FcstRT) / 2 + SStock)
            Else
                Turns = 0
            End If
    
            If Turns > rsMDCSummary("Dft_TurnHigh").Value Then
                If rsMDCSummary("Dft_TurnHigh").Value <> 0 Then
                    OrderQty = 2 * (((rsMDCSummary("FcstDemand").Value * 52) _
                        / rsMDCSummary("Dft_TurnHigh").Value) - SStock) - FcstRT
                Else
                    OrderQty = 2 * (((rsMDCSummary("FcstDemand").Value * 52) _
                        / 1) - SStock) - FcstRT
                End If
    
            ElseIf Turns < rsMDCSummary("Dft_TurnLow").Value Then
                If rsMDCSummary("Dft_TurnLow").Value <> 0 Then
                    OrderQty = 2 * (((rsMDCSummary("FcstDemand").Value * 52) / rsMDCSummary("Dft_TurnLow").Value) _
                        - SStock) - FcstRT
                Else
                    OrderQty = 2 * (((rsMDCSummary("FcstDemand").Value * 52) / 1) _
                        - SStock) - FcstRT
                End If
    
            End If
    
        Else
            OrderQty = 0
    
        End If
        
        'Do this checking only if K factor is >0  Kevin Hill
        If rsMDCSummary("kfactor").Value > 0 Then
            'Test for Maximum Order Quantity (1 year)
            If OrderQty > rsMDCSummary("FcstDemand").Value * 52 Then
                OrderQty = rsMDCSummary("FcstDemand").Value * 52
            End If
        
            'Test for Minimum Order Quantity (Based on Review Time)
            If OrderQty < FcstRT Then
                OrderQty = FcstRT
            End If
        
            'Test for Minimum Order Quantity (Minimum 2 Day Supply)
            If OrderQty < rsMDCSummary("FcstDemand").Value / 3.5 Then
                OrderQty = rsMDCSummary("FcstDemand").Value / 3.5
            End If
        
            'Test for Minimum Order Quantity (1 unit)
            If OrderQty < 1 Then
                OrderQty = 1
            End If
        End If
                
        'Update the MDC Summary Table
        With AIM_MDCSummary_Upd_Sp
            .Parameters("@MDC").Value = rsMDCSummary("MDC").Value
            .Parameters("@Item").Value = rsMDCSummary("Item").Value
            .Parameters("@Accum_Lt").Value = rsMDCSummary("Accum_LT").Value
            .Parameters("@ReviewTime").Value = rsMDCSummary("ReviewTime").Value
            .Parameters("@OrderPt").Value = OrderPt
            .Parameters("@OrderQty").Value = OrderQty
            .Parameters("@SafetyStock").Value = SStock
            .Parameters("@FcstRT").Value = FcstRT
            .Parameters("@FcstLT").Value = FcstLT
            .Parameters("@Fcst_Month").Value = Fcst_Month
            .Parameters("@Fcst_Quarter").Value = Fcst_Quarter
            .Parameters("@Fcst_Year").Value = Fcst_Year
            
            .Execute
        
        End With
        
        'Get the next record
          rsMDCSummary.MoveNext
    
    Loop        'End of MDC Summary Loop
    
    'Wrap Up
    If f_IsRecordsetValidAndOpen(rsMDCSummary) Then rsMDCSummary.Close
    Set rsMDCSummary = Nothing
    
    If Not (AIM_MDCSummary_Sp Is Nothing) Then Set AIM_MDCSummary_Sp.ActiveConnection = Nothing
    Set AIM_MDCSummary_Sp = Nothing
    
    If Not (AIM_MDCSummary_Upd_Sp Is Nothing) Then Set AIM_MDCSummary_Upd_Sp.ActiveConnection = Nothing
    Set AIM_MDCSummary_Upd_Sp = Nothing
    
    RtnCode = SQLConnection(Cn1, CONNECTION_CLOSE, False)
    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(BldMDCSummary)", _
            0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::BldMDCSummary", Now, gDRJobError, True, Err
        
    
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsMDCSummary) Then rsMDCSummary.Close
    Set rsMDCSummary = Nothing
    
    If Not (AIM_MDCSummary_Sp Is Nothing) Then Set AIM_MDCSummary_Sp.ActiveConnection = Nothing
    Set AIM_MDCSummary_Sp = Nothing
    
    If Not (AIM_MDCSummary_Upd_Sp Is Nothing) Then Set AIM_MDCSummary_Upd_Sp.ActiveConnection = Nothing
    Set AIM_MDCSummary_Upd_Sp = Nothing
    
    RtnCode = SQLConnection(Cn1, CONNECTION_CLOSE, False)
    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
    
End Function
Private Function BldKitBOMSummary(A_AIM_KitBOMSummary_Sp As ADODB.Command, A_AIM_KitBOMSummary_Upd_Sp As ADODB.Command, _
A_rsAIM_KitBOMSummary As ADODB.Recordset, ALcid As String, AItem As String, AFcstLT As Double, _
AFcstRT As Double, AFcstMonth As Double, AFcstQtr As Double, AFcstYear As Double, _
AFcstDemand As Double, ADepFcstDemand As Double, AMAE As Double)
On Error GoTo ErrorHandler

    Dim Cn1 As ADODB.Connection
    Dim Cn2 As ADODB.Connection
    
'    Dim rsAIM_KitBOMSummary As ADODB.Recordset
'
'    Dim AIM_KitBOMSummary_Sp As ADODB.Command
'    Dim AIM_KitBOMSummary_Upd_Sp As ADODB.Command
    
    Dim i As Integer
    Dim RtnCode As Integer
    
    Dim BkQty(1 To 10) As Long
    Dim BkCost(1 To 10) As Double
    Dim OrderPt As Long
    Dim OrderQty As Long
    Dim RqQty(1 To 26) As Long
    Dim SStock As Double
    Dim Turns As Double
    
    'Open connections to the server
'    Set Cn1 = New ADODB.Connection
'    RtnCode = SQLConnection(Cn1, CONNECTION_OPEN, False)
'
'    Set Cn2 = New ADODB.Connection
'    RtnCode = SQLConnection(Cn2, CONNECTION_OPEN, False)
    
    'Initialize the MDC Summary Update Stored Procedure
'    Set AIM_KitBOMSummary_Upd_Sp = New ADODB.Command
'    With AIM_KitBOMSummary_Upd_Sp
'        Set .ActiveConnection = Cn2
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_KitBOMSummary_Upd_Sp"
'        .CommandTimeout = 0
'        .Parameters.Refresh
'    End With
'
'
'    Set AIM_KitBOMSummary_Sp = New ADODB.Command
'    With AIM_KitBOMSummary_Sp
'        Set .ActiveConnection = Cn1
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_KitBOMSummary_Sp"
'    End With
'
'    Set rsAIM_KitBOMSummary = New ADODB.Recordset
'    With rsAIM_KitBOMSummary
'        .CursorLocation = adUseServer
'        .CursorType = adOpenForwardOnly
'        .LockType = adLockReadOnly
'        .CacheSize = 1000
'    End With
    
    A_AIM_KitBOMSummary_Sp("@LcId").Value = ALcid
    A_AIM_KitBOMSummary_Sp("@Item").Value = AItem
    
    A_rsAIM_KitBOMSummary.Open A_AIM_KitBOMSummary_Sp
    If Not f_IsRecordsetOpenAndPopulated(A_rsAIM_KitBOMSummary) Then
        Exit Function
    ElseIf (A_rsAIM_KitBOMSummary.BOF And A_rsAIM_KitBOMSummary.EOF) Then
        If f_IsRecordsetValidAndOpen(A_rsAIM_KitBOMSummary) Then A_rsAIM_KitBOMSummary.Close
        Exit Function
    Else
'        rsAIM_KitBOMSummary.MoveFirst
    End If
        
        'Initialize Quantity Break Values
        For i = LBound(BkQty) To UBound(BkQty)
            BkQty(i) = A_rsAIM_KitBOMSummary("BkQty" & Format(i, "00"))
            BkCost(i) = A_rsAIM_KitBOMSummary("BkCost" & Format(i, "00"))
        Next i
 
         If AFcstDemand + ADepFcstDemand > 0 Then
            If AMAE / (AFcstDemand + ADepFcstDemand) < A_rsAIM_KitBOMSummary("LoMADP").Value Then
                AMAE = A_rsAIM_KitBOMSummary("LoMADP").Value * (AFcstDemand + ADepFcstDemand)
            End If
            
            If AMAE / (AFcstDemand + ADepFcstDemand) > A_rsAIM_KitBOMSummary("HiMADP").Value Then
                AMAE = A_rsAIM_KitBOMSummary("HiMADP").Value * (AFcstDemand + ADepFcstDemand)
            End If
        
         End If
        
        'Calculate the Safety Stock
        SStock = CalcSafetyStock(AFcstLT, (AFcstDemand + ADepFcstDemand), AMAE, _
                A_rsAIM_KitBOMSummary("Accum_LT").Value, A_rsAIM_KitBOMSummary("Dser").Value, A_rsAIM_KitBOMSummary("MADExk").Value)
                
        'Apply Safety Stock Adjustments
        SStock = SStock * (1 + A_rsAIM_KitBOMSummary("LtvFact").Value) * (1 + A_rsAIM_KitBOMSummary("SSAdj").Value)
    
        'Perform counter stock test
        If SStock < A_rsAIM_KitBOMSummary("CStock").Value And A_rsAIM_KitBOMSummary("ZSStock").Value <> "Y" Then
            SStock = A_rsAIM_KitBOMSummary("CStock").Value
        End If
    
        'Check zero safety stock switch
        If A_rsAIM_KitBOMSummary("ZSStock").Value = "Y" Then
            SStock = 0
        End If
        
        'Calculate the Order Point
        OrderPt = Round(AFcstLT + AFcstRT + SStock, 0)
       
        'Calculate the Economic Order Quantity for an item
        If BkQty(1) = 0 Then
            OrderQty = g_EOQ(A_rsAIM_KitBOMSummary("ReplenCost").Value + A_rsAIM_KitBOMSummary("ReplenCost2").Value, _
                A_rsAIM_KitBOMSummary("KFactor").Value, (AFcstDemand + ADepFcstDemand), A_rsAIM_KitBOMSummary("Cost").Value)
                
        Else
            OrderQty = g_QuantityBreakEOQ((AFcstDemand + ADepFcstDemand), _
                A_rsAIM_KitBOMSummary("KFactor").Value, A_rsAIM_KitBOMSummary("ReplenCost").Value + A_rsAIM_KitBOMSummary("ReplenCost2").Value, _
                BkQty(), BkCost())
    
        End If
        
        If (AFcstDemand + ADepFcstDemand) > 0 Then
            'Test against the high and low turn limits
            If ((OrderQty + AFcstRT) / 2 + SStock) > 0 Then
                Turns = ((AFcstDemand + ADepFcstDemand) * 52) _
                    / ((OrderQty + AFcstRT) / 2 + SStock)
            Else
                Turns = 0
            End If
    
            If Turns > A_rsAIM_KitBOMSummary("Dft_TurnHigh").Value Then
                If A_rsAIM_KitBOMSummary("Dft_TurnHigh").Value <> 0 Then
                    OrderQty = 2 * ((((AFcstDemand + ADepFcstDemand) * 52) _
                        / A_rsAIM_KitBOMSummary("Dft_TurnHigh").Value) - SStock) - AFcstRT
                Else
                    OrderQty = 2 * ((((AFcstDemand + ADepFcstDemand) * 52) _
                        / 1) - SStock) - AFcstRT
                End If
    
            ElseIf Turns < A_rsAIM_KitBOMSummary("Dft_TurnLow").Value Then
                If A_rsAIM_KitBOMSummary("Dft_TurnLow").Value <> 0 Then
                    OrderQty = 2 * ((((AFcstDemand + ADepFcstDemand) * 52) / A_rsAIM_KitBOMSummary("Dft_TurnLow").Value) _
                        - SStock) - AFcstRT
                Else
                    OrderQty = 2 * ((((AFcstDemand + ADepFcstDemand) * 52) / 1) _
                        - SStock) - AFcstRT
                End If
    
            End If
    
        Else
            OrderQty = 0
    
        End If
        
        'Do this checking only if K factor is >0  Kevin Hill
        If A_rsAIM_KitBOMSummary("kfactor").Value > 0 Then
            'Test for Maximum Order Quantity (1 year)
            If OrderQty > (AFcstDemand + ADepFcstDemand) * 52 Then
                OrderQty = (AFcstDemand + ADepFcstDemand) * 52
            End If
        
            'Test for Minimum Order Quantity (Based on Review Time)
            If OrderQty < AFcstRT Then
                OrderQty = AFcstRT
            End If
        
            'Test for Minimum Order Quantity (Minimum 2 Day Supply)
            If OrderQty < (AFcstDemand + ADepFcstDemand) / 3.5 Then
                OrderQty = (AFcstDemand + ADepFcstDemand) / 3.5
            End If
        
            'Test for Minimum Order Quantity (1 unit)
            If OrderQty < 1 Then
                OrderQty = 1
            End If
        End If
                
        'Update the MDC Summary Table
        With A_AIM_KitBOMSummary_Upd_Sp
            .Parameters("@Lcid").Value = ALcid
            .Parameters("@Item").Value = AItem
            .Parameters("@Accum_Lt").Value = A_rsAIM_KitBOMSummary("Accum_LT").Value
            .Parameters("@ReviewTime").Value = A_rsAIM_KitBOMSummary("ReviewTime").Value
            .Parameters("@OrderPt").Value = OrderPt
            .Parameters("@OrderQty").Value = OrderQty
            .Parameters("@SafetyStock").Value = SStock
            .Parameters("@FcstDemand").Value = AFcstDemand
            .Parameters("@DependentFcstDemand").Value = ADepFcstDemand
            .Parameters("@FcstRT").Value = AFcstRT
            .Parameters("@FcstLT").Value = AFcstLT
            .Parameters("@Fcst_Month").Value = AFcstMonth
            .Parameters("@Fcst_Quarter").Value = AFcstQtr
            .Parameters("@Fcst_Year").Value = AFcstYear

            .Execute

        End With
        
        'Get the next record
     '     rsAIM_KitBOMSummary.MoveNext
    
    'Loop        'End of MDC Summary Loop
    
    'Wrap Up
    If f_IsRecordsetValidAndOpen(A_rsAIM_KitBOMSummary) Then A_rsAIM_KitBOMSummary.Close
'    Set rsAIM_KitBOMSummary = Nothing
'
'    If Not (AIM_KitBOMSummary_Sp Is Nothing) Then Set AIM_KitBOMSummary_Sp.ActiveConnection = Nothing
'    Set AIM_KitBOMSummary_Sp = Nothing
'
'    If Not (AIM_MDCSummary_Upd_Sp Is Nothing) Then Set AIM_MDCSummary_Upd_Sp.ActiveConnection = Nothing
'    Set AIM_MDCSummary_Upd_Sp = Nothing
    
'    RtnCode = SQLConnection(Cn1, CONNECTION_CLOSE, False)
'    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(BldKitBOMSummary)", _
            0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::BldKitBOMSummary", Now, gDRJobError, True, Err
    
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(A_rsAIM_KitBOMSummary) Then A_rsAIM_KitBOMSummary.Close
    Set A_rsAIM_KitBOMSummary = Nothing
    
    If Not (A_AIM_KitBOMSummary_Sp Is Nothing) Then Set A_AIM_KitBOMSummary_Sp.ActiveConnection = Nothing
    Set A_AIM_KitBOMSummary_Sp = Nothing
    
    If Not (A_AIM_KitBOMSummary_Upd_Sp Is Nothing) Then Set A_AIM_KitBOMSummary_Upd_Sp.ActiveConnection = Nothing
    Set A_AIM_KitBOMSummary_Upd_Sp = Nothing
    
'    RtnCode = SQLConnection(Cn1, CONNECTION_CLOSE, False)
'    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
    
End Function


Private Function AIMSeasons_Add(SeasSmoothingPds As Integer, SaWork As SAWORK_RCD, _
    rs As ADODB.Recordset)
On Error GoTo ErrorHandler

    Dim J As Integer
    Dim X As Integer
    Dim Pd As Integer
    Dim si As Double
    Dim SiTotal As Double
    Dim Work(1 To 52) As Double
    
    Pd = SeasSmoothingPds
    X = SeasSmoothingPds \ 2
    
    'Smooth Results
    'Results are smoothing using values from future and past periods
    'For example: if smoothing is based on five (5) periods, the results
    'would be based on averaging each period + the next two (2) periods
    ' + the previous two (2) periods.
    
    For Pd = LBound(SaWork.SumSales) To UBound(SaWork.SumSales)
        si = 0
        For J = (Pd - X) To (Pd + X)
            si = si + SaWork.SumSales(GetAbsPd(J))
        Next J
        
        Work(Pd) = Round(si / ((X * 2) + 1), 2)
        
    Next Pd
    
'    If Not f_IsRecordsetOpenAndPopulated(rs) Then Exit Function
    rs.AddNew
    
    rs("said").Value = SaWork.SaId
    rs("saversion").Value = SaWork.SAVersion
    rs("sadesc").Value = SaWork.SaDesc
    rs("salevel").Value = "D"
    rs("lcid").Value = SaWork.LcId
    rs("class").Value = SaWork.Class
    
    For Pd = 1 To 52
        If SaWork.TotSales > (5.2 * SaWork.NbrYears) Then
            If Pd = 52 Then
                rs("bi52").Value = 52 - SiTotal
            Else
                rs("bi" & Format(Pd, "00")).Value = (Work(Pd) / SaWork.TotSales) * 52
                SiTotal = SiTotal + (Work(Pd) / SaWork.TotSales) * 52
            End If
            
        Else
            rs("bi" & Format(Pd, "00")).Value = 1
        End If
    
    Next Pd
    
    rs("AvgUnits").Value = Round(SaWork.TotSales / 52, 1)
    
    rs("itmcnt").Value = SaWork.ItmCnt
    
    'Write Seasonality Record
    rs.Update
    
    'Clear Summary Sales Arry
    SaWork.SaId = ""
    SaWork.SaDesc = ""
    SaWork.TotSales = 0
    SaWork.ItmCnt = 0
    For Pd = 1 To 52
        SaWork.SumSales(Pd) = 0
    Next Pd

Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(AIMSeasons_Add)", _
            0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::AIMSeasons_Add", Now, gDRJobError, True, Err
    
End Function
Private Function DataInterface(TxnSet As String, FileName As String)
On Error GoTo ErrorHandler

    Dim Cn As ADODB.Connection
    Dim AIM_TestLocationId_Sp As ADODB.Command
    Dim Sp As ADODB.Command
    
    Dim rsSysCtrl As ADODB.Recordset
    
    Dim DxPath As String
    Dim DxPath_BackUp As String
    Dim FileCount As Integer
    Dim FileNames() As Variant
    Dim i As Integer
    Dim J As Integer
    Dim LcId As String
    Dim LogFile As String
    Dim strMessage As String
    Dim NewFileName As String
    Dim RtnCode As Integer
    Dim SPName As String
    Dim IndexCounter As Integer
    Dim LineCount As Integer, InsertCount As Integer
    Dim TempString As String
    Dim CompleteLogMsg As String
    
    
    'Open a connection to the server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    
    Select Case TxnSet
    Case g_DI_TT_DD
        SPName = "AIM_DxDD_Sp"
    Case g_DI_TT_LT
        SPName = "AIM_DxLT_Sp"
    Case g_DI_TT_HS
        SPName = "AIM_DxHS_Sp"
    Case g_DI_TT_SD
        SPName = "AIM_DxSD_Sp"
    Case g_DI_TT_SS
        SPName = "AIM_DxSS_Sp"
    Case g_DI_TT_VN
        SPName = "AIM_DxVN_Sp"
    Case g_DI_TT_SL
        SPName = "AIM_DxSL_Sp"
    Case g_DI_TT_LC
        SPName = "AIM_DxLC_Sp"
    Case g_DI_TT_RO
        SPName = "AIM_DxRO_Sp"
    Case g_DI_TT_RP
        SPName = "AIM_DxRP_Sp"
    Case g_DI_TT_RS
        SPName = "AIM_DxRS_Sp"
    Case g_DI_TT_AS
        SPName = "AIM_DxAS_Sp"
    Case g_DI_TT_IS
        SPName = "AIM_DxIS_Sp"
    Case g_DI_TT_KB
        SPName = "AIM_DxKB_Sp"
    Case g_DI_TT_FA
        SPName = "AIM_DxFA_Sp"
    End Select

    'Initialize the System Control Table
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    GetSysInfo Cn, rsSysCtrl
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
    DxPath = Trim$(rsSysCtrl("DxPath").Value)
    DxPath_BackUp = Trim$(rsSysCtrl("DxPath_BackUp").Value)
    LogFile = Trim$(rsSysCtrl("LogFile").Value)
    gDateFormat = rsSysCtrl!DateFormat
    gTimeFormat = rsSysCtrl!TimeFormat
    m_LogFile = LogFile
    gLogFile = m_LogFile
    
    gWantToLogToDB = rsSysCtrl("WantErrorsTobeLoggedToDatabase").Value
    
    
    'Close System Control
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Append "/", if required
    If Right(DxPath, 1) <> "\" Then
        DxPath = DxPath & "\"
    End If
    
    If Right(DxPath_BackUp, 1) <> "\" Then
        DxPath_BackUp = DxPath_BackUp & "\"
    End If
    
    'Define Stored Procedure
    Set AIM_TestLocationId_Sp = New ADODB.Command
    With AIM_TestLocationId_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_TestLocationId_Sp"
        .Parameters.Refresh
    End With
    
    Set Sp = New ADODB.Command
    With Sp
        Set .ActiveConnection = Cn
        .CommandText = SPName
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    
    'Write message to log file
    Write_Log LogFile, "", 0, False, True, True, False
    TempString = getTranslationResource("Data Interface Started")
    Write_Log LogFile, TempString, 0, False, True, False, True
    Call f_HandleErr(0, TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , True)
    
    
    'Output Command Line Parameters
    TempString = getTranslationResource("Txn Set", True) & CStr(TxnSet)
    Write_Log LogFile, TempString, 2, False, False, False, False
    TempString = vbNewLine & TempString
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
    
    
    TempString = getTranslationResource("File Name", True) & CStr(FileName)
    Write_Log LogFile, TempString, 2, False, True, False, False
    TempString = vbNewLine & TempString
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
    
    
    TempString = getTranslationResource("Log File", True) & CStr(LogFile)
    Write_Log LogFile, TempString, 2, False, True, False, False
    TempString = vbNewLine & TempString
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
    
    
    'Load the File Name Array
    If UCase(FileName) = "ALL" Or UCase(FileName) = getTranslationResource("ALL") Then
        FileCount = GetFileNames(DxPath, TxnSet, FileNames())
    Else
        ReDim FileNames(0 To 0)
        FileNames(0) = FileName
        FileCount = 1
    End If
    
    'Check to see if a file name was returned
    If FileCount > 0 Then
        For i = LBound(FileNames) To UBound(FileNames)
            'Parse the Location Id from the File Name
            LcId = ParseLocationFmFileName(FileNames(i))
            
            'Test the existence of the Location Id
            With AIM_TestLocationId_Sp
                .Parameters("@LcId").Value = LcId
                .Execute
            End With
            
            'Process the Data Interface File -- If it is a valid AIM Location
            If AIM_TestLocationId_Sp.Parameters(0).Value = SUCCEED Then
                'Log the file name
                TempString = getTranslationResource("File", True) & CStr(FileNames(i))
                Write_Log LogFile, TempString, 2, False, False, False, True
                Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)


                    
                'Process the file
                Sp.Parameters("@FileName").Value = FileNames(i)
                Sp.Parameters("@InsertCounter").Value = 0
                Sp.Parameters("@UpdateCounter").Value = 0
                Select Case TxnSet
                Case g_DI_TT_DD, g_DI_TT_SD
                    Sp.Parameters("@RowCounter").Value = 0
                Case g_DI_TT_SS, g_DI_TT_SL
                    Sp.Parameters("@RowCounter").Value = 0
                    Sp.Parameters("@PurgedCounter").Value = 0
                    Sp.Parameters("@ErrorCounter").Value = 0
                Case g_DI_TT_AS
                    Sp.Parameters("@FileLog").Value = " "
                Case g_DI_TT_FA
                    Sp.Parameters("@FileLineCounter").Value = 0
                End Select
                
                Sp.Execute
                'Check the return value
                Select Case Sp.Parameters(0).Value
                Case FAIL
                    If Err.Number <> 0 Then
                        strMessage = Err.source & "(DataInterface_Main.DataInterface)" & _
                                    getTranslationResource("In-Process Error", True) & _
                                    CStr(Format(Err.Number, "##0")) & ", " & Err.Description
                        
                        Write_Log LogFile, strMessage, 2, False, False, False, False
                        Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                        
                    Else
                        strMessage = Err.source & "(DataInterface_Main.DataInterface)" & _
                                    getTranslationResource("Unknown Error -- Processing terminated In-Process.")
                        Write_Log LogFile, strMessage, 2, False, False, False, False
                        Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    End If
                    
                    
                Case SUCCEED
                    If Err.Number <> 0 Then
                        strMessage = Err.source & "(DataInterface_Main.DataInterface)" & _
                                    getTranslationResource("In-Process Error", True) & _
                                    CStr(Format(Err.Number, "##0")) & ", " & Err.Description
                        Write_Log LogFile, strMessage, 2, False, False, False, False
                        Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    End If
                    
                Case TOOMANY
                    strMessage = Err.source & "(DataInterface_Main.DataInterface)" & _
                                getTranslationResource("Duplicate File -- Processing aborted.")
                    Write_Log LogFile, strMessage, 2, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    
                
                Case -1 'No Data Found
                    strMessage = Err.source & "(DataInterface_Main.DataInterface)" & _
                                getTranslationResource("Invalid data -- Processing not possible.")
                    Write_Log LogFile, strMessage, 2, False, False, False, False
                    
                    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                
                End Select
                
                'Rename and move the input file to the backup directory
                If Sp.Parameters(0) = SUCCEED Then
                    NewFileName = DxPath_BackUp & Replace(FileNames(i), ".TXT", ".BAK", , , vbTextCompare)
                    Name DxPath & FileNames(i) As NewFileName
                End If
              
                'Log the counters returned by the stored procs
                strMessage = getTranslationResource("Inserts", True) & Format(CStr(Sp.Parameters("@InsertCounter").Value), "#,##0")
                Write_Log LogFile, strMessage, 4, False, False, False, False
                Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                Select Case TxnSet
                Case g_DI_TT_DD, g_DI_TT_SD
                    TempString = getTranslationResource("Updates", True) & Format(CStr(Sp.Parameters("@UpdateCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    
                    TempString = getTranslationResource("Rows", True) & Format(CStr(Sp.Parameters("@RowCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                Case g_DI_TT_SS, g_DI_TT_SL
                    TempString = getTranslationResource("Updates", True) & Format(CStr(Sp.Parameters("@UpdateCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    TempString = getTranslationResource("Rows", True) & Format(CStr(Sp.Parameters("@RowCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    TempString = getTranslationResource("Purged", True) & Format(CStr(Sp.Parameters("@PurgedCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    TempString = getTranslationResource("Errors", True) & Format(CStr(Sp.Parameters("@ErrorCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                Case g_DI_TT_VN
                    TempString = getTranslationResource("Updates", True) & Format(CStr(Sp.Parameters("@UpdateCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                Case g_DI_TT_AS
                    TempString = getTranslationResource("Updates", True) & Format(CStr(Sp.Parameters("@UpdateCounter").Value), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    TempString = getTranslationResource("FileLog", True) & CStr(Sp.Parameters("@FileLog").Value)
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                Case g_DI_TT_FA
                    LineCount = Sp.Parameters("@FileLineCounter").Value
                    InsertCount = Sp.Parameters("@InsertCounter").Value
                    
                    TempString = getTranslationResource("Rows", True) & Format(CStr(LineCount), "#,##0")
                    Write_Log LogFile, TempString, 4, False, False, False, False
                    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    
                    'Check if the file contained lines that did not insert
                    If InsertCount < LineCount Then
                        'Log the lines that have failed.
                        LogDxFAErrors Cn, LogFile
                    Else
                        TempString = getTranslationResource("All forecast adjustments in this file have been logged.")
                        Write_Log LogFile, TempString, 4, False, False, False, True
                        Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                    End If
                End Select
                
            Else
                'Location does not exist...
                TempString = getTranslationResource("File", True) & CStr(FileNames(i))
                Write_Log LogFile, TempString, 2, False, False, False, True
                Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
                
                TempString = CStr(LcId) & " -- " & getTranslationResource("Invalid Location -- Processing aborted.")
                Write_Log LogFile, TempString, 2, False, True, False, False
                Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
            End If
        Next i
    End If
    If TxnSet = g_DI_TT_SD Or TxnSet = g_DI_TT_DD Then
        'Call the function  to update the forecastrepositorydetail tables qtyactualordered and
        'qtyactualshipped fields
        AIMPopulateRepositoryActuals
    End If
    
    'Write message to log file
    TempString = getTranslationResource("Data Interface Complete.")
    Write_Log LogFile, TempString, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRJobUpdate, False, , False)
    
    Write_Log LogFile, "", 0, False, True, True, False

CleanUp:
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    If Not (Sp Is Nothing) Then Set Sp.ActiveConnection = Nothing
    Set Sp = Nothing
    
    If Not (AIM_TestLocationId_Sp Is Nothing) Then Set AIM_TestLocationId_Sp.ActiveConnection = Nothing
    Set AIM_TestLocationId_Sp = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
        
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(DataInterface)", _
            0, True, True, True, True
    Call f_HandleErr(, , "DataInterface_Main::DataInterface", "DataInterface_Main::DataInterface", Now(), gDRGeneralError, False)
    
    
    On Error Resume Next
    GoTo CleanUp
    
End Function

Private Function MDCDetail(Simdata As SIMULATION_RCD, p_AIM_MDCDetail_Sp As ADODB.Command, AIMMethod As METHOD_RCD, _
    P_ApplyMasterAdj As Boolean, P_AIM_GetFcstAdj_Sp As ADODB.Command, p_rsAIM_GetFcstAdj As ADODB.Recordset)
On Error GoTo ErrorHandler

    Dim Avail(1 To 26) As Long
    Dim AvailQty As Long
    Dim Fcst As Double
    Dim FcstDate As Date
    Dim OrderQty As Double
    Dim Pd As Integer
    Dim PdOffSet As Integer
    Dim PlnOrder As Long
    Dim RqQty(1 To 26) As Long
    Dim ReviewDate As Date
    
    Dim EndDate As Date
    Dim StartDate As Date
    
    'Initialize Forecast Date
    FcstDate = Format(Date, g_ISO_DATE_FORMAT)
    
    'Initialize availability/planned order quantity
    AvailQty = Simdata.It.Oh + Simdata.It.Oo - Simdata.It.ComStk - Simdata.It.BkOrder - Simdata.It.BkComStk
    PlnOrder = 0
    
    'Planned orders are offset by the accumulative leadtime
    'Disable the Period offset calculate -- It probably doesn't make sense
    'PdOffSet = Round(SimData.It.Accum_Lt / 7, 0)
    PdOffSet = 0
    
    'Calculate future demand for the next twenty six (26) weeks
    For Pd = LBound(RqQty) To UBound(RqQty)
        'Kevin hill chnage the startdate of the requirements to today's date so requirements will match
        'any purchase orders generated for dependent locations today
        StartDate = Format(Simdata.DM.BaseDate + ((Pd - 1) * 7), g_ISO_DATE_FORMAT)
        'StartDate = Format(date + ((Pd - 1) * 7), g_ISO_DATE_FORMAT)
        EndDate = Format(StartDate + 6, g_ISO_DATE_FORMAT)
        Fcst = Forecast(StartDate, EndDate, AIMMethod.ApplyTrend, AIMMethod.ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_GetFcstAdj_Sp, p_rsAIM_GetFcstAdj)
        
        Select Case Pd
        Case 1
            'Kevin Hill  Starting inventory for the period should not have the forecast subsracted.
            'But should have the inventory burn taken care of ( to be consistent with rest of AIM e.g. PO's
            'Avail(Pd) = AvailQty - Fcst
            Avail(Pd) = AvailQty - (Date - Simdata.LC.StkDate) * Simdata.It.FcstLT / Simdata.It.Accum_Lt
            ReviewDate = Date
            
        Case Else
            Avail(Pd) = Avail(Pd - 1) - Fcst
        End Select
        
        'Test the Order Point
        Select Case Simdata.It.MDCFlag
        Case "Y"        'Master Distribution Center = Forecast
            RqQty(Pd) = Fcst

        Case "N"        'Branch Distribution Center = Net Requirements
            'Kevin Hill Add a check here to see if a review day exists within the current period
            'before checking for placing an order. If a review day does not exist the dependent location
            'will not place an order on the MDC so OrderQty =0 for this period
            If StartDate <= ReviewDate And EndDate >= ReviewDate Then
                'Reviewdate wihtin period update to next reviewdate past enddate
                Do While ReviewDate <= EndDate
                    ReviewDate = ReviewDate + Simdata.It.ReviewTime
                Loop
            End If
            If Avail(Pd) + PlnOrder <= Simdata.It.OrderPt And (Pd + PdOffSet) <= 26 Then
                OrderQty = (Simdata.It.OrderPt - (Avail(Pd) + PlnOrder)) + Simdata.It.OrderQty

                'Pack round the planned requirement
                OrderQty = PackRound(OrderQty, Simdata.It.UOM, Simdata.It.ConvFactor, _
                            Simdata.It.BuyingUOM, Simdata.It.PackRounding, Simdata.It.IMin, Simdata.It.IMax)

                RqQty(Pd + PdOffSet) = OrderQty

                'Increment the Planned Order Quantity
                PlnOrder = PlnOrder + RqQty(Pd + PdOffSet)

            End If

        End Select
        
    Next Pd
    
    'Write a record to the MDC Detail Table
    p_AIM_MDCDetail_Sp.Parameters("@MDC").Value = IIf(Simdata.It.MDCFlag = "Y", Simdata.It.LcId, Simdata.It.MDC)
    p_AIM_MDCDetail_Sp.Parameters("@LcId").Value = Simdata.It.LcId
    p_AIM_MDCDetail_Sp.Parameters("@Item").Value = Simdata.It.Item
    p_AIM_MDCDetail_Sp.Parameters("@FcstDate").Value = FcstDate
    p_AIM_MDCDetail_Sp.Parameters("@AvailQty").Value = AvailQty
    p_AIM_MDCDetail_Sp.Parameters("@FcstDemand").Value = Simdata.It.FcstDemand
    p_AIM_MDCDetail_Sp.Parameters("@MAE").Value = Simdata.It.MAE
    p_AIM_MDCDetail_Sp.Parameters("@Trend").Value = Simdata.It.Trend
    
    For Pd = 1 To 26
        p_AIM_MDCDetail_Sp.Parameters("@RqQty" & Format(Pd, "00")) = RqQty(Pd)
    Next Pd
    
    p_AIM_MDCDetail_Sp.Execute
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(MDCDetail)", _
            0, True, True, True, True
    Call f_HandleErr(, , "DataInterface_Main::MDCDetail", "DataInterface_Main::MDCDetail", Now(), gDRJobError, False, Err)
    
End Function
Private Function AIMPopulateRepositoryActuals() As Integer
'On Error GoTo ErrorHandler
'    Dim Cn As ADODB.Connection
'    Dim LogFile As String
'    Dim FcstId As String
'    Dim Fcsttype As String
'    Dim FcstUpdateKey As String
'    Dim Fcst As String
'    Dim RepKey As String
'    Dim xSales As XArrayDB
'    Dim irow As Long
'    Dim iloop As Integer
'    Dim iFcstIdLoop As Integer
'    Dim iFcstIdCount As Integer
'    Dim iCol As Integer
'    Dim Pd As Integer
'    Dim RtnCode As Integer
'    Dim SqlStmt As String
'    Dim StartDate As Date
'    Dim FcstInterval As Integer
'    'Dim clsAdvForecastMod As AIMAdvForecastMod
'    Dim AIM_PopulateRepositoryActauls_Sp As ADODB.Command
'    Dim rsFcstIds As Recordset
'    Dim rsSysCtrl As Recordset
'    Dim rsCurrentPd As Recordset
'    Dim ActOrdStartInd As Integer
'    Dim CurrentPd   As Integer
'    'Set clsAdvForecastMod = New AIMAdvForecastMod
'    Set rsSysCtrl = New ADODB.Recordset
'
'    Set xSales = New XArrayDB
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
'    If RtnCode <> SUCCEED Then
'        'Write error message and terminate
'        LogFile = App.Path & "\" & "EXceedAIMDataInterface.Log"
'        Write_Log LogFile, "Batch Processor Terminated. Connection Failure", 0, False, False, False, True
'        Err.Description = "Batch Processor Terminated. Connection Failure"
'        GoTo ErrorHandler
'    End If
'
'    RtnCode = GetSysInfo(Cn, rsSysCtrl)
'    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
'        Err.Description = "SysCtrl Empty Recordset"
'        GoTo ErrorHandler
'    Else
'        LogFile = rsSysCtrl("LogFile").Value
'    End If
'
'    Set AIM_PopulateRepositoryActauls_Sp = New ADODB.Command
'    With AIM_PopulateRepositoryActauls_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_PopulateRepositoryActauls_Sp"
'        .Parameters.Refresh
'    End With
'        SqlStmt = "select aimforecast.FcstStartDate, aimforecast.FcstInterval, " & _
'        " aimforecast.fcstid,repositorykey from aimforecast inner join forecastrepository " & _
'        " on aimforecast.fcstid =forecastrepository.fcstid and aimforecast.fcststatus =1"
'        Set rsFcstIds = New ADODB.Recordset
'        rsFcstIds.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
'        'Validate the recordset
'        If f_IsRecordsetOpenAndPopulated(rsFcstIds) Then
'            iFcstIdCount = rsFcstIds.RecordCount
'        End If
'
'    For iFcstIdLoop = 1 To iFcstIdCount
'
'        FcstId = rsFcstIds("fcstid").Value
'        StartDate = rsFcstIds("FcstStartDate").Value
'        FcstInterval = rsFcstIds("FcstInterval").Value
'        RepKey = rsFcstIds("repositorykey").Value
'        'clsAdvForecastMod.GetSalesAndOrders FcstId, xSales, StartDate, FcstInterval
'        GetSalesAndOrders FcstId, xSales, StartDate, FcstInterval
'        SqlStmt = ""
'        SqlStmt = "select CurrentPd= COALESCE(max(fcstpds),0) from forecastrepositorydetail " & _
'                 " where  fcstpdstartdate <= getdate() and Repositorykey = " & RepKey
'
'        If f_IsRecordsetValidAndOpen(rsCurrentPd) Then rsCurrentPd.Close
'        Set rsCurrentPd = Nothing
'        Set rsCurrentPd = New ADODB.Recordset
'        'Refresh recordset for given forecast ID
'        rsCurrentPd.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
'        'Validate recordset
'        If f_IsRecordsetOpenAndPopulated(rsCurrentPd) Then
'             CurrentPd = rsCurrentPd("CurrentPd").Value
'        End If
'
'        'Populate the ForecastRepositoryDetail  table with data
'        ActOrdStartInd = (xSales.UpperBound(1) + 1) / 2
'        For irow = 0 To ActOrdStartInd - 1
'            Pd = 0
'            For iCol = 13 To xSales.UpperBound(2) - 8
'                Pd = Pd + 1
'                If Pd <= CurrentPd Then
'                With AIM_PopulateRepositoryActauls_Sp
'                    .Parameters("@RepositoryKey") = RepKey
'                    .Parameters("@lcid") = xSales(irow, 0)
'                    .Parameters("@item") = xSales(irow, 1)
'                    .Parameters("@FcstPds") = Pd
'                    .Parameters("@QtyActualShipped") = xSales(irow, 12 + Pd)
'                    .Parameters("@QtyActualOrdered") = xSales((ActOrdStartInd + irow), 12 + Pd)
'                    AIM_PopulateRepositoryActauls_Sp.Execute
'                End With
'                RtnCode = AIM_PopulateRepositoryActauls_Sp.Parameters(0).Value
'                Else
'                 Exit For
'                End If
'
'
'            Next iCol
'        Next irow
'        xSales.Clear
'        If f_IsRecordsetValidAndOpen(rsCurrentPd) Then rsCurrentPd.Close
'        rsFcstIds.MoveNext
'    Next iFcstIdLoop
'
''Clean up
'GracefulExit:
'    CleanUpMain
'    If Not (AIM_PopulateRepositoryActauls_Sp Is Nothing) Then Set AIM_PopulateRepositoryActauls_Sp.ActiveConnection = Nothing
'    Set AIM_PopulateRepositoryActauls_Sp = Nothing
'    If f_IsRecordsetValidAndOpen(rsFcstIds) Then rsFcstIds.Close
'    Set rsFcstIds = Nothing
'    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
'    Set rsSysCtrl = Nothing
'    If f_IsRecordsetValidAndOpen(rsCurrentPd) Then rsCurrentPd.Close
'    Set rsCurrentPd = Nothing
'Exit Function
'ErrorHandler:
'    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
'            Err.Description & " -- " & _
'            Err.source & "(DataInterface_Main)" & "(AIMBatchForecastGenerator)", _
'            0, True, True, True, True
'    GoTo GracefulExit
    
End Function

Private Function AIMBatchDemandPlanner(ArgFcstId As String) As Integer
'On Error GoTo ErrorHandler
'
'    Dim Cn As ADODB.Connection
'    Dim LogFile As String
'    Dim FcstId As String
'    Dim Rtn As Boolean
'    Dim RtnCode As Integer
'    Dim xForecastList As XArrayDB
'    Dim RowCounter As Long
'    Dim ColCounter As Integer
'    Dim ForecastCriteria As FORECAST_CRITERIA
'    Dim ForecastData As FORECAST_DATA
'
'    Write_Log LogFile, getTranslationResource("Batch Demand Planner Started"), 0, False, True, False, True
'
'    'Open a connection to the Server
'    Set Cn = New ADODB.Connection
'    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
'    If RtnCode <> SUCCEED Then
'        'Write error message and terminate
'        LogFile = App.Path & "\" & "EXceedAIMDataInterface.Log"
'        Write_Log LogFile, "Batch Processor Terminated. Connection Failure", 0, False, False, False, True
'        Err.Description = "Batch Processor Terminated. Connection Failure"
'        GoTo ErrorHandler
'    End If
'
'    'Get Forecast IDs and Descriptions from AIMForecast
'    If UCase(ArgFcstId) = "ALL" Then
'        Rtn = gList_FcstIDs(Cn, xForecastList, "BatchJob")
'
'        If Rtn = True Then
'            For RowCounter = xForecastList.LowerBound(1) To xForecastList.UpperBound(1)
'                Write_Log LogFile, getTranslationResource("Fcst Id", True) & CStr(xForecastList(RowCounter, 0)) & ": " & getTranslationResource("Processing Started"), 2, False, False, False, False
'                RtnCode = gGen_ForecastData(Cn, xForecastList(RowCounter, 0), False, ForecastCriteria, ForecastData)
'                Write_Log LogFile, getTranslationResource("Fcst Id", True) & CStr(xForecastList(RowCounter, 0)) & ": " & getTranslationResource("Processing Completed"), 2, False, False, False, False
'            Next
'        End If
'    Else
'        Write_Log LogFile, getTranslationResource("Fcst Id", True) & ArgFcstId & ": " & getTranslationResource("Processing Started"), 2, False, False, False, False
'        RtnCode = gGen_ForecastData(Cn, ArgFcstId, False, ForecastCriteria, ForecastData)
'        If RtnCode <= 0 Then
'            Write_Log LogFile, getTranslationResource("Fcst Id", True) & ArgFcstId & ": " & getTranslationResource("Processing Completed  With Errors"), 2, False, False, False, False
'        Else
'            Write_Log LogFile, getTranslationResource("Fcst Id", True) & ArgFcstId & ": " & getTranslationResource("Processing Completed"), 2, False, False, False, False
'        End If
'    End If
'
'    Write_Log LogFile, getTranslationResource("Batch Demand Planner Ended "), 0, False, True, False, True
'
'Exit Function
'ErrorHandler:
'    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
'            Err.Description & " -- " & _
'            Err.source & "(DataInterface_Main)" & "(AIMBatchDemandPlanner)", _
'            0, True, True, True, True
'    Write_Log LogFile, getTranslationResource("Batch Demand Planner Ended With Errors"), 0, False, True, False, True
End Function

Private Function AIMBatchForecastGenerator(ArgFcstId As String) As Integer
On Error GoTo ErrorHandler
    Dim Cn As ADODB.Connection
    Dim LogFile As String
    Dim FcstId As String
    Dim FcstSetupKey As Long
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim iFcstIdCount As Integer
    Dim AllFcstIds As Boolean
    Dim iFcstIdLoop As Integer
    Dim FcstStartDate As Date
    Dim FcstInterval As AIM_INTERVALS
    Dim FcstRolling As Boolean
    Dim FcstHistory As Boolean
    Dim FcstPds_Future As Integer
    Dim FcstPds_Historical As Integer
    Dim FreezePds As Integer
    Dim Calc_SysFcst As Boolean
    Dim Calc_Netreq As Boolean
    Dim Calc_HistDmd As Boolean
    Dim Calc_MasterFcstAdj As Boolean
    Dim Calc_FcstAdj As Boolean
    Dim Calc_AdjNetReq As Boolean
    Dim Calc_ProjInv    As Boolean
    Dim Calc_ProdConst As Boolean
    Dim rsFcstIds As Recordset
    Dim rsFcstData As Recordset
    Dim rsSysCtrl As Recordset
    Dim FcstTypes() As String
    Dim UESelected(0) As String
    Dim MainArray As New XArrayDB
    Dim m_xaOrigCriteria As XArrayDB
    Dim i As Integer
    Dim J As Integer
    Dim TempString As String
'
'   ' Set clsAdvForecastMod = New AIMAdvForecastMod
     Set rsSysCtrl = New ADODB.Recordset
'    Set xTemp = New XArrayDB
'
     FcstId = ArgFcstId
'    'Open a connection to the Server
     Set Cn = New ADODB.Connection
     RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    If RtnCode <> SUCCEED Then
        'Write error message and terminate
        LogFile = App.Path & "\" & "EXceedAIMDataInterface.Log"
        Write_Log LogFile, "Batch Processor Terminated. Connection Failure", 0, False, False, False, True
        Err.Description = "Batch Processor Terminated. Connection Failure"
        Call f_HandleErr(0, Err.Description, "DataInterface_Main::AIMBatchForecastGenerator", "DataInterface_Main::AIMBatchForecastGenerator", Now(), gDRJobError, False, , True)
        GoTo ErrorHandler
    End If

    RtnCode = GetSysInfo(Cn, rsSysCtrl)
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    Else
        LogFile = rsSysCtrl("LogFile").Value
    End If
    'Write message to log file
    Write_Log LogFile, "", 0, False, True, True, False
    Write_Log LogFile, "Batch Fcst Generation Started ", 0, False, True, False, True
    
    'This is first update from this job - sgajjela
    Call f_HandleErr(0, "Batch Fcst Generation Started ", "DataInterface_Main::AIMBatchForecastGenerator", "DataInterface_Main::AIMBatchForecastGenerator", Now(), gDRJobUpdate, False, , True)

    If UCase(FcstId) = UCase(getTranslationResource("ALL")) Then
        AllFcstIds = True
        SqlStmt = "Select FcstId from AIMFcstSetUp where FcstLocked =1"
        Set rsFcstIds = New ADODB.Recordset
        rsFcstIds.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
        'Validate the recordset
        If f_IsRecordsetOpenAndPopulated(rsFcstIds) Then
            iFcstIdCount = rsFcstIds.RecordCount
        Else
            iFcstIdCount = 0
            GoTo GracefulExit
        End If
    Else
        AllFcstIds = False
        iFcstIdCount = 1
    End If

    For iFcstIdLoop = 1 To iFcstIdCount
        If AllFcstIds = True Then
            FcstId = rsFcstIds(0).Value
        Else
            'use the one supplied by the user
        End If

        SqlStmt = ""
       
        SqlStmt = "SELECT FcstSetUpKey,FcstStartDate, " & _
                  " FcstRolling, FcstHistory, " & _
                  "FcstPds_Future, FcstPds_Historical, " & _
                  "FreezePds,FcstInterval, " & _
                  "Calc_SysFcst,Calc_NetReq,Calc_HistDmd,Calc_MasterFcstAdj, " & _
                  "Calc_FcstAdj,Calc_AdjNetReq,Calc_ProjInv,Calc_ProdConst " & _
                " From AIMFcstSetup " & _
                " WHERE FcstId = N'" & FcstId & "'"
        'Clear out existing stuff
        If f_IsRecordsetValidAndOpen(rsFcstData) Then rsFcstData.Close
        Set rsFcstData = Nothing
        Set rsFcstData = New ADODB.Recordset
        'Refresh recordset for given forecast ID
        rsFcstData.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
        'Validate recordset
        If f_IsRecordsetOpenAndPopulated(rsFcstData) Then
            'Populate the local variables from the rsFcstMaint record set
            FcstSetupKey = rsFcstData("FcstSetUpKey").Value
            FcstStartDate = rsFcstData("FcstStartDate").Value
            FcstInterval = rsFcstData("FcstInterval").Value
            FcstRolling = rsFcstData("FcstRolling").Value
            FcstHistory = rsFcstData("FcstHistory").Value
            FcstPds_Future = rsFcstData("FcstPds_Future").Value
            FcstPds_Historical = rsFcstData("FcstPds_Historical").Value
            FreezePds = rsFcstData("FreezePds").Value
            Calc_SysFcst = rsFcstData("Calc_SysFcst").Value
            Calc_Netreq = rsFcstData("Calc_Netreq").Value
            Calc_HistDmd = rsFcstData("Calc_HistDmd").Value
            Calc_MasterFcstAdj = rsFcstData("Calc_MasterFcstAdj").Value
            Calc_FcstAdj = rsFcstData("Calc_FcstAdj").Value
            Calc_AdjNetReq = rsFcstData("Calc_AdjNetReq").Value
            Calc_ProjInv = rsFcstData("Calc_ProjInv").Value
            Calc_ProdConst = rsFcstData("Calc_ProdConst").Value
        End If
        ReDim FcstTypes(0 To 7)
        J = 0
        If Calc_SysFcst = True Then
            FcstTypes(J) = "SYSFCST"
            J = J + 1
        End If
        If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "MASTERFCSTADJ"
            J = J + 1
        End If
        If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "FCSTADJ"
            J = J + 1
        End If
        If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "NETREQ"
            J = J + 1
        End If
       If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "ADJNETREQ"
            J = J + 1
        End If
       If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "PROJINV"
            J = J + 1
        End If
       If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "HISDMD"
            J = J + 1
        End If
       If Calc_MasterFcstAdj = True Then
            FcstTypes(J) = "PRODCONST"
            J = J + 1
        End If
    If J <> 0 Then
        ReDim Preserve FcstTypes(0 To J - 1)
    End If
    
    RtnCode = g_SelectFilters(Cn, FcstSetupKey, m_xaOrigCriteria, COMP_EMPTY)
    MainArray.Clear
    'we always save the fcstdata and any userelemets that do not have there data populated
    GetForecastMaint FcstId, MainArray, "01/01/2004", FcstInterval, m_xaOrigCriteria, FcstTypes(), UESelected(), True, True
    'Write message to log file
    TempString = getTranslationResource("Batch Fcst Successfully Generated for FcstId ", True) & CStr(FcstId)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update of this job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::AIMBatchForecastGenerator", "DataInterface_Main::AIMBatchForecastGenerator", Now(), gDRJobUpdate, False, , False)
    
        If AllFcstIds = True Then
            If f_IsRecordsetValidAndOpen(rsFcstData) Then rsFcstData.Close
            rsFcstIds.MoveNext
        End If
    Next iFcstIdLoop
    
    TempString = getTranslationResource("Batch Fcst Generation Successfull Completed")
    Write_Log LogFile, TempString, 0, True, False, False, True
    
    'This is subsequent & final update of this job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::AIMBatchForecastGenerator", "DataInterface_Main::AIMBatchForecastGenerator", Now(), gDRJobUpdate, False, , False)
    
    Write_Log LogFile, "", 0, False, True, True, False
''Clean up
GracefulExit:
'    CleanUpMain
    If f_IsRecordsetValidAndOpen(rsFcstData) Then rsFcstData.Close
    Set rsFcstData = Nothing
    If f_IsRecordsetValidAndOpen(rsFcstIds) Then rsFcstIds.Close
    Set rsFcstIds = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
'
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(AIMBatchForecastGenerator)", _
            0, True, True, True, True
    
    Call f_HandleErr(0, , "DataInterface_Main::AIMBatchForecastGenerator", "DataInterface_Main::AIMBatchForecastGenerator", Now(), gDRJobUpdate)

    GoTo GracefulExit
    
End Function



Private Function ParseLocationFmFileName(FileName As Variant)
On Error GoTo ErrorHandler

    Dim p As Integer
    
    p = InStr(FileName, ".")
    
    If p > 1 Then
        ParseLocationFmFileName = Left(FileName, p - 1)
    Else
        ParseLocationFmFileName = ""
    End If

Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(ParseLocationFmFileName)", _
            0, True, True, True, True
    Call f_HandleErr(, , "DataInterface_Main::ParseLocationFmFileName", "DataInterface_Main::ParseLocationFmFileName", Now(), gDRJobError, False, Err)
End Function

Private Function SetDevEnvironmentFlag() As Boolean
On Error GoTo ErrorHandler

    SetDevEnvironmentFlag = True
    IsDevEnvironment = True
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(SetDevEnvironmentFlag)", _
            0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::SetDevEnvironmentFlag", Now, gDRJobError, True, Err
            
End Function

Private Function GetSysInfo(ArgCn As Connection, ArgSysCtrl As Recordset) As Integer
On Error GoTo ErrorHandler
'Get necessary information from SysCtrl table
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim RtnCode As Integer
    Dim LogFile As String
    
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        .ActiveConnection = ArgCn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
    End With
    
    'Get the System Control Record
    Set ArgSysCtrl = New ADODB.Recordset
    ArgSysCtrl.Open AIM_SysCtrl_Get_Sp
    If Not f_IsRecordsetOpenAndPopulated(ArgSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
GracefulExit:
    'Clean Up
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetSysInfo)", _
            0, True, True, True, True

    f_HandleErr , , , "DataInterface_Main::GetSysInfo", Now, gDRJobError, True, Err

            
    GoTo GracefulExit
End Function


Private Function UpdateDemand(p_AIM_DemandUpdate_Sp As ADODB.Command, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    With p_AIM_DemandUpdate_Sp
        .Parameters(1).Value = Simdata.It.LcId
        .Parameters(2).Value = Simdata.It.Item
        .Parameters(3).Value = Simdata.It.ItStat
        .Parameters(4).Value = Trim$(Simdata.It.ById)
        .Parameters(5).Value = Simdata.It.LeadTime
        .Parameters(6).Value = Simdata.It.FcstMethod
        .Parameters(7).Value = Format(Simdata.It.FcstDemand, "0.00")
        .Parameters(8).Value = Format(Simdata.It.MAE, "0.00")
        .Parameters(9).Value = Format(Simdata.It.MSE, "0.00")
        .Parameters(10).Value = Format(Simdata.It.Trend, "0.00000")
        .Parameters(11).Value = Simdata.It.FcstCycles
        .Parameters(12).Value = Simdata.It.ZeroCount
        .Parameters(13).Value = Simdata.It.DIFlag
        .Parameters(14).Value = Simdata.It.DmdFilterFlag
        .Parameters(15).Value = Simdata.It.TrkSignalFlag
        .Parameters(16).Value = Simdata.It.UserDemandFlag
        .Parameters(17).Value = Format(Simdata.It.ByPassPct, "0.000")
        .Parameters(18).Value = Simdata.It.FcstUpdCyc
        .Parameters(19).Value = Format(Simdata.It.DSer, "0.000")
        .Parameters(20).Value = Simdata.It.Accum_Lt
        .Parameters(21).Value = Simdata.It.ReviewTime
        .Parameters(22).Value = Simdata.It.OrderPt
        .Parameters(23).Value = Simdata.It.OrderQty
        .Parameters(24).Value = Format(Simdata.It.SafetyStock, "0")
        .Parameters(25).Value = Format(Simdata.It.FcstRT, "0.00")
        .Parameters(26).Value = Format(Simdata.It.FcstLT, "0.00")
        .Parameters(27).Value = Format(Simdata.It.Fcst_Month, "0.00")
        .Parameters(28).Value = Format(Simdata.It.Fcst_Quarter, "0.00")
        .Parameters(29).Value = Format(Simdata.It.Fcst_Year, "0.00")
        .Parameters(30).Value = Format(Simdata.It.Mean_NZ, "0.00")
        .Parameters(31).Value = Format(Simdata.It.StdDev_NZ, "0.00")
        .Parameters(32).Value = Format(Simdata.It.IntSafetyStock, "0.00")
        .Parameters(33).Value = Simdata.It.IsIntermittent
        
        .Execute , , adCmdStoredProc + adExecuteNoRecords

    End With
    
    UpdateDemand = p_AIM_DemandUpdate_Sp(0).Value
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(UpdateDemand)", _
            0, True, True, True, True
    
    f_HandleErr , , , "DataInterface_Main::UpdateDemand", Now, gDRJobError, True, Err
            
End Function
Private Function UpdateDemandForCompItem(p_AIM_DemandUpdateForCompItems_Sp As ADODB.Command, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    With p_AIM_DemandUpdateForCompItems_Sp
        .Parameters(1).Value = Simdata.It.LcId
        .Parameters(2).Value = Simdata.It.Item
        .Parameters(3).Value = Format(Simdata.It.FcstDemand, "0.00")
        .Parameters(4).Value = Format(Simdata.It.FcstRT, "0.00")
        .Parameters(5).Value = Format(Simdata.It.FcstLT, "0.00")
        .Parameters(6).Value = Format(Simdata.It.Fcst_Month, "0.00")
        .Parameters(7).Value = Format(Simdata.It.Fcst_Quarter, "0.00")
        .Parameters(8).Value = Format(Simdata.It.Fcst_Year, "0.00")
        
        .Execute , , adCmdStoredProc + adExecuteNoRecords

    End With
    
    UpdateDemandForCompItem = p_AIM_DemandUpdateForCompItems_Sp(0).Value
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(UpdateDemandForCompItem)", _
            0, True, True, True, True
            
    f_HandleErr , , , "DataInterface_Main::UpdateDemandForCompItem", Now, gDRJobError, True, Err
            
End Function

'Private Function UpdateCompanionItemDetail(p_AIM_CompanionItemDetail_Sp As ADODB.Command, Simdata As SIMULATION_RCD)
'On Error GoTo ErrorHandler
' Dim Counter As Integer
' Counter = 1
' For Counter = 1 To 55
'    With p_AIM_CompanionItemDetail_Sp
'        .Parameters(1).Value = Simdata.KI.LcId
'        .Parameters(2).Value = Simdata.KI.Item
'        .Parameters(3).Value = Simdata.KI.StartDate(Counter)
'        .Parameters(4).Value = Simdata.KI.Enddate(Counter)
'        .Parameters(5).Value = Simdata.KI.Qty(Counter)
'        .Execute , , adCmdStoredProc + adExecuteNoRecords
'
'    End With
'Next
'    UpdateCompanionItemDetail = p_AIM_CompanionItemDetail_Sp(0).Value
'
'Exit Function
'ErrorHandler:
'    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
'            Err.Description & " -- " & _
'            Err.source & "(DataInterface_Main)" & "(UpdateCompanionItemDetail)", _
'            0, True, True, True, True
'End Function
'
Private Function GetKitBOMItemsLTRT(P_AIM_GetKitBOMItemsLTRT_Sp As ADODB.Command, Simdata As SIMULATION_RCD, P_rsComItemLTRT As ADODB.Recordset)
On Error GoTo ErrorHandler

Dim ICounter As Integer
Dim ItemCount As Integer
ICounter = 1
P_AIM_GetKitBOMItemsLTRT_Sp("@LcID") = Simdata.It.LcId
P_AIM_GetKitBOMItemsLTRT_Sp("@Item") = Simdata.It.Item


P_rsComItemLTRT.Open P_AIM_GetKitBOMItemsLTRT_Sp

    If f_IsRecordsetOpenAndPopulated(P_rsComItemLTRT) Then
        ReDim Preserve Simdata.KI.LT(0 To P_rsComItemLTRT.RecordCount)
        ReDim Preserve Simdata.KI.LT_RT(0 To P_rsComItemLTRT.RecordCount)
        ReDim Preserve Simdata.KI.StartDate(0 To P_rsComItemLTRT.RecordCount)
        ReDim Preserve Simdata.KI.EndDate(0 To P_rsComItemLTRT.RecordCount)
       P_rsComItemLTRT.MoveFirst
       Do Until P_rsComItemLTRT.EOF
            Simdata.KI.LcId = Simdata.It.LcId
            Simdata.KI.Item = Simdata.It.Item
            Simdata.KI.LT(ICounter) = P_rsComItemLTRT("Accum_LT")
            Simdata.KI.LT_RT(ICounter) = P_rsComItemLTRT("LT_RT")
            Simdata.KI.StartDate(ICounter) = P_rsComItemLTRT("StartDate")
            Simdata.KI.EndDate(ICounter) = P_rsComItemLTRT("EndDate")
            ICounter = ICounter + 1
            P_rsComItemLTRT.MoveNext
       Loop

    End If
'
    
        P_rsComItemLTRT.Close
   Exit Function
GracefulExit:
    'Clean Up
    If Not (P_AIM_GetKitBOMItemsLTRT_Sp Is Nothing) Then Set P_AIM_GetKitBOMItemsLTRT_Sp.ActiveConnection = Nothing
    Set P_AIM_GetKitBOMItemsLTRT_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetKitBOMItemsLTRT)", _
            0, True, True, True, True
    'sgajjela
    f_HandleErr , , , "DataInterface_Main::GetKitBOMItemsLTRT", Now, gDRJobError, True, Err
            
    GoTo GracefulExit
End Function
Private Function GetCompanionItemsLTRT(P_AIM_GetCompItemsLTRT_Sp As ADODB.Command, Simdata As SIMULATION_RCD, P_rsComItemLTRT1 As ADODB.Recordset)
On Error GoTo ErrorHandler

Dim ICounter As Integer
Dim ItemCount As Integer
ICounter = 1
P_AIM_GetCompItemsLTRT_Sp("@LcID") = Simdata.It.LcId
P_AIM_GetCompItemsLTRT_Sp("@Item") = Simdata.It.Item


P_rsComItemLTRT1.Open P_AIM_GetCompItemsLTRT_Sp

    If f_IsRecordsetOpenAndPopulated(P_rsComItemLTRT1) Then
        ReDim Preserve Simdata.CI.LT(0 To P_rsComItemLTRT1.RecordCount)
        ReDim Preserve Simdata.CI.LT_RT(0 To P_rsComItemLTRT1.RecordCount)
        ReDim Preserve Simdata.CI.StartDate(0 To P_rsComItemLTRT1.RecordCount)
        ReDim Preserve Simdata.CI.EndDate(0 To P_rsComItemLTRT1.RecordCount)
       P_rsComItemLTRT1.MoveFirst
       Do Until P_rsComItemLTRT1.EOF
            Simdata.CI.LcId = Simdata.It.LcId
            Simdata.CI.Item = Simdata.It.Item
            Simdata.CI.LT(ICounter) = P_rsComItemLTRT1("Accum_LT")
            Simdata.CI.LT_RT(ICounter) = P_rsComItemLTRT1("LT_RT")
            Simdata.CI.StartDate(ICounter) = P_rsComItemLTRT1("StartDate")
            Simdata.CI.EndDate(ICounter) = P_rsComItemLTRT1("EndDate")
            ICounter = ICounter + 1
            P_rsComItemLTRT1.MoveNext
       Loop

    End If
'
    
        P_rsComItemLTRT1.Close
   Exit Function
GracefulExit:
    'Clean Up
    If Not (P_AIM_GetCompItemsLTRT_Sp Is Nothing) Then Set P_AIM_GetCompItemsLTRT_Sp.ActiveConnection = Nothing
    Set P_AIM_GetCompItemsLTRT_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetCompanionItemsLTRT)", _
            0, True, True, True, True

    f_HandleErr , , , "DataInterface_Main::GetCompanionItemsLTRT", Now, gDRJobError, True, Err
            
    GoTo GracefulExit
End Function

Private Function GetKitBOMItemsForALoc(P_AIM_GetKitBOMItemsForALoc_Sp As ADODB.Command, P_rsKitBOMItemsForALoc As ADODB.Recordset, DistKitBOMItems() As DIST_KITBOM_ITEM_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

Dim ICounter As Integer
ICounter = 1
If f_IsRecordsetValidAndOpen(P_rsKitBOMItemsForALoc) Then
    P_rsKitBOMItemsForALoc.Close
End If
P_AIM_GetKitBOMItemsForALoc_Sp("@LcID") = Simdata.It.LcId

P_rsKitBOMItemsForALoc.Open P_AIM_GetKitBOMItemsForALoc_Sp
    If f_IsRecordsetOpenAndPopulated(P_rsKitBOMItemsForALoc) Then
        ReDim Preserve DistKitBOMItems(1 To P_rsKitBOMItemsForALoc.RecordCount)
        
       P_rsKitBOMItemsForALoc.MoveFirst
       Do Until P_rsKitBOMItemsForALoc.EOF
            DistKitBOMItems(ICounter).ItemComponent = P_rsKitBOMItemsForALoc("ItemComponent")
            DistKitBOMItems(ICounter).Fcst = P_rsKitBOMItemsForALoc("FCST")
            DistKitBOMItems(ICounter).LT = P_rsKitBOMItemsForALoc("LT")
            DistKitBOMItems(ICounter).RT = P_rsKitBOMItemsForALoc("RT")
            DistKitBOMItems(ICounter).Processed_YN = "N"
            ICounter = ICounter + 1
            P_rsKitBOMItemsForALoc.MoveNext
       Loop
        P_rsKitBOMItemsForALoc.Close
    Else
        P_rsKitBOMItemsForALoc.Close
    End If
   Exit Function
GracefulExit:
    'Clean Up
    If Not (P_AIM_GetKitBOMItemsForALoc_Sp Is Nothing) Then Set P_AIM_GetKitBOMItemsForALoc_Sp.ActiveConnection = Nothing
    Set P_AIM_GetKitBOMItemsForALoc_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetKitBOMItemsForALoc)", _
            0, True, True, True, True

    f_HandleErr , , , "DataInterface_Main::GetKitBOMItemsForALoc", Now, gDRJobError, True, Err
            
    GoTo GracefulExit
End Function
Private Function GetCompItemsForALoc(P_AIM_GetCompItemsForALoc_Sp As ADODB.Command, P_rsCompItemsForALoc As ADODB.Recordset, DistCompItems() As DIST_COMP_ITEM_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

Dim ICounter As Integer
ICounter = 1
If f_IsRecordsetValidAndOpen(P_rsCompItemsForALoc) Then
    P_rsCompItemsForALoc.Close
End If
P_AIM_GetCompItemsForALoc_Sp("@LcID") = Simdata.It.LcId
P_rsCompItemsForALoc.Open P_AIM_GetCompItemsForALoc_Sp
    If f_IsRecordsetOpenAndPopulated(P_rsCompItemsForALoc) Then
        ReDim Preserve DistCompItems(1 To P_rsCompItemsForALoc.RecordCount)
        
       P_rsCompItemsForALoc.MoveFirst
       Do Until P_rsCompItemsForALoc.EOF
            DistCompItems(ICounter).ItemComponent = P_rsCompItemsForALoc("ItemComponent")
            DistCompItems(ICounter).Fcst = P_rsCompItemsForALoc("FCST")
            DistCompItems(ICounter).LT = P_rsCompItemsForALoc("LT")
            DistCompItems(ICounter).RT = P_rsCompItemsForALoc("RT")
            ICounter = ICounter + 1
            P_rsCompItemsForALoc.MoveNext
       Loop
        P_rsCompItemsForALoc.Close
    Else
        P_rsCompItemsForALoc.Close
    End If
   Exit Function
GracefulExit:
    'Clean Up
    If Not (P_AIM_GetCompItemsForALoc_Sp Is Nothing) Then Set P_AIM_GetCompItemsForALoc_Sp.ActiveConnection = Nothing
    Set P_AIM_GetCompItemsForALoc_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetCompItemsForALoc)", _
            0, True, True, True, True

    f_HandleErr , , , "DataInterface_Main::GetCompItemsForALoc", Now, gDRJobError, True, Err
            
    GoTo GracefulExit
End Function


Private Function GetKitBOMMainItems(P_AIM_GetItemsForAKitBOMItem_Sp As ADODB.Command, P_rsItemsForAKitBOMItem As ADODB.Recordset, DistKitBOMMainItems() As DIST_KITBOM_MAIN_ITEM_RCD, CompItem As String, LcId As String)
On Error GoTo ErrorHandler
 'DistKitBOMMainItems, DistKitBOMItems(LoopCompItems)
Dim ICounter As Integer
ICounter = 1
P_AIM_GetItemsForAKitBOMItem_Sp("@KitBOMItem") = CompItem
P_AIM_GetItemsForAKitBOMItem_Sp("@LcID") = LcId

P_rsItemsForAKitBOMItem.Open P_AIM_GetItemsForAKitBOMItem_Sp
    If f_IsRecordsetOpenAndPopulated(P_rsItemsForAKitBOMItem) Then
        ReDim Preserve DistKitBOMMainItems(1 To P_rsItemsForAKitBOMItem.RecordCount)
        
       P_rsItemsForAKitBOMItem.MoveFirst
       Do Until P_rsItemsForAKitBOMItem.EOF
            DistKitBOMMainItems(ICounter).LcId = P_rsItemsForAKitBOMItem("Lcid")
            DistKitBOMMainItems(ICounter).MainItem = P_rsItemsForAKitBOMItem("Item")
            DistKitBOMMainItems(ICounter).ItemComp = P_rsItemsForAKitBOMItem("ItemComponent")
            DistKitBOMMainItems(ICounter).CompUnits = P_rsItemsForAKitBOMItem("ComponentUnits")
            DistKitBOMMainItems(ICounter).CompScrap = P_rsItemsForAKitBOMItem("ComponentScrap")
            DistKitBOMMainItems(ICounter).StartData = P_rsItemsForAKitBOMItem("StartDate")
            DistKitBOMMainItems(ICounter).EndDate = P_rsItemsForAKitBOMItem("EndDate")
            ICounter = ICounter + 1
            P_rsItemsForAKitBOMItem.MoveNext
       Loop
        
    End If
    P_rsItemsForAKitBOMItem.Close
   Exit Function
GracefulExit:
    'Clean Up
    If Not (P_AIM_GetItemsForAKitBOMItem_Sp Is Nothing) Then Set P_AIM_GetItemsForAKitBOMItem_Sp.ActiveConnection = Nothing
    Set P_AIM_GetItemsForAKitBOMItem_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetKitBOMMainItems)", _
            0, True, True, True, True

    f_HandleErr , , , "DataInterface_Main::GetKitBOMMainItems", Now, gDRJobError, True, Err
            
    GoTo GracefulExit
End Function

Private Function GetCompMainItems(P_AIM_GetItemsForACompItem_Sp As ADODB.Command, P_rsItemsForACompItem As ADODB.Recordset, DistCompMainItems() As DIST_COMP_MAIN_ITEM_RCD, CompItem As String, LcId As String)
On Error GoTo ErrorHandler
 'DistKitBOMMainItems, DistKitBOMItems(LoopCompItems)
Dim ICounter As Integer
ICounter = 1
P_AIM_GetItemsForACompItem_Sp("@CompItem") = CompItem
P_AIM_GetItemsForACompItem_Sp("@LcID") = LcId

P_rsItemsForACompItem.Open P_AIM_GetItemsForACompItem_Sp

    If f_IsRecordsetOpenAndPopulated(P_rsItemsForACompItem) Then
        ReDim Preserve DistCompMainItems(1 To P_rsItemsForACompItem.RecordCount)
        
       P_rsItemsForACompItem.MoveFirst
       Do Until P_rsItemsForACompItem.EOF
            DistCompMainItems(ICounter).LcId = P_rsItemsForACompItem("Lcid")
            DistCompMainItems(ICounter).MainItem = P_rsItemsForACompItem("MasterItem")
            DistCompMainItems(ICounter).ItemComp = P_rsItemsForACompItem("Item")
            DistCompMainItems(ICounter).CompUnits = P_rsItemsForACompItem("Qty")
            DistCompMainItems(ICounter).DemandFactor = P_rsItemsForACompItem("DemandFactor")
            DistCompMainItems(ICounter).StartData = P_rsItemsForACompItem("StartDate")
            DistCompMainItems(ICounter).EndDate = P_rsItemsForACompItem("EndDate")
            ICounter = ICounter + 1
            P_rsItemsForACompItem.MoveNext
       Loop
        
    End If
    P_rsItemsForACompItem.Close
   Exit Function
GracefulExit:
    'Clean Up
    If Not (P_AIM_GetItemsForACompItem_Sp Is Nothing) Then Set P_AIM_GetItemsForACompItem_Sp.ActiveConnection = Nothing
    Set P_AIM_GetItemsForACompItem_Sp = Nothing
Exit Function

ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(GetKitBOMMainItems)", _
            0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::GetCompMainItems", Now, gDRJobError, True, Err
            
    GoTo GracefulExit
End Function



Private Function DemandUpdate(AutoOption As String, LcidOption As String, _
    ItemOption As String, FcstYear As Integer, FcstPeriod As Integer, _
    ForceUpdateFlag As String, MDCOption As String)
On Error GoTo ErrorHandler

    Dim Cn1 As ADODB.Connection
    Dim Cn2 As ADODB.Connection
    
    Dim AIM_AIMLocations_List_Sp As ADODB.Command
    Dim AIM_AIMPromotions_GetKey_Sp As ADODB.Command
    Dim AIM_DemandUpdate_Sp As ADODB.Command
    Dim AIM_DUGet_Sp As ADODB.Command
    Dim AIM_MDCDetail_Sp As ADODB.Command
    Dim AIM_CompanionItemDetail_Sp As ADODB.Command
    Dim AIM_GetKitBOMItemsLTRT_Sp As ADODB.Command
    Dim AIM_GetKitBOMItemsForALoc_Sp As ADODB.Command
    Dim AIM_GetItemsForAKitBOMItem_Sp As ADODB.Command
    Dim AIM_GetCompItemsLTRT_Sp As ADODB.Command
    Dim AIM_GetCompItemsForALoc_Sp As ADODB.Command
    Dim AIM_GetItemsForACompItem_Sp As ADODB.Command
    Dim AIM_DemandUpdateForCompItems_Sp As ADODB.Command
    Dim AIM_DemandUpdateForKitBOMItems_Sp As ADODB.Command
    Dim AIM_KitBOMSummary_Sp As ADODB.Command
    Dim AIM_KitBOMSummary_Upd_Sp As ADODB.Command
    Dim AIM_GetFcstAdj_Sp As ADODB.Command
    
    Dim rsAIMPromotions As ADODB.Recordset
    Dim rsduGet As ADODB.Recordset
    Dim rsLc As ADODB.Recordset
    Dim rsSysCtrl As ADODB.Recordset
    Dim rsKitBOMItemsLTRT As ADODB.Recordset
    Dim rsKitBOMItemsForALoc As ADODB.Recordset
    Dim rsItemsForAKitBOMItem As ADODB.Recordset
    Dim rsComItemsLTRT As ADODB.Recordset
    Dim rsCompItemsForALoc As ADODB.Recordset
    Dim rsItemsForACompItem As ADODB.Recordset
    Dim rsAIM_KitBOMSummary As ADODB.Recordset
    Dim rsAIM_GetFcstAdj As ADODB.Recordset
    
    Dim Addl_LTDays As Integer
    Dim ByIdSource As String
    Dim FirstTime As Boolean
    Dim HsYear As Integer
    Dim HsPd As Integer
    Dim i As Integer
    Dim intCount As Long
    Dim LastUpdateCount As Long
    Dim LcProcessedFlag As Boolean
    Dim LogFile As String
    Dim strMessage As String
    Dim Pd As Integer
    Dim PrevLcId As String
    Dim PrevItem As String
    Dim RtnCode As Integer
    Dim UpdateCount As Long
    Dim IncludeMasterItemAdj As Boolean
    
    
    'Define Vendor Data Elements
    Dim Dft_RevCycle As String
    Dim Dft_ById As String
    Dim Dft_LeadTime As Integer
    
    Dim FcstResults(1 To NBR_METHODS) As FCSTRESULTS_RCD
    Dim AIMMethods() As METHOD_RCD
    Dim Simdata As SIMULATION_RCD
    Dim DistKitBOMItems() As DIST_KITBOM_ITEM_RCD
    Dim DistKitBOMMainItems() As DIST_KITBOM_MAIN_ITEM_RCD
    Dim DistCompItems() As DIST_COMP_ITEM_RCD
    Dim DistCompMainItems() As DIST_COMP_MAIN_ITEM_RCD
    
    Dim LoopItems As Long
    Dim LoopItems2 As Long
    Dim LoopMainItems As Long
    Dim LoopItemDetail As Long
    
    Dim FcstItem As Double
    Dim FcstKitBom As Double
    Dim FcstItemLT As Double
    Dim FcstItemRT As Double
    Dim FcstItemQtr As Double
    Dim FcstItemMonth As Double
    Dim FcstItemYear As Double
    Dim MAE As Double
    Dim TempString As String
    
    'Open connections to the server
    Set Cn1 = New ADODB.Connection
    RtnCode = SQLConnection(Cn1, CONNECTION_OPEN, False)
    Set Cn2 = New ADODB.Connection
    RtnCode = SQLConnection(Cn2, CONNECTION_OPEN, False)
    
    'Set up the Stored Procedures and Cursors
    Set AIM_DemandUpdate_Sp = New ADODB.Command
    With AIM_DemandUpdate_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_DemandUpdate_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_CompanionItemDetail_Sp = New ADODB.Command
    With AIM_CompanionItemDetail_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_CompanionItemDetail_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_GetKitBOMItemsLTRT_Sp = New ADODB.Command
    With AIM_GetKitBOMItemsLTRT_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetKitBOMItemsLTRT_Sp"
        .Parameters.Refresh
    End With
    
     Set AIM_GetKitBOMItemsForALoc_Sp = New ADODB.Command
    With AIM_GetKitBOMItemsForALoc_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetKitBOMItemsForALoc_Sp"
        .Parameters.Refresh
    End With
    
     Set AIM_GetItemsForAKitBOMItem_Sp = New ADODB.Command
    With AIM_GetItemsForAKitBOMItem_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetItemsForAKitBOMItem_Sp"
        .Parameters.Refresh
    End With
    
     Set AIM_GetCompItemsLTRT_Sp = New ADODB.Command
    With AIM_GetCompItemsLTRT_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetCompItemsLTRT_Sp"
        .Parameters.Refresh
    End With
    
     Set AIM_GetCompItemsForALoc_Sp = New ADODB.Command
    With AIM_GetCompItemsForALoc_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetCompItemsForALoc_Sp"
        .Parameters.Refresh
    End With
    
     Set AIM_GetItemsForACompItem_Sp = New ADODB.Command
    With AIM_GetItemsForACompItem_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetItemsForACompItem_Sp"
        .Parameters.Refresh
    End With
    
    
    Set AIM_DemandUpdateForCompItems_Sp = New ADODB.Command
    With AIM_DemandUpdateForCompItems_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_DemandUpdateForCompItems_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_DemandUpdateForKitBOMItems_Sp = New ADODB.Command
    With AIM_DemandUpdateForKitBOMItems_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_DemandUpdateForKitBOMItems_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_KitBOMSummary_Upd_Sp = New ADODB.Command
    With AIM_KitBOMSummary_Upd_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_KitBOMSummary_Upd_Sp"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With

    
    Set AIM_KitBOMSummary_Sp = New ADODB.Command
    With AIM_KitBOMSummary_Sp
        Set .ActiveConnection = Cn1
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_KitBOMSummary_Sp"
    End With
    
    Set AIM_DUGet_Sp = New ADODB.Command
    With AIM_DUGet_Sp
        Set .ActiveConnection = Cn1
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_DUGet_Sp"
    'Set parameters and their values
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append .CreateParameter("@SelLcId", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@SelItem", adVarWChar, adParamInput, 25)
        .Parameters.Append .CreateParameter("@FcstYear", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@FcstPeriod", adSmallInt, adParamInput)
        .Parameters.Append .CreateParameter("@ForceUpdateFlag", adVarWChar, adParamInput, 1)
        .Parameters.Append .CreateParameter("@DemandSource", adVarWChar, adParamInput, 1)
        .Parameters.Append .CreateParameter("@SAVersion", adSmallInt, adParamInput)
    End With

    Set AIM_AIMLocations_List_Sp = New ADODB.Command
    With AIM_AIMLocations_List_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_AIMLocations_List_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_MDCDetail_Sp = New ADODB.Command
    With AIM_MDCDetail_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_MDCDetail_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AIMPromotions_GetKey_Sp = New ADODB.Command
    With AIM_AIMPromotions_GetKey_Sp
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_AIMPromotions_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_GetFcstAdj_Sp = New ADODB.Command
    With AIM_GetFcstAdj_Sp
        Set .ActiveConnection = Cn1
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_GetFcstAdj_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize Result Sets
    Set rsAIMPromotions = New ADODB.Recordset
    With rsAIMPromotions
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    Set rsduGet = New ADODB.Recordset
    With rsduGet
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    Set rsLc = New ADODB.Recordset
    With rsLc
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    Set rsKitBOMItemsLTRT = New ADODB.Recordset
    With rsKitBOMItemsLTRT
        rsKitBOMItemsLTRT.CursorLocation = adUseClient
        rsKitBOMItemsLTRT.LockType = adLockReadOnly
    End With
    
    Set rsKitBOMItemsForALoc = New ADODB.Recordset
    With rsKitBOMItemsForALoc
        rsKitBOMItemsForALoc.CursorLocation = adUseClient
        rsKitBOMItemsForALoc.LockType = adLockReadOnly
    End With
    
    Set rsItemsForAKitBOMItem = New ADODB.Recordset
    With rsItemsForAKitBOMItem
        rsItemsForAKitBOMItem.CursorLocation = adUseClient
        rsItemsForAKitBOMItem.LockType = adLockReadOnly
    End With
    
    Set rsComItemsLTRT = New ADODB.Recordset
    With rsComItemsLTRT
        rsComItemsLTRT.CursorLocation = adUseClient
        rsComItemsLTRT.LockType = adLockReadOnly
    End With
    
    Set rsCompItemsForALoc = New ADODB.Recordset
    With rsCompItemsForALoc
        rsCompItemsForALoc.CursorLocation = adUseClient
        rsCompItemsForALoc.LockType = adLockReadOnly
    End With
    
    Set rsItemsForACompItem = New ADODB.Recordset
    With rsItemsForACompItem
        rsItemsForACompItem.CursorLocation = adUseClient
        rsItemsForACompItem.LockType = adLockReadOnly
    End With
    
    Set rsAIM_KitBOMSummary = New ADODB.Recordset
    With rsAIM_KitBOMSummary
        .CursorLocation = adUseServer
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
        .CacheSize = 1000
    End With
    
    
     Set rsAIM_GetFcstAdj = New ADODB.Recordset
    With rsAIM_GetFcstAdj
         rsAIM_GetFcstAdj.CursorLocation = adUseClient
         rsAIM_GetFcstAdj.LockType = adLockReadOnly
    End With
   
    
    'Get the System Control values
    GetSysInfo Cn2, rsSysCtrl
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
    LogFile = rsSysCtrl("LogFile").Value
    gDateFormat = rsSysCtrl!DateFormat
    gTimeFormat = rsSysCtrl!TimeFormat
    If Trim$(LogFile) = "" Then
        LogFile = App.Path & "\" & "EXceedAIMDataInterface.Log"
    End If
    m_LogFile = LogFile
    
    
    ByIdSource = rsSysCtrl("ByIdSource").Value
    Addl_LTDays = rsSysCtrl("Addl_LTDays").Value
    Simdata.MDCOption = rsSysCtrl("MDCOption").Value
    Simdata.SeasSmoothingPds = rsSysCtrl("SeasSmoothingPds").Value
    Simdata.KFactor = rsSysCtrl("KFactor").Value
    Simdata.CurSaVersion = rsSysCtrl("CurSaVersion").Value
    Simdata.Int_Enabled = rsSysCtrl("Int_Enabled").Value
    Simdata.Int_MinPctZero = rsSysCtrl("Int_MinPctZero").Value
    Simdata.Int_MaxPctZero = rsSysCtrl("Int_MaxPctZero").Value
    Simdata.Int_SeasonalityIndex = rsSysCtrl("Int_SeasonalityIndex").Value
    Simdata.Int_SrvLvlOverrideFlag = rsSysCtrl("Int_SrvLvlOverrideFlag").Value
    Simdata.Int_SrvLvl = rsSysCtrl("Int_SrvLvl").Value
    Simdata.Int_LookBackPds = rsSysCtrl("Int_LookBackPds").Value
    'A.Stocksdale Mar 19, 2004 -- comment out until Demand Planner is completed
    IncludeMasterItemAdj = IIf(rsSysCtrl("MasterItemOption").Value = "Y", True, False)
    'A.Stocksdale - end comment
    
    
    'Load the AIM Methods Table
    RtnCode = AIMMethodsLoad(Cn1, AIMMethods)
    
    'Load the AIM Calendar
    AIMCalendar_Load Cn1, Simdata.AIMYears(), Simdata.AIMDays()
    ReDim Preserve Simdata.KIDetail(1 To 1)
    ReDim Preserve Simdata.KI.LT(0 To 0)
    ReDim Preserve Simdata.CIDetail(1 To 1)
    ReDim Preserve Simdata.CI.LT(0 To 0)
    
    'Write message to log file
    Write_Log LogFile, "", 0, False, True, True, False
    TempString = getTranslationResource("Demand Update Started")
    Write_Log LogFile, TempString, 0, False, True, False, True
    
    'This is first update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , True)
    
    'Output Command Line Parameters
    TempString = getTranslationResource("Auto Option", True) & CStr(AutoOption)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)

    TempString = getTranslationResource("Location Option", True) & CStr(LcidOption)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
   
    If AutoOption = "Y" Then
        'Set the forecast date and period based on the current date
        'The current period should be one less than the currend period to ensure a
        'full period of demand data is present
   
        FcstYear = GetFiscalYear(Date, Simdata.AIMYears())
        FcstPeriod = GetPeriod(Date, Simdata.AIMYears()) - 1
        
        If FcstPeriod < 1 Then
            FcstPeriod = 52
            FcstYear = FcstYear - 1
        End If
   
    End If
    
    TempString = getTranslationResource("Forecast Year", True) & CStr(FcstYear)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
    
    TempString = getTranslationResource("Forecast Period", True) & CStr(FcstPeriod)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
    
    TempString = getTranslationResource("Force Update", True) & CStr(ForceUpdateFlag)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
    TempString = getTranslationResource("Build MDC Summary", True) & CStr(MDCOption)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
    TempString = getTranslationResource("Log File", True) & CStr(LogFile)
    Write_Log LogFile, TempString, 2, False, True, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
    'Build Location List
    
    
    
    
    AIM_AIMLocations_List_Sp("@LcIdOption").Value = LcidOption
    'AIM_AIMLocations_List_Sp("@LcIdOption").Value = "dc37" '------------modified code put one location  sri
    rsLc.Open AIM_AIMLocations_List_Sp
    If Not f_IsRecordsetOpenAndPopulated(rsLc) Then
        Err.Description = "rsLC Empty Recordset"
        GoTo ErrorHandler
    End If
    
    'Execute Demand Update by Location
    rsLc.MoveFirst
    
    Do Until rsLc.EOF
        'Set the Location Id
        Simdata.LC.LcId = rsLc("LcId").Value
        Simdata.LC.PutAwayDays = rsLc("PutAwayDays").Value
        Simdata.LC.ReplenCost = rsLc("Replencost").Value
        Simdata.LC.DemandSource = rsLc("DemandSource").Value
        Simdata.LC.StkDate = Format(rsLc("StkDate").Value, g_ISO_DATE_FORMAT)
        Simdata.LC.LookBackPds = rsLc("LookBackPds").Value
        Simdata.LC.Last_FcstUpdCyc = rsLc("Last_FcstUpdCyc").Value
        Simdata.LC.DmdScalingFactor = rsLc("DmdScalingFactor").Value
        Simdata.LC.ScalingEffUntil = rsLc("ScalingEffUntil").Value
        
        'Determine the Forecast Update Cycle
        If AutoOption = "Y" Then
'            FcstYear = CInt(Simdata.LC.Last_FcstUpdCyc \ 100)
'            FcstPeriod = Simdata.LC.Last_FcstUpdCyc - (CLng(FcstYear) * 100)
'             FcstPeriod = FcstPeriod + 1
             
             FcstYear = GetFiscalYear(Date, Simdata.AIMYears())
            FcstPeriod = GetPeriod(Date, Simdata.AIMYears()) - 1
        
        If FcstPeriod < 1 Then
            FcstPeriod = 52
            FcstYear = FcstYear - 1
        End If
            If FcstPeriod > 52 Then
                FcstPeriod = FcstPeriod - 52
                FcstYear = FcstYear + 1
            End If
            
        End If
        
        'Output Message to Log File
        If AutoOption = "Y" Then
            strMessage = getTranslationResource("Location", True) & _
                        Simdata.LC.LcId
            strMessage = strMessage & " (" & _
                        CStr(Format(FcstYear, "0000")) & _
                        CStr(Format(FcstPeriod, "00")) & _
                        ")"
            Write_Log LogFile, strMessage, 4, False, False, False, False
            Call f_HandleErr(0, strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate)
        Else
            strMessage = getTranslationResource("Location", True) & _
                        Trim$(Simdata.LC.LcId)
            strMessage = strMessage & " (" & _
                        CStr(Format(FcstYear, "0000")) & _
                        "/" & _
                        CStr(Format(FcstPeriod, "00")) & _
                        ")"
            Write_Log LogFile, strMessage, 4, False, False, False, False
            Call f_HandleErr(0, strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate)
        End If
        
        Simdata.It.FcstUpdCyc = (CLng(FcstYear) * 100) + FcstPeriod
        Simdata.LC.Last_FcstUpdCyc = Simdata.It.FcstUpdCyc
        
        'Build the Demand Update Control Cursor
        With AIM_DUGet_Sp
            .Parameters("@SelLcid").Value = Simdata.LC.LcId
            .Parameters("@SelItem").Value = ItemOption
            .Parameters("@FcstYear").Value = FcstYear
            .Parameters("@FcstPeriod").Value = FcstPeriod
            .Parameters("@ForceUpdateFlag").Value = ForceUpdateFlag
            .Parameters("@DemandSource").Value = Simdata.LC.DemandSource
            .Parameters("@SAVersion").Value = Simdata.CurSaVersion
        End With
        Cn1.Errors.Clear
        If f_IsRecordsetValidAndOpen(rsduGet) Then rsduGet.Close
        rsduGet.Open AIM_DUGet_Sp
        If Not f_IsRecordsetOpenAndPopulated(rsduGet) _
        Or (rsduGet.BOF = True And rsduGet.EOF = True) Then
            GoTo NextLocation
        End If
        
        'Check for errors in retrieval query
        If Cn1.Errors.Count > 0 Then
            For i = 0 To Cn1.Errors.Count - 1
                strMessage = getTranslationResource("AIM_DDUGet_Sp Error for") & " " & _
                        CStr(Trim$(Simdata.It.LcId)) & ": "
                strMessage = strMessage & Cn1.Errors(i).Description
                Write_Log LogFile, strMessage, 2, False, True, False, True
                
                'This is subsequentupdate from DemandUpdate job - sgajjela
                Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobError, False, , False)

                Cn1.Errors.Clear
            Next i
        End If
        
        'Initialize Control Variables
        FirstTime = True
        PrevLcId = ""
        PrevItem = ""
        LcProcessedFlag = False
    
        Do Until rsduGet.EOF
            'Set the Location Processed Flag
            LcProcessedFlag = True
            
            'Change in Location/Item
            If rsduGet(du_LcId).Value <> PrevLcId _
            Or rsduGet(du_item).Value <> PrevItem Then
                If Not FirstTime Then
                    'Filter Demand
                    FilterDemand Simdata
                    
                    ' Cn2.Errors.Clear
                    GetKitBOMItemsLTRT AIM_GetKitBOMItemsLTRT_Sp, Simdata, rsKitBOMItemsLTRT
                    GetCompanionItemsLTRT AIM_GetCompItemsLTRT_Sp, Simdata, rsComItemsLTRT
                    
                    'Calculate the Item's Seasonality Index
                    CalcItemSeasIndex Simdata

                    If Simdata.It.IsIntermittent = "Y" Then
                        intCount = intCount + 1
                    End If
                    
                    'Update the Forecast
                    FcstSimulation False, Simdata, FcstResults, AIMMethods, IncludeMasterItemAdj, AIM_GetFcstAdj_Sp, rsAIM_GetFcstAdj
                    
                    '  Cn2.Errors.Clear
                 '   GetKitBOMItemsLTRT AIM_GetKitBOMItemsLTRT_Sp, Simdata, rsKitBOMItemsLTRT
               
                    'Update the Item Table
                    Cn2.Errors.Clear
                    UpdateDemand AIM_DemandUpdate_Sp, Simdata
                    
                    'Check for errors
                    If Cn2.Errors.Count > 0 Then
                        For i = 0 To Cn2.Errors.Count - 1
                            strMessage = getTranslationResource("AIM_DemandUpdate_Sp Error") & " " & _
                                CStr(Trim$(Simdata.It.LcId)) & "/" & CStr(Trim$(Simdata.It.Item)) & ": "
                            strMessage = strMessage & Cn2.Errors(i).Description
                            Write_Log LogFile, strMessage, 2, False, True, False, True
                            
                            'This is subsequentupdate from DemandUpdate job - sgajjela
                            Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobError, False, , False)
                            Cn2.Errors.Clear
                        Next i
                    
                    Else
                        UpdateCount = UpdateCount + 1
                    End If
                    
                    'Check for an MDC Item
                    If Trim$(Simdata.It.MDC) <> "" Or Simdata.It.MDCFlag = "Y" Then
                        MDCDetail Simdata, AIM_MDCDetail_Sp, AIMMethods(Simdata.It.FcstMethod), IncludeMasterItemAdj, AIM_GetFcstAdj_Sp, rsAIM_GetFcstAdj
                    End If
                
                End If
                
                'Initialize the Simulation Record
                SIMData_Init Simdata
    
                'Update the Simulation Record -- Item Data
                Simdata.It.LcId = rsduGet(du_LcId).Value
                Simdata.It.Item = rsduGet(du_item).Value
                Simdata.It.ItDesc = rsduGet(du_ItDesc).Value
                Simdata.It.ItStat = rsduGet(du_ItStat).Value
                Simdata.It.ActDate = Format(rsduGet(du_ActDate).Value, g_ISO_DATE_FORMAT)
                Simdata.It.InActDate = Format(rsduGet(du_InActDate).Value, g_ISO_DATE_FORMAT)
                Simdata.It.OptionId = rsduGet(du_OptionId).Value
                Simdata.It.Class1 = rsduGet(du_Class1).Value
                Simdata.It.Class2 = rsduGet(du_Class2).Value
                Simdata.It.Class3 = rsduGet(du_Class3).Value
                Simdata.It.Class4 = rsduGet(du_Class4).Value
                Simdata.It.BinLocation = rsduGet(du_BinLocation).Value
                Simdata.It.VelCode = rsduGet(du_VelCode).Value
                Simdata.It.VnId = rsduGet(du_VnId).Value
                Simdata.It.Assort = rsduGet(du_Assort).Value
                Simdata.It.ById = rsduGet(du_Dft_ById).Value
                Simdata.It.MDC = rsduGet(du_MDC).Value
                Simdata.It.MDCFlag = rsduGet(du_MDCFlag).Value
                Simdata.It.SaId = rsduGet(du_SaId).Value
                Simdata.It.PmId = rsduGet(du_PmId).Value
                Simdata.It.UPC = rsduGet(du_UPC).Value
                Simdata.It.Weight = rsduGet(du_weight).Value
                Simdata.It.Cube = rsduGet(du_cube).Value
                Simdata.It.Price = rsduGet(du_price).Value
                Simdata.It.Cost = rsduGet(du_cost).Value
                Simdata.It.BkQty(1) = rsduGet(du_bkqty01).Value
                Simdata.It.BkCost(1) = rsduGet(du_bkcost01).Value
                Simdata.It.BkQty(2) = rsduGet(du_bkqty02).Value
                Simdata.It.BkCost(2) = rsduGet(du_bkcost02).Value
                Simdata.It.BkQty(3) = rsduGet(du_bkqty03).Value
                Simdata.It.BkCost(3) = rsduGet(du_bkcost03).Value
                Simdata.It.BkQty(4) = rsduGet(du_bkqty04).Value
                Simdata.It.BkCost(4) = rsduGet(du_bkcost04).Value
                Simdata.It.BkQty(5) = rsduGet(du_bkqty05).Value
                Simdata.It.BkCost(5) = rsduGet(du_bkcost05).Value
                Simdata.It.BkQty(6) = rsduGet(du_bkqty06).Value
                Simdata.It.BkCost(6) = rsduGet(du_bkcost06).Value
                Simdata.It.BkQty(7) = rsduGet(du_bkqty07).Value
                Simdata.It.BkCost(7) = rsduGet(du_bkcost07).Value
                Simdata.It.BkQty(8) = rsduGet(du_bkqty08).Value
                Simdata.It.BkCost(8) = rsduGet(du_bkcost08).Value
                Simdata.It.BkQty(9) = rsduGet(du_bkqty09).Value
                Simdata.It.BkCost(9) = rsduGet(du_bkcost09).Value
                Simdata.It.BkQty(10) = rsduGet(du_bkqty10).Value
                Simdata.It.BkCost(10) = rsduGet(du_bkcost10).Value
                Simdata.It.UOM = rsduGet(du_uom).Value
                Simdata.It.ConvFactor = rsduGet(du_convfactor).Value
                Simdata.It.BuyingUOM = rsduGet(du_buyingUOM).Value
                Simdata.It.ReplenCost2 = rsduGet(du_ReplenCost2).Value
                Simdata.It.Oh = rsduGet(du_Oh).Value
                Simdata.It.Oo = rsduGet(du_oo).Value
                Simdata.It.ComStk = rsduGet(du_comstk).Value
                Simdata.It.BkOrder = rsduGet(du_bkorder).Value
                Simdata.It.BkComStk = rsduGet(du_bkcomstk).Value
                Simdata.It.LeadTime = rsduGet(du_LeadTime).Value
                Simdata.It.PackRounding = rsduGet(du_PackRounding).Value
                Simdata.It.IMin = rsduGet(du_IMin).Value
                Simdata.It.IMax = rsduGet(du_IMax).Value
                Simdata.It.CStock = rsduGet(du_CStock).Value
                Simdata.It.SSAdj = rsduGet(du_ssadj).Value
                Simdata.It.UserMin = rsduGet(du_UserMin).Value
                Simdata.It.UserMax = rsduGet(du_UserMax).Value
                Simdata.It.UserMethod = rsduGet(du_UserMethod).Value
                Simdata.It.FcstMethod = rsduGet(du_FcstMethod).Value
                Simdata.It.FcstDemand = rsduGet(du_FcstDemand).Value
                Simdata.It.UserFcst = rsduGet(du_UserFcst).Value
                Simdata.It.UserFcstExpDate = Format(rsduGet(du_UserFcstExpDate).Value, g_ISO_DATE_FORMAT)
                Simdata.It.MAE = rsduGet(du_MAE).Value
                Simdata.It.MSE = rsduGet(du_MSE).Value
                Simdata.It.Trend = rsduGet(du_trend).Value
                Simdata.It.LtvFact = rsduGet(du_ltvfact).Value
                Simdata.It.FcstCycles = rsduGet(du_fcstcycles).Value
                Simdata.It.ZeroCount = rsduGet(du_zerocount).Value
                Simdata.It.DIFlag = rsduGet(du_diflag).Value
                Simdata.It.DmdFilterFlag = rsduGet(du_DmdFilterFlag).Value
                Simdata.It.TrkSignalFlag = rsduGet(du_TrkSignalFlag).Value
                Simdata.It.UserDemandFlag = rsduGet(du_UserDemandFlag).Value
                Simdata.It.ByPassPct = rsduGet(du_BypassPct).Value
                Simdata.It.FcstUpdCyc = (CLng(FcstYear) * 100) + FcstPeriod
                Simdata.It.PlnTT = rsduGet(du_plntt).Value
                Simdata.It.ZOPSw = rsduGet(du_zopsw).Value
                Simdata.It.OUTLSw = rsduGet(du_outlsw).Value
                Simdata.It.ZSStock = rsduGet(du_zsstock).Value
                Simdata.It.DSer = rsduGet(du_dser).Value
                Simdata.It.Freeze_BuyStrat = rsduGet(du_freeze_buystrat).Value
                Simdata.It.Freeze_ById = rsduGet(du_freeze_byid).Value
                Simdata.It.Freeze_LeadTime = rsduGet(du_freeze_leadtime).Value
                Simdata.It.Freeze_OptionID = rsduGet(du_freeze_optionid).Value
                Simdata.It.Freeze_DSer = rsduGet(du_freeze_dser).Value
                Simdata.It.OldItem = rsduGet(du_olditem).Value
                Simdata.It.Accum_Lt = rsduGet(du_accum_lt).Value
                Simdata.It.ReviewTime = rsduGet(du_reviewtime).Value
                Simdata.It.OrderPt = rsduGet(du_orderpt).Value
                Simdata.It.OrderQty = rsduGet(du_orderqty).Value
                Simdata.It.SafetyStock = rsduGet(du_SafetyStock).Value
                Simdata.It.FcstRT = rsduGet(du_fcstrt).Value
                Simdata.It.FcstLT = rsduGet(du_fcstlt).Value
                Simdata.It.Fcst_Month = rsduGet(du_fcst_month).Value
                Simdata.It.Fcst_Quarter = rsduGet(du_fcst_quarter).Value
                Simdata.It.Fcst_Year = rsduGet(du_fcst_year).Value
                Simdata.It.FcstDate = Format(rsduGet(du_fcstdate).Value, g_ISO_DATE_FORMAT)
                Simdata.It.VC_Amt = rsduGet(du_vc_amt).Value
                Simdata.It.VC_Units_Ranking = rsduGet(du_vc_units_ranking).Value
                Simdata.It.VC_Amt_Ranking = rsduGet(du_vc_amt_ranking).Value
                Simdata.It.VC_Date = Format(rsduGet(du_vc_date).Value, g_ISO_DATE_FORMAT)
                Simdata.It.VelCode_Prev = rsduGet(du_velcode_prev).Value
                Simdata.It.VC_Amt_Prev = rsduGet(du_vc_amt_prev).Value
        
                'Update the Simulation Record -- Options Data
                Simdata.OP.OpDesc = rsduGet(du_OpDesc).Value
                Simdata.OP.IDFL = rsduGet(du_IDFL).Value
                Simdata.OP.BypIDFL = rsduGet(du_BypIDFL).Value
                Simdata.OP.CADL = rsduGet(du_CADL).Value
                Simdata.OP.HiDFL = rsduGet(du_HiDFL).Value
                Simdata.OP.LoDFL = rsduGet(du_LoDFL).Value
                Simdata.OP.NDFL = rsduGet(du_NDFL).Value
                Simdata.OP.DDMAP = rsduGet(du_DDMAP).Value
                Simdata.OP.DAMP = rsduGet(du_DAMP).Value
                Simdata.OP.MinSmk = rsduGet(du_MinSmk).Value
                Simdata.OP.MaxSmk = rsduGet(du_MaxSmk).Value
                Simdata.OP.IntSmk = rsduGet(du_IntSmk).Value
                Simdata.OP.TSL = rsduGet(du_TSL).Value
                Simdata.OP.TSSmK = rsduGet(du_TSSmK).Value
                Simdata.OP.BypDDU = rsduGet(du_BypDDU).Value
                Simdata.OP.ApplyDmdFilter = rsduGet(du_ApplyDmdFilter).Value
                Simdata.OP.BypZSl = rsduGet(du_BypZSl).Value
                Simdata.OP.BypTFP = rsduGet(du_BypTFP).Value
                Simdata.OP.BypZOH = rsduGet(du_BypZOH).Value
                Simdata.OP.BypBef = rsduGet(du_BypBef).Value
                Simdata.OP.BypAft = rsduGet(du_BypAft).Value
                Simdata.OP.MinBi = rsduGet(du_MinBi).Value
                Simdata.OP.hipct = rsduGet(du_HiPct).Value
                Simdata.OP.LoPct = rsduGet(du_LoPct).Value
                Simdata.OP.HiMADP = rsduGet(du_HiMADP).Value
                Simdata.OP.LoMADP = rsduGet(du_LoMADP).Value
                Simdata.OP.MADSmk = rsduGet(du_MADSmk).Value
                Simdata.OP.MADExK = rsduGet(du_MADExk).Value
                Simdata.OP.TrnSmk = rsduGet(du_TrnSmk).Value
                Simdata.OP.DIDDL = rsduGet(du_DIDDL).Value
                Simdata.OP.DITrnDL = rsduGet(du_DiTrnDL).Value
                Simdata.OP.DITrps = rsduGet(du_DITrps).Value
                Simdata.OP.DIMADP = rsduGet(du_DIMADP).Value
                Simdata.OP.hitrndl = rsduGet(du_HiTrndL).Value
                Simdata.OP.FilterSmk = rsduGet(du_FilterSmk).Value
                Simdata.OP.LumpyFilterPct = rsduGet(du_LumpyFilterPct).Value
                Simdata.OP.Dft_TurnHigh = rsduGet(du_Dft_TurnHigh).Value
                Simdata.OP.Dft_TurnLow = rsduGet(du_Dft_TurnLow).Value
                
                'Load the Seasonality Indices
                For Pd = LBound(Simdata.Sa.Bi) To UBound(Simdata.Sa.Bi)
                    Simdata.Sa.Bi(Pd) = rsduGet(du_bi01 + Pd - 1).Value
                Next Pd
                
                'Load the Review Cycle Data
                Simdata.It.ReviewTime = rsduGet(du_RevCycles_ReviewTime).Value
    
                'Determine the start and end fiscal years
                If Simdata.It.FcstUpdCyc = 0 Then
                    Simdata.It.FcstUpdCyc = Simdata.LC.Last_FcstUpdCyc
                End If
                
                Simdata.DM.StartYear = GetFiscalYear(Simdata.It.ActDate, Simdata.AIMYears)
                Simdata.DM.StartPeriod = GetPeriod(Simdata.It.ActDate, Simdata.AIMYears)
                
                Simdata.DM.EndYear = FcstYear
                Simdata.DM.EndPeriod = FcstPeriod
                
                'Set the number of periods of activity
                Simdata.DM.OldestPeriod = GetDmdPd(Simdata.DM.EndYear, Simdata.DM.EndPeriod, _
                    Simdata.DM.StartYear, Simdata.DM.StartPeriod)
               
                'Base Date is the date following the last day of the last forecast update period
                Simdata.DM.BaseDate = GetStartDateFmPeriod(Simdata.DM.EndPeriod, Simdata.DM.EndYear, Simdata.AIMYears()) + 7
                
                'Update Default Values
                If ByIdSource = "L" And Simdata.It.Freeze_ById = "N" Then
                    Simdata.It.ById = rsLc("Dft_ById").Value
                ElseIf ByIdSource = "V" And Simdata.It.Freeze_ById = "N" Then
                    Simdata.It.ById = rsduGet(du_Dft_ById).Value
                End If
                
                If Simdata.It.Freeze_DSer = "N" Then
                    Simdata.It.DSer = rsduGet(du_Dft_Dser).Value
                End If
            
                If rsLc("LeadTimeSource").Value = "V" _
                    And Simdata.It.Freeze_LeadTime = "N" Then
                    Simdata.It.LeadTime = rsduGet(du_Dft_LeadTime).Value
                End If
    
                'Update the Accumulative Lead Time
                Simdata.It.Accum_Lt = Simdata.It.LeadTime + Simdata.LC.PutAwayDays + Addl_LTDays
    
                'Get the Promotions Data
                If UCase(Simdata.It.PmId) <> "NONE" _
                Or UCase(Simdata.It.PmId) <> getTranslationResource("NONE") Then
                    RtnCode = AIMPromotions_GetKey(AIM_AIMPromotions_GetKey_Sp, Simdata.It.PmId, rsAIMPromotions, SQL_GetEq)
                
                    If RtnCode <> FAIL Then
                        Simdata.PM.PmDesc = rsAIMPromotions("PmDesc").Value
                        Simdata.PM.PmStatus = rsAIMPromotions("PmStatus").Value
                        Simdata.PM.PmStartDate = Format(rsAIMPromotions("PmStartDate").Value, g_ISO_DATE_FORMAT)
                        Simdata.PM.PmEndDate = Format(rsAIMPromotions("PmEndDate").Value, g_ISO_DATE_FORMAT)
                        
                        For Pd = LBound(Simdata.PM.PmAdj) To UBound(Simdata.PM.PmAdj)
                            Simdata.PM.PmAdj(Pd) = rsAIMPromotions("PmAdj" & Format(Pd, "00")).Value
                        Next Pd
                    End If
                End If
            End If  'check for change in Location/Item
            
            'No longer first time
            FirstTime = False
            
            'Save current Location/Item
            PrevLcId = rsduGet(du_LcId).Value
            PrevItem = rsduGet(du_item).Value
            
            'Load the Demand History
            If Not IsNull(rsduGet(du_HisYear).Value) Then
                HsYear = rsduGet(du_HisYear).Value
                    
                For HsPd = 52 To 1 Step -1
                    'Calculate Pd
                    Pd = GetDmdPd(Simdata.DM.EndYear, Simdata.DM.EndPeriod, HsYear, HsPd)
                    
                    'Check for full history
                    If Pd >= 1 And Pd <= 156 Then
                        Simdata.DM.Cps(Pd) = rsduGet(du_cps01 + HsPd - 1)
                        If Simdata.Sa.Bi(HsPd) > 0 Then
                            Simdata.DM.dps(Pd) = Simdata.DM.Cps(Pd) / Simdata.Sa.Bi(HsPd)
                        Else
                            Simdata.DM.dps(Pd) = 0
                        End If
                        
                        'Accumulate the Yearly Totals
                        Select Case Pd
                        Case 1 To 52
                            Simdata.DM.CpsTotal(1) = Simdata.DM.CpsTotal(1) + Simdata.DM.Cps(Pd)
                            Simdata.DM.DpsTotal(1) = Simdata.DM.DpsTotal(1) + Simdata.DM.dps(Pd)
                        Case 53 To 104
                            Simdata.DM.CpsTotal(2) = Simdata.DM.CpsTotal(2) + Simdata.DM.Cps(Pd)
                            Simdata.DM.DpsTotal(2) = Simdata.DM.DpsTotal(2) + Simdata.DM.dps(Pd)
                        Case 105 To 156
                            Simdata.DM.CpsTotal(3) = Simdata.DM.CpsTotal(3) + Simdata.DM.Cps(Pd)
                            Simdata.DM.DpsTotal(3) = Simdata.DM.DpsTotal(3) + Simdata.DM.dps(Pd)
                        End Select
                        
                        'Update the oldest period
                        If Pd > Simdata.DM.OldestPeriod Then
                            Simdata.DM.OldestPeriod = Pd
                        End If
                    End If
                Next HsPd
            End If

            'Check for miscellaneous errors
            If Err.Number <> 0 Then
                strMessage = CStr(Simdata.It.LcId) & "/" & CStr(Simdata.It.Item) & ": " & Err.Description
                Write_Log LogFile, strMessage, 2, False, True, False, True
                
                'This is subsequent update from DemandUpdate job - sgajjela
                Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobError, False, , False)
                
                Err.Clear
            End If
            
            'Get Next Demand Update Record
            rsduGet.MoveNext
            
        Loop        'End of Item Fetch Loop
        If f_IsRecordsetValidAndOpen(rsduGet) Then rsduGet.Close
                
        'Update the last record; if there were records for this location
        If LcProcessedFlag Then
            'Update the last item
            FilterDemand Simdata
            
            GetKitBOMItemsLTRT AIM_GetKitBOMItemsLTRT_Sp, Simdata, rsKitBOMItemsLTRT
            GetCompanionItemsLTRT AIM_GetCompItemsLTRT_Sp, Simdata, rsComItemsLTRT
            
            'CalcItemSeasIndex SimData
            FcstSimulation False, Simdata, FcstResults, AIMMethods, IncludeMasterItemAdj, AIM_GetFcstAdj_Sp, rsAIM_GetFcstAdj
            
 '          UpdateCompanionItemDetail AIM_CompanionItemDetail_Sp, Simdata
               
            Cn2.Errors.Clear
            UpdateDemand AIM_DemandUpdate_Sp, Simdata
            
            If Cn2.Errors.Count > 0 Then
                For i = 0 To Cn2.Errors.Count - 1
                    strMessage = getTranslationResource("AIM_DemandUpdate_Sp Error") & _
                        " " & Trim$(CStr(Simdata.It.LcId)) & "/" & Trim$(CStr(Simdata.It.Item)) & ": "
                    strMessage = strMessage & Cn2.Errors(i).Description
                    Write_Log LogFile, strMessage, 2, False, True, False, True
                    
                    'This is subsequent update from DemandUpdate job - sgajjela
                    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobError, False, , False)

                    Cn2.Errors.Clear
                Next i
            Else
                UpdateCount = UpdateCount + 1
            End If
                        
            'Check for an MDC Item
            If Trim$(Simdata.It.MDC) <> "" Or Simdata.It.MDCFlag = "Y" Then
                MDCDetail Simdata, AIM_MDCDetail_Sp, AIMMethods(Simdata.It.FcstMethod), IncludeMasterItemAdj, AIM_GetFcstAdj_Sp, rsAIM_GetFcstAdj
            End If
        End If
        
        'Output Location Counts to Log File
        strMessage = getTranslationResource("Items Updated", True) & _
                    CStr(Format(UpdateCount - LastUpdateCount, "#,##0"))
        Write_Log LogFile, strMessage, 6, False, False, False, False
        
        'This is subsequent update from DemandUpdate job - sgajjela
        Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)

        
        LastUpdateCount = UpdateCount
        
        ReDim DistKitBOMItems(1 To 1)
        ReDim DistKitBOMMainItems(1 To 1)
        GetKitBOMItemsForALoc AIM_GetKitBOMItemsForALoc_Sp, rsKitBOMItemsForALoc, DistKitBOMItems, Simdata
        
        For LoopItems = 1 To UBound(DistKitBOMItems)
            GetKitBOMMainItems AIM_GetItemsForAKitBOMItem_Sp, rsItemsForAKitBOMItem, DistKitBOMMainItems, DistKitBOMItems(LoopItems).ItemComponent, Simdata.LC.LcId
            'GetKitBOMMainItems AIM_GetItemsForAKitBOMItem_Sp, rsItemsForAKitBOMItem, DistKitBOMMainItems, DistKitBOMItems(LoopCompItems).ItemComponent, "940"
            For LoopMainItems = 1 To UBound(DistKitBOMMainItems)
                For LoopItemDetail = 1 To UBound(Simdata.KIDetail)
                    If Simdata.KIDetail(LoopItemDetail).Item = DistKitBOMMainItems(LoopMainItems).MainItem _
                    And Simdata.KIDetail(LoopItemDetail).FcstStartPd = DistKitBOMItems(LoopItems).LT _
                    And Simdata.KIDetail(LoopItemDetail).FcstEndPd = DistKitBOMItems(LoopItems).RT _
                    And Simdata.KIDetail(LoopItemDetail).StartDate = DistKitBOMMainItems(LoopMainItems).StartData _
                    And Simdata.KIDetail(LoopItemDetail).EndDate = DistKitBOMMainItems(LoopMainItems).EndDate Then
                        FcstItem = FcstItem + Simdata.KIDetail(LoopItemDetail).Fcst * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                        FcstItemLT = FcstItemLT + Simdata.KIDetail(LoopItemDetail).FcstLT * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                        FcstItemRT = FcstItemRT + Simdata.KIDetail(LoopItemDetail).FcstRT * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                        FcstItemQtr = FcstItemQtr + Simdata.KIDetail(LoopItemDetail).FcstQtr * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                        FcstItemMonth = FcstItemMonth + Simdata.KIDetail(LoopItemDetail).FcstMonth * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                        FcstItemYear = FcstItemYear + Simdata.KIDetail(LoopItemDetail).FcstYear * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                        MAE = MAE + Simdata.KIDetail(LoopItemDetail).MAE * DistKitBOMMainItems(LoopMainItems).CompUnits * DistKitBOMMainItems(LoopMainItems).CompScrap / 100#
                    End If
                Next LoopItemDetail
            Next LoopMainItems
            
            DistKitBOMItems(LoopItems).FcstKitBom = FcstItem
            DistKitBOMItems(LoopItems).FcstKitBOMLT = FcstItemLT
            DistKitBOMItems(LoopItems).FcstKitBOMRT = FcstItemRT
            DistKitBOMItems(LoopItems).FcstKitBOMMonth = FcstItemMonth
            DistKitBOMItems(LoopItems).FcstKitBOMQtr = FcstItemQtr
            DistKitBOMItems(LoopItems).FcstKitBOMYear = FcstItemYear
            DistKitBOMItems(LoopItems).MAEKitBOM = MAE
    
            FcstItem = 0#
            FcstItemLT = 0#
            FcstItemRT = 0#
            FcstItemQtr = 0#
            FcstItemMonth = 0#
            FcstItemYear = 0#
            MAE = 0#
        Next LoopItems
        
        FcstItem = 0#
        FcstItemLT = 0#
        FcstItemRT = 0#
        FcstItemQtr = 0#
        FcstItemMonth = 0#
        FcstItemYear = 0#
        MAE = 0#
        
        ReDim DistCompItems(1 To 1)
        ReDim DistCompMainItems(1 To 1)
        GetCompItemsForALoc AIM_GetCompItemsForALoc_Sp, rsCompItemsForALoc, DistCompItems, Simdata
        
        For LoopItems = 1 To UBound(DistCompItems)
            'GetKitBOMMainItems AIM_GetItemsForAKitBOMItem_Sp, rsItemsForAKitBOMItem, DistKitBOMMainItems, DistKitBOMItems(LoopCompItems).ItemComponent, SimData.LC.LcId
            GetCompMainItems AIM_GetItemsForACompItem_Sp, rsItemsForACompItem, DistCompMainItems, DistCompItems(LoopItems).ItemComponent, Simdata.LC.LcId
            For LoopMainItems = 1 To UBound(DistCompMainItems)
                For LoopItemDetail = 1 To UBound(Simdata.CIDetail)
                    If Simdata.CIDetail(LoopItemDetail).Item = DistCompMainItems(LoopMainItems).MainItem _
                        And Simdata.CIDetail(LoopItemDetail).FcstStartPd = DistCompItems(LoopItems).LT _
                        And Simdata.CIDetail(LoopItemDetail).FcstEndPd = DistCompItems(LoopItems).RT _
                        And Simdata.CIDetail(LoopItemDetail).StartDate = DistCompMainItems(LoopMainItems).StartData _
                        And Simdata.CIDetail(LoopItemDetail).EndDate = DistCompMainItems(LoopMainItems).EndDate Then
                        FcstItem = FcstItem + Simdata.CIDetail(LoopItemDetail).Fcst * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                        FcstItemLT = FcstItemLT + Simdata.CIDetail(LoopItemDetail).FcstLT * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                        FcstItemRT = FcstItemRT + Simdata.CIDetail(LoopItemDetail).FcstRT * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                        FcstItemQtr = FcstItemQtr + Simdata.CIDetail(LoopItemDetail).FcstQtr * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                        FcstItemMonth = FcstItemMonth + Simdata.CIDetail(LoopItemDetail).FcstMonth * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                        FcstItemYear = FcstItemYear + Simdata.CIDetail(LoopItemDetail).FcstYear * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                        MAE = MAE + Simdata.CIDetail(LoopItemDetail).MAE * DistCompMainItems(LoopMainItems).CompUnits * DistCompMainItems(LoopMainItems).DemandFactor / 100#
                    End If
                Next LoopItemDetail
            Next LoopMainItems
            DistCompItems(LoopItems).FcstCompItem = FcstItem
            DistCompItems(LoopItems).FcstCompItemLT = FcstItemLT
            DistCompItems(LoopItems).FcstCompItemRT = FcstItemRT
            DistCompItems(LoopItems).FcstCompItemMonth = FcstItemMonth
            DistCompItems(LoopItems).FcstCompItemQtr = FcstItemQtr
            DistCompItems(LoopItems).FcstCompItemYear = FcstItemYear
            DistCompItems(LoopItems).MAEComp = MAE
            
            FcstItem = 0#
            FcstItemLT = 0#
            FcstItemRT = 0#
            FcstItemQtr = 0#
            FcstItemMonth = 0#
            FcstItemYear = 0#
            MAE = 0#
        Next LoopItems
        
        'Process each Comp item  if the companion item.
        For LoopItems = 1 To UBound(DistCompItems)
            FcstKitBom = 0
            FcstItem = DistCompItems(LoopItems).FcstCompItem
            FcstItemLT = DistCompItems(LoopItems).FcstCompItemLT
            FcstItemRT = DistCompItems(LoopItems).FcstCompItemRT
            FcstItemMonth = DistCompItems(LoopItems).FcstCompItemMonth
            FcstItemQtr = DistCompItems(LoopItems).FcstCompItemQtr
            FcstItemYear = DistCompItems(LoopItems).FcstCompItemYear
            MAE = DistCompItems(LoopItems).MAEComp
            
            For LoopItems2 = 1 To UBound(DistKitBOMItems)
                  'Check if the Comp item also belongs to KitBOM
                  'if yes add its fcst also and set the processed flag to Y
                 If DistKitBOMItems(LoopItems2).ItemComponent = DistCompItems(LoopItems).ItemComponent Then
                    FcstKitBom = DistKitBOMItems(LoopItems2).FcstKitBom
                    FcstItemLT = FcstItemLT + DistKitBOMItems(LoopItems2).FcstKitBOMLT
                    FcstItemRT = FcstItemRT + DistKitBOMItems(LoopItems2).FcstKitBOMRT
                    FcstItemMonth = FcstItemMonth + DistKitBOMItems(LoopItems2).FcstKitBOMMonth
                    FcstItemQtr = FcstItemQtr + DistKitBOMItems(LoopItems2).FcstKitBOMQtr
                    FcstItemYear = FcstItemYear + DistKitBOMItems(LoopItems2).FcstKitBOMYear
                    MAE = MAE + DistKitBOMItems(LoopItems2).MAEKitBOM
                    DistKitBOMItems(LoopItems2).Processed_YN = "Y"
                    Exit For
                 End If
            Next LoopItems2
            'call update function  which will update
            'in the update function check if the independent demand need to be add or not

            Cn2.Errors.Clear
            ' UpdateDemandForCompItem AIM_DemandUpdateForCompItems_Sp, SimData
            BldKitBOMSummary AIM_KitBOMSummary_Sp, AIM_KitBOMSummary_Upd_Sp, rsAIM_KitBOMSummary, Simdata.It.LcId, DistCompItems(LoopItems).ItemComponent, FcstItemLT, FcstItemRT, FcstItemMonth, FcstItemQtr, FcstItemYear, FcstItem, FcstKitBom, MAE
            
            With AIM_DemandUpdateForCompItems_Sp
                .Parameters(1).Value = Simdata.It.LcId
                .Parameters(2).Value = DistCompItems(LoopItems).ItemComponent
                .Parameters(3).Value = Format(FcstItem, "0.00")
                .Parameters(4).Value = Format(FcstKitBom, "0.00")
                .Parameters(5).Value = Format(FcstItemRT, "0.00")
                .Parameters(6).Value = Format(FcstItemLT, "0.00")
                .Parameters(7).Value = Format(FcstItemMonth, "0.00")
                .Parameters(8).Value = Format(FcstItemQtr, "0.00")
                .Parameters(9).Value = Format(FcstItemYear, "0.00")

                .Execute , , adCmdStoredProc + adExecuteNoRecords
            End With

            If Cn2.Errors.Count > 0 Then
                For i = 0 To Cn2.Errors.Count - 1
                    strMessage = getTranslationResource("AIM_DemandUpdateForCompItems_Sp Error") & _
                        " " & Trim$(CStr(Simdata.It.LcId)) & "/" & Trim$(CStr(Simdata.It.Item)) & ": "
                    strMessage = strMessage & Cn2.Errors(i).Description
                    Write_Log LogFile, strMessage, 2, False, True, False, True
            
                    'This is subsequent update from DemandUpdate job - sgajjela
                    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobError, False, , False)

                    Cn2.Errors.Clear
                Next i
            End If
        Next LoopItems

        'Now process the KitBOM items which does not have Comp items
        'Because these are not processed
        For LoopItems = 1 To UBound(DistKitBOMItems)
            If DistKitBOMItems(LoopItems).Processed_YN = "N" Then
                'call the update function which will update the item table with the demand
                Cn2.Errors.Clear
                
                BldKitBOMSummary AIM_KitBOMSummary_Sp, AIM_KitBOMSummary_Upd_Sp, rsAIM_KitBOMSummary, Simdata.It.LcId, _
                DistKitBOMItems(LoopItems).ItemComponent, DistKitBOMItems(LoopItems).FcstKitBOMLT, _
                DistKitBOMItems(LoopItems).FcstKitBOMRT, DistKitBOMItems(LoopItems).FcstKitBOMMonth, _
                DistKitBOMItems(LoopItems).FcstKitBOMQtr, DistKitBOMItems(LoopItems).FcstKitBOMYear, _
                DistKitBOMItems(LoopItems).Fcst, DistKitBOMItems(LoopItems).FcstKitBom, DistKitBOMItems(LoopItems).MAEKitBOM
               ' UpdateDemandForCompItem AIM_DemandUpdateForCompItems_Sp, SimData
                With AIM_DemandUpdateForKitBOMItems_Sp
                    .Parameters(1).Value = Simdata.It.LcId
                    .Parameters(2).Value = DistKitBOMItems(LoopItems).ItemComponent
                    .Parameters(3).Value = Format(DistKitBOMItems(LoopItems).FcstKitBom, "0.00")
                    .Parameters(4).Value = Format(DistKitBOMItems(LoopItems).FcstKitBOMRT, "0.00")
                    .Parameters(5).Value = Format(DistKitBOMItems(LoopItems).FcstKitBOMLT, "0.00")
                    .Parameters(6).Value = Format(DistKitBOMItems(LoopItems).FcstKitBOMMonth, "0.00")
                    .Parameters(7).Value = Format(DistKitBOMItems(LoopItems).FcstKitBOMQtr, "0.00")
                    .Parameters(8).Value = Format(DistKitBOMItems(LoopItems).FcstKitBOMYear, "0.00")
                    .Execute , , adCmdStoredProc + adExecuteNoRecords
    
                End With
            
                If Cn2.Errors.Count > 0 Then
                    For i = 0 To Cn2.Errors.Count - 1
                        strMessage = getTranslationResource("AIM_DemandUpdateForKitBOMItems_Sp Error") & _
                            " " & Trim$(CStr(Simdata.It.LcId)) & "/" & Trim$(CStr(Simdata.It.Item)) & ": "
                        strMessage = strMessage & Cn2.Errors(i).Description
                        Write_Log LogFile, strMessage, 2, False, True, False, True
                        
                        'This is subsequent update from DemandUpdate job - sgajjela
                        Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobError, False, , False)

                        
                        Cn2.Errors.Clear
                    Next i
                End If
            End If
        Next LoopItems
        
        'Update the Last Forecast Cycle
        rsLc.Update "Last_FcstUpdCyc", Simdata.LC.Last_FcstUpdCyc

NextLocation:
        'Determine the next Location Id
        rsLc.MoveNext
    Loop        'End of Location Loop
    
    'Run the Build MDC Summary Table if elected
    If Simdata.MDCOption = "Y" And MDCOption = "Y" Then
        BldMDCSummary
    End If
    
    'Write message to log file
    strMessage = getTranslationResource("Items Updated", True) & CStr(Format(UpdateCount, "#,##0"))
    Write_Log LogFile, strMessage, 2, True, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)

    
    
    strMessage = getTranslationResource("Intermittent Items", True) & CStr(Format(intCount, "#,##0"))
    Write_Log LogFile, strMessage, 2, False, False, False, False
    
    'This is subsequent update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)
    
    strMessage = getTranslationResource("Demand Update Complete.")
    Write_Log LogFile, strMessage, 0, True, False, False, True
    
    'This is subsequent & final update from DemandUpdate job - sgajjela
    Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::DemandUpdate", "DataInterface_Main::DemandUpdate", Now(), gDRJobUpdate, False, , False)

    
    Write_Log LogFile, "", 0, False, True, True, False
    
    DemandUpdate = SUCCEED
    
GracefulExit:
    If f_IsRecordsetValidAndOpen(rsLc) Then rsLc.Close
    Set rsLc = Nothing
    If f_IsRecordsetValidAndOpen(rsduGet) Then rsduGet.Close
    Set rsduGet = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMPromotions) Then rsAIMPromotions.Close
    Set rsAIMPromotions = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If f_IsRecordsetValidAndOpen(rsKitBOMItemsLTRT) Then rsKitBOMItemsLTRT.Close
    Set rsKitBOMItemsLTRT = Nothing
    If f_IsRecordsetValidAndOpen(rsKitBOMItemsForALoc) Then rsKitBOMItemsForALoc.Close
    Set rsKitBOMItemsForALoc = Nothing
    If f_IsRecordsetValidAndOpen(rsItemsForAKitBOMItem) Then rsItemsForAKitBOMItem.Close
    Set rsItemsForAKitBOMItem = Nothing
    If f_IsRecordsetValidAndOpen(rsComItemsLTRT) Then rsComItemsLTRT.Close
    Set rsComItemsLTRT = Nothing
    If f_IsRecordsetValidAndOpen(rsCompItemsForALoc) Then rsCompItemsForALoc.Close
    Set rsCompItemsForALoc = Nothing
    If f_IsRecordsetValidAndOpen(rsAIM_KitBOMSummary) Then rsAIM_KitBOMSummary.Close
    Set rsAIM_KitBOMSummary = Nothing
    If f_IsRecordsetValidAndOpen(rsAIM_GetFcstAdj) Then rsAIM_GetFcstAdj.Close
    Set rsAIM_GetFcstAdj = Nothing
     

    If Not (AIM_AIMLocations_List_Sp Is Nothing) Then Set AIM_AIMLocations_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLocations_List_Sp = Nothing
    If Not (AIM_AIMPromotions_GetKey_Sp Is Nothing) Then Set AIM_AIMPromotions_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_AIMPromotions_GetKey_Sp = Nothing
    If Not (AIM_DemandUpdate_Sp Is Nothing) Then Set AIM_DemandUpdate_Sp.ActiveConnection = Nothing
    Set AIM_DemandUpdate_Sp = Nothing
    If Not (AIM_DUGet_Sp Is Nothing) Then Set AIM_DUGet_Sp.ActiveConnection = Nothing
    Set AIM_DUGet_Sp = Nothing
    If Not (AIM_MDCDetail_Sp Is Nothing) Then Set AIM_MDCDetail_Sp.ActiveConnection = Nothing
    Set AIM_MDCDetail_Sp = Nothing
    If Not (AIM_KitBOMSummary_Sp Is Nothing) Then Set AIM_KitBOMSummary_Sp.ActiveConnection = Nothing
    Set AIM_KitBOMSummary_Sp = Nothing
    If Not (AIM_KitBOMSummary_Upd_Sp Is Nothing) Then Set AIM_KitBOMSummary_Upd_Sp.ActiveConnection = Nothing
    Set AIM_KitBOMSummary_Upd_Sp = Nothing
      If Not (AIM_CompanionItemDetail_Sp Is Nothing) Then Set AIM_CompanionItemDetail_Sp.ActiveConnection = Nothing
    Set AIM_CompanionItemDetail_Sp = Nothing
      If Not (AIM_GetKitBOMItemsLTRT_Sp Is Nothing) Then Set AIM_GetKitBOMItemsLTRT_Sp.ActiveConnection = Nothing
    Set AIM_GetKitBOMItemsLTRT_Sp = Nothing
      If Not (AIM_GetKitBOMItemsForALoc_Sp Is Nothing) Then Set AIM_GetKitBOMItemsForALoc_Sp.ActiveConnection = Nothing
    Set AIM_GetKitBOMItemsForALoc_Sp = Nothing
      If Not (AIM_GetItemsForAKitBOMItem_Sp Is Nothing) Then Set AIM_GetItemsForAKitBOMItem_Sp.ActiveConnection = Nothing
    Set AIM_GetItemsForAKitBOMItem_Sp = Nothing
      If Not (AIM_GetCompItemsLTRT_Sp Is Nothing) Then Set AIM_GetCompItemsLTRT_Sp.ActiveConnection = Nothing
    Set AIM_GetCompItemsLTRT_Sp = Nothing
      If Not (AIM_GetCompItemsForALoc_Sp Is Nothing) Then Set AIM_GetCompItemsForALoc_Sp.ActiveConnection = Nothing
    Set AIM_GetCompItemsForALoc_Sp = Nothing
      If Not (AIM_GetItemsForACompItem_Sp Is Nothing) Then Set AIM_GetItemsForACompItem_Sp.ActiveConnection = Nothing
    Set AIM_GetItemsForACompItem_Sp = Nothing
      If Not (AIM_KitBOMSummary_Upd_Sp Is Nothing) Then Set AIM_KitBOMSummary_Upd_Sp.ActiveConnection = Nothing
    Set AIM_KitBOMSummary_Upd_Sp = Nothing
      If Not (AIM_DemandUpdateForCompItems_Sp Is Nothing) Then Set AIM_DemandUpdateForCompItems_Sp.ActiveConnection = Nothing
    Set AIM_DemandUpdateForCompItems_Sp = Nothing
     If Not (AIM_DemandUpdateForKitBOMItems_Sp Is Nothing) Then Set AIM_DemandUpdateForKitBOMItems_Sp.ActiveConnection = Nothing
    Set AIM_DemandUpdateForKitBOMItems_Sp = Nothing
     If Not (AIM_GetFcstAdj_Sp Is Nothing) Then Set AIM_GetFcstAdj_Sp.ActiveConnection = Nothing
    Set AIM_GetFcstAdj_Sp = Nothing
    
    RtnCode = SQLConnection(Cn1, CONNECTION_CLOSE, False)
    Set Cn1 = Nothing
    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
    Set Cn2 = Nothing
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
            Err.Description & " -- " & _
            Err.source & "(DataInterface_Main)" & "(DemandUpdate)", _
            0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::DemandUpdate", Now, gDRJobError, True, Err

    On Error Resume Next
    GoTo GracefulExit
    
End Function

Sub Main()
    'Entry point to this project
    'To Debug, set commandline at Project --> Properties --> Make --> Command Line argument
    'for example, <jobid> <server> <database> <userid> <password> N A All All All Y Y Y Y
    'Use the Job Wizard to generate these command line arguments correctly.
On Error GoTo ErrorHandler
    Dim Cn As ADODB.Connection
    
    Dim CmdLine As String
    Dim CmdLineArgs(20) As String
    Dim ExitCode As Long
    Dim JobId As Integer
    Dim RtnCode As Long
    Dim LogFile As String
    Dim BufLgt As Long
    Dim TempString As String
    Dim ErrorObject As ErrObject
    Dim rsSysCtrl As ADODB.Recordset
    Dim Action As Integer
    
       
    'By Default enable error logging -sgajjela
    gLogErrors = True
    
    Set gDBConnection = New ADODB.Connection
    
    'Error logging information includes computer name. So, we need it while error logging. -sgajjela
    BufLgt = MAX_COMPUTERNAME_LENGTH
    gComputerName = String(MAX_COMPUTERNAME_LENGTH, " ")
    RtnCode = GetComputerNameA(gComputerName, BufLgt)
    gComputerName = Left(gComputerName, BufLgt - 1)
    
    'Get the command line arguments
    CmdLine = Command
    'Check for no arguments
     If Trim$(CmdLine) = "" Then
        'Write error message and terminate
        LogFile = App.Path & "\" & "EXceedAIMDataInterface.Log"
        m_LogFile = LogFile
        'Write message -- do not attempt translations from database
        Write_Log LogFile, "", 0, False, True, True, False
        Write_Log LogFile, "Batch Processor Started.", 0, False, True, False, True
        Call f_HandleErr(0, "Batch Processor Started", "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)
        
        TempString = Err.source & "(DataInterface_Main.Main)" & "Invalid Command Line -- Batch Processor Cancelled."
        Write_Log LogFile, TempString, 2, False, True, False, False
        Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)
        
        Write_Log LogFile, "Batch Processor Terminated.", 0, False, False, False, True
        Call f_HandleErr(0, vbNewLine & "Batch Processor Terminated.", "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)
        
        Write_Log LogFile, "", 0, False, True, True, False
        'Terminate
        Exit Sub
    End If
    
    'Isolate individual arguments
    RtnCode = Parse(CmdLine, " ,", "", CmdLineArgs())
    
    'Test the SQL Logon Capability
    gServer = CmdLineArgs(1)
    gDataBase = CmdLineArgs(2)
    gUserID = CmdLineArgs(3)
    gPassWord = CmdLineArgs(4)
    gWinNTAuth = CmdLineArgs(5)
    'Connect to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    'Validate connection
    If RtnCode <> SUCCEED Then
        'Write error message and terminate
        LogFile = App.Path & "\" & "EXceedAIMDataInterface.Log"
        m_LogFile = LogFile
        'Write message -- do not attempt translations from database
        Write_Log LogFile, "", 0, False, True, True, False
        Write_Log LogFile, "Batch Processor Started.", 0, False, True, False, True
        Call f_HandleErr(0, "Batch Processor Started", "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)
        
        
        Write_Log LogFile, Err.source & "(DataInterface_Main.Main)" & _
                "Unable to connect to SQL -- Batch Processor Cancelled.", _
                2, False, True, False, False
        Call f_HandleErr(0, vbNewLine & Err.source & "(DataInterface_Main.Main)" & "Unable to connect to SQL -- Batch Processor Cancelled.", _
                        "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)
        
        'Write connection information for reference
        Write_Log LogFile, "Server" & ": " & CStr(gServer), 2, False, False, False, False
        Write_Log LogFile, "Database" & ": " & CStr(gDataBase), 2, False, False, False, False
        Write_Log LogFile, "UserId" & ": " & CStr(gUserID), 2, False, False, False, False
        Write_Log LogFile, "Password" & ": " & CStr(gPassWord), 2, False, False, False, False
        Write_Log LogFile, "Windows NT Authorization" & ": " & CStr(gWinNTAuth), 2, False, True, False, False
        Call f_HandleErr(0, vbNewLine & "Server" & ": " & CStr(gServer) & "Database" & ": " & CStr(gDataBase) & "UserId" & ": " & CStr(gUserID) & "Password" & ": " & CStr(gPassWord) & "Windows NT Authorization" & ": " & CStr(gWinNTAuth), _
                        "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)
        
        
        Write_Log LogFile, "Batch Processor Terminated.", 0, False, False, False, True
        Call f_HandleErr(0, vbNewLine & "Batch Processor Terminated", "DataInterface_Main::Main", "DataInterface_Main::Main", Now(), gDRJobUpdate)

        Write_Log LogFile, "", 0, False, True, True, False
        'Terminate
        Exit Sub
    Else
        'Connection is valid. Destroy this one and proceed to requested operation.
        RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
        Set Cn = Nothing
    End If
    
    RtnCode = SQLConnection(gDBConnection, CONNECTION_OPEN, False)
    
    'Get the System Control Record to find out the logging options -gajjela
    Set rsSysCtrl = New ADODB.Recordset
    GetSysInfo gDBConnection, rsSysCtrl
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
    gLogFile = rsSysCtrl("LogFile").Value
    gWantToLogToDB = rsSysCtrl("WantErrorsTobeLoggedToDatabase").Value
    
    
    
    'Get the Job Id
    JobId = CInt(CmdLineArgs(0))
    'NOTE: All array elements in CmdLineArgs will be Strings, _
    ' so be sure to apply the proper datatype conversions _
    ' based on the function parameter definitions.
    Select Case JobId
        Case G_JOBID_DATA_INTERFACE       'Data Interface
            If UCase(CmdLineArgs(6)) = UCase(g_DI_TT_PO) Then
                'process outbound
                'Purchase Order Upload to Host
                RtnCode = DxPO_Ctrl(Trim$(CmdLineArgs(8)), "ALL", "ALL", "ALL")
            
            ElseIf UCase(CmdLineArgs(6)) = UCase(g_DI_TT_AO) Then
                'process outbound
                'Allocated Order upload to host
                RtnCode = DxAO_Outbound(CBool(CmdLineArgs(8)), "ALL")
            
            Else
                'process inbounds
                RtnCode = DataInterface(CmdLineArgs(6), CmdLineArgs(7))
            End If
            
        Case G_JOBID_DEMAND_UPDATE         'Demand Update
            RtnCode = DemandUpdate(CmdLineArgs(6), CmdLineArgs(7), "", _
                                CInt(CmdLineArgs(8)), CInt(CmdLineArgs(9)), _
                                CmdLineArgs(10), CmdLineArgs(11))
            'Running this code twice Kevin hill
'            If Trim$(UCase(CmdLineArgs(11))) = "Y" Then
'                BldMDCSummary
'            End If
            
        Case G_JOBID_ORDER_GENERATION        'Order Generation
            RtnCode = OrderGeneration(CmdLineArgs(6), CmdLineArgs(7), _
                                    CmdLineArgs(8), CmdLineArgs(9), _
                                    CmdLineArgs(10), CmdLineArgs(11), _
                                    CmdLineArgs(12), CmdLineArgs(13))
            
        Case G_JOBID_SEASONALITY_PROFILE         'Seasonality Generation
            RtnCode = SeasonalityUpdate(CmdLineArgs(6), CInt(CmdLineArgs(7)), _
                        CInt(CmdLineArgs(8)), CInt(CmdLineArgs(9)), _
                        CInt(CmdLineArgs(10)), CInt(CmdLineArgs(11)))
            
        Case G_JOBID_VELOCITY_CODE         'Velocity Code Assignment
            RtnCode = VelCodeAssignment(CmdLineArgs(6), CmdLineArgs(7))
            
        Case G_JOBID_ORDER_POLICY         'Order Policy Assignment
            'Currently not available
            
        Case G_JOBID_FORECAST_BATCH         'Run Batch Forecast
            'Do the following based on  the CmdLineArgs(7) value
            Action = CmdLineArgs(7)
            'If action is 0 then run the forecast planning genertion process
            'If action is 1 the run the forecast planning generation process and then create the output file
            'If action is 2 then only output file
            If Action = 0 Or Action = 1 Then
                RtnCode = AIMBatchForecastGenerator(CmdLineArgs(6))
            End If
            If Action = 1 Or Action = 2 Then
                RtnCode = DxForecastReport_Ctrl(CmdLineArgs(6))
            End If
    
        Case G_JOBID_ALLOCATION        'Allocation
            RtnCode = Allocation(CInt(CmdLineArgs(6)), CBool(CmdLineArgs(7)))
            
'        Case G_JOBID_DEMANDPLANING_BATCH        'Allocation
'            RtnCode = AIMBatchDemandPlanner(CmdLineArgs(6))

    End Select
        
    Debug.Assert SetDevEnvironmentFlag
    If Not IsDevEnvironment Then
        ExitCode = 0
        ExitProcess ExitCode
    End If
    
Exit Sub
ErrorHandler:
    Dim Description As String
    Dim ErrorNumber As Long
    Dim source As String

    
    Description = Err.Description
    source = Err.source
    ErrorNumber = Err.Number
    f_HandleErr , , , "DataInterface_Main::Main", Now, gDRJobError, True, Err
    
'    Write_Log m_LogFile, CStr(ErrorNumber) & " -- " & _
'            Description & " -- " & _
'            Source & "(DataInterface_Main)" & "(Main)", _
'            0, True, True, True, True
    'sgajjela
    
            
    On Error Resume Next
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
End Sub

Private Function OrderGeneration(FilterOpt As String, RevCycleKey As String, VnIdKey As String, AssortKey As String, _
                        ClearPO As String, LT_Flag As String, PE_Flag As String, OutPutOpt As String)

On Error GoTo ErrorHandler

    Dim Cn As ADODB.Connection
        
    Dim AIM_OrdGenCtrl_Sp As ADODB.Command
    Dim rsSysCtrl As ADODB.Recordset

    Dim RtnCode As Integer
    
    'Order Generation Return Values
    Dim ItemsReviewed As Long
    Dim LinePointReveiws As Long
    Dim LeadTimeExceptions As Long
    Dim PriorityExceptions As Long
    Dim ProductionConstraint As String
    
    Dim TempString As String
    
    'System Control Values
    Dim LogFile As String

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        Exit Function
    End If
    
    'Define the stored procedures
    Set AIM_OrdGenCtrl_Sp = New ADODB.Command
    With AIM_OrdGenCtrl_Sp
        .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_OrdGenCtrl_Sp"
        .CommandTimeout = 0
        .Parameters.Refresh
    
    End With
    
    'Get the System Control Record
    Set rsSysCtrl = New ADODB.Recordset
    GetSysInfo Cn, rsSysCtrl
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If

    LogFile = rsSysCtrl("LogFile").Value
    gDateFormat = rsSysCtrl!DateFormat
    gTimeFormat = rsSysCtrl!TimeFormat
    m_LogFile = LogFile
    
    ProductionConstraint = rsSysCtrl("productionconstraint").Value
    
    'Close the sys ctrl recordset
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Write message to Log File
    Write_Log LogFile, "", 0, False, True, True, False
    
    
    'Write DR Job status update messages to the error table as well - sgajjela
    TempString = getTranslationResource("Order Generation -- Started.")
    Write_Log LogFile, TempString, 0, False, True, False, True
    Call f_HandleErr(0, TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate, False, , True)
    

        
    'Output Command Line Parameters
    TempString = getTranslationResource("Filter Option", True) & CStr(FilterOpt)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Review Cycle", True) & CStr(RevCycleKey)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Vendor ID", True) & CStr(VnIdKey)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Assortment", True) & CStr(AssortKey)
    Write_Log LogFile, getTranslationResource("Assortment", True) & CStr(AssortKey), 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Clear PO's", True) & CStr(ClearPO)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("LT Exceptions", True) & CStr(LT_Flag)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Priority Exceptions", True) & CStr(PE_Flag)
    Write_Log LogFile, TempString, 2, False, False, False, False
    
    TempString = getTranslationResource("Production Constraint", True) & CStr(ProductionConstraint)
    Write_Log LogFile, getTranslationResource("Production Constraint", True) & CStr(ProductionConstraint), 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Log File", True) & CStr(LogFile)
    Write_Log LogFile, getTranslationResource("Log File", True) & CStr(LogFile), 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AimAllocationModule::OrderGeneration", "AimAllocationModule::OrderGeneration", Now(), gDRJobUpdate)
    
    'Execute Order Generation
    With AIM_OrdGenCtrl_Sp
        .Parameters("@FilterOpt").Value = FilterOpt
        .Parameters("@RevCycleKey").Value = RevCycleKey
        .Parameters("@VnIdKey").Value = VnIdKey
        .Parameters("@AssortKey").Value = AssortKey
        .Parameters("@ClearPO").Value = ClearPO
        .Parameters("@LT_Flag").Value = LT_Flag
        .Parameters("@PE_Flag").Value = PE_Flag
        .Parameters("@OutPutOpt").Value = OutPutOpt
        .Parameters("@ReviewTotal").Value = 0
        .Parameters("@LT_Total").Value = 0
        .Parameters("@RC_Total").Value = 0
        .Parameters("@PE_Total").Value = 0
        
        .Execute , , adExecuteNoRecords
        'Check for errors
        If Err.Number <> 0 Then
            Write_Log LogFile, Err.source & "(DataInterface_Main.OrderGeneration)" & _
                        getTranslationResource("Order Generation Error", True) & _
                        CStr(Format(Err.Number, "##0")) & " " & Err.Description, _
                        2, False, True, False, False
            f_HandleErr , , , "DataInterface_Main::Main", Now, gDRJobError, True, Err
            Exit Function
        End If
        On Error GoTo ErrorHandler
        
        If .Parameters(0).Value = SUCCEED Then
            ItemsReviewed = .Parameters("@ReviewTotal").Value
            LinePointReveiws = .Parameters("@RC_Total").Value
            LeadTimeExceptions = .Parameters("@LT_Total").Value
            PriorityExceptions = .Parameters("@PE_Total").Value

        End If
        
    End With
    
    'Apply Production constraint if it is enabled in the SysCtrl table
    If UCase(ProductionConstraint) = "Y" Then
        If FilterOpt = "A" Then
            Prod_Constraint_SizingCtrl "ALL", Cn
        ElseIf FilterOpt = "R" Then
            Prod_Constraint_SizingCtrl RevCycleKey, Cn
        ElseIf FilterOpt = "V" Then
            Prod_Constraint_Sizing VnIdKey, AssortKey, Cn
        End If
    End If

    'Wind Up
    TempString = getTranslationResource("Items Reviewed", True) & CStr(ItemsReviewed)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::OrderGeneration", "DataInterface_Main::OrderGeneration", Now(), gDRJobUpdate)

    
    
    TempString = getTranslationResource("Line Point Reviews", True) & CStr(LinePointReveiws)
    Write_Log LogFile, getTranslationResource("Line Point Reviews", True) & CStr(LinePointReveiws), 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::OrderGeneration", "DataInterface_Main::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Lead Time Exceptions", True) & CStr(LeadTimeExceptions)
    Write_Log LogFile, getTranslationResource("Lead Time Exceptions", True) & CStr(LeadTimeExceptions), 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::OrderGeneration", "DataInterface_Main::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Priority Exceptions", True) & CStr(PriorityExceptions)
    Write_Log LogFile, getTranslationResource("Priority Exceptions", True) & CStr(PriorityExceptions), 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::OrderGeneration", "DataInterface_Main::OrderGeneration", Now(), gDRJobUpdate)
    
    TempString = getTranslationResource("Order Generation Complete.", True)
    Write_Log LogFile, getTranslationResource("Order Generation Complete."), 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::OrderGeneration", "DataInterface_Main::OrderGeneration", Now(), gDRJobUpdate)
    Write_Log LogFile, "", 0, False, True, True, False
    
    
    OrderGeneration = SUCCEED
    
GracefulExit:
    If Not (AIM_OrdGenCtrl_Sp Is Nothing) Then Set AIM_OrdGenCtrl_Sp.ActiveConnection = Nothing
    Set AIM_OrdGenCtrl_Sp = Nothing
    
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing

    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
        Err.Description & " -- " & _
        Err.source & "(DataInterface_Main)" & "(OrderGeneration)", _
        0, True, True, True, True
    
    f_HandleErr , , , "DataInterface_Main::OrderGeneration", Now, gDRJobError, True, Err
    On Error Resume Next
    GoTo GracefulExit
End Function

Private Function SeasonalityUpdate(LcId As String, EndYear As Integer, _
    EndPeriod As Integer, NbrYears As Integer, SAVersion As Integer, _
    ClassOption As Integer)
On Error GoTo ErrorHandler

    Dim Cn1 As ADODB.Connection
    Dim Cn2 As ADODB.Connection
    
    Dim AIM_SASummary_Sp As ADODB.Command
    
    Dim rsLcList As ADODB.Recordset
    Dim rsSaWork As ADODB.Recordset
    Dim rsAIMSeasons As ADODB.Recordset
    Dim rsSysCtrl As ADODB.Recordset
    
    Dim Class As String
    Dim FirstTime As Boolean
    Dim HsYear As Integer
    Dim LogFile As String
    Dim Pd As Integer
    Dim RtnCode As Integer
    Dim UpdateCount As Long
    Dim SeasSmoothingPds As Integer
    Dim StartPeriod As Integer
    Dim StartYear As Integer
    Dim SqlStmt As String
    Dim TempString As String
    
    
    Dim SaWork As SAWORK_RCD
    
    'Calculate the end year and period
    If EndPeriod < 52 Then
        StartYear = EndYear - NbrYears
        StartPeriod = EndPeriod + 1
    Else
        StartYear = EndYear - (NbrYears - 1)
        StartPeriod = 1
    End If
    
    'Set the Class Option
    Class = "class" & Format(ClassOption, "0")
    
    SaWork.SAVersion = SAVersion
    SaWork.NbrYears = NbrYears
    
    'Initialize First Time
    FirstTime = True
    
    'Open connections to the server
    Set Cn1 = New ADODB.Connection
    RtnCode = SQLConnection(Cn1, CONNECTION_OPEN, False)
    Set Cn2 = New ADODB.Connection
    RtnCode = SQLConnection(Cn2, CONNECTION_OPEN, False)
    
    'Initialize the System Control
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    GetSysInfo Cn1, rsSysCtrl
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    Else
        SeasSmoothingPds = rsSysCtrl("SeasSmoothingPds").Value
        LogFile = rsSysCtrl("LogFile").Value
        gDateFormat = rsSysCtrl!DateFormat
        gTimeFormat = rsSysCtrl!TimeFormat
        m_LogFile = LogFile
    End If
    'Close System Control
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Write message to log file
    Write_Log LogFile, "", 0, False, True, True, False
    
    TempString = getTranslationResource("Seasonality Update Started", True)
    Write_Log LogFile, "Seasonality Update Started", 0, False, True, False, True
    Call f_HandleErr(0, TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False, , True)
    
    
    'Output Command Line Parameters
    TempString = getTranslationResource("Location Option", True) & CStr(LcId)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    Write_Log LogFile, TempString, 2, False, False, False, False
    TempString = getTranslationResource("End Year", True) & CStr(EndYear)
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    TempString = getTranslationResource("End Period", True) & CStr(EndPeriod)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    TempString = getTranslationResource("Number Years", True) & CStr(NbrYears)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    TempString = getTranslationResource("Seasonality Version", True) & CStr(SAVersion)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    TempString = getTranslationResource("Class Option", True) & CStr(ClassOption)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    'No need to show the logfile name in the logfile sri Aug-08-05
'    TempString = getTranslationResource("Log File", True) & CStr(LogFile)
'    Write_Log LogFile, TempString, 2, False, False, False, False
'    Call f_HandleErr(0, TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate)
    
    'Drop the version being creating
    If UCase(LcId) = "ALL" Or UCase(LcId) = getTranslationResource("ALL") Then
        SqlStmt = "delete AIMSeasons where SAVersion = " & Format(SAVersion, "0") & _
                 " and SaId <> N'DEFAULT' "
    Else
        SqlStmt = "delete AIMSeasons where LcId = N'" & LcId & "'" & _
                " and SAVersion = " & Format(SAVersion, "0") & _
                " and SaId <> N'DEFAULT' "
    End If
    
    Cn1.Execute SqlStmt
    
    'Open the output cursor
    If UCase(LcId) = "ALL" _
    Or UCase(LcId) = getTranslationResource("ALL") _
    Then
        SqlStmt = "SELECT * FROM AIMSeasons " & _
                    " WHERE SAVersion = " & Format(SAVersion, "0") & " "
    Else
        SqlStmt = "SELECT * FROM AIMSeasons " & _
                    " WHERE LcId = N'" & LcId & "'" & _
                    " AND SAVersion = " & Format(SAVersion, "0") & " "
    End If
    
    'Initialize the AIM Seasonality Table Record Set
    Set rsAIMSeasons = New ADODB.Recordset
    With rsAIMSeasons
       Set .ActiveConnection = Cn2
       .CursorLocation = adUseClient
       .CursorType = adOpenStatic
       .LockType = adLockBatchOptimistic
    End With
    rsAIMSeasons.Open SqlStmt
     'After the  delete statement  the select does not return any rows so no checking is necessary 'sri Aug-08-05
'    If Not f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then
'        TempString = getTranslationResource("Error! No records available for AIMSeasons!")
'        f_HandleErr , , , "DataInterface_Main::SeasonalityUpdate", Now, gDRJobError, True, Err
'        Write_Log LogFile, TempString, 2, True, True, False, True
'    End If

    'Initialize the Seasonality Summary Stored Procedure
    Set AIM_SASummary_Sp = New ADODB.Command
    With AIM_SASummary_Sp
        Set .ActiveConnection = Cn1
        .CommandText = "AIM_SASummary_Sp"
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    
    'Initialize the Location Record Set
    Set rsLcList = New ADODB.Recordset
    With rsLcList
       Set .ActiveConnection = Cn1
       .CursorLocation = adUseClient
       .CursorType = adOpenForwardOnly
       .LockType = adLockReadOnly
    End With
     
    'Build the List of Locations
    If UCase(LcId) = "ALL" _
    Or UCase(LcId) = getTranslationResource("ALL") _
    Then
        SqlStmt = "SELECT LcID FROM AIMLocations "
    Else
        SqlStmt = "SELECT LcID FROM AIMLocations " & _
                " WHERE LcID = N'" & LcId & "'" & _
                " ORDER BY LcID"
    End If
    rsLcList.Open SqlStmt
    If f_IsRecordsetOpenAndPopulated(rsLcList) Then
        Do Until rsLcList.EOF
            SaWork.LcId = rsLcList("lcid").Value
            'Build the SQL Statement
            SqlStmt = "SELECT ItemHistory.LcId, "
            SqlStmt = SqlStmt & "class = " & "Item." & Class & ", "
            SqlStmt = SqlStmt & "classdesc = coalesce(AIMClasses.ClassDesc, 'No Description Found'), "
            SqlStmt = SqlStmt & "ItemHistory.hisyear, "
            SqlStmt = SqlStmt & "sum01 = sum(cps01), sum02 = sum(cps02), sum03 = sum(cps03), "
            SqlStmt = SqlStmt & "sum04 = sum(cps04), sum05 = sum(cps05), sum06 = sum(cps06), "
            SqlStmt = SqlStmt & "sum07 = sum(cps07), sum08 = sum(cps08), sum09 = sum(cps09), "
            SqlStmt = SqlStmt & "sum10 = sum(cps10), sum11 = sum(cps11), sum12 = sum(cps12), "
            SqlStmt = SqlStmt & "sum13 = sum(cps13), sum14 = sum(cps14), sum15 = sum(cps15), "
            SqlStmt = SqlStmt & "sum16 = sum(cps16), sum17 = sum(cps17), sum18 = sum(cps18), "
            SqlStmt = SqlStmt & "sum19 = sum(cps19), sum20 = sum(cps20), sum21 = sum(cps21), "
            SqlStmt = SqlStmt & "sum22 = sum(cps22), sum23 = sum(cps23), sum24 = sum(cps24), "
            SqlStmt = SqlStmt & "sum25 = sum(cps25), sum26 = sum(cps26), sum27 = sum(cps27), "
            SqlStmt = SqlStmt & "sum28 = sum(cps28), sum29 = sum(cps29), sum30 = sum(cps30), "
            SqlStmt = SqlStmt & "sum31 = sum(cps31), sum32 = sum(cps32), sum33 = sum(cps33), "
            SqlStmt = SqlStmt & "sum34 = sum(cps34), sum35 = sum(cps35), sum36 = sum(cps36), "
            SqlStmt = SqlStmt & "sum37 = sum(cps37), sum38 = sum(cps38), sum39 = sum(cps39), "
            SqlStmt = SqlStmt & "sum40 = sum(cps40), sum41 = sum(cps41), sum42 = sum(cps42), "
            SqlStmt = SqlStmt & "sum43 = sum(cps43), sum44 = sum(cps44), sum45 = sum(cps45), "
            SqlStmt = SqlStmt & "sum46 = sum(cps46), sum47 = sum(cps47), sum48 = sum(cps48), "
            SqlStmt = SqlStmt & "sum49 = sum(cps49), sum50 = sum(cps50), sum51 = sum(cps51), "
            SqlStmt = SqlStmt & "sum52 = sum(cps52), "
            SqlStmt = SqlStmt & "itmcnt = count(*) "
            SqlStmt = SqlStmt & "from ItemHistory "
            SqlStmt = SqlStmt & "INNER JOIN Item ON ItemHistory.LcId = Item.Lcid AND "
            SqlStmt = SqlStmt & "ItemHistory.Item = Item.Item "
            SqlStmt = SqlStmt & " INNER JOIN Itstatus on Item.ItStat = Itstatus.ItStat "
            SqlStmt = SqlStmt & "LEFT OUTER JOIN AIMClasses ON Item." & Class & " = AIMClasses.Class "
            SqlStmt = SqlStmt & "WHERE ItemHistory.LcId = N'" & rsLcList("Lcid").Value & "' "
            SqlStmt = SqlStmt & "AND ItemHistory.HisYear BETWEEN " & Format(StartYear, "0000")
            SqlStmt = SqlStmt & " AND " & Format(EndYear, "0000") & " "
            SqlStmt = SqlStmt & " AND Itstatus.inclinseasonsgen = N'Y'"
            SqlStmt = SqlStmt & "GROUP BY ItemHistory.LcId, " & "Item." & Class _
                & ", AIMClasses.ClassDesc, HisYear "
            SqlStmt = SqlStmt & "ORDER BY ItemHistory.LcId, " & "Item." & Class & ", HisYear "
        
            'Re-initialize the Seasonality Record Set
            If f_IsRecordsetValidAndOpen(rsSaWork) Then rsSaWork.Close
            Set rsSaWork = Nothing
            Set rsSaWork = New ADODB.Recordset
            With rsSaWork
                Set .ActiveConnection = Cn1
                .CursorLocation = adUseClient
                .CursorType = adOpenForwardOnly
                .LockType = adLockReadOnly
            End With
            'Build the Seasonality Work Record Set
            rsSaWork.Open SqlStmt
            If f_IsRecordsetOpenAndPopulated(rsSaWork) Then
                Do Until rsSaWork.EOF
                    'Check for change in class code
                    If SaWork.Class <> rsSaWork("class").Value _
                    And Not FirstTime _
                    Then
                       ' If f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then
                            'Add Seasonality Record
                            AIMSeasons_Add SeasSmoothingPds, SaWork, rsAIMSeasons
                       ' End If
                    End If
                    
                    'Initialize Work Record
                    SaWork.Class = rsSaWork("class").Value
                    SaWork.SaId = Trim$(SaWork.LcId) & "." & SaWork.Class
                    SaWork.SaDesc = Trim$(SaWork.LcId) & " - " & Trim$(rsSaWork("Classdesc").Value)
                    
                    'Check for field lenght overflow
                    If Len(SaWork.SaDesc) > 50 Then
                        SaWork.SaDesc = Left(SaWork.SaDesc, 50)
                    End If
        
                   'Accumulate summary sales
                    HsYear = rsSaWork("hisyear").Value
                    
                    For Pd = 1 To 52
                        If HsYear = StartYear And Pd >= StartPeriod Then
                            SaWork.SumSales(Pd) = SaWork.SumSales(Pd) + rsSaWork("sum" & Format(Pd, "00")).Value
                            SaWork.TotSales = SaWork.TotSales + rsSaWork("sum" & Format(Pd, "00")).Value
                        ElseIf HsYear > StartYear And HsYear < EndYear Then
                            SaWork.SumSales(Pd) = SaWork.SumSales(Pd) + rsSaWork("sum" & Format(Pd, "00")).Value
                            SaWork.TotSales = SaWork.TotSales + rsSaWork("sum" & Format(Pd, "00")).Value
                        ElseIf HsYear = EndYear And Pd <= EndPeriod Then
                            SaWork.SumSales(Pd) = SaWork.SumSales(Pd) + rsSaWork("sum" & Format(Pd, "00")).Value
                            SaWork.TotSales = SaWork.TotSales + rsSaWork("sum" & Format(Pd, "00")).Value
                        End If
                        
                    Next Pd
                    
                    SaWork.ItmCnt = SaWork.ItmCnt + rsSaWork("itmcnt").Value
                    
                    'Update First Time Switch
                    FirstTime = False
                
                    'Get the next Seasonality Work Record
                    rsSaWork.MoveNext
                    
                Loop
                'Write Seasonality Record
                'If f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then
                 If rsSaWork.RecordCount > 0 Then
                    AIMSeasons_Add SeasSmoothingPds, SaWork, rsAIMSeasons
                    'Update Batch
                    rsAIMSeasons.UpdateBatch
                End If
                'End If
                
            End If
            
            'rsaimseasons.RecordCount
            'Close Work Result Set
            If f_IsRecordsetValidAndOpen(rsSaWork) Then rsSaWork.Close
            Set rsSaWork = Nothing
            
            'Get next location
            FirstTime = True
            rsLcList.MoveNext
        
        Loop
    End If  'check rslclist for validity
    
    'Create Summary Records
    AIM_SASummary_Sp("@SAVersion").Value = SAVersion
    AIM_SASummary_Sp.Execute
    
    'Update Record Counts
    If f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then UpdateCount = rsAIMSeasons.RecordCount
        
    
    'Write message to log file
    TempString = getTranslationResource("Seasonality Profiles Generated", True) & CStr(UpdateCount)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    TempString = getTranslationResource("Seasonality Update Complete.")
    Write_Log LogFile, TempString, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::SeasonalityUpdate", "DataInterface_Main::SeasonalityUpdate", Now(), gDRJobUpdate, False)
    
    Write_Log LogFile, "", 0, False, True, True, False

GracefulExit:
    If Not (AIM_SASummary_Sp Is Nothing) Then Set AIM_SASummary_Sp.ActiveConnection = Nothing
    Set AIM_SASummary_Sp = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
    Set rsAIMSeasons = Nothing
    If f_IsRecordsetValidAndOpen(rsLcList) Then rsLcList.Close
    Set rsLcList = Nothing
    If f_IsRecordsetValidAndOpen(rsSaWork) Then rsSaWork.Close
    Set rsSaWork = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    RtnCode = SQLConnection(Cn1, CONNECTION_CLOSE, False)
    Set Cn1 = Nothing
    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
    Set Cn2 = Nothing
    
Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
        Err.Description & " -- " & _
        Err.source & "(DataInterface_Main)" & "(SeasonalityUpdate)", _
        0, True, True, True, True
    'sgajjela
    'Call f_HandleErr(Null, False, Null, Null, "DataInterface_Main", "SeasonalityUpdate", Null, "DRJE")
     f_HandleErr , , , "DataInterface_Main::SeasonalityUpdate", Now, gDRJobError, True, Err
    
    On Error Resume Next
    GoTo GracefulExit
    
End Function


Private Function VelCodeAssignment(SelOpt As String, LcIdOpt As String)
On Error GoTo ErrorHandler

    Dim Cn As ADODB.Connection
    Dim AIM_VelCodeCtrl_Sp As ADODB.Command
    Dim rsSysCtrl As ADODB.Recordset

    Dim Rows As Long
    Dim RtnCode As Integer
    Dim LogFile As String
    Dim TempString As String
    

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        Exit Function
    End If
    
    'Define the stored procedures
    Set AIM_VelCodeCtrl_Sp = New ADODB.Command
    With AIM_VelCodeCtrl_Sp
        .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_VelCodeCtrl_Sp"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    'Get the System Control Record
    GetSysInfo Cn, rsSysCtrl
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
    LogFile = rsSysCtrl("LogFile").Value
    gDateFormat = rsSysCtrl!DateFormat
    gTimeFormat = rsSysCtrl!TimeFormat
    m_LogFile = LogFile
    
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Write message to Log File
    Write_Log LogFile, "", 0, False, True, True, False
    
    TempString = getTranslationResource("Velocity Code Assignment -- Started.")
    Write_Log LogFile, TempString, 0, False, True, False, True
    Call f_HandleErr(0, TempString, "DataInterface_Main::VelCodeAssignment", "DataInterface_Main::VelCodeAssignment", Now(), gDRJobUpdate, False, , True)
    
    
    'Output Command Line Parameters
    TempString = getTranslationResource("Selection Option", True) & CStr(SelOpt)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::VelCodeAssignment", "DataInterface_Main::VelCodeAssignment", Now(), gDRJobUpdate, False, , False)
        
    
    TempString = getTranslationResource("Location Option", True) & CStr(LcIdOpt)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::VelCodeAssignment", "DataInterface_Main::VelCodeAssignment", Now(), gDRJobUpdate, False, , False)
    
    TempString = getTranslationResource("Log File", True) & CStr(LogFile)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::VelCodeAssignment", "DataInterface_Main::VelCodeAssignment", Now(), gDRJobUpdate, False, , False)
    
    'Execute Velocity Code Assignment
    With AIM_VelCodeCtrl_Sp
        .Parameters("@SelOpt").Value = SelOpt
        .Parameters("@LcIdOpt").Value = LcIdOpt
        
        .Execute
        
        Rows = .Parameters(0).Value
    End With
    
    'Wind Up
    TempString = getTranslationResource("Items Updated", True) & CStr(Rows)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::VelCodeAssignment", "DataInterface_Main::VelCodeAssignment", Now(), gDRJobUpdate, False, , False)
    
    TempString = getTranslationResource("Velocity Code Assignment Complete.")
    Write_Log LogFile, TempString, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & TempString, "DataInterface_Main::VelCodeAssignment", "DataInterface_Main::VelCodeAssignment", Now(), gDRJobUpdate, False, , False)
    
    Write_Log LogFile, "", 0, False, True, True, False
    
GracefulExit:
    'Clean Up
    If Not (AIM_VelCodeCtrl_Sp Is Nothing) Then Set AIM_VelCodeCtrl_Sp.ActiveConnection = Nothing
    Set AIM_VelCodeCtrl_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing

Exit Function
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
        Err.Description & " -- " & _
        Err.source & "(DataInterface_Main)" & "(VelCodeAssignment)", _
        0, True, True, True, True
        
    f_HandleErr , , , "DataInterface_Main::VelCodeAssignment", Now, gDRJobError, True, Err
    On Error Resume Next
    GoTo GracefulExit
    
End Function

Private Sub LogDxFAErrors(p_Cn As ADODB.Connection, p_LogFile As String)
On Error GoTo ErrorHandler

    Dim strMessage As String, strMessage1 As String
    Dim strSQL As String
    Dim rsDxFA As ADODB.Recordset, cmdDxFA As ADODB.Command
    Dim IndexCounter As Integer
    Dim FieldName As String
    
    strSQL = " SELECT AIMDxFA.LineNbr, AIMDxFA.FcstID, AIMDxFA.LcID, AIMDxFA.Item "
    strSQL = strSQL & ", AIMDxFA.AdjustBegDate, AIMDxFA.AdjustEndDate "
    strSQL = strSQL & " FROM AIMDxFA "
    
    Set rsDxFA = New ADODB.Recordset
    rsDxFA.Open strSQL, p_Cn, adOpenStatic, adLockReadOnly, adCmdText
    If f_IsRecordsetOpenAndPopulated(rsDxFA) Then
        rsDxFA.MoveFirst
        strMessage = getTranslationResource("Data does not match for forecast adjustment lines", True)
        Do While Not rsDxFA.EOF
            strMessage1 = ""
            For IndexCounter = 0 To rsDxFA.Fields.Count - 2
                FieldName = rsDxFA.Fields(IndexCounter).Name
                strMessage1 = strMessage1 & getTranslationResource(FieldName, True) & rsDxFA.Fields(IndexCounter).Value & ", "
            Next IndexCounter
            FieldName = rsDxFA.Fields(IndexCounter).Name
            strMessage1 = strMessage1 & getTranslationResource(FieldName, True) & rsDxFA.Fields(IndexCounter).Value
            
            strMessage = strMessage & vbCrLf & strMessage1
            rsDxFA.MoveNext
        Loop
        
        Write_Log p_LogFile, strMessage, 4, False, False, False, False
        Call f_HandleErr(0, vbNewLine & strMessage, "DataInterface_Main::LogDxFAErrors", "DataInterface_Main::LogDxFAErrors", Now(), gDRJobUpdate)
        
        'Delete these lines.
        Set cmdDxFA = New ADODB.Command
        strSQL = " DELETE FROM AIMDxFA "
        With cmdDxFA
            Set .ActiveConnection = p_Cn
            .CommandType = adCmdText
            .CommandText = strSQL
            .CommandTimeout = 0
            .Execute
        End With
    End If
    
GracefulExit:
    'Clean Up
    If Not (cmdDxFA Is Nothing) Then Set cmdDxFA.ActiveConnection = Nothing
    Set cmdDxFA = Nothing
    If f_IsRecordsetValidAndOpen(rsDxFA) Then rsDxFA.Close
    Set rsDxFA = Nothing

Exit Sub
ErrorHandler:
    Write_Log m_LogFile, CStr(Err.Number) & " -- " & _
        Err.Description & " -- " & _
        Err.source & "(DataInterface_Main)" & "(LogDxFAErrors)", _
        0, True, True, True, True
    f_HandleErr , , , "DataInterface_Main::LogDxFAErrors", Now, gDRJobError, True, Err
    On Error Resume Next
    GoTo GracefulExit
End Sub
