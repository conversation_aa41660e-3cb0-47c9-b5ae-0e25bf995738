VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "EXEStandards"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'******************************************************************
'   This file created Oct 25, 2002.
'   All User Interface standards used in the application need to be
'   defined here as constants for use within the application.
'   For instance, Color Palette definitions, Font Sizes et al.
'   Author: Annalakshmi Stocksdale
'   WORK IN PROGRESS
'******************************************************************

Option Explicit

Public Function GetColor(p_Target As String, p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    If p_ControlType = CTRL_SS_OLEDBCOMBO _
    Or p_ControlType = CTRL_SS_OLEDBDROPDOWN _
    Or p_ControlType = CTRL_SS_OLEDBGRID _
    Then
        GetColor = SetGridColor(p_Target, p_ControlType, p_ColorType)
    
    ElseIf p_ControlType = CTRL_SS_CHART2D _
    Then
        GetColor = SetGraphColor(p_Target, p_ControlType, p_ColorType)
    Else
        Select Case p_Target
            Case TARGET_BACKCOLOR
                GetColor = SetBackColor(p_ControlType, p_ColorType)
                
            Case TARGET_FORECOLOR
                GetColor = SetForeColor(p_ControlType, p_ColorType)
            
            Case Else
                'Here
        End Select
    End If
    
    'If GetColor = 0 Then GetColor = RGB(252, 0, 255)  'Hot Pink, since this is not acceptable
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(GetColor)", Err.Description
End Function


Private Function SetBackColor(p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_ControlType
        Case CTRL_VB_FORM
            'Form=0
            Select Case p_ColorType
                Case COLOR_TYPE_PRIMARY
                    SetBackColor = RGB(230, 230, 230)
                    
                Case COLOR_TYPE_SECONDARY
                    SetBackColor = RGB(72, 88, 197)
                
                Case Else
                    SetBackColor = RGB(230, 230, 230)
                    
            End Select
                
        Case CTRL_VB_LABEL, CTRL_VB_FRAME, CTRL_VB_CHECKBOX, CTRL_VB_OPTIONBUTTON
            'Label=1; Frame=3; CheckBox=5; OptionButton=6
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL
                    SetBackColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    SetBackColor = RGB(72, 88, 197)
                Case COLOR_TYPE_INACTIVE
                    SetBackColor = RGB(236, 238, 248)
                Case Else
                    'Normal
                    SetBackColor = RGB(255, 255, 255)
            End Select
            
        Case CTRL_VB_HSCROLLBAR, CTRL_VB_VSCROLLBAR
            'HScrollBar=9; VScrollbar=10
            
        Case CTRL_VB_TIMER        '11
        Case CTRL_VB_DRIVELISTBOX        '12
        Case CTRL_VB_DIRLISTBOX        '13
        Case CTRL_VB_FILELISTBOX        '14
        Case CTRL_VB_SHAPE        '15
        
        Case CTRL_VB_LINE        '16
        
        Case CTRL_VB_IMAGE        '17
        
        Case CTRL_VB_TABSTRIP        '18
        Case CTRL_VB_TOOLBAR        '19
        Case CTRL_VB_STATUSBAR        '20
        Case CTRL_VB_PROGRESSBAR        '21
        Case CTRL_VB_TREEVIEW        '22
        Case CTRL_VB_LISTVIEW        '23
        Case CTRL_VB_IMAGELIST        '24
        Case CTRL_VB_SLIDER        '25
        Case CTRL_VB_IMAGECOMBO        '26
        
        Case CTRL_VB_COMMANDBUTTON  '30
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
                    SetBackColor = RGB(255, 246, 166)
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    SetBackColor = RGB(255, 230, 0)
                Case Else
                    'Normal
                    SetBackColor = RGB(255, 246, 166)
            End Select
            
        Case CTRL_AR_ARVIEWER2        '50
        
        Case CTRL_VB_TEXTBOX, CTRL_VB_COMBOBOX, CTRL_VB_LISTBOX, _
                CTRL_TDB_NUMBER, CTRL_TDB_CALENDAR, CTRL_TDB_DATE, CTRL_TDB_TEXT, CTRL_TDB_TIME
            'VBTextBox=2; COMBOBOX=7; LISTBOX=8
            'NUMBER=100; CALENDAR=101; DATE=102; TEXT=103; TIME=104
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        White
                    SetBackColor = RGB(255, 255, 255)
                Case Else                  'Bluish
                    SetBackColor = RGB(236, 238, 248)
            End Select
        
        Case CTRL_SS_MONTH        '130
        Case CTRL_SS_DATECOMBO        '131
        Case CTRL_SS_YEAR        '132
        
        Case CTRL_SS_OLEDBGRID, CTRL_SS_OLEDBCOMBO, CTRL_SS_OLEDBDROPDOWN
            '140, 141, 142
            'Should call setgridcolor from GetColor.
            
        Case CTRL_SS_ACTIVETABS        '150
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
                    SetBackColor = RGB(209, 214, 240)
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    SetBackColor = RGB(190, 197, 231)
                Case Else
                    'Normal
                    SetBackColor = RGB(209, 214, 240)
            End Select
            
        Case CTRL_SS_ACTIVETABPANEL        '151
        
        Case CTRL_SS_ACTIVETOOLBARS        '152
            Select Case p_ColorType
                Case COLOR_TYPE_PRIMARY
                    SetBackColor = RGB(230, 230, 230)
                    
                Case COLOR_TYPE_SECONDARY
                    SetBackColor = RGB(72, 88, 197)
                
                Case Else
                    SetBackColor = RGB(230, 230, 230)
                    
            End Select
            
        Case CTRL_SS_COMMONDIALOG        '170

    
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(SetBackColor)", Err.Description
End Function

Private Function SetForeColor(p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_ControlType
        Case CTRL_VB_FORM, CTRL_VB_LABEL, CTRL_VB_FRAME, CTRL_VB_CHECKBOX, CTRL_VB_OPTIONBUTTON, _
                CTRL_VB_TEXTBOX, CTRL_VB_COMBOBOX, CTRL_VB_LISTBOX
        'Form=0; Label=1; Frame=3; CheckBox=5; OptionButton=6
        'TEXTBOX=2; COMBOBOX=7;LISTBOX=8
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL
                    SetForeColor = RGB(80, 80, 80)
                Case COLOR_TYPE_INACTIVE
                    SetForeColor = RGB(160, 160, 160)
            End Select
                
        Case CTRL_VB_TIMER        '11
        
        Case CTRL_VB_DRIVELISTBOX, CTRL_VB_DIRLISTBOX, CTRL_VB_FILELISTBOX
        'DRIVELISTBOX=12; DIRLISTBOX=13; FILELISTBOX=14

        Case CTRL_VB_SHAPE        '15
        Case CTRL_VB_LINE        '16
        
        Case CTRL_VB_IMAGE        '17
        
        Case CTRL_VB_TABSTRIP        '18
        Case CTRL_VB_TOOLBAR        '19
        Case CTRL_VB_STATUSBAR        '20
        Case CTRL_VB_PROGRESSBAR        '21
        Case CTRL_VB_TREEVIEW        '22
        Case CTRL_VB_LISTVIEW        '23
        Case CTRL_VB_IMAGELIST        '24
        Case CTRL_VB_SLIDER        '25
        Case CTRL_VB_IMAGECOMBO        '26
        
        Case CTRL_VB_COMMANDBUTTON  '30
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
                    SetForeColor = RGB(255, 246, 166)
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    SetForeColor = RGB(255, 230, 0)
                Case Else
                    'Normal
                    SetForeColor = RGB(255, 246, 166)
            End Select
            
        Case CTRL_AR_ARVIEWER2        '50
        
        Case CTRL_TDB_NUMBER        '100
        Case CTRL_TDB_CALENDAR        '101
        Case CTRL_TDB_DATE        '102
        Case CTRL_TDB_TEXT        '103
        Case CTRL_TDB_TIME        '104
        
        Case CTRL_SS_MONTH        '130
        Case CTRL_SS_DATECOMBO        '131
        Case CTRL_SS_YEAR        '132
        
        Case CTRL_SS_OLEDBGRID        '140
        Case CTRL_SS_OLEDBCOMBO        '141
        Case CTRL_SS_OLEDBDROPDOWN        '142
        
        Case CTRL_SS_ACTIVETABS        '150
'            Select Case p_ColorType
'                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
'                    SetForeColor = RGB(209, 214, 240)
'                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
'                    SetForeColor = RGB(190, 197, 231)
'                Case Else
'                    'Normal
'                    SetForeColor = RGB(209, 214, 240)
'            End Select
            SetForeColor = RGB(80, 80, 80)
            
        Case CTRL_SS_ACTIVETABPANEL        '151
        
        Case CTRL_SS_ACTIVETOOLBARS        '152
        
        Case CTRL_SS_COMMONDIALOG        '170

    
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(SetForeColor)", Err.Description
End Function

Private Function SetGraphColor(p_Target As String, p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_Target
        Case TARGET_GRAPH_SYMBOL
            Select Case p_ColorType
                Case Else
                    SetGraphColor = RGB(80, 80, 80)
                    
            End Select


        Case TARGET_GRAPH_LINE
            Select Case p_ColorType
                Case Else
                    SetGraphColor = RGB(80, 80, 80) 'Default
                    
            End Select
            
        Case Else
            SetGraphColor = RGB(252, 0, 255)  'Hot Pink, since this is not acceptable
    
    End Select

    If p_ColorType = COLOR_TYPE_INACTIVE Then SetGraphColor = RGB(191, 191, 191)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(SetGraphColor)", Err.Description
End Function

Private Function SetGridColor(p_Target As String, p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_Target
        Case TARGET_BACKCOLOR_EVEN
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    SetGridColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    SetGridColor = RGB(250, 238, 136)
                Case Else   'COLOR_TYPE_READONLY, COLOR_TYPE_NORMAL
                    SetGridColor = RGB(247, 248, 253)   'First Line
            End Select
            
        Case TARGET_BACKCOLOR_ODD
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    SetGridColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    SetGridColor = RGB(250, 238, 136)
                Case Else   'COLOR_TYPE_READONLY, COLOR_TYPE_NORMAL
                    SetGridColor = RGB(236, 238, 248)   'Next Line
            End Select
            
        Case TARGET_BACKCOLOR_COLHEAD
            SetGridColor = RGB(209, 214, 240)
            
        Case TARGET_BACKCOLOR_TABLEHEAD
            SetGridColor = RGB(190, 197, 231)
            
        Case TARGET_BACKCOLOR
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    SetGridColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    SetGridColor = RGB(250, 238, 136)
                Case COLOR_TYPE_FORECAST
                    SetGridColor = RGB(&HD7, &HDD, &H91)
                Case COLOR_TYPE_FROZEN
                    SetGridColor = RGB(&HAE, &HD4, &HFF)
                Case COLOR_TYPE_BUCKET3
                    SetGridColor = RGB(&HF1, &HDB, &HF3)
                Case COLOR_TYPE_BUCKET4
                    SetGridColor = RGB(&HFF, &HE4, &HB0)
                Case Else
                    SetGridColor = RGB(255, 255, 255)   'White
                
            End Select
            
        Case TARGET_FORECOLOR
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    SetGridColor = RGB(255, 246, 166)   'Normal
                    
                Case COLOR_TYPE_INACTIVE
                    'SetGridColor = RGB(80, 80, 80)
                    SetGridColor = RGB(191, 191, 191)
                    
                Case COLOR_TYPE_PRIMARY
                    SetGridColor = RGB(&H50, &H60, &HB8)
                    
                Case COLOR_TYPE_SECONDARY
                    SetGridColor = RGB(&H94, &H30, &HA3)
                    
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    SetGridColor = RGB(255, 230, 0)
                
                Case Else
                    'Normal
                    SetGridColor = RGB(&H0, &H0, &H0)
            
            End Select
            
        Case Else
            SetGridColor = RGB(252, 0, 255)  'Hot Pink, since this is not acceptable
    
    End Select

    If p_ColorType = COLOR_TYPE_INACTIVE Then SetGridColor = RGB(191, 191, 191)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(SetGridColor)", Err.Description
End Function

