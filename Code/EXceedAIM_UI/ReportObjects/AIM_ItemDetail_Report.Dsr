VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_ItemDetail_Report 
   Caption         =   "SSA AIM Item Detail Report"
   ClientHeight    =   11130
   ClientLeft      =   165
   ClientTop       =   435
   ClientWidth     =   15240
   Icon            =   "AIM_ItemDetail_Report.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   26882
   _ExtentY        =   19632
   SectionData     =   "AIM_ItemDetail_Report.dsx":030A
End
Attribute VB_Name = "AIM_ItemDetail_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_DataInitialize()
On Error GoTo ErrorHandler

    Me.Fields.Add "HeadKey"
    Me.Fields.Add "ItemLocation"
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_DataInitialize)"
End Sub

Private Sub ActiveReport_FetchData(eof As Boolean)
On Error GoTo ErrorHandler

    'Check for EOF
    If Me.dcItem.Recordset.eof Then
        Exit Sub
    Else
        Me.Fields("HeadKey").Value = Trim(Me.dcItem.Recordset("VnId").Value) & _
            "/" & Trim(Me.dcItem.Recordset("Assort").Value)
        Me.Fields("ItemLocation").Value = Trim(Me.dcItem.Recordset("Item").Value) & _
            "/" & Trim(Me.dcItem.Recordset("LcID").Value)
    End If

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_FetchData)"
End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page
    
    GetTranslatedCaptions_Reports Me
    
    Me.txtActDate.OutputFormat = gDateFormat
    Me.txtInActDate.OutputFormat = gDateFormat
    Me.txtVC_Date.OutputFormat = gDateFormat
    Me.txtFcstDate.OutputFormat = gDateFormat
    Me.txtUserFcstExpDate.OutputFormat = gDateFormat
    Me.txtNextPODate_1.OutputFormat = gDateFormat
    Me.txtNextPODate_2.OutputFormat = gDateFormat
    Me.txtNextPODate_3.OutputFormat = gDateFormat

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub ghHeadKey_AfterPrint()
On Error GoTo ErrorHandler

    Me.TOC.Add Me.txtVnId.Text + "/" + Me.txtAssort.Text
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ghHeadKey_AfterPrint)"
End Sub

Private Sub ghItemLocation_AfterPrint()
On Error GoTo ErrorHandler

    Me.TOC.Add Me.txtItem.Text + "/" + Me.txtLcid.Text
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ghItemLocation_AfterPrint)"
End Sub


Private Sub PageHeader_Format()
On Error GoTo ErrorHandler
    
    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(PageHeader_Format)"
End Sub
