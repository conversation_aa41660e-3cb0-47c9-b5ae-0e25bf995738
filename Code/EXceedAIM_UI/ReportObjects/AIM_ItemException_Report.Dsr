VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_ItemException_Report 
   Caption         =   "SSA DR Item Exception Report"
   ClientHeight    =   11118
   ClientLeft      =   170
   ClientTop       =   442
   ClientWidth     =   15232
   Icon            =   "AIM_ItemException_Report.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   27754
   _ExtentY        =   20258
   SectionData     =   "AIM_ItemException_Report.dsx":030A
End
Attribute VB_Name = "AIM_ItemException_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public ByPass As Boolean
Public ByPassPct As Double
Public MAPE As Boolean
Public MAPEPct As Double
Public Trend As Boolean
Public TrendPctHigh As Double
Public TrendPctLow As Double
Public Linkages As Boolean
Public DemandFilter As Boolean
Public DyingItems As Boolean
Public OnPromotion As Boolean
Public NewItems As Boolean
Public OutOfStock As Boolean
Public TrackingSignal As Boolean
Public UnOrdered As Boolean
Public UserDemand As Boolean

Private Sub ActiveReport_Initialize()

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page

End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    GetTranslatedCaptions_Reports Me
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_Format()

    Dim s As String
    
    Dim ActMAPEPct As Double
    Dim ActTrendPct As Double
    
    'Initialize exception string
    
    s = ""
    
    'Set Error Conditions
    
    If Me.dcItem.Recordset.State = adStateClosed Then Exit Sub
    
    'Calculate Actual Percentages
    
    If Me.dcItem.Recordset("FcstDemand").Value > 0 Then
        ActMAPEPct = Me.dcItem.Recordset("MAE").Value / Me.dcItem.Recordset("FcstDemand").Value
        ActTrendPct = Me.dcItem.Recordset("Trend").Value / Me.dcItem.Recordset("FcstDemand").Value
    Else
        ActMAPEPct = 0
        ActTrendPct = 0
    End If

    If DemandFilter And Me.dcItem.Recordset("DmdFilterFlag").Value = "Y" Then
        s = s + getTranslationResource("Demand Filter") + vbCrLf
    End If

    If DyingItems And Me.dcItem.Recordset("DIFlag").Value = "Y" Then
        s = s + getTranslationResource("Dying Item") + vbCrLf
    End If
    
    If MAPE And ActMAPEPct >= MAPEPct Then
        s = s + getTranslationResource("High Mean Absolute Percentage Error") + vbCrLf
    End If
    
    If Trend And (ActTrendPct >= TrendPctHigh Or ActTrendPct <= TrendPctLow) Then
        s = s + getTranslationResource("Trend Percentage Exception") + vbCrLf
    End If
    
    If ByPass And Me.dcItem.Recordset("ByPassPct").Value >= ByPassPct Then
        s = s + getTranslationResource("High Bypass Percentage") + vbCrLf
    End If
    
    If NewItems And Me.dcItem.Recordset("ItStat").Value = "N" Then
        s = s + getTranslationResource("New Item") + vbCrLf
    End If
    
    If OnPromotion And Me.dcItem.Recordset("OnPromotion").Value = "Y" Then
        s = s + getTranslationResource("On Promotion") + vbCrLf
    End If
    
    If OutOfStock And Me.dcItem.Recordset("AvailQty").Value <= 0 Then
        s = s + getTranslationResource("Out of Stock") + vbCrLf
    End If
    
    If TrackingSignal And Me.dcItem.Recordset("TrkSignalFlag").Value = "Y" Then
        s = s + getTranslationResource("Tracking Signal Exception") + vbCrLf
    End If
    
    If UnOrdered And Me.dcItem.Recordset("ItStat").Value = "U" Then
        s = s + getTranslationResource("Unordered Item") + vbCrLf
    End If
    
    If UserDemand And Me.dcItem.Recordset("UserDemandFlag").Value = "Y" Then
        s = s + getTranslationResource("User Demand Override") + vbCrLf
    End If
    
    If Linkages Then
    
        If IsNull(Me.dcItem.Recordset("LName").Value) Then
            s = s + getTranslationResource("Invalid Location Id") + vbCrLf
        End If
        
        If IsNull(Me.dcItem.Recordset("OpDesc").Value) Then
            s = s + getTranslationResource("Invalid Option Id") + vbCrLf
        End If
   
        If IsNull(Me.dcItem.Recordset("SaDesc").Value) Then
            s = s + getTranslationResource("Invalid Seasonality Profile") + vbCrLf
        End If
   
        If IsNull(Me.dcItem.Recordset("UserName").Value) Then
            s = s + getTranslationResource("Invalid Buyer Id") + vbCrLf
        End If
   
        If IsNull(Me.dcItem.Recordset("VName").Value) Then
            s = s + getTranslationResource("Invalid Vendor Id/Assortment") + vbCrLf
        End If
    
   End If
    
    'Drop any trailing cr/lf
    
    If Right(s, 1) = vbCrLf Then
        s = Mid(s, 1, Len(s) - 1)
    End If
    
    Me.txtException.Text = s

End Sub

Private Sub PageHeader_Format()
    
    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub
