VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_Options_Report 
   Caption         =   "SSA DR Options Report"
   ClientHeight    =   12631
   ClientLeft      =   187
   ClientTop       =   476
   ClientWidth     =   17272
   Icon            =   "AIM_Options_Report.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   31471
   _ExtentY        =   23015
   SectionData     =   "AIM_Options_Report.dsx":030A
End
Attribute VB_Name = "AIM_Options_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_ReportStart()

On Error GoTo ErrorHandler

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page
    
    GetTranslatedCaptions_Reports Me
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_AfterPrint()

    TOC.Add Me.txtOptionID + " - " + Me.txtOpDesc

End Sub

Private Sub PageHeader_Format()

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub
