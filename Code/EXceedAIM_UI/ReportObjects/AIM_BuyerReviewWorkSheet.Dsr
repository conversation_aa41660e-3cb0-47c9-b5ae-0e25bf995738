VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_BuyerReviewWorkSheet 
   Caption         =   "SSA DR Buyer Review Work Sheet"
   ClientHeight    =   8850
   ClientLeft      =   165
   ClientTop       =   435
   ClientWidth     =   15240
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   26882
   _ExtentY        =   15610
   SectionData     =   "AIM_BuyerReviewWorkSheet.dsx":0000
End
Attribute VB_Name = "AIM_BuyerReviewWorkSheet"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim TotWeight As Double
Dim TotCube As Double
Dim TotAmount As Double

Private Sub ActiveReport_Initialize()
On Error GoTo ErrorHandler

    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_Initialize)"
End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    GetTranslatedCaptions_Reports Me
    
    Me.txtIsDate.OutputFormat = gDateFormat
    Me.txtDuDate.OutputFormat = gDateFormat

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_Format()
On Error GoTo ErrorHandler

    'Accumulate totals
    If Me.dcPODetail.Recordset.State <> adStateClosed Then
        TotWeight = TotWeight + Me.dcPODetail.Recordset("vsoq").Value * Me.dcPODetail.Recordset("weight").Value
        TotCube = TotCube + Me.dcPODetail.Recordset("vsoq").Value * Me.dcPODetail.Recordset("cube").Value
        TotAmount = TotAmount + Me.dcPODetail.Recordset("vsoq").Value * Me.dcPODetail.Recordset("cost").Value
    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Detail_Format)"
End Sub

Private Sub gfAssort_AfterPrint()
On Error GoTo ErrorHandler

    'Clear Sub Totals
    TotWeight = 0
    TotCube = 0
    TotAmount = 0
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(gfAssort_AfterPrint)"
End Sub

Private Sub gfAssort_Format()
On Error GoTo ErrorHandler

    'Set Sub Totals
    Me.txtTotWeight.Text = Format(TotWeight, "#,##0.00")
    Me.txtTotCube.Text = Format(TotCube, "#,##0.00")
    Me.txtTotAmount = Format(TotAmount, "#,##0.00")
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(gfAssort_Format)"
End Sub

Private Sub ghAssort_AfterPrint()
On Error GoTo ErrorHandler
    
    Me.TOC.Add Me.txtById + "\" + Me.txtVnId + "\" + Me.txtAssort

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ghAssort_AfterPrint)"
End Sub

Private Sub ghById_AfterPrint()
On Error GoTo ErrorHandler

    Me.TOC.Add Me.txtById

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ghById_AfterPrint)"
End Sub

Private Sub ghVnId_AfterPrint()
On Error GoTo ErrorHandler

    Me.TOC.Add Me.txtById + "\" + Me.txtVnId

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ghVnId_AfterPrint)"
End Sub

Private Sub PageHeader_Format()
On Error GoTo ErrorHandler

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(PageHeader_Format)"
End Sub
