VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_Locations_Report 
   Caption         =   "SSA DR Locations Report"
   ClientHeight    =   11118
   ClientLeft      =   170
   ClientTop       =   442
   ClientWidth     =   15232
   Icon            =   "AIM_Locations_Report.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   27754
   _ExtentY        =   20258
   SectionData     =   "AIM_Locations_Report.dsx":030A
End
Attribute VB_Name = "AIM_Locations_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_Initialize()

    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page

End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    GetTranslatedCaptions_Reports Me
    
    txtStkDate.OutputFormat = gDateFormat
    txtScalingEffUntil.OutputFormat = gDateFormat

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_AfterPrint()

    TOC.Add Me.txtLcId.Text + " - " + Me.txtLName.Text

End Sub

Private Sub PageHeader_Format()

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub

