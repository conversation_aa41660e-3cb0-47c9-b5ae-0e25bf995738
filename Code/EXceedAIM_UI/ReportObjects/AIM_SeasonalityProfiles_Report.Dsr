VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_SeasonalityProfiles_Report 
   Caption         =   "SSA DR Seasonality Profiles Report"
   ClientHeight    =   11118
   ClientLeft      =   170
   ClientTop       =   442
   ClientWidth     =   15232
   StartUpPosition =   3  'Windows Default
   WindowState     =   2  'Maximized
   _ExtentX        =   27754
   _ExtentY        =   20258
   SectionData     =   "AIM_SeasonalityProfiles_Report.dsx":0000
End
Attribute VB_Name = "AIM_SeasonalityProfiles_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_Initialize()
On Error GoTo ErrorHandler

    Dim i As Integer
    
    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page
    
    'Set up Seasonality Profile Graph
    With Me.ctSeasons
        .IsBatched = True
        .AllowUserChanges = False
        
        'Set up Header
        .Header.Text = ""
        .Border.Type = oc2dBorderPlain
        .Border.Width = 2
        
        'Set up the X and Y Axis
        .ChartArea.Axes("X").Title.Text = ""
        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
        .ChartArea.Axes("X").NumMethod = oc2dNumPrecision
        .ChartArea.Axes("X").MajorGrid.IsStyleDefault = False
        .ChartArea.Axes("X").MajorGrid.Spacing = 4
        .ChartArea.Axes("X").Font.Size = 8
        .ChartArea.Axes("X").Max = 52
        
        .ChartArea.Axes("Y").Title.Text = getTranslationResource("Index")
        .ChartArea.Axes("Y").TitleRotation = oc2dRotate90Degrees
        .ChartArea.Axes("Y").MajorGrid.Spacing = 0.25
        .ChartArea.Axes("Y").Font.Size = 8
        .ChartArea.Axes("Y").Min = 0
        .ChartArea.Axes("Y").Max = gMaxYValue
        .ChartArea.Axes("Y").NumSpacing = 1
        
        'Set up Legends
        .Legend.Anchor = oc2dAnchorEast
        .Legend.IsShowing = True
        .Legend.Border.Type = oc2dBorderPlain
        .Legend.Border.Width = 2
        .Legend.Text = ""
        .Legend.Font.Size = 8
        
        .ChartGroups(1).SeriesLabels.RemoveAll
        .ChartGroups(1).SeriesLabels.Add (getTranslationResource("SA Profile"))
        .ChartGroups(1).SeriesLabels.Add (getTranslationResource("Base Line"))
        
        'Set up Data Series
        .ChartGroups(1).ChartType = oc2dTypePlot
        .ChartGroups(1).Data.NumSeries = 2
        .ChartGroups(1).Data.NumPoints(1) = 52
        .ChartGroups(1).Data.NumPoints(2) = 52
        
        'Set the Line Properties
        .ChartGroups(1).Styles(1).Symbol.Shape = 4
        .ChartGroups(1).Styles(1).Line.Color = vbRed
        .ChartGroups(1).Styles(1).Line.Width = 2
        
        .ChartGroups(1).Styles(2).Symbol.Shape = oc2dShapeNone
        .ChartGroups(1).Styles(2).Line.Color = vbGreen
        .ChartGroups(1).Styles(2).Line.Width = 2
        
        'Build the X Axis Values
        'Set the x axes label
        For i = 1 To 52
            If i Mod 4 = 0 Then
                Me.ctSeasons.ChartArea.Axes("X").ValueLabels.Add i, Format(i, "0")
            End If
        Next i
        
        .IsBatched = False
    End With

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_Initialize)"
End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler
    
    GetTranslatedCaptions_Reports Me
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_AfterPrint()
On Error GoTo ErrorHandler

    TOC.Add Me.txtSaDesc.Text

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Detail_AfterPrint)"
End Sub

Private Sub Detail_Format()
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim f As String
    
    If Me.dcAIMSeasons.Recordset.State <> adStateOpen Then
        Exit Sub
    End If
    
    f = "##0.00"

    'Calculate Sums
    Me.txtSum01.Text = Format((Me.dcAIMSeasons.Recordset("BI01").Value _
        + Me.dcAIMSeasons.Recordset("bi02").Value _
        + Me.dcAIMSeasons.Recordset("bi03").Value _
        + Me.dcAIMSeasons.Recordset("bi04").Value) / 4, f)
    
    Me.txtSum02.Text = Format((Me.dcAIMSeasons.Recordset("BI05").Value _
        + Me.dcAIMSeasons.Recordset("bi06").Value _
        + Me.dcAIMSeasons.Recordset("bi07").Value _
        + Me.dcAIMSeasons.Recordset("bi08").Value) / 4, f)
    
    Me.txtSum03.Text = Format((Me.dcAIMSeasons.Recordset("BI09").Value _
        + Me.dcAIMSeasons.Recordset("bi10").Value _
        + Me.dcAIMSeasons.Recordset("bi11").Value _
        + Me.dcAIMSeasons.Recordset("bi12").Value) / 4, f)
    
    Me.txtSum04.Text = Format((Me.dcAIMSeasons.Recordset("BI13").Value _
        + Me.dcAIMSeasons.Recordset("bi14").Value _
        + Me.dcAIMSeasons.Recordset("bi15").Value _
        + Me.dcAIMSeasons.Recordset("bi16").Value) / 4, f)
    
    Me.txtSum05.Text = Format((Me.dcAIMSeasons.Recordset("BI17").Value _
        + Me.dcAIMSeasons.Recordset("bi18").Value _
        + Me.dcAIMSeasons.Recordset("bi19").Value _
        + Me.dcAIMSeasons.Recordset("bi20").Value) / 4, f)
    
    Me.txtSum06.Text = Format((Me.dcAIMSeasons.Recordset("BI21").Value _
        + Me.dcAIMSeasons.Recordset("bi22").Value _
        + Me.dcAIMSeasons.Recordset("bi23").Value _
        + Me.dcAIMSeasons.Recordset("bi24").Value) / 4, f)
    
    Me.txtSum07.Text = Format((Me.dcAIMSeasons.Recordset("BI25").Value _
        + Me.dcAIMSeasons.Recordset("bi26").Value _
        + Me.dcAIMSeasons.Recordset("bi27").Value _
        + Me.dcAIMSeasons.Recordset("bi28").Value) / 4, f)
    
    Me.txtSum08.Text = Format((Me.dcAIMSeasons.Recordset("BI29").Value _
        + Me.dcAIMSeasons.Recordset("bi30").Value _
        + Me.dcAIMSeasons.Recordset("bi31").Value _
        + Me.dcAIMSeasons.Recordset("bi32").Value) / 4, f)
    
    Me.txtSum09.Text = Format((Me.dcAIMSeasons.Recordset("BI33").Value _
        + Me.dcAIMSeasons.Recordset("bi34").Value _
        + Me.dcAIMSeasons.Recordset("bi35").Value _
        + Me.dcAIMSeasons.Recordset("bi36").Value) / 4, f)
    
    Me.txtSum10.Text = Format((Me.dcAIMSeasons.Recordset("BI37").Value _
        + Me.dcAIMSeasons.Recordset("bi38").Value _
        + Me.dcAIMSeasons.Recordset("bi39").Value _
        + Me.dcAIMSeasons.Recordset("bi40").Value) / 4, f)
    
    Me.txtSum11.Text = Format((Me.dcAIMSeasons.Recordset("BI41").Value _
        + Me.dcAIMSeasons.Recordset("bi42").Value _
        + Me.dcAIMSeasons.Recordset("bi43").Value _
        + Me.dcAIMSeasons.Recordset("bi44").Value) / 4, f)
    
    Me.txtSum12.Text = Format((Me.dcAIMSeasons.Recordset("BI45").Value _
        + Me.dcAIMSeasons.Recordset("bi46").Value _
        + Me.dcAIMSeasons.Recordset("bi47").Value _
        + Me.dcAIMSeasons.Recordset("bi48").Value) / 4, f)
    
    Me.txtSum13.Text = Format((Me.dcAIMSeasons.Recordset("BI49").Value _
        + Me.dcAIMSeasons.Recordset("bi50").Value _
        + Me.dcAIMSeasons.Recordset("bi51").Value _
        + Me.dcAIMSeasons.Recordset("bi52").Value) / 4, f)
        
    'Load Data into the graph
    Me.ctSeasons.IsBatched = True
    
    For i = 1 To 52
        'Set the data value
        Me.ctSeasons.ChartGroups(1).Data.Y(1, i) = Me.dcAIMSeasons.Recordset("BI" + Format(i, "00")).Value
        Me.ctSeasons.ChartGroups(1).Data.Y(2, i) = 1
    Next i
    
    Me.ctSeasons.IsBatched = False

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Detail_Format)"
End Sub

Private Sub PageHeader_Format()
On Error GoTo ErrorHandler

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(PageHeader_Format)"
End Sub
