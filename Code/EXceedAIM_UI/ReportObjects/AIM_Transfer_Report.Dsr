VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_Transfer_Report 
   Caption         =   "SSA DR Overstock Managment Report"
   ClientHeight    =   11118
   ClientLeft      =   170
   ClientTop       =   442
   ClientWidth     =   15232
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   27754
   _ExtentY        =   20258
   SectionData     =   "AIM_Transfer_Report.dsx":0000
End
Attribute VB_Name = "AIM_Transfer_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public NbrWeeks As Integer
Public OUTL As Boolean
Public OrderPt As Boolean
Public ShortageWeeks As Integer

Dim LastItem As String

Private Sub ActiveReport_DataInitialize()

    Fields.Add "MaxQty"
    Fields.Add "OverQty"
    Fields.Add "Type"
    
End Sub

Private Sub ActiveReport_FetchData(eof As Boolean)

    Dim MaxQty As Long
    Dim OverQty As Long
    
    If eof Then Exit Sub
    
    'Initialize work variables
    
    MaxQty = 0
    OverQty = 0
    
    If Me.dcItem.Recordset("RcdId") = 1 Then
        
        Fields("Type").Value = "O"              'Overstock
        
        'Calculate the Maximum Qty
        
        If OUTL Then
            MaxQty = Me.dcItem.Recordset("OrderPt").Value + Me.dcItem.Recordset("OrderQty").Value
        End If
        
        If NbrWeeks > 0 Then
            MaxQty = MaxQty + Round(Me.dcItem.Recordset("FcstDemand").Value * NbrWeeks, 0)
        End If
        
    Else
        
        Fields("Type").Value = "S"              'Shortage
    
        'Calculate the Minimum Qty
        
        If OrderPt Then
            MaxQty = Me.dcItem.Recordset("OrderPt").Value
        End If
        
        If ShortageWeeks > 0 Then
            MaxQty = MaxQty + Round(Me.dcItem.Recordset("FcstDemand").Value * ShortageWeeks, 0)
        End If
    
    End If

    OverQty = Me.dcItem.Recordset("Oh").Value - MaxQty
    
    Fields("MaxQty").Value = MaxQty
    Fields("OverQty").Value = OverQty

End Sub


Private Sub ActiveReport_Initialize()

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page

End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    GetTranslatedCaptions_Reports Me
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_Format()

    If f_IsRecordsetValidAndOpen(Me.dcItem.Recordset) Then

        'Always print overstock records
        If f_IsRecordsetOpenAndPopulated(Me.dcItem.Recordset) _
        And Not Me.dcItem.Recordset.BOF _
        And Not Me.dcItem.Recordset.eof _
        Then
            If Me.dcItem.Recordset("RcdId").Value = 1 Then
            
                LastItem = Me.dcItem.Recordset("Item").Value
                Me.txtItem.Visible = True
                Me.txtItDesc.Visible = True
                
                Detail.BackStyle = ddBKTransparent
                    
            End If
            
            'Don't print a Requirement Record if there is no corresponding Overage Record
            
            If Me.dcItem.Recordset("RcdId").Value = 2 Then
            
                Detail.BackColor = vbCyan
                Detail.BackStyle = ddBKNormal
                
                If Me.dcItem.Recordset("Item").Value <> LastItem Then
                
                    Me.txtItem.Visible = True
                    Me.txtItDesc.Visible = True
                
                Else
                    
                    'Don't duplicate Item and Item Description
                    
                    Me.txtItem.Visible = False
                    Me.txtItDesc.Visible = False
                        
                End If
            
            End If
        End If
    End If

End Sub

Private Sub PageHeader_Format()

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub
