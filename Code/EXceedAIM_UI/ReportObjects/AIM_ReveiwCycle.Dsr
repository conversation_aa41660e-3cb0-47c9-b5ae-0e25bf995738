VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_ReviewCycle 
   Caption         =   "SSA DR Review Cycle Report"
   ClientHeight    =   12631
   ClientLeft      =   51
   ClientTop       =   272
   ClientWidth     =   17272
   Icon            =   "AIM_ReveiwCycle.dsx":0000
   WindowState     =   2  'Maximized
   _ExtentX        =   31471
   _ExtentY        =   23015
   SectionData     =   "AIM_ReveiwCycle.dsx":030A
End
Attribute VB_Name = "AIM_ReviewCycle"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_Initialize()
On Error GoTo ErrorHandler

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = False                       'Disable Table of Contents
    Me.Zoom = -2                                'Fit page

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_Initialize)"
End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler
    
    GetTranslatedCaptions_Reports Me
    
    Me.txtInitRevDate.OutputFormat = gDateFormat
    Me.txtNextDateTime.OutputFormat = gDateFormat
    Me.txtRevEndDate.OutputFormat = gDateFormat
    Me.txtRevStartDate.OutputFormat = gDateFormat
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_AfterPrint()
On Error GoTo ErrorHandler

    TOC.Add Me.txtRevCycle.Text

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Detail_AfterPrint)"
End Sub

Private Sub Detail_Format()
On Error GoTo ErrorHandler

    'Format Next Review Date
    Me.txtNextDateTime = Format(Me.dcRevCycles.Recordset("nextdatetime"), gDateFormat) _
        + " - " + Format(Me.dcRevCycles.Recordset("nextdatetime").Value, "dddd")
        
    'Determine if this is a Seasonal Review Cycle
    If Me.dcRevCycles.Recordset("seasonalreview").Value = 1 Then
        Me.Shape1.Visible = True
        Me.lblRevStartDate.Visible = True
        Me.txtRevStartDate.Visible = True
        Me.lblRevEndDate.Visible = True
        Me.txtRevEndDate.Visible = True
        Me.lblInitBuyPct.Visible = True
        Me.txtInitBuyPct.Visible = True
        Me.lblInitRevDate.Visible = True
        Me.txtInitRevDate.Visible = True
        Me.lblSeasonalReview.Visible = True
        Me.ckSeasonalReview.Visible = True
    Else
        Me.Shape1.Visible = False
        Me.lblRevStartDate.Visible = False
        Me.txtRevStartDate.Visible = False
        Me.lblRevEndDate.Visible = False
        Me.txtRevEndDate.Visible = False
        Me.lblInitBuyPct.Visible = False
        Me.txtInitBuyPct.Visible = False
        Me.lblInitRevDate.Visible = False
        Me.txtInitRevDate.Visible = False
        Me.lblSeasonalReview.Visible = False
        Me.ckSeasonalReview.Visible = False
    End If
        
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Detail_Format)"
End Sub

Private Sub PageHeader_Format()
On Error GoTo ErrorHandler

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(PageHeader_Format)"
End Sub
