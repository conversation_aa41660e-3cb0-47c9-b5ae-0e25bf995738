VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_StockStatusReport 
   Caption         =   "SSA DR Stock Status Report"
   ClientHeight    =   12631
   ClientLeft      =   187
   ClientTop       =   476
   ClientWidth     =   17272
   Icon            =   "AIM_StockStatusReport.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   31471
   _ExtentY        =   23015
   SectionData     =   "AIM_StockStatusReport.dsx":030A
End
Attribute VB_Name = "AIM_StockStatusReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_Initialize()

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page

End Sub

Private Sub ActiveReport_PageStart()
On Error GoTo ErrorHandler

    GetTranslatedCaptions_Reports Me
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_Format()

    Me.txtNetRqmts = Format(CDbl(Me.txtFcstLT.Text) - CDbl(Me.txtAvailQty.Text), "0")
    
End Sub


Private Sub ghAssort_AfterPrint()

    Me.TOC.Add Me.txtById + "\" + Me.txtVnId + "\" + Me.txtAssort

End Sub

Private Sub ghById_AfterPrint()

    Me.TOC.Add Me.txtById

End Sub

Private Sub ghVnId_AfterPrint()

    Me.TOC.Add Me.txtById + "\" + Me.txtVnId

End Sub

Private Sub PageHeader_Format()

    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub
