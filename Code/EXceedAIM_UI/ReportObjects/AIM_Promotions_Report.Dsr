VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_Promotions_Report 
   Caption         =   "SSA DR Promotions Report"
   ClientHeight    =   12631
   ClientLeft      =   187
   ClientTop       =   476
   ClientWidth     =   17272
   Icon            =   "AIM_Promotions_Report.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   31471
   _ExtentY        =   23015
   SectionData     =   "AIM_Promotions_Report.dsx":030A
End
Attribute VB_Name = "AIM_Promotions_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_Initialize()

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page

End Sub

Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    GetTranslatedCaptions_Reports Me
    
    Me.txtPMStartDate.OutputFormat = gDateFormat
    Me.txtPMEndDate.OutputFormat = gDateFormat

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub Detail_AfterPrint()

    Me.TOC.Add Me.txtPmId.Text + " - " + Me.txtPmDesc.Text
    
End Sub

Private Sub Detail_Format()

    Dim f As String
    
    If Me.dcAIMPromotions.Recordset.State <> adStateOpen Then
        Exit Sub
    End If
    
    f = "##0.00"

    'Calculate Sums
    
    Me.txtSum01.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj01").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj02").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj03").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj04").Value) / 4, f)
    
    Me.txtSum02.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj05").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj06").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj07").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj08").Value) / 4, f)
    
    Me.txtSum03.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj09").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj10").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj11").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj12").Value) / 4, f)
    
    Me.txtSum04.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj13").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj14").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj15").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj16").Value) / 4, f)
    
    Me.txtSum05.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj17").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj18").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj19").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj20").Value) / 4, f)
    
    Me.txtSum06.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj21").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj22").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj23").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj24").Value) / 4, f)
    
    Me.txtSum07.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj25").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj26").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj27").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj28").Value) / 4, f)
    
    Me.txtSum08.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj29").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj30").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj31").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj32").Value) / 4, f)
    
    Me.txtSum09.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj33").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj34").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj35").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj36").Value) / 4, f)
    
    Me.txtSum10.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj37").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj38").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj39").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj40").Value) / 4, f)
    
    Me.txtSum11.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj41").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj42").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj43").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj44").Value) / 4, f)
    
    Me.txtSum12.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj45").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj46").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj47").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj48").Value) / 4, f)
    
    Me.txtSum13.Text = Format((Me.dcAIMPromotions.Recordset("PMAdj49").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj50").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj51").Value _
        + Me.dcAIMPromotions.Recordset("PMAdj52").Value) / 4, f)
        

End Sub


Private Sub PageHeader_Format()
    
    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub
