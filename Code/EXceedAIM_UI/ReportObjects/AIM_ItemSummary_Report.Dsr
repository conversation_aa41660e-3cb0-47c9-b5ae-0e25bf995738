VERSION 5.00
Begin {9EB8768B-CDFA-44DF-8F3E-857A8405E1DB} AIM_ItemSummary_Report 
   Caption         =   "SSA DR Item Summary Report"
   ClientHeight    =   11130
   ClientLeft      =   165
   ClientTop       =   435
   ClientWidth     =   15240
   Icon            =   "AIM_ItemSummary_Report.dsx":0000
   StartUpPosition =   3  'Windows Default
   _ExtentX        =   26882
   _ExtentY        =   19632
   SectionData     =   "AIM_ItemSummary_Report.dsx":030A
End
Attribute VB_Name = "AIM_ItemSummary_Report"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub ActiveReport_DataInitialize()

    Me.Fields.Add "HeadKey"
    
End Sub

Private Sub ActiveReport_FetchData(eof As Boolean)

    'Check for EOF
    
    If Me.dcItem.Recordset.eof Then
        Exit Sub
    Else
        Me.Fields("HeadKey").Value = Trim(Me.dcItem.Recordset("VnId").Value) + "/" + Trim(Me.dcItem.Recordset("Assort").Value)
    End If

End Sub


Private Sub ActiveReport_ReportStart()
On Error GoTo ErrorHandler

    Me.Printer.Orientation = ddOLandscape       'Print Landscape
    Me.TOCEnabled = True                        'Enable Table of Contents
    Me.Zoom = -2                                'Fit to page
    
    GetTranslatedCaptions_Reports Me
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(ActiveReport_ReportStart)"
End Sub

Private Sub ghHeadKey_AfterPrint()

    Me.TOC.Add Me.txtVnId.Text + "/" + Me.txtAssort.Text
    
End Sub

Private Sub PageHeader_Format()
    
    Me.txtPageNbr.Text = Me.pageNumber
    Me.txtDate.Text = Date

End Sub
