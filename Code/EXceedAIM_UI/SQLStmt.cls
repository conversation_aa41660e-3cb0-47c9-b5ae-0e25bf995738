VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "SQLBuilder"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'************************************************************
' Used primarily with AIM_SelectionCriteria, and its calling/dependent modules
'************************************************************
Option Explicit

'Public variables, set from within and/or used by calling module
Public FromExpr As String
Public JoinExpr As String
Public SelExpr As String
Public OrderExpr As String

'Modular objects
Private mTables As clsTables
Private mCriterias As Criterias

Public Property Get Criterias() As Criterias
On Error GoTo ErrorHandler
    
    'Fetch collection
    Set Criterias = mCriterias

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "Criterias[PropertyGet]"
End Property

Public Property Get SQLTables() As clsTables
On Error GoTo ErrorHandler
    
    'Fetch collection
    Set SQLTables = mTables

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "SQLTables[PropertyGet]"
End Property

Public Property Get SQLExpr() As String
On Error GoTo ErrorHandler

    Dim tempString As String
    
    'Concatenate SQL statement from various elements
    SQLExpr = Trim(SelExpr)
    
    tempString = Trim(FromExpr)
    SQLExpr = IIf(tempString = "", SQLExpr, SQLExpr + vbCrLf + tempString)
    tempString = ""
    
    tempString = Trim(JoinExpr)
    SQLExpr = IIf(tempString = "", SQLExpr, SQLExpr + vbCrLf + tempString)
    tempString = ""
    
    tempString = Trim(BuildWhrExpr)
    SQLExpr = IIf(tempString = "", SQLExpr, SQLExpr + vbCrLf + tempString)
    tempString = ""
    
    tempString = Trim(OrderExpr)
    SQLExpr = IIf(tempString = "", SQLExpr, SQLExpr + vbCrLf + tempString)
    tempString = ""
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "SQLExpr[PropertyGet]"
End Property

Public Property Get WhrExpr() As String
On Error GoTo ErrorHandler

   'Concatenate Where clause from criterias collection
   WhrExpr = BuildWhrExpr
   
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "WhrExpr[PropertyGet]"
End Property

'**********************************************************************
' Function Name:    BuildWhrExpr()
'
' Description:      Builds a SQL Where Clause using the criteia in the
'                   Criterias Object.
'
' Returns:          BuildWhrExp The string where clause.
'
' By: RES           Date: 4/15/2000
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1996 - 2000
'**********************************************************************
Private Function BuildWhrExpr() As String
On Error GoTo ErrorHandler

    Dim xCriteria As Criteria
    
    Dim i As Integer
    Dim N As Integer
    Dim X As Integer
    Dim ParseLine As String
    Dim RtnCode As Integer
    Dim VArray As Variant
    Dim strWhrExpr As String
    Dim NotOperator As String
    
    'Initialize the Where Expression
    strWhrExpr = ""
    'Loop through collection and process each criterion
    For Each xCriteria In Criterias
        If Trim(strWhrExpr) <> "" Then strWhrExpr = strWhrExpr + vbCrLf
        
        If xCriteria.NotOpt Then
            NotOperator = ""
            Select Case xCriteria.CompOper
            Case "="
                NotOperator = "!"
            Case "<"
                xCriteria.NotOpt = False
                xCriteria.CompOper = ">="
            Case "<="
                xCriteria.NotOpt = False
                xCriteria.CompOper = ">"
            Case ">"
                xCriteria.NotOpt = False
                xCriteria.CompOper = "<="
            Case ">="
                xCriteria.NotOpt = False
                xCriteria.CompOper = "<"
            Case Else
                NotOperator = "NOT"
            End Select
        Else
            NotOperator = ""
        End If
        'Fetch criterion elements
        If Trim(strWhrExpr) = "" _
        And StrComp(Trim(xCriteria.Qualifier), "WHERE", vbTextCompare) < 0 Then
            strWhrExpr = "WHERE "
        Else
            strWhrExpr = strWhrExpr + xCriteria.Qualifier + " "
        End If
        strWhrExpr = strWhrExpr + xCriteria.ColName + " "
        strWhrExpr = strWhrExpr + NotOperator + " "
        strWhrExpr = strWhrExpr + xCriteria.CompOper + " "
    
        'Get rid of any embedded quote characters in the comparison value
        ParseLine = Replace(Trim(xCriteria.CompValue1), "'", "")
        
        'Separate multiple comparison values, if any
        VArray = Split(ParseLine, ",")
        
        'Check for no data
        X = UBound(VArray)
        If X < 0 Then
            BuildWhrExpr = ""
            Exit Function
        End If
    
        'Apply comparison operator
        'IMPORTANT: Ensure the Unicode constant (N) is used before each comparison value
        Select Case UCase(xCriteria.CompOper)
        Case "BETWEEN"
            If Not xCriteria.Numeric Then
                strWhrExpr = strWhrExpr + "N'" + Trim(xCriteria.CompValue1) _
                    + "' AND N'" + Trim(xCriteria.CompValue2) + "' "
            Else
                strWhrExpr = strWhrExpr + Trim(xCriteria.CompValue1) _
                    + " AND " + Trim(xCriteria.CompValue2) + " "
            End If
        
        Case "IN"
            strWhrExpr = strWhrExpr & "("
            
            If Not xCriteria.Numeric _
            And X = 0 Then
                strWhrExpr = strWhrExpr + "N'" + Trim(VArray(0)) + "') "
            ElseIf xCriteria.Numeric _
            And X = 0 Then
                strWhrExpr = strWhrExpr + Trim(VArray(0)) + ") "
            ElseIf Not xCriteria.Numeric _
            And X > 0 Then
                For N = LBound(VArray) To (X - 1)
                    strWhrExpr = strWhrExpr + "N'" + Trim(VArray(N)) + "', "
                Next N
                strWhrExpr = strWhrExpr + "N'" + Trim(VArray(X)) + "') "
            ElseIf xCriteria.Numeric _
            And X > 0 Then
                For N = LBound(VArray) To (X - 1)
                    strWhrExpr = strWhrExpr + Trim(VArray(N)) + ", "
                Next N
                strWhrExpr = strWhrExpr + Trim(VArray(X)) + ") "
            End If
    
        Case "LIKE"
            strWhrExpr = strWhrExpr & "N'" + Trim(xCriteria.CompValue1) + "%' "
        
        Case Else
            If Not xCriteria.Numeric Then
                strWhrExpr = strWhrExpr + "N'" + Trim(xCriteria.CompValue1) + "' "
            Else
                strWhrExpr = strWhrExpr + Trim(xCriteria.CompValue1) + " "
            End If
        
        End Select
    Next xCriteria

    'Return concatenated where clause
    BuildWhrExpr = strWhrExpr

    If InStr(1, strWhrExpr, "WHERE", vbTextCompare) = 0 Then
        If Trim(strWhrExpr) <> "" Then
            If StrComp(Mid(strWhrExpr, 1, 3), "AND", vbTextCompare) = 0 Then
                strWhrExpr = "WHERE " & Mid(strWhrExpr, 3)
            End If
            
        End If
    End If
    
    'Clean up
    Set xCriteria = Nothing
    
Exit Function
ErrorHandler:
    Set xCriteria = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "BuildWhrExpr"
End Function

Private Sub Class_Initialize()
On Error GoTo ErrorHandler

    'Instantiate modular objects
    Set mTables = New clsTables
    Set mCriterias = New Criterias

Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "SQLStmt.Class_Initialize"
End Sub

Private Sub Class_Terminate()
On Error GoTo ErrorHandler

    'Destroy modular objects
    Set mTables = Nothing
    Set mCriterias = Nothing
    
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "SQLStmt.Class_Terminate"
End Sub
