VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "AIMAdvForecast"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Attribute VB_Ext_KEY = "SavedWithClassBuilder6" ,"Yes"
Attribute VB_Ext_KEY = "Member0" ,"AIMCalendar"
Attribute VB_Ext_KEY = "Top_Level" ,"Yes"
Option Explicit


'Availability Public Variables
Public AvailQty As Long                     'Available Quantity
Public AdjAvailQty As Long                  'Adjusted Available Quantity
Public Oh As Long                           'On-hand Quantity
Public Oo As Long                           'On-Order Quantity
Public ComStk As Long                       'Committed Stock Quantity
Public BkOrder As Long                      'Backordered Quantity
Public BkComStk As Long                     'Backordered Committed Stock

'Forecasting Public Variables
Public AdjustPct As Double                  'Adjustment Percentage
Public ActDate As Date                      'Osama's field.
Public ApplySeasonsIndex As Boolean         'Apply Seasonality to Forecast
Public ApplyTrend As Integer                'Apply Trend to Forecast
''    '0 => No Trend
''    '1 => Apply Trend per item based on Item PlnTT
''    '2 => Apply Trend to all items
Public BaseDate As Date                     'The Base Date is the start date of the first forecast period
                                            'This value is used to initialize Trend -- Trend Starts at the first date
Public CarryFwdRoundingOption As Boolean    'Apply Carry Forward Rounding to Forecast?
Public Cost As Double                       'Cost per Unit
Public Cube As Double                       'Cube per Unit
Public Price As Double                      'Price Per Unit
Public DmdScalingFactor As Double           'Demand Scaling Factor
Public FcstDemand As Double                 'Base Forecast Demand

Public FcstStartDate As Date                'Forecast Start Date
Public FcstEndDate As Date                  'Forecast End Date

Public FcstUnit As AIM_FORECASTUNITS        'Forecast Unit
Public HiTrndDemandLimit As Double          'High Trend Demand Limit -- Constrains the
Public InActDate As Date                    'Inactive Date

Public Item As String                       'Item Number
Public ItStat As String                     'Item Status
Public LcId As String                       'Location Id
Public PlnTT As String                      'Planning Trend Type (Y = Apply Trend, N = No Trend)

Public PmEndDate As Date                    'Promotions End Date
Public PmStartDate As Date                  'Promotions Start Date
Public PmStatus As Boolean                  'Promotions Status

Public ScalingEffUntil As Date              'Scaling Effective Until
Public StkDate As Date                      'Stock Date
''    'The Stock Date is used as the starting point for net requirements
''    'calculations in the Order Policy Module
Public Trend As Double                      'Trend -- in units per week
Public MethodTrend As Boolean               'Trend from the Method this should be used if applytrend is 1
Public UserFcst As Double                   'User Forecast
Public UserFcstExpDate  As Date             'User Foecast Expiration Date
Public Weight As Double                     'Weight per Unit

Public AIMAdvCalendar As AIMAdvCalendar     'AIM Calendar

'Order Policy Variables
'Osama 10/08/2002 Buyer Strategy for the Net Req-Start
Public BuyStrat As String
Public UserMin As Integer
Public UserMax As Long
Public DIFlag As String
Public DIMADP As Double
'Osama 10/08/2002 Buyer Strategy for the Net Req-End

Public Accum_Lt As Integer                  'Accumulative Lead Time
Public Fcst_Lt As Double                    'Fcst lead time
Public CStock As Long                       'Counter Stock
Public Dft_TurnHigh As Double               'Default High Turn Goal
Public Dft_TurnLow As Double                'Default Low Turn Goal
Public DSer As Double                       'Desired Service Level
Public HiMADP As Double                     'High MAD Percentage
Public Int_Enabled As String                'Intermittent Processing Enabled (Y/N)
Public Int_LookBackPds As Integer           'Intermittent Demand Look Back Periods
Public IntSafetyStock As Double             'Intermittent Safety Stock
Public IsIntermittent As Boolean            'Is this an Intermittent Item ?
Public KFactor As Double                    'Inventory Carrying Cost
Public AllowNegativeAvailQty As String      'Is negative avail qty allowable
Public LoMADP As Double                     'Low MAD Percentage
Public LtvFact As Double                    'Lead Time Variability Factor
Public MADExK As Double                     'MAD Extrapolation Constant
Public MAE As Double                        'Mean Absolute Error
Public Mean_NZ As Double                    'Intermittent Mean
Public ReplenCost As Double                 'Replenishment Cost (Location Wide)
Public ReplenCost2 As Double                'Replenishment Cost (Item Specific)
Public StdDev_NZ As Double                  'Intermittent Standard Deviation
Public SSAdj As Double                      'Safety Stock Adjustment
Public ZSStock As String                    'Zero Safety Stock Option
Public DropShip_XDock As String             'DropShip location Yes or No

'Packrounding variables
Public UOM As String                        'Unit of Measure (Forecasting)
Public ConvFactor As Double                 'UOM to Buying UOM Conversion Factor
Public BuyingUOM As String                  'Buying UOM
Public PackRounding As String               'Packrounding Rule
Public IMin As Long                         'Minimum Order Quantity
Public IMax As Long                         'Maximum Order Quantity

'Review Cycle Variables
Public NextDateTime As Date                 'Next Scheduled Review
Public RevFreq As Integer                   'Review Frequency
Public RevInterval As Integer               'Review Interval
Public RevSunday As Boolean                 'Review on Sundays
Public RevMonday As Boolean                 'Review on Mondays
Public RevTuesday As Boolean                'Review on Tuesdays
Public RevWednesday As Boolean              'Review on Wednesdays
Public RevThursday As Boolean               'Review on Thursdays
Public RevFriday As Boolean                 'Review on Friday
Public RevSaturday As Boolean               'Review on Saturday
Public WeekQualifier As Integer             'Week Qualifier (Used for Week of Month Review Cycle)
Public RevStartDate As Date                 'Review Start Date
Public RevEndDate As Date                   'Review End Date
Public InitBuyPct As Double                 'Initial Buying Percentage (Seasonal Buys)
Public InitRevDate As Date                  'Initial Review Date
Public ReviewTime As Integer                'Review Time in Days
Public ZOPSw As String
'Generate Net Requirements Flag
Public NetRqmtsOpt As Boolean
'Public Modification As Boolean
Public DataYes As Boolean 'Used to check whether data is present in the structure or not
Public PopulateModificationRcdQtyCalled  As Boolean ' Used to see if the PopulateModificationRcdQty
Public MasterDataYes As Boolean 'Used to check whether data is present in the structure or not for master
Public AdjLoopEntered As Boolean
Public MasterAdjLoopEntered As Boolean
'Public EnteredLoop As Boolean
'Public MasterEnteredLoop As Boolean



Public OrgStartDate As Date
Public NewFcstInterval As AIM_INTERVALS
Public OrgNbrPds As Integer

Public FcstKey As Long
'Public ModificationChangeYN As Boolean

'Private variables
Private m_Adj(1 To 106) As Double           'Adjustments to the Forecast (Promotions, Adjustments, Events, Etc.)
Private m_bi(1 To 52) As Double             'Seasonality Indices
Private m_Fcst(1 To 106) As Double          'Forecast Results by Period
Private m_AdjFcst(1 To 106) As Double          'Forecast Results by Period
Private m_MasterAdjFcst(1 To 106) As Double    'Master adj Forecast Results by Period
Private m_InitialStock As Double            'Stores the Inital Stock for the Item
Private m_Interval As AIM_INTERVALS         'Forecast Interval
Private m_NbrReviewDays As Integer          'Number of Review Days
Private m_NbrPeriods As Integer             'Number of Forecast Periods
Private m_NetRqmts(1 To 106) As Double        'Net Requirments for each period
Private m_AdjNetRqmts(1 To 106) As Double     'Adj Net Requirments for each period
Private m_PMAdj(1 To 52) As Double          'Promotional Adjustments
Private m_PdDate(1 To 106) As Date          'Forecast Period Start Dates
Private m_PlannedRcpts(1 To 106) As Double    'Planned Purchase Order Receipts
Private m_AdjPlannedRcpts(1 To 106) As Double    'Planned Purchase Order Receipts
Private m_ReviewDays() As Date              'Review Days

'Some forecast variables
Private m_TotalFcst As Double               'Total Forecast for Forecast Period
Private m_PriceAdj(1 To 106) As Double      'Total Price per period
Private m_PriceUnit(1 To 106) As Double     'Unit price per period
Private m_TotalPrice As Double              'Total Price  for Forecast Period
Private m_CostAdj(1 To 106) As Double       'Total Cost per period
Private m_CostUnit(1 To 106) As Double      'Unit Cost per period
Private m_TotalCost As Double               'Total Cost  for Forecast Period
Private m_TotalCostAdj As Double
Private m_TotalFcstAdj As Double
Private m_TotalPriceAdj As Double




'Eqv. Net Requirements variables
Private m_xTotalFcst As Double              'Total Net Rqmts Forecast for Forecast Period
Private m_xAdjTotalFcst As Double           'Total Adj Net Rqmts Forecast for Forecast Period
Private m_xPriceAdj(1 To 106) As Double     'Total Price per period for NetRequirments Forecast
Private m_xAdjPriceAdj(1 To 106) As Double
Private m_xPriceUnit(1 To 106) As Double    'Unit price per period
Private m_xTotalPrice As Double             'Total Price for NetRequirements Forecast
Private m_xAdjTotalPrice As Double          'Total Price for NetRequitements using Adj Forecast
Private m_xCostAdj(1 To 106) As Double      'Total Cost per period for NetRequirments Forecast
Private m_xAdjCostAdj(1 To 106) As Double
Private m_xCostUnit(1 To 106) As Double     'Unit Cost per period
Private m_xTotalCost As Double              'Total Cost for NetRequirements Forecast
Private m_xAdjTotalCost As Double

'Order Policy Output Variables
Private m_FcstLT As Double                  'Forecast for the Lead Time
Private m_FcstRT As Double                  'Forecast for the Review Time
Private m_AdjFcstLT As Double               'Forecast for the Lead Time Based on Adjustments
Private m_AdjFcstRT As Double               'Forecast for the Review Time Based on Adjustments
Private m_Fcst_Month As Double              'Forecast for tne Next Month
Private m_Fcst_Quarter As Double            'Forecast for the Next Quarter
Private m_Fcst_Year As Double               'Forecast for the Next Year
Private m_OrderPt As Long                   'Order Point
Private m_AdjOrderPt As Long
Private m_OrderQty As Long                  'Order Quantity
Private m_AdjOrderQty As Long
Private m_SafetyStock As Long               'Safety Stock
Private m_AdjSafetyStock As Long            'Safety Stock based on Adjustments
Private m_Burn As Long                      'Burn -- Inventory consumed between the Stock Date and Start of the Forecast
Private m_EstOnOrder As Long                'Estimated Open Orders -- Inventory consumed between the Stock Date and Start of the Forecast
Private m_AdjEstOnOrder As Long             'Estimated Open Orders -- Inventory consumed between the Stock Date adn Start of the
                                            'Forcast usind Adj Forecast values
                                            

'Generate Orders Variables
Private m_BkQty(1 To 10) As Long            'Price Break Quantities
Private m_BkCost(1 To 10) As Double         'Price Breaks

Private Type MODIFICATION_RCD
    Fcst As Double
    QtyAdj As Double
    FcstNetReq As Double
    FcstAdjNetReq As Double
    QtyAdjOverRide As Double
    QtyAdjPct As Integer
    AdjOverRide As Integer      'values 0 no override,1 override ,2 override master
    MasterQtyAdj As Double
    MasterQtyAdjOverRide As Double
    MasterQtyAdjPct As Integer
    MasterAdjOverRide As Integer
    StartDate As Date
    EndDate As Date
End Type
Private Type Adj_RCD
    QtyAdj As Double
    QtyAdjPct As Integer
    AdjOverRide As Boolean
End Type

Dim AdjRcd As Adj_RCD

Private ModificationRec() As MODIFICATION_RCD
Private MasterModificationRec() As MODIFICATION_RCD
'Private AIMYears() As AIMYEARS_RCD
'Private AIMDays() As AIMDAYS_RCD
Private NetReqAdj As Double
Private ModNetReq() As Double
Private FyYear As Integer

Private ThisFYPrice As Double
Private LastFYPrice As Double
Private NextFYPrice As Double

Private ThisFYCost As Double
Private LastFYCost As Double
Private NextFYCost As Double

Private Cn As ADODB.Connection
Public AdjStartDate As Date
Public AdjEndDate As Date
Private AdjOverRide As Boolean
Private MasterAdjOverRide As Boolean
Private AIM_Repository_Get_Sp As ADODB.Command
Private AIM_ForecastMaster_Get_Sp As ADODB.Command
Private rsModification As ADODB.Recordset
Private rsMasterModification As ADODB.Recordset
Private AIM_CostPriceListPrice_Get_Sp As New ADODB.Command
Private prm As ADODB.Parameter
'Public DPRollingFcstStartDate As Date 'this is the date where the future pd start
Private DPRollingFcstStartDate  As Date 'replaces the FuturePdStartDate
Private DpHistoricalStartDate As Date ' Start of the historical startdate
Private DpFutureEndDate As Date 'End of futuredate

Private FcstPdDates()  As Date 'Used to store the date between which fcst is to be made
Public NbrPds_Hist As Integer
Public NbrPds_Future As Integer
Private NewTotalPeriods As Integer ' Total Periods of forecast
Public FcstRolling As Boolean 'If the forecast is rolling fcst or not
Public FixedPds As Integer
                                'befer this date all the data is historical data
'*****************************************************************************
' CLASS INITIALIZE/TERMINATE
'*****************************************************************************
Private Sub Class_Initialize()
''
'On Error GoTo ErrorHandler
'
'    Dim i As Integer
'
'    'Set the default Seasonality Indices
'    For i = LBound(m_bi) To UBound(m_bi)
'        m_bi(i) = 1
'        m_PMAdj(i) = 1
'    Next i
'
'    For i = LBound(m_Fcst) To UBound(m_Fcst)
'        m_Adj(i) = 0
'        m_Fcst(i) = 0
'        m_PdDate(i) = "01/01/1990"
'        m_PlannedRcpts(i) = 0
'        m_AdjPlannedRcpts(i) = 0
'        m_NetRqmts(i) = 0
'        m_AdjNetRqmts(i) = 0
'        m_PriceAdj(i) = 0
'        m_CostAdj(i) = 0
'        m_xPriceAdj(i) = 0
'        m_xAdjPriceAdj(i) = 0
'        m_xCostAdj(i) = 0
'        m_xAdjCostAdj(i) = 0
'    Next i
'
'    m_NbrReviewDays = 0
'    m_NbrPeriods = 12
'
'    'Initialize Totals
'    m_TotalFcst = 0
'    m_xTotalFcst = 0
'    m_xAdjTotalFcst = 0
'    m_TotalPrice = 0
'    m_TotalCost = 0
'    m_xTotalPrice = 0
'    m_xAdjTotalPrice = 0
'    m_xTotalCost = 0
'    m_xAdjTotalCost = 0
'    'Initialize Available Inventory
'    AvailQty = 0
'    AdjAvailQty = 0
'    Oh = 0
'    Oo = 0
'    ComStk = 0
'    BkOrder = 0
'    BkComStk = 0
'
'    'Initialize Public Variables to Default Values
'    AdjustPct = 0
'    ApplySeasonsIndex = False
'    ApplyTrend = 0
'    BaseDate = Date
'    CarryFwdRoundingOption = True
'
'    Cost = 0
'    Cube = 0
'    DmdScalingFactor = 1
'    FcstDemand = 0
'    FcstEndDate = "12/31/9999"
'
'    FcstStartDate = Date
'    FcstUnit = 0
'    HiTrndDemandLimit = 1
'    InActDate = "12/31/9999"
'    Item = "Item Number"
'
'    ItStat = "A"
'    LcId = "Location Id"
'    PlnTT = "Y"
'    PmStatus = False
'    PmStartDate = "01/01/1990"
'
'    PmEndDate = "01/01/1990"
'    ScalingEffUntil = "1/1/1990"
'    Trend = 0
'    UserFcst = 0
'    UserFcstExpDate = "1/1/1990"
'
'    Weight = 0
'
'    'Initialize Order Policy Calculated Values to their default values
'    m_Burn = 0
'
'    m_FcstLT = 0
'    m_FcstRT = 0
'    m_Fcst_Month = 0
'    m_Fcst_Quarter = 0
'
'    m_Fcst_Year = 0
'    m_OrderPt = 0
'    m_AdjOrderPt = 0
'    m_OrderQty = 0
'    m_AdjOrderQty = 0
'    'm_AdjSafetyStock = 0
'    m_SafetyStock = 0
'
'    'Initialize Order Policy Variables
'    Accum_Lt = 1
'    CStock = 0
'    Dft_TurnHigh = 999.9
'    Dft_TurnLow = 0
'    DSer = 0.95
'    HiMADP = 9
'    Int_Enabled = "N"
'    IntSafetyStock = 0
'    Int_LookBackPds = 52
'    IsIntermittent = False
'    LoMADP = 0.1
'    KFactor = 30
'    LtvFact = 0
'    MADExK = 0.5
'    MAE = 0
'    Mean_NZ = 0
'    ReplenCost = 5
'    ReplenCost2 = 0
'    ReviewTime = 1
'    StdDev_NZ = 0
'    StkDate = Date
'    ZSStock = "N"
'
'    For i = LBound(m_BkQty) To UBound(m_BkQty)
'        m_BkQty(i) = 0
'        m_BkCost(i) = 0
'    Next i
'
'    'Initialize Review Cycle Variables
'    RevFreq = 0
'    RevInterval = 1
'    RevSunday = False
'    RevMonday = True
'    RevTuesday = True
'    RevWednesday = True
'    RevThursday = True
'    RevFriday = True
'    RevSaturday = True
'    WeekQualifier = 0
'    RevStartDate = "01/01/1990"
'    RevEndDate = "12/31/9999"
'    InitBuyPct = 1
'    InitRevDate = "01/01/1990"
'    ReviewTime = 1
'
'    'Initialize Net Requirements Flag
'    NetRqmtsOpt = False
'
'Exit Sub
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.ClassInitialize)"
End Sub

Private Sub Class_Terminate()
On Error GoTo ErrorHandler
If Not (AIM_Repository_Get_Sp Is Nothing) Then Set AIM_Repository_Get_Sp.ActiveConnection = Nothing
Set AIM_Repository_Get_Sp = Nothing
If f_IsRecordsetValidAndOpen(rsModification) Then rsModification.Close
Set rsModification = Nothing

If Not (AIM_ForecastMaster_Get_Sp Is Nothing) Then Set AIM_ForecastMaster_Get_Sp.ActiveConnection = Nothing
Set AIM_ForecastMaster_Get_Sp = Nothing
If f_IsRecordsetValidAndOpen(rsMasterModification) Then rsMasterModification.Close
Set rsMasterModification = Nothing
If Not (AIM_CostPriceListPrice_Get_Sp Is Nothing) Then Set AIM_CostPriceListPrice_Get_Sp.ActiveConnection = Nothing
Set AIM_CostPriceListPrice_Get_Sp = Nothing
Set prm = Nothing

'Disconnect database connection
    SQLConnection Cn, CONNECTION_CLOSE, False, , , "AIMAdvForecast.cls"
    Set Cn = Nothing
'    Set AIMAdvCalendar to nothing?

Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "AIMAdvForecast.Class_Terminate"
End Sub

'*****************************************************************************
' PUBLIC PROPERTIES
'*****************************************************************************
Public Property Get Adj( _
    ByVal Pd As Integer _
) As Double
On Error GoTo ErrorHandler

    Adj = m_Adj(Pd)

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Adj (Get))"
End Property

Public Property Get Bi( _
    ByVal Pd As Integer _
) As Double
On Error GoTo ErrorHandler

    Bi = m_bi(Pd)

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.bi (Get))"
End Property

Public Property Let Bi( _
    ByVal Pd As Integer, _
    ByVal SeasonalIndex As Double _
)
On Error GoTo ErrorHandler

    m_bi(Pd) = SeasonalIndex

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.bi (Let))"
End Property

Public Property Let BkCost( _
    ByVal Cnt As Integer, _
    ByVal BkCost As Double _
)
On Error GoTo ErrorHandler

    m_BkCost(Cnt) = BkCost

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.BkCost (let))"
End Property

Public Property Let BkQty( _
    ByVal Cnt As Integer, _
    ByVal BkQty As Long _
)
On Error GoTo ErrorHandler

    m_BkQty(Cnt) = BkQty

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.BkQty Let))"
End Property
    
Public Property Get Burn() As Long
On Error GoTo ErrorHandler

    Burn = m_Burn
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Burn  (Get))"
End Property

Public Property Get Fcst( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler
    'Set return code to failure
    Fcst = -1
    Select Case FcstUnit
        Case FcstUnits
            Fcst = m_Fcst(Pd)
        Case FcstCost
            'Fcst = m_Fcst(Pd) * Cost
            Fcst = m_Fcst(Pd) * m_CostAdj(Pd)
        Case FcstCube
            Fcst = m_Fcst(Pd) * Cube
        Case FcstWeight
            Fcst = m_Fcst(Pd) * Weight
        Case FcstPrice
           'Fcst = m_priceAdj(Pd)
           Fcst = m_Fcst(Pd) * m_PriceAdj(Pd)
        Case Else
            Fcst = m_Fcst(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Fcst (Get))"
End Property

Public Property Get InitalStock() As Double
On Error GoTo ErrorHandler
InitalStock = m_InitialStock
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.InitalStock (Get))"
End Property
Public Property Get MasterAdjFcst( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler

    'Set return code to failure
    MasterAdjFcst = -1
    
    Select Case FcstUnit
        Case FcstUnits
            MasterAdjFcst = m_MasterAdjFcst(Pd)
        Case FcstCost
            'Fcst = m_Fcst(Pd) * Cost
            MasterAdjFcst = m_MasterAdjFcst(Pd) * m_CostAdj(Pd)
        Case FcstCube
            MasterAdjFcst = m_MasterAdjFcst(Pd) * Cube
        Case FcstWeight
            MasterAdjFcst = m_MasterAdjFcst(Pd) * Weight
        Case FcstPrice
           'Fcst = m_priceAdj(Pd)
           MasterAdjFcst = m_MasterAdjFcst(Pd) * m_PriceAdj(Pd)
        Case Else
            MasterAdjFcst = m_MasterAdjFcst(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.MasterAdjFcst (Get))"
End Property
Public Property Get AdjFcst( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler

    'Set return code to failure
    AdjFcst = -1
    
    Select Case FcstUnit
        Case FcstUnits
            AdjFcst = m_AdjFcst(Pd)
        Case FcstCost
            'Fcst = m_Fcst(Pd) * Cost
            AdjFcst = m_AdjFcst(Pd) * m_CostAdj(Pd)
        Case FcstCube
            AdjFcst = m_AdjFcst(Pd) * Cube
        Case FcstWeight
            AdjFcst = m_AdjFcst(Pd) * Weight
        Case FcstPrice
           'Fcst = m_priceAdj(Pd)
           AdjFcst = m_AdjFcst(Pd) * m_PriceAdj(Pd)
        Case Else
            AdjFcst = m_AdjFcst(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjFcst (Get))"
End Property
Public Property Get Fcst_Month() As Double
On Error GoTo ErrorHandler
    
    Fcst_Month = m_Fcst_Month
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Fcst_Month (Get))"
End Property
    
Public Property Get Fcst_Quarter() As Double
On Error GoTo ErrorHandler
    
    Fcst_Quarter = m_Fcst_Quarter
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Fcst_Quarter (Get))"
End Property
    
Public Property Get Fcst_Year() As Double
On Error GoTo ErrorHandler
    
    Fcst_Year = m_Fcst_Year
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Fcst_Year (Get))"
End Property
    
Public Property Get FcstLT() As Double
On Error GoTo ErrorHandler

    FcstLT = m_FcstLT

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.FcstLT (Get))"
End Property

Public Property Get FcstRT() As Double
On Error GoTo ErrorHandler
    
    FcstRT = m_FcstRT

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.FcstRT (Get))"
End Property
    
Public Property Get LastYearCost() As Double
On Error GoTo ErrorHandler
    
    LastYearCost = LastFYCost
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.LastYearCost  (Get))"
End Property

Public Property Get LastYearPrice() As Double
On Error GoTo ErrorHandler

    LastYearPrice = LastFYPrice
        
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.LastYearPrice  (Get))"
End Property

Public Property Get ThisYearCost() As Double
On Error GoTo ErrorHandler

    ThisYearCost = ThisFYCost
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.ThisYearCost  (Get))"
End Property
Public Property Get ThisYearPrice() As Double
On Error GoTo ErrorHandler
    
    ThisYearPrice = ThisFYPrice

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.ThisYearPrice  (Get))"
End Property

Public Property Get NetRqmt( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler

    Select Case FcstUnit
    Case 0      'Units
        NetRqmt = m_NetRqmts(Pd)
    Case 1      'Cost
        'NetRqmt = m_NetRqmts(Pd) * Cost
        NetRqmt = m_xCostAdj(Pd)
    Case 2      'Cube
        NetRqmt = m_NetRqmts(Pd) * Cube
    Case 3      'Weight
        NetRqmt = m_NetRqmts(Pd) * Weight
    Case 4      'Price
        'NetRqmt = m_NetRqmts(Pd) * Price
        NetRqmt = m_xPriceAdj(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.NetRqmt (Get))"
End Property
Private Function HistNetReqAndAdjNetReqBetweenDates( _
    PdStartDate As Date, _
    PdEndDate As Date, _
    ByRef NetReq As Double, _
    ByRef AdjNetReq As Double _
) As Integer
On Error GoTo ErrorHandler
    Dim Counter As Long
    Dim Ratio As Double
    Dim Numerator As Double
    Dim Denominator As Double
    NetReq = 0#
    AdjNetReq = 0#
    Ratio = 0#
    If PopulateModificationRcdQtyCalled = False Then
        DataYes = PopulateModificationRcdQty(FcstKey, LcId, Item)
    End If
    
    If DataYes = False Then Exit Function
    
    
    For Counter = 1 To UBound(ModificationRec())
        If ModificationRec(Counter).StartDate > PdEndDate Then Exit For
        If ModificationRec(Counter).EndDate >= PdStartDate _
        And ModificationRec(Counter).StartDate <= PdEndDate _
        Then
            If ModificationRec(Counter).StartDate <= PdStartDate _
            And ModificationRec(Counter).EndDate >= PdStartDate _
            Then
                If PdEndDate < ModificationRec(Counter).EndDate Then
                    Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                    
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                Else
                     Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, ModificationRec(Counter).EndDate)
                     Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                End If
            ElseIf ModificationRec(Counter).StartDate <= PdEndDate _
            And ModificationRec(Counter).EndDate >= PdEndDate _
            Then
                Numerator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, PdEndDate)
                Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                If Denominator = 0 Then Denominator = 1
                Ratio = Numerator / Denominator
            Else
                Ratio = 1#
            End If
                NetReq = NetReq + ModificationRec(Counter).FcstNetReq * Ratio
                AdjNetReq = AdjNetReq + ModificationRec(Counter).FcstAdjNetReq * Ratio
        End If
        Ratio = 0#
    Next

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.HistNetReqAndAdjNetReqBetweenDates)"
End Function
Public Property Get AdjNetRqmt( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler

    Select Case FcstUnit
    Case 0      'Units
        AdjNetRqmt = m_AdjNetRqmts(Pd)
    Case 1      'Cost
        'NetRqmt = m_NetRqmts(Pd) * Cost
        AdjNetRqmt = m_xAdjCostAdj(Pd)
    Case 2      'Cube
        AdjNetRqmt = m_AdjNetRqmts(Pd) * Cube
    Case 3      'Weight
        AdjNetRqmt = m_AdjNetRqmts(Pd) * Weight
    Case 4      'Price
        'NetRqmt = m_NetRqmts(Pd) * Price
        AdjNetRqmt = m_xAdjPriceAdj(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjNetRqmt (Get))"
End Property


Public Property Get NextYearCost() As Double
On Error GoTo ErrorHandler
    
    NextYearCost = NextFYCost

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.NextYearCost  (Get))"
End Property

Public Property Get NextYearPrice() As Double
On Error GoTo ErrorHandler
    
    NextYearPrice = NextFYPrice

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.NextYearPrice  (Get))"
End Property

Public Property Get OrderPt() As Long
On Error GoTo ErrorHandler
    
    OrderPt = m_OrderPt
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.OrderPt (Get))"
End Property
Public Property Get AdjOrderPt() As Long
On Error GoTo ErrorHandler
    
    AdjOrderPt = m_AdjOrderPt
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjOrderPt (Get))"
End Property
Public Property Get OrderQty() As Long
On Error GoTo ErrorHandler
    
    OrderQty = m_OrderQty
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.OrderQty (Get))"
End Property
Public Property Get AdjOrderQty() As Long
On Error GoTo ErrorHandler
    
    AdjOrderQty = m_AdjOrderQty
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjOrderQty (Get))"
End Property
    
Public Property Get PdDate( _
    ByVal Pd As Integer _
) As Date
On Error GoTo ErrorHandler

    PdDate = m_PdDate(Pd)

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.PdDate (Get))"
End Property

'Written by Osama Riyahi to get PlannedRct Array to the AIMAdvForecastMod then send it back to
'AIM_ForecastModification Screen to be used to calculate the Inventory rather than using the Adj. Sys Net Req
Public Property Get PlannedRcpts( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler

    Select Case FcstUnit
    Case 0      'Units
        PlannedRcpts = m_PlannedRcpts(Pd)
    Case 1      'Cost
        'NetRqmt = m_NetRqmts(Pd) * Cost
        PlannedRcpts = m_PlannedRcpts(Pd)
    Case 2      'Cube
        PlannedRcpts = m_PlannedRcpts(Pd) * Cube
    Case 3      'Weight
        PlannedRcpts = m_PlannedRcpts(Pd) * Weight
    Case 4      'Price
        'NetRqmt = m_NetRqmts(Pd) * Price
        PlannedRcpts = m_PlannedRcpts(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.NetRqmt (Get))"
End Property

Public Property Get AdjPlannedRcpts( _
    ByVal Pd As Integer, _
    Optional FcstUnit As AIM_FORECASTUNITS = 0 _
) As Double
On Error GoTo ErrorHandler

    Select Case FcstUnit
    Case 0      'Units
        AdjPlannedRcpts = m_AdjPlannedRcpts(Pd)
    Case 1      'Cost
        'NetRqmt = m_NetRqmts(Pd) * Cost
        AdjPlannedRcpts = m_AdjPlannedRcpts(Pd)
    Case 2      'Cube
        AdjPlannedRcpts = m_AdjPlannedRcpts(Pd) * Cube
    Case 3      'Weight
        AdjPlannedRcpts = m_AdjPlannedRcpts(Pd) * Weight
    Case 4      'Price
        'NetRqmt = m_NetRqmts(Pd) * Price
        AdjPlannedRcpts = m_AdjPlannedRcpts(Pd)
    End Select
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjPlannedRcpts (Get))"
End Property
Public Property Get PmAdj( _
    ByVal Pd As Integer _
) As Double
On Error GoTo ErrorHandler
    
    PmAdj = m_PMAdj(Pd)

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.PmAdj (Get))"
End Property

Public Property Let PmAdj( _
    ByVal Pd As Integer, _
    ByVal PmAdj As Double _
)
On Error GoTo ErrorHandler

    m_PMAdj(Pd) = PmAdj
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.pmadj (Let))"
End Property

Public Property Get SafetyStock() As Long
On Error GoTo ErrorHandler
    
    SafetyStock = m_SafetyStock
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.SafetyStock (Get))"
End Property
Public Property Get AdjSafetyStock() As Long
On Error GoTo ErrorHandler
    
    'AdjSafetyStock = m_AdjSafetyStock
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjSafetyStock (Get))"
End Property
Public Property Get TotalCost() As Double
On Error GoTo ErrorHandler

    'TotalCost = m_TotalFcst * Cost
    TotalCost = m_TotalCost

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalCost  (Get))"
End Property
Public Property Get TotalCostAdj() As Double
On Error GoTo ErrorHandler

    'TotalCost = m_TotalFcst * Cost
    TotalCostAdj = m_TotalCostAdj

Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalCostAdj  (Get))"
End Property
Public Property Get TotalCube() As Double
On Error GoTo ErrorHandler

    TotalCube = m_TotalFcst * Cube
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalCube (Get))"
End Property

Public Property Get TotalCubeAdj() As Double
On Error GoTo ErrorHandler

    TotalCubeAdj = m_TotalFcstAdj * Cube
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalCubeAdj (Get))"
End Property
Public Property Get TotalFcst() As Double
On Error GoTo ErrorHandler

    TotalFcst = m_TotalFcst
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalFcst  (Get))"
End Property
Public Property Get TotalFcstAdj() As Double
On Error GoTo ErrorHandler

    TotalFcstAdj = m_TotalFcstAdj
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalFcstAdj  (Get))"
End Property

Public Property Get TotalPrice() As Double
On Error GoTo ErrorHandler

 '   TotalPrice = m_TotalFcst * Price
     TotalPrice = m_TotalPrice
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalPrice  (Get))"
End Property
Public Property Get TotalPriceAdj() As Double
On Error GoTo ErrorHandler

 '   TotalPrice = m_TotalFcst * Price
     TotalPriceAdj = m_TotalPriceAdj
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalPriceAdj  (Get))"
End Property
Public Property Get TotalUnits() As Double
On Error GoTo ErrorHandler

    TotalUnits = m_TotalFcst
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalUnits  (Get))"
End Property
Public Property Get TotalUnitsAdj() As Double
On Error GoTo ErrorHandler

    TotalUnitsAdj = m_TotalFcstAdj
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalUnitsAdj  (Get))"
End Property
Public Property Get TotalWeight() As Double
On Error GoTo ErrorHandler

    TotalWeight = m_TotalFcst * Weight
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalWeight (Get))"
End Property
Public Property Get TotalWeightAdj() As Double
On Error GoTo ErrorHandler

    TotalWeightAdj = m_TotalFcstAdj * Weight
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalWeightAdj (Get))"
End Property

Public Property Get xTotalCost() As Double
On Error GoTo ErrorHandler

    'xTotalCost = m_xTotalFcst * Cost
    xTotalCost = m_xTotalCost
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xTotalCost (Get))"
End Property

Public Property Get xAdjTotalCost() As Double
On Error GoTo ErrorHandler

    'xTotalCost = m_xTotalFcst * Cost
    xAdjTotalCost = m_xAdjTotalCost
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xAdjTotalCost (Get))"
End Property

Public Property Get xTotalCube() As Double
On Error GoTo ErrorHandler
    
    xTotalCube = m_xTotalFcst * Cube
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xTotalCube (Get))"
End Property

Public Property Get xAdjTotalCube() As Double
On Error GoTo ErrorHandler
    
    xAdjTotalCube = m_xAdjTotalFcst * Cube
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xAdjTotalCube (Get))"
End Property

Public Property Get xTotalPrice() As Double
On Error GoTo ErrorHandler

    'xTotalPrice = m_xTotalFcst * Price
    xTotalPrice = m_xTotalPrice
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xTotalPrice (Get))"
End Property
Public Property Get xAdjTotalPrice() As Double
On Error GoTo ErrorHandler

    
    xAdjTotalPrice = m_xAdjTotalPrice
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xAdjTotalPrice (Get))"
End Property

Public Property Get xTotalUnits() As Double
On Error GoTo ErrorHandler

    xTotalUnits = m_xTotalFcst
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xTotalUnits (Get))"
End Property
Public Property Get xAdjTotalUnits() As Double
On Error GoTo ErrorHandler

    xAdjTotalUnits = m_xAdjTotalFcst
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xAdjTotalUnits (Get))"
End Property
Public Property Get xTotalWeight() As Double
On Error GoTo ErrorHandler

    xTotalWeight = m_xTotalFcst * Weight
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xTotalWeight (Get))"
End Property
Public Property Get xAdjTotalWeight() As Double
On Error GoTo ErrorHandler

    xAdjTotalWeight = m_xAdjTotalFcst * Weight
    
Exit Property
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.xAdjTotalWeight (Get))"
End Property


'*****************************************************************************
' PRIVATE FUNCTIONS
'*****************************************************************************
Private Function CarryForwardRounding( _
    ByRef Fcst() As Double _
) As Integer
On Error GoTo ErrorHandler

    Dim CarryForward As Double
    Dim FcstRound As Long
    Dim i As Integer
    
    For i = LBound(Fcst) To m_NbrPeriods
        FcstRound = Round(Fcst(i) + CarryForward, 0)
        CarryForward = CarryForward + (Fcst(i) - FcstRound)
        Fcst(i) = FcstRound
    Next i
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.CarryForwardRounding)"
End Function

'**********************************************************************
' Function Name:    (CalcInitialStock...)
' Description:      Calculates the forecast for the period(s) specified
' Parameters:       StartDate           Start Date
' Returns:          Succed or Fail
' By: SRI           Date: 08/13/2002
'**********************************************************************
Public Function CalcInitialStock( _
    StartDate As Date _
) As Integer
On Error GoTo ErrorHandler
  
    Dim PriceAdj As Double
    Dim CostAdj As Double
    Dim Burn As Double
    Dim Adj As Double
    Dim Price As Double
    Dim Cost As Double
    
    m_TotalFcst = 0
    m_TotalPrice = 0
    m_TotalCost = 0
    Price = 0
    Cost = 0
    
    'FcstStartdate will be today's date which is the default
    If StkDate < FcstStartDate Then
    'use current date for forecast insteard of startdate
        'Diffence of code between 4.2 and 4.4
        'commented
'       Burn = Forecast(StkDate, (FcstStartdate - 1), Adj)
'        Burn = Forecast(StkDate, (StartDate - 1), Adj, PriceAdj, CostAdj)
    Else
        Burn = 0
    End If
    
     'Calculate the available inventory
    m_TotalFcst = Oh + Oo - ComStk - BkOrder - BkComStk - Burn
    
    If m_TotalFcst < 0# Then m_TotalFcst = 0#

    CalcInitialStock = 1
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.CalcInitialStock)"
End Function
'**********************************************************************
' Function Name:    (InitilizeFirstTime)
' Description:      Initilzes connection and Procedures call only one time
' Parameters:
' Returns:          Succed or Fail
' By: SRI
'**********************************************************************
Public Function InitilizeFirstTime() As Integer
On Error GoTo ErrorHandler
'Open a database connection
Set Cn = New ADODB.Connection
SQLConnection Cn, CONNECTION_OPEN, True, , , "AIMAdvForecast.cls"

  Set AIM_Repository_Get_Sp = New ADODB.Command
    With AIM_Repository_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Repository_Get_Sp"
        .Parameters.Refresh
        .Parameters(0).Value = 0
'        .Parameters("@RepositoryKey").Value = 2
    End With
    
Set rsModification = New ADODB.Recordset
rsModification.CursorLocation = adUseClient
rsModification.LockType = adLockReadOnly


 Set AIM_ForecastMaster_Get_Sp = New ADODB.Command
    With AIM_ForecastMaster_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ForecastMaster_Get_Sp"
        .Parameters.Refresh
        .Parameters(0).Value = 0
    End With
 
Set rsMasterModification = New ADODB.Recordset
With rsMasterModification
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
End With

With AIM_CostPriceListPrice_Get_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_CostPriceListPrice_Get_Sp"
        .CommandType = adCmdStoredProc
        '.Parameters.Refresh
End With
    
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Lcid", adVarChar, adParamInput, 12)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@ItemId", adVarChar, adParamInput, 25)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Startdate", adDate, adParamInput)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Enddate", adDate, adParamInput)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Cost", adDouble, adParamOutput, 10.4)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@Price", adDouble, adParamOutput, 10.4)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    Set prm = AIM_CostPriceListPrice_Get_Sp.CreateParameter("@ListPrice", adDouble, adParamOutput, 10.4)
    AIM_CostPriceListPrice_Get_Sp.Parameters.Append prm
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.InitilizeFirstTime)"
End Function
'**********************************************************************
' Function Name:    (InitilizeVariables)
' Description:      Initilzes various variables so it can forecast for another item
' Parameters:
' Returns:          Succed or Fail
' By: SRI
'**********************************************************************
Public Function InitilizeVariables() As Integer
On Error GoTo ErrorHandler
  On Error GoTo ErrorHandler

    Dim i As Integer
    
    'Set the default Seasonality Indices
    For i = LBound(m_bi) To UBound(m_bi)
        m_bi(i) = 1
        m_PMAdj(i) = 1
    Next i
    
    For i = LBound(m_Fcst) To UBound(m_Fcst)
        m_Adj(i) = 0
        m_Fcst(i) = 0
        m_PdDate(i) = "01/01/1990"
        m_PlannedRcpts(i) = 0
        m_AdjPlannedRcpts(i) = 0
        m_NetRqmts(i) = 0
        m_AdjNetRqmts(i) = 0
        m_PriceAdj(i) = 0
        m_CostAdj(i) = 0
        m_xPriceAdj(i) = 0
        m_xAdjPriceAdj(i) = 0
        m_xCostAdj(i) = 0
        m_xAdjCostAdj(i) = 0
    Next i
    
    m_NbrReviewDays = 0
    m_NbrPeriods = 12

    'Initialize Totals
    m_TotalFcst = 0
    m_TotalFcstAdj = 0
    m_xTotalFcst = 0
    m_xAdjTotalFcst = 0
    m_TotalPrice = 0
    m_TotalPriceAdj = 0
    m_TotalCost = 0
    m_TotalCostAdj = 0
    m_xTotalPrice = 0
    m_xAdjTotalPrice = 0
    m_xTotalCost = 0
    m_xAdjTotalCost = 0
    'Initialize Available Inventory
    AvailQty = 0
    AdjAvailQty = 0
    Oh = 0
    Oo = 0
    ComStk = 0
    BkOrder = 0
    BkComStk = 0
    
    'Initialize Public Variables to Default Values
    AdjustPct = 0
    ApplySeasonsIndex = False
    ApplyTrend = 0
    BaseDate = Date
    CarryFwdRoundingOption = True
    
    Cost = 0
    Cube = 0
    DmdScalingFactor = 1
    FcstDemand = 0
    FcstEndDate = "12/31/9999"
    
    FcstStartDate = Date
    FcstUnit = 0
    HiTrndDemandLimit = 1
    InActDate = "12/31/9999"
    Item = "Item Number"
    
    ItStat = "A"
    LcId = "Location Id"
    PlnTT = "Y"
    PmStatus = False
    PmStartDate = "01/01/1990"
    
    PmEndDate = "01/01/1990"
    ScalingEffUntil = "1/1/1990"
    Trend = 0
    UserFcst = 0
    UserFcstExpDate = "1/1/1990"
    
    Weight = 0

    'Initialize Order Policy Calculated Values to their default values
    m_Burn = 0
    
    m_FcstLT = 0
    m_FcstRT = 0
    m_Fcst_Month = 0
    m_Fcst_Quarter = 0

    m_Fcst_Year = 0
    m_OrderPt = 0
    m_AdjOrderPt = 0
    m_OrderQty = 0
    m_AdjOrderQty = 0
    'm_AdjSafetyStock = 0
    m_SafetyStock = 0
    m_EstOnOrder = 0
    m_AdjEstOnOrder = 0
    'Initialize Order Policy Variables
    Accum_Lt = 1
    CStock = 0
    Dft_TurnHigh = 999.9
    Dft_TurnLow = 0
    DSer = 0.95
    HiMADP = 9
    Int_Enabled = "N"
    IntSafetyStock = 0
    Int_LookBackPds = 52
    IsIntermittent = False
    LoMADP = 0.1
    KFactor = 30
    LtvFact = 0
    MADExK = 0.5
    MAE = 0
    Mean_NZ = 0
    ReplenCost = 5
    ReplenCost2 = 0
    ReviewTime = 1
    StdDev_NZ = 0
    StkDate = Date
    ZSStock = "N"

    For i = LBound(m_BkQty) To UBound(m_BkQty)
        m_BkQty(i) = 0
        m_BkCost(i) = 0
    Next i
    
    'Initialize Review Cycle Variables
    RevFreq = 0
    RevInterval = 1
    RevSunday = False
    RevMonday = True
    RevTuesday = True
    RevWednesday = True
    RevThursday = True
    RevFriday = True
    RevSaturday = True
    WeekQualifier = 0
    RevStartDate = "01/01/1990"
    RevEndDate = "12/31/9999"
    InitBuyPct = 1
    InitRevDate = "01/01/1990"
    ReviewTime = 1
    
    'Initialize Net Requirements Flag
    NetRqmtsOpt = False
'     'Open a database connection
'    Set Cn = New ADODB.Connection
'     SQLConnection Cn, CONNECTION_OPEN, True, , , "AIMAdvForecast.cls"
   InitilizeVariables = 1
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.InitilizeVariables)"
End Function







'**********************************************************************
' Function Name:    ForecastOne(...)
' Description:      Calculates the forecast for the period(s) specified
' Parameters:       StartDate           Start Date
'                   EndDate             End Date
'                   Adj                 Forecast Adjustment
' Returns:          Succed or Fail
' By: RES           Date: 03/03/2001
' Copyright (c) Technology Advantage, Alpharetta, GA  1999 - 2001
'Oct-20-2003    Srinivas Modified original Forcast function
'                        called from forecast function
'**********************************************************************
Public Function ForecastOne( _
    StartDate As Date, _
    EndDate As Date, _
    ByRef Adj As Double, _
    ByRef SysFcst As Double, _
    ByRef AdjSysFcst As Double, _
    ByRef MasterAdjSysFcst As Double, _
    FcstDemand As Double, _
    ApplySeasonsIndex As Boolean, _
    ApplyTrend As Integer _
) As Double
On Error GoTo ErrorHandler

    Dim CurPd As Integer
    Dim CurStartdate As Date
    Dim DailyPct As Double
    Dim Demand As Double
    Dim EndWeekDays As Integer
    Dim EndWeekPct As Double
    Dim FirstStartDate As Date
    Dim LastStartDate As Date
    Dim StartWeekDays As Integer
    Dim StartWeekPct As Double
    Dim TrnPds As Integer
    Dim Trend As Double
    Dim Price As Double
    Dim Cost As Double
    Dim PrePd As Integer
    Dim AdjQty As Double
    Dim MasterAdjQty As Double
    'Initialize the Forecast Adjustment
    Adj = 0
    PrePd = 0
    SysFcst = 0
    AdjSysFcst = 0
    

    'Calculate the First Date of the First Period
    FirstStartDate = Me.AIMAdvCalendar.GetStartDateFmDate(StartDate)
    'Calculate the First Date of the Last Period
    LastStartDate = Me.AIMAdvCalendar.GetStartDateFmDate(EndDate)
    'Calculate the number of working days in the first week
    StartWeekDays = Me.AIMAdvCalendar.GetWorkingDaysInWeek(StartDate)
    'Calculate the number of working days in the last period
    EndWeekDays = Me.AIMAdvCalendar.GetWorkingDaysInWeek(EndDate)
    
    If m_Interval = int_Days Then
        DailyPct = Me.AIMAdvCalendar.GetWorkingDays(StartDate, EndDate) / EndWeekDays
        StartWeekPct = 0
        EndWeekPct = 0
    Else
        'Calculate the Start Week Percentages
        If StartWeekDays > 0 Then
            If EndDate < FirstStartDate + 6 Then
                StartWeekPct = Me.AIMAdvCalendar.GetWorkingDays(StartDate, EndDate) / StartWeekDays
            Else
                StartWeekPct = Me.AIMAdvCalendar.GetWorkingDays(StartDate, FirstStartDate + 6) / StartWeekDays
            End If
        Else
            StartWeekPct = 0
        End If
    
        'Calculate the End Week Percentages
        If EndWeekDays > 0 Then
            EndWeekPct = Me.AIMAdvCalendar.GetWorkingDays(LastStartDate, EndDate) / EndWeekDays
        Else
            EndWeekPct = 0
        End If
    End If
    
    'Forecast from the Start Date through the End Date
    CurStartdate = FirstStartDate
    SysFcst = 0
    AdjSysFcst = 0
    MasterAdjSysFcst = 0
    Do Until CurStartdate >= EndDate
        CurPd = Me.AIMAdvCalendar.GetPeriod(CurStartdate)
        CurPd = IIf(CurPd = PrePd, 0, CurPd)
        
        'Check for an invalid period
        If CurPd <> 0 Then
            'Apply Trend if appropriate
            ' Or (ApplyTrend = 1 And Me.PlnTT = "Y")  ' do not check for Plntt as per mike Mar-02-05
            If ApplyTrend = 2 _
            Or (ApplyTrend = 1 And Me.MethodTrend = True) _
            Then
                TrnPds = DateDiff("d", Me.BaseDate, CurStartdate) \ 7
                Trend = TrnPds * Me.Trend
    
                'Check for trend limit exception
                If Abs(Trend) > Abs(Me.HiTrndDemandLimit * FcstDemand) Then
                    If Trend >= 0 Then
                        Trend = FcstDemand * Me.HiTrndDemandLimit
                    Else
                        Trend = FcstDemand * Me.HiTrndDemandLimit * -1
                    End If
                End If
            Else
                TrnPds = 0
                Trend = 0
            End If
    
            'Apply the Seasonality Profile; if required
            Demand = IIf(ApplySeasonsIndex, (FcstDemand + Trend) * Me.Bi(CurPd), (FcstDemand + Trend))
            
            'Check for an active promotion
            'Promotion is Enabled, and
            '[Current Start Date] >= [Promotion Start Date] and [Current Start Date] <= [Promotion End Date]
            If PmStatus _
            And CurStartdate >= PmStartDate _
            And CurStartdate <= PmEndDate _
            Then
                Demand = Demand * m_PMAdj(CurPd)
                Adj = Adj + m_PMAdj(CurPd)
            End If
    
            'Update Demand for Period
            If m_Interval = int_Days Then
                SysFcst = Demand * DailyPct
                AdjSysFcst = Demand * DailyPct 'need to be coded for sri
                MasterAdjSysFcst = Demand * DailyPct ' need to be coded for sri
                
            Else
                If CurStartdate = FirstStartDate _
                And CurStartdate = LastStartDate Then
                    'True and True
                    SysFcst = Demand * (StartWeekPct)
                    'sri begin
                    If StartWeekDays > 0 Then
                        AdjLoopEntered = False
                        MasterAdjLoopEntered = False
                        If EndDate < FirstStartDate + 6 Then
                            AdjBetweenDates StartDate, EndDate, Demand, AdjQty, 1#
                            MasterAdjBetweenDates StartDate, EndDate, Demand, MasterAdjQty
                        Else
                            AdjBetweenDates StartDate, FirstStartDate + 6, Demand, AdjQty, 1#
                            MasterAdjBetweenDates StartDate, FirstStartDate + 6, Demand, MasterAdjQty
                        End If
                        'AdjSysFcst = GetAdjQty(Demand * (StartWeekPct))
                        'commented out by sri on 'Aug-04-2004  the following line and add the one after it
                        'AdjSysFcst = AdjQty * StartWeekPct
                        If AdjLoopEntered = False Then
                            AdjSysFcst = Demand
                        Else
                            AdjSysFcst = AdjQty
                        End If
                        If MasterAdjLoopEntered = False Then
                            MasterAdjSysFcst = Demand
                        Else
                            MasterAdjSysFcst = MasterAdjQty
                        End If
                    Else
                        AdjSysFcst = 0
                        MasterAdjSysFcst = 0
                    End If
                    'sri end
                ElseIf CurStartdate <> FirstStartDate _
                And CurStartdate <> LastStartDate Then
                    AdjLoopEntered = False
                    MasterAdjLoopEntered = False
                    'False and False
                    If CurPd = 0 _
                    And PrePd = 52 Then
                        
                        If DatePart("yyyy", CurStartdate) Mod 4 = 0 Then
                            AdjBetweenDates CurStartdate, CurStartdate + 1, Demand, AdjQty, 1#
                            MasterAdjBetweenDates CurStartdate, CurStartdate + 1, Demand, MasterAdjQty
                        Else
                            AdjBetweenDates CurStartdate, CurStartdate, Demand, AdjQty, 1#
                            MasterAdjBetweenDates CurStartdate, CurStartdate, Demand, MasterAdjQty
                        End If
                    Else
                        AdjBetweenDates CurStartdate, CurStartdate + 6, Demand, AdjQty, 1#
                        MasterAdjBetweenDates CurStartdate, CurStartdate + 6, Demand, MasterAdjQty
                    End If
                    'sri end
'                    AdjSysFcst = AdjSysFcst + GetAdjQty(Demand)
                    If AdjLoopEntered = False Then
                        AdjQty = Demand
                    Else
                        AdjQty = AdjQty
                    End If
                    If MasterAdjLoopEntered = False Then
                        MasterAdjQty = Demand
                    Else
                        MasterAdjQty = MasterAdjQty
                    End If
                    AdjSysFcst = AdjSysFcst + AdjQty
                    MasterAdjSysFcst = MasterAdjSysFcst + MasterAdjQty
                    SysFcst = SysFcst + Demand
                ElseIf CurStartdate = FirstStartDate _
                And CurStartdate <> LastStartDate Then
                    
                    'True and False
                    If StartWeekDays > 0 Then
                        AdjLoopEntered = False
                        MasterAdjLoopEntered = False
                        If EndDate < FirstStartDate + 6 Then
                            AdjBetweenDates StartDate, EndDate, Demand, AdjQty, StartWeekPct
                            MasterAdjBetweenDates StartDate, EndDate, Demand, MasterAdjQty
                        Else
                            'It comes here
                            AdjBetweenDates StartDate, FirstStartDate + 6, Demand, AdjQty, StartWeekPct
                            MasterAdjBetweenDates StartDate, FirstStartDate + 6, Demand, MasterAdjQty
                        End If
'                        AdjSysFcst = GetAdjQty(Demand * (StartWeekPct))
                         If AdjLoopEntered = False Then
                            AdjQty = Demand * StartWeekPct
                         Else
                            AdjQty = AdjQty
                         End If
                         If MasterAdjLoopEntered = False Then
                            MasterAdjQty = Demand * StartWeekPct
                         Else
                            MasterAdjQty = MasterAdjQty
                         End If
                         AdjSysFcst = AdjQty
                         MasterAdjSysFcst = MasterAdjQty
                    Else
                        AdjSysFcst = 0
                        MasterAdjSysFcst = 0
                    End If
                    SysFcst = Demand * StartWeekPct
                ElseIf CurStartdate <> FirstStartDate _
                And CurStartdate = LastStartDate Then
                    'False and True
                    If EndWeekDays > 0 Then
                        AdjLoopEntered = False
                        MasterAdjLoopEntered = False
                        AdjBetweenDates LastStartDate, EndDate, Demand, AdjQty, EndWeekPct
                        MasterAdjBetweenDates LastStartDate, EndDate, Demand, MasterAdjQty
'                        AdjSysFcst = AdjSysFcst + GetAdjQty(Demand * (EndWeekPct))
'                        AdjSysFcst = AdjSysFcst + AdjQty * EndWeekPct
                         If AdjLoopEntered = False Then
                            AdjQty = Demand * EndWeekPct
                          Else
                             AdjQty = AdjQty
                          End If
                          If MasterAdjLoopEntered = False Then
                              MasterAdjQty = Demand * EndWeekPct
                          Else
                             MasterAdjQty = MasterAdjQty
                          End If
                         AdjSysFcst = AdjSysFcst + AdjQty
                         MasterAdjSysFcst = MasterAdjSysFcst + MasterAdjQty
                    Else
                        AdjSysFcst = AdjSysFcst + 0
                        MasterAdjSysFcst = MasterAdjSysFcst + 0
                    End If
                    'sri end
                    SysFcst = SysFcst + (Demand * EndWeekPct)
                    Exit Do
                End If
            End If
        End If

        'Next Week
        If CurPd = 0 _
        And PrePd = 52 Then
            If DatePart("yyyy", CurStartdate) Mod 4 = 0 Then
                CurStartdate = CurStartdate + 2
            Else
                CurStartdate = CurStartdate + 1
            End If
        Else
            CurStartdate = CurStartdate + 7
        End If
        
        PrePd = CurPd
    Loop
    
    ForecastOne = SysFcst
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.ForecastOne)"
End Function

'**********************************************************************
' Function Name:    Forecast(...)
' Description:      Calculates the forecast for the period(s) specified
' Parameters:       StartDate           Start Date
'                   EndDate             End Date
'                   Adj                 Forecast Adjustment
' Returns:          Succed or Fail
' By: RES           Date: 03/03/2001
' Copyright (c) Technology Advantage, Alpharetta, GA  1999 - 2001
'Oct-20-03  Srinvas  Modified original Forecast function now much of the origial
'                    code is in Forecastone function
'******************************************************************************
Public Function Forecast( _
    StartDate As Date, _
    EndDate As Date, _
    ByRef Adj As Double, _
    ByRef SysFcst As Double, _
    ByRef AdjSysFcst As Double, _
    ByRef MasterAdjSysFcst As Double) As Double
On Error GoTo ErrorHandler

    Dim CurPd As Integer
    Dim CurStartdate As Date
    Dim DailyPct As Double
    Dim Demand As Double
    Dim FcstDemand As Double
    Dim SysFcstForPeriod As Double
    Dim AdjSysFcstForPeriod As Double
    Dim MasterAdjSysFcstForPeriod As Double
    Dim EndWeekDays As Integer
    Dim EndWeekPct As Double
    Dim FirstStartDate As Date
    Dim LastStartDate As Date
    Dim StartWeekDays As Integer
    Dim StartWeekPct As Double
    Dim TrnPds As Integer
    Dim Trend As Double
    Dim Price As Double
    Dim Cost As Double
    Dim PrePd As Integer
    
    'Initialize the Forecast Adjustment
    Adj = 0
    PrePd = 0
    SysFcst = 0
    AdjSysFcst = 0
    SysFcstForPeriod = 0
    AdjSysFcstForPeriod = 0
    MasterAdjSysFcstForPeriod = 0
    
    'Adjust the End Date for the Item's Discontinue Date
    Select Case ItStat
    Case "I", "P", "X"      'Inactive, Purge, Discontinued
        SysFcst = 0
        AdjSysFcst = 0
        Forecast = 0
        
        Exit Function
        
    'Stopped By Osama to Check the InActDate regardless of the ItStat
    'Case "D"                'Discontinued -- Still Active
    '    If Enddate > InActDate Then
    '        Enddate = InActDate
    '    End If
    End Select

    'Osama Check the InActDate Regardless of ItStat
    If EndDate > InActDate Then
        EndDate = InActDate
    End If
    'Check for invalid start and end dates
    If EndDate < StartDate Then
        SysFcst = 0
        AdjSysFcst = 0
        Forecast = 0
        Exit Function
    End If

    'Calculate the First Date of the First Period
    FirstStartDate = Me.AIMAdvCalendar.GetStartDateFmDate(StartDate)
    If FirstStartDate = 0 Then
        SysFcst = 0
        AdjSysFcst = 0
        Forecast = 0
        Exit Function
    End If

    'Calculate the First Date of the Last Period
    LastStartDate = Me.AIMAdvCalendar.GetStartDateFmDate(EndDate)
    If LastStartDate = 0 Then
        SysFcst = 0
        AdjSysFcst = 0
        Forecast = 0
        Exit Function
    End If
    
    'sri begin
    'Check for a User Override on Forecast Demand - No Seasonality or Trend is applied to User Overrides
'    If Me.UserFcst > 0 And Me.UserFcstExpDate >= FirstStartDate Then
    If Me.UserFcst > 0 And Me.UserFcstExpDate > StartDate Then
        If EndDate < UserFcstExpDate Then
            If EndDate < DPRollingFcstStartDate Then
                    HistFcstAndAdjBetweenDates StartDate, EndDate, SysFcst, AdjSysFcst
                    SysFcstForPeriod = SysFcst
                    AdjSysFcstForPeriod = AdjSysFcst
                    If MasterDataYes = False Then
                        MasterAdjSysFcstForPeriod = SysFcst
                    Else
                        HistFcstAndMasterAdjBetweenDates StartDate, EndDate, SysFcst, AdjSysFcst
                        MasterAdjSysFcstForPeriod = AdjSysFcst
                    End If
                    
                ElseIf StartDate < DPRollingFcstStartDate And EndDate >= DPRollingFcstStartDate Then
                    HistFcstAndAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
                    SysFcstForPeriod = SysFcst
                    AdjSysFcstForPeriod = AdjSysFcst
                    If MasterDataYes = False Then
                        MasterAdjSysFcstForPeriod = SysFcst
                    Else
                        HistFcstAndMasterAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
                        MasterAdjSysFcstForPeriod = AdjSysFcst
                    End If
                    FcstDemand = Me.UserFcst
                    ForecastOne DPRollingFcstStartDate, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                    SysFcstForPeriod = SysFcstForPeriod + SysFcst
                    AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
                    MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
                    
                Else
                    FcstDemand = Me.UserFcst
                    ForecastOne StartDate, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, False, 0
                    SysFcstForPeriod = SysFcst
                    AdjSysFcstForPeriod = AdjSysFcst
                    MasterAdjSysFcstForPeriod = MasterAdjSysFcst
                End If
            
        Else
            'Calc User Forecast for the period until user-specified expiration date
            ' and add the regular forecast for period after user-spec. exp. date to the enddate
            If EndDate < DPRollingFcstStartDate Then
                HistFcstAndAdjBetweenDates StartDate, EndDate, SysFcst, AdjSysFcst
                SysFcstForPeriod = SysFcst
                AdjSysFcstForPeriod = AdjSysFcst
                If MasterDataYes = False Then
                    MasterAdjSysFcstForPeriod = SysFcst
                Else
                    HistFcstAndMasterAdjBetweenDates StartDate, EndDate, SysFcst, AdjSysFcst
                    MasterAdjSysFcstForPeriod = AdjSysFcst
                End If
            ElseIf UserFcstExpDate > DPRollingFcstStartDate And DPRollingFcstStartDate > StartDate _
                    And DPRollingFcstStartDate < EndDate Then
                HistFcstAndAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
                SysFcstForPeriod = SysFcst
                AdjSysFcstForPeriod = AdjSysFcst
                If MasterDataYes = False Then
                    MasterAdjSysFcstForPeriod = SysFcst
                Else
                    HistFcstAndMasterAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
                    MasterAdjSysFcstForPeriod = AdjSysFcst
                End If
                
                FcstDemand = Me.UserFcst
                ForecastOne DPRollingFcstStartDate, UserFcstExpDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                SysFcstForPeriod = SysFcstForPeriod + SysFcst
                AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
                MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
                
                FcstDemand = Me.FcstDemand
                ForecastOne Me.UserFcstExpDate + 1, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                SysFcstForPeriod = SysFcstForPeriod + SysFcst
                AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
                MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
            ElseIf UserFcstExpDate > DPRollingFcstStartDate And DPRollingFcstStartDate < StartDate Then
                FcstDemand = Me.UserFcst
                ForecastOne StartDate, UserFcstExpDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                SysFcstForPeriod = SysFcst
                AdjSysFcstForPeriod = AdjSysFcst
                
                FcstDemand = Me.FcstDemand
                ForecastOne Me.UserFcstExpDate + 1, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                SysFcstForPeriod = SysFcstForPeriod + SysFcst
                AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
                MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
            ElseIf UserFcstExpDate = DPRollingFcstStartDate Then
                HistFcstAndAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
                SysFcstForPeriod = SysFcst
                AdjSysFcstForPeriod = AdjSysFcst
                If MasterDataYes = False Then
                    MasterAdjSysFcstForPeriod = SysFcst
                Else
                    HistFcstAndMasterAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, MasterAdjSysFcst
                    MasterAdjSysFcstForPeriod = MasterAdjSysFcst
                End If
                FcstDemand = Me.FcstDemand
                ForecastOne DPRollingFcstStartDate, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                SysFcstForPeriod = SysFcstForPeriod + SysFcst
                AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
                MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
            ElseIf UserFcstExpDate < DPRollingFcstStartDate And DPRollingFcstStartDate > StartDate _
                    And DPRollingFcstStartDate < EndDate Then
                HistFcstAndAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
                SysFcstForPeriod = SysFcst
                AdjSysFcstForPeriod = AdjSysFcst
                If MasterDataYes = False Then
                    MasterAdjSysFcstForPeriod = SysFcst
                Else
                    HistFcstAndMasterAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, MasterAdjSysFcst
                    MasterAdjSysFcstForPeriod = MasterAdjSysFcst
                End If
                FcstDemand = Me.FcstDemand
                ForecastOne DPRollingFcstStartDate, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
                SysFcstForPeriod = SysFcstForPeriod + SysFcst
                AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
                MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
            End If
        End If
    Else
        'Calc forecast with Seasonality and Trend
        If EndDate < DPRollingFcstStartDate Then
            HistFcstAndAdjBetweenDates StartDate, EndDate, SysFcst, AdjSysFcst
            SysFcstForPeriod = SysFcst
            AdjSysFcstForPeriod = AdjSysFcst
            If MasterDataYes = False Then
                MasterAdjSysFcstForPeriod = SysFcst
            Else
                HistFcstAndMasterAdjBetweenDates StartDate, EndDate, SysFcst, MasterAdjSysFcst
                 MasterAdjSysFcstForPeriod = MasterAdjSysFcst
            End If
        ElseIf StartDate < DPRollingFcstStartDate And EndDate >= DPRollingFcstStartDate Then
            HistFcstAndAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, AdjSysFcst
            SysFcstForPeriod = SysFcst
            AdjSysFcstForPeriod = AdjSysFcst
            If MasterDataYes = False Then
                MasterAdjSysFcstForPeriod = SysFcst
            Else
                HistFcstAndMasterAdjBetweenDates StartDate, DPRollingFcstStartDate - 1, SysFcst, MasterAdjSysFcst
                MasterAdjSysFcstForPeriod = MasterAdjSysFcst
            End If
             FcstDemand = Me.FcstDemand
            ForecastOne DPRollingFcstStartDate, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
            SysFcstForPeriod = SysFcstForPeriod + SysFcst
            AdjSysFcstForPeriod = AdjSysFcstForPeriod + AdjSysFcst
            MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod + MasterAdjSysFcst
        Else
            FcstDemand = Me.FcstDemand
            ForecastOne StartDate, EndDate, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst, FcstDemand, ApplySeasonsIndex, ApplyTrend
            SysFcstForPeriod = SysFcst
            If DataYes = False Then
                'If there is no adjustment data then set the adjsysfcst same as sysfcst
                AdjSysFcst = SysFcstForPeriod
                AdjSysFcstForPeriod = SysFcstForPeriod
            Else
                AdjSysFcstForPeriod = AdjSysFcst
            End If
            If MasterDataYes = False Then
                'If there is no adjustment data then set the adjsysfcst same as sysfcst
                 MasterAdjSysFcst = SysFcstForPeriod
                 MasterAdjSysFcstForPeriod = MasterAdjSysFcst
            Else
                MasterAdjSysFcstForPeriod = MasterAdjSysFcst
            End If
               
         End If
    End If
    
    'Adjust for negative forecast
    SysFcstForPeriod = IIf(SysFcstForPeriod >= 0, SysFcstForPeriod, 0)
    AdjSysFcstForPeriod = IIf(AdjSysFcstForPeriod >= 0, AdjSysFcstForPeriod, 0)
    MasterAdjSysFcstForPeriod = IIf(MasterAdjSysFcstForPeriod >= 0, MasterAdjSysFcstForPeriod, 0)
    'Adjust Forecast for Location Demand Scaling
    If Date <= Me.ScalingEffUntil Then
        SysFcstForPeriod = SysFcstForPeriod * Me.DmdScalingFactor
        AdjSysFcstForPeriod = AdjSysFcstForPeriod * Me.DmdScalingFactor
        MasterAdjSysFcstForPeriod = MasterAdjSysFcstForPeriod * Me.DmdScalingFactor
    End If
    'Return Forecast -- Adjust for any global forecast adjustment
    SysFcstForPeriod = Round(SysFcstForPeriod, 2) * (1 + (AdjustPct / 100))
    AdjSysFcstForPeriod = Round(AdjSysFcstForPeriod, 2) * (1 + (AdjustPct / 100))
    MasterAdjSysFcstForPeriod = Round(MasterAdjSysFcstForPeriod, 2) * (1 + (AdjustPct / 100))
    
    SysFcst = SysFcstForPeriod
    AdjSysFcst = AdjSysFcstForPeriod
    MasterAdjSysFcst = MasterAdjSysFcstForPeriod
    Forecast = SysFcstForPeriod
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.Forecast)"
End Function

Public Function GetModNetReq( _
    ModNetReqArg() As Double _
) As Double
'This should really be property set
On Error GoTo ErrorHandler

    ModNetReq() = ModNetReqArg()

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetModNetReq)"
End Function

Private Function GetEndDate( _
    BaseDate As Date, _
    StartDate As Date, _
    Interval As AIM_INTERVALS _
) As Date
On Error GoTo ErrorHandler

    GetEndDate = GetNextStartDate(BaseDate, StartDate, Interval) - 1
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetEndDate)"
End Function

Private Function GetFcstPd( _
    TargetDate As Date _
) As Integer
On Error GoTo ErrorHandler

    Dim i As Integer
    
    If TargetDate < FcstStartDate Then
        GetFcstPd = 0           'Target Date Before Forecast Range
    ElseIf TargetDate > FcstEndDate Then
        GetFcstPd = 999
    Else
        For i = LBound(m_PdDate) To m_NbrPeriods
            If TargetDate >= m_PdDate(i) Then
                GetFcstPd = i
            End If
        Next i
    End If
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetFcstPd)"
End Function

Private Function GetAdjQty( _
    Demand As Double _
) As Double
On Error GoTo ErrorHandler

    Dim i As Integer
    
     If AdjOverRide = True Then
        GetAdjQty = AdjRcd.QtyAdj
    Else
        GetAdjQty = (1# + AdjRcd.QtyAdjPct / 100#) * Demand
        GetAdjQty = GetAdjQty + AdjRcd.QtyAdj
    End If
   
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetFcstPd)"
End Function
Public Function GetPrevRevDate( _
    ReviewDate As Date, _
    LastSchReview As Date, _
    Optional RevFreq As Integer = 0, _
    Optional RevInterval As Integer = 1, _
    Optional RevSunday As Boolean = False, _
    Optional RevMonday As Boolean = True, _
    Optional RevTuesday As Boolean = True, _
    Optional RevWednesday As Boolean = True, _
    Optional RevThursday As Boolean = True, _
    Optional RevFriday As Boolean = True, _
    Optional RevSaturday As Boolean = True, _
    Optional WeekQualifier As Integer = 0, _
    Optional RevStartDate As Date = "01/01/1990", _
    Optional RevEndDate As Date = "12/31/9999", _
    Optional InitBuyPct As Double = 1, _
    Optional InitRevDate As Date = "01/01/1990", _
    Optional ReviewTime As Integer = 1 _
) As Integer
On Error GoTo ErrorHandler
    
    Dim EndOfMonth As Date
    Dim IntervalTest As Integer
    Dim LastWeek As Boolean 'bit
    Dim StartofMonth As Date
    Dim TrialRvDate As Date
    Dim WeekOfMonth As Integer
    
    'Initilaize [Trial Review Date] to [LastSchReview]
    TrialRvDate = LastSchReview

    Do Until TrialRvDate < ReviewDate
    
        Select Case RevFreq
            Case 0                  'Dynamic
                TrialRvDate = DateAdd("d", 1, TrialRvDate)
    
            Case 1                  'Daily
                TrialRvDate = DateAdd("d", RevInterval, TrialRvDate)
    
            Case 2                  'Weekly
                'Check for no day selected
                If Not (RevSunday Or RevMonday Or RevTuesday _
                    Or RevWednesday Or RevThursday Or RevFriday _
                    Or RevSaturday) Then
    
                    Exit Do
                End If
    
                Do
                    'Increment date by one day
                    TrialRvDate = DateAdd("d", 1, TrialRvDate)
    
                    'Test for Weekly Interval
                    IntervalTest = (DateDiff("d", RevStartDate, TrialRvDate) / 7) Mod RevInterval
    
                    If Weekday(TrialRvDate) = 1 And RevSunday And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 2 And RevMonday And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 3 And RevTuesday And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 4 And RevWednesday And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 5 And RevThursday And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 6 And RevFriday And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 7 And RevSaturday And IntervalTest = 0 Then Exit Do
                Loop
    
            Case 3      'Monthly
                TrialRvDate = DateAdd("m", RevInterval, TrialRvDate)
    
            Case 4      'Week of Month
                Do
                    TrialRvDate = DateAdd("d", 1, TrialRvDate)
    
                    'Test for Week of the Month
                    StartofMonth = DateSerial(Year(TrialRvDate), Month(TrialRvDate), 1)
                    EndOfMonth = DateAdd("m", 1, StartofMonth) - 1
                    WeekOfMonth = Int(DateDiff("d", StartofMonth, TrialRvDate) \ 7) + 1
    
                    'Is this the last week ???
                    If DateDiff("d", TrialRvDate, EndOfMonth) \ 7 = 0 Then
                        LastWeek = True
                    Else
                        LastWeek = False
                    End If
    
                    If Weekday(TrialRvDate) = 1 And RevSunday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 2 And RevMonday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 3 And RevTuesday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 4 And RevWednesday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 5 And RevThursday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 6 And RevFriday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 7 And RevSaturday _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
    
                Loop
    
        End Select

    Loop        'End of Control Loop
    
    GetPrevRevDate = TrialRvDate
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetPrevRevDate)"
End Function

Private Function InitReviewDays() As Integer
On Error GoTo ErrorHandler

    Dim RvPtr As Integer
    Dim RvDate As Date
    Dim w_NextDateTime As Date
    
    'Dimension the Review Days Array
    ReDim m_ReviewDays(m_NbrPeriods)
    'Use stkdate in place of fcststartdate
    'Is the Item overdue for a review ?
    'When generating net requirements, a key consideration is the relationship between
    'the the Next Review Date (Next scheduled date for an item) and the Forecast Start Date.
'    If NextDateTime = FcstStartdate Then
'        m_ReviewDays(0) = NextDateTime
'        w_NextDateTime = NextDateTime
'    ElseIf NextDateTime < FcstStartdate Then
'        m_ReviewDays(0) = FcstStartdate - 1
'        w_NextDateTime = FcstStartdate - 1
'    ElseIf NextDateTime > FcstStartdate Then
'        m_ReviewDays(0) = FcstStartdate - 1
'        w_NextDateTime = FcstStartdate - 1
'    End If
'
'If the Review Frequency is Monthly and Review time is 30 set the first
'Review Day to the first day of the month from the Stock Date, then
'the next Review Day will be the first day of the next month and so on.
'e.g. : Stock Date = 13/10/2002, First Review Days will be 01/10/2002 .


'    If NextDateTime = DPRollingFcstStartDate Then
''        If RevFreq = 3 And ReviewTime = 30 Then
''            m_ReviewDays(0) = Format(CDate(Format(NextDateTime, "yyyy/mm") + "/01"), "yyyy/mm/dd")
''            w_NextDateTime = Format(CDate(Format(NextDateTime, "yyyy/mm") + "/01"), "yyyy/mm/dd")
''        Else
'            m_ReviewDays(0) = NextDateTime
'            w_NextDateTime = NextDateTime
''        End If
'    ElseIf NextDateTime < DPRollingFcstStartDate Then
''        If RevFreq = 3 And ReviewTime = 30 Then
''             m_ReviewDays(0) = Format(CDate(Format(DPRollingFcstStartDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
''            w_NextDateTime = Format(CDate(Format(DPRollingFcstStartDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
''        Else
'            m_ReviewDays(0) = DPRollingFcstStartDate - 1
'            w_NextDateTime = DPRollingFcstStartDate - 1
' '       End If
'    ElseIf NextDateTime > DPRollingFcstStartDate Then
''        If RevFreq = 3 And ReviewTime = 30 Then
''             m_ReviewDays(0) = Format(CDate(Format(DPRollingFcstStartDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
''            w_NextDateTime = Format(CDate(Format(DPRollingFcstStartDate, "yyyy/mm") + "/01"), "yyyy/mm/dd")
''        Else
'            m_ReviewDays(0) = DPRollingFcstStartDate - 1
'            w_NextDateTime = DPRollingFcstStartDate - 1
''        End If
'    End If
    NextDateTime = BaseDate
    'Determine the review points between the Stock Date and the Forecast End Date
    m_ReviewDays(0) = NextDateTime
    RvDate = m_ReviewDays(0)
    
    'Initialize the Review Date Pointer
    RvPtr = 1
    
    'Do Until RvDate > FcstEndDate
    Do
        'Get the next review date
        RvDate = GetNextRevDate(RvDate, w_NextDateTime, RevFreq, RevInterval, RevSunday, RevMonday, _
            RevTuesday, RevWednesday, RevThursday, RevFriday, RevSaturday, WeekQualifier, _
            RevStartDate, RevEndDate, , , ReviewTime)
            
        w_NextDateTime = RvDate
            
       
        'Add another date to the Review
        If RvPtr > UBound(m_ReviewDays) Then
            ReDim Preserve m_ReviewDays(RvPtr + 20)
        End If
    
        m_ReviewDays(RvPtr) = RvDate
        RvPtr = RvPtr + 1
'        'If the review date is outside the forecast period -- quit
'        If RvDate > FcstEndDate Then
'            Exit Do
'        End If
        
    Loop Until RvDate > FcstEndDate
    'Loop
    
    If RvPtr > 0 Then
        InitReviewDays = (RvPtr - 1)
    Else
        InitReviewDays = 0
    End If
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.InitReviewDays)"
End Function
Private Function GetFYPriceCost( _
    LcId As String, _
    Item As String _
) As Integer

On Error GoTo ErrorHandler
    Dim CurDate As Date
    
    CurDate = Date
    FyYear = AIMAdvCalendar.GetFiscalYear(CurDate)
    GetPrice AIMAdvCalendar.GetFiscalYearStartDate(FyYear), AIMAdvCalendar.GetFiscalYearEndDate(FyYear), LcId, Item, ThisFYPrice, ThisFYCost
    GetPrice AIMAdvCalendar.GetFiscalYearStartDate(FyYear - 1), AIMAdvCalendar.GetFiscalYearEndDate(FyYear - 1), LcId, Item, LastFYPrice, LastFYCost
    GetPrice AIMAdvCalendar.GetFiscalYearStartDate(FyYear + 1), AIMAdvCalendar.GetFiscalYearEndDate(FyYear + 1), LcId, Item, NextFYPrice, NextFYCost
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetFYPriceCost)"
End Function

Private Function GetPrice( _
    StartDate As Date, _
    EndDate As Date, _
    LcId As String, _
    Item As String, _
    Price As Double, _
    Cost As Double _
) As Double
On Error GoTo ErrorHandler
    Price = 0
    Cost = 0
    
     With AIM_CostPriceListPrice_Get_Sp
    'Set values
        .Parameters("@LcID").Value = Trim$(LcId)
        .Parameters("@ItemId").Value = Trim$(Item)
        .Parameters("@Startdate").Value = StartDate
        .Parameters("@Enddate").Value = EndDate
    'Run
        .Execute
    End With
    Price = AIM_CostPriceListPrice_Get_Sp("@price")
    Cost = AIM_CostPriceListPrice_Get_Sp("@Cost")
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetPrice)"
End Function

Private Function GetCurrentPriceCost( _
    StartDate As Date, _
    Price As Double, _
    Cost As Double _
) As Integer

On Error GoTo ErrorHandler
    Dim StartYear As Integer
    StartYear = AIMAdvCalendar.GetFiscalYear(StartDate)
    
    If StartYear = FyYear Then
        Price = ThisFYPrice
        Cost = ThisFYCost
    ElseIf StartYear < FyYear Then
        Price = LastFYPrice
        Cost = LastFYCost
    ElseIf StartYear > FyYear Then
        Price = NextFYPrice
        Cost = NextFYCost
    End If

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetCurrentPriceCost)"
End Function

Public Function PopulateMasterModificationRcdQty( _
    RepositoryKey As Long, _
    LcId As String, _
    ItemId As String _
) As Boolean
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim Pd As Integer
    Dim Counter As Integer
    Dim TotalRec As Integer
    Dim EndDate As Date
    
    RtnCode = MasterBudgetFcst_GetEq(AIM_ForecastMaster_Get_Sp, _
            rsMasterModification, _
            RepositoryKey, _
            LcId, _
            ItemId, _
            AdjStartDate, _
            AdjEndDate)
    
    If RtnCode < 0 Then
        PopulateMasterModificationRcdQty = False
        Exit Function
    End If

    TotalRec = rsMasterModification.RecordCount
    If TotalRec = 0 Then
        PopulateMasterModificationRcdQty = False
        Exit Function
    End If
    
    If f_IsRecordsetOpenAndPopulated(rsMasterModification) Then
         ReDim Preserve MasterModificationRec(1 To TotalRec + 3) 'Add three more periods so we have enough data
        'Load the Budget or Forecast data
        Counter = 1
        Do Until rsMasterModification.eof
            'Get the Demand History Period Number
            
            MasterModificationRec(Counter).StartDate = rsMasterModification("PeriodBegDate").Value
            EndDate = rsMasterModification("PeriodEndDate").Value
            'Enddate = GetNextStartDate(rsMasterModification("PeriodBegDate").Value, rsMasterModification("PeriodBegDate").Value, m_Interval)
            MasterModificationRec(Counter).EndDate = EndDate
            MasterModificationRec(Counter).MasterQtyAdj = rsMasterModification("QtyAdj").Value
            MasterModificationRec(Counter).MasterQtyAdjPct = rsMasterModification("QtyAdjPct").Value
            MasterModificationRec(Counter).MasterQtyAdjOverRide = rsMasterModification("QtyAdjOverRide").Value
            MasterModificationRec(Counter).MasterAdjOverRide = rsMasterModification("AdjOverRide").Value
            MasterModificationRec(Counter).Fcst = rsMasterModification("Fcst").Value
            Counter = Counter + 1
            rsMasterModification.MoveNext
        Loop
    End If
    
    For Counter = TotalRec + 1 To TotalRec + 3
        MasterModificationRec(Counter).StartDate = EndDate + 1
        EndDate = GetNextStartDate(EndDate, EndDate, m_Interval)
        MasterModificationRec(Counter).EndDate = EndDate
        MasterModificationRec(Counter).MasterQtyAdj = 0
        MasterModificationRec(Counter).MasterQtyAdjPct = 0
        MasterModificationRec(Counter).MasterQtyAdjOverRide = 0
        MasterModificationRec(Counter).MasterAdjOverRide = 0
        MasterModificationRec(Counter).Fcst = 0
    Next
    
    PopulateMasterModificationRcdQty = True
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.PopulateMasterModificationRcdQty)"
End Function
Public Function PopulateModificationRcdQty( _
    RepositoryKey As Long, _
    LcId As String, _
    ItemId As String _
) As Boolean
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim Pd As Integer
    Dim Counter As Integer
    Dim TotalRec As Integer
    Dim EndDate As Date
    PopulateModificationRcdQtyCalled = True
    RtnCode = BudgetFcst_GetEq(AIM_Repository_Get_Sp, _
            rsModification, _
            RepositoryKey, _
            LcId, _
            ItemId, _
            AdjStartDate, _
            AdjEndDate + 14)
    
    If RtnCode < 0 Then
        PopulateModificationRcdQty = False
        Exit Function
    End If

    TotalRec = rsModification.RecordCount
    If TotalRec = 0 Then
        PopulateModificationRcdQty = False
        Exit Function
    End If
    
    If f_IsRecordsetOpenAndPopulated(rsModification) Then
         ReDim Preserve ModificationRec(1 To TotalRec + 3)  'Add three more periods so we have enough data
        'Load the Budget or Forecast data
        Counter = 1
        Do Until rsModification.eof
            'Get the Demand History Period Number
            
            ModificationRec(Counter).StartDate = rsModification("FcstPdBegDate").Value
            EndDate = rsModification("FcstPdEndDate").Value
            'Enddate = GetNextStartDate(rsModification("FcstPdBegDate").Value, rsModification("FcstPdBegDate").Value, m_Interval)
            ModificationRec(Counter).EndDate = EndDate 'Enddate - 1
            ModificationRec(Counter).QtyAdj = rsModification("QtyAdj").Value
            ModificationRec(Counter).QtyAdjOverRide = rsModification("QtyAdjOverRide").Value
            ModificationRec(Counter).FcstNetReq = rsModification("FcstNetReq").Value
            ModificationRec(Counter).FcstAdjNetReq = rsModification("FcstAdjNetReq").Value
            ModificationRec(Counter).QtyAdjPct = rsModification("QtyAdjPct").Value
            ModificationRec(Counter).AdjOverRide = rsModification("AdjOverRide").Value
            ModificationRec(Counter).MasterQtyAdj = rsModification("MasterQtyAdj").Value
            ModificationRec(Counter).MasterQtyAdjOverRide = rsModification("MasterQtyAdjOverRide").Value
            ModificationRec(Counter).MasterQtyAdjPct = rsModification("MasterQtyAdjPct").Value
            ModificationRec(Counter).MasterAdjOverRide = rsModification("MasterAdjOverRide").Value
            ModificationRec(Counter).Fcst = rsModification("Fcst").Value
            Counter = Counter + 1
            rsModification.MoveNext
        Loop
    End If
    
    For Counter = TotalRec + 1 To TotalRec + 3
        ModificationRec(Counter).StartDate = EndDate + 1
        EndDate = GetNextStartDate(EndDate, EndDate, m_Interval)
        ModificationRec(Counter).EndDate = EndDate
        ModificationRec(Counter).QtyAdj = 0
        ModificationRec(Counter).QtyAdjOverRide = 0
        ModificationRec(Counter).QtyAdjPct = 0
        ModificationRec(Counter).AdjOverRide = 0
        ModificationRec(Counter).MasterQtyAdj = 0
        ModificationRec(Counter).MasterQtyAdjOverRide = 0
        ModificationRec(Counter).MasterQtyAdjPct = 0
        ModificationRec(Counter).MasterAdjOverRide = 0
        ModificationRec(Counter).Fcst = 0
    Next
    
    PopulateModificationRcdQty = True
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.PopulateModificationRcdQty)"
End Function
Private Sub SetPeriodBounds()
On Error GoTo ErrorHandler

Dim i As Integer   ' column index
Dim IMax As Integer


Dim PdEndDate As Date
Dim PdStartDate As Date
Dim StartPd As Integer
Dim EndPd As Integer


'NbrPds_Hist = txtFcstPeriods_Historical.Text
'NbrPds_Future = txtFcstPeriods_Future.Text
'FcstStartDate = txtFcstStartDate.Text
If FcstRolling = False Then
    DPRollingFcstStartDate = GetRollingFcstStartDate(FcstStartDate, FcstStartDate, AIMAdvCalendar, m_Interval)
    DpHistoricalStartDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, NbrPds_Hist, AIMAdvCalendar, m_Interval)
    DpFutureEndDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, -NbrPds_Future, AIMAdvCalendar, m_Interval) - 1
    PdStartDate = DpHistoricalStartDate
    PdEndDate = DpHistoricalStartDate
Else
    DPRollingFcstStartDate = GetRollingFcstStartDate(Now(), FcstStartDate, AIMAdvCalendar, m_Interval)
    DpHistoricalStartDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, NbrPds_Hist, AIMAdvCalendar, m_Interval)
    DpFutureEndDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, -NbrPds_Future, AIMAdvCalendar, m_Interval) - 1
    PdStartDate = DpHistoricalStartDate
    PdEndDate = DpHistoricalStartDate
End If


ReDim FcstPdDates(1 To NbrPds_Hist + NbrPds_Future + 1)

For i = 1 To NbrPds_Hist + NbrPds_Future + 1
If i = 1 Then
         FcstPdDates(1) = DpHistoricalStartDate
     ElseIf i = 2 And m_Interval <> NewFcstInterval And NewFcstInterval = int_months Then
        FcstPdDates(2) = GetNextRollingFcstStartDate(DpHistoricalStartDate, AIMAdvCalendar, NewFcstInterval)
        PdEndDate = FcstPdDates(2)
        PdStartDate = PdEndDate
    Else
          PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, NewFcstInterval)
         If NewFcstInterval <> int_months Or NewFcstInterval <> int_Quarters Then
             StartPd = AIMAdvCalendar.GetPeriod(PdEndDate)
             EndPd = AIMAdvCalendar.GetPeriod(PdEndDate - 7)
             If StartPd = 52 And EndPd = 52 Then PdEndDate = AIMAdvCalendar.GetEndDate(PdEndDate) + 1
         End If
          If PdEndDate >= DpFutureEndDate Then
                FcstPdDates(i) = DpFutureEndDate + 1
                IMax = i
                Exit For
            Else
            FcstPdDates(i) = PdEndDate
            End If
        
         PdStartDate = PdEndDate
         IMax = i
    End If
Next

ReDim Preserve FcstPdDates(1 To IMax)
NewTotalPeriods = UBound(FcstPdDates())
If FcstRolling = False Then
    'Change this date since the dprollingfcststartdate may be a date that is less then current pd startdate
    'so it will recalulate the historical periods fcst
    DPRollingFcstStartDate = Format(GetRollingFcstStartDate(Now(), FcstStartDate, AIMAdvCalendar, NewFcstInterval), gDateFormat)
Else
End If

Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(SetPeriodBounds)"
End Sub







Public Function GenerateNetRqmts() As Integer
On Error GoTo ErrorHandler

    Dim AccumFcst As Double
    Dim Adj As Double
    Dim MasterAdj As Double
    Dim Pd As Integer
    Dim RevPtr As Integer
    Dim SOQ As Double
    Dim w_AvailQty As Double
    Dim w_StkDate As Date
    Dim Price As Double
    Dim Cost As Double
    Dim SysFcst As Double
    Dim AdjSysFcst As Double
    Price = 0
    Cost = 0
    ActDate = Date
    'Osama - start Calculate Net Req from the ActDate
    If StkDate < ActDate - Accum_Lt Then
        StkDate = ActDate - Accum_Lt
    End If
        
    'Calculate the Burn (Demand Between the Stock Date and the Forecast Start Date)
    If StkDate < FcstStartDate Then
    'Use stkdate in place of fcststartdate so make m_burn =0
    'm_Burn = Forecast(StkDate, (FcstStartdate - 1), Adj)
        m_Burn = 0
    Else
        m_Burn = 0
        w_StkDate = FcstStartDate
        
    End If
    
    'Calculate the available inventory
    AvailQty = Oh + Oo - ComStk - BkOrder - BkComStk - m_Burn
    
    'Build the Review Dates
    m_NbrReviewDays = InitReviewDays()
    
    'Check for no review days
    If m_NbrReviewDays = 0 Then
        Exit Function
    End If
    
    'Key data elements for Net Requirements
    '
    'm_Fcst(1 To 106) As Double         'Forecast Results by Period
    'm_PMAdj(1 To 52) As Double         'Promotional Adjustments
    'm_PlannedRcpts(1 To 106) As Long   'Planned Purchase Order Receipts
    'AvailQty                           'Available Inventory
    'Burn                               'Forecast demand between the Stock Date and Forecast StartDate
    'EstOnOrder                         'Estimated Open Orders (Orders Generated between the StockDate and Forecast Start Date
    'Estimate Planned Receipts between the Stock Date and the Forecast End Date
    'For RevPtr = LBound(m_ReviewDays) To m_NbrReviewDays - 1
    For RevPtr = LBound(m_ReviewDays) To m_NbrReviewDays
        'Update the Accumulate Forecast
        If RevPtr = 0 Then
            'Use stkdate in place of fcststartdate
            'Osama check the ActDate
            If ActDate > StkDate Then
                AccumFcst = AccumFcst + Forecast(StkDate, m_ReviewDays(RevPtr), Adj, SysFcst, AdjSysFcst, MasterAdj)
            End If
'            AccumFcst = AccumFcst + Forecast(FcstStartdate, m_ReviewDays(RevPtr), Adj)
        Else
            'Osama check the ActDate
            If ActDate <= m_ReviewDays(RevPtr - 1) Then
                'Diffence of code between 4.2 and 4.4
                'AccumFcst = AccumFcst + Forecast(m_ReviewDays(RevPtr - 1), m_ReviewDays(RevPtr) - 1, Adj)
                AccumFcst = AccumFcst + Round(Forecast(m_ReviewDays(RevPtr - 1), m_ReviewDays(RevPtr) - 1, Adj, SysFcst, AdjSysFcst, MasterAdj), 0)
            End If
        End If
        
        'Determine the period associated with the review day
        Pd = GetFcstPd(m_ReviewDays(RevPtr))
        If Pd = 0 Then Pd = 1
        'Update Order Policies *****
        OrderPolicyUpdate m_ReviewDays(RevPtr), False

        SOQ = 0

        'Update inventory availability ******
        w_AvailQty = AvailQty - AccumFcst
    
        'Test the Order Point
        If w_AvailQty + m_EstOnOrder <= m_OrderPt Then
            SOQ = m_OrderPt - (w_AvailQty + m_EstOnOrder) + m_OrderQty

            'Pack round the planned requirement
            'Osama And Kevin, only use this function when SOQ > 0
            If SOQ <> 0 Then
                SOQ = PackRound(CDbl(SOQ), UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
            End If
            'Increment the Estimated Order Quantity
            m_EstOnOrder = m_EstOnOrder + SOQ
            
            'Update the Net Requirements Array
            If m_ReviewDays(RevPtr) >= FcstStartDate And m_ReviewDays(RevPtr) <= FcstEndDate Then
                GetCurrentPriceCost m_ReviewDays(RevPtr), Price, Cost
                'AIMAdvFcstMod.GetPrice m_ReviewDays(RevPtr), m_ReviewDays(RevPtr), LcId, Item,Price , Cost
                Price = Price * SOQ
                Cost = Cost * SOQ
                m_xTotalPrice = m_xTotalPrice + Price
                m_xTotalCost = m_xTotalCost + Cost
                m_xPriceAdj(Pd) = m_xPriceAdj(Pd) + Price
                m_xCostAdj(Pd) = m_xCostAdj(Pd) + Cost
                m_NetRqmts(Pd) = m_NetRqmts(Pd) + SOQ
                m_xTotalFcst = m_xTotalFcst + SOQ
                Price = 0
            End If
            
            'Update the Planned Receipts Array
            If m_ReviewDays(RevPtr) + Accum_Lt >= FcstStartDate And m_ReviewDays(RevPtr) + Accum_Lt <= FcstEndDate Then
                Pd = GetFcstPd(m_ReviewDays(RevPtr) + Accum_Lt)
                m_PlannedRcpts(Pd) = m_PlannedRcpts(Pd) + SOQ
            End If
        End If
    Next RevPtr

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GenerateNetRqmts)"
End Function

Public Function GenerateModNetRqmtsForHistPds() As Integer
On Error GoTo ErrorHandler
Dim Pd As Integer
Dim NetReqmts As Double
Dim AdjNetReqmts As Double
    For Pd = LBound(m_PdDate) To m_NbrPeriods - 1
        If m_PdDate(Pd + 1) - 1 >= DPRollingFcstStartDate Then
            HistNetReqAndAdjNetReqBetweenDates m_PdDate(Pd), DPRollingFcstStartDate, NetReqmts, AdjNetReqmts
            'HistFcstAndAdjBetweenDates m_PdDate(Pd), DPRollingFcstStartDate, NetReqmts, AdjNetReqmts
             m_NetRqmts(Pd) = NetReqmts
             m_AdjNetRqmts(Pd) = AdjNetReqmts
            Exit For
        Else
            HistNetReqAndAdjNetReqBetweenDates m_PdDate(Pd), m_PdDate(Pd + 1) - 1, NetReqmts, AdjNetReqmts
            'HistFcstAndAdjBetweenDates m_PdDate(Pd), m_PdDate(Pd + 1) - 1, NetReqmts, AdjNetReqmts
            m_NetRqmts(Pd) = NetReqmts
            m_AdjNetRqmts(Pd) = AdjNetReqmts
        End If
    Next Pd
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GenerateModNetRqmtsForHistPds)"
End Function
Public Function GenerateModNetRqmts() As Integer
On Error GoTo ErrorHandler

    Dim AccumFcst As Double
    Dim AdjAccumFcst As Double
    Dim Adj As Double
    Dim MasterAdj As Double
    Dim Pd As Integer
    Dim RevPtr As Integer
    Dim SOQ As Double
    Dim AdjSOQ As Double
    Dim w_AvailQty As Double
    Dim w_AdjAvailQty As Double
    Dim w_StkDate As Date
    Dim SOQPrice As Double
    Dim SOQCost As Double
    Dim Price As Double
    Dim Cost As Double
    Dim AdjSOQPrice As Double
    Dim AdjSOQCost As Double
    Dim SysFcst As Double
    Dim AdjSysFcst As Double
    Dim MaxOrdQty As Double
    Dim AdjMaxOrdQty As Double
    Dim MaxDueDate As Date
    Dim XDockSOQ As Double
    Dim XDockAdjSOQ As Double
    GenerateModNetRqmtsForHistPds
    
    Price = 0
    Cost = 0
    m_EstOnOrder = 0
    m_AdjEstOnOrder = 0
    
    'Calculate the Burn (Demand Between the Stock Date and the Forecast Start Date)
    If StkDate < Now() And Accum_Lt > 0 Then
        'Forecast StkDate, Now(), Adj, SysFcst, AdjSysFcst, MasterAdj
        m_Burn = DateDiff("d", StkDate, Now()) * Fcst_Lt / Accum_Lt
       'm_Burn = AdjSysFcst
    Else
        m_Burn = 0
        w_StkDate = DPRollingFcstStartDate
    End If
   
    'Calculate the available inventory
    AvailQty = Oh + Oo - ComStk - BkOrder - BkComStk - m_Burn
    AdjAvailQty = Oh + Oo - ComStk - BkOrder - BkComStk - m_Burn
    If AllowNegativeAvailQty = "N" Then
        If AvailQty < 0 Then AvailQty = 0
        If AdjAvailQty < 0 Then AdjAvailQty = 0
    End If
    m_InitialStock = AvailQty
    If DropShip_XDock = "Y" Then
        XDockSOQ = ComStk + BkOrder + BkComStk
        XDockAdjSOQ = XDockSOQ
    End If
    
    'Build the Review Dates
    m_NbrReviewDays = InitReviewDays()
    
    'Check for no review days
    If m_NbrReviewDays = 0 Then
        Exit Function
    End If
    
    'Key data elements for Net Requirements
    '
    'm_Fcst(1 To 106) As Double         'Forecast Results by Period
    'm_PMAdj(1 To 52) As Double         'Promotional Adjustments
    'm_PlannedRcpts(1 To 106) As Long   'Planned Purchase Order Receipts
    'AvailQty                           'Available Inventory
    'Burn                               'Forecast demand between the Stock Date and Forecast StartDate
    'EstOnOrder                         'Estimated Open Orders (Orders Generated between the StockDate and Forecast Start Date
    
    'Estimate Planned Receipts between the Stock Date and the Forecast End Date
    'For RevPtr = LBound(m_ReviewDays) To m_NbrReviewDays - 1
      For RevPtr = LBound(m_ReviewDays) To m_NbrReviewDays
        'Update the Accumulate Forecast
        If RevPtr = 0 Then
                'Calculate the forecast
'                Forecast DPRollingFcstStartDate, m_ReviewDays(RevPtr), Adj, SysFcst, AdjSysFcst, MasterAdj
                'AccumFcst = AccumFcst + SysFcst
                'AdjAccumFcst = AdjAccumFcst + AdjSysFcst
                If DropShip_XDock = "Y" Then
                    Forecast m_ReviewDays(RevPtr), m_ReviewDays(RevPtr + 1) - 1, Adj, SysFcst, AdjSysFcst, MasterAdj
                    XDockSOQ = XDockSOQ + Round(SysFcst)
                    XDockAdjSOQ = XDockAdjSOQ + Round(AdjSysFcst)
                Else
                    AccumFcst = 0
                    AdjAccumFcst = 0
                End If
        Else
                If DropShip_XDock = "Y" Then
                    Forecast m_ReviewDays(RevPtr), m_ReviewDays(RevPtr + 1) - 1, Adj, SysFcst, AdjSysFcst, MasterAdj
                    XDockSOQ = Round(SysFcst)
                    XDockAdjSOQ = Round(AdjSysFcst)
                Else
                    Forecast m_ReviewDays(RevPtr - 1), m_ReviewDays(RevPtr) - 1, Adj, SysFcst, AdjSysFcst, MasterAdj
                    AccumFcst = AccumFcst + Round(SysFcst)
                    AdjAccumFcst = AdjAccumFcst + Round(AdjSysFcst)
                End If
        End If
    If DropShip_XDock <> "Y" Then
'        'Determine the period associated with the review day
'        Pd = GetFcstPd(m_ReviewDays(RevPtr))
        
        'Update Order Policies *****
        OrderPolicyUpdate m_ReviewDays(RevPtr), False

        SOQ = 0
        AdjSOQ = 0
        
        'Update inventory availability ******
        w_AvailQty = AvailQty - AccumFcst
        'If w_AvailQty <= 0 Then w_AvailQty = 0
        
        w_AdjAvailQty = AdjAvailQty - AdjAccumFcst
        'If w_AdjAvailQty <= 0 Then w_AdjAvailQty = 0
        
        'Test the Order Point
        If w_AvailQty + m_EstOnOrder <= m_OrderPt Then
            'Do not buy if discontinued item is approching it's discontiuation date
            If (ItStat = "D" And InActDate <= DateAdd("d", Accum_Lt, Now())) Then
                SOQ = 0
            Else
                If BuyStrat = "M" Then
                    SOQ = m_OrderQty + m_OrderPt - (w_AvailQty + m_EstOnOrder)
                ElseIf BuyStrat = "O" Then
                    SOQ = m_OrderQty + m_FcstLT + m_SafetyStock + 0.5 * m_FcstRT - (w_AvailQty + m_EstOnOrder)
                ElseIf BuyStrat = "T" Then
                    SOQ = m_OrderQty + m_OrderPt - (w_AvailQty + m_EstOnOrder)
                Else
                    SOQ = m_OrderQty + m_FcstLT + m_SafetyStock + 0.5 * m_FcstRT - (w_AvailQty + m_EstOnOrder)
                End If
                'Added code for discontinued items same as in ordergeneration process
                'Pack round the planned requirement
                If ItStat = "D" Then
                 If Now() > InActDate Then
                    MaxOrdQty = 0
                    SOQ = 0
                 'ElseIf InActDate < m_ReviewDays(RevPtr) Then
                 Else
                    If Accum_Lt > 0 Then
                        MaxOrdQty = Abs((DateDiff("d", Now(), InActDate) * m_FcstLT / Accum_Lt)) - (w_AvailQty + m_EstOnOrder)
                    Else
                        MaxOrdQty = 0
                    End If
                    If MaxOrdQty < 0 Then MaxOrdQty = 0
                    If SOQ > MaxOrdQty Then SOQ = MaxOrdQty
                    If SOQ < ConvFactor Then SOQ = 0
                 End If
                End If
                
                If SOQ <> 0 Then
                    If ItStat = "D" Then
                        SOQ = PackRound(CDbl(SOQ), UOM, ConvFactor, BuyingUOM, "D", IMin, IMax)
                    Else
                        SOQ = PackRound(CDbl(SOQ), UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
                    End If
                End If
            End If 'discont item
            
            'Increment the Estimated Order Quantity
            m_EstOnOrder = m_EstOnOrder + SOQ
                        
            'Update the Net Requirements Array
            If m_ReviewDays(RevPtr) >= DPRollingFcstStartDate - 1 And m_ReviewDays(RevPtr) <= FcstEndDate Then
                GetCurrentPriceCost m_ReviewDays(RevPtr), Price, Cost
                'Determine the period associated with the review day
                Pd = GetFcstPd(m_ReviewDays(RevPtr))
                If Pd = 0 Then Pd = 1
                
                SOQPrice = Price * SOQ
                SOQCost = Cost * SOQ
                m_xTotalPrice = m_xTotalPrice + SOQPrice
                m_xTotalCost = m_xTotalCost + SOQCost
                m_xPriceAdj(Pd) = m_xPriceAdj(Pd) + SOQPrice
                m_xCostAdj(Pd) = m_xCostAdj(Pd) + SOQCost
                m_NetRqmts(Pd) = m_NetRqmts(Pd) + SOQ
                m_xTotalFcst = m_xTotalFcst + SOQ
                Price = 0
                Cost = 0
            End If
            'Update the Planned Receipts Array
            If m_ReviewDays(RevPtr) + Accum_Lt >= DPRollingFcstStartDate And m_ReviewDays(RevPtr) + Accum_Lt <= FcstEndDate Then
                Pd = GetFcstPd(m_ReviewDays(RevPtr) + Accum_Lt)
                m_PlannedRcpts(Pd) = m_PlannedRcpts(Pd) + SOQ
            End If
        End If
       
        'Test the Order Point
        If w_AdjAvailQty + m_AdjEstOnOrder <= m_AdjOrderPt Then
            'Do not buy if discontinued item is approching it's discontiuation date
            If (ItStat = "D" And InActDate <= DateAdd("d", Accum_Lt, Now())) Then
                AdjSOQ = 0
            Else
                If BuyStrat = "M" Then
                    AdjSOQ = m_AdjOrderQty + m_AdjOrderPt - (w_AdjAvailQty + m_AdjEstOnOrder)
                ElseIf BuyStrat = "O" Then
                    AdjSOQ = m_AdjOrderQty + m_AdjFcstLT + m_AdjSafetyStock + 0.5 * m_AdjFcstRT - (w_AdjAvailQty + m_AdjEstOnOrder)
                ElseIf BuyStrat = "T" Then
                    AdjSOQ = m_AdjOrderQty + m_AdjOrderPt - (w_AdjAvailQty + m_AdjEstOnOrder)
                Else
                    AdjSOQ = m_AdjOrderQty + m_AdjFcstLT + m_AdjSafetyStock + 0.5 * m_AdjFcstRT - (w_AdjAvailQty + m_AdjEstOnOrder)
                End If
                'Added code for discontinued items same as in ordergeneration process
                'Pack round the planned requirement
                If ItStat = "D" Then
                 If Now() > InActDate Then
                    AdjMaxOrdQty = 0
                    AdjSOQ = 0
                 'ElseIf InActDate < m_ReviewDays(RevPtr) Then
                 Else
                    If Accum_Lt > 0 Then
                        AdjMaxOrdQty = Abs((DateDiff("d", Now(), InActDate) * m_AdjFcstLT / Accum_Lt)) - (w_AdjAvailQty + m_AdjEstOnOrder)
                    Else
                        AdjMaxOrdQty = 0
                    End If
                    If AdjMaxOrdQty < 0 Then AdjMaxOrdQty = 0
                    If AdjSOQ > AdjMaxOrdQty Then AdjSOQ = AdjMaxOrdQty
                    If AdjSOQ < ConvFactor Then AdjSOQ = 0
                 End If
                End If
                'Pack round the planned requirement
                'Osama And Kevin, only use this function when SOQ > 0
                If ItStat = "D" Then
                        AdjSOQ = PackRound(CDbl(AdjSOQ), UOM, ConvFactor, BuyingUOM, "D", IMin, IMax)
                    Else
                        AdjSOQ = PackRound(CDbl(AdjSOQ), UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
                End If
            End If  'Discontined item If
            'Increment the Estimated Order Quantity
            m_AdjEstOnOrder = m_AdjEstOnOrder + AdjSOQ
            
            'Update the Net Requirements Array
            If m_ReviewDays(RevPtr) >= DPRollingFcstStartDate And m_ReviewDays(RevPtr) <= FcstEndDate Then
                GetCurrentPriceCost m_ReviewDays(RevPtr), Price, Cost
                'Determine the period associated with the review day
                Pd = GetFcstPd(m_ReviewDays(RevPtr))
                If Pd = 0 Then Pd = 1
                AdjSOQPrice = Price * AdjSOQ
                AdjSOQCost = Cost * AdjSOQ
                m_xAdjTotalPrice = m_xAdjTotalPrice + AdjSOQPrice
                m_xAdjTotalCost = m_xAdjTotalCost + AdjSOQCost
                m_xAdjPriceAdj(Pd) = m_xAdjPriceAdj(Pd) + AdjSOQPrice
                m_xAdjCostAdj(Pd) = m_xAdjCostAdj(Pd) + AdjSOQCost
                m_AdjNetRqmts(Pd) = m_AdjNetRqmts(Pd) + AdjSOQ
                m_xAdjTotalFcst = m_xAdjTotalFcst + AdjSOQ
                Price = 0
                Cost = 0
            End If
            'Update the Planned Receipts Array
            If m_ReviewDays(RevPtr) + Accum_Lt >= DPRollingFcstStartDate And m_ReviewDays(RevPtr) + Accum_Lt <= FcstEndDate Then
                Pd = GetFcstPd(m_ReviewDays(RevPtr) + Accum_Lt)
                m_AdjPlannedRcpts(Pd) = m_AdjPlannedRcpts(Pd) + AdjSOQ
            End If
        End If
        Else            ' xdoc item
            If (ItStat = "D" And InActDate <= DateAdd("d", Accum_Lt, Now())) Then
                XDockSOQ = 0
                XDockAdjSOQ = 0
            End If
            If m_ReviewDays(RevPtr) >= DPRollingFcstStartDate - 1 And m_ReviewDays(RevPtr) <= FcstEndDate Then
                GetCurrentPriceCost m_ReviewDays(RevPtr), Price, Cost
                'Determine the period associated with the review day
                Pd = GetFcstPd(m_ReviewDays(RevPtr))
                If Pd = 0 Then Pd = 1
                    If XDockSOQ <> 0 Then
                        If ItStat = "D" Then
                            XDockSOQ = PackRound(CDbl(XDockSOQ), UOM, ConvFactor, BuyingUOM, "D", IMin, IMax)
                        Else
                            XDockSOQ = PackRound(CDbl(XDockSOQ), UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
                        End If
                    End If
                    SOQPrice = Price * XDockSOQ
                    SOQCost = Cost * XDockSOQ
                    m_xPriceAdj(Pd) = SOQPrice
                    m_xCostAdj(Pd) = SOQCost
                    m_NetRqmts(Pd) = XDockSOQ
                    Price = 0
                    Cost = 0
                'Update the Planned Receipts Array
                If m_ReviewDays(RevPtr) + Accum_Lt >= DPRollingFcstStartDate And m_ReviewDays(RevPtr) + Accum_Lt <= FcstEndDate Then
                    Pd = GetFcstPd(m_ReviewDays(RevPtr) + Accum_Lt)
                    m_PlannedRcpts(Pd) = XDockSOQ
                End If
              
              'For adj forecast
               'Determine the period associated with the review day
                Pd = GetFcstPd(m_ReviewDays(RevPtr))
                If Pd = 0 Then Pd = 1
                If XDockSOQ <> 0 Then
                    If ItStat = "D" Then
                        XDockAdjSOQ = PackRound(CDbl(XDockAdjSOQ), UOM, ConvFactor, BuyingUOM, "D", IMin, IMax)
                    Else
                        XDockAdjSOQ = PackRound(CDbl(XDockAdjSOQ), UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
                    End If
                End If
                AdjSOQPrice = Price * XDockAdjSOQ
                AdjSOQCost = Cost * XDockSOQ
                m_xAdjPriceAdj(Pd) = AdjSOQPrice
                
                m_xAdjCostAdj(Pd) = AdjSOQCost
                m_AdjNetRqmts(Pd) = XDockAdjSOQ
                Price = 0
                Cost = 0
                'Update the Planned Receipts Array
                If m_ReviewDays(RevPtr) + Accum_Lt >= DPRollingFcstStartDate And m_ReviewDays(RevPtr) + Accum_Lt <= FcstEndDate Then
                    Pd = GetFcstPd(m_ReviewDays(RevPtr) + Accum_Lt)
                    m_AdjPlannedRcpts(Pd) = XDockAdjSOQ
                End If
              End If
        End If
    Next RevPtr
    'Perform Carry Forward Rounding
    If CarryFwdRoundingOption Then
        CarryForwardRounding m_NetRqmts
        CarryForwardRounding m_AdjNetRqmts
        CarryForwardRounding m_PlannedRcpts
        CarryForwardRounding m_AdjPlannedRcpts
    End If
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GenerateModNetRqmts)"
End Function


Private Function AdjBetweenDates( _
    PdStartDate As Date, _
    PdEndDate As Date, _
    Demand As Double, _
    ByRef AdjQty As Double, _
    PartailWeekPct As Double _
) As Integer
On Error GoTo ErrorHandler
    'PartailWeekPct is used only for sandbox in which the fcstperiod is not weekly.
    'The value send is 1 or strartweekpct or endweekpct
    Dim Counter As Long
    Dim Ratio As Double
    Dim Price As Double
    Dim Cost As Double
    Dim Numerator As Double
    Dim Denominator As Double
    Dim EnteredLoop As Boolean
    Dim NewNumerator As Double
    Dim NewDenominator As Double
    Dim NewRatio As Double
    'm_Interval is the original fcst interval
    'if the origial fcst interval is >1  i.e any thing greater then weekly this can happen only
    'for sand box forecast then use the modified calculations
   
    AdjQty = 0#
    Ratio = 0#
    NetReqAdj = 0#
    AdjRcd.QtyAdj = 0
    AdjRcd.QtyAdjPct = 0
    'AdjRcd.AdjOverRide = -999999999
    AdjRcd.AdjOverRide = 0  'false
    AdjOverRide = False
    MasterAdjOverRide = False
    EnteredLoop = False
    If DataYes = False Then Exit Function
    
    
    For Counter = 1 To UBound(ModificationRec())
        If ModificationRec(Counter).StartDate > PdEndDate Then Exit For
        If ModificationRec(Counter).EndDate >= PdStartDate _
        And ModificationRec(Counter).StartDate <= PdEndDate _
        Then
            EnteredLoop = True
            If ModificationRec(Counter).StartDate <= PdStartDate _
            And ModificationRec(Counter).EndDate >= PdStartDate _
            Then
                If PdEndDate < ModificationRec(Counter).EndDate Then
                    Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                    
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                    
                    NewDenominator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    NewNumerator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, PdEndDate)
                    If NewDenominator = 0 Then NewDenominator = 1
                    NewRatio = NewNumerator / NewDenominator
                    If NewRatio > 1 Then NewRatio = 1#
                Else
                     Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, ModificationRec(Counter).EndDate)
                     Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                    
                    NewDenominator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    NewNumerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, ModificationRec(Counter).EndDate)
                    If NewDenominator = 0 Then NewDenominator = 1
                    NewRatio = NewNumerator / NewDenominator
                    If NewRatio > 1 Then NewRatio = 1#
                    
                End If
            ElseIf ModificationRec(Counter).StartDate <= PdEndDate _
            And ModificationRec(Counter).EndDate >= PdEndDate _
            Then
                Numerator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, PdEndDate)
                Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                If Denominator = 0 Then Denominator = 1
                Ratio = Numerator / Denominator
                
                NewDenominator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                NewNumerator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, PdEndDate)
                If NewDenominator = 0 Then NewDenominator = 1
                NewRatio = NewNumerator / NewDenominator
                If NewRatio > 1 Then NewRatio = 1#
            Else
                Ratio = 1#
                NewRatio = 1#
            End If
            If ItStat = "O" Then
                
                'NetReqAdj = NetReqAdj + ModificationRec(Counter).QtyAdj
                AdjRcd.QtyAdj = ModificationRec(Counter).QtyAdj + ModificationRec(Counter).MasterQtyAdj
                AdjRcd.QtyAdjPct = ModificationRec(Counter).QtyAdjPct + ModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = ModificationRec(Counter).AdjOverRide
                'If ModificationRec(Counter).AdjOverRide = -999999999 Then
                If ModificationRec(Counter).AdjOverRide = 1 Then
                    AdjOverRide = True
                Else
                     AdjOverRide = False
                End If
                If ModificationRec(Counter).MasterAdjOverRide = 2 Then '  2 is override master
                    MasterAdjOverRide = False
                Else
                    If ModificationRec(Counter).MasterAdjOverRide = 0 Then
                        MasterAdjOverRide = False
                    Else
                         MasterAdjOverRide = True
                    End If
                End If
                If m_Interval > 1 Then 'greater than weekly forecast
                    If MasterAdjOverRide = True And AdjOverRide = True Then
                        AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide
                    ElseIf MasterAdjOverRide = True Then
                        'AdjQty = AdjQty + AdjRcd.AdjOverRide
                        AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide
                    ElseIf AdjOverRide = True Then
                        AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide
                    Else
                        AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Demand * PartailWeekPct * NewRatio
                        AdjQty = AdjQty + AdjRcd.QtyAdj
                    End If
                Else
                    If MasterAdjOverRide = True And AdjOverRide = True Then
                        AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide
                    ElseIf MasterAdjOverRide = True Then
                        'AdjQty = AdjQty + AdjRcd.AdjOverRide
                        AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide
                    ElseIf AdjOverRide = True Then
                        AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide
                    Else
                        AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Demand
                        AdjQty = AdjQty + AdjRcd.QtyAdj
                    End If
                End If
            Else
                
                'NetReqAdj = NetReqAdj + ModificationRec(Counter).QtyAdj * Ratio
                AdjRcd.QtyAdj = (ModificationRec(Counter).QtyAdj + ModificationRec(Counter).MasterQtyAdj) * Ratio
                AdjRcd.QtyAdjPct = (ModificationRec(Counter).QtyAdjPct + ModificationRec(Counter).MasterQtyAdjPct)
                AdjRcd.AdjOverRide = ModificationRec(Counter).AdjOverRide * Ratio
                'If ModificationRec(Counter).AdjOverRide = -999999999 Then
                If ModificationRec(Counter).MasterAdjOverRide = 2 Then '  2 is override master
                    MasterAdjOverRide = False
                Else
                    If ModificationRec(Counter).MasterAdjOverRide = 0 Then
                        MasterAdjOverRide = False
                    Else
                         MasterAdjOverRide = True
                    End If
                End If
                
                     If ModificationRec(Counter).AdjOverRide = 1 Then
                        AdjOverRide = True
                    Else
                         AdjOverRide = False
                    End If
                If m_Interval > 1 Then 'greater than weekly forecast
                    If MasterAdjOverRide = True And AdjOverRide = True Then
                         AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide * Ratio
                    ElseIf MasterAdjOverRide = True Then
                        'AdjQty = AdjQty + AdjRcd.AdjOverRide
                        AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide * Ratio
                    ElseIf AdjOverRide = True Then
                        'AdjQty = AdjQty + AdjRcd.AdjOverRide
                        AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide * Ratio
                    Else
                        AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Demand * PartailWeekPct * NewRatio
                        AdjQty = AdjQty + AdjRcd.QtyAdj
                    End If
                Else
                    If MasterAdjOverRide = True And AdjOverRide = True Then
                         AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide * Ratio
                    ElseIf MasterAdjOverRide = True Then
                        'AdjQty = AdjQty + AdjRcd.AdjOverRide
                        AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide * Ratio
                    ElseIf AdjOverRide = True Then
                        'AdjQty = AdjQty + AdjRcd.AdjOverRide
                        AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide * Ratio
                    Else
                        AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Ratio * Demand
                        AdjQty = AdjQty + AdjRcd.QtyAdj
                    End If
                End If
            End If
        End If
        Ratio = 0#
    Next
    If EnteredLoop = True Then
        AdjLoopEntered = True
    Else
        AdjLoopEntered = False
    End If
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.AdjBetweenDates)"
End Function
Private Function MasterAdjBetweenDates( _
    PdStartDate As Date, _
    PdEndDate As Date, _
    Demand As Double, _
    ByRef AdjQty As Double _
) As Integer
On Error GoTo ErrorHandler
    Dim Counter As Long
    Dim Ratio As Double
    Dim Price As Double
    Dim Cost As Double
    Dim Numerator As Double
    Dim Denominator As Double
    Dim EnteredLoop As Boolean
    AdjQty = 0#
    Ratio = 0#
    NetReqAdj = 0#
    AdjRcd.QtyAdj = 0
    AdjRcd.QtyAdjPct = 0
    'AdjRcd.AdjOverRide = -999999999
    AdjRcd.AdjOverRide = 0  'false
    MasterAdjOverRide = False
    EnteredLoop = False
    If MasterDataYes = False Then Exit Function
    
    
    For Counter = 1 To UBound(MasterModificationRec())
        If MasterModificationRec(Counter).StartDate > PdEndDate Then Exit For
        If MasterModificationRec(Counter).EndDate >= PdStartDate _
        And MasterModificationRec(Counter).StartDate <= PdEndDate _
        Then
            EnteredLoop = True
            If MasterModificationRec(Counter).StartDate <= PdStartDate _
            And MasterModificationRec(Counter).EndDate >= PdStartDate _
            Then
                If PdEndDate < MasterModificationRec(Counter).EndDate Then
                    Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    Denominator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, MasterModificationRec(Counter).EndDate)
                    
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                Else
                     Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, MasterModificationRec(Counter).EndDate)
                     Denominator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, MasterModificationRec(Counter).EndDate)
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                End If
            ElseIf MasterModificationRec(Counter).StartDate <= PdEndDate _
            And MasterModificationRec(Counter).EndDate >= PdEndDate _
            Then
                Numerator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, PdEndDate)
                Denominator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, MasterModificationRec(Counter).EndDate)
                If Denominator = 0 Then Denominator = 1
                Ratio = Numerator / Denominator
            Else
                Ratio = 1#
            End If
            If ItStat = "O" Then
                
                'NetReqAdj = NetReqAdj + MasterModificationRec(Counter).QtyAdj
                AdjRcd.QtyAdj = MasterModificationRec(Counter).MasterQtyAdj
                AdjRcd.QtyAdjPct = MasterModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = MasterModificationRec(Counter).MasterAdjOverRide
                'If MasterModificationRec(Counter).AdjOverRide = -999999999 Then
                If MasterModificationRec(Counter).MasterAdjOverRide = 0 Then
                    MasterAdjOverRide = False
                Else
                     MasterAdjOverRide = True
                End If
                
                If MasterAdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    AdjQty = AdjQty + MasterModificationRec(Counter).MasterQtyAdjOverRide
                    
                Else
                    AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Demand
                    AdjQty = AdjQty + AdjRcd.QtyAdj
                End If
            Else
                
                'NetReqAdj = NetReqAdj + MasterModificationRec(Counter).QtyAdj * Ratio
                 AdjRcd.QtyAdj = MasterModificationRec(Counter).MasterQtyAdj * Ratio
                AdjRcd.QtyAdjPct = MasterModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = MasterModificationRec(Counter).MasterAdjOverRide * Ratio
                'If MasterModificationRec(Counter).AdjOverRide = -999999999 Then
                If MasterModificationRec(Counter).MasterAdjOverRide = 0 Then
                    MasterAdjOverRide = False
                Else
                     MasterAdjOverRide = True
                End If
                If MasterAdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    AdjQty = AdjQty + MasterModificationRec(Counter).MasterQtyAdjOverRide * Ratio
                Else
                    'AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Ratio * Demand
                    AdjQty = AdjQty + Demand + (AdjRcd.QtyAdjPct / 100#) * Ratio * Demand
                    AdjQty = AdjQty + AdjRcd.QtyAdj
                End If
            End If
        End If
        Ratio = 0#
    Next
    If EnteredLoop = True Then
        MasterAdjLoopEntered = True
    Else
        MasterAdjLoopEntered = False
    End If
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.MasterAdjBetweenDates)"
End Function

Private Function HistFcstAndMasterAdjBetweenDates( _
    PdStartDate As Date, _
    PdEndDate As Date, _
    ByRef Demand As Double, _
    ByRef AdjQty As Double _
) As Integer
On Error GoTo ErrorHandler
    Dim Counter As Long
    Dim Ratio As Double
    Dim Price As Double
    Dim Cost As Double
    Dim Numerator As Double
    Dim Denominator As Double
    Demand = 0#
    AdjQty = 0#
    Ratio = 0#
    NetReqAdj = 0#
    AdjRcd.QtyAdj = 0
    AdjRcd.QtyAdjPct = 0
    'AdjRcd.AdjOverRide = -999999999
    AdjRcd.AdjOverRide = 0  'false
    MasterAdjOverRide = False
    If MasterDataYes = False Then Exit Function
    
    
    For Counter = 1 To UBound(MasterModificationRec())
        If MasterModificationRec(Counter).StartDate > PdEndDate Then Exit For
        If MasterModificationRec(Counter).EndDate >= PdStartDate _
        And MasterModificationRec(Counter).StartDate <= PdEndDate _
        Then
            If MasterModificationRec(Counter).StartDate <= PdStartDate _
            And MasterModificationRec(Counter).EndDate >= PdStartDate _
            Then
                If PdEndDate < MasterModificationRec(Counter).EndDate Then
                    Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    Denominator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, MasterModificationRec(Counter).EndDate)
                    
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                Else
                     Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, MasterModificationRec(Counter).EndDate)
                     Denominator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, MasterModificationRec(Counter).EndDate)
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                End If
            ElseIf MasterModificationRec(Counter).StartDate <= PdEndDate _
            And MasterModificationRec(Counter).EndDate >= PdEndDate _
            Then
                Numerator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, PdEndDate)
                Denominator = AIMAdvCalendar.GetWorkingDays(MasterModificationRec(Counter).StartDate, MasterModificationRec(Counter).EndDate)
                If Denominator = 0 Then Denominator = 1
                Ratio = Numerator / Denominator
            Else
                Ratio = 1#
            End If
            If ItStat = "O" Then
                
                'NetReqAdj = NetReqAdj + MasterModificationRec(Counter).QtyAdj
                AdjRcd.QtyAdj = MasterModificationRec(Counter).MasterQtyAdj
                AdjRcd.QtyAdjPct = MasterModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = MasterModificationRec(Counter).MasterAdjOverRide
                'If MasterModificationRec(Counter).AdjOverRide = -999999999 Then
                
                If MasterModificationRec(Counter).MasterAdjOverRide = 0 Then
                    MasterAdjOverRide = False
                Else
                     MasterAdjOverRide = True
                End If
                 If MasterAdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    Demand = MasterModificationRec(Counter).Fcst
                    AdjQty = AdjQty + MasterModificationRec(Counter).MasterQtyAdjOverRide
                Else
                    Demand = MasterModificationRec(Counter).Fcst
                    AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Demand
                    AdjQty = AdjQty + AdjRcd.QtyAdj
                End If
            Else
                
                'NetReqAdj = NetReqAdj + MasterModificationRec(Counter).QtyAdj * Ratio
                 AdjRcd.QtyAdj = MasterModificationRec(Counter).MasterQtyAdj * Ratio
                AdjRcd.QtyAdjPct = MasterModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = MasterModificationRec(Counter).MasterAdjOverRide * Ratio
                'If MasterModificationRec(Counter).AdjOverRide = -999999999 Then
                'Demand = MasterModificationRec(Counter).Fcst
                 If MasterModificationRec(Counter).MasterAdjOverRide = 0 Then
                    MasterAdjOverRide = False
                Else
                     MasterAdjOverRide = True
                End If
                
                 If MasterAdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    Demand = Demand + MasterModificationRec(Counter).Fcst
                    AdjQty = AdjQty + MasterModificationRec(Counter).MasterQtyAdjOverRide * Ratio
                Else
                    Demand = Demand + MasterModificationRec(Counter).Fcst * Ratio
                    AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * MasterModificationRec(Counter).Fcst * Ratio
                    AdjQty = AdjQty + AdjRcd.QtyAdj
                End If
            End If
        End If
        Ratio = 0#
    Next

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.HistFcstAndMasterAdjBetweenDates)"
End Function
Private Function HistFcstAndAdjBetweenDates( _
    PdStartDate As Date, _
    PdEndDate As Date, _
    ByRef Demand As Double, _
    ByRef AdjQty As Double _
) As Integer
On Error GoTo ErrorHandler
    Dim Counter As Long
    Dim Ratio As Double
    Dim Price As Double
    Dim Cost As Double
    Dim Numerator As Double
    Dim Denominator As Double
    Demand = 0#
    AdjQty = 0#
    Ratio = 0#
    NetReqAdj = 0#
    AdjRcd.QtyAdj = 0
    AdjRcd.QtyAdjPct = 0
    'AdjRcd.AdjOverRide = -999999999
    AdjRcd.AdjOverRide = 0  'false
    AdjOverRide = False
    MasterAdjOverRide = False
    'EnteredLoop = False
    If DataYes = False Then Exit Function
    
    
    For Counter = 1 To UBound(ModificationRec())
        If ModificationRec(Counter).StartDate > PdEndDate Then Exit For
        If ModificationRec(Counter).EndDate >= PdStartDate _
        And ModificationRec(Counter).StartDate <= PdEndDate _
        Then
            'EnteredLoop = True
            If ModificationRec(Counter).StartDate <= PdStartDate _
            And ModificationRec(Counter).EndDate >= PdStartDate _
            Then
                If PdEndDate < ModificationRec(Counter).EndDate Then
                    Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, PdEndDate)
                    Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                    
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                Else
                     Numerator = AIMAdvCalendar.GetWorkingDays(PdStartDate, ModificationRec(Counter).EndDate)
                     Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                    If Denominator = 0 Then Denominator = 1
                    Ratio = Numerator / Denominator
                End If
            ElseIf ModificationRec(Counter).StartDate <= PdEndDate _
            And ModificationRec(Counter).EndDate >= PdEndDate _
            Then
                Numerator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, PdEndDate)
                Denominator = AIMAdvCalendar.GetWorkingDays(ModificationRec(Counter).StartDate, ModificationRec(Counter).EndDate)
                If Denominator = 0 Then Denominator = 1
                Ratio = Numerator / Denominator
            Else
                Ratio = 1#
            End If
            If ItStat = "O" Then
                
                'NetReqAdj = NetReqAdj + ModificationRec(Counter).QtyAdj
                AdjRcd.QtyAdj = ModificationRec(Counter).QtyAdj + ModificationRec(Counter).MasterQtyAdj
                AdjRcd.QtyAdjPct = ModificationRec(Counter).QtyAdjPct + ModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = ModificationRec(Counter).AdjOverRide
                'If ModificationRec(Counter).AdjOverRide = -999999999 Then
                If ModificationRec(Counter).AdjOverRide = 1 Then
                    AdjOverRide = True
                Else
                     AdjOverRide = False
                End If
                If ModificationRec(Counter).MasterAdjOverRide = 2 Then '  2 is override master
                    MasterAdjOverRide = False
                Else
                    If ModificationRec(Counter).MasterAdjOverRide = 0 Then
                        MasterAdjOverRide = False
                    Else
                         MasterAdjOverRide = True
                    End If
                End If
                 If MasterAdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    Demand = ModificationRec(Counter).Fcst
                    AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide
                ElseIf AdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    Demand = ModificationRec(Counter).Fcst
                    AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide
                Else
                    Demand = ModificationRec(Counter).Fcst
                    AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * Demand
                    AdjQty = AdjQty + AdjRcd.QtyAdj
                End If
            Else
                
                'NetReqAdj = NetReqAdj + ModificationRec(Counter).QtyAdj * Ratio
                 AdjRcd.QtyAdj = (ModificationRec(Counter).QtyAdj + ModificationRec(Counter).MasterQtyAdj) * Ratio
                AdjRcd.QtyAdjPct = ModificationRec(Counter).QtyAdjPct + ModificationRec(Counter).MasterQtyAdjPct
                AdjRcd.AdjOverRide = ModificationRec(Counter).AdjOverRide * Ratio
                'If ModificationRec(Counter).AdjOverRide = -999999999 Then
                'Demand = ModificationRec(Counter).Fcst
                 If ModificationRec(Counter).AdjOverRide = 1 Then
                    AdjOverRide = True
                Else
                     AdjOverRide = False
                End If
                If ModificationRec(Counter).MasterAdjOverRide = 2 Then '  2 is override master
                    MasterAdjOverRide = False
                Else
                    If ModificationRec(Counter).MasterAdjOverRide = 0 Then
                        MasterAdjOverRide = False
                    Else
                         MasterAdjOverRide = True
                    End If
                End If
                 If MasterAdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    Demand = Demand + ModificationRec(Counter).Fcst
                    AdjQty = AdjQty + ModificationRec(Counter).MasterQtyAdjOverRide * Ratio
                ElseIf AdjOverRide = True Then
                    'AdjQty = AdjQty + AdjRcd.AdjOverRide
                    Demand = Demand + ModificationRec(Counter).Fcst
                    AdjQty = AdjQty + ModificationRec(Counter).QtyAdjOverRide * Ratio
                Else
                    Demand = Demand + ModificationRec(Counter).Fcst * Ratio
                    AdjQty = AdjQty + (1# + AdjRcd.QtyAdjPct / 100#) * ModificationRec(Counter).Fcst * Ratio
                    AdjQty = AdjQty + AdjRcd.QtyAdj
                End If
            End If
        End If
        Ratio = 0#
    Next

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.HistFcstAndAdjBetweenDates)"
End Function
''
''
''Else
''    Qty = Qty + BudgetOrForecast(counter).QtyFcst
''End If
''
''End If
''Next
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.NetReqAdjBetweenDates)"
'End Function
'
'
'**********************************************************************
' Function Name:    CalcSafetyStock(FcstLT, FcstDemand, MAE, LeadTime,
'                   ServiceLvl, MADExk)
' Description:      Calculates Safety Stock for an item based on the
'                   replenishment lead time, deseasonalized demand,
'                   Mean Absolute Deviation -- Safety Stock, and the
'                   desired Service Level.
' Parameters:       FcstLT              Forecast for Lead Time
'                   FcstDemand          Forecast for One Period
'                   MAE                 Mean Absolute Error
'                   LeadTime            Accumulative Lead Time in Days
'                   ServiceLvl          Service Level
'                   MADExk              Mean Absolute Error Extrapolation Constant
' Returns:          Safety Stock Quantity
' By: RES          Date: 09/23/1999
' Copyright (c) Technology Advantage, Alpharetta, GA  1999
'    Calculate Safety Stock
'     [Average Weekly Forecast for the Lead Time Period] * [SafetyFactory(ServiceLvl)]
'       * [Mean Absolute Error] / [Weekly Forecast Demand] * 1.25
'       * SquareRoot([LeadTime / 7])
'
'    The Safety Factor is the number of standard deviation required to achieve a Desired
'    Service Level
'
'    The SquareRoot of the [Lead Time] / 7 adjusts safety stock for the difference in length between
'    the forecast period and the lead time. Intuitively as lead time increased, the proportional
'    requirement for safety stock diminishes. For example, a three week lead time would adjust safety
'    stock by SquareRoot(3) or 1.732 rather than 3. A ten week lead time would adjust safety stock
'    by 3.162 rather than 10. Conversely very short lead times would result in a proportionally higher
'    safety stock level. For example, a lead time of 2 days would adjust safety stock by .535 rather
'    than the 2/7 or .286.
'
'    Key factors in safety stock:
'     Forecast Demand
'     Mean Absolute Error
'     Desired Service Level
'     Lead Time
'**********************************************************************
Private Function CalcSafetyStock( _
    FcstLT As Double, _
    FcstDemand As Double, _
    MAE As Double, _
    LeadTime As Integer, _
    ServiceLvl As Double, _
    MADExK As Double _
) As Long
On Error GoTo ErrorHandler
    
    'Check for a zero deseasonalized demand or a
    'zero lead time
    If FcstDemand <= 0 Or LeadTime <= 0 Then
        CalcSafetyStock = 0
        Exit Function
    End If
    
    CalcSafetyStock = ((FcstLT * 7) / LeadTime) _
                    * SafetyFactor(ServiceLvl) _
                    * ((MAE / FcstDemand) * 1.25) _
                    * ((LeadTime / 7) ^ MADExK)
    
    'AIM 3.2 Calculation
    'SafetyStock = SafetyFactor(ServiceLvl) _
                    * ((LTForecast / LT * FcstInterval) _
                    * (MADSS / dd * 1.25)) _
                    * ((LT / FcstInterval) ^ 0.75)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.CalcSafetyStock)"
End Function

'**********************************************************************
' Function Name:    SafetyFactor(ServiceFunction)
' Description:      Calculates an approximation of the Inverse Cumulative
'                   Normal Distribution based on an algorith from Abramowitz
'                   and Stegun.
' Parameters:       ServiceLvl      Service Level
' Returns:          Safety Factor
' By: RES           Date: 02/22/2001
' Copyright (c) Technology Advantage, Alpharetta, GA  1999 - 2001
'**********************************************************************
Private Function SafetyFactor( _
    ByVal ServiceLvl As Double _
) As Double
On Error GoTo ErrorHandler

    Dim t As Double
    
    Select Case ServiceLvl
    Case Is < 0.0000002867
        SafetyFactor = -5
    
    Case Is > 0.9999997133
        SafetyFactor = 5
    
    Case Is > 0.5
        ServiceLvl = 1 - ServiceLvl
        t = Sqr(Log(1 / ServiceLvl ^ 2))
        SafetyFactor = Round(t - ((2.515517 + (0.802853 * t) + (0.010328 * t ^ 2)) _
                                  / (1 + (1.432788 * t) + (0.189269 * t ^ 2) + (0.001308 * t ^ 3))) _
                            , 4)
        
    Case Is <= 0.5
        t = Sqr(Log(1 / ServiceLvl ^ 2))
        SafetyFactor = -1 * Round(t - ((2.515517 + (0.802853 * t) + (0.010328 * t ^ 2)) _
                                    / (1 + (1.432788 * t) + (0.189269 * t ^ 2) + (0.001308 * t ^ 3))) _
                            , 4)
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.SafetyFactor)"
End Function

'**********************************************************************
' Function Name:    OrderPolicyUpdate()
' Description:      This function updates the order policy data elements
'                   in an item record for subsequent use by the order
'                   generation module.
' Parameters:       PolicyDate          'Base Date for Order Policy Calculations
'                   UpdatePeriodFcsts   'Update Forecasts for Month, Quarter, and Year
' Returns:          FAIL
'                   SUCCEED
' By: RES           Date: 05/11/2001
' Copyright (c) Technology Advantage, Hartwell, GA  1996 - 2001
''NOTE: Overloaded function (with the same name but different signature) -- exists in AIMUtil.
'Therefore, enforcing scope=Private
'**********************************************************************
Private Function OrderPolicyUpdate( _
    PolicyDate As Date, _
    UpdatePeriodFcsts As Boolean _
) As Integer
On Error GoTo ErrorHandler

    Dim Adj As Double               'Forecast Adjustment
    Dim MasterAdj As Double
    Dim Demand As Double            'Forecast Demand
    Dim Turns As Double             'Estimated Turns
    Dim AdjTurns As Double
    Dim WorkMAE As Double           'Mean Absolute Error
    Dim SysFcst As Double
    Dim AdjSysFcst As Double
    
    'Calculate Fcst for leadtime
    Forecast PolicyDate, PolicyDate + (Accum_Lt - 1), Adj, SysFcst, AdjSysFcst, MasterAdj
    m_FcstLT = SysFcst
    m_AdjFcstLT = AdjSysFcst
    
    'Calculate the Forecast for the Item's Review Time
    Forecast PolicyDate + Accum_Lt, PolicyDate + (Accum_Lt + ReviewTime - 1), Adj, SysFcst, AdjSysFcst, MasterAdj
    m_FcstRT = SysFcst
    m_AdjFcstRT = AdjSysFcst
   
    'Apply High and Low Mean Absolute Error Filters
    WorkMAE = MAE

    If FcstDemand > 0 Then
        If MAE / FcstDemand < LoMADP Then
            WorkMAE = LoMADP * FcstDemand
        End If

        If MAE / FcstDemand > HiMADP Then
            WorkMAE = HiMADP * FcstDemand
        End If
    End If

    'Check forecast for a user override
    If UserFcst > 0 And UserFcstExpDate > PolicyDate Then
        Demand = UserFcst
        WorkMAE = 0.4 * UserFcst
    Else
        Demand = FcstDemand
    End If

    'Is this a dying item -- If so restrict the Mean Absolute Error
    
    If DIFlag = "Y" And FcstDemand > 0 Then
        If (WorkMAE / FcstDemand) > DIMADP Then
            WorkMAE = DIMADP * FcstDemand
        End If
    End If
    
    'Calculate Safety Stock
    m_SafetyStock = CalcSafetyStock(m_FcstLT + m_FcstRT, Demand, WorkMAE, _
        Accum_Lt + ReviewTime, DSer, MADExK)
        
    m_AdjSafetyStock = CalcSafetyStock(m_AdjFcstLT + m_AdjFcstRT, Demand, WorkMAE, _
            Accum_Lt + ReviewTime, DSer, MADExK)
        
        
        'Apply Intermittent Safety Stock; if applicable
        'Assume that IntSafetyStock is constant and use the value from item table
        If IsIntermittent And Int_Enabled = "Y" And IntSafetyStock > m_SafetyStock Then
            m_SafetyStock = IntSafetyStock
        End If

         If IsIntermittent And Int_Enabled = "Y" And IntSafetyStock > m_AdjSafetyStock Then
            m_AdjSafetyStock = IntSafetyStock
        End If

        'Apply Safety Stock Adjustments
        m_SafetyStock = m_SafetyStock * (1 + LtvFact) * (1 + SSAdj)
        m_AdjSafetyStock = m_AdjSafetyStock * (1 + LtvFact) * (1 + SSAdj)
        
        'Perform counter stock test
        If m_SafetyStock < CStock And ZSStock <> "Y" Then
            m_SafetyStock = CStock
        End If
        
        If m_AdjSafetyStock < CStock And ZSStock <> "Y" Then
            m_AdjSafetyStock = CStock
        End If
        'Check zero safety stock switch
        If ZSStock = "Y" Then
            m_SafetyStock = 0
            m_AdjSafetyStock = 0
        End If
        
        'Calculate the order point; round the results
        m_OrderPt = Round(m_FcstRT + m_FcstLT + m_SafetyStock, 0)
        m_AdjOrderPt = Round(m_AdjFcstRT + m_AdjFcstLT + m_AdjSafetyStock, 0)
        
'        'Check for Zero Order Point
        If m_OrderPt = 0 And ZOPSw = "N" Then
            m_OrderPt = 1
        End If
        If m_AdjOrderPt = 0 And ZOPSw = "N" Then
            m_AdjOrderPt = 1
        End If

        'Calculate the Economic Order Quantity for an item
        If m_BkQty(1) = 0 Then
            m_OrderQty = g_EOQ(ReplenCost + ReplenCost2, KFactor, FcstDemand, Cost)
            m_AdjOrderQty = m_OrderQty
        Else
            m_OrderQty = g_QuantityBreakEOQ(FcstDemand, KFactor, (ReplenCost + ReplenCost2), _
                m_BkQty(), m_BkCost())
             m_AdjOrderQty = m_OrderQty
        End If
     
    
   If FcstDemand > 0 Then
        'Test against the high and low turn limits
        If ((m_OrderQty + m_FcstRT) / 2 + m_SafetyStock) > 0 Then
            Turns = (FcstDemand * 52) / ((m_OrderQty + m_FcstRT) / 2 + m_SafetyStock)
        Else
            Turns = 0
        End If

        If Turns > Dft_TurnHigh Then
            If Dft_TurnHigh <> 0 Then
                m_OrderQty = 2 * (((FcstDemand * 52) / Dft_TurnHigh) - m_SafetyStock) - m_FcstRT
            Else
                m_OrderQty = 2 * (((FcstDemand * 52) / 1) - m_SafetyStock) - m_FcstRT
            End If

        ElseIf Turns < Dft_TurnLow Then
            If Dft_TurnLow <> 0 Then
                m_OrderQty = 2 * (((FcstDemand * 52) / Dft_TurnLow) - m_SafetyStock) - m_FcstRT
            Else
                m_OrderQty = 2 * (((FcstDemand * 52) / 1) - m_SafetyStock) - m_FcstRT
            End If

        End If

    Else
    
    m_OrderQty = 0
    
    End If
    
    If FcstDemand > 0 Then
        'Test against the high and low turn limits
        If ((m_AdjOrderQty + m_AdjFcstRT) / 2 + m_AdjSafetyStock) > 0 Then
            AdjTurns = (FcstDemand * 52) / ((m_AdjOrderQty + m_AdjFcstRT) / 2 + m_AdjSafetyStock)
        Else
            AdjTurns = 0
        End If

        If AdjTurns > Dft_TurnHigh Then
            If Dft_TurnHigh <> 0 Then
                m_AdjOrderQty = 2 * (((FcstDemand * 52) / Dft_TurnHigh) - m_AdjSafetyStock) - m_AdjFcstRT
            Else
                m_AdjOrderQty = 2 * (((FcstDemand * 52) / 1) - m_AdjSafetyStock) - m_AdjFcstRT
            End If

        ElseIf AdjTurns < Dft_TurnLow Then
            If Dft_TurnLow <> 0 Then
                m_AdjOrderQty = 2 * (((FcstDemand * 52) / Dft_TurnLow) - m_AdjSafetyStock) - m_AdjFcstRT
            Else
                m_AdjOrderQty = 2 * (((FcstDemand * 52) / 1) - m_AdjSafetyStock) - m_AdjFcstRT
            End If

        End If

    Else
    
    m_AdjOrderQty = 0
    
    End If

    'Test for Maximum Order Quantity (1 year)
    If m_OrderQty > FcstDemand * 52 Then
        m_OrderQty = FcstDemand * 52
    End If
    
    'Test for Maximum Order Quantity (1 year)
    If m_AdjOrderQty > FcstDemand * 52 Then
        m_AdjOrderQty = FcstDemand * 52
    End If

    'Test for Minimum Order Quantity (Based on Review Time)
    If m_OrderQty < m_FcstRT Then 'Osama And Kevin
        m_OrderQty = m_FcstRT
    End If
    
    'Test for Minimum Order Quantity (Based on Review Time)
    If m_AdjOrderQty < m_AdjFcstRT Then 'Osama And Kevin
       m_AdjOrderQty = m_AdjFcstRT
    End If

    'Test for Minimum Order Quantity (Minimum 2 Day Supply)
    If m_OrderQty < FcstDemand / 3.5 Then
        m_OrderQty = FcstDemand / 3.5
    End If
    If m_AdjOrderQty < FcstDemand / 3.5 Then
        m_AdjOrderQty = FcstDemand / 3.5
    End If

    'Test for Minimum Order Quantity (1 unit)
    'If m_OrderQty < 1
    If m_OrderQty < 1 And m_OrderQty > 0 Then 'Osama And Kevin 10/07/2002
        m_OrderQty = 1
    End If
    'Test for Minimum Order Quantity  (1 Unit)
    If m_OrderQty < 0 Then
        m_OrderQty = 0
    End If
    If m_AdjOrderQty < 0 Then
        m_AdjOrderQty = 0
    End If
    
    'Now OrderGeneration Code
    If BuyStrat = "M" Then    'Min/Max Method
        'Check for default values
        If (UserMin = 0 And UserMax = 0) Or (UserMin = 0 And UserMax = 9999999999#) Then
            'the orderpt and orderqty will be same as above
        Else
            m_OrderPt = UserMin
            m_AdjOrderPt = UserMin
            
            m_OrderQty = UserMax - UserMin
            m_AdjOrderQty = UserMax - UserMin
        End If
    ElseIf BuyStrat = "T" Then      'Time Supply Method
        If UserMin = 0 Or Accum_Lt = 0 Then
            'the orderpt will be same as above
        Else
            m_OrderPt = UserMin * (m_FcstLT / Accum_Lt) + m_FcstRT
            m_AdjOrderPt = UserMin * (m_AdjFcstLT / Accum_Lt) + m_AdjFcstRT
        End If
        'Order Point cannot be less than  the forecast for the lead time.
        'If  it is the time supply user minimum  is insufficient to provide safety stock
        If m_OrderPt < m_FcstLT Then
            m_OrderPt = m_FcstLT
            m_AdjOrderPt = m_AdjFcstLT
        End If
        
        If UserMax = 0 Or Accum_Lt = 0 Then
            'use the orderqty values calculated as above
        Else
            m_OrderQty = UserMax * (m_FcstLT / Accum_Lt) - m_OrderPt
            m_AdjOrderQty = UserMax * (m_AdjFcstLT / Accum_Lt) - m_AdjOrderPt
        End If
    End If
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.OrderPolicyUpdate)"
End Function

Public Function TotalAnnualCost( _
    Demand As Double, _
    KFactor As Double, _
    ReplenCost As Double, _
    Qty As Long, _
    Cost As Double _
) As Integer
On Error GoTo ErrorHandler

    TotalAnnualCost = (Cost * 52 * Demand) _
        + ((Demand * 52 / Qty) * ReplenCost) _
        + ((Qty / 2) * Cost * (KFactor / 100))

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.TotalAnnualCost)"
End Function
    
'***********************************************************************
' Function Name():  GetNextRevDate
' Description:      Calculates the next Review Date for an Item
' Parameters:       ReviewDate          Date the item was reviewed
'                   LastSchReview       Last Scheduled Review
'                   RevFreq             Review Frequency
'                   RevInterval         Review Interval
'                   RevSunday           Review on Sundays ?
'                   RevMonday           Review on Mondays ?
'                   RevTuesday          Review on Tuesdays ?
'                   RevWednesday        Review on Wednesdays ?
'                   RevThursday         Review on Thursdays ?
'                   RevFriday           Review on Fridays ?
'                   RevSaturday         Review on Saturdays ?
' Returns:          N/A
' By: RES           Date: 05/01/2001
' Copyright (c) Technology Advantage, Inc. Alpharetta, GA 1998 - 2001
'***********************************************************************
Private Function GetNextRevDate(ReviewDate As Date, _
    LastSchReview As Date, _
    Optional RevFreq As Integer = 0, _
    Optional RevInterval As Integer = 1, _
    Optional RevSunday As Boolean = False, _
    Optional RevMonday As Boolean = True, _
    Optional RevTuesday As Boolean = True, _
    Optional RevWednesday As Boolean = True, _
    Optional RevThursday As Boolean = True, _
    Optional RevFriday As Boolean = True, _
    Optional RevSaturday As Boolean = True, _
    Optional WeekQualifier As Integer = 0, _
    Optional RevStartDate As Date = "01/01/1990", _
    Optional RevEndDate As Date = "12/31/9999", _
    Optional InitBuyPct As Double = 1, _
    Optional InitRevDate As Date = "01/01/1990", _
    Optional ReviewTime As Integer = 1 _
) As Date
On Error GoTo ErrorHandler

    Dim EndOfMonth As Date
    Dim IntervalTest As Integer
    Dim LastWeek As Boolean 'bit
    Dim StartofMonth As Date
    Dim TrialRvDate As Date
    Dim WeekOfMonth As Integer
    
    'Initilaize [Trial Review Date] to [LastSchReview]
    TrialRvDate = LastSchReview

    Do Until TrialRvDate > ReviewDate
        Select Case RevFreq
        Case 0                  'Dynamic
            TrialRvDate = DateAdd("d", 1, TrialRvDate)

        Case 1                  'Daily
            TrialRvDate = DateAdd("d", RevInterval, TrialRvDate)

        Case 2                  'Weekly
            'Check for no day selected
            If Not (RevSunday Or RevMonday Or RevTuesday _
                Or RevWednesday Or RevThursday Or RevFriday _
                Or RevSaturday) Then

                Exit Do
            End If

            Do
                'Increment date by one day
                TrialRvDate = DateAdd("d", 1, TrialRvDate)

                'Test for Weekly Interval
                IntervalTest = (DateDiff("d", RevStartDate, TrialRvDate) / 7) Mod RevInterval

                If Weekday(TrialRvDate) = 1 And RevSunday And IntervalTest = 0 Then Exit Do
                If Weekday(TrialRvDate) = 2 And RevMonday And IntervalTest = 0 Then Exit Do
                If Weekday(TrialRvDate) = 3 And RevTuesday And IntervalTest = 0 Then Exit Do
                If Weekday(TrialRvDate) = 4 And RevWednesday And IntervalTest = 0 Then Exit Do
                If Weekday(TrialRvDate) = 5 And RevThursday And IntervalTest = 0 Then Exit Do
                If Weekday(TrialRvDate) = 6 And RevFriday And IntervalTest = 0 Then Exit Do
                If Weekday(TrialRvDate) = 7 And RevSaturday And IntervalTest = 0 Then Exit Do
            Loop

        Case 3      'Monthly
            TrialRvDate = DateAdd("m", RevInterval, TrialRvDate)

        Case 4      'Week of Month
            Do
                TrialRvDate = DateAdd("d", 1, TrialRvDate)

                'Test for Week of the Month
                StartofMonth = DateSerial(Year(TrialRvDate), Month(TrialRvDate), 1)
                EndOfMonth = DateAdd("m", 1, StartofMonth) - 1
                WeekOfMonth = Int(DateDiff("d", StartofMonth, TrialRvDate) \ 7) + 1

                'Is this the last week ???

                If DateDiff("d", TrialRvDate, EndOfMonth) \ 7 = 0 Then
                    LastWeek = True
                Else
                    LastWeek = False
                End If

                If Weekday(TrialRvDate) = 1 And RevSunday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                If Weekday(TrialRvDate) = 2 And RevMonday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                If Weekday(TrialRvDate) = 3 And RevTuesday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                If Weekday(TrialRvDate) = 4 And RevWednesday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                If Weekday(TrialRvDate) = 5 And RevThursday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                If Weekday(TrialRvDate) = 6 And RevFriday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                If Weekday(TrialRvDate) = 7 And RevSaturday _
                    And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
            Loop
        End Select

    Loop        'End of Control Loop
    
    GetNextRevDate = TrialRvDate

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GetNextRevDate)"
End Function

Public Function CalcReviewTime( _
    RevFreq As Integer, RevInterval As Integer, _
    RevSunday As Integer, RevMonday As Integer, _
    RevTuesday As Integer, RevWednesday As Integer, _
    RevThursday As Integer, RevFriday As Integer, _
    RevSaturday As Integer _
) As Integer
On Error GoTo ErrorHandler

    Select Case RevFreq
    Case 0          'Dynamic
        CalcReviewTime = 1
        
    Case 1          'Daily
        CalcReviewTime = RevInterval
    
    Case 2          'Weekly
        If RevInterval = 1 Then
             CalcReviewTime = 7 \ (RevSunday + RevMonday + RevTuesday + RevWednesday _
                + RevThursday + RevFriday + RevSaturday)
        Else
             CalcReviewTime = RevInterval * 7
        End If
    
    Case 3, 4       'Monthly, Week-Of-Month
        CalcReviewTime = RevInterval * 30
    
    Case Else
         CalcReviewTime = 1
    
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.CalcReviewTime)"
End Function
 '**********************************************************************
' Function Name:    GenerateForecast(...)
' Description:      Calculates the forecast for the period(s) specified
' Parameters:       StartDate           Start Date
'                   fNbrPds              Number Periods
'                   Interval            Forecast Period Interval
' Returns:          Succed or Fail
' By: RES           Date: 03/03/2001
' Copyright (c) Technology Advantage, Alpharetta, GA  1999 - 2001
'**********************************************************************
Public Function GenerateForecast( _
    StartDate As Date, _
    Nbrpds As Integer, _
    Interval As AIM_INTERVALS _
) As Integer
On Error GoTo ErrorHandler

    Dim Adj As Double
    Dim Pd As Integer
    Dim PriceAdj As Double
    Dim CostAdj As Double
    Dim Price As Double
    Dim Cost As Double
    Dim RtnCode As Integer
    Dim SysFcst As Double
    Dim AdjSysFcst As Double
    Dim MasterAdjSysFcst As Double
    Dim MaxPd As Integer
    
    'Retain the number of periods
    m_NbrPeriods = Nbrpds
    
    'Retain the forecast interval
    m_Interval = Interval
    
    m_TotalFcst = 0
    m_xTotalFcst = 0
    m_xTotalPrice = 0
    m_xAdjTotalPrice = 0
    m_xTotalCost = 0
    m_xAdjTotalCost = 0
    m_TotalPrice = 0
    m_TotalCost = 0
    
    'Retain the Forecast Start Date
    FcstStartDate = StartDate
    

    GetFYPriceCost LcId, Item
    
    SetPeriodBounds
    
    MaxPd = UBound(FcstPdDates())
    m_NbrPeriods = MaxPd - 1
    'Check for error conditions
    If MaxPd < 1 Or MaxPd > 106 Then
        GenerateForecast = 0
        Exit Function
    End If
    
    DataYes = PopulateModificationRcdQty(FcstKey, LcId, Item)
    
    MasterDataYes = PopulateMasterModificationRcdQty(FcstKey, LcId, Item)
    FcstStartDate = FcstPdDates(1)
    FcstEndDate = FcstPdDates(MaxPd)

    For Pd = 1 To MaxPd - 1
        'Determine End Date
        
        m_Fcst(Pd) = Forecast(FcstPdDates(Pd), FcstPdDates(Pd + 1) - 1, Adj, SysFcst, AdjSysFcst, MasterAdjSysFcst)
            m_AdjFcst(Pd) = AdjSysFcst
            m_MasterAdjFcst(Pd) = MasterAdjSysFcst
        'End If
        
        'Update any adjustment for the period
        m_Adj(Pd) = Adj
        m_PriceAdj(Pd) = Price
        m_CostAdj(Pd) = Cost
        
        'Update Period Dates
        m_PdDate(Pd) = FcstPdDates(Pd)
        
        'Update Totals
        m_TotalFcst = m_TotalFcst + m_Fcst(Pd)
        m_TotalPrice = m_TotalPrice + m_Fcst(Pd) * Price
        m_TotalCost = m_TotalCost + m_Fcst(Pd) * Cost
        m_TotalFcstAdj = m_TotalFcstAdj + m_AdjFcst(Pd)
        m_TotalPriceAdj = m_TotalPriceAdj + m_AdjFcst(Pd) * Price
        m_TotalCostAdj = m_TotalCostAdj + m_AdjFcst(Pd) * Cost
        Price = 0
        Cost = 0
    Next Pd
    
    GenerateModNetRqmts

    'Perform Carry Forward Rounding
    If CarryFwdRoundingOption Then
        CarryForwardRounding m_Fcst
        CarryForwardRounding m_AdjFcst
        CarryForwardRounding m_MasterAdjFcst
    End If
    
    'Return number of periods forecast
    GenerateForecast = (Pd - 1)
Exit Function
ErrorHandler:
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , "AIMAdvForecast.cls")
    Set Cn = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecast.GenerateForecast)"
End Function







