Attribute VB_Name = "AIMS<PERSON>DMO"
Option Explicit

Function SQLDMO_BldAtString(StartTime As Long, EndTime As Long, _
    FreqSubDay As Long, FreqSubDayInterval As Long)

    Select Case FreqSubDay
    
    Case 0      'Invalid
        SQLDMO_BldAtString = ""
    Case 1      'Once a day
        SQLDMO_BldAtString = "at " + SQLDMO_BldTime(StartTime)
    Case 4      'By Minute
        SQLDMO_BldAtString = "- every " + Format(FreqSubDayInterval, "0") _
            + " minute(s) between " + SQLDMO_BldTime(StartTime) + " and " _
            + SQLDMO_BldTime(EndTime)
    Case 8      'By Hour
        SQLDMO_BldAtString = ", every " + Format(FreqSubDayInterval, "0") _
            + " hour(s) between " + SQLDMO_BldTime(StartTime) + " and " _
            + SQLDMO_BldTime(EndTime)
            
    End Select

End Function
Function SQLDMO_BldDate(RunDate As Long)

    Dim Day As Long
    Dim Month As Long
    Dim Year As Long
    
    Year = RunDate \ 10000
    Month = (RunDate - (Year * 10000)) \ 100
    Day = RunDate - (Year * 10000) - (Month * 100)
    
    SQLDMO_BldDate = Trim(Format(DateSerial(Year, Month, Day), gDateFormat))
    
End Function

Function SQLDMO_BldDateTime(RunDate As Long, RunTime As Long)

    'Check for an uninitialized Date and Time
    If RunDate = 0 And RunTime = 0 Then
        SQLDMO_BldDateTime = "Date and Time are not available"
    Else
        SQLDMO_BldDateTime = SQLDMO_BldDate(RunDate) + " " + SQLDMO_BldTime(RunTime)
    End If

End Function

'
'    Case 64         'Auto Start
'        SchText = "Start automatically when SQL Server Agent starts."
'
'    Case 128        'When SQL Server Agent is idle
'        SchText = "Start whenever CPU(s) become idle"
'
'    End Select
'
' SQLDMO_BldScheduleText = SchText + OnText + AtText
'
'End Function
Function SQLDMO_BldTime(RunTime As Long)

    Dim Hour As Long
    Dim Minute As Long
    Dim Second As Long
    
    Hour = RunTime \ 10000
    Minute = (RunTime - (Hour * 10000)) \ 100
    Second = RunTime - (Hour * 10000) - (Minute * 100)
        
    SQLDMO_BldTime = Format(TimeSerial(Hour, Minute, Second), gTimeFormat)
        
End Function

Function SQLDMO_Date2Long(d As Date) As Long

    SQLDMO_Date2Long = Year(d) * 10000 + Month(d) * 100 + Day(d)

End Function

Function SQLDMO_GetCurrentStatus(Status As Integer)

    Select Case Status
    Case 0
        SQLDMO_GetCurrentStatus = "Unknown"
    Case 1
        SQLDMO_GetCurrentStatus = "Executing"
    Case 2
        SQLDMO_GetCurrentStatus = "Blocked"
    Case 3
        SQLDMO_GetCurrentStatus = "Awaiting Retry"
    Case 4
        SQLDMO_GetCurrentStatus = "Idle"
    Case 5
        SQLDMO_GetCurrentStatus = "Suspended"
    Case 6
        SQLDMO_GetCurrentStatus = "Awaiting Outcome"
    Case 7
        SQLDMO_GetCurrentStatus = "History Logging"
    Case Else
        SQLDMO_GetCurrentStatus = "Unknown"
    End Select

End Function
Function SQLDMO_GetNextStepCode(Desc As String)

    Select Case Desc
    Case getTranslationResource("Unknown")
        SQLDMO_GetNextStepCode = 0
    Case getTranslationResource("Quit the job reporting success")
        SQLDMO_GetNextStepCode = 1
    Case getTranslationResource("Quit the job reporting failure")
        SQLDMO_GetNextStepCode = 2
    Case getTranslationResource("Goto the next step")
         SQLDMO_GetNextStepCode = 3
    Case getTranslationResource("Goto the next identified step")
        SQLDMO_GetNextStepCode = 4
    End Select

End Function
Function SQLDMO_GetNextStepDesc(Code As Integer)

    Select Case Code
    Case 0
        SQLDMO_GetNextStepDesc = "Unknown"
    Case 1
        SQLDMO_GetNextStepDesc = "Quit the job reporting success"
    Case 2
        SQLDMO_GetNextStepDesc = "Quit the job reporting failure"
    Case 3
         SQLDMO_GetNextStepDesc = "Goto the next step"
    Case 4
        SQLDMO_GetNextStepDesc = "Goto the next identified step"
    End Select

End Function

Function SQLDMO_GetOutComeDescription(OutCome As Integer)

    Select Case OutCome
    Case 0
        SQLDMO_GetOutComeDescription = "Failed"
    Case 1
        SQLDMO_GetOutComeDescription = "Succeeded"
    Case 3
        SQLDMO_GetOutComeDescription = "Cancelled"
    Case 4
        SQLDMO_GetOutComeDescription = "Executing"
    Case 5
        SQLDMO_GetOutComeDescription = "Unknown"
    Case Else
        SQLDMO_GetOutComeDescription = "Unknown"
    End Select

End Function

Function SQLDMO_Long2Time(RunTime As Long)
    
    Dim Hour As Long
    Dim Minute As Long
    Dim Second As Long
    
    Hour = RunTime \ 10000
    Minute = (RunTime - (Hour * 10000)) \ 100
    Second = RunTime - (Hour * 10000) - (Minute * 100)
        
    SQLDMO_Long2Time = Format(Hour, "##0") + ":" + Format(Minute, "00") + ":" + Format(Second, "00")

End Function

Function SQLDMO_Time2Long(t As Variant) As Long

    SQLDMO_Time2Long = (Hour(t) * 10000) + (Minute(t) * 100) + Second(t)

End Function


