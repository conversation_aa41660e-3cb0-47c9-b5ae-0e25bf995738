Attribute VB_Name = "AIMMainFunctions"
Option Explicit

Public Sub AddToWindowList(ByVal NewDocID As String)
On Error GoTo ErrorHandler
    
    'Check to see if the form is currently loaded
    If FormExists(NewDocID) = True Then
        Exit Sub
    End If
    
    ' Creates a new State Button Tool by adding it to the
    ' control-level Tools collection
    AIM_Main.tbAIM_Main.Tools.Add NewDocID, ssTypeStateButton
    
    ' Set properties of the new State Button Tool
    With AIM_Main.tbAIM_Main.Tools(NewDocID)
        .Name = NewDocID
        .Group = "Windows"
        .GroupAllowAllUp = False
        .State = ssChecked
        ' Change the file path below, if necessary
        .PictureDown = LoadResPicture("BM_Check", vbResBitmap)
        
    End With

    ' Adds a copy of the new Tool as the last Tool on the
    ' Window List Menu Tool
    With AIM_Main.tbAIM_Main.Tools("ID_mnuWindow").Menu.Tools
        .Add NewDocID, , .Count + 1

        'If there are only five Tools, add a Separator Tool
        If .Count = 5 Then .Add "separator", ssTypeSeparator, 5
        
    End With
    
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(AddToWindowList)", Err.Description
End Sub

Public Function FormExists(ByVal DocId As String) As Boolean
On Error GoTo ErrorHandler

    Dim FormName
    Dim Counter As Long
    Dim tempstr As String
    
    For Counter = 1 To AIM_Main.tbAIM_Main.Tools.Count
        tempstr = AIM_Main.tbAIM_Main.Tools(Counter).ID
        If StrComp(tempstr, DocId, vbTextCompare) = 0 Then
            FormExists = True
            Exit For
        Else
            FormExists = False
        End If
            
    Next
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(FormExists)", Err.Description
End Function

'***********************************************************************
' Function Name:    Main()
'
' Description:      Starting Point for AIM 4.1
'
' Parameters:       None
'
' Returns:          None
'
' By: RES           Date: 2/18/2002
'
' Copyright (c) Technology Advantage, Inc. Alpharetta, GA 1998
'***********************************************************************
Sub Main()
On Error GoTo ErrorHandler

'    Dim RtnCode As Long
'    Dim xOS As OSVERSIONINFO
'
'    'Get the Operating System Version Number
'    xOS.dwOSVersionInfoSize = 148
'    RtnCode = GetVersionEx(xOS)
'
'    If RtnCode <> 0 Then
'        gOSPlatform = xOS.dwPlatformId  '0 = Windows 3.x, 1 = Windows 9x, 2 = NT, Win2000
'    End If
    
    'Initialize the Status Bar
    Set gStatusBar = AIM_Main.sbMessage
    Set gToolBar = AIM_Main.tbAIM_Main

    'Display the Main Form and Logon Screen
    AIM_Main.Show
    AIM_Main.Visible = False

Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(Main)", Err.Description
End Sub

Public Function RemoveFmWindowList(ByVal DocId As String)
On Error GoTo ErrorHandler
    
    AIM_Main.tbAIM_Main.Tools.Remove DocId
    
Exit Function
ErrorHandler:
    If Err.Number <> 40002 Then
        Err.Raise Err.Number, Err.source & "(RemoveFmWindowList)", Err.Description
    Else
        Resume Next
    End If
End Function

Public Function GetClassList( _
    p_Cn As ADODB.Connection, _
    p_Class As String, p_ClassLevel As Integer, _
    p_langID As String, _
    Optional p_rsAIMClasses As ADODB.Recordset _
) As Long
On Error GoTo ErrorHandler
    
    Dim AIM_Class_List_Sp As ADODB.Command
    Dim strMessage As String
    Dim Counter As Long
    
    'Create command object
    Set AIM_Class_List_Sp = New ADODB.Command
    With AIM_Class_List_Sp
        .CommandType = adCmdStoredProc
        Set .ActiveConnection = p_Cn
        .CommandText = "AIM_Class_List_Sp"
        'Set parameters and their values
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("@Class", adVarWChar, adParamInput, 50)
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("@ClassLevel", adTinyInt, adParamInput)
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 10)
        'Values
        .Parameters("@Class").Value = p_Class
        .Parameters("@ClassLevel").Value = IIf(p_ClassLevel = -1, Null, p_ClassLevel)
        .Parameters("@LangID").Value = gLangID
    End With
    
    'Open the Record Set
    If f_IsRecordsetValidAndOpen(p_rsAIMClasses) Then p_rsAIMClasses.Close
    Set p_rsAIMClasses = Nothing
    Set p_rsAIMClasses = New ADODB.Recordset
    With p_rsAIMClasses
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    p_rsAIMClasses.Open AIM_Class_List_Sp
    
    'Return requested val
    If f_IsRecordsetValidAndOpen(p_rsAIMClasses) Then
        GetClassList = 1
    Else
        GetClassList = -1
    End If

    'Clean up
    If Not (AIM_Class_List_Sp Is Nothing) Then Set AIM_Class_List_Sp.ActiveConnection = Nothing
    Set AIM_Class_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    'Clean up
    If Not (AIM_Class_List_Sp Is Nothing) Then Set AIM_Class_List_Sp.ActiveConnection = Nothing
    Set AIM_Class_List_Sp = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(GetClassList)"
End Function

Public Function GetClassDescription(p_Cn As ADODB.Connection, p_Class As String, p_ClassLevel As Integer) As String
    
    Dim rsTemp As ADODB.Recordset
    Dim RtnCode As Long
    
    If p_Class = "" _
    Or p_ClassLevel < 1 _
    Or p_ClassLevel > 4 _
    Then
        Exit Function
    End If
            
    RtnCode = GetClassList(p_Cn, p_Class, p_ClassLevel, gLangID, rsTemp)
    
    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
        GetClassDescription = rsTemp!ClassDesc
    Else
        GetClassDescription = ""
    End If
    
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(GetClassDescription)"
End Function


