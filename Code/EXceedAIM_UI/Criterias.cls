VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "Criterias"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'************************************************************
' Used with SQLStmt.cls
'************************************************************

Option Explicit

Private mCriterias As Collection

Public Function DeleteColumn(TableId As String, _
                            ColName As String)
On Error GoTo ErrorHandler

    Dim p As Integer
    Dim xCriteria As Criteria
    
    'Loop through and remove from the collection
    For Each xCriteria In mCriterias
        p = p + 1
        If UCase(Trim(xCriteria.ColName)) = UCase(Trim(TableId) + "." + Trim(ColName)) Then
            mCriterias.Remove p
        End If
    Next xCriteria

    'Check for remaining balance
    If mCriterias.Count = 1 Then
        mCriterias.Item(1).Qualifier = "Where"
    End If
    
    Set xCriteria = Nothing
    
Exit Function
ErrorHandler:
    Set xCriteria = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(DeleteColumn)"
End Function

Public Function Add(ByVal p_Qualifier As String, _
                    p_ColName As String, _
                    p_Numeric As Boolean, _
                    p_NotOpt As Boolean, _
                    p_CompOper As String, _
                    p_CompValue1 As String, _
                    p_Compvalue2 As String) _
    As Criteria
On Error GoTo ErrorHandler

    'Initialize
    Dim CriteriaNew As Criteria
    Set CriteriaNew = New Criteria
    
    'Set value = input parameter
    With CriteriaNew
        .Qualifier = p_Qualifier
        .ColName = p_ColName
        .Numeric = p_Numeric
        .NotOpt = p_NotOpt
        .CompOper = p_CompOper
        .CompValue1 = p_CompValue1
        .CompValue2 = p_Compvalue2
    End With

    'Add to Collection
    mCriterias.Add CriteriaNew
    
    'Set return value
    Set Add = CriteriaNew
    
    'Clean up
    Set CriteriaNew = Nothing
    
Exit Function
ErrorHandler:
    Set CriteriaNew = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(Add)"
End Function

Public Function Clear()
On Error GoTo ErrorHandler

    Do Until mCriterias.Count = 0
        mCriterias.Remove (1)
    Loop
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Clear)"
End Function

Public Function Count() As Long
On Error GoTo ErrorHandler
    
    'Fetch property from collection
    Count = mCriterias.Count

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Count)"
End Function

Public Function Delete(ByVal Index As Variant)
On Error GoTo ErrorHandler

    'Remove Criteria
    mCriterias.Remove Index
    
    'Check for remaining balance
    If mCriterias.Count = 1 Then
        mCriterias.Item(1).Qualifier = "Where"
    End If
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Delete)"
End Function

Public Function IsCriteria(TableId As String, _
                            ColName As String) As String
On Error GoTo ErrorHandler

    Dim xCriteria As Criteria
    
    'Loop through colleciton to identify given input parameters. If found, return the comparison value
    For Each xCriteria In mCriterias
        If UCase(Trim(xCriteria.ColName)) = UCase(Trim(TableId) + "." + Trim(ColName)) Then
            IsCriteria = xCriteria.CompValue1
            Exit Function
        End If
    Next xCriteria
    
    'Not Found
    IsCriteria = ""

    'Clean up
    Set xCriteria = Nothing
    
Exit Function
ErrorHandler:
    Set xCriteria = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(IsCriteria)"
End Function

Public Function Item(ByVal Index As Variant) As Criteria
Attribute Item.VB_UserMemId = 0
On Error GoTo ErrorHandler
    
    'Fetch collection item for the given index (input parameter)
    Set Item = mCriterias.Item(Index)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Item)"
End Function

Public Function NewEnum() As IUnknown
Attribute NewEnum.VB_UserMemId = -4
Attribute NewEnum.VB_MemberFlags = "40"
On Error GoTo ErrorHandler
    
    'NewEnum is part of the IOleEnum interface.  It is for iterating through a list of items
    Set NewEnum = mCriterias.[_NewEnum]

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(NewEnum)"
End Function

Private Sub Class_Initialize()
On Error GoTo ErrorHandler

    'Instantiate modular objects
    Set mCriterias = New Collection
    
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "Criterias.Class_Initialize"
End Sub

Private Sub Class_Terminate()
On Error GoTo ErrorHandler

    'Destroy Modular objects
    Set mCriterias = Nothing
    
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "Criterias.Class_Terminate"
End Sub
