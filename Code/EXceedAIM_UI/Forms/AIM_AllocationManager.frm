VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{831FDD16-0C5C-11D2-A9FC-0000F8754DA1}#2.0#0"; "MSCOMCTL.OCX"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_AllocationReview 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Allocation Review"
   ClientHeight    =   10155
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   15210
   FillColor       =   &H00FFFFFF&
   BeginProperty Font 
      Name            =   "Microsoft Sans Serif"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   ForeColor       =   &H00C00000&
   Icon            =   "AIM_AllocationManager.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   10155
   ScaleWidth      =   15210
   WindowState     =   2  'Maximized
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   9720
      _ExtentX        =   609
      _ExtentY        =   582
      _Version        =   131083
      FontSource      =   1
      ToolBarsCount   =   1
      ToolsCount      =   8
      DisplayContextMenu=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Tools           =   "AIM_AllocationManager.frx":030A
      ToolBars        =   "AIM_AllocationManager.frx":5CB5
   End
   Begin ActiveTabs.SSActiveTabs atAllocSummary 
      Height          =   2685
      Left            =   8160
      TabIndex        =   1
      Top             =   120
      Width           =   6960
      _ExtentX        =   12277
      _ExtentY        =   4736
      _Version        =   131083
      TabCount        =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TabMinWidth     =   0
      TagVariant      =   ""
      Tabs            =   "AIM_AllocationManager.frx":5F1A
      Begin ActiveTabs.SSActiveTabPanel ssatpEditAllocQties 
         Height          =   2295
         Left            =   30
         TabIndex        =   33
         Top             =   360
         Width           =   6900
         _ExtentX        =   12171
         _ExtentY        =   4048
         _Version        =   131083
         TabGuid         =   "AIM_AllocationManager.frx":5FBA
         Begin VB.Frame Frame1 
            Height          =   2235
            Left            =   120
            TabIndex        =   13
            Top             =   0
            Width           =   6615
            Begin TDBText6Ctl.TDBText txtDesc 
               Height          =   345
               Left            =   3000
               TabIndex        =   16
               Top             =   195
               Width           =   3495
               _Version        =   65536
               _ExtentX        =   6165
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":5FE2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":6055
               Key             =   "AIM_AllocationManager.frx":6073
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtUserDefined 
               Height          =   345
               Left            =   1680
               TabIndex        =   26
               Top             =   1308
               Width           =   4815
               _Version        =   65536
               _ExtentX        =   8493
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":60B7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":612A
               Key             =   "AIM_AllocationManager.frx":6148
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtDivision 
               Height          =   345
               Left            =   1680
               TabIndex        =   18
               Top             =   566
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":618C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":61FF
               Key             =   "AIM_AllocationManager.frx":621D
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtDestId 
               Height          =   345
               Left            =   1680
               TabIndex        =   15
               Top             =   195
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":6261
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":62D4
               Key             =   "AIM_AllocationManager.frx":62F2
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtRegion 
               Height          =   345
               Left            =   1680
               TabIndex        =   22
               Top             =   937
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":6336
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":63A9
               Key             =   "AIM_AllocationManager.frx":63C7
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtRank 
               Height          =   345
               Left            =   1680
               TabIndex        =   28
               Top             =   1680
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   600
               Caption         =   "AIM_AllocationManager.frx":640B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":647E
               Key             =   "AIM_AllocationManager.frx":649C
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtStatus 
               Height          =   345
               Left            =   4800
               TabIndex        =   24
               Top             =   937
               Width           =   1695
               _Version        =   65536
               _ExtentX        =   2990
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":64E0
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":6553
               Key             =   "AIM_AllocationManager.frx":6571
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtType 
               Height          =   345
               Left            =   4800
               TabIndex        =   20
               Top             =   566
               Width           =   1695
               _Version        =   65536
               _ExtentX        =   2990
               _ExtentY        =   609
               Caption         =   "AIM_AllocationManager.frx":65B5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":6628
               Key             =   "AIM_AllocationManager.frx":6646
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label 
               Caption         =   "Destination ID"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   14
               Top             =   240
               Width           =   1455
            End
            Begin VB.Label Label 
               Caption         =   "User Defined"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   25
               Top             =   1353
               Width           =   1455
            End
            Begin VB.Label Label 
               Caption         =   "Type"
               Height          =   300
               Index           =   4
               Left            =   3240
               TabIndex        =   19
               Top             =   611
               Width           =   1455
            End
            Begin VB.Label Label 
               Caption         =   "Division"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   17
               Top             =   611
               Width           =   1455
            End
            Begin VB.Label Label 
               Caption         =   "Region"
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   21
               Top             =   982
               Width           =   1455
            End
            Begin VB.Label Label 
               Caption         =   "Status"
               Height          =   300
               Index           =   5
               Left            =   3240
               TabIndex        =   23
               Top             =   982
               Width           =   1455
            End
            Begin VB.Label Label 
               Caption         =   "Rank"
               Height          =   300
               Index           =   6
               Left            =   120
               TabIndex        =   27
               Top             =   1725
               Width           =   1455
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel ssatpAllocSummaryMain 
         Height          =   2295
         Left            =   30
         TabIndex        =   2
         Top             =   360
         Width           =   6900
         _ExtentX        =   12171
         _ExtentY        =   4048
         _Version        =   131083
         TabGuid         =   "AIM_AllocationManager.frx":668A
         Begin VB.Frame Frame2 
            Height          =   2235
            Left            =   120
            TabIndex        =   3
            Top             =   0
            Width           =   6375
            Begin TDBText6Ctl.TDBText txtOrderFillPct 
               Height          =   345
               Left            =   2400
               TabIndex        =   11
               Top             =   1400
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   600
               Caption         =   "AIM_AllocationManager.frx":66B2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":6725
               Key             =   "AIM_AllocationManager.frx":6743
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtTotalUnits 
               Height          =   340
               Left            =   2400
               TabIndex        =   9
               Top             =   1012
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   600
               Caption         =   "AIM_AllocationManager.frx":6787
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":67FA
               Key             =   "AIM_AllocationManager.frx":6818
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtTotalLines 
               Height          =   340
               Left            =   2400
               TabIndex        =   7
               Top             =   626
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   600
               Caption         =   "AIM_AllocationManager.frx":685C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":68CF
               Key             =   "AIM_AllocationManager.frx":68ED
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtOrdNbr 
               Height          =   340
               Left            =   2400
               TabIndex        =   5
               Top             =   240
               Width           =   1935
               _Version        =   65536
               _ExtentX        =   3413
               _ExtentY        =   600
               Caption         =   "AIM_AllocationManager.frx":6931
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_AllocationManager.frx":69A4
               Key             =   "AIM_AllocationManager.frx":69C2
               BackColor       =   16777215
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   0
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label 
               Caption         =   "Order Number"
               Height          =   300
               Index           =   10
               Left            =   120
               TabIndex        =   4
               Top             =   280
               Width           =   2175
            End
            Begin VB.Label Label 
               Caption         =   "Total Order Units"
               Height          =   300
               Index           =   8
               Left            =   120
               TabIndex        =   8
               Top             =   1052
               Width           =   2175
            End
            Begin VB.Label Label 
               Caption         =   "Order Fill Pct"
               Height          =   300
               Index           =   9
               Left            =   120
               TabIndex        =   10
               Top             =   1445
               Width           =   2175
            End
            Begin VB.Label Label 
               Caption         =   "Total Order Lines"
               Height          =   300
               Index           =   7
               Left            =   120
               TabIndex        =   6
               Top             =   666
               Width           =   2175
            End
         End
      End
   End
   Begin ActiveTabs.SSActiveTabs atAllocDetail 
      Height          =   6705
      Left            =   120
      TabIndex        =   29
      Top             =   2880
      Width           =   15030
      _ExtentX        =   26511
      _ExtentY        =   11827
      _Version        =   131083
      TabCount        =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TabMinWidth     =   390
      TabMinHeight    =   0
      TagVariant      =   ""
      Tabs            =   "AIM_AllocationManager.frx":6A06
      Begin ActiveTabs.SSActiveTabPanel ssatpEditQties 
         Height          =   6330
         Left            =   30
         TabIndex        =   12
         Top             =   345
         Width           =   14970
         _ExtentX        =   26405
         _ExtentY        =   11165
         _Version        =   131083
         TabGuid         =   "AIM_AllocationManager.frx":6AB9
         Begin VB.Frame frAllocEditQty 
            Height          =   6135
            Left            =   120
            TabIndex        =   34
            Top             =   0
            Width           =   14730
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAllocEditQty 
               Height          =   5805
               Left            =   120
               TabIndex        =   35
               Top             =   240
               Width           =   14520
               _Version        =   196617
               DataMode        =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               HeadLines       =   2
               AllowDelete     =   -1  'True
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               ExtraHeight     =   212
               CaptionAlignment=   0
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   25612
               _ExtentY        =   10239
               _StockProps     =   79
               Caption         =   "Expanded Item Detail"
               ForeColor       =   -2147483630
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel ssatpAllocDetail 
         Height          =   6330
         Left            =   30
         TabIndex        =   30
         Top             =   345
         Width           =   14970
         _ExtentX        =   26405
         _ExtentY        =   11165
         _Version        =   131083
         TabGuid         =   "AIM_AllocationManager.frx":6AE1
         Begin VB.Frame frAllocDetail 
            Height          =   6135
            Left            =   120
            TabIndex        =   31
            Top             =   0
            Width           =   14730
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAllocDetail 
               Height          =   5805
               Left            =   120
               TabIndex        =   32
               Top             =   240
               Width           =   14520
               _Version        =   196617
               DataMode        =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               AllowDelete     =   -1  'True
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               ExtraHeight     =   212
               CaptionAlignment=   0
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   25612
               _ExtentY        =   10239
               _StockProps     =   79
               Caption         =   "Allocation Order Detail"
               ForeColor       =   -2147483630
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
      End
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAllocList 
      Height          =   2685
      Left            =   120
      TabIndex        =   0
      Top             =   120
      Width           =   7920
      _Version        =   196617
      DataMode        =   1
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      AllowDelete     =   -1  'True
      AllowColumnSwapping=   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      ExtraHeight     =   212
      CaptionAlignment=   0
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   13970
      _ExtentY        =   4736
      _StockProps     =   79
      Caption         =   "Allocation Order/Loc Summary"
      ForeColor       =   -2147483630
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin MSComctlLib.Toolbar Toolbar1 
      Align           =   1  'Align Top
      Height          =   705
      Left            =   0
      TabIndex        =   36
      Top             =   0
      Width           =   15210
      _ExtentX        =   26829
      _ExtentY        =   1244
      ButtonWidth     =   714
      ButtonHeight    =   1085
      Appearance      =   1
      _Version        =   393216
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Copy &Selected Rows"
         Index           =   1
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Print &All Rows"
         Index           =   2
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print Selected Rows"
         Index           =   3
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   4
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Restore Grid &Defaults"
         Index           =   5
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   6
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Positive SO&Q"
         Checked         =   -1  'True
         Index           =   7
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Pla&nned Orders"
         Checked         =   -1  'True
         Index           =   8
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Released Orders"
         Checked         =   -1  'True
         Index           =   9
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   10
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Pack Size &Enabled"
         Checked         =   -1  'True
         Index           =   11
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   12
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Forecast Simulator"
         Index           =   13
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   14
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Small Font"
         Index           =   15
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Medium Font"
         Index           =   16
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Large Font"
         Index           =   17
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   18
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Total Released Lines Only"
         Index           =   19
      End
   End
   Begin VB.Menu mnuGraph 
      Caption         =   "&Graph"
      Visible         =   0   'False
      Begin VB.Menu mnuGraphOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuGraphOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
   Begin VB.Menu mnuPerformance 
      Caption         =   "&Performance"
      Visible         =   0   'False
      Begin VB.Menu mnuPerformanceOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuPerformanceOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
   Begin VB.Menu mnuPositions 
      Caption         =   "&Positions"
      Visible         =   0   'False
      Begin VB.Menu mnuPositionsOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuPositionsOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
   Begin VB.Menu mnuVendorSummary 
      Caption         =   "&Vendor Summary"
      Visible         =   0   'False
      Begin VB.Menu mnuVendorSummaryOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuVendorSummaryOpt 
         Caption         =   "&Print"
         Index           =   1
      End
      Begin VB.Menu mnuVendorSummaryOpt 
         Caption         =   "-"
         Index           =   2
      End
      Begin VB.Menu mnuVendorSummaryOpt 
         Caption         =   "&Display Exception Orders"
         Checked         =   -1  'True
         Index           =   3
      End
      Begin VB.Menu mnuVendorSummaryOpt 
         Caption         =   "&Summarize Order Lines by Vendor"
         Checked         =   -1  'True
         Index           =   4
         Visible         =   0   'False
      End
   End
   Begin VB.Menu mnuAllocationSummary 
      Caption         =   "&Allocation Summary"
      Visible         =   0   'False
      Begin VB.Menu mnuAllocationSummaryOpt 
         Caption         =   "&Release all locations for this order"
         Index           =   0
      End
      Begin VB.Menu mnuAllocationSummaryOpt 
         Caption         =   "&Undo release for all Locations in this order"
         Index           =   1
      End
      Begin VB.Menu mnuAllocationSummaryOpt 
         Caption         =   "&Release all orders"
         Index           =   2
      End
      Begin VB.Menu mnuAllocationSummaryOpt 
         Caption         =   "Undo release for all orders"
         HelpContextID   =   3
         Index           =   3
      End
      Begin VB.Menu mnuAllocationSummaryOpt 
         Caption         =   "&Copy"
         Index           =   4
      End
      Begin VB.Menu mnuAllocationSummaryOpt 
         Caption         =   "&Print"
         Index           =   5
      End
   End
   Begin VB.Menu mnuAllocationEdit 
      Caption         =   "&AllocationEdit"
      Visible         =   0   'False
      Begin VB.Menu mnuAllocationEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
   End
   Begin VB.Menu mnuAllocationDetails 
      Caption         =   "&Allocation Detail"
      Visible         =   0   'False
      Begin VB.Menu mnuAllocationDetailsOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuAllocationDetailsOpt 
         Caption         =   "Copy& Selected Rows"
         Index           =   1
      End
      Begin VB.Menu mnuAllocationDetailsOpt 
         Caption         =   "Print& All Rows"
         Index           =   2
      End
      Begin VB.Menu mnuAllocationDetailsOpt 
         Caption         =   "&Print Selected Rows"
         Index           =   3
      End
      Begin VB.Menu mnuAllocationDetailsOpt 
         Caption         =   "-"
         Index           =   4
      End
      Begin VB.Menu mnuAllocationDetailsOpt 
         Caption         =   "Restore Grid& Defaults"
         Index           =   5
      End
   End
End
Attribute VB_Name = "AIM_AllocationReview"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False

'*****************************************************************************
' Copyright (c) 2003 EXE Technologies, Inc. All rights reserved.
'*****************************************************************************
' This file contains trade secrets of EXE Technologies. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of EXE Technologies.
'*****************************************************************************
'
'   Allocation Review
'
'   Created - 2003-05-06
'   Author  - Annalakshmi Stocksdale
'
'   Purpose: To provide the user with snapshots of the inventory data
'       before and after allocation.
'
'   These snapshots of alloc. data should be presented for the identification
'       of exceptions so that these may be excluded from the allocation process
'       and/or the final order. This would allow them to verify the results of
'       allocation and make the necessary adjustments to the final output
'       before generating the outbound allocated orders for further execution.
'   The pre-allocation review should allow the users to exclude those items
'       that are not eligible for inventory allocation.
'   The post-allocation review is closer to the buyer review, in that
'       it is meant to allow the user final arbitration on the outbound order
'       sent to fulfill the re-stock requests.
'
'   This module depends on the following:
'   Data Intefaces:
'       LC      Location data
'       SS/SL   Stock Status and more frequent inventory data
'       RO      Re-stock Order
'       RP      Re-stock Profile
'       RS      Re-stock Sourcing
'   Allocation:
'       Exceed Allocation's main function is to optimize the distribution of
'           inventory from sources such as distribution centers
'           to destinations such as retail stores, based on
'           the destination's priority and the source location's hierarchy.
'
'   The back-end task for executing inventory allocation may be run
'       on a schedule (using AIM's Job Scheduler,) or launched manually
'       from this screen (using the Ad-hoc Allocation tool.)
'
'*****************************************************************************
'*****************************************************************************
'History:       Future Updates of note to be recorded below.
'
'*****************************************************************************

'Order/Line Item Status
'This value is for internal use only. It is meant to indicate
'    an order's (and/or it's line items') current position in the allocation lifecycle.
'    Valid options are:
'    0 - Pending
'      - First stage of the lifecycle. The Order has been received as an inbound,
'      and is awaiting pre-allocation review and allocation.
'    1 - Do not Allocate
'      - The Order has been subjected to the pre-allocation review,
'      and has been excluded from the allocation process by the reviewer.
'    10 - Allocated
'      - The Orders that was "Pending" has been processed for allocation
'      and is now awaiting post-allocation review and execution as an outbound interface.
'    11 - Not Allocated
'      - The Order that was excluded from getting quantities allocated
'      ("Do not Allocate") is no longer valid for further processing of any variety,
'      and is now awaiting execution as an outbound interface with an allocated quantity of zero.
'    12 - Allocated with exceptions
'      - The Order that was "Allocated" has been marked for review
'      as having a fillpercentage less than the exception trigger defined for the order/item,
'      and is now awaiting post-allocation review.
'    20 - Released
'      - The Order has been subjected to post-allocation review,
'      or been otherwise marked for execution as an outbound interface.
'    22 - Released with exceptions
'      - The Order that was "Allocated with Exceptions" has been subjected
'      to post-allocation review, or been otherwise marked for execution as an outbound interface.
'    30 - Completed
'      - The Order that was "Released" or "Not Allocated" has been processed
'      as an outbound file to be sent to the host.
'      This is the last stage of the lifecycle
Private Enum OrdStats
    STAT_PENDING = 0
    STAT_DO_NOT_ALLOCATE = 1
    STAT_ALLOCATED = 10
    STAT_NOT_ALLOCATED = 11
    STAT_ALLOC_WITH_EXCEPTIONS = 12
    STAT_RELEASED = 20
    STAT_REL_WITH_EXCEPTIONS = 22
    STAT_COMPLETED = 30
End Enum

Private Enum SingleOrder
    DO_RELEASE = 1
    UNDO_RELEASE = 2
End Enum
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_AllocationDetail_Sp As ADODB.Command
Dim AIM_AllocationSummary_Sp As ADODB.Command
Dim AIM_AllocationEdit_Sp As ADODB.Command
Dim AIM_AllocationResetItem_Sp As ADODB.Command
Dim AIM_AllocationResetOrder_Sp As ADODB.Command
Dim AIM_AllocationSourceQty_Sp As ADODB.Command
Dim AIM_AllocationReviewSave_Sp As ADODB.Command

Dim rsAIMUsers As ADODB.Recordset
Dim rsAllocationDetail As ADODB.Recordset
Dim rsAllocationSummary As ADODB.Recordset
Dim rsAllocationEdit As ADODB.Recordset
Dim rsAllocationSourceQty As ADODB.Recordset
Dim rsAllocationOrderSummary As ADODB.Recordset

Dim OrdNbr As String
Dim LcId As String
Dim ById As String
Dim ColWidth(10) As Integer
Dim NbrAllocOrders As Integer
Dim m_FirstTime As Boolean
Dim ColLoaded_AllocList As Boolean
Dim ColLoaded_Detail As Boolean
Dim AllocationState As String ' this can be PRE or POST initally PRE
Dim FocusLocation As String  ' this can be SUMMARY or DETAIL
Dim UBArray() As String
Dim vClone As Variant
Dim iNewSize As Integer
Dim iCols As Integer
Dim iPoint As Integer
Dim iCount As Integer
Dim NoChange As Boolean
Dim OrgAdjustedAllocQty As Double
Dim AllocDetailBookMark As Variant

Private Type SourceAndQty
    source As String
    Qty  As Double
End Type

Private Function Exec_ToggleReleaseAll( _
    p_Status As Integer, _
    Optional p_SingleOrder As Integer _
)
On Error GoTo ErrorHandler
'P_SingleOrder values:
'   1 = Release single order
'   2 = undo release single order
'P_singleorder argument is supplied from mnuAllocationSummaryOpt_Click event only

    Dim InitBookMark As Variant
    Dim OrdNbr As String
    Dim RelOpt As String

    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) Then Exit Function

    If StrComp(FocusLocation, "DETAIL", vbTextCompare) = 0 _
    And StrComp(AllocationState, "PRE", vbTextCompare) = 0 _
    Then
        InitBookMark = Me.dgAllocDetail.GetBookmark(0)
        With rsAllocationDetail
            .MoveFirst
            Do Until .eof
                .Update "LineItemStatus", p_Status
                .MoveNext
            Loop
            'Rebind the grid
            Me.dgAllocDetail.ReBind
            If f_IsRecordsetOpenAndPopulated(rsAllocationDetail) Then Me.dgAllocDetail.Rows = rsAllocationDetail.RecordCount
        End With
     
        Me.dgAllocDetail.Bookmark = InitBookMark

    ElseIf StrComp(FocusLocation, "SUMMARY", vbTextCompare) = 0 _
    Then
        InitBookMark = Me.dgAllocList.GetBookmark(0)
        OrdNbr = Me.dgAllocList.Columns("OrdNbr").Value
        RelOpt = Me.dgAllocList.Columns("RelOpt").Value
    
        With rsAllocationSummary
            .MoveFirst
            Do Until .eof
                'If the row is already selected then select all the rows  or
                'If the row is not already selected then unselect all the rows
                Select Case p_SingleOrder
                Case SingleOrder.DO_RELEASE, SingleOrder.UNDO_RELEASE
                    If (p_SingleOrder = SingleOrder.DO_RELEASE And rsAllocationSummary("OrdNbr").Value = OrdNbr) _
                    Or (p_SingleOrder = SingleOrder.UNDO_RELEASE) _
                    Then
                        Select Case rsAllocationSummary("OrderStatus").Value
                        Case OrdStats.STAT_ALLOCATED
                            .Update "OrderStatus", OrdStats.STAT_RELEASED
                        Case OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                            .Update "OrderStatus", OrdStats.STAT_REL_WITH_EXCEPTIONS
                        Case OrdStats.STAT_RELEASED
                            .Update "OrderStatus", OrdStats.STAT_ALLOCATED
                        Case OrdStats.STAT_REL_WITH_EXCEPTIONS
                            .Update "OrderStatus", OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                        Case OrdStats.STAT_PENDING
                            .Update "OrderStatus", OrdStats.STAT_DO_NOT_ALLOCATE
                        Case OrdStats.STAT_DO_NOT_ALLOCATE
                            .Update "OrderStatus", OrdStats.STAT_PENDING    'Shouldn't this be NOT ALLOCATED?
                        End Select
                    End If
                Case Else
                    If (p_Status = OrdStats.STAT_DO_NOT_ALLOCATE And RelOpt = "0") _
                    Or (p_Status = OrdStats.STAT_PENDING And RelOpt = "-1") _
                    Or (p_Status = OrdStats.STAT_ALLOCATED And RelOpt = "0") _
                    Or (p_Status = OrdStats.STAT_RELEASED And RelOpt = "-1") _
                    Then
                        '.Update "OrderStatus", p_Status
                        Select Case p_Status
                        Case OrdStats.STAT_RELEASED
                            Select Case rsAllocationSummary("OrderStatus").Value
                            Case OrdStats.STAT_ALLOCATED
                                .Update "OrderStatus", OrdStats.STAT_RELEASED
                            Case OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                                .Update "OrderStatus", OrdStats.STAT_REL_WITH_EXCEPTIONS
                            End Select
                        Case OrdStats.STAT_ALLOCATED
                            Select Case rsAllocationSummary("OrderStatus").Value
                            Case OrdStats.STAT_RELEASED
                                .Update "OrderStatus", OrdStats.STAT_ALLOCATED
                            Case OrdStats.STAT_REL_WITH_EXCEPTIONS
                                .Update "OrderStatus", OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                            End Select
                        Case OrdStats.STAT_PENDING
                            Select Case rsAllocationSummary("OrderStatus").Value
                            Case OrdStats.STAT_DO_NOT_ALLOCATE
                                .Update "OrderStatus", OrdStats.STAT_PENDING
                            End Select
                        Case OrdStats.STAT_DO_NOT_ALLOCATE
                            Select Case rsAllocationSummary("OrderStatus").Value
                            Case OrdStats.STAT_PENDING
                                .Update "OrderStatus", OrdStats.STAT_DO_NOT_ALLOCATE
                            End Select
                        End Select
                    Else
                        'If the row is not selected then select only rows with the same ordnbr or
                        'If the row is selected then  unselet only rows with the same ordnbr
                        If rsAllocationSummary("OrdNbr").Value = OrdNbr Then
                            Select Case rsAllocationSummary("OrderStatus").Value
                            Case OrdStats.STAT_ALLOCATED
                                .Update "OrderStatus", OrdStats.STAT_RELEASED
                            Case OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                                .Update "OrderStatus", OrdStats.STAT_REL_WITH_EXCEPTIONS
                            Case OrdStats.STAT_RELEASED
                                .Update "OrderStatus", OrdStats.STAT_ALLOCATED
                            Case OrdStats.STAT_REL_WITH_EXCEPTIONS
                                .Update "OrderStatus", OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                            Case OrdStats.STAT_PENDING
                                .Update "OrderStatus", OrdStats.STAT_DO_NOT_ALLOCATE
                            Case OrdStats.STAT_DO_NOT_ALLOCATE
                                .Update "OrderStatus", OrdStats.STAT_PENDING
                            End Select
                    End If
                End If
                End Select
                .MoveNext
            Loop
        
            'Rebind the grid
            Me.dgAllocList.ReBind
            If f_IsRecordsetOpenAndPopulated(rsAllocationSummary) Then Me.dgAllocList.Rows = rsAllocationSummary.RecordCount
        End With
        
        Me.dgAllocList.Bookmark = InitBookMark
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Exec_ToggleReleaseAll[" & p_Status & "])"
    f_HandleErr , , , "AIM_AllocationManager::Exec_ToggleReleaseAll[" & p_Status & "]", Now, gDRGeneralError, True, Err
End Function

Private Sub Exec_ResetToOriginal()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim RtnCode As Integer
    
    strMessage = getTranslationResource("MSGBOX07800")
    If StrComp(strMessage, "MSGBOX07800") = 0 Then strMessage = "Do you wish to reset the Adjusted Order Qty  for all items to their Allocated Qty?"
    RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, Me.Caption)
    
    If RtnCode = vbYes Then
        Screen.MousePointer = vbHourglass
        
        If Me.atAllocDetail.SelectedTab.Key = "ItemDetail" Then
            If (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) Then
                AIM_AllocationResetItem_Sp.Parameters("@Item").Value = rsAllocationDetail("Item").Value
                AIM_AllocationResetItem_Sp.Execute
            End If
            'Update the grid
            Me.dgAllocDetail.ReBind
            If f_IsRecordsetOpenAndPopulated(rsAllocationDetail) Then Me.dgAllocDetail.Rows = rsAllocationDetail.RecordCount
            Exec_ItemDetail
        ElseIf Me.atAllocDetail.SelectedTab.Key = "AllocationDetail" Then
            If (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) Then
                ' AIM_AllocationResetOrder_Sp.Parameters("@OrdNbr").Value = rsAllocationSummary("OrdQty").Value
                 AIM_AllocationResetOrder_Sp.Parameters("@OrdNbr").Value = Trim(rsAllocationSummary("OrdNbr").Value)
                 AIM_AllocationResetOrder_Sp.Parameters("@AllocationStatus").Value = AllocationState
                 AIM_AllocationResetOrder_Sp.Execute
                 Refresh_AllocationDetail
                 Me.dgAllocDetail.ReBind
            End If
        End If
    Else
        Exit Sub
    End If
        Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::Exec_ResetToOriginal", Now, gDRGeneralError, True, Err
End Sub

Private Function Exec_Validate(ArgItem As String, ArgOrdNbr As String, ArgSource As String, ArgAdjustedAllocQty As String, ArgLineNbr As String) As Integer
'On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Integer
   
    Dim LSourceAndQty() As SourceAndQty 'used to store the sourec and total qty for that source
    Dim iCol As Integer
    Dim iC As Integer
    Dim iC1 As Integer
    Dim SumQty As Double
    Dim LSource As String
    
    AIM_AllocationSourceQty_Sp.Parameters("@Item").Value = ArgItem
    AIM_AllocationSourceQty_Sp.Parameters("@OrdNbr").Value = ArgOrdNbr
    
    If f_IsRecordsetValidAndOpen(rsAllocationSourceQty) Then rsAllocationSourceQty.Close
    Set rsAllocationSourceQty = Nothing
    Set rsAllocationSourceQty = New ADODB.Recordset
    With rsAllocationSourceQty
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    rsAllocationSourceQty.Open AIM_AllocationSourceQty_Sp
    
    'Populate the structure with source and total qty
    ReDim LSourceAndQty(1 To rsAllocationSourceQty.RecordCount)
    For iC = 1 To rsAllocationSourceQty.RecordCount
    rsAllocationSourceQty.AbsolutePosition = iC
    
        LSourceAndQty(iC).source = rsAllocationSourceQty("lcid")
        LSourceAndQty(iC).Qty = rsAllocationSourceQty("qty")
    Next
    
    'Check to see that the total destination qty does not execceed the total source qty
    For iC1 = 1 To UBound(LSourceAndQty())
        LSource = LSourceAndQty(iC1).source
        SumQty = 0
        For iC = 0 To UBound(UBArray, 2) - 1
            If UBArray(1, iC) = ArgOrdNbr And UBArray(4, iC) = ArgSource And LSource = ArgSource And UBArray(12, iC) = ArgLineNbr Then
                SumQty = SumQty + CInt(ArgAdjustedAllocQty)
            ElseIf UBArray(4, iC) = LSource Then
                SumQty = SumQty + UBArray(9, iC)
            End If
        Next iC
        If SumQty > LSourceAndQty(iC1).Qty Then
        
        strMessage = getTranslationResource("MSGBOX07801")
        If StrComp(strMessage, "MSGBOX07801") = 0 Then strMessage = "Destination  allocated qty is greater than the  available source qty"
            MsgBox strMessage, vbOKOnly, Me.Caption
            Exec_Validate = -1
            Exit Function
        End If
        
   Next iC1
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::Exec_Validate", Now, gDRGeneralError, True, Err
End Function

Private Function Exec_SendOrderToHost()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim RtnCode As Integer
    'Set AIM_DxAO public variables
    If Not Trim(ById) = "" Then
        'Display the Send PO To Host Screen
        AIM_DXAO.ById = ById
        
        AIM_DXAO.Show vbModal, AIM_Main
        
        If Not AIM_DXAO.CancelFlag Then
            AllocationState = "PRE"
            ById = GetCurrentById
            'Build the Tree View
            RtnCode = BldTreeList("A")
            Refresh_Form
            atAllocDetail.Tabs(2).Enabled = False
            atAllocDetail.SelectedTab = "AllocationDetail"
            tbNavigation.Tools(2).Visible = True
            tbNavigation.Tools(8).Visible = False
            dgAllocDetail.Columns("RelOpt").Visible = True
            'Update to database required
            Me.dgAllocDetail.Update
        End If
    Else
        'Refresh_Form
    End If
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::Exec_SendOrderToHost", Now, gDRGeneralError, True, Err
End Function

Private Function Exec_AllocationSave() As Integer
On Error GoTo ErrorHandler
    
    'Call the storedprocedure to save the data
    Dim iC As Integer
    
    For iC = 0 To UBound(UBArray, 2) - 1
        With AIM_AllocationReviewSave_Sp
            .Parameters("@OrdNbr").Value = UBArray(1, iC) 'OrdNbr
            .Parameters("@Item").Value = UBArray(5, iC) 'Item
            .Parameters("@LType").Value = UBArray(3, iC) 'LType
            .Parameters("@Qty").Value = UBArray(9, iC) 'Adjusted Order Qty
            .Parameters("@Lcid").Value = UBArray(2, iC) 'LocationId
            .Parameters("@LineNbr").Value = UBArray(12, iC) 'LineNbr
            'Run
            .Execute
        End With
    Next iC
        
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::Exec_AllocationSave", Now, gDRGeneralError, True, Err
End Function

Private Function Exec_PopulateDestionation(ArgOrdNbr As String, ArgAdjustedQty As Double, ArgLineNbr As String)
On Error GoTo ErrorHandler
    
    'Populate other field in the UBArray with corresponding data
    Dim strMessage As String
    Dim RtnCode As Integer
    Dim iC As Integer

    For iC = 0 To UBound(UBArray, 2) - 1
        If UBArray(1, iC) = ArgOrdNbr _
        And UBArray(3, iC) = "D" _
        And UBArray(12, iC) = ArgLineNbr _
        Then   'OrdNbr
            UBArray(9, iC) = CStr(CInt(UBArray(9, iC)) + ArgAdjustedQty)  'Adjust the Adjusted OrderQty
            UBArray(10, iC) = CStr(CInt(UBArray(9, iC)) - CInt(UBArray(7, iC))) 'Calculate the Difference
            'UBArray(11, iC) = CStr(100 * (CInt(UBArray(9, iC)) - CInt(UBArray(7, iC))) / CInt(UBArray(7, iC))) 'Calc the Line Item Pct
            UBArray(11, iC) = CStr(100 * (CInt(UBArray(9, iC))) / CInt(UBArray(7, iC))) 'Calc the Line Item Pct
        End If
    Next iC

Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::PopulateDestionation", Now, gDRGeneralError, True, Err
End Function

Private Function Exec_ItemDetail()
On Error GoTo ErrorHandler
    
    Dim iCol As Integer
    Dim iC As Integer
    Dim AllocListBookMark As Variant
    
    AllocListBookMark = Me.dgAllocList.GetBookmark(0)
    rsAllocationSummary.Bookmark = AllocListBookMark
    
    'Get Bookmark and set caption
    rsAllocationDetail.Bookmark = Me.dgAllocDetail.GetBookmark(0)
    
    'Store the bookmark so when we tab back we know where we came from
    AllocDetailBookMark = Me.dgAllocDetail.GetBookmark(0)
    
    'Retrieve the On-Hand positions for the item
    With AIM_AllocationEdit_Sp
        .Parameters("@Item").Value = rsAllocationDetail("Item").Value
        .Parameters("@OrdNbr").Value = Trim(rsAllocationSummary("OrdNbr").Value)
    End With

    If f_IsRecordsetValidAndOpen(rsAllocationEdit) Then rsAllocationEdit.Close
    Set rsAllocationEdit = Nothing
    Set rsAllocationEdit = New ADODB.Recordset
    With rsAllocationEdit
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With

    rsAllocationEdit.Open AIM_AllocationEdit_Sp
    
    'Populate the rsALlocationEdit recordset values to the UBArray Xarray
'    iCols = 12
    ReDim UBArray(0 To (iCols), 0 To rsAllocationEdit.RecordCount) As String
    iCount = -1
    rsAllocationEdit.AbsolutePosition = 1
    For iC = 0 To UBound(UBArray, 2) - 1
        For iCol = 0 To (iCols)
            UBArray(iCol, iC) = rsAllocationEdit(iCol).Value
        Next iCol
        rsAllocationEdit.AbsolutePosition = iC + 2 'Move the postion to next record
        iCount = iCount + 1
    Next iC
    
    'Rebind the dgAllocEditQty grid to show the data
    Me.dgAllocEditQty.ReBind

Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::Exec_ItemDetail", Now, gDRGeneralError, True, Err
End Function

Private Function Refresh_Form()
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim lngCounter As Long
    Dim strClear, strZero As String
    
    strClear = ""
    strZero = "0"
    
    If IsNull(dgAllocList.Bookmark) Then
        'Update the Allocation Summary Display
        Me.txtDesc = strClear
        Me.txtDestId = strClear
        Me.txtDivision = strClear
        Me.txtOrderFillPct = strZero
        Me.txtRank = strZero
        Me.txtRegion = strClear
        Me.txtStatus = strClear
        Me.txtTotalLines = ""
        Me.txtTotalUnits = ""
        Me.txtType = strClear
        Me.txtUserDefined = strClear
        Reset_Form
    Else
        If Not (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) Then Exit Function
        
        'Sync the Vendor Summary
        If Not IsNull(Me.dgAllocList.Bookmark) Then rsAllocationSummary.Bookmark = Me.dgAllocList.Bookmark
        
        'Update the Module Level Variables
        OrdNbr = rsAllocationSummary("OrdNbr").Value
        LcId = rsAllocationSummary("Lcid").Value
        'Update the Allocation Summary Display
        
        Me.txtDesc = IIf(IsNull(rsAllocationSummary("Lname").Value), strClear, rsAllocationSummary("Lname").Value)
        Me.txtDestId = IIf(IsNull(rsAllocationSummary("Lcid").Value), strClear, rsAllocationSummary("Lcid").Value)
        Me.txtDivision = IIf(IsNull(rsAllocationSummary("LDivision").Value), strClear, rsAllocationSummary("LDivision").Value)
        Me.txtOrderFillPct = IIf(IsNull(rsAllocationSummary("OrdFillPct").Value), strZero, rsAllocationSummary("OrdFillPct").Value)
        Me.txtRank = IIf(IsNull(rsAllocationSummary("LRank").Value), strZero, rsAllocationSummary("LRank").Value)
        Me.txtRegion = IIf(IsNull(rsAllocationSummary("LRegion").Value), strClear, rsAllocationSummary("LRegion").Value)
        Me.txtStatus = IIf(IsNull(rsAllocationSummary("LStatus").Value), strClear, rsAllocationSummary("LStatus").Value)
        Me.txtTotalLines = IIf(IsNull(rsAllocationSummary("OrdLineCount").Value), strZero, rsAllocationSummary("OrdLineCount").Value)
        Me.txtTotalUnits = IIf(IsNull(rsAllocationSummary("OrdTotalUnits").Value), strZero, rsAllocationSummary("OrdTotalUnits").Value)
        Me.txtType = IIf(IsNull(rsAllocationSummary("LType").Value), strClear, rsAllocationSummary("LType").Value)
        Me.txtUserDefined = IIf(IsNull(rsAllocationSummary("LUserDefined").Value), strClear, rsAllocationSummary("LUserDefined").Value)
        Me.txtOrdNbr = rsAllocationSummary("OrdNbr").Value
        
    End If

    Refresh_AllocationDetail
   
   'Reset the tab control
    Me.atAllocDetail.SelectedTab = "AllocationDetail"

Exit Function
ErrorHandler:
    If Err.Number = 3219 _
    Or Err.Number = 380 _
    Or Err.Number = 438 Then
        'Recordset.close error, ignore
        Resume Next
    Else
        f_HandleErr , , , "AIM_AllocationManager::Refresh_Form", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function Refresh_AllocationDetail()
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim lngCounter As Long
    
    If f_IsRecordsetOpenAndPopulated(rsAllocationSummary) Then
        'Call the Vendor Detail Stored Procedure
        With AIM_AllocationDetail_Sp
            .Parameters("@OrdNbr").Value = Trim(rsAllocationSummary("OrdNbr").Value)
            .Parameters("@Lcid").Value = Trim(rsAllocationSummary("Lcid").Value)
            .Parameters("@AllocationStatus").Value = AllocationState
        End With
    
        If f_IsRecordsetValidAndOpen(rsAllocationDetail) Then rsAllocationDetail.Close
        Set rsAllocationDetail = Nothing
        Set rsAllocationDetail = New ADODB.Recordset
        With rsAllocationDetail
            .CursorLocation = adUseClient
            .CursorType = adOpenDynamic
            .LockType = adLockOptimistic
            '.Filter = "soq > 0 "
        End With
        
        rsAllocationDetail.Open AIM_AllocationDetail_Sp
        
        'Check for errors
        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG01000")
            If StrComp(strMessage, "ERRMSG01000") = 0 Then strMessage = "Error loading Allocation Detail. "
            ADOErrorHandler Cn, strMessage
            Exit Function
        
        End If
    End If

    'Rebind the grid
    Me.dgAllocDetail.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAllocationDetail) Then Me.dgAllocDetail.Rows = rsAllocationDetail.RecordCount
    
    'If the bookmark is valid set it to the row
    If Not IsEmpty(AllocDetailBookMark) Then Me.dgAllocDetail.Bookmark = AllocDetailBookMark
 
    'Check for access and lock controls accordingly
    'SetAccessLevel
    
Exit Function
ErrorHandler:
    If Err.Number = 3219 _
    Or Err.Number = 380 _
    Or Err.Number = 438 Then
        'Recordset.close error, ignore
        Resume Next
    Else
        f_HandleErr , , , "AIM_AllocationManager::Refresh_AllocationDetail", Now, gDRGeneralError, True, Err
    End If
End Function

Private Sub Reset_Form()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    
    'Initialize the Positioning Variables
    OrdNbr = ""
    LcId = ""
        
    'Initialize the Vendor Detail Record Set
    If f_IsRecordsetValidAndOpen(rsAllocationDetail) Then rsAllocationDetail.Close
    With rsAllocationDetail
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        Resume Next
    Else
        f_HandleErr , , , "AIM_AllocationManager::Reset_Form", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Function BldTreeList(FilterOpt As String) As Integer
On Error GoTo ErrorHandler
    
    'Build the AllocationSummary Recordset
    AIM_AllocationSummary_Sp.Parameters("@ReviewerId").Value = ById
    AIM_AllocationSummary_Sp.Parameters("@AllocationStatus").Value = AllocationState
    
    If f_IsRecordsetValidAndOpen(rsAllocationSummary) Then rsAllocationSummary.Close
    Set rsAllocationSummary = Nothing
    Set rsAllocationSummary = New ADODB.Recordset
    With rsAllocationSummary
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    rsAllocationSummary.Open AIM_AllocationSummary_Sp
    
    'Reset the Number of Allocation Orders in  the list
    If f_IsRecordsetOpenAndPopulated(rsAllocationSummary) Then
        NbrAllocOrders = rsAllocationSummary.RecordCount
        
    Else
        NbrAllocOrders = 0
        BldTreeList = -1
    End If
    
    'Rebind the grid
    Me.dgAllocList.ReBind
    Me.dgAllocList.Rows = NbrAllocOrders
    BldTreeList = NbrAllocOrders
    
Exit Function
ErrorHandler:
    If Err.Number = 3219 Then
        Resume Next
    Else
        f_HandleErr , , , "AIM_AllocationManager::BldTreeList", Now, gDRGeneralError, True, Err
    End If
End Function


Private Function InitColWidths()
On Error GoTo ErrorHandler

    ColWidth(0) = 400     'Column(0)RelOpt              "R"
    ColWidth(1) = 400     'Column(1)excpt               "X"
    ColWidth(2) = 1000    'Column(2)LineNbr             "Line"
    ColWidth(3) = 2000    'Column(3)Item                "Item"
    ColWidth(4) = 4000    'Column(4)ItDesc              "Description"
    ColWidth(5) = 1450    'Column(5)RequestedQty        "Requested Qty"
    ColWidth(6) = 1300    'Column(6)AllocatedQty        "Allocated Qty"
    ColWidth(7) = 1300    'Column(7)AdjustedAllocQty    "Adjusted Qty"
    ColWidth(8) = 1200    'Column(8)Difference          "Difference"
    ColWidth(9) = 1100    'Column(9)LineFillPct         "Line Fill Pct"
    

Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::InitColWidths", Now, gDRGeneralError, True, Err
End Function

Private Sub atAllocDetail_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim strMessage As String
    
    'Check for a valid row in the grid
    If Me.dgAllocDetail.Row < 0 _
    Or Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) _
    Or IsNull(dgAllocDetail.Bookmark) _
    Then
        Cancel = True
        Exit Sub
    End If
    
    Screen.MousePointer = vbHourglass
    
    Select Case NewTab.Key
    Case "AllocationDetail"
        tbNavigation.Tools(7).Enabled = False
        dgAllocEditQty.Update
        'Save changes if any
        Exec_AllocationSave
        Refresh_AllocationDetail
    
    Case "ItemDetail"
        'Set value of Item text boxes to match those from the rsAllocationDetail recordset
        Exec_ItemDetail
        tbNavigation.Tools(7).Enabled = True
    End Select

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    If Err.Number = 3219 Then
        Resume Next
    Else
        f_HandleErr , , , "AIM_AllocationManager::atBuyerReviewTab_BeforeTabClick", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub atAllocDetail_Click()
On Error GoTo ErrorHandler
    
    FocusLocation = "SUMMARY"

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::atAllocDetail_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_Click()
On Error GoTo ErrorHandler
    
    FocusLocation = "DETAIL"

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) Then Exit Sub
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
        
    If InStr(rsAllocationDetail.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgAllocDetail.Columns(ColIndex).Name
    ElseIf InStr(rsAllocationDetail.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgAllocDetail.Columns(ColIndex).Name
    End If
    
    If ColName = "RelOpt" Then
        ColName = "LineItemStatus"
    ElseIf ColName = "excpt" Then
        ColName = "Exception"
    ElseIf ColName = "LineFillPct" Then
        ColName = "ItemFillPercentage"
    End If

    'Sort grid by selected column
    rsAllocationDetail.Sort = Trim(ColName & " " & SortSeq)

    Me.dgAllocDetail.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAllocationDetail) Then Me.dgAllocDetail.Rows = rsAllocationDetail.RecordCount
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
     'Define Styles
    Me.dgAllocDetail.StyleSets.Add ("LT_Excpt")
    'Me.dgAllocList.StyleSets("LT_Excpt").BackColor = &H80FFFF  '(Yellow; RGB=255, 255, 128)
    Me.dgAllocDetail.StyleSets("LT_Excpt").Picture = LoadResPicture("ORDER_HARDVIOLATION", vbResBitmap)
    
    Me.dgAllocDetail.StyleSets.Add ("LR_Excpt")
    'Me.dgAllocList.StyleSets("LR_Excpt").BackColor = &HFFFFFF  '(White; RGB=255, 255, 255) Modify, since white implies editable
    Me.dgAllocDetail.StyleSets("LR_Excpt").Picture = LoadResPicture("ORDER_SOFTVIOLATION", vbResBitmap)
    
    'Define Style Sets
   
    Me.dgAllocDetail.StyleSets.Add ("AdjQty")
    Me.dgAllocDetail.StyleSets("AdjQty").Font.Bold = True

    'Define columns
    Me.dgAllocDetail.Columns(0).Name = "RelOpt"
    Me.dgAllocDetail.Columns(0).Caption = "R"
    Me.dgAllocDetail.Columns(0).Width = ColWidth(0)
    Me.dgAllocDetail.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgAllocDetail.Columns(0).Style = ssStyleCheckBox
    Me.dgAllocDetail.Columns(0).Locked = False

    Me.dgAllocDetail.Columns(1).Name = "excpt"
    Me.dgAllocDetail.Columns(1).Caption = "X"
    Me.dgAllocDetail.Columns(1).Width = ColWidth(1)
    Me.dgAllocDetail.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(1).Alignment = ssCaptionAlignmentCenter
    Me.dgAllocDetail.Columns(1).Style = ssStyleButton
    Me.dgAllocDetail.Columns(1).ButtonsAlways = True
    Me.dgAllocDetail.Columns(1).DataType = vbBoolean
    Me.dgAllocDetail.Columns(1).Locked = True
    
    Me.dgAllocDetail.Columns(2).Name = "LineNbr"
    Me.dgAllocDetail.Columns(2).Caption = "Line Nbr"
    Me.dgAllocDetail.Columns(2).Width = ColWidth(2)
    Me.dgAllocDetail.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocDetail.Columns(2).Locked = True
    
    Me.dgAllocDetail.Columns(3).Name = "item"
    Me.dgAllocDetail.Columns(3).Caption = "Item"
    Me.dgAllocDetail.Columns(3).Width = ColWidth(3)
    Me.dgAllocDetail.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocDetail.Columns(3).Locked = True

    Me.dgAllocDetail.Columns(4).Name = "itdesc"
    Me.dgAllocDetail.Columns(4).Caption = "Description"
    Me.dgAllocDetail.Columns(4).Width = ColWidth(4)
    Me.dgAllocDetail.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocDetail.Columns(4).Locked = True

    Me.dgAllocDetail.Columns(5).Name = "RequestedQty"
    Me.dgAllocDetail.Columns(5).Caption = "Requested Qty"
    Me.dgAllocDetail.Columns(5).Width = ColWidth(5)
    Me.dgAllocDetail.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgAllocDetail.Columns(5).NumberFormat = "0"
    Me.dgAllocDetail.Columns(5).DataType = vbLong
    Me.dgAllocDetail.Columns(5).Locked = True
   
    Me.dgAllocDetail.Columns(6).Name = "AllocatedQty"
    Me.dgAllocDetail.Columns(6).Caption = "Allocated Qty"
    Me.dgAllocDetail.Columns(6).Width = ColWidth(6)
    Me.dgAllocDetail.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgAllocDetail.Columns(6).NumberFormat = "0"
    Me.dgAllocDetail.Columns(6).DataType = vbLong
    Me.dgAllocDetail.Columns(6).Locked = True

    Me.dgAllocDetail.Columns(7).Name = "AdjustedAllocQty"
    Me.dgAllocDetail.Columns(7).Caption = "Adjusted Qty"
    Me.dgAllocDetail.Columns(7).Width = ColWidth(7)
    Me.dgAllocDetail.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgAllocDetail.Columns(7).NumberFormat = "0"
    Me.dgAllocDetail.Columns(7).DataType = vbLong
    Me.dgAllocDetail.Columns(7).Locked = True
    Me.dgAllocDetail.Columns(7).StyleSet = "AdjQty"
    Me.dgAllocDetail.Columns(7).Selected = False
    
    Me.dgAllocDetail.Columns(8).Name = "Difference"
    Me.dgAllocDetail.Columns(8).Caption = "Difference"
    Me.dgAllocDetail.Columns(8).Width = ColWidth(8)
    Me.dgAllocDetail.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocDetail.Columns(8).Alignment = ssCaptionAlignmentRight
    Me.dgAllocDetail.Columns(8).NumberFormat = "0"
    Me.dgAllocDetail.Columns(8).DataType = vbLong
    Me.dgAllocDetail.Columns(8).Locked = True
    Me.dgAllocDetail.Columns(8).Selected = False
    
    Me.dgAllocDetail.Columns(9).Name = "LineFillPct"
    Me.dgAllocDetail.Columns(9).Caption = "Line Fill Pct"
    Me.dgAllocDetail.Columns(9).Width = ColWidth(9)
    Me.dgAllocDetail.Columns(9).Locked = True
    Me.dgAllocDetail.Columns(9).NumberFormat = "##"
    Me.dgAllocDetail.Columns(9).Alignment = ssCaptionAlignmentRight

    'Set the Grid Font
    'Me.dgAllocDetail.Font.Size = SMALLFONT

    'Turn on the Columns Loaded Flag
    ColLoaded_Detail = True
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgAllocDetail.Font
    SetProperFont Me.dgAllocDetail.HeadFont
    SetProperFont Me.dgAllocDetail.PageFooterFont
    SetProperFont Me.dgAllocDetail.PageHeaderFont
    Me.dgAllocDetail.Columns(0).Caption = getTranslationResource("R")
    Me.dgAllocDetail.Columns(1).Caption = getTranslationResource("X")
    Me.dgAllocDetail.Columns(2).Caption = getTranslationResource("Line Nbr")
    Me.dgAllocDetail.Columns(3).Caption = getTranslationResource("Item")
    Me.dgAllocDetail.Columns(4).Caption = getTranslationResource("Description")
    Me.dgAllocDetail.Columns(5).Caption = getTranslationResource("Requested Qty")
    Me.dgAllocDetail.Columns(6).Caption = getTranslationResource("Allocated Qty")
    Me.dgAllocDetail.Columns(7).Caption = getTranslationResource("Adjusted Qty")
    Me.dgAllocDetail.Columns(8).Caption = getTranslationResource("Difference")
    Me.dgAllocDetail.Columns(9).Caption = getTranslationResource("Line Fill Pct")
'    'Set Splitter Position
 '   Me.dgAllocDetail.SplitterPos = SPLITTERPOSITION
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAllocDetail, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgAllocDetail.Columns.Count - 1
'        dgAllocDetail.Columns(IndexCounter).HasHeadBackColor = True
'        dgAllocDetail.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAllocDetail.Columns(IndexCounter).Locked = False Then dgAllocDetail.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
       
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuAllocationDetails
    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_RowLoaded(ByVal Bookmark As Variant)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer

    If IsEmpty(Bookmark) _
    Or Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) _
    Then
        Exit Sub
    End If

    rsAllocationDetail.Bookmark = Bookmark

    'Set the Color Coding
    Select Case rsAllocationDetail("Exception").Value
        Case "Y"         'Lead Time/Priority Exception
            Me.dgAllocDetail.Columns("excpt").CellStyleSet "LT_Excpt"

        Case Else        'Line Review
            Me.dgAllocDetail.Columns("excpt").CellStyleSet "LR_Excpt"

    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_RowLoaded", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) Then Exit Sub
    
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsAllocationDetail.MoveLast
        Else
            rsAllocationDetail.MoveFirst
        End If
    Else
        rsAllocationDetail.Bookmark = StartLocation
    End If

    'Note: Do not use StartLocation here - it could be null
    rsAllocationDetail.Move NumberOfRowsToMove

    NewLocation = rsAllocationDetail.Bookmark
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer
    Dim ErrorText As String
    
    If Not ColLoaded_Detail Then Exit Sub
    
    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) _
    Or Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) _
    Then
        Exit Sub
    End If
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAllocationDetail.MoveLast
        Else
            rsAllocationDetail.MoveFirst
        End If
    Else
        rsAllocationDetail.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAllocationDetail.MovePrevious
        Else
            rsAllocationDetail.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsAllocationDetail.BOF Or rsAllocationDetail.eof Then Exit For
        'Item Status
        If AllocationState = "PRE" Then
            Select Case rsAllocationDetail("LineItemStatus").Value
            Case OrdStats.STAT_PENDING
                RowBuf.Value(i, 0) = True
            Case OrdStats.STAT_DO_NOT_ALLOCATE
                RowBuf.Value(i, 0) = False
            End Select
        ElseIf AllocationState = "POST" Then
            Select Case rsAllocationDetail("LineItemStatus").Value
            Case OrdStats.STAT_RELEASED
                RowBuf.Value(i, 0) = True
            Case OrdStats.STAT_ALLOCATED, OrdStats.STAT_NOT_ALLOCATED
                RowBuf.Value(i, 0) = False
            End Select
        End If
        'Buyer Alerts/Exceptions
        RowBuf.Value(i, 1) = "" 'since we do not want to display text in this column
        'the rest
        RowBuf.Value(i, 2) = rsAllocationDetail("LineNbr").Value
        RowBuf.Value(i, 3) = rsAllocationDetail("item").Value
        RowBuf.Value(i, 4) = rsAllocationDetail("itDesc").Value
        RowBuf.Value(i, 5) = rsAllocationDetail("RequestedQty").Value
        RowBuf.Value(i, 6) = rsAllocationDetail("AllocatedQty").Value
        RowBuf.Value(i, 7) = rsAllocationDetail("AdjustedAllocQty").Value
        RowBuf.Value(i, 8) = rsAllocationDetail("Difference").Value
        RowBuf.Value(i, 9) = rsAllocationDetail("ItemFillPercentage").Value

        RowBuf.Bookmark(i) = rsAllocationDetail.Bookmark
            
        If ReadPriorRows Then
            rsAllocationDetail.MovePrevious
        Else
            rsAllocationDetail.MoveNext
        End If
        
        r = r + 1
        If rsAllocationDetail.eof Then
            rsAllocationDetail.MovePrevious
            Exit For
        End If
        If rsAllocationDetail.BOF Then
            rsAllocationDetail.MoveNext
            Exit For
        End If
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocDetail_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) Then Exit Sub
    If Not dgAllocDetail.AllowUpdate Then Exit Sub
    
    'Position to updated record
    rsAllocationDetail.Bookmark = WriteLocation

    'Update the Order Status
    If Not IsNull(RowBuf.Value(0, 0)) Then
        If RowBuf.Value(0, 0) Then
            rsAllocationDetail("LineItemStatus").Value = OrdStats.STAT_PENDING
        Else
            rsAllocationDetail("LineItemStatus").Value = OrdStats.STAT_DO_NOT_ALLOCATE
        End If
    End If
    If Not IsNull(RowBuf.Value(0, 7)) Then
        rsAllocationDetail("AdjustedAllocQty").Value = RowBuf.Value(0, 7)
    End If
    
    'Update the row
    rsAllocationDetail.Update
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocDetail_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgAllocEditQty_AfterUpdate(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    dgAllocEditQty.Refresh

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_AfterUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    NoChange = True
    If dgAllocEditQty.Columns(3).Value = "" Then
        dgAllocEditQty.Columns("Allocate").Value = OldValue
        NoChange = False
        strMessage = getTranslationResource("MSGBOX07802")
        If StrComp(strMessage, "MSGBOX07802") = 0 Then strMessage = "Only source locations can be edited"
        MsgBox strMessage, vbOKOnly, Me.Caption
    End If
    OrgAdjustedAllocQty = OldValue

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dgAllocEditQty.StyleSets.Add ("AdjQty")
    Me.dgAllocEditQty.StyleSets("AdjQty").Font.Bold = True
   
    'Define Columns
    Me.dgAllocEditQty.Columns(0).Name = "Allocate"
    Me.dgAllocEditQty.Columns(0).Caption = "Allocate"
    Me.dgAllocEditQty.Columns(0).Width = 400
    Me.dgAllocEditQty.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgAllocEditQty.Columns(0).Style = ssStyleCheckBox
    Me.dgAllocEditQty.Columns(0).Locked = False
    Me.dgAllocEditQty.Columns(0).Visible = False
     
    Me.dgAllocEditQty.Columns(1).Name = "OrdNbr"
    Me.dgAllocEditQty.Columns(1).Caption = "Ord Nbr"
    Me.dgAllocEditQty.Columns(1).Width = 1000
    Me.dgAllocEditQty.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocEditQty.Columns(1).Locked = True
    
    Me.dgAllocEditQty.Columns(2).Name = "LcidDestination"
    Me.dgAllocEditQty.Columns(2).Caption = "Destination"
    Me.dgAllocEditQty.Columns(2).Width = 1600
    Me.dgAllocEditQty.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocEditQty.Columns(2).Locked = True
    
    Me.dgAllocEditQty.Columns(3).Name = "LcidSource"
    Me.dgAllocEditQty.Columns(3).Caption = "Source"
    Me.dgAllocEditQty.Columns(3).Width = 1600
    Me.dgAllocEditQty.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocEditQty.Columns(3).Locked = True
    
    Me.dgAllocEditQty.Columns(4).Name = "item"
    Me.dgAllocEditQty.Columns(4).Caption = " Item"
    Me.dgAllocEditQty.Columns(4).Width = 1500
    Me.dgAllocEditQty.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocEditQty.Columns(4).Locked = True

    Me.dgAllocEditQty.Columns(5).Name = "itdesc"
    Me.dgAllocEditQty.Columns(5).Caption = " Item Description"
    Me.dgAllocEditQty.Columns(5).Width = 3500
    Me.dgAllocEditQty.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(5).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocEditQty.Columns(5).Locked = True

    Me.dgAllocEditQty.Columns(6).Name = "RequestedQty"
    Me.dgAllocEditQty.Columns(6).Caption = "Requested Qty"
    Me.dgAllocEditQty.Columns(6).Width = 1000
    Me.dgAllocEditQty.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgAllocEditQty.Columns(6).NumberFormat = "0"
    Me.dgAllocEditQty.Columns(6).DataType = vbLong
    Me.dgAllocEditQty.Columns(6).Locked = True
   
    
    Me.dgAllocEditQty.Columns(7).Name = "AllocQty"
    Me.dgAllocEditQty.Columns(7).Caption = "Allocated Qty"
    Me.dgAllocEditQty.Columns(7).Width = 1000
    Me.dgAllocEditQty.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgAllocEditQty.Columns(7).NumberFormat = "0"
    Me.dgAllocEditQty.Columns(7).DataType = vbLong
    Me.dgAllocEditQty.Columns(7).Locked = True
 
    
    Me.dgAllocEditQty.Columns(8).Name = "AdjustedAllocQty"
    Me.dgAllocEditQty.Columns(8).Caption = "Adjusted Qty"
    Me.dgAllocEditQty.Columns(8).Width = 1000
    Me.dgAllocEditQty.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(8).Alignment = ssCaptionAlignmentRight
    Me.dgAllocEditQty.Columns(8).NumberFormat = "0"
    Me.dgAllocEditQty.Columns(8).DataType = vbLong
    Me.dgAllocEditQty.Columns(8).Locked = False
    Me.dgAllocEditQty.Columns(8).StyleSet = "AdjQty"
  
    
    
    Me.dgAllocEditQty.Columns(9).Name = "Difference"
    Me.dgAllocEditQty.Columns(9).Caption = "Difference"
    Me.dgAllocEditQty.Columns(9).Width = 1000
    Me.dgAllocEditQty.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgAllocEditQty.Columns(9).NumberFormat = "0"
    Me.dgAllocEditQty.Columns(9).DataType = vbLong
    Me.dgAllocEditQty.Columns(9).Locked = True
    Me.dgAllocEditQty.Columns(9).Selected = False
    
    Me.dgAllocEditQty.Columns(10).Name = "Line Fill Pct"
    Me.dgAllocEditQty.Columns(10).Caption = getTranslationResource("Line Fill Pct")
    Me.dgAllocEditQty.Columns(10).Width = 900
    Me.dgAllocEditQty.Columns(10).Locked = True
    Me.dgAllocEditQty.Columns(10).NumberFormat = "##"
    Me.dgAllocEditQty.Columns(10).Alignment = ssCaptionAlignmentRight

    Me.dgAllocEditQty.Columns(11).Name = "LineNbr"
    Me.dgAllocEditQty.Columns(11).Caption = "Line Nbr"
    Me.dgAllocEditQty.Columns(11).Width = ColWidth(2)
    Me.dgAllocEditQty.Columns(11).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocEditQty.Columns(11).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocEditQty.Columns(11).Locked = True
    Me.dgAllocEditQty.Columns(11).Visible = False
    
    'Turn on the Columns Loaded Flag
    ColLoaded_Detail = True
      
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgAllocEditQty.Font
    SetProperFont Me.dgAllocEditQty.HeadFont
    SetProperFont Me.dgAllocEditQty.PageFooterFont
    SetProperFont Me.dgAllocEditQty.PageHeaderFont
    Me.dgAllocEditQty.Columns(0).Caption = getTranslationResource("Allocate")
    Me.dgAllocEditQty.Columns(1).Caption = getTranslationResource("Ord Nbr")
    Me.dgAllocEditQty.Columns(2).Caption = getTranslationResource("Destination")
    Me.dgAllocEditQty.Columns(3).Caption = getTranslationResource("Source")
    Me.dgAllocEditQty.Columns(4).Caption = getTranslationResource("Item")
    Me.dgAllocEditQty.Columns(5).Caption = getTranslationResource("Item Description")
    Me.dgAllocEditQty.Columns(6).Caption = getTranslationResource("Requested Qty")
    Me.dgAllocEditQty.Columns(7).Caption = getTranslationResource("Allocated Qty")
    Me.dgAllocEditQty.Columns(8).Caption = getTranslationResource("Adjusted  Qty")
    Me.dgAllocEditQty.Columns(9).Caption = getTranslationResource("Difference")
    Me.dgAllocEditQty.Columns(10).Caption = getTranslationResource("Line Fill Pct")
    Me.dgAllocEditQty.Columns(11).Caption = getTranslationResource("Line Nbr")
'    'Set Splitter Position
 '   Me.dgAllocEditQty.SplitterPos = SPLITTERPOSITION
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAllocEditQty, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgAllocEditQty.Columns.Count - 1
'        dgAllocEditQty.Columns(IndexCounter).HasHeadBackColor = True
'        dgAllocEditQty.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAllocEditQty.Columns(IndexCounter).Locked = False Then dgAllocEditQty.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
       
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuAllocationEdit
    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_RowLoaded(ByVal Bookmark As Variant)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer

    If IsEmpty(Bookmark) Then Exit Sub

    'Set the Color Coding
    Select Case UBArray(3, Bookmark)
    Case "D"         'If Destination then set LcidSource to blank
        Me.dgAllocEditQty.Columns("LcIdSource").Value = ""
        
    Case "S"       'If Source then set the following field to blank
        Me.dgAllocEditQty.Columns("LcIdDestination").Value = ""
        Me.dgAllocEditQty.Columns("RequestedQty").Value = ""
        Me.dgAllocEditQty.Columns("Difference").Value = ""
        Me.dgAllocEditQty.Columns("Line Fill Pct").Value = ""
             
    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_RowLoaded", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim iRBRow As Integer
    Dim iCol As Integer
    Dim irow As Integer
        
    If (UBound(UBArray, 2) <= 0) Then Exit Sub
    
    irow = 0
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            iPoint = iCount
        Else
            iPoint = 0
        End If
    Else
        iPoint = StartLocation
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
    End If
    
    For iRBRow = 0 To RowBuf.RowCount - 1
        If iPoint < 0 Or iPoint > iCount Then Exit For

        Select Case UBArray(0, iPoint) 'Allocate
        Case 0            'Pending
            RowBuf.Value(iRBRow, 0) = True
        Case 1           'Do not Allocate
            RowBuf.Value(iRBRow, 0) = False
        End Select
        RowBuf.Value(iRBRow, 1) = UBArray(1, iPoint) 'OrdNbr
        RowBuf.Value(iRBRow, 2) = UBArray(2, iPoint) 'Store 'lcid can use 5 also
        RowBuf.Value(iRBRow, 3) = UBArray(2, iPoint) 'Source 'lcid can use 5 also
        RowBuf.Value(iRBRow, 4) = UBArray(5, iPoint) 'Item
        RowBuf.Value(iRBRow, 5) = UBArray(6, iPoint) 'ItemDesc
        RowBuf.Value(iRBRow, 6) = UBArray(7, iPoint) 'OriginalOQ
        RowBuf.Value(iRBRow, 7) = UBArray(8, iPoint)  'Allocted Qty
        RowBuf.Value(iRBRow, 8) = UBArray(9, iPoint) 'Adj OQ
        RowBuf.Value(iRBRow, 9) = UBArray(10, iPoint) 'Difference
        RowBuf.Value(iRBRow, 10) = UBArray(11, iPoint) 'LineFill
        RowBuf.Value(iRBRow, 11) = UBArray(12, iPoint) 'Linenbr
'        For iCol = 0 To (iCols - 1)
'            'RowBuf.Value(iRBRow, iCol) = UBArray(iCol, iPoint)
'        Next iCol
        RowBuf.Bookmark(iRBRow) = iPoint
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
        irow = irow + 1
    Next iRBRow

    RowBuf.RowCount = irow

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocEditQty_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim iCol As Integer
    Dim Rtn As Integer

    If NoChange = False Then Exit Sub
    
    If Not IsNull(RowBuf.Value(0, 0)) Then
        If RowBuf.Value(0, 0) Then
            UBArray(iCol, CInt(WriteLocation)) = 0  'Allocated
        Else
            UBArray(iCol, CInt(WriteLocation)) = 1
        End If
    End If
    If Not IsNull(RowBuf.Value(0, 8)) Then  'AdjustedAllocQty
        UBArray(9, CInt(WriteLocation)) = RowBuf.Value(0, 8)
        Rtn = Exec_Validate(UBArray(5, CInt(WriteLocation)), dgAllocEditQty.Columns("OrdNbr").Value, dgAllocEditQty.Columns("LcidSource").Value, dgAllocEditQty.Columns("AdjustedAllocQty").Value, dgAllocEditQty.Columns("LineNbr").Value)
    
        If Rtn = -1 Then
            UBArray(9, CInt(WriteLocation)) = OrgAdjustedAllocQty   'Reset AdjustedAllocQty to Origalvalue
        Else
            Exec_PopulateDestionation dgAllocEditQty.Columns("OrdNbr").Value, (CInt(RowBuf.Value(0, 8)) - OrgAdjustedAllocQty), dgAllocEditQty.Columns("LineNbr").Value
        End If
    End If
    NoChange = False

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocEditQty_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_Click()
On Error GoTo ErrorHandler

    FocusLocation = "SUMMARY"

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) Then Exit Sub
    
    'Check for unsortable column index
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
    
    If InStr(rsAllocationSummary.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgAllocList.Columns(ColIndex).Name
    ElseIf InStr(rsAllocationSummary.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgAllocList.Columns(ColIndex).Name
    End If
    
    If ColName = "TotalLines" Then
        ColName = "LineCount"
    ElseIf ColName = "RelOpt" Then
        ColName = "OrderStatus"
    ElseIf ColName = "OrderFillPct" Then
        ColName = "OrdFillPct"
    End If

    'Sort grid by selected column
    rsAllocationSummary.Sort = Trim(ColName & " " & SortSeq)

    Me.dgAllocList.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAllocationSummary) Then Me.dgAllocList.Rows = rsAllocationSummary.RecordCount
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Define Styles
    Me.dgAllocList.StyleSets.Add ("LT_Excpt")
    'Me.dgAllocList.StyleSets("LT_Excpt").BackColor = &H80FFFF  '(Yellow; RGB=255, 255, 128)
    Me.dgAllocList.StyleSets("LT_Excpt").Picture = LoadResPicture("ORDER_HARDVIOLATION", vbResBitmap)
    
    Me.dgAllocList.StyleSets.Add ("LR_Excpt")
    'Me.dgAllocList.StyleSets("LR_Excpt").BackColor = &HFFFFFF  '(White; RGB=255, 255, 255) Modify, since white implies editable
    Me.dgAllocList.StyleSets("LR_Excpt").Picture = LoadResPicture("ORDER_SOFTVIOLATION", vbResBitmap)
    
    'Define columns
    Me.dgAllocList.Columns(0).Name = "RelOpt"
    Me.dgAllocList.Columns(0).Caption = getTranslationResource("R")
    Me.dgAllocList.Columns(0).Width = 400
    Me.dgAllocList.Columns(0).Locked = False
    Me.dgAllocList.Columns(0).Style = ssStyleCheckBox
    Me.dgAllocList.Columns(0).Alignment = ssCaptionAlignmentCenter
    
    Me.dgAllocList.Columns(1).Name = "Exception"
    Me.dgAllocList.Columns(1).Caption = "X"
    Me.dgAllocList.Columns(1).Width = 400
    Me.dgAllocList.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAllocList.Columns(1).Alignment = ssCaptionAlignmentCenter
    Me.dgAllocList.Columns(1).Style = ssStyleButton
    Me.dgAllocList.Columns(1).ButtonsAlways = True
    Me.dgAllocList.Columns(1).DataType = vbBoolean
    Me.dgAllocList.Columns(1).Locked = True
    
    
    Me.dgAllocList.Columns(2).Name = "OrdNbr"
    Me.dgAllocList.Columns(2).Caption = getTranslationResource("Order Nbr")
    Me.dgAllocList.Columns(2).Locked = True
    Me.dgAllocList.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocList.Columns(2).Width = 1500
    
    Me.dgAllocList.Columns(3).Name = "Lcid"
    Me.dgAllocList.Columns(3).Caption = getTranslationResource("Lcid")
    Me.dgAllocList.Columns(3).Locked = True
    Me.dgAllocList.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgAllocList.Columns(3).Width = 1200
    
    
    Me.dgAllocList.Columns(4).Name = "TotalUnits"
    Me.dgAllocList.Columns(4).Caption = getTranslationResource("Total Loc Units")
    Me.dgAllocList.Columns(4).Width = 1500
    Me.dgAllocList.Columns(4).Locked = True
    Me.dgAllocList.Columns(4).Alignment = ssCaptionAlignmentRight
    
    Me.dgAllocList.Columns(5).Name = "TotalLines"
    Me.dgAllocList.Columns(5).Caption = getTranslationResource("Total Loc Lines")
    Me.dgAllocList.Columns(5).Width = 1500
    Me.dgAllocList.Columns(5).Locked = True
    Me.dgAllocList.Columns(5).Alignment = ssCaptionAlignmentRight

    Me.dgAllocList.Columns(6).Name = "OrderFillPct"
    Me.dgAllocList.Columns(6).Caption = getTranslationResource("Loc Fill Pct")
    Me.dgAllocList.Columns(6).Width = 800
    Me.dgAllocList.Columns(6).Locked = True
    Me.dgAllocList.Columns(6).NumberFormat = "##"
    Me.dgAllocList.Columns(6).Alignment = ssCaptionAlignmentRight

    ColLoaded_AllocList = True
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgAllocList.Font
    SetProperFont Me.dgAllocList.HeadFont
    SetProperFont Me.dgAllocList.PageFooterFont
    SetProperFont Me.dgAllocList.PageHeaderFont
    Me.dgAllocList.Columns(0).Caption = getTranslationResource("R")
    Me.dgAllocList.Columns(1).Caption = getTranslationResource("X")
    Me.dgAllocList.Columns(2).Caption = getTranslationResource("Order Nbr")
    Me.dgAllocList.Columns(3).Name = "Lcid"
    Me.dgAllocList.Columns(3).Caption = getTranslationResource("Lcid")
    Me.dgAllocList.Columns(4).Caption = getTranslationResource("Total Loc Units")
    Me.dgAllocList.Columns(5).Caption = getTranslationResource("Total Loc Lines")
    Me.dgAllocList.Columns(6).Caption = getTranslationResource("Loc Fill Pct")
   
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAllocList, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAllocList.Columns.Count - 1
'        dgAllocList.Columns(IndexCounter).HasHeadBackColor = True
'        dgAllocList.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAllocList.Columns(IndexCounter).Locked = False Then dgAllocList.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuAllocationSummary
    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    If Me.dgAllocList.Rows <= 0 Then Exit Sub
    
    Me.atAllocDetail.SelectedTab = "AllocationDetail"
    'Force an update
    Me.dgAllocList.Update
    
    'Did the user change rows
    If Me.dgAllocList.Columns("OrdNbr").Value = OrdNbr And Me.dgAllocList.Columns("Lcid").Value = LcId Then
        Exit Sub
    End If
    
    'Display Message
    Screen.MousePointer = vbHourglass
    
    strMessage = getTranslationResource("STATMSG07802")
    If StrComp(strMessage, "STATMSG07802") = 0 Then strMessage = "Loading Allocation Detail..."
    Write_Message strMessage

    'Update to database required
    Me.dgAllocDetail.Update
    'Set it to the first row
    AllocDetailBookMark = Me.dgAllocDetail.RowBookmark(0)
    'Refresh the Form
    Refresh_Form
    
    'Me.atAllocDetail.SelectedTab = "AllocationDetail"
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_RowLoaded(ByVal Bookmark As Variant)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer

    If IsEmpty(Bookmark) _
    Or Not (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) _
    Then
        Exit Sub
    End If

    rsAllocationSummary.Bookmark = Bookmark

    'Set the Color Coding
    Select Case rsAllocationSummary("Exception").Value
    Case "Y"
        Me.dgAllocList.Columns("Exception").CellStyleSet "LT_Excpt"

    Case Else
        Me.dgAllocList.Columns("Exception").CellStyleSet "LR_Excpt"

    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_RowLoaded", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsAllocationSummary) Then Exit Sub
    
    'Align recordset
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsAllocationSummary.MoveLast
        Else
            rsAllocationSummary.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsAllocationSummary.Bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsAllocationSummary.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsAllocationSummary.Bookmark

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim iRBRow As Integer
    Dim iGridRows As Integer
    Dim iFld As Integer

    'Check for a closed recordset or a recordset with no rows
    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) Then Exit Sub
    
    If ColLoaded_AllocList = False _
    Or dgAllocList.Columns.Count < 7 _
    Or NbrAllocOrders <= 0 _
    Then
        Exit Sub
    End If
    
    iGridRows = 0

    If IsNull(StartLocation) Then           'If the Grid is empty
        If ReadPriorRows Then               'If ReadPriorRows is True
            rsAllocationSummary.MoveLast            'then the Grid is being
        Else                                    'scrolled up towards the top
            rsAllocationSummary.MoveFirst
        End If
    Else                                    'If Grid contains data
        rsAllocationSummary.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAllocationSummary.MovePrevious
        Else
            rsAllocationSummary.MoveNext
        End If
    
    End If

    For iRBRow = 0 To RowBuf.RowCount - 1
        If rsAllocationSummary.BOF Or rsAllocationSummary.eof Then Exit For
        Select Case RowBuf.ReadType
        Case ssReadTypeAllData          'All data must be read
            RowBuf.Bookmark(iRBRow) = rsAllocationSummary.Bookmark
            If AllocationState = "PRE" Then
                Select Case rsAllocationSummary("OrderStatus").Value
                Case OrdStats.STAT_PENDING
                    RowBuf.Value(iRBRow, 0) = True
                Case OrdStats.STAT_DO_NOT_ALLOCATE
                    RowBuf.Value(iRBRow, 0) = False
                End Select
            ElseIf AllocationState = "POST" Then
                Select Case rsAllocationSummary("OrderStatus").Value
                Case OrdStats.STAT_RELEASED, OrdStats.STAT_REL_WITH_EXCEPTIONS
                    RowBuf.Value(iRBRow, 0) = True
                Case OrdStats.STAT_ALLOCATED, OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                    RowBuf.Value(iRBRow, 0) = False
                End Select
            End If
        
            'Vendor Alerts/Exceptions
            RowBuf.Value(iRBRow, 1) = "" 'since we do not want to display text in this column
            RowBuf.Value(iRBRow, 2) = rsAllocationSummary("OrdNbr").Value
            RowBuf.Value(iRBRow, 3) = rsAllocationSummary("Lcid").Value
            RowBuf.Value(iRBRow, 4) = rsAllocationSummary("TotalUnits").Value
            RowBuf.Value(iRBRow, 5) = rsAllocationSummary("LineCount").Value
            RowBuf.Value(iRBRow, 6) = rsAllocationSummary("LocFillPct").Value
        
        Case ssReadTypeBookmarkOnly 'Only bookmarks must be read
            RowBuf.Bookmark(iRBRow) = rsAllocationSummary.Bookmark
        
        End Select      'Cases 2 and 3 are not used by DBGrid

        If ReadPriorRows Then
            rsAllocationSummary.MovePrevious
        Else
            rsAllocationSummary.MoveNext
        End If

        iGridRows = iGridRows + 1

    Next iRBRow

    RowBuf.RowCount = iGridRows

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAllocList_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) Then Exit Sub
    
    Screen.MousePointer = vbHourglass
    
    strMessage = getTranslationResource("STATMSG07800")
    If StrComp(strMessage, "STATMSG07800") = 0 Then strMessage = "Updating Order Status..."
    Write_Message strMessage
    
    'Position the Vendor Summary
    rsAllocationSummary.Bookmark = WriteLocation
    
    If Not IsNull(RowBuf.Value(0, 0)) Then
        Select Case RowBuf.Value(0, 0)
        Case True
            If AllocationState = "PRE" Then
                rsAllocationSummary("OrderStatus").Value = OrdStats.STAT_PENDING    ' 1 to 0
            Else
                If dgAllocList.Columns("RelOpt").Value = OrdStats.STAT_ALLOC_WITH_EXCEPTIONS Then '12 to 22
                    rsAllocationSummary("OrderStatus").Value = OrdStats.STAT_REL_WITH_EXCEPTIONS
                Else
                    rsAllocationSummary("OrderStatus").Value = OrdStats.STAT_RELEASED   '10 to 20
                End If
            End If
        
        Case False
            If AllocationState = "PRE" Then
                rsAllocationSummary("OrderStatus").Value = OrdStats.STAT_DO_NOT_ALLOCATE    '0 to 1
            Else
                If dgAllocList.Columns("RelOpt").Value = OrdStats.STAT_REL_WITH_EXCEPTIONS Then   '22 to 12
                    rsAllocationSummary("OrderStatus").Value = OrdStats.STAT_ALLOC_WITH_EXCEPTIONS
                Else
                    rsAllocationSummary("OrderStatus").Value = OrdStats.STAT_ALLOCATED   '20 to 10
                End If
            End If
        
        End Select
    End If
    
    'Update the Status code for the Vendor/Assortment
    rsAllocationSummary.Update
    
    Write_Message ""
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::dgAllocList_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
        
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    
    Refresh_Form
    
    gbUpdating = False
    Exit Sub
ErrorHandler:
    If Err.Number = 40002 Then
        Resume Next
    Else
        f_HandleErr , , , "AIM_AllocationManager::Form_Activate", Now, gDRGeneralError, True, Err
    End If
    Screen.MousePointer = vbNormal
End Sub

Private Sub Form_Load()
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Entry-point to the form
' .......................................................................
'
'   Initialize all local objects and dependencies. Initialize specific global objects.
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
        
    'Housekeeping
    Screen.MousePointer = vbHourglass
    
    strMessage = getTranslationResource("STATMSG07801")
    If StrComp(strMessage, "STATMSG07801") = 0 Then strMessage = "Initializing Allocation Summary..."
    Write_Message strMessage
    
    GetTranslatedCaptions Me
    'RemoveSemicolons
    'Initialize the Column Width for the grid
    InitColWidths
    
    AllocationState = "PRE"
    FocusLocation = "SUMMARY"
    atAllocDetail.Tabs(2).Enabled = False
    'ReDim UBArray(0 To 0, 0 To 0) As String
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False, , , Me.Caption)
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        Exit Sub
    End If
    
    'Define the ADO Command objects
    Set AIM_AllocationDetail_Sp = New ADODB.Command
    With AIM_AllocationDetail_Sp
    Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationDetail_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AllocationSummary_Sp = New ADODB.Command
    With AIM_AllocationSummary_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationSummary_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AllocationEdit_Sp = New ADODB.Command
    With AIM_AllocationEdit_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationEdit_Sp"
        .Parameters.Refresh
    End With

     Set AIM_AllocationResetItem_Sp = New ADODB.Command
    With AIM_AllocationResetItem_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationResetItem_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AllocationResetOrder_Sp = New ADODB.Command
    With AIM_AllocationResetOrder_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationResetOrder_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AllocationSourceQty_Sp = New ADODB.Command
    With AIM_AllocationSourceQty_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationSourceQty_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AllocationReviewSave_Sp = New ADODB.Command
    With AIM_AllocationReviewSave_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocationReviewSave_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the Positioning Variables
    ById = GetCurrentById
    
    'Initialize the Allocation Summary Record Set
    Set rsAllocationSummary = New ADODB.Recordset
    With rsAllocationSummary
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Initialize the Allocation Detail Record Set
    Set rsAllocationDetail = New ADODB.Recordset
    With rsAllocationDetail
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
       ' .Filter = "soq > 0 "
    End With
    
    'Initialize the Alocation Edit Record Set
    Set rsAllocationEdit = New ADODB.Recordset
    With rsAllocationEdit
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
     
    'Initialize the Alocation Edit Record Set
    Set rsAllocationSourceQty = New ADODB.Recordset
    With rsAllocationEdit
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
       
    'Initialize the Allocation Summary Record Set
    Set rsAllocationOrderSummary = New ADODB.Recordset
    With rsAllocationOrderSummary
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    
    'Build the Tree View
    RtnCode = BldTreeList("A")
    If RtnCode > 0 Then Refresh_Form
    
    'Size the form
    m_FirstTime = True
    SizeForm
    m_FirstTime = False
    iCols = 12
    
    tbNavigation.Tools(7).Enabled = False
    'Check for access and lock controls accordingly
    'SetAccessLevel
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    tbNavigation.Tools(8).Visible = False
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Exit from the form
' .......................................................................
'
'   Destroy all local objects and dependencies. Destroy specific global objects.
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAllocationDetail) Then rsAllocationDetail.Close
    Set rsAllocationDetail = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAllocationEdit) Then rsAllocationEdit.Close
    Set rsAllocationEdit = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAllocationSummary) Then rsAllocationSummary.Close
    Set rsAllocationSummary = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAllocationSourceQty) Then rsAllocationSourceQty.Close
    Set rsAllocationSourceQty = Nothing
    
    If Not (AIM_AllocationDetail_Sp Is Nothing) Then Set AIM_AllocationDetail_Sp.ActiveConnection = Nothing
    Set AIM_AllocationDetail_Sp = Nothing
    
    If Not (AIM_AllocationSummary_Sp Is Nothing) Then Set AIM_AllocationSummary_Sp.ActiveConnection = Nothing
    Set AIM_AllocationSummary_Sp = Nothing
    
    If Not (AIM_AllocationEdit_Sp Is Nothing) Then Set AIM_AllocationEdit_Sp.ActiveConnection = Nothing
    Set AIM_AllocationEdit_Sp = Nothing
    
    If Not (AIM_AllocationResetItem_Sp Is Nothing) Then Set AIM_AllocationResetItem_Sp.ActiveConnection = Nothing
    Set AIM_AllocationResetItem_Sp = Nothing
    
    If Not (AIM_AllocationResetOrder_Sp Is Nothing) Then Set AIM_AllocationResetOrder_Sp.ActiveConnection = Nothing
    Set AIM_AllocationResetOrder_Sp = Nothing
    
    If Not (AIM_AllocationSourceQty_Sp Is Nothing) Then Set AIM_AllocationSourceQty_Sp.ActiveConnection = Nothing
    Set AIM_AllocationSourceQty_Sp = Nothing
    
    If Not (AIM_AllocationReviewSave_Sp Is Nothing) Then Set AIM_AllocationReviewSave_Sp.ActiveConnection = Nothing
    Set AIM_AllocationReviewSave_Sp = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAllocationOrderSummary) Then rsAllocationOrderSummary.Close
    Set rsAllocationOrderSummary = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE)
    Set Cn = Nothing
    
    'Remove form from window list
    RemoveFmWindowList Me.Caption
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        f_HandleErr , , , "AIM_AllocationManager::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Screen.MousePointer = vbNormal
    Resume Next
End Sub

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Adjust the controls in the screen to the screen size selected by the user.
' .......................................................................
'
'   Adjust the controls in the screen to the screen size selected by the user.
'
'   Function arguments: None
'   Return values:  None
'
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function SizeForm()
On Error GoTo ErrorHandler
    
    'Derive from Buyer Review's SizeForm function
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::SizeForm", Now, gDRGeneralError, True, Err
End Function

Private Sub mnuAllocationDetailsOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim Filter As Variant
    Dim i As Integer
    Dim RtnCode As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    Dim strMessage As String
        
    If Index = 7 Or Index = 8 Or Index = 9 Then
     If Not (f_IsRecordsetValidAndOpen(rsAllocationDetail)) Then Exit Sub
     Else
        If Not (f_IsRecordsetOpenAndPopulated(rsAllocationDetail)) Then Exit Sub
    End If
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
    Case 0          'Copy to Clipboard
        Clipboard.Clear
        
        For i = 0 To rsAllocationDetail.Fields.Count - 1
            s1 = s1 & rsAllocationDetail.Fields(i).Name & vbTab
        Next i
        
        s1 = s1 & vbCrLf
            
        rsAllocationDetail.MoveFirst
        s2 = rsAllocationDetail.GetString(adClipString)
        
        Clipboard.SetText s1 & s2, vbCFText
        
    Case 1          'Copy Selected Rows to Clipboard
        Clipboard.Clear
        
        'Position Recordset
        If Me.dgAllocDetail.SelBookmarks(0) = Empty Then
            Screen.MousePointer = vbNormal
            Exit Sub
        End If
        
        For i = 0 To rsAllocationDetail.Fields.Count - 1
            s1 = s1 & rsAllocationDetail.Fields(i).Name & vbTab
        Next i
        
        s1 = s1 & vbCrLf
        
        rsAllocationDetail.Bookmark = Me.dgAllocDetail.SelBookmarks(0)
        s2 = rsAllocationDetail.GetString(adClipString, Me.dgAllocDetail.SelBookmarks.Count)

        Clipboard.SetText s1 & s2, vbCFText
    
    Case 2          'Print All Rows
        Me.dgAllocDetail.PrintData ssPrintAllRows, False, True
    
    Case 3          'Print Selected Rows
        Me.dgAllocDetail.PrintData ssPrintSelectedRows, False, True
        
    Case 4          '-- separator
    Case 5          'Restore Grid Defaults
        strMessage = getTranslationResource("MSGBOX07803")
        If StrComp(strMessage, "MSGBOX07803") = 0 Then strMessage = "Do you wish to restore this table's display to application defaults?"
        RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
        
        If RtnCode = vbYes Then
            'Restore Column Widths and positions
            For i = 0 To Me.dgAllocDetail.Columns.Count - 1
                Me.dgAllocDetail.Columns(i).Position = i
                Me.dgAllocDetail.Columns(i).Width = ColWidth(i)
            Next i
            
            Me.dgAllocDetail.Refresh
        End If
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub mnuAllocationEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim J As Integer
    Dim s() As String
    Dim s1 As String
        
    Select Case Index
    Case 0          'Copy to Clipboard
        'Check for an empty set
        If Me.dgAllocEditQty.Rows = 0 Then
            Screen.MousePointer = vbNormal
            Exit Sub
        End If

        Clipboard.Clear

        For i = 0 To Me.dgAllocEditQty.Columns.Count - 2
            If i = 3 Then
                s1 = s1 + "Source/Destination" + vbTab
            End If
            s1 = s1 + Me.dgAllocEditQty.Columns(i).Caption + vbTab
        Next i

        s1 = s1 + vbCrLf

        ReDim s(0 To UBound(UBArray, 2) - 1)

        For i = 0 To UBound(UBArray, 2) - 1
            For J = 0 To iCols - 1
                s(i) = s(i) + CStr(UBArray(J, i)) + vbTab
            Next J

        Next i

        Clipboard.SetText s1 + Join(s, vbCrLf), vbCFText
        
        Erase s

    End Select

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::mnuAllocationEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub mnuAllocationSummaryOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim J As Integer
    Dim s1 As String
    Dim s2 As String
    Dim strMessage As String
    
    If Not (f_IsRecordsetOpenAndPopulated(rsAllocationSummary)) Then Exit Sub
    
    Select Case Index
    Case 0
        'Set POStatus to Released
        If AllocationState = "PRE" Then
            Exec_ToggleReleaseAll OrdStats.STAT_PENDING, SingleOrder.DO_RELEASE
        ElseIf AllocationState = "POST" Then
           Exec_ToggleReleaseAll OrdStats.STAT_RELEASED, SingleOrder.DO_RELEASE
        End If
    
    Case 1
        'Set POStatus to Pending
        If AllocationState = "PRE" Then
            Exec_ToggleReleaseAll OrdStats.STAT_DO_NOT_ALLOCATE, SingleOrder.DO_RELEASE
        ElseIf AllocationState = "POST" Then
            Exec_ToggleReleaseAll OrdStats.STAT_ALLOCATED, SingleOrder.DO_RELEASE
        End If
    
       Case 2
        'Set POStatus to Released
        If AllocationState = "PRE" Then
            Exec_ToggleReleaseAll OrdStats.STAT_PENDING, SingleOrder.UNDO_RELEASE
        ElseIf AllocationState = "POST" Then
            Exec_ToggleReleaseAll OrdStats.STAT_RELEASED, SingleOrder.UNDO_RELEASE
        End If
    
    Case 3
        'Set POStatus to Pending
        If AllocationState = "PRE" Then
            Exec_ToggleReleaseAll OrdStats.STAT_DO_NOT_ALLOCATE, SingleOrder.UNDO_RELEASE
        ElseIf AllocationState = "POST" Then
            Exec_ToggleReleaseAll OrdStats.STAT_ALLOCATED, SingleOrder.UNDO_RELEASE
        End If
    
    Case 3          'Copy to Clipboard
        If f_IsRecordsetOpenAndPopulated(rsAllocationSummary) Then
            Clipboard.Clear
            
            For i = 0 To rsAllocationSummary.Fields.Count - 1
                s1 = s1 & rsAllocationSummary.Fields(i).Name & vbTab
            Next i
            
            s1 = s1 & vbCrLf
                
            rsAllocationSummary.MoveFirst
            s2 = rsAllocationSummary.GetString(adClipString)
            
            Clipboard.SetText s1 & s2, vbCFText
        End If
    
    Case 4          'Print
        Me.dgAllocList.PrintData ssPrintAllRows, False, True
    
    End Select

    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    f_HandleErr , , , "AIM_AllocationManager::mnuVendorSummaryOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    Me.dgAllocList.Update       'Force an update
    
    Select Case Tool.ID
    Case "ID_Refresh"
        AllocationState = "PRE"
        ById = GetCurrentById
        'Build the Tree View
        RtnCode = BldTreeList("A")
        If RtnCode > 0 Then Refresh_Form
        atAllocDetail.Tabs(2).Enabled = False
        atAllocDetail.SelectedTab = "AllocationDetail"
        tbNavigation.Tools(2).Visible = True
        tbNavigation.Tools(8).Visible = False
        dgAllocDetail.Columns("RelOpt").Visible = True
        'Update to database required
        Me.dgAllocDetail.Update
       
    Case "ID_Reset"
        'Reset the Vendor Suggested Order Quantities for all items to their original values
        Exec_ResetToOriginal
        
    Case "ID_Close"
        Unload Me
    
    Case "ID_PreAllocation"
        tbNavigation.Tools(2).Visible = False
        tbNavigation.Tools(8).Visible = True
        AllocationState = "POST"
        RtnCode = BldTreeList("A")
        Call Refresh_Form
        atAllocDetail.Tabs(2).Enabled = True
        Me.dgAllocDetail.Columns("RelOpt").Visible = False

    Case "ID_PostAllocation"
        tbNavigation.Tools(2).Visible = True
        tbNavigation.Tools(8).Visible = False
        AllocationState = "PRE"
        Call BldTreeList("A")
        Call Refresh_Form
        atAllocDetail.Tabs(2).Enabled = False
        Me.dgAllocDetail.Columns("RelOpt").Visible = True
    
    Case "ID_ReleaseAll"
        If AllocationState = "PRE" Then
            Exec_ToggleReleaseAll OrdStats.STAT_PENDING
        ElseIf AllocationState = "POST" Then
            Exec_ToggleReleaseAll OrdStats.STAT_RELEASED
        End If
    
    Case "ID_UndoReleaseAll"
       If AllocationState = "PRE" Then
            Exec_ToggleReleaseAll OrdStats.STAT_DO_NOT_ALLOCATE
        ElseIf AllocationState = "POST" Then
            Exec_ToggleReleaseAll OrdStats.STAT_ALLOCATED
        End If
        
    Case "ID_Outbound"
        'Uncomment when we are implementing the functionality fully
        'Exec_SendOrderToHost
    End Select
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AllocationManager::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
End Sub

