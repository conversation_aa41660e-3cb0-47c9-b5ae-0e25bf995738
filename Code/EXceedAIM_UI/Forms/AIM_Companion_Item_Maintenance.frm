VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_Companion_Item_Maintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Companion Demand Modeling Maintenance"
   ClientHeight    =   6330
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10215
   Icon            =   "AIM_Companion_Item_Maintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6330
   ScaleWidth      =   10215
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   150
      Top             =   5880
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_Companion_Item_Maintenance.frx":030A
      ToolBars        =   "AIM_Companion_Item_Maintenance.frx":9B94
   End
   Begin VB.Frame Frame2 
      Caption         =   "Companion Item Details"
      Height          =   4155
      Left            =   105
      TabIndex        =   11
      Top             =   1680
      Width           =   10005
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSourceOrder 
         Height          =   3705
         Left            =   120
         TabIndex        =   12
         Top             =   240
         Width           =   9720
         _Version        =   196617
         DataMode        =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         AllowAddNew     =   -1  'True
         AllowDelete     =   -1  'True
         MultiLine       =   0   'False
         AllowColumnSwapping=   0
         SelectTypeRow   =   1
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         ExtraHeight     =   212
         CaptionAlignment=   0
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   17145
         _ExtentY        =   6535
         _StockProps     =   79
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
   End
   Begin VB.Frame Frame1 
      Height          =   1545
      Left            =   105
      TabIndex        =   0
      Top             =   0
      Width           =   10005
      Begin VB.CommandButton CmdMasterItem 
         Height          =   345
         Left            =   3712
         Picture         =   "AIM_Companion_Item_Maintenance.frx":9EAC
         Style           =   1  'Graphical
         TabIndex        =   6
         Top             =   630
         Width           =   405
      End
      Begin VB.TextBox txtCompItemDesc 
         Height          =   345
         Left            =   2280
         TabIndex        =   2
         Top             =   240
         Width           =   3690
      End
      Begin VB.CommandButton CmdLcid 
         Height          =   345
         Left            =   3712
         Picture         =   "AIM_Companion_Item_Maintenance.frx":9FF6
         Style           =   1  'Graphical
         TabIndex        =   10
         Top             =   1020
         Visible         =   0   'False
         Width           =   375
      End
      Begin VB.CheckBox Check1 
         Caption         =   "Enabled"
         Height          =   345
         Left            =   6120
         TabIndex        =   3
         Top             =   240
         Width           =   2220
      End
      Begin VB.TextBox txtMasterItem 
         Height          =   345
         Left            =   2280
         TabIndex        =   5
         Top             =   630
         Width           =   1380
      End
      Begin TDBText6Ctl.TDBText txtMasterDesc 
         Height          =   345
         Left            =   4170
         TabIndex        =   7
         Top             =   630
         Width           =   4815
         _Version        =   65536
         _ExtentX        =   8493
         _ExtentY        =   609
         Caption         =   "AIM_Companion_Item_Maintenance.frx":A140
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Companion_Item_Maintenance.frx":A1AC
         Key             =   "AIM_Companion_Item_Maintenance.frx":A1CA
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   1
         ShowContextMenu =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   1
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLocation 
         Height          =   345
         Left            =   2280
         TabIndex        =   9
         Top             =   1020
         Width           =   1380
         _Version        =   65536
         _ExtentX        =   2434
         _ExtentY        =   609
         Caption         =   "AIM_Companion_Item_Maintenance.frx":A20E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Companion_Item_Maintenance.frx":A27A
         Key             =   "AIM_Companion_Item_Maintenance.frx":A298
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   1
         ShowContextMenu =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   1
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Master Item"
         Height          =   300
         Index           =   0
         Left            =   120
         TabIndex        =   4
         Top             =   675
         Width           =   2100
      End
      Begin VB.Label Label 
         Caption         =   "Model Description"
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   1
         Top             =   285
         Width           =   2100
      End
      Begin VB.Label Label 
         Caption         =   "Location"
         Height          =   300
         Index           =   2
         Left            =   120
         TabIndex        =   8
         Top             =   1065
         Width           =   2100
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_Companion_Item_Maintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim m_ActiveGridID As String

Dim Cn As ADODB.Connection

Dim AIM_CompanionItem_GetKey_Sp As New ADODB.Command
Dim AIM_SourceOrder_Get_Sp As ADODB.Command
Dim rsCompanionItemDetail As New ADODB.Recordset
Dim rsCompanionItemUpdate As New ADODB.Recordset

Dim CompanionItemBookmark As Variant
Dim LcID_BookMark As String
Dim MasterItem_BookMark As String

'Item Table Key
Dim LcId As String
Dim MasterItem  As String

Dim First_Item As String
Dim First_LcID As String

'Header
Dim ModelDesc As String
Dim MasterItemDesc As String
Dim MasterItemStat As String
Dim AddNewMaster As Boolean
Dim DetailButtonClicked As Boolean
Const ACTIVEGRID_Detail As String = "DETAIL"

Private Function rsItemInit()
On Error GoTo ErrorHandler
    
    'Create a new Item
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
    'Header
    Me.txtLocation.Text = ""
    Me.txtMasterItem.Text = ""
    Me.txtCompItemDesc.Text = ""
    Me.txtMasterDesc.Text = ""
    Me.Check1.Value = 1

    If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    Me.tbNavigation.Refresh
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(rsItemInit)"
     f_HandleErr , , , "AIM_Companion_Item_Maintenance::rsItemInit", Now, gDRGeneralError, True, Err
End Function

Private Sub f_CompanionItemHeaderSave()
On Error GoTo ErrorHandler

    Dim SQLUpdate As String, SQLInsert As String
    Dim NewMasterItem As String, NewModelDesc As String
    Dim NewMasterItemStat As String, NewLcID As String
    Dim IndexCount As Integer
    Dim ExecQuery As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String

    NewMasterItem = Trim$(txtMasterItem.Text)
    NewLcID = Trim$(txtLocation.Text)
    NewModelDesc = Trim$(txtCompItemDesc.Text)
    NewMasterItemStat = IIf(Check1.Value = 0, "Y", "N")
    
    If AddNewMaster = False _
    And StrComp(MasterItem, NewMasterItem, vbTextCompare) = 0 _
    And StrComp(LcId, NewLcID, vbTextCompare) = 0 _
    And StrComp(ModelDesc, NewModelDesc, vbTextCompare) = 0 _
    And StrComp(MasterItemStat, NewMasterItemStat, vbTextCompare) = 0 _
    Then
        Exit Sub
    End If
    
    SQLInsert = ""
    SQLInsert = "INSERT INTO AIMCompanionItem (" & vbCrLf
    SQLInsert = SQLInsert & "     MasterItem, LcID, EnableY_N, CompanionDesc " & vbCrLf
    SQLInsert = SQLInsert & ") VALUES (" & vbCrLf
    SQLInsert = SQLInsert & "     ?, ?, ?, ?" & vbCrLf
    SQLInsert = SQLInsert & ")"

    SQLUpdate = " UPDATE AIMCompanionItem SET " & vbCrLf
    SQLUpdate = SQLUpdate & " MasterItem = ?" & vbCrLf
    SQLUpdate = SQLUpdate & " , LcID = ?" & vbCrLf
    SQLUpdate = SQLUpdate & " , EnableY_N = ?" & vbCrLf
    SQLUpdate = SQLUpdate & " , CompanionDesc = ?" & vbCrLf
    SQLUpdate = SQLUpdate & " WHERE MasterItem = ?" & vbCrLf
    SQLUpdate = SQLUpdate & " AND LcID = ?"
    
    Set ExecQuery = New ADODB.Command
    With ExecQuery
        Set .ActiveConnection = Cn
        .CommandType = adCmdText
        .CommandText = IIf(AddNewMaster, SQLInsert, SQLUpdate)
        .Prepared = True
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("MasterItem", adVarWChar, adParamInput, 25)
        CmdParams.Value = NewMasterItem
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        CmdParams.Value = NewLcID
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@EnableY_N", adVarWChar, adParamInput, 12)
        CmdParams.Value = NewMasterItemStat
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@CompanionDesc", adVarWChar, adParamInput, 255)
        CmdParams.Value = NewModelDesc
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
    End With
    
    Select Case AddNewMaster
    Case False
        With ExecQuery
            'Add the Where-clause with the old master-item values
            Set CmdParams = Nothing
            Set CmdParams = .CreateParameter("MasterItem", adVarWChar, adParamInput, 25)
            CmdParams.Value = MasterItem
            .Parameters.Append CmdParams
            
            Set CmdParams = Nothing
            Set CmdParams = .CreateParameter("@LcID", adVarWChar, adParamInput, 12)
            CmdParams.Value = LcId
            .Parameters.Append CmdParams
            
            Set CmdParams = Nothing
            'Run the update
            .Execute
        End With
        'Refresh the details to refer to the updated master-item values
        If f_IsRecordsetOpenAndPopulated(rsCompanionItemDetail) Then
            rsCompanionItemDetail.MoveFirst
            For IndexCount = 1 To rsCompanionItemDetail.RecordCount
                rsCompanionItemDetail("MasterItem") = NewMasterItem
                rsCompanionItemDetail("LcID") = NewLcID
                rsCompanionItemDetail.MoveNext
            Next IndexCount
            dgSourceOrder.ReBind
        End If
    
    Case True
        'Run the insert
        ExecQuery.Execute
        'Reset flag
        AddNewMaster = False
        
    End Select

    'Reset MasterItem to point at new values.
    MasterItem = NewMasterItem
    LcId = NewLcID
    ModelDesc = NewModelDesc
    MasterItemStat = NewMasterItemStat

'NO EXIT FUNCTION!  See next comment
ErrorHandler:
    'This will also handle cleanup
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Set CmdParams = Nothing
    If Not (ExecQuery Is Nothing) Then Set ExecQuery.ActiveConnection = Nothing
    Set ExecQuery = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_CompanionItemHeaderSave)", ErrDescription
         f_HandleErr , , , "AIM_Companion_Item_Maintenance::f_CompanionItemHeaderSave", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Function f_CompanionItemHeaderValidate() As Integer
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim NewMasterItem As String
    Dim NewModelDesc As String
    Dim NewMasterItemStat As String
    Dim NewLcID As String
    Dim strMessage As String
    Dim NewItemDesc As String
    
    NewMasterItem = Trim$(txtMasterItem.Text)
    NewLcID = Trim$(txtLocation.Text)
    NewItemDesc = Trim$(txtCompItemDesc.Text)

    If AddNewMaster = False Then
        If StrComp(MasterItem, NewMasterItem, vbTextCompare) = 0 _
        And StrComp(LcId, NewLcID, vbTextCompare) = 0 _
        Then
            f_CompanionItemHeaderValidate = 0
            Exit Function
        End If
    Else
        If IsNull(NewMasterItem) Or IsEmpty(NewMasterItem) Or NewMasterItem = "" _
        Or IsNull(NewLcID) Or NewLcID = "" Or IsEmpty(NewLcID) _
        Or IsNull(NewItemDesc) Or NewItemDesc = "" Or IsEmpty(NewItemDesc) _
        Then
            strMessage = getTranslationResource("MSGBOX08001")
            If StrComp(strMessage, "MSGBOX08001") = 0 Then strMessage = "Item, Location and Model Description fields are required."
            MsgBox strMessage, vbOKOnly + vbExclamation, Me.Caption
            f_CompanionItemHeaderValidate = 1
            Exit Function
        End If
    End If
    
    strSQL = ""
    strSQL = "SELECT COUNT(*) AS 'Duplicate' FROM AIMCompanionItem " & _
        " WHERE MasterItem = N'" & NewMasterItem & "'" & _
        " AND LcID = N'" & NewLcID & "'"
    If f_IsRecordsetValidAndOpen(rsCompanionItemUpdate) Then rsCompanionItemUpdate.Close
    rsCompanionItemUpdate.Open strSQL, Cn
    If rsCompanionItemUpdate(0).Value > 0 Then
        f_CompanionItemHeaderValidate = 1
        strMessage = getTranslationResource("MSGBOX08000")
        If StrComp(strMessage, "MSGBOX08000") = 0 Then strMessage = "Dulpicate Item found with the same Item and Location combination."
        MsgBox strMessage, vbOKOnly + vbExclamation, Me.Caption
        Exit Function
    Else
        f_CompanionItemHeaderValidate = 0
        Exit Function
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_CompanionItemHeaderValidate)"
     f_HandleErr , , , "AIM_Companion_Item_Maintenance::f_CompanionItemHeaderValidate", Now, gDRGeneralError, True, Err
End Function

Private Sub f_CompanionItemDelete()
On Error GoTo ErrorHandler
    
    Dim strSQL As String
    Dim f_MasterItem As String
    Dim f_LcID As String
    Dim ExecQuery As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String

    f_MasterItem = Trim$(txtMasterItem.Text)
    f_LcID = Trim$(txtLocation.Text)
        
    Set ExecQuery = New ADODB.Command
    With ExecQuery
        Set .ActiveConnection = Cn
        .CommandType = adCmdText
        .Prepared = True
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@MasterItem", adVarWChar, adParamInput, 25)
        CmdParams.Value = f_MasterItem
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        CmdParams.Value = f_LcID
        .Parameters.Append CmdParams
    
        strSQL = "DELETE FROM AIMCompanionItem " & _
                " WHERE MasterItem = ? " & _
                " AND LcID = ? "
        .CommandText = strSQL
        .Execute

        strSQL = "DELETE FROM AIMCompanionItemDetail " & _
                " WHERE MasterItem = ? " & _
                " AND LcID = ? "
        .CommandText = strSQL
        .Execute
    End With
    
'NO EXIT FUNCTION!  See next comment
ErrorHandler:
    'This will also handle cleanup
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Set CmdParams = Nothing
    If Not (ExecQuery Is Nothing) Then Set ExecQuery.ActiveConnection = Nothing
    Set ExecQuery = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_CompanionItemDelete)", ErrDescription
         f_HandleErr , , , "AIM_Companion_Item_Maintenance::f_CompanionItemDelete", Now, gDRGeneralError, True, Err
    End If
End Sub
Private Function f_CheckDuplicateCompanionItemDetail(DetailItem As String) As Integer
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim MasterItem As String
    Dim LcId As String
    
    MasterItem = txtMasterItem.Text
    LcId = txtLocation.Text
        
    strSQL = ""
    strSQL = "SELECT COUNT(*) AS 'Duplicate' FROM AIMCompanionItemDetail " & _
            " WHERE MasterItem = N'" & MasterItem & "'" & _
            " AND LcID = N'" & LcId & "'" & _
            " AND Item = N'" & DetailItem & "'"
                            
    If f_IsRecordsetValidAndOpen(rsCompanionItemUpdate) Then rsCompanionItemUpdate.Close
    rsCompanionItemUpdate.Open strSQL, Cn
    
    f_CheckDuplicateCompanionItemDetail = rsCompanionItemUpdate(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_CompanionItemDelet)"
    f_HandleErr , , , "AIM_Companion_Item_Maintenance::f_CompanionItemDelet", Now, gDRGeneralError, True, Err
End Function

Private Function rsItem_Get(LcId As String, Item As String, Action As SQL_ACTIONS) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim rsCompanionItem As ADODB.Recordset
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String

    'Get the Item Table Key
    If Item_GetKey(AIM_CompanionItem_GetKey_Sp, LcId, Item, Action) = SUCCEED Then
        AddNewMaster = False
    End If
    SqlStmt = "SELECT CI.*, COALESCE(I.ItDesc , '') As 'ItDesc' FROM AIMCompanionItem CI " & _
               " INNER JOIN Item I ON  CI.MasterItem = I.Item" & _
               " AND CI.LcID = I.LcID " & _
               " WHERE CI.LcID = N'" & LcId & "'" & _
               " AND CI.MasterItem = N'" & Item & "'"
    Set rsCompanionItem = New ADODB.Recordset
    With rsCompanionItem
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
        .ActiveConnection = Cn
        .source = SqlStmt
        .Open
    End With
    If f_IsRecordsetOpenAndPopulated(rsCompanionItem) Then
        'Set the variables
        ModelDesc = rsCompanionItem("CompanionDesc").Value
        MasterItemDesc = rsCompanionItem("ItDesc").Value
        MasterItemStat = rsCompanionItem("EnableY_N").Value
        rsItem_Get = SUCCEED
    End If

'NO EXIT FUNCTION!  See next comment
ErrorHandler:
    'This will also handle cleanup
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    If f_IsRecordsetValidAndOpen(rsCompanionItem) Then rsCompanionItem.Close
    Set rsCompanionItem = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(rsItem_Get)", ErrDescription
        f_HandleErr , , , "AIM_Companion_Item_Maintenance::rsItem_Get", Now, gDRGeneralError, True, Err
    End If
End Function

Private Sub CmdLcID_Click()
On Error GoTo ErrorHandler
    
   m_Find Me.CmdLcid.Name

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(CmdNewItemId_Click)"
     f_HandleErr , , , Me.Caption & "::CmdNewItemId_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub CmdMasterItem_Click()
On Error GoTo ErrorHandler
    
    m_Find Me.CmdMasterItem.Name

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(CmdMasterItem_Click)"
     f_HandleErr , , , Me.Caption & "::CmdMasterItem_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case ColIndex
    Case 2
        'This is Item
        If (f_CheckDuplicateCompanionItemDetail(dgSourceOrder.Columns(2).Value)) > 0 Then
            strMessage = getTranslationResource("MSGBOX08002")
            If StrComp(strMessage, "MSGBOX08002") = 0 Then strMessage = "Duplicate Item found in"
            MsgBox strMessage & " " & dgSourceOrder.Caption & ".", _
                vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If
    Case 3
        'This is Qty
        If Not IsNumeric(dgSourceOrder.Columns(3).Value) Then
            strMessage = getTranslationResource("MSGBOX08003")
            If StrComp(strMessage, "MSGBOX08003") = 0 Then strMessage = "Only numeric data allowed in"
            MsgBox strMessage & " " & dgSourceOrder.Columns(3).Caption & ".", _
                vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If

    Case 4
        'This is DemandFactor
        If Not IsNumeric(dgSourceOrder.Columns(4).Value) Then
            strMessage = getTranslationResource("MSGBOX08003")
            If StrComp(strMessage, "MSGBOX08003") = 0 Then strMessage = "Only numeric data allowed in"
            MsgBox strMessage & " " & dgSourceOrder.Columns(4).Caption & ".", _
                vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If
    Case 5
        'This is Startdate
        If Not IsDate(dgSourceOrder.Columns(5).Value) Then
            strMessage = getTranslationResource("MSGBOX08004")
            If StrComp(strMessage, "MSGBOX08004") = 0 Then strMessage = "Only datetime data allowed in"
            MsgBox strMessage & " " & dgSourceOrder.Columns(5).Caption & ".", _
                vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If
        If CDate(dgSourceOrder.Columns(5).Value) > CDate(dgSourceOrder.Columns(6).Value) Then
            strMessage = getTranslationResource("MSGBOX08006")
            If StrComp(strMessage, "MSGBOX08006") = 0 Then strMessage = "End Date Cannot be greater than Start Date"
            MsgBox strMessage & ".", vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If
    Case 6
        'This is Enddate
       If Not IsDate(dgSourceOrder.Columns(6).Value) Then
            strMessage = getTranslationResource("MSGBOX08004")
            If StrComp(strMessage, "MSGBOX08004") = 0 Then strMessage = "Only datetime data allowed in"
            MsgBox strMessage & " " & dgSourceOrder.Columns(6).Caption & ".", _
                vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If
        If CDate(dgSourceOrder.Columns(5).Value) > CDate(dgSourceOrder.Columns(6).Value) Then
            strMessage = getTranslationResource("MSGBOX08006")
                    If StrComp(strMessage, "MSGBOX08006") = 0 Then strMessage = "End Date Cannot be greater than Start Date"
            MsgBox strMessage & ".", vbOKOnly + vbExclamation, Me.Caption
            Cancel = True
            Exit Sub
        End If
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_BeforeColUpdate)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_BeforeRowColChange(Cancel As Integer)
On Error GoTo ErrorHandler

    If Trim$(txtMasterItem.Text) = "" _
    Or Trim$(txtLocation.Text) = "" _
    Or Trim$(txtCompItemDesc.Text) = "" _
    Then
        Cancel = True
        Exit Sub
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_BeforeRowColChange)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_BeforeRowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim strMessage As String

    '  This is Item
    If dgSourceOrder.Columns(2).Value = "" Then
        strMessage = getTranslationResource("MSGBOX08005")
        If StrComp(strMessage, "MSGBOX08005") = 0 Then strMessage = "cannot be blank."
        MsgBox dgSourceOrder.Columns(2).Caption & " " & strMessage, vbOKOnly + vbExclamation, Me.Caption
        Cancel = True
        Exit Sub
    End If
    
    '  This is Qty
    If Not IsNumeric(dgSourceOrder.Columns(3).Value) Then
        strMessage = getTranslationResource("MSGBOX08003")
        If StrComp(strMessage, "MSGBOX08003") = 0 Then strMessage = "Only numeric data allowed in"
        MsgBox strMessage & " " & dgSourceOrder.Columns(3).Caption & ".", vbOKOnly + vbExclamation, Me.Caption
        Cancel = True
        Exit Sub
    End If

    'This is DemandFactor
    If Not IsNumeric(dgSourceOrder.Columns(4).Value) Then
        strMessage = getTranslationResource("MSGBOX08003")
        If StrComp(strMessage, "MSGBOX08003") = 0 Then strMessage = "Only numeric data allowed in"
        MsgBox strMessage & " " & dgSourceOrder.Columns(4).Caption & ".", vbOKOnly + vbExclamation, Me.Caption
        Cancel = True
        Exit Sub
    End If
        
    'This is Startdate
    If Not IsDate(dgSourceOrder.Columns(5).Value) Then
        strMessage = getTranslationResource("MSGBOX08004")
        If StrComp(strMessage, "MSGBOX08004") = 0 Then strMessage = "Only datetime data allowed in"
        MsgBox strMessage & " " & dgSourceOrder.Columns(5).Caption & ".", vbOKOnly + vbExclamation, Me.Caption
        Cancel = True
        Exit Sub
    End If
        
    'This is Enddate
    If Not IsDate(dgSourceOrder.Columns(6).Value) Then
        strMessage = getTranslationResource("MSGBOX08004")
        If StrComp(strMessage, "MSGBOX08004") = 0 Then strMessage = "Only datetime data allowed in"
        MsgBox strMessage & " " & dgSourceOrder.Columns(6).Caption & ".", vbOKOnly + vbExclamation, Me.Caption
        Cancel = True
        Exit Sub
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_BeforeUpdate)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_BtnClick()
On Error GoTo ErrorHandler
    
    Dim LcIdKey As String
    Dim ItemKey As String
    Dim ItDesc As String
    
    'Display the Item Lookup form
    LcIdKey = LcId
   
    DetailButtonClicked = True
    AIM_ItemLookUp.LcIdKey = LcIdKey
    AIM_ItemLookUp.ItemKey = ""
    AIM_ItemLookUp.ItDesc = ""
    AIM_ItemLookUp.VnIdKey = ""
    AIM_ItemLookUp.ByIdKey = ""
    AIM_ItemLookUp.AssortKey = ""
    Set AIM_ItemLookUp.Cn = Cn
    AIM_ItemLookUp.Show vbModal
    
    'Get data returned from the Item Lookup and populate the PC Detail grid with Item ID
    If Not AIM_ItemLookUp.CancelFlag Then
        LcIdKey = AIM_ItemLookUp.LcIdKey
        ItemKey = AIM_ItemLookUp.ItemKey
        ItDesc = AIM_ItemLookUp.ItDesc
        dgSourceOrder.Columns(2).Value = ItemKey
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_BtnClick)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_BtnClick", Now, gDRGeneralError, True, Err

End Sub

Private Sub dgSourceOrder_Change()
On Error GoTo ErrorHandler

    'Set toggle to active. This is used in the Toolbar_Click event
    m_ActiveGridID = ACTIVEGRID_Detail
    ToggleToolbar
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_Change)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_Click()
On Error GoTo ErrorHandler
      
    If f_CompanionItemHeaderValidate = 1 Then Exit Sub
    
    f_CompanionItemHeaderSave

    'Set toggle to active. This is used in the Toolbar_Click event
    m_ActiveGridID = ACTIVEGRID_Detail
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_Click)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long

    'Set grid properties
    Me.dgSourceOrder.ROWHEIGHT = 300
    Me.dgSourceOrder.AllowUpdate = True

    'Set column properties
    Me.dgSourceOrder.Columns(0).Name = "MasterItem"
    Me.dgSourceOrder.Columns(0).Width = 0
    Me.dgSourceOrder.Columns(0).Locked = True
    Me.dgSourceOrder.Columns(0).Visible = False
    
    Me.dgSourceOrder.Columns(1).Name = "LcID"
    Me.dgSourceOrder.Columns(1).Caption = getTranslationResource("LcID")
   ' Me.dgSourceOrder.Columns(1).Style = ssStyleEditButton
    'Me.dgSourceOrder.Columns(1).ButtonsAlways = True
     Me.dgSourceOrder.Columns(1).Locked = True
    Me.dgSourceOrder.Columns(1).Width = 2880
    Me.dgSourceOrder.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgSourceOrder.Columns(1).Visible = False

    Me.dgSourceOrder.Columns(2).Name = "Item"
    Me.dgSourceOrder.Columns(2).Caption = getTranslationResource("Item")
    Me.dgSourceOrder.Columns(2).Style = ssStyleEditButton
    Me.dgSourceOrder.Columns(2).ButtonsAlways = True
    'Me.dgSourceOrder.Columns(2).Style = ssStyleEdit
    Me.dgSourceOrder.Columns(2).Width = 1500
    Me.dgSourceOrder.Columns(2).Locked = True
    Me.dgSourceOrder.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(2).Alignment = ssCaptionAlignmentRight

    Me.dgSourceOrder.Columns(3).Name = "Qty"
    Me.dgSourceOrder.Columns(3).Caption = getTranslationResource("Qty")
    Me.dgSourceOrder.Columns(3).Style = ssStyleEdit
    Me.dgSourceOrder.Columns(3).Width = 1500
    Me.dgSourceOrder.Columns(3).Locked = False
    Me.dgSourceOrder.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(3).Alignment = ssCaptionAlignmentRight
    
    Me.dgSourceOrder.Columns(4).Name = "DemandFactor"
    Me.dgSourceOrder.Columns(4).Caption = getTranslationResource("DemandFactor %")
    Me.dgSourceOrder.Columns(4).Style = ssStyleEdit
    Me.dgSourceOrder.Columns(4).Mask = "###.##"
'    Me.dgSourceOrder.Columns(4).PromptChar = "#"
'    Me.dgSourceOrder.Columns(4).PromptInclude = false
    Me.dgSourceOrder.Columns(4).Width = 1600
    Me.dgSourceOrder.Columns(4).Locked = False
    Me.dgSourceOrder.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(4).Alignment = ssCaptionAlignmentRight
    
    
    Me.dgSourceOrder.Columns(5).Name = "StartDate"
    Me.dgSourceOrder.Columns(5).Caption = getTranslationResource("StartDate")
    'Me.dgSourceOrder.Columns(5).Style = ssStyleEdit
    Me.dgSourceOrder.Columns(5).Width = 1500
    Me.dgSourceOrder.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgSourceOrder.Columns(5).NumberFormat = gDateFormat
    Me.dgSourceOrder.Columns(5).DataType = vbDate
    Me.dgSourceOrder.Columns(5).Locked = False
    
    Me.dgSourceOrder.Columns(6).Name = "EndDate"
    Me.dgSourceOrder.Columns(6).Caption = getTranslationResource("EndDate")
    'Me.dgSourceOrder.Columns(6).Style = ssStyleEdit
    Me.dgSourceOrder.Columns(6).Width = 1500
    Me.dgSourceOrder.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgSourceOrder.Columns(6).NumberFormat = gDateFormat
    Me.dgSourceOrder.Columns(6).DataType = vbDate
    Me.dgSourceOrder.Columns(6).Locked = False
    
    
    Me.dgSourceOrder.Columns(7).Name = "ExcludeIndepDemY_N"
    Me.dgSourceOrder.Columns(7).Caption = getTranslationResource("Exclude Ind Dmd")
    Me.dgSourceOrder.Columns(7).Style = ssStyleCheckBox
    Me.dgSourceOrder.Columns(7).Width = 1700
    Me.dgSourceOrder.Columns(7).Locked = False
    Me.dgSourceOrder.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSourceOrder.Columns(7).Alignment = ssCaptionAlignmentRight
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgSourceOrder, ACW_EXPAND
    End If

    'UI Standard settings  -- leave commented code as-is until further actions are defined
    For IndexCounter = 0 To dgSourceOrder.Columns.Count - 1
'        dgSourceOrder.Columns(IndexCounter).HasHeadBackColor = True
'        dgSourceOrder.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgSourceOrder.Columns(IndexCounter).Locked = False Then dgSourceOrder.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_LostFocus()
On Error GoTo ErrorHandler

    If DetailButtonClicked = True Then
        DetailButtonClicked = False
        Exit Sub
    Else
        If dgSourceOrder.IsAddRow Then
            dgSourceOrder.Update
        End If
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_LostFocus)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_LostFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    m_ActiveGridID = ACTIVEGRID_Detail
    ToggleToolbar
    
    'Display the Copy and Print options menu
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    If dgSourceOrder.IsAddRow Then
        dgSourceOrder.Columns(3).Value = 1
        dgSourceOrder.Columns(4).Value = 1
        dgSourceOrder.Columns(5).Value = Date
        dgSourceOrder.Columns(6).Value = Date
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_RowColChange)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer

    If Not f_IsRecordsetOpenAndPopulated(rsCompanionItemDetail) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsCompanionItemDetail.MoveLast
        Else
            rsCompanionItemDetail.MoveFirst
        End If

    Else
        rsCompanionItemDetail.Bookmark = StartLocation
        If ReadPriorRows Then
           rsCompanionItemDetail.MovePrevious
        Else
            rsCompanionItemDetail.MoveNext
        End If

    End If

    For i = 0 To RowBuf.RowCount - 1
        If rsCompanionItemDetail.BOF Or rsCompanionItemDetail.eof Then Exit For

        RowBuf.Value(i, 0) = rsCompanionItemDetail("MasterItem").Value
        RowBuf.Value(i, 1) = rsCompanionItemDetail("LcID").Value
        RowBuf.Value(i, 2) = rsCompanionItemDetail("Item").Value
        RowBuf.Value(i, 3) = rsCompanionItemDetail("Qty").Value
        RowBuf.Value(i, 4) = rsCompanionItemDetail("DemandFactor").Value
        RowBuf.Value(i, 5) = rsCompanionItemDetail("StartDate").Value
        RowBuf.Value(i, 6) = rsCompanionItemDetail("EndDate").Value
        RowBuf.Value(i, 7) = IIf(rsCompanionItemDetail("ExcludeIndepDemY_N").Value = "Y", -1, 0)
        
        RowBuf.Bookmark(i) = rsCompanionItemDetail.Bookmark

        If ReadPriorRows Then
            rsCompanionItemDetail.MovePrevious
        Else
            rsCompanionItemDetail.MoveNext
        End If

        r = r + 1
    Next i

    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundReadData)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As String

    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    'Check recordsets
    If Not f_IsRecordsetValidAndOpen(rsCompanionItemDetail) Then Exit Sub
    
    'Validate data
    'If valid, proceed with insert
    rsCompanionItemDetail.AddNew
    rsCompanionItemDetail!MasterItem = MasterItem
    rsCompanionItemDetail!LcId = LcId

    If Not IsNull(RowBuf.Value(0, 2)) Then rsCompanionItemDetail!Item = RowBuf.Value(0, 2)
    If Not IsNull(RowBuf.Value(0, 3)) Then rsCompanionItemDetail!Qty = RowBuf.Value(0, 3)
    If Not IsNull(RowBuf.Value(0, 4)) Then rsCompanionItemDetail!DemandFactor = RowBuf.Value(0, 4)
    If Not IsNull(RowBuf.Value(0, 5)) Then rsCompanionItemDetail!StartDate = RowBuf.Value(0, 5)
    If Not IsNull(RowBuf.Value(0, 6)) Then rsCompanionItemDetail!EndDate = RowBuf.Value(0, 6)
    If Not IsNull(RowBuf.Value(0, 7)) Then rsCompanionItemDetail!ExcludeIndepDemY_N = IIf(RowBuf.Value(0, 7) = "-1", "Y", "N")
    
    rsCompanionItemDetail.Update
    rsCompanionItemDetail.MoveLast
    NewRowBookmark = rsCompanionItemDetail.Bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundAddData)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundAddData", Now, gDRGeneralError, True, Err
    
    rsCompanionItemDetail.CancelUpdate
End Sub

Private Sub dgSourceOrder_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler

    'Delete from recordset.
    rsCompanionItemDetail.Bookmark = Bookmark
    rsCompanionItemDetail.Delete

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundDeleteRow)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    'Check for other processes that might be jeopardized by executing this event.
    If dgSourceOrder.DataChanged Then Exit Sub
    If dgSourceOrder.IsAddRow Then Exit Sub

    If Not f_IsRecordsetOpenAndPopulated(rsCompanionItemDetail) Then Exit Sub

    'Align recordset
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsCompanionItemDetail.MoveLast
        Else
            rsCompanionItemDetail.MoveFirst
        End If
    Else
        'Line up the bookmark of the recordset with the grid's StartLocation
        If rsCompanionItemDetail.BOF Then rsCompanionItemDetail.MoveFirst
        If rsCompanionItemDetail.eof Then rsCompanionItemDetail.MoveLast
        rsCompanionItemDetail.Bookmark = StartLocation
    End If

    'Note: Do not use StartLocation because it could be null
    rsCompanionItemDetail.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsCompanionItemDetail.Bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundPositionData)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As String

    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    rsCompanionItemDetail.Bookmark = WriteLocation

    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsCompanionItemDetail) Then Exit Sub

    'Validate data
    If Not IsNull(RowBuf.Value(0, 0)) Then
        rsCompanionItemDetail!MasterItem = RowBuf.Value(0, 0)
    Else
        If Not IsNull(dgSourceOrder.Columns(0).Value) Then rsCompanionItemDetail!MasterItem = dgSourceOrder.Columns(0).Value
    End If
    If Not IsNull(RowBuf.Value(0, 1)) Then
        rsCompanionItemDetail!LcId = RowBuf.Value(0, 1)
    Else
        If Not IsNull(dgSourceOrder.Columns(1).Value) Then rsCompanionItemDetail!LcId = dgSourceOrder.Columns(1).Value
    End If
    If Not IsNull(RowBuf.Value(0, 2)) Then
        rsCompanionItemDetail!Item = RowBuf.Value(0, 2)
    Else
        If Not IsNull(dgSourceOrder.Columns(2).Value) Then rsCompanionItemDetail!Item = dgSourceOrder.Columns(2).Value
    End If
    If Not IsNull(RowBuf.Value(0, 3)) Then
        rsCompanionItemDetail!Qty = RowBuf.Value(0, 3)
    Else
        If Not IsNull(dgSourceOrder.Columns(3).Value) Then rsCompanionItemDetail!Qty = dgSourceOrder.Columns(3).Value
    End If
    If Not IsNull(RowBuf.Value(0, 4)) Then
        rsCompanionItemDetail!DemandFactor = RowBuf.Value(0, 4)
    Else
        If Not IsNull(dgSourceOrder.Columns(4).Value) Then rsCompanionItemDetail!DemandFactor = dgSourceOrder.Columns(4).Value
    End If
    If Not IsNull(RowBuf.Value(0, 5)) Then
        rsCompanionItemDetail!StartDate = RowBuf.Value(0, 5)
    Else
        If Not IsNull(dgSourceOrder.Columns(5).Value) Then rsCompanionItemDetail!StartDate = dgSourceOrder.Columns(5).Value
    End If
    If Not IsNull(RowBuf.Value(0, 6)) Then
        rsCompanionItemDetail!EndDate = RowBuf.Value(0, 6)
    Else
        If Not IsNull(dgSourceOrder.Columns(6).Value) Then rsCompanionItemDetail!EndDate = dgSourceOrder.Columns(6).Value
    End If
    If Not IsNull(RowBuf.Value(0, 7)) Then
        If RowBuf.Value(0, 7) = -1 Then
            rsCompanionItemDetail!ExcludeIndepDemY_N = "Y"
        Else
            rsCompanionItemDetail!ExcludeIndepDemY_N = "N"
        End If
    Else
        If Not IsNull(dgSourceOrder.Columns(7).Value) Then
            If dgSourceOrder.Columns(7).Value = -1 Then
                rsCompanionItemDetail!ExcludeIndepDemY_N = "Y"
            Else
                rsCompanionItemDetail!ExcludeIndepDemY_N = "N"
            End If
        End If
    End If
    
    rsCompanionItemDetail.Update
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundWriteData)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundWriteData", Now, gDRGeneralError, True, Err
    rsCompanionItemDetail.CancelUpdate
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    txtCompItemDesc.SetFocus
    txtCompItemDesc.SelLength = Len(txtCompItemDesc.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
            
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG08001")
    If StrComp(strMessage, "STATMSG08001") = 0 Then strMessage = "Initializing Companion Demand Modeling Maintenance ..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize Stored Procedures
    With AIM_CompanionItem_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CompanionItem_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    'Open the Result Set
    LcId = ""
    MasterItem = ""
    ModelDesc = ""
    MasterItemDesc = ""
    RtnCode = rsItem_Get(LcId, MasterItem, SQL_GetFirst)
    'Store the firstitem and firstlocation
    First_Item = MasterItem
    First_LcID = LcId
     
    If RtnCode = SUCCEED Then
        Me.tbNavigation.ToolBars("tbNavigation").Tools("Id_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    Else
        AddNewMaster = True
        rsItemInit
    End If
    
    f_GetCompanionItemDetail
    
    'Refresh Form
    RefreshForm
   
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbNavigation.Tools("ID_Save").Enabled = False
    Else
        Me.tbNavigation.Tools("ID_Save").Enabled = True
    End If
    
    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
   
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , Me.Caption & "::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim DestinationBookmark As Variant
    Dim strMessage As String

    'Clear Errors
    Cn.Errors.Clear
    Write_Message ""

    Select Case Tool.ID
    Case "ID_AddNew", _
     "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
     "ID_GoToBookMark", "ID_LookUp"
        'Alert user to possible change, and save changes, if chosen
    
    Case "ID_Close"
        Unload Me
        Exit Sub
    End Select
    
    If m_ActiveGridID = ACTIVEGRID_Detail Then    'Detail
        ToolbarOptions_Detail Tool
    Else
        ToolbarOptions_Header Tool
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , Me.Caption & "::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function RefreshForm()
On Error GoTo ErrorHandler

    'Enable/Disable Key fields
    If AddNewMaster = True Then
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            Me.tbNavigation.Tools("ID_Save").Enabled = False
            Me.tbNavigation.Tools("ID_Delete").Enabled = False
            Me.tbNavigation.Tools("ID_AddNew").Enabled = False
            Me.tbNavigation.Tools("ID_CreateDuplicate").Enabled = False
        Else
            Me.tbNavigation.Tools("ID_Save").Enabled = True
            Me.tbNavigation.Tools("ID_Delete").Enabled = True
            Me.tbNavigation.Tools("ID_AddNew").Enabled = True
            'Me.tbNavigation.Tools("ID_CreateDuplicate").Enabled = True
        End If

        'Me.txtItem.Enabled = False
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")

    End If
        
    'Header
    Me.txtLocation.Text = LcId
    Me.txtMasterItem.Text = MasterItem
    Me.txtCompItemDesc.Text = ModelDesc
    Me.txtMasterDesc = MasterItemDesc
    
    Me.Check1.Value = IIf(MasterItemStat = "Y", 0, 1)

    f_GetCompanionItemDetail
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(RefreshForm)"
     f_HandleErr , , , Me.Caption & "::dcLangID_Change", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleFieldAccess(p_Enabled As Boolean)
On Error GoTo ErrorHandler

    'Based on function argument, enable/disable all form fields for editing
    Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_Delete").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_AddNew").Enabled = p_Enabled
    
    Me.dgSourceOrder.Enabled = p_Enabled
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(ToggleFieldAccess)", Err.Description
     f_HandleErr , , , "AIM_Companion_Item_Maintenance::ToggleFieldAccess", Now, gDRGeneralError, True, Err
End Function

Private Function ToolbarOptions_Header(Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    Dim identity As Long
            
    'Navigate
    Select Case Tool.ID
    Case "ID_AddNew"
        txtMasterItem.Text = ""
        txtLocation.Text = ""
        txtCompItemDesc.Text = ""
        txtMasterDesc.Text = ""
        Check1.Value = 0
        LcId = ""
        MasterItem = ""
        MasterItemDesc = ""
        ModelDesc = ""
        MasterItemStat = "N"
        AddNewMaster = True
    
    Case "ID_Delete"
        f_CompanionItemDelete
        RtnCode = rsItem_Get(LcId, MasterItem, SQL_GetGT)
        If RtnCode <> SUCCEED Then
        RtnCode = rsItem_Get(LcId, MasterItem, SQL_Getlt)
        End If
        If RtnCode <> SUCCEED Then
            AddNewMaster = True
            ModelDesc = ""
            MasterItemStat = 1
            MasterItem = ""
            MasterItemDesc = ""
            LcId = ""
        End If
     
     Case "ID_GetFirst"
        rsItem_Get LcId, MasterItem, SQL_GetFirst
        
     Case "ID_GetPrev"
        rsItem_Get LcId, MasterItem, SQL_Getlt
            
    Case "ID_GetNext"
        rsItem_Get LcId, MasterItem, SQL_GetGT
            
    Case "ID_GetLast"
        rsItem_Get LcId, MasterItem, SQL_GetLast
            
    Case "ID_Save"
        If f_CompanionItemHeaderValidate = 1 Then
            Exit Function
        End If
        f_CompanionItemHeaderSave
            
    Case "ID_SetBookMark"
        LcID_BookMark = LcId
        MasterItem_BookMark = MasterItem
        Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
 
    Case "ID_GoToBookMark"
        If Not IsEmpty(LcID_BookMark) _
        Or Not IsNull(LcID_BookMark) _
        Or LcID_BookMark = "" _
        Or Not IsEmpty(MasterItem_BookMark) _
        Or Not IsNull(MasterItem_BookMark) _
        Or MasterItem_BookMark = "" Then
            LcId = LcID_BookMark
            MasterItem = MasterItem_BookMark
            rsItem_Get LcId, MasterItem, SQL_GetEq
        End If
    
    Case "ID_LookUp"

    Case "ID_Close"
        'Prepare to exit form
        Unload Me
        Exit Function
   End Select
    
    'Refresh
    RefreshForm             'Refresh the form

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ToolbarOptions_Header)"
     f_HandleErr , , , "AIM_Companion_Item_Maintenance::ToolbarOptions_Header", Now, gDRGeneralError, True, Err
End Function

Private Function ToolbarOptions_Detail(Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Save unsaved updates, first
    If dgSourceOrder.DataChanged Then dgSourceOrder.Update
    
    Select Case Tool.ID
    Case "ID_GetFirst"
        'Navigate to the first detail record
        Me.dgSourceOrder.MoveFirst
    Case "ID_GetPrev"
        'Navigate to the previous detail record
        Me.dgSourceOrder.MovePrevious
    Case "ID_GetNext"
        'Navigate to the next detail record
        Me.dgSourceOrder.MoveNext
    Case "ID_GetLast"
        'Navigate to the last detail record
        Me.dgSourceOrder.MoveLast
    Case "ID_AddNew"
        Me.dgSourceOrder.AddNew
    Case "ID_Delete"
        'Trigger Delete event for detail.
        Me.dgSourceOrder.DeleteSelected
        Me.dgSourceOrder.SetFocus
    Case "ID_Save"
        'Trigger Update/Insert event for detail.
        If dgSourceOrder.DataChanged Then dgSourceOrder.Update
    Case "ID_SetBookMark"
        'Toggle bookmark
        If IsEmpty(CompanionItemBookmark) _
        Or IsNull(CompanionItemBookmark) _
        Then
            CompanionItemBookmark = dgSourceOrder.Bookmark
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
        Else
            CompanionItemBookmark = Null
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
        End If
    Case "ID_GoToBookMark"
        'Navigate to the last bookmarked record
        If Not IsEmpty(CompanionItemBookmark) _
        Or Not IsNull(CompanionItemBookmark) _
        Then
            dgSourceOrder.Bookmark = CompanionItemBookmark
        End If
    End Select
    
Exit Function
ErrorHandler:
    'f_HandleErr Me.Caption & "(ToolbarOptions_Detail)"
     f_HandleErr , , , "AIM_Companion_Item_Maintenance::dcLangID_Change", Now, gDRGeneralError, True, Err
End Function

Private Sub ToggleToolbar()
On Error GoTo ErrorHandler

    With Me.tbNavigation
        .Redraw = False
        
        If m_ActiveGridID = ACTIVEGRID_Detail Then
            'Set toolbar tooltiptexts to reflect the Detail
            .Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Detail")
            
            .Tools("ID_GetFirst").ToolTipText = getTranslationResource("Get First Detail Record")
            .Tools("ID_GetLast").ToolTipText = getTranslationResource("Get Last Detail Record")
            .Tools("ID_GetNext").ToolTipText = getTranslationResource("Get Next Detail Record")
            .Tools("ID_GetPrev").ToolTipText = getTranslationResource("Get Previous Detail Record")
            .Tools("ID_GoToBookmark").ToolTipText = getTranslationResource("Go To Detail Bookmark")
            .Tools("ID_Save").ToolTipText = getTranslationResource("Save Detail Record(s)")
            .Tools("ID_SetBookMark").ToolTipText = getTranslationResource("Set Detail Bookmark")
            .Tools("ID_AddNew").ToolTipText = getTranslationResource("Add New Detail Record")
            .Tools("ID_Delete").ToolTipText = getTranslationResource("Delete Selected Detail Record(s)")
        Else
            'Set toolbar tooltiptexts to reflect the Header
            'm_ActiveGridID = ACTIVEGRID_Destination
            m_ActiveGridID = ""
            .Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
            
            .Tools("ID_GetFirst").ToolTipText = getTranslationResource("Get First Record")
            .Tools("ID_GetLast").ToolTipText = getTranslationResource("Get Last Record")
            .Tools("ID_GetNext").ToolTipText = getTranslationResource("Get Next Record")
            .Tools("ID_GetPrev").ToolTipText = getTranslationResource("Get Previous Record")
            .Tools("ID_GoToBookmark").ToolTipText = getTranslationResource("Go To Bookmark")
            .Tools("ID_Save").ToolTipText = getTranslationResource("Save Current Record")
            .Tools("ID_SetBookMark").ToolTipText = getTranslationResource("Set Bookmark")
            .Tools("ID_AddNew").ToolTipText = getTranslationResource("Add New Record")
            .Tools("ID_Delete").ToolTipText = getTranslationResource("Delete Current Record")
        End If
    End With
    
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(ToggleToolbar)", Err.Description
     f_HandleErr , , , "Aim_Companion_Item_Maintenance::ToggleToolbar", Now, gDRGeneralError, True, Err
End Sub


Private Sub f_GetCompanionItemDetail()
On Error GoTo ErrorHandler
    
    Dim SqlStmt As String
    
    SqlStmt = "SELECT * FROM AIMCompanionItemDetail " & _
        " WHERE MasterItem = N'" & MasterItem & "'" & _
        " AND LcID = N'" & LcId & "'"
    
    'Open the Record Sets
    If f_IsRecordsetValidAndOpen(rsCompanionItemDetail) Then rsCompanionItemDetail.Close
    With rsCompanionItemDetail
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
        .ActiveConnection = Cn
        .source = SqlStmt
        .Open
    End With
    
    Me.dgSourceOrder.ReBind
  
  Exit Sub
ErrorHandler:
     'Err.Raise Err.Number, Err.source & "(f_GetCompanionItemDetail)", Err.Description
     f_HandleErr , , "Aim_Companion_Item_Maintenance::dcLangID_Change", Now, gDRGeneralError, True, Err
End Sub

Private Function m_Find(p_Source As String)
On Error GoTo ErrorHandler

    Select Case p_Source
    Case Me.CmdLcid.Name
        With AIM_LocationLookUp
            .LcId = Me.txtLocation.Text
            Set .Cn = Cn
            .dcComparison = "Contains"
            .Show vbModal
            If Not .CancelFlag Then
                Me.txtLocation.Text = .LcId
            End If
        End With
        
    Case Me.CmdMasterItem.Name
        With AIM_ItemLookUp
            .LcIdKey = Me.txtLocation.Text
            .ItemKey = Me.txtMasterItem.Text
            .ItDesc = ""
            .VnIdKey = ""
            .ByIdKey = ""
            .AssortKey = ""
            Set .Cn = Cn
            .dcComparison = "Contains"
            .Show vbModal
            If Not .CancelFlag Then
                Me.txtMasterItem.Text = .ItemKey
                Me.txtLocation.Text = .LcIdKey
                Me.txtMasterDesc = .ItDesc
            End If
        End With
    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(m_Find)"
     f_HandleErr , , , "AIM_Companion_Item_Maintenance::m_Find", Now, gDRGeneralError, True, Err
End Function

Private Sub txtCompItemDesc_GotFocus()
On Error GoTo ErrorHandler
    
    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtCompItemDesc_GotFocus)"
     f_HandleErr , , , Me.Caption & "::txtCompItemDesc_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtCompItemDesc_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    If txtCompItemDesc.Text = "" Then
        strMessage = getTranslationResource("MSGBOX08007")
        If StrComp(strMessage, "MSGBOX08007") = 0 Then strMessage = "Model Description is Required"
        MsgBox strMessage & " " & dgSourceOrder.Caption & ".", vbOKOnly + vbExclamation, Me.Caption
        Cancel = True
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtCompItemDesc_Validate)"
     f_HandleErr , , , Me.Caption & "::txtCompItemDesc_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtLocation_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtLocation_GotFocus)"
     f_HandleErr , , , Me.Caption & "::txtLocation_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMasterItem_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintID_GotFocus)"
     f_HandleErr , , , Me.Caption & "::dcConstraintID_GotFocus", Now, gDRGeneralError, True, Err
End Sub

