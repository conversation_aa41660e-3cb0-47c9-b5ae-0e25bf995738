VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{F9043C88-F6F2-101A-A3C9-08002B2F49FB}#1.2#0"; "COMDLG32.OCX"
Begin VB.Form AIM_JobWizard 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Job Wizard"
   ClientHeight    =   9105
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   14520
   Icon            =   "AIM_JobWizard.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   9105
   ScaleWidth      =   14520
   ShowInTaskbar   =   0   'False
   Begin VB.Frame frJobType 
      Caption         =   "Transaction Type"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   8295
      Left            =   60
      TabIndex        =   115
      Top             =   60
      Width           =   3645
      Begin VB.OptionButton optJobType 
         Caption         =   "Demand Plan Forecast"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   8
         Left            =   120
         TabIndex        =   123
         Top             =   3480
         Visible         =   0   'False
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Order Policy Assignment"
         Enabled         =   0   'False
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   5
         Left            =   100
         TabIndex        =   116
         TabStop         =   0   'False
         Top             =   6600
         Visible         =   0   'False
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Demand Update"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   1
         Left            =   100
         TabIndex        =   1
         Top             =   737
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Velocity Code Assignment"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   4
         Left            =   100
         TabIndex        =   4
         Top             =   2093
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Seasonality Profiles Update"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   3
         Left            =   100
         TabIndex        =   3
         Top             =   1641
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Order Generation"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   2
         Left            =   100
         TabIndex        =   2
         Top             =   1189
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Data Interface"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   0
         Left            =   100
         TabIndex        =   0
         Top             =   285
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Allocation"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   7
         Left            =   100
         TabIndex        =   6
         Top             =   3000
         Width           =   3435
      End
      Begin VB.OptionButton optJobType 
         Caption         =   "Demand Plan Forecast"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   6
         Left            =   100
         TabIndex        =   5
         Top             =   2545
         Width           =   3435
      End
      Begin MSComDlg.CommonDialog CommonDialog1 
         Left            =   540
         Top             =   4380
         _ExtentX        =   847
         _ExtentY        =   847
         _Version        =   393216
      End
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Finish"
      Default         =   -1  'True
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   345
      Left            =   12450
      Style           =   1  'Graphical
      TabIndex        =   65
      Top             =   8520
      Width           =   1800
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   345
      Left            =   7920
      Style           =   1  'Graphical
      TabIndex        =   67
      Top             =   8520
      Width           =   1800
   End
   Begin VB.CommandButton cmdRefresh 
      Caption         =   "&Refresh"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   345
      Left            =   9840
      Style           =   1  'Graphical
      TabIndex        =   66
      Top             =   8520
      Width           =   2520
   End
   Begin ActiveTabs.SSActiveTabs atAIMJobs 
      Height          =   8295
      Left            =   3780
      TabIndex        =   84
      TabStop         =   0   'False
      Top             =   90
      Width           =   10605
      _ExtentX        =   18706
      _ExtentY        =   14631
      _Version        =   131083
      TabCount        =   9
      ControlCount    =   1
      TabStyle        =   3
      TagVariant      =   ""
      Tabs            =   "AIM_JobWizard.frx":030A
      Begin ActiveTabs.SSActiveTabPanel atpDemandPlanForecast 
         Height          =   8295
         Left            =   0
         TabIndex        =   125
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":05C0
         Begin VB.Frame Frame2 
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4755
            Left            =   195
            TabIndex        =   126
            Top             =   495
            Width           =   10290
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFcstSetupID 
               Bindings        =   "AIM_JobWizard.frx":05E8
               Height          =   345
               Left            =   2445
               TabIndex        =   127
               Top             =   240
               Width           =   2505
               DataFieldList   =   "fcstid"
               _Version        =   196617
               DataMode        =   2
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   4419
               _ExtentY        =   609
               _StockProps     =   93
               BackColor       =   -2147483643
               DataFieldToDisplay=   "fcstid"
            End
            Begin VB.Label Label22 
               Caption         =   "Forecast ID"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   128
               Top             =   300
               Width           =   2130
            End
         End
         Begin VB.Label Label26 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Demand Plan Forecast"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   345
            Left            =   120
            TabIndex        =   129
            Top             =   120
            Width           =   10335
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel5 
         Height          =   8295
         Left            =   0
         TabIndex        =   90
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":05FF
         Begin VB.Frame frFcstRepBatch 
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4425
            Left            =   120
            TabIndex        =   113
            Top             =   480
            Width           =   10335
            Begin VB.OptionButton OptDemPlanFcst 
               Caption         =   "Run Demand Forecast Only"
               Height          =   495
               Index           =   0
               Left            =   210
               TabIndex        =   132
               Top             =   915
               Width           =   5460
            End
            Begin VB.OptionButton OptDemPlanFcst 
               Caption         =   "Run Demand Forecast and Export Forecast Plan"
               Height          =   495
               Index           =   1
               Left            =   210
               TabIndex        =   131
               Top             =   1485
               Width           =   5460
            End
            Begin VB.OptionButton OptDemPlanFcst 
               Caption         =   "Export Forecast Plan"
               Height          =   495
               Index           =   2
               Left            =   210
               TabIndex        =   130
               Top             =   2025
               Width           =   5460
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFcstId 
               Bindings        =   "AIM_JobWizard.frx":0627
               Height          =   345
               Left            =   2445
               TabIndex        =   54
               Top             =   240
               Width           =   2505
               DataFieldList   =   "fcstid"
               _Version        =   196617
               DataMode        =   2
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   4419
               _ExtentY        =   609
               _StockProps     =   93
               BackColor       =   -2147483643
               DataFieldToDisplay=   "fcstid"
            End
            Begin VB.Label Label9 
               Caption         =   "Forecast ID"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   114
               Top             =   300
               Width           =   2130
            End
         End
         Begin VB.Label Label21 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Forecast Repository Batch"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   91
            Top             =   105
            Width           =   10335
         End
      End
      Begin VB.Frame Frame1 
         Caption         =   "Common Parameters"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   1450
         Left            =   120
         TabIndex        =   76
         Top             =   5490
         Width           =   10335
         Begin VB.CheckBox ckUseWinNTAuth 
            Caption         =   "Use Windows Authentication"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   200
            TabIndex        =   61
            Top             =   1050
            Width           =   4515
         End
         Begin TDBText6Ctl.TDBText txtServer 
            Height          =   345
            Left            =   2760
            TabIndex        =   59
            Top             =   255
            Width           =   1785
            _Version        =   65536
            _ExtentX        =   3149
            _ExtentY        =   609
            Caption         =   "AIM_JobWizard.frx":063E
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_JobWizard.frx":06AA
            Key             =   "AIM_JobWizard.frx":06C8
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtDatabase 
            Height          =   345
            Left            =   2760
            TabIndex        =   60
            Top             =   645
            Width           =   1785
            _Version        =   65536
            _ExtentX        =   3149
            _ExtentY        =   609
            Caption         =   "AIM_JobWizard.frx":070C
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_JobWizard.frx":0778
            Key             =   "AIM_JobWizard.frx":0796
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtUserId 
            Height          =   345
            Left            =   7500
            TabIndex        =   62
            Top             =   250
            Width           =   1785
            _Version        =   65536
            _ExtentX        =   3149
            _ExtentY        =   609
            Caption         =   "AIM_JobWizard.frx":07DA
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_JobWizard.frx":0846
            Key             =   "AIM_JobWizard.frx":0864
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtPassword 
            Height          =   345
            Left            =   7500
            TabIndex        =   63
            Top             =   650
            Width           =   1785
            _Version        =   65536
            _ExtentX        =   3149
            _ExtentY        =   609
            Caption         =   "AIM_JobWizard.frx":08A8
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_JobWizard.frx":0914
            Key             =   "AIM_JobWizard.frx":0932
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   "*"
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   3
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin VB.Label label 
            Caption         =   "Password"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   4
            Left            =   5190
            TabIndex        =   80
            Top             =   695
            Width           =   2205
         End
         Begin VB.Label label 
            Caption         =   "User ID"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   3
            Left            =   5190
            TabIndex        =   79
            Top             =   295
            Width           =   2205
         End
         Begin VB.Label label 
            Caption         =   "Database"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   1
            Left            =   195
            TabIndex        =   78
            Top             =   695
            Width           =   2445
         End
         Begin VB.Label label 
            Caption         =   "Server"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   0
            Left            =   195
            TabIndex        =   77
            Top             =   295
            Width           =   2445
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel4 
         Height          =   8295
         Left            =   0
         TabIndex        =   83
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":0976
         Begin VB.Frame frVelCodeAssgnt 
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4425
            Left            =   120
            TabIndex        =   111
            Top             =   480
            Width           =   10335
            Begin VB.OptionButton optSelectionOpt 
               Caption         =   "All Locations (Rank Globally)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   0
               Left            =   240
               TabIndex        =   50
               Top             =   240
               Value           =   -1  'True
               Width           =   4035
            End
            Begin VB.OptionButton optSelectionOpt 
               Caption         =   "All Locations (Rank by Location)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   1
               Left            =   240
               TabIndex        =   51
               Top             =   675
               Width           =   4035
            End
            Begin VB.OptionButton optSelectionOpt 
               Caption         =   "Selected Location"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   2
               Left            =   240
               TabIndex        =   52
               Top             =   1110
               Width           =   4035
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLcId3 
               Bindings        =   "AIM_JobWizard.frx":099E
               DataField       =   " "
               Height          =   345
               Left            =   2550
               TabIndex        =   53
               Top             =   1530
               Width           =   1410
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2487
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin VB.Label Label18 
               Caption         =   "Location(s)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   600
               TabIndex        =   112
               Top             =   1575
               Width           =   1920
            End
         End
         Begin VB.Label Label5 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Velocity Code Assignment"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   68
            Top             =   100
            Width           =   10335
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel3 
         Height          =   8295
         Left            =   0
         TabIndex        =   85
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":09A9
         Begin VB.Frame frOrderPolicyUpdate 
            Height          =   4425
            Left            =   120
            TabIndex        =   117
            Top             =   480
            Width           =   10335
         End
         Begin VB.Label Label6 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Order Policy Update"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   69
            Top             =   105
            Width           =   10335
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   8295
         Left            =   0
         TabIndex        =   86
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":09D1
         Begin VB.Frame frSeasPrfUpdate 
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4425
            Left            =   120
            TabIndex        =   104
            Top             =   480
            Width           =   10335
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLcId2 
               Bindings        =   "AIM_JobWizard.frx":09F9
               DataField       =   " "
               Height          =   345
               Left            =   3330
               TabIndex        =   44
               Top             =   240
               Width           =   1170
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2064
               _ExtentY        =   600
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBNumber6Ctl.TDBNumber txtEndPeriod 
               Height          =   345
               Left            =   3330
               TabIndex        =   45
               Top             =   660
               Width           =   1170
               _Version        =   65536
               _ExtentX        =   2064
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":0A04
               Caption         =   "AIM_JobWizard.frx":0A24
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":0A90
               Keys            =   "AIM_JobWizard.frx":0AAE
               Spin            =   "AIM_JobWizard.frx":0AF8
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtEndYear 
               Height          =   345
               Left            =   3330
               TabIndex        =   46
               Top             =   1080
               Width           =   1170
               _Version        =   65536
               _ExtentX        =   2064
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":0B20
               Caption         =   "AIM_JobWizard.frx":0B40
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":0BAC
               Keys            =   "AIM_JobWizard.frx":0BCA
               Spin            =   "AIM_JobWizard.frx":0C14
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "###0;-###0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   2099
               MinValue        =   1900
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNbrOfYears 
               Height          =   345
               Left            =   3330
               TabIndex        =   47
               Top             =   1500
               Width           =   1170
               _Version        =   65536
               _ExtentX        =   2064
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":0C3C
               Caption         =   "AIM_JobWizard.frx":0C5C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":0CC8
               Keys            =   "AIM_JobWizard.frx":0CE6
               Spin            =   "AIM_JobWizard.frx":0D30
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   10
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtSaVersion 
               Height          =   345
               Left            =   3330
               TabIndex        =   48
               Top             =   1920
               Width           =   1170
               _Version        =   65536
               _ExtentX        =   2064
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":0D58
               Caption         =   "AIM_JobWizard.frx":0D78
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":0DE4
               Keys            =   "AIM_JobWizard.frx":0E02
               Spin            =   "AIM_JobWizard.frx":0E4C
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtClassOption 
               Height          =   345
               Left            =   3330
               TabIndex        =   49
               Top             =   2340
               Width           =   1170
               _Version        =   65536
               _ExtentX        =   2064
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":0E74
               Caption         =   "AIM_JobWizard.frx":0E94
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":0F00
               Keys            =   "AIM_JobWizard.frx":0F1E
               Spin            =   "AIM_JobWizard.frx":0F68
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   4
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin VB.Label Label15 
               Caption         =   "Class Code Option"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   110
               Top             =   2385
               Width           =   3000
            End
            Begin VB.Label Label14 
               Caption         =   "Seasonality Profile Version"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   109
               Top             =   1965
               Width           =   3000
            End
            Begin VB.Label Label13 
               Caption         =   "Number of Years"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   108
               Top             =   1545
               Width           =   3000
            End
            Begin VB.Label Label12 
               Caption         =   "End Week"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   107
               Top             =   705
               Width           =   3000
            End
            Begin VB.Label Label8 
               Caption         =   "End Year"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   106
               Top             =   1125
               Width           =   3000
            End
            Begin VB.Label Label7 
               Caption         =   "Location(s)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   105
               Top             =   285
               Width           =   3000
            End
         End
         Begin VB.Label Label1 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Seasonality Profile Update"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   70
            Top             =   100
            Width           =   10335
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   8295
         Left            =   0
         TabIndex        =   87
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":0F90
         Begin VB.Frame frOrdGen 
            Height          =   4425
            Left            =   120
            TabIndex        =   71
            Top             =   480
            Width           =   10335
            Begin VB.Frame frOrdGenSelOpt 
               Caption         =   "Selection Options"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   2115
               Left            =   120
               TabIndex        =   100
               Top             =   240
               Width           =   9255
               Begin VB.OptionButton optFilterOption 
                  Caption         =   "All scheduled Review Cycles"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   210
                  TabIndex        =   32
                  Top             =   255
                  Value           =   -1  'True
                  Width           =   4155
               End
               Begin VB.OptionButton optFilterOption 
                  Caption         =   "Specified Review Cycle"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   210
                  TabIndex        =   33
                  Top             =   712
                  Width           =   4155
               End
               Begin VB.OptionButton optFilterOption 
                  Caption         =   "Specified Vendor/Assortment"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   4920
                  TabIndex        =   34
                  Top             =   712
                  Width           =   4155
               End
               Begin VB.CommandButton cmdReviewCycleLookUp 
                  Enabled         =   0   'False
                  Height          =   320
                  Left            =   4050
                  Picture         =   "AIM_JobWizard.frx":0FB8
                  Style           =   1  'Graphical
                  TabIndex        =   36
                  Top             =   1185
                  Width           =   315
               End
               Begin VB.CommandButton cmdVnIdLookUp 
                  Enabled         =   0   'False
                  Height          =   320
                  Left            =   8760
                  Picture         =   "AIM_JobWizard.frx":1102
                  Style           =   1  'Graphical
                  TabIndex        =   38
                  Top             =   1215
                  Width           =   315
               End
               Begin TDBText6Ctl.TDBText txtRevCycle 
                  Height          =   345
                  Left            =   2620
                  TabIndex        =   35
                  Top             =   1170
                  Width           =   1380
                  _Version        =   65536
                  _ExtentX        =   2434
                  _ExtentY        =   600
                  Caption         =   "AIM_JobWizard.frx":124C
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_JobWizard.frx":12B8
                  Key             =   "AIM_JobWizard.frx":12D6
                  BackColor       =   -2147483643
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   0
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   0
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   0
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   8
                  LengthAsByte    =   0
                  Text            =   "ALL"
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtAssortment 
                  Height          =   345
                  Left            =   7350
                  TabIndex        =   39
                  Top             =   1605
                  Width           =   1380
                  _Version        =   65536
                  _ExtentX        =   2434
                  _ExtentY        =   600
                  Caption         =   "AIM_JobWizard.frx":131A
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_JobWizard.frx":1386
                  Key             =   "AIM_JobWizard.frx":13A4
                  BackColor       =   -2147483643
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   0
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   0
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   0
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   12
                  LengthAsByte    =   0
                  Text            =   "ALL"
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin TDBText6Ctl.TDBText txtVnId 
                  Height          =   345
                  Left            =   7350
                  TabIndex        =   37
                  Top             =   1215
                  Width           =   1380
                  _Version        =   65536
                  _ExtentX        =   2434
                  _ExtentY        =   600
                  Caption         =   "AIM_JobWizard.frx":13E8
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_JobWizard.frx":1454
                  Key             =   "AIM_JobWizard.frx":1472
                  BackColor       =   -2147483643
                  EditMode        =   0
                  ForeColor       =   -2147483640
                  ReadOnly        =   0
                  ShowContextMenu =   -1
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MarginBottom    =   3
                  Enabled         =   0
                  MousePointer    =   0
                  Appearance      =   1
                  BorderStyle     =   1
                  AlignHorizontal =   0
                  AlignVertical   =   2
                  MultiLine       =   -1
                  ScrollBars      =   0
                  PasswordChar    =   ""
                  AllowSpace      =   -1
                  Format          =   ""
                  FormatMode      =   1
                  AutoConvert     =   -1
                  ErrorBeep       =   0
                  MaxLength       =   12
                  LengthAsByte    =   0
                  Text            =   "ALL"
                  Furigana        =   0
                  HighlightText   =   0
                  IMEMode         =   0
                  IMEStatus       =   0
                  DropWndWidth    =   0
                  DropWndHeight   =   0
                  ScrollBarMode   =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
               End
               Begin VB.Label lblAssort_OrdGen 
                  Caption         =   "Assortment"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Left            =   5190
                  TabIndex        =   103
                  Top             =   1650
                  Width           =   2100
               End
               Begin VB.Label lblVnID_OrdGen 
                  Caption         =   "Vendor ID"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Left            =   5190
                  TabIndex        =   102
                  Top             =   1245
                  Width           =   2100
               End
               Begin VB.Label label 
                  Caption         =   "Review Cycle"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   5
                  Left            =   450
                  TabIndex        =   101
                  Top             =   1185
                  Width           =   2100
               End
            End
            Begin VB.CheckBox ckOutputOpt 
               Caption         =   "Output to Purchase Order Detail Table"
               Enabled         =   0   'False
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   4890
               TabIndex        =   43
               Top             =   2985
               Value           =   1  'Checked
               Width           =   4395
            End
            Begin VB.CheckBox ckPE_Excpts 
               Caption         =   "Review for Priority Exceptions"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   330
               TabIndex        =   41
               Top             =   2940
               Width           =   4395
            End
            Begin VB.CheckBox ckLT_Excpts 
               Caption         =   "Review for Lead Time Exceptions"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   330
               TabIndex        =   40
               Top             =   2550
               Width           =   4395
            End
            Begin VB.CheckBox ckClearPO 
               Caption         =   "Clear Purchase Order Detail Table"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   4890
               TabIndex        =   42
               Top             =   2595
               Width           =   4395
            End
         End
         Begin VB.Label Label2 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Order Generation"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   72
            Top             =   100
            Width           =   10335
         End
      End
      Begin ActiveTabs.SSActiveTabPanel apDemandUpdate 
         Height          =   8295
         Left            =   0
         TabIndex        =   88
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":14B6
         Begin VB.Frame frDmdUpdate 
            Height          =   4425
            Left            =   120
            TabIndex        =   96
            Top             =   480
            Width           =   10335
            Begin VB.CheckBox ckAutoOption 
               Caption         =   "Automatic Period/Year Selection"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   26
               Top             =   240
               Width           =   5955
            End
            Begin VB.CheckBox ckForceUpdateFlag 
               Caption         =   "Update all Items"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   30
               Top             =   2100
               Width           =   5955
            End
            Begin VB.CheckBox ckMDCOption 
               Caption         =   "Update Demand for Master Distribution Centers"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   31
               Top             =   2670
               Width           =   5955
            End
            Begin TDBNumber6Ctl.TDBNumber txtFcstPeriod 
               Height          =   345
               Left            =   3135
               TabIndex        =   27
               Top             =   690
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":14DE
               Caption         =   "AIM_JobWizard.frx":14FE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":156A
               Keys            =   "AIM_JobWizard.frx":1588
               Spin            =   "AIM_JobWizard.frx":15D2
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   1479671813
            End
            Begin TDBNumber6Ctl.TDBNumber txtFcstYear 
               Height          =   345
               Left            =   3135
               TabIndex        =   28
               Top             =   1050
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_JobWizard.frx":15FA
               Caption         =   "AIM_JobWizard.frx":161A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":1686
               Keys            =   "AIM_JobWizard.frx":16A4
               Spin            =   "AIM_JobWizard.frx":16EE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "###0;-###0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   2099
               MinValue        =   1900
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   1479671813
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLcId 
               Bindings        =   "AIM_JobWizard.frx":1716
               DataField       =   " "
               Height          =   345
               Left            =   3135
               TabIndex        =   29
               Top             =   1590
               Width           =   1170
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2064
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin VB.Label Label16 
               Caption         =   "Location(s)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   600
               TabIndex        =   99
               Top             =   1635
               Width           =   2445
            End
            Begin VB.Label Label3 
               Caption         =   "Forecast Update Year"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   600
               TabIndex        =   98
               Top             =   1095
               Width           =   2445
            End
            Begin VB.Label Label24 
               Caption         =   "Forecast Update Week"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   600
               TabIndex        =   97
               Top             =   735
               Width           =   2445
            End
         End
         Begin VB.Label Label11 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Demand Update"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   73
            Top             =   100
            Width           =   10335
         End
      End
      Begin TDBText6Ctl.TDBText txtCmdDescription 
         Height          =   675
         Left            =   120
         TabIndex        =   64
         Top             =   7440
         Width           =   10335
         _Version        =   65536
         _ExtentX        =   18230
         _ExtentY        =   1191
         Caption         =   "AIM_JobWizard.frx":1721
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobWizard.frx":178D
         Key             =   "AIM_JobWizard.frx":17AB
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin ActiveTabs.SSActiveTabPanel apDataExchange 
         Height          =   8295
         Left            =   0
         TabIndex        =   89
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":17EF
         Begin VB.Frame frDataInterface 
            Caption         =   "Transaction Sets"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4905
            Left            =   120
            TabIndex        =   74
            Top             =   480
            Width           =   10335
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Forecast Adjustments (FA)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   16
               Left            =   240
               TabIndex        =   124
               Top             =   3840
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "KitBOM(KB)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   15
               Left            =   3450
               TabIndex        =   122
               Top             =   3390
               Width           =   2955
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Re-stock Order (RO)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   9
               Left            =   3400
               TabIndex        =   18
               Top             =   2940
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Re-stock Profile (RP)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   10
               Left            =   240
               TabIndex        =   15
               Top             =   2472
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Sales Detail (SD)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   4
               Left            =   240
               TabIndex        =   13
               Top             =   2029
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Demand Detail (DD)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   0
               Left            =   3400
               TabIndex        =   14
               Top             =   2029
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Stock Status (SS)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   5
               Left            =   240
               TabIndex        =   9
               Top             =   1143
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Vendor/Assortment (VN)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   6
               Left            =   3400
               TabIndex        =   8
               Top             =   700
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Location (LC)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   8
               Left            =   240
               TabIndex        =   7
               Top             =   700
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Re-stock Item Substitutes (IS)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   14
               Left            =   240
               TabIndex        =   17
               Top             =   2915
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Alternate Vendor/Source (AS)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   13
               Left            =   240
               TabIndex        =   19
               Top             =   3360
               Width           =   3135
            End
            Begin VB.CheckBox ckIgnoreRevStatus 
               Caption         =   "Ignore review status"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   435
               Left            =   7080
               TabIndex        =   23
               Top             =   2400
               Width           =   3075
            End
            Begin VB.CommandButton cmdInFileName 
               Caption         =   "..."
               Height          =   345
               Left            =   9765
               TabIndex        =   25
               Top             =   4320
               Width           =   345
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Allocated Order (AO)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   12
               Left            =   6840
               TabIndex        =   22
               Top             =   1936
               Width           =   3255
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Re-stock Sourcing (RS)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   11
               Left            =   3400
               TabIndex        =   16
               Top             =   2472
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Stock Status Light (SL)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   7
               Left            =   3400
               TabIndex        =   10
               Top             =   1143
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Lead Time (LT)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   1
               Left            =   240
               TabIndex        =   11
               Top             =   1586
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Item History (HS)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   2
               Left            =   3400
               TabIndex        =   12
               Top             =   1586
               Width           =   3135
            End
            Begin VB.OptionButton optTxnSet 
               Caption         =   "Purchase Order Detail (PO)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Index           =   3
               Left            =   6840
               TabIndex        =   20
               Top             =   700
               Width           =   3255
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcPoFilterOption 
               Height          =   375
               Left            =   7080
               TabIndex        =   21
               Top             =   1500
               Width           =   2655
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   4683
               _ExtentY        =   661
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBText6Ctl.TDBText txtFileName 
               Height          =   345
               Left            =   3045
               TabIndex        =   24
               Top             =   4320
               Width           =   6645
               _Version        =   65536
               _ExtentX        =   11721
               _ExtentY        =   609
               Caption         =   "AIM_JobWizard.frx":1817
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobWizard.frx":1883
               Key             =   "AIM_JobWizard.frx":18A1
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label label 
               Caption         =   "File Name/All"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   240
               TabIndex        =   95
               Top             =   4335
               Width           =   2445
            End
            Begin VB.Label label 
               Caption         =   "Outbound Data"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   6840
               TabIndex        =   94
               Top             =   255
               Width           =   2925
            End
            Begin VB.Label label 
               Caption         =   "Inbound Data"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   240
               TabIndex        =   93
               Top             =   250
               Width           =   2445
            End
            Begin VB.Line Line2 
               X1              =   6615
               X2              =   6615
               Y1              =   240
               Y2              =   3700
            End
            Begin VB.Line Line1 
               X1              =   240
               X2              =   10200
               Y1              =   615
               Y2              =   615
            End
            Begin VB.Label Label20 
               Caption         =   "Filter Option"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   7080
               TabIndex        =   92
               Top             =   1125
               Width           =   2655
            End
         End
         Begin VB.Label Label10 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Data Interface"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   345
            Left            =   120
            TabIndex        =   75
            Top             =   105
            Width           =   10335
         End
      End
      Begin ActiveTabs.SSActiveTabPanel nnnn 
         Height          =   8295
         Left            =   0
         TabIndex        =   118
         Top             =   0
         Width           =   10605
         _ExtentX        =   18706
         _ExtentY        =   14631
         _Version        =   131083
         TabGuid         =   "AIM_JobWizard.frx":18E5
         Begin VB.Frame frAllocation 
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   4425
            Left            =   120
            TabIndex        =   119
            Top             =   480
            Width           =   10335
            Begin VB.CheckBox ckClearAllocTables 
               Caption         =   "Clear previously completed allocation data"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   405
               Left            =   360
               TabIndex        =   58
               Top             =   2160
               Width           =   4395
            End
            Begin VB.Frame frAllocException 
               Caption         =   "Exception Processing"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   1755
               Left            =   120
               TabIndex        =   121
               Top             =   240
               Width           =   9255
               Begin VB.OptionButton optAllocExceptions 
                  Caption         =   "Execute as outbound without review (Recommended)"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   210
                  TabIndex        =   57
                  Top             =   1250
                  Value           =   -1  'True
                  Width           =   6915
               End
               Begin VB.OptionButton optAllocExceptions 
                  Caption         =   "Hold only orders with exceptions for review"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   210
                  TabIndex        =   56
                  Top             =   815
                  Width           =   6915
               End
               Begin VB.OptionButton optAllocExceptions 
                  Caption         =   "Hold all orders for review"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   210
                  TabIndex        =   55
                  Top             =   380
                  Width           =   6915
               End
            End
         End
         Begin VB.Label Label25 
            Alignment       =   2  'Center
            BackColor       =   &H00FFFFFF&
            BorderStyle     =   1  'Fixed Single
            Caption         =   "Allocation"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   340
            Left            =   120
            TabIndex        =   120
            Top             =   105
            Width           =   10335
         End
      End
      Begin VB.Label Label4 
         Caption         =   "EXceed AIM Data Interface Command"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   -1  'True
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   315
         Left            =   120
         TabIndex        =   81
         Top             =   7080
         Width           =   3960
      End
   End
   Begin VB.Label Label19 
      Caption         =   "Label19"
      Height          =   495
      Left            =   6120
      TabIndex        =   82
      Top             =   3600
      Width           =   1215
   End
End
Attribute VB_Name = "AIM_JobWizard"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public AddFlag As Boolean
Public CancelFlag As Boolean
Public CommandLine As String
Public DisplayCommandLine As String

Dim Cn As ADODB.Connection
Dim AIM_Locations_List_Sp As ADODB.Command
Dim rsLcIdList As ADODB.Recordset
'sri batch fcst start
Dim AIM_ForecastUserAccess_Validate_Sp As ADODB.Command
'sri batch fcst end

Dim AIMUtilPath As String
Dim ClassOption As Integer
Dim FcstUpdCyc As Long
Dim Parameters() As String

Const TT_DATA_INTERFACE As String = "Data Interface"
Const TT_DEMAND_UPDATE As String = "Demand Update"
Const TT_ORDER_GENERATION As String = "Order Generation"
Const TT_SEASONALITY_PROFILE As String = "Seasonality Profile"
Const TT_VELOCITY_CODE As String = "Velocity Code"
Const TT_ORDER_POLICY As String = "Order Policy"
Const TT_FORECAST_BATCH As String = "Forecast Batch"
Const TT_ALLOCATION As String = "Allocation"
Const TT_DPFORECAST As String = "Demand Plan Forecast"

Private Function BldCommandLine(JobId As Integer)
On Error GoTo ErrorHandler
    Dim i As Integer
    
    'Clear the Parameters
    For i = LBound(Parameters) + 1 To UBound(Parameters)
        Parameters(i) = ""
    Next i
    
    'Set the common parameters
    Parameters(0) = AIMUtilPath
    Parameters(1) = Format(JobId, "00")
    Parameters(2) = IIf(Trim(Me.txtServer.Text) = "", gServer, Trim(Me.txtServer.Text))
    Parameters(3) = IIf(Trim(Me.txtDatabase.Text) = "", gDataBase, Trim(Me.txtDatabase.Text))
    Parameters(4) = IIf(Trim(Me.txtUserId.Text) = "", gUserID, Trim(Me.txtUserId.Text))
    Parameters(5) = IIf(Trim(Me.txtPassword.Text) = "", gPassWord, Trim(Me.txtPassword.Text))
    Parameters(6) = IIf(Me.ckUseWinNTAuth = vbChecked, "Y", "N")
    
    Select Case JobId
        Case 10                 'Data Interface
            'Set the Transaction Set
            For i = Me.optTxnSet.LBound To Me.optTxnSet.UBound
                If Me.optTxnSet(i).Value = True Then
                    Select Case i
                        Case 0
                            Parameters(7) = g_DI_TT_DD
                        Case 1
                            Parameters(7) = g_DI_TT_LT
                        Case 2
                            Parameters(7) = g_DI_TT_HS
                        Case 3
                            Parameters(7) = g_DI_TT_PO
                        Case 4
                            Parameters(7) = g_DI_TT_SD
                        Case 5
                            Parameters(7) = g_DI_TT_SS
                        Case 6
                            Parameters(7) = g_DI_TT_VN
                        Case 7
                            Parameters(7) = g_DI_TT_SL
                        Case 8
                            Parameters(7) = g_DI_TT_LC
                        Case 9
                            Parameters(7) = g_DI_TT_RO
                        Case 10
                            Parameters(7) = g_DI_TT_RP
                        Case 11
                            Parameters(7) = g_DI_TT_RS
                        Case 12
                            Parameters(7) = g_DI_TT_AO
                        Case 13
                            Parameters(7) = g_DI_TT_AS
                        Case 14
                            Parameters(7) = g_DI_TT_IS
                        Case 15
                            Parameters(7) = g_DI_TT_KB
                        Case 16
                            Parameters(7) = g_DI_TT_FA
                    End Select
                End If
            Next i
            
            'Set the File Name
            If Trim(Me.txtFileName.Text) = "" Then
                Parameters(8) = "All"   'Untranslate
            Else
                If StrComp(Me.txtFileName.Text, getTranslationResource("All")) = 0 Then
                    Parameters(8) = "All"   'Untranslate
                Else
                    Parameters(8) = Me.txtFileName.Text
                End If
            End If
        
            If Parameters(7) = g_DI_TT_PO Then
                Parameters(9) = dcPoFilterOption.Text
            ElseIf Parameters(7) = g_DI_TT_AO Then
                Parameters(9) = IIf(Me.ckIgnoreRevStatus = vbChecked, vbTrue, vbFalse)
            Else
                'Parameters(9) = " "
            End If
        
        Case 20                 'Demand Update
            Parameters(7) = IIf(Me.ckAutoOption = vbChecked, "Y", "N")        'Auto Option
            If StrComp(Me.dcLcId.Text, getTranslationResource("All")) = 0 Then
                Parameters(8) = "All"   'Untranslate
            Else
                Parameters(8) = Me.dcLcId.Text                                          'Location Option
            End If
            
            Parameters(9) = Format(Me.txtFcstYear.Value, "0000")                    'Forecast Year
            Parameters(10) = Format(Me.txtFcstPeriod.Value, "00")                   'Forecast Period
            Parameters(11) = IIf(Me.ckForceUpdateFlag = vbChecked, "Y", "N")  'Force Update Flag
            Parameters(12) = IIf(Me.ckMDCOption = vbChecked, "Y", "N")        'MDC Option
        
        Case 30                 'Order Generation
            For i = Me.optFilterOption.LBound To Me.optFilterOption.UBound
                If Me.optFilterOption(i).Value = True Then
                    Select Case i
                        Case 0              'All Scheduled Review Cycles
                            Parameters(7) = "A"
                        Case 1              'Specified Review Cycle
                            Parameters(7) = "R"
                        Case 2              'Specified Vendor/Assortment
                            Parameters(7) = "V"
                    End Select
                End If
            Next i
            
            If StrComp(Me.txtRevCycle.Text, getTranslationResource("All")) = 0 Then
                Parameters(8) = "All"   'Untranslate
            Else
                Parameters(8) = Me.txtRevCycle.Text
                If Parameters(8) = "" Then Parameters(8) = "All"
            End If
            
            If StrComp(Me.txtVnId.Text, getTranslationResource("All")) = 0 Then
                Parameters(9) = "All"   'Untranslate
            Else
                Parameters(9) = Me.txtVnId.Text
                If Parameters(9) = "" Then Parameters(9) = "All"
            End If
            
            If StrComp(Me.txtAssortment.Text, getTranslationResource("All")) = 0 Then
                Parameters(10) = "All"  'Untranslate
            Else
                Parameters(10) = Me.txtAssortment.Text
                If Parameters(10) = "" Then Parameters(10) = "All"
            End If
            
            'Set the processing options
            Parameters(11) = IIf(Me.ckClearPO = vbChecked, "Y", "N")
            Parameters(12) = IIf(Me.ckLT_Excpts = vbChecked, "Y", "N")
            Parameters(13) = IIf(Me.ckPE_Excpts = vbChecked, "Y", "N")
            Parameters(14) = "Y"
        
        Case 40                 'Seasonality Profile Update
            If StrComp(Me.dcLcId2.Text, getTranslationResource("All")) = 0 Then
                Parameters(7) = "All"   'Untranslate
            Else
                Parameters(7) = Me.dcLcId2.Text
            End If
            
            Parameters(8) = Format(Me.txtEndYear.Value, "0000")
            Parameters(9) = Format(Me.txtEndPeriod.Value, "00")
            Parameters(10) = Format(Me.txtNbrOfYears.Value, "#0")
            Parameters(11) = Format(Me.txtSaVersion.Value, "##0")
            Parameters(12) = Format(Me.txtClassOption.Value, "0")
        
        Case 50                 'Velocity Code Assignment
            For i = Me.optSelectionOpt.LBound To Me.optSelectionOpt.UBound
                If Me.optSelectionOpt(i).Value = True Then
                    Select Case i
                        Case 0
                            Parameters(7) = "G"
                            Parameters(8) = "All"
                        Case 1
                            Parameters(7) = "A"
                            Parameters(8) = "All"
                        Case 2
                            Parameters(7) = "S"
                            
                            If StrComp(Me.dcLcId3.Text, getTranslationResource("All")) = 0 Then
                                Parameters(8) = "All"   'Untranslate
                            Else
                                Parameters(8) = Me.dcLcId3.Text
                            End If
                    End Select
                End If
            Next i
            
            'Check for an invalid location id value
            If Trim(Parameters(8)) = "" Then
                rsLcIdList.MoveFirst
                Parameters(8) = rsLcIdList("Lcid").Value
            End If
            
        Case 60                 'Order Policy Assignment
            'Not currently available
        
        'sri batch fcst start
        Case 70
            Parameters(7) = dcFcstId.Text
             For i = Me.OptDemPlanFcst.LBound To Me.OptDemPlanFcst.UBound
                If Me.OptDemPlanFcst(i).Value = True Then
                    Parameters(8) = i
                    ' 0 = Run  Demand Forecast Only
                    ' 1 = Run Demand Forecast and Export Forecast Plan
                    ' 2 = Export Forecast Plan only
                End If
            Next i
'            Parameters(8) = dcFcstType.Columns(0).Value
'            For I = 9 To 12
'
'                If ckElement(I - 9) = vbChecked Then
'                    Parameters(I) = "Y"
'                Else
'                    Parameters(I) = "N"
'                End If
'            Next I
            'sri batch fcst end
    
        Case 80     'Allocation
            For i = Me.optAllocExceptions.LBound To Me.optAllocExceptions.UBound
                If Me.optAllocExceptions(i).Value = True Then
                    Parameters(7) = i
                    ' 0 = Hold all orders
                    ' 1 = Hold only exceptions
                    ' 2 = Hold none
                End If
            Next i
            'Set the processing options
            Parameters(8) = IIf(Me.ckClearAllocTables = vbChecked, vbTrue, vbFalse)
            
        Case 90 'Demand Plan forecast
            Parameters(7) = dcFcstSetupID.Text
            
    End Select
    
    'Update the command line
    CommandLine = ""
    
    'Corrections for the future: make flags or some other type of delimiter (instead of space) for the parameters.
    'Currently, if one of the parameter values contains a space, it breaks the sequence of parameters as read by the server exe
    'This is a serious error (NOTE: Do not forget to update the server executable commandline parser, when this is corrected.)
    For i = LBound(Parameters) To UBound(Parameters)
        CommandLine = CommandLine + Parameters(i) + " "
    Next i
    
    'Hide the password from view in the txtCmdDesc
    txtCmdDescription = ""
    For i = LBound(Parameters) To UBound(Parameters)
        If i = 5 Then   'Password
            txtCmdDescription = txtCmdDescription + "********* "
        Else
            txtCmdDescription = txtCmdDescription + Parameters(i) + " "
        End If
    Next i
    
    DisplayCommandLine = txtCmdDescription.Text
        
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_JobWizard::BldCommandLine", Now, gDRGeneralError, True, Err
End Function

Private Function CmdLineParse(ParseLine As String, StrArray() As String)
On Error GoTo ErrorHandler

    Dim ArrayLen As Integer
    Dim Count As Integer
    Dim Path As String
    Dim PrmLine As String
    
    'Check for an empty Parse Line
    If Trim(ParseLine) = "" Then
        Exit Function
    End If
    
    'Save the current number of array elements
    ArrayLen = UBound(StrArray)
    
    'Get the path for the AIM Utility Executable
    Path = ParsePath(ParseLine, PATH_ONLY)
    
    'Get the remaining parts of the command line
    PrmLine = ParsePath(ParseLine, FILE_ONLY)
    
    'Get rid of any redundant spaces in the command line
    PrmLine = TrimSpace(PrmLine)
    
    If PrmLine = "" Then
        CmdLineParse = -1
        Exit Function
    Else
        'Split the Parameters into the StrArray
        StrArray = Split(PrmLine)
        
        'Parameter(0) is the path and executable name
        StrArray(0) = Path + StrArray(0)
        
        'Save the Array Length
        Count = UBound(StrArray) - 1
        
        'Redimension the array
        ReDim Preserve StrArray(ArrayLen)
        
        CmdLineParse = Count

    End If
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_JobWizard::CmdLineParse", Now, gDRGeneralError, True, Err
End Function

Private Function TogglePOFilter()
On Error GoTo ErrorHandler

    Dim i As Integer
    
    For i = Me.optTxnSet.LBound To Me.optTxnSet.UBound
        If Me.optTxnSet(i).Value = True Then
            Select Case i
                Case 3
                     Label20.Enabled = True
                    dcPoFilterOption.Enabled = True
                Case Else
                   Label20.Enabled = False
                    dcPoFilterOption.Enabled = False
            End Select
        End If
    Next i
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_JobWizard::TogglePOFilter", Now, gDRGeneralError, True, Err
End Function

Private Function SetFormVariables(CmdLine As String)
On Error GoTo ErrorHandler

    Dim JobId As Integer
    Dim NbrParams As Integer
    
    'Initialize the Command Line Parameters Array
    ReDim Parameters(20)
    
    'Initialize the Command Line Description
    NbrParams = CmdLineParse(CmdLine, Parameters())
    
    'Determine the Transaction Type
    If Trim(CmdLine) = "" Or NbrParams = -1 Then
        JobId = 0
    Else
        JobId = Parameters(1)
    End If
    
    Select Case JobId                   'Job Id
        Case 10                             'Data Interface
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_DATA_INTERFACE
            'Set the Transaction Set
            Select Case Parameters(7)
                Case g_DI_TT_DD
                    Me.optTxnSet(0).Value = True
                Case g_DI_TT_LT
                    Me.optTxnSet(1).Value = True
                Case g_DI_TT_HS
                    Me.optTxnSet(2).Value = True
                Case g_DI_TT_PO
                    Me.optTxnSet(3).Value = True
                Case g_DI_TT_SD
                    Me.optTxnSet(4).Value = True
                Case g_DI_TT_SS
                    Me.optTxnSet(5).Value = True
                Case g_DI_TT_VN
                    Me.optTxnSet(6).Value = True
                Case g_DI_TT_SL
                    Me.optTxnSet(7).Value = True
                Case g_DI_TT_LC
                    Me.optTxnSet(8).Value = True
                Case g_DI_TT_RO
                    Me.optTxnSet(9).Value = True
                Case g_DI_TT_RP
                    Me.optTxnSet(10).Value = True
                Case g_DI_TT_RS
                    Me.optTxnSet(11).Value = True
                Case g_DI_TT_AO
                    Me.optTxnSet(12).Value = True
                Case g_DI_TT_AS
                    Me.optTxnSet(13).Value = True
                Case g_DI_TT_IS
                    Me.optTxnSet(14).Value = True
                Case g_DI_TT_FA
                    Me.optTxnSet(16).Value = True
                    
            End Select
            'Set the File Name
            If Trim(Parameters(8)) = "" Then
                Me.txtFileName.Text = getTranslationResource("All")
            Else
                Me.txtFileName.Text = Parameters(8)
            End If
            'Set parameters before triggering optevent
            Me.optJobType(0).Value = True
            
        Case 20                             'Demand Update
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_DEMAND_UPDATE
            'Auto Option
            Me.ckAutoOption = IIf(Parameters(7) = "Y", vbChecked, vbUnchecked)
            'Lcid Option
            Me.dcLcId.Text = Parameters(8)
            If StrComp(Me.dcLcId.Text, "All") = 0 Then
                Me.dcLcId.Text = getTranslationResource("All")
            End If
            'Forecast Period
            Me.txtFcstPeriod.Value = CInt(Parameters(10))
            'Forecast Year
            Me.txtFcstYear = CInt(Parameters(9))
            'Force Update Flag
            Me.ckForceUpdateFlag = IIf(Parameters(11) = "Y", vbChecked, vbUnchecked)
            'MDC Option
            Me.ckMDCOption = IIf(Parameters(12) = "Y", vbChecked, vbUnchecked)
            'Set parameters before triggering optevent
            Me.optJobType(1).Value = True
        
        Case 30                             'Order Generation
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_ORDER_GENERATION
            'Set the Filter Option
            Select Case Parameters(7)
                Case "A"
                    Me.optFilterOption(0).Value = True
                    'Text box and look-up button -- toggle access
                    Me.txtRevCycle.Enabled = False
                    Me.cmdReviewCycleLookUp.Enabled = False
                    'Text box and look-up button -- toggle access
                    Me.txtVnId.Enabled = False
                    Me.cmdVnIdLookUp.Enabled = False
                    'Text box -- toggle access
                    Me.txtAssortment.Enabled = False
                    'Text box -- set default text
                    Me.txtRevCycle.Text = getTranslationResource("All")
                    Me.txtVnId.Text = getTranslationResource("All")
                    Me.txtAssortment.Text = getTranslationResource("All")
                    
                Case "R"
                    Me.optFilterOption(1).Value = True
                    'Text box and look-up button -- toggle access
                    Me.txtRevCycle.Enabled = True
                    Me.cmdReviewCycleLookUp.Enabled = True
                    'Text box and look-up button -- toggle access
                    Me.txtVnId.Enabled = False
                    Me.cmdVnIdLookUp.Enabled = False
                    'Text box -- toggle access
                    Me.txtAssortment.Enabled = False
                    'Text box -- set text
                    Me.txtRevCycle.Text = Parameters(8)
                    If StrComp(Me.txtRevCycle.Text, "All") = 0 Then
                        Me.txtRevCycle.Text = getTranslationResource("All")
                    End If
                    Me.txtVnId.Text = getTranslationResource("All")
                    Me.txtAssortment.Text = getTranslationResource("All")
                
                Case "V"
                    Me.optFilterOption(2).Value = True
                    'Text box and look-up button -- toggle access
                    Me.txtRevCycle.Enabled = False
                    Me.cmdReviewCycleLookUp.Enabled = False
                    'Text box and look-up button -- toggle access
                    Me.txtVnId.Enabled = True
                    Me.cmdVnIdLookUp.Enabled = True
                    'Text box -- toggle access
                    Me.txtAssortment.Enabled = True
                    'Text box -- set text
                    Me.txtRevCycle.Text = getTranslationResource("All")
                    Me.txtVnId.Text = Parameters(9)
                    If StrComp(Me.txtVnId.Text, "All") = 0 Then
                        Me.txtVnId.Text = getTranslationResource("All")
                    End If
                    Me.txtAssortment.Text = Parameters(10)
                    If StrComp(Me.txtAssortment.Text, "All") = 0 Then
                        Me.txtAssortment.Text = getTranslationResource("All")
                    End If
            End Select
            'Set the processing options
            Me.ckClearPO = IIf(Parameters(11) = "Y", vbChecked, vbUnchecked)
            Me.ckLT_Excpts = IIf(Parameters(12) = "Y", vbChecked, vbUnchecked)
            Me.ckPE_Excpts = IIf(Parameters(13) = "Y", vbChecked, vbUnchecked)
            Me.ckOutputOpt = vbChecked
            'Set parameters before triggering optevent
            Me.optJobType(2).Value = True
        
        Case 40                             'Seasonality Profile Generation
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_SEASONALITY_PROFILE
            'Set the Location Id
            Me.dcLcId2.Text = Parameters(7)
            If StrComp(Me.dcLcId2.Text, "All") = 0 Then
                Me.dcLcId2.Text = getTranslationResource("All")
            End If
            If StrComp(Me.txtAssortment.Text, "All") = 0 Then
                Me.txtAssortment.Text = getTranslationResource("All")
            End If
            'Set the Ending Year
            Me.txtEndYear.Text = CInt(Parameters(8))
            'Set the Ending Week
            Me.txtEndPeriod.Value = CInt(Parameters(9))
            'Number of Years
            Me.txtNbrOfYears.Value = CInt(Parameters(10))
            'SA Version
            Me.txtSaVersion.Value = CInt(Parameters(11))
            'Class Option
            Me.txtClassOption.Value = CInt(Parameters(12))
            'Set parameters before triggering optevent
            Me.optJobType(3).Value = True
            
        Case 50                             'Velocity Code Assignment
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_VELOCITY_CODE
            'Location select
            Select Case Parameters(7)
                Case "G"    'rank globally
                    Me.optSelectionOpt(0).Value = True
                Case "A"    'rank by location
                    Me.optSelectionOpt(1).Value = True
                Case "S"    'rank selected
                    Me.optSelectionOpt(2).Value = True
            End Select
            'Selection location
            Me.dcLcId3.Text = Parameters(8)
            If StrComp(Me.dcLcId3.Text, "All") = 0 Then
                Me.dcLcId3.Text = getTranslationResource("All")
            End If
            'Set parameters before triggering optevent
            Me.optJobType(4).Value = True
        
        Case 60                             'Order Policy Assignment
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_ORDER_POLICY
            'Set parameters before triggering optevent
            Me.optJobType(5).Value = True
        Case 70
           'Mohammed - Populate the Job Info. - Begin
           Me.atAIMJobs.SelectedTab = TT_FORECAST_BATCH
           'Forecast Id
           Me.dcFcstId.Text = Parameters(7)
           Me.OptDemPlanFcst(Parameters(8)).Value = True
            Me.optJobType(6).Value = True
        Case 80                 'Allocation
            'Set the Job Tab
            Me.atAIMJobs.SelectedTab = TT_ALLOCATION
            'Set the Filter Option
            Me.optAllocExceptions(Parameters(7)).Value = True
            'Set the processing options
            Me.ckClearAllocTables = IIf(Parameters(8) = vbTrue, vbChecked, vbUnchecked)
            'Set parameters before triggering optevent
            Me.optJobType(7).Value = True
            
        Case 90 'Demand Plan forecast
           Me.atAIMJobs.SelectedTab = TT_DPFORECAST
           'Forecast Id
           Me.dcFcstSetupID.Text = Parameters(7)
           
        Case Else               'No command line specified
            'Reinitialize the parameters array
            ReDim Parameters(20)
            Parameters(1) = "0"
            
            InitializeDefaultTransaction
            
    End Select
    
    'Initialize the Common Parameters
    If CInt(Parameters(1)) >= 10 And CInt(Parameters(1)) <= 80 Then
        'Initialize Common Paramters
        Me.txtServer.Text = Parameters(2)
        Me.txtDatabase.Text = Parameters(3)
        Me.txtUserId.Text = Parameters(4)
        Me.txtPassword.Text = Parameters(5)
        Me.ckUseWinNTAuth = IIf(Parameters(6) = "Y", vbChecked, vbUnchecked)
    Else
        'Initialize Common Parameters
        Parameters(0) = AIMUtilPath
        Parameters(1) = "10"
        Me.txtServer.Text = gServer
        Me.txtDatabase.Text = gDataBase
        Me.txtUserId.Text = gUserID
        Me.txtPassword.Text = gPassWord
        Me.ckUseWinNTAuth = IIf(gWinNTAuth = "Y", vbChecked, vbUnchecked)
        
        BldCommandLine 10
    
    End If
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_JobWizard::SetFormVariables", Now, gDRGeneralError, True, Err
End Function

Private Function InitializeDefaultTransaction()
On Error GoTo ErrorHandler

    'Set the Job Tab -- default to the first option
    Me.atAIMJobs.SelectedTab = 1    'TT_DATA_INTERFACE
    
    'Initialize Form Variables -- Data Interface
    Me.optTxnSet(0).Value = True
    Me.txtFileName.Text = getTranslationResource("All")

    'Initialize Form Variables -- Demand Update
    Me.ckAutoOption = vbChecked
    Me.dcLcId.Text = getTranslationResource("All")
    Me.ckForceUpdateFlag = vbChecked
    Me.txtFcstPeriod.Value = 1
    Me.txtFcstYear.Value = Year(Date)
    Me.ckMDCOption = vbUnchecked

    'Initialize Form Variables -- Order Generation
    Me.optFilterOption(0).Value = True
    
    'Text box and look-up button -- toggle access
    Me.txtRevCycle.Enabled = False
    Me.cmdReviewCycleLookUp.Enabled = False
    Me.txtRevCycle.Text = getTranslationResource("All")
    
    'Text box and look-up button -- toggle access
    Me.txtVnId.Enabled = False
    Me.cmdVnIdLookUp.Enabled = False
    Me.txtVnId.Text = getTranslationResource("All")
    
    'Text box -- toggle access
    Me.txtAssortment.Enabled = False
    Me.txtAssortment.Text = getTranslationResource("All")
    
    Me.ckClearPO = vbUnchecked
    Me.ckLT_Excpts = vbUnchecked
    Me.ckPE_Excpts = vbUnchecked
    Me.ckOutputOpt = vbChecked
    
    'Initialize Form Variables -- Seasonality Profiles
    Me.dcLcId2.Text = getTranslationResource("All")
    Me.txtEndYear.Value = IIf((FcstUpdCyc \ 100) >= txtEndYear.MinValue, (FcstUpdCyc \ 100), Null)
    Me.txtEndPeriod.Value = IIf((FcstUpdCyc - ((FcstUpdCyc \ 100) * 100)) >= txtEndPeriod.MinValue, (FcstUpdCyc - ((FcstUpdCyc \ 100) * 100)), Null)
    Me.txtNbrOfYears.Value = 3
    Me.txtSaVersion.Value = 0
    Me.txtClassOption.Value = ClassOption
    
    'Initialize Form Variables -- Velocity Code Assignment
    Me.optSelectionOpt(1).Value = True
    Me.dcLcId3.Text = ""
    
    'Set parameters before triggering optevent
    Me.optJobType(0).Value = True
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_JobWizard::InitializeDefaultTransaction", Now, gDRGeneralError, True, Err
End Function

Private Sub dcFcstId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcFcstId.Columns(0).Name = "fcstid"
    Me.dcFcstId.Columns(0).Caption = getTranslationResource("Forecast ID")
    Me.dcFcstId.Columns(0).Width = 1400
    Me.dcFcstId.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcFcstId.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcFcstId.Columns(0).DataField = "fcstid"

    Me.dcFcstId.Columns(1).Name = "fcstdesc"
    Me.dcFcstId.Columns(1).Caption = getTranslationResource("Description")
    Me.dcFcstId.Columns(1).Width = 2880
    Me.dcFcstId.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcFcstId.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcFcstId.Columns(1).DataField = "fcstdesc"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcFcstId, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcFcstID.Columns.Count - 1
'        dcFcstID.Columns(IndexCounter).HasHeadBackColor = True
'        dcFcstID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
     f_HandleErr , , , "AIM_JobWizard::dcFcstId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcFcstSetupID_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcFcstSetupID.Columns(0).Name = "fcstid"
    Me.dcFcstSetupID.Columns(0).Caption = getTranslationResource("Forecast ID")
    Me.dcFcstSetupID.Columns(0).Width = 1400
    Me.dcFcstSetupID.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcFcstSetupID.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcFcstSetupID.Columns(0).DataField = "fcstid"

    Me.dcFcstSetupID.Columns(1).Name = "fcstdesc"
    Me.dcFcstSetupID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcFcstSetupID.Columns(1).Width = 2880
    Me.dcFcstSetupID.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcFcstSetupID.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcFcstSetupID.Columns(1).DataField = "fcstdesc"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcFcstSetupID, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcFcstSetupID.Columns.Count - 1
'        dcFcstSetupID.Columns(IndexCounter).HasHeadBackColor = True
'        dcFcstSetupID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcFcstSetupID_InitColumnProps)"
     f_HandleErr , , , "AIM_JobWizard::dcFcstId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub


Private Sub dcPoFilterOption_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcPoFilterOption.Columns(0).Caption = getTranslationResource("Code")
    Me.dcPoFilterOption.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcPoFilterOption.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcPoFilterOption.Columns(0).Width = 720
    
    Me.dcPoFilterOption.Columns(1).Caption = getTranslationResource("Description")
    Me.dcPoFilterOption.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcPoFilterOption.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcPoFilterOption.Columns(1).Width = 2000
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcPoFilterOption, ACW_EXPAND
    End If

'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To cPoFilterOption.Columns.Count - 1
'        cPoFilterOption.Columns(IndexCounter).HasHeadBackColor = True
'        cPoFilterOption.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcPoFilterOption_InitColumnProps)"
     f_HandleErr , , , "AIM_JobWizard::dcPoFilterOption_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub optFilterOption_Click(Index As Integer)
On Error GoTo ErrorHandler

    If Me.optFilterOption(Index).Value = True Then
        Select Case Index
            Case 0  'All
                'Text box and look-up button -- toggle access
                Me.txtRevCycle.Enabled = False
                Me.cmdReviewCycleLookUp.Enabled = False
                'Text box and look-up button -- toggle access
                Me.txtVnId.Enabled = False
                Me.cmdVnIdLookUp.Enabled = False
                'Text box -- toggle access
                Me.txtAssortment.Enabled = False
                
                'Text box -- set default text
                Me.txtRevCycle.Text = getTranslationResource("All")
                Me.txtVnId.Text = getTranslationResource("All")
                Me.txtAssortment.Text = getTranslationResource("All")
                
            Case 1  'Review Cycle
                Me.txtRevCycle.Text = ""
                'Text box and look-up button -- toggle access
                Me.txtRevCycle.Enabled = True
                Me.cmdReviewCycleLookUp.Enabled = True
                'Text box and look-up button -- toggle access
                Me.txtVnId.Enabled = False
                Me.cmdVnIdLookUp.Enabled = False
                'Text box -- toggle access
                Me.txtAssortment.Enabled = False
                
                'Text box -- set default text
                Me.txtVnId.Text = getTranslationResource("All")
                Me.txtAssortment.Text = getTranslationResource("All")
            
            Case 2  'Vendor Assortment
                Me.txtVnId.Text = ""
                Me.txtAssortment.Text = ""
                
                'Text box and look-up button -- toggle access
                Me.txtRevCycle.Enabled = False
                Me.cmdReviewCycleLookUp.Enabled = False
                'Text box and look-up button -- toggle access
                Me.txtVnId.Enabled = True
                Me.cmdVnIdLookUp.Enabled = True
                'Text box -- toggle access
                Me.txtAssortment.Enabled = True
                
                'Text box -- set default text
                Me.txtRevCycle.Text = getTranslationResource("All")
        
        End Select
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optFilterOption_Click)"
     f_HandleErr , , , "AIM_JobWizard::optFilterOption_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optJobType_Click(Index As Integer)
On Error GoTo ErrorHandler

    If Parameters(0) = "" Then Exit Sub 'Premature call from SetFormVariables. Don't process these yet.
    
    Select Case Index
        Case 0          'Data Interface
            Me.atAIMJobs.SelectedTab = TT_DATA_INTERFACE
            Parameters(1) = "10"
            BldCommandLine 10
            
        Case 1          'Demand Update
            Me.atAIMJobs.SelectedTab = TT_DEMAND_UPDATE
            Parameters(1) = "20"
            BldCommandLine 20
    
        Case 2          'Order Generation
            Me.atAIMJobs.SelectedTab = TT_ORDER_GENERATION
            Parameters(1) = "30"
            BldCommandLine 30
            
        Case 3          'Seasonality Profile Update
            Me.atAIMJobs.SelectedTab = TT_SEASONALITY_PROFILE
            Parameters(1) = "40"
            BldCommandLine 40
        
        Case 4          'Velocity Code Assignment
            Me.atAIMJobs.SelectedTab = TT_VELOCITY_CODE
            Parameters(1) = "50"
            BldCommandLine 50
        
        Case 5          'Order Policy Assignment
            Me.atAIMJobs.SelectedTab = TT_ORDER_POLICY
            Parameters(1) = "60"
             BldCommandLine 60
        Case 6          'Forecast Batch
            Me.atAIMJobs.SelectedTab = TT_FORECAST_BATCH
            Parameters(1) = "70"
             BldCommandLine 70
        
        Case 7          'Allocation
            Me.atAIMJobs.SelectedTab = TT_ALLOCATION
            Parameters(1) = "80"
            BldCommandLine 80
            
        Case 8          'Demand Plan forecast
            Me.atAIMJobs.SelectedTab = TT_DPFORECAST
            Parameters(1) = "90"
             BldCommandLine 90
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optJobType_Click)"
     f_HandleErr , , , "AIM_JobWizard::optJobType_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()

    CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
     'f_HandleErr Me.Caption & "(cmdCancel_Click)"
      f_HandleErr , , , "AIM_JobWizard::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdInFileName_Click()
On Error GoTo ErrorHandler
    Dim TempString As String
    
    Me.CommonDialog1.CancelError = True ' Causes a trappable error to occur when the user hits the 'Cancel' button
    Me.CommonDialog1.DialogTitle = getTranslationResource(TT_DATA_INTERFACE)
    
    TempString = getTranslationResource("All")
    If StrComp(Me.txtFileName.Text, TempString) = 0 Then
        Me.CommonDialog1.FileName = "All"   'Untranslate
    Else
        Me.CommonDialog1.FileName = Me.txtFileName.Text
    End If
    Me.CommonDialog1.Filter = "Text Format (*.TXT)| *.TXT"
    Me.CommonDialog1.FilterIndex = 1
    Me.CommonDialog1.Flags = cdlOFNOverwritePrompt
'    Me.CommonDialog1.hDC = Me.hDC  'Property is read-only
    
    On Error Resume Next
    Me.CommonDialog1.ShowOpen
    If Not Err = cdlCancel Then '
        Me.txtFileName.Text = Me.CommonDialog1.FileName
        'Check for All
        If InStr(Me.txtFileName.Text, "\" & TempString & ".") > 0 _
        Or InStr(Me.txtFileName.Text, "*") > 0 _
        Then
            Me.txtFileName.Text = getTranslationResource("All")
        End If
    End If
    On Error GoTo ErrorHandler

Exit Sub
ErrorHandler:
    ' f_HandleErr Me.Caption & "(cmdInFileName_Click)"
      f_HandleErr , , , "AIM_JobWizard::cmdInFileName_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdRefresh_Click()
On Error GoTo ErrorHandler

    'Validate
    If ValidateInput() Then
        BldCommandLine CInt(Parameters(1))
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdRefresh_Click)"
     f_HandleErr , , , "AIM_JobWizard::cmdRefresh_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    'Validate
    If ValidateInput() Then
        BldCommandLine CInt(Parameters(1))
        Unload Me
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_JobWizard::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReviewCycleLookUp_Click()
On Error GoTo ErrorHandler

    If StrComp(Me.txtRevCycle.Text, getTranslationResource("All")) = 0 Then
        AIM_ReviewCycleLookUp.RevCycleKey = "All"   'Untranslate
    Else
        AIM_ReviewCycleLookUp.RevCycleKey = Me.txtRevCycle.Text
    End If
    
    Set AIM_ReviewCycleLookUp.Cn = Cn
    AIM_ReviewCycleLookUp.Show vbModal
    
    If Not AIM_ReviewCycleLookUp.CancelFlag Then
        Me.txtRevCycle.Text = AIM_ReviewCycleLookUp.RevCycleKey
        If StrComp(Me.txtRevCycle.Text, "All") = 0 Then
            Me.txtRevCycle.Text = getTranslationResource("All")
        End If
        
    End If

Exit Sub
ErrorHandler:
    ' f_HandleErr Me.Caption & "(cmdReviewCycleLookUp_Click)"
     f_HandleErr , , , "AIM_JobWizard::cmdReviewCycleLookUp_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdVnIdLookUp_Click()
On Error GoTo ErrorHandler

    If StrComp(Me.txtVnId.Text, getTranslationResource("All")) = 0 Then
        AIM_VendorLookUp.VnIdKey = "All"   'Untranslate
    Else
        AIM_VendorLookUp.VnIdKey = Me.txtVnId.Text
    End If

    If StrComp(Me.txtAssortment.Text, getTranslationResource("All")) = 0 Then
        AIM_VendorLookUp.AssortKey = "All"   'Untranslate
    Else
        AIM_VendorLookUp.AssortKey = Me.txtAssortment.Text
    End If
    
    Set AIM_VendorLookUp.Cn = Cn
    AIM_VendorLookUp.Show vbModal
    
    If Not AIM_VendorLookUp.CancelFlag Then
        Me.txtVnId.Text = AIM_VendorLookUp.VnIdKey
        If StrComp(Me.txtVnId.Text, "All") = 0 Then Me.txtVnId.Text = getTranslationResource("All")
        Me.txtAssortment.Text = AIM_VendorLookUp.AssortKey
        If StrComp(Me.txtAssortment.Text, "All") = 0 Then Me.txtAssortment.Text = getTranslationResource("All")
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdVnIdLookUp_Click)"
     f_HandleErr , , , "AIM_JobWizard::cmdVnIdLookUp_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLcId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLcId.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLcId.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId.Columns(0).Width = 720
    
    Me.dcLcId.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLcId.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId.Columns(1).Width = 2000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLcId, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcLcID.Columns.Count - 1
'        dcLcID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLcID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLcId_InitColumnProps)"
     f_HandleErr , , , "AIM_JobWizard::dcLcId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLcId2_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLcId2.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLcId2.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId2.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId2.Columns(0).Width = 720
    
    Me.dcLcId2.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLcId2.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId2.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId2.Columns(1).Width = 2000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLcId2, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcLcID2.Columns.Count - 1
'        dcLcID2.Columns(IndexCounter).HasHeadBackColor = True
'        dcLcID2.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLcId2_InitColumnProps)"
     f_HandleErr , , , "AIM_JobWizard::dcLcId2_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLcId3_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLcId3.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLcId3.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId3.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId3.Columns(0).Width = 720
    
    Me.dcLcId3.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLcId3.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId3.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId3.Columns(1).Width = 2000

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLcId3, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcLcID3.Columns.Count - 1
'        dcLcID3.Columns(IndexCounter).HasHeadBackColor = True
'        dcLcID3.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLcId3_InitColumnProps)"
     f_HandleErr , , , "AIM_JobWizard::dcLcId3_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler
 
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    If Err.Number = 40002 Then
        'ignore this
    Else
     'f_HandleErr Me.Caption & "(Form_Activate)"
      f_HandleErr , , , "AIM_JobWizard::Form_Activate", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim RtnCode As Integer
    Dim strMessage As String
    Dim rsCodeLookup As ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    Dim strSQL As String
    Dim rsFcstId As Recordset
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim rsSysCtrl As ADODB.Recordset
       
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03000")
    If StrComp(strMessage, "STATMSG03000") = 0 Then strMessage = "Initializing Job Wizard... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server'
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build the Location List
    Set AIM_Locations_List_Sp = New ADODB.Command
    With AIM_Locations_List_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_Locations_List_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    
    Set rsLcIdList = New ADODB.Recordset
    With rsLcIdList
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    AIM_Locations_List_Sp.Parameters("@LcIdOption").Value = "All"    'Do not translate!
    rsLcIdList.Open AIM_Locations_List_Sp
    
    'Get the Last Forecast Update Cycle
    If f_IsRecordsetOpenAndPopulated(rsLcIdList) Then
        FcstUpdCyc = rsLcIdList("Last_FcstUpdCyc").Value

        'Build the Location Drop Downs
        Me.dcLcId.AddItem getTranslationResource("All") + vbTab + _
                             getTranslationResource("All Locations")
        Me.dcLcId2.AddItem getTranslationResource("All") + vbTab + _
                             getTranslationResource("All Locations")
        'Me.dcLcId3.AddItem getTranslationResource("All") + vbTab + _
                             getTranslationResource("All Locations")

        Do Until rsLcIdList.eof
            Me.dcLcId.AddItem rsLcIdList("LcId").Value + vbTab + rsLcIdList("LName").Value
            Me.dcLcId2.AddItem rsLcIdList("LcId").Value + vbTab + rsLcIdList("LName").Value
            Me.dcLcId3.AddItem rsLcIdList("LcId").Value + vbTab + rsLcIdList("LName").Value

            rsLcIdList.MoveNext
        Loop

    End If
    
    'Get the Command Name
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
    End With
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        ClassOption = rsSysCtrl("ClassOption").Value
        AIMUtilPath = Trim(rsSysCtrl("AIMBatchPath").Value)
        
        If AIMUtilPath = "" Then
            AIMUtilPath = "C:\Program Files\SSA Global\SSA DR Server\DRDataInterface.Exe"
        End If
    End If
    'Clean up
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    
    'sri begin
    'Build/Bind the dcConstraintType Drop Down
     Set AIM_CodeLookup_Get_Sp = New ADODB.Command
     With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_POFILTEROPTION
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcPoFilterOption.AddItem rsCodeLookup!CodeID + vbTab + rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    Else
        Me.dcPoFilterOption.AddItem getTranslationResource("A") + vbTab + getTranslationResource("All")
    End If
    dcPoFilterOption.Text = "A"
    
    'Clean up
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    
    Set AIM_ForecastUserAccess_Validate_Sp = New ADODB.Command
    With AIM_ForecastUserAccess_Validate_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ForecastUserAccess_Validate_Sp"
        .Parameters.Refresh
    End With

    'sri end
    
    
    strSQL = "SELECT Fcstid, FcstDesc FROM AimFcstSetUp Where FcstEnabled =1 " & _
            " ORDER BY Fcstid"
    
    Set rsFcstId = New ADODB.Recordset
    rsFcstId.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
     Me.dcFcstId.AddItem getTranslationResource("All") + vbTab + _
                             getTranslationResource("All Locations")
    
    'Set dcFcstId.DataSourceList = rsFcstId
    If f_IsRecordsetOpenAndPopulated(rsFcstId) Then
        Do Until rsFcstId.eof
            Me.dcFcstId.AddItem rsFcstId("FcstId").Value + vbTab + rsFcstId("FcstDesc").Value
            rsFcstId.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsFcstId) Then rsFcstId.Close
    

    'Initialize the Command Line Description
    SetFormVariables CommandLine
    
    'Initialize Cancel Flag
    CancelFlag = False
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Make the spin button visible
    Me.txtFcstPeriod.Spin.Visible = 1
    Me.txtFcstYear.Spin.Visible = 1
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'Clean up
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_JobWizard::Form_Load", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    On Error Resume Next
    
    If f_IsRecordsetValidAndOpen(rsLcIdList) Then
        rsLcIdList.Close
    End If
    Set rsLcIdList = Nothing
    'sri batch fcst start
    If Not (AIM_ForecastUserAccess_Validate_Sp Is Nothing) Then Set AIM_ForecastUserAccess_Validate_Sp.ActiveConnection = Nothing
    Set AIM_ForecastUserAccess_Validate_Sp = Nothing
    'sri batch fcst end
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)

    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Unload)"
     f_HandleErr , , , "AIM_JobWizard::Form_Unload", Now, gDRGeneralError, True, Err
End Sub

Private Sub optSelectionOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    If Index = 2 Then
        dcLcId3.Enabled = True
    Else
        dcLcId3.Enabled = False
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optSelectionOpt(" & Index & ")_Click)"
     f_HandleErr , , , "AIM_JobWizard::optSelectionOpt(" & Index & ")_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optTxnSet_Click(Index As Integer)
On Error GoTo ErrorHandler

    TogglePOFilter

    'Keep the Ignore Rev Status checkbox related to the option for Allocate Order (AO)
    Select Case Index
    Case 12     'Allocation Order
        ckIgnoreRevStatus.Enabled = True
    Case Else
        ckIgnoreRevStatus.Enabled = False
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optTxnSet(" & Index & ")_Click)"
     f_HandleErr , , , "AIM_JobWizard::optSelectionOpt::optTxnSet(" & Index & ")_Click", Now, gDRGeneralError, True, Err
End Sub

Private Function ValidateInput() As Boolean
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim ErrorText As String
    Dim RtnCode As Long
    'sri batch fcst start
    Dim AccessType As Integer
    Dim strMessage1 As String
    'sri batch fcst end
    
    strMessage = getTranslationResource("MSGBOX03000")
    If StrComp(strMessage, "MSGBOX03000") = 0 Then _
    strMessage = "The following data is required, or is invalid. " & _
                "Please provide correct values in the expected format for: "
    
    Select Case Parameters(1)
        Case 10                 'Data Interface
            'Add validation here
            
        Case 20                 'Demand Update
            'Add validation here
            
        Case 30                 'Order Generation
            If Me.optFilterOption(0).Value = True Then  'All Scheduled Review Cycles
                'Text boxes -- set default text
                Me.txtRevCycle.Text = getTranslationResource("All")
                Me.txtVnId.Text = getTranslationResource("All")
                Me.txtAssortment.Text = getTranslationResource("All")
                
            ElseIf Me.optFilterOption(1).Value = True Then  'Specified Review Cycle
                'Validate RevCycle
                If txtRevCycle.Text = "" _
                Or StrComp(Me.txtRevCycle.Text, getTranslationResource("All")) = 0 _
                Then
                    If Trim(ErrorText) = "" Then
                        ErrorText = strMessage & vbCrLf & Space(4) & _
                                    Left(label(5).Caption, Len(label(5).Caption) - 1)
                    Else
                        ErrorText = ErrorText & vbCrLf & Space(4) & _
                                    Left(label(5).Caption, Len(label(5).Caption) - 1)
                    End If
                    txtRevCycle.SetFocus
                End If
                'Text box -- set default text
                Me.txtVnId.Text = getTranslationResource("All")
                Me.txtAssortment.Text = getTranslationResource("All")
                
            ElseIf Me.optFilterOption(2).Value = True Then  'Specified Vendor/Assortment
                'Text boxes -- set default text
                Me.txtRevCycle.Text = getTranslationResource("All")
                'Validate VnID
                If txtVnId.Text = "" _
                Or StrComp(Me.txtVnId.Text, getTranslationResource("All")) = 0 _
                Then
                    If Trim(ErrorText) = "" Then
                        ErrorText = strMessage & vbCrLf & Space(4) & _
                                    Left(lblVnID_OrdGen.Caption, Len(lblVnID_OrdGen.Caption) - 1)
                    Else
                        ErrorText = ErrorText & vbCrLf & Space(4) & _
                                    Left(lblVnID_OrdGen.Caption, Len(lblVnID_OrdGen.Caption) - 1)
                    End If
                    txtVnId.SetFocus
                End If
                'Validate Assort
                If txtAssortment.Text = "" _
                Or StrComp(Me.txtAssortment.Text, getTranslationResource("All")) = 0 _
                Then
                    If Trim(ErrorText) = "" Then
                        ErrorText = strMessage & vbCrLf & Space(4) & _
                                    Left(lblAssort_OrdGen.Caption, Len(lblAssort_OrdGen.Caption) - 1)
                    Else
                        ErrorText = ErrorText & vbCrLf & Space(4) & _
                                    Left(lblAssort_OrdGen.Caption, Len(lblAssort_OrdGen.Caption) - 1)
                    End If
                    txtAssortment.SetFocus
                End If
            Else
                'Exception. At least one must be selected
                ErrorText = strMessage & vbCrLf & Space(4) & _
                                    Left(frOrdGen.Caption, Len(frOrdGen.Caption) - 1)
            End If
            
        Case 40                 'Seasonality Profile Update
            'Add validation here
                    
        Case 50                 'Velocity Code Assignment
            'Add validation here
                        
        Case 60                 'Order Policy Assignment
            'Not currently available
            
        'sri batch fcst start
        Case 70
             If Trim(dcFcstId.Text) = "" Then
             
             strMessage1 = getTranslationResource("MSGBOX03001")
                If StrComp(strMessage1, "MSGBOX03001") = 0 Then strMessage1 = "Forecast ID is required, the value is missing or not provided"
                ErrorText = strMessage & vbCrLf & Space(4) + strMessage1
              
               End If
               

             'Get Access Type for the current User
'            With AIM_ForecastUserAccess_Validate_Sp
'                .Parameters("@UserId").Value = txtUserId.Text
'                .Parameters("@FcstId").Value = dcFcstId.Text
'                .Parameters("@FcstType").Value = dcFcstType.Columns(0).Value
'                .Parameters("@AccessType").Value = 0
'
'                .Execute
'
'                If .Parameters("@Return_Value") = 0 Then
'                    AccessType = .Parameters("@AccessType")
'                Else
'                    AccessType = 0
'                End If
'            End With
'
'            If AccessType < 2 Then
'                strMessage1 = getTranslationResource("MSGBOX03003")
'                If StrComp(strMessage1, "MSGBOX03003") = 0 Then strMessage1 = "Insufficient user access rights to the Forecast. Operation aborted."
'                ErrorText = strMessage & vbCrLf & Space(4) + strMessage1
'            End If
            'sri batch fcst end
        Case 80     'Allocation
            'Add validation here
            
        Case 90 'Demand Plan forecast
            If Trim(dcFcstSetupID.Text) = "" Then
                strMessage1 = getTranslationResource("MSGBOX03001")
                If StrComp(strMessage1, "MSGBOX03001") = 0 Then strMessage1 = "Forecast ID is required, the value is missing or not provided"
                ErrorText = strMessage & vbCrLf & Space(4) + strMessage1
            End If
               
             'Get Access Type for the current User

    End Select
    
    'Return
    If Trim(ErrorText) <> "" Then
        RtnCode = MsgBox(ErrorText, vbOKOnly, Me.Caption)
        ValidateInput = False
    Else
        ValidateInput = True
    End If
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ValidateInput[" & Parameters(1) & "])"
End Function
