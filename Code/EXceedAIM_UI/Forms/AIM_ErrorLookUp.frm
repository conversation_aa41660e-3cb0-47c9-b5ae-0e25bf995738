VERSION 5.00
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{562E3E04-2C31-4ECE-83F4-4017EEE51D40}#8.0#0"; "todg8.ocx"
Begin VB.Form AIM_ErrorLookup 
   BorderStyle     =   4  'Fixed ToolWindow
   Caption         =   "SSA DR Error LookUp"
   ClientHeight    =   6480
   ClientLeft      =   750
   ClientTop       =   1020
   ClientWidth     =   12255
   ForeColor       =   &H00400040&
   Icon            =   "AIM_ErrorLookUp.frx":0000
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   6480
   ScaleMode       =   0  'User
   ScaleWidth      =   12255
   ShowInTaskbar   =   0   'False
   Begin TrueOleDBGrid80.TDBGrid dbgErrorTable 
      Height          =   3255
      Left            =   240
      TabIndex        =   0
      Top             =   2880
      Width           =   12015
      _ExtentX        =   21193
      _ExtentY        =   5741
      _LayoutType     =   0
      _RowHeight      =   -2147483647
      _WasPersistedAsPixels=   0
      Columns(0)._VlistStyle=   0
      Columns(0)._MaxComboItems=   5
      Columns(0).DataField=   ""
      Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
      Columns(1)._VlistStyle=   0
      Columns(1)._MaxComboItems=   5
      Columns(1).DataField=   ""
      Columns(1)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
      Columns.Count   =   2
      Splits(0)._UserFlags=   0
      Splits(0).RecordSelectorWidth=   688
      Splits(0)._SavedRecordSelectors=   -1  'True
      Splits(0).AllowColMove=   -1  'True
      Splits(0).DividerColor=   14215660
      Splits(0).SpringMode=   0   'False
      Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
      Splits(0)._ColumnProps(0)=   "Columns.Count=2"
      Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
      Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
      Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
      Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
      Splits(0)._ColumnProps(5)=   "Column(1).Width=2725"
      Splits(0)._ColumnProps(6)=   "Column(1).DividerColor=0"
      Splits(0)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
      Splits(0)._ColumnProps(8)=   "Column(1).Order=2"
      Splits.Count    =   1
      PrintInfos(0)._StateFlags=   0
      PrintInfos(0).Name=   "piInternal 0"
      PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=MS Sans Serif"
      PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=MS Sans Serif"
      PrintInfos(0).PageHeaderHeight=   0
      PrintInfos(0).PageFooterHeight=   0
      PrintInfos.Count=   1
      AllowAddNew     =   -1  'True
      DefColWidth     =   0
      HeadLines       =   1
      FootLines       =   1
      RowDividerStyle =   3
      Caption         =   "Total Error Messages filtered:"
      MultipleLines   =   0
      CellTips        =   2
      CellTipsWidth   =   0
      CellTipsDelay   =   100
      DataView        =   2
      GroupByCaption  =   ""
      DeadAreaBackColor=   -**********
      ScrollTrack     =   -1  'True
      ScrollTips      =   -1  'True
      RowDividerColor =   15919334
      RowSubDividerColor=   14215660
      DirectionAfterEnter=   1
      DirectionAfterTab=   1
      MaxRows         =   250000
      ViewColumnCaptionWidth=   0
      ViewColumnWidth =   0
      _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
      _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
      _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
      _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
      _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=0,.bold=0,.fontsize=825,.italic=0"
      _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(5)   =   ":id=0,.fontname=MS Sans Serif"
      _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33,.bgcolor=&H8000000F&,.bold=0,.fontsize=825"
      _StyleDefs(7)   =   ":id=1,.italic=0,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(8)   =   ":id=1,.fontname=MS Sans Serif"
      _StyleDefs(9)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
      _StyleDefs(10)  =   "HeadingStyle:id=2,.parent=1,.namedParent=34,.bgcolor=&H80000016&"
      _StyleDefs(11)  =   "FooterStyle:id=3,.parent=1"
      _StyleDefs(12)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
      _StyleDefs(13)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
      _StyleDefs(14)  =   "EditorStyle:id=7,.parent=1"
      _StyleDefs(15)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
      _StyleDefs(16)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
      _StyleDefs(17)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
      _StyleDefs(18)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
      _StyleDefs(19)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
      _StyleDefs(20)  =   "Splits(0).Style:id=13,.parent=1,.bgcolor=&HFFFFFF&"
      _StyleDefs(21)  =   "Splits(0).CaptionStyle:id=22,.parent=4"
      _StyleDefs(22)  =   "Splits(0).HeadingStyle:id=14,.parent=2"
      _StyleDefs(23)  =   "Splits(0).FooterStyle:id=15,.parent=3"
      _StyleDefs(24)  =   "Splits(0).InactiveStyle:id=16,.parent=5"
      _StyleDefs(25)  =   "Splits(0).SelectedStyle:id=18,.parent=6"
      _StyleDefs(26)  =   "Splits(0).EditorStyle:id=17,.parent=7"
      _StyleDefs(27)  =   "Splits(0).HighlightRowStyle:id=19,.parent=8"
      _StyleDefs(28)  =   "Splits(0).EvenRowStyle:id=20,.parent=9"
      _StyleDefs(29)  =   "Splits(0).OddRowStyle:id=21,.parent=10"
      _StyleDefs(30)  =   "Splits(0).RecordSelectorStyle:id=23,.parent=11"
      _StyleDefs(31)  =   "Splits(0).FilterBarStyle:id=24,.parent=12"
      _StyleDefs(32)  =   "Splits(0).Columns(0).Style:id=28,.parent=13"
      _StyleDefs(33)  =   "Splits(0).Columns(0).HeadingStyle:id=25,.parent=14"
      _StyleDefs(34)  =   "Splits(0).Columns(0).FooterStyle:id=26,.parent=15"
      _StyleDefs(35)  =   "Splits(0).Columns(0).EditorStyle:id=27,.parent=17"
      _StyleDefs(36)  =   "Splits(0).Columns(1).Style:id=32,.parent=13"
      _StyleDefs(37)  =   "Splits(0).Columns(1).HeadingStyle:id=29,.parent=14"
      _StyleDefs(38)  =   "Splits(0).Columns(1).FooterStyle:id=30,.parent=15"
      _StyleDefs(39)  =   "Splits(0).Columns(1).EditorStyle:id=31,.parent=17"
      _StyleDefs(40)  =   "Named:id=33:Normal"
      _StyleDefs(41)  =   ":id=33,.parent=0,.borderColor=&*********&"
      _StyleDefs(42)  =   "Named:id=34:Heading"
      _StyleDefs(43)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
      _StyleDefs(44)  =   ":id=34,.wraptext=-1"
      _StyleDefs(45)  =   "Named:id=35:Footing"
      _StyleDefs(46)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
      _StyleDefs(47)  =   "Named:id=36:Selected"
      _StyleDefs(48)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&,.wraptext=-1"
      _StyleDefs(49)  =   ":id=36,.bgpicMode=2,.appearance=1,.borderColor=&H80000000&"
      _StyleDefs(50)  =   "Named:id=37:Caption"
      _StyleDefs(51)  =   ":id=37,.parent=34,.alignment=2"
      _StyleDefs(52)  =   "Named:id=38:HighlightRow"
      _StyleDefs(53)  =   ":id=38,.parent=33,.alignment=0,.bgcolor=&*********&,.fgcolor=&*********&"
      _StyleDefs(54)  =   ":id=38,.appearance=0,.borderColor=&H0&,.bold=-1,.fontsize=825,.italic=0"
      _StyleDefs(55)  =   ":id=38,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(56)  =   ":id=38,.fontname=Tahoma"
      _StyleDefs(57)  =   "Named:id=39:EvenRow"
      _StyleDefs(58)  =   ":id=39,.parent=33,.bgcolor=&H80000003&"
      _StyleDefs(59)  =   "Named:id=40:OddRow"
      _StyleDefs(60)  =   ":id=40,.parent=33,.borderColor=&HFDE3D5&"
      _StyleDefs(61)  =   "Named:id=41:RecordSelector"
      _StyleDefs(62)  =   ":id=41,.parent=34"
      _StyleDefs(63)  =   "Named:id=42:FilterBar"
      _StyleDefs(64)  =   ":id=42,.parent=33,.appearance=4,.borderColor=&H80000000&"
   End
   Begin VB.TextBox txtErrorType 
      BackColor       =   &H00F8EEEF&
      Height          =   375
      Left            =   9480
      Locked          =   -1  'True
      TabIndex        =   16
      TabStop         =   0   'False
      Top             =   240
      Width           =   2655
   End
   Begin VB.TextBox txtErrorEvent 
      BackColor       =   &H00F8EEEF&
      Height          =   375
      Left            =   1560
      Locked          =   -1  'True
      TabIndex        =   13
      TabStop         =   0   'False
      Top             =   1200
      Width           =   3135
   End
   Begin VB.TextBox txtErrorSource 
      BackColor       =   &H00F8EEEF&
      Height          =   375
      Left            =   1560
      Locked          =   -1  'True
      TabIndex        =   11
      TabStop         =   0   'False
      Top             =   720
      Width           =   3135
   End
   Begin VB.TextBox txtDateTime 
      BackColor       =   &H00F8EEEF&
      Height          =   375
      Left            =   1560
      Locked          =   -1  'True
      TabIndex        =   9
      TabStop         =   0   'False
      Top             =   1680
      Width           =   1815
   End
   Begin VB.TextBox txtErrorDesc 
      BackColor       =   &H00F8EEEF&
      Height          =   1935
      Left            =   6120
      Locked          =   -1  'True
      MultiLine       =   -1  'True
      ScrollBars      =   3  'Both
      TabIndex        =   7
      TabStop         =   0   'False
      Top             =   720
      Width           =   6135
   End
   Begin VB.TextBox txtErrorNumber 
      BackColor       =   &H00F8EEEF&
      Enabled         =   0   'False
      Height          =   375
      Left            =   1560
      Locked          =   -1  'True
      TabIndex        =   5
      TabStop         =   0   'False
      Top             =   2160
      Width           =   1815
   End
   Begin VB.TextBox txtUserName 
      BackColor       =   &H00F8EEEF&
      Height          =   375
      Left            =   6120
      Locked          =   -1  'True
      TabIndex        =   3
      TabStop         =   0   'False
      Top             =   240
      Width           =   1695
   End
   Begin VB.TextBox txtComputerName 
      BackColor       =   &H00F8EEEF&
      Height          =   375
      Left            =   1560
      Locked          =   -1  'True
      TabIndex        =   2
      TabStop         =   0   'False
      Top             =   240
      Width           =   3135
   End
   Begin ActiveToolBars.SSActiveToolBars tbAIMErrorLog 
      Left            =   5280
      Top             =   1080
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   9
      Tools           =   "AIM_ErrorLookUp.frx":000C
      ToolBars        =   "AIM_ErrorLookUp.frx":72BE
   End
   Begin VB.Label lblUserName 
      Caption         =   "Error Type"
      Height          =   255
      Index           =   0
      Left            =   8520
      TabIndex        =   15
      Top             =   360
      Width           =   855
   End
   Begin VB.Label lblErrorEvent 
      Caption         =   "Error Event"
      Height          =   255
      Left            =   240
      TabIndex        =   14
      Top             =   1200
      Width           =   855
   End
   Begin VB.Label lblErrorSource 
      Caption         =   "Error Source"
      Height          =   255
      Left            =   240
      TabIndex        =   12
      Top             =   720
      Width           =   975
   End
   Begin VB.Label lblErrorDateTime 
      Caption         =   "Date && Time"
      Height          =   255
      Left            =   240
      TabIndex        =   10
      Top             =   1680
      Width           =   975
   End
   Begin VB.Label lblErrorDesc 
      Caption         =   "Description"
      Height          =   255
      Left            =   5160
      TabIndex        =   8
      Top             =   720
      Width           =   855
   End
   Begin VB.Label lblErrorNumber 
      Caption         =   "Error Number"
      Height          =   255
      Left            =   240
      TabIndex        =   6
      Top             =   2160
      Width           =   975
   End
   Begin VB.Label lblUserName 
      Caption         =   "User ID"
      Height          =   255
      Index           =   1
      Left            =   5400
      TabIndex        =   4
      Top             =   360
      Width           =   615
   End
   Begin VB.Label lblComputerName 
      Caption         =   "Computer Name"
      Height          =   255
      Left            =   240
      TabIndex        =   1
      Top             =   240
      Width           =   1215
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&mnuEdit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy selected message"
         HelpContextID   =   1
      End
   End
End
Attribute VB_Name = "AIM_ErrorLookup"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'sgajjela
Option Explicit
Dim AimErrorInfoRecordSet As ADODB.Recordset
Dim AimUsersRecSet As ADODB.Recordset
Dim SQLQuery As String

Dim ReturnCode As Integer
Dim BookMarkIndex As Integer
Public DisplayMode As String
Public RowNumber As Long
Public BookMarkItem As Variant
Public TotalNumberOfRecords As Long



'-----Record Navigation Indicators-----
Private Const ID_FST_RECORD = 1
Private Const ID_NXT_RECORD = 2
Private Const ID_PRV_RECORD = 3
Private Const ID_LST_RECORD = 4
Const OrderByForDateTime = "ORDER BY [DateAndTime] DESC"

Private Sub ETDbGrid_Click()
On Error GoTo ErrorHandler
'-----Displays selected error message on the gird in independent controls.-----
DisplaySelectedRow
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::ETDbGrid_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ETDbGrid_RowColChange(LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler
'-----Displays selected error message on the gird in independent controls.-----
DisplaySelectedRow
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::ETDbGrid_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub ETDbGrid_SelChange(Cancel As Integer)
On Error GoTo ErrorHandler
'-----Displays selected error message on the gird in independent controls-----
DisplaySelectedRow
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::ETDbGrid_SelChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdButton_Click()
On Error GoTo ErrorHandler
'-----Display selected error message on the grid in independent controls -----
DisplaySelectedRow
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::cmdButton_Click", Now, gDRGeneralError, True, Err
End Sub

Private Function RefreshMessages()
On Error GoTo ErrorHandler

Dim WhereClause As String
Dim UserIDFilter As String
Dim LogTypeFilter As String



WhereClause = "WHERE [DATEANDTIME] >= '" & AIM_ErrorFilter.BeginDate & "'" & " AND [DATEANDTIME] <=  '" & AIM_ErrorFilter.EndDate & "'"

'Close it.
AimErrorInfoRecordSet.Close

LogTypeFilter = ""
UserIDFilter = ""

If ((AIM_ErrorFilter.UserID = "All Users") And (AIM_ErrorFilter.ErrorType = "All")) Then
    GetRecordCount ("")
ElseIf ((AIM_ErrorFilter.UserID = "All Users") And (AIM_ErrorFilter.ErrorType <> "All")) Then
    LogTypeFilter = " AND [LOGTYPE] =" & "'" & AIM_ErrorFilter.ErrorType & "'"
    GetRecordCount (WhereClause)
ElseIf ((AIM_ErrorFilter.UserID <> "All Users") And (AIM_ErrorFilter.ErrorType = "All")) Then
    WhereClause = WhereClause & "AND [USERID] = " & "'" & AIM_ErrorFilter.UserID & "'"
    GetRecordCount (WhereClause)
ElseIf ((AIM_ErrorFilter.UserID <> "All Users") And (AIM_ErrorFilter.ErrorType <> "All")) Then
    UserIDFilter = " AND [USERID] = " & "'" & AIM_ErrorFilter.UserID & "'"
    LogTypeFilter = " AND [LOGTYPE] =" & "'" & AIM_ErrorFilter.ErrorType & "'"
    GetRecordCount (WhereClause & LogTypeFilter & UserIDFilter)
End If

AimErrorInfoRecordSet.MaxRecords = AIM_ErrorFilter.MaxNumberOfRowsFortheUser


'Open the record set
AimErrorInfoRecordSet.Open SQLQuery & WhereClause & LogTypeFilter & UserIDFilter & OrderByForDateTime
           

'Reassign and rebind.
dbgErrorTable.DataSource = AimErrorInfoRecordSet

'Rebind the grid to reflect the new filter results on the screen
dbgErrorTable.ReBind

'Display selected error message in detailed error message section.
DisplaySelectedRow

'Display total number of rows filtered
If (TotalNumberOfRecords > AIM_ErrorFilter.MaxNumberOfRowsFortheUser) Then
    dbgErrorTable.Caption = "Maximum Error Messages Filtered: (" + CStr((TotalNumberOfRecords - AimErrorInfoRecordSet.MaxRecords)) + ")"
Else
    dbgErrorTable.Caption = "Maximum Error Messages Filtered: (" + CStr(0) + ")"
End If


Exit Function

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::RefreshMessages", Now, gDRGeneralError, True, Err
End Function
Private Sub dbgErrorTable_Change()
DisplaySelectedRow
End Sub

Private Sub dbgErrorTable_ClassicRead(Bookmark As Variant, ByVal Col As Integer, Value As Variant)
Dim FavRecord As BookmarkEnum
FavRecord = Bookmark
End Sub

Private Sub dbgErrorTable_Click()
'DisplaySelectedRow
End Sub

Private Sub dbgErrorTable_DblClick()
DisplaySelectedRow
End Sub

Private Sub dbgErrorTable_FetchCellTips(ByVal SplitIndex As Integer, ByVal ColIndex As Integer, ByVal RowIndex As Long, CellTip As String, ByVal FullyDisplayed As Boolean, ByVal TipStyle As TrueOleDBGrid80.StyleDisp)
On Error GoTo ErrorHandler
'-----Display tooltips in readable format-----
TipStyle.WrapText = True
TipStyle.BorderAppearance = dbg3DRaised
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::dbgErrorTable_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dbgErrorTable_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler
'-----Display 'Copy Selected Message' menu item on right click of the grid-----
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    End Select
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::dbgErrorTable_MouseDown", Now, gDRGeneralError, True, Err
End Sub
Private Sub dbgErrorTable_SelChange(Cancel As Integer)
On Error GoTo ErrorHandler
DisplaySelectedRow
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::dbgErrorTable_SelChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
Dim RtnCode As Long


GetTranslatedCaptions Me

'-----Initially disable disable GoToFavMsg toolbar button-----
Me.tbAIMErrorLog.Tools("ID_GoToFavMsg").Enabled = False

'-----By default set max number of error messages-----
Me.dbgErrorTable.MaxRows = 200

'-----Request for connection to the database-----
RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True)
If RtnCode <> SUCCEED Then
    RtnCode = Write_Message("")
    Screen.MousePointer = vbNormal
    Exit Sub
End If

'-----Extract and display error messages from AIMERROR_INFO table-----
Set AimErrorInfoRecordSet = New ADODB.Recordset
Set AimErrorInfoRecordSet.ActiveConnection = Cn

'Set recordset properties
AimErrorInfoRecordSet.CursorLocation = adUseClient
AimErrorInfoRecordSet.CursorType = adOpenDynamic
AimErrorInfoRecordSet.LockType = adLockReadOnly
AimErrorInfoRecordSet.MaxRecords = 200


'Build SQL query
SQLQuery = "SELECT [ComputerName], [USERID], [ErrorNumber],  [ErrorSource], [ErrorDesc], [ErrorFromEvent], [DateAndTime] , [LogType] FROM  [AIMERROR_INFO] "


'-----Sense the mode based on the caption of the window-----
If DisplayMode = "JobLog" Then
    'SQLQuery = SQLQuery & " WHERE LogType <> 'DRGE'"
    AimErrorInfoRecordSet.Open SQLQuery & " WHERE LogType <> 'DRGE'" & OrderByForDateTime
    AIM_ErrorLookup.Caption = "SSA DR JobLog"
Else
    'Open recordset.
    AimErrorInfoRecordSet.Open SQLQuery & OrderByForDateTime
End If


'Bind to DBGrid
dbgErrorTable.DataSource = AimErrorInfoRecordSet

'-----Configure error message grid column names-----
dbgErrorTable.Columns(0).Caption = "Compter Name"
dbgErrorTable.Columns(0).Alignment = dbgLeft
dbgErrorTable.Columns(0).Width = dbgErrorTable.Columns(0).Width + 200

dbgErrorTable.Columns(1).Caption = "User ID"
dbgErrorTable.Columns(1).Alignment = dbgLeft

dbgErrorTable.Columns(2).Caption = "Error Number"
dbgErrorTable.Columns(2).Alignment = dbgLeft
dbgErrorTable.Columns(2).Width = dbgErrorTable.Columns(2).Width - 800

dbgErrorTable.Columns(3).Caption = "Source"
dbgErrorTable.Columns(3).Alignment = dbgLeft

dbgErrorTable.Columns(4).Caption = "Description"
dbgErrorTable.Columns(4).Alignment = dbgLeft

dbgErrorTable.Columns(5).Caption = "Event"
dbgErrorTable.Columns(5).Alignment = dbgLeft


dbgErrorTable.Columns(6).Caption = "DateTime"
dbgErrorTable.Columns(6).Alignment = dbgLeft
dbgErrorTable.Columns(6).Width = dbgErrorTable.Columns(6).Width + 400

dbgErrorTable.Columns(7).Caption = "Type"

'-----Initially display selected row in detailed section-----
DisplaySelectedRow

'-----Add current window to the opened window list
AddToWindowList Me.Caption

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::Form_Load", Now, gDRGeneralError, True, Err
End Sub
Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
'-----Perform clean up stuff-----
'Remove form from window list
AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
Unload Me
SQLConnection Cn, CONNECTION_CLOSE, False
Set Cn = Nothing
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::Form_Unload", Now, gDRGeneralError, True, Err
End Sub

Private Sub mnuEditOpt_Click()
'-----Copy selected error message to the windows clipboard-----
CopySelectedErrorMessageToClipBoard
End Sub

Private Sub tbAIMErrorLog_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
Dim RecordCount As Long
'---Detect menu item and perform related functionality-----
Select Case Tool.ID
        Case "ID_Close"      'Close request
            Unload Me
        Case "ID_Refresh"     'Refresh
              AIM_ErrorFilter.Show vbModal
              If Not gIsErrorFilterCreated Then
                Exit Sub
            Else
                'Set the max number of rows.
                Me.dbgErrorTable.MaxRows = AIM_ErrorFilter.MaxErrorMsgs
                Call RefreshMessages
                If (AimErrorInfoRecordSet.RecordCount = 0) Then
                    EnableToolBarButtons (False)
                Else
                    EnableToolBarButtons (True)
                End If
            End If
        Case "ID_Copy"      'Copy selected
            CopySelectedErrorMessageToClipBoard
        Case "ID_FirstMsg"                      'Navigate to first error message, if any
            NavigateToRecord ID_FST_RECORD
        Case "ID_NextMsg"                       'Navigate to next error message, if any
            NavigateToRecord ID_NXT_RECORD
        Case "ID_PrevMsg"                       'Navigate to previous error message, if any
            NavigateToRecord ID_PRV_RECORD
        Case "ID_LastMsg"                       'Navigate to last error message, if any
            NavigateToRecord ID_LST_RECORD
        Case "ID_FavMsg":                       'Navigate to book marked error message.
            If IsNull(Me.dbgErrorTable.Bookmark) = False Then
                BookMarkItem = Me.dbgErrorTable.Bookmark
                Me.tbAIMErrorLog.Tools("ID_GotoFavMsg").Enabled = True
            End If
        Case "ID_GoToFavMsg":
            Me.dbgErrorTable.Bookmark = BookMarkItem
            Call DisplaySelectedRow
End Select

Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::tbAIMErrorLog_ToolClick", Now, gDRGeneralError, True, Err
End Sub

'Gloabl Functions to this current form -sgajjela
Private Function DisplaySelectedRow()
On Error GoTo ErrorHandler
'Display selected row, if current filter returns any error messages.
If (dbgErrorTable.eof <> True) Then
    txtComputerName = dbgErrorTable.Columns(0).Value
    txtUserName = dbgErrorTable.Columns(1).Value
    txtErrorNumber = dbgErrorTable.Columns(2).Value
    txtErrorSource = dbgErrorTable.Columns(3).Value
    txtErrorDesc = dbgErrorTable.Columns(4).Value
    txtErrorEvent = dbgErrorTable.Columns(5)
    txtDateTime = dbgErrorTable.Columns(6).Value
    
    If (dbgErrorTable.Columns(7) = "DRGE") Then
        txtErrorType = "SSA DR General Error"
    ElseIf (dbgErrorTable.Columns(7) = "DRJU") Then
        txtErrorType = "SSA DR Job update(s)"
    ElseIf (dbgErrorTable.Columns(7) = "DRJE") Then
        txtErrorType = "SSA DR Job Error(s)"
    End If
Else
    txtComputerName = ""
    txtErrorDesc = ""
    txtDateTime = ""
    txtErrorNumber = ""
    txtErrorSource = ""
    txtUserName = ""
    txtErrorEvent = ""
    txtErrorType = ""
End If
Exit Function

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::DisplaySelectedRow", Now, gDRGeneralError, True, Err
End Function

Private Function CopySelectedErrorMessageToClipBoard()
On Error GoTo ErrorHandler

'-----Copy selected Error Message  to the Windows clipboard-----
If (dbgErrorTable.eof <> True) Then
    Clipboard.Clear
    Clipboard.SetText "SSA Global Technologies inc. " & vbNewLine & _
                      "Application" & vbTab & "=" & vbTab & "SSA DR" & vbNewLine & _
                      "Error Message" & vbTab & "=" & vbTab & txtErrorDesc & vbNewLine & _
                      "Error Number" & vbTab & "=" & vbTab & txtErrorNumber & vbNewLine & _
                      "Error Source" & vbTab & "=" & vbTab & txtErrorSource & vbNewLine & _
                      "Error Event" & vbTab & "=" & vbTab & txtErrorEvent & vbNewLine & _
                      "<eom>"
                      
Else
    Clipboard.Clear
End If
Exit Function

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::CopySelectedErrorMessageToClipBoard", Now, gDRGeneralError, True, Err
End Function

Private Function NavigateToRecord(NavigateTo As Integer)
On Error GoTo ErrorHandler
Dim ErrorMesage As String

ErrorMesage = "Indicator is alredy pointing to"

If (AimErrorInfoRecordSet.RecordCount <= 0) Then
Exit Function
End If

'-----Navigate to the reqired/specified record-----
Select Case NavigateTo
Case ID_FST_RECORD:
    AimErrorInfoRecordSet.MoveFirst
       
Case ID_NXT_RECORD:
    If (AimErrorInfoRecordSet.eof <> True) Then
        AimErrorInfoRecordSet.MoveNext
    Else
        AimErrorInfoRecordSet.MoveLast
    End If
      
Case ID_PRV_RECORD:
    If (AimErrorInfoRecordSet.BOF <> True) Then
        AimErrorInfoRecordSet.MovePrevious
    Else
        AimErrorInfoRecordSet.MoveFirst
    End If
    
Case ID_LST_RECORD:
    AimErrorInfoRecordSet.MoveLast
End Select

DisplaySelectedRow

Exit Function

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::NavigateToRecord", Now, gDRGeneralError, True, Err
End Function
Private Function EnableToolBarButtons(IsEnable As Boolean)
'Me.tbAIMErrorLog.Tools("ID_GotoFavMsg").Enabled = IsEnable
Me.tbAIMErrorLog.Tools("ID_FavMsg").Enabled = IsEnable
Me.tbAIMErrorLog.Tools("ID_FirstMsg").Enabled = IsEnable
Me.tbAIMErrorLog.Tools("ID_NextMsg").Enabled = IsEnable
Me.tbAIMErrorLog.Tools("ID_PrevMsg").Enabled = IsEnable
Me.tbAIMErrorLog.Tools("ID_LastMsg").Enabled = IsEnable
Me.tbAIMErrorLog.Tools("ID_Copy").Enabled = IsEnable
End Function

Private Function GetRecordCount(WhereClause As String)
On Error GoTo ErrorHandler

'-----Extract 'NumOffDaysBehindFromCurrentDate' From SysCtrl-----
Dim RecSetErrorInfo As ADODB.Recordset
Dim SQLQuery As String

        
SQLQuery = "SELECT COUNT(*) FROM [AIMERROR_INFO] "
Set RecSetErrorInfo = New ADODB.Recordset
With RecSetErrorInfo
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
End With
    
RecSetErrorInfo.Open SQLQuery + WhereClause, Cn
TotalNumberOfRecords = RecSetErrorInfo(0).Value
Exit Function

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorLookup::GetRecordCount", Now, gDRGeneralError, True, Err
End Function









