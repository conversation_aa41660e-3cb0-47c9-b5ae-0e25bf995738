VERSION 5.00
Begin VB.Form AIM_UsersPassword 
   Caption         =   "SSA DR Users Password"
   ClientHeight    =   2190
   ClientLeft      =   75
   ClientTop       =   345
   ClientWidth     =   5295
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   Moveable        =   0   'False
   ScaleHeight     =   2190
   ScaleWidth      =   5295
   Begin VB.TextBox txtReNewPassword 
      Height          =   340
      IMEMode         =   3  'DISABLE
      Left            =   2882
      PasswordChar    =   "*"
      TabIndex        =   2
      Top             =   1071
      Width           =   1564
   End
   Begin VB.TextBox txtNewPassword 
      Height          =   340
      IMEMode         =   3  'DISABLE
      Left            =   2882
      PasswordChar    =   "*"
      TabIndex        =   1
      Top             =   595
      Width           =   1564
   End
   Begin VB.TextBox txtOldPassword 
      Height          =   340
      IMEMode         =   3  'DISABLE
      Left            =   2882
      PasswordChar    =   "*"
      TabIndex        =   0
      Top             =   119
      Width           =   1564
   End
   Begin VB.CommandButton mnuCancel 
      Cancel          =   -1  'True
      Caption         =   "Cancel"
      Height          =   340
      Left            =   2703
      TabIndex        =   4
      Top             =   1665
      Width           =   1326
   End
   Begin VB.CommandButton mnuUpdate 
      Caption         =   "&Update"
      Default         =   -1  'True
      Height          =   340
      Left            =   1275
      TabIndex        =   3
      Top             =   1665
      Width           =   1326
   End
   Begin VB.Label Label3 
      Caption         =   "Re-enter new password"
      Height          =   225
      Left            =   855
      TabIndex        =   7
      Top             =   1129
      Width           =   2040
   End
   Begin VB.Label Label2 
      Caption         =   "New Password"
      Height          =   225
      Left            =   855
      TabIndex        =   6
      Top             =   653
      Width           =   1320
   End
   Begin VB.Label Label1 
      Caption         =   "Enter Old Password"
      Height          =   225
      Left            =   855
      TabIndex        =   5
      Top             =   177
      Width           =   1800
   End
End
Attribute VB_Name = "AIM_UsersPassword"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Me.Width = 5440
    Me.Height = 2805
    Me.Left = (AIM_Main.ScaleWidth - Me.ScaleWidth) / 2
    Me.Top = (AIM_Main.ScaleHeight - Me.ScaleHeight) / 2

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_UserPassword:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub mnuCancel_Click()
On Error GoTo ErrorHandler
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuCancel_Click)"
     f_HandleErr , , , "AIM_UserPassword:::mnuCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub mnuUpdate_Click()
On Error GoTo ErrorHandler

    Dim Cn As ADODB.Connection
    Dim tempCmd As ADODB.Command
    Dim strMessage As String
    Dim RtnCode As Integer
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDesc As String
    Dim strSQL As String
        
    Set Cn = New ADODB.Connection
    Set tempCmd = New ADODB.Command
    With tempCmd
        .CommandType = adCmdText
    End With
    
    If Trim$(Me.txtNewPassword.Text) = "" Then
        strMessage = getTranslationResource("MSGBOX06010")
        If StrComp(strMessage, "MSGBOX06010") = 0 Then strMessage = "A blank password is not acceptable."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        Me.txtNewPassword.SetFocus
        Exit Sub
    End If
    
    If (StrComp(Trim$(Me.txtNewPassword.Text), Trim$(txtReNewPassword), vbBinaryCompare)) <> 0 Then
        strMessage = getTranslationResource("MSGBOX06011")
        If StrComp(strMessage, "MSGBOX06011") = 0 Then strMessage = "The confirmation password is not correct."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        Me.txtNewPassword.SelLength = Len(Me.txtNewPassword.Text)
        Me.txtNewPassword.SetFocus
        Exit Sub
    End If
    
    strMessage = getTranslationResource("MSGBOX06012")
    If StrComp(strMessage, "MSGBOX06012") = 0 Then strMessage = "Do you wish to proceed further?"
    RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
    If RtnCode = vbYes Then
        Cn.Provider = "SQLOleDb"
        Cn.ConnectionTimeout = 10
        Cn.CommandTimeout = 300            'Five minutes
        Cn.Properties("Data Source").Value = gServer
        Cn.Properties("Initial Catalog").Value = gDataBase
        If gWinNTAuth = "Y" Then
            Cn.Properties("Integrated Security").Value = "SSPI"
        Else
            Cn.Properties("User ID").Value = gUserID
            Cn.Properties("Password").Value = gPassWord
        End If
        Cn.Open
        strSQL = "EXEC sp_password N'" & Replace(Trim$(Me.txtOldPassword.Text), "'", "''") & _
            "', N'" & Replace(Trim$(Me.txtNewPassword.Text), "'", "''") & _
            "', N'" & Replace(Trim$(gUserID), "'", "''") & "'"
        With tempCmd
            If .ActiveConnection Is Nothing Then Set .ActiveConnection = Cn
            .CommandText = strSQL
            .Execute
        End With

        strMessage = getTranslationResource("MSGBOX06013")
        If StrComp(strMessage, "MSGBOX06013") = 0 Then strMessage = "The new password has been saved successfully."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        Unload Me
    Else
        Exit Sub
    End If
        
    Set Cn = Nothing
    If Not (tempCmd Is Nothing) Then Set tempCmd.ActiveConnection = Nothing
    Set tempCmd = Nothing
        
Exit Sub
ErrorHandler:
    Set Cn = Nothing
    If Not (tempCmd Is Nothing) Then Set tempCmd.ActiveConnection = Nothing
    Set tempCmd = Nothing
    'f_HandleErr Me.Caption & "(mnuUpdate_Click)"
     f_HandleErr , , , "AIM_UserPassword:::mnuUpdate_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtNewPassword_GotFocus()
On Error GoTo ErrorHandler
    
    txtNewPassword.SelStart = 0
    txtNewPassword.SelLength = Len(Me.txtNewPassword.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtNewPassword_GotFocus)"
     f_HandleErr , , , "AIM_UserPassword:::txtNewPassword_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtOldPassword_GotFocus()
On Error GoTo ErrorHandler
    
    txtOldPassword.SelStart = 0
    txtOldPassword.SelLength = Len(txtOldPassword)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtOldPassword_GotFocus)"
     f_HandleErr , , , "AIM_UserPassword:::txtOldPassword_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtReNewPassword_GotFocus()
On Error GoTo ErrorHandler
    
    txtReNewPassword.SelStart = 0
    txtReNewPassword.SelLength = Len(txtReNewPassword)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtReNewPassword_GotFocus)"
     f_HandleErr , , , "AIM_UserPassword:::txtReNewPassword_GotFocus", Now, gDRGeneralError, True, Err
End Sub
