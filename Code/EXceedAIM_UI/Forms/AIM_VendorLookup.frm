VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_VendorLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Vendor/Assortment Lookup"
   ClientHeight    =   5940
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8475
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_VendorLookup.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5940
   ScaleWidth      =   8475
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   7038
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3000
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return Selected Item"
      Height          =   345
      Left            =   4440
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   5400
      Width           =   2445
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   75
      Style           =   1  'Graphical
      TabIndex        =   8
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   1560
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   5400
      Width           =   1365
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgVendors 
      Height          =   3315
      Left            =   78
      TabIndex        =   9
      Top             =   1905
      Width           =   8325
      _Version        =   196617
      DataMode        =   1
      Cols            =   4
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      TabNavigation   =   1
      _ExtentX        =   14684
      _ExtentY        =   5847
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1650
      Left            =   78
      TabIndex        =   10
      Top             =   120
      Width           =   8325
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   340
         Left            =   108
         TabIndex        =   0
         Top             =   735
         Width           =   1700
         _Version        =   65536
         _ExtentX        =   2999
         _ExtentY        =   600
         Caption         =   "AIM_VendorLookup.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorLookup.frx":0376
         Key             =   "AIM_VendorLookup.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   340
         Left            =   1830
         TabIndex        =   1
         Top             =   735
         Width           =   1695
         _Version        =   65536
         _ExtentX        =   2990
         _ExtentY        =   600
         Caption         =   "AIM_VendorLookup.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorLookup.frx":0444
         Key             =   "AIM_VendorLookup.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
         Height          =   345
         Left            =   120
         TabIndex        =   2
         Top             =   1200
         Width           =   3135
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   600
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7305
         TabIndex        =   3
         Top             =   1200
         Width           =   870
         _Version        =   65536
         _ExtentX        =   1535
         _ExtentY        =   600
         Calculator      =   "AIM_VendorLookup.frx":04A6
         Caption         =   "AIM_VendorLookup.frx":04C6
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorLookup.frx":0532
         Keys            =   "AIM_VendorLookup.frx":0550
         Spin            =   "AIM_VendorLookup.frx":059A
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   200
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin VB.Label Label 
         Caption         =   "Maximum Rows"
         Height          =   300
         Index           =   0
         Left            =   5295
         TabIndex        =   13
         Top             =   1245
         Width           =   1935
      End
      Begin VB.Label Label 
         Caption         =   "Assortment"
         Height          =   340
         Index           =   2
         Left            =   1830
         TabIndex        =   12
         Top             =   340
         Width           =   1695
      End
      Begin VB.Label Label 
         Caption         =   "Vendor ID"
         Height          =   340
         Index           =   1
         Left            =   108
         TabIndex        =   11
         Top             =   340
         Width           =   1700
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_VendorLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As New ADODB.Connection
Dim rsVendors As ADODB.Recordset

Public AssortKey As String
Public CancelFlag As Boolean
Public VnIdKey As String

Private AssortKey_Criteria As String
Private VnIdKey_Criteria As String

Private Function BuildVendorLookupQuery() As String
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    
    'Initialize SQL Statment
    SqlStmt = "SELECT VnID, Assort, VName, Dft_ByID, RevCycle " & _
                " FROM AIMVendors "
    WhrStmt = " WHERE "
    
    'Build the Where Statment
    If Trim(Me.txtVnID.Text) <> "" Then
        WhrStmt = Trim(WhrStmt) + " VnID " + Me.dcComparison.Value + " " + VnIdKey_Criteria + " "
    End If
        
    If Trim(Me.txtAssort.Text) <> "" Then
        If Trim(WhrStmt) = "WHERE" Then
            WhrStmt = Trim(WhrStmt) + " Assort "
        Else
            WhrStmt = Trim(WhrStmt) + " AND Assort "
        End If
        WhrStmt = WhrStmt + Me.dcComparison.Value + " " + AssortKey_Criteria & " "
    End If
    
    SqlStmt = SqlStmt & WhrStmt & "ORDER BY VnID, Assort"
    
    BuildVendorLookupQuery = SqlStmt

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BuildVendorLookupQuery)", Err.Description
     f_HandleErr , , , "AIM_VendorLookUp::BuildVendorLookupQuery", Now, gDRGeneralError, True, Err
End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim strText As String
    Dim strMessage As String
    
    Me.MousePointer = vbHourglass
    Write_Message ""
    
    'Make sure the user selected at least one criterion
    If Trim(Me.txtVnID.Text) + Trim(Me.txtAssort.Text) = "" Then
        strText = getTranslationResource(AIM_VendorLookUp.Caption)
        strMessage = getTranslationResource("MSGBOX06200")
        If StrComp(strMessage, "MSGBOX06200") = 0 Then strMessage = "Please select one or more Lookup Criteria "
        MsgBox strMessage, vbExclamation, strText
        Me.txtVnID.SetFocus
        Me.MousePointer = vbNormal
        Exit Sub

    End If
    
    'Force validation
    txtVnId_Validate False
    txtAssort_Validate False
    If StrComp(VnIdKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(AssortKey_Criteria, "Error", vbTextCompare) = 0 _
    Then
        'Do NOT Proceed
    Else
        'Create the query
        SqlStmt = BuildVendorLookupQuery()
        
        'Recreate the result set
        If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
        rsVendors.MaxRecords = Me.txtMaxRows.Value
        rsVendors.Open SqlStmt
        
        If f_IsRecordsetOpenAndPopulated(rsVendors) Then
            'Refresh the grid
            Me.dgVendors.ReBind
        Else
            If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
            Me.dgVendors.Reset
        End If
        
    End If
        
    'Return the screen values
    Me.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_VendorLookUp::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_VendorLookUp::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    Me.txtVnID.Text = ""
    Me.txtAssort.Text = ""
    
    VnIdKey_Criteria = ""
    AssortKey_Criteria = ""
    
    Me.txtMaxRows.Value = 200
    
    If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
    Me.dgVendors.Reset
    
    Me.txtVnID.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_VendorLookUp::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgVendors.Rows > 0 Then
        
        Screen.MousePointer = vbHourglass
        Me.dgVendors.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_VendorLookUp::cmdPrint_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = False
    Me.VnIdKey = Me.dgVendors.Columns("vnid").Value
    Me.AssortKey = Me.dgVendors.Columns("assort").Value
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_VendorLookUp::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_CloseUp()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case Me.dcComparison.Columns(0).Text
    
    Case "LIKE"
        Select Case Me.dcComparison.Columns(1).Text
            Case getTranslationResource("Begins With")
                strMessage = getTranslationResource("STATMSG06200")
                If StrComp(strMessage, "STATMSG06200") = 0 Then strMessage = "Enter either part or all of the beginning of the desired value. "
                Write_Message strMessage
            Case getTranslationResource("Contains")
                strMessage = getTranslationResource("STATMSG06201")
                If StrComp(strMessage, "STATMSG06201") = 0 Then strMessage = "Enter a string expression which is contained within the desired value. "
                Write_Message strMessage
        End Select
    
    Case "BETWEEN"
        strMessage = getTranslationResource("STATMSG06202")
        If StrComp(strMessage, "STATMSG06202") = 0 Then strMessage = "Enter two values between which the desired value should fall, separated by a comma. "
        Write_Message strMessage
    
    Case "IN"
        strMessage = getTranslationResource("STATMSG06203")
        If StrComp(strMessage, "STATMSG06203") = 0 Then strMessage = "Enter a list of values in which the desired value should fall, separated by commas. "
        Write_Message strMessage
    
    Case Else
        strMessage = getTranslationResource("STATMSG06204")
        If StrComp(strMessage, "STATMSG06204") = 0 Then strMessage = "Enter a value for comparison. "
        Write_Message strMessage
    
    End Select
    
    'Reset the comparison values
    txtVnId_Validate False
    txtAssort_Validate False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_CloseUp)"
    f_HandleErr , , , "AIM_VendorLookUp::dcComparison_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Build Columns
    Me.dcComparison.Columns(0).Caption = getTranslationResource("Comparison")
    Me.dcComparison.Columns(0).FieldLen = 1
    Me.dcComparison.Columns(0).Width = 1000
    
    Me.dcComparison.Columns(1).Caption = getTranslationResource("Description")
    Me.dcComparison.Columns(1).FieldLen = 24
    Me.dcComparison.Columns(1).Width = 2000
    
    'Load Values
    Me.dcComparison.AddItem "=" + vbTab + getTranslationResource("Equal To")
    Me.dcComparison.AddItem ">" + vbTab + getTranslationResource("Greater Than")
    Me.dcComparison.AddItem ">=" + vbTab + getTranslationResource("Greater Than / Equal To")
    Me.dcComparison.AddItem "<" + vbTab + getTranslationResource("Less Than")
    Me.dcComparison.AddItem "<=" + vbTab + getTranslationResource("Less Than / Equal To")
    Me.dcComparison.AddItem "<>" + vbTab + getTranslationResource("Not Equal To")
    Me.dcComparison.AddItem "LIKE" + vbTab + getTranslationResource("Begins With")
    Me.dcComparison.AddItem "LIKE" + vbTab + getTranslationResource("Contains")
    Me.dcComparison.AddItem "BETWEEN" + vbTab + getTranslationResource("Is Between These Values")
    Me.dcComparison.AddItem "IN" + vbTab + getTranslationResource("Is in this List of Values")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcComparison, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcComparison.Columns.Count - 1
'        dcComparison.Columns(IndexCounter).HasHeadBackColor = True
'        dcComparison.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_InitColumnProps)"
     f_HandleErr , , , "AIM_VendorLookUp::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendors_DblClick()
On Error GoTo ErrorHandler

    'Return selected item
    cmdReturn_Click

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_DblClick)"
     f_HandleErr , , , "AIM_VendorLookUp::dgVendors_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendors_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Item Data
    Me.dgVendors.Columns(0).Name = "vnid"
    Me.dgVendors.Columns(0).Caption = getTranslationResource("Vendor ID")
    Me.dgVendors.Columns(0).Width = 720
    Me.dgVendors.Columns(0).Locked = True
    
    Me.dgVendors.Columns(1).Name = "assort"
    Me.dgVendors.Columns(1).Caption = getTranslationResource("Assortment")
    Me.dgVendors.Columns(1).Width = 720
    Me.dgVendors.Columns(1).Locked = True
    
    Me.dgVendors.Columns(2).Name = "vndesc"
    Me.dgVendors.Columns(2).Caption = getTranslationResource("Description")
    Me.dgVendors.Columns(2).Width = 2880
    Me.dgVendors.Columns(2).Locked = True
    
    Me.dgVendors.Columns(3).Name = "byid"
    Me.dgVendors.Columns(3).Caption = getTranslationResource("Buyer ID")
    Me.dgVendors.Columns(3).Width = 1440
    Me.dgVendors.Columns(3).Locked = True
    
    Me.dgVendors.Columns(4).Name = "revcycle"
    Me.dgVendors.Columns(4).Caption = getTranslationResource("Review Cycle")
    Me.dgVendors.Columns(4).Width = 1440
    Me.dgVendors.Columns(4).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgVendors, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgVendors.Columns.Count - 1
'        dgVendors.Columns(IndexCounter).HasHeadBackColor = True
'        dgVendors.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgVendors.Columns(IndexCounter).Locked = False Then dgVendors.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_InitColumnProps)"
     f_HandleErr , , , "AIM_VendorLookUp::dgVendors_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendors_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_MouseDown)"
     f_HandleErr , , , "AIM_VendorLookUp::dgVendors_MouseDown", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgVendors_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG06200")
    If StrComp(strMessage, "RPTMSG06200") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG06201")
    If StrComp(strMessage1, "RPTMSG06201") = 0 Then strMessage = "Vendor Lookup List"
    strMessage2 = getTranslationResource("RPTMSG06202")
    If StrComp(strMessage2, "RPTMSG06202") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + strMessage1 _
                + vbTab + strMessage2 + " <page number>"
                
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_PrintInitialize)"
     f_HandleErr , , , "AIM_VendorLookUp::dgVendors_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendors_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsVendors) Then Exit Sub
    
    If IsNull(StartLocation) Then
        
        If ReadPriorRows Then
            rsVendors.MoveLast
        Else
            rsVendors.MoveFirst
        End If
    
    Else
    
        rsVendors.Bookmark = StartLocation
        If ReadPriorRows Then
            rsVendors.MovePrevious
        Else
            rsVendors.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        
        If rsVendors.BOF Or rsVendors.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsVendors("vnid").Value
        RowBuf.Value(i, 1) = rsVendors("assort").Value
        RowBuf.Value(i, 2) = rsVendors("vname").Value
        RowBuf.Value(i, 3) = rsVendors("dft_byid").Value
        RowBuf.Value(i, 4) = rsVendors("revcycle").Value

        RowBuf.Bookmark(i) = rsVendors.Bookmark
    
        If ReadPriorRows Then
            rsVendors.MovePrevious
        Else
            rsVendors.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_UnboundReadData)"
     f_HandleErr , , , "AIM_VendorLookUp::dgVendors_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Set the default value for the Cancel Flag
    CancelFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_VendorLookUp::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler


    Dim SqlStmt As String
    
    Screen.MousePointer = vbHourglass
    
    'Initialize fields
    Me.txtVnID.Text = Me.VnIdKey
    Me.txtAssort.Text = Me.AssortKey
    
    VnIdKey_Criteria = BldCompVal(Me.VnIdKey, Me.dcComparison.Value, Me.dcComparison.Text)
    AssortKey_Criteria = BldCompVal(Me.AssortKey, Me.dcComparison.Value, Me.dcComparison.Text)
    
    Me.txtMaxRows.Value = 200
    
    Me.dcComparison.Text = getTranslationResource("Greater Than / Equal To")
    
    'Build initial record set
    Set rsVendors = New ADODB.Recordset
    rsVendors.CursorLocation = adUseClient
    rsVendors.CursorType = adOpenStatic
    rsVendors.LockType = adLockReadOnly
    Set rsVendors.ActiveConnection = Cn
    
    SqlStmt = "SELECT VnID, Assort, VName, Dft_ByID, RevCycle " & _
                " FROM AIMVendors " & _
                " WHERE VnID = 'xxx' " & _
                " AND Assort = 'xxx' "
    rsVendors.Open SqlStmt
    
    '************************************************************
    'Mar 01 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtMaxRows.Spin.Visible = 1
    '************************************************************
    
    'Bind the grid
    Me.dgVendors.ReBind

    GetTranslatedCaptions Me
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_VendorLookUp::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
    Set rsVendors = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_VendorLookUp::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function BldCompVal(CompVals As String, CompType As String, _
    CompDetail As String)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim ParameterCount As Integer
    Dim newtext As String
    Dim RtnStr As String
    Dim StrArray(0 To 10) As String
    Dim strText As String
    Dim strMessage As String
    
    Select Case CompType
    Case "LIKE"
        Select Case CompDetail
            Case getTranslationResource("Begins With")
                'Formatted string
                CompVals = "N'" & Trim(CompVals) & "%" & "'"
            Case getTranslationResource("Contains")
                'Formatted string
                CompVals = "N'%" & Trim(CompVals) & "%" & "'"
        End Select
    
    Case "BETWEEN"
        ParameterCount = Parse(Trim(CompVals), ", ", "'", StrArray())
        
        If ParameterCount <> 2 Then
            strText = getTranslationResource(AIM_VendorLookUp.Caption)
            strMessage = getTranslationResource("MSGBOX06201")
            If StrComp(strMessage, "MSGBOX06201") = 0 Then strMessage = "Please enter two values separated by a comma "
            MsgBox strMessage, vbExclamation, strText
            BldCompVal = "Error"
            Exit Function
        Else
            'Formatted string
            CompVals = "N'" & StrArray(0) & _
                 "' AND N'" & StrArray(1) & "' "
        End If
        
    Case "IN"
        'Get the number of comparison parameters supplied.
        ParameterCount = Parse(Trim(CompVals), ", ", "'", StrArray())
        newtext = "("
        
        'Format the parameter list
        For IndexCounter = 0 To (ParameterCount - 2)
            newtext = newtext & "N'" & StrArray(IndexCounter) & "', "
        Next IndexCounter
        'Last one
        newtext = newtext & "N'" & StrArray(ParameterCount - 1) & "')"
        'Formatted string
        CompVals = newtext
        
    Case Else
        'Formatted string
        CompVals = "N'" & Trim(CompVals) & "'"
        
    End Select
    
    'Formatted string
    BldCompVal = CompVals

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldCompVal)", Err.Description
     f_HandleErr , , , "AIM_VendorLookUp::BldCompVal", Now, gDRGeneralError, True, Err
End Function

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
    Case 0          'Copy to Clipboard
        If f_IsRecordsetOpenAndPopulated(rsVendors) Then
            Clipboard.Clear
    
            For i = 0 To rsVendors.Fields.Count - 1
                s1 = s1 + rsVendors.Fields(i).Name + vbTab
            Next i
    
            s1 = s1 + vbCrLf
    
            rsVendors.MoveFirst
            s2 = rsVendors.GetString(adClipString)
    
            Clipboard.SetText s1 + s2, vbCFText
        End If
    Case 1          'Print
        Me.dgVendors.PrintData ssPrintAllRows, False, True
        
    End Select

    Screen.MousePointer = vbNormal


Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , "AIM_VendorLookUp::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtVnId_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(txtVnID.Text) = "" Then
        Exit Sub
    End If
    
    VnIdKey_Criteria = BldCompVal(Me.txtVnID.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtVnId_Validate)"
     f_HandleErr , , , "AIM_VendorLookUp::txtVnId_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtAssort_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(txtAssort.Text) = "" Then
        Exit Sub
    End If
    
    AssortKey_Criteria = BldCompVal(Me.txtAssort.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtAssort_Validate)"
     f_HandleErr , , , "AIM_VendorLookUp::txtAssort_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_VendorLookUp::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_VendorLookUp::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub
