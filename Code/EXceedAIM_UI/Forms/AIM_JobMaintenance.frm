VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_JobMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Job Maintenance"
   ClientHeight    =   5355
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9390
   Icon            =   "AIM_JobMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5355
   ScaleWidth      =   9390
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbJobScheduling 
      Left            =   150
      Top             =   5040
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   3
      Tools           =   "AIM_JobMaintenance.frx":030A
      ToolBars        =   "AIM_JobMaintenance.frx":293C
   End
   Begin ActiveTabs.SSActiveTabs atScheduling 
      Height          =   4935
      Left            =   45
      TabIndex        =   31
      Top             =   0
      Width           =   9270
      _ExtentX        =   16351
      _ExtentY        =   8705
      _Version        =   131083
      TabCount        =   4
      Tabs            =   "AIM_JobMaintenance.frx":2A40
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   4545
         Left            =   30
         TabIndex        =   16
         Top             =   360
         Width           =   9210
         _ExtentX        =   16245
         _ExtentY        =   8017
         _Version        =   131083
         TabGuid         =   "AIM_JobMaintenance.frx":2B4C
         Begin VB.Frame Frame1 
            Height          =   3735
            Left            =   120
            TabIndex        =   19
            Top             =   150
            Width           =   9015
            Begin VB.CheckBox txtHasStep 
               Caption         =   "Has Step(s)"
               Enabled         =   0   'False
               Height          =   340
               Left            =   5940
               TabIndex        =   4
               Top             =   3210
               Width           =   2850
            End
            Begin VB.CheckBox txtEnabled 
               Caption         =   "Enabled"
               Height          =   345
               Left            =   2670
               TabIndex        =   1
               Top             =   2850
               Width           =   2850
            End
            Begin VB.CheckBox txtHasServer 
               Caption         =   "Has Server"
               CausesValidation=   0   'False
               Enabled         =   0   'False
               Height          =   340
               Left            =   5940
               TabIndex        =   2
               Top             =   2850
               Width           =   2850
            End
            Begin VB.CheckBox txtHasSchedule 
               Caption         =   "Has Schedule"
               Enabled         =   0   'False
               Height          =   340
               Left            =   2670
               TabIndex        =   3
               Top             =   3210
               Width           =   2850
            End
            Begin TDBText6Ctl.TDBText txtName 
               Height          =   345
               Left            =   2670
               TabIndex        =   0
               Top             =   240
               Width           =   6000
               _Version        =   65536
               _ExtentX        =   10583
               _ExtentY        =   609
               Caption         =   "AIM_JobMaintenance.frx":2B74
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobMaintenance.frx":2BE0
               Key             =   "AIM_JobMaintenance.frx":2BFE
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   128
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtJobDescription 
               Height          =   1020
               Left            =   2670
               TabIndex        =   32
               Top             =   1425
               Width           =   6000
               _Version        =   65536
               _ExtentX        =   10583
               _ExtentY        =   1799
               Caption         =   "AIM_JobMaintenance.frx":2C42
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobMaintenance.frx":2CAE
               Key             =   "AIM_JobMaintenance.frx":2CCC
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtCategoryName 
               Height          =   345
               Left            =   2670
               TabIndex        =   33
               TabStop         =   0   'False
               Top             =   2520
               Width           =   1440
               _Version        =   65536
               _ExtentX        =   2540
               _ExtentY        =   609
               Caption         =   "AIM_JobMaintenance.frx":2D10
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_JobMaintenance.frx":2D7C
               Key             =   "AIM_JobMaintenance.frx":2D9A
               BackColor       =   14737632
               EditMode        =   0
               ForeColor       =   -2147483630
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   128
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label12 
               Caption         =   "Job Name"
               Height          =   300
               Left            =   120
               TabIndex        =   30
               Top             =   285
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Created"
               Height          =   300
               Left            =   120
               TabIndex        =   29
               Top             =   675
               Width           =   2445
            End
            Begin VB.Label txtDateCreated 
               BackColor       =   &H00E0E0E0&
               BorderStyle     =   1  'Fixed Single
               Height          =   345
               Left            =   2670
               TabIndex        =   28
               Top             =   630
               Width           =   1530
            End
            Begin VB.Label Label2 
               Caption         =   "Modified"
               Height          =   300
               Left            =   4680
               TabIndex        =   27
               Top             =   675
               Width           =   2370
            End
            Begin VB.Label txtDateLastModified 
               BackColor       =   &H00E0E0E0&
               BorderStyle     =   1  'Fixed Single
               Height          =   345
               Left            =   7140
               TabIndex        =   26
               Top             =   630
               Width           =   1530
            End
            Begin VB.Label Label3 
               Caption         =   "Description"
               Height          =   300
               Left            =   120
               TabIndex        =   25
               Top             =   1470
               Width           =   2445
            End
            Begin VB.Label Label4 
               Caption         =   "Category"
               Height          =   300
               Left            =   120
               TabIndex        =   24
               Top             =   2535
               Width           =   2445
            End
            Begin VB.Label txtOwner 
               BackColor       =   &H00E0E0E0&
               BorderStyle     =   1  'Fixed Single
               Height          =   345
               Left            =   2670
               TabIndex        =   23
               Top             =   1050
               Width           =   1530
            End
            Begin VB.Label Label6 
               Caption         =   "Owner"
               Height          =   300
               Left            =   120
               TabIndex        =   22
               Top             =   1095
               Width           =   2445
            End
            Begin VB.Label Label5 
               Caption         =   "Server"
               Height          =   300
               Left            =   4680
               TabIndex        =   21
               Top             =   1095
               Width           =   2370
            End
            Begin VB.Label txtOriginatingSever 
               BackColor       =   &H00E0E0E0&
               BorderStyle     =   1  'Fixed Single
               Height          =   345
               Left            =   7140
               TabIndex        =   20
               Top             =   1050
               Width           =   1530
            End
         End
         Begin VB.CommandButton cmdSave 
            Caption         =   "&Save"
            Default         =   -1  'True
            Height          =   345
            Left            =   7630
            Style           =   1  'Graphical
            TabIndex        =   5
            Top             =   4140
            Width           =   1485
         End
         Begin VB.CommandButton cmdCancel 
            Caption         =   "&Cancel"
            Height          =   345
            Left            =   6090
            Style           =   1  'Graphical
            TabIndex        =   6
            Top             =   4140
            Width           =   1485
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   4545
         Left            =   30
         TabIndex        =   18
         Top             =   360
         Width           =   9210
         _ExtentX        =   16245
         _ExtentY        =   8017
         _Version        =   131083
         TabGuid         =   "AIM_JobMaintenance.frx":2DDE
         Begin VB.CommandButton cmdScheduleDelete 
            Caption         =   "&Delete"
            Height          =   345
            Left            =   4530
            Style           =   1  'Graphical
            TabIndex        =   15
            Top             =   4140
            Width           =   1485
         End
         Begin VB.CommandButton cmdScheduleEdit 
            Caption         =   "&Edit"
            Height          =   345
            Left            =   6037
            Style           =   1  'Graphical
            TabIndex        =   14
            Top             =   4140
            Width           =   1485
         End
         Begin VB.CommandButton cmdScheduleNew 
            Caption         =   "&New"
            Height          =   345
            Left            =   7545
            Style           =   1  'Graphical
            TabIndex        =   13
            Top             =   4140
            Width           =   1485
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSchedule 
            Height          =   3840
            Left            =   150
            TabIndex        =   12
            Top             =   150
            Width           =   8880
            _Version        =   196617
            DataMode        =   1
            AllowUpdate     =   0   'False
            AllowColumnSwapping=   0
            SelectTypeCol   =   0
            SelectTypeRow   =   0
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            ExtraHeight     =   106
            CaptionAlignment=   0
            SplitterPos     =   1
            Columns(0).Width=   3200
            _ExtentX        =   15663
            _ExtentY        =   6773
            _StockProps     =   79
         End
      End
      Begin ActiveTabs.SSActiveTabPanel tpSteps 
         Height          =   4545
         Left            =   30
         TabIndex        =   17
         Top             =   360
         Width           =   9210
         _ExtentX        =   16245
         _ExtentY        =   8017
         _Version        =   131083
         TabGuid         =   "AIM_JobMaintenance.frx":2E06
         Begin VB.CommandButton cmdStepNew 
            Caption         =   "&New"
            Height          =   345
            Left            =   7560
            Style           =   1  'Graphical
            TabIndex        =   8
            Top             =   4140
            Width           =   1485
         End
         Begin VB.CommandButton cmdStepEdit 
            Caption         =   "&Edit"
            Height          =   345
            Left            =   4435
            Style           =   1  'Graphical
            TabIndex        =   10
            Top             =   4140
            Width           =   1485
         End
         Begin VB.CommandButton cmdStepInsert 
            Caption         =   "&Insert"
            Height          =   345
            Left            =   5990
            Style           =   1  'Graphical
            TabIndex        =   9
            Top             =   4140
            Width           =   1485
         End
         Begin VB.CommandButton cmdStepDelete 
            Caption         =   "&Delete"
            Height          =   345
            Left            =   2880
            Style           =   1  'Graphical
            TabIndex        =   11
            Top             =   4140
            Width           =   1485
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSteps 
            Height          =   3840
            Left            =   150
            TabIndex        =   7
            Top             =   150
            Width           =   8880
            _Version        =   196617
            DataMode        =   1
            AllowUpdate     =   0   'False
            AllowColumnSwapping=   0
            SelectTypeCol   =   0
            SelectTypeRow   =   0
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            ExtraHeight     =   106
            CaptionAlignment=   0
            SplitterPos     =   1
            Columns(0).Width=   3200
            _ExtentX        =   15663
            _ExtentY        =   6773
            _StockProps     =   79
         End
      End
   End
End
Attribute VB_Name = "AIM_JobMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Const SPLITTERPOSITION As Integer = 1

Const DRJobCategory As String = "SSADRJobs"

Public AddFlag As Boolean
Public CancelFlag As Boolean
Public CurJobName As String
Dim strMessage As String
Dim Heading As String
Dim Cn As New adodb.Connection

Dim sp_add_job As adodb.Command
'Dim sp_apply_job_to_targets As ADODB.Command  From 4.6.2 we are not adding jobs to multiple servers -sgajjela
Dim sp_add_jobserver As adodb.Command
Dim sp_delete_job As adodb.Command
Dim sp_delete_jobstep As adodb.Command
Dim sp_delete_jobschedule As adodb.Command

Dim sp_help_category As adodb.Command
Dim sp_help_job As adodb.Command
Dim sp_help_jobstep As adodb.Command
Dim sp_help_jobschedule As adodb.Command

Dim sp_start_job As adodb.Command
Dim sp_stop_job As adodb.Command

Dim sp_update_job As adodb.Command

Dim rsCategory As adodb.Recordset
Dim rsJob As adodb.Recordset
Dim rsJobSteps As adodb.Recordset
Dim rsJobSchedules As adodb.Recordset

Private Function AddJob(Job_Name As String, Enabled As Integer, Description As String, _
    Category As String, Target_Server As String) As Long
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim ErrorDesc As String
    
    'Assume failure until proven success
    AddJob = -1
    
    sp_add_job("@job_name").Value = Job_Name
    sp_add_job("@enabled").Value = Enabled
    sp_add_job("@description").Value = Description
    sp_add_job("@start_step_id").Value = 1
    sp_add_job("@category_id").Value = Null
    sp_add_job("@category_name").Value = Category
    sp_add_job("@owner_login_name").Value = Null
    sp_add_job("@notify_level_eventlog").Value = 2
    sp_add_job("@notify_level_email").Value = 0
    sp_add_job("@notify_level_netsend").Value = 0
    sp_add_job("@notify_level_page").Value = 0
    sp_add_job("@notify_email_operator_name").Value = Null
    sp_add_job("@notify_netsend_operator_name").Value = Null
    sp_add_job("@notify_page_operator_name").Value = Null
    sp_add_job("@delete_level").Value = 0
    
    'Add the job
    sp_add_job.Execute
    
    'Check for errors
    If sp_add_job(0) = 1 Then
        strMessage = getTranslationResource("MSGBOX02600")
        If StrComp(strMessage, "MSGBOX02600") = 0 Then strMessage = "Error processing job. "
        strMessage1 = getTranslationResource("MSGBOX02601")
        If StrComp(strMessage1, "MSGBOX02601") = 0 Then strMessage1 = "Job Scheduler Error..."
        
        MsgBox strMessage + vbCrLf + vbCrLf + Err.Description, vbCritical + vbOKOnly, strMessage1
    Else
'        We are not supporting this functionaliy begining from DR 4.6.2 -sgajjela
'        sp_apply_job_to_targets("@job_id").Value = Null
'        sp_apply_job_to_targets("@job_name").Value = Job_Name
'        sp_apply_job_to_targets("@target_server_groups").Value = Null
'        sp_apply_job_to_targets("@target_servers").Value = "(local)"
'        sp_apply_job_to_targets("@operation").Value = "APPLY"
'
'        sp_apply_job_to_targets.Execute
 
        
        sp_add_jobserver("@job_name").Value = Job_Name
        sp_add_jobserver("@server_name").Value = "(local)"
        sp_add_jobserver.Execute
   
        AddFlag = False
    End If

    'Return Success
    AddJob = 0
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AddJob)"
        f_HandleErr , , , "AIM_JobMaintenance::AddJob", Now, gDRGeneralError, True, Err
End Function

Private Function GetJobInfo(Job_Name As String)
On Error GoTo ErrorHandler
    
    'Retrieve the Job Info from the Server
    sp_help_job.Parameters("@job_name").Value = Job_Name
    sp_help_job.Parameters("@job_aspect").Value = "JOB"
    
    If Not f_IsRecordsetValidAndOpen(rsJob) Then
        rsJob.Open sp_help_job
    Else
        rsJob.Requery
    End If
    
    If Not f_IsRecordsetOpenAndPopulated(rsJob) Then
        Exit Function
    End If

    'General Job Info
    Me.txtName = rsJob("name").Value
    Me.txtDateCreated = Format(rsJob("date_created").Value, gDateFormat)
    Me.txtDateLastModified = Format(rsJob("date_modified").Value, gDateFormat)
    Me.txtJobDescription = IIf(IsNull(rsJob("description").Value), "", rsJob!Description)

    If rsJob("enabled").Value = 1 Then
        Me.txtEnabled.Value = vbChecked
    Else
        Me.txtEnabled.Value = vbUnchecked
    End If

    'Me.dcCategory.Text = rsJob("category").Value sgajjela
    
    'This value must be 'SSADRJobs'
    txtCategoryName.Text = rsJob("category").Value
    
    Me.txtOwner = rsJob("owner").Value
    Me.txtOriginatingSever = rsJob("originating_server").Value

    If rsJob("has_schedule").Value > 0 Then
        Me.txtHasSchedule.Value = vbChecked
    Else
        Me.txtHasSchedule.Value = vbUnchecked
    End If

    If rsJob("has_target").Value > 0 Then
        Me.txtHasServer.Value = vbChecked
    Else
        Me.txtHasServer.Value = vbUnchecked
    End If

    If rsJob("has_step").Value Then
        Me.txtHasStep.Value = vbChecked
    Else
        Me.txtHasStep.Value = vbUnchecked
    End If

    'Job Steps
    GetJobSteps CurJobName
    
    'Get Job Schedules
    GetJobSchedules CurJobName
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetJobInfo)"
         f_HandleErr , , , "AIM_JobMaintenance::GetJobInfo", Now, gDRGeneralError, True, Err
End Function


Private Function GetJobSteps(Job_Name As String)
On Error GoTo ErrorHandler
    
    If Job_Name <> "" Then
        sp_help_jobstep.Parameters("@job_name").Value = Job_Name
        
        If Not f_IsRecordsetValidAndOpen(rsJobSteps) Then
            rsJobSteps.Open sp_help_jobstep
        Else
            rsJobSteps.Requery
        End If
    End If
    
    Me.dgSteps.ReBind
    If f_IsRecordsetOpenAndPopulated(rsJobSteps) Then dgSteps.Rows = rsJobSteps.RecordCount

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetJobSteps)"
         f_HandleErr , , , "AIM_JobMaintenance::GetJobSteps", Now, gDRGeneralError, True, Err
End Function


Private Function GetJobSchedules(Job_Name As String)
On Error GoTo ErrorHandler
    
    sp_help_jobschedule.Parameters("@job_name").Value = Job_Name
    sp_help_jobschedule.Parameters("@include_description").Value = 1
    
    If Not f_IsRecordsetValidAndOpen(rsJobSchedules) Then
        rsJobSchedules.Open sp_help_jobschedule
    Else
        rsJobSchedules.Requery
    End If
    
    Me.dgSchedule.ReBind
    If f_IsRecordsetOpenAndPopulated(rsJobSchedules) Then dgSchedule.Rows = rsJobSchedules.RecordCount

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetJobSchedules)"
         f_HandleErr , , , "AIM_JobMaintenance::GetJobSchedules", Now, gDRGeneralError, True, Err
End Function


'Private Function BldJobCategoryList()
'On Error GoTo ErrorHandler
'
'    If Not f_IsRecordsetValidAndOpen(rsCategory) Then
'        rsCategory.Open sp_help_category
'    Else
'        rsCategory.Requery
'    End If
'
'    'Check for an empty record set
'    If Not f_IsRecordsetOpenAndPopulated(rsCategory) Then Exit Function
'
'    'Bind the Data Drop Down
'    Set Me.dcCategory.DataSourceList = rsCategory
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source, Err.Description & "(BldJobCategoryList)"
'End Function


Private Function UpdateJob(Job_Name As String, New_Name As String, Enabled As Integer, Description As String, _
    Category As String) As Long
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim ErrorDesc As String
    
    'Assume failure until proven success
    UpdateJob = -1
    
    sp_update_job("@job_id").Value = Null
    sp_update_job("@job_name").Value = Job_Name
    
    sp_update_job("@new_name").Value = New_Name
    sp_update_job("@enabled").Value = Enabled
    sp_update_job("@description").Value = Description
    sp_update_job("@start_step_id").Value = Null
    sp_update_job("@category_name").Value = Category
    sp_update_job("@owner_login_name").Value = Null
    sp_update_job("@notify_level_eventlog").Value = Null
    sp_update_job("@notify_level_email").Value = Null
    sp_update_job("@notify_level_netsend").Value = Null
    sp_update_job("@notify_level_page").Value = Null
    sp_update_job("@notify_email_operator_name").Value = Null
    sp_update_job("@notify_netsend_operator_name").Value = Null
    sp_update_job("@notify_page_operator_name").Value = Null
    sp_update_job("@delete_level").Value = Null
    
    'Add the job
    sp_update_job.Execute
    
    'Check for errors
    If sp_update_job(0) = 1 Then
        strMessage = getTranslationResource("MSGBOX02600")
        If StrComp(strMessage, "MSGBOX02600") = 0 Then strMessage = "Error processing job. "
        strMessage1 = getTranslationResource("MSGBOX02601")
        If StrComp(strMessage1, "MSGBOX02601") = 0 Then strMessage1 = "Job Scheduler Error..."
        
        MsgBox strMessage + vbCrLf + vbCrLf + Err.Description, vbCritical + vbOKOnly, strMessage1
    End If

    'Return Success
    UpdateJob = 0
    
Exit Function
ErrorHandler:
    If Err.Number = -2147217900 Then
        ErrorDesc = Err.Description
        strMessage = getTranslationResource("MSGBOX02600")
        If StrComp(strMessage, "MSGBOX02600") = 0 Then strMessage = "Error processing job. "
        strMessage1 = getTranslationResource("MSGBOX02601")
        If StrComp(strMessage1, "MSGBOX02601") = 0 Then strMessage1 = "Job Scheduler Error..."
        
        MsgBox strMessage + vbCrLf + vbCrLf + ErrorDesc, vbCritical + vbOKOnly, strMessage1
    Else
        'Err.Raise Err.Number, Err.source, Err.Description & "(UpdateJob)"
                 f_HandleErr , , , "AIM_JobMaintenance::UpdateJob", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function InitNewJob() As Long
On Error GoTo ErrorHandler

    Dim ErrorDesc As String
    
    'Assume failure until proven success
    InitNewJob = -1
    
    AddFlag = True
    
    'General Job Info
    Me.txtName = ""
    Me.txtDateCreated = Format(Date, gDateFormat)
    Me.txtDateLastModified = Format(Date, gDateFormat)
    Me.txtJobDescription = ""
    Me.txtEnabled.Value = vbChecked
    
    Me.txtCategoryName.Text = DRJobCategory
    
'    If f_IsRecordsetOpenAndPopulated(rsCategory) Then sgajjela
'        rsCategory.MoveFirst
'        Me.dcCategory.Text = rsCategory("name").Value
'    Else
'        Me.dcCategory.Text = ""
'    End If
    
    Me.txtOwner = ""
    Me.txtOriginatingSever = ""
    Me.txtHasSchedule.Value = vbUnchecked
    Me.txtHasServer.Value = vbUnchecked
    Me.txtHasStep.Value = vbUnchecked
    
    'Return Success
    InitNewJob = 0
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(InitNewJob)"
         f_HandleErr , , , "AIM_JobMaintenance::InitNewJob", Now, gDRGeneralError, True, Err
End Function


Private Sub atScheduling_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler
    
    Select Case NewTab.Key
    Case "Steps", "Schedules"
        If CurJobName = "" Then
            Heading = getTranslationResource("ERRMSG02702")
            If StrComp(Heading, "ERRMSG02702") = 0 Then Heading = "Job Maintenance"
            strMessage = getTranslationResource("ERRMSG02701")
            If StrComp(strMessage, "ERRMSG02701") = 0 Then strMessage = "Save Job before adding Steps or Schedules."
            MsgBox strMessage, vbCritical + vbOKOnly, Heading
           ' MsgBox "Save Job before adding Steps or Schedules.", vbOKOnly + vbCritical
            Cancel = True
        End If
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr "(atScheduling_BeforeTabClick)"
         f_HandleErr , , , "AIM_JobMaintenance::atScheduling_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr "(cmdCancel_Click)"
         f_HandleErr , , , "AIM_JobMaintenance::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdSave_Click()
On Error GoTo ErrorHandler
    Dim Result As Long

    If Me.txtName.Text = "" Then
        Heading = getTranslationResource("ERRMSG02702")
        If StrComp(Heading, "ERRMSG02702") = 0 Then Heading = "Job Maintenance"
        
         strMessage = getTranslationResource("ERRMSG02700")
        If StrComp(strMessage, "ERRMSG02700") = 0 Then strMessage = "Job name is requied."
        MsgBox strMessage, vbCritical + vbOKOnly, Heading
        
        Exit Sub
        Return
    End If
    
    If AddFlag Then
        'Result = AddJob(Me.txtName.Text, Me.txtEnabled.Value, Me.txtJobDescription.Text, Me.dcCategory.Text, "(local)")
        Result = AddJob(Me.txtName.Text, Me.txtEnabled.Value, Me.txtJobDescription.Text, Me.txtCategoryName.Text, "(local)") 'sgajjela
        If Result = 0 Then
            CurJobName = Me.txtName.Text
        End If
    Else
        'Result = UpdateJob(CurJobName, Me.txtName.Text, Me.txtEnabled.Value, Me.txtJobDescription.Text, Me.dcCategory.Text)
        Result = UpdateJob(CurJobName, Me.txtName.Text, Me.txtEnabled.Value, Me.txtJobDescription.Text, Me.txtCategoryName.Text)
        If Result = 0 Then
            'Update common identifier
            CurJobName = Me.txtName.Text
            'Refresh the details
            GetJobSteps CurJobName
            GetJobSchedules CurJobName
        End If
    End If
    
    'No more need for the Add Flag
    AddFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr "(cmdSave_Click)"
         f_HandleErr , , , "AIM_JobMaintenance::cmdSave_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdScheduleDelete_Click()
On Error GoTo ErrorHandler
    
    'Check for no schedules
    If f_IsRecordsetOpenAndPopulated(rsJobSchedules) Then
        rsJobSchedules.Bookmark = Me.dgSchedule.Bookmark
        
        sp_delete_jobschedule.Parameters("@job_name").Value = CurJobName
        sp_delete_jobschedule.Parameters("@name").Value = rsJobSchedules("schedule_name").Value
        
        sp_delete_jobschedule.Execute
        
        If sp_delete_jobschedule.Parameters(0).Value = 0 Then
            'Update the schedule
            GetJobSchedules CurJobName
        End If
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr "(cmdScheduleDelete_Click)"
         f_HandleErr , , , "AIM_JobMaintenance::cmdScheduleDelete_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdScheduleEdit_Click()
On Error GoTo ErrorHandler

    'Check for no schedules
    If f_IsRecordsetOpenAndPopulated(rsJobSchedules) Then
        'Get the current bookmark
        rsJobSchedules.Bookmark = Me.dgSchedule.Bookmark
        
        Set AIM_JobScheduleMaintenance.Cn = Cn
        Set AIM_JobScheduleMaintenance.rsSchedule = rsJobSchedules
    
        AIM_JobScheduleMaintenance.AddFlag = False
        AIM_JobScheduleMaintenance.CancelFlag = False
        AIM_JobScheduleMaintenance.JobName = CurJobName
        
        AIM_JobScheduleMaintenance.Show vbModal, AIM_Main

        'Update the schedule
        If Not AIM_JobScheduleMaintenance.CancelFlag Then
            GetJobSchedules CurJobName
        End If
        
        Set AIM_JobScheduleMaintenance = Nothing
    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::cmdScheduleEdit_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdScheduleNew_Click()
On Error GoTo ErrorHandler

'    If Not f_IsRecordsetValidAndOpen(rsJobSchedules) Then Exit Sub
    
    'rsJobSchedules.Bookmark = Me.dgSchedule.Bookmark
    Set AIM_JobScheduleMaintenance.Cn = Cn
    Set AIM_JobScheduleMaintenance.rsSchedule = rsJobSchedules

    AIM_JobScheduleMaintenance.AddFlag = True
    AIM_JobScheduleMaintenance.CancelFlag = False
    AIM_JobScheduleMaintenance.JobName = CurJobName

    AIM_JobScheduleMaintenance.Show vbModal, AIM_Main

    'Update the schedule display
    If Not AIM_JobScheduleMaintenance.CancelFlag Then
        GetJobSchedules CurJobName
    End If
    
    Set AIM_JobScheduleMaintenance = Nothing

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::cmdScheduleNew_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdStepDelete_Click()
On Error GoTo ErrorHandler

    'Check for no schedules
    If f_IsRecordsetOpenAndPopulated(rsJobSteps) Then
        rsJobSteps.Bookmark = Me.dgSteps.Bookmark
    
        sp_delete_jobstep.Parameters("@job_name").Value = CurJobName
        sp_delete_jobstep.Parameters("@step_id").Value = rsJobSteps("step_id").Value
        
        sp_delete_jobstep.Execute
        
        If sp_delete_jobstep.Parameters(0).Value = 0 Then
            'Update the schedule
            GetJobSteps CurJobName
        End If
    End If

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::cmdStepDelete_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdStepEdit_Click()
On Error GoTo ErrorHandler

    'Check for no schedules
    If f_IsRecordsetOpenAndPopulated(rsJobSteps) Then
        rsJobSteps.Bookmark = Me.dgSteps.Bookmark

        AIM_JobStepMaintenance.AddFlag = False
        AIM_JobStepMaintenance.CancelFlag = False
        AIM_JobStepMaintenance.StepId = rsJobSteps("step_id").Value
        AIM_JobStepMaintenance.JobName = CurJobName
        Set AIM_JobStepMaintenance.Cn = Cn
        Set AIM_JobStepMaintenance.JobStep = rsJobSteps
        
        AIM_JobStepMaintenance.Show vbModal, AIM_Main

        'Update the schedule
        If Not AIM_JobStepMaintenance.CancelFlag Then
            GetJobSteps CurJobName
        End If
        
        Set AIM_JobStepMaintenance = Nothing
    End If

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::cmdStepEdit_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdStepInsert_Click()
On Error GoTo ErrorHandler

    If f_IsRecordsetOpenAndPopulated(rsJobSteps) Then
        rsJobSteps.Bookmark = Me.dgSteps.Bookmark
        AIM_JobStepMaintenance.StepId = rsJobSteps("step_id").Value
    Else
        AIM_JobStepMaintenance.StepId = 0
    End If
    
    AIM_JobStepMaintenance.AddFlag = True
    AIM_JobStepMaintenance.CancelFlag = False
    AIM_JobStepMaintenance.JobName = CurJobName
    Set AIM_JobStepMaintenance.JobStep = New adodb.Recordset
    
    Set AIM_JobStepMaintenance.Cn = Cn

    AIM_JobStepMaintenance.Show vbModal, AIM_Main

    If AIM_JobStepMaintenance.AddFlag And Not AIM_JobStepMaintenance.CancelFlag Then
        GetJobSteps CurJobName
    End If
    
    Set AIM_JobStepMaintenance = Nothing

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::cmdStepInsert_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdStepNew_Click()
On Error GoTo ErrorHandler

    AIM_JobStepMaintenance.AddFlag = True
    AIM_JobStepMaintenance.CancelFlag = False
    AIM_JobStepMaintenance.StepId = 0
    
    If CurJobName <> "" Then
        AIM_JobStepMaintenance.JobName = CurJobName
    Else
        AIM_JobStepMaintenance.JobName = Me.txtName
    End If
    
    Set AIM_JobStepMaintenance.JobStep = New adodb.Recordset
    
    Set AIM_JobStepMaintenance.Cn = Cn

    AIM_JobStepMaintenance.Show vbModal, AIM_Main

    If AIM_JobStepMaintenance.AddFlag And Not AIM_JobStepMaintenance.CancelFlag Then

        GetJobSteps CurJobName

    End If
    
    Set AIM_JobStepMaintenance = Nothing

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::cmdStepNew_Click", Now, gDRGeneralError, True, Err
    
End Sub
'Control assosicated to this event was replaced with a text box. - sgajjela
'Private Sub dcCategory_Validate(Cancel As Boolean)
'On Error GoTo ErrorHandler
'
'    If Not Me.dcCategory.IsItemInList Then
'        Cancel = True
'    End If
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr "(dcCategory_Validate)"
'End Sub

'Control assosicated to this event was replaced with a text box. - sgajjela
'Private Sub dcCategory_InitColumnProps()
'On Error GoTo ErrorHandler
'
'    Dim IndexCounter As Long
'
'    Me.dcCategory.Columns(0).Name = "category"
'    Me.dcCategory.Columns(0).DataField = "name"
'    Me.dcCategory.Columns(0).Caption = ""
'    Me.dcCategory.Columns(0).Alignment = ssCaptionAlignmentLeft
'    Me.dcCategory.Columns(0).Width = 3000
'
'    Me.dcCategory.Columns(1).Visible = False
'    Me.dcCategory.Columns(2).Visible = False
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dcCategory, ACW_EXPAND
'    End If
'
''    For IndexCounter = 0 To dcCategory.Columns.Count - 1
''        dcCategory.Columns(IndexCounter).HasHeadBackColor = True
''        dcCategory.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
''    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr "(dcCategory_InitColumnProps)"
'End Sub

Private Sub dgSchedule_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgSchedule.ROWHEIGHT = Me.dgSchedule.ROWHEIGHT * 2
    
    'Define Columns
    Me.dgSchedule.Columns(0).Name = "scheduleid"
    Me.dgSchedule.Columns(0).Caption = getTranslationResource("ID")
    Me.dgSchedule.Columns(0).Width = 300
    Me.dgSchedule.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSchedule.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgSchedule.Columns(0).Locked = True
    
    Me.dgSchedule.Columns(1).Name = "name"
    Me.dgSchedule.Columns(1).Caption = getTranslationResource("Schedule Name")
    Me.dgSchedule.Columns(1).Width = 2000
    Me.dgSchedule.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSchedule.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgSchedule.Columns(1).Locked = True
    
    Me.dgSchedule.Columns(2).Name = "enabled"
    Me.dgSchedule.Columns(2).Caption = getTranslationResource("Enabled")
    Me.dgSchedule.Columns(2).Width = 800
    Me.dgSchedule.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSchedule.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgSchedule.Columns(2).Style = ssStyleCheckBox
    Me.dgSchedule.Columns(2).Locked = True
    
    Me.dgSchedule.Columns(3).Name = "description"
    Me.dgSchedule.Columns(3).Caption = getTranslationResource("Description")
    Me.dgSchedule.Columns(3).Width = 4500
    Me.dgSchedule.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSchedule.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgSchedule.Columns(3).Locked = True
    
    Me.dgSchedule.SplitterPos = 1
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgSchedule, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgSchedule.Columns.Count - 1
'        dgSchedule.Columns(IndexCounter).HasHeadBackColor = True
'        dgSchedule.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgSchedule.Columns(IndexCounter).Locked = False Then dgSchedule.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::dgSchedule_InitColumnProps", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgSchedule_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::dgSchedule_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSchedule_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
    
    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsJobSchedules) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsJobSchedules.MoveLast
        Else
            rsJobSchedules.MoveFirst
        End If
    Else
        rsJobSchedules.Bookmark = StartLocation
        If ReadPriorRows Then
            rsJobSchedules.MovePrevious
        Else
            rsJobSchedules.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsJobSchedules.BOF Or rsJobSchedules.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsJobSchedules("schedule_id").Value
        RowBuf.Value(i, 1) = rsJobSchedules("schedule_name").Value
        RowBuf.Value(i, 2) = rsJobSchedules("enabled").Value
        RowBuf.Value(i, 3) = rsJobSchedules("schedule_description").Value
        
        RowBuf.Bookmark(i) = rsJobSchedules.Bookmark
    
        If ReadPriorRows Then
            rsJobSchedules.MovePrevious
        Else
            rsJobSchedules.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::dgSchedule_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSteps_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgSteps.ROWHEIGHT = Me.dgSteps.ROWHEIGHT * 2

    'Define Columns
    Me.dgSteps.Columns(0).Name = "stepid"
    Me.dgSteps.Columns(0).Caption = getTranslationResource("ID")
    Me.dgSteps.Columns(0).Width = 300
    Me.dgSteps.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSteps.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgSteps.Columns(0).Locked = True

    Me.dgSteps.Columns(1).Name = "name"
    Me.dgSteps.Columns(1).Caption = getTranslationResource("Step Name")
    Me.dgSteps.Columns(1).Width = 2500
    Me.dgSteps.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSteps.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgSteps.Columns(1).Locked = True
    
    Me.dgSteps.Columns(2).Name = "subsystem"
    Me.dgSteps.Columns(2).Caption = getTranslationResource("Type")
    Me.dgSteps.Columns(2).Width = 800
    Me.dgSteps.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSteps.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgSteps.Columns(2).Locked = True
    
    Me.dgSteps.Columns(3).Name = "onsuccessaction"
    Me.dgSteps.Columns(3).Caption = getTranslationResource("On success")
    Me.dgSteps.Columns(3).Width = 2000
    Me.dgSteps.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSteps.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgSteps.Columns(3).Locked = True
    
    Me.dgSteps.Columns(4).Name = "onfailaction"
    Me.dgSteps.Columns(4).Caption = getTranslationResource("On failure")
    Me.dgSteps.Columns(4).Width = 2000
    Me.dgSteps.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSteps.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgSteps.Columns(4).Locked = True
    
    Me.dgSteps.SplitterPos = 1
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgSteps, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgSteps.Columns.Count - 1
'        dgSteps.Columns(IndexCounter).HasHeadBackColor = True
'        dgSteps.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgSteps.Columns(IndexCounter).Locked = False Then dgSteps.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::dgSteps_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSteps_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::dgSteps_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSteps_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer

    If Not f_IsRecordsetOpenAndPopulated(rsJobSteps) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsJobSteps.MoveLast
        Else
            rsJobSteps.MoveFirst
        End If
    Else
        rsJobSteps.Bookmark = StartLocation
        If ReadPriorRows Then
            rsJobSteps.MovePrevious
        Else
            rsJobSteps.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsJobSteps.BOF Or rsJobSteps.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsJobSteps("step_id").Value
        RowBuf.Value(i, 1) = rsJobSteps("step_name").Value
        RowBuf.Value(i, 2) = rsJobSteps("subsystem").Value
        
        Select Case rsJobSteps("on_success_action").Value
            Case 0
                RowBuf.Value(i, 3) = getTranslationResource("Unassigned")
            Case 1
                RowBuf.Value(i, 3) = getTranslationResource("Terminate - Report Success")
            Case 2
                RowBuf.Value(i, 3) = getTranslationResource("Terminate - Report Failure")
            Case 3
                RowBuf.Value(i, 3) = getTranslationResource("Continue Next Step")
            Case 4
                RowBuf.Value(i, 3) = getTranslationResource("Continue Next Identified Step")
        End Select
        
        Select Case rsJobSteps("on_fail_action").Value
            Case 0
                RowBuf.Value(i, 4) = getTranslationResource("Unassigned")
            Case 1
                RowBuf.Value(i, 4) = getTranslationResource("Terminate - Report Success")
            Case 2
                RowBuf.Value(i, 4) = getTranslationResource("Terminate - Report Failure")
            Case 3
                RowBuf.Value(i, 4) = getTranslationResource("Continue Next Step")
            Case 4
                RowBuf.Value(i, 4) = getTranslationResource("Continue Next Identified Step")
        End Select

        RowBuf.Bookmark(i) = rsJobSteps.Bookmark
    
        If ReadPriorRows Then
            rsJobSteps.MovePrevious
        Else
            rsJobSteps.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::dgSteps_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler
    
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Form_Activate)"
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    
    'Init category text box.
    txtCategoryName = DRJobCategory 'sgajjela
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02600")
    If StrComp(strMessage, "STATMSG02600") = 0 Then strMessage = "Initializing AIM Job Maintenance... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Connect to SQL Server
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False, True, , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Define Stored Procedures
    Set sp_add_job = New adodb.Command
    With sp_add_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_add_job"
        .Parameters.Refresh
    
    End With
'   We are not supporting functionality peformed by this object begining from DR 4.6.2 -sgajjela
'    Set sp_apply_job_to_targets = New ADODB.Command
'    With sp_apply_job_to_targets
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "sp_apply_job_to_targets"
'        .Parameters.Refresh
'
'    End With
   
    Set sp_add_jobserver = New adodb.Command
    With sp_add_jobserver
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_add_jobserver"
        .Parameters.Refresh

    End With
    
    Set sp_delete_job = New adodb.Command
    With sp_delete_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_delete_job"
        .Parameters.Refresh
        
    End With
    
    Set sp_delete_jobstep = New adodb.Command
    With sp_delete_jobstep
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_delete_jobstep"
        .Parameters.Refresh
    
    End With
    
    Set sp_delete_jobschedule = New adodb.Command
    With sp_delete_jobschedule
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_delete_jobschedule"
        .Parameters.Refresh
        
    End With
    
    Set sp_help_category = New adodb.Command
    With sp_help_category
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_help_category"
        .Parameters.Refresh
    
    End With
    
    Set sp_help_job = New adodb.Command
    With sp_help_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_help_job"
        .Parameters.Refresh
        
    End With
    
    Set sp_help_jobschedule = New adodb.Command
    With sp_help_jobschedule
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_help_jobschedule"
        .Parameters.Refresh
    
    End With
    
    Set sp_help_jobstep = New adodb.Command
    With sp_help_jobstep
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_help_jobstep"
        .Parameters.Refresh
    
    End With
    
    Set sp_start_job = New adodb.Command
    With sp_start_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_start_job"
        .Parameters.Refresh
        
    End With
    
    Set sp_stop_job = New adodb.Command
    With sp_stop_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_stop_job"
        .Parameters.Refresh
    
    End With
    
    Set sp_update_job = New adodb.Command
    With sp_update_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_update_job"
        .Parameters.Refresh
    
    End With
    
    'Define Record Sets
    Set rsCategory = New adodb.Recordset
    With rsCategory
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    
    End With
    
    Set rsJob = New adodb.Recordset
    With rsJob
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
        
    End With
    
    Set rsJobSchedules = New adodb.Recordset
    With rsJobSchedules
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    
    End With
    
    Set rsJobSteps = New adodb.Recordset
    With rsJobSteps
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    
    End With
    
    'Get the Categories List
    'BldJobCategoryList 'sgajjela
    
    'Get the Job Info
    If AddFlag Then
        InitNewJob
    Else
        RtnCode = GetJobInfo(CurJobName)
        
        If Not f_IsRecordsetOpenAndPopulated(rsJob) Then
            Unload Me
            Exit Sub
        End If
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Wind Up
    Screen.MousePointer = vbNormal
    Write_Message ""

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsJob) Then
        rsJob.Close
    End If

    If f_IsRecordsetValidAndOpen(rsCategory) Then
        rsCategory.Close
    End If
    
    If f_IsRecordsetValidAndOpen(rsJobSteps) Then
        rsJobSteps.Close
    End If
    
    If f_IsRecordsetValidAndOpen(rsJobSchedules) Then
        rsJobSchedules.Close
    End If
    
    Set rsCategory = Nothing
    Set rsJob = Nothing
    Set rsJobSteps = Nothing
    Set rsJobSchedules = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbJobScheduling_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
        
        Case "ID_Log"
            AIM_JobLog.Show vbModal
        
    End Select

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobMaintenance::tbJobScheduling_ToolClick", Now, gDRGeneralError, True, Err
End Sub
