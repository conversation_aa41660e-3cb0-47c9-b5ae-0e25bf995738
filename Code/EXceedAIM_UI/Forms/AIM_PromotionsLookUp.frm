VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_PromotionsLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Promotions Lookup"
   ClientHeight    =   5940
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8475
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_PromotionsLookUp.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5940
   ScaleWidth      =   8475
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   7032
      Style           =   1  'Graphical
      TabIndex        =   3
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3000
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return Selected Item"
      Height          =   345
      Left            =   4440
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   5400
      Width           =   2445
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   75
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   1560
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   5400
      Width           =   1365
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMPromotions 
      Height          =   3315
      Left            =   78
      TabIndex        =   8
      Top             =   1905
      Width           =   8325
      _Version        =   196617
      DataMode        =   1
      Cols            =   2
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      TabNavigation   =   1
      _ExtentX        =   14684
      _ExtentY        =   5847
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1650
      Left            =   78
      TabIndex        =   9
      Top             =   120
      Width           =   8325
      Begin TDBText6Ctl.TDBText txtPMId 
         Height          =   340
         Left            =   108
         TabIndex        =   0
         Top             =   735
         Width           =   1700
         _Version        =   65536
         _ExtentX        =   2999
         _ExtentY        =   600
         Caption         =   "AIM_PromotionsLookUp.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_PromotionsLookUp.frx":0376
         Key             =   "AIM_PromotionsLookUp.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
         Height          =   345
         Left            =   120
         TabIndex        =   1
         Top             =   1200
         Width           =   3135
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   600
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7305
         TabIndex        =   2
         Top             =   1200
         Width           =   870
         _Version        =   65536
         _ExtentX        =   1535
         _ExtentY        =   600
         Calculator      =   "AIM_PromotionsLookUp.frx":03D8
         Caption         =   "AIM_PromotionsLookUp.frx":03F8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_PromotionsLookUp.frx":0464
         Keys            =   "AIM_PromotionsLookUp.frx":0482
         Spin            =   "AIM_PromotionsLookUp.frx":04CC
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   200
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin VB.Label Label 
         Caption         =   "Maximum Rows"
         Height          =   300
         Index           =   0
         Left            =   5280
         TabIndex        =   11
         Top             =   1245
         Width           =   1935
      End
      Begin VB.Label Label 
         Caption         =   "Promotion ID"
         Height          =   340
         Index           =   1
         Left            =   108
         TabIndex        =   10
         Top             =   340
         Width           =   1700
      End
   End
End
Attribute VB_Name = "AIM_PromotionsLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection
Dim rsPromotions As ADODB.Recordset

Public CancelFlag As Boolean
Public PmIdKey As String

Private PMIdKey_Criteria As String

Private Function BuildPromotionsIdLookupQuery() As String
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    
    'Initialize SQL Statment
    SqlStmt = "SELECT PmId, PmDesc FROM AIMPromotions "
    'Build the Where Statment
    If Trim(Me.txtPmId.Text) <> "" Then
        WhrStmt = "WHERE PmId " & Me.dcComparison.Value & " " & PMIdKey_Criteria & " "
    Else
        WhrStmt = ""
    End If
        
    SqlStmt = SqlStmt & vbCrLf & _
            WhrStmt & vbCrLf & _
            "ORDER BY PmId "
    
    BuildPromotionsIdLookupQuery = SqlStmt
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BuildPromotionsIdLookupQuery)", Err.Description
    f_HandleErr , , , "AIM_PromotionsLookUp::BuildPromotionsIdLookupQuery", Now, gDRGeneralError, True, Err
End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim strText As String
    Dim strMessage As String
    
    Me.MousePointer = vbHourglass
    Write_Message ""
    
    'Make sure the user selected at least one criterion
    If Trim(Me.txtPmId.Text) = "" Then

        strText = getTranslationResource(AIM_PromotionsLookUp.Caption)
        strMessage = getTranslationResource("MSGBOX03900")
        If StrComp(strMessage, "MSGBOX03900") = 0 Then strMessage = "Please select one or more Lookup Criteria"
        MsgBox strMessage, vbExclamation, strText
        Me.txtPmId.SetFocus
        Me.MousePointer = vbNormal
        Exit Sub

    End If
    
    'Force validation
    txtPMId_Validate False
    If StrComp(PMIdKey_Criteria, "Error", vbTextCompare) = 0 _
    Then
        'Do NOT Proceed
    Else
        'Create the query
        SqlStmt = BuildPromotionsIdLookupQuery()
        
        'Recreate the result set
        If f_IsRecordsetValidAndOpen(rsPromotions) Then rsPromotions.Close
        rsPromotions.MaxRecords = Me.txtMaxRows.Value
        rsPromotions.Open SqlStmt
        
        If f_IsRecordsetOpenAndPopulated(rsPromotions) Then
            'Refresh the grid
            Me.dgAIMPromotions.ReBind
        Else
            If f_IsRecordsetValidAndOpen(rsPromotions) Then rsPromotions.Close
            Me.dgAIMPromotions.Reset
        End If
        
    End If
    
    'Return the screen values
    Me.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_PromotionsLookUp::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_PromotionsLookUp::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    Me.txtPmId.Text = ""
    PMIdKey_Criteria = ""
    
    Me.txtMaxRows.Value = 200
    
    If f_IsRecordsetValidAndOpen(rsPromotions) Then rsPromotions.Close
    Me.dgAIMPromotions.Reset

    Me.txtPmId.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_PromotionsLookUp::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgAIMPromotions.Rows > 0 Then
        Screen.MousePointer = vbHourglass
        Me.dgAIMPromotions.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_PromotionsLookUp::cmdPrint_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = False
    Me.PmIdKey = Me.dgAIMPromotions.Columns("PmId").Value
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_PromotionsLookUp::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_CloseUp()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case Me.dcComparison.Columns(0).Text
        
    Case "LIKE"
        Select Case Me.dcComparison.Columns(1).Text
        Case getTranslationResource("Begins With")
            strMessage = getTranslationResource("STATMSG03900")
            If StrComp(strMessage, "STATMSG03900") = 0 Then strMessage = "Enter either part or all of the beginning of the desired value. "
            Write_Message strMessage
        
        Case getTranslationResource("Contains")
            strMessage = getTranslationResource("STATMSG03901")
            If StrComp(strMessage, "STATMSG03901") = 0 Then strMessage = "Enter a string expression which is contained within the desired value. "
            Write_Message strMessage
        End Select
    
    Case "BETWEEN"
        strMessage = getTranslationResource("STATMSG03902")
        If StrComp(strMessage, "STATMSG03902") = 0 Then strMessage = "Enter two values between which the desired value should fall, separated by a comma. "
        Write_Message strMessage
    
    Case "IN"
        strMessage = getTranslationResource("STATMSG03903")
        If StrComp(strMessage, "STATMSG03903") = 0 Then strMessage = "Enter a list of values in which the desired value should fall, separated by commas. "
        Write_Message strMessage
    
    Case Else
        strMessage = getTranslationResource("STATMSG03904")
        If StrComp(strMessage, "STATMSG03904") = 0 Then strMessage = "Enter a value for comparison. "
        Write_Message strMessage
    
    End Select
    
    'Reset the comparison values
    txtPMId_Validate False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_CloseUp)"
     f_HandleErr , , , "AIM_PromotionsLookUp::dcComparison_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Build Columns
    Me.dcComparison.Columns(0).Caption = getTranslationResource("Comparison")
    Me.dcComparison.Columns(0).FieldLen = 1
    Me.dcComparison.Columns(0).Width = 1000
    
    Me.dcComparison.Columns(1).Caption = getTranslationResource("Description")
    Me.dcComparison.Columns(1).FieldLen = 24
    Me.dcComparison.Columns(1).Width = 2000
    
    'Load Values
    Me.dcComparison.AddItem ("=" + vbTab + getTranslationResource("Equal To"))
    Me.dcComparison.AddItem (">" + vbTab + getTranslationResource("Greater Than"))
    Me.dcComparison.AddItem (">=" + vbTab + getTranslationResource("Greater Than / Equal To"))
    Me.dcComparison.AddItem ("<" + vbTab + getTranslationResource("Less Than"))
    Me.dcComparison.AddItem ("<=" + vbTab + getTranslationResource("Less Than / Equal To"))
    Me.dcComparison.AddItem ("<>" + vbTab + getTranslationResource("Not Equal To"))
    Me.dcComparison.AddItem ("LIKE" + vbTab + getTranslationResource("Begins With"))
    Me.dcComparison.AddItem ("LIKE" + vbTab + getTranslationResource("Contains"))
    Me.dcComparison.AddItem ("BETWEEN" + vbTab + getTranslationResource("Is Between These Values"))
    Me.dcComparison.AddItem ("IN" + vbTab + getTranslationResource("Is in this List of Values"))

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcComparison, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcComparison.Columns.Count - 1
'        dcComparison.Columns(IndexCounter).HasHeadBackColor = True
'        dcComparison.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_InitColumnProps)"
     f_HandleErr , , , "AIM_PromotionsLookUp::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMPromotions_DblClick()
On Error GoTo ErrorHandler

    'Return selected item
    cmdReturn_Click

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMPromotions_DblClick)"
     f_HandleErr , , , "AIM_PromotionsLookUp::dgAIMPromotions_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMPromotions_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Review Cycle Data
    Me.dgAIMPromotions.Columns(0).Name = "PmId"
    Me.dgAIMPromotions.Columns(0).Caption = getTranslationResource("Promotion ID")
    Me.dgAIMPromotions.Columns(0).Width = 1000
    Me.dgAIMPromotions.Columns(0).Locked = True
    
    Me.dgAIMPromotions.Columns(1).Name = "PmDesc"
    Me.dgAIMPromotions.Columns(1).Caption = getTranslationResource("Description")
    Me.dgAIMPromotions.Columns(1).Width = 2880
    Me.dgAIMPromotions.Columns(1).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMPromotions, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMPromotions.Columns.Count - 1
'        dgAIMPromotions.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMPromotions.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMPromotions.Columns(IndexCounter).Locked = False Then dgAIMPromotions.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMPromotions_InitColumnProps)"
     f_HandleErr , , , "AIM_PromotionsLookUp::dgAIMPromotions_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMPromotions_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG03900")
    If StrComp(strMessage, "RPTMSG03900") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG03900")
    If StrComp(strMessage1, "RPTMSG03901") = 0 Then strMessage = "AIM Promotions Lookup List"
    strMessage2 = getTranslationResource("RPTMSG03902")
    If StrComp(strMessage2, "RPTMSG03902") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + strMessage1 _
                + vbTab + strMessage2 + " <page number>"
    
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMPromotions_PrintInitialize)"
     f_HandleErr , , , "AIM_PromotionsLookUp::dgAIMPromotions_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMPromotions_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsPromotions) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsPromotions.MoveLast
        Else
            rsPromotions.MoveFirst
        End If
    
    Else
        rsPromotions.Bookmark = StartLocation
        If ReadPriorRows Then
            rsPromotions.MovePrevious
        Else
            rsPromotions.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        
        If rsPromotions.BOF Or rsPromotions.eof Then Exit For
        
        RowBuf.Value(i, 0) = rsPromotions("PmId").Value
        RowBuf.Value(i, 1) = rsPromotions("PmDesc").Value

        RowBuf.Bookmark(i) = rsPromotions.Bookmark
    
        If ReadPriorRows Then
            rsPromotions.MovePrevious
        Else
            rsPromotions.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMPromotions_UnboundReadData)"
     f_HandleErr , , , "AIM_PromotionsLookUp::dgAIMPromotions_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Set the default value for the Cancel Flag
    CancelFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_PromotionsLookUp::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    'Initialize fields
    Me.txtPmId.Text = Me.PmIdKey
    PMIdKey_Criteria = BldCompVal(Me.PmIdKey, Me.dcComparison.Value, Me.dcComparison.Text)
    
    Me.txtMaxRows.Value = 200
    Me.dcComparison.Text = getTranslationResource("Greater Than / Equal To")
    
    'Build initial record set
    Set rsPromotions = New ADODB.Recordset
    rsPromotions.CursorLocation = adUseClient
    rsPromotions.CursorType = adOpenStatic
    rsPromotions.LockType = adLockReadOnly
    Set rsPromotions.ActiveConnection = Cn
    
    SqlStmt = "SELECT PmId, PmDesc FROM AIMPromotions WHERE PmId = 'xxxxxx' "
    rsPromotions.Open SqlStmt
    
    '************************************************************
    'Mar 01 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtMaxRows.Spin.Visible = 1
    '************************************************************
    
    'Bind the grid
    Me.dgAIMPromotions.ReBind
       
    GetTranslatedCaptions Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_PromotionsLookUp::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsPromotions) Then rsPromotions.Close
    Set rsPromotions = Nothing

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
        f_HandleErr , , , "AIM_PromotionsLookUp::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function BldCompVal(CompVals As String, CompType As String, _
    CompDetail As String)
On Error GoTo ErrorHandler

    Dim i, N As Integer
    Dim newtext As String
    Dim RtnStr As String
    Dim StrArray(0 To 10) As String
    Dim strMessage As String
    Dim strText As String
    
    strText = getTranslationResource(AIM_PromotionsLookUp.Caption)
    
    Select Case CompType
    
    Case "LIKE"
        Select Case CompDetail
        Case getTranslationResource("Begins With")
            CompVals = "N'" & CompVals & "%" & "'"
        Case getTranslationResource("Contains")
            CompVals = "N'%" & CompVals & "%" & "'"
        End Select
    
    Case "BETWEEN"
        N = Parse(Trim(CompVals), ", ", "'", StrArray())
        
        If N <> 2 Then
            strMessage = getTranslationResource("MSGBOX03901")
            If StrComp(strMessage, "MSGBOX03901") = 0 Then strMessage = "Please enter two values separated by a comma "
            MsgBox strMessage, vbExclamation, strText
            BldCompVal = "Error"
            Exit Function
        
        Else
            CompVals = "N'" & StrArray(0) & "'" & _
                  " AND N'" & StrArray(1) & "' "
            
        End If
        
    Case "IN"
        N = Parse(Trim(CompVals), ", ", "'", StrArray())
        newtext = "("
        
        For i = 0 To (N - 2)
            newtext = newtext & "N'" & StrArray(i) & "'" & ", "
        Next i
        
        newtext = newtext & "N'" & StrArray(N - 1) & "'" & ")"
            
        CompVals = newtext
        
    Case Else
        CompVals = "N'" & Trim(CompVals) & "'"
        
    End Select
    
    BldCompVal = CompVals

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldCompVal)", Err.Description
    f_HandleErr , , , "AIM_PromotionsLookUp::BldCompVal", Now, gDRGeneralError, True, Err
End Function

Private Sub txtPMId_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
   
    If Trim(txtPmId.Text) = "" Then
        Exit Sub
    End If
    
    PMIdKey_Criteria = BldCompVal(Me.txtPmId.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtPMId_Validate)"
     f_HandleErr , , , "AIM_PromotionsLookUp::txtPMId_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_PromotionsLookUp::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_PromotionsLookUp::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub
