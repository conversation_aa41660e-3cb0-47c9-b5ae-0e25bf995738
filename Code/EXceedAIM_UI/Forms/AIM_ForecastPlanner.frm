VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Object = "{562E3E04-2C31-4ECE-83F4-4017EEE51D40}#8.0#0"; "todg8.ocx"
Begin VB.Form AIM_ForecastPlanner 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Forecast Planner"
   ClientHeight    =   10005
   ClientLeft      =   975
   ClientTop       =   375
   ClientWidth     =   14685
   FillColor       =   &H00FFFFFF&
   BeginProperty Font 
      Name            =   "Microsoft Sans Serif"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   ForeColor       =   &H8000000F&
   Icon            =   "AIM_ForecastPlanner.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9838.754
   ScaleMode       =   0  'User
   ScaleWidth      =   14655.92
   WindowState     =   2  'Maximized
   Begin ActiveToolBars.SSActiveToolBars tbFcstModification 
      Left            =   120
      Top             =   9600
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   18
      Style           =   0
      DisplayContextMenu=   0   'False
      Tools           =   "AIM_ForecastPlanner.frx":030A
      ToolBars        =   "AIM_ForecastPlanner.frx":F4E0
   End
   Begin ActiveTabs.SSActiveTabs atModification 
      Height          =   5325
      Left            =   180
      TabIndex        =   12
      Top             =   4320
      Width           =   14475
      _ExtentX        =   25532
      _ExtentY        =   9393
      _Version        =   131083
      TabCount        =   5
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TabMinWidth     =   390
      TabMinHeight    =   0
      Tabs            =   "AIM_ForecastPlanner.frx":F955
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel3 
         Height          =   4950
         Left            =   30
         TabIndex        =   13
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   8731
         _Version        =   131083
         TabGuid         =   "AIM_ForecastPlanner.frx":FA9E
         Begin VB.Frame fraItemDetail 
            Height          =   3765
            Left            =   120
            TabIndex        =   26
            Top             =   1080
            Width           =   14175
            Begin TrueOleDBGrid80.TDBGrid dgItemDetail 
               Height          =   3255
               Left            =   120
               TabIndex        =   27
               Top             =   240
               Width           =   13935
               _ExtentX        =   24580
               _ExtentY        =   5741
               _LayoutType     =   0
               _RowHeight      =   -2147483647
               _WasPersistedAsPixels=   0
               Columns(0)._VlistStyle=   0
               Columns(0)._MaxComboItems=   5
               Columns(0).DataField=   ""
               Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
               Columns(1)._VlistStyle=   0
               Columns(1)._MaxComboItems=   5
               Columns(1).DataField=   ""
               Columns(1)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
               Columns.Count   =   2
               Splits(0)._UserFlags=   0
               Splits(0).AllowRowSizing=   0   'False
               Splits(0).RecordSelectorWidth=   688
               Splits(0)._SavedRecordSelectors=   -1  'True
               Splits(0).DividerColor=   14215660
               Splits(0).SpringMode=   0   'False
               Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
               Splits(0)._ColumnProps(0)=   "Columns.Count=2"
               Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
               Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
               Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
               Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
               Splits(0)._ColumnProps(5)=   "Column(1).Width=2725"
               Splits(0)._ColumnProps(6)=   "Column(1).DividerColor=0"
               Splits(0)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
               Splits(0)._ColumnProps(8)=   "Column(1).Order=2"
               Splits(1)._UserFlags=   0
               Splits(1).AllowRowSizing=   0   'False
               Splits(1).RecordSelectorWidth=   688
               Splits(1)._SavedRecordSelectors=   -1  'True
               Splits(1).DividerColor=   14215660
               Splits(1).SpringMode=   0   'False
               Splits(1)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
               Splits(1)._ColumnProps(0)=   "Columns.Count=2"
               Splits(1)._ColumnProps(1)=   "Column(0).Width=2725"
               Splits(1)._ColumnProps(2)=   "Column(0).DividerColor=0"
               Splits(1)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
               Splits(1)._ColumnProps(4)=   "Column(0).Order=1"
               Splits(1)._ColumnProps(5)=   "Column(1).Width=2725"
               Splits(1)._ColumnProps(6)=   "Column(1).DividerColor=0"
               Splits(1)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
               Splits(1)._ColumnProps(8)=   "Column(1).Order=2"
               Splits.Count    =   2
               PrintInfos(0)._StateFlags=   3
               PrintInfos(0).Name=   "piInternal 0"
               PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
               PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
               PrintInfos(0).PageHeaderHeight=   0
               PrintInfos(0).PageFooterHeight=   0
               PrintInfos.Count=   1
               DataMode        =   4
               DefColWidth     =   0
               HeadLines       =   1
               FootLines       =   1
               MultipleLines   =   0
               CellTipsWidth   =   0
               DeadAreaBackColor=   14215660
               RowDividerColor =   14215660
               RowSubDividerColor=   14215660
               DirectionAfterEnter=   1
               DirectionAfterTab=   1
               MaxRows         =   250000
               ViewColumnCaptionWidth=   0
               ViewColumnWidth =   0
               _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
               _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
               _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
               _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
               _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=0,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(5)   =   ":id=0,.fontname=Microsoft Sans Serif"
               _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(7)   =   ":id=1,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(8)   =   ":id=1,.fontname=Microsoft Sans Serif"
               _StyleDefs(9)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
               _StyleDefs(10)  =   "HeadingStyle:id=2,.parent=1,.namedParent=34,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(11)  =   ":id=2,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(12)  =   ":id=2,.fontname=Microsoft Sans Serif"
               _StyleDefs(13)  =   "FooterStyle:id=3,.parent=1,.namedParent=35,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(14)  =   ":id=3,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(15)  =   ":id=3,.fontname=Microsoft Sans Serif"
               _StyleDefs(16)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(17)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
               _StyleDefs(18)  =   "EditorStyle:id=7,.parent=1"
               _StyleDefs(19)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
               _StyleDefs(20)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
               _StyleDefs(21)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
               _StyleDefs(22)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
               _StyleDefs(23)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
               _StyleDefs(24)  =   "Splits(0).Style:id=43,.parent=1"
               _StyleDefs(25)  =   "Splits(0).CaptionStyle:id=52,.parent=4"
               _StyleDefs(26)  =   "Splits(0).HeadingStyle:id=44,.parent=2"
               _StyleDefs(27)  =   "Splits(0).FooterStyle:id=45,.parent=3"
               _StyleDefs(28)  =   "Splits(0).InactiveStyle:id=46,.parent=5"
               _StyleDefs(29)  =   "Splits(0).SelectedStyle:id=48,.parent=6"
               _StyleDefs(30)  =   "Splits(0).EditorStyle:id=47,.parent=7"
               _StyleDefs(31)  =   "Splits(0).HighlightRowStyle:id=49,.parent=8"
               _StyleDefs(32)  =   "Splits(0).EvenRowStyle:id=50,.parent=9"
               _StyleDefs(33)  =   "Splits(0).OddRowStyle:id=51,.parent=10"
               _StyleDefs(34)  =   "Splits(0).RecordSelectorStyle:id=53,.parent=11"
               _StyleDefs(35)  =   "Splits(0).FilterBarStyle:id=54,.parent=12"
               _StyleDefs(36)  =   "Splits(0).Columns(0).Style:id=58,.parent=43"
               _StyleDefs(37)  =   "Splits(0).Columns(0).HeadingStyle:id=55,.parent=44"
               _StyleDefs(38)  =   "Splits(0).Columns(0).FooterStyle:id=56,.parent=45"
               _StyleDefs(39)  =   "Splits(0).Columns(0).EditorStyle:id=57,.parent=47"
               _StyleDefs(40)  =   "Splits(0).Columns(1).Style:id=62,.parent=43"
               _StyleDefs(41)  =   "Splits(0).Columns(1).HeadingStyle:id=59,.parent=44"
               _StyleDefs(42)  =   "Splits(0).Columns(1).FooterStyle:id=60,.parent=45"
               _StyleDefs(43)  =   "Splits(0).Columns(1).EditorStyle:id=61,.parent=47"
               _StyleDefs(44)  =   "Splits(1).Style:id=13,.parent=1"
               _StyleDefs(45)  =   "Splits(1).CaptionStyle:id=22,.parent=4"
               _StyleDefs(46)  =   "Splits(1).HeadingStyle:id=14,.parent=2"
               _StyleDefs(47)  =   "Splits(1).FooterStyle:id=15,.parent=3"
               _StyleDefs(48)  =   "Splits(1).InactiveStyle:id=16,.parent=5"
               _StyleDefs(49)  =   "Splits(1).SelectedStyle:id=18,.parent=6"
               _StyleDefs(50)  =   "Splits(1).EditorStyle:id=17,.parent=7"
               _StyleDefs(51)  =   "Splits(1).HighlightRowStyle:id=19,.parent=8"
               _StyleDefs(52)  =   "Splits(1).EvenRowStyle:id=20,.parent=9"
               _StyleDefs(53)  =   "Splits(1).OddRowStyle:id=21,.parent=10"
               _StyleDefs(54)  =   "Splits(1).RecordSelectorStyle:id=23,.parent=11"
               _StyleDefs(55)  =   "Splits(1).FilterBarStyle:id=24,.parent=12"
               _StyleDefs(56)  =   "Splits(1).Columns(0).Style:id=28,.parent=13"
               _StyleDefs(57)  =   "Splits(1).Columns(0).HeadingStyle:id=25,.parent=14"
               _StyleDefs(58)  =   "Splits(1).Columns(0).FooterStyle:id=26,.parent=15"
               _StyleDefs(59)  =   "Splits(1).Columns(0).EditorStyle:id=27,.parent=17"
               _StyleDefs(60)  =   "Splits(1).Columns(1).Style:id=32,.parent=13"
               _StyleDefs(61)  =   "Splits(1).Columns(1).HeadingStyle:id=29,.parent=14"
               _StyleDefs(62)  =   "Splits(1).Columns(1).FooterStyle:id=30,.parent=15"
               _StyleDefs(63)  =   "Splits(1).Columns(1).EditorStyle:id=31,.parent=17"
               _StyleDefs(64)  =   "Named:id=33:Normal"
               _StyleDefs(65)  =   ":id=33,.parent=0"
               _StyleDefs(66)  =   "Named:id=34:Heading"
               _StyleDefs(67)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(68)  =   ":id=34,.wraptext=-1"
               _StyleDefs(69)  =   "Named:id=35:Footing"
               _StyleDefs(70)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(71)  =   "Named:id=36:Selected"
               _StyleDefs(72)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
               _StyleDefs(73)  =   "Named:id=37:Caption"
               _StyleDefs(74)  =   ":id=37,.parent=34,.alignment=2"
               _StyleDefs(75)  =   "Named:id=38:HighlightRow"
               _StyleDefs(76)  =   ":id=38,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
               _StyleDefs(77)  =   "Named:id=39:EvenRow"
               _StyleDefs(78)  =   ":id=39,.parent=33,.bgcolor=&HFFFF00&"
               _StyleDefs(79)  =   "Named:id=40:OddRow"
               _StyleDefs(80)  =   ":id=40,.parent=33"
               _StyleDefs(81)  =   "Named:id=41:RecordSelector"
               _StyleDefs(82)  =   ":id=41,.parent=34"
               _StyleDefs(83)  =   "Named:id=42:FilterBar"
               _StyleDefs(84)  =   ":id=42,.parent=33"
            End
         End
         Begin VB.Frame fraAdj_ItemDtl 
            Height          =   1095
            Left            =   120
            TabIndex        =   14
            Top             =   0
            Width           =   14175
            Begin VB.OptionButton optAdjType_Item 
               Caption         =   "Override"
               Height          =   345
               Index           =   0
               Left            =   7260
               TabIndex        =   19
               Top             =   645
               Width           =   1575
            End
            Begin VB.OptionButton optAdjType_Item 
               Caption         =   "+/- Units"
               Height          =   345
               Index           =   1
               Left            =   5730
               TabIndex        =   18
               Top             =   645
               Width           =   1485
            End
            Begin VB.OptionButton optAdjType_Item 
               Caption         =   "+/- Percentage"
               Height          =   345
               Index           =   2
               Left            =   3720
               TabIndex        =   17
               Top             =   645
               Value           =   -1  'True
               Width           =   1605
            End
            Begin VB.OptionButton optAdjType_Item 
               Caption         =   "Remove OverRide"
               Height          =   345
               Index           =   3
               Left            =   8910
               TabIndex        =   16
               Top             =   645
               Width           =   1665
            End
            Begin VB.OptionButton optAdjType_Item 
               Caption         =   "Remove Master OverRide"
               Height          =   345
               Index           =   4
               Left            =   10890
               TabIndex        =   15
               Top             =   645
               Width           =   2985
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjQty_Item 
               Height          =   345
               Left            =   5310
               TabIndex        =   20
               Top             =   210
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastPlanner.frx":FAC6
               Caption         =   "AIM_ForecastPlanner.frx":FAE6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":FB52
               Keys            =   "AIM_ForecastPlanner.frx":FB70
               Spin            =   "AIM_ForecastPlanner.frx":FBBA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBText6Ctl.TDBText txtAdjDesc_Item 
               Height          =   345
               Left            =   8070
               TabIndex        =   21
               Top             =   210
               Width           =   6015
               _Version        =   65536
               _ExtentX        =   10610
               _ExtentY        =   609
               Caption         =   "AIM_ForecastPlanner.frx":FBE2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":FC55
               Key             =   "AIM_ForecastPlanner.frx":FC73
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_Start 
               Height          =   345
               Index           =   2
               Left            =   1530
               TabIndex        =   94
               Top             =   180
               Width           =   1860
               DataFieldList   =   "StartDate"
               _Version        =   196617
               DataMode        =   2
               Cols            =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   -2147483643
               DataFieldToDisplay=   "StartDate"
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_End 
               DataField       =   "1"
               Height          =   345
               Index           =   2
               Left            =   1530
               TabIndex        =   97
               Top             =   600
               Width           =   1860
               DataFieldList   =   "EndDate"
               _Version        =   196617
               DataMode        =   2
               Cols            =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   -2147483643
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "End date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   10
               Left            =   180
               TabIndex        =   25
               Top             =   660
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Start date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   120
               TabIndex        =   24
               Top             =   255
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Reason"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   11
               Left            =   6750
               TabIndex        =   23
               Top             =   255
               Width           =   1215
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Adjustment"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   9
               Left            =   3750
               TabIndex        =   22
               Top             =   255
               Width           =   1455
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel7 
         Height          =   4950
         Left            =   30
         TabIndex        =   28
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   8731
         _Version        =   131083
         TabGuid         =   "AIM_ForecastPlanner.frx":FCB7
         Begin VB.Frame fraOptions 
            Height          =   4785
            Left            =   120
            TabIndex        =   29
            Top             =   0
            Width           =   14055
            Begin VB.Frame fraUnits 
               Caption         =   "Forecast Unit"
               Height          =   1365
               Left            =   120
               TabIndex        =   49
               Top             =   240
               Width           =   4305
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Weight"
                  Height          =   300
                  Index           =   2
                  Left            =   2280
                  TabIndex        =   54
                  Top             =   230
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Price"
                  Height          =   300
                  Index           =   4
                  Left            =   120
                  TabIndex        =   53
                  Top             =   940
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Cost"
                  Height          =   300
                  Index           =   1
                  Left            =   120
                  TabIndex        =   52
                  Top             =   585
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Units"
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   51
                  Top             =   230
                  Value           =   -1  'True
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Cube"
                  Height          =   300
                  Index           =   3
                  Left            =   2280
                  TabIndex        =   50
                  Top             =   585
                  Width           =   1695
               End
            End
            Begin VB.Frame Frame1 
               Caption         =   "User Elements"
               Height          =   4365
               Left            =   8790
               TabIndex        =   48
               Top             =   240
               Width           =   5100
               Begin TrueOleDBGrid80.TDBGrid dgUserElements 
                  Height          =   4035
                  Left            =   60
                  TabIndex        =   98
                  Top             =   210
                  Width           =   4965
                  _ExtentX        =   8758
                  _ExtentY        =   7117
                  _LayoutType     =   4
                  _RowHeight      =   -2147483647
                  _WasPersistedAsPixels=   0
                  Columns(0)._VlistStyle=   4
                  Columns(0)._MaxComboItems=   5
                  Columns(0).Caption=   "X"
                  Columns(0).DataField=   ""
                  Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
                  Columns(1)._VlistStyle=   0
                  Columns(1)._MaxComboItems=   5
                  Columns(1).Caption=   "UserElementId"
                  Columns(1).DataField=   ""
                  Columns(1)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
                  Columns(2)._VlistStyle=   0
                  Columns(2)._MaxComboItems=   5
                  Columns(2).Caption=   "UserElementDesc"
                  Columns(2).DataField=   ""
                  Columns(2)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
                  Columns(3)._VlistStyle=   0
                  Columns(3)._MaxComboItems=   5
                  Columns(3).Caption=   "Processed_YN"
                  Columns(3).DataField=   ""
                  Columns(3)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
                  Columns(4)._VlistStyle=   0
                  Columns(4)._MaxComboItems=   5
                  Columns(4).Caption=   "FcstType"
                  Columns(4).DataField=   ""
                  Columns(4)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
                  Columns.Count   =   5
                  Splits(0)._UserFlags=   0
                  Splits(0).RecordSelectorWidth=   503
                  Splits(0)._SavedRecordSelectors=   -1  'True
                  Splits(0).DividerColor=   13160660
                  Splits(0).SpringMode=   0   'False
                  Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
                  Splits(0)._ColumnProps(0)=   "Columns.Count=5"
                  Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
                  Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
                  Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
                  Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
                  Splits(0)._ColumnProps(5)=   "Column(1).Width=2725"
                  Splits(0)._ColumnProps(6)=   "Column(1).DividerColor=0"
                  Splits(0)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
                  Splits(0)._ColumnProps(8)=   "Column(1).Order=2"
                  Splits(0)._ColumnProps(9)=   "Column(2).Width=2725"
                  Splits(0)._ColumnProps(10)=   "Column(2).DividerColor=0"
                  Splits(0)._ColumnProps(11)=   "Column(2)._WidthInPix=2646"
                  Splits(0)._ColumnProps(12)=   "Column(2).Order=3"
                  Splits(0)._ColumnProps(13)=   "Column(3).Width=2725"
                  Splits(0)._ColumnProps(14)=   "Column(3).DividerColor=0"
                  Splits(0)._ColumnProps(15)=   "Column(3)._WidthInPix=2646"
                  Splits(0)._ColumnProps(16)=   "Column(3).Order=4"
                  Splits(0)._ColumnProps(17)=   "Column(4).Width=2725"
                  Splits(0)._ColumnProps(18)=   "Column(4).DividerColor=0"
                  Splits(0)._ColumnProps(19)=   "Column(4)._WidthInPix=2646"
                  Splits(0)._ColumnProps(20)=   "Column(4).Order=5"
                  Splits.Count    =   1
                  PrintInfos(0)._StateFlags=   3
                  PrintInfos(0).Name=   "piInternal 0"
                  PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
                  PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
                  PrintInfos(0).PageHeaderHeight=   0
                  PrintInfos(0).PageFooterHeight=   0
                  PrintInfos.Count=   1
                  DataMode        =   4
                  DefColWidth     =   0
                  HeadLines       =   1
                  FootLines       =   1
                  MultipleLines   =   0
                  CellTipsWidth   =   0
                  DeadAreaBackColor=   13160660
                  RowDividerColor =   13160660
                  RowSubDividerColor=   13160660
                  DirectionAfterEnter=   1
                  DirectionAfterTab=   1
                  MaxRows         =   250000
                  ViewColumnCaptionWidth=   0
                  ViewColumnWidth =   0
                  _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
                  _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
                  _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
                  _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
                  _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=1,.bold=0,.fontsize=825,.italic=0"
                  _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
                  _StyleDefs(5)   =   ":id=0,.fontname=Microsoft Sans Serif"
                  _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33,.bold=0,.fontsize=825,.italic=0"
                  _StyleDefs(7)   =   ":id=1,.underline=0,.strikethrough=0,.charset=0"
                  _StyleDefs(8)   =   ":id=1,.fontname=Microsoft Sans Serif"
                  _StyleDefs(9)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
                  _StyleDefs(10)  =   "HeadingStyle:id=2,.parent=1,.namedParent=34,.bold=0,.fontsize=825,.italic=0"
                  _StyleDefs(11)  =   ":id=2,.underline=0,.strikethrough=0,.charset=0"
                  _StyleDefs(12)  =   ":id=2,.fontname=Microsoft Sans Serif"
                  _StyleDefs(13)  =   "FooterStyle:id=3,.parent=1,.namedParent=35,.bold=0,.fontsize=825,.italic=0"
                  _StyleDefs(14)  =   ":id=3,.underline=0,.strikethrough=0,.charset=0"
                  _StyleDefs(15)  =   ":id=3,.fontname=Microsoft Sans Serif"
                  _StyleDefs(16)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
                  _StyleDefs(17)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
                  _StyleDefs(18)  =   "EditorStyle:id=7,.parent=1"
                  _StyleDefs(19)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
                  _StyleDefs(20)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
                  _StyleDefs(21)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
                  _StyleDefs(22)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
                  _StyleDefs(23)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
                  _StyleDefs(24)  =   "Splits(0).Style:id=13,.parent=1"
                  _StyleDefs(25)  =   "Splits(0).CaptionStyle:id=22,.parent=4"
                  _StyleDefs(26)  =   "Splits(0).HeadingStyle:id=14,.parent=2"
                  _StyleDefs(27)  =   "Splits(0).FooterStyle:id=15,.parent=3"
                  _StyleDefs(28)  =   "Splits(0).InactiveStyle:id=16,.parent=5"
                  _StyleDefs(29)  =   "Splits(0).SelectedStyle:id=18,.parent=6"
                  _StyleDefs(30)  =   "Splits(0).EditorStyle:id=17,.parent=7"
                  _StyleDefs(31)  =   "Splits(0).HighlightRowStyle:id=19,.parent=8"
                  _StyleDefs(32)  =   "Splits(0).EvenRowStyle:id=20,.parent=9"
                  _StyleDefs(33)  =   "Splits(0).OddRowStyle:id=21,.parent=10"
                  _StyleDefs(34)  =   "Splits(0).RecordSelectorStyle:id=23,.parent=11"
                  _StyleDefs(35)  =   "Splits(0).FilterBarStyle:id=24,.parent=12"
                  _StyleDefs(36)  =   "Splits(0).Columns(0).Style:id=54,.parent=13"
                  _StyleDefs(37)  =   "Splits(0).Columns(0).HeadingStyle:id=51,.parent=14"
                  _StyleDefs(38)  =   "Splits(0).Columns(0).FooterStyle:id=52,.parent=15"
                  _StyleDefs(39)  =   "Splits(0).Columns(0).EditorStyle:id=53,.parent=17"
                  _StyleDefs(40)  =   "Splits(0).Columns(1).Style:id=50,.parent=13"
                  _StyleDefs(41)  =   "Splits(0).Columns(1).HeadingStyle:id=47,.parent=14"
                  _StyleDefs(42)  =   "Splits(0).Columns(1).FooterStyle:id=48,.parent=15"
                  _StyleDefs(43)  =   "Splits(0).Columns(1).EditorStyle:id=49,.parent=17"
                  _StyleDefs(44)  =   "Splits(0).Columns(2).Style:id=46,.parent=13"
                  _StyleDefs(45)  =   "Splits(0).Columns(2).HeadingStyle:id=43,.parent=14"
                  _StyleDefs(46)  =   "Splits(0).Columns(2).FooterStyle:id=44,.parent=15"
                  _StyleDefs(47)  =   "Splits(0).Columns(2).EditorStyle:id=45,.parent=17"
                  _StyleDefs(48)  =   "Splits(0).Columns(3).Style:id=28,.parent=13"
                  _StyleDefs(49)  =   "Splits(0).Columns(3).HeadingStyle:id=25,.parent=14"
                  _StyleDefs(50)  =   "Splits(0).Columns(3).FooterStyle:id=26,.parent=15"
                  _StyleDefs(51)  =   "Splits(0).Columns(3).EditorStyle:id=27,.parent=17"
                  _StyleDefs(52)  =   "Splits(0).Columns(4).Style:id=32,.parent=13"
                  _StyleDefs(53)  =   "Splits(0).Columns(4).HeadingStyle:id=29,.parent=14"
                  _StyleDefs(54)  =   "Splits(0).Columns(4).FooterStyle:id=30,.parent=15"
                  _StyleDefs(55)  =   "Splits(0).Columns(4).EditorStyle:id=31,.parent=17"
                  _StyleDefs(56)  =   "Named:id=33:Normal"
                  _StyleDefs(57)  =   ":id=33,.parent=0"
                  _StyleDefs(58)  =   "Named:id=34:Heading"
                  _StyleDefs(59)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
                  _StyleDefs(60)  =   ":id=34,.wraptext=-1"
                  _StyleDefs(61)  =   "Named:id=35:Footing"
                  _StyleDefs(62)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
                  _StyleDefs(63)  =   "Named:id=36:Selected"
                  _StyleDefs(64)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
                  _StyleDefs(65)  =   "Named:id=37:Caption"
                  _StyleDefs(66)  =   ":id=37,.parent=34,.alignment=2"
                  _StyleDefs(67)  =   "Named:id=38:HighlightRow"
                  _StyleDefs(68)  =   ":id=38,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
                  _StyleDefs(69)  =   "Named:id=39:EvenRow"
                  _StyleDefs(70)  =   ":id=39,.parent=33,.bgcolor=&HFFFF00&"
                  _StyleDefs(71)  =   "Named:id=40:OddRow"
                  _StyleDefs(72)  =   ":id=40,.parent=33"
                  _StyleDefs(73)  =   "Named:id=41:RecordSelector"
                  _StyleDefs(74)  =   ":id=41,.parent=34"
                  _StyleDefs(75)  =   "Named:id=42:FilterBar"
                  _StyleDefs(76)  =   ":id=42,.parent=33"
               End
            End
            Begin VB.Frame fraInterval 
               Caption         =   "Forecast Interval"
               Height          =   1695
               Left            =   120
               TabIndex        =   39
               Top             =   1740
               Width           =   4305
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-Week"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   7
                  Left            =   2280
                  TabIndex        =   47
                  Top             =   1316
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-4-5"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   6
                  Left            =   2280
                  TabIndex        =   46
                  Top             =   954
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-5-4"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   5
                  Left            =   2280
                  TabIndex        =   45
                  Top             =   592
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "5-4-4"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   4
                  Left            =   2280
                  TabIndex        =   44
                  Top             =   230
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Quarters"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   3
                  Left            =   120
                  TabIndex        =   43
                  Top             =   1316
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Months"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   2
                  Left            =   120
                  TabIndex        =   42
                  Top             =   954
                  Value           =   -1  'True
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Weeks"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   1
                  Left            =   120
                  TabIndex        =   41
                  Top             =   592
                  Width           =   1695
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Days"
                  BeginProperty Font 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   40
                  Top             =   230
                  Width           =   1695
               End
            End
            Begin VB.Frame fraDataElements 
               Caption         =   "Data Elements"
               Height          =   4365
               Left            =   4560
               TabIndex        =   30
               Top             =   240
               Width           =   4140
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Net requirements"
                  Height          =   340
                  Index           =   3
                  Left            =   120
                  TabIndex        =   34
                  Top             =   1473
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "SysFcst"
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   31
                  Top             =   240
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Historical demand"
                  Height          =   340
                  Index           =   6
                  Left            =   120
                  TabIndex        =   37
                  Top             =   2706
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Projected inventory"
                  Height          =   340
                  Index           =   5
                  Left            =   120
                  TabIndex        =   36
                  Top             =   2295
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Master Fcst Adj"
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   32
                  Top             =   651
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Fcst Adj"
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   33
                  Top             =   1062
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Adj NetReq"
                  Height          =   340
                  Index           =   4
                  Left            =   120
                  TabIndex        =   35
                  Top             =   1884
                  Width           =   3765
               End
               Begin VB.CheckBox ckDataCalc_Type 
                  Caption         =   "Production Constraint"
                  Height          =   340
                  Index           =   7
                  Left            =   120
                  TabIndex        =   38
                  Top             =   3120
                  Width           =   3765
               End
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   4950
         Left            =   30
         TabIndex        =   55
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   8731
         _Version        =   131083
         TabGuid         =   "AIM_ForecastPlanner.frx":FCDF
         Begin VB.Frame FraSummary 
            Height          =   3585
            Left            =   120
            TabIndex        =   68
            Top             =   1080
            Width           =   14175
            Begin TrueOleDBGrid80.TDBGrid dgSummary 
               Height          =   3255
               Left            =   210
               TabIndex        =   69
               Top             =   240
               Width           =   13935
               _ExtentX        =   24580
               _ExtentY        =   5741
               _LayoutType     =   0
               _RowHeight      =   -2147483647
               _WasPersistedAsPixels=   0
               Columns(0)._VlistStyle=   0
               Columns(0)._MaxComboItems=   5
               Columns(0).DataField=   ""
               Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
               Columns(1)._VlistStyle=   0
               Columns(1)._MaxComboItems=   5
               Columns(1).DataField=   ""
               Columns(1)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
               Columns.Count   =   2
               Splits(0)._UserFlags=   0
               Splits(0).AllowSizing=   -1  'True
               Splits(0).AllowRowSizing=   0   'False
               Splits(0).RecordSelectorWidth=   688
               Splits(0)._SavedRecordSelectors=   -1  'True
               Splits(0).DividerColor=   14215660
               Splits(0).SpringMode=   0   'False
               Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
               Splits(0)._ColumnProps(0)=   "Columns.Count=2"
               Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
               Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
               Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
               Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
               Splits(0)._ColumnProps(5)=   "Column(1).Width=2725"
               Splits(0)._ColumnProps(6)=   "Column(1).DividerColor=0"
               Splits(0)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
               Splits(0)._ColumnProps(8)=   "Column(1).Order=2"
               Splits(1)._UserFlags=   0
               Splits(1).AllowSizing=   -1  'True
               Splits(1).AllowRowSizing=   0   'False
               Splits(1).RecordSelectorWidth=   688
               Splits(1)._SavedRecordSelectors=   -1  'True
               Splits(1).DividerColor=   14215660
               Splits(1).SpringMode=   0   'False
               Splits(1)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
               Splits(1)._ColumnProps(0)=   "Columns.Count=2"
               Splits(1)._ColumnProps(1)=   "Column(0).Width=2725"
               Splits(1)._ColumnProps(2)=   "Column(0).DividerColor=0"
               Splits(1)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
               Splits(1)._ColumnProps(4)=   "Column(0).Order=1"
               Splits(1)._ColumnProps(5)=   "Column(1).Width=2725"
               Splits(1)._ColumnProps(6)=   "Column(1).DividerColor=0"
               Splits(1)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
               Splits(1)._ColumnProps(8)=   "Column(1).Order=2"
               Splits.Count    =   2
               PrintInfos(0)._StateFlags=   3
               PrintInfos(0).Name=   "piInternal 0"
               PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
               PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
               PrintInfos(0).PageHeaderHeight=   0
               PrintInfos(0).PageFooterHeight=   0
               PrintInfos.Count=   1
               DataMode        =   4
               DefColWidth     =   0
               HeadLines       =   1
               FootLines       =   1
               MultipleLines   =   0
               CellTipsWidth   =   0
               DeadAreaBackColor=   14215660
               RowDividerColor =   14215660
               RowSubDividerColor=   14215660
               DirectionAfterEnter=   1
               DirectionAfterTab=   1
               MaxRows         =   250000
               ViewColumnCaptionWidth=   0
               ViewColumnWidth =   0
               _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
               _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
               _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
               _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
               _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=0,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(5)   =   ":id=0,.fontname=Microsoft Sans Serif"
               _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(7)   =   ":id=1,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(8)   =   ":id=1,.fontname=Microsoft Sans Serif"
               _StyleDefs(9)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
               _StyleDefs(10)  =   "HeadingStyle:id=2,.parent=1,.namedParent=34,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(11)  =   ":id=2,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(12)  =   ":id=2,.fontname=Microsoft Sans Serif"
               _StyleDefs(13)  =   "FooterStyle:id=3,.parent=1,.namedParent=35,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(14)  =   ":id=3,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(15)  =   ":id=3,.fontname=Microsoft Sans Serif"
               _StyleDefs(16)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(17)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
               _StyleDefs(18)  =   "EditorStyle:id=7,.parent=1"
               _StyleDefs(19)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
               _StyleDefs(20)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
               _StyleDefs(21)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
               _StyleDefs(22)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
               _StyleDefs(23)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
               _StyleDefs(24)  =   "Splits(0).Style:id=43,.parent=1"
               _StyleDefs(25)  =   "Splits(0).CaptionStyle:id=52,.parent=4"
               _StyleDefs(26)  =   "Splits(0).HeadingStyle:id=44,.parent=2"
               _StyleDefs(27)  =   "Splits(0).FooterStyle:id=45,.parent=3"
               _StyleDefs(28)  =   "Splits(0).InactiveStyle:id=46,.parent=5"
               _StyleDefs(29)  =   "Splits(0).SelectedStyle:id=48,.parent=6"
               _StyleDefs(30)  =   "Splits(0).EditorStyle:id=47,.parent=7"
               _StyleDefs(31)  =   "Splits(0).HighlightRowStyle:id=49,.parent=8"
               _StyleDefs(32)  =   "Splits(0).EvenRowStyle:id=50,.parent=9"
               _StyleDefs(33)  =   "Splits(0).OddRowStyle:id=51,.parent=10"
               _StyleDefs(34)  =   "Splits(0).RecordSelectorStyle:id=53,.parent=11"
               _StyleDefs(35)  =   "Splits(0).FilterBarStyle:id=54,.parent=12"
               _StyleDefs(36)  =   "Splits(0).Columns(0).Style:id=58,.parent=43"
               _StyleDefs(37)  =   "Splits(0).Columns(0).HeadingStyle:id=55,.parent=44"
               _StyleDefs(38)  =   "Splits(0).Columns(0).FooterStyle:id=56,.parent=45"
               _StyleDefs(39)  =   "Splits(0).Columns(0).EditorStyle:id=57,.parent=47"
               _StyleDefs(40)  =   "Splits(0).Columns(1).Style:id=62,.parent=43"
               _StyleDefs(41)  =   "Splits(0).Columns(1).HeadingStyle:id=59,.parent=44"
               _StyleDefs(42)  =   "Splits(0).Columns(1).FooterStyle:id=60,.parent=45"
               _StyleDefs(43)  =   "Splits(0).Columns(1).EditorStyle:id=61,.parent=47"
               _StyleDefs(44)  =   "Splits(1).Style:id=13,.parent=1"
               _StyleDefs(45)  =   "Splits(1).CaptionStyle:id=22,.parent=4"
               _StyleDefs(46)  =   "Splits(1).HeadingStyle:id=14,.parent=2"
               _StyleDefs(47)  =   "Splits(1).FooterStyle:id=15,.parent=3"
               _StyleDefs(48)  =   "Splits(1).InactiveStyle:id=16,.parent=5"
               _StyleDefs(49)  =   "Splits(1).SelectedStyle:id=18,.parent=6"
               _StyleDefs(50)  =   "Splits(1).EditorStyle:id=17,.parent=7"
               _StyleDefs(51)  =   "Splits(1).HighlightRowStyle:id=19,.parent=8"
               _StyleDefs(52)  =   "Splits(1).EvenRowStyle:id=20,.parent=9"
               _StyleDefs(53)  =   "Splits(1).OddRowStyle:id=21,.parent=10"
               _StyleDefs(54)  =   "Splits(1).RecordSelectorStyle:id=23,.parent=11"
               _StyleDefs(55)  =   "Splits(1).FilterBarStyle:id=24,.parent=12"
               _StyleDefs(56)  =   "Splits(1).Columns(0).Style:id=28,.parent=13"
               _StyleDefs(57)  =   "Splits(1).Columns(0).HeadingStyle:id=25,.parent=14"
               _StyleDefs(58)  =   "Splits(1).Columns(0).FooterStyle:id=26,.parent=15"
               _StyleDefs(59)  =   "Splits(1).Columns(0).EditorStyle:id=27,.parent=17"
               _StyleDefs(60)  =   "Splits(1).Columns(1).Style:id=32,.parent=13"
               _StyleDefs(61)  =   "Splits(1).Columns(1).HeadingStyle:id=29,.parent=14"
               _StyleDefs(62)  =   "Splits(1).Columns(1).FooterStyle:id=30,.parent=15"
               _StyleDefs(63)  =   "Splits(1).Columns(1).EditorStyle:id=31,.parent=17"
               _StyleDefs(64)  =   "Named:id=33:Normal"
               _StyleDefs(65)  =   ":id=33,.parent=0"
               _StyleDefs(66)  =   "Named:id=34:Heading"
               _StyleDefs(67)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(68)  =   ":id=34,.wraptext=-1"
               _StyleDefs(69)  =   "Named:id=35:Footing"
               _StyleDefs(70)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(71)  =   "Named:id=36:Selected"
               _StyleDefs(72)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
               _StyleDefs(73)  =   "Named:id=37:Caption"
               _StyleDefs(74)  =   ":id=37,.parent=34,.alignment=2"
               _StyleDefs(75)  =   "Named:id=38:HighlightRow"
               _StyleDefs(76)  =   ":id=38,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
               _StyleDefs(77)  =   "Named:id=39:EvenRow"
               _StyleDefs(78)  =   ":id=39,.parent=33,.bgcolor=&HFFFF00&"
               _StyleDefs(79)  =   "Named:id=40:OddRow"
               _StyleDefs(80)  =   ":id=40,.parent=33"
               _StyleDefs(81)  =   "Named:id=41:RecordSelector"
               _StyleDefs(82)  =   ":id=41,.parent=34"
               _StyleDefs(83)  =   "Named:id=42:FilterBar"
               _StyleDefs(84)  =   ":id=42,.parent=33"
            End
         End
         Begin VB.Frame fraAdj_Smr 
            Height          =   1095
            Left            =   120
            TabIndex        =   56
            Top             =   0
            Width           =   14175
            Begin VB.OptionButton optAdjType_Sum 
               Caption         =   "+/- Percentage"
               Height          =   315
               Index           =   2
               Left            =   3660
               TabIndex        =   59
               Top             =   660
               Value           =   -1  'True
               Width           =   1935
            End
            Begin VB.OptionButton optAdjType_Sum 
               Caption         =   "+/- Units"
               Enabled         =   0   'False
               Height          =   345
               Index           =   1
               Left            =   5760
               TabIndex        =   58
               Top             =   660
               Width           =   1935
            End
            Begin VB.OptionButton optAdjType_Sum 
               Caption         =   "Override"
               Enabled         =   0   'False
               Height          =   345
               Index           =   0
               Left            =   7260
               TabIndex        =   57
               Top             =   645
               Width           =   1935
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjQty_Smr 
               Height          =   345
               Left            =   5310
               TabIndex        =   60
               Top             =   210
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastPlanner.frx":FD07
               Caption         =   "AIM_ForecastPlanner.frx":FD27
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":FD93
               Keys            =   "AIM_ForecastPlanner.frx":FDB1
               Spin            =   "AIM_ForecastPlanner.frx":FDFB
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1179653
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcGroupBy 
               Height          =   345
               Left            =   12240
               TabIndex        =   61
               Top             =   645
               Width           =   1830
               DataFieldList   =   "Column 1"
               ListAutoValidate=   0   'False
               ListAutoPosition=   0   'False
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   3228
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "Column 0"
            End
            Begin TDBText6Ctl.TDBText txtAdjDesc_Smr 
               Height          =   345
               Left            =   8070
               TabIndex        =   62
               Top             =   210
               Width           =   6015
               _Version        =   65536
               _ExtentX        =   10610
               _ExtentY        =   609
               Caption         =   "AIM_ForecastPlanner.frx":FE23
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":FE96
               Key             =   "AIM_ForecastPlanner.frx":FEB4
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_Start 
               Height          =   345
               Index           =   0
               Left            =   1470
               TabIndex        =   92
               Top             =   210
               Width           =   1860
               DataFieldList   =   "StartDate"
               AllowInput      =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   -2147483643
               DataFieldToDisplay=   "StartDate"
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_End 
               DataField       =   "1"
               Height          =   345
               Index           =   0
               Left            =   1470
               TabIndex        =   95
               Top             =   600
               Width           =   1860
               DataFieldList   =   "EndDate"
               AllowInput      =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   -2147483643
            End
            Begin VB.Label lblGroupBy 
               Caption         =   "Group by"
               Height          =   300
               Index           =   0
               Left            =   10590
               TabIndex        =   67
               Top             =   660
               Width           =   1605
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Adjustment"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   3750
               TabIndex        =   66
               Top             =   255
               Width           =   1455
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Start date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   150
               TabIndex        =   65
               Top             =   255
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "End date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   150
               TabIndex        =   64
               Top             =   660
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Reason"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   6750
               TabIndex        =   63
               Top             =   255
               Width           =   1215
            End
         End
         Begin ActiveToolBars.SSActiveToolBars tbAdjustments 
            Index           =   0
            Left            =   150
            Top             =   3480
            _ExtentX        =   582
            _ExtentY        =   582
            _Version        =   131083
            ToolBarsCount   =   1
            ToolsCount      =   6
            Style           =   0
            Enabled         =   0   'False
            Visible         =   0   'False
            Tools           =   "AIM_ForecastPlanner.frx":FEF8
            ToolBars        =   "AIM_ForecastPlanner.frx":14B6B
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   4950
         Left            =   30
         TabIndex        =   70
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   8731
         _Version        =   131083
         TabGuid         =   "AIM_ForecastPlanner.frx":14D48
         Begin VB.Frame FraItems 
            Height          =   3765
            Left            =   120
            TabIndex        =   84
            Top             =   1080
            Width           =   14175
            Begin TrueOleDBGrid80.TDBGrid dgItem 
               Height          =   3255
               Left            =   120
               TabIndex        =   85
               Top             =   240
               Width           =   13935
               _ExtentX        =   24580
               _ExtentY        =   5741
               _LayoutType     =   0
               _RowHeight      =   -2147483647
               _WasPersistedAsPixels=   0
               Columns(0)._VlistStyle=   0
               Columns(0)._MaxComboItems=   5
               Columns(0).DataField=   ""
               Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
               Columns(1)._VlistStyle=   0
               Columns(1)._MaxComboItems=   5
               Columns(1).DataField=   ""
               Columns(1)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
               Columns.Count   =   2
               Splits(0)._UserFlags=   0
               Splits(0).AllowRowSizing=   0   'False
               Splits(0).RecordSelectorWidth=   688
               Splits(0)._SavedRecordSelectors=   -1  'True
               Splits(0).DividerColor=   14215660
               Splits(0).SpringMode=   0   'False
               Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
               Splits(0)._ColumnProps(0)=   "Columns.Count=2"
               Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
               Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
               Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
               Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
               Splits(0)._ColumnProps(5)=   "Column(1).Width=2725"
               Splits(0)._ColumnProps(6)=   "Column(1).DividerColor=0"
               Splits(0)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
               Splits(0)._ColumnProps(8)=   "Column(1).Order=2"
               Splits(1)._UserFlags=   0
               Splits(1).AllowRowSizing=   0   'False
               Splits(1).RecordSelectorWidth=   688
               Splits(1)._SavedRecordSelectors=   -1  'True
               Splits(1).DividerColor=   14215660
               Splits(1).SpringMode=   0   'False
               Splits(1)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
               Splits(1)._ColumnProps(0)=   "Columns.Count=2"
               Splits(1)._ColumnProps(1)=   "Column(0).Width=2725"
               Splits(1)._ColumnProps(2)=   "Column(0).DividerColor=0"
               Splits(1)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
               Splits(1)._ColumnProps(4)=   "Column(0).Order=1"
               Splits(1)._ColumnProps(5)=   "Column(1).Width=2725"
               Splits(1)._ColumnProps(6)=   "Column(1).DividerColor=0"
               Splits(1)._ColumnProps(7)=   "Column(1)._WidthInPix=2646"
               Splits(1)._ColumnProps(8)=   "Column(1).Order=2"
               Splits.Count    =   2
               PrintInfos(0)._StateFlags=   3
               PrintInfos(0).Name=   "piInternal 0"
               PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
               PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=Microsoft Sans Serif"
               PrintInfos(0).PageHeaderHeight=   0
               PrintInfos(0).PageFooterHeight=   0
               PrintInfos.Count=   1
               DataMode        =   4
               DefColWidth     =   0
               HeadLines       =   1
               FootLines       =   1
               MultipleLines   =   0
               CellTipsWidth   =   0
               DeadAreaBackColor=   14215660
               RowDividerColor =   14215660
               RowSubDividerColor=   14215660
               DirectionAfterEnter=   1
               DirectionAfterTab=   1
               MaxRows         =   250000
               ViewColumnCaptionWidth=   0
               ViewColumnWidth =   0
               _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
               _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
               _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
               _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
               _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=0,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(5)   =   ":id=0,.fontname=Microsoft Sans Serif"
               _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(7)   =   ":id=1,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(8)   =   ":id=1,.fontname=Microsoft Sans Serif"
               _StyleDefs(9)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
               _StyleDefs(10)  =   "HeadingStyle:id=2,.parent=1,.namedParent=34,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(11)  =   ":id=2,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(12)  =   ":id=2,.fontname=Microsoft Sans Serif"
               _StyleDefs(13)  =   "FooterStyle:id=3,.parent=1,.namedParent=35,.bold=0,.fontsize=825,.italic=0"
               _StyleDefs(14)  =   ":id=3,.underline=0,.strikethrough=0,.charset=0"
               _StyleDefs(15)  =   ":id=3,.fontname=Microsoft Sans Serif"
               _StyleDefs(16)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(17)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
               _StyleDefs(18)  =   "EditorStyle:id=7,.parent=1"
               _StyleDefs(19)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
               _StyleDefs(20)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
               _StyleDefs(21)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
               _StyleDefs(22)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
               _StyleDefs(23)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
               _StyleDefs(24)  =   "Splits(0).Style:id=43,.parent=1"
               _StyleDefs(25)  =   "Splits(0).CaptionStyle:id=52,.parent=4"
               _StyleDefs(26)  =   "Splits(0).HeadingStyle:id=44,.parent=2"
               _StyleDefs(27)  =   "Splits(0).FooterStyle:id=45,.parent=3"
               _StyleDefs(28)  =   "Splits(0).InactiveStyle:id=46,.parent=5"
               _StyleDefs(29)  =   "Splits(0).SelectedStyle:id=48,.parent=6"
               _StyleDefs(30)  =   "Splits(0).EditorStyle:id=47,.parent=7"
               _StyleDefs(31)  =   "Splits(0).HighlightRowStyle:id=49,.parent=8"
               _StyleDefs(32)  =   "Splits(0).EvenRowStyle:id=50,.parent=9"
               _StyleDefs(33)  =   "Splits(0).OddRowStyle:id=51,.parent=10"
               _StyleDefs(34)  =   "Splits(0).RecordSelectorStyle:id=53,.parent=11"
               _StyleDefs(35)  =   "Splits(0).FilterBarStyle:id=54,.parent=12"
               _StyleDefs(36)  =   "Splits(0).Columns(0).Style:id=58,.parent=43"
               _StyleDefs(37)  =   "Splits(0).Columns(0).HeadingStyle:id=55,.parent=44"
               _StyleDefs(38)  =   "Splits(0).Columns(0).FooterStyle:id=56,.parent=45"
               _StyleDefs(39)  =   "Splits(0).Columns(0).EditorStyle:id=57,.parent=47"
               _StyleDefs(40)  =   "Splits(0).Columns(1).Style:id=62,.parent=43"
               _StyleDefs(41)  =   "Splits(0).Columns(1).HeadingStyle:id=59,.parent=44"
               _StyleDefs(42)  =   "Splits(0).Columns(1).FooterStyle:id=60,.parent=45"
               _StyleDefs(43)  =   "Splits(0).Columns(1).EditorStyle:id=61,.parent=47"
               _StyleDefs(44)  =   "Splits(1).Style:id=13,.parent=1"
               _StyleDefs(45)  =   "Splits(1).CaptionStyle:id=22,.parent=4"
               _StyleDefs(46)  =   "Splits(1).HeadingStyle:id=14,.parent=2"
               _StyleDefs(47)  =   "Splits(1).FooterStyle:id=15,.parent=3"
               _StyleDefs(48)  =   "Splits(1).InactiveStyle:id=16,.parent=5"
               _StyleDefs(49)  =   "Splits(1).SelectedStyle:id=18,.parent=6"
               _StyleDefs(50)  =   "Splits(1).EditorStyle:id=17,.parent=7"
               _StyleDefs(51)  =   "Splits(1).HighlightRowStyle:id=19,.parent=8"
               _StyleDefs(52)  =   "Splits(1).EvenRowStyle:id=20,.parent=9"
               _StyleDefs(53)  =   "Splits(1).OddRowStyle:id=21,.parent=10"
               _StyleDefs(54)  =   "Splits(1).RecordSelectorStyle:id=23,.parent=11"
               _StyleDefs(55)  =   "Splits(1).FilterBarStyle:id=24,.parent=12"
               _StyleDefs(56)  =   "Splits(1).Columns(0).Style:id=28,.parent=13"
               _StyleDefs(57)  =   "Splits(1).Columns(0).HeadingStyle:id=25,.parent=14"
               _StyleDefs(58)  =   "Splits(1).Columns(0).FooterStyle:id=26,.parent=15"
               _StyleDefs(59)  =   "Splits(1).Columns(0).EditorStyle:id=27,.parent=17"
               _StyleDefs(60)  =   "Splits(1).Columns(1).Style:id=32,.parent=13"
               _StyleDefs(61)  =   "Splits(1).Columns(1).HeadingStyle:id=29,.parent=14"
               _StyleDefs(62)  =   "Splits(1).Columns(1).FooterStyle:id=30,.parent=15"
               _StyleDefs(63)  =   "Splits(1).Columns(1).EditorStyle:id=31,.parent=17"
               _StyleDefs(64)  =   "Named:id=33:Normal"
               _StyleDefs(65)  =   ":id=33,.parent=0"
               _StyleDefs(66)  =   "Named:id=34:Heading"
               _StyleDefs(67)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(68)  =   ":id=34,.wraptext=-1"
               _StyleDefs(69)  =   "Named:id=35:Footing"
               _StyleDefs(70)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
               _StyleDefs(71)  =   "Named:id=36:Selected"
               _StyleDefs(72)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
               _StyleDefs(73)  =   "Named:id=37:Caption"
               _StyleDefs(74)  =   ":id=37,.parent=34,.alignment=2"
               _StyleDefs(75)  =   "Named:id=38:HighlightRow"
               _StyleDefs(76)  =   ":id=38,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
               _StyleDefs(77)  =   "Named:id=39:EvenRow"
               _StyleDefs(78)  =   ":id=39,.parent=33,.bgcolor=&HFFFF00&"
               _StyleDefs(79)  =   "Named:id=40:OddRow"
               _StyleDefs(80)  =   ":id=40,.parent=33"
               _StyleDefs(81)  =   "Named:id=41:RecordSelector"
               _StyleDefs(82)  =   ":id=41,.parent=34"
               _StyleDefs(83)  =   "Named:id=42:FilterBar"
               _StyleDefs(84)  =   ":id=42,.parent=33"
            End
         End
         Begin VB.Frame fraAdj_Dtl 
            Height          =   1095
            Left            =   120
            TabIndex        =   71
            Top             =   0
            Width           =   14175
            Begin VB.CheckBox ckToggleAdjDisplay 
               Caption         =   "Show available adjustments"
               Height          =   375
               Index           =   1
               Left            =   13680
               TabIndex        =   77
               Top             =   600
               Visible         =   0   'False
               Width           =   165
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "Override"
               Height          =   345
               Index           =   0
               Left            =   7260
               TabIndex        =   76
               Top             =   645
               Width           =   1575
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "+/- Units"
               Height          =   345
               Index           =   1
               Left            =   5730
               TabIndex        =   75
               Top             =   645
               Width           =   1485
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "+/- Percentage"
               Height          =   345
               Index           =   2
               Left            =   3720
               TabIndex        =   74
               Top             =   645
               Value           =   -1  'True
               Width           =   1605
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "Remove OverRide"
               Height          =   345
               Index           =   3
               Left            =   8910
               TabIndex        =   73
               Top             =   645
               Width           =   1665
            End
            Begin VB.OptionButton optAdjType_Dtl 
               Caption         =   "Remove Master OverRide"
               Height          =   345
               Index           =   4
               Left            =   10890
               TabIndex        =   72
               Top             =   645
               Width           =   2985
            End
            Begin TDBNumber6Ctl.TDBNumber txtAdjQty_Dtl 
               Height          =   345
               Left            =   5310
               TabIndex        =   78
               Top             =   210
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastPlanner.frx":14D70
               Caption         =   "AIM_ForecastPlanner.frx":14D90
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":14DFC
               Keys            =   "AIM_ForecastPlanner.frx":14E1A
               Spin            =   "AIM_ForecastPlanner.frx":14E64
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   -999.99
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBText6Ctl.TDBText txtAdjDesc_Dtl 
               Height          =   345
               Left            =   8070
               TabIndex        =   79
               Top             =   180
               Width           =   6015
               _Version        =   65536
               _ExtentX        =   10610
               _ExtentY        =   609
               Caption         =   "AIM_ForecastPlanner.frx":14E8C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":14EFF
               Key             =   "AIM_ForecastPlanner.frx":14F1D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_Start 
               Height          =   345
               Index           =   1
               Left            =   1710
               TabIndex        =   93
               Top             =   270
               Width           =   1860
               DataFieldList   =   "StartDate"
               _Version        =   196617
               DataMode        =   2
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               RowHeight       =   423
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   -2147483643
               DataFieldToDisplay=   "StartDate"
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_End 
               DataField       =   "1"
               Height          =   345
               Index           =   1
               Left            =   1710
               TabIndex        =   96
               Top             =   630
               Width           =   1860
               DataFieldList   =   "EndDate"
               _Version        =   196617
               DataMode        =   2
               Cols            =   1
               BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   3281
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483630
               BackColor       =   -2147483643
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Reason"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   6750
               TabIndex        =   83
               Top             =   255
               Width           =   1215
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "End date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   150
               TabIndex        =   82
               Top             =   660
               Width           =   1395
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Adjustment"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   3750
               TabIndex        =   81
               Top             =   255
               Width           =   1455
            End
            Begin VB.Label lblAdj_Sum 
               Caption         =   "Start date"
               BeginProperty Font 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   150
               TabIndex        =   80
               Top             =   255
               Width           =   1395
            End
         End
         Begin ActiveToolBars.SSActiveToolBars tbAdjustments 
            Index           =   1
            Left            =   720
            Top             =   3480
            _ExtentX        =   582
            _ExtentY        =   582
            _Version        =   131083
            ToolBarsCount   =   1
            ToolsCount      =   6
            Style           =   0
            Enabled         =   0   'False
            Visible         =   0   'False
            Tools           =   "AIM_ForecastPlanner.frx":14F61
            ToolBars        =   "AIM_ForecastPlanner.frx":19BD4
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel8 
         Height          =   4950
         Left            =   30
         TabIndex        =   86
         Top             =   345
         Width           =   14415
         _ExtentX        =   25426
         _ExtentY        =   8731
         _Version        =   131083
         TabGuid         =   "AIM_ForecastPlanner.frx":19DB1
         Begin VB.Frame Frame2 
            Height          =   4875
            Left            =   120
            TabIndex        =   87
            Top             =   0
            Width           =   14055
            Begin TDBText6Ctl.TDBText txtSelected 
               Height          =   2655
               Left            =   240
               TabIndex        =   88
               TabStop         =   0   'False
               Top             =   720
               Width           =   4695
               _Version        =   65536
               _ExtentX        =   8281
               _ExtentY        =   4683
               Caption         =   "AIM_ForecastPlanner.frx":19DD9
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":19E45
               Key             =   "AIM_ForecastPlanner.frx":19E63
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   -1
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtNewFilters 
               Height          =   2655
               Left            =   5040
               TabIndex        =   89
               TabStop         =   0   'False
               Top             =   720
               Width           =   3735
               _Version        =   65536
               _ExtentX        =   6588
               _ExtentY        =   4683
               Caption         =   "AIM_ForecastPlanner.frx":19EA7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastPlanner.frx":19F13
               Key             =   "AIM_ForecastPlanner.frx":19F31
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   -1
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label1 
               Caption         =   "New filters (not included in original):"
               Height          =   300
               Left            =   5040
               TabIndex        =   91
               Top             =   360
               Width           =   3735
            End
            Begin VB.Label Label3 
               Caption         =   "All item filters (original + new):"
               Height          =   300
               Left            =   240
               TabIndex        =   90
               Top             =   360
               Width           =   3975
            End
         End
      End
   End
   Begin VB.Frame fraCommon 
      Height          =   4005
      Left            =   105
      TabIndex        =   0
      Top             =   122
      Width           =   14475
      Begin TDBText6Ctl.TDBText txtFcstComments 
         Height          =   705
         Left            =   9870
         TabIndex        =   10
         Top             =   210
         Visible         =   0   'False
         Width           =   4470
         _Version        =   65536
         _ExtentX        =   7885
         _ExtentY        =   1244
         Caption         =   "AIM_ForecastPlanner.frx":19F75
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastPlanner.frx":19FE8
         Key             =   "AIM_ForecastPlanner.frx":1A006
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBDate6Ctl.TDBDate txtFcstStartDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   2535
         TabIndex        =   4
         Top             =   580
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Calendar        =   "AIM_ForecastPlanner.frx":1A04A
         Caption         =   "AIM_ForecastPlanner.frx":1A162
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastPlanner.frx":1A1CE
         Keys            =   "AIM_ForecastPlanner.frx":1A1EC
         Spin            =   "AIM_ForecastPlanner.frx":1A24A
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   -1
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "__/__/____"
         ValidateMode    =   0
         ValueVT         =   1
         Value           =   36443
         CenturyMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtFcstPeriods_Future 
         Height          =   345
         Left            =   6810
         TabIndex        =   6
         Top             =   200
         Width           =   990
         _Version        =   65536
         _ExtentX        =   1746
         _ExtentY        =   609
         Calculator      =   "AIM_ForecastPlanner.frx":1A272
         Caption         =   "AIM_ForecastPlanner.frx":1A292
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastPlanner.frx":1A2FE
         Keys            =   "AIM_ForecastPlanner.frx":1A31C
         Spin            =   "AIM_ForecastPlanner.frx":1A366
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   0
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   255
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   1
         Separator       =   ""
         ShowContextMenu =   1
         ValueVT         =   1376257
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtFcstPeriods_Historical 
         Height          =   345
         Left            =   6810
         TabIndex        =   8
         Top             =   580
         Width           =   990
         _Version        =   65536
         _ExtentX        =   1746
         _ExtentY        =   609
         Calculator      =   "AIM_ForecastPlanner.frx":1A38E
         Caption         =   "AIM_ForecastPlanner.frx":1A3AE
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastPlanner.frx":1A41A
         Keys            =   "AIM_ForecastPlanner.frx":1A438
         Spin            =   "AIM_ForecastPlanner.frx":1A482
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   0
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   255
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   1
         Separator       =   ""
         ShowContextMenu =   1
         ValueVT         =   24641537
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcForecastID 
         Height          =   345
         Left            =   2550
         TabIndex        =   2
         Top             =   180
         Width           =   1590
         DataFieldList   =   "FcstID"
         ListAutoValidate=   0   'False
         ListAutoPosition=   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2805
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "FcstID"
      End
      Begin C1Chart2D8.Chart2D chtForecast 
         Height          =   2835
         Left            =   150
         TabIndex        =   11
         Top             =   1110
         Width           =   14115
         _Version        =   524288
         _Revision       =   7
         _ExtentX        =   24897
         _ExtentY        =   5001
         _StockProps     =   0
         ControlProperties=   "AIM_ForecastPlanner.frx":1A4AA
      End
      Begin VB.Label lblComments 
         Caption         =   "Comments"
         Height          =   300
         Left            =   8040
         TabIndex        =   9
         Top             =   240
         Visible         =   0   'False
         Width           =   1755
      End
      Begin VB.Label lblFcstPeriods_Future 
         Caption         =   "Future Periods"
         Enabled         =   0   'False
         Height          =   300
         Left            =   4680
         TabIndex        =   5
         Top             =   245
         Width           =   1995
      End
      Begin VB.Label lblFcstPeriods_Historical 
         Caption         =   "Historical Periods"
         Enabled         =   0   'False
         Height          =   300
         Left            =   4680
         TabIndex        =   7
         Top             =   625
         Width           =   1995
      End
      Begin VB.Label Label20 
         Caption         =   "Last updated"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   3
         Top             =   625
         Width           =   2325
      End
      Begin VB.Label Label8 
         Caption         =   "Forecast ID"
         Height          =   300
         Left            =   90
         TabIndex        =   1
         Top             =   210
         Width           =   2325
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&mnuEdit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&CopySelectedRows"
         Index           =   2
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&ExportToFile"
         Index           =   4
      End
   End
End
Attribute VB_Name = "AIM_ForecastPlanner"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2004 SSA Global. All rights reserved.
'*****************************************************************************
'
'   ForecastPlanner.frm
'
'   Version Number - 1.0
'   Last Updated   - 2004/05/01
'   Updated By     - Srinivas Uddanti
'
'   This replaces the former AIM_ForecastModification screen in
'    allowing creation and modification of forecast data.
'
'   The "forecast generator/modification" process is being morphed
'   into demand planning, hence the options required for forecast generator
'   are being phased out, and additional options for demand planning
'   are to be created, as of version 4.5
'   See related updates to AIM_ForecastModification.
'
'*****************************************************************************
' This file contains trade secrets of SSA Global. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of SSA Global.
'*****************************************************************************
Option Explicit
Option Base 1   'Sets default for all arrays on this page to start at 1

Dim Cn As ADODB.Connection

'Flags
Dim m_IsInitialLoad As Boolean      'User in formload to let know if the initial load is happning first time
Dim m_FirstTime As Boolean          'Set to true when it is opened the first time
Dim m_IsGridSummaryLoaded As Boolean    'Use to see if the summary grid has data or not
Dim m_IsGridItemLoaded As Boolean     'Use to see if the item grid has data or not
Dim m_IsChartVisible As Boolean
Dim m_MinCalcType As Integer  ' Used to track how many elements were selected in the options tab  'not used

'Modular Forecast Setup Criteria
Dim m_FcstId As String
Dim m_FcstKey As Long
Dim m_FcstSetupKey As Long
Dim m_FcstStoreKey_Main As Long         'Check
Dim m_FcstStartDate As Date
Dim m_FcstLocked As Boolean
Dim m_FcstEnabled As Boolean
Dim m_FcstHierarchy As Long             'Check
Dim m_FcstRolling As Boolean            'Check
Dim m_FcstHistory As Boolean
Dim m_ApplyTrend As Long                'Check
Dim m_FcstInterval As AIM_INTERVALS
Dim m_NewFcstInterval As AIM_INTERVALS
Dim m_FcstUnit As Integer
Dim m_RollingFcstStartDate As Date  'FcstStartDate Adjusted to Pd
Dim m_HistoricalStartDate As Date  ' Historical FcstStartdate
Dim m_FreezePdDate  As Date 'End of FreezePd
Dim m_RealFreezePdDate As Date ' End of FreezePd used to set the fields that can still have adjustments
                                ' This will be same as m_FreezePdDate except of fixed periods
Dim m_FuturePdEndDate As Date         'Future Pd End Date
Dim m_NbrPds_Hist As Integer          'Historical Pds
Dim m_NbrPds_Future As Integer        'Future Pds
Dim m_FixedPds As Integer              'Fixed Pds used by Sandbox only if m_fcstRolling is false
Dim m_TotalPeriods As Integer         'm_NbrPds_Hist +m_NbrPds_Future
Dim m_NewTotalPeriods As Integer      'Total Periods with m_NewFcstInterval
Dim m_FreezePds As Integer            ' FreezePds

'Current row bookmarks for the grids
Dim m_SumBookMark As Variant          'SummaryGrid row bookmark
Dim m_DtlBookMark As Variant          'DetailGrid row bookmark
'End Current row bookmarks for the grids

' Group by DropDown Constants
Private Const GB_ASSORT As String = "Item.Assort"
Private Const GB_BYID As String = "Item.ByID"
Private Const GB_CLASS1 As String = "Item.Class1"
Private Const GB_CLASS2 As String = "Item.Class2"
Private Const GB_CLASS3 As String = "Item.Class3"
Private Const GB_CLASS4 As String = "Item.Class4"
Private Const GB_ITEM As String = "Item.Item"
Private Const GB_LCID As String = "Item.LcID"
Private Const GB_LDIVISION As String = "AIMLocations.LDivision"
Private Const GB_LREGION As String = "AIMLocations.LRegion"
Private Const GB_LSTATUS As String = "AIMLocations.LStatus"
Private Const GB_LUSERDEFINED As String = "AIMLocations.LUserDefined"
Private Const GB_VELCODE As String = "Item.VelCode"
Private Const GB_VNID As String = "Item.VnID"


'SSTabs.Keys
Private Const TAB_SUMMARY As String = "FORECASTSUMMARY"
Private Const TAB_DETAIL As String = "FORECASTDETAIL"
Private Const TAB_ITEMDETAIL As String = "ITEMDETAIL"
Private Const TAB_OPTIONS As String = "OPTIONS"
Private Const TAB_FILTERS As String = "FILTERS"

'SSToolbars.ToolIDs
Private Const TB_ID_LOAD As String = "ID_LOAD"
Private Const TB_ID_CHART_SYNC As String = "ID_CHART_SYNC"
Private Const TB_ID_CHART_TOGGLESIZE As String = "ID_CHART_TOGGLESIZE"
Private Const TB_ID_ITEMFILTER As String = "ID_ITEMFILTER"
Private Const TB_ID_CLEARFILTER As String = "ID_CLEARFILTER"
Private Const TB_ID_RECALCCURRENT As String = "ID_RECALCCURRENT"
Private Const TB_ID_RUNFORECAST As String = "ID_RUNFORECAST"
Private Const TB_ID_FORECASTSETUP As String = "ID_FORECASTSETUP"
Private Const TB_ID_COPY As String = "ID_COPY"
Private Const TB_ID_SAVEFORECAST As String = "ID_SAVEFORECAST"
Private Const TB_ID_APPLY As String = "ID_APPLY"
Private Const TB_ID_CANCEL As String = "ID_CANCEL"
Private Const TB_ID_PROMOTE As String = "ID_PROMOTE"
Private Const TB_ID_CLOSE As String = "ID_CLOSE"
Private Const TB_ID_SETBOOKMARK As String = "ID_SETBOOKMARK"
Private Const TB_ID_GOTOBOOKMARK As String = "ID_GOTOBOOKMARK"


Private Const DT_SYSFCST As String = "SYSFCST"
Private Const DT_MASTERFCSTADJ As String = "MASTERFCSTADJ"
Private Const DT_FCSTADJ As String = "FCSTADJ"
Private Const DT_NETREQ As String = "NETREQ"
Private Const DT_ADJNETREQ As String = "ADJNETREQ"
Private Const DT_PROJINV As String = "PROJINV"
Private Const DT_HISDMD As String = "HISDMD"
Private Const DT_PRODCONST As String = "PRODCONST"


'XArrays Used to Sored Grid Data
Dim MainArray As New XArrayDB                   'Used to get raw data  for the fcst
Dim MainArrayFiltered As New XArrayDB           'Apply filter criteria for the MainArray
Dim SummaryArray As New XArrayDB                'Get the summary data from the MainArrayFiltered
Dim OverRideArray As New XArrayDB               'Used to store value for lcid,item,startdate,enddate that have override enabled
Dim ItemDetailArray As New XArrayDB             'Used to show item detail in the Itemdetail screen
Dim ItemAdjArray As New XArrayDB                'Used to store Adjustements for an lcid and item finally used in ItemDetailArray
Dim MainSubArray As New XArrayDB                'Used to temporarly store Filtered recalulated data.It is used to recalculate
                                                'A sub set of all data and eventually update the MainArray
Dim UserElementArray As New XArrayDB
                                   
Private Type UserElementRcd                     'Used to store Userelement Data
    SelOpt As Boolean
    UserElementId As String
    UserElementDesc As String
    Processed_YN As Boolean
    FcstType As String
End Type

'StoredProcedures and RecordSets Definitions
Dim AIM_UserElements_Get_Sp As New ADODB.Command
Dim AIM_RepositoryDetail_Validate_Sp As New ADODB.Command
Dim AIM_ForecastRepository_Save_Sp As New ADODB.Command
Dim AIM_RepositoryItemAdj_Get_Sp As New ADODB.Command
Dim AIM_DmdPlanFcstStoreAdjustLog_Save_Sp As New ADODB.Command
Dim AIM_RepositoryOverRide_Get_Sp As New ADODB.Command


Dim m_rsUserElements As New ADODB.Recordset
Dim m_rsOverRide As New ADODB.Recordset
Dim m_rsItemAdj As New ADODB.Recordset
Dim m_rsForecastList As ADODB.Recordset
Dim m_rsCompound As ADODB.Recordset
Dim m_rsAccess As ADODB.Recordset
Dim m_rsFcstMaint As New ADODB.Recordset

Dim prm As ADODB.Parameter

Dim m_UserElements() As UserElementRcd
Dim m_UESelected() As String                    'Used to store userelements selected for display in the grid
Dim m_DistItems() As String                     'Store distinct items ( based on group by criteria like Lcid,Item,Assort etc)
Dim m_FcstDates() As Date                       'Used to store Future Pd  FcstDates from m_RollingFcstStartDate  to m_NbrPds_Future
Dim m_FcstPdDates() As Date                     'Used to store all FcstDates from m_HistoricalStartDate to m_TotalPds
Dim m_FcstTypes() As String                     'Used to store the FcstTypes selected for display initally 'After the calculations
                                                'are done then UserElements are also added  just before populating the grid
Dim m_FcstAdjPos As Integer                      'The Element Pos of FcstAdj in the m_FcstTypes array
Dim m_CubePos As Integer                        'Col position of cube value in MainArray
Dim m_WeightPos As Integer                      'Col position of Weight value in MainArray
Dim m_PricePos As Integer                       'Col position of Price value in MainArray
Dim m_CostPos As Integer                        'Col position of Cost value in MainArray
Private Const m_DetailDataStartPos As Integer = 5     'Col position of starting dataelement in detail grid and MainArray,MainArrayFiltered,ItemDetailArray,
Private Const m_ItemDetailDataStartPos As Integer = 5
Private Const m_ItemDetailDataTypePos As Integer = 3
Private Const m_SummaryDataStartPos As Integer = 3
Dim m_DataEndPos As Integer                     'Col position of End data element
Private Const m_DetailTotalPos As Integer = 4   'Col position of Total field position in Detail Grid
'Dim AC As New AIMAdvCalendar

'Storage for tracking the mouse
Dim px As Long
Dim py As Long

'Storage for tracking the users' interactions
Dim Series As Long
Dim Pnt As Long
Dim Distance As Long
Dim Region As Long
Dim SaveSeries As Long
Dim SavePoint As Long
Dim ChartModify As Boolean

'Filter Criteria Variables
Dim m_xaOrigCriteria As XArrayDB
Dim m_xaAllCriteria As XArrayDB
Dim m_xaNewCriteria As XArrayDB

Private Const m_LcID_Pos As Integer = 0      '
Private Const m_Item_Pos As Integer = 1

'ItemDetail ColPositions

Dim m_DtlCol_ItDesc As Integer
Dim m_DtlCol_ItStat As Integer
Dim m_DtlCol_DataType As Integer
Dim m_DtlCol_DataDesc As Integer
Dim m_DtlCol_Totals As Integer
Dim m_DtlCol_FirstPeriod As Integer
Dim m_DtlCol_Class1 As Integer
Dim m_DtlCol_Class2 As Integer
Dim m_DtlCol_Class3 As Integer
Dim m_DtlCol_Class4 As Integer
Dim m_DtlCol_VelCode As Integer
Dim m_DtlCol_VnID As Integer
Dim m_DtlCol_Assort As Integer
Dim m_DtlCol_ByID As Integer
Dim m_DtlCol_LStatus As Integer
Dim m_DtlCol_LDivision As Integer
Dim m_DtlCol_LRegion As Integer
Dim m_DtlCol_LUserDefined As Integer
Dim m_DtlCol_ModifiedYN As Integer
Dim strMessage As String
Dim Lockflag As Boolean
Dim m_SaveUserElement As Boolean 'set to true if there is a userelement that has its data not calculated
Dim FcstTypeCount As Integer 'original fcsttype count


Private Function GetRepositoryKey() As Boolean
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    m_FcstKey = g_GetRepositoryKey(Cn, m_FcstId)
    If m_FcstKey = -1 Then
        With AIM_ForecastRepository_Save_Sp
            .Parameters("@UserID").Value = m_FcstId
            .Parameters("@FcstID").Value = m_FcstId
            .Parameters("@UserElement").Value = 0
            '.Parameters("@FcstID").Value = 0
        End With
        AIM_ForecastRepository_Save_Sp.Execute
        RtnCode = AIM_ForecastRepository_Save_Sp("@Return")
        m_FcstKey = AIM_ForecastRepository_Save_Sp("@RepositoryKey")

    End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.GetRepositoryKey)"
    f_HandleErr , , , "AIM_ForecastPlanner::GetRepositoryKey", Now, gDRGeneralError, True, Err
End Function


Private Sub atModification_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    Me.tbFcstModification.Redraw = False
    Screen.MousePointer = vbHourglass

    Select Case UCase$(NewTab.Key)
    Case TAB_SUMMARY
        mChart_Refresh TAB_SUMMARY
    Case TAB_DETAIL
        mChart_Refresh TAB_DETAIL
    Case TAB_ITEMDETAIL
        Call Populate_ItemDetailGrid
        SetEffective_Periods 3  ' value 3 is for ItemDetail tab
        mChart_Refresh TAB_ITEMDETAIL
    End Select
    Me.tbFcstModification.Redraw = True
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Me.tbFcstModification.Redraw = True
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(atModification_BeforeTabClick)"
    f_HandleErr , , , Me.Caption & "::atModification_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub chtForecast_DblClick()
On Error GoTo ErrorHandler

    Screen.MousePointer = vbHourglass
    
    Me.chtForecast.IsBatched = True 'Disable the control from repainting until the process has finished
    
    mChart_Refresh (UCase$(atModification.SelectedTab.Key))
    mChart_ToggleSize
    Me.chtForecast.IsBatched = False    'Process is done. Repaint control
    Screen.MousePointer = vbDefault
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbDefault
    Me.chtForecast.IsBatched = False
    'f_HandleErr Me.Caption & "(chtForecast_DblClick)"
    f_HandleErr , , , Me.Caption & "::chtForecast_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub chtForecast_ModifyEnd()
    ChartModify = False
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(chtForecast_ModifyEnd)"
     f_HandleErr , , , Me.Caption & "::chtForecast_ModifyEnd", Now, gDRGeneralError, True, Err
     
End Sub

Private Sub chtForecast_ModifyStart(IsOK As Boolean)
    ChartModify = True
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(chtForecast_ModifyStart)"
    f_HandleErr , , , Me.Caption & "::chtForecast_ModifyStart", Now, gDRGeneralError, True, Err
End Sub

Private Sub chtForecast_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        'Me.PopupMenu Me.mnuEditOpt
    
    Case vbLeftButton
    
        'Make sure we are not in being modified using the middle-mouse button
        If ChartModify Then Exit Sub

        px = X / Screen.TwipsPerPixelX
        py = Y / Screen.TwipsPerPixelY

        Region = chtForecast.ChartGroups(1).CoordToDataIndex(px, py, oc2dFocusXY, Series, Pnt, Distance)
        If Series > 0 Then
            SaveSeries = Series
            SavePoint = Pnt
        End If

    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(chtForecast_MouseDown)"
     f_HandleErr , , , Me.Caption & "::chtForecast_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub chtForecast_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
'Track the mouse movement and if over a bar, then make the markers visible on
' the bar in the series and point that the mouse is currently over.
'If the user has the left-mouse button pressed then change the value of the bar accordingly.

    'Make sure we are not in being modified using the middle-mouse button
    If ChartModify Then Exit Sub
    
    Dim vx As Double
    Dim vy As Double
    Dim PeriodDate As String
        
    px = X / Screen.TwipsPerPixelX
    py = Y / Screen.TwipsPerPixelY
    
    'If no buttons are pressed
    If Button = 0 Then
        'Find out where we are
        Region = chtForecast.ChartGroups(1).CoordToDataIndex(px, py, oc2dFocusXY, Series, Pnt, Distance)
        With chtForecast.ChartArea.Markers("X")
            If Distance <= 5 And Region = oc2dRegionInChartArea Then
                chtForecast.Footer.Text = getTranslationResource("Type") _
                    & ": " _
                    & chtForecast.ChartGroups(1).SeriesLabels(Series) _
                    & "  " _
                    & getTranslationResource("Period") _
                    & ": " _
                    & chtForecast.ChartArea.Axes("X").ValueLabels(Pnt).label _
                    & "  " _
                    & getTranslationResource("Value") _
                    & ": " _
                    & str(Format(Me.chtForecast.ChartGroups(1).Data.Y(Series, Pnt), "###0.###"))
               
                If .Index.Series <> Series Then
                    .Index.Series = Series
                End If
                
                If .Index.Point <> Pnt Then
                    .Index.Point = Pnt
                End If
                
                If .IsShowing <> True Then
                    .IsShowing = True
                End If
            Else
                .IsShowing = False
                chtForecast.Footer.Text = " "
            End If
        End With
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ctSeasons_MouseMove)"
     f_HandleErr , , , Me.Caption & "::ctSeasons_MouseMove", Now, gDRGeneralError, True, Err

End Sub
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Sets data values for the chart/graph
' .......................................................................
'   Returns false if the process failed
'   else true after redrawing the chart
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function mChart_Refresh(p_Selectedtab As String) As Long
On Error GoTo ErrorHandler
    Dim HasAccess As Boolean
    Dim GraphPeriod As Integer
    Dim ForecastTypes As Long
    Dim ForecastSort As String
    Dim ForecastItem As String
    Dim ForecastLcID As String
    Dim XArrayIndex As Long
    Dim ForecastIndex As Long
    Dim Period As Integer
    Dim PeriodDate As String
    
    'Check access
    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_Read)
    If HasAccess = False Then
        'Message here
        Exit Function
    End If

    If m_IsChartVisible = False Then Exit Function   'save some on performance by refreshing chart only when it is visible.

    'Disable the controls from repainting until the process has finished.
    Me.chtForecast.IsBatched = True
    Screen.MousePointer = vbHourglass

    With Me.chtForecast
        'Disable the control from repainting until the process has finished.
        .IsBatched = True
        .AllowUserChanges = False
        .ChartGroups(1).SeriesLabels.RemoveAll
        .ChartGroups(1).Styles.RemoveAll
        .ChartGroups(1).PointStyles.RemoveAll
        'Remove any old x-axis labels
        .ChartArea.Axes("X").ValueLabels.RemoveAll
        .ChartArea.Axes("Y").ValueLabels.RemoveAll
        .ChartGroups(1).Data.NumSeries = 0
        ForecastTypes = UBound(m_FcstTypes())
        ForecastSort = m_FcstTypes(0)
        ForecastIndex = 0
        
        Select Case p_Selectedtab
            
        Case TAB_SUMMARY
            If IsNull(dgSummary.Bookmark) Then Exit Function
            If SummaryArray.Count(1) >= UBound(m_FcstTypes) + 1 Then
                XArrayIndex = SummaryArray.Find(dgSummary.Bookmark, 1, ForecastSort, XORDER_DESCEND, , XTYPE_STRING)
                If XArrayIndex < 0 Then XArrayIndex = 0
                
                For ForecastIndex = 0 To ForecastTypes
                    'Retrieve All Forecast Types for one set of data.
                    GraphPeriod = 1
                    For Period = m_SummaryDataStartPos To m_SummaryDataStartPos + m_NewTotalPeriods - 1 ' data starts from column m_SummaryDataStartPos
                        .ChartGroups(1).Data.NumSeries = (ForecastIndex + 1)
                        .ChartGroups(1).Data.NumPoints(ForecastIndex + 1) = m_NewTotalPeriods
                        .ChartGroups(1).Data.Y((ForecastIndex + 1), GraphPeriod) = SummaryArray.Value(XArrayIndex, Period)
                        If ForecastIndex = 0 Then
                            PeriodDate = dgSummary.Columns(Period).Caption
                            .ChartArea.Axes("X").ValueLabels.Add GraphPeriod, PeriodDate
                            .ChartArea.Axes("X").ValueLabels(GraphPeriod).Value = GraphPeriod
                        End If
                        GraphPeriod = GraphPeriod + 1
                    Next Period
                    .ChartGroups(1).SeriesLabels.Add getTranslationResource(SummaryArray.Value(XArrayIndex, 1))
                    XArrayIndex = XArrayIndex + 1
                Next ForecastIndex
            End If
        Case TAB_DETAIL
            If IsNull(dgItem.Bookmark) Then Exit Function
            If MainArrayFiltered.Count(1) >= UBound(m_FcstTypes) + 1 Then
                  XArrayIndex = MainArrayFiltered.Find(dgItem.Bookmark, 3, ForecastSort, XORDER_DESCEND, , XTYPE_STRING)
                  If XArrayIndex < 0 Then XArrayIndex = 0
    
                For ForecastIndex = 0 To ForecastTypes
                    'Retrieve All Forecast Types for one set of data.
                    GraphPeriod = 1
                    For Period = m_DetailDataStartPos To m_DetailDataStartPos + m_NewTotalPeriods - 1 ' data starts from column m_DetailDataStartPos
                       .ChartGroups(1).Data.NumSeries = (ForecastIndex + 1)
                       .ChartGroups(1).Data.NumPoints(ForecastIndex + 1) = m_NewTotalPeriods
                       .ChartGroups(1).Data.Y((ForecastIndex + 1), GraphPeriod) = MainArrayFiltered.Value(XArrayIndex, Period)
                       If ForecastIndex = 0 Then
                            PeriodDate = dgItem.Columns(Period).Caption
                            .ChartArea.Axes("X").ValueLabels.Add GraphPeriod, PeriodDate
                            .ChartArea.Axes("X").ValueLabels(GraphPeriod).Value = GraphPeriod
                       End If
                       GraphPeriod = GraphPeriod + 1
                    Next Period
                    .ChartGroups(1).SeriesLabels.Add getTranslationResource(MainArrayFiltered.Value(XArrayIndex, 3))
                    XArrayIndex = XArrayIndex + 1
                 Next ForecastIndex
            End If
        Case "ITEMDETAIL"
            If IsNull(dgItemDetail.Bookmark) Then Exit Function
            If ItemDetailArray.Count(1) > 0 Then
                  XArrayIndex = ItemDetailArray.Find(dgItemDetail.Bookmark, 3, ForecastSort, XORDER_DESCEND, , XTYPE_STRING)
                  If XArrayIndex < 0 Then XArrayIndex = 0
    
                For ForecastIndex = 0 To ForecastTypes
                    'Retrieve All Forecast Types for one set of data.
                    GraphPeriod = 1
                    For Period = m_DetailDataStartPos To m_DetailDataStartPos + m_NewTotalPeriods - 1 ' data starts from column m_DetailDataStartPos
                       .ChartGroups(1).Data.NumSeries = (ForecastIndex + 1)
                       .ChartGroups(1).Data.NumPoints(ForecastIndex + 1) = m_NewTotalPeriods
                       .ChartGroups(1).Data.Y((ForecastIndex + 1), GraphPeriod) = ItemDetailArray.Value(XArrayIndex, Period)
                       If ForecastIndex = 0 Then
                            PeriodDate = dgItem.Columns(Period).Caption
                            .ChartArea.Axes("X").ValueLabels.Add GraphPeriod, PeriodDate
                            .ChartArea.Axes("X").ValueLabels(GraphPeriod).Value = GraphPeriod
                       End If
                       GraphPeriod = GraphPeriod + 1
                    Next Period
                    .ChartGroups(1).SeriesLabels.Add getTranslationResource(ItemDetailArray.Value(XArrayIndex, 3))
                    XArrayIndex = XArrayIndex + 1
                 Next ForecastIndex
            End If
        End Select
        .IsBatched = False
    End With

    'Process is done. Repaint controls
    Me.chtForecast.IsBatched = False
    Screen.MousePointer = vbNormal

Exit Function
ErrorHandler:
    'Process is done. Repaint controls
    Me.chtForecast.IsBatched = False
    Screen.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source, Err.Description & "(mChart_Refresh)"
     f_HandleErr , , , Me.Caption & "::mChart_Refresh", Now, gDRGeneralError, True, Err
End Function
Private Function Generate_Forecast() As Integer
On Error GoTo ErrorHandler

Dim Interval As AIM_INTERVALS
Dim PdEndDate As Date
Dim PdStartDate As Date
Dim RtnCode As Integer
'Populate_FcstDates
'RtnCode = -1   sri may-18-05
Interval = m_NewFcstInterval
MainArray.Clear
Fill_FcstTypes
Fill_UserElements
'Use original filters here
'RtnCode = CheckFcstData() ' now you can save even where the data is saved because we are checking the interval and
'fcsttypes  sri may-18-05
RtnCode = 0 'do not care about the return code
'save data only if interval is origal interval and all the orginal fcsttypes were selected
If m_FcstInterval = Interval And UBound(m_FcstTypes()) + 1 = FcstTypeCount Then
    If RtnCode = 0 And m_SaveUserElement Then
        'Save the changes to the repository and the userelemet
        RtnCode = GetForecastMaint(dcForecastID.Text, MainArray, m_FcstStartDate, Interval, m_xaOrigCriteria, m_FcstTypes(), m_UESelected(), True, True)
        
    ElseIf RtnCode = 0 And m_SaveUserElement = False Then
        'Save the changes to the repository and not the userelemet
        RtnCode = GetForecastMaint(dcForecastID.Text, MainArray, m_FcstStartDate, Interval, m_xaOrigCriteria, m_FcstTypes(), m_UESelected(), True, False)
        
    ElseIf m_SaveUserElement = True Then
        'No need to save changes to repository only the userelement need to be saved
        RtnCode = GetForecastMaint(dcForecastID.Text, MainArray, m_FcstStartDate, Interval, m_xaOrigCriteria, m_FcstTypes(), m_UESelected(), False, True)
    Else
        'No saving only calculating
        RtnCode = GetForecastMaint(dcForecastID.Text, MainArray, m_FcstStartDate, Interval, m_xaOrigCriteria, m_FcstTypes(), m_UESelected(), False, False)
    End If
Else
     'No saving only calculating
        RtnCode = GetForecastMaint(dcForecastID.Text, MainArray, m_FcstStartDate, Interval, m_xaOrigCriteria, m_FcstTypes(), m_UESelected(), False, False)
End If
Fill_FcstTypesAndUserElements

optFcstUnit.Item(0) = True  ' set the Fcst Units to Units

PopulateMainSummary 'This populates the summary col data

Create_Detail_Grid 'Creates the Detailgrid fields

FilterDetailArray MainArray, MainArrayFiltered  'Clears the  MainArrayFiltered
'and populates it with data from MainArray

If MainArrayFiltered.Count(1) > 0 Then
    m_IsGridItemLoaded = True
Else
    m_IsGridItemLoaded = False
End If

Set dgItem.Array = MainArrayFiltered
dgItem.ReBind
'Now populate the Summary Grid
Call Refresh_SummmaryGrid

'Populate OverRide Array
'Get the items that have over ride so that when we are applying the percentages ans addition
'we will not apply to these elements
If gFetch_OverRideItems() <> -1 Then
    m_rsOverRide.MoveFirst
    OverRideArray.Clear
    OverRideArray.LoadRows m_rsOverRide.GetRows()
End If
'Set the effective periods for Summary and Detail screens
SetEffective_Periods 1
SetEffective_Periods 2
RtnCode = 0
CleanUp:
Generate_Forecast = RtnCode
Exit Function

ErrorHandler:
    'f_HandleErr Me.Caption & "(Generate_Forecast_Click)"
     f_HandleErr , , , "AIM_ForecastPlanner::Generate_Forecast_Click", Now, gDRGeneralError, True, Err
    RtnCode = -1
    GoTo CleanUp
End Function

Private Function RollBack_Adjustments() As Integer
Dim RtnCode As Integer  '0 success -1 failure
'Check if data exists or not
If MainArrayFiltered.Count(1) = 0 Then
   strMessage = getTranslationResource("MSGBOX06900")
        If StrComp(strMessage, "MSGBOX06900") = 0 Then strMessage = "No data is available in detail screen"
        MsgBox strMessage, vbQuestion, Me.Caption
    Exit Function
End If
Select Case UCase$(atModification.SelectedTab.Key)
    Case TAB_SUMMARY, TAB_DETAIL
        RollBackAdjToFilteredArray MainArrayFiltered, MainArray
        Set dgItem.Array = MainArrayFiltered
        dgItem.ReBind
        Call Refresh_SummmaryGrid
    Case TAB_ITEMDETAIL
         Call Populate_ItemDetailGrid
        SetEffective_Periods 3  ' value 3 is for ItemDetail tab
End Select
RtnCode = 0
CleanUp:
RollBack_Adjustments = RtnCode
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.RollBack_Adjustments)"
     f_HandleErr , , , "AIM_ForecastPlanner::RollBack_Adjustments", Now, gDRGeneralError, True, Err
    RtnCode = -1
    GoTo CleanUp
End Function


Private Function PromoteTo_Master() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer '0 success -1 failure
    Dim FcstPdBegDate As Date
    Dim FcstPdEndDate As Date
    Dim strMessage, strMessage1, strMessage2, strMessage3 As String
    Select Case UCase$(atModification.SelectedTab.Key)
    Case TAB_SUMMARY
        FcstPdBegDate = CDate(CDate(dcDate_Start(0).Text))
        FcstPdEndDate = CDate(CDate(dcDate_End(0).Text))
    Case TAB_DETAIL
        FcstPdBegDate = CDate(CDate(dcDate_Start(1).Text))
        FcstPdEndDate = CDate(CDate(dcDate_End(1).Text))
    Case TAB_ITEMDETAIL
        FcstPdBegDate = CDate(CDate(dcDate_Start(2).Text))
        FcstPdEndDate = CDate(CDate(dcDate_End(2).Text))
    End Select
    
   
   strMessage = getTranslationResource("MSGBOX06903")
   If StrComp(strMessage, "MSGBOX06903") = 0 Then strMessage = "Items between the StartDate of "
   strMessage1 = getTranslationResource("MSGBOX06904")
   If StrComp(strMessage1, "MSGBOX06904") = 0 Then strMessage = "and End Date of"
   strMessage2 = getTranslationResource("MSGBOX06905")
   If StrComp(strMessage2, "MSGBOX06905") = 0 Then strMessage = "will be Promoted"
   strMessage3 = getTranslationResource("MSGBOX06906")
   If StrComp(strMessage3, "MSGBOX06906") = 0 Then strMessage = "Promote To Master!"
   
    RtnCode = MsgBox(strMessage & " " & CStr(FcstPdBegDate) & _
        " " & strMessage1 & " " & CStr(FcstPdEndDate) & " " & strMessage2, vbOKCancel, strMessage3)
    If RtnCode = 2 Then
        'the user canceled the promotion process
        Exit Function
    End If
    Screen.MousePointer = vbHourglass
    RtnCode = -1
    'Init. the command object
    RtnCode = g_PromoteToMaster(m_FcstId, m_xaAllCriteria, FcstPdBegDate, FcstPdEndDate)
    
CleanUp:
Screen.MousePointer = vbNormal
PromoteTo_Master = RtnCode
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.PromoteTo_Master)"
     f_HandleErr , , , "Aim_ForecastPlanner::PromoteTo_Master", Now, gDRGeneralError, True, Err
    RtnCode = -1
End Function

Private Function Refresh_SummmaryGrid()
 
 Dim SAVersion As Integer
 Dim SqlStmt As String
 Dim I As Long
 Dim J As Long
 Dim K As Long
 Dim L As Long
 Dim M As Long
 Dim II As Long   ' column index
 Dim jj As Long   ' row index
 Dim U_FcstId As String
 Dim Search_String As String
 Dim Sql1 As String
 Dim Sql2 As String
 Dim CompString As String
 Dim StartPd As Integer
 Dim EndPd As Integer
 Dim c1 As TrueOleDBGrid80.Column
 Dim PdEndDate As Date
 Dim PdStartDate As Date
 Dim WhereClause As String
 
 PdStartDate = m_HistoricalStartDate
 PdEndDate = m_HistoricalStartDate
 U_FcstId = dcForecastID.Text

    
    'Set the SQL Statement
    SqlStmt = SqlStmt + "Select VnId,Assort,LcId,Item,Class1,Class2,Class3,Class4,ById,ItStat, " & _
            " LDivision, LStatus, LRegion, LUserDefined, "
    SqlStmt = SqlStmt + "FcstStartDate,FcstInterval,ApplyTrend,FcstUnit,"
    SqlStmt = SqlStmt + " FcstPds_Future,FcstPds_Historical From AimFcstSetUp where FcstId = N'" & U_FcstId & "'"

    'Retrieve the data
    If f_IsRecordsetValidAndOpen(m_rsFcstMaint) Then m_rsFcstMaint.Close
    m_rsFcstMaint.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText

    If f_IsRecordsetOpenAndPopulated(m_rsFcstMaint) Then
        WhereClause = g_BuildWhereClause(m_xaAllCriteria)
        SAVersion = g_GetSAVersion(Cn)
        SqlStmt = g_QueryForFcstItems(False, WhereClause, SAVersion, False)
    End If

    m_rsFcstMaint.Close

    Sql1 = Mid(SqlStmt, InStr(SqlStmt, UCase("From")))
    Sql1 = Mid(Sql1, 1, InStr(Sql1, "ORDER BY") - 1)
    Sql2 = "select distinct(" + dcGroupBy.Text + ") "
    SqlStmt = Sql2 + Sql1
    SqlStmt = SqlStmt + " " + "ORDER BY " + dcGroupBy.Text

    m_rsFcstMaint.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
    If m_rsFcstMaint.RecordCount = 0 Then
    'This means the filter did not return an records
    SummaryArray.Clear
    SummaryArray.ReDim 0, 0, 0, MainArrayFiltered.UpperBound(2) - 10
    GoTo CreateSummaryGrid
    End If

      ReDim m_DistItems(0 To m_rsFcstMaint.RecordCount - 1)
      For I = 0 To m_rsFcstMaint.RecordCount - 1
            m_DistItems(I) = m_rsFcstMaint(0).Value
            m_rsFcstMaint.MoveNext
      Next
      m_rsFcstMaint.Close

SummaryArray.Clear
SummaryArray.ReDim 0, (UBound(m_DistItems()) + 1) * (UBound(m_FcstTypes) + 1) - 1, 0, MainArrayFiltered.UpperBound(2) - 10

L = 0
For I = 0 To UBound(m_DistItems)
    L = I * (UBound(m_FcstTypes) + 1)
    Search_String = m_DistItems(I)

    For K = 0 To MainArrayFiltered.UpperBound(1) Step UBound(m_FcstTypes) + 1
        If dcGroupBy.Text = GB_LCID Then
             CompString = MainArrayFiltered(K, 0)
            ElseIf dcGroupBy.Text = GB_ITEM Then
                 CompString = MainArrayFiltered(K, 1)
            ElseIf dcGroupBy.Text = GB_CLASS1 Then
                 CompString = MainArrayFiltered(K, m_DtlCol_Class1)
            ElseIf dcGroupBy.Text = GB_CLASS2 Then
                 CompString = MainArrayFiltered(K, m_DtlCol_Class2)
            ElseIf dcGroupBy.Text = GB_CLASS3 Then
                 CompString = MainArrayFiltered(K, m_DtlCol_Class3)
             ElseIf dcGroupBy.Text = GB_CLASS4 Then
                 CompString = MainArrayFiltered(K, m_DtlCol_Class4)
             ElseIf dcGroupBy.Text = GB_VELCODE Then
                 CompString = MainArrayFiltered(K, m_DtlCol_VelCode)
             ElseIf dcGroupBy.Text = GB_VNID Then
                 CompString = MainArrayFiltered(K, m_DtlCol_VnID)
            ElseIf dcGroupBy.Text = GB_ASSORT Then
                 CompString = MainArrayFiltered(K, m_DtlCol_Assort)
            ElseIf dcGroupBy.Text = GB_BYID Then
                 CompString = MainArrayFiltered(K, m_DtlCol_ByID)
            ElseIf dcGroupBy.Text = GB_LDIVISION Then
                 CompString = MainArrayFiltered(K, m_DtlCol_LDivision)
            ElseIf dcGroupBy.Text = GB_LSTATUS Then
                 CompString = MainArrayFiltered(K, m_DtlCol_LStatus)
            ElseIf dcGroupBy.Text = GB_LREGION Then
                 CompString = MainArrayFiltered(K, m_DtlCol_LRegion)
            ElseIf dcGroupBy.Text = GB_LUSERDEFINED Then
                 CompString = MainArrayFiltered(K, m_DtlCol_LUserDefined)
        End If
        'If MainArrayFiltered(k, 0) = m_DistItems(i) Then
        If CompString = m_DistItems(I) Then
            For M = 0 To UBound(m_FcstTypes)
                SummaryArray(L + M, 0) = m_DistItems(I)
                SummaryArray(L + M, 1) = MainArrayFiltered(K + M, 3)
                SummaryArray(L + M, 2) = SummaryArray(L + M, 2) + MainArrayFiltered(K + M, 4)
                For J = 1 To m_NewTotalPeriods
                    SummaryArray(L + M, 2 + J) = SummaryArray(L + M, 2 + J) + MainArrayFiltered(K + M, 4 + J)
                Next J
            Next M
        End If
    Next K
Next I
CreateSummaryGrid:
dgSummary.ClearFields
'Create the SummaryGrid
For II = 0 To m_NewTotalPeriods + 2
    If II <= m_NewTotalPeriods Then
        Set c1 = dgSummary.Columns.Add(II)
        c1.Visible = True
    End If
    If II >= m_SummaryDataStartPos And II < 4 + m_NewTotalPeriods Then
        If II <= m_NewTotalPeriods Then
            c1.Caption = CStr(m_FcstPdDates(II - m_SummaryDataStartPos))
            If CDate(c1.Caption) <= m_FreezePdDate Then
                c1.HeadBackColor = RGB(192, 192, 192)
            Else
                c1.HeadBackColor = vbWhite
            End If
        Else
            dgSummary.Columns(II).Caption = CStr(m_FcstPdDates(II - m_SummaryDataStartPos))
            If dgSummary.Columns(II).Caption <= m_FreezePdDate Then
               dgSummary.Columns(II).HeadBackColor = RGB(192, 192, 192)
            Else
                dgSummary.Columns(II).HeadBackColor = vbWhite
            End If
        End If
        PdStartDate = PdEndDate
    Else
        If II = 0 Then
            'Get the string seleced in the dcGroupBy filed
            c1.Caption = getTranslationResource(Mid(dcGroupBy.Text, InStr(dcGroupBy.Text, ".") + 1))
        ElseIf II = 1 Then
            c1.Caption = getTranslationResource("DataType")
        End If
    End If
Next
dgSummary.Columns(2).Caption = getTranslationResource("Summary")
'End of Creation of SummaryGrid

Create_Summary_Split

If SummaryArray.Count(1) >= UBound(m_FcstTypes) + 1 Then
    m_IsGridSummaryLoaded = True
Else
    m_IsGridSummaryLoaded = False
End If

Set dgSummary.Array = SummaryArray
dgSummary.ReBind
SetEffective_Periods 1
SetEffective_Periods 2
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Refresh_SummmaryGrid)"
     f_HandleErr , , , "Aim_ForecastPlanner::Refresh_SummaryGrid", Now, gDRGeneralError, True, Err
End Function

Private Function Filter_Forecast() As Boolean
'Check if data exists or not
Dim RtnCode As Boolean
RtnCode = False
If m_FirstTime = True Then
' this flag is usesed to suppress the  "No data is available which is showing when the screen
'is opened the first time
m_FirstTime = False
Exit Function
End If


If MainArrayFiltered.Count(1) = 0 Then
   strMessage = getTranslationResource("MSGBOX06900")
        If StrComp(strMessage, "MSGBOX06900") = 0 Then strMessage = "No data is available in detail screen"
        MsgBox strMessage, vbQuestion, Me.Caption
    Exit Function
End If
RtnCode = FilterDetailArray(MainArray, MainArrayFiltered)
Set dgItem.Array = MainArrayFiltered
dgItem.ReBind
dgItem.MoveFirst
Call Refresh_SummmaryGrid
Filter_Forecast = RtnCode
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Filter_Forecast)"
     f_HandleErr , , , "Aim_ForecastPlanner::Filter_Forecast", Now, gDRGeneralError, True, Err
End Function
Private Function Apply_Adjutments() As Boolean

Dim I As Long
Dim J As Long
Dim AdjStartIndex As Integer
Dim AdjEndIndex As Integer
Dim AdjMaxIndex As Integer

Screen.MousePointer = vbHourglass
'Check if data exists or not
If MainArrayFiltered.Count(1) = 0 Then
   strMessage = getTranslationResource("MSGBOX06900")
        If StrComp(strMessage, "MSGBOX06900") = 0 Then strMessage = "No data is available in detail screen"
        MsgBox strMessage, vbQuestion, Me.Caption
    Exit Function
End If
 Select Case UCase$(atModification.SelectedTab.Key)
    Case TAB_SUMMARY
        For I = m_SummaryDataStartPos To dgItem.Columns.Count - 1
            If IsDate(dgItem.Columns(I).Caption) Then
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_Start(0).Text) Then
                    AdjStartIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_End(0).Text) + 1 Then
                    AdjEndIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) Then
                    AdjMaxIndex = I
                End If
            End If
        Next
        If CDate(m_FcstDates(UBound(m_FcstDates()))) - 1 = CDate(dcDate_End(0).Text) Then
            If AdjEndIndex = 0 Then AdjEndIndex = AdjMaxIndex + 1
        End If

        'If CDbl(txtAdjQty_Smr.Text) <> 0 Then
            For I = MainArrayFiltered.LowerBound(1) To MainArrayFiltered.UpperBound(1) Step UBound(m_FcstTypes()) + 1
                For J = AdjStartIndex To AdjEndIndex - 1
                     If optAdjType_Sum(ADJ_UNITS).Value = True Then
                       'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            MainArrayFiltered(I + m_FcstAdjPos, J) = MainArrayFiltered(I + m_FcstAdjPos, J) + CDbl(txtAdjQty_Smr.Text)
                        'End If
                    ElseIf optAdjType_Sum(ADJ_percent).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            MainArrayFiltered(I + m_FcstAdjPos, J) = MainArrayFiltered(I + m_FcstAdjPos, J) + MainArrayFiltered(I, J) * CDbl(txtAdjQty_Smr.Text) / 100#
                        'End If
                    ElseIf optAdjType_Sum(ADJ_OVERRIDE).Value = True Then
                        MainArrayFiltered(I + m_FcstAdjPos, J) = txtAdjQty_Smr.Text
                    End If
                 Next
            Next
        'End If
        Set dgItem.Array = MainArrayFiltered
        dgItem.ReBind
        Call Refresh_SummmaryGrid
    Case TAB_DETAIL
       For I = m_DetailDataStartPos To dgItem.Columns.Count - 1
            If IsDate(dgItem.Columns(I).Caption) Then
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_Start(1).Text) Then
                    AdjStartIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_End(1).Text) + 1 Then
                    AdjEndIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) Then
                    AdjMaxIndex = I
                End If
            End If
        Next
        If CDate(m_FcstDates(UBound(m_FcstDates()))) - 1 = CDate(dcDate_End(1).Text) Then
            If AdjEndIndex = 0 Then AdjEndIndex = AdjMaxIndex + 1
        End If
        'If CDbl(txtAdjQty_Dtl.Text) <> 0 Then
            For I = MainArrayFiltered.LowerBound(1) To MainArrayFiltered.UpperBound(1) Step UBound(m_FcstTypes()) + 1
                For J = AdjStartIndex To AdjEndIndex - 1
                     If optAdjType_Dtl(ADJ_UNITS).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            MainArrayFiltered(I + m_FcstAdjPos, J) = MainArrayFiltered(I + m_FcstAdjPos, J) + CDbl(txtAdjQty_Dtl.Text)
                        'End If
                    ElseIf optAdjType_Dtl(ADJ_percent).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            MainArrayFiltered(I + m_FcstAdjPos, J) = MainArrayFiltered(I + m_FcstAdjPos, J) + MainArrayFiltered(I, J) * CDbl(txtAdjQty_Dtl.Text) / 100#
                       'End If
                    ElseIf optAdjType_Dtl(ADJ_OVERRIDE).Value = True Then
                        MainArrayFiltered(I + m_FcstAdjPos, J) = txtAdjQty_Dtl.Text
                    End If
                 Next
            Next
        'End If
        Set dgItem.Array = MainArrayFiltered
        dgItem.ReBind
        Call Refresh_SummmaryGrid
    Case TAB_ITEMDETAIL
        For I = m_DetailDataStartPos To dgItemDetail.Columns.Count - 1
            If IsDate(dgItemDetail.Columns(I).Caption) Then
                If CDate(dgItemDetail.Columns(I).Caption) = CDate(dcDate_Start(2).Text) Then
                    AdjStartIndex = I
                End If
                If CDate(dgItemDetail.Columns(I).Caption) = CDate(dcDate_End(2).Text) + 1 Then
                    AdjEndIndex = I
                End If
                If CDate(dgItemDetail.Columns(I).Caption) Then
                    AdjMaxIndex = I
                End If
            End If
        Next
        If CDate(m_FcstDates(UBound(m_FcstDates()))) - 1 = CDate(dcDate_End(2).Text) Then
            If AdjEndIndex = 0 Then AdjEndIndex = AdjMaxIndex + 1
        End If
        'If CDbl(txtAdjQty_Item.Text) <> 0 Then
                I = 0
                For J = AdjStartIndex To AdjEndIndex - 1
                     If optAdjType_Item(ADJ_UNITS).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            ItemDetailArray(I + m_FcstAdjPos, J) = ItemDetailArray(I + m_FcstAdjPos, J) + CDbl(txtAdjQty_Item.Text)
                        'End If
                    ElseIf optAdjType_Item(ADJ_percent).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            ItemDetailArray(I + m_FcstAdjPos, J) = ItemDetailArray(I + m_FcstAdjPos, J) + ItemDetailArray(I, J) * CDbl(txtAdjQty_Item.Text) / 100#
                       'End If
                    ElseIf optAdjType_Item(ADJ_OVERRIDE).Value = True Then
                        ItemDetailArray(I + m_FcstAdjPos, J) = txtAdjQty_Item.Text
                    End If
                 Next
        'End If
        Set dgItemDetail.Array = ItemDetailArray
        dgItemDetail.ReBind
    End Select
    Screen.MousePointer = vbNormal
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Apply_Adjutments)"
     f_HandleErr , , , "Aim_ForecastPlanner::Apply_Adjutments", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
End Function


Private Function Save_Adjustments() As Integer
Dim I As Long
Dim J As Long
Dim AdjStartIndex As Integer
Dim AdjEndIndex As Integer
Dim AdjMaxIndex As Integer
Dim AdjBegDate As Date
Dim AdjEndDate As Date
Dim RtnCode As Integer '0 success -1 failure

Screen.MousePointer = vbHourglass
RtnCode = -1
Select Case UCase$(atModification.SelectedTab.Key)
Case TAB_SUMMARY
        For I = m_SummaryDataStartPos To dgItem.Columns.Count - 1
            If IsDate(dgItem.Columns(I).Caption) Then
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_Start(0).Text) Then
                    AdjStartIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_End(0).Text) + 1 Then
                    AdjEndIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) Then
                    AdjMaxIndex = I
                End If
            End If
        Next
        If CDate(m_FcstDates(UBound(m_FcstDates()))) - 1 = CDate(dcDate_End(0).Text) Then
            If AdjEndIndex = 0 Then AdjEndIndex = AdjMaxIndex + 1
        End If
        If CDbl(txtAdjQty_Smr.Text) <> 0 Then
            For I = MainArrayFiltered.LowerBound(1) To MainArrayFiltered.UpperBound(1) Step UBound(m_FcstTypes()) + 1
                For J = AdjStartIndex To AdjEndIndex - 1
                            AdjBegDate = dgItem.Columns(J).Caption
                            If J = AdjEndIndex - 1 Then
                                AdjEndDate = CDate(dcDate_End(0).Text) + 1
                            Else
                                AdjEndDate = dgItem.Columns(J + 1).Caption
                            End If
                     If optAdjType_Sum(ADJ_UNITS).Value = True Then
                       'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                             RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_UNITS, CDbl(txtAdjQty_Smr.Text), 1, _
                                    txtAdjDesc_Smr.Text, gUserID)
                        'End If
                    ElseIf optAdjType_Sum(ADJ_percent).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            RtnCode = gSave_Adjustments( _
                                     dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_percent, CDbl(txtAdjQty_Smr.Text), 1, _
                                    txtAdjDesc_Smr.Text, gUserID)
                        'End If
                    ElseIf optAdjType_Sum(ADJ_OVERRIDE).Value = True Then
                        MainArrayFiltered(I + 1, J) = txtAdjQty_Smr.Text
                    End If
                 Next
            Next
            'Transfer the Adjustments made to adjustment rows from MainArrayFiltered to MainArray
            'So MainArray will have the Adjusted values
            ApplyAdjToDetailArray MainArrayFiltered, MainArray
        End If

Case TAB_DETAIL

    For I = m_DetailDataStartPos To dgItem.Columns.Count - 1
            If IsDate(dgItem.Columns(I).Caption) Then
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_Start(1).Text) Then
                    AdjStartIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) = CDate(dcDate_End(1).Text) + 1 Then
                    AdjEndIndex = I
                End If
                If CDate(dgItem.Columns(I).Caption) Then
                    AdjMaxIndex = I
                End If
            End If
        Next
        If CDate(m_FcstDates(UBound(m_FcstDates()))) - 1 = CDate(dcDate_End(1).Text) Then
            If AdjEndIndex = 0 Then AdjEndIndex = AdjMaxIndex + 1
        End If
        'If CDbl(txtAdjQty_Dtl.Text) <> 0 Then
            For I = MainArrayFiltered.LowerBound(1) To MainArrayFiltered.UpperBound(1) Step UBound(m_FcstTypes()) + 1
                For J = AdjStartIndex To AdjEndIndex - 1
                            AdjBegDate = dgItem.Columns(J).Caption
                            If J = AdjEndIndex - 1 Then
                                AdjEndDate = CDate(dcDate_End(1).Text) + 1
                            Else
                                AdjEndDate = dgItem.Columns(J + 1).Caption
                            End If
                     If optAdjType_Dtl(ADJ_UNITS).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            RtnCode = gSave_Adjustments( _
                                     dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_UNITS, CDbl(txtAdjQty_Dtl.Text), 0, _
                                    txtAdjDesc_Dtl.Text, gUserID)
                        'End If
                    ElseIf optAdjType_Dtl(ADJ_percent).Value = True Then
                       ' If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_percent, CDbl(txtAdjQty_Dtl.Text), 0, _
                                    txtAdjDesc_Dtl.Text, gUserID)
                       'End If
                    ElseIf optAdjType_Dtl(ADJ_OVERRIDE).Value = True Then
                        RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_OVERRIDE, CDbl(txtAdjQty_Dtl.Text), 1, _
                                    txtAdjDesc_Dtl.Text, gUserID)
                    ElseIf optAdjType_Dtl(ADJ_REMOVEOVERRIDE).Value = True Then
                        RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    0, 0, 0, _
                                    txtAdjDesc_Dtl.Text, gUserID)
                    ElseIf optAdjType_Dtl(ADJ_REMOVEMASTEROVERRIDE).Value = True Then
                        RtnCode = gSave_Adjustments( _
                                     dcForecastID.Text, 0, MainArrayFiltered(I, 0), MainArrayFiltered(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    0, 0, 2, _
                                    txtAdjDesc_Dtl.Text, gUserID)
                    End If
                 Next
            Next
            'Transfer the Adjustments made to adjustment rows from MainArrayFiltered to MainArray
            'So MainArray will have the Adjusted values
            ApplyAdjToDetailArray MainArrayFiltered, MainArray
        'End If
Case TAB_ITEMDETAIL
            
    For I = m_ItemDetailDataStartPos To dgItemDetail.Columns.Count - 1
            If IsDate(dgItemDetail.Columns(I).Caption) Then
                If CDate(dgItemDetail.Columns(I).Caption) = CDate(dcDate_Start(2).Text) Then
                    AdjStartIndex = I
                End If
                If CDate(dgItemDetail.Columns(I).Caption) = CDate(dcDate_End(2).Text) + 1 Then
                    AdjEndIndex = I
                End If
                If CDate(dgItemDetail.Columns(I).Caption) Then
                    AdjMaxIndex = I
                End If
            End If
        Next
        If CDate(m_FcstDates(UBound(m_FcstDates()))) - 1 = CDate(dcDate_End(2).Text) Then
            If AdjEndIndex = 0 Then AdjEndIndex = AdjMaxIndex + 1
        End If
        'If CDbl(txtAdjQty_Dtl.Text) <> 0 Then
            I = 0
                For J = AdjStartIndex To AdjEndIndex - 1
                            AdjBegDate = dgItemDetail.Columns(J).Caption
                            If J = AdjEndIndex - 1 Then
                                'AdjEndDate = CDate(dcAdjEnd_Item.Text) + 1
                                AdjEndDate = CDate(dcDate_End(2).Text) + 1
                            Else
                                AdjEndDate = dgItemDetail.Columns(J + 1).Caption
                            End If
                     If optAdjType_Item(ADJ_UNITS).Value = True Then
                        'If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then

                            RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, ItemDetailArray(I, 0), ItemDetailArray(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_UNITS, CDbl(txtAdjQty_Item.Text), 0, _
                                    txtAdjDesc_Item.Text, gUserID)
                        'End If
                    ElseIf optAdjType_Item(ADJ_percent).Value = True Then
                       ' If CheckOverRide(MainArrayFiltered(i, 0), MainArrayFiltered(i, 1), dgItem.Columns(j).Caption) = False Then
                            RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, ItemDetailArray(I, 0), ItemDetailArray(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_percent, CDbl(txtAdjQty_Item.Text), 0, _
                                    txtAdjDesc_Item.Text, gUserID)
                       'End If
                    ElseIf optAdjType_Item(ADJ_OVERRIDE).Value = True Then
                        RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, ItemDetailArray(I, 0), ItemDetailArray(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    ADJ_OVERRIDE, CDbl(txtAdjQty_Item.Text), 1, _
                                    txtAdjDesc_Item.Text, gUserID)
                    ElseIf optAdjType_Item(ADJ_REMOVEOVERRIDE).Value = True Then
                        RtnCode = gSave_Adjustments( _
                                    dcForecastID.Text, 0, ItemDetailArray(I, 0), ItemDetailArray(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    0, 0, 0, _
                                    txtAdjDesc_Item.Text, gUserID)
                    ElseIf optAdjType_Item(ADJ_REMOVEMASTEROVERRIDE).Value = True Then
                        RtnCode = gSave_Adjustments( _
                                     dcForecastID.Text, 0, ItemDetailArray(I, 0), ItemDetailArray(I, 1), _
                                    AdjBegDate, AdjEndDate - 1, _
                                    0, 0, 2, _
                                    txtAdjDesc_Item.Text, gUserID)
                    End If
                 Next
            'End If
            'Tranfer the Adjustments made to adjustment row from  ItemDetailArray to MainArrayFiltered
            ' and from MainArrayFiltered to MainArray So MainArray will have the Adjusted values
            ApplyAdjToDetailItemArray ItemDetailArray, MainArrayFiltered, MainArray
            Set dgItem.Array = MainArrayFiltered
            dgItem.ReBind
            Call Refresh_SummmaryGrid
            'Once save remove the reaon code
            
End Select
txtAdjDesc_Smr = ""
txtAdjDesc_Item = ""
txtAdjDesc_Dtl = ""
txtAdjQty_Smr = 0
txtAdjQty_Item = 0
txtAdjQty_Dtl = 0
RtnCode = 0
CleanUp:
    Screen.MousePointer = vbNormal
    Save_Adjustments = RtnCode
Exit Function
ErrorHandler:
    'f_HandleErr Me.Caption & "(Save_Adjustments)"
     f_HandleErr , , , "Aim_ForecastPlanner::Save_Adjustments", Now, gDRGeneralError, True, Err
    RtnCode = -1
    GoTo CleanUp
    
End Function


Private Function Populate_ItemDetailGrid() As Boolean
'Used to Populate the ItemDetail Grid depending on the row that was selected in the Item Grid
Dim c As TrueOleDBGrid80.Column
Dim I As Integer
Dim J As Integer
Dim DataTypeValue As String
Dim bmk As Variant
Dim LcId As String
Dim Item As String
Dim StartDate As Date
Dim StartPos As Integer
Dim FcstUnitPos As Integer
Dim UseUnitPos As Boolean
Dim FcstTypesCount As Integer
FcstTypesCount = UBound(m_FcstTypes())
dgItemDetail.ClearFields
If m_IsGridItemLoaded = False Then Exit Function
'There will be default of two columns already for the grid created so no need to create them
'Make the captions for all columns in ItemDetailGrid same as In ItemGrid
dgItemDetail.Columns(0).Caption = dgItem.Columns(0).Caption
dgItemDetail.Columns(1).Caption = dgItem.Columns(1).Caption

For I = 2 To dgItem.Columns.Count - 1
    Set c = dgItemDetail.Columns.Add(I)
    c.Visible = True
    c.Caption = dgItem.Columns(I).Caption
Next
'Now Populate the selected item values in the detail screen
'Figure out what item was selected
If MainArrayFiltered.Count(1) > 0 Then
If IsNull(dgItem.GetBookmark(0)) Then Exit Function
bmk = dgItem.GetBookmark(0)
'Find out what is the datatype for the selected row
DataTypeValue = MainArrayFiltered.Value(bmk, 3)      '3th column in datatype field
'Only the first row for each item lcid combination will have values
'If the user is not on the first row then go a row back each time till we go to the row that
'has LcId and Item fields values this is done by comparing the datatypevalue with
'the firstFcstElement

If UCase(DataTypeValue) <> m_FcstTypes(0) Then
    For I = 1 To FcstTypesCount
        bmk = dgItem.GetBookmark(-I)
        DataTypeValue = MainArrayFiltered.Value(bmk, m_ItemDetailDataTypePos)
        If UCase(DataTypeValue) = m_FcstTypes(0) Then
            LcId = MainArrayFiltered.Value(bmk, 0)
            Item = MainArrayFiltered.Value(bmk, 1)
            Exit For
        End If
    Next
Else
    LcId = MainArrayFiltered.Value(bmk, m_LcID_Pos)
    Item = MainArrayFiltered.Value(bmk, m_Item_Pos)
End If

ItemDetailArray.Clear
ItemDetailArray.ReDim 0, FcstTypesCount, 0, MainArrayFiltered.UpperBound(2)
ItemDetailArray.AppendRows (6) 'For six types of adjustments


For I = bmk To bmk + FcstTypesCount + 6
    For J = 0 To MainArrayFiltered.UpperBound(2)
    If I <= bmk + FcstTypesCount Then
        ItemDetailArray(I - bmk, J) = MainArrayFiltered(I, J)
    End If
    If J >= m_DetailDataStartPos And J <= m_DetailDataStartPos + m_NewTotalPeriods - 1 And I > bmk + FcstTypesCount Then
        ItemDetailArray(I - bmk, J) = 0 ' Populate the QtyAdjust, PctAdjust, MasterQtyAdjust.and MasterPctAdjust fields to 0 initially
        If I - bmk = FcstTypesCount + 3 Then ItemDetailArray(I - bmk, J) = "NULL" 'populate the QtyOverRide fields to null initally
        If I - bmk = FcstTypesCount + 6 Then ItemDetailArray(I - bmk, J) = "NULL" 'populate the MasterQtyOverRide fields to null initally
    End If
    Next
Next

'Set the  Ajustment types field or Datatype filed names
ItemDetailArray(FcstTypesCount + 1, 3) = getTranslationResource("QtyAdjust")
ItemDetailArray(FcstTypesCount + 2, 3) = getTranslationResource("PctAdjust")
ItemDetailArray(FcstTypesCount + 3, 3) = getTranslationResource("QtyOverRide")
ItemDetailArray(FcstTypesCount + 4, 3) = getTranslationResource("MasterQtyAdjust")
ItemDetailArray(FcstTypesCount + 5, 3) = getTranslationResource("MasterPctAdjust")
ItemDetailArray(FcstTypesCount + 6, 3) = getTranslationResource("MasterQtyOverRide")


If optFcstUnit.Item(0) = True Then
    UseUnitPos = False
ElseIf optFcstUnit.Item(1) = True Then
    UseUnitPos = True
    FcstUnitPos = m_CostPos
ElseIf optFcstUnit.Item(2) = True Then
    UseUnitPos = True
    FcstUnitPos = m_WeightPos
ElseIf optFcstUnit.Item(3) = True Then
    UseUnitPos = True
    FcstUnitPos = m_CubePos
ElseIf optFcstUnit.Item(4) = True Then
    UseUnitPos = True
    FcstUnitPos = m_PricePos
End If

'Get the Adjustments for this item from the database

If gFetch_ItemAdjustments(dcForecastID.Text, LcId, Item, m_rsItemAdj) > 0 Then
'If there is data in Adjlog  for qty and percent adjustement do the following
If m_rsItemAdj.BOF = False And m_rsItemAdj.eof = False Then
    'm_rsItemAdj.MoveFirst
    
    ItemAdjArray.Clear
    ItemAdjArray.LoadRows m_rsItemAdj.GetRows()


    If ItemAdjArray.Count(1) = 0 Then
        'Do nothing there are no adjustments in the database
    Else
        'If there are adjustments the populate them at the corresponding place
        For I = 0 To ItemAdjArray.UpperBound(1)
            StartDate = CDate(ItemAdjArray(I, 0))
            For J = m_ItemDetailDataStartPos To m_ItemDetailDataStartPos + m_NewTotalPeriods - 1
                If StartDate = CDate(dgItemDetail.Columns(J).Caption) Then
                    If UseUnitPos = False Then
                        ItemDetailArray(FcstTypesCount + 1, J) = ItemAdjArray(I, 1)
                        ItemDetailArray(FcstTypesCount + 2, J) = ItemAdjArray(I, 2)
                        'ItemDetailArray(FcstTypesCount + 3, j) = IIf(IsNull(ItemAdjArray(I, 3)), "NULL", ItemAdjArray(I, 3))
                    Else
                    ItemDetailArray(FcstTypesCount + 1, J) = ItemAdjArray(I, 1) * MainArrayFiltered(0, FcstUnitPos)
                    ItemDetailArray(FcstTypesCount + 2, J) = ItemAdjArray(I, 2)
                    'ItemDetailArray(FcstTypesCount + 3, j) = IIf(IsNull(ItemAdjArray(I, 3)), "NULL", ItemAdjArray(I, 3) * MainArrayFiltered(0, FcstUnitPos))
                    End If
                End If
            Next
    
        Next
    End If
End If
End If
'Get Data from Master Table
Set m_rsItemAdj = m_rsItemAdj.NextRecordset
If (f_IsRecordsetValidAndOpen(m_rsItemAdj)) Then
'If there is data in adj log table  for override do the following
If m_rsItemAdj.BOF = False And m_rsItemAdj.eof = False Then
    'm_rsItemAdj.MoveFirst
    ItemAdjArray.Clear
    ItemAdjArray.LoadRows m_rsItemAdj.GetRows()
    
        If ItemAdjArray.Count(1) = 0 Then
            'Do nothing
        Else
            For I = 0 To ItemAdjArray.UpperBound(1)
                StartDate = CDate(ItemAdjArray(I, 0))
                For J = m_ItemDetailDataStartPos To m_ItemDetailDataStartPos + m_NewTotalPeriods - 1
                    If StartDate = CDate(dgItemDetail.Columns(J).Caption) Then
                        If UseUnitPos = False Then
                            If IsNull(ItemAdjArray(I, 1)) Then
                                ItemDetailArray(FcstTypesCount + 3, J) = "NULL"
                            ElseIf ItemAdjArray(I, 2) = 0 Then
                                'overrideenabled is false
                                ItemDetailArray(FcstTypesCount + 3, J) = "NULL"
                            Else
                                ItemDetailArray(FcstTypesCount + 3, J) = ItemAdjArray(I, 1)
                            End If
                        Else
                            If IsNull(ItemAdjArray(I, 1)) Then
                                ItemDetailArray(FcstTypesCount + 3, J) = "NULL"
                            ElseIf ItemAdjArray(I, 2) = 0 Then
                                'overrideenabled is false
                                ItemDetailArray(FcstTypesCount + 3, J) = "NULL"
                            Else
                            ItemDetailArray(FcstTypesCount + 3, J) = ItemAdjArray(I, 1) * MainArrayFiltered(0, FcstUnitPos)
                            End If
                        End If
                    End If
                Next
            Next
        End If
End If
End If

Set m_rsItemAdj = m_rsItemAdj.NextRecordset
'IF there is data in the master table do the following
If (f_IsRecordsetValidAndOpen(m_rsItemAdj)) Then
If m_rsItemAdj.BOF = False And m_rsItemAdj.eof = False Then
   ' m_rsItemAdj.MoveFirst
    ItemAdjArray.Clear
    ItemAdjArray.LoadRows m_rsItemAdj.GetRows()
    
        If ItemAdjArray.Count(1) = 0 Then
            'Do nothing
        Else
            For I = 0 To ItemAdjArray.UpperBound(1)
                StartDate = CDate(ItemAdjArray(I, 0))
                For J = m_ItemDetailDataStartPos To m_ItemDetailDataStartPos + m_NewTotalPeriods - 1
                    If StartDate = CDate(dgItemDetail.Columns(J).Caption) Then
                        If UseUnitPos = False Then
                            If ItemAdjArray(I, 4) = 0 Then
                                ItemDetailArray(FcstTypesCount + 4, J) = ItemAdjArray(I, 1)
                                ItemDetailArray(FcstTypesCount + 5, J) = ItemAdjArray(I, 2)
                                ItemDetailArray(FcstTypesCount + 6, J) = "NULL"
                            Else
                                ItemDetailArray(FcstTypesCount + 4, J) = 0
                                ItemDetailArray(FcstTypesCount + 5, J) = 0
                                ItemDetailArray(FcstTypesCount + 6, J) = ItemAdjArray(I, 3)
                            End If
                        Else
                            If ItemAdjArray(I, 4) = 0 Then
                                ItemDetailArray(FcstTypesCount + 4, J) = ItemAdjArray(I, 1) * MainArrayFiltered(0, FcstUnitPos)
                                ItemDetailArray(FcstTypesCount + 5, J) = ItemAdjArray(I, 2)
                                ItemDetailArray(FcstTypesCount + 6, J) = "NULL"
                            Else
                                ItemDetailArray(FcstTypesCount + 4, J) = 0
                                ItemDetailArray(FcstTypesCount + 5, J) = 0
                                ItemDetailArray(FcstTypesCount + 6, J) = ItemAdjArray(I, 3) * MainArrayFiltered(0, FcstUnitPos)
                            End If
                        End If
                    End If
                Next
            Next
        End If
End If
End If

Set dgItemDetail.Array = ItemDetailArray
dgItemDetail.ReBind
Create_ItemDetail_Split
End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Populate_ItemDetailGrid)"
    f_HandleErr , , , "Aim_ForecastPlanner::Populate_ItemDetailGrid", Now, gDRGeneralError, True, Err
End Function
Private Function gFetch_ItemAdjustments( _
    p_FcstID As String, _
    p_LcID As String, _
    p_Item As String, _
    r_rsItemAdj As ADODB.Recordset _
 ) As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    'Init. the command object
    With AIM_RepositoryItemAdj_Get_Sp
    'Set values
        .Parameters("@FcstId").Value = p_FcstID
        .Parameters("@Lcid").Value = p_LcID
        .Parameters("@Item").Value = p_Item
    End With

    'Clear out any existing data
    If f_IsRecordsetValidAndOpen(r_rsItemAdj) Then r_rsItemAdj.Close
    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
    r_rsItemAdj.Open AIM_RepositoryItemAdj_Get_Sp
    RtnCode = AIM_RepositoryItemAdj_Get_Sp.Parameters(0).Value

CleanUp:
    If f_IsRecordsetOpenAndPopulated(r_rsItemAdj) Then
        RtnCode = 1
    Else
        RtnCode = -1
    End If
    gFetch_ItemAdjustments = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_ItemAdjustments)"
    f_HandleErr , , , "Aim_ForecastPlanner::gFetch_ItemAdjustments", Now, gDRGeneralError, True, Err
End Function

Private Function SaveTo_Repository() As Integer
'We should not allow user to save data from the front end since the data once modified will
'not correct
'This function should be modifed if at all it need to be used
Dim RepositoryKey As Integer
Dim LcId  As String
Dim Item          As String
Dim FcstPds       As Integer
Dim FcstPdBegDate   As Date
Dim FcstPdEndDate As Date
Dim FcstNetReq    As Long
Dim FcstAdjNetReq As Long
Dim QtyActualOrdered  As Long
Dim QtyActualShipped As Long
Dim QtyProjectedInventory As Long
Dim I As Long
Dim J As Long
Dim K As Integer
Dim FcstType As String
Dim AIM_RepositoryDetail_Save_Sp As ADODB.Command
Dim MasterFcstAdj As Boolean
Dim FcstAdj As Boolean
Dim AdjNetReq As Boolean
Dim ProjInv As Boolean
Dim UpdateCode As String
Dim RtnCode As Integer  '0 success -1 failure
Screen.MousePointer = vbHourglass
RtnCode = -1
For I = 0 To UBound(m_FcstTypes())
   If m_FcstTypes(I) = "MASTERFCSTADJ" Then
        MasterFcstAdj = True
    ElseIf m_FcstTypes(I) = "FCSTADJ" Then
        FcstAdj = True
    ElseIf m_FcstTypes(I) = "ADJNETREQ" Then
        AdjNetReq = True
    ElseIf m_FcstTypes(I) = "PROJINV" Then
        ProjInv = True
    End If
Next I

'Use then follwing codes
'1 for MasterFcstAdj
'2 for FcstAdj
'3 for AdjNetReq
'4 fro ProjInv

If MasterFcstAdj = True And FcstAdj = True And AdjNetReq = True And ProjInv = True Then
UpdateCode = "1234"
ElseIf MasterFcstAdj = True And FcstAdj = True And AdjNetReq = True And ProjInv = False Then
    UpdateCode = "123"
ElseIf MasterFcstAdj = True And FcstAdj = True And AdjNetReq = False And ProjInv = True Then
    UpdateCode = "124"
ElseIf MasterFcstAdj = True And FcstAdj = False And AdjNetReq = True And ProjInv = True Then
    UpdateCode = "134"
ElseIf MasterFcstAdj = False And FcstAdj = True And AdjNetReq = True And ProjInv = True Then
    UpdateCode = "234"
ElseIf MasterFcstAdj = True And FcstAdj = True And AdjNetReq = False And ProjInv = False Then
    UpdateCode = "12"
ElseIf MasterFcstAdj = True And FcstAdj = False And AdjNetReq = True And ProjInv = False Then
    UpdateCode = "13"
    ElseIf MasterFcstAdj = True And FcstAdj = False And AdjNetReq = False And ProjInv = True Then
    UpdateCode = "14"
ElseIf MasterFcstAdj = False And FcstAdj = True And AdjNetReq = True And ProjInv = False Then
    UpdateCode = "23"
ElseIf MasterFcstAdj = False And FcstAdj = True And AdjNetReq = False And ProjInv = True Then
    UpdateCode = "24"
ElseIf MasterFcstAdj = False And FcstAdj = False And AdjNetReq = True And ProjInv = True Then
    UpdateCode = "34"
ElseIf MasterFcstAdj = True And FcstAdj = False And AdjNetReq = False And ProjInv = False Then
    UpdateCode = "1"
ElseIf MasterFcstAdj = False And FcstAdj = True And AdjNetReq = False And ProjInv = False Then
    UpdateCode = "2"
ElseIf MasterFcstAdj = False And FcstAdj = False And AdjNetReq = True And ProjInv = False Then
    UpdateCode = "3"
ElseIf MasterFcstAdj = False And FcstAdj = False And AdjNetReq = False And ProjInv = True Then
    UpdateCode = "4"
End If

Set AIM_RepositoryDetail_Save_Sp = New ADODB.Command
    With AIM_RepositoryDetail_Save_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RepositoryDetail_Save_Sp"
        .Parameters.Refresh
    End With

If MainArrayFiltered.Count(1) <> 0 Then
'Assumes only 4 types of fcst are there in the detail first is Fcst second is FcstAdj
'Third is FcstNetReq and fourth is FcstAdjNetReq so the step is 4
'The others QtyActualOrdered,QtyActualSHipped and QtyProjectedInventory is set to zero
'The data starts from the 14 th column
'So 14 +NbrpdsHist gives the first current pd value
'J is used to move from one period to another period

    For I = 0 To MainArrayFiltered.UpperBound(1) Step UBound(m_FcstTypes()) + 1

    LcId = MainArrayFiltered(I, 0)
    Item = MainArrayFiltered(I, 1)
    For J = 0 To m_NbrPds_Future - m_FreezePds - 1

       ' FcstPdStartDate = CDate(dgItem.Columns(13 + j).Caption)
       ' QtyFcst = MainArrayFiltered(i, 13 + j)
       ' QtyAdjust = 10


        With AIM_RepositoryDetail_Save_Sp
                            .Parameters("@RepositoryKey") = m_FcstKey
                            .Parameters("@LcID") = LcId
                            .Parameters("@item") = Item
                            .Parameters("@FcstType") = UpdateCode
                            .Parameters("@FcstPdBegDate") = m_FcstDates(J)
                            .Parameters("@FcstPdEndDate") = m_FcstDates(J + 1) - 1
                            .Parameters("@Qty_1") = 0
                            .Parameters("@Qty_2") = 0
                            .Parameters("@Qty_3") = 0
                            .Parameters("@Qty_4") = 0
                    
                            For K = 0 To UBound(m_FcstTypes())
                                If MasterFcstAdj = True And m_FcstTypes(K) = "MASTERFCSTADJ" Then
                                    .Parameters("@Qty_1") = MainArrayFiltered(I + K, m_DetailDataStartPos + m_NbrPds_Hist + J)
                                ElseIf FcstAdj = True And m_FcstTypes(K) = "FCSTADJ" Then
                                    .Parameters("@Qty_2") = MainArrayFiltered(I + K, m_DetailDataStartPos + m_NbrPds_Hist + J)
                                ElseIf AdjNetReq = True And m_FcstTypes(K) = "ADJNETREQ" Then
                                    .Parameters("@Qty_3") = MainArrayFiltered(I + K, m_DetailDataStartPos + m_NbrPds_Hist + J)
                                ElseIf ProjInv = True And m_FcstTypes(K) = "PROJINV" Then
                                    .Parameters("@Qty_4") = MainArrayFiltered(I + K, m_DetailDataStartPos + m_NbrPds_Hist + J)
                                End If
                            Next K
                
                        .Execute
    End With

    Next

    Next
End If
RtnCode = 0 'Success
CleanUp:
If Not (AIM_RepositoryDetail_Save_Sp Is Nothing) Then Set AIM_RepositoryDetail_Save_Sp.ActiveConnection = Nothing
Set AIM_RepositoryDetail_Save_Sp = Nothing
Screen.MousePointer = vbNormal
SaveTo_Repository = RtnCode
Exit Function
ErrorHandler:
    'f_HandleErr Me.Caption & "(FcstPlanner.SaveTo_Repository)"
    f_HandleErr , , , "Aim_ForecastPlanner::SaveTo_Repository", Now, gDRGeneralError, True, Err
    RtnCode = -1
    GoTo CleanUp
End Function
Private Function GetData_FromRepository() As Boolean
Screen.MousePointer = vbHourglass
Dim RtnCode As Integer
Create_Detail_Grid
MainArray.Clear
Fill_FcstTypes
Fill_UserElements
'This function will get the data from the tables directly with out much calculations
'for the fcsttypes selected and the userelements selected

RtnCode = GenerateBatchFcst(dcForecastID.Text, m_FcstTypes(), MainArray, m_FcstSetupKey, m_UESelected())
If RtnCode = -1 Then
strMessage = getTranslationResource("MSGBOX06901")
        If StrComp(strMessage, "MSGBOX06901") = 0 Then strMessage = "No data is  availabe for the forecast ID"
        MsgBox strMessage, vbQuestion, Me.Caption
Exit Function
End If
'After we run the above function now populate the fcsttyes array with the userelemnets selected
Fill_FcstTypesAndUserElements

FilterDetailArray MainArray, MainArrayFiltered

'Set dgItem.Array = MainArray
'dgItem.ReBind
If MainArrayFiltered.Count(1) > 0 Then
    m_IsGridItemLoaded = True
Else
    m_IsGridItemLoaded = False
    Exit Function
End If
optFcstUnit.Item(0) = True
Set dgItem.Array = MainArrayFiltered
dgItem.ReBind
Call Refresh_SummmaryGrid

'Populate OverRide Array
If gFetch_OverRideItems() <> -1 Then
    m_rsOverRide.MoveFirst
    OverRideArray.Clear
    OverRideArray.LoadRows m_rsOverRide.GetRows()
End If
Screen.MousePointer = vbNormal
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.GetData_FromRepository)"
     f_HandleErr , , , "Aim_ForecastPlanner::GetData_FromRepository", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
End Function
Private Function Generate_ForecastForFilteredItems() As Boolean
'Check if data exists or not
If MainArray.Count(1) = 0 Then
   strMessage = getTranslationResource("MSGBOX06900")
    If StrComp(strMessage, "MSGBOX06900") = 0 Then strMessage = "No data is available in detail screen"
    MsgBox strMessage, vbQuestion, Me.Caption
    Exit Function
End If
Screen.MousePointer = vbHourglass
optFcstUnit.Item(0) = True  ' set the Fcst Units to Units
Fill_FcstTypes
Fill_UserElements
'Use user filters here
GetForecastMaint dcForecastID.Text, MainSubArray, m_FcstStartDate, m_NewFcstInterval, m_xaAllCriteria, m_FcstTypes(), m_UESelected(), False, False
Fill_FcstTypesAndUserElements
ApplyRecalcDataToDetail MainSubArray, MainArray
FilterDetailArray MainArray, MainArrayFiltered
Set dgItem.Array = MainArrayFiltered
dgItem.ReBind
Call Refresh_SummmaryGrid
MainSubArray.ReDim 0, 0, 0, 0
Screen.MousePointer = vbNormal
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.Generate_ForecastForFilteredItems)"
     f_HandleErr , , , "Aim_ForecastPlanner::Generate_ForecastForFilteredItems", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
End Function

Private Sub ckDataCalc_Type_Click(Index As Integer)
    If Index = 2 Then
        If Me.ckDataCalc_Type(2).Value = vbUnchecked Then
            Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
            Me.tbFcstModification.Tools(TB_ID_APPLY).Enabled = False
            Me.tbFcstModification.Tools(TB_ID_CANCEL).Enabled = False
        Else
            Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
            Me.tbFcstModification.Tools(TB_ID_APPLY).Enabled = True
            Me.tbFcstModification.Tools(TB_ID_CANCEL).Enabled = False
        End If
    End If
End Sub

Private Sub dcDate_End_Change(Index As Integer)
Dim EndDate As String
Dim I As Integer
EndDate = dcDate_End(Index).Text
If Not IsDate(EndDate) Then Exit Sub
For I = 0 To 2
    dcDate_End(I).Text = EndDate
Next
SetEffective_Periods 2  ' value 2 is for Dtl tab
SetEffective_Periods 3  ' value 3 is for Item tab
SetEffective_Periods 1  ' value 1 is for Summary tab
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_End_Click)"
    f_HandleErr , , , Me.Caption & "::dcDate_End_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDate_End_Click(Index As Integer)
On Error GoTo ErrorHandler
    dcDate_End_Change (Index)
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_End_Click)"
    f_HandleErr , , , Me.Caption & "::dcDate_End_Click", Now, gDRGeneralError, True, Err
End Sub


Private Sub dcDate_End_InitColumnProps(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    dcDate_End(Index).Redraw = False  'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbHourglass

    dcDate_End(Index).Columns(0).Caption = getTranslationResource("EndDate")
    dcDate_End(Index).Columns(0).Width = 1500
    dcDate_End(Index).Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    dcDate_End(Index).Columns(0).Alignment = ssCaptionAlignmentLeft
    dcDate_End(Index).Columns(0).DataField = "EndDate"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDate_End(Index), ACW_EXPAND
    End If
CleanUp:
    dcDate_End(Index).Redraw = True   'Process is done. Repaint control.
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_End_InitColumnProps)"
    f_HandleErr , , , Me.Caption & "::dcDate_End_InitColumnProps", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub
Private Sub dcDate_Start_Change(Index As Integer)
Dim StartDate As String
Dim I As Integer
StartDate = dcDate_Start(Index).Text
If Not IsDate(StartDate) Then Exit Sub
For I = 0 To 2
    dcDate_Start(I).Text = StartDate
Next
SetEffective_Periods 2  ' value 2 is for Dtl tab
SetEffective_Periods 3  ' value 3 is for Item tab
SetEffective_Periods 1  ' value 1 is for Summary tab
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_Start_Change)"
     f_HandleErr , , , Me.Caption & "::dcDate_Start_Change", Now, gDRGeneralError, True, Err
End Sub
Private Sub dcDate_Start_Click(Index As Integer)
On Error GoTo ErrorHandler
    dcDate_Start_Change (Index)
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_Start_Click)"
    f_HandleErr , , , Me.Caption & "::dcDate_Start_Click", Now, gDRGeneralError, True, Err
End Sub
Private Sub dcDate_Start_InitColumnProps(Index As Integer)
On Error GoTo ErrorHandler
   Dim IndexCounter As Long
   Dim I As Integer
    dcDate_Start(Index).Redraw = False  'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbHourglass

    dcDate_Start(Index).Columns(0).Caption = getTranslationResource("StartDate")
    dcDate_Start(Index).Columns(0).Width = 1500
    dcDate_Start(Index).Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    dcDate_Start(Index).Columns(0).Alignment = ssCaptionAlignmentLeft
    dcDate_Start(Index).Columns(0).DataField = "StartDate"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDate_Start(Index), ACW_EXPAND
    End If
CleanUp:
    dcDate_Start(Index).Redraw = True   'Process is done. Repaint control.
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_Start_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dcDate_Start_InitColumnProps", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub

Private Sub dcForecastID_Change()
On Error GoTo ErrorHandler
    Dim strMessage As String
    Dim FetchSuccess As Boolean
    Dim lngRecordIndex As Long

    'Check for updates, first
'    If Not m_InitialLoad Then f_AIMForecast_Commit True
    'Navigate to the selected forecast ID
    If StrComp(m_FcstId, Me.dcForecastID.Text, vbTextCompare) <> 0 Then
        Me.dcForecastID.Redraw = False  'Disable the control from repainting until the process has finished.
        Screen.MousePointer = vbHourglass
        m_FcstId = dcForecastID.Text
        FetchSuccess = mFcstSetup_Fetch(SQL_GetEq, True)
        Me.dcForecastID.Redraw = True   'Process is done. Repaint control
        Screen.MousePointer = vbNormal
    End If
    GetRepositoryKey
    Fill_FcstTypes
    PopulateUserElements
    ClearGrids
    mChart_Refresh atModification.SelectedTab.Key
    'Fill_UserElements
Exit Sub
ErrorHandler:
    Me.dcForecastID.Redraw = True
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dcForecastID_Change)"
     f_HandleErr , , , Me.Caption & "::dcForecastID_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcForecastID_Click()
On Error GoTo ErrorHandler
    dcForecastID_Change
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcForecastID_Click)"
     f_HandleErr , , , Me.Caption & "::dcForecastID_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcForecastID_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim EBFcstId As SSOleDBCombo   'Early-binding is faster than late-binding a COM control
    Set EBFcstId = Me.dcForecastID

    EBFcstId.Redraw = False  'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbHourglass

    EBFcstId.Columns(0).Caption = getTranslationResource("Forecast ID")
    EBFcstId.Columns(0).Width = 1500
    EBFcstId.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    EBFcstId.Columns(0).Alignment = ssCaptionAlignmentLeft
    EBFcstId.Columns(0).DataField = "FcstID"

    EBFcstId.Columns(1).Caption = getTranslationResource("Description")
    EBFcstId.Columns(1).Width = 2880
    EBFcstId.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    EBFcstId.Columns(1).Alignment = ssCaptionAlignmentLeft
    EBFcstId.Columns(1).DataField = "FcstDesc"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBFcstId, ACW_EXPAND
    End If
CleanUp:
    EBFcstId.Redraw = True   'Process is done. Repaint control.
    Screen.MousePointer = vbNormal
    Set EBFcstId = Nothing
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcForecastID_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dcForecastID_InitColumnProps", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub

Private Sub dcGroupBy_Change()
On Error GoTo ErrorHandler
    If m_IsGridSummaryLoaded = False Then Exit Sub

    If IsNull(dcGroupBy.Text) _
    Or Trim(dcGroupBy.Text) = "" _
    Then
        Me.dcGroupBy.Text = GB_LCID
    End If
    Me.dcGroupBy.Redraw = False
    Screen.MousePointer = vbHourglass
    Call Filter_Forecast
    dgSummary.MoveFirst
CleanUp:
    Me.dcGroupBy.Redraw = True
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcForecastID_Change)"
    f_HandleErr , , , Me.Caption & "::dcGroupBy_Change", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub

Private Sub dcGroupBy_Click()
On Error GoTo ErrorHandler
    dcGroupBy_Change
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcGroupBy_Click)"
    f_HandleErr , , , Me.Caption & "::dcGroupBy_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcGroupBy_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim EBGroupBy As SSOleDBCombo

    Set EBGroupBy = Me.dcGroupBy
    EBGroupBy.Redraw = False
    Screen.MousePointer = vbHourglass
    'Clear any existing columns
    EBGroupBy.Columns.RemoveAll

    IndexCounter = 0
    'Set up the list
    With EBGroupBy
        .Columns.Add IndexCounter
        .Columns(IndexCounter).Name = "ColumnName"
        .Columns(IndexCounter).Caption = getTranslationResource("Column Name")
        .Columns(IndexCounter).Width = EBGroupBy.Width
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        IndexCounter = IndexCounter + 1

        .Columns.Add IndexCounter
        .Columns(IndexCounter).Name = "ColumnID"
        .Columns(IndexCounter).Caption = getTranslationResource("Column ID")
        .Columns(IndexCounter).Width = 1440
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).Visible = False
        IndexCounter = IndexCounter + 1

        .AddItem getTranslationResource(GB_ASSORT) & vbTab & GB_ASSORT
        .AddItem getTranslationResource(GB_BYID) & vbTab & GB_BYID
        .AddItem getTranslationResource(GB_CLASS1) & vbTab & GB_CLASS1
        .AddItem getTranslationResource(GB_CLASS2) & vbTab & GB_CLASS2
        .AddItem getTranslationResource(GB_CLASS3) & vbTab & GB_CLASS3
        .AddItem getTranslationResource(GB_CLASS4) & vbTab & GB_CLASS4
        .AddItem getTranslationResource(GB_ITEM) & vbTab & GB_ITEM
        .AddItem getTranslationResource(GB_LCID) & vbTab & GB_LCID
        .AddItem getTranslationResource(GB_LDIVISION) & vbTab & GB_LDIVISION
        .AddItem getTranslationResource(GB_LREGION) & vbTab & GB_LREGION
        .AddItem getTranslationResource(GB_LSTATUS) & vbTab & GB_LSTATUS
        .AddItem getTranslationResource(GB_LUSERDEFINED) & vbTab & GB_LUSERDEFINED
        .AddItem getTranslationResource(GB_VELCODE) & vbTab & GB_VELCODE
        .AddItem getTranslationResource(GB_VNID) & vbTab & GB_VNID
    End With

    EBGroupBy.Text = GB_LCID
CleanUp:
    EBGroupBy.Redraw = True
    Screen.MousePointer = vbNormal
    Set EBGroupBy = Nothing

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcGroupBy_InitColumnProps)"
    f_HandleErr , , , Me.Caption & "::dcGroupBy_InitColumnProps", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub

Private Sub dgItem_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    End Select
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItem_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgItem_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItem_RowColChange(LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler
    If MainArrayFiltered.Count(1) > 0 Then
        mChart_Refresh TAB_DETAIL
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItem_RowColChange)"
     f_HandleErr , , , Me.Caption & "::dgItem_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItemDetail_LostFocus()
dgUserElements.Update
End Sub

Private Sub dgItemDetail_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    End Select
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItemDetail_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgItemDetail_MouseDown", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgSummary_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    End Select
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSummary_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgSummary_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSummary_RowColChange(LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler
If SummaryArray.Count(1) > 0 Then
    mChart_Refresh TAB_SUMMARY
End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSummary_RowColChange)"
     f_HandleErr , , , Me.Caption & "::dgSummary_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgUserElements_BeforeColUpdate(ByVal ColIndex As Integer, OldValue As Variant, Cancel As Integer)
'If dgUserElements.Columns(3).Value = 0 Then
''MSGBOX06906
'    MsgBox "This row cannot be selected because the UserElement does not have data", vbOKOnly, Me.Caption
'   Cancel = False
'    dgUserElements.Columns(ColIndex).Value = vbUnchecked
'
'Exit Sub
'End If
End Sub

Private Sub dgUserElements_FetchRowStyle(ByVal Split As Integer, Bookmark As Variant, ByVal RowStyle As TrueOleDBGrid80.StyleDisp)
If dgUserElements.Columns(3).CellText(Bookmark) = "0" Then
RowStyle.Locked = True
RowStyle.BackColor = RGB(192, 192, 192)
End If
End Sub


'Private Sub dgUserElements_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
'If dgUserElements.Columns(3).Value = 0 Then
''MSGBOX06906
'    MsgBox "This row cannot be selected because the UserElement does not have data", vbOKOnly, Me.Caption
'    dgUserElements.Columns(0).Value = OldValue
'Exit Sub
'End If
'End Sub

'Private Sub dgUserElements_InitColumnProps()
'On Error GoTo ErrorHandler
'    Dim IndexCounter As Long
'
'    Me.dgUserElements.Columns(0).Name = "SelOpt"
'    Me.dgUserElements.Columns(0).Caption = getTranslationResource("X")
'    Me.dgUserElements.Columns(0).Width = 400
'    Me.dgUserElements.Columns(0).Locked = False
'    Me.dgUserElements.Columns(0).Style = ssStyleCheckBox
'    Me.dgUserElements.Columns(0).Alignment = ssCaptionAlignmentCenter
'
'    Me.dgUserElements.Columns(1).Name = "UserElementId"
'    Me.dgUserElements.Columns(1).Caption = getTranslationResource("User Element")
'    Me.dgUserElements.Columns(1).Width = 1000
'    Me.dgUserElements.Columns(1).Locked = True
'    Me.dgUserElements.Columns(1).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgUserElements.Columns(2).Name = "UserElementDesc"
'    Me.dgUserElements.Columns(2).Caption = getTranslationResource("Description")
'    Me.dgUserElements.Columns(2).Width = 2835
'    Me.dgUserElements.Columns(2).Locked = True
'    Me.dgUserElements.Columns(2).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgUserElements.Columns(3).Name = "Processed_YN"
'    Me.dgUserElements.Columns(3).Caption = getTranslationResource("Processed_YN")
'    Me.dgUserElements.Columns(3).Width = 2835
'    Me.dgUserElements.Columns(3).Locked = True
'    Me.dgUserElements.Columns(3).Visible = False
'    Me.dgUserElements.Columns(3).Alignment = ssCaptionAlignmentLeft
'
'    Me.dgUserElements.Columns(4).Name = "FcstType"
'    Me.dgUserElements.Columns(4).Caption = getTranslationResource("FcstType")
'    Me.dgUserElements.Columns(4).Width = 2835
'    Me.dgUserElements.Columns(4).Locked = True
'    Me.dgUserElements.Columns(4).Visible = False
'    Me.dgUserElements.Columns(4).Alignment = ssCaptionAlignmentLeft
'
'
'    If gGridAutoSizeOption Then
'        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
'        AdjustColumnWidths dgUserElements, ACW_EXPAND
'    End If
'
'    For IndexCounter = 0 To dgUserElements.Columns.Count - 1
'        If dgUserElements.Columns(IndexCounter).Locked = False Then dgUserElements.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
'    Next
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserElements_InitColumnProps)"
'End Sub
'
'Private Sub dgUserElements_LostFocus()
'dgUserElements.Update
'End Sub

'Private Sub dgUserElements_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
''If dgUserElements.Col = 0 And dgUserElements.Columns(3).CellValue(dgUserElements.Bookmark) = 0 Then
''        dgUserElements.Columns(0).Locked = True
''        lockflag = True
''    Else
''        dgUserElements.Columns(0).Locked = False
''
''End If
'
'
'End Sub

'Private Sub dgUserElements_RowLoaded(ByVal Bookmark As Variant)
'If Bookmark = Empty Then Exit Sub
'
'Dim TDBGStyle As TrueOleDBGrid80.Style
'Set TDBGStyle = New TrueOleDBGrid80.Style
'
'If dgUserElements.Columns(3).Text = "0" Then
'
''dgUserElements.Columns(0).AddRegexCellStyle dbgNormalCell, TDBGStyle, CStr(vbGrayed)
'dgUserElements.Columns(0).AddRegexCellStyle dbgNormalCell, TDBGStyle, CStr(vbGrayed)
'End If
'
'   Set TDBGStyle = Nothing
'
'End Sub

'Private Sub dgUserElements_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
'On Error GoTo ErrorHandler
'    If IsNull(StartLocation) Then
'        StartLocation = 0
'    End If
'    NewLocation = CLng(StartLocation) + NumberOfRowsToMove
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserElements_UnboundPositionData)"
'End Sub

'Private Sub dgUserElements_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
'    Dim I As Integer
'    Dim p As Integer
'    Dim r As Integer
'    Dim RowCount As Integer
'    Dim TDBGStyle As TrueOleDBGrid80.Style
'    Set TDBGStyle = New TrueOleDBGrid80.Style
'    RowCount = UBound(m_UserElements)
'    If RowCount <= 0 Then Exit Sub
'
'    If IsNull(StartLocation) Then       'If the grid is empty then
'        If ReadPriorRows Then           'If moving backwards through grid then
'            p = RowCount                'pointer = # of last grid row
'        Else                            'else
'            p = 0                       'pointer = # of first grid row
'        End If
'
'    Else                                'If the grid already has data in it then
'        p = StartLocation               'pointer = location just before or after the row where data will be added
'
'        If ReadPriorRows Then           'If moving backwards through grid then
'            p = p - 1                   'move pointer back one row
'        Else                            'else
'            p = p + 1                   'move pointer ahead one row
'        End If
'
'    End If
'
'    'The pointer (p) now points to the row of the grid where you will start adding data.
'    For I = 0 To RowBuf.RowCount - 1                   'For each row in the row buffer
'
'        If p < 0 Or p > RowCount - 1 Then Exit For     'If the pointer is outside the grid then stop this
'        'For each column in the grid
'        'Set the value of each column in the row buffer
'        'to the corresponding value in the arrray
'
'        RowBuf.Value(I, 0) = m_UserElements(p).SelOpt
'        RowBuf.Value(I, 1) = m_UserElements(p).UserElementId
'        RowBuf.Value(I, 2) = m_UserElements(p).UserElementDesc
'        RowBuf.Value(I, 3) = m_UserElements(p).Processed_YN
'        RowBuf.Value(I, 4) = m_UserElements(p).FcstType
'        RowBuf.Bookmark(I) = p                          'set the value of the bookmark for the current row in the rowbuffer
'
'        If ReadPriorRows Then                           'move the pointer forward or backward, depending
'            p = p - 1                                   'on which way it's supposed to move
'        Else
'            p = p + 1
'        End If
'
'        r = r + 1                                       'increment the number of rows read
'
'    Next I
'
'    RowBuf.RowCount = r                                 'set the size of the row buffer to the number of rows read
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserElements_UnboundReadData)"
'End Sub


'Private Sub dgUserElements_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
'On Error GoTo ErrorHandler
'    m_UserElements(CInt(WriteLocation)).SelOpt = CStr(RowBuf.Value(0, 0))
'
'Exit Sub
'ErrorHandler:
'    f_HandleErr Me.Caption & "(dgUserElements_UnboundWriteData)"
'End Sub
'
Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim Filter As Variant
    Dim I As Long
    Dim J As Integer
    Dim bm As Variant
    Dim RtnCode As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    Dim strMessage As String
    Dim GeneralGrid As TDBGrid
    
    If SummaryArray.Count(1) = 0 Or SummaryArray.Count(2) = 0 Then
            Screen.MousePointer = vbNormal
            Exit Sub
        End If
    Screen.MousePointer = vbHourglass
    If UCase(atModification.SelectedTab.Key) = TAB_SUMMARY Then
        Set GeneralGrid = Me.dgSummary
    ElseIf UCase(atModification.SelectedTab.Key) = TAB_DETAIL Then
        Set GeneralGrid = Me.dgItem
    ElseIf UCase(atModification.SelectedTab.Key) = TAB_ITEMDETAIL Then
        Set GeneralGrid = Me.dgItemDetail
    Else
    Exit Sub
    End If
    
    Select Case Index
    Case 0          'Copy to Clipboard
        Clipboard.Clear
        'Check for an empty set
        For I = 0 To GeneralGrid.Columns.Count - 1
            s1 = s1 + GeneralGrid.Columns(I).Caption + vbTab
        Next I
        s1 = s1 + vbCrLf
        GeneralGrid.MoveFirst
        I = 0
        Do Until GeneralGrid.eof = True
          ' bm = GeneralGrid.GetBookmark(i)
           bm = GeneralGrid.GetBookmark(0)
            For J = 0 To GeneralGrid.Columns.Count - 1
                s1 = s1 & GeneralGrid.Columns(J).CellText(bm) & "" + vbTab
            Next J
            s1 = s1 + vbCrLf
            GeneralGrid.MoveNext
            I = I + 1
        Loop
        Clipboard.SetText s1
    Case 1
        'Print
        With GeneralGrid.PrintInfo
            ' Set the page header
            .PageHeaderFont.Italic = True
            '.PageHeader = "Composers table"
            ' Column headers will be on every page
            .RepeatColumnHeaders = True
            ' Display page numbers (centered)
            .PageFooter = "\tPage: \p"
            ' Invoke Print Preview
            .PrintPreview
        End With
    Case 2          'Copy Selected rows to  Clipboard
        Clipboard.Clear
        For I = 0 To GeneralGrid.Columns.Count - 1
            s1 = s1 + GeneralGrid.Columns(I).Caption + vbTab
        Next I
        s1 = s1 + vbCrLf
        GeneralGrid.MoveFirst
        For I = 0 To GeneralGrid.SelBookmarks.Count - 1
            bm = GeneralGrid.SelBookmarks(I)
            For J = 0 To GeneralGrid.Columns.Count - 1
                s1 = s1 & GeneralGrid.Columns(J).CellText(bm) & "" + vbTab
            Next J
            s1 = s1 + vbCrLf
        Next I
        Clipboard.SetText s1
    Case 4
        GeneralGrid.ExportToFile App.Path & "\" & dcForecastID.Text & ".htm", False, 0
    End Select
    GeneralGrid.MoveFirst
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
    f_HandleErr , , , Me.Caption & "::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
End Sub

Private Sub optFcstInterval_Click(Index As Integer)
On Error GoTo ErrorHandler
    m_NewFcstInterval = Index
    If m_NewFcstInterval <> m_FcstInterval Then
        tbFcstModification.Tools(TB_ID_LOAD).Enabled = False
    Else
        tbFcstModification.Tools(TB_ID_LOAD).Enabled = True
    End If
    mSetPeriodBounds
    
    If m_FcstHierarchy = 2 Then ' live forecast
        If m_NewFcstInterval <> int_Weeks Then
            Me.tbFcstModification.Tools(TB_ID_PROMOTE).Enabled = False
        ElseIf m_NewFcstInterval = int_Weeks Then
            Me.tbFcstModification.Tools(TB_ID_PROMOTE).Enabled = True
        End If
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optFcstInterval_Click()"
     f_HandleErr , , , Me.Caption & "::optFcstInterval_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optFcstUnit_Click(Index As Integer)
On Error GoTo ErrorHandler
Dim AIMFcstUnits As AIM_FORECASTUNITS

If MainArrayFiltered.Count(1) > 0 Then
    m_IsGridItemLoaded = True
Else
    m_IsGridItemLoaded = False
    Exit Sub
End If
FilterDetailArray MainArray, MainArrayFiltered
AIMFcstUnits = Index
ConverToFcstUnits MainArrayFiltered, AIMFcstUnits
Set dgItem.Array = MainArrayFiltered
dgItem.ReBind
Call Refresh_SummmaryGrid
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optFcstUnit_Click)"
     f_HandleErr , , , Me.Caption & "::optFcstUnit_Click", Now, gDRGeneralError, True, Err
End Sub
Private Sub tbAdjustments_ToolClick(Index As Integer, ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim MustRefresh As Boolean
    MustRefresh = False
    Screen.MousePointer = vbHourglass
    Select Case UCase$(Tool.ID)
    Case "ID_ADJLOG"
        Select Case Index
        Case 0  'Summary
            RtnCode = mShowAdjLog(TAB_SUMMARY)
        Case 1  'Detail
            RtnCode = mShowAdjLog(TAB_DETAIL)
        End Select
        MustRefresh = True
    Case "ID_APPLY"
        Call Apply_Adjutments
        Select Case Index
        Case 0  'Summary
         '   RtnCode = mApplyMassAdjustments(TAB_SUMMARY)
        Case 1  'Detail
         '   RtnCode = mApplyMassAdjustments(TAB_DETAIL)
        End Select
        MustRefresh = True
        Me.tbAdjustments(Index).Tools("ID_ADJLOG").Enabled = False  'We don't want them wandering off after applying and before saving/cancelling
        Me.tbAdjustments(Index).Tools("ID_SAVE").Enabled = True
        Me.tbAdjustments(Index).Tools("ID_CANCEL").Enabled = True

    Case "ID_SAVE"
        RtnCode = Save_Adjustments()
        If RtnCode = -1 Then
            'Failure
             strMessage = getTranslationResource("MSGBOX06907")
            If StrComp(strMessage, "MSGBOX06907") = 0 Then strMessage = "Error occurred during saving adjustments to Forecast Repository"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        
        End If
       
        MustRefresh = True
        Me.tbAdjustments(Index).Tools("ID_ADJLOG").Enabled = True
        Me.tbAdjustments(Index).Tools("ID_SAVE").Enabled = False
        Me.tbAdjustments(Index).Tools("ID_CANCEL").Enabled = False
    Case "ID_CANCEL"
       RtnCode = RollBack_Adjustments()
       If RtnCode = -1 Then
            'error occured
            strMessage = getTranslationResource("ERRMSG06906")
            If StrComp(strMessage, "ERRMSG06906") = 0 Then strMessage = "Error occurred during canceling adjustments to selected record"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        End If
        
        MustRefresh = True
        Me.tbAdjustments(Index).Tools("ID_ADJLOG").Enabled = True
        Me.tbAdjustments(Index).Tools("ID_SAVE").Enabled = False
        Me.tbAdjustments(Index).Tools("ID_CANCEL").Enabled = False

    Case TB_ID_SETBOOKMARK
        Select Case UCase$(atModification.SelectedTab.Key)
        Case TAB_SUMMARY
            If IsEmpty(m_SumBookMark) _
            Or IsNull(m_SumBookMark) _
            Then
                m_SumBookMark = Me.dgSummary.Bookmark
                Me.tbAdjustments(0).Tools(TB_ID_GOTOBOOKMARK).Enabled = True
            Else
                m_SumBookMark = Null
                Me.tbAdjustments(0).Tools(TB_ID_GOTOBOOKMARK).Enabled = False
            End If
        Case TAB_DETAIL
            If IsEmpty(m_DtlBookMark) _
            Or IsNull(m_DtlBookMark) _
            Then
                m_DtlBookMark = Me.dgItem.Bookmark
                Me.tbAdjustments(1).Tools(TB_ID_GOTOBOOKMARK).Enabled = True
            Else
                m_DtlBookMark = Null
                Me.tbAdjustments(1).Tools(TB_ID_GOTOBOOKMARK).Enabled = False
            End If
        End Select
        MustRefresh = False

    Case TB_ID_GOTOBOOKMARK
        Select Case UCase$(atModification.SelectedTab.Key)
        Case TAB_SUMMARY
            If Not IsEmpty(m_SumBookMark) _
            Or Not IsNull(m_SumBookMark) _
            Then
                If Me.dgSummary.IsSelected(Me.dgSummary.Bookmark) = 0 Then
                    Me.dgSummary.SelBookmarks.Remove 0
                End If
                Me.dgSummary.Bookmark = m_SumBookMark
            End If
        Case TAB_DETAIL
            If Not IsEmpty(m_DtlBookMark) _
            Or Not IsNull(m_DtlBookMark) _
            Then
                If Me.dgItem.IsSelected(Me.dgItem.Bookmark) = 0 Then
                    Me.dgItem.SelBookmarks.Remove 0
                End If
                Me.dgItem.Bookmark = m_DtlBookMark
            End If
        End Select
        MustRefresh = False
    End Select

    If MustRefresh = True Then
        mChart_Refresh (UCase$(atModification.SelectedTab.Key))
    End If
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbAdjustments_ToolClick)"
    f_HandleErr , , , Me.Caption & "::tbAdjustments_ToolClick", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
End Sub

Private Sub tbFcstModification_ToolClick( _
    ByVal Tool As ActiveToolBars.SSTool _
)
On Error GoTo ErrorHandler
    Dim strMessage As String
    Dim RtnCode As Integer
    Dim CheckTime As Date
    Dim CurrentFilter As String
    Dim GeneralGrid As TDBGrid
    Screen.MousePointer = vbHourglass
    'Clear messages
    Write_Message ""

    Me.tbFcstModification.Redraw = False
    Screen.MousePointer = vbHourglass
    dgUserElements.Update
    Select Case UCase$(Tool.ID)
    Case TB_ID_LOAD
        'Check the refresh case at the end of this select
        Call GetData_FromRepository
    Case TB_ID_SAVEFORECAST
        If Check_Reason() = -1 Then
            strMessage = getTranslationResource("ERRMSG06907")
            If StrComp(strMessage, "ERRMSG06907") = 0 Then
                strMessage = "Reason is required to save "
                MsgBox strMessage, vbQuestion, Me.Caption
                GoTo CleanUp
            End If
        End If
        strMessage = getTranslationResource("STATMSG06906")
        If StrComp(strMessage, "STATMSG06906") = 0 Then strMessage = "Saving selected adjustments to Forecast Repository"
        Write_Message strMessage
        RtnCode = Save_Adjustments()
        If RtnCode = -1 Then
            'Failure
             strMessage = getTranslationResource("ERRMSG06904")
            If StrComp(strMessage, "ERRMSG06904") = 0 Then strMessage = "Error occurred during saving adjustments to Forecast Repository"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        
        End If
        Me.tbFcstModification.Tools("ID_ADJLOG").Enabled = True
        Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
        Me.tbFcstModification.Tools("ID_CANCEL").Enabled = False
       ' RtnCode = SaveTo_Repository()
        
    Case TB_ID_RUNFORECAST
        strMessage = getTranslationResource("STATMSG06904")
        If StrComp(strMessage, "STATMSG06904") = 0 Then strMessage = "Calculating forecast for the Forecast ID"
        Write_Message strMessage
        CheckTime = Now()
       RtnCode = Generate_Forecast()
       PopulateUserElements
       If RtnCode = -1 Then
        'error
        'error occured
            strMessage = getTranslationResource("ERRMSG06902")
            If StrComp(strMessage, "ERRMSG06902") = 0 Then strMessage = "Error occurred during calculating forecast for Forecast ID"
            MsgBox strMessage, vbQuestion, Me.Caption
       Else
       End If
        Debug.Print "Total time for forecast run: " & DateDiff("s", CheckTime, Now())
        'Check the refresh case at the end of this select
        Select Case RtnCode
        Case 1  'Success
        Case -1 'No Data Found
            'Msg here
        Case -2 'SQL Error
            'Msg here
        Case -3 'Invalid parameter
            'Msg here
        End Select

    Case TB_ID_ITEMFILTER
        strMessage = getTranslationResource("STATMSG06901")
        If StrComp(strMessage, "STATMSG06901") = 0 Then strMessage = "Filtering data..."
        Write_Message strMessage

        CurrentFilter = Me.txtSelected.Text
        DisplayFilters CurrentFilter
        If Filter_Forecast() = False Then
            'error occure
            strMessage = getTranslationResource("ERRMSG06900")
            If StrComp(strMessage, "ERRMSG06900") = 0 Then strMessage = "Error occurred during filtering data"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        End If
    Case TB_ID_CLEARFILTER
        strMessage = getTranslationResource("STATMSG06902")
        If StrComp(strMessage, "STATMSG06902") = 0 Then strMessage = "Clearing filter..."
        Write_Message strMessage
        g_CXArr1ToXArr2 MainArray, MainArrayFiltered
        g_CXArr1ToXArr2 m_xaOrigCriteria, m_xaAllCriteria
        g_InitCriteria m_xaNewCriteria
        Me.txtSelected.Text = g_CXArrToPara(m_xaAllCriteria, False, False)
        Me.txtNewFilters.Text = g_CXArrToPara(m_xaNewCriteria, False, False)
        If Filter_Forecast() = False Then
            'error occure
            strMessage = getTranslationResource("ERRMSG06901")
            If StrComp(strMessage, "ERRMSG06901") = 0 Then strMessage = "Error occurred during Removing filtering"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        End If
        
    Case TB_ID_FORECASTSETUP
        AIM_ForecastSetup.Show vbModeless
    Case TB_ID_COPY
        strMessage = getTranslationResource("STATMSG06905")
        If StrComp(strMessage, "STATMSG06905") = 0 Then strMessage = "Creating new user element"
        Write_Message strMessage
        
        RtnCode = mShowUserElements()
        If RtnCode = -1 Then
            'error occured
            strMessage = getTranslationResource("ERRMSG06903")
            If StrComp(strMessage, "ERRMSG06903") = 0 Then strMessage = "Error occurred during creating new user element"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
         
        PopulateUserElements
        End If
    Case TB_ID_CLOSE
        strMessage = getTranslationResource("STATMSG06910")
        If StrComp(strMessage, "STATMSG06910") = 0 Then strMessage = "Closing Forecast Planner"
        Write_Message strMessage
        Unload Me
        Screen.MousePointer = vbNormal
        Exit Sub
    Case TB_ID_RECALCCURRENT
        CheckTime = Now()
        strMessage = getTranslationResource("STATMSG06903")
        If StrComp(strMessage, "STATMSG06903") = 0 Then strMessage = "Recalculating forecast for filtered records"
        Write_Message strMessage
       Call GetData_FromRepository
        Debug.Print "Total time for forecast run: " & DateDiff("s", CheckTime, Now())
        'Check the refresh case at the end of this select
        Select Case RtnCode
        Case 1  'Success
        Case -1 'No Data Found
            'Msg here
        Case -2 'SQL Error
            'Msg here
        Case -3 'Invalid parameter
            'Msg here
        End Select

    Case TB_ID_CHART_TOGGLESIZE
        mChart_ToggleSize

    Case TB_ID_CHART_SYNC
        mChart_Refresh (UCase$(atModification.SelectedTab.Key))

    Case TB_ID_PROMOTE
        strMessage = getTranslationResource("STATMSG06907")
        If StrComp(strMessage, "STATMSG06907") = 0 Then strMessage = "Promoting selected adjustments to Master Forecast Plan "
        Write_Message strMessage
        RtnCode = PromoteTo_Master()
        If RtnCode = -1 Then
            'error occured
            strMessage = getTranslationResource("ERRMSG06905")
            If StrComp(strMessage, "ERRMSG06905") = 0 Then strMessage = "Error occurred during prompting adjustments to Master Forecast Plan"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        End If
    Case TB_ID_GOTOBOOKMARK
         Select Case UCase$(atModification.SelectedTab.Key)
        Case TAB_SUMMARY
            If Not IsEmpty(m_SumBookMark) _
            Or Not IsNull(m_SumBookMark) _
            Then
                If Me.dgSummary.IsSelected(Me.dgSummary.Bookmark) = 0 Then
                    Me.dgSummary.SelBookmarks.Remove 0
                End If
                Me.dgSummary.Bookmark = m_SumBookMark
            End If
        Case TAB_DETAIL
            If Not IsEmpty(m_DtlBookMark) _
            Or Not IsNull(m_DtlBookMark) _
            Then
                If Me.dgItem.IsSelected(Me.dgItem.Bookmark) = 0 Then
                    Me.dgItem.SelBookmarks.Remove 0
                End If
                Me.dgItem.Bookmark = m_DtlBookMark
            End If
        End Select
    Case TB_ID_SETBOOKMARK
        Select Case UCase$(atModification.SelectedTab.Key)
        Case TAB_SUMMARY
            If IsEmpty(m_SumBookMark) _
            Or IsNull(m_SumBookMark) _
            Then
                m_SumBookMark = Me.dgSummary.Bookmark
                Me.tbFcstModification.Tools(TB_ID_GOTOBOOKMARK).Enabled = True
            Else
                m_SumBookMark = Null
                Me.tbFcstModification.Tools(TB_ID_GOTOBOOKMARK).Enabled = False
            End If
        Case TAB_DETAIL
            If IsEmpty(m_DtlBookMark) _
            Or IsNull(m_DtlBookMark) _
            Then
                m_DtlBookMark = Me.dgItem.Bookmark
                Me.tbFcstModification.Tools(TB_ID_GOTOBOOKMARK).Enabled = True
            Else
                m_DtlBookMark = Null
                Me.tbFcstModification.Tools(TB_ID_GOTOBOOKMARK).Enabled = False
            End If
        End Select
    Case "ID_ADJLOG"
            RtnCode = mShowAdjLog(TAB_DETAIL)
    Case "ID_APPLY"
        RtnCode = Check_ZeroAdj()
        If RtnCode = -1 Then
            GoTo CleanUp
        End If
        
        strMessage = getTranslationResource("STATMSG06908")
        If StrComp(strMessage, "STATMSG06908") = 0 Then strMessage = "Applying adjustments to selected records"
        Write_Message strMessage
        Call Apply_Adjutments
        Me.tbFcstModification.Tools("ID_ADJLOG").Enabled = False  'We don't want them wandering off after applying and before saving/cancelling
        Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = True
        Me.tbFcstModification.Tools("ID_CANCEL").Enabled = True
    Case "ID_CANCEL"
        strMessage = getTranslationResource("STATMSG06909")
        If StrComp(strMessage, "STATMSG06909") = 0 Then strMessage = "Canceling adjustments to selected records"
        Write_Message strMessage
        RtnCode = RollBack_Adjustments()
        If RtnCode = -1 Then
            'error occured
            strMessage = getTranslationResource("ERRMSG06906")
            If StrComp(strMessage, "ERRMSG06906") = 0 Then strMessage = "Error occurred during canceling adjustments to selected record"
            MsgBox strMessage, vbQuestion, Me.Caption
        Else
        End If
        
        Me.tbFcstModification.Tools("ID_ADJLOG").Enabled = True
        Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
        Me.tbFcstModification.Tools("ID_CANCEL").Enabled = False
    Case "ID_EXPORTTOFILE"
        If SummaryArray.Count(1) = 0 Or SummaryArray.Count(2) = 0 Then
                Screen.MousePointer = vbNormal
                Exit Sub
            End If
        Screen.MousePointer = vbHourglass
        If UCase(atModification.SelectedTab.Key) = TAB_SUMMARY Then
            Set GeneralGrid = Me.dgSummary
        ElseIf UCase(atModification.SelectedTab.Key) = TAB_DETAIL Then
            Set GeneralGrid = Me.dgItem
        ElseIf UCase(atModification.SelectedTab.Key) = TAB_ITEMDETAIL Then
            Set GeneralGrid = Me.dgItemDetail
        Else
            Exit Sub
        End If
        GeneralGrid.ExportToFile App.Path & "\" & dcForecastID.Text & ".htm", False, 0
    End Select
    mChart_Refresh (UCase$(atModification.SelectedTab.Key))
    
    
CleanUp:
    Write_Message ""
    Me.tbFcstModification.Redraw = True
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atbForecast_ToolClick)"
     f_HandleErr , , , Me.Caption & "::tbFcsModification_ToolClick", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub

Private Sub txtFcstPeriods_Future_Change()
On Error GoTo ErrorHandler
    Dim RtnCode As Long
    If Not IsNull(Me.txtFcstPeriods_Future.Value) And Not IsNull(Me.txtFcstPeriods_Historical.Value) Then
        'Update form variables to reflect date ranges
        mSetPeriodBounds
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtFcstPeriods_Future_Change)"
     f_HandleErr , , , Me.Caption & "::txtFcstPeriods_Future_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtFcstPeriods_Historical_Change()
On Error GoTo ErrorHandler
    Dim RtnCode As Long
    If Not IsNull(Me.txtFcstPeriods_Future.Value) And Not IsNull(Me.txtFcstPeriods_Historical.Value) Then
        'Update form variables to reflect date ranges
        mSetPeriodBounds
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtFcstPeriods_Historical_Change)"
     f_HandleErr , , , Me.Caption & "::txtFcstPeriods_Historical_Change", Now, gDRGeneralError, True, Err
End Sub
Private Sub Form_Activate()
On Error GoTo ErrorHandler
 'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    Dim RtnCode As Long
    Dim strSQL As String
    Dim strMessage As String
    Dim strMessage1 As String
    Dim IndexCounter As Long

    'Housekeeping
    Screen.MousePointer = vbHourglass
    m_IsInitialLoad = True
    m_FirstTime = True
    strMessage = getTranslationResource("STATMSG06900")
    If StrComp(strMessage, "STATMSG06900") = 0 Then strMessage = "Initializing Forecast Planner..."""
    Write_Message strMessage

    GetTranslatedCaptions Me
    m_FcstId = ""
    Me.tbFcstModification.Redraw = False    'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbNormal

    m_FcstSetupKey = -1
    'A.Stocksdale - For item-filter-multi-select
    g_InitCriteria m_xaOrigCriteria
    'A.Stocksdale - End
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    Initialize_StoredProcedures
    'Populate calender
    'AIMAdvCalendar_Load Cn, AC
    'Load Global calender also
    If AIMCalenderLoaded = False Then
        AIMAdvCalendar_Load Cn, AC
        AIMCalenderLoaded = True
    End If
   
    'Populate the dropdown(s)
    If mFcstID_Load = False Then
        'Disable options.
        Me.tbFcstModification.Tools(TB_ID_LOAD).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_RUNFORECAST).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_RECALCCURRENT).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_COPY).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_CHART_SYNC).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_ITEMFILTER).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_CLEARFILTER).Enabled = False
        Me.tbFcstModification.Tools(TB_ID_PROMOTE).Enabled = False
    Else
        'mFcstID_Load should have triggered fetching forecast data for the first record.
    End If

    'Make the spin button(s) visible
    txtFcstPeriods_Future.Spin.Visible = dbiShowAlways
    txtFcstPeriods_Historical.Spin.Visible = dbiShowAlways
    txtAdjQty_Smr.Spin.Visible = dbiShowAlways
    txtAdjQty_Dtl.Spin.Visible = dbiShowAlways
    txtAdjQty_Item.Spin.Visible = dbiShowAlways

    'Initialize adjustment fields
    txtAdjQty_Dtl.Value = 0
    txtAdjQty_Smr.Value = 0
    optAdjType_Dtl(ADJ_UNITS).Value = True
    optAdjType_Sum(ADJ_percent).Value = True
    optAdjType_Sum(ADJ_UNITS).Enabled = False
    optAdjType_Sum(ADJ_UNITS).Visible = False
    optAdjType_Sum(ADJ_OVERRIDE).Enabled = False
    optAdjType_Sum(ADJ_OVERRIDE).Visible = False
    
    mChart_Initialize

    'Size the form
    mForm_Rearrange
    m_IsInitialLoad = False

    'Add to Windows List
    AddToWindowList Me.Caption

    'Windup
    Write_Message ""
CleanUp:
    Me.tbFcstModification.Redraw = True     'Process is done. Repaint control
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
    f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub

Private Sub Form_Resize()
On Error GoTo ErrorHandler
    If m_IsInitialLoad = False Then
        mForm_Rearrange
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Resize)"
     f_HandleErr , , , Me.Caption & "::Form_Resize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    Dim RtnCode As Long
    Write_Message ""
    ClearGrids
    Destroy_StoredProcedures
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, True, , , Me.Caption)
    Set Cn = Nothing
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , Me.Caption & "::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function mForm_Rearrange()
On Error GoTo ErrorHandler
    Dim IndexCounter As Long
    Dim setHeight As Long
    Dim setWidth As Long
    Dim setTop As Long
    Dim setLeft As Long
    Dim tempUnit As Long

    If m_IsInitialLoad Then
        Me.Top = 0
        Me.Left = 0
        Me.Width = AIM_Main.ScaleWidth
        Me.Height = AIM_Main.ScaleHeight

        Me.fraCommon.Height = 5100
        Me.fraCommon.Top = 60
        Me.fraCommon.Left = 60
        Me.chtForecast.Height = 4000
        Me.chtForecast.Top = 1050
        Me.chtForecast.Left = 120
    End If

    If Me.WindowState <> vbMinimized _
    And AIM_Main.WindowState <> vbMinimized _
    Then
        'Set the common set's stats
        setWidth = Me.Width - 400
        'check for errors and correct to defaults
        setWidth = IIf(setWidth > 0, setWidth, 14925)
        Me.fraCommon.Width = setWidth
        Me.chtForecast.Width = IIf(fraCommon.Width >= 400, fraCommon.Width - 225, 14655)

        'SSTabs
        'Use the left and width of the common frame above
        atModification.Left = Me.fraCommon.Left
        atModification.Width = Me.fraCommon.Width
        'Set relative top
        setTop = (Me.fraCommon.Top + Me.fraCommon.Height + 60)
        atModification.Top = IIf(setTop >= 5192, 5192, setTop)
        'Set relative height
        setHeight = Me.Height - (Me.fraCommon.Height + 1200)
        If atModification.Top < 5000 _
        Then
            atModification.Height = IIf(setHeight > 0, setHeight, 4026)
        ElseIf setHeight <= 4126 _
        Then
            atModification.Height = 4126
        Else
            atModification.Height = setHeight
        End If

        'Frames within the tab
        setLeft = 120
        'set relative width
        setWidth = Me.atModification.ClientWidth - 200
        setTop = 0
        'set relative height
        setHeight = Me.atModification.ClientHeight - 50
        'check for errors and correct to defaults
        setWidth = IIf(setWidth > 0, setWidth, 14175)
        setHeight = IIf(setHeight > 0, setHeight, 3615)

        'Now, position the frames and grids accordingly
        Me.fraAdj_Smr.Top = 0
        Me.fraAdj_Dtl.Top = 0
        Me.fraAdj_ItemDtl.Top = 0
        Me.fraAdj_Smr.Left = 120
        Me.fraAdj_Dtl.Left = 120
        Me.fraAdj_ItemDtl.Left = 120
        
        Me.FraSummary.Top = (Me.fraAdj_Smr.Top + Me.fraAdj_Smr.Height + 60)
        Me.FraSummary.Left = setLeft
        Me.FraSummary.Width = setWidth
        'Set relative height
        tempUnit = (Me.atModification.ClientHeight - 50) - (Me.fraAdj_Dtl.Height + 300)
        Me.FraSummary.Height = IIf(tempUnit > 0, tempUnit, 2010)

        Me.FraItems.Top = Me.FraSummary.Top
        Me.FraItems.Left = Me.FraSummary.Left
        Me.FraItems.Height = Me.FraSummary.Height
        Me.FraItems.Width = Me.FraSummary.Width

        Me.fraItemDetail.Top = Me.FraSummary.Top
        Me.fraItemDetail.Left = Me.FraSummary.Left
        Me.fraItemDetail.Height = Me.FraSummary.Height
        Me.fraItemDetail.Width = Me.FraSummary.Width

        Me.dgSummary.Top = 200
        Me.dgSummary.Left = 120
        tempUnit = FraSummary.Height - (400)
        Me.dgSummary.Height = IIf(tempUnit > 0, tempUnit, 2730)
        tempUnit = (FraSummary.Width - 300)
        Me.dgSummary.Width = IIf(tempUnit > 0, tempUnit, 13815)

        Me.dgItem.Top = Me.dgSummary.Top
        Me.dgItem.Left = Me.dgSummary.Left
        Me.dgItem.Width = Me.dgSummary.Width
        Me.dgItem.Height = Me.dgSummary.Height
        
        Me.dgItemDetail.Top = Me.dgSummary.Top
        Me.dgItemDetail.Left = Me.dgSummary.Left
        Me.dgItemDetail.Width = Me.dgSummary.Width
        Me.dgItemDetail.Height = Me.dgSummary.Height

        'Size the options
        fraOptions.Top = setTop
        fraOptions.Left = setLeft
        fraOptions.Height = setHeight
        fraOptions.Width = setWidth
    End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(mForm_Rearrange)"
     f_HandleErr , , , "AIM_ForeCastPlanner::mForm_Rearrange", Now, gDRGeneralError, True, Err
End Function
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the AIMForecast table to populate the ForecastID dropdown
' .......................................................................
'   Returns false if there are no records to fetch,
'   else true after setting the display to the first record.
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function mFcstID_Load() As Boolean
On Error GoTo ErrorHandler
    Dim RtnCode As Boolean
    Dim RowCounter As Long, ColCounter As Long
    Dim AddItemString As String
    Dim EBFcstId As SSOleDBCombo

    Set EBFcstId = Me.dcForecastID
    EBFcstId.Redraw = False
    Screen.MousePointer = vbHourglass

    'Default to false until the process returns it's result.
    mFcstID_Load = False

    'Get Forecast IDs and Descriptions from AIMForecast
    RtnCode = gList_FcstIDs(Cn, m_rsForecastList, Me.Caption, True)
    If RtnCode = True Then
        EBFcstId.RemoveAll
        EBFcstId.Reset

        m_FcstId = IIf(Trim$(m_FcstId) = "", m_rsForecastList(0).Value, m_FcstId)
        For RowCounter = 0 To m_rsForecastList.RecordCount - 1
            For ColCounter = 0 To m_rsForecastList.Fields.Count - 1
                AddItemString = AddItemString & m_rsForecastList.Fields(ColCounter).Value & vbTab
            Next
            EBFcstId.AddItem AddItemString
            AddItemString = ""
            m_rsForecastList.MoveNext
        Next

        'Get the forecast data to display in the screen's controls
        mFcstSetup_Fetch SQL_GetEq, True

        mFcstID_Load = True
    End If
CleanUp:
    If f_IsRecordsetValidAndOpen(m_rsForecastList) Then m_rsForecastList.Close
    Set m_rsForecastList = Nothing
    EBFcstId.Redraw = True
    Set EBFcstId = Nothing
    Screen.MousePointer = vbNormal
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.mFcstID_Load)"
     f_HandleErr , , , "AIM_ForeCastPlanner::mFcstID_Load", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Function


'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the AIMForecastSetup table for the modular forecast ID.
' .......................................................................
'   Parameters:
'       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
'       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function mFcstSetup_Fetch( _
    p_Action As SQL_ACTIONS, _
    p_Related As Boolean _
    ) As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim Count As Long
    'Default to false until process returns it's result
    mFcstSetup_Fetch = False
    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
    RtnCode = gFetch_ForecastSetup(Cn, m_FcstId, gUserID, p_Action, p_Related, m_rsCompound)

    'Check return code -- 0 = fail; 1 = succeed
    If RtnCode = 0 Then
        'FAIL
        Exit Function
    End If
    If f_IsRecordsetValidAndOpen(m_rsCompound) Then
        ' Display results from each SELECT statement
        'dgUserElements.Redraw = False
        Screen.MousePointer = vbHourglass
        Count = 1
        Do Until m_rsCompound Is Nothing
            Do Until m_rsCompound.eof
                'Set display to match recordset(s)
                Select Case Count
                Case 1  'AIMForecastSetup
                    'Get recordset values
                    m_FcstId = m_rsCompound!FcstId
                    m_FcstSetupKey = m_rsCompound!FcstSetupKey

                    RtnCode = mFcstSetup_RefreshUI(m_rsCompound!FcstId, m_rsCompound!FcstDesc, _
                        m_rsCompound!FcstLocked, m_rsCompound!FcstEnabled, _
                        m_rsCompound!FcstHierarchy, m_rsCompound!FcstStartDate, m_rsCompound!LastUpdated, _
                        m_rsCompound!FcstRolling, m_rsCompound!FcstPds_Future, _
                        m_rsCompound!FcstHistory, m_rsCompound!FcstPds_Historical, _
                        m_rsCompound!FreezePds, m_rsCompound!FixedPds, _
                        m_rsCompound!ApplyTrend, m_rsCompound!Calc_SysFcst, _
                        m_rsCompound!Calc_Netreq, m_rsCompound!Calc_HistDmd, _
                        m_rsCompound!Calc_MasterFcstAdj, m_rsCompound!Calc_FcstAdj, m_rsCompound!Calc_AdjNetReq, _
                        m_rsCompound!Calc_ProjInv, m_rsCompound!Calc_ProdConst, m_rsCompound!FcstInterval, _
                        m_rsCompound!FcstUnit, m_rsCompound!Item, m_rsCompound!ItStat, _
                        m_rsCompound!VnId, m_rsCompound!Assort, m_rsCompound!ById, _
                        m_rsCompound!Class1, m_rsCompound!Class2, m_rsCompound!Class3, m_rsCompound!Class4, _
                        m_rsCompound!LcId, m_rsCompound!LStatus, m_rsCompound!LDivision, m_rsCompound!LRegion, m_rsCompound!LUserDefined)
                Case 2  'ForecastAccess
'                    RtnCode = RefreshForecastAccess(m_rsCompound)

                Case 3  'UserElements
'                    RtnCode = RefreshUserElements(m_rsCompound!FcstRepositoryID, m_rsCompound!RecordDesc)

                End Select
                m_rsCompound.MoveNext 'There should be no more for the first recordset
            Loop

            If p_Related = True Then
                Set m_rsCompound = m_rsCompound.NextRecordset
                Count = Count + 1
            Else
                Exit Do
            End If
        Loop

        'dgUserElements.Redraw = True
        Screen.MousePointer = vbNormal
    End If
    mFcstSetup_Fetch = True
CleanUp:
    If f_IsRecordsetValidAndOpen(m_rsCompound) Then m_rsCompound.Close
    Screen.MousePointer = vbNormal
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.mFcstSetup_Fetch)"
    f_HandleErr , , , "AIM_ForeCastPlanner::mFcstSetup_Fetch", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Function
Private Function mFcstSetup_RefreshUI( _
    p_FcstID As String, p_FcstDesc As String, _
    p_FcstLocked As Boolean, p_FcstEnabled As Boolean, _
    p_FcstHierarchy As Long, p_FcstStartDate As Date, p_LastUpdated As Date, _
    p_FcstRolling As Boolean, p_FcstPeriods_Future As Long, _
    p_FcstHistory As Boolean, p_FcstPeriods_Historical As Long, _
    p_FreezePds As Long, p_FixedPds As Long, _
    p_ApplyTrend As Long, p_Calc_SysFcst As Boolean, _
    p_Calc_NetReq As Boolean, p_Calc_HistDmd As Boolean, _
    p_Calc_MasterFcstAdj As Boolean, p_Calc_FcstAdj As Boolean, p_Calc_AdjNetReq As Boolean, _
    p_Calc_ProjInv As Boolean, p_Calc_ProdConst As Boolean, p_FcstInterval As Long, _
    p_FcstUnit As Long, p_Item As String, p_ItStat As String, _
    p_VnId As String, p_Assort As String, _
    p_ById As String, p_Class1 As String, _
    p_Class2 As String, p_Class3 As String, _
    p_Class4 As String, p_LcID As String, _
    p_LStatus As String, p_LDivision As String, _
    p_LRegion As String, p_LUserDefined As String _
) As Long
On Error GoTo ErrorHandler
    Dim IndexVar As Long, IndexCounter As Long
    Dim RtnCode As Long

    Me.dcForecastID.Text = p_FcstID
    m_FcstLocked = IIf(p_FcstLocked = False, vbUnchecked, vbChecked)
    m_FcstEnabled = IIf(p_FcstEnabled = False, vbUnchecked, vbChecked)
    m_FcstHierarchy = p_FcstHierarchy
    m_FcstStartDate = p_FcstStartDate
    m_ApplyTrend = p_ApplyTrend
    IndexVar = p_FcstInterval
    For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
        Me.optFcstInterval(IndexCounter).Enabled = True ' enable all options
    Next
    For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
        Me.optFcstInterval(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
    If IndexVar = 1 Then ' weeklyforecast so
        Me.optFcstInterval(0).Enabled = False 'daily forecast is not an option
    End If
     If IndexVar = 2 Then ' monthly forecast so
        Me.optFcstInterval(0).Enabled = False 'daily forecast is not an option
        Me.optFcstInterval(1).Enabled = False 'weekly forecast is not an option
        Me.optFcstInterval(3).Enabled = False 'quarterly forecast is not an option
        Me.optFcstInterval(4).Enabled = False '5-4-4 forecast is not an option
        Me.optFcstInterval(5).Enabled = False '4-5-4 forecast is not an option
        Me.optFcstInterval(6).Enabled = False '4-4-5 forecast is not an option
        Me.optFcstInterval(7).Enabled = False '4 weeks forecast is not an option
    End If

    For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
        Me.optFcstInterval(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
    m_FcstInterval = p_FcstInterval
    m_NewFcstInterval = p_FcstInterval
    m_FcstRolling = p_FcstRolling
    m_NbrPds_Hist = p_FcstPeriods_Historical
    m_NbrPds_Future = p_FcstPeriods_Future
    If m_FcstRolling = True Then
        m_TotalPeriods = m_NbrPds_Hist + m_NbrPds_Future
    Else
        m_TotalPeriods = m_FixedPds
    End If
    m_NewTotalPeriods = m_TotalPeriods
    m_FreezePds = p_FreezePds
    m_FixedPds = p_FixedPds
    If m_FcstRolling = True Then
        Me.txtFcstStartDate.Text = Format(GetRollingFcstStartDate(Now(), m_FcstStartDate, AC, m_FcstInterval), gDateFormat)
        'Me.txtFcstStartDate.Text = Format(GetRollingFcstStartDate(Now(), AC, m_FcstInterval), gDateFormat)
        m_FcstStartDate = Format(Me.txtFcstStartDate.Text, gDateFormat)
    Else
        Me.txtFcstStartDate.Text = Format(m_FcstStartDate, gDateFormat)
        'set the m_NbrPds_Future to  m_FixedPds and m_NbrPds_Hist to 0
        m_NbrPds_Future = m_FixedPds
        m_NbrPds_Hist = 0
    End If
    Me.txtFcstPeriods_Future.MinValue = 0
    Me.txtFcstPeriods_Future.MaxValue = p_FcstPeriods_Future
    Me.txtFcstPeriods_Future.Text = Format(p_FcstPeriods_Future, "##0")

    m_FcstHistory = p_FcstHistory
    Me.txtFcstPeriods_Historical.MinValue = 0
    Me.txtFcstPeriods_Historical.MaxValue = p_FcstPeriods_Historical
    Me.txtFcstPeriods_Historical.Text = Format(p_FcstPeriods_Historical, "##0")

    IndexVar = p_FcstUnit
    For IndexCounter = optFcstUnit.LBound To optFcstUnit.UBound
        Me.optFcstUnit(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
    m_FcstUnit = p_FcstUnit
    Me.ckDataCalc_Type(0).Value = IIf(p_Calc_SysFcst = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(1).Value = IIf(p_Calc_MasterFcstAdj = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(2).Value = IIf(p_Calc_FcstAdj = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(3).Value = IIf(p_Calc_NetReq = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(4).Value = IIf(p_Calc_AdjNetReq = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(5).Value = IIf(p_Calc_ProjInv = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(6).Value = IIf(p_Calc_HistDmd = False, vbUnchecked, vbChecked)
    Me.ckDataCalc_Type(7).Value = IIf(p_Calc_ProdConst = False, vbUnchecked, vbChecked)
     FcstTypeCount = 0 'Initail fcstcount
    If p_Calc_SysFcst = False Then
        Me.ckDataCalc_Type(0).Enabled = False
    Else
        Me.ckDataCalc_Type(0).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_MasterFcstAdj = False Then
        Me.ckDataCalc_Type(1).Enabled = False
    Else
        Me.ckDataCalc_Type(1).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_FcstAdj = False Then
        Me.ckDataCalc_Type(2).Enabled = False
    Else
        Me.ckDataCalc_Type(2).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_NetReq = False Then
        Me.ckDataCalc_Type(3).Enabled = False
    Else
        Me.ckDataCalc_Type(3).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_AdjNetReq = False Then
        Me.ckDataCalc_Type(4).Enabled = False
    Else
        Me.ckDataCalc_Type(4).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_ProjInv = False Then
        Me.ckDataCalc_Type(5).Enabled = False
    Else
        Me.ckDataCalc_Type(5).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_HistDmd = False Then
        Me.ckDataCalc_Type(6).Enabled = False
    Else
        Me.ckDataCalc_Type(6).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    If p_Calc_ProdConst = False Then
        Me.ckDataCalc_Type(7).Enabled = False
    Else
        Me.ckDataCalc_Type(7).Enabled = True
        FcstTypeCount = FcstTypeCount + 1
    End If
    RtnCode = g_SelectFilters(Cn, m_FcstSetupKey, m_xaOrigCriteria, COMP_EMPTY)
    g_CXArr1ToXArr2 m_xaOrigCriteria, m_xaAllCriteria
    'g_PrintCriteria m_xaOrigCriteria
    If RtnCode = SUCCEED Then
        Me.txtSelected.Text = g_CXArrToPara(m_xaOrigCriteria, False, False)
    Else
        Me.txtSelected.Text = COMP_EMPTY
    End If '   'A.Stocksdale - END - Multi-filters for item
    
    'Update form variables to reflect date ranges
    mSetPeriodBounds

    'Enable toolbar based on security access control
    mToggleToolbarAccess
    'Desible promote to master if the forecasttype is sandbox or if the m_NewFcstInterval is not 1 ie weeks
    If m_FcstHierarchy = 1 Then Me.tbFcstModification.Tools(TB_ID_PROMOTE).Enabled = False ' this is a sand box
    If m_FcstHierarchy = 1 Then ' for sand box masteroverride adj is not an option
        optAdjType_Dtl(ADJ_REMOVEMASTEROVERRIDE).Enabled = False
        optAdjType_Item(ADJ_REMOVEMASTEROVERRIDE).Enabled = False
    Else
        optAdjType_Dtl(ADJ_REMOVEMASTEROVERRIDE).Enabled = True
        optAdjType_Item(ADJ_REMOVEMASTEROVERRIDE).Enabled = True
    End If
    
'    If m_FcstHierarchy = 1 Then ' for sand box nothing can be promoted to master so this option is wrong
'                                ' even if it is there it will return same values as sysfcst
'        Me.ckDataCalc_Type(1).Value = False
'        Me.ckDataCalc_Type(1).Enabled = False
'    Else
'    End If
    If m_NewFcstInterval <> int_Weeks Then Me.tbFcstModification.Tools(TB_ID_PROMOTE).Enabled = False
 
    Populate_FcstDates m_FcstInterval, CInt(txtFcstPeriods_Historical.Text), CInt(txtFcstPeriods_Future.Text), CDate(txtFcstStartDate.Text), m_FcstDates()
    'Fill_FcstDates
'    dcDate_Start_Initilize
    mStartDate_Load
    mEndDate_Load
    'Return success
    mFcstSetup_RefreshUI = RtnCode
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(mFcstSetup_RefreshUI)"
     f_HandleErr , , , "AIM_ForeCastPlanner::mFcstSetup_RefreshUI", Now, gDRGeneralError, True, Err
End Function
Private Function ClearGrids() As Boolean
On Error GoTo ErrorHandler
    MainArray.ReDim 0, 0, 0, 0
    MainArray.Clear
    
    MainArrayFiltered.ReDim 0, 0, 0, 0
    MainArrayFiltered.Clear
    Set dgItem.Array = MainArrayFiltered
    dgItem.ReBind
    dgItem.Update
    dgItem.ClearFields
    
    SummaryArray.ReDim 0, 0, 0, 0
    SummaryArray.Clear
    Set dgSummary.Array = SummaryArray
    dgSummary.ReBind
    dgSummary.Update
    dgSummary.ClearFields
    
    ItemDetailArray.ReDim 0, 0, 0, 0
    ItemDetailArray.Clear
    Set dgItemDetail.Array = ItemDetailArray
    dgItemDetail.ReBind
    dgItemDetail.Update
    dgItemDetail.ClearFields
    
    
    OverRideArray.ReDim 0, 0, 0, 0
    OverRideArray.Clear
    ItemAdjArray.ReDim 0, 0, 0, 0
    ItemAdjArray.Clear
    MainSubArray.ReDim 0, 0, 0, 0
    MainSubArray.Clear
 
    Set MainArray = Nothing
    Set MainArrayFiltered = Nothing
    Set SummaryArray = Nothing
    Set ItemDetailArray = Nothing
    Set OverRideArray = Nothing
    Set ItemAdjArray = Nothing
    Set MainSubArray = Nothing
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ClearGrids)"
    f_HandleErr , , , "AIM_ForeCastPlanner::ClearGrids", Now, gDRGeneralError, True, Err
End Function
Private Function gFetch_OverRideItems() As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    'Clear out any existing data
    If f_IsRecordsetValidAndOpen(m_rsOverRide) Then m_rsOverRide.Close
    'Init. the command object
    With AIM_RepositoryOverRide_Get_Sp
    'Set values
        .Parameters("@FcstId").Value = dcForecastID.Text
    End With

    'Init. the recordset
    
    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
    m_rsOverRide.Open AIM_RepositoryOverRide_Get_Sp

    RtnCode = AIM_RepositoryOverRide_Get_Sp.Parameters(0).Value

CleanUp:
    If f_IsRecordsetOpenAndPopulated(m_rsOverRide) Then
        RtnCode = 1
    Else
        RtnCode = -1
    End If
    gFetch_OverRideItems = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_OverRideItems)"
    f_HandleErr , , , "AIM_ForeCastPlanner::gFetch_OverRideItems", Now, gDRGeneralError, True, Err
End Function

Private Sub mSetPeriodBounds()
On Error GoTo ErrorHandler

Dim I As Integer   ' column index
Dim IMax As Integer
Dim PdEndDate As Date
Dim PdStartDate As Date
Dim StartPd As Integer
Dim EndPd As Integer


'm_NbrPds_Hist = IIf(txtFcstPeriods_Historical.Text = "", "0", txtFcstPeriods_Historical.Text)
'm_NbrPds_Future = IIf(txtFcstPeriods_Future.Text = "", "0", txtFcstPeriods_Future.Text)
'If m_FcstHierarchy = 1 Then 'Sand Box
'm_FcstStartDate = txtFcstStartDate.Text
'ElseIf m_FcstHierarchy = 2 Then  'Live
'm_FcstStartDate = Date + 7
'End If

m_RollingFcstStartDate = GetRollingFcstStartDate(m_FcstStartDate, m_FcstStartDate, AC, m_FcstInterval)
m_HistoricalStartDate = GetHistoricalFcstStartDate(m_RollingFcstStartDate, m_NbrPds_Hist, AC, m_FcstInterval)
m_FreezePdDate = GetHistoricalFcstStartDate(m_RollingFcstStartDate, -m_FreezePds, AC, m_FcstInterval)
If m_FcstRolling = False Then
        m_RealFreezePdDate = GetHistoricalFcstStartDate(GetRollingFcstStartDate(Now(), m_FcstStartDate, AC, m_FcstInterval), _
        -m_FreezePds, AC, m_FcstInterval)
    Else '
        m_RealFreezePdDate = m_FreezePdDate
    End If
'm_RealFreezePdDate=
m_FuturePdEndDate = GetHistoricalFcstStartDate(m_RollingFcstStartDate, -m_NbrPds_Future, AC, m_FcstInterval) - 1
PdStartDate = m_HistoricalStartDate
PdEndDate = m_HistoricalStartDate


ReDim m_FcstPdDates(0 To m_TotalPeriods)

For I = 0 To m_TotalPeriods
If I = 0 Then
         m_FcstPdDates(0) = m_HistoricalStartDate
     ElseIf I = 1 And m_FcstInterval <> m_NewFcstInterval And m_NewFcstInterval = int_months Then
        m_FcstPdDates(1) = GetNextRollingFcstStartDate(m_HistoricalStartDate, AC, m_NewFcstInterval)
        PdEndDate = m_FcstPdDates(1)
        PdStartDate = PdEndDate
    Else
          PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, m_NewFcstInterval)
         If m_NewFcstInterval <> int_months Or m_NewFcstInterval <> int_Quarters Then
             StartPd = AC.GetPeriod(PdEndDate)
             EndPd = AC.GetPeriod(PdEndDate - 7)
             If StartPd = 52 And EndPd = 52 Then PdEndDate = AC.GetEndDate(PdEndDate) + 1
         End If
          If PdEndDate >= m_FuturePdEndDate Then
                m_FcstPdDates(I) = m_FuturePdEndDate + 1
                IMax = I
                Exit For
            Else
            m_FcstPdDates(I) = PdEndDate
            End If
        
         PdStartDate = PdEndDate
         IMax = I
    End If
Next

ReDim Preserve m_FcstPdDates(0 To IMax)
m_NewTotalPeriods = UBound(m_FcstPdDates())
'm_TotalPeriods = UBound(m_FcstPdDates())
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(mSetPeriodBounds)"
    f_HandleErr , , , "AIM_ForeCastPlanner::mSetPeriodBounds", Now, gDRGeneralError, True, Err
End Sub
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Sets default values for the chart/graph
' .......................................................................
'   Returns false if the process failed
'   else true after redrawing the chart
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function mChart_Initialize()
On Error GoTo ErrorHandler
    Dim Counter As Long
    With Me.chtForecast
        .IsBatched = True       'Disable the control from repainting until the process has finished.
        .AllowUserChanges = False
        
        'Set up Header
        .Header.Text = ""
        .Header.Font.Bold = True
        .Border.Type = oc2dBorderPlain
        .Border.Width = 1
        
        'Set up Legends
        .Legend.Anchor = oc2dAnchorWest
        .Legend.IsShowing = True
        .Legend.Border.Type = oc2dBorderPlain
        .Legend.Border.Width = 1
        .Legend.Text = getTranslationResource("Forecast Types")
        
        'Set up Chart Area X and Y Axes
        .ChartGroups(1).IsStacked = False
        .ChartGroups(1).SeriesLabels.RemoveAll
        .ChartGroups(1).Styles.RemoveAll
        .ChartGroups(1).PointStyles.RemoveAll
        'Remove any old x-axis labels
        .ChartArea.Axes("X").ValueLabels.RemoveAll
        .ChartArea.Axes("Y").ValueLabels.RemoveAll
        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
        .ChartArea.Axes("X").AnnotationRotation = oc2dRotate90Degrees
        .ChartArea.Axes("X").TitleRotation = oc2dRotate90Degrees
        .ChartArea.Axes("X").Title.Text = getTranslationResource("Periods")
'        .ChartArea.Axes("X").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("X").MajorGrid.Spacing = 100
'        .ChartArea.Axes("X").MajorGrid.Style.Pattern = oc2dLineDotted
        
        .ChartArea.Axes("Y").AnnotationMethod = oc2dAnnotateValues
        .ChartArea.Axes("Y").TitleRotation = oc2dRotateNone
        .ChartArea.Axes("Y").Origin = 0
        .ChartArea.Axes("Y").Title.Text = getTranslationResource("Value")
'        .ChartArea.Axes("Y").AxisStyle.LineStyle.Width = 1
'        .ChartArea.Axes("Y").MajorGrid.Spacing = 100
'        .ChartArea.Axes("Y").MajorGrid.Style.Pattern = oc2dLineSolid
        
        .ChartArea.Axes("Y2").IsShowing = True
        .ChartArea.Axes("Y2").Multiplier = 1

    End With
CleanUp:
    Me.chtForecast.IsBatched = False  'Process is done. Repaint control.
    m_IsChartVisible = True

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(mChart_Initialize)", Err.Description
    f_HandleErr , , , "AIM_ForeCastPlanner::mChart_Initialize", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Function
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Show/hide chart control based on current state.
' .......................................................................
'   Returns false if the process failed
'   else true after resizing the chart
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function mChart_ToggleSize()
On Error GoTo ErrorHandler
    If Me.chtForecast.Height > 75 Then
        Me.chtForecast.Height = 75
        Me.fraCommon.Height = 1076
        'Set visibility flag
        m_IsChartVisible = False
    Else
        Me.chtForecast.Height = 4000
        Me.fraCommon.Height = 5000
        'Set visibility flag
        m_IsChartVisible = True
    End If
    mForm_Rearrange
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SizeChart)"
     f_HandleErr , , , "AIM_ForeCastPlanner::mChart_ToggleSize", Now, gDRGeneralError, True, Err
End Function

Private Function CheckOverRide(p_LcID As String, p_Item As String, P_StartDate As Date) As Boolean
On Error GoTo ErrorHandler
'Check to see if an override exists for a particular Lcid and Item and StartDate
Dim RtnCode As Boolean
Dim I As Long
RtnCode = False
If OverRideArray.Count(1) = 0 Then
    CheckOverRide = False
    Exit Function
End If
For I = 0 To OverRideArray.UpperBound(1)
If OverRideArray(I, 0) = p_LcID And OverRideArray(I, 1) = p_Item And OverRideArray(I, 2) = P_StartDate Then
CheckOverRide = True
Exit Function
End If
Next
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.CheckOverRide)"
    f_HandleErr , , , "AIM_ForeCastPlanner::CheckOverRide", Now, gDRGeneralError, True, Err
End Function
Private Function Fill_FcstTypes()
On Error GoTo ErrorHandler
Dim I As Integer
Dim J As Integer
'Populates the m_FcstTypes array with the FcstTypes that were selected for display
'Also sets the position of FCSTADJ  FcstTypes
'Call this function to automatically select dataelemets if there are any useelements that do not have there data
'calcualted
Call AddDataElements

ReDim m_FcstTypes(0 To ckDataCalc_Type.UBound)
J = 0
For I = 0 To ckDataCalc_Type.UBound
    If ckDataCalc_Type(I).Value = 1 Then
        If I = 0 Then
            m_FcstTypes(J) = DT_SYSFCST
            J = J + 1
        ElseIf I = 1 Then
            m_FcstTypes(J) = DT_MASTERFCSTADJ
            J = J + 1
        ElseIf I = 2 Then
        m_FcstTypes(J) = DT_FCSTADJ
            m_FcstAdjPos = J              'Get the m_FcstAdjPos
            J = J + 1
        ElseIf I = 3 Then
        m_FcstTypes(J) = DT_NETREQ
            J = J + 1
        ElseIf I = 4 Then
        m_FcstTypes(J) = DT_ADJNETREQ
            J = J + 1
        ElseIf I = 5 Then
        m_FcstTypes(J) = DT_PROJINV
             J = J + 1
         ElseIf I = 6 Then
        m_FcstTypes(J) = DT_HISDMD
             J = J + 1
        ElseIf I = 7 Then
        m_FcstTypes(J) = DT_PRODCONST
             J = J + 1
        End If
    End If
Next I
If J <> 0 Then
    ReDim Preserve m_FcstTypes(0 To J - 1)
End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Fill_FcstTypes)"
    f_HandleErr , , , "AIM_ForeCastPlanner::Fill_FcstTypes", Now, gDRGeneralError, True, Err
End Function
Private Function Fill_UserElements()
'Populate the selected userelements into m_UESelected array
On Error GoTo ErrorHandler
Dim I As Integer
Dim J As Integer
Dim bm As Variant
Dim NumUserElements As Integer

ReDim m_UESelected(0 To UBound(m_UserElements()))
J = 0
dgUserElements.MoveFirst
If dgUserElements.BOF = True And dgUserElements.eof = True Then GoTo GetOut
'If UBound(m_UserElements()) = 0 Then
'    NumUserElements = 0
'Else
'End If
'For i = 0 To UBound(m_UserElements()) - 1
For I = 0 To UBound(m_UserElements())
    bm = dgUserElements.GetBookmark(I)
    If dgUserElements.Columns(0).CellValue(bm) = True Then
      m_UESelected(J) = dgUserElements.Columns(1).CellValue(bm)
      J = J + 1
    End If
Next I
GetOut:
If J > 0 Then
    ReDim Preserve m_UESelected(0 To J - 1)
Else
    ReDim Preserve m_UESelected(0 To J)
End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Fill_UserElements)"
     f_HandleErr , , , "AIM_ForeCastPlanner::Fill_UserElements", Now, gDRGeneralError, True, Err
End Function
Private Function Fill_FcstTypesAndUserElements()
'This Function called to fill the FcstTypes array with the selected UserElements
'This function should be called after the call to fill the xarrays
On Error GoTo ErrorHandler
Dim I As Integer
Dim UserElementSelected As Integer
Dim FcstTypesSelected As Integer

If UBound(m_UESelected()) = 0 Then
    If m_UESelected(0) = "" Or IsNull(m_UESelected(0)) Then
      'There are no user elements selected  so no need to add to FcstTypes array
      Exit Function
    End If
End If
'If it comes here then there are userelements that need to be populated
UserElementSelected = UBound(m_UESelected()) + 1
FcstTypesSelected = UBound(m_FcstTypes()) + 1

ReDim Preserve m_FcstTypes(0 To FcstTypesSelected + UserElementSelected - 1)

For I = 0 To UserElementSelected - 1
    m_FcstTypes(FcstTypesSelected + I) = m_UESelected(I)
Next I

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Fill_FcstTypesAndUserElements)"
     f_HandleErr , , , "AIM_ForeCastPlanner::Fill_FcstTypesAndUserElements", Now, gDRGeneralError, True, Err
End Function
Private Function PopulateUserElements() As Integer
On Error GoTo ErrorHandler

    Dim I As Integer
    Dim p As Integer
    Dim ColIdx As Integer
    
    If f_IsRecordsetValidAndOpen(m_rsUserElements) Then m_rsUserElements.Close
    
    'Initialize the System Control Recordset
    With AIM_UserElements_Get_Sp
         .Parameters("@FcstId").Value = m_FcstId
    End With
    With m_rsUserElements
        .Open AIM_UserElements_Get_Sp
    End With
    
    I = 0
    'ReDim m_UserElements(0)
    ReDim m_UserElements(0 To 100)

    If f_IsRecordsetOpenAndPopulated(m_rsUserElements) Then
        m_rsUserElements.MoveFirst
       Do Until m_rsUserElements.eof
            m_UserElements(I).SelOpt = False
            m_UserElements(I).UserElementId = m_rsUserElements("UserElementId").Value
            m_UserElements(I).UserElementDesc = m_rsUserElements("UserElementDesc").Value
            m_UserElements(I).Processed_YN = m_rsUserElements("Processed_YN").Value
            m_UserElements(I).FcstType = m_rsUserElements("FcstType").Value
            I = I + 1

            If I > UBound(m_UserElements()) Then
                ReDim Preserve m_UserElements(UBound(m_UserElements) + 10)
            End If

            m_rsUserElements.MoveNext
        Loop
    Else
    UserElementArray.Clear
    UserElementArray.ReDim 0, 0, 0, 4
    Set UserElementArray = Nothing
    GoTo ReBind
    Exit Function
    End If
    
    'Truncate the array
    If I > 0 Then I = I - 1
    ReDim Preserve m_UserElements(0 To I)
    UserElementArray.ReDim 0, I, 0, 4
    
    For p = 0 To I
        UserElementArray.Value(p, 0) = m_UserElements(p).SelOpt
        UserElementArray.Value(p, 1) = m_UserElements(p).UserElementId
        UserElementArray.Value(p, 2) = m_UserElements(p).UserElementDesc
        UserElementArray.Value(p, 3) = m_UserElements(p).Processed_YN
        UserElementArray.Value(p, 4) = m_UserElements(p).FcstType
    Next
ReBind:
    dgUserElements.Columns(0).ValueItems.Presentation = dbgCheckBox
    dgUserElements.Splits(0).Columns(0).Alignment = dbgCenter
    dgUserElements.Splits(0).Columns(0).Width = 500
    For ColIdx = 0 To 4 '  5 columns in the userelements
        dgUserElements.Columns(ColIdx).Caption = getTranslationResource(dgUserElements.Columns(ColIdx).Caption)
    Next ColIdx
    For ColIdx = 3 To 4 'Hide columns 3 and 4 for userelement grid
        dgUserElements.Columns(ColIdx).Visible = False
    Next ColIdx
    Set dgUserElements.Array = UserElementArray
    dgUserElements.FetchRowStyle = True
    dgUserElements.ReBind
    dgUserElements.Update
CleanUp:
    If f_IsRecordsetValidAndOpen(m_rsUserElements) Then m_rsUserElements.Close
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateUserElements)"
     f_HandleErr , , , "AIM_ForeCastPlanner::PopulateUserElements", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Function
Private Function AddDataElements() As Integer

On Error GoTo ErrorHandler
'Selects dataelements automatically if there are any userelemets which does not have there datacalculated
Dim I As Integer
Dim bm As Variant
m_SaveUserElement = False
dgUserElements.MoveFirst

If dgUserElements.BOF = True And dgUserElements.eof = True Then Exit Function
Do
    'For I = 0 To dgUserElements.Rows - 1
        bm = dgUserElements.GetBookmark(0)
        If IsNull(bm) Then Exit Function
        If dgUserElements.Columns(3).CellValue(bm) = 0 Then
            m_SaveUserElement = True
            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "SYSFCST" Then Me.ckDataCalc_Type(0).Value = vbChecked
             If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "MASTERFCSTADJ" Then Me.ckDataCalc_Type(1).Value = vbChecked
            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "FCSTADJ" Then Me.ckDataCalc_Type(2).Value = vbChecked
            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "NETREQ" Then Me.ckDataCalc_Type(3).Value = vbChecked
            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "ADJNETREQ" Then Me.ckDataCalc_Type(4).Value = vbChecked
            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "PROJINV" Then Me.ckDataCalc_Type(5).Value = vbChecked
             If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "HISDMD" Then Me.ckDataCalc_Type(6).Value = vbChecked
            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "PRODCONST" Then Me.ckDataCalc_Type(7).Value = vbChecked
        End If
        dgUserElements.MoveNext
  Loop While dgUserElements.eof = False

CleanUp:
    'Windup
    Write_Message ""
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Me.ckDataCalc_Type(7).Value = )"
    f_HandleErr , , , "AIM_ForeCastPlanner::AddDataElements", Now, gDRGeneralError, True, Err
    GoTo CleanUp:


'On Error GoTo ErrorHandler
''Selects dataelements automatically if there are any userelemets which does not have there datacalculated
'Dim I As Integer
'Dim bm As Variant
'm_SaveUserElement = False
'dgUserElements.MoveFirst
'If dgUserElements.VisibleRows = 0 Then Exit Function
'For I = 0 To dgUserElements.VisibleRows
'    'For I = 0 To dgUserElements.Rows - 1
'        bm = dgUserElements.GetBookmark(I)
'        If IsNull(bm) Then Exit Function
'        If dgUserElements.Columns(3).CellValue(bm) = 0 Then
'            m_SaveUserElement = True
'            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "SYSFCST" Then Me.ckDataCalc_Type(0).Value = vbChecked
'             If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "MASTERFCSTADJ" Then Me.ckDataCalc_Type(1).Value = vbChecked
'            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "FCSTADJ" Then Me.ckDataCalc_Type(2).Value = vbChecked
'            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "NETREQ" Then Me.ckDataCalc_Type(3).Value = vbChecked
'            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "ADJNETREQ" Then Me.ckDataCalc_Type(4).Value = vbChecked
'            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "PROJINV" Then Me.ckDataCalc_Type(5).Value = vbChecked
'             If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "HISDMD" Then Me.ckDataCalc_Type(6).Value = vbChecked
'            If (UCase(LTrim(RTrim(dgUserElements.Columns(4).CellValue(bm))))) = "PRODCONST" Then Me.ckDataCalc_Type(7).Value = vbChecked
'        End If
'    Next I
'
'CleanUp:
'    'Windup
'    Write_Message ""
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.Source, Err.Description & "(Me.ckDataCalc_Type(7).Value = )"
'    GoTo CleanUp:
End Function


Private Function SetEffective_Periods(Index As Integer) As Integer
On Error GoTo ErrorHandler
'Index values are defied as
'1 is Summary
'2 is Item
'3 is ItemDetail

Dim RtnCode As Long
Dim I As Integer
Dim StartDate As Date
Dim EndDate As Date

'check if there is any data in the detail grid if there is no data do not run any of this code
If Not MainArrayFiltered.Count(1) > 0 Then
 Exit Function
End If

Select Case Index
Case 1 'Summary
    StartDate = CDate(Me.dcDate_Start(0).Text)
    EndDate = CDate(Me.dcDate_End(0).Text) + 1
    For I = m_SummaryDataStartPos + m_NbrPds_Hist + m_FreezePds To dgSummary.Columns.Count - 1
        If IsDate(dgSummary.Columns(I).Caption) Then
            If CDate(dgSummary.Columns(I).Caption) >= m_RealFreezePdDate Then
                If CDate(dgSummary.Columns(I).Caption) >= StartDate And _
                    CDate(dgSummary.Columns(I).Caption) < EndDate Then
                    dgSummary.Columns(I).HeadBackColor = &HC0FFFF
                Else
                    dgSummary.Columns(I).HeadBackColor = RGB(255, 255, 255)
                End If
            Else
                dgSummary.Columns(I).HeadBackColor = RGB(192, 192, 192)
            End If
        End If
    Next

Case 2 'Item
    StartDate = CDate(Me.dcDate_Start(1).Text)
    EndDate = CDate(Me.dcDate_End(1).Text) + 1
    For I = m_DetailDataStartPos + m_NbrPds_Hist + m_FreezePds To dgItem.Columns.Count - 1
        If IsDate(dgItem.Columns(I).Caption) Then
            If CDate(dgItem.Columns(I).Caption) >= m_RealFreezePdDate Then
                If CDate(dgItem.Columns(I).Caption) >= StartDate And _
                    CDate(dgItem.Columns(I).Caption) < EndDate Then
                    dgItem.Columns(I).HeadBackColor = &HC0FFFF
                Else
                    dgItem.Columns(I).HeadBackColor = RGB(255, 255, 255)
                End If
            Else
                dgItem.Columns(I).HeadBackColor = RGB(192, 192, 192)
            End If
        End If
    Next
Case 3 'ItemDetail
    StartDate = CDate(Me.dcDate_Start(2).Text)
    EndDate = CDate(Me.dcDate_End(2).Text) + 1
    For I = m_ItemDetailDataStartPos + m_NbrPds_Hist + m_FreezePds To dgItemDetail.Columns.Count - 1
        If IsDate(dgItemDetail.Columns(I).Caption) Then
            If CDate(dgItemDetail.Columns(I).Caption) >= m_RealFreezePdDate Then
                If CDate(dgItemDetail.Columns(I).Caption) >= StartDate And _
                    CDate(dgItemDetail.Columns(I).Caption) < EndDate Then
                    dgItemDetail.Columns(I).HeadBackColor = &HC0FFFF
                Else
                    dgItemDetail.Columns(I).HeadBackColor = RGB(255, 255, 255)
                End If
            Else
                dgItemDetail.Columns(I).HeadBackColor = RGB(192, 192, 192)
            End If
        End If
    Next
End Select
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetEffective_Periods)"
     f_HandleErr , , , "AIM_ForeCastPlanner::SetEffective_Periods", Now, gDRGeneralError, True, Err
End Function
Private Function Check_ZeroAdj() As Integer
On Error GoTo ErrorHandler
Dim RtnCode As Integer
RtnCode = -1
Select Case UCase$(atModification.SelectedTab.Key)
    Case TAB_SUMMARY
        If optAdjType_Sum(ADJ_percent).Value = True Then
            If CDbl(txtAdjQty_Smr.Text) = 0 Then
            GoTo CleanUp:
            End If
        End If
    Case TAB_DETAIL
        If optAdjType_Dtl(ADJ_UNITS).Value = True Or optAdjType_Dtl(ADJ_percent).Value = True Then
            If CDbl(txtAdjQty_Dtl.Text) = 0 Then
             GoTo CleanUp:
            End If
        End If
    Case TAB_ITEMDETAIL
        If optAdjType_Item(ADJ_UNITS).Value = True Or optAdjType_Item(ADJ_percent).Value = True Then
            If CDbl(txtAdjQty_Item.Text) = 0 Then
            GoTo CleanUp:
            End If
            'Good
        End If
End Select
RtnCode = 0
CleanUp:
    Screen.MousePointer = vbNormal
    Check_ZeroAdj = RtnCode
Exit Function
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Check_ZeroAdj)"
    f_HandleErr , , , "AIM_ForeCastPlanner::Check_ZeroAdj", Now, gDRGeneralError, True, Err
End Function

Private Function Check_Reason() As Integer
On Error GoTo ErrorHandler
Dim RtnCode As Integer
RtnCode = -1
Select Case UCase$(atModification.SelectedTab.Key)
    Case TAB_SUMMARY
        If txtAdjDesc_Smr.Text = "" Then GoTo CleanUp
        
    Case TAB_DETAIL
    If txtAdjDesc_Dtl.Text = "" Then GoTo CleanUp
    Case TAB_ITEMDETAIL
    If txtAdjDesc_Item.Text = "" Then GoTo CleanUp
End Select
RtnCode = 0
CleanUp:
    Screen.MousePointer = vbNormal
    Check_Reason = RtnCode
Exit Function
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Check_Reason)"
    f_HandleErr , , , "AIM_ForeCastPlanner::Check_Reason", Now, gDRGeneralError, True, Err
End Function
Private Function PopulateMainSummary() As Integer
On Error GoTo ErrorHandler
Dim Total As Double
Dim I As Long
Dim J As Long
Dim Count As Long
'Populate the totals field
Count = MainArray.UpperBound(1)
For I = 0 To Count
    Total = 0
    For J = m_DetailDataStartPos To m_DetailDataStartPos + m_NewTotalPeriods - 1 ' data starts from column m_DetailDataStartPos
        Total = Total + MainArray(I, J)
    Next
    MainArray(I, m_DetailTotalPos) = Total       'Total field is 4
Next
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(PopulateMainSummary)"
    f_HandleErr , , , "AIM_ForeCastPlanner::PopulateMainSummary", Now, gDRGeneralError, True, Err
End Function

Private Function Create_Detail_Split()
On Error GoTo ErrorHandler
Dim c As TrueOleDBGrid80.Column
Dim s As TrueOleDBGrid80.Split
Dim Cols As TrueOleDBGrid80.Columns

Set Cols = dgItem.Splits(0).Columns
For Each c In Cols
    c.Visible = False
Next c

Cols(0).Visible = True  'Lcid
Cols(1).Visible = True  'Item
Cols(2).Visible = True  'ItemDesc
Cols(3).Visible = True  'DataType

' Configure Splits(1) to display exactly two columns, and
' disable resizing
' Hide all columns in Splits(1) except for columns 0 to 3
With dgItem.Splits(0)
   .SizeMode = dbgNumberOfColumns
    .Size = 4       'First 4 columns should be in the split
    .AllowSizing = False
End With
' Make columns 0 and 1 invisible in splits 0 and 2
' Show all columns in Splits(1) except for columns 0 to 3
Set Cols = dgItem.Splits(1).Columns
Cols(0).Visible = False     'Lcid
Cols(1).Visible = False     'Item
Cols(2).Visible = False     'ItemDesc
Cols(3).Visible = False     'DataType

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Create_Detail_Split)"
    f_HandleErr , , , "AIM_ForeCastPlanner::Check_Reason", Now, gDRGeneralError, True, Err
End Function

Private Function Create_ItemDetail_Split()
On Error GoTo ErrorHandler
Dim c As TrueOleDBGrid80.Column
' Hide all columns in Splits(1) except for columns 0 to 3
Dim s As TrueOleDBGrid80.Split
Dim Cols As TrueOleDBGrid80.Columns

Set Cols = dgItemDetail.Splits(0).Columns
For Each c In Cols
    c.Visible = False
Next c

Cols(0).Visible = True 'Lcid
Cols(1).Visible = True  'Item
Cols(2).Visible = True  'ItemDesc
Cols(3).Visible = True  'DataType
' Configure Splits(1) to display columns 0 to 3 , and
' disable resizing
With dgItemDetail.Splits(0)
   .SizeMode = dbgNumberOfColumns
    .Size = 4       'Split1 will have 4 columns
    .AllowSizing = False
End With
' Make columns 0 to 3 invisible in splits 2
Set Cols = dgItemDetail.Splits(1).Columns
Cols(0).Visible = False 'Lcid
Cols(1).Visible = False 'Item
Cols(2).Visible = False 'ItemDesc
Cols(3).Visible = False 'DataType
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Create_Detail_Split)"
End Function
Private Function Create_Summary_Split()
On Error GoTo ErrorHandler
Dim c As TrueOleDBGrid80.Column
Dim s As TrueOleDBGrid80.Split
Dim Cols As TrueOleDBGrid80.Columns
Set Cols = dgSummary.Splits(0).Columns

For Each c In Cols
    c.Visible = False
Next c

' Hide all columns in Splits(1) except for columns 0 and 1
Cols(0).Visible = True  'Group By dropdown selected Preference
Cols(1).Visible = True  'DataType
' Configure Splits(1) to display exactly two columns, and
' disable resizing
With dgSummary.Splits(0)
   .SizeMode = dbgNumberOfColumns
    .Size = 2
    .AllowSizing = False
End With

'Usually, if you keep columns 0 and 1 from scrolling in one split, you will want to make them invisible in the other splits:

' Make columns 0 and 1 invisible in splits 2
Set Cols = dgSummary.Splits(1).Columns
Cols(0).Visible = False     'Group By dropdown selected Preference
Cols(1).Visible = False     'DataType

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Create_Summary_Split)"
End Function


Private Function Create_Detail_Grid()
On Error GoTo ErrorHandler
Dim I As Integer   ' column index
Dim J As Long      ' row index
Dim c As TrueOleDBGrid80.Column
Dim PdEndDate As Date
Dim PdStartDate As Date
Dim StartPd As Integer
Dim EndPd As Integer
'Populate_FcstDates
dgItem.ClearFields
PdStartDate = m_HistoricalStartDate
PdEndDate = m_HistoricalStartDate
m_DataEndPos = m_DetailTotalPos + m_NewTotalPeriods - 1

For I = 0 To 20 + m_NewTotalPeriods
    Set c = dgItem.Columns.Add(I)
    c.Visible = True
    If I >= m_DetailDataStartPos And I < m_DetailDataStartPos + m_NewTotalPeriods Then
        c.Caption = CStr(m_FcstPdDates(I - m_DetailDataStartPos))
        If I >= m_DetailDataStartPos And CDate(c.Caption) <= m_FreezePdDate Then
            c.HeadBackColor = RGB(192, 192, 192)
        ElseIf I >= m_DetailDataStartPos And CDate(c.Caption) > m_FreezePdDate Then
            c.HeadBackColor = vbWhite
        End If
    Else
        If I = m_LcID_Pos Then
            c.Caption = getTranslationResource("LcId")
        ElseIf I = m_Item_Pos Then
            c.Caption = getTranslationResource("Item")
        ElseIf I = 2 Then
            c.Caption = getTranslationResource("ItemDesc")
            m_DtlCol_ItDesc = I
        ElseIf I = 3 Then
            c.Caption = getTranslationResource("DataType")
            m_DtlCol_DataType = I
        ElseIf I = 4 Then
            c.Caption = getTranslationResource("Totals")
            m_DtlCol_Totals = I
        ElseIf I = m_NewTotalPeriods + 5 Then
            c.Caption = getTranslationResource("Class1")
            m_DtlCol_Class1 = I
        ElseIf I = m_NewTotalPeriods + 6 Then
            c.Caption = getTranslationResource("Class2")
            m_DtlCol_Class2 = I
        ElseIf I = m_NewTotalPeriods + 7 Then
            c.Caption = getTranslationResource("Class3")
            m_DtlCol_Class3 = I
        ElseIf I = m_NewTotalPeriods + 8 Then
            c.Caption = getTranslationResource("Class4")
            m_DtlCol_Class4 = I
        ElseIf I = m_NewTotalPeriods + 9 Then
            c.Caption = getTranslationResource("ItStat")
            m_DtlCol_ItStat = I
        ElseIf I = m_NewTotalPeriods + 10 Then
            c.Caption = getTranslationResource("VelCode")
            m_DtlCol_VelCode = I
        ElseIf I = m_NewTotalPeriods + 11 Then
            c.Caption = getTranslationResource("VnId")
            m_DtlCol_VnID = I
        ElseIf I = m_NewTotalPeriods + 12 Then
            c.Caption = getTranslationResource("Assort")
            m_DtlCol_Assort = I
        ElseIf I = m_NewTotalPeriods + 13 Then
            c.Caption = getTranslationResource("ById")
            m_DtlCol_ByID = I
        ElseIf I = m_NewTotalPeriods + 14 Then
            c.Caption = getTranslationResource("LDivision")
            m_DtlCol_LDivision = I
        ElseIf I = m_NewTotalPeriods + 15 Then
            c.Caption = getTranslationResource("LStatus")
            m_DtlCol_LStatus = I
        ElseIf I = m_NewTotalPeriods + 16 Then
            c.Caption = getTranslationResource("LRegion")
            m_DtlCol_LRegion = I
        ElseIf I = m_NewTotalPeriods + 17 Then
            c.Caption = getTranslationResource("LUserDefined")
            m_DtlCol_LUserDefined = I
        ElseIf I = m_NewTotalPeriods + 18 Then
            c.Caption = getTranslationResource("Cube")
            m_CubePos = I
        ElseIf I = m_NewTotalPeriods + 19 Then
            c.Caption = getTranslationResource("Weight")
            m_WeightPos = I
        ElseIf I = m_NewTotalPeriods + 20 Then
            c.Caption = getTranslationResource("Price")
            m_PricePos = I
        End If
    End If
Next
dgItem.Columns(21 + m_NewTotalPeriods).Caption = getTranslationResource("Cost")
m_CostPos = m_NewTotalPeriods + 21
dgItem.Columns(22 + m_NewTotalPeriods).Caption = getTranslationResource("ModifiedYN")
m_DtlCol_ModifiedYN = m_NewTotalPeriods + 22
Create_Detail_Split
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Create_Detail_Grid)"
End Function
Private Function RollBackAdjToFilteredArray(ArgFromArray As XArrayDB, ArgToArray As XArrayDB) As Date
Dim I As Long
Dim II As Long
Dim J As Long
Dim L As Long
Dim FcstTypeCount As Long
Dim FromArrayColCount As Long
Dim FromArrayRowCount As Long
Dim ToArrayRowCount As Long
FcstTypeCount = UBound(m_FcstTypes())
FromArrayColCount = ArgFromArray.UpperBound(2)
FromArrayRowCount = ArgFromArray.UpperBound(1)
ToArrayRowCount = ArgToArray.UpperBound(1)
'Here ArgToArray is MainArray
'And ArgFromArray is MainArrayFiltered
For I = ArgFromArray.LowerBound(1) To FromArrayRowCount Step FcstTypeCount + 1
    For II = ArgToArray.LowerBound(1) To ToArrayRowCount Step FcstTypeCount + 1
        If ArgFromArray(I, m_LcID_Pos) = ArgToArray(II, m_LcID_Pos) And ArgFromArray(I, m_Item_Pos) = ArgToArray(II, m_Item_Pos) Then
            For L = 0 To 0 'my be we can remove this loop
                For J = m_DetailDataStartPos To FromArrayColCount
                    ' setting adjfcst field values to  MainArray values from MainArrayFiltered
                    ArgFromArray(I + m_FcstAdjPos, J) = ArgToArray(II + m_FcstAdjPos, J)
                Next J
            Next L

        Else
        End If
    Next II
Next I
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(RollBackAdjToFilteredArray)"
End Function
Private Function gSave_Adjustments( _
    p_RepositoryKey As String, p_FcstStoreKey As Long, _
    p_LcID As String, p_Item As String, _
    p_AdjustStartDate As Date, p_AdjustEndDate As Date, _
    p_AdjustType As Long, p_AdjustQty As Double, _
    p_OverRideEnabled As Integer, _
    p_AdjustDesc As String, p_AdjustUserID As String _
) As Integer
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    'Init. the command object
    With AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
    'Set values
        .Parameters("@RepositoryKey").Value = p_RepositoryKey
       ' .Parameters("@FcstStoreKey").Value = p_FcstStoreKey
        .Parameters("@LcID").Value = Trim$(p_LcID)
        .Parameters("@Item").Value = IIf(Trim$(p_Item) = "", Null, Trim$(p_Item))
        .Parameters("@AdjustType").Value = p_AdjustType
        .Parameters("@AdjustQty").Value = p_AdjustQty
        .Parameters("@OverRideEnabled").Value = p_OverRideEnabled
        .Parameters("@AdjustBegDate").Value = Format(p_AdjustStartDate, g_ISO_DATE_FORMAT)
        .Parameters("@AdjustEndDate").Value = Format(p_AdjustEndDate, g_ISO_DATE_FORMAT)
        .Parameters("@AdjustUserID").Value = p_AdjustUserID
        .Parameters("@AdjustDesc").Value = Trim$(p_AdjustDesc)
    'Run
        .Execute
    End With

    'Check return code -- 0 = fail; 1 = succeed
    RtnCode = AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.Parameters(0).Value

    'Set return value
    gSave_Adjustments = RtnCode

CleanUp:
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.gSave_Adjustments)"
End Function
Private Function ApplyAdjToDetailItemArray(ArgFromArray As XArrayDB, ArgToArray As XArrayDB, ArgMainArray As XArrayDB) As Date
Dim I As Long
Dim II As Long
Dim J As Long
Dim L As Long
'Here ArgToArray is MainArrayFiltered
'And ArgFromArray is ItemArrayDtail
'For I = ArgFromArray.LowerBound(1) To ArgFromArray.UpperBound(1) Step UBound(m_FcstTypes()) + 7 ' Six more new rows 3 for adj and 3 for master adj
' The I loop is removed because ItemArrayDetail will have only one set of data
    For II = ArgToArray.LowerBound(1) To ArgToArray.UpperBound(1) Step UBound(m_FcstTypes()) + 1
        If ArgFromArray(I, 0) = ArgToArray(II, 0) And ArgFromArray(I, 1) = ArgToArray(II, 1) Then
            For L = 0 To 0 'We can remove this loop because we are updating only AdjFcst field
                For J = ArgFromArray.LowerBound(2) To ArgFromArray.UpperBound(2)
                    ' setting adjfcst field values to  MainArrayFiltered values from MainArray
                    ArgToArray(II + m_FcstAdjPos, J) = ArgFromArray(I + m_FcstAdjPos, J)
                Next J
            Next L
        Else
        End If
    Next II
'Next I
' Now apply the same chages to the MainArray
'Here ArgToArray is MainArray
'And ArgFromArray is ItemArrayDtail
I = 0
'For I = ArgFromArray.LowerBound(1) To ArgFromArray.UpperBound(1) ' Step UBound(m_FcstTypes()) + 7 ' Six more new rows 3 for adj and 3 for master adj
'The I loop is removed because ItemArrayDetail will have only one set of data
    For II = ArgMainArray.LowerBound(1) To ArgMainArray.UpperBound(1) Step UBound(m_FcstTypes()) + 1
        If ArgFromArray(I, 0) = ArgMainArray(II, 0) And ArgFromArray(I, 1) = ArgMainArray(II, 1) Then
            For L = 0 To 0 'We can remove this loop because we are updating only AdjFcst field
                For J = ArgFromArray.LowerBound(2) To ArgFromArray.UpperBound(2)
                    ' setting adjfcst field values to  MainArrayFiltered values from MainArray
                    ArgMainArray(II + m_FcstAdjPos, J) = ArgFromArray(I + m_FcstAdjPos, J)
                Next J
            Next L
        Else
        End If
    Next II
'Next I
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ApplyAdjToDetailItemArray)"
End Function
Private Function ApplyAdjToDetailArray(ArgFromArray As XArrayDB, ArgToArray As XArrayDB) As Date

Dim I As Long
Dim II As Long
Dim J As Long
Dim L As Long

'Here ArgToArray is MainArray
'And ArgFromArray is MainArrayFiltered

'For each element in the MainArrayFiltered find the corresponding element in the MainArray and update the AdjRow
For I = ArgFromArray.LowerBound(1) To ArgFromArray.UpperBound(1) Step UBound(m_FcstTypes()) + 1
    For II = ArgToArray.LowerBound(1) To ArgToArray.UpperBound(1) Step UBound(m_FcstTypes()) + 1
        If ArgFromArray(I, 0) = ArgToArray(II, 0) And ArgFromArray(I, 1) = ArgToArray(II, 1) Then
            For L = 0 To 0 'We can remove this loop because we are updating only AdjFcst field
                For J = ArgFromArray.LowerBound(2) To ArgFromArray.UpperBound(2)
                    ' Setting adjfcst field values to  MainArrayFiltered values from MainArray
                    ArgToArray(II + m_FcstAdjPos, J) = ArgFromArray(I + m_FcstAdjPos, J)
                Next J
            Next L
        Else
        End If
    Next II
Next I
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ApplyAdjToDetailArray)"
End Function

Private Function CheckFcstData() As Integer
On Error GoTo ErrorHandler
Dim RtnCode As Integer
    With AIM_RepositoryDetail_Validate_Sp
        .Parameters("@FcstID").Value = Trim$(dcForecastID.Text)
    End With
    AIM_RepositoryDetail_Validate_Sp.Execute
    RtnCode = AIM_RepositoryDetail_Validate_Sp.Parameters(0).Value
    CheckFcstData = RtnCode
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.CheckFcstData)"
End Function

Private Function Initialize_StoredProcedures() As Integer
On Error GoTo ErrorHandler
    'Initialize Stored Procedures
    With AIM_RepositoryDetail_Validate_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RepositoryDetail_Validate_Sp"
        .Parameters.Refresh
    End With
    
    With AIM_ForecastRepository_Save_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_ForecastRepository_Save_Sp"
        .CommandType = adCmdStoredProc
    End With
    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@Return", adInteger, adParamReturnValue, , 0)
    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@UserID", adVarChar, adParamInput, 12, "")
    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@FcstID", adVarChar, adParamInput, 12, "")
    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@UserElement", adBoolean, adParamInput, , 0)
    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@RepositoryKey", adInteger, adParamOutput, , 0)
    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
    
    
     With AIM_UserElements_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_UserElements_Get_Sp"
        .Parameters.Refresh
    End With
     With AIM_RepositoryItemAdj_Get_Sp
        Set .ActiveConnection = Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RepositoryItemAdj_Get_Sp"
    'Declare parameters
        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("@FcstId", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("@Lcid", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_RepositoryItemAdj_Get_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
    End With

    'Init. the command object
    Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp = New ADODB.Command
    With AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
        Set .ActiveConnection = Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DmdPlanFcstStoreAdjustLog_Save_Sp"

    'Declare parameters
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@RepositoryKey", adVarWChar, adParamInput, 12)
'        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@FcstStoreKey", adInteger, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustType", adTinyInt, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustQty", adDecimal, adParamInput)
        .Parameters("@AdjustQty").Precision = 10
        .Parameters("@AdjustQty").NumericScale = 2
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@OverRideEnabled", adTinyInt, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustBegDate", adDBDate, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustEndDate", adDBDate, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustUserID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.CreateParameter("@AdjustDesc", adVarWChar, adParamInput, 255)
    End With
    
    With AIM_RepositoryOverRide_Get_Sp
        Set .ActiveConnection = Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RepositoryOverRide_Get_Sp"
    'Declare parameters
        .Parameters.Append AIM_RepositoryOverRide_Get_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_RepositoryOverRide_Get_Sp.CreateParameter("@FcstId", adVarWChar, adParamInput, 12)
    End With
    With m_rsUserElements
        .CursorLocation = adUseClient
        .LockType = adLockReadOnly
        .CursorType = adOpenStatic
    End With
    With m_rsFcstMaint
        .CursorLocation = adUseClient
        .LockType = adLockReadOnly
        .CursorType = adOpenForwardOnly
    End With
    'Init. the recordset
    
'    With m_rsItemAdj
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockReadOnly
'    End With
    With m_rsItemAdj
        '.CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    With m_rsOverRide
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.Initialize_StoredProcedures)"
End Function
Private Function Destroy_StoredProcedures() As Integer
On Error GoTo ErrorHandler

If Not (AIM_RepositoryDetail_Validate_Sp Is Nothing) Then
    Set AIM_RepositoryDetail_Validate_Sp.ActiveConnection = Nothing
End If

Set AIM_RepositoryDetail_Validate_Sp = Nothing

If Not (AIM_ForecastRepository_Save_Sp Is Nothing) Then
    Set AIM_ForecastRepository_Save_Sp.ActiveConnection = Nothing
End If
Set AIM_ForecastRepository_Save_Sp = Nothing

If Not (AIM_UserElements_Get_Sp Is Nothing) Then
    Set AIM_UserElements_Get_Sp.ActiveConnection = Nothing
End If
Set AIM_UserElements_Get_Sp = Nothing
 If Not (AIM_RepositoryItemAdj_Get_Sp Is Nothing) Then Set AIM_RepositoryItemAdj_Get_Sp.ActiveConnection = Nothing
Set AIM_RepositoryItemAdj_Get_Sp = Nothing

If Not (AIM_DmdPlanFcstStoreAdjustLog_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.ActiveConnection = Nothing
Set AIM_DmdPlanFcstStoreAdjustLog_Save_Sp = Nothing
If Not (AIM_RepositoryOverRide_Get_Sp Is Nothing) Then Set AIM_RepositoryOverRide_Get_Sp.ActiveConnection = Nothing
Set AIM_RepositoryOverRide_Get_Sp = Nothing

If f_IsRecordsetValidAndOpen(m_rsOverRide) Then m_rsOverRide.Close
    Set m_rsOverRide = Nothing
If f_IsRecordsetValidAndOpen(m_rsItemAdj) Then m_rsItemAdj.Close
    Set m_rsItemAdj = Nothing
If f_IsRecordsetValidAndOpen(m_rsUserElements) Then m_rsUserElements.Close
     Set m_rsUserElements = Nothing
If f_IsRecordsetValidAndOpen(m_rsUserElements) Then m_rsUserElements.Close
    Set m_rsUserElements = Nothing
If f_IsRecordsetValidAndOpen(m_rsCompound) Then m_rsCompound.Close
    Set m_rsCompound = Nothing
If f_IsRecordsetValidAndOpen(m_rsAccess) Then m_rsAccess.Close
    Set m_rsAccess = Nothing
 If f_IsRecordsetValidAndOpen(m_rsFcstMaint) Then m_rsFcstMaint.Close
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.Destroy_StoredProcedures)"
End Function

Private Function ApplyRecalcDataToDetail(ArgFromArray As XArrayDB, ArgToArray As XArrayDB) As Date
Dim I As Long
Dim J As Long
Dim K As Long
Dim L As Long
'This Function is used to populate the MainArray with the newly calculated data
'We will be replacing the some rows of ArgToArray with data From ArgFromArray
'ArgFromArray contains MainSubArray which will be a sub set of MainArray  which is ArgToArray

'Find out the matching rows in ArgToArray  and replace them with ArgFromArray data
For I = ArgFromArray.LowerBound(1) To ArgFromArray.UpperBound(1) Step UBound(m_FcstTypes()) + 1
    For J = ArgToArray.LowerBound(1) To ArgToArray.UpperBound(1) Step UBound(m_FcstTypes()) + 1
        If ArgFromArray(I, 0) = ArgToArray(J, 0) And ArgFromArray(I, 1) = ArgToArray(J, 1) Then
            For K = 0 To UBound(m_FcstTypes())
                For L = ArgToArray.LowerBound(2) To ArgToArray.UpperBound(2)
                    ArgToArray(J + K, L) = ArgFromArray(I + K, L)
                Next
            Next
        End If
     Next
Next
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ApplyRecalcDataToDetail)"
End Function
Private Function ConverToFcstUnits(ArrayToConvert As XArrayDB, Index As AIM_FORECASTUNITS) As Integer
Dim I As Long
Dim J As Long
Dim SummaryValue As Double
For I = ArrayToConvert.LowerBound(1) To ArrayToConvert.UpperBound(1)
    SummaryValue = 0
    For J = m_DetailTotalPos To m_DataEndPos
        If Index = AIM_FORECASTUNITS.FcstCost Then
        ArrayToConvert(I, J) = ArrayToConvert(I, J) * ArrayToConvert(I, m_CostPos)
        ElseIf Index = AIM_FORECASTUNITS.FcstPrice Then
        ArrayToConvert(I, J) = ArrayToConvert(I, J) * ArrayToConvert(I, m_PricePos)
        ElseIf Index = AIM_FORECASTUNITS.FcstWeight Then
        ArrayToConvert(I, J) = ArrayToConvert(I, J) * ArrayToConvert(I, m_WeightPos)
        ElseIf Index = AIM_FORECASTUNITS.FcstCube Then
        ArrayToConvert(I, J) = ArrayToConvert(I, J) * ArrayToConvert(I, m_CubePos)
        End If
        SummaryValue = SummaryValue + ArrayToConvert(I, J)
    Next
    ArrayToConvert(I, m_DetailTotalPos) = SummaryValue
Next
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.ConverToFcstUnits)"
End Function
Private Function FilterDetailArray(ArgFromArray As XArrayDB, ArgToArray As XArrayDB) As Boolean
On Error GoTo ErrorHandler
    Dim I As Long
    Dim J As Long
    Dim K As Long
    Dim L As Long
    Dim l_ItStat As String, l_LStatus As String, l_LDivision As String, l_LRegion As String, _
    l_LUserDefined As String, l_Class1 As String, l_Class2 As String, l_Class3 As String, _
    l_Class4 As String, l_ById As String, l_Lcid As String, l_VnId As String, _
    l_Assort As String, l_Item As String
    
    FilterDetailArray = False
    If ArgFromArray.LowerBound(1) = 1 And ArgFromArray.LowerBound(2) = 1 Then Exit Function
    ArgToArray.Clear
    With ArgFromArray
        K = .LowerBound(1)
        'Match the To array to the From's size
        ArgToArray.ReDim .LowerBound(1), .UpperBound(1), .LowerBound(2), .UpperBound(2)
        For I = .LowerBound(1) To .UpperBound(1) Step UBound(m_FcstTypes) + 1
            
            l_Lcid = .Value(I, 0)
            l_Item = .Value(I, 1)
            l_Class1 = .Value(I, m_DtlCol_Class1)
            l_Class2 = .Value(I, m_DtlCol_Class2)
            l_Class3 = .Value(I, m_DtlCol_Class3)
            l_Class4 = .Value(I, m_DtlCol_Class4)
            l_ItStat = .Value(I, m_DtlCol_ItStat)
            l_VnId = .Value(I, m_DtlCol_VnID)
            l_Assort = .Value(I, m_DtlCol_Assort)
            l_ById = .Value(I, m_DtlCol_ByID)
            l_LDivision = .Value(I, m_DtlCol_LDivision)
            l_LStatus = .Value(I, m_DtlCol_LStatus)
            l_LRegion = .Value(I, m_DtlCol_LRegion)
            l_LUserDefined = .Value(I, m_DtlCol_LUserDefined)
            
            'Check if From Array's data falls within the filter
            If g_IsInFilters(m_xaAllCriteria, l_ItStat, l_LStatus, l_LDivision, l_LRegion, _
                l_LUserDefined, l_Class1, l_Class2, l_Class3, _
                l_Class4, l_ById, l_Lcid, l_VnId, _
                l_Assort, l_Item) _
            Then
                For L = 0 To UBound(m_FcstTypes)
                    For J = .LowerBound(2) To .UpperBound(2)
                        ArgToArray(K, J) = .Value(I + L, J)
                    Next
                    K = K + 1
                Next
            End If
        Next
        ArgToArray.ReDim .LowerBound(1), K - 1, .LowerBound(2), .UpperBound(2)
    End With
    FilterDetailArray = True
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(FilterDetailArray)"
End Function
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Opens modal dialog for showing detailed adjustments log and adding more adjustments
' .......................................................................
'   Returns false if the process failed
'   else true after closing modal dialog
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function mShowAdjLog(p_Selectedtab As String) As Long
On Error GoTo ErrorHandler
    Dim IndexCounter As Integer
    Dim LcId As String
    Dim Item As String
    Dim BkMark As Variant
    Dim HasAccess As Boolean
    'Check access
    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, FCST_ACCESS.CODE_ModifyDemandPlan)
    If HasAccess = False Then
        'Message here
        Exit Function
    End If

    Select Case p_Selectedtab
    Case TAB_SUMMARY
        If m_IsGridSummaryLoaded Then
            BkMark = Me.dgSummary.Bookmark
            If Me.dcGroupBy.Text = GB_LCID Then LcId = Me.dgSummary.Columns(0).CellText(BkMark)
            If Me.dcGroupBy.Text = GB_ITEM Then Item = Me.dgSummary.Columns(0).CellText(BkMark)
        End If

    Case TAB_DETAIL
        If m_IsGridItemLoaded Then
            BkMark = Me.dgItem.Bookmark
            LcId = Me.dgItem.Columns(0).CellText(BkMark)
            Item = Me.dgItem.Columns(1).CellText(BkMark)
        End If
    End Select
     
    With AIM_ForecastAdjustments
        .p_FcstSetupKey = m_FcstSetupKey
        For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
            .p_FcstInterval = IIf(optFcstInterval(IndexCounter).Value = True, IndexCounter, .p_FcstInterval)
        Next
        .p_FcstStoreKey = m_FcstStoreKey_Main

        .p_FcstItem = Item
        .p_FcstLcID = LcId
        .p_AdjustStartDate = CDate(dcDate_Start(0).Text)
        .p_AdjustEndDate = CDate(dcDate_End(0).Text)

        .p_FcstID = m_FcstId
        .P_HistPds = CInt(txtFcstPeriods_Historical.Text)
        .P_FuturePds = CInt(txtFcstPeriods_Future.Text)
        .p_FcstStartDate = m_FcstStartDate
        .mPopulateFcstDates m_FcstPdDates()
'        .p_FcstDates = FcstDates
        .Show vbModal
    End With

CleanUp:

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(mShowAdjLog)"
End Function

Private Function mShowUserElements() As Integer
On Error GoTo ErrorHandler
Dim RtnCode As Integer '-1 error 0 ok
 RtnCode = -1
With AIM_ForecastUserElements
    .m_FcstId = m_FcstId
    .Show vbModal
End With
RtnCode = 0
CleanUp:
mShowUserElements = RtnCode
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(mShowAdjLog)"
End Function
Private Function mStartDate_Load() As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Boolean
    Dim RowCounter As Long
    Dim AddItemString As String
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    Dim I As Integer

    Screen.MousePointer = vbHourglass
    'Default to false until the process returns it's result.
    mStartDate_Load = False
   If UBound(m_FcstDates()) > 0 Then RtnCode = True
    If RtnCode = True Then
        For I = 0 To 2
            dcDate_Start(I).RemoveAll
            dcDate_Start(I).Reset
            For RowCounter = 0 To UBound(m_FcstDates()) - 1
                AddItemString = AddItemString & m_FcstDates(RowCounter) & vbTab
                dcDate_Start(I).AddItem AddItemString
                AddItemString = ""
            Next
        Next I
        mStartDate_Load = True
        dcDate_Start(0).Value = m_FcstDates(0)
    End If
CleanUp:
    Screen.MousePointer = vbNormal

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(mStartDate_Load)"
    Screen.MousePointer = vbNormal
End Function
Private Function mEndDate_Load() As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Boolean
    Dim RowCounter As Long
    Dim AddItemString As String
    Dim I As Integer
    
    Screen.MousePointer = vbHourglass
    'Default to false until the process returns it's result.
    mEndDate_Load = False

   If UBound(m_FcstDates()) > 0 Then RtnCode = True
    If RtnCode = True Then
        For I = 0 To 2
        dcDate_End(I).RemoveAll
        dcDate_End(I).Reset
        For RowCounter = 1 To UBound(m_FcstDates())
            AddItemString = AddItemString & CStr(m_FcstDates(RowCounter) - 1) & vbTab
            dcDate_End(I).AddItem AddItemString
            AddItemString = ""
        Next
        Next I
        dcDate_End(0).Value = m_FcstDates(UBound(m_FcstDates())) - 1
        mEndDate_Load = True
    End If

CleanUp:
    Screen.MousePointer = vbNormal

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(ForecastPlanner.mEndDate_Load)"
    Screen.MousePointer = vbNormal
End Function
Private Function Populate_FcstDates(Interval As AIM_INTERVALS, _
m_NbrPds_Hist As Integer, m_NbrPds_Future As Integer, FcstStartDate As Date, m_FcstDates() As Date)
On Error GoTo ErrorHandler
Dim I As Integer
Dim J As Integer
Dim DateCount As Integer
DateCount = UBound(m_FcstPdDates())
ReDim m_FcstDates(0 To DateCount)
 J = 0
For I = 0 To DateCount
     'If m_FcstPdDates(i) >= m_FreezePdDate Then
     If m_FcstPdDates(I) >= m_RealFreezePdDate Then
        m_FcstDates(J) = m_FcstPdDates(I)
        J = J + 1
    End If
Next
ReDim Preserve m_FcstDates(0 To J - 1)
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(Populate_FcstDates)"
End Function

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Updates the AIMFcstMaster table using all available adjustments for the given FcstID/FcstSetupKey
'   that have a current status of IsPromoted = False (0)
' .......................................................................
'   Returns false if the process failed
'   else true after saving to the database.
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Sub mToggleToolbarAccess()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim EBFcstMod As SSActiveToolBars
    
    Set EBFcstMod = Me.tbFcstModification
    EBFcstMod.Redraw = False
    'Start with a default of no access
    EBFcstMod.Tools(TB_ID_LOAD).Enabled = False
    EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = False
    EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = False
    EBFcstMod.Tools(TB_ID_COPY).Enabled = False
    EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = False
    EBFcstMod.Tools(TB_ID_APPLY).Enabled = False
    EBFcstMod.Tools(TB_ID_CANCEL).Enabled = False
    EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = False
    EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = False
    EBFcstMod.Tools(TB_ID_PROMOTE).Enabled = False

    'Get Access Type
    RtnCode = gFetch_ForecastAccess(Cn, m_FcstSetupKey, m_rsAccess, gUserID)

    'Enable based on permissions
    If RtnCode = 1 Then
        m_rsAccess.MoveFirst
        Do Until m_rsAccess.eof
            Select Case CLng(m_rsAccess!AccessCode)
            Case FCST_ACCESS.CODE_FullControl
                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
                EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = True
                EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = True
                EBFcstMod.Tools(TB_ID_COPY).Enabled = True
                EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = False
                EBFcstMod.Tools(TB_ID_APPLY).Enabled = True
                EBFcstMod.Tools(TB_ID_CANCEL).Enabled = False
                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
                EBFcstMod.Tools(TB_ID_PROMOTE).Enabled = True

                'All done, here. Exit
                Exit Do
            Case FCST_ACCESS.CODE_ModifyForecastSetup     '1
                'Not relevant to the demand planner, ignore
            Case FCST_ACCESS.CODE_ModifyDemandPlan     '2
                'All except Promote
                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
                EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = True
                EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = True
                EBFcstMod.Tools(TB_ID_COPY).Enabled = True
                EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = False
                EBFcstMod.Tools(TB_ID_APPLY).Enabled = True
                EBFcstMod.Tools(TB_ID_CANCEL).Enabled = False
                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
            Case FCST_ACCESS.CODE_Read     '3
                'Limited access
                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
                EBFcstMod.Tools(TB_ID_RUNFORECAST).Enabled = False      'NOT ALLOWED
                EBFcstMod.Tools(TB_ID_RECALCCURRENT).Enabled = False      'NOT ALLOWED
                EBFcstMod.Tools(TB_ID_COPY).Enabled = False      'NOT ALLOWED
                EBFcstMod.Tools(TB_ID_SAVEFORECAST).Enabled = False      'NOT ALLOWED
                EBFcstMod.Tools(TB_ID_APPLY).Enabled = False
                EBFcstMod.Tools(TB_ID_CANCEL).Enabled = False
                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
            Case FCST_ACCESS.CODE_ControlAccess     '4
                'Not relevant to the demand planner, ignore
            Case FCST_ACCESS.CODE_Promote     '5
                'Read and promote
                EBFcstMod.Tools(TB_ID_LOAD).Enabled = True
                EBFcstMod.Tools(TB_ID_ITEMFILTER).Enabled = True
                EBFcstMod.Tools(TB_ID_CLEARFILTER).Enabled = True
                EBFcstMod.Tools(TB_ID_PROMOTE).Enabled = True
            End Select
            m_rsAccess.MoveNext
        Loop
    End If
    If Me.ckDataCalc_Type(2).Enabled = False Then
       Me.tbFcstModification.Tools("ID_ADJLOG").Enabled = False
       Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = False
       Me.tbFcstModification.Tools("ID_CANCEL").Enabled = False
       Me.tbFcstModification.Tools("ID_APPLY").Enabled = False
    Else
'        Me.tbFcstModification.Tools("ID_ADJLOG").Enabled = True
'        Me.tbFcstModification.Tools(TB_ID_SAVEFORECAST).Enabled = True
'        Me.tbFcstModification.Tools("ID_CANCEL").Enabled = True
'        Me.tbFcstModification.Tools("ID_APPLY").Enabled = True
    End If
CleanUp:
    EBFcstMod.Redraw = True
    Set EBFcstMod = Nothing
    If f_IsRecordsetValidAndOpen(m_rsAccess) Then m_rsAccess.Close
    'Set m_rsAccess = Nothing
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(mToggleToolbarAccess)"
     f_HandleErr , , , "AIM_ForecastPlanner::mToggleToolbarAccess", Now, gDRGeneralError, True, Err
    GoTo CleanUp:
End Sub



Private Function DisplayFilters( _
    p_CurrentFilter As String _
) As String
On Error GoTo ErrorHandler

    'Dim arrItems As XArrayDB
    Dim LockFields As Boolean
    
    'Dim FcstItemFilter As FCST_ITEM_FILTER
    Dim RowCounter As Long, ColCounter As Long
    Dim AllFilters As String, NewFilters As String
    Dim ShowFilters As Form
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    
    LockFields = True   'Users of this screen should NOT edit existing filters.
    Set ShowFilters = AIM_ForecastFilter
    With ShowFilters
        Set .pr_ADOConn = Cn
        'Send over the ones that are to be locked
        Set .rt_xaOrigFilters = m_xaOrigCriteria
        'Send over the ones that are additional to the saved set
        Set .rt_xaExtraFilters = m_xaNewCriteria
        'Send over the entire set (permanent + temporary)
        Set .rt_xaAllFilters = IIf(g_IsArray(m_xaAllCriteria), m_xaAllCriteria, m_xaOrigCriteria)
        'Set other parameters
        .pr_ExistingFilters = p_CurrentFilter
        .pr_LockExisting = LockFields
        .prm_ItemFilter = False
        'Display the the Item Filter screen
        .Show vbModal
        'Retrieve results from the Item Filter screen
        If .rt_UserCancel = False Then
            Me.txtSelected.Text = .rt_AllFilters
            Me.txtNewFilters.Text = .rt_NewFilters
            'Retrieve updated set of all criteria
            g_CXArr1ToXArr2 .rt_xaAllFilters, m_xaAllCriteria
            'Retrieve set of criteria that are additional to the saved set
            g_CXArr1ToXArr2 .rt_xaExtraFilters, m_xaNewCriteria
        End If
    End With
    'Destroy the object
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    
'    g_PrintCriteria m_xaOrigCriteria
'    g_PrintCriteria m_xaAllCriteria
'    g_PrintCriteria m_xaNewCriteria
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    'Err.Raise ErrNumber, ErrSource & "{DisplayFilters}", ErrDesc
     f_HandleErr , , , "AIM_ForecastPlanner::DisplayFilters", Now, gDRGeneralError, True, Err
End Function






