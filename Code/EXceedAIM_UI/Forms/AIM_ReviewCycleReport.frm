VERSION 5.00
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ReviewCycleReport 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Review Cycle Report"
   ClientHeight    =   2130
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   6420
   Icon            =   "AIM_ReviewCycleReport.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2130
   ScaleWidth      =   6420
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   120
      Top             =   1620
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_ReviewCycleReport.frx":030A
      ToolBars        =   "AIM_ReviewCycleReport.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   1410
      Left            =   36
      TabIndex        =   6
      Top             =   36
      Width           =   6350
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Seasonal"
         Height          =   300
         Index           =   5
         Left            =   3285
         TabIndex        =   5
         Top             =   930
         Width           =   2940
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Week of Month"
         Height          =   300
         Index           =   4
         Left            =   3285
         TabIndex        =   4
         Top             =   570
         Width           =   2940
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Monthly"
         Height          =   300
         Index           =   3
         Left            =   3285
         TabIndex        =   3
         Top             =   210
         Width           =   2940
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Weekly"
         Height          =   300
         Index           =   2
         Left            =   120
         TabIndex        =   2
         Top             =   930
         Width           =   2940
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Daily"
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   1
         Top             =   570
         Width           =   2940
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "All Review Cycles"
         Height          =   300
         Index           =   6
         Left            =   120
         TabIndex        =   0
         Top             =   210
         Value           =   -1  'True
         Width           =   2940
      End
   End
End
Attribute VB_Name = "AIM_ReviewCycleReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Private Function BldSQL(CycleOption As Integer)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    SqlStmt = "SELECT RevCycle, RevCycleDesc, NextDateTime, "
    SqlStmt = SqlStmt & "RevStatus = CASE RevStatus "
    SqlStmt = SqlStmt & "   WHEN 0 THEN ' ' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevFreq = CASE "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 1 AND DynamicFlag = 0 THEN 'Daily' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 2 AND DynamicFlag = 0 THEN 'Weekly' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 3 AND DynamicFlag = 0 THEN 'Monthly' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 4 AND DynamicFlag = 0 THEN 'Week-Of-Month' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 1 AND DynamicFlag <> 0 THEN 'Daily *' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 2 AND DynamicFlag <> 0 THEN 'Weekly *' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 3 AND DynamicFlag <> 0 THEN 'Monthly *' "
    SqlStmt = SqlStmt & "   WHEN RevFreq = 4 AND DynamicFlag <> 0 THEN 'Week-Of-Month *' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevInterval, "
    SqlStmt = SqlStmt & "RevSunday = CASE RevSunday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevMonday = CASE RevMonday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevTuesday = CASE RevTuesday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevWednesday = CASE RevWednesday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevThursday = CASE RevThursday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevFriday = CASE RevFriday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevSaturday = CASE RevSaturday "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'X' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "WeekQualifier = CASE WeekQualifier "
    SqlStmt = SqlStmt & "   WHEN 0 THEN '' "
    SqlStmt = SqlStmt & "   WHEN 1 THEN 'First Week' "
    SqlStmt = SqlStmt & "   WHEN 2 THEN 'Second Week' "
    SqlStmt = SqlStmt & "   WHEN 3 THEN 'Third Week' "
    SqlStmt = SqlStmt & "   WHEN 4 THEN 'Fourth Week' "
    SqlStmt = SqlStmt & "   WHEN 5 THEN 'Last Week' "
    SqlStmt = SqlStmt & "END, "
    SqlStmt = SqlStmt & "RevStartDate, RevENDDate, InitBuyPct, "
    SqlStmt = SqlStmt & "SeasonalReview, InitRevDate, ReviewTime "
    SqlStmt = SqlStmt & "FROM RevCycles "
    
    If CycleOption < 6 Then
        If CycleOption = 5 Then
            SqlStmt = SqlStmt & vbCrLf & "WHERE SeasonalReview <> 0"
        Else
            SqlStmt = SqlStmt & vbCrLf & "WHERE RevFreq = " & Format(CycleOption, "0") & " "
        End If
    End If
    
    SqlStmt = SqlStmt & "ORDER BY RevCycle "
    
    BldSQL = SqlStmt


Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldSQL)"
    f_HandleErr , , , "AIM_ReviewCycleReport::BldSQL", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim CycleOption As Integer
    Dim Report As AIM_ReviewCycle
    Dim SqlStmt As String
    Dim strMessage As String
    Dim rsReport As ADODB.Recordset

    Select Case Tool.ID
    Case "ID_Print", "ID_Preview"
        'Housekeeping
        Screen.MousePointer = vbHourglass
        strMessage = getTranslationResource("STATMSG04700")
        If StrComp(strMessage, "STATMSG04700") = 0 Then strMessage = "Building the Review Cycle Report..."
        Write_Message strMessage
        
        'Determine the selection criteria
        For CycleOption = Me.optRevFreq.LBound To Me.optRevFreq.UBound
            If Me.optRevFreq(CycleOption).Value = True Then
                Exit For
            End If
        Next CycleOption
        
        'Build SQL Statement
        SqlStmt = BldSQL(CycleOption)
        
        'Open the report result set
        Set rsReport = New ADODB.Recordset
        rsReport.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
        
        Write_Message ""
        Screen.MousePointer = vbNormal
        
        If Not f_IsRecordsetOpenAndPopulated(rsReport) Then
            If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
            Set rsReport = Nothing
            strMessage = getTranslationResource("TEXTMSG05103")
            If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
            Write_Message strMessage
            Exit Sub
        End If
    
        'Instantiate the Report
        Set Report = New AIM_ReviewCycle
        Set Report.dcRevCycles.Recordset = rsReport
        
        Select Case Tool.ID
        Case "ID_Print"
            Report.PrintReport True
    
        Case "ID_Preview"
            Set AIM_Reports.ARViewer.ReportSource = Report
            AIM_Reports.Show vbModal, AIM_Main
    
        End Select
        
    Case "ID_Close"
        Unload Me
        Exit Sub
    End Select

    'Wrap Up
    Write_Message ""
    Screen.MousePointer = vbNormal
    Set Report = Nothing
    Set AIM_ReviewCycle = Nothing
    
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing

Exit Sub
ErrorHandler:
    Write_Message ""
    Screen.MousePointer = vbNormal
    Set Report = Nothing
    Set AIM_ReviewCycle = Nothing
    
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing

    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AIM_ReviewCycleReport::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_ReviewCycleReport::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG04701")
    If StrComp(strMessage, "STATMSG04701") = 0 Then strMessage = "Initializing Review Cycle Report..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_ReviewCycleReport::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_ReviewCycleReport::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

