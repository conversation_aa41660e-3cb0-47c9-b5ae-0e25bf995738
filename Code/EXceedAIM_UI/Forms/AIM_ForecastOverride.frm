VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ForecastOverride 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Forecast/Safety Stock Override Utility"
   ClientHeight    =   7995
   ClientLeft      =   45
   ClientTop       =   345
   ClientWidth     =   15270
   Icon            =   "AIM_ForecastOverride.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   7995
   ScaleWidth      =   15270
   ShowInTaskbar   =   0   'False
   Begin VB.CommandButton cmdReset 
      Caption         =   "Clear &All Overrides"
      Height          =   345
      Left            =   12495
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   7200
      Width           =   2685
   End
   Begin VB.CommandButton cmdItemFilter 
      Caption         =   "Filter Ite&ms"
      Height          =   345
      Left            =   10440
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   7200
      Width           =   1965
   End
   Begin ActiveToolBars.SSActiveToolBars atbForecast 
      Left            =   13440
      Top             =   7560
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   3
      Tools           =   "AIM_ForecastOverride.frx":030A
      ToolBars        =   "AIM_ForecastOverride.frx":4259
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgOverride 
      Height          =   5550
      Left            =   75
      TabIndex        =   8
      Top             =   1530
      Width           =   15105
      _Version        =   196617
      DataMode        =   1
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      HeadLines       =   2
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   26644
      _ExtentY        =   9790
      _StockProps     =   79
   End
   Begin VB.Frame Frame1 
      Height          =   1440
      Left            =   72
      TabIndex        =   9
      Top             =   0
      Width           =   15105
      Begin VB.CheckBox ckOverrideCounterStock 
         Caption         =   "Overwrite &Counter Stock"
         Height          =   340
         Left            =   6720
         TabIndex        =   5
         Top             =   1022
         Width           =   3300
      End
      Begin VB.CheckBox ckOverrideFcst 
         Caption         =   "&Overwrite Forecast Overrides"
         Height          =   340
         Left            =   345
         TabIndex        =   2
         Top             =   1022
         Width           =   3300
      End
      Begin TDBNumber6Ctl.TDBNumber txtCounterStockAdjUnits 
         Height          =   345
         Left            =   10065
         TabIndex        =   4
         Top             =   615
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calculator      =   "AIM_ForecastOverride.frx":4364
         Caption         =   "AIM_ForecastOverride.frx":4384
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastOverride.frx":43F0
         Keys            =   "AIM_ForecastOverride.frx":440E
         Spin            =   "AIM_ForecastOverride.frx":4458
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#####0;-#####0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#####0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011627525
         Value           =   0
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin TDBNumber6Ctl.TDBNumber txtFcstAdj 
         Height          =   340
         Left            =   3705
         TabIndex        =   1
         Top             =   616
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   600
         Calculator      =   "AIM_ForecastOverride.frx":4480
         Caption         =   "AIM_ForecastOverride.frx":44A0
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastOverride.frx":450C
         Keys            =   "AIM_ForecastOverride.frx":452A
         Spin            =   "AIM_ForecastOverride.frx":4574
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.00;(##0.00)"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999.99
         MinValue        =   -999.99
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011627525
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBDate6Ctl.TDBDate txtUserFcstExpDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   340
         Left            =   3705
         TabIndex        =   0
         Top             =   210
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   600
         Calendar        =   "AIM_ForecastOverride.frx":459C
         Caption         =   "AIM_ForecastOverride.frx":46B4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastOverride.frx":4720
         Keys            =   "AIM_ForecastOverride.frx":473E
         Spin            =   "AIM_ForecastOverride.frx":479C
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/15/2001"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37179
         CenturyMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtCounterStockAdj 
         Height          =   345
         Left            =   10065
         TabIndex        =   3
         Top             =   210
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calculator      =   "AIM_ForecastOverride.frx":47C4
         Caption         =   "AIM_ForecastOverride.frx":47E4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastOverride.frx":4850
         Keys            =   "AIM_ForecastOverride.frx":486E
         Spin            =   "AIM_ForecastOverride.frx":48B8
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011627525
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label4 
         Caption         =   "Counter Stock Adjustment Units"
         Height          =   345
         Left            =   6720
         TabIndex        =   13
         Top             =   660
         Width           =   3300
      End
      Begin VB.Label Label3 
         Caption         =   "Counter Stock Adjustment %"
         Height          =   345
         Left            =   6720
         TabIndex        =   12
         Top             =   255
         Width           =   3300
      End
      Begin VB.Label Label1 
         Caption         =   "Forecast Override Expiration"
         Height          =   340
         Left            =   345
         TabIndex        =   11
         Top             =   255
         Width           =   3300
      End
      Begin VB.Label Label2 
         Caption         =   "Forecast Adjustment %"
         Height          =   340
         Left            =   345
         TabIndex        =   10
         Top             =   660
         Width           =   3300
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ForecastOverride"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_UpdateOverrides_Sp As ADODB.Command
Dim rsOverride As ADODB.Recordset

Dim aOverride() As Variant
Dim m_DataExists As Boolean
'Dim m_FcstItemFilter As FCST_ITEM_FILTER
Dim m_xaOrigCriteria As XArrayDB
Dim m_xaAllCriteria As XArrayDB
Dim m_xaNewCriteria As XArrayDB

Public Function BldGetOverrideSQL( _
    UserFcstExpDate As Date, FcstAdj As Double, _
    CounterStockAdj As Double, CounterStockAdjUnits As Long, _
    OverrideFcst As Integer, OverrideCounterStock As Integer) As String
On Error GoTo ErrorHandler
    Dim strSQL As String
    Dim strWhere As String
    Dim strFilters As String
    If m_xaAllCriteria Is Nothing Then
        strSQL = ""
        Exit Function
    End If
    
    strFilters = g_BuildWhereClause(m_xaAllCriteria)
    'If no parameters, then exit with no query, else build the query using the parameters
    If Trim$(strFilters) = COMP_EMPTY _
    Then
        BldGetOverrideSQL = ""
        Exit Function
    Else
        'Initialize SQL Statement
        strSQL = "SELECT Sel = cast(1 as int), "
        strSQL = strSQL & "Item.LcID, Item.Item, Item.ItDesc, Item.ItStat, Item.FcstDemand, "
        strSQL = strSQL & "Item.FcstMethod, "
        strSQL = strSQL & "AdjFcst = Item.FcstDemand * " & Format(1 + (FcstAdj / 100), "0.00") & ", "
        strSQL = strSQL & "UserFcstExpDate = '" & Format(Me.txtUserFcstExpDate.Value, "short date") & "', "
        strSQL = strSQL & "Item.SafetyStock, "
        strSQL = strSQL & "Item.Accum_lt, "
        strSQL = strSQL & "Days = Round(CASE "
        strSQL = strSQL & "     WHEN Item.Accum_lt > 0 AND Item.Fcstlt > 0 "
        strSQL = strSQL & "     THEN Item.SafetyStock /(Item.Fcstlt/Item.Accum_lt) "
        strSQL = strSQL & "     ELSE 0 END, 1), "
        'Counter Stock Adjustment.
        If CounterStockAdjUnits > 0 Then
            strSQL = strSQL & _
                "CStockAdj = Round(Item.SafetyStock + " & _
                            Format(CounterStockAdjUnits, "#,##0") & _
                            ",0), "
        Else
            strSQL = strSQL & _
                "CStockAdj = Round(Item.SafetyStock * " & _
                            Format(1 + (CounterStockAdj / 100), "0.00") & _
                            ",0), "
        End If
        'Adjustment Days
        strSQL = strSQL & "AdjDays = Round(CASE "
        strSQL = strSQL & "     WHEN Item.Accum_lt > 0 AND Item.Fcstlt > 0 "
        If CounterStockAdjUnits > 0 Then
            strSQL = strSQL & _
                "     THEN Round(Item.SafetyStock + " & Format(CounterStockAdjUnits, "#,##0") & ",0) " & _
                                    "/(Item.Fcstlt/Item.Accum_lt) "
        Else
            strSQL = strSQL & _
                "     THEN (Item.SafetyStock * " & Format(1 + (CounterStockAdj / 100), "0.00") & ")" & _
                                    "/(Item.Fcstlt/Item.Accum_lt) "
        End If
        strSQL = strSQL & "ELSE 0 END, 1), "
        'Burn
        strSQL = strSQL & "Burn = CASE "
        strSQL = strSQL & "     WHEN Item.Accum_lt > 0 THEN Item.Fcstlt/Item.Accum_lt "
        strSQL = strSQL & "     ELSE 0 END, "
        strSQL = strSQL & "Item.Fcstlt, "
        strSQL = strSQL & "Item.FcstUpdCyc, Item.UserFcst, Item.UserFcstExpDate, Item.CStock "
        strSQL = strSQL & vbCrLf & "FROM Item "
        strSQL = strSQL & vbCrLf & "INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat "
        strSQL = strSQL & vbCrLf & "INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID "
        'Build the where clause
        strWhere = "WHERE ItStatus.DmdUpd = N'Y'"
        If OverrideFcst = vbUnchecked Then
            strWhere = strWhere & vbCrLf & _
                "AND (Item.UserFcst = 0 or Item.UserFcstExpDate <= GetDate()) "
        End If
        If OverrideCounterStock = vbUnchecked Then
            strWhere = strWhere & vbCrLf & _
                "AND Item.CStock = 0 "
        End If
        
        'Add Selection Criteria to the WHERE clause
        strWhere = strWhere & strFilters
        
        'Concatenate all
        strSQL = strSQL & vbCrLf & strWhere
    End If
    
    'Add Order By Clause
    BldGetOverrideSQL = strSQL & vbCrLf & "ORDER BY Item.Item, Item.LcID "
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldGetOverrideSQL)"
     f_HandleErr , , , "AIM_ForecastOverride::BldGetOverrideSQL", Now, gDRGeneralError, True, Err
    
End Function

Private Sub atbForecast_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim strMessage1 As String
    Dim TestDim     As Long
    Screen.MousePointer = vbHourglass
        
    Select Case Tool.ID
        Case "ID_Update"
            FetchData
            
        Case "ID_Save"
'            If IsNull(aOverride) Then
'                Write_Message ""
'                Screen.MousePointer = vbNormal
'                Exit Sub
'            End If
            
            On Error Resume Next
            TestDim = UBound(aOverride, 2)
            If Err.Number = 9 Or TestDim <= 0 Then
                strMessage = getTranslationResource("STATMSG07407")
                If StrComp(strMessage, "STATMSG07407") = 0 Then strMessage = "No Items available to Save"
                Write_Message strMessage
                MsgBox strMessage & vbCrLf & vbCrLf & _
                strMessage1, vbCritical + vbOKOnly, _
                getTranslationResource(Me.Caption)
                Screen.MousePointer = vbNormal
                Exit Sub
            End If
            On Error GoTo 0
             
            strMessage = getTranslationResource("STATMSG07401")
            If StrComp(strMessage, "STATMSG07401") = 0 Then strMessage = "Updating Item Table for Forecast Overrides and Safety Stock Adjustments..."
            Write_Message strMessage
            
            'Process rows
            For IndexCounter = LBound(aOverride, 2) To UBound(aOverride, 2)
                'Check to see if the item has been selected
                If aOverride(0, IndexCounter) = 1 Then
                    ' Specify input parameter values
                    With AIM_UpdateOverrides_Sp
                        .Parameters("@LcId").Value = aOverride(1, IndexCounter)
                        .Parameters("@Item").Value = aOverride(2, IndexCounter)
                        
                        If aOverride(5, IndexCounter) <> aOverride(7, IndexCounter) Then
                            .Parameters("@UserFcst").Value = aOverride(7, IndexCounter)
                            .Parameters("@UserFcstExpDate").Value = aOverride(8, IndexCounter)
                        Else
                            .Parameters("@UserFcst").Value = 0
                            .Parameters("@UserFcstExpDate").Value = "01/01/1990"
                        End If
                        
                        If aOverride(9, IndexCounter) <> aOverride(12, IndexCounter) Then
                            .Parameters("@CStock").Value = aOverride(12, IndexCounter)
                        Else
                            .Parameters("@CStock").Value = 0
                        End If
                        
                        If aOverride(5, IndexCounter) = aOverride(7, IndexCounter) _
                        And aOverride(9, IndexCounter) = aOverride(12, IndexCounter) Then
                            .Parameters("@ResetOption").Value = "Y"
                        Else
                            .Parameters("@ResetOption").Value = "N"
                        End If
        
                        ' Execute the command
                        .Execute , , adExecuteNoRecords
                        
                        'Check for errors
                        If .Parameters(0).Value = 0 Then
                            strMessage = getTranslationResource("STATMSG07402")
                            If StrComp(strMessage, "STATMSG07402") = 0 Then strMessage = "Error updating Forecast/Safety Stock Overrides."
                            Write_Message strMessage
                            Exit For
                        Else
                            strMessage = getTranslationResource("STATMSG07403")
                            If StrComp(strMessage, "STATMSG07403") = 0 Then strMessage = "Forecast/Safety Stock Overrides updated."
                            Write_Message strMessage
                        End If
                    End With
                End If
            Next IndexCounter
        
        Case "ID_Close"
            Screen.MousePointer = vbNormal
            Unload Me
            Exit Sub
    End Select
    
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    If Err.Number = 9 Then
        'Subscript out of Range
        strMessage1 = Err.Description
        strMessage = getTranslationResource("STATMSG07402")
        If StrComp(strMessage, "STATMSG07402") = 0 Then strMessage = "Error updating Forecast/Safety Stock Overrides."
        MsgBox strMessage & vbCrLf & vbCrLf & _
                strMessage1, vbCritical + vbOKOnly, _
                getTranslationResource(Me.Caption)
    Else
     'f_HandleErr Me.Caption & "(atbForecast_ToolClick)"
     f_HandleErr , , , Me.Caption & "::atbForecast_ToolClick", Now, gDRGeneralError, True, Err
        
    End If
End Sub

Private Sub cmdItemFilter_Click()
On Error GoTo ErrorHandler
    
    Dim CurrentFilter As String
    
    If g_IsArray(m_xaAllCriteria) Then
        CurrentFilter = g_CXArrToPara(m_xaAllCriteria, False, False)
    Else
        CurrentFilter = COMP_EMPTY
    End If
    DisplayFilters CurrentFilter
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdItemFilter_Click)"
     f_HandleErr , , , Me.Caption & "::cmdItemFilter_Click", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub cmdReset_Click()
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim strMessage1 As String
        
    'Check for an empty array
    On Error Resume Next
    If LBound(aOverride) < 0 Then
        Exit Sub
    End If
    On Error GoTo ErrorHandler
    
    'Check for user permission
    strMessage = getTranslationResource("MSGBOX07403")
    If StrComp(strMessage, "MSGBOX07403") = 0 Then strMessage = "Do you wish to reset the existing User Forecast Overrides and Counter Stocks?"
    RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, getTranslationResource(Me.Caption))
    If RtnCode <> vbYes Then
        Exit Sub
    End If
    
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07404")
    If StrComp(strMessage, "STATMSG07404") = 0 Then strMessage = "Clearing all overrides..."
    Write_Message strMessage
    
    'Reset the override elements for all selected items
    For IndexCounter = LBound(aOverride, 2) To UBound(aOverride, 2)
        If aOverride(0, IndexCounter) = 1 Then
            ' Specify input parameter values
            With AIM_UpdateOverrides_Sp
                .Parameters("@LcId").Value = aOverride(1, IndexCounter)
                .Parameters("@Item").Value = aOverride(2, IndexCounter)
                .Parameters("@UserFcst").Value = 0
                .Parameters("@UserFcstExpDate").Value = "01/01/1990"
                .Parameters("@CStock").Value = 0
                .Parameters("@ResetOption").Value = "Y"

                ' Execute the command
                .Execute , , adExecuteNoRecords
                
                'Check for errors
                If .Parameters(0).Value = 0 Then
                    strMessage = getTranslationResource("STATMSG07405")
                    If StrComp(strMessage, "STATMSG07405") = 0 Then strMessage = "Error resetting Forecast/Safety Stock Overrides."
                    Write_Message strMessage
                    Exit For
                End If
            End With
        End If
    Next IndexCounter
    
    'Get the data again
    FetchData
    
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsOverride) Then rsOverride.Close
    'f_HandleErr Me.Caption & "(cmdReset_Click)"
     f_HandleErr , , , Me.Caption & "::cmdReset_Click", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgOverride_AfterColUpdate(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim Burn As Double
    Dim p As Variant
    
    Select Case ColIndex
        Case 12         'Counter Stock Adjustment
            'Get a pointer to the Override array
            p = Me.dgOverride.Bookmark
            
            'Get the burn
            Burn = aOverride(14, p)
            
            'Update the adjusted days
            If Burn > 0 Then
                Me.dgOverride.Columns(13).Value = Me.dgOverride.Columns(ColIndex).Value / Burn
            Else
                Me.dgOverride.Columns(13).Value = 0
            End If
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgOverride_AfterColUpdate)"
     f_HandleErr , , , Me.Caption & "::dgOverride_AfterColUpdate", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgOverride_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Define Columns
    Me.dgOverride.Columns(0).Name = "selopt"
    Me.dgOverride.Columns(0).Caption = getTranslationResource("Sel")
    Me.dgOverride.Columns(0).Width = 450
    Me.dgOverride.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgOverride.Columns(0).Style = ssStyleCheckBox
    Me.dgOverride.Columns(0).Locked = False

    Me.dgOverride.Columns(1).Name = "lcid"
    Me.dgOverride.Columns(1).Caption = getTranslationResource("Location")
    Me.dgOverride.Columns(1).Width = 1000
    Me.dgOverride.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgOverride.Columns(1).Locked = True
    
    Me.dgOverride.Columns(2).Name = "item"
    Me.dgOverride.Columns(2).Caption = getTranslationResource("Item")
    Me.dgOverride.Columns(2).Width = 1400
    Me.dgOverride.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgOverride.Columns(2).Locked = True
    
    Me.dgOverride.Columns(3).Name = "itdesc"
    Me.dgOverride.Columns(3).Caption = getTranslationResource("Description")
    Me.dgOverride.Columns(3).Width = 2400
    Me.dgOverride.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgOverride.Columns(3).Locked = True

    Me.dgOverride.Columns(4).Name = "itstatus"
    Me.dgOverride.Columns(4).Caption = getTranslationResource("S")
    Me.dgOverride.Columns(4).Width = 300
    Me.dgOverride.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(4).Alignment = ssCaptionAlignmentCenter
    Me.dgOverride.Columns(4).Locked = True

    Me.dgOverride.Columns(5).Name = "fcstdemand"
    Me.dgOverride.Columns(5).Caption = getTranslationResource("Demand")
    Me.dgOverride.Columns(5).Width = 950
    Me.dgOverride.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(5).Locked = True
    Me.dgOverride.Columns(5).NumberFormat = "#,##0.00"

    Me.dgOverride.Columns(6).Name = "fcstmethod"
    Me.dgOverride.Columns(6).Caption = getTranslationResource("Method")
    Me.dgOverride.Columns(6).Width = 950
    Me.dgOverride.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(6).Locked = True
    Me.dgOverride.Columns(6).NumberFormat = "00"

    Me.dgOverride.Columns(7).Name = "adjfcst"
    Me.dgOverride.Columns(7).Caption = getTranslationResource("Adjust Demand")
    Me.dgOverride.Columns(7).Width = 950
    Me.dgOverride.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(7).Locked = False
    Me.dgOverride.Columns(7).NumberFormat = "#,##0.00"
    Me.dgOverride.Columns(7).DataType = vbDecimal

    Me.dgOverride.Columns(8).Name = "userfcstexpdate"
    Me.dgOverride.Columns(8).Caption = getTranslationResource("Expiration Date")
    Me.dgOverride.Columns(8).Width = 1200
    Me.dgOverride.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(8).Alignment = ssCaptionAlignmentLeft
    Me.dgOverride.Columns(8).Locked = False
    Me.dgOverride.Columns(8).DataType = vbDate
    Me.dgOverride.Columns(8).NumberFormat = gDateFormat

    Me.dgOverride.Columns(9).Name = "safetystock"
    Me.dgOverride.Columns(9).Caption = getTranslationResource("Safety Stock")
    Me.dgOverride.Columns(9).Width = 1000
    Me.dgOverride.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(9).Locked = True
    Me.dgOverride.Columns(9).DataType = vbLong
    Me.dgOverride.Columns(9).NumberFormat = "#,##0"

    Me.dgOverride.Columns(10).Name = "accum_lt"
    Me.dgOverride.Columns(10).Caption = getTranslationResource("Lead Time")
    Me.dgOverride.Columns(10).Width = 900
    Me.dgOverride.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(10).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(10).Locked = True
    Me.dgOverride.Columns(10).DataType = vbLong
    Me.dgOverride.Columns(10).NumberFormat = "#,##0"
    
    Me.dgOverride.Columns(11).Name = "safetydays"
    Me.dgOverride.Columns(11).Caption = getTranslationResource("Safety Days")
    Me.dgOverride.Columns(11).Width = 900
    Me.dgOverride.Columns(11).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(11).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(11).Locked = True
    Me.dgOverride.Columns(11).DataType = vbLong
    Me.dgOverride.Columns(11).NumberFormat = "#,##0"

    Me.dgOverride.Columns(12).Name = "adjSafetyStock"
    Me.dgOverride.Columns(12).Caption = getTranslationResource("Adj Safety Stock")
    Me.dgOverride.Columns(12).Width = 1200
    Me.dgOverride.Columns(12).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(12).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(12).Locked = False
    Me.dgOverride.Columns(12).DataType = vbLong
    Me.dgOverride.Columns(12).NumberFormat = "#,##0"

    Me.dgOverride.Columns(13).Name = "adjsafetydays"
    Me.dgOverride.Columns(13).Caption = getTranslationResource("Adj Safety Days")
    Me.dgOverride.Columns(13).Width = 1200
    Me.dgOverride.Columns(13).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgOverride.Columns(13).Alignment = ssCaptionAlignmentRight
    Me.dgOverride.Columns(13).Locked = True
    Me.dgOverride.Columns(13).DataType = vbLong
    Me.dgOverride.Columns(13).NumberFormat = "#,##0"
    
    
    On Error Resume Next 'To prevent error when they run the first time
    Me.dgOverride.LoadLayout App.Path & "\" & "AIM_ForecastOverride" & Trim$(gUserID) & "_Layout.grd"
    'Check for non-existent file
    If Err.Number = 53 _
    Or Err.Number = 9 Then
    'File Not Found
        Resume Next
    ElseIf Err.Number <> 0 Then
        GoTo ErrorHandler
    End If
    On Error GoTo ErrorHandler
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgOverride.Font
    SetProperFont Me.dgOverride.HeadFont
    SetProperFont Me.dgOverride.PageFooterFont
    SetProperFont Me.dgOverride.PageHeaderFont
    Me.dgOverride.Columns(0).Caption = getTranslationResource("Sel")
    Me.dgOverride.Columns(1).Caption = getTranslationResource("Location")
    Me.dgOverride.Columns(2).Caption = getTranslationResource("Item")
    Me.dgOverride.Columns(3).Caption = getTranslationResource("Description")
    Me.dgOverride.Columns(4).Caption = getTranslationResource("S")
    Me.dgOverride.Columns(5).Caption = getTranslationResource("Demand")
    Me.dgOverride.Columns(6).Caption = getTranslationResource("Method")
    Me.dgOverride.Columns(7).Caption = getTranslationResource("Adjust Demand")
    Me.dgOverride.Columns(8).Caption = getTranslationResource("Expiration Date")
    Me.dgOverride.Columns(9).Caption = getTranslationResource("Safety Stock")
    Me.dgOverride.Columns(10).Caption = getTranslationResource("Lead Time")
    Me.dgOverride.Columns(11).Caption = getTranslationResource("Safety Days")
    Me.dgOverride.Columns(12).Caption = getTranslationResource("Adj Safety Stock")
    Me.dgOverride.Columns(13).Caption = getTranslationResource("Adj Safety Days")
   
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgOverride, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgOverride.Columns.Count - 1
'        dgOverride.Columns(IndexCounter).HasHeadBackColor = True
'        dgOverride.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgOverride.Columns(IndexCounter).Locked = False Then dgOverride.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgOverride_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dgOverride_InitColumnProps", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgOverride_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgOverride_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgOverride_MouseDown", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgOverride_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) & NumberOfRowsToMove
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgOverride_UnboundPositionData)"
     f_HandleErr , , , Me.Caption & "::dgOverride_UnboundPositionData", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgOverride_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
    
    Dim iRBRow As Integer
    Dim iCol As Integer
    Dim irow As Integer
    Dim iPoint As Integer
    Dim iCount As Integer
    
    'Test for an empty array
    On Error Resume Next
    iCount = UBound(aOverride, 2)
    irow = 0
    If Err.Number <> 0 Then
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            iPoint = iCount
        Else
            iPoint = 0
        End If
    Else
        iPoint = StartLocation
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
    End If

    For iRBRow = 0 To RowBuf.RowCount - 1
        If iPoint < 0 Or iPoint > iCount Or m_DataExists = False Then Exit For

        For iCol = LBound(aOverride, 1) To (RowBuf.ColumnCount - 1) ' (UBound(aOverride, 1))
            RowBuf.Value(iRBRow, iCol) = aOverride(iCol, iPoint)
        Next iCol
        
        RowBuf.Bookmark(iRBRow) = iPoint
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
        
        irow = irow + 1
    Next iRBRow

    RowBuf.RowCount = irow
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgOverride_UnboundReadData)"
     f_HandleErr , , , Me.Caption & "::dgOverride_UnboundReadData", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dgOverride_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim iCol As Integer

    For iCol = 0 To 13
        If Not IsNull(RowBuf.Value(0, iCol)) Then
            aOverride(iCol, CInt(WriteLocation)) = CStr(RowBuf.Value(0, iCol))
        End If
    
    Next iCol

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgOverride_UnboundWriteData)"
     f_HandleErr , , , Me.Caption & "::dgOverride_UnboundWriteData", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07406")
    If StrComp(strMessage, "STATMSG07406") = 0 Then strMessage = "Initializing AIM Forecast Override Utility..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    'A.Stocksdale - For item-filter-multi-select
    g_InitConstants
    g_InitCriteria m_xaOrigCriteria
    'A.Stocksdale - End
        
    'Initialize the Update Stored Procedure
    Set AIM_UpdateOverrides_Sp = New ADODB.Command
    With AIM_UpdateOverrides_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_UpdateOverrides_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    
    End With
    
    'Initialize the recordset
    Set rsOverride = New ADODB.Recordset
    With rsOverride
        .CursorLocation = adUseServer
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    
    End With
    
    'Initialize form variables
    SetTDBDate txtUserFcstExpDate, Date
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    '************************************************************
    'Mar 07 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtCounterStockAdj.Spin.Visible = 1
    Me.txtCounterStockAdjUnits.Spin.Visible = 1
    Me.txtFcstAdj.Spin.Visible = 1
    Me.txtUserFcstExpDate.DropDown.Visible = 1
    '************************************************************

    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Close the recordset
    If f_IsRecordsetValidAndOpen(rsOverride) Then rsOverride.Close
    Set rsOverride = Nothing
    
    'Close the SQL Connection
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Save current grid layout.
     Me.dgOverride.SaveLayout App.Path & "\" & "AIM_ForecastOverride" & Trim$(gUserID) & "_Layout.grd", ssSaveLayoutAll
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
    'Clear messages
    Write_Message ""

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
     'f_HandleErr Me.Caption & "(Form_Unload)"
      f_HandleErr , , , Me.Caption & "::Form_Unload", Now, gDRGeneralError, True, Err
        
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim FileName As String
    Dim FormatOption As Integer
    Dim RowCounter As Integer
    Dim ColCounter As Integer
    Dim s1 As String
    Dim s2 As String
    
    Me.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy
            'Check for an empty set
            If Me.dgOverride.Rows = 0 Then
                Screen.MousePointer = vbNormal
                Exit Sub
            End If
            
            Clipboard.Clear
            
            'Process Column Headings
            For RowCounter = 0 To Me.dgOverride.Columns.Count - 1
                s1 = s1 & Me.dgOverride.Columns(RowCounter).Caption & vbTab
            Next RowCounter
    
            s1 = s1 & vbCrLf
    
            'Process rows
            For RowCounter = LBound(aOverride, 1) To UBound(aOverride, 1)    'Rows
                s2 = ""
                For ColCounter = LBound(aOverride, 2) To UBound(aOverride, 2)    'Columns
                    s2 = s2 & CStr(aOverride(RowCounter, ColCounter)) & vbTab
                Next ColCounter
    
                s2 = s2 & vbCrLf
    
                s1 = s1 & s2
            Next RowCounter
    
            Clipboard.SetText s1, vbCFText
        
        Case 1          'Print
            Me.dgOverride.PrintData ssPrintAllRows, False, True
        
    End Select

    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , Me.Caption & "::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
    
End Sub

Private Function DisplayFilters( _
    p_CurrentFilter As String _
) As String
On Error GoTo ErrorHandler

    'Dim arrItems As XArrayDB
    Dim LockFields As Boolean
    
    'Dim FcstItemFilter As FCST_ITEM_FILTER
    Dim RowCounter As Long, ColCounter As Long
    Dim AllFilters As String, NewFilters As String
    Dim ShowFilters As Form
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    
    LockFields = False  'Users of this screen SHOULD be able to edit existing filters
    Set ShowFilters = AIM_ForecastFilter
    With ShowFilters
        Set .pr_ADOConn = Cn
        'Send over the ones that are to be locked
        Set .rt_xaOrigFilters = m_xaOrigCriteria
        'Send over the ones that are additional to the saved set
        Set .rt_xaExtraFilters = m_xaNewCriteria
        'Send over the entire set (permanent + temporary)
        Set .rt_xaAllFilters = IIf(g_IsArray(m_xaAllCriteria), m_xaAllCriteria, m_xaOrigCriteria)
        'Set other parameters
        .pr_ExistingFilters = p_CurrentFilter
        .pr_LockExisting = LockFields
        .prm_ItemFilter = True
        'Display the the Item Filter screen
        .Show vbModal
        'Retrieve results from the Item Filter screen
        If .rt_UserCancel = False Then
            'Retrieve updated set of all criteria
            g_CXArr1ToXArr2 .rt_xaAllFilters, m_xaAllCriteria
            'Retrieve set of criteria that are additional to the saved set
            g_CXArr1ToXArr2 .rt_xaExtraFilters, m_xaNewCriteria
        End If
    End With
    
    'Destroy the object
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    
'    g_PrintCriteria m_xaOrigCriteria
'    g_PrintCriteria m_xaAllCriteria
'    g_PrintCriteria m_xaNewCriteria
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    'Err.Raise ErrNumber, ErrSource & "{DisplayFilters}", ErrDesc
     f_HandleErr , , , "AIM_ForeCastOverride::DisplayFilters", Now, gDRGeneralError, True, Err
    
End Function

Private Function FetchData()
On Error GoTo ErrorHandler
    
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    Dim strSQL As String
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Display action message
    strMessage = getTranslationResource("STATMSG07400")
    If StrComp(strMessage, "STATMSG07400") = 0 Then strMessage = "Retrieving Forecast/Safety Stock Info..."
    Write_Message strMessage
    
    'Set the SQL Statement
    strSQL = BldGetOverrideSQL(Me.txtUserFcstExpDate.Value, Me.txtFcstAdj.Value, Me.txtCounterStockAdj.Value, _
            Me.txtCounterStockAdjUnits.Value, Me.ckOverrideFcst.Value, Me.ckOverrideCounterStock.Value)
'    With m_FcstItemFilter
'        strSQL = BldGetOverrideSQL(.SubFilterVnId, .SubFilterAssort, .SubFilterLcID, .SubFilterItem, _
'            .SubFilterClass1, .SubFilterClass2, .SubFilterClass3, .SubFilterClass4, _
'            .SubFilterById, .SubFilterItemStatus, _
'            .SubFilterLDivision, .SubFilterLRegion, .SubFilterLStatus, .SubFilterLUserDefined, _
'            Me.txtUserFcstExpDate.Value, Me.txtFcstAdj.Value, Me.txtCounterStockAdj.Value, _
'            Me.txtCounterStockAdjUnits.Value, Me.ckOverrideFcst.Value, Me.ckOverrideCounterStock.Value)
'    End With

    'A.Stocksdale - END - For item-filter-multi-select

    'Check for an empty string
    If Trim$(strSQL) = "" Then
        strMessage = getTranslationResource("MSGBOX07400")
        If StrComp(strMessage, "MSGBOX07400") = 0 Then strMessage = "Unable to construct the SQL Statement."
        strMessage1 = getTranslationResource("MSGBOX07405")
        If StrComp(strMessage1, "MSGBOX07405") = 0 Then strMessage1 = "Please enter selection criteria by using Filter Items Button."
        MsgBox strMessage & vbCrLf & vbCrLf & _
                strMessage1, vbCritical + vbOKOnly, _
                getTranslationResource(Me.Caption)
        
        Screen.MousePointer = vbNormal
        Write_Message ""
        m_DataExists = False
        Exit Function
    End If
    
    'Build the recordset
    If f_IsRecordsetValidAndOpen(rsOverride) Then rsOverride.Close
    On Error Resume Next
    rsOverride.Open strSQL, Cn, adOpenForwardOnly, adLockReadOnly, adCmdText
    If Err.Number <> 0 Then
        strMessage = getTranslationResource("MSGBOX07402")
        If StrComp(strMessage, "MSGBOX07402") = 0 Then strMessage = "Error retrieving forecast/safety stock information."
        MsgBox strMessage & vbCrLf & vbCrLf & _
            Err.Description, vbCritical + vbOKOnly, _
            getTranslationResource(Me.Caption)
        On Error GoTo ErrorHandler
        Screen.MousePointer = vbNormal
        Write_Message ""
        m_DataExists = False
        Exit Function
    End If
    On Error GoTo ErrorHandler
        If rsOverride.BOF = True And rsOverride.eof = True Then
        strMessage = getTranslationResource("MSGBOX07404")
        If StrComp(strMessage, "MSGBOX07404") = 0 Then strMessage = "No Items matched the filter criteria"
        MsgBox strMessage & vbCrLf & vbCrLf & _
            Err.Description, vbInformation + vbOKOnly, _
            getTranslationResource(Me.Caption)
            
        Me.dgOverride.Rows = 0
        ReDim aOverride(0, 0)
        m_DataExists = False
        If f_IsRecordsetValidAndOpen(rsOverride) Then rsOverride.Close
        'Bind the Grid
        Me.dgOverride.ReBind
        Screen.MousePointer = vbNormal
        Write_Message ""
        Exit Function
    End If
    
    'Load the Override Grid
    aOverride = rsOverride.GetRows
    Me.dgOverride.Rows = UBound(aOverride, 2)
    If f_IsRecordsetValidAndOpen(rsOverride) Then rsOverride.Close
    m_DataExists = True
    'Bind the Grid
    Me.dgOverride.ReBind

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    If f_IsRecordsetValidAndOpen(rsOverride) Then rsOverride.Close
    'Err.Raise ErrNumber, ErrSource & "{DisplayFilters}", ErrDesc
     f_HandleErr , , , "AIM_ForeCastOverride::FetchData", Now, gDRGeneralError, True, Err
    
End Function


