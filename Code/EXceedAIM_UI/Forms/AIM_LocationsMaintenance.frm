VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_LocationsMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Locations Maintenance"
   ClientHeight    =   6225
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9315
   Icon            =   "AIM_LocationsMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6225
   ScaleWidth      =   9315
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Heading 
      Height          =   1020
      Left            =   60
      TabIndex        =   3
      Top             =   0
      Width           =   9135
      Begin TDBText6Ctl.TDBText txtLcid 
         Height          =   345
         Left            =   2760
         TabIndex        =   0
         Top             =   210
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3006
         _ExtentY        =   600
         Caption         =   "AIM_LocationsMaintenance.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_LocationsMaintenance.frx":0376
         Key             =   "AIM_LocationsMaintenance.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLName 
         Height          =   345
         Left            =   2760
         TabIndex        =   1
         Top             =   600
         Width           =   6150
         _Version        =   65536
         _ExtentX        =   10848
         _ExtentY        =   609
         Caption         =   "AIM_LocationsMaintenance.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_LocationsMaintenance.frx":0444
         Key             =   "AIM_LocationsMaintenance.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label1 
         Caption         =   "Location ID"
         Height          =   300
         Left            =   120
         TabIndex        =   40
         Top             =   240
         Width           =   2445
      End
      Begin VB.Label Label2 
         Caption         =   "Location Name"
         Height          =   300
         Left            =   120
         TabIndex        =   39
         Top             =   645
         Width           =   2445
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   150
      Top             =   5760
      _ExtentX        =   609
      _ExtentY        =   582
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_LocationsMaintenance.frx":04A6
      ToolBars        =   "AIM_LocationsMaintenance.frx":90A4
   End
   Begin ActiveTabs.SSActiveTabs tabLocation 
      Height          =   4500
      Left            =   60
      TabIndex        =   2
      Top             =   1185
      Width           =   9195
      _ExtentX        =   16219
      _ExtentY        =   7938
      _Version        =   131083
      TabCount        =   4
      TabWidth        =   2010
      TagVariant      =   ""
      Tabs            =   "AIM_LocationsMaintenance.frx":93BC
      Begin ActiveTabs.SSActiveTabPanel sstbpnlAddress 
         Height          =   4110
         Left            =   -99969
         TabIndex        =   41
         Top             =   360
         Width           =   9135
         _ExtentX        =   16113
         _ExtentY        =   7250
         _Version        =   131083
         TabGuid         =   "AIM_LocationsMaintenance.frx":94D9
         Begin VB.Frame fraAddress 
            Height          =   4005
            Left            =   120
            TabIndex        =   42
            Top             =   0
            Width           =   8895
            Begin TDBText6Ctl.TDBText txtLAddress1 
               Height          =   345
               Left            =   2550
               TabIndex        =   27
               Top             =   240
               Width           =   6150
               _Version        =   65536
               _ExtentX        =   10848
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9501
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":956D
               Key             =   "AIM_LocationsMaintenance.frx":958B
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLAddress2 
               Height          =   345
               Left            =   2550
               TabIndex        =   28
               Top             =   615
               Width           =   6150
               _Version        =   65536
               _ExtentX        =   10848
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":95CF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":963B
               Key             =   "AIM_LocationsMaintenance.frx":9659
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLCity 
               Height          =   345
               Left            =   2550
               TabIndex        =   31
               Top             =   1770
               Width           =   1950
               _Version        =   65536
               _ExtentX        =   3440
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":969D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9709
               Key             =   "AIM_LocationsMaintenance.frx":9727
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLState 
               Height          =   345
               Left            =   6825
               TabIndex        =   32
               Top             =   1770
               Width           =   1875
               _Version        =   65536
               _ExtentX        =   3307
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":976B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":97D7
               Key             =   "AIM_LocationsMaintenance.frx":97F5
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   10
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLZip 
               Height          =   345
               Left            =   6825
               TabIndex        =   34
               Top             =   2140
               Width           =   1875
               _Version        =   65536
               _ExtentX        =   3307
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9839
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":98A5
               Key             =   "AIM_LocationsMaintenance.frx":98C3
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   10
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLContact 
               Height          =   345
               Left            =   2550
               TabIndex        =   35
               Top             =   2700
               Width           =   6150
               _Version        =   65536
               _ExtentX        =   10848
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9907
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9973
               Key             =   "AIM_LocationsMaintenance.frx":9991
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLEMail 
               Height          =   345
               Left            =   2550
               TabIndex        =   38
               Top             =   3465
               Width           =   6150
               _Version        =   65536
               _ExtentX        =   10848
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":99D5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9A41
               Key             =   "AIM_LocationsMaintenance.frx":9A5F
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLPhone 
               Height          =   345
               Left            =   2550
               TabIndex        =   36
               Top             =   3075
               Width           =   1950
               _Version        =   65536
               _ExtentX        =   3440
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9AA3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9B0F
               Key             =   "AIM_LocationsMaintenance.frx":9B2D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLFax 
               Height          =   345
               Left            =   6825
               TabIndex        =   37
               Top             =   3075
               Width           =   1875
               _Version        =   65536
               _ExtentX        =   3307
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9B71
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9BDD
               Key             =   "AIM_LocationsMaintenance.frx":9BFB
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLCountry 
               Height          =   345
               Left            =   2550
               TabIndex        =   33
               Top             =   2140
               Width           =   1950
               _Version        =   65536
               _ExtentX        =   3440
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9C3F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9CAB
               Key             =   "AIM_LocationsMaintenance.frx":9CC9
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLAddress3 
               Height          =   345
               Left            =   2550
               TabIndex        =   29
               Top             =   1005
               Width           =   6150
               _Version        =   65536
               _ExtentX        =   10848
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9D0D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9D79
               Key             =   "AIM_LocationsMaintenance.frx":9D97
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLAddress4 
               Height          =   345
               Left            =   2550
               TabIndex        =   30
               Top             =   1380
               Width           =   6150
               _Version        =   65536
               _ExtentX        =   10848
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":9DDB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9E47
               Key             =   "AIM_LocationsMaintenance.frx":9E65
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label lblAddress 
               Caption         =   "City"
               Height          =   300
               Index           =   4
               Left            =   120
               TabIndex        =   54
               Top             =   1813
               Width           =   2445
            End
            Begin VB.Label lblAddress 
               Caption         =   "Address 2"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   53
               Top             =   667
               Width           =   2445
            End
            Begin VB.Label lblAddress 
               Caption         =   "Address 1"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   52
               Top             =   285
               Width           =   2445
            End
            Begin VB.Label lblAddress 
               Caption         =   "State"
               Height          =   300
               Index           =   5
               Left            =   5265
               TabIndex        =   51
               Top             =   1815
               Width           =   1440
            End
            Begin VB.Label lblContact 
               Caption         =   "Location Contact"
               Height          =   300
               Index           =   0
               Left            =   120
               TabIndex        =   50
               Top             =   2745
               Width           =   2445
            End
            Begin VB.Label lblContact 
               Caption         =   "Telephone"
               Height          =   300
               Index           =   1
               Left            =   120
               TabIndex        =   49
               Top             =   3120
               Width           =   2445
            End
            Begin VB.Label lblContact 
               Caption         =   "Email"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   48
               Top             =   3510
               Width           =   2445
            End
            Begin VB.Label lblContact 
               Caption         =   "Fax"
               Height          =   300
               Index           =   2
               Left            =   5265
               TabIndex        =   47
               Top             =   3120
               Width           =   1440
            End
            Begin VB.Label lblAddress 
               Caption         =   "Zip"
               Height          =   300
               Index           =   7
               Left            =   5265
               TabIndex        =   46
               Top             =   2185
               Width           =   1440
            End
            Begin VB.Label lblAddress 
               Caption         =   "Country"
               Height          =   300
               Index           =   6
               Left            =   120
               TabIndex        =   45
               Top             =   2185
               Width           =   2445
            End
            Begin VB.Label lblAddress 
               Caption         =   "Address 3"
               Height          =   300
               Index           =   2
               Left            =   120
               TabIndex        =   44
               Top             =   1049
               Width           =   2445
            End
            Begin VB.Label lblAddress 
               Caption         =   "Address 4"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   43
               Top             =   1431
               Width           =   2445
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel TransferToPanel 
         Height          =   4110
         Left            =   -99969
         TabIndex        =   55
         Top             =   360
         Width           =   9135
         _ExtentX        =   16113
         _ExtentY        =   7250
         _Version        =   131083
         TabGuid         =   "AIM_LocationsMaintenance.frx":9EA9
         Begin VB.Frame Frame3 
            Height          =   4005
            Left            =   120
            TabIndex        =   25
            Top             =   0
            Width           =   8895
            Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown dcTransferToLcDropDown 
               Height          =   1095
               Left            =   3360
               TabIndex        =   79
               Top             =   960
               Width           =   2415
               _Version        =   196617
               RowHeight       =   423
               ExtraHeight     =   185
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   4260
               _ExtentY        =   1931
               _StockProps     =   77
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dcTransferToLc 
               Height          =   3615
               Left            =   120
               TabIndex        =   26
               Top             =   240
               Width           =   8655
               _Version        =   196617
               DataMode        =   1
               AllowAddNew     =   -1  'True
               MultiLine       =   0   'False
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   15266
               _ExtentY        =   6376
               _StockProps     =   79
               Caption         =   "Transfer To Locations"
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel ControlPanel 
         Height          =   4110
         Left            =   -99969
         TabIndex        =   56
         Top             =   360
         Width           =   9135
         _ExtentX        =   16113
         _ExtentY        =   7250
         _Version        =   131083
         TabGuid         =   "AIM_LocationsMaintenance.frx":9ED1
         Begin VB.Frame Frame2 
            Height          =   4005
            Left            =   120
            TabIndex        =   57
            Top             =   0
            Width           =   8895
            Begin VB.CheckBox Check19 
               Caption         =   "Check19"
               Height          =   255
               Left            =   4440
               TabIndex        =   100
               Top             =   1815
               Width           =   255
            End
            Begin VB.CheckBox Check17 
               Caption         =   "Check17"
               Height          =   255
               Left            =   4440
               TabIndex        =   99
               Top             =   1050
               Width           =   255
            End
            Begin VB.CheckBox Check16 
               Caption         =   "Check16"
               Height          =   255
               Left            =   4440
               TabIndex        =   98
               Top             =   720
               Width           =   255
            End
            Begin VB.CheckBox Check15 
               Caption         =   "Check15"
               Height          =   255
               Left            =   4440
               TabIndex        =   97
               Top             =   330
               Width           =   255
            End
            Begin VB.CheckBox Check11 
               Caption         =   "Check11"
               Height          =   225
               Left            =   135
               TabIndex        =   96
               Top             =   1470
               Width           =   195
            End
            Begin VB.CheckBox Check12 
               Caption         =   "Check12"
               Height          =   225
               Left            =   135
               TabIndex        =   95
               Top             =   1845
               Width           =   195
            End
            Begin VB.CheckBox Check10 
               Caption         =   "Check10"
               Height          =   225
               Left            =   135
               TabIndex        =   94
               Top             =   1065
               Width           =   195
            End
            Begin VB.CheckBox Check9 
               Caption         =   "Check9"
               Height          =   225
               Left            =   135
               TabIndex        =   93
               Top             =   675
               Width           =   195
            End
            Begin VB.CheckBox Check8 
               Caption         =   "Check8"
               Height          =   225
               Left            =   135
               TabIndex        =   92
               Top             =   345
               Width           =   195
            End
            Begin VB.CheckBox Check21 
               Caption         =   "Check17"
               Height          =   255
               Left            =   4440
               TabIndex        =   90
               Top             =   2520
               Width           =   255
            End
            Begin VB.CheckBox Check20 
               Caption         =   "Check16"
               Height          =   255
               Left            =   4440
               TabIndex        =   89
               Top             =   2205
               Width           =   255
            End
            Begin VB.CheckBox Check18 
               Caption         =   "Check15"
               Height          =   255
               Left            =   4440
               TabIndex        =   88
               Top             =   1425
               Width           =   255
            End
            Begin VB.CheckBox Check14 
               Caption         =   "Check14"
               Height          =   225
               Left            =   135
               TabIndex        =   87
               Top             =   2565
               Width           =   195
            End
            Begin VB.CheckBox Check13 
               Caption         =   "Check13"
               Height          =   225
               Left            =   135
               TabIndex        =   86
               Top             =   2190
               Width           =   195
            End
            Begin VB.CheckBox Ck_Upd 
               Caption         =   "Update Current Year Option"
               Height          =   340
               Left            =   5160
               TabIndex        =   24
               Top             =   2520
               Width           =   3135
            End
            Begin TDBNumber6Ctl.TDBNumber txtExceptionPct 
               Height          =   345
               Left            =   7140
               TabIndex        =   23
               Top             =   2140
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":9EF9
               Caption         =   "AIM_LocationsMaintenance.frx":9F19
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":9F85
               Keys            =   "AIM_LocationsMaintenance.frx":9FA3
               Spin            =   "AIM_LocationsMaintenance.frx":9FED
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   0
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MaxValue        =   100
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ","
               ShowContextMenu =   -1
               ValueVT         =   82575361
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDemandSource 
               Bindings        =   "AIM_LocationsMaintenance.frx":A015
               DataField       =   " "
               Height          =   345
               Left            =   7110
               TabIndex        =   18
               Top             =   240
               Width           =   1470
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2593
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLeadTimeSource 
               Bindings        =   "AIM_LocationsMaintenance.frx":A020
               DataField       =   " "
               Height          =   345
               Left            =   7140
               TabIndex        =   19
               Top             =   620
               Width           =   1470
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2593
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBNumber6Ctl.TDBNumber txtPutAwayDays 
               Height          =   345
               Left            =   2715
               TabIndex        =   11
               Top             =   240
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":A02B
               Caption         =   "AIM_LocationsMaintenance.frx":A04B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A0B7
               Keys            =   "AIM_LocationsMaintenance.frx":A0D5
               Spin            =   "AIM_LocationsMaintenance.frx":A11F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1245189
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtReplenCost 
               Height          =   345
               Left            =   2715
               TabIndex        =   13
               Top             =   1005
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":A147
               Caption         =   "AIM_LocationsMaintenance.frx":A167
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A1D3
               Keys            =   "AIM_LocationsMaintenance.frx":A1F1
               Spin            =   "AIM_LocationsMaintenance.frx":A23B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#####0.0000;-#####0.0000;0.0000;0.0000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#####0.0000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999999.9999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   28246017
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLast_FcstUpdCyc 
               Height          =   345
               Left            =   2715
               TabIndex        =   14
               Top             =   1380
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":A263
               Caption         =   "AIM_LocationsMaintenance.frx":A283
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A2EF
               Keys            =   "AIM_LocationsMaintenance.frx":A30D
               Spin            =   "AIM_LocationsMaintenance.frx":A357
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "########0;-########0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "########0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999999999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   1
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1245189
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLookBackPds 
               Height          =   345
               Left            =   2715
               TabIndex        =   12
               Top             =   615
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":A37F
               Caption         =   "AIM_LocationsMaintenance.frx":A39F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A40B
               Keys            =   "AIM_LocationsMaintenance.frx":A429
               Spin            =   "AIM_LocationsMaintenance.frx":A473
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   13
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   82575361
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMReviewers 
               DataSource      =   "rslocations"
               Height          =   345
               Left            =   7140
               TabIndex        =   22
               Top             =   1760
               Width           =   1470
               DataFieldList   =   "UserId"
               _Version        =   196617
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2593
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
               DataField       =   " "
               DataSource      =   "rsLocations"
               Height          =   345
               Left            =   7140
               TabIndex        =   20
               Top             =   1000
               Width           =   1470
               DataFieldList   =   "Userid"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2593
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBDate6Ctl.TDBDate txtStkDate 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "MM/dd/yy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   7140
               TabIndex        =   21
               Top             =   1380
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calendar        =   "AIM_LocationsMaintenance.frx":A49B
               Caption         =   "AIM_LocationsMaintenance.frx":A5B3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A61F
               Keys            =   "AIM_LocationsMaintenance.frx":A63D
               Spin            =   "AIM_LocationsMaintenance.frx":A69B
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "03/07/2002"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   37322
               CenturyMode     =   0
            End
            Begin TDBNumber6Ctl.TDBNumber txtFreezePds 
               Height          =   345
               Left            =   2715
               TabIndex        =   17
               Top             =   2520
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":A6C3
               Caption         =   "AIM_LocationsMaintenance.frx":A6E3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A74F
               Keys            =   "AIM_LocationsMaintenance.frx":A76D
               Spin            =   "AIM_LocationsMaintenance.frx":A7B7
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   0
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "####0;;Null"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "####0"
               HighlightText   =   0
               MarginBottom    =   1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MaxValue        =   99999
               MinValue        =   -99999
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ","
               ShowContextMenu =   -1
               ValueVT         =   2088828933
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDmdScalingFactor 
               Height          =   345
               Left            =   2715
               TabIndex        =   15
               Top             =   1755
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":A7DF
               Caption         =   "AIM_LocationsMaintenance.frx":A7FF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":A86B
               Keys            =   "AIM_LocationsMaintenance.frx":A889
               Spin            =   "AIM_LocationsMaintenance.frx":A8D3
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9.999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   82837505
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBDate6Ctl.TDBDate txtScalingEffUntil 
               BeginProperty DataFormat 
                  Type            =   1
                  Format          =   "MM/dd/yy"
                  HaveTrueFalseNull=   0
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   3
               EndProperty
               Height          =   345
               Left            =   2715
               TabIndex        =   16
               Top             =   2145
               Width           =   1470
               _Version        =   65536
               _ExtentX        =   2593
               _ExtentY        =   609
               Calendar        =   "AIM_LocationsMaintenance.frx":A8FB
               Caption         =   "AIM_LocationsMaintenance.frx":AA13
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":AA7F
               Keys            =   "AIM_LocationsMaintenance.frx":AA9D
               Spin            =   "AIM_LocationsMaintenance.frx":AAFB
               AlignHorizontal =   0
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "03/07/2002"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   37322
               CenturyMode     =   0
            End
            Begin VB.Line Line3 
               BorderWidth     =   3
               Index           =   0
               X1              =   4920
               X2              =   4920
               Y1              =   330
               Y2              =   2790
            End
            Begin VB.Line Line2 
               BorderWidth     =   3
               Index           =   0
               X1              =   480
               X2              =   480
               Y1              =   285
               Y2              =   2745
            End
            Begin VB.Label Label5 
               Caption         =   "Freeze Period"
               Height          =   300
               Left            =   615
               TabIndex        =   78
               Top             =   2565
               Width           =   2085
            End
            Begin VB.Label Label4 
               Caption         =   "Exception Percentage"
               Height          =   300
               Left            =   5160
               TabIndex        =   77
               Top             =   2190
               Width           =   1965
            End
            Begin VB.Label Label3 
               Caption         =   "Default Reviewer ID"
               Height          =   300
               Left            =   5190
               TabIndex        =   76
               Top             =   1800
               Width           =   1965
            End
            Begin VB.Label Label12 
               Caption         =   "Put-Away Days"
               Height          =   300
               Left            =   615
               TabIndex        =   67
               Top             =   285
               Width           =   2085
            End
            Begin VB.Label Label27 
               Caption         =   "Scaling Effective Until"
               Height          =   300
               Left            =   615
               TabIndex        =   66
               Top             =   2190
               Width           =   2085
            End
            Begin VB.Label Label26 
               Caption         =   "Demand Scaling Factor"
               Height          =   300
               Left            =   615
               TabIndex        =   65
               Top             =   1800
               Width           =   2085
            End
            Begin VB.Label Label25 
               Caption         =   "Stock Status Date"
               Height          =   300
               Left            =   5190
               TabIndex        =   64
               Top             =   1425
               Width           =   1965
            End
            Begin VB.Label Label14 
               Caption         =   "Look Back Periods"
               Height          =   300
               Left            =   615
               TabIndex        =   63
               Top             =   660
               Width           =   2085
            End
            Begin VB.Label Label13 
               Caption         =   "Lead Time Source"
               Height          =   300
               Left            =   5190
               TabIndex        =   62
               Top             =   660
               Width           =   1965
            End
            Begin VB.Label Label15 
               Caption         =   "Replenishment Cost"
               Height          =   300
               Left            =   615
               TabIndex        =   61
               Top             =   1050
               Width           =   2085
            End
            Begin VB.Label Label16 
               Caption         =   "Demand Source"
               Height          =   300
               Left            =   5190
               TabIndex        =   60
               Top             =   285
               Width           =   1965
            End
            Begin VB.Label Label17 
               Caption         =   "Default Buyer ID"
               Height          =   300
               Left            =   5190
               TabIndex        =   59
               Top             =   1050
               Width           =   1965
            End
            Begin VB.Label Label24 
               Caption         =   "Last Forecast Update Cycle"
               Height          =   300
               Left            =   615
               TabIndex        =   58
               Top             =   1425
               Width           =   2085
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel GeneralPanel 
         Height          =   4110
         Left            =   30
         TabIndex        =   68
         Top             =   360
         Width           =   9135
         _ExtentX        =   16113
         _ExtentY        =   7250
         _Version        =   131083
         TabGuid         =   "AIM_LocationsMaintenance.frx":AB23
         Begin VB.Frame Frame1 
            Height          =   4005
            Left            =   120
            TabIndex        =   69
            Top             =   0
            Width           =   8895
            Begin VB.CheckBox Check7 
               Caption         =   "Check7"
               Height          =   195
               Left            =   240
               TabIndex        =   91
               Top             =   2640
               Width           =   225
            End
            Begin VB.CheckBox Check6 
               Caption         =   "Check6"
               Height          =   195
               Left            =   240
               TabIndex        =   85
               Top             =   2190
               Width           =   255
            End
            Begin VB.CheckBox Check5 
               Caption         =   "Check5"
               Height          =   195
               Left            =   240
               TabIndex        =   84
               Top             =   1800
               Width           =   255
            End
            Begin VB.CheckBox Check4 
               Caption         =   "Check4"
               Height          =   195
               Left            =   240
               TabIndex        =   83
               Top             =   1425
               Width           =   255
            End
            Begin VB.CheckBox Check3 
               Caption         =   "Check3"
               Height          =   195
               Left            =   240
               TabIndex        =   82
               Top             =   1050
               Width           =   255
            End
            Begin VB.CheckBox Check2 
               Caption         =   "Check2"
               Height          =   195
               Left            =   240
               TabIndex        =   81
               Top             =   660
               Width           =   255
            End
            Begin VB.CheckBox Check1 
               Caption         =   "Check1"
               Height          =   195
               Left            =   240
               TabIndex        =   80
               Top             =   285
               Width           =   255
            End
            Begin VB.CheckBox Ck_Drop_Ship 
               Alignment       =   1  'Right Justify
               Caption         =   "Drop Ship"
               Height          =   300
               Left            =   735
               TabIndex        =   5
               Top             =   2565
               Width           =   1680
            End
            Begin TDBText6Ctl.TDBText txtLDivision 
               Height          =   345
               Left            =   3225
               TabIndex        =   7
               Top             =   1005
               Width           =   2310
               _Version        =   65536
               _ExtentX        =   4075
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":AB4B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":ABB7
               Key             =   "AIM_LocationsMaintenance.frx":ABD5
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLRegion 
               Height          =   345
               Left            =   3225
               TabIndex        =   8
               Top             =   1380
               Width           =   2310
               _Version        =   65536
               _ExtentX        =   4075
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":AC19
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":AC85
               Key             =   "AIM_LocationsMaintenance.frx":ACA3
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLUserDefined 
               Height          =   345
               Left            =   3225
               TabIndex        =   9
               Top             =   1755
               Width           =   2310
               _Version        =   65536
               _ExtentX        =   4075
               _ExtentY        =   609
               Caption         =   "AIM_LocationsMaintenance.frx":ACE7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":AD53
               Key             =   "AIM_LocationsMaintenance.frx":AD71
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLStatus 
               DataField       =   " "
               Height          =   345
               Left            =   3225
               TabIndex        =   6
               Top             =   615
               Width           =   2310
               DataFieldList   =   "Column 0"
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   4075
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "Column 1"
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLType 
               DataField       =   " "
               Height          =   345
               Left            =   3225
               TabIndex        =   4
               Top             =   240
               Width           =   2310
               DataFieldList   =   "Column 0"
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   4075
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "Column 1"
            End
            Begin TDBNumber6Ctl.TDBNumber txtLRank 
               Height          =   345
               Left            =   3240
               TabIndex        =   10
               Top             =   2145
               Width           =   690
               _Version        =   65536
               _ExtentX        =   1217
               _ExtentY        =   609
               Calculator      =   "AIM_LocationsMaintenance.frx":ADB5
               Caption         =   "AIM_LocationsMaintenance.frx":ADD5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_LocationsMaintenance.frx":AE41
               Keys            =   "AIM_LocationsMaintenance.frx":AE5F
               Spin            =   "AIM_LocationsMaintenance.frx":AEA9
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   255
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   1
               ValueVT         =   82771969
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Line Line1 
               BorderWidth     =   3
               Index           =   0
               X1              =   585
               X2              =   585
               Y1              =   285
               Y2              =   2820
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Rank"
               Height          =   300
               Index           =   2
               Left            =   720
               TabIndex        =   75
               Top             =   2190
               Width           =   2445
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "User-defined"
               Height          =   300
               Index           =   5
               Left            =   720
               TabIndex        =   74
               Top             =   1800
               Width           =   2445
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Region"
               Height          =   300
               Index           =   4
               Left            =   720
               TabIndex        =   73
               Top             =   1425
               Width           =   2445
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Division"
               Height          =   300
               Index           =   3
               Left            =   720
               TabIndex        =   72
               Top             =   1050
               Width           =   2445
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Status"
               Height          =   300
               Index           =   1
               Left            =   720
               TabIndex        =   71
               Top             =   660
               Width           =   2445
            End
            Begin VB.Label lblLGeneral 
               Caption         =   "Location Type"
               Height          =   300
               Index           =   0
               Left            =   720
               TabIndex        =   70
               Top             =   285
               Width           =   2445
            End
         End
      End
   End
End
Attribute VB_Name = "AIM_LocationsMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsBuyers As ADODB.Recordset
Dim rsReviewers As ADODB.Recordset
Dim rsLocations As ADODB.Recordset
Attribute rsLocations.VB_VarHelpID = -1
Dim rsAimTransferTo As ADODB.Recordset
Dim rsAimTransferInsert As ADODB.Recordset
Dim rsAimLocAndDesc As ADODB.Recordset

Dim LocationsBookMark As Variant

Dim m_xaOrigCriteria As XArrayDB
Dim m_xaAllCriteria As XArrayDB
Dim m_xaNewCriteria As XArrayDB
Dim FilterString As String
Dim rsLcidCount As ADODB.Recordset
Dim RecordCount As Long
Dim cmdMultiLocUpdate As New ADODB.Command
Private Function GetLcidRecordCount( _
    ArgInput As String _
) As Long
On Error GoTo ErrorHandler
 Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
 Dim SqlStmt As String
 Dim RecordCount As Long
    If f_IsRecordsetOpenAndPopulated(rsLcidCount) Then
       rsLcidCount.Close
    End If
  'Validate the Item for the location
    'SqlStmt = "SELECT LcID,item FROM Item  Where " + ArgInput
    SqlStmt = "Select count(*) as  'RecordCount' " + "" + ArgInput
    
    
    rsLcidCount.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
        
    If f_IsRecordsetOpenAndPopulated(rsLcidCount) Then
        RecordCount = rsLcidCount("RecordCount").Value
    Else
        RecordCount = 0
    End If
    GetLcidRecordCount = RecordCount

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    'Err.Raise ErrNumber, ErrSource & "{GetLcidRecordCount}", ErrDesc
    f_HandleErr , , , "AIM_LocationsMaintenance::GetLcidRecordCount", Now, gDRGeneralError, True, Err
End Function

Private Function EnableDesibleCheckBoxes( _
    ArgInput As Boolean) As Boolean
On Error GoTo ErrorHandler
Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
Dim IndexCounter As Integer
'For IndexCounter = 1 To 21
    Check1.Visible = ArgInput
    Check2.Visible = ArgInput
    Check3.Visible = ArgInput
    Check4.Visible = ArgInput
    Check5.Visible = ArgInput
    Check6.Visible = ArgInput
    Check7.Visible = ArgInput
    Check8.Visible = ArgInput
    Check9.Visible = ArgInput
    Check10.Visible = ArgInput
    Check11.Visible = ArgInput
    Check12.Visible = ArgInput
    Check13.Visible = ArgInput
    Check14.Visible = ArgInput
    Check15.Visible = ArgInput
    Check16.Visible = ArgInput
    Check17.Visible = ArgInput
    Check18.Visible = ArgInput
    Check19.Visible = ArgInput
    Check20.Visible = ArgInput
    Check21.Visible = ArgInput
    
    Line1(0).Visible = ArgInput
    Line2(0).Visible = ArgInput
    Line3(0).Visible = ArgInput
    
    
    If ArgInput = False Then
        Check1.Value = 0    'unchecked
        Check2.Value = 0
        Check3.Value = 0
        Check4.Value = 0
        Check5.Value = 0
        Check6.Value = 0
        Check7.Value = 0
        Check8.Value = 0
        Check9.Value = 0
        Check10.Value = 0
        Check11.Value = 0
        Check12.Value = 0
        Check13.Value = 0
        Check14.Value = 0
        Check15.Value = 0
        Check16.Value = 0
        Check17.Value = 0
        Check18.Value = 0
        Check19.Value = 0
        Check20.Value = 0
        Check21.Value = 0
        

   End If
    
'Next IndexCounter
 
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    'Err.Raise ErrNumber, ErrSource & "{EnableDesibleCheckBoxs}", ErrDesc
    f_HandleErr , , , "AIM_LocationsMaintenance::EnableDesibleCheckBoxs", Now, gDRGeneralError, True, Err
End Function



Private Function DisplayFilters( _
    p_CurrentFilter As String _
) As String
On Error GoTo ErrorHandler

    'Dim arrItems As XArrayDB
    Dim LockFields As Boolean
    
    'Dim FcstItemFilter As FCST_ITEM_FILTER
    Dim RowCounter As Long, ColCounter As Long
    Dim AllFilters As String, NewFilters As String
    Dim ShowFilters As Form
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    
    LockFields = False  'Users of this screen SHOULD be able to edit existing filters
    Set ShowFilters = AIM_LocationFilter
    With ShowFilters
        Set .pr_ADOConn = Cn
        'Send over the ones that are to be locked
        Set .rt_xaOrigFilters = m_xaOrigCriteria
        'Send over the ones that are additional to the saved set
        Set .rt_xaExtraFilters = m_xaNewCriteria
        'Send over the entire set (permanent + temporary)
        Set .rt_xaAllFilters = IIf(g_IsArray(m_xaAllCriteria), m_xaAllCriteria, m_xaOrigCriteria)
        'Set other parameters
        .pr_ExistingFilters = p_CurrentFilter
        .pr_LockExisting = LockFields
        'Display the the Item Filter screen
        g_PrintCriteria m_xaOrigCriteria
        g_PrintCriteria m_xaAllCriteria
        g_PrintCriteria m_xaNewCriteria
        .Show vbModal
        'Retrieve results from the Item Filter screen
        FilterString = ""
        If .rt_UserCancel = False Then
            'Me.txtSelected.Text = .rt_AllFilters
            'Me.txtNewFilters.Text = .rt_NewFilters
            'FilterString = .rt_AllFilters
            FilterString = .rt_ItemSelectStmt
            'FilterString = BuildWhereClause(FilterString)
            'Retrieve updated set of all criteria
            g_CXArr1ToXArr2 .rt_xaAllFilters, m_xaAllCriteria
            'Retrieve set of criteria that are additional to the saved set
            g_CXArr1ToXArr2 .rt_xaExtraFilters, m_xaNewCriteria
        End If
    End With
    
    'Destroy the object
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    
    g_PrintCriteria m_xaOrigCriteria
    g_PrintCriteria m_xaAllCriteria
    g_PrintCriteria m_xaNewCriteria
    DisplayFilters = FilterString
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    'Err.Raise ErrNumber, ErrSource & "{DisplayFilters}", ErrDesc
    f_HandleErr , , , "AIM_LocationsMaintenance::DisplayFilters", Now, gDRGeneralError, True, Err
End Function
Private Function Refresh_Form()
On Error GoTo ErrorHandler

    'Enable/Disable Key fields
    If rsLocations.EditMode = adEditAdd Then
        If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
        Me.tbNavigation.Tools("ID_Delete").Enabled = False
        Me.tbNavigation.Tools("ID_AddNew").Enabled = False
        Me.txtLcid.Enabled = True
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")
    Else
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            Me.tbNavigation.Tools("ID_Save").Enabled = False
            Me.tbNavigation.Tools("ID_Delete").Enabled = False
            Me.tbNavigation.Tools("ID_AddNew").Enabled = False
        Else
            Me.tbNavigation.Tools("ID_Save").Enabled = True
            Me.tbNavigation.Tools("ID_Delete").Enabled = True
            Me.tbNavigation.Tools("ID_AddNew").Enabled = True
        End If

        Me.txtLcid.Enabled = False
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Edit")
    End If
    
    'Bind to the Locations Binding Collection
    Me.txtLcid.Text = rsLocations!LcId
    Me.txtLName.Text = rsLocations!LName
    Me.dcLType.Value = rsLocations!LType
    Me.dcLStatus.Value = rsLocations!LStatus
    
    Me.txtLDivision.Text = IIf(IsNull(rsLocations!LDivision), "", rsLocations!LDivision)
    Me.txtLRegion.Text = IIf(IsNull(rsLocations!LRegion), "", rsLocations!LRegion)
    Me.txtLUserDefined.Text = IIf(IsNull(rsLocations!LUserDefined), "", rsLocations!LUserDefined)
    Me.txtLRank.Value = IIf(IsNull(rsLocations!LRank), 0, rsLocations!LRank)
    Me.Ck_Drop_Ship.Value = IIf(rsLocations!DropShip_XDock = "Y", VALUETRUE, VALUEFALSE)
    Me.Ck_Upd.Value = IIf(rsLocations!UpdateCurrentYearOption = "Y", VALUETRUE, VALUEFALSE)
    
    Me.txtPutAwayDays.Value = rsLocations!PutAwayDays
    If (rsLocations!LookBackPds < 13) Then
        Me.txtLookBackPds.Value = 13
    ElseIf (rsLocations!LookBackPds > 52) Then
        Me.txtLookBackPds.Value = 52
    Else
        Me.txtLookBackPds.Value = rsLocations!LookBackPds
    End If
    Me.txtReplenCost.Value = rsLocations!ReplenCost
    Me.dcDemandSource.Value = rsLocations!DemandSource
    Me.dcLeadTimeSource.Value = rsLocations!LeadTimeSource
    Me.dcAIMUsers.Text = rsLocations!Dft_ById
    Me.dcAIMReviewers.Text = IIf(IsNull(rsLocations!Dft_ReviewerID), "", rsLocations!Dft_ReviewerID)
    Me.txtLast_FcstUpdCyc.Value = rsLocations!Last_FcstUpdCyc
    Me.txtDmdScalingFactor.Value = rsLocations!DmdScalingFactor
    Me.txtExceptionPct.Value = IIf(IsNull(rsLocations!ExceptionPct), 0, rsLocations!ExceptionPct)
    SetTDBDate Me.txtStkDate, rsLocations!StkDate
    SetTDBDate Me.txtScalingEffUntil, rsLocations!ScalingEffUntil
    Me.txtFreezePds.Text = IIf(IsNull(rsLocations!Freeze_Period), 0, rsLocations!Freeze_Period)
    Me.txtLAddress1.Text = rsLocations!LAddress1
    Me.txtLAddress2.Text = rsLocations!LAddress2
    Me.txtLAddress3.Text = IIf(IsNull(rsLocations!LAddress3), "", rsLocations!LAddress3)
    Me.txtLAddress4.Text = IIf(IsNull(rsLocations!LAddress4), "", rsLocations!LAddress4)
    Me.txtLCity.Text = rsLocations!LCity
    Me.txtLState.Text = rsLocations!LState
    Me.txtLZip.Text = rsLocations!LZip
    Me.txtLCountry.Text = IIf(IsNull(rsLocations!LCountry), "", rsLocations!LCountry)
    Me.txtLContact.Text = rsLocations!LContact
    Me.txtLPhone.Text = rsLocations!LPhone
    Me.txtLFax.Text = rsLocations!LFax
    Me.txtLEMail.Text = rsLocations!LEMail
    
Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    'Err.Raise Err.Number, Err.source, Err.Description & "(Refresh_Form)"
     f_HandleErr , , , "AIM_LocationsMaintenance::Refresh_Form", Now, gDRGeneralError, True, Err
End Function

Private Sub f_Locations_InitializeNew()
On Error GoTo ErrorHandler

    If Not f_IsRecordsetValidAndOpen(rsLocations) Then Exit Sub
    rsLocations.AddNew
            
    rsLocations!LcId = getTranslationResource("New Location")
    
    rsLocations!LName = ""
    rsLocations!LAddress1 = ""
    rsLocations!LAddress2 = ""
    rsLocations!LCity = ""
    rsLocations!LState = ""
    rsLocations!LZip = ""
    rsLocations!LContact = ""
    rsLocations!LPhone = ""
    rsLocations!LFax = ""
    rsLocations!LEMail = ""
    rsLocations!PutAwayDays = 0
    rsLocations!LookBackPds = 52
    rsLocations!ReplenCost = 5
    rsLocations!DemandSource = "S"
    rsLocations!LeadTimeSource = "S"
    rsLocations!Dft_ById = ""
    rsLocations!Last_FcstUpdCyc = 0
    rsLocations!StkDate = Format(Date, g_ISO_DATE_FORMAT)
    rsLocations!DmdScalingFactor = 1
    rsLocations!ScalingEffUntil = "01/01/1990"
    rsLocations!DropShip_XDock = VALUEFALSE
    rsLocations!Freeze_Period = 0
    'Enable/Disable Key fields
    Me.txtLcid.Enabled = True
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")

    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    
Exit Sub
ErrorHandler:
    Me.MousePointer = vbDefault
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_Locations_InitializeNew)"
    f_HandleErr , , , "AIM_LocationsMaintenance::f_Locations_InitializeNew", Now, gDRGeneralError, True, Err
End Sub

Private Function f_Locations_Save()
On Error GoTo ErrorHandler

'    Dim RtnCode As Long
'    Dim strMessage As String
'
'    Cn.Errors.Clear
'
'    'Validate required info
'    RtnCode = UserInputValidation
'    If RtnCode = SUCCEED Then
'        'Save
'        rsLocations.Update
'
'        If Cn.Errors.Count > 0 Then
'            ADOErrorHandler Cn, Cn.Errors.Count - 1
'        Else
'            strMessage = getTranslationResource("STATMSG03201")
'            If StrComp(strMessage, "STATMSG03201") = 0 Then strMessage = "Location successfully updated."
'            Write_Message strMessage
'        End If
'    Else
'        f_Locations_Save = RtnCode
'    End If



Dim RtnCode As Long
    Dim strMessage As String
    
    Cn.Errors.Clear
    If RecordCount <= 1 Then
        'Validate required info
        RtnCode = UserInputValidation
        If RtnCode = SUCCEED Then
            'Save
            rsLocations.Update
        
            If Cn.Errors.Count > 0 Then
                ADOErrorHandler Cn, Cn.Errors.Count - 1
            Else
                strMessage = getTranslationResource("STATMSG03201")
                If StrComp(strMessage, "STATMSG03201") = 0 Then strMessage = "Location successfully updated."
                Write_Message strMessage
            End If
        Else
            f_Locations_Save = RtnCode
        End If
    Else
        UpdateLcidRecords ("test")
    End If
Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_Locations_Save)"
    f_HandleErr , , , "AIM_LocationsMaintenance::f_Locations_Save", Now, gDRGeneralError, True, Err
End Function
Private Function UpdateLcidRecords( _
    ArgInput As String _
) As Boolean
On Error GoTo ErrorHandler
 Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
 Dim SqlStmt As String

 
'Build the sql for mass update

'Build the columns update script
If Check1.Value = Checked Then
   SqlStmt = SqlStmt + " LType = " + "'" + CStr(dcLType.Value) + "',"
End If
If Check2.Value = Checked Then
   SqlStmt = SqlStmt + " LStatus  = " + "'" + CStr(dcLStatus.Value) + "',"
End If
If Check3.Value = Checked Then
   SqlStmt = SqlStmt + " LDivision = " + "'" + txtLDivision.Text + "',"
End If
If Check4.Value = Checked Then
   SqlStmt = SqlStmt + " LRegion = " + "'" + txtLRegion.Text + "',"
End If
If Check5.Value = Checked Then
   SqlStmt = SqlStmt + " LUserDefined = " + "'" + txtLUserDefined.Text + "',"
End If
If Check6.Value = Checked Then
   SqlStmt = SqlStmt + " LRank  = " + "'" + txtLRank.Text + "',"
End If
If Check7.Value = Checked Then
   SqlStmt = SqlStmt + " DropShip_XDock = " + "'" + IIf(Me.Ck_Drop_Ship.Value = VALUETRUE, "Y", "N") + "',"
End If
If Check8.Value = Checked Then
   SqlStmt = SqlStmt + " PutAwaydays = " + "'" + CStr(txtPutAwayDays.Value) + "',"
End If
If Check9.Value = Checked Then
   SqlStmt = SqlStmt + " LookBackPds = " + "'" + CStr(txtLookBackPds.Text) + "',"
End If
If Check10.Value = Checked Then
   SqlStmt = SqlStmt + " ReplenCost= " + "'" + CStr(txtReplenCost.Text) + "',"
End If
If Check11.Value = Checked Then
   SqlStmt = SqlStmt + " Last_FcstUpdCyc = " + "'" + CStr(txtLast_FcstUpdCyc.Text) + "',"
End If
If Check12.Value = Checked Then
   SqlStmt = SqlStmt + " DmdScalingFactor = " + "'" + CStr(txtDmdScalingFactor.Text) + "',"
End If


If Check13.Value = Checked Then

SqlStmt = SqlStmt + " ScalingEffUntil = " + "'" + CStr(Format(Me.txtScalingEffUntil.Text, g_ISO_DATE_FORMAT)) + "',"
'SqlStmt = SqlStmt + " ScalingEffUntil = " + "'" + CStr(txtScalingEffUntil.Text) + "',"
End If
If Check14.Value = Checked Then
   SqlStmt = SqlStmt + " Freeze_Period = " + "'" + CStr(txtFreezePds.Text) + "',"
End If
If Check15.Value = Checked Then
   SqlStmt = SqlStmt + " DemandSource = " + "'" + dcDemandSource.Value + "',"
End If
If Check16.Value = Checked Then
   SqlStmt = SqlStmt + " LeadTimeSource= " + "'" + dcLeadTimeSource.Value + "',"
End If
If Check17.Value = Checked Then
   SqlStmt = SqlStmt + " Dft_ById = " + "'" + dcAIMUsers.Value + "',"
End If
If Check18.Value = Checked Then
   SqlStmt = SqlStmt + " Stkdate= " + "'" + CStr(Format(txtStkDate.Text, g_ISO_DATE_FORMAT)) + "',"
End If
If Check19.Value = Checked Then
   SqlStmt = SqlStmt + " Dft_ReviewerId = " + "'" + dcAIMReviewers.Value + "',"
End If
If Check20.Value = Checked Then
   SqlStmt = SqlStmt + " ExceptionPct = " + "'" + txtExceptionPct.Text + "',"
End If
If Check21.Value = Checked Then
   SqlStmt = SqlStmt + " UpdateCurrentYearOption = " + "'" + IIf(Me.Ck_Upd.Value = VALUETRUE, "Y", "N") + "',"
End If


'remove the last , from the sqlstmt if the sqlstmt is valid
If Len(SqlStmt) > 0 Then
    SqlStmt = Mid(SqlStmt, 1, InStrRev(SqlStmt, ",", -1, vbTextCompare) - 1)
    SqlStmt = "Update AIMLocations set " + SqlStmt & " " + FilterString
    cmdMultiLocUpdate.CommandText = SqlStmt
    cmdMultiLocUpdate.Execute
Else
    MsgBox ("No check box was checked so no fields will be updated")
End If


Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    'Err.Raise ErrNumber, ErrSource & "{UpdateLcidRecords}", ErrDesc
    f_HandleErr , , , "AIM_LocationsMaintenance::UpdateLcidRecords", Now, gDRGeneralError, True, Err
End Function
Private Function f_Locations_Update()
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsLocations) Then Exit Function
    
    'Bind to the Locations Binding Collection
    rsLocations!LcId = Me.txtLcid.Text
    rsLocations!LName = Me.txtLName.Text
    rsLocations!LType = Me.dcLType.Value
    rsLocations!LStatus = Me.dcLStatus.Value
    rsLocations!LDivision = Me.txtLDivision.Text
    rsLocations!LRegion = Me.txtLRegion.Text
    rsLocations!LUserDefined = Me.txtLUserDefined.Text
    rsLocations!LRank = IIf(IsNull(Me.txtLRank.Value), 0, Me.txtLRank.Value)
    rsLocations!DropShip_XDock = IIf(Me.Ck_Drop_Ship.Value = VALUETRUE, "Y", "N")
    rsLocations!UpdateCurrentYearOption = IIf(Me.Ck_Upd.Value = VALUETRUE, "Y", "N")
    
    rsLocations!PutAwayDays = Me.txtPutAwayDays.Value
    rsLocations!LookBackPds = Me.txtLookBackPds.Value
    rsLocations!ReplenCost = Me.txtReplenCost.Value
    rsLocations!DemandSource = Me.dcDemandSource.Value
    rsLocations!LeadTimeSource = Me.dcLeadTimeSource.Value
    rsLocations!Dft_ById = Me.dcAIMUsers.Value
    rsLocations!Dft_ReviewerID = IIf(IsNull(Me.dcAIMReviewers.Value), "", Me.dcAIMReviewers.Value)
    rsLocations!Last_FcstUpdCyc = Me.txtLast_FcstUpdCyc.Value
    rsLocations!StkDate = IIf(DateDiff("d", Format(rsLocations!StkDate, g_ISO_DATE_FORMAT), Format(Me.txtStkDate.Text, g_ISO_DATE_FORMAT)) <> 0, Me.txtStkDate.Text, rsLocations!StkDate)
    rsLocations!DmdScalingFactor = Me.txtDmdScalingFactor.Value
    rsLocations!ScalingEffUntil = IIf(DateDiff("d", Format(rsLocations!ScalingEffUntil, g_ISO_DATE_FORMAT), Format(Me.txtScalingEffUntil.Text, g_ISO_DATE_FORMAT)) <> 0, Me.txtScalingEffUntil.Text, rsLocations!ScalingEffUntil)
    rsLocations!Freeze_Period = IIf(IsNull(Me.txtFreezePds.Value), 0, Me.txtFreezePds.Value)
    rsLocations!ExceptionPct = IIf(IsNull(Me.txtExceptionPct.Value), 0, Me.txtExceptionPct.Value)
    
    rsLocations!LAddress1 = Me.txtLAddress1.Text
    rsLocations!LAddress2 = Me.txtLAddress2.Text
    rsLocations!LAddress3 = IIf(IsNull(Me.txtLAddress3.Text), "", Me.txtLAddress3.Text)
    rsLocations!LAddress4 = IIf(IsNull(Me.txtLAddress4.Text), "", Me.txtLAddress4.Text)
    rsLocations!LCity = Me.txtLCity.Text
    rsLocations!LState = Me.txtLState.Text
    rsLocations!LZip = Me.txtLZip.Text
    rsLocations!LCountry = IIf(IsNull(Me.txtLCountry.Text), "", Me.txtLCountry.Text)
    rsLocations!LContact = Me.txtLContact.Text
    rsLocations!LPhone = Me.txtLPhone.Text
    rsLocations!LFax = Me.txtLFax.Text
    rsLocations!LEMail = Me.txtLEMail.Text
    
Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_Locations_Update)"
    f_HandleErr , , , "AIM_LocationsMaintenance::f_Locations_Update", Now, gDRGeneralError, True, Err
End Function

Private Function UserInputValidation()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX07312")
    If StrComp(strMessage, "MSGBOX07312") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim(txtLcid.Text) = "" _
    Or StrComp(txtLcid.Text, getTranslationResource("New Location")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    Label1.Caption  'Location ID
        txtLcid.SetFocus
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    'Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
    f_HandleErr , , , "AIM_LocationsMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function

Private Function Insert_AimTransferLocation(FromLcID As String, ToLcID As String, EnableTransfer As String)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    SqlStmt = "INSERT INTO AimTransferPolicy VALUES" & vbCrLf & _
            "('" & FromLcID & "'" & _
            ",'" & ToLcID & "'" & _
            ",'T'" & _
            ",'" & CStr(EnableTransfer) & "')"
    rsAimTransferInsert.Open SqlStmt, Cn
    
    SqlStmt = "SELECT * FROM AimTransferPolicy" & vbCrLf & _
            "WHERE LcID = N'" & txtLcid.Text & "'" & vbCrLf & _
            "ORDER BY LcIDTransferTo"
    If f_IsRecordsetValidAndOpen(rsAimTransferTo) Then rsAimTransferTo.Close
    rsAimTransferTo.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dcTransferToLc.ReBind

Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    rsAimTransferInsert.CancelUpdate
    'Err.Raise Err.Number, Err.source & "(Insert_AimTransferLocation)", Err.Description
    f_HandleErr , , , "AIM_LocationsMaintenance::Insert_AimTransferLocation", Now, gDRGeneralError, True, Err
End Function
Private Function Update_AimTransferLocation(FromLcID As String, OldToLcID As String, NewToLcID As String, EnableTransfer As String)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    SqlStmt = "UPDATE AimTransferPolicy SET" & vbCrLf & _
            "LcIDTransferTo = N'" & NewToLcID & "'" & vbCrLf & _
            ", EnableTransferPolicy = N'" & EnableTransfer & "'" & vbCrLf & _
            "WHERE LcIDTransferTo = N'" & OldToLcID & "'" & vbCrLf & _
            "AND LcID = N'" & FromLcID & "'"
    rsAimTransferInsert.Open SqlStmt, Cn
    
    SqlStmt = "SELECT * FROM AimTransferPolicy" & vbCrLf & _
            "WHERE LcID = N'" & txtLcid.Text & "'" & vbCrLf & _
            "ORDER BY LcIDTransferTo"
    If f_IsRecordsetValidAndOpen(rsAimTransferTo) Then rsAimTransferTo.Close
    rsAimTransferTo.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dcTransferToLc.ReBind

Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    rsAimTransferInsert.CancelUpdate
    'Err.Raise Err.Number, Err.source & "(Update_AimTransferLocation)", Err.Description
    f_HandleErr , , , "AIM_LocationsMaintenance::Update_AimTransferLocation", Now, gDRGeneralError, True, Err
End Function

Private Function Refresh_AimTransfer()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    SqlStmt = "SELECT * FROM AimTransferPolicy" & vbCrLf & _
            "WHERE LcID = N'" & txtLcid.Text & "'" & vbCrLf & _
            "ORDER BY LcIDTransferTo"
    If f_IsRecordsetValidAndOpen(rsAimTransferTo) Then rsAimTransferTo.Close
    rsAimTransferTo.Open SqlStmt, Cn

    Me.dcTransferToLc.ReBind

Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    rsAimTransferInsert.CancelUpdate
    'Err.Raise Err.Number, Err.source & "(Refresh_AimTransfer)", Err.Description
    f_HandleErr , , , "AIM_LocationsMaintenance::Refresh_AimTransfer", Now, gDRGeneralError, True, Err
End Function

Private Function Save_AimTransfer()
On Error GoTo ErrorHandler

    Dim TransferEnabled As String
    
    TransferEnabled = dcTransferToLc.Columns(3).Value
    'If the TransferEnabled field is -1 then it is true  1 is true and 0 is false
    If TransferEnabled = "-1" Then TransferEnabled = "1"
    
    If dcTransferToLc.DataChanged Then
        dcTransferToLc.Update
        Me.dcTransferToLc.DataChanged = False
    End If

Exit Function
ErrorHandler:
    Me.MousePointer = vbDefault
    'rsAimTransferInsert.CancelUpdate
    'Err.Raise Err.Number, Err.source & "(Save_AimTransfer)", Err.Description
    f_HandleErr , , , "AIM_LocationsMaintenance::Save_AimTransfer", Now, gDRGeneralError, True, Err
End Function

Private Sub Command1_Click()
On Error GoTo ErrorHandler

    Me.dcTransferToLc.DeleteSelected

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::Command1_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub Command2_Click()
On Error GoTo ErrorHandler

    Me.dcTransferToLc.ReBind

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::Command2_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcAIMReviewers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMReviewers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMReviewers.Columns(0).Width = 1000
        
    Me.dcAIMReviewers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMReviewers.Columns(1).Width = 2880
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMReviewers, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcAIMReviewers_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
        
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcDemandSource_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long

    Me.dcDemandSource.Columns(0).Caption = getTranslationResource("Code")
    Me.dcDemandSource.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcDemandSource.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDemandSource.Columns(0).Width = 720
    
    Me.dcDemandSource.Columns(1).Caption = getTranslationResource("Description")
    Me.dcDemandSource.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcDemandSource.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDemandSource.Columns(1).Width = 3000
    
    Me.dcDemandSource.AddItem "O" & vbTab & _
                 getTranslationResource("Ordered Quantity")
    Me.dcDemandSource.AddItem "S" & vbTab & _
                 getTranslationResource("Shipped Quantity")
    Me.dcDemandSource.AddItem "B" & vbTab & _
                 getTranslationResource("Shipped With Substitute Quantity")
                 
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDemandSource, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcDemandSource.Columns.Count - 1
'        dcDemandSource.Columns(IndexCounter).HasHeadBackColor = True
'        dcDemandSource.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcDemandSource_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLeadTimeSource_InitColumnProps()
On Error GoTo ErrorHandler

    Dim tempInt As Integer
    Dim IndexCounter As Long
    
    Me.dcLeadTimeSource.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLeadTimeSource.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcLeadTimeSource.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLeadTimeSource.Columns(0).Width = 720
    
    Me.dcLeadTimeSource.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLeadTimeSource.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLeadTimeSource.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLeadTimeSource.Columns(1).Width = 2000
    Me.dcLeadTimeSource.Columns(1).Width = 3200

    
    Me.dcLeadTimeSource.AddItem "L" & vbTab & _
                         getTranslationResource("Lead Time Transaction")
    Me.dcLeadTimeSource.AddItem "S" & vbTab & _
                         getTranslationResource("Stock Status Transaction")
    Me.dcLeadTimeSource.AddItem "V" & vbTab & _
                         getTranslationResource("Vendor Table Default Lead Time")

'    Since these are long strings
'    Me.dcLeadTimeSource.ROWHEIGHT = Me.dcLeadTimeSource.ROWHEIGHT * 2
'    Me.dcLeadTimeSource.MultiLine = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLeadTimeSource, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcLeadTimeSource.Columns.Count - 1
'        dcLeadTimeSource.Columns(IndexCounter).HasHeadBackColor = True
'        dcLeadTimeSource.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcLeadTimeSource_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLStatus_Change()
On Error GoTo ErrorHandler
    
    Me.dcLStatus.ToolTipText = Me.dcLStatus.Value
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcLStatus_Change", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLStatus_Click()
On Error GoTo ErrorHandler
    
    Me.dcLStatus.ToolTipText = Me.dcLStatus.Value
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcLStatus_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLStatus_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcLStatus.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLStatus.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcLStatus.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLStatus.Columns(0).Width = 720
    
    Me.dcLStatus.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLStatus.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLStatus.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLStatus.Columns(1).Width = 2000
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLStatus, ACW_EXPAND
    End If
    
'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To Dclstatus.Columns.Count - 1
'        Dclstatus.Columns(IndexCounter).HasHeadBackColor = True
'        Dclstatus.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::Dclstatus_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLType_Change()
On Error GoTo ErrorHandler
    
    Me.dcLType.ToolTipText = Me.dcLType.Text
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcLType_Change", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub


Private Sub dcLType_Click()
On Error GoTo ErrorHandler
    
    Me.dcLStatus.ToolTipText = Me.dcLStatus.Text
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcLType_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLType_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcLType.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLType.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcLType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLType.Columns(0).Width = 720
    
    Me.dcLType.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLType.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLType.Columns(1).Width = 2000
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLType, ACW_EXPAND
    End If
    
'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To dcLtype.Columns.Count - 1
'        dcLtype.Columns(IndexCounter).HasHeadBackColor = True
'        dcLtype.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcLtype_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::Form_Activate", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Display status message
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03200")
    If StrComp(strMessage, "STATMSG03200") = 0 Then strMessage = "Initializing Locations Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Populate the dropdowns
    GetListOfBuyers
    GetListOfReviewers
    GetGeneralCodeLookups
        
    'Initialize the Locations Record Set
    SqlStmt = "SELECT * FROM AIMLocations ORDER BY LcID "
    
    Set rsLocations = New ADODB.Recordset
    rsLocations.CursorLocation = adUseClient
    rsLocations.Open SqlStmt, Cn, adOpenDynamic, adLockOptimistic
    
    If f_IsRecordsetOpenAndPopulated(rsLocations) Then
        Me.tbNavigation.ToolBars("tbNavigation").Tools("Id_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    Else
        f_Locations_InitializeNew
    End If

    'Bind form to Locations Record Set
    Refresh_Form
    
    
    Set rsAimTransferInsert = New ADODB.Recordset
    With rsAimTransferInsert
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    Set rsAimTransferTo = New ADODB.Recordset
    With rsAimTransferTo
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    Set rsLcidCount = New ADODB.Recordset
    With rsLcidCount
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    cmdMultiLocUpdate.ActiveConnection = Cn
    cmdMultiLocUpdate.CommandType = adCmdText
    
    'Build the SQL Statement to populate transfer to locations tab
    SqlStmt = "SELECT * FROM AimTransferPolicy" & vbCrLf & _
            "WHERE LcID = N'" & txtLcid.Text & "'" & vbCrLf & _
            "ORDER BY LcIDTransferTo"
    'Open the Record Set
    rsAimTransferTo.Open SqlStmt, Cn
    
    'Open the Record Set
    Set rsAimLocAndDesc = New ADODB.Recordset
    With rsAimLocAndDesc
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    'Populate the dropdownlist of aimlocations
    SqlStmt = "select LcID,LName from aimlocations order by LcID"
    rsAimLocAndDesc.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dcTransferToLc.ReBind
    Set Me.dcTransferToLcDropDown.DataSource = rsAimLocAndDesc
    Me.dcTransferToLcDropDown.Reset
    
    'Make the spin button visible
    Me.txtDmdScalingFactor.Spin.Visible = 1
    Me.txtReplenCost.Spin.Visible = 1
    Me.txtPutAwayDays.Spin.Visible = 1
    Me.txtLookBackPds.Spin.Visible = 1
    Me.txtStkDate.DropDown.Visible = 1
    Me.txtScalingEffUntil.DropDown.Visible = 1
    Me.txtFreezePds.Spin.Visible = 1
    Me.txtLRank.Spin.Visible = 1
    Me.txtExceptionPct.Spin.Visible = 1
    EnableDesibleCheckBoxes False
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::Form_Load", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
        
    If f_IsRecordsetValidAndOpen(rsBuyers) Then rsBuyers.Close
    Set rsBuyers = Nothing
    
    If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
    Set rsLocations = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAimTransferTo) Then rsAimTransferTo.Close
    Set rsAimTransferTo = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAimTransferInsert) Then rsAimTransferInsert.Close
    Set rsAimTransferInsert = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAimLocAndDesc) Then rsAimLocAndDesc.Close
    Set rsAimLocAndDesc = Nothing
    
    If f_IsRecordsetValidAndOpen(rsLcidCount) Then rsLcidCount.Close
    Set rsLcidCount = Nothing
    
    Set cmdMultiLocUpdate.ActiveConnection = Nothing
    Set cmdMultiLocUpdate = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Screen.MousePointer = vbDefault
Exit Sub
ErrorHandler:
    Me.MousePointer = vbDefault
    If Err.Number = 3219 Then
        'Ignore
    Else
        f_HandleErr , , , "AIM_LocationsMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub dcTransferToLcDropDown_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcTransferToLcDropDown.DataFieldList = "LcID"
   Me.dcTransferToLcDropDown.DataFieldToDisplay = "LcID"
    
    Me.dcTransferToLcDropDown.Columns(0).Name = "LcID"
    Me.dcTransferToLcDropDown.Columns(0).Caption = getTranslationResource("Loc ID")
    Me.dcTransferToLcDropDown.Columns(0).Width = 1000
    Me.dcTransferToLcDropDown.Columns(0).CaptionAlignment = ssColCapAlignCenter
    Me.dcTransferToLcDropDown.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcTransferToLcDropDown.Columns(0).DataField = "LcID"
    
    Me.dcTransferToLcDropDown.Columns(1).Name = "LName"
    Me.dcTransferToLcDropDown.Columns(1).Caption = getTranslationResource("Description")
    Me.dcTransferToLcDropDown.Columns(1).Width = 2880
    Me.dcTransferToLcDropDown.Columns(1).CaptionAlignment = ssColCapAlignCenter
    Me.dcTransferToLcDropDown.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcTransferToLcDropDown.Columns(1).DataField = "LName"
    
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcTransferToLcDropDown, ACW_EXPAND
    End If
'
'    Me.dcTransferToLcDropDown.RowNavigation = ssRowNavigationUDLock
'
    For IndexCounter = 0 To dcTransferToLcDropDown.Columns.Count - 1

        If dcTransferToLcDropDown.Columns(IndexCounter).Locked = False Then dcTransferToLcDropDown.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcTransferToLcDropDown_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcTransferToLc_AfterColUpdate(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler
'
    'ColIndex =1 is LcID which is read from the dropdown
    If ColIndex = 1 Then
        dcTransferToLc.Columns(0).Value = dcTransferToLcDropDown.Columns(0).Value
    End If
'
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcTransferToLc_AfterColUpdate", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcTransferToLc_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Define Columns
    Me.dcTransferToLc.Columns(0).Name = "LcID"
    Me.dcTransferToLc.Columns(0).Caption = getTranslationResource("Location Name")
    Me.dcTransferToLc.Columns(0).Width = 2880
    Me.dcTransferToLc.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcTransferToLc.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcTransferToLc.Columns(0).Style = ssStyleEdit
    Me.dcTransferToLc.Columns(0).FieldLen = 12
    Me.dcTransferToLc.Columns(0).DataType = vbString
    Me.dcTransferToLc.Columns(0).Visible = False
    
    Me.dcTransferToLc.Columns(1).Name = "LcIDTransferTo"
    Me.dcTransferToLc.Columns(1).Caption = getTranslationResource("Transfer To Location")
    Me.dcTransferToLc.Columns(1).Width = 2880
    Me.dcTransferToLc.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcTransferToLc.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcTransferToLc.Columns(1).Style = ssStyleEdit
    Me.dcTransferToLc.Columns(1).Locked = True
    Me.dcTransferToLc.Columns(1).DropDownHwnd = Me.dcTransferToLcDropDown.hwnd

        
    Me.dcTransferToLc.Columns(2).Name = "TransferType"
    Me.dcTransferToLc.Columns(2).Caption = getTranslationResource("Transfer Type")
    Me.dcTransferToLc.Columns(2).Width = 2880
    Me.dcTransferToLc.Columns(2).Width = 0
    Me.dcTransferToLc.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcTransferToLc.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dcTransferToLc.Columns(2).Style = ssStyleEdit
    Me.dcTransferToLc.Columns(2).FieldLen = 30
    Me.dcTransferToLc.Columns(2).DataType = vbString
    Me.dcTransferToLc.Columns(2).Visible = True
    Me.dcTransferToLc.Columns(2).Locked = True
    
    Me.dcTransferToLc.Columns(3).Name = "EnabledTransferPolciy"
    Me.dcTransferToLc.Columns(3).Caption = "Enabled"
    Me.dcTransferToLc.Columns(3).Width = 2880
    Me.dcTransferToLc.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcTransferToLc.Columns(3).Alignment = ssCaptionAlignmentCenter
    Me.dcTransferToLc.Columns(3).Style = ssStyleCheckBox
    Me.dcTransferToLc.Columns(3).Locked = False
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcTransferToLc, ACW_EXPAND
    End If

    Me.dcTransferToLc.RowNavigation = ssRowNavigationUDLock
    
    For IndexCounter = 0 To dcTransferToLc.Columns.Count - 1
        If dcTransferToLc.Columns(IndexCounter).Locked = False Then _
            dcTransferToLc.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcTransferToLc_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcTransferToLc_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim LcId As String
    Dim EnableTransfer As String
    
    Dim SqlStmt As String
    Dim strMessage As String
    Dim RtnCode As Long
        
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    If Not IsNull(RowBuf.Value(0, 0)) Then
        'The value of RowBuf will be populated when we change the row
        LcId = RowBuf.Value(0, 0)
    Else
        'This is run when the user changes the value and cliks out side the control
        'then we call the update method to trigget this event
        LcId = dcTransferToLc.Columns(1).Value
    End If

    'If the field is checked then it is true and the value stored is "1" unchecked it is false and value is "0"
    If RowBuf.Value(0, 3) = True Then
        EnableTransfer = "1"
    Else
        EnableTransfer = "0"
    End If

    'Populate the table with the data
    Insert_AimTransferLocation txtLcid.Text, LcId, EnableTransfer

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dgItStatus_UnboundAddData", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcTransferToLc_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler
    
    If Not f_IsRecordsetOpenAndPopulated(rsAimTransferTo) Then Exit Sub
    
    rsAimTransferTo.Bookmark = Bookmark
    rsAimTransferTo.Delete

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcTransferToLc_UnboundDeleteRow", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub


Private Sub dcTransferToLc_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsAimTransferTo) Then Exit Sub
    If dcTransferToLc.DataChanged Then Exit Sub
    If dcTransferToLc.IsAddRow Then Exit Sub

    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsAimTransferTo.MoveLast
        Else
            rsAimTransferTo.MoveFirst
        End If

    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsAimTransferTo.Bookmark = StartLocation

    End If

    'Note: Do not use StartLocation because it could be null
    rsAimTransferTo.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsAimTransferTo.Bookmark

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dcTransferToLc_UnboundPositionData", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcTransferToLc_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsAimTransferTo) Then Exit Sub
    If dcTransferToLc.DataChanged Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAimTransferTo.MoveLast
        Else
            rsAimTransferTo.MoveFirst
        End If
    Else
        rsAimTransferTo.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAimTransferTo.MovePrevious
        Else
            rsAimTransferTo.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsAimTransferTo.BOF Or rsAimTransferTo.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsAimTransferTo("LcID").Value
        RowBuf.Value(i, 1) = rsAimTransferTo("LcIDTransferTo").Value
        RowBuf.Value(i, 2) = rsAimTransferTo("TransferType").Value
        RowBuf.Value(i, 3) = rsAimTransferTo("EnableTransferPolicy").Value
    
        RowBuf.Bookmark(i) = rsAimTransferTo.Bookmark
    
        If ReadPriorRows Then
            rsAimTransferTo.MovePrevious
        Else
            rsAimTransferTo.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
   f_HandleErr , , , "AIM_LocationsMaintenance::dcTransferToLc_UnboundReadData", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcTransferToLc_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
        
    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    If Not f_IsRecordsetOpenAndPopulated(rsAimTransferTo) Then Exit Sub

    'If Not UserInputValidation(strMessage) Then
        rsAimTransferTo.Bookmark = WriteLocation

        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsAimTransferTo("LcIDTransferTo").Value = RowBuf.Value(0, 0)
        Else
            rsAimTransferTo("LcIDTransferTo").Value = dcTransferToLc.Columns(1).Value
        End If
        If Not IsNull(RowBuf.Value(0, 3)) Then
           
            If RowBuf.Value(0, 3) = "-1" Then
                rsAimTransferTo("EnableTransferPolicy").Value = "1"
            Else
                rsAimTransferTo("EnableTransferPolicy").Value = "0"
            End If
        End If
        'Update the Item Status Table
        rsAimTransferTo.Update

        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG02201")
            If StrComp(strMessage, "ERRMSG02201") = 0 Then strMessage = "Error editing Item Status Record."
            ADOErrorHandler Cn, strMessage
            rsAimTransferTo.CancelUpdate
        Else
            strMessage = getTranslationResource("STATMSG02201")
            If StrComp(strMessage, "STATMSG02201") = 0 Then strMessage = "Item Status successfully modified."
            Write_Message strMessage
        End If
'    Else
'        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
'        rsAimTransferTo.CancelUpdate
'    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::dgItStatus_UnboundWriteData", Now, gDRGeneralError, True, Err
    rsAimTransferTo.CancelUpdate
    Me.MousePointer = vbDefault
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim LcId As String
    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strText As String
    Dim SqlStmt As String
    Dim WhereClause As String
    
    Cn.Errors.Clear
    
    Write_Message ""
    If RecordCount <= 1 Then
        RtnCode = f_Locations_Update
    End If
    
    
    strText = getTranslationResource(Me.Caption)
    If Tool.ID <> "ID_Save" Then
        'Reset the mass update variables
        RecordCount = 0
        WhereClause = ""
        EnableDesibleCheckBoxes False
    End If
    'Alert user to possible change
    Select Case Tool.ID
        Case "ID_AddNew", "ID_Close", "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
            "ID_GoToBookMark", "ID_LookUp"
            
            If gAccessLvl <> 1 And DataChanged(rsLocations, adAffectCurrent) > 0 Then
                strMessage = getTranslationResource("MSGBOX03200")
                If StrComp(strMessage, "MSGBOX03200") = 0 Then strMessage = "Abandon changes to current record?"
                RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
                                
                If RtnCode = vbYes Then
                    rsLocations.CancelUpdate
                Else
                    f_Locations_Save
                End If
            Else
                rsLocations.CancelUpdate
            End If
    
    End Select
    
    'Navigate
    Select Case Tool.ID
        Case "ID_AddNew"
            If tabLocation.SelectedTab.Index = 3 Then
                'Do nothing the control is alredy in add mode
            Else
                f_Locations_InitializeNew
            End If
        Case "ID_Delete"
        
            If tabLocation.SelectedTab.Index = 3 Then
                Me.dcTransferToLc.DeleteSelected
            Else
                If rsLocations.EditMode <> adEditAdd Then
                    'Display a confirmation msgbox
                    strMessage = getTranslationResource("MSGBOX03201")
                    If StrComp(strMessage, "MSGBOX03201") = 0 Then strMessage = "Delete the current Location?"
                    
                    RtnCode = MsgBox(strMessage, vbYesNo, strText)
                
                    Select Case RtnCode
                    Case vbYes
                        'User chose to delete.
                        rsLocations.Delete
                        rsLocations.MovePrevious
                        If rsLocations.BOF Then
                            rsLocations.MoveFirst
                        End If
            
                    Case vbNo
                        'User chose to Cancel
                    End Select
                End If
            End If
        Case "ID_GetFirst"
            rsLocations.MoveFirst
        
        Case "ID_GetPrev"
            rsLocations.MovePrevious
            If rsLocations.BOF Then
                rsLocations.MoveFirst
            End If
            
        Case "ID_GetNext"
            rsLocations.MoveNext
            If rsLocations.eof Then
                rsLocations.MoveLast
            End If
            
        Case "ID_GetLast"
            rsLocations.MoveLast
        
        Case "ID_Save"
'             If atForecastCriteria.Tabs(1).Selected Then
'        'Forecast Header/Criteria
'        ToolbarOptions_ForecastCriteria Tool
            f_Locations_Save
    
        Case "ID_SetBookMark"
            LocationsBookMark = rsLocations.Bookmark
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
            
        Case "ID_GoToBookMark"
            rsLocations.Bookmark = LocationsBookMark
        
        Case "ID_LookUp"
            'Save the current bookmark
            LocationsBookMark = rsLocations.Bookmark

'            'Initialize the browser connection
'            Set AIM_LocationLookUp.Cn = Cn
'            AIM_LocationLookUp.LcId = Me.txtLcid.Text
'            AIM_LocationLookUp.LType = Me.dcLType.Value
'
'            AIM_LocationLookUp.Show vbModal
'
'            If AIM_LocationLookUp.CancelFlag Then
'                rsLocations.bookmark = LocationsBookMark
'                Exit Sub
'            Else
'                LcId = AIM_LocationLookUp.LcId
'                rsLocations.MoveFirst
'                rsLocations.Find "LcID = '" & LcId & "' "
'                If rsLocations.BOF = True Or rsLocations.eof = True Then
'                 rsLocations.bookmark = LocationsBookMark
'                End If
'            End If
'
'            'Clear the lookup form
'            Set AIM_LocationLookUp = Nothing
             g_InitCriteria m_xaAllCriteria
             g_InitCriteria m_xaNewCriteria
             g_InitCriteria m_xaOrigCriteria
             WhereClause = DisplayFilters("")
             If WhereClause = "" Then
                EnableDesibleCheckBoxes (False)
                rsLocations.Bookmark = LocationsBookMark
                Exit Sub
'                Item = First_Item
'                LcId = First_LcID
'                getItemRecord LcId, Item, SQL_GetEq
            Else
                'multiselect or single select
                RecordCount = GetLcidRecordCount(WhereClause)
                If RecordCount = 0 Then
                  EnableDesibleCheckBoxes (False)
                    rsLocations.Bookmark = LocationsBookMark
                    Exit Sub
'                    Item = First_Item
'                    LcId = First_LcID
'                    getItemRecord LcId, Item, SQL_GetEq
                ElseIf RecordCount = 1 Then
                    EnableDesibleCheckBoxes (False)
                    LcId = AIM_LocationLookUp.LcId
                    rsLocations.MoveFirst
                    rsLocations.Find "LcID = '" & LcId & "' "
                    If rsLocations.BOF = True Or rsLocations.eof = True Then
                        rsLocations.Bookmark = LocationsBookMark
                    End If
'                   GetLcidItemRecord WhereClause, Item, LcId
'                    getItemRecord LcId, Item, SQL_GetEq
                Else
                    
                    EnableDesibleCheckBoxes (True)
                    
                    'UpdateLcidItemRecords ("test")
                End If
                
                
            End If

        Case "ID_Close"
            Unload Me
            Exit Sub
    
    End Select
'If data in Transfer to locations tab is changed then save it
Save_AimTransfer
'Refresh the form with new location data if the location changes
Refresh_Form        'Refresh the form
'Refresh Transfer to locations tab with new location data
Refresh_AimTransfer
 If RecordCount > 1 Then
    'if itis multiselect then blank  out the fields
    Me.txtLcid.Text = ""
    Me.txtLName.Text = ""
    
 End If
Exit Sub
ErrorHandler:
'    rsAimTransferInsert.CancelUpdate
    f_HandleErr , , , "AIM_LocationsMaintenance::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub txtLState_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    If Len(txtLState.Text) = 2 Then Me.txtLState = UCase(Me.txtLState)
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsMaintenance::txtLState_Validate", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Function GetListOfReviewers() As Long
On Error GoTo ErrorHandler

    Dim AIM_AIMUser_List_Sp As ADODB.Command
    
    'Set default to failure
    GetListOfReviewers = -1
    
    'Build/Bind the AIM User Drop Down
    Set AIM_AIMUser_List_Sp = New ADODB.Command
    With AIM_AIMUser_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
        .CommandText = "AIM_AIMUser_List_Sp"
    End With

    'No Parameters
    
    If f_IsRecordsetValidAndOpen(rsReviewers) Then rsReviewers.Close
    Set rsReviewers = Nothing
    Set rsReviewers = New ADODB.Recordset
    
    With rsReviewers
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsReviewers.Open AIM_AIMUser_List_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsReviewers) Then
        'Bind the Data Combo Drop Downs
        Set dcAIMUsers.DataSourceList = rsReviewers
        GetListOfReviewers = 0 'Success
    End If
    
    If f_IsRecordsetOpenAndPopulated(rsReviewers) Then
        'Bind the Data Combo Drop Downs
        Set dcAIMReviewers.DataSourceList = rsReviewers
        GetListOfReviewers = 0 'Success
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetListOfReviewers)"
     f_HandleErr , , , "AIM_LocationsMaintenance::GetListOfReviewers", Now, gDRGeneralError, True, Err
End Function

Private Function GetListOfBuyers() As Long
On Error GoTo ErrorHandler

    Dim AIM_AIMUser_List_Sp As ADODB.Command
    
    'Set default to failure
    GetListOfBuyers = -1
    
    'Build/Bind the AIM User Drop Down
    Set AIM_AIMUser_List_Sp = New ADODB.Command
    With AIM_AIMUser_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
        .CommandText = "AIM_AIMUser_List_Sp"
    End With

    'No Parameters
    
    If f_IsRecordsetValidAndOpen(rsBuyers) Then rsBuyers.Close
    Set rsBuyers = Nothing
    Set rsBuyers = New ADODB.Recordset
    
    With rsBuyers
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsBuyers.Open AIM_AIMUser_List_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsBuyers) Then
        'Bind the Data Combo Drop Downs
        Set dcAIMUsers.DataSourceList = rsBuyers
        GetListOfBuyers = 0 'Success
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetListOfBuyers)"
    f_HandleErr , , , "AIM_LocationsMaintenance::GetListOfBuyers", Now, gDRGeneralError, True, Err
End Function



Private Function GetGeneralCodeLookups() As Long
On Error GoTo ErrorHandler

    Dim rsCodeLookup As ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    
    'Set default to failure
    GetGeneralCodeLookups = -1
    
    '************************************************************
    'Build/Bind the Location Type dropdown
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_LTYPE
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcLType.AddItem rsCodeLookup!CodeID & vbTab & rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    End If
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing

    '************************************************************
    'Build/Bind the Location Status dropdown
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_LSTATUS
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcLStatus.AddItem rsCodeLookup!CodeID & vbTab & rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    End If
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetListOfBuyers)"
    f_HandleErr , , , "AIM_LocationsMaintenance::GetListOfBuyers", Now, gDRGeneralError, True, Err
End Function

