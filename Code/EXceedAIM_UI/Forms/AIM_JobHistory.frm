VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_JobHistory 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Job History"
   ClientHeight    =   6930
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11580
   Icon            =   "AIM_JobHistory.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   6930
   ScaleWidth      =   11580
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Frame1 
      Height          =   750
      Left            =   36
      TabIndex        =   8
      Top             =   -36
      Width           =   11500
      Begin VB.CheckBox ckShowStepDetail 
         Caption         =   "Show step details"
         Height          =   340
         Left            =   7920
         TabIndex        =   1
         Top             =   272
         Width           =   3300
      End
      Begin TDBText6Ctl.TDBText txtJobName 
         Height          =   345
         Left            =   2205
         TabIndex        =   9
         TabStop         =   0   'False
         Top             =   270
         Width           =   5415
         _Version        =   65536
         _ExtentX        =   9551
         _ExtentY        =   609
         Caption         =   "AIM_JobHistory.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobHistory.frx":0376
         Key             =   "AIM_JobHistory.frx":0394
         BackColor       =   -2147483644
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   40
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label1 
         Caption         =   "Job Name"
         Height          =   300
         Left            =   200
         TabIndex        =   10
         Top             =   315
         Width           =   2000
      End
   End
   Begin VB.Frame Frame2 
      Height          =   2175
      Left            =   36
      TabIndex        =   5
      Top             =   4080
      Width           =   11500
      Begin TDBText6Ctl.TDBText txtMessages 
         Height          =   1455
         Left            =   120
         TabIndex        =   6
         TabStop         =   0   'False
         Top             =   615
         Width           =   11265
         _Version        =   65536
         _ExtentX        =   19870
         _ExtentY        =   2566
         Caption         =   "AIM_JobHistory.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobHistory.frx":0444
         Key             =   "AIM_JobHistory.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label LblDateTime 
         Caption         =   "Errors and/or messages from the job/step run at "
         Height          =   345
         Left            =   165
         TabIndex        =   7
         Top             =   240
         Width           =   9015
      End
   End
   Begin VB.CommandButton cmdRefresh 
      Caption         =   "&Refresh"
      Default         =   -1  'True
      Height          =   345
      Left            =   9241
      TabIndex        =   2
      Top             =   6480
      Width           =   2295
   End
   Begin VB.CommandButton cmdClearAll 
      Caption         =   "Clear &All"
      Height          =   345
      Left            =   6960
      TabIndex        =   3
      Top             =   6480
      Width           =   2055
   End
   Begin VB.CommandButton cmdClose 
      Caption         =   "&Close"
      Height          =   345
      Left            =   5040
      TabIndex        =   4
      Top             =   6480
      Width           =   1700
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgJobHistory 
      Height          =   3180
      Left            =   30
      TabIndex        =   0
      Top             =   830
      Width           =   11505
      _Version        =   196617
      DataMode        =   1
      RecordSelectors =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   20285
      _ExtentY        =   5609
      _StockProps     =   79
   End
End
Attribute VB_Name = "AIM_JobHistory"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public m_CurJobName As String
Public m_Cn As ADODB.Connection

Dim sp_help_jobhistory As ADODB.Command
Dim sp_purge_jobhistory As ADODB.Command

Dim rsJobHistory As ADODB.Recordset

Private Function GetJobHistory(p_CurJobName As String)
On Error GoTo ErrorHandler

    With sp_help_jobhistory
        .Parameters("@job_name").Value = p_CurJobName
        .Parameters("@mode").Value = "FULL"
    End With
    
    If f_IsRecordsetValidAndOpen(rsJobHistory) Then
        rsJobHistory.Requery
    Else
        rsJobHistory.Open sp_help_jobhistory
    End If
    
    'Rebind the grid
    Me.dgJobHistory.ReBind
    If f_IsRecordsetOpenAndPopulated(rsJobHistory) Then dgJobHistory.Rows = rsJobHistory.RecordCount

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetJobHistory)"
	 f_HandleErr , , , "AIM_JobHistory::GetJobHistory", Now, gDRGeneralError, True, Err
End Function

Private Sub ckShowStepDetail_Click()
On Error GoTo ErrorHandler

    'Detail or summary
    If Me.ckShowStepDetail = vbChecked Then
        rsJobHistory.Filter = ""
    Else
        rsJobHistory.Filter = "step_id = 0"
    End If
    
    If Me.ckShowStepDetail.Value = VALUEFALSE Then
        Me.dgJobHistory.Columns("step_id").Visible = False
        Me.dgJobHistory.Columns("step_name").Visible = False
    Else
        Me.dgJobHistory.Columns("step_id").Visible = True
        Me.dgJobHistory.Columns("step_name").Visible = True
    End If
    
    Me.dgJobHistory.ReBind
    If f_IsRecordsetOpenAndPopulated(rsJobHistory) Then dgJobHistory.Rows = rsJobHistory.RecordCount
    
    'Check for an empty recordset
    If Not f_IsRecordsetOpenAndPopulated(rsJobHistory) Then
        Exit Sub
    Else
        'Get a pointer to the current row
        rsJobHistory.bookmark = Me.dgJobHistory.GetBookmark(0)
        
        'Set the Messages
        Me.txtMessages = rsJobHistory("message").Value
        
        'Set the Date Time Display
        Me.LblDateTime = getTranslationResource("Errors and/or messages from the job/step run at") + _
            SQLDMO_BldDateTime(rsJobHistory("run_date").Value, rsJobHistory("run_time").Value)
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckShowStepDetail_Click)"
	 f_HandleErr , , , "AIM_JobHistory::ckShowStepDetail_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClearAll_Click()
On Error GoTo ErrorHandler

    With sp_purge_jobhistory
        .Parameters("@job_name").Value = m_CurJobName
        .Execute
    End With
    txtMessages.Text = ""
    GetJobHistory m_CurJobName
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClearAll_Click)"
	 f_HandleErr , , , "AIM_JobHistory::cmdClearAll_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClose_Click()
    
    Unload Me
    
End Sub

Private Sub cmdRefresh_Click()
On Error GoTo ErrorHandler

    GetJobHistory m_CurJobName
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdRefresh_Click)"
	 f_HandleErr , , , "AIM_JobHistory::cmdRefresh_Click", Now, gDRGeneralError, True, Err
	
End Sub

Private Sub dgJobHistory_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long

    Me.dgJobHistory.Columns(0).Name = "step_id"
    Me.dgJobHistory.Columns(0).Caption = getTranslationResource("ID")
    Me.dgJobHistory.Columns(0).Width = 300
    Me.dgJobHistory.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobHistory.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgJobHistory.Columns(0).Visible = False
    Me.dgJobHistory.Columns(0).Locked = True
    
    Me.dgJobHistory.Columns(1).Name = "step_name"
    Me.dgJobHistory.Columns(1).Caption = getTranslationResource("Step Name")
    Me.dgJobHistory.Columns(1).Width = 2000
    Me.dgJobHistory.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobHistory.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgJobHistory.Columns(1).Visible = False
    Me.dgJobHistory.Columns(1).Locked = True
    
    Me.dgJobHistory.Columns(2).Name = "run_at"
    Me.dgJobHistory.Columns(2).Caption = getTranslationResource("Run At")
    Me.dgJobHistory.Columns(2).Width = 2250
    Me.dgJobHistory.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobHistory.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgJobHistory.Columns(2).Locked = True
    
    Me.dgJobHistory.Columns(3).Name = "run_status"
    Me.dgJobHistory.Columns(3).Caption = getTranslationResource("Result")
    Me.dgJobHistory.Columns(3).Width = 1440
    Me.dgJobHistory.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobHistory.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgJobHistory.Columns(3).Locked = True
    
    Me.dgJobHistory.Columns(4).Name = "notifications"
    Me.dgJobHistory.Columns(4).Caption = getTranslationResource("Notifications")
    Me.dgJobHistory.Columns(4).Width = 1440
    Me.dgJobHistory.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobHistory.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgJobHistory.Columns(4).Locked = True
    
    Me.dgJobHistory.Columns(5).Name = "run_duration"
    Me.dgJobHistory.Columns(5).Caption = getTranslationResource("Run Duration")
    Me.dgJobHistory.Columns(5).Width = 1200
    Me.dgJobHistory.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobHistory.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgJobHistory.Columns(5).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgJobHistory, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgJobHistory.Columns.Count - 1
'        dgJobHistory.Columns(IndexCounter).HasHeadBackColor = True
'        dgJobHistory.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgJobHistory.Columns(IndexCounter).Locked = False Then dgJobHistory.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobHistory_InitColumnProps)"
	 f_HandleErr , , , "AIM_JobHistory::dgJobHistory_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobHistory_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler
    
    'Get a pointer to the current row
    If IsNull(dgJobHistory.bookmark) Then Exit Sub

    rsJobHistory.bookmark = Me.dgJobHistory.bookmark
    
    'Set the Messages
    Me.txtMessages = rsJobHistory("message").Value
    
    'Set the Date Time Display
    Me.LblDateTime = getTranslationResource("Errors and/or messages from the job/step run at") & _
        SQLDMO_BldDateTime(rsJobHistory("run_date").Value, rsJobHistory("run_time").Value)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobHistory_RowColChange)"
	 f_HandleErr , , , "AIM_JobHistory::dgJobHistory_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobHistory_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    Dim Notifications As String
    
    If Not f_IsRecordsetOpenAndPopulated(rsJobHistory) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsJobHistory.MoveLast
        Else
            rsJobHistory.MoveFirst
        End If
    
    Else
        rsJobHistory.bookmark = StartLocation
        If ReadPriorRows Then
            rsJobHistory.MovePrevious
        Else
            rsJobHistory.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsJobHistory.BOF Or rsJobHistory.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsJobHistory("step_id").Value
        RowBuf.Value(i, 1) = "  " + rsJobHistory("step_name").Value
        RowBuf.Value(i, 2) = SQLDMO_BldDateTime(rsJobHistory("run_date").Value, _
            rsJobHistory("run_time").Value)
        RowBuf.Value(i, 3) = getTranslationResource _
            (SQLDMO_GetOutComeDescription(rsJobHistory("run_status").Value))
        
        Notifications = IIf(IsNull(rsJobHistory("operator_emailed").Value), _
            "", Trim(rsJobHistory("operator_emailed").Value))
        
        Notifications = Notifications + "; " + IIf(IsNull(rsJobHistory("operator_netsent").Value), _
            "", Trim(rsJobHistory("operator_netsent").Value))
        
        Notifications = Notifications + "; " + IIf(IsNull(rsJobHistory("operator_paged").Value), _
            "", Trim(rsJobHistory("operator_paged").Value))
        
        If Len(Notifications) = 0 Or Notifications = "; ; " Then
            Notifications = getTranslationResource("None")
        End If
        
        RowBuf.Value(i, 4) = Notifications
        RowBuf.Value(i, 5) = SQLDMO_Long2Time(rsJobHistory("run_duration").Value)

        RowBuf.bookmark(i) = rsJobHistory.bookmark
    
        If ReadPriorRows Then
            rsJobHistory.MovePrevious
        Else
            rsJobHistory.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobHistory_UnboundReadData)"
	 f_HandleErr , , , "AIM_JobHistory::dgJobHistory_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Me.txtJobName = m_CurJobName
    
    'Initialize Stored Procedures
    Set sp_help_jobhistory = New ADODB.Command
    With sp_help_jobhistory
        Set .ActiveConnection = m_Cn
        .CommandText = "sp_help_jobhistory"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With

    Set sp_purge_jobhistory = New ADODB.Command
    With sp_purge_jobhistory
        Set .ActiveConnection = m_Cn
        .CommandText = "sp_purge_jobhistory"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    
    'Initialize Record Sets
    Set rsJobHistory = New ADODB.Recordset
    With rsJobHistory
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    GetJobHistory m_CurJobName
    
    'Initially only show the job summary
    rsJobHistory.Filter = "step_id = 0"
    If f_IsRecordsetOpenAndPopulated(rsJobHistory) Then dgJobHistory.Rows = rsJobHistory.RecordCount

    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption
	 f_HandleErr , , , "AIM_JobHistory::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    If f_IsRecordsetValidAndOpen(rsJobHistory) Then rsJobHistory.Close
    Set rsJobHistory = Nothing

    If Not (sp_purge_jobhistory Is Nothing) Then Set sp_purge_jobhistory.ActiveConnection = Nothing
    Set sp_purge_jobhistory = Nothing

    If Not (sp_help_jobhistory Is Nothing) Then Set sp_help_jobhistory.ActiveConnection = Nothing
    Set sp_help_jobhistory = Nothing

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
		 f_HandleErr , , , "AIM_JobHistory::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
