VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_ItemLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Item Lookup"
   ClientHeight    =   5940
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8475
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_ItemLookup.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5940
   ScaleWidth      =   8475
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgItem 
      Height          =   2715
      Left            =   75
      TabIndex        =   13
      Top             =   2625
      Width           =   8325
      _Version        =   196617
      DataMode        =   1
      Cols            =   6
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   14684
      _ExtentY        =   4789
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   1560
      Style           =   1  'Graphical
      TabIndex        =   11
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   75
      Style           =   1  'Graphical
      TabIndex        =   12
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return Selected Item"
      Height          =   345
      Left            =   4440
      Style           =   1  'Graphical
      TabIndex        =   9
      Top             =   5400
      Width           =   2445
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   2370
      Left            =   78
      TabIndex        =   14
      Top             =   120
      Width           =   8325
      Begin TDBText6Ctl.TDBText txtLcid 
         Height          =   345
         Left            =   105
         TabIndex        =   0
         Top             =   648
         Width           =   1605
         _Version        =   65536
         _ExtentX        =   2831
         _ExtentY        =   609
         Caption         =   "AIM_ItemLookup.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":0376
         Key             =   "AIM_ItemLookup.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtLcid"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtItem 
         Height          =   345
         Left            =   1740
         TabIndex        =   1
         Top             =   648
         Width           =   1605
         _Version        =   65536
         _ExtentX        =   2831
         _ExtentY        =   609
         Caption         =   "AIM_ItemLookup.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":0444
         Key             =   "AIM_ItemLookup.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtItem"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtById 
         Height          =   345
         Left            =   120
         TabIndex        =   3
         Top             =   1494
         Width           =   1605
         _Version        =   65536
         _ExtentX        =   2831
         _ExtentY        =   609
         Caption         =   "AIM_ItemLookup.frx":04A6
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":0512
         Key             =   "AIM_ItemLookup.frx":0530
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtById"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   345
         Left            =   1755
         TabIndex        =   4
         Top             =   1494
         Width           =   1605
         _Version        =   65536
         _ExtentX        =   2831
         _ExtentY        =   609
         Caption         =   "AIM_ItemLookup.frx":0574
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":05E0
         Key             =   "AIM_ItemLookup.frx":05FE
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtVnId"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   345
         Left            =   3390
         TabIndex        =   5
         Top             =   1494
         Width           =   1605
         _Version        =   65536
         _ExtentX        =   2831
         _ExtentY        =   609
         Caption         =   "AIM_ItemLookup.frx":0642
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":06AE
         Key             =   "AIM_ItemLookup.frx":06CC
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtAssort"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
         Height          =   345
         Left            =   120
         TabIndex        =   6
         Top             =   1920
         Width           =   3135
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   609
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7365
         TabIndex        =   7
         Top             =   1920
         Width           =   870
         _Version        =   65536
         _ExtentX        =   1535
         _ExtentY        =   609
         Calculator      =   "AIM_ItemLookup.frx":0710
         Caption         =   "AIM_ItemLookup.frx":0730
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":079C
         Keys            =   "AIM_ItemLookup.frx":07BA
         Spin            =   "AIM_ItemLookup.frx":0804
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;;200"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   200
         MaxValueVT      =   5
         MinValueVT      =   1479671813
      End
      Begin TDBText6Ctl.TDBText txtItDesc 
         Height          =   345
         Left            =   3390
         TabIndex        =   2
         Top             =   648
         Width           =   4725
         _Version        =   65536
         _ExtentX        =   8334
         _ExtentY        =   609
         Caption         =   "AIM_ItemLookup.frx":082C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemLookup.frx":0898
         Key             =   "AIM_ItemLookup.frx":08B6
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtItDesc"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label6 
         Caption         =   "Item Description"
         Height          =   345
         Left            =   3390
         TabIndex        =   21
         Top             =   225
         Width           =   4725
      End
      Begin VB.Label Label8 
         Caption         =   "Maximum Rows"
         Height          =   340
         Left            =   5280
         TabIndex        =   20
         Top             =   1925
         Width           =   1935
      End
      Begin VB.Label Label5 
         Caption         =   "Vendor ID"
         Height          =   340
         Left            =   1755
         TabIndex        =   19
         Top             =   1071
         Width           =   1605
      End
      Begin VB.Label Label4 
         Caption         =   "Assortment"
         Height          =   340
         Left            =   3390
         TabIndex        =   18
         Top             =   1071
         Width           =   1605
      End
      Begin VB.Label Label3 
         Caption         =   "Buyer ID"
         Height          =   340
         Left            =   135
         TabIndex        =   17
         Top             =   1071
         Width           =   1605
      End
      Begin VB.Label Label2 
         Caption         =   "Item"
         Height          =   345
         Left            =   1740
         TabIndex        =   16
         Top             =   225
         Width           =   1605
      End
      Begin VB.Label Label1 
         Caption         =   "Location ID"
         Height          =   345
         Left            =   105
         TabIndex        =   15
         Top             =   225
         Width           =   1605
      End
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3000
      Style           =   1  'Graphical
      TabIndex        =   10
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   7032
      Style           =   1  'Graphical
      TabIndex        =   8
      Top             =   5400
      Width           =   1365
   End
End
Attribute VB_Name = "AIM_ItemLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection

Public AssortKey As String
Public ByIdKey As String
Public CancelFlag As Boolean
Public ItemKey As String
Public ItDesc As String
Public LcIdKey As String
Public VnIdKey As String
Public Comparison As String

Dim rsItem As ADODB.Recordset

Private AssortKey_Criteria As String
Private ByIdKey_Criteria As String
Private ItemKey_Criteria As String
Private ItDescKey_Criteria As String
Private LcIDKey_Criteria As String
Private VnIdKey_Criteria As String

Private Function BuildItemLookupQuery() As String
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim WhrStmt As String
    
    Const KEYWORD_WHERE As String = " WHERE "
    Const KEYWORD_AND As String = " AND "
    
    'Initialize SQL Statment
    strSQL = "SELECT Item.LcID, Item.Item, Item.ItDesc, " & vbCrLf & _
            " Item.ItStat, Item.VnID, Item.Assort, Item.ByID " & vbCrLf & _
            " FROM Item " & vbCrLf
    WhrStmt = KEYWORD_WHERE
    
    'Build the Where Statment
    If Trim$(Me.txtLcid.Text) <> "" Then
        WhrStmt = WhrStmt & " LcID " & Me.dcComparison.Value & " " & LcIDKey_Criteria & " "
    End If
        
    If Trim$(Me.txtItem.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), Trim$(KEYWORD_WHERE), vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & " Item.Item "
        Else
            WhrStmt = WhrStmt & vbCrLf & KEYWORD_AND & " Item.Item "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & ItemKey_Criteria & " "
    
    End If
        
    If Trim$(Me.txtItDesc.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), Trim$(KEYWORD_WHERE), vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & " Item.ItDesc "
        Else
            WhrStmt = WhrStmt & vbCrLf & KEYWORD_AND & " Item.ItDesc "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & ItDescKey_Criteria & " "

    End If
    
    If Trim$(Me.txtById.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), Trim$(KEYWORD_WHERE), vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & " Item.ByID "
        Else
            WhrStmt = WhrStmt & vbCrLf & KEYWORD_AND & " Item.ByID "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & ByIdKey_Criteria & " "
            
    End If
        
    If Trim$(Me.txtVnId.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), Trim$(KEYWORD_WHERE), vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & " Item.VnID "
        Else
            WhrStmt = WhrStmt & vbCrLf & KEYWORD_AND & " Item.VnID "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & VnIdKey_Criteria & " "
            
    End If
    
    If Trim$(Me.txtAssort.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), Trim$(KEYWORD_WHERE), vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & " Item.Assort "
        Else
            WhrStmt = WhrStmt & "AND Item.Assort "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & AssortKey_Criteria & " "
            
    End If
    
    strSQL = strSQL & WhrStmt & " ORDER BY Item.LcID, Item.Item"
    
    BuildItemLookupQuery = strSQL
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BuildItemLookupQuery)", Err.Description
     f_HandleErr , , , "AIM_ItemLookup::BuildItemLookupQuery", Now, gDRGeneralError, True, Err
End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim strMessage As String
    Dim strText As String
    
    Screen.MousePointer = vbHourglass
    Write_Message ""
    
    'Make sure the user selected at least one criterion
    If (Trim$(Me.txtLcid.Text) & _
        Trim$(Me.txtItem.Text) & _
        Trim$(Me.txtItDesc.Text) & _
        Trim$(Me.txtById.Text) & _
        Trim$(Me.txtVnId.Text) & _
        Trim$(Me.txtAssort.Text) _
        ) = "" _
    Then
        strMessage = getTranslationResource("MSGBOX02000")
        strText = getTranslationResource(Me.Caption)
        If StrComp(strMessage, "MSGBOX02000") = 0 Then strMessage = "Please select one or more Lookup Criteria "
        MsgBox strMessage, vbExclamation, strText
                        
        Me.txtLcid.SetFocus
        'Return the screen values
        Screen.MousePointer = vbDefault
        
        Exit Sub
    End If
    
    'Force validation
    txtLcID_Validate False
    txtItem_Validate False
    txtItDesc_Validate False
    txtById_Validate False
    txtVnId_Validate False
    txtAssort_Validate False
    If StrComp(LcIDKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(ItemKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(ItDescKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(ByIdKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(VnIdKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(AssortKey_Criteria, "Error", vbTextCompare) = 0 _
    Then
        'Do NOT Proceed
    Else
        'Create the query
        strSQL = BuildItemLookupQuery()
        
        'Recreate the result set
        If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
        rsItem.MaxRecords = Me.txtMaxRows.Value
        rsItem.Open strSQL
        
        If f_IsRecordsetOpenAndPopulated(rsItem) Then
            'Refresh the grid
            Me.dgItem.ReBind
        Else
            If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
            Me.dgItem.Reset
        End If
    End If
    
    'Return the screen values
    Screen.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_ItemLookup::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_ItemLookup::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    Me.txtLcid.Text = ""
    Me.txtItem.Text = ""
    Me.txtItDesc.Text = ""
    Me.txtById.Text = ""
    Me.txtVnId.Text = ""
    Me.txtAssort.Text = ""
    
    LcIDKey_Criteria = ""
    ItemKey_Criteria = ""
    ItDescKey_Criteria = ""
    ByIdKey_Criteria = ""
    VnIdKey_Criteria = ""
    AssortKey_Criteria = ""
    
    If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
    Me.dgItem.Reset
    
    Me.txtMaxRows.Value = 200
    
    Me.txtLcid.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_ItemLookup::cmdClear_Click", Now, gDRGeneralError, True, Err

End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgItem.Rows > 0 Then
        Screen.MousePointer = vbHourglass
        Me.dgItem.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_ItemLookup::cmdPrint_Click", Now, gDRGeneralError, True, Err

End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = False
    Me.LcIdKey = Me.dgItem.Columns("lcid").Value
    Me.ItemKey = Me.dgItem.Columns("item").Value
    Me.ItDesc = Me.dgItem.Columns("itdesc").Value
    Me.AssortKey = Me.dgItem.Columns("Assort").Value
    Me.ByIdKey = Me.dgItem.Columns("ByID").Value
    Me.VnIdKey = Me.dgItem.Columns("VnID").Value
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_ItemLookup::cmdReturn_Click", Now, gDRGeneralError, True, Err

End Sub

Private Sub dcComparison_CloseUp()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case Me.dcComparison.Columns(0).Text
        Case "LIKE"
            Select Case Me.dcComparison.Columns(1).Text
                Case getTranslationResource("Begins With")
                    strMessage = getTranslationResource("STATMSG02000")
                    If StrComp(strMessage, "STATMSG02000") = 0 Then strMessage = "Enter either part or all of the beginning of the desired value. "
                    Write_Message strMessage
                Case getTranslationResource("Contains")
                    strMessage = getTranslationResource("STATMSG02001")
                    If StrComp(strMessage, "STATMSG02001") = 0 Then strMessage = "Enter a string expression which is contained within the desired value. "
                    Write_Message strMessage
            End Select
        
        Case "BETWEEN"
            strMessage = getTranslationResource("STATMSG02002")
            If StrComp(strMessage, "STATMSG02002") = 0 Then strMessage = "Enter two values between which the desired value should fall, separated by a comma. "
            Write_Message strMessage
        
        Case "IN"
            strMessage = getTranslationResource("STATMSG02003")
            If StrComp(strMessage, "STATMSG02003") = 0 Then strMessage = "Enter a list of values in which the desired value should fall, separated by commas. "
            Write_Message strMessage
        
        Case Else
            strMessage = getTranslationResource("STATMSG02004")
            If StrComp(strMessage, "STATMSG02004") = 0 Then strMessage = "Enter a value for comparison. "
            Write_Message strMessage
        
    End Select
    
    'Reset the comparison values
    txtLcID_Validate False
    txtItem_Validate False
    txtItDesc_Validate False
    txtById_Validate False
    txtVnId_Validate False
    txtAssort_Validate False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_CloseUp)"
     f_HandleErr , , , "AIM_ItemLookup::dcComparison_CloseUp", Now, gDRGeneralError, True, Err

End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Build Columns
    Me.dcComparison.Columns(0).Caption = getTranslationResource("Comparison")
    Me.dcComparison.Columns(0).FieldLen = 1
    Me.dcComparison.Columns(0).Width = 1000
    
    Me.dcComparison.Columns(1).Caption = getTranslationResource("Description")
    Me.dcComparison.Columns(1).FieldLen = 24
    Me.dcComparison.Columns(1).Width = 2000
    
    'Load Values
    Me.dcComparison.AddItem ("=" & Chr(9) & _
                            getTranslationResource("Equal To"))
    Me.dcComparison.AddItem (">" & Chr(9) & _
                            getTranslationResource("Greater Than"))
    Me.dcComparison.AddItem (">=" & Chr(9) & _
                            getTranslationResource("Greater Than / Equal To"))
    Me.dcComparison.AddItem ("<" & Chr(9) & _
                            getTranslationResource("Less Than"))
    Me.dcComparison.AddItem ("<=" & Chr(9) & _
                            getTranslationResource("Less Than / Equal To"))
    Me.dcComparison.AddItem ("<>" & Chr(9) & _
                            getTranslationResource("Not Equal To"))
    Me.dcComparison.AddItem ("LIKE" & Chr(9) & _
                            getTranslationResource("Begins With"))
    Me.dcComparison.AddItem ("LIKE" & Chr(9) & _
                            getTranslationResource("Contains"))
    Me.dcComparison.AddItem ("BETWEEN" & Chr(9) & _
                            getTranslationResource("Is Between These Values"))
    Me.dcComparison.AddItem ("IN" & Chr(9) & _
                            getTranslationResource("Is in this List of Values"))
                            
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcComparison, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcComparison.Columns.Count - 1
'        dcComparison.Columns(IndexCounter).HasHeadBackColor = True
'        dcComparison.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_InitColumnProps)"
     f_HandleErr , , , "AIM_ItemLookup::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err

End Sub

Private Sub dgItem_DblClick()
On Error GoTo ErrorHandler

    'Return selected item
    cmdReturn_Click
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItem_DblClick)"
     f_HandleErr , , , "AIM_ItemLookup::dgItem_DblClick", Now, gDRGeneralError, True, Err

End Sub

Private Sub dgItem_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Item Data
    Me.dgItem.Columns(0).Name = "lcid"
    Me.dgItem.Columns(0).Caption = getTranslationResource("Location")
    Me.dgItem.Columns(0).Width = 720
    Me.dgItem.Columns(0).Locked = True
    
    Me.dgItem.Columns(1).Name = "item"
    Me.dgItem.Columns(1).Caption = getTranslationResource("Item")
    Me.dgItem.Columns(1).Width = 1440
    Me.dgItem.Columns(1).Locked = True
    
    Me.dgItem.Columns(2).Name = "itdesc"
    Me.dgItem.Columns(2).Caption = getTranslationResource("Description")
    Me.dgItem.Columns(2).Width = 2880
    Me.dgItem.Columns(2).Locked = True
    
    Me.dgItem.Columns(3).Name = "byid"
    Me.dgItem.Columns(3).Caption = getTranslationResource("Buyer")
    Me.dgItem.Columns(3).Width = 720
    Me.dgItem.Columns(3).Locked = True
    
    Me.dgItem.Columns(4).Name = "vnid"
    Me.dgItem.Columns(4).Caption = getTranslationResource("Vendor")
    Me.dgItem.Columns(4).Width = 720
    Me.dgItem.Columns(4).Locked = True
    
    Me.dgItem.Columns(5).Name = "assort"
    Me.dgItem.Columns(5).Caption = getTranslationResource("Assortment")
    Me.dgItem.Columns(5).Width = 720
    Me.dgItem.Columns(5).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgItem, ACW_EXPAND
    End If
   
    For IndexCounter = 0 To dgItem.Columns.Count - 1
'        dgItem.Columns(IndexCounter).HasHeadBackColor = True
'        dgitem.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgItem.Columns(IndexCounter).Locked = False Then dgItem.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItem_InitColumnProps)"
     f_HandleErr , , , "AIM_ItemLookup::dgItem_InitColumnProps", Now, gDRGeneralError, True, Err

End Sub

Private Sub dgItem_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG02000")
    If StrComp(strMessage, "RPTMSG02000") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG02001")
    If StrComp(strMessage1, "RPTMSG02001") = 0 Then strMessage1 = "Item Lookup List"
    strMessage2 = getTranslationResource("RPTMSG02002")
    If StrComp(strMessage2, "RPTMSG02002") = 0 Then strMessage2 = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & _
                            vbTab & strMessage1 & _
                            vbTab & strMessage2 & " <page number>"

    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItem_PrintInitialize)"
     f_HandleErr , , , "AIM_ItemLookup::dgItem_PrintInitialize", Now, gDRGeneralError, True, Err

End Sub

Private Sub dgItem_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsItem) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsItem.MoveLast
        Else
            rsItem.MoveFirst
        End If
    Else
        rsItem.Bookmark = StartLocation
        If ReadPriorRows Then
            rsItem.MovePrevious
        Else
            rsItem.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsItem.BOF Or rsItem.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsItem("lcid").Value
        RowBuf.Value(i, 1) = rsItem("item").Value
        RowBuf.Value(i, 2) = rsItem("itdesc").Value
        RowBuf.Value(i, 3) = rsItem("byid").Value
        RowBuf.Value(i, 4) = rsItem("vnid").Value
        RowBuf.Value(i, 5) = rsItem("assort").Value

        RowBuf.Bookmark(i) = rsItem.Bookmark
    
        If ReadPriorRows Then
            rsItem.MovePrevious
        Else
            rsItem.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItem_UnboundReadData)"
     f_HandleErr , , , "AIM_ItemLookup::dgItem_UnboundReadData", Now, gDRGeneralError, True, Err

End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Set the default value for the Cancel Flag
    CancelFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_ItemLookup::Form_Activate", Now, gDRGeneralError, True, Err

End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim strSQL As String
    
    Screen.MousePointer = vbHourglass
    
    'Initialize fields
    Me.txtLcid.Text = Me.LcIdKey
    Me.txtItem.Text = Me.ItemKey
    Me.txtItDesc.Text = Me.ItDesc
    Me.txtById.Text = Me.ByIdKey
    Me.txtVnId.Text = Me.VnIdKey
    Me.txtAssort.Text = Me.AssortKey
    
    LcIDKey_Criteria = BldCompVal(Me.LcIdKey, Me.dcComparison.Value, Me.dcComparison.Text)
    ItemKey_Criteria = BldCompVal(Me.ItemKey, Me.dcComparison.Value, Me.dcComparison.Text)
    ItDescKey_Criteria = BldCompVal(Me.ItDesc, Me.dcComparison.Value, Me.dcComparison.Text)
    ByIdKey_Criteria = BldCompVal(Me.ByIdKey, Me.dcComparison.Value, Me.dcComparison.Text)
    VnIdKey_Criteria = BldCompVal(Me.VnIdKey, Me.dcComparison.Value, Me.dcComparison.Text)
    AssortKey_Criteria = BldCompVal(Me.AssortKey, Me.dcComparison.Value, Me.dcComparison.Text)
    
    Me.txtMaxRows.Value = 200
    
    If Comparison = "" Or IsNull(Comparison) Then
        Me.dcComparison.Text = getTranslationResource("Greater Than / Equal To")
    Else
        Me.dcComparison.Text = getTranslationResource(Comparison)
    End If
        
    'Build initial record set
    Set rsItem = New ADODB.Recordset
    rsItem.CursorLocation = adUseClient
    rsItem.CursorType = adOpenStatic
    rsItem.LockType = adLockReadOnly
    Set rsItem.ActiveConnection = Cn
    
    strSQL = "SELECT  Item.LcID, Item.Item, Item.ItDesc, Item.ItStat, Item.VnID, Item.Assort, Item.ByID " & _
            " FROM Item " & _
            " WHERE LcID = 'xxx' AND Item = 'xxx' "
    rsItem.Open strSQL
    
    'Bind the grid
    Me.dgItem.ReBind
       
    Me.txtMaxRows.Spin.Visible = 1
    
    GetTranslatedCaptions Me
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption
     f_HandleErr , , , "AIM_ItemLookup::Form_Load", Now, gDRGeneralError, True, Err

End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
    Set rsItem = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_ItemLookup::Form_Unload", Now, gDRGeneralError, True, Err

    End If
    Resume Next
End Sub

Private Function BldCompVal(CompVals As String, CompType As String, _
    CompDetail As String)
On Error GoTo ErrorHandler

    Dim i, N As Integer
    Dim newtext As String
    Dim RtnStr As String
    Dim StrArray(0 To 10) As String
    Dim strMessage As String
    Dim strText As String
    
    strText = getTranslationResource(Me.Caption)
    
    Select Case CompType
        Case getTranslationResource("LIKE")
            Select Case CompDetail
                Case getTranslationResource("Begins With")
                    CompVals = "'" & CompVals & "%" & "'"
                Case getTranslationResource("Contains")
                    CompVals = "'%" & CompVals & "%" & "'"
            End Select
        
        Case getTranslationResource("BETWEEN")
            N = Parse(Trim$(CompVals), ", ", "'", StrArray())
            
            If N <> 2 Then
                strMessage = getTranslationResource("MSGBOX02001")
                If StrComp(strMessage, "MSGBOX02001") = 0 Then strMessage = "Please enter two values separated by a comma."
                MsgBox strMessage, vbExclamation, strText
                        
                BldCompVal = "Error"
                Exit Function
            Else
                CompVals = "'" & StrArray(0) & "'" & " and " & "'" & StrArray(1) & "' "
            End If
            
        Case getTranslationResource("IN")
            N = Parse(Trim$(CompVals), ", ", "'", StrArray())
            
            newtext = "("
            
            For i = 0 To (N - 2)
                newtext = newtext & "'" & StrArray(i) & "'" & ", "
            Next i
            
            newtext = newtext & "'" & StrArray(N - 1) & "'" & ")"
                
            CompVals = newtext
            
        Case Else
            CompVals = "'" & Trim$(CompVals) & "'"
            
    End Select
    
    BldCompVal = CompVals

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldCompVal)", Err.Description
     f_HandleErr , , , "AIM_ItemLookup::BldCompVal", Now, gDRGeneralError, True, Err

End Function

Private Sub txtAssort_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(txtAssort.Text) = "" Then
        Exit Sub
    End If
    
    AssortKey_Criteria = BldCompVal(Me.txtAssort.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtAssort_Validate)"
     f_HandleErr , , , "AIM_ItemLookup::txtAssort_Validate", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtById_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    If Trim$(txtById.Text) = "" Then
        Exit Sub
    End If
    
    ByIdKey_Criteria = BldCompVal(Me.txtById.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtById_Validate)"
     f_HandleErr , , , "AIM_ItemLookup::txtById_Validate", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtItDesc_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(txtItDesc.Text) = "" Then
        Exit Sub
    End If
    
    ItDescKey_Criteria = BldCompVal(Me.txtItDesc.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtItDesc_Validate)"
     f_HandleErr , , , "AIM_ItemLookup::txtItDesc_Validate", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtItem_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(txtItem.Text) = "" Then
        Exit Sub
    End If
    
    ItemKey_Criteria = BldCompVal(Me.txtItem.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtItem_Validate)"
     f_HandleErr , , , "AIM_ItemLookup::txtItem_Validate", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtLcID_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(txtLcid.Text) = "" Then
        Exit Sub
    End If
    
    LcIDKey_Criteria = BldCompVal(Me.txtLcid.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtLcid_Validate)"
     f_HandleErr , , , "AIM_ItemLookup::txtLcid_Validate", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_ItemLookup::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_ItemLookup::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtVnId_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(txtVnId.Text) = "" Then
        Exit Sub
    End If
    
    VnIdKey_Criteria = BldCompVal(Me.txtVnId.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtVnId_Validate)"
     f_HandleErr , , , "AIM_ItemLookup::txtVnId_Validate", Now, gDRGeneralError, True, Err

End Sub
