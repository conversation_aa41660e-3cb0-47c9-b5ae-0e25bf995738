VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Object = "{F9043C88-F6F2-101A-A3C9-08002B2F49FB}#1.2#0"; "COMDLG32.OCX"
Begin VB.Form AIM_IntermittentAnalysis 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Intermittent Demand Analysis"
   ClientHeight    =   9405
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   15000
   Icon            =   "AIM_IntermittentAnalysis.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9405
   ScaleWidth      =   15000
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbIntermittentDemand 
      Left            =   120
      Top             =   9000
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Style           =   0
      Tools           =   "AIM_IntermittentAnalysis.frx":030A
      ToolBars        =   "AIM_IntermittentAnalysis.frx":1CCB
   End
   Begin ActiveTabs.SSActiveTabs atAnalysisOptions 
      Height          =   2385
      Left            =   0
      TabIndex        =   0
      Top             =   30
      Width           =   14985
      _ExtentX        =   26432
      _ExtentY        =   4207
      _Version        =   131083
      TabCount        =   2
      TagVariant      =   ""
      Tabs            =   "AIM_IntermittentAnalysis.frx":1DA4
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel4 
         Height          =   1995
         Left            =   30
         TabIndex        =   1
         Top             =   360
         Width           =   14925
         _ExtentX        =   26326
         _ExtentY        =   3519
         _Version        =   131083
         TabGuid         =   "AIM_IntermittentAnalysis.frx":1E5B
         Begin VB.Frame Frame1 
            Caption         =   "Analysis Options"
            Height          =   1545
            Left            =   30
            TabIndex        =   40
            Top             =   100
            Width           =   14865
            Begin VB.CommandButton cmdInFileName 
               Caption         =   "..."
               Height          =   345
               Left            =   14430
               TabIndex        =   46
               Top             =   675
               Width           =   345
            End
            Begin VB.CheckBox ckInventoryImpact 
               Caption         =   "Analyze Inventory Impact"
               Height          =   315
               Left            =   5325
               TabIndex        =   47
               Top             =   1110
               Value           =   1  'Checked
               Width           =   3855
            End
            Begin MSComDlg.CommonDialog CommonDialog1 
               Left            =   13590
               Top             =   165
               _ExtentX        =   847
               _ExtentY        =   847
               _Version        =   393216
            End
            Begin TDBNumber6Ctl.TDBNumber txtPctZeroMin 
               Height          =   345
               Left            =   3330
               TabIndex        =   41
               Top             =   300
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":1E83
               Caption         =   "AIM_IntermittentAnalysis.frx":1EA3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":1F0F
               Keys            =   "AIM_IntermittentAnalysis.frx":1F2D
               Spin            =   "AIM_IntermittentAnalysis.frx":1F77
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtPctZeroMax 
               Height          =   345
               Left            =   3330
               TabIndex        =   42
               Top             =   690
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":1F9F
               Caption         =   "AIM_IntermittentAnalysis.frx":1FBF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":202B
               Keys            =   "AIM_IntermittentAnalysis.frx":2049
               Spin            =   "AIM_IntermittentAnalysis.frx":2093
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtSrvLvlGoal 
               Height          =   345
               Left            =   3330
               TabIndex        =   43
               Top             =   1080
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":20BB
               Caption         =   "AIM_IntermittentAnalysis.frx":20DB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":2147
               Keys            =   "AIM_IntermittentAnalysis.frx":2165
               Spin            =   "AIM_IntermittentAnalysis.frx":21AF
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0.5
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLookBackPds 
               Height          =   345
               Left            =   7710
               TabIndex        =   44
               Top             =   270
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":21D7
               Caption         =   "AIM_IntermittentAnalysis.frx":21F7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":2263
               Keys            =   "AIM_IntermittentAnalysis.frx":2281
               Spin            =   "AIM_IntermittentAnalysis.frx":22CB
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "###0;-###0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   104
               MinValue        =   26
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBText6Ctl.TDBText txtInFileName 
               Height          =   345
               Left            =   7710
               TabIndex        =   45
               Top             =   675
               Width           =   6645
               _Version        =   65536
               _ExtentX        =   11721
               _ExtentY        =   609
               Caption         =   "AIM_IntermittentAnalysis.frx":22F3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":235F
               Key             =   "AIM_IntermittentAnalysis.frx":237D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   "IntAnalysis.Txt"
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label label 
               Caption         =   "Look Back Periods"
               Height          =   300
               Index           =   7
               Left            =   5325
               TabIndex        =   52
               Top             =   315
               Width           =   2340
            End
            Begin VB.Label Label2 
               Caption         =   "Analysis File Name"
               Height          =   300
               Left            =   5325
               TabIndex        =   51
               Top             =   720
               Width           =   2340
            End
            Begin VB.Label label 
               Caption         =   "Service Level Goal"
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   50
               Top             =   1125
               Width           =   3045
            End
            Begin VB.Label label 
               Caption         =   "Maximum Percentage Zero"
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   49
               Top             =   735
               Width           =   3045
            End
            Begin VB.Label label 
               Caption         =   "Minimum Percentage Zero"
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   48
               Top             =   345
               Width           =   3045
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel5 
         Height          =   1995
         Left            =   30
         TabIndex        =   2
         Top             =   360
         Width           =   14925
         _ExtentX        =   26326
         _ExtentY        =   3519
         _Version        =   131083
         TabGuid         =   "AIM_IntermittentAnalysis.frx":23C1
         Begin VB.CommandButton cmdCreate 
            Caption         =   "&Create Analysis File"
            Height          =   345
            Left            =   11460
            Style           =   1  'Graphical
            TabIndex        =   61
            Top             =   1600
            Width           =   3405
         End
         Begin VB.Frame Frame2 
            Height          =   1425
            Index           =   1
            Left            =   30
            TabIndex        =   53
            Top             =   100
            Width           =   14865
            Begin VB.CommandButton cmdGetFile 
               Caption         =   "..."
               Height          =   345
               Left            =   14340
               TabIndex        =   59
               Top             =   600
               Width           =   345
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMLocations 
               Bindings        =   "AIM_IntermittentAnalysis.frx":23E9
               Height          =   345
               Left            =   3120
               TabIndex        =   54
               Top             =   240
               Width           =   1380
               DataFieldList   =   "LcId"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2434
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "LcId"
            End
            Begin TDBNumber6Ctl.TDBNumber txtEndYear 
               Height          =   345
               Left            =   3120
               TabIndex        =   55
               Top             =   600
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Calculator      =   "AIM_IntermittentAnalysis.frx":2402
               Caption         =   "AIM_IntermittentAnalysis.frx":2422
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":248E
               Keys            =   "AIM_IntermittentAnalysis.frx":24AC
               Spin            =   "AIM_IntermittentAnalysis.frx":24F6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0000;;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   2999
               MinValue        =   1990
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtEndPeriod 
               Height          =   345
               Left            =   3120
               TabIndex        =   56
               Top             =   960
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Calculator      =   "AIM_IntermittentAnalysis.frx":251E
               Caption         =   "AIM_IntermittentAnalysis.frx":253E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":25AA
               Keys            =   "AIM_IntermittentAnalysis.frx":25C8
               Spin            =   "AIM_IntermittentAnalysis.frx":2612
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDemandSource 
               Bindings        =   "AIM_IntermittentAnalysis.frx":263A
               DataField       =   " "
               Height          =   345
               Left            =   8160
               TabIndex        =   57
               Top             =   240
               Width           =   1380
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2434
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBText6Ctl.TDBText txtRcdCount 
               Height          =   345
               Left            =   8160
               TabIndex        =   60
               TabStop         =   0   'False
               Top             =   960
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Caption         =   "AIM_IntermittentAnalysis.frx":2645
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":26B1
               Key             =   "AIM_IntermittentAnalysis.frx":26CF
               BackColor       =   12648447
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   "0"
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtFileName 
               Height          =   345
               Left            =   8160
               TabIndex        =   58
               Top             =   600
               Width           =   6075
               _Version        =   65536
               _ExtentX        =   10716
               _ExtentY        =   609
               Caption         =   "AIM_IntermittentAnalysis.frx":2713
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":277F
               Key             =   "AIM_IntermittentAnalysis.frx":279D
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   "IntAnalysis.Txt"
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label17 
               Caption         =   "Location ID"
               Height          =   300
               Left            =   240
               TabIndex        =   67
               Top             =   285
               Width           =   2730
            End
            Begin VB.Label label 
               Caption         =   "Ending Period"
               Height          =   300
               Index           =   6
               Left            =   240
               TabIndex        =   66
               Top             =   1005
               Width           =   2730
            End
            Begin VB.Label label 
               Caption         =   "Ending Fiscal Year"
               Height          =   300
               Index           =   5
               Left            =   240
               TabIndex        =   65
               Top             =   645
               Width           =   2730
            End
            Begin VB.Label Label16 
               Caption         =   "Demand Source"
               Height          =   300
               Left            =   5350
               TabIndex        =   64
               Top             =   285
               Width           =   2730
            End
            Begin VB.Label label 
               Caption         =   "Records Processed"
               Height          =   300
               Index           =   21
               Left            =   5355
               TabIndex        =   63
               Top             =   1005
               Width           =   2730
            End
            Begin VB.Label Label1 
               Caption         =   "Analysis File Name"
               Height          =   300
               Left            =   5350
               TabIndex        =   62
               Top             =   645
               Width           =   2730
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel6 
         Height          =   6435
         Left            =   -99969
         TabIndex        =   3
         Top             =   360
         Width           =   14925
         _ExtentX        =   26326
         _ExtentY        =   11351
         _Version        =   131083
         TabGuid         =   "AIM_IntermittentAnalysis.frx":27E1
         Begin TDBText6Ctl.TDBText TDBText6 
            Height          =   345
            Left            =   3000
            TabIndex        =   4
            TabStop         =   0   'False
            Top             =   240
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2809
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2875
            Key             =   "AIM_IntermittentAnalysis.frx":2893
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid SSOleDBGrid1 
            Height          =   3495
            Left            =   195
            TabIndex        =   5
            Top             =   2370
            Width           =   14595
            _Version        =   196617
            DataMode        =   1
            AllowUpdate     =   0   'False
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            CaptionAlignment=   0
            Columns(0).Width=   3200
            _ExtentX        =   25744
            _ExtentY        =   6165
            _StockProps     =   79
         End
         Begin TDBText6Ctl.TDBText TDBText7 
            Height          =   345
            Left            =   3000
            TabIndex        =   6
            TabStop         =   0   'False
            Top             =   660
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":28D7
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2943
            Key             =   "AIM_IntermittentAnalysis.frx":2961
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText8 
            Height          =   345
            Left            =   3000
            TabIndex        =   7
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":29A5
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2A11
            Key             =   "AIM_IntermittentAnalysis.frx":2A2F
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText9 
            Height          =   345
            Left            =   3000
            TabIndex        =   8
            TabStop         =   0   'False
            Top             =   1500
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2A73
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2ADF
            Key             =   "AIM_IntermittentAnalysis.frx":2AFD
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText10 
            Height          =   345
            Left            =   3000
            TabIndex        =   9
            TabStop         =   0   'False
            Top             =   1920
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2B41
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2BAD
            Key             =   "AIM_IntermittentAnalysis.frx":2BCB
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText11 
            Height          =   345
            Left            =   6990
            TabIndex        =   10
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2C0F
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2C7B
            Key             =   "AIM_IntermittentAnalysis.frx":2C99
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText12 
            Height          =   345
            Left            =   8550
            TabIndex        =   11
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2CDD
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2D49
            Key             =   "AIM_IntermittentAnalysis.frx":2D67
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText13 
            Height          =   345
            Left            =   10110
            TabIndex        =   12
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2DAB
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2E17
            Key             =   "AIM_IntermittentAnalysis.frx":2E35
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText14 
            Height          =   345
            Left            =   13410
            TabIndex        =   13
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2E79
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2EE5
            Key             =   "AIM_IntermittentAnalysis.frx":2F03
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText15 
            Height          =   345
            Left            =   11670
            TabIndex        =   14
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":2F47
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":2FB3
            Key             =   "AIM_IntermittentAnalysis.frx":2FD1
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText16 
            Height          =   345
            Left            =   6990
            TabIndex        =   15
            TabStop         =   0   'False
            Top             =   1465
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":3015
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":3081
            Key             =   "AIM_IntermittentAnalysis.frx":309F
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText17 
            Height          =   345
            Left            =   8550
            TabIndex        =   16
            TabStop         =   0   'False
            Top             =   1465
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":30E3
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":314F
            Key             =   "AIM_IntermittentAnalysis.frx":316D
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText18 
            Height          =   345
            Left            =   10110
            TabIndex        =   17
            TabStop         =   0   'False
            Top             =   1465
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":31B1
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":321D
            Key             =   "AIM_IntermittentAnalysis.frx":323B
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText19 
            Height          =   345
            Left            =   13410
            TabIndex        =   18
            TabStop         =   0   'False
            Top             =   1465
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":327F
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":32EB
            Key             =   "AIM_IntermittentAnalysis.frx":3309
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText20 
            Height          =   345
            Left            =   11670
            TabIndex        =   19
            TabStop         =   0   'False
            Top             =   1465
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":334D
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":33B9
            Key             =   "AIM_IntermittentAnalysis.frx":33D7
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText21 
            Height          =   345
            Left            =   6990
            TabIndex        =   20
            TabStop         =   0   'False
            Top             =   1850
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":341B
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":3487
            Key             =   "AIM_IntermittentAnalysis.frx":34A5
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText22 
            Height          =   345
            Left            =   8550
            TabIndex        =   21
            TabStop         =   0   'False
            Top             =   1850
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":34E9
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":3555
            Key             =   "AIM_IntermittentAnalysis.frx":3573
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText23 
            Height          =   345
            Left            =   10110
            TabIndex        =   22
            TabStop         =   0   'False
            Top             =   1850
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":35B7
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":3623
            Key             =   "AIM_IntermittentAnalysis.frx":3641
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText24 
            Height          =   345
            Left            =   13410
            TabIndex        =   23
            TabStop         =   0   'False
            Top             =   1850
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":3685
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":36F1
            Key             =   "AIM_IntermittentAnalysis.frx":370F
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText TDBText25 
            Height          =   345
            Left            =   11670
            TabIndex        =   24
            TabStop         =   0   'False
            Top             =   1850
            Width           =   1200
            _Version        =   65536
            _ExtentX        =   2117
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":3753
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":37BF
            Key             =   "AIM_IntermittentAnalysis.frx":37DD
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin VB.Label label 
            Caption         =   "Items Tested"
            Height          =   300
            Index           =   39
            Left            =   210
            TabIndex        =   39
            Top             =   300
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Weeks Tested"
            Height          =   300
            Index           =   38
            Left            =   210
            TabIndex        =   38
            Top             =   1125
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Safety Stock OK Percentage"
            Height          =   300
            Index           =   37
            Left            =   210
            TabIndex        =   37
            Top             =   1545
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Percent Zero Count"
            Height          =   300
            Index           =   36
            Left            =   210
            TabIndex        =   36
            Top             =   720
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Elapsed Time (mm:ss)"
            Height          =   300
            Index           =   35
            Left            =   210
            TabIndex        =   35
            Top             =   1980
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Normal Items"
            Height          =   300
            Index           =   34
            Left            =   5000
            TabIndex        =   34
            Top             =   1125
            Width           =   1800
         End
         Begin VB.Label label 
            Caption         =   "Total"
            Height          =   300
            Index           =   33
            Left            =   5000
            TabIndex        =   33
            Top             =   1895
            Width           =   1800
         End
         Begin VB.Label label 
            Caption         =   "Intermittent Items"
            Height          =   300
            Index           =   32
            Left            =   5000
            TabIndex        =   32
            Top             =   1510
            Width           =   1800
         End
         Begin VB.Label Label11 
            Alignment       =   2  'Center
            Caption         =   "Inventory Cost"
            Height          =   345
            Left            =   8370
            TabIndex        =   31
            Top             =   660
            Width           =   1560
         End
         Begin VB.Label Label10 
            Alignment       =   2  'Center
            Caption         =   "Inventory Cost"
            Height          =   345
            Left            =   11490
            TabIndex        =   30
            Top             =   660
            Width           =   1560
         End
         Begin VB.Label Label9 
            Alignment       =   2  'Center
            Caption         =   "Average Inventory - Normal Safety Stock"
            Height          =   465
            Left            =   6870
            TabIndex        =   29
            Top             =   120
            Width           =   3060
         End
         Begin VB.Label Label8 
            Alignment       =   2  'Center
            Caption         =   "Average Inventory - Intermittent Safety Stock"
            Height          =   465
            Left            =   9990
            TabIndex        =   28
            Top             =   120
            Width           =   3060
         End
         Begin VB.Label Label7 
            Alignment       =   2  'Center
            Caption         =   "Change Percentage"
            Height          =   345
            Left            =   13110
            TabIndex        =   27
            Top             =   660
            Width           =   1800
         End
         Begin VB.Label Label6 
            Alignment       =   2  'Center
            Caption         =   "Units"
            Height          =   345
            Left            =   9990
            TabIndex        =   26
            Top             =   660
            Width           =   1440
         End
         Begin VB.Label Label5 
            Alignment       =   2  'Center
            Caption         =   "Units"
            Height          =   345
            Left            =   6870
            TabIndex        =   25
            Top             =   660
            Width           =   1440
         End
         Begin VB.Line Line1 
            Index           =   21
            X1              =   6840
            X2              =   13080
            Y1              =   75
            Y2              =   75
         End
         Begin VB.Line Line1 
            Index           =   20
            X1              =   6840
            X2              =   14880
            Y1              =   620
            Y2              =   620
         End
         Begin VB.Line Line1 
            Index           =   19
            X1              =   4875
            X2              =   14895
            Y1              =   1035
            Y2              =   1035
         End
         Begin VB.Line Line1 
            Index           =   18
            X1              =   4875
            X2              =   14895
            Y1              =   2235
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   17
            X1              =   9950
            X2              =   9950
            Y1              =   75
            Y2              =   2230
         End
         Begin VB.Line Line1 
            Index           =   16
            X1              =   13080
            X2              =   13080
            Y1              =   75
            Y2              =   2230
         End
         Begin VB.Line Line1 
            Index           =   15
            X1              =   8330
            X2              =   8330
            Y1              =   620
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   14
            X1              =   11455
            X2              =   11455
            Y1              =   620
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   13
            X1              =   6840
            X2              =   6840
            Y1              =   75
            Y2              =   2230
         End
         Begin VB.Line Line1 
            Index           =   12
            X1              =   14880
            X2              =   14880
            Y1              =   620
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   11
            X1              =   4875
            X2              =   4875
            Y1              =   1035
            Y2              =   2230
         End
      End
   End
   Begin ActiveTabs.SSActiveTabs atIntermittentDemand 
      Height          =   6465
      Left            =   0
      TabIndex        =   68
      Top             =   2520
      Width           =   14985
      _ExtentX        =   26432
      _ExtentY        =   11404
      _Version        =   131083
      TabCount        =   2
      TagVariant      =   ""
      Tabs            =   "AIM_IntermittentAnalysis.frx":3821
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   6075
         Left            =   30
         TabIndex        =   69
         Top             =   360
         Width           =   14925
         _ExtentX        =   26326
         _ExtentY        =   10716
         _Version        =   131083
         TabGuid         =   "AIM_IntermittentAnalysis.frx":38DA
         Begin VB.CommandButton cmdExecute 
            Caption         =   "E&xecute Lead Time Distribution Model"
            Height          =   345
            Left            =   9750
            Style           =   1  'Graphical
            TabIndex        =   77
            Top             =   5640
            Width           =   4725
         End
         Begin VB.Frame Frame3 
            Caption         =   "Simulation Results"
            Height          =   1695
            Left            =   300
            TabIndex        =   84
            Top             =   3450
            Width           =   5145
            Begin TDBText6Ctl.TDBText txtModelMean 
               DataField       =   "a"
               Height          =   340
               Left            =   3290
               TabIndex        =   85
               TabStop         =   0   'False
               Top             =   330
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Caption         =   "AIM_IntermittentAnalysis.frx":3902
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":396E
               Key             =   "AIM_IntermittentAnalysis.frx":398C
               BackColor       =   12648447
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   "0.00"
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtModelStdDev 
               Height          =   340
               Left            =   3290
               TabIndex        =   86
               TabStop         =   0   'False
               Top             =   690
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Caption         =   "AIM_IntermittentAnalysis.frx":39D0
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":3A3C
               Key             =   "AIM_IntermittentAnalysis.frx":3A5A
               BackColor       =   12648447
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   "0.00"
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtModelMaxDemand 
               Height          =   340
               Left            =   3290
               TabIndex        =   87
               TabStop         =   0   'False
               Top             =   1050
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Caption         =   "AIM_IntermittentAnalysis.frx":3A9E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":3B0A
               Key             =   "AIM_IntermittentAnalysis.frx":3B28
               BackColor       =   12648447
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   1
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   "0.00"
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label label 
               Caption         =   "Model Standard Deviation"
               Height          =   300
               Index           =   19
               Left            =   200
               TabIndex        =   90
               Top             =   750
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Model Max Demand"
               Height          =   300
               Index           =   18
               Left            =   200
               TabIndex        =   89
               Top             =   1110
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Model Mean"
               Height          =   300
               Index           =   17
               Left            =   200
               TabIndex        =   88
               Top             =   390
               Width           =   3000
            End
         End
         Begin VB.Frame Frame2 
            Caption         =   "Options"
            Height          =   2895
            Index           =   0
            Left            =   300
            TabIndex        =   70
            Top             =   390
            Width           =   5145
            Begin TDBNumber6Ctl.TDBNumber txtLTWks 
               Height          =   340
               Left            =   3290
               TabIndex        =   71
               Top             =   480
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":3B6C
               Caption         =   "AIM_IntermittentAnalysis.frx":3B8C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":3BF8
               Keys            =   "AIM_IntermittentAnalysis.frx":3C16
               Spin            =   "AIM_IntermittentAnalysis.frx":3C60
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   154
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtPctZero 
               Height          =   340
               Left            =   3290
               TabIndex        =   72
               Top             =   870
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":3C88
               Caption         =   "AIM_IntermittentAnalysis.frx":3CA8
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":3D14
               Keys            =   "AIM_IntermittentAnalysis.frx":3D32
               Spin            =   "AIM_IntermittentAnalysis.frx":3D7C
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNZ_Mean 
               Height          =   340
               Left            =   3290
               TabIndex        =   73
               Top             =   1260
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":3DA4
               Caption         =   "AIM_IntermittentAnalysis.frx":3DC4
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":3E30
               Keys            =   "AIM_IntermittentAnalysis.frx":3E4E
               Spin            =   "AIM_IntermittentAnalysis.frx":3E98
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "############0.00;-############0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "############0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999999999999999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNZ_StdDev 
               Height          =   340
               Left            =   3290
               TabIndex        =   74
               Top             =   1650
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":3EC0
               Caption         =   "AIM_IntermittentAnalysis.frx":3EE0
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":3F4C
               Keys            =   "AIM_IntermittentAnalysis.frx":3F6A
               Spin            =   "AIM_IntermittentAnalysis.frx":3FB4
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "############0.00;-############0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "############0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999999999999999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtSrvLvlGoal2 
               Height          =   340
               Left            =   3290
               TabIndex        =   75
               Top             =   2010
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":3FDC
               Caption         =   "AIM_IntermittentAnalysis.frx":3FFC
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":4068
               Keys            =   "AIM_IntermittentAnalysis.frx":4086
               Spin            =   "AIM_IntermittentAnalysis.frx":40D0
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0.5
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNbrLoops 
               Height          =   340
               Left            =   3290
               TabIndex        =   76
               Top             =   2400
               Width           =   1155
               _Version        =   65536
               _ExtentX        =   2037
               _ExtentY        =   600
               Calculator      =   "AIM_IntermittentAnalysis.frx":40F8
               Caption         =   "AIM_IntermittentAnalysis.frx":4118
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_IntermittentAnalysis.frx":4184
               Keys            =   "AIM_IntermittentAnalysis.frx":41A2
               Spin            =   "AIM_IntermittentAnalysis.frx":41EC
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#####0;-#####0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#####0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   100000
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1245185
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label label 
               Caption         =   "Number of Loops"
               Height          =   300
               Index           =   14
               Left            =   200
               TabIndex        =   83
               Top             =   2430
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Service Level Goal"
               Height          =   300
               Index           =   13
               Left            =   200
               TabIndex        =   82
               Top             =   2040
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Non-Zero Standard Deviation"
               Height          =   300
               Index           =   12
               Left            =   200
               TabIndex        =   81
               Top             =   1680
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Non-Zero Mean"
               Height          =   300
               Index           =   11
               Left            =   200
               TabIndex        =   80
               Top             =   1290
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Percentage Zero"
               Height          =   300
               Index           =   10
               Left            =   200
               TabIndex        =   79
               Top             =   900
               Width           =   3000
            End
            Begin VB.Label label 
               Caption         =   "Lead Time (Weeks)"
               Height          =   300
               Index           =   9
               Left            =   200
               TabIndex        =   78
               Top             =   510
               Width           =   3000
            End
         End
         Begin C1Chart2D8.Chart2D FSChart 
            Height          =   5355
            Left            =   5820
            TabIndex        =   91
            Top             =   150
            Width           =   8655
            _Version        =   524288
            _Revision       =   7
            _ExtentX        =   15266
            _ExtentY        =   9446
            _StockProps     =   0
            ControlProperties=   "AIM_IntermittentAnalysis.frx":4214
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   6075
         Left            =   30
         TabIndex        =   92
         Top             =   360
         Width           =   14925
         _ExtentX        =   26326
         _ExtentY        =   10716
         _Version        =   131083
         TabGuid         =   "AIM_IntermittentAnalysis.frx":48C4
         Begin TDBText6Ctl.TDBText txtNbrItems 
            Height          =   345
            Left            =   3000
            TabIndex        =   93
            TabStop         =   0   'False
            Top             =   240
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":48EC
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4958
            Key             =   "AIM_IntermittentAnalysis.frx":4976
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgResults 
            Height          =   3495
            Left            =   195
            TabIndex        =   114
            Top             =   2370
            Width           =   14595
            _Version        =   196617
            DataMode        =   1
            AllowUpdate     =   0   'False
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            CaptionAlignment=   0
            Columns(0).Width=   3200
            _ExtentX        =   25744
            _ExtentY        =   6165
            _StockProps     =   79
         End
         Begin TDBText6Ctl.TDBText txtPctZeroCount 
            Height          =   345
            Left            =   3000
            TabIndex        =   94
            TabStop         =   0   'False
            Top             =   660
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":49BA
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4A26
            Key             =   "AIM_IntermittentAnalysis.frx":4A44
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtNbrTested 
            Height          =   345
            Left            =   3000
            TabIndex        =   95
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4A88
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4AF4
            Key             =   "AIM_IntermittentAnalysis.frx":4B12
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtSafetyStockOK 
            Height          =   345
            Left            =   3000
            TabIndex        =   96
            TabStop         =   0   'False
            Top             =   1500
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4B56
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4BC2
            Key             =   "AIM_IntermittentAnalysis.frx":4BE0
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtMinutes 
            Height          =   345
            Left            =   3000
            TabIndex        =   97
            TabStop         =   0   'False
            Top             =   1920
            Width           =   1470
            _Version        =   65536
            _ExtentX        =   2593
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4C24
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4C90
            Key             =   "AIM_IntermittentAnalysis.frx":4CAE
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtNI_NSS_Units 
            Height          =   345
            Left            =   6870
            TabIndex        =   98
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1440
            _Version        =   65536
            _ExtentX        =   2540
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4CF2
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4D5E
            Key             =   "AIM_IntermittentAnalysis.frx":4D7C
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtNI_NSS_Cost 
            Height          =   345
            Left            =   8370
            TabIndex        =   99
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1560
            _Version        =   65536
            _ExtentX        =   2752
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4DC0
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4E2C
            Key             =   "AIM_IntermittentAnalysis.frx":4E4A
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtNI_ISS_Units 
            Height          =   345
            Left            =   9990
            TabIndex        =   100
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1440
            _Version        =   65536
            _ExtentX        =   2540
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4E8E
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4EFA
            Key             =   "AIM_IntermittentAnalysis.frx":4F18
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtNI_PctChg 
            Height          =   345
            Left            =   13110
            TabIndex        =   102
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1760
            _Version        =   65536
            _ExtentX        =   3104
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":4F5C
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":4FC8
            Key             =   "AIM_IntermittentAnalysis.frx":4FE6
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtNI_ISS_Cost 
            Height          =   345
            Left            =   11490
            TabIndex        =   101
            TabStop         =   0   'False
            Top             =   1080
            Width           =   1560
            _Version        =   65536
            _ExtentX        =   2752
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":502A
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":5096
            Key             =   "AIM_IntermittentAnalysis.frx":50B4
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtII_NSS_Units 
            Height          =   345
            Left            =   6870
            TabIndex        =   103
            TabStop         =   0   'False
            Top             =   1470
            Width           =   1440
            _Version        =   65536
            _ExtentX        =   2540
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":50F8
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":5164
            Key             =   "AIM_IntermittentAnalysis.frx":5182
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtII_NSS_Cost 
            Height          =   345
            Left            =   8370
            TabIndex        =   104
            TabStop         =   0   'False
            Top             =   1470
            Width           =   1560
            _Version        =   65536
            _ExtentX        =   2752
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":51C6
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":5232
            Key             =   "AIM_IntermittentAnalysis.frx":5250
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtII_ISS_Units 
            Height          =   345
            Left            =   9990
            TabIndex        =   105
            TabStop         =   0   'False
            Top             =   1470
            Width           =   1440
            _Version        =   65536
            _ExtentX        =   2540
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":5294
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":5300
            Key             =   "AIM_IntermittentAnalysis.frx":531E
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtII_PctChg 
            Height          =   345
            Left            =   13110
            TabIndex        =   107
            TabStop         =   0   'False
            Top             =   1470
            Width           =   1760
            _Version        =   65536
            _ExtentX        =   3104
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":5362
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":53CE
            Key             =   "AIM_IntermittentAnalysis.frx":53EC
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtII_ISS_Cost 
            Height          =   345
            Left            =   11490
            TabIndex        =   106
            TabStop         =   0   'False
            Top             =   1470
            Width           =   1560
            _Version        =   65536
            _ExtentX        =   2752
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":5430
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":549C
            Key             =   "AIM_IntermittentAnalysis.frx":54BA
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtTot_NSS_Units 
            Height          =   345
            Left            =   6870
            TabIndex        =   108
            TabStop         =   0   'False
            Top             =   1845
            Width           =   1440
            _Version        =   65536
            _ExtentX        =   2540
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":54FE
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":556A
            Key             =   "AIM_IntermittentAnalysis.frx":5588
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtTot_NSS_Cost 
            Height          =   345
            Left            =   8370
            TabIndex        =   109
            TabStop         =   0   'False
            Top             =   1845
            Width           =   1560
            _Version        =   65536
            _ExtentX        =   2752
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":55CC
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":5638
            Key             =   "AIM_IntermittentAnalysis.frx":5656
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtTot_ISS_Units 
            Height          =   345
            Left            =   9990
            TabIndex        =   110
            TabStop         =   0   'False
            Top             =   1845
            Width           =   1440
            _Version        =   65536
            _ExtentX        =   2540
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":569A
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":5706
            Key             =   "AIM_IntermittentAnalysis.frx":5724
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtTot_PctChg 
            Height          =   345
            Left            =   13110
            TabIndex        =   112
            TabStop         =   0   'False
            Top             =   1845
            Width           =   1760
            _Version        =   65536
            _ExtentX        =   3104
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":5768
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":57D4
            Key             =   "AIM_IntermittentAnalysis.frx":57F2
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0.0%"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtTot_ISS_Cost 
            Height          =   345
            Left            =   11490
            TabIndex        =   111
            TabStop         =   0   'False
            Top             =   1845
            Width           =   1560
            _Version        =   65536
            _ExtentX        =   2752
            _ExtentY        =   609
            Caption         =   "AIM_IntermittentAnalysis.frx":5836
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_IntermittentAnalysis.frx":58A2
            Key             =   "AIM_IntermittentAnalysis.frx":58C0
            BackColor       =   12648447
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   -1
            ShowContextMenu =   -1
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   1
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "0"
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin VB.Label label 
            Caption         =   "Items Tested"
            Height          =   300
            Index           =   24
            Left            =   210
            TabIndex        =   128
            Top             =   300
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Weeks Tested"
            Height          =   300
            Index           =   0
            Left            =   210
            TabIndex        =   127
            Top             =   1125
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Safety Stock Relevance %"
            Height          =   300
            Index           =   1
            Left            =   210
            TabIndex        =   126
            Top             =   1545
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Percent Zero Count"
            Height          =   300
            Index           =   8
            Left            =   210
            TabIndex        =   125
            Top             =   720
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Elapsed Time (mm:ss)"
            Height          =   300
            Index           =   22
            Left            =   210
            TabIndex        =   124
            Top             =   1980
            Width           =   2730
         End
         Begin VB.Label label 
            Caption         =   "Normal Items"
            Height          =   300
            Index           =   15
            Left            =   5000
            TabIndex        =   123
            Top             =   1125
            Width           =   1800
         End
         Begin VB.Label label 
            Caption         =   "Total"
            Height          =   300
            Index           =   16
            Left            =   5000
            TabIndex        =   122
            Top             =   1895
            Width           =   1800
         End
         Begin VB.Label label 
            Caption         =   "Intermittent Items"
            Height          =   300
            Index           =   20
            Left            =   5000
            TabIndex        =   121
            Top             =   1510
            Width           =   1800
         End
         Begin VB.Label Label18 
            Alignment       =   2  'Center
            Caption         =   "Inventory Cost"
            Height          =   345
            Left            =   8370
            TabIndex        =   120
            Top             =   660
            Width           =   1560
         End
         Begin VB.Label Label20 
            Alignment       =   2  'Center
            Caption         =   "Inventory Cost"
            Height          =   345
            Left            =   11490
            TabIndex        =   119
            Top             =   660
            Width           =   1560
         End
         Begin VB.Label Label22 
            Alignment       =   2  'Center
            Caption         =   "Average Inventory - Normal Safety Stock"
            Height          =   465
            Left            =   6870
            TabIndex        =   118
            Top             =   120
            Width           =   3060
         End
         Begin VB.Label Label23 
            Alignment       =   2  'Center
            Caption         =   "Average Inventory - Intermittent Safety Stock"
            Height          =   465
            Left            =   9990
            TabIndex        =   117
            Top             =   120
            Width           =   3060
         End
         Begin VB.Label Label21 
            Alignment       =   2  'Center
            Caption         =   "Change Percentage"
            Height          =   345
            Left            =   13110
            TabIndex        =   116
            Top             =   660
            Width           =   1760
         End
         Begin VB.Label Label19 
            Alignment       =   2  'Center
            Caption         =   "Units"
            Height          =   345
            Left            =   9990
            TabIndex        =   115
            Top             =   660
            Width           =   1440
         End
         Begin VB.Label Label15 
            Alignment       =   2  'Center
            Caption         =   "Units"
            Height          =   345
            Left            =   6870
            TabIndex        =   113
            Top             =   660
            Width           =   1440
         End
         Begin VB.Line Line1 
            Index           =   0
            X1              =   6840
            X2              =   13080
            Y1              =   75
            Y2              =   75
         End
         Begin VB.Line Line1 
            Index           =   1
            X1              =   6840
            X2              =   14880
            Y1              =   620
            Y2              =   620
         End
         Begin VB.Line Line1 
            Index           =   2
            X1              =   4875
            X2              =   14895
            Y1              =   1035
            Y2              =   1035
         End
         Begin VB.Line Line1 
            Index           =   3
            X1              =   4875
            X2              =   14895
            Y1              =   2235
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   4
            X1              =   9950
            X2              =   9950
            Y1              =   75
            Y2              =   2230
         End
         Begin VB.Line Line1 
            Index           =   5
            X1              =   13080
            X2              =   13080
            Y1              =   75
            Y2              =   2230
         End
         Begin VB.Line Line1 
            Index           =   6
            X1              =   8330
            X2              =   8330
            Y1              =   620
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   7
            X1              =   11455
            X2              =   11455
            Y1              =   620
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   8
            X1              =   6840
            X2              =   6840
            Y1              =   75
            Y2              =   2230
         End
         Begin VB.Line Line1 
            Index           =   9
            X1              =   14880
            X2              =   14880
            Y1              =   620
            Y2              =   2235
         End
         Begin VB.Line Line1 
            Index           =   10
            X1              =   4875
            X2              =   4875
            Y1              =   1035
            Y2              =   2230
         End
      End
   End
End
Attribute VB_Name = "AIM_IntermittentAnalysis"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Type DmdRcd
    LcId As String
    Item As String
    ItStat As String
    ActDate As Date
    FcstCycles As Long
    ZeroCount As Integer
    FcstDemand As Double
    MAE As Double
    DSer As Double
    Accum_Lt As Long
    ReviewTime As Long
    OrderQty As Long
    Cost As Double
    HisYear As Long
    Cps(1 To 156) As Double
End Type

Private Type ResultsRcd
    FileName As String
    PctZeroMin As Double
    PctZeroMax As Double
    SrvLvlGoal As Double
    LookBackPds As Long
    ItemCount As Long
    PctZeroCount As Integer
    NbrWeeks As Long
    SafetyStockOK As Long
End Type

Dim Cn As ADODB.Connection
Dim DemandInfo As DmdRcd
Dim Results() As ResultsRcd

Dim ResultsCount As Long

Function CalcAdjNZMean(PctZero As Double, Mean As Double, StdDev As Double, N As Long)
On Error GoTo ErrorHandler
    
    'As the number of periods increase the possible values for demand in a given period are
    'a function of N. For example, for a mean of 10 and N = 2 the possible non-zero values of demand
    'are 10, 20. For a mean of 10 and N = 3, the possible values are 10, 20, and 30.
    
    'See page 172 of "Statistical Analysis: A Decision Making Approach" by Robert Parsons
    
    Dim dDemand As Double
    Dim dTotalDemand As Double
    Dim i As Long, J As Long
    Dim NextCoeff As Long
    Dim PctNZ As Double
    
    'Calculate the Percentage of Non Zero Items
    PctNZ = (1 - PctZero)

    'Sum the terms of the Binomial Expansion
    'Terms = N + 1; howerver since first term
    'is ignored
    For i = 1 To N
        'Calculate Value for each term
        Select Case i
            Case 1              'Term 1
                dDemand = N * PctZero ^ (N - i) * PctNZ ^ i
                NextCoeff = (N - i) * N / (i + 1)
            
            Case Else           'All Other Terms
                dDemand = NextCoeff * PctZero ^ (N - i) * PctNZ ^ i
                NextCoeff = NextCoeff * (N - i) / (i + 1)
        
        End Select
        
        dTotalDemand = dTotalDemand + (dDemand * Mean * i) / (1 - PctZero ^ N)
    Next i

    CalcAdjNZMean = dTotalDemand

Exit Function
ErrorHandler:
    Screen.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source & "(CalcAdjNZMean)", Err.Description
    f_HandleErr , , , "AIM_IntermittentAnalysis::CalcAdjNZMean", Now, gDRGeneralError, True, Err
End Function

Private Function LeadTimeSimulation(LtWks As Long, PctZero As Double, Mean As Double, _
    StdDev As Double, SrvLvlGoal As Double, NbrLoops As Long, ModelMean As Double, _
    ModelStdDev As Double, NZ_ModelMean As Double, NZ_ModelStdDev As Double, RsltsDist() As Long)
On Error GoTo ErrorHandler

    Dim AccumCount As Long
    Dim AdjNbrLoops As Long
    Dim i, J As Long
    Dim dDemand As Double
    Dim Interval As Double
    Dim LastAccumPct As Double
    Dim MaxDemand As Double
    Dim NonZeroCount As Integer
    Dim NZ_SumX As Double
    Dim NZ_SumSq As Double
    Dim p1, p2 As Double
    Dim SumX As Double
    Dim SumSq As Double
    Dim ZeroCount As Integer
    
    'Clear the Results Array
    Erase RsltsDist
    
    'Adjust Number of Loops
    AdjNbrLoops = NbrLoops / LtWks
    
    'Calculate the interval for the rsltsdist array
    Interval = ((Mean + (3.5 * StdDev)) * LtWks) / (UBound(RsltsDist) - LBound(RsltsDist))
    
    'Seed the Random number generator
    Randomize (CSng(Format(Now, "hmmssyyyymmdd")))
    
    NonZeroCount = 0
    
    For i = 1 To AdjNbrLoops
        'Initialize Demand for LTWks periods
        dDemand = 0
        ZeroCount = 0
        
        For J = 1 To LtWks
            'Determine if this data point is zero or non zero
            p1 = Round(Rnd, 3)
            
            If p1 < PctZero Then
                ZeroCount = ZeroCount + 1
                'dDemand = dDemand + 0
            Else
                p2 = Round(Rnd, 3)
                dDemand = dDemand + (Mean + (IIf(p2 > 0.5, SafetyFactor(p2), -1 * SafetyFactor(1 - p2)) * StdDev))
            End If
        Next J
        
        'Update Standard Deviation Calculation
        SumX = SumX + dDemand
        SumSq = SumSq + (dDemand ^ 2)
        
        'Update Non-Zero Standard Deviation
        If ZeroCount = LtWks Then
            J = 0
        
        Else
            NonZeroCount = NonZeroCount + 1
        
            NZ_SumX = NZ_SumX + dDemand
            NZ_SumSq = NZ_SumSq + (dDemand ^ 2)
            
            J = Int(dDemand / Interval) + 1
            J = IIf(J <= UBound(RsltsDist), J, UBound(RsltsDist))   'Check for high pointer
            J = IIf(J >= 1, J, 1)                                   'Check for low pointer
        
        End If
         
        'Update results array
        RsltsDist(J) = RsltsDist(J) + 1
        
    Next i
    
    'Determine the Maximum Demand at the specified Service Level
    AccumCount = 0
    LastAccumPct = 0
    
    For i = (LBound(RsltsDist) + 1) To UBound(RsltsDist)
        AccumCount = AccumCount + RsltsDist(i)
        If (AccumCount / NonZeroCount) >= SrvLvlGoal Then
            MaxDemand = ((i - 1) + ((SrvLvlGoal - LastAccumPct) _
                / ((AccumCount / NonZeroCount) - LastAccumPct))) * Interval
            Exit For
        End If
        
        'Save Accumulative Percentage for this loop
        LastAccumPct = AccumCount / NonZeroCount
    Next i
    
    'Calculate the Model Standard Deviation
    If AdjNbrLoops > 1 Then
        ModelMean = SumX / AdjNbrLoops
        ModelStdDev = Sqr((SumSq - ((SumX ^ 2) / AdjNbrLoops)) / (AdjNbrLoops - 1))
    End If
    
    If NonZeroCount > 1 Then
        NZ_ModelMean = NZ_SumX / NonZeroCount
        NZ_ModelStdDev = Sqr((NZ_SumSq - ((NZ_SumX ^ 2) / NonZeroCount)) / (NonZeroCount - 1))
    End If
    
    LeadTimeSimulation = MaxDemand
    
Exit Function
ErrorHandler:
    Screen.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source & "(LeadTimeSimulation)", Err.Description
    f_HandleErr , , , "AIM_IntermittentAnalysis::LeadTimeSimulation", Now, gDRGeneralError, True, Err
End Function

Public Function IntermittentAnalysis(PctZeroMin As Double, PctZeroMax As Double, _
    FileName As String, LookBackPds As Long, SrvLvlGoal As Double)
On Error GoTo ErrorHandler
    
    Dim Cost As Double
    Dim i As Long
    Dim InArray As Variant
    Dim MaxInArray As Long
    
    Dim InFile As Long
    Dim InRecord As String
    
    Dim LtWks As Long
    Dim PctZeroCount As Integer
    Dim Pd As Long
    Dim RcdCount As Long
    Dim RtnCode As Long
    
    Dim MaxDemand As Double
    Dim ModelMaxDemand As Double
    Dim NbrTested As Long
    Dim SafetyStockOK As Long
    
    Dim TotalTested As Long
    Dim TotalOK As Long
    Dim Units As Double
    
    'Define Totals
    Dim NI_NSS_Units As Double
    Dim II_NSS_Units As Double
    Dim Tot_NSS_Units As Double
    
    Dim NI_NSS_Cost As Double
    Dim II_NSS_Cost As Double
    Dim Tot_NSS_Cost As Double
    
    Dim NI_ISS_Units As Double
    Dim II_ISS_Units As Double
    Dim Tot_ISS_Units As Double
    
    Dim NI_ISS_Cost As Double
    Dim II_ISS_Cost As Double
    Dim Tot_ISS_Cost As Double
    
    Dim NI_PctChg As Double
    Dim II_PctChg As Double
    Dim Tot_PctChg As Double
    
    Dim NormalSafetyStock As Double
    Dim IntSafetyStock As Double
    
    Dim ModelStdDev As Double
    Dim ModelMean As Double
    
    Dim PctZero As Double
     
    Dim NZ_StdDev As Double
    Dim NZ_Mean As Double
    Dim NZ_ModelMean As Double
    Dim NZ_ModelStdDev As Double
    
    Dim ElapsedSeconds As Long
    Dim StartTime As Date
    Dim strMessage As String
    Dim strText As String
    
    Dim Rslts(0 To 49) As Long
    
    'Start the Timer
    StartTime = Now
    
    'Open the Intermittent Analysis File
    InFile = FreeFile
    
    On Error Resume Next
    Open FileName For Input Access Read Lock Read Write As #InFile Len = 30000
    If Err.Number <> 0 Then
        strMessage = getTranslationResource("MSGBOX01700")
        strText = getTranslationResource(Me.Caption)
        If StrComp(strMessage, "MSGBOX01700") = 0 Then strMessage = "Error opening Intermittent Analysis Input File (fs "
        MsgBox strMessage & FileName & ").", vbCritical + vbOKOnly, strText
        Exit Function
    End If
    On Error GoTo ErrorHandler
    Do Until eof(InFile)
        'la here
        DoEvents
        'Read a record
        Input #InFile, InRecord
        InArray = Split(InRecord, vbTab)
        MaxInArray = UBound(InArray)
        'Parse the record
        DemandInfo.LcId = InArray(0)
        DemandInfo.Item = InArray(1)
        DemandInfo.ItStat = InArray(2)
        DemandInfo.ActDate = InArray(3)
        DemandInfo.FcstCycles = InArray(4)
        DemandInfo.ZeroCount = InArray(5)
        DemandInfo.FcstDemand = InArray(6)
        DemandInfo.MAE = InArray(7)
        DemandInfo.DSer = InArray(8)
        DemandInfo.Accum_Lt = InArray(9)
        DemandInfo.ReviewTime = InArray(10)
        DemandInfo.OrderQty = InArray(11)
        DemandInfo.Cost = InArray(12)
        DemandInfo.HisYear = InArray(13)
        
        For i = LBound(DemandInfo.Cps) To UBound(DemandInfo.Cps)
            If MaxInArray < i + 13 Then
                Exit For
            End If
            DemandInfo.Cps(i) = InArray(i + 13)
        Next i
    
        'Perform the Intermittent Analysis
        TestIntermittentDemand DemandInfo.Cps(), PctZeroMin, PctZeroMax, LookBackPds, SrvLvlGoal, _
            NbrTested, SafetyStockOK, NZ_Mean, NZ_StdDev, MaxDemand, PctZero
        
        'Test for change in safety stock
        If NbrTested > 0 Then
            TotalTested = TotalTested + NbrTested
            TotalOK = TotalOK + SafetyStockOK
            PctZeroCount = IIf(NbrTested > 0, PctZeroCount + 1, PctZeroCount)
            
            If Me.ckInventoryImpact = vbChecked Then
                'Calculate traditional safetystock
                NormalSafetyStock = CalcSafetyStock((DemandInfo.FcstDemand * (DemandInfo.Accum_Lt + DemandInfo.ReviewTime) / 7), _
                                                    DemandInfo.FcstDemand, _
                                                    DemandInfo.MAE, _
                                                    DemandInfo.Accum_Lt + DemandInfo.ReviewTime, _
                                                    DemandInfo.DSer, _
                                                    0.5)
                
                'Calculate the maximum demand from the Model
                ModelMaxDemand = GetMaxDemand(DemandInfo.Accum_Lt + DemandInfo.ReviewTime, _
                                            PctZero, _
                                            NZ_Mean, _
                                            NZ_StdDev, _
                                            SrvLvlGoal, _
                                            1000)
                
                'Calculate the Intermittent Safety Stock
                IntSafetyStock = ModelMaxDemand - ((DemandInfo.Accum_Lt + DemandInfo.ReviewTime) / 7) * DemandInfo.FcstDemand
                    
                'Calculate Average Inventory using Normal Safety Stock
                Units = 0.5 * ((DemandInfo.FcstDemand * DemandInfo.ReviewTime / 7) + DemandInfo.OrderQty) + NormalSafetyStock
                Cost = Units * DemandInfo.Cost
                
                II_NSS_Units = II_NSS_Units + Units
                II_NSS_Cost = II_NSS_Cost + Cost
                
                'Calculate Average Inventory using Intermittent Safety Stock
                Units = 0.5 * ((ModelMean * DemandInfo.ReviewTime / 7) + DemandInfo.OrderQty) + IntSafetyStock
                Cost = Units * DemandInfo.Cost
                
                II_ISS_Units = II_ISS_Units + Units
                II_ISS_Cost = II_ISS_Cost + Cost
            End If
        Else
            If Me.ckInventoryImpact = vbChecked Then
                'Update Normal Item Totals
                NormalSafetyStock = CalcSafetyStock((DemandInfo.FcstDemand * (DemandInfo.Accum_Lt + DemandInfo.ReviewTime) / 7), _
                                                    DemandInfo.FcstDemand, _
                                                    DemandInfo.MAE, _
                                                    DemandInfo.Accum_Lt + DemandInfo.ReviewTime, _
                                                    DemandInfo.DSer, _
                                                    0.5)
                    
                Units = 0.5 * ((DemandInfo.FcstDemand * DemandInfo.ReviewTime / 7) + DemandInfo.OrderQty) + NormalSafetyStock
                Cost = Units * DemandInfo.Cost
                
                NI_NSS_Units = NI_NSS_Units + Units
                NI_NSS_Cost = NI_NSS_Cost + Cost
            
                NI_ISS_Units = NI_NSS_Units
                NI_ISS_Cost = NI_NSS_Cost
            End If
        End If
        
        If Me.ckInventoryImpact = vbChecked Then
            'Update Totals
            Tot_NSS_Units = NI_NSS_Units + II_NSS_Units
            Tot_NSS_Cost = NI_NSS_Cost + II_NSS_Cost
            
            Tot_ISS_Units = NI_ISS_Units + II_ISS_Units
            Tot_ISS_Cost = NI_ISS_Cost + II_ISS_Cost
        End If
        
        'Update Percentage Change
        RcdCount = RcdCount + 1

        'Update Counters
        If RcdCount Mod 100 = 0 Then
            Me.txtNbrItems.Text = Format(RcdCount, "#,##0")
            Me.txtPctZeroCount.Text = Format(PctZeroCount, "#,##0")
            Me.txtNbrTested.Text = Format(TotalTested, "#,##0")
            Me.txtSafetyStockOK.Text = Format(TotalOK / TotalTested, "0.0%")
            
            If Me.ckInventoryImpact = vbChecked Then
                 Me.txtNI_NSS_Units.Text = Format(NI_NSS_Units, "#,##0")
                 Me.txtII_NSS_Units.Text = Format(II_NSS_Units, "#,##0")
                 Me.txtTot_NSS_Units.Text = Format(Tot_NSS_Units, "#,##0")
                 
                 Me.txtNI_NSS_Cost.Text = Format(NI_NSS_Cost, "#,##0.00")
                 Me.txtII_NSS_Cost.Text = Format(II_NSS_Cost, "#,##0.00")
                 Me.txtTot_NSS_Cost.Text = Format(Tot_NSS_Cost, "#,##0.00")
                 
                 Me.txtNI_ISS_Units.Text = Format(NI_ISS_Units, "#,##0")
                 Me.txtII_ISS_Units.Text = Format(II_ISS_Units, "#,##0")
                 Me.txtTot_ISS_Units.Text = Format(Tot_ISS_Units, "#,##0")
                 
                 Me.txtNI_ISS_Cost.Text = Format(NI_ISS_Cost, "#,##0.00")
                 Me.txtII_ISS_Cost.Text = Format(II_ISS_Cost, "#,##0.00")
                 Me.txtTot_ISS_Cost.Text = Format(Tot_ISS_Cost, "#,##0.00")
                
                 If NI_NSS_Cost > 0 Then
                     Me.txtNI_PctChg.Text = Format((NI_ISS_Cost - NI_NSS_Cost) / NI_NSS_Cost, "0.00%")
                 Else
                     Me.txtNI_PctChg.Text = Format(0, "0.00%")
                 End If
                     
                 If II_NSS_Cost > 0 Then
                     Me.txtII_PctChg.Text = Format((II_ISS_Cost - II_NSS_Cost) / II_NSS_Cost, "0.00%")
                 Else
                     Me.txtII_PctChg.Text = Format(0, "0.00%")
                 End If
                 
                 If Tot_NSS_Cost > 0 Then
                     Me.txtTot_PctChg.Text = Format((Tot_ISS_Cost - Tot_NSS_Cost) / Tot_NSS_Cost, "0.00%")
                 Else
                     Me.txtTot_PctChg.Text = Format(0, "0.00%")
                 End If
            End If
            
            ElapsedSeconds = DateDiff("s", StartTime, Now)
            
            Me.txtMinutes.Text = Format((ElapsedSeconds \ 60), "#,#00") + ":" _
                + Format(ElapsedSeconds - (60 * (ElapsedSeconds \ 60)), "00")
    
            Me.Refresh
        End If
    Loop
    
    'Update the results array
    Results(ResultsCount).FileName = FileName
    Results(ResultsCount).PctZeroMin = PctZeroMin
    Results(ResultsCount).PctZeroMax = PctZeroMax
    Results(ResultsCount).SrvLvlGoal = SrvLvlGoal
    Results(ResultsCount).LookBackPds = LookBackPds
    Results(ResultsCount).ItemCount = RcdCount
    Results(ResultsCount).PctZeroCount = PctZeroCount
    Results(ResultsCount).NbrWeeks = TotalTested
    Results(ResultsCount).SafetyStockOK = TotalOK
    
    'Increment Results Counter
    ResultsCount = ResultsCount + 1
    
    If ResultsCount > UBound(Results) Then
        ReDim Preserve Results(UBound(Results) + 10)
    End If
    
    Me.dgResults.ReBind
        
    'Windup
    Close #InFile
    
    IntermittentAnalysis = RcdCount

Exit Function
ErrorHandler:
    Screen.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source & "(IntermittentAnalysis)", Err.Description
     f_HandleErr , , , "AIM_IntermittentAnalysis::IntermittentAnalysis", Now, gDRGeneralError, True, Err
End Function

Private Function TestIntermittentDemand(p_Demand() As Double, PctZeroMin As Double, PctZeroMax As Double, _
    LookBackPds As Long, SrvLvlGoal As Double, NbrTested As Long, SafetyStockOK As Long, _
    Mean As Double, StdDev As Double, MaxDemand As Double, PctZero As Double)
On Error GoTo ErrorHandler
    
    Dim Pd As Long
    Dim TestPd As Long
    Dim ZeroCount As Integer
    
    'Initialize Statistics
    NbrTested = 0
    SafetyStockOK = 0
    
    'Calculate the Percent Zero during the test period
    For TestPd = 52 To 1 Step -1
        'Determine Non-Zero Mean, Standard Deviation, and Zero Count
        StdDev = NZ_StdDev(p_Demand(), TestPd + 1, TestPd + LookBackPds, Mean, ZeroCount)
        
        'Calculate the Percentage Zero
        PctZero = ZeroCount / LookBackPds
        
        'Test the week if the following conditions are meet:
        ' - The Percentage Zero for the item falls between the Minimum and Maximum
        ' - Their was demand for the item during the period
        If PctZero >= PctZeroMin And PctZero <= PctZeroMax And p_Demand(TestPd) > 0 Then
            NbrTested = NbrTested + 1
            'Calculate the Expected Maximum Demand during the period
            MaxDemand = Mean + (SafetyFactor(SrvLvlGoal) * StdDev)
            
            'Check to see if demand exceeeds the Calculated Expected Maximum Demand
            If p_Demand(TestPd) <= MaxDemand Then
                SafetyStockOK = SafetyStockOK + 1
            End If
        End If
    Next TestPd
    
    'Get final mean and standard deviation
    StdDev = NZ_StdDev(p_Demand(), 1, 52, Mean, ZeroCount)

Exit Function
ErrorHandler:
    Screen.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source & "(TestIntermittentDemand)", Err.Description
    f_HandleErr , , , "AIM_IntermittentAnalysis::TestIntermittentDemand", Now, gDRGeneralError, True, Err
End Function

Private Sub atAnalysisOptions_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    If StrComp(NewTab.Caption, atAnalysisOptions.Tabs(2).Caption, vbBinaryCompare) = 0 Then
        If cmdCreate.Enabled = False Then
            Me.MousePointer = vbHourglass
        Else
            Me.MousePointer = vbNormal
        End If
    Else
        Me.MousePointer = vbNormal
    End If
        
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(atAnalysisOptions_BeforeTabClick)"
    f_HandleErr , , , "AIM_IntermittentAnalysis::atAnalysisOptions_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub atIntermittentDemand_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    If StrComp(NewTab.Caption, atIntermittentDemand.Tabs(1).Caption, vbBinaryCompare) = 0 Then
        If tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = False Then
            Me.MousePointer = vbHourglass
        Else
            Me.MousePointer = vbNormal
        End If
    Else
        Me.MousePointer = vbNormal
    End If
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(atIntermittentDemand_BeforeTabClick)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::atIntermittentDemand_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbIntermittentDemand_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
            
    Dim FileName As String
    Dim PctZeroMin As Double
    Dim PctZeroMax As Double
    Dim SrvLvlGoal As Double
    Dim LookBackPds As Long
    Dim strMessage As String
    
    Select Case Tool.ID
        Case "ID_RunAnalysis"
            AIM_IntermittentAnalysis.MousePointer = vbHourglass
            
            strMessage = getTranslationResource("STATMSG01700")
            If StrComp(strMessage, "STATMSG01700") = 0 Then strMessage = "Executing Intermittent Analysis..."
            Write_Message strMessage
            
            'Set parameters
            FileName = Me.txtInFileName.Text
            PctZeroMin = Me.txtPctZeroMin.Value
            PctZeroMax = Me.txtPctZeroMax.Value
            SrvLvlGoal = Me.txtSrvLvlGoal.Value
            LookBackPds = Me.txtLookBackPds.Value
            
            'Clear display
            Me.txtNbrItems.Text = Format(0, "#,##0")
            Me.txtPctZeroCount.Text = Format(0, "#,##0")
            Me.txtNbrTested.Text = Format(0, "#,##0")
            Me.txtSafetyStockOK.Text = Format(0, "0.0%")
            
            Me.txtNI_NSS_Units.Text = Format(0, "#,##0")
            Me.txtII_NSS_Units.Text = Format(0, "#,##0")
            Me.txtTot_NSS_Units.Text = Format(0, "#,##0")
            
            Me.txtNI_NSS_Cost.Text = Format(0, "#,##0.00")
            Me.txtII_NSS_Cost.Text = Format(0, "#,##0.00")
            Me.txtTot_NSS_Cost.Text = Format(0, "#,##0.00")
            
            Me.txtNI_ISS_Units.Text = Format(0, "#,##0")
            Me.txtII_ISS_Units.Text = Format(0, "#,##0")
            Me.txtTot_ISS_Units.Text = Format(0, "#,##0")
            
            Me.txtNI_ISS_Cost.Text = Format(0, "#,##0.00")
            Me.txtII_ISS_Cost.Text = Format(0, "#,##0.00")
            Me.txtTot_ISS_Cost.Text = Format(0, "#,##0.00")
            
            Me.txtNI_PctChg.Text = Format(0, "0.00%")
            Me.txtII_PctChg.Text = Format(0, "0.00%")
            Me.txtTot_PctChg.Text = Format(0, "0.00%")
            
            Me.txtMinutes = "00:00"
            
            Me.Refresh
            
            'Run Analysis...
            'WAIT!!
            'Since this function calls DoEvents, Disable the Tool until this function returns
            tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = False
            tbIntermittentDemand.Tools("ID_Close").Enabled = False
            cmdCreate.Enabled = False
                'Now, Run analysis
                IntermittentAnalysis PctZeroMin, PctZeroMax, FileName, LookBackPds, SrvLvlGoal
            'Don't forget to re-enable the tool before exiting
            tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = True
            tbIntermittentDemand.Tools("ID_Close").Enabled = True
            cmdCreate.Enabled = True
            'Wrap Up
            Write_Message ""
            Me.MousePointer = vbNormal
        
        Case "ID_Close"
            Unload Me
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbIntermittentDemand_ToolClick)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::tbIntermittentDemand_ToolClick", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
    'Don't forget to re-enable the tool before exiting
    tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = True
    tbIntermittentDemand.Tools("ID_Close").Enabled = True
    cmdCreate.Enabled = True
    Write_Message ""
End Sub

Private Sub cmdCreate_Click()
On Error GoTo ErrorHandler

    Dim AIM_GetDemand_Sp As ADODB.Command
    
    Dim rsGetDemand As ADODB.Recordset
    
    Dim DemandSource As String
    Dim EndPeriod As Integer
    Dim EndYear As Integer
    Dim FileName As String
    Dim f_FileIndex As Long
    Dim LcId As String
    
    Dim FirstTime As Boolean
    Dim HsYear As Integer
    Dim HsPd As Integer
    Dim i As Long
    Dim OutRcd As String
    Dim Pd As Long
    Dim PrevLcId As String
    Dim PrevItem As String
    Dim RcdCount As Long
    Dim RtnCode As Long
    Dim StartYear As Long
    Dim StartPeriod As Long
    Dim strMessage As String
    Dim strText As String
    
    'Housekeeping
    Me.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01701")
    If StrComp(strMessage, "STATMSG01701") = 0 Then strMessage = "Creating Intermittent Demand Analysis File..."
    Write_Message strMessage
    
    'Set parameters
    If StrComp(Me.dcAIMLocations.Text, getTranslationResource("All")) = 0 Then
        LcId = "All"    'Untranslate
    Else
        LcId = Me.dcAIMLocations.Text
    End If
    
    EndYear = Me.txtEndYear.Value
    EndPeriod = Me.txtEndPeriod.Value
    DemandSource = Me.dcDemandSource.Text
    FileName = Me.txtFileName.Text
    
    'Initialize Control Variables
    FirstTime = True
    PrevLcId = ""
    PrevItem = ""
    
    Me.txtRcdCount.Text = "0"
    
    'Set up the Stored Procedures and Cursors
    Set AIM_GetDemand_Sp = New ADODB.Command
    With AIM_GetDemand_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_GetDemand_Sp"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    
    Set rsGetDemand = New ADODB.Recordset
    With rsGetDemand
        .CursorLocation = adUseServer
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
        .CacheSize = 1000
    End With
    
    'Build the Demand Update Control Cursor
    AIM_GetDemand_Sp("@SelLcId").Value = LcId
    AIM_GetDemand_Sp("@FcstYear").Value = EndYear
    AIM_GetDemand_Sp("@FcstPeriod").Value = EndPeriod
    AIM_GetDemand_Sp("@DemandSource").Value = DemandSource

    rsGetDemand.Open AIM_GetDemand_Sp

    'Open the Output File
    f_FileIndex = FreeFile
    
    On Error Resume Next
    Open FileName For Output Lock Read Write As f_FileIndex
    If Err.Number <> 0 Then
        strMessage = getTranslationResource("MSGBOX01702")
        strText = getTranslationResource(Me.Caption)
        If StrComp(strMessage, "MSGBOX01702") = 0 Then strMessage = "Error opening output file."
        MsgBox strMessage & vbCrLf & vbCrLf & Err.Description, vbCritical + vbOKOnly, strText
        Exit Sub
    End If
    On Error GoTo ErrorHandler
    
    If f_IsRecordsetValidAndOpen(rsGetDemand) Then
        'Disable this button to prevent this being re-executed after the DoEvents
        cmdCreate.Enabled = False
        tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = False
        tbIntermittentDemand.Tools("ID_Close").Enabled = False

        Do Until rsGetDemand.eof
            DoEvents
            
            'Change in Location/Item
            If (rsGetDemand("LcId").Value <> PrevLcId _
                Or rsGetDemand("Item").Value <> PrevItem) Then
                
                If Not FirstTime Then
                    RcdCount = RcdCount + 1
                    If RcdCount = 1829 Then
                        Debug.Print "Problem zone next"
                    End If
                    
                    Me.txtRcdCount.Text = Format(RcdCount, "#,##0")
                    Me.txtRcdCount.Refresh
                
                    'Write a record to the Analysis File
                    OutRcd = DemandInfo.LcId & vbTab _
                        & DemandInfo.Item & vbTab _
                        & DemandInfo.ItStat & vbTab _
                        & FormatDateTime(DemandInfo.ActDate, vbShortDate) & vbTab _
                        & Format(DemandInfo.FcstCycles, "0") & vbTab _
                        & Format(DemandInfo.ZeroCount, "0") & vbTab _
                        & Format(DemandInfo.FcstDemand, "0.00") & vbTab _
                        & Format(DemandInfo.MAE, "0.00") & vbTab _
                        & Format(DemandInfo.DSer, "0.00") & vbTab _
                        & Format(DemandInfo.Accum_Lt, "0") & vbTab _
                        & Format(DemandInfo.ReviewTime, "0") & vbTab _
                        & Format(DemandInfo.OrderQty, "0") & vbTab _
                        & Format(DemandInfo.Cost, "0.0000") & vbTab _
                        & Format(DemandInfo.HisYear, "0") & vbTab
                        
                    For i = LBound(DemandInfo.Cps) To UBound(DemandInfo.Cps) - 1
                        OutRcd = OutRcd & Format(DemandInfo.Cps(i), "0") & vbTab
                    Next i
                    
                    OutRcd = OutRcd & Format(DemandInfo.Cps(UBound(DemandInfo.Cps)), "0")
                      
                    Print #f_FileIndex, OutRcd
                End If
                
                'Update the Simulation Record -- Item Data
                DemandInfo.LcId = rsGetDemand("LcId").Value
                DemandInfo.Item = rsGetDemand("Item").Value
                DemandInfo.ItStat = rsGetDemand("ItStat").Value
                DemandInfo.ActDate = rsGetDemand("ActDate").Value
                DemandInfo.FcstCycles = rsGetDemand("fcstcycles").Value
                DemandInfo.ZeroCount = rsGetDemand("zerocount").Value
                DemandInfo.FcstDemand = rsGetDemand("FcstDemand").Value
                DemandInfo.MAE = rsGetDemand("MAE").Value
                DemandInfo.DSer = rsGetDemand("DSer").Value
                DemandInfo.Accum_Lt = rsGetDemand("Accum_LT").Value
                DemandInfo.ReviewTime = rsGetDemand("ReviewTime").Value
                DemandInfo.OrderQty = rsGetDemand("OrderQty").Value
                DemandInfo.Cost = rsGetDemand("Cost").Value
                DemandInfo.HisYear = rsGetDemand("hisyear").Value
                    
            End If
    
            'No longer first time
            FirstTime = False
    
            'Save current Location/Item
            PrevLcId = rsGetDemand("LcId").Value
            PrevItem = rsGetDemand("Item").Value
    
            'Load the Demand History
            HsYear = rsGetDemand("HisYear").Value
    
            For HsPd = 52 To 1 Step -1
                'Calculate Pd
                Pd = GetDmdPd(EndYear, EndPeriod, HsYear, HsPd)
    
                'Check for full history
                If Pd >= 1 And Pd <= 156 Then
                    DemandInfo.Cps(Pd) = rsGetDemand("cps" & Format(HsPd, "00")).Value
                End If
            Next HsPd
    
            'Get Next Demand Update Record
            rsGetDemand.MoveNext
        
        Loop
    
        'Update the last item
        RcdCount = RcdCount + 1
        Me.txtRcdCount.Text = Format(RcdCount, "#,##0")
    
        'Write a record to the Analysis File
        OutRcd = DemandInfo.LcId & vbTab _
            & DemandInfo.Item & vbTab _
            & DemandInfo.ItStat & vbTab _
            & FormatDateTime(DemandInfo.ActDate, vbShortDate) & vbTab _
            & Format(DemandInfo.FcstCycles, "0") & vbTab _
            & Format(DemandInfo.ZeroCount, "0") & vbTab _
            & Format(DemandInfo.FcstDemand, "0.00") & vbTab _
            & Format(DemandInfo.MAE, "0.00") & vbTab _
            & Format(DemandInfo.DSer, "0.00") & vbTab _
            & Format(DemandInfo.Accum_Lt, "0") & vbTab _
            & Format(DemandInfo.ReviewTime, "0") & vbTab _
            & Format(DemandInfo.OrderQty, "0") & vbTab _
            & Format(DemandInfo.Cost, "0.0000") & vbTab _
            & Format(DemandInfo.HisYear, "0") & vbTab
            
        For i = LBound(DemandInfo.Cps) To UBound(DemandInfo.Cps) - 1
            OutRcd = OutRcd & Format(DemandInfo.Cps(i), "0") & vbTab
        Next i
        
        OutRcd = OutRcd & Format(DemandInfo.Cps(UBound(DemandInfo.Cps)), "0")
                    
        Print #f_FileIndex, OutRcd
    End If
    
    'Don't forget to re-enable the command button before exiting!
    cmdCreate.Enabled = True
    tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = True
    tbIntermittentDemand.Tools("ID_Close").Enabled = True
    
    'Wrap up
    Close #f_FileIndex
    
    Write_Message ""
    Me.MousePointer = vbNormal
    
    If f_IsRecordsetValidAndOpen(rsGetDemand) Then rsGetDemand.Close
    Set rsGetDemand = Nothing

    If Not (AIM_GetDemand_Sp Is Nothing) Then Set AIM_GetDemand_Sp.ActiveConnection = Nothing
    Set AIM_GetDemand_Sp = Nothing
    
    '    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCreate_Click)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::cmdCreate_Click", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbNormal
    On Error Resume Next
    Close #f_FileIndex
    If f_IsRecordsetValidAndOpen(rsGetDemand) Then rsGetDemand.Close
    Set rsGetDemand = Nothing
    If Not (AIM_GetDemand_Sp Is Nothing) Then Set AIM_GetDemand_Sp.ActiveConnection = Nothing
    Set AIM_GetDemand_Sp = Nothing
    'Don't forget to re-enable the command button before exiting!
    cmdCreate.Enabled = True
    tbIntermittentDemand.Tools("ID_RunAnalysis").Enabled = True
    tbIntermittentDemand.Tools("ID_Close").Enabled = True
    
End Sub

Private Sub cmdExecute_Click()
On Error GoTo ErrorHandler

    Dim f_Results(0 To 50) As Long
    
    Dim LtWks As Long
    Dim PctZero As Double
    Dim Mean As Double
    Dim StdDev As Double
    Dim SrvLvl As Double
    Dim NbrLoops As Long
    Dim MaxDemand As Double
    Dim ModelMean As Double
    Dim ModelStdDev As Double
    Dim NZ_ModelMean As Double
    Dim NZ_ModelStdDev As Double
    Dim SFact As Double
    Dim i, J As Long
    Dim Interval As Double
    
    LtWks = Me.txtLTWks.Value
    PctZero = Me.txtPctZero.Value
    Mean = Me.txtNZ_Mean.Value
    StdDev = Me.txtNZ_StdDev.Value
    SrvLvl = Me.txtSrvLvlGoal2.Value
    NbrLoops = Me.txtNbrLoops.Value
    
    'Clear the results array
    Erase f_Results
    
'    SFact = SAfetyFactor(SrvLvl)
'
'    For i = 1 To 10     'Vary LTWks
'        LtWks = i
'
'        For j = 0 To 9    'Vary PZ
'            Erase Results
'            PctZero = j * 0.1
'
'            MaxDemand = LeadTimeSimulation(LtWks, PctZero, Mean, StdDev, SrvLvl, NbrLoops, ModelMean, _
'                ModelStdDev, NZ_ModelMean, NZ_ModelStdDev, Results())
'
'            Debug.Print Mean, StdDev, SrvLvl, PctZero, i, Round(ModelMean, 2), Round(ModelStdDev, 2), Round(MaxDemand, 2), Round(NZ_ModelMean, 2), Round(NZ_ModelStdDev, 2), Round(SFact, 3)
'        Next j
'    Next i
    
    'Calculate Interval
    Interval = ((Mean + (3.5 * StdDev)) * LtWks) / (UBound(f_Results) - LBound(f_Results) + 1)
    
    Me.txtModelMean.Text = Format(0, "#,##0.00")
    Me.txtModelStdDev.Text = Format(0, "#,##0.00")
    Me.txtModelMaxDemand.Text = Format(0, "#,##0.00")
    
    MaxDemand = LeadTimeSimulation(LtWks, PctZero, Mean, StdDev, SrvLvl, NbrLoops, ModelMean, _
         ModelStdDev, NZ_ModelMean, NZ_ModelStdDev, f_Results())

    'Update Results
    Me.txtModelMean.Text = Format(ModelMean, "#,##0.00")
    Me.txtModelStdDev.Text = Format(ModelStdDev, "#,##0.00")
    Me.txtModelMaxDemand.Text = Format(MaxDemand, "#,##0.00")
    
    'Graph the Results array
    Me.FSChart.IsBatched = True
        
    With Me.FSChart
        .AllowUserChanges = False
        
        'Set up Header
        .Header.Text = getTranslationResource("Demand Distribution")
        .Border.Type = oc2dBorderPlain
        .Border.Width = 2
        
        .ChartArea.Axes("X").Title.Text = getTranslationResource("")
        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
        .ChartArea.Axes("X").MajorGrid.Spacing = 4
        .ChartArea.Axes("X").Max = 52
        
        .ChartArea.Axes("Y").Title.Text = getTranslationResource("Demand")
        .ChartArea.Axes("Y").MajorGrid.Spacing = 10
        
        'Remove any old x-axis labels
        .ChartArea.Axes("X").ValueLabels.RemoveAll
        
        'Set up Legends
        .Legend.Anchor = oc2dAnchorSouth
        .Legend.IsShowing = True
        .Legend.Border.Type = oc2dBorderPlain
        .Legend.Border.Width = 2
        .Legend.Text = getTranslationResource("")
        
        'Set up Data Series
        .ChartGroups(1).ChartType = oc2dTypeBar
        .ChartGroups(1).Data.NumSeries = 1
        .ChartGroups(1).Data.NumPoints(1) = 51
        
        'Set the Line Properties
        .ChartGroups(1).Styles(1).Symbol.Shape = 4
        .ChartGroups(1).Styles(1).Line.Color = vbRed
        .ChartGroups(1).Styles(1).Line.Width = 2
        
        'Set the x axes label
        .ChartArea.Axes("X").ValueLabels.Add 1, Format(0, "#0")

        'Set the data value
        .ChartGroups(1).Data.Y(1, 1) = f_Results(0)
         
        'Load Data
         For i = LBound(f_Results) + 1 To UBound(f_Results)
            'Set the x axis' label
            If i Mod 4 = 0 Then
                .ChartArea.Axes("X").ValueLabels.Add i + 1, Format(i * Interval, "#0")
            End If

            'Set the data value
            .ChartGroups(1).Data.Y(1, i + 1) = f_Results(i)
        Next i

    End With

    Me.FSChart.IsBatched = False

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(cmdExecute_Click)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::cmdExecute_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdGetFile_Click()
On Error GoTo ErrorHandler
    
    Me.CommonDialog1.CancelError = True ' Causes a trappable error to occur when the user hits the 'Cancel' button
    Me.CommonDialog1.DialogTitle = getTranslationResource("Intermittent Demand Analysis")
    Me.CommonDialog1.FileName = Me.txtFileName.Text
    Me.CommonDialog1.Filter = "Text Format (*.TXT)| *.TXT"
    Me.CommonDialog1.FilterIndex = 1
    Me.CommonDialog1.Flags = cdlOFNOverwritePrompt
    Me.CommonDialog1.ShowSave
    
    Me.txtFileName.Text = Me.CommonDialog1.FileName

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    If Err.Number = 32755 Then
        'Cancel was selected
        Exit Sub
    Else
        'f_HandleErr Me.Caption & "(cmdGetFile_Click)"
         f_HandleErr , , , "AIM_IntermittentAnalysis::cmdGetFile_Click", Now, gDRGeneralError, True, Err
    End If
End Sub
 
Private Sub cmdInFileName_Click()
On Error GoTo ErrorHandler
    
    Me.CommonDialog1.CancelError = True ' Causes a trappable error to occur when the user hits the 'Cancel' button
    Me.CommonDialog1.DialogTitle = getTranslationResource("Intermittent Demand Analysis")
    Me.CommonDialog1.FileName = Me.txtInFileName.Text
    Me.CommonDialog1.Filter = "Text Format (*.TXT)| *.TXT"
    Me.CommonDialog1.FilterIndex = 1
    Me.CommonDialog1.Flags = cdlOFNOverwritePrompt
    Me.CommonDialog1.ShowOpen
    
    Me.txtInFileName.Text = Me.CommonDialog1.FileName
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    If Err.Number = 32755 Then
        'Cancel was selected
        Exit Sub
    Else
        'f_HandleErr Me.Caption & "(cmdInFileName_Click)"
         f_HandleErr , , , "AIM_IntermittentAnalysis::cmdInFileName_Click", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub dcAIMLocations_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMLocations.Columns(0).Name = "lcid"
    Me.dcAIMLocations.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcAIMLocations.Columns(0).Width = 1000
    Me.dcAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(0).DataField = "lcid"
    
    Me.dcAIMLocations.Columns(1).Name = "lname"
    Me.dcAIMLocations.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcAIMLocations.Columns(1).Width = 2880
    Me.dcAIMLocations.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(1).DataField = "lname"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMLocations, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMLocations.Columns.Count - 1
'        dcAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dcAIMLocations_InitColumnProps)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::dcAIMLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDemandSource_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcDemandSource.Columns(0).Caption = getTranslationResource("Code")
    Me.dcDemandSource.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcDemandSource.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDemandSource.Columns(0).Width = 720
    
    Me.dcDemandSource.Columns(1).Caption = getTranslationResource("Description")
    Me.dcDemandSource.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcDemandSource.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDemandSource.Columns(1).Width = 2000
    
    Me.dcDemandSource.AddItem "O" & vbTab & _
                        getTranslationResource("Ordered Quantity")
    Me.dcDemandSource.AddItem "S" & vbTab & _
                        getTranslationResource("Shipped Quantity")
                        
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDemandSource, ACW_EXPAND
    End If
'    For IndexCounter = 0 To dcDemandSource.Columns.Count - 1
'        dcDemandSource.Columns(IndexCounter).HasHeadBackColor = True
'        dcDemandSource.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'   Next

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dcDemandSource_InitColumnProps)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::dcDemandSource_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgResults_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Define columns
    Me.dgResults.Columns(0).Name = "FileName"
    Me.dgResults.Columns(0).Caption = getTranslationResource("Analysis File")
    Me.dgResults.Columns(0).Alignment = ssAlignLeft
    Me.dgResults.Columns(0).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(0).Width = 4200
    
    Me.dgResults.Columns(1).Name = "PctZeroMin"
    Me.dgResults.Columns(1).Caption = getTranslationResource("Min % Zero")
    Me.dgResults.Columns(1).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(1).Alignment = ssAlignRight
    Me.dgResults.Columns(1).NumberFormat = "0.000"
    Me.dgResults.Columns(1).Width = 1400

    Me.dgResults.Columns(2).Name = "PctZeroMax"
    Me.dgResults.Columns(2).Caption = getTranslationResource("Max % Zero")
    Me.dgResults.Columns(2).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(2).Alignment = ssAlignRight
    Me.dgResults.Columns(2).NumberFormat = "0.000"
    Me.dgResults.Columns(2).Width = 1400

    Me.dgResults.Columns(3).Name = "SrvLvlGoal"
    Me.dgResults.Columns(3).Caption = getTranslationResource("Goal")
    Me.dgResults.Columns(3).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(3).Alignment = ssAlignRight
    Me.dgResults.Columns(3).NumberFormat = "0.000"
    Me.dgResults.Columns(3).Width = 1200

    Me.dgResults.Columns(4).Name = "LookBackPds"
    Me.dgResults.Columns(4).Caption = getTranslationResource("Look Back")
    Me.dgResults.Columns(4).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(4).Alignment = ssAlignRight
    Me.dgResults.Columns(4).NumberFormat = "0"
    Me.dgResults.Columns(4).Width = 1400

    Me.dgResults.Columns(5).Name = "ItemCount"
    Me.dgResults.Columns(5).Caption = getTranslationResource("Items")
    Me.dgResults.Columns(5).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(5).Alignment = ssAlignRight
    Me.dgResults.Columns(5).NumberFormat = "#,##0"
    Me.dgResults.Columns(5).Width = 1200

    Me.dgResults.Columns(6).Name = "PctZeroCount"
    Me.dgResults.Columns(6).Caption = getTranslationResource("% Zero")
    Me.dgResults.Columns(6).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(6).Alignment = ssAlignRight
    Me.dgResults.Columns(6).NumberFormat = "0.0%"
    Me.dgResults.Columns(6).Width = 1200

    Me.dgResults.Columns(7).Name = "NbrWeeks"
    Me.dgResults.Columns(7).Caption = getTranslationResource("Weeks")
    Me.dgResults.Columns(7).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(7).Alignment = ssAlignRight
    Me.dgResults.Columns(7).NumberFormat = "#,##0"
    Me.dgResults.Columns(7).Width = 1200

    Me.dgResults.Columns(8).Name = "SafetyStockOk"
    Me.dgResults.Columns(8).Caption = getTranslationResource("Results")
    Me.dgResults.Columns(8).CaptionAlignment = ssCaptionAlignmentLeft
    Me.dgResults.Columns(8).Alignment = ssAlignRight
    Me.dgResults.Columns(8).NumberFormat = "##0.###0%"
    Me.dgResults.Columns(8).Width = 1000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgResults, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgResults.Columns.Count - 1
'        dgResults.Columns(IndexCounter).HasHeadBackColor = True
'        dgResults.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgResults.Columns(IndexCounter).Locked = False Then dgResults.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgResults_InitColumnProps)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::dgResults_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgResults_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim i As Long
    Dim p As Long
    Dim r As Long
    Dim IsSafetyStockOk As Long
    
    If IsNull(StartLocation) Then       'If the grid is empty then
        If ReadPriorRows Then           'If moving backwards through grid then
            p = ResultsCount - 1               'pointer = # of last grid row
        Else                            'else
            p = 0                       'pointer = # of first grid row
        End If
        
    Else                                'If the grid already has data in it then
        p = StartLocation               'pointer = location just before or after the row where data will be added

        If ReadPriorRows Then           'If moving backwards through grid then
            p = p - 1                   'move pointer back one row
        Else                            'else
            p = p + 1                   'move pointer ahead one row
        End If
    End If

    'The pointer (p) now points to the row of the grid where you will start adding data.
    For i = 0 To RowBuf.RowCount - 1                    'For each row in the row buffer
        If p < 0 Or p > ResultsCount - 1 Then Exit For         'If the pointer is outside the grid then stop this

        'For each column in the grid
        'Set the value of each column in the row buffer
        'to the corresponding value in the arrray
        RowBuf.Value(i, 0) = Results(p).FileName
        RowBuf.Value(i, 1) = Results(p).PctZeroMin
        RowBuf.Value(i, 2) = Results(p).PctZeroMax
        RowBuf.Value(i, 3) = Results(p).SrvLvlGoal
        RowBuf.Value(i, 4) = Results(p).LookBackPds
        RowBuf.Value(i, 5) = Results(p).ItemCount
        RowBuf.Value(i, 6) = (Results(p).PctZeroCount / Results(p).ItemCount)
        RowBuf.Value(i, 7) = Results(p).NbrWeeks
        If Results(p).NbrWeeks > 0 _
        And Results(p).SafetyStockOK > 0 _
        Then
            IsSafetyStockOk = Results(p).SafetyStockOK / Results(p).NbrWeeks
        Else
            IsSafetyStockOk = 0
        End If
        RowBuf.Value(i, 8) = IsSafetyStockOk
        
        RowBuf.Bookmark(i) = p                          'set the value of the bookmark for the current row in the rowbuffer

        If ReadPriorRows Then                           'move the pointer forward or backward, depending
            p = p - 1                                   'on which way it's supposed to move
        Else
            p = p + 1
        End If

        r = r + 1                                       'increment the number of rows read
    Next i

    RowBuf.RowCount = r                                 'set the size of the row buffer to the number of rows read
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgResults_UnboundReadData)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::dgResults_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    Me.WindowState = vbMaximized
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
        
    Dim LastFcstUpdCyc As Long
    Dim RtnCode As Long
    Dim strMessage As String
    
    'Display status message
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01702")
    If StrComp(strMessage, "STATMSG01702") = 0 Then strMessage = "Initializing Intermittent Demand Analysis..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    RemoveSemicolons
    
    InitializeChart
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build/Bind the AIM Locations Drop Down and get the Last Forecast Update Cycle
    LoadLocationsList LastFcstUpdCyc
    
    'Initialize Analysis Options
    Me.dcAIMLocations.Text = getTranslationResource("All")
    Me.txtPctZeroMin.Value = 0.4
    Me.txtPctZeroMax.Value = 0.9
    Me.txtSrvLvlGoal.Value = 0.9
    Me.txtLookBackPds.Value = 52
    Me.dcDemandSource.Text = "S"
    
    If LastFcstUpdCyc <> 0 Then
        Me.txtEndYear.Value = LastFcstUpdCyc \ 100
        Me.txtEndPeriod.Value = LastFcstUpdCyc - CLng(Me.txtEndYear.Value) * 100
        
    Else
        Me.txtEndYear.Value = Year(Date)
        Me.txtEndPeriod.Value = DatePart("ww", Date)
    
    End If
    
    Me.txtLTWks.Value = 1
    Me.txtPctZero.Value = 0.4
    Me.txtNZ_Mean.Value = 10
    Me.txtNZ_StdDev.Value = 4
    Me.txtSrvLvlGoal2.Value = 0.9
    Me.txtNbrLoops.Value = 10000
    Me.txtFileName.Text = App.Path & "\" & "IntAnalysis.Txt"
    Me.txtInFileName.Text = App.Path & "\" & "intAnalysis.Txt"
    Me.txtMinutes = "00:00"
    
    'Initialize the Results Array
    ReDim Results(10)
    ResultsCount = 0
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Make the spin button visible
    Me.txtPctZeroMin.Spin.Visible = 1
    Me.txtPctZeroMax.Spin.Visible = 1
    Me.txtSrvLvlGoal.Spin.Visible = 1
    Me.txtPctZero.Spin.Visible = 1
    Me.txtNZ_Mean.Spin.Visible = 1
    Me.txtNZ_StdDev.Spin.Visible = 1
    Me.txtSrvLvlGoal2.Spin.Visible = 1
    Me.txtLTWks.Spin.Visible = 1
    Me.txtNbrLoops.Spin.Visible = 1
    Me.txtEndYear.Spin.Visible = 1
    Me.txtEndPeriod.Spin.Visible = 1

    'Wind Up
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::Form_Load", Now, gDRGeneralError, True, Err

End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error Resume Next

    Erase Results
    
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Unload)"
     f_HandleErr , , , "AIM_IntermittentAnalysis::Form_Unload", Now, gDRGeneralError, True, Err

    Resume Next
End Sub

Private Function InitializeChart()
On Error GoTo ErrorHandler

    Dim Counter As Long
    
    With Me.FSChart
        .AllowUserChanges = False
        
        'Set up Header
        .Header.Text = ""
        .Border.Type = oc2dBorderPlain
        .Border.Width = 2
        
        .ChartArea.Axes("X").Title.Text = ""
        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
        .ChartArea.Axes("X").MajorGrid.Spacing = 4
        .ChartArea.Axes("X").Max = 52
        
        .ChartArea.Axes("Y").Title.Text = ""
        .ChartArea.Axes("Y").MajorGrid.Spacing = 10
        
        'Remove any old x-axis labels
        .ChartArea.Axes("X").ValueLabels.RemoveAll
        .ChartArea.Axes("Y").ValueLabels.RemoveAll
        
        'Set up Legends
        .Legend.Anchor = oc2dAnchorSouth
        .Legend.IsShowing = True
        .Legend.Border.Type = oc2dBorderNone
        .Legend.Text = ""
        
        'Set up Data Series
        .ChartGroups(1).ChartType = oc2dTypeBar
        .ChartGroups(1).Data.NumSeries = 1
        .ChartGroups(1).Data.NumPoints(1) = 51
        
        'Set the Line Properties
        .ChartGroups(1).Styles(1).Symbol.Shape = 4
        .ChartGroups(1).Styles(1).Line.Color = vbRed
        .ChartGroups(1).Styles(1).Line.Width = 2
        
        'Set the data value
        For Counter = 1 To .ChartGroups(1).Data.NumPoints(1)
            .ChartGroups(1).Data.Y(1, Counter) = 0
        Next
        
        .IsBatched = False
    End With

Exit Function
ErrorHandler:
    Screen.MousePointer = vbNormal
        Me.FSChart.IsBatched = False
    'Err.Raise Err.Number, Err.source & "(InitializeChart)", Err.Description
    f_HandleErr , , , "AIM_IntermittentAnalysis::InitializeChart", Now, gDRGeneralError, True, Err

End Function

Private Function LoadLocationsList(p_LastFcstUpdCyc As Long)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Long
    Dim strSQL As String
    Dim rsAIMLocations As ADODB.Recordset
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    strSQL = "select lcid, lname, last_fcstupdcyc from AIMLocations order by lcid "
    Set rsAIMLocations = New ADODB.Recordset
    rsAIMLocations.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    'Get the Last Forecast Cycle from the Location Table
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        p_LastFcstUpdCyc = rsAIMLocations("last_fcstupdcyc").Value
    
        Me.dcAIMLocations.AddItem getTranslationResource("All") & _
                            vbTab & getTranslationResource("All Locations")
        
        Do Until rsAIMLocations.eof
            Me.dcAIMLocations.AddItem rsAIMLocations("LcId").Value & vbTab & rsAIMLocations("LName").Value
            rsAIMLocations.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Screen.MousePointer = vbNormal
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
    'Err.Raise ErrNumber, ErrSource & "(LoadLocationsList)", ErrDescription
    f_HandleErr , , , "AIM_IntermittentAnalysis::LoadLocationsList", Now, gDRGeneralError, True, Err
End Function

Private Function RemoveSemicolons()
'Remove semi colons from static text
On Error GoTo ErrorHandler
    
    If Right(Label15.Caption, 1) = ":" Then Label15.Caption = Left(Label15.Caption, Len(Label15.Caption) - 1)
    If Right(Label18.Caption, 1) = ":" Then Label18.Caption = Left(Label18.Caption, Len(Label18.Caption) - 1)
    If Right(Label19.Caption, 1) = ":" Then Label19.Caption = Left(Label19.Caption, Len(Label19.Caption) - 1)
    If Right(Label20.Caption, 1) = ":" Then Label20.Caption = Left(Label20.Caption, Len(Label20.Caption) - 1)
    If Right(Label21.Caption, 1) = ":" Then Label21.Caption = Left(Label21.Caption, Len(Label21.Caption) - 1)
    If Right(Label22.Caption, 1) = ":" Then Label22.Caption = Left(Label22.Caption, Len(Label22.Caption) - 1)
    If Right(Label23.Caption, 1) = ":" Then Label23.Caption = Left(Label23.Caption, Len(Label23.Caption) - 1)
    
Exit Function
ErrorHandler:
    Screen.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source, Err.Description
    f_HandleErr , , , "AIM_IntermittentAnalysis::RemoveSemicolons", Now, gDRGeneralError, True, Err
End Function
    
    
