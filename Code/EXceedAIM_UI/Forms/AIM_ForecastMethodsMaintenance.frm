VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ForecastMethodsMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Forecast Methods Maintenance"
   ClientHeight    =   6960
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11865
   Icon            =   "AIM_ForecastMethodsMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6960
   ScaleWidth      =   11865
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   6600
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_ForecastMethodsMaintenance.frx":030A
      ToolBars        =   "AIM_ForecastMethodsMaintenance.frx":9BB2
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMMethods 
      Align           =   1  'Align Top
      Height          =   6528
      Left            =   0
      TabIndex        =   0
      Top             =   0
      Width           =   11866
      _Version        =   196617
      DataMode        =   1
      AllowAddNew     =   -1  'True
      AllowDelete     =   -1  'True
      AllowColumnSwapping=   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   20930
      _ExtentY        =   11515
      _StockProps     =   79
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ForecastMethodsMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMMethods As ADODB.Recordset

Dim AIMMethodsBookMark As Variant

Private Sub dgAIMMethods_AfterInsert(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    If Cn.Errors.Count > 0 Then
        RtnDispErrMsg = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_AfterInsert)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_AfterInsert", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    InvalidData = UserInputValidation(strMessage, ColIndex)
    
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        dgAIMMethods.DataChanged = False
    End If
    
    Cancel = InvalidData
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_BeforeColUpdate)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
On Error GoTo ErrorHandler
    
    Dim Result As Long
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Cancel the default prompt
    DispPromptMsg = False
    
    'Display a confirmation msgbox
    strMessage = getTranslationResource("MSGBOX01500")
    If StrComp(strMessage, "MSGBOX01500") = 0 Then strMessage = "Delete the"
    
    strMessage = strMessage + " " + str(dgAIMMethods.SelBookmarks.Count) + " "
    strMessage1 = getTranslationResource("MSGBOX01501")
    If StrComp(strMessage1, "MSGBOX01501") = 0 Then strMessage1 = "selected Forecast Method(s)?"
    strMessage = strMessage + strMessage1

    Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ForecastMethodsMaintenance.Caption))

    Select Case Result
    Case vbYes
        'User chose to delete. Do nothing
    Case vbNo
        'User chose to Cancel
        Cancel = True
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_BeforeDelete)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    InvalidData = UserInputValidation(strMessage)
    
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
    End If
    
    Cancel = InvalidData
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_BeforeUpdate)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsAIMMethods)) Then Exit Sub
    
    'Check for unsortable column index
'    If Me.dgAIMMethods.Columns(ColIndex).Name = "" _
'    Then Exit Sub
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
    
    If InStr(rsAIMMethods.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgAIMMethods.Columns(ColIndex).Name
    ElseIf InStr(rsAIMMethods.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgAIMMethods.Columns(ColIndex).Name
    End If
    
    'Sort grid by selected column
    rsAIMMethods.Sort = Trim(ColName & " " & SortSeq)

    Me.dgAIMMethods.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then Me.dgAIMMethods.Rows = rsAIMMethods.RecordCount
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_HeadClick)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Define Columns
    Me.dgAIMMethods.Columns(0).Name = "methodid"
    Me.dgAIMMethods.Columns(0).Caption = getTranslationResource("ID")
    Me.dgAIMMethods.Columns(0).Width = 400
    Me.dgAIMMethods.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMMethods.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgAIMMethods.Columns(0).Style = ssStyleEdit
    Me.dgAIMMethods.Columns(0).FieldLen = 3
    'Me.dgAIMMethods.Columns(0).Mask = "#"
    Me.dgAIMMethods.Columns(0).DataType = vbInteger
    'Me.dgAIMMethods.Columns(0).PromptChar = " "
    
    Me.dgAIMMethods.Columns(1).Name = "methodstatus"
    Me.dgAIMMethods.Columns(1).Caption = getTranslationResource("Active")
    Me.dgAIMMethods.Columns(1).Width = 1000
    Me.dgAIMMethods.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMMethods.Columns(1).Alignment = ssCaptionAlignmentCenter
    Me.dgAIMMethods.Columns(1).Style = ssStyleCheckBox
    
    Me.dgAIMMethods.Columns(2).Name = "methoddesc"
    Me.dgAIMMethods.Columns(2).Caption = getTranslationResource("Description")
    Me.dgAIMMethods.Columns(2).Width = 6000
    Me.dgAIMMethods.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMMethods.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgAIMMethods.Columns(2).Style = ssStyleEdit
    Me.dgAIMMethods.Columns(2).FieldLen = 80

    Me.dgAIMMethods.Columns(3).Name = "applyseasonsindex"
    Me.dgAIMMethods.Columns(3).Caption = getTranslationResource("Apply SI")
    Me.dgAIMMethods.Columns(3).Width = 1000
    Me.dgAIMMethods.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMMethods.Columns(3).Alignment = ssCaptionAlignmentCenter
    Me.dgAIMMethods.Columns(3).Style = ssStyleCheckBox
    
    Me.dgAIMMethods.Columns(4).Name = "applytrend"
    Me.dgAIMMethods.Columns(4).Caption = getTranslationResource("Apply Trend")
    Me.dgAIMMethods.Columns(4).Width = 1000
    Me.dgAIMMethods.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMMethods.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgAIMMethods.Columns(4).Style = ssStyleCheckBox
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMMethods, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMMethods.Columns.Count - 1
'        dgAIMMethods.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMMethods.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMMethods.Columns(IndexCounter).Locked = False Then dgAIMMethods.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_PrintBegin)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG01500")
    If StrComp(strMessage, "RPTMSG01500") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG01501")
    If StrComp(strMessage1, "RPTMSG01501") = 0 Then strMessage = "AIM Forecast Methods Listing"
    strMessage2 = getTranslationResource("RPTMSG01502")
    If StrComp(strMessage2, "RPTMSG01502") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + _
                            strMessage1 + vbTab + strMessage2 + " <page number>"

    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_PrintInitialize)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long
    
    Cn.Errors.Clear
    
    If Not f_IsRecordsetValidAndOpen(rsAIMMethods) Then Exit Sub
    
    If Not UserInputValidation(strMessage) Then
        rsAIMMethods.AddNew
    
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsAIMMethods("methodid").Value = RowBuf.Value(0, 0)
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsAIMMethods("methodstatus").Value = RowBuf.Value(0, 1)
        Else
             rsAIMMethods("methodstatus").Value = 1
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsAIMMethods("methoddesc").Value = RowBuf.Value(0, 2)
        Else
            rsAIMMethods("methoddesc").Value = ""
        End If
    
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsAIMMethods("applyseasonsindex").Value = IIf(RowBuf.Value(0, 3) = vbUnchecked, "N", "Y")
        Else
            rsAIMMethods("applyseasonsindex").Value = "N"
        End If
    
        If Not IsNull(RowBuf.Value(0, 4)) Then
            rsAIMMethods("applytrend").Value = RowBuf.Value(0, 4)
        Else
            rsAIMMethods("applytrend").Value = 0
        End If
    
        rsAIMMethods.Update
    Else
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        Exit Sub
    End If
        
    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG01500")
        If StrComp(strMessage, "ERRMSG01500") = 0 Then strMessage = "Error adding AIM Forecast Method Record."
        ADOErrorHandler Cn, strMessage
        rsAIMMethods.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG01500")
        If StrComp(strMessage, "STATMSG01500") = 0 Then strMessage = "AIM Forecast Method successfully added."
        Write_Message strMessage
        rsAIMMethods.MoveLast
        NewRowBookmark = rsAIMMethods.Bookmark
    End If
    
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_UnboundAddData)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_UnboundAddData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then Exit Sub
        
    rsAIMMethods.Bookmark = Bookmark
    rsAIMMethods.Delete

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_UnboundDeleteRow)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then Exit Sub
    
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsAIMMethods.MoveLast
        Else
            rsAIMMethods.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        
        rsAIMMethods.Bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsAIMMethods.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsAIMMethods.Bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_UnboundPositionData)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAIMMethods.MoveLast
        Else
            rsAIMMethods.MoveFirst
        End If
    
    Else
        rsAIMMethods.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAIMMethods.MovePrevious
        Else
            rsAIMMethods.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsAIMMethods.BOF Or rsAIMMethods.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsAIMMethods("methodid").Value
        If rsAIMMethods("methodstatus").Value = 1 Then
            RowBuf.Value(i, 1) = True
        Else
            RowBuf.Value(i, 1) = False
        End If
        RowBuf.Value(i, 2) = rsAIMMethods("methoddesc").Value
        RowBuf.Value(i, 3) = IIf(rsAIMMethods("applyseasonsindex").Value = "Y", 1, 0)
        RowBuf.Value(i, 4) = rsAIMMethods("applytrend").Value
        
        RowBuf.Bookmark(i) = rsAIMMethods.Bookmark
    
        If ReadPriorRows Then
            rsAIMMethods.MovePrevious
        Else
            rsAIMMethods.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_UnboundReadData)"
     f_HandleErr , , , Me.Caption & "::dgAIMMethods_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMMethods_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long
        
    Cn.Errors.Clear

    rsAIMMethods.Bookmark = WriteLocation

    If Not f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then Exit Sub
    
    If Not UserInputValidation(strMessage) Then
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsAIMMethods("methodid").Value = RowBuf.Value(0, 0)
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            If RowBuf.Value(0, 1) = True Then
                rsAIMMethods("methodstatus").Value = 1
            Else
                rsAIMMethods("methodstatus").Value = 999999
            End If
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsAIMMethods("methoddesc").Value = RowBuf.Value(0, 2)
        End If
    
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsAIMMethods("applyseasonsindex").Value = IIf(RowBuf.Value(0, 3) = 0, "N", "Y")
        End If
    
        If Not IsNull(RowBuf.Value(0, 4)) Then
            rsAIMMethods("applytrend").Value = RowBuf.Value(0, 4)
        End If
    
        'Update the Item Status Table
        rsAIMMethods.Update
    Else
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        'Cancel update
        rsAIMMethods.CancelUpdate
    End If
    
    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG01501")
        If StrComp(strMessage, "ERRMSG01501") = 0 Then strMessage = "Error editing AIM Forecast Method Record."
        ADOErrorHandler Cn, strMessage
        rsAIMMethods.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG01501")
        If StrComp(strMessage, "STATMSG01501") = 0 Then strMessage = "AIM Forecast Method successfully modified."
        Write_Message strMessage
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMMethods_UnboundWriteData)"
     f_HandleErr , , , Me.Caption & "dgAIMMethods_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01502")
    If StrComp(strMessage, "STATMSG01502") = 0 Then strMessage = "Initializing AIM Forecast Methods Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
        
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build the SQL Statement
    SqlStmt = "SELECT * FROM AIMMethods ORDER BY MethodId"
    
    'Open the Record Set
    Set rsAIMMethods = New ADODB.Recordset
    With rsAIMMethods
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    rsAIMMethods.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dgAIMMethods.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then dgAIMMethods.Rows = rsAIMMethods.RecordCount
    
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbNavigation.Tools("ID_Save").Enabled = False
        Me.tbNavigation.Tools("ID_Delete").Enabled = False
        Me.tbNavigation.Tools("ID_AddNew").Enabled = False
        Me.dgAIMMethods.AllowUpdate = False
        Me.dgAIMMethods.AllowAddNew = False
        Me.dgAIMMethods.AllowDelete = False
    Else
        Me.tbNavigation.Tools("ID_Save").Enabled = True
        Me.tbNavigation.Tools("ID_Delete").Enabled = True
        Me.tbNavigation.Tools("ID_AddNew").Enabled = True
        Me.dgAIMMethods.AllowUpdate = True
        Me.dgAIMMethods.AllowAddNew = True
        Me.dgAIMMethods.AllowDelete = True
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMMethods) Then rsAIMMethods.Close
    Set rsAIMMethods = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
     'f_HandleErr Me.Caption & "(Form_Unload)"
      f_HandleErr , , , Me.Caption & "::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next

End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then
                Clipboard.Clear
                
                For i = 0 To rsAIMMethods.Fields.Count - 1
                    s1 = s1 + rsAIMMethods.Fields(i).Name + vbTab
                Next i
                
                s1 = s1 + vbCrLf
                    
                rsAIMMethods.MoveFirst
                s2 = rsAIMMethods.GetString(adClipString)
                
                Clipboard.SetText s1 + s2, vbCFText
            End If
        Case 1          'Print
            Me.dgAIMMethods.PrintData ssPrintAllRows, False, True
        
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , Me.Caption & "::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    'Navigate
    Select Case Tool.ID
        Case "ID_AddNew"
            Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
            Me.dgAIMMethods.AddNew
            
        Case "ID_Delete"
            Me.dgAIMMethods.DeleteSelected
            
        Case "ID_GetFirst"
            Me.dgAIMMethods.MoveFirst
            
        Case "ID_GetPrev"
            Me.dgAIMMethods.MovePrevious
            
        Case "ID_GetNext"
            Me.dgAIMMethods.MoveNext
            
        Case "ID_GetLast"
            Me.dgAIMMethods.MoveLast
            
        Case "ID_Save"
            Me.dgAIMMethods.Update
            
        Case "ID_SetBookMark"
            AIMMethodsBookMark = Me.dgAIMMethods.Bookmark
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        
        Case "ID_GoToBookMark"
            Me.dgAIMMethods.Bookmark = AIMMethodsBookMark
        
        Case "ID_Refresh"
            rsAIMMethods.Sort = ""
            Me.dgAIMMethods.ReBind
            If f_IsRecordsetOpenAndPopulated(rsAIMMethods) Then dgAIMMethods.Rows = rsAIMMethods.RecordCount
            Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
            
        Case "ID_Close"
            Unload Me
            Exit Sub
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , Me.Caption & "::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function UserInputValidation(p_Message As String, Optional p_ColIndex As Integer = -1) As Boolean
On Error GoTo ErrorHandler
    
    Dim lngCounter As Integer
    Dim LoopUpperBound As Integer
    Dim LoopLowerBound As Integer
    
    Dim RtnCode As Integer
    
    'ErrorMessage
    p_Message = getTranslationResource("MSGBOX06602")
    If StrComp(p_Message, "MSGBOX06602") = 0 Then _
        p_Message = "Some required fields are missing. Please review before moving to another row."
        
    'Check if it's just one field, or all.
    If p_ColIndex >= 0 Then
        LoopLowerBound = p_ColIndex
        LoopUpperBound = p_ColIndex
    Else
        LoopLowerBound = 0
        LoopUpperBound = dgAIMMethods.Columns.Count - 1
    End If

    For lngCounter = LoopLowerBound To LoopUpperBound
        If (lngCounter = 0 Or lngCounter = 2) _
        Then
            'Not the Checkboxes
            If dgAIMMethods.Columns(lngCounter).Value = "" _
            Or IsNull(dgAIMMethods.Columns(lngCounter).Value) _
            Then
                'Cancel = True
                UserInputValidation = True
                Exit Function
            End If
        End If
    Next

    'Cancel = false
    UserInputValidation = False
    
Exit Function
ErrorHandler:
   'Err.Raise Err.Number, Err.source, Err.Description & "(UserInputValidation)"
    f_HandleErr , , , "AIM_ForecastMethodsMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function

