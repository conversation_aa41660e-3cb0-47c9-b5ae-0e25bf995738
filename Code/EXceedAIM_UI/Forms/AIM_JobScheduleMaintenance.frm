VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{66A5AC41-25A9-11D2-9BBF-00A024695830}#1.0#0"; "titime8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_JobScheduleMaintenance 
   BorderStyle     =   4  'Fixed ToolWindow
   Caption         =   "SSA DR Job Schedule Maintenance"
   ClientHeight    =   6135
   ClientLeft      =   45
   ClientTop       =   285
   ClientWidth     =   11820
   BeginProperty Font 
      Name            =   "MS Sans Serif"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "AIM_JobScheduleMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   6135
   ScaleWidth      =   11820
   ShowInTaskbar   =   0   'False
   Begin VB.Frame fmDaily 
      Caption         =   "Daily Frequency"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   1545
      Left            =   53
      TabIndex        =   52
      Top             =   2850
      Width           =   11685
      Begin VB.OptionButton optSubday 
         Caption         =   "Occurs every"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   1
         Left            =   200
         TabIndex        =   27
         Top             =   675
         Width           =   2295
      End
      Begin VB.OptionButton optSubday 
         Caption         =   "Occurs once at"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   0
         Left            =   200
         TabIndex        =   25
         Top             =   270
         Width           =   2295
      End
      Begin TDBTime6Ctl.TDBTime txtStartTimeofDay 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "hh:mm:ss AMPM"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   4
         EndProperty
         Height          =   345
         Left            =   2580
         TabIndex        =   26
         Top             =   270
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_JobScheduleMaintenance.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Keys            =   "AIM_JobScheduleMaintenance.frx":0376
         Spin            =   "AIM_JobScheduleMaintenance.frx":03C6
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "hh:nn:ss AMPM"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "hh:nn:ss AMPM"
         HighlightText   =   0
         Hour12Mode      =   1
         IMEMode         =   3
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxTime         =   0.99999
         MidnightMode    =   0
         MinTime         =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "11:09:26 AM"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   0.464884259259259
      End
      Begin TDBNumber6Ctl.TDBNumber txtFreqSubDayInterval 
         Height          =   345
         Left            =   2580
         TabIndex        =   28
         Top             =   675
         Width           =   810
         _Version        =   65536
         _ExtentX        =   1429
         _ExtentY        =   609
         Calculator      =   "AIM_JobScheduleMaintenance.frx":03EE
         Caption         =   "AIM_JobScheduleMaintenance.frx":040E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":047A
         Keys            =   "AIM_JobScheduleMaintenance.frx":0498
         Spin            =   "AIM_JobScheduleMaintenance.frx":04E2
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   365
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcSubDay 
         DataField       =   " "
         Height          =   345
         Left            =   3405
         TabIndex        =   29
         Top             =   675
         Width           =   1170
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ColumnHeaders   =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   397
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   2064
         _ExtentY        =   609
         _StockProps     =   93
         BackColor       =   -2147483643
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin TDBTime6Ctl.TDBTime txtStartTimeOfDay2 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "hh:mm:ss AMPM"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   4
         EndProperty
         Height          =   345
         Left            =   6840
         TabIndex        =   30
         Top             =   675
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_JobScheduleMaintenance.frx":050A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Keys            =   "AIM_JobScheduleMaintenance.frx":0576
         Spin            =   "AIM_JobScheduleMaintenance.frx":05C6
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "hh:nn:ss AMPM"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "hh:nn:ss AMPM"
         HighlightText   =   0
         Hour12Mode      =   1
         IMEMode         =   3
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxTime         =   0.99999
         MidnightMode    =   0
         MinTime         =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "11:09:26 AM"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   0.464884259259259
      End
      Begin TDBTime6Ctl.TDBTime txtEndTimeOfDay 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "hh:mm:ss AMPM"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   4
         EndProperty
         Height          =   345
         Left            =   6840
         TabIndex        =   31
         Top             =   1095
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_JobScheduleMaintenance.frx":05EE
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Keys            =   "AIM_JobScheduleMaintenance.frx":065A
         Spin            =   "AIM_JobScheduleMaintenance.frx":06AA
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "hh:nn:ss AMPM"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "hh:nn:ss AMPM"
         HighlightText   =   0
         Hour12Mode      =   1
         IMEMode         =   3
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxTime         =   0.99999
         MidnightMode    =   0
         MinTime         =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "11:09:26 AM"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   0.464884259259259
      End
      Begin VB.Label lblEndTimeOfDay 
         Caption         =   "Ending At"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   5130
         TabIndex        =   54
         Top             =   1140
         Width           =   1635
      End
      Begin VB.Label lblStartTimeofDay2 
         Caption         =   "Starting At"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   5130
         TabIndex        =   53
         Top             =   720
         Width           =   1635
      End
   End
   Begin VB.Frame Frame1 
      Caption         =   "Duration"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   1035
      Left            =   53
      TabIndex        =   45
      Top             =   4545
      Width           =   11685
      Begin VB.OptionButton optEndDate 
         Caption         =   "No End Date"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   1
         Left            =   4890
         TabIndex        =   34
         Top             =   650
         Width           =   2295
      End
      Begin VB.OptionButton optEndDate 
         Caption         =   "End Date"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   0
         Left            =   4890
         TabIndex        =   33
         Top             =   277
         Width           =   2295
      End
      Begin TDBDate6Ctl.TDBDate txtStartDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   2580
         TabIndex        =   32
         Top             =   240
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Calendar        =   "AIM_JobScheduleMaintenance.frx":06D2
         Caption         =   "AIM_JobScheduleMaintenance.frx":07EA
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":0856
         Keys            =   "AIM_JobScheduleMaintenance.frx":0874
         Spin            =   "AIM_JobScheduleMaintenance.frx":08D2
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "03/07/2002"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37322
         CenturyMode     =   0
      End
      Begin TDBDate6Ctl.TDBDate txtEndDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   7320
         TabIndex        =   35
         Top             =   240
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Calendar        =   "AIM_JobScheduleMaintenance.frx":08FA
         Caption         =   "AIM_JobScheduleMaintenance.frx":0A12
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":0A7E
         Keys            =   "AIM_JobScheduleMaintenance.frx":0A9C
         Spin            =   "AIM_JobScheduleMaintenance.frx":0AFA
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "03/07/2002"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37322
         CenturyMode     =   0
      End
      Begin VB.Label lblStartDate 
         Caption         =   "Start Date"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   195
         TabIndex        =   46
         Top             =   285
         Width           =   2295
      End
   End
   Begin VB.CommandButton cmdOK 
      Caption         =   "&Apply"
      Default         =   -1  'True
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   345
      Left            =   10133
      Style           =   1  'Graphical
      TabIndex        =   36
      Top             =   5700
      Width           =   1485
   End
   Begin VB.CheckBox txtEnabled 
      Caption         =   "Enabled"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   340
      Left            =   9060
      TabIndex        =   1
      Top             =   169
      Width           =   2415
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   345
      Left            =   8415
      Style           =   1  'Graphical
      TabIndex        =   37
      Top             =   5700
      Width           =   1605
   End
   Begin VB.Frame Frame2 
      Caption         =   "Occurs"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   2025
      Left            =   53
      TabIndex        =   38
      Top             =   676
      Width           =   4900
      Begin VB.OptionButton optOccurs 
         Caption         =   "Start whenever the &CPU(s) become idle"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   1
         Left            =   200
         TabIndex        =   3
         Top             =   720
         Width           =   4440
      End
      Begin VB.OptionButton optOccurs 
         Caption         =   "Daily"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   3
         Left            =   200
         TabIndex        =   4
         Top             =   1144
         Width           =   2040
      End
      Begin VB.OptionButton optOccurs 
         Caption         =   "Weekly"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   4
         Left            =   200
         TabIndex        =   5
         Top             =   1562
         Width           =   2040
      End
      Begin VB.OptionButton optOccurs 
         Caption         =   "Monthly"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   5
         Left            =   2595
         TabIndex        =   7
         Top             =   1560
         Width           =   2040
      End
      Begin VB.OptionButton optOccurs 
         Caption         =   "&One Time"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   2
         Left            =   2595
         TabIndex        =   6
         Top             =   1140
         Value           =   -1  'True
         Width           =   2040
      End
      Begin VB.OptionButton optOccurs 
         Caption         =   "Start automatically when &SQL Agent starts"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   0
         Left            =   200
         TabIndex        =   2
         Top             =   270
         Width           =   4440
      End
   End
   Begin TDBText6Ctl.TDBText txtName 
      BeginProperty DataFormat 
         Type            =   0
         Format          =   "MM/dd/yy"
         HaveTrueFalseNull=   0
         FirstDayOfWeek  =   0
         FirstWeekOfYear =   0
         LCID            =   1033
         SubFormatType   =   0
      EndProperty
      Height          =   345
      Left            =   2750
      TabIndex        =   0
      Top             =   164
      Width           =   5670
      _Version        =   65536
      _ExtentX        =   10001
      _ExtentY        =   609
      Caption         =   "AIM_JobScheduleMaintenance.frx":0B22
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_JobScheduleMaintenance.frx":0B8E
      Key             =   "AIM_JobScheduleMaintenance.frx":0BAC
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   -1
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   128
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin VB.Frame fmGeneral 
      Caption         =   "One Time"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   2025
      Left            =   5033
      TabIndex        =   47
      Top             =   676
      Width           =   6705
      Begin VB.CheckBox optMonday 
         Caption         =   "Mon"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   200
         TabIndex        =   11
         Top             =   1150
         Width           =   1185
      End
      Begin VB.CheckBox optTuesday 
         Caption         =   "Tue"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   1462
         TabIndex        =   12
         Top             =   1150
         Width           =   1185
      End
      Begin VB.CheckBox optWednesday 
         Caption         =   "Wed"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   2724
         TabIndex        =   13
         Top             =   1150
         Width           =   1185
      End
      Begin VB.CheckBox optThursday 
         Caption         =   "Thu"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   3986
         TabIndex        =   14
         Top             =   1150
         Width           =   1185
      End
      Begin VB.CheckBox optFriday 
         Caption         =   "Fri"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   5250
         TabIndex        =   15
         Top             =   1150
         Width           =   1185
      End
      Begin VB.CheckBox optSaturday 
         Caption         =   "Sat"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   200
         TabIndex        =   16
         Top             =   1575
         Width           =   1185
      End
      Begin VB.CheckBox optSunday 
         Caption         =   "Sun"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   1462
         TabIndex        =   17
         Top             =   1575
         Width           =   1185
      End
      Begin TDBTime6Ctl.TDBTime txtOneTimeTime 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "hh:mm:ss AMPM"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   4
         EndProperty
         Height          =   345
         Left            =   5130
         TabIndex        =   9
         Top             =   300
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Caption         =   "AIM_JobScheduleMaintenance.frx":0BF0
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Keys            =   "AIM_JobScheduleMaintenance.frx":0C5C
         Spin            =   "AIM_JobScheduleMaintenance.frx":0CAC
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "hh:nn:ss AMPM"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "hh:nn:ss AMPM"
         HighlightText   =   0
         Hour12Mode      =   1
         IMEMode         =   3
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxTime         =   0.99999
         MidnightMode    =   0
         MinTime         =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "11:03:03 AM"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   0.460451388888889
      End
      Begin TDBNumber6Ctl.TDBNumber txtFrequencyInterval 
         Height          =   345
         Left            =   1700
         TabIndex        =   10
         Top             =   720
         Width           =   1300
         _Version        =   65536
         _ExtentX        =   2293
         _ExtentY        =   609
         Calculator      =   "AIM_JobScheduleMaintenance.frx":0CD4
         Caption         =   "AIM_JobScheduleMaintenance.frx":0CF4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":0D60
         Keys            =   "AIM_JobScheduleMaintenance.frx":0D7E
         Spin            =   "AIM_JobScheduleMaintenance.frx":0DC8
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   365
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBDate6Ctl.TDBDate txtOneTimeDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   1695
         TabIndex        =   8
         Top             =   300
         Width           =   1425
         _Version        =   65536
         _ExtentX        =   2514
         _ExtentY        =   609
         Calendar        =   "AIM_JobScheduleMaintenance.frx":0DF0
         Caption         =   "AIM_JobScheduleMaintenance.frx":0F08
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":0F74
         Keys            =   "AIM_JobScheduleMaintenance.frx":0F92
         Spin            =   "AIM_JobScheduleMaintenance.frx":0FF0
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "03/07/2002"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37322
         CenturyMode     =   0
      End
      Begin VB.Label txtOccursDescription 
         Caption         =   "day(s)"
         BeginProperty DataFormat 
            Type            =   0
            Format          =   "MM/dd/yy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   0
         EndProperty
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   3600
         TabIndex        =   51
         Top             =   750
         Width           =   1455
      End
      Begin VB.Label lblOneTimeTime 
         Caption         =   "At Time"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   3600
         TabIndex        =   50
         Top             =   345
         Width           =   1455
      End
      Begin VB.Label lblOneTimeDate 
         Caption         =   "On Date"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   49
         Top             =   345
         Width           =   1455
      End
      Begin VB.Label lblFrequencyInterval 
         Caption         =   "Every "
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   48
         Top             =   750
         Width           =   1455
      End
   End
   Begin VB.Frame fmMonthly 
      Caption         =   "Monthly"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   2028
      Left            =   5033
      TabIndex        =   40
      Top             =   676
      Width           =   6705
      Begin VB.OptionButton optRelative 
         Caption         =   "Day"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   0
         Left            =   180
         TabIndex        =   18
         Top             =   330
         Width           =   1185
      End
      Begin VB.OptionButton optRelative 
         Caption         =   "The"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Index           =   1
         Left            =   180
         TabIndex        =   19
         Top             =   1215
         Width           =   1185
      End
      Begin TDBNumber6Ctl.TDBNumber txtMR_FrequencyInterval 
         Height          =   340
         Left            =   1455
         TabIndex        =   20
         Top             =   330
         Width           =   840
         _Version        =   65536
         _ExtentX        =   1482
         _ExtentY        =   600
         Calculator      =   "AIM_JobScheduleMaintenance.frx":1018
         Caption         =   "AIM_JobScheduleMaintenance.frx":1038
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":10A4
         Keys            =   "AIM_JobScheduleMaintenance.frx":10C2
         Spin            =   "AIM_JobScheduleMaintenance.frx":110C
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   31
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtMR_FrequencyRecurrenceFactor 
         Height          =   345
         Left            =   2685
         TabIndex        =   21
         Top             =   690
         Width           =   840
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calculator      =   "AIM_JobScheduleMaintenance.frx":1134
         Caption         =   "AIM_JobScheduleMaintenance.frx":1154
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":11C0
         Keys            =   "AIM_JobScheduleMaintenance.frx":11DE
         Spin            =   "AIM_JobScheduleMaintenance.frx":1228
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   12
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFrequencyRelativeInterval 
         DataField       =   " "
         Height          =   340
         Left            =   1455
         TabIndex        =   22
         Top             =   1215
         Width           =   1140
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ColumnHeaders   =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   397
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   2011
         _ExtentY        =   609
         _StockProps     =   93
         BackColor       =   -2147483643
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcMR_FrequencyInterval 
         DataField       =   " "
         Height          =   340
         Left            =   2670
         TabIndex        =   23
         Top             =   1215
         Width           =   1800
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ColumnHeaders   =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   397
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   3175
         _ExtentY        =   609
         _StockProps     =   93
         BackColor       =   -2147483643
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin TDBNumber6Ctl.TDBNumber txtMR_FrequencyRecurrenceFactor2 
         Height          =   345
         Left            =   2670
         TabIndex        =   24
         Top             =   1575
         Width           =   840
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calculator      =   "AIM_JobScheduleMaintenance.frx":1250
         Caption         =   "AIM_JobScheduleMaintenance.frx":1270
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobScheduleMaintenance.frx":12DC
         Keys            =   "AIM_JobScheduleMaintenance.frx":12FA
         Spin            =   "AIM_JobScheduleMaintenance.frx":1344
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   12
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label lblMR_4 
         Caption         =   "month(s)"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   3645
         TabIndex        =   44
         Top             =   1620
         Width           =   1020
      End
      Begin VB.Label lblMR_3 
         Caption         =   "of every"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   1455
         TabIndex        =   43
         Top             =   1620
         Width           =   1140
      End
      Begin VB.Label lblMR_2 
         Caption         =   "month(s)"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   3645
         TabIndex        =   42
         Top             =   735
         Width           =   1020
      End
      Begin VB.Label lblMR_1 
         Caption         =   "of every"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   1455
         TabIndex        =   41
         Top             =   735
         Width           =   1140
      End
   End
   Begin VB.Label Label1 
      Caption         =   "Schedule Name"
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Left            =   200
      TabIndex        =   39
      Top             =   209
      Width           =   2450
   End
End
Attribute VB_Name = "AIM_JobScheduleMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection

Public rsSchedule As ADODB.Recordset

Public AddFlag As Boolean
Public CancelFlag As Boolean
Public JobName As String

Dim sp_add_jobschedule As ADODB.Command
Dim sp_update_jobschedule As ADODB.Command

Dim ScheduleName As String

Private Function InitSchedule()
On Error GoTo ErrorHandler
    
    'Occurs
    Me.optOccurs(2).Value = VALUETRUE        'One Time
    
    'General Frame
    Me.fmGeneral.Caption = ""
    
    Me.txtFrequencyInterval.Value = 1
    Me.optMonday.Value = VALUETRUE
    Me.optTuesday.Value = VALUETRUE
    Me.optWednesday.Value = VALUETRUE
    Me.optThursday.Value = VALUETRUE
    Me.optFriday.Value = VALUETRUE
    Me.optSaturday.Value = VALUEFALSE
    Me.optSunday.Value = VALUEFALSE
    
    'Relative Frame
    Me.optRelative(0).Value = VALUETRUE
    Me.txtMR_FrequencyInterval.Value = 1
    Me.txtMR_FrequencyRecurrenceFactor.Value = 1
    Me.dcFrequencyRelativeInterval.Text = getTranslationResource("1st")
    Me.dcMR_FrequencyInterval.Text = getTranslationResource("Sunday")
    Me.txtMR_FrequencyRecurrenceFactor2.Value = 1

    'Daily Frequency
    Me.optSubday(0).Value = VALUETRUE
    Me.txtFreqSubDayInterval.Value = 1
    Me.dcSubDay.Text = getTranslationResource("Hour(s)")
    
    
    'Duration
    Me.optEndDate(1).Value = VALUETRUE
    
    SetTDBDate Me.txtEndDate, "12/31/9999"
    SetTDBDate Me.txtOneTimeDate, Date
    SetTDBDate Me.txtStartDate, Date
    
    SetTDBTime Me.txtEndTimeOfDay, "11:59:00 PM"
    SetTDBTime Me.txtOneTimeTime, Time
    SetTDBTime Me.txtStartTimeofDay, Time
    SetTDBTime Me.txtStartTimeOfDay2, "12:00:00 AM"
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(InitSchedule)"
	 f_HandleErr , , , "AIM_JobMaintenance::InitSchedule", Now, gDRGeneralError, True, Err
End Function

Private Function RefreshForm()
On Error GoTo ErrorHandler
    
    InitSchedule                    'Initialize Schedule
    
    If Not Me.AddFlag Then          'Existing Schedule
        Me.txtName.Text = rsSchedule("schedule_name").Value
        Me.txtEnabled.Value = rsSchedule("enabled").Value
        ScheduleName = rsSchedule("schedule_name").Value

        Select Case rsSchedule("freq_type").Value
            Case 1      'Once
                Me.optOccurs(2).Value = VALUETRUE
                optOccurs_Click 2                   'Force refresh
                SetTDBDate Me.txtOneTimeDate, SQLDMO_BldDate(rsSchedule!Active_Start_Date)
                SetTDBTime Me.txtOneTimeTime, SQLDMO_BldTime(rsSchedule!Active_Start_Time)
    
            Case 4      'Daily
                Me.txtFrequencyInterval.MaxValue = 365  'days
                
                Me.optOccurs(3).Value = VALUETRUE
                
                'Set Daily Panel
                Me.txtFrequencyInterval = rsSchedule("freq_interval").Value
    
                SetSubDay                       'Set Subday Panel
                SetDuration                     'Set Duration Panel
    
            Case 8      'Weekly
                Me.txtFrequencyInterval.MaxValue = 52  'weeks
                
                Me.optOccurs(4).Value = VALUETRUE
    
                'Set Weekly Panel
                Me.txtFrequencyInterval = rsSchedule("freq_recurrence_factor").Value
    
                If rsSchedule("freq_interval").Value And 1 Then
                    Me.optSunday.Value = VALUETRUE
                Else
                    Me.optSunday.Value = VALUEFALSE
                End If
    
                If rsSchedule("freq_interval").Value And 2 Then
                    Me.optMonday.Value = VALUETRUE
                Else
                    Me.optMonday.Value = VALUEFALSE
                End If
    
                If rsSchedule("freq_interval").Value And 4 Then
                    Me.optTuesday.Value = VALUETRUE
                Else
                    Me.optTuesday.Value = VALUEFALSE
                End If
    
                If rsSchedule("freq_interval").Value And 8 Then
                    Me.optWednesday.Value = VALUETRUE
                Else
                    Me.optWednesday.Value = VALUEFALSE
                End If
    
                If rsSchedule("freq_interval").Value And 16 Then
                    Me.optThursday.Value = VALUETRUE
                Else
                    Me.optThursday.Value = VALUEFALSE
                End If
    
                If rsSchedule("freq_interval").Value And 32 Then
                    Me.optFriday.Value = VALUETRUE
                Else
                    Me.optFriday.Value = VALUEFALSE
                End If
    
                If rsSchedule("freq_interval").Value And 64 Then
                    Me.optSaturday.Value = VALUETRUE
                Else
                    Me.optSaturday.Value = VALUEFALSE
                End If
    
                SetSubDay                       'Set Subday Panel
                SetDuration                     'Set Duration Panel
    
            Case 16         'Monthly
                Me.optOccurs(5).Value = VALUETRUE
    
                'Set Monthly Panel
                Me.optRelative(0).Value = VALUETRUE
                Me.txtMR_FrequencyRecurrenceFactor.Value = rsSchedule("freq_recurrence_factor").Value
                Me.txtMR_FrequencyInterval.Value = rsSchedule("freq_interval").Value
    
                SetSubDay                       'Set Subday Panel
                SetDuration                     'Set Duration Panel
    
            Case 32         'Monthly - Relative to Frequency Interval
                Me.optOccurs(5).Value = VALUETRUE
    
                'Set Monthly Relative Panel
                Me.optRelative(1).Value = VALUETRUE
    
                Me.txtMR_FrequencyRecurrenceFactor2.Value = rsSchedule("freq_recurrence_factor").Value
                Select Case rsSchedule("freq_interval").Value
                    Case 1
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Sunday")
                    Case 2
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Monday")
                    Case 3
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Tuesday")
                    Case 4
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Wednesday")
                    Case 5
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Thursday")
                    Case 6
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Friday")
                    Case 7
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Saturday")
                    Case 8
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Day")
                    Case 9
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Week Day")
                    Case 10
                        Me.dcMR_FrequencyInterval.Text = getTranslationResource("Week End Day")
                End Select
    
                Select Case rsSchedule("freq_relative_interval").Value
                    Case 1
                        Me.dcFrequencyRelativeInterval.Text = getTranslationResource("1st")
                    Case 2
                        Me.dcFrequencyRelativeInterval.Text = getTranslationResource("2nd")
                    Case 4
                        Me.dcFrequencyRelativeInterval.Text = getTranslationResource("3rd")
                    Case 8
                        Me.dcFrequencyRelativeInterval.Text = getTranslationResource("4th")
                    Case 16
                        Me.dcFrequencyRelativeInterval.Text = getTranslationResource("Last")
                End Select
    
                SetSubDay                       'Set Subday Panel
                SetDuration                     'Set Duration Panel
    
            Case 64         'Auto Start
                Me.optOccurs(0).Value = VALUETRUE
                SetTDBDate Me.txtStartDate, SQLDMO_BldDate(rsSchedule!Active_Start_Date)
    
            Case 128        'When the computer is idle
                Me.optOccurs(0).Value = VALUETRUE
                SetTDBDate Me.txtStartDate, SQLDMO_BldDate(rsSchedule!Active_Start_Date)
            
        End Select

    Else
        Me.txtName.Text = ""
        Me.txtEnabled.Value = VALUETRUE
        ScheduleName = ""
        
        Me.optOccurs(2).Value = VALUETRUE
        optOccurs_Click 2                   'Force refresh
        
        SetTDBDate Me.txtOneTimeDate, Date
        SetTDBTime Me.txtOneTimeTime, Time
        
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(RefreshForm)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::RefreshForm", Now, gDRGeneralError, True, Err
End Function

Private Function SetDuration()
On Error GoTo ErrorHandler
    
    SetTDBDate Me.txtStartDate, SQLDMO_BldDate(rsSchedule!Active_Start_Date)
    SetTDBDate Me.txtEndDate, SQLDMO_BldDate(rsSchedule!Active_End_Date)
        
    'Check for No-End-Date
    If rsSchedule("active_end_date").Value = 99991231 Then
        Me.optEndDate(1).Value = VALUETRUE
    Else
        Me.optEndDate(0).Value = VALUETRUE
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetDuration)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::SetDuration", Now, gDRGeneralError, True, Err
End Function

Private Function SetFreqValues(FrequencyType As Long, FrequencyInterval As Long, FrequencySubDay As Long, _
    FrequencySubDayInterval As Long, FrequencyRelativeInterval As Long, FrequencyRecurrenceFactor As Long, _
    ActiveStartDate As Long, ActiveEndDate As Long, ActiveStartTimeofDay As Long, ActiveEndTimeofDay As Long)
On Error GoTo ErrorHandler
    
    Dim FreqType As Integer
    Dim i As Integer

    For i = Me.optOccurs.LBound To Me.optOccurs.UBound
        If Me.optOccurs(i).Value = True Then
            FreqType = i
            Exit For
        End If
    Next i

    Select Case FreqType
        Case 0              'Automatic
            FrequencyType = 64                         'Run when SQLServerAgent Service Starts
            FrequencyInterval = 0
            FrequencySubDay = 0
            FrequencySubDayInterval = 0
            FrequencyRelativeInterval = 0
            FrequencyRecurrenceFactor = 0
            ActiveStartDate = Me.txtStartDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            ActiveEndDate = 99991231
            ActiveStartTimeofDay = 0
            ActiveEndTimeofDay = 235959

        Case 1              'When CPU Idle
            FrequencyType = 128                        'Run when the computer is idle
            FrequencyInterval = 0
            FrequencySubDay = 0
            FrequencySubDayInterval = 0
            FrequencyRelativeInterval = 0
            FrequencyRelativeInterval = 0
            FrequencyRecurrenceFactor = 0
            ActiveStartDate = Me.txtStartDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            ActiveEndDate = 99991231
            ActiveStartTimeofDay = 0
            ActiveEndTimeofDay = 235959

        Case 2              'One Time
            FrequencyType = 1                          'One Time
            FrequencyInterval = 0
            FrequencySubDayInterval = 0
            FrequencySubDay = 1
            FrequencyRelativeInterval = 0
            FrequencyRecurrenceFactor = 0
            ActiveStartDate = Me.txtOneTimeDate.Number  'Is in the Long date format AIM_DATEVALUEFORMAT
            ActiveEndDate = 99991231
            ActiveStartTimeofDay = Me.txtOneTimeTime.Number 'Is in the Long date format AIM_DATEVALUEFORMAT
            ActiveEndTimeofDay = 235959

        Case 3              'Daily
            FrequencyInterval = Me.txtFrequencyInterval.Value
            FrequencyType = 4                          'Daily

            If Me.optSubday(0).Value = True Then
                FrequencySubDayInterval = 0
                FrequencySubDay = 1
                FrequencyRelativeInterval = 0
                FrequencyRecurrenceFactor = 0
                ActiveStartTimeofDay = Me.txtStartTimeofDay.Number  'Is in the Long date format AIM_DATEVALUEFORMAT
                ActiveEndTimeofDay = 235959
            Else
                FrequencySubDayInterval = Me.txtFreqSubDayInterval.Value
                If Me.dcSubDay.Value = getTranslationResource("Hour(s)") Then
                    FrequencySubDay = 8         'Hour
                ElseIf Me.dcSubDay.Value = getTranslationResource("Minute(s)") Then
                    FrequencySubDay = 4         'Minute
                Else
                    FrequencySubDay = 1         'Once
                End If

                FrequencyRelativeInterval = 0
                FrequencyRecurrenceFactor = 0
                ActiveStartTimeofDay = Me.txtStartTimeOfDay2.Number  'Is in the Long date format AIM_DATEVALUEFORMAT
                ActiveEndTimeofDay = Me.txtEndTimeOfDay.Number  'Is in the Long date format AIM_DATEVALUEFORMAT
            End If

            ActiveStartDate = Me.txtStartDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT

            If Me.optEndDate(0).Value = True Then
                ActiveEndDate = Me.txtEndDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            Else
                ActiveEndDate = 99991231
            End If

        Case 4              'Weekly
            FrequencyType = 8               'Weekly
            FrequencyRecurrenceFactor = Me.txtFrequencyInterval.Value

            'Build the Frequency Interval
            FrequencyInterval = 0

            If Me.optSunday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 1
            End If

            If Me.optMonday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 2
            End If

            If Me.optTuesday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 4
            End If

            If Me.optWednesday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 8
            End If

            If Me.optThursday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 16
            End If

            If Me.optFriday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 32
            End If

            If Me.optSaturday.Value = vbChecked Then
                FrequencyInterval = FrequencyInterval Or 64
            End If

            If Me.optSubday(0).Value = True Then
                FrequencySubDayInterval = 0
                FrequencySubDay = 1         'Once
                FrequencyRelativeInterval = 0
                ActiveStartTimeofDay = Me.txtStartTimeofDay.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
                ActiveEndTimeofDay = 235959
            Else
                FrequencySubDayInterval = Me.txtFreqSubDayInterval.Value
                If Me.dcSubDay.Value = getTranslationResource("Hour(s)") Then
                    FrequencySubDay = 8             'Hour
                ElseIf Me.dcSubDay.Value = getTranslationResource("Minute(s)") Then
                    FrequencySubDay = 4             'Minutes
                Else
                    FrequencySubDay = 1             'Once
                End If

                FrequencyRelativeInterval = 0
                ActiveStartTimeofDay = Me.txtStartTimeOfDay2.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
                ActiveEndTimeofDay = Me.txtEndTimeOfDay.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            End If

            ActiveStartDate = Me.txtStartDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT

            If Me.optEndDate(0).Value = True Then
                ActiveEndDate = Me.txtEndDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            Else
                ActiveEndDate = 99991231
            End If

        Case 5              'Monthly
            FrequencyType = 16                  'Monthly

            If Me.optRelative(0).Value = True Then
                FrequencyRecurrenceFactor = Me.txtMR_FrequencyRecurrenceFactor.Value
                FrequencyInterval = Me.txtMR_FrequencyInterval.Value
                FrequencyRelativeInterval = 0
            Else
                FrequencyRecurrenceFactor = Me.txtMR_FrequencyRecurrenceFactor2.Value

                Select Case Me.dcMR_FrequencyInterval.Text
                    Case getTranslationResource("Sunday")
                        FrequencyInterval = 1
                    Case getTranslationResource("Monday")
                        FrequencyInterval = 2
                    Case getTranslationResource("Tuesday")
                        FrequencyInterval = 3
                    Case getTranslationResource("Wednesday")
                        FrequencyInterval = 4
                    Case getTranslationResource("Thursday")
                        FrequencyInterval = 5
                    Case getTranslationResource("Friday")
                        FrequencyInterval = 6
                    Case getTranslationResource("Saturday")
                        FrequencyInterval = 7
                    Case getTranslationResource("Day")
                        FrequencyInterval = 8
                    Case getTranslationResource("Week Day")
                        FrequencyInterval = 9
                    Case getTranslationResource("Week End Day")
                        FrequencyInterval = 10
                End Select

                Select Case Me.dcFrequencyRelativeInterval.Text
                    Case getTranslationResource("1st")
                        FrequencyRelativeInterval = 1       'First
                    Case getTranslationResource("2nd")
                        FrequencyRelativeInterval = 2       'Second
                    Case getTranslationResource("3rd")
                        FrequencyRelativeInterval = 4       'Third
                    Case getTranslationResource("4th")
                        FrequencyRelativeInterval = 8       'Fourth
                    Case getTranslationResource("Last")
                        FrequencyRelativeInterval = 16      'Last
                    Case Else
                        FrequencyRelativeInterval = 0       'Unknown
                End Select
            End If

            If Me.optSubday(0).Value = True Then
                FrequencySubDayInterval = 0
                FrequencySubDay = 1
                ActiveStartTimeofDay = Me.txtStartTimeofDay.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
                ActiveEndTimeofDay = 235959
            Else
                FrequencySubDayInterval = Me.txtFreqSubDayInterval.Value
                If Me.dcSubDay.Value = getTranslationResource("Hour(s)") Then
                    FrequencySubDay = 8
                ElseIf Me.dcSubDay.Value = getTranslationResource("Minute(s)") Then
                    FrequencySubDay = 4
                Else
                    FrequencySubDay = 1
                End If

                ActiveStartTimeofDay = Me.txtStartTimeOfDay2.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
                ActiveEndTimeofDay = Me.txtEndTimeOfDay.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            End If

            ActiveStartDate = Me.txtStartDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT

            If Me.optEndDate(0).Value = True Then
                ActiveEndDate = Me.txtEndDate.Number    'Is in the Long date format AIM_DATEVALUEFORMAT
            Else
                ActiveEndDate = 99991231
            End If
    End Select
        
    SetFreqValues = SUCCEED

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetFreqValues)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::SetFreqValues", Now, gDRGeneralError, True, Err
End Function

Private Function SetSubDay()
On Error GoTo ErrorHandler
    
    Select Case rsSchedule("freq_subday_type").Value
        Case 1      'At the Specified Time
            Me.optSubday(0).Value = VALUETRUE
            SetTDBTime Me.txtStartTimeofDay, SQLDMO_BldTime(rsSchedule!Active_Start_Time)
            
        Case 4      'Minutes
            Me.optSubday(1).Value = VALUETRUE
            Me.txtFreqSubDayInterval.Value = rsSchedule("freq_subday_interval").Value
            Me.dcSubDay.Value = getTranslationResource("Minute(s)")
            SetTDBTime Me.txtStartTimeOfDay2, SQLDMO_BldTime(rsSchedule!Active_Start_Time)
            SetTDBTime Me.txtEndTimeOfDay, SQLDMO_BldTime(rsSchedule!Active_End_Time)
             
        Case 8      'Hours
            Me.optSubday(1).Value = VALUETRUE
            Me.txtFreqSubDayInterval.Value = rsSchedule("freq_subday_interval").Value
            Me.dcSubDay.Value = getTranslationResource("Hour(s)")
            SetTDBTime Me.txtStartTimeOfDay2, SQLDMO_BldTime(rsSchedule!Active_Start_Time)
            SetTDBTime Me.txtEndTimeOfDay, SQLDMO_BldTime(rsSchedule!Active_End_Time)
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetSubDay)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::SetSubDay", Now, gDRGeneralError, True, Err
End Function

Private Function SetVisibility(OccursOption As Integer)
On Error GoTo ErrorHandler
    
    Dim i As Integer

    Select Case OccursOption
        Case 0, 1         'Starts Automatically, When CPU is Idle
            Me.fmGeneral.Visible = True
            Me.fmMonthly.Visible = False
        
            Me.fmGeneral.Caption = ""
            
            Me.lblOneTimeDate.Visible = False
            Me.txtOneTimeDate.Visible = False
            Me.lblOneTimeTime.Visible = False
            Me.txtOneTimeTime.Visible = False
            Me.lblFrequencyInterval.Visible = False
            Me.txtFrequencyInterval.Visible = False
            Me.txtOccursDescription.Visible = False
            Me.optMonday.Visible = False
            Me.optTuesday.Visible = False
            Me.optWednesday.Visible = False
            Me.optThursday.Visible = False
            Me.optFriday.Visible = False
            Me.optSaturday.Visible = False
            Me.optSunday.Visible = False
            
            For i = Me.optSubday.LBound To Me.optSubday.UBound
                Me.optSubday(i).Visible = False
            Next i
            
            Me.txtStartTimeofDay.Visible = False
            Me.txtFreqSubDayInterval.Visible = False
            Me.dcSubDay.Visible = False
            Me.lblStartTimeofDay2.Visible = False
            Me.txtStartTimeOfDay2.Visible = False
            Me.lblEndTimeOfDay.Visible = False
            Me.txtEndTimeOfDay.Visible = False
            Me.lblStartDate.Visible = False
            Me.txtStartDate.Visible = False
            
            For i = Me.optEndDate.LBound To Me.optEndDate.UBound
                Me.optEndDate(i).Visible = False
            Next i
            
            Me.txtEndDate.Visible = False
        
        Case 2          'One Time
            Me.fmGeneral.Visible = True
            Me.fmMonthly.Visible = False
            
            Me.fmGeneral.Caption = ""
            
            Me.lblOneTimeDate.Visible = True
            Me.txtOneTimeDate.Visible = True
            Me.lblOneTimeTime.Visible = True
            Me.txtOneTimeTime.Visible = True
            Me.lblFrequencyInterval.Visible = False
            Me.txtFrequencyInterval.Visible = False
            Me.txtOccursDescription.Visible = False
            Me.optMonday.Visible = False
            Me.optTuesday.Visible = False
            Me.optWednesday.Visible = False
            Me.optThursday.Visible = False
            Me.optFriday.Visible = False
            Me.optSaturday.Visible = False
            Me.optSunday.Visible = False
            
            For i = Me.optSubday.LBound To Me.optSubday.UBound
                Me.optSubday(i).Visible = False
            Next i
            
            Me.txtStartTimeofDay.Visible = False
            Me.txtFreqSubDayInterval.Visible = False
            Me.dcSubDay.Visible = False
            Me.lblStartTimeofDay2.Visible = False
            Me.txtStartTimeOfDay2.Visible = False
            Me.lblEndTimeOfDay.Visible = False
            Me.txtEndTimeOfDay.Visible = False
            Me.lblStartDate.Visible = False
            Me.txtStartDate.Visible = False
            
            For i = Me.optEndDate.LBound To Me.optEndDate.UBound
                Me.optEndDate(i).Visible = False
            Next i
            
            Me.txtEndDate.Visible = False
        
        Case 3          'Daily
            Me.fmGeneral.Visible = True
            Me.fmMonthly.Visible = False
            
            Me.fmGeneral.Caption = getTranslationResource("Daily")
            
            Me.lblOneTimeDate.Visible = False
            Me.txtOneTimeDate.Visible = False
            Me.lblOneTimeTime.Visible = False
            Me.txtOneTimeTime.Visible = False
            Me.lblFrequencyInterval.Visible = True
            Me.txtFrequencyInterval.Visible = True
            
            Me.txtOccursDescription.Visible = True
            Me.txtOccursDescription.Caption = getTranslationResource("day(s)")
            
            Me.optMonday.Visible = False
            Me.optTuesday.Visible = False
            Me.optWednesday.Visible = False
            Me.optThursday.Visible = False
            Me.optFriday.Visible = False
            Me.optSaturday.Visible = False
            Me.optSunday.Visible = False
            
            For i = Me.optSubday.LBound To Me.optSubday.UBound
                Me.optSubday(i).Visible = True
            Next i
            
            Me.txtStartTimeofDay.Visible = True
            Me.txtFreqSubDayInterval.Visible = True
            Me.dcSubDay.Visible = True
            Me.lblStartTimeofDay2.Visible = True
            Me.txtStartTimeOfDay2.Visible = True
            Me.lblEndTimeOfDay.Visible = True
            Me.txtEndTimeOfDay.Visible = True
            Me.lblStartDate.Visible = True
            Me.txtStartDate.Visible = True
            
            For i = Me.optEndDate.LBound To Me.optEndDate.UBound
                Me.optEndDate(i).Visible = True
            Next i
            
            Me.txtEndDate.Visible = True
        
        Case 4          'Weekly
            Me.fmGeneral.Visible = True
            Me.fmMonthly.Visible = False
            
            Me.fmGeneral.Caption = getTranslationResource("Weekly")
            
            Me.lblOneTimeDate.Visible = False
            Me.txtOneTimeDate.Visible = False
            Me.lblOneTimeTime.Visible = False
            Me.txtOneTimeTime.Visible = False
            
            Me.lblFrequencyInterval.Visible = True
            Me.txtFrequencyInterval.Visible = True
            
            Me.txtOccursDescription.Visible = True
            Me.txtOccursDescription.Caption = getTranslationResource("week(s) on")
    
            Me.optMonday.Visible = True
            Me.optTuesday.Visible = True
            Me.optWednesday.Visible = True
            Me.optThursday.Visible = True
            Me.optFriday.Visible = True
            Me.optSaturday.Visible = True
            Me.optSunday.Visible = True
            
            For i = Me.optSubday.LBound To Me.optSubday.UBound
                Me.optSubday(i).Visible = True
            Next i
            
            Me.txtStartTimeofDay.Visible = True
            Me.txtFreqSubDayInterval.Visible = True
            Me.dcSubDay.Visible = True
            Me.lblStartTimeofDay2.Visible = True
            Me.txtStartTimeOfDay2.Visible = True
            Me.lblEndTimeOfDay.Visible = True
            Me.txtEndTimeOfDay.Visible = True
            Me.lblStartDate.Visible = True
            Me.txtStartDate.Visible = True
            
            For i = Me.optEndDate.LBound To Me.optEndDate.UBound
                Me.optEndDate(i).Visible = True
            Next i
            
            Me.txtEndDate.Visible = True
        
        Case 5          'Monthly
            Me.fmGeneral.Visible = False
            Me.fmMonthly.Visible = True
        
            Me.lblOneTimeDate.Visible = False
            Me.txtOneTimeDate.Visible = False
            Me.lblOneTimeTime.Visible = False
            Me.txtOneTimeTime.Visible = False
            
            Me.lblFrequencyInterval.Visible = False
            Me.txtFrequencyInterval.Visible = False
            
            Me.txtOccursDescription.Visible = False
            Me.txtOccursDescription.Caption = ""
    
            Me.optMonday.Visible = False
            Me.optTuesday.Visible = False
            Me.optWednesday.Visible = False
            Me.optThursday.Visible = False
            Me.optFriday.Visible = False
            Me.optSaturday.Visible = False
            Me.optSunday.Visible = False
            
            For i = Me.optSubday.LBound To Me.optSubday.UBound
                Me.optSubday(i).Visible = True
            Next i
            
            Me.txtStartTimeofDay.Visible = True
            Me.txtFreqSubDayInterval.Visible = True
            Me.dcSubDay.Visible = True
            Me.lblStartTimeofDay2.Visible = True
            Me.txtStartTimeOfDay2.Visible = True
            Me.lblEndTimeOfDay.Visible = True
            Me.txtEndTimeOfDay.Visible = True
            Me.lblStartDate.Visible = True
            Me.txtStartDate.Visible = True
            
            For i = Me.optEndDate.LBound To Me.optEndDate.UBound
                Me.optEndDate(i).Visible = True
            Next i
            
            Me.txtEndDate.Visible = True
    
    End Select

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(SetVisibility)"
	f_HandleErr , , , "AIM_JobScheduleMaintenance::SetVisibility", Now, gDRGeneralError, True, Err
End Function

Private Sub optOccurs_Click(Index As Integer)
On Error GoTo ErrorHandler
    
    Dim i As Integer
    
    'Get the current option
    For i = Me.optOccurs.LBound To Me.optOccurs.UBound
        If Me.optOccurs(i).Value = True Then
            Select Case i
                Case 0  '
                
                Case 1  '
                
                Case 2  'One Time
                
                Case 3  'Daily
                    Me.txtFrequencyInterval.MaxValue = 365  'days
                    
                Case 4  'Weekly
                    Me.txtFrequencyInterval.MaxValue = 52  'weeks
                    
                Case 5  'Monthly
                
            End Select
            Exit For
        End If
    Next i
            
    SetVisibility i

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optOccurs_Click)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::optOccurs_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optRelative_Click(Index As Integer)
On Error GoTo ErrorHandler
    
    If Me.optRelative(0).Value = True Then
        Me.txtMR_FrequencyInterval.Enabled = True
        Me.txtMR_FrequencyRecurrenceFactor.Enabled = True
        Me.lblMR_1.Enabled = True
        Me.lblMR_2.Enabled = True
        
        Me.dcFrequencyRelativeInterval.Enabled = False
        Me.dcMR_FrequencyInterval.Enabled = False
        Me.txtMR_FrequencyRecurrenceFactor2.Enabled = False
        Me.lblMR_3.Enabled = False
        Me.lblMR_4.Enabled = False
    Else
        Me.txtMR_FrequencyInterval.Enabled = False
        Me.txtMR_FrequencyRecurrenceFactor.Enabled = False
        Me.lblMR_1.Enabled = False
        Me.lblMR_2.Enabled = False
        
        Me.dcFrequencyRelativeInterval.Enabled = True
        Me.dcMR_FrequencyInterval.Enabled = True
        Me.txtMR_FrequencyRecurrenceFactor2.Enabled = True
        Me.lblMR_3.Enabled = True
        Me.lblMR_4.Enabled = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optRelative_Click)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::optRelative_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optSubday_Click(Index As Integer)
On Error GoTo ErrorHandler
    
    Dim i As Integer

    If Me.optSubday(0).Value = True Then
        Me.txtStartTimeofDay.Enabled = True
        
        Me.txtFreqSubDayInterval.Enabled = False
        Me.dcSubDay.Enabled = False
        Me.txtStartTimeOfDay2.Enabled = False
        Me.txtEndTimeOfDay.Enabled = False
        
    ElseIf Me.optSubday(1).Value = True Then
        Me.txtStartTimeofDay.Enabled = False
        
        Me.txtFreqSubDayInterval.Enabled = True
        Me.dcSubDay.Enabled = True
        Me.txtStartTimeOfDay2.Enabled = True
        Me.txtEndTimeOfDay.Enabled = True
    End If

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(optSubday_Click)"
	f_HandleErr , , , "AIM_JobScheduleMaintenance::optSubday_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler
    
    CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdOK_Click()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strText As String
    Dim ErrorDesc As String
        
    Dim FrequencyType As Long
    Dim FrequencyInterval As Long
    Dim FrequencySubDay As Long
    Dim FrequencySubDayInterval As Long
    Dim FrequencyRelativeInterval As Long
    Dim FrequencyRecurrenceFactor As Long
    Dim ActiveStartDate As Long
    Dim ActiveEndDate As Long
    Dim ActiveStartTimeofDay As Long
    Dim ActiveEndTimeofDay As Long
    
    CancelFlag = False
    
    'Parse the user's input
    RtnCode = SetFreqValues(FrequencyType, FrequencyInterval, FrequencySubDay, FrequencySubDayInterval, _
        FrequencyRelativeInterval, FrequencyRecurrenceFactor, ActiveStartDate, ActiveEndDate, _
        ActiveStartTimeofDay, ActiveEndTimeofDay)

    If AddFlag Then
        With sp_add_jobschedule
            .Parameters("@job_id").Value = Null
            .Parameters("@job_name").Value = JobName
            .Parameters("@name").Value = Me.txtName.Text
            .Parameters("@enabled").Value = Me.txtEnabled.Value
            .Parameters("@freq_type").Value = FrequencyType
            .Parameters("@freq_interval").Value = FrequencyInterval
            .Parameters("@freq_subday_type").Value = FrequencySubDay
            .Parameters("@freq_subday_interval").Value = FrequencySubDayInterval
            .Parameters("@freq_relative_interval").Value = FrequencyRelativeInterval
            .Parameters("@freq_recurrence_factor").Value = FrequencyRecurrenceFactor
            .Parameters("@active_start_date").Value = ActiveStartDate
            .Parameters("@active_end_Date").Value = ActiveEndDate
            .Parameters("@active_start_time").Value = ActiveStartTimeofDay
            .Parameters("@active_end_time").Value = ActiveEndTimeofDay
            
            .Execute
            
            'Check for error on execution
             If .Parameters(0).Value = 1 Then
                MsgBox Err.Description, vbCritical + vbOKOnly
                Exit Sub
            End If
        End With
    
    Else
        With sp_update_jobschedule
            .Parameters("@job_id").Value = Null
            .Parameters("@job_name").Value = JobName
            .Parameters("@name").Value = ScheduleName
            .Parameters("@new_name").Value = Me.txtName.Text
            .Parameters("@enabled").Value = Me.txtEnabled.Value
            .Parameters("@freq_type").Value = FrequencyType
            .Parameters("@freq_interval").Value = FrequencyInterval
            .Parameters("@freq_subday_type").Value = FrequencySubDay
            .Parameters("@freq_subday_interval").Value = FrequencySubDayInterval
            .Parameters("@freq_relative_interval").Value = FrequencyRelativeInterval
            .Parameters("@freq_recurrence_factor").Value = FrequencyRecurrenceFactor
            .Parameters("@active_start_date").Value = ActiveStartDate
            .Parameters("@active_end_Date").Value = ActiveEndDate
            .Parameters("@active_start_time").Value = ActiveStartTimeofDay
            .Parameters("@active_end_time").Value = ActiveEndTimeofDay
            
            .Execute
            
            'Check for error on execution
             If .Parameters(0).Value = 1 Then
                MsgBox Err.Description, vbCritical + vbOKOnly
                Exit Sub
            End If
        End With
    End If
    
    'If everything executed ok -- return to calling form
    Unload Me
        
Exit Sub
ErrorHandler:
    'check for known errors:
    If Err.Number = -2147217900 _
    Then
        ErrorDesc = Err.Description
        strMessage = getTranslationResource("MSGBOX02900")
        If StrComp(strMessage, "MSGBOX02900") = 0 Then strMessage = "Error processing job."
        strText = getTranslationResource(Me.Caption)
        MsgBox strMessage + vbCrLf + vbCrLf + ErrorDesc, vbCritical + vbOKOnly, strText
    Else
        'f_HandleErr Me.Caption & "(cmdOK_Click)"
		 f_HandleErr , , , "AIM_JobScheduleMaintenance::cmdOK_Click", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub dcFrequencyRelativeInterval_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dcFrequencyRelativeInterval.Columns(0).Name = "Frequency"
    Me.dcFrequencyRelativeInterval.Columns(0).Caption = ""
    Me.dcFrequencyRelativeInterval.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcFrequencyRelativeInterval.Columns(0).Width = 1000
    
    Me.dcFrequencyRelativeInterval.AddItem getTranslationResource("1st")
    Me.dcFrequencyRelativeInterval.AddItem getTranslationResource("2nd")
    Me.dcFrequencyRelativeInterval.AddItem getTranslationResource("3rd")
    Me.dcFrequencyRelativeInterval.AddItem getTranslationResource("4th")
    Me.dcFrequencyRelativeInterval.AddItem getTranslationResource("Last")
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcFrequencyRelativeInterval, ACW_EXPAND
    End If

    For IndexCounter = 0 To dcFrequencyRelativeInterval.Columns.Count - 1
'        dcFrequencyRelativeInterval.Columns(IndexCounter).HasHeadBackColor = True
'        dcFrequencyRelativeInterval.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcFrequencyRelativeInterval_InitColumnProps)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::dcFrequencyRelativeInterval_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcMR_FrequencyInterval_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dcMR_FrequencyInterval.Columns(0).Name = "Frequency"
    Me.dcMR_FrequencyInterval.Columns(0).Caption = ""
    Me.dcMR_FrequencyInterval.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcMR_FrequencyInterval.Columns(0).Width = 1000
    
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Sunday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Monday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Tuesday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Wednesday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Thursday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Friday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Saturday")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Day")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Week Day")
    Me.dcMR_FrequencyInterval.AddItem getTranslationResource("Week End Day")
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcMR_FrequencyInterval, ACW_EXPAND
    End If

    For IndexCounter = 0 To dcMR_FrequencyInterval.Columns.Count - 1
'        dcMR_FrequencyInterval.Columns(IndexCounter).HasHeadBackColor = True
'        dcMR_FrequencyInterval.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcMR_FrequencyInterval_InitColumnProps)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::dcMR_FrequencyInterval_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcSubDay_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dcSubDay.Columns(0).Name = "Frequency"
    Me.dcSubDay.Columns(0).Caption = ""
    Me.dcSubDay.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcSubDay.Columns(0).Width = 1000
    
    Me.dcSubDay.AddItem getTranslationResource("Hour(s)")
    Me.dcSubDay.AddItem getTranslationResource("Minute(s)")
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcSubDay, ACW_EXPAND
    End If

    For IndexCounter = 0 To dcSubDay.Columns.Count - 1
'        dcSubDay.Columns(IndexCounter).HasHeadBackColor = True
'        dcSubDay.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
    Next

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dcSubDay_InitColumnProps)"
	f_HandleErr , , , "AIM_JobScheduleMaintenance::dcSubDay_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    'Set Default for Cancel Flag
    CancelFlag = False
    
    'Initialize Stored Procedures
    Set sp_add_jobschedule = New ADODB.Command
    With sp_add_jobschedule
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_add_jobschedule"
        .Parameters.Refresh
    End With
    
    Set sp_update_jobschedule = New ADODB.Command
    With sp_update_jobschedule
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_update_jobschedule"
        .Parameters.Refresh
    End With

    If AddFlag Then
        SetVisibility 2
    End If

    '************************************************************
    'Mar 05 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtFreqSubDayInterval.Spin.Visible = 1
    Me.txtFrequencyInterval.Spin.Visible = 1
    Me.txtMR_FrequencyInterval.Spin.Visible = 1
    Me.txtMR_FrequencyRecurrenceFactor.Spin.Visible = 1
    Me.txtMR_FrequencyRecurrenceFactor2.Spin.Visible = 1
    Me.txtStartDate.DropDown.Visible = 1
    Me.txtOneTimeDate.DropDown.Visible = 1
    Me.txtEndDate.DropDown.Visible = 1
    Me.txtEndTimeOfDay.Spin.Visible = 1
    Me.txtOneTimeTime.Spin.Visible = 1
    Me.txtStartTimeofDay.Spin.Visible = 1
    Me.txtStartTimeOfDay2.Spin.Visible = 1
    '************************************************************
    
    RefreshForm
    
    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
	f_HandleErr , , , "AIM_JobScheduleMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)

    If Not (sp_add_jobschedule Is Nothing) Then Set sp_add_jobschedule.ActiveConnection = Nothing
    If Not (sp_update_jobschedule Is Nothing) Then Set sp_update_jobschedule.ActiveConnection = Nothing
    
    Set sp_add_jobschedule = Nothing
    Set sp_update_jobschedule = Nothing
    
End Sub

Private Sub optEndDate_Click(Index As Integer)
On Error GoTo ErrorHandler
        
    If Me.optEndDate(0).Value = True Then
        Me.txtEndDate.Enabled = True
    ElseIf Me.optEndDate(1).Value = True Then
        Me.txtEndDate.Enabled = False
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optEndDate_Click)"
	 f_HandleErr , , , "AIM_JobScheduleMaintenance::optEndDate_Click", Now, gDRGeneralError, True, Err
End Sub

