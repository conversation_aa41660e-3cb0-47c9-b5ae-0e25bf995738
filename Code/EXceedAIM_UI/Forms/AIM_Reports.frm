VERSION 5.00
Object = "{A45D986F-3AAF-4A3B-A003-A6C53E8715A2}#1.0#0"; "ARVIEW2.OCX"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{F9043C88-F6F2-101A-A3C9-08002B2F49FB}#1.2#0"; "COMDLG32.OCX"
Begin VB.Form AIM_Reports 
   Caption         =   "SSA DR Reports -- Print Preview"
   ClientHeight    =   4800
   ClientLeft      =   75
   ClientTop       =   345
   ClientWidth     =   8070
   Icon            =   "AIM_Reports.frx":0000
   LinkTopic       =   "Form1"
   ScaleHeight     =   4800
   ScaleWidth      =   8070
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   WindowState     =   2  'Maximized
   Begin MSComDlg.CommonDialog ComDialog 
      Left            =   936
      Top             =   4356
      _ExtentX        =   688
      _ExtentY        =   688
      _Version        =   393216
   End
   Begin ActiveToolBars.SSActiveToolBars tbReports 
      Left            =   144
      Top             =   4356
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   6
      Tools           =   "AIM_Reports.frx":030A
      ToolBars        =   "AIM_Reports.frx":1232
   End
   Begin DDActiveReportsViewer2Ctl.ARViewer2 ARViewer 
      Height          =   4215
      Left            =   30
      TabIndex        =   0
      Top             =   15
      Width           =   8010
      _ExtentX        =   14129
      _ExtentY        =   7435
      SectionData     =   "AIM_Reports.frx":12A7
   End
End
Attribute VB_Name = "AIM_Reports"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Function GetSaveFile(cmdialog As CommonDialog, _
    Optional FileName As String = "", _
    Optional DlgTitle As String = "Save File", _
    Optional Filter As String = "All Files (*.*)| *.*", _
    Optional FilterIndex As Long = 1, _
    Optional Owner As Long = -1)

On Error GoTo ErrorHandler

    cmdialog.CancelError = True ' Causes a trappable error to occur when the user hits the 'Cancel' button
    cmdialog.DialogTitle = DlgTitle
    cmdialog.FileName = FileName
    cmdialog.Filter = Filter
    cmdialog.FilterIndex = FilterIndex
    cmdialog.Flags = cdlOFNOverwritePrompt
    
    cmdialog.ShowSave
    
    If Not Err = cdlCancel Then
        GetSaveFile = cmdialog.FileName
    Else
        GetSaveFile = ""
    End If

Exit Function
ErrorHandler:
    If Err = cdlCancel Then Exit Function
    'Err.Raise Err.Number, Err.source & "(GetSaveFile)", Err.Description
    f_HandleErr , , , "AIM_Reports::GetSaveFile", Now, gDRGeneralError, True, Err
End Function

Private Sub ARViewer_Error(ByVal Number As Integer, ByVal Description As String, ByVal Scode As Long, ByVal source As String, ByVal HelpFile As String, ByVal HelpContext As Long, ByVal CancelDisplay As DDActiveReportsViewer2Ctl.IReturnBool)
    Dim ErrorDesc As String
    Dim ErrorID As String
    Dim ErrorMessage As String

    ErrorID = "ERRMSG1" & Number

    ErrorDesc = getTranslationResource(ErrorID)
    If StrComp(ErrorDesc, ErrorID) = 0 Then ErrorDesc = Description

    Err.Clear
    Err.Number = Number
    Err.Description = ErrorDesc & "<ActiveReports>"
    f_HandleErr Me.Caption

End Sub

Private Sub ARViewer_ToolbarClick(ByVal Tool As DDActiveReportsViewer2Ctl.DDTool)
On Error GoTo ErrorHandler

    If Tool.ID = 999 Then
        If Not Me.ARViewer.ReportSource Is Nothing Then
            Me.ARViewer.ReportSource.Stop
        End If
    End If
  
Exit Sub
ErrorHandler:
    '_HandleErr Me.Caption & "(ARViewer_ToolbarClick)"
     f_HandleErr , , , "AIM_Reports::ARViewer_ToolbarClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Screen.MousePointer = vbHourglass
    
    'Add the Stop Function to the toolbar
    With Me.ARViewer.Toolbar.Tools
    .Add "Stop"
    .Item(.Count - 1).ID = 999
    .Item(.Count - 1).Caption = getTranslationResource("&Stop")
    .Item(.Count - 1).Tooltip = getTranslationResource("Stop Retrieval")
    End With
    
    'Set the width of the Table of Contents
    Me.ARViewer.TOCWidth = 2880
    
    'Reset mouse pointer
    Screen.MousePointer = vbDefault

    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_Reports::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Resize()
On Error GoTo ErrorHandler
    
    Me.ARViewer.Move 0, 0, Me.ScaleWidth, Me.ScaleHeight

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Form_Resize)"
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If Not Me.ARViewer.ReportSource Is Nothing Then
        Me.ARViewer.ReportSource.Stop
    End If

    Set Me.ARViewer.Object = Nothing
    Set Me.ARViewer.ReportSource = Nothing
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Unload)"
     f_HandleErr , , , "AIM_Reports::Form_UnLoad", Now, gDRGeneralError, True, Err
    
    Resume Next
End Sub

Private Sub tbReports_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim bSave As Boolean
    Dim AR_Excel As ActiveReportsExcelExport.ARExportExcel
    Dim AR_PDF As ActiveReportsPDFExport.ARExportPDF
    Dim AR_RTF As ActiveReportsRTFExport.ARExportRTF
    Dim AR_TXT As ActiveReportsTextExport.ARExportText
    Dim FileName As String
    
    Select Case Tool.ID
    
    Case "ID_ExcelExport"
        Set AR_Excel = New ActiveReportsExcelExport.ARExportExcel
        FileName = GetSaveFile(Me.ComDialog, FileName, _
            getTranslationResource("Export to Excel"), _
            "Microsoft Excel Format (*.XLS)| *.XLS", 1, Me.hwnd)

        If FileName <> "" Then
            AR_Excel.FileName = FileName
        Else
            Exit Sub
        End If
        
        'Set the Excel Export Properties
        AR_Excel.AutoRowHeight = True
        AR_Excel.GenPagebreaks = True
        AR_Excel.MinColumnWidth = 200
        AR_Excel.TrimEmptySpace = False
        
        'AR_Excel.ShowMarginSpace = True
        'xls.MinRowHeight = 26
        'xls.MultiSheet = False
        'xls.BorderSpace = 0
        'xls.DoubleBoundaries = True
    
        If ARViewer.Pages.Count > 0 Then
            AR_Excel.Export ARViewer.Pages
        ElseIf Not ARViewer.ReportSource Is Nothing Then
            If ARViewer.ReportSource.Pages.Count > 0 Then
                AR_Excel.Export ARViewer.ReportSource.Pages
            End If
        End If
        Set AR_Excel = Nothing
    
    Case "ID_TextExport"
        Set AR_TXT = New ActiveReportsTextExport.ARExportText
        Load AIM_TextExportOptions
        With AIM_TextExportOptions
            .Show vbModal
            If Not .Cancelled Then
                AR_TXT.FileName = .txtFileName.Text
                AR_TXT.PageDelimiter = .txtPageDelimiter.Text
                AR_TXT.TextDelimiter = .txtFieldDelimiter.Text
                AR_TXT.Unicode = (.chkUnicode.Value = 1)
                AR_TXT.SuppressEmptyLines = (.chkSuppressBlank.Value = 1)
                If Me.ARViewer.Pages.Count > 0 Then
                    AR_TXT.Export Me.ARViewer.Pages
                ElseIf Not Me.ARViewer.ReportSource Is Nothing Then
                    If Me.ARViewer.ReportSource.Pages.Count > 0 Then
                        AR_TXT.Export Me.ARViewer.ReportSource.Pages
                    End If
                End If
            End If
        End With
        
        Unload AIM_TextExportOptions
        Set AR_TXT = Nothing
    
    Case "ID_PDFExport"
        Set AR_PDF = New ActiveReportsPDFExport.ARExportPDF
        FileName = GetSaveFile(Me.ComDialog, FileName, _
            getTranslationResource("Export to PDF"), _
            "Portable Document Format (*.PDF)| *.PDF", 1, Me.hwnd)

        If FileName <> "" Then
            AR_PDF.FileName = FileName
        Else
            Exit Sub
        End If

        If ARViewer.Pages.Count > 0 Then
            AR_PDF.Export ARViewer.Pages
        ElseIf Not ARViewer.ReportSource Is Nothing Then
            If ARViewer.ReportSource.Pages.Count > 0 Then
                AR_PDF.Export ARViewer.ReportSource.Pages
            End If
        End If
        Set AR_PDF = Nothing
    
    Case "ID_RTFExport"
        Set AR_RTF = New ActiveReportsRTFExport.ARExportRTF
        FileName = GetSaveFile(Me.ComDialog, FileName, _
            getTranslationResource("Export to RTF"), _
            "Rich Text Format (*.RTF)| *.RTF", 1, Me.hwnd)
    
        If FileName <> "" Then
            AR_RTF.FileName = FileName
        Else
            Exit Sub
        End If
    
        If Me.ARViewer.Pages.Count > 0 Then
            AR_RTF.Export Me.ARViewer.Pages
        ElseIf Not Me.ARViewer.ReportSource Is Nothing Then
            If Me.ARViewer.ReportSource.Pages.Count > 0 Then
                AR_RTF.Export Me.ARViewer.ReportSource.Pages
            End If
        End If
        
        Set AR_RTF = Nothing
        
    Case "ID_Exit"
        Unload Me
    
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbReports_ToolClick)"
     f_HandleErr , , , "AIM_Reports::tbReports_ToolClick", Now, gDRGeneralError, True, Err
    On Error Resume Next
    Set AR_Excel = Nothing
    Set AR_PDF = Nothing
    Set AR_RTF = Nothing
    Set AR_TXT = Nothing
End Sub

