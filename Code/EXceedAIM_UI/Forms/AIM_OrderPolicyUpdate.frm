VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_OrderPolicyUpdate 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "AIM Order Policy Update"
   ClientHeight    =   2805
   ClientLeft      =   30
   ClientTop       =   270
   ClientWidth     =   5730
   Icon            =   "AIM_OrderPolicyUpdate.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   2805
   ScaleWidth      =   5730
   ShowInTaskbar   =   0   'False
   StartUpPosition =   2  'CenterScreen
   Begin VB.Frame Frame2 
      Height          =   768
      Left            =   36
      TabIndex        =   7
      Top             =   2016
      Width           =   5664
      Begin VB.CommandButton cmdClose 
         Caption         =   "&Close"
         Height          =   444
         Left            =   3384
         TabIndex        =   4
         Top             =   216
         Width           =   2172
      End
      Begin VB.CommandButton cmdUpdate 
         Caption         =   "&Update Order Policies..."
         Height          =   444
         Left            =   144
         TabIndex        =   3
         Top             =   180
         Width           =   2172
      End
   End
   Begin VB.Frame Frame1 
      Height          =   2064
      Left            =   36
      TabIndex        =   5
      Top             =   -36
      Width           =   5664
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLcid 
         Height          =   330
         Left            =   2220
         TabIndex        =   2
         Top             =   1290
         Width           =   1200
         DataFieldList   =   "lcid"
         AllowInput      =   0   'False
         _Version        =   196617
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2117
         _ExtentY        =   593
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         Enabled         =   0   'False
      End
      Begin VB.OptionButton optLcid 
         Caption         =   "&Selected Location"
         Height          =   300
         Index           =   1
         Left            =   288
         TabIndex        =   1
         Top             =   1296
         Width           =   1848
      End
      Begin VB.OptionButton optLcid 
         Caption         =   "&All Locations"
         Height          =   300
         Index           =   0
         Left            =   288
         TabIndex        =   0
         Top             =   936
         Width           =   1848
      End
      Begin VB.Label Label1 
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Caption         =   "This program updates the Order Policy Data Elements in the AIM Item Table for the Locations Specified."
         Height          =   564
         Left            =   72
         TabIndex        =   6
         Top             =   180
         Width           =   5508
      End
   End
End
Attribute VB_Name = "AIM_OrderPolicyUpdate"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As New ADODB.Connection
    
Dim rsLocations As New ADODB.Recordset

Private Type IT_RCD

    Lcid As String * 12
    item As String * 25
    Revcycle As String * 8
    cost As Double
    LT As Integer
    cstock As Long
    SSAdj As Double
    dd As Double
    MADSS As Double
    Trndp As Double
    LtvFact  As Double
    fcstcycles As Long
    ZeroCount As Long
    madstat As String * 1
    FctUpdCyc As Long
    hitrndl As Double
    PlnTT As String * 1
    ZSStock As String * 1
    dser As Double
    accum_lt As Integer
    reviewtime As Integer
    orderpt As Long
    orderqty As Long
    safetystock As Double
    fcstrt As Double
    fcstlt As Double
    fcst_month As Double
    fcst_quarter As Double
    fcst_year As Double
    fcstdate As Variant
    dflt_turnlow As Double
    dflt_turnhigh As Double
    PutAwayDays As Integer

End Type

Private Type SA_RCD

    said As String * 20
    sadesc As String * 30
    sastatus As String * 1
    bi(1 To 52) As Double
    itmcnt As Long

End Type

Private Const CALT_WEEKLY = 0
Private Const CALT_MONTHLY = 1
Private Const CALT_RETAIL544 = 2
Private Const CALT_RETAIL454 = 3
Private Const CALT_RETAIL445 = 4
Private Const CALT_RETAIL13 = 5

'**********************************************************************
' Function Name:    GetFiscalBegOfYear(TargetDate, BegOfYear, Calt)
'
' Description:      Returns the beginning date of the fiscal year
'                   in which a target date falls. (ie. "1/1/96")
'
' Parameters:       TargetDate  Target Date
'                   BegofYear   Beginning of Current Fiscal Year
'                   Calt        Calendar Type
'
' Returns:          Beginning date of fiscal year (see above)
'
' By: UKK           Date: 1/31/97
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995-1997
'
'**********************************************************************
Private Function GetFiscalBegOfYear(TargetDate As Variant, BegofYear As Variant, CALT As Integer) As Variant

    Dim FYBegin As Variant
    
    If CALT = CALT_MONTHLY Then

        'First adjust to same calendar year, then if target date is before
        'the FYBegin date, adjust FYBegin back one year.
        
        FYBegin = DateSerial(Year(TargetDate), Month(BegofYear), Day(BegofYear))
    
        If CVDate(FYBegin) > CVDate(TargetDate) Then
    
            FYBegin = DateSerial(Year(FYBegin) - 1, Month(FYBegin), Day(FYBegin))
    
        End If

    Else      'all other calendar types correspond to a 52-week year

        'Calculate the beginning of year date for the fiscal year in which the
        'target date falls.

        If DateDiff("d", BegofYear, TargetDate) / 7 >= 52 Then     'TargetDate is in a future FY

            FYBegin = DateAdd("d", 364, BegofYear)

            If DateDiff("d", FYBegin, TargetDate) / 7 >= 52 Then
            
                Do Until DateDiff("d", FYBegin, TargetDate) / 7 < 52
    
                    FYBegin = DateAdd("d", 364, FYBegin)
    
                Loop

            End If
    
        ElseIf DateDiff("d", BegofYear, TargetDate) / 7 < 0 Then   'TargetDate is in a past FY
            
            FYBegin = DateAdd("d", -364, BegofYear)

            If DateDiff("d", FYBegin, TargetDate) / 7 < 0 Then
            
                Do Until DateDiff("d", FYBegin, TargetDate) / 7 >= 0
    
                    FYBegin = DateAdd("d", -364, FYBegin)
    
                Loop

            End If
    
        Else
            
            FYBegin = BegofYear

        End If
    
    End If

    GetFiscalBegOfYear = FYBegin

End Function


'**********************************************************************
' Function Name:    GetStartDate(Period, CurYear, BegOfYear, CurFiscalYear, CalType)
'
' Description:      Returns the 1st Day of the Period specified.
'
' Parameters:       Period          Current Period
'                   CurYear         Current Year
'                   BegOfYear       Beginning of Year from Company Table
'                   CurFiscalYear   Current Fiscal Year from Company Table
'                   CalType         Calendar Type
'
' Returns:
'
' By: RES         Date: 12/12/95
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995
'
'**********************************************************************
Private Function GetStartDate(period As Integer, CurYear As Integer, BegofYear As Variant, CurFiscalYear As Integer, CalType As Integer) As Variant
    
    Dim X As Variant
    Dim AdjBegOfYear As Variant
    
    'Adjust for change in Fiscal Year
    
    If CurYear = 0 Then
        CurYear = CurFiscalYear
    End If
    
    If CurYear <> CurFiscalYear Then
    
        Select Case CalType

        Case CALT_WEEKLY
            AdjBegOfYear = DateAdd("ww", (CurYear - CurFiscalYear) * 52, BegofYear)
    
        Case CALT_MONTHLY
            AdjBegOfYear = DateAdd("m", (CurYear - CurFiscalYear) * 12, BegofYear)

        Case CALT_RETAIL544
            AdjBegOfYear = DateAdd("ww", (CurYear - CurFiscalYear) * 52, BegofYear)

        Case CALT_RETAIL454
            AdjBegOfYear = DateAdd("ww", (CurYear - CurFiscalYear) * 52, BegofYear)
        
        Case CALT_RETAIL445
            AdjBegOfYear = DateAdd("ww", (CurYear - CurFiscalYear) * 52, BegofYear)
    
        Case CALT_RETAIL13
            AdjBegOfYear = DateAdd("ww", (CurYear - CurFiscalYear) * 52, BegofYear)
        
        End Select
    
    Else
    
        AdjBegOfYear = BegofYear
        
    End If
    
    Select Case CalType
    
    Case CALT_WEEKLY
    
        X = DateAdd("ww", period - 1, AdjBegOfYear)
    
    Case CALT_MONTHLY
    
        X = DateAdd("m", period - 1, AdjBegOfYear)

    Case CALT_RETAIL544
    
        Select Case period
        
        Case 1
            X = AdjBegOfYear
        Case 2
            X = DateAdd("ww", 6, AdjBegOfYear)
        Case 3
            X = DateAdd("ww", 10, AdjBegOfYear)
        Case 4
            X = DateAdd("ww", 14, AdjBegOfYear)
        Case 5
            X = DateAdd("ww", 19, AdjBegOfYear)
        Case 6
            X = DateAdd("ww", 23, AdjBegOfYear)
        Case 7
            X = DateAdd("ww", 28, AdjBegOfYear)
        Case 8
            X = DateAdd("ww", 32, AdjBegOfYear)
        Case 9
            X = DateAdd("ww", 36, AdjBegOfYear)
        Case 10
            X = DateAdd("ww", 41, AdjBegOfYear)
        Case 11
            X = DateAdd("ww", 45, AdjBegOfYear)
        Case 12
            X = DateAdd("ww", 49, AdjBegOfYear)

        End Select

    Case CALT_RETAIL454
        
        Select Case period
        
        Case 1
            X = AdjBegOfYear
        Case 2
            X = DateAdd("ww", 5, AdjBegOfYear)
        Case 3
            X = DateAdd("ww", 10, AdjBegOfYear)
        Case 4
            X = DateAdd("ww", 14, AdjBegOfYear)
        Case 5
            X = DateAdd("ww", 18, AdjBegOfYear)
        Case 6
            X = DateAdd("ww", 23, AdjBegOfYear)
        Case 7
            X = DateAdd("ww", 27, AdjBegOfYear)
        Case 8
            X = DateAdd("ww", 31, AdjBegOfYear)
        Case 9
            X = DateAdd("ww", 36, AdjBegOfYear)
        Case 10
            X = DateAdd("ww", 40, AdjBegOfYear)
        Case 11
            X = DateAdd("ww", 44, AdjBegOfYear)
        Case 12
            X = DateAdd("ww", 49, AdjBegOfYear)

        End Select
    
    Case CALT_RETAIL445
        
        Select Case period
        
        Case 1
            X = AdjBegOfYear
        Case 2
            X = DateAdd("ww", 5, AdjBegOfYear)
        Case 3
            X = DateAdd("ww", 9, AdjBegOfYear)
        Case 4
            X = DateAdd("ww", 14, AdjBegOfYear)
        Case 5
            X = DateAdd("ww", 18, AdjBegOfYear)
        Case 6
            X = DateAdd("ww", 22, AdjBegOfYear)
        Case 7
            X = DateAdd("ww", 27, AdjBegOfYear)
        Case 8
            X = DateAdd("ww", 31, AdjBegOfYear)
        Case 9
            X = DateAdd("ww", 35, AdjBegOfYear)
        Case 10
            X = DateAdd("ww", 40, AdjBegOfYear)
        Case 11
            X = DateAdd("ww", 44, AdjBegOfYear)
        Case 12
            X = DateAdd("ww", 48, AdjBegOfYear)

        End Select
    
    Case CALT_RETAIL13
    
        X = DateAdd("ww", (period - 1) * 4, AdjBegOfYear)

    End Select

    GetStartDate = X

End Function

'**********************************************************************
' Function Name:    OrderPolicyUpdate(SQLConn, it, sa, co, it.dflt_turnhigh,
'                       dflt_turnlow, Excpt_Flag)
'
' Description:      This function updates the order policy data elements
'                   in an item record for subsequent use by the order
'                   generation module.
'
' Parameters:       it              Item Record
'                   sa              Seasonality Record
'                   co              Company Record
'
' Returns:          FAIL
'                   SUCCEED
'
' By: RES           Date: 12/07/98
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1998
'
'**********************************************************************
Private Function OrderPolicyUpdate(It As IT_RCD, Sa As SA_RCD, Co As ADODB.Recordset)

    Dim BaseDate As Variant     'Base Date -- Date of last demand update
    Dim DailyDemand As Double   'Average Daily Demand for an Item
    Dim LastYear As Integer     'Last Demand Update Year
    Dim LastPd As Integer       'Last Demand Update Period
    Dim PctZero As Double       'Percentage Zero Sales
    Dim RtnCode As Integer      'Return Code
    Dim Turns As Double         'Estimated Turns
    
    'Calculate accumulative leadtime
    
    It.accum_lt = It.LT + It.PutAwayDays
        
    'Determine the Last Year and Last Period

    LastYear = It.FctUpdCyc \ 100
    LastPd = It.FctUpdCyc - (CLng(LastYear) * 100) + 1

    'Determine the Base Date -- the base date is the effective date of the last dd update

    BaseDate = GetStartDate(LastPd, LastYear, Co("BegOfYr").Value, Co("CurFiscalYr").Value, Co("CALT").Value)

    'Calculate the Quantity Consumed during the leadtime

    It.fcstlt = Fcst(BaseDate, It.fcstdate, It.fcstdate + It.accum_lt + Co("Addl_LTDays").Value, Co("BegOfYr").Value, Co("CALT").Value, It.dd, It.Trndp, Sa, "N", True, It.hitrndl)

    'Calculate the Forecast for the Item's Review Time

    It.fcstrt = Fcst(BaseDate, It.fcstdate + It.accum_lt, It.fcstdate + It.accum_lt + It.reviewtime, Co("BegOfYr").Value, Co("CALT").Value, It.dd, It.Trndp, Sa, "N", True, It.hitrndl)

    'Calculate the Forecast for the next month, quarter, and year

    It.fcst_month = Fcst(BaseDate, It.fcstdate, DateAdd("m", 1, It.fcstdate) - 1, Co("BegOfYr").Value, Co("CALT").Value, It.dd, It.Trndp, Sa, It.PlnTT, True, It.hitrndl)
    It.fcst_quarter = Fcst(BaseDate, It.fcstdate, DateAdd("q", 1, It.fcstdate) - 1, Co("BegOfYr").Value, Co("CALT").Value, It.dd, It.Trndp, Sa, It.PlnTT, True, It.hitrndl)
    It.fcst_year = Fcst(BaseDate, It.fcstdate, DateAdd("yyyy", 1, It.fcstdate) - 1, Co("BegOfYr").Value, Co("CALT").Value, It.dd, It.Trndp, Sa, It.PlnTT, True, It.hitrndl)

    'Calculate Safety Stock

    If It.fcstcycles > 0 Then

        PctZero = It.ZeroCount / It.fcstcycles
        It.safetystock = safetystock(It.fcstlt, It.dd, It.MADSS, It.dser, "Y", PctZero, Co("lumpy_maxdd").Value, Co("lumpy_minpctzero").Value)

    Else

        It.safetystock = safetystock(It.fcstlt, It.dd, It.MADSS, It.dser, "N", 0, Co("lumpy_maxdd").Value, Co("lumpy_minpctzero").Value)

    End If

    'Apply Safety Stock Adjustments

    It.safetystock = It.safetystock * (1 + It.LtvFact) * (1 + It.SSAdj)

    'Perform counter stock test

    If It.safetystock < It.cstock And It.ZSStock <> "Y" Then

        It.safetystock = It.cstock

    End If

    'Check zero safety stock switch

    If It.ZSStock = "Y" Then
        It.safetystock = 0
    End If

    'Calculate the order point; round the results

    It.orderpt = CLng(It.fcstrt + It.fcstlt + It.safetystock)

    'Calculate the order quantity

    If (It.reviewtime + It.accum_lt) > 0 Then
        DailyDemand = (It.fcstrt + It.fcstlt) / (It.accum_lt + It.reviewtime)
    Else
        DailyDemand = 0
    End If

    'Calculate the Economic Order Quantity for an item

    It.orderqty = EOQ(Co("r").Value, Co("K").Value, DailyDemand, It.cost)

   If DailyDemand > 0 Then

        'Test against the high and low turn limits

        Turns = (DailyDemand * 365) / ((It.orderqty + It.fcstrt) / 2 + It.safetystock)

        If Turns > It.dflt_turnhigh Then

            If It.dflt_turnhigh <> 0 Then
                It.orderqty = 2 * (((DailyDemand * 365) / It.dflt_turnhigh) - It.safetystock) - It.fcstrt
            Else
                It.orderqty = 2 * (((DailyDemand * 365) / 1) - It.safetystock) - It.fcstrt
            End If

        ElseIf Turns < It.dflt_turnlow Then

            If It.dflt_turnlow <> 0 Then
                It.orderqty = 2 * (((DailyDemand * 365) / It.dflt_turnlow) - It.safetystock) - It.fcstrt
            Else
                It.orderqty = 2 * (((DailyDemand * 365) / 1) - It.safetystock) - It.fcstrt
            End If

        End If

    Else

        It.orderqty = 0

    End If

    'Test for Maximum Order Quantity (1 year)

    If It.orderqty > DailyDemand * 365 Then

        It.orderqty = DailyDemand * 365

    End If

    'Test for Minimum Order Quantity (Based on Review Time)

    If It.orderqty < It.fcstrt Then

        It.orderqty = It.fcstrt

    End If

    'Test for Minimum Order Quantity (Minimum 2 Day Supply)

    If It.orderqty < DailyDemand * 2 Then

        It.orderqty = DailyDemand * 2

    End If

    'Test for Minimum Order Quantity (1 unit)

    If It.orderqty < 1 Then

        It.orderqty = 1

    End If

End Function
'**********************************************************************
' Function Name:    EOQ(R, K, DailyDemand, UnitCost)
'
' Description:      Calculates the Economic Order Quantity.
'
' Parameters:       R               Cost of Ordering
'                   K               Cost of Carring Inventory (%)
'                                   K should be passed in this format:  0.00
'                   DailyDemand     Estimated Daily Demand
'                   UnitCost        Unit Cost
'
' Returns:          Economic Order Quantity
'
' By: RES           Date: 01/26/96
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995, 1996
'
'**********************************************************************
Private Function EOQ(r As Double, k As Double, DailyDemand As Double, UnitCost As Double)

    'Check for invalid values
    
    If k <= 0 Or UnitCost <= 0 Then
    
        EOQ = 0
        Exit Function
    
    End If
    
    EOQ = Sqr((365 * 2 * r * DailyDemand) / (k / 100 * UnitCost))

End Function


'**********************************************************************
' Function Name:    SafetyStock(LTForecast, DD, MADSS, ServiceLvl,
'                   UseLumpy, PctZero, Lumpy_MaxDD, Lumpy_MinPctZero)
'
' Description:      Calculates Safety Stock for an item based on the
'                   replenishment lead time, deseasonalized demand,
'                   Mean Absolute Deviation -- Safety Stock, and the
'                   desired Service Level.
'
' Parameters:       LTForecast          Forecast for Lead Time
'                   DD                  Deseasonalized Demand
'                   MADSS               Mean Absolute Deviation - Safety Stock
'                   ServiceLvl          Service Level
'                   UseLumpy            User Lumpy Demand Switch
'                                       Y - Calculate Lumpy Demand
'                                       N - Do Not Calculate Lumpy Demand
'                   PctZero             Percent Zero
'                   Lumpy_MaxDD         Lumpy Maximum Deseasonalized Demand
'                   Lumpy_MinPctZero    Lumpy Maximum Percentage Zero
'
' Returns:          Safety Stock Quantity
'
' By: RES          Date: 01/26/96
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995, 1996
'
'**********************************************************************
Private Function safetystock(LTForecast As Double, dd As Double, MADSS As Double, ServiceLvl As Double, UseLumpy As String, PctZero As Double, lumpy_maxdd As Double, lumpy_minpctzero As Double)

    Dim Normal_SafetyStock As Double
    Dim Lumpy_SafetyStock As Double

    'Check for a zero deseasonalized demand
    
    If dd <= 0 Then
        
        safetystock = 0
        Exit Function
        
    End If
    
    'Determine is this is a lumpy item
    
    If UseLumpy = "Y" And PctZero >= (lumpy_minpctzero / 100) And dd <= lumpy_maxdd Then
    
        'Calculate Lumpy Safety Stock

        Lumpy_SafetyStock = Poisson(LTForecast, ServiceLvl)

    Else

        Lumpy_SafetyStock = 0

    End If
        
    'Calculate Normal Safety Stock
    
    Normal_SafetyStock = SafetyFactor(ServiceLvl) * LTForecast * MADSS / dd
    
    If Lumpy_SafetyStock > Normal_SafetyStock Then
        
        safetystock = Lumpy_SafetyStock

    Else

        safetystock = Normal_SafetyStock

    End If

End Function

'**********************************************************************
' Function Name:    SafetyFactor(ServiceFunction)
'
' Description:      This function returns the safety factor
'                   associated with a given service level.
'
' Parameters:       ServiceLvl      Service Level
'
' Returns:          Safety Factor
'
' By: RES           Date: 03/16/96
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995, 1996
'
'**********************************************************************
Private Function SafetyFactor(ServiceLvl As Double)

    Select Case ServiceLvl
    
    Case Is <= 0.5
        SafetyFactor = 0
    Case Is <= 0.5398
        SafetyFactor = 0.1
    Case Is <= 0.5793
        SafetyFactor = 0.2
    Case Is <= 0.6179
        SafetyFactor = 0.3
    Case Is <= 0.6554
        SafetyFactor = 0.4
    Case Is <= 0.6915
        SafetyFactor = 0.5
    Case Is <= 0.7257
        SafetyFactor = 0.6
    Case Is <= 0.758
        SafetyFactor = 0.7
    Case Is <= 0.7881
        SafetyFactor = 0.8
    Case Is <= 0.8159
        SafetyFactor = 0.9
    Case Is <= 0.8413
        SafetyFactor = 1#
    Case Is <= 0.8643
        SafetyFactor = 1.1
    Case Is <= 0.8849
        SafetyFactor = 1.2
    Case Is <= 0.9032
        SafetyFactor = 1.3
    Case Is <= 0.9192
        SafetyFactor = 1.4
    Case Is <= 0.9332
        SafetyFactor = 1.5
    Case Is <= 0.9452
        SafetyFactor = 1.6
    Case Is <= 0.9555
        SafetyFactor = 1.7
    Case Is <= 0.9641
        SafetyFactor = 1.8
    Case Is <= 0.9713
        SafetyFactor = 1.9
    Case Is <= 0.9773
        SafetyFactor = 2#
    Case Is <= 0.9821
        SafetyFactor = 2.1
    Case Is <= 0.9861
        SafetyFactor = 2.2
    Case Is <= 0.9893
        SafetyFactor = 2.3
    Case Is <= 0.9918
        SafetyFactor = 2.4
    Case Is <= 0.9938
        SafetyFactor = 2.5
    Case Is <= 0.9953
        SafetyFactor = 2.6
    Case Is <= 0.9965
        SafetyFactor = 2.7
    Case Is <= 0.9974
        SafetyFactor = 2.8
    Case Is <= 0.9981
        SafetyFactor = 2.9
    Case Is <= 0.9987
        SafetyFactor = 3#
    Case Is <= 0.999
        SafetyFactor = 3.1
    Case Is <= 0.9993
        SafetyFactor = 3.2
    Case Is <= 0.9995
        SafetyFactor = 3.3
    Case Is <= 0.9997
        SafetyFactor = 3.4
    Case Is <= 0.9998
        SafetyFactor = 3.5
    Case Is <= 0.9998
        SafetyFactor = 3.6
    Case Is <= 0.9999
        SafetyFactor = 3.7
    Case Is <= 0.9999
        SafetyFactor = 3.8
    Case Is <= 0.9999
        SafetyFactor = 3.9
    Case Is <= 1#
        SafetyFactor = 4#
    Case Else
        SafetyFactor = 4
    End Select

End Function


'**********************************************************************
' Function Name:    Poisson(LTForecast, ServiceLvl)
'
' Description:      Returns the number of units required to provide
'                   the desired service level for an item with a
'                   Poisson distribution.
'
' Parameters:       LTForecast      Lead Time Forecast
'                   ServiceLvl      Desired Service Level
'
' Returns:          Maximum number of units.
'
' By: RES          Date: 08/04/96
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995, 1996
'
'**********************************************************************
Private Function Poisson(LTForecast As Double, ServiceLvl As Double)

    Dim CumProb As Double       'Accumulative Probability
    Dim Fact As Double          'Factorial
    Dim NbrUnits As Integer     'Number of units
    
    'Initialize variables
    
    CumProb = 0
    Fact = 0
    NbrUnits = 0
    
    For NbrUnits = 0 To 20
        
        'Calculate Factorial
        
        If NbrUnits < 2 Then
            Fact = 1
        Else
            Fact = NbrUnits * Fact
        End If

        CumProb = CumProb + ((LTForecast ^ NbrUnits) * Exp(LTForecast * -1) / Fact)

        'Increment Number of units
        
        If CumProb >= ServiceLvl Then
        
            Exit For

        End If

    Next NbrUnits

    Poisson = NbrUnits
    
End Function


'**********************************************************************
' Function Name:    Fcst(BaseDate, startdate, enddate, BegofYear, Calt,
'                   dd, trend, sa, trndopt, unitopt, hitrndl)
'
' Description:      Calculates the forecast for the period specified
'                   (inclusive -- period is the beginning of the start date
'                   through the end of the end date).
'
' Parameters:       BaseDate        Base Date (can be Now or another date)
'                   startdate       Start Date
'                   enddate         Ending Date
'                   BegofYear       Beginning of Fiscal Year
'                   Calt            Calendar Type
'                   dd              Deseasonalized Demand
'                   trend           Trend Percentage
'                   sa              Seasonality Record
'                   trndopt         Trend Option (plntt)
'                                       Y=Apply Trend
'                                       N=Do Not Apply Trend
'                   UnitOpt         Unit Opt
'                                       True=Return Units
'                                       False=Return Accumulated Base Indices
'                   hitrndl         High Trend Limit (from item table)
'
' Returns:          Forecast in Units
'
' By: RES and UKK   Date: 12/18/95
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995
'
'**********************************************************************
Private Function Fcst(BaseDate As Variant, StartDate As Variant, EndDate As Variant, BegofYear As Variant, CALT As Integer, dd As Double, trend As Double, Sa As SA_RCD, trndopt As String, UnitOpt As Integer, hitrndl As Double) As Double

    Dim w_EndDAte As Variant
    Dim sp As Double
    Dim sp_p As Integer             'Starting Period Integer
    Dim sp_r As Double              'Starting Period Portion
    Dim ep As Double
    Dim ep_p As Integer             'Ending Period Integer
    Dim ep_r As Double              'Ending Period Portion
    Dim ssa As Integer
    Dim esa As Integer
    Dim Accum_Fcst As Double
    Dim Accum_Fcst_Trend As Double
    Dim i As Integer
    Dim si As Integer

    'Check for invalid start and end dates
    
    If EndDate < StartDate Then
    
        Fcst = 0
        Exit Function
        
    End If
    
    w_EndDAte = EndDate
    
    'Determine the period for the beginning and ending periods

    sp = GetFuturePds(BaseDate, StartDate, BegofYear, CALT)
    ep = GetFuturePds(BaseDate, w_EndDAte, BegofYear, CALT)
    
    ssa = Int(GetPd(StartDate, BegofYear, CALT))
    esa = Int(GetPd(EndDate, BegofYear, CALT))

    sp_p = Int(sp)          'Integer Starting Period
    sp_r = sp - sp_p        'Starting Period Portion
    
    ep_p = Int(ep)          'Integer Ending Period
    ep_r = ep - ep_p        'Ending Period Portion
    
    'Calculate Forecast for Starting Period
    
    If trndopt = "Y" Then   'User selected Apply Trend
        
        If ep_p > sp_p Then
            Accum_Fcst_Trend = ((1 / 2) * ((1 + trend) ^ sp_p + (1 + trend) ^ (sp_p - 1)) * Sa.bi(ssa)) * (1 - sp_r)
        Else
            Accum_Fcst_Trend = ((1 / 2) * ((1 + trend) ^ sp_p + (1 + trend) ^ (sp_p - 1)) * Sa.bi(esa)) * (ep_r - sp_r)
        End If
    
        'Calculate Forecast for intervening periods
        
        si = ssa + 1

        If si > 52 Then
            si = si - 52
        End If

        For i = (sp_p + 1) To (ep_p - 1)
    
            Accum_Fcst_Trend = Accum_Fcst_Trend + (((1 / 2) * ((1 + trend) ^ i + (1 + trend) ^ (i - 1)) * Sa.bi(si)))
            
            si = si + 1
            If si > 52 Then
                si = si - 52
            End If

        Next i
    
        'Calculate Forecast for Ending Period
    
        If ep_p > sp_p Then
            
            Accum_Fcst_Trend = Accum_Fcst_Trend + ((1 / 2) * ((1 + trend) ^ ep_p + (1 + trend) ^ (ep_p - 1)) * Sa.bi(esa)) * ep_r
        
        End If
    
    End If
    
    'Calculate Accum_Fcst without using trend

    'Calculate Forecast for starting period
    If ep_p > sp_p Then
        Accum_Fcst = 1 * Sa.bi(ssa) * (1 - sp_r)
    Else
        Accum_Fcst = 1 * Sa.bi(esa) * (ep_r - sp_r)
    End If

    'Calculate Forecast for intervening periods
    si = ssa + 1
    
    If si > 52 Then
        si = si - 52
    End If

    For i = (sp_p + 1) To (ep_p - 1)

        Accum_Fcst = Accum_Fcst + 1 * Sa.bi(si)

        si = si + 1
        
        If si > 52 Then
            si = si - 52
        End If

    Next i

    'Calculate Forecast for Ending Period
    If ep_p > sp_p Then
        Accum_Fcst = Accum_Fcst + 1 * Sa.bi(esa) * ep_r
    End If
    
    'Check for appropriate Accumulative Forecast

    If trndopt = "Y" Then

        'Check for Trend Limit Exception

        If Accum_Fcst_Trend > Accum_Fcst * (1 + hitrndl) Then

            Accum_Fcst = Accum_Fcst * (1 + hitrndl)

        Else
            
            Accum_Fcst = Accum_Fcst_Trend

        End If

    End If
    
    If UnitOpt Then
        Fcst = Accum_Fcst * dd
    Else
        Fcst = Accum_Fcst
    End If

End Function
'**********************************************************************
' Function Name:    GetPd(TargetDate, BegOfYear, Calt)
'
' Description:      Returns the period number (1-12, 1-52, or 1-13)
'                   associated with a target date, as an integer and
'                   decimal combination.
'                   Integer Portion:  Period Number
'                   Decimal Portion:  Portion of the Period (**BEGINNING
'                   of the day on the Target Date.)
'
' Parameters:       TargetDate  Target Date
'                   BegofYear   Beginning of Fiscal Year
'                   Calt        Calendar Type
'
' Returns:          Period Number
'
' By: UKK           Date: 12/18/95    modified 1/27/97 - 1/29/97
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995
'
'**********************************************************************
Private Function GetPd(TargetDate As Variant, BegofYear As Variant, CALT As Integer) As Double

    Dim X As Double
    Dim d As Integer
    Dim m As Double
    Dim wk As Integer
    Dim FYBegin As Variant
    Dim firstday As Variant
    Dim lastday As Variant

    'Calculate the beginning date of the fiscal year in which TargetDate falls.

    FYBegin = GetFiscalBegOfYear(TargetDate, BegofYear, CALT)
    
    'Find the amount of time from FYBegin to TargetDate using calendar type.

    Select Case CALT
    
    Case CALT_WEEKLY        '(0)
        
        X = DateDiff("d", FYBegin, TargetDate)

        X = X / 7           'convert to weeks

        X = X + 1           'Makes Period Numbering start with 1, not 0.
        
        If X >= 53 Then     'Credit any activity in the "53rd" wk to wk 1 (of the next year).
            X = 1
        End If

    Case CALT_MONTHLY       '(1)
        
        firstday = DateSerial(Year(TargetDate), Month(TargetDate), 1)
        lastday = DateSerial(Year(TargetDate), Month(TargetDate) + 1, 1 - 1)

        d = DateAdd("d", 1, DateDiff("d", firstday, lastday))  '31 30 or 28
        m = (DatePart("d", TargetDate) - 1) / d   'fraction of month

        X = DateDiff("m", FYBegin, TargetDate) + m + 1

    Case CALT_RETAIL544     '(2)
    
        X = DateDiff("d", FYBegin, TargetDate)

        X = X / 7

        wk = Int(X) + 1

        Select Case wk

        Case 1 To 5
            X = 1 + ((7 * X) / 35)
        Case 6 To 9
            X = 2 + (((7 * X) - 35) / 28)
        Case 10 To 13
            X = 3 + (((7 * X) - 63) / 28)
        Case 14 To 18
            X = 4 + (((7 * X) - 91) / 35)
        Case 19 To 22
            X = 5 + (((7 * X) - 126) / 28)
        Case 23 To 26
            X = 6 + (((7 * X) - 154) / 28)
        Case 27 To 31
            X = 7 + (((7 * X) - 182) / 35)
        Case 32 To 35
            X = 8 + (((7 * X) - 217) / 28)
        Case 36 To 39
            X = 9 + (((7 * X) - 245) / 28)
        Case 40 To 44
            X = 10 + (((7 * X) - 273) / 35)
        Case 45 To 48
            X = 11 + (((7 * X) - 308) / 28)
        Case 48 To 52
            X = 12 + (((7 * X) - 336) / 28)
        Case Is > 52    'All "week 53" activity credited to period 1 (of the next year)
            X = 1
        
        End Select

    Case CALT_RETAIL454     '(3)
        
        X = DateDiff("d", FYBegin, TargetDate)

        X = X / 7

        wk = Int(X) + 1

        Select Case wk

        Case 1 To 4
            X = 1 + ((7 * X) / 28)
        Case 5 To 9
            X = 2 + (((7 * X) - 28) / 35)
        Case 10 To 13
            X = 3 + (((7 * X) - 63) / 28)
        Case 14 To 17
            X = 4 + (((7 * X) - 91) / 28)
        Case 18 To 22
            X = 5 + (((7 * X) - 119) / 35)
        Case 23 To 26
            X = 6 + (((7 * X) - 154) / 28)
        Case 27 To 30
            X = 7 + (((7 * X) - 182) / 28)
        Case 31 To 35
            X = 8 + (((7 * X) - 210) / 35)
        Case 36 To 39
            X = 9 + (((7 * X) - 245) / 28)
        Case 40 To 43
            X = 10 + (((7 * X) - 273) / 28)
        Case 44 To 48
            X = 11 + (((7 * X) - 301) / 35)
        Case 48 To 52
            X = 12 + (((7 * X) - 336) / 28)
        Case Is > 52    'All "week 53" activity credited to period 1 (of the next year)
            X = 1
        
        End Select
    
    Case CALT_RETAIL445    '(4)
        
        X = DateDiff("d", FYBegin, TargetDate)

        X = X / 7

        wk = Int(X) + 1

        Select Case wk

        Case 1 To 4
            X = 1 + ((7 * X) / 28)
        Case 5 To 8
            X = 2 + (((7 * X) - 28) / 28)
        Case 9 To 13
            X = 3 + (((7 * X) - 56) / 35)
        Case 14 To 17
            X = 4 + (((7 * X) - 91) / 28)
        Case 18 To 21
            X = 5 + (((7 * X) - 119) / 28)
        Case 22 To 26
            X = 6 + (((7 * X) - 147) / 35)
        Case 27 To 30
            X = 7 + (((7 * X) - 182) / 28)
        Case 31 To 34
            X = 8 + (((7 * X) - 210) / 28)
        Case 35 To 39
            X = 9 + (((7 * X) - 238) / 35)
        Case 40 To 43
            X = 10 + (((7 * X) - 273) / 28)
        Case 44 To 47
            X = 11 + (((7 * X) - 301) / 28)
        Case 48 To 52
            X = 12 + (((7 * X) - 329) / 35)
        Case Is > 52    'All "week 53" activity credited to period 1 (of the next year)
            X = 1
                  
        End Select
    
    Case CALT_RETAIL13      '(5)
    
        X = DateDiff("d", FYBegin, TargetDate)

        X = X / 7
        
        wk = Int(X) + 1
        
        Select Case wk

        Case 1 To 4
            X = 1 + ((7 * X) / 28)
        Case 5 To 8
            X = 2 + (((7 * X) - 28) / 28)
        Case 9 To 12
            X = 3 + (((7 * X) - 56) / 28)
        Case 13 To 16
            X = 4 + (((7 * X) - 84) / 28)
        Case 17 To 20
            X = 5 + (((7 * X) - 112) / 28)
        Case 21 To 24
            X = 6 + (((7 * X) - 140) / 28)
        Case 25 To 28
            X = 7 + (((7 * X) - 168) / 28)
        Case 29 To 32
            X = 8 + (((7 * X) - 196) / 28)
        Case 33 To 36
            X = 9 + (((7 * X) - 224) / 28)
        Case 37 To 40
            X = 10 + (((7 * X) - 252) / 28)
        Case 41 To 44
            X = 11 + (((7 * X) - 280) / 28)
        Case 45 To 48
            X = 12 + (((7 * X) - 308) / 28)
        Case 49 To 52
            X = 13 + (((7 * X) - 336) / 28)
        Case Is > 52    'All "week 53" activity credited to period 1 (of the next year)
            X = 1
                  
        End Select
    
    End Select

    GetPd = X

End Function


'**********************************************************************
' Function Name:    GetFuturePds(BaseDate, TargetDate, BegOfYear, Calt)
'
' Description:      Returns the time difference (in periods) between
'                   a specified base date and a target date,
'                   as an integer and decimal combination.
'                   Integer Portion:  Period Number, relative to base date
'                   Decimal Portion:  Portion of the Period (**BEGINNING
'                   of the day on the Target Date.)
'
' Parameters:       BaseDate    Base Date (can be Now or another date.)
'                   TargetDate  Target Date
'                   BegofYear   Beginning of Fiscal Year
'                   Calt        Calendar Type
'
' Returns:          Period Number
'
' By: UKK           Date: 12/20/95    modified 1/29/97
'
' Copyright (c) Technology Advantage, Alpharetta, GA  1995
'
'**********************************************************************
Private Function GetFuturePds(BaseDate As Variant, TargetDate As Variant, BegofYear As Variant, CALT As Integer) As Double

    Dim X As Double
    Dim NumYears As Integer
    Dim i As Integer
    Dim w_BaseDate As Variant
    Dim d As Integer
    Dim m As Double
    Dim firstday As Variant
    Dim lastday As Variant
    Dim days As Integer
    Dim Base_Fraction As Double
    Dim Target_fraction As Double
    Dim months As Integer
    Dim z As Variant
    Dim wks As Double
    Dim yr_adjust As Integer
    
    'BaseDate must be earlier than targetdate
    If CVDate(TargetDate) < CVDate(BaseDate) Then

        GetFuturePds = 0
        Exit Function

    End If

    Select Case CALT

    Case CALT_WEEKLY

        X = DateDiff("d", BaseDate, TargetDate) / 7

    Case CALT_MONTHLY
            
        If Month(TargetDate) = Month(BaseDate) And Year(TargetDate) = Year(BaseDate) Then   'in same month
            
            days = DateDiff("d", BaseDate, TargetDate)      'days between the two

            firstday = DateSerial(Year(TargetDate), Month(TargetDate), 1)  'first day of month
            lastday = DateSerial(Year(TargetDate), Month(TargetDate) + 1, 1 - 1) 'last day of month
    
            d = DateDiff("d", firstday, lastday) + 1    'number of days in month
            X = days / d                                'fraction of month

        Else         'in different months.

            'Calculate the decimal portion from basedate to end of basedate's month.
            
            firstday = DateSerial(Year(BaseDate), Month(BaseDate), 1)
            lastday = DateSerial(Year(BaseDate), Month(BaseDate) + 1, 1 - 1)
    
            days = DateDiff("d", BaseDate, lastday)
            
            d = DateDiff("d", firstday, lastday) + 1    'number of days in month
            Base_Fraction = days / d                    'fraction of month

            'Calculate the decimal portion from beginning of targetdate's month to targetdate.
            
            firstday = DateSerial(Year(TargetDate), Month(TargetDate), 1)
            lastday = DateSerial(Year(TargetDate), Month(TargetDate) + 1, 1 - 1)
    
            days = DateDiff("d", firstday, TargetDate)
            
            d = DateDiff("d", firstday, lastday) + 1    'number of days in month
            Target_fraction = days / d                  'fraction of month
            
            'Calculate the number of complete months between basedate and targetdate.
            
            months = DateDiff("m", BaseDate, TargetDate) - 1

            'Sum the three.
            
            X = months + Target_fraction + Base_Fraction

        End If

    Case CALT_RETAIL544, CALT_RETAIL454, CALT_RETAIL445, CALT_RETAIL13

        'Find the number of weeks between BaseDate, TargetDate.
        
        wks = DateDiff("d", BaseDate, TargetDate) / 7
        
        'Subtract 52 from that result until less than 52.  Count the number of subtractions.
        
        yr_adjust = 0

        Do Until wks < 52

            wks = wks - 52
            yr_adjust = yr_adjust + 1
            
        Loop

        'Get the period number for the result.  Subtract 1 and add yr_adjust * nbr of periods.
        
        z = DateAdd("d", CInt(wks * 7), BegofYear)

        If CALT = CALT_RETAIL13 Then
            X = (GetPd(z, BegofYear, CALT) - 1) + (yr_adjust * 13)
        Else
            X = (GetPd(z, BegofYear, CALT) - 1) + (yr_adjust * 12)
        End If

    End Select

    GetFuturePds = X

End Function


Private Sub cmdClose_Click()

    Unload Me
    
End Sub


Private Sub cmdUpdate_Click()

    Dim Cn2 As New ADODB.Connection
    
    Dim AIM_OrderPolicyUpdate_Sp As New ADODB.Command

    Dim rsCompany As New ADODB.Recordset
    Dim rsItem As New ADODB.Recordset
    Dim rsRevCycle As New ADODB.Recordset
    
    Dim i, j, k As Integer
    Dim It As IT_RCD
    Dim LcidList() As String
    Dim RtnCode As Integer
    Dim Sa As SA_RCD
    Dim SelLcid As String
    Dim SqlStmt As String
    
    'Housekeeping
    
    Screen.MousePointer = vbHourglass
    Write_Message "Updating order policies..."
    
    RtnCode = SQLConnection(Cn2, CONNECTION_OPEN, False)
    Cn2.CursorLocation = adUseClient
    
    rsCompany.CursorLocation = adUseClient
    rsItem.CursorLocation = adUseClient
    rsRevCycle.CursorLocation = adUseClient
    
    Cn2.Errors.Clear
    With AIM_OrderPolicyUpdate_Sp
    
        Set .ActiveConnection = Cn2
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_OrderPolicyUpdate_Sp"
        
    End With
    
    'Get the Company Record
    
    SqlStmt = "select * from company "
    
    Cn.Errors.Clear
    rsCompany.Open SqlStmt, Cn, adOpenForwardOnly, adLockReadOnly
    If Cn.Errors.Count > 0 Then
        MsgBox "Unable to open Company Table -- Processing aborted.", vbCritical
        Write_Message ""
        Screen.MousePointer = vbNormal
        Exit Sub
    End If
    
    'Build the Location List
    
    If Me.optLcid(0).Value = True Then  'All Locations
        
        ReDim LcidList(rsLocations.RecordCount - 1)
        rsLocations.MoveFirst
        
        For i = 0 To rsLocations.RecordCount - 1
            LcidList(i) = rsLocations("lcid").Value
            rsLocations.MoveNext
        Next i
    
    Else
        ReDim LcidList(0)
        LcidList(0) = Me.dcLcid.text
    End If
    
    For i = 0 To UBound(LcidList)
    
        SelLcid = Trim(LcidList(i))
        
        Write_Message "Processing locations [" * Trim(SelLcid) & "]..."
        
        'Build the Item SQL Statement
        
        SqlStmt = "select item.lcid, item.item, item.dd, item.madss, item.trndp, "
        SqlStmt = SqlStmt & "item.fcstcycles, item.zerocount, item.fctupdcyc, item.hitrndl, "
        SqlStmt = SqlStmt & "item.plntt, item.cost, item.cstock, "
        SqlStmt = SqlStmt & "item.ssadj, item.ltvfact, item.zsstock, item.fcstdate, "
        SqlStmt = SqlStmt & "op.dflt_turnhigh, op.dflt_turnlow, "
        SqlStmt = SqlStmt & "sa.bi01, sa.bi02, sa.bi03, sa.bi04, sa.bi05, sa.bi06, sa.bi07, "
        SqlStmt = SqlStmt & "sa.bi08, sa.bi09, sa.bi10, sa.bi11, sa.bi12, sa.bi13, sa.bi14, "
        SqlStmt = SqlStmt & "sa.bi15, sa.bi16, sa.bi17, sa.bi18, sa.bi19, sa.bi20, sa.bi21, "
        SqlStmt = SqlStmt & "sa.bi22, sa.bi23, sa.bi24, sa.bi25, sa.bi26, sa.bi27, sa.bi28, "
        SqlStmt = SqlStmt & "sa.bi29, sa.bi30, sa.bi31, sa.bi32, sa.bi33, sa.bi34, sa.bi35, "
        SqlStmt = SqlStmt & "sa.bi36, sa.bi37, sa.bi38, sa.bi39, sa.bi40, sa.bi41, sa.bi42, "
        SqlStmt = SqlStmt & "sa.bi43, sa.bi44, sa.bi45, sa.bi46, sa.bi47, sa.bi48, sa.bi49, "
        SqlStmt = SqlStmt & "sa.bi50, sa.bi51, sa.bi52, "
        SqlStmt = SqlStmt & "lc.putawaydays, "
        SqlStmt = SqlStmt & "lt = case when item.lt = 0 then vn.dftlt else item.lt end, "
        SqlStmt = SqlStmt & "revcycle = case when item.revcycle = '' then vn.dflt_revcycle else item.revcycle end, "
        SqlStmt = SqlStmt & "dser = case when item.dser = 0 then op.dflt_dser else item.dser end "
        SqlStmt = SqlStmt & "from item, op, sa, lc, itstat, vn "
        SqlStmt = SqlStmt & "where item.lcid = op.lcid "
        SqlStmt = SqlStmt & "and item.velcode = op.velcode "
        SqlStmt = SqlStmt & "and item.said = sa.said "
        SqlStmt = SqlStmt & "and item.lcid = lc.lcid "
        SqlStmt = SqlStmt & "and item.itstat = itstat.itstat "
        SqlStmt = SqlStmt & "and item.vnid = vn.vnid "
        SqlStmt = SqlStmt & "and item.assort = vn.assort "
        SqlStmt = SqlStmt & "and item.lcid = '" & SelLcid & "' "
        SqlStmt = SqlStmt & "and itstat.dmdupd = 'Y' "
        
        rsItem.Open SqlStmt, Cn, adOpenForwardOnly, adLockReadOnly

        Do Until rsItem.eof
        
            'Load the Item and Seasonality records
            
            It.Lcid = rsItem("lcid").Value
            It.item = rsItem("item").Value
            It.dd = rsItem("dd").Value
            It.MADSS = rsItem("madss").Value
            It.Trndp = rsItem("trndp").Value
            It.fcstcycles = rsItem("fcstcycles").Value
            It.ZeroCount = rsItem("zerocount").Value
            It.FctUpdCyc = rsItem("fctupdcyc").Value
            It.hitrndl = rsItem("hitrndl").Value
            It.PlnTT = rsItem("plntt").Value
            It.cost = rsItem("cost").Value
            It.cstock = rsItem("cstock").Value
            It.SSAdj = rsItem("ssadj").Value
            It.LtvFact = rsItem("ltvfact").Value
            It.ZSStock = rsItem("zsstock").Value
            It.fcstdate = rsItem("fcstdate").Value
            It.dflt_turnhigh = rsItem("dflt_turnhigh").Value
            It.dflt_turnlow = rsItem("dflt_turnlow").Value
            It.PutAwayDays = rsItem("putawaydays").Value
            It.LT = rsItem("lt").Value
            It.Revcycle = rsItem("revcycle").Value
            It.dser = rsItem("dser").Value
            
            For k = 0 To rsItem.Fields.Count - 1
                If rsItem.Fields(k).Name = "bi01" Then
                    Exit For
                End If
            Next k
            
            For j = 1 To 52
                Sa.bi(j) = rsItem(j + k - 1).Value
            Next j
                
            'Calculate review time for this item
    
            SqlStmt = "select period, frequency from revcycle where revcycle = '" & Trim(It.Revcycle) & "' "
        
            rsRevCycle.Open SqlStmt, Cn2, adOpenForwardOnly, adLockReadOnly
            
            If rsRevCycle.RecordCount > 0 Then
            
                Select Case Trim(rsRevCycle("period").Value)
                Case "D"
                    It.reviewtime = CInt(rsRevCycle("frequency").Value)
                Case "W"
                    It.reviewtime = CInt(rsRevCycle("frequency").Value) * 7
                Case "M"
                    It.reviewtime = CInt(rsRevCycle("frequency").Value) * 30
                Case "Q"
                    It.reviewtime = CInt(rsRevCycle("frequency").Value) * 90
                Case Else
                    It.reviewtime = 0
                End Select
            
            Else
                It.reviewtime = 0
            End If
            
            RtnCode = OrderPolicyUpdate(It, Sa, rsCompany)
            
            'Update the Item Table
            
            On Error Resume Next
            Cn2.Errors.Clear
                        
            With AIM_OrderPolicyUpdate_Sp
            
                .Parameters("@lcid") = It.Lcid
                .Parameters("@item") = It.item
                .Parameters("@accum_lt") = It.accum_lt
                .Parameters("@reviewtime") = It.reviewtime
                .Parameters("@orderpt") = It.orderpt
                .Parameters("@orderqty") = It.orderqty
                .Parameters("@safetystock") = It.safetystock
                .Parameters("@fcstrt") = It.fcstrt
                .Parameters("@fcstlt") = It.fcstlt
                .Parameters("@fcst_month") = It.fcst_month
                .Parameters("@fcst_quarter") = It.fcst_quarter
                .Parameters("@fcst_year") = It.fcst_year
       
               'Execute the SQL Statement
    
                AIM_OrderPolicyUpdate_Sp.Execute
                
                If Cn2.Errors.Count > 0 Then
                    MsgBox "Critical Error Updating Item Table -- Update aborted.", vbCritical
                    Write_Message ""
                    Screen.MousePointer = vbNormal
                    Exit Sub
                    
                End If
                
              End With
            
            'Close the review cycle table
            
            rsRevCycle.Close
            
            'Get the Next Record
            
            rsItem.MoveNext
            
        Loop
        
        rsItem.Close

    Next i
     
    'Windup
    
    rsCompany.Close
    
    RtnCode = SQLConnection(Cn2, CONNECTION_CLOSE, False)
        
    Write_Message "Order Policy Update Complete."
    Screen.MousePointer = vbNormal

End Sub

Private Sub dcLcid_InitColumnProps()

    Me.dcLcid.Columns(0).Caption = "Id"
    Me.dcLcid.Columns(0).Width = 1000
        
    Me.dcLcid.Columns(1).Caption = "Location Name"
    Me.dcLcid.Columns(1).Width = 2880

End Sub


Private Sub Form_Activate()

    'Check for an open connection
    
    If Cn.State <> adStateOpen Then
        Unload Me
    End If

End Sub

Private Sub Form_Load()

    Dim RtnCode As Integer
    Dim SqlStmt As String

    'Display status message
    
    Screen.MousePointer = vbHourglass
    Write_Message "Initializing Order Policy Update..."
    
    'Open a connection to the Server

    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True)
    
    Select Case RtnCode
    
    Case FAIL
    
        MsgBox "Error logging on to " & gServer & ". -- Try Again", vbOKOnly + vbExclamation
        Write_Message ""
        Screen.MousePointer = vbNormal
        Exit Sub
        
    Case TOOMANY
    
        MsgBox "Too many SQL Connections open -- Form Load aborted.", vbCritical
        Write_Message ""
        Screen.MousePointer = vbNormal
        Exit Sub
                
    End Select
    
    'Initialize the Locations Record Set
    
    SqlStmt = "select lcid, lname from AIMLocations "
    'SqlStmt = SqlStmt & "where lcid in ('dc11', 'dc19', 'dc21', 'dc22', "
    'SqlStmt = SqlStmt & "'dc25', 'dc26', 'dc30', 'dc31', 'dc32', 'dc34', "
    'SqlStmt = SqlStmt & "'dc35', 'dc36', 'dc37', 'dc38', 'dc39', 'dc40', "
    'SqlStmt = SqlStmt & "'dc41', 'dc42', 'dc43', 'dc44', 'dc45') "
    SqlStmt = SqlStmt & "order by lcid "
    
    rsLocations.CursorLocation = adUseClient
    rsLocations.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    Set Me.dcLcid.DataSourceList = rsLocations
    
    If rsLocations.RecordCount > 0 Then
        Me.dcLcid.text = rsLocations("lcid").Value
    Else
        Me.dcLcid.text = ""
    End If
    
    'Windup
    
    Write_Message ""
    Screen.MousePointer = vbNormal

End Sub

Private Sub Form_Unload(Cancel As Integer)

    Dim RtnCode As Integer
    
    On Error Resume Next
    
    Write_Message ""
    rsLocations.Close
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    
End Sub

Private Sub optLcid_Click(Index As Integer)

    If Me.optLcid(0).Value = True Then
        Me.dcLcid.Enabled = False
    Else
        Me.dcLcid.Enabled = True
    End If
    
End Sub


