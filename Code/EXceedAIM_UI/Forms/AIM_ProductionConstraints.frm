VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ProductionConstraints 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Production Constraints Maintenance"
   ClientHeight    =   6165
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9885
   Icon            =   "AIM_ProductionConstraints.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6165
   ScaleWidth      =   9885
   ShowInTaskbar   =   0   'False
   Begin VB.Frame fraPCDetails 
      Caption         =   "Production Constraint Details"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   3450
      Left            =   0
      TabIndex        =   14
      Top             =   2280
      Width           =   9855
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgPCDetail 
         Height          =   2925
         Left            =   120
         TabIndex        =   7
         Top             =   360
         Width           =   9600
         _Version        =   196617
         DataMode        =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         AllowAddNew     =   -1  'True
         AllowDelete     =   -1  'True
         AllowColumnSwapping=   0
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         ExtraHeight     =   212
         CaptionAlignment=   0
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   16933
         _ExtentY        =   5159
         _StockProps     =   79
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
   End
   Begin VB.Frame fraPCHeader 
      Height          =   2225
      Left            =   15
      TabIndex        =   8
      Top             =   0
      Width           =   9855
      Begin VB.CheckBox ckEnabled 
         Caption         =   "Enabled"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6240
         TabIndex        =   2
         Top             =   245
         Width           =   3495
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcConstraintType 
         Height          =   345
         Left            =   3000
         TabIndex        =   3
         Top             =   617
         Width           =   1875
         DataFieldList   =   "Column 0"
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   3307
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DataFieldToDisplay=   "Column 0"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcRatioType 
         Height          =   345
         Left            =   3000
         TabIndex        =   6
         Top             =   1750
         Width           =   1875
         DataFieldList   =   "Column 1"
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   3307
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DataFieldToDisplay=   "Column 0"
      End
      Begin TDBText6Ctl.TDBText txtConstraintDesc 
         Height          =   345
         Left            =   3000
         TabIndex        =   4
         Top             =   990
         Width           =   3105
         _Version        =   65536
         _ExtentX        =   5477
         _ExtentY        =   609
         Caption         =   "AIM_ProductionConstraints.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ProductionConstraints.frx":0376
         Key             =   "AIM_ProductionConstraints.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtCycleDays 
         Height          =   345
         Left            =   3000
         TabIndex        =   5
         Top             =   1371
         Width           =   1350
         _Version        =   65536
         _ExtentX        =   2381
         _ExtentY        =   609
         Calculator      =   "AIM_ProductionConstraints.frx":03D8
         Caption         =   "AIM_ProductionConstraints.frx":03F8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ProductionConstraints.frx":0464
         Keys            =   "AIM_ProductionConstraints.frx":0482
         Spin            =   "AIM_ProductionConstraints.frx":04CC
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   0
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcConstraintID 
         Height          =   345
         Left            =   3000
         TabIndex        =   1
         Top             =   240
         Width           =   2355
         DataFieldList   =   "ConstraintID"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   4154
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "ConstraintID"
      End
      Begin TDBText6Ctl.TDBText txtConstraintID 
         Height          =   345
         Left            =   3000
         TabIndex        =   0
         Top             =   240
         Width           =   2355
         _Version        =   65536
         _ExtentX        =   4154
         _ExtentY        =   609
         Caption         =   "AIM_ProductionConstraints.frx":04F4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ProductionConstraints.frx":0560
         Key             =   "AIM_ProductionConstraints.frx":057E
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label lblProdConstraint 
         Caption         =   "Ratio Type"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   4
         Left            =   195
         TabIndex        =   13
         Top             =   1795
         Width           =   2715
      End
      Begin VB.Label lblProdConstraint 
         Caption         =   "Cycle Days"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   3
         Left            =   195
         TabIndex        =   12
         Top             =   1416
         Width           =   2715
      End
      Begin VB.Label lblProdConstraint 
         Caption         =   "Constraint Description"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   2
         Left            =   195
         TabIndex        =   11
         Top             =   1039
         Width           =   2715
      End
      Begin VB.Label lblProdConstraint 
         Caption         =   "Constraint ID"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   10
         Top             =   285
         Width           =   2715
      End
      Begin VB.Label lblProdConstraint 
         Caption         =   "Constraint Type"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   9
         Top             =   662
         Width           =   2715
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   5760
      _ExtentX        =   688
      _ExtentY        =   582
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_ProductionConstraints.frx":05C2
      ToolBars        =   "AIM_ProductionConstraints.frx":9E4C
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ProductionConstraints"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsProductionConstraint As ADODB.Recordset
Dim rsPCDetail As ADODB.Recordset

Dim PCBookmark As Variant
Dim PCDetailBookmark As Variant

Dim ConstraintID As String

Dim m_ActiveGridID As String
Dim m_NewRecord As Boolean

Const ACTIVEGRID_PCDetail As String = "PCDETAIL"

Private Sub dcConstraintID_Click()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim RtnCode As Long
    
    'Check for updates, first
    RtnCode = f_ProdConstraint_Commit(True)
    If RtnCode <> SUCCEED Then Exit Sub

    'Navigate to the selected Constraint ID
    If f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then
        ConstraintID = dcConstraintID.Text
        
        If StrComp(rsProductionConstraint!ConstraintID, ConstraintID, vbTextCompare) <> 0 Then
            'Get record
            RtnCode = f_ProdConstraint_GetKey(rsProductionConstraint, ConstraintID, SQL_GetEq)
            If RtnCode = SUCCEED Then
                'Set background value (used to add/update)
                txtConstraintID.Text = ConstraintID
            Else
                ConstraintID = txtConstraintID.Text
                RtnCode = f_ProdConstraint_GetKey(rsProductionConstraint, ConstraintID, SQL_GetEq)
                If RtnCode = SUCCEED Then
                    dcConstraintID.Text = ConstraintID
                Else
                    'Should not happen. Logical error somewhere!
                End If
            End If
            
            'Set PCDetailBookmark = null if header record has been changed
            PCDetailBookmark = Null
            If IsEmpty(PCBookmark) _
            Or IsNull(PCBookmark) _
            Then
                Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
            End If
        
        Else
            txtConstraintID.Text = rsProductionConstraint!ConstraintID
        End If
    End If
    RefreshForm
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintID_Click)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintID_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcConstraintID_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintID_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintID_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcConstraintID_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcConstraintID.Columns(0).Caption = getTranslationResource("ID")
    Me.dcConstraintID.Columns(0).Width = 1500
    Me.dcConstraintID.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcConstraintID.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcConstraintID.Columns(0).DataField = "ConstraintID"
    
    Me.dcConstraintID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcConstraintID.Columns(1).Width = 2880
    Me.dcConstraintID.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcConstraintID.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcConstraintID.Columns(1).DataField = "ConstraintDesc"
            
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcConstraintID, ACW_EXPAND
    End If

'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To dcconstraintid.Columns.Count - 1
'        dcconstraintid.Columns(IndexCounter).HasHeadBackColor = True
'        dcconstraintid.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintID_InitColumnProps)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintID_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcConstraintType_Change()
On Error GoTo ErrorHandler
    
    Me.dcConstraintType.ToolTipText = Me.dcConstraintType.Value
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintType_Change)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintType_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcConstraintType_Click()
On Error GoTo ErrorHandler
    
    Me.dcConstraintType.ToolTipText = Me.dcConstraintType.Value
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintType_Click)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintType_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcConstraintType_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcConstraintType.Columns(0).Caption = getTranslationResource("Code")
    Me.dcConstraintType.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcConstraintType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcConstraintType.Columns(0).Width = 720
    
    Me.dcConstraintType.Columns(1).Caption = getTranslationResource("Description")
    Me.dcConstraintType.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcConstraintType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcConstraintType.Columns(1).Width = 2000
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcConstraintType, ACW_EXPAND
    End If
    
'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To dcconstrainttype.Columns.Count - 1
'        dcconstrainttype.Columns(IndexCounter).HasHeadBackColor = True
'        dcconstrainttype.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintType_InitColumnProps)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintType_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRatioType_Change()
On Error GoTo ErrorHandler
    
    Me.dcRatioType.ToolTipText = Me.dcRatioType.Value
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcRatioType_Change)"
    f_HandleErr , , , "AIM_ProductionConstraints::dcRatioType_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRatioType_Click()
On Error GoTo ErrorHandler

    Me.dcRatioType.ToolTipText = Me.dcRatioType.Value
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcRatioType_Click)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcRatioType_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRatioType_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcRatioType_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcRatioType_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRatioType_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcRatioType.Columns(0).Caption = getTranslationResource("Code")
    Me.dcRatioType.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcRatioType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRatioType.Columns(0).Width = 720
    
    Me.dcRatioType.Columns(1).Caption = getTranslationResource("Description")
    Me.dcRatioType.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcRatioType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRatioType.Columns(1).Width = 2500
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcRatioType, ACW_EXPAND
    End If

'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To dcRatioType.Columns.Count - 1
'        dcRatioType.Columns(IndexCounter).HasHeadBackColor = True
'        dcRatioType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcRatioType_InitColumnProps)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcRatioType_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    'Validate user input; Cancel ColUpdate if required.
    InvalidData = GridInputValidation(strMessage, ColIndex)
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
    End If
    
    Cancel = InvalidData
    dgPCDetail.SetFocus
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_BeforeColUpdate)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
On Error GoTo ErrorHandler
    
    Dim Result As Long
    Dim strMessage As String
    Dim strMessage1 As String

    'Cancel the default prompt
    DispPromptMsg = False

    'Display a confirmation msgbox
    If dgPCDetail.SelBookmarks.Count <= 0 Then
        strMessage = getTranslationResource("MSGBOX07108")
        If StrComp(strMessage, "MSGBOX07108") = 0 Then strMessage = "Unable to complete the requested operation: No detail records have been selected from the given table. Please review."
        Result = MsgBox(strMessage, vbOKOnly, Me.Caption)
        dgPCDetail.SetFocus
        Exit Sub
    End If
    
    strMessage = getTranslationResource("MSGBOX07102")
    If StrComp(strMessage, "MSGBOX07102") = 0 Then strMessage = "Delete the"

    strMessage = strMessage + " " + str(dgPCDetail.SelBookmarks.Count) + " "
    strMessage1 = getTranslationResource("MSGBOX07103")
    If StrComp(strMessage1, "MSGBOX07103") = 0 Then strMessage1 = "selected Production Constraint Detail records?"
    strMessage = strMessage + strMessage1
    Result = MsgBox(strMessage, vbYesNo, Me.Caption)
    
    Select Case Result
    Case vbYes
        'User chose to delete. Do nothing
    Case vbNo
        'User chose to Cancel
        Cancel = True
        dgPCDetail.SetFocus
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_BeforeDelete)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    'Validate user input; Cancel Update if required.
    'sujit09302003 to call new validation
    If ValidateCtls_Length = False Then
        Cancel = True
        dgPCDetail.SetFocus
        Exit Sub
    End If
    InvalidData = GridInputValidation(strMessage)
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
    End If
    
    Cancel = InvalidData
    dgPCDetail.SetFocus
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_BeforeUpdate)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_BtnClick()
On Error GoTo ErrorHandler
    Dim LcIdKey As String
    Dim ItemKey As String
    Dim ItDesc As String
    
    'Display the Item Lookup form
    LcIdKey = getTranslationResource("All")
    ItemKey = dgPCDetail.Columns(0).Value
    If (ItemKey = "") Then ItemKey = getTranslationResource("All")
    
    AIM_ItemLookUp.LcIdKey = LcIdKey
    AIM_ItemLookUp.ItemKey = ItemKey
    Set AIM_ItemLookUp.Cn = Cn
    AIM_ItemLookUp.Show vbModal
    
    'Get data returned from the Item Lookup and populate the PC Detail grid with Item ID
    If Not AIM_ItemLookUp.CancelFlag Then
        LcIdKey = AIM_ItemLookUp.LcIdKey
        ItemKey = AIM_ItemLookUp.ItemKey
        ItDesc = AIM_ItemLookUp.ItDesc
        dgPCDetail.Columns(0).Value = ItemKey
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_BtnClick)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_BtnClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_Change()
On Error GoTo ErrorHandler

    'Set toggle to active. This is used in the Toolbar_Click event
    m_ActiveGridID = ACTIVEGRID_PCDetail
    ToggleToolbar
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_Change)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_Click()
On Error GoTo ErrorHandler

    'Set toggle to active. This is used in the Toolbar_Click event
    m_ActiveGridID = ACTIVEGRID_PCDetail
    ToggleToolbar
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_Click)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsPCDetail)) Then Exit Sub
    
    'Check for unsortable column index
'    If Me.dgAIMMethods.Columns(ColIndex).Name = "" _
'    Then Exit Sub
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
    
    If InStr(rsPCDetail.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgPCDetail.Columns(ColIndex).Name
    ElseIf InStr(rsPCDetail.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgPCDetail.Columns(ColIndex).Name
    End If
    
    'Sort grid by selected column
    rsPCDetail.Sort = Trim(ColName & " " & SortSeq)

    Me.dgPCDetail.ReBind
    If f_IsRecordsetOpenAndPopulated(rsPCDetail) Then Me.dgPCDetail.Rows = rsPCDetail.RecordCount
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_HeadClick)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_InitColumnProps()
On Error GoTo ErrorHandler
     
    Dim IndexCounter As Long
    
    'Set grid properties
    Me.dgPCDetail.ROWHEIGHT = 300
    Me.dgPCDetail.AllowUpdate = True
    
    'Set column properties
    Me.dgPCDetail.Columns(0).Name = "Item"
    Me.dgPCDetail.Columns(0).Caption = getTranslationResource("Item")
    Me.dgPCDetail.Columns(0).Style = ssStyleEditButton
    Me.dgPCDetail.Columns(0).ButtonsAlways = True
    Me.dgPCDetail.Columns(0).Width = 2880
    Me.dgPCDetail.Columns(0).Locked = False
    Me.dgPCDetail.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPCDetail.Columns(0).Alignment = ssCaptionAlignmentLeft

    Me.dgPCDetail.Columns(1).Name = "MinUnits"
    Me.dgPCDetail.Columns(1).Caption = getTranslationResource("Min Units")
    Me.dgPCDetail.Columns(1).Style = ssStyleEdit
    Me.dgPCDetail.Columns(1).Width = 2000
    Me.dgPCDetail.Columns(1).Locked = False
    Me.dgPCDetail.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPCDetail.Columns(1).Alignment = ssCaptionAlignmentRight

    Me.dgPCDetail.Columns(2).Name = "MaxUnits"
    Me.dgPCDetail.Columns(2).Caption = getTranslationResource("Max Units")
    Me.dgPCDetail.Columns(2).Style = ssStyleEdit
    Me.dgPCDetail.Columns(2).Width = 2000
    Me.dgPCDetail.Columns(2).Locked = False
    Me.dgPCDetail.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPCDetail.Columns(2).Alignment = ssCaptionAlignmentRight

    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgPCDetail, ACW_EXPAND
    End If

    'UI Standard settings  -- leave commented code as-is until further actions are defined
    For IndexCounter = 0 To dgPCDetail.Columns.Count - 1
'        dgPCDetail.Columns(IndexCounter).HasHeadBackColor = True
'        dgPCDetail.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgPCDetail.Columns(IndexCounter).Locked = False Then dgPCDetail.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_InitColumnProps)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    m_ActiveGridID = ACTIVEGRID_PCDetail
    ToggleToolbar
    
    'Display the Copy and Print options menu
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_MouseDown)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_PrintBegin)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG07100")
    If StrComp(strMessage, "RPTMSG07100") = 0 Then strMessage = "Date:"
    
    strMessage1 = getTranslationResource("RPTMSG07101")
    If StrComp(strMessage1, "RPTMSG07101") = 0 Then strMessage1 = "AIM Production Constraints Details for Constraint ID:"
    strMessage1 = strMessage1 & " '" & CStr(txtConstraintID.Text) & "'"
    
    strMessage2 = getTranslationResource("RPTMSG07102")
    If StrComp(strMessage2, "RPTMSG07102") = 0 Then strMessage2 = "Page:"

    'Set Report title
    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + _
                            strMessage1 + vbTab + strMessage2 + " <page number>"

    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_PrintInitialize)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As String
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then Exit Sub
    If Not f_IsRecordsetValidAndOpen(rsPCDetail) Then Exit Sub
    
    'Validate data
    If Not GridInputValidation(strMessage) Then
        'If valid, proceed with insert
        rsPCDetail.AddNew

        rsPCDetail!ConstraintID = rsProductionConstraint!ConstraintID

        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsPCDetail!Item = RowBuf.Value(0, 0)
        End If

        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsPCDetail!MinUnits = RowBuf.Value(0, 1)
        End If

        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsPCDetail!MaxUnits = RowBuf.Value(0, 2)
        End If

        rsPCDetail.Update
    Else
        'Else, display the error message returned from the validation function
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        Exit Sub
    End If

    'Check for and display errors/status
    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG07100")
        If StrComp(strMessage, "ERRMSG07100") = 0 Then strMessage = "Error editing AIM Production Constraint Detail record."
        ADOErrorHandler Cn, strMessage
        rsPCDetail.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG07101")
        If StrComp(strMessage, "STATMSG07101") = 0 Then strMessage = "AIM Production Constraint Detail record successfully updated."
        Write_Message strMessage
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_UnboundAddData)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_UnboundAddData", Now, gDRGeneralError, True, Err
    rsPCDetail.CancelUpdate
End Sub

Private Sub dgPCDetail_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler

    'Delete from recordset.
    rsPCDetail.Bookmark = Bookmark
    rsPCDetail.Delete

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_UnboundDeleteRow)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    'Check for other processes that might be jeopardized by executing this event.
    If dgPCDetail.DataChanged Then Exit Sub
    If dgPCDetail.IsAddRow Then Exit Sub
    
    If Not f_IsRecordsetOpenAndPopulated(rsPCDetail) Then Exit Sub
    
    'Align recordset
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsPCDetail.MoveLast
        Else
            rsPCDetail.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsPCDetail.Bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsPCDetail.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsPCDetail.Bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_UnboundPositionData)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim RowCounter As Integer
    Dim RowIndex As Integer

    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsPCDetail) Then Exit Sub
    If dgPCDetail.DataChanged Then Exit Sub
    
    'Align the recordset
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsPCDetail.MoveLast
        Else
            rsPCDetail.MoveFirst
        End If
    Else
        rsPCDetail.Bookmark = StartLocation
        If ReadPriorRows Then
            rsPCDetail.MovePrevious
        Else
            rsPCDetail.MoveNext
        End If
    End If

    'Fetch data into grid
    For RowIndex = 0 To RowBuf.RowCount - 1
        If rsPCDetail.BOF Or rsPCDetail.eof Then Exit For
        
        Select Case RowBuf.ReadType
            Case 0  'All data must be read
                RowBuf.Value(RowIndex, 0) = rsPCDetail!Item
                RowBuf.Value(RowIndex, 1) = rsPCDetail!MinUnits
                RowBuf.Value(RowIndex, 2) = rsPCDetail!MaxUnits

            Case 1  'Only bookmarks must be read
                'That is done anyway, so leave it after end select
            Case Else
                'Cases 2 and 3 are not used by DBGrid... yet?
        End Select
        RowBuf.Bookmark(RowIndex) = rsPCDetail.Bookmark

        If ReadPriorRows Then
            rsPCDetail.MovePrevious
        Else
            rsPCDetail.MoveNext
        End If

        RowCounter = RowCounter + 1
    Next RowIndex

    RowBuf.RowCount = RowCounter

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_UnboundReadData)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPCDetail_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As String
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    rsPCDetail.Bookmark = WriteLocation

    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then Exit Sub
    If Not f_IsRecordsetOpenAndPopulated(rsPCDetail) Then Exit Sub

    'Validate dataq
    'sujit09302003 call new validation fn
    If ValidateCtls_Length = False Then
        Exit Sub
    End If
    If Not GridInputValidation(strMessage) Then
        'If valid, proceed with update
        rsPCDetail!ConstraintID = rsProductionConstraint!ConstraintID

        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsPCDetail!Item = RowBuf.Value(0, 0)
        Else
            If Not IsNull(dgPCDetail.Columns(0).Value) Then rsPCDetail!Item = dgPCDetail.Columns(0).Value
        End If

        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsPCDetail!MinUnits = RowBuf.Value(0, 1)
        Else
            If Not IsNull(dgPCDetail.Columns(1).Value) Then rsPCDetail!MinUnits = dgPCDetail.Columns(1).Value
        End If

        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsPCDetail!MaxUnits = RowBuf.Value(0, 2)
        Else
            If Not IsNull(dgPCDetail.Columns(2).Value) Then rsPCDetail!MaxUnits = dgPCDetail.Columns(2).Value
        End If

        rsPCDetail.Update
    Else
        'Else, display the error message returned from the validation function
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        'Cancel update
        rsPCDetail.CancelUpdate
    End If

    'Check for and display errors/status
    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG07100")
        If StrComp(strMessage, "ERRMSG07100") = 0 Then strMessage = "Error editing AIM Production Constraint Detail record."
        ADOErrorHandler Cn, strMessage
        rsPCDetail.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG07101")
        If StrComp(strMessage, "STATMSG07101") = 0 Then strMessage = "AIM Production Constraint Detail record successfully updated."
        Write_Message strMessage
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgPCDetail_UnboundWriteData)"
     f_HandleErr , , , "AIM_ProductionConstraints::dgPCDetail_UnboundWriteData", Now, gDRGeneralError, True, Err
    rsPCDetail.CancelUpdate
End Sub


Private Sub ckEnabled_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckEnabled_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::ckEnabled_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcConstraintType_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcConstraintType_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::dcConstraintType_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtConstraintDesc_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtConstraintDesc_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::txtConstraintDesc_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtConstraintID_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtConstraintID_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::txtConstraintID_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtCycleDays_GotFocus()
On Error GoTo ErrorHandler

    'Set toggle to inactive. This is used in the Toolbar_Click event
    m_ActiveGridID = ""
    ToggleToolbar

Exit Sub
ErrorHandler:
    ' f_HandleErr Me.Caption & "(txtCycleDays_GotFocus)"
     f_HandleErr , , , "AIM_ProductionConstraints::txtCycleDays_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_ProductionConstraints::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim rsCodeLookup As ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
        
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07100")
    If StrComp(strMessage, "STATMSG07100") = 0 Then strMessage = "Initializing AIM Production Constraints Maintenance..."
    Write_Message strMessage
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    GetTranslatedCaptions Me
    
    'Build/Bind the dcConstraintType Drop Down
     Set AIM_CodeLookup_Get_Sp = New ADODB.Command
     With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_CONSTRAINTTYPE
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcConstraintType.AddItem rsCodeLookup!CodeID + vbTab + rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    
    End If
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
            
    'Build/Bind the dcConstraintType Drop Down
     Set AIM_CodeLookup_Get_Sp = New ADODB.Command
     With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_RATIOTYPE
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcRatioType.AddItem rsCodeLookup!CodeID + vbTab + rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    
    End If
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
            
    'Open the Record Set
    RtnCode = f_ProdConstraint_GetKey(rsProductionConstraint, ConstraintID, SQL_GetFirst)
    'If there are no records, set to add new mode
    If Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then
        f_ProdConstraint_InitializeNew
    End If
    
    'Refresh the form
    RefreshForm
    Me.txtCycleDays.Spin.Visible = 1
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_ProductionConstraints::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Check for unsaved changes first
    RtnCode = f_ProdConstraint_Commit(True)
    If RtnCode <> SUCCEED Then
        Cancel = True
        Exit Sub
    End If
    
    'Close and destroy objects and connections
    If f_IsRecordsetValidAndOpen(rsProductionConstraint) Then rsProductionConstraint.Close
    Set rsProductionConstraint = Nothing

    If f_IsRecordsetValidAndOpen(rsPCDetail) Then rsPCDetail.Close
    Set rsPCDetail = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_ProductionConstraints::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next

End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim FldNames As Variant
    Dim CopyData As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If f_IsRecordsetOpenAndPopulated(rsPCDetail) Then
                Clipboard.Clear
                
                For IndexCounter = 0 To rsPCDetail.Fields.Count - 1
                    FldNames = FldNames + rsPCDetail.Fields(IndexCounter).Name + vbTab
                Next IndexCounter
                
                FldNames = FldNames + vbCrLf
                    
                rsPCDetail.MoveFirst
                CopyData = rsPCDetail.GetString(adClipString)
                
                Clipboard.SetText FldNames + CopyData, vbCFText
            End If
            
        Case 1          'Print
            Me.dgPCDetail.PrintData ssPrintAllRows, False, True
        
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
    f_HandleErr , , , "AIM_ProductionConstraints::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim PCBookmark As Variant
    Dim strMessage As String
    
    'Clear Errors
    Cn.Errors.Clear
    Write_Message ""
       
    'ToggleToolbar
    'sujit09302003 to call new validation
    If Tool.ID = "ID_Save" Then
        If ValidateCtls_Length = False Then
            Exit Sub
        End If
    End If
    'Alert user to possible change, and save changes, if chosen
    Select Case Tool.ID
        Case "ID_AddNew", _
         "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
         "ID_GoToBookMark", "ID_LookUp"
            
            'Check for updates, first
            RtnCode = f_ProdConstraint_Commit(True)
            If RtnCode <> SUCCEED Then Exit Sub

        Case "ID_Close"
            'Close connections and prepare to exit form
            If dgPCDetail.DataChanged Then dgPCDetail.Update
            Unload Me
            Exit Sub
    End Select

    'Toggle between display record navigation commands for the header or detail,
    'based on whether the cursor is currently on the detail grid.
'    If dgPCDetail.SelBookmarks.Count >= 1 _
'    Or m_ActiveGridID = ACTIVEGRID_PCDetail _
'    Then    'Detail
    If m_ActiveGridID = ACTIVEGRID_PCDetail _
    Then    'Detail
        ToolbarOptions_PCDetail Tool
        
    Else    'Header
        ToolbarOptions_PCHeader Tool
    End If
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_ProductionConstraints::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function f_ProdConstraint_GetKey(ByRef rs As ADODB.Recordset, _
                                        p_ConstraintID As String, _
                                        ByVal Action As SQL_ACTIONS) As Integer
On Error GoTo ErrorHandler
                
    Dim RtnCode As Integer
    Dim AIM_ProductionConstraint_GetKey_Sp As ADODB.Command
    
    'Initialize Command object
    Set AIM_ProductionConstraint_GetKey_Sp = New ADODB.Command
    With AIM_ProductionConstraint_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ProductionConstraint_GetKey_Sp"
    End With
    
    'Re-initialize the recordset
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rs) Then rs.Close
        If Err.Number = 3219 Then
            'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
            'Discard the rs and move on.
            Set rs = Nothing
        End If
    Set rs = Nothing
    On Error GoTo ErrorHandler
    
    'Re-create a new instance of the Recordset
    Set rs = New ADODB.Recordset
    
    'Set recordset cursor and lock properties
    With rs
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    'Get the Production Constraint header Data, using the stored procedure in the Command object
    With AIM_ProductionConstraint_GetKey_Sp
        .Parameters.Refresh
        .Parameters("@ConstraintID").Value = p_ConstraintID
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
        
        'Fetch in recordset
        rs.Open AIM_ProductionConstraint_GetKey_Sp
        
        RtnCode = .Parameters(0).Value
    End With
    
    'Validate returns from the operation, and set up returns to calling function
    If f_IsRecordsetOpenAndPopulated(rs) Then
        p_ConstraintID = rs!ConstraintID
        f_ProdConstraint_GetKey = SUCCEED
    Else
        p_ConstraintID = ""
        f_ProdConstraint_GetKey = RtnCode
    End If
        
    'Destroy command object
    If Not (AIM_ProductionConstraint_GetKey_Sp Is Nothing) Then Set AIM_ProductionConstraint_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_ProductionConstraint_GetKey_Sp = Nothing
    
Exit Function
ErrorHandler:
    If Not (AIM_ProductionConstraint_GetKey_Sp Is Nothing) Then Set AIM_ProductionConstraint_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_ProductionConstraint_GetKey_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_ProdConstraint_GetKey)"
     f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_GetKey", Now, gDRGeneralError, True, Err
End Function

Private Function f_ProdConstraint_InitializeNew()
On Error GoTo ErrorHandler
        
    'Set flags
    m_NewRecord = True
    
    'Clear current recordset for header
    If f_IsRecordsetValidAndOpen(rsProductionConstraint) Then rsProductionConstraint.Close
    Set rsProductionConstraint = Nothing
    
    'Clear current recordset for detail. Set grid to empty and inactive
    If f_IsRecordsetValidAndOpen(rsPCDetail) Then rsPCDetail.Close
    dgPCDetail.Reset
        
    'Set default values for header (detail grid should be empty and inactive)
    ConstraintID = ""
    dcConstraintID.Text = ""
    txtConstraintID.Text = getTranslationResource("New Record")
    txtConstraintDesc.Text = getTranslationResource("New Record Description")
    dcConstraintType.Text = ""
    txtCycleDays = 1
    dcRatioType.Text = ""
    ckEnabled.Value = vbUnchecked
    
    'Enable all header fields
    ToggleFieldAccess True
    fraPCDetails.Enabled = False
    dgPCDetail.Enabled = False
    
    'Enable key field for editing
    txtConstraintID.Enabled = True
    txtConstraintID.Visible = True
    dcConstraintID.Visible = False
    
    'Disable toolbar actions that should be protected.
    'Hide add, delete and the dropdown
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_SetBookMark").Enabled = False
    If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
    
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
    Me.tbNavigation.Refresh

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_ProdConstraint_InitializeNew)", Err.Description
    f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_InitializeNew", Now, gDRGeneralError, True, Err
End Function

Private Function f_ProdConstraint_Changed() As Boolean
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long
    
    'Set default return value to "Unchanged"
    f_ProdConstraint_Changed = False
    
    'If this is a new record, then set return value to "Changed"
    If m_NewRecord Then
        f_ProdConstraint_Changed = True
        Exit Function
    End If
    
    If Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then Exit Function
    
    'Compare current input fields with recordset values for changes.
    If StrComp(txtConstraintID.Text, rsProductionConstraint!ConstraintID, vbTextCompare) <> 0 Then
        strMessage = getTranslationResource("MSGBOX07104")
        If StrComp(strMessage, "MSGBOX07104") = 0 Then strMessage = "Changes to key reference field (Constraint ID) are not allowed!"
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        f_ProdConstraint_Changed = False
        dcConstraintID.Text = rsProductionConstraint!ConstraintID
        Exit Function
    End If
    
    If StrComp(txtConstraintDesc.Text, rsProductionConstraint!ConstraintDesc) <> 0 Then f_ProdConstraint_Changed = True
    If StrComp(dcConstraintType.Text, rsProductionConstraint!ConstraintType) <> 0 Then f_ProdConstraint_Changed = True
    If txtCycleDays.Value <> rsProductionConstraint!CycleDays Then f_ProdConstraint_Changed = True
    If StrComp(dcRatioType.Text, rsProductionConstraint!RatioType) <> 0 Then f_ProdConstraint_Changed = True
    If (ckEnabled.Value = vbChecked And rsProductionConstraint!EnableConstraint <> "Y") _
    Or (ckEnabled.Value = vbUnchecked And rsProductionConstraint!EnableConstraint = "Y") _
    Then
        f_ProdConstraint_Changed = True
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_ProdConstraint_Changed)", Err.Description
    f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_Changed", Now, gDRGeneralError, True, Err
End Function

Private Function f_ProdConstraint_Commit(Optional p_Prompt As Boolean = False) As Long
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long

    f_ProdConstraint_Commit = SUCCEED   'default to success
    
    'Check for updates
    If f_ProdConstraint_Changed Then
        'Validate permissions; Check prompt; Then Save
        If gAccessLvl = 1 _
        Then
            'No access = no action
            strMessage = getTranslationResource("MSGBOX07109")
            If StrComp(strMessage, "MSGBOX07109") = 0 Then strMessage = "Insufficient access rights. Operation cancelled."
            MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        Else
            If p_Prompt = True Then
                strMessage = getTranslationResource("MSGBOX07105")
                If StrComp(strMessage, "MSGBOX07105") = 0 Then strMessage = "Do you wish to save the changes made to the current record?"
                RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
                If RtnCode <> vbYes Then
                    'CancelUpdate
                    If m_NewRecord = True Then
                        m_NewRecord = False
                        'Show add, delete and the dropdown. Hide the text box
                        Me.tbNavigation.Tools("ID_AddNew").Enabled = True
                        Me.tbNavigation.Tools("ID_Delete").Enabled = True
                        Me.tbNavigation.Tools("ID_SetBookMark").Enabled = True
                        
                        dcConstraintID.Visible = True
                        txtConstraintID.Visible = False
                    End If
                    Exit Function
                Else
                    'User has opted to save... proceed
                    RtnCode = f_ProdConstraint_Save
                    f_ProdConstraint_Commit = RtnCode
                End If
            Else
                'User has clicked the save icon, no prompt required... proceed
                RtnCode = f_ProdConstraint_Save
                f_ProdConstraint_Commit = RtnCode
            End If
        End If
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_ProdConstraint_Commit)", Err.Description
     f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_Commit", Now, gDRGeneralError, True, Err
End Function

Private Function f_ProdConstraint_Delete() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim AIM_ProductionConstraint_Delete_Sp As ADODB.Command
    Dim strMessage As String
    
    'Initialize the Command object
    Set AIM_ProductionConstraint_Delete_Sp = New ADODB.Command
    With AIM_ProductionConstraint_Delete_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ProductionConstraint_Delete_Sp"
    End With
    
    'Set parameters
    With AIM_ProductionConstraint_Delete_Sp
        .Parameters.Refresh
        .Parameters("@ConstraintID").Value = rsProductionConstraint!ConstraintID
    End With
    
    'Run the Stored Procedure
    AIM_ProductionConstraint_Delete_Sp.Execute , , adExecuteNoRecords
    
    'Check for and display errors/status
    RtnCode = AIM_ProductionConstraint_Delete_Sp.Parameters(0).Value
    If RtnCode < 0 Then
        strMessage = getTranslationResource("ERRMSG07101")
        If StrComp(strMessage, "ERRMSG07101") = 0 Then strMessage = "Failed to delete the current Production Constraint."
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, strMessage
        End If
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        f_ProdConstraint_Delete = FAIL
        
    Else
        f_ProdConstraint_Delete = SUCCEED
        'Get the previous record, if available
        RtnCode = f_ProdConstraint_GetKey(rsProductionConstraint, txtConstraintID.Text, SQL_Getlt)
        'If there are no records, set to add new mode
        If RtnCode <> SUCCEED _
        Or Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then
            f_ProdConstraint_InitializeNew
        End If
            
        RefreshForm
    End If

    'Destroy objects
    If Not (AIM_ProductionConstraint_Delete_Sp Is Nothing) Then Set AIM_ProductionConstraint_Delete_Sp.ActiveConnection = Nothing
    Set AIM_ProductionConstraint_Delete_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_ProductionConstraint_Delete_Sp Is Nothing) Then Set AIM_ProductionConstraint_Delete_Sp.ActiveConnection = Nothing
    Set AIM_ProductionConstraint_Delete_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_ProdConstraint_Delete)"
    f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_Delete", Now, gDRGeneralError, True, Err
End Function

Private Function f_ProdConstraint_Save() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim AIM_AIMProductionConstraint_Save_Sp As ADODB.Command
    Dim strMessage As String
    
    'Validate the data
    RtnCode = f_ProdConstraint_Validate
    If RtnCode <> SUCCEED Then
        f_ProdConstraint_Save = RtnCode
        Exit Function
    End If
    
    'Initialize the Command object
    Set AIM_AIMProductionConstraint_Save_Sp = New ADODB.Command
    With AIM_AIMProductionConstraint_Save_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMProductionConstraint_Save_Sp"
        
        .Parameters.Refresh
    End With
    
    'Set Parameters
    With AIM_AIMProductionConstraint_Save_Sp
        .Parameters("@ConstraintID").Value = txtConstraintID.Text
        .Parameters("@ConstraintType").Value = dcConstraintType.Text
        .Parameters("@ConstraintDesc").Value = txtConstraintDesc.Text
        .Parameters("@CycleDays").Value = txtCycleDays.Value
        .Parameters("@RatioType").Value = dcRatioType.Text
        .Parameters("@EnableConstraint").Value = IIf(ckEnabled.Value = vbChecked, "Y", "N")
        
'        If m_NewRecord = True Then
'            'Insert
'            .Parameters("@InsertUpdate").Value = "I"
'        Else
'            'Update
'            .Parameters("@InsertUpdate").Value = "U"
'        End If
    End With
                
    'Run the Stored Procedure
    AIM_AIMProductionConstraint_Save_Sp.Execute , , adExecuteNoRecords
    
    'Check for and display errors/status
    RtnCode = AIM_AIMProductionConstraint_Save_Sp.Parameters(0).Value
    If RtnCode < 0 Then
        f_ProdConstraint_Save = FAIL
        strMessage = getTranslationResource("ERRMSG07102")
        If StrComp(strMessage, "ERRMSG07102") = 0 Then strMessage = "Error editing AIM Production Constraint record."
        Write_Message strMessage
    Else
        f_ProdConstraint_Save = SUCCEED
    
        strMessage = getTranslationResource("STATMSG07102")
        If StrComp(strMessage, "STATMSG07102") = 0 Then strMessage = "AIM Production Constraint record successfully updated."
        Write_Message strMessage
        If m_NewRecord = True Then m_NewRecord = False
        
    End If

    'Destroy objects
    If Not (AIM_AIMProductionConstraint_Save_Sp Is Nothing) Then Set AIM_AIMProductionConstraint_Save_Sp.ActiveConnection = Nothing
    Set AIM_AIMProductionConstraint_Save_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_AIMProductionConstraint_Save_Sp Is Nothing) Then Set AIM_AIMProductionConstraint_Save_Sp.ActiveConnection = Nothing
    Set AIM_AIMProductionConstraint_Save_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_ProdConstraint_Save)"
     f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_Save", Now, gDRGeneralError, True, Err
End Function

Private Function f_ProdConstraint_Validate() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX07106")
    If StrComp(strMessage, "MSGBOX07106") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating input fields. Check for emtpy or null values where they are not allowed in the database.
    If txtConstraintID.Text = "" _
    Or StrComp(txtConstraintID.Text, getTranslationResource("New Record")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    lblProdConstraint(0).Caption  'Constraint ID
        If txtConstraintID.Visible = False Then
            dcConstraintID.SetFocus
        Else
            txtConstraintID.SetFocus
        End If
        MessageRequired = True
    End If
    
    If Me.dcConstraintType.Text = "" _
    Then
        strMessage = strMessage & vbCrLf & _
                    lblProdConstraint(1).Caption  'Constraint Type
        dcConstraintType.SetFocus
        MessageRequired = True
    End If
    
    If Not (IsNumeric(txtCycleDays.Value)) _
    Or (txtCycleDays.Value < 0) Then
        strMessage = strMessage & vbCrLf & _
                    lblProdConstraint(3).Caption  'Cycle Days
        txtCycleDays.SetFocus
        MessageRequired = True
    End If
    
    If Me.dcRatioType.Text = "" _
    Then
        strMessage = strMessage & vbCrLf & _
                    lblProdConstraint(4).Caption  'Ratio Type
        dcRatioType.SetFocus
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        f_ProdConstraint_Validate = FAIL
    Else
        f_ProdConstraint_Validate = SUCCEED
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_ProdConstraint_Validate)"
     f_HandleErr , , , "AIM_ProductionConstraints::f_ProdConstraint_Validate", Now, gDRGeneralError, True, Err
End Function

Private Function f_GetPCDetails()
On Error GoTo ErrorHandler

    Dim AIM_PCDetail_Get_Sp As ADODB.Command
    Dim IndexCounter As Long
    
    'Validate ConstraintID
    If Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then Exit Function
    
    'Initialize the Command object
    Set AIM_PCDetail_Get_Sp = New ADODB.Command
    With AIM_PCDetail_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ProductionConstraintDetail_Get_Sp"
        .Parameters.Refresh
    End With
    
    'Set parameters
    With AIM_PCDetail_Get_Sp
        .Parameters.Refresh
        .Parameters("@ConstraintID").Value = rsProductionConstraint!ConstraintID
    End With
    
    'Fetch ConstraintDetails into recordset
    Set rsPCDetail = Nothing
    Set rsPCDetail = New ADODB.Recordset
    With rsPCDetail
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    rsPCDetail.Open AIM_PCDetail_Get_Sp
    
    'Bind to grid
    Me.dgPCDetail.ReBind
    
    'Set grid row count
    If f_IsRecordsetOpenAndPopulated(rsPCDetail) Then dgPCDetail.Rows = rsPCDetail.RecordCount

    'Destroy objects
    If Not (AIM_PCDetail_Get_Sp Is Nothing) Then Set AIM_PCDetail_Get_Sp.ActiveConnection = Nothing
    Set AIM_PCDetail_Get_Sp = Nothing
    
Exit Function
ErrorHandler:
    If Not (AIM_PCDetail_Get_Sp Is Nothing) Then Set AIM_PCDetail_Get_Sp.ActiveConnection = Nothing
    Set AIM_PCDetail_Get_Sp = Nothing
    'Err.Raise Err.Number, Err.source & "(f_GetPCDetails)", Err.Description
    f_HandleErr , , , "AIM_ProductionConstraints::f_GetPCDetails", Now, gDRGeneralError, True, Err
End Function

Private Function RefreshForm()
On Error GoTo ErrorHandler

    'Check recordset
    If Not f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then Exit Function

    'Enable/Disable Key fields
    If rsProductionConstraint.EditMode <> adEditAdd Then
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            ToggleFieldAccess False
        Else
            ToggleFieldAccess True
        End If
        
        'Refresh the ConstraintID dropdown
        f_GetConstraintIDList
        ConstraintID = rsProductionConstraint!ConstraintID
        Me.dcConstraintID.Text = ConstraintID
        txtConstraintID = ConstraintID
        
        'Show add, delete and the dropdown. Hide the text box
        Me.tbNavigation.Tools("ID_AddNew").Enabled = True
        Me.tbNavigation.Tools("ID_Delete").Enabled = True
        Me.tbNavigation.Tools("ID_SetBookMark").Enabled = True
        dcConstraintID.Visible = True
        txtConstraintID.Visible = False
'        'Disable key field for editing
'        Me.txtConstraintID.Enabled = False
        
        'Enable Detail grid for editing
        Me.fraPCDetails.Enabled = True
        dgPCDetail.Enabled = True
        
        'Set Record Status to "Edit"
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    End If
    
    'Sync. input fields with recordset
    Me.txtConstraintDesc.Text = rsProductionConstraint!ConstraintDesc
    Me.dcConstraintType.Text = rsProductionConstraint!ConstraintType
    Me.txtCycleDays.Value = rsProductionConstraint!CycleDays
    Me.dcRatioType.Text = rsProductionConstraint!RatioType
    Me.ckEnabled = IIf(rsProductionConstraint!EnableConstraint = "Y", vbChecked, vbUnchecked)
    
    'Get detail records
    f_GetPCDetails

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(RefreshForm)", Err.Description
    f_HandleErr , , , "AIM_ProductionConstraints::RefreshForm", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleFieldAccess(p_Enabled As Boolean)
On Error GoTo ErrorHandler

    'Based on function argument, enable/disable all form fields for editing
    Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_Delete").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_AddNew").Enabled = p_Enabled
    
    Me.txtConstraintID.Enabled = p_Enabled
    Me.ckEnabled.Enabled = p_Enabled
    Me.dcConstraintType.Enabled = p_Enabled
    Me.txtConstraintDesc.Enabled = p_Enabled
    Me.txtCycleDays.Enabled = p_Enabled
    Me.dcRatioType.Enabled = p_Enabled
    
    Me.dgPCDetail.Enabled = p_Enabled
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(ToggleFieldAccess)", Err.Description
    f_HandleErr , , , "AIM_ProductionConstraints::ToggleFieldAccess", Now, gDRGeneralError, True, Err
End Function

Private Function ToolbarOptions_PCHeader(Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
            
    'Navigate
    Select Case Tool.ID
    Case "ID_AddNew"
        'Call Initialize function for Header. This will set the details, accordingly
        f_ProdConstraint_InitializeNew
        Exit Function
        
    Case "ID_Delete"
        'Verify operation and call Delete function for the Header. This will remove the detail records, too.
        strMessage = getTranslationResource("MSGBOX07101")
        If StrComp(strMessage, "MSGBOX07101") = 0 Then strMessage = "Delete the current Production Constraint and its details"
        RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, getTranslationResource(Me.Caption))
        If RtnCode = vbYes Then
            RtnCode = f_ProdConstraint_Delete
        End If
        
    Case "ID_GetFirst"
        'Navigate to the first record
        f_ProdConstraint_GetKey rsProductionConstraint, ConstraintID, SQL_GetFirst
        
    Case "ID_GetPrev"
        'Navigate to the previous record
        f_ProdConstraint_GetKey rsProductionConstraint, ConstraintID, SQL_Getlt

    Case "ID_GetNext"
        'Navigate to the next record
        f_ProdConstraint_GetKey rsProductionConstraint, ConstraintID, SQL_GetGT

    Case "ID_GetLast"
        'Navigate to the last record
        f_ProdConstraint_GetKey rsProductionConstraint, ConstraintID, SQL_GetLast

    Case "ID_Save"
        'Call Save function for the header.
        RtnCode = f_ProdConstraint_Commit(False)
        If RtnCode = SUCCEED Then
            ConstraintID = txtConstraintID.Text
            RtnCode = f_ProdConstraint_GetKey(rsProductionConstraint, ConstraintID, SQL_GetEq)
            If RtnCode = SUCCEED Then dcConstraintID.Text = ConstraintID
        Else
            'Commit failed due to some reason. Return user to maint. screen.
            dcRatioType.SetFocus
        End If
        
    Case "ID_SetBookMark"
        'Toggle bookmark
        If IsEmpty(PCBookmark) _
        Or IsNull(PCBookmark) _
        Then
            PCBookmark = ConstraintID
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
        Else
            PCBookmark = Null
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
        End If
                
    Case "ID_GoToBookMark"
        'Navigate to the bookmarked record
        If Not IsEmpty(PCBookmark) _
        Or Not IsNull(PCBookmark) _
        Then
            ConstraintID = PCBookmark
            RtnCode = f_ProdConstraint_GetKey(rsProductionConstraint, ConstraintID, SQL_GetEq)
            If RtnCode = SUCCEED Then
                txtConstraintID.Text = ConstraintID
                dcConstraintID.Text = ConstraintID
            End If
        End If

    Case "ID_LookUp"
        'Currently unavailable
'            'Save the current Bookmark
'            PCBookmark = rsProductionConstraint.Bookmark

'            'Display the Production Constraints Lookup screen
'            AIM_UsersLookUp.UserIdKey = Me.txtConstraintID.Text
'            Set AIM_UsersLookUp.Cn = Cn
'            AIM_UsersLookUp.Show vbModal
'
'            'Get data returned from the Lookup and populate the PC Constraint ID with returned value.
'            'Refresh form to synchronise display
'            If AIM_UsersLookUp.CancelFlag Then
'                rsProductionConstraint.Bookmark = PCBookmark
'                Exit Sub
'            Else
'                rsProductionConstraint.MoveFirst
'                UserId = AIM_UsersLookUp.UserIdKey
'                rsProductionConstraint.Find "userid = '" + UserId + "'"
'            End If

    Case "ID_Close"
        'Prepare to exit form
        If f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then
            rsProductionConstraint.CancelUpdate
        End If
        Unload Me
        Exit Function
   End Select
    
    'Refresh
    Select Case Tool.ID
        Case "ID_GetFirst", "ID_GetPrev", "ID_GetNext", "ID_GetLast", _
             "ID_GotoBookmark", "ID_Delete", "ID_Save"
            If f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then
                m_NewRecord = False
                'Show add, delete and the dropdown. Hide the text box
                Me.tbNavigation.Tools("ID_AddNew").Enabled = True
                Me.tbNavigation.Tools("ID_Delete").Enabled = True
                Me.tbNavigation.Tools("ID_SetBookMark").Enabled = True
            Else
                m_NewRecord = True
                'Show add, delete and the dropdown. Hide the text box
                Me.tbNavigation.Tools("ID_AddNew").Enabled = False
                Me.tbNavigation.Tools("ID_Delete").Enabled = False
                Me.tbNavigation.Tools("ID_SetBookMark").Enabled = False
            End If
                
        Case Else
            'If m_NewRecord = True Then m_NewRecord = False
    End Select
    
    'Set PCDetailBookmark = null if header record has been changed
    If Tool.ID <> "ID_Save" Then
        PCDetailBookmark = Null
        If IsEmpty(PCBookmark) _
        Or IsNull(PCBookmark) _
        Then
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
        End If
    End If
    
    'Sync. display to recordset
    RefreshForm

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ToolbarOptions_ForecastCriteria)"
    f_HandleErr , , , "AIM_ProductionConstraints::ToolbarOptions_ForecastCriteria", Now, gDRGeneralError, True, Err
End Function

Private Function ToolbarOptions_PCDetail(Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    'Save unsaved updates, first
    If dgPCDetail.DataChanged Then dgPCDetail.Update
    
    Select Case Tool.ID
        Case "ID_GetFirst"
            'Navigate to the first detail record
            Me.dgPCDetail.MoveFirst
             
        Case "ID_GetPrev"
            'Navigate to the previous detail record
            Me.dgPCDetail.MovePrevious
        
        Case "ID_GetNext"
            'Navigate to the next detail record
            Me.dgPCDetail.MoveNext
        
        Case "ID_GetLast"
            'Navigate to the last detail record
            Me.dgPCDetail.MoveLast
        
        Case "ID_AddNew"
            'Trigger Initialize event for detail.
            Me.dgPCDetail.AddNew
    
        Case "ID_Delete"
            'Trigger Delete event for detail.
            Me.dgPCDetail.DeleteSelected
            Me.dgPCDetail.SetFocus
            
        Case "ID_Save"
            'Trigger Update/Insert event for detail.
            If dgPCDetail.DataChanged Then dgPCDetail.Update

        Case "ID_SetBookMark"
            'Toggle bookmark
            If IsEmpty(PCDetailBookmark) _
            Or IsNull(PCDetailBookmark) _
                Then
                    PCDetailBookmark = dgPCDetail.Bookmark
                    Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
                Else
                    PCDetailBookmark = Null
                    Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
                End If
                
        Case "ID_GoToBookMark"
            'Navigate to the last bookmarked record
            If Not IsEmpty(PCDetailBookmark) _
            Or Not IsNull(PCDetailBookmark) _
            Then
                dgPCDetail.Bookmark = PCDetailBookmark
            End If

    End Select
    
Exit Function
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPCDetailMenu_Click)"
     f_HandleErr , , , "AIM_ProductionConstraints::cmdPCDetailMenu_Click", Now, gDRGeneralError, True, Err

End Function

Private Function GridInputValidation(p_Message As String, Optional p_ColIndex As Integer = -1) As Boolean
On Error GoTo ErrorHandler
    
    Dim lngCounter As Integer
    Dim LoopUpperBound As Integer
    Dim LoopLowerBound As Integer
    
    Dim RtnCode As Integer
    
    'ErrorMessage
    p_Message = getTranslationResource("MSGBOX07107")
    If StrComp(p_Message, "MSGBOX07107") = 0 Then _
        p_Message = "Some required fields are missing. Please review before moving to another row."
        
    'Check if it's just one field to be validated, or all. Set Counter accordinly
    If p_ColIndex >= 0 Then
        LoopLowerBound = p_ColIndex
        LoopUpperBound = p_ColIndex
    Else
        LoopLowerBound = 0
        LoopUpperBound = dgPCDetail.Columns.Count - 1
    End If

    'Process columns and check for emtpy or null values where they are not allowed in the database.
    For lngCounter = LoopLowerBound To LoopUpperBound
        If dgPCDetail.Columns(lngCounter).Style = ssStyleEdit _
        Or dgPCDetail.Columns(lngCounter).Style = ssStyleEditButton _
        Or dgPCDetail.Columns(lngCounter).Style = ssStyleComboBox _
        Then
            If dgPCDetail.Columns(lngCounter).Value = "" _
            Or IsNull(dgPCDetail.Columns(lngCounter).Value) _
            Then
                'Cancel = True
                GridInputValidation = True
                Exit Function
            
            ElseIf lngCounter = 1 _
            Or lngCounter = 2 _
            Then
                '1=Min Units; 2=Max Units
                If Not IsNumeric(dgPCDetail.Columns(lngCounter).Value) Then
                    p_Message = getTranslationResource("MSGBOX07312")
                    If StrComp(p_Message, "MSGBOX07312") = 0 Then _
                        p_Message = "The following data is required, or is invalid. Please provide correct values in the expected format for:"
                    p_Message = p_Message & " " & dgPCDetail.Columns(lngCounter).Caption
                    'Cancel = True
                    GridInputValidation = True
                    Exit Function
                End If
            End If
        End If
    Next
    
    If LoopLowerBound <> LoopUpperBound Then
        'check all columns
        If Not IsNumeric(dgPCDetail.Columns("MinUnits").Value) Then
            p_Message = getTranslationResource("MSGBOX07312")
            If StrComp(p_Message, "MSGBOX07312") = 0 Then _
                p_Message = "The following data is required, or is invalid. Please provide correct values in the expected format for:"
            p_Message = p_Message & " " & dgPCDetail.Columns("MinUnits").Caption
            'Cancel = True
            GridInputValidation = True
            Exit Function
        End If
        
        If Not IsNumeric(dgPCDetail.Columns("MaxUnits").Value) Then
            p_Message = getTranslationResource("MSGBOX07312")
            If StrComp(p_Message, "MSGBOX07312") = 0 Then _
                p_Message = "The following data is required, or is invalid. Please provide correct values in the expected format for:"
            p_Message = p_Message & " " & dgPCDetail.Columns("MaxUnits").Caption 'Cancel = True
            GridInputValidation = True
            Exit Function
        End If
        
        If CLng(dgPCDetail.Columns("MinUnits").Value) > CLng(dgPCDetail.Columns("MaxUnits").Value) _
        Then
            'ErrorMessage
            p_Message = getTranslationResource("ERRMSG07103")
            If StrComp(p_Message, "ERRMSG07103") = 0 Then _
                    p_Message = "Error! Minimum Units cannot be greater than the Maximum Units."
            'Cancel = True
            GridInputValidation = True
            Exit Function
        End If
    End If

    'Cancel = false
    GridInputValidation = False
    
Exit Function
ErrorHandler:
   Err.Raise Err.Number, Err.source, Err.Description & "(GridInputValidation)"
   f_HandleErr , , , "AIM_ProductionConstraints::GridInputValidation", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleToolbar()
On Error GoTo ErrorHandler

    If m_ActiveGridID = ACTIVEGRID_PCDetail Then
        'Set toolbar tooltiptexts to reflect the Detail
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Detail")
        Me.tbNavigation.Tools("ID_GetFirst").ToolTipText = getTranslationResource("Get First Detail Record")
        Me.tbNavigation.Tools("ID_GetLast").ToolTipText = getTranslationResource("Get Last Detail Record")
        Me.tbNavigation.Tools("ID_GetNext").ToolTipText = getTranslationResource("Get Next Detail Record")
        Me.tbNavigation.Tools("ID_GetPrev").ToolTipText = getTranslationResource("Get Previous Detail Record")
        Me.tbNavigation.Tools("ID_GoToBookmark").ToolTipText = getTranslationResource("Go To Detail Bookmark")
        Me.tbNavigation.Tools("ID_Save").ToolTipText = getTranslationResource("Save Detail Record(s)")
        Me.tbNavigation.Tools("ID_SetBookMark").ToolTipText = getTranslationResource("Set Detail Bookmark")
        Me.tbNavigation.Tools("ID_AddNew").ToolTipText = getTranslationResource("Add New Detail Record")
        Me.tbNavigation.Tools("ID_Delete").ToolTipText = getTranslationResource("Delete Selected Detail Record(s)")
    Else
        'Set toolbar tooltiptexts to reflect the Header
        If f_IsRecordsetOpenAndPopulated(rsProductionConstraint) Then
            If rsProductionConstraint.EditMode = adEditAdd Then
                Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
            Else
                Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
            End If
        End If
        Me.tbNavigation.Tools("ID_GetFirst").ToolTipText = getTranslationResource("Get First Record")
        Me.tbNavigation.Tools("ID_GetLast").ToolTipText = getTranslationResource("Get Last Record")
        Me.tbNavigation.Tools("ID_GetNext").ToolTipText = getTranslationResource("Get Next Record")
        Me.tbNavigation.Tools("ID_GetPrev").ToolTipText = getTranslationResource("Get Previous Record")
        Me.tbNavigation.Tools("ID_GoToBookmark").ToolTipText = getTranslationResource("Go To Bookmark")
        Me.tbNavigation.Tools("ID_Save").ToolTipText = getTranslationResource("Save Current Record")
        Me.tbNavigation.Tools("ID_SetBookMark").ToolTipText = getTranslationResource("Set Bookmark")
        Me.tbNavigation.Tools("ID_AddNew").ToolTipText = getTranslationResource("Add New Record")
        Me.tbNavigation.Tools("ID_Delete").ToolTipText = getTranslationResource("Delete Current Record")
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(ToggleToolbar)", Err.Description
     f_HandleErr , , , "AIM_ProductionConstraints::ToggleToolbar", Now, gDRGeneralError, True, Err
End Function

Private Function f_GetConstraintIDList()
On Error GoTo ErrorHandler

    Dim rsTemp As ADODB.Recordset
    Dim strSQL As String

    dcConstraintID.RemoveAll
    dcConstraintID.Reset
    
    'Build the Forecast List Drop Down
    strSQL = "SELECT ConstraintID, ConstraintDesc " & _
            " FROM AIMProductionConstraint " & _
            " ORDER BY ConstraintID "
            
    Set rsTemp = New ADODB.Recordset
    rsTemp.Open strSQL, Cn, adOpenStatic, adLockReadOnly, adCmdText
    
    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
        rsTemp.MoveFirst
        Do Until rsTemp.eof
            dcConstraintID.AddItem rsTemp!ConstraintID & vbTab & rsTemp!ConstraintDesc
            rsTemp.MoveNext
        Loop
'        dcConstraintID_InitColumnProps
    End If
    
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    'Err.Raise Err.Number, Err.source & "(f_GetConstraintIDList)", Err.Description
     f_HandleErr , , , "AIM_ProductionConstraints::f_GetConstraintIDList", Now, gDRGeneralError, True, Err
End Function
'Added this function to validate control field length    'sujit09302003
Private Function ValidateCtls_Length() As Boolean
    On Error GoTo ErrorHandler
    Dim strMessage As String
    Dim RtnCode As Integer
    
    ValidateCtls_Length = False
    If Len(dcConstraintType.Text) > 30 Then
        dcConstraintType.Text = Left(dcConstraintType.Text, 30)
        strMessage = getTranslationResource("MSGBOX07110")
        If StrComp(strMessage, "MSGBOX07110") = 0 Then strMessage = "You have entered invalid data.(Max length should be 30)"
        RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
        dcConstraintType.SetFocus
        Exit Function
    End If

    If Len(dcRatioType.Text) > 30 Then
        dcRatioType.Text = Left(dcRatioType.Text, 30)
        strMessage = getTranslationResource("MSGBOX07110")
        If StrComp(strMessage, "MSGBOX07110") = 0 Then strMessage = "You have entered invalid data.(Max length should be 30)"
        RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
        dcRatioType.SetFocus
        Exit Function
    End If
    
    '////////////////////////
    Dim grdCols, maxLength As Integer
    Dim grdCounter As Integer
    
    For grdCols = 0 To dgPCDetail.Cols - 1
        If dgPCDetail.Rows > 0 Then
            If grdCols = 0 Then
                If Len(dgPCDetail.Columns(grdCols).Value) > 25 Then
                    ValidateCtls_Length = False
                    strMessage = getTranslationResource("MSGBOX07111")
                    If StrComp(strMessage, "MSGBOX07111") = 0 Then strMessage = "You have entered invalid data.(Max length should be 25)"
                    RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
                    If dgPCDetail.Enabled Then
                        dgPCDetail.SetFocus
                    End If
                    Exit Function
                End If
            ElseIf (grdCols = 1 Or grdCols = 2) Then
                If dgPCDetail.Columns(grdCols).Value > 99999999# Then
                    ValidateCtls_Length = False
                    strMessage = getTranslationResource("MSGBOX07112")
                    If StrComp(strMessage, "MSGBOX07112") = 0 Then strMessage = "You have entered invalid data.(Value for Min Units/Max Units should not be more than 99999999#)"
                    RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
                    If dgPCDetail.Enabled Then
                        dgPCDetail.SetFocus
                    End If
                    Exit Function
                End If
            End If
        End If
    Next
    '////////////////////////
    
    ValidateCtls_Length = True
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_GetConstraintIDList)", Err.Description
     f_HandleErr , , , "AIM_ProductionConstraints::f_GetConstraintIDList", Now, gDRGeneralError, True, Err
    Resume Next
End Function

