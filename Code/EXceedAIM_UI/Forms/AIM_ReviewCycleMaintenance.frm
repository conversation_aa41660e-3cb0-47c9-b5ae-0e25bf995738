VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ReviewCycleMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Review Cycle Maintenance"
   ClientHeight    =   5370
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11730
   Icon            =   "AIM_ReviewCycleMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   5370
   ScaleWidth      =   11730
   ShowInTaskbar   =   0   'False
   Begin VB.Frame fmSchedule 
      Caption         =   "Schedule"
      Height          =   1875
      Left            =   3525
      TabIndex        =   26
      Top             =   1455
      Width           =   8100
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Saturday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   6
         Left            =   5850
         TabIndex        =   18
         Top             =   1380
         Width           =   2175
      End
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Friday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   5
         Left            =   5850
         TabIndex        =   17
         Top             =   1005
         Width           =   2175
      End
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Thursday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   4
         Left            =   5835
         TabIndex        =   16
         Top             =   630
         Width           =   2175
      End
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Wednesday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   3
         Left            =   5820
         TabIndex        =   15
         Top             =   255
         Width           =   2175
      End
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Tuesday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   2
         Left            =   3600
         TabIndex        =   14
         Top             =   1005
         Width           =   2175
      End
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Monday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   1
         Left            =   3600
         TabIndex        =   13
         Top             =   630
         Width           =   2175
      End
      Begin VB.CheckBox ckRevDay 
         Caption         =   "Sunday"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Index           =   0
         Left            =   3600
         TabIndex        =   12
         Top             =   255
         Width           =   2175
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcWeekQualifier 
         Height          =   345
         Left            =   2750
         TabIndex        =   22
         Top             =   240
         Width           =   765
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648384
         Columns(0).Width=   3200
         _ExtentX        =   1349
         _ExtentY        =   609
         _StockProps     =   93
         Text            =   "0"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 0"
      End
      Begin TDBNumber6Ctl.TDBNumber txtRevInterval 
         Height          =   345
         Left            =   2700
         TabIndex        =   11
         Top             =   233
         Width           =   765
         _Version        =   65536
         _ExtentX        =   1349
         _ExtentY        =   609
         Calculator      =   "AIM_ReviewCycleMaintenance.frx":030A
         Caption         =   "AIM_ReviewCycleMaintenance.frx":032A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":0396
         Keys            =   "AIM_ReviewCycleMaintenance.frx":03B4
         Spin            =   "AIM_ReviewCycleMaintenance.frx":03FE
         AlignHorizontal =   1
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   365
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011758597
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label lblRevInterval 
         Caption         =   "Review Cycle Interval"
         Height          =   300
         Left            =   200
         TabIndex        =   34
         Top             =   285
         Width           =   2450
      End
      Begin VB.Label lblWeekQualifier 
         Caption         =   "Week Qualifier"
         Height          =   300
         Left            =   200
         TabIndex        =   35
         Top             =   285
         Width           =   2450
      End
   End
   Begin VB.Frame Frame1 
      Height          =   1400
      Left            =   60
      TabIndex        =   23
      Top             =   0
      Width           =   11565
      Begin VB.CheckBox ckDynamicFlag 
         Caption         =   "Dynamic Review"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   8055
         TabIndex        =   3
         Top             =   562
         Value           =   1  'Checked
         Width           =   1890
      End
      Begin VB.CheckBox ckRevStatus 
         Caption         =   "Enabled"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   8055
         TabIndex        =   1
         Top             =   202
         Value           =   1  'Checked
         Width           =   1890
      End
      Begin TDBDate6Ctl.TDBDate txtNextDateTime 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   10005
         TabIndex        =   6
         Top             =   900
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calendar        =   "AIM_ReviewCycleMaintenance.frx":0426
         Caption         =   "AIM_ReviewCycleMaintenance.frx":053E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":05AA
         Keys            =   "AIM_ReviewCycleMaintenance.frx":05C8
         Spin            =   "AIM_ReviewCycleMaintenance.frx":0626
         AlignHorizontal =   0
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483639
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/2002"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37539
         CenturyMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtRevCycleDesc 
         Height          =   345
         Left            =   2745
         TabIndex        =   2
         Top             =   540
         Width           =   4965
         _Version        =   65536
         _ExtentX        =   8758
         _ExtentY        =   609
         Caption         =   "AIM_ReviewCycleMaintenance.frx":064E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":06BA
         Key             =   "AIM_ReviewCycleMaintenance.frx":06D8
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtRevCycle 
         Height          =   345
         Left            =   2750
         TabIndex        =   0
         Top             =   180
         Width           =   1380
         _Version        =   65536
         _ExtentX        =   2434
         _ExtentY        =   600
         Caption         =   "AIM_ReviewCycleMaintenance.frx":071C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":0788
         Key             =   "AIM_ReviewCycleMaintenance.frx":07A6
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   8
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBDate6Ctl.TDBDate txtRevStartDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   2750
         TabIndex        =   4
         Top             =   900
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calendar        =   "AIM_ReviewCycleMaintenance.frx":07EA
         Caption         =   "AIM_ReviewCycleMaintenance.frx":0902
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":096E
         Keys            =   "AIM_ReviewCycleMaintenance.frx":098C
         Spin            =   "AIM_ReviewCycleMaintenance.frx":09EA
         AlignHorizontal =   0
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/1999"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   36443
         CenturyMode     =   0
      End
      Begin TDBDate6Ctl.TDBDate txtRevEndDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   6240
         TabIndex        =   5
         Top             =   900
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calendar        =   "AIM_ReviewCycleMaintenance.frx":0A12
         Caption         =   "AIM_ReviewCycleMaintenance.frx":0B2A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":0B96
         Keys            =   "AIM_ReviewCycleMaintenance.frx":0BB4
         Spin            =   "AIM_ReviewCycleMaintenance.frx":0C12
         AlignHorizontal =   0
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/1999"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   36443
         CenturyMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "End Date"
         Height          =   300
         Index           =   3
         Left            =   4620
         TabIndex        =   30
         Top             =   945
         Width           =   1575
      End
      Begin VB.Label Label 
         Caption         =   "Start Date"
         Height          =   300
         Index           =   2
         Left            =   200
         TabIndex        =   29
         Top             =   945
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Next Review Date"
         Height          =   300
         Index           =   4
         Left            =   8055
         TabIndex        =   27
         Top             =   945
         Width           =   1890
      End
      Begin VB.Label Label 
         Caption         =   "Description"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   25
         Top             =   585
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Review Cycle Name"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   24
         Top             =   225
         Width           =   2450
      End
   End
   Begin VB.Frame Frame4 
      Caption         =   "Frequency"
      Height          =   1875
      Left            =   60
      TabIndex        =   28
      Top             =   1455
      Width           =   3390
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Week of Month"
         Height          =   300
         Index           =   4
         Left            =   120
         TabIndex        =   10
         Top             =   1380
         Width           =   3015
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Monthly"
         Height          =   300
         Index           =   3
         Left            =   120
         TabIndex        =   9
         Top             =   1005
         Width           =   3015
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Weekly"
         Height          =   300
         Index           =   2
         Left            =   120
         TabIndex        =   8
         Top             =   630
         Width           =   3015
      End
      Begin VB.OptionButton optRevFreq 
         Caption         =   "Daily"
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   7
         Top             =   255
         Value           =   -1  'True
         Width           =   3015
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbReviewCycle 
      Left            =   120
      Top             =   5040
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_ReviewCycleMaintenance.frx":0C3A
      ToolBars        =   "AIM_ReviewCycleMaintenance.frx":9838
   End
   Begin VB.Frame fmSeasonal 
      Caption         =   "Seasonal"
      Height          =   1530
      Left            =   60
      TabIndex        =   31
      Top             =   3405
      Width           =   11565
      Begin VB.CheckBox optSeasonalReview 
         Caption         =   "Seasonal Review"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   19
         Top             =   210
         Width           =   2450
      End
      Begin TDBNumber6Ctl.TDBNumber txtInitBuyPct 
         Height          =   345
         Left            =   2750
         TabIndex        =   21
         Top             =   900
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calculator      =   "AIM_ReviewCycleMaintenance.frx":9B51
         Caption         =   "AIM_ReviewCycleMaintenance.frx":9B71
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":9BDD
         Keys            =   "AIM_ReviewCycleMaintenance.frx":9BFB
         Spin            =   "AIM_ReviewCycleMaintenance.frx":9C45
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "0.00;-0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   5
         Value           =   0
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin TDBDate6Ctl.TDBDate txtInitRevDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   2750
         TabIndex        =   20
         ToolTipText     =   "s"
         Top             =   540
         Width           =   1470
         _Version        =   65536
         _ExtentX        =   2593
         _ExtentY        =   609
         Calendar        =   "AIM_ReviewCycleMaintenance.frx":9C6D
         Caption         =   "AIM_ReviewCycleMaintenance.frx":9D85
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReviewCycleMaintenance.frx":9DF1
         Keys            =   "AIM_ReviewCycleMaintenance.frx":9E0F
         Spin            =   "AIM_ReviewCycleMaintenance.frx":9E6D
         AlignHorizontal =   2
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/2002"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37539
         CenturyMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Initial Review Date"
         Height          =   300
         Index           =   5
         Left            =   200
         TabIndex        =   33
         Top             =   585
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Initial Buy Percent"
         Height          =   300
         Index           =   6
         Left            =   200
         TabIndex        =   32
         Top             =   945
         Width           =   2450
      End
   End
End
Attribute VB_Name = "AIM_ReviewCycleMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_RevCycles_GetEq_Sp As ADODB.Command
Dim AIM_RevCycles_GetKey_Sp As ADODB.Command

Dim rsRevCycle As ADODB.Recordset

Dim RevCycle As String

Dim RevCycleBookMark As Variant

Const EndDate As Date = "12/31/9999"

Private Function Refresh_Form()
On Error GoTo ErrorHandler

    Dim i As Integer

    'Enable/Disable Key fields
    If rsRevCycle.EditMode = adEditAdd Then
        If gAccessLvl <> 1 Then tbReviewCycle.Tools("ID_Save").Enabled = True
        Me.tbReviewCycle.Tools("ID_Delete").Enabled = False
        Me.tbReviewCycle.Tools("ID_AddNew").Enabled = False
        Me.txtRevCycle.Enabled = True
        Me.tbReviewCycle.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")
    Else
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            Me.tbReviewCycle.Tools("ID_Save").Enabled = False
            Me.tbReviewCycle.Tools("ID_Delete").Enabled = False
            Me.tbReviewCycle.Tools("ID_AddNew").Enabled = False
        Else
            Me.tbReviewCycle.Tools("ID_Save").Enabled = True
            Me.tbReviewCycle.Tools("ID_Delete").Enabled = True
            Me.tbReviewCycle.Tools("ID_AddNew").Enabled = True
        End If

        Me.txtRevCycle.Enabled = False
        Me.tbReviewCycle.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Edit")
    End If
    
    If f_IsRecordsetOpenAndPopulated(rsRevCycle) Then
        'Bind to the Review Cycle Binding Collection
        Me.txtRevCycle.Text = rsRevCycle("revcycle").Value
        Me.txtRevCycleDesc.Text = rsRevCycle("revcycledesc").Value
        Me.ckRevStatus.Value = rsRevCycle("revstatus").Value
        Me.ckDynamicFlag.Value = rsRevCycle("DynamicFlag").Value
        Me.ckRevStatus.Value = rsRevCycle("revstatus").Value
        Me.optRevFreq(rsRevCycle("revfreq").Value).Value = True
        Me.txtRevInterval.Value = rsRevCycle("revinterval").Value
        Me.ckRevDay(0).Value = rsRevCycle("revsunday").Value
        Me.ckRevDay(1).Value = rsRevCycle("revmonday").Value
        Me.ckRevDay(2).Value = rsRevCycle("revtuesday").Value
        Me.ckRevDay(3).Value = rsRevCycle("revwednesday").Value
        Me.ckRevDay(4).Value = rsRevCycle("revthursday").Value
        Me.ckRevDay(5).Value = rsRevCycle("revfriday").Value
        Me.ckRevDay(6).Value = rsRevCycle("revsaturday").Value
        Me.dcWeekQualifier.Value = rsRevCycle("weekqualifier").Value
        Me.txtRevEndDate.Number = Format(rsRevCycle("revenddate").Value, AIM_DATEVALUEFORMAT)
        Me.txtInitBuyPct.Value = rsRevCycle("initbuypct").Value
        Me.txtInitRevDate.Number = Format(rsRevCycle("initrevdate").Value, AIM_DATEVALUEFORMAT)
        Me.optSeasonalReview.Value = rsRevCycle("seasonalreview").Value
        SetTDBDate Me.txtNextDateTime, rsRevCycle!NextDateTime
        SetTDBDate Me.txtRevStartDate, rsRevCycle!RevStartDate
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(Refresh_Form)", Err.Description
    f_HandleErr , , , "AIM_Reports::Refresh_Form", Now, gDRGeneralError, True, Err
End Function

Private Function f_RevCycle_InitializeNew()
On Error GoTo ErrorHandler

    'Initialize add record
    rsRevCycle.AddNew
    
    rsRevCycle("revcycle").Value = getTranslationResource("New")               'Review Cycle Name
    rsRevCycle("revcycledesc").Value = ""           'Review Cycle Description
    rsRevCycle("nextdatetime").Value = Date         'Review Cycle Next Date
    rsRevCycle("revstatus").Value = 1               'Review Cycle Enabled
    rsRevCycle("revfreq").Value = 1                 'Daily Review
    rsRevCycle("revinterval").Value = 1             'One Period
    rsRevCycle("revsunday").Value = 1               'Sunday checkbox
    rsRevCycle("revmonday").Value = 0               'Monday checkbox
    rsRevCycle("revtuesday").Value = 0              'Tuesday checkbox
    rsRevCycle("revwednesday").Value = 0            'Wednesday checkbox
    rsRevCycle("revthursday").Value = 0             'Thursday checkbox
    rsRevCycle("revfriday").Value = 0               'Friday checkbox
    rsRevCycle("revsaturday").Value = 0             'Saturday checkbox
    rsRevCycle("weekqualifier").Value = 1           'Week Qualifier
    rsRevCycle("revstartdate").Value = Date         'Day value of Start Date
    rsRevCycle("revenddate").Value = EndDate        'Day value of End Date
    rsRevCycle("initbuypct").Value = 1              'Initial Buy Percentage
    rsRevCycle("initrevdate").Value = Date          'Initial Review Date
    rsRevCycle("seasonalreview").Value = 0          'Seasonal Reviw
    
    rsRevCycle("ReviewTime").Value = 1
    rsRevCycle("OrdGenStatus").Value = ""
    
    'Enable/Disable Key fields
    Me.txtRevCycle.Enabled = True
    Me.tbReviewCycle.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")

    Me.tbReviewCycle.Tools("ID_Delete").Enabled = False
    Me.tbReviewCycle.Tools("ID_AddNew").Enabled = False
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_RevCycle_InitializeNew)", Err.Description
    f_HandleErr , , , "AIM_Reports::f_RevCycle_InitializeNew", Now, gDRGeneralError, True, Err
End Function

Private Function f_RevCycle_Save()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    
    Cn.Errors.Clear
    
    'Validate required info
    RtnCode = UserInputValidation
    If RtnCode = SUCCEED Then
        'Save
        rsRevCycle.Update
        
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, Cn.Errors.Count - 1
        Else
            strMessage = getTranslationResource("STATMSG04601")
            If StrComp(strMessage, "STATMSG04601") = 0 Then strMessage = "Review Cycle successfully updated. "
            Write_Message strMessage
        End If
        
    Else
        f_RevCycle_Save = RtnCode
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_RevCycle_Save)", Err.Description
    f_HandleErr , , , "AIM_Reports::f_RevCycle_Save", Now, gDRGeneralError, True, Err
End Function

Private Function f_RevCycle_Update()
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim RevCycle As String
    Dim RevCycleDesc As String
    Dim NextDateTime As Date
    Dim RevStatus As Integer
    Dim DynamicFlag As Integer
    Dim RevFreq As Integer
    Dim RevInterval As Integer
    Dim RevSunday As Integer
    Dim RevMonday As Integer
    Dim RevTuesday As Integer
    Dim RevWednesday As Integer
    Dim RevThursday As Integer
    Dim RevFriday As Integer
    Dim RevSaturday As Integer
    Dim WeekQualifier As Integer
    Dim RevStartDate As Date
    Dim RevEndDate As Date
    Dim SeasonalReview As Integer
    Dim InitBuyPct As Double
    Dim InitRevDate As Date
    Dim ReviewTime As Integer
    
    RevCycle = Me.txtRevCycle.Text
    RevCycleDesc = Me.txtRevCycleDesc.Text
    NextDateTime = Me.txtNextDateTime.Text
    RevStatus = Me.ckRevStatus
    DynamicFlag = Me.ckDynamicFlag
    
    For i = Me.optRevFreq.LBound To Me.optRevFreq.UBound
        
        If Me.optRevFreq(i).Value = True Then
            RevFreq = i
            Exit For
        End If
    
    Next i
    
    RevInterval = Me.txtRevInterval.Value
    
    'If the Review Interval is > 1; Only one day can be selected
    If RevInterval > 1 Then
        'Find the first check day
        For i = 0 To Me.ckRevDay.Count - 1
            If Me.ckRevDay(i) = vbChecked Then
                Exit For
            End If
        
        Next i
        
        'Turn off any others
        For i = i + 1 To Me.ckRevDay.Count - 1
            Me.ckRevDay(i) = vbUnchecked
        Next i
    
    End If
    
    RevSunday = Me.ckRevDay(0)
    RevMonday = Me.ckRevDay(1)
    RevTuesday = Me.ckRevDay(2)
    RevWednesday = Me.ckRevDay(3)
    RevThursday = Me.ckRevDay(4)
    RevFriday = Me.ckRevDay(5)
    RevSaturday = Me.ckRevDay(6)
    WeekQualifier = Me.dcWeekQualifier.Value
    RevStartDate = Me.txtRevStartDate.Text
    RevEndDate = Me.txtRevEndDate.Text
    SeasonalReview = Me.optSeasonalReview.Value
    InitBuyPct = Me.txtInitBuyPct.Value
    InitRevDate = Me.txtInitRevDate.Text
    
    'Clean Up
    Select Case RevFreq
    Case 0, 1, 3, 5
        RevSunday = 0
        RevMonday = 0
        RevTuesday = 0
        RevWednesday = 0
        RevThursday = 0
        RevFriday = 0
        RevSaturday = 0
        WeekQualifier = 0
    
    Case 2
        WeekQualifier = 0
    
    End Select
    
    'Determine the Review Time in Days
    ReviewTime = CalcReviewTime(RevFreq, RevInterval, RevSunday, RevMonday, RevTuesday, _
        RevWednesday, RevThursday, RevFriday, RevSaturday)
    
    'Get the Next Review Date for this Cycle
    'NextDateTime = GetNextRevDate(Date - 1, Date - 1, RevFreq, RevInterval, RevSunday, RevMonday, _
        RevTuesday, RevWednesday, RevThursday, RevFriday, RevSaturday, WeekQualifier, _
        RevStartDate, RevEndDate, InitBuyPct, InitRevDate, ReviewTime)
        
    'Update the Text box
    SetTDBDate Me.txtNextDateTime, NextDateTime
        
    rsRevCycle("revcycle").Value = RevCycle
    rsRevCycle("revcycledesc").Value = RevCycleDesc
    rsRevCycle("nextdatetime").Value = NextDateTime
    rsRevCycle("revstatus").Value = RevStatus
    rsRevCycle("DynamicFlag").Value = DynamicFlag
    rsRevCycle("revfreq").Value = RevFreq
    rsRevCycle("revinterval").Value = RevInterval
    rsRevCycle("revsunday").Value = RevSunday
    rsRevCycle("revmonday").Value = RevMonday
    rsRevCycle("revtuesday").Value = RevTuesday
    rsRevCycle("revwednesday").Value = RevWednesday
    rsRevCycle("revthursday").Value = RevThursday
    rsRevCycle("revfriday").Value = RevFriday
    rsRevCycle("revsaturday").Value = RevSaturday
    rsRevCycle("weekqualifier").Value = WeekQualifier
    rsRevCycle("revstartdate").Value = RevStartDate
    rsRevCycle("revenddate").Value = RevEndDate
    rsRevCycle("seasonalreview").Value = SeasonalReview
    rsRevCycle("initbuypct").Value = InitBuyPct
    rsRevCycle("initrevdate").Value = InitRevDate
    rsRevCycle("reviewtime").Value = ReviewTime

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_RevCycle_Update)", Err.Description
    f_HandleErr , , , "AIM_Reports::f_RevCycle_Update", Now, gDRGeneralError, True, Err
End Function

Private Function UserInputValidation() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX07312")
    If StrComp(strMessage, "MSGBOX07312") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim(txtRevCycle.Text) = "" _
    Or StrComp(txtRevCycle.Text, getTranslationResource("New")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    Label(0).Caption  'Promotion ID
        txtRevCycle.SetFocus
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
    f_HandleErr , , , "AIM_Reports::UserInputValidation", Now, gDRGeneralError, True, Err
End Function

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_Reports::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
            
    Dim RtnCode As Integer
    Dim strMessage As String
    Dim rsWeekQualifier As New ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As New ADODB.Command
        
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG04600")
    If StrComp(strMessage, "STATMSG04600") = 0 Then strMessage = "Initializing Review Cycle Maintenenace... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
        
    If RtnCode <> SUCCEED Then Exit Sub
    'Initialize Stored Procedures
    Set AIM_RevCycles_GetEq_Sp = New ADODB.Command
    With AIM_RevCycles_GetEq_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RevCycles_GetEq_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_RevCycles_GetKey_Sp = New ADODB.Command
    With AIM_RevCycles_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RevCycles_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the Revcycle Record Set
    Set rsRevCycle = New ADODB.Recordset
    With rsRevCycle
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Build/Bind the dcWeeklyQualifier Drop Down
     With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_WEEKOFMONTH
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    With rsWeekQualifier
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsWeekQualifier.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsWeekQualifier) Then
        Do Until rsWeekQualifier.eof
            Me.dcWeekQualifier.AddItem rsWeekQualifier("CodeId").Value + vbTab + rsWeekQualifier("CodeDesc").Value
            rsWeekQualifier.MoveNext
        Loop

    End If
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsWeekQualifier) Then rsWeekQualifier.Close
    Set rsWeekQualifier = Nothing
    
    'Open the Result Set
    RevCycle = ""
    
    RtnCode = RevCycles_GetKey(AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetFirst)
    RtnCode = RevCycles_GetEq(AIM_RevCycles_GetEq_Sp, rsRevCycle, RevCycle)
    
    If f_IsRecordsetOpenAndPopulated(rsRevCycle) Then
        Me.tbReviewCycle.ToolBars("tbReviewCycle").Tools("Id_AddItem").ChangeAll ssChangeAllName, _
                        getTranslationResource("Edit")
    Else
        f_RevCycle_InitializeNew
    End If
    
    'Sync. input boxes with recordset
    Refresh_Form
    
    'Set the visibility of input factors
    optRevFreq_Click (0)
    
    '************************************************************
    'Mar 01 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtInitBuyPct.Spin.Visible = 1
    Me.txtRevInterval.Spin.Visible = 1
    Me.txtInitRevDate.DropDown.Visible = 1
    Me.txtNextDateTime.DropDown.Visible = 1
    Me.txtRevEndDate.DropDown.Visible = 1
    Me.txtRevStartDate.DropDown.Visible = 1
    '************************************************************
   
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsWeekQualifier) Then rsWeekQualifier.Close
    Set rsWeekQualifier = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_Reports::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsRevCycle) Then rsRevCycle.Close
    Set rsRevCycle = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_Reports::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub dcWeekQualifier_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcWeekQualifier.Columns(0).Caption = getTranslationResource("Code")
    Me.dcWeekQualifier.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcWeekQualifier.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcWeekQualifier.Columns(0).Width = 720
    
    Me.dcWeekQualifier.Columns(1).Caption = getTranslationResource("Description")
    Me.dcWeekQualifier.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcWeekQualifier.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcWeekQualifier.Columns(1).Width = 2000
    
'    Me.dcWeekQualifier.AddItem "1" + vbTab + getTranslationResource("First Week")
'    Me.dcWeekQualifier.AddItem "2" + vbTab + getTranslationResource("Second Week")
'    Me.dcWeekQualifier.AddItem "3" + vbTab + getTranslationResource("Third Week")
'    Me.dcWeekQualifier.AddItem "4" + vbTab + getTranslationResource("Fourth Week")
'    Me.dcWeekQualifier.AddItem "5" + vbTab + getTranslationResource("Last Week")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcWeekQualifier, ACW_EXPAND
    End If
        
'    For IndexCounter = 0 To dcWeekQualifier.Columns.Count - 1
'        dcWeekQualifier.Columns(IndexCounter).HasHeadBackColor = True
'        dcWeekQualifier.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcWeekQualifier_InitColumnProps)"
     f_HandleErr , , , "AIM_Reports::dcWeekQualifier_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub optRevFreq_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    
    If Me.optRevFreq(1).Value = True Then   'Daily

        Me.lblRevInterval.Visible = True
        Me.lblWeekQualifier.Visible = False
        Me.txtRevInterval.Visible = True
        Me.dcWeekQualifier.Visible = False
        For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
            Me.ckRevDay(i).Visible = False
        Next i
        'Me.fmSeasonal.Visible = False

    ElseIf Me.optRevFreq(2).Value = True Then    'Weekly

        Me.lblRevInterval.Visible = True
        Me.lblWeekQualifier.Visible = False
        Me.txtRevInterval.Visible = True
        Me.dcWeekQualifier.Visible = False
        For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
            Me.ckRevDay(i).Visible = True
        Next i
        'Me.fmSeasonal.Visible = False

    ElseIf Me.optRevFreq(3).Value = True Then   'Monthly

        Me.lblRevInterval.Visible = True
        Me.lblWeekQualifier.Visible = False
        Me.txtRevInterval.Visible = True
        Me.dcWeekQualifier.Visible = False
        For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
            Me.ckRevDay(i).Visible = False
        Next i
        'Me.fmSeasonal.Visible = False

    ElseIf Me.optRevFreq(4).Value = True Then   'Week of Month

        Me.lblRevInterval.Visible = False
        Me.lblWeekQualifier.Visible = True
        Me.txtRevInterval.Visible = False
        Me.dcWeekQualifier.Visible = True
        For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
            Me.ckRevDay(i).Visible = True
        Next i
        'Me.fmSeasonal.Visible = False

    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optRevFreq_Click)"
     f_HandleErr , , , "AIM_Reports::optRevFreq_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbReviewCycle_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String

    'Clear Errors
    Cn.Errors.Clear
    
    Write_Message ""
    
    RtnCode = f_RevCycle_Update           'Update the recordset
    
    'Alert user to possible change
    Select Case Tool.ID
    Case "ID_AddNew", "ID_Close", "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
        "ID_GoToBookMark", "ID_LookUp"
        
        If gAccessLvl <> 1 And DataChanged(rsRevCycle, adAffectCurrent) > 0 Then
            strMessage = getTranslationResource("MSGBOX04603")
            If StrComp(strMessage, "MSGBOX04603") = 0 Then strMessage = "Abandon changes to current record ? "
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
            If RtnCode = vbYes Then
                rsRevCycle.CancelUpdate
            Else
                f_RevCycle_Save
            End If
        Else
            rsRevCycle.CancelUpdate
        End If
    
    End Select
    
    'Navigate
    Select Case Tool.ID
    
    Case "ID_AddNew"
        f_RevCycle_InitializeNew
    
    Case "ID_Delete"
        strMessage = getTranslationResource("MSGBOX04604")
        If StrComp(strMessage, "MSGBOX04604") = 0 Then strMessage = "Delete the current Review Cycle?"
        RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, Me.Caption)
        
        If RtnCode = vbYes Then
            rsRevCycle.Delete
            If RevCycles_GetKey(AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_Getlt) <> SUCCEED Then
                RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetFirst
            End If
            
        End If

    Case "ID_GetFirst"
        RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetFirst
    
    Case "ID_GetPrev"
        RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_Getlt
        
    Case "ID_GetNext"
        RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetGT
        
    Case "ID_GetLast"
        RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetLast
    
    Case "ID_Save"
        f_RevCycle_Save
        
    Case "ID_SetBookMark"
        RevCycleBookMark = RevCycle
        Me.tbReviewCycle.Tools("ID_GoToBookMark").Enabled = True
        
    Case "ID_GoToBookMark"
        RevCycle = RevCycleBookMark
        RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetEq
    
    Case "ID_LookUp"
        AIM_ReviewCycleLookUp.RevCycleKey = Me.txtRevCycle.Text
        Set AIM_ReviewCycleLookUp.Cn = Cn
        AIM_ReviewCycleLookUp.Show vbModal
        
        If Not AIM_ReviewCycleLookUp.CancelFlag Then
            RevCycle = AIM_ReviewCycleLookUp.RevCycleKey
            RevCycles_GetKey AIM_RevCycles_GetKey_Sp, RevCycle, rsRevCycle, SQL_GetEq
            
        End If
        
    Case "ID_Close"
        Unload Me
        Exit Sub

    End Select

    Refresh_Form        'Refresh the form
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbReviewCycle_ToolClick)"
     f_HandleErr , , , "AIM_Reports::tbReviewCycle_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtRevInterval_Change()
On Error GoTo ErrorHandler

'    Dim i As Integer
'    Dim GroupSelect As Integer
'
'    If Me.optRevFreq(2).Value = ValueTrue Then
'
'        If Me.txtRevInterval.Value = 1 _
'            And Me.ckRevDay(0).GroupSelect <> AnyCombination Then
'
'            For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
'                Me.ckRevDay(i).GroupSelect = AnyCombination
'            Next i
'
'        ElseIf Me.txtRevInterval.Value <> 1 _
'            And Me.ckRevDay(0).GroupSelect <> OneOnlyOne Then
'
'            For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
'                Me.ckRevDay(i).Value = ValueFalse
'            Next i
'
'            Me.ckRevDay(1).Value = ValueTrue
'
'            For i = Me.ckRevDay.LBound To Me.ckRevDay.UBound
'                Me.ckRevDay(i).GroupSelect = OneOnlyOne
'            Next i
'
'        End If
'
'    End If
'
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtRevInterval_Change)"
    f_HandleErr , , , "AIM_Reports::txtRevInterval_Change", Now, gDRGeneralError, True, Err
End Sub
