VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Begin VB.Form AIM_HistoryAdjReason 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "Reason for adjusting Item History"
   ClientHeight    =   3195
   ClientLeft      =   2760
   ClientTop       =   3750
   ClientWidth     =   6030
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3195
   ScaleWidth      =   6030
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin TDBText6Ctl.TDBText txtReason 
      Height          =   975
      Left            =   120
      TabIndex        =   0
      Top             =   2160
      Width           =   4455
      _Version        =   65536
      _ExtentX        =   7858
      _ExtentY        =   1720
      Caption         =   "AIM_HistoryAdjReason.frx":0000
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_HistoryAdjReason.frx":006C
      Key             =   "AIM_HistoryAdjReason.frx":008A
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   1
      MarginRight     =   1
      MarginTop       =   1
      MarginBottom    =   1
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   2
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   255
      LengthAsByte    =   0
      Text            =   "<Type your comments here and then click OK.>"
      Furigana        =   0
      HighlightText   =   -1
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   1
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin VB.CommandButton CancelButton 
      Caption         =   "Cancel"
      Height          =   375
      Left            =   4680
      TabIndex        =   2
      Top             =   2760
      Width           =   1215
   End
   Begin VB.CommandButton OKButton 
      Caption         =   "OK"
      Default         =   -1  'True
      Height          =   375
      Left            =   4680
      TabIndex        =   1
      Top             =   2160
      Width           =   1215
   End
   Begin VB.Label lblMessage 
      Caption         =   "Message: "
      Height          =   1815
      Left            =   120
      TabIndex        =   3
      Top             =   120
      Width           =   5775
   End
End
Attribute VB_Name = "AIM_HistoryAdjReason"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False

Option Explicit

Public AcceptValue As Boolean
Public ReasonText As String

Public Property Let ShowMessage(MsgText As String)
On Error GoTo ErrorHandler

    lblMessage.Caption = MsgText
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(ShowMessage [Property Let])"
     f_HandleErr , , , "AIM_HistoryAdjReason::howMessage [Property Let]", Now, gDRGeneralError, True, Err

End Property

Private Sub Form_Load()
On Error GoTo ErrorHandler

    AcceptValue = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_HistoryAdjReason::Form_Load", Now, gDRGeneralError, True, Err
     
End Sub

Private Sub CancelButton_Click()
On Error GoTo ErrorHandler

    AcceptValue = False
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(CancelButton_Click)"
     f_HandleErr , , , "AIM_HistoryAdjReason::CancelButton_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub OKButton_Click()
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    ReasonText = Me.txtReason.Text
    If StrComp(ReasonText, "<Type your comments here and then click OK.>") = 0 Then
        strMessage = getTranslationResource("STATMSG08202")
        If StrComp(strMessage, "STATMSG08202") = 0 Then strMessage = "A reason for changing the data is required.  Please try again."
        MsgBox strMessage
        Exit Sub
    Else
        AcceptValue = True
        Unload Me
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(OKButton_Click)"
     f_HandleErr , , , "AIM_HistoryAdjReason::OKButton_Click", Now, gDRGeneralError, True, Err
End Sub

