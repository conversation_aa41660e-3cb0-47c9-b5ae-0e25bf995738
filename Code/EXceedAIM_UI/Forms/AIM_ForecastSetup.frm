VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ForecastSetup 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Forecast Setup"
   ClientHeight    =   7230
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10350
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   7230
   ScaleWidth      =   10350
   Begin VB.Frame fraForecastInfo 
      Height          =   2175
      Left            =   123
      TabIndex        =   0
      Top             =   0
      Width           =   10095
      Begin VB.CheckBox ckFcstEnabled 
         Caption         =   "Enabled"
         Height          =   340
         Left            =   5160
         TabIndex        =   10
         Top             =   1685
         Value           =   1  'Checked
         Width           =   2535
      End
      Begin VB.Frame fraHierarchy 
         Caption         =   "Forecast Hierarchy"
         Height          =   900
         Left            =   120
         TabIndex        =   6
         Top             =   1125
         Width           =   4875
         Begin VB.OptionButton optFcstHierarchy 
            Caption         =   "Sandbox"
            Height          =   340
            Index           =   1
            Left            =   120
            TabIndex        =   7
            Top             =   285
            Value           =   -1  'True
            Width           =   2115
         End
         Begin VB.OptionButton optFcstHierarchy 
            Caption         =   "Live"
            Height          =   340
            Index           =   2
            Left            =   2400
            TabIndex        =   8
            Top             =   270
            Width           =   2115
         End
      End
      Begin VB.CheckBox ckFcstLocked 
         Caption         =   "Forecast Locked"
         CausesValidation=   0   'False
         Enabled         =   0   'False
         Height          =   340
         Left            =   5160
         TabIndex        =   9
         TabStop         =   0   'False
         Top             =   1245
         Width           =   2535
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcForecastID 
         Bindings        =   "AIM_ForecastSetup.frx":0000
         Height          =   345
         Left            =   2520
         TabIndex        =   3
         Top             =   240
         Width           =   2115
         DataFieldList   =   "FcstID"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3731
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "FcstID"
      End
      Begin TDBText6Ctl.TDBText txtFcstId 
         Height          =   345
         Left            =   2505
         TabIndex        =   2
         Top             =   240
         Visible         =   0   'False
         Width           =   2115
         _Version        =   65536
         _ExtentX        =   3731
         _ExtentY        =   609
         Caption         =   "AIM_ForecastSetup.frx":0021
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastSetup.frx":008D
         Key             =   "AIM_ForecastSetup.frx":00AB
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtFcstDesc 
         Height          =   345
         Left            =   2520
         TabIndex        =   5
         Top             =   675
         Width           =   6465
         _Version        =   65536
         _ExtentX        =   11404
         _ExtentY        =   609
         Caption         =   "AIM_ForecastSetup.frx":00EF
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastSetup.frx":015B
         Key             =   "AIM_ForecastSetup.frx":0179
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   40
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label lblForecastInfo 
         Caption         =   "Description"
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   4
         Top             =   720
         Width           =   2235
      End
      Begin VB.Label lblForecastInfo 
         Caption         =   "Forecast ID"
         Height          =   300
         Index           =   0
         Left            =   120
         TabIndex        =   1
         Top             =   285
         Width           =   2235
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   180
      Top             =   6750
      _ExtentX        =   582
      _ExtentY        =   582
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   14
      Style           =   0
      Tools           =   "AIM_ForecastSetup.frx":01BD
      ToolBars        =   "AIM_ForecastSetup.frx":B3DF
   End
   Begin ActiveTabs.SSActiveTabs atForecastSetup 
      Height          =   4485
      Left            =   90
      TabIndex        =   11
      Top             =   2280
      Width           =   10095
      _ExtentX        =   17806
      _ExtentY        =   7911
      _Version        =   131083
      TabCount        =   4
      Tabs            =   "AIM_ForecastSetup.frx":B6A3
      Begin ActiveTabs.SSActiveTabPanel atpAccessControl 
         Height          =   4095
         Left            =   -99969
         TabIndex        =   12
         Top             =   360
         Width           =   10035
         _ExtentX        =   17701
         _ExtentY        =   7223
         _Version        =   131083
         TabGuid         =   "AIM_ForecastSetup.frx":B7AB
         Begin VB.Frame fraAccessControl 
            Height          =   3450
            Left            =   120
            TabIndex        =   13
            Top             =   120
            Width           =   9735
            Begin VB.Frame fraAddPermissions 
               Caption         =   "Edit Permissions"
               Height          =   2850
               Left            =   5490
               TabIndex        =   15
               Top             =   300
               Width           =   4155
               Begin VB.CheckBox ckAccessCodes 
                  Caption         =   "Full Control"
                  Height          =   255
                  Index           =   0
                  Left            =   120
                  TabIndex        =   21
                  Top             =   675
                  Width           =   3255
               End
               Begin VB.CheckBox ckAccessCodes 
                  Caption         =   "Modify forecast setup"
                  Height          =   255
                  Index           =   1
                  Left            =   120
                  TabIndex        =   20
                  Top             =   1020
                  Width           =   3255
               End
               Begin VB.CheckBox ckAccessCodes 
                  Caption         =   "Modify demand plan"
                  Height          =   255
                  Index           =   2
                  Left            =   120
                  TabIndex        =   19
                  Top             =   1365
                  Width           =   3255
               End
               Begin VB.CheckBox ckAccessCodes 
                  Caption         =   "Read"
                  Height          =   255
                  Index           =   3
                  Left            =   120
                  TabIndex        =   18
                  Top             =   1710
                  Width           =   3255
               End
               Begin VB.CheckBox ckAccessCodes 
                  Caption         =   "Control access"
                  Height          =   255
                  Index           =   4
                  Left            =   120
                  TabIndex        =   17
                  Top             =   2055
                  Width           =   3255
               End
               Begin VB.CheckBox ckAccessCodes 
                  Caption         =   "Promote to master demand plan"
                  Height          =   255
                  Index           =   5
                  Left            =   120
                  TabIndex        =   16
                  Top             =   2400
                  Width           =   3255
               End
               Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
                  Height          =   345
                  Left            =   2160
                  TabIndex        =   22
                  Top             =   240
                  Width           =   1875
                  DataFieldList   =   "Column 1"
                  AllowNull       =   0   'False
                  _Version        =   196617
                  DataMode        =   2
                  Cols            =   2
                  BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  ForeColorEven   =   0
                  BackColorOdd    =   12648447
                  RowHeight       =   423
                  Columns(0).Width=   3200
                  _ExtentX        =   3307
                  _ExtentY        =   609
                  _StockProps     =   93
                  ForeColor       =   -2147483640
                  BackColor       =   -2147483643
                  DataFieldToDisplay=   "Column 0"
               End
               Begin VB.Label lblEditCrit 
                  Caption         =   "User ID"
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   23
                  Top             =   285
                  Width           =   1905
               End
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAccessControl 
               Height          =   3075
               Left            =   120
               TabIndex        =   14
               Top             =   240
               Width           =   5295
               _Version        =   196617
               DataMode        =   2
               Col.Count       =   0
               AllowUpdate     =   0   'False
               AllowColumnSwapping=   0
               UseExactRowCount=   0   'False
               SelectTypeRow   =   1
               MaxSelectedRows =   1
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               _ExtentX        =   9340
               _ExtentY        =   5424
               _StockProps     =   79
            End
         End
         Begin ActiveToolBars.SSActiveToolBars tbAccessControl 
            Left            =   240
            Top             =   3720
            _ExtentX        =   582
            _ExtentY        =   582
            _Version        =   131083
            ToolBarsCount   =   1
            ToolsCount      =   12
            Style           =   0
            Tools           =   "AIM_ForecastSetup.frx":B7D3
            ToolBars        =   "AIM_ForecastSetup.frx":1504F
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpPeriods 
         Height          =   4095
         Left            =   30
         TabIndex        =   24
         Top             =   360
         Width           =   10035
         _ExtentX        =   17701
         _ExtentY        =   7223
         _Version        =   131083
         TabGuid         =   "AIM_ForecastSetup.frx":151E2
         Begin VB.Frame fraPeriods 
            Height          =   3870
            Left            =   120
            TabIndex        =   25
            Top             =   120
            Width           =   9780
            Begin VB.Frame fraRollingFcst 
               Caption         =   "Rolling Forecast"
               Height          =   2085
               Left            =   120
               TabIndex        =   41
               Top             =   1590
               Width           =   4815
               Begin VB.CheckBox ckFcstHistory 
                  Caption         =   "Retain historical periods"
                  Height          =   345
                  Left            =   90
                  TabIndex        =   44
                  Top             =   1620
                  Visible         =   0   'False
                  Width           =   3975
               End
               Begin VB.CheckBox ckFcstRolling 
                  Caption         =   "Rolling forecast updates"
                  Height          =   340
                  Left            =   120
                  TabIndex        =   43
                  Top             =   285
                  Value           =   1  'Checked
                  Width           =   3975
               End
               Begin TDBNumber6Ctl.TDBNumber txtFcstPeriods_Future 
                  Height          =   345
                  Left            =   3045
                  TabIndex        =   42
                  Top             =   660
                  Width           =   1110
                  _Version        =   65536
                  _ExtentX        =   1958
                  _ExtentY        =   609
                  Calculator      =   "AIM_ForecastSetup.frx":1520A
                  Caption         =   "AIM_ForecastSetup.frx":1522A
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_ForecastSetup.frx":15296
                  Keys            =   "AIM_ForecastSetup.frx":152B4
                  Spin            =   "AIM_ForecastSetup.frx":152FE
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  Appearance      =   1
                  BackColor       =   -2147483643
                  BorderStyle     =   1
                  BtnPositioning  =   1
                  ClipMode        =   0
                  ClearAction     =   0
                  DecimalPoint    =   "."
                  DisplayFormat   =   "##0;-##0;0;0"
                  EditMode        =   0
                  Enabled         =   -1
                  ErrorBeep       =   0
                  ForeColor       =   -2147483640
                  Format          =   "##0"
                  HighlightText   =   0
                  MarginBottom    =   3
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MaxValue        =   52
                  MinValue        =   1
                  MousePointer    =   0
                  MoveOnLRKey     =   0
                  NegativeColor   =   255
                  OLEDragMode     =   0
                  OLEDropMode     =   0
                  ReadOnly        =   0
                  Separator       =   ""
                  ShowContextMenu =   -1
                  ValueVT         =   2088828933
                  Value           =   12
                  MaxValueVT      =   5
                  MinValueVT      =   5
               End
               Begin TDBNumber6Ctl.TDBNumber txtFcstPeriods_Historical 
                  Height          =   345
                  Left            =   3045
                  TabIndex        =   45
                  Top             =   1080
                  Width           =   1110
                  _Version        =   65536
                  _ExtentX        =   1958
                  _ExtentY        =   609
                  Calculator      =   "AIM_ForecastSetup.frx":15326
                  Caption         =   "AIM_ForecastSetup.frx":15346
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_ForecastSetup.frx":153B2
                  Keys            =   "AIM_ForecastSetup.frx":153D0
                  Spin            =   "AIM_ForecastSetup.frx":1541A
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  Appearance      =   1
                  BackColor       =   -2147483643
                  BorderStyle     =   1
                  BtnPositioning  =   1
                  ClipMode        =   0
                  ClearAction     =   0
                  DecimalPoint    =   "."
                  DisplayFormat   =   "##0;-##0;0;0"
                  EditMode        =   0
                  Enabled         =   -1
                  ErrorBeep       =   0
                  ForeColor       =   -2147483640
                  Format          =   "##0"
                  HighlightText   =   0
                  MarginBottom    =   3
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MaxValue        =   52
                  MinValue        =   0
                  MousePointer    =   0
                  MoveOnLRKey     =   0
                  NegativeColor   =   255
                  OLEDragMode     =   0
                  OLEDropMode     =   0
                  ReadOnly        =   0
                  Separator       =   ""
                  ShowContextMenu =   -1
                  ValueVT         =   2088828933
                  Value           =   2
                  MaxValueVT      =   5
                  MinValueVT      =   5
               End
               Begin VB.Label lblFcstPeriods_Future 
                  Caption         =   "Future Periods"
                  Height          =   300
                  Left            =   645
                  TabIndex        =   47
                  Top             =   731
                  Width           =   2235
               End
               Begin VB.Label lblFcstPeriods_Historical 
                  Caption         =   "Historical Periods"
                  Height          =   300
                  Left            =   645
                  TabIndex        =   46
                  Top             =   1125
                  Width           =   2235
               End
            End
            Begin VB.Frame fraForecastInterval 
               Caption         =   "Forecast Interval"
               Height          =   1850
               Left            =   5100
               TabIndex        =   31
               Top             =   240
               Width           =   4500
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-Week"
                  Height          =   340
                  Index           =   7
                  Left            =   2650
                  TabIndex        =   39
                  Top             =   1395
                  Width           =   1635
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Quarters"
                  Height          =   340
                  Index           =   3
                  Left            =   120
                  TabIndex        =   38
                  Top             =   1380
                  Width           =   2235
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-4-5"
                  Height          =   340
                  Index           =   6
                  Left            =   2650
                  TabIndex        =   37
                  Top             =   1020
                  Width           =   1635
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "4-5-4"
                  Height          =   340
                  Index           =   5
                  Left            =   2650
                  TabIndex        =   36
                  Top             =   645
                  Width           =   1635
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "5-4-4"
                  Height          =   340
                  Index           =   4
                  Left            =   2650
                  TabIndex        =   35
                  Top             =   270
                  Width           =   1635
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Months"
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   34
                  Top             =   1020
                  Width           =   2235
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Weeks"
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   33
                  Top             =   645
                  Value           =   -1  'True
                  Width           =   2235
               End
               Begin VB.OptionButton optFcstInterval 
                  Caption         =   "Days"
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   32
                  Top             =   270
                  Width           =   2235
               End
            End
            Begin VB.Frame fraFixed 
               Caption         =   "Fixed Forecast"
               Height          =   1155
               Left            =   120
               TabIndex        =   26
               Top             =   240
               Width           =   4815
               Begin TDBNumber6Ctl.TDBNumber txtFixedPeriods 
                  Height          =   345
                  Left            =   3045
                  TabIndex        =   27
                  Top             =   660
                  Width           =   1110
                  _Version        =   65536
                  _ExtentX        =   1958
                  _ExtentY        =   609
                  Calculator      =   "AIM_ForecastSetup.frx":15442
                  Caption         =   "AIM_ForecastSetup.frx":15462
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_ForecastSetup.frx":154CE
                  Keys            =   "AIM_ForecastSetup.frx":154EC
                  Spin            =   "AIM_ForecastSetup.frx":15536
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  Appearance      =   1
                  BackColor       =   -2147483643
                  BorderStyle     =   1
                  BtnPositioning  =   1
                  ClipMode        =   0
                  ClearAction     =   0
                  DecimalPoint    =   "."
                  DisplayFormat   =   "##0;-##0;0;0"
                  EditMode        =   0
                  Enabled         =   -1
                  ErrorBeep       =   0
                  ForeColor       =   -2147483640
                  Format          =   "##0"
                  HighlightText   =   0
                  MarginBottom    =   3
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MaxValue        =   104
                  MinValue        =   0
                  MousePointer    =   0
                  MoveOnLRKey     =   0
                  NegativeColor   =   255
                  OLEDragMode     =   0
                  OLEDropMode     =   0
                  ReadOnly        =   0
                  Separator       =   ""
                  ShowContextMenu =   -1
                  ValueVT         =   1179653
                  Value           =   12
                  MaxValueVT      =   5
                  MinValueVT      =   5
               End
               Begin TDBDate6Ctl.TDBDate txtFcstStartDate 
                  BeginProperty DataFormat 
                     Type            =   1
                     Format          =   "M/d/yyyy"
                     HaveTrueFalseNull=   0
                     FirstDayOfWeek  =   0
                     FirstWeekOfYear =   0
                     LCID            =   1033
                     SubFormatType   =   3
                  EndProperty
                  Height          =   345
                  Left            =   3045
                  TabIndex        =   28
                  Top             =   240
                  Width           =   1710
                  _Version        =   65536
                  _ExtentX        =   3016
                  _ExtentY        =   609
                  Calendar        =   "AIM_ForecastSetup.frx":1555E
                  Caption         =   "AIM_ForecastSetup.frx":15676
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Verdana"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_ForecastSetup.frx":156E2
                  Keys            =   "AIM_ForecastSetup.frx":15700
                  Spin            =   "AIM_ForecastSetup.frx":1575E
                  AlignHorizontal =   0
                  AlignVertical   =   0
                  Appearance      =   1
                  BackColor       =   -2147483643
                  BorderStyle     =   1
                  BtnPositioning  =   1
                  ClipMode        =   0
                  CursorPosition  =   0
                  DataProperty    =   0
                  DisplayFormat   =   "mm/dd/yyyy"
                  EditMode        =   0
                  Enabled         =   -1
                  ErrorBeep       =   0
                  FirstMonth      =   4
                  ForeColor       =   -2147483640
                  Format          =   "mm/dd/yyyy"
                  HighlightText   =   0
                  IMEMode         =   3
                  MarginBottom    =   3
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MaxDate         =   2958465
                  MinDate         =   -657434
                  MousePointer    =   0
                  MoveOnLRKey     =   0
                  OLEDragMode     =   0
                  OLEDropMode     =   0
                  PromptChar      =   "_"
                  ReadOnly        =   0
                  ShowContextMenu =   -1
                  ShowLiterals    =   0
                  TabAction       =   0
                  Text            =   "10/15/2001"
                  ValidateMode    =   0
                  ValueVT         =   7
                  Value           =   37179
                  CenturyMode     =   0
               End
               Begin VB.Label lblFcstStartDate 
                  Caption         =   "Start Date"
                  Height          =   300
                  Left            =   120
                  TabIndex        =   30
                  Top             =   285
                  Width           =   2595
               End
               Begin VB.Label lblFixedPeriods 
                  Caption         =   "Fixed Periods"
                  Height          =   300
                  Left            =   120
                  TabIndex        =   29
                  Top             =   705
                  Width           =   2595
               End
            End
            Begin TDBNumber6Ctl.TDBNumber txtFreezePeriods 
               Height          =   345
               Left            =   7500
               TabIndex        =   40
               Top             =   2220
               Width           =   1110
               _Version        =   65536
               _ExtentX        =   1958
               _ExtentY        =   609
               Calculator      =   "AIM_ForecastSetup.frx":15786
               Caption         =   "AIM_ForecastSetup.frx":157A6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Verdana"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastSetup.frx":15812
               Keys            =   "AIM_ForecastSetup.frx":15830
               Spin            =   "AIM_ForecastSetup.frx":1587A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label lblFreezePeriods 
               Caption         =   "Freeze Periods"
               Height          =   300
               Left            =   5100
               TabIndex        =   48
               Top             =   2250
               Width           =   2235
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpOptions 
         Height          =   4095
         Left            =   -99969
         TabIndex        =   49
         Top             =   360
         Width           =   10035
         _ExtentX        =   17701
         _ExtentY        =   7223
         _Version        =   131083
         TabGuid         =   "AIM_ForecastSetup.frx":158A2
         Begin VB.Frame fraDataElements 
            Caption         =   "Data Elements"
            Height          =   3870
            Left            =   120
            TabIndex        =   50
            Top             =   120
            Width           =   9780
            Begin VB.Frame fraUnits 
               Caption         =   "Forecast Unit"
               Height          =   1575
               Left            =   4920
               TabIndex        =   63
               Top             =   1765
               Width           =   4515
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Weight"
                  Height          =   300
                  HelpContextID   =   3
                  Index           =   3
                  Left            =   2400
                  TabIndex        =   68
                  Top             =   270
                  Width           =   1935
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Price"
                  Height          =   300
                  Index           =   2
                  Left            =   120
                  TabIndex        =   67
                  Top             =   1080
                  Width           =   1935
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Cost"
                  Height          =   300
                  Index           =   1
                  Left            =   120
                  TabIndex        =   66
                  Top             =   675
                  Width           =   1935
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Units"
                  Height          =   300
                  Index           =   0
                  Left            =   120
                  TabIndex        =   65
                  Top             =   270
                  Value           =   -1  'True
                  Width           =   1935
               End
               Begin VB.OptionButton optFcstUnit 
                  Caption         =   "Cube"
                  Height          =   300
                  Index           =   4
                  Left            =   2400
                  TabIndex        =   64
                  Top             =   675
                  Width           =   1935
               End
            End
            Begin VB.Frame fraApplyTrend 
               Caption         =   "Apply Trend"
               Height          =   1140
               Left            =   4920
               TabIndex        =   59
               Top             =   285
               Width           =   4515
               Begin VB.OptionButton optApplyTrend 
                  Caption         =   "By Item"
                  Height          =   340
                  Index           =   1
                  Left            =   2370
                  TabIndex        =   62
                  Top             =   270
                  Value           =   -1  'True
                  Width           =   2000
               End
               Begin VB.OptionButton optApplyTrend 
                  Caption         =   "No"
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   61
                  Top             =   645
                  Width           =   2000
               End
               Begin VB.OptionButton optApplyTrend 
                  Caption         =   "Yes"
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   60
                  Top             =   270
                  Width           =   2000
               End
            End
            Begin VB.CheckBox ckCalc_FcstAdj 
               Caption         =   "Fcst Adj"
               Enabled         =   0   'False
               Height          =   340
               Left            =   120
               TabIndex        =   58
               Top             =   1059
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_ProjInv 
               Caption         =   "Projected Inventory"
               Enabled         =   0   'False
               Height          =   340
               Left            =   120
               TabIndex        =   57
               Top             =   2220
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_NetReq 
               Caption         =   "Net Req"
               Enabled         =   0   'False
               Height          =   340
               Left            =   120
               TabIndex        =   56
               Top             =   1446
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_AdjNetReq 
               Caption         =   "AdjNetReq"
               Height          =   340
               Left            =   120
               TabIndex        =   55
               Top             =   1833
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_HistDmd 
               Caption         =   "Historical demand"
               Height          =   340
               Left            =   120
               TabIndex        =   54
               Top             =   2607
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_SysFcst 
               Caption         =   "Sys Fcst"
               Height          =   340
               Left            =   120
               TabIndex        =   53
               Top             =   285
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_MasterFcstAdj 
               Caption         =   "Master Fcst Adj"
               Height          =   340
               Left            =   120
               TabIndex        =   52
               Top             =   672
               Width           =   4575
            End
            Begin VB.CheckBox ckCalc_ProdConst 
               Caption         =   "Production Constraint"
               Height          =   340
               Left            =   120
               TabIndex        =   51
               Top             =   3000
               Width           =   4575
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpMultiFilters 
         Height          =   4095
         Left            =   -99969
         TabIndex        =   69
         Top             =   360
         Width           =   10035
         _ExtentX        =   17701
         _ExtentY        =   7223
         _Version        =   131083
         TabGuid         =   "AIM_ForecastSetup.frx":158CA
         Begin VB.Frame fraMultiFilters 
            Height          =   3870
            Left            =   120
            TabIndex        =   70
            Top             =   120
            Width           =   9780
            Begin TDBText6Ctl.TDBText txtSelected 
               Height          =   2655
               Left            =   120
               TabIndex        =   71
               TabStop         =   0   'False
               Top             =   600
               Width           =   4695
               _Version        =   65536
               _ExtentX        =   8281
               _ExtentY        =   4683
               Caption         =   "AIM_ForecastSetup.frx":158F2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastSetup.frx":1595E
               Key             =   "AIM_ForecastSetup.frx":1597C
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   -1
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtNewFilters 
               Height          =   2655
               Left            =   4920
               TabIndex        =   72
               TabStop         =   0   'False
               Top             =   600
               Width           =   3735
               _Version        =   65536
               _ExtentX        =   6588
               _ExtentY        =   4683
               Caption         =   "AIM_ForecastSetup.frx":159C0
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_ForecastSetup.frx":15A2C
               Key             =   "AIM_ForecastSetup.frx":15A4A
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   -1
               ShowContextMenu =   -1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MarginBottom    =   1
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   0
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   -1
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label2 
               Caption         =   "New filters (not included in original):"
               Height          =   300
               Left            =   4920
               TabIndex        =   74
               Top             =   240
               Width           =   3735
            End
            Begin VB.Label Label1 
               Caption         =   "All item filters (original + new):"
               Height          =   300
               Left            =   120
               TabIndex        =   73
               Top             =   240
               Width           =   3975
            End
         End
      End
   End
End
Attribute VB_Name = "AIM_ForecastSetup"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2004 SSA Global. All rights reserved.
'*****************************************************************************
'
'   AIM_ForecastSetup.frm
'
'   Version Number - 1.0
'   Last Updated   - 2004/01/01
'   Updated By     - Annalakshmi Stocksdale
'
'   This replaces the former AIM_ForecastMaintenance screen in allowing definitions of forecast criteria.
'
'   The "forecast generator/modification" process is being morphed
'   into demand planning, hence the options required for forecast generator
'   are being phased out, and additional options for demand planning
'   are to be created, as of version 4.5
'   See related updates to AIM_ForecastModification.
'
'*****************************************************************************
' This file contains trade secrets of SSA Global. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of SSA Global.
'*****************************************************************************
Option Explicit
Option Base 1
'*****************************************************************************
'   Modular Variables
'*****************************************************************************
Dim Cn As ADODB.Connection

Dim m_FcstId As String
Dim m_FcstSetupKey As Long

Dim m_NewFcst As Boolean
Dim m_InitialLoad As Boolean
Dim m_FcstIdBookMark As String

Dim m_ColIdx_FcstSetupKey As Long
Dim m_ColIdx_UserID As Long
Dim m_ColIdx_UserName As Long
Dim m_ColIdx_FunctionCtrlNbr As Long
Dim m_ColIdx_FuncDesc As Long
Dim m_ColIdx_AccessCode() As Integer

Dim m_xaOrigCriteria As XArrayDB
Dim m_xaAllCriteria As XArrayDB
Dim m_xaNewCriteria As XArrayDB

Private Const NEWFCST As String = "New Fcst"
Private Const NEWFCSTDESC As String = "New Fcst Description"
Dim AIM_DmdPlanFcstSetup_Save_Sp As New ADODB.Command

Private Sub ckFcstRolling_Click()
If optFcstHierarchy(1).Value = True Then ' Sandbox is selected
    If ckFcstRolling.Value = 0 Then  'Rollingforecast is un selected
        lblFcstPeriods_Future.Enabled = False
        lblFcstPeriods_Historical.Enabled = False
        txtFcstPeriods_Future.Enabled = False
        txtFcstPeriods_Historical.Enabled = False
        txtFixedPeriods.Enabled = True
        lblFixedPeriods.Enabled = True
        txtFcstStartDate.Enabled = True
        lblFcstStartDate.Enabled = True
        fraFixed.ForeColor = RGB(0, 0, 0)
        fraRollingFcst.ForeColor = RGB(128, 128, 128)
    Else
        lblFcstPeriods_Future.Enabled = True
        lblFcstPeriods_Historical.Enabled = True
        txtFcstPeriods_Future.Enabled = True
        txtFcstPeriods_Historical.Enabled = True
        txtFixedPeriods.Enabled = False
        lblFixedPeriods.Enabled = False
        txtFcstStartDate.Enabled = False
        lblFcstStartDate.Enabled = False
        fraFixed.ForeColor = RGB(128, 128, 128)
        fraRollingFcst.ForeColor = RGB(0, 0, 0)
    End If
Else
    lblFcstPeriods_Future.Enabled = True
    lblFcstPeriods_Historical.Enabled = True
    txtFcstPeriods_Future.Enabled = True
    txtFcstPeriods_Historical.Enabled = True
End If
End Sub

'*****************************************************************************
'   EVENTS
'*****************************************************************************
Private Sub Form_Activate()
On Error GoTo ErrorHandler
    
    Screen.MousePointer = vbHourglass
    
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
        
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
    f_HandleErr , , , "AIM_ForecastSetup::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String

    Screen.MousePointer = vbHourglass
    m_InitialLoad = True
    GetTranslatedCaptions Me
    m_FcstId = ""
    strMessage = getTranslationResource("STATMSG07300")
    If StrComp(strMessage, "STATMSG07300") = 0 Then strMessage = "Initializing Forecast Setup..."
    Write_Message strMessage
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    With AIM_DmdPlanFcstSetup_Save_Sp
        Set .ActiveConnection = Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DmdPlanFcstSetup_Save_Sp"

        'Set parameters and their values
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
    '-- Conditions
        .Parameters.Append .CreateParameter("@UserID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@UpdateForRolling", adBoolean, adParamInput)
    '-- Fields
        .Parameters.Append .CreateParameter("@FcstID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@FcstDesc", adVarWChar, adParamInput, 40)
        .Parameters.Append .CreateParameter("@FcstHierarchy", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@FcstEnabled", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@FcstLocked", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@FcstStartDate", adDate, adParamInput)
        .Parameters.Append .CreateParameter("@LastUpdated", adDate, adParamInput)
        .Parameters.Append .CreateParameter("@FcstRolling", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@FcstHistory", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@FcstPds_Future", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@FcstPds_Historical", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@FreezePds", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@FixedPds", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@FcstInterval", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@FcstUnit", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@ApplyTrend", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_SysFcst", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_NetRqmt", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_ProjInv", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_HistDmnd", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_MasterFcstAdj", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_FcstAdj", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_AdjNetReq", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter("@Calc_ProdConst", adBoolean, adParamInput)
    '-- Item filter criteria
        .Parameters.Append .CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append .CreateParameter("@ItStat", adVarWChar, adParamInput, 1)
        .Parameters.Append .CreateParameter("@VnId", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@Assort", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@ById", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@Class1", adVarWChar, adParamInput, 50)
        .Parameters.Append .CreateParameter("@Class2", adVarWChar, adParamInput, 50)
        .Parameters.Append .CreateParameter("@Class3", adVarWChar, adParamInput, 50)
        .Parameters.Append .CreateParameter("@Class4", adVarWChar, adParamInput, 50)
        .Parameters.Append .CreateParameter("@LcId", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@LStatus", adVarWChar, adParamInput, 1)
        .Parameters.Append .CreateParameter("@LDivision", adVarWChar, adParamInput, 20)
        .Parameters.Append .CreateParameter("@LRegion", adVarWChar, adParamInput, 20)
        .Parameters.Append .CreateParameter("@LUserDefined", adVarWChar, adParamInput, 30)
    '-- Return parameters
        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInputOutput)
    End With
    
    
    'A.Stocksdale - For item-filter-multi-select
    g_InitConstants
    g_InitCriteria m_xaOrigCriteria
    'A.Stocksdale - End
    
    'Load first record/open new record
    If GetForecastList = False Then InitializeNewRecord
    'Fetch codelookups
    RtnCode = m_FetchUserAccessCodes
    
    'Add to Windows List
    AddToWindowList Me.Caption
    'Make the spin button visible
    Me.txtFcstPeriods_Future.Spin.Visible = 1
    Me.txtFcstPeriods_Historical.Spin.Visible = 1
    Me.txtFcstStartDate.DropDown.Visible = 1
    'Make the grid read-only
    Me.dgAccessControl.AllowUpdate = False
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    m_InitialLoad = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
    f_HandleErr , , , "AIM_ForecastSetup::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    
'>>>
    'Save pending updates, first
    '...
    
    'Destroy objects
'    If f_IsRecordsetValidAndOpen(m_rsUserAccess) Then m_rsUserAccess.Close
'    Set m_rsUserAccess = Nothing
     If Not (AIM_DmdPlanFcstSetup_Save_Sp Is Nothing) Then Set AIM_DmdPlanFcstSetup_Save_Sp.ActiveConnection = Nothing
     Set AIM_DmdPlanFcstSetup_Save_Sp = Nothing
    'Destroy connection and object
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message COMP_EMPTY
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
                 f_HandleErr , , , "AIM_ForecastSetup::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub atForecastSetup_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    
    Select Case UCase$(NewTab.Key)
    Case "PERIODS"
        mToggleToolbar True
        
    Case "ACCESSCONTROL"
        mAccessControl_Rearrange (False)
        RtnCode = mAccessControl_Fetch
        mToggleToolbar False
    
    Case Else
        mToggleToolbar False
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atForecastSetup_BeforeTabClick)"
         f_HandleErr , , , "AIM_ForecastSetup::atForecastSetup_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub ckAccessCodes_Click(Index As Integer)
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Integer
    
    With ckAccessCodes
        If Index = 0 Then
        'Reset all accesscodes to match the first one
            For IndexCounter = .LBound To .UBound
                .Item(IndexCounter).Value = .Item(0).Value
            Next
        End If
        
        If .Item(1).Value = vbUnchecked _
        And .Item(2).Value = vbUnchecked _
        And .Item(3).Value = vbUnchecked _
        And .Item(4).Value = vbUnchecked _
        And .Item(5).Value = vbUnchecked _
        Then
            .Item(0).Value = vbUnchecked
        End If
    End With
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckAccessCodes_Click)"
     f_HandleErr , , , "AIM_ForecastSetup::ckAccessCodes_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim AIM_AIMUser_List_Sp As ADODB.Command, rsAIMUsers As ADODB.Recordset
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    Dim IndexCounter As Integer, RowCount As Integer
    Dim EBUsers As SSOleDBCombo
    
    Set EBUsers = Me.dcAIMUsers
    
    Set AIM_AIMUser_List_Sp = New ADODB.Command
    With AIM_AIMUser_List_Sp
        .CommandType = adCmdStoredProc
        Set .ActiveConnection = Cn
        .CommandText = "AIM_AIMUser_List_Sp"
        'Set parameters and their values
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
    End With

    Set rsAIMUsers = New ADODB.Recordset
    With rsAIMUsers
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsAIMUsers.Open AIM_AIMUser_List_Sp
    
    With EBUsers
        If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
            .Redraw = False
            IndexCounter = 0
            .Columns(IndexCounter).Name = "UserID"
            .Columns(IndexCounter).Caption = getTranslationResource("User ID")
            .Columns(IndexCounter).Width = 3880
            .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
            .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
            IndexCounter = IndexCounter + 1
            .Columns(IndexCounter).Name = "User Name"
            .Columns(IndexCounter).Caption = getTranslationResource("User Name")
            .Columns(IndexCounter).Width = 3880
            .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
            .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
            If gGridAutoSizeOption Then
                'Added to Automatically adjust the column widths depending on the Columns Label translation size.
                AdjustColumnWidths dcAIMUsers, ACW_EXPAND
            End If
            'Populate with data
            rsAIMUsers.MoveFirst
            RowCount = rsAIMUsers.RecordCount
            ReDim m_ColIdx_AccessCode(RowCount)
            Do While Not rsAIMUsers.eof
                .AddItem CStr(rsAIMUsers!UserID) & vbTab & CStr(rsAIMUsers!UserName)
                rsAIMUsers.MoveNext
            Loop
            rsAIMUsers.MoveFirst
            .Text = rsAIMUsers!UserID
            .Redraw = True
        End If
    End With 'EBUsers
    
    Set EBUsers = Nothing
    If Not (AIM_AIMUser_List_Sp Is Nothing) Then Set AIM_AIMUser_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMUser_List_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
        
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    If Err.Number = 3219 Then
        Resume Next
    Else
        EBUsers.Redraw = True
        Set EBUsers = Nothing
        If Not (AIM_AIMUser_List_Sp Is Nothing) Then Set AIM_AIMUser_List_Sp.ActiveConnection = Nothing
        Set AIM_AIMUser_List_Sp = Nothing
        If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
        Set rsAIMUsers = Nothing
        Err.Number = ErrNumber
        Err.source = ErrSource
        Err.Description = ErrDescription
        'f_HandleErr Me.Caption & "(dcAIMUsers)"
        f_HandleErr , , , "AIM_ForecastSetup::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub dcForecastID_Change()
On Error GoTo ErrorHandler
    
    Dim FetchSuccess As Boolean
    Dim lngRecordIndex As Long
    
    'Check for updates, first
'    If Not m_InitialLoad Then f_AIMForecast_Commit True
    'Navigate to the selected forecast ID
    If StrComp(m_FcstId, dcForecastID.Text, vbTextCompare) <> 0 Then
        m_FcstId = dcForecastID.Text
        FetchSuccess = FetchForecastSetup(SQL_GetEq, True)
    End If
 
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcForecastID_Change)"
     f_HandleErr , , , "AIM_ForecastSetup::dcForecastID_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcForecastID_Click()
On Error GoTo ErrorHandler
'same as dcForecastID_Change
    
    dcForecastID_Change
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcForecastID_Click)"
     f_HandleErr , , , "AIM_ForecastSetup::dcForecastID_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcForecastID_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim EBForecast As SSOleDBCombo
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    Set EBForecast = Me.dcForecastID
    With EBForecast
        .Redraw = True
        IndexCounter = 0
        .Columns(IndexCounter).Caption = getTranslationResource("Forecast ID")
        .Columns(IndexCounter).Width = 1500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "FcstID"
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Caption = getTranslationResource("Description")
        .Columns(IndexCounter).Width = 2880
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "FcstDesc"
    End With
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBForecast, ACW_EXPAND
    End If
    EBForecast.Redraw = True
    Set EBForecast = Nothing

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    EBForecast.Redraw = True
    Set EBForecast = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    'f_HandleErr Me.Caption & "(dcForecastID_InitColumnProps)"
     f_HandleErr , , , "AIM_ForecastSetup::dcForecastID_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub



Private Sub dgAccessControl_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    mSet_Mode

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAccessControl_RowColChange)"
     f_HandleErr , , , "AIM_ForecastSetup::dgAccessControl_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub optFcstHierarchy_Click(Index As Integer)
On Error GoTo ErrorHandler

    If ckFcstLocked.Value = vbUnchecked Then FcstHierarchyChanged Index
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optFcstHierarchy_Click)"
        f_HandleErr , , , "AIM_ForecastSetup::optFcstHierarchy_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optFcstInterval_Click(Index As Integer)
On Error GoTo ErrorHandler

    txtFcstStartDate_Validate False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optFcstInterval_Click)"
    f_HandleErr , , , "AIM_ForecastSetup::GetRepositoryKey", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbAccessControl_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Dim UserID As String, CodeID As String
    Dim IndexCounter As Integer, RtnCode As Long
    
    Dim AccessCode As Integer, RowCount As Integer
    Dim BkMark As Variant
    Dim RowIdx As Integer, ColIdx As Integer
    Dim strMessage As String, strMessage1 As String
    Dim AccessCodeColsStart As Integer, AccessCodeColsEnd As Integer
    Dim SaveCode As Boolean
    Dim DG_ACtrl As SSOleDBGrid
    
    Screen.MousePointer = vbHourglass
    Set DG_ACtrl = dgAccessControl
    With DG_ACtrl
        .Redraw = False
        
        Select Case UCase$(Tool.ID)
        Case "ID_DELETE"
            RowCount = .SelBookmarks.Count
            If RowCount > 0 Then
    '        If gAccessLvl = g_ACCESS_CREATEMODIFY _
    '        And ValidateMainUserAccess = g_ACCESS_CREATEMODIFY _
    '        Then
                'Display a confirmation msgbox
                strMessage = getTranslationResource("MSGBOX07300")
                If StrComp(strMessage, "MSGBOX07300") = 0 Then strMessage = "Delete the "
                
                strMessage = strMessage & " " & CStr(dgAccessControl.SelBookmarks.Count) & " "
                strMessage1 = getTranslationResource("MSGBOX07301")
                If StrComp(strMessage1, "MSGBOX07301") = 0 Then strMessage1 = " selected Forecast Access records?"
                strMessage = strMessage & strMessage1
            
                RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
            
                Select Case RtnCode
                Case vbYes
                    'User chose to delete.
                    For RowIdx = 0 To RowCount - 1
                        BkMark = .SelBookmarks(RowIdx)
                        UserID = .Columns(m_ColIdx_UserID).CellValue(BkMark)
                        If StrComp(UCase$(UserID), "SA", vbTextCompare) <> 0 Then
                            RtnCode = mAccessControl_Delete(UserID)
                            If RtnCode = -2 Then
                                'error
                                strMessage = getTranslationResource("ERRMSG07303")
                                If StrComp(strMessage, "ERRMSG07303") = 0 Then strMessage = " Error occurred while deleting Access Control Record"
                                MsgBox strMessage, vbExclamation, Me.Caption
                            End If
                        Else
                            strMessage = getTranslationResource("MSGBOX07302")
                            If StrComp(strMessage, "MSGBOX07302") = 0 Then strMessage = "Permissions for SA cannot be deleted."
                            MsgBox strMessage, vbExclamation
                        End If
                    Next RowIdx
                Case vbNo
                    'User chose to Cancel
                End Select
    '        Else
    '            'Cancel the delete request
    '            strMessage = getTranslationResource("MSGBOX99999")
    '            If StrComp(strMessage, "MSGBOX99999") = 0 Then strMessage = "Insufficient access rights. Operation aborted."
    '            MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
            End If
            .SelBookmarks.RemoveAll
            RtnCode = mAccessControl_Fetch
            RtnCode = mAccessControl_Rearrange(False)
            
        Case "ID_ADDNEW"
            RtnCode = mSet_EditMode
            RtnCode = mAccessControl_Rearrange(True)
            Call txtFcstStartDate_Validate(False)
            Call ckFcstRolling_Click
        Case "ID_EDIT"
            RtnCode = mSet_Mode
            RtnCode = mAccessControl_Rearrange(True)
        
        Case "ID_SAVE"
            If Me.tbAccessControl.Tools("ID_EDIT").Enabled = False Then
                RtnCode = mAccessControl_Delete(Me.dcAIMUsers.Text)
            End If
            
            UserID = Me.dcAIMUsers.Text
            For IndexCounter = Me.ckAccessCodes.LBound To Me.ckAccessCodes.UBound
                SaveCode = IIf(Me.ckAccessCodes(IndexCounter).Value = vbChecked, True, False)
                RtnCode = mAccessControl_Update(UserID, IndexCounter, SaveCode)
                If RtnCode = -2 Then
                'error
                    strMessage = getTranslationResource("ERRMSG07302")
                    If StrComp(strMessage, "ERRMSG07302") = 0 Then strMessage = "Error occurred while saving Access Control Record"
                    MsgBox strMessage, vbQuestion, Me.Caption
                End If
            Next IndexCounter
            
            RtnCode = mAccessControl_Fetch()
            RtnCode = mAccessControl_Rearrange(False)
            
        Case "ID_CANCEL"
            If dgAccessControl.Rows > 0 Then BkMark = dgAccessControl.Bookmark
            'refresh
            RtnCode = mAccessControl_Fetch
            dgAccessControl.Bookmark = BkMark
            
            RtnCode = mAccessControl_Rearrange(False)
            
        End Select
    
        .Redraw = True
    End With
    Set DG_ACtrl = Nothing
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    DG_ACtrl.Redraw = True
    Set DG_ACtrl = Nothing
    'f_HandleErr Me.Caption & "(tbAccessControl_ToolClick)"
     f_HandleErr , , , "AIM_ForecastSetup::tbAccessControl_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim BoolReturn As Boolean
    Dim IndexCounter As Integer
    Dim CurrentFilter As String
    Dim strMessage As String
    
    Select Case UCase$(Tool.ID)
    Case UCase$("ID_Close")
'>>> Check for unsaved records
        Unload Me
    
    Case UCase$("ID_AddNew")
        RtnCode = InitializeNewRecord()
        txtFixedPeriods.Enabled = False
        lblFixedPeriods.Enabled = False
        txtFcstStartDate.Enabled = False
        lblFcstStartDate.Enabled = False
        fraFixed.ForeColor = RGB(128, 128, 128)
        fraRollingFcst.ForeColor = RGB(0, 0, 0)
        txtFcstStartDate_Validate True
        Exit Sub
        
    Case UCase$("ID_Delete")
    
        strMessage = getTranslationResource("MSGBOX07399")
                If StrComp(strMessage, "MSGBOX07399") = 0 Then strMessage = "Do you want to delete the current FcstId "
                RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
                If RtnCode = vbYes Then
                    BoolReturn = DeleteForecastSetup(dcForecastID.Text)
                    If BoolReturn = True Then
                        'success
                    Else
                        'failure
                        strMessage = getTranslationResource("ERRMSG07301")
                        If StrComp(strMessage, "ERRMSG07301") = 0 Then strMessage = "Error occurred while deleting Forecast ID"
                         MsgBox strMessage, vbQuestion, Me.Caption
                    End If
                Else
                    'VbNo
                End If
        
'>>> verify here

    Case UCase$("ID_GetFirst")
        RtnCode = FetchForecastSetup(SQL_GetFirst, True)
        dcForecastID.Text = m_FcstId
        
    Case UCase$("ID_GetPrev")
        RtnCode = FetchForecastSetup(SQL_Getlt, True)
        dcForecastID.Text = m_FcstId
        
    Case UCase$("ID_GetNext")
        RtnCode = FetchForecastSetup(SQL_GetGT, True)
        dcForecastID.Text = m_FcstId
        
    Case UCase$("ID_GetLast")
        dcForecastID.MoveLast
        RtnCode = FetchForecastSetup(SQL_GetLast, True)
        dcForecastID.Text = m_FcstId
        
    Case UCase$("ID_Save")
        RtnCode = SaveForecastSetup
        If RtnCode = 1 Then
            'success
        Else
            'failure
            strMessage = getTranslationResource("ERRMSG07300")
            If StrComp(strMessage, "ERRMSG07300") = 0 Then strMessage = "Error occurred while saving Forecast "
             MsgBox strMessage, vbQuestion, Me.Caption
        End If
        
    Case UCase$("ID_SetBookMark")
        If m_FcstIdBookMark <> "" Then
            m_FcstIdBookMark = ""
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
        Else
            m_FcstIdBookMark = m_FcstId
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        End If
    
    Case UCase$("ID_GoToBookMark")
        m_FcstId = m_FcstIdBookMark
        RtnCode = FetchForecastSetup(SQL_GetEq, True)
        dcForecastID.Text = m_FcstId
            
    Case UCase$("ID_FilterItems")
    '   'A.Stocksdale - BEGIN - Multi-filters for item.
        'ShowItemFilter     'Replaced with function DisplayFilters()
        CurrentFilter = Me.txtSelected.Text
        DisplayFilters CurrentFilter
    '   'A.Stocksdale - END - Multi-filters for item.
    
'    Case UCase$("ID_FreezeItems")
'        With AIM_FcstFreeze
'            .p_FcstSetupKey = m_FcstSetupKey
'            For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
'                .p_FcstIntervalCaption = IIf(optFcstInterval(IndexCounter).Value = True, optFcstInterval(IndexCounter).Caption, .p_FcstIntervalCaption)
'            Next
'            .Show vbModal
'        End With
'
'        Set AIM_FcstFreeze = Nothing
        
    End Select

    'Refresh
    Select Case Tool.ID
        Case "ID_GetFirst", "ID_GetPrev", _
            "ID_GetNext", "ID_GetLast", _
            "ID_GotoBookmark", "ID_Delete"
            'Anything that changes the current record
'            If f_IsRecordsetOpenAndPopulated(rsForecast) Then
'                Me.dcForecastID(IDX_dcForecastID_FCSTID).Text = rsForecast!FcstId
'                m_NewFcst = False
'                'Show add, delete and the dropdown. Hide the text box
'                Me.tbNavigation.Tools("ID_AddNew").Enabled = True
'                tbNavigation.Tools("ID_Delete").Enabled = True
'                dcForecastID(IDX_dcForecastID_FCSTID).Visible = True
'                txtFcstId.Visible = False
'                txtFcstId.Text = rsForecast!FcstId
'                f_AIMForecast_Refresh
'            Else
'                'Create a new record
'                m_NewFcst = True
'                'Show add, delete and the dropdown. Hide the text box
'                Me.tbNavigation.Tools("ID_AddNew").Enabled = False
'                tbNavigation.Tools("ID_Delete").Enabled = False
'                dcForecastID(IDX_dcForecastID_FCSTID).Visible = False
'                txtFcstId.Visible = True
'            End If
        Case Else
            
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_ForecastSetup::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtFcstId_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim SqlStmt As String
    Dim CheckFcstID As ADODB.Command
    Dim rsCheckFcstID As ADODB.Recordset
    Dim CmdParams As ADODB.Parameter
    Dim strMessage As String
    
    If Cn Is Nothing Then Exit Sub
    Write_Message ""
    
    SqlStmt = "SELECT FcstID " & vbCrLf
    SqlStmt = SqlStmt & " FROM AIMFcstSetup  " & vbCrLf
    SqlStmt = SqlStmt & " WHERE FcstID = ? " & vbCrLf
    
    Set CheckFcstID = New ADODB.Command
    With CheckFcstID
        Set .ActiveConnection = Cn
        .CommandType = adCmdText
        .CommandText = SqlStmt
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@FcstID", adVarWChar, adParamInput, 12)
        CmdParams.Value = Me.txtFcstId.Text
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        
        .Prepared = True
    End With
    
    Set rsCheckFcstID = New ADODB.Recordset
    With rsCheckFcstID
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        .Open CheckFcstID
    End With
    If f_IsRecordsetOpenAndPopulated(rsCheckFcstID) Then
        strMessage = getTranslationResource("ERRMSG07305")
        If StrComp(strMessage, "ERRMSG07305") = 0 Then strMessage = "Duplicate Forecast IDs are not allowed.  Please provide a unique identifier."
        MsgBox strMessage, vbExclamation, Me.Caption
        Cancel = True
    Else
        Cancel = False
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtFcstId_Validate)"
     f_HandleErr , , , "AIM_ForecastSetup::txtFcstId_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtFcstStartDate_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim FcstInterval As Long, IndexCounter As Long, PeriodIndex As Long
    Dim UserDate As Date, PeriodStart As Date, PeriodEnd As Date
    Dim strMessage As String
    
    If Cn Is Nothing Then Exit Sub
    Write_Message ""
    
    'Find the Start and End boundaries for the specified forecast period.
    'Using the date and forecast interval specified by the user...
    'UserDate = Me.txtFcstStartDate.Text
    UserDate = Format(Date, gDateFormat)
    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
        FcstInterval = IIf(Me.optFcstInterval(IndexCounter).Value = True, IndexCounter, FcstInterval)
    Next
    '...fetch boundary dates
    RtnCode = Date_Boundaries(UserDate, FcstInterval, PeriodStart, PeriodEnd, PeriodIndex)
    
    'Map to AIM calendar
    If PeriodStart <> UserDate Then
        Me.txtFcstStartDate.Text = Format(PeriodStart, gDateFormat)
    
        strMessage = getTranslationResource("STATMSG07301")
        If StrComp(strMessage, "STATMSG07301") = 0 Then strMessage = "The specified date has been mapped to the interval's start date, as dictated by the AIM Calendar."
        strMessage = Me.lblFcstStartDate.Caption & " " & strMessage
        Write_Message strMessage
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtFcstStartDate_Validate)"
     f_HandleErr , , , "AIM_ForecastSetup::txtFcstStartDate_Validate", Now, gDRGeneralError, True, Err
End Sub
Private Function Date_Boundaries( _
    p_TargetDate As Date, _
    p_FcstInterval As Long, _
    r_PeriodStartDate As Date, _
    r_PeriodEndDate As Date, _
    r_PeriodIndex As Long _
) As Long
On Error GoTo ErrorHandler

    Dim AIM_AIMCalendar_BoundaryDates_Sp As ADODB.Command
    Dim RtnCode As Long

    Set AIM_AIMCalendar_BoundaryDates_Sp = New ADODB.Command
    With AIM_AIMCalendar_BoundaryDates_Sp
        Set .ActiveConnection = Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMCalendar_BoundaryDates_Sp"
    'Declare parameters
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append .CreateParameter("@TargetDate", adDBDate, adParamInput)
        .Parameters.Append .CreateParameter("@PeriodInterval", adTinyInt, adParamInput)
    'Output
        .Parameters.Append .CreateParameter("@PeriodStartDate", adDBDate, adParamOutput)
        .Parameters.Append .CreateParameter("@PeriodEndDate", adDBDate, adParamOutput)
        .Parameters.Append .CreateParameter("@PeriodIndex", adInteger, adParamOutput)
    'Set values
        .Parameters("@TargetDate").Value = p_TargetDate
        .Parameters("@PeriodInterval").Value = p_FcstInterval
    'Fetch results
        .Execute
    End With

    RtnCode = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(0).Value
    'Check return code -- 0 = fail; 1 = succeed
    Select Case RtnCode
    Case 1  'Success
        r_PeriodStartDate = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(3).Value
        r_PeriodEndDate = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(4).Value
        r_PeriodIndex = AIM_AIMCalendar_BoundaryDates_Sp.Parameters(5).Value
    Case -1 'No Data Found
        'Msg here
    Case -2 'SQL Error
        'Msg here
    Case -3 'Invalid parameter
        'Msg here
    End Select

    Date_Boundaries = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Date_Boundaries)"
         f_HandleErr , , , "AIM_ForecastSetup::Date_Boundaries", Now, gDRGeneralError, True, Err
End Function

Public Property Let prForecastID(p_FcstID As String)
On Error GoTo ErrorHandler

    m_FcstId = p_FcstID
    
Exit Property
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Let ForecastID)"
    f_HandleErr , , , "AIM_ForecastSetup::Let ForecastID", Now, gDRGeneralError, True, Err
End Property

Public Property Get prForecastID() As String
On Error GoTo ErrorHandler

    prForecastID = m_FcstId
    
Exit Property
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Let ForecastID)"
     f_HandleErr , , , "AIM_ForecastSetup::prForecastID", Now, gDRGeneralError, True, Err
End Property

'*****************************************************************************
'   FUNCTIONS
'*****************************************************************************
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the AIMForecast table to populate the ForecastID dropdown
' .......................................................................
'   Returns false if there are no records to fetch,
'   else true after setting the display to the first record.
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function GetForecastList() As Boolean
On Error GoTo ErrorHandler
    
    Dim rsForecastList As ADODB.Recordset
    Dim RtnCode As Boolean
    Dim RowCounter As Long, ColCounter As Integer
    Dim AddItemString As String
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    Dim EBFcstId As SSOleDBCombo
    
    Set EBFcstId = Me.dcForecastID
    
    EBFcstId.Redraw = False
    Screen.MousePointer = vbHourglass
    
    'Default to false until the process returns it's result.
    GetForecastList = False
    
    'Get Forecast IDs and Descriptions from AIMForecast
    RtnCode = gList_FcstIDs(Cn, rsForecastList, Me.Caption, False)
    If RtnCode = True Then
        EBFcstId.RemoveAll
        EBFcstId.Reset
    
        m_FcstId = IIf(Trim$(m_FcstId) = "", rsForecastList(0).Value, m_FcstId)
        rsForecastList.MoveFirst
        AddItemString = ""
        For RowCounter = 0 To rsForecastList.RecordCount - 1
            For ColCounter = 0 To rsForecastList.Fields.Count - 1
                AddItemString = AddItemString & rsForecastList.Fields(ColCounter).Value & vbTab
            Next
            EBFcstId.AddItem AddItemString
            AddItemString = ""
            rsForecastList.MoveNext
        Next
        
        'Get the forecast data to display in the screen's controls
        FetchForecastSetup SQL_GetEq, True
        GetForecastList = True
    End If

CleanUp:
    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
    Set rsForecastList = Nothing
    EBFcstId.Redraw = True
    Set EBFcstId = Nothing
    Screen.MousePointer = vbNormal

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
    Set rsForecastList = Nothing
    EBFcstId.Redraw = True
    Set EBFcstId = Nothing
    'Err.Raise ErrNumber, ErrSource, ErrDescription & "(GetForecastList)"
        f_HandleErr , , , "AIM_ForecastSetup::GetForecastList", Now, gDRGeneralError, True, Err
End Function

Private Function FcstHierarchyChanged(Index As Integer) As Boolean
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim IsEnabled As Boolean
    
    Select Case Index
    Case 2 'Live (critical)
        IsEnabled = False
        'Only weeks for forecast interval
        optFcstInterval(1) = True
        'Live ones will always have rolling
        ckFcstRolling.Value = vbChecked
        'Live ones will always have historical
        ckFcstHistory.Value = vbChecked
        'Live ones will always have system forecast
        ckCalc_SysFcst.Value = vbChecked
        ckCalc_FcstAdj.Value = vbChecked
        'Cannot change the ForecastStartDate for a live forecast
        txtFcstStartDate.Text = Format(Date, gDateFormat)
        txtFcstStartDate_Validate (False)
    Case 1  'Sandbox (non-critical)
        IsEnabled = True
        'Sandbox will allow user to: modify Forecast Interval; deselect Rolling;
        '   deselect History; deselect System Forecast calcs; modify Forecast Start Date
        ckCalc_MasterFcstAdj.Value = vbUnchecked
    End Select
    
    'Set access control for:
    '* Forecast Interval
    For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
        optFcstInterval(IndexCounter).Enabled = IsEnabled
    Next
    '* Forecast Rolling
    ckFcstRolling.Enabled = IsEnabled
    txtFixedPeriods.Enabled = IsEnabled
    lblFixedPeriods.Enabled = IsEnabled
    '* Forecast History
    ckFcstHistory.Enabled = IsEnabled
    '* Forecast Calculations
    ckCalc_SysFcst.Enabled = IsEnabled
    ckCalc_FcstAdj.Enabled = IsEnabled
    txtFcstStartDate.Enabled = IsEnabled
    lblFcstStartDate.Enabled = IsEnabled
     
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(FcstHierarchyChanged)"
        f_HandleErr , , , "AIM_ForecastSetup::FcstHierarchyChanged", Now, gDRGeneralError, True, Err
End Function

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Saves data to the AIMForecastSetup table
'   for the given Forecast ID.
' .......................................................................
'   Parameters:
'       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
'       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
'       r_rsCompound -- If p_Related was true, then there will be more than one recordset, else
'           there will be just the recordset for AIMForecastSetup.
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function SaveForecastSetup() As Long
On Error GoTo ErrorHandler
                
    Dim RtnCode As Long
    Dim intCount As Long
    Dim ForecastCriteria As FORECAST_CRITERIA
    Dim IndexVar As Integer
    Dim IndexCounter As Integer
    Dim strMessage As String
    
    'Validate fields that have rules associated with them
    txtFcstId_Validate (False)
    txtFcstStartDate_Validate (False)
    With ForecastCriteria
        'Get the screen values.
        .FcstId = Trim$(txtFcstId.Text)
        .FcstDesc = Trim$(txtFcstDesc.Text)
        .FcstLocked = IIf(ckFcstLocked.Value = vbChecked, True, False)
        .FcstEnabled = IIf(ckFcstEnabled.Value = vbChecked, True, False)
        For IndexCounter = optFcstHierarchy.LBound To optFcstHierarchy.UBound
            .FcstHierarchy = IIf(optFcstHierarchy(IndexCounter).Value = True, IndexCounter, .FcstHierarchy)
        Next
        .FcstStartDate = Format(Trim$(txtFcstStartDate.Text), g_ISO_DATE_FORMAT)
        .FcstRolling = IIf(ckFcstRolling = vbChecked, True, False)
        .FcstPeriods_Future = txtFcstPeriods_Future.Text
        .FcstHistory = IIf(ckFcstHistory = vbChecked, True, False)
        .FcstPeriods_Historical = txtFcstPeriods_Historical.Text
        .FreezePds = txtFreezePeriods.Text
        .FixedPds = txtFixedPeriods.Text
        For IndexCounter = optApplyTrend.LBound To optApplyTrend.UBound
            .ApplyTrend = IIf(optApplyTrend(IndexCounter).Value = True, IndexCounter, .ApplyTrend)
        Next
        For IndexCounter = optFcstInterval.LBound To optFcstInterval.UBound
            .FcstInterval = IIf(optFcstInterval(IndexCounter).Value = True, IndexCounter, .FcstInterval)
        Next
        For IndexCounter = optFcstUnit.LBound To optFcstUnit.UBound
            .FcstUnit = IIf(optFcstUnit(IndexCounter).Value = True, IndexCounter, .FcstUnit)
        Next
        .Calc_SysFcst = IIf(ckCalc_SysFcst = vbChecked, True, False)
        .Calc_Netreq = IIf(ckCalc_NetReq = vbChecked, True, False)
        .Calc_ProjInv = IIf(ckCalc_ProjInv = vbChecked, True, False)
        .Calc_HistDmd = IIf(ckCalc_HistDmd = vbChecked, True, False)
        .Calc_MasterFcstAdj = IIf(ckCalc_MasterFcstAdj = vbChecked, True, False)
        .Calc_AdjNetReq = IIf(ckCalc_AdjNetReq = vbChecked, True, False)
        .Calc_FcstAdj = IIf(ckCalc_FcstAdj = vbChecked, True, False)
        .Calc_ProdConst = IIf(ckCalc_ProdConst = vbChecked, True, False)
    End With
    
    RtnCode = gSave_ForecastSetup(ForecastCriteria, False)
    If RtnCode = 1 Then
        m_FcstSetupKey = ForecastCriteria.FcstSetupKey
        m_FcstId = ForecastCriteria.FcstId
        '   'A.Stocksdale - BEGIN - Multi-filters for item.
        'Save filters
        If Trim$(Me.txtSelected.Text) <> COMP_EMPTY Then
            RtnCode = g_SaveFilters(Cn, m_FcstSetupKey, m_xaAllCriteria)
            If RtnCode = SUCCEED Then
                strMessage = getTranslationResource("STATMSG07302")
                If StrComp(strMessage, "STATMSG07302") = 0 Then strMessage = "Item Filters saved successfully."
                Write_Message strMessage
            End If
        End If
        '   'A.Stocksdale - END - Multi-filters for item.
        If m_NewFcst = True Then
            'If SuccessFull add the user with all rights
            mAccessControl_Update gUserID, 0, True
            mAccessControl_Update gUserID, 1, True
            mAccessControl_Update gUserID, 2, True
            mAccessControl_Update gUserID, 3, True
            mAccessControl_Update gUserID, 4, True
            mAccessControl_Update gUserID, 5, True
        End If
        m_NewFcst = False
        atForecastSetup.Tabs(4).Enabled = True
        If GetForecastList = False Then
            'Start a new forecast
            InitializeNewRecord
        Else
            'GetForecastList should have triggered fetching forecast data for the first record.
        End If
        
        Me.dcForecastID.Text = m_FcstId
        
        Me.tbAccessControl.Enabled = True
    
        'Set return value
        SaveForecastSetup = RtnCode
    End If
            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SaveForecastSetup)"
     f_HandleErr , , , "AIM_ForecastSetup::SaveForecastSetup", Now, gDRGeneralError, True, Err
End Function
Private Function gSave_ForecastSetup( _
    ForecastCriteria As FORECAST_CRITERIA, _
    Optional p_UpdateForRolling As Boolean, Optional p_LastUpdated As Date _
) As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    
    Dim intCount As Long

    Dim IndexVar As Long
    Dim IndexCounter As Long

    p_LastUpdated = IIf(Trim$(p_LastUpdated) = "12:00:00 AM", ForecastCriteria.FcstStartDate, p_LastUpdated)

    'Init. the command object
    With AIM_DmdPlanFcstSetup_Save_Sp
    'Set values
        '-- Conditions
        .Parameters("@UserID").Value = gUserID
        .Parameters("@UpdateForRolling").Value = p_UpdateForRolling
        '-- Fields
        .Parameters("@FcstID").Value = ForecastCriteria.FcstId
        .Parameters("@FcstDesc").Value = ForecastCriteria.FcstDesc
        .Parameters("@FcstHierarchy").Value = ForecastCriteria.FcstHierarchy
        .Parameters("@FcstEnabled").Value = ForecastCriteria.FcstEnabled
        .Parameters("@FcstLocked").Value = ForecastCriteria.FcstLocked
        .Parameters("@FcstStartDate").Value = ForecastCriteria.FcstStartDate
        .Parameters("@LastUpdated").Value = p_LastUpdated
        .Parameters("@FcstRolling").Value = ForecastCriteria.FcstRolling
        .Parameters("@FcstHistory").Value = ForecastCriteria.FcstHistory
        .Parameters("@FcstPds_Future").Value = ForecastCriteria.FcstPeriods_Future
        .Parameters("@FcstPds_Historical").Value = ForecastCriteria.FcstPeriods_Historical
        .Parameters("@FreezePds").Value = ForecastCriteria.FreezePds
        .Parameters("@FixedPds").Value = ForecastCriteria.FixedPds
        .Parameters("@FcstInterval").Value = ForecastCriteria.FcstInterval
        .Parameters("@FcstUnit").Value = ForecastCriteria.FcstUnit
        .Parameters("@ApplyTrend").Value = ForecastCriteria.ApplyTrend
        .Parameters("@Calc_SysFcst").Value = ForecastCriteria.Calc_SysFcst
        .Parameters("@Calc_NetRqmt").Value = ForecastCriteria.Calc_Netreq
        .Parameters("@Calc_ProjInv").Value = ForecastCriteria.Calc_ProjInv
        .Parameters("@Calc_HistDmnd").Value = ForecastCriteria.Calc_HistDmd
        .Parameters("@Calc_MasterFcstAdj").Value = ForecastCriteria.Calc_MasterFcstAdj
        .Parameters("@Calc_FcstAdj").Value = ForecastCriteria.Calc_FcstAdj
        .Parameters("@Calc_AdjNetReq").Value = ForecastCriteria.Calc_AdjNetReq
        .Parameters("@Calc_ProdConst").Value = ForecastCriteria.Calc_ProdConst
        '-- Item filter criteria
        .Parameters("@Item").Value = ForecastCriteria.Item
        .Parameters("@ItStat").Value = ForecastCriteria.ItStat
        .Parameters("@VnId").Value = ForecastCriteria.VnId
        .Parameters("@Assort").Value = ForecastCriteria.Assort
        .Parameters("@ById").Value = ForecastCriteria.ById
        .Parameters("@Class1").Value = ForecastCriteria.Class1
        .Parameters("@Class2").Value = ForecastCriteria.Class2
        .Parameters("@Class3").Value = ForecastCriteria.Class3
        .Parameters("@Class4").Value = ForecastCriteria.Class4
        .Parameters("@LcId").Value = ForecastCriteria.LcId
        .Parameters("@LStatus").Value = ForecastCriteria.LStatus
        .Parameters("@LDivision").Value = ForecastCriteria.LDivision
        .Parameters("@LRegion").Value = ForecastCriteria.LRegion
        .Parameters("@LUserDefined").Value = ForecastCriteria.LUserDefined
        '-- Return parameters
        '.Parameters("@FcstSetupKey").Value = ForecastCriteria.FcstSetupKey '  commented out since this is giving error sri Aug-16-04
        .Parameters("@FcstSetupKey").Value = 0 ' Added this line in place of the abouve line sri
    '-- Save
        .Execute
    End With

    RtnCode = AIM_DmdPlanFcstSetup_Save_Sp.Parameters(0).Value

    If RtnCode = 1 Then
        ForecastCriteria.FcstSetupKey = AIM_DmdPlanFcstSetup_Save_Sp.Parameters("@FcstSetupKey").Value
        'Set return value
        gSave_ForecastSetup = RtnCode
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(gSave_ForecastSetup)"
     f_HandleErr , , , "AIM_ForecastSetup::gSave_ForecastSetup", Now, gDRGeneralError, True, Err
End Function

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the AIMForecastSetup table for the modular forecast ID.
' .......................................................................
'   Parameters:
'       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
'       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function DeleteForecastSetup( _
    p_ForecastID As String _
) As Boolean
On Error GoTo ErrorHandler
                
    Dim RtnCode As Long
    Dim BoolRtnCode As Boolean
    Dim AIM_DmdPlanFcstSetup_Delete_Sp As ADODB.Command
    Dim CurrentForecastID As String
    'Dim CurrentForecastKey As String
    Dim strMessage As String
    
    'Default to false until process returns it's result
    DeleteForecastSetup = False
    
    'Save the current forecast ID, incase of failure
    CurrentForecastID = m_FcstId
    'CurrentForecastKey = m_FcstSetupKey
    
    'Init. the command object
    Set AIM_DmdPlanFcstSetup_Delete_Sp = New ADODB.Command
    With AIM_DmdPlanFcstSetup_Delete_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DmdPlanFcstSetup_Delete_Sp"
        
        'Set parameters and their values
        .Parameters.Append .CreateParameter( _
            "RETURN", adInteger, adParamReturnValue)
        '@FcstID nvarchar(12) = '',
        .Parameters.Append .CreateParameter( _
            "@FcstID", adVarWChar, adParamInput, 12, p_ForecastID)
        '@UserID nvarchar(12) = '',
        .Parameters.Append .CreateParameter( _
            "@UserID", adVarWChar, adParamInput, 12, gUserID)
        
        .Execute
        RtnCode = .Parameters(0).Value
    End With
    'Check return code -- 0 = fail; 1 = succeed
    If RtnCode <> 1 Then
        'FAIL
        '>>>Show message here
        If RtnCode = -3 Then
            'Bad permissions
            strMessage = getTranslationResource("MSGBOX07303")
            If StrComp(strMessage, "MSGBOX07303") = 0 Then _
                strMessage = "Insufficient access rights for Forecast Maintenance. Operation aborted."
        End If
        
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        'Reset to current forecast
        dcForecastID.Text = CurrentForecastID
        
    Else
        Me.dgAccessControl.Reset
        
        'Get the next record
        RtnCode = FetchForecastSetup(SQL_GetGT, True)
        If GetForecastList = False Then
            'Start a new forecast
            InitializeNewRecord
        Else
            'GetForecastList should have triggered fetching forecast data for the first record.
        End If
        
    End If
     BoolRtnCode = True
CleanUp:
    DeleteForecastSetup = BoolRtnCode
    If Not (AIM_DmdPlanFcstSetup_Delete_Sp Is Nothing) Then Set AIM_DmdPlanFcstSetup_Delete_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstSetup_Delete_Sp = Nothing
    
Exit Function
ErrorHandler:
    dcForecastID.Text = CurrentForecastID
    'Err.Raise Err.Number, Err.source, Err.Description & "(DeleteForecastSetup)"
     f_HandleErr , , , "AIM_ForecastSetup::DeleteForecastSetup", Now, gDRGeneralError, True, Err

    GoTo CleanUp
End Function

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the AIMForecastSetup table for the modular forecast ID.
' .......................................................................
'   Parameters:
'       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
'       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function FetchForecastSetup( _
    p_Action As SQL_ACTIONS, _
    p_Related As Boolean _
    ) As Boolean
On Error GoTo ErrorHandler
                
    Dim RtnCode As Long
    Dim intCount As Long
    Dim rsCompound As ADODB.Recordset
    Dim DBugCounter As Integer
    Dim DBugText As String
    
    'Default to false until process returns it's result
    FetchForecastSetup = False
    
    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
    RtnCode = gFetch_ForecastSetup(Cn, m_FcstId, gUserID, p_Action, p_Related, rsCompound)
    
    'Check return code -- 0 = fail; 1 = succeed
    If RtnCode = 0 Then
        'FAIL
        Exit Function
    End If
    
    If f_IsRecordsetValidAndOpen(rsCompound) Then
        ' Display results from each SELECT statement
        intCount = 1
        Do Until rsCompound Is Nothing
            Do Until rsCompound.eof
                'Set display to match recordset(s)
                Select Case intCount
                Case 1  'AIMForecastSetup
                    'Get recordset values
                    m_FcstId = rsCompound!FcstId
                    m_FcstSetupKey = rsCompound!FcstSetupKey
                    
                    RtnCode = RefreshForecastSetup(rsCompound!FcstId, rsCompound!FcstDesc, _
                        rsCompound!FcstLocked, rsCompound!FcstEnabled, _
                        rsCompound!FcstHierarchy, rsCompound!FcstStartDate, _
                        rsCompound!FcstRolling, rsCompound!FcstPds_Future, _
                        rsCompound!FcstHistory, rsCompound!FcstPds_Historical, _
                        rsCompound!FreezePds, rsCompound!FixedPds, _
                        rsCompound!ApplyTrend, rsCompound!Calc_SysFcst, _
                        rsCompound!Calc_MasterFcstAdj, rsCompound!Calc_FcstAdj, _
                        rsCompound!Calc_Netreq, rsCompound!Calc_AdjNetReq, _
                        rsCompound!Calc_ProjInv, rsCompound!Calc_HistDmd, rsCompound!Calc_ProdConst, _
                        rsCompound!FcstInterval, rsCompound!FcstUnit)
                Case 2  'ForecastAccess
                    If rsCompound.Bookmark = 1 Then
                        'RtnCode = RefreshForecastAccess(rsCompound)
                    End If
                Case 3  'UserElements
'                    RtnCode = RefreshUserElements(rsCompound!FcstRepositoryID, rsCompound!RecordDesc)
                    
                End Select
                If Not rsCompound.eof Then rsCompound.MoveNext 'There should be no more for the first recordset
            Loop
            
            If p_Related = True Then
                Set rsCompound = rsCompound.NextRecordset
                intCount = intCount + 1
            Else
                Exit Do
            End If
        Loop
        
        If GetRepositoryCount() > 0 Then
            'Check permissions and enable/disable fields
            Me.ckFcstLocked.Value = vbChecked
            ForecastFieldsToggle False
        Else
            ForecastFieldsToggle True
            mCheck_UserAccess
        End If
        
        'Show add, delete and the dropdown
        Me.tbNavigation.Tools("ID_AddNew").Enabled = True
        tbNavigation.Tools("ID_Delete").Enabled = True
        txtFcstId.Visible = False
        dcForecastID.Visible = True
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    End If
        
    FetchForecastSetup = True

CleanUp:
    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
    Set rsCompound = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
    Set rsCompound = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(FetchForecastSetup)"
     f_HandleErr , , , "AIM_ForecastSetup::FetchForecastSetup", Now, gDRGeneralError, True, Err
End Function
Private Function GetRepositoryCount() As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim AIM_RepositoryDetail_Checkin_Sp As ADODB.Command
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDesc As String
    Dim RepositoryKey As Long

    RepositoryKey = g_GetRepositoryKey(Cn, m_FcstId)
    If RepositoryKey = -1 Then
        'some thing is wrong in getting the repositorykey value
        GetRepositoryCount = -1
    Exit Function
    End If

    Set AIM_RepositoryDetail_Checkin_Sp = New ADODB.Command
    With AIM_RepositoryDetail_Checkin_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RepositoryDetail_Checkin_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter("@RepositoryKey", adNumeric, adParamInput)
        .Parameters("@RepositoryKey").Precision = 18
        .Parameters("@RepositoryKey").NumericScale = 0
        .Parameters.Append .CreateParameter("@RowCount", adInteger, adParamInputOutput)
        'Set values
        .Parameters("@RepositoryKey").Value = RepositoryKey
        .Parameters("@RowCount").Value = Null
        .Execute
        RtnCode = .Parameters(0).Value
        GetRepositoryCount = IIf(IsNull(.Parameters("@RowCount").Value), -1, .Parameters("@RowCount").Value)
    End With
    
    If Not (AIM_RepositoryDetail_Checkin_Sp Is Nothing) Then Set AIM_RepositoryDetail_Checkin_Sp.ActiveConnection = Nothing
    Set AIM_RepositoryDetail_Checkin_Sp = Nothing

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    If Not (AIM_RepositoryDetail_Checkin_Sp Is Nothing) Then Set AIM_RepositoryDetail_Checkin_Sp.ActiveConnection = Nothing
    Set AIM_RepositoryDetail_Checkin_Sp = Nothing
    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(AIM_ForecastSetup.GetRepositoryCount)"
     f_HandleErr , , , "AIM_ForecastSetup::GetRepositoryCount", Now, gDRGeneralError, True, Err
End Function
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Starts a new record-entry form for ForecastSetup
' .......................................................................
'   Parameters:
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function InitializeNewRecord() As Long
On Error GoTo ErrorHandler
    
    Dim RtnCode As Long
    Dim FcstId As String
    Dim FcstDesc As String
    Dim FcstLocked As Boolean
    Dim FcstEnabled As Boolean
    Dim FcstHierarchy As Byte
    Dim FcstStartDate As Date
    Dim FcstRolling As Boolean
    Dim FcstPeriods_Future As Byte
    Dim FcstHistory As Boolean
    Dim FcstPeriods_Historical As Byte
    Dim FreezePds As Byte
    Dim FixedPds As Byte
    Dim ApplyTrend As Byte
    Dim FcstInterval As Byte
    Dim FcstUnit As Byte
    Dim p_Calc_SysRqmt As Boolean
    Dim p_Calc_NetRqmt As Boolean
    Dim p_Calc_PrjInvt As Boolean
    Dim p_Calc_HstDmnd As Boolean
    Dim p_Calc_NetRqmtAndPlnRcpt As Boolean
    Dim p_Calc_NetRqmtAndPrdCons As Boolean
    Dim p_Calc_PrjInvtAndPrdCons As Boolean
    Dim p_Calc_ProdConst As Boolean
    
    'Set flags
    m_NewFcst = True
        
    g_InitCriteria m_xaAllCriteria
    g_InitCriteria m_xaNewCriteria
    g_InitCriteria m_xaOrigCriteria
    Me.txtSelected.Text = COMP_EMPTY
    Me.txtNewFilters.Text = COMP_EMPTY
    
    'Set default values
    FcstId = getTranslationResource(NEWFCST)
    FcstDesc = getTranslationResource(NEWFCSTDESC)
    FcstLocked = False
    FcstEnabled = True
    
    FcstStartDate = Format(Date, gDateFormat)
    FcstPeriods_Future = 6
    FcstPeriods_Historical = 6
    FreezePds = 0
    FixedPds = 6
    FcstRolling = True
    FcstHistory = False
    FcstHierarchy = 1
    ApplyTrend = 2
    FcstInterval = 1
    FcstUnit = 0
    p_Calc_SysRqmt = True
    p_Calc_NetRqmt = False
    p_Calc_PrjInvt = False
    p_Calc_HstDmnd = False
    p_Calc_NetRqmtAndPlnRcpt = False
    p_Calc_NetRqmtAndPrdCons = False
    p_Calc_PrjInvtAndPrdCons = False
    p_Calc_ProdConst = False
    m_FcstSetupKey = -1
    RtnCode = RefreshForecastSetup(FcstId, FcstDesc, FcstLocked, FcstEnabled, _
        FcstHierarchy, FcstStartDate, FcstRolling, FcstPeriods_Future, FcstHistory, _
        FcstPeriods_Historical, FreezePds, FixedPds, ApplyTrend, p_Calc_SysRqmt, p_Calc_NetRqmt, _
        p_Calc_PrjInvt, p_Calc_HstDmnd, p_Calc_NetRqmtAndPlnRcpt, p_Calc_NetRqmtAndPrdCons, p_Calc_PrjInvtAndPrdCons, _
        p_Calc_ProdConst, FcstInterval, FcstUnit)
    
    Me.tbAccessControl.Enabled = False
    
    'Enable all fields
    ForecastFieldsToggle True
    
    'Hide add, delete and the dropdown
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    tbNavigation.Tools("ID_Delete").Enabled = False
    txtFcstId.Visible = True
    dcForecastID.Visible = False
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
    atForecastSetup.Tabs(4).Enabled = False 'desible the accesscontrol until the new fcst is saved
    RtnCode = 0
CleanUp:
    InitializeNewRecord = RtnCode
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_AIMForecast_InitializeNew)"
     f_HandleErr , , , "AIM_ForecastSetup::InitializeNewRecord", Now, gDRGeneralError, True, Err
    RtnCode = -1
    GoTo CleanUp
End Function

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the recordset and sets values for screen controls.
'   The recordset is to be discarded by the calling function.
' .......................................................................
'   Parameters:
'       AIMForecastSetup.*
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function RefreshForecastSetup( _
    p_FcstID As String, p_FcstDesc As String, _
    p_FcstLocked As Boolean, p_FcstEnabled As Boolean, _
    p_FcstHierarchy As Byte, p_FcstStartDate As Date, _
    p_FcstRolling As Boolean, p_FcstPeriods_Future As Byte, _
    p_FcstHistory As Boolean, p_FcstPeriods_Historical As Byte, _
    p_FreezePds As Byte, p_FixedPds As Byte, _
    p_ApplyTrend As Byte, p_Calc_SysFcst As Boolean, _
    p_Calc_MasterFcstAdj As Boolean, p_Calc_FcstAdj As Boolean, _
    p_Calc_NetReq As Boolean, p_Calc_AdjNetReq As Boolean, p_Calc_ProjInv As Boolean, _
    p_Calc_HistDmd As Boolean, p_Calc_ProdConst As Boolean, p_FcstInterval As Byte, _
    p_FcstUnit As Byte, Optional p_Item As String = "", Optional p_ItStat As String = "", _
    Optional p_VnId As String = "", Optional p_Assort As String = "", _
    Optional p_ById As String = "", Optional p_Class1 As String = "", _
    Optional p_Class2 As String = "", Optional p_Class3 As String = "", _
    Optional p_Class4 As String = "", Optional p_LcID As String = "", _
    Optional p_LStatus As String = "", Optional p_LDivision As String = "", _
    Optional p_LRegion As String = "", Optional p_LUserDefined As String = "" _
) As Long
On Error GoTo ErrorHandler

    Dim IndexVar As Integer
    Dim IndexCounter As Integer
    Dim RtnCode As Long
    
    Me.txtFcstId.Text = p_FcstID
    Me.dcForecastID.Text = IIf(StrComp(p_FcstID, getTranslationResource(NEWFCST), vbTextCompare) <> 0, p_FcstID, "")
    Me.txtFcstDesc.Text = p_FcstDesc
    Me.ckFcstLocked.Value = IIf(p_FcstLocked = False, vbUnchecked, vbChecked)
    Me.ckFcstEnabled.Value = IIf(p_FcstEnabled = False, vbUnchecked, vbChecked)
    
    IndexVar = p_FcstHierarchy
    For IndexCounter = Me.optFcstHierarchy.LBound To Me.optFcstHierarchy.UBound
        Me.optFcstHierarchy(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
'    Call optFcstHierarchy_Click(IndexVar)
    Me.txtFcstStartDate.Text = Format(p_FcstStartDate, gDateFormat)
    Me.ckFcstRolling.Value = IIf(p_FcstRolling = False, vbUnchecked, vbChecked)
    Me.txtFcstPeriods_Future.Text = Format(p_FcstPeriods_Future, "##0")
    Me.ckFcstHistory.Value = IIf(p_FcstHistory = False, vbUnchecked, vbChecked)
    Me.txtFcstPeriods_Historical.Text = Format(p_FcstPeriods_Historical, "##0")
    Me.txtFreezePeriods.Text = p_FreezePds
    Me.txtFixedPeriods.Text = p_FixedPds
    IndexVar = p_ApplyTrend
    For IndexCounter = Me.optApplyTrend.LBound To Me.optApplyTrend.UBound
        Me.optApplyTrend(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
    
    IndexVar = p_FcstInterval
    For IndexCounter = Me.optFcstInterval.LBound To Me.optFcstInterval.UBound
        Me.optFcstInterval(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
    IndexVar = p_FcstUnit
    For IndexCounter = Me.optFcstUnit.LBound To Me.optFcstUnit.UBound
        Me.optFcstUnit(IndexCounter).Value = IIf(IndexCounter = IndexVar, True, False)
    Next
    
    Me.ckCalc_SysFcst.Value = IIf(p_Calc_SysFcst = False, vbUnchecked, vbChecked)
    Me.ckCalc_MasterFcstAdj.Value = IIf(p_Calc_MasterFcstAdj = False, vbUnchecked, vbChecked)
    Me.ckCalc_FcstAdj.Value = IIf(p_Calc_FcstAdj = False, vbUnchecked, vbChecked)
    Me.ckCalc_NetReq.Value = IIf(p_Calc_NetReq = False, vbUnchecked, vbChecked)
    Me.ckCalc_AdjNetReq.Value = IIf(p_Calc_AdjNetReq = False, vbUnchecked, vbChecked)
    Me.ckCalc_ProjInv.Value = IIf(p_Calc_ProjInv = False, vbUnchecked, vbChecked)
    Me.ckCalc_HistDmd.Value = IIf(p_Calc_HistDmd = False, vbUnchecked, vbChecked)
    Me.ckCalc_ProdConst.Value = IIf(p_Calc_ProdConst = False, vbUnchecked, vbChecked)
    
    RtnCode = g_SelectFilters(Cn, m_FcstSetupKey, m_xaOrigCriteria, COMP_EMPTY)
    If RtnCode = SUCCEED Then
        Me.txtSelected.Text = g_CXArrToPara(m_xaOrigCriteria, False, False)
    Else
        Me.txtSelected.Text = COMP_EMPTY
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(RefreshForecastSetup)"
     f_HandleErr , , , "AIM_ForecastSetup::RefreshForecastSetup", Now, gDRGeneralError, True, Err
End Function

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Toggles the availability of screen controls, given the boolean parameter.
' .......................................................................
'   Parameters:
'       p_Enabled - True or False
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Sub ForecastFieldsToggle(p_Enabled As Boolean)
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Integer
    
    If ckFcstLocked.Value = vbUnchecked Then
        LockControl True
        With Me.optFcstHierarchy
            For IndexCounter = .LBound To .UBound
                If .Item(IndexCounter).Value = True Then
                   FcstHierarchyChanged CInt(IndexCounter)
                   Exit For
                End If
            Next
        End With
    Else
        LockControl False
    End If
    

Exit Sub
ErrorHandler:
    Me.tbNavigation.Redraw = True
    'Err.Raise Err.Number, Err.source, Err.Description & "(ForecastFieldsToggle)"
     f_HandleErr , , , "AIM_ForecastSetup::ForecastFieldsToggle", Now, gDRGeneralError, True, Err
End Sub

Private Sub LockControl(p_Enabled As Boolean)
On Error GoTo ErrorHandler
    Dim lngCounter As Long
        
    With Me.tbNavigation
        .Redraw = False
        .Tools("ID_Save").Enabled = p_Enabled
        .Tools("ID_Delete").Enabled = p_Enabled
        .Tools("ID_AddNew").Enabled = p_Enabled
        .Tools("ID_FilterItems").Enabled = p_Enabled
        .Redraw = True
    End With
'    Me.fraHierarchy.Enabled = p_Enabled
    With Me.optFcstHierarchy
        For lngCounter = .LBound To .UBound
            .Item(lngCounter).Enabled = p_Enabled
        Next lngCounter
    End With
    
'    Me.fraPeriods.Enabled = p_Enabled
'    Me.fraFixed.Enabled = p_Enabled
    Me.txtFcstStartDate.Enabled = p_Enabled
    Me.lblFcstStartDate.Enabled = p_Enabled
    Me.txtFixedPeriods.Enabled = p_Enabled
    Me.lblFixedPeriods.Enabled = p_Enabled
    
'    Me.fraRollingFcst.Enabled = p_Enabled
    Me.ckFcstRolling.Enabled = p_Enabled
    Me.txtFcstPeriods_Future.Enabled = p_Enabled
    Me.lblFcstPeriods_Future.Enabled = p_Enabled
    Me.ckFcstHistory.Enabled = p_Enabled
    Me.txtFcstPeriods_Historical.Enabled = p_Enabled
    Me.lblFcstPeriods_Historical.Enabled = p_Enabled
    
'    Me.fraForecastInterval.Enabled = p_Enabled
    With Me.optFcstInterval
        For lngCounter = .LBound To .UBound
            .Item(lngCounter).Enabled = p_Enabled
        Next
    End With
    Me.txtFreezePeriods.Enabled = p_Enabled
    Me.lblFreezePeriods.Enabled = p_Enabled
    
'    Me.fraDataElements.Enabled = p_Enabled
    ckCalc_SysFcst.Enabled = p_Enabled
    ckCalc_MasterFcstAdj.Enabled = p_Enabled
    ckCalc_FcstAdj.Enabled = p_Enabled
    ckCalc_NetReq.Enabled = p_Enabled
    ckCalc_AdjNetReq.Enabled = p_Enabled
    ckCalc_ProjInv.Enabled = p_Enabled
    ckCalc_HistDmd.Enabled = p_Enabled
    ckCalc_ProdConst.Enabled = p_Enabled
    
'    Me.fraApplyTrend.Enabled = p_Enabled
    With Me.optApplyTrend
        For lngCounter = .LBound To .UBound
            .Item(lngCounter).Enabled = p_Enabled
        Next
    End With
    
'    Me.fraUnits.Enabled = p_Enabled
    With Me.optFcstUnit
        For lngCounter = .LBound To .UBound
            .Item(lngCounter).Enabled = p_Enabled
        Next
    End With
    
Exit Sub
ErrorHandler:
    Me.tbNavigation.Redraw = True
    'Err.Raise Err.Number, Err.source, Err.Description & "(LockControls)"
     f_HandleErr , , , "AIM_ForecastSetup::LockControls", Now, gDRGeneralError, True, Err
End Sub

Private Function m_FetchUserAccessCodes() As Long
On Error GoTo ErrorHandler

    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    Dim rsCodeLookup As ADODB.Recordset
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDescription As String
    Dim AddItemString As String
    Dim IndexCounter As Integer
    Dim RowCount As Integer
    
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
        With AIM_CodeLookup_Get_Sp
            .CommandType = adCmdStoredProc
            Set .ActiveConnection = Cn
            .CommandText = "AIM_CodeLookup_Get_Sp"
            'Set parameters and their values
            .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
            .Parameters.Append .CreateParameter("@CodeType", adVarWChar, adParamInput, 30)
            .Parameters.Append .CreateParameter("@LangID", adVarWChar, adParamInput, 10)
            'Values
            .Parameters("@CodeType").Value = g_CODETYPE_ACCESSCONTROL
            .Parameters("@LangID").Value = gLangID
        End With
    
        Set rsCodeLookup = New ADODB.Recordset
        With rsCodeLookup
            .CursorLocation = adUseClient
            .CursorType = adOpenStatic
            .LockType = adLockReadOnly
        End With
        rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    
        If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
            RowCount = rsCodeLookup.RecordCount
            If RowCount = Me.ckAccessCodes.Count Then
                rsCodeLookup.MoveFirst
                For IndexCounter = Me.ckAccessCodes.LBound To Me.ckAccessCodes.UBound
                    Me.ckAccessCodes(IndexCounter).Caption = rsCodeLookup!CodeDesc
                    rsCodeLookup.MoveNext
                Next IndexCounter
            End If
        End If
        
        If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
        Set AIM_CodeLookup_Get_Sp = Nothing
        If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
        Set rsCodeLookup = Nothing
        
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description

    If Err.Number = 3219 Then
        'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
        'Discard the rs and move on.
        Resume Next
    Else
        If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
        Set AIM_CodeLookup_Get_Sp = Nothing
        If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
        Set rsCodeLookup = Nothing
    
        'Err.Raise Err.Number, Err.source, Err.Description & "(m_FetchUserAccessCodes)"
         f_HandleErr , , , "AIM_ForecastSetup::m_FetchUserAccessCodes", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function mAccessControl_Fetch() As Long
On Error GoTo ErrorHandler

    Dim rsAccessControl As ADODB.Recordset
    Dim RtnCode As Long
    
    Dim AddItemString As String
    Dim IndexCounter As Integer
    Dim RowCount As Integer
    Dim AccessControlIdx As Integer
    Dim KeyValue As String
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDescription As String
    Dim CurrentUser As String
    Dim CurrentAccessCode As Integer
    Dim LoopUser As String
    Dim LoopAccessCode As Integer
    Dim RecordCount As Integer
    Dim OldUser As String
    Dim LastAccessCode As Integer
    Dim LastUser As String
    Dim i As Integer
    
    Dim EBAccessGrid As SSOleDBGrid
    Set EBAccessGrid = Me.dgAccessControl
    
    If IsNull(m_FcstSetupKey) Or m_FcstSetupKey = -1 Then Exit Function
    
    'Get Access Type
    RtnCode = gFetch_ForecastAccess(Cn, m_FcstSetupKey, rsAccessControl, "ALL")
    
    Select Case RtnCode
    Case Is > 0 'Success
        If Not f_IsRecordsetOpenAndPopulated(rsAccessControl) Then
            Me.tbAccessControl.Tools("ID_DELETE").Enabled = False
            GoTo CleanUp
        End If
        
        EBAccessGrid.Columns.RemoveAll
        EBAccessGrid.Reset
        
        EBAccessGrid.Redraw = False
        EBAccessGrid.HeadLines = 2
        IndexCounter = 0
        EBAccessGrid.Columns.Add IndexCounter
        EBAccessGrid.Columns(IndexCounter).Name = "UserID"
        EBAccessGrid.Columns(IndexCounter).Caption = getTranslationResource("User ID")
        EBAccessGrid.Columns(IndexCounter).Width = 800
        EBAccessGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        EBAccessGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        m_ColIdx_UserID = IndexCounter
        
        IndexCounter = IndexCounter + 1
        EBAccessGrid.Columns.Add IndexCounter
        EBAccessGrid.Columns(IndexCounter).Name = "UserName"
        EBAccessGrid.Columns(IndexCounter).Caption = getTranslationResource("User Name")
        EBAccessGrid.Columns(IndexCounter).Width = 1200
        EBAccessGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        EBAccessGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        m_ColIdx_UserName = IndexCounter
        
        With Me.ckAccessCodes
            RecordCount = .Count
            ReDim m_ColIdx_AccessCode(RecordCount)
            AccessControlIdx = 0
            For AccessControlIdx = .LBound To .UBound
                IndexCounter = IndexCounter + 1
                EBAccessGrid.Columns.Add IndexCounter
                EBAccessGrid.Columns(IndexCounter).Name = "Code" & CStr(.Item(AccessControlIdx).Index)
                EBAccessGrid.Columns(IndexCounter).Caption = .Item(AccessControlIdx).Caption
                EBAccessGrid.Columns(IndexCounter).Width = 800
                EBAccessGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
                EBAccessGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
                EBAccessGrid.Columns(IndexCounter).Style = ssStyleCheckBox
                m_ColIdx_AccessCode(AccessControlIdx + 1) = IndexCounter
            Next AccessControlIdx
        End With
        
        If gGridAutoSizeOption Then
            'Added to Automatically adjust the column widths depending on the Columns Label translation size.
            AdjustColumnWidths dgAccessControl, ACW_EXPAND
        End If
        
        rsAccessControl.MoveFirst
        LastUser = rsAccessControl!UserID
        
        Do While Not rsAccessControl.eof
            CurrentUser = rsAccessControl!UserID
            AddItemString = CStr(rsAccessControl!UserID) & vbTab & _
                            CStr(rsAccessControl!UserName)
            With Me.ckAccessCodes
                RecordCount = .Count
                IndexCounter = 0
                For AccessControlIdx = .LBound To .UBound
                    If rsAccessControl.eof Then
                            AddItemString = AddItemString & vbTab & _
                                CStr(vbUnchecked)
                    Else
                        LastAccessCode = rsAccessControl!AccessCode
                            If IndexCounter <> LastAccessCode Then
                                For i = IndexCounter To LastAccessCode - 1
                                AddItemString = AddItemString & vbTab & _
                                        CStr(vbUnchecked)
                                Next i
                                AddItemString = AddItemString & vbTab & _
                                CStr(IIf(IsNull(rsAccessControl!AccessCode), vbUnchecked, vbChecked))
                                IndexCounter = LastAccessCode + 1
                            Else
                                AddItemString = AddItemString & vbTab & _
                                CStr(IIf(IsNull(rsAccessControl!AccessCode), vbUnchecked, vbChecked))
                                IndexCounter = IndexCounter + 1
                            End If
                            
                            rsAccessControl.MoveNext
                            If rsAccessControl.eof Then Exit Do
                            CurrentUser = rsAccessControl!UserID
                            If CurrentUser <> LastUser Then
                                LastUser = CurrentUser
                                If IndexCounter <> 6 Then
                                    For i = IndexCounter To 6
                                        AddItemString = AddItemString & vbTab & _
                                            CStr(vbUnchecked)
                                    Next i
                                End If
                                EBAccessGrid.AddItem AddItemString
                                AddItemString = ""
                                AddItemString = CStr(rsAccessControl!UserID) & vbTab & _
                            CStr(rsAccessControl!UserName)
                                If rsAccessControl.eof Then Exit Do
                                Exit For
                            End If
                        End If
                Next AccessControlIdx
            End With
            
            'EBAccessGrid.AddItem AddItemString
            AddItemString = ""
        Loop
        EBAccessGrid.AddItem AddItemString
    
    Case -1
        'No data found
        EBAccessGrid.Redraw = False
        EBAccessGrid.Columns.RemoveAll
        EBAccessGrid.Reset
        
        Me.tbAccessControl.Tools("ID_DELETE").Enabled = False    'Delete
    End Select

CleanUp:
    If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
    Set rsAccessControl = Nothing
    EBAccessGrid.Redraw = True
    Set EBAccessGrid = Nothing

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description

    If Err.Number = 3219 Then
        'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
        'Discard the rs and move on.
        Resume Next
    Else
        EBAccessGrid.Redraw = True
        Set EBAccessGrid = Nothing
        
        If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
        Set rsAccessControl = Nothing
    
        'Err.Raise ErrNumber, ErrSource, ErrDescription & "(mAccessControl_Fetch)"
          f_HandleErr , , , "AIM_ForecastSetup::mAccessControl_Fetch", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function mAccessControl_Update( _
    p_UserID As String, _
    p_AccessCode As Integer, _
    p_Save As Boolean _
) As Long
On Error GoTo ErrorHandler

    Dim AIM_DmdPlanFcstAccess_Update_Sp As ADODB.Command
    Dim rsAccessControl As ADODB.Recordset
    Dim RtnCode As Long
    Dim AddItemString As String
    Dim IndexCounter As Integer
    Dim RowCount As Integer
    Dim AccessControlIdx As Integer
    Dim KeyValue As String
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDescription As String
    
    If IsNull(m_FcstSetupKey) Or m_FcstSetupKey = -1 Then Exit Function
    RtnCode = -2 ' error
    'Create command object
    Set AIM_DmdPlanFcstAccess_Update_Sp = New ADODB.Command
    With AIM_DmdPlanFcstAccess_Update_Sp
        .CommandType = adCmdStoredProc
        Set .ActiveConnection = Cn
        .CommandText = "AIM_DmdPlanFcstAccess_Update_Sp"
        'Set parameters and their values
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        '-- Conditions
        '@CodeType nvarchar(30),
        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@UserID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@AccessCode", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@Save", adBoolean, adParamInput)
        'Values
        .Parameters("@FcstSetupKey").Value = m_FcstSetupKey
        .Parameters("@UserID").Value = p_UserID
        .Parameters("@AccessCode").Value = p_AccessCode
        .Parameters("@Save").Value = p_Save
        'Execute
        .Execute
        RtnCode = .Parameters(0).Value
    End With
CleanUp:
    'Clean up
    If Not (AIM_DmdPlanFcstAccess_Update_Sp Is Nothing) Then Set AIM_DmdPlanFcstAccess_Update_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstAccess_Update_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
    Set rsAccessControl = Nothing
    mAccessControl_Update = RtnCode
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description

    If Err.Number = 3219 Then
        Resume Next
    Else
        Me.dgAccessControl.Redraw = True
        'Err.Raise ErrNumber, ErrSource, ErrDescription & "(mAccessControl_Update)"
         f_HandleErr , , , "AIM_ForecastSetup::mAccessControl_Update", Now, gDRGeneralError, True, Err
        RtnCode = -2
        GoTo CleanUp
    End If
End Function

Private Function mAccessControl_Delete( _
    p_UserID As String _
) As Long
On Error GoTo ErrorHandler

    Dim AIM_DmdPlanFcstAccess_Delete_Sp As ADODB.Command
    Dim rsAccessControl As ADODB.Recordset
    Dim RtnCode As Long
    
    Dim AddItemString As String
    Dim IndexCounter As Integer
    Dim RowCount As Integer
    Dim AccessControlIdx As Integer
    Dim KeyValue As String
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDescription As String
    
    If IsNull(m_FcstSetupKey) Or m_FcstSetupKey = -1 Then Exit Function
    RtnCode = -2
    'Create command object
    Set AIM_DmdPlanFcstAccess_Delete_Sp = New ADODB.Command
    With AIM_DmdPlanFcstAccess_Delete_Sp
        .CommandType = adCmdStoredProc
        Set .ActiveConnection = Cn
        .CommandText = "AIM_DmdPlanFcstAccess_Delete_Sp"
        'Set parameters and their values
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        '-- Conditions
        '@CodeType nvarchar(30),
        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@UserID", adVarWChar, adParamInput, 12)
        'Values
        .Parameters("@FcstSetupKey").Value = m_FcstSetupKey
        .Parameters("@UserID").Value = p_UserID
        'Execute
        .Execute
        RtnCode = .Parameters(0).Value
    End With
CleanUp:
    'Clean up
    If Not (AIM_DmdPlanFcstAccess_Delete_Sp Is Nothing) Then Set AIM_DmdPlanFcstAccess_Delete_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstAccess_Delete_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
    Set rsAccessControl = Nothing
    mAccessControl_Delete = RtnCode
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description

    If Err.Number = 3219 Then
        Resume Next
    Else
        Me.dgAccessControl.Redraw = True
        'Err.Raise ErrNumber, ErrSource, ErrDescription & "(mAccessControl_Delete)"
         f_HandleErr , , , "AIM_ForecastSetup::mAccessControl_Delete", Now, gDRGeneralError, True, Err
        GoTo CleanUp
    End If
End Function

Private Function mSet_EditMode()
On Error GoTo ErrorHandler

    Dim RtnCode As Long

    Dim tempString As String
    Dim BkMark As Variant
    Dim exitloop As Boolean
    Dim frtypecode As Integer

    dcAIMUsers.Redraw = False
    dcAIMUsers.MoveFirst
    BkMark = dcAIMUsers.Bookmark
    dcAIMUsers.Value = dcAIMUsers.Columns(0).CellText(BkMark)
    dcAIMUsers.Redraw = True
    ckAccessCodes(0).Value = vbUnchecked
    ckAccessCodes(1).Value = vbUnchecked
    ckAccessCodes(2).Value = vbUnchecked
    ckAccessCodes(3).Value = vbUnchecked
    ckAccessCodes(4).Value = vbUnchecked
    ckAccessCodes(5).Value = vbUnchecked
   
Exit Function
ErrorHandler:
    dcAIMUsers.Redraw = True
    dgAccessControl.Redraw = True
    'Err.Raise Err.source, Err.Number, Err.Description & "(mSet_EditMode)"
     f_HandleErr , , , "AIM_ForecastSetup::mSet_EditMode", Now, gDRGeneralError, True, Err
End Function

Private Function mCheck_UserAccess()
On Error GoTo ErrorHandler
Dim HasAccess As Boolean
    
    'Check access
    'Enable all the controls
    mToggleUserAccess True, CODE_FullControl
    
    'Now, lock selective controls, as applicable to the user
    HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, CODE_FullControl)
    If HasAccess Then
        Exit Function
    Else
        HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, CODE_ModifyForecastSetup)
        If HasAccess = False Then
            mToggleUserAccess False, CODE_ModifyForecastSetup
        End If
        
        HasAccess = gCheckUserAccess(Cn, m_FcstSetupKey, gUserID, CODE_ControlAccess)
        If HasAccess = False Then
            atForecastSetup.Tabs("AccessControl").Enabled = False
            mToggleUserAccess False, CODE_ControlAccess
        End If
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.source, Err.Number, Err.Description & "(mCheck_UserAccess)"
     f_HandleErr , , , "AIM_ForecastSetup::mCheck_UserAccess", Now, gDRGeneralError, True, Err
End Function

Private Function mSet_Mode()
On Error GoTo ErrorHandler
  
    Dim RtnCode As Long
    Dim tempString As String
    Dim BkMark As Variant
    
    dcAIMUsers.Redraw = False
    dcAIMUsers.Columns(0).Text = dgAccessControl.Columns("UserID").Text
    dcAIMUsers.Text = dgAccessControl.Columns("UserID").Text
    dcAIMUsers.Redraw = True
    
    ckAccessCodes(0).Value = dgAccessControl.Columns("Code0").Text
    ckAccessCodes(1).Value = dgAccessControl.Columns("Code1").Text
    ckAccessCodes(2).Value = dgAccessControl.Columns("Code2").Text
    ckAccessCodes(3).Value = dgAccessControl.Columns("Code3").Text
    ckAccessCodes(4).Value = dgAccessControl.Columns("Code4").Text
    ckAccessCodes(5).Value = dgAccessControl.Columns("Code5").Text

Exit Function
ErrorHandler:
    dcAIMUsers.Redraw = True
    dgAccessControl.Redraw = True
    'Err.Raise Err.source, Err.Number, Err.Description & "(mSet_Mode)"
     f_HandleErr , , , "AIM_ForecastSetup::mSet_Mode", Now, gDRGeneralError, True, Err
End Function

Private Function mAccessControl_Rearrange(p_EditMode As Boolean)
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim setHeight As Integer
    Dim setWidth As Integer
    Dim setTop As Integer
    Dim setLeft As Integer
    Dim tempUnit As Integer
    
    Dim EBAccessControl As SSActiveToolBars
    Set EBAccessControl = Me.tbAccessControl
    
    EBAccessControl.Redraw = False
    
    If p_EditMode Then
        'Contract the grid to uncover the edit frame
        Me.dgAccessControl.Width = 4845
        Me.fraAddPermissions.Visible = True
        Me.dcAIMUsers.SetFocus
        EBAccessControl.Tools("ID_Mode").ChangeAll ssChangeAllName, getTranslationResource("Add")
    Else
        'Expand the grid to cover the edit criterion frame
        Me.dgAccessControl.Width = 9525
        Me.fraAddPermissions.Visible = False
        EBAccessControl.Tools("ID_Mode").ChangeAll ssChangeAllName, getTranslationResource("View")
    End If

    'Toggle record manipulation
    EBAccessControl.Tools("ID_DELETE").Enabled = Not p_EditMode
    EBAccessControl.Tools("ID_ADDNEW").Enabled = Not p_EditMode
    EBAccessControl.Tools("ID_EDIT").Enabled = Not p_EditMode
    
    EBAccessControl.Redraw = True
    Set EBAccessControl = Nothing
    
Exit Function
ErrorHandler:
    EBAccessControl.Redraw = True
    Set EBAccessControl = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(mAccessControl_Rearrange)"
     f_HandleErr , , , "AIM_ForecastSetup::mAccessControl_Rearrange", Now, gDRGeneralError, True, Err
End Function

Private Function mToggleToolbar( _
    p_Enabled As Boolean _
)
On Error GoTo ErrorHandler
    
    Dim EBNav As SSActiveToolBars
    Set EBNav = Me.tbNavigation
    
    'Toggle
    EBNav.Tools("ID_GetFirst").Enabled = p_Enabled
    EBNav.Tools("ID_GetPrev").Enabled = p_Enabled
    EBNav.Tools("ID_GetNext").Enabled = p_Enabled
    EBNav.Tools("ID_GetLast").Enabled = p_Enabled
    EBNav.Tools("ID_Delete").Enabled = p_Enabled
    EBNav.Tools("ID_AddNew").Enabled = p_Enabled
    
    dcForecastID.Enabled = p_Enabled
    txtFcstDesc.Enabled = p_Enabled
    
    optFcstHierarchy(1).Enabled = p_Enabled
    optFcstHierarchy(2).Enabled = p_Enabled
    
    ckFcstEnabled.Enabled = p_Enabled
   
    EBNav.Redraw = True
    Set EBNav = Nothing
    
Exit Function
ErrorHandler:
    EBNav.Redraw = True
    Set EBNav = Nothing
    
   ' Err.Raise Err.Number, Err.source, Err.Description & "(mToggleToolbar)"
     f_HandleErr , , , "AIM_ForecastSetup::mToggleToolbar", Now, gDRGeneralError, True, Err
End Function

Private Sub mToggleUserAccess( _
    p_Enabled As Boolean, _
    p_UserAccess As FCST_ACCESS _
)
On Error GoTo ErrorHandler

    Dim EBNav As SSActiveToolBars
    
    Set EBNav = Me.tbNavigation
    
    Select Case p_Enabled
    Case True
        EBNav.Tools("ID_Save").Enabled = p_Enabled
        EBNav.Tools("ID_GoToBookMark").Enabled = p_Enabled
        EBNav.Tools("ID_SetBookMark").Enabled = p_Enabled
        EBNav.Tools("ID_FilterItems").Enabled = p_Enabled
        EBNav.Tools("ID_FreezeItems").Enabled = p_Enabled
        atForecastSetup.Tabs("AccessControl").Enabled = p_Enabled
        tbAccessControl.Enabled = p_Enabled
    
    Case False
        Select Case p_UserAccess
        Case CODE_ControlAccess
            tbAccessControl.Enabled = False
            atForecastSetup.Tabs("AccessControl").Enabled = False
        
        Case CODE_ModifyForecastSetup
            EBNav.Tools("ID_Save").Enabled = p_Enabled
            EBNav.Tools("ID_GoToBookMark").Enabled = p_Enabled
            EBNav.Tools("ID_SetBookMark").Enabled = p_Enabled
            EBNav.Tools("ID_FilterItems").Enabled = p_Enabled
            EBNav.Tools("ID_FreezeItems").Enabled = p_Enabled
            'tbAccessControl.Enabled = False
        End Select  'p_UserAccess
    End Select  'p_Enabled
    
    EBNav.Redraw = True
    Set EBNav = Nothing

Exit Sub
ErrorHandler:
    EBNav.Redraw = True
    Set EBNav = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(mToggleUserAccess)"
     f_HandleErr , , , "AIM_ForecastSetup::mToggleUserAccess", Now, gDRGeneralError, True, Err
End Sub

'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Calls the Item Filter screen
'   Accepts the parameters returned by that dialog and sets values for
'   ForecastSetup item-filter screen controls.
' .......................................................................
'   Parameters:
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Private Function DisplayFilters( _
    p_CurrentFilter As String _
) As String
On Error GoTo ErrorHandler

    'Dim arrItems As XArrayDB
    Dim LockFields As Boolean
    
    'Dim FcstItemFilter As FCST_ITEM_FILTER
    Dim RowCounter As Long, ColCounter As Long
    Dim AllFilters As String, NewFilters As String
    Dim ShowFilters As Form
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    
    LockFields = False  'Users of this screen SHOULD be able to edit existing filters
    Set ShowFilters = AIM_ForecastFilter
    With ShowFilters
        Set .pr_ADOConn = Cn
        'Send over the ones that are to be locked
        Set .rt_xaOrigFilters = m_xaOrigCriteria
        'Send over the ones that are additional to the saved set
        Set .rt_xaExtraFilters = m_xaNewCriteria
        'Send over the entire set (permanent + temporary)
        Set .rt_xaAllFilters = IIf(g_IsArray(m_xaAllCriteria), m_xaAllCriteria, m_xaOrigCriteria)
        'Set other parameters
        .pr_ExistingFilters = p_CurrentFilter
        .pr_LockExisting = LockFields
        .prm_ItemFilter = False
        'Display the the Item Filter screen
        .Show vbModal
        'Retrieve results from the Item Filter screen
        If .rt_UserCancel = False Then
            Me.txtSelected.Text = .rt_AllFilters
            Me.txtNewFilters.Text = .rt_NewFilters
            'Retrieve updated set of all criteria
            g_CXArr1ToXArr2 .rt_xaAllFilters, m_xaAllCriteria
            'Retrieve set of criteria that are additional to the saved set
            g_CXArr1ToXArr2 .rt_xaExtraFilters, m_xaNewCriteria
        End If
    End With
    
    'Destroy the object
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    
    g_PrintCriteria m_xaOrigCriteria
    g_PrintCriteria m_xaAllCriteria
    g_PrintCriteria m_xaNewCriteria
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    ShowFilters.CleanUpObjects
    Set ShowFilters = Nothing
    'Err.Raise ErrNumber, ErrSource & "{DisplayFilters}", ErrDesc
     f_HandleErr , , , "AIM_ForecastSetup::DisplayFilters", Now, gDRGeneralError, True, Err
End Function

