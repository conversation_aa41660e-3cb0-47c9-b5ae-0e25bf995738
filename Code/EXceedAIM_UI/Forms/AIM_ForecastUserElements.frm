VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ForecastUserElements 
   Caption         =   "SSA DR User Elements"
   ClientHeight    =   3060
   ClientLeft      =   2040
   ClientTop       =   1800
   ClientWidth     =   6825
   LinkTopic       =   "Form1"
   ScaleHeight     =   3060
   ScaleWidth      =   6825
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   240
      Top             =   2430
      _ExtentX        =   582
      _ExtentY        =   582
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   15
      Style           =   0
      Tools           =   "AIM_ForecastUserElements.frx":0000
      ToolBars        =   "AIM_ForecastUserElements.frx":BE69
   End
   Begin VB.Frame Frame1 
      Height          =   2610
      Left            =   60
      TabIndex        =   0
      Top             =   30
      Width           =   6705
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dgFcsttype 
         Height          =   375
         Left            =   2190
         TabIndex        =   7
         Top             =   540
         Width           =   1575
         DataFieldList   =   "Fcst Type"
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   2778
         _ExtentY        =   661
         _StockProps     =   93
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Fcst Type"
      End
      Begin TDBText6Ctl.TDBText txtUserElementId 
         Height          =   345
         Left            =   2190
         TabIndex        =   2
         Top             =   930
         Width           =   1575
         _Version        =   65536
         _ExtentX        =   2778
         _ExtentY        =   609
         Caption         =   "AIM_ForecastUserElements.frx":BF4D
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastUserElements.frx":BFB9
         Key             =   "AIM_ForecastUserElements.frx":BFD7
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtUserElementDesc 
         Height          =   1095
         Left            =   2190
         TabIndex        =   4
         Top             =   1290
         Width           =   4335
         _Version        =   65536
         _ExtentX        =   7646
         _ExtentY        =   1931
         Caption         =   "AIM_ForecastUserElements.frx":C01B
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastUserElements.frx":C087
         Key             =   "AIM_ForecastUserElements.frx":C0A5
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtFcstId 
         Height          =   345
         Left            =   2190
         TabIndex        =   6
         Top             =   180
         Width           =   1575
         _Version        =   65536
         _ExtentX        =   2778
         _ExtentY        =   609
         Caption         =   "AIM_ForecastUserElements.frx":C0E9
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastUserElements.frx":C155
         Key             =   "AIM_ForecastUserElements.frx":C173
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label4 
         Caption         =   "Fcst Type"
         Height          =   240
         Left            =   60
         TabIndex        =   8
         Top             =   600
         Width           =   2115
      End
      Begin VB.Label Label3 
         Caption         =   "Fcst ID"
         Height          =   240
         Left            =   60
         TabIndex        =   5
         Top             =   270
         Width           =   2115
      End
      Begin VB.Label Label2 
         Caption         =   "User Element Description"
         Height          =   240
         Left            =   60
         TabIndex        =   3
         Top             =   1290
         Width           =   2115
      End
      Begin VB.Label Label1 
         Caption         =   "User Element ID"
         Height          =   240
         Left            =   60
         TabIndex        =   1
         Top             =   960
         Width           =   2115
      End
   End
End
Attribute VB_Name = "AIM_ForecastUserElements"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
 '*****************************************************************************
' Copyright (c) 2004 SSA Global. All rights reserved.
'*****************************************************************************
'
'   AIM_DmdPlan_UserElements.frm
'
'   Version Number - 1.0
'   Last Updated   - 2004/01/01
'   Updated By     - Annalakshmi Stocksdale
'
'   This replaces the former Copy Forecast screen in
'       allowing creation and modification of forecast data snapshots.
'
'   The "forecast generator/modification" process is being morphed
'   into demand planning, hence the options required for forecast generator
'   are being phased out, and additional options for demand planning
'   are to be created, as of version 4.5
'   See related updates to AIM_ForecastModification.
'
'*****************************************************************************
' This file contains trade secrets of SSA Global. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of SSA Global.
'*****************************************************************************
Option Explicit
Option Base 1   'Sets default for all arrays on this page to start at 1

Dim m_Cn As ADODB.Connection
Public m_FcstId As String
Dim rsGetFcstTypes As New ADODB.Recordset
Dim CmdSaveUserElement As New ADODB.Command

Private Sub dgUserElements_InitColumnProps()

End Sub
Private Function GetFcstTypes() As Boolean
On Error GoTo ErrorHandler
Dim SqlStmt As String
Dim m_SysFcst As Boolean
Dim m_MasterFcstAdj As Boolean
Dim m_FcstAdj As Boolean
Dim m_NetReq As Boolean
Dim m_AdjNetReq As Boolean
Dim m_ProjInv As Boolean
Dim m_HistDmd As Boolean
Dim m_ProdConst As Boolean
GetFcstTypes = False

SqlStmt = "Select Calc_SysFcst,Calc_NetReq,Calc_HistDmd,Calc_MasterFcstAdj," & _
"Calc_FcstAdj , Calc_AdjNetReq, Calc_ProjInv, Calc_ProdConst" & _
" From AIMFcstSetUp " & _
"Where FcstId = N'" & m_FcstId & "'"
rsGetFcstTypes.Open SqlStmt, m_Cn, adOpenStatic, adLockReadOnly, adCmdText


If f_IsRecordsetOpenAndPopulated(rsGetFcstTypes) Then
    If rsGetFcstTypes("Calc_SysFcst").Value = True Then
    dgFcsttype.AddItem "SYSFCST" & vbTab
    End If
    If rsGetFcstTypes("Calc_NetReq").Value = True Then
    dgFcsttype.AddItem "NETREQ" & vbTab
    End If
    If rsGetFcstTypes("Calc_HistDmd").Value = True Then
    dgFcsttype.AddItem "HISDMD" & vbTab
    End If
    If rsGetFcstTypes("Calc_MasterFcstAdj").Value = True Then
    dgFcsttype.AddItem "MASTERFCSTADJ" & vbTab
    End If
    If rsGetFcstTypes("Calc_FcstAdj").Value = True Then
    dgFcsttype.AddItem "FCSTADJ" & vbTab
    End If
    If rsGetFcstTypes("Calc_AdjNetReq").Value = True Then
    dgFcsttype.AddItem "ADJNETREQ" & vbTab
    End If
    If rsGetFcstTypes("Calc_ProjINv").Value = True Then
    dgFcsttype.AddItem "PROJINV" & vbTab
    End If
    If rsGetFcstTypes("Calc_ProdConst").Value = True Then
    dgFcsttype.AddItem "PRODCONST" & vbTab
    End If
    GetFcstTypes = True
Else
    GetFcstTypes = False
End If
rsGetFcstTypes.Close

Exit Function
ErrorHandler:
    rsGetFcstTypes.Close
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DmdPlan_UserElements.GetFcstTypes)"
    f_HandleErr , , , "AIM_ForecastUserElements::GetFcstTypes", Now, gDRGeneralError, True, Err
End Function
Private Function SaveUserElement() As Boolean
On Error GoTo ErrorHandler
Dim SqlStmt As String

SaveUserElement = False

'Check If all field are populated
If txtUserElementId.Text = "" Then
MsgBox "A vaild UserElementId is required"
Exit Function
End If
If txtUserElementDesc.Text = "" Then
MsgBox "A valid description is required for the UserElement"
Exit Function
End If
If dgFcsttype.Text = "" Then
MsgBox "FcstType is a required field please select a value "
Exit Function
End If
SqlStmt = "insert into UserElement values (" & _
 "'" + Trim(txtFcstId.Text) + "'" + "," & _
 "'" + Trim(txtUserElementId.Text) + "'" + "," & _
 "'" + Trim(txtUserElementDesc.Text) + "'" + "," & _
 "'" + dgFcsttype.Text + "'" + ",0)"
CmdSaveUserElement.ActiveConnection = m_Cn
CmdSaveUserElement.CommandText = SqlStmt
CmdSaveUserElement.CommandType = adCmdText
CmdSaveUserElement.Execute

'Once the user element is saved clear the fields
ClearFields
SaveUserElement = True
Exit Function
ErrorHandler:
SaveUserElement = False
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DmdPlan_UserElements.SaveUserElement)"
    f_HandleErr , , , "AIM_ForecastUserElements::AIM_DmdPlan_UserElements.SaveUserElement", Now, gDRGeneralError, True, Err
End Function
Private Function ClearFields() As Boolean
On Error GoTo ErrorHandler

txtUserElementId.Text = ""
txtUserElementDesc.Text = ""
dgFcsttype.Text = ""
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DmdPlan_UserElements.ClearFileds)"
    f_HandleErr , , , "AIM_ForecastUserElements::ClearFileds", Now, gDRGeneralError, True, Err
End Function

Private Sub dgFcsttype_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    dgFcsttype.Redraw = False  'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbHourglass

    dgFcsttype.Columns(0).Caption = getTranslationResource("Fcst Type")
    dgFcsttype.Columns(0).Width = 1500
    dgFcsttype.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    dgFcsttype.Columns(0).Alignment = ssCaptionAlignmentLeft
    dgFcsttype.Columns(0).DataField = "Fcst Type"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgFcsttype, ACW_EXPAND
    End If
    dgFcsttype.Redraw = True   'Process is done. Repaint control.
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    dgFcsttype.Redraw = True
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgFcsttype_InitColumnProps)"
     f_HandleErr , , , "AIM_ForecastUserElements::dgFcsttype_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Long
    Dim strMessage As String
    Dim strMessage1 As String
    Dim IndexCounter As Long
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    
'    strMessage = getTranslationResource("STATMSG06900")
'    If StrComp(strMessage, "STATMSG06900") = 0 Then strMessage = "Initializing Forecast User Element copy..."""
'    Write_Message strMessage
    
    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set m_Cn = New ADODB.Connection
    RtnCode = SQLConnection(m_Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    Me.txtFcstId = m_FcstId
    
    GetFcstTypes
    'Windup
    Write_Message ""
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_ForecastUserElements::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
    Dim RtnCode As Long
    
'>>>
    'Save pending updates, first
    '...
    
    'Destroy objects
    
    'Destroy connection and object
    RtnCode = SQLConnection(m_Cn, CONNECTION_CLOSE, False)
    Set m_Cn = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_ForecastUserElements::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim p_ToolID As String
    Dim BkMark As Variant
    Dim FreezeKey As Integer
    Dim FreezeType As Integer
    Dim LcId As String
    Dim FreezeLimit As String

    Select Case UCase$(Tool.ID)
    Case "ID_CLOSE"
        '>>> Check for unsaved records
        Unload Me
        Exit Sub

'    Case "ID_ADDNEW"
'        Me.txtFcstStoreID.Text = ""
'        Me.txtFcstComments_Orig.Text = p_FcstComments_Orig
'        Me.txtFcstComments_Element.Text = ""
'        Me.tbNavigation.Tools.Item("ID_Save").Enabled = True
'        Me.tbNavigation.Tools.Item("ID_Cancel").Enabled = True
'        Me.tbNavigation.Tools.Item("ID_Delete").Enabled = False
'
    Case "ID_CANCEL"
        ClearFields
'        RtnCode = mFcstUserElements_Fetch
'        Me.tbNavigation.Tools.Item("ID_Save").Enabled = False
'        Me.tbNavigation.Tools.Item("ID_Cancel").Enabled = False
'        Me.tbNavigation.Tools.Item("ID_Delete").Enabled = True
'
    Case "ID_SAVE"
        SaveUserElement
'        RtnCode = mFcstUserElements_Save  'la here
'        If RtnCode > 0 Then
'            RtnCode = mFcstUserElements_Fetch
'        End If
'        Me.tbNavigation.Tools.Item("ID_Save").Enabled = False
'        Me.tbNavigation.Tools.Item("ID_Cancel").Enabled = False
'        Me.tbNavigation.Tools.Item("ID_Delete").Enabled = True
'
'    Case "ID_DELETE"
'        RtnCode = mFcstUserElements_Delete
'        If RtnCode > 0 Then
'            RtnCode = mFcstUserElements_Fetch
'        End If
'        Me.tbNavigation.Tools.Item("ID_Save").Enabled = False
'        Me.tbNavigation.Tools.Item("ID_Cancel").Enabled = False
'        Me.tbNavigation.Tools.Item("ID_Delete").Enabled = True

    End Select

    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_ForecastUserElements::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err

End Sub




