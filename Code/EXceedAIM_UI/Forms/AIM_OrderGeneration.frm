VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Begin VB.Form AIM_OrderGeneration 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Order Generation"
   ClientHeight    =   3075
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   4905
   Icon            =   "AIM_OrderGeneration.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3075
   ScaleWidth      =   4905
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.Frame Frame1 
      Height          =   2415
      Left            =   60
      TabIndex        =   6
      Top             =   60
      Width           =   4755
      Begin VB.CommandButton cmdLookUp 
         Height          =   310
         Left            =   4200
         Picture         =   "AIM_OrderGeneration.frx":030A
         Style           =   1  'Graphical
         TabIndex        =   1
         ToolTipText     =   "Look Up Vendor / Assortment"
         Top             =   257
         Width           =   310
      End
      Begin TDBText6Ctl.TDBText txtResults 
         Height          =   1095
         Left            =   195
         TabIndex        =   3
         TabStop         =   0   'False
         Top             =   1050
         Width           =   4365
         _Version        =   65536
         _ExtentX        =   7699
         _ExtentY        =   1931
         Caption         =   "AIM_OrderGeneration.frx":0454
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_OrderGeneration.frx":04C0
         Key             =   "AIM_OrderGeneration.frx":04DE
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   345
         Left            =   2750
         TabIndex        =   0
         Top             =   240
         Width           =   1335
         _Version        =   65536
         _ExtentX        =   2355
         _ExtentY        =   609
         Caption         =   "AIM_OrderGeneration.frx":0522
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_OrderGeneration.frx":058E
         Key             =   "AIM_OrderGeneration.frx":05AC
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   345
         Left            =   2750
         TabIndex        =   2
         Top             =   600
         Width           =   1335
         _Version        =   65536
         _ExtentX        =   2350
         _ExtentY        =   600
         Caption         =   "AIM_OrderGeneration.frx":05F0
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_OrderGeneration.frx":065C
         Key             =   "AIM_OrderGeneration.frx":067A
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   8
         Top             =   645
         Width           =   2450
      End
      Begin VB.Label label 
         Caption         =   "Vendor"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   7
         Top             =   285
         Width           =   2450
      End
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "E&xit"
      Height          =   345
      Left            =   1200
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   2640
      Width           =   1455
   End
   Begin VB.CommandButton cmdGenOrd 
      Caption         =   "&Generate Orders"
      Default         =   -1  'True
      Height          =   345
      Left            =   2760
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   2640
      Width           =   2055
   End
End
Attribute VB_Name = "AIM_OrderGeneration"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
    f_HandleErr , , , "AIM_OrderGeneration::Form_Unload", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdGenOrd_Click()
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    'Housekeeping
    Me.MousePointer = vbHourglass
    Me.cmdCancel.Enabled = False    'sujit09262003  Wont allow user to Exit when in proc--Disabled EXIT
    strMessage = getTranslationResource("STATMSG03700")
    If StrComp(strMessage, "STATMSG03700") = 0 Then strMessage = "Generating Orders..."
    Write_Message strMessage
    
    'Execute Order Generation
     ExecOrderGen
    
    'Apply Production Constraints, if applicable.
    InitProdConstraints
    
    'Clean Up
    Write_Message ""
    Me.cmdCancel.Enabled = True    'sujit09262003  Wont allow user to Exit when in proc--Enabled EXIT
    Me.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_OrderGeneration::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    AIM_VendorLookUp.VnIdKey = Me.txtVnId.Text
    AIM_VendorLookUp.AssortKey = Me.txtAssort.Text
    Set AIM_VendorLookUp.Cn = Cn
    AIM_VendorLookUp.Show vbModal
    
    If Not AIM_VendorLookUp.CancelFlag Then
        Me.txtVnId.Text = AIM_VendorLookUp.VnIdKey
        Me.txtAssort.Text = AIM_VendorLookUp.AssortKey
        
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_OrderGeneration::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_OrderGeneration::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim AccessLevel As Integer
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03701")
    If StrComp(strMessage, "STATMSG03701") = 0 Then strMessage = "Initializing Order Generation..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    AccessLevel = VerifyAccess(gUserID, AF_AD_HOC_ORDER_GENERATION, "")
    If AccessLevel = g_ACCESS_READ Then
        cmdGenOrd.Enabled = False
     Else
        cmdGenOrd.Enabled = True
    End If
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption
     f_HandleErr , , , "AIM_OrderGeneration::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_OrderGeneration::Form_UnLoad", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function ExecOrderGen()
On Error GoTo ErrorHandler

    Dim AIM_OrdGenCtrl_Sp As ADODB.Command
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Initialize the Order Generation Record Set
    Set AIM_OrdGenCtrl_Sp = New ADODB.Command
    With AIM_OrdGenCtrl_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandTimeout = 0
        .CommandText = "AIM_OrdGenCtrl_Sp"
        .Parameters.Refresh
    If Me.txtVnId.Text = "*" And Me.txtAssort.Text = "*" Then
        'If both are splats then run for all items
        'Execute Order Generation
        .Parameters("@FilterOpt").Value = "A"
        .Parameters("@RevCycleKey").Value = getTranslationResource("All")
        .Parameters("@VnIdKey").Value = getTranslationResource("All")
        .Parameters("@AssortKey").Value = getTranslationResource("All")
        .Parameters("@ClearPO").Value = "N"
        .Parameters("@LT_Flag").Value = "N"
        .Parameters("@PE_Flag").Value = "N"
        .Parameters("@OutPutOpt").Value = "Y"
        .Parameters("@ReviewTotal").Value = 0
        .Parameters("@LT_Total").Value = 0
        .Parameters("@RC_Total").Value = 0
        .Parameters("@PE_Total").Value = 0
        
    Else
    
        'Execute Order Generation
        .Parameters("@FilterOpt").Value = "V"
        .Parameters("@RevCycleKey").Value = getTranslationResource("All")
        .Parameters("@VnIdKey").Value = Me.txtVnId.Text
        .Parameters("@AssortKey").Value = Me.txtAssort.Text
        .Parameters("@ClearPO").Value = "N"
        .Parameters("@LT_Flag").Value = "N"
        .Parameters("@PE_Flag").Value = "N"
        .Parameters("@OutPutOpt").Value = "Y"
        .Parameters("@ReviewTotal").Value = 0
        .Parameters("@LT_Total").Value = 0
        .Parameters("@RC_Total").Value = 0
        .Parameters("@PE_Total").Value = 0
     End If
        .Execute
        
        If .Parameters(0).Value = SUCCEED Then
            strMessage = getTranslationResource("TEXTMSG03701")
            If StrComp(strMessage, "TEXTMSG03701") = 0 Then strMessage = "Items Reviewed:"
            strMessage = strMessage + " " + Format(.Parameters("@ReviewTotal").Value, "0")
            strMessage = strMessage + vbCrLf + vbCrLf
            strMessage1 = getTranslationResource("TEXTMSG03702")
            If StrComp(strMessage1, "TEXTMSG03702") = 0 Then strMessage1 = "Line Point Reviews:"
            strMessage = strMessage + strMessage1 + " "
            strMessage = strMessage + Format(.Parameters("@RC_Total").Value, "0")
            
            Me.txtResults = strMessage + vbCrLf
        
        End If
        
    End With
    
    If Not (AIM_OrdGenCtrl_Sp Is Nothing) Then Set AIM_OrdGenCtrl_Sp.ActiveConnection = Nothing
    Set AIM_OrdGenCtrl_Sp = Nothing
    
Exit Function
ErrorHandler:
    strMessage = getTranslationResource("TEXTMSG03700")
    If StrComp(strMessage, "TEXTMSG03700") = 0 Then strMessage = "Error(s) encountered during Order Generation."
    strMessage = strMessage + vbCrLf + vbCrLf + Err.Description
    
    Me.txtResults = strMessage + vbCrLf
    Err.Clear
    If Not (AIM_OrdGenCtrl_Sp Is Nothing) Then Set AIM_OrdGenCtrl_Sp.ActiveConnection = Nothing
    Set AIM_OrdGenCtrl_Sp = Nothing
End Function

Private Function InitProdConstraints()
On Error GoTo ErrorHandler

    Dim rsSysCtrl As ADODB.Recordset
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim ProductionConstraint As String
    
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
    End With
    
    'Get the System Control Record
    Set rsSysCtrl = New ADODB.Recordset
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    ProductionConstraint = rsSysCtrl("ProductionConstraint").Value

    'Apply Production constraint if it is enabled in the SysCtrl table
    If UCase(ProductionConstraint) = "Y" Then
        'Call the Production Constraint Routine
        If Me.txtVnId.Text = "*" And Me.txtAssort.Text = "*" Then
            'If both are splats then run for all items
            Prod_Constraint_SizingCtrl "ALL", Cn
        Else
            Prod_Constraint_Sizing Me.txtVnId.Text, Me.txtAssort.Text, Cn
        End If
    End If
    
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(InitProdConstraints)"
    f_HandleErr , , , "AIM_OrderGeneration::InitProdConstraints", Now, gDRGeneralError, True, Err
End Function
