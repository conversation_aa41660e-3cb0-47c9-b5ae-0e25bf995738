VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_SuggestedReviewCycles 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Suggested Review Cycles"
   ClientHeight    =   8295
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   12780
   Icon            =   "AIM_SuggestedReviewCycles.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   8295
   ScaleWidth      =   12780
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Frame3 
      Caption         =   "Base Annual Demand on"
      Height          =   1515
      Left            =   6090
      TabIndex        =   11
      Top             =   30
      Width           =   6525
      Begin VB.OptionButton ckDemandOption 
         Caption         =   "Forecast for the next year"
         Height          =   300
         Index           =   2
         Left            =   225
         TabIndex        =   5
         Tag             =   "4"
         Top             =   1060
         Width           =   3825
      End
      Begin VB.OptionButton ckDemandOption 
         Caption         =   "Forecast for the next quarter"
         Height          =   300
         Index           =   1
         Left            =   225
         TabIndex        =   4
         Tag             =   "2"
         Top             =   680
         Width           =   3825
      End
      Begin VB.OptionButton ckDemandOption 
         Caption         =   "Forecast for the next month"
         Height          =   300
         Index           =   0
         Left            =   225
         TabIndex        =   3
         Tag             =   "1"
         Top             =   300
         Value           =   -1  'True
         Width           =   3825
      End
   End
   Begin ActiveToolBars.SSActiveToolBars atSuggestedReviewCycles 
      Left            =   120
      Top             =   7920
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Style           =   0
      Tools           =   "AIM_SuggestedReviewCycles.frx":030A
      ToolBars        =   "AIM_SuggestedReviewCycles.frx":1CB3
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSuggestedRevCycles 
      Height          =   6135
      Left            =   0
      TabIndex        =   6
      Top             =   1680
      Width           =   12690
      _Version        =   196617
      DataMode        =   1
      Cols            =   4
      AllowUpdate     =   0   'False
      AllowColumnSwapping=   0
      SelectTypeCol   =   0
      SelectTypeRow   =   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      SplitterPos     =   1
      SplitterVisible =   -1  'True
      Columns(0).Width=   3200
      _ExtentX        =   22384
      _ExtentY        =   10821
      _StockProps     =   79
      Caption         =   "Suggested Review Cycles"
   End
   Begin VB.Frame Frame1 
      Caption         =   "Selection Criteria"
      Height          =   1515
      Left            =   45
      TabIndex        =   7
      Top             =   30
      Width           =   5925
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
         Bindings        =   "AIM_SuggestedReviewCycles.frx":1D82
         Height          =   345
         Left            =   2750
         TabIndex        =   0
         Top             =   210
         Width           =   1710
         DataFieldList   =   "Userid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserId"
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   340
         Left            =   2750
         TabIndex        =   1
         Top             =   570
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_SuggestedReviewCycles.frx":1D9B
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SuggestedReviewCycles.frx":1E07
         Key             =   "AIM_SuggestedReviewCycles.frx":1E25
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   340
         Left            =   2750
         TabIndex        =   2
         Top             =   930
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_SuggestedReviewCycles.frx":1E69
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SuggestedReviewCycles.frx":1ED5
         Key             =   "AIM_SuggestedReviewCycles.frx":1EF3
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Buyer ID"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   10
         Top             =   255
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   2
         Left            =   200
         TabIndex        =   9
         Top             =   970
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Vendor ID"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   8
         Top             =   610
         Width           =   2450
      End
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_SuggestedReviewCycles"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsReviewCycles As ADODB.Recordset

Private Sub atSuggestedReviewCycles_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim strMessage As String
    
    Select Case Tool.ID
    Case "ID_CalcCycles"
        Screen.MousePointer = vbHourglass
        strMessage = getTranslationResource("STATMSG05400")
        If StrComp(strMessage, "STATMSG05400") = 0 Then strMessage = "Building Suggested Review Cycles ... "
        Write_Message strMessage
    
        'Build the SQL Statment
        strSQL = BldSQL(Me.dcAIMUsers.Text, Me.txtVnId, Me.txtAssort)
        
        If f_IsRecordsetValidAndOpen(rsReviewCycles) Then rsReviewCycles.Close
        rsReviewCycles.Open strSQL, Cn, adOpenStatic, adLockReadOnly
        
        'Bind the Grid
        Me.dgSuggestedRevCycles.ReBind
        If f_IsRecordsetOpenAndPopulated(rsReviewCycles) Then
            dgSuggestedRevCycles.Rows = rsReviewCycles.RecordCount
            mnuEdit.Enabled = True
        Else
            mnuEdit.Enabled = False
        End If
        
        Write_Message ""
        Screen.MousePointer = vbNormal
    
    Case "ID_Close"
        Unload Me
    
    End Select

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        Resume Next
    Else
        'f_HandleErr Me.Caption & "(atSuggestedReviewCycles_ToolClick)"
         f_HandleErr , , , "AIM_SuggestedReviewCycles:::atSuggestedReviewCycles_ToolClick", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub ckDemandOption_Click(Index As Integer)
On Error GoTo ErrorHandler

    Me.dgSuggestedRevCycles.ReBind
    If f_IsRecordsetOpenAndPopulated(rsReviewCycles) Then dgSuggestedRevCycles.Rows = rsReviewCycles.RecordCount
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckDemandOption_Click)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::atSuggestedReviewCycles_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "userid"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "userid"
    
    Me.dcAIMUsers.Columns(1).Name = "username"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = "username"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSuggestedRevCycles_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long

    Me.dgSuggestedRevCycles.Columns(0).Name = "ById"
    Me.dgSuggestedRevCycles.Columns(0).Caption = "Buyer ID"
    Me.dgSuggestedRevCycles.Columns(0).Width = 1000
    Me.dgSuggestedRevCycles.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(0).Alignment = ssCaptionAlignmentLeft
    
    Me.dgSuggestedRevCycles.Columns(1).Name = "VnId"
    Me.dgSuggestedRevCycles.Columns(1).Caption = "Vendor ID"
    Me.dgSuggestedRevCycles.Columns(1).Width = 1000
    Me.dgSuggestedRevCycles.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(1).Alignment = ssCaptionAlignmentLeft
    
    Me.dgSuggestedRevCycles.Columns(2).Name = "Assort"
    Me.dgSuggestedRevCycles.Columns(2).Caption = "Assortment"
    Me.dgSuggestedRevCycles.Columns(2).Width = 1000
    Me.dgSuggestedRevCycles.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(2).Alignment = ssCaptionAlignmentLeft

    Me.dgSuggestedRevCycles.Columns(3).Name = "VName"
    Me.dgSuggestedRevCycles.Columns(3).Caption = "Vendor Name"
    Me.dgSuggestedRevCycles.Columns(3).Width = 2000
    Me.dgSuggestedRevCycles.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(3).Alignment = ssCaptionAlignmentLeft
    
    Me.dgSuggestedRevCycles.Columns(4).Name = "RevCycle"
    Me.dgSuggestedRevCycles.Columns(4).Caption = "Review Cycle"
    Me.dgSuggestedRevCycles.Columns(4).Width = 1000
    Me.dgSuggestedRevCycles.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(4).Alignment = ssCaptionAlignmentLeft
    
    Me.dgSuggestedRevCycles.Columns(5).Name = "ReviewTime"
    Me.dgSuggestedRevCycles.Columns(5).Caption = "RT - Days"
    Me.dgSuggestedRevCycles.Columns(5).Width = 1000
    Me.dgSuggestedRevCycles.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(5).NumberFormat = "#,##0"
    
    Me.dgSuggestedRevCycles.Columns(6).Name = "SugRT_Min"
    Me.dgSuggestedRevCycles.Columns(6).Caption = "Sug - Min"
    Me.dgSuggestedRevCycles.Columns(6).Width = 1000
    Me.dgSuggestedRevCycles.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(6).NumberFormat = "#,##0"
    
    Me.dgSuggestedRevCycles.Columns(7).Name = "SugRT_Best"
    Me.dgSuggestedRevCycles.Columns(7).Caption = "Sug - Best"
    Me.dgSuggestedRevCycles.Columns(7).Width = 1000
    Me.dgSuggestedRevCycles.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(7).NumberFormat = "#,##0"
    
    Me.dgSuggestedRevCycles.Columns(8).Name = "RevFreq"
    Me.dgSuggestedRevCycles.Columns(8).Caption = "Freq"
    Me.dgSuggestedRevCycles.Columns(8).Width = 1000
    Me.dgSuggestedRevCycles.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(8).Alignment = ssCaptionAlignmentLeft
    
    Me.dgSuggestedRevCycles.Columns(9).Name = "RevInterval"
    Me.dgSuggestedRevCycles.Columns(9).Caption = "Interval"
    Me.dgSuggestedRevCycles.Columns(9).Width = 800
    Me.dgSuggestedRevCycles.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(9).NumberFormat = "#,##0"

    Me.dgSuggestedRevCycles.Columns(10).Name = "Reach Code"
    Me.dgSuggestedRevCycles.Columns(10).Caption = "Reach On"
    Me.dgSuggestedRevCycles.Columns(10).Width = 1000
    Me.dgSuggestedRevCycles.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(10).Alignment = ssCaptionAlignmentLeft

    Me.dgSuggestedRevCycles.Columns(11).Name = "vn_min"
    Me.dgSuggestedRevCycles.Columns(11).Caption = "Vn Minimum"
    Me.dgSuggestedRevCycles.Columns(11).Width = 1000
    Me.dgSuggestedRevCycles.Columns(11).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(11).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(11).NumberFormat = "#,##0.00"

    Me.dgSuggestedRevCycles.Columns(12).Name = "vn_best"
    Me.dgSuggestedRevCycles.Columns(12).Caption = "Vn Best"
    Me.dgSuggestedRevCycles.Columns(12).Width = 1000
    Me.dgSuggestedRevCycles.Columns(12).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(12).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(12).NumberFormat = "#,##0.00"
    
    Me.dgSuggestedRevCycles.Columns(13).Name = "Units"
    Me.dgSuggestedRevCycles.Columns(13).Caption = "Annual Units"
    Me.dgSuggestedRevCycles.Columns(13).Width = 1400
    Me.dgSuggestedRevCycles.Columns(13).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(13).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(13).NumberFormat = "#,##0"
    
    Me.dgSuggestedRevCycles.Columns(14).Name = "Cost"
    Me.dgSuggestedRevCycles.Columns(14).Caption = "Annual Cost"
    Me.dgSuggestedRevCycles.Columns(14).Width = 1400
    Me.dgSuggestedRevCycles.Columns(14).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(14).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(14).NumberFormat = "#,##0"
    
    Me.dgSuggestedRevCycles.Columns(15).Name = "Weight"
    Me.dgSuggestedRevCycles.Columns(15).Caption = "Annual Weight"
    Me.dgSuggestedRevCycles.Columns(15).Width = 1400
    Me.dgSuggestedRevCycles.Columns(15).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(15).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(15).NumberFormat = "#,##0"
    
    Me.dgSuggestedRevCycles.Columns(16).Name = "Cube"
    Me.dgSuggestedRevCycles.Columns(16).Caption = "Annual Cube"
    Me.dgSuggestedRevCycles.Columns(16).Width = 1400
    Me.dgSuggestedRevCycles.Columns(16).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSuggestedRevCycles.Columns(16).Alignment = ssCaptionAlignmentRight
    Me.dgSuggestedRevCycles.Columns(16).NumberFormat = "#,##0"
    
    'Restore the grid layout
    On Error Resume Next
    Me.dgSuggestedRevCycles.LoadLayout App.Path & "\" & "AIM_SuggestedReviewCycles" & Trim(gUserID) & "_Layout.grd"
    'Check for non-existent file
    If Err.Number = 53 Then     'File Not Found
        Resume Next
    ElseIf Err.Number <> 0 Then
        GoTo ErrorHandler
    End If
    On Error GoTo ErrorHandler
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgSuggestedRevCycles.Font
    SetProperFont Me.dgSuggestedRevCycles.HeadFont
    SetProperFont Me.dgSuggestedRevCycles.PageFooterFont
    SetProperFont Me.dgSuggestedRevCycles.PageHeaderFont
    Me.dgSuggestedRevCycles.Columns(0).Caption = getTranslationResource("Buyer ID")
    Me.dgSuggestedRevCycles.Columns(1).Caption = getTranslationResource("Vendor ID")
    Me.dgSuggestedRevCycles.Columns(2).Caption = getTranslationResource("Assortment")
    Me.dgSuggestedRevCycles.Columns(3).Caption = getTranslationResource("Vendor Name")
    Me.dgSuggestedRevCycles.Columns(4).Caption = getTranslationResource("Review Cycle")
    Me.dgSuggestedRevCycles.Columns(5).Caption = getTranslationResource("RT - Days")
    Me.dgSuggestedRevCycles.Columns(6).Caption = getTranslationResource("Sug - Min")
    Me.dgSuggestedRevCycles.Columns(7).Caption = getTranslationResource("Sug - Best")
    Me.dgSuggestedRevCycles.Columns(8).Caption = getTranslationResource("Freq")
    Me.dgSuggestedRevCycles.Columns(9).Caption = getTranslationResource("Interval")
    Me.dgSuggestedRevCycles.Columns(10).Caption = getTranslationResource("Reach On")
    Me.dgSuggestedRevCycles.Columns(11).Caption = getTranslationResource("Vn Minimum")
    Me.dgSuggestedRevCycles.Columns(12).Caption = getTranslationResource("Vn Best")
    Me.dgSuggestedRevCycles.Columns(13).Caption = getTranslationResource("Annual Units")
    Me.dgSuggestedRevCycles.Columns(14).Caption = getTranslationResource("Annual Cost")
    Me.dgSuggestedRevCycles.Columns(15).Caption = getTranslationResource("Annual Weight")
    Me.dgSuggestedRevCycles.Columns(16).Caption = getTranslationResource("Annual Cube")
    Me.dgSuggestedRevCycles.Caption = getTranslationResource("Suggested Review Cycles")
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgSuggestedRevCycles, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgSuggestedRevCycles.Columns.Count - 1
        dgSuggestedRevCycles.Columns(IndexCounter).Locked = True
'        dgSuggestedRevCycles.Columns(IndexCounter).HasHeadBackColor = True
'        dgSuggestedRevCycles.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgSuggestedRevCycles.Columns(IndexCounter).Locked = False Then dgSuggestedRevCycles.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSuggestedRevCycles_InitColumnProps)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::dgSuggestedRevCycles_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSuggestedRevCycles_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSuggestedRevCycles_MouseDown)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::dgSuggestedRevCycles_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSuggestedRevCycles_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSuggestedRevCycles_PrintBegin)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::dgSuggestedRevCycles_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSuggestedRevCycles_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG05400")
    If StrComp(strMessage, "RPTMSG05400") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG05401")
    If StrComp(strMessage1, "RPTMSG05401") = 0 Then strMessage = "Suggested Review Cycles Listing"
    strMessage2 = getTranslationResource("RPTMSG05402")
    If StrComp(strMessage2, "RPTMSG05402") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & strMessage1 _
                & vbTab & strMessage2 & " <page number>"
    
    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSuggestedRevCycles_PrintInitialize)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::dgSuggestedRevCycles_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSuggestedRevCycles_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    Dim Units As Double
    Dim Cost As Double
    Dim Weight As Double
    Dim Cube As Double
    
    Dim MinDays As Long
    Dim BestDays As Long
    
    Dim Vn_Min As Double
    Dim Vn_Best As Double
    
    If Not f_IsRecordsetOpenAndPopulated(rsReviewCycles) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsReviewCycles.MoveLast
        Else
            rsReviewCycles.MoveFirst
        End If
    
    Else
        rsReviewCycles.Bookmark = StartLocation
        If ReadPriorRows Then
            rsReviewCycles.MovePrevious
        Else
            rsReviewCycles.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
    
        If rsReviewCycles.BOF Or rsReviewCycles.eof Then Exit For
        
        'Determine annualized amounts
        If Me.ckDemandOption(0).Value = True Then
            'Month
            Units = rsReviewCycles("units_month").Value
            Cost = rsReviewCycles("amount_month").Value
            Weight = rsReviewCycles("weight_month").Value
            Cube = rsReviewCycles("cube_month").Value
        ElseIf Me.ckDemandOption(1).Value = True Then
            'Quarter
            Units = rsReviewCycles("units_quarter").Value
            Cost = rsReviewCycles("amount_quarter").Value
            Weight = rsReviewCycles("weight_quarter").Value
            Cube = rsReviewCycles("cube_quarter").Value
        ElseIf Me.ckDemandOption(2).Value = True Then
            'Annual
            Units = rsReviewCycles("units_year").Value
            Cost = rsReviewCycles("amount_year").Value
            Weight = rsReviewCycles("weight_year").Value
            Cube = rsReviewCycles("cube_year").Value
        End If
        
        'Calculate days to meet minimum/best buy
        Vn_Min = rsReviewCycles("vn_min").Value
        Vn_Best = rsReviewCycles("vn_best").Value
        
        'Initialize days
        MinDays = 1
        BestDays = 1
        
        Select Case rsReviewCycles("reach_code").Value
        Case "C"
            If Cube > 0 Then
                MinDays = Round((365 * Vn_Min) / Cube, 0)
                BestDays = Round((365 * Vn_Best) / Cube, 0)
            End If
            
        Case "D"
            If Cost > 0 Then
                MinDays = Round((365 * Vn_Min) / Cost, 0)
                BestDays = Round((365 * Vn_Best) / Cost, 0)
            End If
            
        Case "N"
            MinDays = 1
            BestDays = 1
            
        Case "U"
            If Units > 0 Then
                MinDays = Round((365 * Vn_Min) / Units, 0)
                BestDays = Round((365 * Vn_Best) / Units, 0)
            End If
            
        Case "W"
            If Weight > 0 Then
                MinDays = Round((365 * Vn_Min) / Weight, 0)
                BestDays = Round((365 * Vn_Best) / Weight, 0)
            End If
            
        Case Else
            MinDays = 1
            BestDays = 1
        
        End Select
        
        'Days cannot be less than 1
        MinDays = IIf(MinDays < 1, 1, MinDays)
        BestDays = IIf(BestDays < 1, 1, BestDays)
        
        'Days cannot exceed 90
        MinDays = IIf(MinDays > 90, 90, MinDays)
        BestDays = IIf(BestDays > 90, 90, BestDays)
    
        RowBuf.Value(i, 0) = rsReviewCycles("byid").Value
        RowBuf.Value(i, 1) = rsReviewCycles("vnid").Value
        RowBuf.Value(i, 2) = rsReviewCycles("assort").Value
        RowBuf.Value(i, 3) = rsReviewCycles("vname").Value
        RowBuf.Value(i, 4) = rsReviewCycles("revcycle").Value
        RowBuf.Value(i, 5) = rsReviewCycles("reviewtime").Value
        RowBuf.Value(i, 6) = MinDays
        RowBuf.Value(i, 7) = BestDays
        
        Select Case rsReviewCycles("revfreq").Value
        Case 0
            RowBuf.Value(i, 8) = getTranslationResource("Dynamic")
        Case 1
            RowBuf.Value(i, 8) = getTranslationResource("Daily")
        Case 2
            RowBuf.Value(i, 8) = getTranslationResource("Weekly")
        Case 3
            RowBuf.Value(i, 8) = getTranslationResource("Monthly")
        Case 4
            RowBuf.Value(i, 8) = getTranslationResource("Week of Month")
        Case 5
            RowBuf.Value(i, 8) = getTranslationResource("Seasonal")
        End Select
        
        RowBuf.Value(i, 9) = rsReviewCycles("revinterval").Value
        
        Select Case rsReviewCycles("reach_code").Value
        Case "C"
            RowBuf.Value(i, 10) = getTranslationResource("Cube")
        Case "D"
             RowBuf.Value(i, 10) = getTranslationResource("Cost")
        Case "N"
            RowBuf.Value(i, 10) = getTranslationResource("None")
        Case "U"
            RowBuf.Value(i, 10) = getTranslationResource("Units")
        Case "W"
            RowBuf.Value(i, 10) = getTranslationResource("Weight")
        Case Else
            RowBuf.Value(i, 10) = getTranslationResource("Unknown")
        End Select
        
        RowBuf.Value(i, 11) = rsReviewCycles("vn_min").Value
        RowBuf.Value(i, 12) = rsReviewCycles("vn_best").Value
        RowBuf.Value(i, 13) = Units
        RowBuf.Value(i, 14) = Cost
        RowBuf.Value(i, 15) = Weight
        RowBuf.Value(i, 16) = Cube
        RowBuf.Bookmark(i) = rsReviewCycles.Bookmark
    
        If ReadPriorRows Then
            rsReviewCycles.MovePrevious
        Else
            rsReviewCycles.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSuggestedRevCycles_UnboundReadData)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::dgSuggestedRevCycles_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Function BldSQL(ById As String, VnId As String, Assort As String)
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim WhrStmt As String
    
    'Set the column list and table joins
    strSQL = "SELECT Item.ById, Item.VnId, Item.Assort, AIMVendors.VName,"
    strSQL = strSQL & vbCrLf & "AIMVendors.RevCycle, RevCycles.RevFreq,"
    strSQL = strSQL & vbCrLf & "RevCycles.RevInterval, RevCycles.ReviewTime,"
    strSQL = strSQL & vbCrLf & "AIMVendors.Vn_Min, AIMVendors.Vn_Best,"
    strSQL = strSQL & vbCrLf & "AIMVendors.Reach_Code,"
    strSQL = strSQL & vbCrLf & "Units_Month = SUM(Item.Fcst_Month) * 12,"
    strSQL = strSQL & vbCrLf & "Units_Quarter = SUM(Item.Fcst_Quarter) * 4,"
    strSQL = strSQL & vbCrLf & "Units_Year = SUM(Item.Fcst_Year),"
    strSQL = strSQL & vbCrLf & "Weight_Month = SUM(item.fcst_month * Item.weight) * 12,"
    strSQL = strSQL & vbCrLf & "Weight_Quarter = SUM(Item.Fcst_Quarter * Item.Weight) * 4,"
    strSQL = strSQL & vbCrLf & "Weight_Year = SUM(Item.Fcst_Year * Item.Weight),"
    strSQL = strSQL & vbCrLf & "Cube_Month = SUM(Item.Fcst_Month * Item.Cube) * 12,"
    strSQL = strSQL & vbCrLf & "Cube_Quarter = SUM(Item.Fcst_Quarter * Item.Cube) * 4,"
    strSQL = strSQL & vbCrLf & "Cube_Year = SUM(Item.Fcst_Year * Item.Cube),"
    strSQL = strSQL & vbCrLf & "Amount_Month = SUM(Item.Fcst_Month * Item.Cost) * 12,"
    strSQL = strSQL & vbCrLf & "Amount_Quarter = SUM(Item.Fcst_Quarter * Item.Cost) * 4,"
    strSQL = strSQL & vbCrLf & "Amount_Year = SUM(Item.Fcst_Year * Item.cost)"
    strSQL = strSQL & vbCrLf & "FROM Item"
    strSQL = strSQL & vbCrLf & "INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId" & _
                               " AND Item.Assort = AIMVendors.Assort"
    strSQL = strSQL & vbCrLf & "INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat"
    strSQL = strSQL & vbCrLf & "INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle"
    
    'Build the Where Clause
    WhrStmt = "WHERE ItStatus.Ordgen = N'Y' "
    If ById <> getTranslationResource("All") Then WhrStmt = WhrStmt & vbCrLf & "AND Item.ById = N'" & ById & "'"
    If Trim(VnId) <> "" Then WhrStmt = WhrStmt & vbCrLf & "AND Item.VnId = N'" & VnId & "'"
    If Trim(Assort) <> "" Then WhrStmt = WhrStmt & vbCrLf & "AND Item.Assort = N'" & Assort & "'"

    'Append Where Clause
    strSQL = strSQL & vbCrLf & WhrStmt
    
    'Append Group by Clause
    strSQL = strSQL & vbCrLf & "GROUP BY Item.ById, Item.VnId, Item.Assort, AIMVendors.VName,"
    strSQL = strSQL & vbCrLf & "ItStatus.Ordgen, AIMVendors.RevCycle, RevCycles.RevFreq,"
    strSQL = strSQL & vbCrLf & "RevCycles.RevInterval, RevCycles.ReviewTime,"
    strSQL = strSQL & vbCrLf & "AIMVendors.Vn_Min, AIMVendors.Vn_Best, AIMVendors.Reach_Code"

    'Return the query
    BldSQL = strSQL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldSQL)", Err.Description
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::BldSQL", Now, gDRGeneralError, True, Err
End Function

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05401")
    If StrComp(strMessage, "STATMSG05401") = 0 Then strMessage = "Initializing Suggested Review Cycle Analysis... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build/Bind the AIM User Drop Down
    PopulateAIMUsers
    
    '*********************************************************************************
    'Initialize the Grid
    strSQL = BldSQL("xxxxxxxxxxxx", "xxxxxxxxxxxx", "xxxxxxxxxxxx")
    Set rsReviewCycles = New ADODB.Recordset
    rsReviewCycles.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dgSuggestedRevCycles.ReBind
    If f_IsRecordsetOpenAndPopulated(rsReviewCycles) Then
        dgSuggestedRevCycles.Rows = rsReviewCycles.RecordCount
        mnuEdit.Enabled = True
    Else
        mnuEdit.Enabled = False
    End If
        
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsReviewCycles) Then rsReviewCycles.Close
    Set rsReviewCycles = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Save current grid layout.
     Me.dgSuggestedRevCycles.SaveLayout App.Path & "\" & "AIM_SuggestedReviewCycles" & Trim(gUserID) & "_Layout.grd", ssSaveLayoutAll
     
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_SuggestedReviewCycles:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If f_IsRecordsetOpenAndPopulated(rsReviewCycles) Then
                Clipboard.Clear
                
                For i = 0 To rsReviewCycles.Fields.Count - 1
                    s1 = s1 & rsReviewCycles.Fields(i).Name & vbTab
                Next i
                
                s1 = s1 & vbCrLf
                    
                rsReviewCycles.MoveFirst
                s2 = rsReviewCycles.GetString(adClipString)
                
                Clipboard.SetText s1 & s2, vbCFText
            End If
            
        Case 1          'Print
            Me.dgSuggestedRevCycles.PrintData ssPrintAllRows, False, True
        
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , "AIM_SuggestedReviewCycles:::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Function PopulateAIMUsers()
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim rsAIMUsers As ADODB.Recordset

    If Me.dcAIMUsers.Rows > 0 Then Me.dcAIMUsers.RemoveAll
    
    Me.dcAIMUsers.Text = AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text
    
    strSQL = "SELECT UserID, UserName" & _
            " FROM AIMUsers" & _
            " ORDER BY UserID"
    
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dcAIMUsers.AddItem getTranslationResource("All") _
                        & vbTab & getTranslationResource("All Users")
    
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserId").Value & vbTab & rsAIMUsers("Username").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "PopulateAIMUsers"
    f_HandleErr , , , "AIM_SuggestedReviewCycles:::PopulateAIMUsers", Now, gDRGeneralError, True, Err
End Function
