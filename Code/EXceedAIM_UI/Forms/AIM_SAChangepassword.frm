VERSION 5.00
Begin VB.Form AIM_SAChangePassword 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR SA Change Password"
   ClientHeight    =   2190
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   5070
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2190
   ScaleWidth      =   5070
   Begin VB.CommandButton cmdCancel 
      Cancel          =   -1  'True
      Caption         =   "Cancel"
      Height          =   340
      Left            =   2584
      TabIndex        =   4
      Top             =   1649
      Width           =   1326
   End
   Begin VB.CommandButton cmdOK 
      Caption         =   "OK"
      Default         =   -1  'True
      Height          =   340
      Left            =   1156
      TabIndex        =   2
      Top             =   1649
      Width           =   1326
   End
   Begin VB.TextBox txtVerify 
      Height          =   340
      IMEMode         =   3  'DISABLE
      Left            =   2244
      PasswordChar    =   "*"
      TabIndex        =   1
      Top             =   1190
      Width           =   2397
   End
   Begin VB.TextBox txtNewPassword 
      Height          =   340
      IMEMode         =   3  'DISABLE
      Left            =   2244
      PasswordChar    =   "*"
      TabIndex        =   0
      Top             =   714
      Width           =   2397
   End
   Begin VB.Label lblUserName 
      Height          =   340
      Left            =   2244
      TabIndex        =   7
      Top             =   238
      Width           =   2397
   End
   Begin VB.Label Label3 
      Caption         =   "&Verify:"
      Height          =   340
      Left            =   425
      TabIndex        =   6
      Top             =   1190
      Width           =   1326
   End
   Begin VB.Label Label2 
      Caption         =   "&New Password:"
      Height          =   340
      Left            =   425
      TabIndex        =   5
      Top             =   714
      Width           =   1326
   End
   Begin VB.Label Label1 
      Caption         =   "User Name:"
      Height          =   340
      Left            =   425
      TabIndex        =   3
      Top             =   238
      Width           =   1326
   End
End
Attribute VB_Name = "AIM_SAChangePassword"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdOK_Click()
On Error GoTo ErrorHandler
    
    Dim Cn As ADODB.Connection
    Dim tempCmd As ADODB.Command
    Dim strMessage As String
    Dim RtnCode As Integer
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDesc As String
    Dim strSQL As String
    
    If Trim$(Me.txtNewPassword.Text) = "" Then
        strMessage = getTranslationResource("MSGBOX06010")
        If StrComp(strMessage, "MSGBOX06010") = 0 Then strMessage = "A blank password is not acceptable."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        Me.txtNewPassword.SetFocus
        Exit Sub
    End If
    
    If (StrComp(Trim$(Me.txtNewPassword.Text), Trim$(Me.txtVerify.Text), vbBinaryCompare)) <> 0 Then
        strMessage = getTranslationResource("MSGBOX06011")
        If StrComp(strMessage, "MSGBOX06011") = 0 Then strMessage = "The confirmation password is not correct."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        Me.txtNewPassword.SelLength = Len(Me.txtNewPassword.Text)
        Me.txtNewPassword.SetFocus
        Exit Sub
    End If
    
    Set Cn = New ADODB.Connection
    Set tempCmd = New ADODB.Command
    
    strMessage = getTranslationResource("MSGBOX06012")
    If StrComp(strMessage, "MSGBOX06012") = 0 Then strMessage = "Do you wish to proceed further?"
    RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
    If RtnCode = vbYes Then
        Cn.Provider = "SQLOleDb"
        Cn.ConnectionTimeout = 10
        Cn.CommandTimeout = 300            'Five minutes
        Cn.Properties("Data Source").Value = gServer
        Cn.Properties("Initial Catalog").Value = gDataBase
        If gWinNTAuth = "Y" Then
            Cn.Properties("Integrated Security").Value = "SSPI"
        Else
            Cn.Properties("User ID").Value = gUserID
            Cn.Properties("Password").Value = gPassWord
        End If
        Cn.Open
        
        strSQL = "EXEC sp_password NULL, N'" & _
            Replace(Trim$(Me.txtNewPassword.Text), "'", "''") & _
            "', N'" & Replace(Trim$(Me.lblUserName.Caption), "'", "''") & "'"
        With tempCmd
            If .ActiveConnection Is Nothing Then Set .ActiveConnection = Cn
            .CommandText = strSQL
            .Execute
        End With
        
        strMessage = getTranslationResource("MSGBOX06013")
        If StrComp(strMessage, "MSGBOX06013") = 0 Then strMessage = "The new password has been saved successfully."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        
        Unload Me
    Else
        Exit Sub
    End If

    Set Cn = Nothing
    If Not (tempCmd Is Nothing) Then Set tempCmd.ActiveConnection = Nothing
    Set tempCmd = Nothing

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    If Err.Number = -2147217900 Then
        'The login '<>' does not exist.
        strMessage = getTranslationResource("MSGBOX06014")
        If StrComp(strMessage, "MSGBOX06014") = 0 Then strMessage = ".. or they are a Windows-authenticated user. Contact your database administrator for further assistance."
        MsgBox ErrDesc & strMessage, vbCritical
        Unload Me
    ElseIf Err.Number = -2147217843 Then
        Set Cn = Nothing
        If Not (tempCmd Is Nothing) Then Set tempCmd.ActiveConnection = Nothing
        Set tempCmd = Nothing
        
        MsgBox ErrDesc, vbExclamation
    Else
        Set Cn = Nothing
        If Not (tempCmd Is Nothing) Then Set tempCmd.ActiveConnection = Nothing
        Set tempCmd = Nothing
        
        'f_HandleErr Me.Caption & "(cmdOK_Click)"
        f_HandleErr , , , "AIM_SAChangePassword::cmdOK_Click", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub Form_Deactivate()
    
    Me.SetFocus

End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Me.Width = 5168
    Me.Height = 2584
    Me.Left = (AIM_Main.ScaleWidth - Me.ScaleWidth) / 2
    Me.Top = (AIM_Main.ScaleHeight - Me.ScaleHeight) / 2
    Me.lblUserName.Caption = AIM_UsersMaintenance.txtUserId.Text

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SAChangePassword::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_SAChangePassword::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_LostFocus()
    
    Me.SetFocus

End Sub
