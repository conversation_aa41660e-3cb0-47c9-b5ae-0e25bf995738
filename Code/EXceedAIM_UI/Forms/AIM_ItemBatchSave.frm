VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItemBatchSave 
   Caption         =   "DR Batch Item Save"
   ClientHeight    =   8685
   ClientLeft      =   60
   ClientTop       =   450
   ClientWidth     =   14535
   LinkTopic       =   "Form1"
   ScaleHeight     =   8685
   ScaleWidth      =   14535
   StartUpPosition =   3  'Windows Default
   Begin VB.CheckBox CkKitBOMFlag 
      Alignment       =   1  'Right Justify
      Caption         =   "Kit/BOM Master Item "
      Height          =   375
      Left            =   6600
      TabIndex        =   33
      Top             =   7440
      Width           =   2925
   End
   Begin VB.CheckBox ckProduction_Constraint 
      Alignment       =   1  'Right Justify
      Caption         =   "Production Constraint"
      Height          =   255
      Left            =   6600
      TabIndex        =   30
      Top             =   6600
      Width           =   3135
   End
   Begin VB.CheckBox ckFreeze_Forecast 
      Alignment       =   1  'Right Justify
      Caption         =   "Freeze Forecast"
      Height          =   300
      Left            =   6600
      TabIndex        =   29
      Top             =   6240
      Width           =   3150
   End
   Begin VB.CheckBox ckPlnTT 
      Alignment       =   1  'Right Justify
      Caption         =   "Apply Trend to Forecast"
      Height          =   300
      Left            =   6600
      TabIndex        =   28
      Top             =   5880
      Width           =   3150
   End
   Begin VB.CheckBox ckFreeze_DSer 
      Alignment       =   1  'Right Justify
      Caption         =   "Freeze Service Level"
      Height          =   300
      Left            =   6600
      TabIndex        =   27
      Top             =   5400
      Width           =   3180
   End
   Begin VB.CheckBox ckFreeze_OptionId 
      Alignment       =   1  'Right Justify
      Caption         =   "Freeze Option ID"
      Height          =   300
      Left            =   6600
      TabIndex        =   26
      Top             =   5040
      Width           =   3180
   End
   Begin VB.CheckBox ckFreeze_LeadTime 
      Alignment       =   1  'Right Justify
      Caption         =   "Freeze Lead Time"
      Height          =   300
      Left            =   6600
      TabIndex        =   25
      Top             =   4680
      Width           =   3180
   End
   Begin VB.CheckBox ckFreeze_ById 
      Alignment       =   1  'Right Justify
      Caption         =   "Freeze Buyer ID"
      Height          =   300
      Left            =   6600
      TabIndex        =   24
      Top             =   4320
      Width           =   3180
   End
   Begin VB.CheckBox ckFreeze_BuyStrat 
      Alignment       =   1  'Right Justify
      Caption         =   "Freeze Buying Strategy"
      Height          =   300
      Left            =   6600
      TabIndex        =   23
      Top             =   3960
      Width           =   3180
   End
   Begin VB.CheckBox ckOnPromotion 
      Alignment       =   1  'Right Justify
      Caption         =   "On Promotion"
      Height          =   300
      Left            =   6600
      TabIndex        =   22
      Top             =   3480
      Width           =   3180
   End
   Begin TDBDate6Ctl.TDBDate txtActDate 
      BeginProperty DataFormat 
         Type            =   1
         Format          =   "MM/dd/yyyy"
         HaveTrueFalseNull=   0
         FirstDayOfWeek  =   0
         FirstWeekOfYear =   0
         LCID            =   1033
         SubFormatType   =   3
      EndProperty
      Height          =   345
      Left            =   2760
      TabIndex        =   31
      Top             =   840
      Width           =   1380
      _Version        =   65536
      _ExtentX        =   2117
      _ExtentY        =   873
      Calendar        =   "AIM_ItemBatchSave.frx":0000
      Caption         =   "AIM_ItemBatchSave.frx":0118
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0184
      Keys            =   "AIM_ItemBatchSave.frx":01A2
      Spin            =   "AIM_ItemBatchSave.frx":0200
      AlignHorizontal =   0
      AlignVertical   =   0
      Appearance      =   1
      BackColor       =   -2147483643
      BorderStyle     =   1
      BtnPositioning  =   1
      ClipMode        =   0
      CursorPosition  =   0
      DataProperty    =   0
      DisplayFormat   =   "mm/dd/yyyy"
      EditMode        =   0
      Enabled         =   -1
      ErrorBeep       =   0
      FirstMonth      =   4
      ForeColor       =   -2147483640
      Format          =   "mm/dd/yyyy"
      HighlightText   =   0
      IMEMode         =   3
      MarginBottom    =   3
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MaxDate         =   2958465
      MinDate         =   -657434
      MousePointer    =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
      PromptChar      =   "_"
      ReadOnly        =   0
      ShowContextMenu =   -1
      ShowLiterals    =   0
      TabAction       =   0
      Text            =   "10/10/1999"
      ValidateMode    =   0
      ValueVT         =   7
      Value           =   36443
      CenturyMode     =   0
   End
   Begin TDBDate6Ctl.TDBDate txtInActDate 
      BeginProperty DataFormat 
         Type            =   1
         Format          =   "MM/dd/yyyy"
         HaveTrueFalseNull=   0
         FirstDayOfWeek  =   0
         FirstWeekOfYear =   0
         LCID            =   1033
         SubFormatType   =   3
      EndProperty
      Height          =   345
      Left            =   2760
      TabIndex        =   34
      Top             =   1320
      Width           =   1380
      _Version        =   65536
      _ExtentX        =   2434
      _ExtentY        =   609
      Calendar        =   "AIM_ItemBatchSave.frx":0228
      Caption         =   "AIM_ItemBatchSave.frx":0340
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":03AC
      Keys            =   "AIM_ItemBatchSave.frx":03CA
      Spin            =   "AIM_ItemBatchSave.frx":0428
      AlignHorizontal =   0
      AlignVertical   =   0
      Appearance      =   1
      BackColor       =   -2147483643
      BorderStyle     =   1
      BtnPositioning  =   1
      ClipMode        =   0
      CursorPosition  =   0
      DataProperty    =   0
      DisplayFormat   =   "mm/dd/yyyy"
      EditMode        =   0
      Enabled         =   -1
      ErrorBeep       =   0
      FirstMonth      =   4
      ForeColor       =   -2147483640
      Format          =   "mm/dd/yyyy"
      HighlightText   =   0
      IMEMode         =   3
      MarginBottom    =   3
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MaxDate         =   2958465
      MinDate         =   -657434
      MousePointer    =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
      PromptChar      =   "_"
      ReadOnly        =   0
      ShowContextMenu =   -1
      ShowLiterals    =   0
      TabAction       =   0
      Text            =   "10/10/1999"
      ValidateMode    =   0
      ValueVT         =   7
      Value           =   36443
      CenturyMode     =   0
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcOptionId 
      Bindings        =   "AIM_ItemBatchSave.frx":0450
      DataField       =   " "
      Height          =   345
      Left            =   2760
      TabIndex        =   35
      Top             =   1800
      Width           =   1380
      DataFieldList   =   "optionid"
      AllowInput      =   0   'False
      AllowNull       =   0   'False
      _Version        =   196617
      Cols            =   2
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   2434
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
   End
   Begin TDBText6Ctl.TDBText txtBinLocation 
      Height          =   345
      Left            =   2760
      TabIndex        =   36
      Top             =   2160
      Width           =   1800
      _Version        =   65536
      _ExtentX        =   3175
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":045B
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":04C7
      Key             =   "AIM_ItemBatchSave.frx":04E5
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   6
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   12
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtUPC 
      Height          =   345
      Left            =   2760
      TabIndex        =   37
      Top             =   2640
      Width           =   1800
      _Version        =   65536
      _ExtentX        =   3175
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":0529
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0595
      Key             =   "AIM_ItemBatchSave.frx":05B3
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   22
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtUOM 
      Height          =   345
      Left            =   2760
      TabIndex        =   38
      Top             =   3120
      Width           =   540
      _Version        =   65536
      _ExtentX        =   952
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":05F7
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0663
      Key             =   "AIM_ItemBatchSave.frx":0681
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   6
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtBuyingUOM 
      Height          =   345
      Left            =   2760
      TabIndex        =   39
      Top             =   3600
      Width           =   540
      _Version        =   65536
      _ExtentX        =   952
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":06C5
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0731
      Key             =   "AIM_ItemBatchSave.frx":074F
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   6
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBNumber6Ctl.TDBNumber txtConvFactor 
      Height          =   345
      Left            =   2760
      TabIndex        =   40
      Top             =   4080
      Width           =   1380
      _Version        =   65536
      _ExtentX        =   2434
      _ExtentY        =   609
      Calculator      =   "AIM_ItemBatchSave.frx":0793
      Caption         =   "AIM_ItemBatchSave.frx":07B3
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":081F
      Keys            =   "AIM_ItemBatchSave.frx":083D
      Spin            =   "AIM_ItemBatchSave.frx":0887
      AlignHorizontal =   1
      AlignVertical   =   2
      Appearance      =   1
      BackColor       =   -2147483643
      BorderStyle     =   1
      BtnPositioning  =   1
      ClipMode        =   0
      ClearAction     =   0
      DecimalPoint    =   "."
      DisplayFormat   =   "#########0;-#########0;0;0"
      EditMode        =   0
      Enabled         =   -1
      ErrorBeep       =   0
      ForeColor       =   -2147483640
      Format          =   "#########0"
      HighlightText   =   0
      MarginBottom    =   3
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MaxValue        =   2147483647
      MinValue        =   0
      MousePointer    =   0
      MoveOnLRKey     =   0
      NegativeColor   =   255
      OLEDragMode     =   0
      OLEDropMode     =   0
      ReadOnly        =   0
      Separator       =   ","
      ShowContextMenu =   -1
      ValueVT         =   1245189
      Value           =   0
      MaxValueVT      =   5
      MinValueVT      =   5
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcSaId 
      Bindings        =   "AIM_ItemBatchSave.frx":08AF
      DataField       =   " "
      Height          =   345
      Left            =   2760
      TabIndex        =   41
      Top             =   4440
      Width           =   2010
      DataFieldList   =   "said"
      AllowInput      =   0   'False
      AllowNull       =   0   'False
      _Version        =   196617
      Cols            =   2
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   3545
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "said"
   End
   Begin TDBText6Ctl.TDBText txtPmId 
      Height          =   345
      Left            =   2760
      TabIndex        =   42
      Top             =   4920
      Width           =   1380
      _Version        =   65536
      _ExtentX        =   2434
      _ExtentY        =   600
      Caption         =   "AIM_ItemBatchSave.frx":08BA
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0926
      Key             =   "AIM_ItemBatchSave.frx":0944
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   6
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtAllocUOM 
      Height          =   345
      Left            =   2760
      TabIndex        =   43
      Top             =   5400
      Width           =   540
      _Version        =   65536
      _ExtentX        =   952
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":0988
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":09F4
      Key             =   "AIM_ItemBatchSave.frx":0A12
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   6
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBNumber6Ctl.TDBNumber txtAllocConvFactor 
      Height          =   345
      Left            =   2760
      TabIndex        =   44
      Top             =   5760
      Width           =   1380
      _Version        =   65536
      _ExtentX        =   2434
      _ExtentY        =   609
      Calculator      =   "AIM_ItemBatchSave.frx":0A56
      Caption         =   "AIM_ItemBatchSave.frx":0A76
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0AE2
      Keys            =   "AIM_ItemBatchSave.frx":0B00
      Spin            =   "AIM_ItemBatchSave.frx":0B4A
      AlignHorizontal =   1
      AlignVertical   =   2
      Appearance      =   1
      BackColor       =   -2147483643
      BorderStyle     =   1
      BtnPositioning  =   1
      ClipMode        =   0
      ClearAction     =   0
      DecimalPoint    =   "."
      DisplayFormat   =   "#########0;-#########0;0;0"
      EditMode        =   0
      Enabled         =   -1
      ErrorBeep       =   0
      ForeColor       =   -2147483640
      Format          =   "#########0"
      HighlightText   =   0
      MarginBottom    =   3
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MaxValue        =   2147483647
      MinValue        =   0
      MousePointer    =   0
      MoveOnLRKey     =   0
      NegativeColor   =   255
      OLEDragMode     =   0
      OLEDropMode     =   0
      ReadOnly        =   0
      Separator       =   ","
      ShowContextMenu =   -1
      ValueVT         =   1245189
      Value           =   0
      MaxValueVT      =   5
      MinValueVT      =   5
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
      Height          =   345
      Index           =   0
      Left            =   2760
      TabIndex        =   45
      Top             =   6240
      Width           =   2100
      DataFieldList   =   "Class"
      _Version        =   196617
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   3704
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "Class"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
      Height          =   345
      Index           =   1
      Left            =   2760
      TabIndex        =   46
      Top             =   6720
      Width           =   2100
      DataFieldList   =   "Class"
      _Version        =   196617
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   3704
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "Class"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
      Height          =   345
      Index           =   2
      Left            =   2760
      TabIndex        =   47
      Top             =   7200
      Width           =   2100
      DataFieldList   =   "Class"
      _Version        =   196617
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   3704
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "Class"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
      Height          =   345
      Index           =   3
      Left            =   10200
      TabIndex        =   48
      Top             =   600
      Width           =   2100
      DataFieldList   =   "Class"
      _Version        =   196617
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   3704
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "Class"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcVelCode 
      Bindings        =   "AIM_ItemBatchSave.frx":0B72
      DataField       =   " "
      Height          =   345
      Left            =   10200
      TabIndex        =   49
      Top             =   960
      Width           =   630
      DataFieldList   =   "velcode"
      AllowInput      =   0   'False
      AllowNull       =   0   'False
      _Version        =   196617
      Cols            =   2
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnHeaders   =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   1111
      _ExtentY        =   600
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "velcode"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcVC_Amt 
      Bindings        =   "AIM_ItemBatchSave.frx":0B7D
      Height          =   345
      Left            =   10200
      TabIndex        =   50
      Top             =   1320
      Width           =   630
      DataFieldList   =   "velcode"
      AllowInput      =   0   'False
      AllowNull       =   0   'False
      _Version        =   196617
      Cols            =   2
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ColumnHeaders   =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   1111
      _ExtentY        =   600
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "velcode"
   End
   Begin TDBText6Ctl.TDBText txtUserRef1 
      Height          =   345
      Left            =   10200
      TabIndex        =   51
      Top             =   1680
      Width           =   1800
      _Version        =   65536
      _ExtentX        =   3175
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":0B88
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0BF4
      Key             =   "AIM_ItemBatchSave.frx":0C12
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   12
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtUserRef2 
      Height          =   345
      Left            =   10200
      TabIndex        =   52
      Top             =   2040
      Width           =   1800
      _Version        =   65536
      _ExtentX        =   3175
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":0C56
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0CC2
      Key             =   "AIM_ItemBatchSave.frx":0CE0
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   12
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtUserRef3 
      Height          =   345
      Left            =   10200
      TabIndex        =   53
      Top             =   2400
      Width           =   1800
      _Version        =   65536
      _ExtentX        =   3175
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":0D24
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0D90
      Key             =   "AIM_ItemBatchSave.frx":0DAE
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   12
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin TDBText6Ctl.TDBText txtOldItem 
      Height          =   345
      Left            =   10200
      TabIndex        =   54
      Top             =   2760
      Width           =   3180
      _Version        =   65536
      _ExtentX        =   5609
      _ExtentY        =   609
      Caption         =   "AIM_ItemBatchSave.frx":0DF2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":0E5E
      Key             =   "AIM_ItemBatchSave.frx":0E7C
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   25
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcItStat 
      Bindings        =   "AIM_ItemBatchSave.frx":0EC0
      DataField       =   "itstat"
      Height          =   345
      Left            =   9120
      TabIndex        =   55
      Top             =   6960
      Width           =   630
      DataFieldList   =   "itstat"
      AllowInput      =   0   'False
      AllowNull       =   0   'False
      _Version        =   196617
      Cols            =   2
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   1111
      _ExtentY        =   609
      _StockProps     =   93
      BackColor       =   -2147483643
      DataFieldToDisplay=   "itstat"
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   0
      Top             =   0
      _ExtentX        =   582
      _ExtentY        =   661
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_ItemBatchSave.frx":0ECB
      ToolBars        =   "AIM_ItemBatchSave.frx":A78D
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcBuyStrat 
      Bindings        =   "AIM_ItemBatchSave.frx":AB09
      DataField       =   " "
      Height          =   345
      Left            =   9120
      TabIndex        =   57
      Top             =   8040
      Width           =   630
      DataFieldList   =   "Column 0"
      AllowInput      =   0   'False
      AllowNull       =   0   'False
      _Version        =   196617
      DataMode        =   2
      Cols            =   2
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      _ExtentX        =   1111
      _ExtentY        =   600
      _StockProps     =   93
      ForeColor       =   -2147483640
      BackColor       =   -2147483643
   End
   Begin TDBText6Ctl.TDBText txtVnId 
      Height          =   345
      Left            =   9120
      TabIndex        =   59
      Top             =   8400
      Width           =   1710
      _Version        =   65536
      _ExtentX        =   3016
      _ExtentY        =   600
      Caption         =   "AIM_ItemBatchSave.frx":AB14
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ItemBatchSave.frx":AB80
      Key             =   "AIM_ItemBatchSave.frx":AB9E
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   0
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   0
      ScrollBars      =   0
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   12
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   0
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin VB.Label label 
      Caption         =   "Primary Vendor ID"
      Height          =   300
      Index           =   24
      Left            =   6600
      TabIndex        =   58
      Top             =   8400
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Buying Strategy"
      Height          =   300
      Index           =   23
      Left            =   6600
      TabIndex        =   56
      Top             =   8040
      Width           =   2445
   End
   Begin VB.Label Label16 
      Caption         =   "Item Status"
      Height          =   300
      Left            =   6600
      TabIndex        =   32
      Top             =   6960
      Width           =   1680
   End
   Begin VB.Label label 
      Caption         =   "Old Item Number"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Index           =   19
      Left            =   6600
      TabIndex        =   21
      Top             =   3000
      Width           =   3045
   End
   Begin VB.Label label 
      Caption         =   "User Reference 3"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Index           =   22
      Left            =   6600
      TabIndex        =   20
      Top             =   2640
      Width           =   3045
   End
   Begin VB.Label label 
      Caption         =   "User Reference 2"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Index           =   21
      Left            =   6600
      TabIndex        =   19
      Top             =   2160
      Width           =   3045
   End
   Begin VB.Label label 
      Caption         =   "User Reference 1"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Index           =   20
      Left            =   6600
      TabIndex        =   18
      Top             =   1800
      Width           =   3045
   End
   Begin VB.Label label 
      Caption         =   "Velocity Code - Amount"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Index           =   11
      Left            =   6600
      TabIndex        =   17
      Top             =   1440
      Width           =   3045
   End
   Begin VB.Label label 
      Caption         =   "Velocity Code"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   300
      Index           =   10
      Left            =   6600
      TabIndex        =   16
      Top             =   1080
      Width           =   3045
   End
   Begin VB.Label label 
      Caption         =   "Classification Code 4"
      Height          =   300
      Index           =   7
      Left            =   6600
      TabIndex        =   15
      Top             =   720
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Classification Code 3"
      Height          =   300
      Index           =   6
      Left            =   0
      TabIndex        =   14
      Top             =   7320
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Classification Code 2"
      Height          =   300
      Index           =   5
      Left            =   0
      TabIndex        =   13
      Top             =   6840
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Classification Code 1"
      Height          =   300
      Index           =   4
      Left            =   0
      TabIndex        =   12
      Top             =   6360
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Allocation - Conversion Factor"
      Height          =   300
      Index           =   96
      Left            =   0
      TabIndex        =   11
      Top             =   5880
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Allocation - Unit of Measure"
      Height          =   300
      Index           =   95
      Left            =   0
      TabIndex        =   10
      Top             =   5520
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Promotion ID"
      Height          =   300
      Index           =   51
      Left            =   0
      TabIndex        =   9
      Top             =   5040
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Seasonality Profile"
      Height          =   300
      Index           =   50
      Left            =   0
      TabIndex        =   8
      Top             =   4560
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Conversion Factor"
      Height          =   300
      Index           =   18
      Left            =   0
      TabIndex        =   7
      Top             =   4080
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Buying UOM"
      Height          =   300
      Index           =   17
      Left            =   0
      TabIndex        =   6
      Top             =   3600
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Unit of Measure"
      Height          =   300
      Index           =   16
      Left            =   0
      TabIndex        =   5
      Top             =   3120
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Universal Product Code"
      Height          =   300
      Index           =   9
      Left            =   0
      TabIndex        =   4
      Top             =   2640
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Bin Location"
      Height          =   300
      Index           =   8
      Left            =   0
      TabIndex        =   3
      Top             =   2160
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Option ID"
      Height          =   300
      Index           =   3
      Left            =   0
      TabIndex        =   2
      Top             =   1800
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Inactive Date"
      Height          =   300
      Index           =   1
      Left            =   0
      TabIndex        =   1
      Top             =   1320
      Width           =   2445
   End
   Begin VB.Label label 
      Caption         =   "Activation Date"
      Height          =   300
      Index           =   0
      Left            =   120
      TabIndex        =   0
      Top             =   840
      Width           =   2445
   End
End
Attribute VB_Name = "AIM_ItemBatchSave"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_Item_GetKey_Sp As ADODB.Command
Dim AIM_Item_GetEq_Sp As ADODB.Command

Dim rsAIMOptions As ADODB.Recordset
Dim rsAIMSeasons As ADODB.Recordset
Dim rsItStatus As ADODB.Recordset
Dim rsItem As ADODB.Recordset
Dim rsVelCode As ADODB.Recordset

Dim rsClass1 As ADODB.Recordset
Dim rsClass2 As ADODB.Recordset
Dim rsClass3 As ADODB.Recordset
Dim rsClass4 As ADODB.Recordset

Dim LcID_BookMark As String
Dim Item_BookMark As String

'Item Table Key
Dim LcId As String
Dim Item As String
Dim First_Item As String
Dim First_LcID As String

'CreateDuplicate Flag
Dim CreateDuplicate As Boolean
'Header
Dim ItStat As String
Dim ItDesc As String
Dim ItItem As String

'General Tab
Dim OptionId As String
Dim Class1 As String
Dim Class2 As String
Dim Class3 As String
Dim Class4  As String
Dim BinLocation As String
Dim UPC As String
Dim UOM As String
Dim BuyingUOM As String
Dim ConvFactor As Double
Dim AllocUOM As String
Dim AllocConvFactor As Double
    
Dim OldItem As String
Dim VelCode As String
Dim VC_Amt As String
Dim VC_Units_Ranking As Integer
Dim VC_Amt_Ranking As Integer
Dim VelCode_Prev As String
Dim VC_Amt_Prev As String
Dim UserRef1 As String
Dim UserRef2 As String
Dim UserRef3 As String
Dim OnPromotion As Boolean
Dim Freeze_BuyStrat As Boolean
Dim Freeze_ById As Boolean
Dim Freeze_LeadTime As Boolean
Dim Freeze_OptionID As Boolean
Dim Freeze_DSer As Boolean
'Order Policies
Dim BuyStrat As String
Dim VnId As String
Dim Assort As String
Dim ById As String
Dim MDC As String
Dim ReplenCost2 As Double
Dim PackRounding As String
Dim IMin As Double
Dim IMax As Double
Dim CStock As Double
Dim SSAdj As Double
Dim UserMin As Double
Dim UserMax As Double
Dim DSer As Double
Dim NextPONbr_1 As String
Dim NextPOQty_1 As Double
Dim NextPONbr_2 As String
Dim NextPOQty_2 As Double
Dim NextPONbr_3 As String
Dim NextPOQty_3 As Double
Dim BkQty01 As Double
Dim BkQty02 As Double
Dim BkQty03 As Double
Dim BkQty04 As Double
Dim BkQty05 As Double
Dim BkQty06 As Double
Dim BkQty07 As Double
Dim BkQty08 As Double
Dim BkQty09 As Double
Dim BkQty10 As Double
Dim BkCost01 As Double
Dim BkCost02 As Double
Dim BkCost03 As Double
Dim BkCost04 As Double
Dim BkCost05 As Double
Dim BkCost06 As Double
Dim BkCost07 As Double
Dim BkCost08 As Double
Dim BkCost09 As Double
Dim BkCost10 As Double
Dim ZOPSw As Boolean
Dim OUTLSw As Boolean
Dim ZSStock As Boolean
Dim MDCFlag As Boolean
'Stock Status Tab
Dim Weight As Double
Dim Cube As Double
Dim ListPrice As Double
Dim Price As Double
Dim Cost As Double
Dim Oh As Double
Dim Oo As Double
Dim ComStk As Double
Dim BkOrder As Double
Dim BkComStk As Double
Dim Avail As Double
Dim AvgOH As Double
'Forecast Control/Results
Dim SaId As String
Dim PmId As String
Dim LeadTime As Double
Dim UserMethod As Integer
Dim UserFcst As Double
Dim LtvFact As Double
Dim PlnTT As Boolean
Dim FcstMethod As Integer
Dim FcstDemand As Double
Dim DependentFcst As Double
Dim MAE As Double
Dim MSE As Double
Dim Trend As Double
Dim FcstCycles As Double
Dim ZeroCount As Double
Dim ByPassPct As Double
Dim FcstUpdCyc As Double
Dim Accum_Lt As Double
Dim ReviewTime As Long
Dim OrderPt As Double
Dim OrderQty As Double
Dim SafetyStock As Double
Dim FcstLT As Double
Dim FcstRT As Double
Dim Fcst_Month As Double
Dim Fcst_Quarter As Double
Dim Fcst_Year As Double
Dim DIFlag As Boolean
Dim DmdFilterFlag As Boolean
Dim TrkSignalFlag As Boolean
Dim UserDemandFlag As Boolean
Dim ActDate As Date
Dim InActDate As Date
Dim VC_Date As Date
Dim NextPODate_1 As Date
Dim NextPODate_2 As Date
Dim NextPODate_3 As Date
Dim UserFcstExpDate As Date
Dim FcstDate As Date
Dim Mean_NZ As Double
Dim StdDev_NZ As Double
Dim IntSafetyStock As Double
Dim IsIntermittent As String
Dim KitBOMFlag As Boolean

Dim rsAlternateSource As ADODB.Recordset
Dim EnabledUpdateFlag As Boolean

Dim rsKitBOMSource As ADODB.Recordset
Dim rsItemKitBOMInsert As ADODB.Recordset
Dim EnabledBOMUpdateFlag As Boolean

Private Function GetAIMClasses()
On Error GoTo ErrorHandler

    Dim AIM_StoredProcs As ADODB.Command
    
    'Prepare the command obj for the stored proc to fetch DropDown lists.
    Set AIM_StoredProcs = New ADODB.Command
    With AIM_StoredProcs
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
    End With
        
    'Build/Bind the AIM Class Drop Down
    AIM_StoredProcs.CommandText = "AIM_ClassByClassLevel_List_Sp"
    AIM_StoredProcs.Parameters("@LangID").Value = gLangID
    
    'Class level 1
    AIM_StoredProcs.Parameters("@ClassLevel").Value = 1
    If f_IsRecordsetValidAndOpen(rsClass1) Then rsClass1.Close
    Set rsClass1 = Nothing
    Set rsClass1 = New ADODB.Recordset
    With rsClass1
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass1.Open AIM_StoredProcs
    If f_IsRecordsetOpenAndPopulated(rsClass1) Then Set Me.dcClass(0).DataSourceList = rsClass1
    
    'Class level 2
    AIM_StoredProcs.Parameters("@ClassLevel").Value = 2
    If f_IsRecordsetValidAndOpen(rsClass2) Then rsClass2.Close
    Set rsClass2 = Nothing
    Set rsClass2 = New ADODB.Recordset
    With rsClass2
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass2.Open AIM_StoredProcs
    If f_IsRecordsetOpenAndPopulated(rsClass2) Then Set Me.dcClass(1).DataSourceList = rsClass2
    
    'Class level 3
    AIM_StoredProcs.Parameters("@ClassLevel").Value = 3
    If f_IsRecordsetValidAndOpen(rsClass3) Then rsClass3.Close
    Set rsClass3 = Nothing
    Set rsClass3 = New ADODB.Recordset
    With rsClass3
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass3.Open AIM_StoredProcs
    If f_IsRecordsetOpenAndPopulated(rsClass3) Then Set Me.dcClass(2).DataSourceList = rsClass3
    
    'Class level 4
    AIM_StoredProcs.Parameters("@ClassLevel").Value = 4
    If f_IsRecordsetValidAndOpen(rsClass4) Then rsClass4.Close
    Set rsClass4 = Nothing
    Set rsClass4 = New ADODB.Recordset
    With rsClass4
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass4.Open AIM_StoredProcs
    If f_IsRecordsetOpenAndPopulated(rsClass4) Then Set Me.dcClass(3).DataSourceList = rsClass4
    
    'Clean Up
    If Not (AIM_StoredProcs Is Nothing) Then Set AIM_StoredProcs.ActiveConnection = Nothing
    Set AIM_StoredProcs = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_StoredProcs Is Nothing) Then Set AIM_StoredProcs.ActiveConnection = Nothing
    Set AIM_StoredProcs = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(GetAIMClasses)"
End Function
Private Function getItemRecord(LcId As String, Item As String, Action As SQL_ACTIONS)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    
    'Get the Item Table Key
    RtnCode = Item_GetKey(AIM_Item_GetKey_Sp, LcId, Item, Action)
    
    'Get the Item Record
    If RtnCode <> SUCCEED Then
    'If the function fails to get item and LcID get then set it to firstitem and firstlocation
        Item = First_Item
        LcId = First_LcID
    End If
    
''    If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
    Set rsItem = Nothing
    Set rsItem = New ADODB.Recordset
    With rsItem
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    RtnCode = Item_GetEq(AIM_Item_GetEq_Sp, rsItem, LcId, Item)
    
    getItemRecord = RtnCode
  
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(getItemRecord)"
End Function



Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    'AIM_ItemBatchSave.v
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemBatchSave::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
            
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02100")
    If StrComp(strMessage, "STATMSG02100") = 0 Then strMessage = "Initializing AIM Item Table Maintenenace..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
   
    GetAIMClasses
    
    'Initialize Stored Procedures
    Set AIM_Item_GetKey_Sp = New ADODB.Command
    With AIM_Item_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Item_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_Item_GetEq_Sp = New ADODB.Command
    With AIM_Item_GetEq_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Item_GetEq_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the AIM Options Dropdown
    Set rsAIMOptions = New ADODB.Recordset
    SqlStmt = "SELECT OptionID, OpDesc FROM AIMOptions ORDER BY OptionID "
    rsAIMOptions.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    Set Me.dcOptionId.DataSourceList = rsAIMOptions
    
    'Initialize the AIM Seasons Dropdown
    Set rsAIMSeasons = New ADODB.Recordset
    SqlStmt = "SELECT SaID, SADesc FROM AIMSeasons ORDER BY SaID "
    rsAIMSeasons.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    Set Me.dcSaId.DataSourceList = rsAIMSeasons
    
    'Initialize the Item Status Dropdown
    Set rsItStatus = New ADODB.Recordset
    SqlStmt = "SELECT ItStat, ItStatDesc FROM ItStatus ORDER BY ItStat "
    rsItStatus.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    Set Me.dcItStat.DataSourceList = rsItStatus
    
    'Initialize the Velocity Code Dropdown
    Set rsVelCode = New ADODB.Recordset
    SqlStmt = "SELECT VelCode FROM AIMVelCodePcts ORDER BY VelCode "
    rsVelCode.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    Set Me.dcVelCode.DataSourceList = rsVelCode
    Set Me.dcVC_Amt.DataSourceList = rsVelCode
    
    'Initialize the Item Record Set
'    With rsItem
'        .CursorLocation = adUseClient
'        .CursorType = adOpenStatic
'        .LockType = adLockOptimistic
'    End With
    
    'Open the Result Set
    LcId = ""
    Item = ""
    
    RtnCode = getItemRecord(LcId, Item, SQL_GetFirst)
    'Store the firstitem and firstlocation
    First_Item = Item
    First_LcID = LcId
    
    If f_IsRecordsetOpenAndPopulated(rsItem) Then
        Me.tbNavigation.ToolBars("tbNavigation").Tools("Id_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    Else
  '      initItemRecord--------------------------------------------------------------------------------sri
    End If
    
    'Refresh Form
    'RefreshForm------------------------------------------------------------------------------------sri
   
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbNavigation.Tools("ID_Save").Enabled = False
    Else
        Me.tbNavigation.Tools("ID_Save").Enabled = True
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption

    'Make the spin button visible
    'Me.txtLTVFact.Spin.Visible = 1  sri
    'Me.txtDSer.Spin.Visible = 1   sri
    'Me.txtSSAdj.Spin.Visible = 1  sri
    'Me.txtReplenCost2.Spin.Visible = 1 sri
    'Me.txtBkCost10.Spin.Visible = 1 sri
    'Me.txtBkCost09.Spin.Visible = 1 sri
    'Me.txtBkCost08.Spin.Visible = 1 sri
    'Me.txtBkCost07.Spin.Visible = 1 sri
    'Me.txtBkCost06.Spin.Visible = 1 sri
    'Me.txtBkCost05.Spin.Visible = 1 sri
    'Me.txtBkCost04.Spin.Visible = 1 sri
    'Me.txtBkCost03.Spin.Visible = 1 sri
    'Me.txtBkCost02.Spin.Visible = 1 sri
    'Me.txtBkCost01.Spin.Visible = 1 sri
    'Me.txtAvgOh.Spin.Visible = 1 sri
    'Me.txtCost.Spin.Visible = 1 sri
    'Me.txtPrice.Spin.Visible = 1 sri
    'Me.txtListPrice.Spin.Visible = 1 sri
    'Me.txtCube.Spin.Visible = 1 sri
    'Me.txtWeight.Spin.Visible = 1 sri
'    Me.txtUserFcst.Spin.Visible = 1
'    Me.txtConvFactor.Spin.Visible = 1
'    Me.txtAllocConvFactor.Spin.Visible = 1
'    Me.txtIMax.Spin.Visible = 1
'    Me.txtIMin.Spin.Visible = 1
'    Me.txtCStock.Spin.Visible = 1
'    Me.txtUserMax.Spin.Visible = 1
'    Me.txtUserMin.Spin.Visible = 1
'    Me.txtLeadTime.Spin.Visible = 1
'    Me.txtNextPOQty_1.Spin.Visible = 1
'    Me.txtNextPOQty_2.Spin.Visible = 1
'    Me.txtNextPOQty_3.Spin.Visible = 1
'    Me.txtBkQty01.Spin.Visible = 1
'    Me.txtBkQty02.Spin.Visible = 1
'    Me.txtBkQty03.Spin.Visible = 1
'    Me.txtBkQty04.Spin.Visible = 1
'    Me.txtBkQty05.Spin.Visible = 1
'    Me.txtBkQty06.Spin.Visible = 1
'    Me.txtBkQty07.Spin.Visible = 1
'    Me.txtBkQty08.Spin.Visible = 1
'    Me.txtBkQty09.Spin.Visible = 1
'    Me.txtBkQty10.Spin.Visible = 1
'    Me.txtOh.Spin.Visible = 1
'    Me.txtOo.Spin.Visible = 1
'    Me.txtComStk.Spin.Visible = 1
'    Me.txtBkOrder.Spin.Visible = 1
'    Me.txtBkComStk.Spin.Visible = 1
'    Me.txtUserMethod.Spin.Visible = 1
'    Me.txtUserFcstExpDate.DropDown.Visible = 1
'    Me.txtNextPODate_1.DropDown.Visible = 1
'    Me.txtNextPODate_2.DropDown.Visible = 1
'    Me.txtNextPODate_3.DropDown.Visible = 1
'    Me.txtActDate.DropDown.Visible = 1
'    Me.txtInActDate.DropDown.Visible = 1
  'sri comment end all the above
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemBatchSave::Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMOptions) Then rsAIMOptions.Close
    Set rsAIMOptions = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
    Set rsAIMSeasons = Nothing
        
    If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
    Set rsItem = Nothing
    
    If f_IsRecordsetValidAndOpen(rsItStatus) Then rsItStatus.Close
    Set rsItStatus = Nothing
     
    If f_IsRecordsetValidAndOpen(rsVelCode) Then rsVelCode.Close
    Set rsVelCode = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass1) Then rsClass1.Close
    Set rsClass1 = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass2) Then rsClass2.Close
    Set rsClass2 = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass3) Then rsClass3.Close
    Set rsClass3 = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass4) Then rsClass4.Close
    Set rsClass4 = Nothing
   
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form FROM window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        f_HandleErr , , , "AIM_ItemBatchSave::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next

End Sub


