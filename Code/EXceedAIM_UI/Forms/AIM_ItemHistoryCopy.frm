VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItemHistoryCopy 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Item History Copy"
   ClientHeight    =   3150
   ClientLeft      =   3270
   ClientTop       =   3975
   ClientWidth     =   7275
   Icon            =   "AIM_ItemHistoryCopy.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   3150
   ScaleWidth      =   7275
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atbNavigator 
      Left            =   240
      Top             =   2640
      _ExtentX        =   767
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Tools           =   "AIM_ItemHistoryCopy.frx":030A
      ToolBars        =   "AIM_ItemHistoryCopy.frx":1C95
   End
   Begin VB.Frame Frame1 
      Height          =   2655
      Left            =   150
      TabIndex        =   0
      Top             =   0
      Width           =   6975
      Begin TDBText6Ctl.TDBText TDBNewItemId 
         Height          =   345
         Left            =   3120
         TabIndex        =   8
         Top             =   1042
         Width           =   3135
         _Version        =   65536
         _ExtentX        =   5530
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryCopy.frx":1D31
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryCopy.frx":1D9D
         Key             =   "AIM_ItemHistoryCopy.frx":1DBB
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText TDBOldItemId 
         Height          =   345
         Left            =   3120
         TabIndex        =   5
         Top             =   641
         Width           =   3135
         _Version        =   65536
         _ExtentX        =   5530
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryCopy.frx":1DFF
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryCopy.frx":1E6B
         Key             =   "AIM_ItemHistoryCopy.frx":1E89
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.CheckBox ckCopyHistoryData 
         Caption         =   "Copy History Data"
         Height          =   340
         Left            =   120
         TabIndex        =   12
         Top             =   1844
         Width           =   6255
      End
      Begin VB.CommandButton cmdLcID 
         Height          =   315
         Left            =   6360
         MouseIcon       =   "AIM_ItemHistoryCopy.frx":1ECD
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ItemHistoryCopy.frx":201F
         Style           =   1  'Graphical
         TabIndex        =   3
         Top             =   255
         Width           =   315
      End
      Begin VB.CheckBox ckTransBudFcst 
         Caption         =   "Replace all Forecast Types"
         Height          =   340
         Left            =   6360
         TabIndex        =   14
         TabStop         =   0   'False
         Top             =   1440
         Visible         =   0   'False
         Width           =   495
      End
      Begin VB.CommandButton cmdNewItemID 
         Height          =   315
         Left            =   6360
         MouseIcon       =   "AIM_ItemHistoryCopy.frx":2169
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ItemHistoryCopy.frx":22BB
         Style           =   1  'Graphical
         TabIndex        =   9
         Top             =   1057
         Width           =   310
      End
      Begin VB.CheckBox ckPurgeOldItem 
         Caption         =   "Purge Old Item"
         Height          =   340
         Left            =   120
         TabIndex        =   13
         Top             =   2240
         Width           =   6255
      End
      Begin VB.CommandButton cmdOldItemID 
         Height          =   315
         Left            =   6360
         MouseIcon       =   "AIM_ItemHistoryCopy.frx":2405
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ItemHistoryCopy.frx":2557
         Style           =   1  'Graphical
         TabIndex        =   6
         Top             =   656
         Width           =   310
      End
      Begin TDBNumber6Ctl.TDBNumber txtScalingFactor 
         Height          =   345
         Left            =   3120
         TabIndex        =   11
         Top             =   1443
         Width           =   1455
         _Version        =   65536
         _ExtentX        =   2566
         _ExtentY        =   609
         Calculator      =   "AIM_ItemHistoryCopy.frx":26A1
         Caption         =   "AIM_ItemHistoryCopy.frx":26C1
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryCopy.frx":272D
         Keys            =   "AIM_ItemHistoryCopy.frx":274B
         Spin            =   "AIM_ItemHistoryCopy.frx":2795
         AlignHorizontal =   1
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "0.0000"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "0.0000"
         HighlightText   =   0
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxValue        =   9.9999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ","
         ShowContextMenu =   -1
         ValueVT         =   2011627525
         Value           =   1
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLocationID 
         Bindings        =   "AIM_ItemHistoryCopy.frx":27BD
         DataSource      =   "rsAIMlocations"
         Height          =   345
         Left            =   3120
         TabIndex        =   2
         Top             =   240
         Width           =   3135
         DataFieldList   =   "lcid"
         AllowInput      =   0   'False
         _Version        =   196617
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "lcid"
      End
      Begin VB.Label Label_Location 
         Caption         =   "Location ID"
         Height          =   300
         Left            =   120
         TabIndex        =   1
         Top             =   285
         Width           =   2800
      End
      Begin VB.Label LabelOldItem 
         Caption         =   "Old Item ID"
         Height          =   300
         Left            =   120
         TabIndex        =   4
         Top             =   686
         Width           =   2800
      End
      Begin VB.Label LabelNewItem 
         Caption         =   "New Item ID"
         Height          =   300
         Left            =   120
         TabIndex        =   7
         Top             =   1087
         Width           =   2800
      End
      Begin VB.Label LabelItemScalingFactor 
         Caption         =   "Scaling Factor"
         Height          =   300
         Left            =   120
         TabIndex        =   10
         Top             =   1488
         Width           =   2800
      End
   End
End
Attribute VB_Name = "AIM_ItemHistoryCopy"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
Dim rsAIMLocations As ADODB.Recordset
    
Private Function m_Find(p_Source As String)
On Error GoTo ErrorHandler

    Dim ShowLookup As Form
    
    AIM_ItemLookUp.LcIdKey = Me.dcLocationID.Text
    'AIM_ItemLookUp.ItemKey = Me.dcNewItemID.Value
    Select Case UCase$(p_Source)
        Case UCase$(Me.cmdNewItemID.Name)
            If Trim(Me.TDBNewItemId.Text) = "" Then
                AIM_ItemLookUp.ItemKey = ""
                AIM_ItemLookUp.ByIdKey = ""
                AIM_ItemLookUp.VnIdKey = ""
                AIM_ItemLookUp.AssortKey = ""
            Else
                AIM_ItemLookUp.ItemKey = Me.TDBNewItemId.Text
            End If
        Case UCase$(Me.cmdOldItemID.Name)
            If Trim(Me.TDBOldItemId.Text) = "" Then
                AIM_ItemLookUp.ItemKey = ""
                AIM_ItemLookUp.ByIdKey = ""
                AIM_ItemLookUp.VnIdKey = ""
                AIM_ItemLookUp.AssortKey = ""
            Else
                AIM_ItemLookUp.ItemKey = Me.TDBOldItemId.Text
            End If
    End Select
    
    Set ShowLookup = AIM_ItemLookUp
    With ShowLookup
        Set .Cn = Cn
        .Comparison = "Contains"
        .Show vbModal
        If Not .CancelFlag Then
            Select Case p_Source
                Case Me.cmdLcID.Name
                    Me.dcLocationID.Text = AIM_ItemLookUp.LcIdKey
                    'Me.dcNewItemID.Text = AIM_ItemLookUp.ItemKey
                    Me.TDBNewItemId.Text = AIM_ItemLookUp.ItemKey
                
                Case Me.cmdNewItemID.Name
                    Me.TDBNewItemId.Text = AIM_ItemLookUp.ItemKey
                    'Me.dcNewItemID.Text = AIM_ItemLookUp.ItemKey
                
                Case Me.cmdOldItemID.Name
                    Me.TDBOldItemId.Text = AIM_ItemLookUp.ItemKey
                    'Me.dcOldItemID.Text = AIM_ItemLookUp.ItemKey
                
            End Select
        End If
    End With
    Set ShowLookup = Nothing
    
Exit Function
ErrorHandler:
    Set ShowLookup = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(m_Find)"
End Function

Private Sub atbNavigator_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Dim DmdScalingFactor As Double
    Dim Heading As String
    Dim RtnCode As Long
    
    Dim LcId As String
    Dim NewItemId As String
    Dim OldItemId As String
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    Dim AIM_Item_GetKey_Sp As ADODB.Command
    Dim AIM_ItemHistory_Copy_Sp As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
        
    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
        
        Case "ID_Save"
            LcId = Trim(Me.dcLocationID.Text)
            NewItemId = Trim(Me.TDBNewItemId.Text)
            OldItemId = Trim(Me.TDBOldItemId.Text)
        
'            'Check for parameter errors
'            DmdScalingFactor = Me.txtHistoryScalingFactor.Value
'            Heading = getTranslationResource("Initialize New Location")
'            ScalingEffUntil = Me.txtScalingEffUntil.Value
'            SourceLcId = Trim(Me.dcSourceLcId.Text)
'            TargetLcid = Trim(Me.dcDestinationLcId.Text)
            Heading = Me.Caption
            If Trim$(LcId) = "" Then
                strMessage = getTranslationResource("MSGBOX06800")
                If StrComp(strMessage, "MSGBOX06800") = 0 Then strMessage = "Please select a Location ID."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If

            If Trim$(NewItemId) = "" Then
                strMessage = getTranslationResource("MSGBOX06801")
                If StrComp(strMessage, "MSGBOX06801") = 0 Then strMessage = "Please select a New Item ID."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If

            If Trim$(OldItemId) = "" Then
                strMessage = getTranslationResource("MSGBOX06802")
                If StrComp(strMessage, "MSGBOX06802") = 0 Then strMessage = "Please select an Old Item ID."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If
            If StrComp(OldItemId, NewItemId, vbTextCompare) = 0 Then
                strMessage = getTranslationResource("MSGBOX06806")
                If StrComp(strMessage, "MSGBOX06806") = 0 Then strMessage = "Old and New Item IDs cannot be the same."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If
            If StrComp(LcId, getTranslationResource("All"), vbTextCompare) <> 0 Then
                Set AIM_Item_GetKey_Sp = New ADODB.Command
                With AIM_Item_GetKey_Sp
                Set .ActiveConnection = Cn
                    .CommandType = adCmdStoredProc
                    .CommandText = "AIM_Item_GetKey_Sp"
                    
                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
                    .Parameters.Append CmdParams
                    
                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@LcID", adVarWChar, adParamInputOutput, 12)
                    .Parameters.Append CmdParams
                    
                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@Item", adVarWChar, adParamInputOutput, 25)
                    .Parameters.Append CmdParams

                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@action", adTinyInt, adParamInput)
                    .Parameters.Append CmdParams
                End With

                RtnCode = Item_GetKey(AIM_Item_GetKey_Sp, LcId, OldItemId, SQL_GetEq)
                If RtnCode <> 1 Then
                    strMessage = getTranslationResource("MSGBOX06807")
                    If StrComp(strMessage, "MSGBOX06807") = 0 Then strMessage = "Please select a valid Old Item ID for the Location" & " " & LcId
                    MsgBox strMessage, vbCritical + vbOKOnly, Heading
                    Exit Sub
                End If
                RtnCode = Item_GetKey(AIM_Item_GetKey_Sp, LcId, NewItemId, SQL_GetEq)
                If RtnCode <> 1 Then
                    strMessage = getTranslationResource("MSGBOX06808")
                    If StrComp(strMessage, "MSGBOX06808") = 0 Then strMessage = "Please select a valid New Item ID for the Location" & " " & LcId
                    MsgBox strMessage, vbCritical + vbOKOnly, Heading
                    Exit Sub
                End If
                
                If Not (AIM_Item_GetKey_Sp Is Nothing) Then AIM_Item_GetKey_Sp.ActiveConnection = Nothing
                Set AIM_Item_GetKey_Sp = Nothing
            End If
            
            'Display warning on Purge Option
            If Me.ckPurgeOldItem.Value = vbChecked Then
                strMessage = getTranslationResource("MSGBOX06803")
                If StrComp(strMessage, "MSGBOX06803") = 0 Then strMessage = "Item and Item History information for the Old Item --"
                strMessage1 = getTranslationResource("MSGBOX06804")
                If StrComp(strMessage1, "MSGBOX06804") = 0 Then strMessage1 = "-- will be deleted."
                strMessage2 = getTranslationResource("MSGBOX06805")
                If StrComp(strMessage2, "MSGBOX06805") = 0 Then strMessage2 = "Do you wish to proceed further?"

                RtnCode = MsgBox(strMessage & " " & OldItemId _
                    & " " & strMessage1 & vbCrLf & vbCrLf & strMessage2, vbCritical + vbYesNo, Heading)

                If RtnCode = vbNo Then
                    Exit Sub
                End If
            End If

            Me.MousePointer = vbHourglass

            strMessage = getTranslationResource("STATMSG06802")
            If StrComp(strMessage, "STATMSG076802") = 0 Then strMessage = "Copying Item History. Please wait..."
            Write_Message strMessage
            
            'Beef up the Connection Timeout
            Cn.CommandTimeout = 0

            Set CmdParams = Nothing
            'Initialize the History copy process
            Set AIM_ItemHistory_Copy_Sp = New ADODB.Command
            With AIM_ItemHistory_Copy_Sp
                Set .ActiveConnection = Cn
                .CommandText = "AIM_ItemHistory_Copy_Sp"
                .CommandType = adCmdStoredProc
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
                .Parameters.Append CmdParams
                
                Set CmdParams = .CreateParameter("@LocationId", adVarWChar, adParamInput, 12)
                CmdParams.Value = LcId
                .Parameters.Append CmdParams
                
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@OldItemId", adVarWChar, adParamInput, 25)
                CmdParams.Value = OldItemId
                .Parameters.Append CmdParams
                
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@NewItemId", adVarWChar, adParamInput, 25)
                CmdParams.Value = NewItemId
                .Parameters.Append CmdParams
                
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@DeleteOpt", adTinyInt, adParamInput)
                CmdParams.Value = Me.ckPurgeOldItem.Value
                .Parameters.Append CmdParams
                
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@TransBudFcst", adTinyInt, adParamInput)
                CmdParams.Value = Me.ckTransBudFcst.Value
                .Parameters.Append CmdParams
                
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@ScalingF", adDecimal, adParamInput)
                CmdParams.Precision = 5
                CmdParams.NumericScale = 4
                CmdParams.Value = Me.txtScalingFactor.Value
                .Parameters.Append CmdParams
                
                Set CmdParams = Nothing
                Set CmdParams = .CreateParameter("@CopyHistoryData", adTinyInt, adParamInput)
                CmdParams.Value = Me.ckCopyHistoryData.Value
                .Parameters.Append CmdParams
                Set CmdParams = Nothing
                
                ' Execute the command
                .Execute , , adExecuteNoRecords

                'Retrieve stored procedure return value and output parameters
                RtnCode = .Parameters(0).Value

                If RtnCode <> 0 Then
                    strMessage = getTranslationResource("ERRMSG06800")
                    If StrComp(strMessage, "ERRMSG06800") = 0 Then strMessage = "Error(s) while copying Item History"
                    MsgBox strMessage & vbCrLf & vbCrLf & _
                        Cn.Errors(0).Description, vbCritical + vbOKOnly, Heading
                Else
                    strMessage = getTranslationResource("STATMSG06801")
                    If StrComp(strMessage, "STATMSG06801") = 0 Then strMessage = "Item History copied successfully."
                    Write_Message strMessage
                End If
                
                'Clear the data
                Me.dcLocationID.Text = ""
                Me.TDBNewItemId.Text = ""
                Me.TDBOldItemId.Text = ""
                Me.ckPurgeOldItem.Value = vbUnchecked
                Me.ckTransBudFcst.Value = vbUnchecked
                Me.ckCopyHistoryData.Value = vbUnchecked
                Me.MousePointer = vbNormal
            End With
            If Not (AIM_ItemHistory_Copy_Sp Is Nothing) Then Set AIM_ItemHistory_Copy_Sp.ActiveConnection = Nothing
            Set AIM_ItemHistory_Copy_Sp = Nothing

    End Select

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    If Not (AIM_Item_GetKey_Sp Is Nothing) Then AIM_Item_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_Item_GetKey_Sp = Nothing
    Set CmdParams = Nothing
    If Not (AIM_ItemHistory_Copy_Sp Is Nothing) Then Set AIM_ItemHistory_Copy_Sp.ActiveConnection = Nothing
    Set AIM_ItemHistory_Copy_Sp = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr , , , "AIM_ItemHistoryCopy::atbNavigator_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub CmdLcId_Click()
On Error GoTo ErrorHandler
    
    m_Find Me.cmdLcID.Name

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemHistoryCopy::CmdLcId_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub CmdNewItemId_Click()
On Error GoTo ErrorHandler
    
    m_Find Me.cmdNewItemID.Name

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemHistoryCopy::CmdNewItemId_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub CmdOldItemId_Click()
On Error GoTo ErrorHandler

    m_Find Me.cmdOldItemID.Name

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemHistoryCopy::CmdOldItemId_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLocationId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLocationID.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcLocationID.Columns(0).Width = 1000
        
    Me.dcLocationID.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcLocationID.Columns(1).Width = 2880
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLocationID, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcLocationID.Columns.Count - 1
'        dcLocationID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLocationID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemHistoryCopy::dcLocationId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler
    
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ItemHistoryCopy::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG06800")
    If StrComp(strMessage, "STATMSG06800") = 0 Then strMessage = "Initializing AIM Item History Copy..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize the recordset
    Set rsAIMLocations = New ADODB.Recordset
    With rsAIMLocations
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    'Validate the Item for the location
    SqlStmt = "SELECT LcID, LName FROM AIMLocations  "
    SqlStmt = SqlStmt & " UNION SELECT 'ALL','ALL Locations' ORDER BY LcID"
    
    rsAIMLocations.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
        
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        'Initialize the location data combos
        Set Me.dcLocationID.DataSourceList = rsAIMLocations
    End If
        
    Me.dcLocationID.Text = ""
    Me.TDBNewItemId.Text = ""
    Me.TDBOldItemId.Text = ""
    Me.txtScalingFactor.Value = 1
    Me.txtScalingFactor.Spin.Visible = 1
    Cn.Errors.Clear
    
    'Add to Windows List
    AddToWindowList Me.Caption
     
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description

    'Cleanup objects before raising the error
    
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr , , , "AIM_ItemHistoryCopy::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
             
    'Close the recordset
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
    'Close the SQL Connection
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
    'Clear messages
    Write_Message ""

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        f_HandleErr , , , "AIM_ItemHistoryCopy::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
