VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItemExceptions 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Item Exceptions Report"
   ClientHeight    =   6765
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11250
   Icon            =   "AIM_ItemExceptions.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6765
   ScaleWidth      =   11250
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   180
      Top             =   6360
      _ExtentX        =   741
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_ItemExceptions.frx":030A
      ToolBars        =   "AIM_ItemExceptions.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   2130
      Left            =   60
      TabIndex        =   21
      Top             =   0
      Width           =   11070
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
         Bindings        =   "AIM_ItemExceptions.frx":3733
         Height          =   340
         Left            =   2750
         TabIndex        =   0
         Top             =   240
         Width           =   1710
         DataFieldList   =   "Userid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserId"
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   340
         Left            =   2750
         TabIndex        =   2
         Top             =   660
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_ItemExceptions.frx":374C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemExceptions.frx":37B8
         Key             =   "AIM_ItemExceptions.frx":37D6
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   340
         Left            =   2750
         TabIndex        =   3
         Top             =   1080
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_ItemExceptions.frx":381A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemExceptions.frx":3886
         Key             =   "AIM_ItemExceptions.frx":38A4
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMLocations 
         Bindings        =   "AIM_ItemExceptions.frx":38E8
         Height          =   340
         Left            =   2750
         TabIndex        =   4
         Top             =   1500
         Width           =   1710
         DataFieldList   =   "LcId"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "LcId"
      End
      Begin VB.CheckBox ckSummary 
         Caption         =   "Summary Exception Report"
         Enabled         =   0   'False
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   1
         Top             =   233
         Width           =   4020
      End
      Begin VB.Label Label1 
         Caption         =   "Location ID"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   25
         Top             =   1545
         Width           =   2430
      End
      Begin VB.Label label 
         Caption         =   "Vendor ID"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   24
         Left            =   200
         TabIndex        =   24
         Top             =   705
         Width           =   2430
      End
      Begin VB.Label label 
         Caption         =   "Assortment"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   25
         Left            =   200
         TabIndex        =   23
         Top             =   1125
         Width           =   2430
      End
      Begin VB.Label Label17 
         Caption         =   "Buyer ID"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   22
         Top             =   285
         Width           =   2430
      End
   End
   Begin VB.Frame Frame1 
      Caption         =   "Exception Tests"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   4065
      Left            =   30
      TabIndex        =   26
      Top             =   2160
      Width           =   11070
      Begin VB.CheckBox ckDyingItems 
         Caption         =   "Dying Items"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   14
         Top             =   1080
         Width           =   4020
      End
      Begin VB.CheckBox ckDemandFilter 
         Caption         =   "Demand Filter Exceptions"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   13
         Top             =   705
         Width           =   4020
      End
      Begin VB.CheckBox ckTrackingSignal 
         Caption         =   "Tracking Signal Exceptions"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   18
         Top             =   2580
         Width           =   4020
      End
      Begin VB.CheckBox ckTrend 
         Caption         =   "Trend Percentage Greater Than or Equal To"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   150
         TabIndex        =   9
         Top             =   1080
         Width           =   5190
      End
      Begin VB.CheckBox ckMAPE 
         Caption         =   "High Mean Absolute Error % Greater Than or Equal To"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   150
         TabIndex        =   7
         Top             =   705
         Width           =   5190
      End
      Begin VB.CheckBox ckBypass 
         Caption         =   "Forecast Update Bypass % Greater Than or Equal To"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   150
         TabIndex        =   5
         Top             =   330
         Width           =   5190
      End
      Begin VB.CheckBox ckUserDemand 
         Caption         =   "User Demand Override"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   20
         Top             =   3330
         Width           =   4020
      End
      Begin VB.CheckBox ckOnPromotion 
         Caption         =   "Items On Promotion"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   15
         Top             =   1455
         Width           =   4020
      End
      Begin VB.CheckBox ckOutOfStock 
         Caption         =   "Out of Stock"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   17
         Top             =   2205
         Width           =   4020
      End
      Begin VB.CheckBox ckNewItems 
         Caption         =   "New Items"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   16
         Top             =   1830
         Width           =   4020
      End
      Begin VB.CheckBox ckUnOrdered 
         Caption         =   "Unordered Items"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   19
         Top             =   2955
         Width           =   4020
      End
      Begin VB.CheckBox ckLinkages 
         Caption         =   "Database Linkages"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   340
         Left            =   6840
         TabIndex        =   12
         Top             =   330
         Width           =   4020
      End
      Begin TDBNumber6Ctl.TDBNumber txtBypassPct 
         Height          =   345
         Left            =   5385
         TabIndex        =   6
         Top             =   330
         Width           =   1125
         _Version        =   65536
         _ExtentX        =   1984
         _ExtentY        =   609
         Calculator      =   "AIM_ItemExceptions.frx":3901
         Caption         =   "AIM_ItemExceptions.frx":3921
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemExceptions.frx":398D
         Keys            =   "AIM_ItemExceptions.frx":39AB
         Spin            =   "AIM_ItemExceptions.frx":39F5
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "0.000;-0.000;0.000;0.000"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "0.000"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   0.3
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin TDBNumber6Ctl.TDBNumber txtMAPE 
         Height          =   345
         Left            =   5385
         TabIndex        =   8
         Top             =   705
         Width           =   1125
         _Version        =   65536
         _ExtentX        =   1984
         _ExtentY        =   609
         Calculator      =   "AIM_ItemExceptions.frx":3A1D
         Caption         =   "AIM_ItemExceptions.frx":3A3D
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemExceptions.frx":3AA9
         Keys            =   "AIM_ItemExceptions.frx":3AC7
         Spin            =   "AIM_ItemExceptions.frx":3B11
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   1479671813
      End
      Begin TDBNumber6Ctl.TDBNumber txtTrendPctHigh 
         Height          =   345
         Left            =   5385
         TabIndex        =   10
         Top             =   1080
         Width           =   1125
         _Version        =   65536
         _ExtentX        =   1984
         _ExtentY        =   609
         Calculator      =   "AIM_ItemExceptions.frx":3B39
         Caption         =   "AIM_ItemExceptions.frx":3B59
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemExceptions.frx":3BC5
         Keys            =   "AIM_ItemExceptions.frx":3BE3
         Spin            =   "AIM_ItemExceptions.frx":3C2D
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   0.1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtTrendPctLow 
         Height          =   345
         Left            =   5385
         TabIndex        =   11
         Top             =   1440
         Width           =   1125
         _Version        =   65536
         _ExtentX        =   1984
         _ExtentY        =   609
         Calculator      =   "AIM_ItemExceptions.frx":3C55
         Caption         =   "AIM_ItemExceptions.frx":3C75
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemExceptions.frx":3CE1
         Keys            =   "AIM_ItemExceptions.frx":3CFF
         Spin            =   "AIM_ItemExceptions.frx":3D49
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999.99
         MinValue        =   -999.99
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   -0.1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label2 
         Caption         =   "Or Lesser Than or Equal To"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   1830
         TabIndex        =   27
         Top             =   1440
         Width           =   3360
      End
   End
End
Attribute VB_Name = "AIM_ItemExceptions"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
Dim SAVersion As Integer
Dim m_SQLStmt As SQLBuilder        'SQLStmt.cls, defined as a Common File

Private Function SetFormCriteria()
On Error GoTo ErrorHandler

    Dim CompValue1 As String

    'Set Criteria Selection Controls
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "ById")
    Me.dcAIMUsers.Text = IIf(CompValue1 = "", getTranslationResource("All"), CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "VnId")
    Me.txtVnId.Text = IIf(CompValue1 = "", "", CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "Assort")
    Me.txtAssort = IIf(CompValue1 = "", "", CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "LcId")
    Me.dcAIMLocations.Text = IIf(CompValue1 = "", getTranslationResource("All"), CompValue1)
   
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(SetFormCriteria)", Err.Description
	f_HandleErr , , , "AIM_ItemExceptions::SetFormCriteria", Now, gDRGeneralError, True, Err
End Function

Private Function BldSqlStmt(ById As String, VnId As String, Assort As String, LcId As String)
On Error GoTo ErrorHandler

    Dim ExcptCount As Integer
    Dim Qualifier As String
    Dim SQL As String
    Dim WhrExpr As String
    
    'Clean up the SQL Statement
    m_SQLStmt.Criterias.DeleteColumn "Item", "ById"
    m_SQLStmt.Criterias.DeleteColumn "Item", "VnId"
    m_SQLStmt.Criterias.DeleteColumn "Item", "Assort"
    m_SQLStmt.Criterias.DeleteColumn "Item", "LcId"
    
    If ById <> getTranslationResource("All") _
    And ById <> "All" Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "Item.ById", False, False, "=", ById, ""
    End If
    
    If VnId <> "" Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add "And", "Item.VnId", False, False, "=", VnId, ""
    End If
    
    If Assort <> "" Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "Item.Assort", False, False, "=", Assort, ""
    End If
    
    If LcId <> getTranslationResource("All") _
    And LcId <> "All" Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "Item.LcId", False, False, "=", LcId, ""
    End If
    
    'Fetch SQL Statement
    SQL = m_SQLStmt.SQLExpr
    
    'Append Exception Conditions to the Where Statment
    If Me.ckBypass.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + "Item.BypassPct >= " + str(Me.txtBypassPct.Text)
    End If
    
    If Me.ckMAPE.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " (Item.FcstDemand > 0  and Item.MAE / Item.FcstDemand >= " + str(Me.txtMAPE.Text) + ")"
    End If
    
    If Me.ckTrend.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " (Item.FcstDemand > 0  and (Item.Trend / Item.FcstDemand >= " + str(Me.txtTrendPctHigh.Text) + _
           " or Item.Trend / Item.FcstDemand <= " + str(Me.txtTrendPctLow) + "))"
    End If
    
    If Me.ckUnOrdered.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.ItStat = 'U' "
    End If
    
    If Me.ckDemandFilter.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.DmdFilterFlag = 'Y' "
    End If
    
    If Me.ckDemandFilter.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.DmdFilterFlag = 'Y' "
    End If
    
    If Me.ckDyingItems.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.DIFlag = 'Y' "
    End If
    
    If Me.ckOnPromotion.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.OnPromotion = 'Y' "
    End If
    
    If Me.ckNewItems.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.ItStat = 'N' "
    End If
    
    If Me.ckTrackingSignal.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.TrkSignalFlag = 'Y' "
    End If
    
    If Me.ckUserDemand.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.UserDemandFlag = 'Y' "
    End If
    
    If Me.ckOutOfStock.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " Item.Oh + Item.Oo - Item.ComStk - Item.BkOrder - Item.BkComStk <= 0 "
    End If
    
    If Me.ckLinkages.Value = VALUETRUE Then
        WhrExpr = IIf(m_SQLStmt.Criterias.Count = 0 And ExcptCount = 0, "WHERE (", WhrExpr + IIf(ExcptCount = 0, " and (", " or "))
        ExcptCount = ExcptCount + 1
        WhrExpr = WhrExpr + " (isnull(AIMVendors.VName, 'Y') = 'Y' or isnull(AIMUsers.UserName, 'Y') = 'Y' "
        WhrExpr = WhrExpr + "or isnull(AIMOptions.OpDesc, 'Y') = 'Y' or isnull(AIMSeasons.SaDesc, 'Y') = 'Y' "
        WhrExpr = WhrExpr + "or isnull(AIMLocations.LName, 'Y') = 'Y') "
    End If
    
    'Close or expressions
    If ExcptCount > 0 Then
        WhrExpr = WhrExpr + ")"
    End If
    
    'Build the SQL Statement
    SQL = Trim(m_SQLStmt.SelExpr)
    SQL = IIf(Trim(m_SQLStmt.FromExpr) = "", SQL, SQL + vbCrLf + Trim(m_SQLStmt.FromExpr))
    SQL = IIf(Trim(m_SQLStmt.JoinExpr) = "", SQL, SQL + vbCrLf + Trim(m_SQLStmt.JoinExpr))
    SQL = IIf(Trim(m_SQLStmt.WhrExpr) = "", SQL, SQL + vbCrLf + Trim(m_SQLStmt.WhrExpr))
    SQL = IIf(Trim(WhrExpr) = "", SQL, SQL + vbCrLf + Trim(WhrExpr))
    SQL = IIf(Trim(m_SQLStmt.OrderExpr) = "", SQL, SQL + vbCrLf + Trim(m_SQLStmt.OrderExpr))

    BldSqlStmt = SQL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldSqlStmt)", Err.Description
	f_HandleErr , , , "AIM_ItemExceptions::BldSqlStmt", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim rsItemExceptions As ADODB.Recordset

    Dim Assort As String
    Dim ById As String
    Dim LcId As String
    Dim VnId As String
    Dim SQL As String
    Dim strMessage As String
    
    Dim Report As AIM_ItemException_Report
    
    Select Case Tool.ID
        Case "ID_Print", "ID_Preview"
            'Housekeeping
            Screen.MousePointer = vbHourglass
            strMessage = getTranslationResource("STATMSG01900")
            If StrComp(strMessage, "STATMSG01900") = 0 Then strMessage = "Building the Item Exception Report..."
            Write_Message strMessage
            
            'Build SQL Statement
            SQL = BldSqlStmt(Me.dcAIMUsers.Text, Me.txtVnId.Text, Me.txtAssort.Text, Me.dcAIMLocations.Text)
            
            'Open the report result set
            Set rsItemExceptions = New ADODB.Recordset
            rsItemExceptions.Open SQL, Cn, adOpenStatic, adLockReadOnly
            
            Write_Message ""
            Screen.MousePointer = vbNormal
            
            If f_IsRecordsetOpenAndPopulated(rsItemExceptions) Then
                
                Set Report = New AIM_ItemException_Report
                
                'Set the Control variables
                If Me.ckBypass.Value = VALUETRUE Then
                    Report.ByPass = True
                    Report.ByPassPct = Me.txtBypassPct.Value
                End If
                
                If Me.ckMAPE.Value = VALUETRUE Then
                    Report.MAPE = True
                    Report.MAPEPct = Me.txtMAPE.Value
                End If
                
                If Me.ckTrend.Value = VALUETRUE Then
                    Report.Trend = True
                    Report.TrendPctHigh = Me.txtTrendPctHigh.Value
                    Report.TrendPctLow = Me.txtTrendPctLow.Value
                End If
                
                If Me.ckLinkages.Value = VALUETRUE Then
                    Report.Linkages = True
                End If
                
                If Me.ckDemandFilter.Value = VALUETRUE Then
                    Report.DemandFilter = True
                End If
                
                If Me.ckDyingItems.Value = VALUETRUE Then
                    Report.DyingItems = True
                End If
                
                If Me.ckOnPromotion.Value = VALUETRUE Then
                    Report.OnPromotion = True
                End If
                
                If Me.ckNewItems.Value = VALUETRUE Then
                    Report.NewItems = True
                End If
                
                If Me.ckOutOfStock.Value = VALUETRUE Then
                    Report.OutOfStock = True
                End If
                
                If Me.ckTrackingSignal.Value = VALUETRUE Then
                    Report.TrackingSignal = True
                End If
                
                If Me.ckUnOrdered.Value = VALUETRUE Then
                    Report.UnOrdered = True
                End If
                
                If Me.ckUserDemand.Value = VALUETRUE Then
                    Report.UserDemand = True
                End If
                
                'Instance the Report
                Set Report.dcItem.Recordset = rsItemExceptions
                Set AIM_Reports.ARViewer.ReportSource = Report
                
                Select Case Tool.ID
                    Case "ID_Print"
                        Report.PrintReport True
                    
                    Case "ID_Preview"
                        Set AIM_Reports.ARViewer.ReportSource = Report
                        AIM_Reports.Show vbModal, AIM_Main
                    
                End Select
            Else
                strMessage = getTranslationResource("TEXTMSG05103")
                If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
                Write_Message strMessage
            End If
            
            'Wrap Up
            Set Report = Nothing
            
            If f_IsRecordsetValidAndOpen(rsItemExceptions) Then rsItemExceptions.Close
            Set rsItemExceptions = Nothing
            
            Set AIM_ItemException_Report = Nothing
            
        Case "ID_Filter"
            'Update the Sql Statment
            'Untranslate the "all"s
            If StrComp(Me.dcAIMUsers.Text, getTranslationResource("All")) = 0 Then
                ById = "All"
            Else
                ById = Me.dcAIMUsers.Text
            End If
            
            VnId = Me.txtVnId.Text
            Assort = Me.txtAssort.Text
            
            If StrComp(Me.dcAIMLocations.Text, getTranslationResource("All")) = 0 Then
                LcId = "All"
            Else
                LcId = Me.dcAIMLocations.Text
            End If
            
            BldSqlStmt ById, VnId, Assort, LcId
            
            Set AIM_SelectionCriteria.p_SQLStmt = m_SQLStmt
            Set AIM_SelectionCriteria.Cn = Cn
        
            AIM_SelectionCriteria.Show vbModal, AIM_Main
            
            SetFormCriteria
            
        Case "ID_Close"
            Unload Me
            Exit Sub
        
    End Select
    
Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsItemExceptions) Then rsItemExceptions.Close
    Set rsItemExceptions = Nothing
    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
	f_HandleErr , , , "AIM_ItemExceptions::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMLocations_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMLocations.Columns(0).Name = "lcid"
    Me.dcAIMLocations.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcAIMLocations.Columns(0).Width = 1000
    Me.dcAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(0).DataField = "lcid"
    
    Me.dcAIMLocations.Columns(1).Name = "lname"
    Me.dcAIMLocations.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcAIMLocations.Columns(1).Width = 2880
    Me.dcAIMLocations.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(1).DataField = "lname"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMLocations, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMLocations.Columns.Count - 1
'        dcAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMLocations_InitColumnProps)"
	f_HandleErr , , , "AIM_ItemExceptions::dcAIMLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "userid"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "userid"
    
    Me.dcAIMUsers.Columns(1).Name = "username"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = "username"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
	f_HandleErr , , , "AIM_ItemExceptions::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
	f_HandleErr , , , "AIM_ItemExceptions::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SQL As String
    Dim strMessage As String
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim rsAIMUsers As ADODB.Recordset
    Dim rsAIMLocations As ADODB.Recordset
    Dim rsSysCtrl As ADODB.Recordset

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01901")
    If StrComp(strMessage, "STATMSG01901") = 0 Then strMessage = "Initializing Item Exception Report..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    Set m_SQLStmt = New SQLBuilder
    
    'Initialize the System Control Stored Procedure
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
    End With
    
    'Get the Seasonality Version
    Set rsSysCtrl = New ADODB.Recordset
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        SAVersion = rsSysCtrl("CurSaVersion").Value
    Else
        SAVersion = 0
    End If
    
    'Build/Bind the AIM User Drop Down
    Me.dcAIMUsers.Text = AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text

    SQL = "SELECT UserID, UserName FROM AIMUsers ORDER BY UserID"
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open SQL, Cn, adOpenStatic, adLockReadOnly
    
    'Set generic option to fetch "All"
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Me.dcAIMUsers.AddItem getTranslationResource("All") + _
                    vbTab + getTranslationResource("All Users")
        'Get user names from recordset
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserId").Value + vbTab + rsAIMUsers("Username").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    
    'Build/Bind the AIM Locations Drop Down
    
    SQL = "SELECT LcID, LName FROM AIMLocations ORDER BY LcID"
    Set rsAIMLocations = New ADODB.Recordset
    rsAIMLocations.Open SQL, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        Me.dcAIMLocations.AddItem getTranslationResource("All") + _
                        vbTab + getTranslationResource("All Locations")
        
        Do Until rsAIMLocations.eof
            Me.dcAIMLocations.AddItem rsAIMLocations("LcId").Value + vbTab + rsAIMLocations("LName").Value
            rsAIMLocations.MoveNext
        Loop
    End If
    
    Me.dcAIMLocations.Text = getTranslationResource("All")
    
    'Set up the SQL Statement
    m_SQLStmt.SQLTables.Add "Item"
    m_SQLStmt.SQLTables.Add "AIMVendors"
    m_SQLStmt.SQLTables.Add "AIMUsers"
    m_SQLStmt.SQLTables.Add "AIMSeasons"
    m_SQLStmt.SQLTables.Add "AIMOptions"
    m_SQLStmt.SQLTables.Add "AIMLocations"
    
    SQL = "SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ItStat, Item.OptionID, "
    SQL = SQL + vbCrLf + "Item.VnId, Item.Assort, Item.ById, Item.SaId, "
    SQL = SQL + vbCrLf + "(Item.Oh + Item.Oo - Item.ComStk - Item.BkOrder - Item.BkComStk) AS AvailQty, "
    SQL = SQL + vbCrLf + "Item.FcstDemand, Item.MAE, Item.Trend, Item.DIFlag, Item.DmdFilterFlag, "
    SQL = SQL + vbCrLf + "Item.TrkSignalFlag, Item.UserDemandFlag, Item.ByPassPct, Item.OnPromotion, "
    SQL = SQL + vbCrLf + "AIMVendors.VName, AIMUsers.UserName, AIMOptions.OpDesc, AIMSeasons.SaDesc, "
    SQL = SQL + vbCrLf + "AIMLocations.LName "
    
    m_SQLStmt.SelExpr = SQL

    SQL = "FROM Item "
    SQL = SQL + vbCrLf + "LEFT OUTER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId " & _
            "AND Item.Assort = AIMVendors.Assort "
    SQL = SQL + vbCrLf + "LEFT OUTER JOIN AIMUsers ON Item.ById = AIMUsers.UserId "
    SQL = SQL + vbCrLf + "LEFT OUTER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId " & _
            "AND AIMSeasons.SaVersion = " + Format(SAVersion, "0") + " "
    SQL = SQL + vbCrLf + "LEFT OUTER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId "
    SQL = SQL + vbCrLf + "LEFT OUTER JOIN AIMLocations ON Item.Lcid = AIMLocations.Lcid "
        
    m_SQLStmt.FromExpr = SQL
    
    m_SQLStmt.OrderExpr = "ORDER BY Item.VnId, Item.Assort, Item.LcId, Item.Item "
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    '************************************************************
    'Mar 04 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtBypassPct.Spin.Visible = 1
    Me.txtMAPE.Spin.Visible = 1
    Me.txtTrendPctHigh.Spin.Visible = 1
    Me.txtTrendPctLow.Spin.Visible = 1
    '************************************************************
        
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption
	f_HandleErr , , , "AIM_ItemExceptions::Form_Load", Now, gDRGeneralError, True, Err
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    Set m_SQLStmt = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
		f_HandleErr , , , "AIM_ItemExceptions::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
