VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_TxnLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Data Interface Transaction File Lookup"
   ClientHeight    =   5940
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8475
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_TxnLookUp.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5940
   ScaleWidth      =   8475
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   7038
      Style           =   1  'Graphical
      TabIndex        =   2
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   5520
      Style           =   1  'Graphical
      TabIndex        =   3
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   2595
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   4080
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   5400
      Width           =   1365
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgTxns 
      Height          =   3315
      Left            =   78
      TabIndex        =   6
      Top             =   1905
      Width           =   8325
      _Version        =   196617
      Cols            =   4
      AllowDelete     =   -1  'True
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      TabNavigation   =   1
      _ExtentX        =   14684
      _ExtentY        =   5847
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1650
      Left            =   78
      TabIndex        =   7
      Top             =   120
      Width           =   8325
      Begin TDBDate6Ctl.TDBDate txtStartDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   340
         Left            =   108
         TabIndex        =   0
         Top             =   735
         Width           =   1700
         _Version        =   65536
         _ExtentX        =   2999
         _ExtentY        =   600
         Calendar        =   "AIM_TxnLookUp.frx":030A
         Caption         =   "AIM_TxnLookUp.frx":0422
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_TxnLookUp.frx":048E
         Keys            =   "AIM_TxnLookUp.frx":04AC
         Spin            =   "AIM_TxnLookUp.frx":050A
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   2
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/1999"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   36443
         CenturyMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7305
         TabIndex        =   1
         Top             =   1200
         Width           =   870
         _Version        =   65536
         _ExtentX        =   1535
         _ExtentY        =   600
         Calculator      =   "AIM_TxnLookUp.frx":0532
         Caption         =   "AIM_TxnLookUp.frx":0552
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_TxnLookUp.frx":05BE
         Keys            =   "AIM_TxnLookUp.frx":05DC
         Spin            =   "AIM_TxnLookUp.frx":0626
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   200
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label 
         Caption         =   "Maximum Rows"
         Height          =   300
         Index           =   0
         Left            =   5280
         TabIndex        =   9
         Top             =   1245
         Width           =   1935
      End
      Begin VB.Label Label 
         Caption         =   "Start Date"
         Height          =   300
         Index           =   1
         Left            =   108
         TabIndex        =   8
         Top             =   340
         Width           =   1700
      End
   End
End
Attribute VB_Name = "AIM_TxnLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Cn As ADODB.Connection
Dim rsTxns As ADODB.Recordset

Private CancelFlag As Boolean
Private EndDate As Date
Private StartDate As Date

Private Function BuildTxnLookupQuery() As String
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim WhrStmt As String
    
    StartDate = Me.txtStartDate.Value
    
    'Initialize SQL Statment
    strSQL = "SELECT 'Txn Set' = TxnSet, " & _
            "'File Name' = FileName, " & _
            "'Txn Date' = TxnDate " & _
            " FROM AIMDataExchangeCtrl "
    
    'Build the Where Statment
    WhrStmt = " WHERE TxnDate >= '" & CStr(StartDate) & "' "
    
    strSQL = strSQL & WhrStmt & "ORDER BY TxnDate, FileName "
    
    BuildTxnLookupQuery = strSQL
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BuildTxnLookupQuery)", Err.Description
     f_HandleErr , , , "AIM_TxnLookUp:::BuildTxnLookupQuery", Now, gDRGeneralError, True, Err
End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim strSQL As String
    
    Me.MousePointer = vbHourglass
    Write_Message ""
    
    'Create the query
    strSQL = BuildTxnLookupQuery()
    
    'Recreate the result set
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsTxns) Then rsTxns.Close
        If Err.Number = 3219 Then
            'Operation not valid in this context. (recordset's connection had apparently been closed at some point.)
            'Discard the rs and move on.
            Set rsTxns = Nothing
        End If
    Set rsTxns = Nothing
    On Error GoTo ErrorHandler
    
    Set rsTxns = New ADODB.Recordset
    rsTxns.CursorLocation = adUseClient
    rsTxns.CursorType = adOpenStatic
    rsTxns.LockType = adLockOptimistic
    Set rsTxns.ActiveConnection = Cn
    
    
    rsTxns.MaxRecords = Me.txtMaxRows.Value
    rsTxns.Open strSQL
    
    If f_IsRecordsetOpenAndPopulated(rsTxns) Then
        'Refresh the grid
        Set Me.dgTxns.DataSource = rsTxns
        'Me.dgTxns.ReBind
    Else
        If f_IsRecordsetValidAndOpen(rsTxns) Then rsTxns.Close
        Me.dgTxns.Reset
    End If
    
    'Return the screen values
    Me.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_TxnLookUp:::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_TxnLookUp:::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    SetTDBDate Me.txtStartDate, Date
    
    Me.txtMaxRows.Value = 200
    
    If f_IsRecordsetValidAndOpen(rsTxns) Then rsTxns.Close
    Me.dgTxns.Reset
        
    Me.txtStartDate.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_TxnLookUp:::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgTxns.Rows > 0 Then
        Screen.MousePointer = vbHourglass
        Me.dgTxns.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_TxnLookUp:::cmdPrint_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTxns_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Txn Data
    Me.dgTxns.Columns(0).Name = "txnset"
    Me.dgTxns.Columns(0).Caption = getTranslationResource("Transaction Set")
    Me.dgTxns.Columns(0).Width = 720
    Me.dgTxns.Columns(0).Locked = True
    
    Me.dgTxns.Columns(1).Name = "filename"
    Me.dgTxns.Columns(1).Caption = getTranslationResource("File Name")
    Me.dgTxns.Columns(1).Width = 2880
    Me.dgTxns.Columns(1).Locked = True
    
    Me.dgTxns.Columns(2).Name = "txndate"
    Me.dgTxns.Columns(2).Caption = getTranslationResource("Transaction Date")
    Me.dgTxns.Columns(2).Width = 1440
    Me.dgTxns.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgTxns.Columns(2).NumberFormat = gDateFormat
    Me.dgTxns.Columns(2).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgTxns, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgTxns.Columns.Count - 1
'        dgTxns.Columns(IndexCounter).HasHeadBackColor = True
'        dgTxns.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgTxns.Columns(IndexCounter).Locked = False Then dgTxns.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgTxns_InitColumnProps)"
     f_HandleErr , , , "AIM_TxnLookUp:::dgTxns_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTxns_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG05700")
    If StrComp(strMessage, "RPTMSG05700") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG05701")
    If StrComp(strMessage1, "RPTMSG05701") = 0 Then strMessage = "Vendor Lookup List"
    strMessage2 = getTranslationResource("RPTMSG05702")
    If StrComp(strMessage2, "RPTMSG05702") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & strMessage1 _
                & vbTab & strMessage2 & " <page number>"
    
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgTxns_PrintInitialize)"
     f_HandleErr , , , "AIM_TxnLookUp:::dgTxns_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTxns_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsTxns) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsTxns.MoveLast
        Else
            rsTxns.MoveFirst
        End If
    
    Else
        rsTxns.Bookmark = StartLocation
        If ReadPriorRows Then
            rsTxns.MovePrevious
        Else
            rsTxns.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        
        If rsTxns.BOF Or rsTxns.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsTxns("vnid").Value
        RowBuf.Value(i, 1) = rsTxns("assort").Value
        RowBuf.Value(i, 2) = rsTxns("vname").Value
        RowBuf.Value(i, 3) = rsTxns("dft_byid").Value

        RowBuf.Bookmark(i) = rsTxns.Bookmark
    
        If ReadPriorRows Then
            rsTxns.MovePrevious
        Else
            rsTxns.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgTxns_UnboundReadData)"
     f_HandleErr , , , "AIM_TxnLookUp:::dgTxns_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Set the default value for the Cancel Flag
    CancelFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_TxnLookUp:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05700")
    If StrComp(strMessage, "STATMSG05700") = 0 Then strMessage = "Initializing Data Interface Transaction File Lookup... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize fields
    SetTDBDate Me.txtStartDate, Date
    Me.txtMaxRows.Value = 200
    
    'Build initial record set
    Set rsTxns = New ADODB.Recordset
    rsTxns.CursorLocation = adUseClient
    rsTxns.CursorType = adOpenStatic
    rsTxns.LockType = adLockOptimistic
    Set rsTxns.ActiveConnection = Cn
    
    strSQL = BuildTxnLookupQuery()
    rsTxns.Open strSQL
    
    'Make the spin button visible
    Me.txtMaxRows.Spin.Visible = 1
    Me.txtStartDate.DropDown.Visible = 1
    
    'Bind the grid
    Set Me.dgTxns.DataSource = rsTxns
       
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_TxnLookUp:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsTxns) Then rsTxns.Close
    Set rsTxns = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE)
    Set Cn = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_TxnLookUp:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_TxnLookUp:::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_TxnLookUp:::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub
