VERSION 5.00
Object = "{831FDD16-0C5C-11D2-A9FC-0000F8754DA1}#2.0#0"; "Mscomctl.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.MDIForm AIM_Main 
   BackColor       =   &H8000000C&
   Caption         =   "SSA Distribution Replenishment"
   ClientHeight    =   9330
   ClientLeft      =   45
   ClientTop       =   270
   ClientWidth     =   9705
   Icon            =   "AIM_Main.frx":0000
   LinkTopic       =   "MDIForm1"
   Picture         =   "AIM_Main.frx":030A
   StartUpPosition =   2  'CenterScreen
   Visible         =   0   'False
   WindowState     =   2  'Maximized
   Begin ActiveToolBars.SSActiveToolBars tbAIM_Main 
      Left            =   120
      Top             =   45
      _ExtentX        =   582
      _ExtentY        =   582
      _Version        =   131083
      BackColor       =   -2147483638
      ToolBarsCount   =   1
      ToolsCount      =   68
      PersonalizedMenus=   0
      DisplayContextMenu=   0   'False
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Tools           =   "AIM_Main.frx":C6538
      ToolBars        =   "AIM_Main.frx":F0322
   End
   Begin MSComctlLib.StatusBar sbMessage 
      Align           =   2  'Align Bottom
      Height          =   375
      Left            =   0
      TabIndex        =   0
      Top             =   8955
      Width           =   9705
      _ExtentX        =   17119
      _ExtentY        =   661
      _Version        =   393216
      BeginProperty Panels {8E3867A5-8586-11D1-B16A-00C0F0283628} 
         NumPanels       =   4
         BeginProperty Panel1 {8E3867AB-8586-11D1-B16A-00C0F0283628} 
            AutoSize        =   1
            Object.Width           =   10001
            Object.ToolTipText     =   "Status"
         EndProperty
         BeginProperty Panel2 {8E3867AB-8586-11D1-B16A-00C0F0283628} 
            AutoSize        =   2
            Object.ToolTipText     =   "User ID"
         EndProperty
         BeginProperty Panel3 {8E3867AB-8586-11D1-B16A-00C0F0283628} 
            Style           =   6
            Alignment       =   1
            AutoSize        =   2
            Object.Width           =   2196
            MinWidth        =   2187
            TextSave        =   "11/30/2005"
            Object.ToolTipText     =   "Date"
         EndProperty
         BeginProperty Panel4 {8E3867AB-8586-11D1-B16A-00C0F0283628} 
            Style           =   5
            Alignment       =   1
            AutoSize        =   2
            Object.Width           =   1773
            MinWidth        =   1764
            TextSave        =   "2:36 PM"
            Object.ToolTipText     =   "Time"
         EndProperty
      EndProperty
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
End
Attribute VB_Name = "AIM_Main"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim FirstTime As Boolean
Dim HelpFileName As String

Function SetMenuOptions()
On Error GoTo ErrorHandler

    Static GotList As Boolean
    
    Dim Cn As ADODB.Connection
    Dim rsAIMUsers As ADODB.Recordset
    Dim SqlStmt As String
    Dim strSQL As String
    Dim rsAIMRoles As ADODB.Recordset
    Dim strRoles As String
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    Dim CurrentRev As String
    
    GetDBAccessLevel    'SUJIT030204
    With Me.tbAIM_Main
        If gLogOnStatus = True Then
'            '!! If Application release version requires a second minor version, then
'            '   set autoincrement off;
'            '   set the revision to a specific number (indicating the second minor version);
'            '   and use the following caption (Comment out following False condition):
            CurrentRev = "2"
            Me.Caption = Me.Caption & " " & _
                        App.Major & "." & _
                        App.Minor & "." & _
                        CurrentRev & _
                        " (" & gServer & ":" & gDataBase & ")"
'            '!!Else, leave auto increment on
'            '   and use the following caption (Comment out preceding True condition):
'            Me.Caption = Me.Caption & " " & _
'                        App.Major & "." & _
'                        App.Minor & "." & _
'                        " (" & gServer & ":" & gDataBase & ")"
'            '!!End If
            
            .Tools("ID_Logon").Enabled = False
            .Tools("ID_mnuTableMaintenance").Enabled = True
            .Tools("ID_mnuUtilities").Enabled = True
            .Tools("ID_mnuSetup").Enabled = True
            .Tools("ID_Buyer").Enabled = True
            .Tools("ID_Reports").Enabled = True
            .Tools("ID_mnuBuyerReview").Enabled = True
            .Tools("ID_mnuAllocation").Enabled = True
            .Tools("ID_User_password").Enabled = False      'SUJIT030204
            
            If Not GotList Then
                Set Cn = New ADODB.Connection
                SQLConnection Cn, CONNECTION_OPEN, False
               'Build the buyers list
                Set rsAIMUsers = New ADODB.Recordset
                
                strSQL = "SELECT RoleID FROM AIMUsers WHERE UserID = N'" & Trim$(gUserID) & "'"
                rsAIMUsers.Open strSQL, Cn
                gRole = rsAIMUsers("RoleID")
                rsAIMUsers.Close
                                
                strSQL = "DECLARE @@TOTROLES as nvarchar(4000) EXEC AIM_getSubRole_SP N'" & Trim$(gRole) & "', @@TOTROLES OUTPUT"
                Set rsAIMRoles = New ADODB.Recordset
                rsAIMRoles.Open strSQL, Cn
                If Not rsAIMRoles.eof Then
                    strRoles = rsAIMRoles(0)
                End If
                
                If f_IsRecordsetValidAndOpen(rsAIMRoles) Then rsAIMRoles.Close
                Set rsAIMRoles = Nothing
                
                strSQL = "SELECT UserID FROM AIMUsers WHERE RoleID IN (" & Trim$(strRoles) & ")"
                rsAIMUsers.Open strSQL, Cn
                
                If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
                    rsAIMUsers.MoveFirst
                    Do Until rsAIMUsers.eof
                        AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.AddItem rsAIMUsers("UserId").Value
                        rsAIMUsers.MoveNext
                    Loop
                Else
                    AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.AddItem gUserID
                End If
                
                AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text = gUserID
                
                GotList = True
            End If
        Else
            .Tools("ID_Logon").Enabled = True
            .Tools("ID_mnuTableMaintenance").Enabled = False
            .Tools("ID_mnuUtilities").Enabled = False
            .Tools("ID_mnuSetup").Enabled = False
            .Tools("ID_Buyer").Enabled = False
            .Tools("ID_Reports").Enabled = False
            .Tools("ID_mnuBuyerReview").Enabled = False
        End If
    End With
    
    'SUJIT
    If g_UpdatePassword Then
        'Me.tbAIM_Main.Tools("ID_User_password ").Visible = True
        If UCase$(gUserID) <> "SA" Then
            Me.tbAIM_Main.Tools("ID_User_password").Enabled = True
            Me.tbAIM_Main.Tools("ID_User_password").Visible = False
        Else
            Me.tbAIM_Main.Tools("ID_User_password").Enabled = True
            Me.tbAIM_Main.Tools("ID_User_password").Visible = False
        End If
    Else
        Me.tbAIM_Main.Tools("ID_User_password").Enabled = True
        Me.tbAIM_Main.Tools("ID_User_password").Visible = False
    End If
    
CleanUp:
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMRoles) Then rsAIMRoles.Close
    Set rsAIMRoles = Nothing
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(SetMenuOptions)"
     f_HandleErr , , , "AIM_Main::SetMenuOptions", Now, gDRGeneralError, True, Err
End Function

Private Sub MDIForm_Activate()
On Error GoTo ErrorHandler
    
    Dim freeFileIndex As Integer
    
    If FirstTime Then
        LOGON.Show vbModal, AIM_Main
        
        'Set HelpFileName; Define default; INTERNATIONALIZATION: Get Localised version
        Select Case gLocaleID   'Jan21
            Case g_LOCALEID_EN_US   'en-us
                HelpFileName = App.Path & "\Help\" & AIM_HELPFILE & ".chm"
            
            Case Else   'other languages
                HelpFileName = App.Path & "\Help\" & AIM_HELPFILE & "_" & str(gLocaleID) & ".chm"
                
        End Select
        'Default, if localised file not found
        If StrComp(HelpFileName, (AIM_HELPFILE & ".chm"), vbTextCompare) <> 0 Then
            On Error Resume Next
            freeFileIndex = FreeFile
            Open HelpFileName For Input Access Read Shared As freeFileIndex
            If Err.Number = 53 Then 'File Not Found
                Err.Clear
                HelpFileName = App.Path & "\Help\" & AIM_HELPFILE & ".chm"
            End If
            On Error GoTo ErrorHandler
            Close
        End If
        
        If gLangID <> "" Then
            'Internationalize
            GetTranslatedCaptions Me
            'tbAIM_Main.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_ACTIVETOOLBARS, COLOR_TYPE_PRIMARY)
        End If
        
        
        Me.Visible = True
        SetMenuOptions
        Me.tbAIM_Main.Redraw = True
        FirstTime = False
        Me.sbMessage.Panels(2).Text = gUserID

    End If
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(MDIForm_Activate)"
     f_HandleErr , , , "AIM_Main::MDIForm_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub MDIForm_Load()
On Error GoTo ErrorHandler
    
    Me.Visible = False
    
    '*****REVIEW
    'Splash Screen Warning!!!  uncomment for GA Release of Product. Comment out for beta/internal release.
    Me.Picture = LoadPicture()
    FirstTime = True

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(MDIForm_Load)"
     f_HandleErr , , , "AIM_Main::MDIForm_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub MDIForm_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strText As String
    
    'Check for an open form
    If Forms.Count > 1 Then
        Exit Sub
    End If
    
    If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US 'default to english
    strMessage = LoadResString(gLangIndex + 21) '"Exit from SSA DR?"
    strText = LoadResString(gLangIndex + 0)
    RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
    If RtnCode <> vbYes Then
        Cancel = True
    Else
        CleanUpGlobals
        HelpExit            'SUJIT10222003 TO EXIT FROM HTML HELP
        End
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(MDIForm_Unload)"
     f_HandleErr , , , "AIM_Main::MDIForm_Unload", Now, gDRGeneralError, True, Err
End Sub

Private Sub sbMessage_PanelClick(ByVal Panel As MSComctlLib.Panel)

End Sub

Private Sub tbAIM_Main_ComboCloseUp(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    POLimitCalc
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbAIM_Main_ComboCloseUp)"
     f_HandleErr , , , "AIM_Main::tbAIM_Main_ComboCloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbAIM_Main_ToolChange(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    POLimitCalc

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbAIM_Main_ToolChange)"
     f_HandleErr , , , "AIM_Main::tbAIM_Main_ToolChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbAIM_Main_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim AimFunction As Integer
    Dim AimFunctionDesc As String
    Dim iForms As Integer
    Dim RtnCode As Integer
    Dim strMessage, strMessage1 As String
    Dim strText As String
        
    'Check for tool update
    If gbUpdating Then Exit Sub

    'Validate Access
    AimFunction = m_VerifyAccessLevel(Tool)
    If AimFunction > 0 Then
        AimFunctionDesc = ""
        gAccessLvl = VerifyAccess(gUserID, AimFunction, AimFunctionDesc)
        Select Case gAccessLvl
            Case g_ACCESS_NONE          'No Access
                strMessage = getTranslationResource("MSGBOX00101")
                If StrComp(strMessage, "MSGBOX00101") = 0 Then strMessage = "Access to"
                strMessage1 = getTranslationResource("MSGBOX00102")
                If StrComp(strMessage1, "MSGBOX00102") = 0 Then strMessage1 = "has not been granted."
                strText = getTranslationResource("MSGBOX00103")
                If StrComp(strText, "MSGBOX00103") = 0 Then strText = "SSA DR Access Verification"
                'Note: AimFunctionDesc has already been translated in VerifyAccess
                MsgBox strMessage & " " & AimFunctionDesc & " " & strMessage1, _
                    vbCritical, strText
                Exit Sub
            
            Case g_ACCESS_READ          'Read Only Access
                'Lock down all controls
                
            Case g_ACCESS_CREATEMODIFY          'Full Access
                'Unlock all applicable controls - Default
        End Select
    End If
    'If access granted, launch approp. form
    LaunchForm Tool
    
    ' If the Tool that was clicked belongs to the WindowList group...
    If Tool.Group = "Windows" Then
        'then iterate through Forms collection and set focus to document
        For iForms = 0 To Forms.Count - 1
            If Forms(iForms).Caption = Tool.ID Then
                Forms(iForms).SetFocus
                Exit For                'Once a matching form is found, stop looking
            End If
        Next iForms
    End If

Exit Sub
ErrorHandler:
    If Err.Number = 5 Then
    'Occurs on Forms.setFocus -- need to ignore this until a solution is found.
        Resume Next
    Else
        'f_HandleErr Me.Caption & "(tbAIM_Main_ToolClick)"
         f_HandleErr , , , "AIM_Main::tbAIM_Main_ToolClick", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Function m_VerifyAccessLevel(ByRef Tool As ActiveToolBars.SSTool) As Long
On Error GoTo ErrorHandler
    'Verify Access Level
    Select Case Tool.ID
        
        Case "ID_BuyerReview"
            m_VerifyAccessLevel = AF_BUYER_REVIEW   '1
        Case "ID_BuyerReviewWorksheet"
            m_VerifyAccessLevel = AF_BUYER_REVIEW_WORKSHEET   '2
        'not used: 3 -- initial seasonal review
        Case "ID_ReleaseUnorderedItems"
            m_VerifyAccessLevel = AF_RELEASE_UNORDERED_ITEMS   '4
        Case "ID_StockStatusReport"
            m_VerifyAccessLevel = AF_STOCK_STATUS_REPORT   '5
        Case "ID_SuggestedReviewCycles"
            m_VerifyAccessLevel = AF_SUGGESTED_REVIEW_CYCLES_REPORT   '6
        Case "ID_ItemMaintenance"
            m_VerifyAccessLevel = AF_ITEM_MAINTENANCE   '7
        'not used: 8 -- unknown
        Case "ID_LocationsMaintenance"
            m_VerifyAccessLevel = AF_LOCATIONS_MAINTENANCE   '9
        Case "ID_OptionsMaintenance"
            m_VerifyAccessLevel = AF_OPTIONS_MAINTENANCE   '10
        Case "ID_PromotionsMaintenance"
            m_VerifyAccessLevel = AF_PROMOTIONS_MAINTENANCE   '11
        Case "ID_ReviewCycleMaintenance"
            m_VerifyAccessLevel = AF_REVIEW_CYCLE_MAINTENANCE   '12
        Case "ID_SeasonsMaintenance"
            m_VerifyAccessLevel = AF_SEASONALITY_PROFILE_MAINTENANCE   '13
        Case "ID_VendorsMaintenance"
            m_VerifyAccessLevel = AF_VENDOR_ASSORTMENT_MAINTENANCE   '14
        Case "ID_ItemReport"
            m_VerifyAccessLevel = AF_ITEM_SUMMARY_DETAIL_REPORT   '15
        Case "ID_LocationsReport"
            m_VerifyAccessLevel = AF_LOCATIONS_REPORT   '16
        Case "ID_OptionsReport"
            m_VerifyAccessLevel = AF_OPTIONS_REPORT   '17
        Case "ID_PromotionsReport"
            m_VerifyAccessLevel = AF_PROMOTIONS_REPORT   '18
        Case "ID_ReviewCycleReport"
            m_VerifyAccessLevel = AF_REVIEW_CYCLE_REPORT   '19
        Case "ID_SeasonalityProfileReport"
            m_VerifyAccessLevel = AF_SEASONALITY_REPORT   '20
        Case "ID_VendorsReport"
            m_VerifyAccessLevel = AF_VENDOR_ASSORTMENT_REPORT   '21
        Case "ID_ItemExceptionsReport"
            m_VerifyAccessLevel = AF_ITEM_EXCEPTIONS_REPORT   '22
        'not used: 23 -- Item/Location Report
        'not used: 24 -- Location/Item Report
        Case "ID_ForecastSimulator"
            m_VerifyAccessLevel = AF_FORECAST_SIMULATOR   '25
        Case "ID_JobScheduling"
            m_VerifyAccessLevel = AF_JOB_SCHEDULING    '26
        Case "ID_SystemControlTable"
            m_VerifyAccessLevel = AF_SYSTEM_CONTROL_MAINTENANCE   '27
        Case "ID_AIMCalendarMaintenance"
            m_VerifyAccessLevel = AF_AIM_CALENDAR_MAINTENANCE   '28
        Case "ID_AIMUsers"
            m_VerifyAccessLevel = AF_AIM_USERS_MAINTENANCE   '29
        Case "ID_MethodsMaintenance"
            m_VerifyAccessLevel = AF_FORECAST_METHODS_MAINTENANCE   '30
        Case "ID_ItemDefaults"
            m_VerifyAccessLevel = AF_ITEM_DEFAULTS_MAINTENANCE   '31
        Case "ID_ItemStatusCodes"
            m_VerifyAccessLevel = AF_ITEM_STATUS_MAINTENANCE   '32
        Case "ID_VelocityCodePercentages"
            m_VerifyAccessLevel = AF_VELOCITY_CODE_PERCENTAGES   '33
        Case "ID_TransferManagement"
            m_VerifyAccessLevel = AF_TRANSFER_MANAGEMENT   '34
'        Case "ID_JobWizard"
'            m_VerifyAccessLevel = AF_JOB_WIZARD   '35
        Case "ID_IntermittentAnalysis"
            m_VerifyAccessLevel = AF_INTERMITTENT_ANALYSIS   '36
        '37 = Ad Hoc Ord. Gen  -- called from within Buyer Review
        Case "ID_BuyerStatus"
            m_VerifyAccessLevel = AF_BUYER_STATUS   '38
        Case "ID_ViewJobLog"
            m_VerifyAccessLevel = AF_VIEW_JOB_LOG   '39
        Case "ID_DataExchangeTxnLookUp"
            m_VerifyAccessLevel = AF_DATA_INTERFACE_TRANSACTIONS   '40
        Case "ID_ForecastMaintenance"
            m_VerifyAccessLevel = AF_FORECAST_MAINTENANCE   '41
        Case "ID_ForecastGenerator"
            m_VerifyAccessLevel = AF_FORECAST_GENERATOR   '42
        Case "ID_InitializeNewLocation"
            m_VerifyAccessLevel = AF_INITIALIZE_NEW_LOCATION   '43
        Case "ID_ForecastOverride"
            m_VerifyAccessLevel = AF_FORECAST_SAFETY_STOCK_OVERRIDE   '44
        Case "ID_ClassMaintenance"
            m_VerifyAccessLevel = AF_CLASS_MAINTENANCE   '45
        Case "ID_ForecastModification"
            m_VerifyAccessLevel = AF_FORECAST_MODIFICATION   '46
        Case "ID_ItemHistoryCopy"
            m_VerifyAccessLevel = AF_ITEM_HISTORY_COPY   '47
        Case "ID_ProdConstraintsMaintenance"
            m_VerifyAccessLevel = AF_PRODUCTION_CONSTRAINTS_MAINTENANCE   '48
        Case "ID_AllocationReview"
            m_VerifyAccessLevel = AF_ALLOCATION_REVIEW   '53
        Case "ID_ForecastSetup"
            m_VerifyAccessLevel = AF_FORECASTSETUP       '54
        Case "ID_DemandPlanner"
            m_VerifyAccessLevel = AF_DEMANDPLANNER        '55
        Case "ID_AIMRoles"
            m_VerifyAccessLevel = AF_AIMROLES        '56
        Case "ID_CompanionMaintenance"
            m_VerifyAccessLevel = AF_COMP_DEMAND_MOD_MAINT        '57
        Case "ID_DestinationDefault"
            m_VerifyAccessLevel = AF_DESTINATION_DEFAULTS        '58
        Case "ID_User_password"
            m_VerifyAccessLevel = AF_USER_PASSWORD                     '59
                 Case "ID_LogView"
            m_VerifyAccessLevel = AF_VIEW_LOG           '60
            
        Case Else
            m_VerifyAccessLevel = 0
    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(m_VerifyAccessLevel)"
     f_HandleErr , , , "AIM_Main::m_VerifyAccessLevel", Now, gDRGeneralError, True, Err
End Function

Private Function LaunchForm(p_Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strText As String
    Dim Cn As ADODB.Connection
    
    Select Case p_Tool.ID
        'File
        Case "ID_Logon"
            LOGON.Show vbModal, Me
            
            SetMenuOptions
            
        Case "ID_Exit"
            Unload Me
        
        'Buyer Review
        Case "ID_BuyerReview"
            If Not FormExists(AIM_BuyerReview.Caption) Then
                AIM_BuyerReview.Show vbModeless
                Set AIM_BuyerReview = Nothing
            End If
    
        Case "ID_BuyerReviewWorksheet"
            If Not FormExists(AIM_BuyerReviewReport.Caption) Then
                AIM_BuyerReviewReport.Show vbModeless
            End If
            
        Case "ID_TransferManagement"
            If Not FormExists(AIM_TransferManagement.Caption) Then
                AIM_TransferManagement.Show vbModeless
            End If
    
        Case "ID_ReleaseUnorderedItems"
            If Not FormExists(AIM_ReleaseUnorderedItems.Caption) Then
                AIM_ReleaseUnorderedItems.Show vbModeless
            End If
            
        Case "ID_StockStatusReport"
                If Not FormExists(AIM_StockStatus.Caption) Then
                    AIM_StockStatus.Show vbModeless
                End If
            
        Case "ID_SuggestedReviewCycles"
            If Not FormExists(AIM_SuggestedReviewCycles.Caption) Then
                AIM_SuggestedReviewCycles.Show vbModeless
            End If
        
        'Table Maintenance
        Case "ID_ItemMaintenance"
            If Not FormExists(AIM_ItemMaintenance.Caption) Then
                AIM_ItemMaintenance.Show vbModeless
                Set AIM_ItemMaintenance = Nothing
            End If
        
        Case "ID_LocationsMaintenance"
            If Not FormExists(AIM_LocationsMaintenance.Caption) Then
                AIM_LocationsMaintenance.Show vbModeless
                Set AIM_LocationsMaintenance = Nothing
            End If
         
        Case "ID_OptionsMaintenance"
            If Not FormExists(AIM_OptionsMaintenance.Caption) Then
                AIM_OptionsMaintenance.Show vbModeless
                Set AIM_OptionsMaintenance = Nothing
            End If
        
        Case "ID_PromotionsMaintenance"
            If Not FormExists(AIM_PromotionsMaintenance.Caption) Then
                AIM_PromotionsMaintenance.Show vbModeless
                Set AIM_PromotionsMaintenance = Nothing
            End If
            
        Case "ID_ProdConstraintsMaintenance"
            If Not FormExists(AIM_ProductionConstraints.Caption) Then
                AIM_ProductionConstraints.Show vbModeless
                Set AIM_ProductionConstraints = Nothing
            End If
        
        Case "ID_ReviewCycleMaintenance"
            If Not FormExists(AIM_ReviewCycleMaintenance.Caption) Then
                AIM_ReviewCycleMaintenance.Show vbModeless
                Set AIM_ReviewCycleMaintenance = Nothing
            End If
            
        Case "ID_SeasonsMaintenance"
            If Not FormExists(AIM_SeasonalityProfiles.Caption) Then
                AIM_SeasonalityProfiles.Show vbModeless
                Set AIM_SeasonalityProfiles = Nothing
            End If
            
        Case "ID_VendorsMaintenance"
            If Not FormExists(AIM_VendorMaintenance.Caption) Then
                AIM_VendorMaintenance.Show vbModeless
                Set AIM_VendorMaintenance = Nothing
            End If
        Case "ID_CompanionMaintenance"
           If Not FormExists(AIM_Companion_Item_Maintenance.Caption) Then
                AIM_Companion_Item_Maintenance.Show vbModeless
                Set AIM_Companion_Item_Maintenance = Nothing
            End If
            
        'Reports
        Case "ID_ItemReport"
            If Not FormExists(AIM_ItemSummary.Caption) Then
                AIM_ItemSummary.Show vbModeless
            End If
        
        Case "ID_ItemExceptionsReport"
            If Not FormExists(AIM_ItemExceptions.Caption) Then
                AIM_ItemExceptions.Show vbModeless
            End If
        
        Case "ID_LocationsReport"
            If Not FormExists(AIM_LocationsReport.Caption) Then
                AIM_LocationsReport.Show vbModeless
            End If
            
        Case "ID_OptionsReport"
            If Not FormExists(AIM_OptionsReport.Caption) Then
                AIM_OptionsReport.Show vbModeless
            End If
        
        Case "ID_PromotionsReport"
            If Not FormExists(AIM_PromotionsReport.Caption) Then
                AIM_PromotionsReport.Show vbModeless
            End If
            
        Case "ID_ReviewCycleReport"
            If Not FormExists(AIM_ReviewCycleReport.Caption) Then
                AIM_ReviewCycleReport.Show vbModeless
            End If
        
        Case "ID_SeasonalityProfileReport"
            If Not FormExists(AIM_SeasonalityProfileReport.Caption) Then
                AIM_SeasonalityProfileReport.Show vbModeless
            End If
        
        Case "ID_VendorsReport"
            If Not FormExists(AIM_VendorReport.Caption) Then
                AIM_VendorReport.Show vbModeless
            End If
        
        'Utilities
        Case "ID_BuyerStatus"
            If Not FormExists(AIM_BuyerStatus.Caption) Then
                AIM_BuyerStatus.Show vbModeless
                Set AIM_BuyerStatus = Nothing
            End If
        
        Case "ID_ForecastSimulator"
            If Not FormExists(AIM_ForecastSimulator.Caption) Then
                AIM_ForecastSimulator.Show vbModeless
                Set AIM_ForecastSimulator = Nothing
            End If
            
        Case "ID_IntermittentAnalysis"
            If Not FormExists(AIM_IntermittentAnalysis.Caption) Then
                AIM_IntermittentAnalysis.Show vbModeless
            End If
            
        Case "ID_JobScheduling"
            If Not FormExists(AIM_JobScheduler.Caption) Then
                AIM_JobScheduler.Show vbModeless
            End If
            
'        Case "ID_JobWizard"
'            AIM_JobWizard.Show vbModeless, Me
'            Set AIM_JobWizard = Nothing
'
        'sgajjela
        Case "ID_ViewJobLog"
            If gWantToLogToDB = True Then
                'If Not FormExists(AIM_ErrorLookup.Caption) Then
                    AIM_ErrorLookup.Show vbModal, AIM_Main
                    Set AIM_ErrorLookup = Nothing
                'End If
            Else
        
                'If Not FormExists(AIM_JobLog.Caption) Then
                        AIM_JobLog.Show vbModal, AIM_Main
                        Set AIM_JobLog = Nothing
                'End If
            End If
            
        Case "ID_ForecastSetup"
            If Not FormExists(AIM_ForecastSetup.Caption) Then
                AIM_ForecastSetup.Show vbModeless
                Set AIM_ForecastSetup = Nothing
            End If

         Case "ID_FcstPlanning"
            If Not FormExists(AIM_ForecastPlanner.Caption) Then
                AIM_ForecastPlanner.Show vbModeless
                Set AIM_ForecastPlanner = Nothing
            End If
                
        Case "ID_ForecastOverride"
            If Not FormExists(AIM_ForecastOverride.Caption) Then
                AIM_ForecastOverride.Show vbModeless
                Set AIM_ForecastOverride = Nothing
            End If
        
        Case "ID_DataExchangeTxnLookUp"
            AIM_TxnLookUp.Show vbModeless, Me
            
        'Setup
        Case "ID_ClassMaintenance"
            If Not FormExists(AIM_ClassMaintenance.Caption) Then
                AIM_ClassMaintenance.Show vbModeless
                Set AIM_ClassMaintenance = Nothing
            End If
        
        Case "ID_AIMCalendarMaintenance"
            If Not FormExists(AIM_Calendar.Caption) Then
                AIM_Calendar.Show vbModeless
                Set AIM_Calendar = Nothing
            End If
            
        Case "ID_AIMUsers"
            If Not FormExists(AIM_UsersMaintenance.Caption) Then
                AIM_UsersMaintenance.Show vbModeless
                Set AIM_UsersMaintenance = Nothing
            End If
        
        Case "ID_AIMRoles"
            If Not FormExists(AIM_RolesMaintenance.Caption) Then
                AIM_RolesMaintenance.Show vbModeless
                Set AIM_RolesMaintenance = Nothing
            End If
            
        Case "ID_User_password"
            If Not FormExists(AIM_UsersPassword.Caption) Then
                AIM_UsersPassword.Show vbModeless
                Set AIM_UsersPassword = Nothing
            End If
            
        Case "ID_ItemDefaults"
            If Not FormExists(AIM_ItDefaultsMaintenance.Caption) Then
                AIM_ItDefaultsMaintenance.Show vbModeless
                Set AIM_ItDefaultsMaintenance = Nothing
            End If
            
        Case "ID_ItemStatusCodes"
            If Not FormExists(AIM_ItemStatusMaintenance.Caption) Then
                AIM_ItemStatusMaintenance.Show vbModeless
                Set AIM_ItemStatusMaintenance = Nothing
            End If
            
        Case "ID_MethodsMaintenance"
            If Not FormExists(AIM_ForecastMethodsMaintenance.Caption) Then
                AIM_ForecastMethodsMaintenance.Show vbModeless
                Set AIM_ForecastMethodsMaintenance = Nothing
            End If
        
        Case "ID_SystemControlTable"
            If Not FormExists(AIM_SysCtrlMaintenance.Caption) Then
                AIM_SysCtrlMaintenance.Show vbModeless
                Set AIM_SysCtrlMaintenance = Nothing
            End If
            
        Case "ID_VelocityCodePercentages"
            If Not FormExists(AIM_VelocityCodePercentages.Caption) Then
                AIM_VelocityCodePercentages.Show vbModeless
                Set AIM_VelocityCodePercentages = Nothing
            End If
            
        Case "ID_InitializeNewLocation"
            If Not FormExists(AIM_InitializeNewLocation.Caption) Then
                AIM_InitializeNewLocation.Show vbModeless
                Set AIM_InitializeNewLocation = Nothing
            End If
         Case "ID_ItemHistoryCopy"
            If Not FormExists(AIM_ItemHistoryCopy.Caption) Then
                AIM_ItemHistoryCopy.Show vbModeless
                Set AIM_ItemHistoryCopy = Nothing
            End If
        Case "ID_Connections"
            strMessage = getTranslationResource("MSGBOX00104")
            If StrComp(strMessage, "MSGBOX00104") = 0 Then strMessage = "Connections"
            strText = getTranslationResource("MSGBOX00105")
            If StrComp(strText, "MSGBOX00105") = 0 Then strText = "SQL Connections"
            MsgBox strMessage & " " & Format(gNbrConnections, "0") & "      ", _
                    vbOKOnly + vbInformation, strText
            
        'Windows
        Case "ID_TileH"
            AIM_Main.Arrange vbTileHorizontal
                
        Case "ID_TileV"
            AIM_Main.Arrange vbTileVertical
        
        Case "ID_Cascade"
            AIM_Main.Arrange vbCascade
            
        Case "ID_Arrange"
            AIM_Main.Arrange vbArrangeIcons
                            
        Case "ID_SSADRLogView"
            
             If gWantToLogToDB = True Then
                'If Not FormExists(AIM_ErrorLookup.Caption) Then
                    AIM_ErrorLookup.Show vbModal, AIM_Main
                    Set AIM_ErrorLookup = Nothing
                'End If
            Else
                'If Not FormExists(AIM_JobLog.Caption) Then
                        AIM_JobLog.Show vbModal, AIM_Main
                        Set AIM_JobLog = Nothing
                'End If
            End If
                 
        'Help
         Case "ID_Contents"
            'HTMLHelp 0, HelpFileName, HH_DISPLAY_INDEX, 0
            '****SUJIT10222003 CALL NEW FORMAT HTML HELP
            Set Cn = New ADODB.Connection
            SQLConnection Cn, CONNECTION_OPEN, False
            Popup_Help "", "", Cn
            SQLConnection Cn, CONNECTION_CLOSE, False
            Set Cn = Nothing
            '****SUJIT10222003 CALL NEW FORMAT HTML HELP

            HTMLHelp 0, HelpFileName, HH_DISPLAY_TOC, 0
            
'        Case "ID_Index"
'            'HTMLHelp 0, HelpFileName, HH_DISPLAY_INDEX, 0
'            '****SUJIT10222003 CALL NEW FORMAT HTML HELP
'            Set Cn = New ADODB.Connection
'            SQLConnection Cn, CONNECTION_OPEN, False
'            Popup_Help "", "", Cn
'            SQLConnection Cn, CONNECTION_CLOSE, False
'            Set Cn = Nothing
'            '****SUJIT10222003 CALL NEW FORMAT HTML HELP

        Case "ID_About"
            AIM_About.Show vbModal, Me
            
        'Allocation
         Case "ID_AllocationReview"
           If Not FormExists(AIM_AllocationReview.Caption) Then
                AIM_AllocationReview.Show vbModeless
                Set AIM_AllocationReview = Nothing
            End If
        Case "ID_DestinationDefault"
           If Not FormExists(AIM_DestinationDefault.Caption) Then
                AIM_DestinationDefault.Show vbModeless
                Set AIM_DestinationDefault = Nothing
            End If
        
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(LaunchForm)"
     f_HandleErr , , , "AIM_Main::LaunchForm", Now, gDRGeneralError, True, Err
End Function

'sujit
Public Function POLimitCalc()
    Dim Cn As ADODB.Connection
    Dim RtnCode As Integer
    Dim rsPOLimit As ADODB.Recordset
    Dim dblPolimit As Double
    Dim curUser, strSQL As String
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Function
    
    g_UserPOLimit = 0
    g_bUserPOLT = False
    curUser = Trim$(tbAIM_Main.Tools.Item(18).ComboBox.Text)
    strSQL = "SELECT POExtCost, POExtCostLimit FROM AIMUsers " & _
            " WHERE UserID = N'" & Trim$(curUser) & "'"
    Set rsPOLimit = New ADODB.Recordset
    rsPOLimit.Open strSQL, Cn
    If Not rsPOLimit.eof Then
        If Not IsNull(rsPOLimit("POExtCostLimit")) Then
            g_UserPOLimit = CDbl(rsPOLimit("POExtCostLimit"))
            If UCase$(Trim$(rsPOLimit("POExtCost"))) = "Y" Then
                g_bUserPOLT = True
            Else
                g_bUserPOLT = False
            End If
        Else
            g_UserPOLimit = 0
            g_bUserPOLT = False
        End If
    End If

CleanUp:
    If f_IsRecordsetValidAndOpen(rsPOLimit) Then rsPOLimit.Close
    Set rsPOLimit = Nothing
    Set Cn = Nothing

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    
    If f_IsRecordsetValidAndOpen(rsPOLimit) Then rsPOLimit.Close
    Set rsPOLimit = Nothing
    Set Cn = Nothing
    
    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(POLimitCalc)"
     f_HandleErr , , , "AIM_Main::POLimitCalc", Now, gDRGeneralError, True, Err
End Function

'sujit
Private Function GetDBAccessLevel()

    Dim Cn As ADODB.Connection
    Dim RtnCode As Integer
    Dim rsDBAccess As ADODB.Recordset
    Dim strSQL As String
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String

    Set Cn = New ADODB.Connection
    
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Function
    
    g_CreateDBUser = False
    g_UpdatePassword = False

    strSQL = "SELECT CreateDBUser, UpdateDBPassword FROM SYSCTRL"
    
    Set rsDBAccess = New ADODB.Recordset
    rsDBAccess.Open strSQL, Cn
    If Not rsDBAccess.eof Then
        If UCase$(Trim$(rsDBAccess("CreateDBUser"))) = "Y" Then
            g_CreateDBUser = True
        End If
        If UCase$(Trim$(rsDBAccess("UpdateDBPassword"))) = "Y" Then
            g_UpdatePassword = True
        End If
    End If

CleanUp:
    If f_IsRecordsetValidAndOpen(rsDBAccess) Then rsDBAccess.Close
    Set rsDBAccess = Nothing
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    
    If f_IsRecordsetValidAndOpen(rsDBAccess) Then rsDBAccess.Close
    Set rsDBAccess = Nothing
    Set Cn = Nothing
    
    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(GetDBAccessLevel)"
     f_HandleErr , , , "AIM_Main::GetDBAccessLevel", Now, gDRGeneralError, True, Err
End Function


