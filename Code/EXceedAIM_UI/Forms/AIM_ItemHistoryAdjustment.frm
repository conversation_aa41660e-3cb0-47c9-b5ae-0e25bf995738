VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItemHistoryAdjustment 
   Caption         =   "SSA DR Item History Adjustment"
   ClientHeight    =   8940
   ClientLeft      =   60
   ClientTop       =   330
   ClientWidth     =   14670
   LinkTopic       =   "Form1"
   LockControls    =   -1  'True
   ScaleHeight     =   8940
   ScaleWidth      =   14670
   StartUpPosition =   1  'CenterOwner
   Begin VB.Frame frmHistory 
      Height          =   6975
      Left            =   53
      TabIndex        =   14
      Top             =   1560
      Width           =   14535
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgDemand 
         Height          =   6615
         Left            =   120
         TabIndex        =   0
         Top             =   240
         Width           =   7665
         _Version        =   196617
         DataMode        =   1
         GroupHeaders    =   0   'False
         AllowColumnMoving=   0
         AllowColumnSwapping=   0
         SelectTypeRow   =   1
         SelectByCell    =   -1  'True
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         CaptionAlignment=   0
         SplitterVisible =   -1  'True
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   13520
         _ExtentY        =   11668
         _StockProps     =   79
         Caption         =   "Demand History"
         ForeColor       =   0
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAdjustLog 
         Height          =   3015
         Left            =   7920
         TabIndex        =   15
         Top             =   240
         Width           =   6465
         _Version        =   196617
         DataMode        =   2
         Rows            =   1
         RecordSelectors =   0   'False
         GroupHeaders    =   0   'False
         Col.Count       =   0
         DefColWidth     =   2540
         AllowUpdate     =   0   'False
         AllowRowSizing  =   0   'False
         AllowGroupSizing=   0   'False
         AllowGroupMoving=   0   'False
         AllowColumnMoving=   0
         AllowGroupSwapping=   0   'False
         AllowColumnSwapping=   0
         AllowGroupShrinking=   0   'False
         AllowDragDrop   =   0   'False
         SelectTypeCol   =   0
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   450
         ExtraHeight     =   291
         CaptionAlignment=   0
         SplitterVisible =   -1  'True
         Columns(0).Width=   2540
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   11404
         _ExtentY        =   5318
         _StockProps     =   79
         Caption         =   "Saved adjustments (logged in database)"
         ForeColor       =   0
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Arial"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgPending 
         Height          =   3495
         Left            =   7920
         TabIndex        =   16
         ToolTipText     =   "Click on the Save button to log these in the database."
         Top             =   3360
         Width           =   6465
         _Version        =   196617
         DataMode        =   2
         Rows            =   1
         RecordSelectors =   0   'False
         GroupHeaders    =   0   'False
         Col.Count       =   0
         DefColWidth     =   2540
         AllowGroupSizing=   0   'False
         AllowGroupMoving=   0   'False
         AllowColumnMoving=   0
         AllowGroupSwapping=   0   'False
         AllowColumnSwapping=   0
         AllowGroupShrinking=   0   'False
         AllowDragDrop   =   0   'False
         SelectTypeCol   =   0
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   450
         ExtraHeight     =   291
         CaptionAlignment=   0
         SplitterVisible =   -1  'True
         Columns(0).Width=   2540
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   11404
         _ExtentY        =   6165
         _StockProps     =   79
         Caption         =   "Pending adjustments (current session)"
         ForeColor       =   0
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Arial"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
   End
   Begin VB.Frame frmHeader 
      Height          =   1470
      Left            =   53
      TabIndex        =   1
      Top             =   0
      Width           =   14535
      Begin TDBText6Ctl.TDBText txtLcID 
         Height          =   345
         Left            =   2955
         TabIndex        =   2
         Top             =   240
         Width           =   1770
         _Version        =   65536
         _ExtentX        =   3122
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryAdjustment.frx":0000
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryAdjustment.frx":006C
         Key             =   "AIM_ItemHistoryAdjustment.frx":008A
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtItem 
         Height          =   345
         Left            =   2955
         TabIndex        =   3
         Top             =   600
         Width           =   2700
         _Version        =   65536
         _ExtentX        =   4762
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryAdjustment.frx":00CE
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryAdjustment.frx":013A
         Key             =   "AIM_ItemHistoryAdjustment.frx":0158
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtItDesc 
         Height          =   345
         Left            =   2955
         TabIndex        =   4
         Top             =   960
         Width           =   3540
         _Version        =   65536
         _ExtentX        =   6244
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryAdjustment.frx":019C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryAdjustment.frx":0208
         Key             =   "AIM_ItemHistoryAdjustment.frx":0226
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   1
         ShowContextMenu =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtVnID 
         Height          =   345
         Left            =   10815
         TabIndex        =   5
         Top             =   240
         Width           =   1050
         _Version        =   65536
         _ExtentX        =   1852
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryAdjustment.frx":026A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryAdjustment.frx":02D6
         Key             =   "AIM_ItemHistoryAdjustment.frx":02F4
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   345
         Left            =   10815
         TabIndex        =   6
         Top             =   600
         Width           =   1050
         _Version        =   65536
         _ExtentX        =   1852
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryAdjustment.frx":0338
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryAdjustment.frx":03A4
         Key             =   "AIM_ItemHistoryAdjustment.frx":03C2
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtByID 
         Height          =   345
         Left            =   10815
         TabIndex        =   7
         Top             =   960
         Width           =   1050
         _Version        =   65536
         _ExtentX        =   1852
         _ExtentY        =   609
         Caption         =   "AIM_ItemHistoryAdjustment.frx":0406
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemHistoryAdjustment.frx":0472
         Key             =   "AIM_ItemHistoryAdjustment.frx":0490
         BackColor       =   14737632
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label lblHeader 
         Caption         =   "Item"
         Height          =   300
         Index           =   1
         Left            =   105
         TabIndex        =   13
         Top             =   645
         Width           =   2805
      End
      Begin VB.Label lblHeader 
         Caption         =   "Description"
         Height          =   300
         Index           =   2
         Left            =   105
         TabIndex        =   12
         Top             =   1005
         Width           =   2805
      End
      Begin VB.Label lblHeader 
         Caption         =   "Location"
         Height          =   300
         Index           =   0
         Left            =   105
         TabIndex        =   11
         Top             =   285
         Width           =   2805
      End
      Begin VB.Label lblHeader 
         Caption         =   "Vendor"
         Height          =   300
         Index           =   3
         Left            =   7950
         TabIndex        =   10
         Top             =   285
         Width           =   2805
      End
      Begin VB.Label lblHeader 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   4
         Left            =   7950
         TabIndex        =   9
         Top             =   645
         Width           =   2805
      End
      Begin VB.Label lblHeader 
         Caption         =   "Buyer ID"
         Height          =   300
         Index           =   5
         Left            =   7950
         TabIndex        =   8
         Top             =   1005
         Width           =   2805
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   8520
      _ExtentX        =   582
      _ExtentY        =   661
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_ItemHistoryAdjustment.frx":04D4
      ToolBars        =   "AIM_ItemHistoryAdjustment.frx":9D96
   End
End
Attribute VB_Name = "AIM_ItemHistoryAdjustment"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim pr_Cn As ADODB.Connection
Dim pr_LcID As String
Dim pr_Item As String
Dim pr_DemandSource As String
Dim pr_StartYear As Integer
Dim pr_StartPeriod As Integer

Dim m_HistoryYear As Integer
Dim m_HistoryPeriod As Integer
    
Dim m_FYTracker(0 To 2) As Integer
Dim m_DemandBookmark As Variant

Dim m_ErrNumber As Long
Dim m_ErrSource As String
Dim m_ErrDesc As String

Dim AIMYears() As AIMYEARS_RCD
Dim AIMDays() As AIMDAYS_RCD
Dim m_NumItems As Integer
Dim m_TotalGridLines As Integer

Private Type PRIMARY_ITEM_INFO
    LcId As String
    Item As String
    ItDesc As String
    VnId As String
    Assort As String
    ById As String
    FcstUpdCyc As Long
    OldItem As String
    'Location info
    DemandSource As String
    Last_FcstUpdCyc As Long
    'Demand
    EndYear As Integer
    EndPeriod As Integer
    StartYear As Integer
    StartPeriod As Integer
    AnnualSummary(1 To 3) As Double
End Type
Dim m_PrimaryItemInfo As PRIMARY_ITEM_INFO

Private Type SUBS_HISTORY
    PeriodDate() As Date
    PeriodIndex() As Integer
    Item() As String
    SubsItem() As String
    PrevYear2() As Double
    PrevYear1() As Double
    CurrYear() As Double
End Type
Dim m_SubsHistory As SUBS_HISTORY

Private Type EDIT_HISTORY
    PrevYear1 As Integer
    PrevYear2 As Integer
    CurrYear As Integer
End Type
Dim m_Demand_ColIdx As EDIT_HISTORY

Public Property Set Connection(Cn As ADODB.Connection)
On Error GoTo ErrorHandler

    Set pr_Cn = Cn
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(Connection [PropertySet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::Connection [PropertySet]", Now, gDRGeneralError, True, Err
End Property

Public Property Let LcId(LcId As String)
On Error GoTo ErrorHandler

    pr_LcID = LcId
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(LcID [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::LcID [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Public Property Let Item(Item As String)
On Error GoTo ErrorHandler

    pr_Item = Item
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(Item [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::Item [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Public Property Let DemandSource(DemandSource As String)
On Error GoTo ErrorHandler

    pr_DemandSource = DemandSource
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(DemandSource [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::DemandSource PropertyLet", Now, gDRGeneralError, True, Err
End Property

Public Property Let StartYear(HYear As Integer)
On Error GoTo ErrorHandler

    pr_StartYear = HYear
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(StartYear [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::StartYear PropertyLet", Now, gDRGeneralError, True, Err
End Property

Public Property Let StartPeriod(HPeriod As Integer)
On Error GoTo ErrorHandler

    pr_StartPeriod = HPeriod
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(StartPeriod [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::StartPeriod PropertyLet", Now, gDRGeneralError, True, Err
End Property

Private Property Get HistoryYear() As Integer
On Error GoTo ErrorHandler

    HistoryYear = m_HistoryYear
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(HistoryYear [PropertyGet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::HistoryYear PropertyGet", Now, gDRGeneralError, True, Err
End Property

Private Property Let HistoryYear(HYear As Integer)
On Error GoTo ErrorHandler

    m_HistoryYear = HYear
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(HistoryYear [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::HistoryYear PropertyLet", Now, gDRGeneralError, True, Err
End Property

Private Property Get HistoryPeriod() As Integer
On Error GoTo ErrorHandler

    HistoryPeriod = m_HistoryPeriod
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(HistoryPeriod [PropertyGet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::HistoryPeriod [PropertyGet]", Now, gDRGeneralError, True, Err
End Property

Private Property Let HistoryPeriod(HPeriod As Integer)
On Error GoTo ErrorHandler

    m_HistoryPeriod = HPeriod
    
Exit Property
ErrorHandler:
    'f_HandleErr Me.Caption & "(HistoryPeriod [PropertyLet])"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::HistoryPeriod [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Private Sub dgDemand_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim OriginalQty As Double
    Dim AdjustedQty As Double
    Dim Item As String
    Dim RtnCode As Integer
    Dim RecIndex As Integer
    
    RecIndex = Me.dgDemand.bookmark
    If RecIndex <= 0 Then Exit Sub
    
    If IsNumeric(Me.dgDemand.Columns(ColIndex).Text) = False Then
        Cancel = True
        Exit Sub
    End If
    
    OriginalQty = OldValue
    AdjustedQty = Me.dgDemand.Columns(ColIndex).Text
    
    If OriginalQty = AdjustedQty Then Exit Sub
    
    Item = Me.dgDemand.Columns("Item").Text
    HistoryPeriod = Me.dgDemand.Columns("Period").Text
    HistoryYear = m_FYTracker(ColIndex - m_Demand_ColIdx.PrevYear2)
    
    strMessage = "You have changed the history for item '" & Item & "'" & vbCrLf & _
        "   from: " & OriginalQty & vbCrLf & "   to: " & AdjustedQty & ", " & vbCrLf & _
        " for period #" & HistoryPeriod & _
        " in the fiscal year " & HistoryYear & "." & vbCrLf & vbCrLf & _
        "Are you sure you wish to continue? " & vbCrLf & _
        "   If yes, please provide a reason, then press Ok to proceed." & vbCrLf & _
        "   If not, press Cancel to revert to the original value."
    
    With AIM_HistoryAdjReason
        .ShowMessage = strMessage
        .Show vbModal
        If .AcceptValue = False Then
            Cancel = True
        Else
            strMessage = .ReasonText
            RtnCode = AddToPending(Item, OriginalQty, AdjustedQty, strMessage)
            Select Case HistoryYear
            Case Is = m_FYTracker(0)
                m_SubsHistory.PrevYear2(RecIndex) = AdjustedQty
            Case Is = m_FYTracker(1)
                m_SubsHistory.PrevYear1(RecIndex) = AdjustedQty
            Case Is = m_FYTracker(2)
                m_SubsHistory.CurrYear(RecIndex) = AdjustedQty
            End Select
        End If
    End With
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgDemand_BeforeColUpdate)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::dgDemand_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDemand_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgDemand.Redraw = False  'Performance boosters
    Screen.MousePointer = vbHourglass   'Status indicator
    
    'Define number of header lines
    Me.dgDemand.HeadLines = 2
        
    'Define Column Styles
    Me.dgDemand.StyleSets.Add ("Excpt")
    Me.dgDemand.StyleSets("Excpt").ForeColor = vbRed
    Me.dgDemand.StyleSets("Excpt").Font.Bold = True
    
    Me.dgDemand.StyleSets.Add ("NoExcpt")
    Me.dgDemand.StyleSets("NoExcpt").ForeColor = vbBlack
    
    'Define Columns
    IndexCounter = 0
    Me.dgDemand.Columns(IndexCounter).Name = "Date"
    Me.dgDemand.Columns(IndexCounter).Caption = getTranslationResource("Period Start-date")
    Me.dgDemand.Columns(IndexCounter).Width = 1300
    Me.dgDemand.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgDemand.Columns(IndexCounter).Alignment = ssCaptionAlignmentCenter
    Me.dgDemand.Columns(IndexCounter).NumberFormat = gDateFormat
    Me.dgDemand.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgDemand.Columns(IndexCounter).Name = "Period"
    Me.dgDemand.Columns(IndexCounter).Caption = getTranslationResource("Period Index")
    Me.dgDemand.Columns(IndexCounter).Width = 800
    Me.dgDemand.Columns(IndexCounter).NumberFormat = "##0"
    Me.dgDemand.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgDemand.Columns(IndexCounter).Alignment = ssCaptionAlignmentCenter
    Me.dgDemand.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgDemand.Columns(IndexCounter).Name = "Item"
    Me.dgDemand.Columns(IndexCounter).Caption = getTranslationResource("Item/Substitute")
    Me.dgDemand.Columns(IndexCounter).Width = 1300
    Me.dgDemand.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgDemand.Columns(IndexCounter).DataType = vbString
    Me.dgDemand.Columns(IndexCounter).FieldLen = 25
    Me.dgDemand.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    Me.dgDemand.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    m_Demand_ColIdx.PrevYear2 = IndexCounter
    Me.dgDemand.Columns(IndexCounter).Name = "demand3"
    Me.dgDemand.Columns(IndexCounter).Caption = getTranslationResource("Prev Year 2")
    Me.dgDemand.Columns(IndexCounter).Width = 1200
    Me.dgDemand.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgDemand.Columns(IndexCounter).DataType = vbDecimal
    Me.dgDemand.Columns(IndexCounter).NumberFormat = "###,###,##0.0"
    Me.dgDemand.Columns(IndexCounter).FieldLen = 10
    Me.dgDemand.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgDemand.Columns(IndexCounter).Locked = False   'Editable
    
    IndexCounter = IndexCounter + 1
    m_Demand_ColIdx.PrevYear1 = IndexCounter
    Me.dgDemand.Columns(IndexCounter).Name = "demand2"
    Me.dgDemand.Columns(IndexCounter).Caption = getTranslationResource("Prev Year 1")
    Me.dgDemand.Columns(IndexCounter).Width = 1200
    Me.dgDemand.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgDemand.Columns(IndexCounter).DataType = vbDecimal
    Me.dgDemand.Columns(IndexCounter).NumberFormat = "###,###,##0.0"
    Me.dgDemand.Columns(IndexCounter).FieldLen = 10
    Me.dgDemand.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgDemand.Columns(IndexCounter).Locked = False   'Editable
    
    IndexCounter = IndexCounter + 1
    m_Demand_ColIdx.CurrYear = IndexCounter
    Me.dgDemand.Columns(IndexCounter).Name = "demand1"
    Me.dgDemand.Columns(IndexCounter).Caption = getTranslationResource("Current Year")
    Me.dgDemand.Columns(IndexCounter).Width = 1200
    Me.dgDemand.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgDemand.Columns(IndexCounter).DataType = vbDecimal
    Me.dgDemand.Columns(IndexCounter).NumberFormat = "###,###,##0.0"
    Me.dgDemand.Columns(IndexCounter).FieldLen = 10
    Me.dgDemand.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgDemand.Columns(IndexCounter).Locked = False   'Editable

    Me.dgDemand.StyleSets.Add "FirstLine"
    Me.dgDemand.StyleSets("FirstLine").ForeColor = &HC00000
    Me.dgDemand.StyleSets("FirstLine").Font.Size = Me.dgDemand.Font.Size

    'Set Splitter Position
    Me.dgDemand.SplitterPos = 1
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgDemand, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgDemand.Columns.Count - 1
'        dgDemand.Columns(IndexCounter).HasHeadBackColor = True
'        dgDemand.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgDemand.Columns(IndexCounter).Locked = False Then dgDemand.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

    Me.dgDemand.Redraw = True
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Me.dgDemand.Redraw = True
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDemand_InitColumnProps)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::dgDemand_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDemand_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    Dim ColIndex As Integer
    Dim RecIndex As Integer
    Dim RtnCode As Integer
    Dim SubsItem As String
    
    ColIndex = Me.dgDemand.Col
    RecIndex = Me.dgDemand.bookmark
    
    If ColIndex >= m_Demand_ColIdx.PrevYear2 And RecIndex > 0 Then
        HistoryYear = m_FYTracker(ColIndex - m_Demand_ColIdx.PrevYear2)
        HistoryPeriod = Me.dgDemand.Columns("Period").Text
        SubsItem = Me.dgDemand.Columns("item").Text
        
        'Fetch adjustment logs here.
        RtnCode = GetAdjustLog(SubsItem)
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgDemand_RowColChange)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::dgDemand_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDemand_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim RowBufCounter As Integer
    Dim LastGridRow As Integer
    Dim Z_Idx As Integer
    Dim NumRowsRead As Integer
    
    'This screen will not display the following elements shown in Forecast Simulator:
    '   Seas Profile
    '   Item Profile
    '   Deseasonalized Demand
    '   Demand Filters
    '   Four-week periods
    'This screen WILL display the following additional elements to those in the Forecast Sim:
    '   Item/Substitute Item (sorted by period)
    
    Me.dgDemand.Redraw = False  'Performance boosters
    Screen.MousePointer = vbHourglass   'Status indicator
        
    If IsNull(StartLocation) Then       'If the grid is empty then
        If ReadPriorRows Then           'If moving backwards through grid then
            LastGridRow = m_TotalGridLines                'pointer = # of last grid row
        Else                            'else
            LastGridRow = 0                       'pointer = # of first grid row
        End If
    Else                                'If the grid already has data in it then
        LastGridRow = StartLocation               'pointer = location just before or after the row where data will be added

        If ReadPriorRows Then           'If moving backwards through grid then
            LastGridRow = LastGridRow - 1                   'move pointer back one row
        Else                            'else
            LastGridRow = LastGridRow + 1                   'move pointer ahead one row
        End If
    End If

    Me.dgDemand.Columns("demand3").Caption = getTranslationResource("Fiscal Year") & ": " & CStr(m_FYTracker(0)) 'Prev Year 2
    Me.dgDemand.Columns("demand2").Caption = getTranslationResource("Fiscal Year") & ": " & CStr(m_FYTracker(1)) 'Prev Year 1
    Me.dgDemand.Columns("demand1").Caption = getTranslationResource("Fiscal Year") & ": " & CStr(m_FYTracker(2)) 'Current Year
    
    'The pointer (LastGridRow) now points to the row of the grid where you will start adding data.
    For RowBufCounter = 0 To RowBuf.RowCount - 1                    'For each row in the row buffer
        If LastGridRow < 0 Or LastGridRow > (m_TotalGridLines - 1) Then
            Exit For      'If the pointer is outside the grid then stop this
        End If
        
        'For each column in the grid
        'Set the value of each column in the row buffer
        'to the corresponding value in the array
        If LastGridRow = 0 Then           'Write Totals
            RowBuf.Value(RowBufCounter, 0) = getTranslationResource("Totals")
            RowBuf.Value(RowBufCounter, 1) = ""
            RowBuf.Value(RowBufCounter, 2) = Me.txtItem.Text
            RowBuf.Value(RowBufCounter, 3) = m_PrimaryItemInfo.AnnualSummary(1)
            RowBuf.Value(RowBufCounter, 4) = m_PrimaryItemInfo.AnnualSummary(2)
            RowBuf.Value(RowBufCounter, 5) = m_PrimaryItemInfo.AnnualSummary(3)
        Else
            With m_SubsHistory
                Z_Idx = LastGridRow
                RowBuf.Value(RowBufCounter, 0) = .PeriodDate(Z_Idx)
                RowBuf.Value(RowBufCounter, 1) = .PeriodIndex(Z_Idx)
                RowBuf.Value(RowBufCounter, 2) = .SubsItem(Z_Idx)
                RowBuf.Value(RowBufCounter, 3) = .PrevYear2(Z_Idx)
                RowBuf.Value(RowBufCounter, 4) = .PrevYear1(Z_Idx)
                RowBuf.Value(RowBufCounter, 5) = .CurrYear(Z_Idx)
            End With
        End If
        
        RowBuf.bookmark(RowBufCounter) = LastGridRow                          'set the value of the bookmark for the current row in the rowbuffer

        If ReadPriorRows Then                           'move the pointer forward or backward, depending
            LastGridRow = LastGridRow - 1                                   'on which way it's supposed to move
        Else
            LastGridRow = LastGridRow + 1
        End If
        
        NumRowsRead = NumRowsRead + 1                                       'increment the number of rows read
    Next RowBufCounter

    RowBuf.RowCount = NumRowsRead                                 'set the size of the row buffer to the number of rows read
    
    Me.dgDemand.Redraw = True
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Me.dgDemand.Redraw = True
    Screen.MousePointer = vbNormal
    
    'f_HandleErr Me.Caption & "(dgDemand_UnboundReadData)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::dgDemand_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgPending_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer

    Me.dgPending.Redraw = False
    Screen.MousePointer = vbHourglass
    
    'Clear any existing columns
    Me.dgPending.Columns.RemoveAll
    Me.dgPending.HeadLines = 2
    
    'Define Columns
    IndexCounter = 0
    Me.dgPending.Columns.Add IndexCounter
    Me.dgPending.Columns(IndexCounter).Name = "Item"
    Me.dgPending.Columns(IndexCounter).Caption = getTranslationResource("Item/Substitute")
    Me.dgPending.Columns(IndexCounter).Width = 1300
    Me.dgPending.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPending.Columns(IndexCounter).DataType = vbString
    Me.dgPending.Columns(IndexCounter).FieldLen = 25
    Me.dgPending.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    Me.dgPending.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgPending.Columns.Add IndexCounter
    Me.dgPending.Columns(IndexCounter).Name = "OriginalQty"
    Me.dgPending.Columns(IndexCounter).Caption = getTranslationResource("Original Qty")
    Me.dgPending.Columns(IndexCounter).Width = 1200
    Me.dgPending.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPending.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgPending.Columns(IndexCounter).DataType = vbDecimal
    Me.dgPending.Columns(IndexCounter).NumberFormat = "###,###,##0.0"
    Me.dgPending.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgPending.Columns.Add IndexCounter
    Me.dgPending.Columns(IndexCounter).Name = "AdjustedQty"
    Me.dgPending.Columns(IndexCounter).Caption = getTranslationResource("Adjusted Qty")
    Me.dgPending.Columns(IndexCounter).Width = 1200
    Me.dgPending.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPending.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgPending.Columns(IndexCounter).DataType = vbDecimal
    Me.dgPending.Columns(IndexCounter).NumberFormat = "###,###,##0.0"
    Me.dgPending.Columns(IndexCounter).Locked = True    'False   'Editable
    
    IndexCounter = IndexCounter + 1
    Me.dgPending.Columns.Add IndexCounter
    Me.dgPending.Columns(IndexCounter).Name = "DemandPeriod"
    Me.dgPending.Columns(IndexCounter).Caption = getTranslationResource("Period #")
    Me.dgPending.Columns(IndexCounter).Width = 800
    Me.dgPending.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPending.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgPending.Columns(IndexCounter).DataType = vbInteger
    Me.dgPending.Columns(IndexCounter).NumberFormat = "##0"
    Me.dgPending.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgPending.Columns.Add IndexCounter
    Me.dgPending.Columns(IndexCounter).Name = "FiscalYear"
    Me.dgPending.Columns(IndexCounter).Caption = getTranslationResource("Fiscal Year")
    Me.dgPending.Columns(IndexCounter).Width = 800
    Me.dgPending.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPending.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
    Me.dgPending.Columns(IndexCounter).DataType = vbInteger
    Me.dgPending.Columns(IndexCounter).NumberFormat = "###0"
    Me.dgPending.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgPending.Columns.Add IndexCounter
    Me.dgPending.Columns(IndexCounter).Name = "Reason"
    Me.dgPending.Columns(IndexCounter).Caption = getTranslationResource("Reason")
    Me.dgPending.Columns(IndexCounter).Width = 4000
    Me.dgPending.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgPending.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    Me.dgPending.Columns(IndexCounter).DataType = vbString
    Me.dgPending.Columns(IndexCounter).FieldLen = 255
    Me.dgPending.Columns(IndexCounter).Locked = False   'Editable
    
    'Set Splitter Position
    Me.dgPending.SplitterPos = 3
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgPending, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgPending.Columns.Count - 1
'        dgPending.Columns(IndexCounter).HasHeadBackColor = True
'        dgPending.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgPending.Columns(IndexCounter).Locked = False Then dgPending.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
    Me.dgPending.Redraw = True
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Me.dgPending.Redraw = True
    Screen.MousePointer = vbNormal
    
    'f_HandleErr Me.Caption & "(dgPending_InitColumnProps)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::dgPending_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If pr_Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of the window-list's State button to Busy
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    'Reset State of the window-list's State button to Normal
    gbUpdating = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG08200")
    If StrComp(strMessage, "STATMSG08200") = 0 Then strMessage = "Initializing Edit Item History..."
    Write_Message strMessage
    
    GetTranslatedCaptions Me
    
    'Load the AIM Calendar
    strMessage = getTranslationResource("STATMSG01601")
    If StrComp(strMessage, "STATMSG01601") = 0 Then strMessage = "Loading AIM Calendar..."
    RtnCode = Write_Message(strMessage)
    AIMCalendar_Load pr_Cn, AIMYears(), AIMDays()
        
    'Fetch data
    If InitVariables = SUCCEED Then
        If GetDemandData = SUCCEED Then
            Me.txtLcID.Text = m_PrimaryItemInfo.LcId
            Me.txtItem.Text = m_PrimaryItemInfo.Item
            Me.txtItDesc.Text = m_PrimaryItemInfo.ItDesc
            Me.txtVnID.Text = m_PrimaryItemInfo.VnId
            Me.txtAssort.Text = m_PrimaryItemInfo.Assort
            Me.txtByID.Text = m_PrimaryItemInfo.ById
        End If
    End If
    
    'Add to list of windows
    AddToWindowList Me.Caption
    
    'CleanUp
    Write_Message ""
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim YesNoCancel As VbMsgBoxResult
    
    'Do NOT set Connection to Nothing.  That is borrowed from Forecast Simulator.
    
    'Check for unsaved
    If Me.dgPending.Rows > 0 Then
        strMessage = getTranslationResource("STATMSG08201")
        If StrComp(strMessage, "STATMSG08201") = 0 Then
            strMessage = "There are pending changes in this session.  Please click one of the following buttons:" & _
                vbCrLf & "'Yes' to save these to the database before exiting" & _
                vbCrLf & "'No' to continue exiting without saving" & _
                vbCrLf & "'Cancel' to stop exiting and review the changes"
        End If
        YesNoCancel = MsgBox(strMessage, vbYesNoCancel)
        Select Case YesNoCancel
        Case vbYes
            SaveToLog
        Case vbCancel
            Cancel = True
        Case Else
            'Continue unloading.
        End Select
    End If
    
    'Remove from from list of windows
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Unload)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::Form_Unload", Now, gDRGeneralError, True, Err
End Sub

Private Function InitVariables() As Integer
On Error GoTo ErrorHandler

    Dim rsItem As ADODB.Recordset
    Dim AIM_Item_GetEq_Sp As ADODB.Command
    Dim rsLocations As ADODB.Recordset
    Dim AIM_AIMLocations_GetEq_Sp As ADODB.Command
    Dim IndexCount As Integer
    
    Dim RtnCode As Integer
    
    '**************************************************
    'Get Item data
    '**************************************************
    Set AIM_Item_GetEq_Sp = New ADODB.Command
    With AIM_Item_GetEq_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Item_GetEq_Sp"
        'Default return value
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        'Named parameters
        .Parameters.Append .CreateParameter("@LcIDKey", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@ItemKey", adVarWChar, adParamInput, 25)
    End With
    
    Set rsItem = New ADODB.Recordset
    With rsItem
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    RtnCode = Item_GetEq(AIM_Item_GetEq_Sp, rsItem, pr_LcID, pr_Item)
    If RtnCode = FAIL _
    Or Not f_IsRecordsetOpenAndPopulated(rsItem) _
    Then
        InitVariables = FAIL
        GoTo ErrorHandler
    Else
        With m_PrimaryItemInfo
            .LcId = rsItem!LcId
            .Item = rsItem!Item
            .ItDesc = rsItem!ItDesc
            .VnId = rsItem!VnId
            .Assort = rsItem!Assort
            .ById = rsItem!ById
            .FcstUpdCyc = rsItem!FcstUpdCyc
            .OldItem = rsItem!OldItem
        End With
    End If
    
    If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
    Set rsItem = Nothing
    
    If Not (AIM_Item_GetEq_Sp Is Nothing) Then Set AIM_Item_GetEq_Sp.ActiveConnection = Nothing
    Set AIM_Item_GetEq_Sp = Nothing
    
    '**************************************************
    'Get Locations data
    '**************************************************
    Set AIM_AIMLocations_GetEq_Sp = New ADODB.Command
    With AIM_AIMLocations_GetEq_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMLocations_GetEq_Sp"
        'Default return value
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        'Named parameters
        .Parameters.Append .CreateParameter("@LcIDKey", adVarWChar, adParamInput, 12)
    End With
    
    Set rsLocations = New ADODB.Recordset
    With rsLocations
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    RtnCode = AIMLocations_GetEq(AIM_AIMLocations_GetEq_Sp, _
        rsLocations, pr_LcID)
    
    If RtnCode = FAIL _
    Or Not f_IsRecordsetOpenAndPopulated(rsLocations) _
    Then
        InitVariables = FAIL
        GoTo ErrorHandler
    Else
        With m_PrimaryItemInfo
            .DemandSource = rsLocations!DemandSource
            .Last_FcstUpdCyc = rsLocations!Last_FcstUpdCyc
        End With
    End If
    
    If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
    Set rsLocations = Nothing
    
    If Not (AIM_AIMLocations_GetEq_Sp Is Nothing) Then Set AIM_AIMLocations_GetEq_Sp.ActiveConnection = Nothing
    Set AIM_AIMLocations_GetEq_Sp = Nothing
    
    
    InitVariables = SUCCEED
    
    Exit Function
    
ErrorHandler:
    m_ErrNumber = Err.Number
    m_ErrSource = Err.source
    m_ErrDesc = Err.Description
    On Error Resume Next

    If f_IsRecordsetValidAndOpen(rsItem) Then rsItem.Close
    Set rsItem = Nothing
    
    If Not (AIM_Item_GetEq_Sp Is Nothing) Then Set AIM_Item_GetEq_Sp.ActiveConnection = Nothing
    Set AIM_Item_GetEq_Sp = Nothing
    
    If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
    Set rsLocations = Nothing
    
    If Not (AIM_AIMLocations_GetEq_Sp Is Nothing) Then Set AIM_AIMLocations_GetEq_Sp.ActiveConnection = Nothing
    Set AIM_AIMLocations_GetEq_Sp = Nothing
    
    If m_ErrNumber <> 0 Then
        Err.Raise m_ErrNumber, m_ErrSource, m_ErrDesc & "(InitVariables)"
		f_HandleErr , , , "AIM_ItemHistoryAdjustment::InitVariables", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function GetDemandData() As Integer
On Error GoTo ErrorHandler
        
    Dim FetchItemHistory_Sp As ADODB.Command
    Dim rsItemHistory As ADODB.Recordset
    
    Dim GivenYear As Integer
    Dim GivenItem As String
    Dim IsDemandPeriod As Integer
    Dim Y_Idx As Integer
    Dim Z_Idx As Integer
    
    Dim PeriodIdx As Integer
    Dim RecIndex As Integer
    Dim End_Period As Integer
    Dim End_Year As Integer
    
    Dim RtnCode As Integer
        
    'Initialize variables/return values
    GetDemandData = FAIL
    
    'This screen will not display the following elements shown in Forecast Simulator:
    '   Seas Profile
    '   Item Profile
    '   Deseasonalized Demand
    '   Demand Filters
    '   Four-week periods
    
    'Determine the starting and ending fiscal years
    With m_PrimaryItemInfo
        If .FcstUpdCyc = 0 Then
            .FcstUpdCyc = .Last_FcstUpdCyc
        End If
        .StartYear = pr_StartYear
        .StartPeriod = pr_StartPeriod
        
        If .FcstUpdCyc > 0 Then
            .EndYear = .FcstUpdCyc \ 100
            .EndPeriod = .FcstUpdCyc - CLng(.EndYear) * 100
            
            'Determine the Start Year and Start Period
            If .EndPeriod = 52 Then
                .StartYear = .EndYear - 2
                .StartPeriod = 1
            Else
                .StartYear = .EndYear - 3
                .StartPeriod = .EndPeriod + 1
            End If
        
        ElseIf .StartYear > 0 Then
            .EndYear = .StartYear
            .EndPeriod = 52 '?
        End If
    End With
    
    'Initialize the FiscalYear tracker.
    m_FYTracker(0) = m_PrimaryItemInfo.EndYear - 2   'Prev. Year 2
    m_FYTracker(1) = m_FYTracker(0) + 1         'Prev. Year 1
    m_FYTracker(2) = m_FYTracker(1) + 1         'Current Year
        
    'Initialize the yearly totals
    For PeriodIdx = 1 To 3
        m_PrimaryItemInfo.AnnualSummary(PeriodIdx) = 0
    Next PeriodIdx
        
    'Get the Item's History
    Set FetchItemHistory_Sp = New ADODB.Command
    With FetchItemHistory_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        If StrComp(pr_DemandSource, "B", vbTextCompare) = 0 Then
            .CommandText = "AIM_ItemHistory_GetSubsItems_Sp"
            'Default return value
            .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
            'Named parameters
            .Parameters.Append .CreateParameter("@LcIDKey", adVarWChar, adParamInput, 12)
            .Parameters.Append .CreateParameter("@ItemKey", adVarWChar, adParamInput, 25)
            .Parameters.Append .CreateParameter("@StartYear", adInteger, adParamInput)
            .Parameters.Append .CreateParameter("@EndYear", adInteger, adParamInput)
            .Parameters.Append .CreateParameter("@NumberOfSubs", adInteger, adParamInputOutput)
            'Set Values
            .Parameters(1).Value = pr_LcID
            .Parameters(2).Value = pr_Item
            .Parameters(3).Value = m_PrimaryItemInfo.StartYear
            .Parameters(4).Value = m_PrimaryItemInfo.EndYear
            .Parameters(5).Value = Null
        Else
            .CommandText = "AIM_ItemHistory_GetEq_Sp"
            'Default return value
            .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
            'Named parameters
            .Parameters.Append .CreateParameter("@LcIDKey", adVarWChar, adParamInput, 12)
            .Parameters.Append .CreateParameter("@ItemKey", adVarWChar, adParamInput, 25)
            .Parameters.Append .CreateParameter("@StartYear", adInteger, adParamInput)
            .Parameters.Append .CreateParameter("@EndYear", adInteger, adParamInput)
            .Parameters.Append .CreateParameter("@DemandSource", adVarWChar, adParamInput, 1)
            'Set Values
            .Parameters(1).Value = pr_LcID
            .Parameters(2).Value = pr_Item
            .Parameters(3).Value = m_PrimaryItemInfo.StartYear
            .Parameters(4).Value = m_PrimaryItemInfo.EndYear
            .Parameters(5).Value = pr_DemandSource
        End If
        
    End With
    
    Set rsItemHistory = New ADODB.Recordset
    With rsItemHistory
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsItemHistory.Open FetchItemHistory_Sp
    If Not f_IsRecordsetOpenAndPopulated(rsItemHistory) _
    Then
        GetDemandData = FAIL
    Else
        If StrComp(pr_DemandSource, "B", vbTextCompare) = 0 Then
            m_NumItems = FetchItemHistory_Sp.Parameters(5).Value
            m_NumItems = m_NumItems + 1
        Else
            m_NumItems = 1
        End If
        
        RecIndex = m_NumItems * 52
        m_TotalGridLines = RecIndex + 1
        With m_SubsHistory
            'Number of Items multiplied by Number of Periods
            ReDim .PeriodDate(1 To RecIndex)
            ReDim .PeriodIndex(1 To RecIndex)
            ReDim .Item(1 To RecIndex)
            ReDim .SubsItem(1 To RecIndex)
            ReDim .PrevYear2(1 To RecIndex)
            ReDim .PrevYear1(1 To RecIndex)
            ReDim .CurrYear(1 To RecIndex)
            
            'Initialize the demand data
            For PeriodIdx = 1 To RecIndex
                .PrevYear2(PeriodIdx) = 0
                .PrevYear1(PeriodIdx) = 0
                .CurrYear(PeriodIdx) = 0
            Next PeriodIdx
        End With
        
        GivenYear = 0
        GivenItem = ""
        Y_Idx = 0
        'Load the Demand History
        Do Until rsItemHistory.eof
            'Sorted by SubsItem, HistoryYear.
            If GivenYear <> rsItemHistory!HisYear Then
                GivenYear = rsItemHistory!HisYear
            End If
            If m_NumItems > 1 Then
                If StrComp(GivenItem, rsItemHistory!SubsItem) <> 0 Then
                    GivenItem = rsItemHistory!SubsItem
                    Y_Idx = Y_Idx + 1
                End If
            Else
                GivenItem = rsItemHistory!Item
                Y_Idx = 1
            End If
            Z_Idx = Y_Idx
            For PeriodIdx = 1 To 52 Step 1
                End_Period = m_PrimaryItemInfo.EndPeriod - (PeriodIdx - 1)
                End_Year = m_PrimaryItemInfo.EndYear
                            
                Do Until End_Period > 0
                    End_Period = End_Period + 52
                    End_Year = End_Year - 1
                Loop
                m_SubsHistory.PeriodIndex(Z_Idx) = End_Period
                m_SubsHistory.PeriodDate(Z_Idx) = GetStartDateFmPeriod(End_Period, End_Year, AIMYears)
                
                m_SubsHistory.Item(Z_Idx) = rsItemHistory!Item
                If m_NumItems = 1 Then
                    m_SubsHistory.SubsItem(Z_Idx) = rsItemHistory!Item
                Else
                    m_SubsHistory.SubsItem(Z_Idx) = rsItemHistory!SubsItem
                End If
                
                IsDemandPeriod = GetDmdPd(m_PrimaryItemInfo.EndYear, m_PrimaryItemInfo.EndPeriod, GivenYear, PeriodIdx)
                If IsDemandPeriod > 0 Then
                    Select Case GivenYear
                    Case Is = m_FYTracker(0)
                        m_SubsHistory.PrevYear2(Z_Idx) = IIf(IsNull(rsItemHistory("dmd" + Format(End_Period, "00")).Value), 0, rsItemHistory("dmd" + Format(End_Period, "00")).Value)
                        m_PrimaryItemInfo.AnnualSummary(1) = m_PrimaryItemInfo.AnnualSummary(1) + m_SubsHistory.PrevYear2(Z_Idx)
                    
                    Case Is = m_FYTracker(1)
                        m_SubsHistory.PrevYear1(Z_Idx) = IIf(IsNull(rsItemHistory("dmd" + Format(End_Period, "00")).Value), 0, rsItemHistory("dmd" + Format(End_Period, "00")).Value)
                        m_PrimaryItemInfo.AnnualSummary(2) = m_PrimaryItemInfo.AnnualSummary(2) + m_SubsHistory.PrevYear1(Z_Idx)
                    
                    Case Is = m_FYTracker(2)
                        m_SubsHistory.CurrYear(Z_Idx) = IIf(IsNull(rsItemHistory("dmd" + Format(End_Period, "00")).Value), 0, rsItemHistory("dmd" + Format(End_Period, "00")).Value)
                        m_PrimaryItemInfo.AnnualSummary(3) = m_PrimaryItemInfo.AnnualSummary(3) + m_SubsHistory.CurrYear(Z_Idx)
                    
                    End Select
                End If
                Z_Idx = Z_Idx + m_NumItems
            Next PeriodIdx
        
            rsItemHistory.MoveNext
        Loop
        Me.dgDemand.Rows = m_TotalGridLines
        GetDemandData = SUCCEED
    End If

ErrorHandler:
    m_ErrNumber = Err.Number
    m_ErrSource = Err.source
    m_ErrDesc = Err.Description
    On Error Resume Next
    
    If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
    Set rsItemHistory = Nothing
    
    If Not (FetchItemHistory_Sp Is Nothing) Then Set FetchItemHistory_Sp.ActiveConnection = Nothing
    Set FetchItemHistory_Sp = Nothing
    
    If m_ErrNumber <> 0 Then
        'Err.Raise m_ErrNumber, m_ErrSource, m_ErrDesc & "(GetDemandData)"
		f_HandleErr , , , "AIM_ItemHistoryAdjustment::GetDemandData", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function GetAdjustLog(Optional SubsItem As String) As Integer
On Error GoTo ErrorHandler
        
    Dim AIM_ItemHistoryAdjLog_Select_Sp As ADODB.Command
    Dim rsAdjustLog As ADODB.Recordset
    Dim IndexCounter As Long
    Dim RowCount As Long
    Dim AddItemString As String
    
    If SubsItem = "" Then SubsItem = pr_Item
    
    Set AIM_ItemHistoryAdjLog_Select_Sp = New ADODB.Command
    With AIM_ItemHistoryAdjLog_Select_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ItemHistoryAdjLog_Select_Sp"
        'Default return value
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        'Named parameters
        .Parameters.Append .CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append .CreateParameter("@SubsItem", adVarWChar, adParamInput, 25)
        .Parameters.Append .CreateParameter("@HisYear", adSmallInt, adParamInput)
        .Parameters.Append .CreateParameter("@DemandPeriod", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@DemandSource", adVarWChar, adParamInput, 1)
        'Set values
        .Parameters(1).Value = pr_LcID
        .Parameters(2).Value = pr_Item
        .Parameters(3).Value = Trim$(SubsItem)
        .Parameters(4).Value = HistoryYear
        .Parameters(5).Value = HistoryPeriod
        .Parameters(6).Value = pr_DemandSource
    End With
    
    Set rsAdjustLog = New ADODB.Recordset
    With rsAdjustLog
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    rsAdjustLog.Open AIM_ItemHistoryAdjLog_Select_Sp
    If Not f_IsRecordsetOpenAndPopulated(rsAdjustLog) _
    Then
        Me.dgAdjustLog.Columns.RemoveAll
        GetAdjustLog = FAIL
        GoTo ErrorHandler
    Else
        Me.dgAdjustLog.Redraw = False       'Performance booster.
        Me.dgAdjustLog.Columns.RemoveAll    'Clear any existing columns
        Me.dgAdjustLog.Reset
        RowCount = rsAdjustLog.RecordCount
        
        'Define Columns
        IndexCounter = 0
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "OldValue"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Original Qty")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1200
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        Me.dgAdjustLog.Columns(IndexCounter).DataType = vbDecimal
        Me.dgAdjustLog.Columns(IndexCounter).NumberFormat = "#,##0.0"
        Me.dgAdjustLog.Columns(IndexCounter).Locked = True
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "NewValue"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Adjusted Qty")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1200
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        Me.dgAdjustLog.Columns(IndexCounter).DataType = vbDecimal
        Me.dgAdjustLog.Columns(IndexCounter).NumberFormat = "#,##0.0"
        Me.dgAdjustLog.Columns(IndexCounter).Locked = True
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustDate"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Changed On")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1300
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        Me.dgAdjustLog.Columns(IndexCounter).DataType = vbDate
        Me.dgAdjustLog.Columns(IndexCounter).NumberFormat = gDateFormat
        Me.dgAdjustLog.Columns(IndexCounter).Locked = True
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "UserID"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Changed By")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1300
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        Me.dgAdjustLog.Columns(IndexCounter).DataType = vbString
        Me.dgAdjustLog.Columns(IndexCounter).Locked = True
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "Reason"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Reason")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 4000
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        Me.dgAdjustLog.Columns(IndexCounter).DataType = vbString
        Me.dgAdjustLog.Columns(IndexCounter).Locked = True
        
        'Set Splitter Position
        Me.dgAdjustLog.SplitterPos = 2
        
        If gGridAutoSizeOption Then
            'Added to Automatically adjust the column widths depending on the Columns Label translation size.
            AdjustColumnWidths dgAdjustLog, ACW_EXPAND
        End If
        
        For IndexCounter = 0 To dgAdjustLog.Columns.Count - 1
    '        dgadjustlog.Columns(IndexCounter).HasHeadBackColor = True
    '        dgadjustlog.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
            If dgAdjustLog.Columns(IndexCounter).Locked = False Then dgAdjustLog.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
        Next
        
        rsAdjustLog.MoveFirst
        Do Until rsAdjustLog.eof
            AddItemString = ""
            AddItemString = rsAdjustLog!OldValue
            AddItemString = AddItemString & vbTab & rsAdjustLog!NewValue
            AddItemString = AddItemString & vbTab & Format(rsAdjustLog!AdjustDateTime, gDateFormat)
            AddItemString = AddItemString & vbTab & rsAdjustLog!UserId
            AddItemString = AddItemString & vbTab & rsAdjustLog!Reason
            
            Me.dgAdjustLog.AddItem AddItemString
            rsAdjustLog.MoveNext
        Loop
        'Me.dgAdjustLog.Rows = RowCount
    End If
        
ErrorHandler:
    'If no error number, then this acts as cleanup
    m_ErrNumber = Err.Number
    m_ErrSource = Err.source
    m_ErrDesc = Err.Description
    
    On Error Resume Next
    Me.dgAdjustLog.Redraw = True
    
    If f_IsRecordsetValidAndOpen(rsAdjustLog) Then rsAdjustLog.Close
    Set rsAdjustLog = Nothing
    
    If Not (AIM_ItemHistoryAdjLog_Select_Sp Is Nothing) Then Set AIM_ItemHistoryAdjLog_Select_Sp.ActiveConnection = Nothing
    Set AIM_ItemHistoryAdjLog_Select_Sp = Nothing
    
    If m_ErrNumber <> 0 Then
        Err.Raise m_ErrNumber, m_ErrSource, m_ErrDesc & "(GetAdjustLog)"
		f_HandleErr , , , "AIM_ItemHistoryAdjustment::GetAdjustLog", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function AddToPending( _
    Item As String, _
    OriginalQty As Double, _
    AdjustedQty As Double, _
    ReasonText As String _
) As Integer
On Error GoTo ErrorHandler

    Dim AddItemString As String
    
    AddToPending = FAIL
    
    Me.dgPending.Redraw = False
    Screen.MousePointer = vbHourglass
    
    AddItemString = Item
    AddItemString = AddItemString & vbTab & OriginalQty
    AddItemString = AddItemString & vbTab & AdjustedQty
    AddItemString = AddItemString & vbTab & HistoryPeriod
    AddItemString = AddItemString & vbTab & HistoryYear
    AddItemString = AddItemString & vbTab & ReasonText
    
    Me.dgPending.AddItem AddItemString

    AddToPending = SUCCEED

ErrorHandler:
    'If no error number, then this acts as cleanup
    m_ErrNumber = Err.Number
    m_ErrSource = Err.source
    m_ErrDesc = Err.Description
    
    On Error Resume Next
    Me.dgPending.Redraw = True
    Screen.MousePointer = vbNormal
    
    If m_ErrNumber <> 0 Then
        'Err.Raise m_ErrNumber, m_ErrSource, m_ErrDesc & "(AddToPending)"
		f_HandleErr , , , "AIM_ItemHistoryAdjustment::AddToPending", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function SaveToLog() As Integer
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim RowCount As Long
    Dim AIM_ItemHistoryAdjLog_Insert_Sp As ADODB.Command
    Dim SubsItem As String
    Dim OriginalQty As Double
    Dim AdjustedQty As Double
    Dim HYear As Integer
    Dim HPeriod As Integer
    Dim ReasonText As String
    Dim RtnCode As Integer
    
    SaveToLog = FAIL
    
    Me.dgPending.Redraw = False
    Screen.MousePointer = vbHourglass
    
    Set AIM_ItemHistoryAdjLog_Insert_Sp = New ADODB.Command
    With AIM_ItemHistoryAdjLog_Insert_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ItemHistoryAdjLog_Insert_Sp"
        'Default return value
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        'Named parameters
        .Parameters.Append .CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append .CreateParameter("@SubsItem", adVarWChar, adParamInput, 25)
        .Parameters.Append .CreateParameter("@HisYear", adSmallInt, adParamInput)
        .Parameters.Append .CreateParameter("@DemandPeriod", adTinyInt, adParamInput)
        .Parameters.Append .CreateParameter("@DemandSource", adVarWChar, adParamInput, 1)
        .Parameters.Append .CreateParameter("@OldValue", adDecimal, adParamInput, 9.1)
        .Parameters("@OldValue").Precision = 9
        .Parameters("@OldValue").NumericScale = 1
        .Parameters.Append .CreateParameter("@NewValue", adDecimal, adParamInput, 9.1)
        .Parameters("@NewValue").Precision = 9
        .Parameters("@NewValue").NumericScale = 1
        .Parameters.Append .CreateParameter("@UserID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@Reason", adVarWChar, adParamInput, 255)
    End With
    
    Me.dgPending.MoveFirst
    RowCount = Me.dgPending.Rows - 1
    For IndexCounter = 0 To RowCount
        OriginalQty = Me.dgPending.Columns("OriginalQty").Text
        AdjustedQty = Me.dgPending.Columns("AdjustedQty").Text
        SubsItem = Me.dgPending.Columns("Item").Text
        HYear = Me.dgPending.Columns("FiscalYear").Text
        HPeriod = Me.dgPending.Columns("DemandPeriod").Text
        ReasonText = Me.dgPending.Columns("Reason").Text
        
        With AIM_ItemHistoryAdjLog_Insert_Sp
            'Set values
            .Parameters(1).Value = pr_LcID
            .Parameters(2).Value = pr_Item
            .Parameters(3).Value = SubsItem
            .Parameters(4).Value = HYear
            .Parameters(5).Value = HPeriod
            .Parameters(6).Value = pr_DemandSource
            .Parameters(7).Value = OriginalQty
            .Parameters(8).Value = AdjustedQty
            .Parameters(9).Value = gUserID
            .Parameters(10).Value = ReasonText
            'Save to table
            .Execute
            'Check return code -- 0 = fail; 1 = succeed
            RtnCode = .Parameters(0).Value
            If RtnCode <= FAIL Then
                If RtnCode = -9 Then
                    'Message here saying there are no records available to edit
                End If
                Exit For
            End If
        End With
        
        Me.dgPending.MoveNext
    Next IndexCounter
    
    If RtnCode = SUCCEED Then
        Me.dgPending.RemoveAll
        dgPending_InitColumnProps
        'Refetch data
        RtnCode = GetDemandData
        RtnCode = GetAdjustLog(SubsItem)
    End If
    
    SaveToLog = RtnCode

ErrorHandler:
    'If no error number, then this acts as cleanup
    m_ErrNumber = Err.Number
    m_ErrSource = Err.source
    m_ErrDesc = Err.Description
    
    On Error Resume Next
    Me.dgPending.Redraw = True
    Screen.MousePointer = vbNormal
    
    If Not (AIM_ItemHistoryAdjLog_Insert_Sp Is Nothing) Then Set AIM_ItemHistoryAdjLog_Insert_Sp.ActiveConnection = Nothing
    Set AIM_ItemHistoryAdjLog_Insert_Sp = Nothing

    If m_ErrNumber <> 0 Then
        'Err.Raise m_ErrNumber, m_ErrSource, m_ErrDesc & "(SaveToLog)"
		f_HandleErr , , , "AIM_ItemHistoryAdjustment::SaveToLog", Now, gDRGeneralError, True, Err
    End If
End Function

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    Me.tbNavigation.Redraw = False  'Performance boosters
    Screen.MousePointer = vbHourglass   'Status indicator
    
    pr_Cn.Errors.Clear
    
    Write_Message ""
    
    Select Case Tool.ID
    Case "ID_GetFirst"
        Me.dgDemand.MoveFirst
        
    Case "ID_GetPrev"
        Me.dgDemand.MovePrevious
        
    Case "ID_GetNext"
        Me.dgDemand.MoveNext
        
    Case "ID_GetLast"
        Me.dgDemand.MoveLast
        
    Case "ID_Save"
        RtnCode = SaveToLog
        
    Case "ID_SetBookMark"
        If IsEmpty(m_DemandBookmark) _
        Or IsNull(m_DemandBookmark) _
        Then
            m_DemandBookmark = Me.dgDemand.bookmark
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        Else
            m_DemandBookmark = Null
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
        End If
        
    Case "ID_GoToBookMark"
        If Not IsEmpty(m_DemandBookmark) _
        Or Not IsNull(m_DemandBookmark) _
        Then
            Me.dgDemand.bookmark = m_DemandBookmark
        End If
            
    Case "ID_Close"
        Unload Me
        Exit Sub

    End Select
    
    Me.tbNavigation.Redraw = True
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Me.tbNavigation.Redraw = True
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
	f_HandleErr , , , "AIM_ItemHistoryAdjustment::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub
