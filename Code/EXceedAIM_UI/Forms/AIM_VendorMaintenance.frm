VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_VendorMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Vendor/Assortment Maintenance"
   ClientHeight    =   6765
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9900
   Icon            =   "AIM_VendorMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6765
   ScaleWidth      =   9900
   ShowInTaskbar   =   0   'False
   Begin ActiveTabs.SSActiveTabs atVendors 
      Height          =   5115
      Left            =   75
      TabIndex        =   2
      Top             =   1170
      Width           =   9735
      _ExtentX        =   17171
      _ExtentY        =   9022
      _Version        =   131083
      TabCount        =   2
      TagVariant      =   ""
      Tabs            =   "AIM_VendorMaintenance.frx":030A
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   4725
         Left            =   30
         TabIndex        =   32
         Top             =   360
         Width           =   9675
         _ExtentX        =   17066
         _ExtentY        =   8334
         _Version        =   131083
         TabGuid         =   "AIM_VendorMaintenance.frx":039A
         Begin VB.Frame Frame3 
            Height          =   4695
            Left            =   30
            TabIndex        =   45
            Top             =   0
            Width           =   9555
            Begin VB.CommandButton cmdReviewCycleLookUp 
               Caption         =   "..."
               Height          =   345
               Left            =   4755
               TabIndex        =   17
               Top             =   600
               Width           =   345
            End
            Begin VB.CheckBox ckPOByZone 
               Caption         =   "Produce Purchase Orders by Zone"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   200
               TabIndex        =   23
               Top             =   2805
               Value           =   1  'Checked
               Width           =   4080
            End
            Begin VB.CheckBox ckLastWeekSalesFlag 
               Caption         =   "Last Week Sales Alert"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   200
               TabIndex        =   24
               Top             =   3165
               Value           =   1  'Checked
               Width           =   4080
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
               DataField       =   " "
               DataSource      =   "rsLocations"
               Height          =   345
               Left            =   3345
               TabIndex        =   15
               Top             =   240
               Width           =   1380
               DataFieldList   =   "Userid"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   2434
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBText6Ctl.TDBText txtRevCycle 
               Height          =   345
               Left            =   3345
               TabIndex        =   16
               Top             =   600
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":03C2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":042E
               Key             =   "AIM_VendorMaintenance.frx":044C
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   8
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBNumber6Ctl.TDBNumber txtDft_LeadTime 
               Height          =   345
               Left            =   3345
               TabIndex        =   18
               Top             =   960
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Calculator      =   "AIM_VendorMaintenance.frx":0490
               Caption         =   "AIM_VendorMaintenance.frx":04B0
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":051C
               Keys            =   "AIM_VendorMaintenance.frx":053A
               Spin            =   "AIM_VendorMaintenance.frx":0584
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "###0;-###0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   1146880005
               MinValueVT      =   1549926405
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcReach_Code 
               Bindings        =   "AIM_VendorMaintenance.frx":05AC
               DataField       =   " "
               Height          =   345
               Left            =   3345
               TabIndex        =   21
               Top             =   2040
               Width           =   1065
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   1879
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "Column 0"
            End
            Begin TDBNumber6Ctl.TDBNumber txtVn_Min 
               Height          =   345
               Left            =   3345
               TabIndex        =   19
               Top             =   1320
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Calculator      =   "AIM_VendorMaintenance.frx":05B7
               Caption         =   "AIM_VendorMaintenance.frx":05D7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0643
               Keys            =   "AIM_VendorMaintenance.frx":0661
               Spin            =   "AIM_VendorMaintenance.frx":06AB
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "######0.00;-######0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "######0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9999999.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1245185
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin TDBNumber6Ctl.TDBNumber txtVn_Best 
               Height          =   345
               Left            =   3345
               TabIndex        =   20
               Top             =   1680
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Calculator      =   "AIM_VendorMaintenance.frx":06D3
               Caption         =   "AIM_VendorMaintenance.frx":06F3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":075F
               Keys            =   "AIM_VendorMaintenance.frx":077D
               Spin            =   "AIM_VendorMaintenance.frx":07C7
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "######0.00;-######0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "######0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9999999.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   1479671813
            End
            Begin TDBText6Ctl.TDBText txtVnComments1 
               Height          =   1065
               Left            =   5265
               TabIndex        =   25
               Top             =   600
               Width           =   4140
               _Version        =   65536
               _ExtentX        =   7302
               _ExtentY        =   1879
               Caption         =   "AIM_VendorMaintenance.frx":07EF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":085B
               Key             =   "AIM_VendorMaintenance.frx":0879
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVnComments2 
               Height          =   1065
               Left            =   5265
               TabIndex        =   26
               Top             =   2400
               Width           =   4140
               _Version        =   65536
               _ExtentX        =   7302
               _ExtentY        =   1879
               Caption         =   "AIM_VendorMaintenance.frx":08BD
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0929
               Key             =   "AIM_VendorMaintenance.frx":0947
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   -1
               ScrollBars      =   2
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   255
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   1
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtShipIns 
               Height          =   345
               Left            =   5265
               TabIndex        =   27
               Top             =   4080
               Width           =   4140
               _Version        =   65536
               _ExtentX        =   7302
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":098B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":09F7
               Key             =   "AIM_VendorMaintenance.frx":0A15
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcTransmitPO 
               Bindings        =   "AIM_VendorMaintenance.frx":0A59
               DataField       =   " "
               Height          =   345
               Left            =   3345
               TabIndex        =   22
               Top             =   2400
               Width           =   1065
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   1
               ColumnHeaders   =   0   'False
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               Columns(0).Width=   3200
               _ExtentX        =   1879
               _ExtentY        =   600
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "Column 0"
            End
            Begin VB.Label Label2 
               Caption         =   "Default Buyer ID"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   55
               Top             =   285
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Review Cycle"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   54
               Top             =   645
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Default Lead Time"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   53
               Top             =   1005
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Vendor Best Buy"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   52
               Top             =   1725
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Vendor Minimum"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   51
               Top             =   1365
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Reach Type"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   195
               TabIndex        =   50
               Top             =   2085
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Vendor Comments 1"
               Height          =   300
               Index           =   7
               Left            =   5265
               TabIndex        =   49
               Top             =   285
               Width           =   4620
            End
            Begin VB.Label Label2 
               Caption         =   "Vendor Comments 2"
               Height          =   300
               Index           =   8
               Left            =   5265
               TabIndex        =   48
               Top             =   2085
               Width           =   4620
            End
            Begin VB.Label Label2 
               Caption         =   "Transmit Purchase Order via"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   195
               TabIndex        =   47
               Top             =   2445
               Width           =   3045
            End
            Begin VB.Label Label2 
               Caption         =   "Shipping Instructions"
               Height          =   300
               Index           =   9
               Left            =   5265
               TabIndex        =   46
               Top             =   3765
               Width           =   4620
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   4725
         Left            =   30
         TabIndex        =   31
         Top             =   360
         Width           =   9675
         _ExtentX        =   17066
         _ExtentY        =   8334
         _Version        =   131083
         TabGuid         =   "AIM_VendorMaintenance.frx":0A64
         Begin VB.Frame Frame2 
            Height          =   4695
            Left            =   30
            TabIndex        =   33
            Top             =   0
            Width           =   9555
            Begin VB.CheckBox ckMDCFlag 
               Caption         =   "Master Distribution Center"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   200
               TabIndex        =   14
               Top             =   4245
               Value           =   1  'Checked
               Width           =   4005
            End
            Begin TDBText6Ctl.TDBText txtVName 
               Height          =   345
               Left            =   2750
               TabIndex        =   3
               Top             =   240
               Width           =   6345
               _Version        =   65536
               _ExtentX        =   11192
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0A8C
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0AF8
               Key             =   "AIM_VendorMaintenance.frx":0B16
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVAddress1 
               Height          =   345
               Left            =   2750
               TabIndex        =   4
               Top             =   600
               Width           =   6345
               _Version        =   65536
               _ExtentX        =   11192
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0B5A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0BC6
               Key             =   "AIM_VendorMaintenance.frx":0BE4
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVAddress2 
               Height          =   345
               Left            =   2750
               TabIndex        =   5
               Top             =   960
               Width           =   6345
               _Version        =   65536
               _ExtentX        =   11192
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0C28
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0C94
               Key             =   "AIM_VendorMaintenance.frx":0CB2
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVCity 
               Height          =   345
               Left            =   2745
               TabIndex        =   6
               Top             =   1320
               Width           =   2550
               _Version        =   65536
               _ExtentX        =   4498
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0CF6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0D62
               Key             =   "AIM_VendorMaintenance.frx":0D80
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVState 
               Height          =   345
               Left            =   2745
               TabIndex        =   7
               Top             =   1680
               Width           =   2550
               _Version        =   65536
               _ExtentX        =   4498
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0DC4
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0E30
               Key             =   "AIM_VendorMaintenance.frx":0E4E
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   10
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVZip 
               Height          =   345
               Left            =   2750
               TabIndex        =   8
               Top             =   2040
               Width           =   1350
               _Version        =   65536
               _ExtentX        =   2381
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0E92
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0EFE
               Key             =   "AIM_VendorMaintenance.frx":0F1C
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   10
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVContact 
               Height          =   345
               Left            =   2745
               TabIndex        =   9
               Top             =   2400
               Width           =   6345
               _Version        =   65536
               _ExtentX        =   11192
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":0F60
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":0FCC
               Key             =   "AIM_VendorMaintenance.frx":0FEA
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVEMail 
               Height          =   345
               Left            =   2745
               TabIndex        =   12
               Top             =   3480
               Width           =   6345
               _Version        =   65536
               _ExtentX        =   11192
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":102E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":109A
               Key             =   "AIM_VendorMaintenance.frx":10B8
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVPhone 
               Height          =   345
               Left            =   2745
               TabIndex        =   10
               Top             =   2760
               Width           =   2295
               _Version        =   65536
               _ExtentX        =   4048
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":10FC
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":1168
               Key             =   "AIM_VendorMaintenance.frx":1186
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVFax 
               Height          =   345
               Left            =   2745
               TabIndex        =   11
               Top             =   3120
               Width           =   2295
               _Version        =   65536
               _ExtentX        =   4048
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":11CA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":1236
               Key             =   "AIM_VendorMaintenance.frx":1254
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   20
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtVDocEMail 
               Height          =   345
               Left            =   2745
               TabIndex        =   13
               Top             =   3840
               Width           =   6345
               _Version        =   65536
               _ExtentX        =   11192
               _ExtentY        =   609
               Caption         =   "AIM_VendorMaintenance.frx":1298
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_VendorMaintenance.frx":1304
               Key             =   "AIM_VendorMaintenance.frx":1322
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin VB.Label Label1 
               Caption         =   "City"
               Height          =   300
               Index           =   3
               Left            =   200
               TabIndex        =   44
               Top             =   1365
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Address 2"
               Height          =   300
               Index           =   2
               Left            =   200
               TabIndex        =   43
               Top             =   1005
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Address 1"
               Height          =   300
               Index           =   1
               Left            =   200
               TabIndex        =   42
               Top             =   645
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Vendor Name"
               Height          =   300
               Index           =   0
               Left            =   200
               TabIndex        =   41
               Top             =   285
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Zip"
               Height          =   300
               Index           =   5
               Left            =   200
               TabIndex        =   39
               Top             =   2085
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Vendor Contact"
               Height          =   300
               Index           =   6
               Left            =   195
               TabIndex        =   38
               Top             =   2445
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Telephone"
               Height          =   300
               Index           =   7
               Left            =   195
               TabIndex        =   37
               Top             =   2805
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Fax"
               Height          =   300
               Index           =   8
               Left            =   195
               TabIndex        =   36
               Top             =   3165
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Email"
               Height          =   300
               Index           =   9
               Left            =   200
               TabIndex        =   35
               Top             =   3525
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "Document Email"
               Height          =   300
               Index           =   10
               Left            =   200
               TabIndex        =   34
               Top             =   3885
               Width           =   2445
            End
            Begin VB.Label Label1 
               Caption         =   "State"
               Height          =   300
               Index           =   4
               Left            =   200
               TabIndex        =   40
               Top             =   1725
               Width           =   2445
            End
         End
      End
   End
   Begin VB.Frame Frame1 
      Height          =   1065
      Left            =   87
      TabIndex        =   28
      Top             =   -36
      Width           =   9732
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   340
         Left            =   3200
         TabIndex        =   1
         Top             =   576
         Width           =   2340
         _Version        =   65536
         _ExtentX        =   4128
         _ExtentY        =   600
         Caption         =   "AIM_VendorMaintenance.frx":1366
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorMaintenance.frx":13D2
         Key             =   "AIM_VendorMaintenance.frx":13F0
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   340
         Left            =   3200
         TabIndex        =   0
         Top             =   210
         Width           =   2340
         _Version        =   65536
         _ExtentX        =   4128
         _ExtentY        =   600
         Caption         =   "AIM_VendorMaintenance.frx":1434
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorMaintenance.frx":14A0
         Key             =   "AIM_VendorMaintenance.frx":14BE
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   0
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Vendor ID"
         Height          =   192
         Index           =   0
         Left            =   200
         TabIndex        =   30
         Top             =   252
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Vendor Assortment"
         Height          =   192
         Index           =   1
         Left            =   200
         TabIndex        =   29
         Top             =   600
         Width           =   2450
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbVendors 
      Left            =   120
      Top             =   6360
      _ExtentX        =   582
      _ExtentY        =   582
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_VendorMaintenance.frx":1502
      ToolBars        =   "AIM_VendorMaintenance.frx":A100
   End
End
Attribute VB_Name = "AIM_VendorMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMVendors As ADODB.Recordset
Dim rsBuyers As ADODB.Recordset

Dim VnId As String
Dim Assort As String

Dim VnId_BookMark As String
Dim Assort_BookMark As String

Private Sub cmdReviewCycleLookUp_Click()
On Error GoTo ErrorHandler

    AIM_ReviewCycleLookUp.RevCycleKey = Me.txtRevCycle.Text
    Set AIM_ReviewCycleLookUp.Cn = Cn
    AIM_ReviewCycleLookUp.Show vbModal
    
    If Not AIM_ReviewCycleLookUp.CancelFlag Then
        Me.txtRevCycle.Text = AIM_ReviewCycleLookUp.RevCycleKey
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReviewCycleLookUp_Click)"
     f_HandleErr , , , "AIM_VendorMaintenance::cmdReviewCycleLookUp_Click", Now, gDRGeneralError, True, Err

End Sub


Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
        
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
     f_HandleErr , , , "AIM_VendorMaintenance::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcReach_Code_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcReach_Code.Columns(0).Caption = getTranslationResource("Code")
    Me.dcReach_Code.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcReach_Code.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcReach_Code.Columns(0).Width = 720
    
    Me.dcReach_Code.Columns(1).Caption = getTranslationResource("Description")
    Me.dcReach_Code.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcReach_Code.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcReach_Code.Columns(1).Width = 2000
    
    Me.dcReach_Code.AddItem "C" + vbTab + getTranslationResource("Cube")
    Me.dcReach_Code.AddItem "D" + vbTab + getTranslationResource("Dollars")
    Me.dcReach_Code.AddItem "N" + vbTab + getTranslationResource("None")
    Me.dcReach_Code.AddItem "U" + vbTab + getTranslationResource("Units")
    Me.dcReach_Code.AddItem "W" + vbTab + getTranslationResource("Weight")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcReach_Code, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcReach_Code.Columns.Count - 1
'        dcReach_Code.Columns(IndexCounter).HasHeadBackColor = True
'        dcReach_Code.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcReach_Code_InitColumnProps)"
     f_HandleErr , , , "AIM_VendorMaintenance::dcReach_Code_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcTransmitPO_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcTransmitPO.Columns(0).Caption = getTranslationResource("Transmit Via")
    Me.dcTransmitPO.Columns(0).Width = 1000
        
    Me.dcTransmitPO.AddItem getTranslationResource("Fax")
    Me.dcTransmitPO.AddItem getTranslationResource("EDI")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcTransmitPO, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcTransmitPO.Columns.Count - 1
'        dcTransmitPO.Columns(IndexCounter).HasHeadBackColor = True
'        dcTransmitPO.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcTransmitPO_InitColumnProps)"
     f_HandleErr , , , "AIM_VendorMaintenance::dcTransmitPO_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_VendorMaintenance::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG06300")
    If StrComp(strMessage, "STATMSG06300") = 0 Then strMessage = "Initializing Vendor/Assortment Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Populate the dropdowns
    GetListOfBuyers
    
    'Open the Result Set
    'Initialize the Vendor/Assortment Record Set
    Set rsAIMVendors = New ADODB.Recordset
    With rsAIMVendors
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    VnId = ""
    Assort = ""
    RtnCode = f_AIMVendors_Get(VnId, Assort, SQL_GetFirst)
    
    'Check status and take approp. action
    If RtnCode <> SUCCEED Then
        f_AIMVendors_Init
    End If
    
    'Refresh Form
    Refresh_Form
        
    '************************************************************
    'Mar 01 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtVn_Min.Spin.Visible = 1
    Me.txtVn_Best.Spin.Visible = 1
    Me.txtDft_LeadTime.Spin.Visible = 1
    '************************************************************
       
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_VendorMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    If f_IsRecordsetValidAndOpen(rsAIMVendors) Then rsAIMVendors.Close
    Set rsAIMVendors = Nothing
    
    If f_IsRecordsetValidAndOpen(rsBuyers) Then rsBuyers.Close
    Set rsBuyers = Nothing
    
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_VendorMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub tbVendors_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strText As String

    'Clear errors
    Cn.Errors.Clear
    
    Write_Message ""

    RtnCode = f_AIMVendors_Update           'Update the recordset
    
    strText = getTranslationResource(AIM_VendorMaintenance.Caption)

    'Alert user to possible change
    Select Case Tool.ID
    Case "ID_AddNew", "ID_Close", "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
        "ID_GoToBookMark", "ID_LookUp"

        If gAccessLvl <> 1 And DataChanged(rsAIMVendors, adAffectCurrent) > 0 Then
            strMessage = getTranslationResource("MSGBOX06303")
            If StrComp(strMessage, "MSGBOX06303") = 0 Then strMessage = "Abandon changes to current record? "
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
            If RtnCode = vbYes Then
                rsAIMVendors.CancelUpdate
            Else
                f_AIMVendors_Save
            End If

        Else
            If Not rsAIMVendors.eof Then    'SUJIT10102003
                rsAIMVendors.CancelUpdate       'required to prevent error on requery
            End If
        End If

    End Select

    'Navigate
    Select Case Tool.ID
        Case "ID_AddNew"
            f_AIMVendors_Init
    
        Case "ID_Delete"
            'Display a confirmation msgbox
            If rsAIMVendors.EditMode <> adEditAdd Then
                strMessage = getTranslationResource("MSGBOX06300")
                If StrComp(strMessage, "MSGBOX06300") = 0 Then strMessage = "Delete the current Vendor?"
                
                RtnCode = MsgBox(strMessage, vbYesNo, strText)
                Select Case RtnCode
                Case vbYes
                    'User chose to delete.
                    rsAIMVendors.Delete
                    If f_AIMVendors_Get(VnId, Assort, SQL_Getlt) <> SUCCEED Then
                        If f_AIMVendors_Get(VnId, Assort, SQL_GetFirst) <> SUCCEED Then
                            f_AIMVendors_Init
                        End If
                    End If
        
                Case vbNo
                    'User chose to Cancel
                    
                End Select
            End If
            
        Case "ID_GetFirst"
            f_AIMVendors_Get VnId, Assort, SQL_GetFirst
    
        Case "ID_GetPrev"
            f_AIMVendors_Get VnId, Assort, SQL_Getlt
        
        Case "ID_GetNext"
            f_AIMVendors_Get VnId, Assort, SQL_GetGT
    
        Case "ID_GetLast"
            f_AIMVendors_Get VnId, Assort, SQL_GetLast
    
        Case "ID_Save"
            f_AIMVendors_Save
            
        Case "ID_SetBookMark"
            VnId_BookMark = VnId
            Assort_BookMark = Assort
            Me.tbVendors.Tools("ID_GoToBookMark").Enabled = True
    
        Case "ID_GoToBookMark"
            VnId = VnId_BookMark
            Assort = Assort_BookMark
            f_AIMVendors_Get VnId, Assort, SQL_GetEq
    
        Case "ID_LookUp"
            AIM_VendorLookUp.VnIdKey = Me.txtVnId.Text
            AIM_VendorLookUp.AssortKey = Me.txtAssort.Text
            Set AIM_VendorLookUp.Cn = Cn
            AIM_VendorLookUp.Show vbModal
            
            If Not AIM_VendorLookUp.CancelFlag Then
                
                VnId = AIM_VendorLookUp.VnIdKey
                Assort = AIM_VendorLookUp.AssortKey
                
                f_AIMVendors_Get VnId, Assort, SQL_GetEq
                
            End If
        
        Case "ID_Close"
            Unload Me
            Exit Sub

    End Select
    If Trim(VnId) <> "" Then    'SUJIT10102003
        Refresh_Form        'Refresh the form
    End If
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbVendors_ToolClick)"
     f_HandleErr , , , "AIM_VendorMaintenance::tbVendors_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function Refresh_Form()
On Error GoTo ErrorHandler
    
    'Enable/Disable Key fields
    If rsAIMVendors.EditMode = adEditAdd Then
        If gAccessLvl <> 1 Then tbVendors.Tools("ID_Save").Enabled = True
        Me.tbVendors.Tools("ID_Delete").Enabled = False
        Me.tbVendors.Tools("ID_AddNew").Enabled = False
        Me.txtVnId.Enabled = True
        Me.txtAssort.Enabled = True
        Me.tbVendors.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")
    Else
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            Me.tbVendors.Tools("ID_Save").Enabled = False
            Me.tbVendors.Tools("ID_Delete").Enabled = False
            Me.tbVendors.Tools("ID_AddNew").Enabled = False
        Else
            Me.tbVendors.Tools("ID_Save").Enabled = True
            Me.tbVendors.Tools("ID_Delete").Enabled = True
            Me.tbVendors.Tools("ID_AddNew").Enabled = True
        End If

        Me.txtVnId.Enabled = False
        Me.txtAssort.Enabled = False
        Me.tbVendors.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Edit")
    End If
    
    Me.txtVnId.Text = rsAIMVendors("VnId").Value
    Me.txtAssort.Text = rsAIMVendors("Assort").Value
    
    Me.txtVName.Text = rsAIMVendors("VName").Value
    Me.txtVAddress1.Text = rsAIMVendors("VAddress1").Value
    Me.txtVAddress2.Text = rsAIMVendors("VAddress2").Value
    Me.txtVCity.Text = rsAIMVendors("VCity").Value
    Me.txtVState.Text = rsAIMVendors("VState").Value
    Me.txtVZip.Text = rsAIMVendors("VZip").Value
    Me.txtVContact.Text = rsAIMVendors("VContact").Value
    Me.txtVPhone.Text = rsAIMVendors("VPhone").Value
    Me.txtVFax.Text = rsAIMVendors("VFax").Value
    Me.txtVEMail.Text = rsAIMVendors("VEMail").Value
    Me.txtVDocEMail.Text = rsAIMVendors("VDocEMail").Value
    Me.ckMDCFlag = IIf(rsAIMVendors("MDCFlag") = "Y", vbChecked, vbUnchecked)
    Me.dcAIMUsers.Text = rsAIMVendors("Dft_ById").Value
    Me.txtRevCycle.Text = rsAIMVendors("RevCycle").Value
    Me.txtDft_LeadTime.Value = rsAIMVendors("Dft_LeadTime").Value
    Me.txtVn_Min.Value = rsAIMVendors("Vn_Min").Value
    Me.txtVn_Best.Value = rsAIMVendors("Vn_Best").Value
    Me.dcReach_Code.Text = rsAIMVendors("Reach_Code").Value
    Me.dcTransmitPO.Text = xlTransmitPO(rsAIMVendors("TransmitPO").Value)
    Me.ckPOByZone = IIf(rsAIMVendors("POByZone").Value = "Y", vbChecked, vbUnchecked)
    Me.ckLastWeekSalesFlag = IIf(rsAIMVendors("LastWeekSalesFlag").Value = "Y", vbChecked, vbUnchecked)
    Me.txtVnComments1.Text = rsAIMVendors("VnComments1").Value
    Me.txtVnComments2.Text = rsAIMVendors("VnComments2").Value
    Me.txtShipIns.Text = rsAIMVendors("ShipIns").Value
     
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(Refresh_Form)", Err.Description
     f_HandleErr , , , "AIM_VendorMaintenance::Refresh_Form", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMVendors_Get(VnId As String, Assort As String, Action As SQL_ACTIONS)
On Error GoTo ErrorHandler

    Dim AIM_AIMVendors_GetKey_Sp As ADODB.Command
    Dim AIM_AIMVendors_GetEq_Sp As ADODB.Command
    
    Dim RtnCode As Integer
    
    Set AIM_AIMVendors_GetKey_Sp = New ADODB.Command
    With AIM_AIMVendors_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMVendors_GetKey_Sp"
        .Parameters.Refresh
'    End With
'    Get the Vendor/Assortment Key
'    Rtncode = AIMVendors_GetKey(AIM_AIMVendors_GetKey_Sp, VnId, Assort, Action)
        .Parameters("@VnId").Value = VnId
        .Parameters("@Assort").Value = Assort
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
        .Execute , , adExecuteNoRecords
        
        RtnCode = .Parameters(0).Value
        
        If RtnCode = SUCCEED Then
            VnId = .Parameters("@VnId").Value
            Assort = .Parameters("@Assort").Value
        Else
            VnId = ""
            Assort = ""
        End If
    End With
    
'    'Get the Seasonality Profile
'    If Rtncode = SUCCEED Then
        Set AIM_AIMVendors_GetEq_Sp = New ADODB.Command
        With AIM_AIMVendors_GetEq_Sp
            Set .ActiveConnection = Cn
            .CommandType = adCmdStoredProc
            .CommandText = "AIM_AIMVendors_GetEq_Sp"
            .Parameters.Refresh
            .Parameters(0).Value = 0
            .Parameters("@VnId").Value = VnId
            .Parameters("@Assort").Value = Assort
        End With
        
        'Destroy any existing stuff
        If f_IsRecordsetValidAndOpen(rsAIMVendors) Then rsAIMVendors.Close
        Set rsAIMVendors = Nothing
        'Initialize the Vendor/Assortment Record Set
        Set rsAIMVendors = New ADODB.Recordset
        With rsAIMVendors
            .CursorLocation = adUseClient
            .CursorType = adOpenDynamic
            .LockType = adLockOptimistic
        End With
        
        rsAIMVendors.Open AIM_AIMVendors_GetEq_Sp
        RtnCode = AIM_AIMVendors_GetEq_Sp(0).Value
        
        'Rtncode = AIMVendors_GetEq(AIM_AIMVendors_GetEq_Sp, rsAIMVendors, VnId, Assort)
        
'    End If
    
    f_AIMVendors_Get = RtnCode

    'Clean up
    If Not (AIM_AIMVendors_GetKey_Sp Is Nothing) Then Set AIM_AIMVendors_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_AIMVendors_GetKey_Sp = Nothing
    If Not (AIM_AIMVendors_GetEq_Sp Is Nothing) Then Set AIM_AIMVendors_GetEq_Sp.ActiveConnection = Nothing
    Set AIM_AIMVendors_GetEq_Sp = Nothing
    
Exit Function
ErrorHandler:
    If Not (AIM_AIMVendors_GetKey_Sp Is Nothing) Then Set AIM_AIMVendors_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_AIMVendors_GetKey_Sp = Nothing
    If Not (AIM_AIMVendors_GetEq_Sp Is Nothing) Then Set AIM_AIMVendors_GetEq_Sp.ActiveConnection = Nothing
    Set AIM_AIMVendors_GetEq_Sp = Nothing
    'Err.Raise Err.Number, Err.source & "(f_AIMVendors_Get)", Err.Description
    f_HandleErr , , , "AIM_VendorMaintenance::f_AIMVendors_Get", Now, gDRGeneralError, True, Err
End Function


Private Function f_AIMVendors_Init()
On Error GoTo ErrorHandler

    rsAIMVendors.AddNew
        
    rsAIMVendors("VnId").Value = getTranslationResource("New Vendor")
    rsAIMVendors("Assort").Value = ""
    
    rsAIMVendors("VName").Value = ""
    rsAIMVendors("VAddress1").Value = ""
    rsAIMVendors("VAddress2").Value = ""
    rsAIMVendors("VCity").Value = ""
    rsAIMVendors("VState").Value = ""
    rsAIMVendors("VZip").Value = ""
    rsAIMVendors("VContact").Value = ""
    rsAIMVendors("VPhone").Value = ""
    rsAIMVendors("VFax").Value = ""
    rsAIMVendors("VEMail").Value = ""
    rsAIMVendors("VDocEMail").Value = ""
    rsAIMVendors("MDCFlag") = "N"
    rsAIMVendors("Dft_ById").Value = ""
    rsAIMVendors("RevCycle").Value = ""
    rsAIMVendors("Dft_LeadTime").Value = 1
    rsAIMVendors("Vn_Min").Value = 0
    rsAIMVendors("Vn_Best").Value = 0
    rsAIMVendors("Reach_Code").Value = "N"
    rsAIMVendors("POByZone").Value = "N"
    rsAIMVendors("LastWeekSalesFlag").Value = "N"
    rsAIMVendors("VnComments1").Value = ""
    rsAIMVendors("VnComments2").Value = ""
    
    'Enable/Disable Key fields
    Me.txtVnId.Enabled = True
    Me.txtAssort.Enabled = True
    Me.tbVendors.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")

    Me.tbVendors.Tools("ID_Delete").Enabled = False
    Me.tbVendors.Tools("ID_AddNew").Enabled = False
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMVendors_Init)", Err.Description
     f_HandleErr , , , "AIM_VendorMaintenance::f_AIMVendors_Init", Now, gDRGeneralError, True, Err
End Function


Private Function f_AIMVendors_Save() As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    
    Cn.Errors.Clear
    
    'Validate required info
    RtnCode = UserInputValidation
    If RtnCode = SUCCEED Then
    
        'Save
        rsAIMVendors.Update
    
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, Cn.Errors.Count - 1
        Else
            strMessage = getTranslationResource("STATMSG06301")
            If StrComp(strMessage, "STATMSG06301") = 0 Then strMessage = "Vendor/Assortment successfully updated. "
            Write_Message strMessage
        End If
    Else
        f_AIMVendors_Save = RtnCode
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMVendors_Save)", Err.Description
    f_HandleErr , , , "AIM_VendorMaintenance::f_AIMVendors_Save", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMVendors_Update()
On Error GoTo ErrorHandler

    If Not rsAIMVendors.eof Then    'SUJIT10102003
        rsAIMVendors("VnId").Value = Me.txtVnId.Text
        rsAIMVendors("Assort").Value = Me.txtAssort.Text
        
        rsAIMVendors("VName").Value = Me.txtVName.Text
        rsAIMVendors("VAddress1").Value = Me.txtVAddress1.Text
        rsAIMVendors("VAddress2").Value = Me.txtVAddress2.Text
        rsAIMVendors("VCity").Value = Me.txtVCity.Text
        rsAIMVendors("VState").Value = Me.txtVState.Text
        rsAIMVendors("VZip").Value = Me.txtVZip.Text
        rsAIMVendors("VContact").Value = Me.txtVContact.Text
        rsAIMVendors("VPhone").Value = Me.txtVPhone.Text
        rsAIMVendors("VFax").Value = Me.txtVFax.Text
        rsAIMVendors("VEMail").Value = Me.txtVEMail.Text
        rsAIMVendors("VDocEMail").Value = Me.txtVDocEMail.Text
        rsAIMVendors("MDCFlag") = IIf(Me.ckMDCFlag = vbChecked, "Y", "N")
        rsAIMVendors("Dft_ById").Value = Me.dcAIMUsers.Text
        rsAIMVendors("RevCycle").Value = Me.txtRevCycle.Text
        rsAIMVendors("Dft_LeadTime").Value = Me.txtDft_LeadTime.Value
        rsAIMVendors("Vn_Min").Value = IIf(IsNull(Me.txtVn_Min.Value), 0, Me.txtVn_Min.Value)
        rsAIMVendors("Vn_Best").Value = IIf(IsNull(Me.txtVn_Best.Value), 0, Me.txtVn_Best.Value)
        rsAIMVendors("Reach_Code").Value = Me.dcReach_Code.Text
        rsAIMVendors("TransmitPO").Value = xlTransmitPOString(Me.dcTransmitPO.Text)
        rsAIMVendors("POByZone").Value = IIf(Me.ckPOByZone = vbChecked, "Y", "N")
        rsAIMVendors("LastWeekSalesFlag").Value = IIf(Me.ckLastWeekSalesFlag = vbChecked, "Y", "N")
        rsAIMVendors("VnComments1").Value = Me.txtVnComments1.Text
        rsAIMVendors("VnComments2").Value = Me.txtVnComments2.Text
        rsAIMVendors("ShipIns").Value = Me.txtShipIns.Text
    End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMVendors_Update)", Err.Description
    f_HandleErr , , , "AIM_VendorMaintenance::f_AIMVendors_Update", Now, gDRGeneralError, True, Err
End Function

Private Function GetListOfBuyers() As Long
On Error GoTo ErrorHandler

    Dim AIM_AIMUser_List_Sp As ADODB.Command
    
    'Set default to failure
    GetListOfBuyers = -1
    
    '************************************************************
    'Build/Bind the AIM User Drop Down
    Set AIM_AIMUser_List_Sp = New ADODB.Command
    With AIM_AIMUser_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
        .CommandText = "AIM_AIMUser_List_Sp"
    End With

    'No Parameters
    
    If f_IsRecordsetValidAndOpen(rsBuyers) Then rsBuyers.Close
    Set rsBuyers = Nothing
    Set rsBuyers = New ADODB.Recordset
    
    With rsBuyers
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsBuyers.Open AIM_AIMUser_List_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsBuyers) Then
        'Bind the Data Combo Drop Downs
        Set dcAIMUsers.DataSourceList = rsBuyers
        GetListOfBuyers = 0 'Success
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetListOfBuyers)"
     f_HandleErr , , , "AIM_VendorMaintenance::GetListOfBuyers", Now, gDRGeneralError, True, Err
End Function

Private Function UserInputValidation() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX07312")
    If StrComp(strMessage, "MSGBOX07312") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim(txtVnId.Text) = "" _
    Or StrComp(txtVnId.Text, getTranslationResource("New Vendor")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    Label(0).Caption  'Vendor ID
        txtVnId.SetFocus
        MessageRequired = True
    End If
    
    If Me.txtAssort.Text = "" _
    Then
    strMessage = strMessage & vbCrLf & _
                    Label(1).Caption  'Description
        txtAssort.SetFocus
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
     f_HandleErr , , , "AIM_VendorMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function
