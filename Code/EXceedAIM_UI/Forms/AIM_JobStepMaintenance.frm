VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_JobStepMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Job Step Maintenance"
   ClientHeight    =   4785
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9975
   Icon            =   "AIM_JobStepMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   4785
   ScaleWidth      =   9975
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.Frame Frame1 
      Height          =   4215
      Left            =   60
      TabIndex        =   12
      Top             =   0
      Width           =   9855
      Begin VB.CommandButton cmdWizard 
         Caption         =   "&Wizard"
         Height          =   345
         Left            =   240
         Style           =   1  'Graphical
         TabIndex        =   4
         Top             =   1980
         Width           =   2450
      End
      Begin TDBText6Ctl.TDBText txtName 
         Height          =   345
         Left            =   2790
         TabIndex        =   0
         Top             =   240
         Width           =   6795
         _Version        =   65536
         _ExtentX        =   11994
         _ExtentY        =   600
         Caption         =   "AIM_JobStepMaintenance.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobStepMaintenance.frx":0376
         Key             =   "AIM_JobStepMaintenance.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   128
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcSubSystem 
         Bindings        =   "AIM_JobStepMaintenance.frx":03D8
         DataField       =   " "
         Height          =   345
         Left            =   2790
         TabIndex        =   1
         Top             =   660
         Width           =   2040
         DataFieldList   =   "Column 0"
         ListAutoValidate=   0   'False
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   3598
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin TDBNumber6Ctl.TDBNumber txtCmdExecSuccessCode 
         Height          =   345
         Left            =   2790
         TabIndex        =   2
         Top             =   1080
         Width           =   840
         _Version        =   65536
         _ExtentX        =   1482
         _ExtentY        =   600
         Calculator      =   "AIM_JobStepMaintenance.frx":03E3
         Caption         =   "AIM_JobStepMaintenance.frx":0403
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobStepMaintenance.frx":046F
         Keys            =   "AIM_JobStepMaintenance.frx":048D
         Spin            =   "AIM_JobStepMaintenance.frx":04D7
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   100
         MinValue        =   -100
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBText6Ctl.TDBText txtCommand 
         Height          =   1305
         Left            =   2790
         TabIndex        =   5
         Top             =   1500
         Width           =   6795
         _Version        =   65536
         _ExtentX        =   11994
         _ExtentY        =   2302
         Caption         =   "AIM_JobStepMaintenance.frx":04FF
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobStepMaintenance.frx":056B
         Key             =   "AIM_JobStepMaintenance.frx":0589
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcOnSuccessAction 
         Bindings        =   "AIM_JobStepMaintenance.frx":05CD
         DataField       =   " "
         Height          =   345
         Left            =   2790
         TabIndex        =   6
         Top             =   2940
         Width           =   4140
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         ColumnHeaders   =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   7302
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcOnFailAction 
         Bindings        =   "AIM_JobStepMaintenance.frx":05D8
         DataField       =   " "
         Height          =   345
         Left            =   2790
         TabIndex        =   9
         Top             =   3780
         Width           =   4140
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         ColumnHeaders   =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   7302
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin TDBNumber6Ctl.TDBNumber txtRetryAttempts 
         Height          =   345
         Left            =   2790
         TabIndex        =   7
         Top             =   3360
         Width           =   840
         _Version        =   65536
         _ExtentX        =   1482
         _ExtentY        =   600
         Calculator      =   "AIM_JobStepMaintenance.frx":05E3
         Caption         =   "AIM_JobStepMaintenance.frx":0603
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobStepMaintenance.frx":066F
         Keys            =   "AIM_JobStepMaintenance.frx":068D
         Spin            =   "AIM_JobStepMaintenance.frx":06D7
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtRetryInterval 
         Height          =   345
         Left            =   7545
         TabIndex        =   8
         Top             =   3360
         Width           =   840
         _Version        =   65536
         _ExtentX        =   1482
         _ExtentY        =   609
         Calculator      =   "AIM_JobStepMaintenance.frx":06FF
         Caption         =   "AIM_JobStepMaintenance.frx":071F
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobStepMaintenance.frx":078B
         Keys            =   "AIM_JobStepMaintenance.frx":07A9
         Spin            =   "AIM_JobStepMaintenance.frx":07F3
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDatabases 
         Bindings        =   "AIM_JobStepMaintenance.frx":081B
         Height          =   345
         Left            =   7545
         TabIndex        =   3
         Top             =   1080
         Width           =   2040
         DataFieldList   =   "name"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         Cols            =   1
         ColumnHeaders   =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   3598
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "name"
      End
      Begin VB.Label Label13 
         Caption         =   "Retry Interval (minutes)"
         Height          =   300
         Left            =   4980
         TabIndex        =   21
         Top             =   3405
         Width           =   2445
      End
      Begin VB.Label Label12 
         Caption         =   "Retry Attempts"
         Height          =   300
         Left            =   240
         TabIndex        =   20
         Top             =   3405
         Width           =   2445
      End
      Begin VB.Label Label11 
         Caption         =   "On Failure Action"
         Height          =   300
         Left            =   240
         TabIndex        =   19
         Top             =   3825
         Width           =   2445
      End
      Begin VB.Label Label10 
         Caption         =   "On Success Action"
         Height          =   300
         Left            =   240
         TabIndex        =   18
         Top             =   2985
         Width           =   2445
      End
      Begin VB.Label Label9 
         Caption         =   "Command"
         Height          =   300
         Left            =   240
         TabIndex        =   17
         Top             =   1500
         Width           =   2445
      End
      Begin VB.Label lblSubSystemOption 
         Caption         =   "Process exit code"
         Height          =   300
         Left            =   240
         TabIndex        =   16
         Top             =   1125
         Width           =   2445
      End
      Begin VB.Label Label7 
         Caption         =   "Type"
         Height          =   300
         Left            =   240
         TabIndex        =   15
         Top             =   705
         Width           =   2445
      End
      Begin VB.Label Label1 
         Caption         =   "Step Name"
         Height          =   300
         Left            =   240
         TabIndex        =   14
         Top             =   285
         Width           =   2445
      End
      Begin VB.Label Label2 
         Caption         =   "Database"
         Height          =   300
         Left            =   4980
         TabIndex        =   13
         Top             =   1125
         Width           =   2445
      End
   End
   Begin VB.CommandButton cmdOK 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   8430
      Style           =   1  'Graphical
      TabIndex        =   10
      Top             =   4350
      Width           =   1485
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   6870
      Style           =   1  'Graphical
      TabIndex        =   11
      Top             =   4350
      Width           =   1485
   End
End
Attribute VB_Name = "AIM_JobStepMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

'Public parameters
Public Cn As ADODB.Connection
Public AddFlag As Boolean
Public CancelFlag As Boolean
Public StepId As Long
Public JobStep As ADODB.Recordset
Public JobName As String

'Modular variables
Dim CommandLine As String
Dim sp_add_jobstep As ADODB.Command
Dim sp_update_jobstep As ADODB.Command

Dim rsDataBases As ADODB.Recordset

Private Function InitJobStep()
On Error GoTo ErrorHandler

    Me.txtName.Enabled = True
    Me.txtName = ""
    
    'Me.dcSubSystem.Text = "TSQL"
    Me.dcSubSystem.Value = "CMDEXEC"        'Make CMDEXEC the default
    
    If rsDataBases.RecordCount > 0 Then
        Me.dcDatabases.Text = "master"
    End If
    
    Me.txtCmdExecSuccessCode.Value = 0
    Me.txtCmdExecSuccessCode.Enabled = False
    
    Me.txtCommand.Text = ""
    CommandLine = ""
    Me.txtRetryAttempts.Value = 0
    
    Me.txtRetryInterval.Value = 1
    Me.txtRetryInterval.Enabled = False
    
    'Initialize the Next Step on Success/Failure
    Me.dcOnFailAction.Text = getTranslationResource("Quit the job reporting failure")
    Me.dcOnSuccessAction.Text = getTranslationResource("Quit the job reporting success")
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(InitJobStep)"
End Function


Private Function SetJobStep()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim StringIndex As Long
    Dim tempString As String
    Dim DisplayCommand As String
    
    Me.txtName = JobStep("step_name").Value
    Me.txtName.Enabled = False
    Me.dcSubSystem.Text = JobStep("subsystem").Value
    Me.dcDatabases.Text = IIf(IsNull(JobStep("database_name").Value), "", JobStep("database_name").Value)
    Me.txtCmdExecSuccessCode.Value = JobStep("cmdexec_success_code").Value
    CommandLine = JobStep("command").Value
    'Find and encrypt the password for display (the CommandLine variable should still contain the password)
    DisplayCommand = CommandLine
    StringIndex = InStr(1, DisplayCommand, ".exe")
    If StringIndex > 0 Then
        For IndexCounter = 1 To 5   'Password should be the 6th parameter
            StringIndex = InStr(StringIndex + 1, DisplayCommand, " ")
        Next
        IndexCounter = InStr(StringIndex + 1, DisplayCommand, " ")
        
        tempString = Trim(Mid(DisplayCommand, StringIndex + 1, (IndexCounter - StringIndex)))
        Me.txtCommand.Text = Trim(Mid(DisplayCommand, 1, StringIndex)) + " **********" + Replace(DisplayCommand, tempString, "**********", IndexCounter, 1)
    Else
        Me.txtCommand.Text = DisplayCommand
    End If
    
    Me.txtRetryAttempts.Value = JobStep("retry_attempts").Value
    Me.txtRetryInterval.Value = JobStep("retry_interval").Value
    Me.dcOnFailAction.Text = getTranslationResource(SQLDMO_GetNextStepDesc(JobStep("on_fail_action").Value))
    Me.dcOnSuccessAction.Text = getTranslationResource(SQLDMO_GetNextStepDesc(JobStep("on_success_action").Value))
    
    'Sub System Setup
    dcSubSystem_CloseUp
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(SetJobStep)"
End Function


Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdOK_Click()
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strText As String
    Dim ErrorDesc As String
        
    strText = getTranslationResource(Me.Caption)
    
    'Update Job Step
    If AddFlag Then
        sp_add_jobstep("@job_id").Value = Null
        sp_add_jobstep("@job_name").Value = Me.JobName
        If Me.StepId > 0 Then
            sp_add_jobstep("@step_id").Value = StepId
        Else
            sp_add_jobstep("@step_id").Value = Null
        End If
        sp_add_jobstep("@step_name").Value = Me.txtName.Text
        sp_add_jobstep("@subsystem").Value = Me.dcSubSystem.Text
        'sp_add_jobstep("@command").Value = Me.txtCommand.Text
        'sp_add_jobstep("@command").Value = CommandLine
        If InStr(1, CommandLine, ".exe") = 0 Then
            sp_add_jobstep("@command").Value = Me.txtCommand.Text
        Else
            sp_add_jobstep("@command").Value = CommandLine
        End If
        
        sp_add_jobstep("@additional_parameters").Value = Null
        sp_add_jobstep("@cmdexec_success_code").Value = Me.txtCmdExecSuccessCode.Value
        sp_add_jobstep("@on_success_action").Value = SQLDMO_GetNextStepCode(Me.dcOnSuccessAction.Text)
        sp_add_jobstep("@on_success_step_id").Value = 0
        sp_add_jobstep("@on_fail_action").Value = SQLDMO_GetNextStepCode(Me.dcOnFailAction.Text)
        sp_add_jobstep("@on_fail_step_id").Value = 0
        sp_add_jobstep("@server").Value = Null
        sp_add_jobstep("@database_name").Value = Me.dcDatabases.Text
        sp_add_jobstep("@database_user_name").Value = Null
        sp_add_jobstep("@retry_attempts").Value = Me.txtRetryAttempts.Value
        sp_add_jobstep("@retry_interval").Value = Me.txtRetryInterval.Value
        sp_add_jobstep("@output_file_name").Value = Null
        sp_add_jobstep("@flags").Value = 0
        
        sp_add_jobstep.Execute
        
        'Check for errors
         If sp_add_jobstep(0).Value = 1 Then
            strMessage = getTranslationResource("MSGBOX02900")
            If StrComp(strMessage, "MSGBOX02900") = 0 Then strMessage = "Error processing job."
            MsgBox strMessage + vbCrLf + vbCrLf + Err.Description, vbCritical + vbOKOnly, strText

            Exit Sub
        End If
       
    Else
        sp_update_jobstep("@job_id").Value = Null
        sp_update_jobstep("@job_name").Value = Me.JobName
        If Me.StepId > 0 Then
            sp_update_jobstep("@step_id").Value = StepId
        Else
            sp_update_jobstep("@step_id").Value = Null
        End If
        sp_update_jobstep("@step_name").Value = Me.txtName.Text
        sp_update_jobstep("@subsystem").Value = Me.dcSubSystem.Text
        'sp_update_jobstep("@command").Value = Me.txtCommand.Text
        'sp_update_jobstep("@command").Value = CommandLine
        If InStr(1, CommandLine, ".exe") = 0 Then
            sp_add_jobstep("@command").Value = Me.txtCommand.Text
        Else
            sp_add_jobstep("@command").Value = CommandLine
        End If
        sp_update_jobstep("@additional_parameters").Value = Null
        sp_update_jobstep("@cmdexec_success_code").Value = Me.txtCmdExecSuccessCode.Value
        sp_update_jobstep("@on_success_action").Value = SQLDMO_GetNextStepCode(Me.dcOnSuccessAction.Text)
        sp_update_jobstep("@on_success_step_id").Value = 0
        sp_update_jobstep("@on_fail_action").Value = SQLDMO_GetNextStepCode(Me.dcOnFailAction.Text)
        sp_update_jobstep("@on_fail_step_id").Value = 0
        sp_update_jobstep("@server").Value = Null
        sp_update_jobstep("@database_name").Value = Me.dcDatabases.Text
        sp_update_jobstep("@database_user_name").Value = Null
        sp_update_jobstep("@retry_attempts").Value = Me.txtRetryAttempts.Value
        sp_update_jobstep("@retry_interval").Value = Me.txtRetryInterval.Value
        sp_update_jobstep("@output_file_name").Value = Null
        sp_update_jobstep("@flags").Value = 0
        
        sp_update_jobstep.Execute
    
         If sp_update_jobstep(0).Value = 1 Then
            strMessage = getTranslationResource("MSGBOX02900")
            If StrComp(strMessage, "MSGBOX02900") = 0 Then strMessage = "Error processing job."
            MsgBox strMessage + vbCrLf + vbCrLf + Err.Description, vbCritical + vbOKOnly, strText
            
            Exit Sub
        End If
    End If
       
    CancelFlag = False
    Unload Me
    
Exit Sub
ErrorHandler:
    'check for known errors:
    If Err.Number = -2147217900 _
    Then
        ErrorDesc = Err.Description
        strMessage = getTranslationResource("MSGBOX02900")
        If StrComp(strMessage, "MSGBOX02900") = 0 Then strMessage = "Error processing job."
        strText = getTranslationResource(Me.Caption)
        MsgBox strMessage + vbCrLf + vbCrLf + ErrorDesc, vbCritical + vbOKOnly, strText
    Else
        f_HandleErr , , , "AIM_JobScheduleMaintenance::cmdOK_Click", Now, gDRGeneralError, True, Err
    End If
    
End Sub

Private Sub cmdWizard_Click()
On Error GoTo ErrorHandler

'    AIM_JobWizard.CommandLine = Me.txtCommand.Text
    AIM_JobWizard.CommandLine = CommandLine
    AIM_JobWizard.Show vbModal, Me

    If Not AIM_JobWizard.CancelFlag Then
'        Me.txtCommand.Text = AIM_JobWizard.CommandLine
        CommandLine = AIM_JobWizard.CommandLine
        txtCommand = AIM_JobWizard.DisplayCommandLine
    End If
    
    Set AIM_JobWizard = Nothing
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::cmdWizard_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDatabases_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcDatabases.Columns(0).Caption = getTranslationResource("Database")
    Me.dcDatabases.Columns(0).Name = "name"
    Me.dcDatabases.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcDatabases.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDatabases.Columns(0).DataField = "name"
    Me.dcDatabases.Columns(0).Width = 1800
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDatabases, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcDatabases.Columns.Count - 1
'        dcDatabases.Columns(IndexCounter).HasHeadBackColor = True
'        dcDatabases.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcDatabases_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcOnFailAction_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcOnFailAction.ListWidthAutoSize = True

    Me.dcOnFailAction.Columns(0).Caption = getTranslationResource("On Success Action")
    Me.dcOnFailAction.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcOnFailAction.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcOnFailAction.Columns(0).Width = 2880

    Me.dcOnFailAction.AddItem getTranslationResource("Quit the job reporting success")
    Me.dcOnFailAction.AddItem getTranslationResource("Quit the job reporting failure")
    Me.dcOnFailAction.AddItem getTranslationResource("Goto the next step")
    Me.dcOnFailAction.AddItem getTranslationResource("Goto the next identified step")
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcOnFailAction, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcOnFailAction.Columns.Count - 1
'        dcOnFailAction.Columns(IndexCounter).HasHeadBackColor = True
'        dcOnFailAction.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcOnFailAction_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcOnSuccessAction_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcOnSuccessAction.Columns(0).Caption = getTranslationResource("On Success Action")
    Me.dcOnSuccessAction.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcOnSuccessAction.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcOnSuccessAction.Columns(0).Width = 2880

    '**OPXLATE** have to wait on AIMSQLDMO.bas being translated.
    Me.dcOnSuccessAction.AddItem getTranslationResource(SQLDMO_GetNextStepDesc(1))
    Me.dcOnSuccessAction.AddItem getTranslationResource(SQLDMO_GetNextStepDesc(2))
    Me.dcOnSuccessAction.AddItem getTranslationResource(SQLDMO_GetNextStepDesc(3))
    Me.dcOnSuccessAction.AddItem getTranslationResource(SQLDMO_GetNextStepDesc(4))
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcOnSuccessAction, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcOnSuccessAction.Columns.Count - 1
'        dcOnSuccessAction.Columns(IndexCounter).HasHeadBackColor = True
'        dcOnSuccessAction.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcOnSuccessAction_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcOnSuccessAction_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    dcSubSystem_CloseUp

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcOnSuccessAction_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcOnSuccessAction_ValidateList(Text As String, RtnPassed As Integer)
On Error GoTo ErrorHandler

    dcSubSystem_CloseUp

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcOnSuccessAction_ValidateList", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcSubSystem_CloseUp()
On Error GoTo ErrorHandler

    Select Case UCase(Me.dcSubSystem.Value)
        Case "TSQL"
            Me.txtCmdExecSuccessCode.Enabled = False
            Me.dcDatabases.Enabled = True
                
        Case "CMDEXEC"
            Me.dcDatabases.Enabled = False
            Me.txtCmdExecSuccessCode.Enabled = True
            
        Case Else
            Me.dcDatabases.Enabled = False
            Me.txtCmdExecSuccessCode.Enabled = False
    
    End Select

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcSubSystem_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcSubSystem_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcSubSystem.Columns(0).Caption = getTranslationResource("Sub System")
    Me.dcSubSystem.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcSubSystem.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcSubSystem.Columns(0).Width = 2000
    
    Me.dcSubSystem.Columns(1).Caption = getTranslationResource("Description")
    Me.dcSubSystem.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcSubSystem.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcSubSystem.Columns(1).Width = 3400
    
    'Initialize list
    Me.dcSubSystem.AddItem "ACTIVESCRIPTING" + vbTab + _
                             getTranslationResource("Active Script")
    Me.dcSubSystem.AddItem "CMDEXEC" + vbTab + _
                             getTranslationResource("Operating-system command or executable program")
    Me.dcSubSystem.AddItem "DISTRIBUTION" + vbTab + _
                             getTranslationResource("Replication Distribution Agent job")
    Me.dcSubSystem.AddItem "SNAPSHOT" + vbTab + _
                             getTranslationResource("Replication Snapshot Agent job")
    Me.dcSubSystem.AddItem "LOGREADER" + vbTab + _
                             getTranslationResource("Replication Log Reader Agent job")
    Me.dcSubSystem.AddItem "MERGE" + vbTab + _
                             getTranslationResource("Replication Merge Agent job")
    Me.dcSubSystem.AddItem "TSQL" + vbTab + _
                             getTranslationResource("Transaction-SQL Statement")
                             
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcSubSystem, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcSubSystem.Columns.Count - 1
'        dcSubSystem.Columns(IndexCounter).HasHeadBackColor = True
'        dcSubSystem.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

    'Default to AIM's Job Wizard requirements
    'Me.dcSubSystem.Value = "CMDEXEC"
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcSubSystem_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcSubSystem_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    dcSubSystem_CloseUp
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::dcSubSystem_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim SQL As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02900")
    If StrComp(strMessage, "STATMSG02900") = 0 Then strMessage = "Initializing AIM Job Step Mainteance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
        
    'Initialize stored procedures
    Set sp_add_jobstep = New ADODB.Command
    With sp_add_jobstep
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_add_jobstep"
        .Parameters.Refresh
    End With
    
    Set sp_update_jobstep = New ADODB.Command
    With sp_update_jobstep
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_update_jobstep"
        .Parameters.Refresh
    End With
    
    'Initialize database list
    Set rsDataBases = New ADODB.Recordset
    With rsDataBases
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    'Initialize the database listing
    SQL = "SELECT name FROM master.dbo.sysdatabases" & _
        " WHERE has_dbaccess(Name) = 1" & _
        " ORDER BY name"
    rsDataBases.Open SQL, Cn
    
    If Not (rsDataBases.BOF And rsDataBases.eof) Then
        Set Me.dcDatabases.DataSourceList = rsDataBases
    End If
    
    If AddFlag Then
        InitJobStep
    Else
        SetJobStep
    End If
    
    '************************************************************
    'Mar 05 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtRetryAttempts.Spin.Visible = 1
    Me.txtRetryInterval.Spin.Visible = 1
    Me.txtCmdExecSuccessCode.Spin.Visible = 1
    '************************************************************
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If Not (sp_add_jobstep Is Nothing) Then Set sp_add_jobstep.ActiveConnection = Nothing
    If Not (sp_update_jobstep Is Nothing) Then Set sp_update_jobstep.ActiveConnection = Nothing
    
    Set sp_add_jobstep = Nothing
    Set sp_update_jobstep = Nothing
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduleMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
End Sub

