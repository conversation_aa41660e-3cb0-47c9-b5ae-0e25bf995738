VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_VendorSizing 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Vendor Sizing"
   ClientHeight    =   6030
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   12105
   Icon            =   "AIM_VendorSizing.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   6030
   ScaleWidth      =   12105
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdReturn 
      Caption         =   "E&xit"
      Height          =   372
      Left            =   7320
      Style           =   1  'Graphical
      TabIndex        =   32
      Top             =   5500
      Width           =   1500
   End
   Begin VB.CommandButton cmdReset 
      Caption         =   "&Reset"
      Height          =   372
      Left            =   8895
      Style           =   1  'Graphical
      TabIndex        =   31
      Top             =   5500
      Width           =   1500
   End
   Begin VB.CommandButton cmdSize 
      Caption         =   "&Size"
      Default         =   -1  'True
      Height          =   372
      Left            =   10470
      Style           =   1  'Graphical
      TabIndex        =   30
      Top             =   5505
      Width           =   1500
   End
   Begin VB.Frame Frame2 
      Height          =   1815
      Left            =   30
      TabIndex        =   26
      Top             =   0
      Width           =   12045
      Begin VB.CheckBox ckOrderPoint 
         Caption         =   "Size only items below their reorder point"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   3
         Top             =   1005
         Value           =   1  'Checked
         Width           =   4305
      End
      Begin VB.CheckBox ckReachPct 
         Caption         =   "Apply Reach Percentages"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   4
         Top             =   1365
         Value           =   1  'Checked
         Width           =   4305
      End
      Begin VB.CheckBox ckOUTL 
         Caption         =   "Enforce Order-Up-To-Limit"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   6480
         TabIndex        =   5
         Top             =   1015
         Value           =   1  'Checked
         Width           =   4305
      End
      Begin VB.CheckBox ckReduce 
         Caption         =   "Reduce Order Size"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   6480
         TabIndex        =   6
         Top             =   1360
         Width           =   4305
      End
      Begin TDBText6Ctl.TDBText txtVnID 
         Height          =   345
         Left            =   2750
         TabIndex        =   0
         TabStop         =   0   'False
         Top             =   240
         Width           =   1050
         _Version        =   65536
         _ExtentX        =   1852
         _ExtentY        =   600
         Caption         =   "AIM_VendorSizing.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0376
         Key             =   "AIM_VendorSizing.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   345
         Left            =   2750
         TabIndex        =   1
         TabStop         =   0   'False
         Top             =   600
         Width           =   1050
         _Version        =   65536
         _ExtentX        =   1852
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0444
         Key             =   "AIM_VendorSizing.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtVName 
         Height          =   345
         Left            =   3825
         TabIndex        =   2
         TabStop         =   0   'False
         Top             =   240
         Width           =   2910
         _Version        =   65536
         _ExtentX        =   5133
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":04A6
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0512
         Key             =   "AIM_VendorSizing.frx":0530
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label label 
         Caption         =   "Vendor"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   28
         Top             =   280
         Width           =   2450
      End
      Begin VB.Label label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   27
         Top             =   640
         Width           =   2450
      End
   End
   Begin VB.Frame Frame4 
      Caption         =   "Sizing Results"
      Height          =   3495
      Left            =   5985
      TabIndex        =   17
      Top             =   1870
      Width           =   6105
      Begin TDBText6Ctl.TDBText txtMessage 
         Height          =   675
         Left            =   195
         TabIndex        =   33
         TabStop         =   0   'False
         Top             =   2760
         Width           =   5840
         _Version        =   65536
         _ExtentX        =   10301
         _ExtentY        =   1191
         Caption         =   "AIM_VendorSizing.frx":0574
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":05E0
         Key             =   "AIM_VendorSizing.frx":05FE
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtUnits_Current 
         Height          =   345
         Left            =   2640
         TabIndex        =   34
         Top             =   1335
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0642
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":06AE
         Key             =   "AIM_VendorSizing.frx":06CC
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtDollars_Current 
         Height          =   345
         Left            =   2640
         TabIndex        =   35
         Top             =   1680
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0710
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":077C
         Key             =   "AIM_VendorSizing.frx":079A
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtCube_Current 
         Height          =   345
         Left            =   2640
         TabIndex        =   36
         Top             =   2370
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":07DE
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":084A
         Key             =   "AIM_VendorSizing.frx":0868
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtWeight_Current 
         Height          =   345
         Left            =   2640
         TabIndex        =   37
         Top             =   2025
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":08AC
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0918
         Key             =   "AIM_VendorSizing.frx":0936
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtReqDiscount 
         Height          =   345
         Left            =   4440
         TabIndex        =   38
         Top             =   645
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":097A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":09E6
         Key             =   "AIM_VendorSizing.frx":0A04
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAddlDays 
         Height          =   345
         Left            =   4440
         TabIndex        =   39
         Top             =   990
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0A48
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0AB4
         Key             =   "AIM_VendorSizing.frx":0AD2
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtUnits_Sized 
         Height          =   345
         Left            =   4440
         TabIndex        =   40
         Top             =   1335
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0B16
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0B82
         Key             =   "AIM_VendorSizing.frx":0BA0
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtCube_Sized 
         Height          =   345
         Left            =   4440
         TabIndex        =   41
         Top             =   2370
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0BE4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0C50
         Key             =   "AIM_VendorSizing.frx":0C6E
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtWeight_Sized 
         Height          =   345
         Left            =   4440
         TabIndex        =   42
         Top             =   2025
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0CB2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0D1E
         Key             =   "AIM_VendorSizing.frx":0D3C
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtDollars_Sized 
         Height          =   345
         Left            =   4440
         TabIndex        =   43
         Top             =   1680
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Caption         =   "AIM_VendorSizing.frx":0D80
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0DEC
         Key             =   "AIM_VendorSizing.frx":0E0A
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label label 
         Alignment       =   2  'Center
         BackColor       =   &H00FFFFFF&
         BackStyle       =   0  'Transparent
         Caption         =   "After Sizing"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   -1  'True
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   345
         Index           =   8
         Left            =   4440
         TabIndex        =   25
         Top             =   300
         Width           =   1590
         WordWrap        =   -1  'True
      End
      Begin VB.Label label 
         Alignment       =   2  'Center
         BackColor       =   &H00FFFFFF&
         BackStyle       =   0  'Transparent
         Caption         =   "Current"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   -1  'True
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   345
         Index           =   7
         Left            =   2640
         TabIndex        =   24
         Top             =   300
         Width           =   1590
         WordWrap        =   -1  'True
      End
      Begin VB.Label label 
         Caption         =   "Required Discount %"
         Height          =   300
         Index           =   9
         Left            =   200
         TabIndex        =   23
         Top             =   690
         Width           =   2400
      End
      Begin VB.Label label 
         Caption         =   "Cube"
         Height          =   300
         Index           =   14
         Left            =   200
         TabIndex        =   22
         Top             =   2415
         Width           =   2400
      End
      Begin VB.Label label 
         Caption         =   "Extended Cost"
         Height          =   300
         Index           =   12
         Left            =   200
         TabIndex        =   21
         Top             =   1725
         Width           =   2400
      End
      Begin VB.Label label 
         Caption         =   "Weight"
         Height          =   300
         Index           =   13
         Left            =   200
         TabIndex        =   20
         Top             =   2070
         Width           =   2400
      End
      Begin VB.Label label 
         Caption         =   "Units"
         Height          =   300
         Index           =   11
         Left            =   200
         TabIndex        =   19
         Top             =   1380
         Width           =   2400
      End
      Begin VB.Label label 
         Caption         =   "Additional Days"
         Height          =   300
         Index           =   10
         Left            =   200
         TabIndex        =   18
         Top             =   1035
         Width           =   2400
      End
   End
   Begin VB.Frame Frame1 
      Caption         =   "Sizing Parameters"
      Height          =   3495
      Left            =   30
      TabIndex        =   12
      Top             =   1870
      Width           =   5895
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcReach_Code 
         Bindings        =   "AIM_VendorSizing.frx":0E4E
         DataField       =   " "
         Height          =   345
         Left            =   2475
         TabIndex        =   7
         Top             =   345
         Width           =   1470
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2593
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtVn_Min 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "0.00"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   1
         EndProperty
         Height          =   345
         Left            =   4155
         TabIndex        =   9
         Top             =   1920
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Calculator      =   "AIM_VendorSizing.frx":0E59
         Caption         =   "AIM_VendorSizing.frx":0E79
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":0EE5
         Keys            =   "AIM_VendorSizing.frx":0F03
         Spin            =   "AIM_VendorSizing.frx":0F4D
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "######0.00;-######0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "######0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtVn_Best 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "0.00"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   1
         EndProperty
         Height          =   345
         Left            =   4155
         TabIndex        =   11
         Top             =   2280
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Calculator      =   "AIM_VendorSizing.frx":0F75
         Caption         =   "AIM_VendorSizing.frx":0F95
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":1001
         Keys            =   "AIM_VendorSizing.frx":101F
         Spin            =   "AIM_VendorSizing.frx":1069
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "######0.00;-######0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "######0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtDft_MinRchPct 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "0.0"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   1
         EndProperty
         Height          =   345
         Left            =   2475
         TabIndex        =   8
         Top             =   1920
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Calculator      =   "AIM_VendorSizing.frx":1091
         Caption         =   "AIM_VendorSizing.frx":10B1
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":111D
         Keys            =   "AIM_VendorSizing.frx":113B
         Spin            =   "AIM_VendorSizing.frx":1185
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.0;-##0.0;0.0;0.0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   100
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   1146880005
         MinValueVT      =   1549926405
      End
      Begin TDBNumber6Ctl.TDBNumber txtDft_BestRchPct 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "0.0"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   1
         EndProperty
         Height          =   345
         Left            =   2475
         TabIndex        =   10
         Top             =   2280
         Width           =   1590
         _Version        =   65536
         _ExtentX        =   2805
         _ExtentY        =   609
         Calculator      =   "AIM_VendorSizing.frx":11AD
         Caption         =   "AIM_VendorSizing.frx":11CD
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorSizing.frx":1239
         Keys            =   "AIM_VendorSizing.frx":1257
         Spin            =   "AIM_VendorSizing.frx":12A1
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "##0.0;-##0.0;0.0;0.0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0.0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   100
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   1146880005
         MinValueVT      =   977469445
      End
      Begin VB.Label label 
         Alignment       =   2  'Center
         BackColor       =   &H80000004&
         BackStyle       =   0  'Transparent
         Caption         =   "Reach %"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   -1  'True
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   3
         Left            =   2475
         TabIndex        =   29
         Top             =   1560
         Width           =   1590
         WordWrap        =   -1  'True
      End
      Begin VB.Label label 
         Alignment       =   2  'Center
         BackColor       =   &H80000004&
         BackStyle       =   0  'Transparent
         Caption         =   "Goal"
         BeginProperty Font 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   -1  'True
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Index           =   4
         Left            =   4155
         TabIndex        =   16
         Top             =   1560
         Width           =   1590
         WordWrap        =   -1  'True
      End
      Begin VB.Label label 
         Caption         =   "Best Buy"
         Height          =   300
         Index           =   6
         Left            =   195
         TabIndex        =   15
         Top             =   2325
         Width           =   2250
      End
      Begin VB.Label label 
         Caption         =   "Minimum"
         Height          =   300
         Index           =   5
         Left            =   195
         TabIndex        =   14
         Top             =   1965
         Width           =   2250
      End
      Begin VB.Label label 
         Caption         =   "Reach Type"
         Height          =   300
         Index           =   2
         Left            =   200
         TabIndex        =   13
         Top             =   390
         Width           =   2250
      End
   End
End
Attribute VB_Name = "AIM_VendorSizing"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Assort As String
Public Dft_MinRchPct As Double
Public Dft_BestRchPct As Double
Public KFactor As Double
Public MinROAI As Double
Public Reach_Code As String
Public ReleasedOpt As Boolean
Public Total_Cube As Double
Public Total_Dollars As Double
Public Total_Units As Double
Public Total_Weight As Double
Public Vn_Min As Double
Public Vn_Best As Double
Public VnId As String
Public VName As String
Public ById As String   'Mohammed - MultiVendor

Public rs As ADODB.Recordset

Private Sub ckReduce_Click()
On Error GoTo ErrorHandler

    If Me.ckReduce = vbChecked Then
        Me.ckOrderPoint = vbChecked
        Me.ckReachPct = vbUnchecked
        Me.ckOUTL = vbChecked
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckReduce_Click)"
     f_HandleErr , , , "AIM_VendorSizing:::ckReduce_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReset_Click()
On Error GoTo ErrorHandler

    'Reset the Vendor SOQ to the Suggested Order Quantity
    If Not f_IsRecordsetOpenAndPopulated(rs) Then Exit Sub
    
    rs.MoveFirst
    Do Until rs.eof
        rs("vsoq").Value = rs("original_vsoq").Value
        
        rs.Update
        rs.MoveNext
    Loop
    
    'Reset Sizing Totals
    Me.txtAddlDays = Format(0, "0.0")
    Me.txtReqDiscount = Format(0, "0.0")
    Me.txtCube_Sized = Format(0, "#,##0.00")
    Me.txtDollars_Sized = Format(0, "#,##0.00")
    Me.txtUnits_Sized = Format(0, "#,##0")
    Me.txtWeight_Sized = Format(0, "#,##0.00")

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReset_Click)"
     f_HandleErr , , , "AIM_VendorSizing:::cmdReset_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_VendorSizing:::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdSize_Click()
On Error GoTo ErrorHandler

    Dim AddlDays As Double
    Dim ApplyReachPct As Boolean
    Dim OUTLOpt As Boolean
    Dim ReduceOpt As Boolean
    Dim ReqDiscount As Double
    Dim ReOrderPtOpt As Boolean
    Dim RtnCode As Integer
    Dim strMessage As String
    
    Dim Cube_Sized As Double
    Dim Dollars_Sized As Double
    Dim Units_Sized As Double
    Dim Weight_Sized As Double
    
    Dim POTotals As POTOTALS_RCD
    
    'Update Variables
    Dft_MinRchPct = Me.txtDft_MinRchPct.Value
    Dft_BestRchPct = Me.txtDft_BestRchPct.Value
    Reach_Code = Me.dcReach_Code.Value
    Vn_Min = Me.txtVn_Min.Value
    Vn_Best = Me.txtVn_Best.Value
    
    'Test Vendor Best
    If Vn_Best < Vn_Min Then
        Vn_Best = Vn_Min
    End If
    
    'Set options
    ReOrderPtOpt = IIf(Me.ckOrderPoint = vbChecked, True, False)
    ReduceOpt = IIf(Me.ckReduce = vbChecked, True, False)
    ApplyReachPct = IIf(Me.ckReachPct = vbChecked, True, False)
    OUTLOpt = IIf(Me.ckOUTL = vbChecked, True, False)
    
    'Check for invalid combinations
    If ReduceOpt Then
        ReOrderPtOpt = True
        ApplyReachPct = False
        OUTLOpt = True
    End If
    
    RtnCode = VendorSizing(rs, POTotals, Reach_Code, Vn_Min, Dft_MinRchPct, Vn_Best, _
        Dft_BestRchPct, ReOrderPtOpt, ApplyReachPct, OUTLOpt, AddlDays, KFactor, MinROAI, _
        ReleasedOpt, ReduceOpt)
                 
    'Check return values
    Select Case RtnCode
    Case 0                  'Failed Apply Reach Percentages Goal
        strMessage = getTranslationResource("TEXTMSG06500")
        If StrComp(strMessage, "TEXTMSG06500") = 0 Then strMessage = "Sizing failed -- no lines to size."
        
    Case 1                  'Sizing unnecessary; SOQ Amount exceed buying goal
        strMessage = getTranslationResource("TEXTMSG06501")
        If StrComp(strMessage, "TEXTMSG06501") = 0 Then strMessage = "Sizing failed -- Reach Percentages Goals not met."
        
    Case 2                  'No data to size
        strMessage = getTranslationResource("TEXTMSG06502")
        If StrComp(strMessage, "TEXTMSG06502") = 0 Then strMessage = "Sizing unnecessary -- Suggested Order Amount exceeds goal."
               
    Case 3                  'Sizing successful
        strMessage = getTranslationResource("TEXTMSG06503")
        If StrComp(strMessage, "TEXTMSG06503") = 0 Then strMessage = "Sizing successful."
                
        'Update display values
        Me.txtAddlDays = Format(AddlDays, "0.0")
        Me.txtCube_Sized = Format(POTotals.VSOQ_Cube, "#,##0.00")
        Me.txtDollars_Sized = Format(POTotals.VSOQ_Dollars, "#,##0.00")
        Me.txtUnits_Sized = Format(POTotals.VSOQ_Units, "#,##0")
        Me.txtWeight_Sized = Format(POTotals.VSOQ_Weight, "#,##0.00")
        Me.txtReqDiscount.Text = Format(POTotals.ReqDiscount * 100, "##0.00")
        
        ResetVendorSizing  'Mohammed - MultiVendor
    Case 4
        Me.txtMessage.Text = "Invalid Reach Code (None) specified."
    End Select
    
        Me.txtMessage.Text = strMessage

    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdSize_Click)"
     f_HandleErr , , , "AIM_VendorSizing:::cmdSize_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcReach_Code_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcReach_Code.Columns(0).Caption = getTranslationResource("Code")
    Me.dcReach_Code.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcReach_Code.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcReach_Code.Columns(0).Width = 720
    
    Me.dcReach_Code.Columns(1).Caption = getTranslationResource("Description")
    Me.dcReach_Code.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcReach_Code.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcReach_Code.Columns(1).Width = 2000
    
    Me.dcReach_Code.AddItem "C" + vbTab + getTranslationResource("Cube")
    Me.dcReach_Code.AddItem "D" + vbTab + getTranslationResource("Dollars")
    Me.dcReach_Code.AddItem "N" + vbTab + getTranslationResource("None")
    Me.dcReach_Code.AddItem "U" + vbTab + getTranslationResource("Units")
    Me.dcReach_Code.AddItem "W" + vbTab + getTranslationResource("Weight")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcReach_Code, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcReach_Code.Columns.Count - 1
'        dcReach_Code.Columns(IndexCounter).HasHeadBackColor = True
'        dcReach_Code.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcReach_Code_InitColumnProps)"
     f_HandleErr , , , "AIM_VendorSizing:::dcReach_Code_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Initialize variables
    Me.txtAssort.Text = Assort
    Me.txtDft_MinRchPct.Value = Dft_MinRchPct
    Me.txtDft_BestRchPct.Value = Dft_BestRchPct
    Me.dcReach_Code.Text = GetReachCodeDesc(Reach_Code)
    Me.txtCube_Current.Text = Format(Total_Cube, "#,##0.00")
    Me.txtDollars_Current.Text = Format(Total_Dollars, "#,##0.00")
    Me.txtUnits_Current.Text = Format(Total_Units, "#,##0")
    Me.txtWeight_Current.Text = Format(Total_Weight, "#,##0.00")
    Me.txtVn_Min.Value = Vn_Min
    Me.txtVn_Best.Value = Vn_Best
    Me.txtVnId.Text = VnId
    Me.txtVName = VName
    
    '************************************************************
    'Mar 01 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtVn_Min.Spin.Visible = 1
    Me.txtVn_Best.Spin.Visible = 1
    Me.txtDft_BestRchPct.Spin.Visible = 1
    Me.txtDft_MinRchPct.Spin.Visible = 1
    '************************************************************
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_VendorSizing:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
Dim AccessLevel As Integer
    GetTranslatedCaptions Me
    AccessLevel = VerifyAccess(gUserID, AF_VENDOR_SIZING, "")
    If AccessLevel = g_ACCESS_READ Then
       cmdSize.Enabled = False
       cmdReset.Enabled = False
    Else
        cmdSize.Enabled = True
        cmdReset.Enabled = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_VendorSizing:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    If f_IsRecordsetValidAndOpen(rs) Then rs.Close

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_VendorSizing:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub txtVn_Best_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    'Vendor Best cannot be less than Vendor Minimum
    If CDbl(Me.txtVn_Best.Value) < CDbl(Me.txtVn_Min.Value) Then
        Me.txtVn_Best.Value = Me.txtVn_Min.Value
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtVn_Best_Validate)"
     f_HandleErr , , , "AIM_VendorSizing:::txtVn_Best_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub ResetVendorSizing()
'Mohammed - MultiVendor - Begin
'Reset the VnSizeFlag on AIMPO table if Vednor Sizing is done successfully
On Error GoTo ErrorHandler

Dim cmUpdate As ADODB.Command
Dim SqlStmt As String
Dim RtnCode As Integer
Dim Cn As ADODB.Connection

Set Cn = New ADODB.Connection
RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False, , , Me.Caption)
If RtnCode <> SUCCEED Then
   Exit Sub
End If

SqlStmt = "Update AIMPO Set VndSizeFlag = 'N' Where ById = '" & ById & "' AND VnId = '" & VnId & "' AND Assort = '" & Assort & "' "
    
Set cmUpdate = New ADODB.Command
cmUpdate.ActiveConnection = Cn
cmUpdate.CommandText = SqlStmt
cmUpdate.CommandType = adCmdText
cmUpdate.Execute
    
If Not (cmUpdate Is Nothing) Then Set cmUpdate.ActiveConnection = Nothing
Set cmUpdate = Nothing
    
RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , Me.Caption)
Exit Sub

ErrorHandler:
    If Not (cmUpdate Is Nothing) Then Set cmUpdate.ActiveConnection = Nothing
    Set cmUpdate = Nothing
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, , , Me.Caption)
    'Err.Raise Err.Number, Err.source & "(ResetVenSizing))", Err.Description
    f_HandleErr , , , "AIM_VendorSizing:::ResetVenSizing", Now, gDRGeneralError, True, Err
End Sub
'Mohammed - MultiVendor - End

