VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_ItemFilter 
   Caption         =   "SSA AIM Item Filter"
   ClientHeight    =   7275
   ClientLeft      =   75
   ClientTop       =   345
   ClientWidth     =   11655
   LinkTopic       =   "Form1"
   ScaleHeight     =   7275
   ScaleWidth      =   11655
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdClear 
      Caption         =   "Clear &All"
      Height          =   345
      Left            =   2280
      Style           =   1  'Graphical
      TabIndex        =   26
      Top             =   6840
      Width           =   1485
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return"
      Height          =   345
      Left            =   8280
      Style           =   1  'Graphical
      TabIndex        =   23
      Top             =   6840
      Width           =   1485
   End
   Begin VB.CommandButton cmdRestore 
      Caption         =   "Restore &Originals"
      Height          =   345
      Left            =   3840
      Style           =   1  'Graphical
      TabIndex        =   25
      Top             =   6840
      Width           =   2565
   End
   Begin VB.CommandButton cmdApply 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   10065
      Style           =   1  'Graphical
      TabIndex        =   22
      Top             =   6840
      Width           =   1485
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   6720
      Style           =   1  'Graphical
      TabIndex        =   24
      Top             =   6840
      Width           =   1485
   End
   Begin VB.Frame fraForecastFilters 
      Height          =   6615
      Left            =   120
      TabIndex        =   27
      Top             =   60
      Width           =   11430
      Begin VB.CommandButton cmdLocationLookup 
         Height          =   310
         Left            =   11040
         MouseIcon       =   "AIM_ItemFilter.frx":0000
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ItemFilter.frx":0152
         Style           =   1  'Graphical
         TabIndex        =   16
         Top             =   195
         Width           =   310
      End
      Begin VB.CommandButton cmdItemLookup 
         Height          =   310
         Left            =   5280
         MouseIcon       =   "AIM_ItemFilter.frx":029C
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ItemFilter.frx":03EE
         Style           =   1  'Graphical
         TabIndex        =   1
         Top             =   215
         Width           =   310
      End
      Begin VB.CommandButton cmdVendorLookup 
         Height          =   310
         Left            =   5280
         MouseIcon       =   "AIM_ItemFilter.frx":0538
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ItemFilter.frx":068A
         Style           =   1  'Graphical
         TabIndex        =   4
         Top             =   1009
         Width           =   310
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   345
         Left            =   3105
         TabIndex        =   3
         Top             =   974
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":07D4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0840
         Key             =   "AIM_ItemFilter.frx":085E
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   345
         Left            =   3105
         TabIndex        =   5
         Top             =   1371
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":08A2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":090E
         Key             =   "AIM_ItemFilter.frx":092C
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtItem 
         Height          =   345
         Left            =   3105
         TabIndex        =   0
         Top             =   180
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0970
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":09DC
         Key             =   "AIM_ItemFilter.frx":09FA
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtClassDesc 
         Height          =   345
         Index           =   0
         Left            =   5280
         TabIndex        =   8
         TabStop         =   0   'False
         Top             =   2165
         Width           =   5940
         _Version        =   65536
         _ExtentX        =   10477
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0A3E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0AAA
         Key             =   "AIM_ItemFilter.frx":0AC8
         BackColor       =   -2147483638
         EditMode        =   2
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtClassDesc 
         Height          =   345
         Index           =   1
         Left            =   5280
         TabIndex        =   10
         TabStop         =   0   'False
         Top             =   2562
         Width           =   5940
         _Version        =   65536
         _ExtentX        =   10477
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0B0C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0B78
         Key             =   "AIM_ItemFilter.frx":0B96
         BackColor       =   -2147483638
         EditMode        =   2
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtClassDesc 
         Height          =   345
         Index           =   3
         Left            =   5280
         TabIndex        =   14
         TabStop         =   0   'False
         Top             =   3360
         Width           =   5940
         _Version        =   65536
         _ExtentX        =   10477
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0BDA
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0C46
         Key             =   "AIM_ItemFilter.frx":0C64
         BackColor       =   -2147483638
         EditMode        =   2
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLcId 
         Height          =   345
         Left            =   8865
         TabIndex        =   15
         Top             =   180
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0CA8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0D14
         Key             =   "AIM_ItemFilter.frx":0D32
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtClassDesc 
         Height          =   345
         Index           =   2
         Left            =   5280
         TabIndex        =   12
         TabStop         =   0   'False
         Top             =   2959
         Width           =   5940
         _Version        =   65536
         _ExtentX        =   10477
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0D76
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0DE2
         Key             =   "AIM_ItemFilter.frx":0E00
         BackColor       =   -2147483638
         EditMode        =   2
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcBuyerID 
         Height          =   345
         Left            =   3105
         TabIndex        =   6
         Top             =   1768
         Width           =   2100
         DataFieldList   =   "UserID"
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserID"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
         Height          =   345
         Index           =   0
         Left            =   3105
         TabIndex        =   7
         Top             =   2165
         Width           =   2100
         DataFieldList   =   "Class"
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Class"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
         Height          =   345
         Index           =   1
         Left            =   3105
         TabIndex        =   9
         Top             =   2562
         Width           =   2100
         DataFieldList   =   "Class"
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Class"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
         Height          =   345
         Index           =   2
         Left            =   3105
         TabIndex        =   11
         Top             =   2959
         Width           =   2100
         DataFieldList   =   "Class"
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Class"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
         Height          =   345
         Index           =   3
         Left            =   3105
         TabIndex        =   13
         Top             =   3360
         Width           =   2100
         DataFieldList   =   "Class"
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Class"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcItStatus 
         Height          =   345
         Left            =   3105
         TabIndex        =   2
         Top             =   577
         Width           =   2100
         DataFieldList   =   "ItStat"
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "ItStat"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgResultsGrid 
         Height          =   2700
         Left            =   30
         TabIndex        =   21
         Top             =   3870
         Width           =   11355
         _Version        =   196617
         DataMode        =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         HeadLines       =   2
         MultiLine       =   0   'False
         SelectTypeCol   =   0
         SelectTypeRow   =   0
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         ExtraHeight     =   185
         CaptionAlignment=   0
         SplitterVisible =   -1  'True
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   20029
         _ExtentY        =   4762
         _StockProps     =   79
         Caption         =   "Filtered Items"
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin TDBText6Ctl.TDBText txtLDivision 
         Height          =   345
         Left            =   8865
         TabIndex        =   18
         Top             =   974
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0E44
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0EB0
         Key             =   "AIM_ItemFilter.frx":0ECE
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLRegion 
         Height          =   345
         Left            =   8865
         TabIndex        =   19
         Top             =   1371
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0F12
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":0F7E
         Key             =   "AIM_ItemFilter.frx":0F9C
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLUserDefined 
         Height          =   345
         Left            =   8865
         TabIndex        =   20
         Top             =   1768
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ItemFilter.frx":0FE0
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemFilter.frx":104C
         Key             =   "AIM_ItemFilter.frx":106A
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLStatus 
         DataField       =   " "
         Height          =   345
         Left            =   8865
         TabIndex        =   17
         Top             =   577
         Width           =   2100
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 0"
      End
      Begin VB.Label lblLGeneral 
         Caption         =   "Location Status"
         Height          =   300
         Index           =   1
         Left            =   6075
         TabIndex        =   41
         Top             =   622
         Width           =   2685
      End
      Begin VB.Label lblLGeneral 
         Caption         =   "Location Division"
         Height          =   300
         Index           =   3
         Left            =   6075
         TabIndex        =   40
         Top             =   1019
         Width           =   2685
      End
      Begin VB.Label lblLGeneral 
         Caption         =   "Location Region"
         Height          =   300
         Index           =   4
         Left            =   6075
         TabIndex        =   39
         Top             =   1416
         Width           =   2685
      End
      Begin VB.Label lblLGeneral 
         Caption         =   "User-defined"
         Height          =   300
         Index           =   5
         Left            =   6075
         TabIndex        =   38
         Top             =   1813
         Width           =   2685
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Classification 3"
         Height          =   300
         Index           =   8
         Left            =   195
         TabIndex        =   36
         Top             =   3004
         Width           =   2805
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Location ID"
         Height          =   300
         Index           =   2
         Left            =   6075
         TabIndex        =   32
         Top             =   225
         Width           =   2685
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   31
         Top             =   1416
         Width           =   2800
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Vendor ID"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   30
         Top             =   1019
         Width           =   2800
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Buyer ID"
         Height          =   300
         Index           =   4
         Left            =   200
         TabIndex        =   33
         Top             =   1813
         Width           =   2800
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Item"
         Height          =   300
         Index           =   3
         Left            =   200
         TabIndex        =   28
         Top             =   225
         Width           =   2800
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Item Status"
         Height          =   300
         Index           =   5
         Left            =   200
         TabIndex        =   29
         Top             =   622
         Width           =   2800
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Classification 1"
         Height          =   300
         Index           =   6
         Left            =   195
         TabIndex        =   34
         Top             =   2210
         Width           =   2805
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Classification 2"
         Height          =   300
         Index           =   7
         Left            =   195
         TabIndex        =   35
         Top             =   2607
         Width           =   2805
      End
      Begin VB.Label lblForecastFilters 
         Caption         =   "Classification 4"
         Height          =   300
         Index           =   9
         Left            =   195
         TabIndex        =   37
         Top             =   3405
         Width           =   2805
      End
   End
End
Attribute VB_Name = "AIM_ItemFilter"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public p_Item As String
Public p_ItemStatus As String
Public p_VendorID As String
Public p_Assortment As String
Public p_LocationID As String
Public p_BuyerID As String
Public p_Class1 As String
Public p_Class2 As String
Public p_Class3 As String
Public p_Class4 As String
Public p_LStatus As String
Public p_LDivision As String
Public p_LRegion As String
Public p_LUserDefined As String

Public p_SubFilterItem As String
Public p_SubFilterItemStatus As String
Public p_SubFilterVendorID As String
Public p_SubFilterAssortment As String
Public p_SubFilterLocationID As String
Public p_SubFilterBuyerID As String
Public p_SubFilterClass1 As String
Public p_SubFilterClass2 As String
Public p_SubFilterClass3 As String
Public p_SubFilterClass4 As String
Public p_SubFilterLStatus As String
Public p_SubFilterLDivision As String
Public p_SubFilterLRegion As String
Public p_SubFilterLUserDefined As String

Public p_ADOConn As ADODB.Connection
Public p_LockFields As Boolean
Public p_LockExtraLocFields As Boolean

Public r_CancelFlag As Boolean
Public r_arrItems As XArrayDB

Dim rsAIMUsers As ADODB.Recordset
Dim rsItStatus As ADODB.Recordset

Dim rsDisconnected As ADODB.Recordset
Dim arrItems As XArrayDB

Dim rsClass1 As ADODB.Recordset
Dim rsClass2 As ADODB.Recordset
Dim rsClass3 As ADODB.Recordset
Dim rsClass4 As ADODB.Recordset

Dim m_ColIdx_Item As String
Dim m_ColIdx_ItemDesc As String
Dim m_ColIdx_ItemStatus As String
Dim m_ColIdx_VendorID As String
Dim m_ColIdx_Assortment As String
Dim m_ColIdx_LocationID As String
Dim m_ColIdx_BuyerID As String
Dim m_ColIdx_Class1 As String
Dim m_ColIdx_Class2 As String
Dim m_ColIdx_Class3 As String
Dim m_ColIdx_Class4 As String
Dim m_ColIdx_LStatus As String
Dim m_ColIdx_LDivision As String
Dim m_ColIdx_LRegion As String
Dim m_ColIdx_LUserDefined As String

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    'Clear
    Me.txtItem.Text = ""
    Me.dcItStatus.Text = ""
    Me.txtVnId.Text = ""
    Me.txtAssort.Text = ""
    Me.dcBuyerID.Text = ""
    Me.dcClass(0).Text = ""
    Me.dcClass(1).Text = ""
    Me.dcClass(2).Text = ""
    Me.dcClass(3).Text = ""
    Me.txtLcId.Text = ""
    Me.dcLStatus.Text = ""
    Me.txtLDivision.Text = ""
    Me.txtLRegion.Text = ""
    Me.txtLUserDefined.Text = ""
    
    If f_IsRecordsetValidAndOpen(rsDisconnected) Then rsDisconnected.Close
    Set rsDisconnected = Nothing
    dgResultsGrid.ReBind
    
    dgResultsGrid.Caption = getTranslationResource("Filtered Items")

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdRestore_Click()
On Error GoTo ErrorHandler
    
    'Restore Originals
    Me.txtItem.Text = IIf(Trim$(p_Item) = "", p_SubFilterItem, p_Item)
    Me.dcItStatus.Text = IIf(Trim$(p_ItemStatus) = "", p_SubFilterItemStatus, p_ItemStatus)
    Me.txtVnId.Text = IIf(Trim$(p_VendorID) = "", p_SubFilterVendorID, p_VendorID)
    Me.txtAssort.Text = IIf(Trim$(p_Assortment) = "", p_SubFilterAssortment, p_Assortment)
    Me.dcBuyerID.Text = IIf(Trim$(p_BuyerID) = "", p_SubFilterBuyerID, p_BuyerID)
    Me.dcClass(0).Text = IIf(Trim$(p_Class1) = "", p_SubFilterClass1, p_Class1)
    Me.dcClass(1).Text = IIf(Trim$(p_Class2) = "", p_SubFilterClass2, p_Class2)
    Me.dcClass(2).Text = IIf(Trim$(p_Class3) = "", p_SubFilterClass3, p_Class3)
    Me.dcClass(3).Text = IIf(Trim$(p_Class4) = "", p_SubFilterClass4, p_Class4)
    Me.txtLcId.Text = IIf(Trim$(p_LocationID) = "", p_SubFilterLocationID, p_LocationID)
    Me.dcLStatus.Text = IIf(Trim$(p_LStatus) = "", p_SubFilterLStatus, p_LStatus)
    Me.txtLDivision.Text = IIf(Trim$(p_LDivision) = "", p_SubFilterLDivision, p_LDivision)
    Me.txtLRegion.Text = IIf(Trim$(p_LRegion) = "", p_SubFilterLRegion, p_LRegion)
    Me.txtLUserDefined.Text = IIf(Trim$(p_LUserDefined) = "", p_SubFilterLUserDefined, p_LUserDefined)
    
    If f_IsRecordsetValidAndOpen(rsDisconnected) Then rsDisconnected.Close
    Set rsDisconnected = Nothing
    dgResultsGrid.ReBind
    
    dgResultsGrid.Caption = getTranslationResource("Filtered Items")

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdRestore_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdRestore_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdItemLookup_Click()
On Error GoTo ErrorHandler

    Screen.MousePointer = vbHourglass
    
    If Me.txtItem.Text = "" Then
        AIM_ItemLookUp.ItemKey = getTranslationResource("All")
    Else
        AIM_ItemLookUp.ItemKey = Me.txtItem.Text
    End If
    AIM_ItemLookUp.LcIdKey = Me.txtLcId.Text
    AIM_ItemLookUp.VnIdKey = Me.txtVnId.Text
    AIM_ItemLookUp.AssortKey = Me.txtAssort.Text
    AIM_ItemLookUp.ByIdKey = Me.dcBuyerID.Text
    
    Set AIM_ItemLookUp.Cn = p_ADOConn
    AIM_ItemLookUp.Show vbModal
    
    If Not AIM_ItemLookUp.CancelFlag Then
        Me.txtLcId.Text = IIf(AIM_ItemLookUp.LcIdKey = getTranslationResource("All"), "", AIM_ItemLookUp.LcIdKey)
        Me.txtItem.Text = IIf(AIM_ItemLookUp.ItemKey = getTranslationResource("All"), "", AIM_ItemLookUp.ItemKey)
        Me.txtAssort.Text = IIf(AIM_ItemLookUp.AssortKey = getTranslationResource("All"), "", AIM_ItemLookUp.AssortKey)
        Me.dcBuyerID.Text = IIf(AIM_ItemLookUp.ByIdKey = getTranslationResource("All"), "", AIM_ItemLookUp.ByIdKey)
        Me.txtVnId.Text = IIf(AIM_ItemLookUp.VnIdKey = getTranslationResource("All"), "", AIM_ItemLookUp.VnIdKey)
    End If

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdItemLookup_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdItemLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdLocationLookup_Click()
On Error GoTo ErrorHandler

    Screen.MousePointer = vbHourglass
    
    If Me.txtLcId.Text = "" Then
        AIM_LocationLookUp.LcId = getTranslationResource("All")
    Else
        AIM_LocationLookUp.LcId = Me.txtLcId.Text
    End If
    
    Set AIM_LocationLookUp.Cn = p_ADOConn
    
    AIM_LocationLookUp.Show vbModal
    
    If Not AIM_LocationLookUp.CancelFlag Then
        Me.txtLcId.Text = IIf(AIM_LocationLookUp.LcId = getTranslationResource("All"), "", AIM_LocationLookUp.LcId)
    End If

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLocationLookup_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdLocationLookup_Click", Now, gDRGeneralError, True, Err

End Sub

Private Sub cmdApply_Click()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    
    Screen.MousePointer = vbHourglass
    dgResultsGrid.Redraw = False
    
    RtnCode = GetFilteredItems
    dgResultsGrid.ReBind
    
    If RtnCode = 0 Then
        If f_IsRecordsetOpenAndPopulated(rsDisconnected) Then
            dgResultsGrid.Rows = rsDisconnected.RecordCount
            Me.dgResultsGrid.Caption = _
                getTranslationResource("Number of filtered Items", True) & dgResultsGrid.Rows
        Else
            Me.dgResultsGrid.Caption = getTranslationResource("Number of filtered Items: 0")
        End If
    Else
        Me.dgResultsGrid.Caption = getTranslationResource("Number of filtered Items: 0")
    End If
    
    Screen.MousePointer = vbNormal
    dgResultsGrid.Redraw = True
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    dgResultsGrid.Redraw = True
    'f_HandleErr Me.Caption & "(cmdApply_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdApply_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler
        
    Screen.MousePointer = vbHourglass
    
    Me.r_CancelFlag = True
    Unload Me
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Screen.MousePointer = vbHourglass
    
    p_Item = Me.txtItem.Text
    p_ItemStatus = Me.dcItStatus.Text
    p_VendorID = Me.txtVnId.Text
    p_Assortment = Me.txtAssort.Text
    p_BuyerID = Me.dcBuyerID.Text
    p_Class1 = Me.dcClass(0).Text
    p_Class2 = Me.dcClass(1).Text
    p_Class3 = Me.dcClass(2).Text
    p_Class4 = Me.dcClass(3).Text
    p_LocationID = Me.txtLcId.Text
    p_LStatus = Me.dcLStatus.Text
    p_LDivision = Me.txtLDivision.Text
    p_LRegion = Me.txtLRegion.Text
    p_LUserDefined = Me.txtLUserDefined.Text
    
    p_SubFilterItem = Me.txtItem.Text
    p_SubFilterItemStatus = Me.dcItStatus.Text
    p_SubFilterVendorID = Me.txtVnId.Text
    p_SubFilterAssortment = Me.txtAssort.Text
    p_SubFilterBuyerID = Me.dcBuyerID.Text
    p_SubFilterClass1 = Me.dcClass(0).Text
    p_SubFilterClass2 = Me.dcClass(1).Text
    p_SubFilterClass3 = Me.dcClass(2).Text
    p_SubFilterClass4 = Me.dcClass(3).Text
    p_SubFilterLocationID = Me.txtLcId.Text
    p_SubFilterLStatus = Me.dcLStatus.Text
    p_SubFilterLDivision = Me.txtLDivision.Text
    p_SubFilterLRegion = Me.txtLRegion.Text
    p_SubFilterLUserDefined = Me.txtLUserDefined.Text
    
    Set Me.r_arrItems = arrItems
    
    Screen.MousePointer = vbNormal
    
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdVendorLookup_Click()
On Error GoTo ErrorHandler
    
    Screen.MousePointer = vbHourglass
    
    If Me.txtVnId.Text = "" Then
        AIM_VendorLookUp.VnIdKey = getTranslationResource("All")
    Else
        AIM_VendorLookUp.VnIdKey = Me.txtVnId.Text
    End If
    
    AIM_VendorLookUp.AssortKey = Me.txtAssort.Text
    Set AIM_VendorLookUp.Cn = p_ADOConn
    AIM_VendorLookUp.Show vbModal
    
    If Not AIM_VendorLookUp.CancelFlag Then
        Me.txtVnId.Text = IIf(AIM_VendorLookUp.VnIdKey = getTranslationResource("All"), "", AIM_VendorLookUp.VnIdKey)
        Me.txtAssort.Text = IIf(AIM_VendorLookUp.AssortKey = getTranslationResource("All"), "", AIM_VendorLookUp.AssortKey)
    End If

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdVendorLookup_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::cmdVendorLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcBuyerID_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcBuyerID.Columns(0).Name = "UserID"
    Me.dcBuyerID.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcBuyerID.Columns(0).Width = 1000
        
    Me.dcBuyerID.Columns(1).Name = "UserName"
    Me.dcBuyerID.Columns(1).Caption = getTranslationResource("User Name")
    Me.dcBuyerID.Columns(1).Width = 2000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths Me.dcBuyerID, ACW_EXPAND
    End If

'    For IndexCounter = 0 To Me.dcBuyerID.Columns.Count - 1
'        Me.dcBuyerID.Columns(IndexCounter).HasHeadBackColor = True
'        Me.dcBuyerID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcBuyerID_InitColumnProps)"
	 f_HandleErr , , , "AIM_ItemFilter::dcBuyerID_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcClass_Change(Index As Integer)
On Error GoTo ErrorHandler

    If IsNull(dcClass(Index).Text) _
    Or Trim(dcClass(Index).Text) = "" _
    Then
        Me.txtClassDesc(Index).Text = ""
    Else
        Me.txtClassDesc(Index).Text = GetClassDescription(p_ADOConn, Me.dcClass(Index).Text, (Index + 1))
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcClass(" & CStr(Index) & ")_Change)"
	 f_HandleErr , , , "AIM_ItemFilter::dcClass(" & CStr(Index) & ")_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcClass_Click(Index As Integer)
On Error GoTo ErrorHandler

    If IsNull(dcClass(Index).Text) _
    Or Trim(dcClass(Index).Text) = "" _
    Then
        Me.txtClassDesc(Index).Text = ""
    Else
        Me.txtClassDesc(Index).Text = Me.dcClass(Index).Columns(1).Value
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcClass(" & CStr(Index) & ")_Click)"
	 f_HandleErr , , , "AIM_ItemFilter::dcClass(" & CStr(Index) & ")_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcClass_InitColumnProps(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcClass(Index).Columns(0).Name = "Class"
    Me.dcClass(Index).Columns(0).Caption = getTranslationResource("Class")
    Me.dcClass(Index).Columns(0).Width = 1000
        
    Me.dcClass(Index).Columns(1).Name = "ClassDesc"
    Me.dcClass(Index).Columns(1).Caption = getTranslationResource("Class Description")
    Me.dcClass(Index).Columns(1).Width = 2000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths Me.dcClass(Index), ACW_EXPAND
    End If

'    For IndexCounter = 0 To Me.dcClass.Columns.Count - 1
'        Me.dcClass.Columns(IndexCounter).HasHeadBackColor = True
'        Me.dcClass.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcClass(" & CStr(Index) & ")_InitColumnProps)"
	 f_HandleErr , , , "AIM_ItemFilter::dcClass(" & CStr(Index) & ")_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcItStatus_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcItStatus.Columns(0).Name = "ItStat"
    Me.dcItStatus.Columns(0).Caption = getTranslationResource("Item Status")
    Me.dcItStatus.Columns(0).Width = 1000
        
    Me.dcItStatus.Columns(1).Name = "ItStatDesc"
    Me.dcItStatus.Columns(1).Caption = getTranslationResource("Description")
    Me.dcItStatus.Columns(1).Width = 2000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths Me.dcItStatus, ACW_EXPAND
    End If

'    For IndexCounter = 0 To Me.dcItStatus.Columns.Count - 1
'        Me.dcItStatus.Columns(IndexCounter).HasHeadBackColor = True
'        Me.dcItStatus.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcItStatus_InitColumnProps)"
	 f_HandleErr , , , "AIM_ItemFilter::dcItStatus_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLStatus_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcLStatus.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLStatus.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcLStatus.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLStatus.Columns(0).Width = 720
    
    Me.dcLStatus.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLStatus.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLStatus.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLStatus.Columns(1).Width = 2000
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths Me.dcLStatus, ACW_EXPAND
    End If
    
'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To Me.dclstatus.Columns.Count - 1
'        Me.dclstatus.Columns(IndexCounter).HasHeadBackColor = True
'        Me.dclstatus.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLStatus_InitColumnProps)"
	 f_HandleErr , , , "AIM_ItemFilter::dcLStatus_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgResultsGrid_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    If f_IsRecordsetOpenAndPopulated(rsDisconnected) Then
        Me.dgResultsGrid.Caption = _
            getTranslationResource("Number of filtered Items", True) & dgResultsGrid.Rows
    End If
    
    IndexCounter = 0
    Me.dgResultsGrid.Columns(IndexCounter).Name = "Item"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "Item"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Item")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 2000
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_Item = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).Name = "LcID"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "LcID"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Location")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_LocationID = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).Name = "ItDesc"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "ItDesc"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Item Description")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 3400
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_ItemDesc = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "ItStat"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "ItStat"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Item Status")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_ItemStatus = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "VnID"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "VnID"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Vendor ID")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_VendorID = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "Assort"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "Assort"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Assortment")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_Assortment = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "ByID"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "ByID"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Buyer ID")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_BuyerID = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "Class1"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "Class1"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Classification 1")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_Class1 = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "Class2"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "Class2"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Classification 2")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_Class2 = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "Class3"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "Class3"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Classification 3")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_Class3 = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "Class4"
    Me.dgResultsGrid.Columns(IndexCounter).Name = "Class4"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Classification 4")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_Class4 = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).Name = "LStatus"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "LStatus"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Location Status")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_LStatus = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).Name = "LDivision"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "LDivision"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Location Division")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_LDivision = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).Name = "LRegion"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "LRegion"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Location Region")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_LRegion = IndexCounter
    
    IndexCounter = IndexCounter + 1
    Me.dgResultsGrid.Columns(IndexCounter).Name = "LUserDefined"
    Me.dgResultsGrid.Columns(IndexCounter).DataField = "LUserDefined"
    Me.dgResultsGrid.Columns(IndexCounter).Caption = getTranslationResource("Location UserDefined")
    Me.dgResultsGrid.Columns(IndexCounter).Width = 1500
    Me.dgResultsGrid.Columns(IndexCounter).Style = ssStyleEdit
    Me.dgResultsGrid.Columns(IndexCounter).Locked = True
    Me.dgResultsGrid.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgResultsGrid.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
    m_ColIdx_LUserDefined = IndexCounter
    
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths Me.dgResultsGrid, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgResultsGrid.Columns.Count - 1
'        dgResultsGrid.Columns(IndexCounter).HasHeadBackColor = True
'        dgResultsGrid.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgResultsGrid.Columns(IndexCounter).Locked = False Then dgResultsGrid.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgResultsGrid_InitColumnProps)"
	 f_HandleErr , , , "AIM_ItemFilter::dgResultsGrid_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgResultsGrid_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim RowCount As Integer
    Dim Counter As Integer

    If Not f_IsRecordsetOpenAndPopulated(rsDisconnected) _
    Then Exit Sub

    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsDisconnected.MoveLast
        Else
            rsDisconnected.MoveFirst
        End If

    Else
        rsDisconnected.bookmark = StartLocation
        If ReadPriorRows Then
            rsDisconnected.MovePrevious
        Else
            rsDisconnected.MoveNext
        End If

    End If

    For Counter = 0 To RowBuf.RowCount - 1
        If rsDisconnected.BOF Or rsDisconnected.eof Then Exit For
        
        RowBuf.Value(Counter, 0) = rsDisconnected!Item
        RowBuf.Value(Counter, 1) = rsDisconnected!LcId
        RowBuf.Value(Counter, 2) = rsDisconnected!ItDesc
        RowBuf.Value(Counter, 3) = rsDisconnected!ItStat
        RowBuf.Value(Counter, 4) = rsDisconnected!VnId
        RowBuf.Value(Counter, 5) = rsDisconnected!Assort
        RowBuf.Value(Counter, 6) = rsDisconnected!ById
        RowBuf.Value(Counter, 7) = rsDisconnected!Class1
        RowBuf.Value(Counter, 8) = rsDisconnected!Class2
        RowBuf.Value(Counter, 9) = rsDisconnected!Class3
        RowBuf.Value(Counter, 10) = rsDisconnected!Class4

        RowBuf.bookmark(Counter) = rsDisconnected.bookmark

        If ReadPriorRows Then
            rsDisconnected.MovePrevious
        Else
            rsDisconnected.MoveNext
        End If

        RowCount = RowCount + 1
    Next Counter

    RowBuf.RowCount = RowCount

    Me.dgResultsGrid.Caption = _
        getTranslationResource("Number of filtered Items", True) & dgResultsGrid.Rows

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgResultsGrid_UnboundReadData)"
	 f_HandleErr , , , "AIM_ItemFilter::dgResultsGrid_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Long
    
    GetTranslatedCaptions Me
    
    RtnCode = GetAIMUsers
    RtnCode = GetItStatus
    RtnCode = GetAIMClasses
    RtnCode = GetLStatus
    
    'Initialize fields
    Me.txtItem.Text = IIf(Trim$(p_Item) = "", p_SubFilterItem, p_Item)
    Me.dcItStatus.Text = IIf(Trim$(p_ItemStatus) = "", p_SubFilterItemStatus, p_ItemStatus)
    Me.txtVnId.Text = IIf(Trim$(p_VendorID) = "", p_SubFilterVendorID, p_VendorID)
    Me.txtAssort.Text = IIf(Trim$(p_Assortment) = "", p_SubFilterAssortment, p_Assortment)
    Me.dcBuyerID.Text = IIf(Trim$(p_BuyerID) = "", p_SubFilterBuyerID, p_BuyerID)
    Me.dcClass(0).Text = IIf(Trim$(p_Class1) = "", p_SubFilterClass1, p_Class1)
    Me.dcClass(1).Text = IIf(Trim$(p_Class2) = "", p_SubFilterClass2, p_Class2)
    Me.dcClass(2).Text = IIf(Trim$(p_Class3) = "", p_SubFilterClass3, p_Class3)
    Me.dcClass(3).Text = IIf(Trim$(p_Class4) = "", p_SubFilterClass4, p_Class4)
    Me.txtLcId.Text = IIf(Trim$(p_LocationID) = "", p_SubFilterLocationID, p_LocationID)
    Me.dcLStatus.Text = IIf(Trim$(p_LStatus) = "", p_SubFilterLStatus, p_LStatus)
    Me.txtLDivision.Text = IIf(Trim$(p_LDivision) = "", p_SubFilterLDivision, p_LDivision)
    Me.txtLRegion.Text = IIf(Trim$(p_LRegion) = "", p_SubFilterLRegion, p_LRegion)
    Me.txtLUserDefined.Text = IIf(Trim$(p_LUserDefined) = "", p_SubFilterLUserDefined, p_LUserDefined)
    
    If p_LockFields Then
        If p_Item = "" Then
            Me.txtItem.Enabled = True
            Me.cmdItemLookup.Enabled = True
        Else
            Me.txtItem.Enabled = False
            Me.cmdItemLookup.Enabled = False
        End If
        Me.dcItStatus.Enabled = IIf(p_ItemStatus = "", True, False)
        If p_VendorID = "" Then
            Me.txtVnId.Enabled = True
            Me.cmdVendorLookup.Enabled = True
        Else
            Me.txtVnId.Enabled = False
            Me.cmdVendorLookup.Enabled = False
        End If
        Me.txtAssort.Enabled = IIf(p_Assortment = "", True, False)
        Me.dcClass(0).Enabled = IIf(p_Class1 = "", True, False)
        Me.dcClass(1).Enabled = IIf(p_Class2 = "", True, False)
        Me.dcClass(2).Enabled = IIf(p_Class3 = "", True, False)
        Me.dcClass(3).Enabled = IIf(p_Class4 = "", True, False)
        If p_LocationID = "" Then
            Me.txtLcId.Enabled = True
            Me.cmdLocationLookup.Enabled = True
        Else
            Me.txtLcId.Enabled = False
            Me.cmdLocationLookup.Enabled = False
        End If
    
        Me.dcLStatus.Enabled = IIf(p_LStatus = "", True, False)
        Me.txtLDivision.Enabled = IIf(p_LDivision = "", True, False)
        Me.txtLRegion.Enabled = IIf(p_LRegion = "", True, False)
        Me.txtLUserDefined.Enabled = IIf(p_LUserDefined = "", True, False)
        
        cmdClear.Enabled = False
    End If
    
    'A.Stocksdale - start
    'Remove this once forecast mod has been upgraded/removed
    If p_LockExtraLocFields Then
        Me.dcLStatus.Enabled = False
        Me.txtLDivision.Enabled = False
        Me.txtLRegion.Enabled = False
        Me.txtLUserDefined.Enabled = False
    End If
    'A.Stocksdale - end
    
    If p_ADOConn Is Nothing Then
        Set p_ADOConn = New ADODB.Connection
        RtnCode = SQLConnection(p_ADOConn, CONNECTION_OPEN, True, , , Me.Caption)
    End If

    Me.r_CancelFlag = False
    
    Screen.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
	 f_HandleErr , , , "AIM_ItemFilter::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
    If f_IsRecordsetValidAndOpen(rsItStatus) Then rsItStatus.Close
    Set rsItStatus = Nothing
    
    If f_IsRecordsetValidAndOpen(rsDisconnected) Then rsDisconnected.Close
    Set rsDisconnected = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass1) Then rsClass1.Close
    Set rsClass1 = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass2) Then rsClass2.Close
    Set rsClass2 = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass3) Then rsClass3.Close
    Set rsClass3 = Nothing
    
    If f_IsRecordsetValidAndOpen(rsClass4) Then rsClass4.Close
    Set rsClass4 = Nothing
        
    Set arrItems = Nothing

End Sub

Private Function GetAIMUsers() As Long
On Error GoTo ErrorHandler

    Dim AIM_AIMUser_List_Sp As ADODB.Command

    'Set default to failure
    GetAIMUsers = -1
    
    Set AIM_AIMUser_List_Sp = New ADODB.Command
    With AIM_AIMUser_List_Sp
        Set .ActiveConnection = p_ADOConn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMUser_List_Sp"
        .Parameters.Append AIM_AIMUser_List_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        'Set common values
    End With
    
    '************************************************************
    'Build/Bind the AIM User Drop Down
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    Set rsAIMUsers = New ADODB.Recordset
    
    With rsAIMUsers
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsAIMUsers.Open AIM_AIMUser_List_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Set Me.dcBuyerID.DataSourceList = rsAIMUsers
        GetAIMUsers = 0 'Success
    End If

    If Not (AIM_AIMUser_List_Sp Is Nothing) Then Set AIM_AIMUser_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMUser_List_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_AIMUser_List_Sp Is Nothing) Then Set AIM_AIMUser_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMUser_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetAIMUsers)"
	 f_HandleErr , , , "AIM_ItemFilter::GetAIMUsers", Now, gDRGeneralError, True, Err
End Function

Private Function GetItStatus() As Long
On Error GoTo ErrorHandler

    Dim AIM_ItemStatus_List_Sp As ADODB.Command
    'Set default to failure
    GetItStatus = -1
    
    Set AIM_ItemStatus_List_Sp = New ADODB.Command
    With AIM_ItemStatus_List_Sp
        Set .ActiveConnection = p_ADOConn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ItemStatus_List_Sp"
        .Parameters.Append AIM_ItemStatus_List_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        'Set common values
    End With
    '************************************************************
    'Build/Bind the Item Status Drop Down
    If f_IsRecordsetValidAndOpen(rsItStatus) Then rsItStatus.Close
    Set rsItStatus = Nothing
    Set rsItStatus = New ADODB.Recordset
    With rsItStatus
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsItStatus.Open AIM_ItemStatus_List_Sp
    If f_IsRecordsetOpenAndPopulated(rsItStatus) Then
        Set Me.dcItStatus.DataSourceList = rsItStatus
        GetItStatus = 0 'Success
    End If

    If Not (AIM_ItemStatus_List_Sp Is Nothing) Then Set AIM_ItemStatus_List_Sp.ActiveConnection = Nothing
    Set AIM_ItemStatus_List_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_ItemStatus_List_Sp Is Nothing) Then Set AIM_ItemStatus_List_Sp.ActiveConnection = Nothing
    Set AIM_ItemStatus_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetItStatus)"
	 f_HandleErr , , , "AIM_ItemFilter::GetItStatus", Now, gDRGeneralError, True, Err
End Function

Private Function GetAIMClasses() As Long
On Error GoTo ErrorHandler

    Dim AIM_ClassByClassLevel_List_Sp As ADODB.Command

    'Set default to failure
    GetAIMClasses = -1
    
    Set AIM_ClassByClassLevel_List_Sp = New ADODB.Command
    With AIM_ClassByClassLevel_List_Sp
        Set .ActiveConnection = p_ADOConn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ClassByClassLevel_List_Sp"
        .Parameters.Append AIM_ClassByClassLevel_List_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append AIM_ClassByClassLevel_List_Sp.CreateParameter("@ClassLevel", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_ClassByClassLevel_List_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 10)
        
        'Set common values
        .Parameters("@LangID").Value = gLangID
    End With
    
    '************************************************************
    'Build/Bind the AIM User Drop Down
    With AIM_ClassByClassLevel_List_Sp
        .Parameters("@ClassLevel").Value = 1
    End With
    If f_IsRecordsetValidAndOpen(rsClass1) Then rsClass1.Close
    Set rsClass1 = Nothing
    Set rsClass1 = New ADODB.Recordset
    With rsClass1
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass1.Open AIM_ClassByClassLevel_List_Sp
    If f_IsRecordsetOpenAndPopulated(rsClass1) Then
        Set Me.dcClass(0).DataSourceList = rsClass1
    End If
    
    '************************************************************
    'Class level 2
    With AIM_ClassByClassLevel_List_Sp
        .Parameters("@ClassLevel").Value = 2
    End With
    If f_IsRecordsetValidAndOpen(rsClass2) Then rsClass2.Close
    Set rsClass2 = Nothing
    Set rsClass2 = New ADODB.Recordset
    With rsClass2
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass2.Open AIM_ClassByClassLevel_List_Sp
    If f_IsRecordsetOpenAndPopulated(rsClass2) Then
        Set Me.dcClass(1).DataSourceList = rsClass2
    End If
    
    '************************************************************
    'Class level 3
    With AIM_ClassByClassLevel_List_Sp
        .Parameters("@ClassLevel").Value = 3
    End With
    If f_IsRecordsetValidAndOpen(rsClass3) Then rsClass3.Close
    Set rsClass3 = Nothing
    Set rsClass3 = New ADODB.Recordset
    With rsClass3
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass3.Open AIM_ClassByClassLevel_List_Sp
    If f_IsRecordsetOpenAndPopulated(rsClass3) Then
        Set Me.dcClass(2).DataSourceList = rsClass3
    End If
    
    '************************************************************
    'Class level 4
    With AIM_ClassByClassLevel_List_Sp
        .Parameters("@ClassLevel").Value = 4
    End With
    If f_IsRecordsetValidAndOpen(rsClass4) Then rsClass4.Close
    Set rsClass4 = Nothing
    Set rsClass4 = New ADODB.Recordset
    With rsClass4
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsClass4.Open AIM_ClassByClassLevel_List_Sp
    If f_IsRecordsetOpenAndPopulated(rsClass4) Then
        Set Me.dcClass(3).DataSourceList = rsClass4
    End If

    GetAIMClasses = 0   'Success
    
    If Not (AIM_ClassByClassLevel_List_Sp Is Nothing) Then Set AIM_ClassByClassLevel_List_Sp.ActiveConnection = Nothing
    Set AIM_ClassByClassLevel_List_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_ClassByClassLevel_List_Sp Is Nothing) Then Set AIM_ClassByClassLevel_List_Sp.ActiveConnection = Nothing
    Set AIM_ClassByClassLevel_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetAIMClasses)"
	 f_HandleErr , , , "AIM_ItemFilter::GetAIMClasses", Now, gDRGeneralError, True, Err
End Function


Private Function GetFilteredItems() As Long
On Error GoTo ErrorHandler

    Dim AIM_ItemFilter_List_Sp As ADODB.Command
    Dim RtnCode As Long
    
    Dim m_Item As String
    Dim m_ItemStatus As String
    Dim m_VendorID As String
    Dim m_Assortment As String
    Dim m_LocationID As String
    Dim m_BuyerID As String
    Dim m_Class1 As String
    Dim m_Class2 As String
    Dim m_Class3 As String
    Dim m_Class4 As String
    Dim m_LStatus As String
    Dim m_LDivision As String
    Dim m_LRegion As String
    Dim m_LUserDefined As String
    
    Dim RowCounter As Long
    Dim ColCounter As Long
    Dim rsConnected As ADODB.Recordset
        
    'Set default to failure
    GetFilteredItems = -1
    
    m_Item = Trim$(Me.txtItem.Text)
    m_ItemStatus = Trim$(Me.dcItStatus.Text)
    m_VendorID = Trim$(Me.txtVnId.Text)
    m_Assortment = Trim$(Me.txtAssort.Text)
    m_LocationID = Trim$(Me.txtLcId.Text)
    m_BuyerID = Trim$(Me.dcBuyerID.Text)
    m_Class1 = Trim$(Me.dcClass(0).Text)
    m_Class2 = Trim$(Me.dcClass(1).Text)
    m_Class3 = Trim$(Me.dcClass(2).Text)
    m_Class4 = Trim$(Me.dcClass(3).Text)
    m_LStatus = Trim$(Me.dcLStatus.Text)
    m_LDivision = Trim$(Me.txtLDivision.Text)
    m_LRegion = Trim$(Me.txtLRegion.Text)
    m_LUserDefined = Trim$(Me.txtLUserDefined.Text)
    

    '************************************************************
    'Build/Bind the AIM User Drop Down
    Set AIM_ItemFilter_List_Sp = New ADODB.Command
    With AIM_ItemFilter_List_Sp
        Set .ActiveConnection = p_ADOConn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ItemFilter_List_Sp"
        
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@VnId", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@Assort", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@Class1", adVarWChar, adParamInput, 50)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@Class2", adVarWChar, adParamInput, 50)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@Class3", adVarWChar, adParamInput, 50)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@Class4", adVarWChar, adParamInput, 50)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@ByID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@ItStat", adVarWChar, adParamInput, 1)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@LStatus", adVarWChar, adParamInput, 1)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@LDivision", adVarWChar, adParamInput, 20)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@LRegion", adVarWChar, adParamInput, 20)
        .Parameters.Append AIM_ItemFilter_List_Sp.CreateParameter("@LUserDefined", adVarWChar, adParamInput, 30)
        
        'Set values
        .Parameters("@Item").Value = IIf(Trim$(m_Item) = "", Null, Trim$(m_Item))
        .Parameters("@VnID").Value = IIf(Trim$(m_VendorID) = "", Null, Trim$(m_VendorID))
        .Parameters("@Assort").Value = IIf(Trim$(m_Assortment) = "", Null, Trim$(m_Assortment))
        .Parameters("@Lcid").Value = IIf(Trim$(m_LocationID) = "", Null, Trim$(m_LocationID))
        .Parameters("@Class1").Value = IIf(Trim$(m_Class1) = "", Null, Trim$(m_Class1))
        .Parameters("@Class2").Value = IIf(Trim$(m_Class2) = "", Null, Trim$(m_Class2))
        .Parameters("@Class3").Value = IIf(Trim$(m_Class3) = "", Null, Trim$(m_Class3))
        .Parameters("@Class4").Value = IIf(Trim$(m_Class4) = "", Null, Trim$(m_Class4))
        .Parameters("@ById").Value = IIf(Trim$(m_BuyerID) = "", Null, Trim$(m_BuyerID))
        .Parameters("@ItStat").Value = IIf(Trim$(m_ItemStatus) = "", Null, Trim$(m_ItemStatus))
        .Parameters("@LStatus").Value = IIf(Trim$(m_LStatus) = "", Null, Trim$(m_LStatus))
        .Parameters("@LDivision").Value = IIf(Trim$(m_LDivision) = "", Null, Trim$(m_LDivision))
        .Parameters("@LRegion").Value = IIf(Trim$(m_LRegion) = "", Null, Trim$(m_LRegion))
        .Parameters("@LUserDefined").Value = IIf(Trim$(m_LUserDefined) = "", Null, Trim$(m_LUserDefined))
    End With
    
    Set rsConnected = New ADODB.Recordset
    With rsConnected
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly

        .Open AIM_ItemFilter_List_Sp

    End With
    
    RtnCode = AIM_ItemFilter_List_Sp.Parameters(0).Value
    'Empty the existing items recordset
    If f_IsRecordsetValidAndOpen(rsDisconnected) Then rsDisconnected.Close
    Set rsDisconnected = Nothing
    'Populate
    If f_IsRecordsetOpenAndPopulated(rsConnected) Then
        Set arrItems = New XArrayDB
        arrItems.LoadRows rsConnected.GetRows(adGetRowsRest)
        
        'Associate with a disconnected recordset
        Set rsDisconnected = New ADODB.Recordset
        rsDisconnected.Fields.Append "Item", adBSTR, 25
        rsDisconnected.Fields.Append "LcID", adBSTR, 25
        rsDisconnected.Fields.Append "ItDesc", adBSTR, 30
        rsDisconnected.Fields.Append "ItStat", adBSTR, 1
        rsDisconnected.Fields.Append "VnID", adBSTR, 25
        rsDisconnected.Fields.Append "Assort", adBSTR, 25
        rsDisconnected.Fields.Append "ByID", adBSTR, 30
        rsDisconnected.Fields.Append "Class1", adBSTR, 50
        rsDisconnected.Fields.Append "Class2", adBSTR, 50
        rsDisconnected.Fields.Append "Class3", adBSTR, 50
        rsDisconnected.Fields.Append "Class4", adBSTR, 50
        rsDisconnected.Fields.Append "LStatus", adBSTR, 1
        rsDisconnected.Fields.Append "LDivision", adBSTR, 20
        rsDisconnected.Fields.Append "LRegion", adBSTR, 20
        rsDisconnected.Fields.Append "LUserDefined", adBSTR, 30
        
        rsDisconnected.Open
        For RowCounter = arrItems.LowerBound(1) To arrItems.UpperBound(1)
            With rsDisconnected
                .AddNew
                For ColCounter = arrItems.LowerBound(2) To arrItems.UpperBound(2)
                    .Fields(ColCounter) = arrItems(RowCounter, ColCounter)
                Next
            End With
        Next
    
        GetFilteredItems = 0 'Success
    Else
        GetFilteredItems = -1   'Failure
    End If

    If f_IsRecordsetValidAndOpen(rsConnected) Then rsConnected.Close
    Set rsConnected = Nothing
    If Not (AIM_ItemFilter_List_Sp Is Nothing) Then Set AIM_ItemFilter_List_Sp.ActiveConnection = Nothing
    Set AIM_ItemFilter_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsConnected) Then rsConnected.Close
    Set rsConnected = Nothing
    If Not (AIM_ItemFilter_List_Sp Is Nothing) Then Set AIM_ItemFilter_List_Sp.ActiveConnection = Nothing
    Set AIM_ItemFilter_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetFilteredItems)"
	 f_HandleErr , , , "AIM_ItemFilter::GetFilteredItems", Now, gDRGeneralError, True, Err
End Function

Private Function GetLStatus() As Long
On Error GoTo ErrorHandler

    Dim rsCodeLookup As ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    
    'Set default to failure
    GetLStatus = -1
    
    '************************************************************
    'Build/Bind the Location Status dropdown
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = p_ADOConn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_LSTATUS
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcLStatus.AddItem rsCodeLookup!CodeID & vbTab & rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    End If
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetLStatus)"
	 f_HandleErr , , , "AIM_ItemFilter::GetLStatus", Now, gDRGeneralError, True, Err
End Function




