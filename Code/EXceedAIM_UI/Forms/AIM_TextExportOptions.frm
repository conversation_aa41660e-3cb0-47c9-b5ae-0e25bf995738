VERSION 5.00
Object = "{F9043C88-F6F2-101A-A3C9-08002B2F49FB}#1.2#0"; "COMDLG32.OCX"
Begin VB.Form AIM_TextExportOptions 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Text Export Options"
   ClientHeight    =   3345
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   6915
   ControlBox      =   0   'False
   Icon            =   "AIM_TextExportOptions.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3345
   ScaleWidth      =   6915
   ShowInTaskbar   =   0   'False
   StartUpPosition =   3  'Windows Default
   Begin MSComDlg.CommonDialog cmdialog 
      Left            =   240
      Top             =   2880
      _ExtentX        =   688
      _ExtentY        =   688
      _Version        =   393216
   End
   Begin VB.CommandButton btnCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3600
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   2880
      Width           =   1575
   End
   Begin VB.CommandButton btnOK 
      Caption         =   "&OK"
      Height          =   345
      Left            =   5220
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   2880
      Width           =   1575
   End
   Begin VB.Frame Frame2 
      Height          =   2655
      Left            =   60
      TabIndex        =   6
      Top             =   120
      Width           =   6795
      Begin VB.CommandButton btnOpen 
         Caption         =   "..."
         Height          =   340
         Left            =   6255
         TabIndex        =   11
         Top             =   240
         Width           =   345
      End
      Begin VB.TextBox txtFileName 
         Height          =   340
         Left            =   2750
         TabIndex        =   10
         Top             =   240
         Width           =   3495
      End
      Begin VB.CheckBox chkSuppressBlank 
         Caption         =   "Suppress blank lines"
         Height          =   300
         Left            =   220
         TabIndex        =   2
         Top             =   1665
         Width           =   4575
      End
      Begin VB.TextBox txtFieldDelimiter 
         Height          =   340
         Left            =   2750
         TabIndex        =   0
         Top             =   600
         Width           =   3855
      End
      Begin VB.CheckBox chkUnicode 
         Caption         =   "Unicode"
         Height          =   300
         Left            =   220
         TabIndex        =   3
         Top             =   2055
         Width           =   4575
      End
      Begin VB.TextBox txtPageDelimiter 
         Height          =   340
         Left            =   2750
         TabIndex        =   1
         Top             =   960
         Width           =   3855
      End
      Begin VB.Label lblTag 
         Caption         =   "Field Delimiter"
         Height          =   300
         Index           =   1
         Left            =   220
         TabIndex        =   9
         Top             =   645
         Width           =   2445
      End
      Begin VB.Label lblTag 
         Caption         =   "Page Delimiter"
         Height          =   300
         Index           =   0
         Left            =   220
         TabIndex        =   8
         Top             =   1005
         Width           =   2445
      End
      Begin VB.Label lblTag 
         Caption         =   "File Name"
         Height          =   300
         Index           =   2
         Left            =   220
         TabIndex        =   7
         Top             =   285
         Width           =   2445
      End
   End
End
Attribute VB_Name = "AIM_TextExportOptions"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cancelled As Boolean

Private Sub btnCancel_Click()
On Error GoTo ErrorHandler

    Cancelled = True
    Me.Hide

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(btnCancel_Click)"
     f_HandleErr , , , "AIM_TextExportOptions:::btnOpen_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub btnOK_Click()
On Error GoTo ErrorHandler

    Cancelled = False
    Me.Hide

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(btnOK_Click)"
     f_HandleErr , , , "AIM_TextExportOptions:::btnOK_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub btnOpen_Click()
On Error GoTo ErrorHandler

    Dim Filter As String
    
    Filter = "Text File (*.txt)|*.TXT|All Files (*.*)|*.*"
    
    'Causes a trappable error to occur when the user hits the 'Cancel' button
    Me.cmdialog.CancelError = True
    Me.cmdialog.DialogTitle = getTranslationResource("Save File")
    Me.cmdialog.FileName = ""
    Me.cmdialog.Filter = Filter
    Me.cmdialog.FilterIndex = 1
    Me.cmdialog.Flags = cdlOFNOverwritePrompt
    Me.cmdialog.ShowSave
    
    If Not Err = cdlCancel Then '
        txtFileName.Text = cmdialog.FileName
    Else
        txtFileName.Text = ""
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(btnOpen_Click)"
     f_HandleErr , , , "AIM_TextExportOptions:::btnOpen_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_TextExportOptions:::Form_Load", Now, gDRGeneralError, True, Err
End Sub
