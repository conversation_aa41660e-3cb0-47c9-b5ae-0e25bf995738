VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_RolesMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Roles Maintenance"
   ClientHeight    =   9240
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   6930
   Icon            =   "AIM_RolesMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   9240
   ScaleWidth      =   6930
   ShowInTaskbar   =   0   'False
   Begin VB.Frame fraAIMUsers 
      ClipControls    =   0   'False
      Height          =   1905
      Left            =   79
      TabIndex        =   8
      Top             =   120
      Width           =   6735
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcRoleID 
         Height          =   345
         Left            =   2760
         TabIndex        =   3
         Top             =   1440
         Width           =   2175
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         _Version        =   196617
         DataMode        =   2
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns.Count   =   2
         Columns(0).Width=   3200
         Columns(0).Caption=   "Role Level"
         Columns(0).Name =   "Role Level"
         Columns(0).DataField=   "Column 0"
         Columns(0).DataType=   8
         Columns(0).FieldLen=   256
         Columns(1).Width=   3200
         Columns(1).Caption=   "Level Description"
         Columns(1).Name =   "Level Description"
         Columns(1).DataField=   "Column 1"
         Columns(1).DataType=   8
         Columns(1).FieldLen=   256
         _ExtentX        =   3836
         _ExtentY        =   609
         _StockProps     =   93
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 0"
      End
      Begin TDBText6Ctl.TDBText txtRoleDescription 
         Height          =   345
         Left            =   2760
         TabIndex        =   1
         Top             =   720
         Width           =   3855
         _Version        =   65536
         _ExtentX        =   6800
         _ExtentY        =   609
         Caption         =   "AIM_RolesMaintenance.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_RolesMaintenance.frx":0376
         Key             =   "AIM_RolesMaintenance.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcRepRoles 
         Height          =   345
         Left            =   2760
         TabIndex        =   2
         Top             =   1080
         Width           =   2175
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   3836
         _ExtentY        =   609
         _StockProps     =   93
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 0"
      End
      Begin TDBText6Ctl.TDBText txtRoleID 
         Height          =   345
         Left            =   2760
         TabIndex        =   0
         Top             =   360
         Width           =   2055
         _Version        =   65536
         _ExtentX        =   3625
         _ExtentY        =   609
         Caption         =   "AIM_RolesMaintenance.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_RolesMaintenance.frx":0444
         Key             =   "AIM_RolesMaintenance.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Role ID"
         Height          =   225
         Index           =   0
         Left            =   195
         TabIndex        =   12
         Top             =   420
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "Role Description"
         Height          =   300
         Index           =   1
         Left            =   195
         TabIndex        =   11
         Top             =   735
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "Reports To Role"
         Height          =   300
         Index           =   2
         Left            =   195
         TabIndex        =   10
         Top             =   1095
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "Role Assigned Level"
         Height          =   300
         Index           =   3
         Left            =   195
         TabIndex        =   9
         Top             =   1455
         Width           =   2445
      End
   End
   Begin VB.Frame fraAIMFunctions 
      Height          =   6750
      Left            =   79
      TabIndex        =   7
      Top             =   1980
      Width           =   6735
      Begin VB.CommandButton cmdRevokeAll 
         Caption         =   "&Revoke All"
         Height          =   345
         Left            =   1253
         Style           =   1  'Graphical
         TabIndex        =   6
         Top             =   6240
         Width           =   2175
      End
      Begin VB.CommandButton cmdGrantAll 
         Caption         =   "&Grant All"
         Height          =   345
         Left            =   3503
         Style           =   1  'Graphical
         TabIndex        =   5
         Top             =   6240
         Width           =   2175
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMFunctions 
         Height          =   5940
         Left            =   120
         TabIndex        =   4
         Top             =   240
         Width           =   6465
         _Version        =   196617
         DataMode        =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         MultiLine       =   0   'False
         AllowColumnSwapping=   0
         SelectTypeCol   =   0
         SelectTypeRow   =   0
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         CaptionAlignment=   0
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   11404
         _ExtentY        =   10477
         _StockProps     =   79
         Caption         =   "SSA AIM Functions"
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   0
      Top             =   8760
      _ExtentX        =   1058
      _ExtentY        =   1058
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_RolesMaintenance.frx":04A6
      ToolBars        =   "AIM_RolesMaintenance.frx":9D58
   End
End
Attribute VB_Name = "AIM_RolesMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMFunctions As ADODB.Recordset
Dim rsRoleId As ADODB.Recordset
Dim AIMUsersBookMark As Variant

Dim iReportsToRoleLevel As Integer
Dim tmpRepTo, tmpLevel As Variant

Dim SCArray  As String
Dim BROLEID As Boolean

Dim ErrNumber As Long
Dim ErrSource As String
Dim ErrDescription As String

Private Sub cmdGrantAll_Click()
On Error GoTo ErrorHandler

    SCArray = String(100, "Y")
    Me.dgAIMFunctions.ReBind
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdGrantAll_Click)"
     f_HandleErr , , , "AIM_RolesMaintenance::cmdGrantAll_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdRevokeAll_Click()
On Error GoTo ErrorHandler

    SCArray = String(100, "N")
    Me.dgAIMFunctions.ReBind

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdRevokeAll_Click)"
     f_HandleErr , , , "AIM_RolesMaintenance::cmdRevokeAll_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRoleID_Click()
On Error GoTo ErrorHandler
    
    Dim strMessage As String

    If dcRoleID.Value = "0" Then
        strMessage = getTranslationResource("MSGBOX08100")
        If StrComp(strMessage, "MSGBOX08100") = 0 Then strMessage = _
        "This is the highest level in the system. This should only be assigned to System Administrator."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)

        dcRoleID.Value = tmpLevel
    End If
    
    If CInt(dcRoleID.Value) >= iReportsToRoleLevel And CInt(iReportsToRoleLevel) <> 0 Then
        strMessage = getTranslationResource("MSGBOX08101")
        If StrComp(strMessage, "MSGBOX08101") = 0 Then strMessage = _
        "The 'Reports To' level must be higher than the current role level."
        MsgBox strMessage, vbInformation + vbOKOnly + vbExclamation, getTranslationResource(Me.Caption)
       
        dcRoleID.Value = tmpLevel
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcRoleID_Click)"
    f_HandleErr , , , "AIM_RolesMaintenance::cmdRevokeAll_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRoleID_GotFocus()
On Error GoTo ErrorHandler
    
    tmpLevel = dcRoleID.Value

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dcRoleID_GotFocus)"
End Sub

Private Sub dcRoleID_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Me.dcRoleID.Columns(0).DataField = "LangID"
    Me.dcRoleID.Columns(0).Caption = getTranslationResource("Level")
    Me.dcRoleID.Columns(0).Width = 1000
    Me.dcRoleID.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRoleID.Columns(0).Alignment = ssCaptionAlignmentLeft
    
    'Me.dcRoleID.Columns(1).DataField = "LangDesc"
    Me.dcRoleID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcRoleID.Columns(1).Width = 1500
    Me.dcRoleID.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRoleID.Columns(1).Alignment = ssCaptionAlignmentLeft
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dcRoleID_InitColumnProps)"
End Sub

Private Sub dcRepRoles_Click()
On Error GoTo ErrorHandler

    Dim rstReptoRole As ADODB.Recordset
    Dim strSQL As String
    Dim strMessage As String
    
    strSQL = dcRepRoles.Value
    strSQL = "SELECT RoleLevel FROM AIMROLES WHERE RoleID = N'" & Trim$(strSQL) & "'"
    Set rstReptoRole = New ADODB.Recordset
    rstReptoRole.Open strSQL, Cn
    If Not rstReptoRole.eof Then
        iReportsToRoleLevel = rstReptoRole(0)
    End If
    If f_IsRecordsetValidAndOpen(rstReptoRole) Then rstReptoRole.Close
    Set rstReptoRole = Nothing
    
    If CInt(dcRoleID.Value) >= iReportsToRoleLevel And CInt(iReportsToRoleLevel) <> 0 Then
        strMessage = getTranslationResource("MSGBOX08109")
        If StrComp(strMessage, "MSGBOX08109") = 0 Then strMessage = _
        "The current role level cannot be higher than the 'Reports To' role level."
        MsgBox strMessage, vbInformation + vbOKOnly + vbExclamation, getTranslationResource(Me.Caption)
        dcRepRoles.Value = tmpRepTo
    End If

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    If f_IsRecordsetValidAndOpen(rstReptoRole) Then rstReptoRole.Close
    Set rstReptoRole = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr Me.Caption & "(dcRepRoles_Click)"
End Sub

Private Sub dcRepRoles_GotFocus()
On Error GoTo ErrorHandler
    
    tmpRepTo = dcRepRoles.Value

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dcRepRoles_GotFocus)"
End Sub

Private Sub dcRepRoles_InitColumnProps()
On Error GoTo ErrorHandler
    
    Me.dcRepRoles.Columns(0).Caption = getTranslationResource("Role")
    Me.dcRepRoles.Columns(0).Width = 1000
    Me.dcRepRoles.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRepRoles.Columns(0).Alignment = ssCaptionAlignmentLeft
    
    Me.dcRepRoles.Columns(1).Caption = getTranslationResource("Description")
    Me.dcRepRoles.Columns(1).Width = 2000
    Me.dcRepRoles.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRepRoles.Columns(1).Alignment = ssCaptionAlignmentLeft
    
    Me.dcRepRoles.Columns(2).Caption = getTranslationResource("Level")
    Me.dcRepRoles.Columns(2).Width = 750
    Me.dcRepRoles.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRepRoles.Columns(2).Alignment = ssCaptionAlignmentLeft
    
    
    Me.dcRepRoles.ScrollBars = ssScrollBarsAutomatic

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dcRepRoles_InitColumnProps)"
End Sub

Private Sub dgAIMFunctions_Change()
On Error GoTo ErrorHandler

    dgAIMFunctions.Update
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dgAIMFunctions_Change)"
End Sub

Private Sub dgAIMFunctions_Click()
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    If dgAIMFunctions.AllowUpdate = False Then
        'No access = no action
        strMessage = getTranslationResource("MSGBOX08102")
        If StrComp(strMessage, "MSGBOX08102") = 0 Then _
            strMessage = "Insufficient access rights. Operation cancelled."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dgAIMFunctions_Click)"
End Sub

Private Sub dgAIMFunctions_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgAIMFunctions.Columns(0).Name = "Access"
    Me.dgAIMFunctions.Columns(0).Caption = getTranslationResource("Access")
    Me.dgAIMFunctions.Columns(0).Style = ssStyleCheckBox
    Me.dgAIMFunctions.Columns(0).Width = 1100
    Me.dgAIMFunctions.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    
    Me.dgAIMFunctions.Columns(1).Name = "ReadOnly"
    Me.dgAIMFunctions.Columns(1).Caption = getTranslationResource("Read Only")
    Me.dgAIMFunctions.Columns(1).Style = ssStyleCheckBox
    Me.dgAIMFunctions.Columns(1).Width = 1100
    Me.dgAIMFunctions.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    
    Me.dgAIMFunctions.Columns(2).Name = "funcdesc"
    Me.dgAIMFunctions.Columns(2).Caption = getTranslationResource("Description")
    Me.dgAIMFunctions.Columns(2).Style = ssStyleEdit
    Me.dgAIMFunctions.Columns(2).Width = 3500
    Me.dgAIMFunctions.Columns(2).Locked = True
    Me.dgAIMFunctions.Columns(2).CaptionAlignment = ssColCapAlignUseColumnAlignment
    Me.dgAIMFunctions.Columns(2).Alignment = ssCaptionAlignmentLeft
        
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMFunctions, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMFunctions.Columns.Count - 1
'        dgAIMFunctions.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMFunctions.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMFunctions.Columns(IndexCounter).Locked = False Then dgAIMFunctions.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dgAIMFunctions_InitColumnProps)"
End Sub

Private Sub dgAIMFunctions_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer
    
    If rsAIMFunctions.RecordCount = 0 Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAIMFunctions.MoveLast
        Else
            rsAIMFunctions.MoveFirst
        End If
    
    Else
        rsAIMFunctions.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAIMFunctions.MovePrevious
        Else
            rsAIMFunctions.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        
        If rsAIMFunctions.BOF Or rsAIMFunctions.eof Then Exit For
    
        RowBuf.Value(i, 0) = IIf(Mid(SCArray, rsAIMFunctions("ctrlnbr").Value, 1) = "Y" _
            Or Mid(SCArray, rsAIMFunctions("ctrlnbr").Value, 1) = "R", True, False)
        RowBuf.Value(i, 1) = IIf(Mid(SCArray, rsAIMFunctions("ctrlnbr").Value, 1) = "R", True, False)
        'Translate the function description!
        RowBuf.Value(i, 2) = getTranslationResource(rsAIMFunctions("funcdesc").Value)

        RowBuf.Bookmark(i) = rsAIMFunctions.Bookmark
    
        If ReadPriorRows Then
            rsAIMFunctions.MovePrevious
        Else
            rsAIMFunctions.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dgAIMFunctions_UnboundReadData)"
End Sub

Private Sub dgAIMFunctions_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    rsAIMFunctions.Bookmark = WriteLocation

    If Not IsNull(RowBuf.Value(0, 0)) Then
        Mid(SCArray, rsAIMFunctions("CtrlNbr").Value, 1) = IIf(RowBuf.Value(0, 0) = True, "Y", "N")
    End If

    If Not IsNull(RowBuf.Value(0, 1)) Then
        Mid(SCArray, rsAIMFunctions("CtrlNbr").Value, 1) = IIf(RowBuf.Value(0, 1) = True, "R", "N")
    End If

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(dgAIMFunctions_UnboundWriteData)"
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Form_Activate)"
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim strYes As String
    Dim AIM_AIMRoles_List_Sp_ALL As ADODB.Command
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG08100")
    If StrComp(strMessage, "STATMSG08100") = 0 Then strMessage = "Initializing AIM Roles Maintenance ..."
    Write_Message strMessage
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    GetTranslatedCaptions Me
    
    PopulateRoleLevels
    
    PopulateRoles
    
    Set AIM_AIMRoles_List_Sp_ALL = New ADODB.Command
    With AIM_AIMRoles_List_Sp_ALL
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMRoles_List_Sp_ALL"
        .Parameters.Refresh
    End With

    Set rsRoleId = New ADODB.Recordset
    With rsRoleId
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    rsRoleId.Open AIM_AIMRoles_List_Sp_ALL
    
    'Initialize the Security Array
    SCArray = String(100, "N")
    
    If f_IsRecordsetOpenAndPopulated(rsRoleId) Then
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
            getTranslationResource("Edit")
    Else
        'f_AIMUsers_InitializeNew
    End If
    
    'Open the AIM Function Record Set
    Set rsAIMFunctions = New ADODB.Recordset
    With rsAIMFunctions
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    strSQL = "SELECT CtrlNbr, FuncDesc, RWOption" & _
            " FROM AIMFunctions" & _
            " WHERE CtrlNbr NOT IN (41,42)" & _
            " ORDER BY Funcdesc"
    rsAIMFunctions.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dgAIMFunctions.ReBind
    
    'Refresh the form
    RefreshForm
        
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(Form_Load)"
    Resume Next
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim rtnSave As Integer
    
    If DataChanged(rsRoleId, adAffectCurrent) > 0 Then
        rsRoleId("Roleid") = Me.txtRoleID
    End If
    
    If gAccessLvl <> 1 And DataChanged(rsRoleId, adAffectCurrent) > 0 Then
        strMessage = getTranslationResource("MSGBOX08103")
        If StrComp(strMessage, "MSGBOX08103") = 0 Then _
            strMessage = "Abandon changes to current record?"
        RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
        If RtnCode = vbYes Then
            rsRoleId.CancelUpdate
        Else
            rtnSave = f_AIMRole_Save
            If rtnSave = FAIL Then
                If txtRoleID.Enabled = True Then
                    txtRoleID.SetFocus
                End If
                Cancel = True
                Exit Sub
            End If
        End If
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMFunctions) Then rsAIMFunctions.Close
    Set rsAIMFunctions = Nothing
    
    DeleteBlankRoles
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        If Err.Number = -2147217885 Then
            Exit Sub
        End If
        f_HandleErr Me.Caption & "(Form_Unload)"
    End If
    Resume Next
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim UserID As String
    Dim AIMUserBookMark As Variant
    Dim strText As String
    Dim strMessage As String
    
    Dim rtnSave As Integer
    
    rtnSave = FAIL
    Write_Message ""
    If txtRoleID <> "SA" Then
        f_AIMRoles_Update
    End If
       
    strText = getTranslationResource(AIM_RolesMaintenance.Caption)
    
    'Alert user to possible change
    Select Case Tool.ID
    Case "ID_AddNew", "ID_Close", "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
        "ID_GoToBookMark", "ID_LookUp", "ID_Duplicate"
        
        If gAccessLvl <> 1 And DataChanged(rsRoleId, adAffectCurrent) > 0 Then
            strMessage = getTranslationResource("MSGBOX08103")
            If StrComp(strMessage, "MSGBOX08103") = 0 Then _
                strMessage = "Abandon changes to current record?"
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
            If RtnCode = vbYes Then
                rsRoleId.CancelUpdate
            Else
                rtnSave = f_AIMRole_Save
                If rtnSave = FAIL Then
                    If txtRoleID.Enabled = True Then txtRoleID.SetFocus
                    Exit Sub
                End If
            End If
        End If
    End Select
    
    'Navigate
    Select Case Tool.ID
    Case "ID_AddNew"
        f_AIMRoles_InitializeNew
    
    Case "ID_Duplicate"
          f_AIMRoles_InitializeDuplicate
              
    Case "ID_Delete"
        If Trim$(txtRoleID) = "SA" Then
            strMessage = getTranslationResource("MSGBOX08104")
            If StrComp(strMessage, "MSGBOX08104") = 0 Then _
                strMessage = "The 'SA' role may not be deleted."
            MsgBox strMessage, vbOKOnly + vbExclamation, strText
            Exit Sub
        Else
            strMessage = getTranslationResource("MSGBOX08105")
            If StrComp(strMessage, "MSGBOX08105") = 0 Then _
                strMessage = "Delete the current user's role?"
            RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, strText)
            If RtnCode = vbYes Then
                Dim strSQL, bIsExist
                Dim rstIsExist As New ADODB.Recordset
                strSQL = "select * from AIMROLES where REPTOROLEID='" & Trim$(txtRoleID) & "'"
                rstIsExist.Open strSQL, Cn
                If rstIsExist.eof Then
                    rsRoleId.Delete
                    rsRoleId.MovePrevious
                    If rsRoleId.BOF Then
                        rsRoleId.MoveFirst
                    End If
                Else
                    strMessage = getTranslationResource("MSGBOX08106")
                    If StrComp(strMessage, "MSGBOX08106") = 0 Then _
                        strMessage = "The current role is assigned as the 'Reports To' role for other roles. Please reassign dependent roles before deleting the selected role."
                    MsgBox strMessage, vbOKOnly + vbExclamation, strText
                    Exit Sub
                End If
            End If
        End If
    
    Case "ID_GetFirst"
        rsRoleId.MoveFirst
    
    Case "ID_GetPrev"
        rsRoleId.MovePrevious
        If rsRoleId.BOF Then rsRoleId.MoveFirst
        
    Case "ID_GetNext"
        rsRoleId.MoveNext
        If rsRoleId.eof Then rsRoleId.MoveLast
        
    Case "ID_GetLast"
        rsRoleId.MoveLast
    
    Case "ID_Save"
        rtnSave = f_AIMRole_Save
        If rtnSave = FAIL Then
            txtRoleID.SetFocus
            Exit Sub
        Else
            'Refresh the dropdowns
            PopulateRoles
        End If
        
    Case "ID_SetBookMark"
        AIMUsersBookMark = rsRoleId.Bookmark
        Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        
    Case "ID_GoToBookMark"
        rsRoleId.Bookmark = AIMUsersBookMark
    
    Case "ID_LookUp"
        'Save the current Bookmark
        AIMUsersBookMark = rsRoleId.Bookmark
        
    Case "ID_Close"
        If f_IsRecordsetOpenAndPopulated(rsRoleId) Then
            rsRoleId.MoveFirst
            rsRoleId.CancelUpdate
        End If
        Unload Me
        Exit Sub

    End Select

    RefreshForm        'Refresh the form
    If Me.txtRoleID.Enabled = True Then
        Me.txtRoleID.SetFocus
    End If

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
End Sub

Function f_AIMRoles_InitializeNew()
    On Error GoTo ErrorHandler

    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim rsSysCtrl As ADODB.Recordset
        
    If Not f_IsRecordsetValidAndOpen(rsRoleId) Then Exit Function
    
    rsRoleId.AddNew
    
    rsRoleId!RoleID = ""
    rsRoleId!RoleDescription = ""
    rsRoleId!RoleLevel = 1
    rsRoleId!SCArray = String(100, "N")

    rsRoleId!RepToRoleID = "SA"
    rsRoleId!AltSourceExcpts = "N"
    
    Me.tbNavigation.Tools("Id_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
    If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    
    Me.tbNavigation.Refresh
 
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(f_AIMUsers_InitializeNew)"
End Function

Function f_AIMRoles_InitializeDuplicate()
On Error GoTo ErrorHandler

    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim curRoleDescription As String
    Dim curRoleLevel As Long
    Dim curScArray As String
    Dim curRepToRoleID As String
    Dim curAltSourceExcpts As String
    
    If Not f_IsRecordsetValidAndOpen(rsRoleId) Then Exit Function
    
    curRoleDescription = rsRoleId!RoleDescription
    curRoleLevel = rsRoleId!RoleLevel
    curScArray = rsRoleId!SCArray
    curRepToRoleID = rsRoleId!RepToRoleID
    curAltSourceExcpts = rsRoleId!AltSourceExcpts
    
    rsRoleId.AddNew
    
    rsRoleId!RoleID = ""
    rsRoleId!RoleDescription = curRoleDescription
    rsRoleId!RoleLevel = CInt(curRoleLevel)
    rsRoleId!SCArray = curScArray
    rsRoleId!RepToRoleID = curRepToRoleID
    rsRoleId!AltSourceExcpts = curAltSourceExcpts
    
    
    Me.tbNavigation.Tools("Id_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
    If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    
    Me.tbNavigation.Refresh
 
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(f_AIMUsers_InitializeNew)"
End Function

Function RefreshForm()
On Error GoTo ErrorHandler

    'Enable/Disable Key fields
    If rsRoleId.EditMode <> adEditAdd Then
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            ToggleFieldAccess False
        Else
            ToggleFieldAccess True
        End If

        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    End If
    
    If Not f_IsRecordsetOpenAndPopulated(rsRoleId) Then Exit Function
    
    Me.txtRoleID = rsRoleId("RoleId")
    Me.txtRoleDescription = rsRoleId("RoleDescription")
    Me.dcRoleID.Text = rsRoleId("RoleLevel")
    Me.dcRepRoles.Text = rsRoleId("RepToRoleID")
    
    SCArray = rsRoleId!SCArray
    If Len(SCArray) < 100 Then
        SCArray = SCArray + String(100 - Len(SCArray), "N")
    End If
    
    Me.dgAIMFunctions.ReBind

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(RefreshForm)"
    Resume Next
End Function

Private Function ToggleFieldAccess(p_Enabled As Boolean)
On Error GoTo ErrorHandler

    Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_Delete").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_AddNew").Enabled = p_Enabled
    
    cmdGrantAll.Enabled = p_Enabled
    cmdRevokeAll.Enabled = p_Enabled
    
    Me.dcRoleID.Enabled = p_Enabled
    Me.dgAIMFunctions.AllowUpdate = p_Enabled
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(ToggleFieldAccess)", Err.Description
End Function

Private Function f_AIMRole_Save() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    Dim strText As String
        
    Cn.Errors.Clear
    
    'Validate required info
    RtnCode = UserInputValidation
    If RtnCode = SUCCEED Then
        If txtRoleID = "SA" Then
            f_AIMRole_Save = FAIL
            strMessage = getTranslationResource("MSGBOX08107")
            If StrComp(strMessage, "MSGBOX08107") = 0 Then _
                strMessage = "The 'SA' role is not available for deletion or modification."
            MsgBox strMessage, vbOKOnly + vbExclamation, strText
            Exit Function
        End If
        
        rsRoleId.Update
        
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, Cn.Errors.Count - 1
        Else
            f_AIMRole_Save = SUCCEED
            strMessage = getTranslationResource("STATMSG08101")
            If StrComp(strMessage, "STATMSG08101") = 0 Then strMessage = "User Role successfully updated."
            Write_Message strMessage
        End If
    Else
        f_AIMRole_Save = RtnCode
    End If
        
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(f_AIMRole_Save)", Err.Description
End Function

Function f_AIMRoles_Update()
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    If Not f_IsRecordsetOpenAndPopulated(rsRoleId) Then Exit Function
    
    If UCase$(Trim$(Me.txtRoleID.Text)) = "SA" Then
        strMessage = getTranslationResource("MSGBOX08107")
        If StrComp(strMessage, "MSGBOX08107") = 0 Then _
            strMessage = "The 'SA' role is not available for deletion or modification."
        MsgBox strMessage, vbOKOnly + vbExclamation, Me.Caption
        Exit Function
    End If
    
    rsRoleId!RoleID = Me.txtRoleID
    rsRoleId!RoleDescription = Me.txtRoleDescription
    rsRoleId!RoleLevel = IIf(Trim$(Me.dcRoleID.Text) = "", 1, Me.dcRoleID.Text)
    rsRoleId!RepToRoleID = Me.dcRepRoles.Text
    rsRoleId!SCArray = SCArray

Exit Function
ErrorHandler:
    If Err.Number = 2147217885 Then
        Exit Function
    End If
    Err.Raise Err.Number, Err.source, Err.Description & "(f_AIMRoles_Update)"
End Function

Private Function UserInputValidation() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX08108")
    If StrComp(strMessage, "MSGBOX08108") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim$(txtRoleID.Text) = "" _
    Or StrComp(txtRoleID.Text, getTranslationResource("New")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    Label(0).Caption  'Role ID
        txtRoleID.SetFocus
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
End Function

Private Function PopulateRoles() As Long
On Error GoTo ErrorHandler

    Dim AIM_AIMRoles_List_Sp As ADODB.Command
    Dim rsRoles As ADODB.Recordset
    
    'Build the Roles List
    Set AIM_AIMRoles_List_Sp = New ADODB.Command
    With AIM_AIMRoles_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMRoles_List_Sp"
        .Parameters.Refresh
    End With

    'Initialize the language Record Set
    Set rsRoles = New ADODB.Recordset
    With rsRoles
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
        
    rsRoles.Open AIM_AIMRoles_List_Sp
    Me.dcRepRoles.Columns(2).Visible = True
    Me.dcRepRoles.Columns(2).Alignment = ssCaptionAlignmentLeft
    
    'Bind the Data Combo Drop Downs
    If f_IsRecordsetOpenAndPopulated(rsRoles) Then
        rsRoles.MoveFirst
        Me.dcRepRoles.RemoveAll
        Me.dcRepRoles.Reset
        
        Do Until rsRoles.eof
            Me.dcRepRoles.AddItem rsRoles!RoleID & _
                vbTab & rsRoles!RoleDescription & _
                vbTab & CStr(rsRoles!RoleLevel)
            rsRoles.MoveNext
        Loop
        rsRoles.MoveFirst
        Me.dcRepRoles.Text = rsRoles!RoleDescription
    End If
    rsRoles.Close
    
    If f_IsRecordsetValidAndOpen(rsRoles) Then rsRoles.Close
    Set rsRoles = Nothing
    If Not (AIM_AIMRoles_List_Sp Is Nothing) Then Set AIM_AIMRoles_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMRoles_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetOpenAndPopulated(rsRoles) Then rsRoles.Close
    Set rsRoles = Nothing
    If Not (AIM_AIMRoles_List_Sp Is Nothing) Then Set AIM_AIMRoles_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMRoles_List_Sp = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(PopulateRoles)"
    Resume Next
End Function

Private Function PopulateRoleLevels() As Long
On Error GoTo ErrorHandler

    Dim IRoleLevel As Integer
    Dim rsCodeLookup As ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        .Parameters.Refresh
    End With
    
    AIM_CodeLookup_Get_Sp.Parameters("@CodeType").Value = g_CODETYPE_ROLELEVEL
    AIM_CodeLookup_Get_Sp.Parameters("@LangID").Value = gLangID
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        
        .Open AIM_CodeLookup_Get_Sp
    End With
    
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Do Until rsCodeLookup.eof
            Me.dcRoleID.AddItem rsCodeLookup!CodeID & vbTab & rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrDescription = Err.Description
    ErrSource = Err.source
    
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    
    Err.Raise ErrNumber, ErrSource, ErrDescription & "(PopulateRoleLevels)"
End Function

Private Function DeleteBlankRoles()
On Error GoTo ErrorHandler

    Dim DelRoles As ADODB.Command
    Dim strSQL As String
    
    strSQL = "DELETE FROM AIMROLES WHERE ROLEID = ''"
    Set DelRoles = New ADODB.Command
    With DelRoles
        Set .ActiveConnection = Cn
        .CommandType = adCmdText
        .CommandText = strSQL
        .Prepared = True
        .Execute
    End With
    
    If Not (DelRoles Is Nothing) Then Set DelRoles.ActiveConnection = Nothing
    Set DelRoles = Nothing

Exit Function
ErrorHandler:
    If Not (DelRoles Is Nothing) Then Set DelRoles.ActiveConnection = Nothing
    Set DelRoles = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(DeleteBlankRoles)"
End Function

Private Sub txtRoleID_Change()
On Error GoTo ErrorHandler
    
    If BROLEID = False Then
        If Me.txtRoleID = "SA" Then
            Me.txtRoleID.Enabled = False
            Me.tbNavigation.Tools("ID_Save").Enabled = False
            Me.tbNavigation.Tools("ID_Delete").Enabled = False
            Exit Sub
        End If
        Me.txtRoleID.Enabled = True
        Me.tbNavigation.Tools("ID_Save").Enabled = True
        Me.tbNavigation.Tools("ID_Delete").Enabled = True
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtRoleID_Change)"
     f_HandleErr , , , "AIM_RolesMaintenance::txtRoleID_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtRoleID_Click()
On Error GoTo ErrorHandler

    BROLEID = True

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtRoleID_Click)"
     f_HandleErr , , , "AIM_RolesMaintenance::txtRoleID_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtRoleID_KeyPress(KeyAscii As Integer)
On Error GoTo ErrorHandler
    
    BROLEID = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtRoleID_KeyPress)"
     f_HandleErr , , , "AIM_RolesMaintenance::txtRoleID_KeyPress", Now, gDRGeneralError, True, Err
End Sub

