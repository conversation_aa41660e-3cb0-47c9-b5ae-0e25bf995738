VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Begin VB.Form AIM_BuyerStatus 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Buyer Status"
   ClientHeight    =   8775
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   14670
   Icon            =   "AIM_BuyerStatus.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   8775
   ScaleWidth      =   14670
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atBuyerStatus 
      Left            =   240
      Top             =   8400
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Tools           =   "AIM_BuyerStatus.frx":030A
      ToolBars        =   "AIM_BuyerStatus.frx":1C9A
   End
   Begin C1Chart2D8.Chart2D ctBuyerStatus 
      Height          =   4995
      Left            =   0
      TabIndex        =   1
      Top             =   3375
      Width           =   14625
      _Version        =   524288
      _Revision       =   7
      _ExtentX        =   25797
      _ExtentY        =   8811
      _StockProps     =   0
      ControlProperties=   "AIM_BuyerStatus.frx":1D5F
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgBuyerStatus 
      Height          =   3375
      Left            =   0
      TabIndex        =   0
      Top             =   0
      Width           =   14655
      _Version        =   196617
      Cols            =   1
      HeadLines       =   3
      AllowUpdate     =   0   'False
      AllowColumnSwapping=   0
      SelectTypeCol   =   0
      SelectTypeRow   =   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   25850
      _ExtentY        =   5953
      _StockProps     =   79
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_BuyerStatus"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_BuyerStatus_Sp As ADODB.Command
Dim rsBuyerStatus As ADODB.Recordset

Private Function LoadChartData(Bookmark As Variant)
On Error GoTo ErrorHandler

    'Check for an empty recordset
    If Not (f_IsRecordsetOpenAndPopulated(rsBuyerStatus)) Then Exit Function
    If IsNull(rsBuyerStatus("Buyer ID").Value) Then Exit Function
    
    'Set the Book Mark
    rsBuyerStatus.Bookmark = Bookmark
    
    With Me.ctBuyerStatus
        .IsBatched = True   'Disable repainting for performance boost
    
        'Remove any old Legend Text
        .ChartGroups(1).SeriesLabels.RemoveAll
        'Create the Legend Text
        .ChartGroups(1).SeriesLabels.Add getTranslationResource("Total")
        .ChartGroups(1).SeriesLabels.Add getTranslationResource("Planned")
        .ChartGroups(1).SeriesLabels.Add getTranslationResource("Released")
        .ChartGroups(1).SeriesLabels.Add getTranslationResource("Completed")
        
        'Remove any old x-axis labels
        .ChartArea.Axes("X").ValueLabels.RemoveAll
        .ChartArea.Axes("Y").ValueLabels.RemoveAll
        'Set the x axes label
        .ChartArea.Axes("X").ValueLabels.Add 1, (getTranslationResource("All"))
        .ChartArea.Axes("X").ValueLabels.Add 2, (getTranslationResource("Lead Time Exceptions"))
        .ChartArea.Axes("X").ValueLabels.Add 3, (getTranslationResource("Line Reviews"))
        .ChartArea.Axes("X").ValueLabels.Add 4, (getTranslationResource("Priority Exceptions"))
        .ChartArea.Axes("Y").Title.Text = getTranslationResource("P.O. Lines")
        .ChartArea.Axes("Y").TitleFont.Bold = True
        
        'Update the header
        .Header.Text = rsBuyerStatus("Buyer ID").Value & vbCrLf & _
            (IIf(IsNull(rsBuyerStatus("Buyer Name").Value), "", rsBuyerStatus("Buyer Name").Value))
    
         'Set the data values
        .ChartGroups(1).Data(1, 1) = rsBuyerStatus("Total Lines").Value
        .ChartGroups(1).Data(2, 1) = rsBuyerStatus("Planned Lines").Value
        .ChartGroups(1).Data(3, 1) = rsBuyerStatus("Released Lines").Value
        .ChartGroups(1).Data(4, 1) = rsBuyerStatus("Complete Lines").Value
        
        .ChartGroups(1).Data(1, 2) = rsBuyerStatus("Total LT Excpts").Value
        .ChartGroups(1).Data(2, 2) = rsBuyerStatus("Planned LT Excpts").Value
        .ChartGroups(1).Data(3, 2) = rsBuyerStatus("Released LT Excpts").Value
        .ChartGroups(1).Data(4, 2) = rsBuyerStatus("Complete LT Excpts").Value
        
        .ChartGroups(1).Data(1, 3) = rsBuyerStatus("Total Line Reviews").Value
        .ChartGroups(1).Data(2, 3) = rsBuyerStatus("Planned Line Reviews").Value
        .ChartGroups(1).Data(3, 3) = rsBuyerStatus("Released Line Reviews").Value
        .ChartGroups(1).Data(4, 3) = rsBuyerStatus("Complete Line Reviews").Value
        
        .ChartGroups(1).Data(1, 4) = rsBuyerStatus("Total Priority Excepts").Value
        .ChartGroups(1).Data(2, 4) = rsBuyerStatus("Planned Priority Excepts").Value
        .ChartGroups(1).Data(3, 4) = rsBuyerStatus("Released Priority Excepts").Value
        .ChartGroups(1).Data(4, 4) = rsBuyerStatus("Complete Priority Excepts").Value
        
        .IsBatched = False
        .AllowUserChanges = False
    End With
    
Exit Function
ErrorHandler:
    Me.ctBuyerStatus.IsBatched = False  'Repaint control
    'Err.Raise Err.Number, Err.source, Err.Description & "(LoadChartData)"
     f_HandleErr , , , "AIMBuyerStatus::LoadChartData", Now, gDRGeneralError, True, Err
End Function

Private Sub atBuyerStatus_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim CurrentRec As Variant
    
    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
            
        Case "ID_Refresh"
            getBuyerStatus
            
    End Select
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        Resume Next
    Else
        'f_HandleErr Me.Caption & "(atBuyerStatus_ToolClick)"
         f_HandleErr , , , Me.Caption & "::atBuyerStatus_ToolClick", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub dgBuyerStatus_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dgBuyerStatus.Columns(0).Caption = getTranslationResource("Buyer ID")
    Me.dgBuyerStatus.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dgBuyerStatus.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(0).Width = 900

    Me.dgBuyerStatus.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgBuyerStatus.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(1).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(1).Caption)
    Me.dgBuyerStatus.Columns(1).Width = 1600
    Me.dgBuyerStatus.Columns(1).Visible = False

    Me.dgBuyerStatus.Columns(2).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(2).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(2).Caption)
    Me.dgBuyerStatus.Columns(2).Width = 850
    Me.dgBuyerStatus.Columns(2).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(3).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(3).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(3).Caption)
    Me.dgBuyerStatus.Columns(3).Width = 850
    Me.dgBuyerStatus.Columns(3).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(4).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(4).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(4).Caption)
    Me.dgBuyerStatus.Columns(4).Width = 850
    Me.dgBuyerStatus.Columns(4).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(5).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(5).Caption)
    Me.dgBuyerStatus.Columns(5).Width = 850
    Me.dgBuyerStatus.Columns(5).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(6).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(6).Caption)
    Me.dgBuyerStatus.Columns(6).Width = 850
    Me.dgBuyerStatus.Columns(6).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(7).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(7).Caption)
    Me.dgBuyerStatus.Columns(7).Width = 850
    Me.dgBuyerStatus.Columns(7).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(8).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(8).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(8).Caption)
    Me.dgBuyerStatus.Columns(8).Width = 850
    Me.dgBuyerStatus.Columns(8).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(9).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(9).Caption)
    Me.dgBuyerStatus.Columns(9).Width = 850
    Me.dgBuyerStatus.Columns(9).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(10).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(10).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(10).Caption)
    Me.dgBuyerStatus.Columns(10).Width = 850
    Me.dgBuyerStatus.Columns(10).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(11).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(11).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(11).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(11).Caption)
    Me.dgBuyerStatus.Columns(11).Width = 850
    Me.dgBuyerStatus.Columns(11).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(12).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(12).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(12).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(12).Caption)
    Me.dgBuyerStatus.Columns(12).Width = 850
    Me.dgBuyerStatus.Columns(12).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(13).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(13).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(13).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(13).Caption)
    Me.dgBuyerStatus.Columns(13).Width = 850
    Me.dgBuyerStatus.Columns(13).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(14).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(14).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(14).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(14).Caption)
    Me.dgBuyerStatus.Columns(14).Width = 850
    Me.dgBuyerStatus.Columns(14).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(15).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(15).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(15).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(15).Caption)
    Me.dgBuyerStatus.Columns(15).Width = 850
    Me.dgBuyerStatus.Columns(15).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(16).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(16).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(16).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(16).Caption)
    Me.dgBuyerStatus.Columns(16).Width = 850
    Me.dgBuyerStatus.Columns(16).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.Columns(17).Alignment = ssCaptionAlignmentRight
    Me.dgBuyerStatus.Columns(17).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgBuyerStatus.Columns(17).Caption = getTranslationResource(Me.dgBuyerStatus.Columns(17).Caption)
    Me.dgBuyerStatus.Columns(17).Width = 850
    Me.dgBuyerStatus.Columns(17).NumberFormat = "#,##0"
    
    Me.dgBuyerStatus.SplitterPos = 1
    
    'Restore the grid layout
    On Error Resume Next 'To prevent error when they run the first time
    Me.dgBuyerStatus.LoadLayout App.Path & "\" & "AIM_BuyerStatus" & Trim$(gUserID) & "_Layout.grd"
    'Check for non-existent file
    If Err.Number = 53 Then     'File Not Found
        Resume Next
    ElseIf Err.Number <> 0 Then
        GoTo ErrorHandler
    End If
    On Error GoTo ErrorHandler
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgBuyerStatus.Font
    SetProperFont Me.dgBuyerStatus.HeadFont
    SetProperFont Me.dgBuyerStatus.PageFooterFont
    SetProperFont Me.dgBuyerStatus.PageHeaderFont
    Me.dgBuyerStatus.Columns(0).Caption = getTranslationResource("Buyer ID")
    Me.dgBuyerStatus.Columns(1).Caption = getTranslationResource("Buyer Name")
    Me.dgBuyerStatus.Columns(2).Caption = getTranslationResource("Total Lines")
    Me.dgBuyerStatus.Columns(3).Caption = getTranslationResource("Complete Lines")
    Me.dgBuyerStatus.Columns(4).Caption = getTranslationResource("Planned Lines")
    Me.dgBuyerStatus.Columns(5).Caption = getTranslationResource("Released Lines")
    Me.dgBuyerStatus.Columns(6).Caption = getTranslationResource("Total LT Excpts")
    Me.dgBuyerStatus.Columns(7).Caption = getTranslationResource("Complete LT Excpts")
    Me.dgBuyerStatus.Columns(8).Caption = getTranslationResource("Planned LT Excpts")
    Me.dgBuyerStatus.Columns(9).Caption = getTranslationResource("Released LT Excpts")
    Me.dgBuyerStatus.Columns(10).Caption = getTranslationResource("Total Line Reviews")
    Me.dgBuyerStatus.Columns(11).Caption = getTranslationResource("Complete Line Reviews")
    Me.dgBuyerStatus.Columns(12).Caption = getTranslationResource("Planned Line Reviews")
    Me.dgBuyerStatus.Columns(13).Caption = getTranslationResource("Released Line Reviews")
    Me.dgBuyerStatus.Columns(14).Caption = getTranslationResource("Total Priority Excepts")
    Me.dgBuyerStatus.Columns(15).Caption = getTranslationResource("Complete Priority Excepts")
    Me.dgBuyerStatus.Columns(16).Caption = getTranslationResource("Planned Priority Excepts")
    Me.dgBuyerStatus.Columns(17).Caption = getTranslationResource("Released Priority Excepts")
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgBuyerStatus, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgBuyerStatus.Columns.Count - 1
'        dgBuyerStatus.Columns(IndexCounter).HasHeadBackColor = True
'        dgBuyerStatus.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgBuyerStatus_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "dgBuyerStatus_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgBuyerStatus_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgBuyerStatus_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgBuyerStatus_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgBuyerStatus_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgBuyerStatus_PrintBegin)"
     f_HandleErr , , , Me.Caption & "::dgBuyerStatus_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgBuyerStatus_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG01200")
    If StrComp(strMessage, "RPTMSG01200") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG01201")
    If StrComp(strMessage1, "RPTMSG01201") = 0 Then strMessage = "Buyer Status"
    strMessage2 = getTranslationResource("RPTMSG01202")
    If StrComp(strMessage2, "RPTMSG01202") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & strMessage1 & _
                            vbTab & strMessage2 & " <page number>"
    
    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgBuyerStatus_PrintInitialize)"
     f_HandleErr , , , Me.Caption & "::dgBuyerStatus_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgBuyerStatus_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    If LastRow - 1 <> Me.dgBuyerStatus.Row Then
        'Rebuild the chart's data
        LoadChartData Me.dgBuyerStatus.Bookmark
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgBuyerStatus_RowColChange)"
     f_HandleErr , , , Me.Caption & "::dgBuyerStatus_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
    f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Display status message
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01201")
    If StrComp(strMessage, "STATMSG01201") = 0 Then strMessage = "Initializing Buyer Status..."
    Write_Message strMessage
    
    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Set default properties for the chart
    InitializeChart
    
    'Fetch data for display
    getBuyerStatus
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsBuyerStatus) Then rsBuyerStatus.Close
    Set rsBuyerStatus = Nothing
    
    If Not (AIM_BuyerStatus_Sp Is Nothing) Then Set AIM_BuyerStatus_Sp.ActiveConnection = Nothing
    Set AIM_BuyerStatus_Sp = Nothing

    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
     'Save current grid layout.
     Me.dgBuyerStatus.SaveLayout App.Path & "\" & "AIM_BuyerStatus" & Trim$(gUserID) & "_Layout.grd", ssSaveLayoutAll
   
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , Me.Caption & "::Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim s1 As String
    Dim s2 As String
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If (f_IsRecordsetOpenAndPopulated(rsBuyerStatus)) Then
                Clipboard.Clear
                
                For IndexCounter = 0 To rsBuyerStatus.Fields.Count - 1
                    s1 = s1 & rsBuyerStatus.Fields(IndexCounter).Name & vbTab
                Next IndexCounter
                
                s1 = s1 & vbCrLf
                    
                rsBuyerStatus.MoveFirst
                s2 = rsBuyerStatus.GetString(adClipString)
                
                Clipboard.SetText s1 & s2, vbCFText
            End If
        Case 1          'Print All Rows
            Me.dgBuyerStatus.PrintData ssPrintAllRows, False, True
    
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , Me.Caption & "::mnuEditOpt::Click", Now, gDRGeneralError, True, Err
End Sub

Private Function InitializeChart()
On Error GoTo ErrorHandler

    Dim Counter As Long
    
    With Me.ctBuyerStatus
        .IsBatched = True 'Disable the control from repainting until the process has finished
        .AllowUserChanges = False
        
        'Set up Header
        .Header.Text = getTranslationResource("SSA DR Buyer Status")
        .Header.Border.Type = oc2dBorder3DIn
        .Border.Type = oc2dBorderPlain
        .Border.Width = 2
        .ChartArea.IsHorizontal = False
        .Header.Font.Bold = True

        .ChartArea.Axes("X").Title.Text = ""
        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
        .ChartArea.Axes("X").MajorGrid.Spacing = 0#
        .ChartArea.Axes("X").Font.Bold = False

        .ChartArea.Axes("Y").Title.Text = ""
        .ChartArea.Axes("Y").TitleRotation = oc2dRotate90Degrees
        .ChartArea.Axes("Y").MajorGrid.Spacing = 1000
        .ChartArea.Axes("Y").Min = 0
        .ChartArea.Axes("Y").Font.Bold = False

        'Set up Legends
        .Legend.Border.Type = oc2dBorderNone
        .Legend.Text = ""
        .Legend.IsShowing = True
        .Legend.Anchor = oc2dAnchorNorthEast
        
        'Set up Data Series
        .ChartGroups(1).ChartType = oc2dTypeBar
        .ChartArea.Bar.ClusterWidth = 90
        .ChartArea.Bar.ClusterOverlap = -20
        
        .ChartGroups(1).Data.NumSeries = 4
        
        'Update the number of data points
        .ChartGroups(1).Data.NumPoints(1) = 4
        .ChartGroups(1).Data.NumPoints(2) = 4
        .ChartGroups(1).Data.NumPoints(3) = 4
        .ChartGroups(1).Data.NumPoints(4) = 4
     
        'Set the Series Properties
        .ChartGroups(1).Styles(1).Fill.Interior.BackgroundColor = vbWhite
        .ChartGroups(1).Styles(1).Fill.Interior.BackgroundColor = vbRed
        .ChartGroups(1).Styles(2).Fill.Interior.BackgroundColor = vbYellow
        .ChartGroups(1).Styles(3).Fill.Interior.BackgroundColor = vbGreen
        
        'Remove any old x-axis labels
        .ChartArea.Axes("X").ValueLabels.RemoveAll
        .ChartArea.Axes("Y").ValueLabels.RemoveAll
        .Border.Type = oc2dBorderPlain
        .Border.Width = 2
        
        'Remove any old Legend Text
        .ChartGroups(1).SeriesLabels.RemoveAll
        
        'Set the Series Properties
        .ChartGroups(1).Styles(1).Fill.Interior.BackgroundColor = vbWhite
        .ChartGroups(1).Styles(2).Fill.Interior.BackgroundColor = vbRed
        .ChartGroups(1).Styles(3).Fill.Interior.BackgroundColor = vbYellow
        .ChartGroups(1).Styles(4).Fill.Interior.BackgroundColor = vbGreen
        
        'Set the data value
        For Counter = 1 To .ChartGroups(1).Data.NumPoints(1)
            .ChartGroups(1).Data.Y(1, Counter) = 0
        Next
        
        .IsBatched = False  'Repaint control
    End With
        
Exit Function
ErrorHandler:
    Me.ctBuyerStatus.IsBatched = False  'Repaint control
    'Err.Raise Err.Number, Err.source & "(InitializeChart)", Err.Description
     f_HandleErr , , , Me.Caption & "::InitializeChart", Now, gDRGeneralError, True, Err
End Function

Private Sub getBuyerStatus()
On Error GoTo ErrorHandler

    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    Dim CurrentRec As Variant
    Dim strMessage As String
    
    strMessage = getTranslationResource("STATMSG01200")
    If StrComp(strMessage, "STATMSG01200") = 0 Then strMessage = "Refreshing Buyer Status..."
    Write_Message strMessage
    
    Me.MousePointer = vbHourglass
    Me.dgBuyerStatus.Redraw = False
    
    'Initialize the Stored Procedures and Recordsets
    If AIM_BuyerStatus_Sp Is Nothing Then
        Set AIM_BuyerStatus_Sp = New ADODB.Command
        With AIM_BuyerStatus_Sp
            Set .ActiveConnection = Cn
            .CommandType = adCmdStoredProc
            .CommandText = "AIM_BuyerStatus_Sp"
            .Parameters.Refresh
        End With
    End If
    
    If f_IsRecordsetValidAndOpen(rsBuyerStatus) Then
        CurrentRec = rsBuyerStatus.Bookmark
        rsBuyerStatus.Requery
    Else
        CurrentRec = Null
        Set rsBuyerStatus = Nothing
        Set rsBuyerStatus = New ADODB.Recordset
        With rsBuyerStatus
            .CursorLocation = adUseClient
            .CursorType = adOpenStatic
            .LockType = adLockReadOnly
        End With
        rsBuyerStatus.Open AIM_BuyerStatus_Sp
    End If
    
    If f_IsRecordsetOpenAndPopulated(rsBuyerStatus) Then
        If IsNull(CurrentRec) Then
            'Bind the Record Set to the Data Grid
            rsBuyerStatus.MoveFirst
            Set Me.dgBuyerStatus.DataSource = rsBuyerStatus
        Else
            rsBuyerStatus.Bookmark = CurrentRec
        End If
    Else
        CurrentRec = Null
    End If
    
    'Load Chart Data
    LoadChartData rsBuyerStatus.Bookmark

    Me.MousePointer = vbNormal
    Me.dgBuyerStatus.Redraw = True
    Write_Message ""

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Me.MousePointer = vbNormal
    Me.dgBuyerStatus.Redraw = True
    
    'Err.Raise ErrNumber, ErrSource & "(getBuyerStatus)", ErrDescription
    f_HandleErr , , , Me.Caption & "::getBuyerStatus", Now, gDRGeneralError, True, Err
End Sub

