VERSION 5.00
Begin VB.Form AIM_DXAO 
   Caption         =   "SSA DR Send Allocated Orders to Host"
   ClientHeight    =   2370
   ClientLeft      =   75
   ClientTop       =   345
   ClientWidth     =   6210
   LinkTopic       =   "Form1"
   ScaleHeight     =   2370
   ScaleWidth      =   6210
   StartUpPosition =   3  'Windows Default
   Begin VB.Frame frmTransmitPOs 
      Height          =   1095
      Left            =   120
      TabIndex        =   2
      Top             =   480
      Width           =   5895
      Begin VB.OptionButton optAllReleased 
         Caption         =   "Transmit Released Orders for the Reviewer"
         Height          =   255
         Left            =   240
         TabIndex        =   4
         Top             =   240
         Value           =   -1  'True
         Width           =   5415
      End
      Begin VB.OptionButton optCurrent 
         Caption         =   "Transmit All Orders for the Reviewer"
         Height          =   255
         Left            =   240
         TabIndex        =   3
         Top             =   720
         Width           =   5415
      End
   End
   Begin VB.CommandButton cmdTransmit 
      Caption         =   "&Transmit Order(s)"
      Default         =   -1  'True
      Height          =   345
      Left            =   4275
      Style           =   1  'Graphical
      TabIndex        =   1
      Top             =   1920
      Width           =   1812
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   2355
      Style           =   1  'Graphical
      TabIndex        =   0
      Top             =   1920
      Width           =   1812
   End
End
Attribute VB_Name = "AIM_DXAO"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public ById As String

Public CancelFlag As Boolean

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    CancelFlag = True
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , Me.Caption & "::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdTransmit_Click()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07803")
    If StrComp(strMessage, "STATMSG07803") = 0 Then strMessage = "Transmitting Allocated Orders to host..."
    Write_Message strMessage

    If Me.optAllReleased Then
       
        RtnCode = DxAO_Outbound(False, ById)
    
    Else
        RtnCode = DxAO_Outbound(True, ById)
    
    End If

    'Wind Up
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    CancelFlag = False
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdTransmit_Click)"
     f_HandleErr , , , Me.Caption & "::cmdTransmit_Click", Now, gDRGeneralError, True, Err

End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
    
End Sub


