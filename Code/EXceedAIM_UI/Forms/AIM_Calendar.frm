VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E8671A8B-E5DD-11CD-836C-0000C0C14E92}#1.0#0"; "SSCALA32.OCX"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_Calendar 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Calendar Maintenance"
   ClientHeight    =   7020
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11025
   Icon            =   "AIM_Calendar.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   7020
   ScaleWidth      =   11025
   ShowInTaskbar   =   0   'False
   Begin VB.Frame frAIMYears 
      Height          =   5865
      Left            =   6742
      TabIndex        =   14
      Top             =   0
      Width           =   4200
      Begin VB.Frame frNonWorkingDays 
         Caption         =   "Non-working Days"
         Height          =   2175
         Left            =   120
         TabIndex        =   18
         Top             =   3550
         Width           =   3975
         Begin VB.CommandButton cmdSave 
            Caption         =   "&Save"
            Height          =   345
            Left            =   1680
            Style           =   1  'Graphical
            TabIndex        =   10
            Top             =   1680
            Width           =   2175
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Sunday"
            Height          =   340
            Index           =   1
            Left            =   2040
            TabIndex        =   9
            Top             =   960
            Width           =   1815
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Saturday"
            Height          =   340
            Index           =   7
            Left            =   2040
            TabIndex        =   8
            Top             =   600
            Width           =   1815
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Friday"
            Height          =   340
            Index           =   6
            Left            =   2040
            TabIndex        =   7
            Top             =   240
            Width           =   1815
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Thursday"
            Height          =   340
            Index           =   5
            Left            =   120
            TabIndex        =   6
            Top             =   1320
            Width           =   1815
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Wednesday"
            Height          =   340
            Index           =   4
            Left            =   120
            TabIndex        =   5
            Top             =   960
            Width           =   1815
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Tuesday"
            Height          =   340
            Index           =   3
            Left            =   120
            TabIndex        =   4
            Top             =   600
            Width           =   1815
         End
         Begin VB.CheckBox ckDay 
            Caption         =   "Monday"
            Height          =   340
            Index           =   2
            Left            =   120
            TabIndex        =   3
            Top             =   240
            Width           =   1815
         End
      End
      Begin TDBDate6Ctl.TDBDate txtFYStartDate 
         BeginProperty DataFormat 
            Type            =   0
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1041
            SubFormatType   =   0
         EndProperty
         Height          =   345
         Left            =   2595
         TabIndex        =   1
         Top             =   690
         Width           =   1500
         _Version        =   65536
         _ExtentX        =   2646
         _ExtentY        =   609
         Calendar        =   "AIM_Calendar.frx":030A
         Caption         =   "AIM_Calendar.frx":0428
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Calendar.frx":04A2
         Keys            =   "AIM_Calendar.frx":04C0
         Spin            =   "AIM_Calendar.frx":051E
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/1999"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   36443
         CenturyMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtFirstFiscalYear 
         Height          =   345
         Left            =   2595
         TabIndex        =   0
         Top             =   300
         Width           =   1500
         _Version        =   65536
         _ExtentX        =   2646
         _ExtentY        =   609
         Calculator      =   "AIM_Calendar.frx":0546
         Caption         =   "AIM_Calendar.frx":0566
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Calendar.frx":05D2
         Keys            =   "AIM_Calendar.frx":05F0
         Spin            =   "AIM_Calendar.frx":063A
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   2099
         MinValue        =   1990
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtNbrFiscalYears 
         Height          =   345
         Left            =   2595
         TabIndex        =   2
         Top             =   1080
         Width           =   1500
         _Version        =   65536
         _ExtentX        =   2646
         _ExtentY        =   609
         Calculator      =   "AIM_Calendar.frx":0662
         Caption         =   "AIM_Calendar.frx":0682
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Calendar.frx":06EE
         Keys            =   "AIM_Calendar.frx":070C
         Spin            =   "AIM_Calendar.frx":0756
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#0;-#0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   10
         MinValue        =   2
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgFiscalYears 
         Height          =   1770
         Left            =   180
         TabIndex        =   13
         Top             =   1650
         Width           =   3885
         ScrollBars      =   2
         _Version        =   196617
         DataMode        =   1
         Cols            =   3
         RecordSelectors =   0   'False
         AllowUpdate     =   0   'False
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         CaptionAlignment=   0
         SplitterPos     =   1
         Columns(0).Width=   3200
         _ExtentX        =   6853
         _ExtentY        =   3122
         _StockProps     =   79
         Caption         =   "Fiscal Years"
      End
      Begin VB.Label Label3 
         Caption         =   "Number Fiscal Years"
         Height          =   270
         Left            =   180
         TabIndex        =   17
         Top             =   1110
         Width           =   2355
      End
      Begin VB.Label Label2 
         Caption         =   "Fiscal Year Start Date"
         Height          =   270
         Left            =   180
         TabIndex        =   16
         Top             =   720
         Width           =   2355
      End
      Begin VB.Label Label1 
         Caption         =   "First Fiscal Year"
         Height          =   270
         Left            =   180
         TabIndex        =   15
         Top             =   315
         Width           =   2355
      End
   End
   Begin VB.CommandButton cmdBuildFYDays 
      Caption         =   "&Build/Update Calendar Detail"
      Height          =   345
      Left            =   7680
      Style           =   1  'Graphical
      TabIndex        =   11
      Top             =   6000
      Width           =   3240
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   10560
      Top             =   6360
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Style           =   0
      Tools           =   "AIM_Calendar.frx":077E
      ToolBars        =   "AIM_Calendar.frx":2121
   End
   Begin SSCalendarWidgets_A.SSMonth mnAIMDays 
      Height          =   5865
      Left            =   82
      TabIndex        =   12
      Top             =   0
      Width           =   6510
      _Version        =   65543
      _ExtentX        =   11483
      _ExtentY        =   10345
      _StockProps     =   76
      MinDate         =   "1990/1/1"
      MaxDate         =   "2025/12/31"
      BackColorSelected=   16777215
      ForeColorSelected=   0
      DayCaptionAlignment=   8
      DayNumberAlignment=   3
      ShowTodaysDate  =   0   'False
      AutoSelect      =   -1  'True
      ShowCentury     =   -1  'True
   End
End
Attribute VB_Name = "AIM_Calendar"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_AIMDays_Load_Sp As ADODB.Command
Dim AIM_AIMYears_Load_Sp As ADODB.Command

Dim rsAIMYears As ADODB.Recordset
Dim rsAIMDays As ADODB.Recordset

Private Function GetFiscalPeriod( _
    p_TargetDate As Date _
) As Integer
On Error GoTo ErrorHandler

    Dim AIM_AIMDays_GetFiscalPeriod_Sp As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    
    Set AIM_AIMDays_GetFiscalPeriod_Sp = New ADODB.Command
    With AIM_AIMDays_GetFiscalPeriod_Sp
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMDays_GetFiscalPeriod_Sp"
        Set .ActiveConnection = Cn
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
        .Parameters.Append CmdParams
        
        '@TargetDate datetime
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@TargetDate", adDate, adParamInput)
        CmdParams.Value = p_TargetDate
        .Parameters.Append CmdParams
        
        '@FiscalPeriod int OUTPUT
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@FiscalPeriod", adInteger, adParamOutput)
        CmdParams.Value = 0
        .Parameters.Append CmdParams
            
        .Execute
    End With
        
    If AIM_AIMDays_GetFiscalPeriod_Sp(0).Value > 0 Then
        GetFiscalPeriod = AIM_AIMDays_GetFiscalPeriod_Sp("@FiscalPeriod").Value
    Else
        GetFiscalPeriod = FAIL
    End If

    If Not (AIM_AIMDays_GetFiscalPeriod_Sp Is Nothing) Then Set AIM_AIMDays_GetFiscalPeriod_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_GetFiscalPeriod_Sp = Nothing
    
Exit Function
ErrorHandler:
    If Not (AIM_AIMDays_GetFiscalPeriod_Sp Is Nothing) Then Set AIM_AIMDays_GetFiscalPeriod_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_GetFiscalPeriod_Sp = Nothing
    'Err.Raise Err.Number, Err.source & "(GetFiscalPeriod)", Err.Description
    f_HandleErr , , , "AIMCalendar::GetFiscalPeriod", Now, gDRGeneralError, True, Err
End Function

'''Private Function GetPd(rsYears As ADODB.Recordset, TargetDate As Date) As Integer
'''On Error GoTo ErrorHandler
''''REPLACED WITH GetFiscalPeriod above.
'''    Dim FYStartDate As Date
'''    Dim Pd As Integer
'''
'''    'Find the date
'''    If f_IsRecordsetOpenAndPopulated(rsYears) Then
'''        rsYears.MoveFirst
'''
'''        rsYears.Find "FYEndDate >= " & Format(TargetDate, gDateFormat)
'''        If rsYears.eof Then
'''            GetPd = FAIL
'''            Exit Function
'''
'''        Else
'''            FYStartDate = rsYears("FYStartDate").Value
'''
'''            GetPd = (DateDiff("d", FYStartDate, TargetDate) \ 7) + 1
'''            GetPd = IIf(GetPd > 52, 52, GetPd)
'''        End If
'''    Else
'''        'Add ErrorHandler
'''    End If
'''
'''Exit Function
'''ErrorHandler:
'''    Err.Raise Err.Number, Err.source & "(GetPd)", Err.Description
'''End Function

Private Function RefreshCalendar()
On Error GoTo ErrorHandler

    Dim FYDate As Date
    Dim FYPeriod As Integer
    Dim FiscalYear As Integer
    Dim FYStartDate As Date
    Dim FYEndDate As Date
    
    If f_IsRecordsetOpenAndPopulated(rsAIMDays) Then
        rsAIMDays.MoveFirst
        Do Until rsAIMDays.eof
        
            FYDate = rsAIMDays!FYDate
            FYPeriod = rsAIMDays!FYPeriod_Weeks
            If rsAIMDays!DayStatus = 0 Then
                Me.mnAIMDays.Day(FYDate).StyleSet = "NonWorkingDay"
                Me.mnAIMDays.Day(FYDate).Caption = Format(FYPeriod, "00") + " - " + getTranslationResource("No")
            Else
                Me.mnAIMDays.Day(FYDate).StyleSet = "WorkingDay"
                Me.mnAIMDays.Day(FYDate).Caption = Format(FYPeriod, "00") + " - " + getTranslationResource("Yes")
            End If
            rsAIMDays.MoveNext
        Loop
    Else
        'Add ErrorHandler
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(RefreshCalendar)", Err.Description
     f_HandleErr , , , "AIMCalendar::RefreshCalendar", Now, gDRGeneralError, True, Err
End Function

Private Function SetCalendarRange()
On Error GoTo ErrorHandler

    Dim NbrYears As Integer
    
    On Error GoTo ErrorHandler
    
    If f_IsRecordsetOpenAndPopulated(rsAIMYears) Then
        'Set minimum date
        rsAIMYears.MoveFirst
        Me.mnAIMDays.MinDate = rsAIMYears("FYStartDate").Value
        Me.txtFirstFiscalYear.Value = rsAIMYears("FiscalYear").Value
        
        SetTDBDate Me.txtFYStartDate, rsAIMYears!FYStartDate
        
        Do Until rsAIMYears.eof
            'Increment number of years
            NbrYears = NbrYears + 1
            
            rsAIMYears.MoveNext
        Loop
        
        'Set maximum date
        rsAIMYears.MoveLast
        Me.mnAIMDays.MaxDate = rsAIMYears("FYEndDate").Value
        Me.txtNbrFiscalYears.Value = NbrYears
    Else
        'Add ErrorHandler
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetCalendarRange)"
     f_HandleErr , , , "AIMCalendar::SetCalendarRange", Now, gDRGeneralError, True, Err
End Function

Private Sub cmdBuildFYDays_Click()
On Error GoTo ErrorHandler

    Dim FiscalYear As Integer
    Dim FYStartDate As Date
    Dim NbrYears As Integer
    Dim RtnCode As Integer
    Dim strText As String
    Dim strMessage As String
    Dim AIM_Calendar_Init_Sp As ADODB.Command
    
    Cn.Errors.Clear
    
    'User confirmation
    strMessage = getTranslationResource("MSGBOX01300")
    strText = getTranslationResource(Me.Caption)
    If StrComp(strMessage, "MSGBOX01300") = 0 Then strMessage = "Confirm Build/Update of Calendar."
    RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
    If RtnCode <> vbYes Then
        Exit Sub
    End If
    
    Me.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01300")
    If StrComp(strMessage, "STATMSG01300") = 0 Then strMessage = "Initializing Calendar..."
    Write_Message strMessage
    
    FiscalYear = Me.txtFirstFiscalYear
    FYStartDate = Me.txtFYStartDate.Text
    NbrYears = Me.txtNbrFiscalYears.Value
    
    Set AIM_Calendar_Init_Sp = New ADODB.Command
    With AIM_Calendar_Init_Sp
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Calendar_Init_Sp"
        Set .ActiveConnection = Cn
        
        .Parameters("@FirstFiscalYear").Value = FiscalYear
        .Parameters("@FYStartDate").Value = FYStartDate
        .Parameters("@NbrFiscalYears").Value = NbrYears
        
        .Execute
    End With
    
    If Cn.Errors.Count = 0 Then
        RtnCode = AIMYears_Load(AIM_AIMYears_Load_Sp, rsAIMYears, 0)
        Me.dgFiscalYears.ReBind
        If f_IsRecordsetOpenAndPopulated(rsAIMYears) Then dgFiscalYears.Rows = rsAIMYears.RecordCount
        SetCalendarRange
        RtnCode = AIMDays_Load(AIM_AIMDays_Load_Sp, rsAIMDays)
    End If

    RefreshCalendar
    
    If Not (AIM_Calendar_Init_Sp Is Nothing) Then Set AIM_Calendar_Init_Sp.ActiveConnection = Nothing
    Set AIM_Calendar_Init_Sp = Nothing

    Write_Message ""
    Me.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    If Not (AIM_Calendar_Init_Sp Is Nothing) Then Set AIM_Calendar_Init_Sp.ActiveConnection = Nothing
    Set AIM_Calendar_Init_Sp = Nothing
    'f_HandleErr Me.Caption & "(cmdBuildFYDays_Click)"
    f_HandleErr , , , Me.Caption & "cmdBuildFYDays_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdSave_Click()
On Error GoTo ErrorHandler
    
    Dim AIM_WorkingDays_Update_Sp As ADODB.Command
    Dim strMessage As String
    Dim strText As String
    
    If ckDay(1).Value = vbChecked _
    And ckDay(2).Value = vbChecked _
    And ckDay(3).Value = vbChecked _
    And ckDay(4).Value = vbChecked _
    And ckDay(5).Value = vbChecked _
    And ckDay(6).Value = vbChecked _
    And ckDay(7).Value = vbChecked _
    Then
        strMessage = getTranslationResource("MSGBOX01304")
        If StrComp(strMessage, "MSGBOX01304") = 0 Then strMessage = "There must be at least one working day."
        strText = getTranslationResource(Me.Caption)
        MsgBox strMessage, vbOKOnly + vbExclamation, strText
        Exit Sub
    End If
    
    Set AIM_WorkingDays_Update_Sp = New ADODB.Command
    With AIM_WorkingDays_Update_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_WorkingDays_Update_Sp"
    End With
    
    With AIM_WorkingDays_Update_Sp
        'Create parameters, starting with the default return at 0
        .Parameters.Append .CreateParameter( _
            "RETURN", adInteger, adParamReturnValue)
        '@Day1 through @Day7 As bit = '',
        .Parameters.Append .CreateParameter( _
            "@Day1", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter( _
            "@Day2", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter( _
            "@Day3", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter( _
            "@Day4", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter( _
            "@Day5", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter( _
            "@Day6", adBoolean, adParamInput)
        .Parameters.Append .CreateParameter( _
            "@Day7", adBoolean, adParamInput)
        
        'Set values -
        'Remember, these are NON working days. So 0=non-working, and 1=working
        .Parameters.Item(1).Value = IIf(ckDay(1).Value = vbChecked, 0, 1)
        .Parameters.Item(2).Value = IIf(ckDay(2).Value = vbChecked, 0, 1)
        .Parameters.Item(3).Value = IIf(ckDay(3).Value = vbChecked, 0, 1)
        .Parameters.Item(4).Value = IIf(ckDay(4).Value = vbChecked, 0, 1)
        .Parameters.Item(5).Value = IIf(ckDay(5).Value = vbChecked, 0, 1)
        .Parameters.Item(6).Value = IIf(ckDay(6).Value = vbChecked, 0, 1)
        .Parameters.Item(7).Value = IIf(ckDay(7).Value = vbChecked, 0, 1)
    End With
    
    AIM_WorkingDays_Update_Sp.Execute

    If Not (AIM_WorkingDays_Update_Sp Is Nothing) Then Set AIM_WorkingDays_Update_Sp.ActiveConnection = Nothing
    Set AIM_WorkingDays_Update_Sp = Nothing

Exit Sub
ErrorHandler:
    If Not (AIM_WorkingDays_Update_Sp Is Nothing) Then Set AIM_WorkingDays_Update_Sp.ActiveConnection = Nothing
    Set AIM_WorkingDays_Update_Sp = Nothing
    
    'Err.Raise Err.Number, Err.source & "(cmdSave_Click))", Err.Description
    f_HandleErr , , , Me.Caption & "::cmdSave_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgFiscalYears_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, _
                                        StartLocation As Variant, _
                                        ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
 
    Dim r, i  As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMYears) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAIMYears.MoveLast
        Else
            rsAIMYears.MoveFirst
        End If
    
    Else
        rsAIMYears.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAIMYears.MovePrevious
        Else
            rsAIMYears.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsAIMYears.BOF Or rsAIMYears.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsAIMYears("FiscalYear").Value
        RowBuf.Value(i, 1) = Format(rsAIMYears("FYStartDate").Value, gDateFormat)
        RowBuf.Value(i, 2) = Format(rsAIMYears("FYEndDate").Value, gDateFormat)
        
        RowBuf.Bookmark(i) = rsAIMYears.Bookmark
    
        If ReadPriorRows Then
            rsAIMYears.MovePrevious
        Else
            rsAIMYears.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgFiscalYears_UnboundReadData)"
    f_HandleErr , , , Me.Caption & "dgFiscalYears_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgFiscalYears_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Define columns
    Me.dgFiscalYears.Columns(0).Name = "FiscalYear"
    Me.dgFiscalYears.Columns(0).Caption = getTranslationResource("Year")
    Me.dgFiscalYears.Columns(0).Width = 600
    Me.dgFiscalYears.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgFiscalYears.Columns(0).Locked = True
    
    Me.dgFiscalYears.Columns(1).Name = "FYStartDate"
    Me.dgFiscalYears.Columns(1).Caption = getTranslationResource("Start Date")
    Me.dgFiscalYears.Columns(1).Width = 1150
    Me.dgFiscalYears.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgFiscalYears.Columns(1).Locked = True
    
    Me.dgFiscalYears.Columns(2).Name = "FYEndDate"
    Me.dgFiscalYears.Columns(2).Caption = getTranslationResource("End Date")
    Me.dgFiscalYears.Columns(2).Width = 1150
    Me.dgFiscalYears.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgFiscalYears.Columns(2).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgFiscalYears, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgFiscalYears.Columns.Count - 1
'        dgFiscalYears.Columns(IndexCounter).HasHeadBackColor = True
'        dgFiscalYears.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgFiscalYears.Columns(IndexCounter).Locked = False Then dgFiscalYears.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgFiscalYears_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dgFiscalYears_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strMessage1 As String
        
    'Display status message
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01301")
    If StrComp(strMessage, "STATMSG01301") = 0 Then strMessage = "Initializing Calendar Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Initialize the Style Sets for the Calendar
    Me.mnAIMDays.StyleSets.Add "WorkingDay"
    Me.mnAIMDays.StyleSets.Add "NonWorkingDay"

    Me.mnAIMDays.StyleSets("WorkingDay").BackColor = RGB(209, 255, 190)
    Me.mnAIMDays.StyleSets("NonWorkingDay").BackColor = RGB(255, 255, 206)
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize the stored procedures
    Set AIM_AIMDays_Load_Sp = New ADODB.Command
    With AIM_AIMDays_Load_Sp
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMDays_Load_Sp"
        Set .ActiveConnection = Cn
    End With
    
    Set AIM_AIMYears_Load_Sp = New ADODB.Command
    With AIM_AIMYears_Load_Sp
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMYears_Load_Sp"
        Set .ActiveConnection = Cn
        .Parameters.Append .CreateParameter( _
            "RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter( _
            "@FiscalYear", adInteger, adParamInput, , 0)
    End With

    'Load the AIMYears Table
    Set rsAIMYears = New ADODB.Recordset
    With rsAIMYears
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    RtnCode = AIMYears_Load(AIM_AIMYears_Load_Sp, rsAIMYears, 0)
    If RtnCode > 0 Then
        SetCalendarRange
    End If
    
    'Initialize the AIM Days Record Set
    Set rsAIMDays = New ADODB.Recordset
    With rsAIMDays
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    RtnCode = AIMDays_Load(AIM_AIMDays_Load_Sp, rsAIMDays)
    If RtnCode > 0 Then
        RefreshCalendar
    End If
    
    SetAIMDaysStatus
    
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.cmdBuildFYDays.Enabled = False
    Else
        Me.cmdBuildFYDays.Enabled = True
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Make the spin button visible
    Me.txtFirstFiscalYear.Spin.Visible = 1
    Me.txtNbrFiscalYears.Spin.Visible = 1
    Me.txtFYStartDate.DropDown.Visible = 1

    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_load)"
    f_HandleErr , , , Me.Caption & "Form_load", Now, gDRGeneralError, True, Err
    Unload Me
End Sub

Private Sub Form_Resize()
On Error GoTo ErrorHandler

    Dim ResizedVal As Long
    
    Me.frAIMYears.Top = 0
    
    ResizedVal = Me.ScaleHeight - (cmdBuildFYDays.Height + tbNavigation.GetDockHeight(ssDockedTop) + 600)
    If ResizedVal > 0 _
    And ResizedVal >= 5865 _
    Then
        Me.frAIMYears.Height = ResizedVal
        Me.mnAIMDays.Height = ResizedVal
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Resize)"
    f_HandleErr , , , Me.Caption & "::Form_Resize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsAIMDays) Then rsAIMDays.Close
    Set rsAIMDays = Nothing
    
    If f_IsRecordsetValidAndOpen(rsAIMYears) Then rsAIMYears.Close
    Set rsAIMYears = Nothing
        
    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Load_Sp = Nothing
    
    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing

    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , Me.Caption & "::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnAIMDays_DblClick()
On Error GoTo ErrorHandler

    Dim DayStatus As Integer
    Dim FYPeriod As Integer
    Dim AIM_AIMDays_Upd_Sp As ADODB.Command
    Dim FiscalYear As Integer
    Dim FYStartDate As Date
    Dim FYEndDate As Date
    Dim FYDate As Date
    
    'Check to see if this user has update priviledges
    If gAccessLvl = 1 Then
        Exit Sub
    End If
    
    If f_IsRecordsetOpenAndPopulated(rsAIMYears) Then
        FYDate = Me.mnAIMDays.SelectedDays(0).Date
        FYPeriod = GetFiscalPeriod(FYDate)
        'Toggle the style set for the day
        If Me.mnAIMDays.SelectedDays(0).StyleSet = "WorkingDay" Then
            Me.mnAIMDays.SelectedDays(0).StyleSet = "NonWorkingDay"
            Me.mnAIMDays.SelectedDays(0).Caption = Format(FYPeriod, "00") + " - " + getTranslationResource("No")
            DayStatus = 0
        Else
            Me.mnAIMDays.SelectedDays(0).StyleSet = "WorkingDay"
            Me.mnAIMDays.SelectedDays(0).Caption = Format(FYPeriod, "00") + " - " + getTranslationResource("Yes")
            DayStatus = 1
        End If
            
        Set AIM_AIMDays_Upd_Sp = New ADODB.Command
        With AIM_AIMDays_Upd_Sp
            .CommandType = adCmdStoredProc
            .CommandText = "AIM_AIMDays_Upd_Sp"
            
            Set .ActiveConnection = Cn
            .Parameters.Append .CreateParameter( _
                "RETURN", adInteger, adParamReturnValue)
            '@FYDate datetime,
            .Parameters.Append .CreateParameter( _
                "@FYDate", adDate, adParamInput)
            '@DayStatus tinyint,
            .Parameters.Append .CreateParameter( _
                "@DayStatus", adTinyInt, adParamInput)
            
            .Parameters("@FYDate").Value = Me.mnAIMDays.Date
            .Parameters("@DayStatus").Value = DayStatus
            
            .Execute
        End With
    Else
        'Add ErrorHandler
    End If
    
    If Not (AIM_AIMDays_Upd_Sp Is Nothing) Then Set AIM_AIMDays_Upd_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Upd_Sp = Nothing
    
    Me.mnAIMDays.SelectedDays.RemoveAll
    
Exit Sub
ErrorHandler:
    If Not (AIM_AIMDays_Upd_Sp Is Nothing) Then Set AIM_AIMDays_Upd_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Upd_Sp = Nothing
    'f_HandleErr Me.Caption & "(mnAIMDays_DblClick)"
    f_HandleErr , , , Me.Caption & "::mnAIMDays_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , Me.Caption & "::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub SetAIMDaysStatus()
On Error GoTo ErrorHandler
    
    Dim rsAIMDaysStatus As ADODB.Recordset
    Dim AIM_WorkingDays_Get_Sp As ADODB.Command
    
    Dim IndexCounter As Integer
    Dim FieldName As String
    
    'Instantiate command for stored procedure
    Set AIM_WorkingDays_Get_Sp = New ADODB.Command
    With AIM_WorkingDays_Get_Sp
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_WorkingDays_Get_Sp"
        Set .ActiveConnection = Cn
    End With
    
    'Instantiate Recordset
    Set rsAIMDaysStatus = New ADODB.Recordset
    With rsAIMDaysStatus
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
   
        .Open AIM_WorkingDays_Get_Sp
    End With
    
    'Process data
    If f_IsRecordsetOpenAndPopulated(rsAIMDaysStatus) Then
        For IndexCounter = 1 To 7
          FieldName = "Day" & Trim$(str(IndexCounter))
          ckDay(IndexCounter).Value = IIf(rsAIMDaysStatus(FieldName).Value = False, vbChecked, vbUnchecked)
        Next
    End If
    
    'Clean up
    If f_IsRecordsetValidAndOpen(rsAIMDaysStatus) Then rsAIMDaysStatus.Close
    Set rsAIMDaysStatus = Nothing
    If Not (AIM_WorkingDays_Get_Sp Is Nothing) Then Set AIM_WorkingDays_Get_Sp.ActiveConnection = Nothing
    Set AIM_WorkingDays_Get_Sp = Nothing
    
Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMDaysStatus) Then rsAIMDaysStatus.Close
    Set rsAIMDaysStatus = Nothing
    If Not (AIM_WorkingDays_Get_Sp Is Nothing) Then Set AIM_WorkingDays_Get_Sp.ActiveConnection = Nothing
    Set AIM_WorkingDays_Get_Sp = Nothing
    'f_HandleErr Me.Caption & "(SetAIMDaysStatus)"
    f_HandleErr , , , Me.Caption & "::SetAIMDaysStatus", Now, gDRGeneralError, True, Err
End Sub


