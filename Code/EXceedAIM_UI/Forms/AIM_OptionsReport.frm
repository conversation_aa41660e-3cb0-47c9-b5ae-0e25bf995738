VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_OptionsReport 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Options Report"
   ClientHeight    =   2505
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   4875
   Icon            =   "AIM_OptionsReport.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2505
   ScaleWidth      =   4875
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   120
      Top             =   2040
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_OptionsReport.frx":030A
      ToolBars        =   "AIM_OptionsReport.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   1980
      Left            =   60
      TabIndex        =   1
      Top             =   60
      Width           =   4755
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMOptions 
         Bindings        =   "AIM_OptionsReport.frx":3733
         Height          =   345
         Left            =   2745
         TabIndex        =   0
         Top             =   255
         Width           =   1860
         DataFieldList   =   "OptionId"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3281
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "OptionId"
      End
      Begin VB.Label Label 
         Caption         =   "Option ID"
         Height          =   300
         Left            =   200
         TabIndex        =   2
         Top             =   277
         Width           =   2450
      End
   End
End
Attribute VB_Name = "AIM_OptionsReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Private Function BldSQL(OptionId As String)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    
    SqlStmt = "SELECT * "
    SqlStmt = SqlStmt & vbCrLf & "FROM AIMOptions "
    
    OptionId = Trim(OptionId)
    
    'Build the Where Clause
    If OptionId <> "" _
    And OptionId <> getTranslationResource("All") _
    Then
        WhrStmt = "WHERE AIMOptions.OptionId = N'" & OptionId & "' "
    End If
    
    'Append Where Clause
    SqlStmt = SqlStmt & vbCrLf & WhrStmt
    
    'Append Order by Clause
    SqlStmt = SqlStmt & vbCrLf & "ORDER BY AIMOptions.OptionId "
    
    BldSQL = SqlStmt
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldSQL)"
     f_HandleErr , , , "AIM_OptionsReport::BldSQL", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim Report As AIM_Options_Report
    Dim SqlStmt As String
    Dim strMessage As String
    Dim rsReport As ADODB.Recordset

    Select Case Tool.ID
    Case "ID_Print", "ID_Preview"
        'Housekeeping
        strMessage = getTranslationResource("STATMSG03600")
        If StrComp(strMessage, "STATMSG03600") = 0 Then strMessage = "Building the Options Report ... "
        Write_Message strMessage
        
        'Build SQL Statement
        SqlStmt = BldSQL(Me.dcAIMOptions.Text)
        
        'Open the report result set
        Set rsReport = New ADODB.Recordset
        rsReport.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
              
        If Not f_IsRecordsetOpenAndPopulated(rsReport) Then
            If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
            Set rsReport = Nothing
            strMessage = getTranslationResource("TEXTMSG05103")
            If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
            Write_Message strMessage
            Exit Sub
        End If
    
        'Instantiate the Report
        Set Report = New AIM_Options_Report
        Set Report.dcAIMOptions.Recordset = rsReport
        
        Select Case Tool.ID
            Case "ID_Print"
                Report.PrintReport True
        
            Case "ID_Preview"
                Set AIM_Reports.ARViewer.ReportSource = Report
                AIM_Reports.Show vbModal, AIM_Main
        End Select
        
    Case "ID_Close"
        Unload Me
        Exit Sub
    
    End Select
    
    'Wrap Up
    Write_Message ""
    Screen.MousePointer = vbNormal
    Set Report = Nothing
    Set AIM_Options_Report = Nothing
    
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing

Exit Sub
ErrorHandler:
    Write_Message ""
    Screen.MousePointer = vbNormal
    Set Report = Nothing
    Set AIM_Options_Report = Nothing
    
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing

    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AIM_OptionsReport::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
End Sub
 
Private Sub dcAIMOptions_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMOptions.Columns(0).Name = "optionid"
    Me.dcAIMOptions.Columns(0).Caption = getTranslationResource("Option ID")
    Me.dcAIMOptions.Columns(0).Width = 1000
    Me.dcAIMOptions.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMOptions.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMOptions.Columns(0).DataField = "optionid"
    
    Me.dcAIMOptions.Columns(1).Name = "opdesc"
    Me.dcAIMOptions.Columns(1).Caption = getTranslationResource("Option Description")
    Me.dcAIMOptions.Columns(1).Width = 2880
    Me.dcAIMOptions.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMOptions.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMOptions.Columns(1).DataField = "opdesc"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMOptions, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMOptions.Columns.Count - 1
'        dcAIMOptions.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMOptions.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMOptions_InitColumnProps)"
     f_HandleErr , , , "AIM_OptionsReport::dcAIMOptions_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_OptionsReport::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    Dim rsAIMOptions As ADODB.Recordset
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03601")
    If StrComp(strMessage, "STATMSG03601") = 0 Then strMessage = "Initializing Options Report..."
    Write_Message strMessage

    GetTranslatedCaptions Me

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build/Bind the AIM Locations Drop Down
    Me.dcAIMOptions.AddItem getTranslationResource("All") _
                & vbTab & getTranslationResource("All Options")
    'Fetch from database
    SqlStmt = "SELECT OptionID, OpDesc FROM AIMOptions ORDER BY OptionID "
    Set rsAIMOptions = New ADODB.Recordset
    rsAIMOptions.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMOptions) Then
        Do Until rsAIMOptions.eof
            Me.dcAIMOptions.AddItem rsAIMOptions("OptionId").Value & vbTab & rsAIMOptions("OpDesc").Value
            rsAIMOptions.MoveNext
        Loop
    End If
    
    'Initialize Drop Down
    Me.dcAIMOptions.Text = getTranslationResource("All")
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    If f_IsRecordsetValidAndOpen(rsAIMOptions) Then rsAIMOptions.Close
    Set rsAIMOptions = Nothing
    
Exit Sub
ErrorHandler:
    Write_Message ""
    Screen.MousePointer = vbNormal
    If f_IsRecordsetValidAndOpen(rsAIMOptions) Then rsAIMOptions.Close
    Set rsAIMOptions = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_OptionsReport::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
        f_HandleErr , , , "AIM_OptionsReport::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub


