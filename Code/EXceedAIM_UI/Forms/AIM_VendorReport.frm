VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_VendorReport 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Vendor Report"
   ClientHeight    =   2505
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   4875
   Icon            =   "AIM_VendorReport.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2505
   ScaleWidth      =   4875
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   120
      Top             =   1980
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_VendorReport.frx":030A
      ToolBars        =   "AIM_VendorReport.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   1980
      Left            =   36
      TabIndex        =   1
      Top             =   36
      Width           =   4755
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   345
         Left            =   2745
         TabIndex        =   0
         TabStop         =   0   'False
         Top             =   255
         Width           =   1860
         _Version        =   65536
         _ExtentX        =   3281
         _ExtentY        =   609
         Caption         =   "AIM_VendorReport.frx":3733
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VendorReport.frx":379F
         Key             =   "AIM_VendorReport.frx":37BD
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   "All"
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Vendor ID"
         Height          =   300
         Left            =   200
         TabIndex        =   2
         Top             =   292
         Width           =   2450
      End
   End
End
Attribute VB_Name = "AIM_VendorReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Function BldSQL(VnId As String)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    
    SqlStmt = "SELECT * "
    SqlStmt = SqlStmt + "FROM AIMVendors"
    
    'Build the Where Clause
    If VnId <> getTranslationResource("All") Then
        WhrStmt = "WHERE AIMVendors.VnId = N'" + VnId + "'"
    End If
    
    'Append Where Clause
    SqlStmt = Trim(SqlStmt) & " " & Trim(WhrStmt)
    
    'Append Order by Clause
    SqlStmt = Trim(SqlStmt) & " ORDER BY AIMVendors.VnId, AIMVendors.Assort"
    
    BldSQL = SqlStmt

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldSQL)"
     f_HandleErr , , , "AIM_VendorReport:::BldSQL", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim strMessage As String
    Dim rsVendors As ADODB.Recordset
    Dim VendorReport As AIM_Vendors_Report
    
    Select Case Tool.ID
    Case "ID_Print", "ID_Preview"
        'Housekeeping
        Screen.MousePointer = vbHourglass
        strMessage = getTranslationResource("STATMSG06400")
        If StrComp(strMessage, "STATMSG06400") = 0 Then strMessage = "Building Vendor Report..."
        Write_Message strMessage
        
        'Build SQL Statement
        SqlStmt = BldSQL(Me.txtVnId.Text)
        
        'Open the report result set
        Set rsVendors = New ADODB.Recordset
        rsVendors.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
        
        Write_Message ""
        Screen.MousePointer = vbNormal
        
        'Check if valid recordset
        If (Not f_IsRecordsetOpenAndPopulated(rsVendors)) Then
            If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
            Set rsVendors = Nothing
            strMessage = getTranslationResource("TEXTMSG05103")
            If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
            Write_Message strMessage
            Exit Sub
        End If
        
        'Instantiate the Report
        Set VendorReport = New AIM_Vendors_Report
        Set VendorReport.dcAIMVendors.Recordset = rsVendors
        
        Select Case Tool.ID
        Case "ID_Print"
            VendorReport.PrintReport True
    
        Case "ID_Preview"
            Set AIM_Reports.ARViewer.ReportSource = VendorReport
            AIM_Reports.Show vbModal, AIM_Main
    
        End Select
        
    Case "ID_Close"
        Unload Me
        Exit Sub
    
    End Select
    
    'Clean Up
    Set VendorReport = Nothing
    If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
    Set rsVendors = Nothing
    Set AIM_Vendors_Report = Nothing
        
Exit Sub
ErrorHandler:
    'Clean Up
    Set VendorReport = Nothing
    If f_IsRecordsetValidAndOpen(rsVendors) Then rsVendors.Close
    Set rsVendors = Nothing
    Set AIM_Vendors_Report = Nothing
    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AIM_VendorReport:::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_VendorReport:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG06401")
    If StrComp(strMessage, "STATMSG06401") = 0 Then strMessage = "Initializing Vendor Report..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize the Vendor Id
    Me.txtVnId.Text = getTranslationResource("All")
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_VendorReport:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_VendorReport:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
