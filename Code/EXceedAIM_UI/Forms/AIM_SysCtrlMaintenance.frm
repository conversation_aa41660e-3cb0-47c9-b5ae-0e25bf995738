VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{66A5AC41-25A9-11D2-9BBF-00A024695830}#1.0#0"; "titime8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_SysCtrlMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR System Control Maintenance"
   ClientHeight    =   6180
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11415
   Icon            =   "AIM_SysCtrlMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6180
   ScaleWidth      =   11415
   ShowInTaskbar   =   0   'False
   Begin VB.CheckBox chkLogOption 
      Caption         =   "Log Errros And Job Updates To Database"
      BeginProperty DataFormat 
         Type            =   5
         Format          =   ""
         HaveTrueFalseNull=   1
         TrueValue       =   "True"
         FalseValue      =   "False"
         NullValue       =   ""
         FirstDayOfWeek  =   0
         FirstWeekOfYear =   0
         LCID            =   1033
         SubFormatType   =   7
      EndProperty
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   340
      Left            =   6150
      TabIndex        =   128
      Top             =   4700
      Width           =   4005
   End
   Begin ActiveTabs.SSActiveTabs atSysCtrl 
      Height          =   5760
      Left            =   120
      TabIndex        =   12
      Top             =   0
      Width           =   11220
      _ExtentX        =   19791
      _ExtentY        =   10160
      _Version        =   131083
      TabCount        =   6
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Tabs            =   "AIM_SysCtrlMaintenance.frx":030A
      Begin ActiveTabs.SSActiveTabPanel apPaths 
         Height          =   5370
         Left            =   -99969
         TabIndex        =   60
         Top             =   360
         Width           =   11160
         _ExtentX        =   19685
         _ExtentY        =   9472
         _Version        =   131083
         TabGuid         =   "AIM_SysCtrlMaintenance.frx":0494
         Begin TDBText6Ctl.TDBText txtDXPath 
            Height          =   345
            Left            =   3355
            TabIndex        =   13
            Top             =   420
            Width           =   7700
            _Version        =   65536
            _ExtentX        =   13582
            _ExtentY        =   609
            Caption         =   "AIM_SysCtrlMaintenance.frx":04BC
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_SysCtrlMaintenance.frx":0528
            Key             =   "AIM_SysCtrlMaintenance.frx":0546
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtDxPath_Out 
            Height          =   345
            Left            =   3355
            TabIndex        =   14
            Top             =   780
            Width           =   7700
            _Version        =   65536
            _ExtentX        =   13582
            _ExtentY        =   609
            Caption         =   "AIM_SysCtrlMaintenance.frx":058A
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_SysCtrlMaintenance.frx":05F6
            Key             =   "AIM_SysCtrlMaintenance.frx":0614
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtDxPath_Backup 
            Height          =   345
            Left            =   3355
            TabIndex        =   15
            Top             =   1140
            Width           =   7700
            _Version        =   65536
            _ExtentX        =   13582
            _ExtentY        =   609
            Caption         =   "AIM_SysCtrlMaintenance.frx":0658
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_SysCtrlMaintenance.frx":06C4
            Key             =   "AIM_SysCtrlMaintenance.frx":06E2
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLogFile 
            Height          =   345
            Left            =   3355
            TabIndex        =   16
            Top             =   1500
            Width           =   7700
            _Version        =   65536
            _ExtentX        =   13582
            _ExtentY        =   609
            Caption         =   "AIM_SysCtrlMaintenance.frx":0726
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_SysCtrlMaintenance.frx":0792
            Key             =   "AIM_SysCtrlMaintenance.frx":07B0
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtAIMBatchPath 
            Height          =   345
            Left            =   3355
            TabIndex        =   17
            Top             =   1860
            Width           =   7700
            _Version        =   65536
            _ExtentX        =   13582
            _ExtentY        =   609
            Caption         =   "AIM_SysCtrlMaintenance.frx":07F4
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_SysCtrlMaintenance.frx":0860
            Key             =   "AIM_SysCtrlMaintenance.frx":087E
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   255
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin VB.Frame Frame1 
            Caption         =   "Data Interface Control Options"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   2565
            Left            =   0
            TabIndex        =   66
            Top             =   2640
            Width           =   11115
            Begin TDBTime6Ctl.TDBTime ttimeLast_OrderGen 
               Height          =   375
               Left            =   5640
               TabIndex        =   24
               Top             =   2040
               Width           =   975
               _Version        =   65536
               _ExtentX        =   1720
               _ExtentY        =   661
               Caption         =   "AIM_SysCtrlMaintenance.frx":08C2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Keys            =   "AIM_SysCtrlMaintenance.frx":092E
               Spin            =   "AIM_SysCtrlMaintenance.frx":097E
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "hh:nn:ss"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "hh:nn:ss"
               HighlightText   =   0
               Hour12Mode      =   1
               IMEMode         =   3
               MarginBottom    =   1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MaxTime         =   0.99999
               MidnightMode    =   0
               MinTime         =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "00:00:00"
               ValidateMode    =   0
               ValueVT         =   1917845511
               Value           =   0
            End
            Begin TDBDate6Ctl.TDBDate tdateLast_OrderGen 
               Height          =   375
               Left            =   4320
               TabIndex        =   23
               Top             =   2040
               Width           =   1335
               _Version        =   65536
               _ExtentX        =   2355
               _ExtentY        =   661
               Calendar        =   "AIM_SysCtrlMaintenance.frx":09A6
               Caption         =   "AIM_SysCtrlMaintenance.frx":0ABE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":0B2A
               Keys            =   "AIM_SysCtrlMaintenance.frx":0B48
               Spin            =   "AIM_SysCtrlMaintenance.frx":0BA6
               AlignHorizontal =   0
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   0
               ClipMode        =   0
               CursorPosition  =   0
               DataProperty    =   0
               DisplayFormat   =   "mm/dd/yyyy"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               FirstMonth      =   4
               ForeColor       =   -2147483640
               Format          =   "mm/dd/yyyy"
               HighlightText   =   0
               IMEMode         =   3
               MarginBottom    =   1
               MarginLeft      =   1
               MarginRight     =   1
               MarginTop       =   1
               MaxDate         =   2958465
               MinDate         =   -657434
               MousePointer    =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
               PromptChar      =   "_"
               ReadOnly        =   0
               ShowContextMenu =   -1
               ShowLiterals    =   0
               TabAction       =   0
               Text            =   "01/01/2000"
               ValidateMode    =   0
               ValueVT         =   7
               Value           =   36526
               CenturyMode     =   0
            End
            Begin VB.CheckBox ckUpdateVnLT 
               Caption         =   "Update Vendor Lead Time"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   200
               TabIndex        =   19
               Top             =   585
               Width           =   4125
            End
            Begin VB.CheckBox ckUpdateGoalsOption 
               Caption         =   "Update Vendor Goals"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   200
               TabIndex        =   18
               Top             =   255
               Width           =   4125
            End
            Begin TDBNumber6Ctl.TDBNumber txtLast_POSeq 
               Height          =   345
               Left            =   9555
               TabIndex        =   22
               Top             =   360
               Width           =   1380
               _Version        =   65536
               _ExtentX        =   2434
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":0BCE
               Caption         =   "AIM_SysCtrlMaintenance.frx":0BEE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":0C5A
               Keys            =   "AIM_SysCtrlMaintenance.frx":0C78
               Spin            =   "AIM_SysCtrlMaintenance.frx":0CC2
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "########0;-########0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "########0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999999999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtVnLTPctChgFilter 
               Height          =   345
               Left            =   4305
               TabIndex        =   20
               Top             =   975
               Width           =   945
               _Version        =   65536
               _ExtentX        =   1667
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":0CEA
               Caption         =   "AIM_SysCtrlMaintenance.frx":0D0A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":0D76
               Keys            =   "AIM_SysCtrlMaintenance.frx":0D94
               Spin            =   "AIM_SysCtrlMaintenance.frx":0DDE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.00;-##0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtVnLTDaysChgFilter 
               Height          =   345
               Left            =   4305
               TabIndex        =   21
               Top             =   1335
               Width           =   945
               _Version        =   65536
               _ExtentX        =   1667
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":0E06
               Caption         =   "AIM_SysCtrlMaintenance.frx":0E26
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":0E92
               Keys            =   "AIM_SysCtrlMaintenance.frx":0EB0
               Spin            =   "AIM_SysCtrlMaintenance.frx":0EFA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label2 
               Caption         =   "Last Order Generation Process"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   195
               TabIndex        =   123
               Top             =   2040
               Width           =   4080
            End
            Begin VB.Label Label2 
               Caption         =   "Vendor Lead Time Days Change Filter"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   195
               TabIndex        =   71
               Top             =   1380
               Width           =   4080
            End
            Begin VB.Label Label2 
               Caption         =   "Vendor Lead Time % Change Filter"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   195
               TabIndex        =   70
               Top             =   1020
               Width           =   4080
            End
            Begin VB.Label Label2 
               Caption         =   "Last P. O. Sequence Number"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   6120
               TabIndex        =   67
               Top             =   390
               Width           =   3285
            End
         End
         Begin VB.Label Label2 
            Caption         =   "Data Interface Utility Location"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   4
            Left            =   195
            TabIndex        =   69
            Top             =   1920
            Width           =   3120
         End
         Begin VB.Label Label2 
            Caption         =   "Data Interface Log File name"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   3
            Left            =   195
            TabIndex        =   64
            Top             =   1545
            Width           =   3120
         End
         Begin VB.Label Label2 
            Caption         =   "Data Interface Backup"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   2
            Left            =   195
            TabIndex        =   63
            Top             =   1185
            Width           =   3120
         End
         Begin VB.Label Label2 
            Caption         =   "Data Interface Outbox"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   1
            Left            =   195
            TabIndex        =   62
            Top             =   825
            Width           =   3120
         End
         Begin VB.Label Label2 
            Caption         =   "Data Interface Inbox"
            BeginProperty Font 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            Height          =   300
            Index           =   0
            Left            =   195
            TabIndex        =   61
            Top             =   465
            Width           =   3120
         End
      End
      Begin ActiveTabs.SSActiveTabPanel apAllocation 
         Height          =   5370
         Left            =   -99969
         TabIndex        =   107
         Top             =   360
         Width           =   11160
         _ExtentX        =   19685
         _ExtentY        =   9472
         _Version        =   131083
         TabGuid         =   "AIM_SysCtrlMaintenance.frx":0F22
         Begin VB.Frame frAllocation 
            Height          =   5085
            Left            =   60
            TabIndex        =   108
            Top             =   60
            Width           =   11000
            Begin VB.Frame Frame6 
               Caption         =   "Prepack options"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   2055
               Left            =   5280
               TabIndex        =   57
               Top             =   2880
               Width           =   5535
               Begin VB.CheckBox ckPrepackEnabled 
                  Caption         =   "Enable prepack conversion of allocated quantity"
                  Height          =   375
                  Left            =   240
                  TabIndex        =   58
                  Top             =   360
                  Width           =   4575
               End
            End
            Begin VB.Frame frExceptionProc 
               Caption         =   "Exception Processing"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   2055
               Left            =   120
               TabIndex        =   110
               Top             =   2880
               Width           =   5055
               Begin VB.OptionButton optAllocExceptions 
                  Caption         =   "Apply exception trigger per line item"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   56
                  Top             =   1600
                  Width           =   4575
               End
               Begin VB.OptionButton optAllocExceptions 
                  Caption         =   "Apply exception trigger per destination location"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   55
                  Top             =   1187
                  Width           =   4575
               End
               Begin VB.OptionButton optAllocExceptions 
                  Caption         =   "Apply exception trigger per order"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   120
                  TabIndex        =   53
                  Top             =   360
                  Value           =   -1  'True
                  Width           =   4575
               End
               Begin TDBNumber6Ctl.TDBNumber txtAllocExceptionPct 
                  Height          =   345
                  Left            =   3840
                  TabIndex        =   54
                  Top             =   765
                  Width           =   1005
                  _Version        =   65536
                  _ExtentX        =   1773
                  _ExtentY        =   609
                  Calculator      =   "AIM_SysCtrlMaintenance.frx":0F4A
                  Caption         =   "AIM_SysCtrlMaintenance.frx":0F6A
                  BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  DropDown        =   "AIM_SysCtrlMaintenance.frx":0FD6
                  Keys            =   "AIM_SysCtrlMaintenance.frx":0FF4
                  Spin            =   "AIM_SysCtrlMaintenance.frx":103E
                  AlignHorizontal =   1
                  AlignVertical   =   2
                  Appearance      =   1
                  BackColor       =   -2147483643
                  BorderStyle     =   1
                  BtnPositioning  =   1
                  ClipMode        =   0
                  ClearAction     =   0
                  DecimalPoint    =   "."
                  DisplayFormat   =   "###0;-###0;0;0"
                  EditMode        =   0
                  Enabled         =   -1
                  ErrorBeep       =   0
                  ForeColor       =   -2147483640
                  Format          =   "###0"
                  HighlightText   =   1
                  MarginBottom    =   3
                  MarginLeft      =   3
                  MarginRight     =   3
                  MarginTop       =   3
                  MaxValue        =   100
                  MinValue        =   0
                  MousePointer    =   0
                  MoveOnLRKey     =   0
                  NegativeColor   =   255
                  OLEDragMode     =   0
                  OLEDropMode     =   0
                  ReadOnly        =   0
                  Separator       =   ""
                  ShowContextMenu =   1
                  ValueVT         =   1245189
                  Value           =   0
                  MaxValueVT      =   5
                  MinValueVT      =   5
               End
               Begin VB.Label Label1 
                  Caption         =   "Order exception percentage"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   300
                  Index           =   15
                  Left            =   480
                  TabIndex        =   112
                  Top             =   810
                  Width           =   3120
               End
            End
            Begin VB.Frame frNeedDetermination 
               Caption         =   "Need Determination"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   2535
               Left            =   120
               TabIndex        =   109
               Top             =   240
               Width           =   10695
               Begin VB.OptionButton optAllocNeed 
                  Caption         =   "Use both Re-stock Profile and Order"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   2
                  Left            =   120
                  TabIndex        =   50
                  Top             =   780
                  Width           =   4575
               End
               Begin VB.OptionButton optAllocNeed 
                  Caption         =   "Use only the Re-stock Order"
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   1
                  Left            =   120
                  TabIndex        =   48
                  Top             =   360
                  Value           =   -1  'True
                  Width           =   4575
               End
               Begin VB.OptionButton optAllocNeed 
                  Caption         =   "Use only the Re-stock Profile"
                  Enabled         =   0   'False
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   340
                  Index           =   0
                  Left            =   5880
                  TabIndex        =   49
                  Top             =   360
                  Visible         =   0   'False
                  Width           =   4575
               End
               Begin VB.Frame frRestockProfile 
                  Caption         =   "Re-stock Profile will contain"
                  Enabled         =   0   'False
                  BeginProperty Font 
                     Name            =   "Microsoft Sans Serif"
                     Size            =   8.25
                     Charset         =   0
                     Weight          =   400
                     Underline       =   0   'False
                     Italic          =   0   'False
                     Strikethrough   =   0   'False
                  EndProperty
                  Height          =   1215
                  Left            =   360
                  TabIndex        =   111
                  Top             =   1200
                  Width           =   4695
                  Begin VB.OptionButton optAllocNeedRestockProfile 
                     Caption         =   "On-hand Quantity"
                     Enabled         =   0   'False
                     BeginProperty Font 
                        Name            =   "Microsoft Sans Serif"
                        Size            =   8.25
                        Charset         =   0
                        Weight          =   400
                        Underline       =   0   'False
                        Italic          =   0   'False
                        Strikethrough   =   0   'False
                     EndProperty
                     Height          =   340
                     Index           =   1
                     Left            =   240
                     TabIndex        =   52
                     Top             =   780
                     Width           =   4095
                  End
                  Begin VB.OptionButton optAllocNeedRestockProfile 
                     Caption         =   "Standard Quantity"
                     Enabled         =   0   'False
                     BeginProperty Font 
                        Name            =   "Microsoft Sans Serif"
                        Size            =   8.25
                        Charset         =   0
                        Weight          =   400
                        Underline       =   0   'False
                        Italic          =   0   'False
                        Strikethrough   =   0   'False
                     EndProperty
                     Height          =   340
                     Index           =   0
                     Left            =   240
                     TabIndex        =   51
                     Top             =   360
                     Width           =   4095
                  End
               End
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel apInternat 
         Height          =   5370
         Left            =   -99969
         TabIndex        =   72
         Top             =   360
         Width           =   11160
         _ExtentX        =   19685
         _ExtentY        =   9472
         _Version        =   131083
         TabGuid         =   "AIM_SysCtrlMaintenance.frx":1066
         Begin VB.Frame Frame2 
            Height          =   5085
            Left            =   60
            TabIndex        =   73
            Top             =   60
            Width           =   11000
            Begin VB.CheckBox ckUnicodeOption 
               Caption         =   "Unicode (Data Interface)"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   6780
               TabIndex        =   47
               Top             =   1005
               Width           =   4065
            End
            Begin VB.CheckBox ckColonOption 
               Caption         =   "Display colon(:) with labels"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   6780
               TabIndex        =   45
               Top             =   285
               Width           =   4065
            End
            Begin VB.CheckBox ckGridAutoSizeOption 
               Caption         =   "Auto size table column widths"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   6780
               TabIndex        =   46
               Top             =   645
               Width           =   4065
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDateFormat 
               Height          =   345
               Left            =   3345
               TabIndex        =   43
               Top             =   600
               Width           =   1755
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   3096
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcTimeFormat 
               Height          =   345
               Left            =   3345
               TabIndex        =   44
               Top             =   960
               Width           =   1755
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   3096
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLangID 
               Bindings        =   "AIM_SysCtrlMaintenance.frx":108E
               Height          =   345
               Left            =   3345
               TabIndex        =   41
               Top             =   240
               Width           =   1755
               DataFieldList   =   "LangId"
               _Version        =   196617
               DataMode        =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   3096
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
               DataFieldToDisplay=   "LangID"
            End
            Begin VB.Label Label1 
               Caption         =   "Default Language"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   12
               Left            =   200
               TabIndex        =   76
               Top             =   300
               Width           =   3000
            End
            Begin VB.Label Label1 
               Caption         =   "Date Format"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   13
               Left            =   200
               TabIndex        =   75
               Top             =   645
               Width           =   3000
            End
            Begin VB.Label Label1 
               Caption         =   "Time Format"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   14
               Left            =   200
               TabIndex        =   74
               Top             =   1005
               Width           =   3000
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel apIntermittent 
         Height          =   5370
         Left            =   -99969
         TabIndex        =   68
         Top             =   360
         Width           =   11160
         _ExtentX        =   19685
         _ExtentY        =   9472
         _Version        =   131083
         TabGuid         =   "AIM_SysCtrlMaintenance.frx":10A5
         Begin VB.Frame Frame5 
            Height          =   5085
            Left            =   45
            TabIndex        =   101
            Top             =   45
            Width           =   11000
            Begin VB.CheckBox ckInt_SrvLvlOverride 
               Caption         =   "Override Service Level Goal"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   6300
               TabIndex        =   42
               Top             =   645
               Width           =   4485
            End
            Begin VB.CheckBox ckInt_Enabled 
               Caption         =   "Enable Intermittent Demand Modelling"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Left            =   6300
               TabIndex        =   40
               Top             =   285
               Width           =   4485
            End
            Begin TDBNumber6Ctl.TDBNumber txtInt_MinPctZero 
               Height          =   345
               Left            =   4200
               TabIndex        =   36
               Top             =   600
               Width           =   1035
               _Version        =   65536
               _ExtentX        =   1826
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":10CD
               Caption         =   "AIM_SysCtrlMaintenance.frx":10ED
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1159
               Keys            =   "AIM_SysCtrlMaintenance.frx":1177
               Spin            =   "AIM_SysCtrlMaintenance.frx":11C1
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin TDBNumber6Ctl.TDBNumber txtInt_MaxPctZero 
               Height          =   345
               Left            =   4200
               TabIndex        =   37
               Top             =   960
               Width           =   1035
               _Version        =   65536
               _ExtentX        =   1826
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":11E9
               Caption         =   "AIM_SysCtrlMaintenance.frx":1209
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1275
               Keys            =   "AIM_SysCtrlMaintenance.frx":1293
               Spin            =   "AIM_SysCtrlMaintenance.frx":12DD
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   1479671813
            End
            Begin TDBNumber6Ctl.TDBNumber txtInt_SeasonalityIndex 
               Height          =   345
               Left            =   4200
               TabIndex        =   38
               Top             =   1320
               Width           =   1035
               _Version        =   65536
               _ExtentX        =   1826
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1305
               Caption         =   "AIM_SysCtrlMaintenance.frx":1325
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1391
               Keys            =   "AIM_SysCtrlMaintenance.frx":13AF
               Spin            =   "AIM_SysCtrlMaintenance.frx":13F9
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   99.999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtInt_LookBackPds 
               Height          =   345
               Left            =   4200
               TabIndex        =   39
               Top             =   1680
               Width           =   1035
               _Version        =   65536
               _ExtentX        =   1826
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1421
               Caption         =   "AIM_SysCtrlMaintenance.frx":1441
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":14AD
               Keys            =   "AIM_SysCtrlMaintenance.frx":14CB
               Spin            =   "AIM_SysCtrlMaintenance.frx":1515
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   156
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtInt_SrvLvl 
               Height          =   345
               Left            =   4200
               TabIndex        =   35
               Top             =   240
               Width           =   1035
               _Version        =   65536
               _ExtentX        =   1826
               _ExtentY        =   600
               Calculator      =   "AIM_SysCtrlMaintenance.frx":153D
               Caption         =   "AIM_SysCtrlMaintenance.frx":155D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":15C9
               Keys            =   "AIM_SysCtrlMaintenance.frx":15E7
               Spin            =   "AIM_SysCtrlMaintenance.frx":1631
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label4 
               Caption         =   "Minimum Percent Zero"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   106
               Top             =   645
               Width           =   3960
            End
            Begin VB.Label Label4 
               Caption         =   "Maximum Percent Zero"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   105
               Top             =   1005
               Width           =   3960
            End
            Begin VB.Label Label4 
               Caption         =   "Maximum Seasonality Index Rating"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   104
               Top             =   1365
               Width           =   3960
            End
            Begin VB.Label Label4 
               Caption         =   "Look-back Periods"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   103
               Top             =   1725
               Width           =   3960
            End
            Begin VB.Label Label4 
               Caption         =   "Override Service Level Goal"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   102
               Top             =   285
               Width           =   3960
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel apBuyerReview 
         Height          =   5370
         Left            =   -99969
         TabIndex        =   65
         Top             =   360
         Width           =   11160
         _ExtentX        =   19685
         _ExtentY        =   9472
         _Version        =   131083
         TabGuid         =   "AIM_SysCtrlMaintenance.frx":1659
         Begin VB.Frame Frame4 
            Height          =   5085
            Left            =   60
            TabIndex        =   90
            Top             =   60
            Width           =   11000
            Begin VB.CheckBox ckAllowNegativeAvailQty 
               Caption         =   "Allow Negative Available Qty"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   117
               Top             =   3600
               Width           =   4965
            End
            Begin VB.CheckBox ckVendor_Sizing 
               Caption         =   "Perform Vendor Sizing"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   113
               Top             =   2160
               Width           =   4965
            End
            Begin VB.CheckBox ckMDCOption 
               Caption         =   "MDC Processing"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   114
               Top             =   2520
               Width           =   4965
            End
            Begin VB.CheckBox ckProdConstraint 
               Caption         =   "Enable Production Constraints"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   115
               Top             =   2880
               Width           =   4965
            End
            Begin VB.CheckBox ckGRTZeroRSOQinPRODCONST 
               Caption         =   "Greater than zero RSOQ in Production Constraints"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   240
               TabIndex        =   116
               Top             =   3210
               Width           =   4965
            End
            Begin TDBNumber6Ctl.TDBNumber txtDft_MinRchPct 
               Height          =   345
               Left            =   3825
               TabIndex        =   25
               Top             =   240
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1681
               Caption         =   "AIM_SysCtrlMaintenance.frx":16A1
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":170D
               Keys            =   "AIM_SysCtrlMaintenance.frx":172B
               Spin            =   "AIM_SysCtrlMaintenance.frx":1775
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.0;-#0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   99.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDft_BestRchPct 
               Height          =   345
               Left            =   3825
               TabIndex        =   26
               Top             =   600
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":179D
               Caption         =   "AIM_SysCtrlMaintenance.frx":17BD
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1829
               Keys            =   "AIM_SysCtrlMaintenance.frx":1847
               Spin            =   "AIM_SysCtrlMaintenance.frx":1891
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.0;-#0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   99.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   5
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDft_POStatus 
               Bindings        =   "AIM_SysCtrlMaintenance.frx":18B9
               DataField       =   " "
               Height          =   345
               Left            =   3825
               TabIndex        =   27
               Top             =   960
               Width           =   1005
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   1773
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDxPO_Option 
               Bindings        =   "AIM_SysCtrlMaintenance.frx":18C4
               DataField       =   " "
               Height          =   345
               Left            =   3825
               TabIndex        =   28
               Top             =   1320
               Width           =   1005
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   1773
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBNumber6Ctl.TDBNumber txtKFactor 
               Height          =   345
               Left            =   3825
               TabIndex        =   29
               Top             =   1680
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":18CF
               Caption         =   "AIM_SysCtrlMaintenance.frx":18EF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":195B
               Keys            =   "AIM_SysCtrlMaintenance.frx":1979
               Spin            =   "AIM_SysCtrlMaintenance.frx":19C3
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "###0.00;-###0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1000
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtMinROAI 
               Height          =   345
               Left            =   9825
               TabIndex        =   30
               Top             =   240
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":19EB
               Caption         =   "AIM_SysCtrlMaintenance.frx":1A0B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1A77
               Keys            =   "AIM_SysCtrlMaintenance.frx":1A95
               Spin            =   "AIM_SysCtrlMaintenance.frx":1ADF
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "###0.00;-###0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "###0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1000
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1179653
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtPriority_Min_Dser 
               Height          =   345
               Left            =   9825
               TabIndex        =   31
               Top             =   600
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1B07
               Caption         =   "AIM_SysCtrlMaintenance.frx":1B27
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1B93
               Keys            =   "AIM_SysCtrlMaintenance.frx":1BB1
               Spin            =   "AIM_SysCtrlMaintenance.frx":1BFB
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0.7
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   150142977
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBText6Ctl.TDBText txtPriority_Min_Vel 
               Height          =   345
               Left            =   9825
               TabIndex        =   32
               Top             =   960
               Width           =   495
               _Version        =   65536
               _ExtentX        =   873
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":1C23
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1C8F
               Key             =   "AIM_SysCtrlMaintenance.frx":1CAD
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   2
               AlignVertical   =   2
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   1
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBNumber6Ctl.TDBNumber txtAddl_LTDays 
               Height          =   345
               Left            =   9825
               TabIndex        =   33
               Top             =   1320
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1CF1
               Caption         =   "AIM_SysCtrlMaintenance.frx":1D11
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1D7D
               Keys            =   "AIM_SysCtrlMaintenance.frx":1D9B
               Spin            =   "AIM_SysCtrlMaintenance.frx":1DE5
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   5
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLt_Filter_Pct 
               Height          =   345
               Left            =   9825
               TabIndex        =   34
               Top             =   1680
               Width           =   1005
               _Version        =   65536
               _ExtentX        =   1773
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1E0D
               Caption         =   "AIM_SysCtrlMaintenance.frx":1E2D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1E99
               Keys            =   "AIM_SysCtrlMaintenance.frx":1EB7
               Spin            =   "AIM_SysCtrlMaintenance.frx":1F01
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.00;-0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   150142977
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label3 
               Caption         =   "Default Minimum Reach Percentage"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   100
               Top             =   285
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Default Best Reach Percentage"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   99
               Top             =   645
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Default Purchase Order Status"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   98
               Top             =   1005
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Purchase Order Generation Option"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   97
               Top             =   1365
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "K Factor"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   96
               Top             =   1725
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Minimum Return On Additional Investment (ROAI)"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   4995
               TabIndex        =   95
               Top             =   285
               Width           =   4725
            End
            Begin VB.Label Label3 
               Caption         =   "Priority Item Minimum Service Level"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   4995
               TabIndex        =   94
               Top             =   645
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Priority Item Minimum Velocity Code"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   4995
               TabIndex        =   93
               Top             =   1005
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Additional Lead Time Days"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   4995
               TabIndex        =   92
               Top             =   1365
               Width           =   4605
            End
            Begin VB.Label Label3 
               Caption         =   "Lead Time Filter Percentage"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   9
               Left            =   4995
               TabIndex        =   91
               Top             =   1725
               Width           =   4605
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel apGeneral 
         Height          =   5370
         Left            =   30
         TabIndex        =   59
         Top             =   360
         Width           =   11160
         _ExtentX        =   19685
         _ExtentY        =   9472
         _Version        =   131083
         TabGuid         =   "AIM_SysCtrlMaintenance.frx":1F29
         Begin VB.Frame Frame3 
            Height          =   5325
            Left            =   60
            TabIndex        =   77
            Top             =   45
            Width           =   10995
            Begin VB.CheckBox Ck_MasterItem_Option 
               Caption         =   "Master Item Option"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   5950
               TabIndex        =   125
               Top             =   4000
               Width           =   4965
            End
            Begin VB.CheckBox ckUpdateDbPassword 
               Caption         =   "Update Database Password"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   5950
               TabIndex        =   122
               Top             =   4000
               Width           =   4965
            End
            Begin VB.CheckBox ckCreateDBUser 
               Caption         =   "Create Database User"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   5950
               TabIndex        =   121
               Top             =   3700
               Width           =   4965
            End
            Begin VB.CheckBox CkCarryForwardRounding 
               Caption         =   "Carry Forward Rounding Enabled"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   5950
               TabIndex        =   120
               Top             =   3400
               Width           =   4965
            End
            Begin VB.CheckBox ckCalc_Perfrom 
               Caption         =   "Performance Analysis"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   5950
               TabIndex        =   119
               Top             =   3100
               Width           =   4965
            End
            Begin VB.CheckBox ckRetain_SD_Opt 
               Caption         =   "Retain Sales Detail"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   340
               Left            =   5950
               TabIndex        =   118
               Top             =   2835
               Width           =   4965
            End
            Begin TDBNumber6Ctl.TDBNumber txtCurSaVersion 
               Height          =   345
               Left            =   4185
               TabIndex        =   8
               Top             =   3240
               Width           =   660
               _Version        =   65536
               _ExtentX        =   1164
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":1F51
               Caption         =   "AIM_SysCtrlMaintenance.frx":1F71
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":1FDD
               Keys            =   "AIM_SysCtrlMaintenance.frx":1FFB
               Spin            =   "AIM_SysCtrlMaintenance.frx":2045
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2088828933
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBText6Ctl.TDBText txtCName 
               Height          =   345
               Left            =   4185
               TabIndex        =   0
               Top             =   240
               Width           =   6705
               _Version        =   65536
               _ExtentX        =   11827
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":206D
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":20D9
               Key             =   "AIM_SysCtrlMaintenance.frx":20F7
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAddress1 
               Height          =   345
               Left            =   4185
               TabIndex        =   1
               Top             =   600
               Width           =   6705
               _Version        =   65536
               _ExtentX        =   11827
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":213B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":21A7
               Key             =   "AIM_SysCtrlMaintenance.frx":21C5
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAddress2 
               Height          =   345
               Left            =   4185
               TabIndex        =   2
               Top             =   960
               Width           =   6705
               _Version        =   65536
               _ExtentX        =   11827
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":2209
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":2275
               Key             =   "AIM_SysCtrlMaintenance.frx":2293
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtCity 
               Height          =   345
               Left            =   4185
               TabIndex        =   4
               Top             =   1680
               Width           =   3240
               _Version        =   65536
               _ExtentX        =   5715
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":22D7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":2343
               Key             =   "AIM_SysCtrlMaintenance.frx":2361
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtState 
               Height          =   345
               Left            =   4185
               TabIndex        =   5
               Top             =   2040
               Width           =   3240
               _Version        =   65536
               _ExtentX        =   5715
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":23A5
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":2411
               Key             =   "AIM_SysCtrlMaintenance.frx":242F
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   10
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtCoZip 
               Height          =   345
               Left            =   4185
               TabIndex        =   6
               Top             =   2400
               Width           =   2145
               _Version        =   65536
               _ExtentX        =   3784
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":2473
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":24DF
               Key             =   "AIM_SysCtrlMaintenance.frx":24FD
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   10
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtAddress3 
               Height          =   345
               Left            =   4185
               TabIndex        =   3
               Top             =   1320
               Width           =   6705
               _Version        =   65536
               _ExtentX        =   11827
               _ExtentY        =   609
               Caption         =   "AIM_SysCtrlMaintenance.frx":2541
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":25AD
               Key             =   "AIM_SysCtrlMaintenance.frx":25CB
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   30
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   0
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcByIdSource 
               Bindings        =   "AIM_SysCtrlMaintenance.frx":260F
               DataField       =   " "
               Height          =   345
               Left            =   4185
               TabIndex        =   7
               Top             =   2880
               Width           =   660
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   2
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   1164
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBNumber6Ctl.TDBNumber txtSeasSmoothingPds 
               Height          =   345
               Left            =   4185
               TabIndex        =   9
               Top             =   3600
               Width           =   660
               _Version        =   65536
               _ExtentX        =   1164
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":261A
               Caption         =   "AIM_SysCtrlMaintenance.frx":263A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":26A6
               Keys            =   "AIM_SysCtrlMaintenance.frx":26C4
               Spin            =   "AIM_SysCtrlMaintenance.frx":270E
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   13
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1245189
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtClassOption 
               Height          =   345
               Left            =   4185
               TabIndex        =   10
               Top             =   3960
               Width           =   660
               _Version        =   65536
               _ExtentX        =   1164
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":2736
               Caption         =   "AIM_SysCtrlMaintenance.frx":2756
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":27C2
               Keys            =   "AIM_SysCtrlMaintenance.frx":27E0
               Spin            =   "AIM_SysCtrlMaintenance.frx":282A
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   4
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   82968577
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtAvgInvAlpha 
               Height          =   345
               Left            =   4200
               TabIndex        =   11
               Top             =   4320
               Width           =   765
               _Version        =   65536
               _ExtentX        =   1349
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":2852
               Caption         =   "AIM_SysCtrlMaintenance.frx":2872
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":28DE
               Keys            =   "AIM_SysCtrlMaintenance.frx":28FC
               Spin            =   "AIM_SysCtrlMaintenance.frx":2946
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.00;-0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1
               MinValue        =   0.05
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   82968577
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNumDaysForLogMsgs 
               Height          =   345
               Left            =   4200
               TabIndex        =   127
               Top             =   4680
               Width           =   780
               _Version        =   65536
               _ExtentX        =   1376
               _ExtentY        =   609
               Calculator      =   "AIM_SysCtrlMaintenance.frx":296E
               Caption         =   "AIM_SysCtrlMaintenance.frx":298E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SysCtrlMaintenance.frx":29FA
               Keys            =   "AIM_SysCtrlMaintenance.frx":2A18
               Spin            =   "AIM_SysCtrlMaintenance.frx":2A62
               AlignHorizontal =   1
               AlignVertical   =   0
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "#0;-#0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   365
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1245189
               Value           =   7
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label1 
               Caption         =   "Days to show error messages in lookup"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   16
               Left            =   195
               TabIndex        =   126
               Top             =   4680
               Width           =   3480
            End
            Begin VB.Label Label1 
               Caption         =   "Zip"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   195
               TabIndex        =   89
               Top             =   2445
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "State"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   195
               TabIndex        =   88
               Top             =   2085
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Company Name"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   87
               Top             =   285
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Address 1"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   86
               Top             =   645
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Address 2"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   85
               Top             =   1005
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "City"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   84
               Top             =   1725
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Address 3"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   83
               Top             =   1365
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Buyer ID Source"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   195
               TabIndex        =   82
               Top             =   2925
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Current Seasonality Profile Version"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   195
               TabIndex        =   81
               Top             =   3285
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Seasonality Smoothing Periods"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   9
               Left            =   195
               TabIndex        =   80
               Top             =   3645
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Classification Code Option"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   10
               Left            =   195
               TabIndex        =   79
               Top             =   4005
               Width           =   3960
            End
            Begin VB.Label Label1 
               Caption         =   "Average Inventory Alpha"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   11
               Left            =   195
               TabIndex        =   78
               Top             =   4365
               Width           =   3960
            End
         End
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   5760
      _ExtentX        =   688
      _ExtentY        =   688
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   3
      Style           =   0
      DisplayContextMenu=   0   'False
      Tools           =   "AIM_SysCtrlMaintenance.frx":2A8A
      ToolBars        =   "AIM_SysCtrlMaintenance.frx":50D8
   End
   Begin TDBNumber6Ctl.TDBNumber TDBNumber1 
      Height          =   345
      Left            =   9480
      TabIndex        =   124
      Top             =   3960
      Width           =   1380
      _Version        =   65536
      _ExtentX        =   2434
      _ExtentY        =   609
      Calculator      =   "AIM_SysCtrlMaintenance.frx":51E0
      Caption         =   "AIM_SysCtrlMaintenance.frx":5200
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_SysCtrlMaintenance.frx":526C
      Keys            =   "AIM_SysCtrlMaintenance.frx":528A
      Spin            =   "AIM_SysCtrlMaintenance.frx":52D4
      AlignHorizontal =   1
      AlignVertical   =   2
      Appearance      =   1
      BackColor       =   -2147483643
      BorderStyle     =   1
      BtnPositioning  =   1
      ClipMode        =   0
      ClearAction     =   0
      DecimalPoint    =   "."
      DisplayFormat   =   "########0;-########0;0;0"
      EditMode        =   0
      Enabled         =   -1
      ErrorBeep       =   0
      ForeColor       =   -2147483640
      Format          =   "########0"
      HighlightText   =   0
      MarginBottom    =   3
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MaxValue        =   999999999
      MinValue        =   0
      MousePointer    =   0
      MoveOnLRKey     =   0
      NegativeColor   =   255
      OLEDragMode     =   0
      OLEDropMode     =   0
      ReadOnly        =   0
      Separator       =   ""
      ShowContextMenu =   -1
      ValueVT         =   1245185
      Value           =   0
      MaxValueVT      =   5
      MinValueVT      =   5
   End
End
Attribute VB_Name = "AIM_SysCtrlMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsSysCtrl As ADODB.Recordset

Private Function Refresh_Form()
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    
    Me.txtCName.Text = Trim(rsSysCtrl("cname").Value)
    Me.txtAddress1.Text = Trim(rsSysCtrl("address1").Value)
    Me.txtAddress2.Text = Trim(rsSysCtrl("address2").Value)
    Me.txtAddress3.Text = Trim(rsSysCtrl("address3").Value)
    Me.txtCity.Text = Trim(rsSysCtrl("city").Value)
    Me.txtState.Text = Trim(rsSysCtrl("state").Value)
    Me.txtCoZip.Text = Trim(rsSysCtrl("cozip").Value)
    Me.txtDft_MinRchPct.Value = rsSysCtrl("dft_minrchpct").Value
    Me.txtDft_BestRchPct.Value = rsSysCtrl("dft_bestrchpct").Value
    Me.txtDXPath.Text = rsSysCtrl("dxpath").Value
    Me.dcDft_POStatus.Text = rsSysCtrl("dft_postatus").Value
    Me.dcDxPO_Option.Text = rsSysCtrl("dxpo_option").Value
    Me.txtSeasSmoothingPds.Value = rsSysCtrl("SeasSmoothingPds").Value
    Me.txtCurSaVersion.Value = rsSysCtrl("cursaversion").Value
    Me.ckRetain_SD_Opt.Value = rsSysCtrl("retain_sd_opt").Value
    Me.ckCalc_Perfrom.Value = rsSysCtrl("calc_perform").Value
    Me.ckVendor_Sizing = IIf(rsSysCtrl("Vendor_Sizing").Value = "Y", vbChecked, vbUnchecked)
    Me.ckUpdateGoalsOption = IIf(rsSysCtrl("UpdateGoalsOption").Value = "Y", vbChecked, vbUnchecked)
    Me.ckMDCOption = IIf(rsSysCtrl("MDCOption").Value = "Y", vbChecked, vbUnchecked)
    Me.ckProdConstraint = IIf(rsSysCtrl!ProductionConstraint = "Y", vbChecked, vbUnchecked)
    Me.ckAllowNegativeAvailQty = IIf(rsSysCtrl!AllowNegativeAvailQty = "Y", vbChecked, vbUnchecked)
    Me.ckGRTZeroRSOQinPRODCONST = IIf(rsSysCtrl!ProdConstraintGrtZero = "Y", vbChecked, vbUnchecked)
    Me.ckColonOption = IIf(rsSysCtrl("ColonOption").Value = "Y", vbChecked, vbUnchecked)
    Me.ckGridAutoSizeOption = IIf(rsSysCtrl("GridAutoSizeOption").Value = "Y", vbChecked, vbUnchecked)
    Me.txtDxPath_Out.Text = rsSysCtrl("dxpath_out").Value
    Me.txtDxPath_Backup.Text = rsSysCtrl("dxpath_backup").Value
    Me.txtLogFile.Text = rsSysCtrl("logfile").Value
    Me.txtAIMBatchPath.Text = rsSysCtrl("AIMBatchPath").Value
    Me.dcDateFormat.Text = IIf(IsNull(rsSysCtrl("DateFormat").Value), CDate(#1/1/1900#), rsSysCtrl("DateFormat").Value)
    Me.dcTimeFormat.Text = IIf(IsNull(rsSysCtrl("TimeFormat").Value), CDate("00:00:00 PM"), rsSysCtrl("TimeFormat").Value)
    Me.dcLangID.Text = UCase(rsSysCtrl("dft_LangId").Value)
    
    Me.dcByIdSource.Text = rsSysCtrl("byidsource").Value
    Me.txtKFactor.Value = rsSysCtrl("kfactor").Value
    Me.txtMinROAI.Value = rsSysCtrl("minroai").Value
    Me.txtPriority_Min_Dser.Value = rsSysCtrl("priority_min_dser").Value
    Me.txtPriority_Min_Vel.Text = rsSysCtrl("priority_min_velcode").Value
    Me.txtAddl_LTDays.Value = rsSysCtrl("addl_ltdays").Value
    Me.txtLt_Filter_Pct.Value = rsSysCtrl("lt_filter_pct").Value
    
    Me.ckUpdateVnLT = IIf(rsSysCtrl("UpDateVnLt").Value = "Y", vbChecked, vbUnchecked)
    Me.txtVnLTPctChgFilter.Value = rsSysCtrl("VnLTPctChgFilter").Value
    Me.txtVnLTDaysChgFilter.Value = rsSysCtrl("VnLTDaysChgFilter").Value
    Me.ckUnicodeOption.Value = IIf(rsSysCtrl("UnicodeOption").Value = "Y", vbChecked, vbUnchecked)
    Me.txtLast_POSeq.Value = rsSysCtrl("Last_POSeq").Value
    
    Me.txtClassOption.Value = rsSysCtrl("ClassOption").Value
    Me.txtAvgInvAlpha.Value = rsSysCtrl("AvgInvAlpha").Value
    Me.txtNumDaysForLogMsgs.Value = rsSysCtrl("NumOffDaysBehindFromCurrentDate").Value
    
    Me.txtInt_MinPctZero.Value = rsSysCtrl("Int_MinPctZero").Value
    Me.txtInt_MaxPctZero.Value = rsSysCtrl("Int_MaxPctZero").Value
    Me.txtInt_SeasonalityIndex.Value = rsSysCtrl("Int_SeasonalityIndex").Value
    Me.txtInt_LookBackPds.Value = rsSysCtrl("Int_LookBackPds").Value
    Me.txtInt_SrvLvl.Value = rsSysCtrl("Int_SrvLvl").Value
    Me.ckInt_Enabled = IIf(rsSysCtrl("Int_Enabled").Value = "Y", vbChecked, vbUnchecked)
    Me.ckInt_SrvLvlOverride = IIf(rsSysCtrl("Int_SrvLvlOverrideFlag").Value = "Y", vbChecked, vbUnchecked)
    Me.CkCarryForwardRounding = IIf(rsSysCtrl("CarryForwardRounding").Value = "Y", vbChecked, vbUnchecked)
    
    Me.chkLogOption = IIf(rsSysCtrl("WantErrorsTobeLoggedToDatabase").Value = True, vbChecked, vbUnchecked)
    
    'sujit
    Me.ckCreateDBUser = IIf(rsSysCtrl("CreateDBUser").Value = "Y", vbChecked, vbUnchecked)
    Me.ckUpdateDbPassword = IIf(rsSysCtrl("UpdateDBPassword").Value = "Y", vbChecked, vbUnchecked)
    Me.Ck_MasterItem_Option = IIf(rsSysCtrl("MasterItemOption").Value = "Y", vbChecked, vbUnchecked)
    
    If IsNull(rsSysCtrl("LastOrderGenerationProcess").Value) Then
       Me.tdateLast_OrderGen = CDate(#1/1/1900#) 'set to some old date
    Else
        Me.tdateLast_OrderGen = rsSysCtrl("LastOrderGenerationProcess").Value
    End If
    If IsNull(rsSysCtrl("LastOrderGenerationProcess").Value) Then
        Me.ttimeLast_OrderGen.Text = "00:00:00" 'set to default value
    Else
        Me.ttimeLast_OrderGen = rsSysCtrl("LastOrderGenerationProcess").Value
    End If
    ' Allocation
    ' Need Determination
    ' Disable the restock profile options that are dependent on needdetermination option 3
    If optAllocNeed(2).Value = False Then
        frRestockProfile.Enabled = False
        For IndexCounter = 0 To (optAllocNeedRestockProfile.Count - 1)
            optAllocNeedRestockProfile(IndexCounter).Enabled = False
            optAllocNeedRestockProfile(IndexCounter).Value = False
        Next
    End If
    
    Select Case rsSysCtrl!AllocNeedDetermination
    Case 0      'Use only the restock profile
        Me.optAllocNeed(0).Value = True
    
    Case 1      'Use only the restock order
        Me.optAllocNeed(1).Value = True
        
    Case 2, 3   'Use both the restock profile and the restock order
        Me.optAllocNeed(2).Value = True
        
        'Choose which restock profile qty should be used in allocation
        'Options for restock profile qty: Standard (or Target) Qty; On-hand (or Available) Qty
        frRestockProfile.Enabled = True
        For IndexCounter = 0 To (optAllocNeedRestockProfile.Count - 1)
            optAllocNeedRestockProfile(IndexCounter).Enabled = True
        Next
        If rsSysCtrl!AllocNeedDetermination = 2 Then
            optAllocNeedRestockProfile(0).Value = True
        ElseIf rsSysCtrl!AllocNeedDetermination = 3 Then
            optAllocNeedRestockProfile(1).Value = True
        End If
    End Select
    ' Exception Processing
    Select Case rsSysCtrl!AllocExceptionProcess
    Case 0  'Apply exception trigger per order
        Me.optAllocExceptions(0).Value = True
        Label1(15).Enabled = True
        txtAllocExceptionPct.Enabled = True
    Case 1  ''Apply exception trigger per destination location
        Me.optAllocExceptions(1).Value = True
        Label1(15).Enabled = False
        txtAllocExceptionPct.Enabled = False
    Case 2  ''Apply exception trigger per line item
        Me.optAllocExceptions(2).Value = True
        Label1(15).Enabled = False
        txtAllocExceptionPct.Enabled = False
    End Select
    If IsNull(rsSysCtrl!AllocExceptionPct) Then
        txtAllocExceptionPct.Value = 0
    Else
    txtAllocExceptionPct.Value = rsSysCtrl!AllocExceptionPct
    End If
    'Prepack conversions
    Me.ckPrepackEnabled = IIf(rsSysCtrl!AllocPrepackEnabled = vbTrue, vbChecked, vbUnchecked)
    ' End Allocation values
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(Refresh_Form)", Err.Description
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::Refresh_Form", Now, gDRGeneralError, True, Err
End Function

Private Function SysCtrl_Update(rs As ADODB.Recordset)
On Error GoTo ErrorHandler

    rs("cname").Value = Me.txtCName.Text
    rs("address1").Value = Me.txtAddress1.Text
    rs("address2").Value = Me.txtAddress2.Text
    rs("address3").Value = Me.txtAddress3.Text
    rs("city").Value = Me.txtCity.Text
    rs("state").Value = Me.txtState.Text
    rs("cozip").Value = Me.txtCoZip.Text
    rs("dft_minrchpct").Value = Me.txtDft_MinRchPct.Value
    rs("dft_bestrchpct").Value = Me.txtDft_BestRchPct.Value
    rs("dxpath").Value = Me.txtDXPath.Text
    rs("dft_postatus").Value = Me.dcDft_POStatus.Text
    rs("dxpo_option").Value = Me.dcDxPO_Option.Text
    rs("SeasSmoothingPds").Value = Me.txtSeasSmoothingPds.Value
    rs("cursaversion").Value = Me.txtCurSaVersion.Value
    rs("retain_sd_opt").Value = Me.ckRetain_SD_Opt.Value
    rs("calc_perform").Value = Me.ckCalc_Perfrom.Value
    rs("Vendor_Sizing").Value = IIf(Me.ckVendor_Sizing = vbChecked, "Y", "N")
    rs("UpdateGoalsOption").Value = IIf(Me.ckUpdateGoalsOption = vbChecked, "Y", "N")
    rs("MDCOption").Value = IIf(Me.ckMDCOption = vbChecked, "Y", "N")
    rsSysCtrl!ProductionConstraint = IIf(Me.ckProdConstraint = vbChecked, "Y", "N")
    rsSysCtrl!AllowNegativeAvailQty = IIf(Me.ckAllowNegativeAvailQty = vbChecked, "Y", "N")
    rsSysCtrl!ProdConstraintGrtZero = IIf(Me.ckGRTZeroRSOQinPRODCONST = vbChecked, "Y", "N")
    rs("ColonOption").Value = IIf(Me.ckColonOption = vbChecked, "Y", "N")
    rs("GridAutoSizeOption").Value = IIf(Me.ckGridAutoSizeOption = vbChecked, "Y", "N")
    rs("DateFormat").Value = Me.dcDateFormat.Text
    gDateFormat = Me.dcDateFormat.Text
    rs("TimeFormat").Value = Me.dcTimeFormat.Text
    gTimeFormat = Me.dcTimeFormat.Text
    rs("dft_LangId").Value = UCase(Me.dcLangID.Text)
     
    rs("dxpath_out").Value = Me.txtDxPath_Out.Text
    rs("dxpath_backup").Value = Me.txtDxPath_Backup.Text
    rs("logfile").Value = Me.txtLogFile.Text
    rs("AIMBatchPath").Value = Me.txtAIMBatchPath.Text
    
    rs("byidsource").Value = Me.dcByIdSource.Text
    rs("kfactor").Value = Me.txtKFactor.Value
    rs("minroai").Value = Me.txtMinROAI.Value
    rs("priority_min_dser").Value = Me.txtPriority_Min_Dser.Value
    rs("priority_min_velcode").Value = Me.txtPriority_Min_Vel.Text
    rs("addl_ltdays").Value = Me.txtAddl_LTDays.Value
    rs("lt_filter_pct").Value = Me.txtLt_Filter_Pct.Value
    
    rs("UpdateVnLT").Value = IIf(Me.ckUpdateVnLT = vbChecked, "Y", "N")
    rs("VnLTPctChgFilter").Value = Me.txtVnLTPctChgFilter.Value
    rs("VnLTDaysChgFilter").Value = Me.txtVnLTDaysChgFilter.Value
    rs("UnicodeOption").Value = IIf(Me.ckUnicodeOption = vbChecked, "Y", "N")
    rs("Last_POSeq").Value = Me.txtLast_POSeq.Value
    rs("ClassOption").Value = Me.txtClassOption.Value
    rs("AvgInvAlpha").Value = Me.txtAvgInvAlpha.Value
    rs("NumOffDaysBehindFromCurrentDate").Value = Me.txtNumDaysForLogMsgs.Value
    
    rs("Int_MinPctZero").Value = Me.txtInt_MinPctZero.Value
    rs("Int_MaxPctZero").Value = Me.txtInt_MaxPctZero.Value
    rs("Int_SeasonalityIndex").Value = Me.txtInt_SeasonalityIndex.Value
    rs("Int_LookBackPds").Value = Me.txtInt_LookBackPds.Value
    rs("Int_SrvLvl").Value = Me.txtInt_SrvLvl.Value
    rs("Int_Enabled").Value = IIf(Me.ckInt_Enabled = vbChecked, "Y", "N")
    rs("Int_SrvLvlOverrideFlag").Value = IIf(Me.ckInt_SrvLvlOverride = vbChecked, "Y", "N")
    rs("CarryForwardRounding").Value = IIf(Me.CkCarryForwardRounding = vbChecked, "Y", "N")
    
    rs("WantErrorsTobeLoggedToDatabase").Value = IIf(Me.chkLogOption = vbChecked, 1, 0)
    
    
    
    'sujit
    rs("CreateDBUser").Value = IIf(Me.ckCreateDBUser = vbChecked, "Y", "N")
    rs("UpdateDBPassword").Value = IIf(Me.ckUpdateDbPassword = vbChecked, "Y", "N")
    rs("MasterItemOption").Value = IIf(Me.Ck_MasterItem_Option = vbChecked, "Y", "N")
    
    
    If Me.ckColonOption = vbChecked Then
        gColonOption = True
    ElseIf Me.ckColonOption = vbUnchecked Then
        gColonOption = False
    Else
        gColonOption = False
    End If
    
    If Me.ckGridAutoSizeOption = vbChecked Then
        gGridAutoSizeOption = True
    ElseIf Me.ckColonOption = vbUnchecked Then
        gGridAutoSizeOption = False
    Else
        gGridAutoSizeOption = False
    End If

    ' Allocation
    ' Need Determination
    If optAllocNeed(0).Value = True Then
        rs!AllocNeedDetermination = 0
    ElseIf optAllocNeed(1).Value = True Then
        rs!AllocNeedDetermination = 1
    ElseIf optAllocNeed(2).Value = True Then
        If optAllocNeedRestockProfile(0).Value = True Then
            rs!AllocNeedDetermination = 2
        ElseIf optAllocNeedRestockProfile(1).Value = True Then
            rs!AllocNeedDetermination = 3
        End If
    End If
    ' Exception Processing
    If optAllocExceptions(0).Value = True Then
        rs!AllocExceptionProcess = 0
        rs!AllocExceptionPct = txtAllocExceptionPct.Value
    ElseIf optAllocExceptions(1).Value = True Then
        rs!AllocExceptionProcess = 1
        rs!AllocExceptionPct = 0
    ElseIf optAllocExceptions(2).Value = True Then
        rs!AllocExceptionProcess = 2
        rs!AllocExceptionPct = 0
    End If
    'Prepack conversions
    rs!AllocPrepackEnabled = Me.ckPrepackEnabled.Value
    ' End Allocation
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(SysCtrl_Update)", Err.Description
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::SysCtrl_Update", Now, gDRGeneralError, True, Err
End Function

Private Sub dcByIdSource_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcByIdSource.Columns(0).Caption = getTranslationResource("Code")
    Me.dcByIdSource.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcByIdSource.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcByIdSource.Columns(0).Width = 720
    
    Me.dcByIdSource.Columns(1).Caption = getTranslationResource("Description")
    Me.dcByIdSource.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcByIdSource.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcByIdSource.Columns(1).Width = 2500
    
    Me.dcByIdSource.AddItem "D" + vbTab + getTranslationResource("Item Defaults Table")
    Me.dcByIdSource.AddItem "L" + vbTab + getTranslationResource("Location Table")
    Me.dcByIdSource.AddItem "V" + vbTab + getTranslationResource("Vendor Table")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcByIdSource, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcByIDSource.Columns.Count - 1
'        dcByIDSource.Columns(IndexCounter).HasHeadBackColor = True
'        dcByIDSource.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcByIdSource_InitColumnProps)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcByIdSource_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDft_POStatus_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcDft_POStatus.Columns(0).Caption = getTranslationResource("Code")
    Me.dcDft_POStatus.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcDft_POStatus.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDft_POStatus.Columns(0).Width = 720
    
    Me.dcDft_POStatus.Columns(1).Caption = getTranslationResource("Description")
    Me.dcDft_POStatus.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcDft_POStatus.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDft_POStatus.Columns(1).Width = 2500
    
    Me.dcDft_POStatus.AddItem "P" + vbTab + getTranslationResource("Planned")
    Me.dcDft_POStatus.AddItem "R" + vbTab + getTranslationResource("Released")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDft_POStatus, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcDft_POStatus.Columns.Count - 1
'        dcDft_POStatus.Columns(IndexCounter).HasHeadBackColor = True
'        dcDft_POStatus.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDft_POStatus_InitColumnProps)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcDft_POStatus_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDxPO_Option_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcDxPO_Option.Columns(0).Caption = getTranslationResource("Code")
    Me.dcDxPO_Option.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcDxPO_Option.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDxPO_Option.Columns(0).Width = 720
    
    Me.dcDxPO_Option.Columns(1).Caption = getTranslationResource("Description")
    Me.dcDxPO_Option.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcDxPO_Option.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDxPO_Option.Columns(1).Width = 4500
    
    Me.dcDxPO_Option.AddItem "L" + vbTab + _
                    getTranslationResource("Generate P.O. Data Interface Files by Location")
    Me.dcDxPO_Option.AddItem "S" + vbTab + _
                    getTranslationResource("Generate a Single P.O. Data Interface File")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDxPO_Option, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcDxPO_Option.Columns.Count - 1
'        dcDxPO_Option.Columns(IndexCounter).HasHeadBackColor = True
'        dcDxPO_Option.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDxPO_Option_InitColumnProps)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcDxPO_Option_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLangId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLangID.Columns(0).Caption = getTranslationResource("Language ID")
    Me.dcLangID.Columns(0).Width = 1000
        
    Me.dcLangID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLangID.Columns(1).Width = 2500
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLangID, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcLangID.Columns.Count - 1
'        dcLangID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLangID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangId_InitColumnProps)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcLangId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDateFormat_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcDateFormat.Columns(0).Caption = getTranslationResource("Date Format")
    Me.dcDateFormat.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcDateFormat.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDateFormat.Columns(0).Width = 1500
    
    Me.dcDateFormat.Columns(1).Caption = getTranslationResource("Example")
    Me.dcDateFormat.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcDateFormat.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcDateFormat.Columns(1).Width = 1500
    
    Me.dcDateFormat.AddItem DATEFORMAT_MMDDYYYY + vbTab + Format(Now, DATEFORMAT_MMDDYYYY)
    Me.dcDateFormat.AddItem DATEFORMAT_MMDDYYYY_H + vbTab + Format(Now, DATEFORMAT_MMDDYYYY_H)
    Me.dcDateFormat.AddItem DATEFORMAT_MMMDDYYYY + vbTab + Format(Now, DATEFORMAT_MMMDDYYYY)
    Me.dcDateFormat.AddItem DATEFORMAT_MMMDDYYYY_H + vbTab + Format(Now, DATEFORMAT_MMMDDYYYY_H)
    
    Me.dcDateFormat.AddItem DATEFORMAT_DDMMYYYY + vbTab + Format(Now, DATEFORMAT_DDMMYYYY)
    Me.dcDateFormat.AddItem DATEFORMAT_DDMMYYYY_H + vbTab + Format(Now, DATEFORMAT_DDMMYYYY_H)
    Me.dcDateFormat.AddItem DATEFORMAT_DDMMMYYYY + vbTab + Format(Now, DATEFORMAT_DDMMMYYYY)
    Me.dcDateFormat.AddItem DATEFORMAT_DDMMMYYYY_H + vbTab + Format(Now, DATEFORMAT_DDMMMYYYY_H)
        
    Me.dcDateFormat.AddItem DATEFORMAT_YYYYMMDD + vbTab + Format(Now, DATEFORMAT_YYYYMMDD)
    Me.dcDateFormat.AddItem DATEFORMAT_YYYYMMDD_H + vbTab + Format(Now, DATEFORMAT_YYYYMMDD_H)
    Me.dcDateFormat.AddItem DATEFORMAT_YYYYMMMDD + vbTab + Format(Now, DATEFORMAT_YYYYMMMDD)
    Me.dcDateFormat.AddItem DATEFORMAT_YYYYMMMDD_H + vbTab + Format(Now, DATEFORMAT_YYYYMMMDD_H)
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDateFormat, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcDateFormat.Columns.Count - 1
'        dcDateFormat.Columns(IndexCounter).HasHeadBackColor = True
'        dcDateFormat.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDateFormat_InitColumnProps)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcDateFormat_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcTimeFormat_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcTimeFormat.Columns(0).Caption = getTranslationResource("Time Format")
    Me.dcTimeFormat.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcTimeFormat.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcTimeFormat.Columns(0).Width = 1800
    
    Me.dcTimeFormat.Columns(1).Caption = getTranslationResource("Example")
    Me.dcTimeFormat.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcTimeFormat.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcTimeFormat.Columns(1).Width = 1800
    
    Me.dcTimeFormat.AddItem TIMEFORMAT_24HOUR + vbTab + Format(Time, TIMEFORMAT_24HOUR)
    Me.dcTimeFormat.AddItem TIMEFORMAT_12HOUR_SYSTEM + vbTab + Format(Time, TIMEFORMAT_12HOUR_SYSTEM)
    Me.dcTimeFormat.AddItem TIMEFORMAT_12HOUR_UCASEAMPM + vbTab + Format(Time, TIMEFORMAT_12HOUR_UCASEAMPM)
    Me.dcTimeFormat.AddItem TIMEFORMAT_12HOUR_LCASEAMPM + vbTab + Format(Time, TIMEFORMAT_12HOUR_LCASEAMPM)
    Me.dcTimeFormat.AddItem TIMEFORMAT_12HOUR_UCASEAP + vbTab + Format(Time, TIMEFORMAT_12HOUR_UCASEAP)
    Me.dcTimeFormat.AddItem TIMEFORMAT_12HOUR_LCASEAP + vbTab + Format(Time, TIMEFORMAT_12HOUR_LCASEAP)
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDateFormat, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To v.Columns.Count - 1
'        dcTimeFormat.Columns(IndexCounter).HasHeadBackColor = True
'        dcTimeFormat.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcTimeFormat_InitColumnProps)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcTimeFormat_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLangID_LostFocus()
On Error GoTo ErrorHandler
    
    Me.dcLangID.Text = UCase(Me.dcLangID.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangID_LostFocus)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::dcLangID_LostFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim strYes As String
    
    'Display status message
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05500")
    If StrComp(strMessage, "STATMSG05500") = 0 Then strMessage = "Initializing System Control Table Maintenance..."
    Write_Message strMessage

'    Me.Ck_MasterItem_Option.Enabled = False
'    Me.Ck_MasterItem_Option.Visible = False
'    Me.Ck_MasterItem_Option.Value = vbUnchecked

    'By default we will log the errors to database. -gajjela
    Me.chkLogOption.Value = vbChecked
    
    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize the System Control Table Record Set
    GetSysCtrlRecord
    
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbNavigation.Tools("ID_Save").Enabled = False
    Else
        Me.tbNavigation.Tools("ID_Save").Enabled = True
    End If
    
    PopulateLangIDs
    
    'Make the spin button visible
    Me.txtInt_SeasonalityIndex.Spin.Visible = 1
    Me.txtAddl_LTDays.Spin.Visible = 1
    Me.txtAvgInvAlpha.Spin.Visible = 1
    Me.txtDft_BestRchPct.Spin.Visible = 1
    Me.txtDft_MinRchPct.Spin.Visible = 1
    Me.txtInt_MaxPctZero.Spin.Visible = 1
    Me.txtInt_MinPctZero.Spin.Visible = 1
    Me.txtInt_SrvLvl.Spin.Visible = 1
    Me.txtKFactor.Spin.Visible = 1
    Me.txtLt_Filter_Pct.Spin.Visible = 1
    Me.txtMinROAI.Spin.Visible = 1
    Me.txtPriority_Min_Dser.Spin.Visible = 1
    Me.txtVnLTPctChgFilter.Spin.Visible = 1
    Me.txtLast_POSeq.Spin.Visible = 1
    Me.txtVnLTDaysChgFilter.Spin.Visible = 1
    Me.txtCurSaVersion.Spin.Visible = 1
    Me.txtSeasSmoothingPds.Spin.Visible = 1
    Me.txtClassOption.Spin.Visible = 1
    Me.txtInt_LookBackPds.Spin.Visible = 1
    Me.txtAllocExceptionPct.Spin.Visible = 1
    Me.txtNumDaysForLogMsgs.Spin.Visible = 1
    
    
    
    'Bind form to System Control Record Set
    Refresh_Form
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If Not (dcLangID.DataSourceList Is Nothing) Then Set dcLangID.DataSourceList = Nothing
    
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_SysCtrolMaintenance:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub optAllocExceptions_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    
    Select Case Index
        Case 0
        'Enable the order exception percent spin control
        Label1(15).Enabled = True
        txtAllocExceptionPct.Enabled = True
        
        Case 1, 2
        'Disable the order exception percent spin control
        Label1(15).Enabled = False
        txtAllocExceptionPct.Enabled = False
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optAllocExceptions(" & CStr(Index) & ")_Click)"
    f_HandleErr , , , "AIM_SysCtrolMaintenance:::(optAllocExceptions(" & CStr(Index) & ")_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optAllocNeed_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    
    Select Case Index
    Case 0, 1
        'Disable the restock profile options that are dependent on needdetermination option 3
        frRestockProfile.Enabled = False
        For IndexCounter = 0 To (optAllocNeedRestockProfile.Count - 1)
            optAllocNeedRestockProfile(IndexCounter).Enabled = False
        Next

    Case 2
        'Enable the restock profile options that are dependent on needdetermination option 3
        frRestockProfile.Enabled = True
        For IndexCounter = 0 To (optAllocNeedRestockProfile.Count - 1)
            optAllocNeedRestockProfile(IndexCounter).Enabled = True
        Next
        'Set restock profile default to standard(target) qty
        optAllocNeedRestockProfile(0).Value = True
    
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optAllocNeed(" & CStr(Index) & ")_Click)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::optAllocNeed(" & CStr(Index) & ")_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim LcId As String
    Dim RtnCode As Integer
    Dim strText As String
    Dim strMessage As String

    Cn.Errors.Clear
    
    Write_Message ""
    
    SysCtrl_Update rsSysCtrl
    
    'Alert user to possible change
    Select Case Tool.ID
    
    Case "ID_Close"
        If gAccessLvl <> 1 And DataChanged(rsSysCtrl, adAffectCurrent) > 0 Then
            strText = getTranslationResource(AIM_SysCtrlMaintenance.Caption)
            strMessage = getTranslationResource("MSGBOX05503")
            If StrComp(strMessage, "MSGBOX05503") = 0 Then strMessage = "Abandon changes to current record?"
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
            If RtnCode = vbYes Then
                rsSysCtrl.CancelUpdate
            Else
                rsSysCtrl.Update
            End If

        End If

    End Select

    'Navigate
    Select Case Tool.ID
    
    Case "ID_Refresh"
        rsSysCtrl.CancelUpdate
        GetSysCtrlRecord
        
        Refresh_Form
    
    Case "ID_Save"
        rsSysCtrl.Update

        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG05500")
            If StrComp(strMessage, "ERRMSG05500") = 0 Then strMessage = "Error Updating System Control Table."
            ADOErrorHandler Cn, strMessage
        Else
            strMessage = getTranslationResource("STATMSG05501")
            If StrComp(strMessage, "STATMSG05501") = 0 Then strMessage = "System Control Table successfully updated."
            Write_Message strMessage
        End If

    Case "ID_Close"
        Unload Me
        Exit Sub

    End Select

    Refresh_Form        'Refresh the form
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtPriority_Min_Vel_Change()
On Error GoTo ErrorHandler

    txtPriority_Min_Vel.Text = UCase(txtPriority_Min_Vel.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtPriority_Min_Vel_Change)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::txtPriority_Min_Vel_Change", Now, gDRGeneralError, True, Err
End Sub

Private Function GetSysCtrlRecord()
On Error GoTo ErrorHandler

    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        
    End With
    
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    
    End With
    
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    
    If Err.Number = 3219 Then
        Resume Next
    Else
        'Err.Raise Err.Number, Err.source & "(GetSysCtrlRecord)", Err.Description
        f_HandleErr , , , "AIM_SysCtrolMaintenance:::GetSysCtrlRecord", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function PopulateLangIDs() As Long
On Error GoTo ErrorHandler

    Dim rsLangId As ADODB.Recordset
    Dim AIM_AIMLanguage_List_Sp As ADODB.Command
    Dim strYes As String

    'Build the Language List
    Set AIM_AIMLanguage_List_Sp = New ADODB.Command
    With AIM_AIMLanguage_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMLanguage_List_Sp"
        .Parameters.Refresh
    End With

    'Initialize the language Record Set
    Set rsLangId = New ADODB.Recordset
    With rsLangId
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Only get the enabled Languages, marked with "Y"
    strYes = "Y"
    AIM_AIMLanguage_List_Sp.Parameters("@EnabledOption").Value = strYes
    rsLangId.Open AIM_AIMLanguage_List_Sp
    
    'Bind the Data Combo Drop Downs
    If f_IsRecordsetOpenAndPopulated(rsLangId) Then
        rsLangId.MoveFirst
        Do Until rsLangId.eof
            Me.dcLangID.AddItem rsLangId("LangID").Value & vbTab & rsLangId!LangDesc
            rsLangId.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsLangId) Then rsLangId.Close
    Set rsLangId = Nothing
    If Not (AIM_AIMLanguage_List_Sp Is Nothing) Then Set AIM_AIMLanguage_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLanguage_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetOpenAndPopulated(rsLangId) Then rsLangId.Close
    Set rsLangId = Nothing
    If AIM_AIMLanguage_List_Sp.State <> adStateOpen Then Set AIM_AIMLanguage_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLanguage_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateLangIDs)"
     f_HandleErr , , , "AIM_SysCtrolMaintenance:::PopulateLangIDs", Now, gDRGeneralError, True, Err
End Function

