VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{562E3E04-2C31-4ECE-83F4-4017EEE51D40}#8.0#0"; "todg8.ocx"
Begin VB.Form AIM_ForecastFilter 
   Caption         =   " SSA DR Forecast Item Filter"
   ClientHeight    =   9555
   ClientLeft      =   60
   ClientTop       =   330
   ClientWidth     =   11355
   LinkTopic       =   "Form2"
   MinButton       =   0   'False
   ScaleHeight     =   9555
   ScaleWidth      =   11355
   StartUpPosition =   2  'CenterScreen
   Begin VB.CommandButton cmdClearAll 
      Caption         =   "Clear &All"
      Height          =   345
      Left            =   120
      Style           =   1  'Graphical
      TabIndex        =   30
      Top             =   90
      Width           =   1485
   End
   Begin VB.Frame frmOptions 
      Caption         =   "Data for selected criterion"
      Height          =   6495
      Left            =   3935
      TabIndex        =   0
      Top             =   435
      Width           =   7335
      Begin TrueOleDBGrid80.TDBGrid dgOptions 
         Height          =   3255
         Left            =   120
         TabIndex        =   3
         Top             =   600
         Width           =   7095
         _ExtentX        =   12515
         _ExtentY        =   5741
         _LayoutType     =   4
         _RowHeight      =   -2147483647
         _WasPersistedAsPixels=   0
         Columns(0)._VlistStyle=   0
         Columns(0)._MaxComboItems=   5
         Columns(0).DataField=   ""
         Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
         Columns.Count   =   1
         Splits(0)._UserFlags=   0
         Splits(0).RecordSelectorWidth=   476
         Splits(0)._SavedRecordSelectors=   -1  'True
         Splits(0).DividerColor=   13160660
         Splits(0).SpringMode=   0   'False
         Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
         Splits(0)._ColumnProps(0)=   "Columns.Count=1"
         Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
         Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
         Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
         Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
         Splits.Count    =   1
         PrintInfos(0)._StateFlags=   3
         PrintInfos(0).Name=   "piInternal 0"
         PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=MS Sans Serif"
         PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=MS Sans Serif"
         PrintInfos(0).PageHeaderHeight=   0
         PrintInfos(0).PageFooterHeight=   0
         PrintInfos.Count=   1
         DataMode        =   4
         DefColWidth     =   0
         HeadLines       =   1
         FootLines       =   1
         Caption         =   "Available values (linked to criterion selected on the left)"
         MultipleLines   =   0
         CellTipsWidth   =   0
         DeadAreaBackColor=   13160660
         RowDividerColor =   13160660
         RowSubDividerColor=   13160660
         DirectionAfterEnter=   1
         DirectionAfterTab=   1
         MaxRows         =   250000
         ViewColumnCaptionWidth=   0
         ViewColumnWidth =   0
         _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
         _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
         _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
         _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
         _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=0,.bold=0,.fontsize=825,.italic=0"
         _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
         _StyleDefs(5)   =   ":id=0,.fontname=MS Sans Serif"
         _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33"
         _StyleDefs(7)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
         _StyleDefs(8)   =   "HeadingStyle:id=2,.parent=1,.namedParent=34"
         _StyleDefs(9)   =   "FooterStyle:id=3,.parent=1,.namedParent=35"
         _StyleDefs(10)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
         _StyleDefs(11)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
         _StyleDefs(12)  =   "EditorStyle:id=7,.parent=1"
         _StyleDefs(13)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
         _StyleDefs(14)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
         _StyleDefs(15)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
         _StyleDefs(16)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
         _StyleDefs(17)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
         _StyleDefs(18)  =   "Splits(0).Style:id=13,.parent=1"
         _StyleDefs(19)  =   "Splits(0).CaptionStyle:id=22,.parent=4"
         _StyleDefs(20)  =   "Splits(0).HeadingStyle:id=14,.parent=2"
         _StyleDefs(21)  =   "Splits(0).FooterStyle:id=15,.parent=3"
         _StyleDefs(22)  =   "Splits(0).InactiveStyle:id=16,.parent=5"
         _StyleDefs(23)  =   "Splits(0).SelectedStyle:id=18,.parent=6"
         _StyleDefs(24)  =   "Splits(0).EditorStyle:id=17,.parent=7"
         _StyleDefs(25)  =   "Splits(0).HighlightRowStyle:id=19,.parent=8"
         _StyleDefs(26)  =   "Splits(0).EvenRowStyle:id=20,.parent=9"
         _StyleDefs(27)  =   "Splits(0).OddRowStyle:id=21,.parent=10"
         _StyleDefs(28)  =   "Splits(0).RecordSelectorStyle:id=23,.parent=11"
         _StyleDefs(29)  =   "Splits(0).FilterBarStyle:id=24,.parent=12"
         _StyleDefs(30)  =   "Splits(0).Columns(0).Style:id=32,.parent=13"
         _StyleDefs(31)  =   "Splits(0).Columns(0).HeadingStyle:id=29,.parent=14"
         _StyleDefs(32)  =   "Splits(0).Columns(0).FooterStyle:id=30,.parent=15"
         _StyleDefs(33)  =   "Splits(0).Columns(0).EditorStyle:id=31,.parent=17"
         _StyleDefs(34)  =   "Named:id=33:Normal"
         _StyleDefs(35)  =   ":id=33,.parent=0"
         _StyleDefs(36)  =   "Named:id=34:Heading"
         _StyleDefs(37)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
         _StyleDefs(38)  =   ":id=34,.wraptext=-1"
         _StyleDefs(39)  =   "Named:id=35:Footing"
         _StyleDefs(40)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
         _StyleDefs(41)  =   "Named:id=36:Selected"
         _StyleDefs(42)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
         _StyleDefs(43)  =   "Named:id=37:Caption"
         _StyleDefs(44)  =   ":id=37,.parent=34,.alignment=2"
         _StyleDefs(45)  =   "Named:id=38:HighlightRow"
         _StyleDefs(46)  =   ":id=38,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
         _StyleDefs(47)  =   "Named:id=39:EvenRow"
         _StyleDefs(48)  =   ":id=39,.parent=33,.bgcolor=&HFFFF00&"
         _StyleDefs(49)  =   "Named:id=40:OddRow"
         _StyleDefs(50)  =   ":id=40,.parent=33"
         _StyleDefs(51)  =   "Named:id=41:RecordSelector"
         _StyleDefs(52)  =   ":id=41,.parent=34"
         _StyleDefs(53)  =   "Named:id=42:FilterBar"
         _StyleDefs(54)  =   ":id=42,.parent=33"
      End
      Begin VB.CommandButton cmdClearLookup 
         Caption         =   "Default&s"
         Height          =   345
         Left            =   3840
         Style           =   1  'Graphical
         TabIndex        =   23
         Top             =   6000
         Width           =   1335
      End
      Begin VB.CommandButton cmdLookup 
         Caption         =   "Re-display &Options"
         Height          =   345
         Left            =   5400
         Style           =   1  'Graphical
         TabIndex        =   22
         Top             =   6000
         Width           =   1695
      End
      Begin VB.Frame frmLookup 
         Caption         =   "Sub-filter for selected criterion"
         Height          =   1935
         Left            =   120
         TabIndex        =   4
         Top             =   3960
         Visible         =   0   'False
         Width           =   7095
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   2
            Left            =   1920
            TabIndex        =   10
            Top             =   640
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":0000
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":006C
            Key             =   "AIM_ForecastFilter.frx":008A
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Vendor ID"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   0
            Left            =   1920
            TabIndex        =   6
            Top             =   240
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":00CE
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":013A
            Key             =   "AIM_ForecastFilter.frx":0158
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Location ID"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   4
            Left            =   1920
            TabIndex        =   14
            Top             =   1040
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":019C
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":0208
            Key             =   "AIM_ForecastFilter.frx":0226
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Item ID"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   1
            Left            =   5520
            TabIndex        =   8
            Top             =   240
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":026A
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":02D6
            Key             =   "AIM_ForecastFilter.frx":02F4
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Location Name"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   3
            Left            =   5520
            TabIndex        =   12
            Top             =   640
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":0338
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":03A4
            Key             =   "AIM_ForecastFilter.frx":03C2
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Assortment"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   5
            Left            =   5520
            TabIndex        =   16
            Top             =   1040
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":0406
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":0472
            Key             =   "AIM_ForecastFilter.frx":0490
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Item Desc"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtLkUp 
            Height          =   345
            Index           =   6
            Left            =   1920
            TabIndex        =   18
            Top             =   1440
            Width           =   1485
            _Version        =   65536
            _ExtentX        =   2619
            _ExtentY        =   609
            Caption         =   "AIM_ForecastFilter.frx":04D4
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_ForecastFilter.frx":0540
            Key             =   "AIM_ForecastFilter.frx":055E
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   -1
            ScrollBars      =   2
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   "Buyer ID"
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   1
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
            Height          =   345
            Left            =   3750
            TabIndex        =   19
            Top             =   1440
            Width           =   3255
            DataFieldList   =   "Column 0"
            _Version        =   196617
            DataMode        =   2
            Cols            =   2
            BackColorOdd    =   12648447
            Columns(0).Width=   3200
            _ExtentX        =   5741
            _ExtentY        =   609
            _StockProps     =   93
            ForeColor       =   -2147483640
            BackColor       =   -2147483643
            DataFieldToDisplay=   "Column 1"
         End
         Begin VB.Label lblLkUp 
            Caption         =   "Buyer ID"
            Height          =   300
            Index           =   6
            Left            =   120
            TabIndex        =   17
            Top             =   1485
            Width           =   1695
         End
         Begin VB.Label lblLkUp 
            Caption         =   "Location Name"
            Height          =   300
            Index           =   1
            Left            =   3720
            TabIndex        =   7
            Top             =   285
            Width           =   1695
         End
         Begin VB.Label lblLkUp 
            Caption         =   "VnID"
            Height          =   300
            Index           =   2
            Left            =   120
            TabIndex        =   9
            Top             =   685
            Width           =   1695
         End
         Begin VB.Label lblLkUp 
            Caption         =   "LcID"
            Height          =   300
            Index           =   0
            Left            =   120
            TabIndex        =   5
            Top             =   285
            Width           =   1695
         End
         Begin VB.Label lblLkUp 
            Caption         =   "Item"
            Height          =   300
            Index           =   4
            Left            =   120
            TabIndex        =   13
            Top             =   1085
            Width           =   1695
         End
         Begin VB.Label lblLkUp 
            Caption         =   "Assort"
            Height          =   300
            Index           =   3
            Left            =   3720
            TabIndex        =   11
            Top             =   685
            Width           =   1695
         End
         Begin VB.Label lblLkUp 
            Caption         =   "Item Description"
            Height          =   300
            Index           =   5
            Left            =   3720
            TabIndex        =   15
            Top             =   1085
            Width           =   1695
         End
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   2040
         TabIndex        =   21
         Top             =   6000
         Width           =   1485
         _Version        =   65536
         _ExtentX        =   2619
         _ExtentY        =   609
         Calculator      =   "AIM_ForecastFilter.frx":05A2
         Caption         =   "AIM_ForecastFilter.frx":05C2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastFilter.frx":062E
         Keys            =   "AIM_ForecastFilter.frx":064C
         Spin            =   "AIM_ForecastFilter.frx":0696
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#########0;-#########0;;200"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#,###,###,##0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   2147483647
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   56557569
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   1479671813
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcMultiCompare 
         Height          =   345
         Left            =   1920
         TabIndex        =   2
         Top             =   240
         Width           =   3255
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5741
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin VB.Label Label1 
         Caption         =   "Comparison operator"
         Height          =   300
         Left            =   120
         TabIndex        =   1
         Top             =   285
         Width           =   1695
      End
      Begin VB.Label Label8 
         Caption         =   "Maximum rows"
         Height          =   300
         Left            =   240
         TabIndex        =   20
         Top             =   6045
         Width           =   1695
      End
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return"
      Height          =   345
      Left            =   8045
      Style           =   1  'Graphical
      TabIndex        =   27
      Top             =   90
      Width           =   1485
   End
   Begin VB.CommandButton cmdRestore 
      Caption         =   "Restore &Originals"
      Height          =   345
      Left            =   1625
      Style           =   1  'Graphical
      TabIndex        =   29
      Top             =   90
      Width           =   2565
   End
   Begin VB.CommandButton cmdApply 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   9785
      Style           =   1  'Graphical
      TabIndex        =   26
      Top             =   90
      Width           =   1485
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   6455
      Style           =   1  'Graphical
      TabIndex        =   28
      Top             =   90
      Width           =   1485
   End
   Begin TrueOleDBGrid80.TDBGrid dgResults 
      Height          =   2415
      Left            =   3930
      TabIndex        =   24
      TabStop         =   0   'False
      Top             =   7050
      Width           =   7335
      _ExtentX        =   12938
      _ExtentY        =   4260
      _LayoutType     =   4
      _RowHeight      =   -2147483647
      _WasPersistedAsPixels=   0
      Columns(0)._VlistStyle=   0
      Columns(0)._MaxComboItems=   5
      Columns(0).DataField=   ""
      Columns(0)._PropDict=   "_MaxComboItems,516,2;_VlistStyle,514,3"
      Columns.Count   =   1
      Splits(0)._UserFlags=   0
      Splits(0).RecordSelectorWidth=   476
      Splits(0)._SavedRecordSelectors=   -1  'True
      Splits(0).DividerColor=   13160660
      Splits(0).SpringMode=   0   'False
      Splits(0)._PropDict=   "_ColumnProps,515,0;_UserFlags,518,3"
      Splits(0)._ColumnProps(0)=   "Columns.Count=1"
      Splits(0)._ColumnProps(1)=   "Column(0).Width=2725"
      Splits(0)._ColumnProps(2)=   "Column(0).DividerColor=0"
      Splits(0)._ColumnProps(3)=   "Column(0)._WidthInPix=2646"
      Splits(0)._ColumnProps(4)=   "Column(0).Order=1"
      Splits.Count    =   1
      PrintInfos(0)._StateFlags=   3
      PrintInfos(0).Name=   "piInternal 0"
      PrintInfos(0).PageHeaderFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=MS Sans Serif"
      PrintInfos(0).PageFooterFont=   "Size=8.25,Charset=0,Weight=400,Underline=0,Italic=0,Strikethrough=0,Name=MS Sans Serif"
      PrintInfos(0).PageHeaderHeight=   0
      PrintInfos(0).PageFooterHeight=   0
      PrintInfos.Count=   1
      AllowUpdate     =   0   'False
      DataMode        =   4
      DefColWidth     =   0
      HeadLines       =   1
      FootLines       =   1
      Caption         =   "Filtered Items"
      MultipleLines   =   0
      CellTipsWidth   =   0
      DeadAreaBackColor=   13160660
      RowDividerColor =   13160660
      RowSubDividerColor=   13160660
      DirectionAfterEnter=   1
      DirectionAfterTab=   1
      MaxRows         =   250000
      ViewColumnCaptionWidth=   0
      ViewColumnWidth =   0
      _PropDict       =   "_ExtentX,2003,3;_ExtentY,2004,3;_LayoutType,512,2;_RowHeight,16,3;_StyleDefs,513,0;_WasPersistedAsPixels,516,2"
      _StyleDefs(0)   =   "_StyleRoot:id=0,.parent=-1,.alignment=3,.valignment=0,.bgcolor=&*********&"
      _StyleDefs(1)   =   ":id=0,.fgcolor=&*********&,.wraptext=0,.locked=0,.transparentBmp=0"
      _StyleDefs(2)   =   ":id=0,.fgpicPosition=0,.bgpicMode=0,.appearance=0,.borderSize=0,.ellipsis=0"
      _StyleDefs(3)   =   ":id=0,.borderColor=&*********&,.borderType=0,.bold=0,.fontsize=825,.italic=0"
      _StyleDefs(4)   =   ":id=0,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(5)   =   ":id=0,.fontname=MS Sans Serif"
      _StyleDefs(6)   =   "Style:id=1,.parent=0,.namedParent=33,.bold=0,.fontsize=825,.italic=0"
      _StyleDefs(7)   =   ":id=1,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(8)   =   ":id=1,.fontname=MS Sans Serif"
      _StyleDefs(9)   =   "CaptionStyle:id=4,.parent=2,.namedParent=37"
      _StyleDefs(10)  =   "HeadingStyle:id=2,.parent=1,.namedParent=34,.bold=0,.fontsize=825,.italic=0"
      _StyleDefs(11)  =   ":id=2,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(12)  =   ":id=2,.fontname=MS Sans Serif"
      _StyleDefs(13)  =   "FooterStyle:id=3,.parent=1,.namedParent=35,.bold=0,.fontsize=825,.italic=0"
      _StyleDefs(14)  =   ":id=3,.underline=0,.strikethrough=0,.charset=0"
      _StyleDefs(15)  =   ":id=3,.fontname=MS Sans Serif"
      _StyleDefs(16)  =   "InactiveStyle:id=5,.parent=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
      _StyleDefs(17)  =   "SelectedStyle:id=6,.parent=1,.namedParent=36"
      _StyleDefs(18)  =   "EditorStyle:id=7,.parent=1"
      _StyleDefs(19)  =   "HighlightRowStyle:id=8,.parent=1,.namedParent=38"
      _StyleDefs(20)  =   "EvenRowStyle:id=9,.parent=1,.namedParent=39"
      _StyleDefs(21)  =   "OddRowStyle:id=10,.parent=1,.namedParent=40"
      _StyleDefs(22)  =   "RecordSelectorStyle:id=11,.parent=2,.namedParent=41"
      _StyleDefs(23)  =   "FilterBarStyle:id=12,.parent=1,.namedParent=42"
      _StyleDefs(24)  =   "Splits(0).Style:id=13,.parent=1"
      _StyleDefs(25)  =   "Splits(0).CaptionStyle:id=22,.parent=4"
      _StyleDefs(26)  =   "Splits(0).HeadingStyle:id=14,.parent=2"
      _StyleDefs(27)  =   "Splits(0).FooterStyle:id=15,.parent=3"
      _StyleDefs(28)  =   "Splits(0).InactiveStyle:id=16,.parent=5"
      _StyleDefs(29)  =   "Splits(0).SelectedStyle:id=18,.parent=6"
      _StyleDefs(30)  =   "Splits(0).EditorStyle:id=17,.parent=7"
      _StyleDefs(31)  =   "Splits(0).HighlightRowStyle:id=19,.parent=8"
      _StyleDefs(32)  =   "Splits(0).EvenRowStyle:id=20,.parent=9"
      _StyleDefs(33)  =   "Splits(0).OddRowStyle:id=21,.parent=10"
      _StyleDefs(34)  =   "Splits(0).RecordSelectorStyle:id=23,.parent=11"
      _StyleDefs(35)  =   "Splits(0).FilterBarStyle:id=24,.parent=12"
      _StyleDefs(36)  =   "Splits(0).Columns(0).Style:id=28,.parent=13"
      _StyleDefs(37)  =   "Splits(0).Columns(0).HeadingStyle:id=25,.parent=14"
      _StyleDefs(38)  =   "Splits(0).Columns(0).FooterStyle:id=26,.parent=15"
      _StyleDefs(39)  =   "Splits(0).Columns(0).EditorStyle:id=27,.parent=17"
      _StyleDefs(40)  =   "Named:id=33:Normal"
      _StyleDefs(41)  =   ":id=33,.parent=0"
      _StyleDefs(42)  =   "Named:id=34:Heading"
      _StyleDefs(43)  =   ":id=34,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
      _StyleDefs(44)  =   ":id=34,.wraptext=-1"
      _StyleDefs(45)  =   "Named:id=35:Footing"
      _StyleDefs(46)  =   ":id=35,.parent=33,.valignment=2,.bgcolor=&H8000000F&,.fgcolor=&H80000012&"
      _StyleDefs(47)  =   "Named:id=36:Selected"
      _StyleDefs(48)  =   ":id=36,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
      _StyleDefs(49)  =   "Named:id=37:Caption"
      _StyleDefs(50)  =   ":id=37,.parent=34,.alignment=2"
      _StyleDefs(51)  =   "Named:id=38:HighlightRow"
      _StyleDefs(52)  =   ":id=38,.parent=33,.bgcolor=&H8000000D&,.fgcolor=&H8000000E&"
      _StyleDefs(53)  =   "Named:id=39:EvenRow"
      _StyleDefs(54)  =   ":id=39,.parent=33,.bgcolor=&HFFFF00&"
      _StyleDefs(55)  =   "Named:id=40:OddRow"
      _StyleDefs(56)  =   ":id=40,.parent=33"
      _StyleDefs(57)  =   "Named:id=41:RecordSelector"
      _StyleDefs(58)  =   ":id=41,.parent=34"
      _StyleDefs(59)  =   "Named:id=42:FilterBar"
      _StyleDefs(60)  =   ":id=42,.parent=33"
   End
   Begin TDBText6Ctl.TDBText txtSelected 
      Height          =   2415
      Left            =   90
      TabIndex        =   25
      TabStop         =   0   'False
      Top             =   7050
      Width           =   3735
      _Version        =   65536
      _ExtentX        =   6588
      _ExtentY        =   4260
      Caption         =   "AIM_ForecastFilter.frx":06BE
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ForecastFilter.frx":072A
      Key             =   "AIM_ForecastFilter.frx":0748
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   -1
      ShowContextMenu =   -1
      MarginLeft      =   1
      MarginRight     =   1
      MarginTop       =   1
      MarginBottom    =   1
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   -1
      ScrollBars      =   2
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   0
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   -1
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   1
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin VB.Frame Frame1 
      Caption         =   "Criteria"
      Height          =   6375
      Left            =   84
      TabIndex        =   31
      Top             =   555
      Width           =   3855
      Begin VB.OptionButton optCriterion 
         Caption         =   "Location User-defined"
         Height          =   375
         Index           =   4
         Left            =   120
         TabIndex        =   45
         Top             =   2012
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Location Region"
         Height          =   375
         Index           =   3
         Left            =   120
         TabIndex        =   44
         Top             =   1569
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Location Status"
         Height          =   375
         Index           =   1
         Left            =   120
         TabIndex        =   43
         Top             =   683
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Location Division"
         Height          =   375
         Index           =   2
         Left            =   120
         TabIndex        =   42
         Top             =   1126
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Item Status"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   375
         Index           =   0
         Left            =   120
         TabIndex        =   41
         Top             =   240
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Class 1"
         Height          =   375
         Index           =   5
         Left            =   120
         TabIndex        =   40
         Top             =   2455
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Class 3"
         Height          =   375
         Index           =   7
         Left            =   120
         TabIndex        =   39
         Top             =   3341
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Class 2"
         Height          =   375
         Index           =   6
         Left            =   120
         TabIndex        =   38
         Top             =   2898
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Class 4"
         Height          =   375
         Index           =   8
         Left            =   120
         TabIndex        =   37
         Top             =   3784
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Buyer ID"
         Height          =   375
         Index           =   9
         Left            =   120
         TabIndex        =   36
         Top             =   4227
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Location ID"
         Height          =   375
         Index           =   10
         Left            =   120
         TabIndex        =   35
         Top             =   4670
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Vendor ID"
         Height          =   375
         Index           =   11
         Left            =   120
         TabIndex        =   34
         Top             =   5113
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Assortment"
         Height          =   375
         Index           =   12
         Left            =   120
         TabIndex        =   33
         Top             =   5556
         Width           =   3375
      End
      Begin VB.OptionButton optCriterion 
         Caption         =   "Item ID"
         Height          =   375
         Index           =   13
         Left            =   120
         TabIndex        =   32
         Top             =   6000
         Width           =   3375
      End
   End
End
Attribute VB_Name = "AIM_ForecastFilter"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2004 SSA Global. All rights reserved.
'*****************************************************************************
'
'   AIM_ForecastFilter.frm
'   Version Number - 1.0
'   Last Updated   - 2004/10/05
'   Updated By     - Annalakshmi Stocksdale
'
'   This replaces the former AIM_ItemFilters screen in allowing the creation
'       and modification of forecast item filters.  The screen's salient
'       features (as of original design) are as follows:
'   * The user may select one criterion or many criteria to be used in
'       narrowing the list of items, with respect to the
'       demand forecasting algorithms used in this application.
'   * The user may select one option per criterion or many. The selections
'       (per criterion) may be:
'       01 Inclusive (IN)
'       02 Exclusive (NOT IN)
'       03 For a range (BETWEEN x AND y)
'   * The user may choose to:
'       01 RETURN the selections to the calling form directly,
'       02 APPLY the filter(s) and view the result(s) before RETURNing
'           the filters to the calling form.
'       03 CANCEL any changes made in a particular session
'       04 Restart the filter by RESTORING ALL criteria to the original values
'           (if available, as passed in FROM the calling form;
'           typically applicable to the Forecast Modification screen)
'       05 Restart the filter by CLEARing all selections and starting over
'           (applicable only when called FROM the Forecast Setup screen)
'
'   NOTE: This Item Filter is not meant to replace the more generic
'       Item Lookup screen used by forms other than those related to
'       demand forecasting.
'   See related updates to the Forecast Modification and Setup screens.
'
'*****************************************************************************
' This file contains trade secrets of SSA Global. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of SSA Global.
'*****************************************************************************
Option Explicit
Option Base 0   'Sets default for all arrays in this module to start at 0
'Attributes exposed via properties
    Dim prm_Cn As ADODB.Connection          'Set
    Dim prm_LockExisting As Boolean         'Let
    Dim prm_ExistingFilters As String       'Let
    Dim rtn_AllFilters As String            'Let, Get
    Dim rtn_NewFilters As String            'Let, Get
    Dim rtn_UserCancel As Boolean           'Let, Get
    Dim prm_ItemFilterOnly  As Boolean     'Set  used for item filter only if true
    Dim rtn_ItemSelectStmt As String        'Get returns the Itemselectstatement
    
    'Plus rt_xa*Filters that just SETS and GETS values
'Local Constants
    'The Lookup Fields for the options are part of a control array.
    'The following constants name the specific index of each control
    Private Const LKUP_LcID As Integer = 0
    Private Const LKUP_LNAME As Integer = 1
    Private Const LKUP_VNID As Integer = 2
    Private Const LKUP_ASSORT As Integer = 3
    Private Const LKUP_ITEM As Integer = 4
    Private Const LKUP_ITDESC As Integer = 5
    Private Const LKUP_BYID As Integer = 6
'Modular variables
    'For the data FROM each option's query
    Dim m_rsOptions As ADODB.Recordset
    'For the selection set for the option being viewed.
    Dim m_xaOptions As XArrayDB
    
    'For a copy of the permanent values that are saved in the database (sent FROM calling form)
    Dim m_xaOrigCriteria As XArrayDB
    'For the entire set of selected values FROM all applicable criteria fields (transient set0
    Dim m_xaAllCriteria As XArrayDB
    'For storing the deltas
    Dim m_xaNewCriteria As XArrayDB
    
    'For displaying the results of applying the filters
    Dim m_xaResults As XArrayDB
    
    'For keeping track of the number of criteria selected in m_xaOptions.
    'This is related to the "BETWEEN-AND" comparison
    Dim m_CountSelOptions As Long

Dim ErrNumber As Long
Dim ErrSource As String
Dim ErrDescription As String
    
'*****************************************************************************
'Properties
'*****************************************************************************
Public Property Set pr_ADOConn(Cn As ADODB.Connection)
On Error GoTo ErrorHandler
    'The form will accept a connection object FROM the calling form
    Set prm_Cn = Cn
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::pr_ADOConn [PropertySet]", Now, gDRGeneralError, True, Err
End Property

Public Property Let pr_LockExisting(DoLock As Boolean)
On Error GoTo ErrorHandler
    'The form requires an indicator for locking existing filters.
    '   * True if being called FROM the Planner.
    '   * False if being called FROM the Setup
    prm_LockExisting = DoLock
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::pr_LockExisting [PropertyLet]", Now, gDRGeneralError, True, Err
End Property
Public Property Let prm_ItemFilter(ItemFilterOnly As Boolean)
On Error GoTo ErrorHandler
    'The form requires an indicator for determining if the screen is used for item filter
    '   * True if being called for item fiter only
    '   * False if being called forecast filter screens
        prm_ItemFilterOnly = ItemFilterOnly
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::prm_ItemFilter [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Public Property Let pr_ExistingFilters(SelFilters As String)
On Error GoTo ErrorHandler
    'The form will accept a delimited string containing
    '   the field name, comparison operator and value-list
    prm_ExistingFilters = SelFilters
    'A.STOCKSDALE: TO DO -- Parse into array
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::pr_ExistingFilters [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Private Property Let rt_AllFilters( _
    SelText As String)
On Error GoTo ErrorHandler
    'The form will RETURN a delimited string containing
    '   all the field names with corresponding
    '   comparison operators and value-lists
    rtn_AllFilters = SelText
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_AllFilters [PropertyLet]", Now, gDRGeneralError, True, Err
End Property
'Private Property Let prm_ItemSelectStmts( _
'    SelText As String)
'On Error GoTo ErrorHandler
'    prm_ItemSelectStmts = SelText
'Exit Property
'ErrorHandler:
'    f_HandleErr Me.Caption & "{prm_ItemSelectStmts [PropertyLet]}", Err.Description
'End Property

Public Property Get rt_AllFilters() As String
On Error GoTo ErrorHandler
    'The form will RETURN a delimited string containing
    '   all the field names with corresponding
    '   comparison operators and value-lists
    rt_AllFilters = rtn_AllFilters
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_AllFilters [PropertyLet]", Now, gDRGeneralError, True, Err
End Property
Public Property Get rt_ItemSelectStmt() As String
On Error GoTo ErrorHandler
    rt_ItemSelectStmt = rtn_ItemSelectStmt
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::prm_ItemSelectStmts [PropertyGet]", Now, gDRGeneralError, True, Err
End Property
Private Property Let rt_NewFilters(AddText As String)
On Error GoTo ErrorHandler
    'The form will RETURN a delimited string identifying selected criteria
    '   that are additional to the existent ones sent via pr_ExistingFilters
    rtn_NewFilters = AddText
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_NewFilters [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Public Property Get rt_NewFilters() As String
On Error GoTo ErrorHandler
    'The form will RETURN a delimited string identifying selected criteria
    '   that are additional to the existent ones sent via pr_ExistingFilters
    rt_NewFilters = rtn_NewFilters
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_NewFilters [PropertyGet]", Now, gDRGeneralError, True, Err
End Property

Private Property Let rt_UserCancel(IsCancelled As Boolean)
On Error GoTo ErrorHandler
    'The form will RETURN a cancel flag if the user "Cancel"s out of here.
    rtn_UserCancel = IsCancelled
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_UserCancel [PropertyLet]", Now, gDRGeneralError, True, Err
End Property

Public Property Get rt_UserCancel() As Boolean
On Error GoTo ErrorHandler
    'The form will RETURN a cancel flag if the user "Cancel"s out of here.
    rt_UserCancel = rtn_UserCancel
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_UserCancel [PropertyGet]", Now, gDRGeneralError, True, Err
End Property


Public Property Set rt_xaOrigFilters(p_xaCrit As XArrayDB)
On Error GoTo ErrorHandler
    'The form with ACCEPT an array with the permanent criteria, as saved in the table.
    g_CXArr1ToXArr2 p_xaCrit, m_xaOrigCriteria

Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_xaOrigFilters [PropertySet]", Now, gDRGeneralError, True, Err
End Property

Public Property Set rt_xaExtraFilters(p_xaCrit As XArrayDB)
On Error GoTo ErrorHandler
    'The form will ACCEPT an array with all the newly selected criteria.
    'These are not saved in the table and are not to be locked.
    g_CXArr1ToXArr2 p_xaCrit, m_xaNewCriteria

Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_xaExtraFilters [PropertySet]", Now, gDRGeneralError, True, Err
End Property

Public Property Set rt_xaAllFilters(p_xaCrit As XArrayDB)
On Error GoTo ErrorHandler
    'The form will ACCEPT an array with all the newly selected criteria.
    'These are not saved in the table and are not to be locked.
    g_CXArr1ToXArr2 p_xaCrit, m_xaAllCriteria

Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_xaAllFilters [PropertySet]", Now, gDRGeneralError, True, Err
End Property

Public Property Get rt_xaExtraFilters() As XArrayDB
On Error GoTo ErrorHandler
    'The form will RETURN an array with just the newly selected criteria.
    'These are not saved in the table and were not locked.
    If g_IsArray(m_xaNewCriteria) Then
        g_CXArr1ToXArr2 m_xaNewCriteria, rt_xaExtraFilters
    Else
        Set rt_xaExtraFilters = Nothing
    End If
    
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_xaExtraFilters [PropertySet]", Now, gDRGeneralError, True, Err
End Property

Public Property Get rt_xaAllFilters() As XArrayDB
On Error GoTo ErrorHandler
    'The form will RETURN an array with all the selected criteria.
    'This includes the permanent/saved set and the temporary/extra set.
    'The calling form is expected to save these, assuming that permanent set was unlocked.
    If g_IsArray(m_xaAllCriteria) Then
        g_CXArr1ToXArr2 m_xaAllCriteria, rt_xaAllFilters
    Else
        Set rt_xaAllFilters = Nothing
    End If
    
Exit Property
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::rt_xaAllFilters [PropertyGet]", Now, gDRGeneralError, True, Err
End Property

'*****************************************************************************
'Events
'*****************************************************************************
Private Sub cmdApply_Click()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Long
    Dim rsResults As ADODB.Recordset
    Dim TDBGResults As TDBGrid
    Dim ColResults As TrueOleDBGrid80.Column
    Dim RowCount As Long, ColCount As Integer, IndexCounter As Long
    Dim RowIdx As Long, ColIdx As Integer
    Dim WhereClause As String
    Dim strMessage As String
    Dim IsShortList As Boolean
    
    Screen.MousePointer = vbHourglass
    m_xaResults.Clear
    Write_Message COMP_EMPTY
    
    Set TDBGResults = Me.dgResults
    
    Set rsResults = New ADODB.Recordset
    With rsResults
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    strMessage = getTranslationResource("STATMSG06705")
    If StrComp(strMessage, "STATMSG06705") = 0 Then strMessage = "Please wait while the screen is being refreshed..."
    Write_Message strMessage
    
    'True since is not the sub-filter clause
    WhereClause = BuildWhereClause(True)
    IsShortList = False 'We want all filter columns to be visible for the results grid
    RtnCode = GetItemList(IsShortList, WhereClause, rsResults)
    'Re-open grid
    If RtnCode = 0 Then
        rsResults.MoveFirst
        RowCount = (rsResults.RecordCount - 1)
        ColCount = (rsResults.Fields.Count - 1)
        m_xaResults.ReDim 0, RowCount, 0, ColCount + 1
        For RowIdx = 0 To RowCount
            For ColIdx = 0 To ColCount
                m_xaResults(RowIdx, ColIdx) = rsResults.Fields(ColIdx).Value
            Next ColIdx
            rsResults.MoveNext
        Next RowIdx
    Else
        m_xaResults.ReDim 0, 0, 0, 0
    End If
    
    With TDBGResults
        .Visible = False
        'Clear out existing stuff
        While .Columns.Count > 0
            .Columns.Remove 0
        Wend
        'Re-populate, if any records are available for display
        If f_IsRecordsetOpenAndPopulated(rsResults) Then
            While .Columns.Count <= ColCount
                Set ColResults = .Columns.Add(0)
                ColResults.Visible = True
                ColResults.Locked = True
            Wend
            For ColIdx = 0 To ColCount
                'NOTE: The first grid column is a checkbox, so move up one.
                .Columns(ColIdx).Caption = _
                    getTranslationResource(rsResults.Fields(ColIdx).Name)
            Next ColIdx
            '.MoveFirst
            If RowCount < Me.txtMaxRows.Text Then RowCount = RowCount + 1
        Else
            RowCount = 0
        End If
        'Update screen text
        Me.dgResults.Caption = _
            getTranslationResource("Number of filtered Items", True) & _
            Space(1) & CStr(RowCount)
        
        strMessage = getTranslationResource("STATMSG06700")
        If StrComp(strMessage, "STATMSG06700") = 0 Then strMessage = "Items match the selected filter criteria."
        strMessage = CStr(RowCount) & Space(1) & strMessage
        Write_Message strMessage
        'Adjust user-interface, and destroy transient objects
        AdjustColumnWidths TDBGResults, ACW_EXPAND
        
        .ReBind
        .Refresh
        .Visible = True
    End With
    
    Set TDBGResults = Nothing
    Set ColResults = Nothing

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    On Error Resume Next
    TDBGResults.Visible = True
    Set TDBGResults = Nothing
    Set ColResults = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr , , , "AIM_ForecastFilter::cmdApply_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Write_Message COMP_EMPTY
    
    rt_UserCancel = True
    
    Unload Me
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClearAll_Click()
On Error GoTo ErrorHandler

    Dim TDBGResults As TDBGrid
    Dim OptionIndex As Integer
    Dim strMessage As String
    
    Write_Message COMP_EMPTY
    
    'Find the selected OptionIndex FROM the option-button group
    For OptionIndex = optCriterion.LBound To optCriterion.UBound
        If optCriterion(OptionIndex).Value = True Then Exit For
    Next OptionIndex
    'Update the criteria array to discard all existing values
    g_InitCriteria m_xaAllCriteria
    g_InitCriteria m_xaNewCriteria
    
    'Refresh the options display
    cmdClearLookup_Click
    ResetOptions OptionIndex
    
    'Clear the rest of the screen
    Set TDBGResults = Me.dgResults
    Me.txtSelected.Text = COMP_EMPTY
    m_xaResults.Clear
    m_xaResults.ReDim 0, 0, 0, 0
    With TDBGResults
        .Visible = False
        'Clear out existing stuff
        While .Columns.Count > 0
            .Columns.Remove 0
        Wend
        .Refresh
        .ReBind
        .Caption = getTranslationResource("Filtered Items")
        .Visible = True
    End With
    
    Set TDBGResults = Nothing
    
    Me.dcMultiCompare.Value = COMP_IN
    Me.optCriterion(0).Value = True
    
    strMessage = getTranslationResource("STATMSG06703")
    If StrComp(strMessage, "STATMSG06703") = 0 Then strMessage = "All selected criteria have been removed."
    Write_Message strMessage

Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    On Error Resume Next
    Set TDBGResults = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr Me.Caption & "{cmdClearAll_Click}"
End Sub

Private Sub cmdClearLookup_Click()
On Error GoTo ErrorHandler

    Dim OptionIdx As Integer
    Dim ColIdx As Integer
    Dim strMessage As String
    
    Write_Message COMP_EMPTY
    
    'Clear the lookup fields, first
    With Me.txtLkUp
        For ColIdx = .LBound To .UBound
            .Item(ColIdx).Text = COMP_EMPTY
            .Item(ColIdx).ReadOnly = False
        Next ColIdx
    End With
    
    'Find the selected OptionIndex FROM the option-button group
    For OptionIdx = optCriterion.LBound To optCriterion.UBound
        If optCriterion(OptionIdx).Value = True Then Exit For
    Next OptionIdx
    
    Me.txtMaxRows.Text = 200
    Me.dcComparison.Value = COMP_EQ
    
    ToggleLookupFrame OptionIdx
      
    strMessage = getTranslationResource("STATMSG06701")
    If StrComp(strMessage, "STATMSG06701") = 0 Then strMessage = "Sub-filters have been reset to default values."
    Write_Message strMessage

  
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::cmdClearLookup_Click", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbDefault
End Sub

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim WhereClause As String
    Dim OptionIdx As Integer, IndexCounter As Integer
    Dim RtnCode As Long
    Dim strMessage As String
    Dim IsShortList As Boolean
    
    Screen.MousePointer = vbHourglass
    Write_Message COMP_EMPTY
    WhereClause = COMP_EMPTY
    
    'Find the selected OptionIndex FROM the option-button group
    For OptionIdx = optCriterion.LBound To optCriterion.UBound
        If optCriterion(OptionIdx).Value = True Then Exit For
    Next OptionIdx
    
    WhereClause = COMP_EMPTY
    If OptionIdx = CRIT_IDX_ITEM Then
        'Clear out existing data
        ReInitOptions
        'False since this is the sub-filter clause
        WhereClause = BuildWhereClause(False)
        IsShortList = True  'We want a limited list of columns for the criterion grid
        RtnCode = GetItemList(IsShortList, WhereClause)
        ResetOptions OptionIdx
    Else
        'Build the query string and retrieve
        RedisplayOptions OptionIdx
    End If
    
    Screen.MousePointer = vbDefault
    
    strMessage = getTranslationResource("STATMSG06702")
    If StrComp(strMessage, "STATMSG06702") = 0 Then strMessage = "Data for selected criterion has been reset to match the given sub-filter(s)."
    Write_Message strMessage

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdRestore_Click()
On Error GoTo ErrorHandler

    Dim OptionIndex As Integer
    Dim strMessage As String
    
    Write_Message COMP_EMPTY
    
    'Find the selected OptionIndex FROM the option-button group
    For OptionIndex = optCriterion.LBound To optCriterion.UBound
        If optCriterion(OptionIndex).Value = True Then Exit For
    Next OptionIndex
    
    'Update the criteria array to match the original set
    g_CXArr1ToXArr2 m_xaOrigCriteria, m_xaAllCriteria
    If Not g_IsArray(m_xaAllCriteria) Then
        g_InitCriteria m_xaAllCriteria
    End If
    g_InitCriteria m_xaNewCriteria
    
    'Refresh the options display
    ResetOptions OptionIndex

    strMessage = getTranslationResource("STATMSG06704")
    If StrComp(strMessage, "STATMSG06704") = 0 Then strMessage = "All selected criteria have been reset to the original values."
    Write_Message strMessage

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::cmdRestore_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Write_Message COMP_EMPTY
    
    rt_UserCancel = False
    
    Unload Me
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim DD_DBCombo As SSOleDBCombo
    
    Set DD_DBCombo = dcComparison
    With DD_DBCombo
        'Build Columns
        .Columns(0).Caption = getTranslationResource("Comparison")
        .Columns(0).FieldLen = 1
        .Columns(0).Width = 1000
    
        .Columns(1).Caption = getTranslationResource("Description")
        .Columns(1).FieldLen = 24
        .Columns(1).Width = 2000
        
        If prm_LockExisting _
        And Trim$(prm_ExistingFilters) <> COMP_EMPTY _
        Then
            'Restrict this list disallow elimination of selected criteria
            .AddItem (COMP_EQ & Chr(9) & getTranslationResource("Equal to"))
            '.AddItem (COMP_GT & Chr(9) & getTranslationResource("Greater than"))
            .AddItem (COMP_GTEQ & Chr(9) & getTranslationResource("Greater than / Equal to"))
            '.AddItem (COMP_LT & Chr(9) & getTranslationResource("Less than"))
            .AddItem (COMP_LTEQ & Chr(9) & getTranslationResource("Less than / Equal to"))
            '.AddItem (COMP_NOTEQ & Chr(9) & getTranslationResource("Not equal to"))
            .AddItem (COMP_LIKE & Chr(9) & getTranslationResource("Begins with"))
            .AddItem (COMP_LIKE & Chr(9) & getTranslationResource("Contains"))
            .AddItem (COMP_BETWEEN & Chr(9) & getTranslationResource("Is between these values"))
            .AddItem (COMP_IN & Chr(9) & getTranslationResource("Is in this list of values"))
            '.AddItem (COMP_NOTIN & Chr(9) & getTranslationResource("Is not in this list of values"))
        Else
            'Load all comparison operators
            .AddItem (COMP_EQ & Chr(9) & getTranslationResource("Equal to"))
            .AddItem (COMP_GT & Chr(9) & getTranslationResource("Greater than"))
            .AddItem (COMP_GTEQ & Chr(9) & getTranslationResource("Greater than / Equal to"))
            .AddItem (COMP_LT & Chr(9) & getTranslationResource("Less than"))
            .AddItem (COMP_LTEQ & Chr(9) & getTranslationResource("Less than / Equal to"))
            .AddItem (COMP_NOTEQ & Chr(9) & getTranslationResource("Not equal to"))
            .AddItem (COMP_LIKE & Chr(9) & getTranslationResource("Begins with"))
            .AddItem (COMP_LIKE & Chr(9) & getTranslationResource("Contains"))
            .AddItem (COMP_BETWEEN & Chr(9) & getTranslationResource("Is between these values"))
            .AddItem (COMP_IN & Chr(9) & getTranslationResource("Is in this list of values"))
            .AddItem (COMP_NOTIN & Chr(9) & getTranslationResource("Is not in this list of values"))
        End If
        
        .Value = COMP_EQ
        
    End With
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths DD_DBCombo, ACW_EXPAND
    End If
    Set DD_DBCombo = Nothing
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    On Error Resume Next
    Set DD_DBCombo = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr , , , "AIM_ForecastFilter::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcMultiCompare_CloseUp()
On Error GoTo ErrorHandler

    Dim OptionIndex As Integer
    
    'Find the selected OptionIndex FROM the option-button group
    For OptionIndex = optCriterion.LBound To optCriterion.UBound
        If optCriterion(OptionIndex).Value = True Then Exit For
    Next OptionIndex
    
    'Update the criteria array
    RefreshCriteria OptionIndex
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::dcMultiCompare_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcMultiCompare_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim DD_DBCombo As SSOleDBCombo
    
    Set DD_DBCombo = dcMultiCompare
    With DD_DBCombo
        'Build Columns
        IndexCounter = 0
        .Columns(IndexCounter).Caption = getTranslationResource("Comparison")
        .Columns(IndexCounter).FieldLen = 1
        .Columns(IndexCounter).Width = 1000
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Caption = getTranslationResource("Description")
        .Columns(IndexCounter).FieldLen = 24
        .Columns(IndexCounter).Width = 2000
        'Load Values
        .AddItem (COMP_IN & Chr(9) & getTranslationResource("Is in this list of values"))
        .AddItem (COMP_NOTIN & Chr(9) & getTranslationResource("Is not in this list of values"))
        .AddItem (COMP_BETWEEN & Chr(9) & getTranslationResource("Is between these values"))
    End With
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths DD_DBCombo, ACW_EXPAND
    End If
    DD_DBCombo.Value = COMP_IN
    Set DD_DBCombo = Nothing
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    On Error Resume Next
    Set DD_DBCombo = Nothing
    Err.Number = ErrNumber
    Err.source = ErrSource
    Err.Description = ErrDescription
    f_HandleErr , , , "AIM_ForecastFilter::dcMultiCompare_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgOptions_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler
    
    Dim RowStr As String
    Dim CurrentValue As Variant
    Dim strMessage As String
    
    CurrentValue = Me.dgOptions.Columns(0).Value
    m_CountSelOptions = IIf(CurrentValue = vbUnchecked, (m_CountSelOptions - 1), (m_CountSelOptions + 1))
    
    If Me.dcMultiCompare.Value = COMP_BETWEEN Then
        'Cancel if more than two values selected
        If m_CountSelOptions > 2 Then
            strMessage = getTranslationResource("MSGBOX02001")
            If StrComp(strMessage, "MSGBOX02001") = 0 Then strMessage = "Please enter two values separated by a comma."
            MsgBox strMessage, vbExclamation
            Cancel = True
            Exit Sub
        End If
    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::dgOptions_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgOptions_LostFocus()
On Error GoTo ErrorHandler

    Dim OptionIndex As Integer
    
    'Update the selected criteria list
    If m_xaOptions.UpperBound(2) <= 0 Then Exit Sub
    If Me.dgOptions.DataChanged = False Then Exit Sub
    
    'Else, find the selected OptionIndex FROM the option-button group
    For OptionIndex = optCriterion.LBound To optCriterion.UBound
        If optCriterion(OptionIndex).Value = True Then Exit For
    Next OptionIndex
    
    'Update the criteria array
    RefreshCriteria OptionIndex
        
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::dgOptions_LostFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    Me.optCriterion(0).Value = True
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim MyForm As Form
    Dim strMessage As String
    
    Screen.MousePointer = vbHourglass
    If optCriterion.UBound > CRIT_TOTAL Then
        strMessage = getTranslationResource("ERRMSG06700")
        If StrComp(strMessage, "ERRMSG06700") = 0 Then strMessage = "This form has been changed illegally!"
        MsgBox strMessage, vbOKOnly, Me.Caption
        Unload Me
    End If
    If prm_ItemFilterOnly = True Then
       Me.Caption = "SSA DR Items Filter"
    End If
    Set MyForm = Me
        GetTranslatedCaptions MyForm
    Set MyForm = Nothing
    
    rt_UserCancel = True
    
    Set m_xaOptions = New XArrayDB
    m_xaOptions.ReDim 0, 0, 0, 0
    Me.dgOptions.Array = m_xaOptions
    
    Set m_xaResults = New XArrayDB
    m_xaResults.ReDim 0, 0, 0, 0
    Me.dgResults.Array = m_xaResults
    
    If Not g_IsArray(m_xaAllCriteria) Then
        g_InitCriteria m_xaAllCriteria
    End If
    If Not g_IsArray(m_xaNewCriteria) Then
        g_InitCriteria m_xaNewCriteria
    End If
    
    Me.txtMaxRows.Spin.Visible = 1
    Me.txtMaxRows.Text = 200
    Me.txtSelected.Text = prm_ExistingFilters
    
    cmdClearLookup_Click
    cmdClearAll.Enabled = Not prm_LockExisting
   
    
    Screen.MousePointer = vbNormal
    Write_Message COMP_EMPTY
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::Form_Load", Now, gDRGeneralError, True, Err
    On Error Resume Next
    Set MyForm = Nothing
End Sub

Private Sub Form_Resize()
On Error GoTo ErrorHandler

    Dim setWidth As Long, setHeight As Long
    
    Select Case Me.WindowState
    Case vbMaximized
        'Reset the two grids to make use of extra space.
        setWidth = Me.ScaleWidth - (frmOptions.Left + 200)
        setHeight = Me.ScaleHeight - (txtSelected.Top + 200)
        
        frmOptions.Width = IIf(setWidth > frmOptions.Width, _
                            setWidth, _
                            frmOptions.Width)
        txtSelected.Height = IIf(setHeight > txtSelected.Height, _
                            setHeight, _
                            txtSelected.Height)
        
    Case Else
        frmOptions.Width = 7335
        txtSelected.Height = 2415
    End Select
    'Resize the Results grid
    dgResults.Width = frmOptions.Width
    dgResults.Height = txtSelected.Height
    'Resize the Options grid
    dgOptions.Width = frmOptions.Width - 250
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::Form_Resize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    Screen.MousePointer = vbDefault
    Dim WhereClause As String
    If rt_UserCancel = False Then
        'Concatenate selected options into a string for the user's at-a-glance reference
        rtn_AllFilters = g_CXArrToPara(m_xaAllCriteria, False, False)
        rtn_NewFilters = g_CXArrToPara(m_xaNewCriteria, False, False)
        'True since is not the sub-filter clause
         WhereClause = BuildWhereClause(True)
         rtn_ItemSelectStmt = g_QueryForItems(WhereClause, True, True)
         
        
    Else
        'Return the original values FROM calling function
        g_CXArr1ToXArr2 m_xaOrigCriteria, m_xaAllCriteria
        g_InitCriteria m_xaNewCriteria
        rtn_AllFilters = prm_ExistingFilters
        rtn_NewFilters = COMP_EMPTY
    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::Form_Unload", Now, gDRGeneralError, True, Err
    Resume Next
End Sub

Private Sub optCriterion_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim WhereClause As String
    Dim IndexCounter As Integer
    Dim RtnCode As Long
    Dim strMessage As String
    Dim IsShortList As Boolean
    
    Screen.MousePointer = vbHourglass
    Write_Message COMP_EMPTY
    WhereClause = COMP_EMPTY
    
    WhereClause = COMP_EMPTY
    If Index = CRIT_IDX_ITEM Then
        'Clear out existing data
        ReInitOptions
        'False since this is the sub-filter clause
        WhereClause = BuildWhereClause(False)
        IsShortList = True  'We want a limited list of columns for the criterion grid
        RtnCode = GetItemList(IsShortList, WhereClause)
        ResetOptions Index
    Else
        'Build the query string and retrieve
        RedisplayOptions Index
    End If
    
    'Return control
    Screen.MousePointer = vbDefault
    Me.optCriterion(Index).SetFocus

    strMessage = getTranslationResource("STATMSG06702")
    If StrComp(strMessage, "STATMSG06702") = 0 Then strMessage = "Data for selected criterion has been reset to match the given sub-filter(s)."
    Write_Message strMessage

Exit Sub
ErrorHandler:
    If ErrNumber = 5 Then
        'SetFocus causes error if this event is triggered before Form_Load has completed.
        'Make sure that optCriterion's values are not set until Form_Activate
        Resume Next
    Else
        f_HandleErr , , , "AIM_ForecastFilter::optCriterion_Click", Now, gDRGeneralError, True, Err
    End If
    Screen.MousePointer = vbDefault
End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    With Me.txtMaxRows
        If IsNumeric(.Text) Then
            .Value = Format(.Text, "#,##0")
        ElseIf Len(.Text) = 0 Then
            .Value = Format(0, "#,##0")
        Else
            Beep
            .SetFocus
        End If
    End With

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ForecastFilter::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub



'*****************************************************************************
'Procedures
'*****************************************************************************

Private Function GetItemList( _
    p_ShortList As Boolean, _
    p_WhereClause As String, _
    Optional p_rsResults As ADODB.Recordset) As Long
On Error GoTo ErrorHandler
    
    Dim Query_FilteredItems As ADODB.Command
    Dim RtnCode As Long
    Dim QueryText As String
    Dim SAVersion As Integer
    
    'Set default to failure
    GetItemList = -1
    'Fill in the variables
    SAVersion = g_GetSAVersion(prm_Cn)
    If prm_ItemFilterOnly = False Then
        QueryText = g_QueryForFcstItems(True, p_WhereClause, SAVersion, False, p_ShortList)
    Else
        QueryText = g_QueryForItems(p_WhereClause, p_ShortList, False)
    End If
    'Command object for the Stored Procedure
    Set Query_FilteredItems = New ADODB.Command
    With Query_FilteredItems
        Set .ActiveConnection = prm_Cn
        .CommandType = adCmdText
        .CommandText = QueryText
    End With
    'Fetch data
    If (p_rsResults Is Nothing) Then
        If f_IsRecordsetValidAndOpen(m_rsOptions) Then m_rsOptions.Close
        m_rsOptions.Open Query_FilteredItems
        'Set return status
        If f_IsRecordsetValidAndOpen(m_rsOptions) Then GetItemList = 0
    Else
        p_rsResults.Open Query_FilteredItems
        'Set return status
        If f_IsRecordsetOpenAndPopulated(p_rsResults) Then GetItemList = 0
    End If
    'CleanUp
    If Not (Query_FilteredItems Is Nothing) Then Set Query_FilteredItems.ActiveConnection = Nothing
    Set Query_FilteredItems = Nothing

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    If f_IsRecordsetValidAndOpen(m_rsOptions) Then m_rsOptions.Close
    Set m_rsOptions = Nothing
    If f_IsRecordsetValidAndOpen(p_rsResults) Then p_rsResults.Close
    Set p_rsResults = Nothing
    If Not (Query_FilteredItems Is Nothing) Then Set Query_FilteredItems.ActiveConnection = Nothing
    Set Query_FilteredItems = Nothing
        
    f_HandleErr , , , "AIM_ForecastFilter::GetItemList", Now, gDRGeneralError, True, Err

End Function



Private Function BuildWhereClause(p_MainOptions As Boolean) As String
On Error GoTo ErrorHandler

    Dim CatStr As String, RowStr As String, FieldStr As String
    
    CatStr = g_CXArrToPara(m_xaAllCriteria, True, False)
    If Trim$(CatStr) = COMP_EMPTY Then
        m_xaAllCriteria.ReDim 0, CRIT_TOTAL, 0, CRIT_OFFSET
    End If
    
    If p_MainOptions = False Then
        'Get sub-filters, too.
        CatStr = CatStr & BuildLookupClause(CatStr) 'Do NOT specify option index here
    End If
    'Return paragraph
    BuildWhereClause = CatStr
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    f_HandleErr , , , "AIM_ForecastFilter::BuildWhereClause", Now, gDRGeneralError, True, Err
End Function

Private Function BuildLookupClause(MainClause As String, Optional OptionIndex As Integer = -1) As String
On Error GoTo ErrorHandler

    Dim CatStr As String, RowStr As String, FieldStr As String
    Dim RowIdx As Long, ColIdx As Long, PosIndex As Long
    Dim CompType As String, CompDetail As String
    Dim IndexCounter As Long, TotalStrings As Long
    Dim StrArray() As String
    Dim CompareIndex As Integer
    Dim AllDone As Boolean
    Dim CheckString As String
    
    Select Case OptionIndex
    Case CRIT_IDX_ITSTAT, CRIT_IDX_LSTATUS, _
    CRIT_IDX_LDIVISION, CRIT_IDX_LREGION, CRIT_IDX_LUSERDEFINED, _
    CRIT_IDX_CLASS1, CRIT_IDX_CLASS2, CRIT_IDX_CLASS3, CRIT_IDX_CLASS4, _
    CRIT_IDX_DFTBYID, CRIT_IDX_DFTREVIEWERID, CRIT_IDX_DEMAMADSOURCE, _
    CRIT_IDX_LEADTIMESOURCE, CRIT_IDX_LTYPE, CRIT_IDX_LRANK, CRIT_IDX_DROPSHIP
        'Return the row for the selected criterion
        FieldStr = COMP_EMPTY
        Select Case OptionIndex
        Case CRIT_IDX_ITSTAT
            FieldStr = " AND ItStatus.ItStat "
        Case CRIT_IDX_LSTATUS
            FieldStr = " AND AIMCodeLookup.CodeID "
        Case CRIT_IDX_LDIVISION
            FieldStr = " AND AIMLocations.LDivision "
        Case CRIT_IDX_LREGION
            FieldStr = " AND AIMLocations.LRegion "
        Case CRIT_IDX_LUSERDEFINED
            FieldStr = " AND AIMLocations.LUserDefined "
        Case CRIT_IDX_CLASS1, CRIT_IDX_CLASS2, CRIT_IDX_CLASS3, CRIT_IDX_CLASS4
            FieldStr = " AND AIMClasses.Class "
        Case CRIT_IDX_DFTBYID
            FieldStr = " AND AIMUsers.UserID "
        Case CRIT_IDX_DFTREVIEWERID
            FieldStr = " AND AIMUsers.UserID "
        Case CRIT_IDX_DEMAMADSOURCE
            FieldStr = " AND AIMCODELOOKUP.CODEID "
        Case CRIT_IDX_LEADTIMESOURCE
            FieldStr = " AND AIMCODELOOKUP.CODEID "
        Case CRIT_IDX_LTYPE
            FieldStr = " AND AIMCODELOOKUP.CODEID "
        Case CRIT_IDX_LRANK
            FieldStr = " AND AIMLocations.LRank "
        Case CRIT_IDX_DROPSHIP
        End Select
        If FieldStr <> COMP_EMPTY Then
            RowStr = COMP_EMPTY
            CompType = m_xaAllCriteria(OptionIndex, CRIT_OFFSET)
            If StrComp(CompType, COMP_NOTIN, vbTextCompare) = 0 Then
                'Set the string to empty
                CatStr = COMP_EMPTY
            Else
                RowStr = g_CXArrToLine(m_xaAllCriteria, CLng(OptionIndex), True, False, FieldStr)
                If Trim$(RowStr) <> COMP_EMPTY _
                And StrComp(RowStr, COMP_ERROR, vbTextCompare) <> 0 Then
                    CatStr = RowStr
                End If
            End If  'Check RowStr for validity
        End If  'Check FieldStr for validity
        
    Case Else
        '  These are the criteria with sub-filters associated with them.
        '   The rules are a little different, now.  Instead of just getting the values on the basis of the main filter,
        '   we have to incorporate the sub-filter, too.
        
        'Get comparison operators
        CompType = Me.dcComparison.Value
        CompDetail = Me.dcComparison.Text

        Select Case OptionIndex
        Case CRIT_IDX_LCID
            CompareIndex = LKUP_LcID
        Case CRIT_IDX_VNID
            CompareIndex = LKUP_VNID
        Case CRIT_IDX_ASSORT
            CompareIndex = LKUP_ASSORT
        Case CRIT_IDX_ITEM
            CompareIndex = LKUP_ITEM
        Case Else
            'Default to all sub-filters and proceed
            CompareIndex = -1
        End Select
        
        With Me.txtLkUp
            CatStr = COMP_EMPTY
            For ColIdx = .LBound To .UBound
                FieldStr = COMP_EMPTY
                Select Case ColIdx
                Case LKUP_LcID     '0
                    If InStr(1, MainClause, ".LcID", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_LCID Or OptionIndex = -1) _
                    Then _
                        FieldStr = " AIMLocations.LcID "
                Case LKUP_LNAME     '1
                    If InStr(1, MainClause, ".LName", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_LCID Or OptionIndex = -1) _
                    Then _
                        FieldStr = " AIMLocations.LName "
                Case LKUP_VNID     '2
                    If InStr(1, MainClause, ".VnID", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_VNID Or OptionIndex = -1) _
                    Then _
                        FieldStr = " AIMVendors.VnID "
                Case LKUP_ASSORT     '3
                    If InStr(1, MainClause, ".Assort", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_VNID Or OptionIndex = -1) _
                    Then _
                        FieldStr = " AIMVendors.Assort "
                Case LKUP_ITEM     '4
                    If InStr(1, MainClause, ".Item", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_ITEM Or OptionIndex = -1) _
                    Then _
                        FieldStr = " Item.Item "
                Case LKUP_ITDESC     '5
                    If InStr(1, MainClause, ".ItDesc", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_ITEM Or OptionIndex = -1) _
                    Then _
                        FieldStr = " Item.ItDesc "
                Case LKUP_BYID     '6
                    If InStr(1, MainClause, ".ByID", vbTextCompare) <= 0 _
                    And (OptionIndex = CRIT_IDX_BYID Or OptionIndex = -1) _
                    Then _
                    FieldStr = " AIMUsers.UserId "
                    'FieldStr = " Item.ByID "   'sri changed added the above line and commented this one
                        
                End Select
                If FieldStr <> COMP_EMPTY Then
                    RowStr = COMP_EMPTY
                    RowStr = .Item(ColIdx).Text
                    'This rowstr could be one value, or a set of values delimited by commas
                    If Trim$(RowStr) <> COMP_EMPTY _
                    And StrComp(RowStr, COMP_ERROR, vbTextCompare) <> 0 Then
                        'Parse the string out into an array
                        Erase StrArray
                        ReDim StrArray(0 To 10)
                        For IndexCounter = LBound(StrArray) To UBound(StrArray)
                            StrArray(IndexCounter) = COMP_EMPTY
                        Next IndexCounter
                        IndexCounter = 0
                        StrArray = Split(RowStr, DELIM_COLUMN)
                        TotalStrings = UBound(StrArray)
                        
                        'Prefix the field and comparison operator to value
                        RowStr = COMP_AND & FieldStr & CompType & Space(1)
                        For IndexCounter = 0 To TotalStrings
                            CheckString = g_FormatCompareList(Trim$(StrArray(IndexCounter)), CompType, CompDetail)
                            If StrComp(CheckString, COMP_ERROR, vbTextCompare) = 0 Then
                                'Bad value, exit
                                BuildLookupClause = CheckString
                                Exit Function
                            Else
                                RowStr = RowStr & CheckString
                            End If
                            If IndexCounter < TotalStrings Then _
                                RowStr = RowStr & COMP_OR & FieldStr & CompType & Space(1)
                        Next IndexCounter
                        'Append line to existing paragraph
                        CatStr = IIf(InStr(1, CatStr, FieldStr, vbTextCompare) = 0, _
                                CatStr & RowStr & vbCrLf, _
                                CatStr)
                        If CompareIndex > -1 _
                        Then
                            Select Case CompareIndex
                            Case LKUP_LcID
                                AllDone = IIf(ColIdx = LKUP_LNAME, True, False)
                            Case LKUP_VNID, LKUP_ASSORT
                                AllDone = IIf(ColIdx = LKUP_ASSORT, True, False)
                            Case LKUP_ITEM
                                AllDone = IIf(ColIdx = LKUP_BYID, True, False)
                            End Select
                            If AllDone Then
                                BuildLookupClause = CatStr
                                Exit Function
                            End If
                        End If  'Check CompareIndex for validity
                    End If  'Check RowStr for validity
                End If  'Check FieldStr for validity
            Next ColIdx
        End With    'Me.txtLkUp
    End Select
    
    'Return paragraph
    BuildLookupClause = CatStr
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    f_HandleErr , , , "AIM_ForecastFilter::BuildLookupClause", Now, gDRGeneralError, True, Err
End Function

Private Sub ReInitOptions()
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(m_rsOptions) Then m_rsOptions.Close
    Set m_rsOptions = Nothing
    Set m_rsOptions = New ADODB.Recordset
    With m_rsOptions
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        .MaxRecords = Me.txtMaxRows.Text + 1
    End With
    
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    f_HandleErr , , , "AIM_ForecastFilter::ReInitOptions", Now, gDRGeneralError, True, Err
End Sub

Private Function RedisplayOptions(p_OptionIndex As Integer) As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim RefetchOptions_Sp As ADODB.Command
    Dim SQLQry As String
    Dim Predicates As String
    Dim tempInt As Integer
    Dim CompType As String
    
    'Default to failure
    RedisplayOptions = -1
    
    'Clear out existing data
    ReInitOptions
    Predicates = BuildLookupClause("", p_OptionIndex)
    If StrComp(Predicates, COMP_ERROR, vbTextCompare) = 0 Then Exit Function
    
    Select Case p_OptionIndex
    Case CRIT_IDX_ITSTAT
        SQLQry = "SELECT ItStatus.ItStat, ItStatus.ItStatDesc" & vbCrLf
        SQLQry = SQLQry & "FROM ItStatus" & vbCrLf
        SQLQry = SQLQry & "WHERE ItStatus.ItStat IS NOT NULL" & vbCrLf
        SQLQry = SQLQry & "AND LTRIM(RTRIM(ItStatus.ItStat)) <> ''" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY ItStatus.ItStat, ItStatus.ItStatDesc" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY ItStatus.ItStat DESC, ItStatus.ItStatDesc"
        Else
            SQLQry = SQLQry & "ORDER BY ItStatus.ItStat, ItStatus.ItStatDesc"
        End If
    Case CRIT_IDX_LSTATUS
        SQLQry = "SELECT AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        SQLQry = SQLQry & "FROM AIMCodeLookUp" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMCodeLookUp.CodeType = N'" & g_CODETYPE_LSTATUS & "'" & vbCrLf
        SQLQry = SQLQry & "AND AIMCodeLookUp.LangID = N'" & gLangID & "'" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID DESC, AIMCodeLookUp.CodeDesc"
        Else
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc"
        End If
        
    Case CRIT_IDX_LDIVISION
        SQLQry = "SELECT AIMLocations.LDivision" & vbCrLf
        SQLQry = SQLQry & "FROM AIMLocations" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMLocations.LDivision IS NOT NULL" & vbCrLf
        SQLQry = SQLQry & "AND LTRIM(RTRIM(AIMLocations.LDivision)) <> ''" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMLocations.LDivision" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMLocations.LDivision DESC"
        Else
            SQLQry = SQLQry & "ORDER BY AIMLocations.LDivision"
        End If
    Case CRIT_IDX_LREGION
        SQLQry = "SELECT AIMLocations.LRegion" & vbCrLf
        SQLQry = SQLQry & "FROM AIMLocations" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMLocations.LRegion IS NOT NULL" & vbCrLf
        SQLQry = SQLQry & "AND LTRIM(RTRIM(AIMLocations.LRegion)) <> ''" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMLocations.LRegion" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMLocations.LRegion DESC"
        Else
            SQLQry = SQLQry & "ORDER BY AIMLocations.LRegion"
        End If
    Case CRIT_IDX_LUSERDEFINED
        SQLQry = "SELECT AIMLocations.LUserDefined" & vbCrLf
        SQLQry = SQLQry & "FROM AIMLocations" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMLocations.LUserDefined IS NOT NULL" & vbCrLf
        SQLQry = SQLQry & "AND LTRIM(RTRIM(AIMLocations.LUserDefined)) <> ''" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMLocations.LUserDefined" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMLocations.LUserDefined DESC"
        Else
            SQLQry = SQLQry & "ORDER BY AIMLocations.LUserDefined"
        End If

    Case CRIT_IDX_CLASS1, CRIT_IDX_CLASS2, CRIT_IDX_CLASS3, CRIT_IDX_CLASS4
        Select Case p_OptionIndex
        Case CRIT_IDX_CLASS1
            tempInt = 1
        Case CRIT_IDX_CLASS2
            tempInt = 2
        Case CRIT_IDX_CLASS3
            tempInt = 3
        Case CRIT_IDX_CLASS4
            tempInt = 4
        End Select
        SQLQry = "SELECT Class, ClassDesc" & vbCrLf
        SQLQry = SQLQry & "FROM AIMClasses" & vbCrLf
        SQLQry = SQLQry & "WHERE ClassLevel = " & CStr(tempInt) & vbCrLf
        SQLQry = SQLQry & "AND LangID = N'" & gLangID & "'" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY Class, ClassDesc" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY Class DESC, ClassDesc"
        Else
            SQLQry = SQLQry & "ORDER BY Class, ClassDesc"
        End If
        
    Case CRIT_IDX_BYID
        SQLQry = "SELECT UserID, UserName" & vbCrLf
        SQLQry = SQLQry & " FROM AIMUsers" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMUsers.UserID IS NOT NULL AND LTRIM(RTRIM(AIMUsers.UserID)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMUsers.UserID DESC"
        Else
            SQLQry = SQLQry & " ORDER BY AIMUsers.UserID"
        End If
        
    Case CRIT_IDX_LCID
        SQLQry = "SELECT LcID, LName, LType, LStatus, LDivision, LRegion," & vbCrLf
        SQLQry = SQLQry & " LUserDefined, Last_FcstUpdCyc" & vbCrLf
        SQLQry = SQLQry & " FROM AIMLocations" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMLocations.LcID IS NOT NULL AND LTRIM(RTRIM(AIMLocations.LcID)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMLocations.LcID DESC"
        Else
            SQLQry = SQLQry & " ORDER BY AIMLocations.LcID"
        End If

    Case CRIT_IDX_VNID
        SQLQry = "SELECT AIMVendors.VnID, AIMVendors.VName, COUNT(Assort) As 'Total Assortments'," & vbCrLf
        SQLQry = SQLQry & " AIMVendors.VnComments1 , AIMVendors.VnComments2" & vbCrLf
        SQLQry = SQLQry & " FROM AIMVendors" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMVendors.VnID IS NOT NULL AND LTRIM(RTRIM(AIMVendors.VnID)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        SQLQry = SQLQry & " GROUP BY AIMVendors.VnID, AIMVendors.VName, AIMVendors.VnComments1, AIMVendors.VnComments2" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMVendors.VnID DESC, AIMVendors.VName"
        Else
            SQLQry = SQLQry & " ORDER BY AIMVendors.VnID, AIMVendors.VName"
        End If
        
    Case CRIT_IDX_ASSORT
        SQLQry = "SELECT AIMVendors.Assort, COUNT(AIMVendors.VnID) As 'Total Vendors'" & vbCrLf
        SQLQry = SQLQry & " FROM AIMVendors" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMVendors.VnID IS NOT NULL AND LTRIM(RTRIM(AIMVendors.VnID)) <> '')" & vbCrLf
        SQLQry = SQLQry & " AND (AIMVendors.Assort IS NOT NULL AND LTRIM(RTRIM(AIMVendors.Assort)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        SQLQry = SQLQry & " GROUP BY AIMVendors.Assort" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMVendors.Assort DESC"
        Else
            SQLQry = SQLQry & " ORDER BY AIMVendors.Assort"
        End If
    Case CRIT_IDX_DFTBYID
        SQLQry = "SELECT UserID, UserName " & vbCrLf
        SQLQry = SQLQry & " From AIMUsers" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMUsers.UserID IS NOT NULL AND LTRIM(RTRIM(AIMUsers.UserID)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMUsers.UserID DESC"
        Else
            SQLQry = SQLQry & " ORDER BY AIMUsers.UserID"
        End If

    Case CRIT_IDX_DFTREVIEWERID
        SQLQry = "SELECT UserID, UserName " & vbCrLf
        SQLQry = SQLQry & " From AIMUsers" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMUsers.UserID IS NOT NULL AND LTRIM(RTRIM(AIMUsers.UserID)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMUsers.UserID DESC"
        Else
            SQLQry = SQLQry & " ORDER BY AIMUsers.UserID"
        End If
    Case CRIT_IDX_DEMAMADSOURCE
        SQLQry = "SELECT AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        SQLQry = SQLQry & "FROM AIMCodeLookUp" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMCodeLookUp.CodeType = N'" & "DEMANDSOURCE" & "'" & vbCrLf
        SQLQry = SQLQry & "AND AIMCodeLookUp.LangID = N'" & gLangID & "'" & vbCrLf
        SQLQry = SQLQry & Predicates
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID DESC, AIMCodeLookUp.CodeDesc"
        Else
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc"
        End If
    Case CRIT_IDX_LEADTIMESOURCE
        SQLQry = "SELECT AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        SQLQry = SQLQry & "FROM AIMCodeLookUp" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMCodeLookUp.CodeType = N'" & "LEADTIMESOURCE" & "'" & vbCrLf
        SQLQry = SQLQry & "AND AIMCodeLookUp.LangID = N'" & gLangID & "'" & vbCrLf
        SQLQry = SQLQry & Predicates
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID DESC, AIMCodeLookUp.CodeDesc"
        Else
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc"
        End If
    Case CRIT_IDX_LTYPE
        SQLQry = "SELECT AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        SQLQry = SQLQry & "FROM AIMCodeLookUp" & vbCrLf
        SQLQry = SQLQry & "WHERE AIMCodeLookUp.CodeType = N'" & "LTYPE" & "'" & vbCrLf
        SQLQry = SQLQry & "AND AIMCodeLookUp.LangID = N'" & gLangID & "'" & vbCrLf
        SQLQry = SQLQry & Predicates
        'Find comparison
        SQLQry = SQLQry & IIf(prm_LockExisting, Predicates, COMP_EMPTY)
        'Add groups and orders
        SQLQry = SQLQry & "GROUP BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc" & vbCrLf
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID DESC, AIMCodeLookUp.CodeDesc"
        Else
            SQLQry = SQLQry & "ORDER BY AIMCodeLookUp.CodeID, AIMCodeLookUp.CodeDesc"
        End If
    Case CRIT_IDX_LRANK
        SQLQry = "SELECT distinct(lRank) " & " as LRANK " & vbCrLf
        SQLQry = SQLQry & " From AIMLocations" & vbCrLf
        SQLQry = SQLQry & " WHERE (AIMLocations.LRank IS NOT NULL AND LTRIM(RTRIM(AIMLocations.LRank)) <> '')" & vbCrLf
        'Find comparison
        SQLQry = SQLQry & Predicates
        'Add groups and orders
        If InStr(1, Predicates, COMP_LTEQ, vbTextCompare) > 0 Then
            SQLQry = SQLQry & " ORDER BY AIMLocations.LRank DESC"
        Else
            SQLQry = SQLQry & " ORDER BY AIMLocations.LRank"
        End If
    Case CRIT_IDX_DROPSHIP
    Case Else
        Exit Function
    End Select
    
    'Command object for the Stored Procedure
    Set RefetchOptions_Sp = New ADODB.Command
    With RefetchOptions_Sp
        Set .ActiveConnection = prm_Cn
        .CommandType = adCmdText
        .CommandText = SQLQry
    End With
    
    'Fetch data
    m_rsOptions.Open RefetchOptions_Sp
    
    'CleanUp
    If Not (RefetchOptions_Sp Is Nothing) Then Set RefetchOptions_Sp.ActiveConnection = Nothing
    Set RefetchOptions_Sp = Nothing
    
    'Refresh storage grid
    RtnCode = ResetOptions(p_OptionIndex)

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    Debug.Print SQLQry
    
    If f_IsRecordsetValidAndOpen(m_rsOptions) Then m_rsOptions.Close
    Set m_rsOptions = Nothing
    If Not (RefetchOptions_Sp Is Nothing) Then Set RefetchOptions_Sp.ActiveConnection = Nothing
    Set RefetchOptions_Sp = Nothing
    f_HandleErr , , , "AIM_ForecastFilter::RedisplayOptions", Now, gDRGeneralError, True, Err
End Function

Private Function ResetOptions(p_RecIndex As Integer) As Long
On Error GoTo ErrorHandler

    Dim TDBGOptions As TDBGrid
    Dim TDBGStyle As TrueOleDBGrid80.Style
    Dim ColOptions As TrueOleDBGrid80.Column
    Dim ColIdx As Integer
    Dim RowCount As Long, ColCount As Integer
    
    'Initialize
    ResetOptions = -1
    RowCount = 0
    ColCount = 0
    
    Set TDBGOptions = Me.dgOptions
    
    Me.dcMultiCompare.Enabled = True
    'Re-arrange the contents of m_xaOptions
    RefreshOptions p_RecIndex
    'Re-open grid
    With TDBGOptions
        .Visible = False
        'Clear out existing stuff
        While .Columns.Count > 0
            .Columns.Remove 0
        Wend
        'Re-populate, if any records are available for display
        If f_IsRecordsetOpenAndPopulated(m_rsOptions) Then
            RowCount = (m_rsOptions.RecordCount - 1)
            ColCount = (m_rsOptions.Fields.Count - 1)
            While .Columns.Count < m_xaOptions.Count(2)
                Set ColOptions = .Columns.Add(0)
                ColOptions.Visible = True
            Wend
            Set ColOptions = Nothing
            
            .Columns(0).ValueItems.Presentation = dbgCheckBox
            .Splits(0).Columns(0).Alignment = dbgCenter
            .Splits(0).Columns(0).Width = 500
            For ColIdx = 0 To ColCount
                'NOTE: The first grid column is a checkbox, so move up one.
                .Columns(ColIdx + 1).Caption = _
                    getTranslationResource(m_rsOptions.Fields(ColIdx).Name)
            Next ColIdx
            'Value
            .Splits(0).Columns(1).Alignment = dbgLeft
            .Splits(0).Columns(1).Width = 2000
            'Description
            If m_rsOptions.Fields.Count > 1 Then
                .Splits(0).Columns(2).Alignment = dbgLeft
                .Splits(0).Columns(2).Width = 4000
            End If
            
            Set TDBGStyle = New TrueOleDBGrid80.Style
            TDBGStyle.Locked = prm_LockExisting
            .Columns(0).AddRegexCellStyle dbgNormalCell, TDBGStyle, CStr(vbGrayed)
            Set TDBGStyle = Nothing
            
        End If
        .ReBind
        .Refresh
        'Adjust user-interface, and destroy transient objects
        AdjustColumnWidths TDBGOptions, ACW_EXPAND
        If RowCount < Me.txtMaxRows.Text Then RowCount = RowCount + 1
        .Caption = getTranslationResource( _
            "Number of criterion options displayed: ") & _
            CStr(RowCount)
        .MoveFirst
        .Visible = True
        
    End With
    
    Set TDBGOptions = Nothing
    'Show criterion filters, if applicable
    ToggleLookupFrame p_RecIndex
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Me.dgOptions.Visible = True
    TDBGOptions.Visible = True
    Set ColOptions = Nothing
    Set TDBGOptions = Nothing
    Set TDBGStyle = Nothing
    Write_Message COMP_EMPTY
    f_HandleErr , , , "AIM_ForecastFilter::ResetOptions", Now, gDRGeneralError, True, Err

End Function

Private Sub RefreshOptions(p_RecIndex As Integer)
On Error GoTo ErrorHandler

    Dim RowCount As Long, ColCount As Integer, IndexCounter As Long
    Dim RowIdx As Long, ColIdx As Integer
    Dim CompareAs As String
    Dim IsChecked As Integer
    
    CompareAs = COMP_EMPTY
    
    ''Print out contents of the criteria array
    'g_PrintCriteria m_xaAllCriteria
    
    'Reset data source for the storage grid
    RowCount = 0
    ColCount = 0
    m_xaOptions.Clear
    m_xaOptions.ReDim 0, 0, 0, 0
    
    If f_IsRecordsetOpenAndPopulated(m_rsOptions) Then
        m_rsOptions.MoveFirst
        RowCount = (m_rsOptions.RecordCount - 1)
        ColCount = (m_rsOptions.Fields.Count - 1)
        m_xaOptions.ReDim 0, RowCount, 0, ColCount + 1
        m_CountSelOptions = 0
        'Populate the array/grid
        For RowIdx = 0 To RowCount
            For ColIdx = 0 To ColCount
                If ColIdx = 0 Then
                    m_xaOptions(RowIdx, ColIdx) = vbUnchecked
                    If p_RecIndex >= m_xaAllCriteria.LowerBound(1) _
                    And m_xaAllCriteria.UpperBound(2) > CRIT_OFFSET _
                    Then
                        'Check for existing selections, else default to unchecked
                        If Not IsEmpty(m_xaAllCriteria(p_RecIndex, CRIT_OFFSET + 1)) Then
                            CompareAs = m_xaAllCriteria(p_RecIndex, CRIT_OFFSET)
                            'Lock existing filters if comparison = NOT IN
                            With m_xaOrigCriteria
                                For IndexCounter = (CRIT_OFFSET + 1) To .UpperBound(2)
                                    If StrComp(Trim$(.Value(p_RecIndex, CRIT_OFFSET)), COMP_NOTIN, vbTextCompare) = 0 Then
                                        IsChecked = IIf(prm_LockExisting, vbGrayed, vbChecked)
                                    Else
                                        IsChecked = vbChecked
                                    End If
                                    If StrComp(Trim$(m_rsOptions.Fields(ColIdx).Value), _
                                            Trim$(.Value(p_RecIndex, IndexCounter)), _
                                            vbTextCompare) = 0 Then
                                        m_xaOptions(RowIdx, 0) = IsChecked
                                        'Lock the multicompare for this field
                                        Me.dcMultiCompare.Enabled = Not prm_LockExisting
                                        Exit For
                                    End If
                                Next IndexCounter
                            End With
                            'Now, add in new filters
                            With m_xaAllCriteria
                                For IndexCounter = (CRIT_OFFSET + 1) To .UpperBound(2)
                                    If m_xaOptions(RowIdx, 0) <> vbGrayed Then
                                        If StrComp(m_rsOptions.Fields(ColIdx).Value, _
                                                .Value(p_RecIndex, IndexCounter), _
                                                vbTextCompare) = 0 Then
                                            m_xaOptions(RowIdx, 0) = vbChecked
                                            Exit For
                                        End If
                                    End If
                                Next IndexCounter
                            End With
                        End If
                    End If
                    m_CountSelOptions = IIf(m_xaOptions(RowIdx, 0) = vbUnchecked, (m_CountSelOptions - 1), (m_CountSelOptions + 1))
                End If
                m_xaOptions(RowIdx, (ColIdx + 1)) = m_rsOptions.Fields(ColIdx).Value
                If p_RecIndex = CRIT_IDX_ITEM Then
                    If ColIdx >= 2 Then Exit For
                End If
            Next ColIdx
            m_rsOptions.MoveNext
        Next RowIdx
    End If

    If CompareAs <> COMP_EMPTY Then Me.dcMultiCompare.Value = CompareAs
    
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    Write_Message COMP_EMPTY
    f_HandleErr , , , "AIM_ForecastFilter::RefreshOptions", Now, gDRGeneralError, True, Err
End Sub

Private Sub RefreshCriteria(p_OptionIndex As Integer)
On Error GoTo ErrorHandler

    Dim OptionCount As Integer
    Dim RowIdx As Long, PosIndex As Long
    Dim ThisField As String, CompareAs As String
    Dim CriterionRow As String, NewValues As String
    Dim AllCriteria As String
    Dim RtnCode As Long, IndexCounter As Long
    Dim CompareResult As Integer
    
    If IsEmpty(m_xaOptions(0, 0)) Then Exit Sub
    Me.dgOptions.Update
    ThisField = COMP_EMPTY
    CompareAs = COMP_EMPTY
    CriterionRow = COMP_EMPTY
    NewValues = COMP_EMPTY
    'Insert selected rows into the selected-criteria array
    'In this array, Rows = values (1 to n)
    '   and Cols = criteria (ItStat, LStatus et al)
    Select Case p_OptionIndex
    Case CRIT_IDX_ASSORT
        ThisField = CRIT_COL_ASSORT
    Case CRIT_IDX_BYID
        ThisField = CRIT_COL_BYID
    Case CRIT_IDX_CLASS1
        ThisField = CRIT_COL_CLASS1
    Case CRIT_IDX_CLASS2
        ThisField = CRIT_COL_CLASS2
    Case CRIT_IDX_CLASS3
        ThisField = CRIT_COL_CLASS3
    Case CRIT_IDX_CLASS4
        ThisField = CRIT_COL_CLASS4
    Case CRIT_IDX_ITEM
        ThisField = CRIT_COL_ITEM
    Case CRIT_IDX_ITSTAT
        ThisField = CRIT_COL_ITSTAT
    Case CRIT_IDX_LCID
        ThisField = CRIT_COL_LCID
    Case CRIT_IDX_LDIVISION
        ThisField = CRIT_COL_LDIVISION
    Case CRIT_IDX_LREGION
        ThisField = CRIT_COL_LREGION
    Case CRIT_IDX_LSTATUS
        ThisField = CRIT_COL_LSTATUS
    Case CRIT_IDX_LUSERDEFINED
        ThisField = CRIT_COL_LUSERDEFINED
    Case CRIT_IDX_VNID
        ThisField = CRIT_COL_VNID
     Case CRIT_IDX_DFTBYID
        ThisField = CRIT_COL_DFTBYID
     Case CRIT_IDX_DFTREVIEWERID
        ThisField = CRIT_COL_DFTREVIEWERID
     Case CRIT_IDX_DEMAMADSOURCE
        ThisField = CRIT_COL_DEMAMADSOURCE
     Case CRIT_IDX_LEADTIMESOURCE
        ThisField = CRIT_COL_LEADTIMESOURCE
     Case CRIT_IDX_LTYPE
        ThisField = CRIT_COL_LTYPE
     Case CRIT_IDX_LRANK
        ThisField = CRIT_COL_LRANK
     Case CRIT_IDX_DROPSHIP
        ThisField = CRIT_COL_DROPSHIP
    End Select
    'Set comparison operator
    CompareAs = Me.dcMultiCompare.Value
    'Now, fetch values
    CriterionRow = COMP_EMPTY
    'Check for existing selections, else default to unchecked
    With m_xaOrigCriteria
        For IndexCounter = (CRIT_OFFSET + 1) To .UpperBound(2)
            RowIdx = m_xaOptions.UpperBound(1)
            CompareResult = StrComp(Trim$(.Value(p_OptionIndex, IndexCounter)), _
                    Trim$(m_xaOptions(RowIdx, 1)), _
                    vbTextCompare)
            If CompareResult >= 1 Then    'the original set contains more values than the ones in the options
                RowIdx = RowIdx + 1
                m_xaOptions.InsertRows RowIdx
                m_xaOptions(RowIdx, 0) = vbChecked  'Status
                m_xaOptions(RowIdx, 1) = .Value(p_OptionIndex, IndexCounter)  'Value
            End If
        Next IndexCounter
    End With

    With m_xaOptions
        CriterionRow = COMP_EMPTY
        For OptionCount = .LowerBound(1) To .UpperBound(1)
            'Check first column of the options for vbchecked
            If .Value(OptionCount, 0) <> vbUnchecked Then
                CriterionRow = CriterionRow & _
                                Trim$(.Value(OptionCount, 1)) & _
                                DELIM_COLUMN
                NewValues = NewValues & IIf(.Value(OptionCount, 0) <> vbGrayed, _
                            .Value(OptionCount, 1) & DELIM_COLUMN, _
                            COMP_EMPTY)
                            
            End If
        Next OptionCount
    End With
    'Store all criteria
    CriterionRow = Trim$(CriterionRow)
    PosIndex = InStrRev(CriterionRow, DELIM_COLUMN)
    If PosIndex > 0 Then
        CriterionRow = Left$(CriterionRow, (PosIndex - 1))
    Else
        CriterionRow = COMP_EMPTY
    End If
    RtnCode = g_CLineToXArr(m_xaAllCriteria, _
                p_OptionIndex, _
                ThisField, _
                CompareAs, _
                CriterionRow, _
                NewValues)
    If prm_LockExisting = True Then
        'Record the deltas
        NewValues = Trim$(NewValues)
        PosIndex = InStrRev(NewValues, DELIM_COLUMN)
        If PosIndex > 0 Then
            NewValues = Left$(NewValues, (PosIndex - 1))
        Else
            NewValues = COMP_EMPTY
        End If
        RtnCode = g_CLineToXArr(m_xaNewCriteria, _
                    p_OptionIndex, _
                    ThisField, _
                    CompareAs, _
                    NewValues, _
                    CriterionRow)
    End If
    g_PrintCriteria m_xaOptions
    g_PrintCriteria m_xaOrigCriteria
    g_PrintCriteria m_xaAllCriteria
     g_PrintCriteria m_xaNewCriteria
    'Concatenate selected options into a string for
    '   the user's at-a-glance reference
    Me.txtSelected.Text = g_CXArrToPara(m_xaAllCriteria, False, False)
    If Trim$(Me.txtSelected.Text) = COMP_EMPTY Then
        g_InitCriteria m_xaAllCriteria
    End If
    
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    Write_Message COMP_EMPTY
    f_HandleErr , , , "AIM_ForecastFilter::RefreshCriteria", Now, gDRGeneralError, True, Err
End Sub

Private Function ToggleLookupFrame(p_OptionIndex As Integer) As Long
On Error GoTo ErrorHandler
    
    Dim IsEnabled As Boolean
    Dim RowStr As String
    Dim RowIdx As Long, ColIdx As Long, PosIndex As Long
    
    'Highlight the selected criterion FROM the option-button group
    With Me.optCriterion
        For ColIdx = .LBound To .UBound
            .Item(ColIdx).FontBold = IIf(ColIdx = p_OptionIndex, True, False)
        Next ColIdx
    End With
    
    'Set defaults to invisible
    IsEnabled = False
    lblLkUp(LKUP_VNID).Visible = IsEnabled
    txtLkUp(LKUP_VNID).Visible = IsEnabled
    lblLkUp(LKUP_ASSORT).Visible = IsEnabled
    txtLkUp(LKUP_ASSORT).Visible = IsEnabled
    
    lblLkUp(LKUP_LcID).Visible = IsEnabled
    txtLkUp(LKUP_LcID).Visible = IsEnabled
    lblLkUp(LKUP_LNAME).Visible = IsEnabled
    txtLkUp(LKUP_LNAME).Visible = IsEnabled
    
    lblLkUp(LKUP_ITEM).Visible = IsEnabled
    txtLkUp(LKUP_ITEM).Visible = IsEnabled
    lblLkUp(LKUP_ITDESC).Visible = IsEnabled
    txtLkUp(LKUP_ITDESC).Visible = IsEnabled
    
    lblLkUp(LKUP_BYID).Visible = IsEnabled
    txtLkUp(LKUP_BYID).Visible = IsEnabled
    
    Select Case p_OptionIndex
    Case CRIT_IDX_LCID
        IsEnabled = True
        lblLkUp(LKUP_LcID).Visible = IsEnabled
        txtLkUp(LKUP_LcID).Visible = IsEnabled
        lblLkUp(LKUP_LNAME).Visible = IsEnabled
        txtLkUp(LKUP_LNAME).Visible = IsEnabled
        
    Case CRIT_IDX_VNID, CRIT_IDX_ASSORT
        IsEnabled = True
        lblLkUp(LKUP_VNID).Visible = IsEnabled
        txtLkUp(LKUP_VNID).Visible = IsEnabled
        lblLkUp(LKUP_ASSORT).Visible = IsEnabled
        txtLkUp(LKUP_ASSORT).Visible = IsEnabled
    
    Case CRIT_IDX_ITEM
        IsEnabled = True
        lblLkUp(LKUP_VNID).Visible = IsEnabled
        txtLkUp(LKUP_VNID).Visible = IsEnabled
        lblLkUp(LKUP_ASSORT).Visible = IsEnabled
        txtLkUp(LKUP_ASSORT).Visible = IsEnabled
        
        lblLkUp(LKUP_LcID).Visible = IsEnabled
        txtLkUp(LKUP_LcID).Visible = IsEnabled
        lblLkUp(LKUP_LNAME).Visible = IsEnabled
        txtLkUp(LKUP_LNAME).Visible = IsEnabled
        
        lblLkUp(LKUP_ITEM).Visible = IsEnabled
        txtLkUp(LKUP_ITEM).Visible = IsEnabled
        lblLkUp(LKUP_ITDESC).Visible = IsEnabled
        txtLkUp(LKUP_ITDESC).Visible = IsEnabled
        
        lblLkUp(LKUP_BYID).Visible = IsEnabled
        txtLkUp(LKUP_BYID).Visible = IsEnabled
    
    End Select
    
    Me.dgOptions.Height = IIf(IsEnabled = True, 3255, 5295)
    Me.frmLookup.Visible = IsEnabled
    
    'Now, concatenate selected options into a string for
    '   the user's at-a-glance reference
    Me.txtSelected.Text = g_CXArrToPara(m_xaAllCriteria, False, False)
    If Me.txtSelected.Text <> COMP_EMPTY Then
        For RowIdx = m_xaAllCriteria.LowerBound(1) To m_xaAllCriteria.UpperBound(1)
            RowStr = COMP_EMPTY
            RowStr = g_CXArrToLine(m_xaAllCriteria, RowIdx, False, True)
            If Trim$(RowStr) <> COMP_EMPTY Then
                Select Case m_xaAllCriteria(RowIdx, 0)
                Case CRIT_COL_ITEM
                    Me.txtLkUp(LKUP_ITEM).Text = RowStr
                    Me.txtLkUp(LKUP_ITEM).ReadOnly = True
                Case CRIT_COL_BYID
                    Me.txtLkUp(LKUP_BYID).Text = RowStr
                    Me.txtLkUp(LKUP_BYID).ReadOnly = True
                Case CRIT_COL_LCID
                    Me.txtLkUp(LKUP_LcID).Text = RowStr
                    Me.txtLkUp(LKUP_LcID).ReadOnly = True
                Case CRIT_COL_VNID
                    Me.txtLkUp(LKUP_VNID).Text = RowStr
                    Me.txtLkUp(LKUP_VNID).ReadOnly = True
                Case CRIT_COL_ASSORT
                    Me.txtLkUp(LKUP_ASSORT).Text = RowStr
                    Me.txtLkUp(LKUP_ASSORT).ReadOnly = True
                End Select
            End If
        Next RowIdx
    End If
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    Write_Message COMP_EMPTY
    f_HandleErr , , , "AIM_ForecastFilter::ToggleLookupFrame", Now, gDRGeneralError, True, Err
End Function

Public Sub CleanUpObjects()
On Error Resume Next
    
    'Clean Up
    If g_IsArray(m_xaAllCriteria) Then
        m_xaAllCriteria.Clear
    End If
    Set m_xaAllCriteria = Nothing

    If g_IsArray(m_xaNewCriteria) Then
        m_xaNewCriteria.Clear
    End If
    Set m_xaNewCriteria = Nothing

    If g_IsArray(m_xaOrigCriteria) Then
        m_xaOrigCriteria.Clear
    End If
    Set m_xaOrigCriteria = Nothing
    
    If g_IsArray(m_xaOptions) Then
        m_xaOptions.Clear
    End If
    Set m_xaOptions = Nothing

    If g_IsArray(m_xaResults) Then
        m_xaResults.Clear
    End If
    Set m_xaResults = Nothing

    If f_IsRecordsetValidAndOpen(m_rsOptions) Then m_rsOptions.Close
    Set m_rsOptions = Nothing
    
End Sub

