VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_InitializeNewLocation 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Initialize New Location"
   ClientHeight    =   3255
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   5895
   Icon            =   "AIM_InitializeNewLocation.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   3255
   ScaleWidth      =   5895
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Frame1 
      Height          =   2655
      Left            =   120
      TabIndex        =   0
      Top             =   45
      Width           =   5655
      Begin VB.CheckBox ckDeleteHistory 
         Caption         =   "Delete all history for destination location"
         Height          =   340
         Left            =   120
         TabIndex        =   10
         Top             =   2160
         Width           =   5115
      End
      Begin VB.CheckBox ckDeleteOption 
         Caption         =   "Delete all Items at destination location"
         Height          =   340
         Left            =   120
         TabIndex        =   9
         Top             =   1780
         Width           =   5115
      End
      Begin TDBNumber6Ctl.TDBNumber txtHistoryScalingFactor 
         Height          =   345
         Left            =   3195
         TabIndex        =   6
         Top             =   1010
         Width           =   1485
         _Version        =   65536
         _ExtentX        =   2619
         _ExtentY        =   609
         Calculator      =   "AIM_InitializeNewLocation.frx":030A
         Caption         =   "AIM_InitializeNewLocation.frx":032A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_InitializeNewLocation.frx":0396
         Keys            =   "AIM_InitializeNewLocation.frx":03B4
         Spin            =   "AIM_InitializeNewLocation.frx":03FE
         AlignHorizontal =   1
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "0.000;-0.000;0.000;0.000"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "0.000"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9.999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011627525
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcSourceLcId 
         Bindings        =   "AIM_InitializeNewLocation.frx":0426
         Height          =   345
         Left            =   3195
         TabIndex        =   2
         Top             =   240
         Width           =   1485
         DataFieldList   =   "lcid"
         AllowInput      =   0   'False
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2619
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "lcid"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDestinationLcId 
         Height          =   345
         Left            =   3195
         TabIndex        =   4
         Top             =   625
         Width           =   1485
         DataFieldList   =   "lcid"
         AllowInput      =   0   'False
         _Version        =   196617
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2619
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin TDBDate6Ctl.TDBDate txtScalingEffUntil 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   345
         Left            =   3195
         TabIndex        =   8
         Top             =   1395
         Width           =   1485
         _Version        =   65536
         _ExtentX        =   2619
         _ExtentY        =   609
         Calendar        =   "AIM_InitializeNewLocation.frx":0443
         Caption         =   "AIM_InitializeNewLocation.frx":055B
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_InitializeNewLocation.frx":05C7
         Keys            =   "AIM_InitializeNewLocation.frx":05E5
         Spin            =   "AIM_InitializeNewLocation.frx":0643
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/15/2001"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37179
         CenturyMode     =   0
      End
      Begin VB.Label Label4 
         Caption         =   "Scaling Effective Until"
         Height          =   300
         Left            =   120
         TabIndex        =   7
         Top             =   1440
         Width           =   2805
      End
      Begin VB.Label Label3 
         Caption         =   "History Scaling Factor"
         Height          =   300
         Left            =   120
         TabIndex        =   5
         Top             =   1055
         Width           =   2805
      End
      Begin VB.Label Label2 
         Caption         =   "Destination"
         Height          =   300
         Left            =   120
         TabIndex        =   3
         Top             =   670
         Width           =   2805
      End
      Begin VB.Label Label1 
         Caption         =   "Source"
         Height          =   300
         Left            =   120
         TabIndex        =   1
         Top             =   285
         Width           =   2805
      End
   End
   Begin ActiveToolBars.SSActiveToolBars atbNavigate 
      Left            =   120
      Top             =   2760
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Tools           =   "AIM_InitializeNewLocation.frx":066B
      ToolBars        =   "AIM_InitializeNewLocation.frx":2000
   End
End
Attribute VB_Name = "AIM_InitializeNewLocation"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_InitializeNewLocation_Sp As ADODB.Command

Dim rsAIMLocations As ADODB.Recordset

Private Sub atbNavigate_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim DmdScalingFactor As Double
    Dim Heading As String
    Dim RtnCode As Long
    Dim ScalingEffUntil As Date
    Dim SourceLcId As String
    Dim TargetLcid As String
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
        
        Case "ID_Update"
            'Check for parameter errors
            DmdScalingFactor = Me.txtHistoryScalingFactor.Value
            Heading = getTranslationResource("Initialize New Location")
            ScalingEffUntil = Me.txtScalingEffUntil.Value
            SourceLcId = Trim(Me.dcSourceLcId.Columns("LcID").Value)
            TargetLcid = Trim(Me.dcDestinationLcId.Columns("LcID").Value)
            
            If SourceLcId = "" Then
                strMessage = getTranslationResource("MSGBOX07500")
                If StrComp(strMessage, "MSGBOX07500") = 0 Then strMessage = "Select a Source Location."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If
            
            If TargetLcid = "" Then
                strMessage = getTranslationResource("MSGBOX07501")
                If StrComp(strMessage, "MSGBOX07501") = 0 Then strMessage = "Select a Destination Loctaion."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If
            
            If SourceLcId = TargetLcid Then
                strMessage = getTranslationResource("MSGBOX07502")
                If StrComp(strMessage, "MSGBOX07502") = 0 Then strMessage = "Source and Destination Locations cannot be the same."
                MsgBox strMessage, vbCritical + vbOKOnly, Heading
                Exit Sub
            End If
            
            'Display warning on Delete Option
            If Me.ckDeleteOption.Value = vbChecked Or Me.ckDeleteHistory.Value = vbChecked Then
                strMessage = getTranslationResource("MSGBOX07503")
                If StrComp(strMessage, "MSGBOX07503") = 0 Then strMessage = "All Items and Item History associated with Target Location --"
                strMessage1 = getTranslationResource("MSGBOX07504")
                If StrComp(strMessage1, "MSGBOX07504") = 0 Then strMessage1 = "will be deleted."
                strMessage2 = getTranslationResource("MSGBOX07505")
                If StrComp(strMessage2, "MSGBOX07505") = 0 Then strMessage2 = "Do you wish to proceed?"
                
                RtnCode = MsgBox(strMessage & " " & TargetLcid _
                    & " " & strMessage1 & vbCrLf & vbCrLf & strMessage2, vbCritical + vbYesNo, Heading)
                    
                If RtnCode = vbNo Then
                    Exit Sub
                End If
            End If
            
            Me.MousePointer = vbHourglass
            
            strMessage = getTranslationResource("STATMSG07500")
            If StrComp(strMessage, "STATMSG07500") = 0 Then strMessage = "Initializing Location --"
            strMessage1 = getTranslationResource("STATMSG07501")
            If StrComp(strMessage1, "STATMSG07501") = 0 Then strMessage1 = "."
            Write_Message strMessage & " " & TargetLcid & strMessage1
            
            'Beef up the Connection Timeout
            Cn.CommandTimeout = 0
                
            'Initialize the New Location
            With AIM_InitializeNewLocation_Sp
                .Parameters("@SourceLcId").Value = SourceLcId
                .Parameters("@TargetLcid").Value = TargetLcid
                .Parameters("@DmdScalingFactor").Value = DmdScalingFactor
                .Parameters("@ScalingEffUntil").Value = ScalingEffUntil
                .Parameters("@DeleteOpt").Value = Me.ckDeleteOption.Value
                .Parameters("@DeleteHistoryOpt").Value = Me.ckDeleteHistory.Value
        
                ' Execute the command
                .Execute , , adExecuteNoRecords
                
                'Retrieve stored procedure return value and output parameters
                RtnCode = .Parameters(0).Value
                
                If RtnCode <> SUCCEED Then
                    strMessage = getTranslationResource("MSGBOX07506")
                    If StrComp(strMessage, "MSGBOX07506") = 0 Then strMessage = "Error Initializing New Location."
                    MsgBox strMessage & vbCrLf & vbCrLf & _
                        Cn.Errors(0).Description, vbCritical + vbOKOnly, Heading
                End If
    
                Write_Message ""
                Me.MousePointer = vbNormal
            End With
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atbNavigate_ToolClick)"
     f_HandleErr , , , "AIM_InitializeNewLocation::atbNavigate_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcSourceLcId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcSourceLcId.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcSourceLcId.Columns(0).Width = 1000
        
    Me.dcSourceLcId.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcSourceLcId.Columns(1).Width = 2880
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcSourceLcId, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcSourceLcID.Columns.Count - 1
'        dcSourceLcID.Columns(IndexCounter).HasHeadBackColor = True
'        dcSourceLcID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcSourceLcId_InitColumnProps)"
     f_HandleErr , , , "AIM_InitializeNewLocation::dcSourceLcId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcDestinationLcId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcDestinationLcId.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcDestinationLcId.Columns(0).Width = 1000
        
    Me.dcDestinationLcId.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcDestinationLcId.Columns(1).Width = 2880

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDestinationLcId, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcDestinationLcId.Columns.Count - 1
'        dcDestinationLcId.Columns(IndexCounter).HasHeadBackColor = True
'        dcDestinationLcId.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDestinationLcId_InitColumnProps)"
     f_HandleErr , , , "AIM_InitializeNewLocation::dcDestinationLcId_InitColumnProps", Now, gDRGeneralError, True, Err

End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_InitializeNewLocation::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07502")
    If StrComp(strMessage, "STATMSG07502") = 0 Then strMessage = "Initializing New Location..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Define Stored Procedures
    Set AIM_InitializeNewLocation_Sp = New ADODB.Command
    With AIM_InitializeNewLocation_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_InitializeNewLocation_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With

    'Initialize the recordset
    Set rsAIMLocations = New ADODB.Recordset
    With rsAIMLocations
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    
    End With
    
    strSQL = "SELECT LcID, LName FROM AIMLocations ORDER BY LcID"
    
    rsAIMLocations.Open strSQL, Cn, adOpenStatic, adLockReadOnly, adCmdText
        
    'Initialize the location data combos
    Set Me.dcSourceLcId.DataSourceList = rsAIMLocations
    Set Me.dcDestinationLcId.DataSourceList = rsAIMLocations
    
    Cn.Errors.Clear
    
    'Initialize the Scaling Elements
    Me.txtHistoryScalingFactor.Value = 1
    SetTDBDate txtScalingEffUntil, Date
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    '************************************************************
    'Mar 07 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtScalingEffUntil.DropDown.Visible = 1
    Me.txtHistoryScalingFactor.Spin.Visible = 1
    '************************************************************
    
    'Center the form
    Me.Top = (AIM_Main.ScaleHeight - Me.Height) \ 2
    Me.Left = (AIM_Main.ScaleWidth - Me.Width) \ 2
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_InitializeNewLocation::Form_Load", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Close the recordset
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
    If Not (AIM_InitializeNewLocation_Sp Is Nothing) Then Set AIM_InitializeNewLocation_Sp.ActiveConnection = Nothing
    Set AIM_InitializeNewLocation_Sp = Nothing

    'Close the SQL Connection
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
    'Clear messages
    Write_Message ""

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_InitializeNewLocation::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
