VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_DestinationDefault 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Destination Default Maintenance"
   ClientHeight    =   7020
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11565
   Icon            =   "AIM_DestinationDefault.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   7020
   ScaleWidth      =   11565
   ShowInTaskbar   =   0   'False
   Begin VB.Frame fraPCDetails 
      Caption         =   "Sourcing Options"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   3750
      Left            =   120
      TabIndex        =   1
      Top             =   2760
      Width           =   11325
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSourceOrder 
         Height          =   3345
         Left            =   120
         TabIndex        =   0
         Top             =   240
         Width           =   11100
         _Version        =   196617
         DataMode        =   1
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         AllowDelete     =   -1  'True
         MultiLine       =   0   'False
         AllowColumnSwapping=   0
         SelectTypeRow   =   1
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         ExtraHeight     =   212
         CaptionAlignment=   0
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   19579
         _ExtentY        =   5900
         _StockProps     =   79
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
   End
   Begin ActiveTabs.SSActiveTabs atDestinationList 
      Height          =   2565
      Left            =   120
      TabIndex        =   2
      Top             =   60
      Width           =   11325
      _ExtentX        =   19976
      _ExtentY        =   4524
      _Version        =   131083
      TabCount        =   1
      TagVariant      =   ""
      Tabs            =   "AIM_DestinationDefault.frx":030A
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   2175
         Left            =   30
         TabIndex        =   3
         Top             =   360
         Width           =   11265
         _ExtentX        =   19870
         _ExtentY        =   3836
         _Version        =   131083
         TabGuid         =   "AIM_DestinationDefault.frx":035E
         Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMUsers 
            Height          =   705
            Left            =   6840
            TabIndex        =   8
            Top             =   1080
            Width           =   1965
            DataFieldList   =   "UserID"
            _Version        =   196617
            RowHeight       =   423
            Columns(0).Width=   3200
            _ExtentX        =   3466
            _ExtentY        =   1244
            _StockProps     =   77
            DataFieldToDisplay=   "Userid"
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMLocationsRegion 
            Height          =   675
            Left            =   4680
            TabIndex        =   7
            Top             =   1080
            Width           =   1965
            DataFieldList   =   "LRegion"
            _Version        =   196617
            RowHeight       =   423
            Columns(0).Width=   3200
            _ExtentX        =   3466
            _ExtentY        =   1191
            _StockProps     =   77
            DataFieldToDisplay=   "LRegion"
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMCodeLookUp 
            Height          =   675
            Left            =   480
            TabIndex        =   5
            Top             =   1080
            Width           =   1965
            DataFieldList   =   "CodeId"
            _Version        =   196617
            RowHeight       =   423
            ExtraHeight     =   185
            Columns(0).Width=   3200
            Columns(0).DataType=   8
            Columns(0).FieldLen=   4096
            _ExtentX        =   3466
            _ExtentY        =   1191
            _StockProps     =   77
            DataFieldToDisplay=   "CodeDesc"
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMLocationsDivision 
            Height          =   675
            Left            =   2640
            TabIndex        =   6
            Top             =   1080
            Width           =   1965
            DataFieldList   =   "LDivision"
            _Version        =   196617
            RowHeight       =   423
            Columns(0).Width=   3200
            _ExtentX        =   3466
            _ExtentY        =   1191
            _StockProps     =   77
            DataFieldToDisplay=   "LDivision"
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgDestination 
            Height          =   2025
            Left            =   120
            TabIndex        =   4
            Top             =   120
            Width           =   11100
            _Version        =   196617
            DataMode        =   1
            BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            AllowDelete     =   -1  'True
            MultiLine       =   0   'False
            AllowColumnSwapping=   0
            SelectTypeRow   =   1
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            RowHeight       =   423
            ExtraHeight     =   212
            CaptionAlignment=   0
            Columns(0).Width=   3200
            Columns(0).DataType=   8
            Columns(0).FieldLen=   4096
            _ExtentX        =   19579
            _ExtentY        =   3572
            _StockProps     =   79
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "Microsoft Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
         End
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   90
      Top             =   6600
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_DestinationDefault.frx":0386
      ToolBars        =   "AIM_DestinationDefault.frx":9C10
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_DestinationDefault"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_CodeLookup_Get_Sp As ADODB.Command
Dim AIM_AllocDefaults_Delete_Sp As ADODB.Command

Dim rsDestination As ADODB.Recordset
Dim rsSourceOrder As ADODB.Recordset
Dim rsAIMCodeLookUp As ADODB.Recordset
Dim rsAIMLocationsDivision As ADODB.Recordset
Dim rsAIMLocationsRegion As ADODB.Recordset
Dim rsAIMUsers As ADODB.Recordset

Dim DestinationBookmark As Variant
Dim SourceOrderBookmark As Variant

Dim m_ActiveGridID As String
Dim m_NewRecord As Boolean
Dim m_NewDetail As Boolean

Const ACTIVEGRID_Destination As String = "DESTINATION"
Const ACTIVEGRID_SourceOrder As String = "SOURCEORDER"

Dim AllocDefaultsID As Long
Dim NewRow As Long

Dim UBArray() As String
Dim vClone As Variant
Dim iNewSize As Integer
Dim iCols As Integer
Dim iPoint As Integer
Dim iCount As Integer
Dim DestinationRequired As Boolean
Dim SourceOrderRequired  As Boolean
Dim strMessage As String
Dim RtnCode  As String
Dim iDestinationCount As Integer

Private Sub ddAIMCodeLookUp_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim EBCodeLookup As SSOleDBDropDown
        
    Me.MousePointer = vbHourglass
    
    Set EBCodeLookup = Me.ddAIMCodeLookUp
    With EBCodeLookup
        .Redraw = False
        
        IndexCounter = 0
        .Columns(IndexCounter).Name = "CodeID"
        .Columns(IndexCounter).Caption = getTranslationResource("Code")
        .Columns(IndexCounter).Width = 1500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "CodeID"

        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "CodeDesc"
        .Columns(IndexCounter).Caption = getTranslationResource("Description")
        .Columns(IndexCounter).Width = 2880
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "CodeDesc"
    End With
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBCodeLookup, ACW_EXPAND
    End If
    
    EBCodeLookup.Redraw = True
    Set EBCodeLookup = Nothing
    
    Me.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(ddAIMCodeLookUp_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "ddAIMCodeLookUp_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.ddAIMCodeLookUp.Redraw = True
    Set EBCodeLookup = Nothing
End Sub


Private Sub ddAIMLocationsDivision_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim EBLDiv As SSOleDBDropDown
        
    Me.MousePointer = vbHourglass
    
    Set EBLDiv = Me.ddAIMLocationsDivision
    With EBLDiv
        .Redraw = False
        
        IndexCounter = 0
        .Columns(IndexCounter).Name = "LDivision"
        .Columns(IndexCounter).Caption = getTranslationResource("Division")
        .Columns(IndexCounter).Width = 3500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "LDivision"
        IndexCounter = IndexCounter + 1
    End With
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBLDiv, ACW_EXPAND
    End If
    
    EBLDiv.Redraw = True
    Set EBLDiv = Nothing
    
    Me.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(ddAIMLocationsDivision_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "ddAIMLocationsDivision_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.ddAIMLocationsDivision.Redraw = True
    Set EBLDiv = Nothing
End Sub

Private Sub ddAIMLocationsRegion_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim EBLReg As SSOleDBDropDown
        
    Me.MousePointer = vbHourglass
    
    Set EBLReg = Me.ddAIMLocationsRegion
    With EBLReg
        .Redraw = False
        
        IndexCounter = 0
        .Columns(IndexCounter).Name = "LRegion"
        .Columns(IndexCounter).Caption = getTranslationResource("Region")
        .Columns(IndexCounter).Width = 3500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "LRegion"

        IndexCounter = IndexCounter + 1
    End With
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBLReg, ACW_EXPAND
    End If
    
    EBLReg.Redraw = True
    Set EBLReg = Nothing
    
    Me.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(ddAIMLocationsRegion_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "ddAIMLocationsRegion_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.ddAIMLocationsRegion.Redraw = True
    Set EBLReg = Nothing
End Sub

Private Sub ddAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    Dim EBUsers As SSOleDBDropDown
        
    Me.MousePointer = vbHourglass
    
    Set EBUsers = Me.ddAIMUsers
    With EBUsers
        .Redraw = False
        
        IndexCounter = 0
        .Columns(IndexCounter).Name = "UserId"
        .Columns(IndexCounter).Caption = getTranslationResource("Reviewer ID")
        .Columns(IndexCounter).Width = 1500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "UserId"
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "UserName"
        .Columns(IndexCounter).Caption = getTranslationResource("Name")
        .Columns(IndexCounter).Width = 3000
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).DataField = "UserName"
    End With
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBUsers, ACW_EXPAND
    End If
    
    EBUsers.Redraw = True
    Set EBUsers = Nothing
    
    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(ddAIMUsers_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "ddAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.ddAIMUsers.Redraw = True
    Set EBUsers = Nothing
End Sub

Private Sub dgDestination_AfterDelete(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    Dim iUBRow As Integer
    Dim iCol As Integer
    Dim iC As Integer
    
    If iNewSize <> -1 Then ReDim UBArray(0 To (iCols), 0 To iNewSize)
    
    iUBRow = 0
    
    'Copy data from the clone array into the
    'main array, skipping the deleted records
    For iC = 0 To UBound(vClone, 2)
        If vClone(0, iC) <> "*SS.DELETED*" Then
        For iCol = 0 To (iCols)
            UBArray(iCol, iUBRow) = vClone(iCol, iC)
        Next iCol
        
        iUBRow = iUBRow + 1
        End If
    Next iC
    
    vClone = ""
    iCount = UBound(UBArray, 2)
    
    dgDestination.Rows = iCount
    dgDestination.Refresh
    
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_AfterDelete)"
     f_HandleErr , , , Me.Caption & "dgDestination_AfterDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
On Error GoTo ErrorHandler

    'Cancel the default prompt
    DispPromptMsg = False

    iNewSize = UBound(UBArray, 2) - dgDestination.SelBookmarks.Count
    
    vClone = UBArray()

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_BeforeDelete)"
     f_HandleErr , , , Me.Caption & "dgDestination_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnValue As Integer
    
    DestinationRequired = True

    'Check if this is old row
     If dgDestination.Columns(8).Value = "OLD" Then
        'See if the primary column values have changed from their original values
        'If they've changed, then validate, else do not validate.
        'However, if it is a new row, then dgDestination.Columns(8).value will be ""
        'For a new row, we would need to call function f_DestinationOrder_Validate()
        If UBArray(1, dgDestination.Bookmark) = dgDestination.Columns("LStatus").Value _
        And UBArray(2, dgDestination.Bookmark) = dgDestination.Columns("LDivision").Value _
        And UBArray(3, dgDestination.Bookmark) = dgDestination.Columns("LRegion").Value _
        And UBArray(4, dgDestination.Bookmark) = dgDestination.Columns(4).Value _
        Then
        Else
            RtnValue = f_DestinationOrder_Validate
            If RtnValue = 0 Then
                Cancel = True
                Exit Sub
            End If
        End If
    Else
        RtnValue = f_DestinationOrder_Validate
        If RtnValue = 0 Then
            Cancel = True
            Exit Sub
        End If
    End If

    If dgDestination.Columns(5).Value < 0 Or (dgDestination.Columns(5).Value) > 100 Then
        strMessage = getTranslationResource("MSGBOX07904")
        If StrComp(strMessage, "MSGBOX07904") = 0 Then strMessage = "Exception Percent value should be between 0 and 100"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        Cancel = True
        Exit Sub
    End If
    If CInt(dgDestination.Columns(7).Value) <> (dgDestination.Columns(7).Value) Then
        strMessage = getTranslationResource("MSGBOX07905")
        If StrComp(strMessage, "MSGBOX07905") = 0 Then strMessage = "Rank should be a whole number"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        Cancel = True
        Exit Sub
    End If

    If dgDestination.Columns(7).Value < 0 _
    Or (dgDestination.Columns(7).Value) > 255 _
    Then
        strMessage = getTranslationResource("MSGBOX07906")
        If StrComp(strMessage, "MSGBOX07906") = 0 Then strMessage = "Rank should be between 0 and 255"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        MsgBox ("Rank  should be between 0 and 255")
        Cancel = True
        Exit Sub
    End If

    DestinationRequired = False

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_BeforeUpdate)"
     f_HandleErr , , , Me.Caption & "dgDestination_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_Change()
On Error GoTo ErrorHandler

    'Set toggle to active. This is used in the Toolbar_Click event
    If dgSourceOrder.DataChanged = True Then
        dgSourceOrder.Update
    End If
    m_ActiveGridID = ACTIVEGRID_Destination
   
    ToggleToolbar

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_Change)"
     f_HandleErr , , , Me.Caption & "dgDestination_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_Click()
On Error GoTo ErrorHandler

    If dgSourceOrder.DataChanged = True Then dgSourceOrder.Update

    m_ActiveGridID = ACTIVEGRID_Destination
    
    ToggleToolbar
    
    'Toggle bookmark tool
    If IsEmpty(DestinationBookmark) _
    Or IsNull(DestinationBookmark) _
    Then
        Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
    Else
        Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_Click)"
     f_HandleErr , , , Me.Caption & "dgDestination_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim EBDestination As SSOleDBGrid
        
    Me.MousePointer = vbHourglass
    
    Set EBDestination = Me.dgDestination
    With EBDestination
        .Redraw = False

        'Set grid properties
        .ROWHEIGHT = 300
        .AllowUpdate = True
        
        'Define Columns
        IndexCounter = 0
        .Columns(IndexCounter).Name = "AllocDefaultID"
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).Width = 700
        .Columns(IndexCounter).Visible = False
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "LStatus"
        .Columns(IndexCounter).Caption = getTranslationResource("Status")
        .Columns(IndexCounter).Width = 1200
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).Style = ssStyleComboBox
        .Columns(IndexCounter).ButtonsAlways = True
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).FieldLen = 20
        .Columns(IndexCounter).DataType = vbString
        .Columns(IndexCounter).DropDownHwnd = Me.ddAIMCodeLookUp.hwnd
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "LDivision"
        .Columns(IndexCounter).Caption = getTranslationResource("Division")
        .Columns(IndexCounter).Width = 1200
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).Style = ssStyleComboBox
        .Columns(IndexCounter).ButtonsAlways = True
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).FieldLen = 20
        .Columns(IndexCounter).DataType = vbString
        .Columns(IndexCounter).DropDownHwnd = Me.ddAIMLocationsDivision.hwnd
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "LRegion"
        .Columns(IndexCounter).Caption = getTranslationResource("Region")
        .Columns(IndexCounter).Width = 1200
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).Style = ssStyleComboBox
        .Columns(IndexCounter).ButtonsAlways = True
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).FieldLen = 20
        .Columns(IndexCounter).DataType = vbString
        .Columns(IndexCounter).DropDownHwnd = Me.ddAIMLocationsRegion.hwnd
           
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "LUserDefined"
        .Columns(IndexCounter).Caption = getTranslationResource("User Defined")
        .Columns(IndexCounter).Width = 1500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).Style = ssStyleEdit
        .Columns(IndexCounter).FieldLen = 30
        .Columns(IndexCounter).DataType = vbString
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "ExceptionPct"
        .Columns(IndexCounter).Caption = getTranslationResource("Exception %")
        .Columns(IndexCounter).Width = 1500
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        .Columns(IndexCounter).Style = ssStyleEdit
        .Columns(IndexCounter).FieldLen = 6
        .Columns(IndexCounter).DataType = vbInteger
        .Columns(IndexCounter).Mask = "99"
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "ReviewerId"
        .Columns(IndexCounter).Caption = getTranslationResource("Reviewer ID")
        .Columns(IndexCounter).Width = 1200
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        .Columns(IndexCounter).Style = ssStyleComboBox
        .Columns(IndexCounter).ButtonsAlways = True
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).FieldLen = 30
        .Columns(IndexCounter).DataType = vbString
        .Columns(IndexCounter).DropDownHwnd = Me.ddAIMUsers.hwnd
        .Columns(IndexCounter).ButtonsAlways = True
           
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "LRank"
        .Columns(IndexCounter).Caption = getTranslationResource("Rank")
        .Columns(IndexCounter).Width = 900
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        .Columns(IndexCounter).Style = ssStyleEdit
        .Columns(IndexCounter).FieldLen = 3
        .Columns(IndexCounter).DataType = vbInteger
        .Columns(IndexCounter).Mask = "999"
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "Status"
        .Columns(IndexCounter).Caption = getTranslationResource("Status")
        .Columns(IndexCounter).Width = 700
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        .Columns(IndexCounter).Style = ssStyleEdit
        .Columns(IndexCounter).FieldLen = 3
        .Columns(IndexCounter).Visible = False
    
        .RowNavigation = ssRowNavigationUDLock
        For IndexCounter = 0 To .Columns.Count - 1
            If .Columns(IndexCounter).Locked = False Then .Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
        Next
    
    End With

     If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBDestination, ACW_EXPAND
    End If
    
    EBDestination.Redraw = True
    Set EBDestination = Nothing
    
    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "dgDestination_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.dgDestination.Redraw = True
    Set EBDestination = Nothing
End Sub


Private Sub dgDestination_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler
    
   If IsNull(LastCol) Then Exit Sub
   If IsNull(LastRow) Then Exit Sub
   If IsNull(dgDestination.Bookmark) Then Exit Sub
   
    If Not CStr(dgDestination.Bookmark) = CStr(LastRow) Then f_GetSourceOrder
    
    If dgDestination.Columns(0).Value = "" Then
        If CStr(dgDestination.Bookmark) = CStr(LastRow) Then f_GetSourceOrder
    End If
  
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_RowColChange)"
     f_HandleErr , , , Me.Caption & "dgDestination_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler
    
    Dim iSize As Integer
    Dim iCol As Integer
    Dim RtnCode As Integer
    Dim identity As Long

    iCount = iCount + 1
    iSize = UBound(UBArray, 2)
    ReDim Preserve UBArray(0 To (iCols), 0 To iSize + 1)

    For iCol = 0 To (iCols - 1)
        If Not IsNull(RowBuf.Value(0, iCol)) Then
            UBArray(iCol, iSize + 1) = CStr(RowBuf.Value(0, iCol))
        End If
    Next iCol

    UBArray(8, iSize + 1) = "NEW"
    dgDestination.Columns(8).Value = "NEW"
    RtnCode = f_Destination_Save
    If RtnCode = SUCCEED Then
        If dgDestination.Columns(8).Value = "NEW" Then
            identity = f_Identity_Get()
            UBArray(0, iSize + 1) = identity
            UBArray(8, iSize + 1) = "OLD"
        End If
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_UnboundAddData)"
     f_HandleErr , , , Me.Caption & "dgDestination_UnboundAddData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler
    
    vClone(0, Bookmark) = "*SS.DELETED*"

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_UnboundDeleteRow)"
     f_HandleErr , , , Me.Caption & "dgDestination_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
    
    Dim iRBRow As Integer
    Dim iCol As Integer
    Dim irow As Integer
    
    irow = 0
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            iPoint = iCount
        Else
            iPoint = 0
        End If
    Else
        iPoint = StartLocation
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
    End If
    
    For iRBRow = 0 To RowBuf.RowCount - 1
        If iPoint < 0 Or iPoint > iCount Then Exit For
        For iCol = 0 To (iCols)
            RowBuf.Value(iRBRow, iCol) = UBArray(iCol, iPoint)
        Next iCol
        RowBuf.Bookmark(iRBRow) = iPoint
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
        irow = irow + 1
    Next iRBRow
    RowBuf.RowCount = irow

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_UnboundReadData)"
     f_HandleErr , , , Me.Caption & "dgDestination_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgDestination_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler
 
    Dim iCol As Integer
    
    For iCol = 0 To (iCols - 1)
    If Not IsNull(RowBuf.Value(0, iCol)) Then
        UBArray(iCol, CInt(WriteLocation)) = CStr(RowBuf.Value(0, iCol))
    End If
    Next iCol
    
    f_Destination_Save
   
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_UnboundWriteData)"
     f_HandleErr , , , Me.Caption & "dgDestination_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnValue As Integer
    
    'Check if this is old row by checking if the AllocDefaultsId value is ""
    If dgSourceOrder.Columns(0).Value = "" Then ' new row
        'Call validation
        RtnValue = f_SourceOrder_Validate
        If RtnValue = -1 Then Cancel = True
    Else
        'See if the Lcid_Source column value haa changed from its original value
        'If it has  changed then call validation else do not call else do not call validation
        rsSourceOrder.Bookmark = dgSourceOrder.Bookmark
        If Not (rsSourceOrder!Lcid_Source = dgSourceOrder.Columns(1).Value) Then
            RtnValue = f_SourceOrder_Validate
            If RtnValue = -1 Then Cancel = True
        End If
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_BeforeUpdate)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgSourceOrder_BtnClick()
On Error GoTo ErrorHandler
    
    Dim LcIdKey As String

    'Display the Location Lookup form
    LcIdKey = getTranslationResource("All")
    If (LcIdKey = "") Then LcIdKey = getTranslationResource("All")

    AIM_LocationLookUp.LcId = LcIdKey
    Set AIM_LocationLookUp.Cn = Cn
    AIM_LocationLookUp.Show vbModal

    'Get data returned from the Location Lookup and populate the Source Order grid with Location ID
    If Not AIM_LocationLookUp.CancelFlag Then
        LcIdKey = AIM_LocationLookUp.LcId
        dgSourceOrder.Columns(1).Value = LcIdKey
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_BtnClick)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_BtnClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_Change()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    
    dgDestination.Update
    
    'Set toggle to active. This is used in the Toolbar_Click event
    If dgDestination.Columns(0).Value = "" Then
        strMessage = getTranslationResource("MSGBOX07907")
        If StrComp(strMessage, "MSGBOX07907") = 0 Then strMessage = "Please save the current record "
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        Exit Sub
    End If
    
    AllocDefaultsID = dgDestination.Columns(0).Value
    
    m_ActiveGridID = ACTIVEGRID_SourceOrder
    ToggleToolbar

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_Change)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_Click()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    
    dgDestination.Update
    
    If dgDestination.Columns(0).Value = "" Then
        strMessage = getTranslationResource("MSGBOX07907")
        If StrComp(strMessage, "MSGBOX07907") = 0 Then strMessage = "Please save the current record "
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        Exit Sub
    End If
    
    AllocDefaultsID = dgDestination.Columns(0).Value
    
    'Set toggle to active. This is used in the Toolbar_Click event
    m_ActiveGridID = ACTIVEGRID_SourceOrder
    ToggleToolbar
    
    'Toggle bookmark tool
    If IsEmpty(SourceOrderBookmark) _
    Or IsNull(SourceOrderBookmark) _
    Then
        Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
    Else
        Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_Click)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim EBSourceOrder As SSOleDBGrid
        
    Me.MousePointer = vbHourglass
    
    Set EBSourceOrder = Me.dgSourceOrder
    With EBSourceOrder
        .Redraw = False

        'Set grid properties
        .ROWHEIGHT = 300
        .AllowUpdate = True
        
        'Define Columns
        IndexCounter = 0

        'Set column properties
        .Columns(IndexCounter).Name = "AllocDefaultsId"
        .Columns(IndexCounter).Width = 0
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).Visible = False
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "Lcid_Source"
        .Columns(IndexCounter).Caption = getTranslationResource("Source Location")
        .Columns(IndexCounter).Style = ssStyleEditButton
        .Columns(IndexCounter).ButtonsAlways = True
        .Columns(IndexCounter).Locked = True
        .Columns(IndexCounter).Width = 2880
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        .Columns(IndexCounter).Name = "Src_Priority"
        .Columns(IndexCounter).Caption = getTranslationResource("Sourcing Order")
        .Columns(IndexCounter).Style = ssStyleEdit
        .Columns(IndexCounter).Width = 1500
        .Columns(IndexCounter).Locked = False
        .Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        .Columns(IndexCounter).Alignment = ssCaptionAlignmentRight
        
        For IndexCounter = 0 To .Columns.Count - 1
            If .Columns(IndexCounter).Locked = False Then .Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
        Next
    End With

    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths EBSourceOrder, ACW_EXPAND
    End If

EBSourceOrder.Redraw = True
    Set EBSourceOrder = Nothing
    
    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.dgDestination.Redraw = True
    Set EBSourceOrder = Nothing
End Sub

Private Sub dgSourceOrder_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsSourceOrder) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsSourceOrder.MoveLast
        Else
            rsSourceOrder.MoveFirst
        End If
    Else
        rsSourceOrder.Bookmark = StartLocation
        If ReadPriorRows Then
            rsSourceOrder.MovePrevious
        Else
            rsSourceOrder.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsSourceOrder.BOF Or rsSourceOrder.eof Then Exit For
        
        RowBuf.Value(i, 0) = rsSourceOrder("AllocDefaultsId").Value
        RowBuf.Value(i, 1) = rsSourceOrder("Lcid_Source").Value
        RowBuf.Value(i, 2) = rsSourceOrder("Src_Priority").Value
        
        RowBuf.Bookmark(i) = rsSourceOrder.Bookmark
    
        If ReadPriorRows Then
            rsSourceOrder.MovePrevious
        Else
            rsSourceOrder.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundReadData)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As String

    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsDestination) Then Exit Sub
    If Not f_IsRecordsetValidAndOpen(rsSourceOrder) Then Exit Sub
     
    'Validate data
'    If Not GridSourcingOrderValidation(strMessage) Then
        'If valid, proceed with insert
        rsSourceOrder.AddNew
        rsSourceOrder!AllocDefaultsID = AllocDefaultsID

        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsSourceOrder!Lcid_Source = RowBuf.Value(0, 1)
        End If

        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsSourceOrder!Src_Priority = RowBuf.Value(0, 2)
        End If
        
        rsSourceOrder.Update
        rsSourceOrder.MoveLast
        NewRowBookmark = rsSourceOrder.Bookmark
'    End If
    'Check for and display errors/status
    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG07900")
        If StrComp(strMessage, "ERRMSG07900") = 0 Then strMessage = "Error editing AIM Sourcing Options detail record."
        ADOErrorHandler Cn, strMessage
        rsSourceOrder.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG07901")
        If StrComp(strMessage, "STATMSG07901") = 0 Then strMessage = "AIM Sourcing Options detail record successfully  added or updated."
        Write_Message strMessage
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundAddData)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_UnboundAddData", Now, gDRGeneralError, True, Err
    rsSourceOrder.CancelUpdate
End Sub

Private Sub dgSourceOrder_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler

    'Delete from recordset.
    rsSourceOrder.Bookmark = Bookmark
    rsSourceOrder.Delete

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundDeleteRow)"
     f_HandleErr , , , Me.Caption & "dgSourceOrder_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub
Private Sub dgDestination_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler
    
    If IsNull(StartLocation) Then StartLocation = 0

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove
        
Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgDestination_UnboundPositionData)"
     f_HandleErr , , , Me.Caption & "dgDestination_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    'Check for other processes that might be jeopardized by executing this event.
'    If dgSourceOrder.DataChanged Then Exit Sub
'    If dgSourceOrder.IsAddRow Then Exit Sub

    If Not f_IsRecordsetOpenAndPopulated(rsSourceOrder) Then Exit Sub

    'Align recordset
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsSourceOrder.MoveLast
        Else
            rsSourceOrder.MoveFirst
        End If
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        If rsSourceOrder.BOF Then rsSourceOrder.MoveFirst
        If rsSourceOrder.eof Then rsSourceOrder.MoveLast
        rsSourceOrder.Bookmark = StartLocation
    End If

    'Note: Do not use StartLocation because it could be null
    rsSourceOrder.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsSourceOrder.Bookmark

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundPositionData)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSourceOrder_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As String

    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    rsSourceOrder.Bookmark = WriteLocation

    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsDestination) Then Exit Sub
    If Not f_IsRecordsetOpenAndPopulated(rsSourceOrder) Then Exit Sub

    'Validate data
'    If Not GridSourcingOrderValidation(strMessage) Then
        'If valid, proceed with update
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsSourceOrder!AllocDefaultsID = RowBuf.Value(0, 0)
        Else
            If Not IsNull(dgSourceOrder.Columns(0).Value) Then rsSourceOrder!AllocDefaultsID = dgSourceOrder.Columns(0).Value
        End If
        
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsSourceOrder!Lcid_Source = RowBuf.Value(0, 1)
        Else
            If Not IsNull(dgSourceOrder.Columns(1).Value) Then rsSourceOrder!Lcid_Source = dgSourceOrder.Columns(1).Value
        End If

        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsSourceOrder!Src_Priority = RowBuf.Value(0, 2)
        Else
            If Not IsNull(dgSourceOrder.Columns(2).Value) Then rsSourceOrder!Src_Priority = dgSourceOrder.Columns(2).Value
        End If

        rsSourceOrder.Update
'    Else
'        'Else, display the error message returned from the validation function
'        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
'        'Cancel update
'        rsSourceOrder.CancelUpdate
'    End If

    'Check for and display errors/status
    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG07900")
        If StrComp(strMessage, "ERRMSG07900") = 0 Then strMessage = "Error editing AIM Sourcing Options detail record."
        ADOErrorHandler Cn, strMessage
        rsSourceOrder.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG07901")
        If StrComp(strMessage, "STATMSG07901") = 0 Then strMessage = "AIM Sourcing Options detail record successfully  added or updated."
        Write_Message strMessage
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(dgSourceOrder_UnboundWriteData)"
     f_HandleErr , , , Me.Caption & "::dgSourceOrder_UnboundWriteData", Now, gDRGeneralError, True, Err
    rsSourceOrder.CancelUpdate
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    f_GetSourceOrder

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    Dim SqlStmt As String
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    Dim iCol As Integer
    Dim iC As Integer
    Dim CmdParams As ADODB.Parameter
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07900")
    If StrComp(strMessage, "STATMSG07900") = 0 Then strMessage = "Initializing AIM Destination Defaults Maintenance..."
    Write_Message strMessage
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    GetTranslatedCaptions Me
    
    'Initialize the Command object
    Set AIM_AllocDefaults_Delete_Sp = New ADODB.Command
    With AIM_AllocDefaults_Delete_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocDefaults_Delete_Sp"
        .Parameters.Refresh
    End With
    
    'Build the SQL Statement
    SqlStmt = "SELECT AllocDefaultsId, COALESCE(LStatus,'')" + """LStatus""" + ", COALESCE(LDivision,'')" + """LDivision""" & _
    ", COALESCE(LRegion,'')" + """LRegion""" + ", COALESCE(LUserDefined,'')" + """LUserDefined""" + ", " & _
    " COALESCE(ExceptionPct,0)" + """ExceptionPct""" + ", COALESCE(ReviewerId,'') " + """ReviewerId""" + ", COALESCE(LRank,1) " + """LRank""" & _
    " FROM AllocDefaults " & _
    "ORDER BY LRank, LDivision, LRegion, LStatus"

    SqlStmt = "SELECT" & vbCrLf
    SqlStmt = SqlStmt & "   AllocDefaultsId, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(LStatus, '') As LStatus, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(LDivision, '') As LDivision, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(LRegion, '') As LRegion, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(LUserDefined, '') As LUserDefined, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(ExceptionPct, 0) As ExceptionPct, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(ReviewerID, '') As ReviewerID, " & vbCrLf
    SqlStmt = SqlStmt & "   COALESCE(LRank, 1) As LRank " & vbCrLf
    SqlStmt = SqlStmt & "FROM AllocDefaults" & vbCrLf
    SqlStmt = SqlStmt & "ORDER BY LRank, LDivision, LRegion, LStatus" & vbCrLf
    'Open the Record Sets
    Set rsDestination = New ADODB.Recordset
    With rsDestination
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    rsDestination.Open SqlStmt, Cn
    
    iCols = rsDestination.Fields.Count
        
    If rsDestination.RecordCount > 0 Then
        ReDim UBArray(0 To iCols, 0 To rsDestination.RecordCount - 1) As String
        iCount = -1
        For iC = 0 To UBound(UBArray, 2)
            rsDestination.AbsolutePosition = iC + 1
            
            For iCol = 0 To (iCols - 1)
                UBArray(iCol, iC) = rsDestination(iCol)
            Next iCol
            
            UBArray(8, iC) = "OLD"
            iCount = iCount + 1
        Next iC
    Else
        ReDim UBArray(0 To iCols, 0 To 0) As String
    End If
    
    iDestinationCount = rsDestination.RecordCount
    dgDestination.Rows = iCount

    'Rebind the grid
    Me.dgDestination.ReBind
    If f_IsRecordsetOpenAndPopulated(rsDestination) Then dgDestination.Rows = rsDestination.RecordCount
    
    'Build/Bind the Location Division Drop Down
    SqlStmt = "SELECT LDivision FROM AIMLocations " & _
            " WHERE LType = N'D' GROUP BY LDivision ORDER BY LDivision "
    
    Set rsAIMLocationsDivision = New ADODB.Recordset
    rsAIMLocationsDivision.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly

    If f_IsRecordsetOpenAndPopulated(rsAIMLocationsDivision) Then
        Set Me.ddAIMLocationsDivision.DataSource = rsAIMLocationsDivision
    End If

    If RtnCode <> SUCCEED Then Exit Sub
    GetTranslatedCaptions Me

    SqlStmt = " "

    'Build/Bind the Location Region Drop Down
    SqlStmt = "SELECT LRegion FROM AIMLocations " & _
            " WHERE LType = N'D' GROUP BY LRegion ORDER BY LRegion "
    
    Set rsAIMLocationsRegion = New ADODB.Recordset
    rsAIMLocationsRegion.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly

    If f_IsRecordsetOpenAndPopulated(rsAIMLocationsRegion) Then
        Set Me.ddAIMLocationsRegion.DataSource = rsAIMLocationsRegion
    End If

    SqlStmt = " "

    'Build/Bind the Review ID Drop Down
    SqlStmt = "SELECT UserID, UserName FROM AIMUsers "
    
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly

    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Set Me.ddAIMUsers.DataSource = rsAIMUsers
    End If
        
    'Build/Bind the ddStatus Drop Down
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@CodeType", adVarWChar, adParamInput, 30)
        CmdParams.Value = g_CODETYPE_LSTATUS
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@LangID", adVarWChar, adParamInput, 10)
        CmdParams.Value = gLangID
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        
    End With
    Set rsAIMCodeLookUp = New ADODB.Recordset
    With rsAIMCodeLookUp
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsAIMCodeLookUp.Open AIM_CodeLookup_Get_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsAIMCodeLookUp) Then
        Set Me.ddAIMCodeLookUp.DataSource = rsAIMCodeLookUp
    End If
    
    'Refresh the form
'    RefreshForm

    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "Load", Now, gDRGeneralError, True, Err
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsAIMCodeLookUp) Then rsAIMCodeLookUp.Close
    Set rsAIMCodeLookUp = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer

    Me.MousePointer = vbNormal
    
    'Close and destroy objects and connections
    If f_IsRecordsetValidAndOpen(rsDestination) Then rsDestination.Close
    Set rsDestination = Nothing

    If f_IsRecordsetValidAndOpen(rsSourceOrder) Then rsSourceOrder.Close
    Set rsSourceOrder = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    DestinationRequired = False
    SourceOrderRequired = False
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal

    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , Me.Caption & "::Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim DestinationBookmark As Variant
    Dim strMessage As String
    
    'Clear Errors
    Cn.Errors.Clear
    Write_Message ""
       
    'Alert user to possible change, and save changes, if chosen
    Select Case Tool.ID
        Case "ID_AddNew", _
         "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
         "ID_GoToBookMark", "ID_LookUp"
        
        Case "ID_Close"
            'Close connections and prepare to exit form
            If dgDestination.DataChanged Then dgDestination.Update
            
            Unload Me
            Exit Sub
    
    End Select

    'Toggle between display record navigation commands for the header or detail,
    'based on whether the cursor is currently on the detail grid.
    If dgSourceOrder.SelBookmarks.Count >= 1 _
    Or m_ActiveGridID = ACTIVEGRID_SourceOrder _
    Then    'Detail
        If m_ActiveGridID = ACTIVEGRID_SourceOrder _
        Then    'Detail
            ToolbarOptions_SourceOrder Tool
        End If
    Else
        ToolbarOptions_Destination Tool
    End If

Exit Sub
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , Me.Caption & "::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function RefreshForm()
'On Error GoTo ErrorHandler
'
'    'Check Recordset
'    If Not f_IsRecordsetOpenAndPopulated(rsDestination) Then Exit Function
'
'Exit Function
'ErrorHandler:
'    Err.Raise Err.Number, Err.source & "(RefreshForm)", Err.Description
End Function

Private Function ToggleFieldAccess(p_Enabled As Boolean)
On Error GoTo ErrorHandler

    'Based on function argument, enable/disable all form fields for editing
    Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_Delete").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_AddNew").Enabled = p_Enabled
    
    Me.dgSourceOrder.Enabled = p_Enabled
    
Exit Function
ErrorHandler:
    Me.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source & "(ToggleFieldAccess)", Err.Description
     f_HandleErr , , , "AIM_DestinationDefault::ToggleFileldAccess", Now, gDRGeneralError, True, Err
End Function

Private Function ToolbarOptions_Destination(Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    Dim identity As Long
            
    'Navigate
    Select Case Tool.ID
    Case "ID_AddNew"
        'Call Initialize function for Header. This will set the details, accordingly
        dgDestination.Update
        
        If DestinationRequired = True Then
           Exit Function
        End If
        
        dgDestination.Refresh
        dgDestination.AddNew
        
        dgDestination.Columns(8).Value = "NEW"
        
        m_NewRecord = True
             
    Case "ID_Delete"
        'Verify operation and call Delete function for the Header. This will remove the detail records, too.
        strMessage = getTranslationResource("MSGBOX07908")
        If StrComp(strMessage, "MSGBOX07908") = 0 Then strMessage = "Delete the current Destination Default and its details"
        RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, getTranslationResource(Me.Caption))
        If RtnCode = vbYes Then
            RtnCode = f_Destination_Delete
        End If
    Case "ID_GetFirst"
        'Navigate to the first record
        dgDestination.MoveFirst
    
    Case "ID_GetPrev"
        'Navigate to the previous record
        dgDestination.MovePrevious

    Case "ID_GetNext"
        'Navigate to the next record
        dgDestination.MoveNext
            
    Case "ID_GetLast"
        'Navigate to the last record
        dgDestination.MoveLast
            
    Case "ID_Save"
        'Call Save function for the header.
        dgDestination.Update
        If DestinationRequired = True Then Exit Function
        
        RtnCode = f_Destination_Save
        If RtnCode = SUCCEED Then
            If dgDestination.Columns(8).Value = "NEW" Then
                identity = f_Identity_Get()
                NewRow = CLng(dgDestination.GetBookmark(0))
                
                UBArray(0, NewRow) = identity
                UBArray(8, NewRow) = "OLD"
                dgDestination.ReBind
                dgDestination.Refresh
                dgDestination.Bookmark = NewRow
            End If
        Else
            'Commit failed due to some reason. Return user to maint. screen.
            dgDestination.SetFocus
        End If
        
    Case "ID_SetBookMark"
        'Toggle bookmark
        If IsEmpty(DestinationBookmark) _
        Or IsNull(DestinationBookmark) _
        Then
            DestinationBookmark = dgDestination.Bookmark
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
        Else
            DestinationBookmark = Null
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
        End If
                
    Case "ID_GoToBookMark"
        'Navigate to the bookmarked record
        If Not IsEmpty(DestinationBookmark) _
        Or Not IsNull(DestinationBookmark) _
        Then
            dgDestination.Bookmark = DestinationBookmark
        End If

    Case "ID_LookUp"

    Case "ID_Close"
        'Prepare to exit form
        If f_IsRecordsetOpenAndPopulated(rsDestination) Then
            rsDestination.CancelUpdate
        End If
        Unload Me
        Exit Function
   End Select
    
    'Refresh
    Select Case Tool.ID
        Case "ID_GetFirst", "ID_GetPrev", "ID_GetNext", "ID_GetLast", _
             "ID_GotoBookmark", "ID_Delete", "ID_Save"
            If f_IsRecordsetOpenAndPopulated(rsDestination) Then
                m_NewRecord = False
                'Show add, delete and the dropdown. Hide the text box
                Me.tbNavigation.Tools("ID_AddNew").Enabled = True
                Me.tbNavigation.Tools("ID_Delete").Enabled = True
                Me.tbNavigation.Tools("ID_SetBookMark").Enabled = True
            Else
                m_NewRecord = True
                'Show add, delete and the dropdown. Hide the text box
                Me.tbNavigation.Tools("ID_AddNew").Enabled = False
                Me.tbNavigation.Tools("ID_Delete").Enabled = False
                Me.tbNavigation.Tools("ID_SetBookMark").Enabled = False
            End If
                
        Case Else
            If m_NewRecord = True Then m_NewRecord = False
    End Select

Exit Function
ErrorHandler:
    Me.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source, Err.Description & "(ToolbarOptions_Destination)"
     f_HandleErr , , , "AIM_DestinationDefault::ToolbarOptions_Destination", Now, gDRGeneralError, True, Err
End Function

Private Function ToolbarOptions_SourceOrder(Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
 
    Dim RtnCode As Integer
    
    'Save unsaved updates, first
    If dgSourceOrder.DataChanged Then dgSourceOrder.Update
    
    Select Case Tool.ID
    Case "ID_GetFirst"
        'Navigate to the first detail record
        Me.dgSourceOrder.MoveFirst
         
    Case "ID_GetPrev"
        'Navigate to the previous detail record
        Me.dgSourceOrder.MovePrevious
    
    Case "ID_GetNext"
        'Navigate to the next detail record
        Me.dgSourceOrder.MoveNext
    
    Case "ID_GetLast"
        'Navigate to the last detail record
        Me.dgSourceOrder.MoveLast
    
    Case "ID_AddNew"
        'Trigger Initialize event for detail.
        If SourceOrderRequired = True Then
            Exit Function
        End If
        Me.dgDestination.Refresh
        Me.dgSourceOrder.AddNew
        dgSourceOrder.Columns(2).Value = 0
        
    Case "ID_Delete"
        'Trigger Delete event for detail.
        Me.dgSourceOrder.DeleteSelected
        Me.dgSourceOrder.SetFocus
        
    Case "ID_Save"
        'Trigger Update/Insert event for detail.
        'If dgSourceOrder.DataChanged Then dgSourceOrder.Update

    Case "ID_SetBookMark"
        'Toggle bookmark
        If IsEmpty(SourceOrderBookmark) _
        Or IsNull(SourceOrderBookmark) _
        Then
            SourceOrderBookmark = dgSourceOrder.Bookmark
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = True
        Else
            SourceOrderBookmark = Null
            Me.tbNavigation.Tools("ID_GotoBookMark").Enabled = False
        End If
            
    Case "ID_GoToBookMark"
        'Navigate to the last bookmarked record
        If Not IsEmpty(SourceOrderBookmark) _
        Or Not IsNull(SourceOrderBookmark) _
        Then
            dgSourceOrder.Bookmark = SourceOrderBookmark
        End If

    End Select
    
Exit Function
ErrorHandler:
    Me.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(cmdSourceOrderMenu_Click)"
     f_HandleErr , , , "AIM_DestinationDefault::cmdSourceOrderMenu_Click", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleToolbar()
On Error GoTo ErrorHandler

    If m_ActiveGridID = ACTIVEGRID_SourceOrder Then
        'Set toolbar tooltiptexts to reflect the Detail
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Detail")
        Me.tbNavigation.Tools("ID_GetFirst").ToolTipText = getTranslationResource("Get First Detail Record")
        Me.tbNavigation.Tools("ID_GetLast").ToolTipText = getTranslationResource("Get Last Detail Record")
        Me.tbNavigation.Tools("ID_GetNext").ToolTipText = getTranslationResource("Get Next Detail Record")
        Me.tbNavigation.Tools("ID_GetPrev").ToolTipText = getTranslationResource("Get Previous Detail Record")
        Me.tbNavigation.Tools("ID_GoToBookmark").ToolTipText = getTranslationResource("Go To Detail Bookmark")
        Me.tbNavigation.Tools("ID_Save").ToolTipText = getTranslationResource("Save Detail Record(s)")
        Me.tbNavigation.Tools("ID_SetBookMark").ToolTipText = getTranslationResource("Set Detail Bookmark")
        Me.tbNavigation.Tools("ID_AddNew").ToolTipText = getTranslationResource("Add New Detail Record")
        Me.tbNavigation.Tools("ID_Delete").ToolTipText = getTranslationResource("Delete Selected Detail Record(s)")
    Else
        'Set toolbar tooltiptexts to reflect the Header
        m_ActiveGridID = ACTIVEGRID_Destination
        If f_IsRecordsetOpenAndPopulated(rsDestination) Then
            If rsDestination.BOF = True Then rsDestination.MoveFirst
            If rsDestination.eof = True Then rsDestination.MoveLast
            If rsDestination.EditMode = adEditAdd Then
                Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
            Else
                Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
            End If
        End If
        Me.tbNavigation.Tools("ID_GetFirst").ToolTipText = getTranslationResource("Get First Record")
        Me.tbNavigation.Tools("ID_GetLast").ToolTipText = getTranslationResource("Get Last Record")
        Me.tbNavigation.Tools("ID_GetNext").ToolTipText = getTranslationResource("Get Next Record")
        Me.tbNavigation.Tools("ID_GetPrev").ToolTipText = getTranslationResource("Get Previous Record")
        Me.tbNavigation.Tools("ID_GoToBookmark").ToolTipText = getTranslationResource("Go To Bookmark")
        Me.tbNavigation.Tools("ID_Save").ToolTipText = getTranslationResource("Save Current Record")
        Me.tbNavigation.Tools("ID_SetBookMark").ToolTipText = getTranslationResource("Set Bookmark")
        Me.tbNavigation.Tools("ID_AddNew").ToolTipText = getTranslationResource("Add New Record")
        Me.tbNavigation.Tools("ID_Delete").ToolTipText = getTranslationResource("Delete Current Record")
    End If
    
Exit Function
ErrorHandler:
    Me.MousePointer = vbNormal
    'Err.Raise Err.Number, Err.source & "(ToggleToolbar)", Err.Description
     f_HandleErr , , , "AIM_DestinationDefault::ToggleToolbar", Now, gDRGeneralError, True, Err
End Function


Private Function f_GetSourceOrder()
On Error GoTo ErrorHandler

    Dim AIM_SourceOrder_Get_Sp As ADODB.Command
    Dim IndexCounter As Long
    Dim strMessage As String
    Dim RtnCode As Integer
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    'Validate ConstraintID
    If Not f_IsRecordsetOpenAndPopulated(rsDestination) Then Exit Function
    
    'Initialize the Command object
    Set AIM_SourceOrder_Get_Sp = New ADODB.Command
    With AIM_SourceOrder_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocDefaultsSource_Get_Sp"
        .Parameters.Refresh
    
        'Set parameters
        .Parameters.Refresh
        If dgDestination.Columns(0).Value = "" Then
            .Parameters("@AllocDefaultsID").Value = -1
        Else
            .Parameters("@AllocDefaultsID").Value = dgDestination.Columns(0).Value
        End If
    End With
    
    'Fetch Sourcing Order into recordset
    Set rsSourceOrder = Nothing
    Set rsSourceOrder = New ADODB.Recordset
    With rsSourceOrder
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    rsSourceOrder.Open AIM_SourceOrder_Get_Sp
    'Bind to grid
    Me.dgSourceOrder.ReBind
    dgSourceOrder.Refresh

    'Set grid row count
    If f_IsRecordsetOpenAndPopulated(rsSourceOrder) Then dgSourceOrder.Rows = rsSourceOrder.RecordCount

'NO EXIT FUNCTION! The ErrorHandler will do cleanup as well
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    Me.MousePointer = vbNormal

    If Not (AIM_SourceOrder_Get_Sp Is Nothing) Then Set AIM_SourceOrder_Get_Sp.ActiveConnection = Nothing
    Set AIM_SourceOrder_Get_Sp = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_GetSourceOrder)", ErrDescription
        f_HandleErr , , , "AIM_DestinationDefault::f_GetSourceOrder", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function f_Identity_Get() As Long
On Error GoTo ErrorHandler
    
    Dim Aim_Identity_Get_Sp As Command
    Dim rsAllocDefaultsID As Recordset
    Dim identity As Long
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    'Initialize the Command object
    Set Aim_Identity_Get_Sp = New ADODB.Command
    With Aim_Identity_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Identity_Get_Sp"
        .Parameters.Refresh
        'Set parameters
        .Parameters.Refresh
        .Parameters("@TableName").Value = "AllocDefaults"
    End With
    
    'Fetch Sourcing Order into recordset
    Set rsAllocDefaultsID = Nothing
    Set rsAllocDefaultsID = New ADODB.Recordset
    With rsAllocDefaultsID
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    rsAllocDefaultsID.Open Aim_Identity_Get_Sp
    
    'Bind to grid
    identity = rsAllocDefaultsID(0)
    f_Identity_Get = identity
    
'NO EXIT FUNCTION! The ErrorHandler will do cleanup as well
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Me.MousePointer = vbNormal

    If Not (Aim_Identity_Get_Sp Is Nothing) Then Set Aim_Identity_Get_Sp.ActiveConnection = Nothing
    Set Aim_Identity_Get_Sp = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_Identity_Get)", ErrDescription
        f_HandleErr , , , "f_DestinationOrder_Validate::f_Identity_Get", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function f_SourceOrder_Validate() As Integer
On Error GoTo ErrorHandler
' 0 is failure  1 is success

    Dim AllocDefaulsSource As Long
    Dim Lcid_Source As String
    Dim Src_Priority As Integer
    Dim AIM_AllocDefaultsSource_Validation_Sp As ADODB.Command
    Dim RtnValue As Integer
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    SourceOrderRequired = False
    
    'Initialize the Command object
    Set AIM_AllocDefaultsSource_Validation_Sp = New ADODB.Command
    With AIM_AllocDefaultsSource_Validation_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocDefaultsSource_Validation_Sp"
        .Parameters.Refresh
    End With

    If dgSourceOrder.Columns(1).Value = "" Or _
        dgSourceOrder.Columns(2).Value = "" Then
        strMessage = getTranslationResource("MSGBOX07909")
        If StrComp(strMessage, "MSGBOX07909") = 0 Then strMessage = "Both  Source Location and Sourcing Order values are required"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        f_SourceOrder_Validate = -1
        SourceOrderRequired = True
        Exit Function
        Return
    Else
        'Set parameters
        With AIM_AllocDefaultsSource_Validation_Sp
        .Parameters.Refresh
        .Parameters("@AllocDefaultsID").Value = AllocDefaultsID
        .Parameters("@Lcid_Source").Value = IIf(dgSourceOrder.Columns(1).Value = "", "", dgSourceOrder.Columns(1).Value)
        End With
    
       'Get the Return value
        AIM_AllocDefaultsSource_Validation_Sp.Execute , , adExecuteNoRecords
    
        RtnValue = AIM_AllocDefaultsSource_Validation_Sp.Parameters("@RETURN_VALUE").Value
        f_SourceOrder_Validate = RtnValue
        If Not (AIM_AllocDefaultsSource_Validation_Sp Is Nothing) Then
            Set AIM_AllocDefaultsSource_Validation_Sp.ActiveConnection = Nothing
        End If
        Set AIM_AllocDefaultsSource_Validation_Sp = Nothing
        If RtnValue = 0 Then
            strMessage = getTranslationResource("MSGBOX07910")
            If StrComp(strMessage, "MSGBOX07910") = 0 Then strMessage = "Duplicate Source Location found in the database "
            RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
             f_SourceOrder_Validate = -1
            SourceOrderRequired = True
            Exit Function
            Return
        End If
    End If

'NO EXIT FUNCTION! The ErrorHandler will do cleanup as well
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Me.MousePointer = vbNormal

    If Not (AIM_AllocDefaultsSource_Validation_Sp Is Nothing) Then Set AIM_AllocDefaultsSource_Validation_Sp.ActiveConnection = Nothing
    Set AIM_AllocDefaultsSource_Validation_Sp = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_SourceOrder_Validate)", ErrDescription
         f_HandleErr , , , "AIM_DestinationDefault::f_SourceOrder_Validate", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function f_DestinationOrder_Validate() As Integer
On Error GoTo ErrorHandler
' 0 is failure  1 is success

    Dim AIM_AllocDefaults_Validation_Sp As Command
    Dim RtnValue As Integer
    Dim strMessage As String
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    'Validate if all the required fields are populated
    If dgDestination.Columns("ReviewerId").Value = "" Then
        strMessage = getTranslationResource("MSGBOX07901")
        If StrComp(strMessage, "MSGBOX07901") = 0 Then strMessage = "ReviewerId is required for this record"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        f_DestinationOrder_Validate = 0
        Exit Function
    End If
        
     If dgDestination.Columns("LStatus").Value = "" And _
        dgDestination.Columns("LDivision").Value = "" And _
        dgDestination.Columns("LRegion").Value = "" Then
        
        strMessage = getTranslationResource("MSGBOX07902")
        If StrComp(strMessage, "MSGBOX07902") = 0 Then strMessage = "Atleast one field among status,division,region should be populated"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        f_DestinationOrder_Validate = 0
        Exit Function
    End If
    
    'Initialize the Command object
    Set AIM_AllocDefaults_Validation_Sp = New ADODB.Command
    With AIM_AllocDefaults_Validation_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocDefaults_Validation_Sp"
        .Parameters.Refresh
        'Set parameters
        .Parameters("@LStatus").Value = IIf(dgDestination.Columns("LStatus").Value = "", "", dgDestination.Columns("LStatus").Value)
        .Parameters("@LDivision").Value = IIf(dgDestination.Columns("LDivision").Value = "", "", dgDestination.Columns("LDivision").Value)
        .Parameters("@LRegion").Value = IIf(dgDestination.Columns("LRegion").Value = "", "", dgDestination.Columns("LRegion").Value)
        .Parameters("@LUserDefined").Value = IIf(dgDestination.Columns(4).Value = "", "", dgDestination.Columns(4).Value)
    End With

   'Get the Return value
    AIM_AllocDefaults_Validation_Sp.Execute , , adExecuteNoRecords

    RtnValue = AIM_AllocDefaults_Validation_Sp.Parameters("@RETURN_VALUE").Value
    If RtnValue = 0 Then
        strMessage = getTranslationResource("MSGBOX07911")
        If StrComp(strMessage, "MSGBOX07911") = 0 Then strMessage = "Duplicate record found in the database for the entered values"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
    End If
    
    f_DestinationOrder_Validate = RtnValue
    
    If Not (AIM_AllocDefaults_Validation_Sp Is Nothing) Then
        Set AIM_AllocDefaults_Validation_Sp.ActiveConnection = Nothing
    End If
    Set AIM_AllocDefaults_Validation_Sp = Nothing

'NO EXIT FUNCTION! The ErrorHandler will do cleanup as well
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Me.MousePointer = vbNormal

    If Not (AIM_AllocDefaults_Validation_Sp Is Nothing) Then Set AIM_AllocDefaults_Validation_Sp.ActiveConnection = Nothing
    Set AIM_AllocDefaults_Validation_Sp = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_DestinationOrder_Validate)", ErrDescription
        f_HandleErr , , , "AIM_DestinationDefault::f_DestinationOrder_Validate", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function f_Destination_Delete() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    Dim Row As Long
    
    'Set parameters
    If IsNull(dgDestination.GetBookmark(0)) Then
        dgDestination.DeleteSelected
        Exit Function
        Return
    End If
    
    Row = CLng(dgDestination.GetBookmark(0)) - 1
    With AIM_AllocDefaults_Delete_Sp
        .Parameters.Refresh
        .Parameters("@AllocDefaultsID").Value = UBArray(0, CLng(dgDestination.GetBookmark(0)))
    End With
    
    'Run the Stored Procedure
    AIM_AllocDefaults_Delete_Sp.Execute , , adExecuteNoRecords
    
    'Check for and display errors/status
    RtnCode = AIM_AllocDefaults_Delete_Sp.Parameters(0).Value
    If RtnCode < 0 Then
        strMessage = getTranslationResource("ERRMSG07900")
        If StrComp(strMessage, "ERRMSG07900") = 0 Then strMessage = "Failed to delete the current Record."
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, strMessage
        End If
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        f_Destination_Delete = FAIL
        
    Else
        f_Destination_Delete = SUCCEED
        'Get the previous record, if available
        dgDestination.DeleteSelected
        dgDestination.ReBind
        dgDestination.Refresh
        If Row >= 0 Then
            dgDestination.Bookmark = Row
        End If
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_Destination_Delete)"
     f_HandleErr , , , "AIM_DestinationDefault::f_Destination_Delete", Now, gDRGeneralError, True, Err
End Function

Private Function f_Destination_Save() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim AIM_Destination_Save_Sp As ADODB.Command
    Dim strMessage As String
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    'Validate if all the required fields are populated
    If dgDestination.Columns("ReviewerId").Value = "" Then
        strMessage = getTranslationResource("MSGBOX07901")
        If StrComp(strMessage, "MSGBOX07901") = 0 Then strMessage = "ReviewerId is required for this record"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        f_Destination_Save = FAIL
        Exit Function
    End If
        
     If dgDestination.Columns("LStatus").Value = "" And _
        dgDestination.Columns("LDivision").Value = "" And _
        dgDestination.Columns("LRegion").Value = "" Then
        strMessage = getTranslationResource("MSGBOX07902")
        If StrComp(strMessage, "MSGBOX07902") = 0 Then strMessage = "Atleast one field among status,division,region should be populated"
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
        f_Destination_Save = FAIL
        Exit Function
    End If
        
    'Initialize the Command object
    Set AIM_Destination_Save_Sp = New ADODB.Command
    With AIM_Destination_Save_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AllocDefaults_Save_Sp"
        .Parameters.Refresh
    End With

    'Set Parameters
    With AIM_Destination_Save_Sp
        If dgDestination.Columns(8).Value = "NEW" Or dgDestination.Columns(8).Value = "" Then
            .Parameters("@AllocDefaultsID").Value = 0
        Else
            .Parameters("@AllocDefaultsID").Value = dgDestination.Columns(0).Value
        End If
        .Parameters("@LStatus").Value = IIf(dgDestination.Columns("LStatus").Value = "", "", dgDestination.Columns("LStatus").Value)
        .Parameters("@LDivision").Value = IIf(dgDestination.Columns("LDivision").Value = "", "", dgDestination.Columns("LDivision").Value)
        .Parameters("@LRegion").Value = IIf(dgDestination.Columns("LRegion").Value = "", "", dgDestination.Columns("LRegion").Value)
        .Parameters("@LUserDefined").Value = IIf(dgDestination.Columns(4).Value = "", "", dgDestination.Columns(4).Value)
        .Parameters("@ExceptionPct").Value = IIf(dgDestination.Columns(5).Value = "", 0, dgDestination.Columns(5).Value)
        .Parameters("@ReviewerID").Value = dgDestination.Columns(6).Value
        .Parameters("@LRank").Value = IIf(dgDestination.Columns(7).Value = "", 0, dgDestination.Columns(7).Value)
    End With

    'Run the Stored Procedure
    AIM_Destination_Save_Sp.Execute , , adExecuteNoRecords

    'Check for and display errors/status
    RtnCode = AIM_Destination_Save_Sp.Parameters(0).Value
    If RtnCode < 0 Then
        f_Destination_Save = FAIL
        strMessage = getTranslationResource("MSGBOX07903")
        If StrComp(strMessage, "MSGBOX07903") = 0 Then strMessage = "Error adding or modifiying AIM Destination Defaults record."
        ADOErrorHandler Cn, strMessage
        RtnCode = MsgBox(strMessage, vbQuestion + vbOK, getTranslationResource(Me.Caption))
    Else
        f_Destination_Save = SUCCEED
        strMessage = getTranslationResource("STATMSG07902")
        If StrComp(strMessage, "STATMSG07902") = 0 Then strMessage = "AIM Destination Defaults record successfully  added or updated."
        Write_Message strMessage
        If m_NewRecord = True Then m_NewRecord = False
    End If

'NO EXIT FUNCTION! The ErrorHandler will do cleanup as well
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    Me.MousePointer = vbNormal

    If Not (AIM_Destination_Save_Sp Is Nothing) Then Set AIM_Destination_Save_Sp.ActiveConnection = Nothing
    Set AIM_Destination_Save_Sp = Nothing
    
    If ErrNumber <> 0 Then
        'Err.Raise ErrNumber, ErrSource & "(f_Destination_Save)", ErrDescription
         f_HandleErr , , , "AIM_DestinationDefault::f_Destination_Save", Now, gDRGeneralError, True, Err
    End If
End Function
