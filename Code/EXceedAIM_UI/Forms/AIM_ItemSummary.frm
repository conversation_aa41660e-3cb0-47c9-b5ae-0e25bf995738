VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItemSummary 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Item Summary/Detail Report"
   ClientHeight    =   2820
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   5640
   Icon            =   "AIM_ItemSummary.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2820
   ScaleWidth      =   5640
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   60
      Top             =   2340
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_ItemSummary.frx":030A
      ToolBars        =   "AIM_ItemSummary.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   2340
      Left            =   37
      TabIndex        =   0
      Top             =   0
      Width           =   5550
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
         Bindings        =   "AIM_ItemSummary.frx":3733
         Height          =   340
         Left            =   2750
         TabIndex        =   1
         Top             =   240
         Width           =   1710
         DataFieldList   =   "Userid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserId"
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   340
         Left            =   2750
         TabIndex        =   2
         Top             =   660
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_ItemSummary.frx":374C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemSummary.frx":37B8
         Key             =   "AIM_ItemSummary.frx":37D6
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   340
         Left            =   2750
         TabIndex        =   3
         Top             =   1080
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_ItemSummary.frx":381A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ItemSummary.frx":3886
         Key             =   "AIM_ItemSummary.frx":38A4
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMLocations 
         Bindings        =   "AIM_ItemSummary.frx":38E8
         Height          =   340
         Left            =   2750
         TabIndex        =   4
         Top             =   1500
         Width           =   1710
         DataFieldList   =   "LcId"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "LcId"
      End
      Begin VB.CheckBox ckDetailed 
         Caption         =   "Print Item Detail"
         Height          =   300
         Left            =   2750
         TabIndex        =   5
         Top             =   1920
         Width           =   2310
      End
      Begin VB.Label Label1 
         Caption         =   "Location ID"
         Height          =   300
         Left            =   200
         TabIndex        =   9
         Top             =   1540
         Width           =   2450
      End
      Begin VB.Label label 
         Caption         =   "Vendor ID"
         Height          =   300
         Index           =   24
         Left            =   200
         TabIndex        =   8
         Top             =   700
         Width           =   2450
      End
      Begin VB.Label label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   25
         Left            =   200
         TabIndex        =   7
         Top             =   1125
         Width           =   2450
      End
      Begin VB.Label Label17 
         Caption         =   "Buyer ID"
         Height          =   300
         Left            =   200
         TabIndex        =   6
         Top             =   280
         Width           =   2450
      End
   End
End
Attribute VB_Name = "AIM_ItemSummary"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim m_SQLStmt As SQLBuilder        'SQLStmt.cls, defined as a Common File

Function SetFormCriteria()
    
    Dim CompValue1 As String

    'Set Criteria Selection Controls
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "ById")
    Me.dcAIMUsers.Text = IIf(CompValue1 = "", getTranslationResource("All"), CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "VnId")
    Me.txtVnId.Text = IIf(CompValue1 = "", "", CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "Assort")
    Me.txtAssort = IIf(CompValue1 = "", "", CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("Item", "LcId")
    Me.dcAIMLocations.Text = IIf(CompValue1 = "", getTranslationResource("All"), CompValue1)
   
End Function

Function BldSqlStmt(ById As String, VnId As String, Assort As String, LcId As String)

    Dim Qualifier As String
    
    'Clean up the SQL Statement
    m_SQLStmt.Criterias.DeleteColumn "Item", "ById"
    m_SQLStmt.Criterias.DeleteColumn "Item", "VnId"
    m_SQLStmt.Criterias.DeleteColumn "Item", "Assort"
    m_SQLStmt.Criterias.DeleteColumn "Item", "LcId"
    
    If ById <> getTranslationResource("All") Then
        If ById <> "All" Then
            Qualifier = IIf(m_SQLStmt.Criterias.Count = 0 Or IsNull(m_SQLStmt.Criterias.Count), "WHERE", "AND")
            m_SQLStmt.Criterias.Add Qualifier, "Item.ById", False, False, "=", ById, ""
        End If
    End If
    
    If VnId <> "" Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0 Or IsNull(m_SQLStmt.Criterias.Count), "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "Item.VnId", False, False, "=", VnId, ""
    End If
    
    If Assort <> "" Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0 Or IsNull(m_SQLStmt.Criterias.Count), "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "Item.Assort", False, False, "=", Assort, ""
    End If
    
    If LcId <> getTranslationResource("All") Then
        If LcId <> "All" Then
            Qualifier = IIf(m_SQLStmt.Criterias.Count = 0 Or IsNull(m_SQLStmt.Criterias.Count), "WHERE", "AND")
            m_SQLStmt.Criterias.Add Qualifier, "Item.LcId", False, False, "=", LcId, ""
        End If
    End If
    
    BldSqlStmt = m_SQLStmt.SQLExpr

End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)

    Dim i As Integer
    
    Dim Assort As String
    Dim ById As String
    Dim LcId As String
    Dim VnId As String
    Dim SQL As String
        
    Dim Report1 As AIM_ItemSummary_Report
    Dim Report2 As AIM_ItemDetail_Report
    Dim strMessage As String
    Dim rsReport As New ADODB.Recordset
    
    Select Case Tool.ID
        Case "ID_Print", "ID_Preview"
            'Housekeeping
            Screen.MousePointer = vbHourglass
            strMessage = getTranslationResource("STATMSG02300")
            If StrComp(strMessage, "STATMSG02300") = 0 Then strMessage = "Building the Item Summary/Detail Report..."
            Write_Message strMessage
            
            'Build SQL Statement
            SQL = BldSqlStmt(Me.dcAIMUsers.Text, Me.txtVnId.Text, Me.txtAssort.Text, Me.dcAIMLocations.Text)

            'Open the report result set
            rsReport.Open SQL, Cn, adOpenStatic, adLockReadOnly
            
            Write_Message ""
            Screen.MousePointer = vbNormal
            
            If (Not f_IsRecordsetOpenAndPopulated(rsReport)) Then
                Set rsReport = Nothing
                Set Report1 = Nothing
                Set Report2 = Nothing
                strMessage = getTranslationResource("TEXTMSG05103")
                If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
                Write_Message strMessage
                Exit Sub
            End If
            
            'Instance the Report
            If Me.ckDetailed.Value = VALUETRUE Then
                Set Report2 = New AIM_ItemDetail_Report
                Set Report2.dcItem.Recordset = rsReport
            Else
                Set Report1 = New AIM_ItemSummary_Report
                Set Report1.dcItem.Recordset = rsReport
            End If
            
            Select Case Tool.ID
                Case "ID_Print"
                    If Me.ckDetailed.Value = VALUETRUE Then
                        Report2.PrintReport True
                    Else
                        Report1.PrintReport True
                    End If
        
                Case "ID_Preview"
                    If Me.ckDetailed.Value = VALUETRUE Then
                        Set AIM_Reports.ARViewer.ReportSource = Report2
                    Else
                        Set AIM_Reports.ARViewer.ReportSource = Report1
                    End If
                
                    AIM_Reports.Show vbModal, AIM_Main
            End Select
        
            'Wrap Up
            Set Report1 = Nothing
            Set Report2 = Nothing
            Set AIM_ItemSummary_Report = Nothing
            Set AIM_ItemDetail_Report = Nothing
            
            If rsReport.State <> adStateClosed Then rsReport.Close
            Set rsReport = Nothing
            
        Case "ID_Filter"
            If StrComp(Me.dcAIMUsers.Text, getTranslationResource("All")) = 0 Then
                ById = "All"    'Untranslate
            Else
                ById = Me.dcAIMUsers.Text
            End If
            
            VnId = Me.txtVnId.Text
            Assort = Me.txtAssort.Text
            
            If StrComp(Me.dcAIMLocations.Text, getTranslationResource("All")) = 0 Then
                LcId = "All"    'Untranslate
            Else
                LcId = Me.dcAIMLocations.Text
            End If
            
            'Update the Sql Statment
            BldSqlStmt ById, VnId, Assort, LcId
            
            Set AIM_SelectionCriteria.p_SQLStmt = m_SQLStmt
            Set AIM_SelectionCriteria.Cn = Cn
        
            AIM_SelectionCriteria.Show vbModal, AIM_Main
            
            SetFormCriteria
            
        Case "ID_Close"
            Unload Me
            Exit Sub
        
    End Select
    
End Sub

Private Sub dcAIMLocations_InitColumnProps()

    Dim IndexCounter As Long
    
    Me.dcAIMLocations.Columns(0).Name = "lcid"
    Me.dcAIMLocations.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcAIMLocations.Columns(0).Width = 1000
    Me.dcAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(0).DataField = "lcid"
    
    Me.dcAIMLocations.Columns(1).Name = "lname"
    Me.dcAIMLocations.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcAIMLocations.Columns(1).Width = 2880
    Me.dcAIMLocations.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(1).DataField = "lname"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMLocations, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcAIMLocations.Columns.Count - 1
'        dcAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

End Sub

Private Sub dcAIMUsers_InitColumnProps()

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "userid"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "userid"
    
    Me.dcAIMUsers.Columns(1).Name = "username"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = "username"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

End Sub

Private Sub Form_Activate()

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim rsAIMUsers As New ADODB.Recordset
    Dim rsAIMLocations As New ADODB.Recordset
        
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02301")
    If StrComp(strMessage, "STATMSG02301") = 0 Then strMessage = "Initializing Item Summary Report..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    Set m_SQLStmt = New SQLBuilder
    
    'Build/Bind the AIM User Drop Down
    Me.dcAIMUsers.Text = GetCurrentById()
    
    strSQL = "SELECT UserID, UserName FROM AIMUsers ORDER BY UserID"
    
    rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dcAIMUsers.AddItem getTranslationResource("All") + vbTab + _
                        getTranslationResource("All Users")
    
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserId").Value + vbTab + rsAIMUsers("Username").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    
    If rsAIMUsers.State <> adStateClosed Then
        rsAIMUsers.Close
    End If
    Set rsAIMUsers = Nothing
    
    'Build/Bind the AIM Locations Drop Down
    strSQL = "SELECT LcID, LName FROM AIMLocations ORDER BY LcID"
    
    rsAIMLocations.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dcAIMLocations.AddItem getTranslationResource("All") + vbTab + _
                            getTranslationResource("All Locations")
    
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        Do Until rsAIMLocations.eof
            Me.dcAIMLocations.AddItem rsAIMLocations("LcId").Value + vbTab + rsAIMLocations("LName").Value
            rsAIMLocations.MoveNext
        Loop
        
    End If
    
    If rsAIMLocations.State <> adStateClosed Then
        rsAIMLocations.Close
    End If
    Set rsAIMLocations = Nothing
    
    Me.dcAIMLocations.Text = getTranslationResource("All")
    
    'Set up the SQL Statement
    m_SQLStmt.SQLTables.Add "Item"
    m_SQLStmt.SelExpr = "SELECT * "
    m_SQLStmt.FromExpr = "FROM Item "
    m_SQLStmt.OrderExpr = "ORDER BY VnId, Assort, LcId, Item "
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption
	 f_HandleErr , , , "AIM_ItemSummary::UserInputValidation", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)

    Dim RtnCode As Integer
    
    Set m_SQLStmt = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

End Sub
