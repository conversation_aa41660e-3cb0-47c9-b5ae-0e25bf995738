VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_Specials 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Specials"
   ClientHeight    =   4185
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   6300
   HelpContextID   =   1
   Icon            =   "AIM_Specials.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   4185
   ScaleWidth      =   6300
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin ActiveToolBars.SSActiveToolBars atNavigation 
      Left            =   240
      Top             =   3720
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Tools           =   "AIM_Specials.frx":030A
      ToolBars        =   "AIM_Specials.frx":1C92
   End
   Begin VB.Frame Frame1 
      Height          =   3615
      Left            =   68
      TabIndex        =   9
      Top             =   60
      Width           =   6165
      Begin TDBNumber6Ctl.TDBNumber txtSP_Cost 
         Height          =   345
         Left            =   2750
         TabIndex        =   7
         Top             =   2760
         Width           =   1365
         _Version        =   65536
         _ExtentX        =   2408
         _ExtentY        =   609
         Calculator      =   "AIM_Specials.frx":1D50
         Caption         =   "AIM_Specials.frx":1D70
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":1DDC
         Keys            =   "AIM_Specials.frx":1DFA
         Spin            =   "AIM_Specials.frx":1E44
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#####0.0000;-#####0.0000;0.0000;0.0000"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#####0.0000"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999999.9999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   0
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin TDBNumber6Ctl.TDBNumber txtSp_VSOQ 
         Height          =   345
         Left            =   2750
         TabIndex        =   5
         Top             =   2040
         Width           =   1365
         _Version        =   65536
         _ExtentX        =   2408
         _ExtentY        =   609
         Calculator      =   "AIM_Specials.frx":1E6C
         Caption         =   "AIM_Specials.frx":1E8C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":1EF8
         Keys            =   "AIM_Specials.frx":1F16
         Spin            =   "AIM_Specials.frx":1F60
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#########0;-#########0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#########0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   2147483647
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBText6Ctl.TDBText txtSp_LcId 
         Height          =   345
         Left            =   2750
         TabIndex        =   2
         Top             =   960
         Width           =   1365
         _Version        =   65536
         _ExtentX        =   2408
         _ExtentY        =   609
         Caption         =   "AIM_Specials.frx":1F88
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":1FF4
         Key             =   "AIM_Specials.frx":2012
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtSp_Item 
         Height          =   345
         Left            =   2745
         TabIndex        =   3
         Top             =   1320
         Width           =   2115
         _Version        =   65536
         _ExtentX        =   3731
         _ExtentY        =   609
         Caption         =   "AIM_Specials.frx":2056
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":20C2
         Key             =   "AIM_Specials.frx":20E0
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtSP_ItDesc 
         Height          =   345
         Left            =   2745
         TabIndex        =   4
         Top             =   1680
         Width           =   3315
         _Version        =   65536
         _ExtentX        =   5847
         _ExtentY        =   609
         Caption         =   "AIM_Specials.frx":2124
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":2190
         Key             =   "AIM_Specials.frx":21AE
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcUOM 
         Bindings        =   "AIM_Specials.frx":21F2
         DataField       =   " "
         Height          =   345
         Left            =   2750
         TabIndex        =   6
         Top             =   2400
         Width           =   1065
         DataFieldList   =   "UOM"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   1879
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UOM"
      End
      Begin TDBText6Ctl.TDBText txtSp_VnId 
         Height          =   345
         Left            =   2750
         TabIndex        =   0
         TabStop         =   0   'False
         Top             =   240
         Width           =   1365
         _Version        =   65536
         _ExtentX        =   2408
         _ExtentY        =   609
         Caption         =   "AIM_Specials.frx":21FD
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":2269
         Key             =   "AIM_Specials.frx":2287
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtSp_Assort 
         Height          =   345
         Left            =   2750
         TabIndex        =   1
         TabStop         =   0   'False
         Top             =   600
         Width           =   1365
         _Version        =   65536
         _ExtentX        =   2408
         _ExtentY        =   609
         Caption         =   "AIM_Specials.frx":22CB
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":2337
         Key             =   "AIM_Specials.frx":2355
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtSp_ExtCost 
         Height          =   345
         Left            =   2750
         TabIndex        =   8
         TabStop         =   0   'False
         Top             =   3120
         Width           =   1365
         _Version        =   65536
         _ExtentX        =   2408
         _ExtentY        =   609
         Caption         =   "AIM_Specials.frx":2399
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_Specials.frx":2405
         Key             =   "AIM_Specials.frx":2423
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   1
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   "0"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   18
         Top             =   645
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Vendor"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   17
         Top             =   285
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Extended Cost"
         Height          =   300
         Index           =   8
         Left            =   200
         TabIndex        =   16
         Top             =   3165
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Unit Cost"
         Height          =   300
         Index           =   7
         Left            =   200
         TabIndex        =   15
         Top             =   2805
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "UOM"
         Height          =   300
         Index           =   6
         Left            =   200
         TabIndex        =   14
         Top             =   2445
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Quantity"
         Height          =   300
         Index           =   5
         Left            =   200
         TabIndex        =   13
         Top             =   2085
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Item"
         Height          =   300
         Index           =   3
         Left            =   200
         TabIndex        =   12
         Top             =   1365
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Description"
         Height          =   300
         Index           =   4
         Left            =   200
         TabIndex        =   11
         Top             =   1725
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Location"
         Height          =   300
         Index           =   2
         Left            =   200
         TabIndex        =   10
         Top             =   1005
         Width           =   2450
      End
   End
End
Attribute VB_Name = "AIM_Specials"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
Dim AIM_Special_Sp As ADODB.Command

Dim rsUOM As ADODB.Recordset

Public VnId As String
Public Assort As String
Public ById As String
Public RtnCode As Integer

Private Sub atNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
            
        Case "ID_Save"
            'Update the Stored Procedure Parameters
            With AIM_Special_Sp
                ' Specify input parameter values
                AIM_Special_Sp("@POLineType").Value = 40
                AIM_Special_Sp("@OrdStatus").Value = "R"
                AIM_Special_Sp("@RevCycle") = "None"
                AIM_Special_Sp("@ById") = ById
                AIM_Special_Sp("@VnId") = VnId
                AIM_Special_Sp("@Assort") = Assort
                AIM_Special_Sp("@Lcid") = Me.txtSp_LcId.Text
                AIM_Special_Sp("@Item") = Me.txtSp_Item.Text
                AIM_Special_Sp("@ItDesc") = Me.txtSP_ItDesc.Text
                AIM_Special_Sp("@POType") = "S"
                AIM_Special_Sp("@AvailQty") = 0
                AIM_Special_Sp("@PackRounding") = "R"
                AIM_Special_Sp("@RSOQ") = Me.txtSp_VSOQ.Value
                AIM_Special_Sp("@SOQ") = Me.txtSp_VSOQ.Value
                AIM_Special_Sp("@VSOQ") = Me.txtSp_VSOQ.Value
                AIM_Special_Sp("@UOM") = Me.dcUOM.Text
                AIM_Special_Sp("@BuyingUOM") = Me.dcUOM.Text
                AIM_Special_Sp("@ConvFactor") = 1
                AIM_Special_Sp("@IsDate") = Date
                AIM_Special_Sp("@DuDate") = Date
                AIM_Special_Sp("@LastWeekSalesFlag") = "N"
                AIM_Special_Sp("@Cost") = Format(Me.txtSP_Cost.Value, "###,##0.0000")
                AIM_Special_Sp("@Zone") = ""
    
                ' Execute the command
                AIM_Special_Sp.Execute , , adExecuteNoRecords
                
                ' Retrieve stored procedure return value and output parameters
                RtnCode = AIM_Special_Sp(0).Value
                
                Select Case RtnCode
                Case -3
                    strMessage = getTranslationResource("STATMSG05201")
                    If StrComp(strMessage, "STATMSG05201") = 0 Then strMessage = "Insert failed -- Invalid Quantity..."
                Case -2
                    strMessage = getTranslationResource("STATMSG05202")
                    If StrComp(strMessage, "STATMSG05202") = 0 Then strMessage = "Insert failed -- Invalid Location/Item Number..."
                Case -1
                    strMessage = getTranslationResource("STATMSG05203")
                    If StrComp(strMessage, "STATMSG05203") = 0 Then strMessage = "Insert failed -- Invalid Location Id..."
                Case 0
                    strMessage = getTranslationResource("STATMSG05204")
                    If StrComp(strMessage, "STATMSG05204") = 0 Then strMessage = "Error inserting special..."
                Case 1
                    strMessage = getTranslationResource("STATMSG05205")
                    If StrComp(strMessage, "STATMSG05205") = 0 Then strMessage = "Special successfully inserted..."
                Case 2
                    strMessage = getTranslationResource("STATMSG05205")
                    If StrComp(strMessage, "STATMSG05205") = 0 Then strMessage = "Special successfully updated..."
                End Select
                    
                Write_Message strMessage
           
           End With
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atNavigation_ToolClick)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::atNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub


Private Sub dcUOM_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcUOM.Columns(0).Name = "Sp_UOM"
    Me.dcUOM.Columns(0).DataField = "UOM"
    Me.dcUOM.Columns(0).Caption = getTranslationResource("UOM")
    Me.dcUOM.Columns(0).Width = 1000

    Me.dcUOM.Columns(1).Name = "Sp_UOMDesc"
    Me.dcUOM.Columns(1).DataField = "UOMDesc"
    Me.dcUOM.Columns(1).Caption = getTranslationResource("Description")
    Me.dcUOM.Columns(1).Width = 1700
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcUOM, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcUOM.Columns.Count - 1
'        dcUOM.Columns(IndexCounter).HasHeadBackColor = True
'        dcUOM.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcUOM_InitColumnProps)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dcUOM_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    Dim AccessLevel As Integer
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05200")
    If StrComp(strMessage, "STATMSG05200") = 0 Then strMessage = "Initializing Specials Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize Stored Procedures
    Set AIM_Special_Sp = New ADODB.Command
    With AIM_Special_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Special_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the UOM list
    Set rsUOM = New ADODB.Recordset
    rsUOM.CursorLocation = adUseClient
    
    SqlStmt = "SELECT * FROM AIMUOM ORDER BY UOM "
    rsUOM.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
    
    Set Me.dcUOM.DataSourceList = rsUOM
    
    'Initialize the Vnid and Assortment
    Me.txtSp_VnId = VnId
    Me.txtSp_Assort = Assort
    Me.dcUOM.Text = "EA"
    
'    Me.txtSP_Cost.Spin.Visible = 1
     Me.txtSp_VSOQ.Spin.Visible = 1
     
     AccessLevel = VerifyAccess(gUserID, AF_SPECIALS, "")
    If AccessLevel = g_ACCESS_READ Then
        Me.atNavigation.Tools("ID_Save").Enabled = False
    Else
        Me.atNavigation.Tools("ID_Save").Enabled = True
    End If
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsUOM) Then rsUOM.Close
    Set rsUOM = Nothing
    
    If Not (AIM_Special_Sp Is Nothing) Then Set AIM_Special_Sp.ActiveConnection = Nothing
    Set AIM_Special_Sp = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False, False)
    Set Cn = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub txtSP_Cost_Change()
On Error GoTo ErrorHandler

    Me.txtSp_ExtCost.Text = Format(CDbl(Me.txtSP_Cost.Value) * CDbl(Me.txtSp_VSOQ), "#,###,##0.00")
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtSP_Cost_Change)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtSP_Cost_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtSp_VSOQ_Change()
On Error GoTo ErrorHandler

    Me.txtSp_ExtCost.Text = Format(CDbl(Me.txtSP_Cost.Value) * CDbl(Me.txtSp_VSOQ), "#,###,##0.00")

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtSp_VSOQ_Change)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtSp_VSOQ_Change", Now, gDRGeneralError, True, Err
End Sub
