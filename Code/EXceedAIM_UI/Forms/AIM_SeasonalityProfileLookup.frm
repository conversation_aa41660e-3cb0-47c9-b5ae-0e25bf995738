VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_SeasonalityProfileLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Seasonality Profile Lookup"
   ClientHeight    =   5940
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8475
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_SeasonalityProfileLookup.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5940
   ScaleWidth      =   8475
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   7038
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3000
      Style           =   1  'Graphical
      TabIndex        =   9
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return Selected Item"
      Height          =   345
      Left            =   4440
      Style           =   1  'Graphical
      TabIndex        =   8
      Top             =   5400
      Width           =   2445
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   75
      Style           =   1  'Graphical
      TabIndex        =   11
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   1560
      Style           =   1  'Graphical
      TabIndex        =   10
      Top             =   5400
      Width           =   1365
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSeasons 
      Height          =   3315
      Left            =   78
      TabIndex        =   12
      Top             =   1905
      Width           =   8325
      _Version        =   196617
      DataMode        =   1
      Cols            =   6
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      TabNavigation   =   1
      _ExtentX        =   14684
      _ExtentY        =   5847
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1650
      Left            =   78
      TabIndex        =   13
      Top             =   120
      Width           =   8325
      Begin TDBNumber6Ctl.TDBNumber txtSAVersion 
         Height          =   345
         Left            =   2283
         TabIndex        =   1
         Top             =   735
         Width           =   765
         _Version        =   65536
         _ExtentX        =   1349
         _ExtentY        =   609
         Calculator      =   "AIM_SeasonalityProfileLookup.frx":030A
         Caption         =   "AIM_SeasonalityProfileLookup.frx":032A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileLookup.frx":0396
         Keys            =   "AIM_SeasonalityProfileLookup.frx":03B4
         Spin            =   "AIM_SeasonalityProfileLookup.frx":03FE
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#####0;-#####0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#####0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBText6Ctl.TDBText txtSaID 
         Height          =   345
         Left            =   105
         TabIndex        =   0
         Top             =   735
         Width           =   2070
         _Version        =   65536
         _ExtentX        =   3651
         _ExtentY        =   609
         Caption         =   "AIM_SeasonalityProfileLookup.frx":0426
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileLookup.frx":0492
         Key             =   "AIM_SeasonalityProfileLookup.frx":04B0
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLcID 
         Height          =   345
         Left            =   3156
         TabIndex        =   2
         Top             =   735
         Width           =   1410
         _Version        =   65536
         _ExtentX        =   2487
         _ExtentY        =   609
         Caption         =   "AIM_SeasonalityProfileLookup.frx":04F4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileLookup.frx":0560
         Key             =   "AIM_SeasonalityProfileLookup.frx":057E
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtClass 
         Height          =   345
         Left            =   4674
         TabIndex        =   3
         Top             =   735
         Width           =   1695
         _Version        =   65536
         _ExtentX        =   2990
         _ExtentY        =   609
         Caption         =   "AIM_SeasonalityProfileLookup.frx":05C2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileLookup.frx":062E
         Key             =   "AIM_SeasonalityProfileLookup.frx":064C
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtSALevel 
         Height          =   345
         Left            =   6480
         TabIndex        =   4
         Top             =   735
         Width           =   1695
         _Version        =   65536
         _ExtentX        =   2990
         _ExtentY        =   609
         Caption         =   "AIM_SeasonalityProfileLookup.frx":0690
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileLookup.frx":06FC
         Key             =   "AIM_SeasonalityProfileLookup.frx":071A
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
         Height          =   345
         Left            =   120
         TabIndex        =   5
         Top             =   1200
         Width           =   3135
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   600
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7305
         TabIndex        =   6
         Top             =   1200
         Width           =   870
         _Version        =   65536
         _ExtentX        =   1545
         _ExtentY        =   600
         Calculator      =   "AIM_SeasonalityProfileLookup.frx":075E
         Caption         =   "AIM_SeasonalityProfileLookup.frx":077E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileLookup.frx":07EA
         Keys            =   "AIM_SeasonalityProfileLookup.frx":0808
         Spin            =   "AIM_SeasonalityProfileLookup.frx":0852
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   200
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label 
         Caption         =   "Maximum Rows"
         Height          =   300
         Index           =   0
         Left            =   5295
         TabIndex        =   19
         Top             =   1245
         Width           =   1935
      End
      Begin VB.Label Label 
         Caption         =   "Level"
         Height          =   345
         Index           =   5
         Left            =   6480
         TabIndex        =   17
         Top             =   345
         Width           =   1695
      End
      Begin VB.Label Label 
         Caption         =   "Class"
         Height          =   345
         Index           =   4
         Left            =   4674
         TabIndex        =   18
         Top             =   345
         Width           =   1695
      End
      Begin VB.Label Label 
         Caption         =   "Location ID"
         Height          =   345
         Index           =   3
         Left            =   3156
         TabIndex        =   16
         Top             =   345
         Width           =   1410
      End
      Begin VB.Label Label 
         Caption         =   "Version"
         Height          =   345
         Index           =   2
         Left            =   2283
         TabIndex        =   15
         Top             =   345
         Width           =   765
      End
      Begin VB.Label Label 
         Caption         =   "Seasonality Profile ID"
         Height          =   345
         Index           =   1
         Left            =   105
         TabIndex        =   14
         Top             =   345
         Width           =   2070
      End
   End
End
Attribute VB_Name = "AIM_SeasonalityProfileLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection
Dim rsAIMSeasons As ADODB.Recordset

Public CancelFlag As Boolean
Public Class As String
Public LcId As String
Public SaId As String
Public SaLevel As String
Public SAVersion As String

Private Class_Criteria As String
Private LcId_Criteria As String
Private SaId_Criteria As String
Private SaLevel_Criteria As String
Private SaVersion_Criteria As String

Private Function BuildSaLookupQuery() As String
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim WhrStmt As String
    
    'Initialize SQL Statment
    strSQL = "SELECT SAVersion, SAID, SADesc, SALevel, LcID, Class" & _
            " FROM AIMSeasons"
    
    'Build the Where Statment
    If Trim(Me.txtSAVersion.Text) <> "" Then
        WhrStmt = "WHERE SAVersion " & Me.dcComparison.Value & " " & SaVersion_Criteria & " "
    End If
        
    If Trim(Me.txtSaID.Text) <> "" Then
        If Trim(WhrStmt) = "WHERE" Then
            WhrStmt = WhrStmt & " SAID "
        Else
            WhrStmt = WhrStmt & vbCrLf & "AND SAID "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & SaId_Criteria & " "
    
    End If
        
    If Trim(Me.txtLcID.Text) <> "" Then
        If Trim(WhrStmt) = "WHERE" Then
            WhrStmt = WhrStmt & " LcID "
        Else
            WhrStmt = WhrStmt & vbCrLf & "AND LcID "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & LcId_Criteria & " "
            
    End If
        
    If Trim(Me.txtClass.Text) <> "" Then
        If Trim(WhrStmt) = "WHERE" Then
            WhrStmt = WhrStmt & " Class "
        Else
            WhrStmt = WhrStmt & vbCrLf & "AND Class "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & Class_Criteria & " "
            
    End If
    
    If Trim(Me.txtSALevel.Text) <> "" Then
        If Trim(WhrStmt) = "WHERE" Then
            WhrStmt = WhrStmt & " SALevel "
        Else
            WhrStmt = WhrStmt & vbCrLf & "AND SALevel "
        End If
        WhrStmt = WhrStmt & Me.dcComparison.Value & " " & SaLevel_Criteria & " "
            
    End If
    
    strSQL = strSQL & vbCrLf & _
            WhrStmt & vbCrLf & _
            "ORDER BY SAVersion, SAID"
    
    BuildSaLookupQuery = strSQL
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::BuildSaLookupQuery", Now, gDRGeneralError, True, Err

End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim strMessage As String
    Dim strText As String
    
    Screen.MousePointer = vbHourglass
    Write_Message ""
    
    'Make sure the user selected at least one criterion
    If Trim(Me.txtSAVersion.Text) & Trim(Me.txtSaID.Text) & Trim(Me.txtLcID.Text) _
        & Trim(Me.txtClass.Text) & Trim(Me.txtSALevel.Text) = "" Then
        strText = getTranslationResource(AIM_SeasonalityProfileLookUp.Caption)
        strMessage = getTranslationResource("MSGBOX04800")
        If StrComp(strMessage, "MSGBOX04800") = 0 Then strMessage = "Please select one or more Lookup Criteria "
        MsgBox strMessage, vbExclamation, strText
        Me.txtSaID.SetFocus
        
        'Return the screen values
        Screen.MousePointer = vbDefault
        Exit Sub
    End If
    
    'Force validation
    txtSaVersion_Validate False
    txtSaId_Validate False
    txtLcID_Validate False
    txtClass_Validate False
    txtSaLevel_Validate False
    If StrComp(SaVersion_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(SaId_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(LcId_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(Class_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(SaLevel_Criteria, "Error", vbTextCompare) = 0 _
    Then
        'Do NOT Proceed
    Else
        'Create the query
        strSQL = BuildSaLookupQuery()
        
        'Recreate the result set
        If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
        rsAIMSeasons.MaxRecords = Me.txtMaxRows.Value
        rsAIMSeasons.Open strSQL
        
        If f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then
            'Refresh the grid
            Me.dgSeasons.ReBind
        Else
            If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
            Me.dgSeasons.Reset
        End If
        
    End If
    
    'Return the screen values
    Screen.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    Me.txtSaID.Text = ""
    Me.txtSAVersion.Text = "0"
    Me.txtLcID.Text = ""
    Me.txtClass.Text = ""
    Me.txtSALevel.Text = ""
    
    SaId_Criteria = ""
    SaVersion_Criteria = ""
    LcId_Criteria = ""
    Class_Criteria = ""
    SaLevel_Criteria = ""
    
    Me.txtMaxRows.Value = 200
    
    If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
    Me.dgSeasons.Reset
    
    Me.txtSaID.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgSeasons.Rows > 0 Then
        
        Screen.MousePointer = vbHourglass
        Me.dgSeasons.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::cmdPrint_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = False
    Me.SaId = Me.dgSeasons.Columns("said").Value
    Me.SAVersion = Me.dgSeasons.Columns("saversion").Value
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_CloseUp()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case Me.dcComparison.Columns(0).Text
    
    Case "LIKE"
        Select Case Me.dcComparison.Columns(1).Text
        Case getTranslationResource("Begins With")
            strMessage = getTranslationResource("STATMSG04800")
            If StrComp(strMessage, "STATMSG04800") = 0 Then _
                strMessage = "Enter either part or all of the beginning of the desired value. "
            Write_Message strMessage
        Case getTranslationResource("Contains")
            strMessage = getTranslationResource("STATMSG04801")
            If StrComp(strMessage, "STATMSG04801") = 0 Then _
                strMessage = "Enter a string expression which is contained within the desired value. "
            Write_Message strMessage
        End Select
    
    Case "BETWEEN"
        strMessage = getTranslationResource("STATMSG04802")
        If StrComp(strMessage, "STATMSG04802") = 0 Then _
            strMessage = "Enter two values between which the desired value should fall, separated by a comma. "
        Write_Message strMessage
    
    Case "IN"
        strMessage = getTranslationResource("STATMSG04803")
        If StrComp(strMessage, "STATMSG04803") = 0 Then _
            strMessage = "Enter a list of values in which the desired value should fall, separated by commas. "
        Write_Message strMessage
    
    Case Else
        strMessage = getTranslationResource("STATMSG04804")
        If StrComp(strMessage, "STATMSG04804") = 0 Then _
            strMessage = "Enter a value for comparison. "
        Write_Message strMessage
    
    End Select
    
    'Reset the comparison values
    txtSaId_Validate False
    txtSaVersion_Validate False
    txtLcID_Validate False
    txtClass_Validate False
    txtSaLevel_Validate False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_CloseUp)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dcComparison_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Build Columns
    Me.dcComparison.Columns(0).Caption = getTranslationResource("Comparison")
    Me.dcComparison.Columns(0).FieldLen = 1
    Me.dcComparison.Columns(0).Width = 1000
    
    Me.dcComparison.Columns(1).Caption = getTranslationResource("Description")
    Me.dcComparison.Columns(1).FieldLen = 24
    Me.dcComparison.Columns(1).Width = 2000
    
    'Load Values
    Me.dcComparison.AddItem ("=" & vbTab & getTranslationResource("Equal To"))
    Me.dcComparison.AddItem (">" & vbTab & getTranslationResource("Greater Than"))
    Me.dcComparison.AddItem (">=" & vbTab & getTranslationResource("Greater Than / Equal To"))
    Me.dcComparison.AddItem ("<" & vbTab & getTranslationResource("Less Than"))
    Me.dcComparison.AddItem ("<=" & vbTab & getTranslationResource("Less Than / Equal To"))
    Me.dcComparison.AddItem ("<>" & vbTab & getTranslationResource("Not Equal To"))
    Me.dcComparison.AddItem ("LIKE" & vbTab & getTranslationResource("Begins With"))
    Me.dcComparison.AddItem ("LIKE" & vbTab & getTranslationResource("Contains"))
    Me.dcComparison.AddItem ("BETWEEN" & vbTab & getTranslationResource("Is Between These Values"))
    Me.dcComparison.AddItem ("IN" & vbTab & getTranslationResource("Is in this List of Values"))

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcComparison, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcCOmparison.Columns.Count - 1
'        dcComparison.Columns(IndexCounter).HasHeadBackColor = True
'        dcComparison.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'   Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_InitColumnProps)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSeasons_DblClick()
On Error GoTo ErrorHandler

    'Return selected item
    cmdReturn_Click
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSeasons_DblClick)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dgSeasons_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSeasons_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Seasonality Profile Data
    Me.dgSeasons.Columns(0).Name = "saversion"
    Me.dgSeasons.Columns(0).Caption = getTranslationResource("Version")
    Me.dgSeasons.Columns(0).Width = 720
    Me.dgSeasons.Columns(0).Alignment = ssCaptionAlignmentRight
    Me.dgSeasons.Columns(0).Locked = True
    
    Me.dgSeasons.Columns(1).Name = "said"
    Me.dgSeasons.Columns(1).Caption = getTranslationResource("Profile")
    Me.dgSeasons.Columns(1).Width = 1000
    Me.dgSeasons.Columns(1).Locked = True
    
    Me.dgSeasons.Columns(2).Name = "sadesc"
    Me.dgSeasons.Columns(2).Caption = getTranslationResource("Description")
    Me.dgSeasons.Columns(2).Width = 2880
    Me.dgSeasons.Columns(2).Locked = True
    
    Me.dgSeasons.Columns(3).Name = "lcid"
    Me.dgSeasons.Columns(3).Caption = getTranslationResource("Location")
    Me.dgSeasons.Columns(3).Width = 720
    Me.dgSeasons.Columns(3).Locked = True
    
    Me.dgSeasons.Columns(4).Name = "class"
    Me.dgSeasons.Columns(4).Caption = getTranslationResource("Class")
    Me.dgSeasons.Columns(4).Width = 1000
    Me.dgSeasons.Columns(4).Locked = True
    
    Me.dgSeasons.Columns(5).Name = "salevel"
    Me.dgSeasons.Columns(5).Caption = getTranslationResource("Level")
    Me.dgSeasons.Columns(5).Width = 720
    Me.dgSeasons.Columns(5).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgSeasons, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgSeasons.Columns.Count - 1
'        dgSeasons.Columns(IndexCounter).HasHeadBackColor = True
'        dgSeasons.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgSeasons.Columns(IndexCounter).Locked = False Then dgSeasons.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSeasons_InitColumnProps)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dgSeasons_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgSeasons_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG04800")
    If StrComp(strMessage, "RPTMSG04800") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG04801")
    If StrComp(strMessage1, "RPTMSG04801") = 0 Then strMessage = "Item Lookup List"
    strMessage2 = getTranslationResource("RPTMSG04802")
    If StrComp(strMessage2, "RPTMSG04802") = 0 Then strMessage = "Page:"
    
    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & strMessage1 _
                & vbTab & strMessage2 & " <page number>"
    
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSeasons_PrintInitialize)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dgSeasons_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSeasons_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAIMSeasons.MoveLast
        Else
            rsAIMSeasons.MoveFirst
        End If
    
    Else
        rsAIMSeasons.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAIMSeasons.MovePrevious
        Else
            rsAIMSeasons.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsAIMSeasons.BOF Or rsAIMSeasons.eof Then Exit For
        
        RowBuf.Value(i, 0) = rsAIMSeasons("saversion").Value
        RowBuf.Value(i, 1) = rsAIMSeasons("said").Value
        RowBuf.Value(i, 2) = rsAIMSeasons("sadesc").Value
        RowBuf.Value(i, 3) = rsAIMSeasons("lcid").Value
        RowBuf.Value(i, 4) = rsAIMSeasons("class").Value
        RowBuf.Value(i, 5) = rsAIMSeasons("salevel").Value

        RowBuf.Bookmark(i) = rsAIMSeasons.Bookmark
    
        If ReadPriorRows Then
            rsAIMSeasons.MovePrevious
        Else
            rsAIMSeasons.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSeasons_UnboundReadData)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dgSeasons_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Set the default value for the Cancel Flag
    CancelFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim strSQL As String
    
    'Initialize fields
    Me.txtSAVersion.Text = Me.SAVersion
    Me.txtSaID.Text = Me.SaId
    Me.txtLcID.Text = Me.LcId
    Me.txtClass.Text = Me.Class
    Me.txtSALevel.Text = Me.SaLevel
    
    SaVersion_Criteria = BldCompVal(Me.SAVersion, Me.dcComparison.Value, Me.dcComparison.Text)
    SaId_Criteria = BldCompVal(Me.SaId, Me.dcComparison.Value, Me.dcComparison.Text)
    LcId_Criteria = BldCompVal(Me.LcId, Me.dcComparison.Value, Me.dcComparison.Text)
    Class_Criteria = BldCompVal(Me.Class, Me.dcComparison.Value, Me.dcComparison.Text)
    SaLevel_Criteria = BldCompVal(Me.SaLevel, Me.dcComparison.Value, Me.dcComparison.Text)
    
    Me.txtMaxRows.Value = 200
    
    Me.dcComparison.Text = getTranslationResource("Greater Than / Equal To")
    
    'Build initial record set
    Set rsAIMSeasons = New ADODB.Recordset
    With rsAIMSeasons
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        Set .ActiveConnection = Cn
    End With
        
    strSQL = "SELECT SAVersion, SAID, SADesc, SALevel, LcID, Class FROM AIMSeasons "
    strSQL = strSQL & " WHERE SAVersion = 0 AND SAID = 'xxx' "
    rsAIMSeasons.Open strSQL
    
    '************************************************************
    'Mar 01 2002 - Component Conversion Additions
    'Make the spin button visible
    Me.txtMaxRows.Spin.Visible = 1
    '************************************************************
    
    'Bind the grid
    Me.dgSeasons.ReBind

    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "Form_Load"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
    Set rsAIMSeasons = Nothing

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "Form_Unload"
         f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function BldCompVal(CompVals As String, CompType As String, _
    CompDetail As String)
On Error GoTo ErrorHandler

    Dim i, N As Integer
    Dim newtext As String
    Dim RtnStr As String
    Dim strMessage As String
    Dim StrArray(0 To 10) As String
    
    Select Case CompType
    Case "LIKE"
        Select Case CompDetail
        Case getTranslationResource("Begins With")
            CompVals = "N'" & CompVals & "%" & "'"
        Case getTranslationResource("Contains")
            CompVals = "N'%" & CompVals & "%" & "'"
        End Select
    
    Case "BETWEEN"
        N = Parse(Trim(CompVals), ", ", "'", StrArray())
        
        If N <> 2 Then
            strMessage = getTranslationResource("MSGBOX04801")
            If StrComp(strMessage, "MSGBOX04801") = 0 Then strMessage = "Please enter two values separated by a comma "
            MsgBox strMessage, vbExclamation
            BldCompVal = "Error"
            Exit Function
        
        Else
            CompVals = "N'" & StrArray(0) & "'" & _
                  " AND N'" & StrArray(1) & "'"
            
        End If
        
    Case "IN"
        N = Parse(Trim(CompVals), ", ", "'", StrArray())
        newtext = "("
        
        For i = 0 To (N - 2)
            newtext = newtext & "N'" & StrArray(i) & "', "
        Next i
        
        newtext = newtext & "N'" & StrArray(N - 1) & "')"
        CompVals = newtext
        
    Case Else
        CompVals = "N'" & Trim(CompVals) & "'"
        
    End Select
    
    BldCompVal = CompVals

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldCompVal)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::BldCompVal", Now, gDRGeneralError, True, Err
End Function

Private Sub txtSaId_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(Me.txtSaID.Text) = "" Then
        Exit Sub
    End If
    
    SaId_Criteria = BldCompVal(Me.txtSaID.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtSaId_Validate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtSaId_Validate", Now, gDRGeneralError, True, Err
End Sub


Private Sub txtSaVersion_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    If Trim(Me.txtSAVersion.Text) = "" Then
        Exit Sub
    End If
    
    SaVersion_Criteria = BldCompVal(Me.txtSAVersion.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtSaVersion_Validate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtSaVersion_Validate", Now, gDRGeneralError, True, Err
End Sub


Private Sub txtClass_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(Me.txtClass.Text) = "" Then
        Exit Sub
    End If
    
    Class_Criteria = BldCompVal(Me.txtClass.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtClass_Validate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtClass_Validate", Now, gDRGeneralError, True, Err
End Sub


Private Sub txtLcID_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(txtLcID.Text) = "" Then
        Exit Sub
    End If
    
    LcId_Criteria = BldCompVal(Me.txtLcID.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtLcid_Validate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtLcid_Validate", Now, gDRGeneralError, True, Err
End Sub


Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err
End Sub


Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtSaLevel_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(Me.txtSALevel.Text) = "" Then
        Exit Sub
    End If
    
    SaLevel_Criteria = BldCompVal(Me.txtSALevel.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtSaLevel_Validate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::txtSaLevel_Validate", Now, gDRGeneralError, True, Err
End Sub
