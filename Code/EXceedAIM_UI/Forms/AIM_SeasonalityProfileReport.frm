VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_SeasonalityProfileReport 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Seasonality Profile Report"
   ClientHeight    =   2205
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9135
   Icon            =   "AIM_SeasonalityProfileReport.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2205
   ScaleWidth      =   9135
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   180
      Top             =   1800
      _ExtentX        =   741
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_SeasonalityProfileReport.frx":030A
      ToolBars        =   "AIM_SeasonalityProfileReport.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   1755
      Left            =   75
      TabIndex        =   7
      Top             =   0
      Width           =   8925
      Begin VB.OptionButton ckSALevel 
         Caption         =   "Detail Levels"
         Height          =   300
         Index           =   1
         Left            =   5520
         TabIndex        =   5
         Tag             =   "1"
         Top             =   590
         Width           =   3240
      End
      Begin VB.OptionButton ckSALevel 
         Caption         =   "Summary Levels"
         Height          =   300
         Index           =   2
         Left            =   5520
         TabIndex        =   6
         Tag             =   "1"
         Top             =   950
         Width           =   3240
      End
      Begin VB.OptionButton ckSALevel 
         Caption         =   "All Levels"
         Height          =   300
         Index           =   0
         Left            =   5520
         TabIndex        =   4
         Tag             =   "1"
         Top             =   230
         Value           =   -1  'True
         Width           =   3240
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLcId 
         Bindings        =   "AIM_SeasonalityProfileReport.frx":3733
         DataField       =   " "
         Height          =   345
         Left            =   3795
         TabIndex        =   0
         Top             =   210
         Width           =   840
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   1482
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
         Bindings        =   "AIM_SeasonalityProfileReport.frx":373E
         DataField       =   " "
         Height          =   345
         Left            =   3795
         TabIndex        =   1
         Top             =   570
         Width           =   840
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   3
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   1482
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
      End
      Begin TDBNumber6Ctl.TDBNumber txtSaVersion 
         Height          =   345
         Left            =   3795
         TabIndex        =   2
         Top             =   930
         Width           =   840
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calculator      =   "AIM_SeasonalityProfileReport.frx":3749
         Caption         =   "AIM_SeasonalityProfileReport.frx":3769
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileReport.frx":37D5
         Keys            =   "AIM_SeasonalityProfileReport.frx":37F3
         Spin            =   "AIM_SeasonalityProfileReport.frx":383D
         AlignHorizontal =   1
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtAxisOption 
         Height          =   345
         Left            =   3795
         TabIndex        =   3
         Top             =   1290
         Width           =   840
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calculator      =   "AIM_SeasonalityProfileReport.frx":3865
         Caption         =   "AIM_SeasonalityProfileReport.frx":3885
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfileReport.frx":38F1
         Keys            =   "AIM_SeasonalityProfileReport.frx":390F
         Spin            =   "AIM_SeasonalityProfileReport.frx":3959
         AlignHorizontal =   1
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#0;-#0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   99
         MinValue        =   1
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label 
         Caption         =   "Class"
         Height          =   300
         Index           =   1
         Left            =   195
         TabIndex        =   11
         Top             =   615
         Width           =   3495
      End
      Begin VB.Label Label 
         Caption         =   "Max Y Axis (Seasonality Profile)"
         Height          =   300
         Index           =   3
         Left            =   195
         TabIndex        =   10
         Top             =   1335
         Width           =   3495
      End
      Begin VB.Label Label 
         Caption         =   "Seasonality Profile Version"
         Height          =   300
         Index           =   2
         Left            =   195
         TabIndex        =   9
         Top             =   975
         Width           =   3495
      End
      Begin VB.Label Label 
         Caption         =   "Location ID"
         Height          =   300
         Index           =   0
         Left            =   195
         TabIndex        =   8
         Top             =   255
         Width           =   3495
      End
   End
End
Attribute VB_Name = "AIM_SeasonalityProfileReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
Dim m_SQLStmt As SQLBuilder        'SQLStmt.cls, defined as a Common File

Private Function BldSqlStmt(LcId As String, Class As String, SAVersion As Integer, _
    SaLevel As Integer)
On Error GoTo ErrorHandler

    Dim Qualifier As String
    
    'Clean up the SQL Statement
    m_SQLStmt.Criterias.DeleteColumn "AIMSeasons", "LcId"
    m_SQLStmt.Criterias.DeleteColumn "AIMSeasons", "Class"
    m_SQLStmt.Criterias.DeleteColumn "AIMSeasons", "SAVersion"
    m_SQLStmt.Criterias.DeleteColumn "AIMSeasons", "SALevel"
    
    If LcId <> getTranslationResource("All") Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "AIMSeasons.LcId", False, False, "=", LcId, ""
    End If
    
    If Class <> getTranslationResource("All") Then
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "AIMSeasons.Class", False, False, "=", Class, ""
    End If
    
    Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
    m_SQLStmt.Criterias.Add Qualifier, "AIMSeasons.SAVersion", True, False, "=", Format(SAVersion, "0"), ""
    
    Select Case SaLevel
    Case 0
    Case 1          'Detail
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "AIMSeasons.SALevel", False, False, "=", "D", ""
    Case 2          'Summary
        Qualifier = IIf(m_SQLStmt.Criterias.Count = 0, "WHERE", "AND")
        m_SQLStmt.Criterias.Add Qualifier, "AIMSeasons.SALevel", False, False, "=", "S", ""
    End Select

    BldSqlStmt = m_SQLStmt.SQLExpr
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldSqlStmt)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::BldSqlStmt", Now, gDRGeneralError, True, Err
End Function


Function SetFormCriteria()
On Error GoTo ErrorHandler
    
    Dim CompValue1 As String

    'Set Criteria Selection Controls
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("AIMSeasons", "LcId")
    Me.dcLcId.Text = IIf(CompValue1 = "", getTranslationResource("All"), CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("AIMSeasons", "Class")
    Me.dcClass.Text = IIf(CompValue1 = "", getTranslationResource("All"), CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("AIMSeasons", "SAVersion")
    Me.txtSaVersion.Value = IIf(CompValue1 = "", 0, CompValue1)
    
    CompValue1 = m_SQLStmt.Criterias.IsCriteria("AIMSeasons", "SALevel")
    Select Case CompValue1
    Case ""
        Me.ckSALevel(0).Value = True
    Case "D"
        Me.ckSALevel(1).Value = True
    Case "S"
        Me.ckSALevel(2).Value = True
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(SetFormCriteria)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::SetFormCriteria", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim Class As String
    Dim i As Integer
    Dim LcId As String
    Dim SeasProfReport As AIM_SeasonalityProfiles_Report
    Dim SAVersion As Integer
    Dim SaLevel As Integer
    Dim SQL As String
    Dim strMessage As String
    Dim rsReport As ADODB.Recordset
    
    Select Case Tool.ID
    
    Case "ID_Print", "ID_Preview"
        'Housekeeping
        Screen.MousePointer = vbHourglass
        strMessage = getTranslationResource("STATMSG04900")
        If StrComp(strMessage, "STATMSG04900") = 0 Then strMessage = "Building the Seasonality Profile Report ..."
        Write_Message strMessage
        
        'Determine the selection criteria
        gMaxYValue = Me.txtAxisOption.Value
        Class = Me.dcClass.Value
        LcId = Me.dcLcId.Value
        
        For i = Me.ckSALevel.LBound To Me.ckSALevel.UBound
        
            If Me.ckSALevel(i).Value = True Then
                SaLevel = i
                Exit For
            End If
        
        Next i
        
        SAVersion = Me.txtSaVersion.Value
        
        'Build SQL Statement
        SQL = BldSqlStmt(LcId, Class, SAVersion, SaLevel)
        
        'Open the report result set
        Set rsReport = New ADODB.Recordset
        rsReport.Open SQL, Cn, adOpenStatic, adLockReadOnly
        
        If f_IsRecordsetOpenAndPopulated(rsReport) Then
            'Instantiate the Report
            Set SeasProfReport = New AIM_SeasonalityProfiles_Report
            Set SeasProfReport.dcAIMSeasons.Recordset = rsReport
            
            Select Case Tool.ID
            Case "ID_Print"
                SeasProfReport.PrintReport True
        
            Case "ID_Preview"
                Set AIM_Reports.ARViewer.ReportSource = SeasProfReport
                AIM_Reports.Show vbModal, AIM_Main
        
            End Select
        Else
            strMessage = getTranslationResource("TEXTMSG05103")
            If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
            Write_Message strMessage
        End If
        
        'Wrap Up
        Write_Message ""
        Screen.MousePointer = vbNormal
        
        If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
        Set rsReport = Nothing
        
        Set SeasProfReport = Nothing
        Set AIM_SeasonalityProfiles_Report = Nothing
        
    Case "ID_Filter"
        'Update the Sql Statment
        Class = Me.dcClass.Value
        LcId = Me.dcLcId.Value
        
        For i = Me.ckSALevel.LBound To Me.ckSALevel.UBound
            If Me.ckSALevel(i).Value = True Then
                SaLevel = i
                Exit For
            End If
        Next i
        
        SAVersion = Me.txtSaVersion.Value
        
        BldSqlStmt LcId, Class, SAVersion, SaLevel
        
        Set AIM_SelectionCriteria.p_SQLStmt = m_SQLStmt
        Set AIM_SelectionCriteria.Cn = Cn
    
        AIM_SelectionCriteria.Show vbModal, AIM_Main
        
        SetFormCriteria
    
    Case "ID_Close"
        Set rsReport = Nothing
        Unload Me
        Exit Sub
    
    End Select
    
Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing
    
    Set SeasProfReport = Nothing
    Set AIM_SeasonalityProfiles_Report = Nothing
    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcClass_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcClass.Columns(0).Caption = getTranslationResource("Class")
    Me.dcClass.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcClass.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcClass.Columns(0).Width = 1000
    
    Me.dcClass.Columns(1).Caption = getTranslationResource("Class Level")
    Me.dcClass.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcClass.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcClass.Columns(1).Width = 720
    
    Me.dcClass.Columns(2).Caption = getTranslationResource("Class Description")
    Me.dcClass.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dcClass.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcClass.Columns(2).Width = 2000

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcClass, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcClass.Columns.Count - 1
'        dcClass.Columns(IndexCounter).HasHeadBackColor = True
'        dcClass.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcClass_InitColumnProps)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dcClass_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLcId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLcId.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLcId.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId.Columns(0).Width = 720
    
    Me.dcLcId.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLcId.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLcId.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLcId.Columns(1).Width = 2000
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLcId, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcLcID.Columns.Count - 1
'        dcLcID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLcId.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLcId_InitColumnProps)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::dcLcId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG04901")
    If StrComp(strMessage, "STATMSG04901") = 0 Then strMessage = "Initializing Seasonality Profile Report... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
        
    Set m_SQLStmt = New SQLBuilder
    
    'Build the Location List
    LoadLocationsList
    
    'Build the Class List
    LoadClassList
    
    'Initialize Form Variables
    Me.dcLcId.Value = getTranslationResource("All")
    Me.dcClass.Value = getTranslationResource("All")
    Me.txtSaVersion.Value = 0
    Me.txtAxisOption.Value = 4
    
    'Set up the SQL Statement
    m_SQLStmt.SQLTables.Add "AIMSeasons"
    m_SQLStmt.SelExpr = "SELECT * "
    m_SQLStmt.FromExpr = "FROM AIMSeasons "
    m_SQLStmt.OrderExpr = "ORDER BY SAId "

    'Add to Windows List
    AddToWindowList Me.Caption

    Me.txtAxisOption.Spin.Visible = 1
    Me.txtSaVersion.Spin.Visible = 1

    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SeasonalityProfileLookUp::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error Resume Next

    Set m_SQLStmt = Nothing
    
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal
        
End Sub

Private Function LoadClassList()
On Error GoTo ErrorHandler
    
    Dim rsClassList As ADODB.Recordset
    Dim RtnCode As Long
    
    'Set default values
    Me.dcClass.AddItem getTranslationResource("All") + vbTab + _
                       getTranslationResource("All") + vbTab + _
                       getTranslationResource("All Classes")
    
    'Command object for the Stored Procedure
    RtnCode = GetClassList(Cn, "ALL", -1, gLangID, rsClassList)
        
    'Load the data into the dropdown
    If f_IsRecordsetOpenAndPopulated(rsClassList) Then
        Do Until rsClassList.eof
            Me.dcClass.AddItem CStr(rsClassList!Class) + vbTab + _
                               CStr(rsClassList!ClassLevel) + vbTab + _
                               CStr(rsClassList!ClassDesc)
            
            rsClassList.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsClassList) Then rsClassList.Close
    Set rsClassList = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsClassList) Then rsClassList.Close
    Set rsClassList = Nothing
    'Err.Raise Err.Number, Err.source & "(LoadClassList)", Err.Description
    f_HandleErr , , , "AIM_SeasonalityProfileLookUp::LoadClassList", Now, gDRGeneralError, True, Err
End Function

Private Function LoadLocationsList()
    
    Dim rsLcIdList As ADODB.Recordset
    Dim AIM_Locations_List_Sp As ADODB.Command
    
    'Set default values
    Me.dcLcId.AddItem getTranslationResource("All") + vbTab + _
                       getTranslationResource("All Locations")
    
    'Command object for the Stored Procedure
    Set AIM_Locations_List_Sp = New ADODB.Command
    With AIM_Locations_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Locations_List_Sp"
        .Parameters.Refresh
        .Parameters("@LcIdOption").Value = "All"
    End With
    
    'Recordset to catch the data.
    Set rsLcIdList = New ADODB.Recordset
    With rsLcIdList
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    'Fetch data
    Set rsLcIdList = AIM_Locations_List_Sp.Execute
    
    'Load the data into the dropdown
    If f_IsRecordsetOpenAndPopulated(rsLcIdList) Then
        Do Until rsLcIdList.eof
            Me.dcLcId.AddItem CStr(rsLcIdList!LcId) + vbTab + _
                               CStr(rsLcIdList!LName)
            rsLcIdList.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsLcIdList) Then rsLcIdList.Close
    Set rsLcIdList = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsLcIdList) Then rsLcIdList.Close
    Set rsLcIdList = Nothing
    'Err.Raise Err.Number, Err.source & "(LoadClassList)", Err.Description
    f_HandleErr , , , "AIM_SeasonalityProfileLookUp::LoadClassList", Now, gDRGeneralError, True, Err
End Function
