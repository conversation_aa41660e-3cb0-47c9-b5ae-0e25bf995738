VERSION 5.00
Begin VB.Form AIM_DxPO 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Send Purchase Orders to Host"
   ClientHeight    =   2220
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   6210
   Icon            =   "AIM_DxPO.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   2220
   ScaleWidth      =   6210
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   2400
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   1800
      Width           =   1812
   End
   Begin VB.CommandButton cmdTransmit 
      Caption         =   "&Transmit Order(s)"
      Default         =   -1  'True
      Height          =   345
      Left            =   4320
      Style           =   1  'Graphical
      TabIndex        =   3
      Top             =   1800
      Width           =   1812
   End
   Begin VB.Frame frmTransmitPOs 
      Height          =   1095
      Left            =   158
      TabIndex        =   2
      Top             =   360
      Width           =   5895
      Begin VB.OptionButton optCurrent 
         Caption         =   "Transmit Purchase Orders for current Order"
         Height          =   255
         Left            =   240
         TabIndex        =   1
         Top             =   720
         Width           =   5415
      End
      Begin VB.OptionButton optAllReleased 
         Caption         =   "Transmit Purchase Orders for all Released Orders"
         Height          =   255
         Left            =   240
         TabIndex        =   0
         Top             =   240
         Value           =   -1  'True
         Width           =   5415
      End
   End
End
Attribute VB_Name = "AIM_DxPO"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public ById As String
Public VnId As String
Public Assort As String

Public CancelFlag As Boolean

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    CancelFlag = True
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , Me.Caption & "::CancelFlag", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdTransmit_Click()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01400")
    If StrComp(strMessage, "STATMSG01400") = 0 Then strMessage = "Transmitting Purchase Orders to host..."
    Write_Message strMessage

    If Me.optAllReleased Then
        VnId = ""
        Assort = ""
        RtnCode = DxPO_Ctrl("B", ById, VnId, Assort)
    
    Else
        RtnCode = DxPO_Ctrl("V", ById, VnId, Assort)
    
    End If

    'Wind Up
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    CancelFlag = False
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdTransmit_Click)"
     f_HandleErr , , , Me.Caption & "::cmdTransmit_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    Dim AccessLevel As Integer
    GetTranslatedCaptions Me
    AccessLevel = VerifyAccess(gUserID, AF_DXPO, "")
    If AccessLevel = g_ACCESS_READ Then
        cmdTransmit.Enabled = False
    Else
         cmdTransmit.Enabled = True
    End If
    
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
End Sub
