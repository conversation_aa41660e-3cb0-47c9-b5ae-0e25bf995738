VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_TransferManagement 
   BorderStyle     =   1  'Fixed Single
   Caption         =   "SSA DR Transfer Management"
   ClientHeight    =   9570
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   15255
   Icon            =   "AIM_TransferManagement.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9570
   ScaleWidth      =   15255
   WindowState     =   2  'Maximized
   Begin ActiveTabs.SSActiveTabs tabTransferMgmt 
      Height          =   8895
      Left            =   120
      TabIndex        =   0
      Top             =   120
      Width           =   14895
      _ExtentX        =   26273
      _ExtentY        =   15690
      _Version        =   131083
      TabCount        =   2
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontSelectedTab {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty FontHotTracking {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      TagVariant      =   ""
      Tabs            =   "AIM_TransferManagement.frx":030A
      Begin ActiveTabs.SSActiveTabPanel transferConfigPanel 
         Height          =   8505
         Left            =   30
         TabIndex        =   29
         Top             =   360
         Width           =   14835
         _ExtentX        =   26167
         _ExtentY        =   15002
         _Version        =   131083
         TabGuid         =   "AIM_TransferManagement.frx":03CA
         Begin VB.CheckBox CkDFS 
            Caption         =   "Exclude Drop Ship"
            Height          =   255
            Left            =   3240
            TabIndex        =   4
            Top             =   1320
            Width           =   2175
         End
         Begin VB.Frame Frame3 
            Caption         =   "Classification Codes"
            Height          =   2175
            Left            =   120
            TabIndex        =   53
            Top             =   6240
            Width           =   14580
            Begin VB.CheckBox ckExcludeOpt 
               Caption         =   "Exclude Classification Codes"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   240
               TabIndex        =   23
               Top             =   240
               Width           =   5025
            End
            Begin VB.OptionButton ckClass 
               Caption         =   "Classification Code 1"
               Height          =   300
               Index           =   1
               Left            =   345
               TabIndex        =   24
               Top             =   600
               Value           =   -1  'True
               Width           =   5025
            End
            Begin VB.OptionButton ckClass 
               Caption         =   "Classification Code 2"
               Height          =   300
               Index           =   2
               Left            =   345
               TabIndex        =   25
               Top             =   960
               Width           =   5025
            End
            Begin VB.OptionButton ckClass 
               Caption         =   "Classification Code 3"
               Height          =   300
               Index           =   3
               Left            =   345
               TabIndex        =   26
               Top             =   1320
               Width           =   5025
            End
            Begin VB.OptionButton ckClass 
               Caption         =   "Classification Code 4"
               Height          =   300
               Index           =   4
               Left            =   345
               TabIndex        =   27
               Top             =   1680
               Width           =   5025
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMClasses 
               Height          =   1785
               Left            =   5520
               TabIndex        =   28
               Top             =   240
               Width           =   8835
               _Version        =   196617
               DataMode        =   1
               RecordSelectors =   0   'False
               AllowColumnMoving=   0
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               _ExtentX        =   15584
               _ExtentY        =   3149
               _StockProps     =   79
               Caption         =   "Classification Codes"
            End
         End
         Begin VB.Frame Frame4 
            Caption         =   "Item Status Codes"
            Height          =   1665
            Left            =   120
            TabIndex        =   52
            Top             =   4005
            Width           =   5220
            Begin VB.CheckBox ckActive 
               Caption         =   "Active Item"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   340
               Left            =   120
               TabIndex        =   18
               Top             =   225
               Value           =   1  'Checked
               Width           =   3615
            End
            Begin VB.CheckBox ckHighPriority 
               Caption         =   "High Priority Item"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   340
               Left            =   120
               TabIndex        =   19
               Top             =   570
               Value           =   1  'Checked
               Width           =   3615
            End
            Begin VB.CheckBox ckNew 
               Caption         =   "New Item"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   340
               Left            =   120
               TabIndex        =   20
               Top             =   915
               Width           =   3615
            End
            Begin VB.CheckBox ckInactive 
               Caption         =   "Inactive Item"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   340
               Left            =   120
               TabIndex        =   21
               Top             =   1230
               Width           =   3615
            End
         End
         Begin VB.CheckBox ckUseClassCodes 
            Caption         =   "Filter by Classification Codes"
            BeginProperty DataFormat 
               Type            =   5
               Format          =   ""
               HaveTrueFalseNull=   1
               TrueValue       =   "True"
               FalseValue      =   "False"
               NullValue       =   ""
               FirstDayOfWeek  =   0
               FirstWeekOfYear =   0
               LCID            =   1033
               SubFormatType   =   7
            EndProperty
            Height          =   340
            Left            =   240
            TabIndex        =   22
            Top             =   5775
            Width           =   5280
         End
         Begin VB.Frame Frame6 
            Caption         =   "Shortage Quantity"
            Height          =   1380
            Left            =   10200
            TabIndex        =   48
            Top             =   4800
            Width           =   4455
            Begin VB.CheckBox ckOrderPt 
               Caption         =   "Order Point "
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   340
               Left            =   720
               TabIndex        =   16
               Top             =   240
               Width           =   3300
            End
            Begin TDBNumber6Ctl.TDBNumber txtShortageWeeks 
               Height          =   345
               Left            =   3300
               TabIndex        =   17
               Top             =   585
               Width           =   975
               _Version        =   65536
               _ExtentX        =   1720
               _ExtentY        =   609
               Calculator      =   "AIM_TransferManagement.frx":03F2
               Caption         =   "AIM_TransferManagement.frx":0412
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_TransferManagement.frx":047E
               Keys            =   "AIM_TransferManagement.frx":049C
               Spin            =   "AIM_TransferManagement.frx":04E6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.0;-##0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.0"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label 
               Caption         =   "Number of Weeks"
               Height          =   300
               Index           =   9
               Left            =   720
               TabIndex        =   51
               Top             =   690
               Width           =   2175
            End
            Begin VB.Label Label 
               Alignment       =   2  'Center
               Caption         =   "+"
               Height          =   300
               Index           =   10
               Left            =   240
               TabIndex        =   50
               Top             =   675
               Width           =   390
            End
            Begin VB.Label Label 
               Alignment       =   2  'Center
               Caption         =   "+"
               Height          =   300
               Index           =   12
               Left            =   240
               TabIndex        =   49
               Top             =   285
               Width           =   390
            End
         End
         Begin VB.Frame Frame1 
            Caption         =   "Overstock Quantity"
            Height          =   1380
            Left            =   5520
            TabIndex        =   42
            Top             =   4800
            Width           =   4500
            Begin VB.CheckBox ckOUTL 
               Caption         =   " Order Point + Order Quantity"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   340
               Left            =   720
               TabIndex        =   11
               Top             =   240
               Width           =   3480
            End
            Begin TDBNumber6Ctl.TDBNumber txtCostFloor 
               Height          =   345
               Left            =   3240
               TabIndex        =   13
               Top             =   945
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_TransferManagement.frx":050E
               Caption         =   "AIM_TransferManagement.frx":052E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_TransferManagement.frx":059A
               Keys            =   "AIM_TransferManagement.frx":05B8
               Spin            =   "AIM_TransferManagement.frx":0602
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#####0.00;-#####0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#####0.00"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999999.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1179653
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNbrOfWeeks 
               Height          =   345
               Left            =   3240
               TabIndex        =   12
               Top             =   585
               Width           =   1095
               _Version        =   65536
               _ExtentX        =   1931
               _ExtentY        =   609
               Calculator      =   "AIM_TransferManagement.frx":062A
               Caption         =   "AIM_TransferManagement.frx":064A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_TransferManagement.frx":06B6
               Keys            =   "AIM_TransferManagement.frx":06D4
               Spin            =   "AIM_TransferManagement.frx":071E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.0;-##0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.0"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label 
               Caption         =   "Number of Weeks"
               Height          =   180
               Index           =   4
               Left            =   720
               TabIndex        =   47
               Top             =   675
               Width           =   2175
            End
            Begin VB.Label Label 
               Alignment       =   2  'Center
               Caption         =   "+"
               Height          =   210
               Index           =   3
               Left            =   270
               TabIndex        =   46
               Top             =   270
               Width           =   315
            End
            Begin VB.Label Label 
               Caption         =   "Cost Floor"
               Height          =   180
               Index           =   5
               Left            =   720
               TabIndex        =   45
               Top             =   1035
               Width           =   2175
            End
            Begin VB.Label Label 
               Alignment       =   2  'Center
               Caption         =   "+"
               Height          =   210
               Index           =   8
               Left            =   270
               TabIndex        =   44
               Top             =   1020
               Width           =   315
            End
            Begin VB.Label Label 
               Alignment       =   2  'Center
               Caption         =   "+"
               Height          =   210
               Index           =   11
               Left            =   270
               TabIndex        =   43
               Top             =   645
               Width           =   315
            End
         End
         Begin VB.Frame Frame7 
            Caption         =   "Reporting Options"
            Height          =   2265
            Left            =   120
            TabIndex        =   41
            Top             =   1620
            Width           =   5235
            Begin VB.OptionButton optReportOption 
               Caption         =   "Overstocks and Shortages"
               Height          =   340
               Index           =   2
               Left            =   120
               TabIndex        =   7
               Tag             =   "2"
               Top             =   810
               Width           =   4770
            End
            Begin VB.OptionButton optReportOption 
               Caption         =   "Shortages Only"
               Height          =   340
               Index           =   1
               Left            =   120
               TabIndex        =   6
               Tag             =   "1"
               Top             =   510
               Width           =   4770
            End
            Begin VB.OptionButton optReportOption 
               Caption         =   "Overstocks Only"
               Height          =   340
               Index           =   0
               Left            =   120
               TabIndex        =   5
               Tag             =   "0"
               Top             =   210
               Width           =   4770
            End
            Begin VB.OptionButton optReportOption 
               Caption         =   "Overstocks with Shortages"
               Height          =   340
               Index           =   3
               Left            =   240
               TabIndex        =   8
               Tag             =   "3"
               Top             =   1380
               Width           =   4770
            End
            Begin VB.OptionButton optReportOption 
               Caption         =   "Shortages with OverStocks"
               Height          =   340
               Index           =   4
               Left            =   240
               TabIndex        =   9
               Tag             =   "4"
               Top             =   1680
               Value           =   -1  'True
               Width           =   4770
            End
            Begin VB.Frame Frame8 
               Caption         =   "Reporting/Transfer Options"
               Height          =   915
               Left            =   120
               TabIndex        =   54
               Top             =   1170
               Width           =   4935
            End
         End
         Begin VB.Frame Frame2 
            Caption         =   "Overstocks"
            Height          =   4575
            Left            =   5520
            TabIndex        =   33
            Top             =   120
            Width           =   4515
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMOSLocations 
               Height          =   4125
               Left            =   120
               TabIndex        =   10
               Top             =   240
               Width           =   4275
               _Version        =   196617
               DataMode        =   1
               RecordSelectors =   0   'False
               HeadLines       =   2
               AllowColumnMoving=   0
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   7541
               _ExtentY        =   7276
               _StockProps     =   79
               Caption         =   "Select Locations"
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
         Begin VB.Frame Frame5 
            Caption         =   "Shortages"
            Height          =   4575
            Left            =   10200
            TabIndex        =   31
            Top             =   120
            Width           =   4455
            Begin VB.CheckBox ckUseTransferPolicy 
               Caption         =   "Use Transfer Policy Locations"
               Height          =   340
               Left            =   120
               TabIndex        =   14
               Top             =   240
               Value           =   1  'Checked
               Width           =   4215
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMLocations 
               Height          =   3660
               Left            =   120
               TabIndex        =   15
               Top             =   705
               Width           =   4155
               _Version        =   196617
               DataMode        =   1
               RecordSelectors =   0   'False
               HeadLines       =   2
               AllowColumnMoving=   0
               AllowColumnSwapping=   0
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               RowHeight       =   423
               CaptionAlignment=   0
               Columns(0).Width=   3200
               Columns(0).DataType=   8
               Columns(0).FieldLen=   4096
               _ExtentX        =   7329
               _ExtentY        =   6456
               _StockProps     =   79
               Caption         =   "Select Locations"
               BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
            End
         End
         Begin TDBText6Ctl.TDBText txtVnId 
            Height          =   315
            Left            =   3165
            TabIndex        =   2
            Top             =   495
            Width           =   2190
            _Version        =   65536
            _ExtentX        =   3863
            _ExtentY        =   556
            Caption         =   "AIM_TransferManagement.frx":0746
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_TransferManagement.frx":07B2
            Key             =   "AIM_TransferManagement.frx":07D0
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   2
            MultiLine       =   -1
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   12
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtAssort 
            Height          =   315
            Left            =   3165
            TabIndex        =   3
            Top             =   855
            Width           =   2190
            _Version        =   65536
            _ExtentX        =   3863
            _ExtentY        =   556
            Caption         =   "AIM_TransferManagement.frx":0814
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_TransferManagement.frx":0880
            Key             =   "AIM_TransferManagement.frx":089E
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   0
            ShowContextMenu =   -1
            MarginLeft      =   3
            MarginRight     =   3
            MarginTop       =   3
            MarginBottom    =   3
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   2
            MultiLine       =   -1
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   12
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   -1
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
            Height          =   315
            Left            =   3165
            TabIndex        =   1
            Top             =   120
            Width           =   2190
            DataFieldList   =   "Userid"
            AllowInput      =   0   'False
            AllowNull       =   0   'False
            _Version        =   196617
            DataMode        =   2
            ForeColorEven   =   0
            BackColorOdd    =   12648447
            Columns(0).Width=   3200
            _ExtentX        =   3863
            _ExtentY        =   556
            _StockProps     =   93
            ForeColor       =   -2147483640
            BackColor       =   -2147483643
            DataFieldToDisplay=   "UserId"
         End
         Begin VB.Label Label 
            Caption         =   "Vendor ID"
            Height          =   300
            Index           =   1
            Left            =   120
            TabIndex        =   40
            Top             =   540
            Width           =   3075
         End
         Begin VB.Label Label 
            Caption         =   "Assortment"
            Height          =   300
            Index           =   2
            Left            =   120
            TabIndex        =   39
            Top             =   900
            Width           =   3075
         End
         Begin VB.Label Label 
            Caption         =   "Buyer ID"
            Height          =   300
            Index           =   0
            Left            =   120
            TabIndex        =   38
            Top             =   165
            Width           =   3075
         End
      End
      Begin ActiveTabs.SSActiveTabPanel transferManagemetPanel 
         Height          =   8505
         Left            =   30
         TabIndex        =   35
         Top             =   360
         Width           =   14835
         _ExtentX        =   26167
         _ExtentY        =   15002
         _Version        =   131083
         TabGuid         =   "AIM_TransferManagement.frx":08E2
         Begin TDBText6Ctl.TDBText txtExtCube 
            Height          =   345
            Left            =   3480
            TabIndex        =   34
            Top             =   7920
            Width           =   1335
            _Version        =   65536
            _ExtentX        =   2355
            _ExtentY        =   609
            Caption         =   "AIM_TransferManagement.frx":090A
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_TransferManagement.frx":0976
            Key             =   "AIM_TransferManagement.frx":0994
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   1
            ShowContextMenu =   0
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin TDBText6Ctl.TDBText txtExtWeight 
            Height          =   345
            Left            =   3480
            TabIndex        =   32
            Top             =   7560
            Width           =   1335
            _Version        =   65536
            _ExtentX        =   2355
            _ExtentY        =   609
            Caption         =   "AIM_TransferManagement.frx":09D8
            BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            DropDown        =   "AIM_TransferManagement.frx":0A44
            Key             =   "AIM_TransferManagement.frx":0A62
            BackColor       =   -2147483643
            EditMode        =   0
            ForeColor       =   -2147483640
            ReadOnly        =   1
            ShowContextMenu =   0
            MarginLeft      =   1
            MarginRight     =   1
            MarginTop       =   1
            MarginBottom    =   1
            Enabled         =   -1
            MousePointer    =   0
            Appearance      =   1
            BorderStyle     =   1
            AlignHorizontal =   0
            AlignVertical   =   0
            MultiLine       =   0
            ScrollBars      =   0
            PasswordChar    =   ""
            AllowSpace      =   -1
            Format          =   ""
            FormatMode      =   1
            AutoConvert     =   -1
            ErrorBeep       =   0
            MaxLength       =   0
            LengthAsByte    =   0
            Text            =   ""
            Furigana        =   0
            HighlightText   =   0
            IMEMode         =   0
            IMEStatus       =   0
            DropWndWidth    =   0
            DropWndHeight   =   0
            ScrollBarMode   =   0
            MoveOnLRKey     =   0
            OLEDragMode     =   0
            OLEDropMode     =   0
         End
         Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgTransferMgmt 
            Height          =   7215
            Left            =   120
            TabIndex        =   30
            Top             =   120
            Width           =   14535
            _Version        =   196617
            DataMode        =   1
            AllowColumnSwapping=   0
            ForeColorEven   =   0
            RowHeight       =   423
            CaptionAlignment=   0
            SplitterVisible =   -1  'True
            Columns(0).Width=   3200
            Columns(0).DataType=   8
            Columns(0).FieldLen=   4096
            _ExtentX        =   25638
            _ExtentY        =   12726
            _StockProps     =   79
            Caption         =   "Transfer Order"
            BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
            BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
               Name            =   "MS Sans Serif"
               Size            =   8.25
               Charset         =   0
               Weight          =   400
               Underline       =   0   'False
               Italic          =   0   'False
               Strikethrough   =   0   'False
            EndProperty
         End
         Begin VB.Label Label 
            Caption         =   "Total Extended Weight"
            Height          =   300
            Index           =   6
            Left            =   240
            TabIndex        =   37
            Top             =   7605
            Width           =   3135
         End
         Begin VB.Label Label 
            Caption         =   "Total Extended Cube"
            Height          =   300
            Index           =   7
            Left            =   240
            TabIndex        =   36
            Top             =   7965
            Width           =   3135
         End
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   240
      Top             =   9120
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Tools           =   "AIM_TransferManagement.frx":0AA6
      ToolBars        =   "AIM_TransferManagement.frx":AFF7
   End
   Begin VB.Menu mnuEidt 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Suggested Transfer Greater then Zero"
         Checked         =   -1  'True
         Index           =   2
      End
   End
End
Attribute VB_Name = "AIM_TransferManagement"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public AddOverStockFromLocation As String
Public AddOverStockToLocation As String
Public AddOverStockItem As String

Const SPLITTERPOSITION As Integer = 2
Const ROWHEIGHT As Integer = 270
Const SMALLFONT As Double = 8.25
Const MEDIUMFONT As Double = 9.75
Const LARGEFONT As Double = 12

Dim ColWidth(28) As Integer
Dim ColLoaded_Detail As Boolean

Dim ReportOrTransferTab As Integer
Dim BldFromLcids As String
Private Type AIMClassRcd
    SelOpt As Boolean
    Class As String
    ClassLevel As Integer
    Description As String
End Type

Private Type AIMLocationsRcd
    SelOpt As Boolean
    LcId As String
    LcDesc As String
End Type

Dim Cn As ADODB.Connection

Dim AIM_PoInsert_Sp As ADODB.Command
Dim AIM_PoHdrInsert_Sp As ADODB.Command
Dim AIM_Transfer_SP As ADODB.Command
Dim AIM_TransferManagement_SP As ADODB.Command
Dim AIM_ValidateTransfer_Sp As ADODB.Command
Dim AIM_TransferToReport_SP As ADODB.Command
Dim rsGetLcidFromAIMTransfer As ADODB.Recordset
Dim rsOverstocks As ADODB.Recordset
Dim rsUpdateItem As ADODB.Recordset
Dim AIMClasses() As AIMClassRcd
Dim AIMLocations() As AIMLocationsRcd
Dim AIMOSLocations() As AIMLocationsRcd
Dim OverStockDBArray As New XArrayDB
Dim SortItemAsc As Boolean
Dim SortFromLcIdAsc As Boolean
Dim SortToLcIdAsc As Boolean

Dim SelectAllClassCodes As Boolean
Dim rsTransferBookmark As Variant
Private Type VendorAssortRcd
    VnId As String
    Assort As String
End Type
Dim VendorAssort() As VendorAssortRcd

Private Function InitColWidths()
On Error GoTo ErrorHandler
'sri begin
'    ColWidth(0) = 400
'    ColWidth(1) = 400
'    ColWidth(2) = 400
    ColWidth(3) = 400
'    ColWidth(4) = 600
    ColWidth(5) = 1440
'    ColWidth(6) = 720
    ColWidth(7) = 2880
'    ColWidth(8) = 720
'    ColWidth(9) = 500
'    ColWidth(10) = 600
'    ColWidth(11) = 720
'    ColWidth(12) = 500
'    ColWidth(13) = 600
'    ColWidth(14) = 800
'    ColWidth(15) = 400
'    ColWidth(16) = 500
'    ColWidth(17) = 500
'    ColWidth(18) = 800
'    ColWidth(19) = 600
'    ColWidth(20) = 800
'    ColWidth(21) = 800
'    ColWidth(22) = 800
'    ColWidth(23) = 800
'    ColWidth(24) = 800
'    ColWidth(25) = 400
'    ColWidth(26) = 400
'    ColWidth(27) = 900
'    ColWidth(28) = 900
 'sri end
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(InitColWidths)"
     f_HandleErr , , , "AIM_TransferManagement::InitColWidths", Now, gDRGeneralError, True, Err
End Function

Private Function BldOverstockStmt()
On Error GoTo ErrorHandler
    
    Dim CriteriaCount As Integer
    Dim Index_ClassLevel As Integer
    Dim IndexCounter As Integer
    Dim ListCount As Integer
    Dim strSQL As String
    Dim DemandOption As Integer
    Dim OneOrTwoOption As String
    
    For IndexCounter = Me.optReportOption.LBound To Me.optReportOption.UBound
        If Me.optReportOption(IndexCounter) = True Then
            DemandOption = optReportOption(IndexCounter).Tag
            Exit For
        End If
    Next IndexCounter
    
    'For DemandOption =1 or 4 we will be building the Shortages here not overstock ie recid =2
    If DemandOption = 1 Or DemandOption = 4 Then
        strSQL = "SELECT RcdId = 2,"
    Else
        strSQL = "SELECT RcdId = 1,"
    End If
    
    '************************************************************
    'Clean up the SQL Statement
    '************************************************************
    'The Report has differt sql then the TransferManagement
    If ReportOrTransferTab = 1 Then
        strSQL = strSQL & vbCrLf & "Item, ItDesc, ItStat, Lcid, Cost, OrderPt, OrderQty, Oh, Oo,"
        strSQL = strSQL & vbCrLf & "ComStk, BkOrder, BkComStk, FcstDemand, ConvFactor "
        strSQL = strSQL & vbCrLf & "FROM Item"
    Else
        strSQL = strSQL & vbCrLf & "Item, ItDesc, ItStat, Lcid, Cost, OrderPt, OrderQty, Oh, Oo,"
        strSQL = strSQL & vbCrLf & "ComStk, BkOrder, BkComStk, FcstDemand, Weight, Cube, ConvFactor,UOM, BinLocation"
        strSQL = strSQL & vbCrLf & "FROM Item "
    End If
    
    '************************************************************
    'Build WHERE clause (with Unicode constant for all string parameters
    '************************************************************
    If Me.dcAIMUsers.Text <> getTranslationResource("All") Then
        strSQL = strSQL & vbCrLf & "WHERE ById = N'" & Me.dcAIMUsers.Text & "'"
        CriteriaCount = CriteriaCount + 1
    End If
    If Me.txtVnID.Text <> "" Then
        strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
        strSQL = strSQL & " VnId = N'" & Me.txtVnID.Text & "'"
        CriteriaCount = CriteriaCount + 1
    End If
    If Me.txtAssort.Text <> "" Then
        strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
        strSQL = strSQL & " Assort = N'" & Me.txtAssort.Text & "'"
        CriteriaCount = CriteriaCount + 1
    End If

    '************************************************************
    'Build the Location List
    '************************************************************
    'For TransferMangagemet Tab the from location is sent as an argument to the stored procedure
    If ReportOrTransferTab = 1 Then
        strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
        strSQL = strSQL & " LcID IN ("
        CriteriaCount = CriteriaCount + 1
        ListCount = 0
        'Use from locations for both overstock and shortages
        For IndexCounter = LBound(AIMOSLocations) To UBound(AIMOSLocations)
            If AIMOSLocations(IndexCounter).SelOpt = True Then
                strSQL = strSQL & IIf(ListCount > 0, ", ", "")
                strSQL = strSQL & "N'" & AIMOSLocations(IndexCounter).LcId & "'"
                ListCount = ListCount + 1
            End If
        Next IndexCounter
        'If no location was specified -- select nothing
        If ListCount > 0 Then
            strSQL = strSQL & ") "
        Else
            strSQL = strSQL & "'XXXXXXXXXXXX') "
        End If
    End If
    
    '************************************************************
    'Build the Quantity clause
    '************************************************************
    'DemandOption=1 or 4 => Shortage Quantity
    If DemandOption = 1 _
    Or DemandOption = 4 _
    Then
        If Me.ckOrderPt = vbChecked Then
            strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
            strSQL = strSQL & " (OH + OO )<= OrderPt + " & _
                            "(FcstDemand * " & Format(Me.txtShortageWeeks.Value, "0") & ")"
        Else
            strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
            strSQL = strSQL & " (Item.OH + Item.OO ) <= (FcstDemand * " & Format(Me.txtShortageWeeks.Value, "0") & ")"
        End If
        
        CriteriaCount = CriteriaCount + 1
    Else
        'This is for Overstock Quantity
        If Me.ckOUTL = vbChecked Then
            strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
            strSQL = strSQL & " OH  - Comstk > OrderPt + OrderQty + " & _
                    "(FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & ")"
        Else
            strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
            strSQL = strSQL & " OH  - Comstk > (FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & ")"
        End If
        CriteriaCount = CriteriaCount + 1
    
        If Me.txtCostFloor.Value > 0 Then
            strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
            If Me.ckOUTL = vbChecked Then
                strSQL = strSQL & " (OH " & _
                        "- ( Comstk + OrderPt + OrderQty " & _
                        "+ (FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & "))) " & _
                        "* Cost " & _
                        " > " & Format(Me.txtCostFloor.Value, "0.00") & " "
            Else
                strSQL = strSQL & " (OH " & _
                        "- ( Comstk  + FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & "))" & _
                        "* Cost" & _
                        " > " & Format(Me.txtCostFloor.Value, "0.00") & " "
            End If
        CriteriaCount = CriteriaCount + 1
        End If
    End If
    
    '************************************************************
    'Item Status Codes
    '************************************************************
    If Me.ckActive = vbChecked _
    Or Me.ckHighPriority = vbChecked _
    Or Me.ckNew = vbChecked _
    Or Me.ckInactive = vbChecked _
    Then
        ListCount = 0
        strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
        strSQL = strSQL & " ItStat IN ("
        
        If Me.ckActive = vbChecked Then
            strSQL = strSQL & "N'A'"
            ListCount = ListCount + 1
        End If
        
        If Me.ckHighPriority = vbChecked Then
            strSQL = strSQL & IIf(ListCount > 0, ", N'H'", "N'H'")
            ListCount = ListCount + 1
        End If
        
        If Me.ckNew = vbChecked Then
            strSQL = strSQL & IIf(ListCount > 0, ", N'N'", "N'N'")
            ListCount = ListCount + 1
        End If
        
        If Me.ckInactive = vbChecked Then
            strSQL = strSQL & IIf(ListCount > 0, ", N'I'", "N'I'")
            ListCount = ListCount + 1
        End If
        
        strSQL = strSQL & ") "
        CriteriaCount = CriteriaCount + 1
    End If
    
    '************************************************************
    'Classification Codes
    '************************************************************
    If ckUseClassCodes = vbChecked Then
        ListCount = 0
        strSQL = strSQL & vbCrLf & IIf(CriteriaCount = 0, "WHERE", "AND")
        
        If Me.ckClass(1) = True Then
            Index_ClassLevel = 1
        ElseIf Me.ckClass(2) = True Then
            Index_ClassLevel = 2
        ElseIf Me.ckClass(3) = True Then
            Index_ClassLevel = 3
        ElseIf Me.ckClass(4) = True Then
            Index_ClassLevel = 4
        End If
        
        If Me.ckExcludeOpt = vbChecked Then
            strSQL = strSQL & " Class" & Format(Index_ClassLevel, "0") & " NOT IN ("
        Else
            strSQL = strSQL & " Class" & Format(Index_ClassLevel, "0") & " IN ("
        End If
        'Build list of possible class codes
        For IndexCounter = LBound(AIMClasses) To UBound(AIMClasses)
            If AIMClasses(IndexCounter).SelOpt = True Then
                strSQL = strSQL & IIf(ListCount > 0, ", ", "")
                strSQL = strSQL & "N'" & AIMClasses(IndexCounter).Class & "'"
                ListCount = ListCount + 1
            End If
        Next IndexCounter
        If ListCount > 0 Then
            strSQL = strSQL & ") "
        Else
            strSQL = strSQL & "'XXXXXX') "
        End If
        
    End If
    
    BldOverstockStmt = strSQL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldOverstockStmt)"
     f_HandleErr , , , "AIM_TransferManagement::BldOverstockStmt", Now, gDRGeneralError, True, Err
End Function

Private Function BldShortageStmt()
On Error GoTo ErrorHandler
    Dim IndexCounter As Integer
    Dim ListCount As Integer
    Dim strSQL As String
    Dim DemandOption As Integer
    Dim OneOrTwoOption As String
    
    For IndexCounter = Me.optReportOption.LBound To Me.optReportOption.UBound
        If Me.optReportOption(IndexCounter) = True Then
            DemandOption = optReportOption(IndexCounter).Tag
            Exit For
        End If
    Next IndexCounter
    
    'For DemandOption =1 or 4 it will be a overstock ie RcdId =1 else it is shortage
    If DemandOption = 1 Or DemandOption = 4 Then
        strSQL = "SELECT  DISTINCT RcdId = 1,"
    Else
        strSQL = "SELECT  DISTINCT RcdId = 2,"
    End If
    
    '************************************************************
    'Clean up the SQL Statement
    '************************************************************
    'Different strSQL for Report and TransferManagementTab
    If ReportOrTransferTab = 1 Then
        strSQL = strSQL & vbCrLf & "Item.Item, Item.ItDesc, Item.ItStat, Item.Lcid,"
        strSQL = strSQL & vbCrLf & "Item.Cost, Item.OrderPt, Item.OrderQty, Item.Oh, Item.Oo,"
        strSQL = strSQL & vbCrLf & "Item.ComStk, Item.BkOrder, Item.BkComStk, Item.FcstDemand, Item.ConvFactor"
        strSQL = strSQL & vbCrLf & "FROM #OverStocks"
        strSQL = strSQL & vbCrLf & "INNER JOIN Item ON #OverStocks.Item = Item.Item"
    Else
        strSQL = strSQL & vbCrLf & "Item.Item, Item.ItDesc, Item.ItStat, Item.Lcid,"
        strSQL = strSQL & vbCrLf & "Item.Cost, Item.OrderPt, Item.OrderQty, Item.Oh, Item.Oo,"
        strSQL = strSQL & vbCrLf & "Item.ComStk, Item.BkOrder, Item.BkComStk, Item.FcstDemand,"
        strSQL = strSQL & vbCrLf & " Item.Weight, Item.Cube, Item.ConvFactor,Item.UOM, Item.BinLocation"
        strSQL = strSQL & vbCrLf & "FROM #OverStocks"
        strSQL = strSQL & vbCrLf & "INNER JOIN Item ON #OverStocks.Item = Item.Item"
    End If
    
    '************************************************************
    'Build WHERE clause (with Unicode constant for all string parameters
    '************************************************************
    strSQL = strSQL & vbCrLf & "WHERE Item.ItStat IN (N'A', N'H', N'N')"
    
    '************************************************************
    'Build the Quantity clause
    '************************************************************
    'For DemandOption 1 or 4 use this criteria ie shortages criteria
    If DemandOption = 1 Or DemandOption = 4 Then
        If Me.ckOUTL = vbChecked Then
            strSQL = strSQL & vbCrLf & "AND ( Item.OH -Item.ComStk )  > " & _
                    "Item.OrderPt + Item.OrderQty" & _
                    " + (Item.FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & ")"
        Else
            strSQL = strSQL & vbCrLf & "AND (Item.OH -Item.ComStk) > " & _
                    "(Item.FcstDemand " & _
                    "* " & Format(Me.txtNbrOfWeeks.Value, "0") & ")"
        End If
        
        If Me.txtCostFloor.Value > 0 Then
            If Me.ckOUTL = vbChecked Then
            
            
                strSQL = strSQL & vbCrLf & "AND (Item.OH  - Item.ComStk" & _
                        "- (Item.OrderPt + Item.OrderQty " & _
                        "+ (Item.FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & ")))" & _
                        "* Item.Cost" & _
                        " > " & Format(Me.txtCostFloor.Value, "0.00")

            Else
                strSQL = strSQL & vbCrLf & "AND (Item.Oh  - Item.ComStk " & _
                        "- (Item.FcstDemand * " & Format(Me.txtNbrOfWeeks.Value, "0") & "))" & _
                        "* Item.Cost" & _
                        " > " & Format(Me.txtCostFloor.Value, "0.00")
            End If
        End If
    Else
        'For DemandOption 0,2,3  use this criteria ie overstock criteria
        If Me.ckOrderPt = vbChecked Then
            strSQL = strSQL & vbCrLf & "AND (Item.OH + Item.OO ) " & _
                    "<= Item.OrderPt " & _
                    " + (Item.FcstDemand * " & Format(Me.txtShortageWeeks.Value, "0") & ")"
        Else
            strSQL = strSQL & vbCrLf & "AND (Item.oh + Item.OO ) " & _
                    "<= (Item.FcstDemand * " + Format(Me.txtShortageWeeks.Value, "0") & ")"
        End If
    End If
      
    '************************************************************
    'Build the Location List
    '************************************************************
    'If it is Report and only overstock or shortage we need lcid's else it read from table
    'sri some code need to be modified
    If (ReportOrTransferTab = 1 And DemandOption <> 3) _
    And (ReportOrTransferTab = 1 And DemandOption <> 4) _
    Then
        'Build the Location List
        strSQL = strSQL & vbCrLf & "AND Item.LcID IN ("
        ListCount = 0
        'There may be some redundancy in this code sri
        If DemandOption = 1 _
        Or DemandOption = 4 _
        Then
            For IndexCounter = LBound(AIMOSLocations) To UBound(AIMOSLocations)
                If AIMOSLocations(IndexCounter).SelOpt = True Then
                    strSQL = strSQL & vbCrLf & IIf(ListCount > 0, ", ", "")
                    strSQL = strSQL & "N'" & AIMOSLocations(IndexCounter).LcId & "'"
                    ListCount = ListCount + 1
                End If
            Next IndexCounter
        Else
            For IndexCounter = LBound(AIMLocations) To UBound(AIMLocations)
                If AIMLocations(IndexCounter).SelOpt = True Then
                    strSQL = strSQL & vbCrLf & IIf(ListCount > 0, ", ", "")
                    strSQL = strSQL & "N'" & AIMLocations(IndexCounter).LcId & "'"
                    ListCount = ListCount + 1
                End If
            Next IndexCounter
        End If
            
        'If no location was specified -- select nothing
        If ListCount > 0 Then
            strSQL = strSQL & ") "
        Else
            strSQL = strSQL & "'XXXXXXXXXXXX') "
        End If
    End If
   
    BldShortageStmt = strSQL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldShortageStmt)"
    f_HandleErr , , , "AIM_TransferManagement::BldShortageStmt", Now, gDRGeneralError, True, Err
End Function

Private Sub dgTransferMgmtInitColumnProps()
'AStocks -- As an event, it distorts the other grids on form load. Review later.
On Error GoTo ErrorHandler
    Dim IndexCounter As Long
    
    dgTransferMgmt.HeadLines = 2
    'Restore the grid layout
    On Error Resume Next
    'Me.dgTransferMgmt.LoadLayout App.Path & "\" & Trim(gUserID) & "_TransferLayout.grd"
    'Check for non-existent file
    If Err.Number = 53 Then     'File Not Found
        Resume Next
    ElseIf Err.Number <> 0 Then
        GoTo ErrorHandler
    End If
    On Error GoTo ErrorHandler
    
    'Define Columns
    Me.dgTransferMgmt.Columns(0).Name = "Delete"
    Me.dgTransferMgmt.Columns(0).Caption = getTranslationResource("T")
    Me.dgTransferMgmt.Columns(0).Width = 400
    Me.dgTransferMgmt.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgTransferMgmt.Columns(0).Style = ssStyleCheckBox
    Me.dgTransferMgmt.Columns(0).Locked = False
        
    Me.dgTransferMgmt.Columns(1).Name = "Item"
    Me.dgTransferMgmt.Columns(1).Caption = getTranslationResource("Item")
    Me.dgTransferMgmt.Columns(1).Width = 1600
    Me.dgTransferMgmt.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgTransferMgmt.Columns(1).Locked = True
    
    Me.dgTransferMgmt.Columns(2).Name = "itdesc"
    Me.dgTransferMgmt.Columns(2).Caption = getTranslationResource("Description")
    Me.dgTransferMgmt.Columns(2).Width = 2200
    Me.dgTransferMgmt.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgTransferMgmt.Columns(2).Locked = True
    
    Me.dgTransferMgmt.Columns(3).Name = "FromLcId"
    Me.dgTransferMgmt.Columns(3).Caption = getTranslationResource("From Loc")
    Me.dgTransferMgmt.Columns(3).Width = 1200
    Me.dgTransferMgmt.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgTransferMgmt.Columns(3).Locked = True
       
    Me.dgTransferMgmt.Columns(4).Name = "Fromoh"
    Me.dgTransferMgmt.Columns(4).Caption = getTranslationResource("From OH - ComStk")
    Me.dgTransferMgmt.Columns(4).Width = 1200
    Me.dgTransferMgmt.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(4).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(4).Locked = True
    
    Me.dgTransferMgmt.Columns(5).Name = "FromBestQty"
    Me.dgTransferMgmt.Columns(5).Caption = getTranslationResource("From Best Qty")
    Me.dgTransferMgmt.Columns(5).Width = 1200
    Me.dgTransferMgmt.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(5).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(5).Locked = True
       
    Me.dgTransferMgmt.Columns(6).Name = "SugTrasQty"
    Me.dgTransferMgmt.Columns(6).Caption = getTranslationResource("Suggested Transfer Qty")
    Me.dgTransferMgmt.Columns(6).Width = 1400
    Me.dgTransferMgmt.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(6).Locked = False
    
    Me.dgTransferMgmt.Columns(7).Name = "ToLcId"
    Me.dgTransferMgmt.Columns(7).Caption = getTranslationResource("To Location")
    Me.dgTransferMgmt.Columns(7).Width = 1200
    Me.dgTransferMgmt.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(7).Alignment = ssCaptionAlignmentLeft
    Me.dgTransferMgmt.Columns(7).Locked = True
    
    Me.dgTransferMgmt.Columns(8).Name = "ToOh"
    Me.dgTransferMgmt.Columns(8).Caption = getTranslationResource("To OH + OO")
    Me.dgTransferMgmt.Columns(8).Width = 1200
    Me.dgTransferMgmt.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(8).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(8).Locked = True
    
    Me.dgTransferMgmt.Columns(9).Name = "ToBestQty"
    Me.dgTransferMgmt.Columns(9).Caption = getTranslationResource("To Best Qty")
    Me.dgTransferMgmt.Columns(9).Width = 1200
    Me.dgTransferMgmt.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(9).Locked = True
    
    Me.dgTransferMgmt.Columns(10).Name = "ToFcstDmd"
    Me.dgTransferMgmt.Columns(10).Caption = getTranslationResource("To Fcst Demand")
    Me.dgTransferMgmt.Columns(10).Width = 1200
    Me.dgTransferMgmt.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(10).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(10).Locked = True
        
    Me.dgTransferMgmt.Columns(11).Name = "FromLocDmd"
    Me.dgTransferMgmt.Columns(11).Caption = getTranslationResource("From Fcst Demand")
    Me.dgTransferMgmt.Columns(11).Width = 1200
    Me.dgTransferMgmt.Columns(11).NumberFormat = "#,###,##0.00"
    Me.dgTransferMgmt.Columns(11).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(11).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(11).Locked = True
    
    Me.dgTransferMgmt.Columns(12).Name = "ExItemWeight"
    Me.dgTransferMgmt.Columns(12).Caption = getTranslationResource("Weight")
    Me.dgTransferMgmt.Columns(12).Width = 1200
    Me.dgTransferMgmt.Columns(12).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(12).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(12).NumberFormat = "####0.00"
    Me.dgTransferMgmt.Columns(12).Locked = True
       
    Me.dgTransferMgmt.Columns(13).Name = "ExItemCube"
    Me.dgTransferMgmt.Columns(13).Caption = getTranslationResource("Cube")
    Me.dgTransferMgmt.Columns(13).Width = 1200
    Me.dgTransferMgmt.Columns(13).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(13).Alignment = ssCaptionAlignmentRight
    Me.dgTransferMgmt.Columns(13).NumberFormat = "####0.00"
    Me.dgTransferMgmt.Columns(13).Locked = True
       
    Me.dgTransferMgmt.Columns(14).Name = "itstat"
    Me.dgTransferMgmt.Columns(14).Caption = getTranslationResource("Status")
    Me.dgTransferMgmt.Columns(14).Width = 800
    Me.dgTransferMgmt.Columns(14).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(14).Alignment = ssCaptionAlignmentCenter
    Me.dgTransferMgmt.Columns(14).ButtonsAlways = True
    Me.dgTransferMgmt.Columns(14).Locked = True
    
    Me.dgTransferMgmt.Columns(15).Name = "convfactor"
    Me.dgTransferMgmt.Columns(15).Caption = getTranslationResource("Pack Size")
    Me.dgTransferMgmt.Columns(15).Width = 1200
    Me.dgTransferMgmt.Columns(15).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgTransferMgmt.Columns(15).Alignment = ssCaptionAlignmentCenter
    Me.dgTransferMgmt.Columns(15).NumberFormat = "0"
    Me.dgTransferMgmt.Columns(15).Locked = True
    
    
    'Set Splitposition
    Me.dgTransferMgmt.SplitterPos = SPLITTERPOSITION
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgTransferMgmt, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgTransferMgmt.Columns.Count - 1
'        dgTransferMgmt.Columns(IndexCounter).HasHeadBackColor = True
'        dgTransferMgmt.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgTransferMgmt.Columns(IndexCounter).Locked = False Then dgTransferMgmt.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
        'If dgTransferMgmt.Columns(IndexCounter).Locked = False Then dgTransferMgmt.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_FORECAST)
    Next
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgTransferMgmtInitColumnProps)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmtInitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub CkDFS_Click()
On Error GoTo ErrorHandler

    PopulateAIMLocations

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(CkDFS_Click)"
     f_HandleErr , , , "AIM_TransferManagement::CkDFS_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ckUseTransferPolicy_Click()
On Error GoTo ErrorHandler

    If optReportOption.Item(3) = True Or optReportOption.Item(4) = True Then
        If ckUseTransferPolicy.Value = Checked Then
        ToggleToTargetAccess False
            FillToLcids
        Else
            ToggleToTargetAccess True
        End If
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckUseTransferPolicy_Click)"
     f_HandleErr , , , "AIM_TransferManagement::ckUseTransferPolicy_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMOSLocations_Change()
On Error GoTo ErrorHandler
    
    If optReportOption(3).Value = True Or optReportOption(4).Value = True Then
        If ckUseTransferPolicy.Value = Checked Then
            FillToLcids
        End If
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMOSLocations_Change)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMOSLocations_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEidt
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgForecast_MouseDown)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim FileName As String
    Dim FormatOption As Integer
    Dim i As Integer
    Dim J As Integer
    Dim s1 As String
    Dim s2 As String
    Dim s3 As String
    Dim s() As String
    
    Me.MousePointer = vbHourglass
    
    Select Case Index
    Case 0          'Copy
        'Check for an empty set
        If Me.dgTransferMgmt.Rows = 0 Then
            Screen.MousePointer = vbNormal
            Exit Sub
        End If
        
        Clipboard.Clear
        
        For i = 0 To Me.dgTransferMgmt.Columns.Count - 1
            s1 = s1 & Me.dgTransferMgmt.Columns(i).Caption & vbTab
        Next i

        s1 = s1 & vbCrLf
            ReDim s(OverStockDBArray.Count(1))
            
            For i = OverStockDBArray.LowerBound(1) To OverStockDBArray.UpperBound(1)
                For J = OverStockDBArray.LowerBound(2) To OverStockDBArray.UpperBound(2)
                    s(i) = s(i) & CStr(OverStockDBArray.Value(i, J)) & vbTab
                Next J
                
            Next i
    
            Clipboard.SetText s1 & Join(s, vbCrLf), vbCFText
        
        Erase s
    
    Case 1          'Print
        Me.dgTransferMgmt.PrintData ssPrintAllRows, False, True
        
    Case 2
     If Me.mnuEditOpt(Index).Checked Then
            Me.mnuEditOpt(Index).Checked = False
        Else
            Me.mnuEditOpt(Index).Checked = True
        End If
        'call function to populate the grid
        PopulateTransferGrid
        
    End Select

    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , "AIM_TransferManagement::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim Lrow As Integer
    Dim ExtWeight As Double
    Dim ExtCube As Double
    Dim RtnCode As Integer
    Dim strMessage As String
    Dim Counter As Integer
    
    Select Case Tool.ID
    Case "ID_GetFirst"
        'Navigate to the first detail record
        Me.dgTransferMgmt.MoveFirst
         
    Case "ID_GetPrev"
        'Navigate to the previous detail record
        Me.dgTransferMgmt.MovePrevious
    
    Case "ID_GetNext"
        'Navigate to the next detail record
        Me.dgTransferMgmt.MoveNext
    
    Case "ID_GetLast"
        'Navigate to the last detail record
        Me.dgTransferMgmt.MoveLast
    
    Case "ID_AddNewItem"
        AddOverStockItem = ""
        AddOverStockFromLocation = ""
        AddOverStockToLocation = ""
        AIM_AddTransferStockItem.Show vbModal
        If AddOverStockItem <> "" And AddOverStockFromLocation <> "" And AddOverStockToLocation <> "" Then
            AddNewOverStockItem
        End If
        
    Case "ID_SelectAllForTransfer"
        For Lrow = 0 To OverStockDBArray.UpperBound(1)
            '-1 is true for check box
            OverStockDBArray(Lrow, 0) = -1
            ExtWeight = ExtWeight + OverStockDBArray(Lrow, 19) * OverStockDBArray(Lrow, 6) 'Weight *SugTransferQty
            ExtCube = ExtCube + OverStockDBArray(Lrow, 20) * OverStockDBArray(Lrow, 6) 'Cube *SugTransferQty
        Next
        dgTransferMgmt.ReBind
        dgTransferMgmt.Rows = OverStockDBArray.UpperBound(1) + 1
        txtExtWeight.Text = ExtWeight
        txtExtCube.Text = ExtCube
         
    Case "ID_UnSelectAllForTransfer"
        For Lrow = 0 To OverStockDBArray.UpperBound(1)
            '0 is false for check box
            OverStockDBArray(Lrow, 0) = 0
        Next
        dgTransferMgmt.ReBind
        dgTransferMgmt.Rows = OverStockDBArray.UpperBound(1) + 1
        txtExtWeight.Text = 0
        txtExtCube.Text = 0

    Case "ID_Save"
        strMessage = getTranslationResource("MSGBOX03801")
        If StrComp(strMessage, "MSGBOX03801") = 0 Then strMessage = "Do you wish to save the changes to the Transfer record?"
        RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
        If RtnCode = vbYes Then
            SaveTransferRecord
            tabTransferMgmt.SelectedTab = 1
        Else
            'ignore
        End If
    
    Case "ID_SetBookMark"
        'Toggle bookmark
        If IsEmpty(rsTransferBookmark) _
        Or IsNull(rsTransferBookmark) _
        Then
            rsTransferBookmark = dgTransferMgmt.Bookmark
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        Else
            rsTransferBookmark = Null
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
        End If
        
    Case "ID_GoToBookMark"
        'Navigate to the bookmarked record
        If Not IsEmpty(rsTransferBookmark) _
        Or Not IsNull(rsTransferBookmark) _
        Then
            dgTransferMgmt.Bookmark = rsTransferBookmark
        End If

    Case "ID_Print", "ID_Preview"
        ProcessReport Tool.ID
        
    Case "ID_Close"
        Unload Me
        Exit Sub
    
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_TransferManagement::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
    Screen.MousePointer = vbDefault
End Sub

Private Sub ckClass_Click(Index As Integer)
On Error GoTo ErrorHandler
    
    FetchAIMClasses
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckClass_Click)"
     f_HandleErr , , , "AIM_TransferManagement::ckClass_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub optReportOption_Click(Index As Integer)
On Error GoTo ErrorHandler
    
    Select Case Index
        Case 0
            tabTransferMgmt.Tabs.Item(2).Enabled = False
            ToggleToTargetAccess False
            ToggleOverstockQtyAccess True
            ToggleShortageQtyAccess False
            ckUseTransferPolicy.Value = Unchecked
            ckUseTransferPolicy.Enabled = False
            Frame2.Caption = getTranslationResource("Overstocks")
            Frame5.Caption = getTranslationResource("Shortages")
            ClearToLcids
        Case 1
            tabTransferMgmt.Tabs.Item(2).Enabled = False
            ToggleToTargetAccess False
            ToggleOverstockQtyAccess False
            ToggleShortageQtyAccess True
            ckUseTransferPolicy.Value = Unchecked
            ckUseTransferPolicy.Enabled = False
            Frame2.Caption = getTranslationResource("Shortages")
            Frame5.Caption = getTranslationResource("Overstocks")
            ClearToLcids
        Case 2
            tabTransferMgmt.Tabs.Item(2).Enabled = False
            ToggleToTargetAccess True   'Note
            ToggleOverstockQtyAccess True
            ToggleShortageQtyAccess True
            ckUseTransferPolicy.Value = Unchecked
            ckUseTransferPolicy.Enabled = False
            Frame2.Caption = getTranslationResource("Overstocks")
            Frame5.Caption = getTranslationResource("Shortages")
            ClearToLcids
       Case 3, 4
            tabTransferMgmt.Tabs.Item(2).Enabled = True 'Note
            ToggleToTargetAccess False
            ToggleOverstockQtyAccess True
            ToggleShortageQtyAccess True
            ckUseTransferPolicy.Value = Checked
            ckUseTransferPolicy.Enabled = True
            Frame5.Enabled = True
            'Automatically populate the To Target Lcid from AIMTransferPolicy table valid for index 3,4
            If Index = 3 Then
                 Frame2.Caption = getTranslationResource("Overstocks")
                 Frame5.Caption = getTranslationResource("Shortages")
            Else
                 Frame2.Caption = getTranslationResource("Shortages")
                Frame5.Caption = getTranslationResource("Overstocks")
            End If
            FillToLcids
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(optReportOption_Click)"
     f_HandleErr , , , "AIM_TransferManagement::optReportOption_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ckUseClassCodes_Click()
On Error GoTo ErrorHandler
    
    If ckUseClassCodes <> vbChecked Then
        Frame3.Enabled = False
        ckClass(1).Enabled = False
        ckClass(2).Enabled = False
        ckClass(3).Enabled = False
        ckClass(4).Enabled = False
        ckExcludeOpt.Enabled = False
        dgAIMClasses.Enabled = False
    
    Else
        Frame3.Enabled = True
        ckClass(1).Enabled = True
        ckClass(2).Enabled = True
        ckClass(3).Enabled = True
        ckClass(4).Enabled = True
        ckExcludeOpt.Enabled = True
        dgAIMClasses.Enabled = True
        FetchAIMClasses
    End If
       
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckUseClassCodes_Click)"
     f_HandleErr , , , "AIM_TransferManagement::ckUseClassCodes_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler
    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "UserID"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "UserID"
    
    Me.dcAIMUsers.Columns(1).Name = " UserName"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = " UserName"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
     f_HandleErr , , , "AIM_TransferManagement::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler
    Dim IndexCounter As Long
    
    If ColIndex = 0 Then
        'Toggle check and uncheck
        If SelectAllClassCodes = False Then
            For IndexCounter = LBound(AIMClasses) To UBound(AIMClasses)
                AIMClasses(IndexCounter).SelOpt = True
            Next
            SelectAllClassCodes = True
        Else
            For IndexCounter = LBound(AIMClasses) To UBound(AIMClasses)
                AIMClasses(IndexCounter).SelOpt = False
            Next
            SelectAllClassCodes = False
        End If
    End If
    dgAIMClasses.ReBind
    
    If dgAIMClasses.Columns(0).Selected = True Then dgAIMClasses.Columns(0).Selected = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_HeadClick)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMClasses_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_InitColumnProps()
On Error GoTo ErrorHandler
    Dim IndexCounter As Long
    
    Me.dgAIMClasses.Columns(0).Name = "SelOpt"
    Me.dgAIMClasses.Columns(0).Caption = getTranslationResource("X")
    Me.dgAIMClasses.Columns(0).Width = 400
    Me.dgAIMClasses.Columns(0).Locked = False
    Me.dgAIMClasses.Columns(0).Style = ssStyleCheckBox
    Me.dgAIMClasses.Columns(0).Alignment = ssCaptionAlignmentCenter
    
    Me.dgAIMClasses.Columns(1).Name = "Class"
    Me.dgAIMClasses.Columns(1).Caption = getTranslationResource("Class ID")
    Me.dgAIMClasses.Columns(1).DataField = "Class"
    Me.dgAIMClasses.Columns(1).Width = 1400
    Me.dgAIMClasses.Columns(1).Locked = True
    Me.dgAIMClasses.Columns(1).Alignment = ssCaptionAlignmentLeft
    
    Me.dgAIMClasses.Columns(2).Name = "Class Level"
    Me.dgAIMClasses.Columns(2).Caption = getTranslationResource("Class Level")
    Me.dgAIMClasses.Columns(2).DataField = "ClassLevel"
    Me.dgAIMClasses.Columns(2).Width = 1400
    Me.dgAIMClasses.Columns(2).Locked = True
    Me.dgAIMClasses.Columns(2).Alignment = ssCaptionAlignmentLeft
    
    Me.dgAIMClasses.Columns(3).Name = "Descripton"
    Me.dgAIMClasses.Columns(3).Caption = getTranslationResource("Description")
    Me.dgAIMClasses.Columns(3).DataField = "Description"
    Me.dgAIMClasses.Columns(3).Width = 5350
    Me.dgAIMClasses.Columns(3).Locked = True
    Me.dgAIMClasses.Columns(3).Alignment = ssCaptionAlignmentLeft
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMClasses, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMClasses.Columns.Count - 1
'        dgAIMClasses.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMClasses.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMClasses.Columns(IndexCounter).Locked = False Then dgAIMClasses.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_InitColumnProps)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMClasses_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundPositionData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMClasses_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim p As Integer
    Dim r As Integer
    Dim RowCount As Integer
    
    RowCount = UBound(AIMClasses)
    
    If IsNull(StartLocation) Then       'If the grid is empty then
        If ReadPriorRows Then           'If moving backwards through grid then
            p = RowCount                'pointer = # of last grid row
        Else                            'else
            p = 0                       'pointer = # of first grid row
        End If
        
    Else                                'If the grid already has data in it then
        p = StartLocation               'pointer = location just before or after the row where data will be added

        If ReadPriorRows Then           'If moving backwards through grid then
            p = p - 1                   'move pointer back one row
        Else                            'else
            p = p + 1                   'move pointer ahead one row
        End If

    End If

    'The pointer (p) now points to the row of the grid where you will start adding data.
    For i = 0 To RowBuf.RowCount - 1                    'For each row in the row buffer
        
        If p < 0 Or p > RowCount - 1 Then Exit For      'If the pointer is outside the grid then stop this
        'For each column in the grid
        'Set the value of each column in the row buffer
        'to the corresponding value in the arrray
        
        RowBuf.Value(i, 0) = AIMClasses(p).SelOpt
        RowBuf.Value(i, 1) = AIMClasses(p).Class
        RowBuf.Value(i, 2) = AIMClasses(p).ClassLevel
        RowBuf.Value(i, 3) = AIMClasses(p).Description
        
        RowBuf.Bookmark(i) = p                          'set the value of the bookmark for the current row in the rowbuffer

        If ReadPriorRows Then                           'move the pointer forward or backward, depending
            p = p - 1                                   'on which way it's supposed to move
        Else
            p = p + 1
        End If

        r = r + 1                                       'increment the number of rows read
    
    Next i

    RowBuf.RowCount = r                                 'set the size of the row buffer to the number of rows read

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundReadData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMClasses_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    AIMClasses(CInt(WriteLocation)).SelOpt = CStr(RowBuf.Value(0, 0))

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundWriteData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMClasses_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMLocations_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dgAIMLocations.Columns(0).Name = "SelOpt"
    Me.dgAIMLocations.Columns(0).Caption = getTranslationResource("X")
    Me.dgAIMLocations.Columns(0).Width = 400
    Me.dgAIMLocations.Columns(0).Locked = False
    Me.dgAIMLocations.Columns(0).Style = ssStyleCheckBox
    Me.dgAIMLocations.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgAIMLocations.Columns(0).Visible = True
    
    Me.dgAIMLocations.Columns(1).Name = "LcId"
    Me.dgAIMLocations.Columns(1).Caption = getTranslationResource("Location ID")
    Me.dgAIMLocations.Columns(1).Width = 1000
    Me.dgAIMLocations.Columns(1).Locked = True
    Me.dgAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    
    Me.dgAIMLocations.Columns(2).Name = "LcDesc"
    Me.dgAIMLocations.Columns(2).Caption = getTranslationResource("Description")
    Me.dgAIMLocations.Columns(2).Width = 2710
    Me.dgAIMLocations.Columns(2).Locked = True
    Me.dgAIMLocations.Columns(2).Alignment = ssCaptionAlignmentLeft

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMLocations, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMLocations.Columns.Count - 1
'        dgAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMLocations.Columns(IndexCounter).Locked = False Then dgAIMLocations.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMLocations_InitColumnProps)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMLocations_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler
    
    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMLocations_UnboundPositionData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMLocations_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMLocations_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim p As Integer
    Dim r As Integer
    Dim RowCount As Integer
    
    RowCount = UBound(AIMLocations)
    If RowCount <= 0 Then Exit Sub
    
    If IsNull(StartLocation) Then       'If the grid is empty then
        If ReadPriorRows Then           'If moving backwards through grid then
            p = RowCount                'pointer = # of last grid row
        Else                            'else
            p = 0                       'pointer = # of first grid row
        End If
        
    Else                                'If the grid already has data in it then
        p = StartLocation               'pointer = location just before or after the row where data will be added

        If ReadPriorRows Then           'If moving backwards through grid then
            p = p - 1                   'move pointer back one row
        Else                            'else
            p = p + 1                   'move pointer ahead one row
        End If

    End If

    'The pointer (p) now points to the row of the grid where you will start adding data.
    For i = 0 To RowBuf.RowCount - 1                    'For each row in the row buffer
        
        If p < 0 Or p > RowCount - 1 Then Exit For      'If the pointer is outside the grid then stop this
        'For each column in the grid
        'Set the value of each column in the row buffer
        'to the corresponding value in the arrray
        
        RowBuf.Value(i, 0) = AIMLocations(p).SelOpt
        RowBuf.Value(i, 1) = AIMLocations(p).LcId
        RowBuf.Value(i, 2) = AIMLocations(p).LcDesc
        
        RowBuf.Bookmark(i) = p                          'set the value of the bookmark for the current row in the rowbuffer

        If ReadPriorRows Then                           'move the pointer forward or backward, depending
            p = p - 1                                   'on which way it's supposed to move
        Else
            p = p + 1
        End If

        r = r + 1                                       'increment the number of rows read
    
    Next i

    RowBuf.RowCount = r                                 'set the size of the row buffer to the number of rows read

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMLocations_UnboundReadData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMLocations_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMLocations_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler
    
    AIMLocations(CInt(WriteLocation)).SelOpt = CStr(RowBuf.Value(0, 0))

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMLocations_UnboundWriteData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMLocations_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMOSLocations_AfterColUpdate(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler
    
    If optReportOption(3).Value = True Or optReportOption(4).Value = True Then
        If ckUseTransferPolicy.Value = Checked Then
            FillToLcids
        End If
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMOSLocations_AfterColUpdate)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMOSLocations_AfterColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMOSLocations_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dgAIMOSLocations.Columns(0).Name = "SelOpt"
    Me.dgAIMOSLocations.Columns(0).Caption = getTranslationResource("X")
    Me.dgAIMOSLocations.Columns(0).Width = 400
    Me.dgAIMOSLocations.Columns(0).Locked = False
    Me.dgAIMOSLocations.Columns(0).Style = ssStyleCheckBox
    Me.dgAIMOSLocations.Columns(0).Alignment = ssCaptionAlignmentCenter
    
    Me.dgAIMOSLocations.Columns(1).Name = "LcId"
    Me.dgAIMOSLocations.Columns(1).Caption = getTranslationResource("Location ID")
    Me.dgAIMOSLocations.Columns(1).Width = 1000
    Me.dgAIMOSLocations.Columns(1).Locked = True
    Me.dgAIMOSLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    
    Me.dgAIMOSLocations.Columns(2).Name = "LcDesc"
    Me.dgAIMOSLocations.Columns(2).Caption = getTranslationResource("Description")
    Me.dgAIMOSLocations.Columns(2).Width = 2835
    Me.dgAIMOSLocations.Columns(2).Locked = True
    Me.dgAIMOSLocations.Columns(2).Alignment = ssCaptionAlignmentLeft

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMOSLocations, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMOSLocations.Columns.Count - 1
'        dgAIMOSLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMOSLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMOSLocations.Columns(IndexCounter).Locked = False Then dgAIMOSLocations.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMOSLocations_InitColumnProps)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMOSLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMOSLocations_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler
    
    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMOSLocations_UnboundPositionData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMOSLocations_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMOSLocations_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
    Dim i As Integer
    Dim p As Integer
    Dim r As Integer
    Dim RowCount As Integer

    RowCount = UBound(AIMOSLocations)

    If IsNull(StartLocation) Then       'If the grid is empty then
        If ReadPriorRows Then           'If moving backwards through grid then
            p = RowCount                'pointer = # of last grid row
        Else                            'else
            p = 0                       'pointer = # of first grid row
        End If

    Else                                'If the grid already has data in it then
        p = StartLocation               'pointer = location just before or after the row where data will be added
        If ReadPriorRows Then           'If moving backwards through grid then
            p = p - 1                   'move pointer back one row
        Else                            'else
            p = p + 1                   'move pointer ahead one row
        End If

    End If

    'The pointer (p) now points to the row of the grid where you will start adding data.
    For i = 0 To RowBuf.RowCount - 1                    'For each row in the row buffer

        If p < 0 Or p > RowCount - 1 Then Exit For      'If the pointer is outside the grid then stop this

        'For each column in the grid
        'Set the value of each column in the row buffer
        'to the corresponding value in the arrray

        RowBuf.Value(i, 0) = AIMOSLocations(p).SelOpt
        RowBuf.Value(i, 1) = AIMOSLocations(p).LcId
        RowBuf.Value(i, 2) = AIMOSLocations(p).LcDesc

        RowBuf.Bookmark(i) = p                          'set the value of the bookmark for the current row in the rowbuffer

        If ReadPriorRows Then                           'move the pointer forward or backward, depending
            p = p - 1                                   'on which way it's supposed to move
        Else
            p = p + 1
        End If

        r = r + 1                                       'increment the number of rows read

    Next i

    RowBuf.RowCount = r                                 'set the size of the row buffer to the number of rows read

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMOSLocations_UnboundReadData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMOSLocations_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMOSLocations_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler
    
    AIMOSLocations(CInt(WriteLocation)).SelOpt = CStr(RowBuf.Value(0, 0))

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMOSLocations_UnboundWriteData)"
     f_HandleErr , , , "AIM_TransferManagement::dgAIMOSLocations_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Activate()
On Error GoTo ErrorHandler
    
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_TransferManagement::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim rsAIMUsers As ADODB.Recordset
    
    Set rsOverstocks = New ADODB.Recordset
    With rsOverstocks
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    Set rsUpdateItem = New ADODB.Recordset
    With rsUpdateItem
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    'Open the Record Set
    Set rsGetLcidFromAIMTransfer = New ADODB.Recordset
    With rsGetLcidFromAIMTransfer
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03801")
    If StrComp(strMessage, "STATMSG03801") = 0 Then strMessage = "Initializing Transfer Management..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
        
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialzie the AIM Overstocks Stored Procedure
    Set AIM_Transfer_SP = New ADODB.Command
    With AIM_Transfer_SP
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Transfer_SP"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    Set AIM_TransferToReport_SP = New ADODB.Command
    With AIM_TransferToReport_SP
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_TransferToReport_SP"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    Set AIM_TransferManagement_SP = New ADODB.Command
    With AIM_TransferManagement_SP
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_TransferManagement_SP"
        .CommandTimeout = 0
        .Parameters.Refresh
    End With
    
     'Define Stored Procedures
    Set AIM_PoInsert_Sp = New ADODB.Command
    With AIM_PoInsert_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_PoInsert_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    Set AIM_PoHdrInsert_Sp = New ADODB.Command
    With AIM_PoHdrInsert_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_PoHdrInsert_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    Set AIM_ValidateTransfer_Sp = New ADODB.Command
    With AIM_ValidateTransfer_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_ValidateTransfer_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    
    'Build/Bind the AIM User Drop Down
    Me.dcAIMUsers.Text = AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text
    
    strSQL = "SELECT UserID, UserName FROM AIMUsers " & _
        " ORDER BY UserID "
        
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dcAIMUsers.AddItem getTranslationResource("All") _
                & vbTab & getTranslationResource("All Users")
    
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        rsAIMUsers.MoveFirst
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserID").Value & vbTab & rsAIMUsers("UserName").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    'Build/Bind the AIM Locations Drop Down
    PopulateAIMLocations
    FetchAIMClasses
    
    'Me.dcAIMLocations.text = "All"
    'Check the state of the Class Code Filter checkbox
    ckUseClassCodes.Value = vbUnchecked
    ckOUTL.Value = vbChecked
    ckOrderPt.Value = vbChecked
    ckUseClassCodes_Click
    'Make the spin button visible
    Me.txtCostFloor.Spin.Visible = 1
    Me.txtNbrOfWeeks.Spin.Visible = 1
    Me.txtShortageWeeks.Spin.Visible = 1
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    'Clean Up
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing

    'Initialize the Column Width for the grid
    InitColWidths
    
    'Initially disable some menu options when we open which are valid only for TransferManagemt Tab only
    ToggleToolbar False
    optReportOption(0).Value = True
    
Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing

    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_TransferManagement::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error Resume Next
    
    Dim RtnCode As Integer
    
    'Save current grid layout.
    'Me.dgTransferMgmt.SaveLayout App.Path & "\" & Trim(gUserID) & "_TransferLayout.grd", ssSaveLayoutAll
    
    If Not (AIM_Transfer_SP Is Nothing) Then Set AIM_Transfer_SP.ActiveConnection = Nothing
    Set AIM_Transfer_SP = Nothing
    
    If Not (AIM_TransferToReport_SP Is Nothing) Then Set AIM_TransferToReport_SP.ActiveConnection = Nothing
    Set AIM_TransferToReport_SP = Nothing
    
    If Not (AIM_TransferManagement_SP Is Nothing) Then Set AIM_TransferManagement_SP.ActiveConnection = Nothing
    Set AIM_TransferManagement_SP = Nothing
    
    If Not (AIM_PoInsert_Sp Is Nothing) Then Set AIM_PoInsert_Sp.ActiveConnection = Nothing
    Set AIM_PoInsert_Sp = Nothing
    
    If Not (AIM_PoHdrInsert_Sp Is Nothing) Then Set AIM_PoHdrInsert_Sp.ActiveConnection = Nothing
    Set AIM_PoHdrInsert_Sp = Nothing
    
    If Not (AIM_ValidateTransfer_Sp Is Nothing) Then Set AIM_ValidateTransfer_Sp.ActiveConnection = Nothing
    Set AIM_ValidateTransfer_Sp = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_TransferManagement::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function FetchAIMClasses()
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim Index_ClassLevel As Integer
    Dim rsAIMClasses As ADODB.Recordset
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
    
    If Me.ckClass(1) = True Then
        Index_ClassLevel = 1
    ElseIf Me.ckClass(2) = True Then
        Index_ClassLevel = 2
    ElseIf Me.ckClass(3) = True Then
        Index_ClassLevel = 3
    ElseIf Me.ckClass(4) = True Then
        Index_ClassLevel = 4
    End If

    strSQL = "SELECT Class, ClassLevel, ClassDesc" & _
        " FROM AIMClasses" & _
        " WHERE AIMClasses.ClassLevel = " & Index_ClassLevel & _
        " AND AIMClasses.LangID = N'" & gLangID & "'" & _
        " ORDER BY ClassLevel, Class"
    
    Set rsAIMClasses = New ADODB.Recordset
    With rsAIMClasses
        Set .ActiveConnection = Cn
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        
        .Open strSQL
    End With
    
    'Load the Class Array
    i = 0
    ReDim AIMClasses(100)
    
    If f_IsRecordsetOpenAndPopulated(rsAIMClasses) Then
        rsAIMClasses.MoveFirst
        Do Until rsAIMClasses.eof
            AIMClasses(i).SelOpt = False
            AIMClasses(i).Class = rsAIMClasses!Class
            AIMClasses(i).ClassLevel = rsAIMClasses!ClassLevel
            AIMClasses(i).Description = rsAIMClasses!ClassDesc
            
            i = i + 1
            
            If i > UBound(AIMClasses()) Then
                ReDim Preserve AIMClasses(UBound(AIMClasses) + 10)
            End If
            
            rsAIMClasses.MoveNext
        Loop
        
        'Truncate the array
        ReDim Preserve AIMClasses(i - 1)
        
    End If

    'Rebind the grid
    Me.dgAIMClasses.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAIMClasses) Then
        dgAIMClasses.Rows = rsAIMClasses.RecordCount
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMClasses) Then rsAIMClasses.Close
    Set rsAIMClasses = Nothing

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    'Close the Record Set
    If f_IsRecordsetValidAndOpen(rsAIMClasses) Then rsAIMClasses.Close
    Set rsAIMClasses = Nothing
    
    'Err.Raise ErrNumber, ErrSource & "(FetchAIMClasses)", ErrDescription
     f_HandleErr , , , "AIM_TransferManagement::FetchAIMClasses", Now, gDRGeneralError, True, Err
End Function

Private Function CreatePoHeader(ArgVnId As String, ArgAssort As String, ArgLineCount As Integer, ArgTotalCost As Double) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As String
    Dim irow As Integer

    With AIM_PoHdrInsert_Sp
        .Parameters("@ById").Value = Me.dcAIMUsers.Text
        .Parameters("@VnId").Value = ArgVnId
        .Parameters("@Assort").Value = ArgAssort
        .Parameters("@POStatus").Value = "R"
        .Parameters("@TransmitPO").Value = 0
        .Parameters("@ShipIns").Value = ""
        .Parameters("@UserInitials").Value = Left(Me.dcAIMUsers.Text, 3)
        .Parameters("@Remarks1").Value = ""
        .Parameters("@Remarks2").Value = ""
        .Parameters("@Remarks3").Value = ""
        .Parameters("@AddrOverride").Value = "N"
        .Parameters("@VName").Value = ""
        .Parameters("@VAddress1").Value = ""
        .Parameters("@VAddress2").Value = ""
        .Parameters("@VCity").Value = ""
        .Parameters("@VState").Value = ""
        .Parameters("@VZip").Value = ""
        .Parameters("@LineCount").Value = ArgLineCount
        .Parameters("@PosLineCount").Value = ArgLineCount
        .Parameters("@Vn_Min").Value = 0
        .Parameters("@Vn_Best").Value = 0
        .Parameters("@Reach_Code").Value = ""
        .Parameters("@POSource").Value = "T"
        .Parameters("@POByZone").Value = ""
        .Parameters("@Dft_LeadTime").Value = 0
        .Parameters("@TotalCost").Value = ArgTotalCost
        ' Execute the command
        .Execute , , adExecuteNoRecords

        'Retrieve stored procedure return value and output parameters
        RtnCode = .Parameters(0).Value
    End With

'    If RtnCode <> 0 Then
'        strMessage = getTranslationResource("ERRMSG07500")
'        If StrComp(strMessage, "ERRMSG07500") = 0 Then strMessage = "Error(s) while copying Item History"
'        MsgBox strMessage & vbCrLf & vbCrLf & _
'            Cn.Errors(0).Description, vbCritical + vbOKOnly, Heading
'    Else
'        strMessage = getTranslationResource("STATMSG07501")
'        If StrComp(strMessage, "STATMSG07501") = 0 Then strMessage = "Item History copied successfully."
'        Write_Message strMessage
'    End If
                
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(CreatePo)"
     f_HandleErr , , , "AIM_TransferManagement::CreatePoHeader", Now, gDRGeneralError, True, Err
End Function

Private Function CreatePoDetail() As Integer
On Error GoTo ErrorHandler
    
    Dim RtnCode As String
    Dim irow As Integer
    Dim iLineCount As Integer
    Dim iTotalCost As Double
    Dim FirstVnId As String
    Dim Assort As String
    Dim OldAssort As String
    Dim strSQL As String
    Dim FromOH As Long
    Dim Sugtransqty  As Long
    Dim Item As String
    Dim FromLcID As String
    Dim ToLcID As String
    Dim ToOH As Long
    Dim FirstTime As Boolean
    Dim ComStk As Long
    Dim Oo As Long
    Dim Counter As Integer
    
    ReDim Preserve VendorAssort(100)
    Counter = 1
    'Sort by from lcid
    OverStockDBArray.QuickSort 0, OverStockDBArray.UpperBound(1), 0, XORDER_DESCEND, 9, 3, XORDER_DESCEND, 9
    For irow = OverStockDBArray.LowerBound(2) To OverStockDBArray.UpperBound(1)
        If OverStockDBArray(irow, 0) = -1 Then 'Then it need to generate transfer
            If Not FirstTime Then   'Enter this case if it is first time
                FirstTime = True
                FirstVnId = OverStockDBArray(irow, 3) 'FLcid is used as vendorid
                Assort = GetNextAssortment(OverStockDBArray(irow, 3), dcAIMUsers.Text)
                OldAssort = Assort
                
            Else
               If OverStockDBArray(irow, 3) <> FirstVnId Then
                OldAssort = Assort
                End If
            End If
            
            With AIM_PoInsert_Sp
                .Parameters("@POLineType").Value = 90
                .Parameters("@OrdStatus").Value = "R"
                .Parameters("@RevCycle").Value = ""
                .Parameters("@ById").Value = Me.dcAIMUsers.Text
                .Parameters("@VnId").Value = OverStockDBArray(irow, 3)
                .Parameters("@Assort").Value = Assort
                .Parameters("@Lcid").Value = OverStockDBArray(irow, 7) 'tolcid
                .Parameters("@Item").Value = OverStockDBArray(irow, 1)  'Item
                .Parameters("@ItDesc").Value = OverStockDBArray(irow, 2) 'desc
                .Parameters("@POType").Value = "T"
                .Parameters("@AvailQty").Value = OverStockDBArray(irow, 4) 'fromOH
                .Parameters("@PackRounding").Value = ""
                .Parameters("@RSOQ").Value = OverStockDBArray(irow, 6) 'SuggTransferQty
                .Parameters("@SOQ").Value = OverStockDBArray(irow, 6)   'SuggTransferQty
                .Parameters("@VSOQ").Value = OverStockDBArray(irow, 6)  'SuggTransferQty
                .Parameters("@UOM").Value = OverStockDBArray(irow, 26) 'UOM
                .Parameters("@BuyingUOM").Value = ""
                .Parameters("@ConvFactor").Value = 0
                .Parameters("@IsDate").Value = Format(Date, g_ISO_DATE_FORMAT)
                
                .Parameters("@DuDate").Value = Format(Date, g_ISO_DATE_FORMAT)
                .Parameters("@LastWeekSalesFlag").Value = ""
                .Parameters("@Cost").Value = OverStockDBArray(irow, 22) 'Cost
                .Parameters("@Zone").Value = OverStockDBArray(irow, 23) 'Zone
                ' Execute the command
                .Execute , , adExecuteNoRecords
        
                'Retrieve stored procedure return value and output parameters
                RtnCode = .Parameters(0).Value
               If irow <> OverStockDBArray.LowerBound(1) Then
                    If OverStockDBArray(irow, 3) <> OverStockDBArray(irow - 1, 3) Then  'FromLcid
                        CreatePoHeader OverStockDBArray(irow - 1, 3), OldAssort, iLineCount, iTotalCost
                        iLineCount = 0
                        iTotalCost = 0
                    End If
                End If
                'Store all the differnt vnid and assort so that we can use them when we generate pofile
                VendorAssort(Counter).Assort = Assort
                VendorAssort(Counter).VnId = OverStockDBArray(irow, 3)
                Counter = Counter + 1
                If Counter > UBound(VendorAssort()) Then
                    ReDim Preserve VendorAssort(UBound(VendorAssort()) + 100)
                End If
                iLineCount = iLineCount + 1
                iTotalCost = iTotalCost + OverStockDBArray(irow, 22) * OverStockDBArray(irow, 6) 'Cost*SuggTransferQty
            End With
        
            FromOH = OverStockDBArray(irow, 4) 'FromOH
            Sugtransqty = OverStockDBArray(irow, 6) 'SugTranfrerQty
            Item = OverStockDBArray(irow, 1)    'Item
            FromLcID = OverStockDBArray(irow, 3)    'FromLcid
            ComStk = OverStockDBArray(irow, 24)
            Oo = OverStockDBArray(irow, 25)
            strSQL = ""
            strSQL = "UPDATE Item" & _
                    "  set ComStk = " & CStr(Sugtransqty + ComStk) & _
                    " WHERE Lcid = N'" & FromLcID & "'" & _
                    " AND Item = N'" & Item & "'"
            rsUpdateItem.Open strSQL, Cn
            
            ToOH = OverStockDBArray(irow, 8) 'ToOH
            ToLcID = OverStockDBArray(irow, 7)    'ToLcid
            strSQL = ""
            strSQL = "UPDATE Item" & _
                    " SET OO = " & CStr(Sugtransqty + Oo) & _
                    " WHERE Lcid = N'" & ToLcID & "'" & _
                    " AND Item = N'" & Item & "'"
            rsUpdateItem.Open strSQL, Cn
        End If
    Next
    
    'CreatePoHeader for the last transaction
    If iLineCount <> 0 Then 'Then there is a transction pending
        CreatePoHeader OverStockDBArray(irow - 1, 3), Assort, iLineCount, iTotalCost
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(CreatePoDetail)"
     f_HandleErr , , , "AIM_TransferManagement::CreatePoDetail", Now, gDRGeneralError, True, Err
End Function

Private Function ExtendedCostAndWeight()
On Error GoTo ErrorHandler
    Dim Lrow As Integer
    Dim Lcol As Integer
    Dim ExtWeight As Double
    Dim ExtCube As Double
    
    ExtWeight = 0
    ExtCube = 0
    
    For Lrow = 0 To OverStockDBArray.UpperBound(1)
        If OverStockDBArray(Lrow, 0) <> vbUnchecked Then    'Record is selected. Increment totals with its values.
            ExtWeight = ExtWeight + OverStockDBArray(Lrow, 19) * OverStockDBArray(Lrow, 6) 'Weight*SugTransferQty
            ExtCube = ExtCube + OverStockDBArray(Lrow, 20) * OverStockDBArray(Lrow, 6) 'Cube*SugTransferQty
        End If
    Next
    
    txtExtWeight.Text = ExtWeight
    txtExtCube.Text = ExtCube

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ExtendedCostAndWeight)"
     f_HandleErr , , , "AIM_TransferManagement::ExtendedCostAndWeight", Now, gDRGeneralError, True, Err
End Function

Private Function GetNextAssortment(ArgVnId As String, ArgById As String) As String
On Error GoTo ErrorHandler
    Dim rsAssort As ADODB.Recordset
    Dim strSQL As String
    Dim NextAssort As String
    
    'Get the max assortment from AIMPo table and add 1 to it the assort is in the form of 001 ,002 etc
    strSQL = "SELECT Max(Assort)" & _
            " FROM AIMPo" & _
            " WHERE VnId = N'" & ArgVnId & "'" & _
            " AND Byid = N'" & ArgById & "'"
    
    Set rsAssort = New ADODB.Recordset
    rsAssort.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    If f_IsRecordsetOpenAndPopulated(rsAssort) Then
        If Not IsNull(rsAssort(0).Value) And rsAssort(0).Value <> "" Then
            NextAssort = rsAssort(0).Value
            NextAssort = Format(CInt(NextAssort) + 1, "00#")
        Else
            'The first assort is 001
            NextAssort = "001"
        End If
    End If
    
    GetNextAssortment = NextAssort
    
    If f_IsRecordsetValidAndOpen(rsAssort) Then rsAssort.Close
    Set rsAssort = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAssort) Then rsAssort.Close
    Set rsAssort = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetNextAssortment)"
    f_HandleErr , , , "AIM_TransferManagement::GetNextAssortment", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleShortageQtyAccess(ArgEnable As Boolean)
On Error GoTo ErrorHandler
    
    'Enable/Disable the To Target area based on the option selected
    label(9).Enabled = ArgEnable
    label(10).Enabled = ArgEnable
    label(12).Enabled = ArgEnable
    txtShortageWeeks.Enabled = ArgEnable
    ckOrderPt.Enabled = ArgEnable
    Frame6.Enabled = ArgEnable
    
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ToggleShortageQtyAccess)"
     f_HandleErr , , , "AIM_TransferManagement::ToggleShortageQtyAccess", Now, gDRGeneralError, True, Err
End Function

Private Function ValidateTransfer() As Integer
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    
    'Check to see if double dipping is a possibilit
    With AIM_ValidateTransfer_Sp
        ' Execute the command
        .Execute , , adExecuteNoRecords
        'Retrieve stored procedure return value and output parameters
        RtnCode = .Parameters(0).Value
    End With
    ValidateTransfer = RtnCode
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ValidateTransfer)"
     f_HandleErr , , , "AIM_TransferManagement::ValidateTransfer", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleToTargetAccess(ArgEnable As Boolean)
On Error GoTo ErrorHandler
    
    'Enable/Disable the To Target area based on the option selected
'     Frame5.Enabled = ArgEnable
'    dgAIMLocations.Enabled = ArgEnable
     If ArgEnable = False Then
        dgAIMLocations.Columns(0).Locked = True
        dgAIMLocations.Columns(1).Locked = True
        dgAIMLocations.Columns(2).Locked = True
        dgAIMLocations.Columns(0).ForeColor = RGB(192, 192, 192)
        dgAIMLocations.Columns(1).ForeColor = RGB(192, 192, 192)
        dgAIMLocations.Columns(2).ForeColor = RGB(192, 192, 192)

     Else
         dgAIMLocations.Columns(0).Locked = False
        dgAIMLocations.Columns(1).Locked = False
        dgAIMLocations.Columns(2).Locked = False
        dgAIMLocations.Columns(0).ForeColor = 0
        dgAIMLocations.Columns(1).ForeColor = 0
        dgAIMLocations.Columns(2).ForeColor = 0
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ToggleToTargetAccess)"
     f_HandleErr , , , "AIM_TransferManagement::ToggleToTargetAccess", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleOverstockQtyAccess(ArgEnable As Boolean)
On Error GoTo ErrorHandler

    'Enable/Disable the From Target area based on the option selected
    Frame1.Enabled = ArgEnable
    label(3).Enabled = ArgEnable
    label(4).Enabled = ArgEnable
    label(5).Enabled = ArgEnable
    label(8).Enabled = ArgEnable
    label(11).Enabled = ArgEnable
    ckOUTL.Enabled = ArgEnable
    txtNbrOfWeeks.Enabled = ArgEnable
    txtCostFloor.Enabled = ArgEnable
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ToggleOverstockQtyAccess)"
     f_HandleErr , , , "AIM_TransferManagement::ToggleOverstockQtyAccess", Now, gDRGeneralError, True, Err
End Function

Private Function BuildFromLcids() As Integer
On Error GoTo ErrorHandler
    
    Dim DemandOption As Integer
    Dim X As Integer
    Dim i As Integer
    Dim SQL As String
    Dim ListCount As Integer
    
    'Used to create a string of Form Lcids to be send as argument to Stored Procedure
    For i = LBound(AIMOSLocations) To UBound(AIMOSLocations)
        If AIMOSLocations(i).SelOpt = True Then
            SQL = SQL & IIf(ListCount > 0, ", ", "(")
            SQL = SQL & "N'" & AIMOSLocations(i).LcId & "'"
            ListCount = ListCount + 1
        End If
    Next i
     
    'If no location was specified -- select nothing
    If ListCount > 0 Then
        SQL = SQL & ") "
    Else
        SQL = SQL & "('XXXXXXXXXXXX') "
    End If
    
    BldFromLcids = SQL

Exit Function
ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsOverstocks) Then rsOverstocks.Close
    'Err.Raise Err.Number, Err.source, Err.Description & "(BuidFromLcids)"
     f_HandleErr , , , "AIM_TransferManagement::BuidFromLcids", Now, gDRGeneralError, True, Err
End Function

Private Function BuildToLcids() As String
On Error GoTo ErrorHandler
    
    Dim DemandOption As Integer
    Dim X As Integer
    Dim i As Integer
    Dim SQL As String
    Dim ListCount As Integer
    
    'Used to create a string of Form Lcids to be send as argument to Stored Procedure
    For i = LBound(AIMLocations) To UBound(AIMLocations)
        If AIMLocations(i).SelOpt = True Then
            SQL = SQL & IIf(ListCount > 0, ", ", "(")
            SQL = SQL & "N'" & AIMLocations(i).LcId & "'"
            ListCount = ListCount + 1
        End If
    Next i
     
    'If no location was specified -- select nothing
    If ListCount > 0 Then
        SQL = SQL & ") "
    Else
        SQL = SQL & "('XXXXXXXXXXXX') "
    End If
    
    BuildToLcids = SQL

Exit Function
ErrorHandler:
'    If f_IsRecordsetValidAndOpen(rsOverstocks) Then rsOverstocks.Close
    'Err.Raise Err.Number, Err.source, Err.Description & "(BuildToLcids)"
    f_HandleErr , , , "AIM_TransferManagement::BuildToLcids", Now, gDRGeneralError, True, Err
End Function

Private Function PopulateBestQty() As Integer
On Error GoTo ErrorHandler
    
    Dim MaxQty As Double
    Dim irow As Integer
    Dim MinQty As Double
        
    For irow = OverStockDBArray.LowerBound(2) To OverStockDBArray.UpperBound(1)
        MaxQty = 0
        MinQty = 0
        If Me.ckOUTL = vbChecked Then
            MaxQty = OverStockDBArray(irow, 21) + OverStockDBArray(irow, 16) 'OOrederPt+OOrderQty
        End If
        If Me.txtNbrOfWeeks.Value > 0 Then
            MaxQty = MaxQty + Round(OverStockDBArray(irow, 11) * Me.txtNbrOfWeeks.Value, 0) 'FromFcstDemd
        End If
        
        'OverStockDBArray(irow, 5) = MaxQty 'FromBest
        OverStockDBArray(irow, 5) = OverStockDBArray(irow, 4) - MaxQty
        If Me.ckOrderPt = vbChecked Then
            MinQty = OverStockDBArray(irow, 17) 'ShortOrderPt
        End If
        If Me.txtShortageWeeks.Value > 0 Then
            MinQty = MinQty + Round(OverStockDBArray(irow, 10) * Me.txtShortageWeeks.Value, 0) 'ToFcstdemand
        End If
        'OverStockDBArray(irow, 9) = MinQty  'ToBest
        OverStockDBArray(irow, 9) = Abs(MinQty - OverStockDBArray(irow, 8))
    Next irow

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateBestQty)"
     f_HandleErr , , , "AIM_TransferManagement::PopulateBestQty", Now, gDRGeneralError, True, Err
End Function

Private Function PopulateTransferGrid() As Integer
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim J As Integer
    
    OverStockDBArray.Clear
    rsOverstocks.Requery
    
    OverStockDBArray.LoadRows rsOverstocks.GetRows, True
    'Populate the BestQty field in OverStockDBArray
    PopulateBestQty
    'Populate the SuggTransferQty field in OverStockDBArray
    PopulateInitailSugTransferQty
    'Recalculate the ExtendedCostAndWeight
    ExtendedCostAndWeight
    
    OverStockDBArray.QuickSort 0, OverStockDBArray.UpperBound(1), 6, XORDER_ASCEND, 7
    
    If Me.mnuEditOpt(2).Checked = True Then
        For i = 0 To OverStockDBArray.UpperBound(1)
            J = OverStockDBArray.Find(0, 6, 0#, , XCOMP_EQ)
            If J <> -1 Then
                OverStockDBArray.DeleteRows (J)
            Else
                Exit For
            End If
        Next
    End If
    Me.dgTransferMgmt.ReBind
    dgTransferMgmt.Rows = OverStockDBArray.UpperBound(1) + 1
                
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateTransferGrid)"
     f_HandleErr , , , "AIM_TransferManagement::PopulateTransferGrid", Now, gDRGeneralError, True, Err
End Function

Private Function PopulateInitailSugTransferQty() As Integer
'Populate the SuggestedTransferQty field in OverStockDBArray  with initial values
On Error GoTo ErrorHandler
    
    Dim irow As Integer
        
    For irow = OverStockDBArray.LowerBound(2) To OverStockDBArray.UpperBound(1)
'        set the transfer qty to 0 if the transfer cost is less then the specifed value
'        so that they will be filtered out
        OverStockDBArray(irow, 6) = Min(OverStockDBArray(irow, 5), OverStockDBArray(irow, 9))
        If OverStockDBArray(irow, 6) * OverStockDBArray(irow, 19) < txtCostFloor.Value Then
            OverStockDBArray(irow, 6) = 0#
        End If
        'Populate cost and cube fields using this sugtransferqty
        OverStockDBArray(irow, 12) = OverStockDBArray(irow, 6) * OverStockDBArray(irow, 19)
        OverStockDBArray(irow, 13) = OverStockDBArray(irow, 6) * OverStockDBArray(irow, 20)
     Next irow

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateInitailSugTransferQty)"
     f_HandleErr , , , "AIM_TransferManagement::PopulateInitailSugTransferQty", Now, gDRGeneralError, True, Err
End Function

Private Function PopulateAIMLocations() As Integer
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim strSQL As String
    Dim rsAIMLocations As ADODB.Recordset
    Dim ErrNumber As Long, ErrSource As String, ErrDescription As String
        
    'Build/Bind the AIM Locations Drop Down
    strSQL = "SELECT LcID, LName" & _
            " FROM AIMLocations"
    If Me.CkDFS = vbChecked Then strSQL = strSQL & " WHERE DropShip_XDock <> N'Y'"
    strSQL = strSQL & " ORDER BY lcid "
    
    Set rsAIMLocations = New ADODB.Recordset
    rsAIMLocations.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    'Me.dcAIMLocations.AddItem "All" & vbTab & "All Locations"
    i = 0
    ReDim AIMLocations(0)
    ReDim AIMOSLocations(0)
    ReDim AIMLocations(100)
    ReDim AIMOSLocations(100)
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        rsAIMLocations.MoveFirst
        Do Until rsAIMLocations.eof
            AIMLocations(i).SelOpt = False
            AIMLocations(i).LcId = rsAIMLocations("lcid").Value
            AIMLocations(i).LcDesc = rsAIMLocations("lname").Value
            
            AIMOSLocations(i).SelOpt = False
            AIMOSLocations(i).LcId = rsAIMLocations("lcid").Value
            AIMOSLocations(i).LcDesc = rsAIMLocations("lname").Value
            i = i + 1
            
            If i > UBound(AIMLocations()) Then
                ReDim Preserve AIMLocations(UBound(AIMLocations) + 10)
                ReDim Preserve AIMOSLocations(UBound(AIMOSLocations) + 10)
            End If
            
            rsAIMLocations.MoveNext
        Loop
        
        'Truncate the array
        ReDim Preserve AIMLocations(i)
        ReDim Preserve AIMOSLocations(i)
    End If
    
    dgAIMOSLocations.ReBind
    dgAIMLocations.ReBind
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    'Clean Up
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
    'Initialize the Column Width for the grid
    InitColWidths
    
    'Initially disable some menu options when we open which are valid only for TransferManagemt Tab only
    ToggleToolbar False
    'optReportOption(0).Value = True
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    
    'Close the Record Set
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
    'Err.Raise ErrNumber, ErrSource & "(PopulateAIMLocations)", ErrDescription
     f_HandleErr , , , "AIM_TransferManagement::PopulateAIMLocations", Now, gDRGeneralError, True, Err
End Function

Private Function FillToLcids()
On Error GoTo ErrorHandler

    Dim DemandOption As Integer
    Dim X As Integer
    Dim i As Integer
    Dim SQL As String
    Dim ListCount As Integer
    
    'Used to automatically populate the To Target lcoations based on From Target locations
    Me.dgAIMOSLocations.Update
    For i = LBound(AIMOSLocations) To UBound(AIMOSLocations)
        If AIMOSLocations(i).SelOpt = True Then
            SQL = SQL & IIf(ListCount > 0, ", ", "(")
            SQL = SQL & "N'" & AIMOSLocations(i).LcId & "'"
            ListCount = ListCount + 1
        End If
    Next i
    'If no location was specified -- select nothing
    If ListCount > 0 Then
        SQL = SQL & ") "
    Else
        SQL = SQL & "('XXXXXXXXXXXX') "
    End If
    
    'Reset the selection option
    For i = LBound(AIMLocations) To UBound(AIMLocations)
        AIMLocations(i).SelOpt = False
    Next i
    
    'Concatenate SQL query
    SQL = "SELECT DISTINCT(LcIDTransferTo)" & _
        " FROM AimTransferPolicy" & _
        " WHERE LcId IN " & SQL & _
        " AND EnableTransferPolicy = N'1'"
    SQL = SQL & " AND TransferType = N'T'"
    
    'Fetch into recordset
    If f_IsRecordsetValidAndOpen(rsGetLcidFromAIMTransfer) Then rsGetLcidFromAIMTransfer.Close
    rsGetLcidFromAIMTransfer.Open SQL, Cn
    'Check for validity and process
    If f_IsRecordsetOpenAndPopulated(rsGetLcidFromAIMTransfer) Then
        rsGetLcidFromAIMTransfer.MoveFirst
        Do Until rsGetLcidFromAIMTransfer.eof
            For i = LBound(AIMLocations) To UBound(AIMLocations)
                If UCase(AIMLocations(i).LcId) = UCase(rsGetLcidFromAIMTransfer!LcIDTransferTo) Then
                    AIMLocations(i).SelOpt = True
                End If
            Next i
        rsGetLcidFromAIMTransfer.MoveNext
        Loop
    End If
    
    Me.dgAIMLocations.ReBind
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsGetLcidFromAIMTransfer) Then rsGetLcidFromAIMTransfer.Close
    'Err.Raise Err.Number, Err.source, Err.Description & "(FillToLcids)"
    f_HandleErr , , , "AIM_TransferManagement::FillToLcids", Now, gDRGeneralError, True, Err
End Function

Private Function ClearToLcids()
On Error GoTo ErrorHandler
    
    Dim i As Integer
    
    For i = LBound(AIMLocations) To UBound(AIMLocations)
        AIMLocations(i).SelOpt = False
    Next i
    Me.dgAIMLocations.ReBind

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(ClearToLcids)"
     f_HandleErr , , , "AIM_TransferManagement::ClearToLcids", Now, gDRGeneralError, True, Err
End Function

Private Function SaveTransferRecord()
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Integer
    Dim Counter As Integer
    
    dgTransferMgmt.Update
    CreatePoDetail
    strMessage = getTranslationResource("MSGBOX03802")
    If StrComp(strMessage, "MSGBOX03802") = 0 Then strMessage = "Create Outbound file now? (No will hold item(s) until next scheduled job.)"
    RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
    If RtnCode = vbYes Then
        'call function to cretate the purchase order
        For Counter = 1 To UBound(VendorAssort())
            'If VendorAssort(Counter).Assort <> "" Or Not IsNull(VendorAssort(Counter).Assort) Then
            If VendorAssort(Counter).Assort <> "" Then
                RtnCode = DxPO_Ctrl("V", Me.dcAIMUsers.Text, VendorAssort(Counter).VnId, VendorAssort(Counter).Assort)
            Else
                Exit For
            End If
        Next
    Else
        'Do not create file
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "( SaveTransferRecord)"
    f_HandleErr , , , "AIM_TransferManagement::SaveTransferRecord", Now, gDRGeneralError, True, Err
End Function

Private Function GenerateTransfers() As Integer
'Called when we are going to TransferManagement Tab to fill the grid
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim DemandOption As Integer
    Dim X As Integer
    Dim SQL_Overstocks As String
    Dim SQL_Shortages As String
    Dim strText As String
    Dim strMessage As String
    Dim Drop_Ship_YN As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03800")
    If StrComp(strMessage, "STATMSG03800") = 0 Then strMessage = "Building the Transfer Management Report..."
    Write_Message strMessage
    
    'Force Grids Update
    Me.dgAIMClasses.Update
    Me.dgAIMLocations.Update
    Me.dgAIMOSLocations.Update
    
    For X = Me.optReportOption.LBound To Me.optReportOption.UBound
        If Me.optReportOption(X) = True Then
            DemandOption = optReportOption(X).Tag
            Exit For
        End If
    Next X
    
    'Valid only for OverStock with Shortages and Shortates with OverStock options only
    'If it is Shortages over OverStock change it to OverStock with Shortages as sql is taken care be RecId
    'Build SQL Statements
    SQL_Overstocks = BldOverstockStmt
    SQL_Shortages = BldShortageStmt
    
    'Populate the BldFromLcids string
    BuildFromLcids
    
    If f_IsRecordsetValidAndOpen(rsOverstocks) Then rsOverstocks.Close
    
    AIM_TransferManagement_SP.Parameters("@SQL_Overstocks").Value = SQL_Overstocks
    AIM_TransferManagement_SP.Parameters("@SQL_Shortages").Value = SQL_Shortages
    'Only demand option of 4 is valid
    AIM_TransferManagement_SP.Parameters("@RptOpt").Value = DemandOption
    AIM_TransferManagement_SP.Parameters("@From_LcIds").Value = BldFromLcids
    If Me.CkDFS = vbChecked Then
        Drop_Ship_YN = "Y"
    Else
        Drop_Ship_YN = "N"
    End If
    AIM_TransferManagement_SP.Parameters("@Drop_Ship_YN").Value = Drop_Ship_YN
    
    If ckUseTransferPolicy.Value = False Then
        AIM_TransferManagement_SP.Parameters("@To_Lcids").Value = BuildToLcids
    Else
        AIM_TransferManagement_SP.Parameters("@To_Lcids").Value = "NOTHING"
    End If
    rsOverstocks.Open AIM_TransferManagement_SP
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    If Not f_IsRecordsetOpenAndPopulated(rsOverstocks) Then
        strText = getTranslationResource(AIM_TransferManagement.Caption)
        strMessage = getTranslationResource("MSGBOX03800")
        If StrComp(strMessage, "MSGBOX03800") = 0 Then strMessage = "No overrages found that meet the specified criteria."
        strMessage = strMessage & vbCrLf & vbCrLf
        strMessage = strMessage ' & SQL_Overstocks
        MsgBox strMessage, vbCritical + vbOKOnly, strText
        GenerateTransfers = -1
        Exit Function
    End If
    
    'Wrap Up
    GenerateTransfers = 1
    
Exit Function
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
        Resume Next
    Else
        If f_IsRecordsetValidAndOpen(rsOverstocks) Then rsOverstocks.Close
        'Err.Raise Err.Number, Err.source & "(GenerateTransfers)", Err.Description
        f_HandleErr , , , "AIM_TransferManagement::GenerateTransfers", Now, gDRGeneralError, True, Err
    End If
End Function

Private Sub tabTransferMgmt_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    Select Case NewTab.Key
    Case tabTransferMgmt.Tabs("Transfer Management").Key
        'Check if tranfer can be done with out the possibility of double dipping
        'If not warn the user that double dipping may occur.
        RtnCode = ValidateTransfer
        If RtnCode = -1 Then
            strMessage = getTranslationResource("MSGBOX03803")
            If StrComp(strMessage, "MSGBOX03803") = 0 Then strMessage = "Order Generation has been run and items are already available for review " & _
                   "to the Buyer. Do you want to still continue with transfer?"
            RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
            If RtnCode = vbYes Then
                'continue to show the tab
            Else
                'Failed. Clear all
                ToggleToolbar False
                Cancel = True
                Exit Sub
            End If
        End If
            
        ReportOrTransferTab = 2
        RtnCode = GenerateTransfers
        If RtnCode = -1 Then
            'Failed. Clear all
            ToggleToolbar False
            Cancel = True
            Exit Sub
        Else
            dgTransferMgmtInitColumnProps 'la here
            'Enable tools for the transfer management tab
            
            'call function to populate the grid
            PopulateTransferGrid
            ToggleToolbar True
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False
        End If
            
    Case tabTransferMgmt.Tabs("Transfer Configuration").Key
        If dgTransferMgmt.DataChanged Then
            strMessage = getTranslationResource("MSGBOX03801")
            If StrComp(strMessage, "MSGBOX03801") = 0 Then strMessage = "Do you wish to save the changes to the Transfer record?"
            RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
            If RtnCode = vbYes Then
                SaveTransferRecord
            Else
                'ignore
            End If
        End If
        tbNavigation.Tools.Item(4).Enabled = False
        tbNavigation.Tools.Item(5).Enabled = False
        tbNavigation.Tools.Item(6).Enabled = False
        tbNavigation.Tools.Item(7).Enabled = False
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tabTransferMgmt_BeforeTabClick)"
     f_HandleErr , , , "AIM_TransferManagement::tabTransferMgmt_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler
    
    If ColIndex = 6 Then 'Suggested Transfer Qty column allow only numeric values
        If Not IsNumeric(dgTransferMgmt.Columns(dgTransferMgmt.Col).Text) Then
            Cancel = True
        End If
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & " dgTransferMgmt_BeforeColUpdate"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim SortOrder As Integer
    Dim ColIdx As Long
    Dim ColType As Long
    
    If SortItemAsc = True Then
        SortOrder = XORDER_DESCEND
        SortItemAsc = False
    Else
        SortOrder = XORDER_ASCEND
        SortItemAsc = True
    End If
    
'    ColType = Me.dgTransferMgmt.Columns(ColIndex).DataType
'    OverStockDBArray.QuickSort 0, OverStockDBArray.UpperBound(1), ColIndex, SortOrder, ColType
    
    Select Case UCase$(Me.dgTransferMgmt.Columns(ColIndex).Name)
    Case UCase$("Delete")
        ColIdx = 0
        ColType = XTYPE_BOOLEAN
    Case UCase$("Item")
        ColIdx = 1
        ColType = XTYPE_STRING
    Case UCase$("itdesc")
        ColIdx = 2
        ColType = XTYPE_STRING
    Case UCase$("FromLcId")
        ColIdx = 3
        ColType = XTYPE_STRING
    Case UCase$("Fromoh")
        ColIdx = 4
        ColType = XTYPE_LONG
    Case UCase$("FromBestQty")
        ColIdx = 5
        ColType = XTYPE_LONG
    Case UCase$("SugTrasQty")
        ColIdx = 6
        ColType = XTYPE_LONG
    Case UCase$("ToLcId")
        ColIdx = 7
        ColType = XTYPE_STRING
    Case UCase$("ToOh")
        ColIdx = 8
        ColType = XTYPE_LONG
    Case UCase$("ToBestQty")
        ColIdx = 9
        ColType = XTYPE_LONG
    Case UCase$("ToFcstDmd")
        ColIdx = 10
        ColType = XTYPE_LONG
    Case UCase$("FromLocDmd")
        ColIdx = 11
        ColType = XTYPE_LONG
    Case UCase$("ExItemWeight")
        ColIdx = 12
        ColType = XTYPE_DOUBLE
    Case UCase$("ExItemCube")
        ColIdx = 13
        ColType = XTYPE_DOUBLE
    Case UCase$("itstat")
        ColIdx = 14
        ColType = XTYPE_STRING
    End Select

    OverStockDBArray.QuickSort 0, OverStockDBArray.UpperBound(1), ColIdx, SortOrder, ColType
    dgTransferMgmt.ReBind
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "( dgTransferMgmt_HeadClick)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_LostFocus()
On Error GoTo ErrorHandler
    
    'Force an update
    If Me.dgTransferMgmt.RowChanged Then
        Me.dgTransferMgmt.Update
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgTransferMgmt_LostFocus)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_LostFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler
    
    If IsNull(StartLocation) Then
            StartLocation = 0
    End If
    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "( dgTransferMgmt_UnboundPositionData)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler
    
    Dim iRBRow As Integer
    Dim iCol As Integer
    Dim irow As Integer
    Dim iPoint As Integer
    
    irow = 0
    On Error Resume Next
    If OverStockDBArray.Count(1) = 0 Then
        Exit Sub
    End If
    On Error GoTo ErrorHandler
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            iPoint = OverStockDBArray.UpperBound(1)
        Else
            iPoint = 0
        End If
    Else
        iPoint = StartLocation
        If ReadPriorRows Then
            iPoint = iPoint - 1
        Else
            iPoint = iPoint + 1
        End If
    End If

     For iRBRow = 0 To RowBuf.RowCount - 1
            If iPoint < 0 Or iPoint > OverStockDBArray.UpperBound(1) Then Exit For
    
            For iCol = 0 To RowBuf.ColumnCount - 1  'la here(OverStockDBArray.UpperBound(2)) = 23!!!
                RowBuf.Value(iRBRow, iCol) = OverStockDBArray(iPoint, iCol)
            Next iCol
            
            RowBuf.Bookmark(iRBRow) = iPoint
            If ReadPriorRows Then
                iPoint = iPoint - 1
            Else
                iPoint = iPoint + 1
            End If
            irow = irow + 1
     Next iRBRow
     RowBuf.RowCount = irow

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "( dgTransferMgmt_UnboundReadData)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTransferMgmt_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler
    
    Dim iCol As Integer
    
    On Error Resume Next
    If OverStockDBArray.Count(1) = 0 Then
        Exit Sub
    End If
    On Error GoTo ErrorHandler
        
    For iCol = 0 To RowBuf.ColumnCount - 1  'la here (OverStockDBArray.UpperBound(2))
        If Not IsNull(RowBuf.Value(0, iCol)) Then
            OverStockDBArray(CInt(WriteLocation), iCol) = CStr(RowBuf.Value(0, iCol))
        End If
    Next iCol
    
    If Not IsNull(RowBuf.Value(0, 6)) Then  'SugTransferQty
        OverStockDBArray(CInt(WriteLocation), 12) = CStr(RowBuf.Value(0, 6)) * OverStockDBArray(CInt(WriteLocation), 19)    'SugTransferQty*weight
        OverStockDBArray(CInt(WriteLocation), 13) = CStr(RowBuf.Value(0, 6)) * OverStockDBArray(CInt(WriteLocation), 20)     'SugTransferQty*Cube
    End If
 
    ExtendedCostAndWeight

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "( dgTransferMgmt_UnboundWriteData)"
     f_HandleErr , , , "AIM_TransferManagement::dgTransferMgmt_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Function AddNewOverStockItem() As Integer
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim rsTemp As ADODB.Recordset
    Dim iSize As Integer
    Dim iCol As Integer
    Dim strMessage As String
    
    strSQL = "SELECT DEL=0, Item.Item, Item.ItDesc, Item.LcID, Item.OH, FromBest=0, SugTransferQty=0,"
    strSQL = strSQL & vbCrLf & "ToLcID='" & AddOverStockToLocation & "', ToOH=0, ToBest=0, ToFcstDemand=0,"
    strSQL = strSQL & vbCrLf & "Item.FcstDemand, ExtWeight=0, ExtCube=0, Item.ItStat,Item.convfactor ,"
    strSQL = strSQL & vbCrLf & "Item.OrderQty, ToOrderPt=0, ToOrderQty=0, Item.Weight, Item.Cube, "
    strSQL = strSQL & vbCrLf & "Item.OrderPt, Item.Cost, Item.BinLocation,0 ,0,Item.UOM "
    strSQL = strSQL & vbCrLf & "FROM Item"
    strSQL = strSQL & vbCrLf & "WHERE Item.Item = N'" & AddOverStockItem & "'"
    strSQL = strSQL & vbCrLf & "AND Item.LcID = N'" & AddOverStockFromLocation & "'"
    
    Set rsTemp = New ADODB.Recordset
    rsTemp.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
        iSize = OverStockDBArray.UpperBound(1)
        OverStockDBArray.ReDim 0, iSize + 1, 0, OverStockDBArray.UpperBound(2)
        
        For iCol = 0 To OverStockDBArray.UpperBound(2)
            If iCol = 23 Then   'col 23 is zone
                OverStockDBArray(iSize + 1, iCol) = Mid(rsTemp(iCol).Value, 1, 1)
            Else
                OverStockDBArray(iSize + 1, iCol) = rsTemp(iCol).Value
            End If
        Next iCol
        
        If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
        Set rsTemp = Nothing
        
        strSQL = "SELECT Item.FcstDemand, Item.OH, Item.OrderPt, Item.OrderQty"
        strSQL = strSQL & vbCrLf & "FROM Item"
        strSQL = strSQL & vbCrLf & "WHERE Item.Item = N'" & AddOverStockItem & "'"
        strSQL = strSQL & vbCrLf & "AND Item.LcID = N'" & AddOverStockToLocation & "'"
        
        Set rsTemp = New ADODB.Recordset
        rsTemp.Open strSQL, Cn, adOpenStatic, adLockReadOnly
            If rsTemp.RecordCount = 1 Then
                OverStockDBArray(iSize + 1, 10) = rsTemp(0).Value 'tofcstdemand
                OverStockDBArray(iSize + 1, 8) = rsTemp(1).Value    'tooh
                OverStockDBArray(iSize + 1, 17) = rsTemp(2).Value   'toorderpt
                OverStockDBArray(iSize + 1, 18) = rsTemp(3).Value   'toorderqty
            End If
        dgTransferMgmt.ReBind
        
        AddNewOverStockItem = 1
        dgTransferMgmt.Rows = OverStockDBArray.UpperBound(1) + 1
        strMessage = getTranslationResource("STATMSG03802")
        If StrComp(strMessage, "STATMSG03802") = 0 Then strMessage = "Item added for transfer:"
        strMessage = strMessage & " " & AddOverStockItem
        Write_Message strMessage
            
    Else
        AddNewOverStockItem = 0
    End If
    
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(AddNewOverStockItem)"
     f_HandleErr , , , "AIM_TransferManagement::AddNewOverStockItem", Now, gDRGeneralError, True, Err
End Function

Private Function ProcessReport(p_ToolID As String)
On Error GoTo ErrorHandler
    
    Dim OverstockReport As AIM_Transfer_Report
    Dim strMessage As String
    Dim SQL_Overstocks As String
    Dim SQL_Shortages As String
    Dim IndexCounter As Integer
    Dim DemandOption As Integer
    Dim Drop_Ship_YN As String
    
    Dim rsTemp As ADODB.Recordset
    
    If StrComp(p_ToolID, tbNavigation.Tools("ID_Preview").ID, vbTextCompare) = 0 Then
        'If the use clicked the Preview menu item then show the report else it will be TransferManagement Tab
        ReportOrTransferTab = 1
    Else
        ReportOrTransferTab = 2
    End If
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03800")
    If StrComp(strMessage, "STATMSG03800") = 0 Then strMessage = "Building the Transfer Management Report..."
    Write_Message strMessage
    
    'Force Grids Update
    Me.dgAIMClasses.Update
    Me.dgAIMLocations.Update
    Me.dgAIMOSLocations.Update
    
    'Build SQL Statements
    SQL_Overstocks = BldOverstockStmt
    SQL_Shortages = BldShortageStmt
    
    For IndexCounter = Me.optReportOption.LBound To Me.optReportOption.UBound
        If Me.optReportOption(IndexCounter) = True Then
            DemandOption = optReportOption(IndexCounter).Tag
            Exit For
        End If
    Next IndexCounter
    
    'DemandOption 1 or 0  are same the BldOverStockStmt has taken care of it by changing the Rcdid
    If DemandOption = 1 Then DemandOption = 0
     'DemandOption 4 or 3  are same the BldOverStockStmt has taken care of it by changing the Rcdid
    If DemandOption = 4 Then DemandOption = 3
    
    If ReportOrTransferTab = 1 Then
        'If it is a report and it is Overstock only ,Shortages only or OverStock and Shortages use
        'AIM_Transfer_SP Stored Procedure
        
        Set rsTemp = New ADODB.Recordset
        If DemandOption = 0 Or DemandOption = 2 Then
            AIM_Transfer_SP.Parameters("@SQL_Overstocks").Value = SQL_Overstocks
            AIM_Transfer_SP.Parameters("@SQL_Shortages").Value = SQL_Shortages
            AIM_Transfer_SP.Parameters("@RptOpt").Value = DemandOption
            rsTemp.Open AIM_Transfer_SP
        ElseIf DemandOption = 3 Then
            'If it is report option and
            'For OverStock with Shortages and  Shortages with OverStock use AIM_TransferToReport_SP
            AIM_TransferToReport_SP.Parameters("@SQL_Overstocks").Value = SQL_Overstocks
            AIM_TransferToReport_SP.Parameters("@SQL_Shortages").Value = SQL_Shortages
            AIM_TransferToReport_SP.Parameters("@RptOpt").Value = DemandOption
            BuildFromLcids
            AIM_TransferToReport_SP.Parameters("@From_Lcids").Value = BldFromLcids
            If Me.CkDFS = vbChecked Then
                Drop_Ship_YN = "Y"
            Else
                Drop_Ship_YN = "N"
            End If
            AIM_TransferToReport_SP.Parameters("@Drop_Ship_YN").Value = Drop_Ship_YN
            rsTemp.Open AIM_TransferToReport_SP
        End If
    End If
    
    Write_Message ""
    Screen.MousePointer = vbNormal
    
    If Not f_IsRecordsetOpenAndPopulated(rsTemp) Then
        strMessage = getTranslationResource("MSGBOX03800")
        If StrComp(strMessage, "MSGBOX03800") = 0 Then strMessage = "No overrages found that meet the specified criteria."
        strMessage = strMessage & vbCrLf & vbCrLf
        strMessage = strMessage     '& SQL_Overstocks
        MsgBox strMessage, vbInformation + vbOKOnly, Me.Caption
        If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
        Set rsTemp = Nothing
        Exit Function
        
    End If

    'Instantiate the Report
    Set OverstockReport = New AIM_Transfer_Report
    Set OverstockReport.dcItem.Recordset = rsTemp
    Set AIM_Reports.ARViewer.ReportSource = OverstockReport

    OverstockReport.NbrWeeks = Me.txtNbrOfWeeks.Value
    OverstockReport.OUTL = IIf(Me.ckOUTL = vbChecked, True, False)
    OverstockReport.OrderPt = IIf(Me.ckOrderPt = vbChecked, True, False)
    OverstockReport.ShortageWeeks = Me.txtShortageWeeks.Value

    Select Case p_ToolID
        Case tbNavigation.Tools("ID_Print").ID
            OverstockReport.PrintReport True

        Case tbNavigation.Tools("ID_Preview").ID
            AIM_Reports.Show vbModal, AIM_Main

    End Select
    
    'Wrap Up
    Set OverstockReport = Nothing
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    
Exit Function
ErrorHandler:
    Set OverstockReport = Nothing
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    'Err.Raise Err.Number, Err.source & "(ProcessReport)", Err.Description
     f_HandleErr , , , "AIM_TransferManagement::ProcessReport", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleToolbar(p_Enabled As Boolean)
On Error GoTo ErrorHandler
    
    Me.tbNavigation.Tools("ID_GetFirst").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_GetPrev").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_GetNext").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_GetLast").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_AddNewItem").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_SelectAllForTransfer").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_UnSelectAllForTransfer").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_SetBookMark").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = False    'always turn the goto off, unless set with bookmark
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(ToggleToolbar)", Err.Description
     f_HandleErr , , , "AIM_TransferManagement::ToggleToolbar", Now, gDRGeneralError, True, Err
End Function


