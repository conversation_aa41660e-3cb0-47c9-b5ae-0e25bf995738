VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_UsersMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Users Maintenance"
   ClientHeight    =   7965
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   9060
   Icon            =   "AIM_UsersMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   7965
   ScaleWidth      =   9060
   ShowInTaskbar   =   0   'False
   Visible         =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   240
      Top             =   7560
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_UsersMaintenance.frx":030A
      ToolBars        =   "AIM_UsersMaintenance.frx":8F08
   End
   Begin VB.Frame fraAIMUsers 
      Height          =   2625
      Left            =   83
      TabIndex        =   0
      Top             =   45
      Width           =   8895
      Begin VB.CommandButton cmdResetPassword 
         Caption         =   "Reset &Password"
         Height          =   375
         Left            =   6840
         Style           =   1  'Graphical
         TabIndex        =   6
         Top             =   2048
         Visible         =   0   'False
         Width           =   1921
      End
      Begin VB.CheckBox ckLogErrors 
         Caption         =   "Write Errors to Log"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   2737
         TabIndex        =   5
         Top             =   2123
         Width           =   2450
      End
      Begin TDBText6Ctl.TDBText txtUserName 
         Height          =   345
         Left            =   2737
         TabIndex        =   1
         Top             =   549
         Width           =   3840
         _Version        =   65536
         _ExtentX        =   6773
         _ExtentY        =   609
         Caption         =   "AIM_UsersMaintenance.frx":9220
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":928C
         Key             =   "AIM_UsersMaintenance.frx":92AA
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtUserInitials 
         Height          =   345
         Left            =   2737
         TabIndex        =   2
         Top             =   933
         Width           =   690
         _Version        =   65536
         _ExtentX        =   1217
         _ExtentY        =   609
         Caption         =   "AIM_UsersMaintenance.frx":92EE
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":935A
         Key             =   "AIM_UsersMaintenance.frx":9378
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   3
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLangID 
         Bindings        =   "AIM_UsersMaintenance.frx":93BC
         Height          =   345
         Left            =   2737
         TabIndex        =   3
         Top             =   1317
         Width           =   1394
         DataFieldList   =   "LangId"
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2459
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "LangID"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcRole 
         Height          =   345
         Left            =   2737
         TabIndex        =   4
         Top             =   1701
         Width           =   3840
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   3
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   6773
         _ExtentY        =   609
         _StockProps     =   93
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 0"
      End
      Begin TDBText6Ctl.TDBText txtUserId 
         Height          =   345
         Left            =   2737
         TabIndex        =   30
         Top             =   165
         Width           =   1955
         _Version        =   65536
         _ExtentX        =   3448
         _ExtentY        =   609
         Caption         =   "AIM_UsersMaintenance.frx":93D3
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":943F
         Key             =   "AIM_UsersMaintenance.frx":945D
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "User Name"
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   35
         Top             =   594
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "User's Initials"
         Height          =   300
         Index           =   2
         Left            =   120
         TabIndex        =   34
         Top             =   978
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Language Preference"
         Height          =   300
         Index           =   3
         Left            =   120
         TabIndex        =   33
         Top             =   1362
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Role"
         Height          =   300
         Index           =   7
         Left            =   120
         TabIndex        =   32
         Top             =   1746
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "User ID"
         Height          =   300
         Index           =   0
         Left            =   120
         TabIndex        =   31
         Top             =   210
         Width           =   2445
      End
   End
   Begin VB.CheckBox ckLVCostAlert 
      Caption         =   "Lower Vendor Cost Alert"
      BeginProperty DataFormat 
         Type            =   5
         Format          =   ""
         HaveTrueFalseNull=   1
         TrueValue       =   "True"
         FalseValue      =   "False"
         NullValue       =   ""
         FirstDayOfWeek  =   0
         FirstWeekOfYear =   0
         LCID            =   1033
         SubFormatType   =   7
      EndProperty
      Height          =   300
      Left            =   240
      TabIndex        =   15
      Top             =   5880
      Width           =   3840
   End
   Begin VB.Frame fraUserAlerts 
      Height          =   4845
      Left            =   83
      TabIndex        =   29
      Top             =   2670
      Width           =   8895
      Begin TDBNumber6Ctl.TDBNumber txtPOExtCostLimit 
         Height          =   345
         Left            =   7320
         TabIndex        =   20
         Top             =   585
         Width           =   1395
         _Version        =   65536
         _ExtentX        =   2461
         _ExtentY        =   609
         Calculator      =   "AIM_UsersMaintenance.frx":94A1
         Caption         =   "AIM_UsersMaintenance.frx":94C1
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Courier New"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":952D
         Keys            =   "AIM_UsersMaintenance.frx":954B
         Spin            =   "AIM_UsersMaintenance.frx":9595
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##############0;-##############0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##############0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999999999999999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   7602181
         MinValueVT      =   5
      End
      Begin VB.CheckBox ckPOExtCostLimit 
         Caption         =   "Purchase Order Ext Cost"
         Height          =   374
         Left            =   4440
         TabIndex        =   18
         Top             =   195
         Width           =   3840
      End
      Begin VB.CheckBox ckAltSource 
         Caption         =   "Alternate Source"
         Height          =   255
         Left            =   120
         TabIndex        =   14
         Top             =   2868
         Width           =   1695
      End
      Begin VB.CheckBox ckInvAvailRestriction 
         Caption         =   "Restrict Inventory Availability"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   16
         Top             =   3600
         Width           =   3840
      End
      Begin VB.CheckBox ckBackOrdered 
         Caption         =   "Back-ordered"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   13
         Top             =   2499
         Width           =   3840
      End
      Begin VB.CheckBox ckSafetyStockEroded 
         Caption         =   "Safety Stock Eroded"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   12
         Top             =   2130
         Width           =   3840
      End
      Begin VB.CheckBox ckOverDuePO 
         Caption         =   "Overdue Purchase Order"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   11
         Top             =   1761
         Width           =   3840
      End
      Begin VB.CheckBox ckLastWkSaleExcpts 
         Caption         =   "Sale Last Week"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   10
         Top             =   1392
         Width           =   3840
      End
      Begin VB.CheckBox ckPackSizeExcepts 
         Caption         =   "Pack Size Rounding"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   4440
         TabIndex        =   24
         Top             =   1761
         Width           =   3840
      End
      Begin VB.CheckBox ckExtCostExcpts 
         Caption         =   "Extended Cost"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   4440
         TabIndex        =   21
         Top             =   1023
         Width           =   3840
      End
      Begin VB.CheckBox ckOnPromotionExcpts 
         Caption         =   "On Promotion"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   9
         Top             =   1023
         Width           =   3840
      End
      Begin VB.CheckBox ckDmdUpdExcpts 
         Caption         =   "Demand Update Exceptions"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   8
         Top             =   654
         Value           =   1  'Checked
         Width           =   3840
      End
      Begin VB.CheckBox ckSLevel 
         Caption         =   "SSA AIM System Administrator"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   120
         TabIndex        =   7
         Top             =   285
         Width           =   3840
      End
      Begin TDBNumber6Ctl.TDBNumber txtExtCostLimit 
         Height          =   345
         Left            =   7350
         TabIndex        =   23
         Top             =   1347
         Width           =   1395
         _Version        =   65536
         _ExtentX        =   2461
         _ExtentY        =   609
         Calculator      =   "AIM_UsersMaintenance.frx":95BD
         Caption         =   "AIM_UsersMaintenance.frx":95DD
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Courier New"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":9649
         Keys            =   "AIM_UsersMaintenance.frx":9667
         Spin            =   "AIM_UsersMaintenance.frx":96B1
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "######0.00;-######0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "######0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBText6Ctl.TDBText txtInvAvailList 
         Height          =   705
         Left            =   120
         TabIndex        =   17
         Top             =   3960
         Width           =   3840
         _Version        =   65536
         _ExtentX        =   6773
         _ExtentY        =   1244
         Caption         =   "AIM_UsersMaintenance.frx":96D9
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":9745
         Key             =   "AIM_UsersMaintenance.frx":9763
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtPS_PctRoundLimit 
         Height          =   345
         Left            =   7320
         TabIndex        =   26
         Top             =   2085
         Width           =   1395
         _Version        =   65536
         _ExtentX        =   2461
         _ExtentY        =   609
         Calculator      =   "AIM_UsersMaintenance.frx":97A7
         Caption         =   "AIM_UsersMaintenance.frx":97C7
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Courier New"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":9833
         Keys            =   "AIM_UsersMaintenance.frx":9851
         Spin            =   "AIM_UsersMaintenance.frx":989B
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "0.00;-0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011627525
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtPS_ExtCostLimit 
         Height          =   345
         Left            =   7320
         TabIndex        =   28
         Top             =   2454
         Width           =   1395
         _Version        =   65536
         _ExtentX        =   2461
         _ExtentY        =   609
         Calculator      =   "AIM_UsersMaintenance.frx":98C3
         Caption         =   "AIM_UsersMaintenance.frx":98E3
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Courier New"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UsersMaintenance.frx":994F
         Keys            =   "AIM_UsersMaintenance.frx":996D
         Spin            =   "AIM_UsersMaintenance.frx":99B7
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "#########0.00;-#########0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#########0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999999999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label1 
         Caption         =   "Purchase Order Ext Cost Limit"
         Height          =   300
         Left            =   4725
         TabIndex        =   19
         Top             =   630
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "Percentage Rounding Limit"
         Height          =   300
         Index           =   5
         Left            =   4725
         TabIndex        =   25
         Top             =   2130
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "Extended Cost Limit"
         Height          =   300
         Index           =   6
         Left            =   4725
         TabIndex        =   27
         Top             =   2499
         Width           =   2445
      End
      Begin VB.Label Label 
         Caption         =   "Extended Cost Limit"
         Height          =   300
         Index           =   4
         Left            =   4725
         TabIndex        =   22
         Top             =   1392
         Width           =   2445
      End
   End
End
Attribute VB_Name = "AIM_UsersMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMUsers As ADODB.Recordset
Dim AIMUsersBookMark As Variant

Dim SCArray  As String
Dim UserID As Boolean
Dim IsSysAdmin As Boolean

Private Sub ckExtCostExcpts_Click()
On Error GoTo ErrorHandler

    If ckExtCostExcpts = vbUnchecked Then
        txtExtCostLimit.Enabled = False
    Else
        txtExtCostLimit.Enabled = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckExtCostExcpts_Click)"
     f_HandleErr , , , "AIM_UserMaintenance::ckExtCostExcpts_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ckInvAvailRestriction_Click()
On Error GoTo ErrorHandler

    If ckInvAvailRestriction = vbUnchecked Then
        txtInvAvailList.Enabled = False
    Else
        txtInvAvailList.Enabled = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckInvAvailRestriction_Click)"
     f_HandleErr , , , "AIM_UserMaintenance::ckInvAvailRestriction_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ckPackSizeExcepts_Click()
On Error GoTo ErrorHandler

    If ckPackSizeExcepts = vbUnchecked Then
        Me.txtPS_PctRoundLimit.Enabled = False
        Me.txtPS_ExtCostLimit.Enabled = False
    Else
        Me.txtPS_PctRoundLimit.Enabled = True
        Me.txtPS_ExtCostLimit.Enabled = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckPackSizeExcepts_Click)"
     f_HandleErr , , , "AIM_UserMaintenance::ckPackSizeExcepts_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ckPOExtCostLimit_Click()
On Error GoTo ErrorHandler

    If ckPOExtCostLimit = vbUnchecked Then
        txtPOExtCostLimit.Enabled = False
    Else
        txtPOExtCostLimit.Enabled = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ckPOExtCostLimit_Click)"
     f_HandleErr , , , "AIM_UserMaintenance::ckPOExtCostLimit_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdResetPassword_Click()
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    If Trim$(Me.txtUserId) = "" Then
        strMessage = getTranslationResource("MSGBOX06001")
        If StrComp(strMessage, "MSGBOX06001") = 0 Then strMessage = "Please select a valid User ID."
        MsgBox strMessage, vbInformation + vbOKOnly, getTranslationResource(Me.Caption)
        Exit Sub
    End If
    AIM_SAChangePassword.Show vbModeless

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdResetPassword_Click)"
     f_HandleErr , , , "AIM_UserMaintenance::cmdResetPassword_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLangId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcLangID.Columns(0).Caption = getTranslationResource("Language ID")
    Me.dcLangID.Columns(0).Width = 1000
    Me.dcLangID.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLangID.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLangID.Columns(0).DataField = "LangID"
    
    Me.dcLangID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLangID.Columns(1).Width = 2500
    Me.dcLangID.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLangID.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLangID.Columns(1).DataField = "LangDesc"
    

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLangID, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcLangID.Columns.Count - 1
'        dcLangID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLangID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangID_InitColumnProps)"
     f_HandleErr , , , "AIM_UserMaintenance::dcLangID_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcRole_InitColumnProps()
    On Error GoTo ErrorHandler
    Me.dcRole.Columns(0).Caption = getTranslationResource("Role")
    Me.dcRole.Columns(0).Width = 1000
    Me.dcRole.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRole.Columns(0).Alignment = ssCaptionAlignmentLeft
    
    Me.dcRole.Columns(1).Caption = getTranslationResource("Description")
    Me.dcRole.Columns(1).Width = 2000
    Me.dcRole.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRole.Columns(1).Alignment = ssCaptionAlignmentLeft
    
    Me.dcRole.Columns(2).Caption = getTranslationResource("Level")
    Me.dcRole.Columns(2).Width = 750
    Me.dcRole.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcRole.Columns(2).Alignment = ssCaptionAlignmentLeft
    
    
    Me.dcRole.ScrollBars = ssScrollBarsAutomatic
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcRole_InitColumnProps)"
     f_HandleErr , , , "AIM_UserMaintenance::dcRole_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_UserMaintenance::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim strYes As String
    Dim rstSysAdmin As ADODB.Recordset
    
    Me.txtUserId.Enabled = False
    
    UserID = False
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG06000")
    If StrComp(strMessage, "STATMSG06000") = 0 Then strMessage = "Initializing Users Maintenance..."
    Write_Message strMessage
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    IsSysAdmin = False
    strSQL = "SELECT SLevel FROM AIMUsers WHERE UserID = N'" & Trim$(gUserID) & "'"
    Set rstSysAdmin = New ADODB.Recordset
    rstSysAdmin.Open strSQL, Cn
    If Not rstSysAdmin.eof Then
        If rstSysAdmin(0) = 1 Then
            IsSysAdmin = True
        Else
            IsSysAdmin = False
        End If
    End If
    rstSysAdmin.Close
    Set rstSysAdmin = Nothing
    If g_UpdatePassword Then
        If IsSysAdmin = True Then
            Me.cmdResetPassword.Visible = True
        Else
            Me.cmdResetPassword.Visible = False
        End If
    End If
    
    GetTranslatedCaptions Me
    
    'Call langdropdown here
    PopulateLangIDs
    
    PopulateRoles
    
    'Initialize the Security Array
    SCArray = String(100, "N")
    
    'Open the Record Set
    Set rsAIMUsers = New ADODB.Recordset
    With rsAIMUsers
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    'Build the SQL Statement
    strSQL = "SELECT * FROM AIMUsers" & _
            " ORDER BY UserID "
    'Fetch all users
    rsAIMUsers.Open strSQL, Cn
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
            getTranslationResource("Edit")
    Else
        f_AIMUsers_InitializeNew
    End If
        
    'Refresh the form
    RefreshForm
        
    'Make the spin button visible
    Me.txtExtCostLimit.Spin.Visible = 1
    Me.txtPS_ExtCostLimit.Spin.Visible = 1
    Me.txtPS_PctRoundLimit.Spin.Visible = 1

    Me.txtPOExtCostLimit.Spin.Visible = dbiShowAlways
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_UserMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
    RemoveBlankData
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_UserMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim UserID As String
    Dim AIMUserBookMark As Variant
    Dim strText As String
    Dim strMessage As String
    Dim strSQL As String
    Dim ExecSp As ADODB.Command
                
    Dim RtnCodeSave As Integer
    
    RtnCodeSave = FAIL
    Write_Message ""
       
    f_AIMUsers_Update
       
    strText = getTranslationResource(AIM_UsersMaintenance.Caption)
        
    Me.txtUserId.Enabled = False
    'Alert user to possible change
    Select Case Tool.ID
    Case "ID_AddNew", "ID_Close", "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
        "ID_GoToBookMark", "ID_LookUp"
        
        If gAccessLvl <> 1 And DataChanged(rsAIMUsers, adAffectCurrent) > 0 Then
            strMessage = getTranslationResource("MSGBOX06003")
            If StrComp(strMessage, "MSGBOX06003") = 0 Then strMessage = "Abandon changes to current record?"
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
            If RtnCode = vbYes Then
                rsAIMUsers.CancelUpdate
            Else
                RtnCodeSave = f_AIMUsers_Save
                If RtnCodeSave = FAIL Then
                    If Me.txtUserId.Enabled Then
                        Me.txtUserId.SetFocus
                    Else
                        Me.txtUserInitials.SetFocus
                    End If
                    Exit Sub
                End If
            End If
        
        End If
    
    End Select
    
    'Navigate
    Select Case Tool.ID
    
    Case "ID_AddNew"
        f_AIMUsers_InitializeNew
        Me.txtUserId.Enabled = True
    
    Case "ID_Delete"
        strMessage = getTranslationResource("MSGBOX06004")
        If StrComp(strMessage, "MSGBOX06004") = 0 Then strMessage = "Delete the current User?"
        RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, strText)
        If RtnCode = vbYes Then
            rsAIMUsers.Delete
            If g_CreateDBUser Then
                Set ExecSp = New ADODB.Command
                With ExecSp
                    Set .ActiveConnection = Cn
                    .CommandType = adCmdText
                    
                    strSQL = "EXEC sp_dropuser N'" & Trim$(txtUserId) & "'" ',null, '" & gDataBase & "'"
                    .CommandText = strSQL
                    .Execute
    
                    strSQL = "EXEC sp_droplogin N'" & Trim$(txtUserId) & "'" ',null, '" & gDataBase & "'"
                    .CommandText = strSQL
                    .Execute
                End With
                If Not (ExecSp Is Nothing) Then Set ExecSp.ActiveConnection = Nothing
                Set ExecSp = Nothing
                
                rsAIMUsers.MovePrevious
                If rsAIMUsers.BOF Then
                    rsAIMUsers.MoveFirst
                End If
            End If
        End If
        
    Case "ID_GetFirst"
        rsAIMUsers.MoveFirst
    
    Case "ID_GetPrev"
        rsAIMUsers.MovePrevious
        If rsAIMUsers.BOF Then
            rsAIMUsers.MoveFirst
        End If
        
    Case "ID_GetNext"
        rsAIMUsers.MoveNext
        If rsAIMUsers.eof Then
            rsAIMUsers.MoveLast
        End If
        
    Case "ID_GetLast"
        rsAIMUsers.MoveLast
    
    Case "ID_Save"
        f_AIMUsers_Save
        
    Case "ID_SetBookMark"
        AIMUsersBookMark = rsAIMUsers.Bookmark
        Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        
    Case "ID_GoToBookMark"
        rsAIMUsers.Bookmark = AIMUsersBookMark
    
    Case "ID_LookUp"
        'Save the current Bookmark
        AIMUsersBookMark = rsAIMUsers.Bookmark
        
        AIM_UsersLookUp.UserIdKey = Me.txtUserId.Text
        Set AIM_UsersLookUp.Cn = Cn
        AIM_UsersLookUp.Show vbModal

        If AIM_UsersLookUp.CancelFlag Then
            rsAIMUsers.Bookmark = AIMUsersBookMark
            Exit Sub
        
        Else
            rsAIMUsers.MoveFirst
            UserID = AIM_UsersLookUp.UserIdKey
            rsAIMUsers.Find "UserID = '" & Trim$(UserID) & "'"
        End If

    Case "ID_Close"
        If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
            rsAIMUsers.MoveFirst
            rsAIMUsers.CancelUpdate
        End If
        Unload Me
        Exit Sub

    End Select

    If Not (ExecSp Is Nothing) Then Set ExecSp.ActiveConnection = Nothing
    Set ExecSp = Nothing
    RefreshForm        'Refresh the form
    If txtUserId.Enabled Then
        Me.txtUserId.SetFocus
    Else
        Me.txtUserId.Enabled = False
    End If
  
Exit Sub
ErrorHandler:
    If Err.Number = -2147217900 Then
       Resume Next
    Else
        If Not (ExecSp Is Nothing) Then Set ExecSp.ActiveConnection = Nothing
        Set ExecSp = Nothing
        'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
        f_HandleErr , , , "AIM_UserMaintenance::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Function f_AIMUsers_InitializeNew()
On Error GoTo ErrorHandler

    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim rsSysCtrl As ADODB.Recordset
        
    If Not f_IsRecordsetValidAndOpen(rsAIMUsers) Then Exit Function
    
    rsAIMUsers.AddNew
    
    rsAIMUsers("UserID").Value = ""
    rsAIMUsers("UserName").Value = ""
    rsAIMUsers("SLevel").Value = 0
    rsAIMUsers("SCArray").Value = String(100, "N")

    rsAIMUsers("DmdUpdExcpts").Value = "N"
    rsAIMUsers("OnPromotionExcpts").Value = "N"
    rsAIMUsers("ExtCostExcpts").Value = "N"
    rsAIMUsers("ExtCostLimit").Value = 1000
    rsAIMUsers("PackSizeExcpts").Value = "N"
    rsAIMUsers("PS_PctRoundLimit").Value = 0.7
    rsAIMUsers("PS_ExtCostLimit").Value = 100
    rsAIMUsers("LastWeekSaleExcpts").Value = "N"
    rsAIMUsers("OverDuePO").Value = "N"
    rsAIMUsers("SafetyStockEroded").Value = "N"
    rsAIMUsers("BackOrdered").Value = "N"
    rsAIMUsers("InvAvailRestriction").Value = "N"
    rsAIMUsers("InvAvailList").Value = ""
    rsAIMUsers("UserInitials").Value = ""

    rsAIMUsers("POExtCost").Value = "N"
    rsAIMUsers("POExtCostLimit").Value = 1000

    'Get the System Control Record
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
    End With
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    If f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        rsAIMUsers("LangID").Value = LCase(rsSysCtrl!dft_LangID)
    End If
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
    If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    Me.txtUserId.Enabled = True
    Me.txtUserId.SetFocus
    Me.tbNavigation.Refresh
 
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_AIMUsers_InitializeNew)"
    f_HandleErr , , , "AIM_UserMaintenance::f_AIMUsers_InitializeNew", Now, gDRGeneralError, True, Err
End Function

Private Function RefreshForm()
On Error GoTo ErrorHandler
    'RemoveBlankData
    'Enable/Disable Key fields
    If rsAIMUsers.EditMode <> adEditAdd Then
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            ToggleFieldAccess False
            
            If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
                If rsAIMUsers!UserID = gUserID Then
                    'Allow them to update User Name, Language and ErrorLogging.
                    Me.tbNavigation.Tools("ID_Save").Enabled = True
                    'Me.txtUserName.Enabled = True
                    If txtUserId.Enabled Then
                        Me.txtUserId.Enabled = True
                    Else
                        Me.txtUserId.Enabled = False
                    End If
                    
                    Me.txtUserInitials.Enabled = True
                    Me.dcLangID.Enabled = True
                    Me.ckLogErrors.Enabled = True
                    
                    Me.dcRole.Enabled = True
                End If
            End If
            
        Else
            ToggleFieldAccess True
        End If

        'Me.txtUserID.Enabled = False
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    End If
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then Exit Function

    Me.txtUserId.Text = rsAIMUsers("UserID").Value
    Me.txtUserName.Text = rsAIMUsers("UserName").Value
    Me.txtUserInitials.Text = rsAIMUsers("UserInitials").Value
    Me.dcLangID.Text = rsAIMUsers("LangID").Value
    
    Me.dcRole.Value = rsAIMUsers("RoleID").Value
    
    Me.ckSLevel.Value = IIf(rsAIMUsers("SLevel").Value = 1, vbChecked, vbUnchecked)
    
    SCArray = rsAIMUsers("SCArray").Value
    If Len(SCArray) < 100 Then
        SCArray = SCArray + String(100 - Len(SCArray), "N")
    End If
    
    Me.ckDmdUpdExcpts.Value = IIf(rsAIMUsers("DmdUpdExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.ckOnPromotionExcpts.Value = IIf(rsAIMUsers("OnPromotionExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.ckExtCostExcpts.Value = IIf(rsAIMUsers("ExtCostExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.txtExtCostLimit.Value = CDbl(rsAIMUsers("ExtCostLimit").Value)
    Me.ckPackSizeExcepts.Value = IIf(rsAIMUsers("PackSizeExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.txtPS_PctRoundLimit.Value = rsAIMUsers("PS_PctRoundLimit").Value
    Me.txtPS_ExtCostLimit.Value = rsAIMUsers("PS_ExtCostLimit").Value
    Me.ckLastWkSaleExcpts.Value = IIf(rsAIMUsers("LastWeekSaleExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.ckOverDuePO.Value = IIf(rsAIMUsers("OverDuePO").Value = "Y", vbChecked, vbUnchecked)
    Me.ckSafetyStockEroded.Value = IIf(rsAIMUsers("SafetyStockEroded").Value = "Y", vbChecked, vbUnchecked)
    Me.ckBackOrdered.Value = IIf(rsAIMUsers("BackOrdered").Value = "Y", vbChecked, vbUnchecked)
    Me.ckInvAvailRestriction.Value = IIf(rsAIMUsers("InvAvailRestriction").Value = "Y", vbChecked, vbUnchecked)
    Me.txtInvAvailList.Text = rsAIMUsers("InvAvailList").Value
    
    Me.ckAltSource.Value = IIf(rsAIMUsers("AltSourceExcpts").Value = "Y", vbChecked, vbUnchecked) 'Mohammed - MultiVendor
    Me.ckLVCostAlert.Value = IIf(rsAIMUsers("VendorCostExcpts").Value = "Y", vbChecked, vbUnchecked)
    
    Me.ckPOExtCostLimit.Value = IIf(rsAIMUsers("POExtCost").Value = "Y", vbChecked, vbUnchecked)
    Me.txtPOExtCostLimit.Value = IIf(rsAIMUsers("POExtCostLimit").Value > Me.txtPOExtCostLimit.MaxValue, Me.txtPOExtCostLimit.MaxValue, rsAIMUsers("POExtCostLimit").Value)
    
    'Annalakshmi - May 13, 2002
    Me.ckLogErrors.Value = IIf(rsAIMUsers!LogErrors = "Y", vbChecked, vbUnchecked)
    
    If g_UpdatePassword Then
        If UCase$(Me.txtUserId) <> "SA" Then
            Me.cmdResetPassword.Visible = True
        End If
    Else
        Me.cmdResetPassword.Visible = False
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(RefreshForm)"
    f_HandleErr , , , "AIM_UserMaintenance::RefreshForm", Now, gDRGeneralError, True, Err
End Function

Private Function ToggleFieldAccess(p_Enabled As Boolean)
On Error GoTo ErrorHandler

    Me.tbNavigation.Tools("ID_Save").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_Delete").Enabled = p_Enabled
    Me.tbNavigation.Tools("ID_AddNew").Enabled = p_Enabled
    
    Me.txtUserName.Enabled = p_Enabled
    Me.txtUserInitials.Enabled = p_Enabled
    Me.dcLangID.Enabled = p_Enabled
    
    Me.dcRole.Enabled = p_Enabled
    
    Me.ckSLevel.Enabled = p_Enabled
    Me.ckLogErrors.Enabled = p_Enabled
    Me.ckDmdUpdExcpts.Enabled = p_Enabled
    Me.ckOnPromotionExcpts.Enabled = p_Enabled
    Me.ckExtCostExcpts.Enabled = p_Enabled
    Me.ckPOExtCostLimit.Enabled = p_Enabled
    Me.ckPackSizeExcepts.Enabled = p_Enabled
    Me.ckLastWkSaleExcpts.Enabled = p_Enabled
    Me.ckOverDuePO.Enabled = p_Enabled
    Me.ckSafetyStockEroded.Enabled = p_Enabled
    Me.ckBackOrdered.Enabled = p_Enabled
    Me.ckInvAvailRestriction.Enabled = p_Enabled
    Me.ckAltSource.Enabled = p_Enabled
    Me.ckLVCostAlert.Enabled = p_Enabled
    
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(ToggleFieldAccess)", Err.Description
    f_HandleErr , , , "AIM_UserMaintenance::ToggleFieldAccess", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMUsers_Save() As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    
    Cn.Errors.Clear
    f_AIMUsers_Save = FAIL
    'Validate required info
    RtnCode = UserInputValidation
    If RtnCode = SUCCEED Then
        rsAIMUsers.Update
        f_AIMUsers_Save = SUCCEED
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, Cn.Errors.Count - 1
        Else
            strMessage = getTranslationResource("STATMSG06001")
            If StrComp(strMessage, "STATMSG06001") = 0 Then strMessage = "User successfully updated."
            Write_Message strMessage
        End If
    Else
        f_AIMUsers_Save = RtnCode
        f_AIMUsers_Save = FAIL
    End If
    
    RefreshUserLanguage
        
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMUsers_Save)", Err.Description
     f_HandleErr , , , "AIM_UserMaintenance::f_AIMUsers_Save", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMUsers_Update()
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim ExecSp As ADODB.Command
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then Exit Function
    
    If g_CreateDBUser _
    And Trim$(txtUserId) <> "" _
    Then
        Set ExecSp = New ADODB.Command
        With ExecSp
            .CommandType = adCmdText
            Set .ActiveConnection = Cn
            If Trim$(rsAIMUsers("UserID").Value) = "" _
            And Trim$(Me.txtUserId.Text) <> "" Then
                strSQL = "EXEC sp_addlogin N'" & Trim$(txtUserId) & "', null, N'" & gDataBase & "'"
                .CommandText = strSQL
                .Execute
            
                strSQL = "EXEC sp_adduser N'" & Trim$(txtUserId) & "', N'" & Trim$(txtUserId) & "', 'db_owner'"
                .CommandText = strSQL
                .Execute

                If Me.ckSLevel Then
                    strSQL = "EXEC sp_addsrvrolemember N'" & Trim$(txtUserId) & "', 'sysadmin'"
                    .CommandText = strSQL
                    .Execute
                End If
            
                Me.txtUserId.Enabled = False
                If g_UpdatePassword Then
                    Me.cmdResetPassword.Visible = True
                Else
                    Me.cmdResetPassword.Visible = False
                End If
                AIM_SAChangePassword.Show vbModeless
            Else
                If Me.ckSLevel = vbChecked Then
                    strSQL = "EXEC sp_addsrvrolemember N'" & Trim$(txtUserId) & "', 'sysadmin'"
                    .CommandText = strSQL
                    .Execute
                Else
                    strSQL = "EXEC sp_dropsrvrolemember N'" & Trim$(txtUserId) & "', 'sysadmin'"
                    .CommandText = strSQL
                    .Execute
                End If
            End If
        End With    'ExecSp
        If Not (ExecSp Is Nothing) Then Set ExecSp.ActiveConnection = Nothing
        Set ExecSp = Nothing
    End If
    
    If Trim$(rsAIMUsers("UserID").Value) = "" _
    And Trim$(Me.txtUserId.Text) = "" Then
        'RemoveBlankData
        Exit Function
    End If
    rsAIMUsers("UserID").Value = Me.txtUserId.Text
    rsAIMUsers("UserName").Value = Me.txtUserName.Text
    rsAIMUsers("UserInitials").Value = Me.txtUserInitials.Text
    rsAIMUsers("LangID").Value = Me.dcLangID.Text
    
    rsAIMUsers("RoleID").Value = Me.dcRole.Value
    
    rsAIMUsers("SLevel").Value = IIf(Me.ckSLevel = vbChecked, 1, 0)
    rsAIMUsers("SCArray").Value = SCArray

    rsAIMUsers("DmdUpdExcpts").Value = IIf(Me.ckDmdUpdExcpts = vbChecked, "Y", "N")
    rsAIMUsers("OnPromotionExcpts").Value = IIf(Me.ckOnPromotionExcpts = vbChecked, "Y", "N")
    rsAIMUsers("ExtCostExcpts").Value = IIf(Me.ckExtCostExcpts = vbChecked, "Y", "N")
    rsAIMUsers("ExtCostLimit").Value = Me.txtExtCostLimit.Value
    rsAIMUsers("PackSizeExcpts").Value = IIf(Me.ckPackSizeExcepts = vbChecked, "Y", "N")
    rsAIMUsers("PS_PctRoundLimit").Value = Me.txtPS_PctRoundLimit.Value
    rsAIMUsers("PS_ExtCostLimit").Value = Me.txtPS_ExtCostLimit.Value
    rsAIMUsers("LastWeekSaleExcpts").Value = IIf(Me.ckLastWkSaleExcpts = vbChecked, "Y", "N")
    rsAIMUsers("OverDuePO").Value = IIf(Me.ckOverDuePO = vbChecked, "Y", "N")
    rsAIMUsers("SafetyStockEroded").Value = IIf(Me.ckSafetyStockEroded = vbChecked, "Y", "N")
    rsAIMUsers("BackOrdered").Value = IIf(Me.ckBackOrdered = vbChecked, "Y", "N")
    rsAIMUsers("InvAvailRestriction").Value = IIf(Me.ckInvAvailRestriction = vbChecked, "Y", "N")
    rsAIMUsers("InvAvailList").Value = Me.txtInvAvailList.Text
    
    rsAIMUsers("AltSourceExcpts").Value = IIf(Me.ckAltSource = vbChecked, "Y", "N") 'Mohammed - MultiVendor
    rsAIMUsers("VendorCostExcpts").Value = IIf(Me.ckLVCostAlert.Value = vbChecked, "Y", "N")
    rsAIMUsers("POExtCost").Value = IIf(Me.ckPOExtCostLimit = vbChecked, "Y", "N")
    rsAIMUsers("POExtCostLimit").Value = Me.txtPOExtCostLimit.Value
    
    rsAIMUsers!LogErrors = IIf(Me.ckLogErrors = vbChecked, "Y", "N")
    
Exit Function
ErrorHandler:
    If Err.Number = -2147217900 Then
        'The login already exists.
        Resume Next
    Else
        If Not (ExecSp Is Nothing) Then Set ExecSp.ActiveConnection = Nothing
        Set ExecSp = Nothing
        'Err.Raise Err.Number, Err.source, Err.Description & "(f_AIMUsers_Update)"
        f_HandleErr , , , "AIM_UserMaintenance::f_AIMUsers_Update", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function UserInputValidation() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX06002")
    If StrComp(strMessage, "MSGBOX06002") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim$(txtUserId.Text) = "" _
    Or StrComp(txtUserId.Text, getTranslationResource("New")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    label(0).Caption  'User ID
        If txtUserId.Enabled = False Then
            txtUserId.Enabled = True
            txtUserId.SetFocus
        End If
        txtUserId.Enabled = True
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
     f_HandleErr , , , "AIM_UserMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function

Private Function RefreshUserLanguage()
'If the record to be updated is for the current user, Update Registry entries for Logon screen with this user's language.
On Error GoTo ErrorHandler
    
    Dim LangID As String
    Dim LocaleID As Integer
    Dim strSQL As String
    Dim rsTemp As ADODB.Recordset
    
    'Save the current Bookmark
    AIMUsersBookMark = rsAIMUsers.Bookmark
    rsAIMUsers.MoveFirst
    
    'Look up logged in user for changes to lang.
    rsAIMUsers.Find "UserID = '" & gUserID & "'"
    If StrComp(gLangID, rsAIMUsers!LangID, vbTextCompare) <> 0 Then
        strSQL = "SELECT DecimalValue FROM AIMLanguage " & _
                " WHERE AIMLanguage.LangID = N'" & rsAIMUsers!LangID & "'" & _
                " AND ENABLED = N'Y'"
                
        Set rsTemp = New ADODB.Recordset
        With rsTemp
            .CursorLocation = adUseClient
            .CursorType = adOpenStatic
            .LockType = adLockOptimistic
        End With
        
        rsTemp.Open strSQL, Cn
        
        If f_IsRecordsetOpenAndPopulated(rsTemp) Then
            'Store Current values of LangID and LocaleID before calling SaveLogon for the Updates.
            LangID = gLangID
            gLangID = rsAIMUsers!LangID
            LocaleID = gLocaleID
            gLocaleID = rsTemp!DecimalValue
            'Save the new information to the registry
            SaveLogonToRegistry
            'Restore the current langid
            gLangID = LangID
            gLocaleID = LocaleID
        End If
    End If
    
    'Restore bookmark
    rsAIMUsers.Bookmark = AIMUsersBookMark
    'End - Update Logon Registry for This User
    
    'Clean up
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
            
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(RefreshUserLanguage)"
     f_HandleErr , , , "AIM_UserMaintenance::RefreshUserLanguage", Now, gDRGeneralError, True, Err
End Function

Private Function PopulateLangIDs() As Long
On Error GoTo ErrorHandler

    Dim rsLangId As ADODB.Recordset
    Dim AIM_AIMLanguage_List_Sp As ADODB.Command
    Dim strYes As String

    'Build the Language List
    Set AIM_AIMLanguage_List_Sp = New ADODB.Command
    With AIM_AIMLanguage_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMLanguage_List_Sp"
        .Parameters.Refresh
    End With

    'Initialize the language Record Set
    Set rsLangId = New ADODB.Recordset
    With rsLangId
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Only get the enabled Languages, marked with "Y"
    strYes = "Y"
    AIM_AIMLanguage_List_Sp.Parameters("@EnabledOption").Value = strYes
    rsLangId.Open AIM_AIMLanguage_List_Sp
    
    'Bind the Data Combo Drop Downs
    If f_IsRecordsetOpenAndPopulated(rsLangId) Then
        rsLangId.MoveFirst
        Do Until rsLangId.eof
            Me.dcLangID.AddItem rsLangId("LangID").Value & vbTab & rsLangId!LangDesc
            rsLangId.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsLangId) Then rsLangId.Close
    Set rsLangId = Nothing
    If Not (AIM_AIMLanguage_List_Sp Is Nothing) Then Set AIM_AIMLanguage_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLanguage_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetOpenAndPopulated(rsLangId) Then rsLangId.Close
    Set rsLangId = Nothing
    If AIM_AIMLanguage_List_Sp.State <> adStateOpen Then Set AIM_AIMLanguage_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLanguage_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateLangIDs)"
    f_HandleErr , , , "AIM_UserMaintenance::PopulateLangIDs", Now, gDRGeneralError, True, Err
End Function
Private Function PopulateRoles() As Long
On Error GoTo ErrorHandler

    Dim rsRoleId As ADODB.Recordset
    Dim AIM_AIMRoles_List_Sp As ADODB.Command
    
    Set AIM_AIMRoles_List_Sp = New ADODB.Command
    With AIM_AIMRoles_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMRoles_List_Sp_ALL"
        .Parameters.Refresh
    End With

    'Initialize the language Record Set
    Set rsRoleId = New ADODB.Recordset
    With rsRoleId
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    rsRoleId.Open AIM_AIMRoles_List_Sp
    Me.dcRole.Columns(2).Visible = True
    Me.dcRole.Columns(2).Alignment = ssCaptionAlignmentLeft
    
    If f_IsRecordsetOpenAndPopulated(rsRoleId) Then
        rsRoleId.MoveFirst
        Me.dcRole.Text = rsRoleId("RoleDescription")
        Do Until rsRoleId.eof
            Me.dcRole.AddItem rsRoleId("RoleID").Value & vbTab & rsRoleId("RoleDescription").Value & vbTab & CStr(rsRoleId("RoleLevel").Value)
            rsRoleId.MoveNext
        Loop
    End If
    rsRoleId.Close
    
    If f_IsRecordsetValidAndOpen(rsRoleId) Then rsRoleId.Close
    Set rsRoleId = Nothing
    Set AIM_AIMRoles_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetOpenAndPopulated(rsRoleId) Then rsRoleId.Close
    Set rsRoleId = Nothing
    If AIM_AIMRoles_List_Sp.State <> adStateOpen Then Set AIM_AIMRoles_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMRoles_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateRoles)"
    f_HandleErr , , , "AIM_UserMaintenance::PopulateRoles", Now, gDRGeneralError, True, Err
End Function

Private Sub txtUserID_Change()
On Error GoTo ErrorHandler
    If UserID = False Then
        If UCase$(Me.txtUserId) = "SA" Then
            Me.txtUserId.Enabled = False
            Me.cmdResetPassword.Visible = False
            Me.tbNavigation.Tools("ID_Save").Enabled = False
            Me.tbNavigation.Tools("ID_Delete").Enabled = False
            Exit Sub
        End If
        'Me.txtUserID.Enabled = True
        If txtUserId.Enabled Then
            Me.txtUserId.Enabled = True
        Else
            Me.txtUserId.Enabled = False
        End If
        If g_UpdatePassword Then
            Me.cmdResetPassword.Visible = True
        Else
            Me.cmdResetPassword.Visible = False
        End If
        Me.tbNavigation.Tools("ID_Save").Enabled = True
        Me.tbNavigation.Tools("ID_Delete").Enabled = True
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtUserID_Change)"
     f_HandleErr , , , "AIM_UserMaintenance::txtUserID_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtUserID_Click()
On Error GoTo ErrorHandler
    UserID = True
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtUserID_Click)"
     f_HandleErr , , , "AIM_UserMaintenance::txtUserID_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtUserID_KeyPress(KeyAscii As Integer)
On Error GoTo ErrorHandler
    UserID = False
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtUserID_KeyPress)"
     f_HandleErr , , , "AIM_UserMaintenance::txtUserID_KeyPress", Now, gDRGeneralError, True, Err
End Sub

Public Function RemoveBlankData()
On Error GoTo ErrorHandler
    
    Dim ExecBlankData As ADODB.Command
    Dim strSQL As String
    
    Set ExecBlankData = New ADODB.Command
    
    With ExecBlankData
        Set .ActiveConnection = Cn
        .CommandType = adCmdText
        
        strSQL = "DELETE FROM AIMUSERS WHERE LTrim(RTrim(UserID))=''"
        .CommandText = strSQL
        .Execute
    End With
    
    If Not (ExecBlankData Is Nothing) Then Set ExecBlankData.ActiveConnection = Nothing
    Set ExecBlankData = Nothing
    
Exit Function
ErrorHandler:
    If Not (ExecBlankData Is Nothing) Then Set ExecBlankData.ActiveConnection = Nothing
    Set ExecBlankData = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateRoles)"
    f_HandleErr , , , "AIM_UserMaintenance::RemoveBlankData", Now, gDRGeneralError, True, Err
End Function

