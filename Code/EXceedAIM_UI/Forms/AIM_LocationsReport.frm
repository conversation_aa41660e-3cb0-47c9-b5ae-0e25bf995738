VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_LocationsReport 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Locations Report"
   ClientHeight    =   2505
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   4875
   Icon            =   "AIM_LocationsReport.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2505
   ScaleWidth      =   4875
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   120
      Top             =   2040
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_LocationsReport.frx":030A
      ToolBars        =   "AIM_LocationsReport.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   1980
      Left            =   62
      TabIndex        =   1
      Top             =   36
      Width           =   4755
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMLocations 
         Bindings        =   "AIM_LocationsReport.frx":3733
         Height          =   345
         Left            =   2745
         TabIndex        =   0
         Top             =   255
         Width           =   1860
         DataFieldList   =   "LcId"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3281
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "LcId"
      End
      Begin VB.Label Label17 
         Caption         =   "Location ID"
         Height          =   300
         Left            =   195
         TabIndex        =   2
         Top             =   300
         Width           =   2445
      End
   End
End
Attribute VB_Name = "AIM_LocationsReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Private Function BldSQL(LcId As String)
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    
    SqlStmt = "SELECT * " & _
            "FROM AIMLocations "
    
    LcId = Trim(LcId)
    
    'Build the Where Clause
    If LcId <> "" _
    And LcId <> getTranslationResource("All") Then
        WhrStmt = "WHERE AIMLocations.LcId = N'" & LcId & "' "
    End If
    
    'Append Where Clause
    SqlStmt = SqlStmt & vbCrLf & WhrStmt
    
    'Append Order by Clause
    SqlStmt = SqlStmt & vbCrLf & "ORDER BY AIMLocations.LcId "
    
    BldSQL = SqlStmt

Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_LocationsReport::BldSQL", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim Msg As String
    Dim Report As AIM_Locations_Report
    Dim SqlStmt As String
    Dim strMessage As String
    Dim rsReport As ADODB.Recordset

    Select Case Tool.ID
    Case "ID_Print", "ID_Preview"
        'Housekeeping
        Screen.MousePointer = vbHourglass
        strMessage = getTranslationResource("STATMSG03300")
        If StrComp(strMessage, "STATMSG03300") = 0 Then strMessage = "Building the Locations Report..."
        Write_Message strMessage
        
        'Build SQL Statement
        SqlStmt = BldSQL(Me.dcAIMLocations.Text)
        
        'Open the report result set
        Set rsReport = New ADODB.Recordset
        rsReport.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
        
        Write_Message ""
        Screen.MousePointer = vbNormal
        
        If Not f_IsRecordsetOpenAndPopulated(rsReport) Then
            If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
            Set rsReport = Nothing
            strMessage = getTranslationResource("TEXTMSG05103")
            If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
            Write_Message strMessage
            Exit Sub
        End If
        
        'Instance the Report
        Set Report = New AIM_Locations_Report
        Set Report.dcAIMLocations.Recordset = rsReport
        
        Select Case Tool.ID
        Case "ID_Print"
            Report.PrintReport True
            
            If Err.Number <> 0 Then
                Msg = Format(Err.Number, "0") & ":" & Err.Description
                MsgBox Msg
            End If
    
        Case "ID_Preview"
            Set AIM_Reports.ARViewer.ReportSource = Report
            AIM_Reports.Show vbModal, AIM_Main
    
        End Select

    Case "ID_Close"
        Unload Me
        Exit Sub
    
    End Select
    
    'Wrap Up
    Set Report = Nothing
    Set AIM_Locations_Report = Nothing
    
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing

    
Exit Sub
ErrorHandler:
    Set Report = Nothing
    Set AIM_Locations_Report = Nothing
    
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing

    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
    f_HandleErr , , , "AIM_LocationsReport::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMLocations_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMLocations.Columns(0).Name = "lcid"
    Me.dcAIMLocations.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcAIMLocations.Columns(0).Width = 1000
    Me.dcAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(0).DataField = "lcid"
    
    Me.dcAIMLocations.Columns(1).Name = "lname"
    Me.dcAIMLocations.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcAIMLocations.Columns(1).Width = 2880
    Me.dcAIMLocations.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMLocations.Columns(1).DataField = "lname"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMLocations, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMLocations.Columns.Count - 1
'        dcAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMLocations_InitColumnProps)"
     f_HandleErr , , , "AIM_LocationsReport::dcAIMLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_LocationsReport::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    Dim rsAIMLocations As ADODB.Recordset

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03301")
    If StrComp(strMessage, "STATMSG03301") = 0 Then strMessage = "Initializing Locations Report..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub

    'Build/Bind the AIM Locations Drop Down
    Me.dcAIMLocations.AddItem getTranslationResource("All") & vbTab & _
                        getTranslationResource("All Locations")
    'Fetch from database
    SqlStmt = "SELECT LcID, LName FROM AIMLocations ORDER BY LcID "
    Set rsAIMLocations = New ADODB.Recordset
    rsAIMLocations.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        rsAIMLocations.MoveFirst
        Do Until rsAIMLocations.eof
            Me.dcAIMLocations.AddItem rsAIMLocations("LcId").Value & vbTab & _
                rsAIMLocations("LName").Value
            rsAIMLocations.MoveNext
            
        Loop
    End If
    
    'Initialize Drop Down
    Me.dcAIMLocations.Text = getTranslationResource("All")
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    
Exit Sub
ErrorHandler:
    Write_Message ""
    Screen.MousePointer = vbNormal
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_LocationsReport::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_LocationsReport::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
