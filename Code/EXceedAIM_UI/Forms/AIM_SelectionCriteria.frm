VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_SelectionCriteria 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Advanced Selection"
   ClientHeight    =   6225
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10890
   Icon            =   "AIM_SelectionCriteria.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   6225
   ScaleWidth      =   10890
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdClose 
      Caption         =   "&Close"
      Height          =   345
      Left            =   5760
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   5760
      Width           =   1635
   End
   Begin VB.CommandButton cmdView 
      Caption         =   "&View"
      Height          =   345
      Left            =   7560
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   5760
      Width           =   1635
   End
   Begin VB.CommandButton cmdTest 
      Caption         =   "&Test"
      Default         =   -1  'True
      Height          =   345
      Left            =   9240
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   5760
      Width           =   1635
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Clea&r"
      Height          =   345
      Left            =   4080
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   5760
      Width           =   1635
   End
   Begin VB.CommandButton cmdDelete 
      Caption         =   "&Delete"
      Height          =   345
      Left            =   2400
      Style           =   1  'Graphical
      TabIndex        =   8
      Top             =   5760
      Width           =   1635
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddColumns 
      Height          =   885
      Left            =   3150
      TabIndex        =   3
      Top             =   1230
      Width           =   2055
      DataFieldList   =   "column_name"
      ListAutoValidate=   0   'False
      _Version        =   196617
      DataMode        =   2
      Cols            =   2
      ColumnHeaders   =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      _ExtentX        =   3625
      _ExtentY        =   1561
      _StockProps     =   77
      DataFieldToDisplay=   "column_name"
   End
   Begin TDBText6Ctl.TDBText txtSQL 
      Height          =   1815
      Left            =   0
      TabIndex        =   2
      TabStop         =   0   'False
      Top             =   3720
      Width           =   10875
      _Version        =   65536
      _ExtentX        =   19182
      _ExtentY        =   3201
      Caption         =   "AIM_SelectionCriteria.frx":030A
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_SelectionCriteria.frx":0376
      Key             =   "AIM_SelectionCriteria.frx":0394
      BackColor       =   -2147483643
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   -1
      ShowContextMenu =   -1
      MarginLeft      =   1
      MarginRight     =   1
      MarginTop       =   1
      MarginBottom    =   1
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   -1
      ScrollBars      =   2
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   0
      LengthAsByte    =   0
      Text            =   ""
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   1
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgSelect 
      Height          =   3675
      Left            =   0
      TabIndex        =   0
      Top             =   0
      Width           =   8985
      _Version        =   196617
      DataMode        =   1
      Cols            =   7
      AllowAddNew     =   -1  'True
      AllowDelete     =   -1  'True
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   15849
      _ExtentY        =   6482
      _StockProps     =   79
      Caption         =   "Selection Criteria"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgTables 
      Height          =   3675
      Left            =   9030
      TabIndex        =   1
      Top             =   0
      Width           =   1845
      ScrollBars      =   2
      _Version        =   196617
      DataMode        =   2
      RecordSelectors =   0   'False
      ColumnHeaders   =   0   'False
      Col.Count       =   0
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      TabNavigation   =   1
      _ExtentX        =   3254
      _ExtentY        =   6482
      _StockProps     =   79
      Caption         =   "Tables"
   End
End
Attribute VB_Name = "AIM_SelectionCriteria"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection
Public p_SQLStmt As SQLBuilder        'SQLStmt.cls, defined as a Common File

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler

    'Force the grid to update
    Me.p_SQLStmt.Criterias.Clear
    Me.dgSelect.Update
    Me.dgSelect.ReBind
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_SelectionCriteria:::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClose_Click()
On Error GoTo ErrorHandler

    'Force the grid to update
    Me.dgSelect.Update
    
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClose_Click)"
     f_HandleErr , , , "AIM_SelectionCriteria:::cmdClose_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdDelete_Click()
On Error GoTo ErrorHandler

    'Remove selected row
    Me.dgSelect.DeleteSelected
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdDelete_Click)"
     f_HandleErr , , , "AIM_SelectionCriteria:::cmdDelete_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdTest_Click()
On Error GoTo ErrorHandler

    Dim rsResults As ADODB.Recordset
    
    Dim i As Integer
    Dim strMessage As String
    Dim strMessage1 As String
    Dim SQL As String
    
    'Housekeeping
    Me.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05100")
    If StrComp(strMessage, "STATMSG05100") = 0 Then strMessage = "Executing SQL Statment ..."
    Write_Message strMessage
    
    'Force the grid to update
    Me.dgSelect.Update
    
    'Set up error handling
    Cn.Errors.Clear
    
    'Concatenate sql string from various elements
    With p_SQLStmt
        SQL = "SELECT COUNT(*) "
        
        strMessage = Trim(.FromExpr)
        SQL = IIf(Trim(strMessage) = "", SQL, SQL + vbCrLf + strMessage)
        strMessage = ""
        
        strMessage = Trim(.JoinExpr)
        SQL = IIf(Trim(strMessage) = "", SQL, SQL + vbCrLf + strMessage)
        strMessage = ""
        
        strMessage = Trim(.WhrExpr)
        SQL = IIf(strMessage = "", SQL, SQL + vbCrLf + strMessage)
        strMessage = ""
    End With
    
    'Fetch into recordset
    Set rsResults = New ADODB.Recordset
    rsResults.Open SQL, Cn, adOpenForwardOnly, adLockReadOnly
    
    'Check for errors
    If Cn.Errors.Count > 0 Then
        Beep
        strMessage = getTranslationResource("TEXTMSG05100")
        If StrComp(strMessage, "TEXTMSG05100") = 0 Then strMessage = "Error(s) executing SQL Statement."
        strMessage = strMessage + vbCrLf + vbCrLf
        For i = 0 To Cn.Errors.Count - 1
            strMessage = strMessage + Cn.Errors(i).Description + vbCrLf + vbCrLf
        Next i
    Else
        strMessage = getTranslationResource("TEXTMSG05101")
        If StrComp(strMessage, "TEXTMSG05101") = 0 Then strMessage = "SQL Statement executed successfully."
        strMessage = strMessage + vbCrLf + vbCrLf
        If Not (rsResults.BOF And rsResults.eof) Then
            strMessage1 = getTranslationResource("TEXTMSG05102")
            If StrComp(strMessage1, "TEXTMSG05102") = 0 Then strMessage1 = "Records Returned:"
            strMessage = strMessage + strMessage1 + " " + Format(rsResults(0).Value, "#,##0")
        Else
            strMessage1 = getTranslationResource("TEXTMSG05103")
            If StrComp(strMessage1, "TEXTMSG05103") = 0 Then strMessage1 = "No records returned."
            strMessage = strMessage + strMessage1
        End If
    End If
    
    'Display status/message
    Me.txtSQL.Text = strMessage
    Me.dgSelect.ReBind
    
    'Clean Up
    Write_Message ""
    Me.MousePointer = vbNormal
    If f_IsRecordsetValidAndOpen(rsResults) Then rsResults.Close
    Set rsResults = Nothing
    
Exit Sub
ErrorHandler:
    Write_Message ""
    Me.MousePointer = vbNormal
    If f_IsRecordsetValidAndOpen(rsResults) Then rsResults.Close
    Set rsResults = Nothing
    'f_HandleErr Me.Caption & "(cmdTest_Click)"
     f_HandleErr , , , "AIM_SelectionCriteria:::cmdTest_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdView_Click()
On Error GoTo ErrorHandler

    'Force the grid to update
    Me.dgSelect.Update
    
    'Display the SQL Statement
    Me.txtSQL = p_SQLStmt.SQLExpr
    
    Me.dgSelect.ReBind
    
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdView_Click)"
     f_HandleErr , , , "AIM_SelectionCriteria:::cmdView_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub ddColumns_CloseUp()
On Error GoTo ErrorHandler

    Select Case Me.ddColumns.Columns("numeric").Value
        Case "N"            'Non-numeric
            Me.dgSelect.Columns("numeric").Value = False
        Case Else           'numeric
            Me.dgSelect.Columns("numeric").Value = True
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddColumns_CloseUp)"
     f_HandleErr , , , "AIM_SelectionCriteria:::ddColumns_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub ddColumns_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.ddColumns.Columns(0).Name = "column_name"
    Me.ddColumns.Columns(0).Caption = ""
    Me.ddColumns.Columns(0).Width = 2880
    Me.ddColumns.Columns(0).DataField = "column_name"
    
    Me.ddColumns.Columns(1).Name = "numeric"
    Me.ddColumns.Columns(1).Caption = ""
    Me.ddColumns.Columns(1).Width = 400
    Me.ddColumns.Columns(1).DataField = "numeric"
    Me.ddColumns.Columns(1).Alignment = ssCaptionAlignmentCenter
    Me.ddColumns.Columns(1).Visible = False
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths ddColumns, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To ddColumns.Columns.Count - 1
'        ddColumns.Columns(IndexCounter).HasHeadBackColor = True
'        ddColumns.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'   Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddColumns_InitColumnProps)"
     f_HandleErr , , , "AIM_SelectionCriteria:::ddColumns_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim strText As String
    Dim strMessage As String
    
    strText = getTranslationResource(AIM_SelectionCriteria.Caption)
    
    'Validate
    If Me.dgSelect.Columns("column_name").Value = "" Then
        strMessage = getTranslationResource("MSGBOX05100")
        If StrComp(strMessage, "MSGBOX05100") = 0 Then strMessage = "Column Name required."
        MsgBox strMessage, vbCritical + vbOKOnly, strText
        Cancel = True
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSelect_BeforeUpdate)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgSelect.Columns(0).Name = "qualifier"
    Me.dgSelect.Columns(0).Caption = getTranslationResource("And/Or")
    Me.dgSelect.Columns(0).Width = 1000
    Me.dgSelect.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dgSelect.Columns(0).Style = ssStyleComboBox
    Me.dgSelect.Columns(0).Locked = True
    
    Me.dgSelect.Columns(0).AddItem "and"
    Me.dgSelect.Columns(0).AddItem "or"
    
    Me.dgSelect.Columns(1).Name = "column_name"
    Me.dgSelect.Columns(1).Caption = getTranslationResource("Column Name")
    Me.dgSelect.Columns(1).Width = 2880
    Me.dgSelect.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgSelect.Columns(1).Style = ssStyleEdit
    Me.dgSelect.Columns(1).DataType = vbString
    Me.dgSelect.Columns(1).FieldLen = 30
    Me.dgSelect.Columns(1).DataField = "column_name"
    Me.dgSelect.Columns(1).DropDownHwnd = Me.ddColumns.hwnd
    Me.dgSelect.Columns(1).Locked = True
    
    Me.dgSelect.Columns(2).Name = "numeric"
    Me.dgSelect.Columns(2).Caption = getTranslationResource("Numeric")
    Me.dgSelect.Columns(2).Width = 800
    Me.dgSelect.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(2).Alignment = ssCaptionAlignmentCenter
    Me.dgSelect.Columns(2).Style = ssStyleCheckBox
    Me.dgSelect.Columns(2).Visible = False
    Me.dgSelect.Columns(2).Locked = True
    
    Me.dgSelect.Columns(3).Name = "notopt"
    Me.dgSelect.Columns(3).Caption = getTranslationResource("Not")
    Me.dgSelect.Columns(3).Width = 400
    Me.dgSelect.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(3).Alignment = ssCaptionAlignmentCenter
    Me.dgSelect.Columns(3).Style = ssStyleCheckBox
    
    Me.dgSelect.Columns(4).Name = "CompOper"
    Me.dgSelect.Columns(4).Caption = getTranslationResource("Operator")
    Me.dgSelect.Columns(4).Width = 1200
    Me.dgSelect.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(4).Alignment = ssCaptionAlignmentCenter
    Me.dgSelect.Columns(4).Style = ssStyleComboBox
    Me.dgSelect.Columns(4).Locked = True
    
    Me.dgSelect.Columns(4).AddItem "="
    Me.dgSelect.Columns(4).AddItem ">"
    Me.dgSelect.Columns(4).AddItem "<"
    Me.dgSelect.Columns(4).AddItem ">="
    Me.dgSelect.Columns(4).AddItem "<="
    Me.dgSelect.Columns(4).AddItem "between"
    Me.dgSelect.Columns(4).AddItem "in"
    Me.dgSelect.Columns(4).AddItem "like"
   
    Me.dgSelect.Columns(5).Name = "compvalue1"
    Me.dgSelect.Columns(5).Caption = getTranslationResource("Value 1")
    Me.dgSelect.Columns(5).Width = 1440
    Me.dgSelect.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(5).Alignment = ssCaptionAlignmentLeft
    Me.dgSelect.Columns(5).Style = ssStyleEdit
    Me.dgSelect.Columns(5).DataType = vbString
    Me.dgSelect.Columns(5).FieldLen = 30
    
    Me.dgSelect.Columns(6).Name = "compvalue2"
    Me.dgSelect.Columns(6).Caption = getTranslationResource("Value 2")
    Me.dgSelect.Columns(6).Width = 1440
    Me.dgSelect.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgSelect.Columns(6).Alignment = ssCaptionAlignmentLeft
    Me.dgSelect.Columns(6).Style = ssStyleEdit
    Me.dgSelect.Columns(6).DataType = vbString
    Me.dgSelect.Columns(6).FieldLen = 30
   
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgSelect, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgSelect.Columns.Count - 1
'        dgSelect.Columns(IndexCounter).HasHeadBackColor = True
'        dgSelect.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        dgSelect.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSelect_InitColumnProps)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    If Me.dgSelect.IsAddRow = True Then
        'Initialize row values
        If Me.dgSelect.Rows = 0 And Me.dgSelect.Columns("qualifier").Value = "" Then
            Me.dgSelect.Columns("qualifier").Value = "WHERE"
        ElseIf Me.dgSelect.Columns("qualifier").Value = "" Then
            Me.dgSelect.Columns("qualifier").Value = "AND"
        End If
        
        If Me.dgSelect.Columns("CompOper").Value = "" Then
            Me.dgSelect.Columns("CompOper").Value = "="
        End If
    
        'If this is an add, drop the dropdown columns
        Select Case Me.dgSelect.Col
            Case 0, 1, 4
                Me.dgSelect.DroppedDown = True
            Case Else
                Me.dgSelect.DroppedDown = False
        End Select
    
    Else
        'Check for an invalid qualifier
        If Me.dgSelect.Row = 0 Then
            Me.dgSelect.Columns("qualifier").Value = "WHERE"
        End If
    
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSelect_RowColChange)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    'Initialize collection
    Dim sCriteria As Criteria
    Set sCriteria = New Criteria
    
    'Set values for new item
    If Not IsNull(RowBuf.Value(0, 0)) Then sCriteria.Qualifier = UCase(RowBuf.Value(0, 0))
    If Not IsNull(RowBuf.Value(0, 1)) Then sCriteria.ColName = RowBuf.Value(0, 1)
    If Not IsNull(RowBuf.Value(0, 2)) Then sCriteria.Numeric = RowBuf.Value(0, 2)
    If Not IsNull(RowBuf.Value(0, 3)) Then sCriteria.NotOpt = RowBuf.Value(0, 3)
    If Not IsNull(RowBuf.Value(0, 4)) Then sCriteria.CompOper = RowBuf.Value(0, 4)
    If Not IsNull(RowBuf.Value(0, 5)) Then sCriteria.CompValue1 = RowBuf.Value(0, 5)
    If Not IsNull(RowBuf.Value(0, 6)) Then sCriteria.CompValue2 = RowBuf.Value(0, 6)
    
    'Add to collection
    p_SQLStmt.Criterias.Add sCriteria.Qualifier, _
                            sCriteria.ColName, _
                            sCriteria.Numeric, _
                            sCriteria.NotOpt, _
                            sCriteria.CompOper, _
                            sCriteria.CompValue1, _
                            sCriteria.CompValue2
    'Reset grid's bookmark
    NewRowBookmark = p_SQLStmt.Criterias.Count

    Set sCriteria = Nothing
    
Exit Sub
ErrorHandler:
    Set sCriteria = Nothing
    'f_HandleErr Me.Caption & "(dgSelect_UnboundAddData)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_UnboundAddData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler

    Me.p_SQLStmt.Criterias.Delete (Bookmark)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSelect_UnboundDeleteRow)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim CurrentRow As Integer
    Dim RowCounter As Integer
    Dim RowIndex As Integer
    Dim CollectionCount As Integer
    
    CollectionCount = p_SQLStmt.Criterias.Count
    If CollectionCount = 0 Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            CurrentRow = CollectionCount
        Else
            CurrentRow = 1
        End If
    Else
        CurrentRow = StartLocation
        If ReadPriorRows Then
            CurrentRow = CurrentRow - 1
        Else
            CurrentRow = CurrentRow + 1
        End If
    End If
    
    For RowIndex = 0 To RowBuf.RowCount - 1
        If CurrentRow < 0 _
        Or CurrentRow > CollectionCount _
        Then
            Exit For
        End If
        
        RowBuf.Value(RowIndex, 0) = p_SQLStmt.Criterias(CurrentRow).Qualifier
        RowBuf.Value(RowIndex, 1) = p_SQLStmt.Criterias(CurrentRow).ColName
        RowBuf.Value(RowIndex, 2) = p_SQLStmt.Criterias(CurrentRow).Numeric
        RowBuf.Value(RowIndex, 3) = p_SQLStmt.Criterias(CurrentRow).NotOpt
        RowBuf.Value(RowIndex, 4) = p_SQLStmt.Criterias(CurrentRow).CompOper
        RowBuf.Value(RowIndex, 5) = p_SQLStmt.Criterias(CurrentRow).CompValue1
        RowBuf.Value(RowIndex, 6) = p_SQLStmt.Criterias(CurrentRow).CompValue2

        RowBuf.Bookmark(RowIndex) = CurrentRow
    
        If ReadPriorRows Then
            CurrentRow = CurrentRow - 1
        Else
            CurrentRow = CurrentRow + 1
        End If
    
        RowCounter = RowCounter + 1
    Next RowIndex
    
    RowBuf.RowCount = RowCounter

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSelect_UnboundReadData)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgSelect_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim CurrentRow As Integer
    
    If IsNull(WriteLocation) Then Exit Sub

    CurrentRow = WriteLocation

    If Not IsNull(RowBuf.Value(0, 0)) _
    And (Trim(RowBuf.Value(0, 0)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).Qualifier = UCase(RowBuf.Value(0, 0))
    
    If Not IsNull(RowBuf.Value(0, 1)) _
    And (Trim(RowBuf.Value(0, 1)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).ColName = RowBuf.Value(0, 1)
    
    If Not IsNull(RowBuf.Value(0, 2)) _
    And (Trim(RowBuf.Value(0, 2)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).NotOpt = RowBuf.Value(0, 2)
    
    If Not IsNull(RowBuf.Value(0, 3)) _
    And (Trim(RowBuf.Value(0, 3)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).NotOpt = RowBuf.Value(0, 3)
    
    If Not IsNull(RowBuf.Value(0, 4)) _
    And (Trim(RowBuf.Value(0, 4)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).CompOper = RowBuf.Value(0, 4)
    
    If Not IsNull(RowBuf.Value(0, 5)) _
    And (Trim(RowBuf.Value(0, 5)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).CompValue1 = RowBuf.Value(0, 5)
    
    If Not IsNull(RowBuf.Value(0, 6)) _
    And (Trim(RowBuf.Value(0, 6)) <> "") Then _
        p_SQLStmt.Criterias(CurrentRow).CompValue1 = RowBuf.Value(0, 6)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgSelect_UnboundWriteData)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgSelect_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgTables_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgTables.Columns(0).Name = "Table"
    Me.dgTables.Columns(0).Width = 1440

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgTables, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgTables.Columns.Count - 1
'        dgTables.Columns(IndexCounter).HasHeadBackColor = True
'        dgTables.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        dgTables.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_READONLY)
    Next
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgTables_InitColumnProps)"
     f_HandleErr , , , "AIM_SelectionCriteria:::dgTables_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim sTable As clsTable
    Dim AddItem As String
    Dim strMessage As String
    Dim rsColumns As ADODB.Recordset

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05101")
    If StrComp(strMessage, "STATMSG05101") = 0 Then strMessage = "Initializing Selection Criteria..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    Set rsColumns = New ADODB.Recordset
    
    'Build the Tables Dropdown
    For Each sTable In Me.p_SQLStmt.SQLTables
        Me.dgTables.AddItem sTable.TableName
        
        'Build the Columns Dropdown
        Set rsColumns = Cn.OpenSchema(adSchemaColumns, Array(Empty, Empty, sTable.TableName, Empty))
        If f_IsRecordsetOpenAndPopulated(rsColumns) Then
            Do Until rsColumns.eof
                'Get Field information
                AddItem = rsColumns("table_name").Value + "." + rsColumns("column_name").Value _
                    + vbTab + IIf(IsNull(rsColumns("numeric_precision").Value), "N", "Y")
                'Add to dropdown
                Me.ddColumns.AddItem AddItem
                'Get next field
                rsColumns.MoveNext
            Loop
        End If
        
        'Clear current column set
        If f_IsRecordsetValidAndOpen(rsColumns) Then rsColumns.Close
    Next sTable
    
    'Wind Up
    Screen.MousePointer = vbNormal
    Write_Message ""
    If f_IsRecordsetValidAndOpen(rsColumns) Then rsColumns.Close
    Set rsColumns = Nothing
    
Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    Write_Message ""
    If f_IsRecordsetValidAndOpen(rsColumns) Then rsColumns.Close
    Set rsColumns = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SelectionCriteria:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    Set p_SQLStmt = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_SelectionCriteria:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
