VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Object = "{0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0"; "olch2x8.ocx"
Begin VB.Form AIM_SeasonalityProfiles 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Seasonality Profiles Maintenance"
   ClientHeight    =   6330
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   12345
   Icon            =   "AIM_SeasonalityProfiles.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6330
   ScaleWidth      =   12345
   ShowInTaskbar   =   0   'False
   Begin ActiveTabs.SSActiveTabs atSeasons 
      Height          =   4830
      Left            =   0
      TabIndex        =   2
      Top             =   1155
      Width           =   12255
      _ExtentX        =   21616
      _ExtentY        =   8520
      _Version        =   131083
      TabCount        =   2
      TagVariant      =   ""
      Tabs            =   "AIM_SeasonalityProfiles.frx":030A
      Begin ActiveTabs.SSActiveTabPanel atpGraph 
         Height          =   4440
         Left            =   30
         TabIndex        =   118
         Top             =   360
         Width           =   12195
         _ExtentX        =   21511
         _ExtentY        =   7832
         _Version        =   131083
         TabGuid         =   "AIM_SeasonalityProfiles.frx":0397
         Begin VB.Frame Frame3 
            Height          =   4335
            Left            =   60
            TabIndex        =   120
            Top             =   40
            Width           =   12070
            Begin C1Chart2D8.Chart2D ctSeasons 
               Height          =   4005
               Left            =   195
               TabIndex        =   121
               Top             =   225
               Width           =   11640
               _Version        =   524288
               _Revision       =   7
               _ExtentX        =   20532
               _ExtentY        =   7064
               _StockProps     =   0
               ControlProperties=   "AIM_SeasonalityProfiles.frx":03BF
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel atpGeneral 
         Height          =   4440
         Left            =   30
         TabIndex        =   117
         Top             =   360
         Width           =   12195
         _ExtentX        =   21511
         _ExtentY        =   7832
         _Version        =   131083
         TabGuid         =   "AIM_SeasonalityProfiles.frx":0A6F
         Begin VB.Frame Frame2 
            Height          =   4335
            Left            =   40
            TabIndex        =   119
            Top             =   60
            Width           =   12070
            Begin VB.CheckBox ckSALevel 
               Caption         =   "Summary Profile"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   9120
               TabIndex        =   6
               Top             =   720
               Width           =   2730
            End
            Begin TDBText6Ctl.TDBText txtSaDesc 
               Height          =   345
               Left            =   2760
               TabIndex        =   3
               Top             =   225
               Width           =   8985
               _Version        =   65536
               _ExtentX        =   15849
               _ExtentY        =   609
               Caption         =   "AIM_SeasonalityProfiles.frx":0A97
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":0B03
               Key             =   "AIM_SeasonalityProfiles.frx":0B21
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   50
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   -1
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin TDBText6Ctl.TDBText txtLcId 
               Height          =   345
               Left            =   2750
               TabIndex        =   4
               Top             =   585
               Width           =   2310
               _Version        =   65536
               _ExtentX        =   4075
               _ExtentY        =   609
               Caption         =   "AIM_SeasonalityProfiles.frx":0B65
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":0BD1
               Key             =   "AIM_SeasonalityProfiles.frx":0BEF
               BackColor       =   -2147483643
               EditMode        =   0
               ForeColor       =   -2147483640
               ReadOnly        =   0
               ShowContextMenu =   -1
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MarginBottom    =   3
               Enabled         =   -1
               MousePointer    =   0
               Appearance      =   1
               BorderStyle     =   1
               AlignHorizontal =   0
               AlignVertical   =   0
               MultiLine       =   0
               ScrollBars      =   0
               PasswordChar    =   ""
               AllowSpace      =   -1
               Format          =   ""
               FormatMode      =   1
               AutoConvert     =   -1
               ErrorBeep       =   0
               MaxLength       =   12
               LengthAsByte    =   0
               Text            =   ""
               Furigana        =   0
               HighlightText   =   -1
               IMEMode         =   0
               IMEStatus       =   0
               DropWndWidth    =   0
               DropWndHeight   =   0
               ScrollBarMode   =   0
               MoveOnLRKey     =   0
               OLEDragMode     =   0
               OLEDropMode     =   0
            End
            Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcClass 
               Bindings        =   "AIM_SeasonalityProfiles.frx":0C33
               DataField       =   " "
               Height          =   345
               Left            =   2750
               TabIndex        =   5
               Top             =   950
               Width           =   2310
               DataFieldList   =   "Column 0"
               AllowInput      =   0   'False
               AllowNull       =   0   'False
               _Version        =   196617
               DataMode        =   2
               Cols            =   3
               ForeColorEven   =   0
               BackColorOdd    =   12648447
               Columns(0).Width=   3200
               _ExtentX        =   4075
               _ExtentY        =   609
               _StockProps     =   93
               ForeColor       =   -2147483640
               BackColor       =   -2147483643
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   1
               Left            =   720
               TabIndex        =   7
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":0C3E
               Caption         =   "AIM_SeasonalityProfiles.frx":0C5E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":0CCA
               Keys            =   "AIM_SeasonalityProfiles.frx":0CE8
               Spin            =   "AIM_SeasonalityProfiles.frx":0D32
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   6750213
               MinValueVT      =   3538949
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   2
               Left            =   720
               TabIndex        =   8
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":0D5A
               Caption         =   "AIM_SeasonalityProfiles.frx":0D7A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":0DE6
               Keys            =   "AIM_SeasonalityProfiles.frx":0E04
               Spin            =   "AIM_SeasonalityProfiles.frx":0E4E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   1479671813
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   3
               Left            =   720
               TabIndex        =   9
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":0E76
               Caption         =   "AIM_SeasonalityProfiles.frx":0E96
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":0F02
               Keys            =   "AIM_SeasonalityProfiles.frx":0F20
               Spin            =   "AIM_SeasonalityProfiles.frx":0F6A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   4
               Left            =   720
               TabIndex        =   10
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":0F92
               Caption         =   "AIM_SeasonalityProfiles.frx":0FB2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":101E
               Keys            =   "AIM_SeasonalityProfiles.frx":103C
               Spin            =   "AIM_SeasonalityProfiles.frx":1086
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   5
               Left            =   720
               TabIndex        =   11
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":10AE
               Caption         =   "AIM_SeasonalityProfiles.frx":10CE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":113A
               Keys            =   "AIM_SeasonalityProfiles.frx":1158
               Spin            =   "AIM_SeasonalityProfiles.frx":11A2
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   6
               Left            =   720
               TabIndex        =   12
               Top             =   3105
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":11CA
               Caption         =   "AIM_SeasonalityProfiles.frx":11EA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1256
               Keys            =   "AIM_SeasonalityProfiles.frx":1274
               Spin            =   "AIM_SeasonalityProfiles.frx":12BE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   7
               Left            =   720
               TabIndex        =   13
               Top             =   3465
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":12E6
               Caption         =   "AIM_SeasonalityProfiles.frx":1306
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1372
               Keys            =   "AIM_SeasonalityProfiles.frx":1390
               Spin            =   "AIM_SeasonalityProfiles.frx":13DA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   8
               Left            =   720
               TabIndex        =   14
               Top             =   3825
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":1402
               Caption         =   "AIM_SeasonalityProfiles.frx":1422
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":148E
               Keys            =   "AIM_SeasonalityProfiles.frx":14AC
               Spin            =   "AIM_SeasonalityProfiles.frx":14F6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   9
               Left            =   2415
               TabIndex        =   15
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":151E
               Caption         =   "AIM_SeasonalityProfiles.frx":153E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":15AA
               Keys            =   "AIM_SeasonalityProfiles.frx":15C8
               Spin            =   "AIM_SeasonalityProfiles.frx":1612
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   10
               Left            =   2415
               TabIndex        =   16
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":163A
               Caption         =   "AIM_SeasonalityProfiles.frx":165A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":16C6
               Keys            =   "AIM_SeasonalityProfiles.frx":16E4
               Spin            =   "AIM_SeasonalityProfiles.frx":172E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   11
               Left            =   2415
               TabIndex        =   17
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":1756
               Caption         =   "AIM_SeasonalityProfiles.frx":1776
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":17E2
               Keys            =   "AIM_SeasonalityProfiles.frx":1800
               Spin            =   "AIM_SeasonalityProfiles.frx":184A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   12
               Left            =   2415
               TabIndex        =   18
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":1872
               Caption         =   "AIM_SeasonalityProfiles.frx":1892
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":18FE
               Keys            =   "AIM_SeasonalityProfiles.frx":191C
               Spin            =   "AIM_SeasonalityProfiles.frx":1966
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   13
               Left            =   2415
               TabIndex        =   19
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":198E
               Caption         =   "AIM_SeasonalityProfiles.frx":19AE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1A1A
               Keys            =   "AIM_SeasonalityProfiles.frx":1A38
               Spin            =   "AIM_SeasonalityProfiles.frx":1A82
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   14
               Left            =   2415
               TabIndex        =   20
               Top             =   3105
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":1AAA
               Caption         =   "AIM_SeasonalityProfiles.frx":1ACA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1B36
               Keys            =   "AIM_SeasonalityProfiles.frx":1B54
               Spin            =   "AIM_SeasonalityProfiles.frx":1B9E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   15
               Left            =   2415
               TabIndex        =   21
               Top             =   3465
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":1BC6
               Caption         =   "AIM_SeasonalityProfiles.frx":1BE6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1C52
               Keys            =   "AIM_SeasonalityProfiles.frx":1C70
               Spin            =   "AIM_SeasonalityProfiles.frx":1CBA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   16
               Left            =   2415
               TabIndex        =   22
               Top             =   3825
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":1CE2
               Caption         =   "AIM_SeasonalityProfiles.frx":1D02
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1D6E
               Keys            =   "AIM_SeasonalityProfiles.frx":1D8C
               Spin            =   "AIM_SeasonalityProfiles.frx":1DD6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   17
               Left            =   4110
               TabIndex        =   23
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":1DFE
               Caption         =   "AIM_SeasonalityProfiles.frx":1E1E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1E8A
               Keys            =   "AIM_SeasonalityProfiles.frx":1EA8
               Spin            =   "AIM_SeasonalityProfiles.frx":1EF2
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   18
               Left            =   4110
               TabIndex        =   24
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":1F1A
               Caption         =   "AIM_SeasonalityProfiles.frx":1F3A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":1FA6
               Keys            =   "AIM_SeasonalityProfiles.frx":1FC4
               Spin            =   "AIM_SeasonalityProfiles.frx":200E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   19
               Left            =   4110
               TabIndex        =   25
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2036
               Caption         =   "AIM_SeasonalityProfiles.frx":2056
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":20C2
               Keys            =   "AIM_SeasonalityProfiles.frx":20E0
               Spin            =   "AIM_SeasonalityProfiles.frx":212A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   20
               Left            =   4110
               TabIndex        =   26
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2152
               Caption         =   "AIM_SeasonalityProfiles.frx":2172
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":21DE
               Keys            =   "AIM_SeasonalityProfiles.frx":21FC
               Spin            =   "AIM_SeasonalityProfiles.frx":2246
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   21
               Left            =   4110
               TabIndex        =   27
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":226E
               Caption         =   "AIM_SeasonalityProfiles.frx":228E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":22FA
               Keys            =   "AIM_SeasonalityProfiles.frx":2318
               Spin            =   "AIM_SeasonalityProfiles.frx":2362
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   22
               Left            =   4110
               TabIndex        =   28
               Top             =   3105
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":238A
               Caption         =   "AIM_SeasonalityProfiles.frx":23AA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2416
               Keys            =   "AIM_SeasonalityProfiles.frx":2434
               Spin            =   "AIM_SeasonalityProfiles.frx":247E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   23
               Left            =   4110
               TabIndex        =   29
               Top             =   3465
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":24A6
               Caption         =   "AIM_SeasonalityProfiles.frx":24C6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2532
               Keys            =   "AIM_SeasonalityProfiles.frx":2550
               Spin            =   "AIM_SeasonalityProfiles.frx":259A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   24
               Left            =   4110
               TabIndex        =   30
               Top             =   3825
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":25C2
               Caption         =   "AIM_SeasonalityProfiles.frx":25E2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":264E
               Keys            =   "AIM_SeasonalityProfiles.frx":266C
               Spin            =   "AIM_SeasonalityProfiles.frx":26B6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   25
               Left            =   5805
               TabIndex        =   31
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":26DE
               Caption         =   "AIM_SeasonalityProfiles.frx":26FE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":276A
               Keys            =   "AIM_SeasonalityProfiles.frx":2788
               Spin            =   "AIM_SeasonalityProfiles.frx":27D2
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   26
               Left            =   5805
               TabIndex        =   32
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":27FA
               Caption         =   "AIM_SeasonalityProfiles.frx":281A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2886
               Keys            =   "AIM_SeasonalityProfiles.frx":28A4
               Spin            =   "AIM_SeasonalityProfiles.frx":28EE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   27
               Left            =   5805
               TabIndex        =   33
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2916
               Caption         =   "AIM_SeasonalityProfiles.frx":2936
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":29A2
               Keys            =   "AIM_SeasonalityProfiles.frx":29C0
               Spin            =   "AIM_SeasonalityProfiles.frx":2A0A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   28
               Left            =   5805
               TabIndex        =   34
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2A32
               Caption         =   "AIM_SeasonalityProfiles.frx":2A52
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2ABE
               Keys            =   "AIM_SeasonalityProfiles.frx":2ADC
               Spin            =   "AIM_SeasonalityProfiles.frx":2B26
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   29
               Left            =   5805
               TabIndex        =   35
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2B4E
               Caption         =   "AIM_SeasonalityProfiles.frx":2B6E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2BDA
               Keys            =   "AIM_SeasonalityProfiles.frx":2BF8
               Spin            =   "AIM_SeasonalityProfiles.frx":2C42
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   30
               Left            =   5805
               TabIndex        =   36
               Top             =   3105
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2C6A
               Caption         =   "AIM_SeasonalityProfiles.frx":2C8A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2CF6
               Keys            =   "AIM_SeasonalityProfiles.frx":2D14
               Spin            =   "AIM_SeasonalityProfiles.frx":2D5E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   31
               Left            =   5805
               TabIndex        =   37
               Top             =   3465
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2D86
               Caption         =   "AIM_SeasonalityProfiles.frx":2DA6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2E12
               Keys            =   "AIM_SeasonalityProfiles.frx":2E30
               Spin            =   "AIM_SeasonalityProfiles.frx":2E7A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   32
               Left            =   5805
               TabIndex        =   38
               Top             =   3825
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":2EA2
               Caption         =   "AIM_SeasonalityProfiles.frx":2EC2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":2F2E
               Keys            =   "AIM_SeasonalityProfiles.frx":2F4C
               Spin            =   "AIM_SeasonalityProfiles.frx":2F96
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   33
               Left            =   7515
               TabIndex        =   39
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":2FBE
               Caption         =   "AIM_SeasonalityProfiles.frx":2FDE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":304A
               Keys            =   "AIM_SeasonalityProfiles.frx":3068
               Spin            =   "AIM_SeasonalityProfiles.frx":30B2
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   34
               Left            =   7515
               TabIndex        =   40
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":30DA
               Caption         =   "AIM_SeasonalityProfiles.frx":30FA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3166
               Keys            =   "AIM_SeasonalityProfiles.frx":3184
               Spin            =   "AIM_SeasonalityProfiles.frx":31CE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   35
               Left            =   7515
               TabIndex        =   41
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":31F6
               Caption         =   "AIM_SeasonalityProfiles.frx":3216
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3282
               Keys            =   "AIM_SeasonalityProfiles.frx":32A0
               Spin            =   "AIM_SeasonalityProfiles.frx":32EA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   135200769
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   36
               Left            =   7515
               TabIndex        =   42
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3312
               Caption         =   "AIM_SeasonalityProfiles.frx":3332
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":339E
               Keys            =   "AIM_SeasonalityProfiles.frx":33BC
               Spin            =   "AIM_SeasonalityProfiles.frx":3406
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   37
               Left            =   7515
               TabIndex        =   43
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":342E
               Caption         =   "AIM_SeasonalityProfiles.frx":344E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":34BA
               Keys            =   "AIM_SeasonalityProfiles.frx":34D8
               Spin            =   "AIM_SeasonalityProfiles.frx":3522
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   38
               Left            =   7515
               TabIndex        =   44
               Top             =   3105
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":354A
               Caption         =   "AIM_SeasonalityProfiles.frx":356A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":35D6
               Keys            =   "AIM_SeasonalityProfiles.frx":35F4
               Spin            =   "AIM_SeasonalityProfiles.frx":363E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   39
               Left            =   7515
               TabIndex        =   45
               Top             =   3465
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3666
               Caption         =   "AIM_SeasonalityProfiles.frx":3686
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":36F2
               Keys            =   "AIM_SeasonalityProfiles.frx":3710
               Spin            =   "AIM_SeasonalityProfiles.frx":375A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   40
               Left            =   7515
               TabIndex        =   46
               Top             =   3825
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3782
               Caption         =   "AIM_SeasonalityProfiles.frx":37A2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":380E
               Keys            =   "AIM_SeasonalityProfiles.frx":382C
               Spin            =   "AIM_SeasonalityProfiles.frx":3876
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   41
               Left            =   9210
               TabIndex        =   47
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":389E
               Caption         =   "AIM_SeasonalityProfiles.frx":38BE
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":392A
               Keys            =   "AIM_SeasonalityProfiles.frx":3948
               Spin            =   "AIM_SeasonalityProfiles.frx":3992
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   42
               Left            =   9210
               TabIndex        =   48
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":39BA
               Caption         =   "AIM_SeasonalityProfiles.frx":39DA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3A46
               Keys            =   "AIM_SeasonalityProfiles.frx":3A64
               Spin            =   "AIM_SeasonalityProfiles.frx":3AAE
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   43
               Left            =   9210
               TabIndex        =   49
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3AD6
               Caption         =   "AIM_SeasonalityProfiles.frx":3AF6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3B62
               Keys            =   "AIM_SeasonalityProfiles.frx":3B80
               Spin            =   "AIM_SeasonalityProfiles.frx":3BCA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   44
               Left            =   9210
               TabIndex        =   50
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3BF2
               Caption         =   "AIM_SeasonalityProfiles.frx":3C12
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3C7E
               Keys            =   "AIM_SeasonalityProfiles.frx":3C9C
               Spin            =   "AIM_SeasonalityProfiles.frx":3CE6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   45
               Left            =   9210
               TabIndex        =   51
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3D0E
               Caption         =   "AIM_SeasonalityProfiles.frx":3D2E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3D9A
               Keys            =   "AIM_SeasonalityProfiles.frx":3DB8
               Spin            =   "AIM_SeasonalityProfiles.frx":3E02
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   46
               Left            =   9210
               TabIndex        =   52
               Top             =   3105
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3E2A
               Caption         =   "AIM_SeasonalityProfiles.frx":3E4A
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3EB6
               Keys            =   "AIM_SeasonalityProfiles.frx":3ED4
               Spin            =   "AIM_SeasonalityProfiles.frx":3F1E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   47
               Left            =   9210
               TabIndex        =   53
               Top             =   3465
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":3F46
               Caption         =   "AIM_SeasonalityProfiles.frx":3F66
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":3FD2
               Keys            =   "AIM_SeasonalityProfiles.frx":3FF0
               Spin            =   "AIM_SeasonalityProfiles.frx":403A
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   48
               Left            =   9210
               TabIndex        =   54
               Top             =   3825
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_SeasonalityProfiles.frx":4062
               Caption         =   "AIM_SeasonalityProfiles.frx":4082
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":40EE
               Keys            =   "AIM_SeasonalityProfiles.frx":410C
               Spin            =   "AIM_SeasonalityProfiles.frx":4156
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   49
               Left            =   10905
               TabIndex        =   55
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":417E
               Caption         =   "AIM_SeasonalityProfiles.frx":419E
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":420A
               Keys            =   "AIM_SeasonalityProfiles.frx":4228
               Spin            =   "AIM_SeasonalityProfiles.frx":4272
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   50
               Left            =   10905
               TabIndex        =   56
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":429A
               Caption         =   "AIM_SeasonalityProfiles.frx":42BA
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":4326
               Keys            =   "AIM_SeasonalityProfiles.frx":4344
               Spin            =   "AIM_SeasonalityProfiles.frx":438E
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   51
               Left            =   10905
               TabIndex        =   57
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":43B6
               Caption         =   "AIM_SeasonalityProfiles.frx":43D6
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":4442
               Keys            =   "AIM_SeasonalityProfiles.frx":4460
               Spin            =   "AIM_SeasonalityProfiles.frx":44AA
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2006777857
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBI 
               Height          =   345
               Index           =   52
               Left            =   10905
               TabIndex        =   58
               Top             =   2385
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_SeasonalityProfiles.frx":44D2
               Caption         =   "AIM_SeasonalityProfiles.frx":44F2
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "MS Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_SeasonalityProfiles.frx":455E
               Keys            =   "AIM_SeasonalityProfiles.frx":457C
               Spin            =   "AIM_SeasonalityProfiles.frx":45C6
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "#0.000;(#0.000)"
               HighlightText   =   -1
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   52
               MinValue        =   -52
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "52"
               Height          =   300
               Index           =   56
               Left            =   10380
               TabIndex        =   113
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "51"
               Height          =   300
               Index           =   55
               Left            =   10380
               TabIndex        =   112
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "50"
               Height          =   300
               Index           =   54
               Left            =   10380
               TabIndex        =   111
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "49"
               Height          =   300
               Index           =   53
               Left            =   10380
               TabIndex        =   110
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "40"
               Height          =   300
               Index           =   44
               Left            =   6960
               TabIndex        =   109
               Top             =   3870
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "39"
               Height          =   300
               Index           =   43
               Left            =   6960
               TabIndex        =   108
               Top             =   3510
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "38"
               Height          =   300
               Index           =   42
               Left            =   6960
               TabIndex        =   107
               Top             =   3150
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "37"
               Height          =   300
               Index           =   41
               Left            =   6960
               TabIndex        =   106
               Top             =   2790
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "36"
               Height          =   300
               Index           =   40
               Left            =   6960
               TabIndex        =   105
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "35"
               Height          =   300
               Index           =   39
               Left            =   6960
               TabIndex        =   104
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "34"
               Height          =   300
               Index           =   38
               Left            =   6960
               TabIndex        =   103
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "33"
               Height          =   300
               Index           =   37
               Left            =   6960
               TabIndex        =   102
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "32"
               Height          =   300
               Index           =   36
               Left            =   5250
               TabIndex        =   101
               Top             =   3870
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "31"
               Height          =   300
               Index           =   35
               Left            =   5250
               TabIndex        =   100
               Top             =   3510
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "30"
               Height          =   300
               Index           =   34
               Left            =   5250
               TabIndex        =   99
               Top             =   3150
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "29"
               Height          =   300
               Index           =   33
               Left            =   5250
               TabIndex        =   98
               Top             =   2790
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "28"
               Height          =   300
               Index           =   32
               Left            =   5250
               TabIndex        =   97
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "27"
               Height          =   300
               Index           =   31
               Left            =   5250
               TabIndex        =   96
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "26"
               Height          =   300
               Index           =   30
               Left            =   5250
               TabIndex        =   95
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "25"
               Height          =   300
               Index           =   29
               Left            =   5250
               TabIndex        =   94
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "24"
               Height          =   300
               Index           =   28
               Left            =   3540
               TabIndex        =   93
               Top             =   3870
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "23"
               Height          =   300
               Index           =   27
               Left            =   3540
               TabIndex        =   92
               Top             =   3510
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "22"
               Height          =   300
               Index           =   26
               Left            =   3540
               TabIndex        =   91
               Top             =   3150
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "21"
               Height          =   300
               Index           =   25
               Left            =   3540
               TabIndex        =   90
               Top             =   2790
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "20"
               Height          =   300
               Index           =   24
               Left            =   3540
               TabIndex        =   89
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "19"
               Height          =   300
               Index           =   23
               Left            =   3540
               TabIndex        =   88
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "18"
               Height          =   300
               Index           =   22
               Left            =   3540
               TabIndex        =   87
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "17"
               Height          =   300
               Index           =   21
               Left            =   3540
               TabIndex        =   86
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "16"
               Height          =   300
               Index           =   20
               Left            =   1830
               TabIndex        =   85
               Top             =   3870
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "15"
               Height          =   300
               Index           =   19
               Left            =   1830
               TabIndex        =   84
               Top             =   3510
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "14"
               Height          =   300
               Index           =   18
               Left            =   1830
               TabIndex        =   83
               Top             =   3150
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "13"
               Height          =   300
               Index           =   17
               Left            =   1830
               TabIndex        =   82
               Top             =   2790
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "12"
               Height          =   300
               Index           =   16
               Left            =   1830
               TabIndex        =   81
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "11"
               Height          =   300
               Index           =   15
               Left            =   1830
               TabIndex        =   80
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "10"
               Height          =   300
               Index           =   14
               Left            =   1830
               TabIndex        =   79
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "09"
               Height          =   300
               Index           =   13
               Left            =   1830
               TabIndex        =   78
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "08"
               Height          =   300
               Index           =   12
               Left            =   120
               TabIndex        =   77
               Top             =   3870
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "07"
               Height          =   300
               Index           =   11
               Left            =   120
               TabIndex        =   76
               Top             =   3510
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "06"
               Height          =   300
               Index           =   10
               Left            =   120
               TabIndex        =   75
               Top             =   3150
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "05"
               Height          =   300
               Index           =   9
               Left            =   120
               TabIndex        =   74
               Top             =   2790
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "04"
               Height          =   300
               Index           =   8
               Left            =   120
               TabIndex        =   73
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "03"
               Height          =   300
               Index           =   7
               Left            =   120
               TabIndex        =   72
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "02"
               Height          =   300
               Index           =   6
               Left            =   120
               TabIndex        =   71
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "01"
               Height          =   300
               Index           =   3
               Left            =   120
               TabIndex        =   70
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "41"
               Height          =   300
               Index           =   45
               Left            =   8670
               TabIndex        =   69
               Top             =   1365
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "42"
               Height          =   300
               Index           =   46
               Left            =   8670
               TabIndex        =   68
               Top             =   1725
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "43"
               Height          =   300
               Index           =   47
               Left            =   8670
               TabIndex        =   67
               Top             =   2085
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "44"
               Height          =   300
               Index           =   48
               Left            =   8670
               TabIndex        =   66
               Top             =   2430
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "45"
               Height          =   300
               Index           =   49
               Left            =   8670
               TabIndex        =   65
               Top             =   2790
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "46"
               Height          =   300
               Index           =   50
               Left            =   8670
               TabIndex        =   64
               Top             =   3150
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "47"
               Height          =   300
               Index           =   51
               Left            =   8670
               TabIndex        =   63
               Top             =   3510
               Width           =   435
            End
            Begin VB.Label Label 
               Alignment       =   1  'Right Justify
               Caption         =   "48"
               Height          =   300
               Index           =   52
               Left            =   8670
               TabIndex        =   62
               Top             =   3870
               Width           =   435
            End
            Begin VB.Label Label 
               Caption         =   "Class"
               Height          =   300
               Index           =   5
               Left            =   200
               TabIndex        =   61
               Top             =   995
               Width           =   2445
            End
            Begin VB.Label Label 
               Caption         =   "Location ID"
               Height          =   300
               Index           =   4
               Left            =   200
               TabIndex        =   60
               Top             =   630
               Width           =   2445
            End
            Begin VB.Label Label 
               Caption         =   "Description"
               Height          =   300
               Index           =   2
               Left            =   200
               TabIndex        =   59
               Top             =   265
               Width           =   2445
            End
         End
      End
   End
   Begin VB.Frame Frame1 
      Height          =   1065
      Left            =   45
      TabIndex        =   114
      Top             =   0
      Width           =   12255
      Begin TDBNumber6Ctl.TDBNumber txtSAVersion 
         Height          =   345
         Left            =   2750
         TabIndex        =   0
         Top             =   180
         Width           =   480
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calculator      =   "AIM_SeasonalityProfiles.frx":45EE
         Caption         =   "AIM_SeasonalityProfiles.frx":460E
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfiles.frx":467A
         Keys            =   "AIM_SeasonalityProfiles.frx":4698
         Spin            =   "AIM_SeasonalityProfiles.frx":46E2
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   0
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "#####0;-#####0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#####0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   32767
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   1
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBText6Ctl.TDBText txtSaId 
         Height          =   345
         Left            =   2745
         TabIndex        =   1
         Top             =   600
         Width           =   9090
         _Version        =   65536
         _ExtentX        =   16034
         _ExtentY        =   609
         Caption         =   "AIM_SeasonalityProfiles.frx":470A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_SeasonalityProfiles.frx":4776
         Key             =   "AIM_SeasonalityProfiles.frx":4794
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   62
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Total"
         Height          =   300
         Index           =   57
         Left            =   10080
         TabIndex        =   123
         Top             =   225
         Width           =   600
      End
      Begin VB.Label txtBISum 
         Alignment       =   1  'Right Justify
         BackColor       =   &H80000004&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   10920
         TabIndex        =   122
         Top             =   180
         Width           =   900
      End
      Begin VB.Label Label 
         Caption         =   "Seasonality Version"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   116
         Top             =   225
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Seasonality Profile ID"
         Height          =   300
         Index           =   1
         Left            =   195
         TabIndex        =   115
         Top             =   600
         Width           =   2445
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbSeasons 
      Left            =   225
      Top             =   5970
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_SeasonalityProfiles.frx":47D8
      ToolBars        =   "AIM_SeasonalityProfiles.frx":D3D6
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_SeasonalityProfiles"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_AIMSeasons_GetEq_Sp As ADODB.Command
Dim AIM_AIMSeasons_GetKey_Sp As ADODB.Command

Dim rsAIMSeasons As ADODB.Recordset

Dim SAVersion As Integer
Dim SaId As String

Dim SAVersion_BookMark As Integer
Dim SaId_BookMark As String

'Storage for tracking the mouse
Dim px As Long
Dim py As Long

'Storage for tracking the users' interactions
Dim Series As Long
Dim Pnt As Long
Dim Distance As Long
Dim Region As Long
Dim SaveSeries As Long
Dim SavePoint As Long

Dim ChartModify As Boolean

Private Function Refresh_Form()
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim BISum As Double

    'Enable/Disable Key fields
    If rsAIMSeasons.EditMode = adEditAdd Then
        If gAccessLvl <> 1 Then tbSeasons.Tools("ID_Save").Enabled = True
        Me.tbSeasons.Tools("ID_Delete").Enabled = False
        Me.tbSeasons.Tools("ID_AddNew").Enabled = False
        Me.txtSAVersion.Enabled = True
        Me.txtSaId.Enabled = True
        Me.tbSeasons.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")
    Else
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            Me.tbSeasons.Tools("ID_Save").Enabled = False
            Me.tbSeasons.Tools("ID_Delete").Enabled = False
            Me.tbSeasons.Tools("ID_AddNew").Enabled = False
        Else
            Me.tbSeasons.Tools("ID_Save").Enabled = True
            Me.tbSeasons.Tools("ID_Delete").Enabled = True
            Me.tbSeasons.Tools("ID_AddNew").Enabled = True
        End If

        Me.txtSAVersion.Enabled = False
        Me.txtSaId.Enabled = False
        Me.tbSeasons.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Edit")
    End If
    
    'Update Form
    Me.txtSAVersion.Text = rsAIMSeasons("SAVersion").Value
    Me.txtSaId.Text = rsAIMSeasons("SaId").Value
    Me.txtSaDesc.Text = rsAIMSeasons("sadesc").Value
    Me.ckSALevel = IIf(rsAIMSeasons("SaLevel").Value = "S", vbChecked, vbUnchecked)
    Me.txtLcId.Text = rsAIMSeasons("lcid").Value
    Me.dcClass.Text = rsAIMSeasons("class").Value
    
    BISum = 0

    Me.ctSeasons.IsBatched = True
    For i = Me.txtBI.LBound To Me.txtBI.UBound
        Me.txtBI(i).Value = rsAIMSeasons("bi" & Format(i, "00")).Value
        BISum = BISum + Me.txtBI(i).Value
        Me.ctSeasons.ChartGroups(1).Data.Y(1, i) = Me.txtBI(i).Value

    Next i
    Me.ctSeasons.IsBatched = False

    Me.txtBISum.Caption = Format(BISum, "#0.000")
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(Refresh_Form)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfiles:::Refresh_Form", Now, gDRGeneralError, True, Err
End Function


Private Function f_AIMSeasons_Get(SAVersion As Integer, SaId As String, Action As SQL_ACTIONS)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Get the Seasonality Profile Key
    RtnCode = AIMSeasons_GetKey(AIM_AIMSeasons_GetKey_Sp, _
        SAVersion, SaId, Action)
    If Trim(SaId) <> "" Then
        If Not (Trim(SaId) = Trim(Me.txtSaId.Text) And Trim(SAVersion) = Trim(Me.txtSAVersion.Text)) Then
            RtnCode = AIMSeasons_GetEq(AIM_AIMSeasons_GetEq_Sp, _
            rsAIMSeasons, SaId, SAVersion)
        End If
    End If
    'Get the Seasonality Profile
'    If RtnCode = SUCCEED Then
'        rTNcODE = AIMSeasons_GetEq(AIM_AIMSeasons_GetEq_Sp, _
'            rsAIMSeasons, SaId, SAVersion)
    
'    End If
    
    f_AIMSeasons_Get = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMSeasons_Get)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfiles:::f_AIMSeasons_Get", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMSeasons_InitializeNew()
On Error GoTo ErrorHandler

    Dim intCounter As Integer

    rsAIMSeasons.AddNew
    
    rsAIMSeasons("SaId").Value = getTranslationResource("New")
    rsAIMSeasons("SAVersion").Value = 0
    rsAIMSeasons("sadesc").Value = ""
    rsAIMSeasons("SaLevel").Value = "D"
    rsAIMSeasons("lcid").Value = ""
    rsAIMSeasons("class").Value = ""
    
    For intCounter = Me.txtBI.LBound To Me.txtBI.UBound
        rsAIMSeasons("bi" & Format(intCounter, "00")).Value = 1
    Next intCounter

    'Enable/Disable Key fields
    Me.txtSAVersion.Enabled = True
    Me.txtSaId.Enabled = True
    Me.tbSeasons.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")

    Me.tbSeasons.Tools("ID_Delete").Enabled = False
    Me.tbSeasons.Tools("ID_AddNew").Enabled = False
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMSeasons_InitializeNew)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfiles:::f_AIMSeasons_InitializeNew", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMSeasons_Save()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    
    Cn.Errors.Clear
    
    'Validate required info
    RtnCode = UserInputValidation
    If RtnCode = SUCCEED Then
        rsAIMSeasons.Update
        
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, Cn.Errors.Count - 1
        Else
            strMessage = getTranslationResource("STATMSG05001")
            If StrComp(strMessage, "STATMSG05001") = 0 Then strMessage = "Seasonality Profile successfully updated."
            Write_Message strMessage
        End If
    Else
        f_AIMSeasons_Save = RtnCode
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMSeasons_Save)", Err.Description
    f_HandleErr , , , "AIM_SeasonalityProfiles:::f_AIMSeasons_Save", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMSeasons_Update()
On Error GoTo ErrorHandler

    Dim intCounter As Integer
    
    rsAIMSeasons("SAVersion").Value = Me.txtSAVersion.Text
    rsAIMSeasons("SaId").Value = Me.txtSaId.Text

    rsAIMSeasons("sadesc").Value = Me.txtSaDesc.Text
    rsAIMSeasons("SaLevel").Value = IIf(Me.ckSALevel = vbChecked, "S", "D")
    rsAIMSeasons("lcid").Value = Me.txtLcId.Text
    rsAIMSeasons("class").Value = Me.dcClass.Text

    For intCounter = Me.txtBI.LBound To Me.txtBI.UBound
        rsAIMSeasons("bi" & Format(intCounter, "00")).Value = Me.txtBI(intCounter).Value
    Next intCounter
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMSeasons_Update)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfiles:::f_AIMSeasons_Update", Now, gDRGeneralError, True, Err
End Function

Private Function UserInputValidation() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX07312")
    If StrComp(strMessage, "MSGBOX07312") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim(txtSaId.Text) = "" _
    Or StrComp(txtSaId.Text, getTranslationResource("New")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    Label(0).Caption  'Promotion ID
        txtSaId.SetFocus
        MessageRequired = True
    End If
    
    'check sa version?
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
     f_HandleErr , , , "AIM_SeasonalityProfiles:::UserInputValidation", Now, gDRGeneralError, True, Err
End Function

Private Sub atSeasons_BeforeTabClick(ByVal NewTab As ActiveTabs.SSTab, ByVal Cancel As ActiveTabs.SSReturnBoolean)
On Error GoTo ErrorHandler

    Dim i As Integer
    
    Select Case NewTab.Key
    Case "Graph"
       With Me.ctSeasons
            .IsBatched = True
            For i = Me.txtBI.LBound To Me.txtBI.UBound
                .ChartGroups(1).Data.Y(1, i) = Me.txtBI(i).Value
             Next i
            .IsBatched = False
        End With
        
    'Setup how the Chart Looks
    'Make some changes to the Header
    With Me.ctSeasons.Header
        .Text = getTranslationResource("Seasonality")
        .Location.Left = ((Me.ctSeasons.Width / Screen.TwipsPerPixelX - .Location.Width) / 2)
    End With
    
    'Make some changes to the Footer
    With Me.ctSeasons.Footer
        .Text = getTranslationResource("Move Points on Graph")
        .Location.Left = ((Me.ctSeasons.Width / Screen.TwipsPerPixelX - .Location.Width) / 2)
    End With
    
    'Make some changes to the colors of the lines in Chart Group 1
    With Me.ctSeasons.ChartGroups(1)
        .IsStacked = True
    End With
    
    'Make the Y2 Axis visible
    Me.ctSeasons.ChartArea.Axes("Y2").IsShowing = True
    
    Me.ctSeasons.ChartArea.Axes("Y2").Multiplier = 1
    
    'Setup the marker style
    With Me.ctSeasons.ChartArea.Markers("Y")
        .IsShowing = True
        .Style.Pattern = oc2dLineLongDash
        .Style.Color = RGB(0, 0, 0)     'Black
        .Style.Width = 1
        .Method = oc2dMarkerDataIndex   'Set the attach method for the marker
    End With
    
    'The Chart is not currently being modified
    ChartModify = False
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atSeasons_BeforeTabClick)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::atSeasons_BeforeTabClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub ctSeasons_ModifyEnd()

ChartModify = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ctSeasons_ModifyEnd)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::ctSeasons_ModifyEnd", Now, gDRGeneralError, True, Err
End Sub

Private Sub ctSeasons_ModifyStart(IsOK As Boolean)
 
ChartModify = True

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ctSeasons_ModifyStart)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::ctSeasons_ModifyStart", Now, gDRGeneralError, True, Err
End Sub

Private Sub ctSeasons_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    
    Case vbLeftButton
    
        'Make sure we are not in being modified using the middle-mouse button
        If ChartModify Then Exit Sub

        px = X / Screen.TwipsPerPixelX
        py = Y / Screen.TwipsPerPixelY

        Region = Me.ctSeasons.ChartGroups(1).CoordToDataIndex(px, py, oc2dFocusXY, Series, Pnt, Distance)
        If Series > 0 Then
            SaveSeries = Series
            SavePoint = Pnt
        End If

    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ctSeasons_MouseDown)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::ctSeasons_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub ctSeasons_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
'Track the mouse movement and if over a bar, then make the markers visible on
' the bar in the series and point that the mouse is currently over.
'If the user has the left-mouse button pressed then change the value of the bar accordingly.

    'Make sure we are not in being modified using the middle-mouse button
    If ChartModify Then Exit Sub
    
    Dim vx As Double
    Dim vy As Double
    
    px = X / Screen.TwipsPerPixelX
    py = Y / Screen.TwipsPerPixelY
    
    'If no buttons are pressed
    If Button = 0 Then
        'Find out where we are
        Region = Me.ctSeasons.ChartGroups(1).CoordToDataIndex(px, py, oc2dFocusXY, Series, Pnt, Distance)
        With Me.ctSeasons.ChartArea.Markers("X")
            If Distance <= 5 And Region = oc2dRegionInChartArea Then
                Me.ctSeasons.Footer.Text = getTranslationResource("Period") + str(Pnt) + "  " + getTranslationResource("Index Value") + str(Format(Me.ctSeasons.ChartGroups(1).Data.Y(Series, Pnt), "###0.###"))
               
                If .Index.Series <> Series Then
                    .Index.Series = Series
                End If
                
                If .Index.Point <> Pnt Then
                    .Index.Point = Pnt
                End If
                
                If .IsShowing <> True Then
                    .IsShowing = True
                End If
            Else
                .IsShowing = False
                Me.ctSeasons.Footer.Text = getTranslationResource("Move Points on Graph")
            End If
        End With
    'If the left button is being held
    ElseIf Button = 1 Then
        If Distance <= 5 Then    'If we are right on the bar, go ahead and drag
            With Me.ctSeasons.ChartGroups(1)
                .CoordToDataCoord px, py, vx, vy
                'Make sure the value we are at is a visible value (Hole values are invisible)
                If (vy <> 1E+308) And (SaveSeries > 0 And SavePoint > 0) Then
                    'Format vy Value
                    vy = Format(vy, "#0.###")
                    'Limit the drag value
                    
                    If vy > 52 Then
                        vy = 52
                    ElseIf vy < 0 Then
                        vy = 0
                    End If
                    If py < 0 Then
                        py = 0
                    End If
                    .Data.Y(SaveSeries, SavePoint) = vy         'Set the new size of the bar
                End If
            End With
        End If
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ctSeasons_MouseMove)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::ctSeasons_MouseMove", Now, gDRGeneralError, True, Err
End Sub

Private Sub ctSeasons_MouseUp(Button As Integer, Shift As Integer, X As Single, Y As Single)
'Watch for the user to let go of the mouse button and reset the saved series and point values.

    Me.txtBI(Pnt).Value = Me.ctSeasons.ChartGroups(1).Data.Y(Series, Pnt)
    SaveSeries = 0
    SavePoint = 0
    txtBI_LostFocus (Pnt)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ctSeasons_MouseUp)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::ctSeasons_MouseUp", Now, gDRGeneralError, True, Err
End Sub


Private Sub dcClass_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcClass.Columns(0).Name = "Class"
    Me.dcClass.Columns(0).Caption = getTranslationResource("Class")
    Me.dcClass.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcClass.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcClass.Columns(0).Width = 1000
    
    Me.dcClass.Columns(1).Name = "ClassLevel"
    Me.dcClass.Columns(1).Caption = getTranslationResource("Class Level")
    Me.dcClass.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcClass.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcClass.Columns(1).Width = 720
    
    Me.dcClass.Columns(2).Name = "ClassDesc"
    Me.dcClass.Columns(2).Caption = getTranslationResource("Class Description")
    Me.dcClass.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dcClass.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcClass.Columns(2).Width = 2000

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcClass, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcClass.Columns.Count - 1
'        dcClass.Columns(IndexCounter).HasHeadBackColor = True
'        dcClass.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'   Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcClass_InitColumnProps)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::dcClass_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Populate the class dropdown
    LoadClassList
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim intCounter As Integer
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05000")
    If StrComp(strMessage, "STATMSG05000") = 0 Then strMessage = "Initializing Seasonality Profile Maintenance... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
        
    'Initialize Stored Procedures
    Set AIM_AIMSeasons_GetEq_Sp = New ADODB.Command
    With AIM_AIMSeasons_GetEq_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMSeasons_GetEq_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AIMSeasons_GetKey_Sp = New ADODB.Command
    With AIM_AIMSeasons_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMSeasons_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the Seasonality Profile Record Set
    Set rsAIMSeasons = New ADODB.Recordset
    With rsAIMSeasons
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Set up the Seasonality Profile Graph
    With Me.ctSeasons
        .IsBatched = False
        
        'Set up Header
        .Header.Text = getTranslationResource("")
        .Border.Type = oc2dBorderPlain
        .Border.Width = 2
        
        .ChartArea.Axes("X").Title.Text = getTranslationResource("")
        .ChartArea.Axes("X").AnnotationMethod = oc2dAnnotateValueLabels
        .ChartArea.Axes("X").MajorGrid.Spacing = 4
        .ChartArea.Axes("X").Max = 52
        
        .ChartArea.Axes("Y").Title.Text = getTranslationResource("Index")
        .ChartArea.Axes("Y").TitleRotation = oc2dRotate90Degrees
        .ChartArea.Axes("Y").MajorGrid.Spacing = 0.2
        .ChartArea.Axes("Y").Min = 0
        
        'Set up Legends
        .Legend.IsShowing = False
        
        'Set up Data Series
        .ChartGroups(1).ChartType = oc2dTypePlot
        .ChartGroups(1).Data.NumSeries = 1
        .ChartGroups(1).Data.NumPoints(1) = 52
        
        'Set the Line Properties
        .ChartGroups(1).Styles(1).Symbol.Shape = 4
        .ChartGroups(1).Styles(1).Line.Color = vbRed
        .ChartGroups(1).Styles(1).Line.Width = 1
        
        'Build X Axes
         For intCounter = 1 To 52

            'Set the x axes label
            If intCounter Mod 4 = 0 Then
                .ChartArea.Axes("X").ValueLabels.Add intCounter, Format(intCounter, "#0")
            End If

        Next intCounter

        .IsBatched = False
    
    End With

    'Open the Result Set
    SAVersion = 0
    SaId = ""
    
    RtnCode = f_AIMSeasons_Get(SAVersion, SaId, SQL_GetFirst)
    
    'Check status and take approp. action
    If RtnCode <> SUCCEED Then
        f_AIMSeasons_InitializeNew
    End If
    
    'Sync. input boxes with recordset
    Refresh_Form

    'Make the spin button visible
    For intCounter = txtBI.LBound To txtBI.UBound
        txtBI(intCounter).Spin.Visible = 1
    Next
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strMessage As String
    
    Refresh_Form
    
    If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
    Set rsAIMSeasons = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_SeasonalityProfiles:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler
        
    Select Case Index
    Case 0  'Copy
        Me.ctSeasons.CopyToClipboard oc2dFormatEnhMetafile
    Case 1  'Print
        Me.ctSeasons.PrintChart oc2dFormatEnhMetafile, oc2dScaleToFit, 0, 0, 0, 0
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbSeasons_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Clear errors
    Cn.Errors.Clear

    Write_Message ""

    RtnCode = f_AIMSeasons_Update           'Update the recordset

    'Alert user to possible change
    Select Case Tool.ID
    Case "ID_AddNew", "ID_Close", "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev", _
        "ID_GoToBookMark", "ID_LookUp"

        If gAccessLvl <> 1 And DataChanged(rsAIMSeasons, adAffectCurrent) > 0 Then

            strMessage = getTranslationResource("MSGBOX05003")
            If StrComp(strMessage, "MSGBOX05003") = 0 Then strMessage = "Abandon changes to current record?"
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, Me.Caption)
            If RtnCode = vbYes Then
                rsAIMSeasons.CancelUpdate
            Else
                f_AIMSeasons_Save
                
            End If

        Else
            rsAIMSeasons.CancelUpdate       'required to prevent error on requery
        End If

    End Select

    'Navigate
    Select Case Tool.ID

    Case "ID_AddNew"
        f_AIMSeasons_InitializeNew

    Case "ID_Delete"
        'Display a confirmation msgbox
        strMessage = getTranslationResource("MSGBOX05000")
        If StrComp(strMessage, "MSGBOX05000") = 0 Then strMessage = "Delete the current Seasonality Profile?"
        
        RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
    
        Select Case RtnCode
        Case vbYes
            'User chose to delete.
            rsAIMSeasons.Delete
            If f_AIMSeasons_Get(SAVersion, SaId, SQL_Getlt) <> SUCCEED Then
                If f_AIMSeasons_Get(SAVersion, SaId, SQL_GetFirst) <> SUCCEED Then
                    f_AIMSeasons_InitializeNew
                End If
            End If

        Case vbNo
            'User chose to Cancel
        End Select

    Case "ID_GetFirst"
        f_AIMSeasons_Get SAVersion, SaId, SQL_GetFirst

    Case "ID_GetPrev"
        f_AIMSeasons_Get SAVersion, SaId, SQL_Getlt
    
    Case "ID_GetNext"
        f_AIMSeasons_Get SAVersion, SaId, SQL_GetGT

    Case "ID_GetLast"
        f_AIMSeasons_Get SAVersion, SaId, SQL_GetLast

    Case "ID_Save"
        f_AIMSeasons_Save
        
    Case "ID_SetBookMark"
        SAVersion_BookMark = SAVersion
        SaId_BookMark = SaId
        Me.tbSeasons.Tools("ID_GoToBookMark").Enabled = True

    Case "ID_GoToBookMark"
        SAVersion = SAVersion_BookMark
        SaId = SaId_BookMark
        f_AIMSeasons_Get SAVersion, SaId, SQL_GetEq

    Case "ID_LookUp"
        AIM_SeasonalityProfileLookUp.SAVersion = Format(SAVersion, "##0")
        AIM_SeasonalityProfileLookUp.SaId = SaId
        Set AIM_SeasonalityProfileLookUp.Cn = Cn
        AIM_SeasonalityProfileLookUp.Show vbModal
        
        If Not AIM_SeasonalityProfileLookUp.CancelFlag Then
        
            SaId = AIM_SeasonalityProfileLookUp.SaId
            SAVersion = AIM_SeasonalityProfileLookUp.SAVersion
            
            f_AIMSeasons_Get SAVersion, SaId, SQL_GetEq
            
        End If

    Case "ID_Close"
        Unload Me
        Exit Sub

    End Select

    Refresh_Form        'Refresh the form
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbSeasons_ToolClick)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::tbSeasons_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtBI_LostFocus(Index As Integer)
    On Error GoTo ErrorHandler
    Dim BISum As Double
    Dim i As Integer
    Dim strMessage As String
    Dim RtnCode As Integer
    If txtBI.Item(Index).Value > 52# Then
        txtBI.Item(Index).SetFocus
        strMessage = getTranslationResource("MSGBOX05001")
        If StrComp(strMessage, "MSGBOX05001") = 0 Then strMessage = "Invalid Data.Value should not be more than 52.000."
        RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
        txtBI.Item(Index).Value = 52#
        Exit Sub
    End If
    BISum = 0
    For i = Me.txtBI.LBound To Me.txtBI.UBound
        BISum = BISum + Me.txtBI(i).Value
    Next i
    If BISum > 52.005 Then
        Beep
    End If
    Me.txtBISum.Caption = Format(BISum, "#0.000")
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtBI_Validate)"
     f_HandleErr , , , "AIM_SeasonalityProfiles:::txtBI_LostFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtBI_KeyUp(Index As Integer, KeyCode As Integer, Shift As Integer)
    txtBI_LostFocus Index
End Sub

Private Function LoadClassList()
On Error GoTo ErrorHandler
    
    Dim rsClassList As ADODB.Recordset
    Dim AIM_Class_List_Sp As ADODB.Command

    'Set default values
    Me.dcClass.AddItem getTranslationResource("All") & vbTab & _
                       getTranslationResource("All") & vbTab & _
                       getTranslationResource("All Classes")
    
    'Command object for the Stored Procedure
    Set AIM_Class_List_Sp = New ADODB.Command
    With AIM_Class_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Class_List_Sp"
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        '-- Conditions
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("@Class", adVarWChar, adParamInput, 50)
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("@ClassLevel", adTinyInt, adParamInput)
        .Parameters.Append AIM_Class_List_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 10)
        'Values
        .Parameters("@Class").Value = "All"
        .Parameters("@LangID").Value = gLangID
    End With
    
    'Recordset to catch the data.
    Set rsClassList = New ADODB.Recordset
    With rsClassList
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    'Fetch data
    Set rsClassList = AIM_Class_List_Sp.Execute
    
    'Load the data into the dropdown
    If f_IsRecordsetOpenAndPopulated(rsClassList) Then
        Do Until rsClassList.eof
            Me.dcClass.AddItem CStr(rsClassList!Class) & vbTab & _
                               CStr(rsClassList!ClassLevel) & vbTab & _
                               CStr(rsClassList!ClassDesc)
            rsClassList.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsClassList) Then rsClassList.Close
    Set rsClassList = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsClassList) Then rsClassList.Close
    Set rsClassList = Nothing
    'Err.Raise Err.Number, Err.source & "(LoadClassList)", Err.Description
    f_HandleErr , , , "AIM_SeasonalityProfiles:::LoadClassList", Now, gDRGeneralError, True, Err
End Function

