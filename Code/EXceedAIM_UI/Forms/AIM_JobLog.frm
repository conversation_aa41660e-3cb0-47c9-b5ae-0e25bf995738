VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_JobLog 
   Caption         =   "SSA DR Job Log"
   ClientHeight    =   6930
   ClientLeft      =   75
   ClientTop       =   345
   ClientWidth     =   11295
   Icon            =   "AIM_JobLog.frx":0000
   LinkTopic       =   "Form1"
   ScaleHeight     =   6930
   ScaleWidth      =   11295
   StartUpPosition =   1  'CenterOwner
   Begin VB.Frame Frame1 
      Height          =   6520
      Left            =   8
      TabIndex        =   2
      Top             =   0
      Width           =   11295
      Begin TDBText6Ctl.TDBText txtLogFile 
         Height          =   5775
         Left            =   113
         TabIndex        =   1
         Top             =   660
         Width           =   11085
         _Version        =   65536
         _ExtentX        =   19553
         _ExtentY        =   10186
         Caption         =   "AIM_JobLog.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_JobLog.frx":0376
         Key             =   "AIM_JobLog.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLangID 
         Bindings        =   "AIM_JobLog.frx":03D8
         Height          =   345
         Left            =   3210
         TabIndex        =   0
         Top             =   240
         Width           =   1395
         DataFieldList   =   "LangId"
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2461
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "LangID"
      End
      Begin VB.Label lblLangID 
         Caption         =   "Language ID"
         Height          =   300
         Left            =   240
         TabIndex        =   3
         Top             =   285
         Width           =   2850
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbAIMJobLog 
      Left            =   10800
      Top             =   6480
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Tools           =   "AIM_JobLog.frx":03EF
      ToolBars        =   "AIM_JobLog.frx":1D73
   End
End
Attribute VB_Name = "AIM_JobLog"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
Dim rsLanguages As ADODB.Recordset

Dim LogFileName As String

Private Sub dcLangID_Click()
On Error GoTo ErrorHandler
    
    Dim LocaleID As Integer
    
    'LocaleID = getLocaleID(dcLangID.Value)
    rsLanguages.MoveFirst
    rsLanguages.Find "LangID = '" & Trim(dcLangID.Value) & "'", , adSearchForward, 0
    If rsLanguages.eof Then
        LocaleID = getLocaleID(dcLangID.Value)
    Else
        LocaleID = rsLanguages!DecimalValue
    End If
    dcLangID.Text = rsLanguages!LangID
    
    SetProperFont txtLogFile.Font, LocaleID
    
    If Not Printer Is Nothing Then
        Printer.Font.Charset = Me.txtLogFile.Font.Charset
        Printer.Font.Name = Me.txtLogFile.Font.Name
        Printer.Font.Size = Me.txtLogFile.Font.Size
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangID_Click)"
	 f_HandleErr , , , "AIM_JobLog::dcLangID_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLangId_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dcLangID.Columns(0).Caption = getTranslationResource("Language ID")
    Me.dcLangID.Columns(0).Width = 1000
    Me.dcLangID.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLangID.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLangID.Columns(0).DataField = "LangID"
    
    Me.dcLangID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLangID.Columns(1).Width = 2000
    Me.dcLangID.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLangID.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLangID.Columns(1).DataField = "LangDesc"
        
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLangID, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcLangID.Columns.Count - 1
'        dcLangID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLangID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangID_InitColumnProps)"
	f_HandleErr , , , "AIM_JobLog::dcLangID_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

'Private Sub ckUnicode_Click()
'On Error GoTo ErrorHandler
'
'    If ckUnicode.Value = vbChecked Then
'        'It's an english env, display text in DBCS
'        txtLogFile.Font.Charset = 128   'DBCS
'        If txtLogFile.Font.Size < 9 Then txtLogFile.Font.Size = 9
'    Else
'        txtLogFile.Font.Charset = ckUnicode.Font.Charset
'        txtLogFile.Font.Size = ckUnicode.Font.Size
'    End If
'
'    If Not Printer Is Nothing Then
'        Printer.Font.Charset = Me.txtLogFile.Font.Charset
'        Printer.Font.Name = Me.txtLogFile.Font.Name
'        Printer.Font.Size = Me.txtLogFile.Font.Size
'    End If
'
'Exit Sub
'ErrorHandler:
'    If Err.Number = 482 Then
'        Resume Next
'    Else
'        f_HandleErr Me.Caption & "(ckUnicode_Click)"
'    End If
'End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
	 f_HandleErr , , , "AIM_JobLog::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim rsSysCtrl As ADODB.Recordset
    
    'Display status message
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02500")
    If StrComp(strMessage, "STATMSG02500") = 0 Then strMessage = "Initializing AIM Job Log..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    If RtnCode <> SUCCEED Then Exit Sub

    'Initialize the System Control Table Record Set
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
    End With
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
        
    LogFileName = rsSysCtrl("LogFile").Value
    
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'If Trim(LogFileName) = "" Then
       ' Exit Sub
   ' Else
        Me.txtLogFile.Text = f_GetLogFileText(LogFileName)
        Me.Caption = Me.Caption + " (" + LogFileName + ")"
    'End If
    
'    If gLocaleID = g_LOCALEID_EN_US _
'    Then
'        ckUnicode.Value = vbUnchecked
'        ckUnicode.Enabled = True
'    Else
'        ckUnicode.Value = vbChecked
'    End If
        
    g_FetchLanguages gLocaleID, rsLanguages
    If f_IsRecordsetOpenAndPopulated(rsLanguages) Then
        SetLanguages
        If rsLanguages.RecordCount > 1 Then dcLangID.Visible = True
    Else
        dcLangID.Visible = False
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
	f_HandleErr , , , "AIM_JobLog::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Resize()
On Error GoTo ErrorHandler

    If (Me.ScaleWidth >= 4775 And Me.ScaleHeight >= 1000) Then
        Frame1.Height = (Me.ScaleHeight - 100) - Me.tbAIMJobLog.GetDockHeight(ssDockedTop)
        Frame1.Width = Me.ScaleWidth - 100
        Me.txtLogFile.Height = (Me.Frame1.Height - 400) - (Me.dcLangID.Height)
        Me.txtLogFile.Width = Me.Frame1.Width - 250
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Resize)"
	 f_HandleErr , , , "AIM_JobLog::Form_Resize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsLanguages) Then rsLanguages.Close
    Set rsLanguages = Nothing
    
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
		 f_HandleErr , , , "AIM_JobLog::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub tbAIMJobLog_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    Select Case Tool.ID
    Case "ID_Print"
        Me.MousePointer = vbHourglass
        strMessage = getTranslationResource("STATMSG02501")
        If StrComp(strMessage, "STATMSG02501") = 0 Then strMessage = "Printing AIM Log File..."
        Write_Message strMessage
        
        If Not (Printer Is Nothing) Then
            'Localize printer
            Printer.Font.Charset = Me.txtLogFile.Font.Charset
            Printer.Font.Name = Me.txtLogFile.Font.Name
            Printer.Font.Size = Me.txtLogFile.Font.Size
            'Print
            Printer.Print Me.txtLogFile.Text
            Printer.EndDoc
        End If
        
        Write_Message ""
        Me.MousePointer = vbNormal
    
    Case "ID_Close"
        Unload Me
    
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbAIMJobLog_ToolClick)"
	 f_HandleErr , , , "AIM_JobLog::tbAIMJobLog_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function f_GetLogFileText(p_LogFileName As String) As String
On Error GoTo ErrorHandler
    Dim FileSystemObject As Object
    Dim FileToRead As Object
    Dim strFileText As String
    Dim strMessage As String
    Dim FileFormat As Long
    
    Const FOR_READING = 1
    Const FOR_WRITING = 2
    Const FOR_APPENDING = 8
    
    Const TRISTATE_FALSE = 0
    Const TRISTATE_TRUE = -1
    Const TRISTATE_USEDEFAULT = -2
    
    'Create the File System object
    Set FileSystemObject = CreateObject("Scripting.FileSystemObject")

    If FileSystemObject.FileExists(p_LogFileName) Then
        'Get File
        FileFormat = TRISTATE_USEDEFAULT
        Set FileToRead = FileSystemObject.OpenTextFile(p_LogFileName, FOR_READING, False, FileFormat)
        Do
            If strFileText = "" Then
                strFileText = strFileText & FileToRead.ReadLine
            Else
                strFileText = strFileText & vbCrLf & FileToRead.ReadLine
            End If
        Loop While (FileToRead.AtEndOfLine = False) Or (FileToRead.AtEndOfStream = False)
        
        f_GetLogFileText = strFileText
                    
    Else
        strMessage = getTranslationResource("TEXTMSG02500")
        If StrComp(strMessage, "TEXTMSG02500") = 0 Then strMessage = "Log File empty or nonexistent."
        f_GetLogFileText = strMessage
        
    End If
    
    'Clean Up
    Close
    Set FileToRead = Nothing
    Set FileSystemObject = Nothing
    
Exit Function
ErrorHandler:
    'Store err number before executing other commands
    strFileText = Err.Number
    'Clean up before determine error action
    Close
    Set FileToRead = Nothing
    Set FileSystemObject = Nothing
    'Based on stored err number, take approp. action
    If strFileText = 62 Then
        strMessage = getTranslationResource("TEXTMSG02500")
        If StrComp(strMessage, "TEXTMSG02500") = 0 Then strMessage = "Log File empty or nonexistent."
        f_GetLogFileText = strMessage
        Exit Function
    Else
        'Err.Raise Err.Number, Err.source, Err.Description & "(f_GetLogFileText)"
		 f_HandleErr , , , "AIM_JobLog::f_GetLogFileText", Now, gDRGeneralError, True, Err
    End If
End Function

Private Function SetLanguages()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Bind the Data Combo Drop Downs
    If f_IsRecordsetOpenAndPopulated(rsLanguages) Then
        rsLanguages.MoveFirst
        For IndexCounter = 0 To rsLanguages.RecordCount - 1
            Me.dcLangID.AddItem rsLanguages!LangID + vbTab + getTranslationResource(rsLanguages!LangDesc)
            rsLanguages.MoveNext
        Next
    End If

    Me.dcLangID.Text = gLangID
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(f_GetLogFileText)"
	 f_HandleErr , , , "AIM_JobLog::f_GetLogFileText", Now, gDRGeneralError, True, Err
End Function

