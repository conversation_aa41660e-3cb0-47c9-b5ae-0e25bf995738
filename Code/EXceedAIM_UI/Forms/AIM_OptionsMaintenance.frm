VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0"; "sstabs2.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_OptionsMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Options Maintenance"
   ClientHeight    =   5655
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   11700
   Icon            =   "AIM_OptionsMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   5655
   ScaleWidth      =   11700
   ShowInTaskbar   =   0   'False
   Begin ActiveTabs.SSActiveTabs atAIMOptions 
      Height          =   4455
      Left            =   60
      TabIndex        =   2
      Top             =   750
      Width           =   11535
      _ExtentX        =   20346
      _ExtentY        =   7858
      _Version        =   131083
      TabCount        =   3
      TagVariant      =   ""
      Tabs            =   "AIM_OptionsMaintenance.frx":030A
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel3 
         Height          =   4065
         Left            =   30
         TabIndex        =   44
         Top             =   360
         Width           =   11475
         _ExtentX        =   20241
         _ExtentY        =   7170
         _Version        =   131083
         TabGuid         =   "AIM_OptionsMaintenance.frx":0407
         Begin VB.Frame Frame4 
            Height          =   3975
            Left            =   40
            TabIndex        =   67
            Top             =   30
            Width           =   11355
            Begin TDBNumber6Ctl.TDBNumber txtDft_TurnLow 
               Height          =   345
               Left            =   3465
               TabIndex        =   35
               Top             =   240
               Width           =   1125
               _Version        =   65536
               _ExtentX        =   1984
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":042F
               Caption         =   "AIM_OptionsMaintenance.frx":044F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":04BB
               Keys            =   "AIM_OptionsMaintenance.frx":04D9
               Spin            =   "AIM_OptionsMaintenance.frx":0523
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.0;-##0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDft_TurnHigh 
               Height          =   345
               Left            =   3465
               TabIndex        =   36
               Top             =   600
               Width           =   1125
               _Version        =   65536
               _ExtentX        =   1984
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":054B
               Caption         =   "AIM_OptionsMaintenance.frx":056B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":05D7
               Keys            =   "AIM_OptionsMaintenance.frx":05F5
               Spin            =   "AIM_OptionsMaintenance.frx":063F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "##0.0;-##0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDSer 
               Height          =   345
               Left            =   3465
               TabIndex        =   37
               Top             =   960
               Width           =   1125
               _Version        =   65536
               _ExtentX        =   1984
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":0667
               Caption         =   "AIM_OptionsMaintenance.frx":0687
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":06F3
               Keys            =   "AIM_OptionsMaintenance.frx":0711
               Spin            =   "AIM_OptionsMaintenance.frx":075B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.999
               MinValue        =   0.7
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtHiMADP 
               Height          =   345
               Left            =   3465
               TabIndex        =   38
               Top             =   1320
               Width           =   1125
               _Version        =   65536
               _ExtentX        =   1984
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":0783
               Caption         =   "AIM_OptionsMaintenance.frx":07A3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":080F
               Keys            =   "AIM_OptionsMaintenance.frx":082D
               Spin            =   "AIM_OptionsMaintenance.frx":0877
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9.9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLoMADP 
               Height          =   345
               Left            =   3465
               TabIndex        =   39
               Top             =   1680
               Width           =   1125
               _Version        =   65536
               _ExtentX        =   1984
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":089F
               Caption         =   "AIM_OptionsMaintenance.frx":08BF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":092B
               Keys            =   "AIM_OptionsMaintenance.frx":0949
               Spin            =   "AIM_OptionsMaintenance.frx":0993
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label label3 
               Caption         =   "Low MAD Percentage"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   72
               Top             =   1725
               Width           =   3285
            End
            Begin VB.Label label3 
               Caption         =   "High MAD Percentage"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   71
               Top             =   1365
               Width           =   3285
            End
            Begin VB.Label label3 
               Caption         =   "Service Level"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   70
               Top             =   1005
               Width           =   3285
            End
            Begin VB.Label label3 
               Caption         =   "Min Turns Target"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   69
               Top             =   285
               Width           =   3285
            End
            Begin VB.Label label3 
               Caption         =   "Max Turns Target"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   68
               Top             =   645
               Width           =   3285
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel2 
         Height          =   4065
         Left            =   30
         TabIndex        =   43
         Top             =   360
         Width           =   11475
         _ExtentX        =   20241
         _ExtentY        =   7170
         _Version        =   131083
         TabGuid         =   "AIM_OptionsMaintenance.frx":09BB
         Begin VB.Frame Frame2 
            Height          =   3975
            Left            =   40
            TabIndex        =   46
            Top             =   30
            Width           =   11355
            Begin VB.CheckBox ckBypDDU 
               Caption         =   "Bypass Demand Update"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   6360
               TabIndex        =   19
               Top             =   2880
               Width           =   4530
            End
            Begin TDBNumber6Ctl.TDBNumber txtDamp 
               Height          =   345
               Left            =   4785
               TabIndex        =   4
               Top             =   600
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":09E3
               Caption         =   "AIM_OptionsMaintenance.frx":0A03
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":0A6F
               Keys            =   "AIM_OptionsMaintenance.frx":0A8D
               Spin            =   "AIM_OptionsMaintenance.frx":0AD7
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtMinSmk 
               Height          =   345
               Left            =   4785
               TabIndex        =   5
               Top             =   960
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":0AFF
               Caption         =   "AIM_OptionsMaintenance.frx":0B1F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":0B8B
               Keys            =   "AIM_OptionsMaintenance.frx":0BA9
               Spin            =   "AIM_OptionsMaintenance.frx":0BF3
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtMaxSmk 
               Height          =   345
               Left            =   4785
               TabIndex        =   6
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":0C1B
               Caption         =   "AIM_OptionsMaintenance.frx":0C3B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":0CA7
               Keys            =   "AIM_OptionsMaintenance.frx":0CC5
               Spin            =   "AIM_OptionsMaintenance.frx":0D0F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtIntSmk 
               Height          =   345
               Left            =   4785
               TabIndex        =   7
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":0D37
               Caption         =   "AIM_OptionsMaintenance.frx":0D57
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":0DC3
               Keys            =   "AIM_OptionsMaintenance.frx":0DE1
               Spin            =   "AIM_OptionsMaintenance.frx":0E2B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2008154113
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtTSL 
               Height          =   345
               Left            =   4785
               TabIndex        =   8
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":0E53
               Caption         =   "AIM_OptionsMaintenance.frx":0E73
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":0EDF
               Keys            =   "AIM_OptionsMaintenance.frx":0EFD
               Spin            =   "AIM_OptionsMaintenance.frx":0F47
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtHiPct 
               Height          =   345
               Left            =   4785
               TabIndex        =   9
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":0F6F
               Caption         =   "AIM_OptionsMaintenance.frx":0F8F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":0FFB
               Keys            =   "AIM_OptionsMaintenance.frx":1019
               Spin            =   "AIM_OptionsMaintenance.frx":1063
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLoPct 
               Height          =   345
               Left            =   4785
               TabIndex        =   10
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":108B
               Caption         =   "AIM_OptionsMaintenance.frx":10AB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1117
               Keys            =   "AIM_OptionsMaintenance.frx":1135
               Spin            =   "AIM_OptionsMaintenance.frx":117F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtMADExk 
               Height          =   345
               Left            =   10380
               TabIndex        =   13
               Top             =   600
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":11A7
               Caption         =   "AIM_OptionsMaintenance.frx":11C7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1233
               Keys            =   "AIM_OptionsMaintenance.frx":1251
               Spin            =   "AIM_OptionsMaintenance.frx":129B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.5
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtTrnSmk 
               Height          =   345
               Left            =   10380
               TabIndex        =   14
               Top             =   960
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":12C3
               Caption         =   "AIM_OptionsMaintenance.frx":12E3
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":134F
               Keys            =   "AIM_OptionsMaintenance.frx":136D
               Spin            =   "AIM_OptionsMaintenance.frx":13B7
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.00;-0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.3
               MinValue        =   0.03
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDIDDL 
               Height          =   345
               Left            =   10380
               TabIndex        =   15
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":13DF
               Caption         =   "AIM_OptionsMaintenance.frx":13FF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":146B
               Keys            =   "AIM_OptionsMaintenance.frx":1489
               Spin            =   "AIM_OptionsMaintenance.frx":14D3
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDITrnDL 
               Height          =   345
               Left            =   10380
               TabIndex        =   16
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":14FB
               Caption         =   "AIM_OptionsMaintenance.frx":151B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1587
               Keys            =   "AIM_OptionsMaintenance.frx":15A5
               Spin            =   "AIM_OptionsMaintenance.frx":15EF
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDITrps 
               Height          =   345
               Left            =   10380
               TabIndex        =   17
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":1617
               Caption         =   "AIM_OptionsMaintenance.frx":1637
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":16A3
               Keys            =   "AIM_OptionsMaintenance.frx":16C1
               Spin            =   "AIM_OptionsMaintenance.frx":170B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDIMADP 
               Height          =   345
               Left            =   10380
               TabIndex        =   18
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":1733
               Caption         =   "AIM_OptionsMaintenance.frx":1753
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":17BF
               Keys            =   "AIM_OptionsMaintenance.frx":17DD
               Spin            =   "AIM_OptionsMaintenance.frx":1827
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtDDMAP 
               Height          =   345
               Left            =   4785
               TabIndex        =   3
               Top             =   240
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":184F
               Caption         =   "AIM_OptionsMaintenance.frx":186F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":18DB
               Keys            =   "AIM_OptionsMaintenance.frx":18F9
               Spin            =   "AIM_OptionsMaintenance.frx":1943
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtMADSmk 
               Height          =   345
               Left            =   10380
               TabIndex        =   12
               Top             =   240
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   609
               Calculator      =   "AIM_OptionsMaintenance.frx":196B
               Caption         =   "AIM_OptionsMaintenance.frx":198B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":19F7
               Keys            =   "AIM_OptionsMaintenance.frx":1A15
               Spin            =   "AIM_OptionsMaintenance.frx":1A5F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0.1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtHiTrndL 
               Height          =   345
               Left            =   4785
               TabIndex        =   11
               Top             =   3120
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":1A87
               Caption         =   "AIM_OptionsMaintenance.frx":1AA7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1B13
               Keys            =   "AIM_OptionsMaintenance.frx":1B31
               Spin            =   "AIM_OptionsMaintenance.frx":1B7B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.00;-0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9.99
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label label1 
               Caption         =   "MAD Extrapolation Constant"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   10
               Left            =   6360
               TabIndex        =   79
               Top             =   645
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "Trend Smoothing Constant"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   11
               Left            =   6360
               TabIndex        =   78
               Top             =   1005
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "Dying Item Demand Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   12
               Left            =   6360
               TabIndex        =   77
               Top             =   1365
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "Dying Item Trend Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   13
               Left            =   6360
               TabIndex        =   76
               Top             =   1725
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "Dying Item Trip Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   14
               Left            =   6360
               TabIndex        =   75
               Top             =   2085
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "Dying Item MAD Percentage Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   15
               Left            =   6360
               TabIndex        =   74
               Top             =   2445
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "MAD Smoothing Constant"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   9
               Left            =   6360
               TabIndex        =   73
               Top             =   285
               Width           =   3930
            End
            Begin VB.Label label1 
               Caption         =   "Initial Moving Average Periods"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   55
               Top             =   285
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "Gamma Damping Factor"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   54
               Top             =   645
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "Min Smoothing Factor"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   53
               Top             =   1005
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "Max Smoothing Factor"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   52
               Top             =   1365
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "Initial Smoothing Factor"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   51
               Top             =   1725
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "Tracking Signal Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   195
               TabIndex        =   50
               Top             =   2085
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "High % Change for Deseasonalized Demand"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   195
               TabIndex        =   49
               Top             =   2445
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "Low % Change for Deseasonalized Demand"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   195
               TabIndex        =   48
               Top             =   2805
               Width           =   4530
            End
            Begin VB.Label label1 
               Caption         =   "High Trend Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   195
               TabIndex        =   47
               Top             =   3165
               Width           =   4530
            End
         End
      End
      Begin ActiveTabs.SSActiveTabPanel SSActiveTabPanel1 
         Height          =   4065
         Left            =   30
         TabIndex        =   42
         Top             =   360
         Width           =   11475
         _ExtentX        =   20241
         _ExtentY        =   7170
         _Version        =   131083
         TabGuid         =   "AIM_OptionsMaintenance.frx":1BA3
         Begin VB.Frame Frame3 
            Height          =   3975
            Left            =   40
            TabIndex        =   56
            Top             =   30
            Width           =   11355
            Begin VB.CheckBox ckApplyDemandFilter 
               Caption         =   "Apply Demand Filters"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   6480
               TabIndex        =   21
               Top             =   255
               Width           =   4680
            End
            Begin VB.CheckBox ckBypIDFL 
               Caption         =   "Bypass Initial Demand Filter Limit"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   6480
               TabIndex        =   24
               Top             =   975
               Width           =   4680
            End
            Begin VB.CheckBox ckBypZSl 
               Caption         =   "Bypass on Zero Sales"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   6480
               TabIndex        =   26
               Top             =   1335
               Width           =   4680
            End
            Begin VB.CheckBox ckBypTFP 
               Caption         =   "Bypass Until First Positive Sale"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   6480
               TabIndex        =   28
               Top             =   1695
               Width           =   4680
            End
            Begin VB.CheckBox ckBypZOH 
               Caption         =   "Bypass Zero On-Hand Inventory"
               BeginProperty DataFormat 
                  Type            =   5
                  Format          =   ""
                  HaveTrueFalseNull=   1
                  TrueValue       =   "True"
                  FalseValue      =   "False"
                  NullValue       =   ""
                  FirstDayOfWeek  =   0
                  FirstWeekOfYear =   0
                  LCID            =   1033
                  SubFormatType   =   7
               EndProperty
               Height          =   300
               Left            =   6480
               TabIndex        =   30
               Top             =   2055
               Width           =   4680
            End
            Begin TDBNumber6Ctl.TDBNumber txtIDFL 
               Height          =   345
               Left            =   4785
               TabIndex        =   20
               Top             =   240
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":1BCB
               Caption         =   "AIM_OptionsMaintenance.frx":1BEB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1C57
               Keys            =   "AIM_OptionsMaintenance.frx":1C75
               Spin            =   "AIM_OptionsMaintenance.frx":1CBF
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtHiDFL 
               Height          =   345
               Left            =   4785
               TabIndex        =   23
               Top             =   960
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":1CE7
               Caption         =   "AIM_OptionsMaintenance.frx":1D07
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1D73
               Keys            =   "AIM_OptionsMaintenance.frx":1D91
               Spin            =   "AIM_OptionsMaintenance.frx":1DDB
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLoDFL 
               Height          =   345
               Left            =   4785
               TabIndex        =   25
               Top             =   1320
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":1E03
               Caption         =   "AIM_OptionsMaintenance.frx":1E23
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1E8F
               Keys            =   "AIM_OptionsMaintenance.frx":1EAD
               Spin            =   "AIM_OptionsMaintenance.frx":1EF7
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtNDFL 
               Height          =   345
               Left            =   4785
               TabIndex        =   27
               Top             =   1680
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":1F1F
               Caption         =   "AIM_OptionsMaintenance.frx":1F3F
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":1FAB
               Keys            =   "AIM_OptionsMaintenance.frx":1FC9
               Spin            =   "AIM_OptionsMaintenance.frx":2013
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   1
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011627525
               Value           =   1
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtCADL 
               Height          =   345
               Left            =   4785
               TabIndex        =   22
               Top             =   600
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":203B
               Caption         =   "AIM_OptionsMaintenance.frx":205B
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":20C7
               Keys            =   "AIM_OptionsMaintenance.frx":20E5
               Spin            =   "AIM_OptionsMaintenance.frx":212F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "##0;-##0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "##0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   999
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBypBef 
               Height          =   345
               Left            =   4785
               TabIndex        =   29
               Top             =   2040
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":2157
               Caption         =   "AIM_OptionsMaintenance.frx":2177
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":21E3
               Keys            =   "AIM_OptionsMaintenance.frx":2201
               Spin            =   "AIM_OptionsMaintenance.frx":224B
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtBypAft 
               Height          =   345
               Left            =   4785
               TabIndex        =   31
               Top             =   2400
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":2273
               Caption         =   "AIM_OptionsMaintenance.frx":2293
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":22FF
               Keys            =   "AIM_OptionsMaintenance.frx":231D
               Spin            =   "AIM_OptionsMaintenance.frx":2367
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   "."
               DisplayFormat   =   "0;-0;0;0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtMinBI 
               Height          =   345
               Left            =   4785
               TabIndex        =   32
               Top             =   2760
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":238F
               Caption         =   "AIM_OptionsMaintenance.frx":23AF
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":241B
               Keys            =   "AIM_OptionsMaintenance.frx":2439
               Spin            =   "AIM_OptionsMaintenance.frx":2483
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.0;-0.0;0.0;0.0"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.0"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.9
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtFilterSmk 
               Height          =   345
               Left            =   4785
               TabIndex        =   33
               Top             =   3120
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":24AB
               Caption         =   "AIM_OptionsMaintenance.frx":24CB
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":2537
               Keys            =   "AIM_OptionsMaintenance.frx":2555
               Spin            =   "AIM_OptionsMaintenance.frx":259F
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.00;-0.00;0.00;0.00"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.00"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   0.5
               MinValue        =   0.05
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   1
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin TDBNumber6Ctl.TDBNumber txtLumpyFilterPct 
               Height          =   345
               Left            =   4785
               TabIndex        =   34
               Top             =   3480
               Width           =   900
               _Version        =   65536
               _ExtentX        =   1587
               _ExtentY        =   600
               Calculator      =   "AIM_OptionsMaintenance.frx":25C7
               Caption         =   "AIM_OptionsMaintenance.frx":25E7
               BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               DropDown        =   "AIM_OptionsMaintenance.frx":2653
               Keys            =   "AIM_OptionsMaintenance.frx":2671
               Spin            =   "AIM_OptionsMaintenance.frx":26BB
               AlignHorizontal =   1
               AlignVertical   =   2
               Appearance      =   1
               BackColor       =   -2147483643
               BorderStyle     =   1
               BtnPositioning  =   1
               ClipMode        =   0
               ClearAction     =   0
               DecimalPoint    =   ""
               DisplayFormat   =   "0.000;-0.000;0.000;0.000"
               EditMode        =   0
               Enabled         =   -1
               ErrorBeep       =   0
               ForeColor       =   -2147483640
               Format          =   "0.000"
               HighlightText   =   0
               MarginBottom    =   3
               MarginLeft      =   3
               MarginRight     =   3
               MarginTop       =   3
               MaxValue        =   1
               MinValue        =   0
               MousePointer    =   0
               MoveOnLRKey     =   0
               NegativeColor   =   255
               OLEDragMode     =   0
               OLEDropMode     =   0
               ReadOnly        =   0
               Separator       =   ""
               ShowContextMenu =   -1
               ValueVT         =   2011955205
               Value           =   0
               MaxValueVT      =   5
               MinValueVT      =   5
            End
            Begin VB.Label label2 
               Caption         =   "Initial Demand Filter Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   0
               Left            =   195
               TabIndex        =   66
               Top             =   285
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Lumpy Filter Percentage"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   9
               Left            =   195
               TabIndex        =   65
               Top             =   3525
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Filter Smoothing Constant"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   8
               Left            =   195
               TabIndex        =   64
               Top             =   3165
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Minimum Seasonality Index"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   7
               Left            =   195
               TabIndex        =   63
               Top             =   2805
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Current Absolute Deviation Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   1
               Left            =   195
               TabIndex        =   62
               Top             =   645
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "High Demand Filter Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   2
               Left            =   195
               TabIndex        =   61
               Top             =   1005
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Low Demand Filter Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   3
               Left            =   195
               TabIndex        =   60
               Top             =   1365
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Demand Filter Hit Limit"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   4
               Left            =   195
               TabIndex        =   59
               Top             =   1725
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Bypass After Promotions"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   6
               Left            =   195
               TabIndex        =   58
               Top             =   2445
               Width           =   4485
            End
            Begin VB.Label label2 
               Caption         =   "Bypass Before Promotions"
               BeginProperty Font 
                  Name            =   "Microsoft Sans Serif"
                  Size            =   8.25
                  Charset         =   0
                  Weight          =   400
                  Underline       =   0   'False
                  Italic          =   0   'False
                  Strikethrough   =   0   'False
               EndProperty
               Height          =   300
               Index           =   5
               Left            =   195
               TabIndex        =   57
               Top             =   2085
               Width           =   4485
            End
         End
      End
   End
   Begin VB.Frame Frame1 
      Height          =   615
      Left            =   60
      TabIndex        =   40
      Top             =   60
      Width           =   11535
      Begin TDBText6Ctl.TDBText txtOptionId 
         Height          =   345
         Left            =   2505
         TabIndex        =   0
         Top             =   180
         Width           =   1380
         _Version        =   65536
         _ExtentX        =   2434
         _ExtentY        =   609
         Caption         =   "AIM_OptionsMaintenance.frx":26E3
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_OptionsMaintenance.frx":274F
         Key             =   "AIM_OptionsMaintenance.frx":276D
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   6
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtOpDesc 
         Height          =   345
         Left            =   7005
         TabIndex        =   1
         Top             =   180
         Width           =   4290
         _Version        =   65536
         _ExtentX        =   7567
         _ExtentY        =   609
         Caption         =   "AIM_OptionsMaintenance.frx":27B1
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_OptionsMaintenance.frx":281D
         Key             =   "AIM_OptionsMaintenance.frx":283B
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Description"
         Height          =   300
         Index           =   1
         Left            =   4650
         TabIndex        =   45
         Top             =   225
         Width           =   2205
      End
      Begin VB.Label Label 
         Caption         =   "Option ID"
         Height          =   300
         Index           =   0
         Left            =   195
         TabIndex        =   41
         Top             =   225
         Width           =   2205
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   240
      Top             =   5310
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   12
      Style           =   0
      Tools           =   "AIM_OptionsMaintenance.frx":287F
      ToolBars        =   "AIM_OptionsMaintenance.frx":B47D
   End
End
Attribute VB_Name = "AIM_OptionsMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_AIMOptions_GetKey_Sp As ADODB.Command
Dim AIM_AIMOptions_GetEq_Sp As ADODB.Command

Dim rsAIMOptions As ADODB.Recordset

Dim AIMOptionBookMark As Variant
Dim OptionId As String

Private Function Refresh_Form()
On Error GoTo ErrorHandler
    
    'Enable/Disable Key fields
    If rsAIMOptions.EditMode = adEditAdd Then
        If gAccessLvl <> 1 Then tbNavigation.Tools("ID_Save").Enabled = True
        Me.tbNavigation.Tools("ID_Delete").Enabled = False
        Me.tbNavigation.Tools("ID_AddNew").Enabled = False
        Me.txtOptionId.Enabled = True
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")
    Else
        'Enable/Disable Update
        If gAccessLvl = 1 Then
            Me.tbNavigation.Tools("ID_Save").Enabled = False
            Me.tbNavigation.Tools("ID_Delete").Enabled = False
            Me.tbNavigation.Tools("ID_AddNew").Enabled = False
        Else
            Me.tbNavigation.Tools("ID_Save").Enabled = True
            Me.tbNavigation.Tools("ID_Delete").Enabled = True
            Me.tbNavigation.Tools("ID_AddNew").Enabled = True
        End If

        Me.txtOptionId.Enabled = False
        Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Edit")
    End If
    
    'Update the Form
    Me.txtOptionId.Text = rsAIMOptions("OptionId").Value
    Me.txtOpDesc.Text = rsAIMOptions("OpDesc").Value
    
    Me.txtIDFL.Value = rsAIMOptions("IDFL").Value
    Me.ckBypIDFL = IIf(rsAIMOptions("BypIDFL").Value = "Y", vbChecked, vbUnchecked)
    Me.txtCADL.Value = rsAIMOptions("CADL").Value
    Me.txtHiDFL.Value = rsAIMOptions("HiDFL").Value
    Me.txtLoDFL.Value = rsAIMOptions("LoDFL").Value
    Me.txtNDFL = rsAIMOptions("NDFL").Value
    Me.txtDDMAP.Value = rsAIMOptions("DDMAP").Value
    Me.txtDamp.Value = rsAIMOptions("DAMP").Value
    Me.txtMinSmk.Value = rsAIMOptions("MinSmk").Value
    Me.txtMaxSmk.Value = rsAIMOptions("MaxSmk").Value
    Me.txtIntSmk.Value = rsAIMOptions("IntSmk").Value
    Me.txtTSL.Value = rsAIMOptions("TSL").Value
    Me.ckBypDDU = IIf(rsAIMOptions("BypDDU").Value = "Y", vbChecked, vbUnchecked)
    Me.ckApplyDemandFilter = IIf(rsAIMOptions("ApplyDmdFilter").Value = "Y", vbChecked, vbUnchecked)
    Me.ckBypZSl = IIf(rsAIMOptions("BypZSl").Value = "Y", vbChecked, vbUnchecked)
    Me.ckBypTFP = IIf(rsAIMOptions("BypTFP").Value = "Y", vbChecked, vbUnchecked)
    Me.ckBypZOH = IIf(rsAIMOptions("BypZOH").Value = "Y", vbChecked, vbUnchecked)
    Me.txtBypBef.Value = rsAIMOptions("BypBef").Value
    Me.txtBypAft.Value = rsAIMOptions("BypAft").Value
    Me.txtMinBI.Value = rsAIMOptions("MinBi").Value
    Me.txtHiPct.Value = rsAIMOptions("hipct").Value
    Me.txtLoPct.Value = rsAIMOptions("LoPct").Value
    Me.txtHiMADP.Value = rsAIMOptions("HiMADP").Value
    Me.txtLoMADP.Value = rsAIMOptions("LoMADP").Value
    Me.txtMADSmk.Value = rsAIMOptions("MADSmk").Value
    Me.txtMADExk.Value = rsAIMOptions("MADExK").Value
    Me.txtTrnSmk.Value = rsAIMOptions("TrnSmk").Value
    Me.txtDIDDL.Value = rsAIMOptions("DIDDL").Value
    Me.txtDITrnDL.Value = rsAIMOptions("DITrnDL").Value
    Me.txtDITrps.Value = rsAIMOptions("DITrps").Value
    Me.txtDIMADP.Value = rsAIMOptions("DIMADP").Value
    Me.txtHiTrndL.Value = rsAIMOptions("hitrndl").Value
    Me.txtFilterSmk.Value = rsAIMOptions("FilterSmk").Value
    Me.txtLumpyFilterPct.Value = rsAIMOptions("LumpyFilterPct").Value
    Me.txtDft_TurnHigh.Value = rsAIMOptions("Dft_TurnHigh").Value
    Me.txtDft_TurnLow.Value = rsAIMOptions("Dft_TurnLow").Value
    Me.txtDSer.Value = rsAIMOptions("Dft_DSer").Value
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(Refresh_Form)", Err.Description
    f_HandleErr , , , "AIM_OptionsMaintenance::Refresh_Form", Now, gDRGeneralError, True, Err
End Function


Private Function f_AIMOptions_InitializeNew()
On Error GoTo ErrorHandler

    rsAIMOptions.AddNew
        
    rsAIMOptions("OptionId").Value = getTranslationResource("New")
    OptionId = ""
    
    rsAIMOptions("OpDesc").Value = ""
    rsAIMOptions("IDFL").Value = 3
    rsAIMOptions("BypIDFL").Value = "N"
    rsAIMOptions("CADL").Value = 5
    rsAIMOptions("HiDFL").Value = 5
    rsAIMOptions("LoDFL").Value = 3
    rsAIMOptions("NDFL").Value = 3
    rsAIMOptions("DDMAP").Value = 6
    rsAIMOptions("DAMP").Value = 0.7
    rsAIMOptions("MinSmk").Value = 0.1
    rsAIMOptions("MaxSmk").Value = 0.5
    rsAIMOptions("IntSmk").Value = 0.1
    rsAIMOptions("TSL").Value = 0.5
    rsAIMOptions("BypDDU").Value = "N"
    rsAIMOptions("ApplyDmdFilter").Value = "Y"
    rsAIMOptions("BypZSl").Value = "N"
    rsAIMOptions("BypTFP").Value = "Y"
    rsAIMOptions("BypZOH").Value = "N"
    rsAIMOptions("BypBef").Value = 1
    rsAIMOptions("BypAft").Value = 1
    rsAIMOptions("MinBi").Value = 0.3
    rsAIMOptions("hipct").Value = 0.5
    rsAIMOptions("LoPct").Value = 0.2
    rsAIMOptions("HiMADP").Value = 9
    rsAIMOptions("LoMADP").Value = 0.1
    rsAIMOptions("MADSmk").Value = 0.1
    rsAIMOptions("MADExK").Value = 0.5
    rsAIMOptions("TrnSmk").Value = 0.08
    rsAIMOptions("DIDDL").Value = 3
    rsAIMOptions("DITrnDL").Value = 0.3
    rsAIMOptions("DITrps").Value = 6
    rsAIMOptions("DIMADP").Value = 0.3
    rsAIMOptions("hitrndl").Value = 1
    rsAIMOptions("FilterSmk").Value = 0.1
    rsAIMOptions("LumpyFilterPct").Value = 0.15
    rsAIMOptions("Dft_TurnHigh").Value = 999#
    rsAIMOptions("Dft_TurnLow").Value = 0
    rsAIMOptions("Dft_DSer").Value = 0.95

    'Enable/Disable Key fields
    Me.txtOptionId.Enabled = True
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, _
                    getTranslationResource("Add")

    Me.tbNavigation.Tools("ID_Delete").Enabled = False
    Me.tbNavigation.Tools("ID_AddNew").Enabled = False
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMOptions_InitializeNew)", Err.Description
    f_HandleErr , , , "AIM_OptionsMaintenance::f_AIMOptions_InitializeNew", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMOptions_Save()
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim strMessage As String
    
    Cn.Errors.Clear
    
    'Validate required info
    RtnCode = UserInputValidation
    If RtnCode = SUCCEED Then
    
        'Save
        rsAIMOptions.Update
    
        If Cn.Errors.Count > 0 Then
            ADOErrorHandler Cn, "Error Updating AIM Option."
        Else
            strMessage = getTranslationResource("STATMSG03501")
            If StrComp(strMessage, "STATMSG03501") = 0 Then strMessage = "AIM Option successfully updated."
            Write_Message strMessage
        End If
    Else
        f_AIMOptions_Save = RtnCode
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMOptions_Save)", Err.Description
    f_HandleErr , , , "AIM_OptionsMaintenance::f_AIMOptions_Save", Now, gDRGeneralError, True, Err
End Function

Private Function f_AIMOptions_Update()
On Error GoTo ErrorHandler

    OptionId = Me.txtOptionId.Text

    rsAIMOptions("OptionId").Value = Me.txtOptionId.Text
    rsAIMOptions("OpDesc").Value = Me.txtOpDesc.Text
    rsAIMOptions("IDFL").Value = Me.txtIDFL.Value
    rsAIMOptions("BypIDFL").Value = IIf(Me.ckBypIDFL = vbChecked, "Y", "N")
    rsAIMOptions("CADL").Value = Me.txtCADL.Value
    rsAIMOptions("HiDFL").Value = Me.txtHiDFL.Value
    rsAIMOptions("LoDFL").Value = Me.txtLoDFL.Value
    rsAIMOptions("NDFL").Value = Me.txtNDFL
    rsAIMOptions("DDMAP").Value = Me.txtDDMAP.Value
    rsAIMOptions("DAMP").Value = Me.txtDamp.Value
    rsAIMOptions("MinSmk").Value = Me.txtMinSmk.Value
    rsAIMOptions("MaxSmk").Value = Me.txtMaxSmk.Value
    rsAIMOptions("IntSmk").Value = Me.txtIntSmk.Value
    rsAIMOptions("TSL").Value = Me.txtTSL.Value
    rsAIMOptions("BypDDU").Value = IIf(Me.ckBypDDU = vbChecked, "Y", "N")
    rsAIMOptions("ApplyDmdFilter").Value = IIf(Me.ckApplyDemandFilter.Value = VALUETRUE, "Y", "N")
    rsAIMOptions("BypZSl").Value = IIf(Me.ckBypZSl = vbChecked, "Y", "N")
    rsAIMOptions("BypTFP").Value = IIf(Me.ckBypTFP = vbChecked, "Y", "N")
    rsAIMOptions("BypZOH").Value = IIf(Me.ckBypZOH = vbChecked, "Y", "N")
    rsAIMOptions("BypBef").Value = Me.txtBypBef.Value
    rsAIMOptions("BypAft").Value = Me.txtBypAft.Value
    rsAIMOptions("MinBi").Value = Me.txtMinBI.Value
    rsAIMOptions("hipct").Value = Me.txtHiPct.Value
    rsAIMOptions("LoPct").Value = Me.txtLoPct.Value
    rsAIMOptions("HiMADP").Value = Me.txtHiMADP.Value
    rsAIMOptions("LoMADP").Value = Me.txtLoMADP.Value
    rsAIMOptions("MADSmk").Value = Me.txtMADSmk.Value
    rsAIMOptions("MADExK").Value = Me.txtMADExk.Value
    rsAIMOptions("TrnSmk").Value = Me.txtTrnSmk.Value
    rsAIMOptions("DIDDL").Value = Me.txtDIDDL.Value
    rsAIMOptions("DITrnDL").Value = Me.txtDITrnDL.Value
    rsAIMOptions("DITrps").Value = Me.txtDITrps.Value
    rsAIMOptions("DIMADP").Value = Me.txtDIMADP.Value
    rsAIMOptions("hitrndl").Value = Me.txtHiTrndL.Value
    rsAIMOptions("FilterSmk").Value = Me.txtFilterSmk.Value
    rsAIMOptions("LumpyFilterPct").Value = Me.txtLumpyFilterPct.Value
    rsAIMOptions("Dft_TurnHigh").Value = Me.txtDft_TurnHigh.Value
    rsAIMOptions("Dft_TurnLow").Value = Me.txtDft_TurnLow.Value
    rsAIMOptions("Dft_DSer").Value = Me.txtDSer.Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(f_AIMOptions_Update)", Err.Description
    f_HandleErr , , , "AIM_OptionsMaintenance::f_AIMOptions_Update", Now, gDRGeneralError, True, Err
End Function

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
    f_HandleErr , , , "AIM_OptionsMaintenance::Form_Activate", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Load()
On Error GoTo ErrorHandler
            
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG03500")
    If StrComp(strMessage, "STATMSG03500") = 0 Then strMessage = "Initializing AIM Forecasting Options Maintenenace..."
    Write_Message strMessage

    GetTranslatedCaptions Me

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
        
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize Stored Procedures
    Set AIM_AIMOptions_GetKey_Sp = New ADODB.Command
    With AIM_AIMOptions_GetKey_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMOptions_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    Set AIM_AIMOptions_GetEq_Sp = New ADODB.Command
    With AIM_AIMOptions_GetEq_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMOptions_GetEq_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the AIM Options Record Set
    Set rsAIMOptions = New ADODB.Recordset
    With rsAIMOptions
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Open the Result Set
    OptionId = ""
    
    RtnCode = AIMOptions_GetKey(AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetFirst)
    RtnCode = AIMOptions_GetEq(AIM_AIMOptions_GetEq_Sp, rsAIMOptions, OptionId)
    
    If f_IsRecordsetOpenAndPopulated(rsAIMOptions) Then
        Me.tbNavigation.ToolBars("tbNavigation").Tools("Id_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    Else
        f_AIMOptions_InitializeNew
    End If

    'Sync. input fields with recordset
     Refresh_Form
           
    'Make the spin button visible
    Me.txtDamp.Spin.Visible = 1
    Me.txtDft_TurnHigh.Spin.Visible = 1
    Me.txtDft_TurnLow.Spin.Visible = 1
    Me.txtDIMADP.Spin.Visible = 1
    Me.txtDITrnDL.Spin.Visible = 1
    Me.txtDSer.Spin.Visible = 1
    Me.txtFilterSmk.Spin.Visible = 1
    Me.txtHiMADP.Spin.Visible = 1
    Me.txtHiPct.Spin.Visible = 1
    Me.txtHiTrndL.Spin.Visible = 1
    Me.txtIntSmk.Spin.Visible = 1
    Me.txtLoMADP.Spin.Visible = 1
    Me.txtLoPct.Spin.Visible = 1
    Me.txtLumpyFilterPct.Spin.Visible = 1
    Me.txtMADExk.Spin.Visible = 1
    Me.txtMADSmk.Spin.Visible = 1
    Me.txtMaxSmk.Spin.Visible = 1
    Me.txtMinBI.Spin.Visible = 1
    Me.txtMinSmk.Spin.Visible = 1
    Me.txtTrnSmk.Spin.Visible = 1
    Me.txtTSL.Spin.Visible = 1
    Me.txtDDMAP.Spin.Visible = 1
    Me.txtDIDDL.Spin.Visible = 1
    Me.txtDITrps.Spin.Visible = 1
    Me.txtIDFL.Spin.Visible = 1
    Me.txtLoDFL.Spin.Visible = 1
    Me.txtHiDFL.Spin.Visible = 1
    Me.txtCADL.Spin.Visible = 1
    Me.txtNDFL.Spin.Visible = 1
    Me.txtBypBef.Spin.Visible = 1
    Me.txtBypAft.Spin.Visible = 1
        
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
    f_HandleErr , , , "AIM_OptionsMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMOptions) Then rsAIMOptions.Close
    Set rsAIMOptions = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_OptionsMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub


Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strText As String
    Dim strMessage As String

    'Clear Errors
    Cn.Errors.Clear
    
    Write_Message ""
    
    RtnCode = f_AIMOptions_Update           'Update the recordset

    'Alert user to possible change
    Select Case Tool.ID
    Case "ID_AddNew", "ID_Close", "ID_GoToBookMark", "ID_LookUp", _
    "ID_GetFirst", "ID_GetLast", "ID_GetNext", "ID_GetPrev"
        If gAccessLvl <> 1 And DataChanged(rsAIMOptions, adAffectCurrent) > 0 Then
            strMessage = getTranslationResource("MSGBOX03503")
            If StrComp(strMessage, "MSGBOX03503") = 0 Then strMessage = "Abandon changes to current record?"
            RtnCode = MsgBox(strMessage, vbYesNo + vbQuestion, strText)
            If RtnCode = vbYes Then
 '               rsAIMOptions.CancelUpdate
            Else
                f_AIMOptions_Save
            End If
        
        End If
    End Select
    
    'Navigate
    Select Case Tool.ID
    Case "ID_AddNew"
        f_AIMOptions_InitializeNew
    
    Case "ID_Delete"
        strMessage = getTranslationResource("MSGBOX03504")
        If StrComp(strMessage, "MSGBOX03504") = 0 Then strMessage = "Delete the current Option?"
        RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, strText)
        If RtnCode = vbYes Then
            rsAIMOptions.Delete
            If AIMOptions_GetKey(AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_Getlt) <> SUCCEED Then
                If AIMOptions_GetKey(AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetFirst) <> SUCCEED Then
                    f_AIMOptions_InitializeNew
                End If
            End If
        
        End If

    Case "ID_GetFirst"
        AIMOptions_GetKey AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetFirst
    
    Case "ID_GetPrev"
        AIMOptions_GetKey AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_Getlt
        
    Case "ID_GetNext"
        AIMOptions_GetKey AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetGT
        
    Case "ID_GetLast"
        AIMOptions_GetKey AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetLast
    
    Case "ID_Save"
        f_AIMOptions_Save
        
    Case "ID_SetBookMark"
        AIMOptionBookMark = OptionId
        Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        
    Case "ID_GoToBookMark"
        OptionId = AIMOptionBookMark
        AIMOptions_GetKey AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetEq
    
    Case "ID_LookUp"
        AIM_OptionsLookUp.OptionIdKey = Me.txtOptionId.Text
        Set AIM_OptionsLookUp.Cn = Cn
        AIM_OptionsLookUp.Show vbModal

        If Not AIM_OptionsLookUp.CancelFlag Then
            OptionId = AIM_OptionsLookUp.OptionIdKey
            AIMOptions_GetKey AIM_AIMOptions_GetKey_Sp, rsAIMOptions, OptionId, SQL_GetEq

        End If
        
    Case "ID_Close"
        Unload Me
        Exit Sub

    End Select

    Refresh_Form        'Refresh the form
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
    f_HandleErr , , , "AIM_OptionsMaintenance::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function UserInputValidation() As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim MessageRequired As Boolean
    
    Dim RtnCode As String
    
    strMessage = getTranslationResource("MSGBOX07312")
    If StrComp(strMessage, "MSGBOX07312") = 0 Then _
                strMessage = "The following data is required, or is invalid. Please provide correct values in the expected format for: "
    
    'Start validating
    If Trim(txtOptionId.Text) = "" _
    Or StrComp(txtOptionId.Text, getTranslationResource("New")) = 0 _
    Then
        strMessage = strMessage & vbCrLf & _
                    Label(0).Caption  'Option ID
        txtOptionId.SetFocus
        MessageRequired = True
    End If
    
    If MessageRequired Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        UserInputValidation = FAIL
    Else
        UserInputValidation = SUCCEED
    End If
            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(UserInputValidation)", Err.Description
    f_HandleErr , , , "AIM_OptionsMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function
