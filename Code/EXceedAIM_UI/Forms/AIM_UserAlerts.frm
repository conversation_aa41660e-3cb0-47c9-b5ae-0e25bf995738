VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_UserAlerts 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Buyer Alerts"
   ClientHeight    =   8625
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   7050
   HelpContextID   =   1
   Icon            =   "AIM_UserAlerts.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   8625
   ScaleWidth      =   7050
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   8280
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Style           =   0
      Tools           =   "AIM_UserAlerts.frx":030A
      ToolBars        =   "AIM_UserAlerts.frx":1C9D
   End
   Begin VB.Frame fraUserAlerts 
      Height          =   8175
      Left            =   120
      TabIndex        =   15
      Top             =   0
      Width           =   6855
      Begin VB.CheckBox ckLVCostAlert 
         Caption         =   "Lower Vendor Cost Alert"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   12
         Top             =   6435
         Width           =   5160
      End
      Begin VB.CheckBox ckAltSource 
         Caption         =   "Alternate Source"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   11
         Top             =   6078
         Width           =   5160
      End
      Begin VB.CheckBox ckDmdUpdExcpts 
         Caption         =   "Demand Update Exceptions"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   0
         Top             =   1230
         Value           =   1  'Checked
         Width           =   5160
      End
      Begin VB.CheckBox ckInvAvailRestriction 
         Caption         =   "Restrict Inventory Availability List"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   13
         Top             =   6795
         Width           =   5160
      End
      Begin VB.CheckBox ckBackOrdered 
         Caption         =   "Back-ordered"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   10
         Top             =   5721
         Width           =   5160
      End
      Begin VB.CheckBox ckSafetyStockEroded 
         Caption         =   "Safety Stock Eroded"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   9
         Top             =   5364
         Width           =   5160
      End
      Begin VB.CheckBox ckOverDuePO 
         Caption         =   "Overdue Purchase Order"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   8
         Top             =   5007
         Width           =   5160
      End
      Begin VB.CheckBox ckLastWkSaleExcpts 
         Caption         =   "Sale Last Week"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   7
         Top             =   4650
         Width           =   5160
      End
      Begin VB.CheckBox ckPackSizeExcepts 
         Caption         =   "Pack Size Rounding"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   4
         Top             =   3375
         Width           =   5160
      End
      Begin VB.CheckBox ckExtCostExcpts 
         Caption         =   "Extended Cost"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   2
         Top             =   2235
         Width           =   5160
      End
      Begin VB.CheckBox ckOnPromotionExcpts 
         Caption         =   "On Promotion"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   1
         Top             =   1620
         Width           =   5160
      End
      Begin TDBText6Ctl.TDBText txtUserId 
         Height          =   345
         Left            =   2750
         TabIndex        =   16
         TabStop         =   0   'False
         Top             =   240
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   609
         Caption         =   "AIM_UserAlerts.frx":1D5A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UserAlerts.frx":1DC6
         Key             =   "AIM_UserAlerts.frx":1DE4
         BackColor       =   -2147483633
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   0
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBNumber6Ctl.TDBNumber txtExtCostLimit 
         Height          =   345
         Left            =   3510
         TabIndex        =   3
         Top             =   2580
         Width           =   1380
         _Version        =   65536
         _ExtentX        =   2434
         _ExtentY        =   609
         Calculator      =   "AIM_UserAlerts.frx":1E28
         Caption         =   "AIM_UserAlerts.frx":1E48
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UserAlerts.frx":1EB4
         Keys            =   "AIM_UserAlerts.frx":1ED2
         Spin            =   "AIM_UserAlerts.frx":1F1C
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "######0.00;-######0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "######0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin TDBNumber6Ctl.TDBNumber txtPS_ExtCostLimit 
         Height          =   345
         Left            =   3510
         TabIndex        =   6
         Top             =   4095
         Width           =   1380
         _Version        =   65536
         _ExtentX        =   2434
         _ExtentY        =   609
         Calculator      =   "AIM_UserAlerts.frx":1F44
         Caption         =   "AIM_UserAlerts.frx":1F64
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UserAlerts.frx":1FD0
         Keys            =   "AIM_UserAlerts.frx":1FEE
         Spin            =   "AIM_UserAlerts.frx":2038
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "######0.00;-######0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "######0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   9999999.99
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   1479671813
      End
      Begin TDBNumber6Ctl.TDBNumber txtPS_PctRoundLimit 
         Height          =   345
         Left            =   3510
         TabIndex        =   5
         Top             =   3720
         Width           =   1380
         _Version        =   65536
         _ExtentX        =   2434
         _ExtentY        =   609
         Calculator      =   "AIM_UserAlerts.frx":2060
         Caption         =   "AIM_UserAlerts.frx":2080
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Microsoft Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UserAlerts.frx":20EC
         Keys            =   "AIM_UserAlerts.frx":210A
         Spin            =   "AIM_UserAlerts.frx":2154
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "0.00;-0.00;0.00;0.00"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "0.00"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2012741633
         Value           =   0
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBText6Ctl.TDBText txtUserName 
         Height          =   345
         Left            =   2750
         TabIndex        =   22
         TabStop         =   0   'False
         Top             =   645
         Width           =   3480
         _Version        =   65536
         _ExtentX        =   6138
         _ExtentY        =   609
         Caption         =   "AIM_UserAlerts.frx":217C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UserAlerts.frx":21E8
         Key             =   "AIM_UserAlerts.frx":2206
         BackColor       =   -2147483633
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   0
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   30
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtInvAvailList 
         Height          =   840
         Left            =   200
         TabIndex        =   14
         Top             =   7200
         Width           =   6480
         _Version        =   65536
         _ExtentX        =   11430
         _ExtentY        =   1482
         Caption         =   "AIM_UserAlerts.frx":224A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_UserAlerts.frx":22B6
         Key             =   "AIM_UserAlerts.frx":22D4
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "User ID"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   21
         Top             =   285
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "User Name"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   20
         Top             =   690
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Extended Cost Limit"
         Height          =   300
         Index           =   4
         Left            =   645
         TabIndex        =   19
         Top             =   4140
         Width           =   2775
      End
      Begin VB.Label Label 
         Caption         =   "Extended Cost Limit"
         Height          =   300
         Index           =   2
         Left            =   645
         TabIndex        =   18
         Top             =   2625
         Width           =   2775
      End
      Begin VB.Label Label 
         Caption         =   "Percentage Rounding Limit"
         Height          =   300
         Index           =   3
         Left            =   645
         TabIndex        =   17
         Top             =   3765
         Width           =   2775
      End
   End
End
Attribute VB_Name = "AIM_UserAlerts"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public p_rsAIMUsers As ADODB.Recordset

Private Function RefreshForm()
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(p_rsAIMUsers) Then Exit Function
    
    'Assign variables to form
    Me.txtUserId.Text = p_rsAIMUsers("UserId").Value
    Me.txtUserName.Text = p_rsAIMUsers("UserName").Value
    
    Me.ckDmdUpdExcpts = IIf(p_rsAIMUsers("DmdUpdExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.ckOnPromotionExcpts = IIf(p_rsAIMUsers("OnPromotionExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.ckExtCostExcpts = IIf(p_rsAIMUsers("ExtCostExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.txtExtCostLimit.Value = p_rsAIMUsers("ExtCostLimit").Value
    Me.ckPackSizeExcepts = IIf(p_rsAIMUsers("PackSizeExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.txtPS_PctRoundLimit.Value = p_rsAIMUsers("PS_PctRoundLimit").Value
    Me.txtPS_ExtCostLimit.Value = p_rsAIMUsers("PS_ExtCostLimit").Value
    Me.ckLastWkSaleExcpts = IIf(p_rsAIMUsers("LastWeekSaleExcpts").Value = "Y", vbChecked, vbUnchecked)
    Me.ckOverDuePO = IIf(p_rsAIMUsers("OverDuePO").Value = "Y", vbChecked, vbUnchecked)
    Me.ckSafetyStockEroded = IIf(p_rsAIMUsers("SafetyStockEroded").Value = "Y", vbChecked, vbUnchecked)
    Me.ckBackOrdered = IIf(p_rsAIMUsers("BackOrdered").Value = "Y", vbChecked, vbUnchecked)
    Me.ckInvAvailRestriction = IIf(p_rsAIMUsers("InvAvailRestriction").Value = "Y", vbChecked, vbUnchecked)
    Me.txtInvAvailList.Text = p_rsAIMUsers("InvAvailList").Value
    
    Me.ckAltSource = IIf(p_rsAIMUsers("AltSourceExcpts").Value = "Y", vbChecked, vbUnchecked)   'Mohammed - MultiVendor
    Me.ckLVCostAlert = IIf(p_rsAIMUsers("VendorCostExcpts").Value = "Y", vbChecked, vbUnchecked)
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(RefreshForm)", Err.Description
     f_HandleErr , , , "AIM_UserAlerts:::RefreshForm", Now, gDRGeneralError, True, Err
End Function

Private Function AIMUsersUpdate()
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(p_rsAIMUsers) Then Exit Function
        
    p_rsAIMUsers("DmdUpdExcpts").Value = IIf(Me.ckDmdUpdExcpts = vbChecked, "Y", "N")
    p_rsAIMUsers("OnPromotionExcpts").Value = IIf(Me.ckOnPromotionExcpts = vbChecked, "Y", "N")
    p_rsAIMUsers("ExtCostExcpts").Value = IIf(Me.ckExtCostExcpts = vbChecked, "Y", "N")
    p_rsAIMUsers("ExtCostLimit").Value = Me.txtExtCostLimit.Value
    p_rsAIMUsers("PackSizeExcpts").Value = IIf(Me.ckPackSizeExcepts = vbChecked, "Y", "N")
    p_rsAIMUsers("PS_PctRoundLimit").Value = Me.txtPS_PctRoundLimit.Value
    p_rsAIMUsers("PS_ExtCostLimit").Value = Me.txtPS_ExtCostLimit.Value
    p_rsAIMUsers("LastWeekSaleExcpts").Value = IIf(Me.ckLastWkSaleExcpts = vbChecked, "Y", "N")
    p_rsAIMUsers("OverDuePO").Value = IIf(Me.ckOverDuePO = vbChecked, "Y", "N")
    p_rsAIMUsers("SafetyStockEroded").Value = IIf(Me.ckSafetyStockEroded = vbChecked, "Y", "N")
    p_rsAIMUsers("BackOrdered").Value = IIf(Me.ckBackOrdered = vbChecked, "Y", "N")
    p_rsAIMUsers("InvAvailRestriction").Value = IIf(Me.ckInvAvailRestriction = vbChecked, "Y", "N")
    p_rsAIMUsers("InvAvailList").Value = Me.txtInvAvailList.Text
    p_rsAIMUsers("AltSourceExcpts").Value = IIf(Me.ckAltSource = vbChecked, "Y", "N") 'Mohammed - MultiVendor
    p_rsAIMUsers("VendorCostExcpts").Value = IIf(Me.ckLVCostAlert = vbChecked, "Y", "N")
    p_rsAIMUsers.Update

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUsersUpdate)", Err.Description
     f_HandleErr , , , "AIM_UserAlerts:::AIMUsersUpdate", Now, gDRGeneralError, True, Err
End Function

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05800")
    If StrComp(strMessage, "STATMSG05800") = 0 Then strMessage = "Initializing Buyer Alerts... "
    Write_Message strMessage
    
    GetTranslatedCaptions Me
    
    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(p_rsAIMUsers) Then
        RefreshForm
    End If
    
    'Make the spin button visible
    Me.txtExtCostLimit.Spin.Visible = 1
    Me.txtPS_PctRoundLimit.Spin.Visible = 1
    Me.txtPS_ExtCostLimit.Spin.Visible = 1
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_UserAlerts:::Form_Load", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    'Save before exit
    AIMUsersUpdate
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Unload)"
     f_HandleErr , , , "AIM_UserAlerts:::Form_Unload", Now, gDRGeneralError, True, Err
    Resume Next
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Select Case Tool.ID
        Case "ID_Save"
            AIMUsersUpdate
        
        Case "ID_Close"
            Unload Me
            
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_UserAlerts:::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub
