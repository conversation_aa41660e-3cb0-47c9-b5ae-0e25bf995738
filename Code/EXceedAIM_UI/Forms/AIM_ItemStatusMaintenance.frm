VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItemStatusMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Item Status Maintenance"
   ClientHeight    =   5550
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10830
   Icon            =   "AIM_ItemStatusMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   5550
   ScaleWidth      =   10830
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbItStatus 
      Left            =   180
      Top             =   5160
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_ItemStatusMaintenance.frx":030A
      ToolBars        =   "AIM_ItemStatusMaintenance.frx":9BB2
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgItStatus 
      Align           =   1  'Align Top
      Height          =   5115
      Left            =   0
      TabIndex        =   0
      Top             =   0
      Width           =   10830
      _Version        =   196617
      DataMode        =   1
      AllowAddNew     =   -1  'True
      AllowDelete     =   -1  'True
      AllowColumnSwapping=   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   19103
      _ExtentY        =   9022
      _StockProps     =   79
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ItemStatusMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsItStatus As New ADODB.Recordset
Dim ItStatBookMark As Variant

Private Sub dgItStatus_AfterInsert(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    If Cn.Errors.Count > 0 Then
        RtnDispErrMsg = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_AfterInsert)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_AfterInsert", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    InvalidData = UserInputValidation(strMessage, ColIndex)
    
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
    End If
    
    Cancel = InvalidData
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_BeforeColUpdate)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
On Error GoTo ErrorHandler
    
    Dim Result As Long
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Cancel the default prompt
    DispPromptMsg = False
    
    'Display a confirmation msgbox
    strMessage = getTranslationResource("MSGBOX02200")
    If StrComp(strMessage, "MSGBOX02200") = 0 Then strMessage = "Delete the"
    
    strMessage = strMessage + " " + str(dgItStatus.SelBookmarks.Count) + " "
    strMessage1 = getTranslationResource("MSGBOX02201")
    If StrComp(strMessage1, "MSGBOX02201") = 0 Then strMessage1 = "selected Item Status(es)?"
    strMessage = strMessage + strMessage1

    Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ItemStatusMaintenance.Caption))

    Select Case Result
    Case vbYes
        'User chose to delete. Do nothing
    Case vbNo
        'User chose to Cancel
        Cancel = True
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_BeforeDelete)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    InvalidData = UserInputValidation(strMessage)
    
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
    End If
    
    Cancel = InvalidData
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_BeforeUpdate)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsItStatus)) Then Exit Sub
    
    'Check for unsortable column index
    '-- All columns as of v4.4 are sortable.
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
    
    If InStr(rsItStatus.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgItStatus.Columns(ColIndex).Name
    ElseIf InStr(rsItStatus.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgItStatus.Columns(ColIndex).Name
    End If
    
    'Sort grid by selected column
    rsItStatus.Sort = Trim(ColName & " " & SortSeq)

    Me.dgItStatus.ReBind
    If f_IsRecordsetOpenAndPopulated(rsItStatus) Then Me.dgItStatus.Rows = rsItStatus.RecordCount
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_HeadClick)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Define Columns
    Me.dgItStatus.Columns(0).Name = "itstat"
    Me.dgItStatus.Columns(0).Caption = getTranslationResource("Status")
    Me.dgItStatus.Columns(0).Width = 800
    Me.dgItStatus.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgItStatus.Columns(0).Style = ssStyleEdit
    Me.dgItStatus.Columns(0).Case = ssCaseUpper
    Me.dgItStatus.Columns(0).FieldLen = 1
    Me.dgItStatus.Columns(0).DataType = vbString
    
    Me.dgItStatus.Columns(1).Name = "itstatdesc"
    Me.dgItStatus.Columns(1).Caption = getTranslationResource("Description")
    Me.dgItStatus.Columns(1).Width = 2880
    Me.dgItStatus.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgItStatus.Columns(1).Style = ssStyleEdit
    Me.dgItStatus.Columns(1).FieldLen = 30
    Me.dgItStatus.Columns(1).DataType = vbString
    
    Me.dgItStatus.Columns(2).Name = "ordgen"
    Me.dgItStatus.Columns(2).Caption = getTranslationResource("Ord Gen")
    Me.dgItStatus.Columns(2).Width = 1000
    Me.dgItStatus.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(2).Alignment = ssCaptionAlignmentCenter
    Me.dgItStatus.Columns(2).Style = ssStyleCheckBox

    Me.dgItStatus.Columns(3).Name = "dmdupd"
    Me.dgItStatus.Columns(3).Caption = getTranslationResource("Dmd Upd")
    Me.dgItStatus.Columns(3).Width = 1000
    Me.dgItStatus.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(3).Alignment = ssCaptionAlignmentCenter
    Me.dgItStatus.Columns(3).Style = ssStyleCheckBox

    Me.dgItStatus.Columns(4).Name = "vcassn"
    Me.dgItStatus.Columns(4).Caption = getTranslationResource("VC Assign")
    Me.dgItStatus.Columns(4).Width = 1000
    Me.dgItStatus.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgItStatus.Columns(4).Style = ssStyleCheckBox

    Me.dgItStatus.Columns(5).Name = "canpurge"
    Me.dgItStatus.Columns(5).Caption = getTranslationResource("Can Purge")
    Me.dgItStatus.Columns(5).Width = 1200
    Me.dgItStatus.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(5).Alignment = ssCaptionAlignmentLeft
    Me.dgItStatus.Columns(5).Style = ssStyleCheckBox

    Me.dgItStatus.Columns(6).Name = "InclInSeasonsgen"
    Me.dgItStatus.Columns(6).Caption = getTranslationResource("Seas Incl")
    Me.dgItStatus.Columns(6).Width = 1000
    Me.dgItStatus.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(6).Alignment = ssCaptionAlignmentLeft
    Me.dgItStatus.Columns(6).Style = ssStyleCheckBox
    
    Me.dgItStatus.Columns(7).Name = "buyerreviewseq"
    Me.dgItStatus.Columns(7).Caption = getTranslationResource("Buy Rev Seq")
    Me.dgItStatus.Columns(7).Width = 1440
    Me.dgItStatus.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItStatus.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgItStatus.Columns(7).Style = ssStyleEdit
    Me.dgItStatus.Columns(7).DataType = vbInteger
    Me.dgItStatus.Columns(7).Mask = "99#"
    Me.dgItStatus.Columns(7).FieldLen = 3
    Me.dgItStatus.Columns(7).PromptChar = " "
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgItStatus, ACW_EXPAND
    End If

    Me.dgItStatus.RowNavigation = ssRowNavigationUDLock
    
    For IndexCounter = 0 To dgItStatus.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgItStatus.Columns(IndexCounter).Locked = False Then dgItStatus.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_InitColumnProps)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_MouseDown)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_PrintBegin)"
	 f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG02200")
    If StrComp(strMessage, "RPTMSG02200") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG02201")
    If StrComp(strMessage1, "RPTMSG02201") = 0 Then strMessage = "Item Status Listing"
    strMessage2 = getTranslationResource("RPTMSG02202")
    If StrComp(strMessage2, "RPTMSG02202") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + _
                            vbTab + strMessage1 + _
                            vbTab + strMessage2 + " <page number>"
                            
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_PrintInitialize)"
	 f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    
    If Not f_IsRecordsetOpenAndPopulated(rsItStatus) Then Exit Sub
    
    If Not UserInputValidation(strMessage) Then
        rsItStatus.AddNew
    
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsItStatus("itstat").Value = RowBuf.Value(0, 0)
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsItStatus("itstatdesc").Value = RowBuf.Value(0, 1)
        Else
             rsItStatus("itstatdesc").Value = ""
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsItStatus("ordgen").Value = IIf(RowBuf.Value(0, 2) = VALUETRUESS, "Y", "N")
        Else
            rsItStatus("ordgen").Value = "N"
        End If
    
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsItStatus("dmdupd").Value = IIf(RowBuf.Value(0, 3) = VALUETRUESS, "Y", "N")
        Else
            rsItStatus("dmdupd").Value = "N"
        End If
    
        If Not IsNull(RowBuf.Value(0, 4)) Then
            rsItStatus("vcassn").Value = IIf(RowBuf.Value(0, 4) = VALUETRUESS, "Y", "N")
        Else
            rsItStatus("vcassn").Value = "N"
        End If
    
        If Not IsNull(RowBuf.Value(0, 5)) Then
            rsItStatus("canpurge").Value = IIf(RowBuf.Value(0, 5) = VALUETRUESS, "Y", "N")
        Else
            rsItStatus("canpurge").Value = "N"
        End If
    
        If Not IsNull(RowBuf.Value(0, 6)) Then
            rsItStatus("InclInSeasonsGen").Value = IIf(RowBuf.Value(0, 6) = VALUETRUESS, "Y", "N")
        Else
            rsItStatus("InclInSeasonsGen").Value = "N"
        End If
        If Not IsNull(RowBuf.Value(0, 7)) Then
            rsItStatus("buyerreviewseq").Value = RowBuf.Value(0, 7)
        Else
            rsItStatus("buyerreviewseq").Value = 0
        End If
       
        
        rsItStatus.Update
        
        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG02200")
            If StrComp(strMessage, "ERRMSG02200") = 0 Then strMessage = "Error adding Item Status Record."
            ADOErrorHandler Cn, strMessage
            rsItStatus.CancelUpdate
        Else
            strMessage = getTranslationResource("STATMSG02200")
            If StrComp(strMessage, "STATMSG02200") = 0 Then strMessage = "Item Status successfully added."
            Write_Message strMessage
            rsItStatus.MoveLast
            NewRowBookmark = rsItStatus.bookmark
        End If
        
    Else
        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
    End If
    
    Me.tbItStatus.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
            
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_UnboundAddData)"
	 f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_UnboundAddData", Now, gDRGeneralError, True, Err
    rsItStatus.CancelUpdate
End Sub

Private Sub dgItStatus_UnboundDeleteRow(bookmark As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsItStatus) Then Exit Sub
    
    rsItStatus.bookmark = bookmark
    rsItStatus.Delete

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_UnboundDeleteRow)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsItStatus) Then Exit Sub
    If dgItStatus.DataChanged Then Exit Sub
    If dgItStatus.IsAddRow Then Exit Sub
    
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsItStatus.MoveLast
        Else
            rsItStatus.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsItStatus.bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsItStatus.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsItStatus.bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_UnboundPositionData)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, I As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsItStatus) Then Exit Sub
    If dgItStatus.DataChanged Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsItStatus.MoveLast
        Else
            rsItStatus.MoveFirst
        End If
    
    Else
        rsItStatus.bookmark = StartLocation
        If ReadPriorRows Then
            rsItStatus.MovePrevious
        Else
            rsItStatus.MoveNext
        End If
    
    End If
    
    For I = 0 To RowBuf.RowCount - 1
        If rsItStatus.BOF Or rsItStatus.eof Then Exit For
    
        RowBuf.Value(I, 0) = rsItStatus("itstat").Value
        RowBuf.Value(I, 1) = rsItStatus("itstatdesc").Value
        RowBuf.Value(I, 2) = IIf(rsItStatus("ordgen").Value = "Y", VALUETRUE, VALUEFALSE)
        RowBuf.Value(I, 3) = IIf(rsItStatus("dmdupd").Value = "Y", VALUETRUE, VALUEFALSE)
        RowBuf.Value(I, 4) = IIf(rsItStatus("vcassn").Value = "Y", VALUETRUE, VALUEFALSE)
        RowBuf.Value(I, 5) = IIf(rsItStatus("canpurge").Value = "Y", VALUETRUE, VALUEFALSE)
        RowBuf.Value(I, 6) = IIf(rsItStatus("InclInSeasonsgen").Value = "Y", VALUETRUE, VALUEFALSE)
        RowBuf.Value(I, 7) = rsItStatus("buyerreviewseq").Value
        
        RowBuf.bookmark(I) = rsItStatus.bookmark
    
        If ReadPriorRows Then
            rsItStatus.MovePrevious
        Else
            rsItStatus.MoveNext
        End If
    
        r = r + 1
    Next I
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_UnboundReadData)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItStatus_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
        
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    
    If Not f_IsRecordsetOpenAndPopulated(rsItStatus) Then Exit Sub
    
    If Not UserInputValidation(strMessage) Then
        rsItStatus.bookmark = WriteLocation
    
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsItStatus("itstat").Value = RowBuf.Value(0, 0)
        End If
        
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsItStatus("itstatdesc").Value = RowBuf.Value(0, 1)
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsItStatus("ordgen").Value = IIf(RowBuf.Value(0, 2) = VALUETRUESS, "Y", "N")
        End If
    
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsItStatus("dmdupd").Value = IIf(RowBuf.Value(0, 3) = VALUETRUESS, "Y", "N")
        End If
    
        If Not IsNull(RowBuf.Value(0, 4)) Then
            rsItStatus("vcassn").Value = IIf(RowBuf.Value(0, 4) = VALUETRUESS, "Y", "N")
        End If
    
        If Not IsNull(RowBuf.Value(0, 5)) Then
            rsItStatus("canpurge").Value = IIf(RowBuf.Value(0, 5) = VALUETRUESS, "Y", "N")
        End If
        If Not IsNull(RowBuf.Value(0, 6)) Then
            rsItStatus("InclInSeasonsgen").Value = IIf(RowBuf.Value(0, 6) = VALUETRUESS, "Y", "N")
        End If
        If Not IsNull(RowBuf.Value(0, 7)) Then
            rsItStatus("buyerreviewseq").Value = RowBuf.Value(0, 7)
        End If
    
        'Update the Item Status Table
        rsItStatus.Update
    
        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG02201")
            If StrComp(strMessage, "ERRMSG02201") = 0 Then strMessage = "Error editing Item Status Record."
            ADOErrorHandler Cn, strMessage
            rsItStatus.CancelUpdate
        Else
            strMessage = getTranslationResource("STATMSG02201")
            If StrComp(strMessage, "STATMSG02201") = 0 Then strMessage = "Item Status successfully modified."
            Write_Message strMessage
        End If
    Else
        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
        rsItStatus.CancelUpdate
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItStatus_UnboundWriteData)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::dgItStatus_UnboundWriteData", Now, gDRGeneralError, True, Err
    rsItStatus.CancelUpdate
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02202")
    If StrComp(strMessage, "STATMSG02202") = 0 Then strMessage = "Initializing Item Status Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub

    'Build the SQL Statement
    SqlStmt = "select * from ItStatus Order by ItStat "
    
    'Open the Record Set
    With rsItStatus
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    rsItStatus.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dgItStatus.ReBind
    If f_IsRecordsetOpenAndPopulated(rsItStatus) Then dgItStatus.Rows = rsItStatus.RecordCount
    
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbItStatus.Tools("ID_Save").Enabled = False
        Me.tbItStatus.Tools("ID_Delete").Enabled = False
        Me.tbItStatus.Tools("ID_AddNew").Enabled = False
        Me.dgItStatus.AllowUpdate = False
        Me.dgItStatus.AllowAddNew = False
        Me.dgItStatus.AllowDelete = False
    Else
        Me.tbItStatus.Tools("ID_Save").Enabled = True
        Me.tbItStatus.Tools("ID_Delete").Enabled = True
        Me.tbItStatus.Tools("ID_AddNew").Enabled = True
        Me.dgItStatus.AllowUpdate = True
        Me.dgItStatus.AllowAddNew = True
        Me.dgItStatus.AllowDelete = True
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption

    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsItStatus) Then rsItStatus.Close
    Set rsItStatus = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
		f_HandleErr , , , "AIM_ItemStatusMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim I As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If f_IsRecordsetOpenAndPopulated(rsItStatus) Then
                Clipboard.Clear
                
                For I = 0 To rsItStatus.Fields.Count - 1
                    s1 = s1 + rsItStatus.Fields(I).Name + vbTab
                Next I
                
                s1 = s1 + vbCrLf
                    
                rsItStatus.MoveFirst
                s2 = rsItStatus.GetString(adClipString)
                
                Clipboard.SetText s1 + s2, vbCFText
            End If
            
        Case 1          'Print
            Me.dgItStatus.PrintData ssPrintAllRows, False, True
        
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbItStatus_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    If dgItStatus.DataChanged = True _
    Or dgItStatus.IsAddRow = True _
    Then
        dgItStatus.Update
    End If
    
    'Navigate
    Select Case Tool.ID
        Case "ID_AddNew"
            Me.tbItStatus.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
            Me.dgItStatus.AddNew
            
        Case "ID_Delete"
            Me.dgItStatus.DeleteSelected
        
        Case "ID_GetFirst"
            Me.dgItStatus.MoveFirst
        
        Case "ID_GetPrev"
            Me.dgItStatus.MovePrevious
        
        Case "ID_GetNext"
            Me.dgItStatus.MoveNext
        
        Case "ID_GetLast"
            Me.dgItStatus.MoveLast
        
        Case "ID_Save"
            Me.dgItStatus.Update
        
        Case "ID_SetBookMark"
            ItStatBookMark = Me.dgItStatus.bookmark
            Me.tbItStatus.Tools("ID_GoToBookMark").Enabled = True
        
        Case "ID_GoToBookMark"
            Me.dgItStatus.bookmark = ItStatBookMark
        
        Case "ID_Refresh"
            rsItStatus.Sort = ""
            Me.dgItStatus.ReBind
            If f_IsRecordsetOpenAndPopulated(rsItStatus) Then dgItStatus.Rows = rsItStatus.RecordCount
            Me.tbItStatus.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
            
        Case "ID_Close"
            Unload Me
            Exit Sub

    End Select

    Me.dgItStatus.Refresh

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbItStatus_ToolClick)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::tbItStatus_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function UserInputValidation(p_Message As String, Optional p_ColIndex As Integer = -1) As Boolean
On Error GoTo ErrorHandler
    
    Dim lngCounter As Integer
    Dim LoopUpperBound As Integer
    Dim LoopLowerBound As Integer
    
    Dim RtnCode As Integer
    
    UserInputValidation = False
    
    'ErrorMessage
    p_Message = getTranslationResource("MSGBOX07312")
    If StrComp(p_Message, "MSGBOX07312") = 0 Then _
        p_Message = "The following data is required, or is invalid. Please provide correct values in the expected format for:"
        
    'Check if it's just one field, or all.
    If p_ColIndex >= 0 Then
        LoopLowerBound = p_ColIndex
        LoopUpperBound = p_ColIndex
    Else
        LoopLowerBound = 0
        LoopUpperBound = dgItStatus.Columns.Count - 1
    End If

    For lngCounter = LoopLowerBound To LoopUpperBound
        Select Case lngCounter
        Case 0 'ItStat
            If dgItStatus.Columns(lngCounter).Value = "" _
            Or IsNull(dgItStatus.Columns(lngCounter).Value) _
            Then
                'Cancel = True
                p_Message = p_Message & vbCrLf & _
                            dgItStatus.Columns(lngCounter).Caption
                UserInputValidation = True
            End If
            
        Case 1 'ItStatDesc
            If dgItStatus.Columns(lngCounter).Value = "" _
            Or IsNull(dgItStatus.Columns(lngCounter).Value) _
            Then
                'Default Value
                dgItStatus.Columns(lngCounter).Value = ""   'so it's not null
            End If
                
        Case 2, 3, 4, 5, 6 'OrdGen, DmdUpd, VCAssn, CanPurge, InclInSeasonsGen
            If dgItStatus.Columns(lngCounter).Value = "" _
            Or IsNull(dgItStatus.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItStatus.Columns(lngCounter).Value = "N"   'so it's not null
            End If
            
        Case 7  'BuyerReview Sequence
            If dgItStatus.Columns(lngCounter).Value = "" _
            Or IsNull(dgItStatus.Columns(lngCounter).Value) _
            Then
                'Default value
                dgItStatus.Columns(lngCounter).Value = 0
            End If
        End Select
        
    Next
    
    If UserInputValidation <> True Then p_Message = ""
    
Exit Function
ErrorHandler:
   'Err.Raise Err.Number, Err.source, Err.Description & "(UserInputValidation)"
	f_HandleErr , , , "AIM_ItemStatusMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function




