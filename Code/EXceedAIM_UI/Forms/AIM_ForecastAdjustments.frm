VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ForecastAdjustments 
   Caption         =   "SSA DR Forecast Planner - Adjustments"
   ClientHeight    =   8400
   ClientLeft      =   60
   ClientTop       =   330
   ClientWidth     =   12015
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   LinkTopic       =   "Form1"
   ScaleHeight     =   8400
   ScaleWidth      =   12015
   StartUpPosition =   3  'Windows Default
   Begin VB.Frame fraAdjustments 
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   7815
      Left            =   120
      TabIndex        =   0
      Top             =   60
      Width           =   11775
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_End 
         Height          =   345
         Left            =   3030
         TabIndex        =   17
         Top             =   2100
         Width           =   2100
         DataFieldList   =   "EndDate"
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "EndDate"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcDate_Start 
         Height          =   345
         Left            =   3030
         TabIndex        =   16
         Top             =   1725
         Width           =   2100
         DataFieldList   =   "StartDate"
         _Version        =   196617
         DataMode        =   2
         Cols            =   1
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "StartDate"
      End
      Begin VB.CommandButton cmdLocationLookup 
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   310
         Left            =   5205
         MouseIcon       =   "AIM_ForecastAdjustments.frx":0000
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ForecastAdjustments.frx":0152
         Style           =   1  'Graphical
         TabIndex        =   4
         Top             =   1367
         Width           =   310
      End
      Begin VB.CommandButton cmdItemFilter 
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   310
         Left            =   5205
         MouseIcon       =   "AIM_ForecastAdjustments.frx":029C
         MousePointer    =   99  'Custom
         Picture         =   "AIM_ForecastAdjustments.frx":03EE
         Style           =   1  'Graphical
         TabIndex        =   1
         Top             =   1007
         Width           =   310
      End
      Begin TDBText6Ctl.TDBText txtItem 
         Height          =   345
         Left            =   3030
         TabIndex        =   2
         Top             =   990
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ForecastAdjustments.frx":0538
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastAdjustments.frx":05A4
         Key             =   "AIM_ForecastAdjustments.frx":05C2
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLcId 
         Height          =   345
         Left            =   3030
         TabIndex        =   5
         Top             =   1350
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ForecastAdjustments.frx":0606
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastAdjustments.frx":0672
         Key             =   "AIM_ForecastAdjustments.frx":0690
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAdjustLog 
         Height          =   4275
         Left            =   120
         TabIndex        =   9
         Top             =   2505
         Width           =   11565
         _Version        =   196617
         DataMode        =   2
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Col.Count       =   0
         AllowUpdate     =   0   'False
         MaxSelectedRows =   1
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         CaptionAlignment=   0
         Columns(0).Width=   3200
         _ExtentX        =   20399
         _ExtentY        =   7541
         _StockProps     =   79
         ForeColor       =   64
         BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAdjustType 
         Height          =   345
         Left            =   3030
         TabIndex        =   10
         Top             =   615
         Width           =   2100
         DataFieldList   =   "Column 0"
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         Columns(0).Width=   3200
         _ExtentX        =   3704
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBText6Ctl.TDBText txtAdjustDesc 
         Height          =   825
         Left            =   3105
         TabIndex        =   11
         Top             =   6840
         Width           =   8580
         _Version        =   65536
         _ExtentX        =   15134
         _ExtentY        =   1455
         Caption         =   "AIM_ForecastAdjustments.frx":06D4
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastAdjustments.frx":0740
         Key             =   "AIM_ForecastAdjustments.frx":075E
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   2
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   1
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtFcstID 
         Height          =   345
         Left            =   3030
         TabIndex        =   15
         TabStop         =   0   'False
         Top             =   240
         Width           =   2100
         _Version        =   65536
         _ExtentX        =   3704
         _ExtentY        =   609
         Caption         =   "AIM_ForecastAdjustments.frx":07A2
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ForecastAdjustments.frx":080E
         Key             =   "AIM_ForecastAdjustments.frx":082C
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label lblFcstID 
         Caption         =   "Forecast ID"
         Height          =   300
         Left            =   120
         TabIndex        =   14
         Top             =   285
         Width           =   2805
      End
      Begin VB.Label lblAdjustDesc 
         Caption         =   "Adjustment Description"
         Height          =   300
         Left            =   120
         TabIndex        =   13
         Top             =   6885
         Width           =   3045
      End
      Begin VB.Label lblAdjustType 
         Caption         =   "Adjustment Type"
         Height          =   300
         Left            =   120
         TabIndex        =   12
         Top             =   660
         Width           =   2805
      End
      Begin VB.Label lblAdjustEndDate 
         Caption         =   "Adjustment End Date"
         Height          =   300
         Left            =   120
         TabIndex        =   8
         Top             =   2145
         Width           =   2805
      End
      Begin VB.Label lblAdjustStartDate 
         Caption         =   "Adjustment Start Date"
         Height          =   300
         Left            =   120
         TabIndex        =   7
         Top             =   1770
         Width           =   2805
      End
      Begin VB.Label lblLcID 
         Caption         =   "Location ID"
         Height          =   300
         Left            =   120
         TabIndex        =   6
         Top             =   1395
         Width           =   2805
      End
      Begin VB.Label lblItem 
         Caption         =   "Item"
         Height          =   300
         Left            =   120
         TabIndex        =   3
         Top             =   1035
         Width           =   2805
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   8040
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   15
      Style           =   0
      Tools           =   "AIM_ForecastAdjustments.frx":0870
      ToolBars        =   "AIM_ForecastAdjustments.frx":C6DB
   End
End
Attribute VB_Name = "AIM_ForecastAdjustments"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
'*****************************************************************************
' Copyright (c) 2004 SSA Global. All rights reserved.
'*****************************************************************************
'
'   AIM_DmdPlan_Adjustments.frm
'
'   Version Number - 1.0
'   Last Updated   - 2004/02/24
'   Updated By     - Annalakshmi Stocksdale
'
'
'*****************************************************************************
' This file contains trade secrets of SSA Global. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of SSA Global.
'*****************************************************************************
Option Explicit

Public p_FcstSetupKey As Long
Public p_FcstStoreKey As Long
Public p_FcstInterval As Long

Public p_FcstItem As String
Public p_FcstLcID As String
Public p_AdjustStartDate As Date
Public p_AdjustEndDate As Date

Public p_FilterItem As String
Public p_FilterItemStatus As String
Public p_FilterVendorID As String
Public p_FilterAssortment As String
Public p_FilterLocationID As String
Public p_FilterBuyerID As String
Public p_FilterClass1 As String
Public p_FilterClass2 As String
Public p_FilterClass3 As String
Public p_FilterClass4 As String
Public p_FilterLStatus As String
Public p_FilterLDivision As String
Public p_FilterLRegion As String
Public p_FilterLUserDefined As String
Public p_FcstID As String
Public P_HistPds As Integer
Public P_FuturePds As Integer
Public p_FcstStartDate As Date

Dim P_FcstDates() As Date

Dim m_Cn As ADODB.Connection

Private Sub cmdItemFilter_Click()
On Error GoTo ErrorHandler

    Dim LookupForm As Form
    
    Set LookupForm = AIM_ItemLookUp
    With LookupForm
        .LcIdKey = txtLcId.Text
        .ItemKey = txtItem.Text
        Set .Cn = m_Cn
        
        .Show vbModal
    
        If Not .CancelFlag Then
            txtLcId = .LcIdKey
            txtItem = .ItemKey
        End If
    End With
    
    Set LookupForm = Nothing
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLocationLookup_Click)"
     f_HandleErr , , , "AIM_ForecastAdjustments::cmdItemFilter_Click", Now, gDRGeneralError, True, Err
    
    Set LookupForm = Nothing
End Sub

Private Sub cmdLocationLookup_Click()
On Error GoTo ErrorHandler

    Dim LookupForm As Form
    Dim LocationID As String
    
    Screen.MousePointer = vbHourglass
    
    Set LookupForm = AIM_LocationLookUp
    With LookupForm
        Set .Cn = m_Cn
        LocationID = IIf(Trim$(Me.txtLcId.Text) <> "", Me.txtLcId.Text, getTranslationResource("All"))
        .LcId = LocationID
        .Show vbModal
        If Not .CancelFlag Then
            LocationID = .LcId
            Me.txtLcId.Text = IIf(StrComp(LocationID, getTranslationResource("All"), vbTextCompare) = 0, "", LocationID)
        End If
    End With
    
    Set LookupForm = Nothing
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLocationLookup_Click)"
     f_HandleErr , , , "AIM_ForecastAdjustments::cmdLocationLookup_Click", Now, gDRGeneralError, True, Err
    
    Set LookupForm = Nothing
    Screen.MousePointer = vbNormal
End Sub

Private Sub dcAdjustType_InitColumnProps()
On Error GoTo ErrorHandler

    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    Dim rsCodeLookup As ADODB.Recordset
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    Dim AddItemString As String
    Dim IndexCounter As Long
    Dim RowCount As Long
    
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = m_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
    'Declare parameters
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append CmdParams
    'Input
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@CodeType", adVarWChar, adParamInput, 30)
        CmdParams.Value = g_CODETYPE_ADJUSTTYPE
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@LangID", adVarWChar, adParamInput, 10)
        CmdParams.Value = gLangID
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        
    End With
    
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    rsCodeLookup.Open AIM_CodeLookup_Get_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        Me.dcAdjustType.Redraw = False
        
        Me.dcAdjustType.RemoveAll

        IndexCounter = 0
        Me.dcAdjustType.AddItem IndexCounter
        Me.dcAdjustType.Columns(IndexCounter).Name = "CodeID"
        Me.dcAdjustType.Columns(IndexCounter).Caption = getTranslationResource("Adjust Type")
        Me.dcAdjustType.Columns(IndexCounter).Width = 1500
        Me.dcAdjustType.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dcAdjustType.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
'        Me.dcAdjustType.Columns(IndexCounter).Visible = False
        
        IndexCounter = IndexCounter + 1
        Me.dcAdjustType.AddItem IndexCounter
        Me.dcAdjustType.Columns(IndexCounter).Name = "CodeDesc"
        Me.dcAdjustType.Columns(IndexCounter).Caption = getTranslationResource("Description")
        Me.dcAdjustType.Columns(IndexCounter).Width = 2880
        Me.dcAdjustType.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dcAdjustType.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        If gGridAutoSizeOption Then
            'Added to Automatically adjust the column widths depending on the Columns Label translation size.
            AdjustColumnWidths dcAdjustType, ACW_EXPAND
        End If
        
        rsCodeLookup.MoveFirst
        RowCount = rsCodeLookup.RecordCount
        Me.dcAdjustType.RemoveAll
        AddItemString = "" & vbTab & getTranslationResource("ALL")
        Me.dcAdjustType.AddItem AddItemString
        For IndexCounter = 1 To RowCount
            AddItemString = CStr(rsCodeLookup!CodeID) & vbTab & CStr(rsCodeLookup!CodeDesc)
            Me.dcAdjustType.AddItem AddItemString
            
            rsCodeLookup.MoveNext
        Next
    End If
    
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing

    Me.dcAdjustType.Redraw = True
        
Exit Sub
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    If Err.Number = 3219 Then
        Resume Next
    Else
        'f_HandleErr Me.Caption & "(dcAdjustType_InitColumnProps)"
         f_HandleErr , , , "AIM_ForecastAdjustments::dcAdjustType_InitColumnProps", Now, gDRGeneralError, True, Err

        Me.dcAdjustType.Redraw = True
        If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
        Set rsCodeLookup = Nothing
        If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
        Set AIM_CodeLookup_Get_Sp = Nothing
        Set CmdParams = Nothing
    End If
End Sub

Private Sub dcDate_End_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    dcDate_End.Redraw = False  'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbHourglass

    dcDate_End.Columns(0).Caption = getTranslationResource("EndDate")
    dcDate_End.Columns(0).Width = 1500
    dcDate_End.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    dcDate_End.Columns(0).Alignment = ssCaptionAlignmentLeft
    dcDate_End.Columns(0).DataField = "EndDate"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDate_End, ACW_EXPAND
    End If
    
    dcDate_End.Redraw = True   'Process is done. Repaint control.
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_End_InitColumnProps)"
     f_HandleErr , , , "AIM_ForecastAdjustments::dcDate_End_InitColumnProps", Now, gDRGeneralError, True, Err
    
    dcDate_End.Redraw = True
    Screen.MousePointer = vbNormal
End Sub

Private Sub dcDate_Start_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim i As Integer
    
    dcDate_Start.Redraw = False  'Disable the control from repainting until the process has finished.
    Screen.MousePointer = vbHourglass

    dcDate_Start.Columns(0).Caption = getTranslationResource("StartDate")
    dcDate_Start.Columns(0).Width = 1500
    dcDate_Start.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    dcDate_Start.Columns(0).Alignment = ssCaptionAlignmentLeft
    dcDate_Start.Columns(0).DataField = "StartDate"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcDate_Start, ACW_EXPAND
    End If
    
    dcDate_Start.Redraw = True   'Process is done. Repaint control.
    Screen.MousePointer = vbNormal
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcDate_Start_InitColumnProps)"
     f_HandleErr , , , "AIM_ForecastAdjustments::dcDate_Start_InitColumnProps", Now, gDRGeneralError, True, Err
    
    dcDate_Start.Redraw = True
    Screen.MousePointer = vbNormal
End Sub

Private Sub dgAdjustLog_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
If CStr(dgAdjustLog.RowBookmark(0)) = "" Then Exit Sub
txtAdjustDesc.Text = dgAdjustLog.Columns(6).Text
End Sub


Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Long
    Dim strMessage As String
    Dim strMessage1 As String
    Dim IndexCounter As Long
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    
    Me.dgAdjustLog.Redraw = False
    
'    strMessage = getTranslationResource("STATMSG06900")
'    If StrComp(strMessage, "STATMSG06900") = 0 Then strMessage = "Initializing Forecast Adjustments..."""
'    Write_Message strMessage
    
    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set m_Cn = New ADODB.Connection
    RtnCode = SQLConnection(m_Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Initialize other fields
    Me.txtFcstId.Text = p_FcstID
    Me.txtItem.Text = p_FcstItem
    Me.txtLcId.Text = p_FcstLcID
 
    mStartDate_Load
    mEndDate_Load
    'Populate the grid
    RtnCode = mFcstAdjustments_Fetch
    
    'Windup
'    Write_Message ""
    Me.dgAdjustLog.Redraw = True
    
    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    Me.dgAdjustLog.Redraw = True
    Screen.MousePointer = vbNormal
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_ForecastAdjustments::Form_Load", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub Form_Unload(Cancel As Integer)
    Dim RtnCode As Long
    
'>>>
    'Save pending updates, first
    '...
    
    'Destroy objects
    
    'Destroy connection and object
    RtnCode = SQLConnection(m_Cn, CONNECTION_CLOSE, False)
    Set m_Cn = Nothing
    
    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
    'f_HandleErr Me.Caption & "(Form_Unload)"
     f_HandleErr , , , "AIM_ForecastAdjustments::Form_Unload", Now, gDRGeneralError, True, Err
        
    End If
    Resume Next

End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim p_ToolID As String
    Dim BkMark As Variant
    Dim FreezeKey As Long
    Dim FreezeType As Long
    Dim LcId As String
    Dim FreezeLimit As String
    Dim UpdateTextFields As Boolean

    Me.tbNavigation.Redraw = False

    Select Case UCase$(Tool.ID)
    Case "ID_CLOSE"
        '>>> Check for unsaved records
        Unload Me
        Exit Sub
    Case "ID_REFRESH"
        
        RtnCode = mFcstAdjustments_Fetch
        Me.tbNavigation.Tools.Item("ID_Save").Enabled = False
        Me.tbNavigation.Tools.Item("ID_Cancel").Enabled = False
        UpdateTextFields = False
    End Select

    If UpdateTextFields = True Then
        Me.txtLcId.Text = p_FcstLcID
        Me.txtItem.Text = p_FcstItem
        Me.dcAdjustType.Text = ""
        Me.txtAdjustDesc.Text = ""
    End If

    Me.tbNavigation.Redraw = True
    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    Me.tbNavigation.Redraw = True
    Me.MousePointer = vbNormal

    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
     f_HandleErr , , , "AIM_ForecastAdjustments::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
    
End Sub

Private Function mFcstAdjustments_Fetch() As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim rsAdjustments As ADODB.Recordset

    Dim DBugCounter As Long
    Dim DBugText As String
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDesc As String

    Dim IndexCounter As Long
    Dim RowCount As Long
    Dim RowIdx As Long
    Dim AddItemString As String
    Dim BkMark As Variant
    Dim FcstLcID As String, FcstItem As String
    Dim AdjustStart As Date, AdjustEnd As Date
    Dim AdjustType As Integer
    Dim AdjType As String
    
    'check bookmark here
    BkMark = IIf(IsEmpty(Me.dgAdjustLog.Bookmark), Null, Me.dgAdjustLog.Bookmark)
    
    FcstLcID = Me.txtLcId.Text
    FcstItem = Me.txtItem.Text
    AdjustStart = Format(Me.dcDate_Start.Text, g_ISO_DATE_FORMAT)
    AdjustEnd = Format(Me.dcDate_End.Text, g_ISO_DATE_FORMAT)
    If (dcAdjustType.Columns(0).Text = "") Then
        AdjustType = 3
    Else
        AdjustType = CInt(dcAdjustType.Columns(0).Text)
    End If
        'Fetch in data
        RtnCode = gFetch_ForecastAdjustments(FcstLcID, FcstItem, AdjustType, _
                    AdjustStart, AdjustEnd, rsAdjustments)
    
    
    'Check return code -- 0 = fail; 1 = succeed
    Select Case RtnCode
    Case Is > 0 'Success
        dgAdjustLog.Redraw = False

        'Clear any existing columns
        Me.dgAdjustLog.Columns.RemoveAll
        RowCount = rsAdjustments.RecordCount

        IndexCounter = 0
        'Set up the Grid
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "LcID"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Location")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 800
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "Item"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("Item")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1000
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustType"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustType")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 800
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustQty"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustQty")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1000
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustStartDate"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustStartDate")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1440
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustEndDate"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustEndDate")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1440
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustDesc"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustDesc")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 2000
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustUserID"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustUserID")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1000
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        IndexCounter = IndexCounter + 1
        Me.dgAdjustLog.Columns.Add IndexCounter
        Me.dgAdjustLog.Columns(IndexCounter).Name = "AdjustDateTime"
        Me.dgAdjustLog.Columns(IndexCounter).Caption = getTranslationResource("AdjustDateTime")
        Me.dgAdjustLog.Columns(IndexCounter).Width = 1440
        Me.dgAdjustLog.Columns(IndexCounter).CaptionAlignment = ssColCapAlignLeftJustify
        Me.dgAdjustLog.Columns(IndexCounter).Alignment = ssCaptionAlignmentLeft
        
        If gGridAutoSizeOption Then
            'Added to Automatically adjust the column widths depending on the Columns Label translation size.
            AdjustColumnWidths dgAdjustLog, ACW_EXPAND
        End If

        
        If f_IsRecordsetOpenAndPopulated(rsAdjustments) Then
            rsAdjustments.MoveFirst
            Do Until rsAdjustments.eof
                AddItemString = ""
                AddItemString = rsAdjustments!LcId
                AddItemString = AddItemString & vbTab & rsAdjustments!Item
                AdjType = rsAdjustments!AdjustType
                If AdjType = "0" Then
                AdjType = getTranslationResource("Override")
                ElseIf AdjType = "1" Then
                AdjType = getTranslationResource("Units")
                ElseIf AdjType = "2" Then
                AdjType = getTranslationResource("Percentage")
                End If
                AddItemString = AddItemString & vbTab & AdjType
                AddItemString = AddItemString & vbTab & rsAdjustments!AdjustQty
                AddItemString = AddItemString & vbTab & rsAdjustments!AdjustBegDate
                AddItemString = AddItemString & vbTab & rsAdjustments!AdjustEndDate
                AddItemString = AddItemString & vbTab & rsAdjustments!AdjustDesc
                AddItemString = AddItemString & vbTab & rsAdjustments!AdjustUserID
                AddItemString = AddItemString & vbTab & rsAdjustments!AdjustDateTime
                
                Me.dgAdjustLog.AddItem AddItemString
                rsAdjustments.MoveNext
            Loop
        End If

    Case -1 'No Data Found
        Me.dgAdjustLog.Columns.RemoveAll
        'Msg here
    Case -2 'SQL Error
        'Msg here
    Case -3 'Invalid parameter
        'Msg here
    End Select

    'Set return value
    mFcstAdjustments_Fetch = RtnCode

    Me.dgAdjustLog.Bookmark = BkMark
    
    dgAdjustLog.Redraw = True

CleanUp:
    If f_IsRecordsetValidAndOpen(rsAdjustments) Then rsAdjustments.Close
    Set rsAdjustments = Nothing

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description

    dgAdjustLog.Redraw = True
    If f_IsRecordsetValidAndOpen(rsAdjustments) Then rsAdjustments.Close
    Set rsAdjustments = Nothing

    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(mFcstAdjustments_Fetch)"
     f_HandleErr , , , "AIM_ForecastAdjustments::mFcstAdjustments_Fetch", Now, gDRGeneralError, True, Err
    
End Function



Public Sub mPopulateFcstDates(ArgDates() As Date)
On Error GoTo ErrorHandler
Dim i As Integer
ReDim P_FcstDates(0 To UBound(ArgDates()))
For i = 0 To UBound(ArgDates())
P_FcstDates(i) = ArgDates(i)
Next i
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(mPopulateFcstDates)"
     f_HandleErr , , , "AIM_ForecastAdjustments::mPopulateFcstDates", Now, gDRGeneralError, True, Err
    
End Sub

Private Function mStartDate_Load() As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Boolean
    Dim RowCounter As Long
    Dim AddItemString As String
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String

    'dcDate_smr.Redraw = False
    Screen.MousePointer = vbHourglass

    'Default to false until the process returns it's result.
    mStartDate_Load = False

   If UBound(P_FcstDates()) > 0 Then RtnCode = True
    If RtnCode = True Then
        dcDate_Start.RemoveAll
        dcDate_Start.Reset
        For RowCounter = 0 To UBound(P_FcstDates()) - 1
            AddItemString = AddItemString & P_FcstDates(RowCounter) & vbTab
            dcDate_Start.AddItem AddItemString
            AddItemString = ""
        Next
        mStartDate_Load = True
        dcDate_Start.Value = p_AdjustStartDate
    End If

CleanUp:
    Screen.MousePointer = vbNormal

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    Screen.MousePointer = vbNormal

    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(mStartDate_Load)"
     f_HandleErr , , , "AIM_ForecastAdjustments::mStartDate_Load", Now, gDRGeneralError, True, Err
    
End Function
Private Function gFetch_ForecastAdjustments(FcstLcID As String, FcstItem As String, AdjustType As Integer, _
                    AdjustStart As Date, AdjustEnd As Date, rsAdjustments As ADODB.Recordset) As Long

On Error GoTo ErrorHandler
    Dim RtnCode As Long
    Dim AIM_DmdPlan_Adjustments_Fetch_Sp As ADODB.Command
    Dim RowCount As Long
    Dim RowIdx As Long
    Dim rsCompound As ADODB.Recordset
    Dim RSCounter As Long, ColCounter As Long, RowCounter As Long

    'Clear out any existing data
    If f_IsRecordsetValidAndOpen(rsAdjustments) Then rsAdjustments.Close
    Set rsAdjustments = Nothing

    'Init. the command object
    Set AIM_DmdPlan_Adjustments_Fetch_Sp = New ADODB.Command
    With AIM_DmdPlan_Adjustments_Fetch_Sp
        Set .ActiveConnection = m_Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DmdPlan_Adjustments_Fetch_Sp"
    'Declare parameters
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@FcstId", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@AdjType", adSmallInt, adParamInput)
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@AdjustBegDate", adDBDate, adParamInput)
        .Parameters.Append AIM_DmdPlan_Adjustments_Fetch_Sp.CreateParameter("@AdjustEndDate", adDBDate, adParamInput)
    'Set values
        .Parameters("@FcstId").Value = p_FcstID
        .Parameters("@LcID").Value = IIf(Trim$(FcstLcID) = "", Null, Trim$(FcstLcID))
        .Parameters("@Item").Value = IIf(Trim$(FcstItem) = "", Null, Trim$(FcstItem))
        .Parameters("@AdjType").Value = AdjustType
        .Parameters("@AdjustBegDate").Value = IIf(Trim$(AdjustStart) = "12:00:00 AM", Null, Trim$(Format(AdjustStart, g_ISO_DATE_FORMAT)))
        .Parameters("@AdjustEndDate").Value = IIf(Trim$(AdjustEnd) = "12:00:00 AM", Null, Trim$(Format(AdjustEnd, g_ISO_DATE_FORMAT)))
    End With

    'Init. the recordset
    Set rsCompound = New ADODB.Recordset
    With rsCompound
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
    rsCompound.Open AIM_DmdPlan_Adjustments_Fetch_Sp

    RtnCode = AIM_DmdPlan_Adjustments_Fetch_Sp.Parameters(0).Value
    'Check return code -- 0 = fail; 1 = succeed
    If RtnCode <= 0 Then
        'FAIL
        GoTo CleanUp
        Exit Function
    End If
    If f_IsRecordsetValidAndOpen(rsCompound) Then
        'Associate as a disconnected recordset
        Set rsAdjustments = New ADODB.Recordset
        rsAdjustments.Fields.Append "LcID", adVarWChar, 12
        rsAdjustments.Fields.Append "Item", adVarWChar, 25
        rsAdjustments.Fields.Append "AdjustType", adInteger
        rsAdjustments.Fields.Append "AdjustQty", adDecimal
        rsAdjustments.Fields("AdjustQty").Precision = 10
        rsAdjustments.Fields("AdjustQty").NumericScale = 2
        rsAdjustments.Fields.Append "AdjustUserID", adVarWChar, 12
        rsAdjustments.Fields.Append "AdjustDesc", adVarWChar, 255
        rsAdjustments.Fields.Append "AdjustDateTime", adDBDate
        rsAdjustments.Fields.Append "AdjustBegDate", adDBDate
        rsAdjustments.Fields.Append "AdjustEndDate", adDBDate
        'Populate
        rsAdjustments.Open

        RSCounter = 1
        Do Until rsCompound Is Nothing
            Do Until rsCompound.eof
                'Set display to match recordset(s)
                With rsAdjustments
                    .AddNew
                    For ColCounter = 0 To rsCompound.Fields.Count - 1
                        .Fields(ColCounter) = rsCompound.Fields(ColCounter)
                    Next
                End With
                rsCompound.MoveNext 'There should be no more for the first recordset
            Loop

            Set rsCompound = rsCompound.NextRecordset
            RSCounter = RSCounter + 1
        Loop
    Else
        RtnCode = -1
    End If

CleanUp:
    If f_IsRecordsetValidAndOpen(rsCompound) Then rsCompound.Close
    Set rsCompound = Nothing
    If Not (AIM_DmdPlan_Adjustments_Fetch_Sp Is Nothing) Then Set AIM_DmdPlan_Adjustments_Fetch_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlan_Adjustments_Fetch_Sp = Nothing

    If f_IsRecordsetOpenAndPopulated(rsAdjustments) Then
        RtnCode = 1
    Else
        RtnCode = -1
    End If
    gFetch_ForecastAdjustments = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DmdPlan_Adjustments.gFetch_ForecastAdjustments)"
     f_HandleErr , , , "AIM_ForecastAdjustments::gFetch_ForecastAdjustments", Now, gDRGeneralError, True, Err

    GoTo CleanUp
End Function

Private Function mEndDate_Load() As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Boolean
    Dim RowCounter As Long
    Dim AddItemString As String
    Dim ErrNumber As Long, ErrSource As String, ErrDesc As String
    'dcDate_smr.Redraw = False
    Screen.MousePointer = vbHourglass

    'Default to false until the process returns it's result.
    mEndDate_Load = False

   If UBound(P_FcstDates()) > 0 Then RtnCode = True
    If RtnCode = True Then
        dcDate_End.RemoveAll
        dcDate_End.Reset
        For RowCounter = 1 To UBound(P_FcstDates())
            AddItemString = AddItemString & CStr(P_FcstDates(RowCounter) - 1) & vbTab
            dcDate_End.AddItem AddItemString
            AddItemString = ""
        Next
         dcDate_End.Value = p_AdjustEndDate
        mEndDate_Load = True
    End If

CleanUp:
    Screen.MousePointer = vbNormal

Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    Screen.MousePointer = vbNormal

    'Err.Raise ErrNumber, ErrSource, ErrDesc & "(mEndDate_Load)"
     f_HandleErr , , , "AIM_ForecastAdjustments::mEndDate_Load", Now, gDRGeneralError, True, Err

End Function





