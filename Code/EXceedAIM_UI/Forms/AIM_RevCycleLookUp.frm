VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_ReviewCycleLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Review Cycle Lookup"
   ClientHeight    =   5940
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8475
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_RevCycleLookUp.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5940
   ScaleWidth      =   8475
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Default         =   -1  'True
      Height          =   345
      Left            =   7032
      Style           =   1  'Graphical
      TabIndex        =   3
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdCancel 
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3000
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return Selected Item"
      Height          =   345
      Left            =   4440
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   5400
      Width           =   2445
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   75
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   1560
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   5400
      Width           =   1365
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgRevCycles 
      Height          =   3315
      Left            =   72
      TabIndex        =   8
      Top             =   1905
      Width           =   8325
      _Version        =   196617
      DataMode        =   1
      Cols            =   3
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      TabNavigation   =   1
      _ExtentX        =   14684
      _ExtentY        =   5847
      _StockProps     =   79
      ForeColor       =   64
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1650
      Left            =   78
      TabIndex        =   9
      Top             =   120
      Width           =   8325
      Begin TDBText6Ctl.TDBText txtRevCycle 
         Height          =   340
         Left            =   108
         TabIndex        =   0
         Top             =   735
         Width           =   1700
         _Version        =   65536
         _ExtentX        =   2999
         _ExtentY        =   600
         Caption         =   "AIM_RevCycleLookUp.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_RevCycleLookUp.frx":0376
         Key             =   "AIM_RevCycleLookUp.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
         Height          =   345
         Left            =   120
         TabIndex        =   1
         Top             =   1200
         Width           =   3135
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   600
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7305
         TabIndex        =   2
         Top             =   1200
         Width           =   870
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calculator      =   "AIM_RevCycleLookUp.frx":03D8
         Caption         =   "AIM_RevCycleLookUp.frx":03F8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_RevCycleLookUp.frx":0464
         Keys            =   "AIM_RevCycleLookUp.frx":0482
         Spin            =   "AIM_RevCycleLookUp.frx":04CC
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   200
         MaxValueVT      =   6750213
         MinValueVT      =   3538949
      End
      Begin VB.Label Label 
         Caption         =   "Maximum Rows"
         Height          =   300
         Index           =   0
         Left            =   5280
         TabIndex        =   11
         Top             =   1245
         Width           =   1935
      End
      Begin VB.Label Label 
         Caption         =   "Review Cycle"
         Height          =   340
         Index           =   1
         Left            =   108
         TabIndex        =   10
         Top             =   340
         Width           =   1700
      End
   End
End
Attribute VB_Name = "AIM_ReviewCycleLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection
Dim rsReviewCycle As ADODB.Recordset

Public CancelFlag As Boolean
Public RevCycleKey As String

Private RevCycleKey_Criteria As String

Private Function BuildRevCycleLookupQuery() As String
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    
    'Initialize SQL Statment
    SqlStmt = "SELECT RevCycle, RevCycleDesc, NextDatetime" & _
            " FROM RevCycles "
    'Build the Where Statment
    If Trim(Me.txtRevCycle.Text) <> "" Then
        WhrStmt = "WHERE RevCycle " + Me.dcComparison.Value + " " + RevCycleKey_Criteria + " "
    Else
        WhrStmt = ""
    End If
        
    SqlStmt = SqlStmt & vbCrLf & _
              WhrStmt & vbCrLf & _
              "ORDER BY RevCycle "
    
    BuildRevCycleLookupQuery = SqlStmt
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BuildRevCycleLookupQuery)", Err.Description
     f_HandleErr , , , "AIM_Reports::BuildRevCycleLookupQuery", Now, gDRGeneralError, True, Err
End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim strText As String
    Dim strMessage As String
    
    Me.MousePointer = vbHourglass
    Write_Message ""
    
    strText = getTranslationResource(AIM_ReviewCycleLookUp.Caption)
    
    'Make sure the user selected at least one criterion
    If Trim(Me.txtRevCycle.Text) = "" Then
        strMessage = getTranslationResource("MSGBOX04500")
        If StrComp(strMessage, "MSGBOX04500") = 0 Then strMessage = "Please select one or more Lookup Criteria "
        MsgBox strMessage, vbExclamation, strText
        Me.txtRevCycle.SetFocus
        Me.MousePointer = vbNormal
        Exit Sub

    End If
    
    'Force validation
    txtRevCycle_Validate False
    If StrComp(RevCycleKey_Criteria, "Error", vbTextCompare) = 0 _
    Then
        'Do NOT Proceed
    Else
        'Create the query
        SqlStmt = BuildRevCycleLookupQuery()
        
        'Recreate the result set
        If f_IsRecordsetValidAndOpen(rsReviewCycle) Then rsReviewCycle.Close
        rsReviewCycle.MaxRecords = Me.txtMaxRows.Value
        rsReviewCycle.Open SqlStmt
        
        If f_IsRecordsetOpenAndPopulated(rsReviewCycle) Then
            'Refresh the grid
            Me.dgRevCycles.ReBind
        Else
            If f_IsRecordsetValidAndOpen(rsReviewCycle) Then rsReviewCycle.Close
            Me.dgRevCycles.Reset
        End If
    End If
    
    'Return the screen values
    Me.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_Reports::cmdLookup_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_Reports::cmdCancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    Me.txtRevCycle.Text = ""
    RevCycleKey_Criteria = ""
    
    Me.txtMaxRows.Value = 200
    
    If f_IsRecordsetValidAndOpen(rsReviewCycle) Then rsReviewCycle.Close
    Me.dgRevCycles.Reset
    
    Me.txtRevCycle.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_Reports::cmdClear_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgRevCycles.Rows > 0 Then
        Screen.MousePointer = vbHourglass
        Me.dgRevCycles.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_Reports::cmdPrint_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = False
    Me.RevCycleKey = Me.dgRevCycles.Columns("RevCycle").Value
    
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_Reports::cmdReturn_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_CloseUp()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case Me.dcComparison.Columns(0).Text
    
    Case "LIKE"
        Select Case Me.dcComparison.Columns(1).Text
        Case getTranslationResource("Begins With")
            strMessage = getTranslationResource("STATMSG04500")
            If StrComp(strMessage, "STATMSG04500") = 0 Then strMessage = "Enter either part or all of the beginning of the desired value."
            Write_Message strMessage
        Case getTranslationResource("Contains")
            strMessage = getTranslationResource("STATMSG04501")
            If StrComp(strMessage, "STATMSG04501") = 0 Then strMessage = "Enter a string expression which is contained within the desired value."
            Write_Message strMessage
        End Select
    
    Case "BETWEEN"
        strMessage = getTranslationResource("STATMSG04502")
        If StrComp(strMessage, "STATMSG04502") = 0 Then strMessage = "Enter two values between which the desired value should fall, separated by a comma."
        Write_Message strMessage
    
    Case "IN"
        strMessage = getTranslationResource("STATMSG04503")
        If StrComp(strMessage, "STATMSG04503") = 0 Then strMessage = "Enter a list of values in which the desired value should fall, separated by commas."
        Write_Message strMessage
    
    Case Else
        strMessage = getTranslationResource("STATMSG04504")
        If StrComp(strMessage, "STATMSG04504") = 0 Then strMessage = "Enter a value for comparison."
        Write_Message strMessage
    
    End Select
    
    'Reset the comparison values
    txtRevCycle_Validate False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_CloseUp)"
     f_HandleErr , , , "AIM_Reports::dcComparison_CloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Build Columns
    Me.dcComparison.Columns(0).Caption = getTranslationResource("Comparison")
    Me.dcComparison.Columns(0).FieldLen = 1
    Me.dcComparison.Columns(0).Width = 1000
    
    Me.dcComparison.Columns(1).Caption = getTranslationResource("Description")
    Me.dcComparison.Columns(1).FieldLen = 24
    Me.dcComparison.Columns(1).Width = 2000
    
    'Load Values
    Me.dcComparison.AddItem ("=" + vbTab + getTranslationResource("Equal To"))
    Me.dcComparison.AddItem (">" + vbTab + getTranslationResource("Greater Than"))
    Me.dcComparison.AddItem (">=" + vbTab + getTranslationResource("Greater Than / Equal To"))
    Me.dcComparison.AddItem ("<" + vbTab + getTranslationResource("Less Than"))
    Me.dcComparison.AddItem ("<=" + vbTab + getTranslationResource("Less Than / Equal To"))
    Me.dcComparison.AddItem ("<>" + vbTab + getTranslationResource("Not Equal To"))
    Me.dcComparison.AddItem ("LIKE" + vbTab + getTranslationResource("Begins With"))
    Me.dcComparison.AddItem ("LIKE" + vbTab + getTranslationResource("Contains"))
    Me.dcComparison.AddItem ("BETWEEN" + vbTab + getTranslationResource("Is Between These Values"))
    Me.dcComparison.AddItem ("IN" + vbTab + getTranslationResource("Is in this List of Values"))

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcComparison, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcComparison.Columns.Count - 1
'        dcComparison.Columns(IndexCounter).HasHeadBackColor = True
'        dcComparison.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_InitColumnProps)"
     f_HandleErr , , , "AIM_Reports::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRevCycles_DblClick()
On Error GoTo ErrorHandler

    'Return selected item
    cmdReturn_Click

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRevCycles_DblClick)"
     f_HandleErr , , , "AIM_Reports::dgRevCycles_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRevCycles_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Review Cycle Data
    Me.dgRevCycles.Columns(0).Name = "RevCycle"
    Me.dgRevCycles.Columns(0).Caption = getTranslationResource("Review Cycle")
    Me.dgRevCycles.Columns(0).Width = 1000
    Me.dgRevCycles.Columns(0).Locked = True
    
    Me.dgRevCycles.Columns(1).Name = "RevCycledesc"
    Me.dgRevCycles.Columns(1).Caption = getTranslationResource("Description")
    Me.dgRevCycles.Columns(1).Width = 2880
    Me.dgRevCycles.Columns(1).Locked = True
    
    Me.dgRevCycles.Columns(2).Name = "nextdatetime"
    Me.dgRevCycles.Columns(2).Caption = getTranslationResource("Next Review Date")
    Me.dgRevCycles.Columns(2).Width = 1200
    Me.dgRevCycles.Columns(2).NumberFormat = "mm/dd/yyyy"
    Me.dgRevCycles.Columns(2).DataType = vbDate
    Me.dgRevCycles.Columns(2).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgRevCycles, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgRevCycles.Columns.Count - 1
'        dgRevCycles.Columns(IndexCounter).HasHeadBackColor = True
'        dgRevCycles.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgRevCycles.Columns(IndexCounter).Locked = False Then dgRevCycles.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next


Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRevCycles_InitColumnProps)"
     f_HandleErr , , , "AIM_Reports::dgRevCycles_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRevCycles_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG04500")
    If StrComp(strMessage, "RPTMSG05700") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG04501")
    If StrComp(strMessage1, "RPTMSG05701") = 0 Then strMessage = "Review Cycle Lookup List"
    strMessage2 = getTranslationResource("RPTMSG04502")
    If StrComp(strMessage2, "RPTMSG05702") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + strMessage1 + vbTab _
                + strMessage2 + " <page number>"
    
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRevCycles_PrintInitialize)"
     f_HandleErr , , , "AIM_Reports::dgRevCycles_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRevCycles_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsReviewCycle) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsReviewCycle.MoveLast
        Else
            rsReviewCycle.MoveFirst
        End If
    
    Else
        rsReviewCycle.Bookmark = StartLocation
        If ReadPriorRows Then
            rsReviewCycle.MovePrevious
        Else
            rsReviewCycle.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        
        If rsReviewCycle.BOF Or rsReviewCycle.eof Then Exit For
        RowBuf.Value(i, 0) = rsReviewCycle("RevCycle").Value
        RowBuf.Value(i, 1) = rsReviewCycle("RevCycledesc").Value
        RowBuf.Value(i, 2) = rsReviewCycle("nextdatetime").Value

        RowBuf.Bookmark(i) = rsReviewCycle.Bookmark
    
        If ReadPriorRows Then
            rsReviewCycle.MovePrevious
        Else
            rsReviewCycle.MoveNext
        End If
    
        r = r + 1
    
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRevCycles_UnboundReadData)"
     f_HandleErr , , , "AIM_Reports::dgRevCycles_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Set the default value for the Cancel Flag
    CancelFlag = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_Reports::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim SqlStmt As String
    
    'Initialize fields
    Me.txtRevCycle.Text = Me.RevCycleKey
    RevCycleKey_Criteria = BldCompVal(Me.RevCycleKey, Me.dcComparison.Value, Me.dcComparison.Text)
    
    Me.txtMaxRows.Value = 200
    
    Me.dcComparison.Text = getTranslationResource("Greater Than / Equal To")
    
    'Build initial record set
    Set rsReviewCycle = New ADODB.Recordset
    rsReviewCycle.CursorLocation = adUseClient
    rsReviewCycle.CursorType = adOpenStatic
    rsReviewCycle.LockType = adLockReadOnly
    Set rsReviewCycle.ActiveConnection = Cn
    
    SqlStmt = "SELECT RevCycle, RevCycleDesc, NextDatetime FROM RevCycles WHERE RevCycle = 'xxxxxxxx' "
    rsReviewCycle.Open SqlStmt
    
    'Make the spin button visible
    Me.txtMaxRows.Spin.Visible = 1
    
    'Bind the grid
    Me.dgRevCycles.ReBind

    GetTranslatedCaptions Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_Reports::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsReviewCycle) Then rsReviewCycle.Close
    Set rsReviewCycle = Nothing

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_Reports::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Function BldCompVal(CompVals As String, CompType As String, _
    CompDetail As String)
On Error GoTo ErrorHandler

    Dim i, N As Integer
    Dim newtext As String
    Dim RtnStr As String
    Dim StrArray(0 To 10) As String
    Dim strText As String
    Dim strMessage As String
    
    strText = getTranslationResource(AIM_ReviewCycleLookUp.Caption)
    
    Select Case CompType
    
    Case "LIKE"
        Select Case CompDetail
        Case getTranslationResource("Begins With")
            CompVals = "N'" & CompVals & "%" & "'"
        Case getTranslationResource("Contains")
            CompVals = "N'%" & CompVals & "%" & "'"
        End Select
    
    Case "BETWEEN"
        N = Parse(Trim(CompVals), ", ", "'", StrArray())
        
        If N <> 2 Then
            strMessage = getTranslationResource("MSGBOX04501")
            If StrComp(strMessage, "MSGBOX04501") = 0 Then strMessage = "Please enter two values separated by a comma "
            MsgBox strMessage, vbExclamation, strText
            BldCompVal = "Error"
            Exit Function
        
        Else
            CompVals = "N'" & StrArray(0) & "'" & _
                  " AND N'" & StrArray(1) & "' "
            
        End If
        
    Case "IN"
        N = Parse(Trim(CompVals), ", ", "'", StrArray())
        
        newtext = "("
        For i = 0 To (N - 2)
            newtext = newtext & "N'" & StrArray(i) & "'" & ", "
        Next i
        
        newtext = newtext & "N'" & StrArray(N - 1) & "'" & ")"
        CompVals = newtext
        
    Case Else
        CompVals = "N'" & Trim(CompVals) & "'"
        
    End Select
    
    BldCompVal = CompVals

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldCompVal)", Err.Description
    f_HandleErr , , , "AIM_Reports::BldCompVal", Now, gDRGeneralError, True, Err
End Function

Private Sub txtRevCycle_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim(txtRevCycle.Text) = "" Then
        Exit Sub
    End If
    
    RevCycleKey_Criteria = BldCompVal(Me.txtRevCycle.Text, _
        Me.dcComparison.Value, Me.dcComparison.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtRevCycle_Validate)"
     f_HandleErr , , , "AIM_Reports::txtRevCycle_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_Reports::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_Reports::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub
