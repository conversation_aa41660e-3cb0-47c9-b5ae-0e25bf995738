VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_ErrorFilter 
   BorderStyle     =   4  'Fixed ToolWindow
   Caption         =   "SSA Distribution Replenishment error filter"
   ClientHeight    =   3060
   ClientLeft      =   45
   ClientTop       =   315
   ClientWidth     =   6795
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   3060
   ScaleWidth      =   6795
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.CommandButton Command1 
      BackColor       =   &H00C0FFFF&
      Caption         =   "&Clear"
      Height          =   345
      Left            =   1920
      Style           =   1  'Graphical
      TabIndex        =   13
      Top             =   2520
      Width           =   1395
   End
   Begin VB.CommandButton Cancel 
      BackColor       =   &H00C0FFFF&
      Cancel          =   -1  'True
      Caption         =   "&Cancel"
      Default         =   -1  'True
      Height          =   345
      Left            =   3600
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   2520
      Width           =   1395
   End
   Begin VB.CommandButton cmdApply 
      BackColor       =   &H0000E6FF&
      Caption         =   "&Return"
      Height          =   345
      Left            =   5280
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   2520
      Width           =   1395
   End
   Begin VB.Frame Frame1 
      Caption         =   "Choose date range and/or user name and/or Log Type"
      BeginProperty Font 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   177
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   1455
      Left            =   120
      TabIndex        =   1
      Top             =   240
      Width           =   6495
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo DBComboUserName 
         DataField       =   " "
         Height          =   375
         Left            =   4560
         TabIndex        =   3
         Top             =   360
         Width           =   1815
         DataFieldList   =   "USERNAME"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColorOdd    =   15919334
         RowHeight       =   423
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   3201
         _ExtentY        =   661
         _StockProps     =   93
         BackColor       =   15919334
         DataFieldToDisplay=   "USERNAME"
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dbcmbJobType 
         DataField       =   " "
         Height          =   375
         Left            =   4560
         TabIndex        =   4
         Top             =   840
         Width           =   1815
         DataFieldList   =   "CODEDESC"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         Cols            =   2
         BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         BackColorOdd    =   15919334
         RowHeight       =   423
         Columns(0).Width=   3200
         Columns(0).DataType=   8
         Columns(0).FieldLen=   4096
         _ExtentX        =   3201
         _ExtentY        =   661
         _StockProps     =   93
         BackColor       =   15919334
         DataFieldToDisplay=   "CODEDESC"
      End
      Begin TDBDate6Ctl.TDBDate tdbEndDate 
         Height          =   375
         Left            =   1275
         TabIndex        =   2
         Top             =   840
         Width           =   1335
         _Version        =   65536
         _ExtentX        =   2355
         _ExtentY        =   661
         Calendar        =   "AIM_ErrorFilter.frx":0000
         Caption         =   "AIM_ErrorFilter.frx":0118
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ErrorFilter.frx":0184
         Keys            =   "AIM_ErrorFilter.frx":01A2
         Spin            =   "AIM_ErrorFilter.frx":0200
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   15919334
         BorderStyle     =   1
         BtnPositioning  =   0
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "07/12/2005"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   38545
         CenturyMode     =   0
      End
      Begin TDBDate6Ctl.TDBDate tdbBeginDate 
         Height          =   375
         Left            =   1275
         TabIndex        =   0
         Top             =   360
         Width           =   1335
         _Version        =   65536
         _ExtentX        =   2355
         _ExtentY        =   661
         Calendar        =   "AIM_ErrorFilter.frx":0228
         Caption         =   "AIM_ErrorFilter.frx":0340
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ErrorFilter.frx":03AC
         Keys            =   "AIM_ErrorFilter.frx":03CA
         Spin            =   "AIM_ErrorFilter.frx":0428
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   15919334
         BorderStyle     =   1
         BtnPositioning  =   0
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "07/12/2005"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   38545
         CenturyMode     =   0
      End
      Begin VB.Label lblUserName 
         Caption         =   "User Name"
         Height          =   255
         Index           =   0
         Left            =   3360
         TabIndex        =   10
         Top             =   480
         Width           =   855
      End
      Begin VB.Label lblJobType 
         Caption         =   "Error Type"
         Height          =   255
         Index           =   1
         Left            =   3360
         TabIndex        =   9
         Top             =   960
         Width           =   855
      End
      Begin VB.Label lblEndDate 
         Caption         =   "End Date"
         Height          =   255
         Left            =   120
         TabIndex        =   8
         Top             =   960
         Width           =   735
      End
      Begin VB.Label lblStarDate 
         Caption         =   "Begin Date"
         Height          =   255
         Left            =   120
         TabIndex        =   7
         Top             =   480
         Width           =   855
      End
   End
   Begin TDBNumber6Ctl.TDBNumber MaxLogMsgs 
      Height          =   345
      Left            =   1920
      TabIndex        =   11
      Top             =   1800
      Width           =   780
      _Version        =   65536
      _ExtentX        =   1376
      _ExtentY        =   609
      Calculator      =   "AIM_ErrorFilter.frx":0450
      Caption         =   "AIM_ErrorFilter.frx":0470
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Microsoft Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_ErrorFilter.frx":04DC
      Keys            =   "AIM_ErrorFilter.frx":04FA
      Spin            =   "AIM_ErrorFilter.frx":0544
      AlignHorizontal =   1
      AlignVertical   =   0
      Appearance      =   1
      BackColor       =   15919334
      BorderStyle     =   1
      BtnPositioning  =   1
      ClipMode        =   0
      ClearAction     =   0
      DecimalPoint    =   "."
      DisplayFormat   =   "#0;-#0;0;0"
      EditMode        =   0
      Enabled         =   -1
      ErrorBeep       =   0
      ForeColor       =   -2147483640
      Format          =   "##0"
      HighlightText   =   0
      MarginBottom    =   3
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MaxValue        =   2147483647
      MinValue        =   1
      MousePointer    =   0
      MoveOnLRKey     =   0
      NegativeColor   =   255
      OLEDragMode     =   0
      OLEDropMode     =   0
      ReadOnly        =   0
      Separator       =   ""
      ShowContextMenu =   -1
      ValueVT         =   2010775557
      Value           =   200
      MaxValueVT      =   5
      MinValueVT      =   5
   End
   Begin VB.Label Label1 
      Caption         =   "Max. Error Messages:"
      Height          =   255
      Left            =   240
      TabIndex        =   12
      Top             =   1920
      Width           =   1575
   End
End
Attribute VB_Name = "AIM_ErrorFilter"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Dim JobTypesRecSet As ADODB.Recordset
Dim AimUsersRecSet As ADODB.Recordset
Private SQLQuery As String
Public BeginDate As Date
Public EndDate As Date
Public UserID As String
Public ErrorType As String
Public MaxErrorMsgs As Integer
Public MaxNumberOfRowsFortheUser As Long
Private NumberOfDaysToBeginDate As Integer

Private Sub Cancel_Click()
On Error GoTo ErrorHandler

'Set flag to indicate that filter is not set.
gIsErrorFilterCreated = False

'Unload the current form
Unload Me
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorFilter::Cancel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub cmdApply_Click()
On Error GoTo ErrorHandler

'Set the flag to indicate that filter is not set.
gIsErrorFilterCreated = True

'------------Caputre the filters including begin & end dates, user id, error type---------------
BeginDate = tdbBeginDate.Value

'We are adding one day,since we are using BETWEEN operator to build where clause. - sgajjela
EndDate = DateAdd("d", (1), tdbEndDate.Value)

MaxErrorMsgs = MaxLogMsgs.Value
ErrorType = Me.dbcmbJobType.Columns(0).Value
UserID = Me.DBComboUserName.Columns(1).Value
MaxNumberOfRowsFortheUser = MaxLogMsgs.Value

'Unload the current form.
Unload Me
Exit Sub

ErrorHandler:
   f_HandleErr , , , "AIM_ErrorFilter::cmdApply_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Command1_Click()
Call Initilize
End Sub

Private Sub dbcmbJobType_InitColumnProps()
On Error GoTo ErrorHandler

'Set up a single column, called Error Type  as message type indicator.
Me.dbcmbJobType.Columns(0).Name = "Error Type"
Me.dbcmbJobType.Columns(0).Caption = getTranslationResource("Error Type")
Me.dbcmbJobType.Columns(0).Width = 1000

Me.dbcmbJobType.Columns(1).Name = "Error Description"
Me.dbcmbJobType.Columns(1).Caption = getTranslationResource("Error Description")
Me.dbcmbJobType.Columns(1).Width = 1550


Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorFilter::dccmbJobType::InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub DBComboUserName_InitColumnProps()
On Error GoTo ErrorHandler

'Set up User name column
Me.DBComboUserName.Columns(0).Name = "User Name"
Me.DBComboUserName.Columns(0).Caption = getTranslationResource("User Name")
Me.DBComboUserName.Columns(0).Width = 1400

'Set up User ID column
Me.DBComboUserName.Columns(1).Name = "User ID"
Me.DBComboUserName.Columns(1).Caption = getTranslationResource("User ID")
Me.DBComboUserName.Columns(1).Width = 900
Exit Sub

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorFilter::DBComboUserName_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
GetTranslatedCaptions Me
Call Initilize
End Sub


Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

'-----Perform clean up-----
JobTypesRecSet.Close
AimUsersRecSet.Close
Set JobTypesRecSet = Nothing
Set AimUsersRecSet = Nothing

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_ErrorFilter::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Function Initilize()
On Error GoTo ErrorHandler

'-----This setting is not reqired here. However, we may need it in rare cases-----
gIsErrorFilterCreated = False

'-----Process Begin & End date controls-----
Me.tdbBeginDate.DropDown.Visible = 1
Me.tdbEndDate.DropDown.Visible = 1

'-----Set up the date formats  as per  specified under system control maintanance-----
Me.tdbBeginDate.Format = gDateFormat
Me.tdbEndDate.Format = gDateFormat
Me.tdbBeginDate.DisplayFormat = gDateFormat
Me.tdbEndDate.DisplayFormat = gDateFormat

'-----Extract 'NumOffDaysBehindFromCurrentDate' From SysCtrl-----
Dim AIM_SysCtrl_Get_Sp As ADODB.Command
Dim rsSysCtrl As ADODB.Recordset
        
'Get the System Control Record
Set AIM_SysCtrl_Get_Sp = New ADODB.Command
With AIM_SysCtrl_Get_Sp
    Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
End With
    
Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
rsSysCtrl.Open AIM_SysCtrl_Get_Sp
NumberOfDaysToBeginDate = IIf(IsNull(rsSysCtrl("NumOffDaysBehindFromCurrentDate").Value), 7, rsSysCtrl("NumOffDaysBehindFromCurrentDate").Value)

'-----Setup the begin date to the number of days specified in SysCtrl-----

'Point the begin date to specified days back from current date.
Me.tdbBeginDate.Value = DateAdd("d", -(NumberOfDaysToBeginDate), Date)

'Point end date to today's date.
Me.tdbEndDate = Date

'-----Populate DBUsername Combo box with User ID & Name-----
Set AimUsersRecSet = New ADODB.Recordset

'Assign active connection to the recordset.
Set AimUsersRecSet.ActiveConnection = Cn

'Specify the cursor location.
AimUsersRecSet.CursorLocation = adUseClient

'Specify recordset type.
AimUsersRecSet.CursorType = adOpenStatic

'specify lock type. In this case we just need read lock.
AimUsersRecSet.LockType = adLockReadOnly

'Build Query.
SQLQuery = "SELECT [USERNAME], [USERID] FROM [AIMUSERS] UNION SELECT 'All', 'All Users' USERNAME ORDER BY [USERNAME]"

'Fetch all the data from AIMUSERS table.
AimUsersRecSet.Open SQLQuery

'Link ADO RecordSet object with DBComboUserName.
Set Me.DBComboUserName.DataSourceList = AimUsersRecSet

'Default value for user name
Me.DBComboUserName.Value = DBComboUserName.Columns(0).Value


'-----Populate LogType DBCombo box with log types-----
SQLQuery = "SELECT  [CODEID], [CODEDESC] FROM AIMCODELOOKUP WHERE [CODETYPE] = 'ERRORCODES' UNION SELECT 'All', 'All Types of Errors'  ORDER BY [CODEID] ASC"

'Create record set
Set JobTypesRecSet = New ADODB.Recordset

'Assign active connection to the recordset.
Set JobTypesRecSet.ActiveConnection = Cn

'Specify the cursor location.
JobTypesRecSet.CursorLocation = adUseClient

'Specify recordset type.
JobTypesRecSet.CursorType = adOpenStatic

'specify lock type. In this case we just need read lock.
JobTypesRecSet.LockType = adLockReadOnly

'Open it.
JobTypesRecSet.Open SQLQuery


'Link ADO RecordSet object with DBComboJobType.
Set Me.dbcmbJobType.DataSourceList = JobTypesRecSet

'Set the display value.
Me.dbcmbJobType.Value = dbcmbJobType.Columns(0).Value

MaxLogMsgs.Spin.Visible = dbiShowAlways

Exit Function

ErrorHandler:
    f_HandleErr , , , "AIM_ErrorFilter::Initilize", Now, gDRGeneralError, True, Err
End Function



