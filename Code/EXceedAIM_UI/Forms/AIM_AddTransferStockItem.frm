VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_AddTransferStockItem 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Add Transfer Item"
   ClientHeight    =   2955
   ClientLeft      =   3270
   ClientTop       =   3975
   ClientWidth     =   7455
   Icon            =   "AIM_AddTransferStockItem.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   2955
   ScaleWidth      =   7455
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atbNavigator 
      Left            =   360
      Top             =   2520
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   2
      Tools           =   "AIM_AddTransferStockItem.frx":030A
      ToolBars        =   "AIM_AddTransferStockItem.frx":1C95
   End
   Begin VB.Frame Frame1 
      Height          =   2175
      Left            =   240
      TabIndex        =   0
      Top             =   120
      Width           =   6975
      Begin VB.CommandButton CmdToLcId 
         Height          =   315
         Left            =   6240
         MouseIcon       =   "AIM_AddTransferStockItem.frx":1D10
         MousePointer    =   99  'Custom
         Picture         =   "AIM_AddTransferStockItem.frx":1E62
         Style           =   1  'Graphical
         TabIndex        =   4
         Top             =   727
         Width           =   315
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcToLocationId 
         DataSource      =   "rsAIMlocations"
         Height          =   345
         Left            =   3120
         TabIndex        =   3
         Top             =   720
         Width           =   3135
         DataFieldList   =   "lcid"
         _Version        =   196617
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "lcid"
      End
      Begin TDBText6Ctl.TDBText TDBItemId 
         Height          =   345
         Left            =   3120
         TabIndex        =   5
         Top             =   1200
         Width           =   3135
         _Version        =   65536
         _ExtentX        =   5530
         _ExtentY        =   609
         Caption         =   "AIM_AddTransferStockItem.frx":1FAC
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_AddTransferStockItem.frx":2018
         Key             =   "AIM_AddTransferStockItem.frx":2036
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   1
         MarginRight     =   1
         MarginTop       =   1
         MarginBottom    =   1
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   25
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.CommandButton CmdFromLcId 
         Height          =   315
         Left            =   6240
         MouseIcon       =   "AIM_AddTransferStockItem.frx":207A
         MousePointer    =   99  'Custom
         Picture         =   "AIM_AddTransferStockItem.frx":21CC
         Style           =   1  'Graphical
         TabIndex        =   2
         Top             =   255
         Width           =   315
      End
      Begin VB.CommandButton CmdItemId 
         Height          =   315
         Left            =   6240
         MouseIcon       =   "AIM_AddTransferStockItem.frx":2316
         MousePointer    =   99  'Custom
         Picture         =   "AIM_AddTransferStockItem.frx":2468
         Style           =   1  'Graphical
         TabIndex        =   6
         Top             =   1200
         Width           =   310
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcFromLocationID 
         Bindings        =   "AIM_AddTransferStockItem.frx":25B2
         DataSource      =   "rsAIMlocations"
         Height          =   345
         Left            =   3120
         TabIndex        =   1
         Top             =   240
         Width           =   3135
         DataFieldList   =   "lcid"
         AllowInput      =   0   'False
         _Version        =   196617
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   609
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "lcid"
      End
      Begin VB.Label Label_ToLocation 
         Caption         =   "To Location ID"
         Height          =   300
         Left            =   240
         TabIndex        =   9
         Top             =   742
         Width           =   2805
      End
      Begin VB.Label Label_FromLocation 
         Caption         =   "From Location ID"
         Height          =   300
         Left            =   240
         TabIndex        =   8
         Top             =   285
         Width           =   2805
      End
      Begin VB.Label LabelItem 
         Caption         =   "Item ID"
         Height          =   300
         Left            =   240
         TabIndex        =   7
         Top             =   1200
         Width           =   2805
      End
   End
End
Attribute VB_Name = "AIM_AddTransferStockItem"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
Dim AIM_Item_GetKey_Sp As ADODB.Command
Dim rsAIMLocations As ADODB.Recordset
Public From_Lcid As String
Public To_Lcid As String
Public Item As String

Private Function m_Find(p_Source As String)
On Error GoTo ErrorHandler

    Select Case p_Source
        Case Me.CmdItemId.Name
            If Trim(Me.TDBItemId.Text) = "" Then
                AIM_ItemLookUp.ItemKey = ""
                AIM_ItemLookUp.ByIdKey = ""
                AIM_ItemLookUp.VnIdKey = ""
                AIM_ItemLookUp.AssortKey = ""
                AIM_ItemLookUp.LcIdKey = dcFromLocationID.Text
                
            Else
                AIM_ItemLookUp.ItemKey = Me.TDBItemId.Text
                AIM_ItemLookUp.LcIdKey = dcFromLocationID.Text
            End If
    End Select
    
    Set AIM_ItemLookUp.Cn = Cn
    AIM_ItemLookUp.Comparison = "Contains"
    
    AIM_ItemLookUp.Show vbModal
    
    If Not AIM_ItemLookUp.CancelFlag Then
        Select Case p_Source
            Case Me.CmdFromLcId.Name
                Me.dcFromLocationID.Text = AIM_ItemLookUp.LcIdKey
            Case Me.CmdToLcId.Name
                Me.dcToLocationId.Text = AIM_ItemLookUp.LcIdKey
            Case Me.CmdItemId.Name
                Me.TDBItemId.Text = AIM_ItemLookUp.ItemKey
        End Select
    End If

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(m_Find)"
End Function

Private Sub atbNavigator_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    Dim RtnCode As Long
    Dim strMessage As String
    
    Select Case Tool.ID
        Case "ID_Close"
            AIM_TransferManagement.AddOverStockFromLocation = dcFromLocationID.Text
            AIM_TransferManagement.AddOverStockToLocation = dcToLocationId.Text
            AIM_TransferManagement.AddOverStockItem = TDBItemId.Text
            If dcFromLocationID.Text = "" Or dcToLocationId.Text = "" Or TDBItemId.Text = "" Then
                strMessage = getTranslationResource("MSGBOX07700")
                If StrComp(strMessage, "MSGBOX07700") = 0 Then strMessage = "Abandon changes to current record ?"
                    RtnCode = MsgBox(strMessage, vbYesNo, Me.Caption)
                If RtnCode = vbYes Then
                    Unload Me
                Else
                    'Set Focus to approprate field
                    If dcFromLocationID.Text = "" Then
                        dcFromLocationID.SetFocus
                        
                    ElseIf dcToLocationId.Text = "" Then
                            dcToLocationId.SetFocus
                            
                    ElseIf TDBItemId.Text = "" Then
                        TDBItemId.SetFocus
                    End If
                    
                End If
            Else
                Unload Me
            End If
   End Select

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::atbNavigator_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub CmdFromLcId_Click()
On Error GoTo ErrorHandler

    m_Find Me.CmdFromLcId.Name

Exit Sub
ErrorHandler:
    f_HandleErr Me.Caption & "(CmdFromLcId_Click)"
End Sub

Private Sub CmdItemId_Click()
On Error GoTo ErrorHandler
    
    m_Find Me.CmdItemId.Name

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::atbNavigator_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub CmdToLcId_Click()
On Error GoTo ErrorHandler
    
    m_Find Me.CmdToLcId.Name

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::CmdToLcId_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcFromLocationID_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcFromLocationID.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcFromLocationID.Columns(0).Width = 1000
        
    Me.dcFromLocationID.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcFromLocationID.Columns(1).Width = 2880
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcFromLocationID, ACW_EXPAND
    End If

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::dcFromLocationId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcToLocationId_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcToLocationId.Columns(0).Caption = getTranslationResource("Location ID")
    Me.dcToLocationId.Columns(0).Width = 1000
        
    Me.dcToLocationId.Columns(1).Caption = getTranslationResource("Location Name")
    Me.dcToLocationId.Columns(1).Width = 2880
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcToLocationId, ACW_EXPAND
    End If

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::dcToLocationId_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler
    
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG07700")
    If StrComp(strMessage, "STATMSG07700") = 0 Then strMessage = "Initializing DR Add Transfer Stock Item LookUp Screen..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub

    'Initialize the recordset
    Set rsAIMLocations = New ADODB.Recordset
    With rsAIMLocations
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    'Validate the Item for the location
    Set AIM_Item_GetKey_Sp = New ADODB.Command
    With AIM_Item_GetKey_Sp
    Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Item_GetKey_Sp"
        .Parameters.Refresh
    End With
    
    SqlStmt = "SELECT LcID, LName FROM AIMLocations ORDER BY LcID"
    
    rsAIMLocations.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
    If f_IsRecordsetOpenAndPopulated(rsAIMLocations) Then
        'Initialize the location data combos
        Set Me.dcFromLocationID.DataSourceList = rsAIMLocations
        Set Me.dcToLocationId.DataSourceList = rsAIMLocations
    End If
    
    Me.dcFromLocationID.Text = ""
    Me.dcToLocationId.Text = ""
    Me.TDBItemId.Text = ""
    Cn.Errors.Clear
    
    'Add to Windows List
    AddToWindowList Me.Caption
     
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_AddTransferStockItem::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    'Close the recordset
    If f_IsRecordsetValidAndOpen(rsAIMLocations) Then rsAIMLocations.Close
    Set rsAIMLocations = Nothing
     
    If Not (AIM_Item_GetKey_Sp Is Nothing) Then AIM_Item_GetKey_Sp.ActiveConnection = Nothing
    Set AIM_Item_GetKey_Sp = Nothing
    
    'Close the SQL Connection
    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
    'Clear messages
    Write_Message ""

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        f_HandleErr , , , "AIM_AddTransferStockItem::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
