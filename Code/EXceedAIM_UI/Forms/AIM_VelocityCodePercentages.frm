VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_VelocityCodePercentages 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Velocity Code Percentages"
   ClientHeight    =   4965
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   5580
   Icon            =   "AIM_VelocityCodePercentages.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   4965
   ScaleWidth      =   5580
   ShowInTaskbar   =   0   'False
   Begin VB.Frame Frame2 
      Height          =   4455
      Left            =   53
      TabIndex        =   0
      Top             =   0
      Width           =   5475
      Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMVelCodePcts 
         Height          =   3675
         Left            =   195
         TabIndex        =   1
         Top             =   645
         Width           =   5100
         _Version        =   196617
         DataMode        =   1
         AllowColumnSwapping=   0
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         RowHeight       =   423
         CaptionAlignment=   0
         Columns(0).Width=   3200
         _ExtentX        =   8996
         _ExtentY        =   6482
         _StockProps     =   79
      End
      Begin TDBNumber6Ctl.TDBNumber txtAccumPct 
         Height          =   345
         Left            =   4320
         TabIndex        =   3
         Top             =   240
         Width           =   975
         _Version        =   65536
         _ExtentX        =   1720
         _ExtentY        =   609
         Calculator      =   "AIM_VelocityCodePercentages.frx":030A
         Caption         =   "AIM_VelocityCodePercentages.frx":032A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_VelocityCodePercentages.frx":0396
         Keys            =   "AIM_VelocityCodePercentages.frx":03B4
         Spin            =   "AIM_VelocityCodePercentages.frx":03FE
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   ""
         DisplayFormat   =   "#0.000;-#0.000;0.000;0.000"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "#0.000"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   52
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   -1
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011955205
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin VB.Label Label 
         Caption         =   "Accumulative Percentages"
         BeginProperty Font 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   300
         Left            =   195
         TabIndex        =   2
         Top             =   285
         Width           =   4005
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbVelCodePcts 
      Left            =   0
      Top             =   4560
      _ExtentX        =   741
      _ExtentY        =   741
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   11
      Style           =   0
      Tools           =   "AIM_VelocityCodePercentages.frx":0426
      ToolBars        =   "AIM_VelocityCodePercentages.frx":8373
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_VelocityCodePercentages"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMVelCodePcts As ADODB.Recordset

Dim AIMVelCodePctsBookMark As Variant

Private Function AccumPcts()
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then
        AccumPcts = 0
        Me.txtAccumPct.Value = Format(AccumPcts, "0.000")
        Exit Function
    End If
    
    rsAIMVelCodePcts.MoveFirst
    Do Until rsAIMVelCodePcts.eof
        AccumPcts = AccumPcts + rsAIMVelCodePcts("vcpct").Value
        rsAIMVelCodePcts.MoveNext
    Loop
    
    Me.txtAccumPct.Value = Format(AccumPcts, "0.000")

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AccumPcts)", Err.Description
     f_HandleErr , , , "AIM_VelocityCodePercentages::AccumPcts", Now, gDRGeneralError, True, Err
End Function

Private Sub dgAIMVelCodePcts_AfterUpdate(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    'Accumulate Percentage Totals
    AccumPcts
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_AfterUpdate)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_AfterUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMVelCodePcts_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Define Columns
    Me.dgAIMVelCodePcts.Columns(0).Name = "velcode"
    Me.dgAIMVelCodePcts.Columns(0).Caption = getTranslationResource("Code")
    Me.dgAIMVelCodePcts.Columns(0).Width = 600
    Me.dgAIMVelCodePcts.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMVelCodePcts.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgAIMVelCodePcts.Columns(0).Style = ssStyleEdit
    Me.dgAIMVelCodePcts.Columns(0).FieldLen = 1
    Me.dgAIMVelCodePcts.Columns(0).DataType = vbString
    Me.dgAIMVelCodePcts.Columns(0).Case = ssCaseUpper
        
    Me.dgAIMVelCodePcts.Columns(1).Name = "vcpct"
    Me.dgAIMVelCodePcts.Columns(1).Caption = getTranslationResource("Percent")
    Me.dgAIMVelCodePcts.Columns(1).Width = 1000
    Me.dgAIMVelCodePcts.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMVelCodePcts.Columns(1).Alignment = ssCaptionAlignmentRight
    Me.dgAIMVelCodePcts.Columns(1).Style = ssStyleEdit
    Me.dgAIMVelCodePcts.Columns(1).NumberFormat = "0.000"
    Me.dgAIMVelCodePcts.Columns(1).DataType = vbDouble
    Me.dgAIMVelCodePcts.Columns(1).PromptChar = " "
    
    Me.dgAIMVelCodePcts.Columns(2).Name = "Dft_OptionId"
    Me.dgAIMVelCodePcts.Columns(2).Caption = getTranslationResource("Default Option ID")
    Me.dgAIMVelCodePcts.Columns(2).Width = 2000
    Me.dgAIMVelCodePcts.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMVelCodePcts.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgAIMVelCodePcts.Columns(2).Style = ssStyleEdit
    Me.dgAIMVelCodePcts.Columns(2).DataType = vbString
    Me.dgAIMVelCodePcts.Columns(2).FieldLen = 6

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMVelCodePcts, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgAIMVelCodePcts.Columns.Count - 1
'        dgAIMVelCodePcts.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMVelCodePcts.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMVelCodePcts.Columns(IndexCounter).Locked = False Then dgAIMVelCodePcts.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_InitColumnProps)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMVelCodePcts_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_MouseDown)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMVelCodePcts_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG06100")
    If StrComp(strMessage, "RPTMSG06100") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG06101")
    If StrComp(strMessage1, "RPTMSG06101") = 0 Then strMessage = "Velocity Code Percentages Listing"
    strMessage2 = getTranslationResource("RPTMSG06102")
    If StrComp(strMessage2, "RPTMSG06102") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + vbTab + strMessage1 + vbTab _
            + strMessage2 + " <page number>"
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_PrintInitialize)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMVelCodePcts_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then Exit Sub
    
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsAIMVelCodePcts.MoveLast
        Else
            rsAIMVelCodePcts.MoveFirst
        End If
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsAIMVelCodePcts.Bookmark = StartLocation
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsAIMVelCodePcts.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsAIMVelCodePcts.Bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_UnboundPositionData)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMVelCodePcts_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim TotalRows, RowIndex As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAIMVelCodePcts.MoveLast
        Else
            rsAIMVelCodePcts.MoveFirst
        End If
    Else
        rsAIMVelCodePcts.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAIMVelCodePcts.MovePrevious
        Else
            rsAIMVelCodePcts.MoveNext
        End If
    
    End If
    
    For RowIndex = 0 To RowBuf.RowCount - 1
        If rsAIMVelCodePcts.BOF Or rsAIMVelCodePcts.eof Then Exit For
    
        RowBuf.Value(RowIndex, 0) = rsAIMVelCodePcts("velcode").Value
        RowBuf.Value(RowIndex, 1) = rsAIMVelCodePcts("vcpct").Value
        RowBuf.Value(RowIndex, 2) = rsAIMVelCodePcts("dft_optionid").Value
        
        RowBuf.Bookmark(RowIndex) = rsAIMVelCodePcts.Bookmark
    
        If ReadPriorRows Then
            rsAIMVelCodePcts.MovePrevious
        Else
            rsAIMVelCodePcts.MoveNext
        End If
    
        TotalRows = TotalRows + 1
    Next RowIndex
    
    RowBuf.RowCount = TotalRows

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_UnboundReadData)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMVelCodePcts_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    Cn.Errors.Clear

    If Not f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then Exit Sub
    
    rsAIMVelCodePcts.Bookmark = WriteLocation

    If Not IsNull(RowBuf.Value(0, 0)) Then
        rsAIMVelCodePcts("velcode").Value = RowBuf.Value(0, 0)
    End If

    If Not IsNull(RowBuf.Value(0, 1)) Then
        rsAIMVelCodePcts("vcpct").Value = RowBuf.Value(0, 1)
    End If

    If Not IsNull(RowBuf.Value(0, 2)) Then
        rsAIMVelCodePcts("dft_optionid").Value = RowBuf.Value(0, 2)
    End If
    
    'Update the Item Status Table
    rsAIMVelCodePcts.Update

    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG06100")
        If StrComp(strMessage, "ERRMSG06100") = 0 Then strMessage = "Error editing Velocity Code Percentage Record. "
        ADOErrorHandler Cn, strMessage
        rsAIMVelCodePcts.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG06100")
        If StrComp(strMessage, "STATMSG06100") = 0 Then strMessage = "Velocity Code Percentage successfully updated. "
        Write_Message strMessage
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMVelCodePcts_UnboundWriteData)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::dgAIMVelCodePcts_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG06101")
    If StrComp(strMessage, "STATMSG06101") = 0 Then strMessage = "Initializing Velocity Code Percentages Maintenance..."
    Write_Message strMessage

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build the SQL Statement
    SqlStmt = "SELECT * FROM AIMVelCodePcts ORDER BY VelCode "
    
    'Open the Record Set
    Set rsAIMVelCodePcts = New ADODB.Recordset
    With rsAIMVelCodePcts
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    rsAIMVelCodePcts.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dgAIMVelCodePcts.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then
        dgAIMVelCodePcts.Rows = rsAIMVelCodePcts.RecordCount
        mnuEdit.Enabled = True
    Else
        mnuEdit.Enabled = False
    End If
    
    'Calculate the Accumulative Percentage
    AccumPcts
    
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbVelCodePcts.Tools("ID_Save").Enabled = False
        Me.dgAIMVelCodePcts.AllowUpdate = False
    Else
        Me.dgAIMVelCodePcts.AllowUpdate = True
        Me.tbVelCodePcts.Tools("ID_Save").Enabled = True
    End If
    
    GetTranslatedCaptions Me
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMVelCodePcts) Then rsAIMVelCodePcts.Close
    Set rsAIMVelCodePcts = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_VelocityCodePercentages::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim RowIndex As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
    Case 0          'Copy to Clipboard
        If f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then
            Clipboard.Clear
            For RowIndex = 0 To rsAIMVelCodePcts.Fields.Count - 1
                s1 = s1 + rsAIMVelCodePcts.Fields(RowIndex).Name + vbTab
            Next RowIndex
            
            s1 = s1 + vbCrLf
                
            rsAIMVelCodePcts.MoveFirst
            s2 = rsAIMVelCodePcts.GetString(adClipString)
            
            Clipboard.SetText s1 + s2, vbCFText
        End If
    Case 1          'Print
        Me.dgAIMVelCodePcts.PrintData ssPrintAllRows, False, True
        
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbVelCodePcts_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Integer
    
    'Navigate
    Select Case Tool.ID
    Case "ID_AddNew"
        Me.dgAIMVelCodePcts.AddNew
        
    Case "ID_Delete"
        strMessage = getTranslationResource("MSGBOX06100")
        If StrComp(strMessage, "MSGBOX06100") = 0 Then strMessage = "Delete the current Velocity Code Percentages?"
        RtnCode = MsgBox(strMessage, vbQuestion + vbYesNo, getTranslationResource(Me.Caption))
        If RtnCode = vbYes Then
            Me.dgAIMVelCodePcts.DeleteSelected
        End If
        
    Case "ID_GetFirst"
        Me.dgAIMVelCodePcts.MoveFirst
    
    Case "ID_GetPrev"
        Me.dgAIMVelCodePcts.MovePrevious
    
    Case "ID_GetNext"
        Me.dgAIMVelCodePcts.MoveNext
    
    Case "ID_GetLast"
        Me.dgAIMVelCodePcts.MoveLast
    
    Case "ID_Save"
        Me.dgAIMVelCodePcts.Update
    
    Case "ID_SetBookMark"
        AIMVelCodePctsBookMark = Me.dgAIMVelCodePcts.Bookmark
        Me.tbVelCodePcts.Tools("ID_GoToBookMark").Enabled = True
    
    Case "ID_GoToBookMark"
        Me.dgAIMVelCodePcts.Bookmark = AIMVelCodePctsBookMark
    
    Case "ID_Refresh"
        Me.dgAIMVelCodePcts.ReBind
        If f_IsRecordsetOpenAndPopulated(rsAIMVelCodePcts) Then dgAIMVelCodePcts.Rows = rsAIMVelCodePcts.RecordCount
    
    Case "ID_Close"
        Unload Me
        Exit Sub

    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbVelCodePcts_ToolClick)"
     f_HandleErr , , , "AIM_VelocityCodePercentages::tbVelCodePcts_ToolClick", Now, gDRGeneralError, True, Err
End Sub
