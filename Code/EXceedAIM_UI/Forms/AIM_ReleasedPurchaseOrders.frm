VERSION 5.00
Object = "{A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0"; "tidate8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ReleasedPurchaseOrders 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Released Purchase Orders"
   ClientHeight    =   6480
   ClientLeft      =   30
   ClientTop       =   270
   ClientWidth     =   11160
   Icon            =   "AIM_ReleasedPurchaseOrders.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   6480
   ScaleWidth      =   11160
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgRelPO 
      Height          =   4755
      Left            =   0
      TabIndex        =   2
      Top             =   1230
      Width           =   11160
      _Version        =   196617
      AllowColumnSwapping=   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   19685
      _ExtentY        =   8387
      _StockProps     =   79
   End
   Begin VB.Frame Frame1 
      Caption         =   "Selection Criteria"
      Height          =   1185
      Left            =   0
      TabIndex        =   3
      Top             =   0
      Width           =   11115
      Begin TDBDate6Ctl.TDBDate txtRelDate 
         BeginProperty DataFormat 
            Type            =   1
            Format          =   "MM/dd/yyyy"
            HaveTrueFalseNull=   0
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   3
         EndProperty
         Height          =   340
         Left            =   2750
         TabIndex        =   1
         Top             =   640
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   2117
         _ExtentY        =   873
         Calendar        =   "AIM_ReleasedPurchaseOrders.frx":030A
         Caption         =   "AIM_ReleasedPurchaseOrders.frx":0422
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_ReleasedPurchaseOrders.frx":048E
         Keys            =   "AIM_ReleasedPurchaseOrders.frx":04AC
         Spin            =   "AIM_ReleasedPurchaseOrders.frx":050A
         AlignHorizontal =   0
         AlignVertical   =   0
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         CursorPosition  =   0
         DataProperty    =   0
         DisplayFormat   =   "mm/dd/yyyy"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   -1
         FirstMonth      =   4
         ForeColor       =   -2147483640
         Format          =   "mm/dd/yyyy"
         HighlightText   =   0
         IMEMode         =   3
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxDate         =   2958465
         MinDate         =   -657434
         MousePointer    =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
         PromptChar      =   "_"
         ReadOnly        =   0
         ShowContextMenu =   -1
         ShowLiterals    =   0
         TabAction       =   0
         Text            =   "10/10/2002"
         ValidateMode    =   0
         ValueVT         =   7
         Value           =   37539
         CenturyMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
         Height          =   340
         Left            =   2750
         TabIndex        =   0
         Top             =   280
         Width           =   1710
         DataFieldList   =   "Userid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserId"
      End
      Begin VB.Label Label 
         Caption         =   "Release Date"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   5
         Top             =   660
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Buyer ID"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   4
         Top             =   300
         Width           =   2450
      End
   End
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   120
      Top             =   6090
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_ReleasedPurchaseOrders.frx":0532
      ToolBars        =   "AIM_ReleasedPurchaseOrders.frx":3810
   End
   Begin VB.Menu mnuReleasedPO 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuReleasedPOOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuReleasedPOOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ReleasedPurchaseOrders"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim AIM_ReleasedPurchaseOrders_Sp As ADODB.Command

Dim rsRelPO As ADODB.Recordset

Dim ById As String
Dim RelDate As Date

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    Select Case Tool.ID
    Case "ID_Print"
        Me.dgRelPO.PrintData ssPrintAllRows, False, True
    
    Case "ID_Refresh"
        ById = Me.dcAIMUsers.Text
        RelDate = Me.txtRelDate.Text
        
        RtnCode = ReleasedPurchaseOrders(AIM_ReleasedPurchaseOrders_Sp, rsRelPO, ById, RelDate)
        If f_IsRecordsetOpenAndPopulated(rsRelPO) Then
            mnuReleasedPO.Enabled = True
        Else
            mnuReleasedPO.Enabled = False
        End If
        
        'Bind the Grid
        Set Me.dgRelPO.DataSource = rsRelPO
    
    Case "ID_Close"
        Unload Me
        Exit Sub
    
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
    
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "userid"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "userid"
    
    Me.dcAIMUsers.Columns(1).Name = "username"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = "username"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMusers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRelPO_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuReleasedPO
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRelPO_MouseDown)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::dgRelPO_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRelPO_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dgRelPO.Columns(0).Caption = getTranslationResource("Buyer")
    Me.dgRelPO.Columns(1).Caption = getTranslationResource("Vendor")
    Me.dgRelPO.Columns(2).Caption = getTranslationResource("Assort")
    Me.dgRelPO.Columns(3).Caption = getTranslationResource("Released")
    Me.dgRelPO.Columns(4).Caption = getTranslationResource("Nbr Lines")
    Me.dgRelPO.Columns(5).Caption = getTranslationResource("VSOQ")
    Me.dgRelPO.Columns(6).Caption = getTranslationResource("Ext Cost")

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgRelPO, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgRelPO.Columns.Count - 1
        dgRelPO.Columns(IndexCounter).Locked = True
'        dgRelPO.Columns(IndexCounter).HasHeadBackColor = True
'        dgRelPO.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgRelPO.Columns(IndexCounter).Locked = False Then dgRelPO.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRelPO_InitColumnProps)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::dgRelPO_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRelPO_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRelPO_PrintBegin)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::dgRelPO_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgRelPO_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("Release Date", True)
    strMessage1 = getTranslationResource("RPTMSG04201", False)
    If StrComp(strMessage1, "RPTMSG04201") = 0 Then strMessage = "Released Purchase Orders"
    strMessage2 = getTranslationResource("RPTMSG04202")
    If StrComp(strMessage2, "RPTMSG04202") = 0 Then strMessage = "Page" & ": "

    ssPrintInfo.PageHeader = strMessage & Space(1) & Format(RelDate, gDateFormat) & vbTab & strMessage1 _
                & vbTab & strMessage2 & " <page number>"
    
    strMessage = getTranslationResource("RPTMSG04200")
    If StrComp(strMessage, "RPTMSG04200") = 0 Then strMessage = "Date: "
    ssPrintInfo.PageFooter = strMessage & Space(1) & Format(Date, gDateFormat)
    
    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgRelPO_PrintInitialize)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::dgRelPO_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SQL As String
    Dim strMessage As String
    Dim rsAIMUsers As ADODB.Recordset

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG04200")
    If StrComp(strMessage, "STATMSG04200") = 0 Then strMessage = "Initializing Released Purchase Orders..."
    Write_Message strMessage

    GetTranslatedCaptions Me
        
    'Initialize Release Date / Buyer Id
    ById = GetCurrentById()
    RelDate = Date
    SetTDBDate Me.txtRelDate, RelDate
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build/Bind the AIM User Drop Down
    Me.dcAIMUsers.Text = AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text
    
    SQL = "SELECT UserID, UserName FROM AIMUsers ORDER BY UserID"
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open SQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dcAIMUsers.AddItem getTranslationResource("All") _
            & vbTab & getTranslationResource("All Users")
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserId").Value + vbTab + rsAIMUsers("Username").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
    'Initialize the Released Purchase Order Record Set
    Set AIM_ReleasedPurchaseOrders_Sp = New ADODB.Command
    With AIM_ReleasedPurchaseOrders_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ReleasedPurchaseOrders_Sp"
        .Parameters.Refresh
    End With
        
    Set rsRelPO = New ADODB.Recordset
    With rsRelPO
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    RtnCode = ReleasedPurchaseOrders(AIM_ReleasedPurchaseOrders_Sp, rsRelPO, ById, Date)
    If f_IsRecordsetOpenAndPopulated(rsRelPO) Then
        mnuReleasedPO.Enabled = True
    Else
        mnuReleasedPO.Enabled = False
    End If
    
    ' dgRelPO_InitColumnProps
    
    'Bind the Grid
    Set Me.dgRelPO.DataSource = rsRelPO
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsRelPO) Then rsRelPO.Close
    Set rsRelPO = Nothing
    
    If Not AIM_ReleasedPurchaseOrders_Sp Is Nothing Then Set AIM_ReleasedPurchaseOrders_Sp.ActiveConnection = Nothing
    Set AIM_ReleasedPurchaseOrders_Sp = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, True)
    Set Cn = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
        f_HandleErr , , , "AIM_ReleasedPurchaseOrders::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuReleasedPOOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer
    Dim s1 As String
    Dim s2 As String
    
    Select Case Index
    Case 0          'Copy to Clipboard
        If Not f_IsRecordsetOpenAndPopulated(rsRelPO) Then Exit Sub
        
        Clipboard.Clear
        
        For IndexCounter = 0 To rsRelPO.Fields.Count - 1
            s1 = s1 + rsRelPO.Fields(IndexCounter).Name + vbTab
        Next IndexCounter
        
        s1 = s1 + vbCrLf
            
        rsRelPO.MoveFirst
        s2 = rsRelPO.GetString(adClipString)
        
        Clipboard.SetText s1 + s2, vbCFText
        
    Case 1          'Print
        Me.dgRelPO.PrintData ssPrintAllRows, False, True
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuReleasedPOOpt_Click)"
     f_HandleErr , , , "AIM_ReleasedPurchaseOrders::mnuReleasedPOOpt_Click", Now, gDRGeneralError, True, Err
End Sub
