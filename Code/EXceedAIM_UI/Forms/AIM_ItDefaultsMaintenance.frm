VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ItDefaultsMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Item Defaults Maintenance"
   ClientHeight    =   7020
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10080
   Icon            =   "AIM_ItDefaultsMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   7020
   ScaleWidth      =   10080
   ShowInTaskbar   =   0   'False
   Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMSeasons 
      Height          =   945
      Left            =   4440
      TabIndex        =   3
      Top             =   2010
      Width           =   1725
      DataFieldList   =   "said"
      _Version        =   196617
      Cols            =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      _ExtentX        =   3043
      _ExtentY        =   1667
      _StockProps     =   77
      DataFieldToDisplay=   "said"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMLocations 
      Height          =   945
      Left            =   360
      TabIndex        =   1
      Top             =   1950
      Width           =   1725
      DataFieldList   =   "lcid"
      _Version        =   196617
      Cols            =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      _ExtentX        =   3043
      _ExtentY        =   1667
      _StockProps     =   77
      DataFieldToDisplay=   "lcid"
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   30
      Top             =   6660
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   13
      Style           =   0
      Tools           =   "AIM_ItDefaultsMaintenance.frx":030A
      ToolBars        =   "AIM_ItDefaultsMaintenance.frx":A83E
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMClasses 
      Height          =   945
      Left            =   2340
      TabIndex        =   2
      Top             =   1980
      Width           =   1725
      DataFieldList   =   "class"
      _Version        =   196617
      Cols            =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      _ExtentX        =   3043
      _ExtentY        =   1667
      _StockProps     =   77
      DataFieldToDisplay=   "class"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMUsers 
      Height          =   945
      Left            =   6630
      TabIndex        =   4
      Top             =   2010
      Width           =   1725
      DataFieldList   =   "userid"
      _Version        =   196617
      Cols            =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      _ExtentX        =   3043
      _ExtentY        =   1667
      _StockProps     =   77
      DataFieldToDisplay=   "userid"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBDropDown ddAIMOptions 
      Height          =   945
      Left            =   300
      TabIndex        =   5
      Top             =   3000
      Width           =   1725
      DataFieldList   =   "optionid"
      _Version        =   196617
      Cols            =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      Columns(0).Width=   3200
      _ExtentX        =   3043
      _ExtentY        =   1667
      _StockProps     =   77
      DataFieldToDisplay=   "optionid"
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgItDefaults 
      Align           =   1  'Align Top
      Height          =   6525
      Left            =   0
      TabIndex        =   0
      Top             =   0
      Width           =   10080
      _Version        =   196617
      DataMode        =   1
      AllowAddNew     =   -1  'True
      AllowDelete     =   -1  'True
      AllowColumnSwapping=   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   17780
      _ExtentY        =   11509
      _StockProps     =   79
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ItDefaultsMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMClasses As New ADODB.Recordset
Dim rsAIMLocationsList As New ADODB.Recordset
Dim rsAIMOptions As New ADODB.Recordset
Dim rsAIMSeasons As New ADODB.Recordset
Dim rsAIMUsers As New ADODB.Recordset
Dim rsItDefaults As New ADODB.Recordset

Dim ItDefaultsBookMark As Variant

Private Sub ddAIMLocations_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.ddAIMLocations.Columns(0).Name = "lcid"
    Me.ddAIMLocations.Columns(0).Caption = getTranslationResource("Location")
    Me.ddAIMLocations.Columns(0).Width = 1000
    Me.ddAIMLocations.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMLocations.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMLocations.Columns(0).DataField = "lcid"
    
    Me.ddAIMLocations.Columns(1).Name = "lname"
    Me.ddAIMLocations.Columns(1).Caption = getTranslationResource("Location Name")
    Me.ddAIMLocations.Columns(1).Width = 2880
    Me.ddAIMLocations.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMLocations.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMLocations.Columns(1).DataField = "lname"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths ddAIMLocations, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To ddAIMLocations.Columns.Count - 1
'        ddAIMLocations.Columns(IndexCounter).HasHeadBackColor = True
'        ddAIMLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddAIMLocations_InitColumnProps)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::ddAIMLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub ddAIMClasses_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.ddAIMClasses.Columns(0).Name = "ClassLevel"
    Me.ddAIMClasses.Columns(0).Caption = getTranslationResource("Class Level")
    Me.ddAIMClasses.Columns(0).Width = 1000
    Me.ddAIMClasses.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMClasses.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMClasses.Columns(0).DataField = "classlevel"
    
    Me.ddAIMClasses.Columns(1).Name = "Class"
    Me.ddAIMClasses.Columns(1).Caption = getTranslationResource("Class")
    Me.ddAIMClasses.Columns(1).Width = 1000
    Me.ddAIMClasses.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMClasses.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMClasses.Columns(1).DataField = "class"
    
    Me.ddAIMClasses.Columns(2).Name = "ClassDesc"
    Me.ddAIMClasses.Columns(2).Caption = getTranslationResource("Class Description")
    Me.ddAIMClasses.Columns(2).Width = 2880
    Me.ddAIMClasses.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMClasses.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMClasses.Columns(2).DataField = "classdesc"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths ddAIMClasses, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To ddAIMClasses.Columns.Count - 1
'        ddAIMClasses.Columns(IndexCounter).HasHeadBackColor = True
'        ddAIMClasses.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddAIMClasses_InitColumnProps)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::ddAIMClasses_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub ddAIMOptions_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.ddAIMOptions.Columns(0).Name = "OptionId"
    Me.ddAIMOptions.Columns(0).Caption = getTranslationResource("Option ID")
    Me.ddAIMOptions.Columns(0).Width = 1000
    Me.ddAIMOptions.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMOptions.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMOptions.Columns(0).DataField = "OptionId"
    
    Me.ddAIMOptions.Columns(1).Name = "OpDesc"
    Me.ddAIMOptions.Columns(1).Caption = getTranslationResource("Description")
    Me.ddAIMOptions.Columns(1).Width = 2880
    Me.ddAIMOptions.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMOptions.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMOptions.Columns(1).DataField = "OpDesc"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths ddAIMOptions, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To ddAIMOptions.Columns.Count - 1
'        ddAIMOptions.Columns(IndexCounter).HasHeadBackColor = True
'        ddAIMOptions.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddAIMOptions_InitColumnProps)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::ddAIMOptions_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub ddAIMSeasons_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.ddAIMSeasons.Columns(0).Name = "said"
    Me.ddAIMSeasons.Columns(0).Caption = getTranslationResource("Seasonality Index")
    Me.ddAIMSeasons.Columns(0).Width = 1000
    Me.ddAIMSeasons.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMSeasons.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMSeasons.Columns(0).DataField = "said"
    
    Me.ddAIMSeasons.Columns(1).Name = "saDesc"
    Me.ddAIMSeasons.Columns(1).Caption = getTranslationResource("Description")
    Me.ddAIMSeasons.Columns(1).Width = 2880
    Me.ddAIMSeasons.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMSeasons.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMSeasons.Columns(1).DataField = "sadesc"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths ddAIMSeasons, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To ddAIMSeasons.Columns.Count - 1
'        ddAIMSeasons.Columns(IndexCounter).HasHeadBackColor = True
'        ddAIMSeasons.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddAIMSeasons_InitColumnProps)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::ddAIMSeasons_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub ddAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.ddAIMUsers.Columns(0).Name = "userid"
    Me.ddAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.ddAIMUsers.Columns(0).Width = 1000
    Me.ddAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMUsers.Columns(0).DataField = "userid"
    
    Me.ddAIMUsers.Columns(1).Name = "username"
    Me.ddAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.ddAIMUsers.Columns(1).Width = 2880
    Me.ddAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.ddAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.ddAIMUsers.Columns(1).DataField = "username"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths ddAIMUsers, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To ddAIMUsers.Columns.Count - 1
'        ddAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        ddAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(ddAIMUsers_InitColumnProps)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::ddAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgItDefaults_AfterInsert(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    If Cn.Errors.Count > 0 Then
        RtnDispErrMsg = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_AfterInsert)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_AfterInsert", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    InvalidData = UserInputValidation(strMessage, ColIndex)
    
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
    End If
    
    Cancel = InvalidData
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_BeforeColUpdate)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
On Error GoTo ErrorHandler
    
    Dim Result As Long
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Cancel the default prompt
    DispPromptMsg = False
    
    'Display a confirmation msgbox
    strMessage = getTranslationResource("MSGBOX01800")
    If StrComp(strMessage, "MSGBOX01800") = 0 Then strMessage = "Delete the"
    
    strMessage = strMessage & " " & str(dgItDefaults.SelBookmarks.Count) & " "
    strMessage1 = getTranslationResource("MSGBOX01801")
    If StrComp(strMessage1, "MSGBOX01801") = 0 Then strMessage1 = "selected Item Default(s)?"
    strMessage = strMessage & strMessage1

    Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ItDefaultsMaintenance.Caption))

    Select Case Result
    Case vbYes
        'User chose to delete. Do nothing
    Case vbNo
        'User chose to Cancel
        Cancel = True
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_BeforeDelete )"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub


Private Sub dgItDefaults_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim InvalidData As Boolean
    Dim strMessage As String
    Dim RtnCode As Long
    
    InvalidData = UserInputValidation(strMessage)
    
    If InvalidData = True Then
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
    End If
    
    Cancel = InvalidData
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_BeforeUpdate)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsItDefaults)) Then Exit Sub
    
    'Check for unsortable column index
'    If Me.dgItDefaults.Columns(ColIndex).Name = "" _
'    Then Exit Sub
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
    
    If InStr(rsItDefaults.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgItDefaults.Columns(ColIndex).Name
    ElseIf InStr(rsItDefaults.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgItDefaults.Columns(ColIndex).Name
    End If
    
    'Sort grid by selected column
    rsItDefaults.Sort = Trim(ColName & " " & SortSeq)

    Me.dgItDefaults.ReBind
    If f_IsRecordsetOpenAndPopulated(rsItDefaults) Then Me.dgItDefaults.Rows = rsItDefaults.RecordCount
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_HeadClick)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Define Columns
    Me.dgItDefaults.Columns(0).Name = "lcid"
    Me.dgItDefaults.Columns(0).Caption = getTranslationResource("Location")
    Me.dgItDefaults.Columns(0).Width = 1000
    Me.dgItDefaults.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dgItDefaults.Columns(0).Style = ssStyleEdit
    Me.dgItDefaults.Columns(0).FieldLen = 12
    Me.dgItDefaults.Columns(0).DataType = vbString
    Me.dgItDefaults.Columns(0).DropDownHwnd = Me.ddAIMLocations.hwnd
     
    Me.dgItDefaults.Columns(1).Name = "class"
    Me.dgItDefaults.Columns(1).Caption = getTranslationResource("Class")
    Me.dgItDefaults.Columns(1).Width = 1000
    Me.dgItDefaults.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgItDefaults.Columns(1).Style = ssStyleEdit
    Me.dgItDefaults.Columns(1).FieldLen = 6
    Me.dgItDefaults.Columns(1).DataType = vbString
    Me.dgItDefaults.Columns(1).DropDownHwnd = Me.ddAIMClasses.hwnd
    
    Me.dgItDefaults.Columns(2).Name = "said"
    Me.dgItDefaults.Columns(2).Caption = getTranslationResource("Seasonality Index")
    Me.dgItDefaults.Columns(2).Width = 1440
    Me.dgItDefaults.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgItDefaults.Columns(2).Style = ssStyleEdit
    Me.dgItDefaults.Columns(2).FieldLen = 20
    Me.dgItDefaults.Columns(2).DataType = vbString
    Me.dgItDefaults.Columns(2).DropDownHwnd = Me.ddAIMSeasons.hwnd

    Me.dgItDefaults.Columns(3).Name = "velcode"
    Me.dgItDefaults.Columns(3).Caption = getTranslationResource("Vel Code")
    Me.dgItDefaults.Columns(3).Width = 800
    Me.dgItDefaults.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(3).Alignment = ssCaptionAlignmentCenter
    Me.dgItDefaults.Columns(3).Style = ssStyleComboBox
    Me.dgItDefaults.Columns(3).FieldLen = 1
    Me.dgItDefaults.Columns(3).DataType = vbString
    Me.dgItDefaults.Columns(3).Case = ssCaseUpper
    'Initialize Combo Box
    Me.dgItDefaults.Columns(3).AddItem "A"
    Me.dgItDefaults.Columns(3).AddItem "B"
    Me.dgItDefaults.Columns(3).AddItem "C"
    Me.dgItDefaults.Columns(3).AddItem "D"
    Me.dgItDefaults.Columns(3).AddItem "E"
    Me.dgItDefaults.Columns(3).AddItem "F"
    Me.dgItDefaults.Columns(3).AddItem "G"
    Me.dgItDefaults.Columns(3).AddItem "H"
    Me.dgItDefaults.Columns(3).AddItem "I"
    Me.dgItDefaults.Columns(3).AddItem "J"
    Me.dgItDefaults.Columns(3).AddItem "K"
    Me.dgItDefaults.Columns(3).AddItem "L"

    Me.dgItDefaults.Columns(4).Name = "buystrat"
    Me.dgItDefaults.Columns(4).Caption = getTranslationResource("Strategy")
    Me.dgItDefaults.Columns(4).Width = 1000
    Me.dgItDefaults.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgItDefaults.Columns(4).Style = ssStyleComboBox
    Me.dgItDefaults.Columns(4).FieldLen = 1
    Me.dgItDefaults.Columns(4).DataType = vbString
    Me.dgItDefaults.Columns(4).Case = ssCaseUpper
    'Initialize Combo Box
    Me.dgItDefaults.Columns(4).AddItem "M"
    Me.dgItDefaults.Columns(4).AddItem "O"
    Me.dgItDefaults.Columns(4).AddItem "T"
    
    Me.dgItDefaults.Columns(5).Name = "byid"
    Me.dgItDefaults.Columns(5).Caption = getTranslationResource("Buyer")
    Me.dgItDefaults.Columns(5).Width = 1000
    Me.dgItDefaults.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(5).Alignment = ssCaptionAlignmentLeft
    Me.dgItDefaults.Columns(5).Style = ssStyleEdit
    Me.dgItDefaults.Columns(5).FieldLen = 12
    Me.dgItDefaults.Columns(5).DataType = vbString
    Me.dgItDefaults.Columns(5).DropDownHwnd = Me.ddAIMUsers.hwnd
    
    Me.dgItDefaults.Columns(6).Name = "replencost2"
    Me.dgItDefaults.Columns(6).Caption = getTranslationResource("Replen Cost")
    Me.dgItDefaults.Columns(6).Width = 1000
    Me.dgItDefaults.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgItDefaults.Columns(6).Style = ssStyleEdit
    Me.dgItDefaults.Columns(6).FieldLen = 6
    Me.dgItDefaults.Columns(6).DataType = vbDouble
    Me.dgItDefaults.Columns(6).Mask = "999.99"
    Me.dgItDefaults.Columns(6).PromptChar = " "
    
    Me.dgItDefaults.Columns(7).Name = "dser"
    Me.dgItDefaults.Columns(7).Caption = getTranslationResource("Srv Lvl")
    Me.dgItDefaults.Columns(7).Width = 1000
    Me.dgItDefaults.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgItDefaults.Columns(7).Style = ssStyleEdit
    Me.dgItDefaults.Columns(7).FieldLen = 4
    Me.dgItDefaults.Columns(7).DataType = vbDouble
    Me.dgItDefaults.Columns(7).Mask = "9.99"
    Me.dgItDefaults.Columns(7).PromptChar = " "
    
    Me.dgItDefaults.Columns(8).Name = "optionid"
    Me.dgItDefaults.Columns(8).Caption = getTranslationResource("Option ID")
    Me.dgItDefaults.Columns(8).Width = 1000
    Me.dgItDefaults.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgItDefaults.Columns(8).Alignment = ssCaptionAlignmentLeft
    Me.dgItDefaults.Columns(8).Style = ssStyleEdit
    Me.dgItDefaults.Columns(8).FieldLen = 6
    Me.dgItDefaults.Columns(8).DataType = vbString
    Me.dgItDefaults.Columns(8).DropDownHwnd = Me.ddAIMOptions.hwnd
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgItDefaults, ACW_EXPAND
    End If
    
    Me.dgItDefaults.RowNavigation = ssRowNavigationUDLock
        
    For IndexCounter = 0 To dgItDefaults.Columns.Count - 1
'        dgItDefaults.Columns(IndexCounter).HasHeadBackColor = True
'        dgItDefaults.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgItDefaults.Columns(IndexCounter).Locked = False Then dgItDefaults.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_InitColumnProps)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_MouseDown)"
	 f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_PrintBegin)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG01800")
    If StrComp(strMessage, "RPTMSG01800") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG01801")
    If StrComp(strMessage1, "RPTMSG01801") = 0 Then strMessage = "AIM Item Defaults Listing"
    strMessage2 = getTranslationResource("RPTMSG01802")
    If StrComp(strMessage2, "RPTMSG01802") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & strMessage1 & _
                            vbTab & strMessage2 & " <page number>"

    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_PrintInitialize)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    
    If Not f_IsRecordsetOpenAndPopulated(rsItDefaults) Then Exit Sub
    
    If Not UserInputValidation(strMessage) Then
        rsItDefaults.AddNew
    
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsItDefaults("lcid").Value = RowBuf.Value(0, 0)
        Else
            rsItDefaults("lcid").Value = ""
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsItDefaults("class").Value = RowBuf.Value(0, 1)
        Else
            rsItDefaults("class").Value = ""
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsItDefaults("said").Value = RowBuf.Value(0, 2)
        Else
            rsItDefaults("said").Value = getTranslationResource("DEFAULT")
        End If
            
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsItDefaults("velcode").Value = RowBuf.Value(0, 3)
        Else
            rsItDefaults("velcode").Value = getTranslationResource("C")
        End If
    
        If Not IsNull(RowBuf.Value(0, 4)) Then
            rsItDefaults("buystrat").Value = RowBuf.Value(0, 4)
        Else
            rsItDefaults("buystrat").Value = getTranslationResource("O")
        End If
    
        If Not IsNull(RowBuf.Value(0, 5)) Then
            rsItDefaults("byid").Value = RowBuf.Value(0, 5)
        Else
            rsItDefaults("byid").Value = ""
        End If
    
        If Not IsNull(RowBuf.Value(0, 6)) Then
            rsItDefaults("replencost2").Value = RowBuf.Value(0, 6)
        Else
            rsItDefaults("replencost2").Value = 0
        End If
    
        If Not IsNull(RowBuf.Value(0, 7)) Then
            rsItDefaults("dser").Value = RowBuf.Value(0, 7)
        Else
            rsItDefaults("dser").Value = 0.95
        End If
    
        If Not IsNull(RowBuf.Value(0, 8)) Then
            rsItDefaults("optionid").Value = RowBuf.Value(0, 8)
        Else
            rsItDefaults("optionid").Value = getTranslationResource("DEFLT")
        End If
    
        rsItDefaults.Update
        
        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG01800")
            If StrComp(strMessage, "ERRMSG01800") = 0 Then strMessage = "Error adding AIM Forecast Method Record."
            ADOErrorHandler Cn, strMessage
            rsItDefaults.CancelUpdate
        Else
            strMessage = getTranslationResource("STATMSG01800")
            If StrComp(strMessage, "STATMSG01800") = 0 Then strMessage = "AIM Forecast Method successfully added."
            Write_Message strMessage
            rsItDefaults.MoveLast
            NewRowBookmark = rsItDefaults.bookmark
        End If
        
    Else
        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
        
    End If
    
    Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_UnboundAddData)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_UnboundAddData", Now, gDRGeneralError, True, Err
    rsItDefaults.CancelUpdate
End Sub

Private Sub dgItDefaults_UnboundDeleteRow(bookmark As Variant)
On Error GoTo ErrorHandler
    
    If Not f_IsRecordsetOpenAndPopulated(rsItDefaults) Then Exit Sub

    rsItDefaults.bookmark = bookmark
    rsItDefaults.Delete

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_UnboundDeleteRow)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If Not f_IsRecordsetOpenAndPopulated(rsItDefaults) Then Exit Sub
    If dgItDefaults.DataChanged Then Exit Sub
    If dgItDefaults.IsAddRow Then Exit Sub
    
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsItDefaults.MoveLast
        Else
            rsItDefaults.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsItDefaults.bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsItDefaults.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsItDefaults.bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_UnboundPositionData)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r, i As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsItDefaults) Then Exit Sub
    If dgItDefaults.DataChanged Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsItDefaults.MoveLast
        Else
            rsItDefaults.MoveFirst
        End If
    
    Else
        rsItDefaults.bookmark = StartLocation
        If ReadPriorRows Then
            rsItDefaults.MovePrevious
        Else
            rsItDefaults.MoveNext
        End If
    
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsItDefaults.BOF Or rsItDefaults.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsItDefaults("lcid").Value
        RowBuf.Value(i, 1) = rsItDefaults("class").Value
        RowBuf.Value(i, 2) = rsItDefaults("said").Value
        RowBuf.Value(i, 3) = rsItDefaults("velcode").Value
        RowBuf.Value(i, 4) = rsItDefaults("buystrat").Value
        RowBuf.Value(i, 5) = rsItDefaults("byid").Value
        RowBuf.Value(i, 6) = rsItDefaults("replencost2").Value
        RowBuf.Value(i, 7) = rsItDefaults("dser").Value
        RowBuf.Value(i, 8) = rsItDefaults("optionid").Value
        
        RowBuf.bookmark(i) = rsItDefaults.bookmark
    
        If ReadPriorRows Then
            rsItDefaults.MovePrevious
        Else
            rsItDefaults.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_UnboundReadData)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgItDefaults_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    
    If Not f_IsRecordsetOpenAndPopulated(rsItDefaults) Then Exit Sub
    
    If Not UserInputValidation(strMessage) Then
        rsItDefaults.bookmark = WriteLocation
    
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsItDefaults("lcid").Value = RowBuf.Value(0, 0)
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsItDefaults("class").Value = RowBuf.Value(0, 1)
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsItDefaults("said").Value = RowBuf.Value(0, 2)
        End If
    
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsItDefaults("velcode").Value = RowBuf.Value(0, 3)
        End If
    
        If Not IsNull(RowBuf.Value(0, 4)) Then
            rsItDefaults("buystrat").Value = RowBuf.Value(0, 4)
        End If
    
        If Not IsNull(RowBuf.Value(0, 5)) Then
            rsItDefaults("byid").Value = RowBuf.Value(0, 5)
        End If
    
        If Not IsNull(RowBuf.Value(0, 6)) Then
            rsItDefaults("replencost2").Value = RowBuf.Value(0, 6)
        End If
    
        If Not IsNull(RowBuf.Value(0, 7)) Then
            rsItDefaults("dser").Value = RowBuf.Value(0, 7)
        End If
    
        If Not IsNull(RowBuf.Value(0, 8)) Then
            rsItDefaults("optionid").Value = RowBuf.Value(0, 8)
        End If
    
        'Update the Item Status Table
        rsItDefaults.Update
    
        If Cn.Errors.Count > 0 Then
            strMessage = getTranslationResource("ERRMSG01801")
            If StrComp(strMessage, "ERRMSG01801") = 0 Then strMessage = "Error editing AIM Item Defaults Record."
            ADOErrorHandler Cn, strMessage
            rsItDefaults.CancelUpdate
        Else
            strMessage = getTranslationResource("STATMSG01801")
            If StrComp(strMessage, "STATMSG01801") = 0 Then strMessage = "AIM Item Defaults Record successfully updated."
            Write_Message strMessage
        End If

    Else
        MsgBox strMessage, vbOKOnly, getTranslationResource(Me.Caption)
        rsItDefaults.CancelUpdate
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgItDefaults_UnboundWriteData)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::dgItDefaults_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim SqlStmt As String
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01802")
    If StrComp(strMessage, "STATMSG01802") = 0 Then strMessage = "Initializing AIM Item Defaults Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
       
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build the SQL Statement
    SqlStmt = "SELECT * FROM ItDefaults " & _
            " ORDER BY LcId, Class "
    
    'Open the Record Sets
    With rsItDefaults
        .CursorLocation = adUseClient
        .CursorType = adOpenDynamic
        .LockType = adLockOptimistic
    End With
    
    rsItDefaults.Open SqlStmt, Cn
    
    'Rebind the grid
    Me.dgItDefaults.ReBind
    If f_IsRecordsetOpenAndPopulated(rsItDefaults) Then dgItDefaults.Rows = rsItDefaults.RecordCount
    
    'Build/Bind the Location Drop Down
    SqlStmt = "SELECT Lcid, LName FROM AIMLocations " & _
            " ORDER BY LcId "
    
    rsAIMLocationsList.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMLocationsList) Then
        Set Me.ddAIMLocations.DataSource = rsAIMLocationsList
    End If
    
    'Build/Bind the Classes Drop Down
    '4.2 Updates -- Add class level to AIMClasses.
    'Update Dropdown to display Class Code from AIMClasses
    'where Class Level = SysCtrl.ClassOption and LangID = User's LangID     'la here -- fetchaimclasses?
    SqlStmt = "SELECT AIMClasses.ClassLevel, AIMClasses.Class, AIMClasses.ClassDesc " & _
            " FROM AIMClasses, SysCtrl " & _
            " WHERE AIMClasses.ClassLevel = SysCtrl.ClassOption " & _
            " AND AIMClasses.LangID = N'" & gLangID & "' " & _
            " ORDER BY AIMClasses.ClassLevel, AIMClasses.Class "
    
    rsAIMClasses.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMClasses) Then Set Me.ddAIMClasses.DataSource = rsAIMClasses
    
    'Build/Bind the Seasonality Profile Drop Down
    SqlStmt = "SELECT distinct said, sadesc" & _
            " FROM AIMSeasons" & _
            " ORDER BY said "
    
    rsAIMSeasons.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMSeasons) Then
        Set Me.ddAIMSeasons.DataSource = rsAIMSeasons
    End If
    
    'Build/Bind the AIM User Drop Down
    SqlStmt = "select userid, username from AIMUsers order by userid "
    
    rsAIMUsers.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Set Me.ddAIMUsers.DataSource = rsAIMUsers
    End If
    
    'Build/Bind the AIM User Drop Down
    SqlStmt = "select OptionId, OpDesc from AIMOptions order by OptionId "
    
    rsAIMOptions.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly
    
    If f_IsRecordsetOpenAndPopulated(rsAIMOptions) Then
        Set Me.ddAIMOptions.DataSource = rsAIMOptions
    End If
    
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbNavigation.Tools("ID_Save").Enabled = False
        Me.dgItDefaults.AllowUpdate = False
        Me.dgItDefaults.AllowAddNew = False
        Me.dgItDefaults.AllowDelete = False
    Else
        Me.tbNavigation.Tools("ID_Save").Enabled = True
        Me.dgItDefaults.AllowUpdate = True
        Me.dgItDefaults.AllowAddNew = True
        Me.dgItDefaults.AllowDelete = True
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMClasses) Then rsAIMClasses.Close
    If f_IsRecordsetValidAndOpen(rsAIMLocationsList) Then rsAIMLocationsList.Close
    If f_IsRecordsetValidAndOpen(rsAIMOptions) Then rsAIMOptions.Close
    If f_IsRecordsetValidAndOpen(rsAIMSeasons) Then rsAIMSeasons.Close
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    If f_IsRecordsetValidAndOpen(rsItDefaults) Then rsItDefaults.Close

    Set rsAIMClasses = Nothing
    Set rsAIMLocationsList = Nothing
    Set rsAIMOptions = Nothing
    Set rsAIMSeasons = Nothing
    Set rsAIMUsers = Nothing
    Set rsItDefaults = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
		f_HandleErr , , , "AIM_ItDefaultsMaintenance::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If f_IsRecordsetOpenAndPopulated(rsItDefaults) Then
                Clipboard.Clear
                
                For i = 0 To rsItDefaults.Fields.Count - 1
                    s1 = s1 & rsItDefaults.Fields(i).Name & vbTab
                Next i
                
                s1 = s1 & vbCrLf
                    
                rsItDefaults.MoveFirst
                s2 = rsItDefaults.GetString(adClipString)
                
                Clipboard.SetText s1 & s2, vbCFText
            End If
            
        Case 1          'Print
            Me.dgItDefaults.PrintData ssPrintAllRows, False, True
            
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub
 
Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    If dgItDefaults.DataChanged = True _
    Or dgItDefaults.IsAddRow = True _
    Then
        dgItDefaults.Update
    End If
    
    'Navigate
    Select Case Tool.ID
        Case "ID_AddNew"
            Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
            Me.dgItDefaults.AddNew
            
        Case "ID_Delete"
            Me.dgItDefaults.DeleteSelected
        
        Case "ID_GetFirst"
            Me.dgItDefaults.MoveFirst
        
        Case "ID_GetPrev"
            Me.dgItDefaults.MovePrevious
        
        Case "ID_GetNext"
            Me.dgItDefaults.MoveNext
        
        Case "ID_GetLast"
            Me.dgItDefaults.MoveLast
        
        Case "ID_Save"
            Me.dgItDefaults.Update
        
        Case "ID_SetBookMark"
            ItDefaultsBookMark = Me.dgItDefaults.bookmark
            Me.tbNavigation.Tools("ID_GoToBookMark").Enabled = True
        
        Case "ID_GoToBookMark"
            Me.dgItDefaults.bookmark = ItDefaultsBookMark
        
        Case "ID_Refresh"
            rsItDefaults.Sort = ""
            Me.dgItDefaults.ReBind
            If f_IsRecordsetOpenAndPopulated(rsItDefaults) Then dgItDefaults.Rows = rsItDefaults.RecordCount
            Me.tbNavigation.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")

        Case "ID_Close"
            Unload Me
            Exit Sub

    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
	f_HandleErr , , , "AIM_ItDefaultsMaintenance::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function UserInputValidation(p_Message As String, Optional p_ColIndex As Integer = -1) As Boolean
On Error GoTo ErrorHandler
    
    Dim lngCounter As Integer
    Dim LoopUpperBound As Integer
    Dim LoopLowerBound As Integer
    
    Dim RtnCode As Integer
    
    UserInputValidation = False
    
    'ErrorMessage
    p_Message = getTranslationResource("MSGBOX07312")
    If StrComp(p_Message, "MSGBOX07312") = 0 Then _
        p_Message = "The following data is required, or is invalid. Please provide correct values in the expected format for:"
        
    'Check if it's just one field, or all.
    If p_ColIndex >= 0 Then
        LoopLowerBound = p_ColIndex
        LoopUpperBound = p_ColIndex
    Else
        LoopLowerBound = 0
        LoopUpperBound = dgItDefaults.Columns.Count - 1
    End If

    For lngCounter = LoopLowerBound To LoopUpperBound
        Select Case lngCounter
        Case 0 'LcID
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Cancel = True
                p_Message = p_Message & vbCrLf & _
                            dgItDefaults.Columns(lngCounter).Caption
                UserInputValidation = True
            Else
                'Validate
                rsAIMLocationsList.MoveFirst
                rsAIMLocationsList.Find "lcid = '" & Trim(dgItDefaults.Columns(lngCounter).Value) & "'", , adSearchForward, 0
                If rsAIMLocationsList.eof Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True

                End If
            End If
            
        Case 1 'Class
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Cancel = True
                p_Message = p_Message & vbCrLf & _
                            dgItDefaults.Columns(lngCounter).Caption
                UserInputValidation = True
                Exit Function
            Else
                'Validate
                rsAIMClasses.MoveFirst
                rsAIMClasses.Find "Class = '" & Trim(dgItDefaults.Columns(lngCounter).Value) & "'", , adSearchForward, 0
                If rsAIMClasses.eof Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True


                End If
            End If
                
        Case 2 'SAID
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = getTranslationResource("DEFAULT")
            Else
                'Validate
                rsAIMSeasons.MoveFirst
                rsAIMSeasons.Find "SAID = '" & Trim(dgItDefaults.Columns(lngCounter).Value) & "'", , adSearchForward, 0
                If rsAIMSeasons.eof Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True


                End If
            End If
            
        Case 3 'VelCode
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = getTranslationResource("C")
            Else
                'Validate
                If UCase(dgItDefaults.Columns(lngCounter).Value) > "L" Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True


                End If
            End If
            
        Case 4 'BuyStrat
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = getTranslationResource("O")
            Else
                'Validate
                If (UCase(dgItDefaults.Columns(lngCounter).Value) <> "M" _
                And UCase(dgItDefaults.Columns(lngCounter).Value) <> "O" _
                And UCase(dgItDefaults.Columns(lngCounter).Value) <> "T") _
                Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True


                End If
            End If
            
        Case 5 'ByID
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = ""
            Else
                'Validate
                rsAIMUsers.MoveFirst
                rsAIMUsers.Find "UserID = '" & Trim(dgItDefaults.Columns(lngCounter).Value) & "'", , adSearchForward, 0
                If rsAIMUsers.eof Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True


                End If
            End If
            
        Case 6 'ReplenCost
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = 0
            Else
                'Validate
            End If
        
        Case 7 'DSer
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = 0.95
            Else
                'Validate
            End If
        
        Case 8 'Option ID
            If dgItDefaults.Columns(lngCounter).Value = "" _
            Or IsNull(dgItDefaults.Columns(lngCounter).Value) _
            Then
                'Default values
                dgItDefaults.Columns(lngCounter).Value = getTranslationResource("DEFLT")
            Else
                'Validate
                rsAIMOptions.MoveFirst
                rsAIMOptions.Find "OptionID = '" & Trim(dgItDefaults.Columns(lngCounter).Value) & "'", , adSearchForward, 0
                If rsAIMOptions.eof Then
                    'Cancel = True
                    p_Message = p_Message & vbCrLf & _
                                dgItDefaults.Columns(lngCounter).Caption
                    UserInputValidation = True

                End If
            End If
        End Select
        
    Next
    
    If UserInputValidation <> True Then p_Message = ""
    
Exit Function
ErrorHandler:
   'Err.Raise Err.Number, Err.source, Err.Description & "(UserInputValidation)"
   f_HandleErr , , , "AIM_ItDefaultsMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function


