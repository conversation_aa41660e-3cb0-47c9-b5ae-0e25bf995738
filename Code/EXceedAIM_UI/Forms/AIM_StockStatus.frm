VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_StockStatus 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Stock Status Report"
   ClientHeight    =   2310
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10065
   Icon            =   "AIM_StockStatus.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2310
   ScaleWidth      =   10065
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   240
      Top             =   1920
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_StockStatus.frx":030A
      ToolBars        =   "AIM_StockStatus.frx":35E1
   End
   Begin VB.Frame Frame3 
      Caption         =   "Base Forecast Demand On"
      Height          =   1830
      Left            =   4935
      TabIndex        =   13
      Top             =   60
      Width           =   5055
      Begin VB.OptionButton ckDemandOption 
         Caption         =   "Lead Time Forecast + n Units"
         Height          =   300
         Index           =   2
         Left            =   195
         TabIndex        =   7
         Tag             =   "4"
         Top             =   1010
         Width           =   3585
      End
      Begin VB.OptionButton ckDemandOption 
         Caption         =   "Lead Time Forecast + n Days"
         Height          =   300
         Index           =   1
         Left            =   195
         TabIndex        =   5
         Tag             =   "2"
         Top             =   610
         Width           =   3585
      End
      Begin VB.OptionButton ckDemandOption 
         Caption         =   "Lead Time Forecast"
         Height          =   300
         Index           =   0
         Left            =   195
         TabIndex        =   4
         Tag             =   "1"
         Top             =   210
         Value           =   -1  'True
         Width           =   3585
      End
      Begin TDBNumber6Ctl.TDBNumber txtDays 
         Height          =   345
         Left            =   3840
         TabIndex        =   6
         Top             =   565
         Width           =   960
         _Version        =   65536
         _ExtentX        =   1693
         _ExtentY        =   609
         Calculator      =   "AIM_StockStatus.frx":3733
         Caption         =   "AIM_StockStatus.frx":3753
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_StockStatus.frx":37BF
         Keys            =   "AIM_StockStatus.frx":37DD
         Spin            =   "AIM_StockStatus.frx":3827
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2010578949
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
      Begin TDBNumber6Ctl.TDBNumber txtUnits 
         Height          =   345
         Left            =   3840
         TabIndex        =   8
         Top             =   965
         Width           =   960
         _Version        =   65536
         _ExtentX        =   1693
         _ExtentY        =   609
         Calculator      =   "AIM_StockStatus.frx":384F
         Caption         =   "AIM_StockStatus.frx":386F
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_StockStatus.frx":38DB
         Keys            =   "AIM_StockStatus.frx":38F9
         Spin            =   "AIM_StockStatus.frx":3943
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "##0;-##0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "##0"
         HighlightText   =   0
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   999
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   327685
         Value           =   1
         MaxValueVT      =   5
         MinValueVT      =   5
      End
   End
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1830
      Left            =   80
      TabIndex        =   9
      Top             =   60
      Width           =   4695
      Begin VB.CheckBox ckExceptions 
         Caption         =   "Print Exceptions Only"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   300
         Left            =   200
         TabIndex        =   3
         Top             =   1410
         Value           =   1  'Checked
         Width           =   2450
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
         Bindings        =   "AIM_StockStatus.frx":396B
         Height          =   340
         Left            =   2750
         TabIndex        =   0
         Top             =   210
         Width           =   1710
         DataFieldList   =   "Userid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   3016
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserId"
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   340
         Left            =   2750
         TabIndex        =   1
         Top             =   570
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_StockStatus.frx":3984
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_StockStatus.frx":39F0
         Key             =   "AIM_StockStatus.frx":3A0E
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   340
         Left            =   2750
         TabIndex        =   2
         Top             =   930
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_StockStatus.frx":3A52
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_StockStatus.frx":3ABE
         Key             =   "AIM_StockStatus.frx":3ADC
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label Label 
         Caption         =   "Vendor ID"
         Height          =   300
         Index           =   1
         Left            =   200
         TabIndex        =   12
         Top             =   610
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   2
         Left            =   200
         TabIndex        =   11
         Top             =   970
         Width           =   2450
      End
      Begin VB.Label Label 
         Caption         =   "Buyer ID"
         Height          =   300
         Index           =   0
         Left            =   200
         TabIndex        =   10
         Top             =   250
         Width           =   2450
      End
   End
End
Attribute VB_Name = "AIM_StockStatus"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection
    
Private Function BldSQL( _
    ById As String, VnId As String, Assort As String, _
    Exceptions As Integer, DemandOption As Integer, _
    nDays As Integer, nUnits As Integer)
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim WhrStmt As String
    Dim AvailQty As String
    
    'Default nDays and nUnits to at least 1
    nDays = IIf(nDays <= 0, 1, nDays)
    nUnits = IIf(nUnits <= 0, 1, nUnits)
    
    AvailQty = " (Item.Oh + Item.Oo - Item.ComStk - Item.BkOrder - Item.BkComStk)"
    
    strSQL = "SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ItStat, Item.VelCode,"
    strSQL = strSQL & vbCrLf & " Item.VnId, Item.Assort, Item.ById, Item.UOM,"
    strSQL = strSQL & vbCrLf & AvailQty & " AS AvailQty,"
    strSQL = strSQL & vbCrLf & " Item.FcstDemand,"
    strSQL = strSQL & vbCrLf & " Accum_Lt = CASE Item.Accum_Lt"
    strSQL = strSQL & vbCrLf & "    WHEN 0 THEN 1"
    strSQL = strSQL & vbCrLf & "    ELSE Accum_Lt"
    strSQL = strSQL & vbCrLf & "    END,"
    strSQL = strSQL & vbCrLf & " Item.FcstLT,"
    strSQL = strSQL & vbCrLf & " Item.OrderPt , Item.OrderQty, ItStatus.Ordgen"
    strSQL = strSQL & vbCrLf & " FROM Item"
    strSQL = strSQL & vbCrLf & " INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat"
    
    'Build the Where Clause
    WhrStmt = "WHERE ItStatus.Ordgen = N'Y' "
    
    If Trim$(ById) <> getTranslationResource("All") Then WhrStmt = WhrStmt & vbCrLf & " AND Item.ById = ? "
    If Trim$(VnId) <> "" Then WhrStmt = WhrStmt & vbCrLf & " AND Item.VnId = ? "
    If Trim$(Assort) <> "" Then WhrStmt = WhrStmt & vbCrLf & " AND Item.Assort = ? "
    
    If Exceptions = VALUETRUE Then
        Select Case DemandOption
        Case 1
            'Lead Time Forecast
            WhrStmt = WhrStmt & vbCrLf & " AND FcstLT >= " & AvailQty
        Case 2
            'Divide by zero -- avoid that -- check accumlt for zeros
            'Lead Time Forecast + n nDays
            WhrStmt = WhrStmt & vbCrLf & " AND (CASE" & _
                    vbCrLf & "  WHEN Item.Accum_LT <= 0 " & _
                    vbCrLf & "  THEN FcstLT + FcstLT/" & Format(nDays, "0") & _
                    vbCrLf & "  ELSE FcstLT + FcstLT/Accum_LT * " & Format(nDays, "0") & _
                    vbCrLf & " END) >= " & AvailQty
        Case 4      'Lead Time Forecast + n nUnits
            WhrStmt = WhrStmt & vbCrLf & "AND (FcstLT + " & Format(nUnits, "0") & ")" & _
                    " >= " & AvailQty
        End Select
    End If
        
    'Append Where Clause
    strSQL = strSQL & vbCrLf & WhrStmt
    
    'Append Order by Clause
    strSQL = strSQL & vbCrLf & "ORDER BY Item.VnId, Item.Assort, Item.Item, Item.Lcid "
    
    'Return the query
    BldSQL = strSQL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BldSQL)", Err.Description
     f_HandleErr , , , "AIM_StockStatus:::BldSQL", Now, gDRGeneralError, True, Err
End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim Report As AIM_StockStatusReport
    Dim strSQL As String
    Dim strMessage As String
    Dim DemandOption As Integer
    Dim IndexCounter As Integer
    Dim rsStockStatus As ADODB.Recordset
    Dim ExecQuery As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    Dim BuyerID As String, VendorID As String, Assortment As String
    
    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
            Exit Sub
    
        Case "ID_Print", "ID_Preview"
            'Housekeeping
            Screen.MousePointer = vbHourglass
            strMessage = getTranslationResource("STATMSG05300")
            If StrComp(strMessage, "STATMSG05300") = 0 Then strMessage = "Building the Stock Status Report ... "
            Write_Message strMessage
            
            For IndexCounter = Me.ckDemandOption.LBound To Me.ckDemandOption.UBound
                If Me.ckDemandOption(IndexCounter).Value = True Then
                    DemandOption = ckDemandOption(IndexCounter).Tag
                    Exit For
                End If
            Next IndexCounter
    
            'Build SQL Statement
            BuyerID = Trim$(Me.dcAIMUsers.Text)
            VendorID = Trim$(Me.txtVnId.Text)
            Assortment = Trim$(Me.txtAssort.Text)
            strSQL = BldSQL(BuyerID, VendorID, Assortment, _
                Me.ckExceptions.Value, DemandOption, _
                Me.txtDays.Value, Me.txtUnits.Value)
            Set ExecQuery = New ADODB.Command
            With ExecQuery
                Set .ActiveConnection = Cn
                .CommandType = adCmdText
                .CommandText = strSQL
                If StrComp(BuyerID, getTranslationResource("All"), vbTextCompare) <> 0 Then
                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@ByID", adVarWChar, adParamInput, 12)
                    CmdParams.Value = BuyerID
                    .Parameters.Append CmdParams
                End If
                If VendorID <> COMP_EMPTY Then
                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@VnID", adVarWChar, adParamInput, 12)
                    CmdParams.Value = VendorID
                    .Parameters.Append CmdParams
                End If
                If Assortment <> COMP_EMPTY Then
                    Set CmdParams = Nothing
                    Set CmdParams = .CreateParameter("@Assort", adVarWChar, adParamInput, 12)
                    CmdParams.Value = Assortment
                    .Parameters.Append CmdParams
                End If
                Set CmdParams = Nothing
                .Prepared = True
            End With
            'Open the report result set
            Set rsStockStatus = New ADODB.Recordset
            With rsStockStatus
                .CursorType = adOpenStatic
                .LockType = adLockReadOnly
                'Fetch data
                .Open ExecQuery
            End With
            If f_IsRecordsetOpenAndPopulated(rsStockStatus) Then
                'Instantiate the Report
                Set Report = New AIM_StockStatusReport
                Set Report.dcItem.Recordset = rsStockStatus
                
                Select Case Tool.ID
                Case "ID_Print"
                    Report.PrintReport True
            
                Case "ID_Preview"
                    Set AIM_Reports.ARViewer.ReportSource = Report
                    AIM_Reports.Show vbModal, AIM_Main
            
                End Select
                'Destroy the objects
                Set Report = Nothing
                Set AIM_StockStatusReport = Nothing

                Write_Message ""
            Else
                strMessage = getTranslationResource("TEXTMSG05103")
                If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
                Write_Message strMessage
        End If
            
        'Wrap Up
        Set CmdParams = Nothing
        If Not (ExecQuery Is Nothing) Then Set ExecQuery.ActiveConnection = Nothing
        Set ExecQuery = Nothing
        
        If f_IsRecordsetValidAndOpen(rsStockStatus) Then rsStockStatus.Close
        Set rsStockStatus = Nothing
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AIM_StockStatus:::atPrintMenu_ToolClick", Now, gDRGeneralError, True, Err
    On Error Resume Next
    If Not Report Is Nothing Then
        Set Report = Nothing
        Set AIM_StockStatusReport = Nothing
    End If
    Set CmdParams = Nothing
    If Not (ExecQuery Is Nothing) Then Set ExecQuery.ActiveConnection = Nothing
    Set ExecQuery = Nothing
    If f_IsRecordsetValidAndOpen(rsStockStatus) Then rsStockStatus.Close
    Set rsStockStatus = Nothing
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "userid"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "userid"
    
    Me.dcAIMUsers.Columns(1).Name = "username"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = "username"

    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
     f_HandleErr , , , "AIM_StockStatus::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_StockStatus:::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim rsAIMUsers As ADODB.Recordset

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG05301")
    If StrComp(strMessage, "STATMSG05301") = 0 Then strMessage = "Initializing Stock Status Report... "
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Build/Bind the AIM User Drop Down
    Me.dcAIMUsers.Text = AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text
    
    strSQL = "SELECT UserID, UserName FROM AIMUsers ORDER BY UserID"
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Me.dcAIMUsers.AddItem getTranslationResource("All") _
                & vbTab & getTranslationResource("All Users")
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserId").Value & vbTab & rsAIMUsers("Username").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Make the spin buttons visible
    Me.txtUnits.Spin.Visible = 1
    Me.txtDays.Spin.Visible = 1
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_StockStatus:::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    SQLConnection Cn, CONNECTION_CLOSE, False
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_StockStatus:::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub
