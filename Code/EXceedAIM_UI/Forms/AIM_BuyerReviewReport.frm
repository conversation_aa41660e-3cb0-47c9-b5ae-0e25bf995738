VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_BuyerReviewReport 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Buyer Review Work Sheet"
   ClientHeight    =   2445
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   6090
   Icon            =   "AIM_BuyerReviewReport.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   2445
   ScaleWidth      =   6090
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars atPrintMenu 
      Left            =   90
      Top             =   2040
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Tools           =   "AIM_BuyerReviewReport.frx":030A
      ToolBars        =   "AIM_BuyerReviewReport.frx":35E1
   End
   Begin VB.Frame Frame2 
      Height          =   1980
      Left            =   30
      TabIndex        =   4
      Top             =   36
      Width           =   6030
      Begin VB.CheckBox ckPositiveSOQ 
         Caption         =   "Positive Suggested Order Quantity"
         Height          =   300
         Left            =   240
         TabIndex        =   3
         Top             =   1500
         Value           =   1  'Checked
         Width           =   5280
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcAIMUsers 
         Bindings        =   "AIM_BuyerReviewReport.frx":3733
         Height          =   345
         Left            =   2250
         TabIndex        =   0
         Top             =   195
         Width           =   1380
         DataFieldList   =   "Userid"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         ForeColorEven   =   0
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2434
         _ExtentY        =   600
         _StockProps     =   93
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "UserId"
      End
      Begin TDBText6Ctl.TDBText txtVnId 
         Height          =   345
         Left            =   2250
         TabIndex        =   1
         Top             =   570
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_BuyerReviewReport.frx":374C
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_BuyerReviewReport.frx":37B8
         Key             =   "AIM_BuyerReviewReport.frx":37D6
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtAssort 
         Height          =   345
         Left            =   2250
         TabIndex        =   2
         Top             =   960
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   600
         Caption         =   "AIM_BuyerReviewReport.frx":381A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_BuyerReviewReport.frx":3886
         Key             =   "AIM_BuyerReviewReport.frx":38A4
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   0
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   12
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Label label 
         Caption         =   "Vendor ID"
         Height          =   225
         Index           =   24
         Left            =   240
         TabIndex        =   7
         Top             =   615
         Width           =   1950
      End
      Begin VB.Label label 
         Caption         =   "Assortment"
         Height          =   225
         Index           =   25
         Left            =   240
         TabIndex        =   6
         Top             =   1005
         Width           =   1950
      End
      Begin VB.Label Label17 
         Caption         =   "Buyer ID"
         Height          =   225
         Left            =   240
         TabIndex        =   5
         Top             =   240
         Width           =   1950
      End
   End
End
Attribute VB_Name = "AIM_BuyerReviewReport"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Private Function BldSQL(ById As String, VnId As String, Assort As String, PositiveSOQ As Boolean)
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim strWhere As String
    
    strSQL = "SELECT PODetail.ById, PODetail.VnId, PODetail.Assort,"
    strSQL = strSQL & vbCrLf & "PODetail.Lcid, PODetail.Item, PODetail.OrdStatus,"
    strSQL = strSQL & vbCrLf & "PODetail.AvailQty,  Item.OrderPt, Item.OrderQty, Item.FcstDemand,"
    strSQL = strSQL & vbCrLf & "Item.UOM,  Item.ConvFactor, Item.BuyingUOM, PODetail.RSOQ,"
    strSQL = strSQL & vbCrLf & "PODetail.SOQ, PODetail.VSOQ, PODetail.IsDate,"
    strSQL = strSQL & vbCrLf & "PODetail.DuDate, Item.ItDesc, Item.Weight, Item.Cube,"
    strSQL = strSQL & vbCrLf & "Item.Cost"
    strSQL = strSQL & vbCrLf & "FROM PODetail"
    strSQL = strSQL & vbCrLf & "INNER JOIN Item ON PODetail.Lcid = Item.Lcid"
    strSQL = strSQL & vbCrLf & "AND PODetail.Item = Item.Item"
    
    'Build the Where Clause
    strWhere = ""
    
    If Trim(ById) <> getTranslationResource("All") Then
        strWhere = "WHERE PODetail.ById = N'" & ById & "'"
    End If
    
    If Trim(VnId) <> "" Then
        If Trim(strWhere) = "" Then
            strWhere = "WHERE PODetail.VnId = N'" & VnId & "'"
        Else
            strWhere = strWhere & vbCrLf & "AND PODetail.VnId = N'" & VnId & "'"
        End If
    End If
    
    If Trim(Assort) <> "" Then
        If strWhere = "" Then
            strWhere = "WHERE PODetail.Assort = N'" & Assort & "'"
        Else
            strWhere = strWhere & vbCrLf & "AND PODetail.Assort = N'" & Assort & "'"
        End If
    End If
    
    If PositiveSOQ Then
        If strWhere = "" Then
            strWhere = "WHERE PODetail.VSOQ > 0"
        Else
            strWhere = strWhere & vbCrLf & "AND PODetail.VSOQ > 0"
        End If
    End If
        
    'Append Where Clause
    strSQL = strSQL & vbCrLf & strWhere
    
    'Append Order by Clause
    strSQL = strSQL & vbCrLf & "ORDER BY PODetail.ById, PODetail.VnId, PODetail.Assort,"
    strSQL = strSQL & vbCrLf & "PODetail.LcId , PODetail.Item"
    
    BldSQL = strSQL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BldSQL)"
     f_HandleErr , , , "AimBuyerReviewReport::BldSQL", Now, gDRGeneralError, True, Err

End Function

Private Sub atPrintMenu_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim Report As AIM_BuyerReviewWorkSheet
    Dim strSQL As String
    Dim strMessage As String
    Dim rsReport As ADODB.Recordset
    
    Select Case Tool.ID
        Case "ID_Close"
            Unload Me
            Exit Sub
        
        Case "ID_Preview", "ID_Print"
            'Housekeeping
            Screen.MousePointer = vbHourglass
            strMessage = getTranslationResource("STATMSG01100")
            If StrComp(strMessage, "STATMSG01100") = 0 Then strMessage = "Building the Buyer Review Work Sheet..."
            Write_Message strMessage
        
            'Build SQL Statement
            strSQL = BldSQL(Me.dcAIMUsers.Text, Me.txtVnId.Text, Me.txtAssort.Text, Me.ckPositiveSOQ.Value)
            
            'Open the report result set
            Set Report = New AIM_BuyerReviewWorkSheet
            Set rsReport = New ADODB.Recordset
            rsReport.Open strSQL, Cn, adOpenStatic, adLockReadOnly
            
            Write_Message ""
            Screen.MousePointer = vbNormal
            
            If (f_IsRecordsetOpenAndPopulated(rsReport)) Then
                'Instantiate the Report
                Set Report.dcPODetail.Recordset = rsReport
                Set AIM_Reports.ARViewer.ReportSource = Report
                
                Select Case Tool.ID
                    Case "ID_Print"
                        Report.PrintReport True
                
                    Case "ID_Preview"
                        Set AIM_Reports.ARViewer.ReportSource = Report
                        AIM_Reports.Show vbModal, AIM_Main
            
                End Select
            Else
                strMessage = getTranslationResource("TEXTMSG05103")
                If StrComp(strMessage, "TEXTMSG05103") = 0 Then strMessage = "No records returned."
                Write_Message strMessage
            End If
            
            'Wrap Up
            If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
            Set rsReport = Nothing
            Set AIM_BuyerReviewWorkSheet = Nothing
            Set Report = Nothing
            
    End Select
    
Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsReport) Then rsReport.Close
    Set rsReport = Nothing
    Set Report = Nothing
    'f_HandleErr Me.Caption & "(atPrintMenu_ToolClick)"
     f_HandleErr , , , "AimBuyerReviewReport::atPrintMenu", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcAIMUsers_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dcAIMUsers.Columns(0).Name = "UserID"
    Me.dcAIMUsers.Columns(0).Caption = getTranslationResource("User ID")
    Me.dcAIMUsers.Columns(0).Width = 1000
    Me.dcAIMUsers.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(0).DataField = "UserID"
    
    Me.dcAIMUsers.Columns(1).Name = "UserName"
    Me.dcAIMUsers.Columns(1).Caption = getTranslationResource("Name")
    Me.dcAIMUsers.Columns(1).Width = 2880
    Me.dcAIMUsers.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcAIMUsers.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcAIMUsers.Columns(1).DataField = "UserName"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcAIMUsers, ACW_EXPAND
    End If

'    For IndexCounter = 0 To dcAIMUsers.Columns.Count - 1
'        dcAIMUsers.Columns(IndexCounter).HasHeadBackColor = True
'        dcAIMUsers.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcAIMUsers_InitColumnProps)"
     f_HandleErr , , , "AimBuyerReviewReport::dcAIMUsers_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AimBuyerReviewReport::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strSQL As String
    Dim strMessage As String
    Dim rsAIMUsers As ADODB.Recordset

    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG01101")
    If StrComp(strMessage, "STATMSG01101") = 0 Then strMessage = "Initializing Buyer Review Work Sheet..."
    Write_Message strMessage
    
    GetTranslatedCaptions Me
        
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub

    'Build/Bind the AIM User Drop Down
    Me.dcAIMUsers.Text = AIM_Main.tbAIM_Main.Tools("ID_Buyer").ComboBox.Text
    
    strSQL = "SELECT UserID, UserName FROM AIMUsers ORDER BY UserID"
    Set rsAIMUsers = New ADODB.Recordset
    rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockReadOnly
    
    Me.dcAIMUsers.AddItem getTranslationResource("All") & _
                        vbTab & getTranslationResource("All Users")
    
    If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
        Do Until rsAIMUsers.eof
            Me.dcAIMUsers.AddItem rsAIMUsers("UserId").Value & vbTab & rsAIMUsers("Username").Value
            rsAIMUsers.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    
    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AimBuyerReviewReport::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Unload)"
     f_HandleErr , , , "AimBuyerReviewReport::Form_Unload", Now, gDRGeneralError, True, Err
End Sub
