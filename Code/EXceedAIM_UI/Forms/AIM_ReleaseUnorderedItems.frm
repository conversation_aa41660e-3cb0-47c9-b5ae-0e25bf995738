VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ReleaseUnorderedItems 
   AutoRedraw      =   -1  'True
   Caption         =   "SSA DR Release Unordered Items"
   ClientHeight    =   9120
   ClientLeft      =   165
   ClientTop       =   435
   ClientWidth     =   15210
   Icon            =   "AIM_ReleaseUnorderedItems.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9120
   ScaleWidth      =   15210
   WindowState     =   2  'Maximized
   Begin VB.Frame fmVendorSummary 
      Caption         =   "Vendor Summary"
      Height          =   1850
      Left            =   4560
      TabIndex        =   13
      Top             =   30
      Width           =   10590
      Begin VB.Label txtVnDesc 
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   3165
         TabIndex        =   2
         Top             =   210
         Width           =   3660
      End
      Begin VB.Label label 
         Caption         =   "Vendor Minimum"
         Height          =   300
         Index           =   7
         Left            =   7380
         TabIndex        =   23
         Top             =   735
         Width           =   1905
      End
      Begin VB.Label txtVn_Min 
         Alignment       =   1  'Right Justify
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   9375
         TabIndex        =   9
         Top             =   690
         Width           =   1170
      End
      Begin VB.Label label 
         Caption         =   "Vendor Best"
         Height          =   300
         Index           =   8
         Left            =   7380
         TabIndex        =   22
         Top             =   1095
         Width           =   1905
      End
      Begin VB.Label txtVn_Best 
         Alignment       =   1  'Right Justify
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   9375
         TabIndex        =   10
         Top             =   1050
         Width           =   1170
      End
      Begin VB.Label label 
         Caption         =   "Reach Goal"
         Height          =   300
         Index           =   9
         Left            =   7380
         TabIndex        =   21
         Top             =   1455
         Width           =   1905
      End
      Begin VB.Label txtReach_Code 
         Alignment       =   2  'Center
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   9375
         TabIndex        =   11
         Top             =   1410
         Width           =   1170
      End
      Begin VB.Label txtExtUnits 
         Alignment       =   1  'Right Justify
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   5415
         TabIndex        =   6
         Top             =   690
         Width           =   1410
      End
      Begin VB.Label label 
         Caption         =   "Units"
         Height          =   300
         Index           =   3
         Left            =   3660
         TabIndex        =   20
         Top             =   735
         Width           =   1710
      End
      Begin VB.Label label 
         Caption         =   "Extended Cost"
         Height          =   270
         Index           =   4
         Left            =   120
         TabIndex        =   19
         Top             =   1455
         Width           =   1530
      End
      Begin VB.Label txtExtCost 
         Alignment       =   1  'Right Justify
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   1710
         TabIndex        =   5
         Top             =   1440
         Width           =   1410
      End
      Begin VB.Label label 
         Caption         =   "Weight"
         Height          =   300
         Index           =   5
         Left            =   3660
         TabIndex        =   18
         Top             =   1095
         Width           =   1710
      End
      Begin VB.Label txtExtWeight 
         Alignment       =   1  'Right Justify
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   5415
         TabIndex        =   7
         Top             =   1050
         Width           =   1410
      End
      Begin VB.Label label 
         Caption         =   "Cube"
         Height          =   270
         Index           =   6
         Left            =   3660
         TabIndex        =   17
         Top             =   1485
         Width           =   1710
      End
      Begin VB.Label txtExtCube 
         Alignment       =   1  'Right Justify
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   5415
         TabIndex        =   8
         Top             =   1410
         Width           =   1410
      End
      Begin VB.Label label 
         Caption         =   "Assortment"
         Height          =   300
         Index           =   1
         Left            =   120
         TabIndex        =   15
         Top             =   735
         Width           =   1530
      End
      Begin VB.Label txtAssort 
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   1710
         TabIndex        =   3
         Top             =   690
         Width           =   1410
      End
      Begin VB.Label label 
         Caption         =   "Vendor"
         Height          =   300
         Index           =   0
         Left            =   120
         TabIndex        =   14
         Top             =   250
         Width           =   1530
      End
      Begin VB.Label txtVnId 
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   340
         Left            =   1710
         TabIndex        =   1
         Top             =   210
         Width           =   1410
      End
      Begin VB.Label label 
         Caption         =   "Buyer"
         Height          =   270
         Index           =   2
         Left            =   120
         TabIndex        =   16
         Top             =   1125
         Width           =   1530
      End
      Begin VB.Label txtById 
         BackColor       =   &H00FFFFFF&
         BorderStyle     =   1  'Fixed Single
         Height          =   345
         Left            =   1710
         TabIndex        =   4
         Top             =   1050
         Width           =   1410
      End
   End
   Begin ActiveToolBars.SSActiveToolBars tbNavigation 
      Left            =   120
      Top             =   8760
      _ExtentX        =   609
      _ExtentY        =   609
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   4
      Style           =   0
      Tools           =   "AIM_ReleaseUnorderedItems.frx":030A
      ToolBars        =   "AIM_ReleaseUnorderedItems.frx":3637
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgVendors 
      Height          =   8625
      Left            =   30
      TabIndex        =   0
      Top             =   30
      Width           =   4500
      _Version        =   196617
      RecordSelectors =   0   'False
      AllowUpdate     =   0   'False
      AllowColumnSwapping=   0
      SelectTypeCol   =   0
      SelectTypeRow   =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   7937
      _ExtentY        =   15214
      _StockProps     =   79
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgVendorDetail 
      Height          =   6705
      Left            =   4560
      TabIndex        =   12
      Top             =   1950
      Width           =   10590
      _Version        =   196617
      DataMode        =   1
      AllowColumnSwapping=   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   18680
      _ExtentY        =   11827
      _StockProps     =   79
      Caption         =   "Unordered Items"
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "-"
         Index           =   2
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "Pack Size &Enabled"
         Checked         =   -1  'True
         Index           =   3
      End
   End
End
Attribute VB_Name = "AIM_ReleaseUnorderedItems"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Type UnorderedItemsRcd
    ItStat As String
    Item As String
    ItDesc As String
    LcId As String
    VSOQ As Long
    UOM As String
    ConvFactor As Double
    IMin As Long
    IMax As Long
    Weight As Double
    Cube As Double
    Cost As Double
    BuyingUOM As String
    PackRounding As String
End Type

Dim Cn As ADODB.Connection

Dim AIM_UnorderedItems_Sp As ADODB.Command
Dim AIM_UnorderedVendors_Sp As ADODB.Command

Dim rsVendorList As ADODB.Recordset
Dim rsUnorderedItems As ADODB.Recordset

Dim ById As String
Dim VnId As String
Dim Assort As String

Dim NbrItems As Long

Dim UnorderedItems() As UnorderedItemsRcd

Private Function CalcTotals()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim ExtUnits As Long
    Dim ExtCost As Double
    Dim ExtWeight As Double
    Dim ExtCube As Double
    Dim VSOQ As Double
    
    For IndexCounter = LBound(UnorderedItems) To UBound(UnorderedItems)
        If UnorderedItems(IndexCounter).ItStat = "N" Then
            VSOQ = CDbl(UnorderedItems(IndexCounter).VSOQ)
            ExtUnits = ExtUnits + VSOQ
            ExtCost = ExtCost + (VSOQ * UnorderedItems(IndexCounter).Cost)
            ExtWeight = ExtWeight + (VSOQ * UnorderedItems(IndexCounter).Weight)
            ExtCube = ExtCube + (VSOQ * UnorderedItems(IndexCounter).Cube)
        End If
    Next IndexCounter

    'Update the display
    Me.txtExtUnits.Caption = Format(ExtUnits, "#,##0")
    Me.txtExtCost.Caption = Format(ExtCost, "#,##0.00")
    Me.txtExtWeight.Caption = Format(ExtWeight, "#,##0.00")
    Me.txtExtCube.Caption = Format(ExtCube, "#,##0.00")

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(CalcTotals)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::CalcTotals", Now, gDRGeneralError, True, Err
End Function

Private Function SetVendorData()
On Error GoTo ErrorHandler

    'Set the Module variables
    VnId = rsVendorList("VnId").Value
    Assort = rsVendorList("Assort").Value
    
    'Set the display
    Me.txtVnId.Caption = VnId
    Me.txtAssort.Caption = Assort
    Me.txtVnDesc.Caption = rsVendorList("VName").Value
    Me.txtById.Caption = ById
    Me.txtVn_Min.Caption = Format(rsVendorList("Vn_Min").Value, "#,##0.00")
    Me.txtVn_Best.Caption = Format(rsVendorList("Vn_Best").Value, "#,##0.00")
    Me.txtReach_Code.Caption = GetReachCodeDesc(rsVendorList("Reach_Code").Value)
    
    'Update the Unordered Item Grid
    With AIM_UnorderedItems_Sp
    
        .Parameters("@ById").Value = ById
        .Parameters("@VnId").Value = VnId
        .Parameters("@Assort").Value = Assort
        
    End With
    
    If f_IsRecordsetValidAndOpen(rsUnorderedItems) Then
        rsUnorderedItems.Requery
    Else
        rsUnorderedItems.Open AIM_UnorderedItems_Sp
    End If
    
    'Load the data into the array
    ReDim UnorderedItems(100)
    NbrItems = 0
    If f_IsRecordsetOpenAndPopulated(rsUnorderedItems) Then
        Do Until rsUnorderedItems.eof
            UnorderedItems(NbrItems).ItStat = rsUnorderedItems("ItStat").Value
            UnorderedItems(NbrItems).Item = rsUnorderedItems("Item").Value
            UnorderedItems(NbrItems).ItDesc = rsUnorderedItems("ItDesc").Value
            UnorderedItems(NbrItems).LcId = rsUnorderedItems("LcId").Value
            UnorderedItems(NbrItems).VSOQ = rsUnorderedItems("VSOQ").Value
            UnorderedItems(NbrItems).UOM = rsUnorderedItems("UOM").Value
            UnorderedItems(NbrItems).ConvFactor = rsUnorderedItems("ConvFactor").Value
            UnorderedItems(NbrItems).IMin = rsUnorderedItems("IMin").Value
            UnorderedItems(NbrItems).IMax = rsUnorderedItems("IMax").Value
            UnorderedItems(NbrItems).Weight = rsUnorderedItems("Weight").Value
            UnorderedItems(NbrItems).Cube = rsUnorderedItems("Cube").Value
            UnorderedItems(NbrItems).Cost = rsUnorderedItems("Cost").Value
            UnorderedItems(NbrItems).BuyingUOM = rsUnorderedItems("BuyingUOM").Value
            UnorderedItems(NbrItems).PackRounding = rsUnorderedItems("PackRounding").Value
            
            'Increment the row counter
            NbrItems = NbrItems + 1
            
            If NbrItems > UBound(UnorderedItems) Then
                ReDim Preserve UnorderedItems(UBound(UnorderedItems) + 100)
            End If
            
            rsUnorderedItems.MoveNext
        
        Loop
    End If
    
    'Truncate the array
    If NbrItems > 0 Then
        ReDim Preserve UnorderedItems(NbrItems - 1)
    End If
    
    'Display the totals
    CalcTotals
    
    'Rebind the Grid
    Me.dgVendorDetail.ReBind

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetVendorData)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::SetVendorData", Now, gDRGeneralError, True, Err
End Function

'***sujit10102003   added function for validation vsoq column
Private Sub dgVendorDetail_Change()
    Dim strMessage As String
    Dim RtnCode As Integer
    If Not Trim(dgVendorDetail.Columns(4).Text) = "" Then
        If IsNumeric(dgVendorDetail.Columns(4).Text) Then
            If CDbl(dgVendorDetail.Columns(4).Text) > 32000 Then
                strMessage = getTranslationResource("MSGBOX04303")
                If StrComp(strMessage, "MSGBOX04303") = 0 Then strMessage = "Invalid data entry.Entered number value should not be more than 32000."
                RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
                dgVendorDetail.Columns(4).Text = "32000"
            End If
        Else
            dgVendorDetail.Columns(4).Text = "0"
        End If
    Else
        dgVendorDetail.Columns(4).Text = "0"
    End If
End Sub

'***sujit10102003   added function for validation vsoq column
Private Sub dgVendorDetail_KeyDown(KeyCode As Integer, Shift As Integer)
    Dim strMessage As String
    Dim RtnCode As Integer
    If Not Trim(dgVendorDetail.Columns(4).Text) = "" Then
        If IsNumeric(dgVendorDetail.Columns(4).Text) Then
            If CDbl(dgVendorDetail.Columns(4).Text) > 32000 Then
                strMessage = getTranslationResource("MSGBOX04303")
                If StrComp(strMessage, "MSGBOX04303") = 0 Then strMessage = "Invalid data entry.Entered number value should not be more than 32000."
                RtnCode = MsgBox(strMessage, vbExclamation, Me.Caption)
    
                'MsgBox "Invalid data entry.Entered number value should not be more than 32000.", vbExclamation
                'dgVendorDetail.Columns(4).Text = Left(dgVendorDetail.Columns(4).Text, Len(Trim(dgVendorDetail.Columns(4).Text)) - 1)
                dgVendorDetail.Columns(4).Text = "32000"
            End If
        Else
            dgVendorDetail.Columns(4).Text = "0"
        End If
    Else
        dgVendorDetail.Columns(4).Text = "0"
    End If
End Sub

Private Sub tbNavigation_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    If Tool.ID = "ID_Close" Then
        Unload Me
        Exit Sub
    End If
        
    Me.MousePointer = vbHourglass
    
    Select Case Tool.ID
    Case "ID_ReleaseAll"
        For IndexCounter = LBound(UnorderedItems) To UBound(UnorderedItems)
            UnorderedItems(IndexCounter).ItStat = "N"
        Next IndexCounter
        
        'Update Totals
        CalcTotals
        
    Case "ID_UndoReleaseAll"
        For IndexCounter = LBound(UnorderedItems) To UBound(UnorderedItems)
            UnorderedItems(IndexCounter).ItStat = "U"
        Next IndexCounter
        
        'Update Totals
        CalcTotals
        
    Case "ID_Update"
        UpdateUnorderedItems
        
    End Select
    
    'Rebind the Grid
    Me.dgVendorDetail.ReBind
    
    Write_Message ""
    Me.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    If Err.Number = 9 Then
        'Subscript out of range
        Resume Next
    Else
        Me.MousePointer = vbNormal
        'f_HandleErr Me.Caption & "(tbNavigation_ToolClick)"
         f_HandleErr , , , "AIM_ReleaseUnorderedItems::tbNavigation_ToolClick", Now, gDRGeneralError, True, Err
    End If
End Sub

Private Sub dgVendorDetail_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    If Not f_IsRecordsetOpenAndPopulated(rsUnorderedItems) Then Exit Sub
    
    'Define Columns
    Me.dgVendorDetail.Columns(0).Name = "itstat"
    Me.dgVendorDetail.Columns(0).Caption = "Rel"
    Me.dgVendorDetail.Columns(0).Width = 400
    Me.dgVendorDetail.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgVendorDetail.Columns(0).Style = ssStyleCheckBox
    Me.dgVendorDetail.Columns(0).Locked = False

    Me.dgVendorDetail.Columns(1).Name = "item"
    Me.dgVendorDetail.Columns(1).Caption = "Item"
    Me.dgVendorDetail.Columns(1).Width = 1200
    Me.dgVendorDetail.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgVendorDetail.Columns(1).Locked = True

    Me.dgVendorDetail.Columns(2).Name = "lcid"
    Me.dgVendorDetail.Columns(2).Caption = "Location"
    Me.dgVendorDetail.Columns(2).Width = 800
    Me.dgVendorDetail.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgVendorDetail.Columns(2).Locked = True

    Me.dgVendorDetail.Columns(3).Name = "itdesc"
    Me.dgVendorDetail.Columns(3).Caption = "Description"
    Me.dgVendorDetail.Columns(3).Width = 2880
    Me.dgVendorDetail.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgVendorDetail.Columns(3).Locked = True

    Me.dgVendorDetail.Columns(4).Name = "vsoq"
    Me.dgVendorDetail.Columns(4).Caption = "VSOQ"
    Me.dgVendorDetail.Columns(4).Width = 800
    Me.dgVendorDetail.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(4).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(4).NumberFormat = "0"
    Me.dgVendorDetail.Columns(4).DataType = vbLong
    Me.dgVendorDetail.Columns(4).Locked = False
    'Me.dgVendorDetail.Columns(4).Selected = True

    Me.dgVendorDetail.Columns(5).Name = "uom"
    Me.dgVendorDetail.Columns(5).Caption = "UOM"
    Me.dgVendorDetail.Columns(5).Width = 600
    Me.dgVendorDetail.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(5).Alignment = ssCaptionAlignmentLeft
    Me.dgVendorDetail.Columns(5).Locked = True

    Me.dgVendorDetail.Columns(6).Name = "convfactor"
    Me.dgVendorDetail.Columns(6).Caption = "Pack Size"
    Me.dgVendorDetail.Columns(6).Width = 800
    Me.dgVendorDetail.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(6).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(6).NumberFormat = "0"
    Me.dgVendorDetail.Columns(6).Locked = True
    
    Me.dgVendorDetail.Columns(7).Name = "imin"
    Me.dgVendorDetail.Columns(7).Caption = "Min Ord Qty"
    Me.dgVendorDetail.Columns(7).Width = 800
    Me.dgVendorDetail.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(7).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(7).NumberFormat = "0"
    Me.dgVendorDetail.Columns(7).Locked = True
    
    Me.dgVendorDetail.Columns(8).Name = "imax"
    Me.dgVendorDetail.Columns(8).Caption = "Max Ord Qty"
    Me.dgVendorDetail.Columns(8).Width = 1000
    Me.dgVendorDetail.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(8).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(8).NumberFormat = "#,##0"
    Me.dgVendorDetail.Columns(8).Locked = True
    
    Me.dgVendorDetail.Columns(9).Name = "weight"
    Me.dgVendorDetail.Columns(9).Caption = "Weight"
    Me.dgVendorDetail.Columns(9).Width = 800
    Me.dgVendorDetail.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(9).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(9).NumberFormat = "#,##0.00"
    Me.dgVendorDetail.Columns(9).Locked = True
    
    Me.dgVendorDetail.Columns(10).Name = "cube"
    Me.dgVendorDetail.Columns(10).Caption = "Cube"
    Me.dgVendorDetail.Columns(10).Width = 800
    Me.dgVendorDetail.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(10).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(10).NumberFormat = "#,##0.00"
    Me.dgVendorDetail.Columns(10).Locked = True
    
    Me.dgVendorDetail.Columns(11).Name = "cost"
    Me.dgVendorDetail.Columns(11).Caption = "Cost"
    Me.dgVendorDetail.Columns(11).Width = 800
    Me.dgVendorDetail.Columns(11).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgVendorDetail.Columns(11).Alignment = ssCaptionAlignmentRight
    Me.dgVendorDetail.Columns(11).NumberFormat = "#,##0.00"
    Me.dgVendorDetail.Columns(11).Locked = True
    
    'Restore the grid layout
    On Error Resume Next 'So that first time this is run it will not rise an error
    Me.dgVendorDetail.LoadLayout App.Path & "\" & "AIM_ReleaseUnorderedItems" & Trim(gUserID) & "_Layout.grd"
    'Check for non-existent file
    If Err.Number = 53 Then     'File Not Found
        Resume Next
    ElseIf Err.Number <> 0 Then
        GoTo ErrorHandler
    End If
    On Error GoTo ErrorHandler
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgVendorDetail.Font
    SetProperFont Me.dgVendorDetail.HeadFont
    SetProperFont Me.dgVendorDetail.PageFooterFont
    SetProperFont Me.dgVendorDetail.PageHeaderFont
    Me.dgVendorDetail.Caption = getTranslationResource("Unordered Items")
    Me.dgVendorDetail.Columns(0).Caption = getTranslationResource("Rel")
    Me.dgVendorDetail.Columns(1).Caption = getTranslationResource("Item")
    Me.dgVendorDetail.Columns(2).Caption = getTranslationResource("Location")
    Me.dgVendorDetail.Columns(3).Caption = getTranslationResource("Description")
    Me.dgVendorDetail.Columns(4).Caption = getTranslationResource("VSOQ")
    Me.dgVendorDetail.Columns(5).Caption = getTranslationResource("UOM")
    Me.dgVendorDetail.Columns(6).Caption = getTranslationResource("Pack Size")
    Me.dgVendorDetail.Columns(7).Caption = getTranslationResource("Min Ord Qty")
    Me.dgVendorDetail.Columns(8).Caption = getTranslationResource("Max Ord Qty")
    Me.dgVendorDetail.Columns(9).Caption = getTranslationResource("Weight")
    Me.dgVendorDetail.Columns(10).Caption = getTranslationResource("Cube")
    Me.dgVendorDetail.Columns(11).Caption = getTranslationResource("Cost")
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgVendorDetail, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgVendorDetail.Columns.Count - 1
'        dgVendorDetail.Columns(IndexCounter).HasHeadBackColor = True
'        dgVendorDetail.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgVendorDetail.Columns(IndexCounter).Locked = False Then dgVendorDetail.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendorDetail_InitColumnProps)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendorDetail_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendorDetail_MouseDown)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendorDetail_PrintBegin(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler

    ssPrintInfo.Portrait = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendorDetail_PrintBegin)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_PrintBegin", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendorDetail_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG04300")
    If StrComp(strMessage, "RPTMSG04300") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG04301")
    If StrComp(strMessage1, "RPTMSG04301") = 0 Then strMessage = "Unordered Items --"
    strMessage2 = getTranslationResource("RPTMSG04302")
    If StrComp(strMessage2, "RPTMSG04302") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & strMessage1 & Trim(VnId) & "/" & Trim(Assort) _
                & vbTab & strMessage2 & " <page number>"
    
    ssPrintInfo.Portrait = False
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendorDetail_PrintInitialize)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendorDetail_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        StartLocation = 0
    End If

    NewLocation = CLng(StartLocation) + NumberOfRowsToMove

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendorDetail_UnboundPositionData)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendorDetail_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim p As Integer
    Dim r As Integer
    
    If IsNull(StartLocation) Then       'If the grid is empty then
        If ReadPriorRows Then           'If moving backwards through grid then
            p = NbrItems                'pointer = # of last grid row
        Else                            'else
            p = 0                       'pointer = # of first grid row
        End If
        
    Else                                'If the grid already has data in it then
        p = StartLocation               'pointer = location just before or after the row where data will be added
        If ReadPriorRows Then           'If moving backwards through grid then
            p = p - 1                   'move pointer back one row
        Else                            'else
            p = p + 1                   'move pointer ahead one row
        End If

    End If

    'The pointer (p) now points to the row of the grid where you will start adding data.
    For i = 0 To RowBuf.RowCount - 1                    'For each row in the row buffer
        If p < 0 Or p > NbrItems - 1 Then Exit For      'If the pointer is outside the grid then stop this

        'For each column in the grid
        'Set the value of each column in the row buffer
        'to the corresponding value in the arrray
        
        RowBuf.Value(i, 0) = IIf(UnorderedItems(p).ItStat = "U", False, True)
        RowBuf.Value(i, 1) = UnorderedItems(p).Item
        RowBuf.Value(i, 2) = UnorderedItems(p).LcId
        RowBuf.Value(i, 3) = UnorderedItems(p).ItDesc
        RowBuf.Value(i, 4) = UnorderedItems(p).VSOQ
        RowBuf.Value(i, 5) = UnorderedItems(p).UOM
        RowBuf.Value(i, 6) = UnorderedItems(p).ConvFactor
        RowBuf.Value(i, 7) = UnorderedItems(p).IMin
        RowBuf.Value(i, 8) = UnorderedItems(p).IMax
        RowBuf.Value(i, 9) = UnorderedItems(p).Weight
        RowBuf.Value(i, 10) = UnorderedItems(p).Cube
        RowBuf.Value(i, 11) = UnorderedItems(p).Cost
        
        RowBuf.Bookmark(i) = p                          'set the value of the bookmark for the current row in the rowbuffer

        If ReadPriorRows Then                           'move the pointer forward or backward, depending
            p = p - 1                                   'on which way it's supposed to move
        Else
            p = p + 1
        End If

        r = r + 1                                       'increment the number of rows read
    
    Next i

    RowBuf.RowCount = r                                 'set the size of the row buffer to the number of rows read

Exit Sub
ErrorHandler:
    If Err.Number = 30403 Then
        'Rowbuf.ColumnCount does not match recordset.fields.count!! To be researched.
        Resume Next
    Else
        'f_HandleErr Me.Caption & "(dgVendorDetail_UnboundReadData)"
         f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_UnboundReadData", Now, gDRGeneralError, True, Err
    End If
    
End Sub

Private Sub dgVendorDetail_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim BuyingUOM As String
    Dim ConvFactor As Double
    Dim IMin As Long
    Dim IMax As Long
    Dim p As Long
    Dim PackRounding As String
    Dim SOQ As Double
    Dim UOM As String
    Dim VSOQ As Long
    
    'Position to updated record
    p = WriteLocation

    'Update the Item Status
    If Not IsNull(RowBuf.Value(0, 0)) Then
        If RowBuf.Value(0, 0) Then
            UnorderedItems(p).ItStat = "N"
        Else
            UnorderedItems(p).ItStat = "U"
        End If
        
    End If
        
    'Update the VSOQ
    If Not IsNull(RowBuf.Value(0, 4)) Then
        If mnuEditOpt.Item(3).Checked Then
            
            'Initialize Variables
            BuyingUOM = UnorderedItems(p).BuyingUOM
            ConvFactor = UnorderedItems(p).ConvFactor
            IMin = UnorderedItems(p).IMin
            IMax = UnorderedItems(p).IMax
            PackRounding = UnorderedItems(p).PackRounding
            SOQ = RowBuf.Value(0, 4)
            UOM = UnorderedItems(p).UOM
            
            VSOQ = PackRound(SOQ, UOM, ConvFactor, BuyingUOM, PackRounding, IMin, IMax)
        
            UnorderedItems(p).VSOQ = VSOQ
            
        Else
            UnorderedItems(p).VSOQ = RowBuf.Value(0, 4)
        End If
        
    End If
    
    'Update totals
    CalcTotals
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendorDetail_UnboundWriteData)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendorDetail_UnboundWriteData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendors_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgVendors.Columns(0).Caption = getTranslationResource("Vendor")
    Me.dgVendors.Columns(0).DataField = "Vnid"
    Me.dgVendors.Columns(0).Width = 800
    
    Me.dgVendors.Columns(1).Caption = getTranslationResource("Assort")
    Me.dgVendors.Columns(1).DataField = "Assort"
    Me.dgVendors.Columns(1).Width = 800
    
    Me.dgVendors.Columns(2).Caption = getTranslationResource("Description")
    Me.dgVendors.Columns(2).DataField = "VName"
    Me.dgVendors.Columns(2).Width = 2880
    
    Me.dgVendors.Columns(3).Caption = getTranslationResource("Vendor Minimum")
    Me.dgVendors.Columns(3).DataField = "Vn_Min"
    Me.dgVendors.Columns(3).Width = 800
    
    Me.dgVendors.Columns(4).Caption = getTranslationResource("Vendor Best")
    Me.dgVendors.Columns(4).DataField = "Vn_Best"
    Me.dgVendors.Columns(4).Width = 800
    
    Me.dgVendors.Columns(5).Caption = getTranslationResource("Reach Code")
    Me.dgVendors.Columns(5).DataField = "Reach_Code"
    Me.dgVendors.Columns(5).Width = 800
    
    Me.dgVendors.Columns(6).Caption = getTranslationResource("Review Cycle")
    Me.dgVendors.Columns(6).DataField = "RevCycle"
    Me.dgVendors.Columns(6).Width = 800
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgVendors, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgVendors.Columns.Count - 1
'        dgVendors.Columns(IndexCounter).HasHeadBackColor = True
'        dgVendors.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgVendors.Columns(IndexCounter).Locked = False Then dgVendors.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_InitColumnProps)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendors_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgVendors_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler
    
    If Me.dgVendors.Row >= 0 Then
        rsVendorList.Bookmark = Me.dgVendors.Bookmark
    
        VnId = Me.dgVendors.Columns("VnId").Value
        Assort = Me.dgVendors.Columns("Assort").Value
        
        'Redisplay the form
        SetVendorData
    
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgVendors_RowColChange)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::dgVendors_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    RemoveSemicolons
    
    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG04301")
    If StrComp(strMessage, "STATMSG04301") = 0 Then strMessage = "Initializing Release Unordered Items ..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Get the active Buyer Id
    ById = GetCurrentById
    
    'Initialize the Unordered Item Recordset
    Set AIM_UnorderedItems_Sp = New ADODB.Command
    With AIM_UnorderedItems_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_UnorderedItems_Sp"
        .Parameters.Refresh
    End With
    
    Set rsUnorderedItems = New ADODB.Recordset
    With rsUnorderedItems
        .CursorLocation = adUseServer
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    'Set up the Vendor Grid
    Set AIM_UnorderedVendors_Sp = New ADODB.Command
    With AIM_UnorderedVendors_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_UnorderedVendors_Sp"
        .Parameters.Refresh
    End With
    
    Set rsVendorList = New ADODB.Recordset
    With rsVendorList
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    AIM_UnorderedVendors_Sp.Parameters("@ById").Value = ById
    rsVendorList.Open AIM_UnorderedVendors_Sp
    
    If f_IsRecordsetOpenAndPopulated(rsVendorList) Then
        Set Me.dgVendors.DataSource = rsVendorList
        SetVendorData
        
        tbNavigation.Tools("ID_ReleaseAll").Enabled = True
        tbNavigation.Tools("ID_UndoReleaseAll").Enabled = True
        tbNavigation.Tools("ID_Update").Enabled = True
    Else
        tbNavigation.Tools("ID_ReleaseAll").Enabled = False
        tbNavigation.Tools("ID_UndoReleaseAll").Enabled = False
        tbNavigation.Tools("ID_Update").Enabled = False
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption

    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Resize()
On Error GoTo ErrorHandler

    SizeForm
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Resize)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::Form_Resize", Now, gDRGeneralError, True, Err
End Sub

Private Function SizeForm()
On Error GoTo ErrorHandler
    
    'Resize form
    If Me.WindowState <> vbMinimized Then
    
        'Position and Size Vendors List
        Me.dgVendors.Top = 50
        Me.dgVendors.Left = 50
        Me.dgVendors.Height = Me.ScaleHeight - 100
        
        'Position and Size Vendor Summary Panel
        Me.fmVendorSummary.Top = 50
        Me.fmVendorSummary.Left = Me.dgVendors.Width + 100
        Me.fmVendorSummary.Width = Me.ScaleWidth - (Me.dgVendors.Width + 100)
        
        'Position and Size Vendor Detail Grid
        Me.dgVendorDetail.Top = Me.fmVendorSummary.Height + 100
        Me.dgVendorDetail.Left = Me.dgVendors.Width + 100
        Me.dgVendorDetail.Width = Me.ScaleWidth - (Me.dgVendors.Width + 100)
        Me.dgVendorDetail.Height = Me.ScaleHeight - (Me.fmVendorSummary.Height + 200)
        
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SizeForm)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::SizeForm", Now, gDRGeneralError, True, Err
End Function

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    
    'Save current grid layout.
    If f_IsRecordsetOpenAndPopulated(rsUnorderedItems) Then
        Me.dgVendorDetail.SaveLayout App.Path & "\" & "AIM_ReleaseUnorderedItems" & Trim(gUserID) & "_Layout.grd", ssSaveLayoutAll
    End If
    
    'Destroy all objects
    If f_IsRecordsetValidAndOpen(rsVendorList) Then rsVendorList.Close
    Set rsVendorList = Nothing
    
    If f_IsRecordsetValidAndOpen(rsUnorderedItems) Then rsUnorderedItems.Close
    Set rsUnorderedItems = Nothing
    
    If Not (AIM_UnorderedItems_Sp Is Nothing) Then Set AIM_UnorderedItems_Sp.ActiveConnection = Nothing
    Set AIM_UnorderedItems_Sp = Nothing
    
    If Not (AIM_UnorderedVendors_Sp Is Nothing) Then Set AIM_UnorderedVendors_Sp.ActiveConnection = Nothing
    Set AIM_UnorderedVendors_Sp = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_ReleaseUnorderedItems::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim s1 As Variant
    Dim s2 As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
    Case 0          'Copy to Clipboard
        Clipboard.Clear
        
        For IndexCounter = 0 To rsUnorderedItems.Fields.Count - 1
            s1 = s1 & rsUnorderedItems.Fields(IndexCounter).Name & vbTab
        Next IndexCounter
        
        s1 = s1 & vbCrLf
            
        For IndexCounter = LBound(UnorderedItems) To UBound(UnorderedItems)
            s2 = s2 & UnorderedItems(IndexCounter).ItStat & vbTab
            s2 = s2 & UnorderedItems(IndexCounter).Item & vbTab
            s2 = s2 & UnorderedItems(IndexCounter).LcId & vbTab
            s2 = s2 & UnorderedItems(IndexCounter).ItDesc & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).VSOQ, "#,##0.00") & vbTab
            s2 = s2 & UnorderedItems(IndexCounter).UOM & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).ConvFactor, "#,##0.") & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).IMin, "#,##0") & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).IMax, "#.##0") & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).Weight, "#,##0.00") & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).Cube, "#,##0.00") & vbTab
            s2 = s2 & Format(UnorderedItems(IndexCounter).Cost, "#,##0.00") & vbTab
            s2 = s2 & UnorderedItems(IndexCounter).BuyingUOM & vbTab
            s2 = s2 & UnorderedItems(IndexCounter).PackRounding & vbTab
            s2 = s2 & vbCrLf
        Next IndexCounter
        
        Clipboard.SetText s1 & s2, vbCFText
        
    Case 1          'Print
        Me.dgVendorDetail.PrintData ssPrintAllRows, False, True
        
    Case 2          '--
    
    Case 3          'Packsizing Enabled
        Me.mnuEditOpt(3).Checked = Not Me.mnuEditOpt(3).Checked
    
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Function RemoveSemicolons()
'Remove semi colons from static text
On Error GoTo ErrorHandler
    Dim LastChar As Long
    
    LastChar = InStr(1, txtAssort.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtAssort.Caption = Mid(txtAssort, 1, LastChar - 1)
    
    LastChar = InStr(1, txtById.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtById.Caption = Mid(txtById, 1, LastChar - 1)
    
    LastChar = InStr(1, txtExtCost.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtExtCost.Caption = Mid(txtExtCost, 1, LastChar - 1)
    
    LastChar = InStr(1, txtExtCube.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtExtCube.Caption = Mid(txtExtCube, 1, LastChar - 1)
    
    LastChar = InStr(1, txtExtUnits.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtExtUnits.Caption = Mid(txtExtUnits, 1, LastChar - 1)
    
    LastChar = InStr(1, txtExtWeight.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtExtWeight.Caption = Mid(txtExtWeight, 1, LastChar - 1)
    
    LastChar = InStr(1, txtReach_Code.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtReach_Code.Caption = Mid(txtReach_Code, 1, LastChar - 1)
    
    LastChar = InStr(1, txtVn_Best.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtVn_Best.Caption = Mid(txtVn_Best, 1, LastChar - 1)
    
    LastChar = InStr(1, txtVn_Min.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtVn_Min.Caption = Mid(txtVn_Min, 1, LastChar - 1)
    
    LastChar = InStr(1, txtVnDesc.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtVnDesc.Caption = Mid(txtVnDesc, 1, LastChar - 1)
    
    LastChar = InStr(1, txtVnId.Caption, ":", vbTextCompare)
    If LastChar > 0 Then txtVnId.Caption = Mid(txtVnId, 1, LastChar - 1)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::RemoveSemicolons", Now, gDRGeneralError, True, Err
End Function

Private Function UpdateUnorderedItems()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    Dim strMessage As String
    Dim AIM_InitNewItem_Sp As ADODB.Command
    Dim AIM_InitPOHeader_Sp As ADODB.Command

    strMessage = getTranslationResource("STATMSG04300")
    If StrComp(strMessage, "STATMSG04300") = 0 Then strMessage = "Updating Item Status Code/Creating New PO Detail Line(s)... "
    Write_Message strMessage
    
    Me.dgVendorDetail.Update
    
    'Initialize the Initialize New Item SP
    Set AIM_InitNewItem_Sp = New ADODB.Command
    With AIM_InitNewItem_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_InitNewItem_Sp"
        .Parameters.Refresh
    End With
    
    For IndexCounter = LBound(UnorderedItems) To UBound(UnorderedItems)
        If UnorderedItems(IndexCounter).ItStat = "N" Then
            With AIM_InitNewItem_Sp
                .Parameters("@ProcessOpt").Value = IIf(UnorderedItems(IndexCounter).VSOQ > 0, 1, 0)
                .Parameters("@ById").Value = ById
                .Parameters("@VnId").Value = VnId
                .Parameters("@Assort").Value = Assort
                .Parameters("@Lcid").Value = UnorderedItems(IndexCounter).LcId
                .Parameters("@Item").Value = UnorderedItems(IndexCounter).Item
                .Parameters("@VSOQ").Value = UnorderedItems(IndexCounter).VSOQ
                
                .Execute
            End With
        End If
    Next IndexCounter
    
    'Create a Purchase Order Header
    Set AIM_InitPOHeader_Sp = New ADODB.Command
    With AIM_InitPOHeader_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_InitPOHeader_Sp"
        .Parameters.Refresh
    End With

    With AIM_InitPOHeader_Sp
        .Parameters("@ById") = ById
        .Parameters("@VnId") = VnId
        .Parameters("@Assort") = Assort
        
        .Execute , , adExecuteNoRecords
    End With
    
    SetVendorData
    
    If Not (AIM_InitNewItem_Sp Is Nothing) Then Set AIM_InitNewItem_Sp.ActiveConnection = Nothing
    Set AIM_InitNewItem_Sp = Nothing
    If Not (AIM_InitPOHeader_Sp Is Nothing) Then Set AIM_InitPOHeader_Sp.ActiveConnection = Nothing
    Set AIM_InitPOHeader_Sp = Nothing
    
Exit Function
ErrorHandler:
    If Not (AIM_InitNewItem_Sp Is Nothing) Then Set AIM_InitNewItem_Sp.ActiveConnection = Nothing
    Set AIM_InitNewItem_Sp = Nothing
    If Not (AIM_InitPOHeader_Sp Is Nothing) Then Set AIM_InitPOHeader_Sp.ActiveConnection = Nothing
    Set AIM_InitPOHeader_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(UpdateUnorderedItems)"
     f_HandleErr , , , "AIM_ReleaseUnorderedItems::UpdateUnorderedItems", Now, gDRGeneralError, True, Err
End Function
