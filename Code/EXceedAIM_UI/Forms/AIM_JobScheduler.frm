VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_JobScheduler 
   Caption         =   "SSA DR Job Scheduling"
   ClientHeight    =   9480
   ClientLeft      =   75
   ClientTop       =   345
   ClientWidth     =   11910
   Icon            =   "AIM_JobScheduler.frx":0000
   LinkTopic       =   "Form1"
   MDIChild        =   -1  'True
   ScaleHeight     =   9480
   ScaleWidth      =   11910
   WindowState     =   2  'Maximized
   Begin ActiveToolBars.SSActiveToolBars tbJobScheduling 
      Left            =   120
      Top             =   9120
      _ExtentX        =   767
      _ExtentY        =   767
      _Version        =   131083
      ToolBarsCount   =   1
      ToolsCount      =   6
      Tools           =   "AIM_JobScheduler.frx":030A
      ToolBars        =   "AIM_JobScheduler.frx":4F3E
   End
   Begin VB.Timer tiJobScheule 
      Interval        =   60000
      Left            =   2520
      Top             =   480
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgJobs 
      Height          =   9075
      Left            =   0
      TabIndex        =   0
      Top             =   120
      Width           =   11925
      _Version        =   196617
      DataMode        =   1
      BeginProperty HeadFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Arial"
         Size            =   9
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      GroupHeaders    =   0   'False
      AllowUpdate     =   0   'False
      RowSelectionStyle=   1
      AllowColumnSwapping=   0
      SelectTypeCol   =   0
      SelectTypeRow   =   0
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   450
      ExtraHeight     =   26
      CaptionAlignment=   0
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   21034
      _ExtentY        =   16007
      _StockProps     =   79
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Arial"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Arial"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Arial"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin VB.Menu mnuJobs 
      Caption         =   "&Jobs"
      Visible         =   0   'False
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "New &Job"
         Index           =   0
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "-"
         Index           =   1
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "&Start Job"
         Index           =   2
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "S&top Job"
         Index           =   3
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "-"
         Index           =   4
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "&Enable Job"
         Index           =   5
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "&View Job History"
         Index           =   6
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "&Refresh Jobs"
         Index           =   7
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "-"
         Index           =   8
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "&Delete Job"
         Index           =   9
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "-"
         Index           =   10
      End
      Begin VB.Menu mnuJobsOpt 
         Caption         =   "&Properties"
         Index           =   11
      End
   End
End
Attribute VB_Name = "AIM_JobScheduler"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Const SPLITTERPOSITION As Integer = 1

Dim AddFlag As Boolean
Dim CurJobName As String
Dim CurJobBookMark As Variant

Dim Cn As ADODB.Connection

Dim sp_delete_job As ADODB.Command
Dim sp_help_category As ADODB.Command
Dim sp_help_job As ADODB.Command
Dim sp_start_job As ADODB.Command
Dim sp_stop_job As ADODB.Command
Dim sp_update_job As ADODB.Command

Dim rsJobs As ADODB.Recordset

Private Function GetJobsList()
On Error GoTo ErrorHandler

    Dim CatFilter As String
    Dim strMessage As String
    
    'Disable Timer
    Me.tiJobScheule.Enabled = False
    
    'Display Status
    strMessage = getTranslationResource("STATMSG02800")
    If StrComp(strMessage, "STATMSG02800") = 0 Then strMessage = "Updating job(s) status..."
    Write_Message strMessage

    'Get the Job Category Filter
    CatFilter = Me.tbJobScheduling.ToolBars("tbPrintMenu").Tools("Id_Filter").ComboBox.Text
    
    'Set the Filter
    If CatFilter = getTranslationResource("All") Then
        sp_help_job.Parameters("@category_name").Value = Null
    Else
        'sp_help_job.Parameters("@category_name").Value = CatFilter
        sp_help_job.Parameters("@category_name").Value = "SSADRJobs" 'sgajjela
    End If
    
    If f_IsRecordsetValidAndOpen(rsJobs) Then
        rsJobs.Requery
    Else
        rsJobs.Open sp_help_job
    End If
    
    If (Err <> 0) Then
        f_HandleErr Me.Caption & "AIM_JobScheduler::GetJobsList"
    End If

    
    'If a Job List could not be created
    If Not f_IsRecordsetOpenAndPopulated(rsJobs) Then
        CurJobName = ""
        CurJobBookMark = 0
        dgJobs.Reset
    Else
        If rsJobs.eof Or rsJobs.BOF Then rsJobs.MoveFirst
        If Not IsEmpty(CurJobBookMark) Then
            If CurJobBookMark > 0 And CurJobBookMark <= rsJobs.RecordCount Then
                rsJobs.Bookmark = CurJobBookMark
            End If
        Else
            'Save the Job Name and Bookmark
            CurJobBookMark = rsJobs.Bookmark
        End If
        'Bind the Jobs Data Grid
        CurJobName = rsJobs!Name
        Me.dgJobs.ReBind
        Me.dgJobs.Bookmark = CurJobBookMark
        Me.dgJobs.Rows = rsJobs.RecordCount
    End If
    'Update the toolbar
    Me.tbJobScheduling.ToolBars("tbPrintMenu").Tools("ID_LastUpdate").ChangeAll ssChangeAllName, Format(Time, gTimeFormat)

    Write_Message ""
    
    'Enable Timer
    Me.tiJobScheule.Enabled = True

    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_JobScheduler::GetJobsList", Now, gDRGeneralError, True, Err
   ' Err.Raise Err.Number, Err.source, Err.Description & "(GetJobsList)"
    
End Function

Private Function LoadJobMaintenance(p_CurJobName As String, p_AddFlag As Boolean)
On Error GoTo ErrorHandler

    If p_CurJobName = "" And p_AddFlag = False Then
        Exit Function
    Else
        AIM_JobMaintenance.CurJobName = p_CurJobName
        AIM_JobMaintenance.AddFlag = p_AddFlag
        AIM_JobMaintenance.CancelFlag = False
        AIM_JobMaintenance.Show vbModal, AIM_Main
        
        Set AIM_JobMaintenance = Nothing
        
        'Refresh Job
        GetJobsList
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(LoadJobMaintenance)"
    f_HandleErr , , , "AIM_JobScheduler::LoadJobMaintenance", Now, gDRGeneralError, True, Err
End Function

Private Function UpdateJob(Job_Name As String, New_Name As String, Enabled As Integer, Description As String, _
    Category As String)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim strText As String
    
    sp_update_job("@job_id").Value = Null
    sp_update_job("@job_name").Value = Job_Name
    
    sp_update_job("@new_name").Value = New_Name
    sp_update_job("@enabled").Value = Enabled
    sp_update_job("@description").Value = Description
    sp_update_job("@start_step_id").Value = Null
    sp_update_job("@category_name").Value = Category
    sp_update_job("@owner_login_name").Value = Null
    sp_update_job("@notify_level_eventlog").Value = Null
    sp_update_job("@notify_level_email").Value = Null
    sp_update_job("@notify_level_netsend").Value = Null
    sp_update_job("@notify_level_page").Value = Null
    sp_update_job("@notify_email_operator_name").Value = Null
    sp_update_job("@notify_netsend_operator_name").Value = Null
    sp_update_job("@notify_page_operator_name").Value = Null
    sp_update_job("@delete_level").Value = Null
    
    'Add the job
    sp_update_job.Execute
    
    'Check for errors
    If sp_update_job(0) = 1 Then
        strMessage = getTranslationResource("MSGBOX02800")
        If StrComp(strMessage, "MSGBOX02800") = 0 Then strMessage = "Error processing job."
        strText = getTranslationResource(Me.Caption)
        MsgBox strMessage + vbCrLf + vbCrLf + Err.Description, vbCritical + vbOKOnly, strText
    Else
        GetJobsList
    End If

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(UpdateJob)"
End Function

Private Sub dgJobs_BtnClick()
On Error GoTo ErrorHandler

    'Disable Timer
    Me.tiJobScheule.Enabled = False

    If CurJobName <> "" Then
        AIM_JobHistory.m_CurJobName = CurJobName
        Set AIM_JobHistory.m_Cn = Cn
        
        AIM_JobHistory.Show vbModal, AIM_Main
        
        Set AIM_JobHistory = Nothing
    
    End If
    
    'Enable Timer
    Me.tiJobScheule.Enabled = True
        
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_BtnClick)"
    f_HandleErr , , , "AIM_JobScheduler::dgJobs_BtnClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_DblClick()
On Error GoTo ErrorHandler

    'Disable Timer
    Me.tiJobScheule.Enabled = False
    
    'Launch the Job Maintenance Form
    LoadJobMaintenance CurJobName, False
    
    'Enable Timer
    Me.tiJobScheule.Enabled = True
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_DblClick)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_DblClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_RowColChange(ByVal LastRow As Variant, ByVal LastCol As Integer)
On Error GoTo ErrorHandler

    CurJobName = Me.dgJobs.Columns("name").Value
    CurJobBookMark = Me.dgJobs.GetBookmark(0)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_RowColChange)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_RowColChange", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dgJobs.ROWHEIGHT = 540
    
    'Define Columns
    Me.dgJobs.Columns(0).Name = "alert"
    Me.dgJobs.Columns(0).Caption = " "
    Me.dgJobs.Columns(0).Width = 400
    Me.dgJobs.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dgJobs.Columns(0).Style = ssStyleButton
    Me.dgJobs.Columns(0).ButtonsAlways = True
    Me.dgJobs.Columns(0).Locked = False

    Me.dgJobs.Columns(1).Name = "name"
    Me.dgJobs.Columns(1).Caption = getTranslationResource("Job Name")
    Me.dgJobs.Columns(1).Width = 3000
    Me.dgJobs.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dgJobs.Columns(1).Locked = True

    Me.dgJobs.Columns(2).Name = "enabled"
    Me.dgJobs.Columns(2).Caption = ""
    Me.dgJobs.Columns(2).Width = 400
    Me.dgJobs.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(2).Alignment = ssCaptionAlignmentCenter
    Me.dgJobs.Columns(2).Style = ssStyleCheckBox
    Me.dgJobs.Columns(2).Locked = False

    Me.dgJobs.Columns(3).Name = "category"
    Me.dgJobs.Columns(3).Caption = getTranslationResource("Category")
    Me.dgJobs.Columns(3).Width = 2000
    Me.dgJobs.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgJobs.Columns(3).Locked = True

    Me.dgJobs.Columns(4).Name = "currentrunstatus"
    Me.dgJobs.Columns(4).Caption = getTranslationResource("Status")
    Me.dgJobs.Columns(4).Width = 1200
    Me.dgJobs.Columns(4).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(4).Alignment = ssCaptionAlignmentLeft
    Me.dgJobs.Columns(4).Locked = True

    Me.dgJobs.Columns(5).Name = "lastrunoutcome"
    Me.dgJobs.Columns(5).Caption = getTranslationResource("Last Outcome")
    Me.dgJobs.Columns(5).Width = 1200
    Me.dgJobs.Columns(5).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(5).Alignment = ssCaptionAlignmentLeft
    Me.dgJobs.Columns(5).Locked = True

    Me.dgJobs.Columns(6).Name = "lastrundate"
    Me.dgJobs.Columns(6).Caption = getTranslationResource("Last Run Date")
    Me.dgJobs.Columns(6).Width = 2400
    Me.dgJobs.Columns(6).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(6).Alignment = ssCaptionAlignmentLeft
    Me.dgJobs.Columns(6).Locked = True

    Me.dgJobs.Columns(7).Name = "nextrundate"
    Me.dgJobs.Columns(7).Caption = getTranslationResource("Next Run Date")
    Me.dgJobs.Columns(7).Width = 2400
    Me.dgJobs.Columns(7).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(7).Alignment = ssCaptionAlignmentLeft
    Me.dgJobs.Columns(7).Locked = True

    Me.dgJobs.Columns(8).Name = "has_step"
    Me.dgJobs.Columns(8).Caption = getTranslationResource("Step")
    Me.dgJobs.Columns(8).Width = 600
    Me.dgJobs.Columns(8).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(8).Alignment = ssCaptionAlignmentCenter
    Me.dgJobs.Columns(8).Style = ssStyleCheckBox
    Me.dgJobs.Columns(8).Locked = True

    Me.dgJobs.Columns(9).Name = "has_schedule"
    Me.dgJobs.Columns(9).Caption = getTranslationResource("Schd")
    Me.dgJobs.Columns(9).Width = 600
    Me.dgJobs.Columns(9).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(9).Alignment = ssCaptionAlignmentCenter
    Me.dgJobs.Columns(9).Style = ssStyleCheckBox
    Me.dgJobs.Columns(9).Locked = True

    Me.dgJobs.Columns(10).Name = "has_target"
    Me.dgJobs.Columns(10).Caption = getTranslationResource("Target")
    Me.dgJobs.Columns(10).Width = 700
    Me.dgJobs.Columns(10).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgJobs.Columns(10).Alignment = ssCaptionAlignmentCenter
    Me.dgJobs.Columns(10).Style = ssStyleCheckBox
    Me.dgJobs.Columns(10).Locked = True

    'Set Splitter Position
    Me.dgJobs.SplitterPos = SPLITTERPOSITION
    
    'Restore the grid layout
    On Error Resume Next 'To prevent error when they run the first time
    Me.dgJobs.LoadLayout App.Path + "\" + "AIM_JobScheduler11" + Trim(gUserID) + "_Layout.grd"
    'Check for non-existent file
    If Err.Number = 53 Then     'File Not Found
        Resume Next
    ElseIf Err.Number <> 0 Then
        GoTo ErrorHandler
    End If
    On Error GoTo ErrorHandler
    
    'Reset the font and captions, just in case the language in the layout file was different.
    SetProperFont Me.dgJobs.Font
    SetProperFont Me.dgJobs.HeadFont
    SetProperFont Me.dgJobs.PageFooterFont
    SetProperFont Me.dgJobs.PageHeaderFont
    Me.dgJobs.Columns(0).Caption = " "
    Me.dgJobs.Columns(1).Caption = getTranslationResource("Job Name")
    Me.dgJobs.Columns(2).Caption = ""
    Me.dgJobs.Columns(3).Caption = getTranslationResource("Category")
    Me.dgJobs.Columns(4).Caption = getTranslationResource("Status")
    Me.dgJobs.Columns(5).Caption = getTranslationResource("Last Outcome")
    Me.dgJobs.Columns(6).Caption = getTranslationResource("Last Run Date")
    Me.dgJobs.Columns(7).Caption = getTranslationResource("Next Run Date")
    Me.dgJobs.Columns(8).Caption = getTranslationResource("Step")
    Me.dgJobs.Columns(9).Caption = getTranslationResource("Schd")
    Me.dgJobs.Columns(10).Caption = getTranslationResource("Target")
        
    If Me.dgJobs.StyleSets.Count > 0 Then
        Me.dgJobs.StyleSets.RemoveAll
    End If
     'Define Style Sets
    Me.dgJobs.StyleSets.Add ("Active")
    Me.dgJobs.StyleSets("Active").BackColor = vbGreen
    Me.dgJobs.StyleSets("Active").Picture = LoadResPicture("PO_Active", vbResBitmap)

    Me.dgJobs.StyleSets.Add ("New")
    Me.dgJobs.StyleSets("New").BackColor = vbRed
    Me.dgJobs.StyleSets("New").Picture = LoadResPicture("PO_New", vbResBitmap)

    Me.dgJobs.StyleSets.Add ("Generic")
    Me.dgJobs.StyleSets("Generic").BackColor = vbYellow
    Me.dgJobs.StyleSets("Generic").Picture = LoadResPicture("PO_Priority", vbResBitmap)
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgJobs, ACW_EXPAND
    End If
    
    For IndexCounter = 0 To dgJobs.Columns.Count - 1
'        dgJobs.Columns(IndexCounter).HasHeadBackColor = True
'        dgJobs.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgJobs.Columns(IndexCounter).Locked = False Then dgJobs.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_InitColumnProps)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    'Check for a closed or empty Job Set
    If f_IsRecordsetOpenAndPopulated(rsJobs) Then
        If CurJobBookMark > 0 Then
            rsJobs.Bookmark = CurJobBookMark
        Else
            'Add else statement so that if the bookmark is not valid the recordset is moved to first record
            'Sri March-03-2004
            rsJobs.MoveFirst
            CurJobBookMark = rsJobs.Bookmark
            CurJobName = rsJobs("name").Value
        End If
        
        
        If rsJobs("Enabled").Value = vbChecked Then
            Me.mnuJobsOpt(5).Caption = getTranslationResource("D&isable Job")
        Else
            Me.mnuJobsOpt(5).Caption = getTranslationResource("&Enable Job")
        End If
    End If
    
    'Set the enable/disable option
    Select Case Button
        Case vbRightButton
            Me.PopupMenu Me.mnuJobs
    
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_MouseDown)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler
    
    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_JobScheduler::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

'    If IsNull(StartLocation) Then
'        StartLocation = 0
'    End If
'
'    NewLocation = CLng(StartLocation) + NumberOfRowsToMove
'
    
    If Not f_IsRecordsetOpenAndPopulated(rsJobs) Then Exit Sub
    
    'Align recordset
    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsJobs.MoveLast
        Else
            rsJobs.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsJobs.Bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsJobs.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsJobs.Bookmark


Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_UnboundPositionData)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
'On Error GoTo ErrorHandler
On Error Resume Next

    Dim RowCounter As Integer
    Dim RowIndex As Integer

    'Clear residual errors from the Connection object
    Cn.Errors.Clear

    'Reset the AddFlag
    AddFlag = False
    
    'Check recordsets
    If Not f_IsRecordsetOpenAndPopulated(rsJobs) Then Exit Sub
    If dgJobs.DataChanged Then Exit Sub
    
    'Align the recordset
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsJobs.MoveLast
        Else
            rsJobs.MoveFirst
        End If
    Else
        rsJobs.Bookmark = StartLocation
        If ReadPriorRows Then
            rsJobs.MovePrevious
        Else
            rsJobs.MoveNext
        End If
    End If
    
    'Fetch data into grid
    For RowIndex = 0 To RowBuf.RowCount - 1
        If rsJobs.BOF Or rsJobs.eof Then Exit For
        
        Select Case RowBuf.ReadType
            Case 0  'All data must be read
                RowBuf.Value(RowIndex, 0) = ""
                RowBuf.Value(RowIndex, 1) = rsJobs("name").Value
                RowBuf.Value(RowIndex, 2) = rsJobs("enabled").Value
                RowBuf.Value(RowIndex, 3) = rsJobs("category").Value
                RowBuf.Value(RowIndex, 4) = SQLDMO_GetCurrentStatus(rsJobs("current_execution_status").Value)
                RowBuf.Value(RowIndex, 5) = SQLDMO_GetOutComeDescription(rsJobs("last_run_outcome").Value)
                RowBuf.Value(RowIndex, 6) = SQLDMO_BldDateTime(rsJobs("last_run_date").Value, rsJobs("last_run_time").Value)
                RowBuf.Value(RowIndex, 7) = SQLDMO_BldDateTime(rsJobs("next_run_date").Value, rsJobs("next_run_time").Value)
                RowBuf.Value(RowIndex, 8) = IIf(rsJobs("has_step").Value = 0, 0, 1)
                RowBuf.Value(RowIndex, 9) = IIf(rsJobs("has_schedule").Value = 0, 0, 1)
                RowBuf.Value(RowIndex, 10) = IIf(rsJobs("has_target").Value = 0, 0, 1)
                    
            Case 1  'Only bookmarks must be read
                'That is done anyway, so leave it after end select
            
            Case Else
                'Cases 2 and 3 are not used by DBGrid... yet?
        End Select
        
        'Read the bookmark
        RowBuf.Bookmark(RowIndex) = rsJobs.Bookmark
    
        If ReadPriorRows Then
            rsJobs.MovePrevious
        Else
            rsJobs.MoveNext
        End If

        RowCounter = RowCounter + 1
    Next RowIndex

    RowBuf.RowCount = RowCounter
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_UnboundReadData)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgJobs_RowLoaded(ByVal Bookmark As Variant)
'On Error GoTo ErrorHandler
On Error Resume Next

    If Not IsEmpty(Bookmark) Then
        'Position Job Record Set
        rsJobs.Bookmark = Bookmark

        'Job Alert
        Select Case rsJobs("last_run_outcome").Value
            Case 0  'Failed
                Me.dgJobs.Columns("alert").CellStyleSet "Generic"
            Case 1  'Succeeded
                Me.dgJobs.Columns("alert").CellStyleSet "Active"
            Case 3  'Cancelled
                Me.dgJobs.Columns("alert").CellStyleSet "Generic"
            Case 4  'Executing
                Me.dgJobs.Columns("alert").CellStyleSet "New"
            Case 5  'Unknown
                Me.dgJobs.Columns("alert").CellStyleSet "Generic"
            Case Else
                Me.dgJobs.Columns("alert").CellStyleSet "Generic"
        End Select
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgJobs_RowLoaded)"
     f_HandleErr , , , "AIM_JobScheduler::dgJobs_RowLoaded", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Create Categery 'SSADRJobs' if reqired -sgajjela
    CheckExisttensOfSSADRCategoryAndCreateIfReqired
        
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG02801")
    If StrComp(strMessage, "STATMSG02801") = 0 Then strMessage = "Initializing AIM Job Scheduler..."
    Write_Message strMessage

    GetTranslatedCaptions Me
    
    'Connect to SQL Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False, True, , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub
    
    'Define Stored Procedures
    Set sp_delete_job = New ADODB.Command
    With sp_delete_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_delete_job"
        .Parameters.Refresh
    End With
    
    Set sp_help_category = New ADODB.Command
    With sp_help_category
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_help_category"
        .Parameters.Refresh
    End With
    
    Set sp_help_job = New ADODB.Command
    With sp_help_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_help_job"
        .Parameters.Refresh
    End With
    
    Set sp_start_job = New ADODB.Command
    With sp_start_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_start_job"
        .Parameters.Refresh
    End With
    
    Set sp_stop_job = New ADODB.Command
    With sp_stop_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_stop_job"
        .Parameters.Refresh
    End With
    
    Set sp_update_job = New ADODB.Command
    With sp_update_job
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "sp_update_job"
        .Parameters.Refresh
    End With
    
    'Define Record Sets
    Set rsJobs = New ADODB.Recordset
    With rsJobs
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        .Sort = "name"
    End With
    
    'Build the Category List
    GetCategoryList
    
    'Get the Job List
    GetJobsList
    
    'Set the Job and Job Step
    If f_IsRecordsetOpenAndPopulated(rsJobs) Then
        rsJobs.MoveFirst
        
        CurJobName = rsJobs("name").Value
        CurJobBookMark = rsJobs.Bookmark
    End If
    
    'Initialize the Last Update Label
    Me.tbJobScheduling.ToolBars("tbPrintMenu").Tools("ID_LastUpdate").ChangeAll ssChangeAllName, Format(Time, gTimeFormat)

    'Add to Windows List
    AddToWindowList Me.Caption
    
    'Wind Up
    Screen.MousePointer = vbNormal
    Write_Message ""

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , "AIM_JobScheduler::Form_Load", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Resize()
On Error GoTo ErrorHandler
    
    'Resize the Job Grid
    Me.dgJobs.Left = 0
    Me.dgJobs.Top = 0
    Me.dgJobs.Width = Me.ScaleWidth
    Me.dgJobs.Height = Me.ScaleHeight

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Resize)"
     f_HandleErr , , , "AIM_JobScheduler::Form_Resize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsJobs) Then rsJobs.Close
    Set rsJobs = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Save current grid layout.
     Me.dgJobs.SaveLayout App.Path + "\" + "AIM_JobScheduler" + Trim(gUserID) + "_Layout.grd", ssSaveLayoutAll
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
         f_HandleErr , , , "AIM_JobScheduler::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuJobsOpt_Click(Index As Integer)
On Error GoTo ErrorHandler
    Dim Enabled As Integer
    Dim strMessage As String
    Dim strText As String
    Dim ErrorDesc As String
    
    'Disable Timer
    Me.tiJobScheule.Enabled = False
    
    Select Case Index
        Case 0          'New Job
            LoadJobMaintenance "", True
        
        Case 1
        
        Case 2          'Start Job
            sp_start_job("@job_name").Value = CurJobName
            sp_start_job.Execute
            
            'Refresh everying
            GetJobsList
            
        Case 3          'Stop Job
            sp_stop_job("@job_name").Value = CurJobName
            sp_stop_job.Execute
            
            'Refresh everyting
            GetJobsList
    
        Case 4
        
        Case 5          'Enable Job
            rsJobs.Bookmark = CurJobBookMark
            
            Enabled = IIf(rsJobs("Enabled").Value = vbChecked, 0, 1)
            UpdateJob rsJobs("name").Value, rsJobs("name").Value, Enabled, rsJobs("description").Value, rsJobs("category").Value
        
        Case 6          'View Job History
            If CurJobName <> "" Then
                AIM_JobHistory.m_CurJobName = CurJobName
                Set AIM_JobHistory.m_Cn = Cn
                
                AIM_JobHistory.Show vbModal, AIM_Main
                Set AIM_JobHistory = Nothing
            End If
        Case 7          'Refresh Job
            GetJobsList
    
        Case 8
        
        Case 9          'Delete Job
            sp_delete_job.Parameters("@job_name").Value = CurJobName
            sp_delete_job.Execute
            
            If sp_delete_job(0).Value = 0 Then
                GetJobsList
            End If
    
        Case 11         'Properties
            LoadJobMaintenance CurJobName, False
        
    End Select
    
    
    'Enable Timer
    Me.tiJobScheule.Enabled = True
    
Exit Sub
ErrorHandler:
    'check for known errors:
    If Err.Number = -2147217900 _
    Then
        ErrorDesc = Err.Description
        strMessage = getTranslationResource("MSGBOX02800")
        If StrComp(strMessage, "MSGBOX02800") = 0 Then strMessage = "Error processing job."
        strText = getTranslationResource(Me.Caption)
        MsgBox strMessage + vbCrLf + vbCrLf + ErrorDesc, vbCritical + vbOKOnly, strText
    Else
        'f_HandleErr Me.Caption & "(mnuJobsOpt_Click)"
         f_HandleErr , , , "AIM_JobScheduler::mnuJobsOpt_Click", Now, gDRGeneralError, True, Err
    End If

End Sub

Private Sub tbJobScheduling_ComboCloseUp(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    Select Case Tool.ID
        Case "ID_Filter"
            GetJobsList
        
        Case Else
    
    End Select
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbJobScheduling_ComboCloseUp)"
     f_HandleErr , , , "AIM_JobScheduler::tbJobScheduling_ComboCloseUp", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbJobScheduling_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler
    
    Select Case Tool.ID
        Case "ID_Refresh"
            GetJobsList
    
        Case "ID_Log"
            Me.tiJobScheule.Enabled = False
            If gWantToLogToDB = True Then
                
                'If Not FormExists(AIM_ErrorLookup.Caption) Then
                    AIM_ErrorLookup.DisplayMode = "JobLog"
                    AIM_ErrorLookup.Show vbModal
                    Set AIM_ErrorLookup = Nothing
                'End If
            Else
               AIM_JobLog.Show vbModal
            End If
            Me.tiJobScheule.Enabled = True
        Case "ID_AddNew"
            Me.tiJobScheule.Enabled = False
            LoadJobMaintenance "", True
            Me.tiJobScheule.Enabled = True
        
        Case "ID_Close"
            Unload Me
        
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbJobScheduling_ToolClick)"
     f_HandleErr , , , "AIM_JobScheduler::tbJobScheduling_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub tiJobScheule_Timer()
On Error GoTo ErrorHandler

    GetJobsList

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tiJobScheule_Timer)"
     f_HandleErr , , , "AIM_JobScheduler::tiJobScheule_Timer", Now, gDRGeneralError, True, Err
End Sub

Private Function GetCategoryList()
On Error GoTo ErrorHandler

    Dim rsCategory As ADODB.Recordset

    Set rsCategory = New ADODB.Recordset
    With rsCategory
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsCategory.Open sp_help_category
    If f_IsRecordsetOpenAndPopulated(rsCategory) Then
        rsCategory.MoveFirst
        Do Until rsCategory.eof
            With Me.tbJobScheduling
                .Tools("Id_Filter").ComboBox.AddItem rsCategory("name").Value
                rsCategory.MoveNext
            End With
        Loop
    Else
        Me.tbJobScheduling.Tools("ID_Filter").ComboBox.Clear
    End If
    
    'Clean up
    If f_IsRecordsetValidAndOpen(rsCategory) Then rsCategory.Close
    Set rsCategory = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsCategory) Then rsCategory.Close
    Set rsCategory = Nothing
    Err.Raise Err.Number, Err.source & "(GetCategoryList)", Err.Description
End Function


'Returns true if exists. Otherwise false - sgajjela
Private Function CheckExisttensOfSSADRCategoryAndCreateIfReqired() As Boolean

On Error GoTo ErrorHandler

Dim RecSetCategory As ADODB.Recordset
Dim MSDBConnection As ADODB.Connection
Dim CmdHelpCategory As ADODB.Command
Dim RtnCode As Long

Set MSDBConnection = New ADODB.Connection

'Get connection to MSDB database
RtnCode = SQLConnection(MSDBConnection, CONNECTION_OPEN, False, True)

If (RtnCode <> SUCCEED) Then
    CheckExisttensOfSSADRCategoryAndCreateIfReqired = False ' Means, this function failled.
    Set MSDBConnection = Nothing
    Exit Function
End If

'Query the server to find 'SSARDRJobs' category existence
Set RecSetCategory = MSDBConnection.Execute("EXEC sp_help_category @class = 'JOB', @type = 'LOCAL', @name = 'SSADRJobs'")
If (RecSetCategory.eof <> True) Then
    If (RecSetCategory(2).Value = "SSADRJobs") Then
        CheckExisttensOfSSADRCategoryAndCreateIfReqired = True ' Means, category exists.
        RtnCode = SQLConnection(MSDBConnection, CONNECTION_CLOSE, False, True)
        Set MSDBConnection = Nothing
        Exit Function
    End If
End If

'Category not exists. Create it.
MSDBConnection.Execute ("EXEC sp_add_category 'JOB', @type = 'LOCAL', @name = 'SSADRJobs'")
If (Err <> 0) Then
    CheckExisttensOfSSADRCategoryAndCreateIfReqired = False
Else
    CheckExisttensOfSSADRCategoryAndCreateIfReqired = True
End If

'Close the connection.
RtnCode = SQLConnection(MSDBConnection, CONNECTION_CLOSE, False, True)

Exit Function
ErrorHandler:
    'f_HandleErr Me.Caption, True, Err.Number, Err.Description, Err.source, "AIM_JobScheduler::CheckExisttensOfSSADRCategoryAndCreateIfReqired", Now, "DRGE"
     f_HandleErr , , , "AIM_JobScheduler::CheckExisttensOfSSADRCategoryAndCreateIfReqired", Now, gDRGeneralError, True, Err
End Function
