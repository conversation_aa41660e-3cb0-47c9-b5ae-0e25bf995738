VERSION 5.00
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Object = "{1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0"; "sstbars2.ocx"
Begin VB.Form AIM_ClassMaintenance 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Class Maintenance"
   ClientHeight    =   6150
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   10905
   Icon            =   "AIM_ClassMaintenance.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MDIChild        =   -1  'True
   MinButton       =   0   'False
   ScaleHeight     =   6150
   ScaleWidth      =   10905
   ShowInTaskbar   =   0   'False
   Begin ActiveToolBars.SSActiveToolBars tbAIMClasses 
      Left            =   180
      Top             =   5760
      _ExtentX        =   741
      _ExtentY        =   767
      _Version        =   131083
      FontSource      =   1
      ToolBarsCount   =   1
      ToolsCount      =   13
      Tools           =   "AIM_ClassMaintenance.frx":030A
      ToolBars        =   "AIM_ClassMaintenance.frx":9BB2
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgAIMClasses 
      Height          =   5115
      Left            =   37
      TabIndex        =   0
      Top             =   600
      Width           =   10830
      _Version        =   196617
      DataMode        =   1
      AllowAddNew     =   -1  'True
      AllowDelete     =   -1  'True
      MultiLine       =   0   'False
      AllowColumnSwapping=   0
      CellNavigation  =   1
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      Columns(0).DataType=   8
      Columns(0).FieldLen=   4096
      _ExtentX        =   19103
      _ExtentY        =   9022
      _StockProps     =   79
      BeginProperty PageFooterFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      BeginProperty PageHeaderFont {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLangID 
      Bindings        =   "AIM_ClassMaintenance.frx":9ECD
      Height          =   345
      Left            =   2355
      TabIndex        =   1
      Top             =   120
      Width           =   1755
      DataFieldList   =   "LangId"
      _Version        =   196617
      DataMode        =   2
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      Columns(0).Width=   3200
      _ExtentX        =   3096
      _ExtentY        =   609
      _StockProps     =   93
      ForeColor       =   -2147483640
      BackColor       =   -2147483643
      DataFieldToDisplay=   "LangID"
   End
   Begin VB.Label lblLangID 
      Caption         =   "Language ID"
      Height          =   300
      Left            =   150
      TabIndex        =   2
      Top             =   165
      Width           =   2010
   End
   Begin VB.Menu mnuEdit 
      Caption         =   "&Edit"
      Visible         =   0   'False
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Copy"
         Index           =   0
      End
      Begin VB.Menu mnuEditOpt 
         Caption         =   "&Print"
         Index           =   1
      End
   End
End
Attribute VB_Name = "AIM_ClassMaintenance"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim Cn As ADODB.Connection

Dim rsAIMClasses As ADODB.Recordset

Dim AIMClassesBookMark As Variant

Const VALIDATIONSCOPE_FIELD As Integer = 0
Const VALIDATIONSCOPE_ROW As Integer = 1

Private Sub dcLangID_Click()
On Error GoTo ErrorHandler

    FetchAIMClasses (Me.dcLangID.Text)

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangID_Change)"
     f_HandleErr , , , Me.Caption & "::dcLangID_Change", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLangId_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    Me.dcLangID.Columns(0).Caption = getTranslationResource("Language ID")
    Me.dcLangID.Columns(0).Width = 1000
    Me.dcLangID.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLangID.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dcLangID.Columns(0).DataField = "LangID"
    
    Me.dcLangID.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLangID.Columns(1).Width = 2000
    Me.dcLangID.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLangID.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLangID.Columns(1).DataField = "LangDesc"
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLangID, ACW_EXPAND
    End If
    
'    For IndexCounter = 0 To dcLangID.Columns.Count - 1
'        dcLangID.Columns(IndexCounter).HasHeadBackColor = True
'        dcLangID.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLangID_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dcLangID_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_AfterInsert(RtnDispErrMsg As Integer)
On Error GoTo ErrorHandler

    If Cn.Errors.Count > 0 Then
        RtnDispErrMsg = True
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_AfterInsert)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_AfterInsert", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_BeforeColUpdate(ByVal ColIndex As Integer, ByVal OldValue As Variant, Cancel As Integer)
On Error GoTo ErrorHandler
    
    Dim RtnCode As Integer
    
    If ColIndex > 0 Then
        RtnCode = UserInputValidation(VALIDATIONSCOPE_FIELD)
        If RtnCode < 0 Then
            Cancel = True
        End If
        
    End If
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_BeforeColUpdate)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_BeforeColUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_BeforeDelete(Cancel As Integer, DispPromptMsg As Integer)
On Error GoTo ErrorHandler
    
    Dim Result As Long
    Dim strMessage As String
    Dim strMessage1 As String
    
    'Cancel the default prompt
    DispPromptMsg = False
    
    'Display a confirmation msgbox
    strMessage = getTranslationResource("MSGBOX06600")
    If StrComp(strMessage, "MSGBOX06600") = 0 Then strMessage = "Delete the"
    
    strMessage = strMessage + " " + str(dgAIMClasses.SelBookmarks.Count) + " "
    strMessage1 = getTranslationResource("MSGBOX06601")
    If StrComp(strMessage1, "MSGBOX06601") = 0 Then strMessage1 = "selected Classification Code(s)?"
    strMessage = strMessage + strMessage1

    Result = MsgBox(strMessage, vbYesNo, getTranslationResource(AIM_ClassMaintenance.Caption))

    Select Case Result
    Case vbYes
        'User chose to delete. Do nothing
    Case vbNo
        'User chose to Cancel
        Cancel = True
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_BeforeDelete)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_BeforeUpdate(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    
    RtnCode = UserInputValidation(VALIDATIONSCOPE_ROW)
    If RtnCode < 0 Then
        Cancel = True
    Else
        Cancel = False
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_BeforeUpdate)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_BeforeUpdate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_DblClick()
On Error GoTo ErrorHandler

'la here

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_BeforeDelete)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_BeforeDelete", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_HeadClick(ByVal ColIndex As Integer)
On Error GoTo ErrorHandler

    Dim ColName As String
    Dim SortSeq As String
    
    'Is the recordset open
    If Not (f_IsRecordsetOpenAndPopulated(rsAIMClasses)) Then Exit Sub
    
    'Check for unsortable column index
    If Me.dgAIMClasses.Columns(ColIndex).Name = "LangID" _
    Then Exit Sub
    
    'Set sort sequence, toggle existing one between ascending, descinding and none.
    SortSeq = ""
    
    If InStr(rsAIMClasses.Sort, " asc") <> 0 Then
        SortSeq = "desc"
        ColName = Me.dgAIMClasses.Columns(ColIndex).Name
    ElseIf InStr(rsAIMClasses.Sort, " desc") <> 0 Then
        SortSeq = ""
        ColName = ""
    Else
        SortSeq = "asc"
        ColName = Me.dgAIMClasses.Columns(ColIndex).Name
    End If
    
    'Sort grid by selected column
    rsAIMClasses.Sort = Trim(ColName & " " & SortSeq)

    Me.dgAIMClasses.ReBind
    If f_IsRecordsetOpenAndPopulated(rsAIMClasses) Then Me.dgAIMClasses.Rows = rsAIMClasses.RecordCount
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_HeadClick)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_HeadClick", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_InitColumnProps()
On Error GoTo ErrorHandler

    Dim Counter As Long
    Dim IndexCounter As Long
    
    'Define Columns
    Me.dgAIMClasses.Columns(0).Name = "Class"
    Me.dgAIMClasses.Columns(0).Caption = getTranslationResource("Class ID")
    Me.dgAIMClasses.Columns(0).Width = 1500
    Me.dgAIMClasses.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMClasses.Columns(0).Alignment = ssCaptionAlignmentLeft
    Me.dgAIMClasses.Columns(0).Style = ssStyleEdit
    Me.dgAIMClasses.Columns(0).Case = ssCaseUpper
    Me.dgAIMClasses.Columns(0).FieldLen = 50
    Me.dgAIMClasses.Columns(0).DataType = vbString

    Me.dgAIMClasses.Columns(1).Name = "ClassLevel"
    Me.dgAIMClasses.Columns(1).Caption = getTranslationResource("Class Level")
    Me.dgAIMClasses.Columns(1).Width = 2000
    Me.dgAIMClasses.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMClasses.Columns(1).Alignment = ssCaptionAlignmentRight
    Me.dgAIMClasses.Columns(1).DataType = vbInteger
    Me.dgAIMClasses.Columns(1).Style = ssStyleComboBox
    'Initialize Combo Box
    Me.dgAIMClasses.Columns(1).AddItem "1", 0
    Me.dgAIMClasses.Columns(1).AddItem "2", 1
    Me.dgAIMClasses.Columns(1).AddItem "3", 2
    Me.dgAIMClasses.Columns(1).AddItem "4", 3

    Me.dgAIMClasses.Columns(2).Name = "LangID"
    Me.dgAIMClasses.Columns(2).Caption = getTranslationResource("Language ID")
    Me.dgAIMClasses.Columns(2).Width = 1500
    Me.dgAIMClasses.Columns(2).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMClasses.Columns(2).Alignment = ssCaptionAlignmentLeft
    Me.dgAIMClasses.Columns(2).Case = ssCaseLower
    Me.dgAIMClasses.Columns(2).FieldLen = 10
    Me.dgAIMClasses.Columns(2).DataType = vbString
    Me.dgAIMClasses.Columns(2).Style = ssStyleComboBox
    'Initialize Combo Box
    Me.dcLangID.MoveFirst
    For Counter = 1 To Me.dcLangID.Rows
        If Me.dcLangID.Columns(0).Value <> getTranslationResource("All") Then
            Me.dgAIMClasses.Columns(2).AddItem Me.dcLangID.Columns(0).Value
        End If
        Me.dcLangID.MoveNext
    Next
    
    Me.dgAIMClasses.Columns(3).Name = "ClassDesc"
    Me.dgAIMClasses.Columns(3).Caption = getTranslationResource("Class Description")
    Me.dgAIMClasses.Columns(3).Width = 4880
    Me.dgAIMClasses.Columns(3).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dgAIMClasses.Columns(3).Alignment = ssCaptionAlignmentLeft
    Me.dgAIMClasses.Columns(3).Style = ssStyleEdit
    Me.dgAIMClasses.Columns(3).FieldLen = 255
    Me.dgAIMClasses.Columns(3).DataType = vbString
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgAIMClasses, ACW_EXPAND
    End If

    Me.dgAIMClasses.RowNavigation = ssRowNavigationUDLock
        
    For IndexCounter = 0 To dgAIMClasses.Columns.Count - 1
'        dgAIMClasses.Columns(IndexCounter).HasHeadBackColor = True
'        dgAIMClasses.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgAIMClasses.Columns(IndexCounter).Locked = False Then dgAIMClasses.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_InitColumnProps)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_MouseDown(Button As Integer, Shift As Integer, X As Single, Y As Single)
On Error GoTo ErrorHandler

    Dim strMessage As String
    
    Select Case Button
    Case vbRightButton
        Me.PopupMenu Me.mnuEdit
    Case Else
        'Left mouse click. validations here?
    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_MouseDown)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_MouseDown", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG06600")
    If StrComp(strMessage, "RPTMSG06600") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG06601")
    If StrComp(strMessage1, "RPTMSG06601") = 0 Then strMessage = "List of Classification Codes"
    strMessage2 = getTranslationResource("RPTMSG06602")
    If StrComp(strMessage2, "RPTMSG06602") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage + Format(Date, gDateFormat) + _
                            vbTab + strMessage1 + _
                            vbTab + strMessage2 + " <page number>"
                            
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_PrintInitialize)"
    f_HandleErr , , , Me.Caption & "::dgAIMClasses_PrintInitialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundAddData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, NewRowBookmark As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Integer
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    
    If IsNull(RowBuf.Value(0, 0)) _
    Or IsNull(RowBuf.Value(0, 1)) _
    Or IsNull(RowBuf.Value(0, 3)) _
    Then
        Exit Sub
    End If
    
    If UserInputValidation(VALIDATIONSCOPE_ROW) = 0 Then
        rsAIMClasses.AddNew
        
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsAIMClasses!Class = RowBuf.Value(0, 0)
        Else
            strMessage = getTranslationResource("MSGBOX06604")
            If StrComp(strMessage, "MSGBOX06604") = 0 Then _
                    strMessage = "Please provide a valid Classification Code ID."
            RtnCode = MsgBox(strMessage, vbOKOnly, Me.Caption)
            Exit Sub
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsAIMClasses!ClassLevel = RowBuf.Value(0, 1)
        End If
    
        If Me.dcLangID.Text <> getTranslationResource("All") Then
            rsAIMClasses!LangID = Me.dcLangID.Text
        Else
            If Not IsNull(RowBuf.Value(0, 2)) Then
                rsAIMClasses!LangID = RowBuf.Value(0, 2)
            Else
                rsAIMClasses!LangID = gLangID
            End If
        End If
        
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsAIMClasses!ClassDesc = RowBuf.Value(0, 3)
        Else
            rsAIMClasses!ClassDesc = " "
        End If
    
        rsAIMClasses.Update
        
        Me.tbAIMClasses.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")

    End If
        
    Me.tbAIMClasses.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundAddData)"
    rsAIMClasses.CancelUpdate
    FetchAIMClasses (Me.dcLangID.Text)
    f_HandleErr , , , Me.Caption & "::dgAIMClasses_UnboundAddData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundDeleteRow(Bookmark As Variant)
On Error GoTo ErrorHandler

    rsAIMClasses.Bookmark = Bookmark
    rsAIMClasses.Delete

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundDeleteRow)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_UnboundDeleteRow", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundPositionData(StartLocation As Variant, ByVal NumberOfRowsToMove As Long, NewLocation As Variant)
On Error GoTo ErrorHandler

    If IsNull(StartLocation) Then
        'Going up or down?
        If NumberOfRowsToMove = 0 Then
            Exit Sub
        ElseIf NumberOfRowsToMove < 0 Then
            rsAIMClasses.MoveLast
        Else
            rsAIMClasses.MoveFirst
        End If
        
    Else
        'Line up the bookmark of the recordset
        'with the grid's StartLocation
        rsAIMClasses.Bookmark = StartLocation
    
    End If
    
    'Note: Do not use StartLocation because it could be null
    rsAIMClasses.Move NumberOfRowsToMove

    'Set the new location in the grid
    NewLocation = rsAIMClasses.Bookmark

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundPositionData)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_UnboundPositionData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim RowIndex As Integer
    Dim ColIndex As Integer
    Dim RowCounter As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsAIMClasses) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsAIMClasses.MoveLast
        Else
            rsAIMClasses.MoveFirst
        End If
    
    Else
        rsAIMClasses.Bookmark = StartLocation
        If ReadPriorRows Then
            rsAIMClasses.MovePrevious
        Else
            rsAIMClasses.MoveNext
        End If
    
    End If
    
    If rsAIMClasses.BOF Or rsAIMClasses.eof Then Exit Sub
        
    For RowIndex = 0 To RowBuf.RowCount - 1
        If rsAIMClasses.BOF Or rsAIMClasses.eof Then Exit For
        
        Select Case RowBuf.ReadType
            Case 0      'All data must be read
                RowBuf.Value(RowIndex, 0) = rsAIMClasses!Class
                RowBuf.Value(RowIndex, 1) = rsAIMClasses!ClassLevel
                RowBuf.Value(RowIndex, 2) = rsAIMClasses!LangID
                RowBuf.Value(RowIndex, 3) = rsAIMClasses!ClassDesc
                
            
            Case 1      'Only bookmarks must be read
                'That is done anyway, so leave it after end select
            Case Else
                'Cases 2 and 3 are not used by DBGrid... yet?
        End Select
        
        RowBuf.Bookmark(RowIndex) = rsAIMClasses.Bookmark
    
        If ReadPriorRows Then
            rsAIMClasses.MovePrevious
        Else
            rsAIMClasses.MoveNext
        End If
    
        RowCounter = RowCounter + 1
    Next RowIndex
    
    RowBuf.RowCount = RowCounter

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundReadData)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_UnboundReadData", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgAIMClasses_UnboundWriteData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, WriteLocation As Variant)
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Integer
    
    'Clear residual errors from the Connection object
    Cn.Errors.Clear
    
    rsAIMClasses.Bookmark = WriteLocation

    If UserInputValidation(VALIDATIONSCOPE_ROW) = 0 Then
        If Not IsNull(RowBuf.Value(0, 0)) Then
            rsAIMClasses!Class = RowBuf.Value(0, 0)
        End If
    
        If Not IsNull(RowBuf.Value(0, 1)) Then
            rsAIMClasses!ClassLevel = RowBuf.Value(0, 1)
        End If
    
        If Not IsNull(RowBuf.Value(0, 2)) Then
            rsAIMClasses!LangID = RowBuf.Value(0, 2)
        Else
            rsAIMClasses!LangID = gLangID
        End If
        
        If Not IsNull(RowBuf.Value(0, 3)) Then
            rsAIMClasses!ClassDesc = RowBuf.Value(0, 3)
        Else
            rsAIMClasses!ClassDesc = ""
        End If
        
        'Update the Class Code Table
        rsAIMClasses.Update
    Else
        rsAIMClasses.CancelUpdate
        Exit Sub
    End If

    If Cn.Errors.Count > 0 Then
        strMessage = getTranslationResource("ERRMSG06601")
        If StrComp(strMessage, "ERRMSG02201") = 0 Then strMessage = "Error editing Classification Code record."
        ADOErrorHandler Cn, strMessage
        rsAIMClasses.CancelUpdate
    Else
        strMessage = getTranslationResource("STATMSG06601")
        If StrComp(strMessage, "STATMSG06601") = 0 Then strMessage = "Classification Code successfully modified."
        Write_Message strMessage
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgAIMClasses_UnboundWriteData)"
     f_HandleErr , , , Me.Caption & "::dgAIMClasses_UnboundWriteData", Now, gDRGeneralError, True, Err
    rsAIMClasses.CancelUpdate
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    'Check for an open connection
    If Cn.State <> adStateOpen Then
        Unload Me
        Exit Sub
    End If

    'Set State of window list State Button
    gbUpdating = True
    
    AIM_Main.tbAIM_Main.Tools(Me.Caption).State = ssChecked
    gbUpdating = False

    Me.dcLangID.Text = gLangID

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
    f_HandleErr , , , Me.Caption & "::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim strYes As String
    Dim RtnCode As Integer
    Dim strMessage As String
    
    'Housekeeping
    Screen.MousePointer = vbHourglass
    strMessage = getTranslationResource("STATMSG06600")
    If StrComp(strMessage, "STATMSG06600") = 0 Then strMessage = "Initializing AIM Classification Code Maintenance..."
    Write_Message strMessage

    GetTranslatedCaptions Me
        
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , Me.Caption)
    If RtnCode <> SUCCEED Then Exit Sub

    'Call langdropdown here
    PopulateLangIDs
    
    'Call AIMClasses here
    FetchAIMClasses (gLangID)
        
    'Enable/Disable Update
    If gAccessLvl = 1 Then
        Me.tbAIMClasses.Tools("ID_Save").Enabled = False
        Me.tbAIMClasses.Tools("ID_Delete").Enabled = False
        Me.tbAIMClasses.Tools("ID_AddNew").Enabled = False
        Me.dgAIMClasses.AllowUpdate = False
        Me.dgAIMClasses.AllowAddNew = False
        Me.dgAIMClasses.AllowDelete = False
    Else
        Me.tbAIMClasses.Tools("ID_Save").Enabled = True
        Me.tbAIMClasses.Tools("ID_Delete").Enabled = True
        Me.tbAIMClasses.Tools("ID_AddNew").Enabled = True
        Me.dgAIMClasses.AllowUpdate = True
        Me.dgAIMClasses.AllowAddNew = True
        Me.dgAIMClasses.AllowDelete = True
    End If
    
    'Add to Windows List
    AddToWindowList Me.Caption

    'Windup
    Write_Message ""
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Load)"
     f_HandleErr , , , Me.Caption & "::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    If f_IsRecordsetValidAndOpen(rsAIMClasses) Then rsAIMClasses.Close
    Set rsAIMClasses = Nothing
        
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Remove form from window list
    AIM_Main.tbAIM_Main.Tools.Remove Me.Caption

Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
        f_HandleErr , , , Me.Caption & "::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub mnuEditOpt_Click(Index As Integer)
On Error GoTo ErrorHandler

    Dim Counter As Integer
    Dim FieldName As Variant
    Dim FieldData As Variant
    
    Screen.MousePointer = vbHourglass
    
    Select Case Index
        Case 0          'Copy to Clipboard
            If f_IsRecordsetOpenAndPopulated(rsAIMClasses) Then
                Clipboard.Clear
                
                For Counter = 0 To (rsAIMClasses.Fields.Count - 1)
                    FieldName = FieldName + rsAIMClasses.Fields(Counter).Name + vbTab
                Next Counter
                
                FieldName = FieldName + vbCrLf
                    
                rsAIMClasses.MoveFirst
                FieldData = rsAIMClasses.GetString(adClipString)
                
                Clipboard.SetText FieldName + FieldData, vbCFText
            End If
            
        Case 1          'Print
            Me.dgAIMClasses.PrintData ssPrintAllRows, False, True
        
    End Select
    
    Screen.MousePointer = vbNormal

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(mnuEditOpt_Click)"
    f_HandleErr , , , Me.Caption & "::mnuEditOpt_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub tbAIMClasses_ToolClick(ByVal Tool As ActiveToolBars.SSTool)
On Error GoTo ErrorHandler

    'Navigate
    Select Case Tool.ID
        Case "ID_AddNew"
            Me.tbAIMClasses.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Add")
            dgAIMClasses.AddNew
            
        Case "ID_Delete"
            dgAIMClasses.DeleteSelected
        
        Case "ID_GetFirst"
            dgAIMClasses.MoveFirst
        
        Case "ID_GetPrev"
            dgAIMClasses.MovePrevious
        
        Case "ID_GetNext"
            dgAIMClasses.MoveNext
        
        Case "ID_GetLast"
            dgAIMClasses.MoveLast
        
        Case "ID_Save"
            dgAIMClasses.Update
        
        Case "ID_SetBookMark"
            If IsEmpty(AIMClassesBookMark) _
            Or IsNull(AIMClassesBookMark) _
            Then
                AIMClassesBookMark = dgAIMClasses.Bookmark
                Me.tbAIMClasses.Tools("ID_GoToBookMark").Enabled = True
            Else
                AIMClassesBookMark = Null
                Me.tbAIMClasses.Tools("ID_GoToBookMark").Enabled = False
            End If
            
        Case "ID_GoToBookMark"
            If Not IsEmpty(AIMClassesBookMark) _
            Or Not IsNull(AIMClassesBookMark) _
            Then
                dgAIMClasses.Bookmark = AIMClassesBookMark
            End If
        
        Case "ID_Refresh"
            FetchAIMClasses (Me.dcLangID.Text)
            
        Case "ID_Close"
            Unload Me
            Exit Sub

    End Select

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(tbAIMClasses_ToolClick)"
    f_HandleErr , , , Me.Caption & "::tbAIMClasses_ToolClick", Now, gDRGeneralError, True, Err
End Sub

Private Function FetchAIMClasses(p_langID) As Long
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim Counter As Long
    Dim RtnCode As Long
    Dim RowCount As Long
    
    'Recreate the recordset.
    RtnCode = GetClassList(Cn, "ALL", -1, gLangID, rsAIMClasses)
    
    If f_IsRecordsetValidAndOpen(rsAIMClasses) _
    And rsAIMClasses.RecordCount > 0 Then
        RowCount = rsAIMClasses.RecordCount
        'Rebind the grid
        Me.dgAIMClasses.ReBind
        Me.dgAIMClasses.Rows = RowCount
        mnuEdit.Enabled = True
    Else
        mnuEdit.Enabled = False
    End If
    
    'Adjust the language id by selection/user's language
    If Me.dgAIMClasses.Columns.Count > 0 Then
        If p_langID <> getTranslationResource("All") Then
            'Re-do dropdown to show only that language
            Me.dgAIMClasses.Columns(2).RemoveAll
            Me.dgAIMClasses.Columns(2).AddItem p_langID
        Else
            're-do dropdown to show all available languages
            Me.dgAIMClasses.Columns(2).RemoveAll
            Me.dcLangID.MoveFirst
            For Counter = 1 To Me.dcLangID.Rows
                If Me.dcLangID.Columns(0).Value <> getTranslationResource("All") Then
                    Me.dgAIMClasses.Columns(2).AddItem Me.dcLangID.Columns(0).Value
                End If
                Me.dcLangID.MoveNext
            Next
        End If
    End If

    Me.tbAIMClasses.Tools("ID_AddItem").ChangeAll ssChangeAllName, getTranslationResource("Edit")
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(FetchAIMClasses)"
     f_HandleErr , , , "AIM_ClassMaintenance::FetchAIMClasses", Now, gDRGeneralError, True, Err
End Function

Private Function PopulateLangIDs() As Long
On Error GoTo ErrorHandler

    Dim rsLangId As ADODB.Recordset
    Dim AIM_AIMLanguage_List_Sp As ADODB.Command
    Dim strYes As String

    'Build the Language List
    Set AIM_AIMLanguage_List_Sp = New ADODB.Command
    With AIM_AIMLanguage_List_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMLanguage_List_Sp"
        .Parameters.Refresh
    End With
    
    'Initialize the LangID Record Set
    Set rsLangId = New ADODB.Recordset
    With rsLangId
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    
    'Only get the enabled Languages, marked with "Y"
    strYes = "Y"
    AIM_AIMLanguage_List_Sp.Parameters("@EnabledOption").Value = strYes
    rsLangId.Open AIM_AIMLanguage_List_Sp
    
    'Bind the Data Combo Drop Downs
    Me.dcLangID.AddItem getTranslationResource("All") + _
                vbTab + getTranslationResource("All")
    
    'Get LangIDs from recordset
    If f_IsRecordsetOpenAndPopulated(rsLangId) Then
        rsLangId.MoveFirst
        Do Until rsLangId.eof
            Me.dcLangID.AddItem rsLangId("LangID").Value + vbTab + rsLangId("LangDesc").Value
            rsLangId.MoveNext
        Loop
    End If
    
    If f_IsRecordsetValidAndOpen(rsLangId) Then rsLangId.Close
    Set rsLangId = Nothing
    If Not (AIM_AIMLanguage_List_Sp Is Nothing) Then Set AIM_AIMLanguage_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLanguage_List_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetOpenAndPopulated(rsLangId) Then rsLangId.Close
    Set rsLangId = Nothing
    If AIM_AIMLanguage_List_Sp.State <> adStateOpen Then Set AIM_AIMLanguage_List_Sp.ActiveConnection = Nothing
    Set AIM_AIMLanguage_List_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(PopulateLangIDs)"
    f_HandleErr , , , "AIM_ClassMaintenance::PopulateLangIDs", Now, gDRGeneralError, True, Err
End Function

Private Function UserInputValidation(p_Scope As Integer) As Integer
On Error GoTo ErrorHandler

    Dim strMessage As String
    Dim RtnCode As Long
    
    'Set default to false
    UserInputValidation = -1
    
    If dgAIMClasses.Columns("LangID").Value = "" Then dgAIMClasses.Columns("LangID").Value = dcLangID.Text
    
    If dgAIMClasses.Columns("ClassLevel").Value > 4 _
    Or dgAIMClasses.Columns("ClassLevel").Value <= 0 _
    Then
        'ErrorMessage
        strMessage = getTranslationResource("MSGBOX06603")
        If StrComp(strMessage, "MSGBOX06603") = 0 Then _
                strMessage = "Invalid value for Class Level. Please choose from the given list."
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        dgAIMClasses.Columns("ClassLevel").Value = ""
        dgAIMClasses.Columns("ClassLevel").Text = ""
        Exit Function
    End If

    Select Case p_Scope
    Case VALIDATIONSCOPE_FIELD
        'Validation complete and successful, if reached this far.
        'Set status to success and exit
        UserInputValidation = 0
        Exit Function
        
    Case VALIDATIONSCOPE_ROW
        'Continue with the rest of the function
    End Select
    
    If dgAIMClasses.Columns("Class").Value = "" _
    Or dgAIMClasses.Columns("ClassLevel").Value = "" _
    Or dgAIMClasses.Columns("ClassDesc").Value = "" _
    Then
        'ErrorMessage
        strMessage = getTranslationResource("MSGBOX06602")
        If StrComp(strMessage, "MSGBOX06602") = 0 Then _
                strMessage = "Some required fields are missing. Please review before moving to another row."
        RtnCode = MsgBox(strMessage, vbOKOnly, getTranslationResource(Me.Caption))
        Exit Function
    End If
    
    'Set return value to success
    UserInputValidation = 0
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(UserInputValidation)"
    f_HandleErr , , , "AIM_ClassMaintenance::UserInputValidation", Now, gDRGeneralError, True, Err
End Function
