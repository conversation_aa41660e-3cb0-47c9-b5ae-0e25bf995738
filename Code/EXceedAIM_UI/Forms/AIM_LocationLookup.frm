VERSION 5.00
Object = "{49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0"; "tinumb8.ocx"
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Object = "{4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0"; "ssdw3bo.ocx"
Begin VB.Form AIM_LocationLookUp 
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA DR Location Lookup"
   ClientHeight    =   5865
   ClientLeft      =   45
   ClientTop       =   330
   ClientWidth     =   8445
   ForeColor       =   &H00C0C0C0&
   Icon            =   "AIM_LocationLookup.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   5865
   ScaleWidth      =   8445
   ShowInTaskbar   =   0   'False
   StartUpPosition =   1  'CenterOwner
   Begin VB.Frame Frame2 
      Caption         =   "Selection Criteria"
      Height          =   1650
      Left            =   120
      TabIndex        =   10
      Top             =   120
      Width           =   8325
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcLType 
         Height          =   345
         Left            =   6240
         TabIndex        =   15
         Top             =   735
         Width           =   1455
         DataFieldList   =   "Column 0"
         AllowInput      =   0   'False
         AllowNull       =   0   'False
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         Rows            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   2566
         _ExtentY        =   609
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBText6Ctl.TDBText txtLcId 
         Height          =   345
         Left            =   105
         TabIndex        =   0
         Top             =   735
         Width           =   1710
         _Version        =   65536
         _ExtentX        =   3016
         _ExtentY        =   609
         Caption         =   "AIM_LocationLookup.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_LocationLookup.frx":0376
         Key             =   "AIM_LocationLookup.frx":0394
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtLcId"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtLName 
         Height          =   345
         Left            =   2160
         TabIndex        =   1
         Top             =   735
         Width           =   3435
         _Version        =   65536
         _ExtentX        =   6059
         _ExtentY        =   609
         Caption         =   "AIM_LocationLookup.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_LocationLookup.frx":0444
         Key             =   "AIM_LocationLookup.frx":0462
         BackColor       =   -2147483643
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   0
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   1
         BorderStyle     =   1
         AlignHorizontal =   0
         AlignVertical   =   0
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   0
         LengthAsByte    =   0
         Text            =   "txtLName"
         Furigana        =   0
         HighlightText   =   -1
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin SSDataWidgets_B_OLEDB.SSOleDBCombo dcComparison 
         Height          =   345
         Left            =   120
         TabIndex        =   2
         Top             =   1200
         Width           =   3135
         DataFieldList   =   "Column 0"
         _Version        =   196617
         DataMode        =   2
         Cols            =   2
         BackColorOdd    =   12648447
         Columns(0).Width=   3200
         _ExtentX        =   5530
         _ExtentY        =   600
         _StockProps     =   93
         Text            =   "SSOleDBCombo1"
         ForeColor       =   -2147483640
         BackColor       =   -2147483643
         DataFieldToDisplay=   "Column 1"
      End
      Begin TDBNumber6Ctl.TDBNumber txtMaxRows 
         Height          =   345
         Left            =   7305
         TabIndex        =   3
         Top             =   1200
         Width           =   870
         _Version        =   65536
         _ExtentX        =   1535
         _ExtentY        =   600
         Calculator      =   "AIM_LocationLookup.frx":04A6
         Caption         =   "AIM_LocationLookup.frx":04C6
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "MS Sans Serif"
            Size            =   8.25
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_LocationLookup.frx":0532
         Keys            =   "AIM_LocationLookup.frx":0550
         Spin            =   "AIM_LocationLookup.frx":059A
         AlignHorizontal =   1
         AlignVertical   =   2
         Appearance      =   1
         BackColor       =   -2147483643
         BorderStyle     =   1
         BtnPositioning  =   1
         ClipMode        =   0
         ClearAction     =   0
         DecimalPoint    =   "."
         DisplayFormat   =   "###0;-###0;0;0"
         EditMode        =   0
         Enabled         =   -1
         ErrorBeep       =   0
         ForeColor       =   -2147483640
         Format          =   "###0"
         HighlightText   =   -1
         MarginBottom    =   3
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MaxValue        =   1000
         MinValue        =   0
         MousePointer    =   0
         MoveOnLRKey     =   0
         NegativeColor   =   255
         OLEDragMode     =   0
         OLEDropMode     =   0
         ReadOnly        =   0
         Separator       =   ""
         ShowContextMenu =   -1
         ValueVT         =   2011693061
         Value           =   200
         MaxValueVT      =   6750213
         MinValueVT      =   1479671813
      End
      Begin VB.Label Label3 
         Caption         =   "Type"
         Height          =   375
         Left            =   6240
         TabIndex        =   14
         Top             =   345
         Width           =   1455
      End
      Begin VB.Label Label8 
         Caption         =   "Maximum Rows"
         Height          =   300
         Left            =   5280
         TabIndex        =   13
         Top             =   1245
         Width           =   1935
      End
      Begin VB.Label Label2 
         Caption         =   "Location Name"
         Height          =   345
         Left            =   2160
         TabIndex        =   12
         Top             =   345
         Width           =   3435
      End
      Begin VB.Label Label1 
         Caption         =   "Location ID"
         Height          =   345
         Left            =   120
         TabIndex        =   11
         Top             =   345
         Width           =   1695
      End
   End
   Begin VB.CommandButton cmdLookup 
      Caption         =   "&Apply"
      Height          =   345
      Left            =   7032
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdCancel 
      Cancel          =   -1  'True
      Caption         =   "&Cancel"
      Height          =   345
      Left            =   3000
      Style           =   1  'Graphical
      TabIndex        =   6
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdReturn 
      Caption         =   "&Return Selected Item"
      Default         =   -1  'True
      Height          =   345
      Left            =   4440
      Style           =   1  'Graphical
      TabIndex        =   5
      Top             =   5400
      Width           =   2445
   End
   Begin VB.CommandButton cmdClear 
      Caption         =   "Cl&ear"
      Height          =   345
      Left            =   75
      Style           =   1  'Graphical
      TabIndex        =   8
      Top             =   5400
      Width           =   1365
   End
   Begin VB.CommandButton cmdPrint 
      Caption         =   "&Print"
      Height          =   345
      Left            =   1560
      Style           =   1  'Graphical
      TabIndex        =   7
      Top             =   5400
      Width           =   1365
   End
   Begin SSDataWidgets_B_OLEDB.SSOleDBGrid dgLocations 
      Height          =   3315
      Left            =   72
      TabIndex        =   9
      Top             =   1905
      Width           =   8325
      _Version        =   196617
      DataMode        =   1
      Cols            =   4
      AllowUpdate     =   0   'False
      ForeColorEven   =   0
      BackColorOdd    =   12648447
      RowHeight       =   423
      CaptionAlignment=   0
      Columns(0).Width=   3200
      _ExtentX        =   14684
      _ExtentY        =   5847
      _StockProps     =   79
      ForeColor       =   64
   End
End
Attribute VB_Name = "AIM_LocationLookUp"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Public Cn As ADODB.Connection
Public rsLocations As ADODB.Recordset
 
Public CancelFlag As Boolean
Public LcId As String
Public LType As String

Private LcIDKey_Criteria As String
Private LNameKey_Criteria As String
Private LTypeKey_Criteria As String

Private Function BuildLocationLookupQuery() As String
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim WhrStmt As String
    Dim CompareAs As String
    
    CompareAs = Space(1) & Me.dcComparison.Value & Space(1)
    If Trim$(CompareAs) = "" Then CompareAs = Space(1) & "=" & Space(1)
    
    'Initialize SQL Statment
    SqlStmt = "SELECT" & vbCrLf
    SqlStmt = SqlStmt & "   LcID, LName, LType, " & vbCrLf
    SqlStmt = SqlStmt & "   LStatus = COALESCE(LStatus,'')," & vbCrLf
    SqlStmt = SqlStmt & "   LDivision = COALESCE(Ldivision,'')," & vbCrLf
    SqlStmt = SqlStmt & "   LRegion = COALESCE(LRegion,'')," & vbCrLf
    SqlStmt = SqlStmt & "   LCity, LState," & vbCrLf
    SqlStmt = SqlStmt & "   LUserDefined = COALESCE(LUserDefined,'')" & vbCrLf
    SqlStmt = SqlStmt & "FROM AIMLocations "
    
    'Build the WHERE Statment
    WhrStmt = "WHERE"
    If Trim$(Me.txtLcId.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), "WHERE", vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & Space(1) & "LcID"
        Else
            WhrStmt = WhrStmt & "AND LcID"
        End If
        WhrStmt = WhrStmt & CompareAs & LcIDKey_Criteria & vbCrLf
    End If
        
    If Trim$(Me.txtLName.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), "WHERE", vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & Space(1) & "LName"
        Else
            WhrStmt = WhrStmt & "AND LName"
        End If
        WhrStmt = WhrStmt & CompareAs & LNameKey_Criteria & vbCrLf
            
    End If
    
    If Trim$(Me.dcLType.Text) <> "" Then
        If StrComp(Trim$(WhrStmt), "WHERE", vbTextCompare) = 0 Then
            WhrStmt = WhrStmt & Space(1) & "LType"
        Else
            WhrStmt = WhrStmt & "AND LType"
        End If
        WhrStmt = WhrStmt & CompareAs & LTypeKey_Criteria & vbCrLf
            
    End If
    If StrComp(Trim$(WhrStmt), "WHERE", vbTextCompare) = 0 Then WhrStmt = ""

    SqlStmt = SqlStmt & WhrStmt & "ORDER BY LcID"
     
    BuildLocationLookupQuery = Trim$(SqlStmt)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(BuildLocationLookupQuery)", Err.Description
     f_HandleErr , , , "AIM_LocationLookUp::BuildLocationLookupQuery", Now, gDRGeneralError, True, Err
     
End Function

Private Sub cmdLookup_Click()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    Dim strMessage As String
    
    Me.MousePointer = vbHourglass
    Write_Message ""
    
    'Force validation
    txtLcID_Validate False
    txtLName_Validate False
    dcLType_Validate False
    
    If StrComp(LcIDKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(LNameKey_Criteria, "Error", vbTextCompare) = 0 _
    Or StrComp(LTypeKey_Criteria, "Error", vbTextCompare) = 0 _
    Then
        'Do NOT Proceed
    Else
        'Create the query
        SqlStmt = BuildLocationLookupQuery()
        'Recreate the result set
        If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
        With rsLocations
            .MaxRecords = Me.txtMaxRows.Text
            .source = SqlStmt
            .Open
        End With
        If f_IsRecordsetOpenAndPopulated(rsLocations) Then
            'Refresh the grid
            Me.dgLocations.ReBind
        Else
            If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
            Me.dgLocations.Reset
        End If
    End If
    
    'Return the screen values
    Me.MousePointer = vbDefault
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdLookup_Click)"
     f_HandleErr , , , "AIM_LocationLookUp::cmdLookup_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub cmdCancel_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = True
    Unload Me
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdCancel_Click)"
     f_HandleErr , , , "AIM_LocationLookUp::cmdCancel_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub cmdClear_Click()
On Error GoTo ErrorHandler
    
    Me.txtLcId.Text = ""
    Me.txtLName.Text = ""
    Me.dcLType.Text = ""
    
    LcIDKey_Criteria = ""
    LNameKey_Criteria = ""
    LTypeKey_Criteria = ""
    
    Me.txtMaxRows.Text = 200
    
    If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
    Me.dgLocations.Reset

    Me.txtLcId.SetFocus
  
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdClear_Click)"
     f_HandleErr , , , "AIM_LocationLookUp::cmdClear_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub cmdPrint_Click()
On Error GoTo ErrorHandler

    If Me.dgLocations.Rows > 0 Then
        Screen.MousePointer = vbHourglass
        Me.dgLocations.PrintData ssPrintAllRows, False, True
        Screen.MousePointer = vbNormal
    End If
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdPrint_Click)"
     f_HandleErr , , , "AIM_LocationLookUp::cmdPrint_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub cmdReturn_Click()
On Error GoTo ErrorHandler

    Me.CancelFlag = False
    Me.LcId = Me.dgLocations.Columns("LcID").Value
    Me.LType = Me.dgLocations.Columns("LType").Value
    'Me.Status = Me.dgLocations.Columns("LStatus").Value
    Unload Me

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(cmdReturn_Click)"
     f_HandleErr , , , "AIM_LocationLookUp::cmdReturn_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcComparison_CloseUp()
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Select Case Me.dcComparison.Text
    Case "LIKE"
        Select Case Me.dcComparison.Value
        Case getTranslationResource("Begins With")
            strMessage = getTranslationResource("STATMSG03100")
            If StrComp(strMessage, "STATMSG03100") = 0 Then strMessage = "Enter either part or all of the beginning of the desired value."
            Write_Message strMessage
        Case getTranslationResource("Contains")
            strMessage = getTranslationResource("STATMSG03101")
            If StrComp(strMessage, "STATMSG03101") = 0 Then strMessage = "Enter a string expression which is contained within the desired value."
            Write_Message strMessage
        End Select
    
    Case "BETWEEN"
        strMessage = getTranslationResource("STATMSG03102")
        If StrComp(strMessage, "STATMSG03102") = 0 Then strMessage = "Enter two values between which the desired value should fall, separated by a comma."
        Write_Message strMessage
    
    Case "IN"
        strMessage = getTranslationResource("STATMSG03103")
        If StrComp(strMessage, "STATMSG03103") = 0 Then strMessage = "Enter a list of values in which the desired value should fall, separated by commas."
        Write_Message strMessage
    
    Case Else
        strMessage = getTranslationResource("STATMSG03104")
        If StrComp(strMessage, "STATMSG03104") = 0 Then strMessage = "Enter a value for comparison."
        Write_Message strMessage
    
    End Select
    
    'Reset the comparison values
    txtLcID_Validate False
    txtLName_Validate False

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_CloseUp)"
    f_HandleErr , , , "AIM_LocationLookUp::dcComparison_CloseUp", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcComparison_InitColumnProps()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    'Build Columns
    Me.dcComparison.Columns(0).Caption = getTranslationResource("Comparison")
    Me.dcComparison.Columns(0).FieldLen = 1
    Me.dcComparison.Columns(0).Width = 1000
    
    Me.dcComparison.Columns(1).Caption = getTranslationResource("Description")
    Me.dcComparison.Columns(1).FieldLen = 24
    Me.dcComparison.Columns(1).Width = 2000
    
    'Load Values
    Me.dcComparison.AddItem ("=" & Chr(9) & _
                             getTranslationResource("Equal To"))
    Me.dcComparison.AddItem (">" & Chr(9) & _
                             getTranslationResource("Greater Than"))
    Me.dcComparison.AddItem (">=" & Chr(9) & _
                             getTranslationResource("Greater Than / Equal To"))
    Me.dcComparison.AddItem ("<" & Chr(9) & _
                             getTranslationResource("Less Than"))
    Me.dcComparison.AddItem ("<=" & Chr(9) & _
                             getTranslationResource("Less Than / Equal To"))
    Me.dcComparison.AddItem ("<>" & Chr(9) & _
                             getTranslationResource("Not Equal To"))
    Me.dcComparison.AddItem ("LIKE" & Chr(9) & _
                             getTranslationResource("Begins With"))
    Me.dcComparison.AddItem ("LIKE" & Chr(9) & _
                             getTranslationResource("Contains"))
    Me.dcComparison.AddItem ("BETWEEN" & Chr(9) & _
                             getTranslationResource("Is Between These Values"))
    Me.dcComparison.AddItem ("IN" & Chr(9) & _
                             getTranslationResource("Is in this List of Values"))
                             
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcComparison, ACW_EXPAND
    End If

    For IndexCounter = 0 To dcComparison.Columns.Count - 1
'        dcComparison.Columns(IndexCounter).HasHeadBackColor = True
'        dcComparison.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
    Next

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcComparison_InitColumnProps)"
     f_HandleErr , , , "AIM_LocationLookUp::dcComparison_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLType_Change()
On Error GoTo ErrorHandler
    
    Me.dcLType.ToolTipText = Me.dcLType.Text
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLType_Change)"
     f_HandleErr , , , "AIM_LocationLookUp::dcLType_Change", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dcLType_Click()
On Error GoTo ErrorHandler
    
    Me.dcLType.ToolTipText = Me.dcLType.Text
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLType_Click)"
     f_HandleErr , , , "AIM_LocationLookUp::dcLType_Click", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub


Private Sub dcLType_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    'Set column properties
    Me.dcLType.Columns(0).Caption = getTranslationResource("Code")
    Me.dcLType.Columns(0).Alignment = ssCaptionAlignmentCenter
    Me.dcLType.Columns(0).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLType.Columns(0).Width = 720
    
    Me.dcLType.Columns(1).Caption = getTranslationResource("Description")
    Me.dcLType.Columns(1).Alignment = ssCaptionAlignmentLeft
    Me.dcLType.Columns(1).CaptionAlignment = ssColCapAlignLeftJustify
    Me.dcLType.Columns(1).Width = 2000
    
    'Formatting
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dcLType, ACW_EXPAND
    End If
    
'    'UI Standard settings  -- leave commented until further actions are defined
'    For IndexCounter = 0 To dcLType.Columns.Count - 1
'        dcLType.Columns(IndexCounter).HasHeadBackColor = True
'        dcLType.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'    Next
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLType_InitColumnProps)"
     f_HandleErr , , , "AIM_LocationLookUp::dcLType_InitColumnProps", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dgLocations_DblClick()
On Error GoTo ErrorHandler

    'Return selected item
    cmdReturn_Click

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgLocations_DblClick)"
     f_HandleErr , , , "AIM_LocationLookUp::dgLocations_DblClick", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dgLocations_InitColumnProps()
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Me.dgLocations.Redraw = False
    Me.MousePointer = vbHourglass
    
    'Item Data
    IndexCounter = 0
    Me.dgLocations.Columns(IndexCounter).Name = "LcID"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("Location ID")
    Me.dgLocations.Columns(IndexCounter).Width = 720
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LName"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("Location Name")
    Me.dgLocations.Columns(IndexCounter).Width = 2880
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LType"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("Location Type")
    Me.dgLocations.Columns(IndexCounter).Width = 600
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LStatus"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("Status")
    Me.dgLocations.Columns(IndexCounter).Width = 1000
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LDivision"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("Division")
    Me.dgLocations.Columns(IndexCounter).Width = 1000
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LRegion"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("Region")
    Me.dgLocations.Columns(IndexCounter).Width = 1000
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LCity"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("City")
    Me.dgLocations.Columns(IndexCounter).Width = 1000
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LState"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("State")
    Me.dgLocations.Columns(IndexCounter).Width = 1000
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    IndexCounter = IndexCounter + 1
    Me.dgLocations.Columns(IndexCounter).Name = "LUserDefined"
    Me.dgLocations.Columns(IndexCounter).Caption = getTranslationResource("User Defined")
    Me.dgLocations.Columns(IndexCounter).Width = 1000
    Me.dgLocations.Columns(IndexCounter).Locked = True
    
    If gGridAutoSizeOption Then
        'Added to Automatically adjust the column widths depending on the Columns Label translation size.
        AdjustColumnWidths dgLocations, ACW_EXPAND
    End If

    For IndexCounter = 0 To dgLocations.Columns.Count - 1
'        dgLocations.Columns(IndexCounter).HasHeadBackColor = True
'        dgLocations.Columns(IndexCounter).HeadBackColor = UI_GetColor(TARGET_BACKCOLOR_COLHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
        If dgLocations.Columns(IndexCounter).Locked = False Then dgLocations.Columns(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_OLEDBGRID, COLOR_TYPE_EDITABLE)
    Next

    Me.MousePointer = vbDefault
    Me.dgLocations.Redraw = True
    
Exit Sub
ErrorHandler:
    Me.MousePointer = vbDefault
    Me.dgLocations.Redraw = True
    'f_HandleErr Me.Caption & "(dgLocations_InitColumnProps)"
     f_HandleErr , , , "AIM_LocationLookUp::dgLocations_InitColumnProps", Now, gDRGeneralError, True, Err
End Sub

Private Sub dgLocations_PrintInitialize(ByVal ssPrintInfo As SSDataWidgets_B_OLEDB.ssPrintInfo)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strMessage2 As String
    
    'Set printer properties
    strMessage = getTranslationResource("RPTMSG03100")
    If StrComp(strMessage, "RPTMSG03100") = 0 Then strMessage = "Date:"
    strMessage1 = getTranslationResource("RPTMSG03101")
    If StrComp(strMessage1, "RPTMSG03101") = 0 Then strMessage = "Locations Lookup List"
    strMessage2 = getTranslationResource("RPTMSG03102")
    If StrComp(strMessage2, "RPTMSG03102") = 0 Then strMessage = "Page:"

    ssPrintInfo.PageHeader = strMessage & Format(Date, gDateFormat) & vbTab & _
                            strMessage1 & vbTab & strMessage2 & " <page number>"
    
    ssPrintInfo.Portrait = True
    ssPrintInfo.PrintHeaders = ssTopOfPage

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgLocations_PrintInitialize)"
     f_HandleErr , , , "AIM_LocationLookUp::dgLocations_PrintInitialize", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub dgLocations_UnboundReadData(ByVal RowBuf As SSDataWidgets_B_OLEDB.ssRowBuffer, StartLocation As Variant, ByVal ReadPriorRows As Boolean)
On Error GoTo ErrorHandler

    Dim r As Integer, i As Integer
    
    'Check if valid recordset
    If (Not f_IsRecordsetOpenAndPopulated(rsLocations)) Then Exit Sub
    
    If IsNull(StartLocation) Then
        If ReadPriorRows Then
            rsLocations.MoveLast
        Else
            rsLocations.MoveFirst
        End If
    Else
        rsLocations.Bookmark = StartLocation
        If ReadPriorRows Then
            rsLocations.MovePrevious
        Else
            rsLocations.MoveNext
        End If
    End If
    
    For i = 0 To RowBuf.RowCount - 1
        If rsLocations.BOF Or rsLocations.eof Then Exit For
    
        RowBuf.Value(i, 0) = rsLocations("LcID").Value
        RowBuf.Value(i, 1) = rsLocations("LName").Value
        RowBuf.Value(i, 2) = rsLocations("LStatus").Value
        RowBuf.Value(i, 3) = rsLocations("ldivision").Value
        RowBuf.Value(i, 4) = rsLocations("LRegion").Value
        RowBuf.Value(i, 5) = rsLocations("LCity").Value
        RowBuf.Value(i, 6) = rsLocations("LState").Value
        RowBuf.Value(i, 7) = rsLocations("LUserDefined").Value
        
        RowBuf.Bookmark(i) = rsLocations.Bookmark
    
        If ReadPriorRows Then
            rsLocations.MovePrevious
        Else
            rsLocations.MoveNext
        End If
    
        r = r + 1
    Next i
    
    RowBuf.RowCount = r

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dgLocations_UnboundReadData)"
     f_HandleErr , , , "AIM_LocationLookUp::dgLocations_UnboundReadData", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    'Set the default value for the Cancel Flag
    CancelFlag = False
    
    'Initialize fields
    Me.txtMaxRows.Text = 200
    
    Me.dcComparison.Text = getTranslationResource("Greater Than / Equal To")
    
    Me.txtLcId.Text = Me.LcId
    Me.txtLName.Text = ""
    Me.dcLType.Text = Me.LType
    
    LcIDKey_Criteria = BldCompVal(Me.txtLcId.Text, Me.dcComparison.Text, Me.dcComparison.Text)
    If StrComp(Trim$(LcIDKey_Criteria), "All", vbTextCompare) = 0 Then LcIDKey_Criteria = "AIMLocations.LcID"
    
    LNameKey_Criteria = BldCompVal(Me.txtLName.Text, Me.dcComparison.Text, Me.dcComparison.Text)
    If StrComp(Trim$(LNameKey_Criteria), "All", vbTextCompare) = 0 Then LNameKey_Criteria = "AIMLocations.LName"
    
    LTypeKey_Criteria = BldCompVal(Me.dcLType.Text, Me.dcComparison.Text, Me.dcComparison.Text)
    If StrComp(Trim$(LTypeKey_Criteria), "All", vbTextCompare) = 0 Then LTypeKey_Criteria = "AIMLocations.LType"
           
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(Form_Activate)"
     f_HandleErr , , , "AIM_LocationLookUp::Form_Activate", Now, gDRGeneralError, True, Err
    Me.MousePointer = vbDefault
End Sub

Private Function getLTypes() As Long
On Error GoTo ErrorHandler

    Dim rsCodeLookup As ADODB.Recordset
    Dim AIM_CodeLookup_Get_Sp As ADODB.Command
    Dim CmdParams As ADODB.Parameter
    
    'Set default to failure
    getLTypes = -1
    
    'Build/Bind the Location Type dropdown
    Set AIM_CodeLookup_Get_Sp = New ADODB.Command
    With AIM_CodeLookup_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_CodeLookup_Get_Sp"
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@CodeType", adVarWChar, adParamInput, 30)
        CmdParams.Value = g_CODETYPE_LTYPE
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        Set CmdParams = .CreateParameter("@LangID", adVarWChar, adParamInput, 10)
        CmdParams.Value = gLangID
        .Parameters.Append CmdParams
        
        Set CmdParams = Nothing
        
    End With
    
    Set rsCodeLookup = New ADODB.Recordset
    With rsCodeLookup
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        'Fetch data
        .Open AIM_CodeLookup_Get_Sp
    End With
    If f_IsRecordsetOpenAndPopulated(rsCodeLookup) Then
        rsCodeLookup.MoveFirst
        Do Until rsCodeLookup.eof
            Me.dcLType.AddItem rsCodeLookup!CodeID & vbTab & rsCodeLookup!CodeDesc
            rsCodeLookup.MoveNext
        Loop
        Me.dcLType.Text = ""
    End If
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsCodeLookup) Then rsCodeLookup.Close
    Set rsCodeLookup = Nothing
    If Not (AIM_CodeLookup_Get_Sp Is Nothing) Then Set AIM_CodeLookup_Get_Sp.ActiveConnection = Nothing
    Set AIM_CodeLookup_Get_Sp = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "(getLTypes)"
End Function


Private Function BldCompVal(CompVals As String, CompType As String, _
    CompDetail As String)
On Error GoTo ErrorHandler
    
    Dim strMessage As String
    
    Dim i As Integer, N As Integer
    Dim newtext As String
    Dim RtnStr As String
    Dim StrArray(0 To 10) As String
    
    If StrComp(Trim$(CompVals), "All", vbTextCompare) = 0 Then
        BldCompVal = CompVals
        Exit Function
    End If
    
    Select Case CompType
    Case "LIKE"
        Select Case CompDetail
            Case getTranslationResource("Begins With")
                CompVals = "'" & CompVals & "%" & "'"
            Case getTranslationResource("Contains")
                CompVals = "'%" & CompVals & "%" & "'"
        End Select
    
    Case "BETWEEN"
        N = Parse(Trim$(CompVals), ", ", "'", StrArray())
        
        If N <> 2 Then
            strMessage = getTranslationResource("MSGBOX03101")
            If StrComp(strMessage, "MSGBOX03101") = 0 Then strMessage = "Please enter two values separated by a comma"
            MsgBox strMessage, vbExclamation, _
                    getTranslationResource(Me.Caption)
                    
            BldCompVal = "Error"
            Exit Function
        Else
            CompVals = "'" & StrArray(0) & "'" & " AND " & "'" & StrArray(1) & "' "
        End If
        
    Case "IN"
        N = Parse(Trim$(CompVals), ", ", "'", StrArray())
        
        newtext = "("
        
        For i = 0 To (N - 2)
            newtext = newtext & "'" & StrArray(i) & "'" & ", "
        Next i
        
        newtext = newtext & "'" & StrArray(N - 1) & "'" & ")"
            
        CompVals = newtext
        
    Case Else
        CompVals = "'" & Trim$(CompVals) & "'"
        
    End Select
    
    BldCompVal = CompVals

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(BldCompVal)", Err.Description
End Function

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim SqlStmt As String
    
    Screen.MousePointer = vbHourglass
    
    'Initialize fields
    LcIDKey_Criteria = ""
    LNameKey_Criteria = ""
    LTypeKey_Criteria = ""
    
    Me.txtLcId.Text = Me.LcId
    Me.txtLName.Text = ""
    Me.dcLType.Text = Me.LType
    
    Me.txtMaxRows.Text = 200
        
    'Build initial record set
    Set rsLocations = New ADODB.Recordset
    'SqlStmt = BuildLocationLookupQuery
    With rsLocations
        Set .ActiveConnection = Cn
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    getLTypes
    
    'Make the spin button visible
    Me.txtMaxRows.Spin.Visible = 1
    
    'Bind the grid
    Me.dgLocations.ReBind

    GetTranslatedCaptions Me

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "Form_Load"
     f_HandleErr , , , "AIM_LocationLookUp::Form_Load", Now, gDRGeneralError, True, Err
End Sub


Private Sub Form_Unload(Cancel As Integer)
On Error GoTo ErrorHandler

    If f_IsRecordsetValidAndOpen(rsLocations) Then rsLocations.Close
    Set rsLocations = Nothing
    
Exit Sub
ErrorHandler:
    If Err.Number = 3219 Then
        'Ignore
    Else
        'f_HandleErr Me.Caption & "(Form_Unload)"
        f_HandleErr , , , "AIM_LocationLookUp::Form_Unload", Now, gDRGeneralError, True, Err
    End If
    Resume Next
End Sub

Private Sub txtLcID_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(txtLcId.Text) = "" Then
        Exit Sub
    End If
    
    LcIDKey_Criteria = BldCompVal(Me.txtLcId.Text, Me.dcComparison.Text, Me.dcComparison.Text)
    If StrComp(Trim$(LcIDKey_Criteria), "All", vbTextCompare) = 0 Then LcIDKey_Criteria = "AIMLocations.LcID"
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtLcID_Validate)"
    f_HandleErr , , , "AIM_LocationLookUp::txtLcID_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtLName_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler

    If Trim$(txtLName.Text) = "" Then
        Exit Sub
    End If
    
    LNameKey_Criteria = BldCompVal(Me.txtLName, Me.dcComparison.Text, Me.dcComparison.Text)
    If StrComp(Trim$(LNameKey_Criteria), "All", vbTextCompare) = 0 Then LNameKey_Criteria = "AIMLocations.LName"
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtLName_Validate)"
    f_HandleErr , , , "AIM_LocationLookUp::txtLName_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub dcLType_Validate(Cancel As Boolean)
On Error GoTo ErrorHandler
    
    If Trim$(dcLType.Text) = "" Then
        Exit Sub
    End If
    
    LTypeKey_Criteria = BldCompVal(Me.dcLType.Text, Me.dcComparison.Text, Me.dcComparison.Text)
    If StrComp(Trim$(LTypeKey_Criteria), "All", vbTextCompare) = 0 Then LTypeKey_Criteria = "AIMLocations.LType"
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(dcLType_Validate)"
     f_HandleErr , , , "AIM_LocationLookUp::dcLType_Validate", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_GotFocus()
On Error GoTo ErrorHandler

    Me.txtMaxRows.SelLength = Len(Me.txtMaxRows)
    
Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_GotFocus)"
     f_HandleErr , , , "AIM_LocationLookUp::txtMaxRows_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtMaxRows_LostFocus()
On Error GoTo ErrorHandler

    If IsNumeric(txtMaxRows) Then
        txtMaxRows = Format(txtMaxRows, "#,##0")
    ElseIf Len(txtMaxRows) = 0 Then
        txtMaxRows = Format(0, "#,##0")
    Else
        Beep
        txtMaxRows.SetFocus
    End If

Exit Sub
ErrorHandler:
    'f_HandleErr Me.Caption & "(txtMaxRows_LostFocus)"
     f_HandleErr , , , "AIM_LocationLookUp::txtMaxRows_LostFocus", Now, gDRGeneralError, True, Err
End Sub
