VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "Criteria"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'************************************************************
' Used with Criterias.cls
'************************************************************

Option Explicit

Public Qualifier As String
Public ColName As String
Public Numeric As Boolean
Public NotOpt As Boolean
Public CompOper As String
Public CompValue1 As String
Public CompValue2 As String
