VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "AIMAdvCalendar"
Attribute VB_GlobalNameSpace = True
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Attribute VB_Ext_KEY = "SavedWithClassBuilder6" ,"Yes"
Attribute VB_Ext_KEY = "Top_Level" ,"Yes"
Option Explicit

'Constants
Const xFISCALYEAR = 1
Const xFYSTARTDATE = 2
Const xFYENDDATE = 3
Const xNBRWEEKS = 4
Const xNBRDAYS = 5

'Private Variables
Private m_EndDate As Date
Private m_StartDate As Date
Private m_NbrDays As Integer
Private m_NbrYears As Integer

'Collections
Private m_AIMYears As XArrayDB
Private m_AIMDays As XArrayDB

'************************************************************
' CLASS INITIALIZE/TERMINATE
'************************************************************
Private Sub Class_Initialize()
On Error GoTo ErrorHandler

    Set m_AIMDays = New XArrayDB
    Set m_AIMYears = New XArrayDB
    
    'Dimension Days Array
    '1 = date
    '2 = daystatus (0=non-working day and 1=working day)
    m_AIMDays.ReDim 0, 0, 1, 2
    
    'AIMYears
    '1 = Fiscal Year, Integer
    '2 = Fiscal Year Start Date, Date
    '3 = Fiscal Year End Date, Date
    '4 = Number of Weeks, Integer
    '5 = Number of Days, Integer
    m_AIMYears.ReDim 0, 0, 1, 5
    
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "AIMAdvCalendar.Class_Initialize"
     f_HandleErr , , , "AIMAdvCalendar::Class_Initialize", Now, gDRGeneralError, True, Err
End Sub

Private Sub Class_Terminate()
On Error GoTo ErrorHandler

    Set m_AIMDays = Nothing
    Set m_AIMYears = Nothing
    
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "AIMAdvCalendar_Load.Class_Terminate"
     f_HandleErr , , , "AIMAdvCalendar::Class_Terminate", Now, gDRGeneralError, True, Err
    
End Sub

'************************************************************
' PROPERTIES
'************************************************************
Public Property Get StartDate() As Date
On Error GoTo ErrorHandler

    StartDate = m_StartDate
    
Exit Property
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.StartDate (Get))"
     f_HandleErr , , , "AIMAdvCalendar::StartDate (Get)", Now, gDRGeneralError, True, Err
End Property

Public Property Get EndDate() As Date
On Error GoTo ErrorHandler

    EndDate = m_EndDate

Exit Property
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.EndDate (Get))"
     f_HandleErr , , , "AIMAdvCalendar::EndDate (Get)", Now, gDRGeneralError, True, Err
End Property

Public Property Get NbrDays() As Integer
On Error GoTo ErrorHandler
    
    NbrDays = m_NbrDays

Exit Property
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.NbrDays(Get))"
     f_HandleErr , , , "AIMAdvCalendar::NbrDays(Get)", Now, gDRGeneralError, True, Err
End Property

Public Property Get NbrYears() As Integer
On Error GoTo ErrorHandler
    
    NbrYears = m_NbrYears

Exit Property
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.NbrYears (Get))"
     f_HandleErr , , , "AIMAdvCalendar::NbrYears (Get)", Now, gDRGeneralError, True, Err
End Property

'************************************************************
' PROCEDURES (PUBLIC)
'************************************************************
Public Function AddYear( _
    FiscalYear As Integer, _
    FYStartDate As Date, _
    FYEndDate As Date _
) As Boolean
'Called by AIMForecast.bas -- AIMAdvCalendar_Load()
On Error GoTo ErrorHandler

    Dim CurDay As Date
    Dim NbrDays As Integer
    Dim NbrWeeks As Integer
    Dim p As Integer
    
    'Check for an existing Year
    If GetFiscalYearIndex(FYStartDate) <> FAIL Then
        AddYear = False
        Exit Function
    Else
        'Add Year
        m_AIMYears.AppendRows
        p = m_AIMYears.UpperBound(1)
        
        m_AIMYears.Value(p, xFISCALYEAR) = FiscalYear
        m_AIMYears.Value(p, xFYSTARTDATE) = FYStartDate
        m_AIMYears.Value(p, xFYENDDATE) = FYEndDate
        m_AIMYears.Value(p, xNBRWEEKS) = 52
        
        m_NbrYears = m_AIMYears.Count(1)
        
        'Add Days for Year
        CurDay = FYStartDate
        Do Until CurDay > FYEndDate
            'Increment the Day Count
            NbrDays = NbrDays + 1
            'Add a row to the x array
            m_AIMDays.AppendRows
            m_AIMDays.Value(m_AIMDays.UpperBound(1), 1) = CurDay
            
            'Determine the default working day value
            Select Case DatePart("w", CurDay)
                Case 1, 7
                    m_AIMDays.Value(m_AIMDays.UpperBound(1), 2) = 0
                Case 2 To 6
                    m_AIMDays.Value(m_AIMDays.UpperBound(1), 2) = 1
            End Select
            
            'Increment the Current Day
            CurDay = CurDay + 1
        Loop
        
        'Update the Number of Days in the Year
        m_AIMYears.Value(p, xNBRDAYS) = NbrDays
    End If
    
    'Sort the years array
    'm_AIMYears.QuickSort xFISCALYEAR, xFISCALYEAR
    
    f_SetClassProperties

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.AddYear)"
     f_HandleErr , , , "AIMAdvCalendar::AddYear", Now, gDRGeneralError, True, Err
End Function

Public Function DeleteYear( _
    FiscalYear As Integer _
)
'Called by...
On Error GoTo ErrorHandler

    Dim EndDate As Date
    Dim p As Integer
    Dim StartDate As Date
    
    'Check for an existing Year
    p = f_FYExists(FiscalYear)
    
    If p = 0 Then
        DeleteYear = False
        Exit Function
    Else
        'Get the Fiscal Year Start and End Dates
        StartDate = m_AIMYears.Value(p, xFYSTARTDATE)
        EndDate = m_AIMYears.Value(p, xFYENDDATE)
        
        'Drop the Year
        m_AIMYears.DeleteRows p
        
        'Delete associated days
        For p = 1 To m_AIMDays.UpperBound(1)
            If m_AIMDays.Value(p, 1) >= StartDate _
            And m_AIMDays.Value(p, 1) <= EndDate _
            Then
                m_AIMDays.DeleteRows p
            End If
        Next p
            
        f_SetClassProperties
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.DeleteYear)"
     f_HandleErr , , , "AIMAdvCalendar::DeleteYear", Now, gDRGeneralError, True, Err
End Function

Public Function SetDayStatus( _
    FYDate As Date, _
    DayStatus As Integer _
) As Boolean
'Called in AIMForecast.bas -- AIMAdvCalendar_Load
On Error GoTo ErrorHandler

    Dim FDKey As Integer
    
    FDKey = GetDayPointer(FYDate)
    
    If FDKey = 0 Then
        SetDayStatus = FAIL
    Else
        m_AIMDays.Value(FDKey, 2) = DayStatus
        SetDayStatus = SUCCEED
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.SetDayStatus)"
     f_HandleErr , , , "AIMAdvCalendar::SetDayStatus", Now, gDRGeneralError, True, Err
End Function

Private Function GetDayPointer( _
    FYDate As Date _
) As Integer
On Error GoTo ErrorHandler

    If FYDate < Me.StartDate Or FYDate > Me.EndDate Then
        GetDayPointer = 0
    Else
        GetDayPointer = DateDiff("d", Me.StartDate, FYDate) + 1
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetDayPointer)"
     f_HandleErr , , , "AIMAdvCalendar::GetDayPointer", Now, gDRGeneralError, True, Err
End Function

Public Function GetDayStatus( _
    TargetDate As Date _
) As Double
On Error GoTo ErrorHandler

    Dim FDKey As Integer

    'Find the date
    FDKey = GetDayPointer(TargetDate)

    If FDKey > 0 Then
        GetDayStatus = m_AIMDays.Value(FDKey, 2)
    Else
        GetDayStatus = 0
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetDayStatus)"
     f_HandleErr , , , "AIMAdvCalendar::GetDayStatus", Now, gDRGeneralError, True, Err
End Function

Public Function GetEndDate( _
    TargetDate As Date _
) As Date
On Error GoTo ErrorHandler

    Dim Fy As Integer
    Dim Pd As Integer
    Dim Row As Integer
    
    'Find the date
    Fy = GetFiscalYearIndex(TargetDate)
    
    If Fy > 0 Then
        Pd = (DateDiff("d", m_AIMYears.Value(Fy, xFYSTARTDATE), TargetDate) \ 7) + 1
        Pd = IIf(Pd > 52, 52, Pd)
        
        Select Case Pd
            Case 1
                GetEndDate = m_AIMYears.Value(Fy, xFYSTARTDATE) + 6
            Case 2 To 51
                GetEndDate = DateAdd("d", (Pd * 7) - 1, m_AIMYears.Value(Fy, xFYSTARTDATE))
            Case 52
                GetEndDate = m_AIMYears.Value(Fy, xFYENDDATE)
        End Select
        
    Else
        GetEndDate = FAIL
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetEndDate)"
    f_HandleErr , , , "AIMAdvCalendar::GetEndDate", Now, gDRGeneralError, True, Err
End Function

Public Function GetPeriod( _
    TargetDate As Date _
) As Integer
On Error GoTo ErrorHandler
'see AIMCalendar.frm -- function GetFiscalPeriod
    Dim ay As Integer
    
    ay = GetFiscalYearIndex(TargetDate)
    
    If ay > 0 Then
        GetPeriod = (DateDiff("d", m_AIMYears.Value(ay, xFYSTARTDATE), TargetDate) \ 7) + 1
        GetPeriod = IIf(GetPeriod > 52, 52, GetPeriod)
    Else
        GetPeriod = FAIL
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetPeriod)"
     f_HandleErr , , , "AIMAdvCalendar::GetPeriod", Now, gDRGeneralError, True, Err
End Function

Private Function GetFiscalYearIndex( _
    TargetDate As Date _
) As Integer
On Error GoTo ErrorHandler

    Dim i As Integer
    
    For i = m_AIMYears.LowerBound(1) To m_AIMYears.UpperBound(1)
        If TargetDate >= m_AIMYears.Value(i, xFYSTARTDATE) And TargetDate <= m_AIMYears.Value(i, xFYENDDATE) Then
            GetFiscalYearIndex = i
            Exit Function
        End If
    Next i
    
    'Target date not found
    GetFiscalYearIndex = 0

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetFiscalYearIndex)"
     f_HandleErr , , , "AIMAdvCalendar::GetFiscalYearIndex", Now, gDRGeneralError, True, Err
End Function
Public Function GetFiscalYear( _
    TargetDate As Date _
) As Integer
On Error GoTo ErrorHandler

    Dim i As Integer
    
    For i = m_AIMYears.LowerBound(1) To m_AIMYears.UpperBound(1)
        If TargetDate >= m_AIMYears.Value(i, xFYSTARTDATE) And TargetDate <= m_AIMYears.Value(i, xFYENDDATE) Then
            GetFiscalYear = m_AIMYears.Value(i, xFISCALYEAR)
            
            Exit Function
        End If
    Next i
    
    'Target date not found
    GetFiscalYear = 0

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetFiscalYear)"
     f_HandleErr , , , "AIMAdvCalendar::GetFiscalYear", Now, gDRGeneralError, True, Err
End Function
Public Function GetFiscalYearStartDate( _
    FyYear As Integer _
) As Date

On Error GoTo ErrorHandler

    Dim i As Integer
    
    For i = m_AIMYears.LowerBound(1) To m_AIMYears.UpperBound(1)
        If m_AIMYears.Value(i, xFISCALYEAR) = FyYear Then
            GetFiscalYearStartDate = m_AIMYears.Value(i, xFYSTARTDATE)
            Exit Function
        End If
    Next i
    
    'Target date not found
    GetFiscalYearStartDate = #1/1/1900#

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetFiscalYearStartDate)"
     f_HandleErr , , , "AIMAdvCalendar::GetFiscalYearStartDate", Now, gDRGeneralError, True, Err
End Function

Public Function GetFiscalYearEndDate( _
    FyYear As Integer _
) As Date


On Error GoTo ErrorHandler

    Dim i As Integer
    
    For i = m_AIMYears.LowerBound(1) To m_AIMYears.UpperBound(1)
        If m_AIMYears.Value(i, xFISCALYEAR) = FyYear Then
            GetFiscalYearEndDate = m_AIMYears.Value(i, xFYENDDATE)
            Exit Function
        End If
    Next i
    
    'Target date not found
    GetFiscalYearEndDate = #1/1/1900#

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetFiscalYearEndDate)"
     f_HandleErr , , , "AIMAdvCalendar::GetFiscalYearEndDate", Now, gDRGeneralError, True, Err
End Function
Public Function GetEndDateFmPeriod( _
    TargetPeriod As Integer, _
    FiscalYear As Integer _
) As Date
On Error GoTo ErrorHandler
    
    Dim ay As Integer
     
    'Find the date
    ay = f_FYExists(FiscalYear)
    
    If ay > 0 Then
        Select Case TargetPeriod
            Case 1
                GetEndDateFmPeriod = m_AIMYears.Value(ay, xFYSTARTDATE) + 6
            Case 2 To 51
                GetEndDateFmPeriod = DateAdd("d", (TargetPeriod * 7) - 1, m_AIMYears.Value(ay, xFYSTARTDATE))
            Case 52
                GetEndDateFmPeriod = m_AIMYears.Value(ay, xFYENDDATE)
        End Select
        
        Exit Function
    Else
        GetEndDateFmPeriod = FAIL
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetEndDateFmPeriod)"
     f_HandleErr , , , "AIMAdvCalendar::GetEndDateFmPeriod", Now, gDRGeneralError, True, Err
End Function

Public Function GetStartDateFmPeriod( _
    TargetPeriod As Integer, _
    FiscalYear As Integer _
) As Date
On Error GoTo ErrorHandler

    Dim ay As Integer
    
    ay = f_FYExists(FiscalYear)
    
    'Find the date
    If ay > 0 Then
        GetStartDateFmPeriod = DateAdd("ww", TargetPeriod, m_AIMYears(ay, xFYSTARTDATE)) - 7
    Else
        GetStartDateFmPeriod = FAIL
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetStartDateFmPeriod)"
     f_HandleErr , , , "AIMAdvCalendar::GetStartDateFmPeriod", Now, gDRGeneralError, True, Err
End Function

Public Function GetStartDateFmDate( _
    TargetDate As Date _
) As Date
On Error GoTo ErrorHandler

    Dim Fy As Integer
    Dim Pd As Integer
    
    'Find the date
    Fy = GetFiscalYearIndex(TargetDate)
    
    If Fy > 0 Then
        Pd = (DateDiff("d", m_AIMYears.Value(Fy, xFYSTARTDATE), TargetDate) \ 7) + 1
        Pd = IIf(Pd > 52, 52, Pd)
        
        GetStartDateFmDate = DateAdd("ww", Pd, m_AIMYears.Value(Fy, xFYSTARTDATE)) - 7
    Else
        GetStartDateFmDate = 0
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetStartDateFmDate)"
    f_HandleErr , , , "AIMAdvCalendar::GetStartDateFmDate", Now, gDRGeneralError, True, Err
End Function

Public Function GetWorkingDays( _
    StartDate As Date, _
    EndDate As Date _
) As Integer
On Error GoTo ErrorHandler
    
    Dim FDKey1 As Integer
    Dim FDKey2 As Integer
    Dim p As Integer
    Dim WorkingDays  As Integer
    
    'On Error Resume Next
    
    'Check for error condition
    If StartDate > EndDate Or StartDate < m_StartDate Or EndDate > m_EndDate Then
        GetWorkingDays = 0
        Exit Function
    End If
    
    'Build the Fiscal Day Keys
    FDKey1 = GetDayPointer(StartDate)
    FDKey2 = GetDayPointer(EndDate)
    
    'Position to the Start Date
    For p = FDKey1 To FDKey2
        WorkingDays = WorkingDays + m_AIMDays.Value(p, 2)
    Next p

    GetWorkingDays = WorkingDays

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetWorkingDays)"
    f_HandleErr , , , "AIMAdvCalendar::GetWorkingDays", Now, gDRGeneralError, True, Err
End Function

Public Function GetWorkingDaysInWeek( _
    TargetDate As Date _
) As Integer
On Error GoTo ErrorHandler

    Dim EndDate As Date
    Dim StartDate As Date
        
    StartDate = GetStartDateFmDate(TargetDate)
    EndDate = GetEndDate(TargetDate)
        
    GetWorkingDaysInWeek = GetWorkingDays(StartDate, EndDate)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.GetWorkingDaysInWeek)"
     f_HandleErr , , , "AIMAdvCalendar::GetWorkingDaysInWeek", Now, gDRGeneralError, True, Err
End Function

'************************************************************
' PROCEDURES (PRIVATE)
'************************************************************
Private Function f_SetClassProperties()
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer

    'Reset Properties
    m_StartDate = 0
    m_EndDate = 0
    m_NbrDays = 0
    m_NbrYears = 0
    
    If m_AIMYears.Count(1) = 0 Then
        Exit Function
    Else
        m_NbrYears = m_AIMYears.Count(1)
        
        m_StartDate = 0
        For IndexCounter = m_AIMYears.LowerBound(1) To m_AIMYears.UpperBound(1)
            If m_AIMYears.Value(IndexCounter, xFYSTARTDATE) < m_StartDate _
            Or m_StartDate = 0 _
            Then
                m_StartDate = m_AIMYears.Value(IndexCounter, xFYSTARTDATE)
            End If
        
            If m_AIMYears.Value(IndexCounter, xFYENDDATE) > m_EndDate Then
                m_EndDate = m_AIMYears.Value(IndexCounter, xFYENDDATE)
            End If
            
            m_NbrDays = m_NbrDays + m_AIMYears.Value(IndexCounter, xNBRDAYS)
        Next IndexCounter
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.f_SetClassProperties)"
     f_HandleErr , , , "AIMAdvCalendar::f_SetClassProperties", Now, gDRGeneralError, True, Err
End Function

Private Function f_FYExists( _
    FiscalYear As Integer _
) As Integer
On Error GoTo ErrorHandler

    Dim IndexCounter As Integer

    If m_AIMYears.Count(1) = 0 Then
        f_FYExists = 0
        Exit Function
    End If
    
    For IndexCounter = m_AIMYears.LowerBound(1) To m_AIMYears.UpperBound(1)
        If FiscalYear = m_AIMYears.Value(IndexCounter, xFISCALYEAR) Then
            f_FYExists = IndexCounter
            Exit Function
        End If
    Next IndexCounter
    
    'Not Found
    f_FYExists = 0

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvCalendar.f_FYExists)"
     f_HandleErr , , , "AIMAdvCalendar::f_FYExists", Now, gDRGeneralError, True, Err
End Function


