Type=Exe
Reference=*\G{00020430-0000-0000-C000-000000000046}#2.0#0#C:\WINNT\system32\STDOLE2.TLB#OLE Automation
Reference=*\G{********-BC64-4F16-84D4-A4BE059B4927}#2.0#0#C:\Program Files\Common Files\data dynamics\activereports 2\actrpt2.dll#Data Dynamics ActiveReports 2.0
Object={A45D986F-3AAF-4A3B-A003-A6C53E8715A2}#1.0#0; ARVIEW2.OCX
Reference=*\G{A31CC8EF-A0BE-11D2-ACA7-00C04FA372BC}#1.0#0#C:\Program Files\Common Files\data dynamics\activereports 2\exclexpt.dll#ActiveReports Excel Export Filter
Reference=*\G{013A8BB0-A25B-11D1-8BB6-00A0C98CD92B}#1.0#0#C:\Program Files\Common Files\data dynamics\activereports 2\pdfexpt.dll#ActiveReports PDF Export Filter
Reference=*\G{22408F71-BAE4-11D1-8C04-82E667000000}#1.0#0#C:\Program Files\Common Files\data dynamics\activereports 2\textexpt.dll#ActiveReports Text Export Filter
Reference=*\G{C0483F01-BAE3-11D1-8C04-82E667000000}#1.0#0#C:\Program Files\Common Files\data dynamics\activereports 2\rtfexpt.dll#ActiveReports RTF Export Filter 2.0
Object={49CBFCC0-1337-11D2-9BBF-00A024695830}#1.0#0; tinumb8.ocx
Object={82392BA0-C18D-11D2-B0EA-00A024695830}#1.0#0; ticaldr8.ocx
Object={A49CE0E0-C0F9-11D2-B0EA-00A024695830}#1.0#0; tidate8.ocx
Object={E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0; titext8.ocx
Reference=*\G{6B263850-900B-11D0-9484-00A0C91110ED}#1.0#0#C:\WINNT\system32\MSSTDFMT.DLL#Microsoft Data Formatting Object Library 6.0 (SP4)
Object={66A5AC41-25A9-11D2-9BBF-00A024695830}#1.0#0; titime8.ocx
Reference=*\G{2A75196C-D9EB-4129-B803-931327F72D5C}#2.8#0#C:\Program Files\Common Files\system\ado\msado15.dll#Microsoft ActiveX Data Objects 2.8 Library
Reference=*\G{EAB22AC0-30C1-11CF-A7EB-0000C05BAE0B}#1.1#0#C:\WINNT\System32\shdocvw.dll#Microsoft Internet Controls
Reference=*\G{C10EF3FA-DEBF-4189-8859-C35CA400BBA8}#8.0#0#C:\WINNT\system32\xadb8.ocx#ComponentOne XArrayDB 8.0 Object
Object={E8671A8B-E5DD-11CD-836C-0000C0C14E92}#1.0#0; SSCALA32.OCX
Object={831FDD16-0C5C-11D2-A9FC-0000F8754DA1}#2.0#0; MSCOMCTL.OCX
Object={4A4AA691-3E6F-11D2-822F-00104B9E07A1}#3.0#0; ssdw3bo.ocx
Object={0A45DB48-BD0D-11D2-8D14-00104B9E072A}#2.0#0; sstabs2.ocx
Object={1BE65FA0-CBF9-11D2-BBC7-00104B9E0792}#2.0#0; sstbars2.ocx
Object={0BE3824E-5AFE-4B11-A6BC-4B3AD564982A}#8.0#0; olch2x8.ocx
Object={F9043C88-F6F2-101A-A3C9-08002B2F49FB}#1.2#0; COMDLG32.OCX
Object={562E3E04-2C31-4ECE-83F4-4017EEE51D40}#8.0#0; todg8.ocx
Class=Criteria; Criteria.cls
Class=clsTables; clsTables.cls
Class=clsTable; clsTable.cls
Class=Criterias; Criterias.cls
Module=AIMSQLDMO; AIMSQLDMO.bas
Module=AIMCalendar; ..\Common Files\AIMCalendar.bas
Class=AIMAdvCalendar; AIMAdvCalendar.cls
Module=AIMMainFunctions; AIMMainFunctions.bas
Form=Forms\AIM_OptionsLookUp.frm
Form=Forms\AIM_PromotionsLookUp.frm
Form=Forms\AIM_RevCycleLookUp.frm
Form=Forms\AIM_SeasonalityProfileLookup.frm
Form=Forms\AIM_SelectionCriteria.frm
Form=Forms\AIM_Specials.frm
Form=Forms\AIM_TextExportOptions.frm
Form=Forms\AIM_ItemLookup.frm
Form=Forms\AIM_DxPO.frm
Form=Forms\AIM_InitializeNewLocation.frm
Form=Forms\AIM_ForecastOverride.frm
Form=Forms\AIM_UserAlerts.frm
Form=Forms\AIM_VendorMaintenance.frm
Form=Forms\AIM_VendorSizing.frm
Form=Forms\AIM_VendorReport.frm
Form=Forms\AIM_SeasonalityProfiles.frm
Form=Forms\AIM_BuyerReviewReport.frm
Form=Forms\AIM_ItemMaintenance.frm
Module=AIMUtil; ..\Common Files\AIMUtil.bas
Form=Forms\AIM_UsersLookUp.frm
Form=Forms\AIM_VendorLookup.frm
Form=Forms\AIM_SuggestedReviewCycles.frm
Form=Forms\AIM_VelocityCodePercentages.frm
Form=Forms\AIM_JobScheduleMaintenance.frm
Form=Forms\AIM_JobHistory.frm
Form=Forms\AIM_JobMaintenance.frm
Form=Forms\AIM_JobStepMaintenance.frm
Form=Forms\AIM_OptionsMaintenance.frm
Designer=ReportObjects\AIM_BuyerReviewWorkSheet.Dsr
Designer=ReportObjects\AIM_ItemDetail_Report.Dsr
Designer=ReportObjects\AIM_ItemException_Report.Dsr
Designer=ReportObjects\AIM_ItemSummary_Report.Dsr
Designer=ReportObjects\AIM_Promotions_Report.Dsr
Designer=ReportObjects\AIM_StockStatusReport.Dsr
Designer=ReportObjects\AIM_Vendors_Report.Dsr
Form=Forms\AIM_OptionsReport.frm
Designer=ReportObjects\AIM_Transfer_Report.Dsr
Designer=ReportObjects\AIM_Locations_Report.Dsr
Form=Forms\AIM_LocationsReport.frm
Designer=ReportObjects\AIM_Options_Report.Dsr
Form=Forms\AIM_ReviewCycleReport.frm
Form=Forms\AIM_PromotionsMaintenance.frm
Form=Forms\AIM_PromotionsReport.frm
Form=Forms\AIM_ReleasedPurchaseOrders.frm
Form=Forms\AIM_ReleaseUnorderedItems.frm
Form=Forms\AIM_ReviewCycleMaintenance.frm
Designer=ReportObjects\AIM_ReveiwCycle.Dsr
Designer=ReportObjects\AIM_SeasonalityProfiles_Report.Dsr
Form=Forms\AIM_TxnLookUp.frm
Form=Forms\AIM_StockStatus.frm
Form=Forms\AIM_ForecastSimulator.frm
Form=Forms\AIM_BuyerStatus.frm
Form=Forms\AIM_Reports.frm
Form=Forms\AIM_ItemStatusMaintenance.frm
Form=Forms\AIM_ClassMaintenance.frm
Form=Forms\AIM_ItDefaultsMaintenance.frm
Form=Forms\AIM_ForecastMethodsMaintenance.frm
Form=Forms\AIM_ProductionConstraints.frm
Form=Forms\AIM_Calendar.frm
Form=Forms\AIM_IntermittentAnalysis.frm
Form=Forms\AIM_ItemExceptions.frm
Form=Forms\AIM_ItemSummary.frm
Form=Forms\AIM_SeasonalityProfileReport.frm
Class=SQLBuilder; SQLStmt.cls
Module=AIMUIStandards; ..\Common Files\AIMUIStandards.bas
Form=Forms\AIM_JobScheduler.frm
Form=Forms\AIM_SysCtrlMaintenance.frm
Form=Forms\AIM_AllocationManager.frm
Form=Forms\AIM_ItemHistoryCopy.frm
Form=Forms\AIM_AddTransferStockItem.frm
Form=Forms\AIM_DXAO.frm
Form=Forms\AIM_SAChangepassword.frm
Form=Forms\AIM_UsersPassword.frm
Form=Forms\AIM_DestinationDefault.frm
Form=Forms\AIM_TransferManagement.frm
Form=Forms\AIM_OrderGeneration.frm
Form=Forms\AIM_Companion_Item_Maintenance.frm
Form=Forms\AIM_LocationsMaintenance.frm
Form=Forms\AIM_LocationLookup.frm
Form=Forms\AIM_RolesMaintenance.frm
Form=Forms\AIM_UsersMaintenance.frm
Module=AIMInclude; ..\Common Files\AIMInclude.bas
Module=AIM_Help; AIM_Help.bas
Module=AIMTranslator; ..\Common Files\AIMTranslator.bas
Module=AIM_DBIO; ..\Common Files\AIM_DBIO.bas
Module=AIMFcstPlanner; ..\Common Files\AIMFcstPlanner.bas
Class=AIMAdvForecast; AIMAdvForecast.cls
Form=Forms\AIM_JobLog.frm
Form=Forms\AIM_ItemHistoryAdjustment.frm
Module=AIMInternationalize; ..\Common Files\AIM_Internationalize.bas
Module=AIMFcstFilterCrit; ..\Common Files\AIMFcstFilterCrit.bas
Form=Forms\AIM_ForecastSetup.frm
Form=Forms\AIM_ForecastAdjustments.frm
Form=Forms\AIM_ForecastUserElements.frm
Form=Forms\AIM_ForecastPlanner.frm
Form=Forms\AIM_ForecastFilter.frm
Form=Forms\AIM_HistoryAdjReason.frm
Module=AIMAllocationModule; ..\Common Files\AIMAllocationModule.bas
Form=..\Common Files\LOGON.frm
Form=Forms\AIM_ItemBatchSave.frm
Form=Forms\AIM_LocationFilter.frm
Form=Forms\AIM_BuyerReview.frm
Form=Forms\AIM_JobWizard.frm
Form=Forms\AIM_ErrorFilter.frm
Form=Forms\AIM_ErrorLookUp.frm
Form=Forms\AIM_Main.frm
Form=..\Common Files\AIM_About.frm
ResFile32="..\Common Files\AIMResources.res"
Startup="Sub Main"
HelpFile=""
Title="SSA_DR"
ExeName32="SSA_DR.exe"
Path32=".."
Command32=""
Name="SSA_DR"
HelpContextID="0"
Description="SSA Distribution Replenishment"
CompatibleMode="0"
MajorVer=4
MinorVer=6
RevisionVer=55
AutoIncrementVer=1
ServerSupportFiles=0
VersionComments="SSA DR (Distribution Replenishment) user-interface"
VersionCompanyName="SSA Global"
VersionFileDescription="SSA DR (Distribution Replenishment) user-interface"
VersionLegalCopyright="(c) Copyright  2004 SSA Global Technologies, Inc."
VersionLegalTrademarks="SSA Distribution Replenishment; SSA DR"
VersionProductName="SSA DR (Distribution Replenishment)"
CompilationType=0
OptimizationType=0
FavorPentiumPro(tm)=-1
CodeViewDebugInfo=0
NoAliasing=0
BoundsCheck=0
OverflowCheck=0
FlPointCheck=0
FDIVCheck=0
UnroundedFP=0
StartMode=0
Unattended=0
Retained=0
ThreadPerObject=0
MaxNumberOfThreads=1
DebugStartupOption=0

[MS Transaction Server]
AutoRefresh=1

[RVB]
DeleteClass1=LOGON
DeleteClass2=AIM_About
DeleteClass3=AIMCalendar
DeleteClass4=AIMInclude
DeleteClass5=AIMUtil
DeleteClass6=Form1
