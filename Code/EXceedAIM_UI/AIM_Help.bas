Attribute VB_Name = "AIM_Help"
'***Created by Sujit 10/23/2003 To call web based HTML help
Option Explicit
Public objIEHelp As InternetExplorer

Public Function Popup_Help(strForm As String, strControl As String, Cn As ADODB.Connection)
    On Error GoTo ErrorHandler
    
    Dim strFilePath As String
    Dim rstHelp As ADODB.Recordset
    Dim strSQL As String
    Dim cmdAIM_Help As ADODB.Command
    
    'Quitting from Existing Help browser if it is already opened
    HelpExit
    
    'Generating new instance for Help browser
    Set objIEHelp = New InternetExplorer
    Set cmdAIM_Help = New ADODB.Command
    Set rstHelp = New ADODB.Recordset
    
    With cmdAIM_Help
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_GetHelp_SP"
        .Parameters(1).Value = strForm
        .Parameters(2).Value = strControl
    End With
    
    With rstHelp
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    rstHelp.Open cmdAIM_Help
    Dim DCType As String
    If Not rstHelp.eof Then
        strFilePath = App.Path & Trim(rstHelp(0))
    Else
        strFilePath = App.Path & "\WebHelp\SSA_DR_Help.htm"
    End If
    
    
    objIEHelp.StatusBar = False
    objIEHelp.AddressBar = False
    objIEHelp.MenuBar = False
    objIEHelp.Resizable = True
    objIEHelp.Navigate (strFilePath)
    objIEHelp.Visible = True
    
    rstHelp.Close
    Set rstHelp = Nothing
Exit Function

ErrorHandler:
    If Err.Number = 462 Or Err.Number = 91 Then
        Exit Function
    End If
    Err.Raise Err.Number, Err.source & "(f_GetConstraintIDList)", Err.Description
    
End Function

Public Function HelpExit()
    On Error GoTo ErrorHandler
    objIEHelp.Quit
    Set objIEHelp = Nothing
    Exit Function

ErrorHandler:
    If Err.Number = 462 Or Err.Number = 91 Then
        Exit Function
    End If
    Err.Raise Err.Number, Err.source & "(f_GetConstraintIDList)", Err.Description
    
End Function
