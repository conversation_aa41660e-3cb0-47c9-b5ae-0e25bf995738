VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsTables"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'************************************************************
' Used with SQLStmt.cls
'************************************************************
Option Explicit

Private mTables As Collection

Public Function Add(ByVal TableName As String) As clsTable
On Error GoTo ErrorHandler
    
    'Initialize
    Dim TableNew As clsTable
    Set TableNew = New clsTable
    
    'Set value = input parameter
    TableNew.TableName = TableName
    
    'Add to Collection
    mTables.Add TableNew, TableName
    
    'Set return value
    Set Add = TableNew
    
    'Clean up
    Set TableNew = Nothing
    
Exit Function
ErrorHandler:
    Set TableNew = Nothing
    Err.Raise Err.Number, Err.source, Err.Description & "Add"
End Function

Public Function Count() As Long
On Error GoTo ErrorHandler

    'Fetch property from collection
    Count = mTables.Count

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "Count"
End Function

Public Function Delete(ByVal Index As Variant)
On Error GoTo ErrorHandler
    
    'Remove item from collection
    mTables.Remove Index

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "Delete"
End Function

Public Function Item(ByVal Index As Variant) As clsTable
Attribute Item.VB_UserMemId = 0
On Error GoTo ErrorHandler
    
    'Fetch collection item for the given index (input parameter)
    Set Item = mTables.Item(Index)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "Item"
End Function

Public Function NewEnum() As IUnknown
Attribute NewEnum.VB_UserMemId = -4
Attribute NewEnum.VB_MemberFlags = "40"
On Error GoTo ErrorHandler
    
    'NewEnum is part of the IOleEnum interface.  It is for iterating through a list of items
    Set NewEnum = mTables.[_NewEnum]

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "NewEnum"
End Function

Private Sub Class_Initialize()
On Error GoTo ErrorHandler

    'Instantiate Modular objects
    Set mTables = New Collection
    
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "clsTables.Class_Initialize"
End Sub

Private Sub Class_Terminate()
On Error GoTo ErrorHandler
    
    'Destroy Modular objects
    Set mTables = Nothing
    
Exit Sub
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "clsTables.Class_Terminate"
End Sub
