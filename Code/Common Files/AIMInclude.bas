Attribute VB_Name = "AIMInclude"
Option Explicit
Public AC As New AIMAdvCalendar
Public AIMCalenderLoaded As Boolean
Public Cn As New Connection
Public CnConnected As Boolean
'Global Control Variable for the Main Menu
Public gbUpdating As Boolean

'Holds name of the computer, where DR application is running
Public gComputerName As String

'Global and single connection to log errors to the DB.
Public gDBConnection As ADODB.Connection

'Connection used to log to the database
'Public DBConnection As ADODB.Connection

Public gJobUpdateID As String

Public gWantToLogToDB As Boolean

Public gLogFile As String


Public gIsErrorFilterCreated As Boolean

'-------Error type codes ----------------------
Public Const gDRGeneralError As String = "DRGE" 'Purely errros from UI
Public Const gDRJobUpdate As String = "DRJU"    'Purely FYI type messages from scheduled jobs.
Public Const gDRJobError As String = "DRJE"     'Purely errors related to scheduled jobs

'------------------------------------------------------------------------------------------
    'TO SPECIFY USERS PO EXT LIMIT      sujit
    Public g_UserPOLimit As Double
    Public g_bUserPOLT As Boolean
    'TO SPECIFY CAN CREATE USER TO DATABASE BY USING AIM APPLICATION    sujit
    Public g_CreateDBUser As Boolean
    'TO SPECIFY CAN UPDATEPASSWORD BY USING AIM APPLICATION     sujit
    Public g_UpdatePassword As Boolean
    Public gRole As String
'------------------------------------------------------------------------------------------
'TO SPECIFY USERS LEVEL                                     sujit
    Public Const g_RoleLevel_0 As Integer = 0
    Public Const g_RoleLevel_1 As Integer = 1
    Public Const g_RoleLevel_2 As Integer = 2
    Public Const g_RoleLevel_3 As Integer = 3
    Public Const g_RoleLevel_4 As Integer = 4
    Public Const g_RoleLevel_5 As Integer = 5
    Public Const g_RoleLevel_6 As Integer = 6
    Public Const g_RoleLevel_7 As Integer = 7
    Public Const g_RoleLevel_8 As Integer = 8
    Public Const g_RoleLevel_9 As Integer = 9
    Public Const g_RoleLevel_10 As Integer = 10
    
'    Public Const g_strRoleLevel_0 As String = "System Admin Level"
'    Public Const g_strRoleLevel_1 As String = "Basic Role Level"
'    Public Const g_strRoleLevel_2 As String = "Role Level 2"
'    Public Const g_strRoleLevel_3 As String = "Role Level 3"
'    Public Const g_strRoleLevel_4 As String = "Role Level 4"
'    Public Const g_strRoleLevel_5 As String = "Role Level 5"
'    Public Const g_strRoleLevel_6 As String = "Role Level 6"
'    Public Const g_strRoleLevel_7 As String = "Role Level 7"
'    Public Const g_strRoleLevel_8 As String = "Role Level 8"
'    Public Const g_strRoleLevel_9 As String = "Role Level 9"
'    Public Const g_strRoleLevel_10 As String = "Role Level 10"
     
'------------------------------------------------------------------------------------------
'******************************************************************************************
'SQL Connection variables and constants
'------------------------------------------------------------------------------------------
'Database Connection Variables
    Public gServer As String
    Public gDataBase As String
    Public gUserID As String
    Public gPassWord As String
    Public gWinNTAuth As String
    Public gLogOnStatus As Boolean
    Public gNbrConnections As Integer
    Public Const MAXSQLCONNECTIONS = 10
    Public Const g_CONNECTSTRING As String = "APP=EXceed AIM" & ";"
    
    
    
'------------------------------------------------------------------------------------------
'SQL Action Codes
Public Enum SQL_ACTIONS
    SQL_GetEq = 0
    SQL_GetGT = 1
    SQL_Getlt = 2
    SQL_GetGE = 3
    SQL_GetLE = 4
    SQL_GetFirst = 5
    SQL_GetLast = 6
End Enum
'------------------------------------------------------------------------------------------
'SQL Connection Return Codes
    Public Const FAIL = 0
    Public Const SUCCEED = 1
    Public Const TOOMANY = 2
'******************************************************************************************

'******************************************************************************************
'Global variables and constants used for Localization.
'------------------------------------------------------------------------------------------
    Public gxarTranslate As New XArrayDB
    Public gLangIndex As Long
    Public gLangID As String
    Public gLocaleID As Integer
    Public gTranslateDate As Date
    Public gColonOption As Boolean
    Public gGridAutoSizeOption As Boolean
    Public gDateFormat As String
    Public gTimeFormat As String
    Public gLogErrors As Boolean
'------------------------------------------------------------------------------------------
'Define Language and Locale constants:
'------------------------------------------------------------------------------------------
'INTERNATIONALIZATION: Refer to the AIMLanguages table for Locales and LangIDs
'NOTE: Be sure to
'   (1) declare the langindex and localeid constants in AIMInclude.bas
'   (2) add the translations to the Resource File with the index starting at
'       n00, where n would be incremented by 200 for each new language.
'       and the next two places would be for the string id.
'       e.g. EN-US=0-199; JA=200-399 and so forth.
'------------------------------------------------------------------------------------------
'   Locale ID
'------------------------------------------------------------------------------------------
    Public Const g_LOCALEID_EN_US   As Long = 1033      'English (US)
    Public Const g_LOCALEID_JA      As Long = 1041      'Japanese
    Public Const g_LOCALEID_ZH_CN   As Long = 2052      'Chinese (Simplified/PRC)
    Public Const g_LOCALEID_KO      As Long = 1042      'Korean
    Public Const g_LOCALEID_ZH_TW   As Long = 1028      'Chinese (Traditional/Taiwan)
'------------------------------------------------------------------------------------------
'   Resource ID Offset
'------------------------------------------------------------------------------------------
    Public Const g_LANGINDEX_EN_US  As Long = 0         'English (US)
    Public Const g_LANGINDEX_JA     As Long = 200       'Japanese
    Public Const g_LANGINDEX_ZH_CN  As Long = 400       'Chinese (Simplified/PRC)
    Public Const g_LANGINDEX_KO     As Long = 600       'Korean
'******************************************************************************************

'ACCESS_TYPE constants:
    Public Const g_ACCESS_NONE As Long = 0
    Public Const g_ACCESS_READ As Long = 1
    Public Const g_ACCESS_CREATEMODIFY As Long = 2
    Public Const g_ACCESS_CREATE As Long = 3
    Public Const g_ACCESS_MODIFY As Long = 4

'Codelookup constants:
    Public Const g_CODETYPE_FCSTTYPE As String = "FORECASTTYPE"
    Public Const g_CODETYPE_FCSTELEMENT As String = "FCSTELEMENT"
    Public Const g_CODETYPE_ACCESSTYPE As String = "FCSTACCESSTYPE"
    Public Const g_CODETYPE_FREEZETYPE As String = "FREEZETYPE"
    Public Const g_CODETYPE_ACCESSCONTROL As String = "ACCESSCONTROL"
    Public Const g_CODETYPE_ADJUSTTYPE As String = "ADJUSTTYPE"
    Public Const g_CODETYPE_DATACALCTYPE As String = "DATACALCTYPE"
    
    Public Const g_CODETYPE_WEEKOFMONTH As String = "WEEKOFMONTH"
    Public Const g_CODETYPE_RATIOTYPE As String = "RATIOTYPE"
    Public Const g_CODETYPE_CONSTRAINTTYPE As String = "CONSTRAINTTYPE"
    Public Const g_CODETYPE_POFILTEROPTION As String = "POFILTEROPTION"
    Public Const g_CODETYPE_LSTATUS As String = "LSTATUS"
    Public Const g_CODETYPE_LTYPE As String = "LTYPE"
    Public Const g_CODETYPE_ROLELEVEL As String = "ROLELEVEL"
    

'MDC Enhancements -- Distribution Center Types. Should match those returned by stored proc "AIM_ItemPosition_Sp"
    Public Const g_DCTYPE_MDC As String = "MDC"
    Public Const g_DCTYPE_DEPENDENT As String = "DEPENDENT"
    Public Const g_DCTYPE_INDEPENDENT As String = "INDEPENDENT"
'End MDC Enhancements.

'Enhancements TO RETAIN ROW POSITION AT VENDOR DETAIL --BUYER REVIEW -SUJIT10132003
    Public Const g_SPLITTERTYPE As String = "_VendorDetailSplitter"
'End 'Enhancements TO RETAIN ROW POSITION AT VENDOR DETAIL --BUYER REVIEW

'File Name and other constants:
    Public Const g_ERRORLOG As String = "AIMErrorLog.txt"
    Public Const g_ISO_DATE_FORMAT As String = "yyyy/mm/dd"
    Public Const g_ISO_DATE_FORMAT_NODELIMITERS As String = "yyyymmdd"

'Help Constants
    Public Const HH_DISPLAY_TOC = &H1
    Public Const HH_DISPLAY_INDEX = &H2
    Public Const HH_DISPLAY_SEARCH = &H3

'Miscellaneous Constants
Public Const SW_SHOWMAXIMIZED = 3
Public Const SW_SHOWNORMAL = 1
Public Const SW_SHOWDEFAULT = 10
'Osama --Adding a new 2 Methods as Method 3 Starting from First History Period if less than number of MovAvg Periods
Public Const NBR_METHODS = 25
'Public Const NBR_METHODS = 23
Public Const MIN_EVALUATION_PERIODS = 10

Public Const PI = 3.14159

Public Const VALUETRUE = 1
Public Const VALUEFALSE = 0

'Constants for SSOLEGRID
    Public Const VALUETRUESS = -1
    Public Const VALUEFALSESS = 0

'Constants for Extracting Computer Name
        Public Const MAX_COMPUTERNAME_LENGTH As Integer = 255

'Constants used for AdjustColumnWidth
    Public Const ACW_EXPAND As String = "EXPAND"
    Public Const ACW_CONTRACT As String = "CONTRACT"
    Public Const ACW_ADJUST As String = "ADJUST"

'Color Codes
    Public Const LIGHTGREEN = &HC0FFC0
    Public Const LIGHTYELLOW = &HC0FFFF

'Data Interface Transaction Type Constants
    Public Const g_DI_TT_DD As String = "DD"    'Demand Detail (in)
    Public Const g_DI_TT_LT As String = "LT"    'Lead Time (in)
    Public Const g_DI_TT_HS As String = "HS"    'Item History (in)
    Public Const g_DI_TT_SD As String = "SD"    'Sales Detail (in)
    Public Const g_DI_TT_SS As String = "SS"    'Stock Status (in)
    Public Const g_DI_TT_VN As String = "VN"    'Vendor Assignment (in)
    Public Const g_DI_TT_SL As String = "SL"    'Stock Lite (in -- corollary to SS)
    Public Const g_DI_TT_LC As String = "LC"    'Locations (in)
    Public Const g_DI_TT_RO As String = "RO"    'Re-stock Order (in)
    Public Const g_DI_TT_RP As String = "RP"    'Re-stock Profile (in)
    Public Const g_DI_TT_RS As String = "RS"    'Restock Sourcing (in)
    Public Const g_DI_TT_AS As String = "AS"    'Alternate Source/Vendor (in)
    Public Const g_DI_TT_IS As String = "IS"    'Allocation Item Substitutes (in)
    Public Const g_DI_TT_KB As String = "KB"    'KitBOM (in)
    Public Const g_DI_TT_FA As String = "FA"    'Demand Plan User Forecast (in)
    
    Public Const g_DI_TT_AO As String = "AO"    'Allocation Order (out)
    Public Const g_DI_TT_PO As String = "PO"    'Purchase Order (out)
    
   
     Public Const G_JOBID_DATA_INTERFACE As String = "10"
    Public Const G_JOBID_DEMAND_UPDATE As String = "20"
    Public Const G_JOBID_ORDER_GENERATION As String = "30"
    Public Const G_JOBID_SEASONALITY_PROFILE As String = "40"
    Public Const G_JOBID_VELOCITY_CODE As String = "50"
    Public Const G_JOBID_ORDER_POLICY As String = "60"
    Public Const G_JOBID_FORECAST_BATCH As String = "70"
    Public Const G_JOBID_ALLOCATION As String = "80"
    Public Const G_JOBID_DEMANDPLANING_BATCH As String = "90"

    
'Public gOSPlatform As Integer
'Public Status Bar
    Public gStatusBar As Object
    Public gToolBar As Object

'Public Access Level
Public gAccessLvl As Integer
'Control Number constants, derived from table EXceedAIM.AIMFunctions. Used with VerifyAccess()
    Public Const AF_BUYER_REVIEW As Integer = 1
    Public Const AF_BUYER_REVIEW_WORKSHEET As Integer = 2
    Public Const AF_INITIAL_SEASONAL_REVIEW As Integer = 3  'not used
    Public Const AF_RELEASE_UNORDERED_ITEMS As Integer = 4
    Public Const AF_STOCK_STATUS_REPORT As Integer = 5
    Public Const AF_SUGGESTED_REVIEW_CYCLES_REPORT As Integer = 6
    Public Const AF_ITEM_MAINTENANCE As Integer = 7
    'missing 8
    Public Const AF_LOCATIONS_MAINTENANCE As Integer = 9
    Public Const AF_OPTIONS_MAINTENANCE As Integer = 10
    Public Const AF_PROMOTIONS_MAINTENANCE As Integer = 11
    Public Const AF_REVIEW_CYCLE_MAINTENANCE As Integer = 12
    Public Const AF_SEASONALITY_PROFILE_MAINTENANCE As Integer = 13
    Public Const AF_VENDOR_ASSORTMENT_MAINTENANCE As Integer = 14
    Public Const AF_ITEM_SUMMARY_DETAIL_REPORT As Integer = 15
    Public Const AF_LOCATIONS_REPORT As Integer = 16
    Public Const AF_OPTIONS_REPORT As Integer = 17
    Public Const AF_PROMOTIONS_REPORT As Integer = 18
    Public Const AF_REVIEW_CYCLE_REPORT As Integer = 19
    Public Const AF_SEASONALITY_REPORT As Integer = 20
    Public Const AF_VENDOR_ASSORTMENT_REPORT As Integer = 21
    Public Const AF_ITEM_EXCEPTIONS_REPORT As Integer = 22
    Public Const AF_ITEM_LOCATION_REPORT As Integer = 23    'not used
    Public Const AF_LOCATION_ITEM_REPORT As Integer = 24    'not used
    Public Const AF_FORECAST_SIMULATOR As Integer = 25
    Public Const AF_JOB_SCHEDULING As Integer = 26
    Public Const AF_SYSTEM_CONTROL_MAINTENANCE As Integer = 27
    Public Const AF_AIM_CALENDAR_MAINTENANCE As Integer = 28
    Public Const AF_AIM_USERS_MAINTENANCE As Integer = 29
    Public Const AF_FORECAST_METHODS_MAINTENANCE As Integer = 30
    Public Const AF_ITEM_DEFAULTS_MAINTENANCE As Integer = 31
    Public Const AF_ITEM_STATUS_MAINTENANCE As Integer = 32
    Public Const AF_VELOCITY_CODE_PERCENTAGES As Integer = 33
    Public Const AF_TRANSFER_MANAGEMENT As Integer = 34
    Public Const AF_JOB_WIZARD As Integer = 35
    Public Const AF_INTERMITTENT_ANALYSIS As Integer = 36
    Public Const AF_AD_HOC_ORDER_GENERATION As Integer = 37
    Public Const AF_BUYER_STATUS As Integer = 38
    Public Const AF_VIEW_JOB_LOG As Integer = 39
    Public Const AF_DATA_INTERFACE_TRANSACTIONS As Integer = 40
    Public Const AF_FORECAST_MAINTENANCE As Integer = 41
    Public Const AF_FORECAST_GENERATOR As Integer = 42
    Public Const AF_INITIALIZE_NEW_LOCATION As Integer = 43
    Public Const AF_FORECAST_SAFETY_STOCK_OVERRIDE As Integer = 44
    Public Const AF_CLASS_MAINTENANCE As Integer = 45
    Public Const AF_FORECAST_MODIFICATION As Integer = 46
    Public Const AF_ITEM_HISTORY_COPY As Integer = 47
    Public Const AF_PRODUCTION_CONSTRAINTS_MAINTENANCE As Integer = 48
    Public Const AF_VENDOR_SIZING As Integer = 49
    Public Const AF_SPECIALS As Integer = 50
    Public Const AF_DXPO As Integer = 51
    Public Const AF_RELEASED_PURCHASE_ORDERS As Integer = 52
    Public Const AF_ALLOCATION_REVIEW As Integer = 53
    Public Const AF_FORECASTSETUP As Integer = 54
    Public Const AF_DEMANDPLANNER As Integer = 55
    Public Const AF_AIMROLES As Integer = 56
    Public Const AF_COMP_DEMAND_MOD_MAINT As Integer = 57
    Public Const AF_DESTINATION_DEFAULTS As Integer = 58
    Public Const AF_USER_PASSWORD As Integer = 59
    Public Const AF_VIEW_LOG As Integer = 60    'sgajjela
    
    
    
'End Control Number constants

'Global Y Axis Maximum
Public gMaxYValue As Integer

'******************************************************************************************
' Define AIM Enums.
'------------------------------------------------------------------------------------------
Public Enum CONNECTION_ACTIONS
    CONNECTION_OPEN = 0
    CONNECTION_CLOSE = 1
End Enum
'------------------------------------------------------------------------------------------
Public Enum BYPASSREASON
    BR_NOEXCEPTION = 0
    BR_BYPASS = 1
    BR_LOWDEMAND = 2
    BR_HIDEMAND = 3
    BR_REALHI = 4
End Enum
'------------------------------------------------------------------------------------------
Public Enum FCSTSTATUS_CODE
    FS_ACTIVE = 1
    FS_INACTIVE = 999999
End Enum
'------------------------------------------------------------------------------------------
Public Enum opgParsePath
    FILE_ONLY
    PATH_ONLY
    DRIVE_ONLY
    FILEEXT_ONLY
End Enum
'------------------------------------------------------------------------------------------
'******************************************************************************************

'******************************************************************************************
'* Define AIM Data Structures
'------------------------------------------------------------------------------------------
Public Type AIMDAYS_RCD
    FYDate As Date
    DayStatus As Integer
    FYPeriod As Integer
End Type
'------------------------------------------------------------------------------------------
Public Type AIMYEARS_RCD
    FiscalYear As Integer
    FYStartDate As Date
    FYEndDate As Date
    NbrWeeks As Integer
End Type
'------------------------------------------------------------------------------------------
Public Type DEMAND_RCD
    EndYear As Integer
    EndPeriod As Integer
    StartYear As Integer
    StartPeriod As Integer
    BaseDate As Date
    OldestPeriod As Integer
    CpsTotal(1 To 3) As Double
    DpsTotal(1 To 3) As Double
    Cps(1 To 156) As Double
    dps(1 To 156) As Double
    ByPassFlag(1 To 156) As BYPASSREASON
    si(1 To 52) As Double
End Type
'------------------------------------------------------------------------------------------
Public Type DISTRIBUTION_RCD
    Base As Double
    Interval As Double
    SvcLvl As Double
    MaxUnits As Integer
    Points As Integer
    DistCounts(1 To 20) As Double
End Type
'------------------------------------------------------------------------------------------
Public Type FCSTRESULTS_RCD
    FcstStatus As FCSTSTATUS_CODE
    FcstMethod As Integer
    FcstDescription As String
    FcstNextPd As Double
    ByPassFlag(1 To 52) As Boolean
    Demand(1 To 52) As Double
    Fcst(1 To 52) As Double
    Accum_Demand(1 To 52) As Double
    Accum_E(1 To 52) As Double
    Accum_AE(1 To 52) As Double
    TrkSignal(1 To 52) As Double
    TotalDemand As Double
    TotalForecast As Double
    Trend As Double
    N As Integer
End Type
'------------------------------------------------------------------------------------------
Public Type ITEM_RCD
    LcId As String
    Item As String
    ItDesc As String
    ItStat As String
    ActDate As Date
    InActDate As Date
    OptionId As String
    Class1 As String
    Class2 As String
    Class3 As String
    Class4 As String
    BinLocation As String
    SaId As String
    VelCode As String
    VnId As String
    Assort As String
    ById As String
    MDC As String
    MDCFlag As String
    PmId As String
    UPC As String
    Weight As Double
    Cube As Double
    Price As Double
    Cost As Double
    BkQty(1 To 10) As Long
    BkCost(1 To 10) As Double
    UOM As String
    ConvFactor As Double
    BuyingUOM As String
    ReplenCost2 As Double
    Oh As Long
    Oo As Long
    ComStk As Long
    BkOrder As Long
    BkComStk As Long
    LeadTime As Integer
    PackRounding As String
    IMin As Long
    IMax As Long
    CStock As Long
    SSAdj As Double
    UserMin As Long
    UserMax As Long
    UserMethod As Integer
    FcstMethod As Integer
    FcstDemand As Double
    UserFcst As Double
    UserFcstExpDate As Date
    MAE As Double
    MSE As Double
    Trend As Double
    Mean_NZ As Double
    StdDev_NZ As Double
    IntSafetyStock As Double
    IsIntermittent As String
    LtvFact As Double
    FcstCycles As Integer
    ZeroCount As Integer
    DIFlag As String
    DmdFilterFlag As String
    TrkSignalFlag As String
    UserDemandFlag As String
    ByPassPct As Double
    FcstUpdCyc As Long
    PlnTT As String
    ZOPSw As String
    OUTLSw As String
    ZSStock As String
    DSer As Double
    Freeze_BuyStrat As String
    Freeze_ById As String
    Freeze_LeadTime As String
    Freeze_OptionID As String
    Freeze_DSer As String
    OldItem As String
    Accum_Lt As Integer
    ReviewTime As Integer
    OrderPt As Long
    OrderQty As Long
    SafetyStock As Double
    FcstRT As Double
    FcstLT As Double
    Fcst_Month As Double
    Fcst_Quarter As Double
    Fcst_Year As Double
    FcstDate As Date
    VC_Amt As String
    VC_Units_Ranking As Long
    VC_Amt_Ranking As Long
    VC_Date As Date
    VelCode_Prev As String
    VC_Amt_Prev As String
End Type
'------------------------------------------------------------------------------------------
Public Type ITEMHISTORY_RCD
    LcId As String
    Item As String
    HisYear As Integer
End Type
'------------------------------------------------------------------------------------------
Public Type LOCATION_RCD
    LcId As String
    PutAwayDays As Integer
    ReplenCost As Double
    DemandSource As String
    StkDate  As Date
    LookBackPds As Integer
    Last_FcstUpdCyc As Long
    DmdScalingFactor As Double
    ScalingEffUntil As Double
End Type
'------------------------------------------------------------------------------------------
Public Type METHOD_RCD
    MethodId As Integer
    MethodStatus As FCSTSTATUS_CODE
    MethodDesc As String
    ApplySeasonsIndex As String
    ApplyTrend As Boolean
End Type
'------------------------------------------------------------------------------------------
Public Type MOVAVGWORK_RCD
    MA001_013(52) As Double
    Pd001_013(52) As Integer
    MA014_026(52) As Double
    Pd014_026(52) As Integer
    MA027_039(52) As Double
    Pd027_039(52) As Integer
    MA040_052(52) As Double
    Pd040_052(52) As Integer
    MA053_065(52) As Double
    Pd053_065(52) As Integer
    MA066_078(52) As Double
    Pd066_078(52) As Integer
    MA079_091(52) As Double
    Pd079_091(52) As Integer
    MA092_104(52) As Double
    Pd092_104(52) As Integer
    Trend(52) As Double
    'SaAdj(52) As Double
End Type
'------------------------------------------------------------------------------------------
Public Type OPTIONS_RCD
    OpId As String
    OpDesc As String
    IDFL As Integer
    BypIDFL As String
    CADL As Integer
    HiDFL As Integer
    LoDFL As Integer
    NDFL As Integer
    DDMAP As Integer
    DAMP As Double
    MinSmk As Double
    MaxSmk As Double
    IntSmk As Double
    TSL As Double
    TSSmK As Double
    BypDDU As String
    ApplyDmdFilter As String
    BypZSl As String
    BypTFP As String
    BypZOH As String
    BypBef As Integer
    BypAft As Integer
    MinBi As Double
    hipct As Double
    LoPct As Double
    HiMADP As Double
    LoMADP As Double
    MADSmk As Double
    MADExK As Double
    TrnSmk As Double
    DIDDL As Integer
    DITrnDL As Double
    DITrps As Integer
    DIMADP As Double
    hitrndl As Double
    FilterSmk As Double
    LumpyFilterPct As Double
    Dft_TurnHigh As Double
    Dft_TurnLow As Double
End Type
'------------------------------------------------------------------------------------------
Public Type PERFORMANCE_RCD
    EndYear As Integer
    EndPeriod As Integer
    StartYear As Integer
    StartPeriod As Integer
    FYStartDate As Date
    CpsTotal(1 To 3) As Double
    SubsTotal(1 To 3) As Double
    QtyOrdTotal(1 To 3) As Double
    OrdCntTotal(1 To 3) As Long
    Cps(1 To 156) As Double
    Subs(1 To 156) As Double
    QtyOrd(1 To 156) As Double
    OrdCnt(1 To 156) As Long
End Type
'------------------------------------------------------------------------------------------
Public Type POTOTALS_RCD
    DailyReach As Double
    ReqDiscount As Double
    ReachCode As String
    AnnualPurchases As Double
    RSOQ_Cube As Double
    RSOQ_Dollars As Double
    RSOQ_Units As Double
    RSOQ_Weight As Double
    SOQ_Cube As Double
    SOQ_Dollars As Double
    SOQ_Units As Double
    SOQ_Weight As Double
    VSOQ_Cube As Double
    VSOQ_Dollars As Double
    VSOQ_Units As Double
    VSOQ_Weight As Double
    RSOQ_Amount As Double
    SOQ_Amount As Double
    VSOQ_Amount As Double
End Type
'------------------------------------------------------------------------------------------
Public Type PROMOTIONS_RCD
    PmId As String
    PmDesc As String
    PmStatus As Boolean
    PmStartDate As Date
    PmEndDate As Date
    PmAdj(1 To 52) As Double
End Type
'------------------------------------------------------------------------------------------
Public Type SAWORK_RCD
    SaId As String
    SaDesc As String
    LcId As String
    Class As String
    SAVersion As Integer
    NbrYears As Integer
    TotSales As Double
    ItmCnt As Long
    SumSales(1 To 52) As Double
End Type
'------------------------------------------------------------------------------------------
Public Type SEASONS_RCD
    SaId As String
    Bi(1 To 52) As Double
End Type
'------------------------------------------------------------------------------------------

'------------------------------------------------------------------------------------------
Public Type KITBOM_ITEM_RCD
    LcId As String
    Item As String
    LT() As Integer
    LT_RT() As Integer
    StartDate() As Date
    EndDate() As Date
End Type

'------------------------------------------------------------------------------------------
Public Type COMP_ITEM_RCD
    LcId As String
    Item As String
    LT() As Integer
    LT_RT() As Integer
    StartDate() As Date
    EndDate() As Date
End Type

'------------------------------------------------------------------------------------------
Public Type KITBOM_ITEM_DETAIL_RCD
    LcId As String
    Item As String
    FcstStartPd As Integer
    FcstEndPd As Integer
    StartDate As Date
    EndDate As Date
    Fcst As Double
    FcstLT As Double
    FcstRT As Double
    FcstMonth As Double
    FcstQtr As Double
    FcstYear As Double
    MAE As Double
End Type


'------------------------------------------------------------------------------------------
Public Type COMP_ITEM_DETAIL_RCD
    LcId As String
    Item As String
    FcstStartPd As Integer
    FcstEndPd As Integer
    StartDate As Date
    EndDate As Date
    Fcst As Double
    FcstLT As Double
    FcstRT As Double
    FcstMonth As Double
    FcstQtr As Double
    FcstYear As Double
    MAE As Double
End Type
'------------------------------------------------------------------------------------------
Public Type DIST_KITBOM_MAIN_ITEM_RCD
    LcId As String
    MainItem As String
    ItemComp As String
    CompUnits As Integer
    CompScrap As Double
    StartData  As Date
    EndDate As Date
End Type

'------------------------------------------------------------------------------------------
Public Type DIST_COMP_MAIN_ITEM_RCD
    LcId As String
    MainItem As String
    ItemComp As String
    CompUnits As Integer
    DemandFactor As Double
    StartData  As Date
    EndDate As Date
End Type
'------------------------------------------------------------------------------------------
Public Type DIST_KITBOM_ITEM_RCD
    ItemComponent As String
    Fcst As Double
    LT As Double
    RT As Double
    FcstKitBom As Double
    FcstKitBOMLT As Double
    FcstKitBOMRT As Double
    FcstKitBOMMonth As Double
    FcstKitBOMQtr As Double
    FcstKitBOMYear As Double
    MAEKitBOM   As Double
    Processed_YN As String
End Type

'------------------------------------------------------------------------------------------
Public Type DIST_COMP_ITEM_RCD
    ItemComponent As String
    Fcst As Double
    LT As Double
    RT As Double
    FcstCompItem As Double
    FcstCompItemLT As Double
    FcstCompItemRT As Double
    FcstCompItemMonth As Double
    FcstCompItemQtr As Double
    FcstCompItemYear As Double
    MAEComp As Double
End Type
'------------------------------------------------------------------------------------------

Public Type SIMULATION_RCD
    KFactor As Double
    SeasSmoothingPds As Double
    CurSaVersion As Integer
    MDCOption As String
    Int_Enabled As String
    Int_MinPctZero As Double
    Int_MaxPctZero As Double
    Int_SeasonalityIndex As Double
    Int_SrvLvlOverrideFlag As String
    Int_SrvLvl As Double
    Int_LookBackPds As Integer
    It As ITEM_RCD
    DM As DEMAND_RCD
    LC As LOCATION_RCD
    OP As OPTIONS_RCD
    PM As PROMOTIONS_RCD
    Sa As SEASONS_RCD
    KI As KITBOM_ITEM_RCD
    KIDetail() As KITBOM_ITEM_DETAIL_RCD
    CI As COMP_ITEM_RCD
    CIDetail() As COMP_ITEM_DETAIL_RCD
    'AIM Calendar
    AIMDays() As AIMDAYS_RCD
    AIMYears() As AIMYEARS_RCD
End Type
'------------------------------------------------------------------------------------------
Public Type OSVERSIONINFO
    dwOSVersionInfoSize As Long
    dwMajorVersion As Long
    dwMinorVersion As Long
    dwBuildNumber As Long
    dwPlatformId As Long
    szCSDVersion As String * 128      '  Maintenance string for PSS usage
End Type
'------------------------------------------------------------------------------------------
Public Type VENDOR_RCD
    VnId As String
    Assort As String
    MDCFlag As String
    VName As String
    VAddress1 As String
    VAddress2 As String
    VCity  As String
    VZip As String
    VContact As String
    VPhone As String
    VFax As String
    VEMail As String
    VDocEMail As String
    Dft_ById  As String
    RevCycle As String
    DftLt As Integer
    Vn_Min As Double
    Vn_Best As Double
    Reach_Code As String
    POByZone As String
End Type

Public Type FCST_ITEM_FILTER
    Item As String
    ItemStatus As String
    VnId As String
    Assort As String
    LcId As String
    ById As String
    Class1 As String
    Class2 As String
    Class3 As String
    Class4 As String
    LStatus As String
    LDivision As String
    LRegion As String
    LUserDefined As String
    'Optional -- used primarily with demand planner
    SubFilterItem As String
    SubFilterItemStatus As String
    SubFilterVnId As String
    SubFilterAssort As String
    SubFilterLcID As String
    SubFilterById As String
    SubFilterClass1 As String
    SubFilterClass2 As String
    SubFilterClass3 As String
    SubFilterClass4 As String
    SubFilterLStatus As String
    SubFilterLDivision As String
    SubFilterLRegion As String
    SubFilterLUserDefined As String
End Type

Public Enum AIM_INTERVALS
    int_Days = 0
    int_Weeks = 1
    int_months = 2
    int_Quarters = 3
    int_544 = 4
    int_454 = 5
    int_445 = 6
    int_4Wks = 7
End Enum

Public Enum AIM_FORECASTUNITS
    FcstUnits = 0
    FcstCost = 1
    FcstWeight = 2
    FcstCube = 3
    FcstPrice = 4
End Enum

Public Type AIM_FCSTTYPES
    FcstType As String
    Selected As Boolean
End Type

'******************************************************************************************

'Derived functions
Public Declare Function GetUserName Lib "advapi32.dll" _
    Alias "GetUserNameA" _
    (ByVal lpBuffer As String, nSize As Long) As Long

Public Declare Function ShellExecute Lib "shell32.dll" _
    Alias "ShellExecuteA" _
    (ByVal hwnd As Long, ByVal lpOperation As String, _
    ByVal lpFile As String, ByVal lpParameters As String, _
    ByVal lpDirectory As String, ByVal nShowCmd As Long) As Long

Public Declare Function HTMLHelp Lib "hhctrl.ocx" _
  Alias "HtmlHelpA" (ByVal hwnd As Long, _
  ByVal lpHelpFile As String, _
  ByVal wCommand As Long, _
  ByVal dwData As Long) As Long

'NT APIs
Public Declare Function GetComputerNameA Lib _
        "kernel32" (ByVal lpBuffer As String, nSize As Long) As Long



    






