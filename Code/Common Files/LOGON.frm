VERSION 5.00
Begin VB.Form LOGON 
   BackColor       =   &H00E6E6E6&
   BorderStyle     =   3  'Fixed Dialog
   Caption         =   "SSA Distribution Replenishment"
   ClientHeight    =   6030
   ClientLeft      =   7590
   ClientTop       =   2805
   ClientWidth     =   11580
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   400
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "LOGON.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   Moveable        =   0   'False
   PaletteMode     =   1  'UseZOrder
   ScaleHeight     =   402
   ScaleMode       =   3  'Pixel
   ScaleWidth      =   772
   Begin VB.Frame Frame1 
      BackColor       =   &H00E6E6E6&
      BorderStyle     =   0  'None
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   5835
      Left            =   2835
      TabIndex        =   7
      Top             =   98
      Width           =   8595
      Begin VB.TextBox txtServer 
         Height          =   340
         Left            =   2175
         MaxLength       =   30
         TabIndex        =   1
         Text            =   "txtServer"
         Top             =   1440
         Width           =   2820
      End
      Begin VB.TextBox txtDatabase 
         Height          =   340
         Left            =   2175
         MaxLength       =   30
         TabIndex        =   2
         Text            =   "txtDataBase"
         Top             =   1875
         Width           =   2820
      End
      Begin VB.TextBox txtUserId 
         Height          =   340
         Left            =   2175
         MaxLength       =   30
         TabIndex        =   4
         Text            =   "txtUserID"
         Top             =   2310
         Width           =   2820
      End
      Begin VB.TextBox txtPassWord 
         Height          =   330
         IMEMode         =   3  'DISABLE
         Left            =   2175
         MaxLength       =   30
         PasswordChar    =   "*"
         TabIndex        =   5
         Text            =   "txtPassWord"
         Top             =   2745
         Width           =   2820
      End
      Begin VB.CommandButton cmdLogin 
         BackColor       =   &H0000E6FF&
         Caption         =   "Log In"
         Default         =   -1  'True
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   345
         Left            =   5100
         MaskColor       =   &H00FFFFFF&
         Style           =   1  'Graphical
         TabIndex        =   6
         Top             =   2745
         Width           =   1545
      End
      Begin VB.CheckBox ckWinNTAuth 
         BackColor       =   &H00E6E6E6&
         Caption         =   "Use Windows Authentication"
         BeginProperty DataFormat 
            Type            =   5
            Format          =   ""
            HaveTrueFalseNull=   1
            TrueValue       =   "True"
            FalseValue      =   "False"
            NullValue       =   ""
            FirstDayOfWeek  =   0
            FirstWeekOfYear =   0
            LCID            =   1033
            SubFormatType   =   7
         EndProperty
         Height          =   315
         Left            =   5100
         TabIndex        =   3
         Top             =   1905
         Width           =   3300
      End
      Begin VB.ComboBox Languages 
         Height          =   315
         ItemData        =   "LOGON.frx":030A
         Left            =   2175
         List            =   "LOGON.frx":030C
         TabIndex        =   0
         Text            =   "Choose a Language"
         Top             =   480
         Width           =   3735
      End
      Begin VB.Label LogonLabel 
         BackStyle       =   0  'Transparent
         Caption         =   "(c) Copyright 2004 SSA Global Technologies, Inc."
         Height          =   315
         Index           =   5
         Left            =   120
         TabIndex        =   18
         Top             =   4800
         Width           =   8295
      End
      Begin VB.Line Line1 
         X1              =   120
         X2              =   8400
         Y1              =   4710
         Y2              =   4710
      End
      Begin VB.Label LogonLabel 
         BackStyle       =   0  'Transparent
         Caption         =   "Display Color: 65536 colors (min.)"
         Height          =   315
         Index           =   4
         Left            =   120
         TabIndex        =   17
         Top             =   4185
         Width           =   8295
      End
      Begin VB.Label LogonLabel 
         BackStyle       =   0  'Transparent
         Caption         =   "Display Resolution: 1024x768"
         Height          =   315
         Index           =   3
         Left            =   120
         TabIndex        =   16
         Top             =   3825
         Width           =   8295
      End
      Begin VB.Label LogonLabel 
         BackStyle       =   0  'Transparent
         Caption         =   "This application is best viewed with these settings:"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   315
         Index           =   2
         Left            =   120
         TabIndex        =   15
         Top             =   3465
         Width           =   8295
      End
      Begin VB.Label LogonLabel 
         BackStyle       =   0  'Transparent
         Caption         =   "Please enter your connection and user authentication information below."
         Height          =   315
         Index           =   1
         Left            =   120
         TabIndex        =   14
         Top             =   1080
         Width           =   8295
      End
      Begin VB.Label LogonLabel 
         BackStyle       =   0  'Transparent
         Caption         =   "Welcome!"
         BeginProperty Font 
            Name            =   "Verdana"
            Size            =   8.25
            Charset         =   0
            Weight          =   700
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         Height          =   315
         Index           =   0
         Left            =   120
         TabIndex        =   13
         Top             =   120
         Width           =   8295
      End
      Begin VB.Label lblDatabase 
         BackStyle       =   0  'Transparent
         Caption         =   "Database"
         Height          =   315
         Left            =   120
         TabIndex        =   12
         Top             =   1905
         Width           =   1980
      End
      Begin VB.Label lblUserID 
         BackStyle       =   0  'Transparent
         Caption         =   "User ID"
         Height          =   315
         Left            =   120
         TabIndex        =   11
         Top             =   2355
         Width           =   1980
      End
      Begin VB.Label lblPassword 
         BackStyle       =   0  'Transparent
         Caption         =   "Password"
         Height          =   315
         Left            =   120
         TabIndex        =   10
         Top             =   2805
         Width           =   1980
      End
      Begin VB.Label lblServer 
         BackStyle       =   0  'Transparent
         Caption         =   "Server"
         Height          =   315
         Left            =   120
         TabIndex        =   9
         Top             =   1440
         Width           =   1980
      End
      Begin VB.Label lblLangID 
         Appearance      =   0  'Flat
         AutoSize        =   -1  'True
         BackColor       =   &H80000005&
         BackStyle       =   0  'Transparent
         Caption         =   "Language ID"
         ForeColor       =   &H80000008&
         Height          =   315
         Left            =   120
         TabIndex        =   8
         Top             =   480
         Width           =   1980
         WordWrap        =   -1  'True
      End
   End
   Begin VB.Image Image1 
      Height          =   3000
      Left            =   240
      Picture         =   "LOGON.frx":030E
      Top             =   1200
      Width           =   2355
   End
End
Attribute VB_Name = "LOGON"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Dim WinUserId As String
Dim m_UserLanguage As String

Private Declare Function GetUserDefaultLCID% Lib "kernel32" ()

Private Sub cmdLogin_Click()
On Error GoTo ErrorHandler
    
    Dim Cn As ADODB.Connection
    Dim rsSysCtrl As ADODB.Recordset
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim rsAIMUsers As ADODB.Recordset
    
    Dim strMessage As String
    Dim strText As String
    Dim RtnCode As Long
    Dim strSQL As String
    
    Screen.MousePointer = vbHourglass
        
    'Update Global Variables
    gServer = txtServer.Text
    gDataBase = txtDatabase.Text
    gPassWord = IIf(Trim(txtPassword.Text) = "", "****", Trim(Me.txtPassword.Text))
    gWinNTAuth = IIf(Me.ckWinNTAuth = vbChecked, "Y", "N")
    
    'If Windows Authentication is used, the User Id has already been initialized
    'If Windows Authentication is not used, get the User Id from the logon form
    If gWinNTAuth = "Y" Then
        Me.txtUserId.Text = WinUserId
        gUserID = WinUserId
    Else
        gUserID = txtUserId.Text
    End If
    
    'Connect to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True)
    
    If RtnCode <> SUCCEED Then
        RtnCode = Write_Message("")
        Screen.MousePointer = vbNormal
        Exit Sub
    End If
    
    'Update User's record with selected Language
    UpdateUserLanguage Cn
    
    'Fetch translations
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .CommandType = adCmdStoredProc
    End With
    
    
    
    Set rsSysCtrl = New ADODB.Recordset
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then
        gLangID = rsSysCtrl!dft_LangID
        If Trim(gLangID) = " " Then
            If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US
            strMessage = LoadResString(gLangIndex + 15) '"Unable to retrieve SSA DR System Control Data -- Connection terminated."
            Err.Description = strMessage
            Exit Sub
        End If
        
        gWantToLogToDB = rsSysCtrl!WantErrorsTobeLoggedToDatabase.Value
        gLogFile = Trim$(rsSysCtrl("LogFile").Value)
        
        
        
        gDateFormat = rsSysCtrl!DateFormat
        If gDateFormat = " " Then
            gDateFormat = "mm/dd/yyyy"
        
        End If
        
        gTimeFormat = rsSysCtrl!TimeFormat
        If gTimeFormat = " " Then
            gTimeFormat = "hh:mm:ss ampm"
        
        End If
        
        If rsSysCtrl!ColonOption = "Y" Then
            gColonOption = True
        ElseIf rsSysCtrl!ColonOption = "N" Then
            gColonOption = False
        Else
            gColonOption = False
        End If
        
        
        If rsSysCtrl!GridAutoSizeOption = "Y" Then
            gGridAutoSizeOption = True
        ElseIf rsSysCtrl!GridAutoSizeOption = "N" Then
            gGridAutoSizeOption = False
        Else
            gGridAutoSizeOption = False
        End If
        
        'Load the AIM User Profile
        Set rsAIMUsers = New ADODB.Recordset
        strSQL = "SELECT * FROM AIMUsers WHERE UserID = '" & gUserID & "'"
        rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockOptimistic
        
        If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
            gLangID = rsAIMUsers!LangID
            If rsAIMUsers!LogErrors = "Y" Then
                gLogErrors = True
            Else
                gLogErrors = False
            End If
            
            gLogOnStatus = True
            
            'Close the Recordset
            rsAIMUsers.Close
        
        Else
            gLogOnStatus = False
            If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US
            strMessage = LoadResString(gLangIndex + 16) '"The specified User ID is not authorized for access to SSA DR -- Connection terminated."
            MsgBox strMessage, vbCritical + vbOKOnly, LoadResString(gLangIndex + 0)

        End If
    Else
        'Error message here. This should not happen, if the database was installed correctly.
    End If  'check rsSysCtrl's status
    
    'Clean up
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, True)
    Set Cn = Nothing
    
    'Check for system constraints
'    RtnCode = AIMSysCheck(Cn, "Item")
'    RtnCode = AIMSysCheck(Cn, "AIMVendors")
'    RtnCode = AIMSysCheck(Cn, "AIMLocations")
'    RtnCode = AIMSysCheck(Cn, "xxx")
    
    If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US
    strMessage = LoadResString(gLangIndex + 20) '"Logging on to the SSA DR Database Server..."
    RtnCode = Write_Message(strMessage + " " + Trim(Me.txtServer.Text))
    
    'Get Locale ID
    getLocaleID
    
    'Translation to User's Locale
    GetTranslatedCaptions Me
    RemoveSemicolons
    cmdLogin.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_COMMANDBUTTON, COLOR_TYPE_SELECTED)
        
    'Update Registry values
    SaveLogonToRegistry
    
    RtnCode = Write_Message("")
    Screen.MousePointer = vbNormal
        
    Unload Me

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::Click", Now, gDRGeneralError, True, Err
    'f_HandleErr Me.Caption
    On Error Resume Next
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, True)
    Set Cn = Nothing
End Sub

Private Sub ckWinNTAuth_Click()
On Error GoTo ErrorHandler

    If Me.ckWinNTAuth = vbChecked Then
        Me.txtUserId.Text = WinUserId
        Me.txtUserId.Enabled = False
        Me.txtPassword.Enabled = False
    Else
        Me.txtUserId.Enabled = True
        Me.txtPassword.Enabled = True
        Me.txtUserId.SetFocus
    End If

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::ckWinNTAuth_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Activate()
On Error GoTo ErrorHandler

    If Me.txtServer.Text = "" Then
        Me.txtServer.SetFocus
    ElseIf Me.txtDatabase.Text = "" Then
        Me.txtDatabase.SetFocus
    ElseIf Me.txtUserId.Text = "" Then
        Me.txtUserId.SetFocus
    ElseIf gWinNTAuth = "Y" Then
        Me.cmdLogin.SetFocus
    ElseIf gWinNTAuth = "N" Then
        Me.txtPassword.SetFocus
    End If
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::Form_Activate", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler

    Dim Cn As New ADODB.Connection
    Dim RtnCode As Long
    Dim BufLgt As Long
    Dim NameLgt As Long
    Dim strMessage As String
    
    Screen.MousePointer = vbHourglass
    'Initialize global error logging setting to true -sgajjela
    gLogErrors = True
    
    'Get Resources for the Logon Screen
    gLocaleID = -1
    GetResourceIDFromRegistry
    If gLocaleID = -1 Then gLocaleID = GetUserDefaultLCID()
    
    GetLanguageList gLocaleID
    'Set the language dropdown to reflect the User's locale. The related event will call the translation function.
    Select Case gLocaleID
        Case g_LOCALEID_JA
            Languages.Text = Languages.List(1)
            m_UserLanguage = "ja"
                
        Case g_LOCALEID_ZH_CN
            Languages.Text = Languages.List(2)
            m_UserLanguage = "zh-cn"
        
        Case g_LOCALEID_KO
            Languages.Text = Languages.List(3)
            m_UserLanguage = "ko"
        
        'INTERNATIONALIZATION: Add more languages here. Refer to the AIMLanguages table for Locales and LangIDs
        'NOTE: Be sure to
        '   (1) declare the langindex and localeid constants in AIMInclude.bas
        '   (2) add the translations to the Resource File with the index starting at
        '       n00, where n would be incremented by 200 for each new language.
        '       and the next two places would be for the string id.
        '       e.g. EN-US=0-199; JA=200-399 and so forth.
        
        Case Else
            'Default to EN-US
            Languages.Text = Languages.List(0)
            m_UserLanguage = "en-us"
            
    End Select
    TranslateAllCaptions
    
    'Initialize the User Id Buffer
    BufLgt = 64
    gUserID = String(BufLgt, " ")

    'Center the form
    Me.Top = (AIM_Main.ScaleHeight - Me.Height) \ 2
    Me.Left = (AIM_Main.ScaleWidth - Me.Width) \ 2

    'Retrieve the values from the Registry
    'Get the Server Name
    gServer = GetSetting(AppName:="SSADR", Section:="Logon", Key:="Server", Default:="")
    Me.txtServer.Text = gServer

    'Get the database name
    gDataBase = GetSetting(AppName:="SSADR", Section:="Logon", Key:="DataBase", Default:="")
    Me.txtDatabase.Text = gDataBase

    'Get the Windows NT Authorization
    gWinNTAuth = GetSetting(AppName:="SSADR", Section:="Logon", Key:="WinNTAuth", Default:="Y")
    
    'Get the user's ID
    NameLgt = GetUserName(gUserID, BufLgt)
    gUserID = Left(gUserID, BufLgt - 1)
    WinUserId = gUserID     'May need this later if the Authentication Option is changed
    
    
    'Error logging information includes computer name. So, we need it while error logging. -sgajjela
    BufLgt = MAX_COMPUTERNAME_LENGTH
    gComputerName = String(MAX_COMPUTERNAME_LENGTH, " ")
    RtnCode = GetComputerNameA(gComputerName, BufLgt)
    gComputerName = Left(gComputerName, BufLgt - 1)
     
    If gWinNTAuth = "Y" Then
        Me.ckWinNTAuth = vbChecked
        Me.txtUserId.Enabled = False
        Me.txtPassword.Enabled = False
    Else
        Me.ckWinNTAuth = vbUnchecked
        gUserID = GetSetting(AppName:="SSADR", Section:="Logon", Key:="UserID", Default:="")
        Me.txtUserId.Enabled = True
        Me.txtPassword.Enabled = True
    End If
    
    Me.txtUserId.Text = gUserID
 
    'Set the user's password
    Me.txtPassword.Text = ""
    
    'Set the Logon Status
    gLogOnStatus = False

    Screen.MousePointer = vbNormal
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::Form_Load", Now, gDRGeneralError, True, Err
End Sub


Private Sub Languages_Click()
On Error GoTo ErrorHandler

    Dim LocaleID As Integer
    Dim ListIndex As Integer
    
    Screen.MousePointer = vbHourglass
    
    If Languages.Enabled Then
        Languages.Enabled = False
    
        Select Case Languages.Text
            Case Languages.List(0)
                ListIndex = 0
                LocaleID = g_LOCALEID_EN_US 'EN-US
                m_UserLanguage = "en-us"
            
            Case Languages.List(1)
                ListIndex = 1
                LocaleID = g_LOCALEID_JA
                m_UserLanguage = "ja"
                
            Case Languages.List(2)
                ListIndex = 2
                LocaleID = g_LOCALEID_ZH_CN
                m_UserLanguage = "zh-cn"
            
            Case Languages.List(3)
                ListIndex = 3
                LocaleID = g_LOCALEID_KO
                m_UserLanguage = "ko"
                        
            'INTERNATIONALIZATION: Add more languages here. Refer to the AIMLanguages table for Locales and LangIDs
            'NOTE: Be sure to
            '   (1) declare the langindex and localeid constants in AIMInclude.bas
            '   (2) add the translations to the Resource File with the index starting at
            '       n00, where n would be incremented by 200 for each new language.
            '       and the next two places would be for the string id.
            '       e.g. EN-US=0-199; JA=200-399 and so forth.
            
            Case Else
                'Default to English
                ListIndex = 0
                LocaleID = g_LOCALEID_EN_US
                m_UserLanguage = "en-us"
            
        End Select
        
        GetLanguageList LocaleID
        Languages.Text = Languages.List(ListIndex)
        If Languages.ListIndex <> ListIndex Then Languages.ListIndex = ListIndex
        TranslateAllCaptions
        
        Languages.Enabled = True
    End If
    
    Screen.MousePointer = vbDefault

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::Languages_Click", Now, gDRGeneralError, True, Err
End Sub


Private Sub txtDatabase_GotFocus()
On Error GoTo ErrorHandler

    Me.txtDatabase.SelLength = Len(Me.txtDatabase)

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::txtDatabase_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtPassWord_GotFocus()
On Error GoTo ErrorHandler

    Me.txtPassword.SelLength = Len(Me.txtPassword)

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::txtPassWord_GotFocus", Now, gDRGeneralError, True, Err

End Sub

Private Sub txtServer_GotFocus()
On Error GoTo ErrorHandler

    Me.txtServer.SelLength = Len(Me.txtServer)

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::txtServer_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Sub txtUserId_GotFocus()
On Error GoTo ErrorHandler

    Me.txtUserId.SelLength = Len(Me.txtUserId)

Exit Sub
ErrorHandler:
    f_HandleErr , , , "LOGON::txtUserId_GotFocus", Now, gDRGeneralError, True, Err
End Sub

Private Function GetResourceIDFromRegistry()
On Error GoTo ErrorHandler

    Dim strCaption As String
    
    'Retrieve Logon Translations from the Registry
    'Get the Server Name
    strCaption = GetSetting(AppName:="SSADR", Section:="Logon\ResourceStrings", Key:="LocaleID", Default:="1033")
    gLocaleID = strCaption
        
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(GetResourceIDFromRegistry)"
End Function

Private Function GetLanguageList(p_LocaleID As Integer)
On Error GoTo ErrorHandler

    Dim IndexCounter As Long
    
    Languages.Clear
    
    gLangIndex = GetLocaleIndex(p_LocaleID)
    For IndexCounter = 70 To 73 'INTERNATIONALIZATION: Update this counter for each new language that is added to the res. file
        'For the lang. dropdown, always display English text -- default and otherwise.
        If p_LocaleID = g_LOCALEID_EN_US Then
            Languages.AddItem LoadResString(g_LANGINDEX_EN_US + IndexCounter)
        Else
            'if the User's OS locale is non-english, then
            'display translated versions for these language names beside the english text, if available.
            Languages.AddItem LoadResString(g_LANGINDEX_EN_US + IndexCounter) & _
                              vbTab & _
                              "(" & LoadResString(gLangIndex + IndexCounter) & ")"
        End If
    Next
            
    Languages.Refresh
    
Exit Function
ErrorHandler:
    If Err.Number = 326 Then
        'LoadResString -- Resource with identifier not found
        Resume Next
    Else
        Err.Raise Err.Number, Err.source, Err.Description & "(GetLanguageList)"
    End If
End Function

Private Function TranslateAllCaptions()
On Error GoTo ErrorHandler

    SetLocaleIndex gLangIndex, gLocaleID  'resets the parameters.
    
    'Begin localization
    GetProperFont Me    'needs the gLocaleID set above.
    Me.Caption = LoadResString(gLangIndex + 0)
    LogonLabel(0).Caption = LoadResString(gLangIndex + 1)
    LogonLabel(1).Caption = LoadResString(gLangIndex + 2)
    LogonLabel(2).Caption = LoadResString(gLangIndex + 3)
    LogonLabel(3).Caption = LoadResString(gLangIndex + 4)
    LogonLabel(4).Caption = LoadResString(gLangIndex + 5)
    LogonLabel(5).Caption = LoadResString(gLangIndex + 6)
    'LogonLabel(6).Caption = LoadResString(gLangIndex + 7)
    lblServer.Caption = LoadResString(gLangIndex + 8)
    lblDatabase.Caption = LoadResString(gLangIndex + 9)
    ckWinNTAuth.Caption = LoadResString(gLangIndex + 10)
    lblUserID.Caption = LoadResString(gLangIndex + 11)
    lblPassword.Caption = LoadResString(gLangIndex + 12)
    cmdLogin.Caption = LoadResString(gLangIndex + 13)
    lblLangID.Caption = LoadResString(gLangIndex + 14)
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(TranslateAllCaptions)"
End Function

Private Function SetLocaleIndex(r_LangIndex As Long, r_LocaleID As Integer)
On Error GoTo ErrorHandler

    Select Case Languages.Text
        Case Languages.List(0)
            r_LangIndex = g_LANGINDEX_EN_US
            r_LocaleID = g_LOCALEID_EN_US
        
        Case Languages.List(1)
            r_LangIndex = g_LANGINDEX_JA
            r_LocaleID = g_LOCALEID_JA
        
        Case Languages.List(2)
            r_LangIndex = g_LANGINDEX_ZH_CN
            r_LocaleID = g_LOCALEID_ZH_CN
        
        Case Languages.List(3)
            r_LangIndex = g_LANGINDEX_KO
            r_LocaleID = g_LOCALEID_KO
                
        'INTERNATIONALIZATION: Add more languages here. Refer to the AIMLanguages table for Locales and LangIDs
        'NOTE: Be sure to
        '   (1) declare the langindex and localeid constants in AIMInclude.bas
        '   (2) add the translations to the Resource File with the index starting at
        '       n00, where n would be incremented by 200 for each new language.
        '       and the next two places would be for the string id.
        '       e.g. EN-US=0-199; JA=200-399 and so forth.
        
        Case Else
            r_LangIndex = g_LANGINDEX_EN_US       'Default to EN-US
            r_LocaleID = g_LOCALEID_EN_US
            
    End Select

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(SetLocaleIndex)"
End Function

Private Function GetLocaleIndex(p_LocaleID As Integer) As Long
On Error GoTo ErrorHandler

    Select Case p_LocaleID
        Case g_LOCALEID_JA
            GetLocaleIndex = g_LANGINDEX_JA
        
        Case g_LOCALEID_ZH_CN
            GetLocaleIndex = g_LANGINDEX_ZH_CN
            
        Case g_LOCALEID_KO
            GetLocaleIndex = g_LANGINDEX_KO
            
        'INTERNATIONALIZATION: Add more languages here. Refer to the AIMLanguages table for Locales and LangIDs
        'NOTE: Be sure to
        '   (1) declare the langindex and localeid constants in AIMInclude.bas
        '   (2) add the translations to the Resource File with the index starting at
        '       n00, where n would be incremented by 200 for each new language.
        '       and the next two places would be for the string id.
        '       e.g. EN-US=0-199; JA=200-399 and so forth.
        
        Case Else
            GetLocaleIndex = g_LANGINDEX_EN_US   'default to english
            
    End Select

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(GetLocaleIndex)"
End Function

Private Function RemoveSemicolons()
'Remove semi colons from static text
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    For IndexCounter = LogonLabel.LBound To LogonLabel.UBound
        If Right(LogonLabel(IndexCounter).Caption, 1) = ":" Then LogonLabel(IndexCounter).Caption = Left(LogonLabel(IndexCounter).Caption, Len(LogonLabel(IndexCounter).Caption) - 1)
    Next
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(RemoveSemicolons)", Err.Description
End Function

Private Function UpdateUserLanguage(p_Cn As ADODB.Connection)
On Error GoTo ErrorHandler
    
    Dim strSQL As String
    Dim cmUpdate As ADODB.Command
    
    strSQL = "UPDATE AIMUsers " & _
            " SET LangID = '" & m_UserLanguage & "'" & _
            " WHERE UserID = '" & txtUserId.Text & "'"
    
    Set cmUpdate = New ADODB.Command
    cmUpdate.ActiveConnection = p_Cn
    cmUpdate.CommandText = strSQL
    cmUpdate.CommandType = adCmdText
    cmUpdate.Execute
    
    If Not (cmUpdate Is Nothing) Then Set cmUpdate.ActiveConnection = Nothing
    Set cmUpdate = Nothing
    
Exit Function
ErrorHandler:
    If Not (cmUpdate Is Nothing) Then Set cmUpdate.ActiveConnection = Nothing
    Set cmUpdate = Nothing
    Err.Raise Err.Number, Err.source & "(UpdateUserLanguage)", Err.Description
End Function
