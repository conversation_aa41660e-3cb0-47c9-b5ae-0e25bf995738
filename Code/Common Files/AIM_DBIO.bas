Attribute VB_Name = "AIM_DBIO"
Option Explicit

Public Function AIMSysCheck( _
    Cn As ADODB.Connection, _
    Table As String _
) As Long
On Error GoTo ErrorHandler

    Dim rsSpaceUsed As ADODB.Recordset
    Dim source As String
        
    Set rsSpaceUsed = New ADODB.Recordset
    rsSpaceUsed.CursorLocation = adUseClient
    
    'Build source string
    source = "sp_spaceused '" + Trim(Table) + "' "
    rsSpaceUsed.Open source, Cn, adOpenForwardOnly, adLockReadOnly
    
    'Check for errors
    If Cn.Errors.Count > 0 Then
        AIMSysCheck = 0
        Exit Function
    End If
    
    'Get record counts
    If f_IsRecordsetOpenAndPopulated(rsSpaceUsed) Then
        AIMSysCheck = rsSpaceUsed!Rows
    Else
        AIMSysCheck = 0
    End If
    
    'CleanUp
    If f_IsRecordsetValidAndOpen(rsSpaceUsed) Then rsSpaceUsed.Close
    Set rsSpaceUsed = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsSpaceUsed) Then rsSpaceUsed.Close
    Set rsSpaceUsed = Nothing
    
    'sgajjela
     f_HandleErr , , , "AIM_DBIO::Form_Load", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMSysCheck)"
End Function

Public Function VerifyAccess( _
    UserID As String, _
    AimFunction As Integer, _
    AimFunctionDesc As String _
) As Integer
On Error GoTo ErrorHandler

    Dim Cn As ADODB.Connection
    Dim AIM_VerifyAccess_Sp As ADODB.Command
    Dim p1 As ADODB.Parameter
    Dim RtnCode As Integer
        
    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True)
    
    Select Case RtnCode
        Case FAIL
            Exit Function
    
        Case TOOMANY
            Exit Function

    End Select


'''SUJIT
    Dim AIM_VerifyAccessRole_Sp As ADODB.Command
    Set AIM_VerifyAccessRole_Sp = New ADODB.Command
    Set p1 = New ADODB.Parameter
    With AIM_VerifyAccessRole_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_VerifyAccessRole_Sp"
    
        Set p1 = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
        .Parameters.Append p1
        
        Set p1 = .CreateParameter("@RoleID", adVarChar, adParamInput, 12)
        .Parameters.Append p1
        
        Set p1 = .CreateParameter("@AIMFunction", adInteger, adParamInput)
        .Parameters.Append p1
    
         Set p1 = .CreateParameter("@AIMFunctionDesc", adVarChar, adParamInputOutput, 40)
        .Parameters.Append p1
       
       'Call the Stored Procedure
        .Parameters("@RoleId").Value = gRole
        .Parameters("@AIMFunction").Value = AimFunction
        .Parameters("@AIMFunctionDesc").Value = ""

        .Execute , , adExecuteNoRecords

        VerifyAccess = AIM_VerifyAccessRole_Sp.Parameters("@RETURN_VALUE").Value
        AimFunctionDesc = AIM_VerifyAccessRole_Sp.Parameters("@AIMFunctionDesc").Value
        'Translate this, since we have international languages now
         AimFunctionDesc = getTranslationResource(AimFunctionDesc)
    End With
    
    If Not (AIM_VerifyAccessRole_Sp Is Nothing) Then Set AIM_VerifyAccessRole_Sp.ActiveConnection = Nothing
    Set AIM_VerifyAccessRole_Sp = Nothing
    
    Set p1 = Nothing


'    Set AIM_VerifyAccess_Sp = New ADODB.Command
'    Set p1 = New ADODB.Parameter
'    With AIM_VerifyAccess_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_VerifyAccess_Sp"
'
'        Set p1 = .CreateParameter("@RETURN_VALUE", adInteger, adParamReturnValue)
'        .Parameters.Append p1
'
'        Set p1 = .CreateParameter("@UserId", adVarChar, adParamInput, 12)
'        .Parameters.Append p1
'
'        Set p1 = .CreateParameter("@AIMFunction", adInteger, adParamInput)
'        .Parameters.Append p1
'
'         Set p1 = .CreateParameter("@AIMFunctionDesc", adVarChar, adParamInputOutput, 40)
'        .Parameters.Append p1
'
'       'Call the Stored Procedure
'        .Parameters("@UserID").Value = UserId
'        .Parameters("@AIMFunction").Value = AimFunction
'        .Parameters("@AIMFunctionDesc").Value = ""
'
'        .Execute , , adExecuteNoRecords
'
'        VerifyAccess = AIM_VerifyAccess_Sp.Parameters("@RETURN_VALUE").Value
'        AimFunctionDesc = AIM_VerifyAccess_Sp.Parameters("@AIMFunctionDesc").Value
'        'Translate this, since we have international languages now
'        AimFunctionDesc = getTranslationResource(AimFunctionDesc)
'    End With
'
'    If Not (AIM_VerifyAccess_Sp Is Nothing) Then Set AIM_VerifyAccess_Sp.ActiveConnection = Nothing
'    Set AIM_VerifyAccess_Sp = Nothing
'
'    Set p1 = Nothing
'
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    If Not (AIM_VerifyAccess_Sp Is Nothing) Then Set AIM_VerifyAccess_Sp.ActiveConnection = Nothing
    Set AIM_VerifyAccess_Sp = Nothing
    
    Set p1 = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'sgajjela
    ''Call f_HandleErr(Null, False, Null, Null, Null, "AIM_DBIO.VerifyAccess", Null, "DRGE")
     f_HandleErr , , , "AIM_DBIO::VerifyAccess", Now, gDRGeneralError, True, Err
     'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.VerifyAccess)"
End Function

Public Function AIMMethodsLoad( _
    Cn As ADODB.Connection, _
    AIMMethods() As METHOD_RCD _
) As Integer
On Error GoTo ErrorHandler

    Dim AIM_Methods_Load_Sp As ADODB.Command
    Dim i As Integer
    Dim Row As Integer
    Dim rsAIMMethods As ADODB.Recordset
     
    'Redimension Arrays
    ReDim AIMMethods(1 To 25)
         
    'Define the Stored Procedures
    Set AIM_Methods_Load_Sp = New ADODB.Command
    With AIM_Methods_Load_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Methods_Load_Sp"
        .Parameters(0).Direction = adParamReturnValue
    End With
    
    'Load the table
    Set rsAIMMethods = New ADODB.Recordset
    rsAIMMethods.CursorLocation = adUseClient
    rsAIMMethods.Open "AIM_Methods_Load_Sp", Cn, adOpenStatic, adLockReadOnly

    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(rsAIMMethods) = False Then
        'Add ErrorHandler here
    Else
        Do Until rsAIMMethods.EOF
            Row = Row + 1
            
            If Row > UBound(AIMMethods) Then
                ReDim Preserve AIMMethods(1 To UBound(AIMMethods) + 10)
            End If
        
            AIMMethods(Row).MethodId = rsAIMMethods("MethodId").Value
            AIMMethods(Row).MethodStatus = rsAIMMethods("MethodStatus").Value
            AIMMethods(Row).MethodDesc = rsAIMMethods("MethodDesc").Value
            AIMMethods(Row).ApplySeasonsIndex = rsAIMMethods("ApplySeasonsIndex").Value
            AIMMethods(Row).ApplyTrend = rsAIMMethods("ApplyTrend").Value
            
            rsAIMMethods.MoveNext
        Loop
        
        If Row > 0 Then
            ReDim Preserve AIMMethods(1 To Row)
        End If
    End If
    
    AIMMethodsLoad = Row
    
    'Clean up
    If f_IsRecordsetValidAndOpen(rsAIMMethods) Then rsAIMMethods.Close
    Set rsAIMMethods = Nothing
    If Not (AIM_Methods_Load_Sp Is Nothing) Then Set AIM_Methods_Load_Sp.ActiveConnection = Nothing
    Set AIM_Methods_Load_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMMethods) Then rsAIMMethods.Close
    Set rsAIMMethods = Nothing
    If Not (AIM_Methods_Load_Sp Is Nothing) Then Set AIM_Methods_Load_Sp.ActiveConnection = Nothing
    Set AIM_Methods_Load_Sp = Nothing
    
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMMethodsLoad)"
     f_HandleErr , , , "AIM_DBIO::AIMMethodsLoad", Now, gDRGeneralError, True, Err
End Function

Public Function AIMSeasons_GetKey( _
    AIM_AIMSeasons_GetKey_Sp As ADODB.Command, _
    SAVersion As Integer, _
    SaId As String, _
    Action As SQL_ACTIONS _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Get the Seasonality Data
    With AIM_AIMSeasons_GetKey_Sp
        .Parameters("@SAVersion").Value = SAVersion
        .Parameters("@SaId").Value = SaId
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
        .Execute , , adExecuteNoRecords
        
        RtnCode = .Parameters(0).Value
        
        If RtnCode = SUCCEED Then
            SAVersion = .Parameters("@SAVersion").Value
            SaId = .Parameters("@SaId").Value
            
        Else
            SAVersion = 0
            SaId = ""
            
        End If
    
    End With
    
    AIMSeasons_GetKey = RtnCode
    
Exit Function
ErrorHandler:
    'sgajjela
     f_HandleErr , , , "AIM_DBIO::AIMSeasons_getKey", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMSeasons_GetKey)"
End Function

Public Function AIMVendors_GetEq( _
    AIM_AIMVendors_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    VnIdKey As String, _
    AssortKey As String _
) As Integer
On Error GoTo ErrorHandler

    'Get the Seasons Data
    With AIM_AIMVendors_GetEq_Sp
        .Parameters(0).Value = 0
        .Parameters("@VnId").Value = VnIdKey
        .Parameters("@Assort").Value = AssortKey
    
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_AIMVendors_GetEq_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        rs.Requery
    End If
    
    AIMVendors_GetEq = AIM_AIMVendors_GetEq_Sp(0).Value
    
Exit Function
ErrorHandler:
    'sgajjela
     f_HandleErr , , , "AIM_DBIO::AIMVendors_GetEq", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMVendors_GetEq)"
End Function

Public Function AIMVendors_GetKey( _
    AIM_AIMVendors_GetKey_Sp As ADODB.Command, _
    VnId As String, _
    Assort As String, _
    Action As SQL_ACTIONS _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Get the Vendor/Assortment Data
    With AIM_AIMVendors_GetKey_Sp
        .Parameters("@VnId").Value = VnId
        .Parameters("@Assort").Value = Assort
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
        .Execute , , adExecuteNoRecords
        
        RtnCode = .Parameters(0).Value
        
        If RtnCode = SUCCEED Then
            VnId = .Parameters("@VnId").Value
            Assort = .Parameters("@Assort").Value
            
        Else
            VnId = ""
            Assort = ""
            
        End If
    
    End With
    
    AIMVendors_GetKey = RtnCode

Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_DBIO::AIMVendors_GetEq", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMVendors_GetKey)"
End Function

Function AIMYears_Load( _
    p_CommandObject As ADODB.Command, _
    p_RecordSet As ADODB.Recordset, _
    Optional p_FiscalYear As Integer = 0 _
) As Integer
'Merged two former functions - AIMYears_GetEq and AIMYearsLoad - into one
' by including the fiscal year as an optional parameter
On Error GoTo ErrorHandler

    'Get the Seasons Data
    With p_CommandObject
        .Parameters(0).Value = 0
        .Parameters("@FiscalYear").Value = p_FiscalYear
    End With
    
    If Not f_IsRecordsetValidAndOpen(p_RecordSet) Then
        p_RecordSet.Open p_CommandObject
    Else
        If p_RecordSet.EditMode = adEditInProgress Then
            p_RecordSet.CancelUpdate
        End If
        
        p_RecordSet.Requery
    End If
    
    AIMYears_Load = p_CommandObject(0).Value
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_DBIO::AIMYears_Load", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMYears_Load)"
End Function

Public Function ItemPerformance( _
    AIM_ItemPerformance_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    LcIdKey As String, _
    ItemKey As String, _
    StartYear As Integer, _
    EndYear As Integer _
) As Integer
On Error GoTo ErrorHandler

    'Get the History Records
    With AIM_ItemPerformance_Sp
        .Parameters("@LcIdKey").Value = LcIdKey
        .Parameters("@ItemKey").Value = ItemKey
        .Parameters("@StartYear").Value = StartYear
        .Parameters("@EndYear").Value = EndYear
    
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_ItemPerformance_Sp
    Else
        rs.Requery
    End If
    
    ItemPerformance = AIM_ItemPerformance_Sp(0).Value

Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_DBIO::ItemPerformance", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.ItemPerformance)"
End Function

Public Function ReleasedPurchaseOrders( _
    AIM_ReleasedPurchaseOrders_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    ById As String, _
    RelDate As Date _
) As Integer
On Error GoTo ErrorHandler

    'Get the Released Purchase Orders from the P.O. Statistics Table
    With AIM_ReleasedPurchaseOrders_Sp
        .Parameters("@ById").Value = ById
        .Parameters("@RelDate").Value = RelDate
    
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_ReleasedPurchaseOrders_Sp
    Else
        rs.Requery
    End If
    
    ReleasedPurchaseOrders = AIM_ReleasedPurchaseOrders_Sp(0).Value

Exit Function
ErrorHandler:

    f_HandleErr , , , "AIM_DBIO::ReleasedPurchaseOrders", Now, gDRGeneralError, True, Err
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.ReleasedPurchaseOrders)"
End Function

Public Function RevCycles_GetEq( _
    AIM_RevCycles_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    RevCycle As String _
) As Integer
    
On Error GoTo ErrorHandler
   
    'Get the Review Cycle Data
    With AIM_RevCycles_GetEq_Sp
        '.Parameters(0).Value = 0
        .Parameters("@RevCycle").Value = RevCycle
    
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_RevCycles_GetEq_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        rs.Requery
    End If
    
    RevCycles_GetEq = AIM_RevCycles_GetEq_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.RevCycles_GetEq)"
     f_HandleErr , , , "AIM_DBIO::RevCycles_GetEq", Now, gDRGeneralError, True, Err
End Function

Public Function RevCycles_GetKey( _
    AIM_RevCycles_GetKey_Sp As ADODB.Command, _
    RevCycle As String, _
    rs As ADODB.Recordset, _
    Action As SQL_ACTIONS _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Get the RevCycle Key
    With AIM_RevCycles_GetKey_Sp
        .Parameters("@RevCycle").Value = RevCycle
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
        
        If Not f_IsRecordsetValidAndOpen(rs) Then
            rs.Open AIM_RevCycles_GetKey_Sp
        Else
            On Error Resume Next
            rs.Requery
            If Err.Number > 0 Then
                Err.Clear
'                If rs.EditMode = adEditInProgress Then
'                    rs.CancelUpdate
'                End If
                rs.Requery
            End If
            On Error GoTo ErrorHandler
        End If
        
        RtnCode = .Parameters(0).Value
        
        If RtnCode = SUCCEED Then
            RevCycle = .Parameters("@RevCycle").Value
        Else
            RevCycle = ""
        End If
    
    End With
    
    RevCycles_GetKey = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.RevCycles_GetKey)"
     f_HandleErr , , , "AIM_DBIO::RevCycles_GetKey", Now, gDRGeneralError, True, Err
End Function

Public Function AIMOptions_GetKey( _
    AIM_AIMOptions_GetKey_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    OptionId As String, _
    Action As SQL_ACTIONS _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Get the Seasonality Data
    With AIM_AIMOptions_GetKey_Sp
        .Parameters("@OptionId").Value = OptionId
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
        
        If Not f_IsRecordsetValidAndOpen(rs) Then
            rs.Open AIM_AIMOptions_GetKey_Sp
        Else
            If rs.EditMode = adEditInProgress Or rs.EditMode = adEditAdd Then
                rs.CancelUpdate
            End If
            
            rs.Requery
        End If
        
        RtnCode = .Parameters(0).Value
        
        If RtnCode = SUCCEED Then
            OptionId = .Parameters("@OptionId").Value
        Else
            OptionId = ""
        End If
    
    End With
    
    AIMOptions_GetKey = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMOptions_GetKey)"
     f_HandleErr , , , "AIM_DBIO::AIMOptions_GetKey", Now, gDRGeneralError, True, Err
End Function

Public Function AIMPromotions_GetKey( _
    AIM_AIMPromotions_GetKey_Sp As ADODB.Command, _
    PmId As String, _
    rs As ADODB.Recordset, _
    Action As SQL_ACTIONS _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    
    'Get the Seasonality Data
    With AIM_AIMPromotions_GetKey_Sp
        .Parameters("@PmId").Value = PmId
        .Parameters("@Action").Value = Action
        .Parameters(0).Value = 0
    
        If Not f_IsRecordsetValidAndOpen(rs) Then
            rs.Open AIM_AIMPromotions_GetKey_Sp
        Else
            rs.Requery
        End If
    
        RtnCode = .Parameters(0).Value
        
        If RtnCode = SUCCEED Then
            PmId = .Parameters("@PmId").Value
        Else
            PmId = ""
        End If
    
    End With
    
    AIMPromotions_GetKey = RtnCode

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMPromotions_GetKey)"
     f_HandleErr , , , "AIM_DBIO::AIMPromotions_GetKey", Now, gDRGeneralError, True, Err
End Function

'***********************************************************************
' Function Name:    SQLConnection(Cn)
'
' Description:      Open a connection to SQL Server.
'
' Parameters:       Cn      ADODB Connection
'                   Action  Action Code
'                           0 = Open Connection
'                           1 = Close Connection
'                   MsgOpt  True  = Display Message
'                           False = No Messages
'
' Returns:          FAIL, SUCCEED, or TOOMANY
'
' By: RES           Date: 06/30/97
'
' Copyright (c) Technology Advantage, Inc. Alpharetta, GA 1997
'***********************************************************************
Public Function SQLConnection( _
    Cn As ADODB.Connection, _
    Action As CONNECTION_ACTIONS, _
    Optional MsgOption As Boolean = False, _
    Optional JobDatabase As Boolean = False, _
    Optional Translation As Boolean = False, _
    Optional strFormCaption As String _
) As Integer
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim strMessage As String
    Dim strMessage1 As String
    Dim strFormName  As String
    
    'Set Default return val to failure
    SQLConnection = FAIL
    
''''************************************************************
''''A.Stocksdale 2004/01/23 DO NOT CHECK MAXSQLCONNECTIONS
'''' The function's errorhandler should be able to write out a log file
'''' if the ADO object exceeds the number of connections allowed.
''''************************************************************
'''    'Test for the number of open connections
'''    If gNbrConnections >= MAXSQLCONNECTIONS And Action = 0 Then
'''        If MsgOption Then
'''            If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US
'''            strMessage = LoadResString(gLangIndex + 19) '"Too many SQL Connections open -- Form Load aborted."
'''            MsgBox strMessage, vbCritical, LoadResString(gLangIndex + 0)
'''
'''            Write_Message ""
'''            Screen.MousePointer = vbNormal
'''            SQLConnection = TOOMANY
'''            Exit Function
'''        Else
'''            'Nothing
'''        End If
'''
'''    End If
''''************************************************************
''''A.Stocksdale 2004/01/23 END COMMENT
''''************************************************************

    Select Case Action
        Case 0          'Open Connection
            Cn.Provider = "SQLOleDb"
            Cn.ConnectionTimeout = 10
            Cn.CommandTimeout = 300            'Five minutes
            Cn.Properties("Data Source").Value = gServer
            If JobDatabase Then
                Cn.Properties("Initial Catalog").Value = "msdb"
            Else
                Cn.Properties("Initial Catalog").Value = gDataBase
            End If
            Cn.ConnectionString = g_CONNECTSTRING
            
            'Set security option
            If gWinNTAuth = "Y" Then
                Cn.Properties("Integrated Security").Value = "SSPI"
            Else
                Cn.Properties("User ID").Value = gUserID
                Cn.Properties("Password").Value = gPassWord
            End If
             
            Cn.Open
            
            gNbrConnections = gNbrConnections + 1
            SQLConnection = SUCCEED
            
            Cn.Errors.Clear
    
        Case 1          'Close Connection
            gNbrConnections = gNbrConnections - 1
            If (Not Cn Is Nothing) Then
                If Cn.State = adStateOpen Then Cn.Close
            End If
            Set Cn = Nothing
            
            If Err <> 0 Then
                SQLConnection = FAIL
                Exit Function
            Else
                SQLConnection = SUCCEED
            End If
            
    End Select
    
Exit Function
ErrorHandler:
    If Err.Number <> 0 Then 'No Connection Information found for initial logon
        'Prepare error message
        If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US
        strMessage = LoadResString(gLangIndex + 17) '"Error(s) logging on to"
        strMessage = strMessage & ": " & gServer & vbCrLf
        For i = 0 To Cn.Errors.Count - 1
            strMessage = strMessage & Cn.Errors(i).Description + vbCrLf
        Next i
        strMessage = strMessage & vbCrLf & LoadResString(gLangIndex + 18) '"Please try again."
        
        'Clean up
        If (Not Cn Is Nothing) Then
            If Cn.State = adStateOpen Then Cn.Close
        End If
        Set Cn = Nothing
        
        'Display/Escalate error
        If MsgOption Then
            MsgBox strMessage & " " & strMessage1, vbOKOnly + vbExclamation, LoadResString(gLangIndex + 0)
            'Set return and other status
            SQLConnection = FAIL
            Write_Message ""
            Screen.MousePointer = vbNormal
        Else
            'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.SQLConnection)"
             f_HandleErr , , , "AIM_DBIO::SQLConnection", Now, gDRGeneralError, True, Err
        End If
    End If
End Function

Public Function AIMCalendar_Load( _
    Cn As ADODB.Connection, _
    AIMYears() As AIMYEARS_RCD, _
    AIMDays() As AIMDAYS_RCD _
) As Integer

On Error GoTo ErrorHandler

    Dim AIM_AIMDays_Load_Sp As ADODB.Command
    Dim AIM_AIMYears_Load_Sp As ADODB.Command
    
    Dim rsTemp As ADODB.Recordset
    
    Dim Row As Long
    
    'Redimension Arrays
    ReDim AIMDays(1 To 3000)
    ReDim AIMYears(1 To 10)
    
    'Define the Stored Procedures
    Set AIM_AIMDays_Load_Sp = New ADODB.Command
    With AIM_AIMDays_Load_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMDays_Load_Sp"
        .Parameters(0).Direction = adParamReturnValue
    
    End With
        
    Set AIM_AIMYears_Load_Sp = New ADODB.Command
    With AIM_AIMYears_Load_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMYears_Load_Sp"
        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
            "RETURN", adInteger, adParamReturnValue)
        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
            "@FiscalYear", adInteger, adParamInput, , 0)
    End With
        
    'Load the AIM Calendar/AIM Years
    Set rsTemp = New ADODB.Recordset
    With rsTemp
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    'Load the year collection
    rsTemp.Open AIM_AIMYears_Load_Sp
    
    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(rsTemp) = False Then
        'Add ErrorHandler here
    Else
        Do Until rsTemp.EOF
            Row = Row + 1
            If Row > UBound(AIMYears) Then
                ReDim Preserve AIMYears(1 To UBound(AIMYears) + 10)
            End If
        
            AIMYears(Row).FiscalYear = rsTemp("FiscalYear").Value
            AIMYears(Row).FYStartDate = rsTemp("FYStartDate").Value
            AIMYears(Row).FYEndDate = rsTemp("FYEndDate").Value
            AIMYears(Row).NbrWeeks = rsTemp("NbrWeeks").Value
            
            rsTemp.MoveNext
            
        Loop
    
        If Row > 0 Then
            ReDim Preserve AIMYears(1 To Row)
        End If
        
        If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
        Set rsTemp = Nothing
        
        Set rsTemp = New ADODB.Recordset
        With rsTemp
            .CursorLocation = adUseClient
            .CursorType = adOpenStatic
            .LockType = adLockReadOnly
        End With
            
        rsTemp.Open AIM_AIMDays_Load_Sp
        Row = 0
        
        If f_IsRecordsetOpenAndPopulated(rsTemp) Then
            Do Until rsTemp.EOF
                Row = Row + 1
                If Row > UBound(AIMDays) Then
                    ReDim Preserve AIMDays(1 To UBound(AIMDays) + 10)
                End If
                
                AIMDays(Row).FYDate = rsTemp("FYDate").Value
                AIMDays(Row).DayStatus = rsTemp("DayStatus").Value
                'Sri DP Change
                'AIMDays(Row).FYPeriod = rsTemp("FcstInterval_Weeks").Value
                AIMDays(Row).FYPeriod = rsTemp("FYPeriod_Weeks").Value
                rsTemp.MoveNext
                
            Loop
        End If
        
        If Row > 0 Then
            ReDim Preserve AIMDays(1 To Row)
        End If
        
    End If
    
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    
    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Load_Sp = Nothing
        
    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Load_Sp = Nothing
    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing
    
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMCalendar_Load)"
    f_HandleErr , , , "AIM_DBIO::AIMCalendar_Load", Now, gDRGeneralError, True, Err
End Function

Public Function AIMLocations_GetEq( _
    AIM_AIMLocations_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    LcIdKey As String _
) As Integer
On Error GoTo ErrorHandler
        
    'Get the Location Data
    AIM_AIMLocations_GetEq_Sp(0).Value = 0
    AIM_AIMLocations_GetEq_Sp("@LcIdKey").Value = LcIdKey
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_AIMLocations_GetEq_Sp
    Else
        rs.Requery
    End If
    
    AIMLocations_GetEq = AIM_AIMLocations_GetEq_Sp(0).Value
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMLocations_GetEq)"
     f_HandleErr , , , "AIM_DBIO::AIMLocations_GetEq", Now, gDRGeneralError, True, Err
End Function

Public Function AIMDays_Load( _
    AIM_AIMDays_Load_Sp As ADODB.Command, _
    rs As ADODB.Recordset _
) As Integer
On Error GoTo ErrorHandler
    
    'Get the Location Data
    AIM_AIMDays_Load_Sp(0).Value = 0
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_AIMDays_Load_Sp
    Else
        rs.Requery
    End If
    
    AIMDays_Load = AIM_AIMDays_Load_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMDays_Load)"
     f_HandleErr , , , "AIM_DBIO::AIMDays_Load", Now, gDRGeneralError, True, Err
End Function


Public Function AIMOptions_GetEq( _
    AIM_AIMOptions_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    OptionIdKey As String _
) As Integer
On Error GoTo ErrorHandler

    'Get the Location Data
    AIM_AIMOptions_GetEq_Sp(0).Value = 0
    AIM_AIMOptions_GetEq_Sp("@OptionIdKey").Value = OptionIdKey
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_AIMOptions_GetEq_Sp
    Else
        rs.Requery
    End If
    
    AIMOptions_GetEq = AIM_AIMOptions_GetEq_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMOptions_GetEq)"
     f_HandleErr , , , "AIM_DBIO::AIMOptions_GetEq", Now, gDRGeneralError, True, Err
End Function

Public Function AIMSeasons_GetEq( _
    AIM_AIMSeasons_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    SaIdKey As String, _
    SAVersion As Integer _
) As Integer
On Error GoTo ErrorHandler
    
    'Get the Seasons Data
    With AIM_AIMSeasons_GetEq_Sp
        .Parameters(0).Value = 0
        .Parameters("@SaIdKey").Value = SaIdKey
        .Parameters("@SAVersion").Value = SAVersion
    
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_AIMSeasons_GetEq_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        
        rs.Requery
    End If
    
    AIMSeasons_GetEq = AIM_AIMSeasons_GetEq_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.AIMSeasons_GetEq)"
     f_HandleErr , , , "AIM_DBIO::AIMSeasons_GetEq", Now, gDRGeneralError, True, Err
End Function

Public Function Item_GetEq( _
    AIM_Item_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    LcIdKey As String, _
    ItemKey As String _
) As Integer
On Error GoTo ErrorHandler
    
    'Get the Item Data
    AIM_Item_GetEq_Sp(0).Value = 0
    AIM_Item_GetEq_Sp("@LcIdKey").Value = LcIdKey
    AIM_Item_GetEq_Sp("@ItemKey").Value = ItemKey
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_Item_GetEq_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        
        rs.Requery
    End If
    
    Item_GetEq = AIM_Item_GetEq_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.Item_GetEq)"
     f_HandleErr , , , "AIM_DBIO::Item_GetEq", Now, gDRGeneralError, True, Err
End Function

Public Function Item_GetKey( _
    Item_GetKey_Sp As ADODB.Command, _
    ByRef LcIdKey As String, _
    ByRef ItemKey As String, _
    Action As SQL_ACTIONS _
) As Integer
On Error GoTo ErrorHandler

    Item_GetKey = FAIL
    
    Item_GetKey_Sp("@LcId").Value = LcIdKey
    Item_GetKey_Sp("@Item").Value = ItemKey
    Item_GetKey_Sp("@action").Value = Action
        
    Item_GetKey_Sp.Execute , , adExecuteNoRecords

    If Item_GetKey_Sp(0).Value = SUCCEED Then
        'Assign Key Values
        LcIdKey = Item_GetKey_Sp("@LcId").Value
        ItemKey = Item_GetKey_Sp("@Item").Value
        
        Item_GetKey = SUCCEED
        
    End If
    
Exit Function
ErrorHandler:
     f_HandleErr , , , "AIM_DBIO::Item_GetKey", Now, gDRGeneralError, True, Err
     'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.Item_GetKey)"
End Function

Public Function ItemHistory_GetEq( _
    AIM_ItemHistory_GetEq_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    LcIdKey As String, _
    ItemKey As String, _
    StartYear As Integer, _
    EndYear As Integer, _
    DemandSource As String _
) As Integer
On Error GoTo ErrorHandler

    'Get the Location Data
    With AIM_ItemHistory_GetEq_Sp
        .Parameters(0).Value = 0
        .Parameters("@LcIdKey").Value = LcIdKey
        .Parameters("@ItemKey").Value = ItemKey
        .Parameters("@StartYear").Value = StartYear
        .Parameters("@EndYear").Value = EndYear
        .Parameters("@DemandSource").Value = DemandSource
    
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_ItemHistory_GetEq_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        
        rs.Requery
    End If
    
    ItemHistory_GetEq = AIM_ItemHistory_GetEq_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.ItemHistory_GetEq)"
     f_HandleErr , , , "AIM_DBIO::ItemHistory_GetEq", Now, gDRGeneralError, True, Err
End Function

Public Function MasterBudgetFcst_GetEq( _
    AIM_ForecastMaster_Get_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    RepKey As Long, _
    LcIdKey As String, _
    ItemKey As String, _
    PdStartDate As Date, _
    PdEndDate As Date _
) As Integer
On Error GoTo ErrorHandler

    'Get the Location Data
    With AIM_ForecastMaster_Get_Sp
        .Parameters(0).Value = 0
        .Parameters("@RepositoryKey").Value = RepKey
        .Parameters("@LcId").Value = LcIdKey
        .Parameters("@Item").Value = ItemKey
        .Parameters("@PeriodBegDate").Value = PdStartDate
        .Parameters("@PeriodEndDate").Value = PdEndDate
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_ForecastMaster_Get_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        
        rs.Requery
    End If
    
    MasterBudgetFcst_GetEq = AIM_ForecastMaster_Get_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_ForecastMaster_Get_Sp)"
     f_HandleErr , , , "AIM_DBIO::AIM_ForecastMaster_Get_Sp", Now, gDRGeneralError, True, Err
End Function
Public Function BudgetFcst_GetEq( _
    AIM_Repository_Get_Sp As ADODB.Command, _
    rs As ADODB.Recordset, _
    RepKey As Long, _
    LcIdKey As String, _
    ItemKey As String, _
    PdStartDate As Date, _
    PdEndDate As Date _
) As Integer
On Error GoTo ErrorHandler

    'Get the Location Data
    With AIM_Repository_Get_Sp
        .Parameters(0).Value = 0
        .Parameters("@RepositoryKey").Value = RepKey
        .Parameters("@LcId").Value = LcIdKey
        .Parameters("@Item").Value = ItemKey
        .Parameters("@PdStartDate").Value = PdStartDate
        .Parameters("@PdEndDate").Value = PdEndDate
    End With
    
    If Not f_IsRecordsetValidAndOpen(rs) Then
        rs.Open AIM_Repository_Get_Sp
    Else
        If rs.EditMode = adEditInProgress Then
            rs.CancelUpdate
        End If
        
        rs.Requery
    End If
    
    BudgetFcst_GetEq = AIM_Repository_Get_Sp(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIM_DBIO.BudgetFcst_GetEq)"
     f_HandleErr , , , "AIM_DBIO::BudgetFcst_GetEq", Now, gDRGeneralError, True, Err
End Function


