Attribute VB_Name = "AIMFcstFilterCrit"
'*****************************************************************************
' Copyright (c) 2004 SSA Global. All rights reserved.
'*****************************************************************************
'
'   Fcst_FilterCrit.bas
'
'   Version Number - 1.0
'   Last Updated   - 2004/10/05
'   Updated By     - Annalakshmi Stocksdale
'
'   This module contains the procedures required for interaction with the
'   re-designed Forecast Item Filter screen.
'
'   See related updates to the Forecast Modification and Setup screens.
'
'*****************************************************************************
' This file contains trade secrets of SSA Global. No part
' may be reproduced or transmitted in any form by any means or for any purpose
' without the express written permission of SSA Global.
'*****************************************************************************
Option Explicit
Option Base 0   'Sets default for all arrays in this module to start at 0

'The Criteria Array has a number of static fields starting from the first index.
'The following constant identifies the column index for the last static field in this array
Public Const CRIT_OFFSET As Integer = 1
Public Const CRIT_TOTAL As Integer = 20

'The Criteria Array lists fields in rows and values in columns.
'Therefore, the following constants are used for rowindices to associate them with specific database fields.
Public Const CRIT_IDX_ITSTAT = 0
Public Const CRIT_IDX_LSTATUS = 1
Public Const CRIT_IDX_LDIVISION = 2
Public Const CRIT_IDX_LREGION = 3
Public Const CRIT_IDX_LUSERDEFINED = 4
Public Const CRIT_IDX_CLASS1 = 5
Public Const CRIT_IDX_CLASS2 = 6
Public Const CRIT_IDX_CLASS3 = 7
Public Const CRIT_IDX_CLASS4 = 8
Public Const CRIT_IDX_BYID = 9
Public Const CRIT_IDX_LCID = 10
Public Const CRIT_IDX_VNID = 11
Public Const CRIT_IDX_ASSORT = 12
Public Const CRIT_IDX_ITEM = 13
Public Const CRIT_IDX_DFTBYID = 14
Public Const CRIT_IDX_DFTREVIEWERID = 15
Public Const CRIT_IDX_DEMAMADSOURCE = 16
Public Const CRIT_IDX_LEADTIMESOURCE = 17
Public Const CRIT_IDX_LTYPE = 18
Public Const CRIT_IDX_LRANK = 19
Public Const CRIT_IDX_DROPSHIP = 20
'The Criteria Array lists fields in rows and values in columns.
'Therefore, the following constants are used to identify a specific row with the corresponding database field name.
Public Const CRIT_COL_ITSTAT = "ITSTAT"
Public Const CRIT_COL_LSTATUS = "LSTATUS"
Public Const CRIT_COL_LDIVISION = "LDIVISION"
Public Const CRIT_COL_LREGION = "LREGION"
Public Const CRIT_COL_LUSERDEFINED = "LUSERDEFINED"
Public Const CRIT_COL_CLASS1 = "CLASS1"
Public Const CRIT_COL_CLASS2 = "CLASS2"
Public Const CRIT_COL_CLASS3 = "CLASS3"
Public Const CRIT_COL_CLASS4 = "CLASS4"
Public Const CRIT_COL_BYID = "BYID"
Public Const CRIT_COL_LCID = "LCID"
Public Const CRIT_COL_VNID = "VNID"
Public Const CRIT_COL_ASSORT = "ASSORT"
Public Const CRIT_COL_ITEM = "ITEM"
Public Const CRIT_COL_DFTBYID = "DFT_BYID"
Public Const CRIT_COL_DFTREVIEWERID = "DFT_REVIEWERID"
Public Const CRIT_COL_DEMAMADSOURCE = "DEMANDSOURCE"
Public Const CRIT_COL_LEADTIMESOURCE = "LEADTIMESOURCE"
Public Const CRIT_COL_LTYPE = "LTYPE"
Public Const CRIT_COL_LRANK = "LRANK"
Public Const CRIT_COL_DROPSHIP = "DROPSHIP"
'The comparison operators used for the dropdowns will use the following static values
Public Const COMP_EMPTY As String = ""
Public Const COMP_EQ As String = "="
Public Const COMP_GT As String = ">"
Public Const COMP_GTEQ As String = ">="
Public Const COMP_LT As String = "<"
Public Const COMP_LTEQ As String = "<="
Public Const COMP_NOTEQ As String = "<>"
Public Const COMP_LIKE As String = "LIKE"
Public Const COMP_BETWEEN As String = "BETWEEN"
Public Const COMP_AND As String = " AND "
Public Const COMP_OR As String = " OR "
Public Const COMP_IN As String = "IN"
Public Const COMP_NOTIN As String = "NOT IN"
Public Const COMP_ERROR As String = "ERROR"
'The criteria text passed to and from the Item Filter screen will use the following delimiters
Public Const DELIM_HEADER As String = ":"   'Chr(58)      'colon :
Public Const DELIM_ROW As String = ";"  'Chr(59)         'semi-colon ;
Public Const DELIM_COLUMN As String = ","   'Chr(44)      'comma ,
Public Const DELIM_LINE As String = vbCrLf  'Chr(13) & Chr(10)      'carriage-return and line-feed
Public Const DELIM_SQLQUOTE As String = "'" 'Chr(39)        'single-quote '
Public Const KEYWORD_SELECT As String = " SELECT "
Public Const KEYWORD_GROUPBY As String = " GROUP BY "
Public Const KEYWORD_ORDERBY As String = " ORDER BY "

Public Function g_GetSAVersion(pr_Cn As ADODB.Connection) As Integer
On Error GoTo ErrorHandler

    Dim GetSaVersion_Sp As ADODB.Command
    
    'Set default to failure
    g_GetSAVersion = -1
    
    'Fetch from database
    Set GetSaVersion_Sp = New ADODB.Command
    With GetSaVersion_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_GetSaVersion_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Execute , , adExecuteNoRecords
    End With
    
    g_GetSAVersion = GetSaVersion_Sp.Parameters("RETURN").Value
    
    'CleanUp
    If Not (GetSaVersion_Sp Is Nothing) Then Set GetSaVersion_Sp.ActiveConnection = Nothing
    Set GetSaVersion_Sp = Nothing
    
Exit Function
ErrorHandler:
    If Not (GetSaVersion_Sp Is Nothing) Then Set GetSaVersion_Sp.ActiveConnection = Nothing
    Set GetSaVersion_Sp = Nothing
    'Err.Raise Err.Number, Err.source & "{g_GetSAVersion}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_GetSAVersion", Now, gDRJobError, True, Err
End Function

Public Function g_QueryForFcstItems( _
    p_OnlyFilters As Boolean, _
    p_WhereClause As String, _
    p_SAVersion As Integer, _
    Optional p_NoOrderBy As Boolean, _
    Optional p_ShortList As Boolean _
) As String
On Error GoTo ErrorHandler

    Dim SelectClause As String
    Dim JoinClause As String
    Dim OtherClauses As String
    
    Select Case p_OnlyFilters
    Case True
        If p_ShortList = True Then
            'Limit the display to values that won't cause confusion re selections/duplications
            'This is applicable to the "data for selected criterion" grid of the Forecast Filter
            SelectClause = SelectClause & vbTab & "Item.Item, Item.ItDesc" & vbCrLf
        Else
            'Limit the returned set to the values that are directly related to the Forecast Filter
            'This is applicable to the "filtered items" grid of the Forecast Filter
            SelectClause = SelectClause & vbTab & "Item.Item, Item.LcID," & vbCrLf
            SelectClause = SelectClause & vbTab & "Item.ItDesc, Item.ItStat," & vbCrLf
            SelectClause = SelectClause & vbTab & "Item.VnID, Item.Assort, Item.ByID," & vbCrLf
            SelectClause = SelectClause & vbTab & "Item.Class1, Item.Class2, Item.Class3, Item.Class4," & vbCrLf
            SelectClause = SelectClause & vbTab & "AIMLocations.LStatus, AIMLocations.LDivision," & vbCrLf
            SelectClause = SelectClause & vbTab & "AIMLocations.LRegion, AIMLocations.LUserDefined" & vbCrLf
        End If
    Case False
        'Return all columns that might be required in the forecasting process.
        'This is applicable to all Forecast calculations
        SelectClause = SelectClause & vbTab & "Item.Item, Item.Lcid, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.VelCode, Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.SaId, Item.PmId, Item.Weight, Item.Cube, Item.Price, Item.Cost, Item.UOM," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.ConvFactor, Item.BuyingUOM, Item.Oh, Item.Oo, Item.ComStk," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.BkOrder, Item.BkComStk, Item.FcstMethod, Item.FcstDemand, Item.UserFcst," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.UserFcstExpDate, Item.MAE, Item.Trend, Item.FcstUpdCyc, Item.PlnTT," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.Accum_Lt, Item.ReviewTime, Item.OrderPt, Item.OrderQty, Item.SafetyStock," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.FcstRT, Item.FcstLT, Item.Fcst_Month, Item.Fcst_Quarter, Item.Fcst_Year," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMOptions.HiTrndL," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51,"
        SelectClause = SelectClause & vbTab & "AIMSeasons.BI52," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMVendors.VName,"
        SelectClause = SelectClause & vbTab & "AIMLocations.DmdScalingFactor, AIMLocations.ScalingEffUntil," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmStatus = isnull(AIMPromotions.PmStatus, 0)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmStartDate = isnull(AIMPromotions.PmStartDate, '01/01/1990')," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmEndDate = isnull(AIMPromotions.PmEndDate, '01/01/1990')," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj01 = isnull(AIMPromotions.PmAdj01,1), PmAdj02 = isnull(AIMPromotions.PmAdj02,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj03 = isnull(AIMPromotions.PmAdj03,1), PmAdj04 = isnull(AIMPromotions.PmAdj04,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj05 = isnull(AIMPromotions.PmAdj05,1), PmAdj06 = isnull(AIMPromotions.PmAdj06,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj07 = isnull(AIMPromotions.PmAdj07,1), PmAdj08 = isnull(AIMPromotions.PmAdj08,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj09 = isnull(AIMPromotions.PmAdj09,1), PmAdj10 = isnull(AIMPromotions.PmAdj10,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj11 = isnull(AIMPromotions.PmAdj11,1), PmAdj12 = isnull(AIMPromotions.PmAdj12,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj13 = isnull(AIMPromotions.PmAdj13,1), PmAdj14 = isnull(AIMPromotions.PmAdj14,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj15 = isnull(AIMPromotions.PmAdj15,1), PmAdj16 = isnull(AIMPromotions.PmAdj16,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj17 = isnull(AIMPromotions.PmAdj17,1), PmAdj18 = isnull(AIMPromotions.PmAdj18,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj19 = isnull(AIMPromotions.PmAdj19,1), PmAdj20 = isnull(AIMPromotions.PmAdj20,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj21 = isnull(AIMPromotions.PmAdj21,1), PmAdj22 = isnull(AIMPromotions.PmAdj22,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj23 = isnull(AIMPromotions.PmAdj23,1), PmAdj24 = isnull(AIMPromotions.PmAdj24,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj25 = isnull(AIMPromotions.PmAdj25,1), PmAdj26 = isnull(AIMPromotions.PmAdj26,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj27 = isnull(AIMPromotions.PmAdj27,1), PmAdj28 = isnull(AIMPromotions.PmAdj28,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj29 = isnull(AIMPromotions.PmAdj29,1), PmAdj30 = isnull(AIMPromotions.PmAdj30,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj31 = isnull(AIMPromotions.PmAdj31,1), PmAdj32 = isnull(AIMPromotions.PmAdj32,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj33 = isnull(AIMPromotions.PmAdj33,1), PmAdj34 = isnull(AIMPromotions.PmAdj34,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj35 = isnull(AIMPromotions.PmAdj35,1), PmAdj36 = isnull(AIMPromotions.PmAdj36,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj37 = isnull(AIMPromotions.PmAdj37,1), PmAdj38 = isnull(AIMPromotions.PmAdj38,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj39 = isnull(AIMPromotions.PmAdj39,1), PmAdj40 = isnull(AIMPromotions.PmAdj40,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj41 = isnull(AIMPromotions.PmAdj41,1), PmAdj42 = isnull(AIMPromotions.PmAdj42,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj43 = isnull(AIMPromotions.PmAdj43,1), PmAdj44 = isnull(AIMPromotions.PmAdj44,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj45 = isnull(AIMPromotions.PmAdj45,1), PmAdj46 = isnull(AIMPromotions.PmAdj46,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj47 = isnull(AIMPromotions.PmAdj47,1), PmAdj48 = isnull(AIMPromotions.PmAdj48,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj49 = isnull(AIMPromotions.PmAdj49,1), PmAdj50 = isnull(AIMPromotions.PmAdj50,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "PmAdj51 = isnull(AIMPromotions.PmAdj51,1), PmAdj52 = isnull(AIMPromotions.PmAdj52,1)," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMMethods.ApplySeasonsIndex , AIMMethods.ApplyTrend," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, Item.IntSafetyStock, Item.IsIntermittent," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.Mean_NZ, Item.StdDev_NZ, Item.ReplenCost2, Item.SSAdj, Item.ZSStock, Item.LTVFact," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.Dser,Item.ZOPSw," & vbCrLf
        SelectClause = SelectClause & vbTab & "RevCycles.NextDateTime, RevCycles.RevFreq, RevCycles.RevInterval, RevCycles.RevSunday," & vbCrLf
        SelectClause = SelectClause & vbTab & "RevCycles.RevMonday, RevCycles.RevTuesday, RevCycles.RevWednesday, RevCycles.RevThursday," & vbCrLf
        SelectClause = SelectClause & vbTab & "RevCycles.RevFriday, RevCycles.RevSaturday, RevCycles.WeekQualifier, RevCycles.RevStartDate," & vbCrLf
        SelectClause = SelectClause & vbTab & "RevCycles.RevEndDate, RevCycles.InitBuyPct, RevCycles.InitRevDate, RevCycles.ReviewTime," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.HiMadP, AIMOptions.LoMadP," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMOptions.MadExK,"
        SelectClause = SelectClause & vbTab & "AIMLocations.ReplenCost,"
        SelectClause = SelectClause & vbTab & "Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02, Item.BkQty03," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, Item.BkQty06," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08, Item.BkQty09," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.BkCost09 , Item.BkQty10, Item.BkCost10," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.StkDate,  AIMLocations.DemandSource,    AIMLocations.dropship_xdock," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.VelCode,Item.Class1,Item.Class2,Item.Class3,Item.Class4," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.BuyStrat, Item.UserMin, Item.UserMax, Item.DIFlag, AIMOptions.DIMADP,ItStatus.DmdUpd," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined" & vbCrLf
    End Select
    
    JoinClause = "FROM Item" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionID" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMSeasons ON Item.SaID = AIMSeasons.SaID" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMLocations on Item.Lcid = AIMLocations.Lcid" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID"
    JoinClause = JoinClause & vbTab & "AND Item.Assort = AIMVendors.Assort" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle" & vbCrLf
    JoinClause = JoinClause & "LEFT OUTER JOIN AIMPromotions ON Item.PmID = AIMPromotions.PmID" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodID" & vbCrLf

    JoinClause = JoinClause & "WHERE AIMSeasons.SAVersion = " & p_SAVersion & vbCrLf
    
    If p_OnlyFilters Then OtherClauses = KEYWORD_GROUPBY & vbCrLf & SelectClause
    If p_NoOrderBy = False Then
        OtherClauses = OtherClauses & KEYWORD_ORDERBY & vbCrLf & _
            IIf(p_OnlyFilters, SelectClause, vbTab & "Item.Item, Item.LcID")
    End If
    
    'Concatenate all:
    g_QueryForFcstItems = KEYWORD_SELECT & vbCrLf & SelectClause & _
        JoinClause & _
        p_WhereClause & _
        OtherClauses
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_QueryForFcstItems}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_QueryForFcstItems", Now, gDRJobError, True, Err
End Function

Public Function g_QueryForFcstBatchItems( _
    p_OnlyCount As Boolean, _
    p_WhereClause As String, _
    p_RepositoryKey As Long, _
    p_FcstPdBegDate As Date _
) As String
On Error GoTo ErrorHandler

    Dim SelectClause As String
    Dim JoinClause As String
    Dim OtherClauses As String
    
    If p_OnlyCount Then
        SelectClause = SelectClause & vbTab & "COUNT(Item.Item) As 'Discrete ItemLcIDs' " & vbCrLf
    Else
        SelectClause = SelectClause & vbTab & "Item.LcID, Item.Item, Item.ItDesc, " & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.Class1, Item.Class2, Item.Class3, Item.Class4, " & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.ItStat, Item.VelCode, Item.VnID, Item.Assort, Item.ByID, " & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.LDivision, AIMLocations.LStatus, AIMLocations.LRegion, AIMLocations.LUserDefined, " & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.Cube, Item.Weight, Item.Price, Item.Cost, " & vbCrLf
        SelectClause = SelectClause & vbTab & "FRD.FcstPdBegDate, FRD.Fcst, FRD.MasterFcstAdj, FRD.FcstAdj, " & vbCrLf
        SelectClause = SelectClause & vbTab & "FRD.FcstNetReq, FRD.FcstAdjNetReq, FRD.QtyProjectedInventory, " & vbCrLf
        SelectClause = SelectClause & vbTab & "FRD.HistDmd, FRD.QtyActualShipped, FRD.ProdConst" & vbCrLf
    End If
    
    JoinClause = "FROM Item" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat " & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID " & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMVendors ON (Item.VnID = AIMVendors.VnID AND Item.Assort = AIMVendors.Assort) " & vbCrLf
    JoinClause = JoinClause & "INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle " & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodID " & vbCrLf
    JoinClause = JoinClause & "INNER JOIN ForecastRepositoryDetail FRD ON (Item.Lcid = FRD.Lcid AND Item.Item = FRD.Item) " & vbCrLf
    JoinClause = JoinClause & "WHERE UPPER(Item.ItStat) NOT IN (N'I', N'P', N'X') " & vbCrLf
    JoinClause = JoinClause & vbTab & "AND FRD.RepositoryKey = " & CStr(p_RepositoryKey) & vbCrLf
    JoinClause = JoinClause & vbTab & "AND FRD.FcstPdBegDate >= '" & Format(p_FcstPdBegDate, g_ISO_DATE_FORMAT) & "' " & vbCrLf
    
    If p_OnlyCount Then
        OtherClauses = OtherClauses & KEYWORD_GROUPBY & vbCrLf & vbTab & "Item.Item, Item.LcID"
    Else
        OtherClauses = OtherClauses & KEYWORD_ORDERBY & vbCrLf & vbTab & "Item.Item, Item.LcID, FRD.FcstPdBegDate"
    End If
    
    'Concatenate all:
    g_QueryForFcstBatchItems = KEYWORD_SELECT & vbCrLf & SelectClause & _
        JoinClause & _
        vbTab & Trim$(p_WhereClause) & _
        OtherClauses
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_QueryForFcstBatchItems}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_QueryForFcstBatchItems", Now, gDRJobError, True, Err
End Function

Public Function g_SelectFilters( _
    pr_Cn As ADODB.Connection, _
    p_FcstSetupKey As Long, _
    p_xaCriteria As XArrayDB, _
    Optional p_FilterColumn As String _
) As Long
On Error GoTo ErrorHandler

    Dim FetchFilters_Sp As ADODB.Command
    Dim rsFilters As ADODB.Recordset
    Dim RtnCode As Long
    
    'Set default to failure
    g_SelectFilters = -1
    
    g_InitCriteria p_xaCriteria

    'Fetch from database
    Set FetchFilters_Sp = New ADODB.Command
    With FetchFilters_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_FilterCriteria_Select_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@FilterColumn", adVarWChar, adParamInput, 255)
        'Set values
        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
        .Parameters("@FilterColumn").Value = IIf(Trim$(p_FilterColumn) <> COMP_EMPTY, p_FilterColumn, Null)
    End With
    
    Set rsFilters = New ADODB.Recordset
    With rsFilters
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    rsFilters.Open FetchFilters_Sp
    
    If FetchFilters_Sp.Parameters("RETURN").Value = SUCCEED Then
        g_SelectFilters = SUCCEED
        RtnCode = g_CRcstToXArr(p_xaCriteria, rsFilters) 'TODO
    Else
        g_SelectFilters = FetchFilters_Sp.Parameters("RETURN").Value
    End If
    
    'CleanUp
    If f_IsRecordsetValidAndOpen(rsFilters) Then rsFilters.Close
    Set rsFilters = Nothing
    If Not (FetchFilters_Sp Is Nothing) Then Set FetchFilters_Sp.ActiveConnection = Nothing
    Set FetchFilters_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsFilters) Then rsFilters.Close
    Set rsFilters = Nothing
    If Not (FetchFilters_Sp Is Nothing) Then Set FetchFilters_Sp.ActiveConnection = Nothing
    Set FetchFilters_Sp = Nothing
    'Err.Raise Err.Number, Err.source & "{g_SelectFilters}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_SelectFilters", Now, gDRJobError, True, Err
End Function

Public Function g_SaveFilters( _
    pr_Cn As ADODB.Connection, _
    p_FcstSetupKey As Long, _
    p_xaSelCriteria As XArrayDB _
) As Long
On Error GoTo ErrorHandler

    Dim SaveFilters_Sp As ADODB.Command
    Dim ClearFilters_Sp As ADODB.Command
    
    Dim ColName As String, SearchCondition As String
    Dim RowStr As String
    Dim RowIdx As Integer, ColIdx As Long, PosIndex As Long, IndexOffset As Integer
    Dim RtnCode As Long
    Dim ParmCount As Integer
    
    If Not g_IsArray(p_xaSelCriteria) Then Exit Function
    
    'Set default to failure
    g_SaveFilters = -1
    
    'Clear out existing values before saving current set
    Set ClearFilters_Sp = New ADODB.Command
    With ClearFilters_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_FilterCriteria_Clear_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@FilterColumn", adVarWChar, adParamInput, 255)
        'Set common values
        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
        .Parameters("@FilterColumn").Value = Null
        'Run
        .Execute
        'Get status
        RtnCode = .Parameters(0).Value
    End With
    
    'Save to database
    Set SaveFilters_Sp = New ADODB.Command
    With SaveFilters_Sp
        Set .ActiveConnection = pr_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_FilterCriteria_Save_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@FilterColumn", adVarWChar, adParamInput, 255)
        .Parameters.Append .CreateParameter("@SearchCondition", adVarWChar, adParamInput, 255)
        .Parameters.Append .CreateParameter("@FilterIndex", adInteger, adParamInput)
        .Parameters.Append .CreateParameter("@FilterValue", adVarWChar, adParamInput, 255)
        'Set common values
        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
    End With
    'g_PrintCriteria p_xaSelCriteria
    For RowIdx = p_xaSelCriteria.LowerBound(1) To p_xaSelCriteria.UpperBound(1)
        RowStr = COMP_EMPTY
        If p_xaSelCriteria.UpperBound(2) > 1 Then
            ColIdx = p_xaSelCriteria.LowerBound(2)
            ColName = p_xaSelCriteria(RowIdx, ColIdx)
            
            ColIdx = ColIdx + 1
            SearchCondition = p_xaSelCriteria(RowIdx, ColIdx)
            IndexOffset = ColIdx
            With SaveFilters_Sp
                .Parameters("@FilterColumn").Value = ColName
                .Parameters("@SearchCondition").Value = SearchCondition
            End With
            'Now, parse the values and insert in
            ColIdx = ColIdx + 1
            If IsEmpty(p_xaSelCriteria(RowIdx, ColIdx)) = False Then
                While ColIdx <= p_xaSelCriteria.UpperBound(2)
                    RowStr = p_xaSelCriteria(RowIdx, ColIdx)
                    If RowStr <> COMP_EMPTY Then
                        With SaveFilters_Sp
                            .Parameters("@FilterIndex").Value = ColIdx - IndexOffset
                            .Parameters("@FilterValue").Value = RowStr
                            'Run
                            .Execute
                            'Check staus
                            RtnCode = .Parameters(0).Value
                        End With
                    End If
                    ColIdx = ColIdx + 1
                Wend
            End If
        End If
    Next RowIdx
    
    g_SaveFilters = RtnCode
    
    'CleanUp
    If Not (SaveFilters_Sp Is Nothing) Then Set SaveFilters_Sp.ActiveConnection = Nothing
    Set SaveFilters_Sp = Nothing
    If Not (ClearFilters_Sp Is Nothing) Then Set ClearFilters_Sp.ActiveConnection = Nothing
    Set ClearFilters_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (SaveFilters_Sp Is Nothing) Then Set SaveFilters_Sp.ActiveConnection = Nothing
    Set SaveFilters_Sp = Nothing
    If Not (ClearFilters_Sp Is Nothing) Then Set ClearFilters_Sp.ActiveConnection = Nothing
    Set ClearFilters_Sp = Nothing
    'Err.Raise Err.Number, Err.source & "{g_SaveFilters}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_SaveFilters", Now, gDRJobError, True, Err
End Function


Public Function g_CRcstToXArr( _
    p_xaCriteria As XArrayDB, _
    p_rsFilters As ADODB.Recordset _
) As Long
On Error GoTo ErrorHandler
    
    Dim RowIndex As Long, ColIndex As Long
    Dim ColOffset As Long, TrimLength As Long
    Dim ColValue As String
    Dim ThisField As String, CompareAs As String, ThisValueList As String
    Dim RowCount As Long, ColCount As Long
    
    If Not f_IsRecordsetOpenAndPopulated(p_rsFilters) Then Exit Function
    
    With p_xaCriteria
        RowCount = p_rsFilters.RecordCount
        ColCount = p_rsFilters.Fields.Count
        
        p_rsFilters.MoveFirst
        ThisField = COMP_EMPTY
        CompareAs = COMP_EMPTY
        While Not p_rsFilters.EOF
            If StrComp(ThisField, p_rsFilters!FilterColumn, vbTextCompare) <> 0 Then
                ThisField = p_rsFilters!FilterColumn
                CompareAs = p_rsFilters!SearchCondition
                'Set rowindex here
                Select Case ThisField
                Case CRIT_COL_ASSORT
                    RowIndex = CRIT_IDX_ASSORT
                Case CRIT_COL_BYID
                    RowIndex = CRIT_IDX_BYID
                Case CRIT_COL_CLASS1
                    RowIndex = CRIT_IDX_CLASS1
                Case CRIT_COL_CLASS2
                    RowIndex = CRIT_IDX_CLASS2
                Case CRIT_COL_CLASS3
                    RowIndex = CRIT_IDX_CLASS3
                Case CRIT_COL_CLASS4
                    RowIndex = CRIT_IDX_CLASS4
                Case CRIT_COL_ITEM
                    RowIndex = CRIT_IDX_ITEM
                Case CRIT_COL_ITSTAT
                    RowIndex = CRIT_IDX_ITSTAT
                Case CRIT_COL_LCID
                    RowIndex = CRIT_IDX_LCID
                Case CRIT_COL_LDIVISION
                    RowIndex = CRIT_IDX_LDIVISION
                Case CRIT_COL_LREGION
                    RowIndex = CRIT_IDX_LREGION
                Case CRIT_COL_LSTATUS
                    RowIndex = CRIT_IDX_LSTATUS
                Case CRIT_COL_LUSERDEFINED
                    RowIndex = CRIT_IDX_LUSERDEFINED
                Case CRIT_COL_VNID
                    RowIndex = CRIT_IDX_VNID
                End Select
    
                'Set Field Name
                ColIndex = 0
                .Value(RowIndex, ColIndex) = ThisField
            
                'Set Comparison Operator
                ColIndex = ColIndex + 1
                .Value(RowIndex, ColIndex) = CompareAs
            End If
            
            If Not IsNull(p_rsFilters!FilterValue) _
            And Trim$(p_rsFilters!FilterValue) <> COMP_EMPTY _
            Then
                ColValue = p_rsFilters!FilterValue
                'Add to xarray
                ColIndex = ColIndex + 1
                If ColIndex > .UpperBound(2) Then
                    .AppendColumns
                End If
                .Value(RowIndex, ColIndex) = ColValue
            End If
            p_rsFilters.MoveNext
        Wend    'RowCount
        
        'Blank out of the rest if the selected criterion has fewer options than other criteria
        ColIndex = ColIndex + 1
        If ColIndex < .UpperBound(2) Then
            For ColIndex = ColIndex To .UpperBound(2)
                .Value(RowIndex, ColIndex) = COMP_EMPTY
            Next ColIndex
        End If
    End With

    'g_PrintCriteria p_xaCriteria
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_CRcstToXArr}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_CRcstToXArr", Now, gDRJobError, True, Err
End Function

Public Function g_CLineToXArr( _
    p_Criteria As XArrayDB, _
    p_RowIndex As Integer, _
    p_ThisField As String, _
    p_CompareAs As String, _
    p_ThisValueList As String, _
    p_NewCriteria As String _
) As Long
On Error GoTo ErrorHandler
    
    Dim ColIndex As Long
    Dim ColOffset As Long, TrimLength As Long
    Dim ColValue As String
    
    With p_Criteria
        'Set Field Name
        ColIndex = 0
        .Value(p_RowIndex, ColIndex) = p_ThisField
        
        'Set Comparison Operator
        ColIndex = ColIndex + 1
        .Value(p_RowIndex, ColIndex) = p_CompareAs
        
        'For each value in the delimited list, insert a column
        ColOffset = InStr(1, p_ThisValueList, DELIM_COLUMN, vbTextCompare)
        If ColOffset = 0 Then
            ColValue = Trim$(p_ThisValueList)
            ColIndex = ColIndex + 1
            If ColIndex > .UpperBound(2) Then
                .InsertColumns ColIndex
                'Mark given value as a new one
                If Trim$(p_NewCriteria) = COMP_EMPTY Then
                    p_NewCriteria = ColValue
                Else
                    p_NewCriteria = ColValue & DELIM_COLUMN
                End If
            End If
            If StrComp(.Value(p_RowIndex, ColIndex), ColValue, vbTextCompare) <> 0 Then
                'Mark given value as a new one
                If Trim$(p_NewCriteria) = COMP_EMPTY Then
                    p_NewCriteria = ColValue
                Else
                    p_NewCriteria = ColValue & DELIM_COLUMN
                End If
            End If
            'Reset the criteria value to the one from the string
            .Value(p_RowIndex, ColIndex) = ColValue
            TrimLength = (Len(p_ThisValueList) - ColOffset)
            If TrimLength = Len(p_ThisValueList) Then
                p_ThisValueList = COMP_EMPTY
            Else
                p_ThisValueList = Right$(p_ThisValueList, TrimLength)
            End If
        Else
            While ColOffset > 0
                ColOffset = InStr(1, p_ThisValueList, DELIM_COLUMN, vbTextCompare)
                If ColOffset > 1 Then
                    ColValue = Trim$(Left$(p_ThisValueList, ColOffset - 1))
                Else
                    ColValue = Trim$(p_ThisValueList)
                End If
                If ColValue <> "" Then
                    ColIndex = ColIndex + 1
                    If ColIndex > .UpperBound(2) Then
                        .InsertColumns ColIndex
                        'Mark given value as a new one
                        If Trim$(p_NewCriteria) = COMP_EMPTY Then
                            p_NewCriteria = ColValue
                        Else
                            p_NewCriteria = ColValue & DELIM_COLUMN
                        End If
                    End If
                    If StrComp(.Value(p_RowIndex, ColIndex), ColValue, vbTextCompare) <> 0 Then
                        'Mark given value as a new one
                        If Trim$(p_NewCriteria) = COMP_EMPTY Then
                            p_NewCriteria = ColValue
                        Else
                            p_NewCriteria = ColValue & DELIM_COLUMN
                        End If
                    End If
                    'Reset the criteria value to the one from the string
                    .Value(p_RowIndex, ColIndex) = ColValue
                    TrimLength = (Len(p_ThisValueList) - ColOffset)
                    If TrimLength = Len(p_ThisValueList) Then
                        p_ThisValueList = COMP_EMPTY
                    Else
                        p_ThisValueList = Right$(p_ThisValueList, TrimLength)
                    End If
                End If
            Wend
        End If
            
        'Blank out of the rest if the selected criterion has fewer options than other criteria
        ColIndex = ColIndex + 1
        If ColIndex <= .UpperBound(2) Then
            For ColIndex = ColIndex To .UpperBound(2)
                .Value(p_RowIndex, ColIndex) = COMP_EMPTY
            Next ColIndex
        End If
    End With

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_CLineToXArr}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_CLineToXArr", Now, gDRJobError, True, Err
End Function

Public Function g_CParaToXArr( _
    p_AllCriteria As String, _
    p_Criteria As XArrayDB _
) As Long
'This function expects one string for all criteria, formatted as follows into a paragraph:
'FieldName1 CompareAs (Val1, Val2, Val3, ..., ValN)
'FieldName2 CompareAs (Val1, Val2, Val3, ..., ValN)
'FieldName3 CompareAs (Val1, Val2, Val3, ..., ValN)
'...
'FieldNameN CompareAs (Val1, Val2, Val3, ..., ValN)
' that is, each criterion is on a separate line.
'   the first delimited column is the criterion's Field Name
'   the second delimited column is the criterion's Comparison Operator
'   the next N delimited columns (bound by listbegin and listend parentheses) are the
'       values associated with the criterion.
'NOTE: The BETWEEN-AND values are handled within this function,
'so there's no need to reformat them before calling this function.
'That is, a line with BETWEEN-AND would show up as:
'FieldNameN BETWEEN Val1 AND Val2
On Error GoTo ErrorHandler
    
    Const C_FIELDNAME As String = " "
    Const C_LISTBEGIN = "("
    Const C_LISTEND = ")"
    Dim CriterionRow As String, ThisField As String, CompareAs As String
    Dim RowIndex As Integer, RowOffset As Integer
    Dim ColIndex As Long, ColOffset As Long
    Dim HeaderOffset As Integer, ListOffset As Integer
    Dim TrimLength As Long, RtnCode As Long
    Dim NewCrit As String
    
    g_CParaToXArr = -1
    If Replace(p_AllCriteria, DELIM_COLUMN, COMP_EMPTY) = COMP_EMPTY Then Exit Function
    g_InitCriteria p_Criteria
    
    RowOffset = InStr(1, p_AllCriteria, DELIM_ROW, vbTextCompare)
    If RowOffset = 0 Then RowOffset = InStr(1, p_AllCriteria, DELIM_LINE, vbTextCompare)
    While RowOffset > 0
        CriterionRow = Trim$(Left$(p_AllCriteria, RowOffset - 1))
        If StrComp(CriterionRow, COMP_EMPTY, vbTextCompare) <> 0 Then
            'Find the field name
            HeaderOffset = InStr(1, CriterionRow, C_FIELDNAME, vbTextCompare)
            ThisField = Trim$(Left$(p_AllCriteria, HeaderOffset - 1))
            TrimLength = (Len(CriterionRow) - HeaderOffset)
            CriterionRow = Right$(CriterionRow, TrimLength)
            'Find the relative rowindex for the array
            Select Case UCase$(ThisField)
            Case CRIT_COL_ASSORT
                RowIndex = CRIT_IDX_ASSORT
            Case CRIT_COL_BYID
                RowIndex = CRIT_IDX_BYID
            Case CRIT_COL_CLASS1
                RowIndex = CRIT_IDX_CLASS1
            Case CRIT_COL_CLASS2
                RowIndex = CRIT_IDX_CLASS2
            Case CRIT_COL_CLASS3
                RowIndex = CRIT_IDX_CLASS3
            Case CRIT_COL_CLASS4
                RowIndex = CRIT_IDX_CLASS4
            Case CRIT_COL_ITEM
                RowIndex = CRIT_IDX_ITEM
            Case CRIT_COL_ITSTAT
                RowIndex = CRIT_IDX_ITSTAT
            Case CRIT_COL_LCID
                RowIndex = CRIT_IDX_LCID
            Case CRIT_COL_LDIVISION
                RowIndex = CRIT_IDX_LDIVISION
            Case CRIT_COL_LREGION
                RowIndex = CRIT_IDX_LREGION
            Case CRIT_COL_LSTATUS
                RowIndex = CRIT_IDX_LSTATUS
            Case CRIT_COL_LUSERDEFINED
                RowIndex = CRIT_IDX_LUSERDEFINED
            Case CRIT_COL_VNID
                RowIndex = CRIT_IDX_VNID
            End Select
            
            'Find the comparison operator
            ListOffset = InStr(1, CriterionRow, C_LISTBEGIN, vbTextCompare)
            'A.STOCKSDALE: TO DO -- This will fail for BETWEEN-AND
            If ListOffset = 0 Then
                ListOffset = InStr(1, CriterionRow, COMP_BETWEEN, vbTextCompare)
                CompareAs = COMP_BETWEEN
                TrimLength = (Len(CriterionRow) - (ListOffset + Len(COMP_BETWEEN)))
                CriterionRow = Right$(CriterionRow, TrimLength)
                'Reparse the value list to eliminat the keyword AND
                CriterionRow = Replace(CriterionRow, COMP_AND, DELIM_COLUMN)
                CriterionRow = Replace(CriterionRow, DELIM_SQLQUOTE, COMP_EMPTY)
            Else
                CompareAs = Trim$(Left$(CriterionRow, ListOffset - 1))
                TrimLength = (Len(CriterionRow) - ListOffset)
                CriterionRow = Right$(CriterionRow, TrimLength)
            End If
            'Remove the delimiters - (, ) and '
            CriterionRow = Replace(CriterionRow, C_LISTBEGIN, COMP_EMPTY)
            CriterionRow = Replace(CriterionRow, C_LISTEND, COMP_EMPTY)
            CriterionRow = Replace(CriterionRow, DELIM_SQLQUOTE, COMP_EMPTY)
            'Parse the line into the array
            RtnCode = g_CLineToXArr(p_Criteria, RowIndex, ThisField, CompareAs, CriterionRow, NewCrit)
        End If
        TrimLength = (Len(p_AllCriteria) - RowOffset) - 1
        p_AllCriteria = Right$(p_AllCriteria, TrimLength)
        RowOffset = InStr(1, p_AllCriteria, DELIM_ROW, vbTextCompare)
        If RowOffset = 0 Then RowOffset = InStr(1, p_AllCriteria, DELIM_LINE, vbTextCompare)
    Wend
    'g_PrintCriteria p_Criteria
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_CParaToXArr}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_CParaToXArr", Now, gDRJobError, True, Err
End Function

Public Function g_CXArrToLine(p_Criteria As XArrayDB, p_RowIndex As Long, _
    p_SQLFormat As Boolean, p_OnlyValues As Boolean, _
    Optional p_FieldStr As String = "" _
) As String
On Error GoTo ErrorHandler

    Dim ColIndex As Long, PosIndex As Long
    Dim RowStr As String, CompareAs As String

    With p_Criteria
        'Initiate
        ColIndex = CRIT_OFFSET + 1
        RowStr = COMP_EMPTY
        g_CXArrToLine = COMP_EMPTY
        'Validate
        If .UpperBound(2) < ColIndex _
        Then
            Exit Function
        ElseIf IsEmpty(.Value(p_RowIndex, ColIndex)) Then
            Exit Function
        End If
        'Comparison operator
        CompareAs = .Value(p_RowIndex, CRIT_OFFSET)
        'Process
        While ColIndex <= .UpperBound(2)
            'Build just the list of values delimited by commas
            If p_SQLFormat _
            Then
                If CompareAs <> COMP_BETWEEN Then
                    'Prefix N for the Unicode fields
                    RowStr = RowStr & _
                        IIf(Trim$(.Value(p_RowIndex, ColIndex)) <> COMP_EMPTY, _
                            .Value(p_RowIndex, ColIndex) & Trim$(DELIM_COLUMN), _
                            COMP_EMPTY)
                Else
                    'Between-And will be adjusted for Unicode later
                    RowStr = RowStr & _
                        IIf(Trim$(.Value(p_RowIndex, ColIndex)) <> COMP_EMPTY, _
                            .Value(p_RowIndex, ColIndex) & Trim$(DELIM_COLUMN), _
                            COMP_EMPTY)
                End If
            Else
                'For display, do not prefix for Unicode
                RowStr = RowStr & _
                    IIf(Trim$(.Value(p_RowIndex, ColIndex)) <> COMP_EMPTY, _
                        .Value(p_RowIndex, ColIndex) & Trim$(DELIM_COLUMN), _
                        COMP_EMPTY)
            End If
            ColIndex = ColIndex + 1
        Wend
        
        'Trim ends
        RowStr = Trim$(RowStr)
        PosIndex = InStrRev(RowStr, DELIM_COLUMN)
        If PosIndex > 0 Then
            RowStr = Left$(RowStr, (PosIndex - 1))
        Else
            RowStr = COMP_EMPTY
        End If
        
        'Elaborate if requested.
        If p_OnlyValues = False _
        And RowStr <> COMP_EMPTY _
        Then
            'Prefix the comparison operator to this list of values
            RowStr = g_FormatCompareList(RowStr, .Value(p_RowIndex, 1), COMP_EMPTY, (ColIndex - (CRIT_OFFSET + 1)))
            'Build the entire string: field, comparision operator and valuelist
            If Trim$(RowStr) <> COMP_EMPTY _
            And StrComp(RowStr, COMP_ERROR, vbTextCompare) <> 0 Then
                'Build in SQL syntax
                If p_SQLFormat Then
                    If Trim$(p_FieldStr) <> COMP_EMPTY Then
                        RowStr = p_FieldStr & " " & _
                                .Value(p_RowIndex, 1) & " " _
                                & RowStr & vbCrLf
                    Else
                        Select Case .Value(p_RowIndex, 0)
                        Case CRIT_COL_ITEM, CRIT_COL_ITSTAT, _
                        CRIT_COL_CLASS1, CRIT_COL_CLASS2, _
                        CRIT_COL_CLASS3, CRIT_COL_CLASS4, _
                        CRIT_COL_BYID
                            RowStr = " AND Item." & Trim$(.Value(p_RowIndex, 0)) & " " & _
                                .Value(p_RowIndex, 1) & " " _
                                & RowStr & vbCrLf
                                
                        Case CRIT_COL_LCID, CRIT_COL_LSTATUS, _
                        CRIT_COL_LDIVISION, CRIT_COL_LREGION, _
                        CRIT_COL_LUSERDEFINED, CRIT_COL_LRANK, _
                        CRIT_COL_LTYPE, CRIT_COL_LEADTIMESOURCE, _
                        CRIT_COL_DEMAMADSOURCE, CRIT_COL_DFTREVIEWERID, _
                        CRIT_COL_DFTBYID
                            RowStr = " AND AIMLocations." & Trim$(.Value(p_RowIndex, 0)) & " " & _
                                .Value(p_RowIndex, 1) & " " _
                                & RowStr & vbCrLf
                                
                        Case CRIT_COL_VNID, CRIT_COL_ASSORT
                            RowStr = " AND AIMVendors." & Trim$(.Value(p_RowIndex, 0)) & " " & _
                                .Value(p_RowIndex, 1) & " " _
                                & RowStr & vbCrLf
                                
                        End Select
                    End If
                Else
                    'Build in pseudo-SQL syntax
                    RowStr = getTranslationResource(.Value(p_RowIndex, 0)) & " " & _
                            getTranslationResource(.Value(p_RowIndex, 1)) & " " & _
                            RowStr & vbCrLf
                    RowStr = Replace(RowStr, " N'", " '", , , vbTextCompare)
                    RowStr = Replace(RowStr, "(N'", "('", , , vbTextCompare)
                End If
            Else
                RowStr = COMP_EMPTY
            End If
        End If  'Check for p_OnlyValues
    
    End With
    
    'Return
    g_CXArrToLine = RowStr
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_CXArrToLine}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_CXArrToLine", Now, gDRJobError, True, Err
End Function

Public Function g_CXArrToPara(p_Criteria As XArrayDB, _
    p_SQLFormat As Boolean, p_OnlyValues As Boolean _
) As String
On Error GoTo ErrorHandler

    Dim CatStr As String, RowStr As String
    Dim RowIdx As Long
    
    CatStr = COMP_EMPTY
    For RowIdx = p_Criteria.LowerBound(1) To p_Criteria.UpperBound(1)
        RowStr = COMP_EMPTY
        RowStr = g_CXArrToLine(p_Criteria, RowIdx, p_SQLFormat, p_OnlyValues)
        CatStr = CatStr & RowStr
    Next RowIdx
        
    g_CXArrToPara = CatStr
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_CXArrToPara}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_CXArrToPara", Now, gDRJobError, True, Err
End Function

Public Function g_FormatCompareList( _
    CompVals As String, _
    CompType As String, _
    CompDetail As String, _
    Optional ColCount As Long = 10 _
) As String
On Error GoTo ErrorHandler

    Dim IndexCounter As Long, TotalStrings As Long, PosIndex As Long
    Dim newtext As String
    Dim ReturnText As String
    Dim StrArray() As String
    Dim strMessage As String
       
    If Trim$(CompVals) = COMP_EMPTY Then Exit Function
    
    Select Case CompType
        Case COMP_LIKE
            'Untested
            Select Case CompDetail
                Case getTranslationResource("Begins with")
                    newtext = newtext & "N'" & CompVals & "%" & "'"
                Case getTranslationResource("Contains")
                    newtext = newtext & "N'%" & CompVals & "%" & "'"
            End Select
            
        Case COMP_BETWEEN
            StrArray = Split(CompVals, DELIM_COLUMN)
            TotalStrings = UBound(StrArray) + 1
            
            If TotalStrings <> 2 Then
                strMessage = getTranslationResource("MSGBOX02001")
                If StrComp(strMessage, "MSGBOX02001") = 0 Then strMessage = "Please enter two values separated by a comma."
                MsgBox strMessage, vbExclamation
                        
                g_FormatCompareList = COMP_ERROR
                Exit Function
            Else
                newtext = "N'" & StrArray(0) & "'" & COMP_AND & "N'" & StrArray(1) & "' "
            End If
            
        Case COMP_IN, COMP_NOTIN
'            newtext = "(" & CompVals & ")"
            
            StrArray = Split(CompVals, DELIM_COLUMN)
            TotalStrings = UBound(StrArray)
            newtext = "("
            For IndexCounter = LBound(StrArray) To TotalStrings
                newtext = newtext & "N'" & StrArray(IndexCounter) & "', "
            Next IndexCounter
            newtext = Trim$(newtext)
            PosIndex = InStrRev(newtext, DELIM_COLUMN)
            If PosIndex > 0 Then
                newtext = Left$(newtext, (PosIndex - 1))
            Else
                newtext = COMP_EMPTY
            End If

            newtext = newtext & ")"
            
        Case Else
            newtext = IIf(Trim$(CompVals) <> COMP_EMPTY, "N'" & Trim$(CompVals) & "'", COMP_EMPTY)
            
    End Select
    
    CompVals = newtext
            
    g_FormatCompareList = CompVals

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_FormatCompareList}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_FormatCompareList", Now, gDRJobError, True, Err
End Function

Public Function g_IsArray(p_Array As XArrayDB) As Boolean
On Error GoTo ErrorHandler

    Dim TestArray As Long
    
    On Error Resume Next
    TestArray = p_Array.UpperBound(1)
    If Err.Number = 9 _
    Or TestArray <= 0 _
    Then
        On Error GoTo ErrorHandler
        g_IsArray = False
    Else
        On Error GoTo ErrorHandler
        g_IsArray = True
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_IsArray}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_IsArray", Now, gDRJobError, True, Err
End Function

Public Sub g_InitConstants()
On Error GoTo ErrorHandler

'     DELIM_HEADER = Chr(58) 'colon :
'     DELIM_ROW = Chr(59)    'semi-colon ;
'     DELIM_COLUMN = Chr(44) 'comma ,
'     DELIM_LINE = Chr(13) & Chr(10) 'carriage-return and line-feed
'     DELIM_SQLQUOTE = Chr(39)   'single-quote '

Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_InitConstants}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_InitConstants", Now, gDRJobError, True, Err
End Sub

Public Sub g_InitCriteria(p_Array As XArrayDB)
On Error GoTo ErrorHandler

    If g_IsArray(p_Array) Then
        p_Array.Clear
    End If
    Set p_Array = Nothing
    Set p_Array = New XArrayDB
    p_Array.ReDim 0, CRIT_TOTAL, 0, CRIT_OFFSET
    
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_InitCriteria}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_InitCriteria", Now, gDRJobError, True, Err
End Sub

Public Sub g_PrintCriteria(p_Criteria As XArrayDB)
'Remove/comment once debugging is over.
On Error GoTo ErrorHandler
    
    Dim RowIdx As Long, ColIdx As Long
    Dim CatStr As String, RowStr As String
        
    If Not g_IsArray(p_Criteria) Then Exit Sub
    With p_Criteria
        Debug.Print "Xarray's contents..."
        For RowIdx = .LowerBound(1) To .UpperBound(1)
            CatStr = CStr(RowIdx) & ": "
            For ColIdx = .LowerBound(2) To .UpperBound(2)
                CatStr = CatStr & .Value(RowIdx, ColIdx) & ", "
            Next ColIdx
            Debug.Print CatStr
        Next RowIdx
        Debug.Print "...end contents."
    End With

Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_PrintCriteria}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_PrintCriteria", Now, gDRJobError, True, Err
End Sub

Public Sub g_CXArr1ToXArr2( _
    FromArray As XArrayDB, ToArray As XArrayDB, _
    Optional ValAsBoolean As Boolean)
On Error GoTo ErrorHandler

    Dim RowIdx As Long, ColIdx As Long
    
    Set ToArray = Nothing
    Set ToArray = New XArrayDB
    
    If Not g_IsArray(FromArray) Then Exit Sub
    With FromArray
        'set to same size as FromArray
        ToArray.ReDim .LowerBound(1), .UpperBound(1), .LowerBound(2), .UpperBound(2)
        'Copy data
        For RowIdx = .LowerBound(1) To .UpperBound(1)
            For ColIdx = .LowerBound(2) To .UpperBound(2)
                If ValAsBoolean = True Then
                    'This is only for the array that stores lock values.
                    'Not applicable for transferring data
                    If ColIdx = 0 _
                    And Not IsEmpty(.Value(RowIdx, ColIdx)) Then
                        ToArray(RowIdx, ColIdx) = ValAsBoolean
                    End If
                Else
                    'Transfer data from A to B
                    ToArray(RowIdx, ColIdx) = .Value(RowIdx, ColIdx)
                End If
            Next ColIdx
        Next RowIdx
    End With

Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{CloneFromArrayToB}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::CloneFromArrayToB", Now, gDRJobError, True, Err
End Sub

Public Function g_IsInFilters(p_AllCriteria As XArrayDB, _
    argItStat As String, argLStatus As String, argLDivision As String, argLRegion As String, _
    argLUserDefined As String, argClass1 As String, argClass2 As String, argClass3 As String, _
    argClass4 As String, ArgById As String, ArgLcid As String, ArgVnId As String, _
    ArgAssort As String, ArgItem As String) _
As Boolean
On Error GoTo ErrorHandler
    
    Dim RowIdx As Integer, ColIdx As Long
    Dim ThisField As String, CompareAs As String
    Dim CompareValue As String, CriterionValue As String
    Dim BetweenAnd(0 To 1) As String
    Dim CountCriteria As Integer, CountMatches As Integer
    Dim IsMatched As Boolean
    
    'Default to failure
    g_IsInFilters = False
    
    If Not g_IsArray(p_AllCriteria) Then
        g_IsInFilters = True
        Exit Function
    End If
    With p_AllCriteria
        CountCriteria = 0
        CountMatches = 0
        For RowIdx = .LowerBound(1) To .UpperBound(1)
            'Identify Criteria field name
            ColIdx = .LowerBound(2)
            ThisField = .Value(RowIdx, ColIdx)
            Select Case RowIdx
            Case CRIT_IDX_ASSORT
                CompareValue = Trim(ArgAssort)
            Case CRIT_IDX_BYID
                CompareValue = Trim(ArgById)
            Case CRIT_IDX_CLASS1
                CompareValue = Trim(argClass1)
            Case CRIT_IDX_CLASS2
                CompareValue = Trim(argClass2)
            Case CRIT_IDX_CLASS3
                CompareValue = Trim(argClass3)
            Case CRIT_IDX_CLASS4
                CompareValue = Trim(argClass4)
            Case CRIT_IDX_ITEM
                CompareValue = Trim(ArgItem)
            Case CRIT_IDX_ITSTAT
                CompareValue = Trim(argItStat)
            Case CRIT_IDX_LCID
                CompareValue = Trim(ArgLcid)
            Case CRIT_IDX_LDIVISION
                CompareValue = Trim(argLDivision)
            Case CRIT_IDX_LREGION
                CompareValue = Trim(argLRegion)
            Case CRIT_IDX_LSTATUS
                CompareValue = Trim(argLStatus)
            Case CRIT_IDX_LUSERDEFINED
                CompareValue = Trim(argLUserDefined)
            Case CRIT_IDX_VNID
                CompareValue = Trim(ArgVnId)
            End Select
            'Identify Comparison Operator
            ColIdx = ColIdx + 1
            CompareAs = .Value(RowIdx, ColIdx)
            'Check if any values have been selected for this criterion
            If Trim$(CompareAs) <> COMP_EMPTY Then CountCriteria = CountCriteria + 1
            'Check within the forecast-items array to see if the value is matched by a filter
            IsMatched = False   'Default to no matches
            For ColIdx = (CRIT_OFFSET + 1) To .UpperBound(2)
                CriterionValue = .Value(RowIdx, ColIdx)
                Select Case CompareAs
                Case COMP_IN
                    If StrComp(CriterionValue, CompareValue, vbTextCompare) = 0 Then
                        IsMatched = True
                    End If

                Case COMP_NOTIN
                    If StrComp(CriterionValue, CompareValue, vbTextCompare) <> 0 Then
                        IsMatched = True
                    End If
                    
                Case COMP_BETWEEN
                    BetweenAnd(0) = .Value(RowIdx, CRIT_OFFSET + 1)
                    BetweenAnd(1) = .Value(RowIdx, CRIT_OFFSET + 2)
                    
                    If CompareValue >= BetweenAnd(0) _
                    And CompareValue <= BetweenAnd(1) _
                    Then
                        IsMatched = True
                    Else
                        Exit For
                    End If
                    
                Case Else
                    IsMatched = False
                End Select
            Next ColIdx
            'If any one filter matches, then the record is valid
            If IsMatched = True Then CountMatches = CountMatches + 1
            'So on, for each one of the criteria fields
        Next RowIdx
    End With
    
    If CountMatches = CountCriteria Then
        g_IsInFilters = True
    Else
        g_IsInFilters = False
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "{g_IsInFilters}", Err.Description
     f_HandleErr , , , "AIMFcstFilterCrit::g_IsInFilters", Now, gDRJobError, True, Err
End Function

Public Function g_BuildWhereClause(p_xaCrit As XArrayDB) As String
On Error GoTo ErrorHandler

    Dim CatStr As String, RowStr As String, FieldStr As String
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDescription As String
    
    CatStr = g_CXArrToPara(p_xaCrit, True, False)
    If Trim$(CatStr) = COMP_EMPTY Then
        p_xaCrit.ReDim 0, CRIT_TOTAL, 0, CRIT_OFFSET
    End If
    
    'Return paragraph
    g_BuildWhereClause = CatStr
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDescription = Err.Description
    'Err.Raise ErrNumber, ErrSource & "{g_BuildWhereClause}", ErrDescription
     f_HandleErr , , , "AIMFcstFilterCrit::g_BuildWhereClause", Now, gDRJobError, True, Err
End Function
Public Function g_QueryForLocations( _
    p_WhereClause As String, _
    Optional p_ShortList As Boolean, _
    Optional p_OnlyJoinClause As Boolean _
) As String
On Error GoTo ErrorHandler

    Dim SelectClause As String
    Dim JoinClause As String
    Dim OtherClauses As String

    If p_ShortList = True Then
        'Limit the display to values that won't cause confusion re selections/duplications
        'This is applicable to the "data for selected criterion" grid of the Item Filter
        SelectClause = SelectClause & vbTab & "AIMLocations.Lcid, AIMLocations.LName" & vbCrLf
    Else
        'Limit the returned set to the values that are directly related to the item Filter
        'This is applicable to the "filtered items" grid of the Item Filter
        SelectClause = SelectClause & vbTab & "AIMLocations.Lcid, AIMLocations.LName,AIMLocations.LStatus," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.Ltype, AIMLocations.LeadTimeSource," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.DemandSource, AIMLocations.LDivision," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.LRegion, AIMLocations.LUserDefined,AIMLocations.LRank," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.Dft_ById,AIMLocations.Dft_ReviewerId" & vbCrLf
    End If
    
    JoinClause = "FROM AIMLocations" & vbCrLf
    JoinClause = JoinClause & "WHERE AIMLocations.Lcid =  AIMLocations.Lcid" & vbCrLf
    OtherClauses = KEYWORD_GROUPBY & vbCrLf & SelectClause
    OtherClauses = OtherClauses & KEYWORD_ORDERBY & vbCrLf & SelectClause
    'Concatenate all:
   If p_OnlyJoinClause = True Then
        g_QueryForLocations = JoinClause & vbCrLf & p_WhereClause
    Else
        g_QueryForLocations = KEYWORD_SELECT & vbCrLf & SelectClause & _
            JoinClause & _
            p_WhereClause & _
            OtherClauses
    End If
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "{g_QueryForLocations}", Err.Description
End Function
Public Function g_QueryForItems( _
    p_WhereClause As String, _
    Optional p_ShortList As Boolean, _
    Optional p_OnlyJoinClause As Boolean _
) As String
On Error GoTo ErrorHandler

    Dim SelectClause As String
    Dim JoinClause As String
    Dim OtherClauses As String

    If p_ShortList = True Then
        'Limit the display to values that won't cause confusion re selections/duplications
        'This is applicable to the "data for selected criterion" grid of the Item Filter
        SelectClause = SelectClause & vbTab & "Item.Item, Item.ItDesc" & vbCrLf
    Else
        'Limit the returned set to the values that are directly related to the item Filter
        'This is applicable to the "filtered items" grid of the Item Filter
        SelectClause = SelectClause & vbTab & "Item.Item, Item.LcID," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.ItDesc, Item.ItStat," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.VnID, Item.Assort, Item.ByID," & vbCrLf
        SelectClause = SelectClause & vbTab & "Item.Class1, Item.Class2, Item.Class3, Item.Class4," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.LStatus, AIMLocations.LDivision," & vbCrLf
        SelectClause = SelectClause & vbTab & "AIMLocations.LRegion, AIMLocations.LUserDefined" & vbCrLf
    End If
    
    JoinClause = "FROM Item" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMLocations on Item.Lcid = AIMLocations.Lcid" & vbCrLf
    JoinClause = JoinClause & "INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID"
    JoinClause = JoinClause & vbTab & "AND Item.Assort = AIMVendors.Assort" & vbCrLf
    JoinClause = JoinClause & "WHERE Item.Item =  Item.Item" & vbCrLf
    
    OtherClauses = KEYWORD_GROUPBY & vbCrLf & SelectClause
    OtherClauses = OtherClauses & KEYWORD_ORDERBY & vbCrLf & SelectClause
    'Concatenate all:
   If p_OnlyJoinClause = True Then
        g_QueryForItems = JoinClause & vbCrLf & p_WhereClause
    Else
        g_QueryForItems = KEYWORD_SELECT & vbCrLf & SelectClause & _
            JoinClause & _
            p_WhereClause & _
            OtherClauses
    End If
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "{g_QueryForItems}", Err.Description
End Function

