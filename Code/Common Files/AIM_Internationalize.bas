Attribute VB_Name = "AIMInternationalize"
'******************************************************************
' This module intended to hold the functions and other items
'necessary for making AIM an international application.
'******************************************************************

Option Explicit

'Date Formatting
Public Const AIM_DATEVALUEFORMAT As String = "yyyymmdd"
Public Const AIM_TIMEVALUEFORMAT As String = "hhnnss"

'Specific to TDBDate_Calendar
Public Const TDBDATE_CALENDAR_TITLEFORMAT_MMYYYY = "mm/yyyy"
Public Const TDBDATE_CALENDAR_TITLEFORMAT_YYYYMM = "yyyy/mm"
'Std Numeric
Public Const DATEFORMAT_YYYYMMDD = "yyyy/mm/dd"
Public Const DATEFORMAT_MMDDYYYY = "mm/dd/yyyy"
Public Const DATEFORMAT_DDMMYYYY = "dd/mm/yyyy"
''With Month as an abbr.
Public Const DATEFORMAT_YYYYMMMDD = "yyyy/mmm/dd"
Public Const DATEFORMAT_MMMDDYYYY = "mmm/dd/yyyy"
Public Const DATEFORMAT_DDMMMYYYY = "dd/mmm/yyyy"
''The above with hyphen instead of forward-slash
Public Const DATEFORMAT_YYYYMMDD_H = "yyyy-mm-dd"
Public Const DATEFORMAT_MMDDYYYY_H = "mm-dd-yyyy"
Public Const DATEFORMAT_DDMMYYYY_H = "dd-mm-yyyy"
''With Month as an abbr.
Public Const DATEFORMAT_YYYYMMMDD_H = "yyyy-mmm-dd"
Public Const DATEFORMAT_MMMDDYYYY_H = "mmm-dd-yyyy"
Public Const DATEFORMAT_DDMMMYYYY_H = "dd-mmm-yyyy"

'Time Formatting
Public Const TIMEFORMAT_24HOUR = "hh:nn:ss" '24 hour
Public Const TIMEFORMAT_12HOUR_SYSTEM = "hh:nn:ss AMPM" 'localised AM/PM
Public Const TIMEFORMAT_12HOUR_UCASEAMPM = "hh:nn:ss AM/PM"
Public Const TIMEFORMAT_12HOUR_LCASEAMPM = "hh:nn:ss am/pm"
Public Const TIMEFORMAT_12HOUR_UCASEAP = "hh:nn:ss A/P"
Public Const TIMEFORMAT_12HOUR_LCASEAP = "hh:nn:ss a/p"

'Help File Generic Identifier
Public Const AIM_HELPFILE As String = "SSAAIM_Help"

'******************************************************************
'Internationalization
'Source: MSDN Online - Oct. 2001
'Font, Display and Print Considerations in a DBCS Environment
'******************************************************************
Private Const DEFAULT_CHARSET = 1
Private Const SYMBOL_CHARSET = 2
Private Const SHIFTJIS_CHARSET = 128
Private Const HANGEUL_CHARSET = 129
Private Const CHINESEBIG5_CHARSET = 136
Private Const CHINESESIMPLIFIED_CHARSET = 134
Private Declare Function GetUserDefaultLCID Lib "kernel32" () As Long

Public Function GetTranslatedCaptions(GetForm As Form)
On Error GoTo ErrorHandler
    
    Dim objControl As Control
    Dim Counter As Integer
    Dim tempString As String
    Dim tempInt As Integer
    Dim objTools As SSTool
    
    'Dim LogFile As String
    'Dim SourceFile As String
    'LogFile = App.Path & "\Translations\" & gLangID & "_" & Mid(GetForm.Name, 4) & ".txt"
    'SourceFile = GetForm.Caption
    '''First, the form
    'Write_Log LogFile, "ResourceID;ResourceString;FormName;ControlName;LanguageID", 0, False, False, False, False
    'Write_Log LogFile, GetForm.Caption & ";" & getTranslationResource(GetForm.Caption) & ";" & SourceFile & ";" & GetForm.Caption & ";" & gLangID, 0, False, False, False, False

    GetForm.Caption = getTranslationResource(GetForm.Caption)
    
    'Now, get all it's controls
    For Each objControl In GetForm.Controls
        If (TypeOf objControl Is SSOleDBCombo _
        Or TypeOf objControl Is SSOleDBDropDown _
        Or TypeOf objControl Is TDBText _
        Or TypeOf objControl Is TDBNumber _
        Or TypeOf objControl Is TDBCalendar _
        Or TypeOf objControl Is TDBTime _
        Or TypeOf objControl Is Image _
        Or TypeOf objControl Is Timer _
        Or TypeOf objControl Is SSActiveTabPanel _
        Or TypeOf objControl Is CommonDialog) _
        Then
            'No Caption
            'If (TypeOf objControl Is TDBText Or TypeOf objControl Is TDBNumber) Then Write_Log LogFile, objControl.Text & ";" & getTranslationResource(objControl.Text) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False

        ElseIf (TypeOf objControl Is SSActiveTabs) Then
            For Counter = 1 To objControl.Tabs.Count
                With objControl
                   tempString = .Tabs(Counter).Caption

                    'Write_Log LogFile, tempString & ";" & getTranslationResource(tempString) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
                    .Tabs(Counter).Caption = getTranslationResource(tempString)
                End With
            Next
        
        ElseIf (TypeOf objControl Is SSActiveToolBars) Then
            'Disable "Customize" on right click
            objControl.DisplayContextMenu = False
            
            For Each objTools In objControl.Tools
                tempString = objTools.Name
                'Write_Log LogFile, tempString & ";" & getTranslationResource(tempString) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
                objTools.ChangeAll ssChangeAllName, getTranslationResource(tempString)
                tempString = objTools.ToolTipText
                'Write_Log LogFile, tempString & ";" & getTranslationResource(tempString) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
                objTools.ToolTipText = getTranslationResource(tempString)
                Set objTools = Nothing
            Next objTools
            
        ElseIf (TypeOf objControl Is Chart2D) _
        Then
            objControl.Legend.Font.Charset = GetForm.Font.Charset
            objControl.Legend.Font.Name = GetForm.Font.Name
            If objControl.Legend.Font.Size < 9 Then objControl.Legend.Font.Size = GetForm.Font.Size
    
        ElseIf (TypeOf objControl Is TDBDate) _
        Then
            SetTDBDate objControl, Now
        
        ElseIf (TypeOf objControl Is SSMonth) _
        Then
            objControl.DayOfWeek(1).Caption = getTranslationResource("Sun")
            objControl.DayOfWeek(2).Caption = getTranslationResource("Mon")
            objControl.DayOfWeek(3).Caption = getTranslationResource("Tue")
            objControl.DayOfWeek(4).Caption = getTranslationResource("Wed")
            objControl.DayOfWeek(5).Caption = getTranslationResource("Thu")
            objControl.DayOfWeek(6).Caption = getTranslationResource("Fri")
            objControl.DayOfWeek(7).Caption = getTranslationResource("Sat")
            
            objControl.Month(1).Caption = getTranslationResource("Jan")
            objControl.Month(2).Caption = getTranslationResource("Feb")
            objControl.Month(3).Caption = getTranslationResource("Mar")
            objControl.Month(4).Caption = getTranslationResource("Apr")
            objControl.Month(5).Caption = getTranslationResource("May")
            objControl.Month(6).Caption = getTranslationResource("Jun")
            objControl.Month(7).Caption = getTranslationResource("Jul")
            objControl.Month(8).Caption = getTranslationResource("Aug")
            objControl.Month(9).Caption = getTranslationResource("Sep")
            objControl.Month(10).Caption = getTranslationResource("Oct")
            objControl.Month(11).Caption = getTranslationResource("Nov")
            objControl.Month(12).Caption = getTranslationResource("Dec")
                
        ElseIf (TypeOf objControl Is ARViewer2) _
        Then
            objControl.Toolbar.Tools.Item(2).Caption = getTranslationResource(objControl.Toolbar.Tools.Item(2).Caption)
            objControl.Toolbar.Tools.Item(19).Caption = getTranslationResource(objControl.Toolbar.Tools.Item(19).Caption)
            objControl.Toolbar.Tools.Item(20).Caption = getTranslationResource(objControl.Toolbar.Tools.Item(20).Caption)
            
            objControl.Toolbar.Tools.Item(0).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(0).Tooltip)
            objControl.Toolbar.Tools.Item(2).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(2).Tooltip)
            objControl.Toolbar.Tools.Item(4).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(4).Tooltip)
            objControl.Toolbar.Tools.Item(6).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(6).Tooltip)
            objControl.Toolbar.Tools.Item(8).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(8).Tooltip)
            objControl.Toolbar.Tools.Item(9).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(9).Tooltip)
            objControl.Toolbar.Tools.Item(11).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(11).Tooltip)
            objControl.Toolbar.Tools.Item(12).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(12).Tooltip)
            objControl.Toolbar.Tools.Item(13).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(13).Tooltip)
            objControl.Toolbar.Tools.Item(15).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(15).Tooltip)
            objControl.Toolbar.Tools.Item(16).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(16).Tooltip)
            objControl.Toolbar.Tools.Item(17).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(17).Tooltip)
            objControl.Toolbar.Tools.Item(19).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(19).Tooltip)
            objControl.Toolbar.Tools.Item(20).Tooltip = getTranslationResource(objControl.Toolbar.Tools.Item(20).Tooltip)
            
            objControl.Localize ddLTPageWidth, (getTranslationResource("Page Width"))
            objControl.Localize ddLTWholePage, (getTranslationResource("Whole Page"))
            objControl.Localize ddLTViewerTocHeader, (getTranslationResource("Contents"))

        ElseIf (TypeOf objControl Is Label) _
        Then
            'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
            objControl.Caption = getTranslationResource(objControl.Caption, True)
        
        Else
            'Default
            'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
            objControl.Caption = getTranslationResource(objControl.Caption)
        
        End If
  
        Set objControl = Nothing
    Next objControl
    
    GetProperFont GetForm
        
    'Center the form
    GetForm.Top = (AIM_Main.ScaleHeight - GetForm.Height) \ 2
    GetForm.Left = (AIM_Main.ScaleWidth - GetForm.Width) \ 2

    'Destroy the object
    Set objTools = Nothing
    Set objControl = Nothing
    
Exit Function
ErrorHandler:
    If Err.Number = 438 _
    Or Err.Number = 384 _
    Then
    '438 = "Object doesn't support this property or method"
    '384 = "A form cannot be moved or resized when it is min/maximized"
        Resume Next
    Else
        Set objTools = Nothing
        Set objControl = Nothing
        Err.Raise Err.Number, Err.source, Err.Description & "(GetTranslatedCaptions)"
    End If
End Function

Public Function GetProperFont(GetForm As Form)
On Error GoTo ErrorHandler
    
    Dim objControl As Control
    Dim Counter As Integer
    Dim tempString As String
    Dim tempInt As Integer

    'First, the form
    If Not TypeOf GetForm Is MDIForm Then
        SetProperFont GetForm.Font
    Else
        'GetForm.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FORM, COLOR_TYPE_PRIMARY)
    End If
'    If GetForm.Name <> "LOGON" _
'    And GetForm.Name <> "AIM_About" _
'    Then
'        GetForm.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FORM, COLOR_TYPE_PRIMARY)
'    End If
    
    'Now, get all its controls
    For Each objControl In GetForm.Controls
        If (TypeOf objControl Is SSActiveToolBars) Then
            If TypeOf GetForm Is MDIForm Then
'                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_ACTIVETOOLBARS, COLOR_TYPE_PRIMARY)
            Else
                'objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_ACTIVETOOLBARS, COLOR_TYPE_SECONDARY)
                'add picture here, when ready
            End If
            SetProperFont objControl.Font
            If objControl.Font.Charset > DEFAULT_CHARSET Then
                objControl.Font.Bold = False
            End If
            
        ElseIf (TypeOf objControl Is SSOleDBGrid) Then
        'SSOLE Grid Controls
            SetProperFont objControl.Font 'General VB Forms and controls
            SetProperFont objControl.HeadFont
            SetProperFont objControl.PageFooterFont
            SetProperFont objControl.PageHeaderFont
            
            objControl.BackColorEven = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
            objControl.BackColorOdd = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'            objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR_TABLEHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
'            objControl.BevelColorFace = UI_GetColor(TARGET_BACKCOLOR_TABLEHEAD, CTRL_SS_OLEDBGRID, COLOR_TYPE_NORMAL)
            
        ElseIf (TypeOf objControl Is SSOleDBCombo) Then
        'Sheridan Grid Controls
            SetProperFont objControl.Font 'General VB Forms and controls
            SetProperFont objControl.HeadFont
            objControl.BackColorEven = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBCOMBO, COLOR_TYPE_NORMAL)
            objControl.BackColorOdd = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBCOMBO, COLOR_TYPE_NORMAL)
'            objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR_TABLEHEAD, CTRL_SS_OLEDBCOMBO, COLOR_TYPE_NORMAL)
'            objControl.BevelColorFace = UI_GetColor(TARGET_BACKCOLOR_TABLEHEAD, CTRL_SS_OLEDBCOMBO, COLOR_TYPE_NORMAL)
            
        ElseIf (TypeOf objControl Is SSOleDBDropDown) Then
        'Sheridan Grid Drop Down
            SetProperFont objControl.Font
            SetProperFont objControl.HeadFont
            objControl.BackColorEven = UI_GetColor(TARGET_BACKCOLOR_EVEN, CTRL_SS_OLEDBDROPDOWN, COLOR_TYPE_NORMAL)
            objControl.BackColorOdd = UI_GetColor(TARGET_BACKCOLOR_ODD, CTRL_SS_OLEDBDROPDOWN, COLOR_TYPE_NORMAL)
'            objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR_TABLEHEAD, CTRL_SS_OLEDBDROPDOWN, COLOR_TYPE_NORMAL)
'            objControl.BevelColorFace = UI_GetColor(TARGET_BACKCOLOR_TABLEHEAD, CTRL_SS_OLEDBDROPDOWN, COLOR_TYPE_NORMAL)
            
        ElseIf (TypeOf objControl Is SSActiveTabs) Then
        'Sheridan Tab Controls
            SetProperFont objControl.Font 'General VB Forms and controls
            'objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_ACTIVETABS, COLOR_TYPE_NORMAL)
            
            SetProperFont objControl.FontSelectedTab
            SetProperFont objControl.HotTrackingFont
            
            'Resize the width, if required.
            For Counter = 1 To objControl.Tabs.Count
                'Set tabheight and tabwidth to 0, in order to make them flexible, per SSActiveTabs Help
                objControl.TabHeight = 0
                objControl.TabWidth = 0
            Next
            
        ElseIf (TypeOf objControl Is Chart2D) Then
        'Olectra Chart
            SetProperFont objControl.Legend.Font
            SetProperFont objControl.Header.Font
            SetProperFont objControl.Footer.Font
            SetProperFont objControl.ChartArea.Axes("X").Font
            SetProperFont objControl.ChartArea.Axes("Y").Font
            SetProperFont objControl.ChartArea.Axes("Y2").Font
'            'objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_SS_CHART2D, COLOR_TYPE_NORMAL)
            
        ElseIf (TypeOf objControl Is TDBDate) Then
        'True DB Date
            SetProperFont objControl.Font
            SetProperFont objControl.Calendar.DayFont
            SetProperFont objControl.Calendar.WeekFont
            SetProperFont objControl.Calendar.TitleFont
            If objControl.ReadOnly = True Then
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_TDB_DATE, COLOR_TYPE_INACTIVE)
            Else
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_TDB_DATE, COLOR_TYPE_NORMAL)
            End If
            
        ElseIf (TypeOf objControl Is ARViewer2) Then
        'Active Reports Viewer
            SetProperFont objControl.Toolbar.Font
        
        ElseIf (TypeOf objControl Is Menu _
        Or TypeOf objControl Is Image _
        Or TypeOf objControl Is Timer _
        Or TypeOf objControl Is SSActiveTabPanel _
        Or TypeOf objControl Is CommonDialog) _
        Then
            'Nothing - no font here
            
        ElseIf (TypeOf objControl Is TDBText) Then
        'True DB Text
            SetProperFont objControl.Font
            Select Case gLocaleID
                Case &H411 ' Japan
                    objControl.IMEMode = dbiIMEOn
            
                Case Else
                    objControl.IMEMode = dbiIMEDefault
            
            End Select
            objControl.IMEStatus = dbiIMENormal
            
            If objControl.ReadOnly = True Then
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_TDB_TEXT, COLOR_TYPE_INACTIVE)
            Else
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_TDB_TEXT, COLOR_TYPE_NORMAL)
            End If
            
        ElseIf (TypeOf objControl Is TDBNumber) Then
        'True DB Number
            SetProperFont objControl.Font
            If objControl.ReadOnly = True Then
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_TDB_NUMBER, COLOR_TYPE_INACTIVE)
            Else
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_TDB_NUMBER, COLOR_TYPE_NORMAL)
            End If
            
        ElseIf (TypeOf objControl Is CommandButton) _
        Then
            SetProperFont objControl.Font
            'Ensure that the button's style = 1 - Graphical. This property cannot be set at runtime
            If objControl.Default = True Then
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_COMMANDBUTTON, COLOR_TYPE_SELECTED)
            Else
                objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_COMMANDBUTTON, COLOR_TYPE_NORMAL)
            End If
            
        ElseIf (TypeOf objControl Is Frame) _
        Or (TypeOf objControl Is Label) _
        Or (TypeOf objControl Is OptionButton) _
        Or (TypeOf objControl Is CheckBox) _
        Then
            SetProperFont objControl.Font
            If GetForm.Name <> "LOGON" _
            And GetForm.Name <> "AIM_About" _
            Then
                'objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FRAME, COLOR_TYPE_NORMAL)
                'If Not (TypeOf objControl Is Frame) Then objControl.Alignment = 0    'left justify
            End If
            
        Else
            SetProperFont objControl.Font 'General VB Forms and controls
            'objControl.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FORM, COLOR_TYPE_NORMAL)
    
        End If
  
        Set objControl = Nothing
    Next objControl
    
    'Destroy the object
    Set objControl = Nothing

Exit Function
ErrorHandler:
    If Err.Number <> 438 Then
        Set objControl = Nothing
        Err.Raise Err.Number, Err.source, Err.Description & "(GetProperFont)"
    Else
        Resume Next
    End If
End Function

Public Function GetTranslatedCaptions_Reports(objReport As Object)
On Error GoTo ErrorHandler
    
    Dim objControl As Object
    Dim objReportControl As Object
    Dim Item As Object
    'Dim LogFile As String
    'Dim SourceFile As String
    '
    'LogFile = App.Path & "\Translations\" & gLangID & "_" & Mid(objReport.Name, 4) & ".txt"
    'SourceFile = objReport.Caption
    '
    '''''First, the form
    'Write_Log LogFile, "ResourceID;ResourceString;ReportName;ControlName;LanguageID", 0, False, False, False, False
    'Write_Log LogFile, objReport.Caption & ";" & getTranslationResource(objReport.Caption) & ";" & SourceFile & ";" & objReport.Name & ";" & gLangID, 0, False, False, False, False
    
    objReport.Caption = getTranslationResource(objReport.Caption)
    
    Set objReportControl = objReport.Detail

    'Write_Log LogFile, objReportControl.Caption & ";" & getTranslationResource(objReport.Caption) & ";" & SourceFile & ";" & objReport.Name & ";" & gLangID, 0, False, False, False, False
    objReportControl.Caption = getTranslationResource(objReportControl.Caption)
    'Now, get all it's controls
    For Each objControl In objReportControl.Controls
        'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
        objControl.Caption = getTranslationResource(objControl.Caption)
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
    Set objReportControl = objReport.PageFooter
    objReportControl.Caption = getTranslationResource(objReportControl.Caption)
    For Each objControl In objReportControl.Controls
        'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
        objControl.Caption = getTranslationResource(objControl.Caption)
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
    Set objReportControl = objReport.PageHeader
    objReportControl.Caption = getTranslationResource(objReportControl.Caption)
    For Each objControl In objReportControl.Controls
        'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
        objControl.Caption = getTranslationResource(objControl.Caption)
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
    Set objReportControl = objReport.Toolbar
    For Each objControl In objReportControl.Tools
        'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
        objControl.Caption = getTranslationResource(objControl.Caption)
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
'    For Each objReportControl In objReport.Sections
'        'Write_Log LogFile, objReportControl.Caption & ";" & getTranslationResource(objReportControl.Caption) & ";" & SourceFile & ";" & objReportControl.Name & ";" & gLangID, 0, False, False, False, False
'        objReportControl.Caption = getTranslationResource(objReportControl.Caption)
'        For Each objControl In objReportControl.Controls
'            'Write_Log LogFile, objControl.Caption & ";" & getTranslationResource(objControl.Caption) & ";" & SourceFile & ";" & objControl.Name & ";" & gLangID, 0, False, False, False, False
'            objControl.Caption = getTranslationResource(objControl.Caption)
'            Set objControl = Nothing
'        Next objControl
'    Next objReportControl
'    Set objReportControl = Nothing
    
    'Destroy the object
    Set objControl = Nothing
    Set objReportControl = Nothing

    getProperFont_Reports objReport
    
Exit Function
ErrorHandler:
    If Err.Number <> 438 Then
        Set objControl = Nothing
        Set objReportControl = Nothing
        Err.Raise Err.Number, Err.source, Err.Description & "(GetTranslatedCaptions_Reports)"
    Else
        Resume Next
    End If
End Function

Public Function getProperFont_Reports(objReport As Object)
    
    Dim objControl As Object
    Dim objReportControl As Object
    Dim Item As Object
    
    On Error GoTo ErrorHandler
    
    Set objReportControl = objReport.Detail
    'Now, get all it's controls
    For Each objControl In objReportControl.Controls
        SetProperFont objControl.Font 'General VB Forms and controls
        'If (TypeOf objControl Is Chart2D) Then  'Olectra Chart
            'Doesn't seem to recognize type, but since we need these updated for AIM_SeasProfRpt...
            'the errorhandler should move to the next statement.
            SetProperFont objControl.Legend.Font
            SetProperFont objControl.Header.Font
            SetProperFont objControl.Footer.Font
            SetProperFont objControl.ChartArea.Axes("X").Font
            SetProperFont objControl.ChartArea.Axes("Y").Font
            SetProperFont objControl.ChartArea.Axes("Y2").Font
        'End If
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
    Set objReportControl = objReport.PageFooter
    For Each objControl In objReportControl.Controls
        SetProperFont objControl.Font 'General VB Forms and controls
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
    Set objReportControl = objReport.PageHeader
    For Each objControl In objReportControl.Controls
        SetProperFont objControl.Font 'General VB Forms and controls
        Set objControl = Nothing
    Next objControl
    Set objReportControl = Nothing
    
    Set objReportControl = objReport.Toolbar
    SetProperFont objReportControl.Font
    Set objReportControl = Nothing
    
'    For Each objReportControl In objReport.Sections
'        For Each objControl In objReportControl.Controls
'            SetProperFont objControl.Font 'General VB Forms and controls
'            Set objControl = Nothing
'        Next objControl
'    Next objReportControl
'    Set objReportControl = Nothing
    
    'Destroy the object
    Set objControl = Nothing
    Set objReportControl = Nothing

Exit Function
ErrorHandler:
    If Err.Number <> 438 Then
        Set objControl = Nothing
        Set objReportControl = Nothing
        Err.Raise Err.Number, Err.source, Err.Description & "(getProperFont_Reports)"
    Else
        Resume Next
    End If
End Function

Public Sub SetProperFont(objFont As StdFont, Optional p_LocaleID As Integer)
'******************************************************************
'Internationalization
'Source: MSDN Online - Oct. 2001
'Font, Display and Print Considerations in a DBCS Environment
'******************************************************************
    
    If p_LocaleID <= 0 Then p_LocaleID = gLocaleID
    
    Select Case p_LocaleID
        Case g_LOCALEID_ZH_TW ' Traditional Chinese
            objFont.Charset = CHINESEBIG5_CHARSET
            objFont.Name = ChrW(&H65B0) + ChrW(&H7D30) + ChrW(&H660E) _
             + ChrW(&H9AD4)   'New Ming-Li
            If objFont.Size < 9 Then objFont.Size = 9
            
        Case g_LOCALEID_JA ' Japan
            objFont.Charset = SHIFTJIS_CHARSET
            objFont.Name = ChrW(&HFF2D) + ChrW(&HFF33) + ChrW(&H20) + _
             ChrW(&HFF30) + ChrW(&H30B4) + ChrW(&H30B7) + ChrW(&H30C3) + _
             ChrW(&H30AF)
             If objFont.Size < 9 Then objFont.Size = 9
        
        Case g_LOCALEID_KO 'Korea UserLCID
            objFont.Charset = HANGEUL_CHARSET
            objFont.Name = ChrW(&HAD74) + ChrW(&HB9BC)
            If objFont.Size < 9 Then objFont.Size = 9
        
        Case g_LOCALEID_ZH_CN ' Simplified Chinese
            objFont.Charset = CHINESESIMPLIFIED_CHARSET
            objFont.Name = ChrW(&H5B8B) + ChrW(&H4F53)
            If objFont.Size < 9 Then objFont.Size = 9
        
        Case Else   ' The other countries
            objFont.Charset = EXE_FONT_CHARSET
            objFont.Name = EXE_FONT_NAME
            If objFont.Size <= 9 Then objFont.Size = EXE_FONT_SIZE
    
    End Select

Exit Sub
ErrorHandler:
    If Err.Number <> 438 Then
        Set objFont = Nothing
        Err.Raise Err.Number, Err.source, Err.Description & "(SetProperFont)"
    Else
        Resume Next
    End If
End Sub
'******************************************************************
'End - Internationalization
'******************************************************************

Public Function SaveLogonToRegistry()

    Dim strMessage As String
    
    strMessage = ""
    SaveSetting "SSADR", "Logon", "Server", gServer
    SaveSetting "SSADR", "Logon", "DataBase", gDataBase
    SaveSetting "SSADR", "Logon", "UserID", gUserID
    SaveSetting "SSADR", "Logon", "WinNTAuth", gWinNTAuth
    
    SaveSetting "SSADR", "Logon\ResourceStrings", "LocaleID", gLocaleID

End Function

Public Sub AdjustCaptionWidths(p_Width As Integer, p_Caption As String, p_AdjustmentType As String)
''Derived from AdjustColumnWidths()
'
'    Const DEFAULT_SCALINGFACTOR = 100
'    Const SHIFTJIS_SCALINGFACTOR = 150
'    Const HANGEUL_SCALINGFACTOR = 160
'    Const CHINESEBIG5_SCALINGFACTOR = 170
'    Const CHINESESIMPLIFIED_SCALINGFACTOR = 180
'
'    Dim counter As Integer
'    Dim CaptionLength As Integer
'    Dim SuggestedWidth As Integer
'
'    CaptionLength = LenMbcs(p_Caption)
'    Select Case gLocaleID
'        Case &H404 ' Traditional Chinese
'            SuggestedWidth = CaptionLength * CHINESEBIG5_SCALINGFACTOR
'
'        Case &H411 ' Japan
'            SuggestedWidth = CaptionLength * SHIFTJIS_SCALINGFACTOR
'
'        Case &H412 'Korea UserLCID
'            SuggestedWidth = CaptionLength * HANGEUL_SCALINGFACTOR
'
'        Case &H804 ' Simplified Chinese
'            SuggestedWidth = CaptionLength * CHINESESIMPLIFIED_SCALINGFACTOR
'
'        Case Else   ' The other countries
'            'leave as is
'            'SuggestedWidth = CaptionLength * DEFAULT_SCALINGFACTOR
'
'    End Select
'
'    Select Case UCase(p_AdjustmentType)
'        Case ACW_EXPAND
'            If SuggestedWidth > p_Width Then
'                p_Width = SuggestedWidth
'            End If
'
'        Case ACW_CONTRACT
'            If SuggestedWidth < p_Width Then
'                p_Width = SuggestedWidth
'            End If
'
'        Case ACW_ADJUST
'            p_Width = SuggestedWidth
'
'    End Select
'
End Sub

Public Function SetTDBDate(getDate As TDBDate, dateValue As Date)
On Error GoTo ErrorHandler
    
    Dim WeekTitles As String
    Dim ISODate As String
    
    ISODate = Format(dateValue, g_ISO_DATE_FORMAT)
    
    getDate.Number = Format(ISODate, AIM_DATEVALUEFORMAT)
    getDate.Format = gDateFormat
    getDate.Value = Format(ISODate, gDateFormat)
    getDate.DisplayFormat = gDateFormat & ";" & Format(Now, gDateFormat) & ";Null"

    WeekTitles = getTranslationResource("Sun") & ", " & _
                getTranslationResource("Mon") & ", " & _
                getTranslationResource("Tue") & ", " & _
                getTranslationResource("Wed") & ", " & _
                getTranslationResource("Thu") & ", " & _
                getTranslationResource("Fri") & ", " & _
                getTranslationResource("Sat")
                
    getDate.Calendar.WeekTitles = WeekTitles
    getDate.Calendar.Width = 250
    
    Select Case gDateFormat
        Case DATEFORMAT_YYYYMMDD, DATEFORMAT_YYYYMMDD_H, DATEFORMAT_YYYYMMMDD, DATEFORMAT_YYYYMMMDD_H
            getDate.Calendar.TitleFormat = TDBDATE_CALENDAR_TITLEFORMAT_YYYYMM
        
        Case Else
            getDate.Calendar.TitleFormat = TDBDATE_CALENDAR_TITLEFORMAT_MMYYYY
        
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(SetTDBDate)"
End Function

Public Function SetTDBTime(getTime As TDBTime, dateValue As Date)
On Error GoTo ErrorHandler
    
    getTime.Number = Format(dateValue, AIM_TIMEVALUEFORMAT)
    getTime.Format = gTimeFormat
    getTime.Value = Format(dateValue, gTimeFormat)
    getTime.DisplayFormat = gTimeFormat
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, Err.Description & "(SetTDBTime)"
End Function

Public Sub AdjustColumnWidths(ByRef p_objGrid As Object, p_AdjustmentType As String)

    Const DEFAULT_SCALINGFACTOR = 1
    Const SHIFTJIS_SCALINGFACTOR = 200
    Const HANGEUL_SCALINGFACTOR = 160
    Const CHINESEBIG5_SCALINGFACTOR = 170
    Const CHINESESIMPLIFIED_SCALINGFACTOR = 180
    
    Dim Counter As Integer
    Dim CaptionLength As Integer
    Dim ColumnWidth As Double
    Dim SuggestedWidth As Integer
    Dim ScaleFactor As Integer
    
    Select Case gLocaleID
        Case g_LOCALEID_ZH_TW ' Traditional Chinese
            ScaleFactor = CHINESEBIG5_SCALINGFACTOR
            
        Case g_LOCALEID_JA ' Japan
            ScaleFactor = SHIFTJIS_SCALINGFACTOR
            
        Case g_LOCALEID_KO 'Korea UserLCID
            ScaleFactor = HANGEUL_SCALINGFACTOR
            
        Case g_LOCALEID_ZH_CN ' Simplified Chinese
            ScaleFactor = CHINESESIMPLIFIED_SCALINGFACTOR
            
        Case Else   ' The other countries
            'leave as is
            ScaleFactor = DEFAULT_SCALINGFACTOR
            
    End Select
    
    For Counter = 0 To p_objGrid.Columns.Count - 1
        'Given:
        CaptionLength = LenMbcs(p_objGrid.Columns.Item(Counter).Caption)
        ColumnWidth = p_objGrid.Columns.Item(Counter).Width
        
        '...Calculate what the most readable width _should_ be
        If IsDate(p_objGrid.Columns.Item(Counter).Caption) Then
            SuggestedWidth = CaptionLength * 105
        Else
            SuggestedWidth = CaptionLength * ScaleFactor
        End If
        
        '...Apply based on Adjustment Type
        Select Case UCase(p_AdjustmentType)
            Case ACW_EXPAND
                If SuggestedWidth > ColumnWidth Then
                    p_objGrid.Columns.Item(Counter).Width = SuggestedWidth
                End If
            
            Case ACW_CONTRACT
                If SuggestedWidth < ColumnWidth Then
                    p_objGrid.Columns.Item(Counter).Width = SuggestedWidth
                End If
            
            Case ACW_ADJUST
                p_objGrid.Columns.Item(Counter).Width = SuggestedWidth
            
        End Select
    Next Counter
        
    'Set Scrollbars to auto.
    p_objGrid.ScrollBars = ssScrollBarsAutomatic
    
End Sub

Function LenMbcs(ByVal str As String) As Long
   LenMbcs = LenB(StrConv(str, vbFromUnicode))
End Function
