VERSION 5.00
Object = "{E2D000D0-2DA1-11D2-B358-00104B59D73D}#1.0#0"; "titext8.ocx"
Begin VB.Form AIM_About 
   Appearance      =   0  'Flat
   BackColor       =   &H80000009&
   BorderStyle     =   4  'Fixed ToolWindow
   Caption         =   "SSA Global :: About :: SSA DR"
   ClientHeight    =   7455
   ClientLeft      =   45
   ClientTop       =   285
   ClientWidth     =   6660
   BeginProperty Font 
      Name            =   "Verdana"
      Size            =   8.25
      Charset         =   0
      Weight          =   700
      Underline       =   0   'False
      Italic          =   0   'False
      Strikethrough   =   0   'False
   EndProperty
   Icon            =   "AIM_About.frx":0000
   LinkTopic       =   "Form1"
   MaxButton       =   0   'False
   MinButton       =   0   'False
   ScaleHeight     =   497
   ScaleMode       =   3  'Pixel
   ScaleWidth      =   444
   ShowInTaskbar   =   0   'False
   StartUpPosition =   2  'CenterScreen
   Begin VB.CommandButton cmdClose 
      BackColor       =   &H0000E6FF&
      Caption         =   "&Close"
      Default         =   -1  'True
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   345
      Left            =   5025
      MaskColor       =   &H00FFFFFF&
      Style           =   1  'Graphical
      TabIndex        =   4
      Top             =   6960
      Width           =   1305
   End
   Begin VB.Frame Frame2 
      BackColor       =   &H80000009&
      BorderStyle     =   0  'None
      BeginProperty Font 
         Name            =   "MS Sans Serif"
         Size            =   9.75
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      Height          =   3000
      Left            =   0
      TabIndex        =   3
      Top             =   2520
      Width           =   6675
      Begin TDBText6Ctl.TDBText txtCopyright 
         Height          =   345
         Index           =   0
         Left            =   15
         TabIndex        =   5
         TabStop         =   0   'False
         Top             =   2630
         Width           =   6600
         _Version        =   65536
         _ExtentX        =   11642
         _ExtentY        =   609
         Caption         =   "AIM_About.frx":030A
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   9
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_About.frx":0376
         Key             =   "AIM_About.frx":0394
         BackColor       =   -2147483639
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   0
         AlignHorizontal =   2
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   "(c) Copyright  2004 SSA Global Technoligies, Inc."
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin TDBText6Ctl.TDBText txtCopyright 
         Height          =   345
         Index           =   1
         Left            =   22
         TabIndex        =   6
         TabStop         =   0   'False
         Top             =   100
         Width           =   6600
         _Version        =   65536
         _ExtentX        =   11642
         _ExtentY        =   609
         Caption         =   "AIM_About.frx":03D8
         BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
            Name            =   "Verdana"
            Size            =   9
            Charset         =   0
            Weight          =   400
            Underline       =   0   'False
            Italic          =   0   'False
            Strikethrough   =   0   'False
         EndProperty
         DropDown        =   "AIM_About.frx":0444
         Key             =   "AIM_About.frx":0462
         BackColor       =   -2147483639
         EditMode        =   0
         ForeColor       =   -2147483640
         ReadOnly        =   -1
         ShowContextMenu =   -1
         MarginLeft      =   3
         MarginRight     =   3
         MarginTop       =   3
         MarginBottom    =   3
         Enabled         =   -1
         MousePointer    =   0
         Appearance      =   0
         BorderStyle     =   0
         AlignHorizontal =   2
         AlignVertical   =   2
         MultiLine       =   -1
         ScrollBars      =   0
         PasswordChar    =   ""
         AllowSpace      =   -1
         Format          =   ""
         FormatMode      =   1
         AutoConvert     =   -1
         ErrorBeep       =   0
         MaxLength       =   255
         LengthAsByte    =   0
         Text            =   ""
         Furigana        =   0
         HighlightText   =   0
         IMEMode         =   0
         IMEStatus       =   0
         DropWndWidth    =   0
         DropWndHeight   =   0
         ScrollBarMode   =   0
         MoveOnLRKey     =   0
         OLEDragMode     =   0
         OLEDropMode     =   0
      End
      Begin VB.Image Image2 
         Height          =   1785
         Left            =   2625
         Picture         =   "AIM_About.frx":04A6
         Top             =   630
         Width           =   1365
      End
   End
   Begin TDBText6Ctl.TDBText txtAdditionalInfo 
      Height          =   945
      Left            =   315
      TabIndex        =   0
      TabStop         =   0   'False
      Top             =   5880
      Width           =   6015
      _Version        =   65536
      _ExtentX        =   10610
      _ExtentY        =   1667
      Caption         =   "AIM_About.frx":6F48
      BeginProperty Font {0BE35203-8F91-11CE-9DE3-00AA004BB851} 
         Name            =   "Verdana"
         Size            =   8.25
         Charset         =   0
         Weight          =   400
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      DropDown        =   "AIM_About.frx":6FB4
      Key             =   "AIM_About.frx":6FD2
      BackColor       =   16314092
      EditMode        =   0
      ForeColor       =   -2147483640
      ReadOnly        =   -1
      ShowContextMenu =   -1
      MarginLeft      =   3
      MarginRight     =   3
      MarginTop       =   3
      MarginBottom    =   3
      Enabled         =   -1
      MousePointer    =   0
      Appearance      =   1
      BorderStyle     =   1
      AlignHorizontal =   0
      AlignVertical   =   0
      MultiLine       =   -1
      ScrollBars      =   2
      PasswordChar    =   ""
      AllowSpace      =   -1
      Format          =   ""
      FormatMode      =   1
      AutoConvert     =   -1
      ErrorBeep       =   0
      MaxLength       =   255
      LengthAsByte    =   0
      Text            =   $"AIM_About.frx":7016
      Furigana        =   0
      HighlightText   =   0
      IMEMode         =   0
      IMEStatus       =   0
      DropWndWidth    =   0
      DropWndHeight   =   0
      ScrollBarMode   =   1
      MoveOnLRKey     =   0
      OLEDragMode     =   0
      OLEDropMode     =   0
   End
   Begin VB.Label lblBETA 
      Alignment       =   2  'Center
      Appearance      =   0  'Flat
      BackColor       =   &*********&
      BackStyle       =   0  'Transparent
      Caption         =   "BETA"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   20.25
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &H000000FF&
      Height          =   585
      Left            =   60
      TabIndex        =   10
      Top             =   2040
      Visible         =   0   'False
      Width           =   6555
      WordWrap        =   -1  'True
   End
   Begin VB.Label AboutTextLabel 
      Alignment       =   2  'Center
      Appearance      =   0  'Flat
      BackColor       =   &*********&
      BackStyle       =   0  'Transparent
      Caption         =   "SSA Distribution Replenishment"
      BeginProperty Font 
         Name            =   "Verdana"
         Size            =   15.75
         Charset         =   0
         Weight          =   700
         Underline       =   0   'False
         Italic          =   0   'False
         Strikethrough   =   0   'False
      EndProperty
      ForeColor       =   &*********&
      Height          =   945
      Index           =   2
      Left            =   135
      TabIndex        =   9
      Top             =   1215
      Width           =   6405
      WordWrap        =   -1  'True
   End
   Begin VB.Label AboutTextLabel 
      Appearance      =   0  'Flat
      BackColor       =   &*********&
      BackStyle       =   0  'Transparent
      Caption         =   "License"
      ForeColor       =   &*********&
      Height          =   345
      Index           =   1
      Left            =   4140
      TabIndex        =   8
      Top             =   660
      Width           =   2055
   End
   Begin VB.Label AboutTextLabel 
      Appearance      =   0  'Flat
      BackColor       =   &*********&
      BackStyle       =   0  'Transparent
      Caption         =   "Version"
      ForeColor       =   &*********&
      Height          =   345
      Index           =   0
      Left            =   4140
      TabIndex        =   7
      Top             =   240
      Width           =   2055
   End
   Begin VB.Label AboutLabel 
      Appearance      =   0  'Flat
      BackColor       =   &*********&
      BackStyle       =   0  'Transparent
      Caption         =   "Licensed to"
      ForeColor       =   &*********&
      Height          =   315
      Index           =   1
      Left            =   2280
      TabIndex        =   2
      Top             =   690
      Width           =   1740
   End
   Begin VB.Label AboutLabel 
      Appearance      =   0  'Flat
      BackColor       =   &*********&
      BackStyle       =   0  'Transparent
      Caption         =   "Version"
      ForeColor       =   &*********&
      Height          =   315
      Index           =   0
      Left            =   2280
      TabIndex        =   1
      Top             =   270
      Width           =   1740
   End
End
Attribute VB_Name = "AIM_About"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub cmdClose_Click()
On Error GoTo ErrorHandler

    Unload Me
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_About::AboutTextLabel_Click", Now, gDRGeneralError, True, Err
End Sub

Private Sub Form_Load()
On Error GoTo ErrorHandler
  
    GetTranslatedCaptions Me
    
    AboutAIM
      
    SetUIStandards
    
Exit Sub
ErrorHandler:
    f_HandleErr , , , "AIM_About::Form_Load", Now, gDRGeneralError, True, Err
End Sub

Private Function AboutAIM()
On Error GoTo ErrorHandler

    Dim CopyRight As String
    Dim DevNames As String
    Dim CurrentRev As String
    
    Me.Caption = "SSA Global :: " & getTranslationResource("About") & " :: " & getTranslationResource(App.Title)
    
    CopyRight = getTranslationResource("Copyright")
    If StrComp(CopyRight, "Copyright") = 0 Then CopyRight = App.LegalCopyright
    
    'App.Info should contain up-to-date versions for these, especially for a primary release
    AboutLabel(0).Caption = getTranslationResource("Version", True)
    AboutTextLabel(0).Caption = Format(App.Major, "#0") & "." & _
                        Format(App.Minor, "#0") & "." & _
                        Format(App.Revision, "###0") & ".0"
    'AboutLabel(1).Caption = getTranslationResource("Licensed to", True)
    AboutLabel(1).Visible = False
    AboutTextLabel(1).Visible = False
    
    AboutTextLabel(2).Caption = getTranslationResource("SSA Adaptive Inventory Manager")
    
    
    'BEGIN -- Comment out if NOT a secondary release
        CurrentRev = "2"
        AboutTextLabel(0).Caption = Format(App.Major, "#0") & "." & _
                            Format(App.Minor, "#0") & "." & _
                            Format(CurrentRev, "#0") & "." & _
                            Format(App.Revision, "###0") '& ".0"
    'END -- Comment out if secondary release
'    'BEGIN -- Comment out if not BETA
'        lblBETA.Visible = True
'        lblBETA.ForeColor = vbRed
'        lblBETA.FontBold = True
'        lblBETA.Caption = "BETA"
'    'END -- Comment out if not BETA
    
    txtCopyright(0).Text = getTranslationResource(CopyRight)
    
    DevNames = "Riza, W.; Stocksdale, A.; Surve, S.; Uddanti S.; Uskert, M."
    txtAdditionalInfo.Text = CopyRight & vbCrLf & _
                        getTranslationResource(App.CompanyName) & vbCrLf & _
                        getTranslationResource("500 West Madison") & vbCrLf & _
                        getTranslationResource("Suite 1600") & vbCrLf & _
                        getTranslationResource("Chicago, IL 60661 USA") & vbCrLf & _
                        getTranslationResource("Tel ****** 258 6000") & vbCrLf & _
                        getTranslationResource("www.ssaglobal.com") & vbCrLf & _
                        Trim(DevNames)
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_About::AboutAIM", Now, gDRGeneralError, True, Err
End Function

Private Function SetUIStandards()
On Error GoTo ErrorHandler
    
    Dim IndexCounter As Long
    
    cmdClose.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_COMMANDBUTTON, COLOR_TYPE_SELECTED)
    
    For IndexCounter = 0 To AboutLabel.Count - 1
        AboutLabel(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_LABEL, COLOR_TYPE_NORMAL)
        AboutLabel(IndexCounter).ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_VB_LABEL, COLOR_TYPE_INACTIVE)
    Next
    
    For IndexCounter = 0 To AboutTextLabel.Count - 1
        AboutTextLabel(IndexCounter).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_LABEL, COLOR_TYPE_NORMAL)
        AboutTextLabel(IndexCounter).ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_VB_LABEL, COLOR_TYPE_INACTIVE)
    Next
    
    txtCopyright(0).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FRAME, COLOR_TYPE_SELECTED)
    txtCopyright(0).ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_VB_LABEL, COLOR_TYPE_INACTIVE)
    
    txtCopyright(1).BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FRAME, COLOR_TYPE_SELECTED)
    txtCopyright(1).ForeColor = txtCopyright(1).BackColor
    
    txtAdditionalInfo.BackColor = UI_GetColor(TARGET_BACKCOLOR, CTRL_VB_FRAME, COLOR_TYPE_INACTIVE)
    txtAdditionalInfo.ForeColor = UI_GetColor(TARGET_FORECOLOR, CTRL_VB_LABEL, COLOR_TYPE_NORMAL)
    
Exit Function
ErrorHandler:
    f_HandleErr , , , "AIM_About::SetUIStandards", Now, gDRGeneralError, True, Err
End Function

