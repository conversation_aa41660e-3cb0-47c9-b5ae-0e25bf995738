Attribute VB_Name = "AIMFcstPlanner"
Option Explicit
'Dim AC As New AIMAdvCalendar
'Dim Cn As New ADODB.Connection
'RecordSet Objects
Dim rsVnItemList As New ADODB.Recordset
Dim rsFcstMaint As New ADODB.Recordset
Dim rsSysCtrl As New ADODB.Recordset
Dim rsBatchData As New ADODB.Recordset
Dim rsGetProdConstData As New Recordset
Dim rsUniqueId As New Recordset
Dim rsCount As New ADODB.Recordset
Dim rsUserElement As New ADODB.Recordset

Dim rsItemHistory As New ADODB.Recordset
'Command objects
Dim AIM_SysCtrl_Get_Sp As New ADODB.Command
Dim AIM_ProdConstraintTemp_Save_Sp As New ADODB.Command
Dim AIM_ProductionConstraintCtrl_Sp As New ADODB.Command
Dim AIM_ProdConstraintTemp_Get_Sp As New ADODB.Command
Dim AIM_GuId_Get_SP As New ADODB.Command
Dim cmdDelete As New ADODB.Command
Dim FetchBatch As New ADODB.Command
Dim FetchCount As New ADODB.Command
Dim DPAIM_DmdPlan_BatchUserElement_Get_Sp As New ADODB.Command
Dim AIM_UserElementDetail_Get_Sp As New ADODB.Command
Dim AIM_ItemHistory_GetEq_Sp As New ADODB.Command
Dim AIM_UserElementDetail_Save_Sp As New ADODB.Command
Dim prm As ADODB.Parameter

Dim Assort As String
Dim SAVersion As Integer

Public Enum FCST_ACCESS
    CODE_FullControl = 0
    CODE_ModifyForecastSetup = 1
    CODE_ModifyDemandPlan = 2
    CODE_Read = 3
    CODE_ControlAccess = 4
    CODE_Promote = 5
End Enum
Public Enum ADJUST_TYPE   'Forecast qty adjustment factor
    ADJ_OVERRIDE = 0
    ADJ_UNITS = 1
    ADJ_percent = 2
    ADJ_REMOVEOVERRIDE = 3
    ADJ_REMOVEMASTEROVERRIDE = 4
End Enum
Public Type FORECAST_CRITERIA
    FcstSetupKey As String
    FcstId As String
    FcstDesc As String
    FcstLocked As Boolean
    FcstEnabled As Boolean
    FcstHierarchy As Long
    FcstStartDate As Date
    FcstRolling As Boolean
    FcstPeriods_Future As Long
    FcstHistory As Boolean
    FcstPeriods_Historical As Long
    FreezePds As Long
    FixedPds As Long
    ApplyTrend As Long
    FcstInterval As Long
    FcstUnit As Long
    Calc_SysFcst As Boolean
    Calc_Netreq As Boolean
    Calc_ProjInv As Boolean
    Calc_HistDmd As Boolean
    Calc_MasterFcstAdj As Boolean
    Calc_FcstAdj As Boolean
    Calc_AdjNetReq As Boolean
    Calc_ProdConst As Boolean
    'Filters
    Item As String
    ItStat As String
    VnId As String
    Assort As String
    ById As String
    Class1 As String
    Class2 As String
    Class3 As String
    Class4 As String
    LcId As String
    LStatus As String
    LDivision As String
    LRegion As String
    LUserDefined As String
End Type


Private Type ACTUALSALES_RCD
    Fy As Integer
    Sale As Double
    Pd As Integer
    StartDate As Date
    EndDate As Date
End Type


Private Type UserElement_RCD
    StartDate As Date
    EndDate As Date
    Qty As Double
End Type


Private Type UserElementBatch_RCD
    LcId As String
    Item As String
    StartDate As Date
    EndDate As Date
    Qty As Double
End Type


Private Type BUDGETFCST_RCD
    Fy As Integer
    QtyFcst As Double
    QtyFcstNetReq As Double
    QtyAdjust As Double
    QtyAdjPct As Integer
    QtyAdjOverRide As Double
    QtyActualOrdered As Double
    QtyActualShipped As Double
    Pd As Integer
    StartDate As Date
    EndDate As Date
End Type


'
Private Type BFQtyPiceCost_RCD
    QtyFcst As Double
    QtyFcstNetReq As Double
    QtyAdjust As Double
    QtyActualOrdered As Double
    QtyActualShipped As Double
End Type

Dim BFRecord As BFQtyPiceCost_RCD
Dim BudgetOrForecast()  As BUDGETFCST_RCD
Dim ActualSales() As ACTUALSALES_RCD
Dim UserElementRcd() As UserElement_RCD
Dim UserElementBatchRcd() As UserElementBatch_RCD

'These variables are populate by the data send by the calling function

Private U_VnId As String
Private U_LcId As String
Private U_ItemId As String
Private U_FcstStartDate As Date
Private U_NetRqmts As Boolean
Private U_PlannedRct As Boolean
Private U_FcstInterval As AIM_INTERVALS
Private U_FcstId As String
Private U_UserElement As Boolean
Private U_FcstKey As Long
'Private U_Modification As Boolean
'Private U_ModificationChangeYN As Boolean 'Used to store wether the user is sending an array of forecast values or not


Private FcstStartDate As Date
Private FcstRolling As Boolean
Private FcstInterval As AIM_INTERVALS
Private ApplyTrend As Integer
Private FcstUnit As AIM_FORECASTUNITS
Private FuturePds As Integer
Private HistPds As Integer
Private Nbrpds As Integer
Private FixedPds As Integer
Private strMessage As String

'Public DPRollingFcstStartDate As Date 'this is the date where the future pd start
Private DPRollingFcstStartDate  As Date 'replaces the FuturePdStartDate
Private DpHistoricalStartDate As Date ' Start of the historical startdate
Private DpFutureEndDate As Date 'End of futuredate
Private FcstPdDates()  As Date 'Used to store the date between which fcst is to be made
Private NewTotalPeriods As Integer ' Total Periods of forecast


Private CurrentStartDate As Date
Private HistoricalStartDate As Date
Private AdjStartDate As Date ' This is  a date which is one period before the historicalStartDate
Private AdjEndDate As Date  ' This is a date which is one period after the FcstEndDate
Private U_UseUserFilter As Boolean 'This varible is true if the items to be forcast should be based on
                                    'Filter the user has specified
'   'A.Stocksdale - BEGIN - Multi-filters for item.
Private m_xaFilterCriteria As XArrayDB 'The filter criteria the user has send
'   'A.Stocksdale - END - Multi-filters for item.
Private FcstTypes() As String 'FcstTypes for which data is required
Private UserElements() As String 'User Elements for which data is required
Private UserElementsExists As Boolean 'Used to specify if userelement are there

Private XItems As New XArrayDB  'used to store Item related info
Private XSysFcst As New XArrayDB
Private XMasterFcstAdj As New XArrayDB
Private XFcstAdj As New XArrayDB
Private XNetReq As New XArrayDB
Private XAdjNetReq As New XArrayDB
Private XProjInv As New XArrayDB
Private XHistDmd As New XArrayDB
Private xProdConst As New XArrayDB
Private xUserElement() As New XArrayDB
Private xUserElementBatch() As New XArrayDB

'Variables used to know what to be calculated
Private USysFcst As Boolean
Private UMasterFcstAdj As Boolean
Private UFcstAdj As Boolean
Private UNetReq As Boolean
Private UAdjNetReq As Boolean
Private UProjInv As Boolean
Private UHistDmd As Boolean
Private UProdConst As Boolean
'If productionConstriant is checked then set this variable to true
Private UPopulateNetReq As Boolean


Private Function AdvForecastMain( _
xForeCast As XArrayDB, _
task As String) As Integer
On Error GoTo ErrorHandler
Dim RtnCode As Integer ' -1 failure 0 success
Dim SqlStmt As String
Dim Rows As Long
Dim i As Integer
Dim WhereClause As String
    RtnCode = -1
    'Open the System Control Recordset
    With rsSysCtrl
        .Open AIM_SysCtrl_Get_Sp
    End With
    
    'Set the SQL Statement
    SqlStmt = SqlStmt + "Select VnId,Assort,LcId,Item,Class1,Class2,Class3,Class4,ById,ItStat, " & _
            " LDivision, LStatus, LRegion, LUserDefined, "
    SqlStmt = SqlStmt + "FcstStartDate,FcstRolling,FcstInterval,ApplyTrend,FcstUnit,"
    SqlStmt = SqlStmt + " FcstPds_Future,FcstPds_Historical,FixedPds From AimFcstSetUp where FcstId = N'" & U_FcstId & "'"

    'Retrieve the data
    If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
    rsFcstMaint.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
    
    If f_IsRecordsetOpenAndPopulated(rsFcstMaint) Then
        WhereClause = g_BuildWhereClause(m_xaFilterCriteria)
        'Get the Seasonality Table Version Number
        SAVersion = g_GetSAVersion(Cn)
        SqlStmt = g_QueryForFcstItems(False, WhereClause, SAVersion, False)
        
        If f_IsRecordsetValidAndOpen(rsVnItemList) Then rsVnItemList.Close
        rsVnItemList.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
        
        If f_IsRecordsetOpenAndPopulated(rsVnItemList) Then
            'Populate the local variables from the rsFcstMaint record set
            FcstRolling = rsFcstMaint("FcstRolling").Value
            
            FcstInterval = GetForecastInterval(rsFcstMaint("FcstInterval").Value)
            'FcstInterval = U_FcstInterval
            ApplyTrend = rsFcstMaint("ApplyTrend").Value
            FcstUnit = GetForecastUnit(rsFcstMaint("FcstUnit").Value)
            FcstUnit = 0 ' always return units
            FuturePds = rsFcstMaint("FcstPds_Future").Value
            HistPds = rsFcstMaint("FcstPds_Historical").Value
            FixedPds = rsFcstMaint("FixedPds").Value
            If FcstRolling = False Then
                HistPds = 0
                FuturePds = FixedPds
            Else
            End If
            Nbrpds = FuturePds + HistPds
            Rows = rsVnItemList.RecordCount
            If FcstRolling = False Then
                'FcstStartDate = rsFcstMaint("FcstStartDate").Value
                'FcstStartDate = Now
                FcstStartDate = GetRollingFcstStartDate(rsFcstMaint("FcstStartDate").Value, rsFcstMaint("FcstStartDate").Value, AC, FcstInterval)
            Else
                FcstStartDate = GetRollingFcstStartDate(Now(), rsFcstMaint("FcstStartDate").Value, AC, FcstInterval)
            End If
            CurrentStartDate = GetRollingFcstStartDate(FcstStartDate, FcstStartDate, AC, FcstInterval)
            HistoricalStartDate = GetHistoricalFcstStartDate(CurrentStartDate, HistPds, AC, FcstInterval)
            AdjStartDate = GetHistoricalFcstStartDate(CurrentStartDate, HistPds + 1, AC, FcstInterval)
            AdjEndDate = GetHistoricalFcstStartDate(CurrentStartDate, -(FuturePds + 1), AC, FcstInterval) - 1
            
            SetPeriodBounds
        
            'Initialize the xarrays
            XItems.ReDim 0, Rows, 0, 22
            USysFcst = False
            UMasterFcstAdj = False
            UFcstAdj = False
            UNetReq = False
            UAdjNetReq = False
            UProjInv = False
            UHistDmd = False
            UProdConst = False
            UPopulateNetReq = False
            For i = 0 To UBound(FcstTypes())
                If FcstTypes(i) = "SYSFCST" Then
                    XSysFcst.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    USysFcst = True
                ElseIf FcstTypes(i) = "MASTERFCSTADJ" Then
                    XMasterFcstAdj.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UMasterFcstAdj = True
                ElseIf FcstTypes(i) = "FCSTADJ" Then
                    XFcstAdj.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UFcstAdj = True
                ElseIf FcstTypes(i) = "NETREQ" Then
                    XNetReq.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UNetReq = True
                ElseIf FcstTypes(i) = "ADJNETREQ" Then
                    XAdjNetReq.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UAdjNetReq = True
                ElseIf FcstTypes(i) = "PROJINV" Then
                    XProjInv.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UProjInv = True
                ElseIf FcstTypes(i) = "HISDMD" Then
                    XHistDmd.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UHistDmd = True
                ElseIf FcstTypes(i) = "PRODCONST" Then
                    xProdConst.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                    UProdConst = True
                    UPopulateNetReq = True
                    XNetReq.ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                End If
            Next i
            If UserElementsExists = True Then
                ReDim xUserElement(0 To UBound(UserElements()))
                For i = 0 To UBound(UserElements())
                    xUserElement(i).ReDim 0, Rows, 0, (NewTotalPeriods - 1)
                Next i
            End If
            UpdateForecast Rows, rsVnItemList, xForeCast
        End If
    End If
    RtnCode = 1
CleanUp:
    AdvForecastMain = RtnCode     'success
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.AdvForecastMain)"
    f_HandleErr , , , "AIMFcsPlanner::AdvForecastMain", Now, gDRJobError, True, Err
    RtnCode = -1
    GoTo CleanUp
End Function
Private Function Initialize_Objects() As Integer
On Error GoTo ErrorHandler
With AIM_SysCtrl_Get_Sp
    Set .ActiveConnection = Cn
    .CommandType = adCmdStoredProc
    .CommandText = "AIM_SysCtrl_Get_Sp"
    .Parameters.Refresh
End With
With FetchBatch
    Set .ActiveConnection = Cn
    .CommandType = adCmdText
    .Prepared = True
End With
'Command object for the Stored Procedure
With FetchCount
    Set .ActiveConnection = Cn
    .CommandType = adCmdText
    .Prepared = True
End With

With DPAIM_DmdPlan_BatchUserElement_Get_Sp
    Set .ActiveConnection = Cn
    .CommandType = adCmdStoredProc
    .CommandText = "DPAIM_DmdPlan_BatchUserElement_Get_Sp"
    .Parameters.Refresh
End With
With AIM_ItemHistory_GetEq_Sp
    Set .ActiveConnection = Cn
    .CommandType = adCmdStoredProc
    .CommandText = "AIM_ItemHistory_GetEq_Sp"
    .Parameters.Refresh
End With

With AIM_UserElementDetail_Get_Sp
    Set .ActiveConnection = Cn
    .CommandType = adCmdStoredProc
    .CommandText = "AIM_UserElementDetail_Get_Sp"
    .Parameters.Refresh
End With

'Get the Location Data
If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
With rsItemHistory
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
End With
If f_IsRecordsetValidAndOpen(rsUserElement) Then rsUserElement.Close
With rsUserElement
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
End With
'Initialize the System Control Recordset
If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
With rsSysCtrl
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
    .CursorType = adOpenStatic
End With
If f_IsRecordsetValidAndOpen(rsBatchData) Then rsBatchData.Close
With rsBatchData
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
    .CursorType = adOpenStatic
End With
'Initialize the ForeCastMaint Item List Resultset -- fire hose
If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
With rsFcstMaint
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
    .CursorType = adOpenForwardOnly
End With
'Initialize the Vendor Item List Resultset -- fire hose
If f_IsRecordsetValidAndOpen(rsVnItemList) Then rsVnItemList.Close
With rsVnItemList
    .CursorLocation = adUseClient
    .LockType = adLockReadOnly
    .CursorType = adOpenForwardOnly
End With
If f_IsRecordsetValidAndOpen(rsBatchData) Then rsBatchData.Close
With rsBatchData
    .CursorLocation = adUseClient
    .CursorType = adOpenStatic
    .LockType = adLockReadOnly
End With
If f_IsRecordsetValidAndOpen(rsCount) Then rsCount.Close
With rsCount
    .CursorLocation = adUseClient
    .CursorType = adOpenStatic
    .LockType = adLockReadOnly
End With
'    'Initialize Stored Procedures
'    With AIM_RepositoryDetail_Validate_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_RepositoryDetail_Validate_Sp"
'        .Parameters.Refresh
'    End With
'
'    With AIM_ForecastRepository_GetKey_Sp
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_ForecastRepository_GetKey_Sp"
'        .CommandType = adCmdStoredProc
'    End With
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@Return", adInteger, adParamReturnValue, , 0)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@FcstID", adVarChar, adParamInput, 12, "")
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@UserElement", adBoolean, adParamInput, , 0)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_GetKey_Sp.CreateParameter("@RepositoryKey", adInteger, adParamOutput, , 0)
'    AIM_ForecastRepository_GetKey_Sp.Parameters.Append prm


'    With AIM_ForecastRepository_Save_Sp
'        Set .ActiveConnection = Cn
'        .CommandText = "AIM_ForecastRepository_Save_Sp"
'        .CommandType = adCmdStoredProc
'    End With
'    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@Return", adInteger, adParamReturnValue, , 0)
'    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@UserID", adVarChar, adParamInput, 12, "")
'    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@FcstID", adVarChar, adParamInput, 12, "")
'    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@UserElement", adBoolean, adParamInput, , 0)
'    AIM_ForecastRepository_Save_Sp.Parameters.Append prm
'    Set prm = AIM_ForecastRepository_Save_Sp.CreateParameter("@RepositoryKey", adInteger, adParamOutput, , 0)
'    AIM_ForecastRepository_Save_Sp.Parameters.Append prm

'
'     With AIM_UserElements_Get_Sp
'        Set .ActiveConnection = Cn
'        .CommandType = adCmdStoredProc
'        .CommandText = "AIM_UserElements_Get_Sp"
'        .Parameters.Refresh
'    End With
    With AIM_UserElementDetail_Save_Sp
        Set .ActiveConnection = Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_UserElementDetail_Save_Sp"

    'Declare parameters
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("@UserElementId", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("@LcID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("@Item", adVarWChar, adParamInput, 25)
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("@FcstPdBegDate", adDBDate, adParamInput)
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("@FcstPdEndDate", adDBDate, adParamInput)
        .Parameters.Append AIM_UserElementDetail_Save_Sp.CreateParameter("@Qty", adDecimal, adParamInput)
        .Parameters("@Qty").Precision = 10
        .Parameters("@Qty").NumericScale = 2
    End With
    
'    With m_rsUserElements
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenStatic
'    End With
'    With m_rsFcstMaint
'        .CursorLocation = adUseClient
'        .LockType = adLockReadOnly
'        .CursorType = adOpenForwardOnly
'    End With

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.Initialize_Objects)"
     f_HandleErr , , , "AIMFcsPlanner::Initialize_Objects", Now, gDRJobError, True, Err
End Function
Private Function Destroy_Objects() As Integer
On Error GoTo ErrorHandler
    If f_IsRecordsetValidAndOpen(rsCount) Then rsCount.Close
    Set rsCount = Nothing

    If f_IsRecordsetValidAndOpen(rsUserElement) Then rsUserElement.Close
    Set rsUserElement = Nothing

    If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
    Set rsItemHistory = Nothing

    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing

     If f_IsRecordsetValidAndOpen(rsBatchData) Then rsBatchData.Close
    Set rsBatchData = Nothing
    
    If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
    Set rsFcstMaint = Nothing

   If f_IsRecordsetValidAndOpen(rsVnItemList) Then rsVnItemList.Close
    Set rsVnItemList = Nothing
    
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then
    Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
        Set AIM_SysCtrl_Get_Sp = Nothing
    End If

    If Not (FetchBatch Is Nothing) Then
    Set FetchBatch.ActiveConnection = Nothing
        Set FetchBatch = Nothing
    End If
    If Not (FetchCount Is Nothing) Then
    Set FetchCount.ActiveConnection = Nothing
        Set FetchCount = Nothing
    End If
    If Not (AIM_UserElementDetail_Save_Sp Is Nothing) Then
    Set AIM_UserElementDetail_Save_Sp.ActiveConnection = Nothing
        Set AIM_UserElementDetail_Save_Sp = Nothing
    End If
    If Not (DPAIM_DmdPlan_BatchUserElement_Get_Sp Is Nothing) Then
        Set DPAIM_DmdPlan_BatchUserElement_Get_Sp.ActiveConnection = Nothing
    Set DPAIM_DmdPlan_BatchUserElement_Get_Sp = Nothing
    End If
    If Not (AIM_UserElementDetail_Get_Sp Is Nothing) Then
        Set AIM_UserElementDetail_Get_Sp.ActiveConnection = Nothing
    Set AIM_UserElementDetail_Get_Sp = Nothing
    End If
    If Not (AIM_ItemHistory_GetEq_Sp Is Nothing) Then
    Set AIM_ItemHistory_GetEq_Sp.ActiveConnection = Nothing
        Set AIM_ItemHistory_GetEq_Sp = Nothing
     End If
    If Not (AIM_UserElementDetail_Save_Sp Is Nothing) Then
    Set AIM_UserElementDetail_Save_Sp.ActiveConnection = Nothing
        Set AIM_UserElementDetail_Save_Sp = Nothing
     End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.Destroy_Objects)"
     f_HandleErr , , , "AIMFcsPlanner::Destroy_Objects", Now, gDRJobError, True, Err
End Function


Public Function GetForecastMaint( _
    FcstIdArg As String, _
    xForeCast As XArrayDB, _
    FcstStartdateArg As Date, _
    IntToFcstArg As AIM_INTERVALS, _
    xaFilterCriteria As XArrayDB, _
    ArgFcstTypes() As String, _
    ArgUserElements() As String, _
    ArgSaveFcst As Boolean, _
    ArgSaveUserElement As Boolean _
) As Integer
On Error GoTo ErrorHandler
    Dim RtnCode As Integer  '-1 failure 0 success
    Dim i As Integer
    RtnCode = -1
    U_FcstStartDate = FcstStartdateArg
    U_FcstInterval = GetForecastInterval(IntToFcstArg)
    U_FcstId = FcstIdArg
    g_CXArr1ToXArr2 xaFilterCriteria, m_xaFilterCriteria

    
    ReDim FcstTypes(0 To UBound(ArgFcstTypes))
    For i = 0 To UBound(FcstTypes)
        FcstTypes(i) = ArgFcstTypes(i)
    Next
    
    If UBound(ArgUserElements()) = 0 Then
        If ArgUserElements(0) = "" Or IsNull(ArgUserElements(0)) Then
          'There are no user elements selected  so no need to add to FcstTypes array
          UserElementsExists = False
        Else
          UserElementsExists = True
        End If
    Else
        UserElementsExists = True
    End If
    If UserElementsExists = True Then
    ReDim UserElements(0 To UBound(ArgUserElements))
        For i = 0 To UBound(ArgUserElements)
            UserElements(i) = ArgUserElements(i)
        Next
    End If
    'Connect to the database
    'If CnConnected = False Then
    If Cn.State <> adStateOpen Then
        RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , "AIMForecast.bas")
        If RtnCode <> SUCCEED Then
            GetForecastMaint = RtnCode
            Exit Function
        End If
        CnConnected = True
    End If
    If AIMCalenderLoaded = False Then
        AIMAdvCalendar_Load Cn, AC
        AIMCalenderLoaded = True
    End If
    Initialize_Objects
    RtnCode = AdvForecastMain(xForeCast, "ForeCast")
    If ArgSaveFcst = True Then
        BatchFcst xForeCast
    End If
    If ArgSaveUserElement = True Then
        PopulateUserElementData xForeCast
    End If
    RtnCode = 1
CleanUp:
Destroy_Objects
GetForecastMaint = RtnCode
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetForecastMaint)"
     f_HandleErr , , , "AIMFcsPlanner::GetForecastMaint", Now, gDRJobError, True, Err
    RtnCode = -1
    GoTo CleanUp
End Function
Function GetHistoricalFcstStartDate(StartDate As Date, HistoricalPds As Integer, AC As AIMAdvCalendar, Interval As AIM_INTERVALS) As Date
On Error GoTo ErrorHandler
'This function should be rewritten similer to getnextstartdate with modifications
'This works only for days weeks and months,4wks and quarters
Dim FcstStartDate As Date
Dim MyStartDate As Date
Dim i As Integer
Dim Fy As Integer
Dim Pd As Integer
'StartDate = CDate("2005-01-08")
Select Case Interval
Case int_Days
    GetHistoricalFcstStartDate = StartDate - HistoricalPds
Case int_Weeks
    FcstStartDate = StartDate
    For i = 1 To Abs(HistoricalPds)
            If Sgn(HistoricalPds) * (-1) = -1 Then
                FcstStartDate = GetPreviousStartDate(StartDate, FcstStartDate, Interval)
            Else
                FcstStartDate = GetNextStartDate(StartDate, FcstStartDate, Interval)
            End If
    Next i
    GetHistoricalFcstStartDate = AC.GetStartDateFmDate(FcstStartDate)

Case int_months
    GetHistoricalFcstStartDate = DateAdd("m", -HistoricalPds, StartDate)
Case int_Quarters
    GetHistoricalFcstStartDate = DateAdd("q", -HistoricalPds, StartDate)
Case int_4Wks, int_454, int_445, int_544
     FcstStartDate = StartDate
     For i = 1 To Abs(HistoricalPds)
        If Sgn(HistoricalPds) * (-1) = -1 Then
            FcstStartDate = GetPreviousStartDate(StartDate, FcstStartDate, Interval)
        Else
            FcstStartDate = GetNextStartDate(StartDate, FcstStartDate, Interval)
        End If
     Next i
    GetHistoricalFcstStartDate = FcstStartDate
'Case int_454
'     FcstStartDate = StartDate
'     MyStartDate = CDate("2004-01-01")
'     For I = 1 To Abs(HistoricalPds)
'        If Sgn(HistoricalPds) * (-1) = -1 Then
'            FcstStartDate = GetPreviousStartDate(MyStartDate, FcstStartDate, int_454)
'        Else
'            FcstStartDate = GetNextStartDate(MyStartDate, FcstStartDate, int_454)
'        End If
'     Next I
'    GetHistoricalFcstStartDate = FcstStartDate
End Select
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.GetHistoricalFcstStartDate)"
     f_HandleErr , , , "AIMFcsPlanner::GetHistoricalFcstStartDate", Now, gDRJobError, True, Err
End Function
Function GetNextRollingFcstStartDate(StartDate As Date, AC As AIMAdvCalendar, Interval As AIM_INTERVALS) As Date
On Error GoTo ErrorHandler

Dim FyDayStart As Integer

If Interval = int_Weeks Or Interval = int_445 Or Interval = int_454 Or Interval = int_4Wks Or Interval = int_544 Then
    GetNextRollingFcstStartDate = AC.GetStartDateFmDate(StartDate)
    If GetNextRollingFcstStartDate <> StartDate Then
        GetNextRollingFcstStartDate = AC.GetStartDateFmDate(StartDate + 7)
    End If
ElseIf (Interval = int_months Or Interval = int_Quarters) Then
    FyDayStart = Day(AC.GetFiscalYearStartDate(Year(StartDate)))
    GetNextRollingFcstStartDate = CDate(CStr(Month(StartDate)) + "-" + CStr(FyDayStart) + "-" + CStr(Year(StartDate)))
    If GetNextRollingFcstStartDate <> StartDate Then
        GetNextRollingFcstStartDate = CDate(CStr(Month(StartDate) + 1) + "-" + CStr(FyDayStart) + "-" + CStr(Year(StartDate)))
    End If
End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(GetNextRollingFcstStartDate)"
     f_HandleErr , , , "AIMFcsPlanner::GetNextRollingFcstStartDate", Now, gDRJobError, True, Err
End Function
Function GetRollingFcstStartDate(CurrentDate As Date, StartDate As Date, AC As AIMAdvCalendar, Interval As AIM_INTERVALS) As Date
On Error GoTo ErrorHandler
Dim i As Integer
Dim FyDayStart As Integer
'Dim CurrentDate As Date
Dim tempDate As Date
Dim LastStartDate As Date
'CurrentDate = Now()
If Interval = int_Weeks Then
    GetRollingFcstStartDate = AC.GetStartDateFmDate(CurrentDate)
ElseIf Interval = int_4Wks Or Interval = int_445 Or Interval = int_454 Or Interval = int_544 Then
    'CurrentDate = AC.GetStartDateFmDate(CDate("2004-12-30"))
    tempDate = StartDate
    LastStartDate = StartDate
    For i = 1 To 156
        tempDate = GetNextStartDate(StartDate, tempDate, Interval)
        If tempDate > CurrentDate Then
            GetRollingFcstStartDate = LastStartDate
            Exit For
        Else
            LastStartDate = tempDate
        End If
    Next i
ElseIf (Interval = int_months) Then
    'FyDayStart = Day(AC.GetFiscalYearStartDate(Year(StartDate)))
    FyDayStart = Day(AC.GetFiscalYearStartDate(Year(CurrentDate)))
    GetRollingFcstStartDate = CDate(CStr(Month(CurrentDate)) + "-" + CStr(FyDayStart) + "-" + CStr(Year(CurrentDate)))
ElseIf Interval = int_Quarters Then
    FyDayStart = Day(AC.GetFiscalYearStartDate(Year(StartDate)))
    StartDate = CDate(CStr(Month(StartDate)) + "-" + CStr(FyDayStart) + "-" + CStr(Year(StartDate)))
    'CurrentDate = AC.GetStartDateFmDate(CDate("2005-11-15"))
    tempDate = StartDate
    LastStartDate = StartDate
    For i = 1 To 156
        tempDate = GetNextStartDate(StartDate, tempDate, Interval)
        If tempDate > CurrentDate Then
            GetRollingFcstStartDate = LastStartDate
            Exit For
        Else
            LastStartDate = tempDate
        End If
    Next i

End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.GetRollingFcstStartDate)"
     f_HandleErr , , , "AIMFcsPlanner::GetRollingFcstStartDate", Now, gDRJobError, True, Err
End Function

Public Function Populate_Dates(Interval As AIM_INTERVALS, _
Nbrpds As Integer, StartDate As Date, FcstDates() As Date)
On Error GoTo ErrorHandler

Dim PdEndDate As Date
Dim PdStartDate As Date
Dim i As Integer
Dim StartPd As Integer
Dim EndPd As Integer

PdStartDate = StartDate
PdEndDate = StartDate

ReDim FcstDates(0 To Nbrpds)
FcstDates(0) = PdStartDate

For i = 1 To Nbrpds
PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, Interval)
If FcstInterval <> int_months Or FcstInterval <> int_Quarters Then
    StartPd = AC.GetPeriod(PdEndDate)
    EndPd = AC.GetPeriod(PdEndDate - 7)
    If StartPd = 52 And EndPd = 52 Then PdEndDate = AC.GetEndDate(PdEndDate) + 1
End If

FcstDates(i) = PdEndDate
PdStartDate = PdEndDate
Next
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Populate_FcstDates)"
    f_HandleErr , , , "AIMFcsPlanner::Populate_Dates", Now, gDRJobError, True, Err
End Function

Public Function Populate_FcstDates(Interval As AIM_INTERVALS, _
NbrPds_Hist As Integer, NbrPds_Future As Integer, FcstStartDate As Date, FcstDates() As Date)
On Error GoTo ErrorHandler

Dim DPRollingFcstStartDate As Date

Dim PdEndDate As Date
Dim PdStartDate As Date
Dim i As Integer
Dim StartPd As Integer
Dim EndPd As Integer

DPRollingFcstStartDate = GetRollingFcstStartDate(FcstStartDate, FcstStartDate, AC, Interval)

PdStartDate = DPRollingFcstStartDate
PdEndDate = DPRollingFcstStartDate

ReDim FcstDates(0 To NbrPds_Future)
FcstDates(0) = PdStartDate

For i = 1 To NbrPds_Future  'So that the end date will be showing the end of the period
PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, Interval)
If Interval <> int_months Or Interval <> int_Quarters Then
    StartPd = AC.GetPeriod(PdEndDate)
    EndPd = AC.GetPeriod(PdEndDate - 7)
    If StartPd = 52 And EndPd = 52 Then PdEndDate = AC.GetEndDate(PdEndDate) + 1
End If
FcstDates(i) = PdEndDate
PdStartDate = PdEndDate
Next
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Populate_FcstDates)"
    f_HandleErr , , , "AIMFcsPlanner::Populate_FcstDates", Now, gDRJobError, True, Err
End Function

Public Function GetPreviousStartDate( _
    BaseDate As Date, _
    LastStartDate As Date, _
    Interval As AIM_INTERVALS _
) As Date
On Error GoTo ErrorHandler

    Dim NbrWks As Integer
    Dim i As Integer
    Dim Pd As Integer
    Dim Fy As Integer
    Dim FcstDate As Date
    Dim WeekCount As Integer
    'Calculate Number of Weeks between Base Date and Last Start Date
    NbrWks = DateDiff("ww", BaseDate, LastStartDate)
    Do Until NbrWks < 52
        NbrWks = NbrWks - 52
    Loop

    Select Case Interval
    Case int_Days
        GetPreviousStartDate = LastStartDate - 1

    Case int_Weeks
        FcstDate = LastStartDate
        Fy = AC.GetFiscalYear(FcstDate)
        Pd = AC.GetPeriod(FcstDate)
        GetPreviousStartDate = DateAdd("ww", -1, FcstDate)
        If Pd = 1 Then GetPreviousStartDate = AC.GetStartDateFmDate(GetPreviousStartDate)
    Case int_months
        GetPreviousStartDate = DateAdd("m", -1, LastStartDate)

    Case int_Quarters
        GetPreviousStartDate = DateAdd("q", -1, LastStartDate)

    Case int_544
        Select Case NbrWks
        Case 5, 18, 31, 44
            WeekCount = 5
            'GetPreviousStartDate = DateAdd("ww", -5, LastStartDate)
        Case Else
            WeekCount = 4
            'GetPreviousStartDate = DateAdd("ww", -4, LastStartDate)
        End Select
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", -1, FcstDate)
            If Pd = 1 Then FcstDate = AC.GetStartDateFmDate(FcstDate)
        Next
        GetPreviousStartDate = FcstDate

    Case int_454
        Select Case NbrWks
        Case 9, 22, 35, 48
            WeekCount = 5
            'GetPreviousStartDate = DateAdd("ww", -5, LastStartDate)
        Case Else
            WeekCount = 4
            'GetPreviousStartDate = DateAdd("ww", -4, LastStartDate)
        End Select
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", -1, FcstDate)
            If Pd = 1 Then FcstDate = AC.GetStartDateFmDate(FcstDate)
        Next
        GetPreviousStartDate = FcstDate
    Case int_445
        Select Case NbrWks
        Case 13, 26, 39, 52
            WeekCount = 5
            'GetPreviousStartDate = DateAdd("ww", -5, LastStartDate)
        Case Else
            WeekCount = 4
            'GetPreviousStartDate = DateAdd("ww", -4, LastStartDate)
        End Select
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", -1, FcstDate)
            If Pd = 1 Then FcstDate = AC.GetStartDateFmDate(FcstDate)
        Next
        GetPreviousStartDate = FcstDate

    Case int_4Wks
        WeekCount = 4
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", -1, FcstDate)
            If Pd = 1 Then FcstDate = AC.GetStartDateFmDate(FcstDate)
        Next
        GetPreviousStartDate = FcstDate
    Case Else
        GetPreviousStartDate = LastStartDate - 1

    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMForecast.GetPreviousStartDate)"
     f_HandleErr , , , "AIMFcsPlanner::GetPreviousStartDate", Now, gDRJobError, True, Err
End Function




Public Function GetNextStartDate( _
    BaseDate As Date, _
    LastStartDate As Date, _
    Interval As AIM_INTERVALS _
) As Date
'This function was duplicated in AIMAdvForecast.cls and AIMAdvForecastMod.cls
'... but need not be inside a class module at all. Hence moved out to .bas
On Error GoTo ErrorHandler

    Dim NbrWks As Integer
    Dim i As Integer
    Dim Pd As Integer
    Dim Fy As Integer
    Dim FcstDate As Date
    Dim WeekCount As Integer
    
    'Calculate Number of Weeks between Base Date and Last Start Date
    NbrWks = DateDiff("ww", BaseDate, LastStartDate)
    Do Until NbrWks < 52
        NbrWks = NbrWks - 52
    Loop

    Select Case Interval
    Case int_Days
        GetNextStartDate = LastStartDate + 1

    Case int_Weeks
        FcstDate = LastStartDate
        Fy = AC.GetFiscalYear(FcstDate)
        Pd = AC.GetPeriod(FcstDate)
        GetNextStartDate = DateAdd("ww", 1, LastStartDate)
        If Pd = 52 Then GetNextStartDate = AC.GetFiscalYearStartDate(Fy + 1)
    Case int_months
        GetNextStartDate = DateAdd("m", 1, LastStartDate)

    Case int_Quarters
        GetNextStartDate = DateAdd("q", 1, LastStartDate)

    Case int_544
        Select Case NbrWks
        Case 0, 13, 26, 39
            WeekCount = 5
            'GetNextStartDate = DateAdd("ww", 5, LastStartDate)
        Case Else
            WeekCount = 4
            'GetNextStartDate = DateAdd("ww", 4, LastStartDate)
        End Select
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", 1, FcstDate)
            If Pd = 52 Then FcstDate = AC.GetFiscalYearStartDate(Fy + 1)
        Next
        GetNextStartDate = FcstDate

    Case int_454
        Select Case NbrWks
        Case 4, 17, 30, 43
            WeekCount = 5
            'GetNextStartDate = DateAdd("ww", 5, LastStartDate)
        Case Else
            WeekCount = 4
            'GetNextStartDate = DateAdd("ww", 4, LastStartDate)
        End Select
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", 1, FcstDate)
            If Pd = 52 Then FcstDate = AC.GetFiscalYearStartDate(Fy + 1)
        Next
        GetNextStartDate = FcstDate

    Case int_445
        Select Case NbrWks
        Case 8, 21, 34, 47
            'GetNextStartDate = DateAdd("ww", 5, LastStartDate)
            WeekCount = 5
        Case Else
            WeekCount = 4
            'GetNextStartDate = DateAdd("ww", 4, LastStartDate)
        End Select
        FcstDate = LastStartDate
        For i = 1 To WeekCount
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", 1, FcstDate)
            If Pd = 52 Then FcstDate = AC.GetFiscalYearStartDate(Fy + 1)
        Next
        GetNextStartDate = FcstDate
    Case int_4Wks
        'GetNextStartDate = DateAdd("ww", 4, LastStartDate)
        FcstDate = LastStartDate
        For i = 1 To 4
            Fy = AC.GetFiscalYear(FcstDate)
            Pd = AC.GetPeriod(FcstDate)
            FcstDate = DateAdd("ww", 1, FcstDate)
            If Pd = 52 Then FcstDate = AC.GetFiscalYearStartDate(Fy + 1)
        Next
        GetNextStartDate = FcstDate
    Case Else
        GetNextStartDate = LastStartDate + 1

    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMForecast.GetNextStartDate)"
     f_HandleErr , , , "AIMFcsPlanner::GetNextStartDate", Now, gDRJobError, True, Err
End Function


Private Function UpdateForecast( _
    Rows As Long, _
    rs As ADODB.Recordset, _
    xForeCast As XArrayDB) As Integer

On Error GoTo ErrorHandler
    Dim DisplayOption As Integer
    Dim FcstCount As Long
    Dim FiscalYear As Long
    Dim Pd As Long
    Dim i As Long
    Dim Row As Long
    Dim J As Long
    Dim K As Integer ' User element loop
    Dim DataYes As Boolean

    'Control Variables
    Dim PrevItem As String

    'Group Totals
    Dim Items As Integer
    Dim Units As Long
    Dim Weight As Double
    Dim Cost As Double
    Dim Cube As Double
    Dim AdvForecast As AIMAdvForecast
    Dim tempFcst As Long
    Dim CurStartdate As Date
    Dim RowsUnitStart As Long
    Dim ForecastLoop As Long
    Dim PdStartDate As Date
    Dim PdEndDate As Date
    Dim OrgNbrPds As Integer
    Dim AdjUnit As Double
    Dim AdjCost As Double
    Dim AdjCube As Double
    Dim AdjWeight As Double
    Dim AdjPrice As Double
    Dim StartPd As Integer
    Dim EndPd As Integer
    
        OrgNbrPds = Nbrpds
        RowsUnitStart = 0
        U_FcstKey = g_GetRepositoryKey(Cn, U_FcstId)
        'Reintialize the forecast object
        Set AdvForecast = New AIMAdvForecast
        AdvForecast.InitilizeFirstTime
        'Set the Calendar
        Set AdvForecast.AIMAdvCalendar = AC
        RefreshAdjLogData
    For Row = 0 To (Rows - 1)
        AdvForecast.InitilizeVariables
        'Position the recordset
        PdStartDate = FcstStartDate
        PdEndDate = FcstStartDate
        rs.AbsolutePosition = Row + 1

        'Get the Base Date
        FiscalYear = rs("fcstupdcyc").Value \ 100  'Forecast Update Period
        Pd = rs("fcstupdcyc").Value - (FiscalYear * 100)
        AdvForecast.ApplySeasonsIndex = IIf(rs("applyseasonsindex").Value = "Y", True, False)
        AdvForecast.ApplyTrend = ApplyTrend
        AdvForecast.BaseDate = AC.GetEndDateFmPeriod(CInt(Pd), CInt(FiscalYear)) + 1
        AdvForecast.Cost = rs("cost").Value
        AdvForecast.Price = rs("Price").Value
        AdvForecast.Cube = rs("cube").Value
        AdvForecast.DmdScalingFactor = rs("dmdscalingfactor").Value
        AdvForecast.ScalingEffUntil = rs("scalingeffuntil").Value
        AdvForecast.FcstDemand = rs("fcstdemand").Value
        AdvForecast.FcstUnit = FcstUnit
        AdvForecast.HiTrndDemandLimit = rs("hitrndl").Value
        AdvForecast.Item = rs("item").Value
        AdvForecast.ItStat = rs("itstat").Value
        AdvForecast.InActDate = rs("inactdate").Value
        AdvForecast.LcId = rs("lcid").Value
        AdvForecast.PlnTT = rs("plntt").Value
        AdvForecast.UserFcst = rs("userfcst").Value
        AdvForecast.UserFcstExpDate = rs("userfcstexpdate").Value
        AdvForecast.MAE = rs("mae").Value
        AdvForecast.Trend = rs("trend").Value
        AdvForecast.MethodTrend = rs("applytrend").Value
        AdvForecast.Weight = rs("weight").Value
        AdvForecast.AdjustPct = 0
        AdvForecast.ActDate = rs("actdate").Value

        'Set Availability Variables
        AdvForecast.UOM = rs("uom").Value
        AdvForecast.ConvFactor = rs("convfactor").Value
        AdvForecast.BuyingUOM = rs("buyinguom").Value
        AdvForecast.Oh = rs("oh").Value
        AdvForecast.Oo = rs("oo").Value
        AdvForecast.ComStk = rs("comstk").Value
        AdvForecast.BkOrder = rs("bkorder").Value
        AdvForecast.BkComStk = rs("bkcomstk").Value

        'Set Order Policy Variables
        AdvForecast.BuyStrat = rs("BuyStrat").Value
        AdvForecast.UserMin = rs("UserMin").Value
        AdvForecast.UserMax = rs("UserMax").Value
        AdvForecast.DIFlag = rs("DIFlag").Value
        AdvForecast.DIMADP = rs("DIMADP").Value
        AdvForecast.Accum_Lt = rs("accum_lt").Value
        AdvForecast.ReviewTime = rs("reviewtime").Value
        AdvForecast.Fcst_Lt = rs("fcstlt").Value
        AdvForecast.PackRounding = rs("packrounding").Value
        AdvForecast.IMin = rs("imin").Value
        AdvForecast.IMax = rs("imax").Value
        AdvForecast.CStock = rs("cstock").Value
        AdvForecast.IntSafetyStock = rs("intsafetystock").Value
        AdvForecast.IsIntermittent = IIf(rs("isintermittent").Value = "Y", True, False)
        AdvForecast.Mean_NZ = rs("mean_nz").Value
        AdvForecast.StdDev_NZ = rs("stddev_nz").Value
        AdvForecast.ReplenCost2 = rs("replencost2").Value
        AdvForecast.SSAdj = rs("ssadj").Value
        AdvForecast.ZSStock = rs("zsstock").Value
        AdvForecast.DropShip_XDock = rs("dropship_xdock").Value
        AdvForecast.LtvFact = rs("ltvfact").Value
        AdvForecast.DSer = rs("dser").Value
        AdvForecast.HiMADP = rs("himadp").Value
        AdvForecast.LoMADP = rs("lomadp").Value
        AdvForecast.MADExK = rs("madexk").Value
        AdvForecast.ReplenCost = rs("replencost").Value
        AdvForecast.ZOPSw = rs("ZOPSw").Value
        AdvForecast.Int_Enabled = rsSysCtrl("Int_Enabled").Value
        AdvForecast.Int_LookBackPds = rsSysCtrl("Int_LookBackPds").Value
        AdvForecast.KFactor = rsSysCtrl("KFactor").Value
        AdvForecast.CarryFwdRoundingOption = IIf(rsSysCtrl("CarryForwardRounding").Value = "Y", True, False)
        AdvForecast.AllowNegativeAvailQty = rsSysCtrl("AllowNegativeAvailQty").Value
        'Set Review Cycle Parameters
        AdvForecast.NextDateTime = rs("nextdatetime").Value
        AdvForecast.RevFreq = rs("revfreq").Value
        AdvForecast.RevInterval = rs("revinterval").Value
        AdvForecast.RevSunday = rs("revsunday").Value
        AdvForecast.RevMonday = rs("revmonday").Value
        AdvForecast.RevTuesday = rs("revtuesday").Value
        AdvForecast.RevWednesday = rs("revwednesday").Value
        AdvForecast.RevThursday = rs("revthursday").Value
        AdvForecast.RevFriday = rs("revfriday").Value
        AdvForecast.RevSaturday = rs("revsaturday").Value
        AdvForecast.WeekQualifier = rs("weekqualifier").Value
        AdvForecast.RevStartDate = rs("revstartdate").Value
        AdvForecast.RevEndDate = rs("revenddate").Value
        AdvForecast.InitBuyPct = rs("initbuypct").Value
        AdvForecast.InitRevDate = rs("initrevdate").Value
        AdvForecast.ReviewTime = rs("reviewtime").Value
        AdvForecast.Dft_TurnHigh = rs("dft_turnhigh").Value
        AdvForecast.Dft_TurnLow = rs("dft_turnlow").Value
        AdvForecast.StkDate = rs("stkdate").Value

        'Set Promotional Data
        If rs("pmstatus").Value Then
            AdvForecast.PmStatus = rs("pmstatus").Value
            AdvForecast.PmStartDate = rs("pmstartdate").Value
            AdvForecast.PmEndDate = rs("pmenddate").Value
            For i = 1 To 52
                AdvForecast.PmAdj(i) = rs(102 + i).Value
            Next i
        End If
        For i = 1 To 52
            AdvForecast.Bi(i) = rs(44 + i).Value
        Next i
        'Set Item Price Breaks
        For i = 1 To 10
            AdvForecast.BkQty(i) = rs(190 + (2 * i)).Value
            AdvForecast.BkCost(i) = rs(191 + (2 * i)).Value
        Next i

        'Set Net Requirements
        AdvForecast.NetRqmtsOpt = U_NetRqmts
        AdvForecast.FcstKey = U_FcstKey
        AdvForecast.AdjStartDate = AdjStartDate
        AdvForecast.AdjEndDate = AdjEndDate
        AdvForecast.NbrPds_Future = FuturePds
        AdvForecast.NbrPds_Hist = HistPds
        AdvForecast.FixedPds = FixedPds
        strMessage = getTranslationResource("STATMSG90800")
        If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
        Write_Message strMessage & " " & Trim$(AdvForecast.LcId) & getTranslationResource("/") & AdvForecast.Item
        'populate the original values of interval and startdate into new variables
        AdvForecast.OrgStartDate = FcstStartDate
        AdvForecast.NewFcstInterval = U_FcstInterval
        AdvForecast.OrgNbrPds = OrgNbrPds
        AdvForecast.FcstRolling = FcstRolling
        'Reset some variables
        AdvForecast.PopulateModificationRcdQtyCalled = False
        AdvForecast.DataYes = False
        AdvForecast.MasterDataYes = False

        FcstCount = AdvForecast.GenerateForecast(FcstStartDate, Nbrpds, FcstInterval)
        If UHistDmd = True Then
            'Add code for Historical demand
            PopulateSales FcstStartDate, rs("item").Value, rs("lcid").Value, Nbrpds, FcstInterval, Row
        End If
        If UserElementsExists = True Then
            For K = 0 To UBound(UserElements())
                PopulateUserElement UserElements(K), K, rs("item").Value, rs("lcid").Value, Row
            Next K
        End If
        XItems.Value(Row, 0) = rs("lcid").Value
        XItems.Value(Row, 1) = rs("item").Value
        XItems.Value(Row, 2) = rs("itdesc").Value
        XItems.Value(Row, 3) = rs("Class1").Value
        XItems.Value(Row, 4) = rs("Class2").Value
        XItems.Value(Row, 5) = rs("Class3").Value
        XItems.Value(Row, 6) = rs("Class4").Value
        XItems.Value(Row, 7) = rs("ItStat").Value
        XItems.Value(Row, 8) = rs("VelCode").Value
        XItems.Value(Row, 9) = rs("VnId").Value
        XItems.Value(Row, 10) = rs("Assort").Value
        XItems.Value(Row, 11) = rs("ById").Value

        PdStartDate = FcstStartDate
        For i = 0 To NewTotalPeriods - 1
                    If USysFcst = True Then
                        XSysFcst.Value(Row, i) = Round(AdvForecast.Fcst(i + 1, FcstUnits), 0)
                    End If
                    If UMasterFcstAdj = True Then
                        XMasterFcstAdj.Value(Row, i) = Round(AdvForecast.MasterAdjFcst(i + 1, FcstUnits), 0)
                    End If
                    If UFcstAdj = True Then
                        XFcstAdj.Value(Row, i) = Round(AdvForecast.AdjFcst(i + 1, FcstUnits), 0)
                    End If
                    If UNetReq = True Or UPopulateNetReq = True Then
                        XNetReq.Value(Row, i) = Round(AdvForecast.NetRqmt(i + 1, FcstUnits), 0)
                    End If
                    If UAdjNetReq = True Then
                        XAdjNetReq.Value(Row, i) = Round(AdvForecast.AdjNetRqmt(i + 1, FcstUnits), 0)
                    End If
                    If UProjInv = True Then
                        'XProjInv.Value(Row, I) = Round(AdvForecast.AdjPlannedRcpts(I + 1, FcstUnits), 0)
                        If rs("dropship_xdock").Value = "N" Then
                            If i < HistPds Then
                                XProjInv.Value(Row, i) = 0
                            ElseIf i = HistPds Then
                                XProjInv.Value(Row, i) = AdvForecast.InitalStock
                                If XProjInv.Value(Row, i) < 0 Then
                                    XProjInv.Value(Row, i) = 0
                                End If
                            ElseIf i > HistPds Then
                                XProjInv.Value(Row, i) = XProjInv.Value(Row, i - 1) - Round(AdvForecast.AdjFcst(i, FcstUnits), 0) + Round(AdvForecast.AdjPlannedRcpts(i, FcstUnits), 0)
                                If XProjInv.Value(Row, i) < 0 Then
                                    XProjInv.Value(Row, i) = 0
                                End If
                            End If
                        Else
                            XProjInv.Value(Row, i) = 0 ' For dropship the projinv should always be 0
                        End If
                    End If
                    PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, FcstInterval)
                    If FcstInterval <> int_months Or FcstInterval <> int_Quarters Then
                       StartPd = AC.GetPeriod(PdEndDate)
                       EndPd = AC.GetPeriod(PdEndDate - 7)
                       If StartPd = 52 And EndPd = 52 Then PdEndDate = AC.GetEndDate(PdEndDate) + 1
                     End If
                PdStartDate = PdEndDate
            XItems.Value(Row, 14) = rs("LDivision").Value
            XItems.Value(Row, 15) = rs("LStatus").Value
            XItems.Value(Row, 16) = rs("LRegion").Value
            XItems.Value(Row, 17) = rs("LUserDefined").Value
            XItems.Value(Row, 18) = AdvForecast.Cube
            XItems.Value(Row, 19) = AdvForecast.Weight
            XItems.Value(Row, 20) = AdvForecast.Price
            XItems.Value(Row, 21) = AdvForecast.Cost
            

        Next i
            If DataYes = True Then
                XItems.Value(Row, 22) = "N"
            Else
                XItems.Value(Row, 22) = "N"
            End If
        AdjUnit = 0
    Next Row
     Set AdvForecast = Nothing
    If UProdConst = True Then
       RunProdConst NewTotalPeriods - 1
    End If
    PopulateMainArray Rows, xForeCast
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.UpdateForecast)"
     f_HandleErr , , , "AIMFcsPlanner::UpdateForecast", Now, gDRJobError, True, Err
End Function
Private Function RunProdConst(Nbrpds As Integer) As Integer
On Error GoTo ErrorHandler

    'Sri add code for prodconst start new function
    Dim i As Integer
    Dim J As Integer
    Dim Rtn As Integer

    Dim xProdConstData As New XArrayDB
    Dim UniqueId As String
    Dim Jobdate As Date
    Dim Qty As Long
    Dim XDelta As New XArrayDB
    
    Jobdate = Now()
    'Initilize the variables used in prodconst calc
    Initilize_ProdConst
    'Get unique id  and delete date for the unique id if any thing is left from old job
    UniqueId = GetUnique_Id
    XDelta.ReDim 0, XItems.UpperBound(1) - 1, 0, 0
    'For each Period data populate the Lcid Item and Period data to the table
    'Run ProdConstSizing Procedure Get data back and populate the changed data
    'back repeat the process for all the period data

    For J = 0 To Nbrpds
        If J = 0 Then
            For i = XItems.LowerBound(1) To XItems.UpperBound(1) - 1
                Populate_Temp _
                    UniqueId, _
                    XItems.Value(i, 0), _
                    XItems.Value(i, 1), _
                    XNetReq.Value(i, J)
            Next
        Else
            
            For i = XItems.LowerBound(1) To XItems.UpperBound(1) - 1
                    If XNetReq.Value(i, J - 1) > 0 Then
                        XDelta.Value(i, 0) = XNetReq.Value(i, J - 1) - xProdConst.Value(i, J - 1) + XDelta.Value(i, 0)
                    End If
                    'do not carry back log from historical periods into current and future periods
                    If FcstPdDates(J) = DpHistoricalStartDate Then
                        XDelta.Value(i, 0) = 0
                    End If
                    If XNetReq.Value(i, J) > 0 Then
                        Qty = XNetReq.Value(i, J) + XDelta.Value(i, 0)
                        If Qty < 0 Then Qty = 0
                    Else
                        Qty = 0
                    End If
                    
                   Populate_Temp _
                    UniqueId, _
                    XItems.Value(i, 0), _
                    XItems.Value(i, 1), _
                    Qty
            Next
        End If
        'Run actual ProdcutionConstrint logic
        'AIM_ProductionConstraintCtrl_Sp

        With AIM_ProductionConstraintCtrl_Sp
            .Parameters("@UniqueJobId").Value = UniqueId
            .Execute
        End With

        'Get back date from the table
        With AIM_ProdConstraintTemp_Get_Sp
            .Parameters("@UniqueJobId").Value = UniqueId
        End With
        rsGetProdConstData.Open AIM_ProdConstraintTemp_Get_Sp

        'Populate the recordset into an xarray
        xProdConstData.LoadRows (rsGetProdConstData.GetRows(rsGetProdConstData.RecordCount))
        rsGetProdConstData.Close

        'Delete the data we inserted into the table we are done
        cmdDelete.Execute

        'Populate the ProdConstrainedData to the approprate location
        For i = xProdConst.LowerBound(1) To xProdConst.UpperBound(1) - 1
            xProdConst.Value(i, J) = xProdConstData(i, 4)
        Next
    Next

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAFcstPlanner.RunProdConst)"
     f_HandleErr , , , "AIMFcsPlanner::RunProdConst", Now, gDRJobError, True, Err
End Function
Private Function Populate_Temp( _
    UniqueJobId As String, _
    LcIdKey As String, _
    ItemKey As String, _
    Qty As Long _
) As Integer
On Error GoTo ErrorHandler

    'Sri add code for prodconst start new function
     With AIM_ProdConstraintTemp_Save_Sp
        .Parameters("@UniqueJobId") = UniqueJobId
        .Parameters("@lcid") = LcIdKey
        .Parameters("@item") = ItemKey
        .Parameters("@Qty") = Qty
        .Execute
    End With
    Populate_Temp = AIM_ProdConstraintTemp_Save_Sp.Parameters(0).Value

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.Populate_Temp)"
     f_HandleErr , , , "AIMFcsPlanner::Populate_Temp", Now, gDRJobError, True, Err
End Function
'
Private Function GetUnique_Id() As String
On Error GoTo ErrorHandler

    'Sri add code for prodconst start new function
    'Get UniqueId so that we can identify the data we inserted into the table
    Dim UniqueId As String

    rsUniqueId.Open AIM_GuId_Get_SP
    UniqueId = rsUniqueId(0).Value

    'Make sure that the data is not there in the table initially just a precaution
    cmdDelete.ActiveConnection = Cn
    cmdDelete.CommandText = "Delete from ProdConstraintTemp  where  UniqueJobId = N'" & UniqueId & "'"
    cmdDelete.CommandType = adCmdText
    cmdDelete.Prepared = True
    cmdDelete.Execute

    GetUnique_Id = UniqueId

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.GetUnique_Id)"
     f_HandleErr , , , "AIMFcsPlanner::GetUnique_Id", Now, gDRJobError, True, Err
End Function
Private Function PopulateUserElementData(xMainArray As XArrayDB) As String
On Error GoTo ErrorHandler
Dim UserElementId As String
Dim ElementToSave As String
Dim i As Integer
Dim J As Integer
Dim K As Integer
Dim ElementPos As Integer
Dim UserElementsToProcess As Integer
Dim UserElement_Update As New ADODB.Command
Dim rsGetUserElements As New ADODB.Recordset
Dim SqlStmt As String
Dim FcstTypesCount As Integer
Dim UserElementCount As Integer
Dim FirstFcstType As String
FcstTypesCount = UBound(FcstTypes()) + 1
If UserElementsExists = True Then
    UserElementCount = UBound(UserElements()) + 1
Else
    UserElementCount = 0
End If

SqlStmt = "Select FcstId,UserElementId,FcstType From UserElement Where Processed_YN = 0 and FcstId ='" + U_FcstId + "'"
rsGetUserElements.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
If f_IsRecordsetOpenAndPopulated(rsGetUserElements) Then
    UserElementsToProcess = rsGetUserElements.RecordCount
    With UserElement_Update
        Set .ActiveConnection = Cn
    End With
 Else
    'No user elements to process so exit
    UserElementsToProcess = 0
    GoTo GracefulExit
End If
rsGetUserElements.MoveFirst
For K = 0 To UserElementsToProcess - 1
    ElementToSave = Trim(rsGetUserElements("FcstType").Value)
    UserElementId = Trim(rsGetUserElements("UserElementId").Value)
    For i = LBound(FcstTypes()) To UBound(FcstTypes())
        If FcstTypes(i) = UCase(ElementToSave) Then
            ElementPos = i
            Exit For
        End If
    Next i
    i = 0
    For i = ElementPos To xMainArray.UpperBound(1) Step FcstTypesCount + UserElementCount
        For J = LBound(FcstPdDates()) To UBound(FcstPdDates()) - 1
            If FcstPdDates(J) < DPRollingFcstStartDate Then
                'set the historical period data to 0 for the userelement
                Save_UserElement UserElementId, xMainArray.Value(i - ElementPos, 0), xMainArray.Value(i - ElementPos, 1), FcstPdDates(J), FcstPdDates(J + 1) - 1, 0
            Else
                Save_UserElement UserElementId, xMainArray.Value(i - ElementPos, 0), xMainArray.Value(i - ElementPos, 1), FcstPdDates(J), FcstPdDates(J + 1) - 1, xMainArray.Value(i, 4 + J)
            End If
        Next J
    Next i
    'Update the AimFcstSetUp table  with fcstlocked field set to true
    With UserElement_Update
        .CommandText = "Update UserElement Set Processed_YN =1 where FcstId ='" + U_FcstId + "'" + "and  UserElementId ='" + UserElementId + "'"
    End With
    UserElement_Update.Execute
    rsGetUserElements.MoveNext
Next K
GracefulExit:
If f_IsRecordsetValidAndOpen(rsGetUserElements) Then rsGetUserElements.Close
    Set rsGetUserElements = Nothing
If Not (UserElement_Update Is Nothing) Then Set UserElement_Update.ActiveConnection = Nothing
    Set UserElement_Update = Nothing
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.SaveUserElement)"
    f_HandleErr , , , "AIMFcsPlanner::SaveUserElement", Now, gDRJobError, True, Err
    GoTo GracefulExit
End Function

Private Function Save_UserElement( _
    p_UserElementId As String, _
    p_LcID As String, p_Item As String, _
    p_FcstPdBegDate As Date, p_FcstPdEndDate As Date, _
    p_Qty As Long _
) As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    'Init. the command object
    With AIM_UserElementDetail_Save_Sp
    'Set values
        .Parameters("@UserElementId").Value = p_UserElementId
        .Parameters("@LcID").Value = Trim$(p_LcID)
        .Parameters("@Item").Value = IIf(Trim$(p_Item) = "", Null, Trim$(p_Item))
        .Parameters("@FcstPdBegDate").Value = p_FcstPdBegDate
        .Parameters("@FcstPdEndDate").Value = p_FcstPdEndDate
        .Parameters("@Qty").Value = p_Qty
    'Run
        .Execute
    End With

    'Check return code -- 0 = fail; 1 = succeed
    RtnCode = AIM_UserElementDetail_Save_Sp.Parameters(0).Value

    'Set return value
    Save_UserElement = RtnCode

CleanUp:
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Save_UserElement)"
    f_HandleErr , , , "AIMFcsPlanner::Save_UserElement", Now, gDRJobError, True, Err
    
End Function




Private Function Initilize_ProdConst() As Integer
On Error GoTo ErrorHandler

    Initilize_ProdConst = -1
    'Sri add code for prodconst start new function
    With AIM_ProductionConstraintCtrl_Sp
        .CommandType = adCmdStoredProc
        Set .ActiveConnection = Cn
        .CommandText = "AIM_ProductionConstraintCtrl_Sp"
        .Parameters.Refresh
    End With

    With AIM_ProdConstraintTemp_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ProdConstraintTemp_Get_Sp"
        .Parameters.Refresh
    End With

    With rsGetProdConstData
        .CursorLocation = adUseClient
        .LockType = adLockReadOnly
        .CursorType = adOpenStatic
    End With

    With AIM_GuId_Get_SP
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_GuId_Get_SP"
        .Parameters.Refresh
    End With

    With AIM_ProdConstraintTemp_Save_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ProdConstraintTemp_Save_Sp"
        .Parameters.Refresh
    End With
    If f_IsRecordsetValidAndOpen(rsUniqueId) Then rsUniqueId.Close
    With rsUniqueId
        .CursorLocation = adUseClient
        .LockType = adLockReadOnly
        .CursorType = adOpenStatic
    End With

    Initilize_ProdConst = 1

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.Initilize_ProdConst)"
     f_HandleErr , , , "AIMFcsPlanner::Initilize_ProdConst", Now, gDRJobError, True, Err
End Function

Private Sub SetPeriodBounds()
On Error GoTo ErrorHandler

Dim i As Integer   ' column index
Dim IMax As Integer
Dim PdEndDate As Date
Dim PdStartDate As Date
Dim StartPd As Integer
Dim EndPd As Integer
If FcstRolling = False Then
    DPRollingFcstStartDate = GetRollingFcstStartDate(FcstStartDate, FcstStartDate, AC, FcstInterval)
    DpHistoricalStartDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, HistPds, AC, FcstInterval)
    DpFutureEndDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, -FuturePds, AC, FcstInterval) - 1
    PdStartDate = DpHistoricalStartDate
    PdEndDate = DpHistoricalStartDate
Else
    DPRollingFcstStartDate = GetRollingFcstStartDate(Now(), FcstStartDate, AC, FcstInterval)
    DpHistoricalStartDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, HistPds, AC, FcstInterval)
    DpFutureEndDate = GetHistoricalFcstStartDate(DPRollingFcstStartDate, -FuturePds, AC, FcstInterval) - 1
    PdStartDate = DpHistoricalStartDate
    PdEndDate = DpHistoricalStartDate
End If
ReDim FcstPdDates(1 To HistPds + FuturePds + 1)
For i = 1 To HistPds + FuturePds + 1
If i = 1 Then
         FcstPdDates(1) = DpHistoricalStartDate
     ElseIf i = 2 And FcstInterval <> U_FcstInterval And U_FcstInterval = int_months Then
        FcstPdDates(2) = GetNextRollingFcstStartDate(DpHistoricalStartDate, AC, U_FcstInterval)
        PdEndDate = FcstPdDates(2)
        PdStartDate = PdEndDate
    Else
          PdEndDate = GetNextStartDate(PdStartDate, PdEndDate, U_FcstInterval)
         If U_FcstInterval <> int_months Or U_FcstInterval <> int_Quarters Then
             StartPd = AC.GetPeriod(PdEndDate)
             EndPd = AC.GetPeriod(PdEndDate - 7)
             If StartPd = 52 And EndPd = 52 Then PdEndDate = AC.GetEndDate(PdEndDate) + 1
         End If
          If PdEndDate >= DpFutureEndDate Then
                FcstPdDates(i) = DpFutureEndDate + 1
                IMax = i
                Exit For
            Else
            FcstPdDates(i) = PdEndDate
            End If
        
         PdStartDate = PdEndDate
         IMax = i
    End If
Next
ReDim Preserve FcstPdDates(1 To IMax)
NewTotalPeriods = UBound(FcstPdDates())
Exit Sub
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(SetPeriodBounds)"
     f_HandleErr , , , "AIMFcsPlanner::SetPeriodBounds", Now, gDRJobError, True, Err
End Sub
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
'   Gets data from the AIMDmdPlanFcstSetup table for the modular given ID.
' .......................................................................
'
'   Parameters:
'       p_FcstID -- the forecast record to retrieve
'       p_Action -- depending on the enum SQL_ACTIONS, will execute appropriate SQL query.
'       p_Related -- if true, then the function will retrieve data from tables associated with the forecast id
'       r_rsCompound -- If p_Related was true, then there will be more than one recordset, else
'           there will be just the recordset for AIMDmdPlanFcstSetup.
'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Public Function gFetch_ForecastSetup( _
    p_Cn As ADODB.Connection, _
    p_FcstID As String, _
    p_UserID As String, _
    p_Action As SQL_ACTIONS, _
    p_Related As Boolean, _
    r_rsCompound As ADODB.Recordset _
) As Long
On Error GoTo ErrorHandler
'Called from fcstplanner and fcstsetup screen so make it independent
    Dim RtnCode As Long
    Dim AIM_ForecastSetup_Fetch_Sp As ADODB.Command
    Dim intCount As Long

    Dim DBugCounter As Long
    Dim DBugText As String

    'Init. the command object
    Set AIM_ForecastSetup_Fetch_Sp = New ADODB.Command
    With AIM_ForecastSetup_Fetch_Sp
        Set .ActiveConnection = p_Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ForecastSetup_Fetch_Sp"

    'Set parameters and their values
        .Parameters.Append AIM_ForecastSetup_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_ForecastSetup_Fetch_Sp.CreateParameter("@FcstID", adVarWChar, adParamInput, 12, p_FcstID)
        .Parameters.Append AIM_ForecastSetup_Fetch_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12, p_UserID)
        .Parameters.Append AIM_ForecastSetup_Fetch_Sp.CreateParameter("@Action", adTinyInt, adParamInput, , p_Action)
        .Parameters.Append AIM_ForecastSetup_Fetch_Sp.CreateParameter("@Related", adBoolean, adParamInput, , p_Related)
        .Parameters.Append AIM_ForecastSetup_Fetch_Sp.CreateParameter("@LangID", adVarWChar, adParamInput, 20, gLangID)
    End With

    'Init. the recordset
    If f_IsRecordsetValidAndOpen(r_rsCompound) Then r_rsCompound.Close
    Set r_rsCompound = Nothing
    Set r_rsCompound = New ADODB.Recordset
    With r_rsCompound
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    'Fetch in compound recordset (Since Related = 1, which means data will be returned from three tables)
    r_rsCompound.Open AIM_ForecastSetup_Fetch_Sp
    'Check return code -- 0 = fail; 1 = succeed
    RtnCode = AIM_ForecastSetup_Fetch_Sp.Parameters(0).Value

    'Set return value
    gFetch_ForecastSetup = RtnCode
CleanUp:
    If Not (AIM_ForecastSetup_Fetch_Sp Is Nothing) Then Set AIM_ForecastSetup_Fetch_Sp.ActiveConnection = Nothing
    Set AIM_ForecastSetup_Fetch_Sp = Nothing

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(gFetch_ForecastSetup)"
    f_HandleErr , , , "AIMFcsPlanner::gFetch_ForecastSetup", Now, gDRJobError, True, Err
    GoTo CleanUp
End Function
Public Function gCheckUserAccess( _
    p_Cn As ADODB.Connection, _
    p_FcstSetupKey As Long, _
    p_UserID As String, _
    p_AccessCode As FCST_ACCESS _
) As Boolean
On Error GoTo ErrorHandler
'Called from outside this module

    Dim AIM_DmdPlanFcstUser_CheckAccess_Sp As ADODB.Command
    Dim RtnCode As Long

    Set AIM_DmdPlanFcstUser_CheckAccess_Sp = New ADODB.Command
    With AIM_DmdPlanFcstUser_CheckAccess_Sp
        Set .ActiveConnection = p_Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DmdPlanFcstUser_CheckAccess_Sp"
    'Set parameters and their values
        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)
        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@AccessCode", adInteger, adParamInput)
    'Output
        .Parameters.Append AIM_DmdPlanFcstUser_CheckAccess_Sp.CreateParameter("@HasAccess", adBoolean, adParamOutput)

    'SEt values
        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
        .Parameters("@UserID").Value = p_UserID
        .Parameters("@AccessCode").Value = p_AccessCode
        .Parameters("@HasAccess").Value = Null

        .Execute
    End With

    RtnCode = AIM_DmdPlanFcstUser_CheckAccess_Sp(0).Value
    If RtnCode > 0 Then
        gCheckUserAccess = AIM_DmdPlanFcstUser_CheckAccess_Sp.Parameters("@HasAccess").Value
    Else
        gCheckUserAccess = False
    End If

    If Not (AIM_DmdPlanFcstUser_CheckAccess_Sp Is Nothing) Then Set AIM_DmdPlanFcstUser_CheckAccess_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstUser_CheckAccess_Sp = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_DmdPlanFcstUser_CheckAccess_Sp Is Nothing) Then Set AIM_DmdPlanFcstUser_CheckAccess_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstUser_CheckAccess_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(gCheckUserAccess  )"
    f_HandleErr , , , "AIMFcsPlanner::gCheckUserAccess  ", Now, gDRJobError, True, Err
End Function
Public Function gFetch_ForecastAccess( _
    p_Cn As ADODB.Connection, _
    p_FcstSetupKey As Long, _
    r_rsAccess As ADODB.Recordset, _
    Optional p_UserID As String _
) As Long
On Error GoTo ErrorHandler
'Called from outside of this module so make it independent
    Dim AIM_DmdPlanFcstAccess_Fetch_Sp As ADODB.Command
    Dim rsAccessControl As ADODB.Recordset
    Dim RtnCode As Long
    Dim ColCounter As Long
    
    If IsNull(p_FcstSetupKey) Or p_FcstSetupKey = -1 Then Exit Function

    'Clear out any existing data
    If f_IsRecordsetValidAndOpen(r_rsAccess) Then r_rsAccess.Close
    Set r_rsAccess = Nothing

    'Create command object
    Set AIM_DmdPlanFcstAccess_Fetch_Sp = New ADODB.Command
    With AIM_DmdPlanFcstAccess_Fetch_Sp
        .CommandType = adCmdStoredProc
        Set .ActiveConnection = p_Cn
        .CommandText = "AIM_DmdPlanFcstAccess_Fetch_Sp"
        'Set parameters and their values
        .Parameters.Append AIM_DmdPlanFcstAccess_Fetch_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append AIM_DmdPlanFcstAccess_Fetch_Sp.CreateParameter("@FcstSetupKey", adInteger, adParamInput)
        .Parameters.Append AIM_DmdPlanFcstAccess_Fetch_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12)

        .Parameters("@FcstSetupKey").Value = p_FcstSetupKey
        .Parameters("@UserID").Value = p_UserID
    End With

    Set rsAccessControl = New ADODB.Recordset
    With rsAccessControl
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    rsAccessControl.Open AIM_DmdPlanFcstAccess_Fetch_Sp

    RtnCode = AIM_DmdPlanFcstAccess_Fetch_Sp.Parameters(0).Value
    Select Case RtnCode
    Case Is > 0 'Success
        If Not f_IsRecordsetOpenAndPopulated(rsAccessControl) Then
            'Fail
            gFetch_ForecastAccess = -1
            GoTo CleanUp
        Else
            'Associate as a disconnected recordset
            Set r_rsAccess = New ADODB.Recordset
            r_rsAccess.Fields.Append "FcstSetupKey", adInteger
            r_rsAccess.Fields.Append "UserID", adVarWChar, 12
            r_rsAccess.Fields.Append "UserName", adVarWChar, 30
            r_rsAccess.Fields.Append "AccessCode", adTinyInt
            'Populate
            r_rsAccess.Open
            rsAccessControl.MoveFirst
            Do Until rsAccessControl.eof
                'Set display to match recordset(s)
                With r_rsAccess
                    .AddNew
                    For ColCounter = 0 To rsAccessControl.Fields.Count - 1
                        .Fields(ColCounter) = rsAccessControl.Fields(ColCounter)
                    Next
                End With
                rsAccessControl.MoveNext 'There should be no more for the first recordset
            Loop
            r_rsAccess.MoveFirst
        End If
        If rsAccessControl.RecordCount > 0 Then gFetch_ForecastAccess = 1   'Success
    End Select

CleanUp:
    If Not (AIM_DmdPlanFcstAccess_Fetch_Sp Is Nothing) Then Set AIM_DmdPlanFcstAccess_Fetch_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstAccess_Fetch_Sp = Nothing
    If f_IsRecordsetValidAndOpen(rsAccessControl) Then rsAccessControl.Close
    Set rsAccessControl = Nothing

Exit Function
ErrorHandler:
   'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.gFetch_ForecastAccess)"
   f_HandleErr , , , "AIMFcsPlanner::gFetch_ForecastAccess", Now, gDRJobError, True, Err
   GoTo CleanUp
End Function



Private Function PopulateMainArray(Rows As Long, xForeCast As XArrayDB) As Integer
On Error GoTo ErrorHandler
Dim i As Long
Dim J As Long
Dim K As Long
Dim L As Long
Dim M As Long
Dim FcstTypesCount As Integer
Dim UserElementCount As Integer
Dim FirstFcstType As String
FcstTypesCount = UBound(FcstTypes()) + 1
If UserElementsExists = True Then
    UserElementCount = UBound(UserElements()) + 1
Else
    UserElementCount = 0
End If
FirstFcstType = FcstTypes(0)

'xForeCast.ReDim 0, Rows * (FcstTypesCount + UserElementCount) - 1, 0, NewTotalPeriods - 1 + 18
xForeCast.ReDim 0, Rows * (FcstTypesCount + UserElementCount) - 1, 0, NewTotalPeriods - 1 + 22
L = 0
For i = 0 To (Rows * (FcstTypesCount + UserElementCount) - 1) Step FcstTypesCount + UserElementCount
    xForeCast(i, 0) = XItems.Value(L, 0) 'Lcid
    xForeCast(i, 1) = XItems.Value(L, 1) 'Item
    xForeCast(i, 2) = XItems.Value(L, 2) 'ItDesc
    xForeCast(i, NewTotalPeriods - 1 + 5) = XItems.Value(L, 3)  'Class1
    xForeCast(i, NewTotalPeriods - 1 + 6) = XItems.Value(L, 4) 'Class2
    xForeCast(i, NewTotalPeriods - 1 + 7) = XItems.Value(L, 5) 'Class3
    xForeCast(i, NewTotalPeriods - 1 + 8) = XItems.Value(L, 6) 'Class4
    xForeCast(i, NewTotalPeriods - 1 + 9) = XItems.Value(L, 7) 'ItStat
    xForeCast(i, NewTotalPeriods - 1 + 10) = XItems.Value(L, 8) 'VelCode
    xForeCast(i, NewTotalPeriods - 1 + 11) = XItems.Value(L, 9) 'VnId
    xForeCast(i, NewTotalPeriods - 1 + 12) = XItems.Value(L, 10) 'Assort
    xForeCast(i, NewTotalPeriods - 1 + 13) = XItems.Value(L, 11) 'ById
    xForeCast(i, 4) = 0 'Total
    '3 is the forecasttype
'    xForeCast(I, 14 + NewTotalPeriods - 1) = XItems.Value(L, 14) 'Cube
'    xForeCast(I, 15 + NewTotalPeriods - 1) = XItems.Value(L, 15)  'Weight
'    xForeCast(I, 16 + NewTotalPeriods - 1) = XItems.Value(L, 16)  'LastFYPrice
'    xForeCast(I, 17 + NewTotalPeriods - 1) = XItems.Value(L, 17) 'ThisFYPrice
'    xForeCast(I, 18 + NewTotalPeriods - 1) = XItems.Value(L, 18)  'New item or old item
    xForeCast(i, 14 + NewTotalPeriods - 1) = XItems.Value(L, 14) 'LDivision
    xForeCast(i, 15 + NewTotalPeriods - 1) = XItems.Value(L, 15)  'LStatus
    xForeCast(i, 16 + NewTotalPeriods - 1) = XItems.Value(L, 16)  'LRegion
    xForeCast(i, 17 + NewTotalPeriods - 1) = XItems.Value(L, 17) 'LUserDefined
    xForeCast(i, 18 + NewTotalPeriods - 1) = XItems.Value(L, 18) 'Cube
    xForeCast(i, 19 + NewTotalPeriods - 1) = XItems.Value(L, 19)  'Weight
    xForeCast(i, 20 + NewTotalPeriods - 1) = XItems.Value(L, 20)  'LastFYPrice
    xForeCast(i, 21 + NewTotalPeriods - 1) = XItems.Value(L, 21) 'ThisFYPrice
    xForeCast(i, 22 + NewTotalPeriods - 1) = XItems.Value(L, 22)  'New item or old item
    For J = 3 To NewTotalPeriods - 1 + 4
        
        For K = 0 To FcstTypesCount - 1
            If J = 3 Then
                xForeCast(i + K, J) = FcstTypes(K)
            ElseIf J = 4 Then
            'DO nothing
            Else
                If USysFcst = True And FcstTypes(K) = "SYSFCST" Then
                    xForeCast(i + K, J) = XSysFcst(L, J - 5)
                End If
                If UMasterFcstAdj = True And FcstTypes(K) = "MASTERFCSTADJ" Then
                    xForeCast(i + K, J) = XMasterFcstAdj(L, J - 5)
                End If
                If UFcstAdj = True And FcstTypes(K) = "FCSTADJ" Then
                    xForeCast(i + K, J) = XFcstAdj(L, J - 5)
                End If
                If UNetReq = True And FcstTypes(K) = "NETREQ" Then
                    xForeCast(i + K, J) = XNetReq(L, J - 5)
                End If
                If UAdjNetReq = True And FcstTypes(K) = "ADJNETREQ" Then
                    xForeCast(i + K, J) = XAdjNetReq(L, J - 5)
                End If
                If UProjInv = True And FcstTypes(K) = "PROJINV" Then
                    xForeCast(i + K, J) = XProjInv(L, J - 5)
                End If
                If UHistDmd = True And FcstTypes(K) = "HISDMD" Then
                    xForeCast(i + K, J) = XHistDmd(L, J - 5)
                End If
                If UProdConst = True And FcstTypes(K) = "PRODCONST" Then
                    xForeCast(i + K, J) = xProdConst(L, J - 5)
                End If
            End If
       Next K
       If UserElementsExists = True Then
            For M = 0 To UserElementCount - 1
                 If J = 3 Then
                     xForeCast(i + K + M, J) = UserElements(M)
                 ElseIf J = 4 Then
                 'DO nothing
                 Else
                 xForeCast(i + K + M, J) = xUserElement(M).Value(L, J - 5)
                 End If
            Next M
        End If
    Next J
    L = L + 1
Next i

'Clean up the xarrays
XItems.ReDim 0, 0, 0, 0
For i = 0 To UBound(FcstTypes())
    If FcstTypes(i) = "SYSFCST" Then
        XSysFcst.ReDim 0, 0, 0, 0
    ElseIf FcstTypes(i) = "MASTERFCSTADJ" Then
        XMasterFcstAdj.ReDim 0, 0, 0, 0
    ElseIf FcstTypes(i) = "FCSTADJ" Then
        XFcstAdj.ReDim 0, 0, 0, 0
    ElseIf FcstTypes(i) = "NETREQ" Then
        XNetReq.ReDim 0, 0, 0, 0
    ElseIf FcstTypes(i) = "ADJNETREQ" Then
        XAdjNetReq.ReDim 0, 0, 0, 0
    ElseIf FcstTypes(i) = "PROJINV" Then
        XProjInv.ReDim 0, 0, 0, 0
    ElseIf FcstTypes(i) = "HISTDMD" Then
        XHistDmd.ReDim 0, 0, 0, 0
     ElseIf FcstTypes(i) = "PRODCONST" Then
        xProdConst.ReDim 0, 0, 0, 0
        If UPopulateNetReq = True Then
            XNetReq.ReDim 0, 0, 0, 0
        End If
    End If
    
Next i
If UserElementsExists = True Then
    For M = 0 To UserElementCount - 1
        xUserElement(M).ReDim 0, 0, 0, 0
    Next M
End If
Exit Function





















'On Error GoTo ErrorHandler
'Dim I As Long
'Dim J As Long
'Dim K As Long
'Dim L As Long
'Dim FcstTypesCount As Integer
'Dim FirstFcstType As String
'FcstTypesCount = UBound(FcstTypes()) + 1
'FirstFcstType = FcstTypes(0)
'
'xForeCast.ReDim 0, Rows * FcstTypesCount - 1, 0, NewTotalPeriods - 1 + 18
'L = 0
'For I = 0 To (Rows * FcstTypesCount - 1) Step FcstTypesCount
'    xForeCast(I, 0) = XItems.Value(L, 0) 'Lcid
'    xForeCast(I, 1) = XItems.Value(L, 1) 'Item
'    xForeCast(I, 2) = XItems.Value(L, 2) 'ItDesc
'    xForeCast(I, NewTotalPeriods - 1 + 5) = XItems.Value(L, 3)  'Class1
'    xForeCast(I, NewTotalPeriods - 1 + 6) = XItems.Value(L, 4) 'Class2
'    xForeCast(I, NewTotalPeriods - 1 + 7) = XItems.Value(L, 5) 'Class3
'    xForeCast(I, NewTotalPeriods - 1 + 8) = XItems.Value(L, 6) 'Class4
'    xForeCast(I, NewTotalPeriods - 1 + 9) = XItems.Value(L, 7) 'ItStat
'    xForeCast(I, NewTotalPeriods - 1 + 10) = XItems.Value(L, 8) 'VelCode
'    xForeCast(I, NewTotalPeriods - 1 + 11) = XItems.Value(L, 9) 'VnId
'    xForeCast(I, NewTotalPeriods - 1 + 12) = XItems.Value(L, 10) 'Assort
'    xForeCast(I, NewTotalPeriods - 1 + 13) = XItems.Value(L, 11) 'ById
'    xForeCast(I, 4) = 0 'Total
'    '3 is the forecasttype
'    xForeCast(I, 14 + NewTotalPeriods - 1) = XItems.Value(L, 14) 'Cube
'    xForeCast(I, 15 + NewTotalPeriods - 1) = XItems.Value(L, 15)  'Weight
'    xForeCast(I, 16 + NewTotalPeriods - 1) = XItems.Value(L, 16)  'LastFYPrice
'    xForeCast(I, 17 + NewTotalPeriods - 1) = XItems.Value(L, 17) 'ThisFYPrice
'    xForeCast(I, 18 + NewTotalPeriods - 1) = XItems.Value(L, 18)  'New item or old item
'    For J = 3 To NewTotalPeriods - 1 + 4
'
'        For K = 0 To FcstTypesCount - 1
'            If J = 3 Then
'                xForeCast(I + K, J) = FcstTypes(K)
'            ElseIf J = 4 Then
'            'DO nothing
'            Else
'                If USysFcst = True And FcstTypes(K) = "SYSFCST" Then
'                    xForeCast(I + K, J) = XSysFcst(L, J - 5)
'                End If
'                If UMasterFcstAdj = True And FcstTypes(K) = "MASTERFCSTADJ" Then
'                    xForeCast(I + K, J) = XMasterFcstAdj(L, J - 5)
'                End If
'                If UFcstAdj = True And FcstTypes(K) = "FCSTADJ" Then
'                    xForeCast(I + K, J) = XFcstAdj(L, J - 5)
'                End If
'                If UNetReq = True And FcstTypes(K) = "NETREQ" Then
'                    xForeCast(I + K, J) = XNetReq(L, J - 5)
'                End If
'                If UAdjNetReq = True And FcstTypes(K) = "ADJNETREQ" Then
'                    xForeCast(I + K, J) = XAdjNetReq(L, J - 5)
'                End If
'                If UProjInv = True And FcstTypes(K) = "PROJINV" Then
'                    xForeCast(I + K, J) = XProjInv(L, J - 5)
'                End If
'                If UHistDmd = True And FcstTypes(K) = "HISDMD" Then
'                    xForeCast(I + K, J) = XHistDmd(L, J - 5)
'                End If
'                If UProdConst = True And FcstTypes(K) = "PRODCONST" Then
'                    xForeCast(I + K, J) = xProdConst(L, J - 5)
'                End If
'            End If
'       Next K
'    Next J
'    L = L + 1
'Next I
'
''Clean up the xarrays
'XItems.ReDim 0, 0, 0, 0
'For I = 0 To UBound(FcstTypes())
'    If FcstTypes(I) = "SYSFCST" Then
'        XSysFcst.ReDim 0, 0, 0, 0
'    ElseIf FcstTypes(I) = "MASTERFCSTADJ" Then
'        XMasterFcstAdj.ReDim 0, 0, 0, 0
'    ElseIf FcstTypes(I) = "FCSTADJ" Then
'        XFcstAdj.ReDim 0, 0, 0, 0
'    ElseIf FcstTypes(I) = "NETREQ" Then
'        XNetReq.ReDim 0, 0, 0, 0
'    ElseIf FcstTypes(I) = "ADJNETREQ" Then
'        XAdjNetReq.ReDim 0, 0, 0, 0
'    ElseIf FcstTypes(I) = "PROJINV" Then
'        XProjInv.ReDim 0, 0, 0, 0
'    ElseIf FcstTypes(I) = "HISTDMD" Then
'        XHistDmd.ReDim 0, 0, 0, 0
'     ElseIf FcstTypes(I) = "PRODCONST" Then
'        xProdConst.ReDim 0, 0, 0, 0
'    End If
'
'Next I
'Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.PopulateMainArray)"
     f_HandleErr , , , "AIMFcsPlanner::PopulateMainArray", Now, gDRJobError, True, Err
End Function

Private Function RefreshAdjLogData() As Boolean
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim AIM_Repository_RefreshAdj_Sp As ADODB.Command
    'Called only one time so it is ok to difine the command object here
    Set AIM_Repository_RefreshAdj_Sp = New ADODB.Command
    
    With AIM_Repository_RefreshAdj_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_Repository_RefreshAdj_Sp"
        .CommandType = adCmdStoredProc
        '.Parameters.Refresh
    End With

    Set prm = AIM_Repository_RefreshAdj_Sp.CreateParameter("@Return", adInteger, adParamReturnValue, , 0)
    AIM_Repository_RefreshAdj_Sp.Parameters.Append prm
    Set prm = AIM_Repository_RefreshAdj_Sp.CreateParameter("@RepositoryKey", adInteger, adParamInput, 0, U_FcstKey)
    AIM_Repository_RefreshAdj_Sp.Parameters.Append prm

    AIM_Repository_RefreshAdj_Sp.Execute
    RtnCode = AIM_Repository_RefreshAdj_Sp("@Return")
    If RtnCode < 0 Then
        RefreshAdjLogData = False
    Else
        RefreshAdjLogData = True
    End If
CleanUp:
    If Not (AIM_Repository_RefreshAdj_Sp Is Nothing) Then Set AIM_Repository_RefreshAdj_Sp.ActiveConnection = Nothing
    Set AIM_Repository_RefreshAdj_Sp = Nothing
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.RefreshAdjLogData)"
     f_HandleErr , , , "AIMFcsPlanner::RefreshAdjLogData", Now, gDRJobError, True, Err
    GoTo CleanUp
End Function


Public Function AIMAdvCalendar_Load( _
    Cn As ADODB.Connection, _
    p_AC As AIMAdvCalendar _
) As Integer
On Error GoTo ErrorHandler
'>>> Check against AIM_DBIO.AIMCalendar_Load for possible duplication
    Dim AIM_AIMDays_Load_Sp As ADODB.Command
    Dim AIM_AIMYears_Load_Sp As ADODB.Command

    Dim rsTemp As ADODB.Recordset

    Dim Row As Long

    'Define the Stored Procedures
    Set AIM_AIMDays_Load_Sp = New ADODB.Command
    With AIM_AIMDays_Load_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMDays_Load_Sp"
        .Parameters(0).Direction = adParamReturnValue
    End With

    Set AIM_AIMYears_Load_Sp = New ADODB.Command
    With AIM_AIMYears_Load_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMYears_Load_Sp"
        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
            "RETURN", adInteger, adParamReturnValue, , 0)
        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
            "@FiscalYear", adInteger, adParamInput, , 0)
    End With

    'Load the AIM Calendar/AIM Years
    Set rsTemp = New ADODB.Recordset
    With rsTemp
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    'Load the year collection
    rsTemp.Open AIM_AIMYears_Load_Sp
    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
        Do Until rsTemp.eof
            Row = p_AC.AddYear(rsTemp("FiscalYear").Value, rsTemp("FYStartDate").Value, rsTemp("FYEndDate").Value)
            rsTemp.MoveNext
        Loop
    End If

    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    Set rsTemp = New ADODB.Recordset
    With rsTemp
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    'Load the days collection
    rsTemp.Open AIM_AIMDays_Load_Sp

    Row = 0
    If f_IsRecordsetOpenAndPopulated(rsTemp) Then
        Do Until rsTemp.eof
            Row = p_AC.SetDayStatus(rsTemp("FYDate").Value, rsTemp("DayStatus").Value)
            rsTemp.MoveNext
        Loop
    End If
    AIMAdvCalendar_Load = rsTemp.RecordCount

    'Clean Up
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing
    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Load_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsTemp) Then rsTemp.Close
    Set rsTemp = Nothing
    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing
    If Not (AIM_AIMDays_Load_Sp Is Nothing) Then Set AIM_AIMDays_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMDays_Load_Sp = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMForecast.AIMAdvCalendar_Load)"
    f_HandleErr , , , "AIMFcsPlanner::AIMAdvCalendar_Load", Now, gDRJobError, True, Err
End Function

Private Function PopulateSales(FcstStartDate As Date, Item As String, LcId As String, Nbrpds As Integer, FcstInterval As AIM_INTERVALS, Row As Long) As Integer
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim Sales As Double
    Dim Order As Double
    Dim Pd As Long
    Dim SalesYes  As Boolean 'Flag to see if there is sale and order history data
    
     SalesYes = LoadSalesData(LcId, Item, FcstPdDates(1), FcstPdDates(NewTotalPeriods))
    strMessage = getTranslationResource("STATMSG90800")
    If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
    Write_Message strMessage & " " & Trim$(LcId) & _
        getTranslationResource("/") & Item
        
    For Pd = 1 To NewTotalPeriods - 1
        'Calculate sales,order,SalePrice,OrderPrice
        If SalesYes = True Then
            SaleBetweenDates FcstPdDates(Pd), FcstPdDates(Pd + 1) - 1, Sales, LcId, Item
        Else
            Sales = 0
        End If
        'Sales
        XHistDmd.Value(Row, Pd - 1) = Sales
    Next Pd
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.PopulateSales)"
     f_HandleErr , , , "AIMFcsPlanner::PopulateSales", Now, gDRJobError, True, Err
End Function
Private Function PopulateUserElement(UserElementId As String, UserElementPos As Integer, Item As String, LcId As String, Row As Long) As Integer
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim UserElementYes  As Boolean 'Flag to see if there is Userelement data
    Dim UserElementQty As Double
    Dim Pd As Integer
    
    UserElementYes = LoadUserElementData(UserElementId, LcId, Item)
    strMessage = getTranslationResource("STATMSG90800")
    If StrComp(strMessage, "STATMSG90800") = 0 Then strMessage = "Processing..."
    Write_Message strMessage & " " & Trim$(LcId) & _
        getTranslationResource("/") & Item
        
    For Pd = 1 To NewTotalPeriods - 1
        'Calculate sales,order,SalePrice,OrderPrice
        If UserElementYes = True Then
            UserElementQtyBetweenDates FcstPdDates(Pd), FcstPdDates(Pd + 1) - 1, UserElementQty, LcId, Item
        Else
            UserElementQty = 0
        End If
        'UserElementQty
        xUserElement(UserElementPos).Value(Row, Pd - 1) = UserElementQty
    Next Pd
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.PopulateUserElement)"
     f_HandleErr , , , "AIMFcsPlanner::PopulateUserElement", Now, gDRJobError, True, Err
End Function

Private Function GetBatchUserElementQtyForAPd(ArgSearchStartPos As Long, ArgItem As String, ArgLcid As String, ArgPdStartDate As Date, ArgQty As Double) As Integer
On Error GoTo ErrorHandler
    Dim i As Long
    Dim StartPos As Long
    StartPos = ArgSearchStartPos
    For i = StartPos To UBound(UserElementBatchRcd)
        If UserElementBatchRcd(i).LcId = ArgLcid And _
            UserElementBatchRcd(i).Item = ArgItem And _
            UserElementBatchRcd(i).StartDate = ArgPdStartDate Then
            ArgQty = UserElementBatchRcd(i).Qty
            ArgSearchStartPos = i
            Exit For
        End If
    Next i
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.GetBatchUserElementForAPd)"
     f_HandleErr , , , "AIMFcsPlanner::GetBatchUserElementForAPd", Now, gDRJobError, True, Err
End Function
Private Function UserElementQtyBetweenDates( _
    StartDate As Date, _
    EndDate As Date, _
    Qty As Double, _
    LcId As String, _
    ItemId As String _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim Counter As Integer
    Dim EndWeekDays As Integer
    Dim EndWeekPct As Double
    Dim FirstStartDate As Date
    Dim LastStartDays As Integer
    Dim StartWeekDays As Integer
    Dim StartWeekPct As Double
    Dim LastStartDate As Date
    Dim DailyPct As Double

    Qty = 0
    'Calculate the First Date of the First Period
    FirstStartDate = AC.GetStartDateFmDate(StartDate)
    LastStartDate = AC.GetStartDateFmDate(EndDate)
    If FirstStartDate = 0 _
    Or LastStartDate = 0 _
    Then
        Exit Function
    End If

    'Calculate the number of working days in the first period
    StartWeekDays = AC.GetWorkingDaysInWeek(StartDate)
    'Calculate the number of working days in the last period
    EndWeekDays = AC.GetWorkingDaysInWeek(EndDate)
    'If the Startdate and Enddate are the same then it is forecast interval is days
    If StartDate = EndDate Then
        DailyPct = AC.GetWorkingDays(StartDate, EndDate) / EndWeekDays
        StartWeekPct = 0
        EndWeekPct = 0
    Else
        'Calculate the Start Week Percentages
        If StartWeekDays > 0 Then
            StartWeekPct = AC.GetWorkingDays(StartDate, FirstStartDate + 6) / StartWeekDays
        Else
            StartWeekPct = 0
        End If
        'Calculate the End Week Percentages
        If EndWeekDays > 0 Then
            EndWeekPct = AC.GetWorkingDays(LastStartDate, EndDate) / EndWeekDays
        Else
            EndWeekPct = 0
        End If
    End If
    If StartDate = EndDate Then
        For Counter = 1 To UBound(UserElementRcd())
            If UserElementRcd(Counter).StartDate > StartDate - 7 _
            And UserElementRcd(Counter).EndDate < EndDate + 7 _
            Then
               Qty = Qty + UserElementRcd(Counter).Qty
            End If
        Next Counter
    Else
        For Counter = 1 To UBound(UserElementRcd())
        If UserElementRcd(Counter).StartDate > StartDate - 7 _
            And UserElementRcd(Counter).EndDate < EndDate + 7 _
            Then
                If UserElementRcd(Counter).StartDate <= StartDate Then
                    Qty = Qty + UserElementRcd(Counter).Qty * StartWeekPct
                ElseIf UserElementRcd(Counter).StartDate + 7 > EndDate Then
                    Qty = Qty + UserElementRcd(Counter).Qty * EndWeekPct
                Else
                    Qty = Qty + UserElementRcd(Counter).Qty
                End If
            End If
        Next Counter
    End If
    If StartDate = EndDate Then
        Qty = Qty * DailyPct
    End If
  
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.UserElementQtyBetweenDates)"
     f_HandleErr , , , "AIMFcsPlanner::UserElementQtyBetweenDates", Now, gDRJobError, True, Err
End Function
Private Function SaleBetweenDates( _
    StartDate As Date, _
    EndDate As Date, _
    Sale As Double, _
    LcId As String, _
    ItemId As String _
) As Integer
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim Counter As Integer
    Dim EndWeekDays As Integer
    Dim EndWeekPct As Double
    Dim FirstStartDate As Date
    Dim LastStartDays As Integer
    Dim StartWeekDays As Integer
    Dim StartWeekPct As Double
    Dim LastStartDate As Date
    Dim DailyPct As Double

    Sale = 0
    'Calculate the First Date of the First Period
    FirstStartDate = AC.GetStartDateFmDate(StartDate)
    LastStartDate = AC.GetStartDateFmDate(EndDate)
    If FirstStartDate = 0 _
    Or LastStartDate = 0 _
    Then
        Exit Function
    End If

    'Calculate the number of working days in the first period
    StartWeekDays = AC.GetWorkingDaysInWeek(StartDate)
    'Calculate the number of working days in the last period
    EndWeekDays = AC.GetWorkingDaysInWeek(EndDate)
    'If the Startdate and Enddate are the same then it is forecast interval is days
    If StartDate = EndDate Then
        DailyPct = AC.GetWorkingDays(StartDate, EndDate) / EndWeekDays
        StartWeekPct = 0
        EndWeekPct = 0
    Else
        'Calculate the Start Week Percentages
        If StartWeekDays > 0 Then
            StartWeekPct = AC.GetWorkingDays(StartDate, FirstStartDate + 6) / StartWeekDays
        Else
            StartWeekPct = 0
        End If
        'Calculate the End Week Percentages
        If EndWeekDays > 0 Then
            EndWeekPct = AC.GetWorkingDays(LastStartDate, EndDate) / EndWeekDays
        Else
            EndWeekPct = 0
        End If
    End If
    If StartDate = EndDate Then
        For Counter = 1 To UBound(ActualSales())
            If ActualSales(Counter).StartDate > StartDate - 7 _
            And ActualSales(Counter).EndDate < EndDate + 7 _
            Then
                Sale = Sale + ActualSales(Counter).Sale
            End If
        Next Counter
    Else
        For Counter = 1 To UBound(ActualSales())
        If ActualSales(Counter).StartDate > StartDate - 7 _
            And ActualSales(Counter).EndDate < EndDate + 7 _
            Then
                If ActualSales(Counter).StartDate <= StartDate Then
                    Sale = Sale + ActualSales(Counter).Sale * StartWeekPct
                ElseIf ActualSales(Counter).StartDate + 7 > EndDate Then
                    Sale = Sale + ActualSales(Counter).Sale * EndWeekPct
                Else
                    Sale = Sale + ActualSales(Counter).Sale
                End If
            End If
        Next Counter
    End If
    If StartDate = EndDate Then
        Sale = Sale * DailyPct
    End If
  
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.SaleBetweenDates)"
     f_HandleErr , , , "AIMFcsPlanner::SaleBetweenDates", Now, gDRJobError, True, Err
End Function




Private Function GetNumPdsToFcst( _
    OrgInterval As AIM_INTERVALS, _
    OrgStartDate As Date, _
    Nbrpds As Integer _
) As Integer
On Error GoTo ErrorHandler

    Dim StartDate As Date
    Dim CurStartdate As Date
    Dim EndDate As Date
    Dim NbrPdsToFcst As Integer
    Dim Counter As Long
    
    StartDate = OrgStartDate
    CurStartdate = StartDate
    
    For Counter = 1 To Nbrpds
        CurStartdate = GetNextStartDate(StartDate, CurStartdate, OrgInterval)
    Next Counter
    
    'Date till we need to forecast
    EndDate = CurStartdate
    'Loop to cal number of Pds to cal with new interval
    CurStartdate = FcstStartDate
    For Counter = 1 To Nbrpds
        CurStartdate = GetNextStartDate(FcstStartDate, CurStartdate, FcstInterval)
        If CurStartdate > EndDate Then Exit For
        NbrPdsToFcst = NbrPdsToFcst + 1
    Next
    
    GetNumPdsToFcst = NbrPdsToFcst

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.GetNumPdsToFcst)"
     f_HandleErr , , , "AIMFcsPlanner::GetNumPdsToFcst", Now, gDRJobError, True, Err
End Function

Private Function LoadSalesData( _
    LcId As String, _
    ItemId As String, _
    StartDate As Date, _
    EndDate As Date _
) As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim FyStartYear As Integer
    Dim FyEndYear As Integer
    Dim DemandSource As String
    Dim Pd As Integer
    Dim Counter As Integer
    Dim TotalRec As Integer
    Dim Sale As Double
    Dim Order As Double
    FyStartYear = AC.GetFiscalYear(StartDate)
    FyEndYear = AC.GetFiscalYear(EndDate)
    
    RtnCode = ItemHistory_GetEq( _
        AIM_ItemHistory_GetEq_Sp, _
        rsItemHistory, _
        LcId, _
        ItemId, _
        FyStartYear, _
        FyEndYear, _
        "S")
'>>> check status here
    TotalRec = rsItemHistory.RecordCount * 52
    If TotalRec = 0 Then
        LoadSalesData = False
        
        Exit Function
    End If

    ReDim Preserve ActualSales(1 To TotalRec)
    If RtnCode <> FAIL _
    And f_IsRecordsetOpenAndPopulated(rsItemHistory) _
    Then
        'Load the Demand History
        Counter = 1
        Do Until rsItemHistory.eof
            For Pd = 1 To 52 Step 1
                'Get the Demand History Period Number
                ActualSales(Counter).Fy = rsItemHistory("HisYear").Value
                ActualSales(Counter).Sale = rsItemHistory("dmd" + Format(Pd, "00")).Value
                ActualSales(Counter).Pd = Pd
                ActualSales(Counter).StartDate = AC.GetStartDateFmPeriod(Pd, ActualSales(Counter).Fy)
                'ActualSales(Counter).StartDate = GetStartDateFmPeriod(Pd, ActualSales(Pd).Fy, AIMYears())
                ActualSales(Counter).EndDate = AC.GetEndDateFmPeriod(Pd, ActualSales(Counter).Fy)
                'ActualSales(Counter).Enddate = GetEndDateFmPeriod(Pd, ActualSales(Pd).Fy, AIMYears())
                Counter = Counter + 1
            Next Pd
            rsItemHistory.MoveNext
        Loop
        If f_IsRecordsetValidAndOpen(rsItemHistory) Then rsItemHistory.Close
    End If

    LoadSalesData = True
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.LoadSalesData)"
     f_HandleErr , , , "AIMFcsPlanner::LoadSalesData", Now, gDRJobError, True, Err
End Function

Private Function LoadBatchUserElementData( _
    UserElementId As String _
 ) As Boolean
On Error GoTo ErrorHandler
    Dim RtnCode As Integer
    Dim Counter As Long
    Dim TotalRec As Long
    
    'Get the Location Data
    With DPAIM_DmdPlan_BatchUserElement_Get_Sp
        .Parameters(0).Value = 0
        .Parameters("@UserElementId").Value = UserElementId
    End With
    
    If f_IsRecordsetValidAndOpen(rsUserElement) Then rsUserElement.Close
    rsUserElement.Open DPAIM_DmdPlan_BatchUserElement_Get_Sp
    
    TotalRec = rsUserElement.RecordCount
    If TotalRec = 0 Then
        ReDim Preserve UserElementBatchRcd(1 To 1)
        UserElementBatchRcd(1).LcId = ""
        UserElementBatchRcd(1).Item = ""
        UserElementBatchRcd(1).StartDate = "01-01-1900"
        UserElementBatchRcd(1).EndDate = "01-01-1900"
        UserElementBatchRcd(1).Qty = 0
        LoadBatchUserElementData = False
        GoTo CleanUp
        Exit Function
    End If
     rsUserElement.MoveFirst
    ReDim Preserve UserElementBatchRcd(1 To TotalRec)
    
        'Load the Demand History
        Counter = 1
        Do Until rsUserElement.eof
                UserElementBatchRcd(Counter).LcId = rsUserElement("Lcid").Value
                UserElementBatchRcd(Counter).Item = rsUserElement("Item").Value
                UserElementBatchRcd(Counter).StartDate = rsUserElement("FcstPdBegDate").Value
                UserElementBatchRcd(Counter).EndDate = rsUserElement("FcstPdEndDate").Value
                UserElementBatchRcd(Counter).Qty = rsUserElement("Qty").Value
            rsUserElement.MoveNext
            Counter = Counter + 1
        Loop
    LoadBatchUserElementData = True
CleanUp:
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.LoadBatchUserElementData)"
     f_HandleErr , , , "AIMFcsPlanner::LoadBatchUserElementData", Now, gDRJobError, True, Err
End Function
Private Function LoadUserElementData( _
    UserElementId As String, _
    LcId As String, _
    ItemId As String _
 ) As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim Counter As Integer
    Dim TotalRec As Integer
   
    With AIM_UserElementDetail_Get_Sp
        .Parameters(0).Value = 0
        .Parameters("@UserElementId").Value = UserElementId
        .Parameters("@LcId").Value = LcId
        .Parameters("@Item").Value = ItemId
    End With
    If f_IsRecordsetValidAndOpen(rsUserElement) Then rsUserElement.Close
    rsUserElement.Open AIM_UserElementDetail_Get_Sp
    TotalRec = rsUserElement.RecordCount
    If TotalRec = 0 Then
        LoadUserElementData = False
        GoTo CleanUp
        Exit Function
    End If
     rsUserElement.MoveFirst
    ReDim Preserve UserElementRcd(1 To TotalRec)
    
        'Load the Demand History
        Counter = 1
        Do Until rsUserElement.eof
                UserElementRcd(Counter).StartDate = rsUserElement("FcstPdBegDate").Value
                UserElementRcd(Counter).EndDate = rsUserElement("FcstPdEndDate").Value
                UserElementRcd(Counter).Qty = rsUserElement("Qty").Value
            rsUserElement.MoveNext
            Counter = Counter + 1
        Loop
    LoadUserElementData = True
CleanUp:
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.LoadUserElementData)"
     f_HandleErr , , , "AIMFcsPlanner::LoadUserElementData", Now, gDRJobError, True, Err
End Function




Private Function GetForecastInterval(arg As AIM_INTERVALS) As Integer
On Error GoTo ErrorHandler

    If arg = 0 Then
        GetForecastInterval = int_Days
    End If
    If arg = 1 Then
        GetForecastInterval = int_Weeks
    End If
    If arg = 2 Then
        GetForecastInterval = int_months
    End If
    If arg = 3 Then
        GetForecastInterval = int_Quarters
    End If
    If arg = 4 Then
        GetForecastInterval = int_544
    End If
    If arg = 5 Then
        GetForecastInterval = int_454
    End If
    If arg = 6 Then
        GetForecastInterval = int_445
    End If
    If arg = 7 Then
        GetForecastInterval = int_4Wks
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.GetForecastInterval)"
     f_HandleErr , , , "AIMFcsPlanner::GetForecastInterval", Now, gDRJobError, True, Err
End Function

Public Function GenerateBatchFcst( _
    ArgFcstId As String, _
    ArgFcstTypes() As String, _
    xBatchFcst As XArrayDB, _
    p_FcstSetupKey As Long, _
    ArgUserElements() As String _
) As Integer
On Error GoTo ErrorHandler


Dim RtnCode As Integer
Dim SqlStmt As String
Dim FcstDates() As Date
Dim BatchArray As New XArrayDB
Dim TempArray As New XArrayDB
Dim ItemCount As Long
Dim GetSysFcst As Boolean
Dim GetMasterAdjSysFcst As Boolean
Dim GetAdjSysFcst As Boolean
Dim GetNetReq As Boolean
Dim GetAdjNetReq As Boolean
Dim GetHisDmd As Boolean
Dim GetQtyActualShipped As Boolean
Dim GetProjInv As Boolean
Dim GetProdConst As Boolean
Dim FcstCounter As Integer
Dim Total As Double
Dim i As Long   'Track all the rows returned by the query
Dim J As Long   'Used to track the position of the date data field
Dim K As Long   'Used to track the posiotion in the FcstDates array
Dim L As Integer   'Used for differnt data types like Fcst AdjFcst etc
Dim M As Long
Dim N As Long
Dim FcstTypes As Integer 'Used for differnt data types like Fcst AdjFcst etc
Dim UserElementCount As Integer ' User for differnt userelements
Dim Kmax As Long 'Max value of k variable
Dim OldLcid As String
Dim OldItem As String
Dim NewLcID As String
Dim NewItem As String
Dim StartPos As Long
Dim Qty As Double
Dim TempArrayRow    'Row position in the main array
Dim UserElementDataExists As Boolean ' If true then userelement data exists
For FcstCounter = 0 To UBound(ArgFcstTypes())
    If UCase(ArgFcstTypes(FcstCounter)) = UCase("SYSFCST") Then
    GetSysFcst = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("MASTERFCSTADJ") Then
    GetMasterAdjSysFcst = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("FCSTADJ") Then
        GetAdjSysFcst = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("NETREQ") Then
        GetNetReq = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("ADJNETREQ") Then
        GetAdjNetReq = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("HISDMD") Then
        GetHisDmd = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("QTYACTULASHIPPED") Then
        GetQtyActualShipped = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("PROJINV") Then
        GetProjInv = True
    ElseIf UCase(ArgFcstTypes(FcstCounter)) = UCase("PRODCONST") Then
        GetProdConst = True
    End If
Next

U_FcstId = ArgFcstId
'Connect to the database
'If CnConnected = False Then
 If Cn.State <> adStateOpen Then
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, , , "AIMForecast.bas")
    If RtnCode <> SUCCEED Then
        GenerateBatchFcst = RtnCode
        Exit Function
    End If
    CnConnected = True
End If
Initialize_Objects
If AIMCalenderLoaded = False Then
    AIMAdvCalendar_Load Cn, AC
    AIMCalenderLoaded = True
End If

'Set the SQL Statement
SqlStmt = SqlStmt + "Select VnId,Assort,LcId,Item,Class1,Class2,Class3,Class4,ById,ItStat, " & _
        " LDivision, LStatus, LRegion, LUserDefined, "
SqlStmt = SqlStmt + "FcstStartDate,FcstInterval,FcstRolling,ApplyTrend,FcstUnit,"
SqlStmt = SqlStmt + " FcstPds_Future,FcstPds_Historical From AimFcstSetUp where FcstId = N'" & U_FcstId & "'"
 'Retrieve the data
If f_IsRecordsetValidAndOpen(rsFcstMaint) Then rsFcstMaint.Close
rsFcstMaint.Open SqlStmt, Cn, adOpenStatic, adLockReadOnly, adCmdText
FcstRolling = rsFcstMaint("FcstRolling").Value
'Populate the local variables from the rsFcstMaint record set
 'FcstStartDate = rsFcstMaint("FcstStartDate").Value
 FcstInterval = GetForecastInterval(rsFcstMaint("FcstInterval").Value)
 ApplyTrend = rsFcstMaint("ApplyTrend").Value
 FcstUnit = GetForecastUnit(rsFcstMaint("FcstUnit").Value)
 FuturePds = rsFcstMaint("FcstPds_Future").Value
 HistPds = rsFcstMaint("FcstPds_Historical").Value
 Nbrpds = FuturePds + HistPds
 If FcstRolling = False Then
    'FcstStartDate = rsFcstMaint("FcstStartDate").Value
    FcstStartDate = GetRollingFcstStartDate(rsFcstMaint("FcstStartDate").Value, rsFcstMaint("FcstStartDate").Value, AC, FcstInterval)
 
 Else
    'FcstStartDate = Now
    FcstStartDate = GetRollingFcstStartDate(Now(), rsFcstMaint("FcstStartDate").Value, AC, FcstInterval)
  End If

 CurrentStartDate = GetRollingFcstStartDate(FcstStartDate, FcstStartDate, AC, FcstInterval)
 HistoricalStartDate = GetHistoricalFcstStartDate(CurrentStartDate, HistPds, AC, FcstInterval)
 AdjStartDate = GetHistoricalFcstStartDate(CurrentStartDate, HistPds + 1, AC, FcstInterval)
 AdjEndDate = GetHistoricalFcstStartDate(CurrentStartDate, -(FuturePds + 1), AC, FcstInterval) - 1

 'Populate the FcstDates array with the dates
 Populate_Dates FcstInterval, HistPds + FuturePds, HistoricalStartDate, FcstDates()

 'The following function gets batchdata the return value gives the
 'number of lcid and item records returned
 
 ItemCount = g_GetBatchData(ArgFcstId, p_FcstSetupKey, HistoricalStartDate)
 If ItemCount > 0 Then
    rsBatchData.MoveFirst
    'Populate the BatchArray with the BatchFcst data
    BatchArray.LoadRows rsBatchData.GetRows()
Else
GenerateBatchFcst = -1
GoTo CleanUp
 End If
 'BatchArrray will have all the elementdata
 

 'SysFcst position is   21 17
 'MasterFcstAdj position is 22 18
 'AdjSysFcst position is 23 19
 'NetReq position is 24 20
 'AdjNetReq position is 25 21
 'QtyProjectedInventor position is 26 22
 'QtyActualOrdered position is 27 23
 'QtyActualShipped position is 28 24
 'QtyProjInv is 29 25
 'we need to delete column from back so that resized array will not effect us
 If GetProdConst = False Then BatchArray.DeleteColumns 29, 1
 If GetQtyActualShipped = False Then BatchArray.DeleteColumns 28, 1
 If GetHisDmd = False Then BatchArray.DeleteColumns 27, 1
 If GetProjInv = False Then BatchArray.DeleteColumns 26, 1
 If GetAdjNetReq = False Then BatchArray.DeleteColumns 25, 1
 If GetNetReq = False Then BatchArray.DeleteColumns 24, 1
 If GetAdjSysFcst = False Then BatchArray.DeleteColumns 23, 1
 If GetMasterAdjSysFcst = False Then BatchArray.DeleteColumns 22, 1
 If GetSysFcst = False Then BatchArray.DeleteColumns 21, 1



TempArrayRow = 0
J = 5           'First date data field start positon
K = 0           'First item position in FcstDates array
FcstTypes = UBound(ArgFcstTypes())
Kmax = UBound(FcstDates)

If ItemCount > 0 Then
    xBatchFcst.ReDim 0, ItemCount * (FcstTypes + 1) - 1, 0, 22 + Nbrpds
Else
    'Some thing is wrong we should not go ahead
End If

If BatchArray.Count(1) > 0 Then
    OldLcid = BatchArray(0, 0)
    OldItem = BatchArray(0, 1)
    For L = 0 To FcstTypes
        If L = 0 Then
            xBatchFcst(TempArrayRow + L, 0) = BatchArray(0, 0)  'Lcid
            xBatchFcst(TempArrayRow + L, 1) = BatchArray(0, 1)  'Item
            xBatchFcst(TempArrayRow + L, 2) = BatchArray(0, 2)  'ItDesc
            xBatchFcst(TempArrayRow + L, Nbrpds + 5) = BatchArray(0, 3)  'Class1
            xBatchFcst(TempArrayRow + L, Nbrpds + 6) = BatchArray(0, 4) 'Class2
            xBatchFcst(TempArrayRow + L, Nbrpds + 7) = BatchArray(0, 5)  'Class3
            xBatchFcst(TempArrayRow + L, Nbrpds + 8) = BatchArray(0, 6) 'Class4
            xBatchFcst(TempArrayRow + L, Nbrpds + 9) = BatchArray(0, 7)  'ItStat
            xBatchFcst(TempArrayRow + L, Nbrpds + 10) = BatchArray(0, 8) 'VelCode
            xBatchFcst(TempArrayRow + L, Nbrpds + 11) = BatchArray(0, 9) 'Vnid
            xBatchFcst(TempArrayRow + L, Nbrpds + 12) = BatchArray(0, 10) 'Assort
            xBatchFcst(TempArrayRow + L, Nbrpds + 13) = BatchArray(0, 11) 'ById
            xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 1) = BatchArray(0, 12) 'LDivision
            xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 2) = BatchArray(0, 13) 'LStatus
            xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 3) = BatchArray(0, 14) 'LRegion
            xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 4) = BatchArray(0, 15) 'LUserDefined
        End If
        xBatchFcst(TempArrayRow + L, 4) = 0 'total
        xBatchFcst(TempArrayRow + L, 3) = ArgFcstTypes(L) ' FcstType
        xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 5) = BatchArray(0, 16) 'cube
        xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 6) = BatchArray(0, 17) 'weight
        xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 7) = BatchArray(0, 18) 'Price
        xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 8) = BatchArray(0, 19) 'Cost
        xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 9) = "N" 'Modified Flag
    Next
    For i = 0 To BatchArray.UpperBound(1)
        If i = 0 Then
            OldLcid = BatchArray(0, 0)
            OldItem = BatchArray(0, 1)
            For L = 0 To FcstTypes
                If L = 0 Then
                    xBatchFcst(TempArrayRow + L, 0) = BatchArray(0, 0)  'Lcid
                    xBatchFcst(TempArrayRow + L, 1) = BatchArray(0, 1)  'Item
                    xBatchFcst(TempArrayRow + L, 2) = BatchArray(0, 2)  'ItDesc
                    xBatchFcst(TempArrayRow + L, Nbrpds + 5) = BatchArray(0, 3)  'Class1
                    xBatchFcst(TempArrayRow + L, Nbrpds + 6) = BatchArray(0, 4) 'Class2
                    xBatchFcst(TempArrayRow + L, Nbrpds + 7) = BatchArray(0, 5)  'Class3
                    xBatchFcst(TempArrayRow + L, Nbrpds + 8) = BatchArray(0, 6) 'Class4
                    xBatchFcst(TempArrayRow + L, Nbrpds + 9) = BatchArray(0, 7)  'ItStat
                    xBatchFcst(TempArrayRow + L, Nbrpds + 10) = BatchArray(0, 8) 'VelCode
                    xBatchFcst(TempArrayRow + L, Nbrpds + 11) = BatchArray(0, 9) 'Vnid
                    xBatchFcst(TempArrayRow + L, Nbrpds + 12) = BatchArray(0, 10) 'Assort
                    xBatchFcst(TempArrayRow + L, Nbrpds + 13) = BatchArray(0, 11) 'ById
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 1) = BatchArray(0, 12) 'LDivision
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 2) = BatchArray(0, 13) 'LStatus
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 3) = BatchArray(0, 14) 'LRegion
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 4) = BatchArray(0, 15) 'LUserDefined
                End If
                    xBatchFcst(TempArrayRow + L, 4) = 0 'total
                    xBatchFcst(TempArrayRow + L, 3) = ArgFcstTypes(L) ' FcstType
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 5) = BatchArray(0, 16) 'cube
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 6) = BatchArray(0, 17) 'weight
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 7) = BatchArray(0, 18) 'Price
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 8) = BatchArray(0, 19) 'Cost
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 9) = "N" 'Modified Flag
            Next
            NewLcID = BatchArray(1, 0)
            NewItem = BatchArray(1, 1)
        Else
            NewLcID = BatchArray(i, 0)
            NewItem = BatchArray(i, 1)
        End If
        If OldLcid <> NewLcID Or OldItem <> NewItem Then
            If K < Kmax Then
                For K = K To Kmax
                    For L = 0 To FcstTypes
                        xBatchFcst(TempArrayRow + L, J) = 0 'If data is missing fill with 0
                    Next
                    K = K + 1
                    J = J + 1
                Next
            End If
            TempArrayRow = TempArrayRow + 1 * (FcstTypes + 1)
            For L = 0 To FcstTypes
                If L = 0 Then
                    xBatchFcst(TempArrayRow + L, 0) = BatchArray(i, 0)  'Lcid
                    xBatchFcst(TempArrayRow + L, 1) = BatchArray(i, 1)  'Item
                    xBatchFcst(TempArrayRow + L, 2) = BatchArray(i, 2)  'ItDesc
                    xBatchFcst(TempArrayRow + L, Nbrpds + 5) = BatchArray(i, 3)  'Class1
                    xBatchFcst(TempArrayRow + L, Nbrpds + 6) = BatchArray(i, 4) 'Class2
                    xBatchFcst(TempArrayRow + L, Nbrpds + 7) = BatchArray(i, 5)  'Class3
                    xBatchFcst(TempArrayRow + L, Nbrpds + 8) = BatchArray(i, 6) 'Class4
                    xBatchFcst(TempArrayRow + L, Nbrpds + 9) = BatchArray(i, 7)  'ItStat
                    xBatchFcst(TempArrayRow + L, Nbrpds + 10) = BatchArray(i, 8) 'VelCode
                    xBatchFcst(TempArrayRow + L, Nbrpds + 11) = BatchArray(i, 9) 'Vnid
                    xBatchFcst(TempArrayRow + L, Nbrpds + 12) = BatchArray(i, 10) 'Assort
                    xBatchFcst(TempArrayRow + L, Nbrpds + 13) = BatchArray(i, 11) 'ById
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 1) = BatchArray(0, 12) 'LDivision
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 2) = BatchArray(0, 13) 'LStatus
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 3) = BatchArray(0, 14) 'LRegion
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 4) = BatchArray(0, 15) 'LUserDefined
                End If
                    xBatchFcst(TempArrayRow + L, 4) = 0 'total
                    xBatchFcst(TempArrayRow + L, 3) = ArgFcstTypes(L) ' FcstType
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 5) = BatchArray(0, 16) 'cube
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 6) = BatchArray(0, 17) 'weight
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 7) = BatchArray(0, 18) 'Price
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 8) = BatchArray(0, 19) 'Cost
                    xBatchFcst(TempArrayRow + L, 13 + Nbrpds + 9) = "N" 'Modified Flag
            Next
            OldLcid = BatchArray(i, 0)
            OldItem = BatchArray(i, 1)
            J = 5
            K = 0
       End If
            If FcstDates(K) = BatchArray(i, 20) Then
            For L = 0 To FcstTypes
                xBatchFcst(TempArrayRow + L, J) = BatchArray(i, 21 + L)
            Next

             K = K + 1
            J = J + 1
            Else
             For K = K To Kmax          'This loop is for missing data
                If CDate(BatchArray(i, 20)) > CDate(FcstDates(K)) Then
                    For L = 0 To FcstTypes
                        xBatchFcst(TempArrayRow + L, J) = 0 'If Data is missing fill with 0
                    Next
                    J = J + 1
                Else
                    For L = 0 To FcstTypes
                        xBatchFcst(TempArrayRow + L, J) = BatchArray(i, 21 + L)
                    Next
                     J = J + 1
                     K = K + 1
                Exit For
                End If
            Next
            End If

    Next i


    If K < Kmax Then        ' This loop is missing data for last item last periods
        For K = K To Kmax
            For L = 0 To FcstTypes
                xBatchFcst(TempArrayRow + L, J) = 0     'If Data is missing fill with 0
            Next
            K = K + 1
            J = J + 1
        Next
    End If

    'Populate the totals field
    For i = 0 To xBatchFcst.UpperBound(1)
        Total = 0
        For J = 5 To 5 + Nbrpds - 1 ' data starts from column 5
            Total = Total + xBatchFcst(i, J)
        Next
        xBatchFcst(i, 4) = Total       'Total field is 4
    Next
'Add Code For User element
    If UBound(ArgUserElements()) = 0 Then
        If ArgUserElements(0) = "" Or IsNull(ArgUserElements(0)) Then
          'There are no user elements selected  so no need to add to FcstTypes array
          UserElementsExists = False
        Else
          UserElementsExists = True
        End If
    Else
        UserElementsExists = True
    End If
    
    If UserElementsExists = False Then
        GoTo CleanUp
    End If
    If UserElementsExists = True Then
    ReDim UserElements(0 To UBound(ArgUserElements))
        For i = 0 To UBound(ArgUserElements)
            UserElements(i) = ArgUserElements(i)
        Next
    End If
    UserElementCount = UBound(UserElements) + 1
    'ReDim xUserElementBatch(0 To ItemCount - 1)
    ReDim xUserElementBatch(0 To UserElementCount - 1)

    For K = 0 To UBound(UserElements)
        UserElementDataExists = LoadBatchUserElementData(UserElements(K))
        L = 0
        xUserElementBatch(K).ReDim 0, ItemCount - 1, 0, 22 + Nbrpds
        For i = 0 To xBatchFcst.UpperBound(1) Step FcstTypes + 1
            StartPos = 1
            For J = 0 To UBound(FcstDates) - 1
                Qty = 0
                If UserElementDataExists = True Then
                    GetBatchUserElementQtyForAPd StartPos, xBatchFcst.Value(i, 1), xBatchFcst.Value(i, 0), FcstDates(J), Qty
                Else
                Qty = 0
                End If
                xUserElementBatch(K).Value(L, 5 + J) = Qty
            Next J
            L = L + 1
        Next
     Next K
    'Populate Userelement totals
    For K = 0 To UBound(UserElements)
        For L = 0 To ItemCount - 1
                Total = 0
                For J = 5 To 5 + Nbrpds - 1
                    Total = Total + xUserElementBatch(K).Value(L, J)
                Next J
                xUserElementBatch(K).Value(L, 4) = Total
        Next L
    Next K
    TempArray.ReDim 0, ItemCount * (FcstTypes + UserElementCount + 1) - 1, 0, 22 + Nbrpds
    K = 0
    N = 0
    For i = 0 To ItemCount * (FcstTypes + UserElementCount + 1) - 1 Step (FcstTypes + UserElementCount + 1)
        For L = 0 To FcstTypes Step 1
            'For j = 0 To 18 + Nbrpds
            For J = 0 To 22 + Nbrpds
                TempArray.Value(i + L, J) = xBatchFcst(i + L - K, J)
            Next J
        Next L
           
        For M = 0 To UserElementCount - 1 Step 1
            TempArray.Value(i + L + M, 3) = UserElements(M)
            TempArray.Value(i + L + M, 4) = xUserElementBatch(M).Value(N, 4)
             For J = 5 To 5 + Nbrpds
                TempArray.Value(i + L + M, J) = xUserElementBatch(M).Value(N, J)
            Next J
            'For j = 5 + Nbrpds + 9 To Nbrpds + 18
            For J = 5 + Nbrpds + 13 To Nbrpds + 22
                TempArray.Value(i + L + M, J) = xBatchFcst(i - K, J) 'populate the other fields same as fcsttypes data like cost
            Next J
        Next M
            K = K + UserElementCount
            N = N + 1
    Next i
    xBatchFcst.ReDim 0, ItemCount * (FcstTypes + UserElementCount + 1) - 1, 0, 22 + Nbrpds
    For i = 0 To ItemCount * (FcstTypes + UserElementCount + 1) - 1 Step 1
        For J = 0 To 22 + Nbrpds
            xBatchFcst.Value(i, J) = TempArray(i, J)
        Next J
    Next i
End If
GenerateBatchFcst = 1
CleanUp:
Destroy_Objects
Exit Function
ErrorHandler:
'clean up
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GenerateBatchFcst)"
     f_HandleErr , , , "AIMFcsPlanner::GenerateBatchFcst", Now, gDRJobError, True, Err
    GoTo CleanUp
End Function
Private Function BatchFcst(xForeCast As XArrayDB)
On Error GoTo ErrorHandler
    Dim i As Integer
    Dim J As Long
    Dim K As Long
    Dim LcId As String
    Dim Item As String
    Dim FcstDates() As Date
    Dim AIM_BatchFcstDetail_Save_Sp As ADODB.Command
    Dim AIMFcstSetUp_Update As New ADODB.Command
    Dim UserElementCount As Integer
    Dim FcstTypesCount As Integer
    
    FcstTypesCount = UBound(FcstTypes()) + 1
    If UserElementsExists = True Then
        UserElementCount = UBound(UserElements()) + 1
    Else
        UserElementCount = 0
    End If
    If FcstRolling = False Then
        'FcstStartDate = rsFcstMaint("FcstStartDate").Value
        FcstStartDate = GetRollingFcstStartDate(FcstStartDate, FcstStartDate, AC, FcstInterval)
    Else
        'FcstStartDate = Now
        FcstStartDate = GetRollingFcstStartDate(Now(), FcstStartDate, AC, FcstInterval)
    End If
    Populate_FcstDates FcstInterval, HistPds, FuturePds, FcstStartDate, FcstDates()
    
    Set AIM_BatchFcstDetail_Save_Sp = New ADODB.Command
    With AIM_BatchFcstDetail_Save_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_BatchFcstDetail_Save_Sp"
        .Parameters.Refresh
    End With
    
    If xForeCast.Count(1) <> 0 Then

    For i = 0 To xForeCast.UpperBound(1) Step FcstTypesCount + UserElementCount
    LcId = xForeCast(i, 0)
    Item = xForeCast(i, 1)
    For J = 0 To FuturePds - 1
        With AIM_BatchFcstDetail_Save_Sp
                            .Parameters("@RepositoryKey") = U_FcstKey
                            .Parameters("@LcID") = LcId
                            .Parameters("@item") = Item
                            '.Parameters("@FcstType") = UpdateCode
                            .Parameters("@FcstPdBegDate") = FcstDates(J)
                            .Parameters("@FcstPdEndDate") = FcstDates(J + 1) - 1
                            .Parameters("@SysFcst") = 0
                            .Parameters("@MasterFcstAdj") = 0
                            .Parameters("@FcstAdj") = 0
                            .Parameters("@NetReq") = 0
                            .Parameters("@AdjNetReq") = 0
                            .Parameters("@ProjInv") = 0
                            .Parameters("@HistDmd") = 0
                            .Parameters("@ProdConst") = 0

                            For K = 0 To UBound(FcstTypes())
                                If USysFcst = True And FcstTypes(K) = "SYSFCST" Then
                                    .Parameters("@SysFcst") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UMasterFcstAdj = True And FcstTypes(K) = "MASTERFCSTADJ" Then
                                    .Parameters("@MasterFcstAdj") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UFcstAdj = True And FcstTypes(K) = "FCSTADJ" Then
                                    .Parameters("@FcstAdj") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UNetReq = True And FcstTypes(K) = "NETREQ" Then
                                    .Parameters("@NetReq") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UAdjNetReq = True And FcstTypes(K) = "ADJNETREQ" Then
                                    .Parameters("@AdjNetReq") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UProjInv = True And FcstTypes(K) = "PROJINV" Then
                                    .Parameters("@ProjInv") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UHistDmd = True And FcstTypes(K) = "HISDMD" Then
                                    .Parameters("@HistDmd") = xForeCast(i + K, 5 + HistPds + J)
                                ElseIf UProdConst = True And FcstTypes(K) = "PRODCONST" Then
                                    .Parameters("@ProdConst") = xForeCast(i + K, 5 + HistPds + J)
                                End If
                            Next K
                
                        .Execute
    End With

    Next J

    Next i
End If
'
'Update the AimFcstSetUp table  with fcstlocked field set to true
 With AIMFcstSetUp_Update
        Set .ActiveConnection = Cn
        .CommandText = "Update AIMFcstSetUp set FcstLocked =1 where FcstId ='" + U_FcstId + "'"
    End With
  AIMFcstSetUp_Update.Execute
CleanUp:
  
  If Not (AIM_BatchFcstDetail_Save_Sp Is Nothing) Then Set AIM_BatchFcstDetail_Save_Sp.ActiveConnection = Nothing
  Set AIM_BatchFcstDetail_Save_Sp = Nothing

  If Not (AIMFcstSetUp_Update Is Nothing) Then Set AIMFcstSetUp_Update.ActiveConnection = Nothing
  Set AIMFcstSetUp_Update = Nothing

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(BatchFcst)"
    f_HandleErr , , , "AIMFcsPlanner::BatchFcst", Now, gDRJobError, True, Err
    GoTo CleanUp
End Function

Function GetForecastUnit( _
    arg As Integer _
) As Integer
On Error GoTo ErrorHandler

    Select Case arg
    Case 0
        GetForecastUnit = FcstUnits
    Case 1
        GetForecastUnit = FcstCost
    Case 2
        GetForecastUnit = FcstWeight
    Case 3
        GetForecastUnit = FcstCube
    Case 4
        GetForecastUnit = FcstPrice
    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.GetForecastUnit)"
     f_HandleErr , , , "AIMFcsPlanner::GetForecastUnit", Now, gDRJobError, True, Err
End Function

''*****************************************************************************
''   FUNCTIONS
''*****************************************************************************
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
''   Gets data from the AIMDmdPlanFcstSetup table to populate the ForecastID dropdown in the UI
'' .......................................................................
''
''   Returns false with an empty XArray if there are no records to fetch,
''   else true with an active XArray.
''
''~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Public Function gList_FcstIDs( _
    p_Cn As ADODB.Connection, _
    r_ForecastList As ADODB.Recordset, _
    strFormCaption As String, _
    Enabled As Boolean _
) As Boolean
On Error GoTo ErrorHandler
    'called from forecastsetup and fcstplanner screens so make it independent
    Dim rsForecastList As ADODB.Recordset
    Dim AIM_DmdPlanFcstID_Load_Sp As ADODB.Command
    Dim RtnCode As Long
    Dim ColCounter As Long

    'Default to failure until the process returns its result.
    gList_FcstIDs = False

    'Get Forecast IDs and Descriptions from AIMForecast
    Set AIM_DmdPlanFcstID_Load_Sp = New ADODB.Command
    With AIM_DmdPlanFcstID_Load_Sp
        Set .ActiveConnection = p_Cn
        .CommandTimeout = 0
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DmdPlanFcstID_Load_Sp"
    'Set parameters and their values
        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("RETURN", adInteger, adParamReturnValue)
    'Input
        If Enabled = True Then
        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@FcstEnabled", adBoolean, adParamInput, , 1)
        Else
        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@FcstEnabled", adBoolean, adParamInput, , Null)
        End If
        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@UserID", adVarWChar, adParamInput, 12, gUserID)
    'Output
        .Parameters.Append AIM_DmdPlanFcstID_Load_Sp.CreateParameter("@RecordCount", adInteger, adParamOutput, , 0)
    End With

    Set rsForecastList = New ADODB.Recordset
    With rsForecastList
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
        .Open AIM_DmdPlanFcstID_Load_Sp
    End With

    If AIM_DmdPlanFcstID_Load_Sp("@RecordCount").Value > 0 _
    And f_IsRecordsetOpenAndPopulated(rsForecastList) _
    Then
        'Set return value
        gList_FcstIDs = True

        Set r_ForecastList = New ADODB.Recordset
        r_ForecastList.Fields.Append "FcstID", adVarWChar, 12
        r_ForecastList.Fields.Append "FcstDesc", adVarWChar, 40
        'Populate
        r_ForecastList.Open
        rsForecastList.MoveFirst
        Do Until rsForecastList.eof
            'Set display to match recordset(s)
            With r_ForecastList
                .AddNew
                For ColCounter = 0 To rsForecastList.Fields.Count - 1
                    .Fields(ColCounter) = rsForecastList.Fields(ColCounter)
                Next
            End With
            rsForecastList.MoveNext
        Loop
        r_ForecastList.MoveFirst
    Else
        'Set return value
        gList_FcstIDs = False
    End If

CleanUp:
    If f_IsRecordsetValidAndOpen(rsForecastList) Then rsForecastList.Close
    Set rsForecastList = Nothing
    If Not (AIM_DmdPlanFcstID_Load_Sp Is Nothing) Then Set AIM_DmdPlanFcstID_Load_Sp.ActiveConnection = Nothing
    Set AIM_DmdPlanFcstID_Load_Sp = Nothing

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.gList_FcstIDs)"
     f_HandleErr , , , "AIMFcsPlanner::gList_FcstIDs", Now, gDRJobError, True, Err
    GoTo CleanUp
End Function

Private Function g_GetBatchData( _
    p_FcstID As String, _
    p_FcstSetupKey As Long, _
    p_FcstBegDate As Date _
 ) As Long
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    Dim WhereClause As String
    Dim BatchQuery As String
    Dim CountQuery As String
    Dim RepositoryKey As Long
    Dim xaFilterCriteria As XArrayDB
    
    RtnCode = g_SelectFilters(Cn, p_FcstSetupKey, xaFilterCriteria, COMP_EMPTY)
    RepositoryKey = g_GetRepositoryKey(Cn, p_FcstID)
    WhereClause = g_BuildWhereClause(xaFilterCriteria)
    'Get the Seasonality Table Version Number
    SAVersion = g_GetSAVersion(Cn)
    
    BatchQuery = g_QueryForFcstBatchItems(False, WhereClause, RepositoryKey, p_FcstBegDate)
    CountQuery = g_QueryForFcstBatchItems(True, WhereClause, RepositoryKey, p_FcstBegDate)
    'Fetch data
    'Init. the recordset
    If f_IsRecordsetValidAndOpen(rsBatchData) Then rsBatchData.Close
    'Fetch in the list of items affected by the forecast filter(s)
    With FetchBatch
    .CommandText = BatchQuery
    End With
    rsBatchData.Open FetchBatch
    If f_IsRecordsetOpenAndPopulated(rsBatchData) Then
        With FetchCount
        .CommandText = CountQuery
        End With
        'Fetch in the list of items affected by the forecast filter(s)
        rsCount.Open FetchCount
        If f_IsRecordsetOpenAndPopulated(rsCount) Then
            g_GetBatchData = rsCount.RecordCount
        Else
            g_GetBatchData = 0
        End If
    End If
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMAdvForecastMod.g_GetBatchData)"
     f_HandleErr , , , "AIMFcsPlanner::g_GetBatchData", Now, gDRJobError, True, Err
End Function

Public Function g_GetRepositoryKey(p_Cn As ADODB.Connection, p_FcstID As String) As Long
On Error GoTo ErrorHandler
'Called from inside the module and from outside the module also
'So make it selfcontined function
'If this function returs -1 then there is no record created in the Forecastreposioty table
    Dim RtnCode As Integer
    Dim AIM_RepositoryKey_Get_Sp As ADODB.Command
    Set AIM_RepositoryKey_Get_Sp = New ADODB.Command
    With AIM_RepositoryKey_Get_Sp
        Set .ActiveConnection = p_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_RepositoryKey_Get_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter("@FcstID", adVarWChar, adParamInput, 12)
        .Parameters.Append .CreateParameter("@RepositoryKey", adNumeric, adParamInputOutput)
        .Parameters("@RepositoryKey").Precision = 18
        .Parameters("@RepositoryKey").NumericScale = 0
        'Set values
        .Parameters("@FcstID").Value = p_FcstID
        .Parameters("@RepositoryKey").Value = Null
        .Execute
        RtnCode = .Parameters(0).Value
        g_GetRepositoryKey = IIf(IsNull(.Parameters("@RepositoryKey").Value), -1, .Parameters("@RepositoryKey").Value)
    End With
CleanUp:
    If Not (AIM_RepositoryKey_Get_Sp Is Nothing) Then Set AIM_RepositoryKey_Get_Sp.ActiveConnection = Nothing
    Set AIM_RepositoryKey_Get_Sp = Nothing

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.g_GetRepositoryKey)"
    f_HandleErr , , , "AIMFcsPlanner::g_GetRepositoryKey", Now, gDRJobError, True, Err
    GoTo CleanUp
End Function
Public Function g_PromoteToMaster( _
    p_FcstID As String, _
    p_xaFilterCriteria As XArrayDB, _
    p_FcstBegDate As Date, _
    p_FcstEndDate As Date _
) As Integer
On Error GoTo ErrorHandler
'Called from outside the module so make it self contained
    Dim RtnCode As Integer
    Dim RepositoryKey As Long
    Dim WhereClause As String, SQLQuery As String
    Dim DeleteFM As String, InsertFM As String
    Dim UpdateFL As String, InsertFAL As String, DeleteFL As String
    Dim ExecQuery As ADODB.Command
    Dim AIM_Repository_RefreshAdj_Sp As ADODB.Command
    Dim TransCn As ADODB.Connection
    
    'Open a new connection for this process for the sake of using transactions
    Set TransCn = New ADODB.Connection
    RtnCode = SQLConnection(TransCn, CONNECTION_OPEN, True)
    If RtnCode <> SUCCEED Then
        g_PromoteToMaster = RtnCode
        Exit Function
    Else
        'Default return status to failure
        g_PromoteToMaster = FAIL
    End If
    'Now, process each within a transaction
    'Instantiate parameter arguments
    RepositoryKey = g_GetRepositoryKey(TransCn, p_FcstID)
    WhereClause = g_BuildWhereClause(p_xaFilterCriteria)
    'Get the Seasonality Table Version Number
    SAVersion = g_GetSAVersion(TransCn)
    SQLQuery = COMP_EMPTY
    
    'Create queries for the processing
    '1.  Delete existing from Master
    DeleteFM = COMP_EMPTY
    DeleteFM = DeleteFM & " DELETE AIMFcstMaster " & vbCrLf
    DeleteFM = DeleteFM & " FROM AIMFcstMaster FM " & vbCrLf
    DeleteFM = DeleteFM & " INNER JOIN Item ON (FM.LcID = Item.LcID AND FM.Item = Item.Item) " & vbCrLf
    DeleteFM = DeleteFM & " INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat " & vbCrLf
    DeleteFM = DeleteFM & " INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID " & vbCrLf
    DeleteFM = DeleteFM & " INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID"
    DeleteFM = DeleteFM & "     AND Item.Assort = AIMVendors.Assort" & vbCrLf
    DeleteFM = DeleteFM & " INNER JOIN ForecastRepositoryDetail FD " & vbCrLf
    DeleteFM = DeleteFM & " ON Item.LcID = FD.LcID " & vbCrLf
    DeleteFM = DeleteFM & "     AND Item.Item = FD.Item " & vbCrLf
    DeleteFM = DeleteFM & "     AND FM.PeriodBegDate = FD.FcstPdBegDate " & vbCrLf
    DeleteFM = DeleteFM & "     AND FM.PeriodEndDate = FD.FcstPdEndDate " & vbCrLf
    DeleteFM = DeleteFM & " WHERE UPPER(Item.ItStat) NOT IN ('I', 'P', 'X') " & vbCrLf
    DeleteFM = DeleteFM & "     AND FD.FcstPdBegDate >= '" & p_FcstBegDate & "'" & vbCrLf
    DeleteFM = DeleteFM & "     AND FD.FcstPdEndDate <= '" & p_FcstEndDate & "'" & vbCrLf
    DeleteFM = DeleteFM & "     AND FD.RepositoryKey = " & RepositoryKey & vbCrLf
    
    '2.  Insert available into Master
    InsertFM = COMP_EMPTY
    InsertFM = InsertFM & " INSERT INTO AIMFcstMaster ( " & vbCrLf
    InsertFM = InsertFM & "     LcID, Item, PeriodBegDate, PeriodEndDate, " & vbCrLf
    InsertFM = InsertFM & "     QtyAdj, " & vbCrLf
    InsertFM = InsertFM & "     QtyAdjOverride, " & vbCrLf
    InsertFM = InsertFM & "     QtyAdjPct, " & vbCrLf
    InsertFM = InsertFM & "     AdjOverride, " & vbCrLf
    InsertFM = InsertFM & "     DateTimeEdit " & vbCrLf
    InsertFM = InsertFM & " ) SELECT " & vbCrLf
    InsertFM = InsertFM & "     Item.LcID, Item.Item, FD.FcstPdBegDate, FD.FcstPdEndDate, " & vbCrLf
    InsertFM = InsertFM & "     (FD.QtyAdj + FD.MasterQtyAdj), " & vbCrLf
    InsertFM = InsertFM & "     CASE " & vbCrLf
    InsertFM = InsertFM & "         WHEN FD.AdjOverride = 1 THEN FD.QtyAdjOverride " & vbCrLf
    InsertFM = InsertFM & "         ELSE FD.MasterQtyAdjOverride " & vbCrLf
    InsertFM = InsertFM & "     END, " & vbCrLf
    InsertFM = InsertFM & "     (FD.QtyAdjPct + FD.MasterQtyAdjPct), " & vbCrLf
    InsertFM = InsertFM & "     CASE " & vbCrLf
    InsertFM = InsertFM & "         WHEN FD.AdjOverride = 1 THEN 1 " & vbCrLf
    InsertFM = InsertFM & "         WHEN FD.MasterAdjOverride = 2 THEN 0 " & vbCrLf
    InsertFM = InsertFM & "         ELSE FD.MasterAdjOverride " & vbCrLf
    InsertFM = InsertFM & "     END, " & vbCrLf
    InsertFM = InsertFM & "     GetDate() " & vbCrLf
    InsertFM = InsertFM & " FROM Item " & vbCrLf
    InsertFM = InsertFM & " INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat " & vbCrLf
    InsertFM = InsertFM & " INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID " & vbCrLf
    InsertFM = InsertFM & " INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID"
    InsertFM = InsertFM & "     AND Item.Assort = AIMVendors.Assort" & vbCrLf
    InsertFM = InsertFM & " INNER JOIN ForecastRepositoryDetail FD " & vbCrLf
    InsertFM = InsertFM & " ON Item.LcID = FD.LcID AND Item.Item = FD.Item " & vbCrLf
    InsertFM = InsertFM & " WHERE UPPER(Item.ItStat) NOT IN ('I', 'P', 'X') " & vbCrLf
    InsertFM = InsertFM & "     AND FD.FcstPdBegDate >= '" & p_FcstBegDate & "'" & vbCrLf
    InsertFM = InsertFM & "     AND FD.FcstPdEndDate <= '" & p_FcstEndDate & "'" & vbCrLf
    InsertFM = InsertFM & "     AND FD.RepositoryKey = " & RepositoryKey & vbCrLf

    '3.  Update Log.IsPromoted to True
    UpdateFL = COMP_EMPTY
    UpdateFL = UpdateFL & " UPDATE ForecastRepository_Log SET " & vbCrLf
    UpdateFL = UpdateFL & "     IsPromoted = 1 " & vbCrLf    ' True
    UpdateFL = UpdateFL & " FROM Item " & vbCrLf
    UpdateFL = UpdateFL & " INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat " & vbCrLf
    UpdateFL = UpdateFL & " INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID " & vbCrLf
    UpdateFL = UpdateFL & " INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID"
    UpdateFL = UpdateFL & "     AND Item.Assort = AIMVendors.Assort" & vbCrLf
    UpdateFL = UpdateFL & " WHERE ForecastRepository_Log.LcID = Item.LcID AND ForecastRepository_Log.Item = Item.Item " & vbCrLf
    UpdateFL = UpdateFL & " AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X') " & vbCrLf
    UpdateFL = UpdateFL & " AND ForecastRepository_Log.AdjustBegDate >= '" & p_FcstBegDate & "'" & vbCrLf
    UpdateFL = UpdateFL & " AND ForecastRepository_Log.AdjustEndDate <= '" & p_FcstEndDate & "'" & vbCrLf
    UpdateFL = UpdateFL & " AND ForecastRepository_Log.RepositoryKey = " & RepositoryKey & vbCrLf
    
    '4.  Move promoted log to archive
    '4.a.  Insert into Archive Log
    InsertFAL = COMP_EMPTY
    InsertFAL = InsertFAL & " INSERT INTO ForecastRepositoryArch_Log ( " & vbCrLf
    InsertFAL = InsertFAL & "   RepositoryKey, LcID, Item, " & vbCrLf
    InsertFAL = InsertFAL & "   IsPromoted, AdjustType, AdjustQty, OverrideEnabled, " & vbCrLf
    InsertFAL = InsertFAL & "   AdjustBegDate, AdjustEndDate, AdjustUserID, AdjustDesc, AdjustDateTime " & vbCrLf
    InsertFAL = InsertFAL & " ) SELECT  " & vbCrLf
    InsertFAL = InsertFAL & "   FRL.RepositoryKey, FRL.LcID, FRL.Item, " & vbCrLf
    InsertFAL = InsertFAL & "   FRL.IsPromoted, FRL.AdjustType, FRL.AdjustQty, FRL.OverrideEnabled, " & vbCrLf
    InsertFAL = InsertFAL & "   FRL.AdjustBegDate, FRL.AdjustEndDate, FRL.AdjustUserID, FRL.AdjustDesc, FRL.AdjustDateTime " & vbCrLf
    InsertFAL = InsertFAL & " FROM ForecastRepository_Log FRL WHERE IsPromoted = 1 " & vbCrLf
    '4.b.  Deldete from reg. log
    DeleteFL = COMP_EMPTY
    DeleteFL = DeleteFL & " DELETE FROM ForecastRepository_Log WHERE IsPromoted = 1 " & vbCrLf

    
    'Instantiate objects
    Set AIM_Repository_RefreshAdj_Sp = New ADODB.Command
    With AIM_Repository_RefreshAdj_Sp
        Set .ActiveConnection = TransCn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_Repository_RefreshAdj_Sp"
        .Parameters.Append .CreateParameter("RETURN", adInteger, adParamReturnValue)
        .Parameters.Append .CreateParameter("@RepositoryKey", adNumeric, adParamInput)
        .Parameters("@RepositoryKey").Precision = 18
        .Parameters("@RepositoryKey").NumericScale = 0
        'Set values
        .Parameters("@RepositoryKey").Value = RepositoryKey
        .Execute
        RtnCode = .Parameters(0).Value
    End With
    
    'Begin the transaction
    TransCn.BeginTrans
    
    Set ExecQuery = New ADODB.Command
    With ExecQuery
        Set .ActiveConnection = TransCn
        .CommandType = adCmdText
        .Prepared = True
        
        '"1.  Delete existing from Master"
        SQLQuery = DeleteFM & WhereClause
        .CommandText = SQLQuery
        .Execute
        Debug.Print SQLQuery & vbCrLf
        
        '"2.  Insert available into Master"
        SQLQuery = InsertFM & WhereClause
        .CommandText = SQLQuery
        .Execute
        Debug.Print SQLQuery & vbCrLf
        
        '"3.  Update Log.IsPromoted to True"
        SQLQuery = UpdateFL & WhereClause
        .CommandText = SQLQuery
        .Execute
        Debug.Print SQLQuery & vbCrLf
        
        '"4.  Move promoted log to archive" & vbCrLf & _
        "4.a.  Insert into Archive Log"
        SQLQuery = InsertFAL
        .CommandText = SQLQuery
        .Execute
        Debug.Print SQLQuery & vbCrLf
        
        '"4.b.  Delete from reg. log"
        SQLQuery = DeleteFL
        .CommandText = SQLQuery
        .Execute
        Debug.Print SQLQuery & vbCrLf
        
    End With
    
    'Commit transaction
    TransCn.CommitTrans
    RtnCode = 0
CleanUp:
    If Not (AIM_Repository_RefreshAdj_Sp Is Nothing) Then Set AIM_Repository_RefreshAdj_Sp.ActiveConnection = Nothing
    Set AIM_Repository_RefreshAdj_Sp = Nothing
    If Not (ExecQuery Is Nothing) Then Set ExecQuery.ActiveConnection = Nothing
    Set ExecQuery = Nothing
    RtnCode = SQLConnection(TransCn, CONNECTION_CLOSE, False)
    Set TransCn = Nothing
    g_PromoteToMaster = RtnCode
Exit Function
ErrorHandler:
    'Be sure to ROLLBACK
    TransCn.RollbackTrans
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMFcstPlanner.g_PromoteToMaster)"
     f_HandleErr , , , "AIMFcsPlanner::g_PromoteToMaster", Now, gDRJobError, True, Err
    RtnCode = -1
    GoTo CleanUp:
End Function





