Attribute VB_Name = "AIMUIStandards"
'******************************************************************
'   This file created Oct 25, 2002.
'   All User Interface standards used in the application need to be
'   defined here as constants for use within the application.
'   For instance, Color Palette definitions, Font <PERSON> et al.
'   Author: <PERSON><PERSON><PERSON><PERSON> Stocksdale
'   WORK IN PROGRESS
'******************************************************************

Option Explicit

Public Const EXE_FONT_NAME As String = "Microsoft Sans Serif"      '"MS Sans Serif"
Public Const EXE_FONT_CHARSET As Long = 1
Public Const EXE_FONT_SIZE As Long = 8

'Control identifiers
Public Const CTRL_VB_FORM As Long = 0
Public Const CTRL_VB_LABEL As Long = 1
Public Const CTRL_VB_TEXTBOX As Long = 2
Public Const CTRL_VB_FRAME As Long = 3
Public Const CTRL_VB_CHECKBOX As Long = 4
Public Const CTRL_VB_OPTIONBUTTON As Long = 5
Public Const CTRL_VB_COMBOBOX As Long = 6
Public Const CTRL_VB_LISTBOX As Long = 7
Public Const CTRL_VB_HSCROLLBAR As Long = 8
Public Const CTRL_VB_VSCROLLBAR As Long = 9
Public Const CTRL_VB_TIMER As Long = 10
Public Const CTRL_VB_DRIVELISTBOX As Long = 11
Public Const CTRL_VB_DIRLISTBOX As Long = 12
Public Const CTRL_VB_FILELISTBOX As Long = 13
Public Const CTRL_VB_SHAPE As Long = 14
Public Const CTRL_VB_LINE As Long = 15
Public Const CTRL_VB_IMAGE As Long = 16
Public Const CTRL_VB_TABSTRIP As Long = 17
Public Const CTRL_VB_TOOLBAR As Long = 18
Public Const CTRL_VB_STATUSBAR As Long = 19
Public Const CTRL_VB_PROGRESSBAR As Long = 20
Public Const CTRL_VB_TREEVIEW As Long = 21
Public Const CTRL_VB_LISTVIEW As Long = 22
Public Const CTRL_VB_IMAGELIST As Long = 23
Public Const CTRL_VB_SLIDER As Long = 24
Public Const CTRL_VB_IMAGECOMBO As Long = 25
Public Const CTRL_VB_COMMANDBUTTON As Long = 30

Public Const CTRL_AR_ARVIEWER2 As Long = 50

Public Const CTRL_TDB_NUMBER As Long = 100
Public Const CTRL_TDB_CALENDAR As Long = 101
Public Const CTRL_TDB_DATE As Long = 102
Public Const CTRL_TDB_TEXT As Long = 103
Public Const CTRL_TDB_TIME As Long = 104

Public Const CTRL_SS_MONTH As Long = 130
Public Const CTRL_SS_DATECOMBO As Long = 131
Public Const CTRL_SS_YEAR As Long = 132

Public Const CTRL_SS_OLEDBGRID As Long = 140
Public Const CTRL_SS_OLEDBCOMBO As Long = 141
Public Const CTRL_SS_OLEDBDROPDOWN As Long = 142

Public Const CTRL_SS_ACTIVETABS As Long = 150
Public Const CTRL_SS_ACTIVETABPANEL As Long = 151
Public Const CTRL_SS_ACTIVETOOLBARS As Long = 152

Public Const CTRL_SS_CHART2D As Long = 160

Public Const CTRL_SS_COMMONDIALOG As Long = 170

'Color Types
Public Const COLOR_TYPE_NORMAL As Long = 0        'Default/Normal
Public Const COLOR_TYPE_SELECTED As Long = 1      'Selected/Emphasized
Public Const COLOR_TYPE_READONLY As Long = 2
Public Const COLOR_TYPE_EDITABLE As Long = 3
Public Const COLOR_TYPE_INACTIVE As Long = 4
Public Const COLOR_TYPE_LINKEDTEXT As Long = 5
Public Const COLOR_TYPE_LINKVISITED As Long = 6
Public Const COLOR_TYPE_FROZEN As Long = 7
Public Const COLOR_TYPE_FORECAST As Long = 8
Public Const COLOR_TYPE_BUCKET3 As Long = 9
Public Const COLOR_TYPE_BUCKET4 As Long = 10

Public Const COLOR_TYPE_PRIMARY As Long = 50
Public Const COLOR_TYPE_SECONDARY As Long = 51

Public Const COLOR_TYPE_CHART_1 As Long = 71
Public Const COLOR_TYPE_CHART_2 As Long = 72
Public Const COLOR_TYPE_CHART_3 As Long = 73
Public Const COLOR_TYPE_CHART_4 As Long = 74
Public Const COLOR_TYPE_CHART_5 As Long = 75
Public Const COLOR_TYPE_CHART_6 As Long = 76
Public Const COLOR_TYPE_CHART_7 As Long = 77
Public Const COLOR_TYPE_CHART_8 As Long = 78
Public Const COLOR_TYPE_CHART_9 As Long = 79

'Target Colors
Public Const TARGET_FORECOLOR As String = "FORECOLOR"
Public Const TARGET_BACKCOLOR As String = "BACKCOLOR"
Public Const TARGET_BACKCOLOR_EVEN As String = "BACKCOLOREVEN"
Public Const TARGET_BACKCOLOR_ODD As String = "BACKCOLORODD"
Public Const TARGET_BACKCOLOR_COLHEAD As String = "COLHEADBACKCOLOR"
Public Const TARGET_BACKCOLOR_TABLEHEAD As String = "TABLEHEADBACKCOLOR"
Public Const TARGET_GRAPH_SYMBOL As String = "GRAPH_SYMBOL"
Public Const TARGET_GRAPH_LINE As String = "GRAPH_LINE"



Public Function UI_GetColor(p_Target As String, p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    If p_ControlType = CTRL_SS_OLEDBCOMBO _
    Or p_ControlType = CTRL_SS_OLEDBDROPDOWN _
    Or p_ControlType = CTRL_SS_OLEDBGRID _
    Then
        UI_GetColor = UI_SetGridColor(p_Target, p_ControlType, p_ColorType)
    
    ElseIf p_ControlType = CTRL_SS_CHART2D _
    Then
        UI_GetColor = UI_SetGraphColor(p_Target, p_ControlType, p_ColorType)
    Else
        Select Case p_Target
            Case TARGET_BACKCOLOR
                UI_GetColor = UI_SetBackColor(p_ControlType, p_ColorType)
                
            Case TARGET_FORECOLOR
                UI_GetColor = UI_SetForeColor(p_ControlType, p_ColorType)
            
            Case Else
                'Here
        End Select
    End If
    
    'If UI_GetColor = 0 Then UI_GetColor = RGB(252, 0, 255)  'Hot Pink, since this is not acceptable
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(UI_GetColor)", Err.Description
End Function


Private Function UI_SetBackColor(p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_ControlType
        Case CTRL_VB_FORM
            'Form=0
            Select Case p_ColorType
                Case COLOR_TYPE_PRIMARY
                    UI_SetBackColor = RGB(230, 230, 230)
                    
                Case COLOR_TYPE_SECONDARY
                    UI_SetBackColor = RGB(72, 88, 197)
                
                Case Else
                    UI_SetBackColor = RGB(230, 230, 230)
                    
            End Select
                
        Case CTRL_VB_LABEL, CTRL_VB_FRAME, CTRL_VB_CHECKBOX, CTRL_VB_OPTIONBUTTON
            'Label=1; Frame=3; CheckBox=5; OptionButton=6
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL
                    UI_SetBackColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    UI_SetBackColor = RGB(72, 88, 197)
                Case COLOR_TYPE_INACTIVE
                    UI_SetBackColor = RGB(236, 238, 248)
                Case Else
                    'Normal
                    UI_SetBackColor = RGB(255, 255, 255)
            End Select
            
        Case CTRL_VB_HSCROLLBAR, CTRL_VB_VSCROLLBAR
            'HScrollBar=9; VScrollbar=10
            
        Case CTRL_VB_TIMER        '11
        Case CTRL_VB_DRIVELISTBOX        '12
        Case CTRL_VB_DIRLISTBOX        '13
        Case CTRL_VB_FILELISTBOX        '14
        Case CTRL_VB_SHAPE        '15
        
        Case CTRL_VB_LINE        '16
        
        Case CTRL_VB_IMAGE        '17
        
        Case CTRL_VB_TABSTRIP        '18
        Case CTRL_VB_TOOLBAR        '19
        Case CTRL_VB_STATUSBAR        '20
        Case CTRL_VB_PROGRESSBAR        '21
        Case CTRL_VB_TREEVIEW        '22
        Case CTRL_VB_LISTVIEW        '23
        Case CTRL_VB_IMAGELIST        '24
        Case CTRL_VB_SLIDER        '25
        Case CTRL_VB_IMAGECOMBO        '26
        
        Case CTRL_VB_COMMANDBUTTON  '30
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
                    UI_SetBackColor = RGB(255, 246, 166)
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    UI_SetBackColor = RGB(255, 230, 0)
                Case Else
                    'Normal
                    UI_SetBackColor = RGB(255, 246, 166)
            End Select
            
        Case CTRL_AR_ARVIEWER2        '50
        
        Case CTRL_VB_TEXTBOX, CTRL_VB_COMBOBOX, CTRL_VB_LISTBOX, _
                CTRL_TDB_NUMBER, CTRL_TDB_CALENDAR, CTRL_TDB_DATE, CTRL_TDB_TEXT, CTRL_TDB_TIME
            'VBTextBox=2; COMBOBOX=7; LISTBOX=8
            'NUMBER=100; CALENDAR=101; DATE=102; TEXT=103; TIME=104
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        White
                    UI_SetBackColor = RGB(255, 255, 255)
                Case Else                  'Bluish
                    UI_SetBackColor = RGB(236, 238, 248)
            End Select
        
        Case CTRL_SS_MONTH        '130
        Case CTRL_SS_DATECOMBO        '131
        Case CTRL_SS_YEAR        '132
        
        Case CTRL_SS_OLEDBGRID, CTRL_SS_OLEDBCOMBO, CTRL_SS_OLEDBDROPDOWN
            '140, 141, 142
            'Should call UI_SetGridColor from UI_GetColor.
            
        Case CTRL_SS_ACTIVETABS        '150
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
                    UI_SetBackColor = RGB(209, 214, 240)
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    UI_SetBackColor = RGB(190, 197, 231)
                Case Else
                    'Normal
                    UI_SetBackColor = RGB(209, 214, 240)
            End Select
            
        Case CTRL_SS_ACTIVETABPANEL        '151
        
        Case CTRL_SS_ACTIVETOOLBARS        '152
            Select Case p_ColorType
                Case COLOR_TYPE_PRIMARY
                    UI_SetBackColor = RGB(230, 230, 230)
                    
                Case COLOR_TYPE_SECONDARY
                    UI_SetBackColor = RGB(72, 88, 197)
                
                Case Else
                    UI_SetBackColor = RGB(230, 230, 230)
                    
            End Select
            
        Case CTRL_SS_COMMONDIALOG        '170

    
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(UI_SetBackColor)", Err.Description
End Function

Private Function UI_SetForeColor(p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_ControlType
        Case CTRL_VB_FORM, CTRL_VB_LABEL, CTRL_VB_FRAME, CTRL_VB_CHECKBOX, CTRL_VB_OPTIONBUTTON, _
                CTRL_VB_TEXTBOX, CTRL_VB_COMBOBOX, CTRL_VB_LISTBOX
        'Form=0; Label=1; Frame=3; CheckBox=5; OptionButton=6
        'TEXTBOX=2; COMBOBOX=7;LISTBOX=8
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL
                    UI_SetForeColor = RGB(80, 80, 80)
                Case COLOR_TYPE_INACTIVE
                    UI_SetForeColor = RGB(160, 160, 160)
            End Select
                
        Case CTRL_VB_TIMER        '11
        
        Case CTRL_VB_DRIVELISTBOX, CTRL_VB_DIRLISTBOX, CTRL_VB_FILELISTBOX
        'DRIVELISTBOX=12; DIRLISTBOX=13; FILELISTBOX=14

        Case CTRL_VB_SHAPE        '15
        Case CTRL_VB_LINE        '16
        
        Case CTRL_VB_IMAGE        '17
        
        Case CTRL_VB_TABSTRIP        '18
        Case CTRL_VB_TOOLBAR        '19
        Case CTRL_VB_STATUSBAR        '20
        Case CTRL_VB_PROGRESSBAR        '21
        Case CTRL_VB_TREEVIEW        '22
        Case CTRL_VB_LISTVIEW        '23
        Case CTRL_VB_IMAGELIST        '24
        Case CTRL_VB_SLIDER        '25
        Case CTRL_VB_IMAGECOMBO        '26
        
        Case CTRL_VB_COMMANDBUTTON  '30
            Select Case p_ColorType
                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
                    UI_SetForeColor = RGB(255, 246, 166)
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    UI_SetForeColor = RGB(255, 230, 0)
                Case Else
                    'Normal
                    UI_SetForeColor = RGB(255, 246, 166)
            End Select
            
        Case CTRL_AR_ARVIEWER2        '50
        
        Case CTRL_TDB_NUMBER        '100
        Case CTRL_TDB_CALENDAR        '101
        Case CTRL_TDB_DATE        '102
        Case CTRL_TDB_TEXT        '103
        Case CTRL_TDB_TIME        '104
        
        Case CTRL_SS_MONTH        '130
        Case CTRL_SS_DATECOMBO        '131
        Case CTRL_SS_YEAR        '132
        
        Case CTRL_SS_OLEDBGRID        '140
        Case CTRL_SS_OLEDBCOMBO        '141
        Case CTRL_SS_OLEDBDROPDOWN        '142
        
        Case CTRL_SS_ACTIVETABS        '150
'            Select Case p_ColorType
'                Case COLOR_TYPE_NORMAL    '0        'Default/Normal
'                    UI_SetForeColor = RGB(209, 214, 240)
'                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
'                    UI_SetForeColor = RGB(190, 197, 231)
'                Case Else
'                    'Normal
'                    UI_SetForeColor = RGB(209, 214, 240)
'            End Select
            UI_SetForeColor = RGB(80, 80, 80)
            
        Case CTRL_SS_ACTIVETABPANEL        '151
        
        Case CTRL_SS_ACTIVETOOLBARS        '152
        
        Case CTRL_SS_COMMONDIALOG        '170

    
    End Select
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(UI_SetForeColor)", Err.Description
End Function

Private Function UI_SetGraphColor(p_Target As String, p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_Target
        Case TARGET_GRAPH_SYMBOL
            Select Case p_ColorType
                Case Else
                    UI_SetGraphColor = RGB(80, 80, 80)
                    
            End Select


        Case TARGET_GRAPH_LINE
            Select Case p_ColorType
                Case COLOR_TYPE_CHART_1
                    UI_SetGraphColor = RGB(255, 219, 140)
                Case COLOR_TYPE_CHART_2
                    UI_SetGraphColor = RGB(185, 208, 112)
                Case COLOR_TYPE_CHART_3
                    UI_SetGraphColor = RGB(191, 171, 87)
                Case COLOR_TYPE_CHART_4
                    UI_SetGraphColor = RGB(253, 201, 88)
                Case COLOR_TYPE_CHART_5
                    UI_SetGraphColor = RGB(202, 160, 70)
                Case COLOR_TYPE_CHART_6
                    UI_SetGraphColor = RGB(245, 255, 126)
                Case COLOR_TYPE_CHART_7
                    UI_SetGraphColor = RGB(196, 204, 141)
                Case COLOR_TYPE_CHART_8
                    UI_SetGraphColor = RGB(128, 153, 78)
                Case COLOR_TYPE_CHART_9
                    UI_SetGraphColor = RGB(222, 255, 156)
                Case Else
                    UI_SetGraphColor = RGB(80, 80, 80) 'Default
                    
            End Select
            
        Case Else
            UI_SetGraphColor = RGB(252, 0, 255)  'Hot Pink, since this is not acceptable
    
    End Select

    If p_ColorType = COLOR_TYPE_INACTIVE Then UI_SetGraphColor = RGB(191, 191, 191)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(UI_SetGraphColor)", Err.Description
End Function

Private Function UI_SetGridColor(p_Target As String, p_ControlType As Long, p_ColorType As Long) As Long
On Error GoTo ErrorHandler

    Select Case p_Target
        Case TARGET_BACKCOLOR_EVEN
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    UI_SetGridColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    UI_SetGridColor = RGB(250, 238, 136)
                Case Else   'COLOR_TYPE_READONLY, COLOR_TYPE_NORMAL
                    UI_SetGridColor = RGB(247, 248, 253)   'First Line
            End Select
            
        Case TARGET_BACKCOLOR_ODD
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    UI_SetGridColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    UI_SetGridColor = RGB(250, 238, 136)
                Case Else   'COLOR_TYPE_READONLY, COLOR_TYPE_NORMAL
                    UI_SetGridColor = RGB(236, 238, 248)   'Next Line
            End Select
            
        Case TARGET_BACKCOLOR_COLHEAD
            UI_SetGridColor = RGB(209, 214, 240)
            
        Case TARGET_BACKCOLOR_TABLEHEAD
            UI_SetGridColor = RGB(190, 197, 231)
            
        Case TARGET_BACKCOLOR
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    UI_SetGridColor = RGB(255, 255, 255)
                Case COLOR_TYPE_SELECTED
                    UI_SetGridColor = RGB(250, 238, 136)
                Case COLOR_TYPE_FORECAST
                    UI_SetGridColor = RGB(&HD7, &HDD, &H91)
                Case COLOR_TYPE_FROZEN
                    UI_SetGridColor = RGB(&HAE, &HD4, &HFF)
                Case COLOR_TYPE_BUCKET3
                    UI_SetGridColor = RGB(&HF1, &HDB, &HF3)
                Case COLOR_TYPE_BUCKET4
                    UI_SetGridColor = RGB(&HFF, &HE4, &HB0)
                Case Else
                    UI_SetGridColor = RGB(255, 255, 255)   'White
                
            End Select
            
        Case TARGET_FORECOLOR
            Select Case p_ColorType
                Case COLOR_TYPE_EDITABLE
                    UI_SetGridColor = RGB(80, 80, 80)   'Normal
                    
                Case COLOR_TYPE_INACTIVE
                    'UI_SetGridColor = RGB(80, 80, 80)
                    UI_SetGridColor = RGB(191, 191, 191)
                    
                Case COLOR_TYPE_PRIMARY
                    UI_SetGridColor = RGB(&H50, &H60, &HB8)
                    
                Case COLOR_TYPE_SECONDARY
                    UI_SetGridColor = RGB(&H94, &H30, &HA3)
                    
                Case COLOR_TYPE_SELECTED    '1      'Selected/Emphasized
                    UI_SetGridColor = RGB(255, 230, 0)
                
                Case Else
                    'Normal
                    UI_SetGridColor = RGB(80, 80, 80)
            
            End Select
            
        Case Else
            UI_SetGridColor = RGB(252, 0, 255)  'Hot Pink, since this is not acceptable
    
    End Select

    If p_ColorType = COLOR_TYPE_INACTIVE Then UI_SetGridColor = RGB(191, 191, 191)

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(UI_SetGridColor)", Err.Description
End Function


