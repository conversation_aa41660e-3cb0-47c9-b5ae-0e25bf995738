Attribute VB_Name = "AIMUtil"
Option Explicit
Private Type Prod_Const_RCD
    LcId As String
    Item As String
    RSOQ As Double
    Min As Double
    Max As Double
    Order_Qty As Double
    XDock_YN As String
    OP_Split_Pct As Double
    Item_Split_Pct As Double
    Order_Point As Double
    Min_Fullfill_Only As Boolean
    Over_Or_Below_Min As Double
    Repeat_Count As Long
   ' Full_filled As Boolean
    Multi_location As Boolean
    Order_FullFilled As Boolean
    POSeqID As Double
    ConvFactor As Integer
End Type

Private Type XDock_RCD
    LcId As String
    Item As String
    RSOQ As Double
End Type

Private Type Temp_RCD
    LcId As String
    Item As String
    Sum_Order_Point As Double
    Sum_RSOQ As Double
    Item_Count As Double
End Type

Private Type Prod_Const_WithOut_XDock_RCD
    LcId As String
    Item As String
    OrderPoint As Double
    RSOQ As Double
End Type

Private Type Item_RSOQSum_RCD
    Item As String
    Sum_RSOQ As Double
    Min As Double
    Max As Double
End Type



Private l_Prod_Const() As Prod_Const_RCD
Private l_XDock() As XDock_RCD
Private l_Temp() As Temp_RCD
Private l_Prod_Const_WithOut_XDock() As Prod_Const_WithOut_XDock_RCD

Public Function Method25(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, Simdata As SIMULATION_RCD, _
                        ApplyTrend As Boolean, ApplySeasonsIndex As String)
On Error GoTo ErrorHandler
    
    Dim DS001_013 As Double
    Dim DS014_026 As Double
    
    Dim Fcst As Double
    Dim i As Integer
    Dim Pd As Integer
    Dim si As Integer
    
    Dim SI001_013 As Double
    Dim SI014_026 As Double
    
    Dim Trend As Double
    
    'MovAvg(1,26) + Trend + SeaAdj

    Meth24_25BldMovAvgTable Simdata, MA

    'Initialize Trend
    Trend = MA.Trend(0)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        'Initialize Seasonality Indices
        SI001_013 = 0
        SI014_026 = 0
        
        'Calculate Seasonality Indices
        For i = Pd + 1 To Pd + 13
            SI001_013 = SI001_013 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i))
            SI014_026 = SI014_026 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 13))
        Next i
        
        SI001_013 = Max(SI001_013 / 13, Simdata.OP.MinBi)
        SI014_026 = Max(SI014_026 / 13, Simdata.OP.MinBi)
        
        DS001_013 = MA.MA001_013(Pd) / SI001_013
        DS014_026 = MA.MA014_026(Pd) / SI014_026
           
        si = GetAbsPd(Pd)
        si = GetHsPeriod(Simdata.DM.EndPeriod, si)
        
        
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) > 0 Then
            Fcst = (MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA014_026(Pd) * MA.Pd014_026(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd))
        End If
        
        If ApplySeasonsIndex = "Y" Then
             'Adjust Forecat for Trend and Seasonality
            If ApplyTrend = True Then
                Fcst = (Fcst + Trend) * Simdata.Sa.Bi(si)
            Else
                Fcst = Fcst * Simdata.Sa.Bi(si)
            End If
        
            AccumFcstStatistics Pd, Fcst, Trend, Simdata.Sa.Bi(si), FcstResults, Simdata
        Else
            If ApplyTrend = True Then
                Fcst = (Fcst + Trend)
            End If
           
            AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
        End If
        
                    
    Next Pd
    
    
    'Check for minimum number of periods
    'If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 52 Then
    '    FcstResults.FcstStatus = FS_INACTIVE
    'End If

    Method25 = FcstResults.N

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method24)"
     f_HandleErr , , , "AIMUtil::method25", Now, gDRJobError, True, Err

End Function


Public Function Method24(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, Simdata As SIMULATION_RCD, _
                        ApplyTrend As Boolean, ApplySeasonsIndex As String)
On Error GoTo ErrorHandler
    
    Dim DS001_013 As Double
    Dim DS014_026 As Double
    Dim DS027_039 As Double
    Dim DS040_052 As Double
    
    Dim Fcst As Double
    Dim i As Integer
    Dim Pd As Integer
    Dim si As Integer
    
    Dim SI001_013 As Double
    Dim SI014_026 As Double
    Dim SI027_039 As Double
    Dim SI040_052 As Double
    
    Dim Trend As Double
    
    Meth24_25BldMovAvgTable Simdata, MA
    
    
    'MovAvg(1,52) + Trend + SeaAdj

    'Initialize Trend
    Trend = MA.Trend(0)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        'Initialize Seasonality Indices
        SI001_013 = 0
        SI014_026 = 0
        SI027_039 = 0
        SI040_052 = 0
        
        'Calculate Seasonality Indices
        For i = Pd + 1 To Pd + 13
            SI001_013 = SI001_013 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i))
            SI014_026 = SI014_026 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 13))
            SI027_039 = SI027_039 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 26))
            SI040_052 = SI040_052 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 39))
        Next i
        
        SI001_013 = Max(SI001_013 / 13, Simdata.OP.MinBi)
        SI014_026 = Max(SI014_026 / 13, Simdata.OP.MinBi)
        SI027_039 = Max(SI027_039 / 13, Simdata.OP.MinBi)
        SI040_052 = Max(SI040_052 / 13, Simdata.OP.MinBi)
        
        DS001_013 = MA.MA001_013(Pd) / SI001_013
        DS014_026 = MA.MA014_026(Pd) / SI014_026
        DS027_039 = MA.MA027_039(Pd) / SI027_039
        DS040_052 = MA.MA040_052(Pd) / SI040_052
           
        si = GetAbsPd(Pd)
        si = GetHsPeriod(Simdata.DM.EndPeriod, si)
        
        
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) > 0 Then
            Fcst = (MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA014_026(Pd) * MA.Pd014_026(Pd) _
                + MA.MA027_039(Pd) * MA.Pd027_039(Pd) _
                + MA.MA040_052(Pd) * MA.Pd040_052(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd))
                
        End If
        
        If ApplySeasonsIndex = "Y" Then
             'Adjust Forecat for Trend and Seasonality
            If ApplyTrend = True Then
                Fcst = (Fcst + Trend) * Simdata.Sa.Bi(si)
            Else
                Fcst = Fcst * Simdata.Sa.Bi(si)
            End If
        
            AccumFcstStatistics Pd, Fcst, Trend, Simdata.Sa.Bi(si), FcstResults, Simdata
        Else
            If ApplyTrend = True Then
                Fcst = (Fcst + Trend)
            End If
           
            AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
        End If
        
                    
    Next Pd
        
    
    'Check for minimum number of periods
    'If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 52 Then
    '    FcstResults.FcstStatus = FS_INACTIVE
    'End If

    Method24 = FcstResults.N

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method24)"
     f_HandleErr , , , "AIMUtil::method24", Now, gDRJobError, True, Err

End Function

Public Function BldForecastSQL(VnId As String, Assort As String, _
    LcId As String, Item As String, _
    Class1 As String, Class2 As String, _
    Class3 As String, Class4 As String, _
    ById As String, ItemStatus As String, _
    SAVersion As Integer, _
    Optional LDivision As String, Optional LRegion As String, _
    Optional LStatus As String, Optional LUserDefined As String _
) As String
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim WhrStmt As String
    
    'Trim$ the parameters
    VnId = Trim$(VnId)
    Assort = Trim$(Assort)
    LcId = Trim$(LcId)
    Item = Trim$(Item)
    ItemStatus = Trim$(ItemStatus)
    Class1 = Trim$(Class1)
    Class2 = Trim$(Class2)
    Class3 = Trim$(Class3)
    Class4 = Trim$(Class4)
    ById = Trim$(ById)
    LDivision = Trim$(LDivision)
    LRegion = Trim$(LRegion)
    LStatus = Trim$(LStatus)
    LUserDefined = Trim$(LUserDefined)
    
    'Initialize SQL Statement
    strSQL = "SELECT Item.Item, Item.Lcid, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, "
    strSQL = strSQL & "Item.VelCode, Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag, "
    strSQL = strSQL & "Item.SaId, Item.PmId, Item.Weight, Item.Cube, Item.Price, Item.Cost, Item.UOM, "
    strSQL = strSQL & "Item.ConvFactor, Item.BuyingUOM, Item.Oh, Item.Oo, Item.ComStk, "
    strSQL = strSQL & "Item.BkOrder, Item.BkComStk, Item.FcstMethod, Item.FcstDemand, Item.UserFcst, "
    strSQL = strSQL & "Item.UserFcstExpDate, Item.MAE, Item.Trend, Item.FcstUpdCyc, Item.PlnTT, "
    strSQL = strSQL & "Item.Accum_Lt, Item.ReviewTime, Item.OrderPt, Item.OrderQty, Item.SafetyStock, "
    strSQL = strSQL & "Item.FcstRT, Item.FcstLT, Item.Fcst_Month, Item.Fcst_Quarter, Item.Fcst_Year, "
    strSQL = strSQL & "AIMOptions.HiTrndL, "
    strSQL = strSQL & "AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, "
    strSQL = strSQL & "AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, "
    strSQL = strSQL & "AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, "
    strSQL = strSQL & "AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, "
    strSQL = strSQL & "AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, "
    strSQL = strSQL & "AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, "
    strSQL = strSQL & "AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, "
    strSQL = strSQL & "AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, "
    strSQL = strSQL & "AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, "
    strSQL = strSQL & "AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, "
    strSQL = strSQL & "AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, "
    strSQL = strSQL & "AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, "
    strSQL = strSQL & "AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, "
    strSQL = strSQL & "AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42, "
    strSQL = strSQL & "AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, "
    strSQL = strSQL & "AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, "
    strSQL = strSQL & "AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, "
    strSQL = strSQL & "AIMSeasons.BI52, "
    strSQL = strSQL & "AIMVendors.VName, "
    strSQL = strSQL & "AIMLocations.DmdScalingFactor, AIMLocations.ScalingEffUntil, "
    strSQL = strSQL & "PmStatus = isnull(AIMPromotions.PmStatus, 0), "
    strSQL = strSQL & "PmStartDate = isnull(AIMPromotions.PmStartDate, '01/01/1990'), "
    strSQL = strSQL & "PmEndDate = isnull(AIMPromotions.PmEndDate, '01/01/1990'), "
    strSQL = strSQL & "PmAdj01 = isnull(AIMPromotions.PmAdj01,1), PmAdj02 = isnull(AIMPromotions.PmAdj02,1), "
    strSQL = strSQL & "PmAdj03 = isnull(AIMPromotions.PmAdj03,1), PmAdj04 = isnull(AIMPromotions.PmAdj04,1), "
    strSQL = strSQL & "PmAdj05 = isnull(AIMPromotions.PmAdj05,1), PmAdj06 = isnull(AIMPromotions.PmAdj06,1), "
    strSQL = strSQL & "PmAdj07 = isnull(AIMPromotions.PmAdj07,1), PmAdj08 = isnull(AIMPromotions.PmAdj08,1), "
    strSQL = strSQL & "PmAdj09 = isnull(AIMPromotions.PmAdj09,1), PmAdj10 = isnull(AIMPromotions.PmAdj10,1), "
    strSQL = strSQL & "PmAdj11 = isnull(AIMPromotions.PmAdj11,1), PmAdj12 = isnull(AIMPromotions.PmAdj12,1), "
    strSQL = strSQL & "PmAdj13 = isnull(AIMPromotions.PmAdj13,1), PmAdj14 = isnull(AIMPromotions.PmAdj14,1), "
    strSQL = strSQL & "PmAdj15 = isnull(AIMPromotions.PmAdj15,1), PmAdj16 = isnull(AIMPromotions.PmAdj16,1), "
    strSQL = strSQL & "PmAdj17 = isnull(AIMPromotions.PmAdj17,1), PmAdj18 = isnull(AIMPromotions.PmAdj18,1), "
    strSQL = strSQL & "PmAdj19 = isnull(AIMPromotions.PmAdj19,1), PmAdj20 = isnull(AIMPromotions.PmAdj20,1), "
    strSQL = strSQL & "PmAdj21 = isnull(AIMPromotions.PmAdj21,1), PmAdj22 = isnull(AIMPromotions.PmAdj22,1), "
    strSQL = strSQL & "PmAdj23 = isnull(AIMPromotions.PmAdj23,1), PmAdj24 = isnull(AIMPromotions.PmAdj24,1), "
    strSQL = strSQL & "PmAdj25 = isnull(AIMPromotions.PmAdj25,1), PmAdj26 = isnull(AIMPromotions.PmAdj26,1), "
    strSQL = strSQL & "PmAdj27 = isnull(AIMPromotions.PmAdj27,1), PmAdj28 = isnull(AIMPromotions.PmAdj28,1), "
    strSQL = strSQL & "PmAdj29 = isnull(AIMPromotions.PmAdj29,1), PmAdj30 = isnull(AIMPromotions.PmAdj30,1), "
    strSQL = strSQL & "PmAdj31 = isnull(AIMPromotions.PmAdj31,1), PmAdj32 = isnull(AIMPromotions.PmAdj32,1), "
    strSQL = strSQL & "PmAdj33 = isnull(AIMPromotions.PmAdj33,1), PmAdj34 = isnull(AIMPromotions.PmAdj34,1), "
    strSQL = strSQL & "PmAdj35 = isnull(AIMPromotions.PmAdj35,1), PmAdj36 = isnull(AIMPromotions.PmAdj36,1), "
    strSQL = strSQL & "PmAdj37 = isnull(AIMPromotions.PmAdj37,1), PmAdj38 = isnull(AIMPromotions.PmAdj38,1), "
    strSQL = strSQL & "PmAdj39 = isnull(AIMPromotions.PmAdj39,1), PmAdj40 = isnull(AIMPromotions.PmAdj40,1), "
    strSQL = strSQL & "PmAdj41 = isnull(AIMPromotions.PmAdj41,1), PmAdj42 = isnull(AIMPromotions.PmAdj42,1), "
    strSQL = strSQL & "PmAdj43 = isnull(AIMPromotions.PmAdj43,1), PmAdj44 = isnull(AIMPromotions.PmAdj44,1), "
    strSQL = strSQL & "PmAdj45 = isnull(AIMPromotions.PmAdj45,1), PmAdj46 = isnull(AIMPromotions.PmAdj46,1), "
    strSQL = strSQL & "PmAdj47 = isnull(AIMPromotions.PmAdj47,1), PmAdj48 = isnull(AIMPromotions.PmAdj48,1), "
    strSQL = strSQL & "PmAdj49 = isnull(AIMPromotions.PmAdj49,1), PmAdj50 = isnull(AIMPromotions.PmAdj50,1), "
    strSQL = strSQL & "PmAdj51 = isnull(AIMPromotions.PmAdj51,1), PmAdj52 = isnull(AIMPromotions.PmAdj52,1), "
    strSQL = strSQL & "AIMMethods.ApplySeasonsIndex , AIMMethods.ApplyTrend, "
    strSQL = strSQL & "Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, Item.IntSafetyStock, Item.IsIntermittent, "
    strSQL = strSQL & "Item.Mean_NZ, Item.StdDev_NZ, Item.ReplenCost2, Item.SSAdj, Item.ZSStock, Item.LTVFact, "
    strSQL = strSQL & "Item.Dser, "
    strSQL = strSQL & "RevCycles.NextDateTime, RevCycles.RevFreq, RevCycles.RevInterval, RevCycles.RevSunday, "
    strSQL = strSQL & "RevCycles.RevMonday, RevCycles.RevTuesday, RevCycles.RevWednesday, RevCycles.RevThursday, "
    strSQL = strSQL & "RevCycles.RevFriday, RevCycles.RevSaturday, RevCycles.WeekQualifier, RevCycles.RevStartDate, "
    strSQL = strSQL & "RevCycles.RevEndDate, RevCycles.InitBuyPct, RevCycles.InitRevDate, RevCycles.ReviewTime, "
    strSQL = strSQL & "AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.HiMadP, AIMOptions.LoMadP, "
    strSQL = strSQL & "AIMOptions.MadExK, "
    strSQL = strSQL & "AIMLocations.ReplenCost, "
    strSQL = strSQL & "Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02, Item.BkQty03, "
    strSQL = strSQL & "Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, Item.BkQty06, "
    strSQL = strSQL & "Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08, Item.BkQty09, "
    strSQL = strSQL & "Item.BkCost09 , Item.BkQty10, Item.BkCost10, "
    strSQL = strSQL & "AIMLocations.StkDate, " & "AIMLocations.DemandSource, "
    strSQL = strSQL & "Item.VelCode,Item.Class1,Item.Class2,Item.Class3,Item.Class4, "
    'Osama 10/08/2002 Applying the Buyer Strategy to Net Req - Start
    strSQL = strSQL & "Item.BuyStrat, Item.UserMin, Item.UserMax, Item.DIFlag, AIMOptions.DIMADP,ItStatus.DmdUpd "
    'Osama 10/08/2002 Applying the Buyer Strategy to Net Req - End
    strSQL = strSQL & "From Item "
    strSQL = strSQL & "INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId "
    strSQL = strSQL & "INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId "
        strSQL = strSQL & "AND AIMSeasons.SAVersion = " + Format(SAVersion, "##0") + " "
    strSQL = strSQL & "INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat "
    strSQL = strSQL & "INNER JOIN AIMLocations on Item.Lcid = AIMLocations.Lcid "
    strSQL = strSQL & "INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId "
        strSQL = strSQL & "AND Item.Assort = AIMVendors.Assort "
    strSQL = strSQL & "INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle "
    strSQL = strSQL & "LEFT OUTER JOIN AIMPromotions ON Item.PmId = AIMPromotions.PmId "
    strSQL = strSQL & "INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodId "
    
    'Add Selection Criteria
    If VnId = "" _
    And Assort = "" _
    And LcId = "" _
    And Item = "" _
    And ItemStatus = "" _
    And Class1 = "" _
    And Class2 = "" _
    And Class3 = "" _
    And Class4 = "" _
    And ById = "" _
    And LDivision = "" _
    And LRegion = "" _
    And LStatus = "" _
    And LUserDefined = "" _
    Then
     '   WhrStmt = "WHERE ItStatus.DmdUpd = N'Y' "
    Else
        If VnId <> "" Then
            WhrStmt = "WHERE Item.VnId = N'" & VnId & "' "
        End If
        
        If Assort <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.Assort = N'" & Assort & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.Assort = N'" & Assort & "' "
            End If
        End If
        
        If LcId <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.LcId = N'" & LcId & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.LcId = N'" & LcId & "' "
            End If
        End If
        
        If Item <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.Item = N'" & Item & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.Item = N'" & Item & "' "
            End If
        End If
        
        If ItemStatus <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.ItStat = N'" & ItemStatus & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.ItStat = N'" & ItemStatus & "' "
            End If
        End If
        
        If Class1 <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.Class1 = N'" & Class1 & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.Class1 = N'" & Class1 & "' "
            End If
        End If
        
        If Class2 <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.Class2 = N'" & Class2 & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.Class2 = N'" & Class2 & "' "
            End If
        End If
        
        If Class3 <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.Class3 = N'" & Class3 & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.Class3 = N'" & Class3 & "' "
            End If
        End If
        
        If Class4 <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.Class4 = N'" & Class4 & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.Class4 = N'" & Class4 & "' "
            End If
        End If
        
        If ById <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE Item.ById = N'" & ById & "' "
            Else
                WhrStmt = WhrStmt & "AND Item.ById = N'" & ById & "' "
            End If
        End If
        
        If LDivision <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE AIMLocations.LDivision = N'" & LDivision & "' "
            Else
                WhrStmt = WhrStmt & "AND AIMLocations.LDivision = N'" & LDivision & "' "
            End If
        End If
        
        If LRegion <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE AIMLocations.LRegion = N'" & LRegion & "' "
            Else
                WhrStmt = WhrStmt & "AND AIMLocations.LRegion = N'" & LRegion & "' "
            End If
        End If
        
        If LStatus <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE AIMLocations.LStatus = N'" & LStatus & "' "
            Else
                WhrStmt = WhrStmt & "AND AIMLocations.LStatus = N'" & LStatus & "' "
            End If
        End If
        
        If LUserDefined <> "" Then
            If WhrStmt = "" Then
                WhrStmt = "WHERE AIMLocations.LUserDefined = N'" & LUserDefined & "' "
            Else
                WhrStmt = WhrStmt & "AND AIMLocations.LUserDefined = N'" & LUserDefined & "' "
            End If
        End If
        
        strSQL = strSQL & WhrStmt
    '    strSql = strSql & "AND ItStatus.DmdUpd = N'Y' "
    
    End If
    
    'Add Order By Clause
    BldForecastSQL = strSQL & "ORDER BY Item.Item, Item.Lcid "
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(BldForecastSQL)"
     f_HandleErr , , , "AIMUtil::BldForecastSQL", Now, gDRJobError, True, Err
End Function


Public Function CalcReviewTime(RevFreq As Integer, RevInterval As Integer, _
    RevSunday As Integer, RevMonday As Integer, RevTuesday As Integer, _
    RevWednesday As Integer, RevThursday As Integer, RevFriday As Integer, _
    RevSaturday As Integer) As Integer
On Error GoTo ErrorHandler
    
    Select Case RevFreq
    Case 0          'Dynamic
        CalcReviewTime = 1
        
    Case 1          'Daily
        CalcReviewTime = RevInterval
    
    Case 2          'Weekly
        If RevInterval = 1 Then
             CalcReviewTime = 7 \ (RevSunday + RevMonday + RevTuesday + RevWednesday _
                + RevThursday + RevFriday + RevSaturday)
                
        Else
             CalcReviewTime = RevInterval * 7
        End If
    
    Case 3, 4       'Monthly, Week-Of-Month
        CalcReviewTime = RevInterval * 30
    
    Case Else
         CalcReviewTime = 1
    
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CalcReviewTime)"
     f_HandleErr , , , "AIMUtil::CalcReviewTime", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    CalcSAProfileFit()
'
' Description:      Calculates a Fit Index used to rate the degree of
'                   fit between two seasonality profiles.
'
' Parameters:       bi      Assigned Seasonal Profile for an Item
'                   si      Inherent Seasonal Profile for an Item
'
' Returns:          Seasonality Profile Index Rating
'
' By: RES           Date: 11/06/2001
'
'**********************************************************************
Public Function CalcSAProfileFit(Bi() As Double, si() As Double)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim SumSq As Double
    
    'Calculate Summation of Absolute Variances
    For i = LBound(Bi) To UBound(Bi)
        SumSq = SumSq + (Abs(Bi(i) - si(i)) ^ 2)
    Next i
    
    'Seasonality Index Rating = Square Root of the Average Sum of Squares
    CalcSAProfileFit = (SumSq / UBound(Bi)) ^ 0.5

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CalcSAProfileFit)"
     f_HandleErr , , , "AIMUtil::CalcSAProfileFit", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    CalcSeasIndexRating()
'
' Description:      Calculates a Seasonality Rating Index used to
'                   identify items with a high degree of seasonality
'
' Parameters:       Dmd             Array of 52 Seasonality Indices
'
' Returns:          Seasonality Index Rating
'
' By: RES           Date: 10/15/2000
'
'
'**********************************************************************
Public Function CalcSeasIndexRating(Dmd() As Double) As Double
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim SumSq As Double
    
    'Calculate Summation of Absolute Variances
    'from an expected seasonality index value of 1
    For i = LBound(Dmd) To UBound(Dmd)
        SumSq = SumSq + (Abs(1 - Dmd(i)) ^ 2)
    Next i
    
    'Seasonality Index Rating = Square Root of the Average Sum of Squares
    CalcSeasIndexRating = (SumSq / UBound(Dmd)) ^ 0.5

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CalcSeasIndexRating)"
     f_HandleErr , , , "AIMUtil::CalcSeasIndexRating", Now, gDRJobError, True, Err
End Function


Function NZ_StdDev(Dmd() As Double, StartPd As Integer, EndPd As Integer, Mean As Double, ZeroCount As Integer)
On Error GoTo ErrorHandler

    Dim N As Integer
    Dim Pd As Integer
    Dim SumSq As Double
    Dim SumX As Double
    
    'Initialize variables
    ZeroCount = 0
    Mean = 0
    
    'Accumulate totals
    For Pd = StartPd To EndPd
        If Dmd(Pd) > 0 Then
            SumX = SumX + Dmd(Pd)
            SumSq = SumSq + (Dmd(Pd) ^ 2)
            N = N + 1
        Else
            ZeroCount = ZeroCount + 1
        End If
        
    Next Pd
    
    'Calculate Average
    If N > 0 Then
        Mean = SumX / N
    End If
    
    'Calculate Standard Deviation
    If N > 1 Then
        NZ_StdDev = Sqr((SumSq - ((SumX ^ 2) / N)) / (N - 1))
    Else
        NZ_StdDev = 0
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(NZ_StdDev)"
     f_HandleErr , , , "AIMUtil::NZ_StdDev", Now, gDRJobError, True, Err
End Function



Function ParsePath(strPath As String, _
                   lngPart As opgParsePath) As String
On Error GoTo ErrorHandler
                   
    ' This procedure takes a file path and returns
    ' the path (everything but the file name), the
    ' file name, the drive letter, or the file extension,
    ' depending on which constant was passed in.
    Dim lngPos              As Long
    Dim strPart             As String
    Dim blnIncludesFile     As Boolean
    
    ' Check that this is a file path.
    ' Find the last path separator.
    lngPos = InStrRev(strPath, "\")
    ' Determine whether portion of string after last backslash
    ' contains a period.
    blnIncludesFile = InStrRev(strPath, ".") > lngPos
    
    If lngPos > 0 Then
        Select Case lngPart
            ' Return file name.
            Case opgParsePath.FILE_ONLY
                If blnIncludesFile Then
                    strPart = Right$(strPath, Len(strPath) - lngPos)
                Else
                    strPart = ""
                End If
            
            ' Return path.
            Case opgParsePath.PATH_ONLY
                If blnIncludesFile Then
                    strPart = Left$(strPath, lngPos)
                Else
                    strPart = strPath
                End If
            
            ' Return drive.
            Case opgParsePath.DRIVE_ONLY
                strPart = Left$(strPath, 3)
            
            ' Return file extension.
            Case opgParsePath.FILEEXT_ONLY
                If blnIncludesFile Then
                    ' Take three characters after period.
                    strPart = Mid(strPath, InStrRev(strPath, ".") + 1, 3)
                Else
                    strPart = ""
                End If
            
            Case Else
                strPart = ""
        End Select
    End If
    ParsePath = strPart

ParsePath_End:
    Exit Function

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(ParsePath)"
     f_HandleErr , , , "AIMUtil::ParsePath", Now, gDRJobError, True, Err
End Function


Function TrimSpace(strInput As String) As String
    ' This procedure trims extra space from any part of
    ' a string.
On Error GoTo ErrorHandler

    Dim astrInput()     As String
    Dim astrText()      As String
    Dim strElement      As String
    Dim lngCount        As Long
    Dim lngIncr         As Long
    
    If Trim$(strInput) = "" Then Exit Function
    
    ' Split passed-in string.
    astrInput = Split(strInput)
    
    ' Resize second array to be same size.
    ReDim astrText(UBound(astrInput))
    
    ' Initialize counter variable for second array.
    lngIncr = LBound(astrInput)
    ' Loop through split array, looking for
    ' non-zero-length strings.
    For lngCount = LBound(astrInput) To UBound(astrInput)
        strElement = astrInput(lngCount)
        If Len(strElement) > 0 Then
            ' Store in second array.
            astrText(lngIncr) = strElement
            lngIncr = lngIncr + 1
        End If
    Next
    
    ' Resize new array.
    ReDim Preserve astrText(LBound(astrText) To lngIncr - 1)

    ' Join new array to return string.
    TrimSpace = Join(astrText)
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(TrimSpace)"
     f_HandleErr , , , "AIMUtil::TrimSpace", Now, gDRJobError, True, Err
End Function


Public Function Write_Message(Msg As String)
On Error GoTo ErrorHandler

    'Validate, since this file is common to AIMUtil, as well (which doesn't have a UI)
    If Not (gStatusBar Is Nothing) Then gStatusBar.Panels(1).Text = Msg

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Write_Message)"
     f_HandleErr , , , "AIMUtil::Write_Message", Now, gDRJobError, True, Err
End Function


Function GetCurrentById() As String
On Error GoTo ErrorHandler

    GetCurrentById = gToolBar.Tools("ID_Buyer").ComboBox.Text

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetCurrentById)"
     f_HandleErr , , , "AIMUtil::GetCurrentById", Now, gDRJobError, True, Err
End Function


Function QuickSortArray(varArray As Variant, _
                        Optional lngFirst As Long = -1, _
                        Optional lngLast As Long = -1) As Variant
On Error GoTo ErrorHandler
                            
    ' QuickSort algorithm used to sort the items
    ' in the varArray array.
    Dim lngLow      As Long
    Dim lngHigh     As Long
    Dim lngMiddle   As Long
    Dim varTempVal  As Variant
    Dim varTestVal  As Variant
    
    If lngFirst = -1 Then lngFirst = LBound(varArray)
    If lngLast = -1 Then lngLast = UBound(varArray)
        
    If lngFirst < lngLast Then
        lngMiddle = (lngFirst + lngLast) / 2
        varTestVal = varArray(lngMiddle)
        lngLow = lngFirst
        lngHigh = lngLast
        
        Do
            Do While varArray(lngLow) < varTestVal
                lngLow = lngLow + 1
            Loop
            Do While varArray(lngHigh) > varTestVal
                lngHigh = lngHigh - 1
            Loop
            If (lngLow <= lngHigh) Then
                varTempVal = varArray(lngLow)
                varArray(lngLow) = varArray(lngHigh)
                varArray(lngHigh) = varTempVal
                lngLow = lngLow + 1
                lngHigh = lngHigh - 1
            End If
        
        Loop While (lngLow <= lngHigh)
        
        If lngFirst < lngHigh Then QuickSortArray varArray, lngFirst, lngHigh
        If lngLow < lngLast Then QuickSortArray varArray, lngLow, lngLast
    
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(QuickSortArray)"
     f_HandleErr , , , "AIMUtil::QuickSortArray", Now, gDRJobError, True, Err
End Function


Function GetMaxDemand(LtDays As Integer, PctZero As Double, Mean As Double, _
    StdDev As Double, SrvLvlGoal As Double, NbrLoops As Long)
On Error GoTo ErrorHandler

    Dim AccumCount As Long
    Dim AdjNbrLoops As Long
    Dim i As Long, J As Long
    Dim DemandForPd As Boolean
    Dim Dmd As Double
    Dim Interval As Double
    Dim LtWks As Integer
    Dim MaxDemand As Double
    Dim NonZeroCount As Long
    Dim p1 As Double, p2 As Double
    'Dimension the Results Array
    Dim RsltsDist(0 To 50) As Long
    Dim ArrSize As Long
    
    'Calculate the number of weeks -- round up.
    LtWks = Round((LtDays / 7) + 0.49999, 0)
    If LtWks <= 0 Then LtWks = 1
    
    'Adjust Number of Loops
    AdjNbrLoops = NbrLoops / LtWks
    
    'Calculate the interval for the rsltsdist array
    ArrSize = (UBound(RsltsDist) - LBound(RsltsDist))
    If ArrSize <= 0 Then ArrSize = 1
    Interval = ((Mean + (3.5 * StdDev)) * LtWks) / ArrSize
    
    'Seed the Random number generator
    Randomize (CSng(Format(Now, "hmmssyyyymmdd")))
    
    For i = 1 To AdjNbrLoops
        'Initialize Demand for LTWks periods
        Dmd = 0
        DemandForPd = False
        
        For J = 1 To LtWks
            'Determine if this data point is zero or non zero
            p1 = Round(Rnd, 3)
            
            If p1 > PctZero Then
                p2 = Round(Rnd, 3)
                Dmd = Dmd + (Mean + (IIf(p2 > 0.5, SafetyFactor(p2), -1 * SafetyFactor(1 - p2)) * StdDev))
                DemandForPd = True
            
            End If
            
        Next J
        
        'Update Non-Zero Standard Deviation
        If Not DemandForPd Then
            J = 0               'Increment the zero pointer
        Else
            If Interval <= 0 Then Interval = 1
            J = Int(Dmd / Interval) + 1
            
            'The Results pointers must be between 1 and the upper bound of the results array
            J = IIf(J <= UBound(RsltsDist), J, UBound(RsltsDist))   'Check for high pointer
            J = IIf(J >= 1, J, 1)                                   'Check for low pointer
        
        End If
         
        'Update results array
        RsltsDist(J) = RsltsDist(J) + 1
        
    Next i
    
    'Determine the Maximum Demand at the specified Service Level
    NonZeroCount = AdjNbrLoops - RsltsDist(0)
    AccumCount = 0
    
    'Check for no positive demand
    If NonZeroCount <= 0 Then
        MaxDemand = 0
    Else
        For i = (LBound(RsltsDist) + 1) To UBound(RsltsDist)
            AccumCount = AccumCount + RsltsDist(i)
            If (AccumCount / NonZeroCount) >= SrvLvlGoal Then
                MaxDemand = ((i - 1) + ((SrvLvlGoal - ((AccumCount - RsltsDist(i)) / NonZeroCount)) _
                    / ((AccumCount / NonZeroCount) - ((AccumCount - RsltsDist(i)) / NonZeroCount)))) * Interval
                Exit For
            End If
        Next i
        
    End If
    
    'Scale the Maximum Demand adjusting for the difference between Lead Time Days and Lead Time Weeks
    
    GetMaxDemand = Round(MaxDemand * ((LtDays / (LtWks * 7)) ^ 0.8), 2)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetMaxDemand)"
     f_HandleErr , , , "AIMUtil::GetMaxDemand", Now, gDRJobError, True, Err
End Function


Function BldLcIdFilter(AvailList As String)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim List() As String
    Dim s As String
    
    'Clean up any white space
    AvailList = Replace(AvailList, " ", "")
    
    If Len(AvailList) = 0 Then
        BldLcIdFilter = ""
        Exit Function
    End If
    
    'Parse the list
    List = Split(AvailList, ",")
    
    'Build the filter
    s = "lcid = '" & List(0) & "' "
    For i = LBound(List) + 1 To UBound(List)
        s = s & "or lcid = '" & List(i) & "' "
    Next i
    
    BldLcIdFilter = s

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(BldLcIdFilter)"
     f_HandleErr , , , "AIMUtil::BldLcIdFilter", Now, gDRJobError, True, Err
End Function


Public Function GetDmdPd(EndYear As Integer, EndPeriod As Integer, HsYear As Integer, HsPd As Integer)
On Error GoTo ErrorHandler

    Dim StartYear As Integer
    Dim StartPeriod As Integer
    
    'Determine the Start Year and Start Period
    If EndPeriod = 52 Then
        StartYear = EndYear - 2
        StartPeriod = 1
    Else
        StartYear = EndYear - 3
        StartPeriod = EndPeriod + 1
    End If
    
    'Calculates the position in the Demand History Array based on the History Year and History Period
    'Returns a value of 1 - 156
    If HsYear = EndYear And HsPd <= EndPeriod Then
        GetDmdPd = (EndPeriod - HsPd + 1)
    ElseIf HsYear > StartYear And HsYear < EndYear Then
        GetDmdPd = ((EndYear - HsYear) * 52) + (EndPeriod - HsPd + 1)
    ElseIf HsYear = StartYear And HsPd >= StartPeriod Then
        GetDmdPd = ((EndYear - HsYear) * 52) + (EndPeriod - HsPd + 1)
    Else
        GetDmdPd = 0        'Invalid Values
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetDmdPd)"
     f_HandleErr , , , "AIMUtil::GetDmdPd", Now, gDRJobError, True, Err
End Function
Private Function GetAdjFcst(Item As String, LcId As String, FcstStartDate As Date, FcstDemand As Double, P_AIM_FcstAdj As ADODB.Command, P_rsAIM_FcstAdj As ADODB.Recordset) As Double
On Error GoTo ErrorHandler
Dim FcstAdj As Double
Dim AdjQtyOverRide As Double
Dim AdjQtyUnits As Double
Dim AdjQtyPct As Double
Dim AdjOverRide As Boolean

Dim AdjFcstDemand As Double
P_AIM_FcstAdj("@LcID") = LcId
P_AIM_FcstAdj("@Item") = Item
P_AIM_FcstAdj("@PeriodBegDate") = FcstStartDate
If f_IsRecordsetValidAndOpen(P_rsAIM_FcstAdj) Then P_rsAIM_FcstAdj.Close

P_rsAIM_FcstAdj.Open P_AIM_FcstAdj


If f_IsRecordsetOpenAndPopulated(P_rsAIM_FcstAdj) Then

    AdjQtyOverRide = P_rsAIM_FcstAdj("AdjustQty_OverRide").Value
    AdjQtyUnits = P_rsAIM_FcstAdj("AdjustQty_Units").Value
    AdjQtyPct = P_rsAIM_FcstAdj("AdjustQty_Percent").Value
    AdjOverRide = P_rsAIM_FcstAdj("AdjOverRide").Value
    
    If AdjOverRide = True Then
        GetAdjFcst = AdjQtyOverRide
    Else
        GetAdjFcst = FcstDemand * (1 + AdjQtyPct / 100#) + AdjQtyUnits
    End If

Else
    'IF there are no records in the master table
    GetAdjFcst = FcstDemand
End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetAdjFcst)"
     f_HandleErr , , , "AIMUtil::GetAdjFcst", Now, gDRJobError, True, Err
End Function


Function GetFileBaseName(PathName As String)
On Error GoTo ErrorHandler

    Dim BaseName As String
    Dim Pos As Integer
    
    'Clean up the PathName
    BaseName = Trim$(PathName)
    
    'Find the position of the last separator character.
    Pos = InStrRev(BaseName, "\")
    
    If Pos > 0 And Len(BaseName) > Pos Then
        BaseName = Mid(BaseName, Pos + 1)
    End If

    GetFileBaseName = UCase(BaseName)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetFileBaseName)"
    f_HandleErr , , , "AIMUtil::GetFileBaseName", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    GetFileNames(Path, DXType, FN() as string)
'
' Description:      Retrieves the name of all files that meet the
'                   criteria specified in path.
'
' Parameters:       Path        File Selection Criteria (includes path)
'                   DXType      Type of DX File
'                   FN()        File Name Array
'
' Returns:          Number of file names retrieved.
'
' By: RES           Date: 03/28/2000
'
'
'**********************************************************************
Function GetFileNames(Path As String, DXType As String, FN() As Variant)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim FileName As String
    Dim RtnCode As Integer

    'Size the File Name Array
    ReDim FN(0 To 100)
    
    'Get the name of the first file
    DXType = Trim$(DXType)
    
    If DXType = "" Then
        GetFileNames = 0
    End If

    'Determine the type of input file
    'Format for aim input files: LcID.SSADR.TxnType.nnnnnnnn.TXT
    'where TxnType = DD, LT, HS, SD, SS, VN
    FileName = Dir(Path & "*.SSADR." & Trim$(DXType) & ".*.TXT", vbNormal)
    
    'Iterate through PATH, caching all file names into FN()
    Do While FileName <> ""
        'Check for an overflow in the FileName array
        If i >= UBound(FN) Then
            ReDim Preserve FN(UBound(FN) + 10)
        End If
        
        FN(i) = FileName
        
        i = i + 1
        
        'Get next File Name
        FileName = Dir
    Loop

    'Sort the File Name Array
    If i > 0 Then
        ReDim Preserve FN(i - 1)
        QuickSortArray FN()
    
    End If
    
    GetFileNames = i

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetFileNames)"
     f_HandleErr , , , "AIMUtil::GetFileNames", Now, gDRJobError, True, Err
End Function

Function DxPO(ById As String, VnId As String, Assort As String, LcId As String, _
    Path As String, LogFile As String, NextPONbr As Long, _
    rsPO As ADODB.Recordset, rsPODetail As ADODB.Recordset)

On Error GoTo ErrorHandler

    Dim LastVnid As String
    Dim LastZone As String
    Dim LineSeqNbr As Long
    Dim POFile As Integer
    Dim POType As String
    Dim POFileName As String
    Dim POTempName As String
    Dim RcdCount As Long
    Dim strSQL As String
    Dim strMessage As String
    Dim strMessage1 As String
    Dim TempString As String
    
    If Not f_IsRecordsetOpenAndPopulated(rsPO) Then Exit Function
    If Not f_IsRecordsetOpenAndPopulated(rsPODetail) Then Exit Function
    
    'Write message to Log File
    Write_Log LogFile, "", 0, False, True, True, False
    strMessage = getTranslationResource("TEXTMSG80500")
    If StrComp(strMessage, "TEXTMSG80500") = 0 Then strMessage = "Purchase Order Lines -- Started."

    Write_Log LogFile, strMessage, 0, False, True, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    'Output Command Line Parameters
    strMessage = getTranslationResource("Purchase Order Number", , True)
    TempString = strMessage & " " & CStr(NextPONbr)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("Buyer ID", , True)
    TempString = strMessage & " " & CStr(ById)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("Vendor ID", , True)
    TempString = strMessage & " " & CStr(VnId)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("Location", , True)
    TempString = strMessage & " " & CStr(LcId)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("Assortment", , True)
    TempString = strMessage & " " & CStr(Assort)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("TEXTMSG80510")
    If StrComp(strMessage, "TEXTMSG80510") = 0 Then strMessage = "Log File"
    TempString = strMessage & " " & CStr(LogFile)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    'Build the PO Output File Name
    Path = Trim$(Path)
    If Right(Path, 1) <> "\" Then
        Path = Path & "\"
    End If
    
    POFileName = Trim$(LcId) & ".SSADR.PO." & Format(NextPONbr, "00000000.TXT")
    POTempName = Trim$(LcId) & ".SSADR.PO." & Format(NextPONbr, "00000000.TMP")
    LineSeqNbr = 0

    'Open the output file
    POFile = FreeFile
    On Error Resume Next
    Open Path + POTempName For Output As POFile
    'Check for Error
    If Err <> 0 Then
        strMessage = getTranslationResource("TEXTMSG80501")
        If StrComp(strMessage, "TEXTMSG80501") = 0 Then strMessage = "Error opening Purchase Order Line File --"
        strMessage1 = getTranslationResource("TEXTMSG80502")
        If StrComp(strMessage1, "TEXTMSG80502") = 0 Then strMessage1 = "! -- Job aborted."
        
        strMessage = gUserID & _
                    " -- " & Err.Number & " -- " & Err.source & " (AIMUtil.DxPO()) -- " & Err.Description & "(1)" & _
                    strMessage & " (" & Path & POTempName & ")" & strMessage1
        
        Write_Log LogFile, strMessage, 4, False, True, False, True
        Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
        
        
        'Display to the UI, as well, for good measure.
        Write_Message strMessage
        DxPO = 0
        Exit Function
    End If
    On Error GoTo ErrorHandler
    
    'Print the Purchase Order Header 1
    On Error Resume Next
    Print #POFile, _
        "H1" & vbTab; _
        CStr(NextPONbr) & vbTab; _
        Format(LineSeqNbr, "000000") & vbTab; _
        rsPO!VnId & vbTab; _
        rsPO!Assort & vbTab; _
        LcId & vbTab; _
        Format(rsPO!TransmitPO, "00") & vbTab; _
        rsPO!ShipIns & vbTab; _
        rsPO!UserInitials & vbTab; _
        rsPO!Remarks1 & vbTab; _
        rsPO!Remarks2 & vbTab; _
        rsPO!Remarks3
    'Check for Error
    If Err <> 0 Then
        strMessage = getTranslationResource("TEXTMSG80503")
        If StrComp(strMessage, "TEXTMSG80503") = 0 Then strMessage = "Error writing H1 to Purchase Order Line File --"
        strMessage1 = getTranslationResource("TEXTMSG80504")
        If StrComp(strMessage1, "TEXTMSG80504") = 0 Then strMessage1 = "! -- Job aborted."
        
        strMessage = gUserID & _
                    " -- " & Err.Number & " -- " & Err.source & " (AIMUtil.DxPO()) -- " & Err.Description & "(2)" & _
                    strMessage & " (" & Path & POTempName & ")" & strMessage1
        Write_Log LogFile, strMessage, 4, False, True, False, True
        
        Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
        'Display to the UI, as well, for good measure.
        Write_Message strMessage
        DxPO = 0
        Exit Function
    End If
    On Error GoTo ErrorHandler
    
    'Print the Purchase Order Header 2
    If rsPO("AddrOverride").Value = "Y" Then
        On Error Resume Next
        Print #POFile, _
            "H2" & vbTab; _
            CStr(NextPONbr) & vbTab; _
            Format(LineSeqNbr, "000000") & vbTab; _
            rsPO!VnId & vbTab; _
            rsPO!Assort & vbTab; _
            LcId & vbTab; _
            rsPO!VName & vbTab; _
            rsPO!VAddress1 & vbTab; _
            rsPO!VAddress2 & vbTab; _
            rsPO!VCity & vbTab; _
            rsPO!VState & vbTab; _
            rsPO!VZip
        'Check for Error
        If Err <> 0 Then
            strMessage = getTranslationResource("TEXTMSG80505")
            If StrComp(strMessage, "TEXTMSG80505") = 0 Then strMessage = "Error writing H2 to Purchase Order Line File --"
            strMessage1 = getTranslationResource("TEXTMSG80506")
            If StrComp(strMessage1, "TEXTMSG80506") = 0 Then strMessage1 = "! -- Job aborted."
            
            strMessage = gUserID & _
                    " -- " & Err.Number & " -- " & Err.source & " (AIMUtil.DxPO()) -- " & Err.Description & "(3)" & _
                    strMessage & " (" & Path & POTempName & ")" & strMessage1
            Write_Log LogFile, strMessage, 4, False, True, False, True
            Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
            
            'Display to the UI, as well, for good measure.
            Write_Message strMessage
            DxPO = 0
            Exit Function
        End If
        On Error GoTo ErrorHandler
    End If
    
    'Initialize control variables
    RcdCount = 0
    LineSeqNbr = 0
    LastVnid = ""
    LastZone = ""
    
    'Processing Loop
    rsPODetail.MoveFirst
    Do Until rsPODetail.eof
        If rsPODetail("vsoq").Value > 0 And rsPO("POStatus").Value = "R" Then
            'Increment record counter
            RcdCount = RcdCount + 1
            'Check for a change in Vendor or Zone -- If a change
            'occurs, increment the line sequence number
            If LastVnid <> rsPODetail("vnid").Value _
            Or (LastZone <> rsPODetail("zone").Value _
            And rsPO("pobyzone").Value = "Y") Then
                LineSeqNbr = LineSeqNbr + 1
        
            End If
            'Update Last Location ID
            LastVnid = rsPODetail("VnId").Value
            LastZone = rsPODetail("zone").Value
            'Determine the PO Type
            POType = rsPODetail("POType").Value
            'Output to PO Upload file
            'Out Put format for date should always be YYYYMMDD Sri June-23-03
            Print #POFile, _
                "D1" & vbTab; _
                Format(LineSeqNbr, "000000") & vbTab; _
                POType & vbTab; _
                rsPODetail!VnId & vbTab; _
                rsPODetail!Assort & vbTab; _
                rsPODetail!LcId & vbTab; _
                rsPODetail!Item & vbTab; _
                rsPODetail!ItDesc & vbTab; _
                Format(rsPODetail!Cost, "000000.0000") & vbTab; _
                Format(rsPODetail!Price, "000000.0000") & vbTab; _
                Format(rsPODetail!VSOQ, "000000000") & vbTab; _
                rsPODetail!UOM & vbTab; _
                rsPODetail!BuyingUOM & vbTab; _
                Format(rsPODetail!ConvFactor, "0000000") & vbTab; _
                Format(rsPODetail!IsDate, g_ISO_DATE_FORMAT_NODELIMITERS) & vbTab; _
                Format(rsPODetail!dudate, g_ISO_DATE_FORMAT_NODELIMITERS) & vbTab; _
                rsPO!POByZone & vbTab; _
                rsPODetail!Zone
                
            If Err <> 0 Then
                strMessage = getTranslationResource("TEXTMSG80507")
                If StrComp(strMessage, "TEXTMSG80507") = 0 Then strMessage = "Error writing D1 to Purchase Order Line File --"
                strMessage1 = getTranslationResource("TEXTMSG80508")
                If StrComp(strMessage1, "TEXTMSG80508") = 0 Then strMessage1 = "! -- Job aborted."
                
                strMessage = gUserID & _
                    " -- " & Err.Number & " -- " & Err.source & " (AIMUtil.DxPO()) -- " & Err.Description & "(4)" & _
                    strMessage & " (" & Path & POTempName & ")" & strMessage1
                
                Write_Log LogFile, strMessage, 4, False, True, False, True
                Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
                
                'Display to the UI, as well, for good measure.
                Write_Message strMessage
                DxPO = 0
                Exit Function
            End If
        End If
        
        'Update the status of the PODetail Record
        rsPODetail("OrdStatus").Value = "C"
        'rsPODetail.Update

        'Get next record
        rsPODetail.MoveNext
    Loop
    
    'Close the output file
    Close #POFile
    
    'Rename the output file/
    'Check for an empty record set
    If RcdCount = 0 Then
        Kill Path + POTempName
    Else
        On Error Resume Next
        Name Path + POTempName As Path + POFileName
        If Err.Number = 58 Then
            'File already exists
            On Error GoTo ErrorHandler
            Kill Path + POFileName
            Name Path + POTempName As Path + POFileName
        End If
    End If
    On Error GoTo ErrorHandler

    'Write message to log file
    strMessage = getTranslationResource("File Name", , True)
    TempString = TempString
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("TEXTMSG80511")
    If StrComp(strMessage, "TEXTMSG80511") = 0 Then strMessage = "PO Lines Transferred"
    
    TempString = strMessage & " " & CStr(RcdCount)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("TEXTMSG80512")
    If StrComp(strMessage, "TEXTMSG80512") = 0 Then strMessage = "Purchase Order Lines Complete."
    Write_Log LogFile, strMessage, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
    
    Write_Log LogFile, "", 0, False, True, True, False

    'Return Rows Written
    DxPO = RcdCount

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(DxPO)"
    f_HandleErr , , , "AIMUtil::DxPO", Now, gDRJobError, True, Err
End Function

Function DxPO_Ctrl(ByVal FilterOpt As String, ByVal ById As String, _
    ByVal VnId As String, ByVal Assort As String)
On Error GoTo ErrorHandler
    
    'FilterOption from Buyer Review:
    '   A: Select All released purchase orders
    '   B: Select by ById
    '   V: Select by ById, VnId, Assort
    'New FilterOptions implemented for Transfer and Orders (called from the Job Wizard)
    '   A: Select All released purchase orders  i.e Transfers and Orders
    '   P: Select Orders only
    '   T: Select Transfers only
    
    Dim strMessage As String
    Dim Cn As ADODB.Connection
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDesc As String
    
    Dim AIM_DxPO_Sp As ADODB.Command
    Dim AIM_NextPONbr_Sp As ADODB.Command
    Dim AIM_POStatistics_Sp As ADODB.Command
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim AIM_UpdateOrdStatus_Sp As ADODB.Command

    Dim rsPO As ADODB.Recordset
    Dim rsPODetail As ADODB.Recordset
    Dim rsSysCtrl As ADODB.Recordset

    Dim LcId As String
    Dim NextPONbr As Long
    Dim RtnCode As Integer
    Dim SelOption As Integer
    Dim strSQL As String
    
    'System Control Values
    Dim Dft_POStatus As String
    Dim DxPath_Out As String
    Dim DxPO_Option As String
    Dim LogFile As String
    Dim TempString As String

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        Exit Function
    End If
    
    'Define the stored procedures
    Set AIM_DxPO_Sp = New ADODB.Command
    With AIM_DxPO_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_DxPO_Sp"
        .Parameters.Refresh
    
    End With
    
    Set AIM_NextPONbr_Sp = New ADODB.Command
    With AIM_NextPONbr_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_NextPONbr_Sp"
        .Parameters.Refresh
    
    End With
    
    Set AIM_POStatistics_Sp = New ADODB.Command
    With AIM_POStatistics_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_POStatistics_Sp"
        .Parameters.Refresh
    
    End With
    
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
        
    End With
    
    Set AIM_UpdateOrdStatus_Sp = New ADODB.Command
    With AIM_UpdateOrdStatus_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_UpdateOrdStatus_Sp"
        .Parameters.Refresh
        
    End With
    
    'Define the Result Sets
    Set rsPO = New ADODB.Recordset
    With rsPO
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
        
    End With
    
    Set rsPODetail = New ADODB.Recordset
    With rsPODetail
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        '.LockType = adLockOptimistic
        .LockType = adLockBatchOptimistic
        
    End With
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    
    End With
    
    'Get the System Control Record
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
    LogFile = rsSysCtrl("LogFile").Value
    Dft_POStatus = rsSysCtrl("Dft_POStatus").Value
    DxPath_Out = rsSysCtrl("DxPath_Out").Value
    DxPO_Option = rsSysCtrl("DxPO_Option").Value
    gDateFormat = rsSysCtrl!DateFormat
    gTimeFormat = rsSysCtrl!TimeFormat

    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Write message to Log File
    Write_Log LogFile, "", 0, False, True, True, False
    strMessage = getTranslationResource("TEXTMSG80513")
    If StrComp(strMessage, "TEXTMSG80513") = 0 Then strMessage = "Send Purchase Order to Host -- Started."
    Write_Log LogFile, strMessage, 0, True, True, False, True
    Call f_HandleErr(0, strMessage, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , True)
    
    
    'Output Command Line Parameters
    strMessage = getTranslationResource("TEXTMSG80509")
    If StrComp(strMessage, "TEXTMSG80509") = 0 Then strMessage = "PO Filter Option"
    TempString = strMessage + ": " + CStr(FilterOpt)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
    
    
    strMessage = getTranslationResource("Default Purchase Order Status", , True)
    TempString = strMessage + ": " + CStr(Dft_POStatus)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
    
    strMessage = getTranslationResource("Purchase Order Generation Option", , True)
    TempString = strMessage + ": " + CStr(DxPO_Option)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
    
    strMessage = getTranslationResource("Data Interface Outbox", , True)
    TempString = strMessage + ": " + CStr(DxPath_Out)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
    
    'Validate Outbox here
    
    'Build the list of Purchase Order for the specified Buyer
    If FilterOpt = "A" And DxPO_Option = "S" Then
        'Both Transfers and Orders
        strSQL = "select * from AIMPO "
        
        SelOption = 1
    
    ElseIf FilterOpt = "A" And DxPO_Option = "L" Then
        'Both Transfers and Orders by Lcid
        strSQL = "select distinct PODetail.LcId, AIMPo.* from AIMPo "
        strSQL = strSQL & "inner join PODetail on AIMPo.ById = PODetail.ById "
        strSQL = strSQL & "and AIMPO.VnId = PODetail.VnId "
        strSQL = strSQL & "and AIMPO.Assort = PODetail.Assort "
        
        SelOption = 2
        
    ElseIf FilterOpt = "P" And DxPO_Option = "S" Then
        'All Orders but not transfers
        strSQL = "select * from AIMPO  Where POSource <> N'T'"
        
        SelOption = 1
    
    ElseIf FilterOpt = "P" And DxPO_Option = "L" Then
        'All Orders but not transfers by Lcid
        strSQL = "select distinct PODetail.LcId, AIMPo.* from AIMPo "
        strSQL = strSQL & "inner join PODetail on AIMPo.ById = PODetail.ById "
        strSQL = strSQL & "and AIMPO.VnId = PODetail.VnId "
        strSQL = strSQL & "and AIMPO.Assort = PODetail.Assort "
        strSQL = strSQL & "and AIMPO.POSource <>'T' "
        SelOption = 2
    ElseIf FilterOpt = "T" And DxPO_Option = "S" Then
        'All Transfers
        strSQL = "select * from AIMPO  Where POSource = N'T'"
        
        SelOption = 1
    
    ElseIf FilterOpt = "T" And DxPO_Option = "L" Then
        'All Transfers by Lcid
        strSQL = "select distinct PODetail.LcId, AIMPo.* from AIMPo "
        strSQL = strSQL & "inner join PODetail on AIMPo.ById = PODetail.ById "
        strSQL = strSQL & "and AIMPO.VnId = PODetail.VnId "
        strSQL = strSQL & "and AIMPO.Assort = PODetail.Assort "
        strSQL = strSQL & "and AIMPO.POSource ='T' "
        SelOption = 2
    
    ElseIf FilterOpt = "B" And DxPO_Option = "S" Then
        strSQL = "select * from AIMPO "
        strSQL = strSQL & "where ById = N'" & ById & "' "

        SelOption = 1

    ElseIf FilterOpt = "B" And DxPO_Option = "L" Then
        strSQL = "select distinct PODetail.LcId, AIMPo.* from AIMPo "
        strSQL = strSQL & "inner join PODetail on AIMPo.ById = PODetail.ById "
        strSQL = strSQL & "and AIMPO.VnId = PODetail.VnId "
        strSQL = strSQL & "and AIMPO.Assort = PODetail.Assort "
        strSQL = strSQL & "where AIMPO.ById = N'" & ById & "' "

        SelOption = 2

    ElseIf FilterOpt = "V" And DxPO_Option = "S" Then
        strSQL = "select * from AIMPO "
        strSQL = strSQL & "where ById = N'" & ById & "' "
        strSQL = strSQL & "and VnId = N'" & VnId & "' "
        strSQL = strSQL & "and Assort = N'" & Assort & "' "

        SelOption = 1

    ElseIf FilterOpt = "V" And DxPO_Option = "L" Then
        strSQL = "select distinct PODetail.LcId, AIMPo.* from AIMPo "
        strSQL = strSQL & "inner join PODetail on AIMPo.ById = PODetail.ById "
        strSQL = strSQL & "and AIMPO.VnId = PODetail.VnId "
        strSQL = strSQL & "and AIMPO.Assort = PODetail.Assort "
        strSQL = strSQL & "where AIMPo.ById = N'" & ById & "' "
        strSQL = strSQL & "and AIMPO.VnId = N'" & VnId & "' "
        strSQL = strSQL & "and AIMPO.Assort = N'" & Assort & "' "

        SelOption = 2

    End If
    
    strSQL = strSQL & "and AIMPO.POStatus = 'R' "

    'Open the Vendor Control List
    rsPO.Open strSQL, Cn
    
    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(rsPO) = False Then
        strMessage = getTranslationResource("TEXTMSG80514")
        If StrComp(strMessage, "TEXTMSG80514") = 0 Then strMessage = "Send Purchase Order to Host -- Number of files processed"
        TempString = strMessage + ": 0 "
        Write_Log LogFile, TempString, 2, True, True, False, False
        Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
        
    Else
        Do Until rsPO.eof
            'Set the Location Id
            If DxPO_Option = "S" Then
                LcId = "All"
            Else
                LcId = rsPO("LcId").Value
            End If
            
            'Get the Next PO Number
            AIM_NextPONbr_Sp.Execute
            NextPONbr = AIM_NextPONbr_Sp.Parameters(0).Value
            
            'Get the PO Detail records
            AIM_DxPO_Sp.Parameters("@VnId").Value = rsPO("VnId").Value
            AIM_DxPO_Sp.Parameters("@Assort").Value = rsPO("Assort").Value
            AIM_DxPO_Sp.Parameters("@ById").Value = rsPO("ById").Value
            AIM_DxPO_Sp.Parameters("@LcId").Value = LcId
            
            If f_IsRecordsetValidAndOpen(rsPODetail) Then
                rsPODetail.Requery
            Else
                rsPODetail.Open AIM_DxPO_Sp
            End If
            
            'Build the PO Data Interface File
            If Not (rsPODetail.BOF And rsPODetail.eof) Then
                'Update the PO Statistics Table
                With AIM_POStatistics_Sp
                    .Parameters("@SeqNbr").Value = NextPONbr
                    .Parameters("@ById").Value = rsPO("ById").Value
                    .Parameters("@VnId").Value = rsPO("VnId").Value
                    .Parameters("@Assort").Value = rsPO("Assort").Value
                    .Parameters("@LcId").Value = LcId
                    .Parameters("@SelOption").Value = SelOption
                
                    .Execute
                End With
                
                'Output the Purchase Order Upload File
                VnId = rsPO("VnId").Value
                Assort = rsPO("Assort").Value
                
                RtnCode = DxPO(ById, VnId, Assort, LcId, DxPath_Out, LogFile, NextPONbr, rsPO, rsPODetail)
                If RtnCode <= 0 Then
                    'Failed
                End If
            End If
            
            'Update the PO Detail Set
            rsPODetail.UpdateBatch
            
            'Update the POStatus in AIMPO
            With AIM_UpdateOrdStatus_Sp
                .Parameters("@ById").Value = rsPO("ById").Value
                .Parameters("@VnId").Value = rsPO("VnId").Value
                .Parameters("@Assort").Value = rsPO("Assort").Value
                .Parameters("@POStatus").Value = "C"
                
                .Execute , , adCmdStoredProc + adExecuteNoRecords
            
            End With
            
            'Get the Next Purchase Order
            rsPO.MoveNext
        Loop
        strMessage = getTranslationResource("TEXTMSG80514")
        If StrComp(strMessage, "TEXTMSG80514") = 0 Then strMessage = "Send Purchase Order to Host -- Number of files created"
        TempString = strMessage + ": " + CStr(rsPO.RecordCount)
        Write_Log LogFile, TempString, 2, True, True, False, False
        Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
    End If
    
    'Write message to log file
    strMessage = getTranslationResource("TEXTMSG80516")
    If StrComp(strMessage, "TEXTMSG80516") = 0 Then strMessage = "Send Purchase Order to Host -- Completed."
    Write_Log LogFile, strMessage, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO_Ctrl", "AIMUtil::DxPO_Ctrl", Now(), gDRJobUpdate, False, , False)
    
    Write_Log LogFile, "", 0, False, True, True, False
    
    'Wind Up
    If f_IsRecordsetValidAndOpen(rsPODetail) Then rsPODetail.Close
    Set rsPODetail = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If f_IsRecordsetValidAndOpen(rsPO) Then rsPO.Close
    Set rsPO = Nothing
    
    If Not (AIM_DxPO_Sp Is Nothing) Then Set AIM_DxPO_Sp.ActiveConnection = Nothing
    Set AIM_DxPO_Sp = Nothing
    If Not (AIM_NextPONbr_Sp Is Nothing) Then Set AIM_NextPONbr_Sp.ActiveConnection = Nothing
    Set AIM_NextPONbr_Sp = Nothing
    If Not (AIM_POStatistics_Sp Is Nothing) Then Set AIM_POStatistics_Sp.ActiveConnection = Nothing
    Set AIM_POStatistics_Sp = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    If Not (AIM_UpdateOrdStatus_Sp Is Nothing) Then Set AIM_UpdateOrdStatus_Sp.ActiveConnection = Nothing
    Set AIM_UpdateOrdStatus_Sp = Nothing
        
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    ErrNumber = Err.Number
    ErrSource = Err.source
    ErrDesc = Err.Description
    
    If f_IsRecordsetValidAndOpen(rsPODetail) Then rsPODetail.Close
    Set rsPODetail = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If f_IsRecordsetValidAndOpen(rsPO) Then rsPO.Close
    Set rsPO = Nothing
    
    If Not (AIM_DxPO_Sp Is Nothing) Then Set AIM_DxPO_Sp.ActiveConnection = Nothing
    Set AIM_DxPO_Sp = Nothing
    If Not (AIM_NextPONbr_Sp Is Nothing) Then Set AIM_NextPONbr_Sp.ActiveConnection = Nothing
    Set AIM_NextPONbr_Sp = Nothing
    If Not (AIM_POStatistics_Sp Is Nothing) Then Set AIM_POStatistics_Sp.ActiveConnection = Nothing
    Set AIM_POStatistics_Sp = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    If Not (AIM_UpdateOrdStatus_Sp Is Nothing) Then Set AIM_UpdateOrdStatus_Sp.ActiveConnection = Nothing
    Set AIM_UpdateOrdStatus_Sp = Nothing
        
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
    'Err.Raise ErrNumber, ErrSource & "(AIMUtil.bas)", ErrDesc & "(DxPO_Ctrl)"
     f_HandleErr , , , "AIMUtil::DxPO_Ctrl", Now, gDRJobError, True, Err
End Function


Public Function CalcItemSeasIndex(Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim AvgWkSales As Double
    Dim J As Integer
    Dim X As Integer
    Dim Pd As Integer
    Dim si As Double
    Dim Work(1 To 52) As Double
    
    Pd = Simdata.SeasSmoothingPds
    X = Simdata.SeasSmoothingPds \ 2
    
    Select Case Simdata.DM.OldestPeriod
        Case Is >= 156      '3 Year History
            AvgWkSales = (Simdata.DM.CpsTotal(1) + Simdata.DM.CpsTotal(2) _
                + Simdata.DM.CpsTotal(3)) / 156
            
            If AvgWkSales > 0 Then
                For Pd = LBound(Simdata.DM.si) To UBound(Simdata.DM.si)
                    Work(Pd) = (Simdata.DM.Cps(Pd) + Simdata.DM.Cps(Pd + 52) + Simdata.DM.Cps(Pd + 104)) / (3 * AvgWkSales)
                Next Pd
            
            End If
            
        Case Is >= 104      '2 Years History
            AvgWkSales = (Simdata.DM.CpsTotal(1) + Simdata.DM.CpsTotal(2)) / 104
            
            If AvgWkSales > 0 Then
                For Pd = LBound(Simdata.DM.si) To UBound(Simdata.DM.si)
                    Work(Pd) = (Simdata.DM.Cps(Pd) + Simdata.DM.Cps(Pd + 52)) / (2 * AvgWkSales)
                Next Pd
            
            End If
        
        Case Is >= 52       '1 Year History
            AvgWkSales = Simdata.DM.CpsTotal(1) / 52
            
            If AvgWkSales > 0 Then
            
                For Pd = LBound(Simdata.DM.si) To UBound(Simdata.DM.si)
                    Work(Pd) = Simdata.DM.Cps(Pd) / AvgWkSales
                Next Pd
                
            End If
    
    End Select
    
    'Smooth Results
    'Results are smoothing using values from future and past periods
    'For example: if smoothing is based on five (5) periods, the results
    'would be based on averaging each period + the next two (2) periods
    ' + the previous two (2) periods.
    For Pd = LBound(Simdata.DM.si) To UBound(Simdata.DM.si)
        si = 0
        For J = (Pd - X) To (Pd + X)
            si = si + Work(GetAbsPd(J))
        Next J
        
        Simdata.DM.si(Pd) = Round(si / ((X * 2) + 1), 2)
        
    Next Pd

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CalcItemSeasIndex)"
     f_HandleErr , , , "AIMUtil::CalcItemSeasIndex", Now, gDRJobError, True, Err
End Function


Public Function CalcPOTotals(POTotals As POTOTALS_RCD, rs As ADODB.Recordset)
On Error GoTo ErrorHandler
    
    Dim Burn As Double
    Dim BuyingUOM As String
    Dim UOM As String
    
    Dim RSOQ_Units As Double
    Dim SOQ_Units As Double
    Dim VSOQ_Units As Double
    
    'Initialize variables
    POTotals.DailyReach = 0
    POTotals.ReqDiscount = 0
    POTotals.AnnualPurchases = 0
    
    POTotals.RSOQ_Amount = 0
    POTotals.RSOQ_Cube = 0
    POTotals.RSOQ_Dollars = 0
    POTotals.RSOQ_Units = 0
    POTotals.RSOQ_Weight = 0
    
    POTotals.SOQ_Amount = 0
    POTotals.SOQ_Cube = 0
    POTotals.SOQ_Dollars = 0
    POTotals.SOQ_Units = 0
    POTotals.SOQ_Weight = 0
    
    POTotals.VSOQ_Amount = 0
    POTotals.VSOQ_Cube = 0
    POTotals.VSOQ_Dollars = 0
    POTotals.VSOQ_Units = 0
    POTotals.VSOQ_Weight = 0

    'Check for an empty record set
    If f_IsRecordsetOpenAndPopulated(rs) = False Then
        CalcPOTotals = FAIL
        Exit Function
    End If
    
    rs.MoveFirst
    Do Until rs.eof
        BuyingUOM = rs("buyinguom").Value
        UOM = rs("uom").Value
        
         If rs("Accum_LT").Value + rs("ReviewTime").Value > 0 Then
            Burn = (rs("FcstLT").Value + rs("FcstRT").Value) _
                / (rs("Accum_LT").Value + rs("ReviewTime").Value)
        Else
            Burn = 0
        End If
        
        'Debug.Print Format(Burn, "#,##0.0000")
        
        'Calculate Annual Purchases
        POTotals.AnnualPurchases = POTotals.AnnualPurchases + (52 * rs("FcstDemand").Value * rs("cost").Value)
        
        Select Case POTotals.ReachCode
            Case getTranslationResource("Cube"), "C"
                POTotals.DailyReach = POTotals.DailyReach + (Burn * rs("cube").Value)
        
            Case getTranslationResource("Dollars"), "D"
                POTotals.DailyReach = POTotals.DailyReach + (Burn * rs("cost").Value)
        
            Case getTranslationResource("Units"), "U"
                POTotals.DailyReach = POTotals.DailyReach + Burn
        
            Case getTranslationResource("Weight"), "W"
                POTotals.DailyReach = POTotals.DailyReach + (Burn * rs("weight").Value)
        
            Case Else
                POTotals.DailyReach = POTotals.DailyReach + Burn
            
        End Select

        RSOQ_Units = rs("rsoq").Value
        SOQ_Units = rs("soq").Value
        VSOQ_Units = rs("vsoq").Value
        
        POTotals.RSOQ_Cube = POTotals.RSOQ_Cube + (RSOQ_Units * rs("cube").Value)
        POTotals.SOQ_Cube = POTotals.SOQ_Cube + (SOQ_Units * rs("cube").Value)
        POTotals.VSOQ_Cube = POTotals.VSOQ_Cube + (VSOQ_Units * rs("cube").Value)
        
        POTotals.RSOQ_Dollars = POTotals.RSOQ_Dollars + (RSOQ_Units * rs("cost").Value)
        POTotals.SOQ_Dollars = POTotals.SOQ_Dollars + (SOQ_Units * rs("cost").Value)
        POTotals.VSOQ_Dollars = POTotals.VSOQ_Dollars + (VSOQ_Units * rs("cost").Value)
        
        POTotals.RSOQ_Units = POTotals.RSOQ_Units + RSOQ_Units
        POTotals.SOQ_Units = POTotals.SOQ_Units + SOQ_Units
        POTotals.VSOQ_Units = POTotals.VSOQ_Units + VSOQ_Units
        
        POTotals.RSOQ_Weight = POTotals.RSOQ_Weight + (RSOQ_Units * rs("weight").Value)
        POTotals.SOQ_Weight = POTotals.SOQ_Weight + (SOQ_Units * rs("weight").Value)
        POTotals.VSOQ_Weight = POTotals.VSOQ_Weight + (VSOQ_Units * rs("weight").Value)
              
        rs.MoveNext
    Loop
    
    'Update totals
    Select Case POTotals.ReachCode
        Case getTranslationResource("Cube"), "C"
            POTotals.RSOQ_Amount = POTotals.RSOQ_Cube
            POTotals.SOQ_Amount = POTotals.SOQ_Cube
            POTotals.VSOQ_Amount = POTotals.VSOQ_Cube
    
        Case getTranslationResource("Dollars"), "D"
            POTotals.RSOQ_Amount = POTotals.RSOQ_Dollars
            POTotals.SOQ_Amount = POTotals.SOQ_Dollars
            POTotals.VSOQ_Amount = POTotals.VSOQ_Dollars
    
        Case getTranslationResource("Units"), "U"
            POTotals.RSOQ_Amount = POTotals.RSOQ_Units
            POTotals.SOQ_Amount = POTotals.SOQ_Units
            POTotals.VSOQ_Amount = POTotals.VSOQ_Units
    
        Case getTranslationResource("Weight"), "W"
            POTotals.RSOQ_Amount = POTotals.RSOQ_Weight
            POTotals.SOQ_Amount = POTotals.SOQ_Weight
            POTotals.VSOQ_Amount = POTotals.VSOQ_Weight
    
        Case Else
            POTotals.RSOQ_Amount = POTotals.RSOQ_Units
            POTotals.SOQ_Amount = POTotals.SOQ_Units
            POTotals.VSOQ_Amount = POTotals.VSOQ_Units
    
    End Select

    CalcPOTotals = SUCCEED
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CalcPOTotals)"
     f_HandleErr , , , "AIMUtil::CalcPOTotals", Now, gDRJobError, True, Err
End Function


Function GetAbsPd(Pd As Integer) As Integer
On Error GoTo ErrorHandler

    Dim X As Integer
    
    X = Pd
    Do Until X >= 1 And X <= 52
    
        Select Case X
            Case Is > 52
                X = X - 52
            Case Is < 1
                X = X + 52
        End Select
        
    Loop
    
    GetAbsPd = X
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetAbsPd)"
     f_HandleErr , , , "AIMUtil::GetAbsPd", Now, gDRJobError, True, Err
End Function


'***********************************************************************
' Function Name:    GetNextRevDate
'
' Description:      Calculates the next Review Date for an Item
'
' Parameters:       ReviewDate          Date the item was reviewed
'                   LastSchReview       Last Scheduled Review
'                   RevFreq             Review Frequency
'                   RevInterval         Review Interval
'                   RevSunday           Review on Sundays ?
'                   RevMonday           Review on Mondays ?
'                   RevTuesday          Review on Tuesdays ?
'                   RevWednesday        Review on Wednesdays ?
'                   RevThursday         Review on Thursdays ?
'                   RevFriday           Review on Fridays ?
'                   RevSaturday         Review on Saturdays ?
'
' Returns:          N/A
'
' By: RES           Date: 05/01/2001
'
'***********************************************************************
Function GetNextRevDate(ReviewDate As Date, LastSchReview As Date, _
    Optional RevFreq As Integer = 0, _
    Optional RevInterval As Integer = 1, _
    Optional RevSunday As Integer = 0, _
    Optional RevMonday As Integer = 0, _
    Optional RevTuesday As Integer = 0, _
    Optional RevWednesday As Integer = 0, _
    Optional RevThursday As Integer = 0, _
    Optional RevFriday As Integer = 0, _
    Optional RevSaturday As Integer = 0, _
    Optional WeekQualifier As Integer = 0, _
    Optional RevStartDate As Date = "01/01/1990", _
    Optional RevEndDate As Date = "12/31/9999", _
    Optional InitBuyPct As Double = 1, _
    Optional InitRevDate As Date = "01/01/1990", _
    Optional ReviewTime As Integer = 1) As Date
On Error GoTo ErrorHandler
    
    Dim EndOfMonth As Date
    Dim IntervalTest As Integer
    Dim LastWeek As Boolean 'bit
    Dim StartofMonth As Date
    Dim TrialRvDate As Date
    Dim WeekOfMonth As Integer
    
    'Initilaize [Trial Review Date] to [lastschreview]
    TrialRvDate = LastSchReview
    
    Do Until TrialRvDate > ReviewDate
        Select Case RevFreq
            Case 0                  'Dynamic
                TrialRvDate = DateAdd("d", 1, TrialRvDate)
    
            Case 1                  'Daily
                TrialRvDate = DateAdd("d", RevInterval, TrialRvDate)
    
            Case 2                  'Weekly
                'Check for no day selected
                If Not (RevSunday = 1 Or RevMonday = 1 Or RevTuesday = 1 _
                Or RevWednesday = 1 Or RevThursday = 1 Or RevFriday = 1 _
                Or RevSaturday = 1) Then
                    Exit Do
                End If
                
                Do
                    'Increment date by one day
                    TrialRvDate = DateAdd("d", 1, TrialRvDate)
                    'Test for Weekly Interval
                    IntervalTest = (DateDiff("d", RevStartDate, TrialRvDate) / 7) Mod RevInterval
                    If Weekday(TrialRvDate) = 1 And RevSunday = 1 And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 2 And RevMonday = 1 And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 3 And RevTuesday = 1 And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 4 And RevWednesday = 1 And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 5 And RevThursday = 1 And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 6 And RevFriday = 1 And IntervalTest = 0 Then Exit Do
                    If Weekday(TrialRvDate) = 7 And RevSaturday = 1 And IntervalTest = 0 Then Exit Do
                Loop
    
            Case 3      'Monthly
                TrialRvDate = DateAdd("m", RevInterval, TrialRvDate)
    
            Case 4      'Week of Month
                Do
                    TrialRvDate = DateAdd("d", 1, TrialRvDate)
                    'Test for Week of the Month
                    StartofMonth = DateSerial(Year(TrialRvDate), Month(TrialRvDate), 1)
                    EndOfMonth = DateAdd("m", 1, StartofMonth) - 1
                    WeekOfMonth = Int(DateDiff("d", StartofMonth, TrialRvDate) \ 7) + 1
                    
                    'Is this the last week ???
                    If DateDiff("d", TrialRvDate, EndOfMonth) \ 7 = 0 Then
                        LastWeek = True
                    Else
                        LastWeek = False
                    End If
                
                    If Weekday(TrialRvDate) = 1 And RevSunday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 2 And RevMonday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 3 And RevTuesday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 4 And RevWednesday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 5 And RevThursday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 6 And RevFriday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                    If Weekday(TrialRvDate) = 7 And RevSaturday = 1 _
                        And (WeekOfMonth = WeekQualifier Or (LastWeek And WeekQualifier = 5)) Then Exit Do
                Loop
        End Select
    Loop        'End of Control Loop

    GetNextRevDate = TrialRvDate

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetNextRevDate)"
     f_HandleErr , , , "AIMUtil::GetNextRevDate", Now, gDRJobError, True, Err
End Function


Public Function GetReachCodeDesc(ReachCode As String)
On Error GoTo ErrorHandler

     Select Case ReachCode
        Case "C"
            GetReachCodeDesc = getTranslationResource("Cube")
        
        Case "D"
            GetReachCodeDesc = getTranslationResource("Dollars")
        
        Case "U"
            GetReachCodeDesc = getTranslationResource("Units")
        
        Case "W"
            GetReachCodeDesc = getTranslationResource("Weight")
        
        Case "N"
            GetReachCodeDesc = getTranslationResource("None")
        
        Case Else
            GetReachCodeDesc = getTranslationResource("None")
    
    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetReachCodeDesc)"
    f_HandleErr , , , "AIMUtil::GetReachCodeDesc", Now, gDRJobError, True, Err
End Function


Public Function GetReqDiscount()

End Function

Public Function PackRound(RSOQ As Double, UOM As String, ConvFactor As Double, _
    BuyingUOM As String, PackRounding As String, IMin As Long, IMax As Long)
On Error GoTo ErrorHandler

    Dim PackQty As Double
    
    'Check for an invalid conversion factor
    If ConvFactor <= 0 Or Trim$(BuyingUOM) = "" Or RSOQ <= 0 Then
        BuyingUOM = UOM
        PackRound = Round(RSOQ, 0)
        'Check if it exceed imax or less than imin
        If RSOQ > IMax And IMax > 0 Then
            RSOQ = IMax
        End If
        If RSOQ < IMin Then
            RSOQ = IMin
        End If
        Exit Function ' exit since the value send is invalid
    End If
    
    'Restrict order quantity based on IMIN and IMAX
    If RSOQ > IMax And IMax > 0 Then
        RSOQ = IMax
        PackRounding = "D"
    End If
        
    If RSOQ < IMin Then
        RSOQ = IMin
        PackRounding = "U"
    End If
    
    PackQty = RSOQ / ConvFactor
    
    Select Case PackRounding
        Case "D"
            PackQty = Int(PackQty)
        Case "U"
            PackQty = Round(PackQty + 0.5, 0)
        Case "R"
            PackQty = Round(PackQty, 0)
        Case Else
            PackQty = Round(PackQty, 0)
    End Select
    
    'Zero pack size is not allowed
    If PackQty = 0 Then
        PackQty = 1
    End If
    
    PackRound = PackQty * ConvFactor

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(PackRound)"
     f_HandleErr , , , "AIMUtil::PackRound", Now, gDRJobError, True, Err
End Function


Public Function SIMData_Init(Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Pd As Integer

    'Initialize Item Data
    Simdata.It.LcId = ""
    Simdata.It.Item = ""
    Simdata.It.ItDesc = ""
    Simdata.It.ItStat = ""
    Simdata.It.ActDate = "01/01/1999"
    Simdata.It.InActDate = "12/31/9999"
    Simdata.It.OptionId = ""
    Simdata.It.Class1 = ""
    Simdata.It.Class2 = ""
    Simdata.It.Class3 = ""
    Simdata.It.Class4 = ""
    Simdata.It.BinLocation = ""
    Simdata.It.VelCode = ""
    Simdata.It.VnId = ""
    Simdata.It.Assort = ""
    Simdata.It.ById = ""
    Simdata.It.SaId = ""
    Simdata.It.PmId = ""
    Simdata.It.UPC = ""
    Simdata.It.Weight = 0
    Simdata.It.Cube = 0
    Simdata.It.Price = 0
    Simdata.It.Cost = 0
    Simdata.It.BkQty(1) = 0
    Simdata.It.BkCost(1) = 0
    Simdata.It.BkQty(2) = 0
    Simdata.It.BkCost(2) = 0
    Simdata.It.BkQty(3) = 0
    Simdata.It.BkCost(3) = 0
    Simdata.It.BkQty(4) = 0
    Simdata.It.BkCost(4) = 0
    Simdata.It.BkQty(5) = 0
    Simdata.It.BkCost(5) = 0
    Simdata.It.BkQty(6) = 0
    Simdata.It.BkCost(6) = 0
    Simdata.It.BkQty(7) = 0
    Simdata.It.BkCost(7) = 0
    Simdata.It.BkQty(8) = 0
    Simdata.It.BkCost(8) = 0
    Simdata.It.BkQty(9) = 0
    Simdata.It.BkCost(9) = 0
    Simdata.It.BkQty(10) = 0
    Simdata.It.BkCost(10) = 0
    Simdata.It.UOM = ""
    Simdata.It.ConvFactor = 1
    Simdata.It.BuyingUOM = ""
    Simdata.It.ReplenCost2 = 0
    Simdata.It.Oh = 0
    Simdata.It.Oo = 0
    Simdata.It.ComStk = 0
    Simdata.It.BkOrder = 0
    Simdata.It.BkComStk = 0
    Simdata.It.LeadTime = 0
    Simdata.It.PackRounding = ""
    Simdata.It.IMin = 0
    Simdata.It.IMax = 0
    Simdata.It.CStock = 0
    Simdata.It.SSAdj = 0
    Simdata.It.UserMin = 0
    Simdata.It.UserMax = 0
    Simdata.It.UserMethod = 0
    Simdata.It.FcstMethod = 0
    Simdata.It.FcstDemand = 0
    Simdata.It.UserFcst = 0
    Simdata.It.UserFcstExpDate = "01/01/1990"
    Simdata.It.MAE = 0
    Simdata.It.MSE = 0
    Simdata.It.Trend = 0
    Simdata.It.Mean_NZ = 0
    Simdata.It.StdDev_NZ = 0
    Simdata.It.IntSafetyStock = 0
    Simdata.It.IsIntermittent = "N"
    Simdata.It.LtvFact = 0
    Simdata.It.FcstCycles = 0
    Simdata.It.ZeroCount = 0
    Simdata.It.DIFlag = "N"
    Simdata.It.DmdFilterFlag = "N"
    Simdata.It.TrkSignalFlag = "N"
    Simdata.It.UserDemandFlag = "N"
    Simdata.It.ByPassPct = 0
    Simdata.It.FcstUpdCyc = 0
    Simdata.It.PlnTT = ""
    Simdata.It.ZOPSw = "N"
    Simdata.It.OUTLSw = "N"
    Simdata.It.ZSStock = "N"
    Simdata.It.DSer = 0.95
    Simdata.It.Freeze_BuyStrat = "N"
    Simdata.It.Freeze_ById = "N"
    Simdata.It.Freeze_LeadTime = "N"
    Simdata.It.Freeze_OptionID = "N"
    Simdata.It.Freeze_DSer = "N"
    Simdata.It.OldItem = ""
    Simdata.It.Accum_Lt = 0
    Simdata.It.ReviewTime = 0
    Simdata.It.OrderPt = 0
    Simdata.It.OrderQty = 0
    Simdata.It.SafetyStock = 0
    Simdata.It.FcstRT = 0
    Simdata.It.FcstLT = 0
    Simdata.It.Fcst_Month = 0
    Simdata.It.Fcst_Quarter = 0
    Simdata.It.Fcst_Year = 0
    Simdata.It.FcstDate = "01/01/1990"
    Simdata.It.VC_Amt = ""
    Simdata.It.VC_Units_Ranking = 0
    Simdata.It.VC_Amt_Ranking = 0
    Simdata.It.VC_Date = "01/01/1990"
    Simdata.It.VelCode_Prev = ""
    Simdata.It.VC_Amt_Prev = ""

'    'Initialize Location Data
'    SimData.LC.PutAwayDays = 0
'    SimData.LC.KFactor1 = 0
'    SimData.LC.ReplenCost = 0
'    SimData.LC.DemandSource = "S"
'    SimData.LC.StkDate = "01/01/1990"
'    SimData.LC.LookBackPds = 0
'    SimData.LC.Last_FcstUpdCyc = 0

    'Initialize Options Data
    Simdata.OP.OpDesc = ""
    Simdata.OP.IDFL = 0
    Simdata.OP.BypIDFL = "N"
    Simdata.OP.CADL = 0
    Simdata.OP.HiDFL = 0
    Simdata.OP.LoDFL = 0
    Simdata.OP.NDFL = 0
    Simdata.OP.DDMAP = 0
    Simdata.OP.DAMP = 0
    Simdata.OP.MinSmk = 0
    Simdata.OP.MaxSmk = 0
    Simdata.OP.IntSmk = 0
    Simdata.OP.TSL = 0
    Simdata.OP.TSSmK = 0
    Simdata.OP.BypDDU = "N"
    Simdata.OP.ApplyDmdFilter = "N"
    Simdata.OP.BypZSl = "N"
    Simdata.OP.BypTFP = "N"
    Simdata.OP.BypZOH = "N"
    Simdata.OP.BypBef = 0
    Simdata.OP.BypAft = 0
    Simdata.OP.MinBi = 0.1
    Simdata.OP.hipct = 0
    Simdata.OP.LoPct = 0
    Simdata.OP.HiMADP = 0
    Simdata.OP.LoMADP = 0
    Simdata.OP.MADSmk = 0
    Simdata.OP.MADExK = 0
    Simdata.OP.TrnSmk = 0
    Simdata.OP.DIDDL = 0
    Simdata.OP.DITrnDL = 0
    Simdata.OP.DITrps = 0
    Simdata.OP.DIMADP = 0
    Simdata.OP.hitrndl = 0
    Simdata.OP.FilterSmk = 0
    Simdata.OP.LumpyFilterPct = 0
    Simdata.OP.Dft_TurnHigh = 0
    Simdata.OP.Dft_TurnLow = 0

    'Initialize Seasonality Data
    For Pd = LBound(Simdata.Sa.Bi) To UBound(Simdata.Sa.Bi)
        Simdata.Sa.Bi(Pd) = 1
    Next Pd
    
    'Initialize Promotions Data
    Simdata.PM.PmDesc = ""
    Simdata.PM.PmStatus = 0
    Simdata.PM.PmStartDate = "01/01/1990"
    Simdata.PM.PmEndDate = "01/01/1990"
    
    'Initialize the Promotions Indices
    For Pd = LBound(Simdata.PM.PmAdj) To UBound(Simdata.PM.PmAdj)
        Simdata.PM.PmAdj(Pd) = 1
    Next Pd

    'Initialize Demand Data
    Simdata.DM.BaseDate = Date
    
    For Pd = LBound(Simdata.DM.Cps) To UBound(Simdata.DM.Cps)
        Simdata.DM.Cps(Pd) = 0
        Simdata.DM.dps(Pd) = 0
        Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION
    Next Pd
    
    For Pd = LBound(Simdata.DM.CpsTotal) To UBound(Simdata.DM.CpsTotal)
        Simdata.DM.CpsTotal(Pd) = 0
        Simdata.DM.DpsTotal(Pd) = 0
    Next Pd
    ReDim Preserve Simdata.KIDetail(1 To 1)
    ReDim Preserve Simdata.KI.LT(0 To 0)
    ReDim Preserve Simdata.CIDetail(1 To 1)
    ReDim Preserve Simdata.CI.LT(0 To 0)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(SIMData_Init)"
     f_HandleErr , , , "AIMUtil::SIMData_Init", Now, gDRJobError, True, Err
End Function


Public Function VendorSizing(rs As ADODB.Recordset, POTotals As POTOTALS_RCD, _
    ReachCode As String, Vn_Min As Double, MinRchPct As Double, Vn_Best As Double, _
    BestRchPct As Double, ReOrderPtOpt As Boolean, ApplyReachPct As Boolean, _
    OUTLOpt As Boolean, AddlDays As Double, KFactor As Double, MinROAI As Double, _
    ReleasedOpt As Boolean, ReduceOpt As Boolean)
On Error GoTo ErrorHandler
    
    'Return Values:
    '
    ' 0: Failed Apply Reach Percentages Goal
    ' 1: Sizing unncessary; SOQ Amount exceed buying goal
    ' 2: No data to size
    ' 3: Sizing successful
    ' 4: Invalid Reach Code
    
    Dim BuyGoal As Double
    Dim Filter As Variant
    Dim LoopCount As Integer
    Dim NewFilter As String
    Dim RtnCode As Integer
    Dim Sort As String
    Dim VSOQ As Double
    Dim AnnPurchases As Double
    
    'ROAI Variables
    Dim ExtraMonths As Double
    Dim RegOrderMonths As Double
    
    'Check for an invalid reach code
    If ReachCode = "N" Then
        VendorSizing = 4
        Exit Function
    End If
    
    'Initialize variables
    AddlDays = 0
    
    'Save Filter and Sort Options
    Filter = rs.Filter
    Sort = rs.Sort
    
    'Set the filter based on the Reorder Point Option and OUTL Option
    If Not ReleasedOpt And ReOrderPtOpt And OUTLOpt Then
        NewFilter = "RSOQ > 0 "
    ElseIf Not ReleasedOpt And Not ReOrderPtOpt And OUTLOpt Then
        NewFilter = "OUTLFlag = 'N' "
    ElseIf Not ReleasedOpt And ReOrderPtOpt And Not OUTLOpt Then
        NewFilter = "RSOQ > 0 "
    ElseIf Not ReleasedOpt And Not ReOrderPtOpt And Not OUTLOpt Then
        NewFilter = ""
    ElseIf ReleasedOpt And ReOrderPtOpt And OUTLOpt Then
        NewFilter = "RSOQ > 0 AND OrdStatus = 'R'"
    ElseIf ReleasedOpt And Not ReOrderPtOpt And OUTLOpt Then
        NewFilter = "OUTLFlag = 'N' AND OrdStatus = 'R'"
    ElseIf ReleasedOpt And ReOrderPtOpt And Not OUTLOpt Then
        NewFilter = "RSOQ > 0 AND OrdStatus = 'R'"
    ElseIf ReleasedOpt And Not ReOrderPtOpt And Not OUTLOpt Then
        NewFilter = "OrdStatus = 'R'"
    End If
    
    rs.Filter = NewFilter
    
    'Sort the record set by Raw Suggested Order Quantity (RSOQ) descending
    'This ensures that items with an RSOQ size first and prevents packsizing
    'of items with an RSOQ = 0 from consuming the Buying Goal
    rs.Sort = "rsoq desc, fcstdemand desc "
    
    'Check for a lack of data
    If f_IsRecordsetOpenAndPopulated(rs) = False Then
        rs.Filter = Filter
        rs.Sort = Sort
        VendorSizing = 2
        Exit Function
    End If

    'Calculate Totals
    POTotals.ReachCode = ReachCode
    RtnCode = CalcPOTotals(POTotals, rs)
    
    'Determine the Buying Target; Either the Vendor Minimum or Best Buy Percentage
    BuyGoal = 0
    
    If POTotals.SOQ_Amount > (MinRchPct * Vn_Min) / 100 Then
        BuyGoal = Vn_Min
    End If
    
    If POTotals.SOQ_Amount > (BestRchPct * Vn_Best) / 100 Then
        BuyGoal = Vn_Best
    End If
    
    'If the BuyGoal failed the reach tests and the Apply Reach Percentages Option is
    'not set; set the buy goal to the vendor minimum
    If BuyGoal = 0 And Not ApplyReachPct And Vn_Min > 0 Then
        BuyGoal = Vn_Min
    ElseIf BuyGoal = 0 And Not ApplyReachPct And Vn_Min <= 0 Then
        BuyGoal = Vn_Best
    ElseIf BuyGoal = 0 And ApplyReachPct Then
        rs.Filter = Filter
        rs.Sort = Sort
        VendorSizing = 0
        Exit Function
    End If
    
    'If the Suggest Order Quantity Amount >= Buy Goal; no processing is required
    If POTotals.SOQ_Amount >= BuyGoal And Not ReduceOpt Then
        rs.Filter = Filter
        rs.Sort = Sort
        VendorSizing = 1
        Exit Function
    End If
    
    'Check for a Daily Reach = 0 to avoid divide by zero errors
    If POTotals.DailyReach <= 0 Then
        rs.Filter = Filter
        rs.Sort = Sort
        VendorSizing = 0
        Exit Function
    End If
    
    'Calculate the number of additional days needed
    AddlDays = (BuyGoal - POTotals.RSOQ_Amount) / POTotals.DailyReach
    
    'Initialize Loop Variables
    LoopCount = 0
    POTotals.VSOQ_Amount = 0
    
    Do Until POTotals.VSOQ_Amount >= BuyGoal
        'Increment the loop counter
        LoopCount = LoopCount + 1
        
        'Don't do this forever
        If LoopCount > 100 Then
            Exit Do
        End If
        
        'Initialize VSOQ Totals
        POTotals.VSOQ_Amount = 0
        POTotals.VSOQ_Cube = 0
        POTotals.VSOQ_Dollars = 0
        POTotals.VSOQ_Units = 0
        POTotals.VSOQ_Weight = 0
        
        rs.MoveFirst
        
        Do Until rs.eof
            'Calculate New VSOQ
             If rs("Accum_LT").Value + rs("ReviewTime").Value > 0 Then
                VSOQ = rs("rsoq").Value _
                    + (AddlDays * (rs("FcstLt").Value + rs("FcstRT").Value) _
                    / (rs("Accum_LT").Value + rs("ReviewTime").Value))
            
            Else
                VSOQ = rs("rsoq").Value
                
            End If
                    
            'Packround if the VSOQ is positive
            If VSOQ > 0 Then
                VSOQ = PackRound(VSOQ, rs("uom").Value, _
                    rs("convfactor").Value, rs("buyinguom").Value, _
                    rs("packrounding").Value, rs("imin").Value, rs("imax").Value)
                    
            End If
                
            'Enough ?
            If POTotals.VSOQ_Amount >= BuyGoal Then
                rs("vsoq").Value = 0
                rs.Update
                
            Else
                'Accumulate Totals
                POTotals.VSOQ_Cube = POTotals.VSOQ_Cube + (VSOQ * rs("cube").Value)
                POTotals.VSOQ_Dollars = POTotals.VSOQ_Dollars + (VSOQ * rs("cost").Value)
                POTotals.VSOQ_Units = POTotals.VSOQ_Units + VSOQ
                POTotals.VSOQ_Weight = POTotals.VSOQ_Weight + (VSOQ * rs("weight").Value)
                        
                'Set the goal
                Select Case POTotals.ReachCode
                    Case "C"
                        POTotals.VSOQ_Amount = POTotals.VSOQ_Cube
                    Case "D"
                        POTotals.VSOQ_Amount = POTotals.VSOQ_Dollars
                    Case "U"
                        POTotals.VSOQ_Amount = POTotals.VSOQ_Units
                    Case "W"
                        POTotals.VSOQ_Amount = POTotals.VSOQ_Weight
                    Case Else
                        POTotals.VSOQ_Amount = POTotals.VSOQ_Units
                End Select
    
                'Update the SOQ/VSOQ
                'If VSOQ > 0 and SOQ = 0 then set them equal
                'This feature allows the system to identify lines that
                'have been maintained by the buyer; ensuring they are
                'displayed when the buyer selected the "Positive SOQ" option.
                If VSOQ > 0 And rs("SOQ").Value = 0 Then
                    rs("SOQ").Value = VSOQ
                End If
                rs("vsoq").Value = VSOQ
                
                rs.Update
                
            End If
            
            rs.MoveNext
        
        Loop
        
        'Try Again
        AddlDays = AddlDays + 0.1
        
    Loop
    
    'Calculate the Discount required to meet the Minimum Return on Additional
    'Investment (MinROAI).
    ' Check for potential Divide-by-zero error
    AnnPurchases = IIf(POTotals.AnnualPurchases <= 0, 1, POTotals.AnnualPurchases)
    
    RegOrderMonths = (POTotals.SOQ_Dollars / AnnPurchases) * 12
    ExtraMonths = ((POTotals.VSOQ_Dollars / AnnPurchases) * 12) - RegOrderMonths
    
    POTotals.ReqDiscount = (ExtraMonths * ((KFactor / 100) + (MinROAI / 100))) _
        / (24 + (KFactor / 100) * (ExtraMonths + RegOrderMonths) _
        + (MinROAI / 100) * (ExtraMonths + RegOrderMonths))
    
    'Reset recordset filter
    rs.Filter = Filter
    rs.Sort = Sort
    
    'Return success
    VendorSizing = 3

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(VendorSizing)"
     f_HandleErr , , , "AIMUtil::VendorSizing", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    Write_Log((LogFile, Message, IndentPositions, LineBefore, LineAfter, Dashes)
'
' Description:      Writes a message to the Log File.
'
' Parameters:       LogFile         Path/File for log file
'                   Message         Message
'                   IndentPositions Number of positions to indent message
'                   LineBefore      Print line before message
'                   LineAfter       Print line after message
'                   Dashes          Print dashed line rather than blank line
'                   DateTimeStamp   Print Date/Time Stamp on Line
'
' Returns:          SUCCEED or FAIL
'
' By: RES           Date: 07/20/96
'
'
'***********************************************************************
Function Write_Log(LogFile As String, Message As String, _
    IndentPositions As Integer, LineBefore As Boolean, _
    LineAfter As Boolean, Dashes As Boolean, _
    DateTimeStamp As Boolean)
On Error GoTo ErrorHandler

    Dim FileSystemObject As Object
    Dim FileToWrite As Object
    Dim strFileText As String
    Dim strMessage As String
    Dim FileFormat As Long
    
    Dim DashLine As String
    
    Const FOR_READING = 1
    Const FOR_WRITING = 2
    Const FOR_APPENDING = 8
    
    Const TRISTATE_FALSE = 0
    Const TRISTATE_TRUE = -1
    Const TRISTATE_USEDEFAULT = -2
    
    If (gWantToLogToDB <> True) Then
        Exit Function
    End If
    
    
    'Set default return value
    Write_Log = FAIL
    
    'Determine format in which to write the file
    If gLocaleID = 0 Then gLocaleID = g_LOCALEID_EN_US
    
    Select Case gLocaleID
    Case g_LOCALEID_ZH_TW ' Traditional Chinese
        FileFormat = TRISTATE_TRUE
    
    Case g_LOCALEID_JA ' Japan
        FileFormat = TRISTATE_TRUE
    
    Case g_LOCALEID_KO 'Korea UserLCID
        FileFormat = TRISTATE_TRUE
    
    Case g_LOCALEID_ZH_CN ' Simplified Chinese
        FileFormat = TRISTATE_TRUE
    
    Case Else   ' The other countries
        FileFormat = TRISTATE_USEDEFAULT

    End Select
    
    If Trim$(LogFile) = "" Then
        LogFile = App.Path + "\" + App.EXEName + ".Log"
    End If
    
    'Create the File System object
    Set FileSystemObject = CreateObject("Scripting.FileSystemObject")
    Set FileToWrite = FileSystemObject.OpenTextFile(LogFile, FOR_APPENDING, True, FileFormat)
    'Initialize the Print Line
    If Dashes Then
        DashLine = String(72, "-")
    Else
        DashLine = String(72, " ")
    End If
    
    If Message <> "" Then
        If DateTimeStamp Then
            If gDateFormat <> "" Then
                Message = Space(IndentPositions) & Format(Now, (gDateFormat & " " & gTimeFormat)) & Space(2) & Message
            Else
                Message = Space(IndentPositions) & Now & Space(2) & Message
            End If
        Else
            Message = Space(IndentPositions) & Message
        End If
    End If
    
    If LineBefore Then
        FileToWrite.writeline DashLine
    End If
    
    FileToWrite.writeline Message
    
    If LineAfter Then
        FileToWrite.writeline DashLine
    End If
    
    
    Write_Log = SUCCEED
                    
    'Clean Up
    FileToWrite.Close
    Set FileToWrite = Nothing
    Set FileSystemObject = Nothing
    
Exit Function
ErrorHandler:
    Set FileToWrite = Nothing
    Set FileSystemObject = Nothing
    Write_Log = FAIL
    If Err.Number = 76 Then
        'Path not found -- give logfile name for ref.
        Err.source = "(OpenFileForAppending)"
        Err.Description = Err.Description & "-- '" & LogFile & "'"
    End If
    'Err.Raise Err.Number, Err.source & "(Write_Log)", Err.Description
    f_HandleErr , , , "AIMUtil::Write_Log", Now, gDRJobError, True, Err
End Function

Public Function GetHsPeriod(EndPeriod As Integer, Pd As Integer) As Integer
On Error GoTo ErrorHandler

    Dim RtnPd As Integer
    
    'Assigned a pointer to the Seasonality Array
    RtnPd = EndPeriod - Pd + 1
    
    Do Until RtnPd > 0
        RtnPd = RtnPd + 52
    Loop
    
    GetHsPeriod = RtnPd
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetHsPeriod)"
     f_HandleErr , , , "AIMUtil::GetHsPeriod", Now, gDRJobError, True, Err
End Function


Public Function GetHsYear(EndPeriod As Integer, EndYear As Integer, Pd As Integer) As Integer
On Error GoTo ErrorHandler

    Dim RtnPd As Integer
    Dim RtnYear As Integer
    
    'Assigned a pointer to the Seasonality Array
    RtnPd = EndPeriod - Pd + 1
    RtnYear = EndYear
    
    Do Until RtnPd > 0
        RtnPd = RtnPd + 52
        RtnYear = RtnYear - 1
    Loop
    
    GetHsYear = RtnYear

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetHsYear)"
     f_HandleErr , , , "AIMUtil::GetHsYear", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    Parse(ParseLine, Delim, QuoteChars, StrArray())
'
' Description:      This function parses the Parseline and returns its
'                   elements in the StrArray.
'
' Parameters:       ParseLine       Line to be parsed
'                   Delim           element delimiters
'                   QuoteChars      Quote characters
'                                   are stripped from the ParseLine
'                   StrArray()      String Array
'
' Returns:
'
' By: RES           Date: 02/19/96
'
'
'**********************************************************************
Function Parse(ParseLine As String, delim As String, QuoteChars As String, StrArray() As String)
On Error GoTo ErrorHandler

    Dim i, J As Integer
    Dim c As String
    Dim Work1 As String
    Dim Work2 As String
    Dim WhiteSpaceFlag As Integer
    Dim WhiteSpace As String
    
    Work1 = ""
    Work2 = ""

    'Clear String Array
    For i = LBound(StrArray) To UBound(StrArray)
        StrArray(i) = ""
    
    Next i
    
    'Initialize WhiteSpace
    WhiteSpace = delim
    
    If Not InStr(WhiteSpace, Chr(32)) Then
        WhiteSpace = WhiteSpace + Chr(32)
    
    End If
    
    'Trim$ leading and trailing spaces
    ParseLine = Trim$(ParseLine)
    WhiteSpaceFlag = False
    
    'Eliminate duplicate white space, i.e. blank blank blank
    For i = 1 To Len(ParseLine)
        c = Mid$(ParseLine, i, 1)
        If InStr(WhiteSpace, c) Then
            If Not WhiteSpaceFlag Then
                Work1 = Work1 + c
                WhiteSpaceFlag = True
            End If
        
        Else
            WhiteSpaceFlag = False
            Work1 = Work1 + c
        End If
    Next i
    
    'Strip out any Quote Characters
    If QuoteChars <> "" Then
        For i = 1 To Len(Work1)
            c = Mid$(Work1, i, 1)
            If InStr(QuoteChars, c) = 0 Then
                Work2 = Work2 + c
            End If
        Next i
    Else
        Work2 = Work1
    End If
    
    'Parse the line
    J = 0
    
    For i = 1 To Len(Work2)
        c = Mid$(Work2, i, 1)
        If InStr(delim, c) Then
            J = J + 1
        Else
            'Expand string array; if required
            If J > UBound(StrArray) Then
                ReDim Preserve StrArray(0 To UBound(StrArray) + 10)
            End If
            StrArray(J) = StrArray(J) + c
        End If
    Next i

    Parse = J + 1

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Parse)"
     f_HandleErr , , , "AIMUtil::Parse", Now, gDRJobError, True, Err
End Function


Function AccumFcstStatistics(Pd As Integer, Fcst As Double, _
    Trend As Double, Bi As Double, FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim AE As Double            'Absolute Error
    Dim SE As Double            'Signed Error
    Dim MAE As Double           'Mean Absolute Error
    Dim MSE As Double           'Mean Error
    
    With FcstResults
        Select Case Pd
            Case 0          'Next Period
                'Check for an invalid Seasonality Index
                If Bi = 0 Then
                    Bi = 1
                End If
                
                'The Forecast Demand is adjusted for seasonality to provide Deseasonalized Demand for
                'Forecast Methods which utilize seasonality
                .FcstNextPd = IIf(Fcst >= 0, (Fcst / Bi), 0)
                .Trend = Trend
            
            Case Else
                'Update Demand
                .Demand(Pd) = Simdata.DM.Cps(Pd)
                 
                If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
                    .Fcst(Pd) = IIf(Fcst >= 0, Fcst, 0)
                    
                    'Update Totals
                    .N = .N + 1
                    .ByPassFlag(Pd) = False
                    
                    If Pd = Simdata.LC.LookBackPds Then
                        .Accum_Demand(Pd) = .Demand(Pd)
                    Else
                        .Accum_Demand(Pd) = .Accum_Demand(Pd + 1) + .Demand(Pd)
                    End If
                
                    'Calculate Error
                    SE = .Demand(Pd) - .Fcst(Pd)
                    AE = Abs(.Demand(Pd) - .Fcst(Pd))
                    
                    'Accumulate totals
                    .TotalForecast = .TotalForecast + .Fcst(Pd)
                    .TotalDemand = .TotalDemand + .Demand(Pd)
                    
                Else
                    If Pd < 52 Then
                        .Fcst(Pd) = .Fcst(Pd + 1)
                    Else
                        .Fcst(Pd) = 0
                    End If
                    
                    'Calculate no errors for the period
                    SE = 0
                    AE = 0
                    
                End If
            
                'Accumulate Total Demand, Error, and Absolute Error
                If Pd = Simdata.LC.LookBackPds Then
                    .Accum_E(Pd) = SE
                    .Accum_AE(Pd) = AE
                
                Else
                    .Accum_E(Pd) = .Accum_E(Pd + 1) + SE
                    .Accum_AE(Pd) = .Accum_AE(Pd + 1) + AE
                    
                End If
                    
                'Update tracking signal
                If .N > 0 Then
                    MAE = .Accum_AE(Pd) / .N
                    MSE = .Accum_E(Pd) / .N
                Else
                    MAE = 0
                    MSE = 0
                End If
                
                If MAE > 0 Then
                    .TrkSignal(Pd) = MSE / MAE
                End If
                    
        End Select
        
    End With
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(AccumFcstStatistics)"
     f_HandleErr , , , "AIMUtil::AccumFcstStatistics", Now, gDRJobError, True, Err
End Function

Public Function BldForecastSQL_New( _
    p_Cn As ADODB.Connection, _
    p_VendorID As String, p_Assortment As String, _
    p_LocationID As String, p_ItemID As String, _
    p_Class1 As String, p_Class2 As String, _
    p_Class3 As String, p_Class4 As String, _
    p_BuyerID As String, p_ItemStatus As String, _
    p_LDivision As String, p_LRegion As String, _
    p_LStatus As String, p_LUserDefined As String, _
    p_SeasVersion As Integer, _
    r_rsForecastSQL As ADODB.Recordset _
) As Integer
On Error GoTo ErrorHandler

    Dim AIM_BldForecastSQL_Sp As ADODB.Command
    Dim RtnCode As Integer
    
    'Trim$ the parameters
    p_VendorID = Trim$(p_VendorID)
    p_Assortment = Trim$(p_Assortment)
    p_LocationID = Trim$(p_LocationID)
    p_ItemID = Trim$(p_ItemID)
    p_ItemStatus = Trim$(p_ItemStatus)
    p_Class1 = Trim$(p_Class1)
    p_Class2 = Trim$(p_Class2)
    p_Class3 = Trim$(p_Class3)
    p_Class4 = Trim$(p_Class4)
    p_BuyerID = Trim$(p_BuyerID)
    p_LDivision = Trim$(p_LDivision)
    p_LRegion = Trim$(p_LRegion)
    p_LStatus = Trim$(p_LStatus)
    p_LUserDefined = Trim$(p_LUserDefined)
    
    're-Initialize the recordset
    If f_IsRecordsetValidAndOpen(r_rsForecastSQL) Then r_rsForecastSQL.Close
    If (IsObject(r_rsForecastSQL)) Then Set r_rsForecastSQL = Nothing
    Set r_rsForecastSQL = New ADODB.Recordset
    With r_rsForecastSQL
        .CursorLocation = adUseClient
        .LockType = adLockReadOnly
        .CursorType = adOpenForwardOnly
    End With
    
    'Initialize the stored procedure
    Set AIM_BldForecastSQL_Sp = New ADODB.Command
    With AIM_BldForecastSQL_Sp
        Set .ActiveConnection = p_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_BldForecastSQL_Sp"
    End With
    With AIM_BldForecastSQL_Sp.Parameters
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "RETURN", adInteger, adParamReturnValue)
        '@VnId As nvarchar(12) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@VnID", adVarWChar, adParamInput, 12, p_VendorID)
        '@Assort As nvarchar(12) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@Assort", adVarWChar, adParamInput, 12, p_Assortment)
        '@LcId As nvarchar(12) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@LcID", adVarWChar, adParamInput, 12, p_LocationID)
        '@Item As nvarchar(25) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@Item", adVarWChar, adParamInput, 25, p_ItemID)
        '@ItStat As nvarchar(1) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@ItStat", adVarWChar, adParamInput, 1, p_ItemStatus)
        '@Class1 As nvarchar(50) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@Class1", adVarWChar, adParamInput, 50, p_Class1)
        '@Class2 As nvarchar(50) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@Class2", adVarWChar, adParamInput, 50, p_Class2)
        '@Class3 As nvarchar(50) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@Class3", adVarWChar, adParamInput, 50, p_Class3)
        '@Class4 As nvarchar(50) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@Class4", adVarWChar, adParamInput, 50, p_Class4)
        '@ById As nvarchar(12) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@ByID", adVarWChar, adParamInput, 20, p_BuyerID)
        '@LStatus As nvarchar(1) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@LStatus", adVarWChar, adParamInput, 1, p_LStatus)
        '@LDivision As nvarchar(20) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@LDivision", adVarWChar, adParamInput, 20, p_LDivision)
        '@LRegion As nvarchar(20) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@LRegion", adVarWChar, adParamInput, 20, p_LRegion)
        '@LUserDefined As nvarchar(30) = '',
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@LUserDefined", adVarWChar, adParamInput, 30, p_LUserDefined)
        '@SAVersion As smallint,
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@SAVersion", adSmallInt, adParamInput, , p_SeasVersion)
        '@ReturnCode as int OUTPUT
        .Append AIM_BldForecastSQL_Sp.CreateParameter( _
            "@ReturnCode", adInteger, adParamOutput, , 0)
    End With
        
    'Return the result set
    r_rsForecastSQL.Open AIM_BldForecastSQL_Sp
    'Check connection here.
    If AIM_BldForecastSQL_Sp.Parameters(0).Value <> SUCCEED Then
        'Handle SQL Error message
        BldForecastSQL_New = FAIL
    Else
        BldForecastSQL_New = SUCCEED
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(BldForecastSQL_New)"
     f_HandleErr , , , "AIMUtil::BldForecastSQL_New", Now, gDRJobError, True, Err
End Function
Private Function Calc_ProductionConstraint(rsProd_Const As Recordset)
On Error GoTo ErrorHandler

    Dim strSQL As String
    Dim Prod_Cycle_Days As Double
    Dim Initial_Prod_Cycle_Days As Double
    Dim i As Integer
    Dim J As Integer
    Dim K As Double
    Dim L As Long
    Dim Daily_Qty As Double
    Dim Rec_Count_Prod_Const_Id As Long
    Dim Rec_Count As Double
    Dim Rec_Count_Temp As Double
    Dim Total_Rsoq As Double
    Dim Total_Item_Split_Pct As Double
    Dim Daily_Pct_Reduction As Double
    Dim Prod_Const_Id As String
    Dim POSeqID As Double
    Dim Temp_OrdQty  As Double
    Dim RtnCode As Integer
    
    If Not f_IsRecordsetOpenAndPopulated(rsProd_Const) Then Exit Function
    
    Rec_Count = rsProd_Const.RecordCount
    ReDim l_Prod_Const(1 To Rec_Count)
    ReDim l_XDock(1 To Rec_Count)
    ReDim l_Temp(1 To Rec_Count)
    ReDim l_Prod_Const_WithOut_XDock(1 To Rec_Count)
    Temp_OrdQty = 0

    For i = 1 To Rec_Count
        rsProd_Const.AbsolutePosition = i
        l_Prod_Const(i).LcId = rsProd_Const("Lcid").Value
        l_Prod_Const(i).Item = rsProd_Const("Item").Value
        l_Prod_Const(i).RSOQ = rsProd_Const("rsoq").Value
        l_Prod_Const(i).Min = rsProd_Const("minunits").Value
        l_Prod_Const(i).Max = rsProd_Const("maxunits").Value
        If l_Prod_Const(i).Max < l_Prod_Const(i).Min Then
            l_Prod_Const(i).Max = l_Prod_Const(i).Min
        End If
        'l_Prod_Const(i).Order_Point = rsProd_Const("orderpt").Value
        l_Prod_Const(i).Order_Qty = 0
        l_Prod_Const(i).Repeat_Count = 1
        l_Prod_Const(i).OP_Split_Pct = 1
        l_Prod_Const(i).XDock_YN = rsProd_Const("DropShip_XDock").Value
        l_Prod_Const(i).POSeqID = rsProd_Const("POSeqID").Value
        If l_Prod_Const(i).RSOQ > l_Prod_Const(i).Min Then
            l_Prod_Const(i).Min_Fullfill_Only = False
        Else
            l_Prod_Const(i).Min_Fullfill_Only = True
        End If
        l_Prod_Const(i).ConvFactor = rsProd_Const("ConvFactor").Value
        'If ratiotype =1 then use orderpoint to calculate percentages else if ratiotype =2 then use
        ' RSOQ for calculating the percentages so if the ratiotype =2 set the orderpoint values to RSOQ
        If rsProd_Const("RatioType").Value = "1" Then
            l_Prod_Const(i).Order_Point = rsProd_Const("orderpt").Value
        ElseIf rsProd_Const("RatioType").Value = "2" Then
            l_Prod_Const(i).Order_Point = rsProd_Const("rsoq").Value
        End If
    Next
    'All the records in the recordset should have the same Inital_Prod_Cycle_Days
    
    Prod_Cycle_Days = rsProd_Const("CycleDays").Value
    Initial_Prod_Cycle_Days = Prod_Cycle_Days
    'rsProd_Const.Close
    
    
    'Check if production constriant calculations need to be done or not
    'If the Rtncode is 1 then do  not run calculations only do sizing
    'else apply production constriant calcualtions
    
    RtnCode = Check_Min_Max(Initial_Prod_Cycle_Days)
    
    If RtnCode = 1 Then
        For i = 1 To Rec_Count
            If Trim$(l_Prod_Const(i).XDock_YN) <> "Y" Then
                Temp_OrdQty = 0
                'If prodcution constriant need not be applied then set the ordqty to RSOQ
                Temp_OrdQty = l_Prod_Const(i).RSOQ
                Temp_OrdQty = PackRound_Prod_Capacity(Temp_OrdQty, l_Prod_Const(i).ConvFactor, "D")
                l_Prod_Const(i).Order_Qty = Temp_OrdQty
                rsProd_Const.AbsolutePosition = i
                If rsProd_Const("POSeqID").Value = l_Prod_Const(i).POSeqID Then
                    rsProd_Const("VSOQ").Value = l_Prod_Const(i).Order_Qty
                    rsProd_Const("Original_VSOQ").Value = l_Prod_Const(i).Order_Qty
                End If
            End If
         Next
        rsProd_Const.Update
        Exit Function
    Else
    'Continue
    End If
    
    
    
    
    Populate_XDock_WithOut_XDock l_Prod_Const(), l_XDock(), l_Prod_Const_WithOut_XDock()
    
    Populate_Temp_Rec l_Prod_Const_WithOut_XDock(), l_Temp()
    
    Rec_Count_Temp = UBound(l_Temp)
    For i = 1 To Rec_Count_Temp
       For J = 1 To Rec_Count
            If l_Prod_Const(J).Item = l_Temp(i).Item Then
                l_Prod_Const(J).Repeat_Count = l_Temp(i).Item_Count
                l_Prod_Const(J).OP_Split_Pct = l_Prod_Const(J).Order_Point / l_Temp(i).Sum_Order_Point
                l_Prod_Const(J).Multi_location = True
                l_Prod_Const(J).Over_Or_Below_Min = l_Temp(i).Sum_RSOQ - l_Prod_Const(J).Min
                If l_Prod_Const(J).Over_Or_Below_Min > 0 Then
                    l_Prod_Const(J).Min_Fullfill_Only = False
                End If
            End If
        Next
    Next
    
    Rec_Count_Temp = UBound(l_XDock)
    For J = 1 To Rec_Count_Temp
        For i = 1 To Rec_Count
            If l_Prod_Const(i).Item = l_XDock(J).Item Then
                l_Prod_Const(i).Min = l_Prod_Const(i).Min - l_XDock(J).RSOQ
                l_Prod_Const(i).Over_Or_Below_Min = l_Prod_Const(i).Over_Or_Below_Min + l_XDock(J).RSOQ
                If l_Prod_Const(i).Over_Or_Below_Min > 0 Then
                    l_Prod_Const(i).Min_Fullfill_Only = False
                End If
            End If
        Next
    Next
    
    'Satisfy min order qty
    For i = 1 To Rec_Count
        Daily_Qty = l_Prod_Const(i).Max / Initial_Prod_Cycle_Days
        If l_Prod_Const(i).Multi_location = True Then
            If Trim$(l_Prod_Const(i).XDock_YN) = "Y" Then
                l_Prod_Const(i).Order_Qty = l_Prod_Const(i).RSOQ
                Prod_Cycle_Days = Prod_Cycle_Days - l_Prod_Const(i).Order_Qty / Daily_Qty
                l_Prod_Const(i).Order_FullFilled = True
            Else
                If l_Prod_Const(i).Order_Qty < l_Prod_Const(i).Min * l_Prod_Const(i).OP_Split_Pct Then
                    Temp_OrdQty = 0
                    Temp_OrdQty = l_Prod_Const(i).Min * l_Prod_Const(i).OP_Split_Pct
                    Temp_OrdQty = PackRound_Prod_Capacity(Temp_OrdQty, l_Prod_Const(i).ConvFactor, "U")
                    l_Prod_Const(i).Order_Qty = Temp_OrdQty
                    If l_Prod_Const(i).Order_Qty >= l_Prod_Const(i).RSOQ Or l_Prod_Const(i).Min_Fullfill_Only = True Then
                        l_Prod_Const(i).Order_FullFilled = True
                    Else
                        Total_Rsoq = Total_Rsoq + l_Prod_Const(i).RSOQ
                    End If
                    Prod_Cycle_Days = Prod_Cycle_Days - l_Prod_Const(i).Order_Qty / Daily_Qty
                Else
                    Total_Rsoq = Total_Rsoq + l_Prod_Const(i).RSOQ
                End If
            End If
        Else
            If Trim$(l_Prod_Const(i).XDock_YN) = "Y" Then
                l_Prod_Const(i).Order_Qty = l_Prod_Const(i).RSOQ
                Prod_Cycle_Days = Prod_Cycle_Days - l_Prod_Const(i).Order_Qty / Daily_Qty
                l_Prod_Const(i).Order_FullFilled = True
            Else
                If l_Prod_Const(i).Order_Qty < l_Prod_Const(i).Min Then
                    Temp_OrdQty = 0
                    Temp_OrdQty = l_Prod_Const(i).Min
                    Temp_OrdQty = PackRound_Prod_Capacity(Temp_OrdQty, l_Prod_Const(i).ConvFactor, "U")
                    l_Prod_Const(i).Order_Qty = Temp_OrdQty
                    If l_Prod_Const(i).Order_Qty >= l_Prod_Const(i).RSOQ Then
                        l_Prod_Const(i).Order_FullFilled = True
                    Else
                        Total_Rsoq = Total_Rsoq + l_Prod_Const(i).RSOQ
                    End If
                    Prod_Cycle_Days = Prod_Cycle_Days - l_Prod_Const(i).Order_Qty / Daily_Qty
                Else
                    Total_Rsoq = Total_Rsoq + l_Prod_Const(i).RSOQ
                End If
            End If
        End If
    Next
    
    'For multilocation item if the RSOQ is less then min  then we need to process them again
    'first initilize the order qty to zero
    For i = 1 To Rec_Count
        If l_Prod_Const(i).Order_FullFilled = False Then
            l_Prod_Const(i).Item_Split_Pct = l_Prod_Const(i).RSOQ / Total_Rsoq
            If Total_Item_Split_Pct + l_Prod_Const(i).Item_Split_Pct > 1# Then
                l_Prod_Const(i).Item_Split_Pct = 1# - Total_Item_Split_Pct
            End If
            Total_Item_Split_Pct = Total_Item_Split_Pct + l_Prod_Const(i).Item_Split_Pct
        End If
        If l_Prod_Const(i).Min_Fullfill_Only = True And l_Prod_Const(i).Multi_location = True And Not Trim$(l_Prod_Const(i).XDock_YN) = "Y" Then
            l_Prod_Const(i).Order_Qty = 0
        End If
    Next
    
    'Repopulate the order qty
    
    For i = 1 To Rec_Count
    Daily_Qty = l_Prod_Const(i).Max / Initial_Prod_Cycle_Days
    If l_Prod_Const(i).Min_Fullfill_Only = True And l_Prod_Const(i).Multi_location = True And Not Trim$(l_Prod_Const(i).XDock_YN) = "Y" Then
        If l_Prod_Const(i).Order_Qty < l_Prod_Const(i).RSOQ Then
            l_Prod_Const(i).Order_Qty = l_Prod_Const(i).RSOQ + Abs(l_Prod_Const(i).Over_Or_Below_Min) * l_Prod_Const(i).OP_Split_Pct
             'packround the qty
            Temp_OrdQty = 0
            Temp_OrdQty = l_Prod_Const(i).Order_Qty
            Temp_OrdQty = PackRound_Prod_Capacity(Temp_OrdQty, l_Prod_Const(i).ConvFactor, "U")
            Daily_Qty = l_Prod_Const(i).Max / Initial_Prod_Cycle_Days
            Prod_Cycle_Days = Prod_Cycle_Days - (Temp_OrdQty - l_Prod_Const(i).Order_Qty) / Daily_Qty
            l_Prod_Const(i).Order_Qty = Temp_OrdQty
        End If
    Else
    End If
    Next
    
    For J = 1 To Initial_Prod_Cycle_Days
        If Prod_Cycle_Days <= 0 Then Exit For
        'Redo the percentages
        For K = 1 To Rec_Count
            Total_Item_Split_Pct = 0#
            If l_Prod_Const(K).Order_FullFilled = False Then
                l_Prod_Const(K).Item_Split_Pct = l_Prod_Const(K).RSOQ / Total_Rsoq
                If Total_Item_Split_Pct + l_Prod_Const(K).Item_Split_Pct > 1# Then
                    l_Prod_Const(K).Item_Split_Pct = 1# - Total_Item_Split_Pct
                End If
                Total_Item_Split_Pct = Total_Item_Split_Pct + l_Prod_Const(K).Item_Split_Pct
            End If
        Next
        If Prod_Cycle_Days < 1# Then
            Daily_Pct_Reduction = Prod_Cycle_Days
        Else
            Daily_Pct_Reduction = 1#
        End If
        For i = 1 To Rec_Count
            If l_Prod_Const(i).Order_FullFilled = False Then
                Daily_Qty = l_Prod_Const(i).Max / Initial_Prod_Cycle_Days
                If l_Prod_Const(i).Order_Qty < l_Prod_Const(i).RSOQ Then
                    If l_Prod_Const(i).Order_Qty + Daily_Qty * Daily_Pct_Reduction * l_Prod_Const(i).Item_Split_Pct >= l_Prod_Const(i).RSOQ Then
                        Prod_Cycle_Days = Prod_Cycle_Days - (l_Prod_Const(i).RSOQ - l_Prod_Const(i).Order_Qty) / Daily_Qty
                        l_Prod_Const(i).Order_Qty = l_Prod_Const(i).RSOQ
                        Total_Rsoq = Total_Rsoq - l_Prod_Const(i).RSOQ
                        l_Prod_Const(i).Order_FullFilled = True
                        If Prod_Cycle_Days <= 0 Then Exit For
                    Else
                        Prod_Cycle_Days = Prod_Cycle_Days - Daily_Pct_Reduction * l_Prod_Const(i).Item_Split_Pct
                        l_Prod_Const(i).Order_Qty = l_Prod_Const(i).Order_Qty + Daily_Qty * Daily_Pct_Reduction * l_Prod_Const(i).Item_Split_Pct
                        If Prod_Cycle_Days <= 0 Then Exit For
                    End If
                End If
            End If
        Next
        If Prod_Cycle_Days <= 0 Then Exit For
    Next
    
    'packround the  ones that are above the min required
    For i = 1 To Rec_Count
    If l_Prod_Const(i).Min_Fullfill_Only = False And Not (Trim$(l_Prod_Const(i).XDock_YN) = "Y") Then
        Temp_OrdQty = 0
        Temp_OrdQty = l_Prod_Const(i).Order_Qty
        Temp_OrdQty = PackRound_Prod_Capacity(Temp_OrdQty, l_Prod_Const(i).ConvFactor, "D")
        l_Prod_Const(i).Order_Qty = Temp_OrdQty
    End If
    Next

    
    For i = 1 To Rec_Count
    rsProd_Const.AbsolutePosition = i
    If rsProd_Const("POSeqID").Value = l_Prod_Const(i).POSeqID Then
    rsProd_Const("VSOQ").Value = l_Prod_Const(i).Order_Qty
    rsProd_Const("Original_VSOQ").Value = l_Prod_Const(i).Order_Qty
    End If
    Next
    rsProd_Const.Update
    

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Calc_ProductionConstraint)"
     f_HandleErr , , , "AIMUtil::Calc_ProductionConstraint", Now, gDRJobError, True, Err
End Function
Private Function Calc_VSOQ(arg_ConstraintId() As String, Arg_rs As Recordset)
On Error GoTo ErrorHandler

    Dim Counter As Double
    Dim Record_Count As Double
    Dim Filter_Criteria As String
    Dim RtnCode As Double
    
    Record_Count = UBound(arg_ConstraintId)
    
    For Counter = 1 To Record_Count
    'For Counter = 1 To 1
        If f_IsRecordsetOpenAndPopulated(Arg_rs) Then
            Filter_Criteria = arg_ConstraintId(Counter)
            Arg_rs.Filter = ""
            Arg_rs.Filter = "constraintid ='" + Filter_Criteria + "'"
            Calc_ProductionConstraint Arg_rs
        End If
    Next

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Calc_VSOQ)"
     f_HandleErr , , , "AIMUtil::Calc_VSOQ", Now, gDRJobError, True, Err
End Function
    
Private Function PackRound_Prod_Capacity(RSOQ As Double, ConvFactor As Integer, PackRound As String) As Double

On Error GoTo ErrorHandler
Dim PackQty As Double
If ConvFactor <= 1 Then
PackRound_Prod_Capacity = RSOQ
Exit Function
End If

PackQty = RSOQ / ConvFactor
Select Case PackRound
Case "D"
    PackQty = Round(PackQty, 0)
'    PackQty = Int(PackQty)
Case "U"
    PackQty = Round(PackQty + 0.5, 0)
    
Case Else
    PackQty = Round(PackQty, 0)
End Select
PackRound_Prod_Capacity = PackQty * ConvFactor

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(PackRound_Prod_Capacity)"
     f_HandleErr , , , "AIMUtil::PackRound_Prod_Capacity", Now, gDRJobError, True, Err
End Function


Private Function Populate_XDock_WithOut_XDock(Arg_Prod_Const() As Prod_Const_RCD, Arg_XDock() As XDock_RCD, Arg_Prod_Const_WithOut_XDock() As Prod_Const_WithOut_XDock_RCD)
On Error GoTo ErrorHandler
    
    'Used to Populate the Arg_XDock with XDock only data and Arg_Prod_Const_WithOut_XDock with Non XDock data
    Dim Counter As Double
    Dim XDock_Counter As Double
    Dim WithOut_XDock_Counter As Double
    
    XDock_Counter = 0
    WithOut_XDock_Counter = 0
    For Counter = 1 To UBound(Arg_Prod_Const())
        If Trim$(Arg_Prod_Const(Counter).XDock_YN) = "Y" Then
            XDock_Counter = XDock_Counter + 1
            Arg_XDock(XDock_Counter).LcId = Arg_Prod_Const(Counter).LcId
            Arg_XDock(XDock_Counter).Item = Arg_Prod_Const(Counter).Item
            Arg_XDock(XDock_Counter).RSOQ = Arg_Prod_Const(Counter).RSOQ
        Else
            WithOut_XDock_Counter = WithOut_XDock_Counter + 1
            Arg_Prod_Const_WithOut_XDock(WithOut_XDock_Counter).LcId = Arg_Prod_Const(Counter).LcId
            Arg_Prod_Const_WithOut_XDock(WithOut_XDock_Counter).Item = Arg_Prod_Const(Counter).Item
            Arg_Prod_Const_WithOut_XDock(WithOut_XDock_Counter).OrderPoint = Arg_Prod_Const(Counter).Order_Point
            Arg_Prod_Const_WithOut_XDock(WithOut_XDock_Counter).RSOQ = Arg_Prod_Const(Counter).RSOQ
        End If
    Next
    If XDock_Counter = 0 Then
        ReDim Preserve Arg_XDock(1 To 1)
    Else
        ReDim Preserve Arg_XDock(1 To XDock_Counter)
    End If
    
    If WithOut_XDock_Counter = 0 Then
        ReDim Preserve Arg_Prod_Const_WithOut_XDock(1 To 1)
    Else
        ReDim Preserve Arg_Prod_Const_WithOut_XDock(1 To WithOut_XDock_Counter)
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Populate_XDock_WithOutXDock)"
     f_HandleErr , , , "AIMUtil::Populate_XDock_WithOutXDock", Now, gDRJobError, True, Err
End Function

Private Function Populate_DistinctConstraintID(arg_ConstraintId() As String, Arg_rs As ADODB.Recordset)
On Error GoTo ErrorHandler

    Dim Counter As Double
    Dim Record_Count As Double
    Dim DistConstId As Double
    Dim LastConstraintID As String
    
    If f_IsRecordsetOpenAndPopulated(Arg_rs) Then
        Record_Count = Arg_rs.RecordCount
        For Counter = 1 To Record_Count
        Arg_rs.AbsolutePosition = Counter
        If Counter = 1 Then
            DistConstId = 1
            LastConstraintID = Arg_rs("ConstraintID").Value
        arg_ConstraintId(DistConstId) = LastConstraintID
        Else
            If Arg_rs("constraintId").Value <> LastConstraintID Then
                LastConstraintID = Arg_rs("constraintId").Value
                DistConstId = DistConstId + 1
                arg_ConstraintId(DistConstId) = LastConstraintID
            End If
        End If
        Next
        ReDim Preserve arg_ConstraintId(1 To DistConstId)
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Populate_DistinctConstraintID)"
     f_HandleErr , , , "AIMUtil::Populate_DistinctConstraintID", Now, gDRJobError, True, Err
End Function

Private Function Populate_Temp_Rec(Arg_Prod_Const_WithOut_XDock_RCD() As Prod_Const_WithOut_XDock_RCD, _
                                    Arg_Temp_RCD() As Temp_RCD)
On Error GoTo ErrorHandler

'Used to populate Arg_Temp_RCD array with Sum_RSOQ,Sum_Order_Point,Item_count for items that are in more
'than one location other then XDock Items
    Dim Counter As Double
    Dim L_Item_Count As Double 'To count the duplicate items
    Dim L_SOQ_Sum As Double ' To calculate the SOQ sum for duplicate items
    Dim L_OP_Sum As Double  ' To calculate the Order Point sum for duplicate items
    Dim L_Temp_Count As Double
    Dim Record_Count As Double
    L_Item_Count = 1
    L_SOQ_Sum = 0
    L_OP_Sum = 0
    L_Temp_Count = 0
    
    L_SOQ_Sum = Arg_Prod_Const_WithOut_XDock_RCD(1).RSOQ
    L_OP_Sum = Arg_Prod_Const_WithOut_XDock_RCD(1).OrderPoint
    Record_Count = UBound(Arg_Prod_Const_WithOut_XDock_RCD())
    
    For Counter = 1 To Record_Count - 1
        If Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).Item = Arg_Prod_Const_WithOut_XDock_RCD(Counter).Item Then
            L_Item_Count = L_Item_Count + 1
            L_SOQ_Sum = L_SOQ_Sum + Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).RSOQ
            L_OP_Sum = L_OP_Sum + Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).OrderPoint
        Else
            If L_Item_Count > 1 Then
                L_Temp_Count = L_Temp_Count + 1
                Arg_Temp_RCD(L_Temp_Count).Item = Arg_Prod_Const_WithOut_XDock_RCD(Counter).Item
                Arg_Temp_RCD(L_Temp_Count).LcId = Arg_Prod_Const_WithOut_XDock_RCD(Counter).LcId
                Arg_Temp_RCD(L_Temp_Count).Sum_RSOQ = L_SOQ_Sum
                Arg_Temp_RCD(L_Temp_Count).Sum_Order_Point = L_OP_Sum
                Arg_Temp_RCD(L_Temp_Count).Item_Count = L_Item_Count
                L_SOQ_Sum = 0
                L_OP_Sum = 0
                L_SOQ_Sum = L_SOQ_Sum + Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).RSOQ
                L_OP_Sum = L_OP_Sum + Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).OrderPoint
                L_Item_Count = 1
            Else
                L_SOQ_Sum = 0
                L_OP_Sum = 0
                L_SOQ_Sum = L_SOQ_Sum + Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).RSOQ
                L_OP_Sum = L_OP_Sum + Arg_Prod_Const_WithOut_XDock_RCD(Counter + 1).OrderPoint
                L_Item_Count = 1
            End If
        End If
    Next
    
    If L_Item_Count > 1 Then
        L_Temp_Count = L_Temp_Count + 1
        Arg_Temp_RCD(L_Temp_Count).Item = Arg_Prod_Const_WithOut_XDock_RCD(Counter).Item
        Arg_Temp_RCD(L_Temp_Count).LcId = Arg_Prod_Const_WithOut_XDock_RCD(Counter).LcId
        Arg_Temp_RCD(L_Temp_Count).Sum_RSOQ = L_SOQ_Sum
        Arg_Temp_RCD(L_Temp_Count).Sum_Order_Point = L_OP_Sum
        Arg_Temp_RCD(L_Temp_Count).Item_Count = L_Item_Count
    End If
    If L_Temp_Count = 0 Then
    ReDim Preserve Arg_Temp_RCD(1 To 1)
    Else
    ReDim Preserve Arg_Temp_RCD(1 To L_Temp_Count)
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Populate_Temp_Rec)"
     f_HandleErr , , , "AIMUtil::Populate_Temp_Rec", Now, gDRJobError, True, Err
End Function

Private Function Check_Min_Max(ArgProdCycleDays As Double) As Integer
On Error GoTo ErrorHandler

'Used to check if production constraint calculations need tobe done or not


    Dim Counter As Double
    Dim L_Item_Count As Double 'To count the duplicate items
    Dim L_SOQ_Sum As Double ' To calculate the SOQ sum for duplicate items
    Dim L_Temp_Count As Double
    Dim Record_Count As Double
    Dim L_FullFill_Min As Boolean
    Dim L_ProdDaysConsumed As Double
    L_Item_Count = 1
    L_SOQ_Sum = 0
    L_Temp_Count = 0
    Dim l_Item_RSOQ_Sum() As Item_RSOQSum_RCD
    
    If ArgProdCycleDays = 0 Then ArgProdCycleDays = 1
    L_SOQ_Sum = l_Prod_Const(1).RSOQ
    Record_Count = UBound(l_Prod_Const())
    ReDim l_Item_RSOQ_Sum(1 To Record_Count)
    
    For Counter = 1 To Record_Count - 1
        If l_Prod_Const(Counter + 1).Item = l_Prod_Const(Counter).Item Then
            L_Item_Count = L_Item_Count + 1
            L_SOQ_Sum = L_SOQ_Sum + l_Prod_Const(Counter + 1).RSOQ

        Else
            If L_Item_Count >= 1 Then
                L_Temp_Count = L_Temp_Count + 1
                l_Item_RSOQ_Sum(L_Temp_Count).Item = l_Prod_Const(Counter).Item
                l_Item_RSOQ_Sum(L_Temp_Count).Min = l_Prod_Const(Counter).Min
                l_Item_RSOQ_Sum(L_Temp_Count).Max = l_Prod_Const(Counter).Max
                l_Item_RSOQ_Sum(L_Temp_Count).Sum_RSOQ = L_SOQ_Sum
                L_SOQ_Sum = 0
                L_SOQ_Sum = L_SOQ_Sum + l_Prod_Const(Counter + 1).RSOQ
                L_Item_Count = 1
            Else
                L_SOQ_Sum = 0
                L_SOQ_Sum = L_SOQ_Sum + l_Prod_Const(Counter + 1).RSOQ
                L_Item_Count = 1
            End If
        End If
    Next

        L_Temp_Count = L_Temp_Count + 1
        l_Item_RSOQ_Sum(L_Temp_Count).Item = l_Prod_Const(Counter).Item
        l_Item_RSOQ_Sum(L_Temp_Count).Min = l_Prod_Const(Counter).Min
        l_Item_RSOQ_Sum(L_Temp_Count).Max = l_Prod_Const(Counter).Max
        l_Item_RSOQ_Sum(L_Temp_Count).Sum_RSOQ = L_SOQ_Sum

    If L_Temp_Count = 0 Then
    ReDim Preserve l_Item_RSOQ_Sum(1 To 1)
    Else
    ReDim Preserve l_Item_RSOQ_Sum(1 To L_Temp_Count)
    End If
    
    'Check if the minimum production capacity is satisfied
    'If the RSOQ Sum for any item is less than the Min for that item or
    'If the Prodcutioncycledays consumed by the item is greater than InitalProduction
    'cycle days then we need to do sizing
    'It the requested qtys fall in between  then we need not do sizing
    L_FullFill_Min = True
    L_ProdDaysConsumed = 0
    For Counter = 1 To UBound(l_Item_RSOQ_Sum())
        If l_Item_RSOQ_Sum(Counter).Sum_RSOQ < l_Item_RSOQ_Sum(Counter).Min Then
            L_FullFill_Min = False
            Exit For
        End If
        L_ProdDaysConsumed = L_ProdDaysConsumed + l_Item_RSOQ_Sum(Counter).Sum_RSOQ * ArgProdCycleDays / l_Item_RSOQ_Sum(Counter).Max
    Next
    If L_ProdDaysConsumed > ArgProdCycleDays Or L_FullFill_Min = False Then
        Check_Min_Max = -1
    Else
    
        Check_Min_Max = 1
    End If
            
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source, Err.Description & "(Check_Min_Max)"
     f_HandleErr , , , "AIMUtil::Check_Min_Max", Now, gDRJobError, True, Err
End Function


Public Function Prod_Constraint_Sizing(arg_VnId As String, arg_Assort As String, arg_Cn As Connection)
On Error GoTo ErrorHandler

    Dim RtnCode As Integer
    Dim ConstraintID() As String
    Dim AIM_AimProductionConstraint_SP As ADODB.Command
    Dim rsAIMProductionConstraint As ADODB.Recordset
    
    Set rsAIMProductionConstraint = New ADODB.Recordset
    With rsAIMProductionConstraint
            .CursorLocation = adUseClient
            .CursorType = adOpenStatic
            .LockType = adLockOptimistic
    End With
    
    Set AIM_AimProductionConstraint_SP = New ADODB.Command
    With AIM_AimProductionConstraint_SP
        Set .ActiveConnection = arg_Cn
            .CommandType = adCmdStoredProc
            .CommandText = "AIM_AimProductionConstraint_SP"
            .Parameters.Refresh
    End With
    With AIM_AimProductionConstraint_SP
        .Parameters(0).Value = 0
        .Parameters("@VnID").Value = arg_VnId
        .Parameters("@Assort").Value = arg_Assort
    End With
    
    rsAIMProductionConstraint.Open AIM_AimProductionConstraint_SP
    
    'Put code to handle different RtnCode values
    RtnCode = AIM_AimProductionConstraint_SP(0).Value
    If RtnCode > 0 Then
        'Call the cal functions
        ReDim ConstraintID(1 To RtnCode)
        Populate_DistinctConstraintID ConstraintID, rsAIMProductionConstraint
        Calc_VSOQ ConstraintID, rsAIMProductionConstraint
        
    End If
    
CleanUp:
    If Not (AIM_AimProductionConstraint_SP Is Nothing) Then Set AIM_AimProductionConstraint_SP.ActiveConnection = Nothing
    Set AIM_AimProductionConstraint_SP = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMProductionConstraint) Then rsAIMProductionConstraint.Close
    Set rsAIMProductionConstraint = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_AimProductionConstraint_SP Is Nothing) Then Set AIM_AimProductionConstraint_SP.ActiveConnection = Nothing
    Set AIM_AimProductionConstraint_SP = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMProductionConstraint) Then rsAIMProductionConstraint.Close
    Set rsAIMProductionConstraint = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(Prod_Constraint_Sizing)"
     f_HandleErr , , , "AIMUtil::Prod_Constraint_Sizing", Now, gDRJobError, True, Err
End Function

Public Function Prod_Constraint_SizingCtrl(arg_RevCycle As String, arg_Cn As Connection)
On Error GoTo ErrorHandler

    Dim AIM_AimProductionConstraintCtrl_SP As ADODB.Command
    Dim rsAIMVendorAssortment As ADODB.Recordset
    Dim RtnCode As Integer
    Dim Loop_VnAssort As Double
    Dim Rows_VnAssort As Double
    Dim VnId As String
    Dim Assort As String
    
    'Initialize the Vendor/Assortment Record Set
    Set rsAIMVendorAssortment = New ADODB.Recordset
    With rsAIMVendorAssortment
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockOptimistic
    End With
    'Get the VendorId and Assortments to be processed
    
    'Initialize Stored Procedures
    Set AIM_AimProductionConstraintCtrl_SP = New ADODB.Command
    With AIM_AimProductionConstraintCtrl_SP
       Set .ActiveConnection = arg_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AimProductionConstraintCtrl_SP"
        .Parameters.Refresh
        .Parameters(0).Value = 0
    'we need to do this ProdcutionConstranit always
    'If FiterOpt is A then arg is ALL else if FilterOpt is R then  arg is RevCycleKey
        .Parameters("@RevCycle").Value = arg_RevCycle
    End With
    
    rsAIMVendorAssortment.Open AIM_AimProductionConstraintCtrl_SP
    
    'Put code to handle different RtnCode values
    RtnCode = AIM_AimProductionConstraintCtrl_SP(0).Value
    If f_IsRecordsetOpenAndPopulated(rsAIMVendorAssortment) Then
        Rows_VnAssort = rsAIMVendorAssortment.RecordCount
        If Rows_VnAssort > 0 Then
            For Loop_VnAssort = 1 To Rows_VnAssort
                rsAIMVendorAssortment.AbsolutePosition = Loop_VnAssort
                VnId = rsAIMVendorAssortment("VnID").Value
                Assort = rsAIMVendorAssortment("Assort").Value
                Prod_Constraint_Sizing VnId, Assort, arg_Cn
            Next
        End If
    End If
    
CleanUp:
    If Not (AIM_AimProductionConstraintCtrl_SP Is Nothing) Then Set AIM_AimProductionConstraintCtrl_SP.ActiveConnection = Nothing
    Set AIM_AimProductionConstraintCtrl_SP = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMVendorAssortment) Then rsAIMVendorAssortment.Close
    Set rsAIMVendorAssortment = Nothing

Exit Function
ErrorHandler:
    If Not (AIM_AimProductionConstraintCtrl_SP Is Nothing) Then Set AIM_AimProductionConstraintCtrl_SP.ActiveConnection = Nothing
    Set AIM_AimProductionConstraintCtrl_SP = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMVendorAssortment) Then rsAIMVendorAssortment.Close
    Set rsAIMVendorAssortment = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(Prod_Constraint_SizingCtrl)"
     f_HandleErr , , , "AIMUtil::Prod_Constraint_SizingCtrl", Now, gDRJobError, True, Err
End Function

Public Function BldMovAvgTable(Simdata As SIMULATION_RCD, _
    MA As MOVAVGWORK_RCD)
On Error GoTo ErrorHandler

    Dim Pd As Integer

    'Initialize values
    For Pd = LBound(MA.MA001_013) To UBound(MA.MA001_013)
        MA.MA001_013(Pd) = 0
        MA.MA014_026(Pd) = 0
        MA.MA027_039(Pd) = 0
        MA.MA040_052(Pd) = 0
        MA.MA053_065(Pd) = 0
        MA.MA066_078(Pd) = 0
        MA.MA079_091(Pd) = 0
        MA.MA092_104(Pd) = 0
        MA.Trend(Pd) = 0
        'MA.SaAdj(pd) = 0
    Next Pd
    
    'Build the Moving Average Work Table
    For Pd = LBound(MA.MA001_013) To UBound(MA.MA001_013)
        MA.MA001_013(Pd) = MovingAvg(Pd + 1, Pd + 13, Simdata, MA.Pd001_013(Pd))
        MA.MA014_026(Pd) = MovingAvg(Pd + 14, Pd + 26, Simdata, MA.Pd014_026(Pd))
        MA.MA027_039(Pd) = MovingAvg(Pd + 27, Pd + 39, Simdata, MA.Pd027_039(Pd))
        MA.MA040_052(Pd) = MovingAvg(Pd + 40, Pd + 52, Simdata, MA.Pd040_052(Pd))
        MA.MA053_065(Pd) = MovingAvg(Pd + 53, Pd + 65, Simdata, MA.Pd053_065(Pd))
        MA.MA066_078(Pd) = MovingAvg(Pd + 66, Pd + 78, Simdata, MA.Pd066_078(Pd))
        MA.MA079_091(Pd) = MovingAvg(Pd + 79, Pd + 91, Simdata, MA.Pd079_091(Pd))
        MA.MA092_104(Pd) = MovingAvg(Pd + 92, Pd + 104, Simdata, MA.Pd092_104(Pd))
        
        MA.Trend(Pd) = ((MA.MA001_013(Pd) _
                        + MA.MA014_026(Pd) _
                        + MA.MA027_039(Pd) _
                        + MA.MA040_052(Pd)) _
                        - (MA.MA053_065(Pd) _
                        + MA.MA066_078(Pd) _
                        + MA.MA079_091(Pd) _
                        + MA.MA092_104(Pd))) / 208
                        
'        Select Case SimData.DM.OldestPeriod
'        Case Is >= 104  '2 Years History
'            MA.SaAdj(pd) = ((MA.MA040_052(pd) + MA.MA092_104(pd)) / 2) - ((MA.MA001_013(pd) + MA.MA014_026(pd) + MA.MA027_039(pd) + MA.MA040_052(pd) _
'                + MA.MA053_065(pd) + MA.MA066_078(pd) + MA.MA079_091(pd) + MA.MA092_104(pd)) / 8)
'
'        Case Is >= 52   '1 Year History
'            MA.SaAdj(pd) = MA.MA040_052(pd) _
'                - ((MA.MA001_013(pd) + MA.MA014_026(pd) + MA.MA027_039(pd) + MA.MA040_052(pd) / 4))
'
'        Case Else
'            MA.SaAdj(pd) = 0
'
'        End Select
    Next Pd
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(BldMovAvgTable)"
     f_HandleErr , , , "AIMUtil::BldMovAvgTable", Now, gDRJobError, True, Err
End Function


Public Function BldDistRecord(Pds As Integer, DR As DISTRIBUTION_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim i, p As Integer
    
    DR.Interval = 0
    DR.Base = 999999999
    DR.Points = 0
    DR.MaxUnits = 0
    
    'Clear the Distribution Counts
    For i = LBound(DR.DistCounts) To UBound(DR.DistCounts)
        DR.DistCounts(i) = 0
    Next i
   
    'Find the Minimum and Maximum Values
    For i = 1 To Pds
        If Simdata.DM.ByPassFlag(i) = BR_NOEXCEPTION Then
            DR.Interval = Max(DR.Interval, Simdata.DM.Cps(i))
            DR.Base = Min(DR.Base, Simdata.DM.Cps(i))
            
            'Accumulate count of number of data points
            DR.Points = DR.Points + 1
        End If
    Next i
    
    'Calculate the interval
    DR.Interval = ((DR.Interval - DR.Base) + 1) / UBound(DR.DistCounts)
    
    'Accumulate counts
    For i = 1 To Pds
        If Simdata.DM.ByPassFlag(i) = BR_NOEXCEPTION Then
            p = Int((Simdata.DM.Cps(i) - DR.Base) / DR.Interval) + 1
            DR.DistCounts(p) = DR.DistCounts(p) + 1
        End If
    Next i
    
    'Determine the safety stock required for one period
    If DR.SvcLvl > 0 And DR.Points > 0 Then
        DR.MaxUnits = GetDemandSafetyStock(DR.Points, DR.SvcLvl, Simdata)
    End If
    
    BldDistRecord = DR.Points
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(BldDistRecord)"
     f_HandleErr , , , "AIMUtil::BldDistRecord", Now, gDRJobError, True, Err
End Function


Public Function ExpSmooth(Alpha As Double, UseDeSeasonedData As Boolean, _
    UseAdaptiveSmoothing As Boolean, FcstResults As FCSTRESULTS_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim AE As Double            'Absolute Error
    Dim Dd As Double            'Deseasonalized Demand
    Dim Fcst As Double
    Dim FcstCycle As Integer
    Dim Gamma As Double         'Adadaptive Smoothing Factor
    Dim LastDD As Double        'Deseasonalized Demand for Previous Cycle
    Dim LastFcst As Double      'Forecast for Previous Cycle
    Dim LastTrend As Double     'Trend for Previous Cycle
    Dim MAE As Double           'Mean Absolute Error
    Dim MovAvgPct As Double     'Moving Average Percentage
    Dim MSE As Double           'Mean Signed Error
    Dim Pd As Integer
    Dim SE As Double            'Signed Error
    Dim si As Double            'Seasonal Index
    Dim StartPeriod As Integer
    Dim Trend As Double
    
    'Check the range for exceptions
    If Simdata.DM.OldestPeriod > 104 Then
        StartPeriod = 104
    Else
        StartPeriod = Simdata.DM.OldestPeriod
    End If
    
    'Move to the first period with no data exception
    'This value will be used to initialize the forecast
    'for the next period
    For Pd = StartPeriod To LBound(Simdata.DM.dps) Step -1
        If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
            If UseDeSeasonedData Then
                Dd = Simdata.DM.dps(Pd)
            Else
                Fcst = Simdata.DM.Cps(Pd)
            End If
            
            Exit For
        End If
    Next Pd
    
    'Calculate the forecast for each period and update the
    'associated statistics
    For Pd = (Pd - 1) To 0 Step -1
        'Update the Forecast Cycle
        'The Forecast Cycle is used to determine which
        'forecasting method is appropriate.
        'Early values are calculated using a simple moving
        'average, while later values use exponential smoothing
        FcstCycle = FcstCycle + 1
        
        'Get the Seasonal Index for the current period
        si = Simdata.Sa.Bi(GetAbsPd(GetHsPeriod(Simdata.DM.EndPeriod, Pd)))
        
        Select Case Pd
            Case 0                                  'Save the Forecast for the Next Period
                'FcstResults.FcstNextPd = IIf(UseDeSeasonedData, (Dd * Si), Fcst)
                FcstResults.FcstNextPd = IIf(UseDeSeasonedData, Dd, Fcst)
            
            Case Is >= 1                            'Process all other periods
                'Calculate Errors and Trend
                If UseDeSeasonedData And Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
                    SE = Simdata.DM.Cps(Pd) - (Dd * si)
                    AE = Abs(SE)
                
                ElseIf Not UseDeSeasonedData And Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
                    SE = Simdata.DM.Cps(Pd) - Fcst
                    AE = Abs(SE)
                    
                Else            'Periods with a Demand Exception
                    SE = 0
                    AE = 0
                
                End If
                
                'Update Forecast Results
                If Pd >= 1 And Pd <= Simdata.LC.LookBackPds Then
                    'Forecast for the period = Forecast calculated at end of previous period
                    If UseDeSeasonedData Then
                        FcstResults.Fcst(Pd) = (Dd * si)
                    Else
                        FcstResults.Fcst(Pd) = Fcst
                    End If
                    
                    'Save the Demand for the period
                    FcstResults.Demand(Pd) = Simdata.DM.Cps(Pd)
                
                    'Accumulate Total Demand Demand and Accumulative Errors
                    If Pd = Simdata.LC.LookBackPds Then
                        FcstResults.Accum_Demand(Pd) = FcstResults.Demand(Pd)
                        FcstResults.Accum_E(Pd) = SE
                        FcstResults.Accum_AE(Pd) = AE
                    
                    Else
                        FcstResults.Accum_Demand(Pd) = FcstResults.Accum_Demand(Pd + 1) + FcstResults.Demand(Pd)
                        FcstResults.Accum_E(Pd) = FcstResults.Accum_E(Pd + 1) + SE
                        FcstResults.Accum_AE(Pd) = FcstResults.Accum_AE(Pd + 1) + AE
                        
                    End If
    
                    'For periods with no Demand Filter Exceptions Only
                    If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
                        'Update Bypass Flag for this period
                        FcstResults.ByPassFlag(Pd) = False
                    
                       'Update number of forecast periods
                        FcstResults.N = FcstResults.N + 1
                    
                    End If          'End of check for Bypassed Period
                    
                    'Calculate Totals
                    FcstResults.TotalDemand = FcstResults.TotalDemand + FcstResults.Demand(Pd)
                    FcstResults.TotalForecast = FcstResults.TotalForecast + FcstResults.Fcst(Pd)
                    
                    'Update trend
                    FcstResults.Trend = Trend
                    
                    'Update tracking signal
                    If MAE > 0 Then
                        FcstResults.TrkSignal(Pd) = MSE / MAE
                    End If
                End If
                    
                'Save previous cycle's Forecast and Trend
                LastDD = Dd
                LastFcst = Fcst
                LastTrend = Trend
                 
                'Update the forecast for the next cycle; assuming this period has no data exceptions
                If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
                    'Update the Accumlative Errors
                    MSE = MSE + SE
                    MAE = MAE + AE
                            
                    Select Case FcstCycle
                        Case 1 To Simdata.OP.DDMAP      'Moving Average Period
                            'Calculate the Moving Average Percentage
                            MovAvgPct = 1 / (FcstCycle + 1)
                            
                            'Update the forecast
                            If UseDeSeasonedData Then
                                Dd = (Dd * (1 - MovAvgPct)) + (Simdata.DM.dps(Pd) * MovAvgPct)
                            Else
                                Fcst = (Fcst * (1 - MovAvgPct)) + (Simdata.DM.Cps(Pd) * MovAvgPct)
                            End If
                
                        Case Is > Simdata.OP.DDMAP      'Exponential Smoothing
                            'Update the Forecast
                            If UseAdaptiveSmoothing Then
                                'Update Alpha
                                If MAE > 0 Then
                                    Alpha = Abs(MSE / MAE) * Simdata.OP.DAMP
                                End If
                                
                                Alpha = Max(Alpha, Simdata.OP.MinSmk)
                                Alpha = Min(Alpha, Simdata.OP.MaxSmk)
                            
                            End If
                        
                            If UseDeSeasonedData Then
                                Dd = ((1 - Alpha) * Dd) + (Alpha * Simdata.DM.dps(Pd))
                            Else
                                Fcst = ((1 - Alpha) * Fcst) + (Alpha * Simdata.DM.Cps(Pd))
                            End If
                            
                    End Select
                     
                   'Calculate trend
                    If UseDeSeasonedData Then
                        Trend = ((1 - Simdata.OP.TrnSmk) * LastTrend) + (Simdata.OP.TrnSmk * (Dd - LastDD))
                    Else
                        Trend = ((1 - Simdata.OP.TrnSmk) * LastTrend) + (Simdata.OP.TrnSmk * (Fcst - LastFcst))
                    End If
                End If
        End Select
    Next Pd             'End of Period Loop
    
    ExpSmooth = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(ExpSmooth)"
     f_HandleErr , , , "AIMUtil::ExpSmooth", Now, gDRJobError, True, Err
End Function


Public Function Max(v1 As Variant, v2 As Variant)
On Error GoTo ErrorHandler

    Max = IIf(v1 >= v2, v1, v2)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(max)"
     f_HandleErr , , , "AIMUtil::max", Now, gDRJobError, True, Err
End Function


Public Function Method01(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    'MovAvg(1,13)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        Trend = IIf(ApplyTrend, MA.Trend(Pd), 0)
        Fcst = MA.MA001_013(Pd) + Trend
        AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
    Next Pd
    
    Method01 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method01)"
    f_HandleErr , , , "AIMUtil::Method01", Now, gDRJobError, True, Err
End Function


Public Function Method02(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    'MovAvg(1,26)
   
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) > 0 Then
            Trend = IIf(ApplyTrend, MA.Trend(Pd), 0)
            
            Fcst = (MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA014_026(Pd) * MA.Pd014_026(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd)) + Trend
        End If
            
        AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 26 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If
    
    Method02 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method02)"
     f_HandleErr , , , "AIMUtil::Method02", Now, gDRJobError, True, Err
End Function


Public Function Method03(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    'MovAvg(1,52)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) > 0 Then
            Trend = IIf(ApplyTrend, MA.Trend(Pd), 0)
            
            Fcst = (MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA014_026(Pd) * MA.Pd014_026(Pd) _
                + MA.MA027_039(Pd) * MA.Pd027_039(Pd) _
                + MA.MA040_052(Pd) * MA.Pd040_052(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd)) _
                + Trend
        End If
            
        AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 52 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If
    
    Method03 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method03)"
     f_HandleErr , , , "AIMUtil::Method03", Now, gDRJobError, True, Err
End Function


Public Function Method05(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler
    
    'MovAvg(1,13) + Trend
    
    Method05 = Method01(FcstResults, MA, Simdata, ApplyTrend)
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 13 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method05)"
     f_HandleErr , , , "AIMUtil::Method05", Now, gDRJobError, True, Err
End Function


Public Function Method04(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    'MovAvg(53,65)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
            'Note: Trend is adjusted for 52 elapsed weeks
            Trend = IIf(ApplyTrend, MA.Trend(Pd) * 52, 0)
            Fcst = MA.MA053_065(Pd) + Trend
            
            AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 65 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If
    
    Method04 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method04)"
     f_HandleErr , , , "AIMUtil::Method04", Now, gDRJobError, True, Err
End Function


Public Function Method06(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler

    'MovAvg(1,26) + Trend
    
    Method06 = Method02(FcstResults, MA, Simdata, ApplyTrend)

    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 26 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method06)"
    f_HandleErr , , , "AIMUtil::Method06", Now, gDRJobError, True, Err
End Function


Public Function Method07(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler
    
    'MovAvg(1,52) + Trend
    
    Method07 = Method03(FcstResults, MA, Simdata, ApplyTrend)

    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 52 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method07)"
     f_HandleErr , , , "AIMUtil::Method07", Now, gDRJobError, True, Err
End Function


Public Function Method08(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD, ApplyTrend As Boolean)
On Error GoTo ErrorHandler
    
    'MovAvg(53,65) + Trend

    Method08 = Method04(FcstResults, MA, Simdata, ApplyTrend)

    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 65 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method08)"
     f_HandleErr , , , "AIMUtil::Method08", Now, gDRJobError, True, Err
End Function


Public Function Method09(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    'MovAvg(1,104)
    
    'Initialize Trend
    Trend = MA.Trend(0)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) _
            + MA.Pd053_065(Pd) + MA.Pd066_078(Pd) + MA.Pd079_091(Pd) + MA.Pd092_104(Pd) > 0 Then
            
            Fcst = (MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA014_026(Pd) * MA.Pd014_026(Pd) _
                + MA.MA027_039(Pd) * MA.Pd027_039(Pd) _
                + MA.MA040_052(Pd) * MA.Pd040_052(Pd) _
                + MA.MA053_065(Pd) * MA.Pd053_065(Pd) _
                + MA.MA066_078(Pd) * MA.Pd066_078(Pd) _
                + MA.MA079_091(Pd) * MA.Pd079_091(Pd) _
                + MA.MA092_104(Pd) * MA.Pd092_104(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) _
                + MA.Pd053_065(Pd) + MA.Pd066_078(Pd) + MA.Pd079_091(Pd) + MA.Pd092_104(Pd))
        
        End If
            
        AccumFcstStatistics Pd, Fcst, Trend, 1, FcstResults, Simdata
        
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 104 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method09 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method09)"
    f_HandleErr , , , "AIMUtil::Method09", Now, gDRJobError, True, Err
End Function


Public Function Method12(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Bi As Double
    Dim DS001_013 As Double
    Dim DS014_026 As Double
    Dim DS027_039 As Double
    Dim Fcst As Double
    Dim i As Integer
    Dim Pd As Integer
    Dim si As Integer
    Dim SI001_013 As Double
    Dim SI014_026 As Double
    Dim SI027_039 As Double
    Dim Trend As Double
    
    '[MovAvg(1,13) + MovAvg(1,13) + MovAvg(14,26) + MovAvg(27,39)] / 4 + SeaAdj
    
    'Initialize Trend
    Trend = MA.Trend(0)
    
    'Initialize Seasonality
    Bi = 1
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        'Initialize Seasonality Indices
        SI001_013 = 0
        SI014_026 = 0
        SI027_039 = 0
        
        'Calculate Seasonality Indices
        For i = Pd + 1 To Pd + 13
            SI001_013 = SI001_013 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i))
            SI014_026 = SI014_026 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 13))
            SI027_039 = SI027_039 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 26))
        Next i
        
        SI001_013 = Max(SI001_013 / 13, Simdata.OP.MinBi)
        SI014_026 = Max(SI014_026 / 13, Simdata.OP.MinBi)
        SI027_039 = Max(SI027_039 / 13, Simdata.OP.MinBi)
        
        If MA.Pd001_013(Pd) + MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) > 0 Then
            DS001_013 = MA.MA001_013(Pd) / SI001_013
            DS014_026 = MA.MA014_026(Pd) / SI014_026
            DS027_039 = MA.MA027_039(Pd) / SI027_039
            
            si = GetAbsPd(Pd)
            
            si = GetHsPeriod(Simdata.DM.EndPeriod, si)
            
            Fcst = (DS001_013 * MA.Pd001_013(Pd) _
                + DS001_013 * MA.Pd001_013(Pd) _
                + DS014_026 * MA.Pd014_026(Pd) _
                + DS027_039 * MA.Pd027_039(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd)) _
                * Simdata.Sa.Bi(si)
                
            'Update Current Seasonality Index
            Bi = Simdata.Sa.Bi(si)
        
        End If
            
        AccumFcstStatistics Pd, Fcst, Trend, Bi, FcstResults, Simdata
    
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod <= MIN_EVALUATION_PERIODS + 39 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method12 = FcstResults.N

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method12)"
     f_HandleErr , , , "AIMUtil::Method12", Now, gDRJobError, True, Err
End Function


'***********************************************************************
' Function Name:    ADOErrorHandler
'
' Description:      Displays errors identified by the ADO Connection.
'
' Parameters:       Cn      ADODB Connection
'                   Msg     User Message
'
' Returns:          N/A
'
' By: RES           Date: 09/07/1998
'
'***********************************************************************
Function ADOErrorHandler(Cn As ADODB.Connection, Msg As String)
On Error GoTo ErrorHandler

    Dim i As Integer
    Dim s As String
    Dim strMessage As String
    
    s = Msg + vbCrLf + vbCrLf
    
    For i = 0 To Cn.Errors.Count - 1
        s = s & Format(Cn.Errors(i).NativeError, "####0") & " -- " _
            & Cn.Errors(i).Description & vbCrLf & vbCrLf
    Next i

    strMessage = getTranslationResource("MSGBOX80500")
    If StrComp(strMessage, "MSGBOX80500") = 0 Then strMessage = "ADO Critical Error"


    'MsgBox s, vbCritical, , strMessage, getTranslationResource(AIM_Main.Caption)
    MsgBox s, vbCritical, , strMessage, ""

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(ADOErrorHandler)"
     f_HandleErr , , , "AIMUtil::ADOErrorHandler", Now, gDRJobError, True, Err
End Function
Function DxForecastReport(Path As String, LogFile As String, rsForecastPlannerReport As ADODB.Recordset)

On Error GoTo ErrorHandler

    Dim LastVnid As String
    Dim LastZone As String
    Dim LineSeqNbr As Long
    Dim POFile As Integer
    Dim POType As String
    Dim POFileName As String
    Dim POTempName As String
    Dim RcdCount As Long
    Dim strSQL As String
    Dim strMessage As String
    Dim strMessage1 As String
    Dim TempString As String
    
    If Not f_IsRecordsetOpenAndPopulated(rsForecastPlannerReport) Then
        DxForecastReport = -1 ' failure
    Exit Function
    End If
    
    rsForecastPlannerReport.MoveFirst
    'Write message to Log File
    Write_Log LogFile, "", 0, False, True, True, False
    strMessage = getTranslationResource("TEXTMSG80531")
    If StrComp(strMessage, "TEXTMSG80531") = 0 Then strMessage = "ForecastPlannerReport lines -- Started."

    Write_Log LogFile, strMessage, 0, False, True, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxForecastreport", "AIMUtil::DxForecastreport", Now(), gDRJobUpdate)
    
    'Output Command Line Parameters
    strMessage = getTranslationResource("Forecast Id", , True)
    TempString = strMessage & " " & CStr(rsForecastPlannerReport("FcstId").Value)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxForecastreport", "AIMUtil::DxForecastreport", Now(), gDRJobUpdate)
        
    strMessage = getTranslationResource("TEXTMSG80532")
    If StrComp(strMessage, "TEXTMSG80532") = 0 Then strMessage = "Log File"
    TempString = strMessage & " " & CStr(LogFile)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxForecastreport", "AIMUtil::DxForecastreport", Now(), gDRJobUpdate)
    
    'Build the PO Output File Name
    Path = Trim$(Path)
    If Right(Path, 1) <> "\" Then
        Path = Path & "\"
    End If
    
    POFileName = Trim$(rsForecastPlannerReport("FcstId").Value) & ".SSADR.FP." & Format(Now(), g_ISO_DATE_FORMAT_NODELIMITERS) & ".TXT"
    POTempName = Trim$(rsForecastPlannerReport("FcstId").Value) & ".SSADR.FP." & Format(Now(), g_ISO_DATE_FORMAT_NODELIMITERS) & ".TXT"
   
    'Open the output file
    POFile = FreeFile
    On Error Resume Next
    Open Path + POTempName For Output As POFile
    'Check for Error
    If Err <> 0 Then
        strMessage = getTranslationResource("TEXTMSG80533")
        If StrComp(strMessage, "TEXTMSG80533") = 0 Then strMessage = "Error opening ForecastPlanner Report Line File --"
        strMessage1 = getTranslationResource("TEXTMSG80534")
        If StrComp(strMessage1, "TEXTMSG80534") = 0 Then strMessage1 = "! -- Job aborted."
        
        strMessage = gUserID & _
                    " -- " & Err.Number & " -- " & Err.source & " (AIMUtil.DxForecastreport()) -- " & Err.Description & "(1)" & _
                    strMessage & " (" & Path & POTempName & ")" & strMessage1
        
        Write_Log LogFile, strMessage, 4, False, True, False, True
        Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxForecastreport", "AIMUtil::DxForecastreport", Now(), gDRJobUpdate)
        
        
        'Display to the UI, as well, for good measure.
        Write_Message strMessage
        DxForecastReport = 0
        Exit Function
    End If
    On Error GoTo ErrorHandler
    
    'Print the Purchase Order Header 1
    On Error Resume Next
    
    'Processing Loop
    
    Do Until rsForecastPlannerReport.eof
        
            Print #POFile, _
                rsForecastPlannerReport!FcstId & vbTab; _
                rsForecastPlannerReport!LcId & vbTab; _
                rsForecastPlannerReport!Item & vbTab; _
                rsForecastPlannerReport!SysFcst & vbTab; _
                rsForecastPlannerReport!MasterFcstAdj & vbTab; _
                rsForecastPlannerReport!FcstAdj & vbTab; _
                rsForecastPlannerReport!NetReq & vbTab; _
                rsForecastPlannerReport!AdjNetReq & vbTab; _
                rsForecastPlannerReport!ProjInv & vbTab; _
                rsForecastPlannerReport!HistDmd & vbTab; _
                rsForecastPlannerReport!ProdConst&; vbTab; _
                Format(rsForecastPlannerReport!FcstPdBegDate, g_ISO_DATE_FORMAT_NODELIMITERS) & vbTab; _
                Format(rsForecastPlannerReport!FcstPdEndDate, g_ISO_DATE_FORMAT_NODELIMITERS)
            RcdCount = RcdCount + 1
            If Err <> 0 Then
                strMessage = getTranslationResource("TEXTMSG80535")
                If StrComp(strMessage, "TEXTMSG80535") = 0 Then strMessage = "Error writing ForecastPlanner Report Line File --"
                strMessage1 = getTranslationResource("TEXTMSG80536")
                If StrComp(strMessage1, "TEXTMSG80536") = 0 Then strMessage1 = "! -- Job aborted."
                
                strMessage = gUserID & _
                    " -- " & Err.Number & " -- " & Err.source & " (AIMUtil.DxPO()) -- " & Err.Description & "(4)" & _
                    strMessage & " (" & Path & POTempName & ")" & strMessage1
                
                Write_Log LogFile, strMessage, 4, False, True, False, True
                Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxPO", "AIMUtil::DxPO", Now(), gDRJobUpdate)
                
                'Display to the UI, as well, for good measure.
                Write_Message strMessage
                DxForecastReport = 0
                Exit Function
            End If
        
    
        'Get next record
        rsForecastPlannerReport.MoveNext
    Loop
    
    'Close the output file
    Close #POFile
    
    'Rename the output file/
    'Check for an empty record set
    If RcdCount = 0 Then
        Kill Path + POTempName
    Else
        On Error Resume Next
        Name Path + POTempName As Path + POFileName
        If Err.Number = 58 Then
            'File already exists
            On Error GoTo ErrorHandler
            Kill Path + POFileName
            Name Path + POTempName As Path + POFileName
        End If
    End If
    On Error GoTo ErrorHandler

    'Write message to log file
    strMessage = getTranslationResource("File Name", , True)
    TempString = TempString
    'Write_Log LogFile, TempString, 2, False, False, False, False
    'Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxForecastreport", "AIMUtil::DxForecastreport", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("TEXTMSG80537")
    If StrComp(strMessage, "TEXTMSG80537") = 0 Then strMessage = "Forecastreport Lines Transferred"
    
    TempString = strMessage & " " & CStr(RcdCount)
    Write_Log LogFile, TempString, 2, False, False, False, False
    Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxForecastreport", "AIMUtil::DxForecastreport", Now(), gDRJobUpdate)
    
    strMessage = getTranslationResource("TEXTMSG80538")
    If StrComp(strMessage, "TEXTMSG80538") = 0 Then strMessage = "ForecastPlannerReport lines -- Completed"
    Write_Log LogFile, strMessage, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::Forecastreport", "AIMUtil::Forecastreport", Now(), gDRJobUpdate)
    
    Write_Log LogFile, "", 0, False, True, True, False

    'Return Rows Written
    DxForecastReport = RcdCount

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(DxPO)"
    f_HandleErr , , , "AIMUtil::DXForecastreport", Now, gDRJobError, True, Err
End Function
Function DxForecastReport_Ctrl(ByVal FcstId As String)
On Error GoTo ErrorHandler
    
    
    
    Dim strMessage As String
    Dim Cn As ADODB.Connection
    Dim ErrNumber As Long
    Dim ErrSource As String
    Dim ErrDesc As String
    
    Dim AIM_ForecastPlannerReport_Sp As ADODB.Command
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
   

    Dim rsForecastPlannerReport As ADODB.Recordset
    Dim rsSysCtrl As ADODB.Recordset
    Dim rsFcstId    As ADODB.Recordset
    Dim RtnCode As Integer
    Dim strSQL As String
    
    'System Control Values
    Dim DxPath_Out As String
    Dim DxPO_Option As String
    Dim LogFile As String
    Dim TempString As String

    'Open a connection to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        Exit Function
    End If
    
    'Define the stored procedures
    Set AIM_ForecastPlannerReport_Sp = New ADODB.Command
    With AIM_ForecastPlannerReport_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_ForecastPlannerReport_Sp"
        .Parameters.Refresh
    
    End With

    
    Set AIM_SysCtrl_Get_Sp = New ADODB.Command
    With AIM_SysCtrl_Get_Sp
        Set .ActiveConnection = Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_SysCtrl_Get_Sp"
        .Parameters.Refresh
        
    End With
    
    
    'Define the Result Sets
    Set rsForecastPlannerReport = New ADODB.Recordset
    With rsForecastPlannerReport
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    
    
    Set rsSysCtrl = New ADODB.Recordset
    With rsSysCtrl
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    Set rsFcstId = New ADODB.Recordset
    With rsFcstId
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    'Get the System Control Record
    rsSysCtrl.Open AIM_SysCtrl_Get_Sp
    If Not f_IsRecordsetOpenAndPopulated(rsSysCtrl) Then
        Err.Description = "SysCtrl Empty Recordset"
        GoTo ErrorHandler
    End If
    
    LogFile = rsSysCtrl("LogFile").Value
    DxPath_Out = rsSysCtrl("DxPath_Out").Value
    gDateFormat = rsSysCtrl!DateFormat
    gTimeFormat = rsSysCtrl!TimeFormat

    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Write message to Log File
    Write_Log LogFile, "", 0, False, True, True, False
    strMessage = getTranslationResource("TEXTMSG80527")
    If StrComp(strMessage, "TEXTMSG80527") = 0 Then strMessage = "Send Forecast Planner Report to Host -- Started."
    Write_Log LogFile, strMessage, 0, True, True, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxForecastReport_Ctrl", "AIMUtil::DxForecastReport_Ctrl", Now(), gDRJobUpdate, False, , True)


    If UCase(FcstId) = UCase(getTranslationResource("ALL")) Then
        strSQL = "Select FcstId from AIMFcstSetUp where FcstLocked =1"
    Else
        strSQL = "Select FcstId from AIMFcstSetUp where FcstId = '" + FcstId + "'"
        
    End If
    
    rsFcstId.Open strSQL, Cn
    
    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(rsFcstId) = False Then
        strMessage = getTranslationResource("TEXTMSG80528")
        If StrComp(strMessage, "TEXTMSG80528") = 0 Then strMessage = "Send Forecast Planner Report -- Number of files processed"
        TempString = strMessage + ": 0 "
        Write_Log LogFile, TempString, 2, True, True, False, False
        
        Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxForecastReport_Ctrl", "AIMUtil::DxForecastReport_Ctrl", Now(), gDRJobUpdate)
    Else
        Do Until rsFcstId.eof
            
            If f_IsRecordsetValidAndOpen(rsForecastPlannerReport) Then
                rsForecastPlannerReport.Close
            End If
            AIM_ForecastPlannerReport_Sp("@FcstId") = rsFcstId("FcstId").Value
            rsForecastPlannerReport.Open AIM_ForecastPlannerReport_Sp
           
            'If f_IsRecordsetOpenAndPopulated(P_rsAIM_FcstAdj) Then
            
            If Not (rsForecastPlannerReport.BOF And rsForecastPlannerReport.eof) Then
                RtnCode = DxForecastReport(DxPath_Out, LogFile, rsForecastPlannerReport)
                If RtnCode <= 0 Then
                    'Failed
                End If
            End If

            'Get the Next FcstId
            rsFcstId.MoveNext
        Loop
        strMessage = getTranslationResource("TEXTMSG80529")
        If StrComp(strMessage, "TEXTMSG80529") = 0 Then strMessage = "Send Forecast Planner report to Host -- Number of files created"
        TempString = strMessage + ": " + CStr(rsFcstId.RecordCount)
        Write_Log LogFile, TempString, 2, True, True, False, False
        Call f_HandleErr(0, vbNewLine & TempString, "AIMUtil::DxForecastreport_Ctrl", "AIMUtil::DxForecastreport_Ctrl", Now(), gDRJobUpdate)
    End If
    
    'Write message to log file
    strMessage = getTranslationResource("TEXTMSG80530")
    If StrComp(strMessage, "TEXTMSG80530") = 0 Then strMessage = "Send Forecast Planner Report to Host -- Completed."
    Write_Log LogFile, strMessage, 0, True, False, False, True
    Call f_HandleErr(0, vbNewLine & strMessage, "AIMUtil::DxForecastreport_Ctrl", "AIMUtil::DxForecastreport_Ctrl", Now(), gDRJobUpdate)
    
    Write_Log LogFile, "", 0, False, True, True, False
    
    'Wind Up
CleanUp:
    If f_IsRecordsetValidAndOpen(rsFcstId) Then rsFcstId.Close
    Set rsFcstId = Nothing
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    If f_IsRecordsetValidAndOpen(rsForecastPlannerReport) Then rsForecastPlannerReport.Close
    Set rsForecastPlannerReport = Nothing
    
    If Not (AIM_ForecastPlannerReport_Sp Is Nothing) Then Set AIM_ForecastPlannerReport_Sp.ActiveConnection = Nothing
    Set AIM_ForecastPlannerReport_Sp = Nothing
    If Not (AIM_SysCtrl_Get_Sp Is Nothing) Then Set AIM_SysCtrl_Get_Sp.ActiveConnection = Nothing
    Set AIM_SysCtrl_Get_Sp = Nothing
    
        
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    'Err.Raise ErrNumber, ErrSource & "(AIMUtil.bas)", ErrDesc & "(DxPO_Ctrl)"
     f_HandleErr , , , "AIMUtil::DxForecastReport_Ctrl", Now, gDRJobError, True, Err
     GoTo CleanUp:
End Function







'***********************************************************************
' Function Name:    DataChanged(Fm)
'
' Description:      Checks a recordset and determines if any values have
'                   been updated.
'
' Parameters:       Rs      Recordset
'
' Returns:          Number of fields updated
'                   0 = No data changed
'
' By: RES           Date: 11/11/1998
'
'***********************************************************************
Function DataChanged(rs As ADODB.Recordset, Scope As AffectEnum) As Integer
On Error GoTo ErrorHandler

    Dim Bookmark As Variant
    Dim c As Integer
    Dim i As Integer
    
    'Check for an empty recordset
    If Not f_IsRecordsetOpenAndPopulated(rs) Then
        DataChanged = 0
        Exit Function
    End If
    
    If rs.BOF And rs.eof Then
        DataChanged = 0
        Exit Function
    End If
    
    Select Case Scope
        Case adAffectAll
            If Not (rs.eof Or rs.BOF) Then
                Bookmark = rs.Bookmark
            End If
            
            rs.MoveFirst
            Do Until rs.eof
                For i = 0 To (rs.Fields.Count - 1)
                    If rs(i).OriginalValue <> rs(i).Value Then
                        c = c + 1
                    End If
                Next i
                rs.MoveNext
            Loop
            
            'Restore recordset positioning
            If Not IsEmpty(Bookmark) Then
                rs.Bookmark = Bookmark
            End If
            
        Case adAffectCurrent
            For i = 0 To (rs.Fields.Count - 1)
                If rs(i).OriginalValue <> rs(i).Value Then
                    c = c + 1
                End If
            Next i
        
        Case Else
            c = 0
            
    End Select
    
    DataChanged = c
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(DataChanged)"
     f_HandleErr , , , "AIMUtil::DataChanged", Now, gDRJobError, True, Err
End Function


Public Function FcstSimulation(SortResults As Boolean, Simdata As SIMULATION_RCD, _
    FcstResults() As FCSTRESULTS_RCD, AIMMethods() As METHOD_RCD, P_ApplyMasterAdj As Boolean, P_AIM_FcstAdj As ADODB.Command, P_rsAIM_FcstAdj As ADODB.Recordset)
On Error GoTo ErrorHandler

    Dim BestMethod As Integer
    Dim FcstMethod As Integer
    Dim MA As MOVAVGWORK_RCD
    Dim MAE As Double
    Dim N As Integer
    Dim OldMAE As Double
    
    'Check to see if Forecast Update has been turned off
    If Simdata.OP.BypDDU = "Y" Then
        Exit Function
    End If
    
    'Build the Moving Average Work Table
    BldMovAvgTable Simdata, MA
    
    'Initialize the Mean Absolute Error to High Values
    OldMAE = 999999
    BestMethod = 0
    
    For FcstMethod = 1 To NBR_METHODS
        'Clear the Forecast Results
        InitFcstRcd FcstResults(FcstMethod)
    
        FcstResults(FcstMethod).FcstMethod = AIMMethods(FcstMethod).MethodId
        FcstResults(FcstMethod).FcstDescription = AIMMethods(FcstMethod).MethodDesc
        
        'Check for a user override on Forecast Method
        If Simdata.It.UserMethod = 0 Then
            FcstResults(FcstMethod).FcstStatus = AIMMethods(FcstMethod).MethodStatus
        ElseIf Simdata.It.UserMethod > 0 And FcstMethod <> Simdata.It.UserMethod Then
            FcstResults(FcstMethod).FcstStatus = FS_INACTIVE
        ElseIf Simdata.It.UserMethod > 0 And FcstMethod = Simdata.It.UserMethod Then
            FcstResults(FcstMethod).FcstStatus = FS_ACTIVE
        End If
        
        If FcstResults(FcstMethod).FcstStatus = FS_ACTIVE Then
            Select Case FcstMethod
            Case 1          'MovAvg(1,13)
                N = Method01(FcstResults(FcstMethod), MA, Simdata, False)
                
            Case 2          'MovAvg(1,26)
                N = Method02(FcstResults(FcstMethod), MA, Simdata, False)
                
            Case 3          'MovAvg(1,52)
                N = Method03(FcstResults(FcstMethod), MA, Simdata, False)
                
            Case 4          'MovAvg(53,65)
                N = Method04(FcstResults(FcstMethod), MA, Simdata, False)
                
            Case 5          'MovAvg(1,13) + Trend
                N = Method05(FcstResults(FcstMethod), MA, Simdata, True)
            
            Case 6          'MovAvg(1,26) + Trend
                N = Method06(FcstResults(FcstMethod), MA, Simdata, True)
            
            Case 7         'MovAvg(1,52) + Trend
                N = Method07(FcstResults(FcstMethod), MA, Simdata, True)
            
            Case 8          'MovAvg(53,65) + Trend
                N = Method08(FcstResults(FcstMethod), MA, Simdata, True)
            
            Case 9          'MovAvg(1,104)
                N = Method09(FcstResults(FcstMethod), MA, Simdata)
                 
            Case 10         '[(2 * MovAvg(1,13)) + MovAvg(14,26)] / 3
                N = Method10(FcstResults(FcstMethod), MA, Simdata)
            
            Case 11         'MovAvg(1,13) + [MovAvg(40,52) - MovAvg(53,65)]
                N = Method11(FcstResults(FcstMethod), MA, Simdata)
            
            Case 12         '[MovAvg(1,13) + MovAvg(1,13) + MovAvg(14,26) + MovAvg(27,39)] / 4 + SeaAdj
                N = Method12(FcstResults(FcstMethod), MA, Simdata)
            
            Case 13         '[MovAvg(40,52) + MovAvg(92,104)] / 2 + Trend
                N = Method13(FcstResults(FcstMethod), MA, Simdata)
            
            Case 14         'MovAvg(1,104) + SeaAdj
                N = Method14(FcstResults(FcstMethod), MA, Simdata)
            
            Case 15         'MovAvg(1,26) + Trend + SeaAdj
                N = Method15(FcstResults(FcstMethod), MA, Simdata)
             
            Case 16         'MovAvg(1,52) + Trend + SeaAdj
                N = Method16(FcstResults(FcstMethod), MA, Simdata)
            
            Case 17         'ExpSmooth(.1)
                N = Method17(FcstResults(FcstMethod), Simdata)
            
             Case 18         'ExpSmooth(.2)
                N = Method18(FcstResults(FcstMethod), Simdata)
             
            Case 19         'ExpSmooth(.3)
                N = Method19(FcstResults(FcstMethod), Simdata)
             
            Case 20         'ExpSmooth(.1) + SeaAdj
                N = Method20(FcstResults(FcstMethod), Simdata)
            
            Case 21         'ExpSmooth(.2) + SeaAdj
                N = Method21(FcstResults(FcstMethod), Simdata)
            
            Case 22         'ExpSmooth(.3) + SeaAdj
                N = Method22(FcstResults(FcstMethod), Simdata)
            
            Case 23         'Adaptive Smoothing
                N = Method23(FcstResults(FcstMethod), Simdata)
            'Osama --Adding a new 2 Methods as Method 3 Starting from First History Period if less than number of MovAvg Periods
            Case 24         'MovAvg(1,52) + Optional SeaAdj + Optional Trend From History Start Only
                N = Method24(FcstResults(FcstMethod), MA, Simdata, AIMMethods(24).ApplyTrend, AIMMethods(24).ApplySeasonsIndex)
                        
            Case 25         'MovAvg(1,26) + Optional SeaAdj + Optional Trend From History Start Only
                N = Method25(FcstResults(FcstMethod), MA, Simdata, AIMMethods(25).ApplyTrend, AIMMethods(25).ApplySeasonsIndex)
            
            End Select
       
        End If
        
        'Check for an invalid method
        If FcstResults(FcstMethod).FcstNextPd <= 0 Then
            FcstResults(FcstMethod).FcstStatus = FS_INACTIVE
        End If
        
        If FcstResults(FcstMethod).FcstStatus = FS_ACTIVE _
            And FcstResults(FcstMethod).N > 0 Then
            MAE = FcstResults(FcstMethod).Accum_AE(1) / FcstResults(FcstMethod).N
        Else
            MAE = OldMAE
        End If
        
        If MAE < OldMAE Then
            BestMethod = FcstMethod
            OldMAE = MAE
        End If
        
    Next FcstMethod
    
    'If no method qualifies then use Method 1
    If BestMethod = 0 Then
        BestMethod = 1
        FcstResults(1).FcstStatus = FS_ACTIVE
    End If
    
    'Update the Item's Forecast Elements
    Simdata.It.FcstMethod = FcstResults(BestMethod).FcstMethod
    Simdata.It.FcstDemand = FcstResults(BestMethod).FcstNextPd
    If FcstResults(BestMethod).N > 0 Then
        Simdata.It.MAE = FcstResults(BestMethod).Accum_AE(1) / FcstResults(BestMethod).N
        Simdata.It.MSE = FcstResults(BestMethod).Accum_E(1) / FcstResults(BestMethod).N
    End If
    Simdata.It.Trend = FcstResults(BestMethod).Trend
    
    'Test the Tracking Signal
    If Simdata.It.MAE > 0 Then
        If Abs(Simdata.It.MSE / Simdata.It.MAE) > Simdata.OP.TSL Then
            Simdata.It.TrkSignalFlag = "Y"
        End If
    End If
        
    'Sort Results
    If SortResults Then
        SortFcstResults FcstResults()
    End If
    
    'Update the Order Policies
    OrderPolicyUpdate Simdata, AIMMethods(), P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(FcstSimulation)"
     f_HandleErr , , , "AIMUtil::FcstSimulation", Now, gDRJobError, True, Err
End Function


Public Function InitFcstRcd(FcstResults As FCSTRESULTS_RCD)
On Error GoTo ErrorHandler

    Dim i As Integer
    
    With FcstResults
        .FcstStatus = FS_ACTIVE
        .N = 0
        .TotalForecast = 0
        .TotalDemand = 0
        .Trend = 0
        .FcstNextPd = 0
        
        For i = LBound(.Fcst) To UBound(.Fcst)
            .ByPassFlag(i) = True
            .Fcst(i) = 0
            .Accum_AE(i) = 0
            .Accum_E(i) = 0
            .Accum_Demand(i) = 0
            .TrkSignal(i) = 0
            .Demand(i) = 0
        Next i
    
    End With
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(InitFcstRcd)"
     f_HandleErr , , , "AIMUtil::InitFcstRcd", Now, gDRJobError, True, Err
End Function


Public Function IsDmdLimitExcpt(Fcst As Double, MAD As Double, _
    Pd As Integer, DmdLimitExcptType As String, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler
    
    Dim CurPd As Integer
    Dim EndPoint As Integer
    
    'Set the testing end point
    EndPoint = Pd - (Simdata.OP.NDFL - 1)
    
    'Are there sufficient remaining points to perform the test ?
    If EndPoint <= 0 Then
        IsDmdLimitExcpt = True
        Exit Function
    End If
    
    EndPoint = IIf(EndPoint < LBound(Simdata.DM.dps), _
        LBound(Simdata.DM.dps), EndPoint)
    
    IsDmdLimitExcpt = False
    
    For CurPd = (Pd - 1) To EndPoint Step -1
        If DmdLimitExcptType = "H" _
            And Simdata.DM.dps(CurPd) <= Fcst + (MAD * Simdata.OP.HiDFL) Then
            
            IsDmdLimitExcpt = True      'Exception
            Exit For
        
        ElseIf DmdLimitExcptType = "L" _
            And Simdata.DM.dps(CurPd) >= Fcst - (MAD * Simdata.OP.LoDFL) Then
        
            IsDmdLimitExcpt = True      'Exception
            Exit For
        
        End If
    Next CurPd

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(IsDmdLimitExcpt)"
     f_HandleErr , , , "AIMUtil::IsDmdLimitExcpt", Now, gDRJobError, True, Err
End Function


Public Function FilterDemand(Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler
    
    Dim cbi As Double
    Dim cad As Double
    Dim csd As Double
    
    Dim DITrend As Double
    Dim DIHits As Integer
    
    Dim Fcst As Double
    Dim FcstCycle_CY As Integer
    Dim i As Integer
    Dim MAD As Double
    Dim MSD As Double
    Dim OldestPeriodFlag As Boolean
    Dim OldFcst As Double
    Dim PctZero As Double
    Dim PdStartDate As Date
    Dim Pd As Integer
    Dim PositiveSaleFlag As Boolean
    Dim SeasIndexRating As Double
    Dim Total_HiTrps_Cy As Integer
    Dim Total_Cycles As Integer
    Dim ZeroCount As Integer
    
    'Initialize variables
    Simdata.It.IsIntermittent = "N"
    
    Simdata.It.ByPassPct = 0
    Simdata.It.DIFlag = "N"
    Simdata.It.DmdFilterFlag = "N"
    Simdata.It.FcstCycles = 0
    Simdata.It.TrkSignalFlag = "N"
    Simdata.It.UserDemandFlag = "N"
    Simdata.It.ZeroCount = 0
    
    PositiveSaleFlag = False
    OldestPeriodFlag = False
    
    'Bypass all periods before the oldest period
    For Pd = UBound(Simdata.DM.dps) To (Simdata.DM.OldestPeriod + 1) Step -1
        Simdata.DM.ByPassFlag(Pd) = BR_BYPASS
    Next Pd
    
    For Pd = Simdata.DM.OldestPeriod To LBound(Simdata.DM.dps) Step -1
        Total_Cycles = Total_Cycles + 1
    
        'Initialize value
        Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION
        
        'Is this a positive sale
        If Simdata.DM.dps(Pd) > 0 And PositiveSaleFlag = False Then
            PositiveSaleFlag = True
        End If
        
        '***
        '*** Apply Bypass Rules
        '***
        
        'Determine the current Seasonality Index
        cbi = Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, Pd))
        
        'Bypass if:
        '
        ' Current Base Index is <= the Minimum Base Index
        ' Bypass initial update until First Positive Sale
        ' Bypass if DDU bypass switch is on
        If cbi <= Simdata.OP.MinBi _
            Or (Simdata.OP.BypTFP = "Y" And PositiveSaleFlag = False) _
            Or (Simdata.OP.BypZSl = "Y" And Simdata.DM.dps(Pd) <= 0) _
            Or Simdata.OP.BypDDU = "Y" Then
                
            Simdata.DM.ByPassFlag(Pd) = BR_BYPASS
            Simdata.It.ByPassPct = Simdata.It.ByPassPct + 1
        
        End If
        
        'Determine the current Period Start Date
        PdStartDate = GetStartDateFmPeriod(GetHsPeriod(Simdata.DM.EndPeriod, Pd), GetHsYear(Simdata.DM.EndPeriod, Simdata.DM.EndYear, Pd), Simdata.AIMYears)
        
        'Check to see if this demand occurred during a promotional period
        'Demand during promotional periods is not used to update the forecast
        PdStartDate = DateAdd("ww", Pd * -1, Simdata.DM.BaseDate)
        
        If Simdata.It.PmId <> "None" And Trim$(Simdata.It.PmId) <> "" _
            And Simdata.PM.PmStatus = True _
            And DateAdd("ww", Simdata.OP.BypBef * -1, Simdata.PM.PmStartDate) _
                <= PdStartDate _
            And DateAdd("ww", Simdata.OP.BypAft, Simdata.PM.PmEndDate) _
                >= PdStartDate Then
            
            Simdata.DM.ByPassFlag(Pd) = BR_BYPASS
            Simdata.It.ByPassPct = Simdata.It.ByPassPct + 1
        
        End If
        
        If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
            'Increment the Forecast Cycle Count
            Simdata.It.FcstCycles = Simdata.It.FcstCycles + 1
            
            'Increment the Zero Sales Count
            If Simdata.DM.dps(Pd) = 0 Then
                Simdata.It.ZeroCount = Simdata.It.ZeroCount + 1
            End If
            
            'Increment the Current Year Forecast Cycle Count
            If Pd <= 52 Then
                FcstCycle_CY = FcstCycle_CY + 1
            End If
            
            'Set the oldest period to the first period with no filter exceptions
            If Not OldestPeriodFlag Then
                OldestPeriodFlag = True
                Simdata.DM.OldestPeriod = Pd
            End If
            
        End If
        
        'Save the previous period's forecast
        OldFcst = Fcst
        
        Select Case Simdata.It.FcstCycles
            Case 1                          'First Forecast Cycle
                Fcst = Simdata.DM.dps(Pd)
                MAD = MADInit(Fcst)
                MSD = 0
                
            Case 2 To Simdata.OP.DDMAP          'Moving Average Period
                'Calculate the Mean Signed Deviation / Mean Absolute Deviation
                csd = Simdata.DM.dps(Pd) - Fcst
                cad = Abs(Simdata.DM.dps(Pd) - Fcst)
                MSD = ((1 - Simdata.OP.MADSmk) * MSD) + (Simdata.OP.MADSmk * csd)
                MAD = ((1 - Simdata.OP.MADSmk) * MAD) + (Simdata.OP.MADSmk * cad)
                
                'Update the forecast
                Fcst = (Fcst * (Simdata.It.FcstCycles - 1) + Simdata.DM.dps(Pd)) / Simdata.It.FcstCycles
                Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION
    
            Case Is > Simdata.OP.DDMAP
                'Calculate the Mean Signed Deviation / Mean Absolute Deviation
                csd = Simdata.DM.dps(Pd) - Fcst
                cad = Abs(Simdata.DM.dps(Pd) - Fcst)
                
                'CAD cannot be less than the Low MAD Percentage
                If cad < Simdata.OP.LoMADP * Fcst Then
                    cad = Simdata.OP.LoMADP * Fcst
                End If
                
                'Check for a high demand filter exception
                '
                ' Current Absolute Deviation > Current Absolute Deviation Limit
                ' Sales > Forecast + Current Absolute Deviation * High Demand Filter Limit
                ' Consecutive High Trips < Demand Filter Hits Limit
                If cad > Simdata.OP.CADL _
                    And Simdata.DM.dps(Pd) > Fcst + (MAD * Simdata.OP.HiDFL) _
                    And Simdata.OP.ApplyDmdFilter = "Y" Then
                    
                    'Look ahead to see if this is a real exception
                    If IsDmdLimitExcpt(Fcst, MAD, Pd, "H", Simdata) Then
                        Simdata.DM.ByPassFlag(Pd) = BR_HIDEMAND
                        Simdata.It.ByPassPct = Simdata.It.ByPassPct + 1
                        
                        If Pd <= 52 Then
                            Total_HiTrps_Cy = Total_HiTrps_Cy + 1
                        End If
                    End If
                End If
                
                'Check to see if this is an exceptionally high demand Filter Exception
                'This check is only performed when:
                ' . The conditions for a single High Demand Filter Exception Exist
                ' . The deseasonalized demand is greater than 100,
                ' . and the current forecast for the item exceeds 5
                If cad > Simdata.OP.CADL _
                    And Simdata.DM.dps(Pd) > Fcst + (MAD * 100) _
                    And Simdata.OP.ApplyDmdFilter = "Y" _
                    And Simdata.DM.dps(Pd) > 100 _
                    And Fcst > 5 Then
                    
                    If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION _
                        And IsDmdLimitExcpt(Fcst, MAD, Pd, "H", Simdata) Then
                    
                        Simdata.It.ByPassPct = Simdata.It.ByPassPct + 1
                        
                        If Pd <= 52 Then
                            Total_HiTrps_Cy = Total_HiTrps_Cy + 1
                        End If
                        
                        'Tag it as a "Real High" Demand Filter Exception
                    
                        Simdata.DM.ByPassFlag(Pd) = BR_REALHI
                
                    End If
                
                End If
                
                'Check for a low demand filter exception
                '
                ' Current Absolute Deviation > Current Absolute Deviation Limit
                ' Sales < Forecast + Current Absolute Deviation * Low Demand Filter Limit
                ' Consecutive Low Trips < Demand Filter Hits Limit
                If cad > Simdata.OP.CADL _
                    And Simdata.DM.dps(Pd) < Fcst - (MAD * Simdata.OP.LoDFL) _
                    And Simdata.OP.ApplyDmdFilter = "Y" Then
                    
                    'Look ahead to see if this is a real exception
                    If IsDmdLimitExcpt(Fcst, MAD, Pd, "L", Simdata) Then
                        Simdata.DM.ByPassFlag(Pd) = BR_LOWDEMAND
                        Simdata.It.ByPassPct = Simdata.It.ByPassPct + 1
                    End If
                End If
                
                'Update Forecast if no demand filter exceptions
                If Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION Then
                    Fcst = (1 - Simdata.OP.FilterSmk) * Fcst + Simdata.OP.FilterSmk * Simdata.DM.dps(Pd - i)
                    MSD = ((1 - Simdata.OP.MADSmk) * MSD) + (Simdata.OP.MADSmk * csd)
                    MAD = ((1 - Simdata.OP.MADSmk) * MAD) + (Simdata.OP.MADSmk * cad)
                End If
        End Select
        
        'Update the trend
        If OldFcst > 0 Then
            DITrend = ((1 - Simdata.OP.TrnSmk) * DITrend) + _
                (Simdata.OP.TrnSmk * ((Fcst - OldFcst) / OldFcst))
            'Debug.Print Pd, Format(Fcst, "0.00"), Format(DITrend, "0.00000")
        End If
        
        'IF the trend (DITrend) is negative, significant (less than the
        'dying item trend decline limit (DITRNDL)), and DD is greater
        'than the dying item DD limit, increment Dying Item Hits.
        If DITrend < 0 And DITrend < (Simdata.OP.DITrnDL / 52 * -1) _
            And Fcst > Simdata.OP.DIDDL Then
            DIHits = DIHits + 1
        Else
            DIHits = 0
        End If
    
    Next Pd
    
    'If the OldestPeriodFlag is false all the data points are exceptions
    If Not OldestPeriodFlag Then
        Simdata.DM.OldestPeriod = 0
    End If
    
    'Is this item an intermittent item ?
    If Simdata.It.FcstCycles > 0 Then
        PctZero = Simdata.It.ZeroCount / Simdata.It.FcstCycles
        
        Simdata.It.IsIntermittent = IIf(PctZero >= Simdata.Int_MinPctZero _
            And PctZero <= Simdata.Int_MaxPctZero, "Y", "N")
            
        'Test for an item with high seasonality
        If Simdata.It.IsIntermittent = "Y" And CalcSeasIndexRating(Simdata.Sa.Bi) >= Simdata.Int_SeasonalityIndex Then
            Simdata.It.IsIntermittent = "N"
        End If
    
    End If
    
    'Check for a pattern of high demand filter exceptions
    If FcstCycle_CY > 0 Then
        If (Total_HiTrps_Cy / FcstCycle_CY) > Simdata.OP.LumpyFilterPct Then
            'Set the Demand Filter Flag on to indicate Demand Filter Exceptions have been overriden
            Simdata.It.DmdFilterFlag = "Y"
            
            'Reset the Hi Demand Exception Flags
            For Pd = 1 To Simdata.DM.OldestPeriod
    
                If Simdata.DM.ByPassFlag(Pd) = BR_HIDEMAND Then
                    Simdata.DM.ByPassFlag(Pd) = BR_NOEXCEPTION
                    Simdata.It.ByPassPct = Simdata.It.ByPassPct - 1
                End If
                
            Next Pd
        End If
    End If

    'Check for an Item Status Change -- If an "Unordered" item had
    'non-zero demand then update it's status from "Unordered" to "New".
    If Simdata.It.ItStat = "U" And Simdata.It.FcstCycles - Simdata.It.ZeroCount > 0 Then
        Simdata.It.ItStat = "N"
    End If
    
    'Check for an Item Status Change -- If an "Unordered" or "New" item had
    'more non-zero demand update periods than the number of DD Moving Average Periods
    'specified for the item, it's status is changed from "New" to "Active".
    If (Simdata.It.FcstCycles - Simdata.It.ZeroCount) >= Simdata.OP.DDMAP _
        And (Simdata.It.ItStat = "N" Or Simdata.It.ItStat = "U") Then
        Simdata.It.ItStat = "A"
    End If
        
    'Check for an Item Status Change -- If a "Discontinued" item has reached it's
    ' inactive date, change the item's status to "Inactive"
'    If Date >= SimData.It.InActDate And SimData.It.ItStat = "D" Then
'        SimData.It.ItStat = "I"
'    End If
        
    'Test Dying Item Register for consecutive trips
    
    'If the number of consecutive trips is greater than or equal to
    'DITRPS set the MAD Status Flag (MADSTAT).
    If DIHits >= Simdata.OP.DITrps Then
        Simdata.It.DIFlag = "Y"
    End If
    
    'Test for a valid User Override
    'In order for a user override to be valid it must meet the
    'following criteria:
    '
    ' - The User Forecast must not exceed the Upper Control Limit
    ' -  and the User Forecast must not be lower than the Lower Control Limit
    ' - The Bypass Initial Demand Filter Limit must be set to "N"
    ' - The User Forecast Expiration Date must not have been reached
    If (Simdata.It.UserFcst > Fcst + (MAD * Simdata.OP.IDFL) _
    Or Simdata.It.UserFcst < Fcst - (MAD * Simdata.OP.IDFL)) _
    And Simdata.OP.BypIDFL = "N" _
    And Simdata.It.UserFcstExpDate > Date Then
             
       Simdata.It.UserDemandFlag = "Y"
    Else
        Simdata.It.UserDemandFlag = "N"
    End If
    
    'Convert the Bypass Count to a Percentage
    If Total_Cycles > 0 Then
        Simdata.It.ByPassPct = Simdata.It.ByPassPct / Total_Cycles
    Else
        Simdata.It.ByPassPct = 0
    End If
        
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(FilterDemand)"
     f_HandleErr , , , "AIMUtil::FilterDemand", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    ForecastOne(...)
'
' Description:      Calculates the forecast for the period specified
'                   (inclusive -- period is the beginning of the start date
'                   through the end of the end date).
'
' Parameters:       StartDate           Start Date
'                   EndDate             Ending Date
'                   SimData             Simulation Record
'
' Returns:          Forecast in Units
'
' By: RES           Date: 09/21/1999
'
' Sri Oct-20-2003
' Split origial forecast function this fucntion actully does the forecast
' to fix the bug 1006
'
'**********************************************************************
Function ForecastOne(StartDate As Date, EndDate As Date, ApplyTrend As Boolean, _
    ApplySeasonsIndex As String, Simdata As SIMULATION_RCD, FcstDemand As Double, ApplyAdj As Boolean, P_AIM_FcstAdj As ADODB.Command, P_rsAIM_FcstAdj As ADODB.Recordset) As Double
On Error GoTo ErrorHandler

    Dim CurPd As Integer
    Dim CurStartdate As Date
    Dim Demand As Double
   ' Dim FcstDemand As Double
    Dim FcstForPeriod As Double
    
    Dim EndWeekDays As Integer
    Dim EndWeekPct As Double
    
    Dim FirstStartDate As Date
    Dim LastStartDate As Date
    Dim StartWeekDays As Integer
    Dim StartWeekPct As Double
    
    Dim TrnPds As Integer
    Dim Trend As Double
    Dim Counter As Integer
    Counter = 1
    Dim FiscalYear As Integer
    Dim FcstAdj As Double
    

    
    'Calculate the First Date of the First Period
    FirstStartDate = GetStartDateFmDate(StartDate, Simdata.AIMYears)
  
    'Calculate the First Date of the Last Period
    LastStartDate = GetStartDateFmDate(EndDate, Simdata.AIMYears)
    
    'Calculate the number of working days in the first week
    StartWeekDays = GetWorkingDaysInWeek(StartDate, Simdata.AIMDays, Simdata.AIMYears)
    
    'Calculate the Start Week Percentages
    If StartWeekDays > 0 Then
        StartWeekPct = GetWorkingDays(StartDate, FirstStartDate + 6, Simdata.AIMDays, Simdata.AIMYears) / StartWeekDays
    Else
        StartWeekPct = 0
    End If
    
    'Calculate the number of working days in the last period
    EndWeekDays = GetWorkingDaysInWeek(EndDate, Simdata.AIMDays, Simdata.AIMYears)
    
    'Calculate the End Week Percentages
    If EndWeekDays > 0 Then
        EndWeekPct = GetWorkingDays(LastStartDate, EndDate, Simdata.AIMDays, Simdata.AIMYears) / EndWeekDays
    Else
        EndWeekPct = 0
    End If
    
    
    'Forecast from the Start Date through the End Date
    CurStartdate = FirstStartDate
    Do Until CurStartdate > EndDate
        CurPd = GetPeriod(CurStartdate, Simdata.AIMYears)
        FiscalYear = GetFiscalYear(CurStartdate, Simdata.AIMYears)
        If ApplyTrend And Simdata.It.PlnTT = "Y" Then
            TrnPds = DateDiff("d", Simdata.DM.BaseDate, CurStartdate) \ 7
            Trend = TrnPds * Simdata.It.Trend
            
            'Check for trend limit exception
            If Abs(Trend) > Abs(Simdata.OP.hitrndl * FcstDemand) Then
                If Trend >= 0 Then
                    Trend = FcstDemand * Simdata.OP.hitrndl
                Else
                    Trend = FcstDemand * Simdata.OP.hitrndl * -1
                End If
            End If
            
'            If Trend > (SimData.OP.hitrndl * FcstDemand) Then
'                Trend = FcstDemand * SimData.OP.hitrndl
'            End If

        Else
            TrnPds = 0
            Trend = 0
        End If
        
        'Apply the Seasonality Profile, if required
        Demand = IIf(ApplySeasonsIndex = "Y", (FcstDemand + Trend) * Simdata.Sa.Bi(CurPd), (FcstDemand + Trend))
        
            'Check for an active promotion
            'Promotion is Enabled, and
            '[Current Start Date] >= [Promotion Start Date] and [Current Start Date] <= [Promotion End Date]
            
            If Simdata.PM.PmStatus _
            And CurStartdate >= Simdata.PM.PmStartDate _
            And CurStartdate <= Simdata.PM.PmEndDate _
            Then
                Demand = Demand * Simdata.PM.PmAdj(CurPd)
            End If
        
        
      
        If CurStartdate = FirstStartDate And CurStartdate <> LastStartDate Then
            FcstForPeriod = Demand * StartWeekPct
            If ApplyAdj = True Then
                FcstAdj = GetAdjFcst(Simdata.It.Item, Simdata.It.LcId, CurStartdate, FcstForPeriod, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
                FcstForPeriod = FcstAdj
            End If
            
        ElseIf CurStartdate = FirstStartDate And CurStartdate = LastStartDate Then
            FcstForPeriod = Demand * (StartWeekPct - (1 - EndWeekPct))
            If ApplyAdj = True Then
                FcstAdj = GetAdjFcst(Simdata.It.Item, Simdata.It.LcId, CurStartdate, FcstForPeriod, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
                FcstForPeriod = FcstAdj
            End If
                
        ElseIf CurStartdate <> FirstStartDate And CurStartdate <> LastStartDate Then
            'Fixed bug sri trend should not be added  forecast going negitive
            'FcstForPeriod = FcstForPeriod + (Demand + Trend)
'            FcstForPeriod = FcstForPeriod + (Demand)
            If ApplyAdj = True Then
                FcstAdj = GetAdjFcst(Simdata.It.Item, Simdata.It.LcId, CurStartdate, Demand, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
                FcstForPeriod = FcstForPeriod + FcstAdj
            Else
                FcstForPeriod = FcstForPeriod + (Demand) 'origal code
            End If
           
            
        ElseIf CurStartdate <> FirstStartDate And CurStartdate = LastStartDate Then
            'Fixed bug sri trend should not be added  forecast going negitive
            'FcstForPeriod = FcstForPeriod + ((Demand + Trend) * EndWeekPct)
            'FcstForPeriod = FcstForPeriod + ((Demand) * EndWeekPct)
            If ApplyAdj = True Then
                FcstAdj = GetAdjFcst(Simdata.It.Item, Simdata.It.LcId, CurStartdate, (Demand) * EndWeekPct, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
                FcstForPeriod = FcstForPeriod + FcstAdj
            Else
                FcstForPeriod = FcstForPeriod + ((Demand) * EndWeekPct)
            End If
            
            Exit Do
        End If
        
        'Adjust for negative forecast
        FcstForPeriod = IIf(FcstForPeriod >= 0, FcstForPeriod, 0)
    
        'Next Week
        CurStartdate = CurStartdate + 7
        Counter = Counter + 1
        
    Loop
   ForecastOne = FcstForPeriod

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(ForecastOne)"
     f_HandleErr , , , "AIMUtil::ForecastOne", Now, gDRJobError, True, Err
End Function
'**********************************************************************
' Function Name:    Forecast(...)
'
' Description:      Calculates the forecast for the period specified
'                   (inclusive -- period is the beginning of the start date
'                   through the end of the end date).
'
' Parameters:       StartDate           Start Date
'                   EndDate             Ending Date
'                   SimData             Simulation Record
'
' Returns:          Forecast in Units
'
' By: RES           Date: 09/21/1999
'
' Oct-20-2003   Srinivas  Modified code to fix bug 1006
' this  most of this functions functioality is moved to forcastone
'**********************************************************************
Function Forecast(StartDate As Date, EndDate As Date, ApplyTrend As Boolean, _
    ApplySeasonsIndex As String, Simdata As SIMULATION_RCD, AdjFcst As Boolean, P_AIM_FcstAdj As ADODB.Command, P_rsAIM_FcstAdj As ADODB.Recordset) As Double
On Error GoTo ErrorHandler

   
    Dim FcstDemand As Double
    Dim FirstStartDate As Date
    Dim FcstForPeriod As Double
    'Check for invalid start and end dates
    If EndDate < StartDate Then
        Forecast = 0
        Exit Function
    End If
    
    'Calculate the First Date of the First Period
    FirstStartDate = GetStartDateFmDate(StartDate, Simdata.AIMYears)
    
    'Check for invalid FirstStartDate due to Simdata.AIMYears and Simdata.AIMDays not properly populated
    'So check for Invalid year of 1900
    If Year(FirstStartDate) = 1900 Then
        Forecast = 0
        Exit Function
    End If
    
    'sri Start
     'Check for a User Override on Forecast Demand - No Seasonality or Trend is applied to User Overrides
    'Sri change the ApplySeasonsIndex to Flase from N  for bug 1006
    If Simdata.It.UserFcst > 0 And Simdata.It.UserFcstExpDate > FirstStartDate Then
        If EndDate <= Simdata.It.UserFcstExpDate Then
            FcstDemand = Simdata.It.UserFcst
            FcstForPeriod = ForecastOne(StartDate, EndDate, False, False, Simdata, FcstDemand, AdjFcst, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
        Else
            FcstDemand = Simdata.It.UserFcst 'use userfcst for this period
            FcstForPeriod = ForecastOne(StartDate, Simdata.It.UserFcstExpDate, ApplyTrend, ApplySeasonsIndex, Simdata, FcstDemand, AdjFcst, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
            FcstDemand = Simdata.It.FcstDemand  'use fcstdemand for this period
            FcstForPeriod = FcstForPeriod + ForecastOne(Simdata.It.UserFcstExpDate + 1, EndDate, ApplyTrend, ApplySeasonsIndex, Simdata, FcstDemand, AdjFcst, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
        End If
    Else
        FcstDemand = Simdata.It.FcstDemand
        FcstForPeriod = ForecastOne(StartDate, EndDate, ApplyTrend, ApplySeasonsIndex, Simdata, FcstDemand, AdjFcst, P_AIM_FcstAdj, P_rsAIM_FcstAdj)

    End If
     
    'Adjust Forecast for Location Demand Scaling
    If Date <= Simdata.LC.ScalingEffUntil Then
        FcstForPeriod = FcstForPeriod * Simdata.LC.DmdScalingFactor
    End If
    'Adjust for negative forecast
    'Fixed bug sri forecast going negitive
        FcstForPeriod = IIf(FcstForPeriod >= 0, FcstForPeriod, 0)
    'Return Forecast
    Forecast = Round(FcstForPeriod, 2)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Forecast)"
     f_HandleErr , , , "AIMUtil::Forecast", Now, gDRJobError, True, Err
End Function




'**********************************************************************
' Function Name:    OrderPolicyUpdate()
'
' Description:      This function updates the order policy data elements
'                   in an item record for subsequent use by the order
'                   generation module.
'
' Parameters:       SimData         Simulation Record
'
'Returns:           FAIL
'                   SUCCEED
'
' By: RES           Date: 09/21/1999
'
''NOTE: Overloaded function (with the same name but different signature) -- exists in AIMAdvForecast.cls
'Therefore, enforcing scope=Private
'**********************************************************************
Private Function OrderPolicyUpdate(Simdata As SIMULATION_RCD, AIMMethods() As METHOD_RCD, P_ApplyMasterAdj As Boolean, P_AIM_FcstAdj As ADODB.Command, P_rsAIM_FcstAdj As ADODB.Recordset)
On Error GoTo ErrorHandler
    
    Dim Demand As Double            'Forecast Demand
    Dim MAE As Double               'Mean Absolute Error
    Dim RtnCode As Integer          'Return Code
    Dim Turns As Double             'Estimated Turns
    
    Dim IntSrvLvl As Double         'Intermittent Service Level
    Dim MaxDemand As Double         'Maximum Demand - From Intermittent Demand Model
    Dim ZeroCount As Integer        'Zero Count for Look Back Period
    Dim CompItemStartDate   As Date
    Dim CompItemEndDate As Date
    Dim tempDate As Date
    
    
        
    'Calculate the Quantity Consumed during the Lead Time
    Simdata.It.FcstLT = Forecast(Simdata.DM.BaseDate, Simdata.DM.BaseDate + (Simdata.It.Accum_Lt - 1), _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)

    'Calculate the Forecast for the Item's Review Time
    Simdata.It.FcstRT = Forecast(Simdata.DM.BaseDate + Simdata.It.Accum_Lt, _
        Simdata.DM.BaseDate + (Simdata.It.Accum_Lt + Simdata.It.ReviewTime - 1), _
        AIMMethods(Simdata.It.FcstMethod).ApplyTrend, _
        AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    'Calculate the Forecast for the next month, quarter, and year
    Simdata.It.Fcst_Month = Forecast(Simdata.DM.BaseDate, _
        DateAdd("m", 1, Simdata.DM.BaseDate) - 1, _
        AIMMethods(Simdata.It.FcstMethod).ApplyTrend, _
        AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)

    Simdata.It.Fcst_Quarter = Forecast(Simdata.DM.BaseDate, _
        DateAdd("q", 1, Simdata.DM.BaseDate) - 1, _
        AIMMethods(Simdata.It.FcstMethod).ApplyTrend, _
        AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)

    Simdata.It.Fcst_Year = Forecast(Simdata.DM.BaseDate, _
        DateAdd("yyyy", 1, Simdata.DM.BaseDate) - 1, _
        AIMMethods(Simdata.It.FcstMethod).ApplyTrend, _
        AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)


    'Apply High and Low Mean Absolute Error Filters
    MAE = Simdata.It.MAE
    
    If Simdata.It.FcstDemand > 0 Then
        If Simdata.It.MAE / Simdata.It.FcstDemand < Simdata.OP.LoMADP Then
            MAE = Simdata.OP.LoMADP * Simdata.It.FcstDemand
        End If
        
        If Simdata.It.MAE / Simdata.It.FcstDemand > Simdata.OP.HiMADP Then
            MAE = Simdata.OP.HiMADP * Simdata.It.FcstDemand
        End If
    End If
    
    'Check forecast for a user override
    If Simdata.It.UserFcst > 0 And Simdata.It.UserFcstExpDate > Simdata.DM.BaseDate Then
        Demand = Simdata.It.UserFcst
        MAE = 0.4 * Simdata.It.UserFcst
    Else
        Demand = Simdata.It.FcstDemand
    End If
    
    'Is this a dying item -- If so restrict the Mean Absolute Error
    If Simdata.It.DIFlag = "Y" And Simdata.It.FcstDemand > 0 Then
        If (MAE / Simdata.It.FcstDemand) > Simdata.OP.DIMADP Then
            MAE = Simdata.OP.DIMADP * Simdata.It.FcstDemand
        End If
    End If

    'Calculate Safety Stock
    Simdata.It.SafetyStock = CalcSafetyStock(Simdata.It.FcstLT + Simdata.It.FcstRT, Demand, MAE, _
        Simdata.It.Accum_Lt + Simdata.It.ReviewTime, Simdata.It.DSer, Simdata.OP.MADExK)
    
    'Calculate Intermittent Safety Stock; if applicable
    If Simdata.It.IsIntermittent = "Y" Then
        'Calculate the non-zero mean, non-zero standard deviation, and zero count
        Simdata.It.StdDev_NZ = _
            NZ_StdDev(Simdata.DM.Cps(), 1, Simdata.Int_LookBackPds, Simdata.It.Mean_NZ, ZeroCount)
        
        'Determine which service level goal to use
        If Simdata.Int_SrvLvlOverrideFlag = "Y" Then
            IntSrvLvl = Simdata.Int_SrvLvl
        Else
            IntSrvLvl = Simdata.It.DSer
        End If
        
        'Calculate the maximum demand during the accumulative lead time
        MaxDemand = GetMaxDemand(Simdata.It.Accum_Lt, (ZeroCount / Simdata.Int_LookBackPds), _
            Simdata.It.Mean_NZ, Simdata.It.StdDev_NZ, IntSrvLvl, 1000)
        
        'Calculate the Intermittent Safety Stock
        Simdata.It.IntSafetyStock = MaxDemand - ((Simdata.It.Accum_Lt + Simdata.It.ReviewTime) / 7) * Simdata.It.FcstDemand
        
        'Select the larger of the two
        If Simdata.It.IntSafetyStock > Simdata.It.SafetyStock And Simdata.Int_Enabled = "Y" Then
            Simdata.It.SafetyStock = Simdata.It.IntSafetyStock
        End If
    
    End If
    
    'Apply Safety Stock Adjustments
    Simdata.It.SafetyStock = Simdata.It.SafetyStock * (1 + Simdata.It.LtvFact) * (1 + Simdata.It.SSAdj)

    'Perform counter stock test
    If Simdata.It.SafetyStock < Simdata.It.CStock And Simdata.It.ZSStock <> "Y" Then
        Simdata.It.SafetyStock = Simdata.It.CStock
    End If

    'Check zero safety stock switch
    If Simdata.It.ZSStock = "Y" Then
        Simdata.It.SafetyStock = 0
    End If

    'Calculate the order point; round the results
    Simdata.It.OrderPt = Round(Simdata.It.FcstRT + Simdata.It.FcstLT _
        + Simdata.It.SafetyStock, 0)

    'Check for a Zero Order Point
    If Simdata.It.OrderPt = 0 And Simdata.It.ZOPSw = "N" Then
        Simdata.It.OrderPt = 1
    End If
    
    'Calculate the Economic Order Quantity for an item
    If Simdata.It.BkQty(1) = 0 Then
        Simdata.It.OrderQty = g_EOQ(Simdata.LC.ReplenCost + Simdata.It.ReplenCost2, _
            Simdata.KFactor, Simdata.It.FcstDemand, Simdata.It.Cost)
    Else
        Simdata.It.OrderQty = g_QuantityBreakEOQ(Simdata.It.FcstDemand, _
            Simdata.KFactor, Simdata.LC.ReplenCost + Simdata.It.ReplenCost2, _
            Simdata.It.BkQty(), Simdata.It.BkCost())
    End If

   If Simdata.It.FcstDemand > 0 Then
        'Test against the high and low turn limits
        If ((Simdata.It.OrderQty + Simdata.It.FcstRT) / 2 + Simdata.It.SafetyStock) > 0 Then
            Turns = (Simdata.It.FcstDemand * 52) _
                / ((Simdata.It.OrderQty + Simdata.It.FcstRT) / 2 + Simdata.It.SafetyStock)
        Else
            Turns = 0
        End If

        If Turns > Simdata.OP.Dft_TurnHigh Then
            If Simdata.OP.Dft_TurnHigh <> 0 Then
                Simdata.It.OrderQty = 2 * (((Simdata.It.FcstDemand * 52) _
                    / Simdata.OP.Dft_TurnHigh) - Simdata.It.SafetyStock) - Simdata.It.FcstRT
            Else
                Simdata.It.OrderQty = 2 * (((Simdata.It.FcstDemand * 52) _
                    / 1) - Simdata.It.SafetyStock) - Simdata.It.FcstRT
            End If

        ElseIf Turns < Simdata.OP.Dft_TurnLow Then
            If Simdata.OP.Dft_TurnLow <> 0 Then
                Simdata.It.OrderQty = 2 * (((Simdata.It.FcstDemand * 52) / Simdata.OP.Dft_TurnLow) _
                    - Simdata.It.SafetyStock) - Simdata.It.FcstRT
            Else
                Simdata.It.OrderQty = 2 * (((Simdata.It.FcstDemand * 52) / 1) _
                    - Simdata.It.SafetyStock) - Simdata.It.FcstRT
            End If

        End If

    Else
        Simdata.It.OrderQty = 0

    End If
    
    'Test for Maximum Order Quantity (1 year)
    If Simdata.It.OrderQty > Simdata.It.FcstDemand * 52 Then
        Simdata.It.OrderQty = Simdata.It.FcstDemand * 52
    End If

    'Test for Minimum Order Quantity (Based on Review Time)
    If Simdata.It.OrderQty < Simdata.It.FcstRT Then
        Simdata.It.OrderQty = Simdata.It.FcstRT
    End If

    'Test for Minimum Order Quantity (Minimum 2 Day Supply)
    If Simdata.It.OrderQty < Simdata.It.FcstDemand / 3.5 Then
        Simdata.It.OrderQty = Simdata.It.FcstDemand / 3.5
    End If

    'Test for Minimum Order Quantity (1 unit)
    If Simdata.It.OrderQty < 1 Then
        Simdata.It.OrderQty = 1
    End If
    
    
    'New code for companion item demand

Dim Lrow As Integer
Dim URow As Integer
Dim Row As Integer
Dim Index As Integer
If UBound(Simdata.KI.LT) > 0 Then

Lrow = UBound(Simdata.KIDetail)
If Lrow > 1 Then Lrow = Lrow + 1

URow = Lrow + UBound(Simdata.KI.LT) - 1

ReDim Preserve Simdata.KIDetail(1 To URow)
Index = 1
For Row = Lrow To URow
    Simdata.KIDetail(Row).LcId = Simdata.KI.LcId
    Simdata.KIDetail(Row).Item = Simdata.KI.Item
    Simdata.KIDetail(Row).FcstStartPd = Simdata.KI.LT(Index)
    Simdata.KIDetail(Row).FcstEndPd = Simdata.KI.LT_RT(Index)
    Simdata.KIDetail(Row).StartDate = Simdata.KI.StartDate(Index)
    Simdata.KIDetail(Row).EndDate = Simdata.KI.EndDate(Index)
    Simdata.KIDetail(Row).Fcst = Simdata.It.FcstDemand
    Simdata.KIDetail(Row).MAE = Simdata.It.MAE
    
    If Simdata.KIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.KIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If
    If Simdata.KIDetail(Row).EndDate < Simdata.DM.BaseDate + Simdata.KI.LT(Index) - 1 Then
        CompItemEndDate = Simdata.KIDetail(Row).EndDate
    Else
        CompItemEndDate = Simdata.DM.BaseDate + Simdata.KI.LT(Index) - 1
    End If
    
     Simdata.KIDetail(Row).FcstLT = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)

    If Simdata.KIDetail(Row).StartDate > Simdata.DM.BaseDate + Simdata.KI.LT(Index) Then
        CompItemStartDate = Simdata.KIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate + Simdata.KI.LT(Index)
    End If
    If Simdata.KIDetail(Row).EndDate < Simdata.DM.BaseDate + Simdata.KI.LT_RT(Index) - 1 Then
        CompItemEndDate = Simdata.KIDetail(Row).EndDate
    Else
        CompItemEndDate = Simdata.DM.BaseDate + Simdata.KI.LT_RT(Index) - 1
    End If
    
     Simdata.KIDetail(Row).FcstRT = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    If Simdata.KIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.KIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If

    If Simdata.KIDetail(Row).EndDate < DateAdd("m", 1, Simdata.DM.BaseDate) - 1 Then
        CompItemEndDate = Simdata.KIDetail(Row).EndDate
    Else
        CompItemEndDate = DateAdd("m", 1, Simdata.DM.BaseDate) - 1
    End If
    
    Simdata.KIDetail(Row).FcstMonth = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    If Simdata.KIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.KIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If
    If Simdata.KIDetail(Row).EndDate < DateAdd("q", 1, Simdata.DM.BaseDate) - 1 Then
        CompItemEndDate = Simdata.KIDetail(Row).EndDate
    Else
        CompItemEndDate = DateAdd("q", 1, Simdata.DM.BaseDate) - 1
    End If
    
    Simdata.KIDetail(Row).FcstQtr = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    
     If Simdata.KIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.KIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If
    If Simdata.KIDetail(Row).EndDate < DateAdd("yyyy", 1, Simdata.DM.BaseDate) - 1 Then
        CompItemEndDate = Simdata.KIDetail(Row).EndDate
    Else
        CompItemEndDate = DateAdd("yyyy", 1, Simdata.DM.BaseDate) - 1
    End If
    
    Simdata.KIDetail(Row).FcstYear = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
       

       Index = Index + 1
Next
    ReDim Preserve Simdata.KI.LT(0 To 0)
    ReDim Preserve Simdata.KI.LT_RT(0 To 0)
    ReDim Preserve Simdata.KI.EndDate(0 To 0)
    ReDim Preserve Simdata.KI.StartDate(0 To 0)

End If





'start modify

If UBound(Simdata.CI.LT) > 0 Then

Lrow = UBound(Simdata.CIDetail)
If Lrow > 1 Then Lrow = Lrow + 1

URow = Lrow + UBound(Simdata.CI.LT) - 1

ReDim Preserve Simdata.CIDetail(1 To URow)
Index = 1
For Row = Lrow To URow
    Simdata.CIDetail(Row).LcId = Simdata.CI.LcId
    Simdata.CIDetail(Row).Item = Simdata.CI.Item
    Simdata.CIDetail(Row).FcstStartPd = Simdata.CI.LT(Index)
    Simdata.CIDetail(Row).FcstEndPd = Simdata.CI.LT_RT(Index)
    Simdata.CIDetail(Row).StartDate = Simdata.CI.StartDate(Index)
    Simdata.CIDetail(Row).EndDate = Simdata.CI.EndDate(Index)
    Simdata.CIDetail(Row).Fcst = Simdata.It.FcstDemand
    Simdata.CIDetail(Row).MAE = Simdata.It.MAE
    If Simdata.CIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.CIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If
    If Simdata.CIDetail(Row).EndDate < Simdata.DM.BaseDate + Simdata.CI.LT(Index) - 1 Then
        CompItemEndDate = Simdata.CIDetail(Row).EndDate
    Else
        CompItemEndDate = Simdata.DM.BaseDate + Simdata.CI.LT(Index) - 1
    End If
    
     Simdata.CIDetail(Row).FcstLT = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)

    If Simdata.CIDetail(Row).StartDate > Simdata.DM.BaseDate + Simdata.CI.LT(Index) Then
        CompItemStartDate = Simdata.CIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate + Simdata.CI.LT(Index)
    End If
    If Simdata.CIDetail(Row).EndDate < Simdata.DM.BaseDate + Simdata.CI.LT_RT(Index) - 1 Then
        CompItemEndDate = Simdata.CIDetail(Row).EndDate
    Else
        CompItemEndDate = Simdata.DM.BaseDate + Simdata.CI.LT_RT(Index) - 1
    End If
    
     Simdata.CIDetail(Row).FcstRT = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    If Simdata.CIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.CIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If

    If Simdata.CIDetail(Row).EndDate < DateAdd("m", 1, Simdata.DM.BaseDate) - 1 Then
        CompItemEndDate = Simdata.CIDetail(Row).EndDate
    Else
        CompItemEndDate = DateAdd("m", 1, Simdata.DM.BaseDate) - 1
    End If
    
    Simdata.CIDetail(Row).FcstMonth = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    If Simdata.CIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.CIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If
    If Simdata.CIDetail(Row).EndDate < DateAdd("q", 1, Simdata.DM.BaseDate) - 1 Then
        CompItemEndDate = Simdata.CIDetail(Row).EndDate
    Else
        CompItemEndDate = DateAdd("q", 1, Simdata.DM.BaseDate) - 1
    End If
    
    Simdata.CIDetail(Row).FcstQtr = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
    
    
     If Simdata.CIDetail(Row).StartDate > Simdata.DM.BaseDate Then
        CompItemStartDate = Simdata.CIDetail(Row).StartDate
    Else
        CompItemStartDate = Simdata.DM.BaseDate
    End If
    If Simdata.CIDetail(Row).EndDate < DateAdd("yyyy", 1, Simdata.DM.BaseDate) - 1 Then
        CompItemEndDate = Simdata.CIDetail(Row).EndDate
    Else
        CompItemEndDate = DateAdd("yyyy", 1, Simdata.DM.BaseDate) - 1
    End If
    
    Simdata.CIDetail(Row).FcstYear = Forecast(CompItemStartDate, CompItemEndDate, _
       AIMMethods(Simdata.It.FcstMethod).ApplyTrend, AIMMethods(Simdata.It.FcstMethod).ApplySeasonsIndex, Simdata, P_ApplyMasterAdj, P_AIM_FcstAdj, P_rsAIM_FcstAdj)
       

       Index = Index + 1
Next
    ReDim Preserve Simdata.CI.LT(0 To 0)
    ReDim Preserve Simdata.CI.LT_RT(0 To 0)
    ReDim Preserve Simdata.CI.EndDate(0 To 0)
    ReDim Preserve Simdata.CI.StartDate(0 To 0)

End If

            
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(OrderPolicyUpdate)"
     f_HandleErr , , , "AIMUtil::OrderPolicyUpdate", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    g_EOQ()
'
' Description:      Calculates the Economic Order Quantity.
'
' Parameters:       R               Cost of Ordering
'                   K               Cost of Carring Inventory (%)
'                                   K should be passed in this format:  0.00
'                   DailyDemand     Estimated Daily Demand
'                   UnitCost        Unit Cost
'
' Returns:          Economic Order Quantity
'
' By: RES           Date: 01/26/96
'
'
'**********************************************************************
Function g_EOQ(r As Double, K As Double, FcstDemand As Double, _
    UnitCost As Double)
On Error GoTo ErrorHandler

    'Check for invalid values
    If K <= 0 Or UnitCost <= 0 Then
        g_EOQ = 0
        Exit Function
    End If
    
    g_EOQ = Round(Sqr((104 * r * FcstDemand) / (K / 100 * UnitCost)), 0)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(g_EOQ)"
     f_HandleErr , , , "AIMUtil::g_EOQ", Now, gDRJobError, True, Err
End Function


Public Function g_QuantityBreakEOQ(Demand As Double, KFactor As Double, _
    ReplenCost As Double, BkQty() As Long, BkCost() As Double)
On Error GoTo ErrorHandler
    
    Dim EOQ_BK(1 To 10) As Long
    Dim EOQ_TAC(1 To 10) As Double
    Dim i As Integer
    Dim LowestTAC As Double
    Dim MaxBk As Integer
    Dim OQ As Long
    Dim TAC(1 To 10) As Double
    Dim TestEOQ As Long
    
    'Determine extend of the Quantity Break Array
    For i = LBound(BkQty) To UBound(BkQty)
        If BkQty(i) = 0 Then
            MaxBk = i - 1
            Exit For
        End If
    Next i
    
    'Test for an empty array
    If MaxBk = 0 Then
        g_QuantityBreakEOQ = 0
        Exit Function
    End If
    
    'Calculate EOQ using minimum price; if valid use this price
    TestEOQ = g_EOQ(ReplenCost, KFactor, Demand, BkCost(MaxBk))
    
    If TestEOQ >= BkQty(MaxBk) Then
        g_QuantityBreakEOQ = TestEOQ
        Exit Function
    End If
    
    'If not valid, calculate Total Annual Cost (TAC) for each break point,
    ' the EOQ for each Break Cost, and the TAC for each valid EOQ
    For i = LBound(BkQty) To MaxBk
        TAC(i) = TotalAnnualCost(Demand, KFactor, ReplenCost, BkQty(i), BkCost(i))
        
        EOQ_BK(i) = g_EOQ(ReplenCost, KFactor, Demand, BkCost(i))
        
        If EOQ_BK(i) >= BkQty(i) Then
            EOQ_TAC(i) = TotalAnnualCost(Demand, KFactor, ReplenCost, EOQ_BK(i), BkCost(i))
        End If
        
    Next i

    'Determine the minimum cost
    LowestTAC = TAC(MaxBk)
    OQ = BkQty(MaxBk)
    
    For i = LBound(BkQty) To MaxBk
        If TAC(i) < LowestTAC Then
            LowestTAC = TAC(i)
            OQ = BkQty(i)
        End If
        
        If EOQ_BK(i) >= BkQty(i) And EOQ_TAC(i) < LowestTAC Then
            LowestTAC = EOQ_TAC(i)
            OQ = EOQ_BK(i)
        End If
    Next i
    
    g_QuantityBreakEOQ = OQ
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(g_QuantityBreakEOQ)"
     f_HandleErr , , , "AIMUtil::g_QuantityBreakEOQ", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    CalcSafetyStock(FcstLT, FcstDemand, MAE, LeadTime,
'                   ServiceLvl, MADExk)
'
' Description:      Calculates Safety Stock for an item based on the
'                   replenishment lead time, deseasonalized demand,
'                   Mean Absolute Deviation -- Safety Stock, and the
'                   desired Service Level.
'
' Parameters:       FcstLT              Forecast for Lead Time
'                   FcstDemand          Forecast for One Period
'                   MAE                 Mean Absolute Error
'                   LeadTime            Accumulative Lead Time in Days
'                   ServiceLvl          Service Level
'                   MADExk              Mean Absolute Error Extrapolation Constant
'
' Returns:          Safety Stock Quantity
'
' By: RES          Date: 09/23/1999
'
'
'**********************************************************************
Function CalcSafetyStock(FcstLT As Double, FcstDemand As Double, MAE As Double, LeadTime As Integer, _
    ServiceLvl As Double, MADExK As Double)
On Error GoTo ErrorHandler
    
    'Check for a zero deseasonalized demand or a
    'zero lead time
    If FcstDemand <= 0 Or LeadTime <= 0 Then
        CalcSafetyStock = 0
        Exit Function
    End If
    
    'Calculate Safety Stock
    ' [Average Weekly Forecast for the Lead Time Period] * [SafetyFactory(ServiceLvl)]
    '   * [Mean Absolute Error] / [Weekly Forecast Demand] * 1.25
    '   * SquareRoot([LeadTime / 7])
    '
    'The Safety Factor is the number of standard deviation required to achieve a Desired
    'Service Level
    '
    'The SquareRoot of the [Lead Time] / 7 adjusts safety stock for the difference in length between
    'the forecast period and the lead time. Intuitively as lead time increased, the proportional
    'requirement for safety stock diminishes. For example, a three week lead time would adjust safety
    'stock by SquareRoot(3) or 1.732 rather than 3. A ten week lead time would adjust safety stock
    'by 3.162 rather than 10. Conversely very short lead times would result in a proportionally higher
    'safety stock level. For example, a lead time of 2 days would adjust safety stock by .535 rather
    'than the 2/7 or .286.
    
    'Key factors in safety stock:
    ' Forecast Demand
    ' Mean Absolute Error
    ' Desired Service Level
    ' Lead Time
    
    CalcSafetyStock = ((FcstLT * 7) / LeadTime) * SafetyFactor(ServiceLvl) * ((MAE / FcstDemand) * 1.25) * ((LeadTime / 7) ^ MADExK)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CalcSafetyStock)"
     f_HandleErr , , , "AIMUtil::CalcSafetyStock", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    SafetyFactor(ServiceFunction)
'
' Description:      Calculates an approximation of the Inverse Cumulative
'                   Normal Distribution based on an algorith from Abramowitz
'                   and Stegun.
'
' Parameters:       ServiceLvl      Service Level
'
' Returns:          Safety Factor
'
' By: RES           Date: 02/22/2001
'
'
'**********************************************************************
Function SafetyFactor(ByVal ServiceLvl As Double) As Double
On Error GoTo ErrorHandler

    Dim t As Double
    
    Select Case ServiceLvl
        Case Is < 0.0000002867
            SafetyFactor = -5
            Exit Function
        
        Case Is > 0.9999997133
            SafetyFactor = 5
            Exit Function
        
        Case Is > 0.5
            ServiceLvl = 1 - ServiceLvl
            t = Sqr(Log(1 / ServiceLvl ^ 2))
            SafetyFactor = Round(t - ((2.515517 + (0.802853 * t) + (0.010328 * t ^ 2)) / (1 + (1.432788 * t) + (0.189269 * t ^ 2) + (0.001308 * t ^ 3))), 4)
            
        Case Is <= 0.5
            t = Sqr(Log(1 / ServiceLvl ^ 2))
            SafetyFactor = -1 * Round(t - ((2.515517 + (0.802853 * t) + (0.010328 * t ^ 2)) / (1 + (1.432788 * t) + (0.189269 * t ^ 2) + (0.001308 * t ^ 3))), 4)
    
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(SafetyFactor)"
     f_HandleErr , , , "AIMUtil::SafetyFactor", Now, gDRJobError, True, Err
End Function


Function MADInit(Fcst As Double)
On Error GoTo ErrorHandler

    Select Case Fcst
        Case Is < 2
            MADInit = 0.8 * Fcst
        Case Is < 5
            MADInit = 0.6 * Fcst
        Case Is < 10
            MADInit = 0.5 * Fcst
        Case Is < 50
            MADInit = 0.4 * Fcst
        Case Is < 100
            MADInit = 0.4 * Fcst
        Case Is < 1000
            MADInit = 0.4 * Fcst
        Case Else
            MADInit = 0.4 * Fcst
        
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(MADInit)"
     f_HandleErr , , , "AIMUtil::MADInit", Now, gDRJobError, True, Err
End Function


Function GetServerDate() As Date
On Error GoTo ErrorHandler
    
    Dim Cn As ADODB.Connection
    Dim rs As ADODB.Recordset
    
    Dim RtnCode As Integer
    Dim strSQL As String
    
    Set Cn = New ADODB.Connection
    Set rs = New ADODB.Recordset

    'Open a connection to the server
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    
    If RtnCode = SUCCEED Then
        'Get the current server date/time
        strSQL = "select GetDate = getdate() "
        rs.Open strSQL, Cn, adOpenStatic, adLockReadOnly
        
        If f_IsRecordsetOpenAndPopulated(rs) Then
            GetServerDate = rs("GetDate").Value
        End If
        
    End If
    
    'House Keeping
    If f_IsRecordsetValidAndOpen(rs) Then rs.Close
    Set rs = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rs) Then rs.Close
    Set rs = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetServerDate)"
     f_HandleErr , , , "AIMUtil::GetServerDate", Now, gDRJobError, True, Err
End Function


Public Function Method10(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    '[(2 * MovAvg(1,13)) + MovAvg(14,26)] / 3
    
    'Initialize Trend
    Trend = MA.Trend(0)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        If MA.Pd001_013(Pd) + MA.Pd001_013(Pd) + MA.Pd014_026(Pd) > 0 Then
            Fcst = (MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA001_013(Pd) * MA.Pd001_013(Pd) _
                + MA.MA014_026(Pd) * MA.Pd014_026(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd001_013(Pd) + MA.Pd014_026(Pd))
        End If
            
        AccumFcstStatistics Pd, Fcst, Trend, 1, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 26 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method10 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Method10)"
     f_HandleErr , , , "AIMUtil::Method10", Now, gDRJobError, True, Err
End Function


Public Function Method11(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    'MovAvg(1,13) + [[MovAvg(40,52) - MovAvg(53,65)] / 13]
    
    'Initialize Trend
    Trend = MA.Trend(0)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        Fcst = MA.MA001_013(Pd) + ((MA.MA040_052(Pd) - MA.MA053_065(Pd)) / 13)
    
        AccumFcstStatistics Pd, Fcst, Trend, 1, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 65 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method11 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method11)"
     f_HandleErr , , , "AIMUtil::method11", Now, gDRJobError, True, Err
End Function


Public Function Method13(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Fcst As Double
    Dim Pd As Integer
    Dim Trend As Double
    
    '[MovAvg(40,52) + MovAvg(92,104)] / 2 + Trend
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        If MA.Pd040_052(Pd) + MA.Pd092_104(Pd) > 0 Then
        
            Trend = MA.Trend(Pd) * 52
    
            Fcst = (MA.MA040_052(Pd) * MA.Pd040_052(Pd) _
                + MA.MA092_104(Pd) * MA.Pd092_104(Pd)) _
                / (MA.Pd040_052(Pd) + MA.Pd092_104(Pd)) _
                + Trend
                
        End If
        
        AccumFcstStatistics Pd, Fcst, MA.Trend(Pd), 1, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 104 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method13 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method13)"
     f_HandleErr , , , "AIMUtil::method13", Now, gDRJobError, True, Err
End Function


Public Function Method14(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler
    
    Dim Bi As Double
    Dim DS001_013 As Double
    Dim DS014_026 As Double
    Dim DS027_039 As Double
    Dim DS040_052 As Double
    Dim DS053_065 As Double
    Dim DS066_078 As Double
    Dim DS079_091 As Double
    Dim DS092_104 As Double
    Dim Fcst As Double
    Dim i As Integer
    Dim Pd As Integer
    Dim si As Integer
    Dim SI001_013 As Double
    Dim SI014_026 As Double
    Dim SI027_039 As Double
    Dim SI040_052 As Double
    Dim Trend As Double
    
    'MovAvg(1,104) + SeaAdj
    
    'Initialize Trend
    Trend = MA.Trend(0)
    
    'Initialize Seasonality
    Bi = 1
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        'Initialize Seasonality Indices
        SI001_013 = 0
        SI014_026 = 0
        SI027_039 = 0
        SI040_052 = 0
        
        'Calculate Seasonality Indices
        For i = Pd + 1 To Pd + 13
            SI001_013 = SI001_013 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i))
            SI014_026 = SI014_026 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 13))
            SI027_039 = SI027_039 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 26))
            SI040_052 = SI040_052 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 39))
        Next i
        
        SI001_013 = Max(SI001_013 / 13, Simdata.OP.MinBi)
        SI014_026 = Max(SI014_026 / 13, Simdata.OP.MinBi)
        SI027_039 = Max(SI027_039 / 13, Simdata.OP.MinBi)
        SI040_052 = Max(SI040_052 / 13, Simdata.OP.MinBi)
        
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) _
            + MA.Pd053_065(Pd) + MA.Pd066_078(Pd) + MA.Pd079_091(Pd) + MA.Pd092_104(Pd) > 0 Then
            
            DS001_013 = MA.MA001_013(Pd) / SI001_013
            DS014_026 = MA.MA014_026(Pd) / SI014_026
            DS027_039 = MA.MA027_039(Pd) / SI027_039
            DS040_052 = MA.MA040_052(Pd) / SI040_052
            DS053_065 = MA.MA053_065(Pd) / SI001_013
            DS066_078 = MA.MA066_078(Pd) / SI014_026
            DS079_091 = MA.MA079_091(Pd) / SI027_039
            DS092_104 = MA.MA092_104(Pd) / SI040_052
           
            si = GetAbsPd(Pd)
            si = GetHsPeriod(Simdata.DM.EndPeriod, si)
            
            Fcst = (DS001_013 * MA.Pd001_013(Pd) _
                + DS014_026 * MA.Pd014_026(Pd) _
                + DS027_039 * MA.Pd027_039(Pd) _
                + DS040_052 * MA.Pd040_052(Pd) _
                + DS053_065 * MA.Pd053_065(Pd) _
                + DS066_078 * MA.Pd066_078(Pd) _
                + DS079_091 * MA.Pd079_091(Pd) _
                + DS092_104 * MA.Pd092_104(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) _
                + MA.Pd053_065(Pd) + MA.Pd066_078(Pd) + MA.Pd079_091(Pd) + MA.Pd092_104(Pd)) _
                * Simdata.Sa.Bi(si)
        
            'Update Current Seasonality Index
            Bi = Simdata.Sa.Bi(si)
        
        End If

        AccumFcstStatistics Pd, Fcst, Trend, Bi, FcstResults, Simdata
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 104 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method14 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method14)"
    f_HandleErr , , , "AIMUtil::method14", Now, gDRJobError, True, Err
End Function


Public Function Method15(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    Dim Bi As Double
    
    Dim DS001_013 As Double
    Dim DS014_026 As Double
    
    Dim Fcst As Double
    Dim i As Integer
    Dim Pd As Integer
    Dim si As Integer
    
    Dim SI001_013 As Double
    Dim SI014_026 As Double
    
    Dim Trend As Double
    
    'MovAvg(1,26) + Trend + SeaAdj
    
    'Initialize Trend
    Trend = MA.Trend(0)
    
    'Initialize Seasonality Index
    Bi = 1
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        'Initialize Seasonality Indices
        SI001_013 = 0
        SI014_026 = 0
        
        'Calculate Seasonality Indices
        For i = Pd + 1 To Pd + 13
            SI001_013 = SI001_013 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i))
            SI014_026 = SI014_026 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 13))
        Next i
        
        SI001_013 = Max(SI001_013 / 13, Simdata.OP.MinBi)
        SI014_026 = Max(SI014_026 / 13, Simdata.OP.MinBi)
        
        DS001_013 = MA.MA001_013(Pd) / SI001_013
        DS014_026 = MA.MA014_026(Pd) / SI014_026
           
        si = GetAbsPd(Pd)
        si = GetHsPeriod(Simdata.DM.EndPeriod, si)
        
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) > 0 Then
            Fcst = (DS001_013 * MA.Pd001_013(Pd) _
                + DS014_026 * MA.Pd014_026(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd))
        End If
        
        'Adjust Forecast for Trend and Seasonality
        Fcst = (Fcst + Trend) * Simdata.Sa.Bi(si)
        
        Bi = Simdata.Sa.Bi(si)
                    
        AccumFcstStatistics Pd, Fcst, Trend, Bi, FcstResults, Simdata
    
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 26 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method15 = FcstResults.N
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method15)"
     f_HandleErr , , , "AIMUtil::method15", Now, gDRJobError, True, Err
End Function


Public Function Method16(FcstResults As FCSTRESULTS_RCD, MA As MOVAVGWORK_RCD, _
    Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler
    
    Dim DS001_013 As Double
    Dim DS014_026 As Double
    Dim DS027_039 As Double
    Dim DS040_052 As Double
    
    Dim Fcst As Double
    Dim i As Integer
    Dim Pd As Integer
    Dim si As Integer
    
    Dim SI001_013 As Double
    Dim SI014_026 As Double
    Dim SI027_039 As Double
    Dim SI040_052 As Double
    
    Dim Trend As Double
    
    'MovAvg(1,52) + Trend + SeaAdj

    'Initialize Trend
    Trend = MA.Trend(0)
    
    For Pd = Simdata.LC.LookBackPds To LBound(MA.MA001_013) Step -1
        'Initialize Seasonality Indices
        SI001_013 = 0
        SI014_026 = 0
        SI027_039 = 0
        SI040_052 = 0
        
        'Calculate Seasonality Indices
        For i = Pd + 1 To Pd + 13
            SI001_013 = SI001_013 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i))
            SI014_026 = SI014_026 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 13))
            SI027_039 = SI027_039 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 26))
            SI040_052 = SI040_052 + Simdata.Sa.Bi(GetHsPeriod(Simdata.DM.EndPeriod, i + 39))
        Next i
        
        SI001_013 = Max(SI001_013 / 13, Simdata.OP.MinBi)
        SI014_026 = Max(SI014_026 / 13, Simdata.OP.MinBi)
        SI027_039 = Max(SI027_039 / 13, Simdata.OP.MinBi)
        SI040_052 = Max(SI040_052 / 13, Simdata.OP.MinBi)
        
        DS001_013 = MA.MA001_013(Pd) / SI001_013
        DS014_026 = MA.MA014_026(Pd) / SI014_026
        DS027_039 = MA.MA027_039(Pd) / SI027_039
        DS040_052 = MA.MA040_052(Pd) / SI040_052
           
        si = GetAbsPd(Pd)
        si = GetHsPeriod(Simdata.DM.EndPeriod, si)
        
        If MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd) > 0 Then
            Fcst = (DS001_013 * MA.Pd001_013(Pd) _
                + DS014_026 * MA.Pd014_026(Pd) _
                + DS027_039 * MA.Pd027_039(Pd) _
                + DS040_052 * MA.Pd040_052(Pd)) _
                / (MA.Pd001_013(Pd) + MA.Pd014_026(Pd) + MA.Pd027_039(Pd) + MA.Pd040_052(Pd))
                
        End If
        
        'Adjust Forecat for Trend and Seasonality
        Fcst = (Fcst + Trend) * Simdata.Sa.Bi(si)
                    
        AccumFcstStatistics Pd, Fcst, Trend, Simdata.Sa.Bi(si), FcstResults, Simdata
    
    Next Pd
    
    'Check for minimum number of periods
    If Simdata.DM.OldestPeriod < MIN_EVALUATION_PERIODS + 52 Then
        FcstResults.FcstStatus = FS_INACTIVE
    End If

    Method16 = FcstResults.N

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method16)"
     f_HandleErr , , , "AIMUtil::method16", Now, gDRJobError, True, Err
End Function



Public Function Method17(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    'ExpSmooth (0.1)

    Method17 = ExpSmooth(0.1, False, False, FcstResults, Simdata)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method17)"
     f_HandleErr , , , "AIMUtil::method17", Now, gDRJobError, True, Err
End Function


Public Function Method18(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    'ExpSmooth(.2)

    Method18 = ExpSmooth(0.2, False, False, FcstResults, Simdata)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method18)"
     f_HandleErr , , , "AIMUtil::method18", Now, gDRJobError, True, Err
End Function



Public Function Method19(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    'ExpSmooth(.3)

    Method19 = ExpSmooth(0.3, False, False, FcstResults, Simdata)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method19)"
     f_HandleErr , , , "AIMUtil::method19", Now, gDRJobError, True, Err
End Function



Public Function Method20(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    'ExpSmooth(.1) + SeaAdj

    Method20 = ExpSmooth(0.1, True, False, FcstResults, Simdata)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method20)"
     f_HandleErr , , , "AIMUtil::method20", Now, gDRJobError, True, Err
End Function

Public Function Method21(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    'ExpSmooth(.2) + SeaAdj

    Method21 = ExpSmooth(0.2, True, False, FcstResults, Simdata)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method21)"
     f_HandleErr , , , "AIMUtil::method21", Now, gDRJobError, True, Err
End Function


Public Function Method22(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler
    
    'ExpSmooth(.3) + SeaAdj
    
    Method22 = ExpSmooth(0.3, True, False, FcstResults, Simdata)

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method22)"
    f_HandleErr , , , "AIMUtil::method22", Now, gDRJobError, True, Err
End Function



Public Function Method23(FcstResults As FCSTRESULTS_RCD, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler

    'Adaptive Smoothing

    Method23 = ExpSmooth(0.1, True, True, FcstResults, Simdata)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(method23)"
     f_HandleErr , , , "AIMUtil::method23", Now, gDRJobError, True, Err
End Function



Public Function Min(v1 As Variant, v2 As Variant)
On Error GoTo ErrorHandler

    Min = IIf(v1 <= v2, v1, v2)
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(min)"
     f_HandleErr , , , "AIMUtil::min", Now, gDRJobError, True, Err

End Function


Public Function MovingAvg(StartPeriod As Integer, EndPeriod As Integer, _
    Simdata As SIMULATION_RCD, PdCount As Integer)
On Error GoTo ErrorHandler
    
    Dim Avg As Double
    Dim Pd As Integer
    
    'Initialize Period Count
    PdCount = 0
    
    'Check the range for exceptions
    If EndPeriod > Simdata.DM.OldestPeriod Then
        EndPeriod = Simdata.DM.OldestPeriod
    End If
    
    For Pd = StartPeriod To EndPeriod
        'Check for data exceptions
        If Simdata.DM.ByPassFlag(Pd) = False Then
            Avg = Avg + Simdata.DM.Cps(Pd)
            PdCount = PdCount + 1
        End If
    Next Pd
    
    If PdCount > 0 Then
        MovingAvg = Avg / PdCount
    Else
        MovingAvg = 0
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(MovingAvg)"
    f_HandleErr , , , "AIMUtil::MovingAvg", Now, gDRJobError, True, Err
End Function


Public Function GetDemandSafetyStock(Pds As Integer, SvcLvl As Double, Simdata As SIMULATION_RCD)
On Error GoTo ErrorHandler
   
    Dim i, J As Integer
    Dim Hold As Double
    Dim SwapValue As Double
    Dim SwapRow As Integer
    ReDim Work(1 To Pds) As Double
    
    For i = LBound(Simdata.DM.Cps) To UBound(Simdata.DM.Cps)
        If Simdata.DM.ByPassFlag(i) = BR_NOEXCEPTION Then
            J = J + 1
            Work(J) = Simdata.DM.Cps(i)
            If J >= Pds Then
                Exit For
            End If
        End If
    Next i

    For i = LBound(Work) To Pds - 1
        
        SwapRow = i
        SwapValue = Work(i)
        Hold = Work(i)
        
        For J = i + 1 To Pds
        
            If Work(J) < SwapValue Then
                SwapValue = Work(J)
                SwapRow = J
            End If
            
        Next J
     
        If SwapRow <> i Then
            Work(i) = Work(SwapRow)
            Work(SwapRow) = Hold
        End If
        
     Next i
     
     'Return Safety Stock
     i = Int(SvcLvl * Pds)
     
     GetDemandSafetyStock = Work(i)
     
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(GetDemandSafetyStock)"
    f_HandleErr , , , "AIMUtil::GetDemandSafetyStock", Now, gDRJobError, True, Err
End Function


Public Function SortFcstResults(FcstResults() As FCSTRESULTS_RCD)
On Error GoTo ErrorHandler

    Dim Hold As FCSTRESULTS_RCD
    Dim i, J As Integer
    Dim LowValue As Double
    Dim SwapRow As Integer
    
    For i = LBound(FcstResults) To UBound(FcstResults) - 1
              
        Hold = FcstResults(i)
        LowValue = FcstResults(i).Accum_AE(1) + FcstResults(i).FcstStatus
        SwapRow = i
        
        For J = i + 1 To UBound(FcstResults)
        
            If FcstResults(J).Accum_AE(1) + FcstResults(J).FcstStatus < LowValue Then
                LowValue = FcstResults(J).Accum_AE(1) + FcstResults(J).FcstStatus
                SwapRow = J
            End If
            
        Next J
     
        If SwapRow <> i Then
            FcstResults(i) = FcstResults(SwapRow)
            FcstResults(SwapRow) = Hold
        End If
        
     Next i
     
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(SortFcstResults)"
     f_HandleErr , , , "AIMUtil::SortFcstResults", Now, gDRJobError, True, Err
End Function


Public Function TotalAnnualCost(Demand As Double, KFactor As Double, _
    ReplenCost As Double, Qty As Long, Cost As Double)
On Error GoTo ErrorHandler

    TotalAnnualCost = (Cost * 52 * Demand) _
        + ((Demand * 52 / Qty) * ReplenCost) _
            + ((Qty / 2) * Cost * (KFactor / 100))

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(TotalAnnualCost)"
     f_HandleErr , , , "AIMUtil::TotalAnnualCost", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    RndNormal(...)
'
' Description:      Calculates an approximation of a random normal
'                   distribution.
'
' Parameters:       Mean            Mean
'                   StdDev          Standard Deviation
'
' Returns:          Random Value
'
' By: RES           Date: 02/22/2001
'
'
'**********************************************************************
Public Function RndNormal(Mean As Double, StdDev As Double) As Double
On Error GoTo ErrorHandler

    'RndNormal = Mean + (StdDev * (Sqr(-2 * Log(Rnd)) * Cos(6.28 * Rnd)))
    
    'Generate a random number (0 - 1)
    'Determine the Inverse of the Cumulative Normal Distribution
    RndNormal = Mean + (StdDev * SafetyFactor(Rnd))

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(RndNormal)"
    f_HandleErr , , , "AIMUtil::RndNormal", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    NormalDist(z)
'
' Description:      Standard Normal Distribution
'                   Mean = 0
'                   Standard Deviation = 1
'
' Parameters:       Z       Number of Standard Deviations
'
' Returns:          Standard Normal Distribution
'
' By: RES           Date: 02/22/2001
'
'
'**********************************************************************
Public Function NormalDist(z As Double) As Double
On Error GoTo ErrorHandler

    NormalDist = Round((1 / Sqr(2 * PI)) * Exp(-0.5 * (z ^ 2)), 4)
    
Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(NormalDist)"
    f_HandleErr , , , "AIMUtil::NormalDist", Now, gDRJobError, True, Err
End Function


'**********************************************************************
' Function Name:    CumNormalDist(Z)
'
' Description:      Calculates an approximation of the Cumulative Normal
'                   Distribution based on an algorith from Abramowitz
'                   and Stegun.
'
' Parameters:       Z       Number of Standard Deviations
'
' Returns:          Cummulative Normal Distribution
'
' By: RES           Date: 02/22/2001
'
'
'**********************************************************************
Public Function CumNormalDist(z As Double) As Double
On Error GoTo ErrorHandler

    Dim t As Double
    
    t = 1 / (1 + 0.33267 * z)
    
    CumNormalDist = 1 - (NormalDist(z) * ((0.4361836 * t) + (-0.1201676 * t ^ 2) + (0.937298 * t ^ 3)))

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CumNormalDist)"
    f_HandleErr , , , "AIMUtil::CumNormalDist", Now, gDRJobError, True, Err
End Function


Function xlTransmitPO(TransmitPO As Integer) As String
On Error GoTo ErrorHandler

    Select Case TransmitPO
        Case 0
            xlTransmitPO = getTranslationResource("Fax")
        Case 1
            xlTransmitPO = getTranslationResource("EDI")
        Case Else
            xlTransmitPO = getTranslationResource("Fax")
    
    End Select
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(xlTransmitPO)"
    f_HandleErr , , , "AIMUtil::xlTransmitPO", Now, gDRJobError, True, Err
End Function


Function xlTransmitPOString(TransmitPO As String) As Integer
On Error GoTo ErrorHandler

    Select Case TransmitPO
        Case getTranslationResource("Fax")
            xlTransmitPOString = 0
        Case getTranslationResource("EDI")
            xlTransmitPOString = 1
        Case Else
            xlTransmitPOString = 0
    
    End Select

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(xlTransmitPOString)"
     f_HandleErr , , , "AIMUtil::xlTransmitPOString", Now, gDRJobError, True, Err
End Function


Public Function CleanUpGlobals()

    'Set all global Objects to Nothing
    Set gxarTranslate = Nothing
    'Set objTranslate = Nothing
    
    Set gStatusBar = Nothing
    Set gToolBar = Nothing
    Set AC = Nothing
     'Disconnect from the database
     SQLConnection Cn, CONNECTION_CLOSE, False, , , "CleanUpGlobals"
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(CleanUpGlobals)"
     f_HandleErr , , , "AIMUtil::CleanUpGlobals", Now, gDRJobError, True, Err
End Function


'Generic Functions to check recordset viablity
Public Function f_IsRecordsetValidAndOpen(rsToCheck As ADODB.Recordset) As Boolean
On Error GoTo ErrorHandler
    
    'Set default status to negative
    f_IsRecordsetValidAndOpen = False
    'Check object
    If (Not rsToCheck Is Nothing) Then
        'Check State
        If (rsToCheck.State = adStateOpen) Then
            f_IsRecordsetValidAndOpen = True
        End If
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(f_IsRecordsetValidAndOpen)"
    f_HandleErr , , , "AIMUtil::f_IsRecordsetValidAndOpen", Now, gDRJobError, True, Err
End Function


Public Function f_IsRecordsetOpenAndPopulated(rsToCheck As ADODB.Recordset) As Boolean
On Error GoTo ErrorHandler
    
    'Set default status to negative
    f_IsRecordsetOpenAndPopulated = False
    'Check Validity
    If (f_IsRecordsetValidAndOpen(rsToCheck)) Then
        'Check index
        '!!!Can't check index, since a stored proc isn't a cursor.
        '!!!Recordcount applies only to open cursors, AIM's SPs return -1 although they have the data.
        'Ensure that the stored procedure returns the rowcount, that way checking the recordcount will work
        If (rsToCheck.RecordCount > 0) _
        Or (rsToCheck.RecordCount = -1) _
        Then
            f_IsRecordsetOpenAndPopulated = True
        End If
    End If

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(f_IsRecordsetOpenAndPopulated)"
     f_HandleErr , , , "AIMUtil::f_IsRecordsetOpenAndPopulated", Now, gDRJobError, True, Err
End Function
Public Function LogInfoToTable(UpdateType As String, _
                                Optional ErrorNumber As String, _
                                Optional ErrorDesc As String, _
                                Optional ErrorSource As String, _
                                Optional ErrorPath As String)
                                
'if error number is not passed, extract it from error object.
If Len(Trim(ErrorNumber)) = 0 Then
    ErrorNumber = Err.Number
End If

'if descriptionis is not passed, query it from error object.
If Len(Trim(ErrorDesc)) = 0 Then
    ErrorDesc = Err.Description
End If

'If description is not passed then request error object for it.
If Len(Trim(ErrorSource)) = 0 Then
    ErrorSource = Err.source
End If
End Function
Public Function f_HandleErr(Optional ErrorNumber As String, Optional ErrorDesc As String, Optional ErrorSource As String, Optional ErrorSourceEvent As String, Optional ErrorTimeAndDate As String, _
                            Optional ErrorType As String, Optional WantToShowMsgBox As Boolean = True, Optional ErrorObject As ErrObject, Optional IsNewJobUpdate As Boolean = False)

    Dim freeFileIndex As Integer
    Dim ErrorMessage As String
    
'   Dim ErrorDesc As String
'   Dim ErrorSource As String
    Dim ErrorNum As Long
    
    
    Dim ErrotTabelUpdateCmd As ADODB.Command
    Dim RtnCode As Long
    
    
    'Variables reqired for error logging to DB. -sgajjela
    Dim DBRecordSet As ADODB.Recordset
    Dim DBCommand  As ADODB.Command
    Dim ReturnCode As Long
    Dim SQLQuery As String
    Dim BufferLength As Long
    
    'Do not display error message box, if its orgin is from DR SCHEDULED jobs. -sgajjela
    If (ErrorType = gDRJobUpdate) Or (ErrorType = gDRJobError) Then
        WantToShowMsgBox = False
        gLogFile = App.Path & "\" & g_ERRORLOG ' Log file to write errors from DR Data interface.
        
    Else
        WantToShowMsgBox = True
        gLogFile = App.Path & "\" & g_ERRORLOG ' Log file to write error messages from DR GUI
    End If
    
    'Set log file parameters
    freeFileIndex = FreeFile
    
    If (ErrorType = gDRJobUpdate) Then
        ErrorNum = ErrorNumber
        ErrorSource = ErrorSource
        ErrorDesc = ErrorDesc
    Else
        ErrorNum = ErrorObject.Number
        ErrorSource = ErrorObject.source
        ErrorDesc = ErrorObject.Description
    End If
    
        
    'If AIMUsers says log errors, then...
    If gLogErrors Then
        On Error Resume Next
        Open gLogFile For Append Shared As freeFileIndex
        If Err = 0 Then
            'Write to log
            
            Print #freeFileIndex, Spc(1); Format(Now, gDateFormat & " " & gTimeFormat); _
                                Spc(1); "-- "; gUserID & _
                                " -- " & ErrorNum & " -- " & ErrorSource & " -- " & ErrorDesc & "(" & ErrorSource & ErrorSourceEvent & ")"
                                
            If (gWantToLogToDB = True) Then
                    If (IsNewJobUpdate = True) Then
                        Set DBRecordSet = New ADODB.Recordset
                        DBRecordSet.CursorType = adOpenKeyset
                        DBRecordSet.LockType = adLockOptimistic
                        DBRecordSet.Open "AIMERROR_INFO", gDBConnection, , , adCmdTable
                        If (Err = 0) Then
                            DBRecordSet.AddNew
                            If (Err = 0) Then
                                DBRecordSet!ComputerName = gComputerName
                                DBRecordSet!UserID = gUserID
                                DBRecordSet!DateAndTime = Now
                                DBRecordSet!ErrorNumber = ErrorNum
                                DBRecordSet!ErrorSource = ErrorSource
                                DBRecordSet!ErrorDesc = ErrorDesc
                                DBRecordSet!ErrorFromEvent = ErrorSourceEvent
                                DBRecordSet!LOGTYPE = ErrorType
                                DBRecordSet.Update
                                gJobUpdateID = DBRecordSet!MsgID
                                DBRecordSet.Close
                            
                            End If ' End if of If (Err = 0) Then
                            
                        End If 'End If of If (Err = 0) Then
                        
                    Else  ' Its not a new job update. So update it.
                    
                    Set ErrotTabelUpdateCmd = New ADODB.Command
                    With ErrotTabelUpdateCmd
                           Set .ActiveConnection = gDBConnection
                           .CommandTimeout = 0
                           .CommandType = adCmdStoredProc
                           .CommandText = "AIMMessageWriter"
                           .Parameters("@MessageID").Value = gJobUpdateID
                           .Parameters("@Description").Value = ErrorDesc
                       'Update the table
                           .Execute
                       End With
                    End If '  End of If (IsNewJobUpdate = True)
            End If  'end if of If (gWantToLogToDB = True) Then
            
            'Display a concise message to the user indicating log file
            ErrorMessage = getTranslationResource("ERRMSG08050")
            If StrComp(ErrorMessage, "ERRMSG08050") = 0 Then ErrorMessage = "Error Encountered. For details, please refer to:"
    
            If WantToShowMsgBox Then
                ErrorMessage = ErrorNum & " -- " & ErrorMessage & ErrorDesc & " " & "Location of the log:" & gLogFile
                MsgBox ErrorMessage, vbOKOnly, ""
            End If
        ElseIf WantToShowMsgBox Then
            'Display a detailed message to the user
            ErrorMessage = getTranslationResource("ERRMSG08050")
            If StrComp(ErrorMessage, "ERRMSG08050") = 0 Then ErrorMessage = "Error Encountered. For details, please refer to:"
            
            ErrorMessage = ErrorMessage & " " & ErrorNum & " -- " & ErrorSource & " -- " & ErrorDesc & "(" & ErrorSource & ErrorSourceEvent & ")"
            MsgBox ErrorMessage, vbOKOnly, ""
            
            'Write a status message indicating trouble with file ops
            ErrorMessage = getTranslationResource("ERRMSG08051")
            If StrComp(ErrorMessage, "ERRMSG08051") = 0 Then ErrorMessage = "Unable to write to log file. Error:"

            Write_Message ErrorMessage & " " & Err.Number & "; " & Err.Description
        End If
        Close
    Else
        If WantToShowMsgBox Then
            If gLangID <> "" Then
                'Display a detailed message to the user
                ErrorMessage = getTranslationResource("ERRMSG08050")
                If StrComp(ErrorMessage, "ERRMSG08050") = 0 Then ErrorMessage = "Error Encountered. For details, please refer to:"
                
                ErrorMessage = ErrorMessage & " " & ErrorNum & " -- " & ErrorSource & " -- " & ErrorDesc & "(" & ErrorSource & ErrorSourceEvent & ")"
                MsgBox ErrorMessage, vbOKOnly, ""
            Else
                ErrorMessage = "Error Encountered. For details, please refer to:"
                ErrorMessage = ErrorMessage & " " & ErrorNum & " -- " & ErrorDesc & "(" & ErrorSource & ErrorSourceEvent & ")"
                MsgBox ErrorMessage, vbOKOnly, ""
            End If
        End If
    End If
    
    'If this is from the UI, then display error
    Write_Message ErrorDesc
    'Reset mousepointer, assuming it had been set to busy.
    Screen.MousePointer = vbNormal
    
End Function



Public Function getLocaleID(Optional p_langID As String) As Integer
On Error GoTo ErrorHandler

    Dim Cn As ADODB.Connection
    Dim rsLanguage As ADODB.Recordset
    Dim AIMLanguage_SP As ADODB.Command
    Dim LocaleID As Integer
    Dim rsAIMUsers As ADODB.Recordset

    Dim strSQL As String
    Dim strMessage As String
    
    Dim RtnCode As Long
    
    'Default to app's locale
    LocaleID = g_LOCALEID_EN_US    'EN-US
    
    'Start a new connection
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, True, False, True)
    'Check the connection.
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        'Default to app's native language
        If p_langID = "" Then
            'Set global variables to default val.
            gLocaleID = g_LOCALEID_EN_US
            gLangID = "en-us"
            If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US
        Else
            'Return default val. to calling function
            getLocaleID = g_LOCALEID_EN_US
        End If
        Exit Function
    End If
    
    If Trim$(gLangID) = "" Then
        'Load the AIM User Profile
        strSQL = "select * from AIMUsers where UserId = N'" + gUserID + "' "
        
        Set rsAIMUsers = New ADODB.Recordset
        rsAIMUsers.Open strSQL, Cn, adOpenStatic, adLockOptimistic
        
        If f_IsRecordsetOpenAndPopulated(rsAIMUsers) Then
            gLangID = rsAIMUsers!LangID
        Else
            If gLangIndex <= 0 Then gLangIndex = g_LANGINDEX_EN_US 'default to english
            strMessage = LoadResString(gLangIndex + 16) '"The specified User ID is not authorized for access to SSA DR -- Connection terminated."
            Err.Description = strMessage
            GoTo ErrorHandler
        End If
        
        'Close the Recordset
        If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
        Set rsAIMUsers = Nothing
    End If
    
    'Set up Stored Proc.
    Set AIMLanguage_SP = New ADODB.Command
    With AIMLanguage_SP
        Set .ActiveConnection = Cn
        .CommandText = "AIM_AIMLanguage_GetValue_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    
    'Define the Result Sets
    Set rsLanguage = New ADODB.Recordset
    With rsLanguage
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    If p_langID <> "" Then
        AIMLanguage_SP.Parameters("@LangID").Value = p_langID
    Else
        AIMLanguage_SP.Parameters("@LangID").Value = gLangID
    End If
    
    'Fetch
    rsLanguage.Open AIMLanguage_SP
    
    'Validate and assign
    If f_IsRecordsetOpenAndPopulated(rsLanguage) Then
        LocaleID = rsLanguage!DecimalValue
    End If
    
    If p_langID = "" Then
        'Set global variable to return val.
        gLocaleID = LocaleID
    Else
        'Return locale id to calling function
        getLocaleID = LocaleID
    End If
    
    'CleanUp
    If f_IsRecordsetValidAndOpen(rsLanguage) Then rsLanguage.Close
    Set rsLanguage = Nothing
    If Not (AIMLanguage_SP Is Nothing) Then Set AIMLanguage_SP.ActiveConnection = Nothing
    Set AIMLanguage_SP = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsLanguage) Then rsLanguage.Close
    Set rsLanguage = Nothing
    If Not (AIMLanguage_SP Is Nothing) Then Set AIMLanguage_SP.ActiveConnection = Nothing
    Set AIMLanguage_SP = Nothing
    If f_IsRecordsetValidAndOpen(rsAIMUsers) Then rsAIMUsers.Close
    Set rsAIMUsers = Nothing
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    'Err.Raise Err.Number, Err.source & "(AIMUtil.getLocaleID)", Err.Description
     f_HandleErr , , , "AIMUtil::getLocaleID", Now, gDRJobError, True, Err
End Function

Public Function g_FetchLanguages(p_LocaleID As Integer, r_rsLangList As ADODB.Recordset)
On Error GoTo ErrorHandler
    
    Dim Conn As ADODB.Connection
    Dim rsLanguage As ADODB.Recordset
    Dim AIMLanguage_SP As ADODB.Command
    
    Dim strSQL As String
    Dim strMessage As String
    
    Dim RtnCode As Long
    Dim RowCounter As Long
    Dim ColCounter As Long
    
    'Default to app's native locale
    If p_LocaleID <= 0 Then p_LocaleID = g_LOCALEID_EN_US
    
    'Start a new connection
    Set Conn = New ADODB.Connection
    RtnCode = SQLConnection(Conn, CONNECTION_OPEN, True, False, True)
    'Check the connection.
    If RtnCode <> SUCCEED Then
        Screen.MousePointer = vbNormal
        'Default to app's native language
        Exit Function
    End If
    
    'Set up Stored Proc.
    Set AIMLanguage_SP = New ADODB.Command
    With AIMLanguage_SP
        Set .ActiveConnection = Conn
        .CommandText = "AIM_AIMLanguage_List_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
        .Parameters("@EnabledOption") = "Y"
    End With
    
    'Define the Result Sets
    Set rsLanguage = New ADODB.Recordset
    With rsLanguage
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With

    'Fetch
    rsLanguage.Open AIMLanguage_SP
    
    'Validate and assign
    If f_IsRecordsetOpenAndPopulated(rsLanguage) Then
        Set r_rsLangList = New ADODB.Recordset
'        For ColCounter = 0 To rsLanguage.Fields.Count - 1
'            r_rsLangList.Fields.Append rsLanguage.Fields(ColCounter).Name, _
'                                       rsLanguage.Fields(ColCounter).Type, _
'                                       rsLanguage.Fields(ColCounter).DefinedSize
'        Next
        r_rsLangList.Fields.Append "LangID", adBSTR, 10
        r_rsLangList.Fields.Append "LangDesc", adBSTR, 255
        r_rsLangList.Fields.Append "HexValue", adBSTR, 15
        r_rsLangList.Fields.Append "DecimalValue", adDecimal, 20
        r_rsLangList.Fields.Append "Enabled", adChar, 1
            
        r_rsLangList.Open
        rsLanguage.MoveFirst
        For RowCounter = 0 To rsLanguage.RecordCount - 1
            With r_rsLangList
                .AddNew
                For ColCounter = 0 To rsLanguage.Fields.Count - 1
                    .Fields(ColCounter) = rsLanguage.Fields(ColCounter).Value
                Next
            End With
            rsLanguage.MoveNext
        Next
    Else
        If f_IsRecordsetValidAndOpen(r_rsLangList) Then r_rsLangList.Close
        Set r_rsLangList = Nothing
    End If
    
    'CleanUp
    If f_IsRecordsetValidAndOpen(rsLanguage) Then rsLanguage.Close
    Set rsLanguage = Nothing
    If Not (AIMLanguage_SP Is Nothing) Then Set AIMLanguage_SP.ActiveConnection = Nothing
    Set AIMLanguage_SP = Nothing
    RtnCode = SQLConnection(Conn, CONNECTION_CLOSE, False)
    Set Conn = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsLanguage) Then rsLanguage.Close
    Set rsLanguage = Nothing
    If Not (AIMLanguage_SP Is Nothing) Then Set AIMLanguage_SP.ActiveConnection = Nothing
    Set AIMLanguage_SP = Nothing
    RtnCode = SQLConnection(Conn, CONNECTION_CLOSE, False)
    Set Conn = Nothing
    'Err.Raise Err.Number, Err.source, Err.Description & "(g_FetchLanguages)"
     f_HandleErr , , , "AIMUtil::g_FetchLanguages", Now, gDRJobError, True, Err
End Function

Public Function Meth24_25BldMovAvgTable(Simdata As SIMULATION_RCD, _
    MA As MOVAVGWORK_RCD)
On Error GoTo ErrorHandler

    Dim Pd As Integer

    'Initialize values
    For Pd = LBound(MA.MA001_013) To UBound(MA.MA001_013)
        MA.MA001_013(Pd) = 0
        MA.MA014_026(Pd) = 0
        MA.MA027_039(Pd) = 0
        MA.MA040_052(Pd) = 0
        MA.MA053_065(Pd) = 0
        MA.MA066_078(Pd) = 0
        MA.MA079_091(Pd) = 0
        MA.MA092_104(Pd) = 0
        MA.Trend(Pd) = 0
        'MA.SaAdj(pd) = 0
    Next Pd
    
    'Build the Moving Average Work Table
    For Pd = LBound(MA.MA001_013) To UBound(MA.MA001_013)
        MA.MA001_013(Pd) = Meth24_25MovingAvg(Pd + 1, Pd + 13, Simdata, MA.Pd001_013(Pd))
        MA.MA014_026(Pd) = Meth24_25MovingAvg(Pd + 14, Pd + 26, Simdata, MA.Pd014_026(Pd))
        MA.MA027_039(Pd) = Meth24_25MovingAvg(Pd + 27, Pd + 39, Simdata, MA.Pd027_039(Pd))
        MA.MA040_052(Pd) = Meth24_25MovingAvg(Pd + 40, Pd + 52, Simdata, MA.Pd040_052(Pd))
        MA.MA053_065(Pd) = Meth24_25MovingAvg(Pd + 53, Pd + 65, Simdata, MA.Pd053_065(Pd))
        MA.MA066_078(Pd) = Meth24_25MovingAvg(Pd + 66, Pd + 78, Simdata, MA.Pd066_078(Pd))
        MA.MA079_091(Pd) = Meth24_25MovingAvg(Pd + 79, Pd + 91, Simdata, MA.Pd079_091(Pd))
        MA.MA092_104(Pd) = Meth24_25MovingAvg(Pd + 92, Pd + 104, Simdata, MA.Pd092_104(Pd))
        
        MA.Trend(Pd) = ((MA.MA001_013(Pd) _
                        + MA.MA014_026(Pd) _
                        + MA.MA027_039(Pd) _
                        + MA.MA040_052(Pd)) _
                        - (MA.MA053_065(Pd) _
                        + MA.MA066_078(Pd) _
                        + MA.MA079_091(Pd) _
                        + MA.MA092_104(Pd))) / 208
                        
'        Select Case SimData.DM.OldestPeriod
'        Case Is >= 104  '2 Years History
'            MA.SaAdj(pd) = ((MA.MA040_052(pd) + MA.MA092_104(pd)) / 2) - ((MA.MA001_013(pd) + MA.MA014_026(pd) + MA.MA027_039(pd) + MA.MA040_052(pd) _
'                + MA.MA053_065(pd) + MA.MA066_078(pd) + MA.MA079_091(pd) + MA.MA092_104(pd)) / 8)
'
'        Case Is >= 52   '1 Year History
'            MA.SaAdj(pd) = MA.MA040_052(pd) _
'                - ((MA.MA001_013(pd) + MA.MA014_026(pd) + MA.MA027_039(pd) + MA.MA040_052(pd) / 4))
'
'        Case Else
'            MA.SaAdj(pd) = 0
'
'        End Select
    Next Pd
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Meth24_25BldMovAvgTable)"
    f_HandleErr , , , "AIMUtil::Meth24_25BldMovAvgTable", Now, gDRJobError, True, Err
End Function



Public Function Meth24_25MovingAvg(StartPeriod As Integer, EndPeriod As Integer, _
    Simdata As SIMULATION_RCD, PdCount As Integer)
On Error GoTo ErrorHandler
    
    Dim Avg As Double
    Dim Pd As Integer
    
    'Initialize Period Count
    PdCount = 0
    
    'Check the range for exceptions
    If EndPeriod <= 4 Then
        Meth24_25MovingAvg = 0
        Exit Function
    End If
    
    If EndPeriod > Simdata.DM.OldestPeriod - 4 Then
        EndPeriod = Simdata.DM.OldestPeriod - 4
    End If
    
    
    For Pd = StartPeriod To EndPeriod
        'Check for data exceptions
        If Simdata.DM.ByPassFlag(Pd) = False Then
            Avg = Avg + Simdata.DM.Cps(Pd)
            PdCount = PdCount + 1
        End If
    Next Pd
    
    If PdCount > 0 Then
        Meth24_25MovingAvg = Avg / PdCount
    Else
        Meth24_25MovingAvg = 0
    End If
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMUtil.bas)", Err.Description & "(Meth24_25MovingAvg)"
     f_HandleErr , , , "AIMUtil::Meth24_25MovingAvg", Now, gDRJobError, True, Err
End Function








