Attribute VB_Name = "AIMCalendar"
Option Explicit

Public Function GetDayIndex( _
    TargetDate As Date, _
    AIMYears() As AIMYEARS_RCD _
) As Integer
On Error GoTo ErrorHandler

    Dim DayIndex As Integer
    Dim i As Integer
    
    For i = LBound(AIMYears) To UBound(AIMYears)
        If TargetDate >= AIMYears(i).FYStartDate _
            And TargetDate <= AIMYears(i).FYEndDate Then
            
            GetDayIndex = DayIndex + DateDiff("d", AIMYears(i).FYStartDate, TargetDate) + 1
            Exit Function
            
        End If
        
        'Increment Day Index
        DayIndex = DayIndex + DateDiff("d", AIMYears(i).FYStartDate, AIMYears(i).FYEndDate) + 1
    
    Next i
    
    'Target date not found
    GetDayIndex = FAIL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetDayIndex)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetDayIndex", Now, gDRJobError, True, Err
End Function

Function GetDayStatus( _
    TargetDate As Date, _
    AIMDays() As AIMDAYS_RCD, _
    AIMYears() As AIMYEARS_RCD _
) As Integer
On Error GoTo ErrorHandler

    Dim Row As Integer
    
    'Find the date
    For Row = GetDayIndex(TargetDate, AIMYears) To UBound(AIMDays)
    
        If AIMDays(Row).FYDate = TargetDate Then
            GetDayStatus = AIMDays(Row).DayStatus
            Exit Function
        End If
        
    Next Row

    GetDayStatus = FAIL
    
Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetDayStatus)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetDayStatus", Now, gDRJobError, True, Err
End Function

Public Function GetEndDateFmPeriod( _
    TargetPeriod As Integer, _
    FiscalYear As Integer, _
    AIMYears() As AIMYEARS_RCD _
) As Date
On Error GoTo ErrorHandler
    
    Dim Row As Integer
    
    'Find the date
    For Row = LBound(AIMYears) To UBound(AIMYears)
    
        If FiscalYear = AIMYears(Row).FiscalYear Then
            Select Case TargetPeriod
            Case 1
                GetEndDateFmPeriod = AIMYears(Row).FYStartDate + 6
            Case 2 To 51
                GetEndDateFmPeriod = DateAdd("d", (TargetPeriod * 7) - 1, AIMYears(Row).FYStartDate)
            Case 52
                GetEndDateFmPeriod = AIMYears(Row).FYEndDate
            End Select
            
            Exit Function
            
        End If
    
    Next Row

    GetEndDateFmPeriod = FAIL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetEndDateFmPeriod)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetEndDateFmPeriod", Now, gDRJobError, True, Err
End Function

Public Function GetFiscalYear( _
    TargetDate As Date, _
    AIMYears() As AIMYEARS_RCD _
) As Integer
On Error GoTo ErrorHandler

    Dim i As Integer
    
    For i = LBound(AIMYears) To UBound(AIMYears)
        If TargetDate >= AIMYears(i).FYStartDate _
            And TargetDate <= AIMYears(i).FYEndDate Then
            
            GetFiscalYear = AIMYears(i).FiscalYear
            Exit Function
            
        End If
    
    Next i
    
    'Target date not found
    GetFiscalYear = FAIL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetFiscalYear)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetFiscalYear", Now, gDRJobError, True, Err
End Function

Public Function GetPeriod( _
    TargetDate As Date, _
    AIMYears() As AIMYEARS_RCD _
    , Optional r_StartDate As Date _
    , Optional r_EndDate As Date _
) As Integer
On Error GoTo ErrorHandler
'see AIMCalendar.frm -- function GetFiscalPeriod

    Dim Row As Integer

    'Find the date
    For Row = LBound(AIMYears) To UBound(AIMYears)
        If TargetDate >= AIMYears(Row).FYStartDate _
        And TargetDate <= AIMYears(Row).FYEndDate _
        Then
            r_StartDate = AIMYears(Row).FYStartDate
            r_EndDate = AIMYears(Row).FYEndDate
            
            GetPeriod = (DateDiff("d", r_StartDate, TargetDate) \ 7) + 1
            GetPeriod = IIf(GetPeriod > 52, 52, GetPeriod)

            Exit Function

        End If

    Next Row

    GetPeriod = FAIL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetPeriod)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetPeriod", Now, gDRJobError, True, Err
End Function

Public Function GetEndDate( _
    TargetDate As Date, _
    AIMYears() As AIMYEARS_RCD _
) As Date
On Error GoTo ErrorHandler

    Dim Pd As Integer
    Dim Row As Integer
    Dim StartDate As Date
    Dim EndDate As Date
    
    Pd = GetPeriod(TargetDate, AIMYears(), StartDate, EndDate)
    If Pd <> FAIL Then
        Select Case Pd
        Case 1
            GetEndDate = StartDate + 6
        Case 2 To 51
            GetEndDate = DateAdd("d", (Pd * 7) - 1, StartDate)
        Case 52
            GetEndDate = EndDate
        End Select
    Else
        GetEndDate = FAIL
    End If
    
'    'Find the date
'    For Row = LBound(AIMYears) To UBound(AIMYears)
'        If TargetDate >= AIMYears(Row).FYStartDate _
'        And TargetDate <= AIMYears(Row).FYEndDate _
'        Then
'            Pd = (DateDiff("d", AIMYears(Row).FYStartDate, TargetDate) \ 7) + 1
'            Pd = IIf(Pd > 52, 52, Pd)
'
'            Select Case Pd
'                Case 1
'                    GetEndDate = AIMYears(Row).FYStartDate + 6
'                Case 2 To 51
'                    GetEndDate = DateAdd("d", (Pd * 7) - 1, AIMYears(Row).FYStartDate)
'                Case 52
'                    GetEndDate = AIMYears(Row).FYEndDate
'            End Select
'
'            Exit Function
'        End If
'    Next Row
'
'    GetEndDate = FAIL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetEndDate)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetEndDate", Now, gDRJobError, True, Err
End Function

Public Function GetWorkingDaysInWeek( _
    TargetDate As Date, _
    AIMDays() As AIMDAYS_RCD, _
    AIMYears() As AIMYEARS_RCD _
) As Integer
On Error GoTo ErrorHandler

    Dim EndDate As Date
    Dim i As Integer
    Dim StartDate As Date
    Dim WorkingDays As Integer
        
    StartDate = GetStartDateFmDate(TargetDate, AIMYears)
    EndDate = GetEndDate(TargetDate, AIMYears)
        
    For i = GetDayIndex(StartDate, AIMYears) To UBound(AIMDays)
        If AIMDays(i).FYDate < StartDate Then
             
        ElseIf AIMDays(i).FYDate >= StartDate _
            And AIMDays(i).FYDate <= EndDate _
            And AIMDays(i).DayStatus > 0 Then
        
            WorkingDays = WorkingDays + 1
             
        ElseIf AIMDays(i).FYDate > EndDate Then
        
            Exit For
        
        End If
    
    Next i

    GetWorkingDaysInWeek = WorkingDays

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetWorkingDaysInWeek)", Err.Description
    f_HandleErr , , , "AIMCalendar::GetWorkingDaysInWeek", Now, gDRJobError, True, Err
End Function

Public Function GetStartDateFmDate( _
    TargetDate As Date, _
    AIMYears() As AIMYEARS_RCD _
) As Date
On Error GoTo ErrorHandler

    Dim Pd As Integer
    Dim Row As Integer
    Dim StartDate As Date
    Dim EndDate As Date
    
    
    Pd = GetPeriod(TargetDate, AIMYears(), StartDate, EndDate)
    If Pd <> FAIL Then
        GetStartDateFmDate = DateAdd("ww", Pd, StartDate) - 7
    Else
        'Date out of range -- Default to last week of last year
        GetStartDateFmDate = DateAdd("ww", 52, AIMYears(UBound(AIMYears)).FYStartDate) - 7
    End If
    
'    'Find the date
'    For Row = LBound(AIMYears) To UBound(AIMYears)
'        If TargetDate >= AIMYears(Row).FYStartDate _
'            And TargetDate <= AIMYears(Row).FYEndDate Then
'            Pd = (DateDiff("d", AIMYears(Row).FYStartDate, TargetDate) \ 7) + 1
'            Pd = IIf(Pd > 52, 52, Pd)
'
'            GetStartDateFmDate = DateAdd("ww", Pd, AIMYears(Row).FYStartDate) - 7
'            Exit Function
'
'        End If
'
'    Next Row
'
'    'Date out of range -- Default to last week of last year
'    GetStartDateFmDate = DateAdd("ww", 52, AIMYears(UBound(AIMYears)).FYStartDate) - 7

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetStartDateFmDate)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetStartDateFmDate", Now, gDRJobError, True, Err
End Function

Public Function GetStartDateFmPeriod( _
    TargetPeriod As Integer, _
    FiscalYear As Integer, _
    AIMYears() As AIMYEARS_RCD _
) As Date

On Error GoTo ErrorHandler

    Dim Row As Integer
    
    'Find the date
    For Row = LBound(AIMYears) To UBound(AIMYears)
        If FiscalYear = AIMYears(Row).FiscalYear Then
            GetStartDateFmPeriod = DateAdd("ww", TargetPeriod, AIMYears(Row).FYStartDate) - 7
            Exit Function
            
        End If
    
    Next Row

    GetStartDateFmPeriod = FAIL

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetStartDateFmPeriod)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetStartDateFmPeriod", Now, gDRJobError, True, Err
End Function

Public Function GetWorkingDays( _
    StartDate As Date, _
    EndDate As Date, _
    AIMDays() As AIMDAYS_RCD, _
    AIMYears() As AIMYEARS_RCD _
) As Integer
On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim WorkingDays  As Integer
    
    'Check for error condition
    If StartDate > EndDate Then
        GetWorkingDays = 0
        Exit Function
    End If
    
    For i = GetDayIndex(StartDate, AIMYears) To UBound(AIMDays)
        If AIMDays(i).FYDate < StartDate Then
             
        ElseIf AIMDays(i).FYDate >= StartDate _
            And AIMDays(i).FYDate <= EndDate _
            And AIMDays(i).DayStatus > 0 Then
        
            WorkingDays = WorkingDays + 1
             
        ElseIf AIMDays(i).FYDate > EndDate Then
        
            Exit For
        
        End If
    
    Next i

    GetWorkingDays = WorkingDays

Exit Function
ErrorHandler:
    'Err.Raise Err.Number, Err.source & "(AIMCalendarBAS.GetWorkingDays)", Err.Description
     f_HandleErr , , , "AIMCalendar::GetWorkingDays", Now, gDRJobError, True, Err
End Function

Public Function AC_LoadAIMYears( _
    p_Cn As ADODB.Connection, _
    p_AIMYears() As AIMYEARS_RCD _
) As Integer
'A.Stocksdale Dec 04, 2003 - Common to:
'       AIM_ForecastGenerator.frm,
'       AIMAdvForecast.cls
'and    AIMAdvForecastMod.cls
On Error GoTo ErrorHandler

    Dim Row As Long
    Dim AIM_AIMYears_Load_Sp As ADODB.Command
    Dim rsAIMYears As ADODB.Recordset

    'Redimension Arrays
    ReDim p_AIMYears(1 To 10)

    'Define the Stored Procedures
    Set AIM_AIMYears_Load_Sp = New ADODB.Command
    With AIM_AIMYears_Load_Sp
        Set .ActiveConnection = p_Cn
        .CommandType = adCmdStoredProc
        .CommandText = "AIM_AIMYears_Load_Sp"
        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
            "RETURN", adInteger, adParamReturnValue)
        .Parameters.Append AIM_AIMYears_Load_Sp.CreateParameter( _
            "@FiscalYear", adInteger, adParamInput, , 0)
    End With

    'Load the AIM Calendar/AIM Years
    Set rsAIMYears = New ADODB.Recordset
    With rsAIMYears
        .CursorLocation = adUseClient
        .CursorType = adOpenStatic
        .LockType = adLockReadOnly
    End With
    
    'Load the year collection
    rsAIMYears.Open AIM_AIMYears_Load_Sp

    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(rsAIMYears) Then
        Do Until rsAIMYears.EOF
            Row = Row + 1
            If Row > UBound(p_AIMYears) Then
                ReDim Preserve p_AIMYears(1 To UBound(p_AIMYears) + 10)
            End If
            p_AIMYears(Row).FiscalYear = rsAIMYears("FiscalYear").Value
            p_AIMYears(Row).FYStartDate = rsAIMYears("FYStartDate").Value
            p_AIMYears(Row).FYEndDate = rsAIMYears("FYEndDate").Value
            p_AIMYears(Row).NbrWeeks = rsAIMYears("NbrWeeks").Value
            rsAIMYears.MoveNext
        Loop

        If Row > 0 Then
            ReDim Preserve p_AIMYears(1 To Row)
        End If
    End If

    If f_IsRecordsetValidAndOpen(rsAIMYears) Then rsAIMYears.Close
    Set rsAIMYears = Nothing

    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing

Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsAIMYears) Then rsAIMYears.Close
    Set rsAIMYears = Nothing
    If Not (AIM_AIMYears_Load_Sp Is Nothing) Then Set AIM_AIMYears_Load_Sp.ActiveConnection = Nothing
    Set AIM_AIMYears_Load_Sp = Nothing
    
    'Err.Raise Err.Number, Err.source, Err.Description & "(AIMCalendarBAS.AC_LoadAIMYears)"
     f_HandleErr , , , "AIMCalendar::AC_LoadAIMYears", Now, gDRJobError, True, Err
End Function
