VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
  Persistable = 0  'NotPersistable
  DataBindingBehavior = 0  'vbNone
  DataSourceBehavior  = 0  'vbNone
  MTSTransactionMode  = 0  'NotAnMTSObject
END
Attribute VB_Name = "clsTranslate"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = True
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
'*****************************************************************
'Item:          SSA_AIM Translation Module
'Authors: <AUTHORS>
'Description:   This class designed to be included in each project
'               for the purpose of translating application resources
'               and messages.
'REMOVED FROM SSAAIM.VBP -- REPLACED WITH AIMTranslator.BAS
'Version:       Created Feb 07, 2002
'*****************************************************************
'History:       Future Updates of note to be recorded below.
'
'*****************************************************************

Option Explicit

Private Const C_TDBGRID As String = "TDBGRID"
Private Const C_TDBDROPDOWN As String = "TDBDROPDOWN"

Dim m_LogFile As String

'Entry point for this class
Public Function getTranslationResource(p_ResourceID As String, _
                                        Optional p_Colon As Boolean = False, _
                                        Optional p_Login As Boolean = False) As String
On Error GoTo ErrorHandler
    
    Dim lngRecordIndex As Long
    Dim dtNewDate As Date
    Dim RtnCode As Long
    
    m_LogFile = App.Path & "\" & g_ERRORLOG
    
    On Error Resume Next
    'Check if it's populated
    If gxarTranslate(0, 0) = "" Then
        If Err.Number = 91 _
        Or Err.Number = 9 Then
            'gxarFormColl hasn't been instantiated yet, that's what this call does, so keep going
            'Fetch translations
            RtnCode = fetchTranslation
            If RtnCode <> SUCCEED Then
                getTranslationResource = p_ResourceID
                Exit Function
            End If
        ElseIf Err.Number <> 0 Then
            GoTo ErrorHandler
        End If
    End If
    On Error GoTo ErrorHandler
    
    'Check on the age of the Translation Cache.
    dtNewDate = Format(Now, "ddddd")
    validateTranslationXArray (dtNewDate)

    'If records found for that lang...
    If gxarTranslate.Count(1) >= 1 Then
        'search if data contains requested caption
        lngRecordIndex = gxarTranslate.Find(0, 1, Trim(p_ResourceID), , , XTYPE_STRINGCASESENSITIVE)
        '0 is a valid index, for the very first row, so search for negatives
        If lngRecordIndex >= 0 Then
            'set return value = the matching text
            If p_Colon Then
                'add colon if it is sent
                getTranslationResource = gxarTranslate(lngRecordIndex, 2) + ":"
            Else
                getTranslationResource = gxarTranslate(lngRecordIndex, 2)
            End If
            
        ElseIf Trim(p_ResourceID) <> "" Then
            lngRecordIndex = gxarTranslate.Find(0, 2, Trim(p_ResourceID), , , XTYPE_STRINGCASESENSITIVE)
            If lngRecordIndex < 0 Then
                GoTo ReturnDefault
            Else
                'set return value = the matching text
                If p_Colon Then
                    'add colon if it is sent
                    getTranslationResource = gxarTranslate(lngRecordIndex, 2) + ":"
                Else
                    getTranslationResource = gxarTranslate(lngRecordIndex, 2)
                End If
            End If
        End If
    Else
        GoTo ReturnDefault
    End If
    
Exit Function
ReturnDefault:
    'If nothing found, write this to the error log, then default to what's in the sourcecode
    If Trim(p_ResourceID) <> "" Then
        On Error Resume Next
'''        ' Comment out for production version
'''        Write_Log m_LogFile & ".tmp", _
'''            "Missing translation for ResourceID='" & p_ResourceID & _
'''                                 "'; LanguageID='" & gLangID & "'", _
'''            0, False, False, False, True
        On Error GoTo ErrorHandler
    End If
    If p_Colon Then
        If Right(p_ResourceID, 1) <> ":" Then
           getTranslationResource = p_ResourceID + ":"
        Else
           getTranslationResource = p_ResourceID
        End If
    Else
        getTranslationResource = p_ResourceID
    End If

Exit Function
ErrorHandler:
    If Err.Number = 3001 Or Err.Number = 3709 Then
        Exit Function
    Else
        Err.Raise Err.Number, Err.source, "(getTranslationResource)" & Err.Description
    End If
    
End Function

'Get data from recordset, given lang id
Private Function fetchTranslation(Optional p_Logon As Boolean = False)
On Error GoTo ErrorHandler
    
    Dim Cn As ADODB.Connection
    Dim rsTranslate As ADODB.Recordset
    Dim rsSysCtrl As ADODB.Recordset
    Dim AIM_AIMTranslation_List_Sp As ADODB.Command
    Dim AIM_SysCtrl_Get_Sp As ADODB.Command
    Dim RtnCode As Long
    Dim intRecordCount As Integer
    
    'Connect to the Server
    Set Cn = New ADODB.Connection
    RtnCode = SQLConnection(Cn, CONNECTION_OPEN, False)
    If RtnCode <> SUCCEED Then
        fetchTranslation = RtnCode
        Exit Function
    End If
    
    'Validate
    If Trim(gLangID) = "" Then
        If RtnCode <> SUCCEED Then
            Exit Function
        Else
            'Fetch translations
            Set AIM_SysCtrl_Get_Sp = New ADODB.Command
            With AIM_SysCtrl_Get_Sp
                Set .ActiveConnection = Cn
                .CommandText = "AIM_SysCtrl_Get_Sp"
                .CommandType = adCmdStoredProc
            End With
            
            Set rsSysCtrl = New ADODB.Recordset
            rsSysCtrl.Open AIM_SysCtrl_Get_Sp
            
            'Check if valid recordset
            If f_IsRecordsetValidAndOpen(rsSysCtrl) = False Then
                Err.Description = "Using the default System Language ID."
            Else
                gLangID = rsSysCtrl!dft_LangID
                If Trim(gLangID) = " " Then
                    Err.Description = "Unable to retrieve the Language ID, Can not translate."
                    Exit Function
                Else
                    Err.Description = "Using the default System Language ID."
                End If
            End If
        End If
    End If
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    'Fetch translations
    Set AIM_AIMTranslation_List_Sp = New ADODB.Command
    With AIM_AIMTranslation_List_Sp
        Set .ActiveConnection = Cn
        .CommandText = "AIM_AIMTranslation_List_Sp"
        .CommandType = adCmdStoredProc
        .Parameters.Refresh
    End With
    
    Set rsTranslate = New ADODB.Recordset
    With rsTranslate
        .CursorLocation = adUseClient
        .CursorType = adOpenForwardOnly
        .LockType = adLockReadOnly
    End With
    
    AIM_AIMTranslation_List_Sp.Parameters("@LangIDOption").Value = gLangID
    rsTranslate.Open AIM_AIMTranslation_List_Sp
    
    'Check if valid recordset
    If f_IsRecordsetOpenAndPopulated(rsTranslate) = False Then
        Write_Message "Translation failed. No records found"
        gxarTranslate.ReDim 0, 0, 0, 4
    Else
        rsTranslate.MoveFirst
        gxarTranslate.LoadRows rsTranslate.GetRows(adGetRowsRest)
    End If
    
    gTranslateDate = Format(Now, "ddddd")
    
    'Close the database connection
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    If f_IsRecordsetValidAndOpen(rsTranslate) Then rsTranslate.Close
    Set rsTranslate = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Set Cn = Nothing
    
Exit Function
ErrorHandler:
    If f_IsRecordsetValidAndOpen(rsSysCtrl) Then rsSysCtrl.Close
    Set rsSysCtrl = Nothing
    
    If f_IsRecordsetValidAndOpen(rsTranslate) Then rsTranslate.Close
    Set rsTranslate = Nothing
    
    RtnCode = SQLConnection(Cn, CONNECTION_CLOSE, False)
    Err.Raise Err.Number, Err.source, "(fetchTranslation)" & Err.Description
End Function

Private Function validateTranslationXArray(p_dtNewDate As Date) As Boolean
On Error GoTo ErrorHandler

    Dim RtnCode As Long
    
    'Set default status to negative
    validateTranslationXArray = False
    
    'Check Validity
    If p_dtNewDate <> gTranslateDate _
    Or StrComp(gLangID, gxarTranslate(0, 0), vbTextCompare) <> 0 _
    Then
        'Re-retrieve all Translations
        RtnCode = fetchTranslation
        If RtnCode <> SUCCEED Then
            validateTranslationXArray = False
            Exit Function
        End If
    End If
    
    'Return success
    validateTranslationXArray = True

Exit Function
ErrorHandler:
    Err.Raise Err.Number, Err.source, "(validateTranslationXArray)" & Err.Description
End Function
