/****** Object:  Table [dbo].[AIMRoles]    Script Date: 10/12/2004 10:54:03 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMRoles]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMRoles]
GO

CREATE TABLE [dbo].[AIMRoles] (
	[RoleId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RoleDescription] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[RoleLevel] [int] NOT NULL ,
	[scarray] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RepToRoleID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AltSourceExcpts] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	CONSTRAINT [PK_AIMRoles] PRIMARY KEY  CLUSTERED 
	(
		[RoleId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


