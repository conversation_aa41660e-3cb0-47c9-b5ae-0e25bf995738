/****** Object:  Table [dbo].[AIMDxLC]    Script Date: 05/30/2003 11:04:25 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxLC]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxLC]
GO

CREATE TABLE [dbo].[AIMDxLC] (
	[SeqNbr] [int] NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress4] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LCountry] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LRank] [smallint] NULL,
	[PutAwayDays] [tinyint] NULL,
	[ReplenCost] [decimal](18, 0) NULL,
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LeadTimeSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
    [Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LookBackPds] [smallint] NULL,
	[DmdScalingFactor] [decimal](5, 0) NULL,
	[ScalingEffUntil] [datetime] NULL ,
	[Freeze_Period] [int] NULL,
	[UpdateCurrentYearOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[DropShip_XDock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Dft_ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[ExceptionPct] [tinyint] NULL ,
	 PRIMARY KEY  NONCLUSTERED 
	(
		[Lcid]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


