/****** Object:  Table [dbo].[AIMProductionConstraintDetail]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMProductionConstraintDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMProductionConstraintDetail]
GO

CREATE TABLE [dbo].[AIMProductionConstraintDetail] (
	[ConstraintID] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[MinUnits] [numeric](18, 0) NOT NULL ,
	[MaxUnits] [numeric](18, 0) NOT NULL ,
	CONSTRAINT [PK_AIMProductionConstraintDetail] PRIMARY KEY  CLUSTERED 
	(
		[ConstraintID],
		[Item]
	)  ON [PRIMARY] ,
	CONSTRAINT [FK_AIMProductionConstraintDetail_AIMProductionConstraint] FOREIGN KEY 
	(
		[ConstraintID]
	) REFERENCES [dbo].[AIMProductionConstraint] (
		[ConstraintID]
	)
) ON [PRIMARY]
GO


