/****** Object:  Table [dbo].[AIMPromotions]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMPromotions]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMPromotions]
GO

CREATE TABLE [dbo].[AIMPromotions] (
	[PmId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[PmDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[PmStatus] [bit] NOT NULL CONSTRAINT [DF_AIMPromotions_PmStatus] DEFAULT (0),
	[PmStartDate] [smalldatetime] NOT NULL ,
	[PmEndDate] [smalldatetime] NOT NULL ,
	[PmAdj01] [decimal](5, 2) NOT NULL ,
	[PmAdj02] [decimal](5, 2) NOT NULL ,
	[PmAdj03] [decimal](5, 2) NOT NULL ,
	[PmAdj04] [decimal](5, 2) NOT NULL ,
	[PmAdj05] [decimal](5, 2) NOT NULL ,
	[PmAdj06] [decimal](5, 2) NOT NULL ,
	[PmAdj07] [decimal](5, 2) NOT NULL ,
	[PmAdj08] [decimal](5, 2) NOT NULL ,
	[PmAdj09] [decimal](5, 2) NOT NULL ,
	[PmAdj10] [decimal](5, 2) NOT NULL ,
	[PmAdj11] [decimal](5, 2) NOT NULL ,
	[PmAdj12] [decimal](5, 2) NOT NULL ,
	[PmAdj13] [decimal](5, 2) NOT NULL ,
	[PmAdj14] [decimal](5, 2) NOT NULL ,
	[PmAdj15] [decimal](5, 2) NOT NULL ,
	[PmAdj16] [decimal](5, 2) NOT NULL ,
	[PmAdj17] [decimal](5, 2) NOT NULL ,
	[PmAdj18] [decimal](5, 2) NOT NULL ,
	[PmAdj19] [decimal](5, 2) NOT NULL ,
	[PmAdj20] [decimal](5, 2) NOT NULL ,
	[PmAdj21] [decimal](5, 2) NOT NULL ,
	[PmAdj22] [decimal](5, 2) NOT NULL ,
	[PmAdj23] [decimal](5, 2) NOT NULL ,
	[PmAdj24] [decimal](5, 2) NOT NULL ,
	[PmAdj25] [decimal](5, 2) NOT NULL ,
	[PmAdj26] [decimal](5, 2) NOT NULL ,
	[PmAdj27] [decimal](5, 2) NOT NULL ,
	[PmAdj28] [decimal](5, 2) NOT NULL ,
	[PmAdj29] [decimal](5, 2) NOT NULL ,
	[PmAdj30] [decimal](5, 2) NOT NULL ,
	[PmAdj31] [decimal](5, 2) NOT NULL ,
	[PmAdj32] [decimal](5, 2) NOT NULL ,
	[PmAdj33] [decimal](5, 2) NOT NULL ,
	[PmAdj34] [decimal](5, 2) NOT NULL ,
	[PmAdj35] [decimal](5, 2) NOT NULL ,
	[PmAdj36] [decimal](5, 2) NOT NULL ,
	[PmAdj37] [decimal](5, 2) NOT NULL ,
	[PmAdj38] [decimal](5, 2) NOT NULL ,
	[PmAdj39] [decimal](5, 2) NOT NULL ,
	[PmAdj40] [decimal](5, 2) NOT NULL ,
	[PmAdj41] [decimal](5, 2) NOT NULL ,
	[PmAdj42] [decimal](5, 2) NOT NULL ,
	[PmAdj43] [decimal](5, 2) NOT NULL ,
	[PmAdj44] [decimal](5, 2) NOT NULL ,
	[PmAdj45] [decimal](5, 2) NOT NULL ,
	[PmAdj46] [decimal](5, 2) NOT NULL ,
	[PmAdj47] [decimal](5, 2) NOT NULL ,
	[PmAdj48] [decimal](5, 2) NOT NULL ,
	[PmAdj49] [decimal](5, 2) NOT NULL ,
	[PmAdj50] [decimal](5, 2) NOT NULL ,
	[PmAdj51] [decimal](5, 2) NOT NULL ,
	[PmAdj52] [decimal](5, 2) NOT NULL ,
	CONSTRAINT [PK_AIMPromotions] PRIMARY KEY  NONCLUSTERED 
	(
		[PmId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


