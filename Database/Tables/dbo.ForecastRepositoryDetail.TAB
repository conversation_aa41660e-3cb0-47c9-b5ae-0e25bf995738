/****** Object:  Table [dbo].[ForecastRepositoryDetail]    Script Date: 11/16/04 1:16:29 PM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepositoryDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepositoryDetail]
GO

/****** Object:  Table [dbo].[ForecastRepositoryDetail]    Script Date: 11/16/04 1:16:30 PM ******/
CREATE TABLE [dbo].[ForecastRepositoryDetail] (
	[RepositoryKey] [numeric](18, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstPdBegDate] [datetime] NOT NULL ,
	[FcstPdEndDate] [datetime] NOT NULL ,
	[Fcst] [int] NOT NULL ,
	[FcstAdj] [int] NOT NULL ,
	[MasterFcstAdj] [int] NOT NULL ,
	[FcstNetReq] [int] NOT NULL ,
	[FcstAdjNetReq] [int] NOT NULL ,
	[QtyAdj] [int] NOT NULL ,
	[QtyAdjOverRide] [int] NULL ,
	[QtyAdjPct] [int] NOT NULL ,
	[AdjOverRide] [smallint] NOT NULL ,
	[MasterQtyAdj] [int] NOT NULL ,
	[MasterQtyAdjOverRide] [int] NULL ,
	[MasterQtyAdjPct] [int] NOT NULL ,
	[MasterAdjOverRide] [smallint] NOT NULL ,
	[HistDmd] [int] NULL ,
	[QtyActualShipped] [int] NULL ,
	[QtyProjectedInventory] [int] NULL ,
	[ProdConst] [int] NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ForecastRepositoryDetail] WITH NOCHECK ADD 
	CONSTRAINT [PK_ForecastRepositoryDetail] PRIMARY KEY  CLUSTERED 
	(
		[RepositoryKey],
		[LcID],
		[Item],
		[FcstPdBegDate]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[ForecastRepositoryDetail] ADD 
	CONSTRAINT [DF_ForecastRepositoryDetail_Fcst] DEFAULT (0) FOR [Fcst],
	CONSTRAINT [DF_ForecastRepositoryDetail_FcstAdj] DEFAULT (0) FOR [FcstAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterFcstAdj] DEFAULT (0) FOR [MasterFcstAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_FcstNetReq] DEFAULT (0) FOR [FcstNetReq],
	CONSTRAINT [DF_ForecastRepositoryDetail_FcstAdjNetReq] DEFAULT (0) FOR [FcstAdjNetReq],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyAdjust] DEFAULT (0) FOR [QtyAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyAdjustPct] DEFAULT (0) FOR [QtyAdjPct],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyAdjOverRide] DEFAULT (0) FOR [AdjOverRide],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterQtyAdj] DEFAULT (0) FOR [MasterQtyAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterQtyAdjPct] DEFAULT (0) FOR [MasterQtyAdjPct],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterAdjOverRide] DEFAULT (0) FOR [MasterAdjOverRide],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyActualOrdered] DEFAULT (0) FOR [HistDmd],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyActualShipped] DEFAULT (0) FOR [QtyActualShipped],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyProjectedInventory] DEFAULT (0) FOR [QtyProjectedInventory],
	CONSTRAINT [DF_ForecastRepositoryDetail_ProdConst] DEFAULT (0) FOR [ProdConst]
GO

 CREATE  INDEX [IX_ForecastRepositoryDetail] ON [dbo].[ForecastRepositoryDetail]([RepositoryKey], [LcID], [Item], [FcstPdBegDate], [FcstPdEndDate]) ON [PRIMARY]
GO



