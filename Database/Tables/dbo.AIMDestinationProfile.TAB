/****** Object:  Table [dbo].[AIMDestinationProfile]    Script Date: 05/30/2003 11:04:23 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDestinationProfile]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDestinationProfile]
GO

CREATE TABLE [dbo].[AIMDestinationProfile] (
	[LcID_Destination] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ExceptionPct] [tinyint] NOT NULL CONSTRAINT [DF_AIMDProf_ExceptionPct] DEFAULT (0),
	[AvailableQty] [int] NOT NULL ,
	[TargetQty] [int] NOT NULL ,
	 PRIMARY KEY  CLUSTERED 
	(
		[LcID_Destination],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


