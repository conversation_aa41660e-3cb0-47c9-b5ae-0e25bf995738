/****** Object:  Table [dbo].[AIMTransferPolicy]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMTransferPolicy]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMTransferPolicy]
GO

CREATE TABLE [dbo].[AIMTransferPolicy] (
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcidTransferTo] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TransferType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[EnableTransferPolicy] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMTransferPolicy] PRIMARY KEY  CLUSTERED 
	(
		[Lcid],
		[LcidTransferTo],
		[TransferType]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


