	/* Requestor Priority -- this will hold information on priorities for stores with orders */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_Req_Pri]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_Req_Pri]
	GO
	CREATE TABLE AllocScratch_Req_Pri (
		SessionID BIGINT NOT NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		DestinationPriority INT NOT NULL
	);
	CREATE CLUSTERED INDEX ASRP_SESS ON AllocScratch_Req_Pri (SessionID)
	CREATE INDEX ASRP_PRI ON AllocScratch_Req_Pri (DestinationPriority)
	GO
	
