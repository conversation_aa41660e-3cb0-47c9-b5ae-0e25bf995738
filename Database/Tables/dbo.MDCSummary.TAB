/****** Object:  Table [dbo].[MDCSummary]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[MDCSummary]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[MDCSummary]
GO

CREATE TABLE [dbo].[MDCSummary] (
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Accum_Lt] [smallint] NOT NULL CONSTRAINT [DF_MDCSummary_Accum_Lt] DEFAULT (0),
	[ReviewTime] [smallint] NOT NULL CONSTRAINT [DF_MDCSummary_ReviewTime] DEFAULT (0),
	[OrderPt] [int] NOT NULL CONSTRAINT [DF_MDCSummary_OrderPt] DEFAULT (0),
	[OrderQty] [int] NOT NULL CONSTRAINT [DF_MDCSummary_OrderQty] DEFAULT (0),
	[SafetyStock] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_SafetyStock] DEFAULT (0),
	[FcstRT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_FcstRT] DEFAULT (0),
	[FcstLT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_FcstLT] DEFAULT (0),
	[Fcst_Month] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_Fcst_Month] DEFAULT (0),
	[Fcst_Quarter] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_Fcst_Quarter] DEFAULT (0),
	[Fcst_Year] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_Fcst_Year] DEFAULT (0),
	CONSTRAINT [PK_MDCSummary] PRIMARY KEY  NONCLUSTERED 
	(
		[MDC],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


