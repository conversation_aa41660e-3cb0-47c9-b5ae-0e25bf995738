	/* Need ratio -- this will hold information on need ratios	*/
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_Req_Ratio]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_Req_Ratio]
	GO
	CREATE TABLE AllocScratch_Req_Ratio (
		SessionID BIGINT NOT NULL,
		OrdNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		LineNbr nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		NeedRatio DECIMAL(15, 5) NOT NULL,
		WeightedNeed DECIMAL(15, 5) NOT NULL
	);
	CREATE CLUSTERED INDEX ASRR_SESS ON AllocScratch_Req_Ratio (SessionID)
	CREATE INDEX ASRR_RATIO ON AllocScratch_Req_Ratio (NeedRatio)
	CREATE INDEX ASRR_WEIGHTEDNEED ON AllocScratch_Req_Ratio (WeightedNeed)
	GO

