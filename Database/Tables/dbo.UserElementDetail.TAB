
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[UserElementDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[UserElementDetail]
GO

CREATE TABLE [dbo].[UserElementDetail] (
	[UserElementId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstPdBegDate] [datetime] NOT NULL ,
	[FcstPdEndDate] [datetime] NOT NULL ,
	[Qty] [int] NOT NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[UserElementDetail] WITH NOCHECK ADD 
	CONSTRAINT [PK_UserElementDetail] PRIMARY KEY  CLUSTERED 
	(
		[UserElementId],
		[LcId],
		[Item],
		[FcstPdBegDate]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[UserElementDetail] ADD 
	CONSTRAINT [DF_UserElementDetail_Qty] DEFAULT (0) FOR [Qty]
GO


