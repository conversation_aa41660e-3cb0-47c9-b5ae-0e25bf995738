/****** Object:  Table [dbo].[AIMUsers]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMUsers]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMUsers]
GO

CREATE TABLE [dbo].[AIMUsers] (
	[UserId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SLevel] [tinyint] NOT NULL CONSTRAINT [DF_AIMUsers_SLevel] DEFAULT (0),
	[ScArray] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DmdUpdExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_DmdUpdExcpts] DEFAULT ('Y'),
	[OnPromotionExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_OnPromotion] DEFAULT ('N'),
	[ExtCostExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_ExtCostExcpts] DEFAULT ('N'),
	[ExtCostLimit] [decimal](10, 2) NOT NULL CONSTRAINT [DF_AIMUsers_ExtCostLimit] DEFAULT (1000),
	[PackSizeExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_PackSizeExcpts] DEFAULT ('N'),
	[PS_PctRoundLimit] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMUsers_PS_PctRoundLimit] DEFAULT (0.7),
	[PS_ExtCostLimit] [decimal](10, 2) NOT NULL CONSTRAINT [DF_AIMUsers_PS_ExtCostLimit] DEFAULT (100),
	[LastWeekSaleExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_LastWeekSaleExcepts] DEFAULT ('N'),
	[OverDuePO] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_OverDuePO] DEFAULT ('N'),
	[SafetyStockEroded] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_SafetyStockEroded] DEFAULT ('N'),
	[BackOrdered] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_BackOrdered] DEFAULT ('N'),
	[InvAvailRestriction] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_InvAvailRestriction] DEFAULT ('N'),
	[InvAvailList] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_InvAvailList] DEFAULT (''),
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_UserInitials] DEFAULT (''),
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_LangID] DEFAULT ('en-us'),
	[LogErrors] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_LogErrors] DEFAULT ('N'),
    	[AltSourceExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL DEFAULT ('N'),
	[POExtCost] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL DEFAULT ('N'),
	[POExtCostLimit] [decimal](18, 0)  DEFAULT (0) ,
	[RoleId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL DEFAULT ('BasicUser1'),
	[VendorCostExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_VendorCostExcpts] DEFAULT ('N'),
	CONSTRAINT [PK_AIMUsers] PRIMARY KEY  CLUSTERED 
	(
		[UserId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

