/****** Object:  Table [dbo].[SysCtrl]    Script Date: 05/30/2003 11:04:54 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[SysCtrl]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[SysCtrl]
GO

CREATE TABLE [dbo].[SysCtrl] (
	[CName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Address1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Address2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Address3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[City] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[State] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CoZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Dft_MinRchPct] [numeric](3, 1) NOT NULL ,
	[Dft_BestRchPct] [numeric](3, 1) NOT NULL ,
	[DxPath] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DxPath_Out] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DxPath_Backup] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Dft_POStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DxPO_Option] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[KFactor] [decimal](5, 2) NOT NULL CONSTRAINT [DF_SysCtrl_KFactor] DEFAULT (30),
	[MinROAI] [decimal](5, 2) NOT NULL CONSTRAINT [DF_SysCtrl_MinROAI] DEFAULT (20),
	[Vendor_Sizing] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Priority_Min_Dser] [numeric](3, 2) NOT NULL ,
	[Priority_Min_VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Addl_LTDays] [tinyint] NOT NULL ,
	[Lt_Filter_Pct] [numeric](3, 2) NOT NULL ,
	[Retain_SD_Opt] [tinyint] NOT NULL ,
	[Calc_Perform] [tinyint] NOT NULL ,
	[LogFile] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ByIdSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_ByIdSource] DEFAULT ('V'),
	[SeasSmoothingPds] [tinyint] NOT NULL CONSTRAINT [DF_SysCtrl_SeasSmoothingPds] DEFAULT (5),
	[CurSaVersion] [smallint] NOT NULL CONSTRAINT [DF_SysCtrl_CurSaVersion] DEFAULT (0),
	[Last_POSeq] [int] NOT NULL CONSTRAINT [DF_SysCtrl_Last_POSeq] DEFAULT (0),
	[ClassOption] [tinyint] NOT NULL CONSTRAINT [DF_SysCtrl_ClassOption] DEFAULT (1),
	[AvgInvAlpha] [decimal](3, 2) NOT NULL CONSTRAINT [DF_SysCtrl_AvgInvAlpha] DEFAULT (0.05),
	[UpdateGoalsOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_UpdateGoalsOption] DEFAULT ('Y'),
	[UpdateVnLT] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_UpdateVnLT] DEFAULT ('Y'),
	[VnLTPctChgFilter] [decimal](5, 2) NOT NULL CONSTRAINT [DF_SysCtrl_VnLTPctChgFilter] DEFAULT (0.5),
	[VnLTDaysChgFilter] [smallint] NOT NULL CONSTRAINT [DF_SysCtrl_VnLTDaysChgFilter] DEFAULT (1),
	[MDCOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_MDCOption] DEFAULT ('N'),
	[LT_Alpha] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_LT_Alpha] DEFAULT (0.1),
	[LT_CtrlInterval_Pct] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_LT_CtrlInterval_Pct] DEFAULT (3),
	[Int_Enabled] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_Int_Enabled] DEFAULT ('N'),
	[Int_MinPctZero] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_MinPctZero] DEFAULT (0.3),
	[Int_MaxPctZero] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_MaxPctZero] DEFAULT (0.9),
	[Int_SeasonalityIndex] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_SeasonalityIndex] DEFAULT (1.000),
	[Int_SrvLvlOverrideFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_Int_SrvLvlOverrideFlag] DEFAULT ('Y'),
	[Int_SrvLvl] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_SrvLvl] DEFAULT (0.85),
	[Int_LookBackPds] [int] NOT NULL CONSTRAINT [DF_SysCtrl_Int_LookBackPds] DEFAULT (52),
	[AIMBatchPath] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_AIMBatchPath] DEFAULT (''),
	[dft_LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_dft_LangID] DEFAULT ('EN-US'),
	[ColonOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[GridAutoSizeOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateFormat] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TimeFormat] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UnicodeOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ProductionConstraint] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_ProductionConstraint] DEFAULT (N'Y'),
	[ProdConstraintGrtZero] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_SysCtrl_ProdConstraintGrtZero] DEFAULT (N'Y'),
	[AllocExceptionPct] [int] NULL,
	[SessionID] [bigint] NULL,
	[AllocNeedDetermination] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AllocExceptionProcess] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CarryForwardRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_CarryForwardRounding] DEFAULT (N'Y'),
	[LastOrderGenerationProcess] [datetime] NULL,
	[MasterItemOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_MasterItemOption] DEFAULT (N'N'),
	[CreateDBUser] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_CreateDBUser] DEFAULT (N'Y'),
	[UpdateDBPassword] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_UpdateDBPassword] DEFAULT (N'Y'),
	[AllocPrepackEnabled] [bit] NOT NULL DEFAULT 0,
	[AllowNegativeAvailQty] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_AllowNegativeAvailQty] DEFAULT (N'Y'),
	[NumOffDaysBehindFromCurrentDate] [int] NULL DEFAULT (7),
	[WantErrorsTobeLoggedToDatabase] [bit] NULL DEFAULT (1)
	
) ON [PRIMARY]
GO


