/****** Object:  Table [dbo].[ItemHistory]    Script Date: 05/30/2003 11:04:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItemHistory]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItemHistory]
GO

CREATE TABLE [dbo].[ItemHistory] (
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SubsItem][nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[HisYear] [smallint] NOT NULL ,
	[CPS01] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps01] DEFAULT (0),
	[CPS02] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps02] DEFAULT (0),
	[CPS03] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps03] DEFAULT (0),
	[CPS04] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps04] DEFAULT (0),
	[CPS05] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps05] DEFAULT (0),
	[CPS06] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps06] DEFAULT (0),
	[CPS07] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps07] DEFAULT (0),
	[CPS08] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps08] DEFAULT (0),
	[CPS09] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps09] DEFAULT (0),
	[CPS10] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps10] DEFAULT (0),
	[CPS11] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps11] DEFAULT (0),
	[CPS12] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps12] DEFAULT (0),
	[CPS13] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps13] DEFAULT (0),
	[CPS14] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps14] DEFAULT (0),
	[CPS15] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps15] DEFAULT (0),
	[CPS16] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps16] DEFAULT (0),
	[CPS17] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps17] DEFAULT (0),
	[CPS18] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps18] DEFAULT (0),
	[CPS19] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps19] DEFAULT (0),
	[CPS20] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps20] DEFAULT (0),
	[CPS21] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps21] DEFAULT (0),
	[CPS22] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps22] DEFAULT (0),
	[CPS23] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps23] DEFAULT (0),
	[CPS24] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps24] DEFAULT (0),
	[CPS25] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps25] DEFAULT (0),
	[CPS26] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps26] DEFAULT (0),
	[CPS27] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps27] DEFAULT (0),
	[CPS28] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps28] DEFAULT (0),
	[CPS29] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps29] DEFAULT (0),
	[CPS30] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps30] DEFAULT (0),
	[CPS31] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps31] DEFAULT (0),
	[CPS32] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps32] DEFAULT (0),
	[CPS33] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps33] DEFAULT (0),
	[CPS34] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps34] DEFAULT (0),
	[CPS35] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps35] DEFAULT (0),
	[CPS36] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps36] DEFAULT (0),
	[CPS37] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps37] DEFAULT (0),
	[CPS38] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps38] DEFAULT (0),
	[CPS39] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps39] DEFAULT (0),
	[CPS40] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps40] DEFAULT (0),
	[CPS41] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps41] DEFAULT (0),
	[CPS42] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps42] DEFAULT (0),
	[CPS43] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps43] DEFAULT (0),
	[CPS44] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps44] DEFAULT (0),
	[CPS45] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps45] DEFAULT (0),
	[CPS46] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps46] DEFAULT (0),
	[CPS47] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps47] DEFAULT (0),
	[CPS48] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps48] DEFAULT (0),
	[CPS49] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps49] DEFAULT (0),
	[CPS50] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps50] DEFAULT (0),
	[CPS51] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps51] DEFAULT (0),
	[CPS52] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps52] DEFAULT (0),
	[QtyOrd01] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord01] DEFAULT (0),
	[QtyOrd02] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord02] DEFAULT (0),
	[QtyOrd03] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord021] DEFAULT (0),
	[QtyOrd04] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord022] DEFAULT (0),
	[QtyOrd05] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord023] DEFAULT (0),
	[QtyOrd06] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord024] DEFAULT (0),
	[QtyOrd07] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord025] DEFAULT (0),
	[QtyOrd08] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord026] DEFAULT (0),
	[QtyOrd09] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord027] DEFAULT (0),
	[QtyOrd10] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord028] DEFAULT (0),
	[QtyOrd11] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord029] DEFAULT (0),
	[QtyOrd12] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0210] DEFAULT (0),
	[QtyOrd13] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0211] DEFAULT (0),
	[QtyOrd14] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0212] DEFAULT (0),
	[QtyOrd15] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0213] DEFAULT (0),
	[QtyOrd16] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0214] DEFAULT (0),
	[QtyOrd17] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0215] DEFAULT (0),
	[QtyOrd18] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0216] DEFAULT (0),
	[QtyOrd19] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0217] DEFAULT (0),
	[QtyOrd20] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0218] DEFAULT (0),
	[QtyOrd21] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0219] DEFAULT (0),
	[QtyOrd22] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0220] DEFAULT (0),
	[QtyOrd23] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0221] DEFAULT (0),
	[QtyOrd24] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0222] DEFAULT (0),
	[QtyOrd25] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0223] DEFAULT (0),
	[QtyOrd26] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0224] DEFAULT (0),
	[QtyOrd27] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0225] DEFAULT (0),
	[QtyOrd28] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0226] DEFAULT (0),
	[QtyOrd29] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0227] DEFAULT (0),
	[QtyOrd30] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0228] DEFAULT (0),
	[QtyOrd31] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0229] DEFAULT (0),
	[QtyOrd32] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0230] DEFAULT (0),
	[QtyOrd33] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0231] DEFAULT (0),
	[QtyOrd34] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0232] DEFAULT (0),
	[QtyOrd35] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0233] DEFAULT (0),
	[QtyOrd36] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0234] DEFAULT (0),
	[QtyOrd37] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0235] DEFAULT (0),
	[QtyOrd38] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0236] DEFAULT (0),
	[QtyOrd39] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0237] DEFAULT (0),
	[QtyOrd40] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0238] DEFAULT (0),
	[QtyOrd41] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0239] DEFAULT (0),
	[QtyOrd42] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0240] DEFAULT (0),
	[QtyOrd43] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0241] DEFAULT (0),
	[QtyOrd44] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0242] DEFAULT (0),
	[QtyOrd45] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0243] DEFAULT (0),
	[QtyOrd46] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0244] DEFAULT (0),
	[QtyOrd47] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0245] DEFAULT (0),
	[QtyOrd48] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0246] DEFAULT (0),
	[QtyOrd49] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0247] DEFAULT (0),
	[QtyOrd50] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0248] DEFAULT (0),
	[QtyOrd51] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0249] DEFAULT (0),
	[QtyOrd52] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0250] DEFAULT (0),
	[OrdCnt01] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt01] DEFAULT (0),
	[OrdCnt02] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011] DEFAULT (0),
	[OrdCnt03] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011_1] DEFAULT (0),
	[OrdCnt04] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt012] DEFAULT (0),
	[OrdCnt05] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt013] DEFAULT (0),
	[OrdCnt06] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011_2] DEFAULT (0),
	[OrdCnt07] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt012_1] DEFAULT (0),
	[OrdCnt08] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt013_1] DEFAULT (0),
	[OrdCnt09] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt014] DEFAULT (0),
	[OrdCnt10] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt015] DEFAULT (0),
	[OrdCnt11] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt016] DEFAULT (0),
	[OrdCnt12] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt017] DEFAULT (0),
	[OrdCnt13] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt018] DEFAULT (0),
	[OrdCnt14] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt019] DEFAULT (0),
	[OrdCnt15] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt0110] DEFAULT (0),
	[OrdCnt16] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011_3] DEFAULT (0),
	[OrdCnt17] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt012_2] DEFAULT (0),
	[OrdCnt18] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt013_2] DEFAULT (0),
	[OrdCnt19] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt014_1] DEFAULT (0),
	[OrdCnt20] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt015_1] DEFAULT (0),
	[OrdCnt21] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt016_1] DEFAULT (0),
	[OrdCnt22] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt017_1] DEFAULT (0),
	[OrdCnt23] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt018_1] DEFAULT (0),
	[OrdCnt24] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt241] DEFAULT (0),
	[OrdCnt25] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt242] DEFAULT (0),
	[OrdCnt26] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt243] DEFAULT (0),
	[OrdCnt27] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt244] DEFAULT (0),
	[OrdCnt28] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt245] DEFAULT (0),
	[OrdCnt29] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt291] DEFAULT (0),
	[OrdCnt30] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt301] DEFAULT (0),
	[OrdCnt31] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt302] DEFAULT (0),
	[OrdCnt32] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt303] DEFAULT (0),
	[OrdCnt33] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt304] DEFAULT (0),
	[OrdCnt34] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt305] DEFAULT (0),
	[OrdCnt35] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt306] DEFAULT (0),
	[OrdCnt36] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt307] DEFAULT (0),
	[OrdCnt37] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt308] DEFAULT (0),
	[OrdCnt38] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt309] DEFAULT (0),
	[OrdCnt39] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt3010] DEFAULT (0),
	[OrdCnt40] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt401] DEFAULT (0),
	[OrdCnt41] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt402] DEFAULT (0),
	[OrdCnt42] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt403] DEFAULT (0),
	[OrdCnt43] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt404] DEFAULT (0),
	[OrdCnt44] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt405] DEFAULT (0),
	[OrdCnt45] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt406] DEFAULT (0),
	[OrdCnt46] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt407] DEFAULT (0),
	[OrdCnt47] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt408] DEFAULT (0),
	[OrdCnt48] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt409] DEFAULT (0),
	[OrdCnt49] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt4010] DEFAULT (0),
	[OrdCnt50] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt501] DEFAULT (0),
	[OrdCnt51] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt502] DEFAULT (0),
	[OrdCnt52] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt511] DEFAULT (0),
	CONSTRAINT [PK_ItemHistory] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Item],
		[SubsItem],
		[HisYear]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


