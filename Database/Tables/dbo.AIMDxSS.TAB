/****** Object:  Table [dbo].[AIMDxSS]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxSS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxSS]
GO

CREATE TABLE [dbo].[AIMDxSS] (
	[SeqNbr] [int] NULL ,
	[Status] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[InActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Weight] [decimal](10, 4) NULL ,
	[Cube] [decimal](10, 4) NULL ,
	[ListPrice] [decimal](10, 4) NULL ,
	[Price] [decimal](10, 4) NULL ,
	[Cost] [decimal](10, 4) NULL ,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ConvFactor] [int] NULL ,
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LeadTime] [smallint] NULL ,
	[UPC] [nvarchar] (22) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Oh] [int] NULL ,
	[Oo] [int] NULL ,
	[ComStk] [int] NULL ,
	[BkOrder] [int] NULL ,
	[BkComStk] [int] NULL ,
	[StkDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[BinLocation] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[BkQty01] [int] NULL ,
	[BkCost01] [decimal](10, 4) NULL ,
	[BkQty02] [int] NULL ,
	[BkCost02] [decimal](10, 4) NULL ,
	[BkQty03] [int] NULL ,
	[BkCost03] [decimal](10, 4) NULL ,
	[BkQty04] [int] NULL ,
	[BkCost04] [decimal](10, 4) NULL ,
	[BkQty05] [int] NULL ,
	[BkCost05] [decimal](10, 4) NULL ,
	[BkQty06] [int] NULL ,
	[BkCost06] [decimal](10, 4) NULL ,
	[BkQty07] [int] NULL ,
	[BkCost07] [decimal](10, 4) NULL ,
	[BkQty08] [int] NULL ,
	[BkCost08] [decimal](10, 4) NULL ,
	[BkQty09] [int] NULL ,
	[BkCost09] [decimal](10, 4) NULL ,
	[BkQty10] [int] NULL ,
	[BkCost10] [decimal](10, 4) NULL ,
	[OnPromotion] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OldUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UOMFact] [decimal](10, 4) NULL ,
	[OldItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPONbr_1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPODate_1] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPOQty_1] [int] NULL ,
	[NextPONbr_2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPODate_2] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPOQty_2] [int] NULL ,
	[NextPONbr_3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPODate_3] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPOQty_3] [int] NULL ,
	[UserRef1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Freeze_Forecast] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AllocatableQty] [int] NULL ,
	[KitBOMFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AllocConvFactor] [int] NOT NULL, 
) ON [PRIMARY]
GO


