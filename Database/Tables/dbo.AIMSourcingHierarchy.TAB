/****** Object:  Table [dbo].[AIMSourcingHierarchy]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMSourcingHierarchy]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMSourcingHierarchy]
GO

CREATE TABLE [dbo].[AIMSourcingHierarchy] (
	[LcID_Destination] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcID_Source] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Src_Priority] [smallint] NOT NULL CONSTRAINT [DF__AIMSourci__Src_P__20245954] DEFAULT (0),
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF__AIMSourci__Revie__21187D8D] DEFAULT (N'sa' COLLATE SQL_Latin1_General_CP1_CI_AS),
	 PRIMARY KEY  CLUSTERED 
	(
		[LcID_Destination],
		[LcID_Source]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


