/****** Object:  Table [dbo].[DXElements]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DXElements]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[DXElements]
GO

CREATE TABLE [dbo].[DXElements] (
	[TxnSet] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Seq] [smallint] NOT NULL ,
	[Element] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Description] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Mask] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Length] [smallint] NOT NULL ,
	[Notes] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	CONSTRAINT [PK_DXElements] PRIMARY KEY  CLUSTERED 
	(
		[TxnSet],
		[Seq]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


