/****** Object:  Table [dbo].[POStatistics]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[POStatistics]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[POStatistics]
GO

CREATE TABLE [dbo].[POStatistics] (
	[SeqNbr] [int] NOT NULL ,
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IsDate] [datetime] NOT NULL ,
	[Original_NbrLines] [int] NOT NULL ,
	[NbrLines] [int] NOT NULL ,
	[Original_VSOQ] [int] NOT NULL ,
	[VSOQ] [int] NOT NULL ,
	[Original_ExtCost] [decimal](10, 2) NOT NULL ,
	[ExtCost] [decimal](10, 2) NOT NULL ,
	CONSTRAINT [PK_POStatistics] PRIMARY KEY  CLUSTERED 
	(
		[ById],
		[VnId],
		[Assort],
		[SeqNbr]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_ById] ON [dbo].[POStatistics]([ById], [IsDate]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_VnIdAssort] ON [dbo].[POStatistics]([VnId], [Assort], [IsDate]) ON [PRIMARY]
GO


