/****** Object:  Table [dbo].[SSA_DR_Migration]    Script Date: 05/30/2003 11:04:42 AM ******/
-- Global uniqueness would be necessary for these Migration_Log tables
--  because the database migration script copies the contents of these as well.
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[SSA_DR_Migration]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[SSA_DR_Migration]
GO

CREATE TABLE [dbo].[SSA_DR_Migration] (
	[EAM_ID] uniqueidentifier Default NewID() NOT NULL,
	[ProductName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OldVersion] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[NewVersion] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TransactionCount] [bigint] NOT NULL ,
	[TotalTimeMinute] [int] NOT NULL ,
	[StartTime] [datetime] NOT NULL ,
	[EndTime] [datetime] NOT NULL ,
	CONSTRAINT [PK_SSA_DR_Migration] PRIMARY KEY  CLUSTERED 
	(
		[EAM_ID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX EAM_ProdNewVer ON SSA_DR_Migration (ProductName, NewVersion)
GO
