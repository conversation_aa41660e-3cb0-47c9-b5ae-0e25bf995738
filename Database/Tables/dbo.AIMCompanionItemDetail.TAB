if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMCompanionItemDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMCompanionItemDetail]
GO
CREATE TABLE [AIMCompanionItemDetail] (
	[MasterItem] [nvarchar] (25)  NOT NULL ,
	[Lcid] [nvarchar] (12) NOT NULL ,
	[Item] [nvarchar] (25)  NOT NULL ,
	[Qty] [int] NOT NULL CONSTRAINT [DF_CompanionItemDetail_Qty] DEFAULT (0),
	[DemandFactor] [decimal](3, 0) NOT NULL ,
	[Startdate] [datetime] NOT NULL ,
	[Enddate] [datetime] NOT NULL ,
	[ExcludeIndepDemY_N] [nvarchar] (1)  NOT NULL CONSTRAINT [DF_AIMCompanionItemDetail_ExcludeIndepDemY_N] DEFAULT (N'N'),
	CONSTRAINT [PK_CompanionItemDetail] PRIMARY KEY  CLUSTERED 
	(
		[MasterItem],
		[Lcid],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

