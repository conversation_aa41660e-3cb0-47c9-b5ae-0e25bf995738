/*******************************************************************************
 **	Table Name: ALTERNATE_SOURCE
 **	Desc: Contains Alternate Vendors for each Item/Loc
 **
 **              
 **	Author: Mohammed
 *******************************************************************************
 **	Change History
 *******************************************************************************
 **    Date:	   Author:	Description:
 **    ---------- ------------	-------------------------------------------------
 *******************************************************************************/


/****** Object:  Table [dbo].[ALTERNATE_SOURCE]  ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ALTERNATE_SOURCE]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
Drop Table [dbo].[ALTERNATE_SOURCE]
GO

/****** Object:  Table [dbo].[ALTERNATE_SOURCE] ******/
CREATE TABLE [dbo].[ALTERNATE_SOURCE] (
	[Enabled] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT ('1') NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT ('EA') NOT NULL ,
	[ConvFactor] [Int] DEFAULT 1 NOT NULL ,
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT ('R') NOT NULL ,
	[StdCost]   [decimal](10,4) DEFAULT 0 NOT NULL,
	[LeadTime] [SmallInt] DEFAULT 0 NOT NULL 

) ON [PRIMARY]
GO


ALTER TABLE [dbo].[ALTERNATE_SOURCE] WITH NOCHECK ADD 
	CONSTRAINT [PK_AlternateSource] PRIMARY KEY NONCLUSTERED 
	(	[VnId],[LcId],[Item]
	)  ON [PRIMARY] 
Go


ALTER TABLE [dbo].[ALTERNATE_SOURCE] ADD 
	CONSTRAINT [FK_ALTERNATESOURCE_Item] FOREIGN KEY 
	(       [LcId],
		[Item]
	) REFERENCES [dbo].[Item] (
		[Lcid],
		[Item]
	) ON DELETE CASCADE 
GO


ALTER TABLE [dbo].[ALTERNATE_SOURCE] ADD 
	CONSTRAINT [FK_ALTERNATESOURCE_Vendor] FOREIGN KEY 
	(       [VnId],
		[Assort]
	) REFERENCES [dbo].[AIMVendors] (
		[VnId],
		[Assort]
	) ON DELETE CASCADE 
GO
