/****** Object:  Table [dbo].[RevCycles]    Script Date: 05/30/2003 11:04:54 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RevCycles]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RevCycles]
GO

CREATE TABLE [dbo].[RevCycles] (
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RevCycleDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_RevCycle_RevCycleDesc] DEFAULT (''),
	[NextDateTime] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_NextDateTime] DEFAULT ('1/1/1990'),
	[RevStatus] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_RevStatus] DEFAULT (1),
	[DynamicFlag] [tinyint] NOT NULL CONSTRAINT [DF_RevCycles_DynamicFlag] DEFAULT (0),
	[RevFreq] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_RevFreq] DEFAULT (0),
	[RevInterval] [smallint] NOT NULL CONSTRAINT [DF_RevCycle_RevInterval] DEFAULT (1),
	[RevSunday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Sunday] DEFAULT (0),
	[RevMonday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Monday] DEFAULT (1),
	[RevTuesday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Tuesday] DEFAULT (1),
	[RevWednesday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Wednesday] DEFAULT (1),
	[RevThursday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Thursday] DEFAULT (1),
	[RevFriday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Friday] DEFAULT (1),
	[RevSaturday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Saturday] DEFAULT (1),
	[WeekQualifier] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_WeekQualifier] DEFAULT (0),
	[RevStartDate] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_StartDate] DEFAULT ('1/1/1990'),
	[RevEndDate] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_EndDate] DEFAULT ('1/1/1990'),
	[InitBuyPct] [decimal](5, 4) NOT NULL CONSTRAINT [DF_RevCycle_InitBuyPct] DEFAULT (1),
	[InitRevDate] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_InitRevDate] DEFAULT ('01/01/1990'),
	[SeasonalReview] [tinyint] NOT NULL CONSTRAINT [DF_RevCycles_SeasonalReview] DEFAULT (0),
	[ReviewTime] [smallint] NOT NULL CONSTRAINT [DF_RevCycles_ReviewTime] DEFAULT (1),
	[OrdGenStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_RevCycles_OrdGenStatus] DEFAULT (''),
	CONSTRAINT [PK_RevCycle] PRIMARY KEY  NONCLUSTERED 
	(
		[RevCycle]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


