/****** Object:  Table [dbo].[AllocDefaults]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AllocDefaults]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AllocDefaults]
GO

CREATE TABLE [dbo].[AllocDefaults] (
	[AllocDefaultsId] [numeric](18, 0) IDENTITY (1, 1) NOT NULL ,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LStatus] DEFAULT (N''),
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LDivision] DEFAULT (N''),
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LRegion] DEFAULT (N''),
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LUserDefined] DEFAULT (N''),
	[ExceptionPct] [decimal](18, 2) NULL ,
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_AllocDefaults_ReviewerId] DEFAULT (N'sa'),
	[LRank] [tinyint] NOT NULL ,
	CONSTRAINT [IX_AllocDefaults] UNIQUE  NONCLUSTERED 
	(
		[LStatus],
		[LDivision],
		[LRegion],
		[LUserDefined]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


