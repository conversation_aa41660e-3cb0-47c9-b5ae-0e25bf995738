/****** Object:  Table [dbo].[MDCDetail]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[MDCDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[MDCDetail]
GO

CREATE TABLE [dbo].[MDCDetail] (
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstDate] [datetime] NOT NULL ,
	[AvailQty] [int] NOT NULL ,
	[FcstDemand] [decimal](10, 2) NOT NULL ,
	[MAE] [decimal](10, 2) NOT NULL ,
	[Trend] [decimal](10, 3) NOT NULL ,
	[RqQty01] [int] NOT NULL ,
	[RqQty02] [int] NOT NULL ,
	[RqQty03] [int] NOT NULL ,
	[RqQty04] [int] NOT NULL ,
	[RqQty05] [int] NOT NULL ,
	[RqQty06] [int] NOT NULL ,
	[RqQty07] [int] NOT NULL ,
	[RqQty08] [int] NOT NULL ,
	[RqQty09] [int] NOT NULL ,
	[RqQty10] [int] NOT NULL ,
	[RqQty11] [int] NOT NULL ,
	[RqQty12] [int] NOT NULL ,
	[RqQty13] [int] NOT NULL ,
	[RqQty14] [int] NOT NULL ,
	[RqQty15] [int] NOT NULL ,
	[RqQty16] [int] NOT NULL ,
	[RqQty17] [int] NOT NULL ,
	[RqQty18] [int] NOT NULL ,
	[RqQty19] [int] NOT NULL ,
	[RqQty20] [int] NOT NULL ,
	[RqQty21] [int] NOT NULL ,
	[RqQty22] [int] NOT NULL ,
	[RqQty23] [int] NOT NULL ,
	[RqQty24] [int] NOT NULL ,
	[RqQty25] [int] NOT NULL ,
	[RqQty26] [int] NOT NULL ,
	CONSTRAINT [PK_MDCDetail] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Item]
	)  ON [PRIMARY] ,
	CONSTRAINT [IX_MDCItem] UNIQUE  NONCLUSTERED 
	(
		[MDC],
		[Item],
		[LcId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


