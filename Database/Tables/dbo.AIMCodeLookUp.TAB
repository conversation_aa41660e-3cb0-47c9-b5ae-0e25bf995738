/****** Object:  Table [dbo].[AIMCodeLookUp]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMCodeLookUp]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMCodeLookUp]
GO

CREATE TABLE [dbo].[AIMCodeLookUp] (
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CodeType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CodeID] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CodeDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT [PK_AIMCodeLookUp] PRIMARY KEY NONCLUSTERED 
	(
		[LangID],
		[CodeType],
		[CodeID]
	)  ON [PRIMARY] 

) ON [PRIMARY]
GO
