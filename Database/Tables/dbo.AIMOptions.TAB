/****** Object:  Table [dbo].[AIMOptions]    Script Date: 05/30/2003 11:04:35 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMOptions]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMOptions]
GO

CREATE TABLE [dbo].[AIMOptions] (
	[OptionId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OpDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_IDFL] DEFAULT (3),
	[BypIDFL] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypIDFL] DEFAULT ('N'),
	[CADL] [smallint] NOT NULL CONSTRAINT [DF_AIMOptions_CADL] DEFAULT (5),
	[HiDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_HiDFL] DEFAULT (5),
	[LoDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_LoDFL] DEFAULT (3),
	[NDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_NDFL] DEFAULT (3),
	[DDMAP] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_DDMAP] DEFAULT (6),
	[Damp] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_Damp] DEFAULT (0.7),
	[MinSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MinSmk] DEFAULT (0.1),
	[MaxSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MaxSmk] DEFAULT (0.5),
	[IntSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_IntSmk] DEFAULT (0.1),
	[TSL] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_TSL] DEFAULT (0.5),
	[TSSmK] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_TSSmK] DEFAULT (0.1),
	[BypDDU] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypDDU] DEFAULT ('N'),
	[ApplyDmdFilter] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_ApplyDmdFilter] DEFAULT ('Y'),
	[BypZSl] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypZSl] DEFAULT ('N'),
	[BypTFP] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypTFP] DEFAULT ('Y'),
	[BypZOH] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypZOH] DEFAULT ('N'),
	[BypBef] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_BypBef] DEFAULT (1),
	[BypAft] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_BypAft] DEFAULT (1),
	[MinBI] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MinBI] DEFAULT (0.3),
	[HiPct] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_HiPct] DEFAULT (0.5),
	[LoPct] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_LoPct] DEFAULT (0.2),
	[HiMADP] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_HiMADP] DEFAULT (9),
	[LoMADP] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_LoMADP] DEFAULT (0.1),
	[MADSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MADSmk] DEFAULT (0.1),
	[MADExk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MADExk] DEFAULT (0.5),
	[TrnSmk] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_TrnSmk] DEFAULT (0.08),
	[DIDDL] [smallint] NOT NULL CONSTRAINT [DF_AIMOptions_DIDDL] DEFAULT (3),
	[DITrnDL] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_DITrnDL] DEFAULT (0.3),
	[DITrps] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_DITrps] DEFAULT (6),
	[DIMADP] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_DIMADP] DEFAULT (0.3),
	[HiTrndL] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_HiTrndL] DEFAULT (1),
	[FilterSmk] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_FilterSmk] DEFAULT (0.1),
	[LumpyFilterPct] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMOptions_LumpyFilterPct] DEFAULT (0.15),
	[Dft_TurnHigh] [decimal](4, 1) NOT NULL CONSTRAINT [DF_AIMOptions_Dft_TurnHigh] DEFAULT (999.9),
	[Dft_TurnLow] [decimal](4, 1) NOT NULL CONSTRAINT [DF_AIMOptions_Dft_TurnLow] DEFAULT (0),
	[Dft_Dser] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMOptions_Dft_Dser] DEFAULT (0.95),
	CONSTRAINT [PK_AIMOptions] PRIMARY KEY  NONCLUSTERED 
	(
		[OptionId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


