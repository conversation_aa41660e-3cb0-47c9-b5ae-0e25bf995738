if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMERROR_INFO]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMERROR_INFO]
GO

CREATE TABLE [dbo].[AIMERROR_INFO] (
	[ComputerName] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserID] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ErrorNumber] [varchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ErrorSource] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ErrorDesc] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ErrorFromEvent] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DateAndTime] [datetime] NULL ,
	[LogType] [varchar] (4) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[MsgID]  uniqueidentifier ROWGUIDCOL  DEFAULT NEWID() PRIMARY KEY CLUSTERED   NOT NULL 
) ON [PRIMARY]
GO

