/****** Object:  Table [dbo].[AIMDxLT]    Script Date: 05/30/2003 11:04:25 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxLT]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxLT]
GO

CREATE TABLE [dbo].[AIMDxLT] (
	[SeqNbr] [int] NULL ,
	[PONbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LineNbr] [smallint] NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[QtyOrd] [int] NULL ,
	[QtyRec] [int] NULL ,
	[DateOrd] [datetime] NULL ,
	[DateRec] [datetime] NULL ,
	[DatePutAway] [datetime] NULL ,
	[Oh] [int] NULL 
) ON [PRIMARY]
GO


