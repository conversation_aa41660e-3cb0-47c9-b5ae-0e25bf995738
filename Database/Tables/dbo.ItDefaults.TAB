/****** Object:  Table [dbo].[ItDefaults]    Script Date: 05/30/2003 11:04:43 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItDefaults]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItDefaults]
GO

CREATE TABLE [dbo].[ItDefaults] (
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Class] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SaId] [nvarchar] (62) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_SaId] DEFAULT ('DEFAULT'),
	[VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_VelCode] DEFAULT ('C'),
	[BuyStrat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_BuyStrat] DEFAULT ('O'),
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_ById] DEFAULT (''),
	[ReplenCost2] [decimal](5, 2) NOT NULL CONSTRAINT [DF_ItDefaults_KFactor2] DEFAULT (0),
	[DSer] [decimal](3, 2) NOT NULL CONSTRAINT [DF_ItDefaults_DSer] DEFAULT (0.95),
	[OptionId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_OptionId] DEFAULT (''),
	CONSTRAINT [PK_ItDefaults] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Class]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


