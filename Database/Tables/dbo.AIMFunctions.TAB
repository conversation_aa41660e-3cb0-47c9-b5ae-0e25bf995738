/****** Object:  Table [dbo].[AIMFunctions]    Script Date: 05/30/2003 11:04:33 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMFunctions]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMFunctions]
GO

CREATE TABLE [dbo].[AIMFunctions] (
	[CtrlNbr] [int] NOT NULL ,
	[FuncDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMFunctions_FuncDesc] DEFAULT (''),
	[RWOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMFunctions_RWOption] DEFAULT ('N'),
	CONSTRAINT [PK_AIMFunctions] PRIMARY KEY  NONCLUSTERED 
	(
		[CtrlNbr]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


