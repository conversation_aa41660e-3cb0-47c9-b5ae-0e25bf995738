IF EXISTS ( SELECT * FROM dbo.sysobjects WHERE ID = object_id( N'[dbo].[AIMDxFA]' ) AND OBJECTPROPERTY( ID, N'IsUserTable') = 1 )
	DROP TABLE [dbo].[<PERSON><PERSON><PERSON><PERSON>]
GO
/*******************************************************************************
**                       			NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS CONFIDENTIAL INFORMATION OF 
**	SSA GLOBAL TECHNOLOGIES, INC., AND SHALL NOT BE COPIED, USED, NOR DISCLOSED 
**	WITHOUT EXPRESS WRITTEN AUTHORIZATION.  
**	ALTHOUGH PUBLICATION IS NOT INTENDED, IN THE EVENT OF PUBLICATION, 
**	THE FOLLOWING NOTICE IS APPLICABLE:
**  	(c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**	SSA GLOBAL TECHNOLOGIES, INC.
********************************************************************************
**	Name: dbo.AIMDxFA.tab
**	Desc: Create transit table for importing Forecast Adjustments.
**	Auth:   Annalakshmi Stocksdale
**	Date:   2003/05/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
**	2004/10/25	A.Stocksdale	Updates to match Srinivas' Forecast Planner
*******************************************************************************/
CREATE TABLE [dbo].[AIMDxFA] (
	[SeqNbr] [int] NULL,
	[LineNbr] [int] NULL,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AdjustType] [tinyint] NULL,
	[AdjustQty] [decimal](10, 2) NULL,
	[AdjustBegDate] [datetime] NULL,
	[AdjustEndDate] [datetime] NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[RecordDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO
