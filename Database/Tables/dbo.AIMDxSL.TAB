/****** Object:  Table [dbo].[AIMDxSL]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxSL]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxSL]
GO

CREATE TABLE [dbo].[AIMDxSL] (
	[SeqNbr] [int] NULL ,
	[Status] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[InActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UPC] [nvarchar] (22) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Oh] [int] NULL ,
	[Oo] [int] NULL ,
	[ComStk] [int] NULL ,
	[BkOrder] [int] NULL ,
	[BkComStk] [int] NULL ,
	[StkDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OnPromotion] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Freeze_Forecast] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[AllocatableQTY] [int] NULL 
) ON [PRIMARY]
GO


