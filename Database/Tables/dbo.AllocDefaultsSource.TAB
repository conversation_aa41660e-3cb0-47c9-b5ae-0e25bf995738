/****** Object:  Table [dbo].[AllocDefaultsSource]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AllocDefaultsSource]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AllocDefaultsSource]
GO

CREATE TABLE [dbo].[AllocDefaultsSource] (
	[AllocDefaultsId] [numeric](18, 0) NOT NULL ,
	[LcId_Source] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Src_Priority] [tinyint] NOT NULL CONSTRAINT [DF_AllocDefaultsSource_Src_Priority] DEFAULT (5),
	 PRIMARY KEY  CLUSTERED 
	(
		[AllocDefaultsId],
		[LcId_Source]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


