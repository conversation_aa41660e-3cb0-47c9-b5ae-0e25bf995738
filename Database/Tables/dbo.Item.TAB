/****** Object:  Table [dbo].[Item]    Script Date: 05/30/2003 11:04:44 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Item]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Item]
GO

CREATE TABLE [dbo].[Item] (
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItStat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ActDate] [datetime] NOT NULL CONSTRAINT [DF_Item_ActDate] DEFAULT ('1/1/1990'),
	[InActDate] [datetime] NOT NULL CONSTRAINT [DF_Item_InActDate] DEFAULT ('12/31/9999'),
	[OptionID] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OptionID] DEFAULT ('DEFLT'),
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class1] DEFAULT (''),
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class2] DEFAULT (''),
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class3] DEFAULT (''),
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class4] DEFAULT (''),
	[BinLocation] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_BinLocation] DEFAULT (''),
	[BuyStrat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_BuyStrat] DEFAULT ('O'),
	[VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VelCode] DEFAULT ('C'),
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VnId] DEFAULT (''),
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Assort] DEFAULT (''),
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_ById] DEFAULT (''),
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_MDC] DEFAULT (''),
	[MDCFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_MDCFlag] DEFAULT ('N'),
	[SaId] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_SaId] DEFAULT (''),
	[PmId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_PmId] DEFAULT (''),
	[UPC] [nvarchar] (22) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UPC] DEFAULT (''),
	[Weight] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Weight] DEFAULT (0),
	[Cube] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Cube] DEFAULT (0),
	[ListPrice] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_ListPrice] DEFAULT (0),
	[Price] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Price] DEFAULT (0),
	[Cost] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Cost] DEFAULT (0),
	[BkQty01] [int] NOT NULL CONSTRAINT [DF_Item_BkQty01] DEFAULT (0),
	[BkCost01] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost01] DEFAULT (0),
	[BkQty02] [int] NOT NULL CONSTRAINT [DF_Item_BkQty02] DEFAULT (0),
	[BkCost02] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost02] DEFAULT (0),
	[BkQty03] [int] NOT NULL CONSTRAINT [DF_Item_BkQty021] DEFAULT (0),
	[BkCost03] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost021] DEFAULT (0),
	[BkQty04] [int] NOT NULL CONSTRAINT [DF_Item_BkQty021_1] DEFAULT (0),
	[BkCost04] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost021_1] DEFAULT (0),
	[BkQty05] [int] NOT NULL CONSTRAINT [DF_Item_BkQty022] DEFAULT (0),
	[BkCost05] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost022] DEFAULT (0),
	[BkQty06] [int] NOT NULL CONSTRAINT [DF_Item_BkQty023] DEFAULT (0),
	[BkCost06] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost023] DEFAULT (0),
	[BkQty07] [int] NOT NULL CONSTRAINT [DF_Item_BkQty024] DEFAULT (0),
	[BkCost07] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost024] DEFAULT (0),
	[BkQty08] [int] NOT NULL CONSTRAINT [DF_Item_BkQty021_2] DEFAULT (0),
	[BkCost08] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost021_2] DEFAULT (0),
	[BkQty09] [int] NOT NULL CONSTRAINT [DF_Item_BkQty022_1] DEFAULT (0),
	[BkCost09] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost022_1] DEFAULT (0),
	[BkQty10] [int] NOT NULL CONSTRAINT [DF_Item_BkQty023_1] DEFAULT (0),
	[BkCost10] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost023_1] DEFAULT (0),
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UOM] DEFAULT ('EA'),
	[ConvFactor] [int] NOT NULL CONSTRAINT [DF_Item_ConvFactorPL3] DEFAULT (1),
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_BuyingUOM] DEFAULT ('EA'),
	[ReplenCost2] [decimal](9, 4) NOT NULL CONSTRAINT [DF_Item_KFactor2] DEFAULT (0),
	[Oh] [int] NOT NULL CONSTRAINT [DF_Item_Oh] DEFAULT (0),
	[Oo] [int] NOT NULL CONSTRAINT [DF_Item_Oo] DEFAULT (0),
	[ComStk] [int] NOT NULL CONSTRAINT [DF_Item_ComStk] DEFAULT (0),
	[BkOrder] [int] NOT NULL CONSTRAINT [DF_Item_BkOrder] DEFAULT (0),
	[BkComStk] [int] NOT NULL CONSTRAINT [DF_Item_BkComStk] DEFAULT (0),
	[LeadTime] [smallint] NOT NULL CONSTRAINT [DF_Item_LeadTime] DEFAULT (0),
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_PackRounding] DEFAULT ('R'),
	[IMin] [int] NOT NULL CONSTRAINT [DF_Item_IMin] DEFAULT (0),
	[IMax] [int] NOT NULL CONSTRAINT [DF_Item_IMax] DEFAULT (999999999),
	[CStock] [int] NOT NULL CONSTRAINT [DF_Item_CStock] DEFAULT (0),
	[SSAdj] [decimal](9, 4) NOT NULL CONSTRAINT [DF_Item_SSAdj] DEFAULT (0),
	[UserMin] [int] NOT NULL CONSTRAINT [DF_Item_UserMin] DEFAULT (0),
	[UserMax] [int] NOT NULL CONSTRAINT [DF_Item_UserMax] DEFAULT (999999999),
	[UserMethod] [tinyint] NOT NULL CONSTRAINT [DF_Item_UserMethod] DEFAULT (0),
	[FcstMethod] [tinyint] NOT NULL CONSTRAINT [DF_Item_FcstMethod] DEFAULT (0),
	[FcstDemand] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstDemand] DEFAULT (0),
	[UserFcst] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstUser] DEFAULT (0),
	[UserFcstExpDate] [datetime] NOT NULL CONSTRAINT [DF_Item_FcstUserExpDate] DEFAULT ('1/1/1990'),
	[MAE] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_MAPE] DEFAULT (0),
	[MSE] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_MSPE] DEFAULT (0),
	[Trend] [decimal](10, 3) NOT NULL CONSTRAINT [DF_Item_TrndPct] DEFAULT (0),
	[FcstCycles] [int] NOT NULL CONSTRAINT [DF_Item_FcstCycles] DEFAULT (0),
	[ZeroCount] [int] NOT NULL CONSTRAINT [DF_Item_ZeroCount] DEFAULT (0),
	[Mean_NZ] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Mean_NZ] DEFAULT (0),
	[StdDev_NZ] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_StdDev_NZ] DEFAULT (0),
	[IntSafetyStock] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_IntSafetyStock] DEFAULT (0),
	[IsIntermittent] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_IsIntermittent] DEFAULT ('N'),
	[DIFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_DIFlag] DEFAULT ('N'),
	[DmdFilterFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_DmdFilterFlag] DEFAULT ('N'),
	[TrkSignalFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_TrkSignalFlag] DEFAULT ('N'),
	[UserDemandFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserDemandFlag] DEFAULT ('N'),
	[ByPassPct] [decimal](4, 3) NOT NULL CONSTRAINT [DF_Item_ByPassPct] DEFAULT (0),
	[FcstUpdCyc] [int] NOT NULL CONSTRAINT [DF_Item_FctUpdCyc] DEFAULT (0),
	[LTVFact] [decimal](3, 2) NOT NULL CONSTRAINT [DF_Item_LTVFact] DEFAULT (0),
	[PlnTT] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_PlnTT] DEFAULT ('N'),
	[ZOPSw] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_ZOPSw] DEFAULT ('N'),
	[OUTLSw] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OUTLSw] DEFAULT ('N'),
	[ZSStock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_ZSStock] DEFAULT ('N'),
	[DSer] [decimal](4, 3) NOT NULL CONSTRAINT [DF_Item_DSer] DEFAULT (0.95),
	[Freeze_BuyStrat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_BuyStrat] DEFAULT ('N'),
	[Freeze_Byid] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_Byid] DEFAULT ('N'),
	[Freeze_LeadTime] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_LeadTime] DEFAULT ('N'),
	[Freeze_OptionID] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_OptionID] DEFAULT ('N'),
	[Freeze_DSer] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_DSer] DEFAULT ('N'),
	[OldItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OldItem] DEFAULT (''),
	[Accum_Lt] [smallint] NOT NULL CONSTRAINT [DF_Item_Accum_Lt] DEFAULT (0),
	[ReviewTime] [smallint] NOT NULL CONSTRAINT [DF_Item_ReviewTime] DEFAULT (0),
	[OrderPt] [int] NOT NULL CONSTRAINT [DF_Item_OrderPt] DEFAULT (0),
	[OrderQty] [int] NOT NULL CONSTRAINT [DF_Item_OrderQty] DEFAULT (0),
	[SafetyStock] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_SafetyStock] DEFAULT (0),
	[FcstRT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstRT] DEFAULT (0),
	[FcstLT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstLT] DEFAULT (0),
	[Fcst_Month] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Fcst_Month] DEFAULT (0),
	[Fcst_Quarter] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Fcst_Quarter] DEFAULT (0),
	[Fcst_Year] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Fcst_Year] DEFAULT (0),
	[FcstDate] [smalldatetime] NOT NULL CONSTRAINT [DF_Item_FcstDate] DEFAULT ('1/1/1990'),
	[VC_Amt] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VC_Amt] DEFAULT ('C'),
	[VC_Units_Ranking] [int] NOT NULL CONSTRAINT [DF_Item_VC_Units_Ranking] DEFAULT (0),
	[VC_Amt_Ranking] [int] NOT NULL CONSTRAINT [DF_Item_VC_Amt_Ranking] DEFAULT (0),
	[VC_Date] [smalldatetime] NOT NULL CONSTRAINT [DF_Item_VC_Date] DEFAULT ('1/1/1990'),
	[VelCode_Prev] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VelCode_Prev] DEFAULT (''),
	[VC_Amt_Prev] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VC_Amt_Prev] DEFAULT (''),
	[OnPromotion] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OnPromotion] DEFAULT ('N'),
	[AvgOh] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_AvgOh] DEFAULT (0),
	[NextPONbr_1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_NextPONbr_1] DEFAULT (''),
	[NextPODate_1] [datetime] NOT NULL CONSTRAINT [DF_Item_NextPODate_1] DEFAULT ('1/1/1990'),
	[NextPOQty_1] [int] NOT NULL CONSTRAINT [DF_Item_NextPOQty_1] DEFAULT (0),
	[NextPONbr_2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_NextPONbr_2] DEFAULT (''),
	[NextPODate_2] [datetime] NOT NULL CONSTRAINT [DF_Item_NextPODate_2] DEFAULT ('1/1/1990'),
	[NextPOQty_2] [int] NOT NULL CONSTRAINT [DF_Item_NextPOQty_2] DEFAULT (0),
	[NextPONbr_3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_NextPONbr_3] DEFAULT (''),
	[NextPODate_3] [datetime] NOT NULL CONSTRAINT [DF_Item_NextPODate_3] DEFAULT ('1/1/1990'),
	[NextPOQty_3] [int] NOT NULL CONSTRAINT [DF_Item_NextPOQty_3] DEFAULT (0),
	[UserRef1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserRef1] DEFAULT (''),
	[UserRef2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserRef2] DEFAULT (''),
	[UserRef3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserRef3] DEFAULT (''),
	[Freeze_Forecast] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_Forecast] DEFAULT (N'N'),
	[ProductionConstraint] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_Item_ProductionConstraint] DEFAULT (N'N'),
	[AllocatableQty] [int] NOT NULL CONSTRAINT [DF_Item_AllocatableQty] DEFAULT (0),
	[AltVnFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS Not Null DEFAULT (N'N'),
	[KitBOMFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS Not Null DEFAULT (N'N'),
    [DependentFcstDemand] [decimal](10,2) NOT NULL CONSTRAINT [DF_Item_DependentFcstDemand] DEFAULT (0),
	[AllocUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_AllocUOM] DEFAULT ('EA'),
	[AllocConvFactor] [int] NOT NULL CONSTRAINT [DF_Item_AllocConvFactor] DEFAULT (1),
	CONSTRAINT [PK_Item] PRIMARY KEY  NONCLUSTERED 
	(
		[Lcid],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_Byid] ON [dbo].[Item]([ById]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_Item] ON [dbo].[Item]([Item]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_ItStat] ON [dbo].[Item]([ItStat]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_VnidAssort] ON [dbo].[Item]([VnId], [Assort]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_MDC] ON [dbo].[Item]([MDC]) ON [PRIMARY]
GO

CREATE INDEX [IX_COVER_ITEM] ON [dbo].[Item]([Class1], [Class2], [Class3], [Class4], [VnId], [Assort], [ById], [SaId], [PmId]) ON [PRIMARY]
GO

