/****** Object:  Table [dbo].[BuyerStatusTest]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[BuyerStatusTest]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[BuyerStatusTest]
GO

CREATE TABLE [dbo].[BuyerStatusTest] (
	[KeyId] [int] IDENTITY (1, 1) NOT NULL ,
	[Buyer Name] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Total Lines] [int] NULL ,
	[Complete Lines] [int] NULL ,
	[Planned Lines] [int] NULL ,
	[Released Lines] [int] NULL ,
	[Total LT Excpts] [int] NULL ,
	[Complete LT Excpts] [int] NULL ,
	[Planned LT Excpts] [int] NULL ,
	[Released LT Excpts] [int] NULL ,
	[Total Line Reviews] [int] NULL ,
	[Complete Line Reviews] [int] NULL ,
	[Planned Line Reviews] [int] NULL ,
	[Released Line Reviews] [int] NULL ,
	[Total Priority Excepts] [int] NULL ,
	[Complete Priority Excepts] [int] NULL ,
	[Planned Priority Excepts] [int] NULL ,
	[Released Priority Excepts] [int] NULL ,
	CONSTRAINT [PK_BuyerStatusTest] PRIMARY KEY  NONCLUSTERED 
	(
		[KeyId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


