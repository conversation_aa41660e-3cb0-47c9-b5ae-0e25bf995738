/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   ItemHistoryAdjLog.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/08/05
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/

SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[ItemHistoryAdjLog]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[ItemHistoryAdjLog]
GO

-- location, item, history year, and forecast period (1 to 52)
-- previous value, new value, user, date/time, reason,
CREATE TABLE [dbo].[ItemHistoryAdjLog] (
	[AdjustLogKey] [bigint] IDENTITY,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[SubsItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[HisYear] [smallint] NOT NULL,
	[DemandPeriod] [tinyint] NOT NULL,
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[OldValue] [decimal] (9, 1) NOT NULL,
	[NewValue] [decimal] (9, 1) NOT NULL,
	[AdjustDateTime] [datetime] NOT NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Reason] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT [PK_ItemHistoryAdjLog] PRIMARY KEY  NONCLUSTERED 
	(
		[AdjustLogKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX IDX_AIMItemHistoryAdjLog_Cover ON ItemHistoryAdjLog (
	[LcID],
	[Item],
	[HisYear],
	[DemandSource],
	[DemandPeriod],
	[AdjustDateTime]
)
GO

