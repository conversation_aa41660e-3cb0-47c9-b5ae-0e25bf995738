/****** Object:  Table [dbo].[AIMYears]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMYears]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMYears]
GO

CREATE TABLE [dbo].[AIMYears] (
	[FiscalYear] [int] NOT NULL ,
	[FYStartDate] [datetime] NOT NULL ,
	[FYEndDate] [datetime] NOT NULL ,
	[NbrWeeks] [int] NOT NULL ,
	CONSTRAINT [PK_AimYears] PRIMARY KEY  CLUSTERED 
	(
		[FiscalYear]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


CREATE INDEX [IX_AIMYEARS_COVER] ON [dbo].[AIMYears]([FYStartDate], [FYEndDate]) ON [PRIMARY]
GO
