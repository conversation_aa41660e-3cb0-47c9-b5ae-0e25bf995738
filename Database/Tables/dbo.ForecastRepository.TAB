/****** Object:  Table [dbo].[ForecastRepository]    Script Date: 05/30/2003 11:04:42 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepository]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepository]
GO

CREATE TABLE [dbo].[ForecastRepository] (
	[RepositoryKey] [numeric](18, 0) IDENTITY (1, 1) NOT NULL ,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserElement] [bit] NOT NULL ,
	[FcstComment] [nvarchar] (4000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserIDCreate] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateTimeCreate] [datetime] NOT NULL ,
	[UserIDEdit] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateTimeEdit] [datetime] NOT NULL, 
	CONSTRAINT [PK_ForecastRepository] PRIMARY KEY  CLUSTERED 
	(
		[RepositoryKey]
	)  ON [PRIMARY] ,
	CONSTRAINT [IX_ForecastRepository] UNIQUE  NONCLUSTERED 
	(
		[FcstID],
		[UserElement]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


