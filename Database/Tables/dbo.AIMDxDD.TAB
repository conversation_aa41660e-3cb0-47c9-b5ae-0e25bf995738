/****** Object:  Table [dbo].[AIMDxDD]    Script Date: 05/30/2003 11:04:23 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxDD]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxDD]
GO

CREATE TABLE [dbo].[AIMDxDD] (
	[seqnbr] [int] NULL ,
	[ordnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[doctype] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[prmprog] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[linenbr] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[cusnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ordqty] [int] NULL ,
	[shpqty] [int] NULL ,
	[uom] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[extcost] [decimal](10, 2) NULL ,
	[extprice] [decimal](10, 2) NULL ,
	[reason_code] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[txndate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[timeofday] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[txn_type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[source] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[shipping] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[handling] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[olduom] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[uomfact] [decimal](11, 4) NULL ,
	[olditem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


