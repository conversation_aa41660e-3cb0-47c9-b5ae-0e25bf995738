/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstAccess.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/01/01
--   Updated By     - Anna<PERSON><PERSON><PERSON>sdale
--
--   This replaces the former AIM_DmdPlanFcstUserAccess and AIMFcstMainAccess tables
--		in allowing definitions of access permissions to the maintenance and modification screens.
--
--   The "forecast generator/modification" process is being morphed
--   into demand planning, hence the options required for forecast generator
--   are being phased out, and additional options for demand planning
--   are to be created, as of version 4.5
--   See related updates to *AIM_DmdPlanFcst*
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/
IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstAccess]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstAccess]
GO

CREATE TABLE [dbo].[AIMFcstAccess] (
	[FcstSetupKey] int NOT NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AccessCode] [tinyint] NOT NULL,

	CONSTRAINT [PK_AIMFcstAccess] PRIMARY KEY  CLUSTERED 
	(
		[FcstSetupKey],
		[UserID],
		[AccessCode]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


