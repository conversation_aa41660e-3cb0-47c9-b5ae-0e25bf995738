if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[KitBOMSummary]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[KitBOMSummary]
GO
CREATE TABLE [KitBOMSummary] (
	[LcId] [nvarchar] (12)  NOT NULL ,
	[Item] [nvarchar] (25)  NOT NULL ,
	[Accum_Lt] [smallint] NOT NULL ,
	[ReviewTime] [smallint] NOT NULL ,
	[OrderPt] [int] NOT NULL ,
	[OrderQty] [int] NOT NULL ,
	[SafetyStock] [decimal](10, 2) NOT NULL ,
	[FcstDemand] [decimal](10, 2) NOT NULL ,
	[DependentFcstDemand] [decimal](10, 2) NOT NULL ,
	[FcstRT] [decimal](10, 2) NOT NULL ,
	[FcstLT] [decimal](10, 2) NOT NULL ,
	[Fcst_Month] [decimal](10, 2) NOT NULL ,
	[Fcst_Quarter] [decimal](10, 2) NOT NULL ,
	[Fcst_Year] [decimal](10, 2) NOT NULL 
) ON [PRIMARY]
GO

