/****** Object:  Table [dbo].[AIMDays]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDays]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDays]
GO

CREATE TABLE [dbo].[AIMDays] (
	[FYDate] [datetime] NOT NULL ,
	[DayStatus] [tinyint] NOT NULL ,
	[FYPeriod_Days] [int] NULL,
	[FYPeriod_Weeks] [int] NULL,
	[FYPeriod_Months] [int] NULL,
	[FYPeriod_Quarters] [int] NULL,
	[FYPeriod_544] [int] NULL,
	[FYPeriod_454] [int] NULL,
	[FYPeriod_445] [int] NULL,
	[FYPeriod_4Weeks] [int] NULL,
	CONSTRAINT [PK_AIMDays] PRIMARY KEY CLUSTERED 
	(
		[FYDate]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX [IX_AIMDAYS_COVER] ON 
	[dbo].[AIMDays] (
		[FYPeriod_Days],
		[FYPeriod_Weeks],
		[FYPeriod_Months],
		[FYPeriod_Quarters],
		[FYPeriod_544],
		[FYPeriod_454],
		[FYPeriod_445],
		[FYPeriod_4Weeks]
	) ON [PRIMARY]
GO

