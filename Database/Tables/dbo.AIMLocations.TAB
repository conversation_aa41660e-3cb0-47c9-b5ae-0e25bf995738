/****** Object:  Table [dbo].[AIMLocations]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLocations]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLocations]
GO

CREATE TABLE [dbo].[AIMLocations] (
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LName] DEFAULT (''),
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMLocations_LType] DEFAULT (N'D'),
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMLocations_LStatus] DEFAULT (N'A'),
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LAddress1] DEFAULT (''),
	[LAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LAddress2] DEFAULT (''),
	[LAddress3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress4] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LCity] DEFAULT (''),
	[LState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LState] DEFAULT (''),
	[LZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LZip] DEFAULT (''),
	[LCountry] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LContact] DEFAULT (''),
	[LPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LPhone] DEFAULT (''),
	[LFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LFax] DEFAULT (''),
	[LEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LEMail] DEFAULT (''),
	[LRank] [smallint] NULL ,
	[PutAwayDays] [tinyint] NOT NULL CONSTRAINT [DF_Locations_PutAwayDays] DEFAULT (0),
	[ReplenCost] [decimal](11, 4) NOT NULL CONSTRAINT [DF_Locations_ReplenCost] DEFAULT (5),
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_DemandSource] DEFAULT ('S'),
	[LeadTimeSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LeadTimeSource] DEFAULT ('S'),
	[StkDate] [smalldatetime] NOT NULL CONSTRAINT [DF_Locations_StkDate] DEFAULT ('1/1/1990'),
	[Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_Dft_Byid] DEFAULT (''),
	[Last_FcstUpdCyc] [int] NOT NULL CONSTRAINT [DF_Locations_Last_FcstUpdCyc] DEFAULT (0),
	[LookBackPds] [smallint] NOT NULL CONSTRAINT [DF_Locations_LookBackPds] DEFAULT (52),
	[DmdScalingFactor] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMLocations_DmdScalingFactor] DEFAULT (1),
	[ScalingEffUntil] [datetime] NOT NULL CONSTRAINT [DF_AIMLocations_ScalingEffUntil] DEFAULT ('1/1/1990'),
	[Freeze_Period] [int] NOT NULL CONSTRAINT [DF_AIMLocations_Freeze_Period] DEFAULT (0),
	[UpdateCurrentYearOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMLocations_UpdateCurrentYearOption] DEFAULT (N'N'),
	[DropShip_XDock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_AIMLocations_DropShip_XDock] DEFAULT (N'N'),
	[Dft_ReviewerID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_AIMLocations_Dft_ReviewerID] DEFAULT (N'sa'),
	[ExceptionPct] [tinyint] NULL
	CONSTRAINT [PK_Locations] PRIMARY KEY  CLUSTERED 
	(
		[Lcid]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


