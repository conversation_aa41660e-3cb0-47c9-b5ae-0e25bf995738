/****** Object:  Table [dbo].[AIMClasses]    Script Date: 05/30/2003 11:04:21 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMClasses]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMClasses]
GO

CREATE TABLE [dbo].[AIMClasses] (
	[Class] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ClassDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ClassLevel] [tinyint] NOT NULL ,
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMClasses] PRIMARY KEY  CLUSTERED 
	(
		[Class],
		[ClassLevel],
		[LangID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


