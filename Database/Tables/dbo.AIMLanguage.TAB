/****** Object:  Table [dbo].[AIMLanguage]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLanguage]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLanguage]
GO

CREATE TABLE [dbo].[AIMLanguage] (
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LangDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HexValue] [nvarchar] (15) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DecimalValue] [decimal](18, 2) NULL ,
	[Enabled] [nchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO


