/****** Object:  Table [dbo].[ProdConstraintTemp]    Script Date: 05/30/2003 11:04:54 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ProdConstraintTemp]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ProdConstraintTemp]
GO

CREATE TABLE [dbo].[ProdConstraintTemp] (
	[UniqueJobId] [uniqueidentifier] NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Qty] [int] NOT NULL CONSTRAINT [DF_Prodqty_Qty] DEFAULT (0),
	[AdjQty] [int] NOT NULL CONSTRAINT [DF_Prodqty_AdjQty] DEFAULT (0),
	[DateCreated] [datetime] NOT NULL 
) ON [PRIMARY]
GO


