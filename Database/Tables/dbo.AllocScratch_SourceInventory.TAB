	/* Source Inventory -- this will hold information on DC On-Hand (so we don't mess up the real one) */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_SourceInventory]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_SourceInventory]
	GO	
	CREATE TABLE AllocScratch_SourceInventory (
		SessionID BIGINT NOT NULL,
		SourceLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		SourceQty INT NOT NULL,
		AllocUOM nvarchar (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		AllocConvFactor int,

	);
	GO
	CREATE CLUSTERED INDEX ASSI_SESS ON AllocScratch_SourceInventory (SessionID)
	GO

