/****** Object:  Table [dbo].[AIMDxRO]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxRO]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxRO]
GO

CREATE TABLE [dbo].[AIMDxRO] (
	[SeqNbr] [int] NULL,
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[OrdType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LineNbr] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[OrdQty] [int] NULL,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[TxnDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[TxnTimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Txn_Type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[OriginCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


