/****** Object:  Table [dbo].[AIMVendors]    Script Date: 05/30/2003 11:04:40 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMVendors]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMVendors]
GO

CREATE TABLE [dbo].[AIMVendors] (
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[MDCFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VAddress1] DEFAULT (''),
	[VAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VAddress2] DEFAULT (''),
	[VCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VCity] DEFAULT (''),
	[VState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VState] DEFAULT (''),
	[VZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VZip] DEFAULT (''),
	[VContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VContact] DEFAULT (''),
	[VPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VPhone] DEFAULT (''),
	[VFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VFax] DEFAULT (''),
	[VEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VEMail] DEFAULT (''),
	[VDocEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VDocEMail] DEFAULT (''),
	[Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_Dft_Byid] DEFAULT (''),
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_RevCycle] DEFAULT (''),
	[Dft_LeadTime] [smallint] NOT NULL ,
	[Last_Dft_LeadTime] [smallint] NOT NULL CONSTRAINT [DF_AIMVendors_Last_Dft_LeadTime] DEFAULT (0),
	[Vn_Min] [decimal](9, 2) NOT NULL ,
	[Vn_Best] [decimal](9, 2) NOT NULL ,
	[Reach_Code] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POByZone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnComments1] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VnComments1] DEFAULT (''),
	[VnComments2] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VnComments2] DEFAULT (''),
	[LastWeekSalesFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_LastWeekSalesFlag] DEFAULT ('N'),
	[TransmitPO] [tinyint] NOT NULL CONSTRAINT [DF_AIMVendors_TransmitPO] DEFAULT (0),
	[ShipIns] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_ShipIns] DEFAULT (''),
	CONSTRAINT [PK_AIMVendors] PRIMARY KEY  NONCLUSTERED 
	(
		[VnId],
		[Assort]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_Dft_RevCycle] ON [dbo].[AIMVendors]([RevCycle]) ON [PRIMARY]
GO


