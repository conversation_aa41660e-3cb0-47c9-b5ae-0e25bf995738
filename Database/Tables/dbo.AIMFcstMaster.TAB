
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMFcstMaster]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMFcstMaster]
GO

CREATE TABLE [dbo].[AIMFcstMaster] (
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[PeriodBegDate] [datetime] NOT NULL ,
	[PeriodEndDate] [datetime] NOT NULL ,
	[QtyAdj] [int] NOT NULL ,
	[QtyAdjOverRide] [int] NULL ,
	[QtyAdjPct] [int] NOT NULL ,
	[AdjOverRide] [bit] NOT NULL ,
	[DateTimeEdit] [datetime] NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[AIMFcstMaster] WITH NOCHECK ADD 
	CONSTRAINT [PK_AIMFcstMaster] PRIMARY KEY  CLUSTERED 
	(
		[LcID],
		[Item],
		[PeriodBegDate]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[AIMFcstMaster] ADD 
	CONSTRAINT [DF_AIMFcstMaster_AdjustQty] DEFAULT (0) FOR [QtyAdjPct],
	CONSTRAINT [DF_AIMFcstMaster_AdjOverRide] DEFAULT (0) FOR [AdjOverRide],
	CONSTRAINT [IX_AIMFcstMaster] UNIQUE  NONCLUSTERED 
	(
		[LcID],
		[Item],
		[PeriodBegDate]
	)  ON [PRIMARY] 
GO




