/****** Object:  Table [dbo].[AIMLeadTime]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLeadTime]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLeadTime]
GO

CREATE TABLE [dbo].[AIMLeadTime] (
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LeadTime] [smallint] NOT NULL ,
	[Prev_LeadTime1] [smallint] NOT NULL ,
	[Prev_LeadTime2] [smallint] NOT NULL ,
	[Prev_LeadTime3] [smallint] NOT NULL ,
	[LT_Count] [int] NOT NULL ,
	[LT_MAE] [decimal](10, 2) NOT NULL ,
	[SS_Erosion_Pct] [decimal](9, 4) NOT NULL ,
	CONSTRAINT [PK_AIMLeadTimes] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_VnId] ON [dbo].[AIMLeadTime]([VnId]) ON [PRIMARY]
GO


