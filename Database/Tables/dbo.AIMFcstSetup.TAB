/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstSetup.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/01/01
--   Updated By     - Anna<PERSON>sh<PERSON>sdale
--
--   This replaces the former AIM_DmdPlanFcst maintenance table in allowing definitions of forecast criteria.
--
--	NOTE: FcstSetupKey is the PRIMARY KEY for this table, and FcstID has a UNIQUE constraint.
--
--   The "forecast generator/modification" process is being morphed
--   into demand planning, hence the options required for forecast generator
--   are being phased out, and additional options for demand planning
--   are to be created, as of version 4.5
--   See related updates to *AIM_DmdPlanFcst*
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/
IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstSetup]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstSetup]
GO

CREATE TABLE [dbo].[AIMFcstSetup] (
	[FcstSetupKey] int IDENTITY,
	[FcstId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL UNIQUE ,
	[FcstDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstDesc] DEFAULT (''),
	[FcstHierarchy] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstHierarchy] DEFAULT (1),
	[FcstEnabled] [bit],
	[FcstLocked] [bit],
	[FcstStartDate] [datetime] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstStartDate] DEFAULT (0),
	[LastUpdated] [datetime] NOT NULL CONSTRAINT [DF_AIMFcstSetup_LastUpdated] DEFAULT (0),
	[FcstRolling] [bit],
	[FcstHistory] [bit],
	[FcstPds_Future] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstPds_Future] DEFAULT (6),
	[FcstPds_Historical] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstPds_Historical] DEFAULT (6),
	[FixedPds] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FixedPds] DEFAULT (0),
	[FreezePds] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FreezePds] DEFAULT (0),
	[FcstInterval] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstInterval] DEFAULT (2),
	[FcstUnit] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstUnit] DEFAULT (0),
	[ApplyTrend] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_ApplyTrend] DEFAULT (0),
	[Calc_SysFcst] [bit] NULL ,
	[Calc_NetReq] [bit] NULL ,
	[Calc_HistDmd] [bit] NULL ,
	[Calc_MasterFcstAdj] [bit] NULL ,
	[Calc_FcstAdj] [bit] NULL ,
	[Calc_AdjNetReq] [bit] NULL ,
	[Calc_ProjInv] [bit] NULL ,
	[Calc_ProdConst] [bit] NULL ,
-- Item filter criteria
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ItStat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ByID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
	CONSTRAINT [PK_AIMFcstSetup] PRIMARY KEY  CLUSTERED 
	(
		[FcstSetupKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX IDX_AIMFcstSetup_Cover ON AIMFcstSetup (FcstSetupKey, FcstID, FcstStartDate)
GO


