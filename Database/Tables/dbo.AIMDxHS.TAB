/****** Object:  Table [dbo].[AIMDxHS]    Script Date: 05/30/2003 11:04:23 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxHS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxHS]
GO

CREATE TABLE [dbo].[AIMDxHS] (
	[SeqNbr] [int] NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[SubsItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HisYear] [smallint] NULL ,
	[CPS01] [decimal](9, 1) NULL ,
	[CPS02] [decimal](9, 1) NULL ,
	[CPS03] [decimal](9, 1) NULL ,
	[CPS04] [decimal](9, 1) NULL ,
	[CPS05] [decimal](9, 1) NULL ,
	[CPS06] [decimal](9, 1) NULL ,
	[CPS07] [decimal](9, 1) NULL ,
	[CPS08] [decimal](9, 1) NULL ,
	[CPS09] [decimal](9, 1) NULL ,
	[CPS10] [decimal](9, 1) NULL ,
	[CPS11] [decimal](9, 1) NULL ,
	[CPS12] [decimal](9, 1) NULL ,
	[CPS13] [decimal](9, 1) NULL ,
	[CPS14] [decimal](9, 1) NULL ,
	[CPS15] [decimal](9, 1) NULL ,
	[CPS16] [decimal](9, 1) NULL ,
	[CPS17] [decimal](9, 1) NULL ,
	[CPS18] [decimal](9, 1) NULL ,
	[CPS19] [decimal](9, 1) NULL ,
	[CPS20] [decimal](9, 1) NULL ,
	[CPS21] [decimal](9, 1) NULL ,
	[CPS22] [decimal](9, 1) NULL ,
	[CPS23] [decimal](9, 1) NULL ,
	[CPS24] [decimal](9, 1) NULL ,
	[CPS25] [decimal](9, 1) NULL ,
	[CPS26] [decimal](9, 1) NULL ,
	[CPS27] [decimal](9, 1) NULL ,
	[CPS28] [decimal](9, 1) NULL ,
	[CPS29] [decimal](9, 1) NULL ,
	[CPS30] [decimal](9, 1) NULL ,
	[CPS31] [decimal](9, 1) NULL ,
	[CPS32] [decimal](9, 1) NULL ,
	[CPS33] [decimal](9, 1) NULL ,
	[CPS34] [decimal](9, 1) NULL ,
	[CPS35] [decimal](9, 1) NULL ,
	[CPS36] [decimal](9, 1) NULL ,
	[CPS37] [decimal](9, 1) NULL ,
	[CPS38] [decimal](9, 1) NULL ,
	[CPS39] [decimal](9, 1) NULL ,
	[CPS40] [decimal](9, 1) NULL ,
	[CPS41] [decimal](9, 1) NULL ,
	[CPS42] [decimal](9, 1) NULL ,
	[CPS43] [decimal](9, 1) NULL ,
	[CPS44] [decimal](9, 1) NULL ,
	[CPS45] [decimal](9, 1) NULL ,
	[CPS46] [decimal](9, 1) NULL ,
	[CPS47] [decimal](9, 1) NULL ,
	[CPS48] [decimal](9, 1) NULL ,
	[CPS49] [decimal](9, 1) NULL ,
	[CPS50] [decimal](9, 1) NULL ,
	[CPS51] [decimal](9, 1) NULL ,
	[CPS52] [decimal](9, 1) NULL ,
	[QtyOrd01] [decimal](9, 1) NULL ,
	[QtyOrd02] [decimal](9, 1) NULL ,
	[QtyOrd03] [decimal](9, 1) NULL ,
	[QtyOrd04] [decimal](9, 1) NULL ,
	[QtyOrd05] [decimal](9, 1) NULL ,
	[QtyOrd06] [decimal](9, 1) NULL ,
	[QtyOrd07] [decimal](9, 1) NULL ,
	[QtyOrd08] [decimal](9, 1) NULL ,
	[QtyOrd09] [decimal](9, 1) NULL ,
	[QtyOrd10] [decimal](9, 1) NULL ,
	[QtyOrd11] [decimal](9, 1) NULL ,
	[QtyOrd12] [decimal](9, 1) NULL ,
	[QtyOrd13] [decimal](9, 1) NULL ,
	[QtyOrd14] [decimal](9, 1) NULL ,
	[QtyOrd15] [decimal](9, 1) NULL ,
	[QtyOrd16] [decimal](9, 1) NULL ,
	[QtyOrd17] [decimal](9, 1) NULL ,
	[QtyOrd18] [decimal](9, 1) NULL ,
	[QtyOrd19] [decimal](9, 1) NULL ,
	[QtyOrd20] [decimal](9, 1) NULL ,
	[QtyOrd21] [decimal](9, 1) NULL ,
	[QtyOrd22] [decimal](9, 1) NULL ,
	[QtyOrd23] [decimal](9, 1) NULL ,
	[QtyOrd24] [decimal](9, 1) NULL ,
	[QtyOrd25] [decimal](9, 1) NULL ,
	[QtyOrd26] [decimal](9, 1) NULL ,
	[QtyOrd27] [decimal](9, 1) NULL ,
	[QtyOrd28] [decimal](9, 1) NULL ,
	[QtyOrd29] [decimal](9, 1) NULL ,
	[QtyOrd30] [decimal](9, 1) NULL ,
	[QtyOrd31] [decimal](9, 1) NULL ,
	[QtyOrd32] [decimal](9, 1) NULL ,
	[QtyOrd33] [decimal](9, 1) NULL ,
	[QtyOrd34] [decimal](9, 1) NULL ,
	[QtyOrd35] [decimal](9, 1) NULL ,
	[QtyOrd36] [decimal](9, 1) NULL ,
	[QtyOrd37] [decimal](9, 1) NULL ,
	[QtyOrd38] [decimal](9, 1) NULL ,
	[QtyOrd39] [decimal](9, 1) NULL ,
	[QtyOrd40] [decimal](9, 1) NULL ,
	[QtyOrd41] [decimal](9, 1) NULL ,
	[QtyOrd42] [decimal](9, 1) NULL ,
	[QtyOrd43] [decimal](9, 1) NULL ,
	[QtyOrd44] [decimal](9, 1) NULL ,
	[QtyOrd45] [decimal](9, 1) NULL ,
	[QtyOrd46] [decimal](9, 1) NULL ,
	[QtyOrd47] [decimal](9, 1) NULL ,
	[QtyOrd48] [decimal](9, 1) NULL ,
	[QtyOrd49] [decimal](9, 1) NULL ,
	[QtyOrd50] [decimal](9, 1) NULL ,
	[QtyOrd51] [decimal](9, 1) NULL ,
	[QtyOrd52] [decimal](9, 1) NULL ,
	[OrdCnt01] [int] NULL ,
	[OrdCnt02] [int] NULL ,
	[OrdCnt03] [int] NULL ,
	[OrdCnt04] [int] NULL ,
	[OrdCnt05] [int] NULL ,
	[OrdCnt06] [int] NULL ,
	[OrdCnt07] [int] NULL ,
	[OrdCnt08] [int] NULL ,
	[OrdCnt09] [int] NULL ,
	[OrdCnt10] [int] NULL ,
	[OrdCnt11] [int] NULL ,
	[OrdCnt12] [int] NULL ,
	[OrdCnt13] [int] NULL ,
	[OrdCnt14] [int] NULL ,
	[OrdCnt15] [int] NULL ,
	[OrdCnt16] [int] NULL ,
	[OrdCnt17] [int] NULL ,
	[OrdCnt18] [int] NULL ,
	[OrdCnt19] [int] NULL ,
	[OrdCnt20] [int] NULL ,
	[OrdCnt21] [int] NULL ,
	[OrdCnt22] [int] NULL ,
	[OrdCnt23] [int] NULL ,
	[OrdCnt24] [int] NULL ,
	[OrdCnt25] [int] NULL ,
	[OrdCnt26] [int] NULL ,
	[OrdCnt27] [int] NULL ,
	[OrdCnt28] [int] NULL ,
	[OrdCnt29] [int] NULL ,
	[OrdCnt30] [int] NULL ,
	[OrdCnt31] [int] NULL ,
	[OrdCnt32] [int] NULL ,
	[OrdCnt33] [int] NULL ,
	[OrdCnt34] [int] NULL ,
	[OrdCnt35] [int] NULL ,
	[OrdCnt36] [int] NULL ,
	[OrdCnt37] [int] NULL ,
	[OrdCnt38] [int] NULL ,
	[OrdCnt39] [int] NULL ,
	[OrdCnt40] [int] NULL ,
	[OrdCnt41] [int] NULL ,
	[OrdCnt42] [int] NULL ,
	[OrdCnt43] [int] NULL ,
	[OrdCnt44] [int] NULL ,
	[OrdCnt45] [int] NULL ,
	[OrdCnt46] [int] NULL ,
	[OrdCnt47] [int] NULL ,
	[OrdCnt48] [int] NULL ,
	[OrdCnt49] [int] NULL ,
	[OrdCnt50] [int] NULL ,
	[OrdCnt51] [int] NULL ,
	[OrdCnt52] [int] NULL 
) ON [PRIMARY]
GO


