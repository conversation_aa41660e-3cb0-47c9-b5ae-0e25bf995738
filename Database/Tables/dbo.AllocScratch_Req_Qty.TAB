/*******************************************************************************
--	Name: AllocationScratchTables	
--	Desc: Creates work-tables for use by the Allocation Stored procedure. 
--		The Allocation Scratch tables are:
-- 		AllocScratch_Req_Qty:  
--			Requestor Quantity -- this will hold information on aggregate ordered quantity by store and item 
-- 		AllocScratch_Req_Pri:  
--			Requestor Priority -- this will hold information on priorities for stores with orders 
--		AllocScratch_Req_Ratio:
--			Need ratio -- this will hold information on need ratios by store and item 
-- 		AllocScratch_SourceInventory:  
--			Source Inventory -- this will hold information on DC On-Hand (so we don't mess up the real one) 
--		AllocScratch_ItemDemand:
-- 			Item demand summary table -- this will hold summarised demand data for an item for all destinations in a priority group.
-- 		AllocScratch_AllocResults:  
--			Output table for  the allocation result record 
--
--	Author:		<PERSON><PERSON><PERSON><PERSON>
--	Created:	2003/05/21
-------------------------------------------------------------------------------
--	Change History
-------------------------------------------------------------------------------
--	Date:		Updated by:		Description:
--	----------	------------	-----------------------------------------------
--								
*******************************************************************************/

	/* Requestor Quantity -- this will hold information on aggregate ordered quantity by store and item */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_Req_Qty]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_Req_Qty]
	GO
	CREATE TABLE AllocScratch_Req_Qty (
		SessionID BIGINT NOT NULL,
		OrdNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		LineNbr nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		RequestedQty INT NOT NULL
	);
	CREATE CLUSTERED INDEX ASRQ_SESS ON AllocScratch_Req_Qty (SessionID)
	CREATE INDEX ASRQ_ITEM ON AllocScratch_Req_Qty (ItemID)
	CREATE INDEX ASRQ_STORE ON AllocScratch_Req_Qty (DestinationLoc)
	GO
	
