/****** Object:  Table [dbo].[AIMSd]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMSd]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMSd]
GO

CREATE TABLE [dbo].[AIMSd] (
	[seqnbr] [int] NULL ,
	[ordnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[doctype] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[prmprog] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[linenbr] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[cusnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[shipto] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Subsitem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,	
	[ordqty] [int] NULL ,
	[shpqty] [int] NULL ,
	[uom] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[extcost] [decimal](10, 2) NULL ,
	[extprice] [decimal](10, 2) NULL ,
	[reason_code] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[txndate] [smalldatetime] NULL ,
	[txn_type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[source] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[shipping] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[handling] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[wk_start] [smalldatetime] NULL 
) ON [PRIMARY]
GO


