/****** Object:  Table [dbo].[ForecastRepositoryArch_Log]    Script Date: 10/12/2004 10:54:10 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepositoryArch_Log]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepositoryArch_Log]
GO

CREATE TABLE [dbo].[ForecastRepositoryArch_Log] (
	[FcstAdjustKey] [int] IDENTITY (1, 1) NOT NULL ,
	[RepositoryKey] [numeric](12, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IsPromoted] [bit] NOT NULL CONSTRAINT [DF_AIMFcstStoreAdjustLogArch_IsPromoted] DEFAULT (0),
	[AdjustType] [tinyint] NULL ,
	[AdjustQty] [decimal](10, 2) NULL ,
	[OverRideEnabled] [smallint] NOT NULL CONSTRAINT [DF_ForecastRepository_LogArch_OverRideEanbled] DEFAULT (0),
	[AdjustBegDate] [datetime] NULL ,
	[AdjustEndDate] [datetime] NULL ,
	[AdjustUserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDateTime] [datetime] NULL ,
	CONSTRAINT [PK_AIMFcstAdjustLogArch] PRIMARY KEY  NONCLUSTERED 
	(
		[FcstAdjustKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


