/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   ForecastFilterCriteria.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/09/01
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/

SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[ForecastFilterCriteria]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[ForecastFilterCriteria]
GO

-- location, item, history year, and forecast period (1 to 52)
-- previous value, new value, user, date/time, reason,
CREATE TABLE [dbo].[ForecastFilterCriteria] (
	[FcstSetupKey] int NOT NULL,
	[FilterColumn] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[SearchCondition] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[FilterIndex] int NOT NULL,
	[FilterValue] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT [PK_ForecastFilterCriteria] PRIMARY KEY NONCLUSTERED 
	(
		[FcstSetupKey],
		[FilterColumn],
		[FilterIndex]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO
