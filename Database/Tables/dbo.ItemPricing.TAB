/****** Object:  Table [dbo].[ItemPricing]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItemPricing]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItemPricing]
GO

CREATE TABLE [dbo].[ItemPricing] (
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[EffectiveDatetime] [datetime] NOT NULL ,
	[Cost] [decimal](10, 4) NULL ,
	[Price] [decimal](10, 4) NULL ,
	[ListPrice] [decimal](10, 4) NULL ,
	CONSTRAINT [PK_ItemPricing] PRIMARY KEY  CLUSTERED 
	(
		[LcID],
		[Item],
		[EffectiveDatetime]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


