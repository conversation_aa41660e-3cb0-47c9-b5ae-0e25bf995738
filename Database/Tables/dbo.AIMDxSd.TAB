/****** Object:  Table [dbo].[AIMDxSd]    Script Date: 05/30/2003 11:04:27 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxSd]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxSd]
GO

CREATE TABLE [dbo].[AIMDxSd] (
	[SeqNbr] [int] NULL ,
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DocType] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[PrmProg] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LineNbr] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CusNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[SubsItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OrdQty] [int] NULL ,
	[ShpQty] [int] NULL ,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ExtCost] [decimal](10, 2) NULL ,
	[ExtPrice] [decimal](10, 2) NULL ,
	[Reason_Code] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TxnDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Txn_Type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Source] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Shipping] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Handling] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OldUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UOMFact] [decimal](11, 4) NULL ,
	[OldItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


