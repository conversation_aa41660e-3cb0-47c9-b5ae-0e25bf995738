/****** Object:  Table [dbo].[PODetail]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[PODetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[PODetail]
GO

CREATE TABLE [dbo].[PODetail] (
	[POLineType] [smallint] NOT NULL ,
	[POSeqID] [int] IDENTITY (1, 1) NOT NULL ,
	[OrdStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_OrdStatus] DEFAULT ('P'),
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_ItDesc] DEFAULT (''),
	[POType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AvailQty] [int] NOT NULL ,
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RSOQ] [int] NOT NULL ,
	[SOQ] [int] NOT NULL ,
	[VSOQ] [int] NOT NULL ,
	[Original_VSOQ] [int] NOT NULL CONSTRAINT [DF_PODetail_Original_VSOQ] DEFAULT (0),
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ConvFactor] [int] NOT NULL ,
	[IsDate] [datetime] NOT NULL ,
	[DuDate] [datetime] NOT NULL ,
	[LastWeekSalesFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_LastWeekSalesFlag] DEFAULT ('N'),
	[Cost] [numeric](10, 4) NOT NULL CONSTRAINT [DF_PODetail_Cost] DEFAULT (0),
	[Zone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_Zone] DEFAULT (''),
	[VendorCostExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_VendorCostExcpts] DEFAULT ('N'),
	CONSTRAINT [PK_PODetail] PRIMARY KEY  NONCLUSTERED 
	(
		[POSeqID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_ById] ON [dbo].[PODetail]([ById]) ON [PRIMARY]
GO

 CREATE  UNIQUE  INDEX [IX_VnIdAssort] ON [dbo].[PODetail]([ById], [VnId], [Assort], [Item], [Lcid]) ON [PRIMARY]
GO

CREATE INDEX [IDX_PODet_ItemLcId] ON [dbo].[PODetail]([LcId],[Item]) ON [PRIMARY]
GO
