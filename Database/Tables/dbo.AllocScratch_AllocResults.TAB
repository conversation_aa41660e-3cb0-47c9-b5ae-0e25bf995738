	/* Output table for  the allocation result record */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_AllocResults]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_AllocResults]
	GO
	CREATE TABLE AllocScratch_AllocResults (
		SessionID BIGINT NOT NULL,
		OrdNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		LineNbr nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		SourceLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		AllocatedQty INT NOT NULL
	);
	GO
	CREATE CLUSTERED INDEX ASAR_SESS ON AllocScratch_AllocResults (SessionID)
	GO

