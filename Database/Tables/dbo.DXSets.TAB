/****** Object:  Table [dbo].[DXSets]    Script Date: 05/30/2003 11:04:42 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DXSets]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[DXSets]
GO

CREATE TABLE [dbo].[DXSets] (
	[TxnSet] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Description] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FileFormat] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_DXSets] PRIMARY KEY  CLUSTERED 
	(
		[TxnSet]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


