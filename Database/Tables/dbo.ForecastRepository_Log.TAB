if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepository_Log]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepository_Log]
GO

CREATE TABLE [dbo].[ForecastRepository_Log] (
	[FcstAdjustKey] [int] IDENTITY (1, 1) NOT NULL ,
	[RepositoryKey] [numeric](12, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IsPromoted] [bit] NOT NULL ,
	[AdjustType] [tinyint] NULL ,
	[AdjustQty] [decimal](10, 2) NULL ,
	[OverRideEnabled] [smallint] NOT NULL ,
	[AdjustBegDate] [datetime] NULL ,
	[AdjustEndDate] [datetime] NULL ,
	[AdjustUserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDateTime] [datetime] NULL,
	CONSTRAINT [PK_ForecastRepository_Log] PRIMARY KEY  CLUSTERED 
	(
		[FcstAdjustKey]
	)  ON [PRIMARY] 

) ON [PRIMARY]
GO

