/*******************************************************************************
 **	Table Name: AIMDxAS
 **	Desc: to be used in the Alternate Source Interface
 **
 **              
 **	Author: Mohammed
 *******************************************************************************
 **	Change History
 *******************************************************************************
 **    Date:	   Author:	Description:
 **    ---------- ------------	-------------------------------------------------
 *******************************************************************************/


/****** Object:  Table [dbo].[AIMDxAS]  ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxAS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
Drop Table [dbo].[AIMDxAS]
GO

/****** Object:  Table [dbo].[AIMDxAS] ******/
CREATE TABLE [dbo].[AIMDxAS] (
	[SeqNr]   [Int]  NULL,  
	[Enabled] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item]    [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcId]    [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnId]    [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort]  [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[BuyingUOM]  [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ConvFactor] [Int] NULL ,
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[StdCost]  [decimal](10,4) NULL,
	[LeadTime] [SmallInt] NULL 
) ON [PRIMARY]
GO


