/****** Object:  Table [dbo].[AIMAODetail]    Script Date: 05/30/2003 11:04:21 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMAODetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMAODetail]
GO

CREATE TABLE [dbo].[AIMAODetail] (
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[OrdType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LineNbr] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LineItemStatus] [tinyint] NOT NULL,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMAODetail_UOM] DEFAULT (N'EA'),
	[AllocUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMAODetail_AllocUOM] DEFAULT (N'EA'),
	[AllocConvFactor] int CONSTRAINT [DF_AIMAODetail_AllocConvFactor] DEFAULT (1),
	[RequestedQty] [int] NOT NULL,
	[AllocatedQty] [int] NOT NULL,
	[AdjustedAllocQty] [int] NOT NULL,
	[Cost] [decimal](18, 2) NOT NULL,
	[LineFillPct] [decimal](10, 2) NOT NULL,
	[TxnDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[TxnTimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocationDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocationTimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	PRIMARY KEY  CLUSTERED 
	(
		[OrdNbr],
		[LineNbr],
		[Lcid],
		[Item] 
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX AIMAODetail_Item ON AIMAODetail (Item)
GO
