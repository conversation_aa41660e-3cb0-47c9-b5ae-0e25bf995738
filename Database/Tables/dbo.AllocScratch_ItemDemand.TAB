	/* AllocScratch_ItemDemand -- stores total demand for an item at a priority level */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_ItemDemand]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_ItemDemand]
	GO
	CREATE TABLE AllocScratch_ItemDemand (
		SessionID BIGINT NOT NULL,
		DestinationPriority INT NOT NULL, 
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		TotalRequestedQty INT NOT NULL, 
		SumNeedRatios DECIMAL(15, 5) NOT NULL
	);
	GO
	CREATE CLUSTERED INDEX ASID_SESS ON AllocScratch_ItemDemand (SessionID)
	GO

