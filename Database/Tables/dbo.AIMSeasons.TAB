/****** Object:  Table [dbo].[AIMSeasons]    Script Date: 05/30/2003 11:04:38 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMSeasons]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMSeasons]
GO

CREATE TABLE [dbo].[AIMSeasons] (
	[SaVersion] [smallint] NOT NULL CONSTRAINT [DF_AIMSeasons_SaVersion] DEFAULT (0),
	[SaId] [nvarchar] (62) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SaDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SaLevel] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMSeasons_SaLevel] DEFAULT (''),
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMSeasons_LcId] DEFAULT (''),
	[Class] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMSeasons_Class1] DEFAULT (''),
	[BI01] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI01] DEFAULT (0),
	[BI02] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI02] DEFAULT (0),
	[BI03] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI03] DEFAULT (0),
	[BI04] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI04] DEFAULT (0),
	[BI05] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI05] DEFAULT (0),
	[BI06] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI06] DEFAULT (0),
	[BI07] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI07] DEFAULT (0),
	[BI08] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI08] DEFAULT (0),
	[BI09] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI09] DEFAULT (0),
	[BI10] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI10] DEFAULT (0),
	[BI11] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI11] DEFAULT (0),
	[BI12] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI12] DEFAULT (0),
	[BI13] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI13] DEFAULT (0),
	[BI14] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI14] DEFAULT (0),
	[BI15] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI15] DEFAULT (0),
	[BI16] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI16] DEFAULT (0),
	[BI17] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI17] DEFAULT (0),
	[BI18] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI18] DEFAULT (0),
	[BI19] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI19] DEFAULT (0),
	[BI20] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI20] DEFAULT (0),
	[BI21] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI21] DEFAULT (0),
	[BI22] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI22] DEFAULT (0),
	[BI23] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI23] DEFAULT (0),
	[BI24] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI24] DEFAULT (0),
	[BI25] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI25] DEFAULT (0),
	[BI26] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI26] DEFAULT (0),
	[BI27] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI27] DEFAULT (0),
	[BI28] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI28] DEFAULT (0),
	[BI29] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI29] DEFAULT (0),
	[BI30] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI30] DEFAULT (0),
	[BI31] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI31] DEFAULT (0),
	[BI32] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI32] DEFAULT (0),
	[BI33] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI33] DEFAULT (0),
	[BI34] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI34] DEFAULT (0),
	[BI35] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI35] DEFAULT (0),
	[BI36] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI36] DEFAULT (0),
	[BI37] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI37] DEFAULT (0),
	[BI38] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI38] DEFAULT (0),
	[BI39] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI39] DEFAULT (0),
	[BI40] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI40] DEFAULT (0),
	[BI41] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI41] DEFAULT (0),
	[BI42] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI42] DEFAULT (0),
	[BI43] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI43] DEFAULT (0),
	[BI44] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI44] DEFAULT (0),
	[BI45] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI45] DEFAULT (0),
	[BI46] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI46] DEFAULT (0),
	[BI47] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI47] DEFAULT (0),
	[BI48] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI48] DEFAULT (0),
	[BI49] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI49] DEFAULT (0),
	[BI50] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI50] DEFAULT (0),
	[BI51] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI51] DEFAULT (0),
	[BI52] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI52] DEFAULT (0),
	[ItmCnt] [int] NOT NULL CONSTRAINT [DF_AIMSeasons_ItmCnt] DEFAULT (0),
	[AvgUnits] [decimal](18, 1) NOT NULL CONSTRAINT [DF_AIMSeasons_AvgUnits] DEFAULT (0),
	CONSTRAINT [PK_AIMSeasons] PRIMARY KEY  NONCLUSTERED 
	(
		[SaVersion],
		[SaId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


