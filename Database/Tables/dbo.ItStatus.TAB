/****** Object:  Table [dbo].[ItStatus]    Script Date: 05/30/2003 11:04:44 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItStatus]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItStatus]
GO

CREATE TABLE [dbo].[ItStatus] (
	[ItStat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItStatDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Ordgen] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DmdUpd] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VCAssn] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CanPurge] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItStatus_CanPurge] DEFAULT ('N'),
	[BuyerReviewSeq] [tinyint] NOT NULL CONSTRAINT [DF_ItStatus_BuyerReviewSeq] DEFAULT (0),
	[InclInSeasonsGen] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_ItStat] PRIMARY KEY  CLUSTERED 
	(
		[ItStat]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


