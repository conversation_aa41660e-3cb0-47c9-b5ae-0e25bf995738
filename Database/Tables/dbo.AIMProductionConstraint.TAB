/****** Object:  Table [dbo].[AIMProductionConstraint]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMProductionConstraint]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMProductionConstraint]
GO

CREATE TABLE [dbo].[AIMProductionConstraint] (
	[ConstraintID] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ConstraintType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ConstraintDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CycleDays] [numeric](18, 0) NOT NULL ,
	[RatioType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[EnableConstraint] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMProductionConstraint] PRIMARY KEY  CLUSTERED 
	(
		[ConstraintID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


