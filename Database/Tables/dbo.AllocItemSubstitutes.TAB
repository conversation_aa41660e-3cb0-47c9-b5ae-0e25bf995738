/****** Object:  Table [dbo].[AllocItemSubstitutes]    Script Date: 05/30/2003 11:04:33 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AllocItemSubstitutes]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AllocItemSubstitutes]
GO

CREATE TABLE [dbo].[AllocItemSubstitutes] (
	[AllocPrimaryItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocSubstItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocSubstPriority] smallint
		 PRIMARY KEY  CLUSTERED 
	(
		[AllocPrimaryItem],
		[AllocSubstItem]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

