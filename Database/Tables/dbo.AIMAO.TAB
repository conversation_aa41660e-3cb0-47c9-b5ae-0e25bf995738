/****** Object:  Table [dbo].[AIMAO]    Script Date: 05/30/2003 11:04:18 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMAO]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMAO]
GO

CREATE TABLE [dbo].[AIMAO] (
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OrderStatus] [tinyint] NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OriginCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LineCount] [int] NOT NULL ,
	[TotalUnits] [int] NOT NULL ,
	[LocFillPct] [decimal](10, 2) NOT NULL ,
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	 PRIMARY KEY  CLUSTERED 
	(
		[OrdNbr]
		, [LcID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX AIMAO_LcID ON AIMAO (LcID)
GO

