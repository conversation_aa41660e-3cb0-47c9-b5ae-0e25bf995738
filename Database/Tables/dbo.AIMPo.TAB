/****** Object:  Table [dbo].[AIMPo]    Script Date: 05/30/2003 11:04:36 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[<PERSON>MPo]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMPo]
GO

CREATE TABLE [dbo].[AIMPo] (
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TransmitPO] [tinyint] NOT NULL ,
	[ShipIns] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Remarks1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Remarks2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Remarks3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AddrOverride] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LineCount] [int] NOT NULL ,
	[PosLineCount] [int] NOT NULL CONSTRAINT [DF_AIMPo_PosLineCount] DEFAULT (0),
	[Vn_Min] [numeric](9, 2) NOT NULL ,
	[Vn_Best] [numeric](9, 2) NOT NULL ,
	[Reach_Code] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POByZone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Dft_LeadTime] [smallint] NOT NULL ,
	[TotalCost] [decimal](10, 2) NOT NULL CONSTRAINT [DF_AIMPo_TotalCost] DEFAULT (0),
        [VndSizeFlag] [nvarchar] (1) NOT NULL DEFAULT 'N', 
        CONSTRAINT [PK_AIMPo] PRIMARY KEY  CLUSTERED 
	(
		[ById],
		[VnId],
		[Assort]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


