/****** Object:  Table [dbo].[AIMDataExchangeCtrl]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDataExchangeCtrl]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDataExchangeCtrl]
GO

CREATE TABLE [dbo].[AIMDataExchangeCtrl] (
	[TxnSet] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FileName] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TxnDate] [datetime] NOT NULL ,
	CONSTRAINT [PK_AIMDataExchangeCtrl] PRIMARY KEY  CLUSTERED 
	(
		[TxnSet],
		[FileName]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


