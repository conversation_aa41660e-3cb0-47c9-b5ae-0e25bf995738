/****** Object:  Table [dbo].[AIMDxRS]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxRS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxRS]
GO

CREATE TABLE [dbo].[AIMDxRS] (
	[SeqNbr] [int] NOT NULL ,
	[Lcid_Destination] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Lcid_Source] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Src_Priority] [smallint] NULL ,
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
) ON [PRIMARY]
GO


