
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[UserElement]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[UserElement]
GO

CREATE TABLE [dbo].[UserElement] (
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserElementId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserElementDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstType] [nchar] (15) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Processed_YN] [bit] NOT NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[UserElement] WITH NOCHECK ADD 
	CONSTRAINT [PK_UserElement] PRIMARY KEY  CLUSTERED 
	(
		[UserElementId]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[UserElement] ADD 
	CONSTRAINT [DF_UserElement_Processed_YN] DEFAULT (0) FOR [Processed_YN]
GO
