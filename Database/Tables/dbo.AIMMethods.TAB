/****** Object:  Table [dbo].[AIMMethods]    Script Date: 05/30/2003 11:04:35 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMMethods]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMMethods]
GO

CREATE TABLE [dbo].[AIMMethods] (
	[MethodId] [tinyint] NOT NULL ,
	[MethodStatus] [int] NOT NULL ,
	[MethodDesc] [nvarchar] (80) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ApplySeasonsIndex] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ApplyTrend] [bit] NOT NULL ,
	CONSTRAINT [PK_AIMMethods] PRIMARY KEY  NONCLUSTERED 
	(
		[MethodId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


