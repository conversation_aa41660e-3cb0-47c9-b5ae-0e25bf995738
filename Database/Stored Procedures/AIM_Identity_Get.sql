if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Identity_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Identity_Get_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:AIM_Identity_Get_Sp
**	Desc:Returns last Identity value from a table
**
**	Returns: 1) Value
**		 2) Error
**             
**	Auth:   Srinivas Uddanti 
**	Date:   06/09/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/
     
CREATE  PROCEDURE AIM_Identity_Get_Sp
(
	@TableName as nvarchar (45)
)       				

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE 
   	@sql       			nvarchar(500) 

BEGIN
            
  SET NOCOUNT ON 
 
  SET @sql = N'SELECT IDENT_CURRENT(' + char(39)
  SET @sql = @sql +  @TableName 
  SET @sql = @sql +   char(39) + ')'
 
  EXEC(@sql)

  RETURN

  END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

