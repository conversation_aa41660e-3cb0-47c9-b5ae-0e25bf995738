SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_ItemPosition_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_ItemPosition_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemPosition_Sp
**	Desc: Retrieves the Item position FROM the Item table
**
**	Returns: 1)@@rowcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------	---------------------------------------
**      2003/03/01 	Mohammed     	- Add new Parameters (LcID AND Case)
**                            	        - The Case parameter will define 
**                              	  the Item/Location Type (MDC or Dependent)
**                            	        - Add new fields to the SELECT statement 
**                              	  related to the MDC Changes.  
**      2003/05/08 	A. Stocksdale	- Renamed 'RecordType' to 'DCtype'
**	2003/06/05	A. Stocksdale	Commented out above MDC updates for v4.4
**      2003/06/19      Mohammed        Remove some comments to get all the 
**                                      previous on the Position Grid.
**	2003/10/06	Sujit	        Removed some comments for MDC 
**                                      functionality.
*******************************************************************************/

CREATE PROCEDURE AIM_ItemPosition_Sp
(
      	@Item   		nvarchar(25),
        @LcID   		nvarchar(25),
	@Case                   nvarchar(20) OUTPUT		--ssurve10062003
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON
/* Begin: Mohammed 2003/03/01 */
DECLARE @MDC                         nvarchar(25),
        @MDCFlag                     nvarchar(25)
   
     SELECT @MDC = MDC, 
		   @MDCFlag = MDCFlag 
	FROM Item 
 	WHERE Item = @Item 
 	AND LcID = @LcID
--ssurve10062003 
    IF @MDCFlag = 'Y'
    BEGIN
		SELECT @Case='MDC'
    END
	ELSE IF @MDCFlag = 'N'
    BEGIN
		SELECT @Case='DEPENDENT'
    END
--ssurve10062003
/* End: Mohammed 2003/03/01 */
	
	SELECT Item.LcID, Item.OH, Item.OO, Item.ComStk, Item.BkOrder, Item.BkComStk, 
        Avail = (Item.OH + Item.OO - Item.ComStk - Item.BkOrder - Item.BkComStk),
        Excess = CASE
        	WHEN (Item.OH + Item.OO - Item.ComStk - Item.BkOrder - Item.BkComStk) > (Item.OrderPt + Item.OrderQty)
            THEN (Item.OH + Item.OO - Item.ComStk - Item.BkOrder - Item.BkComStk) - (Item.OrderPt + Item.OrderQty)
	        ELSE 0
        	END
/* Begin: Mohammed 2003/03/01 */
-- --	FROM Item
-- --	WHERE Item = @Item
-- --	ORDER BY LcID, Item
/* Removed comments June 05, 2003 - A. Stocksdale */
 		, DCType = CASE 
 			WHEN Item.MDCFlag = 'Y' THEN 'MDC' 
 			WHEN (Item.MDCFlag = 'N' AND Item.MDC = ' ') THEN 'InDependent'
 			WHEN (Item.MDCFlag = 'N' AND Item.MDC <> ' ') THEN 'Dependent'
         	End, 
         IsNull(Sum(PODetail.Original_VSOQ),0) VSOQ,
         IsNull(Sum(PODetail.VSOQ),0) SugVSOQ,
         MDCVSOQ=	(SELECT IsNull(Sum(VSOQ),0) 
 					FROM PODetail,Item 
 					WHERE Item.Item = @Item 
 					AND Item.Item*=PODetail.Item 
 					AND Item.LcID*=PODetail.LcID 
 					AND Item.MDCFlag = 'Y'),
         OrgMDCVSOQ=	(SELECT IsNull(Sum(Original_VSOQ),0) 
 					FROM PODetail,Item 
 					WHERE Item.Item = @Item 
 					AND Item.Item*=PODetail.Item 
 					AND Item.LcID*=PODetail.LcID 
 					AND Item.MDCFlag = 'Y')
-- --    PODetail.UOM, 
-- --    PODetail.BuyingUOM, 
-- --    PODetail.ConvFactor,
-- --    IMin=Coalesce(Item.IMin,0), 
-- --    IMax=Coalesce(Item.IMax,0), 
-- --    PODetail.PackRounding, 
-- --    PODetail.SOQ
 	FROM Item,PODetail
 	WHERE Item.Item = @Item
 	AND Item.Item *= PODetail.Item
 	AND Item.LcID *= PODetail.LcID
        -- Mohammed - Begin
        Group By Item.LcId, Item.Item, Item.oh, Item.oo, Item.comstk, Item.bkorder, item.bkcomstk,
                 Item.orderpt,Item.orderqty, Item.MDCFlag, Item.MDC
        -- Mohammed - End
 	ORDER BY Item.LcID, Item.Item
/* End: Mohammed 2003/03/01 */
        
  RETURN @@rowcount

GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

