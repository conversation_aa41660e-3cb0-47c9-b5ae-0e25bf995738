SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AimVendors_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AimVendors_GetKey_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AimVendors_GetKey_Sp
**	Desc: Retrieves the Vendor data based on the Action Type
**
**	Returns: 1) 0
**	Values:  Recordset - AIMVendors
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   Author:  	Description:
**  ---------- ------------	-----------------------------------------------
**  	
*******************************************************************************/
   
CREATE PROCEDURE AIM_AimVendors_GetKey_Sp
(
     	@VnId       						nvarchar(12)	Output,
     	@Assort     						nvarchar(12)	Output,
    	@Action     						tinyint
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rowcount 						int
  
SET NOCOUNT ON

SET rowcount 1

-- Get the key to the Vendor/Assortment Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
  	SELECT @VnId = VnId, @Assort = Assort
        FROM AIMVendors 
        WHERE VnId = @VnId
        AND Assort = @Assort
        
	SELECT @rowcount = @@rowcount

END
IF @action = 1		-- Get Greater Than
BEGIN
        SELECT @VnId = VnId, @Assort = Assort 
            FROM AIMVendors 
            WHERE VnId = @VnId 
            AND Assort > @Assort 
        
	SELECT @rowcount = @@rowcount
        
	IF @rowcount = 0
        BEGIN
        	SELECT @VnId = VnId, @Assort = Assort 
               	FROM AIMVendors 
               	WHERE VnId > @VnId
           
		SELECT @rowcount = @@rowcount
       	END
END
IF @action = 2		-- Get Less Than 
BEGIN
	SELECT @VnId = VnId, @Assort = Assort 
        FROM AIMVendors 
        WHERE VnId = @VnId 
        AND Assort < @Assort
        ORDER BY VnId desc, Assort desc
  
	SELECT @rowcount = @@rowcount

       	IF @rowcount = 0
       	BEGIN
        	SELECT @VnId = VnId, @Assort = Assort 
        	FROM AIMVendors 
               	WHERE VnId < @VnId
               	ORDER BY VnId desc, Assort desc
           
		SELECT @rowcount = @@rowcount
       	END
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
  	SELECT @VnId = VnId, @Assort = Assort 
        FROM AIMVendors 
        WHERE VnId = @VnId 
        AND Assort >= @Assort
       
	SELECT @rowcount = @@rowcount
       
	IF @rowcount = 0
       	BEGIN
        	SELECT @VnId = VnId, @Assort = Assort 
               	FROM AIMVendors 
               	WHERE VnId > @VnId  

           	SELECT @rowcount = @@rowcount
       	END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
       SELECT @VnId = VnId, @Assort = Assort 
       FROM AIMVendors 
       WHERE VnId = @VnId 
       AND Assort <= @Assort
       ORDER BY VnId desc, Assort desc
	
       SELECT @rowcount = @@rowcount
  	IF @rowcount = 0
       BEGIN
       		SELECT @VnId = VnId, @Assort = Assort 
               	FROM AIMVendors 
               	WHERE VnId < @VnId  
               	ORDER BY VnId desc, Assort desc
           	
		SELECT @rowcount = @@rowcount
       END
END
IF @action = 5		-- Get First
BEGIN
  	SELECT @VnId = VnId, @Assort = Assort 
        FROM AIMVendors  

	SELECT @rowcount = @@rowcount

END
IF @action = 6		-- Get Last
BEGIN
  	SELECT @VnId = VnId, @Assort = Assort 
        FROM AIMVendors 
        ORDER BY VnId desc, Assort desc

	SELECT @rowcount = @@rowcount
END
IF @rowcount > 0  
  	RETURN 1		-- SUCCEED
ELSE
        RETURN 0		-- FAIL

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

