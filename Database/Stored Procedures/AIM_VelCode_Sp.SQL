SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VelCode_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VelCode_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VelCode_Sp
**	Desc: Calculates Velocity Codes for each item in the selected locations 
**            on data from the item table.  Results are written to the item table.
**
**	Returns: 1)@found - Can be Zero
**
**	Values:  @LcIdOpt    Location Option
**			     All = All Locataion
**                           LcId = SpecIFic Location
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**      10/01/2002 Osama Riyahi Added Freeze_OptionID
*******************************************************************************/
  
CREATE  PROCEDURE AIM_VelCode_Sp	
(
    	@LcIdOpt            					nvarchar(12) = 'All'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE	
    	@VCCmpPctA          					decimal(4,3),
    	@VCCmpPctB          					decimal(4,3),
    	@VCCmpPctC          					decimal(4,3),
    	@VCCmpPctD          					decimal(4,3),
    	@VCCmpPctE          					decimal(4,3),
    	@VCCmpPctF          	 				decimal(4,3),
    	@VCCmpPctG         	 	     			decimal(4,3),
    	@VCCmpPctH         	 	    			decimal(4,3),
    	@VCCmpPctI          					decimal(4,3),
    	@VCCmpPctJ          					decimal(4,3),
    	@VCCmpPctK         	 	     			decimal(4,3),
    	@VCCmpPctL          					decimal(4,3),
        @VcCmpPct           					decimal(4,3),
    	@Dft_OptionId_A     					nvarchar(6),
  	    @Dft_OptionId_B     					nvarchar(6),
  	    @Dft_OptionId_C     					nvarchar(6),
  	    @Dft_OptionId_D     					nvarchar(6),
  	    @Dft_OptionId_E     					nvarchar(6),
  	    @Dft_OptionId_F     					nvarchar(6),
  	    @Dft_OptionId_G    		    		 	nvarchar(6),
  	    @Dft_OptionId_H    		    			nvarchar(6),
  	    @Dft_OptionId_I     					nvarchar(6),
  	    @Dft_OptionId_J     					nvarchar(6),
  	    @Dft_OptionId_K     					nvarchar(6),
     	@Dft_OptionId_L     					nvarchar(6),
 	
	-- Item Table
    	@LcId               					nvarchar(12),
     	@Item               					nvarchar(25),
     	@VelCode            					nvarchar(1),
     	@VC_Amt             					nvarchar(1),
    	@VC_Date            					datetime,
     	@OptionId           					nvarchar(6),
    	@UnitRank           					int,
    	@DolRank            					int,
    	@RankPct            					float,
    	@Rows               					int,
	@Freeze_OptionId					nvarchar(1)

SET NOCOUNT ON

-- Get the Accumulative Percentages from the AIM Velocity Code Percentages Table
SELECT @VCCmpPct = 0
SELECT @VCCmpPctA = VcPct, @Dft_OptionId_A = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'A'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctA
SELECT @VCCmpPctB = VcPct, @Dft_OptionId_B = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'B'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctB
SELECT @VCCmpPctB = @VcCmpPct
SELECT @VCCmpPctC = VcPct, @Dft_OptionId_C = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'C'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctC
SELECT @VCCmpPctC = @VcCmpPct
SELECT @VCCmpPctD = VcPct, @Dft_OptionId_D = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'D'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctD
SELECT @VCCmpPctD = @VcCmpPct
SELECT @VCCmpPctE = VcPct, @Dft_OptionId_E = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'E'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctE
SELECT @VCCmpPctE = @VcCmpPct
SELECT @VCCmpPctF = VcPct, @Dft_OptionId_F = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'F'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctF
SELECT @VCCmpPctF = @VcCmpPct
SELECT @VCCmpPctG = VcPct, @Dft_OptionId_G = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'G'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctG
SELECT @VCCmpPctG = @VcCmpPct
SELECT @VCCmpPctH = VcPct, @Dft_OptionId_H = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'H'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctH
SELECT @VCCmpPctH = @VcCmpPct
SELECT @VCCmpPctI = VcPct, @Dft_OptionId_I = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'I'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctI
SELECT @VCCmpPctI = @VcCmpPct
SELECT @VCCmpPctJ = VcPct, @Dft_OptionId_J = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'J'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctJ
SELECT @VCCmpPctJ = @VcCmpPct
SELECT @VCCmpPctK = VcPct, @Dft_OptionId_K = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'K'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctK
SELECT @VCCmpPctK = @VcCmpPct
SELECT @VCCmpPctL = VcPct, @Dft_OptionId_L = Dft_OptionId FROM AIMVelCodePcts WHERE VelCode = 'L'
SELECT @VcCmpPct = @VcCmpPct + @VcCmpPctL
SELECT @VCCmpPctL = @VcCmpPct

-- Get the Velocity Code Date
SELECT @VC_Date = CONVERT(nvarchar(10), GETDATE(), 101)
-- Build the Velocity Code Cursor for Unit Ranking
IF @LcIdOpt = 'All'            -- Global Option
BEGIN
        DECLARE VcWk_Cursor CURSOR LOCAL FORWARD_ONLY KEYSET OPTIMISTIC FOR 
            SELECT Item.Lcid, Item.Item, Item.VelCode, Item.OptionId , Item.Freeze_OptionId
                FROM Item 
                INNER JOIN ItStatus 
                    ON Item.ItStat = ItStatus.ItStat 
                WHERE ItStatus.VCAssn = 'Y'
                ORDER BY Item.FcstDemand DESC        
            FOR UPDATE
END
ELSE
BEGIN
        DECLARE VcWk_Cursor CURSOR LOCAL FORWARD_ONLY KEYSET OPTIMISTIC FOR 
	    --Changed By Osama Riyahi - start
            --SELECT Item.Lcid, Item.Item, Item.VelCode, Item.OptionId 
            SELECT Item.Lcid, Item.Item, Item.VelCode, Item.OptionId, Item.Freeze_OptionId 
	    --Changed By Osama Riyahi - end
                FROM Item 
                INNER JOIN ItStatus 
                    ON Item.ItStat = ItStatus.ItStat 
                WHERE Item.LcId = @LcIdOpt
                AND ItStatus.VCAssn = 'Y'
                ORDER BY Item.FcstDemand DESC        
            FOR UPDATE
END
OPEN VcWk_Cursor

-- Initialize the Counts
SELECT @Rows = @@CURSOR_ROWS, @UnitRank = 0
-- Start Velocity Code Loop
WHILE 1 = 1
BEGIN
        FETCH NEXT FROM VcWk_Cursor into @LcId, @Item, @VelCode, @OptionId, @Freeze_OptionId	
        -- Check for End-Of-Cursor
        IF @@FETCH_STATUS = -1      -- End of Cursor
            BREAK
        IF @@FETCH_STATUS = -2      -- Missing Value
            CONTINUE
        
        -- Increment the unit ranking
        SELECT @UnitRank = @UnitRank + 1
        
        -- Calculate the Ranking Percentage
        SELECT @RankPct = CAST(@UnitRank AS float) / CAST(@Rows AS float)
        
        -- Set the new velocity code
        SELECT @VelCode = CASE 
            WHEN @RankPct <= @VCCmpPctA THEN 'A'
            WHEN @RankPct <= @VCCmpPctB THEN 'B'
            WHEN @RankPct <= @VCCmpPctC THEN 'C'
            WHEN @RankPct <= @VCCmpPctD THEN 'D'
            WHEN @RankPct <= @VCCmpPctE THEN 'E'
            WHEN @RankPct <= @VCCmpPctF THEN 'F'
            WHEN @RankPct <= @VCCmpPctG THEN 'G'
            WHEN @RankPct <= @VCCmpPctH THEN 'H'
            WHEN @RankPct <= @VCCmpPctI THEN 'I'
            WHEN @RankPct <= @VCCmpPctJ THEN 'J'
            WHEN @RankPct <= @VCCmpPctK THEN 'K'
            WHEN @RankPct <= @VCCmpPctL THEN 'L'
            END     
        
        -- Override the Item's Option Id
        SELECT @OptionId = CASE  
            WHEN @VelCode = 'A' and @Dft_OptionId_A <> '' THEN @Dft_OptionId_A
            WHEN @VelCode = 'B' and @Dft_OptionId_B <> '' THEN @Dft_OptionId_B
            WHEN @VelCode = 'C' and @Dft_OptionId_C <> '' THEN @Dft_OptionId_C
            WHEN @VelCode = 'D' and @Dft_OptionId_D <> '' THEN @Dft_OptionId_D
            WHEN @VelCode = 'E' and @Dft_OptionId_E <> '' THEN @Dft_OptionId_E
            WHEN @VelCode = 'F' and @Dft_OptionId_F <> '' THEN @Dft_OptionId_F
            WHEN @VelCode = 'G' and @Dft_OptionId_G <> '' THEN @Dft_OptionId_G
            WHEN @VelCode = 'H' and @Dft_OptionId_H <> '' THEN @Dft_OptionId_H
            WHEN @VelCode = 'I' and @Dft_OptionId_I <> '' THEN @Dft_OptionId_I
            WHEN @VelCode = 'J' and @Dft_OptionId_J <> '' THEN @Dft_OptionId_J
            WHEN @VelCode = 'K' and @Dft_OptionId_K <> '' THEN @Dft_OptionId_K
            WHEN @VelCode = 'L' and @Dft_OptionId_L <> '' THEN @Dft_OptionId_L
            ELSE @OptionId
            END

	--Changed By Osama Riyahi - start
	IF @Freeze_OptionId = 'Y'
	BEGIN             
        	UPDATE Item SET VelCode = @VelCode,
            	Vc_Units_Ranking = @UnitRank,
            	VC_Date = @VC_Date,
            	VelCode_Prev = @VelCode
            	WHERE CURRENT OF VcWk_Cursor 
	END
	ELSE
	BEGIN
        	UPDATE Item SET VelCode = @VelCode,
            	Vc_Units_Ranking = @UnitRank,
            	VC_Date = @VC_Date,
            	VelCode_Prev = @VelCode,
            	OptionId = @OptionId
            	WHERE CURRENT OF VcWk_Cursor 
	END
	--Changed By Osama Riyahi - end
END

-- Clean Up
CLOSE VcWk_Cursor
DEALLOCATE VcWk_Cursor

-- Build the Velocity Code Cursor for Amount Ranking
IF @LcIdOpt = 'All'            -- Global Option
BEGIN
        DECLARE VcWk_Cursor CURSOR LOCAL FORWARD_ONLY KEYSET OPTIMISTIC FOR 
            SELECT Item.Lcid, Item.Item, Item.VC_Amt, Item.OptionId
                FROM Item 
                INNER JOIN ItStatus 
                    ON Item.ItStat = ItStatus.ItStat 
                WHERE ItStatus.VCAssn = 'Y'
                ORDER BY Item.FcstDemand * Item.Cost DESC        
            FOR UPDATE
END
ELSE
BEGIN
        DECLARE VcWk_Cursor CURSOR LOCAL FORWARD_ONLY KEYSET OPTIMISTIC FOR 
            SELECT Item.Lcid, Item.Item, Item.VC_Amt, Item.OptionId
                FROM Item 
                INNER JOIN ItStatus 
                    ON Item.ItStat = ItStatus.ItStat 
                WHERE Item.LcId = @LcIdOpt
                AND ItStatus.VCAssn = 'Y'
                ORDER BY Item.FcstDemand * Item.Cost DESC        
            FOR UPDATE
END
OPEN VcWk_Cursor

-- Initialize the Counts
SELECT @Rows = @@CURSOR_ROWS, @DolRank = 0
-- Start Velocity Code Loop
WHILE 1 = 1
BEGIN
        FETCH NEXT FROM VcWk_Cursor INTO @LcId, @Item, @VC_Amt, @OptionId
        
        -- Check for End-Of-Cursor
        IF @@FETCH_STATUS = -1      -- End of Cursor
            BREAK
        IF @@FETCH_STATUS = -2      -- Missing Value
            CONTINUE
        
        -- Increment the dollar ranking
        SELECT @DolRank = @DolRank + 1
        
        -- Calculate the Ranking Percentage
        SELECT @RankPct = cast(@DolRank AS float) / cast(@Rows AS float)
        
        UPDATE Item SET VC_Amt = Case 
            WHEN @RankPct <= @VCCmpPctA THEN 'A'
            WHEN @RankPct <= @VCCmpPctB THEN 'B'
            WHEN @RankPct <= @VCCmpPctC THEN 'C'
            WHEN @RankPct <= @VCCmpPctD THEN 'D'
            WHEN @RankPct <= @VCCmpPctE THEN 'E'
            WHEN @RankPct <= @VCCmpPctF THEN 'F'
            WHEN @RankPct <= @VCCmpPctG THEN 'G'
            WHEN @RankPct <= @VCCmpPctH THEN 'H'
            WHEN @RankPct <= @VCCmpPctI THEN 'I'
            WHEN @RankPct <= @VCCmpPctJ THEN 'J'
            WHEN @RankPct <= @VCCmpPctK THEN 'K'
            WHEN @RankPct <= @VCCmpPctL THEN 'L'
            END, 
            Vc_Amt_Ranking = @DolRank,
            Vc_Amt_Prev = @Vc_Amt
            WHERE CURRENT OF VcWk_Cursor
END
-- Clean Up
CLOSE VcWk_Cursor
DEALLOCATE VcWk_Cursor

RETURN @Rows

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

