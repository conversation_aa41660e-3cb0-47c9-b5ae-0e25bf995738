SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_Delete_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: 	AIM_ProductionConstraint_Delete_Sp
**	Desc: 	Deletes the AIMProductionConstraint record and all its children
** 		Derived from AIM_Forecast_Delete_Sp
**	Parameters: 
** 		@ConstraintID -- alphanumeric
**	Returns: 
**		 @Found --> (Can be = 0) Successful
**		-1 --> No Data Found
**		-2 --> SQL Error
**		-3 --> User Not Permitted to retrieve data
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2003/01/16
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	 Description:
**	---------- ------------- --------------------------------------------
*******************************************************************************/

CREATE  PROCEDURE AIM_ProductionConstraint_Delete_Sp
(
 	@ConstraintID nvarchar(30)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	
	DECLARE @found int,
		@rtrn int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @ConstraintID Is Null
	BEGIN
	  	RETURN -1
	END
	
	BEGIN TRAN --Start of Transaction
	
		DELETE FROM AIMProductionConstraintDetail
		WHERE ConstraintID = @ConstraintID
	
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
			ROLLBACK TRAN
			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
	
		DELETE FROM AIMProductionConstraint
		WHERE ConstraintID = @ConstraintID
	
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
			ROLLBACK TRAN
			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
	
	COMMIT TRAN  -- End of Transaction
	
	RETURN 0

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

