SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_AllocateInventory_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_AllocateInventory_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:				Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:				Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp				Main Stored Procedure
**
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**											Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_AllocateInventory_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Called by AIM_AllocationCtrl_Sp
**		Control procedure that re-runs weighted allocation if inventory is not fully allocated.
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/31
**-----------------------------------------------------------------------------
**	Change History
**-----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------	-----------------------------------------------
**								
*******************************************************************************/

CREATE PROCEDURE AIM_AllocateInventory_Sp 
(
	@SessID AS BIGINT
	, @DestinationPriority AS INT
	, @RequestedItem AS NVARCHAR(25)
	, @AllocatableItem AS NVARCHAR(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @NumStores As INT
	DECLARE @AllocatableQty AS INT
	DECLARE @RemainingRequests AS INT	
	DECLARE @Return_Stat AS INT

	SET NOCOUNT ON

	-- Validate inputs
	IF @SessID IS NULL
	OR @DestinationPriority IS NULL
	OR @RequestedItem IS NULL
	BEGIN	
		RETURN -1  -- ERROR
	END
	IF @AllocatableItem IS NULL
	BEGIN
		-- Set it equal to the item
		SET @AllocatableItem = @RequestedItem
	END
	
	--Run Allocation
	EXEC @Return_Stat = AIM_Alloc_Pass1_Sp @SessID, @DestinationPriority, @RequestedItem, @AllocatableItem, @AllocatableQty OUTPUT, @RemainingRequests OUTPUT
	IF @Return_Stat < 0 
	OR @@ERROR <> 0
	BEGIN
		RETURN @Return_Stat
	END


	-- The first pass of allocation may have had some inventory left over after rounding down by need ratio.
	-- If there is still inventory to be distributed, then allocate individual units to stores based on their need
	-- until the remaining inventory is used up.
	-- Get Num of stores still eligible for allocation
	If (@RemainingRequests > 0)
	BEGIN
		SELECT @NumStores = Count(ASRQ.DestinationLoc)
			FROM AllocScratch_Req_Qty ASRQ
			INNER JOIN AllocScratch_Req_Pri ASRP
				On ASRQ.DestinationLoc = ASRP.DestinationLoc
			WHERE ASRP.DestinationPriority = @DestinationPriority
				AND ASRQ.ItemID = @RequestedItem
				AND ASRQ.SessionID = @SessID
				AND ASRP.SessionID = @SessID
				AND ASRQ.RequestedQty > 0
		-- Check if remainder is greater than the number of stores requesting it.
		-- This might happen if some of the store requests were out of range from the others.
		IF (@AllocatableQty > @NumStores) 
		BEGIN
			-- If yes then run alloc once more 
			EXEC @Return_Stat = AIM_Alloc_Pass1_Sp @SessID, @DestinationPriority, @RequestedItem, @AllocatableItem, @AllocatableQty OUTPUT, @RemainingRequests OUTPUT
			IF @Return_Stat < 0 
			OR @@ERROR <> 0
			BEGIN
				RETURN @Return_Stat
			END

		END
		If (@RemainingRequests > 0)
		BEGIN
			-- If there is still something left over, or remainder from the first pass was less than the number of stores
			-- then round-robin distribute
			IF (@AllocatableQty > 0)
			BEGIN
				-- Finally, if > 0 but < number of stores, then while greater than zero sort by need and allocate one 
				EXEC @Return_Stat = AIM_Alloc_Pass2_Sp @SessID, @DestinationPriority, @RequestedItem, @AllocatableItem, @AllocatableQty OUTPUT, @RemainingRequests OUTPUT
				IF @Return_Stat < 0 
				OR @@ERROR <> 0
				BEGIN
					RETURN @Return_Stat
				END

			END
		END
	END
	RETURN

END
GO

SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

