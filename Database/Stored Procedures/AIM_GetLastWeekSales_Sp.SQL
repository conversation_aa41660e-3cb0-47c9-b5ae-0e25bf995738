SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetLastWeekSales_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetLastWeekSales_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetLastWeekSales_Sp 
**	Desc: Gets the Item's weekly sales data for an Item.
**
**	Returns: 1) @rowcount
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   Author:   	Description:
**  ---------- ---------------	-----------------------------------------------
**  2003/09/23 Srinivas U       Modified code for subsitem 
**  2005/11/09 Srinivas U	Fixed bug for SP Richards
*******************************************************************************/
     
CREATE PROCEDURE AIM_GetLastWeekSales_Sp
(
    	@LcId           				nvarchar(12),
    	@Item           				nvarchar(25),
    	@FcstUpdCyc     				int
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE @Pd1            				int,
        @Pd2            				int,
        @FiscalYear     				int,
        @cps1           				int,
        @cps2      		     			int

SET NOCOUNT ON

IF @FcstUpdCyc = 0
        RETURN 0
SELECT @FiscalYear = @FcstUpdCyc / 100
SELECT @Pd1 = @FcstUpdCyc - (@FiscalYear * 100)
SELECT @Pd2 = @Pd1 + 1
IF @Pd2 > 52 
        SELECT @Pd2 = 1
SELECT @cps1 = case @Pd1
        WHEN 1 THEN sum(cps01)
        WHEN 2 THEN sum(cps02)
        WHEN 3 THEN sum(cps03)
        WHEN 4 THEN sum(cps04)
        WHEN 5 THEN sum(cps05)
        WHEN 6 THEN sum(cps06)
        WHEN 7 THEN sum(cps07)
        WHEN 8 THEN sum(cps08)
        WHEN 9 THEN sum(cps09)
        WHEN 10 THEN sum(cps10)
        WHEN 11 THEN sum(cps11)
        WHEN 12 THEN sum(cps12)
        WHEN 13 THEN sum(cps13)
        WHEN 14 THEN sum(cps14)
        WHEN 15 THEN sum(cps15)
        WHEN 16 THEN sum(cps16)
        WHEN 17 THEN sum(cps17)
        WHEN 18 THEN sum(cps18)
        WHEN 19 THEN sum(cps19)
        WHEN 20 THEN sum(cps20)
        WHEN 21 THEN sum(cps21)
        WHEN 22 THEN sum(cps22)
        WHEN 23 THEN sum(cps23)
        WHEN 24 THEN sum(cps24)
        WHEN 25 THEN sum(cps25)
        WHEN 26 THEN sum(cps26)
        WHEN 27 THEN sum(cps27)
        WHEN 28 THEN sum(cps28)
        WHEN 29 THEN sum(cps29)
        WHEN 30 THEN sum(cps30)
        WHEN 31 THEN sum(cps31)
        WHEN 32 THEN sum(cps32)
        WHEN 33 THEN sum(cps33)
        WHEN 34 THEN sum(cps34)
        WHEN 35 THEN sum(cps35)
        WHEN 36 THEN sum(cps36)
        WHEN 37 THEN sum(cps37)
        WHEN 38 THEN sum(cps38)
        WHEN 39 THEN sum(cps39)
        WHEN 40 THEN sum(cps40)
        WHEN 41 THEN sum(cps41)
        WHEN 42 THEN sum(cps42)
        WHEN 43 THEN sum(cps43)
        WHEN 44 THEN sum(cps44)
        WHEN 45 THEN sum(cps45)
        WHEN 46 THEN sum(cps46)
        WHEN 47 THEN sum(cps47)
        WHEN 48 THEN sum(cps48)
        WHEN 49 THEN sum(cps49)
        WHEN 50 THEN sum(cps50)
        WHEN 51 THEN sum(cps51)
        WHEN 52 THEN sum(cps52)
        END,
        @cps2 = case @Pd2
        WHEN 1 THEN sum(cps01)
        WHEN 2 THEN sum(cps02)
        WHEN 3 THEN sum(cps03)
        WHEN 4 THEN sum(cps04)
        WHEN 5 THEN sum(cps05)
        WHEN 6 THEN sum(cps06)
        WHEN 7 THEN sum(cps07)
        WHEN 8 THEN sum(cps08)
        WHEN 9 THEN sum(cps09)
        WHEN 10 THEN sum(cps10)
        WHEN 11 THEN sum(cps11)
        WHEN 12 THEN sum(cps12)
        WHEN 13 THEN sum(cps13)
        WHEN 14 THEN sum(cps14)
        WHEN 15 THEN sum(cps15)
        WHEN 16 THEN sum(cps16)
        WHEN 17 THEN sum(cps17)
        WHEN 18 THEN sum(cps18)
        WHEN 19 THEN sum(cps19)
        WHEN 20 THEN sum(cps20)
        WHEN 21 THEN sum(cps21)
        WHEN 22 THEN sum(cps22)
        WHEN 23 THEN sum(cps23)
        WHEN 24 THEN sum(cps24)
        WHEN 25 THEN sum(cps25)
        WHEN 26 THEN sum(cps26)
        WHEN 27 THEN sum(cps27)
        WHEN 28 THEN sum(cps28)
        WHEN 29 THEN sum(cps29)
        WHEN 30 THEN sum(cps30)
        WHEN 31 THEN sum(cps31)
        WHEN 32 THEN sum(cps32)
        WHEN 33 THEN sum(cps33)
        WHEN 34 THEN sum(cps34)
        WHEN 35 THEN sum(cps35)
        WHEN 36 THEN sum(cps36)
        WHEN 37 THEN sum(cps37)
        WHEN 38 THEN sum(cps38)
        WHEN 39 THEN sum(cps39)
        WHEN 40 THEN sum(cps40)
        WHEN 41 THEN sum(cps41)
        WHEN 42 THEN sum(cps42)
        WHEN 43 THEN sum(cps43)        
	WHEN 44 THEN sum(cps44)
        WHEN 45 THEN sum(cps45)
        WHEN 46 THEN sum(cps46)
        WHEN 47 THEN sum(cps47)
        WHEN 48 THEN sum(cps48)
        WHEN 49 THEN sum(cps49)
        WHEN 50 THEN sum(cps50)
        WHEN 51 THEN sum(cps51)
        WHEN 52 THEN sum(cps52)
        END
        FROM ItemHistory 
        WHERE Lcid = @Lcid 
        and Item = @Item
        and HisYear = @FiscalYear
IF @@rowcount = 0
        RETURN 0
Select @cps1 =isnull(@cps1,0),@cps2=isnull(@cps2,0)
IF @cps1 + @cps2 = 0 
        RETURN 0
ELSE
        RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



