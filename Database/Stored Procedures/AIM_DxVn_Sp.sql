SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxVn_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxVn_Sp]
GO

/*******************************************************************************
**	Name: AIM_DxVn_Sp
**	Desc: Loads vendor records into the AIM Vendor/Assortment Table.
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   <PERSON>
**	Date:   03/08/1996
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   		Author:			Description:
**    ---------- 	------------	-----------------------------------------------
**    07/22/1997 	Randy Sadler	Modified vendor update logic so that vendors with a 
**					reach code of C=Cube, U=Units, or W=Weight will not 
**					be updated by the AIM_dxvn_sp stored procedure. 
**					Instead these vendors' vendor minimum, best buy, 
**					and reach codes will be maintained from within the
**					AIM System and the AIM_dxvn_sp will not override 
**					the values entered by the users.
**    2003/05/28	A. Stocksdale	Replaced bulk insert and validation commands 
**					with call to AIM_DxBulkInsert_Sp - common to all Dx processes
**    2005/11/04	S.Uddanti	Replaced with version ft 6/20/03 but added output variables
**					to the stored procedure
*******************************************************************************/

CREATE Procedure AIM_DxVn_Sp
(
	 @FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE 
		-- System Control Table
		@dxpath						nvarchar(255),
		@UpdateVnLt					nvarchar(1),
		@VnLTPctChgFilter				decimal(5,2),
		@VnLTDaysChgFilter				smallint,
		@Dft_LeadTime_Save				smallint,
		@AssortKey			    		nvarchar(12),
		@cmd						nvarchar(255),
		@Existing_Reach_Code				nvarchar(1),
		@MDCFlag					nvarchar(1),
		@RowFound					int,
		@RtnCode                			int,
		@UpdateGoalsOption	    			nvarchar(1), 
		@VnIDKey					nvarchar(12),
		@VnId						nvarchar(12),
		@Assort						nvarchar(12),
		@VnType						nvarchar(1),
		@VName						nvarchar(30),
		@VAddress1					nvarchar(30),
		@VAddress2					nvarchar(30),
		@VCity						nvarchar(20),
		@VState						nvarchar(10),
		@VZip						nvarchar(10),
		@VContact					nvarchar(30),
		@VPhone						nvarchar(20),
		@VFax						nvarchar(20),
		@VEMail						nvarchar(30),
		@VDocEMail					nvarchar(30),
		@Dft_ById					nvarchar(12),
		@RevCycle					nvarchar(8),
		@Dft_LeadTime					smallint,
		@Vn_Min						decimal(9,2),
		@Vn_Best					decimal(9,2),
		@Reach_Code					nvarchar(1),
		@POByZone					nvarchar(1),
		@VnComments1					nvarchar(255),
		@VnComments2					nvarchar(255),
		@TransmitPO					tinyint,
		@ShipIns					nvarchar(20),
		@Last_Dft_LeadTime				tinyint,
		@Unicode_Option					nvarchar(10)
  
	SET NOCOUNT ON
  
	--Initialize counters
	SELECT @InsertCounter = 0, @UpdateCounter = 0

	-- This is a data interface transaction <Transaction Code=VN>. 
	-- Bulk Load the <Transaction Table=AIMDxVN> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'VN', 'AIMDxVN', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
        SELECT 'Inserts' = 0, 
			'Updates' = 0
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- CREATE cursor to process dxvn
	DECLARE DxVn_Cursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT VnId = rtrim(VnId), Assort = rtrim(Assort), 
    	    VnType, VName = rtrim(VName), VAddress1 = rtrim(VAddress1), 
        	VAddress2 = rtrim(VAddress2), VCity = rtrim(VCity), 
    	    VState = rtrim(VState), VZip = rtrim(VZip), 
    	    VContact = rtrim(VContact), VPhone = rtrim(VPhone), 
    	    VFax = rtrim(VFax), VEMail = rtrim(VEMail), 
    	    VDocEMail = rtrim(VDocEMail), Dft_ById = rtrim(Dft_ById), 
    	    RevCycle = rtrim(RevCycle), Dft_LeadTime, 
        	Vn_Min, Vn_Best, Reach_Code, POByZone, 
    	    VnComments = rtrim(VnComments1), 
        	VnComments2 = rtrim(VnComments2),
    		TransmitPO, ShipIns = rtrim(ShipIns)
	FROM AIMDxVn
	OPEN DxVn_Cursor
	-- Check for no rows to process
	IF @@CURSOR_ROWS = 0 
	BEGIN
        RETURN
	END
	-- Update Loop
	WHILE 1 = 1 
	BEGIN 
        FETCH NEXT FROM DxVn_Cursor INTO 
        	@VnId, @Assort, @VnType, @VName, @VAddress1, @VAddress2, @VCity,
    		@VState, @VZip, @VContact, @VPhone, @VFax, @VEMail, @VDocEMail, 
    		@Dft_ById, @RevCycle, @Dft_LeadTime, @Vn_Min, @Vn_Best, 
    		@Reach_Code, @POByZone, @VnComments1, @VnComments2, @TransmitPO,
    		@ShipIns
        -- Check Fetch Status
        IF @@FETCH_STATUS <> 0
		BEGIN
    		break
    	END
	    -- Set the MDC Flag
        IF @VnType = 'V' 
		BEGIN
    		SELECT @MDCFlag = 'N'
		END    
	    ELSE
		BEGIN
    		SELECT @MDCFlag = 'Y'
		END
        -- Check the reach code for valid values
        IF @Reach_Code = ''
		BEGIN
    		SELECT @Reach_Code = 'D'
		END
        -- Check for a valid POByZone Code
        IF @POByZone not in ('N', 'Y') 
		BEGIN
            SELECT @POByZone = 'N'
		END
        -- Check to see IF the row already exists in the vendor table
        SELECT @VnIdKey = vnid, @AssortKey = assort, @Existing_Reach_Code = reach_code,
    		@Last_Dft_LeadTime = Dft_LeadTime
    		FROM AIMVendors
    		WHERE vnid = @VnId
    		and assort = @Assort
        SELECT @RowFound = @@rowcount
        -- New Vendor Row  
        IF @RowFound = 0		
        BEGIN
	 		-- Check for an invalid default lead time
			IF @Dft_LeadTime = 0
			BEGIN
				SELECT @Dft_LeadTime = 10
			END

			-- Insert the new row into the Vendor Table
    		INSERT INTO AIMVendors
        	    		(VnId, Assort, MDCFlag, VName, VAddress1, VAddress2, VCity, 
        	    		VState, VZip, VContact, VPhone, VFax, VEMail, VDocEMail, 
        	    		Dft_Byid, RevCycle, Dft_LeadTime, Last_Dft_LeadTime, Vn_Min, Vn_Best, 
        	    		Reach_Code, POByZone, VnComments1, VnComments2, TransmitPO, ShipIns)
    			VALUES (@VnId, @Assort, @MDCFlag, @VName, @VAddress1, @VAddress2, @VCity, 
        	    		@VState, @VZip, @VContact, @VPhone, @VFax, @VEMail, @VDocEMail, 
        	    		@Dft_Byid, 'DEFLT', @Dft_LeadTime, @Dft_LeadTime, @Vn_Min, @Vn_Best, 
        	    		@Reach_Code, @POByZone, @VnComments1, @VnComments2, @TransmitPO, @ShipIns)
     		--Increment Add Counter
    		SELECT @InsertCounter = @InsertCounter + 1
		END

    	-- Save the input value for Default Lead Time
	  	SELECT @Dft_LeadTime_Save = @Dft_LeadTime
    	-- IF the Update Vendor Lead Time Option is 'N'o retain the existing leadtime
    	-- IF the Update Vendor Lead Time Option is 'Y'es check for a lead time exception
    	IF @UpdateVnLT = 'N'
    	BEGIN
    		SELECT @Dft_LeadTime = @Last_Dft_LeadTime
    		SELECT @Last_Dft_LeadTime = @Dft_LeadTime_Save
    	END
    	IF @UpdateVnLT = 'Y' and @Last_Dft_LeadTime > 0
    	BEGIN
    		IF cast(abs(@Last_Dft_LeadTime - @Dft_LeadTime) as real)/@Last_Dft_LeadTime > @VnLTPctChgFilter
    			and abs(@Last_Dft_LeadTime - @Dft_LeadTime) > @VnLTDaysChgFilter
    		BEGIN
    			SELECT @Dft_LeadTime = @Last_Dft_LeadTime	-- Reject New Value
    			SELECT @Last_Dft_LeadTime = @Dft_LeadTime_Save
    		END
    	END
    	
        -- Existing Location/Item with reach code = Dollars	
        IF @RowFound <> 0 and (@Existing_Reach_Code = 'D' or @UpdateGoalsOption = 'Y')
        BEGIN
        	-- Update the Vendor Table 
    		UPDATE AIMVendors SET 
				MDCFlag = @MDCFlag, 
				VName = @VName, 
				VAddress1 = @VAddress1, 
				VAddress2 = @VAddress2, 
				VCity = @VCity, 
				VState = @VState, 
				VZip = @VZip, 
				VContact = @VContact, 
				VPhone = @VPhone, 
				VFax = @VFax, 
				VEMail = @VEMail, 
				VDocEMail = @VDocEMail, 
				Dft_LeadTime = @Dft_LeadTime, 
				Last_Dft_LeadTime = @Last_Dft_LeadTime,
				Vn_Min = @Vn_Min, 
				Vn_Best = @Vn_Best, 
				Reach_Code = @Reach_Code, 
				VnComments1 = @VnComments1, 
				VnComments2 = @VnComments2,
				TransmitPO = @TransmitPO,
				ShipIns = @ShipIns	
			WHERE VnId = @VnId
    			and Assort = @Assort
    		-- Increment Update Count	
    		IF @@ROWCOUNT <> 0 
    			SELECT @UpdateCounter = @UpdateCounter + 1
        END
        -- Existing Location/Item with reach code <> Dollars	
        IF @RowFound <> 0 and @Existing_Reach_Code <> 'D' and @UpdateGoalsOption = 'N'
        BEGIN
            -- Update the History Table 
    		UPDATE AIMVendors SET 
    			MDCFlag = @MDCFlag, 
    			VName = @VName, 
    			VAddress1 = @VAddress1, 
                VAddress2 = @VAddress2, 
    			VCity = @VCity, 
    			VState = @VState, 
    			VZip = @VZip, 
    			VContact = @VContact, 
    			VPhone = @VPhone, 
                VFax = @VFax, 
    			VEMail = @VEMail, 
    			VDocEMail = @VDocEMail, 
                Dft_LeadTime = @Dft_LeadTime, 
    			Last_Dft_LeadTime = @Last_Dft_LeadTime,
    			VnComments1 = @VnComments1, 
    			VnComments2 = @VnComments2,	
    			TransmitPO = @TransmitPO,
    			ShipIns = @ShipIns	
			WHERE VnId = @VnId
    			and Assort = @Assort
    	
    		-- Increment Update Count	
    		IF @@ROWCOUNT <> 0 
			BEGIN
    			SELECT @UpdateCounter = @UpdateCounter + 1
        	END
		END
	END		

	-- Deallocate the cursor
	CLOSE DxVn_Cursor
	DEALLOCATE DxVn_Cursor
	
	-- Delete records from the dxvn Table
	TRUNCATE TABLE AIMDxVn
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxVn
	
	--SELECT 'Inserts' = @InsertCounter, 'Updates' = @UpdateCounter
	
	RETURN 1	-- Succeed

END	
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



