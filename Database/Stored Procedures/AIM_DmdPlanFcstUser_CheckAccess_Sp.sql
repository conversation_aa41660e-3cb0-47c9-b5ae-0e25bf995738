/*******************************************************************************/
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstUser_CheckAccess_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstUser_CheckAccess_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstUser_CheckAccess_Sp
**	Desc: Returns AIMCodeLookup.CodeID and CodeDesc for the relevant AccessCode, 
**		given a forecast record and a user ID.
**	Derived from AIM_DmdPlanFcstUserAccess_Validate_Sp and AIM_DmdPlanFcstMainUserAccess_Validate_Sp. 
**	Should replace these stored procedures once AIMFcst.tab is deleted.
**
**	Returns: 
**		1)@rowcount - Can be Zero
**		2) AccessCode/AccessDesc - valid values:
**			0/No Access
**			1/Full Control
-- TO BE DONE!!!
**	Values:  AIMDays
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE PROCEDURE AIM_DmdPlanFcstUser_CheckAccess_Sp
(
	@FcstSetupKey as int,
	@UserID as nvarchar(12),
	@AccessCode as tinyint,
	@HasAccess as bit OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	
	DECLARE @Return_Stat as int

	-- Validate parameters
	IF @FcstSetupKey IS NULL
	OR @UserID IS NULL
	OR @AccessCode IS NULL
	BEGIN
		RETURN -1
	END

	-- Check for SA -- grant complete access	
	IF Upper(@UserID) = 'SA'
	BEGIN
		SET @HasAccess = 1	-- Yes
	END
	ELSE

	-- Get Access Type from AIMFcstAccess
	BEGIN
		SELECT @Return_Stat = COUNT(*)
		FROM AIMFcstAccess
		WHERE 
			AIMFcstAccess.FcstSetupKey =  @FcstSetupKey AND
			AIMFcstAccess.UserID = @UserID AND
		  	AIMFcstAccess.AccessCode = @AccessCode 

		IF @Return_Stat > 0 SET @HasAccess = 1		--True
		ELSE SET @HasAccess = 0	-- False
	END

	IF @@ERROR = 0 RETURN 1
	ELSE RETURN @@ERROR

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


