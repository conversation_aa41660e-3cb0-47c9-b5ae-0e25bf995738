SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_ValidateTransfer_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_ValidateTransfer_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ValidateTransfer_Sp
**	Desc: Checks if transfer can be done without double dipping
**
**	Returns: 
**	Returns: @RtnValue
**	         1) - 0 Can Transfer without double dipping
**               2) -1 - May double dip
**            
**	
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   Sep-29-2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/

CREATE  PROCEDURE AIM_ValidateTransfer_Sp

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

DECLARE @DateDiff AS 				Datetime

select  @DateDiff =(select "ctrldate" =(select max(txndate) from aimdataexchangectrl
where txnset in ('SS','SL'))- COALESCE(sysctrl.lastordergenerationprocess,getdate()) from
sysctrl )

If CAST( @DateDiff as float) < 0 
   BEGIN
     	RETURN -1
   END
Else
   BEGIN
   	RETURN 0
   END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

