SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RepositoryItemAdj_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RepositoryItemAdj_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_RepositoryItemAdj_Get_Sp
**	Desc: Gets Adjustments for a given ReposiotoryKey
**	      Lcid and Item combination
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - Forecast Repository,
**             Recordset - Forecast Repository Detail
**              
**	Auth:   Srinivas Uddanti
**	Date:   06/17/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      
*******************************************************************************/

CREATE PROCEDURE AIM_RepositoryItemAdj_Get_Sp
(
 	@FcstId 	        nvarchar(12),       
       	@LcID     		nvarchar(12),
        @Item   		nvarchar(25)
	
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int
DECLARE @RepositoryKey 	        numeric(18,0)

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstId IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

SELECT @RepositoryKey = RepositoryKey FROM ForecastRepository 
WHERE FcstID = @FcstID 

IF @@RowCount =0 
BEGIN
	RETURN -1
END
select a.adjustbegdate, sum(CASE a.adjusttype
         WHEN 1 THEN adjustqty else 0 END) "adjqty",sum(CASE a.adjusttype
         WHEN 2 THEN adjustqty else 0 END) "adjPct"

from forecastrepository_log a
where a.lcid=@LcId
and a.item =@Item
and a.RepositoryKey =@RepositoryKey
group by a.adjustbegdate

select  b.adjustbegdate, b.adjustqty  "adjoverride", b.overrideenabled from forecastrepository_log b
inner join (select c.adjustbegdate, max(c.fcstadjustkey) "fcstadjustkey"
from forecastrepository_log  c
where c.lcid=@LcId
and c.item =@Item
and c.RepositoryKey =@RepositoryKey
and c.adjusttype=0
group by c.adjustbegdate ) d
 on  b.fcstadjustkey =d.fcstadjustkey


select PeriodBegDate,QtyAdj,QtyAdjPct,QtyAdjOverRide,AdjOverRide
From  AimFcstMaster
Where Lcid =@LcId
and Item  =@Item
Order By PeriodBegDate

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO		


