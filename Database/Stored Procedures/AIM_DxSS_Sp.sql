SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxSS_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxSS_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxSS_Sp
**	Desc: Loads data exchange item records into the Item Table
**            from the AIMDxSS Table.
**
**	Returns:
**		@RowCounter -- number of rows updated
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@PurgedCounter -- number of rows updated
**		@ErrorCounter -- number of rows updated
**		@ReturnCode -- possible values:
**		        1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**                      4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**              
**	Auth:   Randy Sadler
**	Date:   03/12/97
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	Author:	       Description:
**  ---------- -------------- -----------------------------------------------
**  03/24/2000 Randy Sadler   Add logic to handle avgoh
**  08/21/2002 Wade Riza      Updated Return Codes AND Error Handling
**  09/24/2002 Wade Riza      Updated Class Codes to NVARCHAR(50)
**  10/01/2002 Osama Riyahi   @Dft_SSAdj to 9,4 from 3,2
**  05/20/2003	A. Stocksdale  Added AllocatableQty
**  2003/05/28	A. Stocksdale  Replaced bulk insert AND validation commands 
**			       with call to AIM_DxBulkInsert_Sp - common to all
**                             Dx processes.
**  2003/06/19  Srinivas U     Set the UserFcstExpDate to sysdate Issue 1007
**  2003/06/29  Srinivas U     Add a record to AIMDataExchnageCtrl
**  2004/02/25  Srinivas U	Added KitBOMField to the Record
**  2005/03/08  Srinivas U	Added truncate and delete before the bulk insert
*******************************************************************************/
 
CREATE  PROCEDURE AIM_DxSS_Sp
(
	@FileName nvarchar(255) = 'All',
	@RowCounter int OUTPUT,
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT,
	@PurgedCounter int OUTPUT,
	@ErrorCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE 
		--AIMDxSS Table fields
		@SeqNbr int,
		@Status nvarchar(1),
		@LcID nvarchar(12),
		@Item nvarchar(25),
		@ItDesc nvarchar(50),
		@ActDate nvarchar(10),
		@InActDate nvarchar(10),
		@Class1 nvarchar(50),
		@Class2 nvarchar(50),
		@Class3 nvarchar(50),
		@Class4 nvarchar(50),
		@VnId nvarchar(12),
		@Assort nvarchar(12),
		@MDC nvarchar(12),
		@Weight decimal(10,4),
		@Cube decimal(10,4),
		@ListPrice decimal(10,4),
		@Price decimal(10,4),
		@Cost decimal(10,4),
		@UOM nvarchar(6),
		@ConvFactor int,
		@BuyingUOM nvarchar(6),
		@LeadTime smallint,
		@UPC nvarchar(22),
		@Oh int,
		@Oo int,
		@ComStk int,
		@BkOrder int,
		@BkComStk int,
		@StkDate nvarchar(10),
		@BinLocation nvarchar(12),
		@BkQty01 int,
		@BkCost01 decimal(10,4),
		@BkQty02 int,
		@BkCost02 decimal(10,4),
		@BkQty03 int,
		@BkCost03 decimal(10,4),
		@BkQty04 int,
		@BkCost04 decimal(10,4),
		@BkQty05 int,
		@BkCost05 decimal(10,4),
		@BkQty06 int,
		@BkCost06 decimal(10,4),
		@BkQty07 int,
		@BkCost07 decimal(10,4),
		@BkQty08 int,
		@BkCost08 decimal(10,4),
		@BkQty09 int,
		@BkCost09 decimal(10,4),
		@BkQty10 int,
		@BkCost10 decimal(10,4),
		@OnPromotion nvarchar(2),
		@OldUOM nvarchar(6),
		@UOMFact decimal(10,4),
		@OldItem nvarchar(25),
		@NextPONbr_1 nvarchar(12),
		@NextPODate_1 nvarchar(10),
		@NextPOQty_1 int,
		@NextPONbr_2 nvarchar(12),
		@NextPODate_2 nvarchar(10),
		@NextPOQty_2 int,
		@NextPONbr_3 nvarchar(12),
		@NextPODate_3 nvarchar(10),
		@NextPOQty_3 int,
		@UserRef1 nvarchar(12),
		@UserRef2 nvarchar(12),
		@UserRef3 nvarchar(12),
		@Freeze_Forecast nvarchar(1), 
		@AllocatableQty int,
		@KitBOMFlag nvarchar(1),
		@AllocUOM nvarchar (6),
		@AllocConvFactor int,
		
		-- Default Values
		@Dft_ItStat nvarchar(1),
		@Dft_OptionID nvarchar(6),
		@Dft_BuyStrat nvarchar(1),
		@Dft_VelCode nvarchar(1),
		@Dft_ById nvarchar(12),
		@Dft_SaId nvarchar(62),
		@Dft_PmId nvarchar(6),
		@Dft_ReplenCost2 decimal(9,4),
		@Dft_LeadTime smallint,
		@Dft_PackRounding nvarchar(1),
		@Dft_IMin int,
		@Dft_IMax  int,
		@Dft_CStock int,
		@Dft_SSAdj decimal(9,4),
		@Dft_UserMin int,
		@Dft_UserMax int,
		@Dft_UserMethod tinyint,
		@Dft_FcstMethod tinyint,
		@Dft_FcstDemAND decimal(10,2),
		@Dft_UserFcst decimal(10,2),
		@Dft_UserFcstExpDate datetime,
		@Dft_MAE decimal(10,2),
		@Dft_MSE decimal(10,2),
		@Dft_Trend decimal(10,3),
		@Dft_FcstCycles int,
		@Dft_ZeroCount int,
		@Dft_DIFlag nvarchar(1),
		@Dft_DmdFilterFlag nvarchar(1),
		@Dft_TrkSignalFlag nvarchar(1),
		@Dft_UserDemandFlag nvarchar(1),
		@Dft_ByPassPct decimal(4,3),
		@Dft_FcstUpdCyc int,
		@Dft_LTVFact decimal(3,2),
		@Dft_PlnTT nvarchar(1),
		@Dft_ZOPSw nvarchar(1),
		@Dft_OUTLSw nvarchar(1),
		@Dft_ZSStock nvarchar(1),
		@Dft_DSer decimal(4,3),
		@Dft_Freeze_BuyStrat nvarchar(1),
		@Dft_Freeze_Byid nvarchar(1),
		@Dft_Freeze_LeadTime nvarchar(1),
		@Dft_Freeze_OptionID nvarchar(1),
		@Dft_Freeze_DSer nvarchar(1),
		@Dft_Accum_Lt smallint,
		@Dft_ReviewTime smallint,
		@Dft_OrderPt int,
		@Dft_OrderQty int,
		@Dft_SafetyStock decimal(10,2),
		@Dft_FcstRT decimal(10,2), 
		@Dft_FcstLT decimal(10,2),
		@Dft_Fcst_Month decimal(10,2),
		@Dft_Fcst_Quarter decimal(10,2),
		@Dft_Fcst_Year decimal(10,2),
		@Dft_FcstDate datetime,
		@Dft_VC_Amt nvarchar(1),
		@Dft_VC_Units_Ranking int,
		@Dft_VC_Amt_Ranking int,
		@Dft_VC_Date datetime,
		@Dft_VelCode_Prev nvarchar(1),
		@Dft_VC_Amt_Prev nvarchar(1),
		@Dft_OnPromotion nvarchar(1),
		@Dft_AvgOh decimal(10,2),
		@Dft_Freeze_Forecast nvarchar(1),
		@Dft_AllocatableQty int,
		@Dft_AllocUOM nvarchar (6),
		@Dft_AllocConvFactor int,

		-- Item Table
		@Item_LcID nvarchar(12),
		@Item_Item nvarchar(25),
		@Item_ItStat nvarchar(1),
		@Item_AvgOh decimal(10,2),
		@Item_UOM nvarchar(6),
		@Item_LeadTime smallint,

		-- Item Default Values
		@Itdft_SaId nvarchar(62), 
		@Itdft_VelCode nvarchar(1),
		@Itdft_BuyStrat nvarchar(1), 
		@Itdft_ById nvarchar(12), 
		@Itdft_ReplenCost2 decimal(9,4),
		@Itdft_DSer decimal(4,3),
		@Itdft_OptionId nvarchar(6),
		
		-- Location Table
		@LeadTimeSource nvarchar(1),
		@Lc_Dft_ById nvarchar(12),
		
		-- SysCtrl Table
		@ByIdSource nvarchar(1),
		@Calc_Perform tinyint,
		@ClassOption tinyint,
		@AvgInvAlpha decimal(3,2),
		@Unicode_Option nvarchar(10),
		
		-- Vendor Table
		@Vn_Dft_ById nvarchar(12),
		@Vn_Dft_LeadTime smallint,
		
		-- Misc Values
		@Class nvarchar(50),
		@cmd nvarchar(255),		-- CommAND String (T-SQL)
		@dxpath nvarchar(255),		-- Data Exchange Path
		@ItemFound int,
		@Prev_LcID nvarchar(12),
		@retstatus int,
		@RtnCode int,
		@said_hit tinyint,
		@FileTime nvarchar(24)

	-- Set the Nocount option on
	SET NOCOUNT ON
	-- Delete records from the AIMDxSS Table
	TRUNCATE TABLE AIMDxSS
	
	-- Just in case the operator does not have security to run the truncate 
	DELETE FROM AIMDxSS  
	-- This is a data interface transaction <Transaction Code = SS>. 
	-- Bulk Load the <Transaction Table = AIMDxSS> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'SS', 'AIMDxSS', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- AND returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
        SELECT 'Rows' = 0, 
			'Inserts' = 0, 
			'Updates' = 0, 
			'Purged' = 0, 
			'Errors' = 0
 	RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- Initialize counters
	SELECT @InsertCounter = 0, 
		@UpdateCounter = 0, 
		@RowCounter = 0, 
		@PurgedCounter = 0, 
		@ErrorCounter = 0
	
	-- Initialize Previous Location Id
	SELECT @Prev_LcID = ''
	
	-- Initialize default values from SysCtrl
	SELECT 
		@ByIdSource = ByIdSource, 
		@Calc_Perform = Calc_Perform, 
		@ClassOption = ClassOption,
		@AvgInvAlpha = AvgInvAlpha 
	FROM SysCtrl
	
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	-- Initialize default values from Item Default Record
	SELECT @Dft_ItStat = ItStat, @Dft_OptionID = OptionId, @Dft_BuyStrat = BuyStrat, @Dft_VelCode = VelCode, 
        @Dft_ById = ById, @Dft_SaId = SaId, @Dft_PmId = PmId, @Dft_ReplenCost2 = ReplenCost2, 
        @Dft_LeadTime = LeadTime, @Dft_PackRounding = PackRounding, @Dft_IMin = IMin, @Dft_IMax = IMax, 
        @Dft_CStock = CStock, @Dft_SSAdj = SSAdj, @Dft_UserMin = UserMin, @Dft_UserMax = UserMax, 
        @Dft_UserMethod = UserMethod, @Dft_FcstMethod = FcstMethod, @Dft_FcstDemAND = FcstDemand, 
        @Dft_UserFcst = UserFcst, @Dft_UserFcstExpDate = UserFcstExpDate, @Dft_MAE = MAE, @Dft_MSE = MSE, 
        @Dft_Trend = Trend, @Dft_FcstCycles = FcstCycles, @Dft_ZeroCount = ZeroCount, @Dft_DIFlag = DIFlag, 
        @Dft_DmdFilterFlag = DmdFilterFlag, @Dft_TrkSignalFlag = TrkSignalFlag, @Dft_UserDemandFlag = UserDemandFlag, 
        @Dft_ByPassPct = ByPassPct, @Dft_FcstUpdCyc = FcstUpdCyc, @Dft_LTVFact = LTVFact, 
        @Dft_PlnTT = PlnTT, @Dft_ZOPSw = ZOPSw, @Dft_OUTLSw = OUTLSw, @Dft_ZSStock = ZSStock, @Dft_DSer = DSer, 
        @Dft_Freeze_BuyStrat = Freeze_BuyStrat, @Dft_Freeze_Byid = Freeze_ById, 
        @Dft_Freeze_LeadTime = Freeze_LeadTime, @Dft_Freeze_OptionID = Freeze_OptionID, 
        @Dft_Freeze_DSer = Freeze_DSer, @Dft_Accum_Lt = Accum_Lt, 
        @Dft_ReviewTime = ReviewTime, @Dft_OrderPt = OrderPt, @Dft_OrderQty = OrderQty, 
        @Dft_SafetyStock = SafetyStock, @Dft_FcstRT = FcstRT, @Dft_FcstLT = FcstLT, 
        @Dft_Fcst_Month = Fcst_Month, @Dft_Fcst_Quarter = Fcst_Quarter, @Dft_Fcst_Year = Fcst_Year, 
        @Dft_FcstDate = FcstDate, @Dft_VC_Amt = VC_Amt, @Dft_VC_Units_Ranking = VC_Units_Ranking, 
        @Dft_VC_Amt_Ranking = VC_Amt_Ranking, @Dft_VC_Date = VC_Date, @Dft_VelCode_Prev = VelCode_Prev, 
        @Dft_VC_Amt_Prev = VC_Amt_Prev, @Dft_OnPromotion = OnPromotion, @Dft_AvgOh = AvgOh,
		@Dft_Freeze_Forecast = Freeze_Forecast, @Dft_AllocatableQty = AllocatableQty,
		@Dft_AllocUOM = AllocUOM, @Dft_AllocConvFactor = AllocConvFactor
    FROM Item
    WHERE LcID = 'DEFAULT'
    AND Item = 'DEFAULT'

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	--Override the @Dft_UserFcstExpDate value read above with sysdate
	SELECT @Dft_UserFcstExpDate = CONVERT(nvarchar(10), GETDATE(), 101)

    DECLARE DxSS_Cursor CURSOR LOCAL FAST_FORWARD FOR
         SELECT 
			SeqNbr, Status, LcID, Item, ItDesc, 
			ISNULL(ActDate, CONVERT(nvarchar(10), GETDATE(), 101)), 
            ISNULL(InActDate, '12/31/9999'), 
            ISNULL(Class1, ''), ISNULL(Class2, ''), ISNULL(Class3, ''), ISNULL(Class4, ''), 
			ISNULL(VnId, ''), ISNULL(Assort, ''), ISNULL(MDC, ''), 
			ISNULL(Weight, 0.0), ISNULL(Cube, 0.0), 
			ISNULL(ListPrice, 0.0), ISNULL(Price, 0.0), 
			ISNULL(Cost, 0.0), RTRIM(UOM), 
            ISNULL(ConvFactor, 0), ISNULL(rtrim(BuyingUOM), ''), 
			ISNULL(LeadTime, 0), ISNULL(UPC, ''), 
			ISNULL(Oh, 0), ISNULL(Oo, 0), ISNULL(ComStk, 0), ISNULL(BkOrder, 0), 
            ISNULL(BkComStk, 0), ISNULL(StkDate, '01/01/1990'), ISNULL(BinLocation, ''), 
			ISNULL(BkQty01, 0), ISNULL(BkCost01, 0.0), 
			ISNULL(BkQty02, 0), ISNULL(BkCost02, 0.0), 
			ISNULL(BkQty03, 0), ISNULL(BkCost03, 0.0), 
			ISNULL(BkQty04, 0), ISNULL(BkCost04, 0.0), 
			ISNULL(BkQty05, 0), ISNULL(BkCost05, 0.0), 
			ISNULL(BkQty06, 0), ISNULL(BkCost06, 0.0), 
			ISNULL(BkQty07, 0), ISNULL(BkCost07, 0.0), 
			ISNULL(BkQty08, 0), ISNULL(BkCost08, 0.0), 
			ISNULL(BkQty09, 0), ISNULL(BkCost09, 0.0), 
			ISNULL(BkQty10, 0), ISNULL(BkCost10, 0.0), 
			ISNULL(OnPromotion, ''), ISNULL(OldUOM, ''), ISNULL(UOMFact, 0.0), ISNULL(OldItem, ''), 
            ISNULL(NextPONbr_1, ''), ISNULL(NextPODate_1, '01/01/1990'), ISNULL(NextPOQty_1, 0), 
            ISNULL(NextPONbr_2, ''), ISNULL(NextPODate_2, '01/01/1990'), ISNULL(NextPOQty_2, 0), 
            ISNULL(NextPONbr_3, ''), ISNULL(NextPODate_3, '01/01/1990'), ISNULL(NextPOQty_3, 0), 
            ISNULL(UserRef1, ''), ISNULL(UserRef2, ''), ISNULL(UserRef3, ''), ISNULL(Freeze_Forecast, 'N'), 
            ISNULL(AllocatableQty, 0), ISNULL(KitBOMFlag,'N'),
			ISNULL(AllocUOM, 'EA'), ISNULL(AllocConvFactor, 1)
        FROM AIMDxSS
        ORDER BY LcID, SeqNbr
    OPEN DxSS_Cursor
	BEGIN TRANSACTION
    WHILE 1 = 1
    BEGIN  --BEGIN cursor loop
        -- Get the next record from the cursor  
        FETCH NEXT FROM DxSS_Cursor INTO
            @SeqNbr, @Status, @LcID, @Item, @ItDesc, @ActDate, @InActDate, @Class1, @Class2, @Class3, @Class4, 
            @VnId, @Assort, @MDC, @Weight, @Cube, @ListPrice, @Price, @Cost, @UOM, @ConvFactor, @BuyingUOM, @LeadTime, 
            @UPC, @Oh, @Oo, @ComStk, @BkOrder, @BkComStk, @StkDate, @BinLocation, @BkQty01, @BkCost01, @BkQty02, 
            @BkCost02, @BkQty03, @BkCost03, @BkQty04, @BkCost04, @BkQty05, @BkCost05, @BkQty06, @BkCost06, @BkQty07, 
            @BkCost07, @BkQty08, @BkCost08, @BkQty09, @BkCost09, @BkQty10, @BkCost10, @OnPromotion, @OldUOM, @UOMFact, @OldItem, 
            @NextPONbr_1, @NextPODate_1, @NextPOQty_1, @NextPONbr_2, @NextPODate_2, @NextPOQty_2, @NextPONbr_3, 
            @NextPODate_3, @NextPOQty_3, @UserRef1, @UserRef2, @UserRef3, @Freeze_Forecast, @AllocatableQty, @KitBOMFlag,
			@AllocUOM, @AllocConvFactor
        -- Check for end of cursor
        IF @@fetch_status <> 0 
		BEGIN
            BREAK
		END
        -- IF the LcID changes; update the stock date in the location table
        IF @LcID <> @Prev_LcID
        BEGIN
            SELECT @Lc_Dft_ById = Dft_ById, @LeadTimeSource = LeadTimeSource 
            FROM AIMLocations 
            WHERE LcID = @LcID
                
			UPDATE AIMLocations
			SET StkDate = @StkDate 
			WHERE LcID = @LcID
            
		    SELECT @Prev_LcID = @LcID
        END
        -- Check for ConvFactor = 0; IF 0 (which is an invalid value), change it to 1.
        IF @ConvFactor = 0 
		BEGIN
            SELECT @ConvFactor = 1
	    END        
        IF @BuyingUOM = ''
		BEGIN
            SELECT @BuyingUOM = @UOM
		END            
        -- Check dates
		IF ISDATE(@ActDate) = 0         -- Invalid Date
		BEGIN
			SELECT @Actdate = CONVERT(nvarchar(10), GETDATE(), 101)
		END    
        IF ISDATE(@InActDate) = 0       -- Invalid Date
		BEGIN
			SELECT @InActDate = '12/31/9999'
		END        
        IF ISDATE(@StkDate) = 0         -- Invalid Date
		BEGIN
          SELECT @Stkdate = CONVERT(nvarchar(10), GETDATE(), 101)
		END
		-- Check for the existence of the item; adjust itemfound status accordingly
        SELECT 
			@Item_LcID = '', 
			@Item_Item = '', 
			@Item_ItStat = '', 
			@Item_UOM = '', 
			@Item_LeadTime = 0
        
		SELECT 
			@Item_LcID = LcID, 
			@Item_Item = Item, 
			@Item_ItStat = ItStat, 
			@Item_AvgOh = AvgOh, 
            @Item_UOM = UOM, 
			@Item_LeadTime = LeadTime
        FROM Item
        WHERE LcID = @LcID
        AND Item = @Item
        -- Set the Item Found Status Code WHERE 0 = Item not found, 1 = Item found
        SELECT @ItemFound = @@ROWCOUNT
        -- IF the item was not found in the Item Table AND the transaction has a 
        -- status of Inactive OR Purge AND a zero on-hAND quantity, THEN do not insert 
	    -- a new record; increment purge counter AND row counter

        IF @ItemFound = 0 
		AND (@Status = 'P' OR (@Status = 'I' AND @Oh = 0)) 
        BEGIN            
			SET @PurgedCounter = @PurgedCounter + 1
			SET @RowCounter = @RowCounter + 1

            CONTINUE
        END

		-- Increment the rowcount counter
		SELECT @RowCounter = @RowCounter + 1
		-- Check for an Item Substitution -- IF true Acquire History from Old Item
		IF @ItemFound = 0 
		AND @OldItem <> '' 
		AND @OldItem <> @Item 
		AND EXISTS (SELECT LcID, Item FROM Item WHERE LcID = @LcID AND Item = @OldItem)
		BEGIN
            -- EXECUTE the AIM Item Number Change Stored Procedure
            EXECUTE AIM_ItmChg_Sp @LcID, @Item, @OldItem
                        
            -- Since the new item now exists reread the Item Table
            SELECT 
				@Item_LcID = '', 
				@Item_Item = '', 
				@Item_ItStat = '', 
				@Item_UOM = '', 
				@Item_LeadTime = 0
            
			SELECT @Item_LcID = LcID, 
				@Item_Item = Item, 
				@Item_ItStat = ItStat, 
				@Item_AvgOh = AvgOh, 
                @Item_UOM = UOM, 
				@Item_LeadTime = LeadTime
            FROM Item
            WHERE LcID = @LcID
            AND Item = @Item
                    
            -- Set the Item Found Status Code WHERE 0 = Item not found, 1 = Item found
            SELECT @ItemFound = @@ROWCOUNT 
        END
        -- Set the Item status code (@Status = Status in AIMDxSS, @Item_ItStat = Status in AIM Item Table)
        SELECT @Status = 
        CASE
			WHEN @ItemFound = 0 
				AND @Status IN ('A', 'U') THEN 'U'
			WHEN @ItemFound = 0 
				AND @Status IN ('C', 'D', 'I', 'P', 'X') THEN @Status
			WHEN @ItemFound = 1 
				AND @Status IN ('A', 'U')
				AND @Item_ItStat IN ('A', 'F', 'H', 'N', 'T', 'U') THEN @Item_ItStat
			WHEN @ItemFound = 1 
				AND @Status = 'A'
				AND @Item_ItStat IN ('C', 'D', 'I', 'P', 'X') THEN 'A'
			WHEN @ItemFound = 1 
				AND @Status IN ('C', 'D', 'I', 'P', 'X') THEN @Status
			ELSE @Status
        END 
            
        IF @ItemFound = 0                               -- Insert a New Item into the Item Table
        BEGIN
            -- Determine which class code is used to key the Item Default Table
            SELECT @Class = 
            CASE @ClassOption
            WHEN 1 THEN @Class1
            WHEN 2 THEN @Class2
            WHEN 3 THEN @Class3
            WHEN 4 THEN @Class4
            END
        
			-- Get the default values from the Item Default Table (ItDefaults)
			SELECT @said_hit = 0
         
            SELECT @Itdft_SaId = SaId, @Itdft_VelCode = VelCode, @Itdft_BuyStrat = BuyStrat, 
                @Itdft_ById = ById, @Itdft_ReplenCost2 = ReplenCost2, @Itdft_Dser = DSer, 
                @Itdft_OptionId = OptionId 
            FROM ItDefaults 
            WHERE LcID = @LcID
            AND Class = @Class        

            IF @@ROWCOUNT <> 0
            BEGIN
                -- Override Item Level Defaults
                SELECT @Dft_SaId = @ItDft_SaId, @Dft_VelCode = @Itdft_VelCode, @Dft_VC_Amt = @Itdft_VelCode,
                    @Dft_BuyStrat = @Itdft_BuyStrat, @Dft_ById = @Itdft_ById, @Dft_ReplenCost2 = @Itdft_ReplenCost2, 
                    @Dft_Dser = @Itdft_Dser, @Dft_OptionId = @Itdft_OptionId 
            END
            
            -- Check for Buyer Id Overrides in the AIMLocations AND AIMVendors Tables
            IF @ByIdSource = 'L'  
			BEGIN
                SELECT @Dft_ById = ISNULL(@Lc_Dft_ById, '')
	        END    
            IF @ByIdSource = 'V' OR @LeadTimeSource = 'V'
            BEGIN
                SELECT @Vn_Dft_ById = Dft_ById, @Vn_Dft_LeadTime = Dft_LeadTime 
                FROM AIMVendors 
                WHERE VnId = @VnId AND Assort = @Assort
                
                IF @ByIdSource = 'V'
				BEGIN
					SELECT @Dft_ById = ISNULL(@Vn_Dft_ById, '')
				END
				IF @LeadTimeSource = 'V'
				BEGIN
					SELECT @LeadTime = ISNULL(@Vn_Dft_LeadTime, 0)
				END
            END
            
	        -- Insert the new item into the Item Table
			IF @Vnid = NULL 
			BEGIN
		    	SELECT @LcID, @item, @vnid, @assort
		    END        

            INSERT INTO Item(LcID, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, Class3, 
                Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, SaId, PmId, UPC, 
                Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, 
                BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, 
                BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, 
                ReplenCost2, Oh, Oo, ComStk, BkOrder, BkComStk, LeadTime, PackRounding, IMin, IMax, 
                CStock, SSAdj, UserMin, UserMax, UserMethod, FcstMethod, FcstDemand, UserFcst, 
                UserFcstExpDate, MAE, MSE, Trend, FcstCycles, ZeroCount, DIFlag, DmdFilterFlag, 
                TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, PlnTT, ZOPSw, OUTLSw, 
                ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, 
                Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, 
                FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, 
                VC_Amt_Ranking, VC_Date, VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, 
                NextPODate_1, NextPOQty_1, NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, 
                NextPODate_3, NextPOQty_3, UserRef1, UserRef2, UserRef3, Freeze_Forecast, AllocatableQty,
				KitBOMFlag, AllocUOM, AllocConvFactor
			) VALUES (
                @LcID, @Item, @ItDesc, @Status, @ActDate, @InActDate, @Dft_OptionID, @Class1, 
                @Class2, @Class3, @Class4, @BinLocation, @Dft_BuyStrat, @Dft_VelCode, 
                @VnId, @Assort, @Dft_ById, @MDC, @Dft_SaId, @Dft_PmId, @UPC, @Weight, 
                @Cube, @ListPrice, @Price, @Cost, @BkQty01, @BkCost01, 
                @BkQty02, @BkCost02, @BkQty03, @BkCost03, @BkQty04, @BkCost04, 
                @BkQty05, @BkCost05, @BkQty06, @BkCost06, @BkQty07, @BkCost07, 
                @BkQty08, @BkCost08, @BkQty09, @BkCost09, @BkQty10, @BkCost10, 
                @UOM, @ConvFactor, @BuyingUOM, @Dft_ReplenCost2, @Oh, @Oo, @ComStk, 
                @BkOrder, @BkComStk, @LeadTime, @Dft_PackRounding, @Dft_IMin, @Dft_IMax, @Dft_CStock, 
                @Dft_SSAdj, @Dft_UserMin, @Dft_UserMax, @Dft_UserMethod, @Dft_FcstMethod, @Dft_FcstDemand, 
                @Dft_UserFcst, @Dft_UserFcstExpDate, @Dft_MAE, @Dft_MSE, @Dft_Trend, @Dft_FcstCycles, 
                @Dft_ZeroCount, @Dft_DIFlag, @Dft_DmdFilterFlag, @Dft_TrkSignalFlag, @Dft_UserDemandFlag, 
                @Dft_ByPassPct, @Dft_FcstUpdCyc, @Dft_LTVFact, @Dft_PlnTT, @Dft_ZOPSw, @Dft_OUTLSw, 
                @Dft_ZSStock, @Dft_DSer, @Dft_Freeze_BuyStrat, @Dft_Freeze_Byid, @Dft_Freeze_LeadTime, 
                @Dft_Freeze_OptionID, @Dft_Freeze_DSer, @OldItem, @Dft_Accum_Lt, @Dft_ReviewTime, 
                @Dft_OrderPt, @Dft_OrderQty, @Dft_SafetyStock, @Dft_FcstRT, @Dft_FcstLT, @Dft_Fcst_Month, 
                @Dft_Fcst_Quarter, @Dft_Fcst_Year, @Dft_FcstDate, @Dft_VC_Amt, @Dft_VC_Units_Ranking, 
                @Dft_VC_Amt_Ranking, @Dft_VC_Date, @Dft_VelCode_Prev, @Dft_VC_Amt_Prev, @OnPromotion, 
                @Oh, @NextPONbr_1, @NextPODate_1, @NextPOQty_1, @NextPONbr_2, 
                @NextPODate_2, @NextPOQty_2, @NextPONbr_3, @NextPODate_3, @NextPOQty_3, 
                @UserRef1, @UserRef2, @UserRef3, @Freeze_Forecast, @AllocatableQty, 
				@KitBOMFlag, @AllocUOM, @AllocConvFactor
			)
            
            IF @@ROWCOUNT > 0 --Successful insert; increment insert counter
            BEGIN
				SELECT @InsertCounter = @InsertCounter + 1  
				EXECUTE AIM_CostPriceListPrice_Save_Sp @LcID,@Item,
					@Price,@Cost,@ListPrice,@StkDate  
			END     
			ELSE --Insert failed; increment error counter
				SELECT @ErrorCounter = @ErrorCounter + 1
		END

        --IF the item was found (itemfound = 1), update the Item Table 
        IF @ItemFound = 1  
        BEGIN   
            -- Check for a unit of measure change  
            IF @UOM <> @Item_UOM 
			AND @UOM <> @OldUOM 
			AND @OldUOM <> '' 
			AND @UOMFact > 0
            BEGIN
                EXECUTE AIM_UOMChg_Sp @LcID, @Item, @UOM, @OldUOM, @UOMFact            
                
                -- Retreive the updated Average On HAND Balance
                SELECT @Item_AvgOh = AvgOh
                    FROM Item
                    WHERE LcID = @LcID
                    AND Item = @Item
            END
       
            -- Determine the correct handling for the leadtime. IF the Lead Time Source is 'S' = Stock Status
            -- retain the leadtime value from the AIMDxSS Table. For any other value retain the leadtime 
            -- currently in the Item Table (@Item_LeadTime)
            IF @LeadTimeSource <> 'S'
			BEGIN
                SELECT @LeadTime = @Item_LeadTime
	        END
        
            -- Update the Average Oh Balance
            SELECT @Dft_AvgOh = (@AvgInvAlpha * @Oh) + ((1 - @AvgInvAlpha) * @Item_AvgOh)
            
            -- Check for an inconsistent inactive date
            IF @Status NOT IN ('D', 'I', 'P', 'X') 
			BEGIN
                SELECT @InActDate = '12/31/9999'
	        END
    
            UPDATE Item SET 
                ItStat = @Status, ItDesc = @ItDesc, InActDate = @InActDate, Class1 = @Class1, 
                Class2 = @Class2, Class3 = @Class3, Class4 = @Class4, VnId = @VnId, Assort = @Assort, 
                MDC = @MDC, Weight = @Weight, Cube = @Cube, ListPrice = @ListPrice, Price = @Price, 
                Cost = @Cost, UOM = @UOM, ConvFactor = @ConvFactor, BuyingUOM = @BuyingUOM, 
                LeadTime = @LeadTime, UPC = @UPC, Oh = @Oh, Oo = @Oo, ComStk = @ComStk, 
                BkOrder = @BkOrder, BkComStk = @BkComStk, BinLocation = @BinLocation, BkQty01 = @BkQty01, 
                BkCost01 = @BkCost01, BkQty02 = @BkQty02, BkCost02 = @BkCost02, BkQty03 = @BkQty03, 
                BkCost03 = @BkCost03, BkQty04 = @BkQty04, BkCost04 = @BkCost04, BkQty05 = @BkQty05, 
                BkCost05 = @BkCost05, BkQty06 = @BkQty06, BkCost06 = @BkCost06, BkQty07 = @BkQty07, 
                BkCost07 = @BkCost07, BkQty08 = @BkQty07, BkCost08 = @BkCost08, BkQty09 = @BkQty09, 
                BkCost09 = @BkCost09, BkQty10 = @BkQty10, BkCost10 = @BkCost10, OnPromotion = @OnPromotion,
                OldItem = @OldItem, NextPONbr_1 = @NextPONbr_1, NextPODate_1 = @NextPODate_1, 
                NextPOQty_1 = @NextPOQty_1, NextPONbr_2 = @NextPONbr_2, NextPODate_2 = @NextPODate_2, 
                NextPOQty_2 = @NextPOQty_2, NextPONbr_3 = @NextPONbr_3, NextPODate_3 = @NextPODate_3, 
                NextPOQty_3 = @NextPOQty_3, UserRef1 = @UserRef1, UserRef2 = @UserRef2, UserRef3 = @UserRef3,
                AvgOh = @Dft_AvgOh, Freeze_Forecast = @Freeze_Forecast, AllocatableQty = @AllocatableQty,
				KitBOMFlag = @KitBOMFlag, AllocUOM = @AllocUOM, AllocConvFactor = @AllocConvFactor
            WHERE LcID = @LcID
            AND Item = @Item
                
			IF @@ROWCOUNT > 0 -- Successful UPDATE; Increment the updcount counter
			BEGIN
				SELECT @UpdateCounter = @UpdateCounter + 1   
				EXECUTE AIM_CostPriceListPrice_Save_Sp @LcID,
					@Item,@Price,@Cost,@ListPrice,@StkDate  
			END      
			ELSE  -- Update failed; increment error counter
			BEGIN 
				SELECT @ErrorCounter = @ErrorCounter + 1
			END   
        END
	END

	CLOSE DxSS_Cursor 
	DEALLOCATE DxSS_Cursor
	COMMIT TRANSACTION

	-- Delete records from the AIMDxSS Table
	TRUNCATE TABLE AIMDxSS
	
	-- Just in case the operator does not have security to run the truncate 
	DELETE FROM AIMDxSS  
	
	--Write a record to AIMDataExchangeCtrl to track when it is write
	-- Used in transfermanagement to check double dipping
	SET @FileTime =  'SS '+ STR(CAST(GETDATE() AS float), 12, 6)
	INSERT INTO AIMDataExchangeCtrl VALUES ('SS', @FileTime, GETDATE())
	
	UPDATE STATISTICS Item WITH RESAMPLE, ALL

	RETURN 1	-- Succeed

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

