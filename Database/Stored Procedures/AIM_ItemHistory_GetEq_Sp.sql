SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHistory_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHistory_GetEq_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemHistory_GetEq_Sp
**	Desc: Updates the Item table with data from the override.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 	09/12/2003 Srinivas U	Modified code for SubsItem
*******************************************************************************/
     
CREATE PROCEDURE AIM_ItemHistory_GetEq_Sp
(
	@LcIdKey		 			nvarchar(12),
	@ItemKey 					nvarchar(25),
        @StartYear			   		int,
        @EndYear			 		int,
        @DemandSource  				        nvarchar(1)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rowcount					int
  
SET NOCOUNT ON
 
IF @DemandSource = 'S'
BEGIN
       SELECT LcId, Item, HisYear, 
           dmd01 = CPS01, 
           dmd02 = CPS02, 
           dmd03 = CPS03, 
           dmd04 = CPS04, 
           dmd05 = CPS05, 
           dmd06 = CPS06, 
           dmd07 = CPS07, 
           dmd08 = CPS08, 
           dmd09 = CPS09, 
           dmd10 = CPS10, 
           dmd11 = CPS11, 
           dmd12 = CPS12, 
           dmd13 = CPS13, 
           dmd14 = CPS14, 
           dmd15 = CPS15, 
           dmd16 = CPS16, 
           dmd17 = CPS17, 
           dmd18 = CPS18, 
           dmd19 = CPS19, 
           dmd20 = CPS20, 
           dmd21 = CPS21, 
           dmd22 = CPS22, 
           dmd23 = CPS23, 
           dmd24 = CPS24, 
           dmd25 = CPS25, 
           dmd26 = CPS26, 
           dmd27 = CPS27, 
           dmd28 = CPS28, 
           dmd29 = CPS29, 
           dmd30 = CPS30, 
           dmd31 = CPS31, 
           dmd32 = CPS32, 
           dmd33 = CPS33, 
           dmd34 = CPS34, 
           dmd35 = CPS35, 
           dmd36 = CPS36, 
           dmd37 = CPS37, 
           dmd38 = CPS38, 
           dmd39 = CPS39, 
           dmd40 = CPS40, 
           dmd41 = CPS41, 
           dmd42 = CPS42, 
           dmd43 = CPS43, 
           dmd44 = CPS44, 
           dmd45 = CPS45, 
           dmd46 = CPS46, 
           dmd47 = CPS47, 
           dmd48 = CPS48, 
           dmd49 = CPS49, 
           dmd50 = CPS50, 
           dmd51 = CPS51, 
           dmd52 = CPS52
       FROM ItemHistory
       WHERE lcid = @LcIdKey
       AND item = @ItemKey
       AND SubsItem =@ItemKey
       AND HisYear BETWEEN @StartYear 
       AND @EndYear
       
       -- ORDER BY HisYear desc
       SELECT @rowcount = @@rowcount
END
IF @DemandSource = 'O'
BEGIN
       SELECT LcId, Item, HisYear, 
           dmd01 = QtyOrd01, 
           dmd02 = QtyOrd02, 
           dmd03 = QtyOrd03, 
           dmd04 = QtyOrd04, 
           dmd05 = QtyOrd05, 
           dmd06 = QtyOrd06, 
           dmd07 = QtyOrd07, 
           dmd08 = QtyOrd08, 
           dmd09 = QtyOrd09, 
           dmd10 = QtyOrd10, 
           dmd11 = QtyOrd11, 
           dmd12 = QtyOrd12, 
           dmd13 = QtyOrd13, 
           dmd14 = QtyOrd14, 
           dmd15 = QtyOrd15, 
           dmd16 = QtyOrd16, 
           dmd17 = QtyOrd17, 
           dmd18 = QtyOrd18, 
           dmd19 = QtyOrd19, 
           dmd20 = QtyOrd20, 
           dmd21 = QtyOrd21, 
           dmd22 = QtyOrd22, 
           dmd23 = QtyOrd23, 
           dmd24 = QtyOrd24, 
           dmd25 = QtyOrd25, 
           dmd26 = QtyOrd26, 
           dmd27 = QtyOrd27, 
           dmd28 = QtyOrd28, 
           dmd29 = QtyOrd29, 
           dmd30 = QtyOrd30, 
           dmd31 = QtyOrd31, 
           dmd32 = QtyOrd32, 
           dmd33 = QtyOrd33, 
           dmd34 = QtyOrd34, 
           dmd35 = QtyOrd35, 
           dmd36 = QtyOrd36, 
           dmd37 = QtyOrd37, 
           dmd38 = QtyOrd38, 
           dmd39 = QtyOrd39, 
           dmd40 = QtyOrd40, 
           dmd41 = QtyOrd41, 
           dmd42 = QtyOrd42, 
           dmd43 = QtyOrd43, 
           dmd44 = QtyOrd44, 
           dmd45 = QtyOrd45, 
           dmd46 = QtyOrd46, 
           dmd47 = QtyOrd47, 
           dmd48 = QtyOrd48, 
           dmd49 = QtyOrd49, 
           dmd50 = QtyOrd50, 
           dmd51 = QtyOrd51, 
           dmd52 = QtyOrd52
       FROM ItemHistory
       WHERE lcid = @LcIdKey
       AND item = @ItemKey
       AND SubsItem =@ItemKey
       AND HisYear BETWEEN @StartYear AND @EndYear

       -- ORDER BY HisYear desc
       SELECT @rowcount = @@rowcount
END
IF @DemandSource = 'B'
BEGIN
       SELECT LcId, Item, HisYear, 
           dmd01 = Sum(CPS01), 
           dmd02 = Sum(CPS02), 
           dmd03 = Sum(CPS03), 
           dmd04 = Sum(CPS04), 
           dmd05 = Sum(CPS05), 
           dmd06 = Sum(CPS06), 
           dmd07 = Sum(CPS07), 
           dmd08 = Sum(CPS08), 
           dmd09 = Sum(CPS09), 
           dmd10 = Sum(CPS10), 
           dmd11 = Sum(CPS11), 
           dmd12 = Sum(CPS12), 
           dmd13 = Sum(CPS13), 
           dmd14 = Sum(CPS14), 
           dmd15 = Sum(CPS15), 
           dmd16 = Sum(CPS16), 
           dmd17 = Sum(CPS17), 
           dmd18 = Sum(CPS18), 
           dmd19 = Sum(CPS19), 
           dmd20 = Sum(CPS20), 
           dmd21 = Sum(CPS21), 
           dmd22 = Sum(CPS22), 
           dmd23 = Sum(CPS23), 
           dmd24 = Sum(CPS24), 
           dmd25 = Sum(CPS25), 
           dmd26 = Sum(CPS26), 
           dmd27 = Sum(CPS27), 
           dmd28 = Sum(CPS28), 
           dmd29 = Sum(CPS29), 
           dmd30 = Sum(CPS30), 
           dmd31 = Sum(CPS31), 
           dmd32 = Sum(CPS32), 
           dmd33 = Sum(CPS33), 
           dmd34 = Sum(CPS34), 
           dmd35 = Sum(CPS35), 
           dmd36 = Sum(CPS36), 
           dmd37 = Sum(CPS37), 
           dmd38 = Sum(CPS38), 
           dmd39 = Sum(CPS39), 
           dmd40 = Sum(CPS40), 
           dmd41 = Sum(CPS41), 
           dmd42 = Sum(CPS42), 
           dmd43 = Sum(CPS43), 
           dmd44 = Sum(CPS44), 
           dmd45 = Sum(CPS45), 
           dmd46 = Sum(CPS46), 
           dmd47 = Sum(CPS47), 
           dmd48 = Sum(CPS48), 
           dmd49 = Sum(CPS49), 
           dmd50 = Sum(CPS50), 
           dmd51 = Sum(CPS51), 
           dmd52 = Sum(CPS52)
       FROM ItemHistory
       WHERE lcid = @LcIdKey
       AND item = @ItemKey
       AND HisYear BETWEEN @StartYear 
       AND @EndYear
	Group by LcId, Item, HisYear
	Order by LcId, Item, HisYear
       
       -- ORDER BY HisYear desc
       SELECT @rowcount = @@rowcount
END


RETURN  @rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

