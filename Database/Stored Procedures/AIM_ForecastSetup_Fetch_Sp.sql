SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastSetup_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastSetup_Fetch_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastSetup_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_Forecast_GetKey_Sp)
**
**	Parameters:
**		FcstID == a valid AIMFcst.Forecast ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**		Related == Determines of the stored procedure should return related forecast setup information
**			0 = False -- only fetch FROM AIMFcstSetup
**			1 = True -- fetch FROM AIMFcstSetup and dependents
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	---------------------------------------
*******************************************************************************/

CREATE    PROCEDURE AIM_ForecastSetup_Fetch_Sp 
(
	@FcstID as nvarchar(12) = '', 
	@UserID as nvarchar(12),
	@Action as tinyint = 0,
	@Related as bit, 
	@LangID as nvarchar(20)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat as int
	DECLARE @FcstSetupKey as int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @Action IS NULL
	BEGIN
		RETURN -1
	END
	
	
	-- Get the key to the Forecast Record based on the action code
	IF @Action = 0		-- Get Equal
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID = @FcstID

		SET @Return_Stat = @@ROWCOUNT
	END
	IF @Action = 1		-- Get Greater Than
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID > @FcstID
		ORDER BY FcstID ASC
		
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
		BEGIN
			SET @Action = 6	-- Get Last Record
		END
	END
	IF @Action = 2		-- Get Less Than 
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID < @FcstID
		ORDER BY FcstID DESC
		
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
		BEGIN
			SET @Action = 5      -- Get first record
		END
	END
	IF @Action = 3		-- Get Greater Than or Equal
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID >= @FcstID
		ORDER BY FcstID ASC
	    	
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
	      BEGIN
		      SET @Action = 6      -- Get last record
		END
	END
	IF @Action = 4		-- Get Less Than or Equal
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID <= @FcstID
		ORDER BY FcstID DESC

		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
	      BEGIN
		      SET @Action = 5      -- Get first record
		END
	END

	IF @Action = 5		-- Get First
	BEGIN
		SELECT TOP 1
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup  
		ORDER BY FcstID, FcstSetupKey

		SET @Return_Stat = @@ROWCOUNT
	END
	IF @Action = 6		-- Get Last
	BEGIN
		SELECT TOP 1
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup  
		ORDER BY FcstID DESC 
		
		SET @Return_Stat = @@ROWCOUNT
	END


	-- set return recordset(s) and status value
	IF @Return_Stat > 0 
	BEGIN
	    	SELECT FcstSetupKey, 
			FcstID, FcstDesc, FcstHierarchy,
			FcstEnabled, FcstLocked, 
			FcstStartDate, LastUpdated, 
			FcstRolling, FcstHistory,
			FcstPds_Future, FcstPds_Historical,
			FreezePds,
			FixedPds,
			FcstInterval,
			FcstUnit, 
			ApplyTrend,
			Calc_SysFcst,
			Calc_NetReq,
			Calc_HistDmd,
			Calc_MasterFcstAdj,
			Calc_FcstAdj,
			Calc_AdjNetReq,
			Calc_ProjInv,
			Calc_ProdConst,
			Item = ISNULL(Item, ''),
			ItStat = ISNULL(ItStat, ''),
			VnID = ISNULL(VnID, ''),
			Assort = ISNULL(Assort, ''),
			ByID = ISNULL(ByID, ''),
			Class1 = ISNULL(Class1, ''),
			Class2 = ISNULL(Class2, ''),
			Class3 = ISNULL(Class3, ''),
			Class4 = ISNULL(Class4, ''),
			LcID = ISNULL(LcID, ''),
			LStatus = ISNULL(LStatus, ''),
			LDivision = ISNULL(LDivision, ''),
			LRegion = ISNULL(LRegion, ''),
			LUserDefined = ISNULL(LUserDefined, '')
		FROM AIMFcstSetup 
		WHERE FcstSetupKey = @FcstSetupKey
	
		RETURN 1		-- SUCCEED	
	END
	ELSE
	BEGIN
		RETURN 0		-- FAIL
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
