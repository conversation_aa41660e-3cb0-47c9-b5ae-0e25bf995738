SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Transfer_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Transfer_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTH<PERSON>IZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_Transfer_Sp
** 	Desc: Returns the Overstocks And Shortages transfer data.
**
** 	Returns: 1) @@recordcount - Can be Zero
**               2) 0 - Failure
**             
** 	Values:
** 	@RptOpt = 0 OverStocks Only Or 2  Overstocks And Shortages are the only valid option  
**              
** 	Auth:   Randy Sadler
** 	Date:   
**
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:      Author: 	       Description:
** 	---------- --------------- ---------------------------------------------
** 	01/14/2003 Srinivas Ud     Updated for AIM 4.3. Replaces AIM_Overstock_Sp	
*******************************************************************************/
     
CREATE PROCEDURE AIM_Transfer_Sp
(
      @SQL_Overstocks         nvarchar(4000),
      @SQL_Shortages          nvarchar(4000),
      @RptOpt                 tinyint       
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE @SQL                  nvarchar(4000)
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @RptOpt IS NULL
BEGIN
 	RETURN 0
END

  IF @RptOpt = 0
  BEGIN

 	IF @SQL_Overstocks IS NULL
 	BEGIN
  		RETURN 0
 	END

        -- Build the Overstock Table
        EXEC (@SQL_Overstocks + ' ORDER BY item, lcid ')
        
        RETURN 
END

IF @RptOpt > 1
BEGIN
      -- CREATE the temporary tables

        SELECT RcdId = 1, Item, ItDesc, ItStat, Lcid, Cost, OrderPt, OrderQty, Oh, Oo,  
            ComStk, BkOrder, BkComStk, FcstDemand, ConvFactor
        INTO #overstocks 
        FROM Item  
        WHERE lcid = 'XXXXXXXXXX'

            
  	IF @SQL_Shortages IS NULL
  	BEGIN
  		RETURN 0
  	END

        SELECT * 
 	INTO #shortages 
 	FROM #overstocks 
END
IF @RptOpt = 2          -- Overstocks and Shortages
BEGIN
        -- Build the Overstock Table
        EXEC ('insert into #overstocks ' + @SQL_Overstocks)
        
        -- Build the Shortage Table
        EXEC ('insert into #shortages ' + @SQL_Shortages)
        
        -- Merge the overstock table with the shortage table
        SELECT * 
FROM #overstocks
UNION ALL SELECT * 
  FROM #shortages 
    ORDER BY item, rcdid, lcid
END
-- Wrap Up
DROP TABLE #overstocks
DROP TABLE #shortages 
        
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

