SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReleasedPurchaseOrders_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReleasedPurchaseOrders_Sp]
GO

/******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ReleasedPurchaseOrders_Sp
**	Desc: Retrieves Released Purchase Orders from the POStatistics table.
**
**	Returns: 1) @@rowcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
    
CREATE PROCEDURE AIM_ReleasedPurchaseOrders_Sp
(
  	@ById       						nvarchar(12),
        @RelDate    						datetime
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @ById IS NULL
BEGIN
	RETURN 0
END

IF @RelDate IS NULL
BEGIN
	RETURN 0
END
If Upper(@ById) ='ALL'
BEGIN
	SELECT Buyer = ById, Vendor = VnId, Assort = Assort, 'Released' = isdate, 
	'Nbr Lines' = NbrLines, VSOQ = VSOQ, 'Ext Cost' = ExtCost  
	FROM POStatistics  
	WHERE  IsDate >= @RelDate
	ORDER BY IsDate, VnId, Assort
END
ELSE
BEGIN
	SELECT Buyer = ById, Vendor = VnId, Assort = Assort, 'Released' = isdate, 
	'Nbr Lines' = NbrLines, VSOQ = VSOQ, 'Ext Cost' = ExtCost  
	FROM POStatistics  
	WHERE ById = @ById
	AND IsDate >= @RelDate
	ORDER BY IsDate, VnId, Assort
END
RETURN @@rowcount
    
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

