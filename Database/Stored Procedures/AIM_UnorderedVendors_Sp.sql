SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UnorderedVendors_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UnorderedVendors_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON>AL<PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UnorderedVendors_Sp
**	Desc: 
**
**	Returns: 1)return - Can be Zero
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
      
CREATE PROCEDURE AIM_UnorderedVendors_Sp
(
	@ById       						nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @ById IS NULL
BEGIN
	RETURN 0
END

SELECT DISTINCT Item.VnId, Item.Assort, AIMVendors.VName, AIMVendors.Vn_Min, AIMVendors.Vn_Best,
AIMVendors.Reach_Code, AIMVendors.RevCycle
FROM Item
INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId 
AND Item.Assort = AIMVendors.Assort
WHERE (ItStat = 'U') AND (ById = @ById)
ORDER BY Item.Vnid, Item.Assort
  
RETURN @@ROWCOUNT

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

