SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
	WHERE ID = object_id(N'[dbo].[AIM_LUserDefined_List_Sp]') 
	AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_LUserDefined_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_LUserDefined_List_Sp
** 	Desc: Gets unique values for AIMLocations.LUserDefined 
** 	
** 	Returns: 1) 0 -- Failure
**	2)	1 - Success
** 	3) -1 - Invalid parameters
** 	4) none of the above -- SQL Error code.
** 	
** 	Auth: Annalakshmi Stocksdale
** 	Date: 2004/09/03
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date: 		Author:	     Description:
** 	---------- 	------------ ------------------------------------------
*******************************************************************************/
CREATE PROCEDURE AIM_LUserDefined_List_Sp
-- 	WITH ENCRYPTION /* Production use must be encrypted */
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	SET NOCOUNT ON

	DECLARE @RowCounter as int
	DECLARE @RtnCode as tinyint

	SELECT AIMLocations.LUserDefined
	FROM AIMLocations
	WHERE AIMLocations.LUserDefined IS NOT NULL
	AND LTRIM(RTRIM(AIMLocations.LUserDefined)) <> ''
	GROUP BY AIMLocations.LUserDefined
	ORDER BY AIMLocations.LUserDefined
	
	SELECT @RowCounter = @@rowcount	
	-- 	Check for SQL Server errors.
	SET @RtnCode = CASE 
		WHEN @RowCounter > 0 THEN  1		-- SUCCEED	
		WHEN @@ERROR <> 0 THEN @@ERROR
		ELSE  0	-- FAIL
	END	

	RETURN @RtnCode
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


