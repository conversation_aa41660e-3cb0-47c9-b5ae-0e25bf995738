SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepository_Save_Sp
**	Desc: Inserts Forecast Repository data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**               3) -1 - No Data Found
**               4) -2 - SQL Error
**               5) -3 - Error Data does not match in Header
**               6) -4 - Error Unable to ALTER  Header
**               7) -5 - Unable to Retrieve Repository Key
**	Values:  Record - Forecast RepositoryKey
**              
**	Auth:   Wade Riza 
**	Date:   07/31/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza    Updated Return Codes and Error Handling
**      09/03/2002 Wade Riza	Updated for no Key found (return of -4)
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastRepository_Save_Sp (
	@UserID as nvarchar(12),
	@FcstID as nvarchar(12),
	@UserElement as bit,
	@RepositoryKey as numeric OUTPUT
	-- FcstComment is stored separately
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON
	DECLARE @Return_Stat as int
	
	BEGIN TRANSACTION
		SELECT @RepositoryKey = ForecastRepository.FcstId 
		FROM ForecastRepository
		WHERE ForecastRepository.FcstId = @FcstId 
		SET @Return_Stat = @@ROWCOUNT	
		IF @Return_Stat <= 0 
		BEGIN		
			INSERT INTO ForecastRepository (
				FcstId,
				UserElement,
				UserIdCreate,
				UserIdEdit,
				DateTimeCreate,
				DateTimeEdit
			) VALUES (
				@FcstId,
				@UserElement,
				@UserID, 
				@UserID,
				GetDate(),
				GetDate()			
			)			
			SET @RepositoryKey = @@IDENTITY
			SET @Return_Stat = @@ROWCOUNT
		END
	COMMIT TRANSACTION
	-- Return status to calling mod.
	IF @Return_Stat > 0 RETURN @Return_Stat
	ELSE RETURN -1
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

