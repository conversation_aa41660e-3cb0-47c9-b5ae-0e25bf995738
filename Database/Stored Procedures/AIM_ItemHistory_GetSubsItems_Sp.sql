SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHistory_GetSubsItems_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHistory_GetSubsItems_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemHistory_GetSubsItems_Sp
**	Desc: Retrieves ItemHistory for QtyShipped showing Primary and Substitute items
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/08/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   		Description:
**	---------- ------------		-----------------------------------------------
*******************************************************************************/
     
CREATE PROCEDURE AIM_ItemHistory_GetSubsItems_Sp
(
	@LcIdKey nvarchar(12),
	@ItemKey nvarchar(25),
    @StartYear int,
    @EndYear int,
	@NumberOfSubs int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @RowCount int

	SELECT @NumberOfSubs = COUNT(*) FROM ItemHistory
	WHERE SubsItem <> Item
	AND LcID = @LcIDKey
	AND Item = @ItemKey
	AND HisYear BETWEEN @StartYear AND @EndYear
	
	SELECT LcId, Item, SubsItem, HisYear, 
		dmd01 = CPS01, 
		dmd02 = CPS02, 
		dmd03 = CPS03, 
		dmd04 = CPS04, 
		dmd05 = CPS05, 
		dmd06 = CPS06, 
		dmd07 = CPS07, 
		dmd08 = CPS08, 
		dmd09 = CPS09, 
		dmd10 = CPS10, 
		dmd11 = CPS11, 
		dmd12 = CPS12, 
		dmd13 = CPS13, 
		dmd14 = CPS14, 
		dmd15 = CPS15, 
		dmd16 = CPS16, 
		dmd17 = CPS17, 
		dmd18 = CPS18, 
		dmd19 = CPS19, 
		dmd20 = CPS20, 
		dmd21 = CPS21, 
		dmd22 = CPS22, 
		dmd23 = CPS23, 
		dmd24 = CPS24, 
		dmd25 = CPS25, 
		dmd26 = CPS26, 
		dmd27 = CPS27, 
		dmd28 = CPS28, 
		dmd29 = CPS29, 
		dmd30 = CPS30, 
		dmd31 = CPS31, 
		dmd32 = CPS32, 
		dmd33 = CPS33, 
		dmd34 = CPS34, 
		dmd35 = CPS35, 
		dmd36 = CPS36, 
		dmd37 = CPS37, 
		dmd38 = CPS38, 
		dmd39 = CPS39, 
		dmd40 = CPS40, 
		dmd41 = CPS41, 
		dmd42 = CPS42, 
		dmd43 = CPS43, 
		dmd44 = CPS44, 
		dmd45 = CPS45, 
		dmd46 = CPS46, 
		dmd47 = CPS47, 
		dmd48 = CPS48, 
		dmd49 = CPS49, 
		dmd50 = CPS50, 
		dmd51 = CPS51, 
		dmd52 = CPS52
	FROM ItemHistory
	WHERE LcID = @LcIdKey
	AND Item = @ItemKey
	AND HisYear BETWEEN @StartYear AND @EndYear
	ORDER BY HisYear, SubsItem

	SELECT @RowCount = @@RowCount

	RETURN  @RowCount

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

