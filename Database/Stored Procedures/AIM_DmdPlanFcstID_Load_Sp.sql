SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstID_Load_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstID_Load_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstID_Load_Sp
**	Desc: Retrieves Forecast ID and Description for use in the UI dropdowns
**	Derived from AIM_GetList_FcstID_Sp. Should replace that stored procedure once AIMFcst.tab is deleted.
**	Returns: 1)@rowcount - Can be Zero
**	Values:  AIMDays
**              
**	Auth:   Annalaksh<PERSON> Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/     
CREATE PROCEDURE AIM_DmdPlanFcstID_Load_Sp
(
	@FcstEnabled as bit,
	@UserID as nvarchar(12),
	@RecordCount as int OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	  
	SELECT 
		AIMFcstSetup.FcstID,
		AIMFcstSetup.FcstDesc
	FROM AIMFcstSetup
	WHERE AIMFcstSetup.FcstEnabled = CASE
		WHEN @FcstEnabled IS NOT NULL THEN @FcstEnabled ELSE AIMFcstSetup.FcstEnabled END
	AND AIMFcstSetup.FcstSetupKey IN 
		(SELECT FcstSetupKey FROM AIMFcstAccess WHERE AIMFcstAccess.UserID = CASE UPPER(@UserID) WHEN 'SA' THEN AIMFcstAccess.UserID ELSE @UserID END GROUP BY AIMFcstAccess.FcstSetupKey) 
	GROUP BY
		AIMFcstSetup.FcstID, 
		AIMFcstSetup.FcstDesc
	ORDER BY 
		AIMFcstSetup.FcstID, 
		AIMFcstSetup.FcstDesc

	SELECT @RecordCount = @@ROWCOUNT
	
	IF @RecordCount > 0
	BEGIN
		RETURN 1
	END
	ELSE
	BEGIN
		RETURN @@ERROR
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
