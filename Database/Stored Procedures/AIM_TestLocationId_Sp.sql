SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_TestLocationId_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_TestLocationId_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_TestLocationId_Sp
**	Desc: 
**
**	Returns: 1) @@rowcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
      
CREATE PROCEDURE AIM_TestLocationId_Sp
(
  	@LcId   						nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

-- Validate the required parameters.
IF @LcId IS NULL
BEGIN
	RETURN 0
END

-- The 'All' Location is ok
IF @LcId = 'All'
BEGIN
  	RETURN 1		-- Succeed
END

SELECT @LcId = LcId
FROM AIMLocations
WHERE LcId = @LcId

 RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

