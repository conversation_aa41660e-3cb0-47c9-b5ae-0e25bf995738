if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_GetKey_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO




/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>O<PERSON>ES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepository_GetKey_Sp
**	Desc: Returns Forecast Repository Key
**
**	Returns: 1)  0 - Successful Insert
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**               4) -3 - Multiple Keys found
**               5) -4 - No Key found
**	Values:  Record - Forecast RepositoryKey
**              
**	Auth:   Wade Riza 
**	Date:   08/29/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      09/03/2002 Wade Riza	Updated return value for no key found.
*******************************************************************************/

CREATE   PROCEDURE AIM_ForecastRepository_GetKey_Sp
(
	@FcstID				nvarchar(12),
	@UserElement			bit,
	@RepositoryKey			int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int,
	@rtncode				int


SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

BEGIN
	SELECT @RepositoryKey = RepositoryKey FROM ForecastRepository 
	WHERE FcstID = @FcstID AND UserElement = @UserElement
    	
	SELECT @found = @@rowcount
END
	
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 1
BEGIN
	RETURN 0
END
ELSE IF @found > 1
BEGIN
	RETURN -3
END
ELSE
BEGIN
	SET @RepositoryKey = 0
	RETURN -4
END




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

