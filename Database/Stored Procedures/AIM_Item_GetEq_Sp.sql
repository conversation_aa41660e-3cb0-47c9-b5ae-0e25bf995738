SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Item_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Item_GetEq_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Item_GetEq_Sp
**	Desc: Retreives Item information based on Location ID and Item
**
**	Returns: 1)@found - Can be Zero
**             2) 1 - No Data Found
**             
**	Values:  Recordset - AccessType
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**      10/22/2002 Wade Riza    Updated Return Codes and Error Handling
*******************************************************************************/
  
CREATE PROCEDURE AIM_Item_GetEq_Sp
(
      	@LcIdKey					nvarchar(12),
       	@ItemKey					nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE @rtncode 					int
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @LcIDKey IS NULL
BEGIN
	RETURN 1
END

IF @ItemKey IS NULL
BEGIN
	RETURN 1
END

SELECT * 
FROM item
WHERE lcid = @LcIdKey
AND item = @ItemKey

SELECT @rtncode = @@rowcount
  
IF @rtncode = 0
BEGIN
      RETURN 0
END
ELSE
BEGIN
      RETURN 1
END  

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

