SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxRS_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxRS_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxRS_Sp
**	Desc: Loads data exchange item records into the Re-stock Sourcing tables
**          from the AIMDxRS Table. 
**
**	Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**		        1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**                      4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/28
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:      Updated by:   Description:
**  ---------- ------------- --------------------------------------------------
**  2003/06/25 Srinivas	U    Get Src_Priority and DftReviewerId from different
**			     Tables.	
**  2005/03/08 Srinivas U    Added truncate and delete before the bulk insert					
*******************************************************************************/
 
CREATE PROCEDURE AIM_DxRS_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT	
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	/* START VARIABLE DECLARATION */  
	DECLARE @RtnCode int
	DECLARE @FetchStatus int

	-- DxRS table
	DECLARE @SeqNbr int
	DECLARE @Lcid_Destination nvarchar (12)
	DECLARE @Lcid_Source nvarchar (12)
	DECLARE @Src_Priority smallint
	DECLARE @ReviewerID nvarchar (12)
	
	-- Defaults -- 
	-- AllocDefaults
	--DECLARE @AllocDft_AllocDefaultsID numeric(18, 0)
	DECLARE @AllocDft_ReviewerId nvarchar (12)
	DECLARE @AllocDft_Src_Priority tinyint
	DECLARE @AllocDefaultsId numeric(18,0)
	DECLARE @LStatus nvarchar(1)
	DECLARE @LDivision nvarchar(20)
	DECLARE @LRegion nvarchar(20)
	DECLARE @LUserDefined nvarchar(30)

	SET NOCOUNT ON
	
	/* Start variable initialization */
	--Initialize counters
	SELECT @InsertCounter = 0
		, @UpdateCounter = 0
	
	-- Delete records from the DxRS Table
	TRUNCATE TABLE AIMDxRS
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxRS
	-- This is a data interface transaction <Transaction Code=RS>. 
	-- Bulk Load the <Transaction Table=AIMDxRS> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'RS', 'AIMDxRS', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- Process records
	DECLARE DxRS_Cursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT SeqNbr
			, RTRIM(Lcid_Destination)
			, RTRIM(Lcid_Source)
			, Src_Priority
			, RTRIM(ReviewerID)
		FROM AIMDxRS
	OPEN DxRS_Cursor
	-- Check for no rows to process
	IF @@CURSOR_ROWS = 0 
	BEGIN
		RETURN
	END

	BEGIN TRANSACTION
	-- Process records for updating Item with
	SELECT @FetchStatus = 0
	WHILE @FetchStatus = 0
	BEGIN  --BEGIN cursor loop
	    -- Get the next record from the cursor  
        FETCH NEXT FROM DxRS_Cursor INTO 
			 @SeqNbr
			, @Lcid_Destination
			, @Lcid_Source
			, @Src_Priority
			, @ReviewerID
	    -- Check for end of cursor and exit loop
		SELECT @FetchStatus = @@FETCH_STATUS
	    If @FetchStatus <> 0 
		BEGIN
	        BREAK
		END

		/* Start data validation */
		/*
		-- Get default values from AllocDefaults and AllocDefaultsSource
		SELECT @AllocDft_AllocDefaultsID = AllocDefaults.AllocDefaultsID
			, @AllocDft_ReviewerId = AllocDefaults.ReviewerId
			, @AllocDft_Src_Priority = AllocDefaultsSource.Src_Priority
		FROM AllocDefaults
		INNER JOIN AIMLocations LOC
			ON LOC.LStatus = AllocDefaults.LStatus
			AND LOC.LDivision = AllocDefaults.LDivision
			AND LOC.LRegion = AllocDefaults.LRegion
			AND LOC.LUserDefined = AllocDefaults.LUserDefined
			AND LOC.LRank = AllocDefaults.LRank
		INNER JOIN AllocDefaultsSource
			ON AllocDefaultsSource.AllocDefaultsID = AllocDefaults.AllocDefaultsID
		WHERE LOC.Lcid = @LcID_Destination
			AND LOC.LType = 'D'
			AND AllocDefaultsSource.LcID_Source = @LcID_Source
		*/
		/*Location table has the default ReviewerId and Src_priority populated from
		  AllocDefaults table on AIM_DXLC_Sp interface 
		*/
		SELECT @LStatus = LOC.LStatus,
			@LDivision =LOC.LDivision,
			@LRegion=LOC.LRegion,
			@LUserDefined =LOC.LUserDefined
		FROM AIMLocations LOC
		WHERE LOC.Lcid =@LcID_Destination

		EXEC @RtnCode = AIM_AllocDefaults_Get_Sp @LStatus,@LDivision,@LRegion,
				@LUserDefined,0 ,
				@AllocDft_ReviewerId output,0,
				@AllocDefaultsId output
	
		SELECT @AllocDft_Src_Priority = Src_Priority
		FROM AllocDefaultsSource
		WHERE AllocDefaultsId =@AllocDefaultsId

		--Substitute defaults if data not provided
		If @ReviewerID IS NULL
		BEGIN
			IF @AllocDft_ReviewerID IS NULL
			BEGIN
				SET @ReviewerID = ''
			END
			ELSE
			BEGIN
				SET @ReviewerID = @AllocDft_ReviewerID
			END
		END

		If @Src_Priority IS NULL
		BEGIN
			IF @AllocDft_Src_Priority IS NULL
			BEGIN
				SET @Src_Priority = 0
			END
			ELSE
			BEGIN
				SET @Src_Priority = @AllocDft_Src_Priority
			END
			
		END

		/* Start writing to AIM tables*/
		-- AIM Sourcing Hierarchy
		UPDATE AIMSourcingHierarchy
		SET Src_Priority = @Src_Priority
			, ReviewerID = @ReviewerID
		WHERE AIMSourcingHierarchy.LcID_Destination = @LcID_Destination
			AND AIMSourcingHierarchy.LcID_Source = @LcID_Source
		If @@ROWCOUNT <> 0 
		BEGIN
			SELECT @UpdateCounter = @UpdateCounter + 1
		END
		ELSE
		BEGIN
			-- INSERT
			INSERT INTO AIMSourcingHierarchy (LcID_Destination
			, LcID_Source
			, Src_Priority
			, ReviewerID)
			VALUES (@Lcid_Destination
			, @LcID_Source
			, @Src_Priority
			, @ReviewerID)
		
			If @@ROWCOUNT > 0
			BEGIN
				SELECT @InsertCounter = @InsertCounter + 1
			END
		END -- check rowcount from update
	END -- WHILE

	-- Deallocate the cursor
	CLOSE DxRS_Cursor
	DEALLOCATE DxRS_Cursor
	COMMIT TRANSACTION
	
	-- Delete records from the DxRS Table
	TRUNCATE TABLE AIMDxRS
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxRS
		
	UPDATE STATISTICS AIMSourcingHierarchy WITH RESAMPLE, ALL

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

