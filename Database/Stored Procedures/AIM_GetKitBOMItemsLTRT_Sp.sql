SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetKitBOMItemsLTRT_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetKitBOMItemsLTRT_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetKitBOMItemsLTRT_Sp
**	Desc: Gets the Accum_LT and Accum_LT + ReviewTime for all items dependent  
**            on the master item
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) -1 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_GetKitBOMItemsLTRT_Sp
(
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

SELECT DISTINCT Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
	StartDate,EndDate
FROM  ITEM,ITEMKITBOM 
WHERE ITEM.Item =ITEMKITBOM.ItemComponent AND 
      ITEM.LcId =ITEMKITBOM.LcId AND 
      ITEM.Item  IN
      (SELECT ITEMCOMPONENT 
      FROM ITEMKITBOM,ITEM 
      WHERE ITEMKITBOM.LCID =ITEM.LCID AND
	ITEMKITBOM.ITEM =ITEM.ITEM AND
	ITEM.KITBOMFLAG ='Y'AND
	ITEMKITBOM.LCID =@LCID AND 
	ITEMKITBOM.ITEM =@ITEM
	 )AND 
      ITEMKITBOM.Item =@ITEM AND 
      ITEM.LcId =@LCID

RETURN  @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

