SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Item_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Item_GetKey_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Item_GetKey_Sp
**	Desc: Retreives Item information based on Location ID, Item And Action
**
**	Returns: 1)@found - Can be Zero
**             2) 1 - No Data Found
**             
**	Values:  Recordset - AccessType
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      10/22/2002 Wade Riza    Updated Return Codes and Error Handling
*******************************************************************************/
   
CREATE PROCEDURE AIM_Item_GetKey_Sp
(
       	@LcId 						nvarchar(12) 	OUTPUT,		-- Location ID
       	@Item						nvarchar(25) 	OUTPUT,		-- Item Number
       	@action						tinyint			        	-- Action Code
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rtncode 					int

SET NOCOUNT ON

SET rowcount 1

-- Validate the required parameters.
--IF @LcID IS NULL
--BEGIN
--	RETURN 1
--END
--IF @Item IS NULL
--BEGIN
--	RETURN 1
--END

IF @action IS NULL
BEGIN
	RETURN 1
END

-- Get the key to the Item Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
  	SELECT @LcId = lcid, @Item = item
	FROM item
	WHERE lcid = @lcid 
	AND item = @item
	ORDER BY lcid ASC, item ASC

	SELECT @rtncode = @@rowcount
END
  
IF @action = 1		-- Get Greater Than
BEGIN
  	SELECT @Lcid = lcid, @Item = item
	FROM item 
	WHERE lcid = @lcid 
	AND item > @item
	ORDER BY lcid ASC, item ASC
        
	SELECT @rtncode = @@rowcount

	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = item 
		FROM item 
		WHERE lcid > @lcid
		ORDER BY lcid ASC, item ASC
 
	 	SELECT @rtncode = @@rowcount
       END
END
  
IF @action = 2		-- Get Less Than 
BEGIN
  	SELECT @Lcid = lcid, @Item = item 
	FROM item
	WHERE lcid = @lcid 
	AND item < @item
        ORDER BY lcid DESC, item DESC

        SELECT @rtncode = @@rowcount
       
	IF @rtncode = 0
       	BEGIN
        	SELECT @Lcid = lcid, @Item = item 
		FROM item
		WHERE lcid < @lcid 
                ORDER BY lcid DESC, item DESC

           	SELECT @rtncode = @@rowcount
       END
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
       	SELECT @Lcid = lcid, @Item = item 
	FROM item
	WHERE lcid = @lcid
	AND item >= @item
	ORDER BY lcid ASC, item ASC

       	SELECT @rtncode = @@rowcount
       
	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = item 
		FROM item 
		WHERE lcid > @lcid
		ORDER BY lcid ASC, item ASC
 
        	SELECT @rtncode = @@rowcount
	END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
  	SELECT @Lcid = lcid, @Item = item
	FROM item
	WHERE lcid = @lcid
	AND item <= @item
        ORDER BY lcid DESC, item DESC

	SELECT @rtncode = @@rowcount

       	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = item 
		FROM item
		WHERE lcid < @lcid 
               	ORDER BY lcid DESC, item DESC
           	
		SELECT @rtncode = @@rowcount
       	END
END
IF @action = 5		-- Get First
BEGIN
	SELECT @LcId = lcid, @Item = item 
	FROM item
    	ORDER BY lcid ASC, item ASC

       	SELECT @rtncode = @@rowcount

END
IF @action = 6		-- Get Last
BEGIN
  	SELECT @LcId = lcid, @Item = item
	FROM item
        ORDER BY lcid DESC, item DESC

	SELECT @rtncode = @@rowcount

END
IF @rtncode > 0 
BEGIN
-- exec AIM_Item_GetEq_Sp @LcId, @Item 
  	return 1		-- SUCCEED
END
ELSE
BEGIN
  	return 0		-- FAIL
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

