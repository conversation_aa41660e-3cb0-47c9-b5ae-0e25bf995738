SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxIS_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxIS_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** Name: AIM_DxIS_Sp
** Desc: Loads substitute item records into the AllocItemSubstitutes table
**           from the AIMDxIS Table. 
** 
** Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**			1)  0 - Successful
**              	2) -1 - No Data Found
**              	3) -2 - SQL Error
**              	4) -3 - Duplicate File Name
**              	5) -4 - Invalid File Name
** 
** Author:	Annalakshmi Stocksdale
** Created:	2003/05/28
********************************************************************************
** Change History
********************************************************************************
** Date:	Updated by:      Description:
** ----------	---------------- -----------------------------------------------
**      2004/03/05 S.Uddanti	 Add delete from AIMDxIS so that the procedure works 
**                               even when there are some updates and some inserts
**    2005/03/08 Srinivas U	 Added truncate and delete before the bulk insert
********************************************************************************/
 
CREATE PROCEDURE AIM_DxIS_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT	
)

-- WITH ENCRYPTION	-- Production use must be encrypted 
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	-- Start variable declaration 
	DECLARE @RtnCode int

	-- Start variable initialization 
	SELECT @InsertCounter = 0
		, @UpdateCounter = 0
	
	-- Delete records from the DxIS Table
	TRUNCATE TABLE AIMDxIS
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxIS
	-- This is a data interface transaction <Transaction Code=IS>. 
	-- Bulk Load the <Transaction Table=AIMDxIS> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'IS', 'AIMDxIS', '\t'
		-- This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	BEGIN TRANSACTION
		UPDATE AllocItemSubstitutes
			SET AllocSubstPriority = ISNULL(AIMDxIS.AllocSubstPriority, AllocItemSubstitutes.AllocSubstPriority)
		FROM AIMDxIS
		WHERE AllocItemSubstitutes.AllocPrimaryItem = AIMDxIS.AllocPrimaryItem
			AND AllocItemSubstitutes.AllocSubstItem = AIMDxIS.AllocSubstItem
		SET @UpdateCounter = @@ROWCOUNT 
		
		-- Delete the ones have been updated already so as to insert the ones that are left behind (and are therefore new)
		DELETE AIMDxIS
		FROM AIMDxIS
		INNER JOIN AllocItemSubstitutes
		ON AIMDxIS.AllocPrimaryItem = AllocItemSubstitutes.AllocPrimaryItem
			AND AIMDxIS.AllocSubstItem = AllocItemSubstitutes.ALlocSubstItem

		-- INSERT
		--Substitute defaults if data not provided		
		INSERT INTO AllocItemSubstitutes (
			AllocPrimaryItem,
			AllocSubstItem,
			AllocSubstPriority
		) SELECT RTRIM( AIMDxIS.AllocPrimaryItem),
			RTRIM(AIMDxIS.AllocSubstItem),
			ISNULL(AIMDxIS.AllocSubstPriority, 0)
		FROM AIMDxIS
		WHERE AIMDxIS.AllocPrimaryItem IS NOT NULL
			AND AIMDxIS.AllocSubstItem IS NOT NULL

		SET @InsertCounter = @@ROWCOUNT 
	COMMIT TRANSACTION

	-- Delete records from the DxIS Table
	TRUNCATE TABLE AIMDxIS
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxIS

	UPDATE STATISTICS AllocItemSubstitutes WITH RESAMPLE, ALL

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
