SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SumMethods_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SumMethods_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHAL<PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_SumMethods_Sp
**	Desc: This stored procedure controls execution of the 
**          Velocity Code Update Procedure.
**
**	Returns: 1) @found - Can be Zero
**               2) 0 - No Data Found
**    
**	Values:  Recordset - Item
**              
**	Auth:   Randy Sadler
**	Date:   03/30/2000
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      11/01/2002 Wade Riza    Added Validation
*******************************************************************************/
     
CREATE PROCEDURE AIM_SumMethods_Sp
(
    @lcid 							nvarchar(12) = 'ALL'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @LcId IS NULL
BEGIN
	RETURN 0
END
 
IF @lcid = 'ALL'
BEGIN
        SELECT Item.FcstMethod, AIMMethods.MethodDesc, 
	COUNT(*) AS SelCount, Avg(Item.FcstDemand) AS AvgDemand,
        Avg(Item.MAE) AS AvgMAE, Avg(Item.MSE) AS AvgMSE,
        Avg(ABS(Item.Trend)) AS AvgTrend
        FROM Item 
    		INNER JOIN AIMMethods 
    		ON Item.FcstMethod = AIMMethods.MethodId
    		INNER JOIN ItStatus
    		ON Item.ItStat = ItStatus.ItStat
        WHERE Item.FcstMethod <> 0
        AND Item.FcstDemand > 0
    	AND ItStatus.DmdUpd = 'Y'
        GROUP BY Item.FcstMethod, AIMMethods.MethodDesc
        ORDER BY COUNT(*) DESC

END
ELSE
BEGIN
        SELECT Item.FcstMethod, AIMMethods.MethodDesc, 
	COUNT(*) AS SelCount, Avg(Item.FcstDemand) AS AvgDemand,
        Avg(Item.MAE) AS AvgMAE, Avg(Item.MSE) AS AvgMSE,
        Avg(ABS(Item.Trend)) AS AvgTrend
        FROM Item  
    		INNER JOIN AIMMethods 
    		ON Item.FcstMethod = AIMMethods.MethodId
    		INNER JOIN ItStatus
    		ON Item.ItStat = ItStatus.ItStat
        WHERE Item.FcstMethod <> 0
        AND Item.FcstDemand > 0
    	AND ItStatus.DmdUpd = 'Y'
        AND Item.Lcid = @Lcid
        GROUP BY Item.FcstMethod, AIMMethods.MethodDesc
        ORDER BY COUNT(*) DESC
END
  
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

