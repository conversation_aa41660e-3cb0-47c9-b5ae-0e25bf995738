SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BatchFcstDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BatchFcstDetail_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BatchFcstDetail_Save_Sp
**	Desc: Inserts Forecast Repository Detail data during batch process
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**		 3) -1 - No Data Found
**               4) -2 - SQL Error
**             
**	Auth:   Srinivas Uddanti
**	Date:   09/05/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**     
*******************************************************************************/
     
CREATE  PROCEDURE AIM_BatchFcstDetail_Save_Sp
(
    @RepositoryKey					int,
    @LcID		        			nvarchar(12), 
    @Item		        			nvarchar(25), 
    @FcstPdBegDate		        	        datetime, 
    @FcstPdEndDate		        	        datetime, 
    @SysFcst					        int=0,
    @MasterFcstAdj					int=0,
    @FcstAdj						int = 0,
    @NetReq						int = 0,
    @AdjNetReq						int = 0,
    @ProjInv						int = 0,	
    @HistDmd						int = 0,
    @ProdConst						int=0
)	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      					int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdBegDate IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdEndDate IS NULL
BEGIN
	RETURN -1
END



SELECT @RepositoryKey = RepositoryKey FROM ForecastRepositoryDetail
WHERE RepositoryKey = @RepositoryKey
AND Item = @Item
AND LcId = @LcId
AND FcstPdBegDate = @FcstPdBegDate
        
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0		-- Insert new Repository Detail record
BEGIN
	BEGIN
	   INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,
	     Fcst,MasterFcstAdj,FcstAdj,FcstNetReq,FcstAdjNetReq,
	     QtyProjectedInventory,HistDmd,ProdConst)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
           @SysFcst,@MasterFcstAdj,@FcstAdj,@NetReq,@AdjNetReq,@ProjInv,@HistDmd,@ProdConst)

	END
	
END
ELSE			-- Update existing Repository Detail record
BEGIN	
        

	BEGIN
	   UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    Fcst =@SysFcst,
            MasterFcstAdj =@MasterFcstAdj,
	    FcstAdj = @FcstAdj,
	    FcstNetReq =@NetReq,
	    FcstAdjNetReq =@AdjNetReq,
	    QtyProjectedInventory = @ProjInv,
	    HistDmd =@HistDmd ,
	    ProdConst=@ProdConst         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate
       
	END
	
END

	SELECT @found = @@rowcount

        -- Check for SQL Server errors.
        IF @@ERROR <> 0 
        BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
        END
        ELSE
	BEGIN
		RETURN 0 -- SUCCESSFUL
	END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
