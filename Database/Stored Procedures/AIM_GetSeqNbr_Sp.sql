SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetSeqNbr_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetSeqNbr_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetSeqNbr_Sp
**	Desc: Retrieves the Sequence Number from the flat file.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_GetSeqNbr_Sp
(
  	@FileName					nvarchar(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @Pos					int

SET NOCOUNT ON
 
-- Format for EXceed AIM Input Files: LcID.EXceedAIM.TxnType.nnnnnnnn.TXT
-- where TxnType = DD, LT, HS, SD, SS, VN
-- Position on the .EXceedAIM.
SELECT @Pos = CHARINDEX('.EXceedAIM.',@FileName)
IF @Pos = 0
BEGIN
        RETURN 0
END
ELSE
BEGIN
    	SELECT @Pos  = @Pos + 10
END
    	
IF isnumeric(substring(@FileName, @Pos, 8)) = 1
BEGIN
      RETURN convert(int, substring(@FileName, @Pos, 8)) 
END
ELSE
BEGIN
        RETURN 0
END    

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

