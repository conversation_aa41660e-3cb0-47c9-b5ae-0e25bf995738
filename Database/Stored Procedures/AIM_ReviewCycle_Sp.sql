SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewCycle_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewCycle_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ReviewCycle_Sp
**	Desc: 
**
**	Returns: 
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_ReviewCycle_Sp 
(	
	@RevCycle						nvarchar(8)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @RC int,
    		@ReviewTotal int,
    		@LT_Total int,
    		@RC_Total int,
    		@PE_Total int
    		
SET NOCOUNT ON

-- Initialize Parameters
SELECT @RC = 0, @ReviewTotal = 0, @LT_Total = 0, @RC_Total = 0, @PE_Total = 0
     
EXEC @RC = AIM_OrdGenCtrl_Sp 'R', @RevCycle, '', '', 'N', 'N', 'N', 'Y', @ReviewTotal OUTPUT , @LT_Total OUTPUT , @RC_Total OUTPUT , @PE_Total OUTPUT 
  
SELECT RevCycle = @RevCycle, 'Review Total' = @ReviewTotal, 'Lead Time Exceptions' = @LT_Total,
  	'Scheduled Reviews' = @RC_Total, 'Priority Exceptions' = @PE_Total

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

