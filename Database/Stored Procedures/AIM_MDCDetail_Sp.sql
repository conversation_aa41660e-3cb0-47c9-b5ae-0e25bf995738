SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_MDCDetail_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_MDCDetail_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_MDCDetail_Sp
**	Desc: Retrieves or Creates the data from the MDCDetail table.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_MDCDetail_Sp
(
      	@MDC            				nvarchar(12),
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25),
        @FcstDate       				datetime,
        @AvailQty       				int,
        @FcstDemand     				decimal(10,2), 
        @MAE            				decimal(10,2),
        @Trend          				decimal(10,2),
        @RqQty01        				int,
        @RqQty02        				int,
        @RqQty03        				int, 
        @RqQty04        				int, 
        @RqQty05        				int, 
        @RqQty06        				int, 
        @RqQty07        				int, 
        @RqQty08        				int, 
        @RqQty09        				int, 
        @RqQty10        				int, 
        @RqQty11        				int, 
        @RqQty12        				int, 
        @RqQty13        				int, 
        @RqQty14        				int, 
        @RqQty15        				int, 
       	@RqQty16        				int, 
        @RqQty17        				int, 
        @RqQty18        				int, 
        @RqQty19        				int, 
        @RqQty20        				int, 
        @RqQty21        				int, 
        @RqQty22        				int, 
        @RqQty23        				int, 
        @RqQty24        				int, 
        @RqQty25        				int, 
        @RqQty26        				int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @LcId_Key   nvarchar(12),
          @Item_Key   nvarchar(12)
  
SET NOCOUNT ON
    
-- Does this record already exist
SELECT @LcId_Key = LcId, @Item_Key = Item
FROM MDCDetail 
WHERE LcId = @LcId and Item = @Item
        
IF @@rowcount = 1       -- Found
BEGIN
        UPDATE MDCDetail
        SET MDC = @MDC, LcId = @LcId, Item = @Item, FcstDate = @FcstDate, 
            AvailQty = @AvailQty, FcstDemand = @FcstDemand, 
            MAE = @MAE, Trend = @Trend, RqQty01 = @RqQty01, RqQty02 = @RqQty02, 
            RqQty03 = @RqQty03, RqQty04 = @RqQty04, RqQty05 = @RqQty05, RqQty06 = @RqQty06, 
            RqQty07 = @RqQty07, RqQty08 = @RqQty08, RqQty09 = @RqQty09, RqQty10 = @RqQty10, 
            RqQty11 = @RqQty11, RqQty12 = @RqQty12, RqQty13 = @RqQty13, RqQty14 = @RqQty14, 
            RqQty15 = @RqQty15, RqQty16 = @RqQty16, RqQty17 = @RqQty17, RqQty18 = @RqQty18, 
            RqQty19 = @RqQty19, RqQty20 = @RqQty20, RqQty21 = @RqQty21, RqQty22 = @RqQty22, 
            RqQty23 = @RqQty23, RqQty24 = @RqQty24, RqQty25 = @RqQty25, RqQty26 = @RqQty26
       WHERE LcId = @LcId_Key 
       AND Item = @Item_Key
END
ELSE
BEGIN               -- Not Found
INSERT INTO MDCDetail
        (MDC, LcId, Item, FcstDate, AvailQty, FcstDemand, MAE, Trend, 
        RqQty01, RqQty02, RqQty03, RqQty04, RqQty05, RqQty06, 
        RqQty07, RqQty08, RqQty09, RqQty10, RqQty11, RqQty12, 
        RqQty13, RqQty14, RqQty15, RqQty16, RqQty17, RqQty18, 
        RqQty19, RqQty20, RqQty21, RqQty22, RqQty23, RqQty24, 
        RqQty25, RqQty26) 
  VALUES 
        (@MDC, @LcId, @Item, @FcstDate, @AvailQty, @FcstDemand, @MAE, @Trend, 
        @RqQty01, @RqQty02, @RqQty03, @RqQty04, @RqQty05, @RqQty06, 
        @RqQty07, @RqQty08, @RqQty09, @RqQty10, @RqQty11, @RqQty12, 
        @RqQty13, @RqQty14, @RqQty15, @RqQty16, @RqQty17, @RqQty18, 
        @RqQty19, @RqQty20, @RqQty21, @RqQty22, @RqQty23, @RqQty24, 
        @RqQty25, @RqQty26) 
END
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

