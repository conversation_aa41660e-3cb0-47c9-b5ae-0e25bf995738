SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UserElementDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UserElementDetail_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UserElementDetail_Save_Sp
**	Desc: Inserts into UserElementDetail table during
**	      the UserElement creation
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**		 3) -1 - No Data Found
**               4) -2 - SQL Error
**             
**	Auth:   Srinivas Uddanti
**	Date:  10/17/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**     
*******************************************************************************/
     
CREATE     PROCEDURE AIM_UserElementDetail_Save_Sp
(
    @UserElementId					nvarchar(12),
    @LcID		        			nvarchar(12), 
    @Item		        			nvarchar(25), 
    @FcstPdBegDate		        	        datetime, 
    @FcstPdEndDate		        	        datetime, 
    @Qty					        int=0
)	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      					int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF  @UserElementId IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdBegDate IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdEndDate IS NULL
BEGIN
	RETURN -1
END


BEGIN
	BEGIN
	   INSERT INTO UserElementDetail
            (UserElementId, LcID, Item, FcstPdBegDate,FcstPdEndDate,Qty)
            VALUES (@UserElementId, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate,@Qty)

	END
	
END

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN 0 -- SUCCESSFUL
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
