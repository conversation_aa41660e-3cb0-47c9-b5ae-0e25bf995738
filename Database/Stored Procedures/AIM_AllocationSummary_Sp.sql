
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocationSummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocationSummary_Sp]
GO
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_AllocationSummary_Sp
**	Desc:	Given ReviewerId  and AllocationStatus this procedure returns 
		AIMAO records
**		(a) The item's AllocationStatus can only be PRE or POST
**				
**
**	Returns:	1)@Found - Can be Zero
**				2) 0 - No Data Found
**				3) -1 - Invalid Parameter
**				4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
**	Values:	Recordset - AIMAO
**              
**	Author:	Srinivas Uddnati
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	 Description:
**	09/30/2003 Sujit	 To eliminate error
**				 "Subquery returned more than 1 value." at EXceed AIM 
** 				 Allocation Review(Form_Load))
**	10/03/2003 Srinivas 	 Modified code to read orderinfo from AIMOder_Info
*******************************************************************************/
CREATE PROCEDURE AIM_AllocationSummary_Sp
(
	@ReviewerId		nvarchar(12),
 	@AllocationStatus	nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
Declare 
	@Found int,
	@ApplyTrigger AS TINYINT,
	@ExceptionPct AS TINYINT
-- Validate the required parameters.
IF @ReviewerId IS NULL
Or @AllocationStatus IS NULL

BEGIN
	RETURN -1	/* Invalid parameter */
END

SELECT @ApplyTrigger = (SELECT MAX(AllocExceptionProcess) FROM SysCtrl)		--sujit09302003
IF @ApplyTrigger IS NULL 
BEGIN
-- If not defined, then default to order level
	SELECT @ApplyTrigger = 0
END

IF @AllocationStatus ='PRE' 
--PreAllocation It is always as not exception
BEGIN
	Select AIMAO.OrderStatus,Exception = 'N'  
	,AIMAO.OrdNbr,AIMAO.Lcid,AIMAO.LineCount,AIMAO.LocFillPct,AIMAO.TotalUnits,
	AIMLocations.LName,AIMLocations.LDivision,AIMLocations.LType,
	AIMLocations.LRegion,AIMLocations.LStatus,AIMLocations.LUserDefined,
	LRank =coalesce(AIMLocations.LRank,0),OrdLineCount=0 ,OrdTotalUnits=0,
	OrdFillPct=0
	From AIMAO inner join AIMLocations
	 on AIMAO.lcid =AIMLocations.Lcid
	where AIMAO.OrderStatus in (0,1)
	and AIMAO.ReviewerId = @ReviewerId
END
IF @AllocationStatus ='POST' 
BEGIN
Select AIMAO.OrderStatus,Exception = CASE
	WHEN AIMAO.OrderStatus in (20,22) 
	THEN 'Y' 
	ELSE 'N' 
	END,AIMAO.OrdNbr,AIMAO.Lcid,AIMAO.LineCount,AIMAO.LocFillPct,AIMAO.TotalUnits,
	AIMLocations.LName,AIMLocations.LDivision,AIMLocations.LType,
	AIMLocations.LRegion,AIMLocations.LStatus,AIMLocations.LUserDefined,
	LRank =coalesce(AIMLocations.LRank,0),OrdLineCount=coalesce(AIMAO_OrderInfo.LineCount,0),
	OrdTotalUnits =coalesce(AIMAO_OrderInfo.TotalUnits,0), OrdFillPct=coalesce(AIMAO_OrderInfo.OrderFillPct,0) 
	From AIMAO inner join AIMLocations
	on AIMAO.lcid =AIMLocations.Lcid
	left outer join AIMAO_OrderInfo
	on AIMAO.OrdNbr =AIMAO_OrderInfo.OrdNbr
	where AIMAO.OrderStatus in (10,12,20,22)
	and AIMAO.ReviewerId = @ReviewerId
END
SELECT @Found = @@RowCount

-- Check for SQL Server errors. */
IF @@ERROR <> 0 
BEGIN
	RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
END
ELSE IF @Found <= 0 
BEGIN	
	RETURN -1 /* ERROR (No Valid data found in Database) */
END
ELSE
BEGIN
	RETURN @Found /* SUCCESSFUL */
END	

RETURN @@rowcount


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

