IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AllocDefaults_Save_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AllocDefaults_Save_Sp]
GO
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AllocDefaults_Save_Sp
**	Desc: Updates/Inserts to AIMAllocDefaults
**
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:   Wade Riza
**	Date:   2003/06/04
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-------------------------------------------------
*******************************************************************************/

  CREATE    PROCEDURE AIM_AllocDefaults_Save_Sp
  (
 	@AllocDefaultsId		numeric(18,0),
 	@LStatus			nvarchar(30),
	@LDivision	  		nvarchar(20),
 	@LRegion			nvarchar(20),
 	@LUserDefined  			nvarchar(30),
 	@ExceptionPct			decimal(18,2),
 	@ReviewerID	  		nvarchar(12),
	@LRank				int
  )
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
  AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/


  DECLARE @found        		int

  SET NOCOUNT ON

 
  IF @AllocDefaultsId = 0 or @AllocDefaultsId is null
  BEGIN
  	INSERT INTO AllocDefaults 		(LStatus, 
						LDivision, 
						LRegion, 
						LUserDefined,
						ExceptionPct,
						ReviewerID,
						LRank)
			  	VALUES  (@LStatus, 
						@LDivision, 
						@LRegion, 
						@LUserDefined,
						@ExceptionPct,
						@ReviewerID,
						@LRank)
  END
  ELSE IF @AllocDefaultsId > 0
  BEGIN
  	UPDATE AllocDefaults
  		SET LStatus = @LStatus, 
		LDivision = @LDivision, 
		LRegion = @LRegion, 
		LUserDefined = @LUserDefined,
		ExceptionPct = @ExceptionPct,
		ReviewerId = @ReviewerId,
		LRank = @LRank
  	WHERE AllocDefaults.AllocDefaultsId = @AllocDefaultsId

  END

  -- Check for SQL Server errors.
  IF @@ERROR <> 0 
  BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  END
  ELSE
  BEGIN
 	RETURN 0
  END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

