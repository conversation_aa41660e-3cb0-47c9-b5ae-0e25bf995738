SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_MDCSummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_MDCSummary_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_MDCSummary_Sp
**	Desc: Retrieves the data from the MDCDetail table.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
    
CREATE PROCEDURE AIM_MDCSummary_Sp

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @KFactor 					decimal(5,2)
  
SET NOCOUNT ON
    
-- Get The K Factor from the System Control Table
SELECT @KFactor = KFactor 
FROM SysCtrl
    
-- Build the MDC Summary Work Table
SELECT MDCDetail.MDC, MDCDetail.Item, 
      SUM(MDCDetail.RqQty01) As RqQty01, SUM(MDCDetail.RqQty02) As RqQty02, 
      SUM(MDCDetail.RqQty03) As RqQty03, SUM(MDCDetail.RqQty04) As RqQty04, 
      SUM(MDCDetail.RqQty05) As RqQty05, SUM(MDCDetail.RqQty06) As RqQty06, 
      SUM(MDCDetail.RqQty07) As RqQty07, SUM(MDCDetail.RqQty08) As RqQty08, 
      SUM(MDCDetail.RqQty09) As RqQty09, SUM(MDCDetail.RqQty10) As RqQty10, 
      SUM(MDCDetail.RqQty11) As RqQty11, SUM(MDCDetail.RqQty12) As RqQty12, 
      SUM(MDCDetail.RqQty13) As RqQty13, SUM(MDCDetail.RqQty14) As RqQty14, 
      SUM(MDCDetail.RqQty15) As RqQty15, SUM(MDCDetail.RqQty16) As RqQty16, 
      SUM(MDCDetail.RqQty17) As RqQty17, SUM(MDCDetail.RqQty18) As RqQty18, 
      SUM(MDCDetail.RqQty19) As RqQty19, SUM(MDCDetail.RqQty20) As RqQty20, 
      SUM(MDCDetail.RqQty21) As RqQty21, SUM(MDCDetail.RqQty22) As RqQty22, 
      SUM(MDCDetail.RqQty23) As RqQty23, SUM(MDCDetail.RqQty24) As RqQty24, 
      SUM(MDCDetail.RqQty25) As RqQty25, SUM(MDCDetail.RqQty26) As RqQty26, 
      SUM(MDCDetail.FcstDemand) As FcstDemand, SUM(MDCDetail.MAE) As MAE, 
      SUM(MDCDetail.Trend) As Trend 
INTO #MDCWork
FROM MDCDetail 
GROUP BY MDCDetail.MDC, MDCDetail.Item 
Order by MDCDetail.MDC, MDCDetail.Item
    
-- Merge with the other AIM Tables
SELECT #MDCWork.MDC, #MDCWork.Item,  
        #MDCWork.RqQty01, #MDCWork.RqQty02, #MDCWork.RqQty03, #MDCWork.RqQty04, 
        #MDCWork.RqQty05, #MDCWork.RqQty06, #MDCWork.RqQty07, #MDCWork.RqQty08, 
        #MDCWork.RqQty09, #MDCWork.RqQty10, #MDCWork.RqQty11, #MDCWork.RqQty12, 
        #MDCWork.RqQty13, #MDCWork.RqQty14, #MDCWork.RqQty15, #MDCWork.RqQty16, 
        #MDCWork.RqQty17, #MDCWork.RqQty18, #MDCWork.RqQty19, #MDCWork.RqQty20, 
        #MDCWork.RqQty21, #MDCWork.RqQty22, #MDCWork.RqQty23, #MDCWork.RqQty24, 
        #MDCWork.RqQty25, #MDCWork.RqQty26, #MDCWork.FcstDemand, #MDCWork.MAE, #MDCWork.Trend,
        Item.LtvFact, Item.SSAdj, Item.ZSStock, Item.CStock, Item.ReplenCost2, Item.Dser,
        Item.Cost, Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02,
        Item.BkQty03, Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05,
        Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08,   
        Item.BkQty09, Item.BkCost09, Item.BkQty10, Item.BkCost10, Item.Accum_LT, Item.ReviewTime, 
        AIMLocations.ReplenCost,
        AIMOptions.LoMadP, AIMOptions.HiMadP, AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow,
        AIMOptions.MadExk,
        KFactor = @KFactor
FROM #MDCWork
        INNER JOIN Item On #MDCWork.MDC = Item.LcId AND #MDCWork.Item = Item.Item
        INNER JOIN AIMLocations On #MDCWork.MDC = AIMLocations.LcId    
        INNER JOIN AIMOptions On Item.OptionId = AIMOptions.OptionId

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

