SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxHS_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxHS_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxHS_Sp
**	Desc: Loads History Records into the AIM Item History Table..
**
** Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**			1)  0 - Successful
**              	2) -1 - No Data Found
**              	3) -2 - SQL Error
**              	4) -3 - Duplicate File Name
**              	5) -4 - Invalid File Name
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:	Description:
**    ---------- --------------	-----------------------------------------------
**    2003/05/28 A. Stocksdale	Replaced bulk insert and validation commands 
**				with call to AIM_DxBulkInsert_Sp - common to all 
**                              Dx processes.
**    2003/09/15 Srinivas U     Modified code for SubsItem
**    2005/03/08 Srinivas U	 Movied truncate and delete outside the commit
**				 Added truncate and delete before the bulk insert
*******************************************************************************/

CREATE PROCEDURE AIM_DxHs_Sp
(
      @FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE 
		@LcIdKey					nvarchar(12),
		@ItemKey					nvarchar(25),
		@SubsItemKey					nvarchar(25),
		@HisYearKey					smallint,
		@RowFound					int,
		@RtnCode              				int,
		@LcId						nvarchar(12), 
		@Item						nvarchar(25),
		@SubsItem					nvarchar(25),
		@HisYear					smallint, 
		@CPS01						decimal(9,1),
		@CPS02						decimal(9,1),
		@CPS03						decimal(9,1),
		@CPS04						decimal(9,1),
		@CPS05						decimal(9,1),
		@CPS06						decimal(9,1),
		@CPS07						decimal(9,1),
		@CPS08						decimal(9,1),
		@CPS09						decimal(9,1),
		@CPS10						decimal(9,1),
		@CPS11						decimal(9,1),
		@CPS12						decimal(9,1),
		@CPS13						decimal(9,1),
		@CPS14						decimal(9,1),
		@CPS15						decimal(9,1),
		@CPS16						decimal(9,1),
		@CPS17						decimal(9,1),
		@CPS18						decimal(9,1),
		@CPS19						decimal(9,1),
		@CPS20						decimal(9,1),
		@CPS21						decimal(9,1),
		@CPS22						decimal(9,1),
		@CPS23						decimal(9,1),
		@CPS24						decimal(9,1),
		@CPS25 						decimal(9,1),
		@CPS26						decimal(9,1),
		@CPS27						decimal(9,1),
		@CPS28						decimal(9,1),
		@CPS29						decimal(9,1),
		@CPS30						decimal(9,1),
		@CPS31						decimal(9,1),
		@CPS32						decimal(9,1),
		@CPS33						decimal(9,1),
		@CPS34						decimal(9,1),
		@CPS35						decimal(9,1),
		@CPS36						decimal(9,1),
		@CPS37						decimal(9,1),
		@CPS38						decimal(9,1),
		@CPS39						decimal(9,1),
		@CPS40						decimal(9,1),
		@CPS41						decimal(9,1),
		@CPS42						decimal(9,1),
		@CPS43						decimal(9,1),
		@CPS44						decimal(9,1),
		@CPS45						decimal(9,1),
		@CPS46						decimal(9,1),
		@CPS47						decimal(9,1),
		@CPS48						decimal(9,1),
		@CPS49						decimal(9,1),
		@CPS50						decimal(9,1),
		@CPS51						decimal(9,1),
		@CPS52						decimal(9,1),
		@QtyOrd01					decimal(9,1),
		@QtyOrd02					decimal(9,1),
		@QtyOrd03					decimal(9,1),
		@QtyOrd04					decimal(9,1),
		@QtyOrd05					decimal(9,1),
		@QtyOrd06					decimal(9,1),
		@QtyOrd07					decimal(9,1),
		@QtyOrd08					decimal(9,1),
		@QtyOrd09					decimal(9,1),
		@QtyOrd10					decimal(9,1),
		@QtyOrd11					decimal(9,1),
		@QtyOrd12					decimal(9,1),
		@QtyOrd13					decimal(9,1),
		@QtyOrd14					decimal(9,1),
		@QtyOrd15					decimal(9,1),
		@QtyOrd16					decimal(9,1),
		@QtyOrd17					decimal(9,1),
		@QtyOrd18					decimal(9,1),
		@QtyOrd19					decimal(9,1),
		@QtyOrd20 					decimal(9,1),
		@QtyOrd21					decimal(9,1),
		@QtyOrd22					decimal(9,1),
		@QtyOrd23					decimal(9,1),
		@QtyOrd24					decimal(9,1),
		@QtyOrd25					decimal(9,1),
		@QtyOrd26					decimal(9,1),
		@QtyOrd27					decimal(9,1),
		@QtyOrd28					decimal(9,1),
		@QtyOrd29					decimal(9,1),
		@QtyOrd30					decimal(9,1),
		@QtyOrd31					decimal(9,1),
		@QtyOrd32					decimal(9,1),
		@QtyOrd33					decimal(9,1),
		@QtyOrd34					decimal(9,1),    	
		@QtyOrd35					decimal(9,1),
		@QtyOrd36					decimal(9,1),
		@QtyOrd37					decimal(9,1),
		@QtyOrd38					decimal(9,1),
		@QtyOrd39					decimal(9,1),
		@QtyOrd40					decimal(9,1),
		@QtyOrd41					decimal(9,1),
		@QtyOrd42					decimal(9,1),
		@QtyOrd43					decimal(9,1),
		@QtyOrd44					decimal(9,1),
		@QtyOrd45					decimal(9,1),
		@QtyOrd46					decimal(9,1),
		@QtyOrd47					decimal(9,1),
		@QtyOrd48					decimal(9,1),
		@QtyOrd49					decimal(9,1),
		@QtyOrd50					decimal(9,1),
		@QtyOrd51					decimal(9,1),
		@QtyOrd52					decimal(9,1),
		@OrdCnt01					int,
		@OrdCnt02					int,
		@OrdCnt03					int,
		@OrdCnt04					int,
		@OrdCnt05					int,
		@OrdCnt06					int,
		@OrdCnt07					int,
		@OrdCnt08					int,
		@OrdCnt09					int,
		@OrdCnt10					int,
		@OrdCnt11					int,
		@OrdCnt12					int,
		@OrdCnt13					int,
		@OrdCnt14					int,
		@OrdCnt15					int,
		@OrdCnt16					int,
		@OrdCnt17					int,
		@OrdCnt18					int,
		@OrdCnt19					int,
		@OrdCnt20					int,
		@OrdCnt21					int,
		@OrdCnt22					int,
		@OrdCnt23					int,
		@OrdCnt24					int,
		@OrdCnt25					int,
		@OrdCnt26					int,
		@OrdCnt27					int,
		@OrdCnt28					int,
		@OrdCnt29					int,
		@OrdCnt30					int,
		@OrdCnt31					int,
		@OrdCnt32					int,
		@OrdCnt33					int,
		@OrdCnt34					int,
		@OrdCnt35					int,
		@OrdCnt36					int,
		@OrdCnt37					int,
		@OrdCnt38					int,
		@OrdCnt39					int,
		@OrdCnt40					int,
		@OrdCnt41					int,
		@OrdCnt42					int,
		@OrdCnt43					int,
		@OrdCnt44					int,
		@OrdCnt45					int,
		@OrdCnt46					int,
		@OrdCnt47					int,
		@OrdCnt48					int,
		@OrdCnt49					int,
		@OrdCnt50					int,
		@OrdCnt51					int,
		@OrdCnt52					int,
		-- SysCtrl Table
		@Unicode_Option					nvarchar(10)

	--Initialize counters
	SET @InsertCounter = 0
	SET @UpdateCounter = 0
	-- Delete records from the DxHs Table
	TRUNCATE TABLE AIMDxHs
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxHs
	BEGIN TRANSACTION
	-- This is a data interface transaction <Transaction Code=HS>. 
	-- Bulk Load the <Transaction Table=AIMDxHS> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'HS', 'AIMDxHS', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
        SELECT 0 AS 'Inserts', 
			0 AS 'Updates'
		ROLLBACK TRANSACTION
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- CREATE cursor to process DxHs
	DECLARE DxHs_Cursor CURSOR LOCAL FAST_FORWARD FOR 
    	SELECT LcId, Item,SubsItem =CASE WHEN (SubsItem ='')THEN Item  ElSE  SubsItem END, HisYear, 
    		CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, CPS10, 
    		CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, 
    		CPS21, CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, 
    		CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, 
    		CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, CPS50, 
    		CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, 
    		QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, 
    		QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, 
    		QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, 
    		QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38,     		
		QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, QtyOrd45, QtyOrd46, 
    		QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, OrdCnt02, 
    		OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, 
    		OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, 
    		OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, 
    		OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, 
    		OrdCnt35, OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, 
    		OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, 
    		OrdCnt51, OrdCnt52 
    		FROM AIMDxHS
	OPEN DxHs_Cursor

	-- Check for no rows to process
	IF @@CURSOR_ROWS = 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	-- Update Loop
	WHILE 1 = 1 
	BEGIN 
    	FETCH NEXT FROM DxHs_Cursor INTO 
    		@LcId, @Item,@SubsItem, @HisYear, 
    		@CPS01, @CPS02, @CPS03, @CPS04, @CPS05, @CPS06, @CPS07, @CPS08, @CPS09, @CPS10, 
    		@CPS11, @CPS12, @CPS13, @CPS14, @CPS15, @CPS16, @CPS17, @CPS18, @CPS19, @CPS20, 
    		@CPS21, @CPS22, @CPS23, @CPS24, @CPS25, @CPS26, @CPS27, @CPS28, @CPS29, @CPS30, 
    		@CPS31, @CPS32, @CPS33, @CPS34, @CPS35, @CPS36, @CPS37, @CPS38, @CPS39, @CPS40, 
    		@CPS41, @CPS42, @CPS43, @CPS44, @CPS45, @CPS46, @CPS47, @CPS48, @CPS49, @CPS50, 
    		@CPS51, @CPS52, @QtyOrd01, @QtyOrd02, @QtyOrd03, @QtyOrd04, @QtyOrd05, @QtyOrd06, 
    		@QtyOrd07, @QtyOrd08, @QtyOrd09, @QtyOrd10, @QtyOrd11, @QtyOrd12, @QtyOrd13, @QtyOrd14, 
    		@QtyOrd15, @QtyOrd16, @QtyOrd17, @QtyOrd18, @QtyOrd19, @QtyOrd20, @QtyOrd21, @QtyOrd22, 
    		@QtyOrd23, @QtyOrd24, @QtyOrd25, @QtyOrd26, @QtyOrd27, @QtyOrd28, @QtyOrd29, @QtyOrd30, 
    		@QtyOrd31, @QtyOrd32, @QtyOrd33, @QtyOrd34, @QtyOrd35, @QtyOrd36, @QtyOrd37, @QtyOrd38, 
    		@QtyOrd39, @QtyOrd40, @QtyOrd41, @QtyOrd42, @QtyOrd43, @QtyOrd44, @QtyOrd45, @QtyOrd46, 
    		@QtyOrd47, @QtyOrd48, @QtyOrd49, @QtyOrd50, @QtyOrd51, @QtyOrd52, @OrdCnt01, @OrdCnt02, 
    		@OrdCnt03, @OrdCnt04, @OrdCnt05, @OrdCnt06, @OrdCnt07, @OrdCnt08, @OrdCnt09, @OrdCnt10, 
    		@OrdCnt11, @OrdCnt12, @OrdCnt13, @OrdCnt14, @OrdCnt15, @OrdCnt16, @OrdCnt17, @OrdCnt18, 
    		@OrdCnt19, @OrdCnt20, @OrdCnt21, @OrdCnt22, @OrdCnt23, @OrdCnt24, @OrdCnt25, @OrdCnt26, 
    		@OrdCnt27, @OrdCnt28, @OrdCnt29, @OrdCnt30, @OrdCnt31, @OrdCnt32, @OrdCnt33, @OrdCnt34, 
    		@OrdCnt35, @OrdCnt36, @OrdCnt37, @OrdCnt38, @OrdCnt39, @OrdCnt40, @OrdCnt41, @OrdCnt42, 
    		@OrdCnt43, @OrdCnt44, @OrdCnt45, @OrdCnt46, @OrdCnt47, @OrdCnt48, @OrdCnt49, @OrdCnt50, 
    		@OrdCnt51, @OrdCnt52
    		
	  	-- Check Fetch Status
	  	IF @@FETCH_STATUS <> 0
		BEGIN
	    		break
		END
		--If SubsItem is null set it to Item value
		Select @SubsItem = isnull(@SubsItem,@Item)
		If @item <>@subsitem
		BEGIN
		-- If the Item and subsiem are not same i.e for subiems set the qtyord and ordcnt to zero
		select @QtyOrd01 =0, @QtyOrd02=0, @QtyOrd03=0, @QtyOrd04=0, @QtyOrd05=0, @QtyOrd06=0, 
	    		@QtyOrd07=0, @QtyOrd08=0, @QtyOrd09=0, @QtyOrd10=0, @QtyOrd11=0, @QtyOrd12=0, @QtyOrd13=0, @QtyOrd14=0, 
	    		@QtyOrd15=0, @QtyOrd16=0, @QtyOrd17=0, @QtyOrd18=0, @QtyOrd19=0, @QtyOrd20=0, @QtyOrd21=0, @QtyOrd22=0, 
	    		@QtyOrd23=0, @QtyOrd24=0, @QtyOrd25=0, @QtyOrd26=0, @QtyOrd27=0, @QtyOrd28=0, @QtyOrd29=0, @QtyOrd30=0, 
	    		@QtyOrd31=0, @QtyOrd32=0, @QtyOrd33=0, @QtyOrd34=0, @QtyOrd35=0, @QtyOrd36=0, @QtyOrd37=0, @QtyOrd38=0, 
	    		@QtyOrd39=0, @QtyOrd40=0, @QtyOrd41=0, @QtyOrd42=0, @QtyOrd43=0, @QtyOrd44=0, @QtyOrd45=0, @QtyOrd46=0, 
	    		@QtyOrd47=0, @QtyOrd48=0, @QtyOrd49=0, @QtyOrd50=0, @QtyOrd51=0, @QtyOrd52=0, @OrdCnt01=0, @OrdCnt02=0, 
	    		@OrdCnt03=0, @OrdCnt04=0, @OrdCnt05=0, @OrdCnt06=0, @OrdCnt07=0, @OrdCnt08=0, @OrdCnt09=0, @OrdCnt10=0, 
	    		@OrdCnt11=0, @OrdCnt12=0, @OrdCnt13=0, @OrdCnt14=0, @OrdCnt15=0, @OrdCnt16=0, @OrdCnt17=0, @OrdCnt18=0, 
	    		@OrdCnt19=0, @OrdCnt20=0, @OrdCnt21=0, @OrdCnt22=0, @OrdCnt23=0, @OrdCnt24=0, @OrdCnt25=0, @OrdCnt26=0, 
	    		@OrdCnt27=0, @OrdCnt28=0, @OrdCnt29=0, @OrdCnt30=0, @OrdCnt31=0, @OrdCnt32=0, @OrdCnt33=0, @OrdCnt34=0, 
	    		@OrdCnt35=0, @OrdCnt36=0, @OrdCnt37=0, @OrdCnt38=0, @OrdCnt39=0, @OrdCnt40=0, @OrdCnt41=0, @OrdCnt42=0, 
	    		@OrdCnt43=0, @OrdCnt44=0, @OrdCnt45=0, @OrdCnt46=0, @OrdCnt47=0, @OrdCnt48=0, @OrdCnt49=0, @OrdCnt50=0, 
	    		@OrdCnt51=0, @OrdCnt52=0 
		END
	        -- Check to see IF the row already exists in the vendor table
	    	SELECT @LcIdKey = LcId, @ItemKey = Item,@SubsItemKey =SubsItem, @HisYearKey = HisYear
	    		FROM ItemHistory
	    		WHERE Lcid = @LcId
	    		and Item = @Item
			and SubsItem =@SubsItem
	    		and HisYear = @HisYear
	        SET @RowFound = @@rowcount
	        -- New Item History Row  
	        IF @RowFound = 0		
	        BEGIN
	    		-- Insert the new row into the Item History Table
	    		INSERT INTO ItemHistory(LcId, Item,SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, 
	    			CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, 
	    			CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, 
	    			CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, CPS50, 
	    			CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, 
	    			QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, 
	    			QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, 
	    			QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35,     			QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, 
	    			QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, 
	    			OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, 
	    			OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, 
	    			OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, 
	    			OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, 
	    			OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, 
	    			OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52) 
    			VALUES (@LcId, @Item,@SubsItem, @HisYear, @CPS01, @CPS02, @CPS03, @CPS04, @CPS05, @CPS06, @CPS07, @CPS08, 
	    			@CPS09, @CPS10, @CPS11, @CPS12, @CPS13, @CPS14, @CPS15, @CPS16, @CPS17, @CPS18, @CPS19, @CPS20, @CPS21, @CPS22, 
	    			@CPS23, @CPS24, @CPS25, @CPS26, @CPS27, @CPS28, @CPS29, @CPS30, @CPS31, @CPS32, @CPS33, @CPS34, @CPS35, @CPS36, 
	    			@CPS37, @CPS38, @CPS39, @CPS40, @CPS41, @CPS42, @CPS43, @CPS44, @CPS45, @CPS46, @CPS47, @CPS48, @CPS49, @CPS50, 
	    			@CPS51, @CPS52, @QtyOrd01, @QtyOrd02, @QtyOrd03, @QtyOrd04, @QtyOrd05, @QtyOrd06, @QtyOrd07, @QtyOrd08, 
	    			@QtyOrd09, @QtyOrd10, @QtyOrd11, @QtyOrd12, @QtyOrd13, @QtyOrd14, @QtyOrd15, @QtyOrd16, @QtyOrd17, 
	    			@QtyOrd18, @QtyOrd19, @QtyOrd20, @QtyOrd21, @QtyOrd22, @QtyOrd23, @QtyOrd24, @QtyOrd25, @QtyOrd26, 
	    			@QtyOrd27, @QtyOrd28, @QtyOrd29, @QtyOrd30, @QtyOrd31, @QtyOrd32, @QtyOrd33, @QtyOrd34, @QtyOrd35, 
	    			@QtyOrd36, @QtyOrd37, @QtyOrd38, @QtyOrd39, @QtyOrd40, @QtyOrd41, @QtyOrd42, @QtyOrd43, @QtyOrd44, 
	    			@QtyOrd45, @QtyOrd46, @QtyOrd47, @QtyOrd48, @QtyOrd49, @QtyOrd50, @QtyOrd51, @QtyOrd52, @OrdCnt01, 
	    			@OrdCnt02, @OrdCnt03, @OrdCnt04, @OrdCnt05, @OrdCnt06, @OrdCnt07, @OrdCnt08, @OrdCnt09, @OrdCnt10, 
	    			@OrdCnt11, @OrdCnt12, @OrdCnt13, @OrdCnt14, @OrdCnt15, @OrdCnt16, @OrdCnt17, @OrdCnt18, @OrdCnt19, 
	    			@OrdCnt20, @OrdCnt21, @OrdCnt22, @OrdCnt23, @OrdCnt24, @OrdCnt25, @OrdCnt26, @OrdCnt27, @OrdCnt28, 
	    			@OrdCnt29, @OrdCnt30, @OrdCnt31, @OrdCnt32, @OrdCnt33, @OrdCnt34, @OrdCnt35, @OrdCnt36, @OrdCnt37, 
	    			@OrdCnt38, @OrdCnt39, @OrdCnt40, @OrdCnt41, @OrdCnt42, @OrdCnt43, @OrdCnt44, @OrdCnt45, @OrdCnt46, 
	    			@OrdCnt47, @OrdCnt48, @OrdCnt49, @OrdCnt50, @OrdCnt51, @OrdCnt52)
	     		--Increment Add Counter
	    		SET @InsertCounter = @InsertCounter + 1
	    	END
	    	ELSE
	    	BEGIN
	        	-- Update the Item History Table 
	    		UPDATE ItemHistory 
	    			SET LcId = @LcId, Item = @Item,SubsItem =@SubsItem, HisYear = @HisYear, 
	    			CPS01 = @CPS01, CPS02 = @CPS02, CPS03 = @CPS03, CPS04 = @CPS04, CPS05 = @CPS05, CPS06 = @CPS06, 
	    			CPS07 = @CPS07, CPS08 = @CPS08, CPS09 = @CPS09, CPS10 = @CPS10, CPS11 = @CPS11, CPS12 = @CPS12, 
	    			CPS13 = @CPS13, CPS14 = @CPS14, CPS15 = @CPS15, CPS16 = @CPS16, CPS17 = @CPS17, CPS18 = @CPS18, 
	    			CPS19 = @CPS19, CPS20 = @CPS20, CPS21 = @CPS21, CPS22 = @CPS22, CPS23 = @CPS23, CPS24 = @CPS24, 
	    			CPS25 = @CPS25, CPS26 = @CPS26, CPS27 = @CPS27, CPS28 = @CPS28, CPS29 = @CPS29, CPS30 = @CPS30, 
	    			CPS31 = @CPS31, CPS32 = @CPS32, CPS33 = @CPS33, CPS34 = @CPS34, CPS35 = @CPS35, CPS36 = @CPS36, 
	    			CPS37 = @CPS37, CPS38 = @CPS38, CPS39 = @CPS39, CPS40 = @CPS40, CPS41 = @CPS41, CPS42 = @CPS42, 
	    			CPS43 = @CPS43, CPS44 = @CPS44, CPS45 = @CPS45, CPS46 = @CPS46, CPS47 = @CPS47, CPS48 = @CPS48, 
	    			CPS49 = @CPS49, CPS50 = @CPS50, CPS51 = @CPS51, CPS52 = @CPS52, 
	    			QtyOrd01 = @QtyOrd01, QtyOrd02 = @QtyOrd02, QtyOrd03 = @QtyOrd03, QtyOrd04 = @QtyOrd04, 
	    			QtyOrd05 = @QtyOrd05, QtyOrd06 = @QtyOrd06, QtyOrd07 = @QtyOrd07, QtyOrd08 = @QtyOrd08, 
	    			QtyOrd09 = @QtyOrd09, QtyOrd10 = @QtyOrd10, QtyOrd11 = @QtyOrd11, QtyOrd12 = @QtyOrd12, 
	    			QtyOrd13 = @QtyOrd13, QtyOrd14 = @QtyOrd14, QtyOrd15 = @QtyOrd15, QtyOrd16 = @QtyOrd16, 
	    			QtyOrd17 = @QtyOrd17, QtyOrd18 = @QtyOrd18, QtyOrd19 = @QtyOrd19, QtyOrd20 = @QtyOrd20, 
	    			QtyOrd21 = @QtyOrd21, QtyOrd22 = @QtyOrd22, QtyOrd23 = @QtyOrd23, QtyOrd24 = @QtyOrd24, 
	    			QtyOrd25 = @QtyOrd25, QtyOrd26 = @QtyOrd26, QtyOrd27 = @QtyOrd27, QtyOrd28 = @QtyOrd28, 
	    			QtyOrd29 = @QtyOrd29, QtyOrd30 = @QtyOrd30, QtyOrd31 = @QtyOrd31, QtyOrd32 = @QtyOrd32, 
	    			QtyOrd33 = @QtyOrd33, QtyOrd34 = @QtyOrd34, QtyOrd35 = @QtyOrd35, QtyOrd36 = @QtyOrd36, 
	    			QtyOrd37 = @QtyOrd37, QtyOrd38 = @QtyOrd38, QtyOrd39 = @QtyOrd39, QtyOrd40 = @QtyOrd40,
	    			QtyOrd41 = @QtyOrd41, QtyOrd42 = @QtyOrd42, QtyOrd43 = @QtyOrd43, QtyOrd44 = @QtyOrd44, 
	    			QtyOrd45 = @QtyOrd45, QtyOrd46 = @QtyOrd46, QtyOrd47 = @QtyOrd47, QtyOrd48 = @QtyOrd48, 
	    			QtyOrd49 = @QtyOrd49, QtyOrd50 = @QtyOrd50, QtyOrd51 = @QtyOrd51, QtyOrd52 = @QtyOrd52, 
	    			OrdCnt01 = @OrdCnt01, OrdCnt02 = @OrdCnt02, OrdCnt03 = @OrdCnt03, OrdCnt04 = @OrdCnt04, 
	    			OrdCnt05 = @OrdCnt05, OrdCnt06 = @OrdCnt06, OrdCnt07 = @OrdCnt07, OrdCnt08 = @OrdCnt08, 
	    			OrdCnt09 = @OrdCnt09, OrdCnt10 = @OrdCnt10, OrdCnt11 = @OrdCnt11, OrdCnt12 = @OrdCnt12, 
	    			OrdCnt13 = @OrdCnt13, OrdCnt14 = @OrdCnt14, OrdCnt15 = @OrdCnt15, OrdCnt16 = @OrdCnt16, 
	    			OrdCnt17 = @OrdCnt17, OrdCnt18 = @OrdCnt18, OrdCnt19 = @OrdCnt19, OrdCnt20 = @OrdCnt20, 
	    			OrdCnt21 = @OrdCnt21, OrdCnt22 = @OrdCnt22, OrdCnt23 = @OrdCnt23, OrdCnt24 = @OrdCnt24, 
	    			OrdCnt25 = @OrdCnt25, OrdCnt26 = @OrdCnt26, OrdCnt27 = @OrdCnt27, OrdCnt28 = @OrdCnt28, 
	    			OrdCnt29 = @OrdCnt29, OrdCnt30 = @OrdCnt30, OrdCnt31 = @OrdCnt31, OrdCnt32 = @OrdCnt32, 
	    			OrdCnt33 = @OrdCnt33, OrdCnt34 = @OrdCnt34, OrdCnt35 = @OrdCnt35, OrdCnt36 = @OrdCnt36, 
	    			OrdCnt37 = @OrdCnt37, OrdCnt38 = @OrdCnt38, OrdCnt39 = @OrdCnt39, OrdCnt40 = @OrdCnt40, 
	    			OrdCnt41 = @OrdCnt41, OrdCnt42 = @OrdCnt42, OrdCnt43 = @OrdCnt43, OrdCnt44 = @OrdCnt44, 
	    			OrdCnt45 = @OrdCnt45, OrdCnt46 = @OrdCnt46, OrdCnt47 = @OrdCnt47, OrdCnt48 = @OrdCnt48, 
	    			OrdCnt49 = @OrdCnt49, OrdCnt50 = @OrdCnt50, OrdCnt51 = @OrdCnt51, OrdCnt52 = @OrdCnt52		
    			WHERE LcId = @Lcid
    			and Item = @Item
			and SubsItem =@SubsItem
    			and HisYear = @HisYear
	    			
	    		-- Increment Update Count	
	    		IF @@ROWCOUNT <> 0 
				BEGIN
	    			SET @UpdateCounter = @UpdateCounter + 1
			END
		END
	END		

	-- Deallocate the cursor
	CLOSE DxHs_Cursor
	DEALLOCATE DxHs_Cursor
	
	COMMIT TRANSACTION
	-- Delete records from the DxHs Table
	TRUNCATE TABLE AIMDxHs
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxHs

	UPDATE STATISTICS ItemHistory WITH RESAMPLE, ALL
	
	RETURN 1	-- Succeed

END
GO

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

