if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstAccess_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstAccess_Fetch_Sp]
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



/*******************************************************************************
**	Name: AIM_DmdPlanFcstAccess_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**		FcstID == a valid AIMFcst.Forecast ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE  PROCEDURE AIM_DmdPlanFcstAccess_Fetch_Sp 
(
	@FcstSetupKey as int,
	@UserID as nvarchar(12) = NULL
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	OR @UserID IS NULL
	BEGIN
		RETURN -1
	END
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
--	BEGIN

		SELECT 
			AIMFcstAccess.FcstSetupKey,
			UserID = CASE 
				WHEN UPPER(@UserID) = 'SA' THEN 'SA'
				ELSE AIMFcstAccess.UserID END, 
			UserName = CASE 
				WHEN AIMFcstAccess.UserID IS NOT NULL THEN (SELECT AIMUsers.UserName FROM AIMUsers WHERE AIMUsers.UserID = AIMFcstAccess.UserID) 
				ELSE '' END,
			AccessCode = CASE WHEN UPPER(@UserID) = 'SA' AND AIMFcstAccess.AccessCode IS NULL THEN 0 ELSE AIMFcstAccess.AccessCode END
		FROM AIMFcstAccess
		WHERE AIMFcstAccess.FcstSetupKey = @FcstSetupKey 
		AND AIMFcstAccess.UserID  = CASE 
			WHEN UPPER(@UserID) = 'ALL' THEN AIMFcstAccess.UserID
			WHEN (UPPER(@UserID) = 'SA' AND AIMFcstAccess.AccessCode IS NOT NULL) THEN @UserID 
			WHEN (UPPER(@UserID) = 'SA' AND AIMFcstAccess.AccessCode IS NULL) THEN AIMFcstAccess.UserID
			ELSE @UserID
			END
		GROUP BY
			FcstSetupKey,
			UserID,
			AccessCode
		ORDER BY
			FcstSetupKey,
			UserID,
			AccessCode

		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat > 0 
		BEGIN
			RETURN @Return_Stat
		END
		ELSE
		BEGIN
			RETURN -1
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

