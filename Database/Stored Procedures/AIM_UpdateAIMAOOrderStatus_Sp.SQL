SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO


IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_UpdateAIMAOOrderStatus_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_UpdateAIMAOOrderStatus_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONF<PERSON>ENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:			Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:			Sub called by AllocationInventory
**		* AIM_GetSessionID_Sp:			Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:		Sub called by AllocationCtrl 
**      	* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**              * AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp			Main Stored Procedure
**
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**		Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_UpdateAIMAOOrderStatus_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Updates the record status in AIMAO and AIMAODetail tables
**			AIMAO.OrderStatus
**			AIMAO.LineItemStatus
**	Parameters:	
**		@NewOrderStatus -- what to set the record statuses to in the update
**		Valid values= 
**		0 - Pending         - First stage of the lifecycle. 
**		                      The Order has been received as an inbound, and is 
**                                    awaiting pre-allocation review and allocation.
**		1 - Do not Allocate - The Order has been subjected to the pre-allocation review, 
**				      and has been excluded from the allocation process by the reviewer.
**		10 - Allocated      - The Order that was Pending has been processed for allocation  
**				      and is now awaiting post-allocation review and execution as an 
**                                    outbound interface.
**		11 - Not Allocated  - The Order that was excluded from getting quantities allocated 
**                                    (Do not Allocate) is no longer valid for further processing 
**                                    of any variety, and is now awaiting execution as an outbound 
**                                    interface with an allocated quantity of zero.
**		12 - Allocated with exceptions - The Order that was Allocated has been marked for review 
**				      as having a fillpercentage less than the exception trigger defined 
**                                    for the order/item and is now awaiting post-allocation review.
**		20 - Released       - The Order has been subjected to post-allocation review,  
**				      or been otherwise marked for execution as an outbound interface.
**		22 - Released with exceptions - The Order that was "Allocated with Exceptions" 
**				      has been subjected to post-allocation review, or been otherwise marked 
**				      for execution as an outbound interface.
**		30 - Completed      - The Order that was Released or Not Allocated.  
**				      has been processed as an outbound file to be sent to the host.  
**				      This is the last stage of the lifecycle
**
**		@WhereStatusIn(1 to N)	-- a range of statuses that should be replaced, of which only the first is required.
**		variables 2 to n may have any of the values defined above.
**		@UserID -- the person running the process that is updating the order status
**
**				
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/21
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Updated by:	Description:
**	---------- ------------	-----------------------------------------------
**	2003/09/25 A.Stocksdale	Modified to include AIMAO_OrderInfo							
*******************************************************************************/

CREATE PROCEDURE AIM_UpdateAIMAOOrderStatus_Sp
(
  	@UserID as NVARCHAR(255),
	@NewOrderStatus AS TINYINT,
	@WhereStatus1 AS TINYINT,
	@WhereStatus2 AS TINYINT = 99,
	@WhereStatus3 AS TINYINT = 99,
	@WhereStatus4 AS TINYINT = 99,
	@WhereStatus5 AS TINYINT = 99,
	@WhereStatus6 AS TINYINT = 99,
	@WhereStatus7 AS TINYINT = 99,
	@WhereStatus8 AS TINYINT = 99
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @UserInitials AS NVARCHAR(3)
	
	SET NOCOUNT ON

	IF @UserID IS NULL
	OR @NewOrderStatus IS NULL
	OR @WhereStatus1 IS NULL
	BEGIN
		RETURN -1
	END

	If (@WhereStatus2 IS NULL)
	BEGIN
		SET @WhereStatus2 = 99
	END
	If (@WhereStatus3 IS NULL)
	BEGIN
		SET @WhereStatus3 = 99
	END
	If (@WhereStatus4 IS NULL)
	BEGIN
		SET @WhereStatus4 = 99
	END
	If (@WhereStatus5 IS NULL)
	BEGIN
		SET @WhereStatus5 = 99
	END
	If (@WhereStatus6 IS NULL)
	BEGIN
		SET @WhereStatus6 = 99
	END
	If (@WhereStatus7 IS NULL)
	BEGIN
		SET @WhereStatus7 = 99
	END
	If (@WhereStatus8 IS NULL)
	BEGIN
		SET @WhereStatus8 = 99
	END
	
	SELECT @UserInitials = AIMUsers.UserInitials FROM AIMUsers WHERE AIMUsers.UserID = @UserID
	IF @UserInitials IS NULL
	BEGIN
		SET @UserInitials = ''
	END

	UPDATE AIMAO_OrderInfo
		SET OrderStatus = @NewOrderStatus
		, UserInitials = @UserInitials
		WHERE OrderStatus IN 
			(@WhereStatus1, @WhereStatus2, @WhereStatus3, @WhereStatus4
			, @WhereStatus5, @WhereStatus6, @WhereStatus7, @WhereStatus8)

	UPDATE AIMAO
		SET OrderStatus = @NewOrderStatus
		, UserInitials = @UserInitials
		WHERE OrderStatus IN 
			(@WhereStatus1, @WhereStatus2, @WhereStatus3, @WhereStatus4
			, @WhereStatus5, @WhereStatus6, @WhereStatus7, @WhereStatus8)
		
	UPDATE AIMAODetail
		SET LineItemStatus = @NewOrderStatus
		WHERE LineItemStatus IN 
			(@WhereStatus1, @WhereStatus2, @WhereStatus3, @WhereStatus4
			, @WhereStatus5, @WhereStatus6, @WhereStatus7, @WhereStatus8)

END
GO

/*******************************************************************************
-- FINISH AIM_UpdateAIMAOOrderStatus_Sp
*******************************************************************************/
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

