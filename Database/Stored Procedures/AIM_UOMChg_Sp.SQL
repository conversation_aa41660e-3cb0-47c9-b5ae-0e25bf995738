SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UOMChg_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UOMChg_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UOMChg_Sp
**	Desc: Makes all the changes associated with changing a UOM in the Item 
**          Table.
**
**	Returns: 1)1 - Succeed
**               2)0 - Failure
**             
**	Values:  Recordset - PODetail
**              
**	Auth:    Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  	09/12/2003 Srinivas U	Modified code for SubsItem No change
*******************************************************************************/
     
CREATE PROCEDURE AIM_UOMChg_Sp 
(
	@LcID nvarchar(12), 
  	@Item nvarchar(25), 
  	@NewUOM nvarchar(6),
  	@OldUOM nvarchar(6),
    @UOMFact decimal(10,4) 
)  	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
    	
	DECLARE @Item_UOM nvarchar(6),
	        @Item_Found int
	
	-- Check for all the conditions before executing the UOM update logic.
	--   - the Item must exist
	--   - the Item.UOM <> ss.UOM
	--   - the Item.UOM =  ss.oldUOM, and
	--   - the ss.conversion factor > 0
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @LcID IS NULL
	OR @Item IS NULL
	OR @NewUOM IS NULL
	OR @OldUOM IS NULL
	OR @UOMFact IS NULL
	BEGIN
		RETURN 0
	END
	  
	SELECT @Item_UOM = UOM
	FROM Item
	WHERE LcID = @LcID
	AND Item = @Item
	
	SELECT @Item_Found = @@ROWCOUNT
	
	IF @Item_Found = 0 
	OR @Item_UOM = @NewUOM 
	OR @Item_UOM <> @OldUOM 
	OR @UOMFact = 0
	BEGIN
		RETURN 0			-- FAIL 	
	END
	ELSE
	BEGIN
		-- Scale the Item values and update the Item Table
		UPDATE Item SET
			UOM = @NewUOM,
	 		iMin = iMin * @UOMFact, 
			iMax = CASE
				WHEN iMax = 999999999 THEN 999999999
				ELSE iMax * @UOMFact
				END,
			CStock = CStock * @UOMFact,
			UserMin = UserMin * @UOMFact,
			UserMax = CASE
				WHEN UserMax = 999999999 THEN 999999999
				ELSE UserMax * @UOMFact
				END,
			FcstDemand = FcstDemand * @UOMFact,
			UserFcst = UserFcst * @UOMFact,
			MAE = MAE * @UOMFact,
			MSE = MSE * @UOMFact,
			Trend = Trend * @UOMFact,
			OrderPt = OrderPt * @UOMFact,
			OrderQty = OrderQty * @UOMFact,
			SafetyStock = SafetyStock * @UOMFact,
			FcstRt = FcstRt * @UOMFact,
			FcstLt = FcstLt * @UOMFact,
			Fcst_Month = Fcst_Month * @UOMFact,
			Fcst_Quarter = Fcst_Quarter * @UOMFact,
			Fcst_Year = Fcst_Year * @UOMFact,
			AvgOh = AvgOh * @UOMFact 
		WHERE LcID = @LcID
		AND Item = @Item
		
		-- Update the History Table
		UPDATE ItemHistory SET CPS01 = CPS01 * @UOMFact, CPS02 = CPS02 * @UOMFact, CPS03 = CPS03 * @UOMFact, 
			CPS04 = CPS04 * @UOMFact, CPS05 = CPS05 * @UOMFact, CPS06 = CPS06 * @UOMFact, CPS07 = CPS07 * @UOMFact, 
		    CPS08 = CPS08 * @UOMFact, CPS09 = CPS09 * @UOMFact, CPS10 = CPS10 * @UOMFact, CPS11 = CPS11 * @UOMFact, 
		    CPS12 = CPS12 * @UOMFact, CPS13 = CPS13 * @UOMFact, CPS14 = CPS14 * @UOMFact, CPS15 = CPS15 * @UOMFact, 
		    CPS16 = CPS16 * @UOMFact, CPS17 = CPS17 * @UOMFact, CPS18 = CPS18 * @UOMFact, CPS19 = CPS19 * @UOMFact, 
		    CPS20 = CPS20 * @UOMFact, CPS21 = CPS21 * @UOMFact, CPS22 = CPS22 * @UOMFact, CPS23 = CPS23 * @UOMFact, 
		    CPS24 = CPS24 * @UOMFact, CPS25 = CPS25 * @UOMFact, CPS26 = CPS26 * @UOMFact, CPS27 = CPS27 * @UOMFact, 
		    CPS28 = CPS28 * @UOMFact, CPS29 = CPS29 * @UOMFact, CPS30 = CPS30 * @UOMFact, CPS31 = CPS31 * @UOMFact, 
		    CPS32 = CPS32 * @UOMFact, CPS33 = CPS33 * @UOMFact, CPS34 = CPS34 * @UOMFact, CPS35 = CPS35 * @UOMFact, 
		    CPS36 = CPS36 * @UOMFact, CPS37 = CPS37 * @UOMFact, CPS38 = CPS38 * @UOMFact, CPS39 = CPS39 * @UOMFact, 
		    CPS40 = CPS40 * @UOMFact, CPS41 = CPS41 * @UOMFact, CPS42 = CPS42 * @UOMFact, CPS43 = CPS43 * @UOMFact, 
		    CPS44 = CPS44 * @UOMFact, CPS45 = CPS45 * @UOMFact, CPS46 = CPS46 * @UOMFact, CPS47 = CPS47 * @UOMFact, 
		    CPS48 = CPS48 * @UOMFact, CPS49 = CPS49 * @UOMFact, CPS50 = CPS50 * @UOMFact, CPS51 = CPS51 * @UOMFact, 
		    CPS52 = CPS52 * @UOMFact, QtyOrd01 = QtyOrd01 * @UOMFact, QtyOrd02 = QtyOrd02 * @UOMFact, 
		    QtyOrd03 = QtyOrd03 * @UOMFact, QtyOrd04 = QtyOrd04 * @UOMFact, QtyOrd05 = QtyOrd05 * @UOMFact, 
		    QtyOrd06 = QtyOrd06 * @UOMFact, QtyOrd07 = QtyOrd07 * @UOMFact, QtyOrd08 = QtyOrd08 * @UOMFact, 
		    QtyOrd09 = QtyOrd09 * @UOMFact, QtyOrd10 = QtyOrd10 * @UOMFact, QtyOrd11 = QtyOrd11 * @UOMFact, 
		    QtyOrd12 = QtyOrd12 * @UOMFact, QtyOrd13 = QtyOrd13 * @UOMFact, QtyOrd14 = QtyOrd14 * @UOMFact, 
		    QtyOrd15 = QtyOrd15 * @UOMFact, QtyOrd16 = QtyOrd16 * @UOMFact, QtyOrd17 = QtyOrd17 * @UOMFact, 
		    QtyOrd18 = QtyOrd18 * @UOMFact, QtyOrd19 = QtyOrd19 * @UOMFact, QtyOrd20 = QtyOrd20 * @UOMFact, 
		    QtyOrd21 = QtyOrd21 * @UOMFact, QtyOrd22 = QtyOrd22 * @UOMFact, QtyOrd23 = QtyOrd23 * @UOMFact, 
		    QtyOrd24 = QtyOrd24 * @UOMFact, QtyOrd25 = QtyOrd25 * @UOMFact, QtyOrd26 = QtyOrd26 * @UOMFact, 
		    QtyOrd27 = QtyOrd27 * @UOMFact, QtyOrd28 = QtyOrd28 * @UOMFact, QtyOrd29 = QtyOrd29 * @UOMFact, 
		    QtyOrd30 = QtyOrd30 * @UOMFact, QtyOrd31 = QtyOrd31 * @UOMFact, QtyOrd32 = QtyOrd32 * @UOMFact, 
		    QtyOrd33 = QtyOrd33 * @UOMFact, QtyOrd34 = QtyOrd34 * @UOMFact, QtyOrd35 = QtyOrd35 * @UOMFact, 
		    QtyOrd36 = QtyOrd36 * @UOMFact, QtyOrd37 = QtyOrd37 * @UOMFact, QtyOrd38 = QtyOrd38 * @UOMFact, 
		    QtyOrd39 = QtyOrd39 * @UOMFact, QtyOrd40 = QtyOrd40 * @UOMFact, QtyOrd41 = QtyOrd41 * @UOMFact, 
		    QtyOrd42 = QtyOrd42 * @UOMFact, QtyOrd43 = QtyOrd43 * @UOMFact, QtyOrd44 = QtyOrd44 * @UOMFact, 
		    QtyOrd45 = QtyOrd45 * @UOMFact, QtyOrd46 = QtyOrd46 * @UOMFact, QtyOrd47 = QtyOrd47 * @UOMFact, 
		    QtyOrd48 = QtyOrd48 * @UOMFact, QtyOrd49 = QtyOrd49 * @UOMFact, QtyOrd50 = QtyOrd50 * @UOMFact, 
		    QtyOrd51 = QtyOrd51 * @UOMFact, QtyOrd52 = QtyOrd52 * @UOMFact
		WHERE ItemHistory.LcId = @LcID 
		AND ItemHistory.Item = @Item
	
		RETURN 1	-- Succeed
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

