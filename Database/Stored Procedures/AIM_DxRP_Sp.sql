SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxRP_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxRP_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxRP_Sp
**	Desc: Loads data exchange item records into the Re-stock Profile tables
**          from the AIMDxRP Table. 
**
**	Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**		        1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**                      4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Updated by:	  Description:
**	---------- -------------- -----------------------------------------------
**	2003/06/25 Srinivas U	  Get Defaults from AIMLocations						
**	2003/08/21 A. Stocksdale  Modified to replace the cursor with update-set- 
**                                and insert-into-select-froms.
**      2004/03/05 S.Uddanti	 Add delete from AIMDxRP so that the procedure works 
**                               even when there are some updates and some inserts
**    	2005/03/08 Srinivas U	Added truncate and delete before the bulk insert
*******************************************************************************/
 
CREATE PROCEDURE AIM_DxRP_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	/* Start variable declaration */
	DECLARE @RtnCode int

	SET NOCOUNT ON

	/* Start variable initialization */
	--Initialize counters
	SELECT @InsertCounter = 0
		, @UpdateCounter = 0
	-- Delete records from the DxRP Table
	TRUNCATE TABLE AIMDxRP
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxRP

	-- This is a data interface transaction <Transaction Code=RP>. 
	-- Bulk Load the <Transaction Table=AIMDxRP> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'RP', 'AIMDxRP', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	BEGIN TRANSACTION
		UPDATE AIMDestinationProfile
			SET ExceptionPct = ISNULL(AIMDxRP.ExceptionPct, AIMDestinationProfile.ExceptionPct),
				AvailableQty = ISNULL(AIMDxRP.OnHandQty, AIMDestinationProfile.AvailableQty),
				TargetQty = ISNULL(AIMDxRP.StandardQty, AIMDestinationProfile.TargetQty)
		FROM AIMDxRP
		WHERE AIMDestinationProfile.LcID_Destination = AIMDxRP.LcID
			AND AIMDestinationProfile.Item = AIMDxRP.Item
	
		SET @UpdateCounter = @@ROWCOUNT 

		DELETE AIMDxRP
		FROM AIMDxRP ,AIMDestinationProfile
		WHERE AIMDxRP.LcID =AIMDestinationProfile.LcID_Destination
			AND AIMDxRP.Item =AIMDestinationProfile.Item

		-- INSERT
		--Substitute defaults if data not provided		
		INSERT INTO AIMDestinationProfile (LcID_Destination
			, Item
			, ExceptionPct 
			, AvailableQty
			, TargetQty)
		SELECT RTRIM(AIMDxRP.Lcid),
			RTRIM( AIMDxRP.Item),
			ISNULL(AIMDxRP.ExceptionPct, ISNULL(AIMLocations.ExceptionPct, 0)),
			ISNULL(AIMDxRP.OnHandQty, 0),
			ISNULL(AIMDxRP.StandardQty, 0)
		FROM AIMDxRP
		INNER JOIN AIMLocations
		ON AIMDxRP.LcID = AIMLocations.LcID
		-- WHERE AIMLocations.LType = 'D' -- Destination
	
		SET @InsertCounter = @@ROWCOUNT 

	COMMIT TRANSACTION

	-- Delete records from the DxRP Table
	TRUNCATE TABLE AIMDxRP
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxRP

	UPDATE STATISTICS AIMDestinationProfile WITH RESAMPLE, ALL

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

