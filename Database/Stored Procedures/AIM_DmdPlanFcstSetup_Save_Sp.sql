if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstSetup_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstSetup_Save_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO





/******************************************************************************
**	Name: AIM_DmdPlanFcstSetup_Save_Sp
**	Desc: Updates AIMFcstSetup for the given FcstID, based on the FcstLocked.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	<PERSON><PERSON><PERSON><PERSON>sdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE    PROCEDURE AIM_DmdPlanFcstSetup_Save_Sp
(
	-- Conditions
	@UserID as nvarchar(12),
	@UpdateForRolling as bit = 0,
	-- Fields
	@FcstID as nvarchar(12) = '',
	@FcstDesc as nvarchar(40),
	@FcstHierarchy as tinyint,
	@FcstEnabled as bit,
	@FcstLocked as bit,
	@FcstStartDate as datetime,
	@LastUpdated as datetime, 
	@FcstRolling as bit,
	@FcstHistory as bit,
	@FcstPds_Future as tinyint,
	@FcstPds_Historical as tinyint,
	@FreezePds as tinyint,
	@FixedPds as tinyint,
	@FcstInterval as tinyint,
	@FcstUnit as tinyint,
	@ApplyTrend as tinyint,
	@Calc_SysFcst as bit,
	@Calc_NetReq as bit,
	@Calc_ProjInv as bit,
	@Calc_HistDmd as bit,
	@Calc_MasterFcstAdj as bit,
	@Calc_FcstAdj as bit,
	@Calc_AdjNetReq as bit,
	@Calc_ProdConst as bit,
	-- Item filter criteria
	@Item as nvarchar(25),
	@ItStat as nvarchar(1),
	@VnID as nvarchar(12),
	@Assort as nvarchar(12),
	@ByID as nvarchar(12),
	@Class1 as nvarchar(50),
	@Class2 as nvarchar(50),
	@Class3 as nvarchar(50),
	@Class4 as nvarchar(50),
	@LcID as nvarchar(12),
	@LStatus as nvarchar(1),
	@LDivision as nvarchar(20),
	@LRegion as nvarchar(20),
	@LUserDefined as nvarchar(30),
	-- Return parameters
 	@FcstSetupKey as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @CkFcstLocked as bit
	DECLARE @AccessCode as int
	
	-- Validate the required parameters.
	IF @FcstID IS Null
	OR @UserID IS NULL
	BEGIN
	  	RETURN -1
	END

	IF @UpdateForRolling = 1
	BEGIN
		UPDATE AIMFcstSetup SET
			LastUpdated = ISNULL(@LastUpdated, AIMFcstSetup.LastUpdated)
		WHERE AIMFcstSetup.FcstSetupKey = @FcstSetupKey
	
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat > 0 
		BEGIN
			RETURN 1
		END
		ELSE 
		BEGIN
			RETURN -1	
		END
	END
	ELSE
	BEGIN
		SELECT 
			@FcstSetupKey = FcstSetupKey,
			@CkFcstLocked = FcstLocked 
		FROM AIMFcstSetup 
		WHERE AIMFcstSetup.FcstID = @FcstID 
		SET @Return_Stat = @@ROWCOUNT

	END

	IF @Return_Stat > 0
	BEGIN
	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	

	--	-- Update if not locked and has access
	-- 	IF @CkFcstLocked = 0
	-- 	AND @AccessCode >= 2
	-- 	BEGIN
		  	UPDATE AIMFcstSetup SET 
		  		FcstDesc = ISNULL(@FcstDesc, AIMFcstSetup.FcstDesc),
		   		FcstHierarchy = ISNULL(@FcstHierarchy, AIMFcstSetup.FcstHierarchy),
		   		FcstEnabled = ISNULL(@FcstEnabled, AIMFcstSetup.FcstEnabled),
		   		FcstLocked = ISNULL(@FcstLocked, AIMFcstSetup.FcstLocked),
		   		FcstStartDate = ISNULL(@FcstStartDate, AIMFcstSetup.FcstStartDate),
				LastUpdated = ISNULL(@LastUpdated, AIMFcstSetup.LastUpdated),	
		   		FcstRolling = ISNULL(@FcstRolling, AIMFcstSetup.FcstRolling),
		   		FcstHistory = ISNULL(@FcstHistory, AIMFcstSetup.FcstHistory),
		   		FcstPds_Future = ISNULL(@FcstPds_Future, AIMFcstSetup.FcstPds_Future),
		   		FcstPds_Historical = ISNULL(@FcstPds_Historical, AIMFcstSetup.FcstPds_Historical),

				FreezePds = ISNULL(@FreezePds, AIMFcstSetup.FreezePds),
				FixedPds = ISNULL(@FixedPds, AIMFcstSetup.FixedPds),
		   		FcstInterval = ISNULL(@FcstInterval, AIMFcstSetup.FcstInterval),
				FcstUnit = ISNULL(@FcstUnit, AIMFcstSetup.FcstUnit),
		   		ApplyTrend = ISNULL(@ApplyTrend, AIMFcstSetup.ApplyTrend),
				Calc_SysFcst = ISNULL(@Calc_SysFcst, AIMFcstSetup.Calc_SysFcst),
				Calc_NetReq = ISNULL(@Calc_NetReq, AIMFcstSetup.Calc_NetReq),
				Calc_ProjInv = ISNULL(@Calc_ProjInv, AIMFcstSetup.Calc_ProjInv),
				Calc_HistDmd = ISNULL(@Calc_HistDmd, AIMFcstSetup.Calc_HistDmd),
				Calc_MasterFcstAdj = ISNULL(@Calc_MasterFcstAdj, AIMFcstSetup.Calc_MasterFcstAdj),
				Calc_FcstAdj= ISNULL( @Calc_FcstAdj, AIMFcstSetup.Calc_FcstAdj),
				Calc_AdjNetReq = ISNULL( @Calc_AdjNetReq, AIMFcstSetup.Calc_AdjNetReq),
				Calc_ProdConst = ISNULL( @Calc_ProdConst, AIMFcstSetup.Calc_ProdConst),
		   		Item = ISNULL(@Item, AIMFcstSetup.Item),
		   		ItStat = ISNULL(@ItStat, AIMFcstSetup.ItStat),
		   		VnID = ISNULL(@VnID, AIMFcstSetup.VnID),
		   		Assort = ISNULL(@Assort, AIMFcstSetup.Assort),
		   		ByID = ISNULL(@ByID, AIMFcstSetup.ByID),
		   		Class1 = ISNULL(@Class1, AIMFcstSetup.Class1),
		   		Class2 = ISNULL(@Class2, AIMFcstSetup.Class2),
		   		Class3 = ISNULL(@Class3, AIMFcstSetup.Class3),
		   		Class4 = ISNULL(@Class4, AIMFcstSetup.Class4),
		   		LcID = ISNULL(@LcID, AIMFcstSetup.LcID),
		   		LStatus = ISNULL(@LStatus, AIMFcstSetup.LStatus),
		   		LDivision = ISNULL(@LDivision, AIMFcstSetup.LDivision),
		   		LRegion = ISNULL(@LRegion, AIMFcstSetup.LRegion),
		   		LUserDefined = ISNULL(@LUserDefined, AIMFcstSetup.LUserDefined)
		  	WHERE AIMFcstSetup.FcstSetupKey = @FcstSetupKey
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
	--	END
	-- 	ELSE
	-- 	BEGIN
	-- 	 	RETURN -3 -- Not Permitted Premissions
	--	END
	END
	ELSE
	BEGIN	
		-- Validate the required parameters.
		IF @FcstID IS Null
		OR @UserID IS NULL
		OR @FcstDesc IS NULL
		OR @FcstHierarchy IS NULL
		OR @FcstEnabled IS NULL
		OR @FcstLocked IS NULL
		OR @FcstStartDate IS NULL
		OR @FcstRolling IS NULL
		OR @FcstHistory IS NULL
		OR @FcstPds_Future IS NULL
		OR @FcstPds_Historical IS NULL
		OR @FcstInterval IS NULL
		OR @FcstUnit IS NULL
		OR @ApplyTrend IS NULL
		BEGIN
		  	RETURN -1
		END

		-- Insert
		INSERT INTO AIMFcstSetup (
			FcstID, FcstDesc,
		   	FcstHierarchy, FcstEnabled, FcstLocked,
		   	FcstStartDate, LastUpdated, 
			FcstRolling, FcstHistory,
		   	FcstPds_Future, FcstPds_Historical,
			FreezePds,FixedPds,
		   	FcstInterval, FcstUnit, ApplyTrend,
			Calc_SysFcst, Calc_NetReq,
			Calc_ProjInv, Calc_HistDmd,
			Calc_MasterFcstAdj, Calc_FcstAdj,
			Calc_AdjNetReq,Calc_ProdConst,
		   	Item, ItStat, VnID, Assort, ByID,
		   	Class1, Class2, Class3, Class4,
		   	LcID, LStatus, LDivision, LRegion, LUserDefined
		) VALUES (
			@FcstID, @FcstDesc,
		   	@FcstHierarchy, @FcstEnabled, @FcstLocked,
		   	@FcstStartDate, @FcstStartDate, 	-- The first time a setup is saved, we want the LastUpdated to be the same as or earlier than the StartDate
			@FcstRolling, @FcstHistory,	
		   	@FcstPds_Future, @FcstPds_Historical,
			@FreezePds,@FixedPds,
		   	@FcstInterval, @FcstUnit, @ApplyTrend,
			ISNULL(@Calc_SysFcst, 0), ISNULL(@Calc_NetReq, 0),
			ISNULL(@Calc_ProjInv, 0), ISNULL(@Calc_HistDmd, 0),
			ISNULL(@Calc_MasterFcstAdj, 0), ISNULL( @Calc_FcstAdj, 0),
			ISNULL( @Calc_AdjNetReq, 0),ISNULL(@Calc_ProdConst,0),
		   	ISNULL(@Item, ''), ISNULL(@ItStat, ''), ISNULL(@VnID, ''), ISNULL(@Assort, ''), ISNULL(@ByID, ''),
			ISNULL(@Class1, ''), ISNULL(@Class2, ''), ISNULL(@Class3, ''), ISNULL(@Class4, ''),
			ISNULL(@LcID, ''), ISNULL(@LStatus, ''), ISNULL(@LDivision, ''), ISNULL(@LRegion, ''), ISNULL(@LUserDefined, '')
		  )

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
		ELSE
		BEGIN
			SET @FcstSetupKey = @@IDENTITY
		END
	END

	RETURN 1





GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

