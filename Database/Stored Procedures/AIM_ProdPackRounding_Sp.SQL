SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProdPackRounding_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProdPackRounding_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ProdPackRounding_Sp
**	Desc: Pack Rounding for Production Constraints
**
**	Returns: Pack Rounded quantity
**
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
   
CREATE  PROCEDURE AIM_ProdPackRounding_Sp
(
        @RSOQ         						float,
        @ConvFactor   						int,
        @PackRounding 						nvarchar(1)
)
       
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE	@packqty 					float

-- Check for an invalid conversion factor
IF @ConvFactor <= 0
	Return @RSOQ
    select @Packqty = @RSOQ/@ConvFactor
 
    select @packqty = case @PackRounding
        when 'D' then floor(@packqty)	        -- Round down
        when 'U' then ceiling(@packqty)	        -- Round up
        ELSE round(@packqty, 0)
        END

-- Zero pack size is not allowed
return ROUND(@packqty * @ConvFactor, 0)

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

