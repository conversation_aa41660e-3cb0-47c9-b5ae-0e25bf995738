SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxLT_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxLT_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxLT_Sp
**	Desc: Loads Item Leadtime into the AIM Item & AIMLeadTime Table..
**
** Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**			1)  0 - Successful
**              	2) -1 - No Data Found
**              	3) -2 - SQL Error
**              	4) -3 - Duplicate File Name
**              	5) -4 - Invalid File Name
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:        Description:
**    ---------- -------------- -----------------------------------------------
**    2003/05/28 A. Stocksdale	Replaced bulk insert and validation commands 
**				with call to AIM_DxBulkInsert_Sp - common to all
**                              Dx processes.
**    	2005/03/08 Srinivas U	Added truncate and delete before the bulk insert
*******************************************************************************/
     
CREATE PROCEDURE AIM_DxLT_Sp
(
	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE 
		@RtnCode            			    	int,
		@RowFound					int,
			
		-- AIMDxLT Table
		@VnId                				nvarchar(12),
		@LcId 					        nvarchar(12),
		@Item					        nvarchar(25),
		@DateOrd				        datetime,
		@DateRec 				        datetime,
		@DatePutAway 					datetime,
		@Oh 					        int,
		    
		-- AIM Lead Time Table
		@LeadTime               			smallint,
		@Prev_LeadTime1    				smallint,
		@Prev_LeadTime2    				smallint,
		@Prev_LeadTime3    				smallint,
		@LT_Count              				int,
		@LT_MAE                				decimal(10, 2),
		@SS_Erosion_Pct    				decimal(9, 4),
		@AE                     			decimal(10,2),
		
		-- System Control Table
		@LT_Alpha               			decimal(5,3),
		@LT_CtrlInterval_Pct    			decimal(5,3),
		
		@CurLeadTime  					smallint,
		@PrevLeadTime 					smallint,
		@Unicode_Option					nvarchar(10)
	
	SET NOCOUNT ON

	--Initialize counters and variables
	SET @InsertCounter = 0
	SET @UpdateCounter = 0
	
	-- Delete records from the DxLT Table
	TRUNCATE TABLE AIMDxLT
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxLT
	-- This is a data interface transaction <Transaction Code=LT>. 
	-- Bulk Load the <Transaction Table=AIMDxLT> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'LT', 'AIMDxLT', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.


	BEGIN TRANSACTION
		-- Retrieve the lead time variables from the System Control Table
		SELECT @LT_Alpha = LT_Alpha, @LT_CtrlInterval_Pct = LT_CtrlInterval_Pct FROM SysCtrl
		-- CREATE cursor to process DxLT Table
		DECLARE DxLT_Cursor CURSOR LOCAL FAST_FORWARD FOR 
			SELECT VnId, LcId, Item, DateOrd, DateRec, DatePutAway, Oh = Oh - QtyRec 
			FROM AIMDxLT 
			ORDER BY LcId, Item
		OPEN DxLT_Cursor
		-- Check for no rows to process
		IF @@CURSOR_ROWS = 0 
		BEGIN
			RETURN
		END	
		-- Update Loop
		WHILE 1 = 1 
		BEGIN 
			FETCH NEXT FROM DxLT_Cursor INTO 
				@VnId, @LcId, @Item, @DateOrd, @DateRec, @DatePutAway, @Oh
			-- Check Fetch Status
			IF @@FETCH_STATUS <> 0
			BEGIN
				break
			END		
		    	-- Update the Lead Time Variables
		    	IF IsDate(@DateOrd) = 1 AND IsDate(@DateRec) = 1
			BEGIN
				SET @CurLeadTime = datediff(dd, @DateOrd, @DateRec)
				-- Check for invalid date ranges
				IF @CurLeadTime < 0 
				BEGIN
					SET @CurLeadTime = 0
				END    	    
			END
			ELSE
			BEGIN
				Set @CurLeadTime = 0
			END
			-- Get information from the AIM Lead Times Table
			SELECT @PrevLeadTime = AIMLeadTime.LeadTime, 
					@Prev_LeadTime1 = AIMLeadTime.Prev_LeadTime1, 
					@Prev_LeadTime2 = AIMLeadTime.Prev_LeadTime2, 
					@Prev_LeadTime3 = AIMLeadTime.Prev_LeadTime3, 
					@LT_Count = AIMLeadTime.LT_Count, 
					@LT_MAE = AIMLeadTime.LT_MAE, 
					@SS_Erosion_Pct = AIMLeadTime.SS_Erosion_Pct 
			FROM AIMLeadTime 
			WHERE LcId = @LcId 
				AND Item = @Item	
			SET @RowFound = @@Rowcount
			-- Increment Update Count	
			IF @RowFound = 1 
			BEGIN
				SET @UpdateCounter = @UpdateCounter + 1
			END
			ELSE
			BEGIN
				-- IF the Lead Time Record Does Not Exist Initialize the Lead Time Variables
				SET @InsertCounter = @InsertCounter + 1
				
				SELECT @PrevLeadTime = LeadTime 
				FROM Item 
				WHERE LcId = @LcId 
					AND Item = @Item
				IF @@rowcount = 0 or @PrevLeadTime = 0
				BEGIN
					SELECT @PrevLeadTime = Avg(Dft_LeadTime) 
					FROM AIMVendors 
					WHERE VnId = @VnId
				END
				IF @PrevLeadTime = 0
				BEGIN
					SET @PrevLeadTime = @CurLeadTime
				END
				SET @Prev_LeadTime1 = 0
				SET @Prev_LeadTime2 = 0
				SET @Prev_LeadTime3 = 0
				SET @LT_Count = 1
				SET @LT_MAE = 0
				SET @SS_Erosion_Pct = 1 
		    	END
			-- Increment the Lead Time Count
			SET @LT_Count = @LT_Count + 1
			-- Calculate the Lead Time Absolute Error
			SET @AE = ABS(@PrevLeadTime - @CurLeadTime)
			-- Update the Lead Time and Lead Time Mean Absolute Error
			IF (1.0 / @LT_Count) > @LT_Alpha
			BEGIN
				SET @LT_MAE = ((1 - (1.0 / @LT_Count)) * @LT_MAE) 
								+ ((1.0 / @LT_Count) * @AE)
				SET @LeadTime = ROUND(
									(@PrevLeadTime * (1 - (1.0 / @LT_Count)) + @CurLeadTime * (1.0 / @LT_Count))
									, 0)
			END
			ELSE
			BEGIN
				SET @LT_MAE = ((1 - @LT_Alpha) * @LT_MAE) 
								+ (@LT_Alpha * @AE)
				SET @LeadTime = ROUND(
									(@PrevLeadTime * (1 - @LT_Alpha)) + (@CurLeadTime * @LT_Alpha)
									, 0)
			END

			-- Update the Safety Stock Erosion Factor
			-- EXE NOTE: This feature was not implemented in AIM 4.0

			-- Update the Lead Time Table
			IF @RowFound = 1            -- Update Row
			BEGIN
				UPDATE AIMLeadTime SET
					VnId = @VnId, 
					LeadTime = ISNULL(@LeadTime, 0),
					Prev_LeadTime1 = ISNULL(@CurLeadTime, 0), 
					Prev_LeadTime2 = ISNULL(@Prev_LeadTime1, 0), 
					Prev_LeadTime3 = ISNULL(@Prev_LeadTime2, 0), 
					LT_Count = ISNULL(@LT_Count, 0),
					LT_MAE = ISNULL(@LT_MAE, 0), 
					SS_Erosion_Pct = ISNULL(@SS_Erosion_Pct, 0)
				WHERE LcId = @Lcid 
					AND Item = @Item
			END
			ELSE                        -- Insert Row
			BEGIN
				INSERT INTO AIMLeadTime
					(LcId, Item, 
					VnId, 
					LeadTime, 
					Prev_LeadTime1, 
					Prev_LeadTime2, 
					Prev_LeadTime3, 
					LT_Count,
					LT_MAE, 
					SS_Erosion_Pct)
				VALUES
					(@LcId, @Item, 
					@VnId, 
					ISNULL(@LeadTime, 0),
					ISNULL(@CurLeadTime, 0), 
					ISNULL(@PrevLeadTime, 0), 
					0,
					ISNULL(@LT_Count, 0),
					ISNULL(@AE, 0), 
					ISNULL(@SS_Erosion_Pct, 0))
			END
			-- Update the Item Table
			UPDATE Item Set
				LeadTime = ISNULL(@LeadTime, 0)
			WHERE Lcid = @Lcid
				AND Item = @Item
		END	-- end while
		-- Deallocate the cursor
		CLOSE DxLT_Cursor
		DEALLOCATE DxLT_Cursor
	COMMIT TRANSACTION

	-- Delete records from the DxLT Table
	TRUNCATE TABLE AIMDxLT

	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxLT

	UPDATE STATISTICS AIMLeadTime WITH RESAMPLE, ALL

	RETURN 1	-- Succeed

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

