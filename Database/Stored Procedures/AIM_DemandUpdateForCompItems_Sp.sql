SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DemandUpdateForCompItems_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DemandUpdateForCompItems_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DemandUpdateForCompItems_Sp
**	Desc: Updates the FcstDemanad, FcstRT,FcstLT,FcstMonth,FcstQtr,FcstYear
**	Values if the item has only Companion item demand or 
**	Campanion item demand and KitBOM Demand 
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   03/04/2004
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   Author:	Description:
**    ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
   
CREATE     PROCEDURE AIM_DemandUpdateForCompItems_Sp
(
        @Lcid	            					nvarchar(12),
        @Item            					nvarchar(25),
        @FcstDemand     					decimal(10, 2),
	@FcstKitBom						decimal(10,2),
        @FcstRT          					decimal(10, 2),
        @FcstLT          					decimal(10, 2),
        @Fcst_Month      					decimal(10, 2),
        @Fcst_Quarter    					decimal(10, 2),
        @Fcst_Year       					decimal(10, 2)
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
-- Declare working variable
DECLARE	

@LineCount		Int
SET NOCOUNT ON

Select @LineCount =Count(*) FROM
AIMCOMPANIONITEMDETAIL
WHERE Lcid =@Lcid AND
Item =@Item  AND
ExcludeIndepDemY_N ='N'

If @LineCount <>0 
BEGIN
	UPDATE Item
    	SET 
        FcstDemand = FcstDemand +@FcstDemand,DependentFcstDemand =@FcstKitBOM,
	FcstRT = FcstRT +@FcstRT,FcstLT = FcstLT +@FcstLT, Fcst_Month = Fcst_Month +@Fcst_Month ,
	Fcst_Quarter = Fcst_Quarter +@Fcst_Quarter, Fcst_Year = Fcst_Year +@Fcst_Year
        WHERE LcId = @Lcid 
    	and Item = @Item
END
ELSE
BEGIN
UPDATE Item
    	SET 
        FcstDemand = @FcstDemand,DependentFcstDemand =@FcstKitBOM,
	FcstRT = @FcstRT,FcstLT = @FcstLT, Fcst_Month = @Fcst_Month,
	Fcst_Quarter =@Fcst_Quarter, Fcst_Year =@Fcst_Year
        WHERE LcId = @Lcid 
    	and Item = @Item
END
RETURN @@rowcount




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

