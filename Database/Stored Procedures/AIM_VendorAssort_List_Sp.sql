SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorAssort_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorAssort_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_VendorAssort_List_Sp
** 	Desc: Gets unique values for AIMLocations.Vendors 
** 	
** 	Returns: 1) 0 -- Failure
**	2)	1 - Success
** 	3) -1 - Invalid parameters
** 	4) none of the above -- SQL Error code.
** 	
** 	Auth: Annalakshmi Stocksdale
** 	Date: 2004/09/03
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date: 		Author:	     Description:
** 	---------- 	------------ ------------------------------------------
*******************************************************************************/
CREATE PROCEDURE AIM_VendorAssort_List_Sp
-- 	WITH ENCRYPTION /* Production use must be encrypted */
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	SET NOCOUNT ON

	DECLARE @RowCounter as int
	DECLARE @RtnCode as tinyint

	SELECT AIMVendors.Assort, COUNT(AIMVendors.VnID) As 'Total Vendors'
	FROM AIMVendors
	WHERE (AIMVendors.VnID IS NOT NULL AND LTRIM(RTRIM(AIMVendors.VnID)) <> '')
	AND (AIMVendors.Assort IS NOT NULL AND LTRIM(RTRIM(AIMVendors.Assort)) <> '')
	GROUP BY AIMVendors.Assort
	ORDER BY AIMVendors.Assort


	SELECT @RowCounter = @@rowcount	
	-- 	Check for SQL Server errors.
	SET @RtnCode = CASE 
		WHEN @RowCounter > 0 THEN  1		-- SUCCEED	
		WHEN @@ERROR <> 0 THEN @@ERROR
		ELSE  0	-- FAIL
	END	

	RETURN @RtnCode
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
