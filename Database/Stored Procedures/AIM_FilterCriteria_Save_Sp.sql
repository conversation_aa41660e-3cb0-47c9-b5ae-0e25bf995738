SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIM_FilterCriteria_Save_Sp]') 
		AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_FilterCriteria_Save_Sp]
GO

/*
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
** 	Name: AIM_FilterCriteria_Save_Sp
** 	Purpose: Inserts the filters into the ForecastFilterCriteria table,
**		given the following criteria:
** 		1.  Forecast ID to match with the AIMForecast table
**		2.  Column name to filter on
** 		3.  Sequential index if there is more than one value
** 		4.  Filter value to be saved
**
** 		Version Number 	- 1.0
** 		Date Created 	- 2004/08/11
** 		Created By 		- Annalakshmi Stocksdale
**
********************************************************************************
** This file contains trade secrets of SSA Global. No part
** may be reproduced or transmitted in any form by any means or for any purpose
** without the express written permission of SSA Global.
********************************************************************************
**	Change History
********************************************************************************
**	Date:      	Author:      		Description:
**	---------- 	------------ 		-------------------------------------------
********************************************************************************
*/

CREATE PROCEDURE AIM_FilterCriteria_Save_Sp (
	@FcstSetupKey int
	, @FilterColumn nvarchar (255)
	, @SearchCondition nvarchar (255)
	, @FilterIndex int
	, @FilterValue nvarchar (255)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @CheckValue as decimal(9, 1)
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	OR @FilterColumn IS NULL
	OR @SearchCondition IS NULL
	OR @FilterIndex IS NULL
	OR @FilterValue IS NULL
	BEGIN
	  	RETURN -1
	END

	-- Validate the existence of the record in the filter table
	UPDATE ForecastFilterCriteria SET
		SearchCondition = @SearchCondition		 
		, FilterValue = @FilterValue
	WHERE ForecastFilterCriteria.FcstSetupKey = @FcstSetupKey
	AND ForecastFilterCriteria.FilterColumn = @FilterColumn
	AND ForecastFilterCriteria.FilterIndex = @FilterIndex

	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	IF @Return_Stat <= 0 
	BEGIN
		INSERT INTO ForecastFilterCriteria (
			FcstSetupKey
			, FilterColumn
			, SearchCondition
			, FilterIndex
			, FilterValue
		) VALUES (
			@FcstSetupKey
			, @FilterColumn
			, @SearchCondition
			, @FilterIndex
			, @FilterValue
		)
	
		SET @Return_Stat = @@ROWCOUNT
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
		IF @Return_Stat <= 0 RETURN -1
		ELSE RETURN 1
	END
	ELSE RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

