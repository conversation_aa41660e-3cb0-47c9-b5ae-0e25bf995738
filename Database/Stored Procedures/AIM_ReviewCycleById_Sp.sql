SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewCycleById_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewCycleById_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ReviewCycleById_Sp
**	Desc: Updates the Item table with data from the override.
**
**	Returns: 
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_ReviewCycleById_Sp 
(      
      	@RevCycleKey					nvarchar(8),
      	@ByIdKey					nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
    
DECLARE @RC 					    	int,
      	@RevCycle						nvarchar(8),
      	@ReviewTotal 						int,
      	@LT_Total 						int,
      	@RC_Total 						int,
      	@PE_Total 						int,
      	@VnId							nvarchar(12),
      	@Assort							nvarchar(12),
      
      -- Control Totals
      	@x_ReviewTotal						int,
      	@x_LT_Total						int,
      	@x_RC_Total						int,
      	@x_PE_Total 						int,
      	@x_Vendors						int
  
SET NOCOUNT ON
    
-- Initialize Control Totals
SELECT @x_Vendors = 0, @x_ReviewTotal = 0, @x_LT_Total = 0, @x_RC_Total = 0, @x_PE_Total = 0
      
-- Build the Cursor
DECLARE VendorList CURSOR LOCAL FAST_FORWARD FOR  
SELECT DISTINCT AIMVendors.VnId, AIMVendors.Assort, AIMVendors.RevCycle
FROM AIMVendors with (NOLOCK)
  	INNER JOIN Item WITH (NOLOCK) ON AIMVendors.VnId = Item.Vnid
      	AND AIMVendors.Assort = Item.Assort
WHERE AIMVendors.Revcycle = @RevCycleKey
AND AIMVendors.Dft_Byid = @ByIdKey
ORDER BY AIMVendors.VnId, AIMVendors.Assort
      
OPEN VendorList
      
FETCH NEXT FROM VendorList INTO @VnId, @Assort,@RevCycle
      
WHILE  @@FETCH_STATUS = 0 
BEGIN
       	-- Initialize Parameters
      	SELECT @RC = 0, @ReviewTotal = 0, @LT_Total = 0, @RC_Total = 0, @PE_Total = 0
       
      	EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', @ReviewTotal output, 
      		@LT_Total output, @RC_Total output, @PE_Total output, 'Y'
      
      	-- Update the Control Totals
      	SELECT @x_Vendors = @x_Vendors + 1, @x_ReviewTotal = @x_ReviewTotal + @ReviewTotal,
      		@x_LT_Total	= @x_LT_Total + @LT_Total, @x_RC_Total = @x_RC_Total + @RC_Total,
      		@x_PE_Total = @x_PE_Total + @PE_Total	 
      
      	FETCH NEXT FROM VendorList INTO	@VnId, @Assort,@RevCycle
      
END
      
-- Output control Totals
SELECT 'Vendors Reviewed' = @x_Vendors, 'Review Total' = @x_ReviewTotal, 
'Lead Time Exceptions' = @x_LT_Total, 'Scheduled Reviews' = @x_RC_Total, 
'Priority Exceptions' = @x_PE_Total
    
CLOSE VendorList
DEALLOCATE VendorList

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

