SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_Load_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_Load_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMYears_Load_Sp
**	Desc: Retrieves all values from the AIMYears table
**
**	Returns: 1)  @rowcount - Successful
**
**	Values:  Recordset - AIMYears
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   Author:	    Description:
**  ---------- ------------	-----------------------------------------------
**  	
*******************************************************************************/         
  
CREATE PROCEDURE AIM_AIMYears_Load_Sp
(
	@FiscalYear int
)  

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	SET NOCOUNT ON
	DECLARE @RowCounter int
	  
	SELECT 
		FiscalYear,
		FYStartDate,
		FYEndDate,
		NbrWeeks
	FROM AIMYears
	WHERE AIMYears.FiscalYear = 
		CASE WHEN (@FiscalYear IS NULL) THEN AIMYears.FiscalYear
		WHEN @FiscalYear < 1000 THEN AIMYears.FiscalYear
		ELSE @FiscalYear
		END
	ORDER BY FiscalYear
	
	SET @RowCounter = @@ROWCOUNT
	
	RETURN  @RowCounter

END 
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

