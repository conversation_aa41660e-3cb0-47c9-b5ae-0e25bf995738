SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProdConstraintTemp_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProdConstraintTemp_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ProdConstraintTemp_Get_Sp
**	Desc: Gets data from AIM_ProdConstraintTemp table
**
**	Returns: 1)  1  - Successful
**               2)  -1 - Failure 
**               3)  -2 - SQL Error
**             
**	Auth:   Srinivas Uddanti 
**	Date:   04/03/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**	April-11-2005	Srinivas U  added order by clause to the select
*******************************************************************************/
     
CREATE PROCEDURE AIM_ProdConstraintTemp_Get_Sp
(
	@UniqueJobId	nvarchar(255)
)
  	
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF  @UniqueJobId ='' or @UniqueJobId =null
BEGIN
	RETURN -1
END

BEGIN
	Select * from ProdConstraintTemp where UniqueJobId =@UniqueJobId
	Order by item,lcid
	Return 1
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


