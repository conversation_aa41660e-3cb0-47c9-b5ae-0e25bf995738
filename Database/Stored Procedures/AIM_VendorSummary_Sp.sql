SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorSummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorSummary_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VendorSummary_Sp
**	Desc: Verifies if the DX File has been run before.
**
**	Parameters:
**		@ByID = BuyerID
**		@FilterOpt = AIMPO.POSource. Acceptable values:
**			A=All, 
**			S=Scheduled Reviews Only, 
**			X=Exceptions Only
**
**	Returns:
**		Return Value, one of the following:
**			1)	Recordcount - Successful
**			2)	0 - Failure
**
**	Values:  
**		Recordset 
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:			Description:
**	---------- -------------- ---------------------------------------------
**	10/30/2002 Wade Riza	  Updated validation and Error handling
**	2003/01/14 A. Stocksdale  Moved 'Select @rowcount = @@rowcount' 
**				  after IF condition to remove error about 
**				  its being null when FilterOpt = 'A'
**      2003/06/19 Mohammed       Retrieve the field AIMPO.VndSizeFlag
*******************************************************************************/
    
CREATE PROCEDURE AIM_VendorSummary_Sp
(
	@ById   				nvarchar(12),
	@FilterOpt				nvarchar(1)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @Rowcount  					int

SET NOCOUNT ON
	
-- Validate the required parameters.
IF @ById IS NULL
BEGIN
	RETURN 0
END

IF @FilterOpt = 'A' 
BEGIN
	SELECT ById, VnId, Assort, POStatus, TransmitPO, ShipIns, 
  	UserInitials, Remarks1, Remarks2, Remarks3, AddrOverride, 
  	VName, VAddress1, VAddress2, VCity, VState, VZip, LineCount,
  	PosLineCount, Vn_Min, Vn_Best, Reach_Code, POSource, POByZone, 
  	Dft_LeadTime, TotalCost, VndSizeFlag
	FROM AIMPo
	WHERE ById = @ById
	AND POStatus in ('P', 'R')
	ORDER BY VnId, Assort
END
ELSE IF @FilterOpt <> 'A'
BEGIN
	SELECT ById, VnId, Assort, POStatus, TransmitPO, ShipIns, 
  	UserInitials, Remarks1, Remarks2, Remarks3, AddrOverride, 
  	VName, VAddress1, VAddress2, VCity, VState, VZip, LineCount,
  	PosLineCount, Vn_Min, Vn_Best, Reach_Code, POSource, POByZone, 
  	Dft_LeadTime, TotalCost, VndSizeFlag
	FROM AIMPo
	WHERE ById = @ById
	AND POStatus in ('P', 'R')
	AND POSource = @FilterOpt
	ORDER BY VnId, Assort
END
	
SELECT @rowcount = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN 0  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN @rowcount
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
