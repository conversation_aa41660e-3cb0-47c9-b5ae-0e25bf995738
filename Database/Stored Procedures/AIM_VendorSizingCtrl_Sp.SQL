SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorSizingCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorSizingCtrl_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VendorSizingCtrl_Sp
**	Desc: Performs Vendor Sizing.
**
**	Returns: 1)  1 - Successful
**               2)  0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      10/30/2002 Wade Riza 	Updated validation and Error handling
**	04/20/2004 S Uddanti	Added VnIdKey and AssortKey arguments
**				So that Vendorsizing can be run for specific 
**				Vendor and Assortment.
**				Also run Vendorsizing if Ordstatus in ('P','R')
*******************************************************************************/
     
CREATE PROCEDURE AIM_VendorSizingCtrl_Sp
(
  	@RevCycle 			nvarchar(8) = 'ALL',
	@VnIdKey		        nvarchar(12) = '', /*  Vendor Id Key */
      	@AssortKey                      nvarchar(12) = '' /*  Assortment Key */
                                        -- Review Cycle Key
                                        -- IF @FilterOpt = R, THEN Review Cycle
  		                        -- IF @FilterOpt = A or V, THEN 'ALL' (Default)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE
   @VnId      	 					nvarchar(12),
   @Assort     						nvarchar(12),
   @Reach_Code 						nvarchar(1),
   @Vn_Min     						decimal(9,2),
   @Vn_Best    						decimal(9,2)

SET NOCOUNT ON

-- Validate the required parameters.
IF @RevCycle IS NULL
BEGIN
	RETURN 0
END
IF @VnIdKey <> '' AND @AssortKey <> ''
BEGIN
        DECLARE VnIdCursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT DISTINCT PODetail.VnId, PODetail.Assort, AIMVendors.Reach_Code, 
        AIMVendors.Vn_Min, AIMVendors.Vn_Best
        FROM PODetail 
        INNER JOIN AIMVendors ON PODetail.VnId = AIMVendors.VnId AND PODetail.Assort = AIMVendors.Assort 
        WHERE PODetail.Vnid = @VnidKey
	AND PODetail.Assort = @AssortKey
	AND PODetail.POLineType = 20 
        AND PODetail.OrdStatus in ('P','R')
        AND AIMVendors.Reach_Code <> 'N'
        AND AIMVendors.Vn_Min + AIMVendors.Vn_Best > 0
END
ELSE
IF @RevCycle = 'ALL'
BEGIN
        DECLARE VnIdCursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT DISTINCT PODetail.VnId, PODetail.Assort, AIMVendors.Reach_Code, 
        AIMVendors.Vn_Min, AIMVendors.Vn_Best
        FROM PODetail 
        INNER JOIN AIMVendors ON PODetail.VnId = AIMVendors.VnId AND PODetail.Assort = AIMVendors.Assort 
        WHERE PODetail.POLineType = 20 
        AND PODetail.OrdStatus in ('P','R')
        AND AIMVendors.Reach_Code <> 'N'
        AND AIMVendors.Vn_Min + AIMVendors.Vn_Best > 0
END
ELSE
BEGIN
        DECLARE VnIdCursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT DISTINCT PODetail.VnId, PODetail.Assort, AIMVendors.Reach_Code, 
        AIMVendors.Vn_Min, AIMVendors.Vn_Best
        FROM PODetail 
        INNER JOIN AIMVendors ON PODetail.VnId = AIMVendors.VnId AND PODetail.Assort = AIMVendors.Assort 
        WHERE PODetail.RevCycle = @RevCycle
  	AND PODetail.POLineType = 20 
        AND PODetail.OrdStatus in ('P','R')
        AND AIMVendors.Reach_Code <> 'N'
        AND AIMVendors.Vn_Min + AIMVendors.Vn_Best > 0
END
            
OPEN VnIdCursor
-- Cursor Processing
WHILE 1 = 1
BEGIN
        FETCH NEXT FROM VnIdCursor INTO @VnId, @Assort, @Reach_Code, @Vn_Min, @Vn_Best
        
        -- Check for End of Cursor
        IF @@FETCH_STATUS <> 0
            BREAK
            
        -- Call the Vendor Sizing Routine for each Vendor/Assortment Combination
        -- select VnId = @VnId, Assort = @Assort, Reach_Code = @Reach_Code, Vn_Min = @Vn_Min, Vn_Best = @Vn_Best
        EXEC AIM_VendorSizing_Sp @VnId, @Assort, @Reach_Code, @Vn_Min, @Vn_Best
        END     -- End of Cursor Loop
    
-- Wrap Up
CLOSE VnIdCursor
DEALLOCATE VnIdCursor
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

