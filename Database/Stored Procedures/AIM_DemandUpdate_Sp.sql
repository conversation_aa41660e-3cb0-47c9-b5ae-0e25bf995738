SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DemandUpdate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DemandUpdate_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DemandUpdate_Sp
**	Desc: Updates the demand for an item 
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   Author:	Description:
**    ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
   
CREATE PROCEDURE AIM_DemandUpdate_Sp
(
        @Lcid	            				nvarchar(12),
        @Item            					nvarchar(25),
        @ItStat          					nvarchar(1),
        @ById            					nvarchar(12),
        @LeadTime        					smallint,
        @FcstMethod      					tinyint,
        @FcstDemand     					decimal(10, 2),
        @MAE             					decimal(10, 2),
        @MSE             					decimal(10, 2),
        @Trend         						decimal(10, 3),
        @FcstCycles      					int,
        @ZeroCount       					int,
        @DIFlag          					nvarchar(1),
        @DmdFilterFlag   					nvarchar(1),
        @TrkSignalFlag   					nvarchar(1),
        @UserDemandFlag  					nvarchar(1),
        @ByPassPct       					decimal(4, 3),
        @FcstUpdCyc      					int,
        @DSer            					decimal(4, 3),
        @Accum_Lt        					smallint,
        @ReviewTime      	    				smallint,
        @OrderPt         					int,
        @OrderQty        					int,
        @SafetyStock     					decimal(10, 2),
        @FcstRT          					decimal(10, 2),
        @FcstLT          					decimal(10, 2),
        @Fcst_Month      					decimal(10, 2),
        @Fcst_Quarter    					decimal(10, 2),
        @Fcst_Year       					decimal(10, 2),
        @Mean_NZ					    	decimal(10, 2),
        @StdDev_NZ					    	decimal(10, 2),
        @IntSafetyStock						decimal(10, 2),
        @IsIntermittent						nvarchar(1)
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

UPDATE Item
    SET ItStat = @ItStat , ById = @ById, LeadTime = @LeadTime, 
        FcstMethod = @FcstMethod, FcstDemand = @FcstDemand, MAE = @MAE, MSE = @MSE, 
        Trend = @Trend, FcstCycles = @FcstCycles, ZeroCount = @ZeroCount,    DIFlag = @DIFlag , 
        DmdFilterFlag = @DmdFilterFlag, TrkSignalFlag = @TrkSignalFlag, 
        UserDemandFlag = @UserDemandFlag, ByPassPct = @ByPassPct, FcstUpdCyc = @FcstUpdCyc, 
        DSer = @DSer , Accum_Lt = @Accum_Lt, ReviewTime = @ReviewTime, 
        OrderPt = @OrderPt, OrderQty = @OrderQty, SafetyStock = @SafetyStock, FcstRT = @FcstRT, 
        FcstLT = @FcstLT, Fcst_Month = @Fcst_Month , Fcst_Quarter = @Fcst_Quarter, Fcst_Year = @Fcst_Year, 
         FcstDate = convert(datetime, convert(nvarchar(10), getdate(), 101)),
    	Mean_NZ = @Mean_NZ, StdDev_NZ = @StdDev_NZ, IntSafetyStock = @IntSafetyStock, 
    	IsIntermittent = @IsIntermittent
        WHERE LcId = @Lcid 
    	and Item = @Item
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

