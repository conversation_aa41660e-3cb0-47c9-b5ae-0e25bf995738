SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewVendor_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewVendor_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ReviewVendor_Sp
**	Desc: Updates the Item table with data from the override.
**
**	Returns: 1) @@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
       
CREATE PROCEDURE AIM_ReviewVendor_Sp 
(
  	@VnId							nvarchar(12),
     	@Assort							nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @RC int,
    		@ReviewTotal 					int,
    		@LT_Total 					int,
    		@RC_Total 					int,
    		@PE_Total 					int
  
SET NOCOUNT ON
 
-- Validate the required parameters.
IF @VnId IS NULL
BEGIN
	RETURN 0
END

IF @Assort IS NULL
BEGIN
	RETURN 0
END
 		
-- Initialize Parameters
SELECT @RC = 0, @ReviewTotal = 0, @LT_Total = 0, @RC_Total = 0, @PE_Total = 0
     
EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', @ReviewTotal output, 
    	@LT_Total output, @RC_Total output, @PE_Total output, 'Y'

SELECT Vendor = @VnId, Assort = @Assort, 'Review Total' = @ReviewTotal, 'Lead Time Exceptions' = @LT_Total,
    	'Scheduled Reviews' = @RC_Total, 'Priority Exceptions' = @PE_Total

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

