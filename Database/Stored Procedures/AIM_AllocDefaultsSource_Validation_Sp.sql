if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocDefaultsSource_Validation_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocDefaultsSource_Validation_Sp]
GO
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AllocDefaultsSource_Validation_Sp
**	Desc: Verifies if a duplicate record exist in the table.
**
**	Returns: 1)  1 - Passed Validation Successfully
**               2)  0 - Failure, Duplicate Record exist
**             
**	Values:  
**              
**	Auth:   Wade Riza 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**
*******************************************************************************/
     
CREATE   PROCEDURE AIM_AllocDefaultsSource_Validation_Sp
(
      	@AllocDefaultsID   					numeric(9,0),
      	@Lcid_Source  						nvarchar(25)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

-- Validate the required parameters.
IF @AllocDefaultsID IS NULL OR @Lcid_Source IS NULL 
BEGIN
	RETURN 0
END

SELECT *
FROM AllocDefaultsSource
WHERE AllocDefaultsID = @AllocDefaultsID
AND Lcid_Source = @Lcid_Source


IF @@rowcount = 0      -- Record Not Found
BEGIN
             
        RETURN 1            -- SUCCEED
END
ELSE                    -- Record Found
BEGIN
        RETURN 0            -- FAIL
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

