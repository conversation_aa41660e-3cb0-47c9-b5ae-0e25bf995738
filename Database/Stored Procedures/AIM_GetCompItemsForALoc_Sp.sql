SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetCompItemsForALoc_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetCompItemsForALoc_Sp]
GO


/******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetCompItemsForALoc_Sp
**	Desc:Gets distict Componentitems from itemkitbom table for a given loction
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
     
  CREATE                PROCEDURE AIM_GetCompItemsForALoc_Sp
  (
      	@LcId           				nvarchar(12)
      

  )
  -- WITH ENCRYPTION	/* Production use must be encrypted */
 AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

 
  
  SET NOCOUNT ON
  
 SET NOCOUNT ON
 /* 
 SELECT DISTINCT ITEM AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT, Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,Fcst_Quarter,Fcst_Year
 FROM  ITEM WHERE ITEM  IN
(SELECT DISTINCT ITEM FROM AIMCOMPANIONITEMDETAIL WHERE 
 LCID =@LCID )
 AND LCID =@LCID
*/
SELECT DISTINCT ITEM AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT, Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,Fcst_Quarter,Fcst_Year
 FROM  ITEM WHERE ITEM  IN
(SELECT DISTINCT ITEM FROM AIMCOMPANIONITEMDETAIL,AIMCOMPANIONITEM WHERE 
AIMCOMPANIONITEMDETAIL.LCID =AIMCOMPANIONITEM.LCID AND
AIMCOMPANIONITEMDETAIL.MASTERITEM =AIMCOMPANIONITEM.MASTERITEM AND
AIMCOMPANIONITEM.ENABLEY_N ='Y' AND
 AIMCOMPANIONITEMDETAIL.LCID =@LCID )
 AND LCID =@LCID


 RETURN  @@rowcount




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

