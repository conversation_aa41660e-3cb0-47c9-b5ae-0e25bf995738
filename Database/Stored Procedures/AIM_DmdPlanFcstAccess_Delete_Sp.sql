if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstAccess_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstAccess_Delete_Sp]
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


/******************************************************************************
**	Name: AIM_DmdPlanFcstAccess_Delete_Sp
**	Desc: Deletes AIMFcstAccess records
**
**	Returns: 1)@Return_Stat - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Annalaksh<PERSON> Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstAccess_Delete_Sp
(
	@FcstSetupKey as int,
	@UserID as nvarchar (12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @Return_Stat int

	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL 
	OR @UserID IS NULL
	BEGIN
	  	RETURN -1
	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	AND @FcstLocked = 'N'
--	BEGIN
		DELETE FROM AIMFcstAccess
		WHERE 
			AIMFcstAccess.FcstSetupKey = @FcstSetupKey AND
			AIMFcstAccess.UserID = @UserID 

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		RETURN 1
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

