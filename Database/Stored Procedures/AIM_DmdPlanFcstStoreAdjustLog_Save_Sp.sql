SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstStoreAdjustLog_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstStoreAdjustLog_Save_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
**	Desc: Updates AIMFcstStoreAdjustLog for the given FcstID.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE    PROCEDURE AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
(
	@FcstId as nvarchar(12),
	@LcID as nvarchar(12),
	@Item as nvarchar(25),
	@AdjustType as tinyint,
	@AdjustQty as decimal(10, 2),
	@OverRideEnabled as tinyint,
	@AdjustBegDate as datetime,
	@AdjustEndDate as datetime,
	@AdjustUserID as nvarchar(12),
	@AdjustDesc as nvarchar(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @AccessCode as int
	DECLARE @RepositoryKey as numeric

	BEGIN
		SELECT @RepositoryKey =RepositoryKey
		FROM ForecastRepository
		WHERE FcstID =@FcstId
		If @@Rowcount =0 
		BEGIN
			Return -1
		END
	END
			
	INSERT INTO ForecastRepository_Log (
		RepositoryKey, LcID, Item, IsPromoted
		, AdjustType, AdjustQty,OverRideEnabled
		, AdjustBegDate , AdjustEndDate
		, AdjustDesc, AdjustUserID, AdjustDateTime
	) VALUES (
		@RepositoryKey, @LcID, @Item, 0	-- A new record is obviously not promoted to master yet
		, @AdjustType, @AdjustQty,@OverRideEnabled
		, @AdjustBegDate, @AdjustEndDate
		, @AdjustDesc, @AdjustUserID, GetDate()
	)
	
	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

