SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastPlannerReport_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastPlannerReport_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastPlannerReport_Sp
**	Desc: Get the Forecast Planner report date for a given FcstId
**
**	Returns: @@rowcount 
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:  		Description:
**	---------- 	------------	-----------------------------------------------
**	Oct-27-05	S.Uddanti	Initail version created
**					
*******************************************************************************/
     
CREATE PROCEDURE AIM_ForecastPlannerReport_Sp
(@FcstId	 nvarchar(12))
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
/* Declare working variables */
DECLARE	
	-- Working Storage
@Fcst	bit,
@MasterFcstAdj bit,
@FcstAdj	bit,
@FcstNetReq	bit,
@FcstAdjNetReq	bit,
@QtyProjInv	bit,
@HistDmd	bit,
@ProdConst	bit,
@DateFormat	nvarchar

SET NOCOUNT ON
-- Get the System Control record
Select @DateFormat =DateFormat
From Sysctrl
SELECT @Fcst = Calc_SysFcst,@MasterFcstAdj =Calc_MasterFcstAdj,
@FcstAdj=Calc_FcstAdj,@FcstNetReq=Calc_NetReq,@FcstAdjNetReq=Calc_AdjNetReq,
@QtyProjInv=Calc_ProjInv,@HistDmd=Calc_HistDmd,@ProdConst=Calc_ProdConst
FROM AIMFcstSetup
Where FcstId =@FcstId

select FR.FcstId,FRD.lcid,FRD.item,
CASE WHEN @Fcst =1  THEN cast(FRD.fcst AS VARCHAR(12) ) ELSE 'NULL' END AS SysFcst,
CASE WHEN @MasterFcstAdj =1  THEN cast(FRD.MasterFcstAdj AS VARCHAR(12) ) ELSE 'NULL' END As MasterFcstAdj,
CASE WHEN @FcstAdj =1  THEN cast(FRD.fcstAdj AS VARCHAR(12) ) ELSE 'NULL' END As FcstAdj,
CASE WHEN @FcstNetReq =1  THEN cast(FRD.FcstNetReq AS VARCHAR(12) ) ELSE 'NULL' END As NetReq,
CASE WHEN @FcstAdjNetReq =1  THEN cast(FRD.FcstAdjNetReq AS VARCHAR(12) ) ELSE 'NULL' END As AdjNetreq,
CASE WHEN @QtyProjInv =1  THEN cast(FRD.QtyProjectedInventory AS VARCHAR(12) ) ELSE 'NULL' END As ProjInv ,
CASE WHEN @HistDmd =1  THEN cast(FRD.HistDmd AS VARCHAR(12) ) ELSE 'NULL' END As HistDmd,
CASE WHEN @ProdConst =1  THEN cast(FRD.ProdConst AS VARCHAR(12) ) ELSE 'NULL' END As ProdConst,
convert(char(10), FRD.fcstPdBegDate,101) As FcstPdBegDate,
convert(char(10), FRD.FcstPdEndDate,101) As FcstPdEndDate
from forecastrepository FR WITH (NOLOCK)
inner join forecastrepositorydetail FRD WITH (NOLOCK) 
on FR.Repositorykey=FRD.Repositorykey
and FR.FcstId=@FcstId
Order By FRD.Lcid,FRD.Item

RETURN @@ROWCOUNT

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

