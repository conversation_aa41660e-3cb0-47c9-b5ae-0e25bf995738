SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Class_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Class_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Class_List_Sp
**	Desc: Gets the Class Codes
**
**	Returns: 1)
**	Values:  Recordset - AIMClasses
**              
**	Auth:   Randy Sadler
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	 	Author:      		Description:
**	---------- 	------------ 		-----------------------------------------------
**	09/24/2002 Wade Riza 		Updated Class Codes to NVARCHAR(50)
**                            			and added LangID 
**	2004/01/30 A.Stocksdale 	Updated SELECT to use case when for the where clause
**	2004/03/16	A.Stocksdale	Added ClassLevel to the parameter list
*******************************************************************************/
    
CREATE PROCEDURE AIM_Class_List_Sp
(
	@Class nvarchar(50) = 'ALL',
	@ClassLevel as tinyint = NULL,
	@LangID nvarchar(10) = 'ALL'
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON
	 	 
	SELECT 
		Class,
		ClassDesc,
		ClassLevel,
		LangID
	FROM AIMClasses 
	WHERE Class = CASE
		WHEN @Class <> '' AND UPPER(@Class) <> 'ALL' THEN @Class
		ELSE AIMClasses.Class
		END
	AND LangID = CASE
		WHEN @LangID <> '' AND UPPER(@LangID) <> 'ALL' THEN @LangID
		ELSE AIMClasses.LangID
		END
	AND ClassLevel = CASE
		WHEN @ClassLevel IS NOT NULL THEN @ClassLevel
		ELSE AIMClasses.ClassLevel
		END
	ORDER BY 
		ClassLevel, 
		Class
	
	RETURN @@ROWCOUNT

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

