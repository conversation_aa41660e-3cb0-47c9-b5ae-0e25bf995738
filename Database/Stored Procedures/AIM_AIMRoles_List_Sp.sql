if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMRoles_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMRoles_List_Sp]
GO


/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMRoles_List_Sp
**	Desc: RETURNS ALL ROLES AND REPORTS  TO ROLE RELATIONAL INFORMATION
**
**	Returns: 1)  ALL ROLES
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
CREATE PROCEDURE AIM_AIMRoles_List_Sp

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int

SET NOCOUNT ON

SELECT AR.ROLEID,AR.ROLEDESCRIPTION,AR.ROLELEVEL,AR.SCARRAY,AR.REPTOROLEID,AR.ALTSOURCEEXCPTS,AR1.ROLELEVEL AS REPTOROLELEVEL
FROM aimroles AR,AIMROLES AR1
WHERE AR.RepToRoleID=AR1.ROLEID
ORDER BY AR.ROLEDESCRIPTION
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
  	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN @found  -- SUCCESSFUL
END
GO


