SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AssignSeasonsDefaults_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AssignSeasonsDefaults_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AssignSeasonsDefaults_Sp
**	Desc: Updates Item with Item Default values
**
**	Returns: 1)  @rowcount - Successful
**
**	Values:  Recordset - Items
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   Author:	Description:
**    ---------- ------------	-----------------------------------------------
**  	
*******************************************************************************/       
  
CREATE PROCEDURE AIM_AssignSeasonsDefaults_Sp
(
  	@LcId       						nvarchar(12) = 'All'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @ClassOption    					tinyint
  
SET NOCOUNT ON 
  
SELECT @ClassOption = ClassOption
FROM SysCtrl

IF @LcId = 'All'
BEGIN
  	IF @ClassOption = 1
        
	UPDATE item
	SET item.said = itdefaults.said
        FROM Item 
        INNER JOIN ItDefaults 
	ON Item.Lcid = ItDefaults.LcId 
        AND Item.Class1 = ItDefaults.Class
        WHERE Item.Lcid <> 'DEFAULT'

        IF @ClassOption = 2
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults 
		ON Item.Lcid = ItDefaults.LcId 
                AND Item.Class2 = ItDefaults.Class
                WHERE Item.Lcid <> 'DEFAULT'
	END
        IF @ClassOption = 3
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults ON Item.Lcid = ItDefaults.LcId 
                AND Item.Class3 = ItDefaults.Class
                WHERE Item.Lcid <> 'DEFAULT'
	END
	IF @ClassOption = 4
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults ON Item.Lcid = ItDefaults.LcId 
                AND Item.Class4 = ItDefaults.Class
		WHERE Item.Lcid <> 'DEFAULT'
	END
END
ELSE
BEGIN
  	IF @ClassOption = 1
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults ON Item.Lcid = ItDefaults.LcId 
                AND Item.Class1 = ItDefaults.Class
                WHERE item.lcid = @LcId
	END
        IF @ClassOption = 2
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults ON Item.Lcid = ItDefaults.LcId 
                AND Item.Class2 = ItDefaults.Class
                WHERE item.lcid = @LcId
	END
        IF @ClassOption = 3
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults ON Item.Lcid = ItDefaults.LcId 
                AND Item.Class3 = ItDefaults.Class
                WHERE item.lcid = @LcId
	END
        IF @ClassOption = 4
	BEGIN
		UPDATE item 
		SET item.said = itdefaults.said
                FROM Item 
                INNER JOIN ItDefaults ON Item.Lcid = ItDefaults.LcId 
                WHERE item.lcid = @LcId
                AND Item.Class4 = ItDefaults.Class
	END
END
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

