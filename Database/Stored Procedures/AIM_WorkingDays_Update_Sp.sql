SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_WorkingDays_Update_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_WorkingDays_Update_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_WorkingDays_Update_Sp
**	Desc: Updates the AIMWorkingDays table with given parameters.
**
** 	Returns: 1)@rowcount/error code
** 	Values:  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/26
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:      Author:      Description:
** 	---------- ------------ -----------------------------------------------
** 
*******************************************************************************/
    
CREATE    Procedure AIM_WorkingDays_Update_Sp
(	@Day1 bit,
	@Day2 bit,
	@Day3 bit,
	@Day4 bit,
	@Day5 bit,
	@Day6 bit,
	@Day7 bit
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	SET NOCOUNT ON

	DECLARE @RtnCode as int

	UPDATE AIMWorkingDays SET
		Day1 =  @Day1,
		Day2 =  @Day2,
		Day3 =  @Day3,
		Day4 =  @Day4,
		Day5 =  @Day5,
		Day6 =  @Day6,
		Day7 =  @Day7
	
	SET @RtnCode = @@ROWCOUNT
	IF @RtnCode = 0
	BEGIN
		INSERT INTO AIMWorkingDays
		VALUES (
			ISNULL(@Day1, 0),
			ISNULL(@Day2, 0),
			ISNULL(@Day3, 0),
			ISNULL(@Day4, 0),
			ISNULL(@Day5, 0),
			ISNULL(@Day6, 0),
			ISNULL(@Day7, 0)
		)
		SET @RtnCode = @@ROWCOUNT
	END

	IF @RtnCode > 0 
	BEGIN
		RETURN @RtnCode
	END
	ELSE IF @@ERROR <> 0
	BEGIN	
		RETURN @@ERROR
	END
	
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

