SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_MDCSummary_Upd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_MDCSummary_Upd_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_MDCSummary_Upd_Sp
**	Desc: Updates the Item table with data from the override.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	    Author:	 Description:
**	----------  ------------ ----------------------------------------------
**      2003/06/26  Srinivas U	 Fix code to populate MDC info into item table
**      2003/08/06  Srinivas U	 Add some more columns to Item update
**      2003/10/01  Srinivas U   Fixed update to item for Keeco  
*******************************************************************************/
     
CREATE PROCEDURE AIM_MDCSummary_Upd_Sp
(
      	@MDC            				nvarchar(12),
  	@Item           				nvarchar(25),
    	@Accum_Lt       				smallint,
    	@ReviewTime     				smallint,
    	@OrderPt        				int,
    	@OrderQty       				int,
    	@SafetyStock    				decimal(10, 2),
    	@FcstRT         				decimal(10, 2),
    	@FcstLT         				decimal(10, 2),
    	@Fcst_Month     				decimal(10, 2),
    	@Fcst_Quarter   				decimal(10, 2),
    	@Fcst_Year      				decimal(10, 2)  
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @MDCKey  		        		nvarchar(12),
        @ItemKey 		         		nvarchar(25) 
            
SET NOCOUNT ON
    
-- Check for the existance of the MDC Summary Record
SELECT @MDCKey = MDC, @ItemKey = Item 
FROM MDCSummary
WHERE MDC = @MDC 
AND Item = @Item
        
IF @@rowcount = 0   -- Not Found
BEGIN
        INSERT INTO MDCSummary(MDC, Item, Accum_Lt, ReviewTime, OrderPt, OrderQty, 
            SafetyStock, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year) 
        VALUES (@MDC, @Item, @Accum_Lt, @ReviewTime, @OrderPt, @OrderQty, 
            @SafetyStock, @FcstRT, @FcstLT, @Fcst_Month, @Fcst_Quarter, @Fcst_Year)
END
ELSE                -- Found
BEGIN
        UPDATE MDCSummary 
	SET Accum_Lt = @Accum_LT, ReviewTime = @ReviewTime, 
            OrderPt = @OrderPt, OrderQty = @OrderQty, SafetyStock = @SafetyStock, 
            FcstRT = @FcstRT, FcstLT = @FcstLT, Fcst_Month = @Fcst_Month, 
            Fcst_Quarter = @Fcst_Quarter, Fcst_Year = @Fcst_Year
        WHERE MDC = @MDC and Item = @Item 
END

BEGIN

UPDATE Item 
 SET  SafetyStock =@SafetyStock,
	FcstRT =@FcstRT,
	FcstLT =@FcstLT,
	Fcst_Month =@Fcst_Month,
	Fcst_Quarter=@Fcst_Quarter,
	Fcst_Year=@Fcst_Year,
	OrderPt=@OrderPt,
	OrderQty =@OrderQty,
	FcstDemand=(@Fcst_Year/52)
 WHERE Lcid =@MDC and
 	Item =@Item
END

RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

