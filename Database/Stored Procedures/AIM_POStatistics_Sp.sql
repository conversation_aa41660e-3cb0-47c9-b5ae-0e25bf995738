SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POStatistics_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POStatistics_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_POStatistics_Sp
**	Desc: Updates the POStatistics table.
**
**	Returns: 
**
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_POStatistics_Sp
(
        @SeqNbr      	 					int,
  	@ById       						nvarchar(12),
     	@VnId       						nvarchar(12),
     	@Assort     						nvarchar(12),
     	@LcId       						nvarchar(12),
    	@SelOption  						tinyint 
                                                                -- 1 = Select by ById, VnId, Assort
 	                                                        -- 2 = Select by ById, VnId, Assort, LcId
    	                                			-- 3 = Select all released PO Detail Lines
    	                                			-- 4 = Select by Location
    	                                			-- 5 = Select by ById
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON
 
IF @SelOption = 1                   -- Select by ById, VnId, Assort    
BEGIN
        INSERT INTO POStatistics
        SELECT @SeqNbr, AIMPO.ById, AIMPO.VnId, AIMPO.Assort, 
            IsDate = max(PODetail.IsDate), 
            Original_NbrLines = SUM(CASE 
                WHEN PODetail.Original_VSOQ > 0 THEN 1
                ELSE 0
                END),
            NbrLines = SUM(CASE 
    			WHEN PODetail.OrdStatus = 'R' AND PODetail.VSOQ > 0 THEN 1
                ELSE 0
                END),
            Original_VSOQ = SUM(PODetail.Original_VSOQ),
            VSOQ = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ
    			ELSE 0
    			END),
            Original_ExtCost = SUM(PODetail.Original_VSOQ * PODetail.Cost),
            ExtCost = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ * PODetail.Cost
    			ELSE 0
    			END)
            FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
            WHERE AIMPO.POStatus = 'R'
            AND AIMPO.ById = @ById
            AND AIMPO.VnId = @VnId
            AND AIMPO.Assort = @Assort
            GROUP BY AIMPO.ById, AIMPO.VnId, AIMPO.Assort    
     
END
IF @SelOption = 2                   -- Select by ById, VnId, Assort, LcId
BEGIN
        INSERT INTO POStatistics
        SELECT @SeqNbr, AIMPO.ById, AIMPO.VnId, AIMPO.Assort, 
            IsDate = max(PODetail.IsDate), 
            Original_NbrLines = SUM(CASE 
                WHEN PODetail.Original_VSOQ > 0 THEN 1
                ELSE 0
                END),
            NbrLines = SUM(CASE 
    			WHEN PODetail.OrdStatus = 'R' AND PODetail.VSOQ > 0 THEN 1
                ELSE 0
                END),
            Original_VSOQ = SUM(PODetail.Original_VSOQ),
            VSOQ = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ
    			ELSE 0
    			END),
            Original_ExtCost = SUM(PODetail.Original_VSOQ * PODetail.Cost),
            ExtCost = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ * PODetail.Cost
    			ELSE 0
    			END)
            FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
            WHERE AIMPO.POStatus = 'R'
            AND AIMPO.ById = @ById
            AND AIMPO.VnId = @VnId
            AND AIMPO.Assort = @Assort
    		AND PODetail.LcId = @LcId
            GROUP BY AIMPO.ById, AIMPO.VnId, AIMPO.Assort  
END
IF @SelOption = 3               -- Select all released PO Detail Lines
BEGIN
        INSERT INTO POStatistics
        SELECT @SeqNbr, AIMPO.ById, AIMPO.VnId, AIMPO.Assort, 
            IsDate = max(PODetail.IsDate), 
            Original_NbrLines = SUM(CASE 
                WHEN PODetail.Original_VSOQ > 0 THEN 1
                ELSE 0
                END),
            NbrLines = SUM(CASE 
    			WHEN PODetail.OrdStatus = 'R' AND PODetail.VSOQ > 0 THEN 1
                ELSE 0
                END),
            Original_VSOQ = SUM(PODetail.Original_VSOQ),
            VSOQ = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ
    			ELSE 0
    			END),
            Original_ExtCost = SUM(PODetail.Original_VSOQ * PODetail.Cost),
            ExtCost = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ * PODetail.Cost
    			ELSE 0
    			END)
            FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
            WHERE AIMPO.POStatus = 'R'
            GROUP BY AIMPO.ById, AIMPO.VnId, AIMPO.Assort  
END
IF @SelOption = 4               -- Select by Location
BEGIN
        INSERT INTO POStatistics
        SELECT @SeqNbr, AIMPO.ById, AIMPO.VnId, AIMPO.Assort, 
            IsDate = max(PODetail.IsDate), 
            Original_NbrLines = SUM(CASE 
                WHEN PODetail.Original_VSOQ > 0 THEN 1
                ELSE 0
                END),
            NbrLines = SUM(CASE 
    			WHEN PODetail.OrdStatus = 'R' AND PODetail.VSOQ > 0 THEN 1
                ELSE 0
                END),
            Original_VSOQ = SUM(PODetail.Original_VSOQ),
            VSOQ = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ
    			ELSE 0
    			END),
            Original_ExtCost = SUM(PODetail.Original_VSOQ * PODetail.Cost),
            ExtCost = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ * PODetail.Cost
    			ELSE 0
    			END)
            FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
            WHERE AIMPO.POStatus = 'R'
    		AND PODetail.LcId = @LcId
            GROUP BY AIMPO.ById, AIMPO.VnId, AIMPO.Assort   
END
IF @SelOption = 5                   -- Select by ById 
BEGIN
        INSERT INTO POStatistics
        SELECT @SeqNbr, AIMPO.ById, AIMPO.VnId, AIMPO.Assort, 
            IsDate = max(PODetail.IsDate), 
            Original_NbrLines = SUM(CASE 
                WHEN PODetail.Original_VSOQ > 0 THEN 1
                ELSE 0
                END),
            NbrLines = SUM(CASE 
    			WHEN PODetail.OrdStatus = 'R' AND PODetail.VSOQ > 0 THEN 1
                ELSE 0
                END),
            Original_VSOQ = SUM(PODetail.Original_VSOQ),
            VSOQ = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ
    			ELSE 0
    			END),
            Original_ExtCost = SUM(PODetail.Original_VSOQ * PODetail.Cost),
            ExtCost = SUM(CASE PODetail.OrdStatus
    			WHEN 'R' THEN PODetail.VSOQ * PODetail.Cost
    			ELSE 0
    			END)
            FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
            WHERE AIMPO.POStatus = 'R'
    		AND PODetail.ById = @ById
            GROUP BY AIMPO.ById, AIMPO.VnId, AIMPO.Assort 
END

RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

