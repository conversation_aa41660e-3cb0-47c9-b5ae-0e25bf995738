
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VerifyAccessRole_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VerifyAccessRole_Sp]
GO


/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VerifyAccessRole_Sp
**	Desc: Verifies a Role's access level to the AIM Functions.
**
**	Returns: 1)  1 - Read Write Access
**               2)  2 - Full Access		
**               3)  0 - No Access
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
CREATE PROCEDURE AIM_VerifyAccessRole_Sp
(
      	@ROLEID             			nvarchar(12),
       	@AIMFunction        			int,
      	@AIMFunctionDesc    		        nvarchar(40) OUTPUT
)    

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE
     	@AccLvl     		    		nvarchar(1),
        @ScArray    		    		nvarchar(100),
        @SLevel     		    		tinyint,
    	@Rowcount			        int 
            
SET NOCOUNT ON

-- Validate the required parameters.

IF @ROLEID IS NULL
BEGIN
	RETURN 0
END
IF @AIMFunction IS NULL
BEGIN
	RETURN 0
END
SELECT @ScArray = ScArray--, @SLevel = SLevel
FROM AIMROLES
WHERE ROLEID = @ROLEID
Select @rowcount = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN 0  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
        
IF @rowcount = 0       -- Not Found
BEGIN
       RETURN 0
END
ELSE                    -- Found
BEGIN
        -- Get the access level for this AIM function
        SELECT @AccLvl = substring(@ScArray, @AIMFunction, 1)
        
        SELECT @AIMFunctionDesc = FuncDesc 
	FROM AIMFunctions
        WHERE CtrlNbr = @AIMFunction
  	-- Check for SQL Server errors.
  	IF @@ERROR <> 0 
  	BEGIN
 		RETURN 0  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END
        
        IF @AccLvl = 'N'
            RETURN 0
        IF @AccLvl = 'R'
            RETURN 1
        IF @AccLvl = 'Y'
            RETURN 2
END
GO

