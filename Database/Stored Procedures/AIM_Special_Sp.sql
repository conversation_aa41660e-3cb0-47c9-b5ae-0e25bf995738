SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Special_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Special_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTH<PERSON>IZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Special_Sp
**	Desc: Updates the PO Detail table with the special data
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 2 - UPDATE
**               3)-1 - Failure, Location must exists
**               4)-2 - Failure, Item can not exists
**               5)-3 - Failure, Qty must be positive
**               6) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**
*******************************************************************************/
     
CREATE PROCEDURE AIM_Special_Sp
(
       	@POLineType	        				smallint, 
     	@OrdStatus	        				nvarchar(1), 
        @RevCycle	        				nvarchar(8), 
       	@ById		        				nvarchar(12), 
       	@VnId		        				nvarchar(12), 
       	@Assort					            nvarchar(12), 
       	@Lcid		        				nvarchar(12), 
       	@Item		        				nvarchar(25), 
     	@ItDesc							    nvarchar(30),
        @POType	            				nvarchar(1), 
       	@AvailQty	        				int, 
        @PackRounding	    				nvarchar(1), 
       	@RSOQ		        				int, 
       	@SOQ		        				int, 
       	@VSOQ		        				int, 
        @UOM		        				nvarchar(6), 
        @BuyingUOM	        				nvarchar(6), 
       	@ConvFactor	        				smallint, 
       	@IsDate	            				datetime, 
       	@DuDate	            				datetime, 
        @LastWeekSalesFlag  				nvarchar(1), 
    	@Cost							    decimal(10,4),
     	@Zone							    nvarchar(1)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      						int,
        @POSeqId    						int
            
SET NOCOUNT ON
 
-- Validate Special
-- The Lcid must be a valid AIM Location
IF NOT EXISTS(SELECT lcid FROM AIMLocations WHERE lcid = @LcId)
BEGIN
  	RETURN -1
END
-- The Item number must not exist
IF EXISTS(SELECT item FROM item WHERE lcid = @LcId AND item = @Item)
BEGIN
    	RETURN -2
END
-- The quantity must be positive
IF @VSOQ <= 0 
BEGIN
    	RETURN -3
END

SELECT @POSeqId = POSeqId 
FROM PODetail
WHERE VnId = @VnId
AND Assort = @Assort
AND Item = @Item
AND LcId = @LcId
        
SELECT @found = @@rowcount
  
IF @found = 0
BEGIN
        INSERT INTO PODetail
        (POLineType, OrdStatus, RevCycle, ById, 
        VnId, Assort, Lcid, Item, ItDesc, POType, AvailQty, PackRounding, 
        RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, ConvFactor, IsDate, 
        DuDate, LastWeekSalesFlag, Cost, Zone)
        VALUES (@POLineType, @OrdStatus, @RevCycle, @ById, 
        @VnId, @Assort, @Lcid, @Item, @ItDesc, @POType, @AvailQty, @PackRounding, 
        @RSOQ, @SOQ, @VSOQ, @VSOQ, @UOM, @BuyingUOM, @ConvFactor, @IsDate, 
        @DuDate, @LastWeekSalesFlag, @Cost, @Zone)
       
        RETURN @@rowcount
END
ELSE
BEGIN
        UPDATE PODetail
        SET POLineType = @POLineType, OrdStatus = @OrdStatus, 
            RevCycle = @RevCycle, ById = @ById, 
            VnId = @VnId, Assort = @Assort, Lcid = @Lcid, Item = @Item, 
            POType = @POType, AvailQty = @AvailQty, 
            PackRounding = @PackRounding, RSOQ = @RSOQ, SOQ = @SOQ, 
            VSOQ = @VSOQ, Original_VSOQ = @VSOQ, UOM = @UOM, BuyingUOM = @BuyingUOM, 
            ConvFactor = @ConvFactor, IsDate = @IsDate, DuDate = @DuDate, 
            LastWeekSalesFlag = @LastWeekSalesFlag, Cost = @Cost, Zone = @Zone
    	WHERE PODetail.vnid = @vnid
    	AND PODetail.assort = @assort
    	AND PODetail.lcid = @lcid
    	AND PODetail.item  = @item
        
	IF @@rowcount > 0
        BEGIN
	    RETURN 2			-- UPDATE
	END
        ELSE
    	BEGIN
	    RETURN 0			-- ERROR
  	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

