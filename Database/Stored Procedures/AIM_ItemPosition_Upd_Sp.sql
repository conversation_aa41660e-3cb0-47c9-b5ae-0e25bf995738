SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
				WHERE ID = OBJECT_ID(N'[dbo].[AIM_ItemPosition_Upd_Sp]') 
				AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
DROP PROCEDURE[dbo].[AIM_ItemPosition_Upd_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND <PERSON>ALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemPosition_Upd_Sp
**	Desc: Update Records on the Item position 
**		  (On-Hand Position Tab/Buyer Review Screen).
**
**	Returns: 
**             
**	Values:  
**              
**	Auth:   Mohammed.
**	Date:   2003/03/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      2003/06/19 Mohammed     Add two new parameters: VnId and Assort 
**                              (MultiVendor)  
*******************************************************************************/

CREATE PROCEDURE AIM_ItemPosition_Upd_Sp
(
      	@Item               nvarchar(25),
      	@LcId               nvarchar(25),
      	@VnId               nvarchar(12),  -- Mohammed - MultiVendor
      	@Assort             nvarchar(12),  -- Mohammed - MultiVendor
    	@VSOQ	            int,
    	@SOQ	            int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
    
UPDATE PODETAIL 
		SET VSOQ = @VSOQ, 
		     SOQ = @SOQ  
 WHERE PODetail.Item = @Item
    AND PODetail.LcID = @LcID   
    AND PODetail.VnID = @vnId       -- Mohammed - MultiVendor
    AND PODetail.Assort = @Assort   -- Mohammed - MultiVendor

	RETURN
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

