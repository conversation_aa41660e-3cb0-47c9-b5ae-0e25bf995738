SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_CostPriceListPrice_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_CostPriceListPrice_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_CostPriceListPrice_Save_SP
**	Desc: Updates the Cost, Price, and List Price for an Item
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Cost, Price, List Price
**              
**	Auth:   Srinivas Uddanti
**	Date:   08/19/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**	---------- ------------ -----------------------------------------------
**	08/20/2002 Wade Riza    Updated Return Codes and Error Handling
**	10/01/2002 Srinivas U   Updated with LastDate
**	10/29/2002 Wade Riza    Updated with conparisions on Day, Month & Year
*******************************************************************************/

CREATE PROCEDURE AIM_CostPriceListPrice_Save_Sp
(
       	@LcId            	nvarchar(12), 
   	@Item        		nvarchar(25), 
   	@Price        		decimal(10,4),
 	@Cost      		decimal(10,4),
 	@ListPrice     		decimal(10,4),
 	@Stkdate     		nvarchar(10)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found        	int,
 	@Lastdate      		datetime,
	@LDMonth		int,
	@LDDay			int,
	@LDYear			int,
	@SDDay			int,
	@SDMonth		int,
	@SDYear			int,
	@ItemFound 		nvarchar(10)

SET NOCOUNT ON

-- Validate the required parameters.
IF @LcID IS NULL
BEGIN
 	RETURN -1
END

IF @Item IS NULL
BEGIN
 	RETURN -1
END

IF @Price IS NULL
BEGIN
 	RETURN -1
END
    
IF @Cost IS NULL
BEGIN
 	RETURN -1
END

IF @ListPrice IS NULL
BEGIN
 	RETURN -1
END

IF IsDate(@Stkdate) =0 -- Invalid date
BEGIN
	SELECT @Stkdate = CONVERT(nvarchar(10),GETDATE(),101)
	SELECT @SDDay = Day(@Stkdate)
	SELECT @SDMonth = Month(@Stkdate)
	SELECT @SDYear = Year(@Stkdate)
END
ELSE
BEGIN
	SELECT @SDDay = Day(@Stkdate)
	SELECT @SDMonth = Month(@Stkdate)
	SELECT @SDYear = Year(@Stkdate)
END

BEGIN
 	SELECT TOP 1  @Lastdate =effectivedatetime 
	FROM itempricing
 	WHERE Lcid=@Lcid AND
 	Item =@Item
 	ORDER BY effectivedatetime DESC

  	SELECT @found = @@rowcount
	
	IF @found = 0 
  	BEGIN
 		SELECT @Lastdate = CONVERT(nvarchar(10),GETDATE(),101)
		SELECT @LDDay = Day(@Lastdate)
		SELECT @LDMonth = Month(@Lastdate)
		SELECT @LDYear = Year(@Lastdate)		
  	END
	ELSE
	BEGIN
		SELECT @LDDay = Day(@Lastdate)
		SELECT @LDMonth = Month(@Lastdate)
		SELECT @LDYear = Year(@Lastdate)
	END
END

SELECT @ItemFound = 'ItemFound' 
FROM ItemPricing
WHERE Cost =@Cost AND
Price =@Price AND
ListPrice = @ListPrice AND
(Day(EffectiveDatetime) = @LDDay AND
Month(EffectiveDatetime) = @LDMonth AND
Year(EffectiveDatetime) = @LDYear) AND
Item =@Item AND
LcID =@LcID

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0 
BEGIN
 	INSERT INTO ItemPricing
 	VALUES (@LcID,@Item,@StkDate,@Cost,@Price,@ListPrice)
End

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
 	RETURN 0
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

