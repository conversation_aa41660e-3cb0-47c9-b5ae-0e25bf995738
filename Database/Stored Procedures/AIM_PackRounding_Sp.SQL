SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_PackRounding_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_PackRounding_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_PackRounding_Sp
**	Desc: Returns the Item Qty from the change of UOM.
**
**	Returns: 1) @return - Can be Zero
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
    
CREATE PROCEDURE AIM_PackRounding_Sp
(
        @RSOQ         						float,
      	@UOM          						nvarchar(6),
        @ConvFactor   						int,
        @BuyingUOM    						nvarchar(6) OUTPUT,
        @PackRounding 						nvarchar(1),
        @IMin         						int,
        @IMax        	 					int 
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE	@packqty 					float

SET NOCOUNT ON

SELECT @packqty = 0

-- Check for an invalid conversion factor
IF @ConvFactor <= 0 or RTRIM(@BuyingUOM) = '' or @RSOQ <= 0
BEGIN
        SELECT @BuyingUOM = @UOM
        RETURN ROUND(@RSOQ, 0)
END
  
-- Restrict order quantity based on IMIN and IMAX
IF @RSOQ > @IMax and @IMax > 0
BEGIN
      SELECT @RSOQ = @IMax, @PackRounding = 'D'
END

IF @RSOQ < @IMin
BEGIN
        SELECT @RSOQ = @IMin, @PackRounding = 'U'
END

SELECT @packqty = @RSOQ / @ConvFactor
 
SELECT @packqty = CASE @PackRounding
WHEN 'D' THEN floor(@packqty)	        -- Round down
WHEN 'U' THEN ceiling(@packqty)	        -- Round up
WHEN 'R' THEN round(@packqty, 0)	    -- Round
ELSE round(@packqty, 0)
END
  
-- Zero pack size is not allowed
IF @packqty = 0
BEGIN
        SELECT @packqty = 1
END

RETURN ROUND(@packqty * @ConvFactor, 0)

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

