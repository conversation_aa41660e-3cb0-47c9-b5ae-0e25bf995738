SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DUGet_ShippedWithSubs_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DUGet_ShippedWithSubs_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DUGet_ShippedWithSubs_Sp
**	Desc: Modified AIM_DUGet_Sp to add new case of T (Shipped with Substitue)
** 	      and made it small by breaking it up
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Cost, Price, List Price
**              
**	Auth:   Srinivas Uddanti
**	Date:   09/08/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**	---------- ------------ -----------------------------------------------
**	
*******************************************************************************/
     
CREATE PROCEDURE AIM_DUGet_ShippedWithSubs_Sp
(
      @SelLcId            					nvarchar(12),
      @SelItem            					nvarchar(25),
      @FcstYear           					int,
      @FcstPeriod        					smallint,
      @ForceUpdateFlag    					nvarchar(1),
      @SAVersion          					smallint,
      @CurFcstUpdCyc						int,
      @StartYear						int
)
  	
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
 select ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	 ItemHistory.CPS01, 
      	ItemHistory.CPS02, ItemHistory.CPS03, ItemHistory.CPS04, 
       	ItemHistory.CPS05, ItemHistory.CPS06, ItemHistory.CPS07, 
      	ItemHistory.CPS08, ItemHistory.CPS09, ItemHistory.CPS10, 
       	ItemHistory.CPS11, ItemHistory.CPS12, ItemHistory.CPS13, 
       	ItemHistory.CPS14, ItemHistory.CPS15, ItemHistory.CPS16, 
       	ItemHistory.CPS17, ItemHistory.CPS18, ItemHistory.CPS19, 
       	ItemHistory.CPS20, ItemHistory.CPS21, ItemHistory.CPS22, 
       	ItemHistory.CPS23, ItemHistory.CPS24, ItemHistory.CPS25, 
       	ItemHistory.CPS26, ItemHistory.CPS27, ItemHistory.CPS28, 
       	ItemHistory.CPS29, ItemHistory.CPS30, ItemHistory.CPS31, 
       	ItemHistory.CPS32, ItemHistory.CPS33, ItemHistory.CPS34, 
       	ItemHistory.CPS35, ItemHistory.CPS36, ItemHistory.CPS37, 
       	ItemHistory.CPS38, ItemHistory.CPS39, ItemHistory.CPS40, 
      	ItemHistory.CPS41, ItemHistory.CPS42, ItemHistory.CPS43, 
       	ItemHistory.CPS44, ItemHistory.CPS45, ItemHistory.CPS46, 
      	ItemHistory.CPS47, ItemHistory.CPS48, ItemHistory.CPS49, 
       	ItemHistory.CPS50, ItemHistory.CPS51, ItemHistory.CPS52
	into #TempItemHistory 
	from itemhistory
	where item ='XXXXX'



-- Issue Appropriate SQL Statement
IF @SelItem = '' and @ForceUpdateFlag = 'Y' 
BEGIN
	INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	From itemhistory
	Where ItemHistory.Lcid = @SelLcid
       --AND ItStatus.DmdUpd = 'Y'
      	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
        OR ItemHistory.HisYear IS NULL)
      	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc

       	SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
       	Item.OptionID, Item.Class1, Item.Class2, Item.Class3, 
       	Item.Class4, Item.BinLocation, Item.BuyStrat, Item.VelCode, 
       	Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag,
       	Item.SaId, Item.PmId, Item.UPC, Item.Weight, Item.Cube, 
       	Item.Price, Item.Cost, Item.BkQty01, Item.BkCost01, 
       	Item.BkQty02, Item.BkCost02, Item.BkQty03, Item.BkCost03, 
       	Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, 
       	Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, 
       	Item.BkQty08, Item.BkCost08, Item.BkQty09, Item.BkCost09, 
       	Item.BkQty10, Item.BkCost10, Item.UOM, Item.ConvFactor, 
       	Item.BuyingUOM, Item.ReplenCost2, Item.Oh, Item.Oo, Item.ComStk, 
       	Item.BkOrder, Item.BkComStk, Item.LeadTime, 
        Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, 
        Item.SSAdj, Item.UserMin, Item.UserMax, Item.UserMethod, 
        Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
        Item.UserFcstExpDate, Item.MAE, Item.MSE, Item.Trend, 
        Item.FcstCycles, Item.ZeroCount, Item.DIFlag, 
        Item.DmdFilterFlag, Item.TrkSignalFlag, Item.UserDemandFlag, 
        Item.ByPassPct, Item.FcstUpdCyc, Item.LTVFact, Item.PlnTT, 
        Item.ZOPSw, Item.OUTLSw, Item.ZSStock, Item.DSer, 
        Item.Freeze_BuyStrat, Item.Freeze_Byid, 
        Item.Freeze_LeadTime, Item.Freeze_OptionID, 
        Item.Freeze_DSer, Item.OldItem, Item.Accum_Lt, 
        Item.ReviewTime, Item.OrderPt, Item.OrderQty, 
        Item.SafetyStock, Item.FcstRT, Item.FcstLT, Item.Fcst_Month, 
        Item.Fcst_Quarter, Item.Fcst_Year, Item.FcstDate, Item.VC_Amt, 
        Item.VC_Units_Ranking, Item.VC_Amt_Ranking, Item.VC_Date, 
        Item.VelCode_Prev, Item.VC_Amt_Prev, AIMOptions.OpDesc, 
      	AIMOptions.IDFL, AIMOptions.BypIDFL, AIMOptions.CADL, 
       	AIMOptions.HiDFL, AIMOptions.LoDFL, AIMOptions.NDFL, 
      	AIMOptions.DDMAP, AIMOptions.Damp, AIMOptions.MinSmk, 
      	AIMOptions.MaxSmk, AIMOptions.IntSmk, AIMOptions.TSL, 
      	AIMOptions.TSSmK, AIMOptions.BypDDU, 
      	AIMOptions.ApplyDmdFilter, AIMOptions.BypZSl, 
      	AIMOptions.BypTFP, AIMOptions.BypZOH, AIMOptions.BypBef, 
       	AIMOptions.BypAft, AIMOptions.MinBI, AIMOptions.HiPct, 
       	AIMOptions.LoPct, AIMOptions.HiMADP, AIMOptions.LoMADP, 
       	AIMOptions.MADSmk, AIMOptions.MADExk, AIMOptions.TrnSmk, 
      	AIMOptions.DIDDL, AIMOptions.DITrnDL, AIMOptions.DITrps, 
      	AIMOptions.DIMADP, AIMOptions.HiTrndL, 
      	AIMOptions.FilterSmk, AIMOptions.LumpyFilterPct, 
       	AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.Dft_DSer,
       	AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, 
       	AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, 
       	AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, 
       	AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, 
       	AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, 
       	AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, 
       	AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, 
       	AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, 
       	AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, 
       	AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, 
       	AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, 
       	AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, 
       	AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, 
       	AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42, 
       	AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, 
       	AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, 
      	AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, 
      	AIMSeasons.BI52, AIMVendors.Dft_Byid, 
      	AIMVendors.RevCycle, AIMVendors.Dft_LeadTime,
       	RevCycles_ReviewTime = ISNULL(RevCycles.ReviewTime,0),
      	#tempitemhistory.HisYear, #tempItemHistory.CPS01, 
      	#tempItemHistory.CPS02, #tempItemHistory.CPS03, #tempItemHistory.CPS04, 
       	#tempItemHistory.CPS05, #tempItemHistory.CPS06, #tempItemHistory.CPS07, 
      	#tempItemHistory.CPS08, #tempItemHistory.CPS09, #tempItemHistory.CPS10, 
       	#tempItemHistory.CPS11, #tempItemHistory.CPS12, #tempItemHistory.CPS13, 
       	#tempItemHistory.CPS14, #tempItemHistory.CPS15, #tempItemHistory.CPS16, 
       	#tempItemHistory.CPS17, #tempItemHistory.CPS18, #tempItemHistory.CPS19, 
       	#tempItemHistory.CPS20, #tempItemHistory.CPS21, #tempItemHistory.CPS22, 
       	#tempItemHistory.CPS23, #tempItemHistory.CPS24, #tempItemHistory.CPS25, 
       	#tempItemHistory.CPS26, #tempItemHistory.CPS27, #tempItemHistory.CPS28, 
       	#tempItemHistory.CPS29, #tempItemHistory.CPS30, #tempItemHistory.CPS31, 
       	#tempItemHistory.CPS32, #tempItemHistory.CPS33, #tempItemHistory.CPS34, 
       	#tempItemHistory.CPS35, #tempItemHistory.CPS36, #tempItemHistory.CPS37, 
       	#tempItemHistory.CPS38, #tempItemHistory.CPS39, #tempItemHistory.CPS40, 
      	#tempItemHistory.CPS41, #tempItemHistory.CPS42, #tempItemHistory.CPS43, 
       	#tempItemHistory.CPS44, #tempItemHistory.CPS45, #tempItemHistory.CPS46, 
      	#tempItemHistory.CPS47, #tempItemHistory.CPS48, #tempItemHistory.CPS49, 
       	#tempItemHistory.CPS50, #tempItemHistory.CPS51, #tempItemHistory.CPS52
       	FROM Item with (nolock)
       	INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
        INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId
        AND AIMSeasons.SAVersion = @SAVersion 
      	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
       	INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId
        AND Item.Assort = AIMVendors.Assort
       	LEFT OUTER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
       	LEFT OUTER JOIN #TempItemHistory ON Item.Lcid = #TempItemHistory.Lcid
        AND Item.Item = #TempItemHistory.Item 
      	WHERE Item.Lcid = @SelLcid
       	AND ItStatus.DmdUpd = 'Y'
      	AND ((#TempItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
        OR #TempItemHistory.HisYear IS NULL)
      	ORDER BY Item.Lcid, Item.Item, #TempItemHistory.HisYear desc
END
    
IF @SelItem = '' and @ForceUpdateFlag = 'N' 
BEGIN	
	
	INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	FROM itemhistory
	WHERE ItemHistory.Lcid = @SelLcid
 --           	AND ItStatus.DmdUpd = 'Y'
  --        	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
  --            OR ItemHistory.HisYear IS NULL)
  --          	AND item.fcstupdcyc < @CurFcstUpdCyc
            	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc

       	SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
       	Item.OptionID, Item.Class1, Item.Class2, Item.Class3, 
       	Item.Class4, Item.BinLocation, Item.BuyStrat, Item.VelCode, 
      	Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag,
      	Item.SaId, Item.PmId, Item.UPC, Item.Weight, Item.Cube, 
      	Item.Price, Item.Cost, Item.BkQty01, Item.BkCost01, 
      	Item.BkQty02, Item.BkCost02, Item.BkQty03, Item.BkCost03, 
      	Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, 
        Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, 
    	Item.BkQty08, Item.BkCost08, Item.BkQty09, Item.BkCost09, 
    	Item.BkQty10, Item.BkCost10, Item.UOM, Item.ConvFactor, 
    	Item.BuyingUOM, Item.ReplenCost2, Item.Oh, Item.Oo, Item.ComStk, 
    	Item.BkOrder, Item.BkComStk, Item.LeadTime, 
    	Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, 
    	Item.SSAdj, Item.UserMin, Item.UserMax, Item.UserMethod, 
    	Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
    	Item.UserFcstExpDate, Item.MAE, Item.MSE, Item.Trend, 
    	Item.FcstCycles, Item.ZeroCount, Item.DIFlag, 
    	Item.DmdFilterFlag, Item.TrkSignalFlag, Item.UserDemandFlag, 
    	Item.ByPassPct, Item.FcstUpdCyc, Item.LTVFact, Item.PlnTT, 
    	Item.ZOPSw, Item.OUTLSw, Item.ZSStock, Item.DSer, 
    	Item.Freeze_BuyStrat, Item.Freeze_Byid, 
    	Item.Freeze_LeadTime, Item.Freeze_OptionID, 
    	Item.Freeze_DSer, Item.OldItem, Item.Accum_Lt, 
    	Item.ReviewTime, Item.OrderPt, Item.OrderQty, 
    	Item.SafetyStock, Item.FcstRT, Item.FcstLT, Item.Fcst_Month, 
    	Item.Fcst_Quarter, Item.Fcst_Year, Item.FcstDate, Item.VC_Amt, 
    	Item.VC_Units_Ranking, Item.VC_Amt_Ranking, Item.VC_Date, 
    	Item.VelCode_Prev, Item.VC_Amt_Prev, AIMOptions.OpDesc, 
    	AIMOptions.IDFL, AIMOptions.BypIDFL, AIMOptions.CADL, 
    	AIMOptions.HiDFL, AIMOptions.LoDFL, AIMOptions.NDFL, 
    	AIMOptions.DDMAP, AIMOptions.Damp, AIMOptions.MinSmk, 
    	AIMOptions.MaxSmk, AIMOptions.IntSmk, AIMOptions.TSL, 
    	AIMOptions.TSSmK, AIMOptions.BypDDU, 
    	AIMOptions.ApplyDmdFilter, AIMOptions.BypZSl, 
    	AIMOptions.BypTFP, AIMOptions.BypZOH, AIMOptions.BypBef, 
    	AIMOptions.BypAft, AIMOptions.MinBI, AIMOptions.HiPct, 
    	AIMOptions.LoPct, AIMOptions.HiMADP, AIMOptions.LoMADP, 
    	AIMOptions.MADSmk, AIMOptions.MADExk, AIMOptions.TrnSmk, 
    	AIMOptions.DIDDL, AIMOptions.DITrnDL, AIMOptions.DITrps, 
    	AIMOptions.DIMADP, AIMOptions.HiTrndL, 
    	AIMOptions.FilterSmk, AIMOptions.LumpyFilterPct, 
    	AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.Dft_DSer,
    	AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, 
    	AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, 
    	AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, 
    	AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, 
    	AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, 
    	AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, 
    	AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, 
    	AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, 
    	AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, 
    	AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, 
    	AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, 
    	AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, 
    	AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, 
    	AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42,             	
	AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, 
    	AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, 
    	AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, 
    	AIMSeasons.BI52, AIMVendors.Dft_Byid, 
    	AIMVendors.RevCycle, AIMVendors.Dft_LeadTime,
    	RevCycles_ReviewTime = ISNULL(RevCycles.ReviewTime,0),
    	#tempitemhistory.HisYear, #tempItemHistory.CPS01, 
      	#tempItemHistory.CPS02, #tempItemHistory.CPS03, #tempItemHistory.CPS04, 
       	#tempItemHistory.CPS05, #tempItemHistory.CPS06, #tempItemHistory.CPS07, 
      	#tempItemHistory.CPS08, #tempItemHistory.CPS09, #tempItemHistory.CPS10, 
       	#tempItemHistory.CPS11, #tempItemHistory.CPS12, #tempItemHistory.CPS13, 
       	#tempItemHistory.CPS14, #tempItemHistory.CPS15, #tempItemHistory.CPS16, 
       	#tempItemHistory.CPS17, #tempItemHistory.CPS18, #tempItemHistory.CPS19, 
       	#tempItemHistory.CPS20, #tempItemHistory.CPS21, #tempItemHistory.CPS22, 
       	#tempItemHistory.CPS23, #tempItemHistory.CPS24, #tempItemHistory.CPS25, 
       	#tempItemHistory.CPS26, #tempItemHistory.CPS27, #tempItemHistory.CPS28, 
       	#tempItemHistory.CPS29, #tempItemHistory.CPS30, #tempItemHistory.CPS31, 
       	#tempItemHistory.CPS32, #tempItemHistory.CPS33, #tempItemHistory.CPS34, 
       	#tempItemHistory.CPS35, #tempItemHistory.CPS36, #tempItemHistory.CPS37, 
       	#tempItemHistory.CPS38, #tempItemHistory.CPS39, #tempItemHistory.CPS40, 
      	#tempItemHistory.CPS41, #tempItemHistory.CPS42, #tempItemHistory.CPS43, 
       	#tempItemHistory.CPS44, #tempItemHistory.CPS45, #tempItemHistory.CPS46, 
      	#tempItemHistory.CPS47, #tempItemHistory.CPS48, #tempItemHistory.CPS49, 
       	#tempItemHistory.CPS50, #tempItemHistory.CPS51, #tempItemHistory.CPS52
    	FROM Item with (nolock)
   	INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
   	INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId 
   	AND AIMSeasons.SAVersion = @SAVersion 
   	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
   	INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId
   	AND Item.Assort = AIMVendors.Assort
    	LEFT OUTER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
    	LEFT OUTER JOIN #TempItemHistory ON Item.Lcid = #TempItemHistory.Lcid
        AND Item.Item = #TempItemHistory.Item 
    	WHERE Item.Lcid = @SelLcid
     	AND ItStatus.DmdUpd = 'Y'
--        	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
--            OR ItemHistory.HisYear IS NULL)
    	AND item.fcstupdcyc < @CurFcstUpdCyc
    	ORDER BY Item.Lcid, Item.Item, #TempItemHistory.HisYear desc
  	END

  	IF @SelItem <> '' and @ForceUpdateFlag = 'Y' 
  	BEGIN

	INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	FROM itemhistory
	WHERE	ItemHistory.Lcid = @SelLcid
            	AND ItemHistory.Item = @SelItem
  --          	AND ItStatus.DmdUpd = 'Y'
  --        	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
  --        	OR ItemHistory.HisYear IS NULL)
            	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc



	SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
    	Item.OptionID, Item.Class1, Item.Class2, Item.Class3, 
    	Item.Class4, Item.BinLocation, Item.BuyStrat, Item.VelCode, 
    	Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag,
    	Item.SaId, Item.PmId, Item.UPC, Item.Weight, Item.Cube, 
    	Item.Price, Item.Cost, Item.BkQty01, Item.BkCost01, 
    	Item.BkQty02, Item.BkCost02, Item.BkQty03, Item.BkCost03, 
    	Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, 
    	Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, 
    	Item.BkQty08, Item.BkCost08, Item.BkQty09, Item.BkCost09, 
    	Item.BkQty10, Item.BkCost10, Item.UOM, Item.ConvFactor, 
    	Item.BuyingUOM, Item.ReplenCost2, Item.Oh, Item.Oo, Item.ComStk, 
    	Item.BkOrder, Item.BkComStk, Item.LeadTime, 
    	Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, 
    	Item.SSAdj, Item.UserMin, Item.UserMax, Item.UserMethod, 
    	Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
    	Item.UserFcstExpDate, Item.MAE, Item.MSE, Item.Trend, 
    	Item.FcstCycles, Item.ZeroCount, Item.DIFlag, 
    	Item.DmdFilterFlag, Item.TrkSignalFlag, Item.UserDemandFlag, 
    	Item.ByPassPct, Item.FcstUpdCyc, Item.LTVFact, Item.PlnTT, 
    	Item.ZOPSw, Item.OUTLSw, Item.ZSStock, Item.DSer, 
    	Item.Freeze_BuyStrat, Item.Freeze_Byid,  
    	Item.Freeze_LeadTime, Item.Freeze_OptionID, 
    	Item.Freeze_DSer, Item.OldItem, Item.Accum_Lt, 
    	Item.ReviewTime, Item.OrderPt, Item.OrderQty, 
    	Item.SafetyStock, Item.FcstRT, Item.FcstLT, Item.Fcst_Month, 
    	Item.Fcst_Quarter, Item.Fcst_Year, Item.FcstDate, Item.VC_Amt, 
    	Item.VC_Units_Ranking, Item.VC_Amt_Ranking, Item.VC_Date, 
    	Item.VelCode_Prev, Item.VC_Amt_Prev, AIMOptions.OpDesc, 
    	AIMOptions.IDFL, AIMOptions.BypIDFL, AIMOptions.CADL, 
    	AIMOptions.HiDFL, AIMOptions.LoDFL, AIMOptions.NDFL, 
    	AIMOptions.DDMAP, AIMOptions.Damp, AIMOptions.MinSmk, 
    	AIMOptions.MaxSmk, AIMOptions.IntSmk, AIMOptions.TSL, 
    	AIMOptions.TSSmK, AIMOptions.BypDDU, 
    	AIMOptions.ApplyDmdFilter, AIMOptions.BypZSl, 
    	AIMOptions.BypTFP, AIMOptions.BypZOH, AIMOptions.BypBef, 
    	AIMOptions.BypAft, AIMOptions.MinBI, AIMOptions.HiPct, 
    	AIMOptions.LoPct, AIMOptions.HiMADP, AIMOptions.LoMADP, 
    	AIMOptions.MADSmk, AIMOptions.MADExk, AIMOptions.TrnSmk, 
    	AIMOptions.DIDDL, AIMOptions.DITrnDL, AIMOptions.DITrps, 
    	AIMOptions.DIMADP, AIMOptions.HiTrndL, 
   	AIMOptions.FilterSmk, AIMOptions.LumpyFilterPct, 
    	AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.Dft_DSer,
    	AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, 
    	AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, 
    	AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, 
    	AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, 
    	AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, 
    	AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, 
    	AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, 
    	AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, 
    	AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, 
    	AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, 
    	AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, 
    	AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, 
    	AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, 
    	AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42, 
    	AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, 
    	AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, 
    	AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, 
    	AIMSeasons.BI52, AIMVendors.Dft_Byid, 
    	AIMVendors.RevCycle, AIMVendors.Dft_LeadTime,
    	RevCycles_ReviewTime = ISNULL(RevCycles.ReviewTime,0),
        #tempitemhistory.HisYear, #tempItemHistory.CPS01, 
      	#tempItemHistory.CPS02, #tempItemHistory.CPS03, #tempItemHistory.CPS04, 
       	#tempItemHistory.CPS05, #tempItemHistory.CPS06, #tempItemHistory.CPS07, 
      	#tempItemHistory.CPS08, #tempItemHistory.CPS09, #tempItemHistory.CPS10, 
       	#tempItemHistory.CPS11, #tempItemHistory.CPS12, #tempItemHistory.CPS13, 
       	#tempItemHistory.CPS14, #tempItemHistory.CPS15, #tempItemHistory.CPS16, 
       	#tempItemHistory.CPS17, #tempItemHistory.CPS18, #tempItemHistory.CPS19, 
       	#tempItemHistory.CPS20, #tempItemHistory.CPS21, #tempItemHistory.CPS22, 
       	#tempItemHistory.CPS23, #tempItemHistory.CPS24, #tempItemHistory.CPS25, 
       	#tempItemHistory.CPS26, #tempItemHistory.CPS27, #tempItemHistory.CPS28, 
       	#tempItemHistory.CPS29, #tempItemHistory.CPS30, #tempItemHistory.CPS31, 
       	#tempItemHistory.CPS32, #tempItemHistory.CPS33, #tempItemHistory.CPS34, 
       	#tempItemHistory.CPS35, #tempItemHistory.CPS36, #tempItemHistory.CPS37, 
       	#tempItemHistory.CPS38, #tempItemHistory.CPS39, #tempItemHistory.CPS40, 
      	#tempItemHistory.CPS41, #tempItemHistory.CPS42, #tempItemHistory.CPS43, 
       	#tempItemHistory.CPS44, #tempItemHistory.CPS45, #tempItemHistory.CPS46, 
      	#tempItemHistory.CPS47, #tempItemHistory.CPS48, #tempItemHistory.CPS49, 
       	#tempItemHistory.CPS50, #tempItemHistory.CPS51, #tempItemHistory.CPS52
    	FROM Item with (nolock)
   	INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
    	INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId 
    	AND AIMSeasons.SAVersion = @SAVersion 
    	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
    	INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId
    	AND Item.Assort = AIMVendors.Assort
    	LEFT OUTER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
    	LEFT OUTER JOIN #TempItemHistory ON Item.Lcid = #TempItemHistory.Lcid
    	AND Item.Item = #TempItemHistory.Item 
    	WHERE Item.Lcid = @SelLcid
    	AND Item.Item = @SelItem
    	AND ItStatus.DmdUpd = 'Y'
--        	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
--        	OR ItemHistory.HisYear IS NULL)
    	ORDER BY Item.Lcid, Item.Item, #TempItemHistory.HisYear desc
END
  
IF @SelItem <> '' and @ForceUpdateFlag = 'N' 
BEGIN

		
	INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	FROM itemhistory
	WHERE ItemHistory.Lcid = @SelLcid
    	AND ItemHistory.Item = @SelItem
--    	AND ItStatus.DmdUpd = 'Y'
--        	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
--            OR ItemHistory.HisYear IS NULL)
--    	AND item.fcstupdcyc < @CurFcstUpdCyc
    	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc




	SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
    	Item.OptionID, Item.Class1, Item.Class2, Item.Class3, 
    	Item.Class4, Item.BinLocation, Item.BuyStrat, Item.VelCode, 
    	Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag,
    	Item.SaId, Item.PmId, Item.UPC, Item.Weight, Item.Cube, 
    	Item.Price, Item.Cost, Item.BkQty01, Item.BkCost01, 
    	Item.BkQty02, Item.BkCost02, Item.BkQty03, Item.BkCost03, 
    	Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, 
    	Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, 
    	Item.BkQty08, Item.BkCost08, Item.BkQty09, Item.BkCost09, 
    	Item.BkQty10, Item.BkCost10, Item.UOM, Item.ConvFactor, 
    	Item.BuyingUOM, Item.ReplenCost2, Item.Oh, Item.Oo, Item.ComStk, 
    	Item.BkOrder, Item.BkComStk, Item.LeadTime, 
    	Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, 
    	Item.SSAdj, Item.UserMin, Item.UserMax, Item.UserMethod, 
    	Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
    	Item.UserFcstExpDate, Item.MAE, Item.MSE, Item.Trend, 
    	Item.FcstCycles, Item.ZeroCount, Item.DIFlag, 
    	Item.DmdFilterFlag, Item.TrkSignalFlag, Item.UserDemandFlag, 
    	Item.ByPassPct, Item.FcstUpdCyc, Item.LTVFact, Item.PlnTT, 
    	Item.ZOPSw, Item.OUTLSw, Item.ZSStock, Item.DSer, 
    	Item.Freeze_BuyStrat, Item.Freeze_Byid, 
    	Item.Freeze_LeadTime, Item.Freeze_OptionID, 
    	Item.Freeze_DSer, Item.OldItem, Item.Accum_Lt, 
    	Item.ReviewTime, Item.OrderPt, Item.OrderQty, 
    	Item.SafetyStock, Item.FcstRT, Item.FcstLT, Item.Fcst_Month, 
    	Item.Fcst_Quarter, Item.Fcst_Year, Item.FcstDate, Item.VC_Amt, 
    	Item.VC_Units_Ranking, Item.VC_Amt_Ranking, Item.VC_Date, 
    	Item.VelCode_Prev, Item.VC_Amt_Prev, AIMOptions.OpDesc, 
    	AIMOptions.IDFL, AIMOptions.BypIDFL, AIMOptions.CADL, 
    	AIMOptions.HiDFL, AIMOptions.LoDFL, AIMOptions.NDFL, 
    	AIMOptions.DDMAP, AIMOptions.Damp, AIMOptions.MinSmk, 
    	AIMOptions.MaxSmk, AIMOptions.IntSmk, AIMOptions.TSL, 
    	AIMOptions.TSSmK, AIMOptions.BypDDU, 
    	AIMOptions.ApplyDmdFilter, AIMOptions.BypZSl, 
    	AIMOptions.BypTFP, AIMOptions.BypZOH, AIMOptions.BypBef, 
    	AIMOptions.BypAft, AIMOptions.MinBI, AIMOptions.HiPct, 
    	AIMOptions.LoPct, AIMOptions.HiMADP, AIMOptions.LoMADP, 
    	AIMOptions.MADSmk, AIMOptions.MADExk, AIMOptions.TrnSmk, 
    	AIMOptions.DIDDL, AIMOptions.DITrnDL, AIMOptions.DITrps, 
    	AIMOptions.DIMADP, AIMOptions.HiTrndL, 
    	AIMOptions.FilterSmk, AIMOptions.LumpyFilterPct, 
    	AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.Dft_DSer,
    	AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, 
    	AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, 
    	AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, 
    	AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, 
    	AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, 
    	AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, 
    	AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, 
    	AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, 
    	AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, 
    	AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, 
    	AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, 
    	AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, 
    	AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, 
    	AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42, 
    	AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, 
    	AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, 
    	AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, 
    	AIMSeasons.BI52, AIMVendors.Dft_Byid, 
    	AIMVendors.RevCycle, AIMVendors.Dft_LeadTime,
    	RevCycles_ReviewTime = ISNULL(RevCycles.ReviewTime,0),
    	#tempitemhistory.HisYear, #tempItemHistory.CPS01, 
      	#tempItemHistory.CPS02, #tempItemHistory.CPS03, #tempItemHistory.CPS04, 
       	#tempItemHistory.CPS05, #tempItemHistory.CPS06, #tempItemHistory.CPS07, 
      	#tempItemHistory.CPS08, #tempItemHistory.CPS09, #tempItemHistory.CPS10, 
       	#tempItemHistory.CPS11, #tempItemHistory.CPS12, #tempItemHistory.CPS13, 
       	#tempItemHistory.CPS14, #tempItemHistory.CPS15, #tempItemHistory.CPS16, 
       	#tempItemHistory.CPS17, #tempItemHistory.CPS18, #tempItemHistory.CPS19, 
       	#tempItemHistory.CPS20, #tempItemHistory.CPS21, #tempItemHistory.CPS22, 
       	#tempItemHistory.CPS23, #tempItemHistory.CPS24, #tempItemHistory.CPS25, 
       	#tempItemHistory.CPS26, #tempItemHistory.CPS27, #tempItemHistory.CPS28, 
       	#tempItemHistory.CPS29, #tempItemHistory.CPS30, #tempItemHistory.CPS31, 
       	#tempItemHistory.CPS32, #tempItemHistory.CPS33, #tempItemHistory.CPS34, 
       	#tempItemHistory.CPS35, #tempItemHistory.CPS36, #tempItemHistory.CPS37, 
       	#tempItemHistory.CPS38, #tempItemHistory.CPS39, #tempItemHistory.CPS40, 
      	#tempItemHistory.CPS41, #tempItemHistory.CPS42, #tempItemHistory.CPS43, 
       	#tempItemHistory.CPS44, #tempItemHistory.CPS45, #tempItemHistory.CPS46, 
      	#tempItemHistory.CPS47, #tempItemHistory.CPS48, #tempItemHistory.CPS49, 
       	#tempItemHistory.CPS50, #tempItemHistory.CPS51, #tempItemHistory.CPS52
    	FROM Item with (nolock)
    	INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
    	INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId 
    	AND AIMSeasons.SAVersion = @SAVersion 
    	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
    	INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId
    	AND Item.Assort = AIMVendors.Assort
    	LEFT OUTER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
    	LEFT OUTER JOIN #TempItemHistory ON Item.Lcid = #TempItemHistory.Lcid
    	AND Item.Item = #TempItemHistory.Item 
    	WHERE Item.Lcid = @SelLcid
    	AND Item.Item = @SelItem
    	AND ItStatus.DmdUpd = 'Y'
--        	AND ((ItemHistory.HisYear BETWEEN @StartYear and @FcstYear)
--            OR ItemHistory.HisYear IS NULL)
    	AND item.fcstupdcyc < @CurFcstUpdCyc
    	ORDER BY Item.Lcid, Item.Item, TempItemHistory.HisYear desc
END
  

-- Return the number of rows processed
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

