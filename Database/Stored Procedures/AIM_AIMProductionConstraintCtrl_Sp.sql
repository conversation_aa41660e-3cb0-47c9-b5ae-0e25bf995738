SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMProductionConstraintCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMProductionConstraintCtrl_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMProductionConstraintCtrl_Sp
**	Desc: Gets the VnId and Assortments to be processed
**		The argument RevCylce means default is ALL
**		IF @FilterOpt = R, then Review Cycle
**		IF @FilterOpt = A or V, then 'ALL'
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Recordset - LcID, LName
**              
**	Auth:   Srinivas Uddanti
**	Date:   10/15/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**	---------- ------------ -----------------------------------------------
**	11/01/2002 Srinivas Udd Removed Vendor Best from selection criteria.
*******************************************************************************/

CREATE PROCEDURE AIM_AIMProductionConstraintCtrl_Sp
(
	@RevCycle  nvarchar(8) = 'ALL' 
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found     int 

SET NOCOUNT ON

BEGIN
   IF @RevCycle = 'ALL'
   BEGIN
        SELECT DISTINCT PODetail.VnId, PODetail.Assort, AIMVendors.Reach_Code, 
            AIMVendors.Vn_Min, AIMVendors.Vn_Best
            FROM PODetail 
            INNER JOIN AIMVendors ON PODetail.VnId = AIMVendors.VnId AND PODetail.Assort = AIMVendors.Assort 
            WHERE PODetail.POLineType = 20 
            AND PODetail.OrdStatus = 'P'
            AND AIMVendors.Reach_Code <> 'N'
            --AND AIMVendors.Vn_Min + AIMVendors.Vn_Best > 0
    END
    ELSE
    BEGIN
        SELECT DISTINCT PODetail.VnId, PODetail.Assort, AIMVendors.Reach_Code, 
            AIMVendors.Vn_Min, AIMVendors.Vn_Best
            FROM PODetail 
            INNER JOIN AIMVendors ON PODetail.VnId = AIMVendors.VnId AND PODetail.Assort = AIMVendors.Assort 
            WHERE PODetail.RevCycle = @RevCycle
      AND PODetail.POLineType = 20 
            AND PODetail.OrdStatus = 'P'
            AND AIMVendors.Reach_Code <> 'N'
            --AND AIMVendors.Vn_Min + AIMVendors.Vn_Best > 0
    END

    SELECT @found = @@rowcount

    -- Check for SQL Server errors.
    IF @@ERROR <> 0 
    BEGIN
        RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    END

    IF @found <= 0 
    BEGIN 
        RETURN -1 -- ERROR (No Valid data found in Database)
    END
    ELSE
    BEGIN
        RETURN @found
    END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

