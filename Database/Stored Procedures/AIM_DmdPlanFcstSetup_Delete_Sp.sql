if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstSetup_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstSetup_Delete_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


/******************************************************************************
**	Name: AIM_DmdPlanFcstSetup_Delete_Sp
**	Desc: Deletes AIMFcst and all dependent tables.
**
**	Returns: 1)@Return_Stat - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Annalaksh<PERSON> Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstSetup_Delete_Sp
(
	@FcstID as nvarchar(12),
	@UserID as nvarchar(12) = ''
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @AccessCode as int
	DECLARE @FcstSetupKey as int
	DECLARE @FcstLocked as bit
	DECLARE @Return_Stat as int

	-- Validate the required parameters.
	IF @FcstID Is Null
	OR @UserID Is Null
	BEGIN
	  	RETURN -1
	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	
	SELECT 
		@FcstSetupKey = FcstSetupKey,
		@FcstLocked = FcstLocked 
	FROM AIMFcstSetup
	WHERE FcstID = @FcstID

-- 	IF @AccessCode >= 2 
-- 	AND @FcstLocked = 'N'
	IF @FcstLocked = 0
	BEGIN
		BEGIN TRAN --Start of Transaction
		
		DELETE FROM AIMFcstAccess
		WHERE FcstSetupKey = @FcstSetupKey
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
-- 		DELETE FROM AIMFcstFreeze
-- 		WHERE FcstSetupKey = @FcstSetupKey
-- 		-- Check for SQL Server errors.
-- 		IF @@ERROR <> 0 
-- 	  	BEGIN
-- 			ROLLBACK TRAN
-- 	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
-- 	  	END
	
	 	DELETE FROM AIMFcstSetup
		WHERE FcstSetupKey = @FcstSetupKey
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		COMMIT TRAN  -- End of Transaction
	
		RETURN 1
	END
	ELSE
	BEGIN
	 	RETURN -3 -- Not Permitted Premissions
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

