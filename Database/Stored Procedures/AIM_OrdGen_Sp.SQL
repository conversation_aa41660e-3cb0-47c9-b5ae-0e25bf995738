SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_OrdGen_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_OrdGen_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_OrdGen_Sp
**	Desc: 
**		@VnId			        -- Vendor Id
**		@Assort				-- Assortment
**		@LT_Flag			-- Process Lead Time Exceptions (Y,N)
**		@PE_Flag        		-- Process Priority Exceptions (Y,N)
**		@RC_Flag        		-- Review Cycle (Y,N)
**		@DR_Flag        		-- Dynamic Review (Y,N)
**		@ReviewCount			-- Count of items reviewed
**		@LT_Count		OUTPUT	-- Count of lead time exceptions
**		@RC_Count		OUTPUT	-- Count of Review Cycles
**		@PE_Count		OUTPUT	-- Count of priority exceptions
**		@OutPutOpt				-- Output Option
**
**	Returns: 
**		1	-- Succeed
**	Values:  
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	 Description:
**	---------- ------------- --------------------------------------
**      2003/03/01 Mohammed 	 Add the Assortment to filter the Items 
**    				 if the Vendor/Assort is MDC
**	2003/06/05 A.Stocksdale	 Commented out the above MDC-specific changes 
**                               for v4.4
** 	2003/08/06 K.Hill	 Fixed for Wanlong -MDC Orders cannot be 
**                               type ='T' and allow the OrderQty to be zero.
**	2003/08/06 S.Uddanti	 Changed VSOQAmt field from 10,4 to 10.2
**      2003/10/01 S.Uddanti	 If AvailQty <0 then set to 0  
**	2003/12/04 S.Uddanti	 DropShip RSOQ calculations changed
**	2003/12/05 S.Uddanti	 Modified code so RSOQ =0 when AvailQty 
**                               is >OrderPt
**	2003/04/19 S.Uddanti	 Increased the size of ItDesc field from 30 to 50
**	2005/04/03 S.Uddanti	 Add Allow_Negative_AvailQty to set AvilQty to zero
**				 or not based on the flag
**				 Modified code for inactive item  changed the 
**				 @isdate >= @inactdate statement
**				 For time supplied method  added a check to inclued
**				@usermax = 999999999 SELECT @orderqty = @orderqty
**	2005/04/22 S.Uddanti	call storedprocedure to set VendorCostExcpt flag
**	2005/05/11 S.Uddanti	Increased the size of maxordqty
**				Added code if the inactdate is '12-31-9999' and 
**				itstat ='D' then rsoq=0 for tbc
*******************************************************************************/ 
CREATE PROCEDURE AIM_OrdGen_Sp 
(	@VnId			nvarchar(12),
	@Assort			nvarchar(12),
	@LT_Flag		nvarchar(1) = 'N',
	@PE_Flag		nvarchar(1) = 'N',
	@RC_Flag		nvarchar(1) = 'N',
	@DR_Flag		nvarchar(1) = 'N',
	@ReviewCount	        int 		OUTPUT,
	@LT_Count		int 		OUTPUT,
	@RC_Count		int 		OUTPUT,
	@PE_Count		int 		OUTPUT,
	@OutPutOpt		nvarchar(1) = 'N'
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

-- Declare working variable
DECLARE	

	-- AIMUsers
	@UserInitials		nvarchar(3),		-- Buyer's Initials
	
	-- PO Header Record
	@LineCount			int,			-- Total Purchase Order Lines
	@PosLineCount		int,			-- Total Lines with Positive VSOQ
	@POSource			nvarchar(1),		-- Purchase Order Source (X=Exception, S=Scheduled)
	@TotalCost			decimal(10,2),		-- Total Cost of Suggested Order Lines
	
	-- PO Detail Record
	@POLineType			smallint,		-- Purchase Order Line Type
	@ordstatus			nvarchar(1),		-- Order Status
	@postatus			nvarchar(1),		-- Purchase Order Status
	@POType				nvarchar(1),		-- Purchase Order Type (P=PO, T=Transfer FROM MDC)
	@availqty			int,			-- Available Inventory
	@RSOQ				int,			-- Raw Suggested Order Quantity
	@SOQ				int,			-- Suggested Order Quantity
	@VSOQ				int,			-- Vendor Sized Suggested Order Quantity
	@dudate				datetime,		-- Due Date
	@isdate				datetime,		-- Issue Date

	-- Stored Procedure Variables
	@BuyGoal			decimal(10,4),		-- Buying Goal
	@MaxDueDate			datetime,		-- Maximum Due Date
	@MaxOrdQty			decimal(15,2),		-- Maximum Order Quantity for Discontinued Item
	@RtnCode			int,			-- Return code
	@VSOQAmt			decimal(10,2),		-- VSOQ Amount -- Used in Order Sizing

	-- Item Table
	@Lcid				nvarchar(12), 
	@Item				nvarchar(25), 
	@ItDesc				nvarchar(50),
	@ById				nvarchar(12), 
	@Oh					int, 
	@Oo					int, 
	@ComStk				int, 
	@BkOrder			int, 
	@BkComStk			int, 
	@Accum_Lt			smallint, 
	@FcstLT				decimal(10,2), 
	@FcstRT				decimal(10,2), 
	@UserMin			int, 
	@UserMax			int, 
	@ItStat				nvarchar(1), 
	@InActDate			datetime, 
	@ZOPSw				nvarchar(1), 
	@OrderPt			int, 
	@OrderQty			int, 
	@OUTLSw				nvarchar(1), 
	@BuyStrat			nvarchar(1), 
	@UOM				nvarchar(6), 
	@ConvFactor			int, 
	@BuyingUOM			nvarchar(6), 
	@PackRounding			nvarchar(1), 
	@IMin				int, 
	@IMax				int, 
	@CStock				int, 
	@MDC				nvarchar(12),
	@MDCFlag			nvarchar(1),
	@VelCode			nvarchar(1),
	@Dser				decimal(4,3),
	@SafetyStock			decimal(10,2),
	@LeadTime			smallint,
	@FcstUpdCyc			int,
	@Zone				nvarchar(1),
	@Weight				decimal(10,4),
	@Cube				decimal(10,4),
	@Cost				decimal(10,4),
	@Freeze_ById			nvarchar(1),

	-- Location Table
	@Lc_Dft_ById			nvarchar(12),
	@StkDate			datetime,
	@DropShip_XDock			nvarchar(1),

	-- Vendor Table
	@Dft_Byid			nvarchar(12), 
	@RevCycle			nvarchar(8), 
	@Dft_LeadTime			smallint, 
	@Vn_Min				decimal(9,2), 
	@Vn_Best			decimal(9,2), 
	@Reach_Code			nvarchar(1), 
	@POByZone			nvarchar(1),
	@LastWeekSalesFlag  		nvarchar(1),
	@TransmitPO			tinyint,
	@ShipIns			nvarchar(20),
	@VName				nvarchar(30),
	@VAddress1			nvarchar(30),
	@VAddress2			nvarchar(30),
	@VCity				nvarchar(20),
	@VState				nvarchar(10),
	@VZip				nvarchar(10),
	@VnMDCFlag			nvarchar(1),

	-- System Control Table
	@ByIdSource			nvarchar(1),
	@Dft_POStatus			nvarchar(1),
	@Priority_Min_Velcode		nvarchar(1),
	@Priority_Min_Dser		decimal(3,2),
	@Vendor_Sizing			nvarchar(1),
	@Allow_Negative_AvailQty	nvarchar(1),

	-- Review Cycle Table
	@NextDateTime			datetime,
	@OrdGenStatus			nvarchar(1)
	
	-- Set runtime options
	SET NOCOUNT ON
	
	-- Start the TRANSACTION cycle
	BEGIN TRANSACTION
	
	-- Check for invalid review flags
	IF @LT_Flag = 'N' 
	AND @PE_Flag = 'N' 
	AND @RC_Flag = 'N' 
	AND @DR_Flag = 'N'
		RETURN

	-- Get the review date
	SELECT @isdate = getdate(), @dudate = getdate()

	-- Get Rid of the Time Portion of the Date
	EXEC AIM_GetDate_Sp @isdate OUTPUT
	EXEC AIM_GetDate_Sp @dudate OUTPUT
	
	-- Get the System Control record
	SELECT @ByIdSource = ByIdSource,
		@Priority_Min_Velcode = priority_min_velcode,
		@Priority_Min_Dser = priority_min_dser,
		@Dft_PoStatus = dft_postatus,
		@postatus = 'P',
		@Allow_Negative_AvailQty=AllowNegativeAvailQty
	FROM SysCtrl
	
	-- Determine the Purchase Order Source
	-- S = Scheduled
	-- X = Exception
	SELECT @POSource = 
		case 
			WHEN @RC_Flag = 'Y' THEN 'S'
			WHEN @DR_Flag = 'Y' THEN 'S'
			WHEN @LT_Flag = 'Y' THEN 'X'
			WHEN @PE_Flag = 'Y' THEN 'X'
			ELSE 'S'
		END

	-- Get the Vendor Data
	SELECT @Dft_Byid = AIMVendors.Dft_Byid, @RevCycle = AIMVendors.RevCycle, 
		@Dft_LeadTime = AIMVendors.Dft_LeadTime, @Vn_Min= AIMVendors.Vn_Min, 
		@Vn_Best = AIMVendors.Vn_Best, 
		@Reach_Code = AIMVendors.Reach_Code, @POByZone = AIMVendors.POByZone, 
		@LastWeekSalesFlag = AIMVendors.LastWeekSalesFlag,
		@VName = AIMVendors.VName, @VAddress1 = AIMVendors.VAddress1, 
		@VAddress2 = AIMVendors.VAddress2,
		@VCity = AIMVendors.VCity, @VState = AIMVendors.VState, @VZip = AIMVendors.VZip,
		@TransmitPO = AIMVendors.TransmitPO, @ShipIns = AIMVendors.ShipIns,
		@VnMDCFlag = AIMVendors.MDCFlag
	FROM AIMVendors WITH (NOLOCK)
	WHERE AIMVendors.Vnid = @VnId
	AND AIMVendors.Assort = @Assort

	-- Build the Cursor
	IF @VnMDCFlag = 'Y' 
	BEGIN
		DECLARE ordgen INSENSITIVE CURSOR FOR  
		SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ById, 
			Item.Oh, Item.Oo, Item.ComStk, Item.BkOrder, Item.BkComStk, 
			Item.Accum_Lt, Item.FcstLT, Item.FcstRT, Item.UserMin, 
			Item.UserMax, Item.ItStat, Item.InActDate, Item.ZOPSw, 
			Item.OrderPt, Item.OrderQty, Item.OUTLSw, Item.BuyStrat, 
			Item.UOM, Item.ConvFactor, Item.BuyingUOM, Item.PackRounding, 
			Item.IMin, Item.IMax, Item.CStock, Item.MDC, Item.VelCode, 
			Item.DSer, Item.SafetyStock, Item.LeadTime, Item.FcstUpdCyc, 
			Substring(Item.BinLocation, 1, 1), Item.MDCFlag,
			Item.Weight, Item.Cube, Item.Cost, Item.Freeze_ById,
			AIMLocations.Dft_ById, AIMLocations.StkDate, AIMLocations.DropShip_XDock  
			FROM Item WITH (NOLOCK, INDEX = IX_VnidAssort)
	   	INNER JOIN ItStatus WITH (NOLOCK) ON Item.ItStat = ItStatus.ItStat 
	   	INNER JOIN AIMLocations WITH (NOLOCK) ON Item.Lcid = AIMLocations.Lcid 
		WHERE Item.Mdc = @VnId
 		AND Item.Assort = @Assort /*** Mohammed ***/
	   	AND ItStatus.Ordgen = 'Y'
		ORDER BY Item.Lcid,Item.Item
	END
	ELSE
	BEGIN
		DECLARE ordgen INSENSITIVE CURSOR FOR  
		SELECT Item.Lcid, Item.Item, Item.ItDesc, Item.ById, 
			Item.Oh, Item.Oo, Item.ComStk, Item.BkOrder, Item.BkComStk, 
			Item.Accum_Lt, Item.FcstLT, Item.FcstRT, Item.UserMin, 
			Item.UserMax, Item.ItStat, Item.InActDate, Item.ZOPSw, 
			Item.OrderPt, Item.OrderQty, Item.OUTLSw, Item.BuyStrat, 
			Item.UOM, Item.ConvFactor, Item.BuyingUOM, Item.PackRounding, 
			Item.IMin, Item.IMax, Item.CStock, Item.MDC, Item.VelCode, 
			Item.DSer, Item.SafetyStock, Item.LeadTime, Item.FcstUpdCyc, 
			Substring(Item.BinLocation, 1, 1), Item.MDCFlag,
			Item.Weight, Item.Cube, Item.Cost, Item.Freeze_ById,
			AIMLocations.Dft_ById, AIMLocations.StkDate, AIMLocations.DropShip_XDock  
		FROM Item WITH (NOLOCK, INDEX = IX_VnidAssort)
           	INNER JOIN ItStatus WITH (NOLOCK) ON Item.ItStat = ItStatus.ItStat 
           	INNER JOIN AIMLocations WITH (NOLOCK) ON Item.Lcid = AIMLocations.Lcid 
		WHERE Item.Vnid = @VnId
    		AND Item.Assort = @Assort
    		AND NOT (Item.MDC <> '' AND Item.Vnid <> Item.MDC)
           	AND ItStatus.Ordgen = 'Y'
	END
	
	-- Initialize counters
	SELECT @ReviewCount = 0,
		@LT_Count = 0,
		@RC_Count = 0,
		@PE_Count = 0,
		@VSOQAmt = 0, 
		@BuyGoal = 0,
		@VSOQAmt = 0,
		@LineCount = 0,
		@PosLineCount = 0,
		@TotalCost = 0

	-- Open the Order Generation Cursor
	OPEN ordgen
	-- Check for an unpopulated cursor
	IF @@CURSOR_ROWS = 0
	BEGIN
		COMMIT TRANSACTION
		CLOSE ORDGEN
		DEALLOCATE ORDGEN
		RETURN 1
	END

	WHILE 1 = 1
	BEGIN
		-- Get Next Item
		FETCH NEXT FROM ordgen 
		INTO	@Lcid, @Item, @ItDesc, @ById,
			@Oh, @Oo, @ComStk, @BkOrder, @BkComStk, 
			@Accum_Lt, @FcstLT, @FcstRT, @UserMin, 
			@UserMax, @ItStat, @InActDate, @ZOPSw, 
			@OrderPt, @OrderQty, @OUTLSw, @BuyStrat, 
			@UOM, @ConvFactor, @BuyingUOM, @PackRounding, 
			@IMin, @IMax, @CStock, @MDC, @VelCode, @DSer,
			@SafetyStock, @LeadTime, @FcstUpdCyc, @Zone, @MDCFlag,
			@Weight, @Cube, @Cost, @Freeze_ById,
			@Lc_Dft_ById, @StkDate, @DropShip_XDock	

		-- Check for end of processing
		IF @@FETCH_STATUS <> 0 break
       
		-- Increment counters
		SELECT @ReviewCount = @ReviewCount + 1    

		-- Initialize runtime variables
		SELECT 
			@AvailQty = 0,
			@MaxOrdQty = 0,
			@POLineType = 0,
			@RSOQ = 0,
			@SOQ = 0,
			@VSOQ = 0

		-- Determine the buyer id for an item
		-- IF the Buyer Id Source is set to Location and the Buyer Id is not equal
		-- to the Default Buyer Id in the Location Table and the Freeze Buyer Id
		-- Option is disabled, update the Buyer Id to reflect the Default Buyer Id 
		-- in the Location Table
		IF @ByIdSource = 'L' 
		AND @ById <> @Lc_Dft_ById 
		AND @Freeze_ById = 'N'
		BEGIN
			IF @ByIdSource = 'L' 
			And @ById = '' 
				SELECT @ById = @Lc_Dft_ById

			UPDATE item SET ById = @Lc_Dft_Byid WHERE lcid = @LcId and item = @Item
		END
 
		-- IF the Buyer Id Source is SET to Vendor and the Buyer Id is not equal
		-- to the Default Buyer Id in the Vendor Table and the Freeze Buyer Id
		-- Option is disabled, update the Buyer Id to reflect the Default Buyer Id 
		-- in the Vendor Table
		IF @ByIdSource = 'V' 
		AND @ById <> @Dft_ById 
		AND @Freeze_ById = 'N'
		BEGIN
			IF @ByIdSource = 'V' 
			AND @ById = '' 
				SELECT @ById = @Dft_ById

			UPDATE item SET ById = @Dft_Byid WHERE lcid = @LcId and item = @Item
		END
 
		-- Get the Buyer's Initials
		SELECT @UserInitials = 'XXX'

		SELECT @UserInitials = coalesce(UserInitials, left(@ById, 3))
		FROM AIMUsers 
		WHERE UserId = @ById

		-- Determine the lead time for the item
		IF @LeadTime = 0 SELECT @LeadTime = @Dft_LeadTime

		-- Calculate available inventory
		SELECT @AvailQty = @oh
    	
		-- IF the accumulative lead time is invalid get the correct value
		IF @accum_lt <=0 
		BEGIN
			EXEC @accum_lt = AIM_GetAccum_LT_Sp @Lcid, @Item, 'Y'
		END
        
		-- Adjust for burn between the stock date and the review date
		IF @accum_lt > 0 
			SELECT @AvailQty = round(@AvailQty 
				- (datedIFf(dd, @StkDate, @isdate) 
				* (convert(decimal(10,4), @fcstlt) / convert(decimal(10,4), @accum_lt))), 0)
     
		-- Adjust Availability for On-Order and COMMITted inventory
		SELECT @AvailQty = (@AvailQty + @oo) - @comstk - @bkorder - @bkcomstk

		IF @Allow_Negative_AvailQty ='N' 
		Begin
			-- On-hand cannot be less than zero
			IF @AvailQty < 0 SELECT @AvailQty = 0
		End
 		
		-- Assign Line Type Codes
		SELECT @POLineType = 
			case 
				WHEN @RC_Flag = 'Y' THEN 20
				WHEN @DR_Flag = 'Y' THEN 20
				WHEN @LT_Flag = 'Y' 
					and @fcstlt >= @AvailQty 
					and @itstat <> 'U' and @itstat <> 'N' THEN 10
				WHEN @PE_Flag = 'Y' 
					and @itstat = 'H' THEN 30
				WHEN @PE_Flag = 'Y' 
					and @velcode <= @Priority_Min_Velcode 
					and @dser >= @Priority_Min_Dser 
					and @itstat <> 'U' 
					and @itstat <> 'N' THEN 30
				ELSE 0
			END
   
		-- Additional processing required ?
		IF @POLineType = 0 
			goto GetNextRow	-- skip the rest of the loop processing.
	
		-- Check for a discontinued item approaching it's discontinuation date
		SELECT @MaxDueDate = dateadd(dd, @accum_lt, @isdate) 
		IF @itstat = 'D' AND @inactdate <= @MaxDueDate 
		BEGIN
			-- Bypass ordering for expired discontinued items
			-- Change the discontinued item's status to I=Inactive
			-- UPDATE item SET itstat = 'I' WHERE lcid = @lcid and item = @item
			goto GetNextRow
		END

		-- Check for an MDC Branch -- Set MDC Flag
		-- Changed POType to Status 'P' since 'T' is for transfer
		IF @VnMDCFlag = 'Y' SELECT @POType = 'P' 
		ELSE SELECT @POType = 'P'
	
		-- Check for an Master Distribution Center. MDC order points
		-- and order quantities are stored in the MDC Summary Table.
		IF @MDCFlag = 'Y'
	  	BEGIN
			SELECT @orderpt = OrderPt, @orderqty = orderqty,
				@fcstlt = FcstLt, @safetystock = SafetyStock,
				@fcstrt = FcstRt
			FROM MDCSummary
			WHERE MDC = @LcId and Item = @Item
		END
	    
		-- Check Buying Strategy 
		IF @buystrat = 'M'			-- Min/Max Method
		BEGIN
			-- Check for default values
			IF (@usermin = 0 and @usermax = 0) 
			OR (@usermin = 0 and @usermax = 999999999)
				SELECT @orderpt = @orderpt, @orderqty = @orderqty
			ELSE SELECT @orderpt = @usermin, @orderqty = @usermax - @usermin
		END
		ELSE IF @buystrat = 'T'			-- Time Supply Method
		BEGIN
			IF @usermin = 0 SELECT @orderpt = @orderpt
			ELSE SELECT @orderpt = (@usermin * (convert(decimal(10,4), @FcstLt) / convert(decimal(10,4), @Accum_Lt))) + @FcstRT
	
			-- Order Point cannot be less than the Forecast for the lead time.
			-- IF it is the time supply user minimum is insufficient to provide
			-- safety stock
			IF @orderpt < @FcstLt SELECT @orderpt = @FcstLt
			
			IF (@usermax = 0 OR  @usermax = 999999999) SELECT @orderqty = @orderqty
			ELSE SELECT @orderqty = (@usermax * (convert(decimal(10,4), @FcstLt) / convert(decimal(10,4), @Accum_Lt))) - @orderpt
		END

		-- IF the item's order point = 0, check for adjustments
		IF @zopsw = 'N' AND @orderpt = 0 SELECT @orderpt = 1
	
		-- IF the Order-Up-To-Limit Switch is set, force an order by
		-- setting the order point = order point + order quantity
		IF @outlsw = 'Y' SELECT @orderpt = @orderpt + @orderqty
	
		-- Check for a zero order quantity
		IF @orderqty < 1 and @Orderqty >0 SELECT @orderqty = 1
	
		-- Generate an order IF the available inventory for an item is
		-- less than or equal to the order point
			IF @DropShip_XDock = 'Y'
		Begin
			Select @RSOQ = @comstk + @bkorder + @bkcomstk
		End
		ELSE
		Begin
			IF @AvailQty > @orderpt 
			Begin
				SELECT @RSOQ = 0, @SOQ = 0, @VSOQ = 0
			End
			Else
			Begin
			 	Select @RSOQ =@OrderPt,@SOQ =0,@VSOQ =0
			End
					
			-- Packround the raw suggested order quantity -    suggested order quantity
			IF @RSOQ > 0
			BEGIN
				-- Adjust the raw order quantity for the available inventory
				-- 	Optimal Order Quantity (O): Adjust to midpoint of forecast for review time
				-- 	Min/Max (M): Adjust for dIFference between order point and available inventory
				-- 	Time Supply (T): Adjust for dIFference between order point and available inventory
				IF @DropShip_XDock = 'N'
				SELECT @RSOQ = 
					case @buystrat
					WHEN 'M' THEN @orderqty + (@orderpt - @AvailQty)
					WHEN 'O' THEN @orderqty + (@fcstlt + @safetystock + (.5 * @fcstrt) - @AvailQty)
					WHEN 'T' THEN @orderqty + (@orderpt - @AvailQty)
					ELSE @orderqty + (@fcstlt + @safetystock + (.5 * @fcstrt) - @AvailQty) 
					END
			
				-- IF this is a discontinued item, the order quantity cannot exceed the
				-- forecast demand between the order generation date (isdate) and the
				-- effective date of the item's discontinuance (inactdate)
				-- less the available inventory
				IF @itstat = 'D' 
				BEGIN
					-- IF the Inactive Date <= Isdate
					-- do not order this item if the inactdate is equal to 12-31-9999
					IF @isdate >= @inactdate or (datediff(dd,CONVERT(datetime,'12-31-9999',101),@inactdate)=0 )
						SELECT @MaxOrdQty = 0, @RSOQ = 0
					ELSE
					BEGIN
						-- Calculate maximum order quantity for the discontinued item
						IF @accum_lt > 0 
							SELECT @MaxOrdQty = round(datedIFf(dd, @isdate, @inactdate) * 
								(convert(decimal(10,4), @FcstLt) / convert(decimal(10,4), @Accum_Lt)), 						0) - @AvailQty 
						ELSE 
							SELECT @MaxOrdQty = 0
							IF @MaxOrdQty < 0 SELECT @MaxOrdQty = 0
			    				-- The order quantity cannot exceed the maximum order quantity for a 					-- discontinued item
							IF @RSOQ > @MaxOrdQty SELECT @RSOQ = @MaxOrdQty
			
							-- A discontinued item cannot packround up
							SELECT @PackRounding = 'D'
			   			
							IF @RSOQ < @ConvFactor SELECT @RSOQ = 0
					END
				END    
		    	END -- >0 RSOQ
		END -- Not DropShip	
			-- Calculate the packrounded quantity
			IF @RSOQ > 0
				EXEC @SOQ = AIM_PackRounding_sp @RSOQ, @UOM, @ConvFactor, 
				@BuyingUOM OUTPUT, @PackRounding, @IMin, @IMax  
		
		-- Initialize the vendor SOQ
		SELECT @VSOQ = @SOQ
	
		-- Test for OUTPUT to PO Table
		-- Write OUTPUT for:
		--   . All items scheduled for a line review (priority 20)
		--   . Lead Time exceptions, priority exceptions with a positive SOQ (priority 10, 30)     
		IF @POLineType = 20 or (@POLineType > 0 and @SOQ > 0) 
		BEGIN
			-- Update the Order Totals
			-- SELECT VSOQAmt = @VSOQAmt, Reach_Code = @Reach_Code, Cube = @Cube, Cost = @Cost, 
			--    Weight = @Weight, VSOQ = @VSOQ
			SELECT @VSOQAmt = 
				@VSOQAmt + case @Reach_Code
					WHEN 'C' THEN @VSOQ * @Cube
					WHEN 'D' THEN @VSOQ * @Cost
					WHEN 'U' THEN @VSOQ 
					WHEN 'W' THEN @VSOQ * @Weight
					ELSE 0
				END
			-- SELECT VSOQAmt = @VSOQAmt, Reach_Code = @Reach_Code, Cube = @Cube, Cost = @Cost, 
			--    Weight = @Weight, VSOQ = @VSOQ
			
			-- Increment counters
			IF @POLineType = 10 SELECT @LT_Count = @LT_Count + 1
			IF @POLineType = 20 SELECT @RC_Count = @RC_Count + 1
			IF @POLineType = 30 SELECT @PE_Count = @PE_Count + 1

			-- Calculate due date
			SELECT @dudate = dateadd(dd, @LeadTime, @isdate)

			-- Determine the Status Code for the Detail Record
			-- Items with a zero Vendor Suggested Order Quantity (VSOQ) 
			-- are always released as planned order lines.
			IF @VSOQ > 0 SELECT @OrdStatus = @Dft_POStatus
			ELSE SELECT @OrdStatus = 'P'

			-- Insert a record into the PO Table/Update a PO Table Record
			IF @OutPutOpt = 'Y'
			BEGIN
				-- Check for a sale last week
				IF @LastWeekSalesFlag = 'Y'
				BEGIN
					EXECute @RtnCode = AIM_GetLastWeekSales_Sp @LcId, @Item, @FcstUpdCyc
					IF @RtnCode = 1SELECT @LastWeekSalesFlag = 'Y'
					ELSE  SELECT @LastWeekSalesFlag = 'N'
				END
				ELSE
				BEGIN
					SELECT @LastWeekSalesFlag = 'N'
	    	   		END

				-- Increment the Positive Line Count/Totals
				IF @VSOQ > 0
				BEGIN
					SELECT @PosLineCount = @PosLineCount + 1,
						@TotalCost = @TotalCost + (@VSOQ * @Cost)
				END
				
				EXEC AIM_POInsert_sp 
					@POLineType, @OrdStatus, @RevCycle, @ById, @VnId, 
					@Assort, @Lcid, @Item, @ItDesc, @POType, @AvailQty, @PackRounding, 
					@RSOQ, @SOQ, @VSOQ, @UOM, @BuyingUOM, @ConvFactor, @IsDate, 
					@DuDate, @LastWeekSalesFlag, @Cost, @Zone
				
			END
			ELSE
			BEGIN
				SELECT POLineType = @POLineType, OrdStatus = @OrdStatus, 
					RevCycle = @RevCycle, ById = @ById, VnId = @VnId, Assort = @Assort, 
					LcId = @Lcid, Item = @Item, POType = @POType, 
					AvailQty = @AvailQty, PackRounding = @PackRounding, 
					RSOQ = @RSOQ, SOQ = @SOQ, VSOQ = @VSOQ, UOM = @UOM, 
					BuyingUOM = @BuyingUOM, ConvFactor = @ConvFactor, 
					IsDate = @IsDate, DuDate = @DuDate, LastWeekSalesFlag =@LastWeekSalesFlag, 
					Cost = @Cost, Zone = @Zone
			END
		END    -- Write output end
		
		-- 'goto' label
		GetNextRow:
			-- => loop has been skipped from where the label was called. Get the next row.

	END	-- End Loop

	-- Clean Up
	CLOSE ordgen
	DEALLOCATE ordgen
	
	-- Determine the buying goal
	IF @BuyGoal = 0 and @Vn_Min > 0 SELECT @BuyGoal = @Vn_Min
	IF @BuyGoal = 0 and @Vn_Min <= 0 SELECT @BuyGoal = @Vn_Best        

	-- SELECT @VnId, @Assort, buygoal = @BuyGoal, vsoqamt = @VSOQAmt
	-- Check to see IF vendor minimums were met for Dynamic Review Cycles
	IF @DR_Flag = 'Y' 
	BEGIN
		If @VSOQAmt >= @BuyGoal  
		BEGIN
			COMMIT TRANSACTION
			RETURN 1                -- Succeed
		END
		ELSE IF @VSOQAmt < @BuyGoal 
		BEGIN
			ROLLBACK TRANSACTION
			SELECT	@LT_Count = 0, @RC_Count = 0, @PE_Count = 0
			RETURN 1                -- Succeed
		END
	END

	-- Update the Purchase Order Header
	SELECT @LineCount = (@LT_Count + @RC_Count + @PE_Count)
	
	-- Write a PO Header IF detail lines were written
	IF @LineCount > 0 
	BEGIN 
		EXEC AIM_POHdrInsert_Sp 
			@ById, @VnId, @Assort, @POStatus, @TransmitPO, 
			@ShipIns, @UserInitials, '', '', '', 'N', 
			@VName, @VAddress1, @VAddress2, @VCity, @VState, 
			@VZip, @LineCount, @PosLineCount, @Vn_Min, @Vn_Best, @Reach_Code, 
			@POSource, @POByZone, @Dft_LeadTime, @TotalCost
		-- Set the VendorCostExcpt flag
				EXEC AIM_SetLowerVendorCostAlert_Sp @Vnid,@Assort
	END
 	
	-- Commit the TRANSACTION Set
	COMMIT TRANSACTION
	
	RETURN 1
	GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

