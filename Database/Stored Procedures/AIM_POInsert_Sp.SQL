SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POInsert_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POInsert_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_POInsert_Sp
**	Desc: Updates/Creates the data in the PODetail table.
**
**	Returns: 1) @@rowcount - Can be Zero
**               2) 2 - Update
**               3) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**	2004/04/19 S.Uddanti 	Increased the size of itdesc field from 30 to 50
*******************************************************************************/
   
CREATE PROCEDURE AIM_POInsert_Sp
(
       	@POLineType	        				smallint, 
     	@OrdStatus	        				nvarchar(1), 
        @RevCycle	        				nvarchar(8), 
        @ById		        				nvarchar(12), 
        @VnId		        				nvarchar(12), 
        @Assort	            				        nvarchar(12), 
        @Lcid		        				nvarchar(12), 
        @Item		        				nvarchar(25), 
     	@ItDesc					    		nvarchar(50),
        @POType	            				        nvarchar(1), 
       	@AvailQty	        				int, 
        @PackRounding	    				        nvarchar(1), 
       	@RSOQ		        				int, 
       	@SOQ		        				int, 
       	@VSOQ		        				int, 
        @UOM		        				nvarchar(6), 
        @BuyingUOM	        				nvarchar(6), 
       	@ConvFactor	        				int, 
       	@IsDate	            				        datetime, 
       	@DuDate	            				        datetime, 
        @LastWeekSalesFlag  				        nvarchar(1), 
    	@Cost					     		decimal(10,4),
     	@Zone						     	nvarchar(1)
)	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      						int,
        @POSeqId    						int
            
SET NOCOUNT ON

-- Validate the required parameters.
IF @ById IS NULL
BEGIN
	RETURN 0
END

IF @VnId IS NULL
BEGIN
	RETURN 0
END

IF @Assort IS NULL
BEGIN
	RETURN 0
END

IF @Item IS NULL
BEGIN
	RETURN 0
END
 
IF @LcId IS NULL
BEGIN
	RETURN 0
END

SELECT @POSeqId = POSeqId 
FROM PODetail
WHERE ById = @ById
AND VnId = @VnId
AND Assort = @Assort
AND Item = @Item
AND LcId = @LcId
        
  SELECT @found = @@rowcount
  IF @found = 0
  BEGIN
        INSERT INTO PODetail
            (POLineType, OrdStatus, RevCycle, ById, 
            VnId, Assort, Lcid, Item, ItDesc, POType, AvailQty, PackRounding, 
            RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, ConvFactor, IsDate, 
            DuDate, LastWeekSalesFlag, Cost, Zone)
        VALUES (@POLineType, @OrdStatus, @RevCycle, @ById, 
            @VnId, @Assort, @Lcid, @Item, @ItDesc, @POType, @AvailQty, @PackRounding, 
            @RSOQ, @SOQ, @VSOQ, @VSOQ, @UOM, @BuyingUOM, @ConvFactor, @IsDate, 
            @DuDate, @LastWeekSalesFlag, @Cost, @Zone)
       
            RETURN @@rowcount
  END
  ELSE
  BEGIN
	UPDATE PODetail
        SET POLineType = @POLineType, OrdStatus = @OrdStatus, 
            RevCycle = @RevCycle, ById = @ById, 
            VnId = @VnId, Assort = @Assort, Lcid = @Lcid, Item = @Item, 
            POType = @POType, AvailQty = @AvailQty, 
            PackRounding = @PackRounding, RSOQ = @RSOQ, SOQ = @SOQ, 
            VSOQ = @VSOQ, Original_VSOQ = @VSOQ, UOM = @UOM, BuyingUOM = @BuyingUOM, 
            ConvFactor = @ConvFactor, IsDate = @IsDate, DuDate = @DuDate, 
            LastWeekSalesFlag = @LastWeekSalesFlag, Cost = @Cost, Zone = @Zone
    	WHERE PODetail.ById = @ById
    	AND PODetail.vnid = @VnId
    	AND PODetail.assort = @Assort
    	AND PODetail.item  = @Item
    	AND PODetail.lcid = @LcId

        IF @@rowcount > 0
        BEGIN
	    RETURN 2			-- UPDATE
    	END
	ELSE
        BEGIN
	    RETURN 0			-- ERROR
    	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

