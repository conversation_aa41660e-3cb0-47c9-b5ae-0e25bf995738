SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SetSafetyStockAdj_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SetSafetyStockAdj_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_SetSafetyStockAdj_Sp
**	Desc: Sets the Saftey Stock Adjustment in the Item Table.
**
**	Returns: 1)@rows - Can be Zero
**
**    Parameters:
**  	@Option		Update Option
**  				C:	Clear all Safety Stock Adjustments (Reset to zero)
**  				L: 	Update all items within a location
**  				LI:	Update a location/item combination
**  				V:	Update all items associated with a Vendor Id
**  				VA:	Update all items associated with a Vendor/Assortment
**  				I:	Update an all across all locations
**  	@LcId		Location Id (required for options L and LI, ELSE blank)
**  	@VnId		Vendor Id (required for options V and VA, ELSE blank)
**  	@Assort		Assortment (required for option VA, ELSE blank)
**  	@Item		Item Id (required for options LI and I, ELSE blank)
**  	@SSAdj_A	Safety Stock Adjustment for A Items
**  	@SSAdj_B	Safety Stock Adjustment for B Items
**  	@SSAdj_C	Safety Stock Adjustment for C Items
**  	@SSAdj_D	Safety Stock Adjustment for D Items
**  	@SSAdj_E	Safety Stock Adjustment for E Items
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      10/01/2002 Osama Riyahi Updated @SSAdy_X values to (9,4)
**      10/29/2002 Wade Riza    Updated C,D,E to their corresponding values
*******************************************************************************/

CREATE PROCEDURE AIM_SetSafetyStockAdj_Sp 
(
  	@Option 						nvarchar(2) = 'L', 
   	@LcId 							nvarchar(12) = '',
   	@VnId							nvarchar(12) = '',
   	@Assort							nvarchar(12) = '',
   	@Item							nvarchar(25) = '',
   	@SSAdj_A						decimal(9,4) = 0,
   	@SSAdj_B						decimal(9,4) = 0,
   	@SSAdj_C						decimal(9,4) = 0,
   	@SSAdj_D						decimal(9,4) = 0,
   	@SSAdj_E						decimal(9,4) = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rows 						int
  
SET NOCOUNT ON
  
IF @option = 'C'		-- Clear All
BEGIN
  	UPDATE item SET ssadj = 0
    	WHERE ssadj <> 0
    	
	SELECT @rows = @@rowcount
END
  	
IF @option = 'L'		-- Update all items within a Location
BEGIN
  	UPDATE item SET ssadj = 
  	CASE VelCode
  	WHEN 'A' THEN @SSAdj_A
  	WHEN 'B' THEN @SSAdj_B
  	WHEN 'C' THEN @SSAdj_C
  	WHEN 'D' THEN @SSAdj_D
  	WHEN 'E' THEn @SSAdj_E
  	END
  	WHERE lcid = @LcId

	SELECT @rows = @@rowcount
END
IF @Option = 'LI'		-- Update a Location/Item combination
BEGIN
  	UPDATE item SET ssadj = 
  	CASE VelCode
  	WHEN 'A' THEN @SSAdj_A
  	WHEN 'B' THEN @SSAdj_B
  	WHEN 'C' THEN @SSAdj_C
  	WHEN 'D' THEN @SSAdj_D
  	WHEN 'E' THEN @SSAdj_E
  	END
  	WHERE lcid = @LcId
  	AND item = @Item

	SELECT @rows = @@rowcount
END
IF @option = 'V'		-- Update all items for a Vendor
BEGIN
  	UPDATE item SET ssadj = 
  	CASE VelCode
  	WHEN 'A' THEN @SSAdj_A
  	WHEN 'B' THEN @SSAdj_B
  	WHEN 'C' THEN @SSAdj_C
  	WHEN 'D' THEN @SSAdj_D
  	WHEN 'E' THEN @SSAdj_E
  	END
  	WHERE vnid = @VnId

	SELECT @rows = @@rowcount
END
IF @option = 'VA'		-- Update all items for a Vendor/Assortment
BEGIN
  	UPDATE item SET ssadj = 
  	CASE VelCode
  	WHEN 'A' THEN @SSAdj_A
  	WHEN 'B' THEN @SSAdj_B
  	WHEN 'C' THEN @SSAdj_C
  	WHEN 'D' THEN @SSAdj_D
  	WHEN 'E' THEN @SSAdj_E
  	END
  	WHERE vnid = @VnId
  	AND Assort = @Assort

	SELECT @rows = @@rowcount
END
IF @option = 'I' 		-- Update all items for an Item
BEGIN
  	UPDATE item SET ssadj = 
  	CASE VelCode
  	WHEN 'A' THEN @SSAdj_A
  	WHEN 'B' THEN @SSAdj_B
  	WHEN 'C' THEN @SSAdj_C
  	WHEN 'D' THEN @SSAdj_D
  	WHEN 'E' THEN @SSAdj_E
  	END
  	WHERE item = @Item
	SELECT @rows = @@rowcount
END
  
-- Return the number of items updated
RETURN @rows

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

