IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AllocDefaults_Delete_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AllocDefaults_Delete_Sp]
GO
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: 	AIM_Allocation_Defaults_Delete_Sp
**	Desc: 	Deletes the AllocDefaults record and all its children from the
** 		AllocDefaultsSource Table. Derived from AIM_Forecast_Delete_Sp
**	Parameters: 
** 		@AllocDefaultsID -- numeric
**	Returns: 
**		 @Found --> (Can be = 0) Successful
**		-1 --> No Data Found
**		-2 --> SQL Error
**		-3 --> User Not Permitted to retrieve data
**              
**	Auth:   Wade Riza
**	Date:   2003/06/09
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------	--------------------------------------------
*******************************************************************************/

  CREATE   PROCEDURE AIM_AllocDefaults_Delete_Sp
  (
 	@AllocDefaultsID  		numeric(9,0)
  )
  
 -- WITH ENCRYPTION	/* Production use must be encrypted */
  AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
 

  DECLARE @found        		int,
	@rtrn				int

  SET NOCOUNT ON

  -- Validate the required parameters.
  IF @AllocDefaultsID Is Null
  BEGIN
  	RETURN -1
  END

  BEGIN TRAN --Start of Transaction

  DELETE FROM AllocDefaultsSource
  WHERE AllocDefaultsID = @AllocDefaultsID

  -- Check for SQL Server errors.
  IF @@ERROR <> 0 
	BEGIN
	ROLLBACK TRAN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

  DELETE FROM AllocDefaults
  WHERE AllocDefaultsID = @AllocDefaultsID

  -- Check for SQL Server errors.
  IF @@ERROR <> 0 
	BEGIN
	ROLLBACK TRAN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

  COMMIT TRAN  -- End of Transaction

  RETURN 0
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

