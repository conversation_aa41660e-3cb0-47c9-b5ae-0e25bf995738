SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxLC_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxLC_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxLC_Sp
**	Desc: Loads data exchange item records into the Location Table
**            from the AIMDxLC Table. 
**	NOTE: Locations is similar to the item and vendor interfaces. Thefore, this 
**	      stored procedure has been derived from AIM_DxVN_Sp. 
**	      It follows the same operations as the Vendor data interface
**
**	Before (and after) making changes to the inbound stored procedures, 
**      please check the table creation scripts and the results of the bulk insert
**      for consistent behaviour.
**
**      Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**			1)  0 - Successful
**              	2) -1 - No Data Found
**              	3) -2 - SQL Error
**              	4) -3 - Duplicate File Name
**              	5) -4 - Invalid File Name
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/16
********************************************************************************
**	Change History
********************************************************************************
**	Date:	   Updated by:	 Description:
**	---------- ------------- -----------------------------------------------
**      2003/05/28 A. Stocksdale Replaced bulk insert and validation commands 
**				 with call to AIM_DxBulkInsert_Sp - common to all
**                               Dx processes.
**      2003/06/25 Srinivas U    Changed code to correct the update statements
**      2003/08/21 A. Stocksdale Rolled back changes made on 2003/06/25 for the 
**                               following reasons:
** 				 Apparently this stored procedure would overwrite 
**                               all records with default values on attempting to 
**                               change data for existing locations.
**  				 The real problem was two-fold:
**				 1. The values were being overwritten on the bulk 
**                                  insert into AIMDxLC (null fields with Default 
**                                  values) and not in this stored procedure.
**				 2. The update statement merely needed a where 
**                                  clause to fix the error.
**      2003/08/21 A. Stocksdale Fixed error on AIMLocations insert, as follows:
**				 Two fields (StkDate and LastFcstUpdCyc) had been 
**                               removed from the AIMDxLC table, but they still 
**                               existed in AIMLocations, and needed values inserted, 
**                               default or otherwise.
**      2004/02/02 S.Uddanti	 LookBackPds field is set to a default value of 13 
**                               if null or 0.
**      2004/02/19 S.Uddanti	 Add delete from AIMDX so that the procedure works 
**                               even when there are some updates and some inserts
**    	2005/03/08 Srinivas U	Added truncate and delete before the bulk insert
***********************************************************************************/
 
CREATE PROCEDURE AIM_DxLC_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT	
)
-- WITH ENCRYPTION 	/* Production use must be encrypted */
AS  	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	/* START VARIABLE DECLARATION */  
	DECLARE 
		@RtnCode int

	/* Start variable initialization */
	--Initialize counters
	SELECT @InsertCounter = 0, @UpdateCounter = 0
		-- Delete records from the DxLC Table
	TRUNCATE TABLE AIMDxLC
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxLC
	-- This is a data interface transaction <Transaction Code=LC>. 
	-- Bulk Load the <Transaction Table=AIMDxLC> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'LC', 'AIMDxLC', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.
	
	BEGIN TRANSACTION
		UPDATE AIMLocations
		SET LName = ISNULL( AIMDxLC.LName, AIMLocations.LName), 
			LType = ISNULL( AIMDxLC.LType, AIMLocations.LType),
			LStatus = ISNULL( AIMDxLC.LStatus, AIMLocations.LStatus), 
			LDivision = ISNULL( AIMDxLC.LDivision, AIMLocations.LDivision),
			LRegion = ISNULL( AIMDxLC.LRegion, AIMLocations.LRegion), 
			LUserDefined = ISNULL( AIMDxLC.LUserDefined, AIMLocations.LUserDefined),
			LAddress1 = ISNULL( AIMDxLC.LAddress1, AIMLocations.LAddress1), 
			LAddress2 = ISNULL( AIMDxLC.LAddress2, AIMLocations.LAddress2),
			LAddress3 = ISNULL( AIMDxLC.LAddress3, AIMLocations.LAddress3), 
			LAddress4 = ISNULL( AIMDxLC.LAddress4, AIMLocations.LAddress4),
			LCity = ISNULL( AIMDxLC.LCity, AIMLocations.LCity), 
			LState = ISNULL( AIMDxLC.LState, AIMLocations.LState),
			LZip = ISNULL( AIMDxLC.LZip, AIMLocations.LZip), 
			LCountry = ISNULL( AIMDxLC.LCountry, AIMLocations.LCountry),
			LContact = ISNULL( AIMDxLC.LContact, AIMLocations.LContact), 
			LPhone = ISNULL( AIMDxLC.LPhone, AIMLocations.LPhone),
			LFax = ISNULL( AIMDxLC.LFax, AIMLocations.LFax), 
			LEmail = ISNULL( AIMDxLC.LEmail, AIMLocations.LEmail),
			LRank = ISNULL( AIMDxLC.LRank, AIMLocations.LRank),
			PutAwayDays = ISNULL( AIMDxLC.PutAwayDays, AIMLocations.PutAwayDays),
			ReplenCost = ISNULL( AIMDxLC.ReplenCost, AIMLocations.ReplenCost),
			DemandSource = ISNULL( AIMDxLC.DemandSource, AIMLocations.DemandSource),
			LeadTimeSource = ISNULL( AIMDxLC.LeadTimeSource, AIMLocations.LeadTimeSource),
			-- not included in the inbound file -- StkDate = ISNULL( AIMDxLC.StkDate, AIMLocations.StkDate),
			Dft_Byid = ISNULL( AIMDxLC.Dft_Byid, AIMLocations.Dft_ByID),
			-- not included in the inbound file -- Last_FcstUpdCyc = ISNULL( AIMDxLC.Last_FcstUpdCyc, AIMLocations.Last_FcstUpdCyc),
			LookBackPds = ISNULL( AIMDxLC.LookBackPds, AIMLocations.LookBackPds),
			DmdScalingFactor = ISNULL( AIMDxLC.DmdScalingFactor, AIMLocations.DmdScalingFactor),
			ScalingEffUntil = ISNULL( AIMDxLC.ScalingEffUntil, AIMLocations.ScalingEffUntil),
			Freeze_Period = ISNULL( AIMDxLC.Freeze_Period, AIMLocations.Freeze_Period),
			UpdateCurrentYearOption = ISNULL( AIMDxLC.UpdateCurrentYearOption, AIMLocations.UpdateCurrentYearOption),
			DropShip_XDock = ISNULL( AIMDxLC.DropShip_XDock, AIMLocations.DropShip_XDock),
			Dft_ReviewerId = ISNULL( AIMDxLC.Dft_ReviewerId, AIMLocations.Dft_ReviewerID)
		FROM AIMDxLC
		WHERE AIMDxLC.LcID = AIMLocations.LcID
	
		SET @UpdateCounter = @@ROWCOUNT 

		DELETE FROM AIMDXLC
		WHERE LcID in ( SELECT LcId FROM AIMLocations)
		-- INSERT
		--Substitute defaults if data not provided		
		INSERT INTO AIMLocations (Lcid,
			LName, LType,
			LStatus, LDivision,
			LRegion, LUserDefined,
			LAddress1, LAddress2,
			LAddress3, LAddress4,
			LCity, LState,
			LZip, LCountry,
			LContact, LPhone,
			LFax, LEMail,
			LRank,
			PutAwayDays,
			ReplenCost,
			DemandSource,
			LeadTimeSource,
			StkDate,
			Dft_Byid,
			Last_FcstUpdCyc,
			LookBackPds,
			DmdScalingFactor, ScalingEffUntil,
			Freeze_Period,
			UpdateCurrentYearOption,
			DropShip_XDock,
			Dft_ReviewerId,
			ExceptionPct)
        SELECT RTRIM(AIMDxLC.Lcid),
			ISNULL( AIMDxLC.LName, ''), ISNULL( AIMDxLC.LType, ''),
			ISNULL( AIMDxLC.LStatus, ''), ISNULL( AIMDxLC.LDivision, ''),
			ISNULL( AIMDxLC.LRegion, ''), ISNULL( AIMDxLC.LUserDefined, ''),
			ISNULL( AIMDxLC.LAddress1, ''), ISNULL( AIMDxLC.LAddress2, ''),
			ISNULL( AIMDxLC.LAddress3, ''), ISNULL( AIMDxLC.LAddress4, ''),
			ISNULL( AIMDxLC.LCity, ''), ISNULL( AIMDxLC.LState, ''),
			ISNULL( AIMDxLC.LZip, ''), ISNULL( AIMDxLC.LCountry, ''),
			ISNULL( AIMDxLC.LContact, ''), ISNULL( AIMDxLC.LPhone, ''),
			ISNULL( AIMDxLC.LFax, ''), ISNULL( AIMDxLC.LEMail, ''),
			CASE WHEN RTRIM(AIMDxLC.LRank) >= 0 THEN AIMDxLC.LRank
				ELSE ISNULL( AllocDefaults.LRank, 0)
				END,
			ISNULL( AIMDxLC.PutAwayDays, 0), ISNULL( AIMDxLC.ReplenCost, 0.0),
			ISNULL( AIMDxLC.DemandSource, ''),
			ISNULL( AIMDxLC.LeadTimeSource, ''),
			CONVERT(nvarchar(10), GETDATE(), 101), -- not included in inbound, use default values
			ISNULL( AIMDxLC.Dft_Byid, ''),
			0, -- not included in inbound, use default values
			 CASE WHEN AIMDXLC.LookBackPds < 13 THEN  13
			      ELSE ISNULL( AIMDxLC.LookBackPds, 13)
			      END,
			ISNULL( AIMDxLC.DmdScalingFactor, 0.0), 
			ISNULL( AIMDxLC.ScalingEffUntil, '12/31/9999'),
			ISNULL( AIMDxLC.Freeze_Period, 0),
			ISNULL( AIMDxLC.UpdateCurrentYearOption, ''),
			ISNULL( AIMDxLC.DropShip_XDock, ''),
			CASE WHEN RTRIM(AIMDxLC.Dft_ReviewerID) <> '' THEN AIMDxLC.Dft_ReviewerID
				ELSE ISNULL( AllocDefaults.ReviewerId, '')
				END,
			CASE WHEN AIMDxLC.ExceptionPct > 0 THEN AIMDxLc.ExceptionPct
				ELSE ISNULL( AllocDefaults.ExceptionPct, 0)
				END
		FROM AIMDxLC
		LEFT OUTER JOIN AllocDefaults ON
			AIMDxLC.LStatus = AllocDefaults.LStatus
			AND AIMDxLC.LDivision = AllocDefaults.LDivision
			AND AIMDxLC.LRegion = AllocDefaults.LRegion 
			AND AIMDxLC.LUserDefined = AllocDefaults.LUserDefined
	
		SET @InsertCounter = @@ROWCOUNT 
	
	COMMIT TRANSACTION	

	-- Delete records from the DxLC Table
	TRUNCATE TABLE AIMDxLC
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxLC
		
	UPDATE STATISTICS AIMLocations WITH RESAMPLE, ALL

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

