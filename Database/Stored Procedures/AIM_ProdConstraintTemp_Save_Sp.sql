SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProdConstraintTemp_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProdConstraintTemp_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ProdConstraintTemp_Save_Sp
**	Desc: Inserts into AIM_ProdConstraintTemp table
**
**	Returns: 1)  1 - Successful Insert
**		 2) -1 - No Data Found
**             
**	Auth:   Srinivas Uddanti 
**	Date:   04/03/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/
     
CREATE PROCEDURE AIM_ProdConstraintTemp_Save_Sp
(
   	@UniqueJobId			    		uniqueidentifier,
	@LcID		        			nvarchar(12), 
   	@Item		        			nvarchar(25), 
	@Qty					        int 
)	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @UniqueJobId IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END


IF @Qty IS NULL
BEGIN
	RETURN -1
END

BEGIN
	
	INSERT INTO ProdConstraintTemp
	    (UniqueJobId, LcID, Item, Qty,AdjQty,DateCreated)
	    VALUES ( @UniqueJobId,@LcID, @Item, @Qty, @Qty,GETDATE())
	
END

RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

