SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMOptions_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMOptions_GetKey_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHAL<PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMOptions_GetKey_Sp
**	Desc: Retrieves AIMOptions data based on value passed.
**
**	Returns: 1)@rowcount - Can be Zero
**	Values:  AIMOptions
**              
**	Auth:   Randy Sadler	
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
  
CREATE PROCEDURE AIM_AIMOptions_GetKey_Sp
(
      	@OptionId   						nvarchar(6)      	Output,
    	@Action     						tinyint
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rowcount						int

SET NOCOUNT ON

SET ROWCOUNT 1  

-- Get the key to the AIM Option Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
  	SELECT @OptionId = OptionId
        FROM AIMOptions 
        WHERE OptionId = @OptionId

        SELECT @rowcount = @@rowcount
END
IF @action = 1		-- Get Greater Than
BEGIN
	SELECT @OptionId = OptionId 
	FROM AIMOptions 
	WHERE OptionId > @OptionId

	SELECT @rowcount = @@rowcount

	IF @rowcount = 0
	BEGIN
		SELECT @action = 6      -- Get last record
	END
END
IF @action = 2		-- Get Less Than 
BEGIN
        SELECT @OptionId = OptionId 
        FROM AIMOptions 
        WHERE OptionId < @OptionId 
        ORDER BY OptionId DESC
 
        SELECT @rowcount = @@rowcount

        IF @rowcount = 0
	BEGIN
		SELECT @action = 5      -- Get first record
	END
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
        SELECT @OptionId = OptionId 
        FROM AIMOptions 
        WHERE OptionId >= @OptionId
 
        SELECT @rowcount = @@rowcount
        
	IF @rowcount = 0
        BEGIN
		SELECT @action = 6      -- Get last record
	END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
        SELECT @OptionId = OptionId 
        FROM AIMOptions 
        WHERE OptionId <= @OptionId 
        ORDER BY OptionId DESC
 
	SELECT @rowcount = @@rowcount
        
	IF @rowcount = 0
	BEGIN
        	SELECT @action = 5      -- Get first record
    	END
END
IF @action = 5		-- Get First
BEGIN
        SELECT @OptionId = OptionId 
        FROM AIMOptions  
	
        SELECT @rowcount = @@rowcount
END
IF @action = 6		-- Get Last
BEGIN
        SELECT @OptionId = OptionId 
        FROM AIMOptions 
        ORDER BY OptionId desc 
       
	SELECT @rowcount = @@rowcount
END
IF @rowcount > 0 
BEGIN
        SELECT * 
	FROM AIMOptions 
	WHERE OptionId = @OptionId 
	
        RETURN 1		-- SUCCEED
END
ELSE
BEGIN
        RETURN 0		-- FAIL
END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

