SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DxKB_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DxKB_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxKB_Sp
**	Desc: Loads data exchange item records into the ItemKitBOM Table
**            from the AIMDxKitBOM Table. 
**	NOTE: This procedure is similer to Locations Interface
**
**	Before (and after) making changes to the inbound stored procedures, 
**      please check the table creation scripts and the results of the bulk 
**      insert for consistent behaviour.
**
**      Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**			1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**                      4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**
**	Author:		Srinivas Uddanti
**	Created:	2003/02/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Updated by:	Description:
**	---------- ------------	-----------------------------------------------
**    2005/03/08 Srinivas U	Added truncate and delete before the bulk insert
*******************************************************************************/
 
CREATE   PROCEDURE AIM_DxKB_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT	
)

-- WITH ENCRYPTION 	/* Production use must be encrypted */
AS  	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	/* START VARIABLE DECLARATION */  
	DECLARE 
		@RtnCode int

	SET NOCOUNT ON

	/* Start variable initialization */
	--Initialize counters
	SELECT @InsertCounter = 0, @UpdateCounter = 0
	-- Delete records from the DxLC Table
	TRUNCATE TABLE AIMDXKB
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDXKB
	-- This is a data interface transaction <Transaction Code=KB>. 
	-- Bulk Load the <Transaction Table=AIMDxKitBOM> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'KB', 'AIMDxKB', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.
	
	BEGIN TRANSACTION
		UPDATE ItemKitBOM
			SET Lcid =AIMDXKB.LCid,
			Item =AIMDXKB.Item,
			ItemComponent =AIMDXKB.ItemComponent,
			ComponentUnits =AIMDXKB.ComponentUnits,
			ComponentScrap=AIMDXKB.ComponentScrap,
			StartDate =CONVERT(datetime,AIMDXKB.StartDate),
			EndDate =CONVERT(datetime,AIMDXKB.EndDate),
			Enabled =AIMDXKB.Enabled
		FROM AIMDxKB
		WHERE AIMDXKB.Lcid =ItemKitBOM.Lcid
		AND   AIMDXKB.Item=ItemKitBOM.Item
		AND   AIMDXKB.ItemComponent =ItemKitBOM.ItemComponent
		SET @UpdateCounter = @@ROWCOUNT 
		
		DELETE FROM AIMDXKB
		WHERE  EXISTS ( SELECT 1 FROM ItemKitBOM WHERE
		ItemKitBOM.Item=AIMDxKB.Item  AND
		ItemKitBOM.Lcid =AIMDXKB.Lcid  AND   
		ItemKitBOM.ItemComponent=AIMDXKB.ItemComponent )
		-- INSERT
		--Substitute defaults if data not provided		
		INSERT INTO ItemKitBOM (Lcid,Item,ItemComponent,
			ComponentUnits,ComponentScrap,StartDate,EndDate,Enabled
		) SELECT RTRIM(Lcid),Item,
			ItemComponent,ComponentUnits,
			ComponentScrap,CONVERT(datetime,StartDate),
			CONVERT(datetime,EndDate),Enabled
		FROM AIMDXKB
			
		SET @InsertCounter = @@ROWCOUNT 
	
	COMMIT TRANSACTION	

	-- Delete records from the DxLC Table
	TRUNCATE TABLE AIMDXKB
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDXKB
		
	UPDATE STATISTICS ItemKitBOM WITH RESAMPLE, ALL

	RETURN 1	-- SUCCESS

END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

