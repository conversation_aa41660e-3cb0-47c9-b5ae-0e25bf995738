SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SysCtrl_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SysCtrl_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_SysCtrl_Get_Sp
**	Desc: Retrieves the SysCrtl table.
**
**	Returns: @@rowcount 
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:  		Description:
**	---------- 	------------	-----------------------------------------------
** 	2005/01/04	A.Stocksdale	Modified query to use explicit field names.
**								Added new fields: [AllocPrepackEnabled], [AllocPrepackLType]
**	2005/01/19	A.Stocksdale	Removed field AllocPrepackLType - this is incompatible with the design
**	2005/03/04	S.Uddanti	Add AllowNegativeAvailQty Field
*******************************************************************************/
     
CREATE PROCEDURE AIM_SysCtrl_Get_Sp

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	SELECT 		
		[CName], [Address1], [Address2], [Address3], [City], [State], [CoZip],
		[Dft_MinRchPct], [Dft_BestRchPct],
		[DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus],
		[DxPO_Option], [KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode],
		[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], [SeasSmoothingPds],
		[CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha],
		[UpdateGOalsOption], [UpdateVnLT], [VnLTPctChgFilter],
		[VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct],
		[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex],
		[Int_SrvLvlOverrideFlag], [Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath],
		[dft_LangID], [ColonOption], [GridAutoSizeOption], [DateFormat], [TimeFormat], [UnicodeOption],
		[ProductionConstraint], [ProdConstraintGrtZero],[AllocExceptionPct],
		[SessionId], [AllocNeedDetermination], [AllocExceptionProcess], [CarryForwardRounding],
		[LastOrderGenerationProcess], [MasterItemOption], [CreateDBUser], [UpdateDBPassword]
		, [AllocPrepackEnabled], [AllowNegativeAvailQty], [NumOffDaysBehindFromCurrentDate], [WantErrorsTobeLoggedToDatabase]
	FROM SysCtrl
	  
	RETURN @@ROWCOUNT

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

