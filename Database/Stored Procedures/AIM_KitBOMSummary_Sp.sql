SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_KitBOMSummary_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_KitBOMSummary_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_KitBOMSummary_Sp
**	Desc: Retrieves the data from the KITBOM Processing table.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
    
CREATE  PROCEDURE AIM_KitBOMSummary_Sp(
 	@Lcid            				nvarchar(12),
  	@Item           				nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @KFactor 					decimal(5,2)
  
SET NOCOUNT ON
    
-- Get The K Factor from the System Control Table
SELECT @KFactor = KFactor 
FROM SysCtrl
 
SELECT  Item.Trend,
        Item.LtvFact, Item.SSAdj, Item.ZSStock, Item.CStock, Item.ReplenCost2, Item.Dser,
        Item.Cost, Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02,
        Item.BkQty03, Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05,
        Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08,   
        Item.BkQty09, Item.BkCost09, Item.BkQty10, Item.BkCost10, Item.Accum_LT, Item.ReviewTime, 
        AIMLocations.ReplenCost,
        AIMOptions.LoMadP, AIMOptions.HiMadP, AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow,
        AIMOptions.MadExk,
        KFactor = @KFactor
FROM 	Item
        INNER JOIN AIMLocations On Item.Lcid= AIMLocations.LcId 
        INNER JOIN AIMOptions On Item.OptionId = AIMOptions.OptionId
WHERE Item.Lcid =@Lcid AND
      Item.Item =@Item
  

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



