SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UpdateOrdStatus_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UpdateOrdStatus_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UpdateOrdStatus_Sp
**	Desc: Modifies the PO Status in the AIMPO table with the current state. 
**
**	Returns: 1)@found - Can be Zero
**
**	Values:  
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**      11/02/2002 Wade Riza	Updated with validation.
*******************************************************************************/
    
CREATE PROCEDURE AIM_UpdateOrdStatus_Sp
(
  	@ById							nvarchar(12),
  	@VnId      						nvarchar(12),
  	@Assort    						nvarchar(12),
        @POStatus  						nvarchar(1)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

-- Validate the required parameters.
IF @VnId IS NULL
BEGIN
	RETURN 0
END

IF @ById IS NULL
BEGIN
	RETURN 0
END

IF @Assort IS NULL
BEGIN
	RETURN 0
END

IF @POStatus IS NULL
BEGIN
	RETURN 0
END

UPDATE AIMPO
SET AIMPO.POStatus = @POStatus
WHERE AIMPO.ById = @ById
AND AIMPO.VnId = @VnId
AND AIMPO.Assort = @Assort
  
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

