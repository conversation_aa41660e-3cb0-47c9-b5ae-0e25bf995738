SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_Adjustments_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_Adjustments_Fetch_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DmdPlan_Adjustments_Fetch_Sp
**	Desc: Updates AIMFcstStoreAdjustLog for the given FcstID.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------
**	10/22/2004      Wade Riza               Add DateDiff function
*******************************************************************************/

CREATE   PROCEDURE AIM_DmdPlan_Adjustments_Fetch_Sp
(
	@FcstId as nvarchar(12),
	@LcID as nvarchar(12) = NULL,
	@Item as nvarchar(25) = NULL,
	@AdjType as smallint =3,
	@AdjustBegDate as datetime = NULL,
	@AdjustEndDate as datetime = NULL
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat int
	DECLARE @AccessCode int	
	DECLARE @RepositoryKey as numeric

	BEGIN
		SELECT @RepositoryKey =RepositoryKey
		FROM ForecastRepository
		WHERE FcstID =@FcstId
		If @@Rowcount =0 
		BEGIN
			Return -1
		END
	END
	If @AdjType =3  -- Get all the records irrespective of AdjType
 
	BEGIN
	SELECT 
			FCSTLOG.LcID, FCSTLOG.Item
			, FCSTLOG.AdjustType
			, FCSTLOG.AdjustQty
 			, FCSTLOG.AdjustUserID, FCSTLOG.AdjustDesc, FCSTLOG.AdjustDateTime
 			, FCSTLOG.AdjustBegDate,FCSTLOG.AdjustEndDate
 		FROM ForecastRepository_Log FCSTLOG
 		WHERE
 			FCSTLOG.RepositoryKey = @RepositoryKey AND
 			FCSTLOG.IsPromoted = 0 AND -- False
 			FCSTLOG.LcID = CASE WHEN @LcID IS NOT NULL THEN @LcID ELSE FCSTLOG.LcID END AND
 			FCSTLOG.Item = CASE WHEN @Item IS NOT NULL THEN @Item ELSE FCSTLOG.Item END AND
 			FCSTLOG.AdjustBegDate Between @AdjustBegDate  AND CASE WHEN DATEDIFF(DAY, @AdjustBegDate, @AdjustEndDate) <= 0 THEN @AdjustBegDate ELSE @AdjustEndDate END
 		ORDER BY
 			FCSTLOG.LcID, 
 			FCSTLOG.Item,
 			FCSTLOG.FcstAdjustKey

	END

	ELSE
	BEGIN
	SELECT 
			FCSTLOG.LcID, FCSTLOG.Item
			, FCSTLOG.AdjustType
			, FCSTLOG.AdjustQty
 			, FCSTLOG.AdjustUserID, FCSTLOG.AdjustDesc, FCSTLOG.AdjustDateTime
 			, FCSTLOG.AdjustBegDate,FCSTLOG.AdjustEndDate
 		FROM ForecastRepository_Log FCSTLOG
 		WHERE
 			FCSTLOG.RepositoryKey = @RepositoryKey AND
 			FCSTLOG.IsPromoted = 0 AND -- False
 			FCSTLOG.LcID = CASE WHEN @LcID IS NOT NULL THEN @LcID ELSE FCSTLOG.LcID END AND
 			FCSTLOG.Item = CASE WHEN @Item IS NOT NULL THEN @Item ELSE FCSTLOG.Item END AND
			FCSTLOG.ADJUSTTYPE =@ADJTYPE --AND
 			--FCSTLOG.AdjustBegDate Between @AdjustBegDate  AND CASE WHEN DATEDIFF(DAY, @AdjustBegDate, @AdjustEndDate) <= 0 THEN @AdjustBegDate ELSE @AdjustEndDate END
 		ORDER BY
 			FCSTLOG.LcID, 
 			FCSTLOG.Item,
 			FCSTLOG.FcstAdjustKey
	END


        SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	RETURN 1
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
