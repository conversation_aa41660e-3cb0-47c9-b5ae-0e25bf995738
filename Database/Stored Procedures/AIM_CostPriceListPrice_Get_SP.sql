SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_CostPriceListPrice_Get_SP]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_CostPriceListPrice_Get_SP]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_CostPriceListPrice_Get_SP
**	Desc: Gets the Cost, Price, and List Price for an Item
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Cost, Price, List Price
**              
**	Auth:   Srinivas Uddanti
**	Date:   08/15/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**      ---------- ------------ -----------------------------------------------
**      08/16/2002 Wade Riza    Updated Return Codes and Error Handling
*******************************************************************************/

CREATE PROCEDURE AIM_CostPriceListPrice_Get_SP
(
      	@LcID      		nvarchar(12), 
      	@Item       		nvarchar(25),
      	@StartDate     		datetime,
      	@EndDate		datetime,
      	@Cost			decimal(10,4) OUTPUT,
      	@Price			decimal(10,4) OUTPUT,
      	@ListPrice		decimal(10,4) OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
    
DECLARE @found		int,
 	@StartCost         	decimal(10,4),
        @StartPrice       	decimal(10,4),
        @StartListPrice         decimal(10,4),
 	@AvgCost		decimal(10,4),
 	@AvgPrice		decimal(10,4),
 	@AvgListPrice     	decimal(10,4)

SET NOCOUNT ON

-- Validate the required parameters.
IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @StartDate IS NULL
BEGIN
	RETURN -1
END

IF @EndDate IS NULL
BEGIN
	RETURN -1
END
  
BEGIN
SET @Found = 0

SELECT TOP 1 @StartCost = COALESCE(Cost,0), @StartPrice = COALESCE(Price,0), 
  @StartListPrice = COALESCE(ListPrice,0)  
FROM ItemPricing 
WHERE EffectiveDateTime <= @StartDate AND
  LcID = @LcID and Item = @Item
  ORDER BY EffectiveDatetime DESC

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
 
SELECT @AvgCost = COALESCE(AVG(Cost),0), @AvgPrice = COALESCE(AVG(Price),0),
  @AvgListPrice = COALESCE(AVG(ListPrice),0)  
FROM ItemPricing 
WHERE EffectiveDatetime > @StartDate  AND
  EffectiveDatetime <= @EndDate AND
  LcID = @LcID AND
  Item = @Item

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

-- Average Cost Validation
IF @AvgCost = 0
BEGIN 
 	 SELECT @Cost = COALESCE(@StartCost,0)

    	-- Check for SQL Server errors.
    	IF @@ERROR <> 0 
    	BEGIN
   		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    	END
END 
ELSE
 	IF @AvgCost <> 0
   	BEGIN
  		SELECT @Cost = (COALESCE(@StartCost,0) + @AvgCost)/2.

  		-- Check for SQL Server errors.
    		IF @@ERROR <> 0 
    		BEGIN
   			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    		END
 	END
   
   	-- Average Price Validation
 
   	IF @AvgPrice = 0
   	BEGIN 
  		SELECT @Price = COALESCE(@StartPrice,0)
 
    		-- Check for SQL Server errors.
    		IF @@ERROR <> 0 
    		BEGIN
   			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    		END
   	END
   	ELSE
 	IF @AvgPrice <> 0
   	BEGIN
  		SELECT @Price= (COALESCE(@StartPrice,0) + @AvgPrice)/2.

    		-- Check for SQL Server errors.
    		IF @@ERROR <> 0 
    		BEGIN
   			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    		END
   	END
   
   	-- Average List Price Validation
   	IF @AvgListPrice =0
   	BEGIN 
  		SELECT @ListPrice = COALESCE(@StartListPrice,0)
 
    		-- Check for SQL Server errors.
    		IF @@ERROR <> 0 
    		BEGIN
   			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    		END
   	END
   	ELSE
 		IF @AvgListPrice <>0
   		BEGIN
  			SELECT @ListPrice = (COALESCE(@StartListPrice,0) + @AvgListPrice)/2.

    			-- Check for SQL Server errors.
    			IF @@ERROR <> 0 
    			BEGIN
   				RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    			END
   		END
  
 		-- Cost Validation
   		IF @Cost = 0 
   		BEGIN
  			SELECT @Cost = Cost 
			FROM ITEM 
			WHERE LcID = @LcID 
			AND Item = @Item
 
    			-- Check for SQL Server errors.
    			IF @@ERROR <> 0 
    			BEGIN
   				RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    			END
   		END

   		-- Price Validation
   		IF @Price = 0
   		BEGIN
  			SELECT @Price = Price 
			FROM Item 
			WHERE LcID = @LcID 
			AND Item = @Item

    			-- Check for SQL Server errors.
    			IF @@ERROR <> 0 
    			BEGIN
  				RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    			END
 		END

   		-- List Price Validation
   		IF @ListPrice = 0 
   		BEGIN
  			SELECT @ListPrice = ListPrice 
			FROM Item 
			WHERE LcID = @LcID 
			AND Item = @Item

    		-- Check for SQL Server errors.
    		IF @@ERROR <> 0 
    		BEGIN
   			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
    		END
   	END

   	IF @Cost IS NULL OR @PRICE IS NULL OR @LISTPRICE IS NULL
   	BEGIN
  		Return -1 -- ERROR (NO DATA FOUND)
 	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

