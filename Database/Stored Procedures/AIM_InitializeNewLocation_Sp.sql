SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InitializeNewLocation_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_InitializeNewLocation_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_InitializeNewLocation_Sp
**	Desc: Retrieves the Sequence Number from the flat file.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
** 	08/06/2003 Srinivas 	For the new location do not copy some field 
				like oh, oo, comstk, bkorder, bkcomstk 
**	09/12/2003 Srinivas 	Modified code for SubsItem
*******************************************************************************/
     
CREATE   PROCEDURE AIM_InitializeNewLocation_Sp 
(
     	@SourceLcId		     			nvarchar(12),	
     	@TargetLcid 					nvarchar(12),
    	@DmdScalingFactor				decimal(5,2),
    	@ScalingEffUntil				datetime,
    	@DeleteOpt		     			tinyint,
    	@DeleteHistoryOpt				tinyint
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE
   	-- Working Storage
   	@LcidKey					nvarchar(12),
   	@ItemKey					nvarchar(25),
    
	-- Item Table Elements
	@Lcid 						nvarchar(12),
	@Item 						nvarchar(25),
	@ItDesc 					nvarchar(30),
	@ItStat 					nvarchar(1),
	@ActDate 					datetime,
	@InActDate 					datetime,
	@OptionID 					nvarchar,
	@Class1 					nvarchar(50),
	@Class2 					nvarchar(50),
	@Class3 					nvarchar(50),
     	@Class4 					nvarchar(50),
     	@BinLocation 					nvarchar,
     	@BuyStrat 					nvarchar,
     	@VelCode 					nvarchar,
     	@VnId 						nvarchar,
     	@Assort 					nvarchar,
     	@ById 						nvarchar,
     	@MDC 						nvarchar,
     	@MDCFlag 					nvarchar,
     	@SaId 						nvarchar,
     	@PmId 						nvarchar,
     	@UPC 						nvarchar,
    	@Weight 					decimal(10, 4),
    	@Cube 						decimal(10, 4),
    	@ListPrice 					decimal(10, 4),
    	@Price 						decimal(10, 4),
    	@Cost 						decimal(10, 4),
    	@BkQty01 					int,
    	@BkCost01 					decimal(10, 4),
    	@BkQty02 					int,
    	@BkCost02 					decimal(10, 4),
    	@BkQty03 					int,
    	@BkCost03 					decimal(10, 4),
    	@BkQty04 					int,
    	@BkCost04 					decimal(10, 4),
    	@BkQty05 					int,
    	@BkCost05 					decimal(10, 4),
    	@BkQty06 					int,
    	@BkCost06 					decimal(10, 4),
    	@BkQty07 					int,
    	@BkCost07 					decimal(10, 4),
    	@BkQty08 					int,
    	@BkCost08 					decimal(10, 4),
    	@BkQty09 					int,
    	@BkCost09 					decimal(10, 4),
    	@BkQty10 					int,
    	@BkCost10 					decimal(10, 4),
     	@UOM 						nvarchar (6),
    	@ConvFactor 					int,
     	@BuyingUOM 					nvarchar (6),
    	@ReplenCost2 					decimal(9, 4),
    	@Oh 						int,
    	@Oo 						int,
    	@ComStk 					int,
    	@BkOrder 					int,
    	@BkComStk 					int,
    	@LeadTime 					smallint,
     	@PackRounding 					nvarchar (1),
    	@IMin 						int,
    	@IMax 						int,
    	@CStock 					int,
    	@SSAdj 						decimal(3, 2),
    	@UserMin 					int,
    	@UserMax 					int,
    	@UserMethod 					tinyint,	
    	@FcstMethod 					tinyint,
    	@FcstDemand 					decimal(10, 2),
    	@UserFcst 					decimal(10, 2),
    	@UserFcstExpDate 				datetime,
    	@MAE 						decimal(10, 2),
    	@MSE 						decimal(10, 2),
    	@Trend 						decimal(10, 3),
    	@FcstCycles 					int,
    	@ZeroCount 					int,
    	@Mean_NZ 					decimal(10, 2),
    	@StdDev_NZ 					decimal(10, 2),
    	@IntSafetyStock 				decimal(10, 2),
     	@IsIntermittent 				nvarchar (1),
     	@DIFlag 					nvarchar (1),
     	@DmdFilterFlag 					nvarchar (1),
     	@TrkSignalFlag 					nvarchar (1),
     	@UserDemandFlag 				nvarchar (1),
    	@ByPassPct 					decimal(4, 3),
    	@FcstUpdCyc 					int,
    	@LTVFact 					decimal(3, 2),
     	@PlnTT 						nvarchar (1),
     	@ZOPSw 						nvarchar (1),
     	@OUTLSw 					nvarchar (1),
     	@ZSStock 					nvarchar (1),    	@DSer 						decimal(4, 3),
     	@Freeze_BuyStrat 				nvarchar (1),
     	@Freeze_Byid 					nvarchar (1),
     	@Freeze_LeadTime 				nvarchar (1),
     	@Freeze_OptionID 				nvarchar (1),
     	@Freeze_DSer 					nvarchar (1),
     	@OldItem 					nvarchar (25),
    	@Accum_Lt 					smallint,
    	@ReviewTime 					smallint,
    	@OrderPt 					int,
    	@OrderQty 					int,
    	@SafetyStock 					decimal(10, 2),
    	@FcstRT 					decimal(10, 2),
    	@FcstLT 					decimal(10, 2),
    	@Fcst_Month 					decimal(10, 2),
    	@Fcst_Quarter 					decimal(10, 2),
    	@Fcst_Year 					decimal(10, 2),
    	@FcstDate 					smalldatetime,
     	@VC_Amt 					nvarchar (1),
    	@VC_Units_Ranking 				int,
    	@VC_Amt_Ranking 				int,
    	@VC_Date 					smalldatetime,
     	@VelCode_Prev 					nvarchar (1),
     	@VC_Amt_Prev 					nvarchar (1),
     	@OnPromotion 					nvarchar (1),
    	@AvgOh 						decimal(10, 2),
     	@NextPONbr_1 					nvarchar (12),
    	@NextPODate_1 					datetime,
    	@NextPOQty_1 					int,
     	@NextPONbr_2 					nvarchar (12),
    	@NextPODate_2 					datetime,
    	@NextPOQty_2 					int,
     	@NextPONbr_3 					nvarchar (12),
    	@NextPODate_3 					datetime,
    	@NextPOQty_3 					int,
     	@UserRef1 					nvarchar (12),
     	@UserRef2 					nvarchar (12),
     	@UserRef3 					nvarchar (12),

	-- Item History Elements
	@SubsItem					nvarchar(25),
    	@HisYear 					smallint,
    	@CPS01 						decimal(9, 1),
    	@CPS02 						decimal(9, 1),
    	@CPS03 						decimal(9, 1),
    	@CPS04 						decimal(9, 1),
    	@CPS05                                          decimal(9, 1),
    	@CPS06                                          decimal(9, 1),
    	@CPS07                                          decimal(9, 1),
    	@CPS08                                          decimal(9, 1), 
    	@CPS09                                          decimal(9, 1), 
    	@CPS10                                       	decimal(9, 1), 
    	@CPS11                                          decimal(9, 1), 
    	@CPS12                                          decimal(9, 1), 
    	@CPS13                                          decimal(9, 1), 
    	@CPS14                                          decimal(9, 1), 
    	@CPS15                                          decimal(9, 1), 
    	@CPS16                                          decimal(9, 1), 
    	@CPS17                                          decimal(9, 1), 
    	@CPS18                                          decimal(9, 1), 
    	@CPS19                                          decimal(9, 1), 
    	@CPS20                                          decimal(9, 1), 
    	@CPS21                                          decimal(9, 1), 
    	@CPS22                                          decimal(9, 1), 
    	@CPS23                                          decimal(9, 1), 
    	@CPS24                                          decimal(9, 1), 
    	@CPS25                                          decimal(9, 1), 
    	@CPS26                                          decimal(9, 1), 
    	@CPS27                                          decimal(9, 1), 
    	@CPS28                                          decimal(9, 1), 
    	@CPS29                                          decimal(9, 1), 
    	@CPS30                                          decimal(9, 1), 
    	@CPS31                                          decimal(9, 1), 
    	@CPS32                                          decimal(9, 1), 
    	@CPS33                                          decimal(9, 1), 
    	@CPS34                                          decimal(9, 1),
    	@CPS35                                          decimal(9, 1), 
    	@CPS36                                          decimal(9, 1), 
    	@CPS37                                          decimal(9, 1), 
    	@CPS38                                          decimal(9, 1), 
    	@CPS39                                          decimal(9, 1),     	
	@CPS40                                          decimal(9, 1), 
    	@CPS41                                          decimal(9, 1), 
    	@CPS42                                          decimal(9, 1), 
    	@CPS43                                          decimal(9, 1), 
    	@CPS44                                          decimal(9, 1), 
    	@CPS45                                          decimal(9, 1), 
    	@CPS46                                          decimal(9, 1), 
    	@CPS47                                          decimal(9, 1), 
    	@CPS48                                          decimal(9, 1), 
    	@CPS49                                          decimal(9, 1), 
    	@CPS50                                          decimal(9, 1), 
    	@CPS51                                          decimal(9, 1), 
    	@CPS52                                          decimal(9, 1), 
    	@QtyOrd01                                      	decimal(9, 1), 
    	@QtyOrd02                                       decimal(9, 1), 
    	@QtyOrd03                                       decimal(9, 1), 
    	@QtyOrd04                                       decimal(9, 1), 
    	@QtyOrd05                                       decimal(9, 1),
    	@QtyOrd06                                       decimal(9, 1), 
    	@QtyOrd07                                       decimal(9, 1), 
    	@QtyOrd08                                       decimal(9, 1), 
    	@QtyOrd09                                       decimal(9, 1), 
    	@QtyOrd10                                       decimal(9, 1), 
    	@QtyOrd11                                       decimal(9, 1), 
    	@QtyOrd12                                       decimal(9, 1), 
    	@QtyOrd13                                       decimal(9, 1), 
    	@QtyOrd14                                       decimal(9, 1), 
    	@QtyOrd15                                       decimal(9, 1), 
    	@QtyOrd16                                       decimal(9, 1), 
    	@QtyOrd17                                       decimal(9, 1), 
    	@QtyOrd18                                       decimal(9, 1), 
    	@QtyOrd19                                       decimal(9, 1), 
    	@QtyOrd20                                       decimal(9, 1), 
    	@QtyOrd21                                       decimal(9, 1), 
    	@QtyOrd22                                       decimal(9, 1), 
    	@QtyOrd23                                       decimal(9, 1), 
    	@QtyOrd24                                       decimal(9, 1), 
    	@QtyOrd25                                      	decimal(9, 1), 
    	@QtyOrd26                                       decimal(9, 1),
    	@QtyOrd27                                       decimal(9, 1),
    	@QtyOrd28                                       decimal(9, 1),
    	@QtyOrd29                                       decimal(9, 1),
    	@QtyOrd30                                       decimal(9, 1),
    	@QtyOrd31                                       decimal(9, 1),
    	@QtyOrd32                                       decimal(9, 1),
    	@QtyOrd33                                       decimal(9, 1),
    	@QtyOrd34                                       decimal(9, 1),
    	@QtyOrd35                                       decimal(9, 1),
    	@QtyOrd36                                       decimal(9, 1),
    	@QtyOrd37                                       decimal(9, 1),
    	@QtyOrd38                                       decimal(9, 1),
    	@QtyOrd39                                       decimal(9, 1),
    	@QtyOrd40                                       decimal(9, 1),
    	@QtyOrd41                                       decimal(9, 1),
    	@QtyOrd42                                       decimal(9, 1),
    	@QtyOrd43                                       decimal(9, 1),
    	@QtyOrd44                                       decimal(9, 1),
    	@QtyOrd45                                       decimal(9, 1),
    	@QtyOrd46                                       decimal(9, 1),
    	@QtyOrd47                                       decimal(9, 1),
    	@QtyOrd48                                       decimal(9, 1),
    	@QtyOrd49                                       decimal(9, 1),
    	@QtyOrd50                                       decimal(9, 1),
    	@QtyOrd51                                       decimal(9, 1), 
    	@QtyOrd52                                       decimal(9, 1), 
    	@OrdCnt01                                       int, 
    	@OrdCnt02                                      	int, 
    	@OrdCnt03                                       int, 
    	@OrdCnt04                                       int, 
    	@OrdCnt05                                      	int, 
    	@OrdCnt06                                       int,
    	@OrdCnt07                                       int,
    	@OrdCnt08                                       int,
    	@OrdCnt09                                       int,
    	@OrdCnt10                                       int,
    	@OrdCnt11                                       int,
    	@OrdCnt12                                       int,
    	@OrdCnt13                                       int,
    	@OrdCnt14                                       int,
    	@OrdCnt15                                       int,
    	@OrdCnt16                                       int,
    	@OrdCnt17                                       int,
    	@OrdCnt18                                       int,
    	@OrdCnt19                                       int,
    	@OrdCnt20                                       int,
    	@OrdCnt21                                       int,
    	@OrdCnt22                                       int,
    	@OrdCnt23                                       int,
    	@OrdCnt24                                       int,
    	@OrdCnt25                                       int,
    	@OrdCnt26                                       int,
    	@OrdCnt27                                       int,
    	@OrdCnt28                                       int,
    	@OrdCnt29                                       int,
    	@OrdCnt30                                       int,
    	@OrdCnt31                                       int,
    	@OrdCnt32                                       int,
    	@OrdCnt33                                       int,
    	@OrdCnt34                                       int,
    	@OrdCnt35                                       int,
    	@OrdCnt36                                       int,
    	@OrdCnt37                                       int, 
    	@OrdCnt38                                       int,
    	@OrdCnt39                                       int,
    	@OrdCnt40                                       int,
    	@OrdCnt41                                       int,
    	@OrdCnt42                                       int,
    	@OrdCnt43                                       int,
    	@OrdCnt44                                       int,
    	@OrdCnt45                                       int,
    	@OrdCnt46                                       int,
    	@OrdCnt47                                       int,
    	@OrdCnt48                                       int,
    	@OrdCnt49                                       int,
    	@OrdCnt50                                       int,
    	@OrdCnt51                                       int,
    	@OrdCnt52                                       int

SET NOCOUNT ON

-- Delete records at Target Location -- IF elected
IF @DeleteOpt = 1
BEGIN
    	DELETE item 
	WHERE lcid = @TargetLcId
END

Select @Oh =0
Select @OO =0
select @ComStk =0
select @BkOrder =0
select @BKComStk =0


IF @DeleteHistoryOpt = 1
BEGIN
	DELETE itemhistory 
	WHERE lcid = @TargetLcId
END

--Create temp table #TempItemHistory
select ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	 ItemHistory.CPS01, 
      	ItemHistory.CPS02, ItemHistory.CPS03, ItemHistory.CPS04, 
       	ItemHistory.CPS05, ItemHistory.CPS06, ItemHistory.CPS07, 
      	ItemHistory.CPS08, ItemHistory.CPS09, ItemHistory.CPS10, 
       	ItemHistory.CPS11, ItemHistory.CPS12, ItemHistory.CPS13, 
       	ItemHistory.CPS14, ItemHistory.CPS15, ItemHistory.CPS16, 
       	ItemHistory.CPS17, ItemHistory.CPS18, ItemHistory.CPS19, 
       	ItemHistory.CPS20, ItemHistory.CPS21, ItemHistory.CPS22, 
       	ItemHistory.CPS23, ItemHistory.CPS24, ItemHistory.CPS25, 
       	ItemHistory.CPS26, ItemHistory.CPS27, ItemHistory.CPS28, 
       	ItemHistory.CPS29, ItemHistory.CPS30, ItemHistory.CPS31, 
       	ItemHistory.CPS32, ItemHistory.CPS33, ItemHistory.CPS34, 
       	ItemHistory.CPS35, ItemHistory.CPS36, ItemHistory.CPS37, 
       	ItemHistory.CPS38, ItemHistory.CPS39, ItemHistory.CPS40, 
      	ItemHistory.CPS41, ItemHistory.CPS42, ItemHistory.CPS43, 
       	ItemHistory.CPS44, ItemHistory.CPS45, ItemHistory.CPS46, 
      	ItemHistory.CPS47, ItemHistory.CPS48, ItemHistory.CPS49, 
       	ItemHistory.CPS50, ItemHistory.CPS51, ItemHistory.CPS52
	into #TempItemHistory 
	from itemhistory
	where item ='XXXXX'
-- Build/Update the Item Table
DECLARE ItemCursor CURSOR LOCAL FAST_FORWARD FOR 
SELECT Lcid, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, Class3, 
  	Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, MDCFlag, SaId, PmId, 
    	UPC, Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, 
    	BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, 
    	BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, 
    	ReplenCost2, LeadTime, PackRounding, IMin, IMax, CStock, 
    	SSAdj, UserMin, UserMax, UserMethod, FcstMethod, FcstDemand, UserFcst, UserFcstExpDate, 
    	MAE, MSE, Trend, FcstCycles, ZeroCount, Mean_NZ, StdDev_NZ, IntSafetyStock, IsIntermittent, 
    	DIFlag, DmdFilterFlag, TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, PlnTT, 
    	ZOPSw, OUTLSw, ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, 
    	Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, 
    	Fcst_Month, Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, VC_Amt_Ranking, 
    	VC_Date, VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, NextPOQty_1, 
    	NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, UserRef1, 
    	UserRef2, UserRef3 
FROM Item
WHERE LcId = @SourceLcId
ORDER BY LcId, Item
  
OPEN ItemCursor
    	
FETCH NEXT FROM ItemCursor INTO
  	@Lcid, @Item, @ItDesc, @ItStat, @ActDate, @InActDate, @OptionID, @Class1, @Class2, @Class3, 
  	@Class4, @BinLocation, @BuyStrat, @VelCode, @VnId, @Assort, @ById, @MDC, @MDCFlag, @SaId, @PmId, 
  	@UPC, @Weight, @Cube, @ListPrice, @Price, @Cost, @BkQty01, @BkCost01, @BkQty02, @BkCost02, @BkQty03,
  	@BkCost03, @BkQty04, @BkCost04, @BkQty05, @BkCost05, @BkQty06, @BkCost06, @BkQty07, @BkCost07, 
  	@BkQty08, @BkCost08, @BkQty09, @BkCost09, @BkQty10, @BkCost10, @UOM, @ConvFactor, @BuyingUOM, 
  	@ReplenCost2, @LeadTime, @PackRounding, @IMin, @IMax, @CStock, 
  	@SSAdj, @UserMin, @UserMax, @UserMethod, @FcstMethod, @FcstDemand, @UserFcst, @UserFcstExpDate, 
  	@MAE, @MSE, @Trend, @FcstCycles, @ZeroCount, @Mean_NZ, @StdDev_NZ, @IntSafetyStock, @IsIntermittent, 
  	@DIFlag, @DmdFilterFlag, @TrkSignalFlag, @UserDemandFlag, @ByPassPct, @FcstUpdCyc, @LTVFact, @PlnTT, 
  	@ZOPSw, @OUTLSw, @ZSStock, @DSer, @Freeze_BuyStrat, @Freeze_Byid, @Freeze_LeadTime, @Freeze_OptionID,
  	@Freeze_DSer, @OldItem, @Accum_Lt, @ReviewTime, @OrderPt, @OrderQty, @SafetyStock, @FcstRT, @FcstLT, 
  	@Fcst_Month, @Fcst_Quarter, @Fcst_Year, @FcstDate, @VC_Amt, @VC_Units_Ranking, @VC_Amt_Ranking, 
  	@VC_Date, @VelCode_Prev, @VC_Amt_Prev, @OnPromotion, @AvgOh, @NextPONbr_1, @NextPODate_1, @NextPOQty_1, 
  	@NextPONbr_2, @NextPODate_2, @NextPOQty_2, @NextPONbr_3, @NextPODate_3, @NextPOQty_3, @UserRef1, 
  	@UserRef2, @UserRef3 
    		
WHILE @@FETCH_STATUS = 0
BEGIN
  	-- Does the Item Exist ? 
  	SELECT @LcIdKey = LcId, @ItemKey = Item 
	FROM Item
    	WHERE LcId = @TargetLcId 
        AND Item = @Item
    	
	IF @@rowcount = 0		-- Not Found
    	BEGIN
		INSERT INTO Item(Lcid, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, 
    			Class3, Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, MDCFlag, 
    			SaId, PmId, UPC, Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, 
    			BkCost02, BkQty03, BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, 
    			BkQty07, BkCost07, BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, 
    			ConvFactor, BuyingUOM, ReplenCost2, Oh, Oo, ComStk, BkOrder, BkComStk, LeadTime, 
    			PackRounding, IMin, IMax, CStock, SSAdj, UserMin, UserMax, UserMethod, FcstMethod, 
    			FcstDemand, UserFcst, UserFcstExpDate, MAE, MSE, Trend, FcstCycles, ZeroCount, Mean_NZ, 
    			StdDev_NZ, IntSafetyStock, IsIntermittent, DIFlag, DmdFilterFlag, TrkSignalFlag,     			UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, PlnTT, ZOPSw, OUTLSw, ZSStock, DSer, 
    			Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, Freeze_DSer, OldItem, 
    			Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, Fcst_Month, 
    			Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, VC_Amt_Ranking, VC_Date, 
    			VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, NextPOQty_1, 
    			NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, 
    			UserRef1, UserRef2, UserRef3)
    		VALUES(@TargetLcId, @Item, @ItDesc, @ItStat, @ActDate, @InActDate, @OptionID, @Class1, @Class2,
    			@Class3, @Class4, @BinLocation, @BuyStrat, @VelCode, @VnId, @Assort, @ById, @MDC, @MDCFlag, 
    			@SaId, @PmId, @UPC, @Weight, @Cube, @ListPrice, @Price, @Cost, @BkQty01, @BkCost01, @BkQty02, 
    			@BkCost02, @BkQty03, @BkCost03, @BkQty04, @BkCost04, @BkQty05, @BkCost05, @BkQty06, @BkCost06, 
    			@BkQty07, @BkCost07, @BkQty08, @BkCost08, @BkQty09, @BkCost09, @BkQty10, @BkCost10, @UOM, 
    			@ConvFactor, @BuyingUOM, @ReplenCost2, @Oh, @Oo, @ComStk, @BkOrder, @BkComStk, @LeadTime, 
    			@PackRounding, @IMin, @IMax, @CStock, @SSAdj, @UserMin, @UserMax, @UserMethod, @FcstMethod, 
    			@FcstDemand, @UserFcst, @UserFcstExpDate, @MAE, @MSE, @Trend, @FcstCycles, @ZeroCount, @Mean_NZ, 
    			@StdDev_NZ, @IntSafetyStock, @IsIntermittent, @DIFlag, @DmdFilterFlag, @TrkSignalFlag, 
    			@UserDemandFlag, @ByPassPct, @FcstUpdCyc, @LTVFact, @PlnTT, @ZOPSw, @OUTLSw, @ZSStock, @DSer, 
    			@Freeze_BuyStrat, @Freeze_Byid, @Freeze_LeadTime, @Freeze_OptionID, @Freeze_DSer, @OldItem, 
    			@Accum_Lt, @ReviewTime, @OrderPt, @OrderQty, @SafetyStock, @FcstRT, @FcstLT, @Fcst_Month, 
    			@Fcst_Quarter, @Fcst_Year, @FcstDate, @VC_Amt, @VC_Units_Ranking, @VC_Amt_Ranking, @VC_Date, 
    			@VelCode_Prev, @VC_Amt_Prev, @OnPromotion, @AvgOh, @NextPONbr_1, @NextPODate_1, @NextPOQty_1, 
    			@NextPONbr_2, @NextPODate_2, @NextPOQty_2, @NextPONbr_3, @NextPODate_3, @NextPOQty_3, 
    			@UserRef1, @UserRef2, @UserRef3)				
    	END
    	ELSE
    	BEGIN
    		UPDATE Item
    		SET ItDesc=@ItDesc, ItStat=@ItStat, ActDate=@ActDate, 
    			InActDate=@InActDate, OptionID=@OptionID, Class1=@Class1, Class2=@Class2, 
    			Class3=@Class3, Class4=@Class4, BinLocation=@BinLocation, BuyStrat=@BuyStrat, 
    			VelCode=@VelCode, VnId=@VnId, Assort=@Assort, ById=@ById, MDC=@MDC, MDCFlag=@MDCFlag, 
    			SaId=@SaId, PmId=@PmId, UPC=@UPC, Weight=@Weight, Cube=@Cube, ListPrice=@ListPrice, 
    			Price=@Price, Cost=@Cost, BkQty01=@BkQty01, BkCost01=@BkCost01, BkQty02=@BkQty02, 
    			BkCost02=@BkCost02, BkQty03=@BkQty03, BkCost03=@BkCost03, BkQty04=@BkQty04, 
    			BkCost04=@BkCost04, BkQty05=@BkQty05, BkCost05=@BkCost05, BkQty06=@BkQty06, 
    			BkCost06=@BkCost06, BkQty07=@BkQty07, BkCost07=@BkCost07, BkQty08=@BkQty08, 
    			BkCost08=@BkCost08, BkQty09=@BkQty09, BkCost09=@BkCost09, BkQty10=@BkQty10, 
    			BkCost10=@BkCost10, UOM=@UOM, ConvFactor=@ConvFactor, BuyingUOM=@BuyingUOM, 
    			ReplenCost2=@ReplenCost2, Oh=@Oh, Oo=@Oo, ComStk=@ComStk, BkOrder=@BkOrder, 
    			BkComStk=@BkComStk, LeadTime=@LeadTime, PackRounding=@PackRounding, IMin=@IMin, 
    			IMax=@IMax, CStock=@CStock, SSAdj=@SSAdj, UserMin=@UserMin, UserMax=@UserMax, 
    			UserMethod=@UserMethod, FcstMethod=@FcstMethod, FcstDemand=@FcstDemand, 
    			UserFcst=@UserFcst, UserFcstExpDate=@UserFcstExpDate, MAE=@MAE, MSE=@MSE, 
    			Trend=@Trend, FcstCycles=@FcstCycles, ZeroCount=@ZeroCount, Mean_NZ=@Mean_NZ, 
    			StdDev_NZ=@StdDev_NZ, IntSafetyStock=@IntSafetyStock, IsIntermittent=@IsIntermittent, 
    			DIFlag=@DIFlag, DmdFilterFlag=@DmdFilterFlag, TrkSignalFlag=@TrkSignalFlag, 
    			UserDemandFlag=@UserDemandFlag, ByPassPct=@ByPassPct, FcstUpdCyc=@FcstUpdCyc, 
    			LTVFact=@LTVFact, PlnTT=@PlnTT, ZOPSw=@ZOPSw, OUTLSw=@OUTLSw, ZSStock=@ZSStock, 
    			DSer=@DSer, Freeze_BuyStrat=@Freeze_BuyStrat, Freeze_Byid=@Freeze_Byid, 
    			Freeze_LeadTime=@Freeze_LeadTime, Freeze_OptionID=@Freeze_OptionID, 
    			Freeze_DSer=@Freeze_DSer, OldItem=@OldItem, Accum_Lt=@Accum_Lt, ReviewTime=@ReviewTime, 
    			OrderPt=@OrderPt, OrderQty=@OrderQty, SafetyStock=@SafetyStock, FcstRT=@FcstRT, 
    			FcstLT=@FcstLT, Fcst_Month=@Fcst_Month, Fcst_Quarter=@Fcst_Quarter, 
    			Fcst_Year=@Fcst_Year, FcstDate=@FcstDate, VC_Amt=@VC_Amt, 
    			VC_Units_Ranking=@VC_Units_Ranking, VC_Amt_Ranking=@VC_Amt_Ranking, 
    			VC_Date=@VC_Date, VelCode_Prev=@VelCode_Prev, VC_Amt_Prev=@VC_Amt_Prev, 
    			OnPromotion=@OnPromotion, AvgOh=@AvgOh, NextPONbr_1=@NextPONbr_1, 
    			NextPODate_1=@NextPODate_1, NextPOQty_1=@NextPOQty_1, NextPONbr_2=@NextPONbr_2, 
    			NextPODate_2=@NextPODate_2, NextPOQty_2=@NextPOQty_2, NextPONbr_3=@NextPONbr_3, 
    			NextPODate_3=@NextPODate_3, NextPOQty_3=@NextPOQty_3, UserRef1=@UserRef1, 
    			UserRef2=@UserRef2, UserRef3=@UserRef3
    		WHERE LcId = @TargetLcId and Item = @Item
	END

	-- Get the next record
	FETCH NEXT FROM ItemCursor INTO
		@Lcid, @Item, @ItDesc, @ItStat, @ActDate, @InActDate, @OptionID, @Class1, @Class2, @Class3, 
    		@Class4, @BinLocation, @BuyStrat, @VelCode, @VnId, @Assort, @ById, @MDC, @MDCFlag, @SaId, @PmId, 
    		@UPC, @Weight, @Cube, @ListPrice, @Price, @Cost, @BkQty01, @BkCost01, @BkQty02, @BkCost02, @BkQty03,
    		@BkCost03, @BkQty04, @BkCost04, @BkQty05, @BkCost05, @BkQty06, @BkCost06, @BkQty07, @BkCost07, 
    		@BkQty08, @BkCost08, @BkQty09, @BkCost09, @BkQty10, @BkCost10, @UOM, @ConvFactor, @BuyingUOM, 
    		@ReplenCost2, @LeadTime, @PackRounding, @IMin, @IMax, @CStock, 
    		@SSAdj, @UserMin, @UserMax, @UserMethod, @FcstMethod, @FcstDemand, @UserFcst, @UserFcstExpDate, 
    		@MAE, @MSE, @Trend, @FcstCycles, @ZeroCount, @Mean_NZ, @StdDev_NZ, @IntSafetyStock, @IsIntermittent, 
    		@DIFlag, @DmdFilterFlag, @TrkSignalFlag, @UserDemandFlag, @ByPassPct, @FcstUpdCyc, @LTVFact, @PlnTT, 
    		@ZOPSw, @OUTLSw, @ZSStock, @DSer, @Freeze_BuyStrat, @Freeze_Byid, @Freeze_LeadTime, @Freeze_OptionID,
    		@Freeze_DSer, @OldItem, @Accum_Lt, @ReviewTime, @OrderPt, @OrderQty, @SafetyStock, @FcstRT, @FcstLT, 
    		@Fcst_Month, @Fcst_Quarter, @Fcst_Year, @FcstDate, @VC_Amt, @VC_Units_Ranking, @VC_Amt_Ranking, 
    		@VC_Date, @VelCode_Prev, @VC_Amt_Prev, @OnPromotion, @AvgOh, @NextPONbr_1, @NextPODate_1, @NextPOQty_1, 
    		@NextPONbr_2, @NextPODate_2, @NextPOQty_2, @NextPONbr_3, @NextPODate_3, @NextPOQty_3, @UserRef1, 
    		@UserRef2, @UserRef3 
    	END
    	CLOSE ItemCursor
    	DEALLOCATE ItemCursor

    	-- Build/Update the Item History Table
	-- Get only items in itemhistory where item =subsitem i.e Do not copy subsitems
    	DECLARE HsCursor CURSOR LOCAL FAST_FORWARD FOR 
    		SELECT LcId, Item,SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, 
    			CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, 
    			CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, 
    			CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, 
    			CPS49, CPS50, CPS51, CPS52, 
    			QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, QtyOrd09, 
    			QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, 
    			QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, QtyOrd27, 
    			QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, 
    			QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, QtyOrd45, 
    			QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, OrdCnt02, 
    			OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, OrdCnt11, 
    			OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, OrdCnt20, 
    			OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, 
    			OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, OrdCnt38, 
    			OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, 
    			OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52 
    		FROM ItemHistory
    		WHERE LcId = @SourceLcId
		and Item =SubsItem
    		ORDER BY LcId, Item, HisYear

    	OPEN HsCursor
    	
    	FETCH NEXT FROM HsCursor INTO
	    	@LcId, @Item,@SubsItem, @HisYear, @CPS01, @CPS02, @CPS03, @CPS04, @CPS05, @CPS06, @CPS07, @CPS08, @CPS09, 
    		@CPS10, @CPS11, @CPS12, @CPS13, @CPS14, @CPS15, @CPS16, @CPS17, @CPS18, @CPS19, @CPS20, @CPS21, @CPS22,  
    		@CPS23, @CPS24, @CPS25, @CPS26, @CPS27, @CPS28, @CPS29, @CPS30, @CPS31, @CPS32, @CPS33, @CPS34, @CPS35, 
    		@CPS36, @CPS37, @CPS38, @CPS39, @CPS40, @CPS41, @CPS42, @CPS43, @CPS44, @CPS45, @CPS46, @CPS47, @CPS48, 
    		@CPS49, @CPS50, @CPS51, @CPS52,  
    		@QtyOrd01, @QtyOrd02, @QtyOrd03, @QtyOrd04, @QtyOrd05, @QtyOrd06, @QtyOrd07, @QtyOrd08, @QtyOrd09, 
    		@QtyOrd10, @QtyOrd11, @QtyOrd12, @QtyOrd13, @QtyOrd14, @QtyOrd15, @QtyOrd16, @QtyOrd17, @QtyOrd18, 
    		@QtyOrd19, @QtyOrd20, @QtyOrd21, @QtyOrd22, @QtyOrd23, @QtyOrd24, @QtyOrd25, @QtyOrd26, @QtyOrd27, 
    		@QtyOrd28, @QtyOrd29, @QtyOrd30, @QtyOrd31, @QtyOrd32, @QtyOrd33, @QtyOrd34, @QtyOrd35, @QtyOrd36, 
    		@QtyOrd37, @QtyOrd38, @QtyOrd39, @QtyOrd40, @QtyOrd41, @QtyOrd42, @QtyOrd43, @QtyOrd44, @QtyOrd45, 
    		@QtyOrd46, @QtyOrd47, @QtyOrd48, @QtyOrd49, @QtyOrd50, @QtyOrd51, @QtyOrd52, @OrdCnt01, @OrdCnt02, 
    		@OrdCnt03, @OrdCnt04, @OrdCnt05, @OrdCnt06, @OrdCnt07, @OrdCnt08, @OrdCnt09, @OrdCnt10, @OrdCnt11, 
    		@OrdCnt12, @OrdCnt13, @OrdCnt14, @OrdCnt15, @OrdCnt16, @OrdCnt17, @OrdCnt18, @OrdCnt19, @OrdCnt20, 
    		@OrdCnt21, @OrdCnt22, @OrdCnt23, @OrdCnt24, @OrdCnt25, @OrdCnt26, @OrdCnt27, @OrdCnt28, @OrdCnt29, 
    		@OrdCnt30, @OrdCnt31, @OrdCnt32, @OrdCnt33, @OrdCnt34, @OrdCnt35, @OrdCnt36, @OrdCnt37, @OrdCnt38, 
    		@OrdCnt39, @OrdCnt40, @OrdCnt41, @OrdCnt42, @OrdCnt43, @OrdCnt44, @OrdCnt45, @OrdCnt46, @OrdCnt47, 
    		@OrdCnt48, @OrdCnt49, @OrdCnt50, @OrdCnt51, @OrdCnt52	
    	WHILE @@FETCH_STATUS = 0
    	BEGIN
    		-- Does the Item History Record Exist ? 
    		SELECT @LcIdKey = LcId, @ItemKey = Item 
		FROM ItemHistory
    		WHERE LcId = @TargetLcId 
		AND Item = @Item 
		AND SubsItem =@Item
		AND HisYear = @HisYear
    		
		IF @@rowcount = 0		-- Not Found
    		BEGIN
    			INSERT INTO ItemHistory(LcId, Item,SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, 
    				CPS08, CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, 
    				CPS21, CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, 
    				CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, 
    				CPS47, CPS48, CPS49, CPS50, CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, 
    				QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, 
    				QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, 
    				QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, 
    				QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, 
    				QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, 
    				QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, 
    				OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, 
    				OrdCnt09, OrdCnt10, OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, 
    				OrdCnt17, OrdCnt18, OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, 
    				OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, 
    				OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, 
    				OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, 
    				OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52)
    			VALUES(@TargetLcId, @Item,@SubsItem, @HisYear, @CPS01, @CPS02, @CPS03, @CPS04, @CPS05, @CPS06, 
    				@CPS07, @CPS08, @CPS09, @CPS10, @CPS11, @CPS12, @CPS13, @CPS14, @CPS15, @CPS16, @CPS17, 
    				@CPS18, @CPS19, @CPS20, @CPS21, @CPS22, @CPS23, @CPS24, @CPS25, @CPS26, @CPS27, @CPS28, 
    				@CPS29, @CPS30, @CPS31, @CPS32, @CPS33, @CPS34, @CPS35, @CPS36, @CPS37, @CPS38, @CPS39, 
    				@CPS40, @CPS41, @CPS42, @CPS43, @CPS44, @CPS45, @CPS46, @CPS47, @CPS48, @CPS49, @CPS50, 
    				@CPS51, @CPS52, @QtyOrd01, @QtyOrd02, @QtyOrd03, @QtyOrd04, @QtyOrd05, @QtyOrd06, 
    				@QtyOrd07, @QtyOrd08, @QtyOrd09, @QtyOrd10, @QtyOrd11, @QtyOrd12, @QtyOrd13, @QtyOrd14, 
    				@QtyOrd15, @QtyOrd16, @QtyOrd17, @QtyOrd18, @QtyOrd19, @QtyOrd20, @QtyOrd21, @QtyOrd22, 
    				@QtyOrd23, @QtyOrd24, @QtyOrd25, @QtyOrd26, @QtyOrd27, @QtyOrd28, @QtyOrd29, @QtyOrd30, 
    				@QtyOrd31, @QtyOrd32, @QtyOrd33, @QtyOrd34, @QtyOrd35, @QtyOrd36, @QtyOrd37, @QtyOrd38, 
    				@QtyOrd39, @QtyOrd40, @QtyOrd41, @QtyOrd42, @QtyOrd43, @QtyOrd44, @QtyOrd45, @QtyOrd46, 
    				@QtyOrd47, @QtyOrd48, @QtyOrd49, @QtyOrd50, @QtyOrd51, @QtyOrd52, @OrdCnt01, @OrdCnt02, 
    				@OrdCnt03, @OrdCnt04, @OrdCnt05, @OrdCnt06, @OrdCnt07, @OrdCnt08, @OrdCnt09, @OrdCnt10, 
    				@OrdCnt11, @OrdCnt12, @OrdCnt13, @OrdCnt14, @OrdCnt15, @OrdCnt16, @OrdCnt17, @OrdCnt18, 
    				@OrdCnt19, @OrdCnt20, @OrdCnt21, @OrdCnt22, @OrdCnt23, @OrdCnt24, @OrdCnt25, @OrdCnt26, 
    				@OrdCnt27, @OrdCnt28, @OrdCnt29, @OrdCnt30, @OrdCnt31, @OrdCnt32, @OrdCnt33, @OrdCnt34, 
    				@OrdCnt35, @OrdCnt36, @OrdCnt37, @OrdCnt38, @OrdCnt39, @OrdCnt40, @OrdCnt41, @OrdCnt42, 
    				@OrdCnt43, @OrdCnt44, @OrdCnt45, @OrdCnt46, @OrdCnt47, @OrdCnt48, @OrdCnt49, @OrdCnt50, 
    				@OrdCnt51, @OrdCnt52)
    		END
    		ELSE
    		BEGIN
    			UPDATE ItemHistory
    			SET CPS01=@CPS01, CPS02=@CPS02, CPS03=@CPS03, 
    				CPS04=@CPS04, CPS05=@CPS05, CPS06=@CPS06, CPS07=@CPS07, CPS08=@CPS08, CPS09=@CPS09, 
    				CPS10=@CPS10, CPS11=@CPS11, CPS12=@CPS12, CPS13=@CPS13, CPS14=@CPS14, CPS15=@CPS15, 
    				CPS16=@CPS16, CPS17=@CPS17, CPS18=@CPS18, CPS19=@CPS19, CPS20=@CPS20, CPS21=@CPS21, 
    				CPS22=@CPS22, CPS23=@CPS23, CPS24=@CPS24, CPS25=@CPS25, CPS26=@CPS26, CPS27=@CPS27, 
    				CPS28=@CPS28, CPS29=@CPS29, CPS30=@CPS30, CPS31=@CPS31, CPS32=@CPS32, CPS33=@CPS33, 
    				CPS34=@CPS34, CPS35=@CPS35, CPS36=@CPS36, CPS37=@CPS37, CPS38=@CPS38, CPS39=@CPS39, 
    				CPS40=@CPS40, CPS41=@CPS41, CPS42=@CPS42, CPS43=@CPS43, CPS44=@CPS44, CPS45=@CPS45, 
    				CPS46=@CPS46, CPS47=@CPS47, CPS48=@CPS48, CPS49=@CPS49, CPS50=@CPS50, CPS51=@CPS51, 
    				CPS52=@CPS52, 
    				QtyOrd01=@QtyOrd01, QtyOrd02=@QtyOrd02, QtyOrd03=@QtyOrd03, QtyOrd04=@QtyOrd04, 
    				QtyOrd05=@QtyOrd05, QtyOrd06=@QtyOrd06, QtyOrd07=@QtyOrd07, QtyOrd08=@QtyOrd08, 
    				QtyOrd09=@QtyOrd09, QtyOrd10=@QtyOrd10, QtyOrd11=@QtyOrd11, QtyOrd12=@QtyOrd12, 
    				QtyOrd13=@QtyOrd13, QtyOrd14=@QtyOrd14, QtyOrd15=@QtyOrd15, QtyOrd16=@QtyOrd16, 
    				QtyOrd17=@QtyOrd17, QtyOrd18=@QtyOrd18, QtyOrd19=@QtyOrd19, QtyOrd20=@QtyOrd20, 
    				QtyOrd21=@QtyOrd21, QtyOrd22=@QtyOrd22, QtyOrd23=@QtyOrd23, QtyOrd24=@QtyOrd24, 
    				QtyOrd25=@QtyOrd25, QtyOrd26=@QtyOrd26, QtyOrd27=@QtyOrd27, QtyOrd28=@QtyOrd28, 
    				QtyOrd29=@QtyOrd29, QtyOrd30=@QtyOrd30, QtyOrd31=@QtyOrd31, QtyOrd32=@QtyOrd32, 
    				QtyOrd33=@QtyOrd33, QtyOrd34=@QtyOrd34, QtyOrd35=@QtyOrd35, QtyOrd36=@QtyOrd36, 
    				QtyOrd37=@QtyOrd37, QtyOrd38=@QtyOrd38, QtyOrd39=@QtyOrd39, QtyOrd40=@QtyOrd40, 
    				QtyOrd41=@QtyOrd41, QtyOrd42=@QtyOrd42, QtyOrd43=@QtyOrd43, QtyOrd44=@QtyOrd44, 
    				QtyOrd45=@QtyOrd45, QtyOrd46=@QtyOrd46, QtyOrd47=@QtyOrd47, QtyOrd48=@QtyOrd48, 
    				QtyOrd49=@QtyOrd49, QtyOrd50=@QtyOrd50, QtyOrd51=@QtyOrd51, QtyOrd52=@QtyOrd52, 
    				OrdCnt01=@OrdCnt01, OrdCnt02=@OrdCnt02, OrdCnt03=@OrdCnt03, OrdCnt04=@OrdCnt04, 
    				OrdCnt05=@OrdCnt05, OrdCnt06=@OrdCnt06, OrdCnt07=@OrdCnt07, OrdCnt08=@OrdCnt08, 
    				OrdCnt09=@OrdCnt09, OrdCnt10=@OrdCnt10, OrdCnt11=@OrdCnt11, OrdCnt12=@OrdCnt12, 
    				OrdCnt13=@OrdCnt13, OrdCnt14=@OrdCnt14, OrdCnt15=@OrdCnt15, OrdCnt16=@OrdCnt16, 
    				OrdCnt17=@OrdCnt17, OrdCnt18=@OrdCnt18, OrdCnt19=@OrdCnt19, OrdCnt20=@OrdCnt20, 
    				OrdCnt21=@OrdCnt21, OrdCnt22=@OrdCnt22, OrdCnt23=@OrdCnt23, OrdCnt24=@OrdCnt24, 
    				OrdCnt25=@OrdCnt25, OrdCnt26=@OrdCnt26, OrdCnt27=@OrdCnt27, OrdCnt28=@OrdCnt28, 
    				OrdCnt29=@OrdCnt29, OrdCnt30=@OrdCnt30, OrdCnt31=@OrdCnt31, OrdCnt32=@OrdCnt32, 
    				OrdCnt33=@OrdCnt33, OrdCnt34=@OrdCnt34, OrdCnt35=@OrdCnt35, OrdCnt36=@OrdCnt36, 
    				OrdCnt37=@OrdCnt37, OrdCnt38=@OrdCnt38, OrdCnt39=@OrdCnt39, OrdCnt40=@OrdCnt40, 
    				OrdCnt41=@OrdCnt41, OrdCnt42=@OrdCnt42, OrdCnt43=@OrdCnt43, OrdCnt44=@OrdCnt44, 
    				OrdCnt45=@OrdCnt45, OrdCnt46=@OrdCnt46, OrdCnt47=@OrdCnt47, OrdCnt48=@OrdCnt48, 
    				OrdCnt49=@OrdCnt49, OrdCnt50=@OrdCnt50, OrdCnt51=@OrdCnt51, OrdCnt52=@OrdCnt52
    			WHERE LcId = @TargetLcId 
    			AND Item = @Item
			AND SubsItem =@Item
    			AND HisYear = @HisYear

	--Get sum of the cps values for main and subitems
	INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	From ItemHistory 
	WHERE LcId = @SourceLcId 
    	AND Item = @Item
    	AND HisYear = @HisYear
	Group by ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear
      	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc

	update ItemHistory 
	set ItemHistory.CPS01 =#TempItemHistory.cps01,ItemHistory.CPS02 =#TempItemHistory.cps02,
	    ItemHistory.CPS03 =#TempItemHistory.cps03,ItemHistory.CPS04 =#TempItemHistory.cps04,
	    ItemHistory.CPS05 =#TempItemHistory.cps05,ItemHistory.CPS06 =#TempItemHistory.cps06,
	    ItemHistory.CPS07 =#TempItemHistory.cps07,ItemHistory.CPS08 =#TempItemHistory.cps08,
	    ItemHistory.CPS09 =#TempItemHistory.cps09,ItemHistory.CPS10 =#TempItemHistory.cps10,
	    ItemHistory.CPS11 =#TempItemHistory.cps11,ItemHistory.CPS12 =#TempItemHistory.cps12,
	    ItemHistory.CPS13 =#TempItemHistory.cps13,ItemHistory.CPS14 =#TempItemHistory.cps14,
	    ItemHistory.CPS15 =#TempItemHistory.cps15,ItemHistory.CPS16 =#TempItemHistory.cps16,
            ItemHistory.CPS17 =#TempItemHistory.cps17,ItemHistory.CPS18 =#TempItemHistory.cps18,
	    ItemHistory.CPS19 =#TempItemHistory.cps19,ItemHistory.CPS20 =#TempItemHistory.cps20,
	    ItemHistory.CPS21 =#TempItemHistory.cps21,ItemHistory.CPS22 =#TempItemHistory.cps22,
	    ItemHistory.CPS23 =#TempItemHistory.cps23,ItemHistory.CPS24 =#TempItemHistory.cps24,
            ItemHistory.CPS25 =#TempItemHistory.cps25,ItemHistory.CPS26 =#TempItemHistory.cps26,
	    ItemHistory.CPS27 =#TempItemHistory.cps27,ItemHistory.CPS28 =#TempItemHistory.cps28,
	    ItemHistory.CPS29 =#TempItemHistory.cps29,ItemHistory.CPS30 =#TempItemHistory.cps30,
	    ItemHistory.CPS31 =#TempItemHistory.cps31,ItemHistory.CPS32 =#TempItemHistory.cps32,
            ItemHistory.CPS33 =#TempItemHistory.cps33,ItemHistory.CPS34 =#TempItemHistory.cps34,
	    ItemHistory.CPS35 =#TempItemHistory.cps35,ItemHistory.CPS36 =#TempItemHistory.cps36,
            ItemHistory.CPS37 =#TempItemHistory.cps37,ItemHistory.CPS38 =#TempItemHistory.cps38,
	    ItemHistory.CPS39 =#TempItemHistory.cps39,ItemHistory.CPS40 =#TempItemHistory.cps40,
	    ItemHistory.CPS41 =#TempItemHistory.cps41,ItemHistory.CPS42 =#TempItemHistory.cps42,
	    ItemHistory.CPS43 =#TempItemHistory.cps43,ItemHistory.CPS44 =#TempItemHistory.cps44,
            ItemHistory.CPS45 =#TempItemHistory.cps45,ItemHistory.CPS46 =#TempItemHistory.cps46,
            ItemHistory.CPS47 =#TempItemHistory.cps47,ItemHistory.CPS48 =#TempItemHistory.cps48,
	    ItemHistory.CPS49 =#TempItemHistory.cps49,ItemHistory.CPS50 =#TempItemHistory.cps50,
	    ItemHistory.CPS51 =#TempItemHistory.cps51,ItemHistory.CPS52 =#TempItemHistory.cps52
	    FROM ItemHistory 
            INNER JOIN #tempItemHistory 
	    ON ItemHistory.Lcid = #tempItemHistory.Lcid
            AND Item.Item = #tempItemHistory.Item 
	    AND ItemHistory.SubsItem =#TempItemHistory.Item
	    AND ItemHistory.HisYear =#TempItemHistory.HisYear
	    WHERE ItemHistory.lcid = @TargetLcId 
    	     AND ItemHistory.HisYear = @HisYear

 	delete #TempItemHistory
    		END

    		-- Get the next record
       		FETCH NEXT FROM HsCursor INTO
    		@LcId, @Item,@SubsItem, @HisYear, @CPS01, @CPS02, @CPS03, @CPS04, @CPS05, @CPS06, @CPS07, @CPS08, @CPS09, 
		@CPS10, @CPS11, @CPS12, @CPS13, @CPS14, @CPS15, @CPS16, @CPS17, @CPS18, @CPS19, @CPS20, @CPS21, @CPS22,  
		@CPS23, @CPS24, @CPS25, @CPS26, @CPS27, @CPS28, @CPS29, @CPS30, @CPS31, @CPS32, @CPS33, @CPS34, @CPS35, 
		@CPS36, @CPS37, @CPS38, @CPS39, @CPS40, @CPS41, @CPS42, @CPS43, @CPS44, @CPS45, @CPS46, @CPS47, @CPS48, 
		@CPS49, @CPS50, @CPS51, @CPS52,  
		@QtyOrd01, @QtyOrd02, @QtyOrd03, @QtyOrd04, @QtyOrd05, @QtyOrd06, @QtyOrd07, @QtyOrd08, @QtyOrd09, 
		@QtyOrd10, @QtyOrd11, @QtyOrd12, @QtyOrd13, @QtyOrd14, @QtyOrd15, @QtyOrd16, @QtyOrd17, @QtyOrd18, 
		@QtyOrd19, @QtyOrd20, @QtyOrd21, @QtyOrd22, @QtyOrd23, @QtyOrd24, @QtyOrd25, @QtyOrd26, @QtyOrd27, 
		@QtyOrd28, @QtyOrd29, @QtyOrd30, @QtyOrd31, @QtyOrd32, @QtyOrd33, @QtyOrd34, @QtyOrd35, @QtyOrd36, 
		@QtyOrd37, @QtyOrd38, @QtyOrd39, @QtyOrd40, @QtyOrd41, @QtyOrd42, @QtyOrd43, @QtyOrd44, @QtyOrd45, 
		@QtyOrd46, @QtyOrd47, @QtyOrd48, @QtyOrd49, @QtyOrd50, @QtyOrd51, @QtyOrd52, @OrdCnt01, @OrdCnt02, 
		@OrdCnt03, @OrdCnt04, @OrdCnt05, @OrdCnt06, @OrdCnt07, @OrdCnt08, @OrdCnt09, @OrdCnt10, @OrdCnt11, 
		@OrdCnt12, @OrdCnt13, @OrdCnt14, @OrdCnt15, @OrdCnt16, @OrdCnt17, @OrdCnt18, @OrdCnt19, @OrdCnt20, 
		@OrdCnt21, @OrdCnt22, @OrdCnt23, @OrdCnt24, @OrdCnt25, @OrdCnt26, @OrdCnt27, @OrdCnt28, @OrdCnt29, 
		@OrdCnt30, @OrdCnt31, @OrdCnt32, @OrdCnt33, @OrdCnt34, @OrdCnt35, @OrdCnt36, @OrdCnt37, @OrdCnt38, 
		@OrdCnt39, @OrdCnt40, @OrdCnt41, @OrdCnt42, @OrdCnt43, @OrdCnt44, @OrdCnt45, @OrdCnt46, @OrdCnt47, 
		@OrdCnt48, @OrdCnt49, @OrdCnt50, @OrdCnt51, @OrdCnt52	
END
CLOSE HsCursor
DEALLOCATE HsCursor

-- Update Scaling Factor in Location Table
UPDATE AIMLocations 
SET DmdScalingFactor = @DmdScalingFactor,
ScalingEffUntil = @ScalingEffUntil
WHERE LcId = @TargetLcId
  
Return 1			-- Successful

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

