SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItmChg_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItmChg_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItmChg_Sp
**	Desc: Updates the changes the location, item key of an item record.
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Cost, Price, List Price
**              
**	Auth:   Randy Sadler
**	Date:   03/24/2000
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**      08/20/2002 Wade Riza    Updated Return Codes and Error Handling
**      09/12/2003 Srinivas U   Modified code for SubsItem
*******************************************************************************/

CREATE PROCEDURE AIM_ItmChg_Sp
(
      	@LcId       					nvarchar(12), 
  	@New_Item   					nvarchar(25), 
  	@Old_Item   					nvarchar(25) 
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @HsCount    				int
  
SET NOCOUNT ON

-- CREATE the new Item Table
INSERT INTO Item 
        (Lcid, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, Class3, Class4, 
        BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, SaId, PmId, UPC, Weight, Cube, 
        ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, BkCost03, BkQty04, 
        BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, BkQty08, BkCost08, BkQty09, 
        BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, ReplenCost2, Oh, Oo, ComStk, 
        BkOrder, BkComStk, LeadTime, PackRounding, IMin, IMax, CStock, SSAdj, UserMin, UserMax, 
        UserMethod, FcstMethod, FcstDemand, UserFcst, UserFcstExpDate, MAE, MSE, Trend, FcstCycles, 
        ZeroCount, DIFlag, DmdFilterFlag, TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, 
        LTVFact, PlnTT, ZOPSw, OUTLSw, ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, 
        Freeze_OptionID, Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, 
        FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, 
        VC_Amt_Ranking, VC_Date, VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, 
        NextPOQty_1, NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, 
        UserRef1, UserRef2, UserRef3,Freeze_Forecast,AllocatableQty) 
        SELECT Lcid, @New_Item, ItDesc, ItStat, ActDate, InActDate, OptionID, 
        Class1, Class2, Class3, Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, SaId, 
        PmId, UPC, Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, 
        BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, BkQty08, 
        BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, ReplenCost2, Oh, Oo, 
        ComStk, BkOrder, BkComStk, LeadTime, PackRounding, IMin, IMax, CStock, SSAdj, UserMin, UserMax, 
        UserMethod, FcstMethod, FcstDemand, UserFcst, UserFcstExpDate, MAE, MSE, Trend, FcstCycles, 
        ZeroCount, DIFlag, DmdFilterFlag, TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, 
        PlnTT, ZOPSw, OUTLSw, ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, 
        Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, Fcst_Month, 
        Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, VC_Amt_Ranking, VC_Date, VelCode_Prev, 
        VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, NextPOQty_1, NextPONbr_2, NextPODate_2, 
        NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, UserRef1, UserRef2, UserRef3,Freeze_ForeCast,allocatableqty
        FROM Item
    	WHERE LcId = @LcId
    	AND Item = @Old_Item
	
-- Check for SQL Server errors.  
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

-- CREATE the Item History Table
SELECT @HsCount = 0

SELECT @HsCount = COUNT(*) 
FROM ItemHistory 
WHERE Lcid = @LcId 
AND Item = @New_Item
IF @HsCount = 0
BEGIN
        INSERT INTO ItemHistory 
            (LcId, Item,SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, CPS10, 
            CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, CPS23, 
            CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, 
            CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, 
            CPS50, CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, 
            QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, 
            QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, 
            QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, 
            QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, 
            QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, 
            OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, 
            OrdCnt10, OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, 
            OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, 
            OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, 
            OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, 
            OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52) 
            SELECT LcId, @New_Item,@New_Item, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, 
            CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, 
            CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, 
            CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, 
            CPS49, CPS50, CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, 
            QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, 
            QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, 
            QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, 
            QtyOrd34, QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, 
            QtyOrd43, QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, 
            QtyOrd52, OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, 
            OrdCnt09, OrdCnt10, OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, 
            OrdCnt18, OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, 
            OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, 
            OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, 
            OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52 
            FROM ItemHistory
    	    WHERE LcId = @LcId
    	    AND Item = @Old_Item
	    AND SubsItem =@Old_Item


	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01) CPS01, 
      	sum(ItemHistory.CPS02) CPS02,sum(ItemHistory.CPS03) CPS03,sum( ItemHistory.CPS04) CPS04,
	sum(ItemHistory.CPS05) CPS05, sum(ItemHistory.CPS06) CPS06, sum(ItemHistory.CPS07) CPS07, 
      	sum(ItemHistory.CPS08) CPS08,sum(ItemHistory.CPS09) CPS09, sum(ItemHistory.CPS10) CPS10,
	sum(ItemHistory.CPS11) CPS11, sum(ItemHistory.CPS12) CPS12,sum( ItemHistory.CPS13)CPS13, 
       	sum(ItemHistory.CPS14) CPS14, sum(ItemHistory.CPS15) CPS15,sum( ItemHistory.CPS16) CPS16, 
       	sum(ItemHistory.CPS17) CPS17, sum(ItemHistory.CPS18) CPS18,sum( ItemHistory.CPS19) CPS19, 
       	sum(ItemHistory.CPS20) CPS20, sum(ItemHistory.CPS21) CPS21,sum( ItemHistory.CPS22) CPS22, 
       	sum(ItemHistory.CPS23) CPS23, sum(ItemHistory.CPS24) CPS24, sum(ItemHistory.CPS25) CPS25, 
       	sum(ItemHistory.CPS26) CPS26,sum( ItemHistory.CPS27) CPS27, sum(ItemHistory.CPS28) CPS28, 
        sum(ItemHistory.CPS29) CPS29,sum( ItemHistory.CPS30) CPS30, sum(ItemHistory.CPS31) CPS31, 
       	sum(ItemHistory.CPS32) CPS32, sum(ItemHistory.CPS33) CPS33, sum(ItemHistory.CPS34) CPS34, 
       	sum(ItemHistory.CPS35) CPS35, sum(ItemHistory.CPS36) CPS36, sum(ItemHistory.CPS37) CPS37, 
       	sum(ItemHistory.CPS38) CPS38, sum(ItemHistory.CPS39) CPS39, sum(ItemHistory.CPS40) CPS40, 
      	sum(ItemHistory.CPS41) CPS41, sum(ItemHistory.CPS42) CPS42, sum(ItemHistory.CPS43) CPS43, 
        sum(ItemHistory.CPS44) CPS44, sum(ItemHistory.CPS45) CPS45, sum(ItemHistory.CPS46) CPS46, 
      	sum(ItemHistory.CPS47) CPS47, sum(ItemHistory.CPS48) CPS48, sum(ItemHistory.CPS49) CPS49, 
        sum(ItemHistory.CPS50) CPS50, sum(ItemHistory.CPS51) CPS51,sum( ItemHistory.CPS52) CPS52
       	INTO #TempItemHistory
       	From ItemHistory 
	WHERE LcId = @LcId 
    	AND Item = @Old_Item
	Group by ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear
      	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc

	update ItemHistory 
	set ItemHistory.CPS01 =#TempItemHistory.cps01,ItemHistory.CPS02 =#TempItemHistory.cps02,
	    ItemHistory.CPS03 =#TempItemHistory.cps03,ItemHistory.CPS04 =#TempItemHistory.cps04,
	    ItemHistory.CPS05 =#TempItemHistory.cps05,ItemHistory.CPS06 =#TempItemHistory.cps06,
	    ItemHistory.CPS07 =#TempItemHistory.cps07,ItemHistory.CPS08 =#TempItemHistory.cps08,
	    ItemHistory.CPS09 =#TempItemHistory.cps09,ItemHistory.CPS10 =#TempItemHistory.cps10,
	    ItemHistory.CPS11 =#TempItemHistory.cps11,ItemHistory.CPS12 =#TempItemHistory.cps12,
	    ItemHistory.CPS13 =#TempItemHistory.cps13,ItemHistory.CPS14 =#TempItemHistory.cps14,
	    ItemHistory.CPS15 =#TempItemHistory.cps15,ItemHistory.CPS16 =#TempItemHistory.cps16,
            ItemHistory.CPS17 =#TempItemHistory.cps17,ItemHistory.CPS18 =#TempItemHistory.cps18,
	    ItemHistory.CPS19 =#TempItemHistory.cps19,ItemHistory.CPS20 =#TempItemHistory.cps20,
	    ItemHistory.CPS21 =#TempItemHistory.cps21,ItemHistory.CPS22 =#TempItemHistory.cps22,
	    ItemHistory.CPS23 =#TempItemHistory.cps23,ItemHistory.CPS24 =#TempItemHistory.cps24,
            ItemHistory.CPS25 =#TempItemHistory.cps25,ItemHistory.CPS26 =#TempItemHistory.cps26,
	    ItemHistory.CPS27 =#TempItemHistory.cps27,ItemHistory.CPS28 =#TempItemHistory.cps28,
	    ItemHistory.CPS29 =#TempItemHistory.cps29,ItemHistory.CPS30 =#TempItemHistory.cps30,
	    ItemHistory.CPS31 =#TempItemHistory.cps31,ItemHistory.CPS32 =#TempItemHistory.cps32,
            ItemHistory.CPS33 =#TempItemHistory.cps33,ItemHistory.CPS34 =#TempItemHistory.cps34,
	    ItemHistory.CPS35 =#TempItemHistory.cps35,ItemHistory.CPS36 =#TempItemHistory.cps36,
            ItemHistory.CPS37 =#TempItemHistory.cps37,ItemHistory.CPS38 =#TempItemHistory.cps38,
	    ItemHistory.CPS39 =#TempItemHistory.cps39,ItemHistory.CPS40 =#TempItemHistory.cps40,
	    ItemHistory.CPS41 =#TempItemHistory.cps41,ItemHistory.CPS42 =#TempItemHistory.cps42,
	    ItemHistory.CPS43 =#TempItemHistory.cps43,ItemHistory.CPS44 =#TempItemHistory.cps44,
            ItemHistory.CPS45 =#TempItemHistory.cps45,ItemHistory.CPS46 =#TempItemHistory.cps46,
            ItemHistory.CPS47 =#TempItemHistory.cps47,ItemHistory.CPS48 =#TempItemHistory.cps48,
	    ItemHistory.CPS49 =#TempItemHistory.cps49,ItemHistory.CPS50 =#TempItemHistory.cps50,
	    ItemHistory.CPS51 =#TempItemHistory.cps51,ItemHistory.CPS52 =#TempItemHistory.cps52
	    FROM ItemHistory 
            INNER JOIN #tempItemHistory 
	    ON ItemHistory.Lcid = #tempItemHistory.Lcid
	    AND ItemHistory.item =@New_Item
	    AND ItemHistory.HisYear =#tempItemHistory.HisYear


	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
END
  
RETURN 1	-- Succeed

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

