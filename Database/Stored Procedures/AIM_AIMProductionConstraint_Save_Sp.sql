SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMProductionConstraint_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMProductionConstraint_Save_Sp]
GO

/*****************************************************************************
**	Name: AIM_AIMProductionConstraint_Save_Sp
**	Desc: Updates/Inserts to AIMProductionConstraint, based on the InsertUpdate flag
**
**	Returns: 1)@Found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:   Annalaksh<PERSON> Stocksdale
**	Date:   2003/01/06
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:   		Description:
**	---------- 	------------		-----------------------------------------------
**	2004/01/09	A.Stocksdale	Removed @InsertUpdate -- not required.
**								Changed datatype for CycleDays from numeric (18,0) to int. 
**								Cycle days is not likely to be larger than 2,147,483,647.
*******************************************************************************/

CREATE PROCEDURE AIM_AIMProductionConstraint_Save_Sp
(
 	@ConstraintID nvarchar(30),
 	@ConstraintType nvarchar(30),
	@ConstraintDesc nvarchar(255),
 	@CycleDays int,
 	@RatioType nvarchar(30),
 	@EnableConstraint nvarchar(1) = "N"
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	DECLARE @Found int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @ConstraintID IS Null
	OR @ConstraintType IS Null
	OR @ConstraintDesc IS Null
	OR @CycleDays IS Null
	OR @RatioType IS Null
	OR @EnableConstraint IS Null
	BEGIN
	  	RETURN -1
	END

  	UPDATE AIMProductionConstraint
	SET ConstraintType = @ConstraintType, 
		ConstraintDesc = @ConstraintDesc, 
		CycleDays = @CycleDays, 
		RatioType = @RatioType,
		EnableConstraint = @EnableConstraint
  	WHERE AIMProductionConstraint.ConstraintID = @ConstraintID

	SET @Found = @@ROWCOUNT 
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	
	IF @Found= 0
	BEGIN
	  	INSERT INTO AIMProductionConstraint (ConstraintID, 
			ConstraintType, 
			ConstraintDesc, 
			CycleDays,
			RatioType,
			EnableConstraint)
		VALUES  (@ConstraintID, 
			@ConstraintType, 
			@ConstraintDesc, 
			@CycleDays,
			@RatioType,
			@EnableConstraint)
	END

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE
	BEGIN
	 	RETURN 0
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

