SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_FySales_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_FySales_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_FySales_Get_Sp
** 	Desc: Gets the Fiscal Year Sales for an Item
**
** 	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
** 	Values:  Sales
**              
** 	Auth:   Srinivas Uddanti
** 	Date:   08/19/2002
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:      Author:      Description:
** 	---------- ------------ -----------------------------------------------
** 	08/20/2002 Wade Riza    Updated Return Codes and Error Handling
** 	09/30/2002 Wade Riza    Updated Field Lengths for (Price, Cost & List 
**                              Price) 
**	09/12/2003 Srinivas U	Changed code for SubsItem
*******************************************************************************/

CREATE PROCEDURE AIM_FySales_Get_Sp
(
    	@LcId            		nvarchar(12), 
   	@Item        			nvarchar(25), 
 	@StartDate     			datetime,
   	@TotalSales        		decimal(12,2) OUTPUT,
 	@TotalPrice     		decimal(12,2) OUTPUT,
 	@TotalCost     			decimal(12,2) OUTPUT,
 	@TotalListPrice     	        decimal(12,2) OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE 
   	@found       			smallint,
   	@FyYear      			smallint,
 	@FyStartDate     		datetime,
 	@Loop      		    	smallint,
 	@Qty      		    	decimal(9,1),
 	@Price      			decimal(10,4),
 	@Cost      		    	decimal(10,4),
 	@ListPrice     			decimal(10,4),
 	@EndWeekDate     		datetime,
 	@StartWeekDate     		datetime,
 	@RowFound     			smallint

SET NOCOUNT ON
 	
SELECT @Fyyear =fiscalyear FROM aimyears WHERE fystartdate <= @Startdate and
fyenddate >=@Startdate

IF @@rowcount = 0

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
 	SELECT @FYStartdate =FYStartDate FROM aimyears WHERE fiscalyear =@FyYear -1
IF  @@rowcount = 0 
BEGIN
 	SELECT @FYStartdate = DATEADD(yy,-1,@Fyyear)
END
 
SELECT @Totalsales = 0

-- IF isdate(@Stkdate) =0 -- Invalid date
SELECT @RowFound =1
FROM Itemhistory
WHERE hisyear =@Fyyear -1  AND
Item = @item AND
LcID = @LcID

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0
BEGIN
 	SELECT @Totalsales =0
 	SELECT @TotalPrice =0
 	SELECT @TotalCost =0
 	SELECT @TotalListPrice =0
 	Return 0
END
SET @loop =1
SET @Price =0
SET @Cost =0
SET @ListPrice =0
SET @StartWeekDate =@FYStartdate
SET @totalSales =0
SET @totalPrice =0
SET @totalCost =0
SET @totalListPrice =0

  WHILE (@Loop <=52) BEGIN
  SELECT @Qty = CASE @loop
        WHEN 1 THEN Sum(cps01)
        WHEN 2 THEN Sum(cps02)
        WHEN 3 THEN Sum(cps03)
        WHEN 4 THEN Sum(cps04)
        WHEN 5 THEN Sum(cps05)
        WHEN 6 THEN Sum(cps06)
        WHEN 7 THEN Sum(cps07)
        WHEN 8 THEN Sum(cps08)
        WHEN 9 THEN Sum(cps09)
        WHEN 10 THEN Sum(cps10)
        WHEN 11 THEN Sum(cps11)
        WHEN 12 THEN Sum(cps12)
        WHEN 13 THEN Sum(cps13)
        WHEN 14 THEN Sum(cps14)
        WHEN 15 THEN Sum(cps15)
        WHEN 16 THEN Sum(cps16)
        WHEN 17 THEN Sum(cps17)
        WHEN 18 THEN Sum(cps18)
        WHEN 19 THEN Sum(cps19)
        WHEN 20 THEN Sum(cps20)
        WHEN 21 THEN Sum(cps21)
        WHEN 22 THEN Sum(cps22)
        WHEN 23 THEN Sum(cps23)
        WHEN 24 THEN Sum(cps24)
        WHEN 25 THEN Sum(cps25)
        WHEN 26 THEN Sum(cps26)
        WHEN 27 THEN Sum(cps27)
        WHEN 28 THEN Sum(cps28)
        WHEN 29 THEN Sum(cps29)
        WHEN 30 THEN Sum(cps30)
        WHEN 31 THEN Sum(cps31)
        WHEN 32 THEN Sum(cps32)
        WHEN 33 THEN Sum(cps33)
        WHEN 34 THEN Sum(cps34)
        WHEN 35 THEN Sum(cps35)
        WHEN 36 THEN Sum(cps36)
        WHEN 37 THEN Sum(cps37)
        WHEN 38 THEN Sum(cps38)
        WHEN 39 THEN Sum(cps39)
        WHEN 40 THEN Sum(cps40)
        WHEN 41 THEN Sum(cps41)
        WHEN 42 THEN Sum(cps42)
        WHEN 43 THEN Sum(cps43)
        WHEN 44 THEN Sum(cps44)
        WHEN 45 THEN Sum(cps45)
        WHEN 46 THEN Sum(cps46)
        WHEN 47 THEN Sum(cps47)
        WHEN 48 THEN Sum(cps48)
        WHEN 49 THEN Sum(cps49)
        WHEN 50 THEN Sum(cps50)
        WHEN 51 THEN Sum(cps51)
        WHEN 52 THEN Sum(cps52)
        END
FROM Itemhistory
WHERE hisyear =@Fyyear -1  AND
Item = @item AND
LcID = @LcID

  	SELECT @EndWeekDate =dateadd (ww,1*@Loop,@FyStartDate)

  	EXECUTE  AIM_CostPriceListPrice_Get_SP @lcid,@item,@StartWeekDate,@EndWeekDate,@cost OUTPUT,@price
 		OUTPUT,@listprice OUTPUT
  
  	SELECT @StartWeekDate =dateadd (ww,1*@Loop,@FyStartDate)
  	SET @Qty =COALESCE(@Qty,0)
  	SET @TotalPrice=@TotalPrice + @Qty *COALESCE(@Price,0)
  	SET @TotalCost=@TotalCost + @Qty *COALESCE(@Cost,0)
  	SET @TotalListPrice=@TotalListPrice + @Qty *COALESCE(@ListPrice,0)
  	SET @TotalSales =@TotalSales +@Qty
  	SET @loop =@loop +1
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

