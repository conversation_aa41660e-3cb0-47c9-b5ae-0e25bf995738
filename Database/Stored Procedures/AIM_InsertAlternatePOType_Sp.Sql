
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InsertAlternatePOType_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
Drop procedure [dbo].[AIM_InsertAlternatePOType_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHOR<PERSON>ZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_InsertAlternatePOType_Sp
**	Desc: Loads Alternate Vendor/Source Records into Alternate_Source Table
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Mohammed
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:      Author:        Description:
**  ---------- -------------- -------------------------------------------------
**
*******************************************************************************/

CREATE    Procedure AIM_InsertAlternatePOType_Sp
(
       @Item					nvarchar(25),
       @LcId					nvarchar(12)
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE 
        @ById                                      nvarchar(12), 
        @Lc_ById                                   nvarchar(12),
      	@StkDate                                   datetime,
        @Vn_ById                                   nvarchar(12),
    	@RtnCode                                   int,
     	@VnId					   nvarchar(12),
     	@Assort					   nvarchar(12),
     	@BuyingUOM				   nvarchar(6),
     	@PackRounding			           nvarchar(1),
    	@LeadTime				   smallint,
    	@Vn_LeadTime			           smallint,
    	@ConvFactor				   smallint,
    	@Cost					   decimal(10,4),
    	@PrimaryCost			           decimal(10,4),
     	@ItDesc					   nvarchar(30),
    	@availqty	                           int,	
        @Oh                                        int, 
        @Oo                                        int, 
        @ComStk                                    int, 
        @BkOrder                                   int, 
        @BkComStk                                  int, 
        @Accum_Lt                                  smallint, 
        @FcstLT                                    decimal(10,2), 
        @FcstUpdCyc                                int,
        @UOM                                       nvarchar(6), 
        @Zone                                      nvarchar(1),
     	@ByIdSource                                nvarchar(1),
        @LeadTimeSource                            nvarchar(1),
     	@LastWeekSalesFlag                         nvarchar(1),
       	@RevCycle                                  nvarchar(8), 
    	@dudate		                           datetime,
    	@isdate		                           datetime,
  	@UserInitials			           nvarchar(3),	
        @POLinefound                               int,
        @POfound                                   int,
       	@Vn_Min                                    decimal(9,2), 
       	@Vn_Best                                   decimal(9,2), 
       	@Reach_Code                                nvarchar(1), 
    	@TransmitPO			           tinyint,
     	@VName					   nvarchar(30),
     	@VAddress1				   nvarchar(30),
     	@VAddress2				   nvarchar(30),
     	@VCity					   nvarchar(20),
     	@VState					   nvarchar(10),
     	@VZip					   nvarchar(10),
     	@ShipIns				   nvarchar(20),
       	@POByZone                                  nvarchar(1),
        @TotalUnits                                decimal(10,2), 
        @TotalCube                                 decimal(10,2), 
        @TotalWeight                               decimal(10,2), 
        @TotalExtCost                              decimal(10,2)
 
  SET NOCOUNT ON
  
  -- Initialize IsDate
  select @isdate = getdate()
  exec AIM_GetDate_Sp @isdate output

  -- End - Initialize

  Select @ByIdSource = ByIdSource From SysCtrl

  Select @Lc_ById=Dft_ById, @StkDate=StkDate, @LeadTimeSource=LeadTimeSource From AIMLocations Where LcId = @LcId
  
  Select @ItDesc=ItDesc,@UOM=UOM, @Zone=Substring(Item.BinLocation,1,1), @Accum_Lt=Accum_Lt, @FcstLT=FcstLT, @Oh=Oh, 
         @Oo=Oo, @ComStk=ComStk, @BKOrder=BkOrder, @BKComStk=BkComStk, @PrimaryCost = Item.Cost, @FcstUpdCyc=FcstUpdCyc
    From Item, AIMVendors
   Where Item = @Item and LcId = @LcId
     AND Item.VnId = AIMVendors.VnId
     AND Item.Assort = AIMVendors.Assort

  
  -- CREATE cursor to process DxAS_Cursor 
  DECLARE DxAS_Cursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT VnId, Assort, BuyingUOM, ConvFactor, PackRounding, StdCost, Leadtime 
         FROM Alternate_Source 
        WHERE Item = @Item 
          AND LcId = @LcId
          AND Enabled = '1'

  OPEN DxAS_Cursor
  -- Check for no rows to process
  IF @@CURSOR_ROWS = 0 
        RETURN
  -- Update Loop
 
  BEGIN TRANSACTION
  WHILE 1 = 1 
  BEGIN -- WHILE 1 = 1 
        FETCH NEXT FROM DxAS_Cursor INTO 
        	@VnId, @Assort, @BuyingUOM, @ConvFactor, @PackRounding, @Cost, @LeadTime
        -- Check Fetch Status
        IF @@FETCH_STATUS <> 0
    		break

        SELECT @VName=VName, @Vn_Byid=Dft_Byid, @RevCycle=RevCycle, @LastWeekSalesFlag=LastWeekSalesFlag, 
                @TransmitPO=TransmitPO, @VAddress1=VAddress1, @VAddress2=VAddress2, @VCity=VCity, @VState=VState, 
                @VZip=VZip, @Vn_LeadTime=Dft_LeadTime, @Vn_Min=Vn_Min, @Vn_Best=Vn_Best, @Reach_Code=Reach_Code, 
                @ShipIns=ShipIns, @POByZone=POByZone
          FROM AIMVendors 
         WHERE Vnid = @VnId AND Assort = @Assort

         -- Get the Buyer Id according to the Buyer-Id Source
         IF @ByIdSource = 'V' 
            Select @ById = @Vn_ById
         Else IF @ByIdSource = 'L' 
            Select @ById = @Lc_ById

       -- Check if a PO Line for the Item exists
        IF EXISTS (SELECT VnId, Assort FROM PODetail WHERE ById = @ById AND VnId = @VnId AND Assort = @Assort
                      AND Item = @Item AND LcId = @LcId)
	   SELECT @POLinefound = 1 -- Nothing to Do
	ELSE
    	   SELECT @POLinefound = 0


        IF @POLinefound = 0
          Begin  --- IF @POLinefound = 0

	    -- Initialize DuDate
	    select @dudate = getdate()
	    exec AIM_GetDate_Sp @dudate output
	    select @dudate = dateadd(dd, @LeadTime, @isdate)

     	    -- Get the Buyer's Initials
    	    Select @UserInitials = 'XXX'
    	    Select @UserInitials = coalesce(UserInitials, left(@ById, 3)) From AIMUsers Where UserId = @ById


	   -- Calculate available inventory
	   select @AvailQty = @oh
    	
	   -- IF the accumulative lead time is invalid get the correct value
    	   IF @accum_lt <=0 
    		EXEC @accum_lt = AIM_GetAccum_LT_Sp @Lcid, @Item, 'Y'
        
           -- Adjust for burn between the stock date and the review date
           IF @accum_lt > 0
    	      select @AvailQty = round(@AvailQty - (datedIFf(dd, @StkDate, @isdate) 
    			* (convert(decimal(10,4), @fcstlt) / convert(decimal(10,4), @accum_lt))), 0)
    	    
    	   -- On-hand cannot be less than zero
    	   IF @AvailQty < 0 
    	        select @AvailQty = 0
        
           -- Adjust Availability for On-Order and committed inventory
           Select @AvailQty = @AvailQty + @oo - @comstk - @bkorder - @bkcomstk 

           -- Check for a sale last week
    	   IF @LastWeekSalesFlag = 'Y'
    	        BEGIN
    	            execute @RtnCode = AIM_GetLastWeekSales_Sp @LcId, @Item, @FcstUpdCyc
    	            IF @RtnCode = 1 
    	                select @LastWeekSalesFlag = 'Y'
    	            ELSE
    	                select @LastWeekSalesFlag = 'N'
    	        END
    	    ELSE
	        Select @LastWeekSalesFlag = 'N'
        
           INSERT INTO PODetail
                (POLineType, OrdStatus, RevCycle, ById, VnId, Assort, Lcid, Item, ItDesc, 
                 POType, AvailQty, PackRounding, RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, 
                 ConvFactor, IsDate, DuDate, LastWeekSalesFlag, Cost, Zone)
            VALUES 
                (50, 'P', @RevCycle, @ById, @VnId, @Assort, @Lcid, @Item, @ItDesc, 'A', @AvailQty, @PackRounding, 
                 0, 0,0, 0, @UOM, @BuyingUOM, @ConvFactor, @IsDate, @DuDate, @LastWeekSalesFlag, @Cost, @Zone)

          IF @@ERROR <> 0 
             Continue

          IF Exists(SELECT VnId, Assort FROM AIMPO WHERE ById = @ById AND VnId = @VnId AND Assort = @Assort)
   	      Select @POfound = 1
          Else
   	      Select @POfound = 0

          IF @POFound = 0
		EXEC AIM_POHdrInsert_Sp @ById, @VnId, @Assort, 'P', @TransmitPO, @ShipIns, @UserInitials, '', '', '', 'N', 
           	    		        @VName, @VAddress1, @VAddress2, @VCity, @VState, @VZip, 1, 0, @Vn_Min, @Vn_Best, 
                                        @Reach_Code, 'S', @POByZone, @LeadTime, 0
          Else
             Update AIMPO Set LineCount = LineCount + 1 WHERE ById = @ById AND VnId = @VnId AND Assort = @Assort
          End  --- IF @POLinefound = 0
  END  --- WHILE 1 = 1 	
  COMMIT TRANSACTION
  
  Select PD.OrdStatus, 
         PD.VnId,  
         AIMPO.VName, 
         PD.Assort, 
         PD.ById, 
         PD.VSOQ, 
         PD.ConvFactor,
         PD.RSOQ, 
         PD.PackRounding, 
         LTrim(Str(PosLineCount)) + '/' + LTrim(Str(LineCount)) Lines, 
         0 TotalExtCost,  
         AIMPO.Vn_Min, 
         AIMPO.Vn_Best, 
         AIMPO.Reach_Code, 
         0 CurrentTotal, 
         NextDateTime, 
         IMax, 
         (PD.VSOQ * PD.Cost) ExtCost, 
         0 LeadTime, 
         PD.IsDate, 
         PD.DuDate, 
         Item.IMin, 
         PD.BuyingUOM, 
         PD.UOM, 
         PD.Cost UnitCost, 
         0 TotalUnits,
         0 TotalCube, 
         0 TotalWeight, 
         CASE POType When 'A' Then 'A' Else 'PRIMARY' End RecordType,
         PD.VSOQ InitialVSOQ,
         PD.OrdStatus InitialOrdStatus, 
         PD.Item,
         PD.LcId, 
         Item.LeadTime ItemLeadTime,     
         AIMVendors.Dft_LeadTime VnLeadTime,
         AIMLocations.LeadTimeSource,
         AIMLocations.PutawayDays
   Into #Sourcing
   From PODetail PD, Item, AIMVendors, RevCycles, AIMPO, AIMLocations
  Where PD.Item = @Item
    AND PD.LcId = @LcId
    AND Item.Item = PD.Item
    AND Item.LcId = PD.LcId
    AND Item.LcId = AIMLocations.LcId
    AND AIMVendors.RevCycle = RevCycles.RevCycle
    AND AIMPO.ById = PD.ById
    AND AIMPO.VnId = PD.VnId
    AND AIMPO.Assort = PD.Assort
    AND AIMPO.Assort = AIMVendors.Assort
    AND AIMPO.VnId = AIMVendors.VnId
    AND PD.OrdStatus IN ('P','R')

    -- Filter Vendors according to the Enabled Flag   
    Delete #Sourcing From Alternate_Source
    Where Alternate_Source.VnId  = #Sourcing.VnId 
      AND #Sourcing.Assort = Alternate_Source.Assort
      AND #Sourcing.Item = Alternate_Source.Item
      AND #Sourcing.LcId = Alternate_Source.LcId
      AND Alternate_Source.Enabled = '0'

   -- Update the Lead Time:
   -- For ALT Vendors, LeadTime = Alternate_Source.LeadTime + Putawaydays
   Update #Sourcing Set LeadTime = Alternate_Source.Leadtime + Putawaydays 
     From Alternate_Source 
    Where Alternate_Source.VnId = #Sourcing.VnId 
      AND #Sourcing.Assort = Alternate_Source.Assort
      AND #Sourcing.Item = Alternate_Source.Item
      AND #Sourcing.LcId = Alternate_Source.LcId
      AND #Sourcing.RecordType = 'A'
 
   -- Primary Lead Time Update
   Update #Sourcing Set LeadTime = Case LeadTimeSource When 'V' Then VnLeadTime + Putawaydays Else ItemLeadTime + Putawaydays End 
    Where #Sourcing.RecordType = 'PRIMARY'

   -- Update TotalUnits, TotalCube, TotalWeight, TotalExtCost 
  Select #Sourcing.VnId, #Sourcing.Assort, #Sourcing.ById, TotalCube = IsNull(Sum(PD.VSOQ * Cube),0), 
         TotalWeight = IsNull(Sum(PD.VSOQ * Weight),0), 
         TotalExtCost = IsNull(Sum(PD.VSOQ * PD.Cost),0), 
         TotalUnits = IsNull(Sum(PD.VSOQ),0)  
    Into #TempCalc
    From Item, PODetail PD, #Sourcing 
   Where Item.Item = PD.Item 
     AND Item.LcId = PD.LcId
     AND #Sourcing.ById = PD.ById 
     AND #Sourcing.VnId = PD.VnId
     AND #Sourcing.Assort = PD.Assort
   Group By #Sourcing.VnId, #Sourcing.Assort, #Sourcing.ById

   UPDATE #Sourcing Set
      CurrentTotal = #TempCalc.TotalUnits, 
      TotalUnits   = #TempCalc.TotalUnits, 
      TotalCube    = #TempCalc.TotalCube, 
      TotalWeight  = #TempCalc.TotalWeight, 
      TotalExtCost = #TempCalc.TotalExtCost
    From #TempCalc 
    Where #Sourcing.ById = #TempCalc.ById 
      AND #Sourcing.VnId = #TempCalc.VnId
      AND #Sourcing.Assort = #TempCalc.Assort

    
   -- Return the Sourcing Data
   Select * From #Sourcing Order By RecordType Desc

  RETURN 1	-- Succeed


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


