if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMCalendar_BoundaryDates_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMCalendar_BoundaryDates_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


/*******************************************************************************
--	Name: AIM_AIMCalendar_BoundaryDates_Sp
--	This stored procedure is meant to retrieve the start and end dates from the AIMCalendar, 
--		depending upon the following parameters (and related dependencies):
--
--	Returns: 1)  0 - Successful
--			2) -1 - No Data Found
--			3) -2 - SQL Error
--			4) -3 - Invalid parameters
--
--	Values:  RETURNDate and/OR RETURNInt
--
--	Auth:   <PERSON><PERSON><PERSON><PERSON>sdale
--	Date:   2004/02/11
*******************************************************************************
--	Change History
*******************************************************************************
--	Date:      	Author:      		Description:
--	---------- 	------------ 		-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_AIMCalendar_BoundaryDates_Sp (
	@TargetDate as datetime
	, @PeriodInterval as tinyint
	, @PeriodStartDate as datetime OUTPUT
	, @PeriodEndDate as datetime OUTPUT
	, @PeriodIndex as int OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @FiscalYear as int
	SELECT
		@PeriodIndex = CASE 
			WHEN @PeriodInterval = 0 THEN AIMDays.FYPeriod_Days
			WHEN @PeriodInterval = 1 THEN AIMDays.FYPeriod_Weeks
			WHEN @PeriodInterval = 2 THEN AIMDays.FYPeriod_Months
			WHEN @PeriodInterval = 3 THEN AIMDays.FYPeriod_Quarters
			WHEN @PeriodInterval = 4 THEN AIMDays.FYPeriod_544
			WHEN @PeriodInterval = 5 THEN AIMDays.FYPeriod_454
			WHEN @PeriodInterval = 6 THEN AIMDays.FYPeriod_445
			WHEN @PeriodInterval = 7 THEN AIMDays.FYPeriod_4Weeks
		END
		, @FiscalYear = AIMYears.FiscalYear
	FROM AIMDays 
	INNER JOIN AIMYears ON (AIMDays.FYDate BETWEEN AIMYears.FYStartDate AND AIMYears.FYEndDate)
	WHERE CONVERT ( nvarchar, AIMDays.FYDate, 101 ) = CONVERT (nvarchar, @TargetDate, 101 )
	GROUP BY 
 		AIMYears.FiscalYear
		, CASE 
			WHEN @PeriodInterval = 0 THEN AIMDays.FYPeriod_Days
			WHEN @PeriodInterval = 1 THEN AIMDays.FYPeriod_Weeks
			WHEN @PeriodInterval = 2 THEN AIMDays.FYPeriod_Months
			WHEN @PeriodInterval = 3 THEN AIMDays.FYPeriod_Quarters
			WHEN @PeriodInterval = 4 THEN AIMDays.FYPeriod_544
			WHEN @PeriodInterval = 5 THEN AIMDays.FYPeriod_454
			WHEN @PeriodInterval = 6 THEN AIMDays.FYPeriod_445
			WHEN @PeriodInterval = 7 THEN AIMDays.FYPeriod_4Weeks
		END 
	ORDER BY 
 		AIMYears.FiscalYear

	SELECT 		
		@PeriodStartDate = MIN(AIMDays.FYDate), 
		@PeriodEndDate = MAX(AIMDays.FYDate)
	FROM AIMDays 
	INNER JOIN AIMYears ON (AIMDays.FYDate BETWEEN AIMYears.FYStartDate AND AIMYears.FYEndDate)
	WHERE 
		AIMDays.FYPeriod_Days = CASE WHEN @PeriodInterval = 0 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Days END
	AND	AIMDays.FYPeriod_Weeks = CASE WHEN @PeriodInterval = 1 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Weeks END
	AND	AIMDays.FYPeriod_Months = CASE WHEN @PeriodInterval = 2 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Months END
	AND	AIMDays.FYPeriod_Quarters = CASE WHEN @PeriodInterval = 3 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Quarters END
	AND	AIMDays.FYPeriod_544 = CASE WHEN @PeriodInterval = 4 THEN @PeriodIndex ELSE AIMDays.FYPeriod_544 END
	AND	AIMDays.FYPeriod_454 = CASE WHEN @PeriodInterval = 5 THEN @PeriodIndex ELSE AIMDays.FYPeriod_454 END
	AND	AIMDays.FYPeriod_445 = CASE WHEN @PeriodInterval = 6 THEN @PeriodIndex ELSE AIMDays.FYPeriod_445 END
	AND	AIMDays.FYPeriod_4Weeks = CASE WHEN @PeriodInterval = 7 THEN @PeriodIndex ELSE AIMDays.FYPeriod_4Weeks END
	AND AIMYears.FiscalYear = @FiscalYear

	RETURN 1

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

