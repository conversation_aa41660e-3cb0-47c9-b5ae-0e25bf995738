SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UnOrderedItems_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UnOrderedItems_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UnOrderedItems_Sp
**	Desc: 
**
**	Returns: 1) @found - Can be Zero
**               2) 0 - No Data Found
**             
**	Values:  Recordset - PODetail
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
    
CREATE PROCEDURE AIM_UnOrderedItems_Sp
(
  	@ById       						nvarchar(12),
  	@VnId       						nvarchar(12),
        @Assort     						nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @ById IS NULL
BEGIN
    RETURN 0
END

IF @VnId IS NULL
BEGIN
	RETURN 0
END
 
IF @Assort IS NULL
BEGIN
	RETURN 0
END

SELECT ItStat, Item, ItDesc, Lcid, VSOQ = ConvFactor, UOM, ConvFactor, IMin, 
IMax, Weight, Cube, Cost, BuyingUOM, Packrounding 
FROM Item 
WHERE ItStat = 'U' 
AND VnId = @VnId 
AND Assort = @Assort 
AND ById = @ById
ORDER BY Item, LcId

RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

