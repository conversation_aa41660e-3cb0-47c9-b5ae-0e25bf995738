SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects 
		where id = object_id(N'[dbo].[AIM_DxPO_Sp]') 
		and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxPO_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxPO_Sp
**	Desc: Gets Purchase Order data from the AIMPO table.
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:	Description:
**    ---------- --------------	-----------------------------------------------
**    June-02-2004 S.Uddanti	Commented out the  Vnid and Assort Join between
**				Item and PoDetail table.
**    Sep-27-2005  S.Uddanti	Changed the Join to Left OuterJoin to include special
**				Items
*******************************************************************************/
     
CREATE PROCEDURE AIM_DxPO_Sp
(
  	@ById       					nvarchar(12),
  	@VnId       					nvarchar(12),
  	@Assort     					nvarchar(12),
  	@LcId       					nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	SET NOCOUNT ON
	 
	-- Determine the scope of the select
	IF @LcId = 'All'
	BEGIN
		SELECT PODetail.OrdStatus, 
			PODetail.POSeqID, PODetail.POType, 
			PODetail.VnId, PODetail.Assort, 
			PODetail.Lcid, PODetail.Item, PODetail.ItDesc, 
			PODetail.Cost, 
			Item.Price,
			PODetail.VSOQ, 
			PODetail.UOM,PODetail.BuyingUOM,PODetail.ConvFactor,
			PODetail.IsDate, PODetail.DuDate, PODetail.Zone, 
			PODetail.ById, PODetail.Original_VSOQ
		FROM PODetail 
		Left Outer JOIN Item ON PODetail.Item = Item.Item
			AND PODetail.LcID = Item.LcID
			--AND PODetail.VnID = Item.VnID
			--AND PODetail.Assort = Item.Assort
		WHERE PODetail.ById = @ById
			AND PODetail.VnId = @VnId
			AND PODetail.Assort = @Assort
			AND PODetail.OrdStatus = 'R'
		ORDER BY PODetail.LcId, PODetail.VnId, 
		PODetail.Zone, PODetail.Item
	                
	END
	ELSE
	BEGIN
		SELECT PODetail.OrdStatus,
			PODetail.POSeqID, PODetail.POType, 
			PODetail.VnId, PODetail.Assort, 
			PODetail.Lcid, PODetail.Item, PODetail.ItDesc, 
			PODetail.Cost, 
			Item.Price,
			PODetail.VSOQ, 
			PODetail.UOM,PODetail.BuyingUOM,PODetail.ConvFactor,
			PODetail.IsDate, PODetail.DuDate, PODetail.Zone, 
			PODetail.ById, PODetail.Original_VSOQ
		FROM PODetail 
		Left Outer JOIN Item ON PODetail.Item = Item.Item
			AND PODetail.LcID = Item.LcID
			--AND PODetail.VnID = Item.VnID
			--AND PODetail.Assort = Item.Assort
		WHERE PODetail.ById = @ById
			AND PODetail.VnId = @VnId
			AND PODetail.Assort = @Assort
			AND PODetail.LcId = @LcId
			AND PODetail.OrdStatus = 'R'
		ORDER BY PODetail.LcId, PODetail.VnId, 
		PODetail.Zone, PODetail.Item
	END
	RETURN @@ROWCOUNT

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

