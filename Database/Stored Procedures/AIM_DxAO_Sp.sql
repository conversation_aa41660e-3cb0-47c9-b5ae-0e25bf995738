SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxAO_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxAO_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON>ES, INC., AND SHALL NOT BE COPIED, 
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxAO_Sp
**	Desc: Fetches allocated Re-stock order tables 
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**               4) -3 - Duplicate File Name
**               5) -4 - Invalid File Name
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/06/02
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Updated by:		Description:
**	---------- 	------------	-----------------------------------------------
**	2004/1/20  	A.Stocksdale		Commented out check for AdjustedAllocQty > 0.
**		                		The outbound order should be identical to the  
**                              inbound data set.	
**	2004/1/26  	S.Uddanti		Converted the TxnDate and  AllocationDate into  
**								datetime data type.
**	2004/7/15  	S.Uddanti		Check for AllocException Process and release partial orders
**								if it is not order level exception process								
**	2005/01/04	A.Stocksdale	Added two new fields: AllocUOM & AllocConvFactor
**	2005/11/18	S.Uddanti	Add Order by clause to the select statement
*******************************************************************************/ 
CREATE PROCEDURE AIM_DxAO_Sp
(
  	@IgnoreRevStatus AS TINYINT = 0
	, @ReviewerID AS NVARCHAR(255) = 'ALL'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
  
	SET NOCOUNT ON

	DECLARE @ExceptionProcess AS TINYINT

	BEGIN
		SELECT @ExceptionProcess = COALESCE(AllocExceptionProcess, 0) FROM SysCtrl 
	END
	IF (@ReviewerID IS NULL OR @ReviewerID = '')
	BEGIN
		SET @ReviewerID = 'ALL'
	END

	-- Determine the scope of the select
	IF @IgnoreRevStatus = 0	--fetch all allocated, not allocated, allocated with exceptions and released
	BEGIN
		IF @ReviewerID = 'ALL'
		BEGIN
			SELECT AIMAO.OrdNbr
				, AOD_Dest.LcID As LcID_Destination
				, AIMAO.OriginCode, AIMAO.UserInitials
				, AIMAO.ReviewerID
				, AOD_Source.OrdType
				, AOD_Source.LineNbr
				, AOD_Source.LcID As LcID_Source
				, AOD_Source.Item, AOD_Source.ItDesc
				, AOD_Source.UOM
				, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
				, AOD_Dest.RequestedQty
				, AOD_Dest.AdjustedAllocQty As ItemAllocQty
				, AOD_Source.AdjustedAllocQty
				, AOD_Source.Cost
				, CONVERT(datetime, AOD_Source.TxnDate, 112) As TxnDate
				, AOD_Source.TxnTimeOfDay
				, CONVERT(datetime, AOD_Source.AllocationDate, 111) As AllocationDate
				, AOD_Source.AllocationTimeOfDay
			FROM AIMAO 
			INNER JOIN AIMAODetail AOD_Source
				ON AIMAO.OrdNbr = AOD_Source.OrdNbr
			INNER JOIN AIMAODetail AOD_Dest
				ON AIMAO.OrdNbr = AOD_Dest.OrdNbr
				AND AOD_Source.LineNbr = AOD_Dest.LineNbr
			WHERE AIMAO.OrderStatus IN 
				(10, 11, 12, 20, 22)	-- Alloc; Not Alloc; Alloc w/ Except.; Rel.; Released w/ Except.
				AND AOD_Source.LType = 'S'	-- Source
				AND AOD_Dest.LType = 'D'
--				AND AOD_Source.AdjustedAllocQty > 0
			GROUP BY AIMAO.OrdNbr
				, AOD_Dest.LcID, AOD_Source.LineNbr, AOD_Source.LcID
				, AOD_Source.Item
				, AIMAO.OriginCode, AIMAO.UserInitials
				, AIMAO.ReviewerID
				, AOD_Source.OrdType
				, AOD_Source.ItDesc, AOD_Source.UOM
				, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
				, AOD_Dest.RequestedQty
				, AOD_Dest.AdjustedAllocQty
				, AOD_Source.AdjustedAllocQty
				, AOD_Source.Cost
				, AOD_Source.TxnDate, AOD_Source.TxnTimeOfDay
				, AOD_Source.AllocationDate, AOD_Source.AllocationTimeOfDay
			ORDER BY AIMAO.OrdNbr
				, AOD_Dest.LcID
		END
		ELSE -- same statement As above but with additional where reviewerid=
		BEGIN
			SELECT AIMAO.OrdNbr
				, AOD_Dest.LcID As LcID_Destination
				, AIMAO.OriginCode, AIMAO.UserInitials
				, AIMAO.ReviewerID
				, AOD_Source.OrdType
				, AOD_Source.LineNbr
				, AOD_Source.LcID As LcID_Source
				, AOD_Source.Item, AOD_Source.ItDesc
				, AOD_Source.UOM
				, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
				, AOD_Dest.RequestedQty
				, AOD_Dest.AdjustedAllocQty As ItemAllocQty
				, AOD_Source.AdjustedAllocQty
				, AOD_Source.Cost
				, CONVERT(datetime, AOD_Source.TxnDate, 112) As TxnDate, AOD_Source.TxnTimeOfDay
				, CONVERT(datetime, AOD_Source.AllocationDate, 111) As AllocationDate
				, AOD_Source.AllocationTimeOfDay
			FROM AIMAO_OrderInfo
			INNER JOIN AIMAO 
				ON AIMAO_OrderInfo.OrdNbr = AIMAO.OrdNbr
			INNER JOIN AIMAODetail AOD_Source
				ON AIMAO_OrderInfo.OrdNbr = AOD_Source.OrdNbr
				AND AIMAO.OrdNbr = AOD_Source.OrdNbr
			INNER JOIN AIMAODetail AOD_Dest
				ON AIMAO_OrderInfo.OrdNbr = AOD_Dest.OrdNbr
				AND AIMAO.OrdNbr = AOD_Dest.OrdNbr
				AND AOD_Source.LineNbr = AOD_Dest.LineNbr
			WHERE AIMAO.OrderStatus IN 
				(10, 11, 12, 20, 22)	-- Alloc; Not Alloc; Alloc w/ Except.; Rel.; Released w/ Except.
				AND AOD_Source.LType = 'S'	-- Source
				AND AOD_Dest.LType = 'D'
-- 				AND AOD_Source.AdjustedAllocQty > 0
				AND AIMAO.ReviewerID = @ReviewerID	-- Different from preceding query
			GROUP BY AIMAO.OrdNbr
				, AOD_Dest.LcID, AOD_Source.LineNbr, AOD_Source.LcID
				, AOD_Source.Item
				, AIMAO.OriginCode, AIMAO.UserInitials
				, AIMAO.ReviewerID
				, AOD_Source.OrdType
				, AOD_Source.ItDesc, AOD_Source.UOM
				, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
				, AOD_Dest.RequestedQty
				, AOD_Dest.AdjustedAllocQty
				, AOD_Source.AdjustedAllocQty
				, AOD_Source.Cost
				, AOD_Source.TxnDate, AOD_Source.TxnTimeOfDay
				, AOD_Source.AllocationDate, AOD_Source.AllocationTimeOfDay
			ORDER BY AIMAO.OrdNbr
				, AOD_Dest.LcID
		END                
	END	
	ELSE
	BEGIN
		IF @ReviewerID = 'ALL'
		BEGIN
			IF @ExceptionProcess = 0 
				BEGIN
				-- fetch only released
				SELECT AIMAO.OrdNbr
					, AOD_Dest.LcID As LcID_Destination
					, AIMAO.OriginCode, AIMAO.UserInitials
					, AIMAO.ReviewerID
					, AOD_Source.OrdType
					, AOD_Source.LineNbr
					, AOD_Source.LcID As LcID_Source
					, AOD_Source.Item, AOD_Source.ItDesc
					, AOD_Source.UOM
					, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
					, AOD_Dest.RequestedQty
					, AOD_Dest.AdjustedAllocQty As ItemAllocQty
					, AOD_Source.AdjustedAllocQty
					, AOD_Source.Cost
					, CONVERT(datetime, AOD_Source.TxnDate, 112) As TxnDate, AOD_Source.TxnTimeOfDay
					, CONVERT(datetime, AOD_Source.AllocationDate, 111) As AllocationDate
					, AOD_Source.AllocationTimeOfDay
				FROM AIMAO_OrderInfo
				INNER JOIN AIMAO 
					ON AIMAO_OrderInfo.OrdNbr = AIMAO.OrdNbr
				INNER JOIN AIMAODetail AOD_Source
					ON AIMAO_OrderInfo.OrdNbr = AOD_Source.OrdNbr
					AND AIMAO.OrdNbr = AOD_Source.OrdNbr
				INNER JOIN AIMAODetail AOD_Dest
					ON AIMAO_OrderInfo.OrdNbr = AOD_Dest.OrdNbr
					AND AIMAO.OrdNbr = AOD_Dest.OrdNbr
					AND AOD_Source.LineNbr = AOD_Dest.LineNbr
				WHERE AIMAO.OrderStatus IN 
					(20, 22)	-- RELEASED; RELEASED WITH EXCEPTIONS
					AND AOD_Source.LType = 'S'	-- Source
					AND AOD_Dest.LType = 'D'
	-- 				AND AOD_Source.AdjustedAllocQty > 0
				GROUP BY AIMAO.OrdNbr
					, AOD_Dest.LcID, AOD_Source.LineNbr, AOD_Source.LcID
					, AOD_Source.Item
					, AIMAO.OriginCode, AIMAO.UserInitials
					, AIMAO.ReviewerID
					, AOD_Source.OrdType
					, AOD_Source.ItDesc, AOD_Source.UOM
					, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
					, AOD_Dest.RequestedQty
					, AOD_Dest.AdjustedAllocQty
					, AOD_Source.AdjustedAllocQty
					, AOD_Source.Cost
					, AOD_Source.TxnDate, AOD_Source.TxnTimeOfDay
					, AOD_Source.AllocationDate, AOD_Source.AllocationTimeOfDay
				ORDER BY AIMAO.OrdNbr
					, AOD_Dest.LcID
				END
				ELSE
				BEGIN
					SELECT AIMAO.OrdNbr
					, AOD_Dest.LcID As LcID_Destination
					, AIMAO.OriginCode, AIMAO.UserInitials
					, AIMAO.ReviewerID
					, AOD_Source.OrdType
					, AOD_Source.LineNbr
					, AOD_Source.LcID As LcID_Source
					, AOD_Source.Item, AOD_Source.ItDesc
					, AOD_Source.UOM
					, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
					, AOD_Dest.RequestedQty
					, AOD_Dest.AdjustedAllocQty As ItemAllocQty
					, AOD_Source.AdjustedAllocQty
					, AOD_Source.Cost
					, CONVERT(datetime, AOD_Source.TxnDate, 112) As TxnDate, AOD_Source.TxnTimeOfDay
					, CONVERT(datetime, AOD_Source.AllocationDate, 111) As AllocationDate
					, AOD_Source.AllocationTimeOfDay
				FROM AIMAO_OrderInfo
				INNER JOIN AIMAO 
					ON AIMAO_OrderInfo.OrdNbr = AIMAO.OrdNbr
				INNER JOIN AIMAODetail AOD_Source
					ON AIMAO_OrderInfo.OrdNbr = AOD_Source.OrdNbr
					AND AIMAO.OrdNbr = AOD_Source.OrdNbr
				INNER JOIN AIMAODetail AOD_Dest
					ON AIMAO_OrderInfo.OrdNbr = AOD_Dest.OrdNbr
					AND AIMAO.OrdNbr = AOD_Dest.OrdNbr
					AND AOD_Source.LineNbr = AOD_Dest.LineNbr
					AND AIMAO.LcID = AOD_Dest.LcID
				WHERE AIMAO.OrderStatus IN 
					(20, 22)	-- RELEASED; RELEASED WITH EXCEPTIONS
					AND AOD_Source.LType = 'S'	-- Source
					AND AOD_Dest.LType = 'D'	-- Destination
	-- 				AND AOD_Source.AdjustedAllocQty > 0
				GROUP BY AIMAO.OrdNbr
					, AOD_Dest.LcID, AOD_Source.LineNbr, AOD_Source.LcID
					, AOD_Source.Item
					, AIMAO.OriginCode, AIMAO.UserInitials
					, AIMAO.ReviewerID
					, AOD_Source.OrdType
					, AOD_Source.ItDesc, AOD_Source.UOM
					, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
					, AOD_Dest.RequestedQty
					, AOD_Dest.AdjustedAllocQty
					, AOD_Source.AdjustedAllocQty
					, AOD_Source.Cost
					, AOD_Source.TxnDate, AOD_Source.TxnTimeOfDay
					, AOD_Source.AllocationDate, AOD_Source.AllocationTimeOfDay
				ORDER BY AIMAO.OrdNbr
					, AOD_Dest.LcID
				END
		END
		ELSE -- same statement As above but with additional where reviewerid=
		BEGIN	
			IF @ExceptionProcess =0 
				BEGIN	
					-- fetch only released
					SELECT AIMAO.OrdNbr
						, AOD_Dest.LcID As LcID_Destination
						, AIMAO.OriginCode, AIMAO.UserInitials
						, AIMAO.ReviewerID
						, AOD_Source.OrdType
						, AOD_Source.LineNbr
						, AOD_Source.LcID As LcID_Source
						, AOD_Source.Item, AOD_Source.ItDesc
						, AOD_Source.UOM
						, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
						, AOD_Dest.RequestedQty
						, AOD_Dest.AdjustedAllocQty As ItemAllocQty
						, AOD_Source.AdjustedAllocQty
						, AOD_Source.Cost
						, CONVERT(datetime, AOD_Source.TxnDate, 112) As TxnDate, AOD_Source.TxnTimeOfDay
						, CONVERT(datetime, AOD_Source.AllocationDate, 111) As AllocationDate
						, AOD_Source.AllocationTimeOfDay
					FROM AIMAO_OrderInfo
					INNER JOIN AIMAO 
						ON AIMAO_OrderInfo.OrdNbr = AIMAO.OrdNbr
					INNER JOIN AIMAODetail AOD_Source
						ON AIMAO_OrderInfo.OrdNbr = AOD_Source.OrdNbr
						AND AIMAO.OrdNbr = AOD_Source.OrdNbr
					INNER JOIN AIMAODetail AOD_Dest
						ON AIMAO_OrderInfo.OrdNbr = AOD_Dest.OrdNbr
						AND AIMAO.OrdNbr = AOD_Dest.OrdNbr
						AND AOD_Source.LineNbr = AOD_Dest.LineNbr
					WHERE AIMAO.OrderStatus IN 
						(20, 22)	-- RELEASED; RELEASED WITH EXCEPTIONS
						AND AOD_Source.LType = 'S'	-- Source
						AND AOD_Dest.LType = 'D'
			-- 				AND AOD_Source.AdjustedAllocQty > 0
						AND AIMAO.ReviewerID = @ReviewerID	-- Different from preceding query
					GROUP BY AIMAO.OrdNbr
						, AOD_Dest.LcID, AOD_Source.LineNbr, AOD_Source.LcID
						, AOD_Source.Item
						, AIMAO.OriginCode, AIMAO.UserInitials
						, AIMAO.ReviewerID
						, AOD_Source.OrdType
						, AOD_Source.ItDesc, AOD_Source.UOM
						, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
						, AOD_Dest.RequestedQty
						, AOD_Dest.AdjustedAllocQty
						, AOD_Source.AdjustedAllocQty
						, AOD_Source.Cost
						, AOD_Source.TxnDate, AOD_Source.TxnTimeOfDay
						, AOD_Source.AllocationDate, AOD_Source.AllocationTimeOfDay
					ORDER BY AIMAO.OrdNbr
						, AOD_Dest.LcID
					END
				ELSE
				BEGIN
					-- fetch only released
					SELECT AIMAO.OrdNbr
						, AOD_Dest.LcID As LcID_Destination
						, AIMAO.OriginCode, AIMAO.UserInitials
						, AIMAO.ReviewerID
						, AOD_Source.OrdType
						, AOD_Source.LineNbr
						, AOD_Source.LcID As LcID_Source
						, AOD_Source.Item, AOD_Source.ItDesc
						, AOD_Source.UOM
						, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
						, AOD_Dest.RequestedQty
						, AOD_Dest.AdjustedAllocQty As ItemAllocQty
						, AOD_Source.AdjustedAllocQty
						, AOD_Source.Cost
						, CONVERT(datetime, AOD_Source.TxnDate, 112) As TxnDate, AOD_Source.TxnTimeOfDay
						, CONVERT(datetime, AOD_Source.AllocationDate, 111) As AllocationDate
						, AOD_Source.AllocationTimeOfDay
					FROM AIMAO_OrderInfo
					INNER JOIN AIMAO 
						ON AIMAO_OrderInfo.OrdNbr = AIMAO.OrdNbr
					INNER JOIN AIMAODetail AOD_Source
						ON AIMAO_OrderInfo.OrdNbr = AOD_Source.OrdNbr
						AND AIMAO.OrdNbr = AOD_Source.OrdNbr
					INNER JOIN AIMAODetail AOD_Dest
						ON AIMAO_OrderInfo.OrdNbr = AOD_Dest.OrdNbr
						AND AIMAO.OrdNbr = AOD_Dest.OrdNbr
						AND AOD_Source.LineNbr = AOD_Dest.LineNbr
					WHERE AIMAO.OrderStatus IN 
						(20, 22)	-- RELEASED; RELEASED WITH EXCEPTIONS
						AND AOD_Source.LType = 'S'	-- Source
						AND AOD_Dest.LType = 'D'
						AND AOD_Dest.LcId in (SELECT LcId FROM AIMAO WHERE ORDERSTATUS IN (20, 22))
			-- 				AND AOD_Source.AdjustedAllocQty > 0
						AND AIMAO.ReviewerID = @ReviewerID	-- Different from preceding query
					GROUP BY AIMAO.OrdNbr
						, AOD_Dest.LcID, AOD_Source.LineNbr, AOD_Source.LcID
						, AOD_Source.Item
						, AIMAO.OriginCode, AIMAO.UserInitials
						, AIMAO.ReviewerID
						, AOD_Source.OrdType
						, AOD_Source.ItDesc, AOD_Source.UOM
						, AOD_Source.AllocUOM, AOD_Source.AllocConvFactor
						, AOD_Dest.RequestedQty
						, AOD_Dest.AdjustedAllocQty
						, AOD_Source.AdjustedAllocQty
						, AOD_Source.Cost
						, AOD_Source.TxnDate, AOD_Source.TxnTimeOfDay
						, AOD_Source.AllocationDate, AOD_Source.AllocationTimeOfDay
					ORDER BY AIMAO.OrdNbr
						, AOD_Dest.LcID
				END
		END
	END

	RETURN @@ROWCOUNT
END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

