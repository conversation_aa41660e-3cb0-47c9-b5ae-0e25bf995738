SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxBulkInsert_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxBulkInsert_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxBulkInsert_Sp
**	Desc: Called from within the AIM_Dx*sp stored procedures. 
**	      This procedure (a) validates file names  (b)transfers data from 
**            file to table.
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**               4) -3 - Duplicate File Name
**               5) -4 - Invalid File Name
**	Values:  Row#, Update#, Purge#, Error#
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Updated by:	Description:
**	---------- ------------	-----------------------------------------------
**								
*******************************************************************************/
 
CREATE PROCEDURE AIM_DxBulkInsert_Sp
(
  	@FileName nvarchar(255),
	@TransactionCode nvarchar(10),
	@TransactionTable nvarchar(255),
	@FieldDelimiter	nvarchar(10) = '\t'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	SET NOCOUNT ON

	/* START VARIABLE DECLARATION */  
	-- System Control Table
	DECLARE @DxPath nvarchar(255)
	DECLARE @Unicode_Option nvarchar(10)
	DECLARE @Cmd nvarchar(255)
	DECLARE @InsertCounter int
	DECLARE @RtnCode int
	DECLARE @UpdateCounter int
	DECLARE @FetchStatus int

	/* Start file validation */
	-- Check for invalid arguments
	IF @FileName IS NULL
	BEGIN
		RETURN -4	-- Invalid filename
	END
	IF @TransactionCode IS NULL
	OR @TransactionTable IS NULL
	BEGIN
		RETURN -1	-- No data found
	END
	-- Default delimiter to tabs
	IF @FieldDelimiter IS NULL
	OR @FieldDelimiter = ''
	BEGIN
		SET @FieldDelimiter = '\t'
	END
	-- Check for a duplicate file name
	EXEC @RtnCode = AIM_VerifyDxFile_Sp @TransactionCode, @FileName
	IF @RtnCode = 0     -- Fail
	BEGIN
		RETURN -3        -- Duplicate File Name
	END

	-- Get the Control Variables from the System Control Table
	SELECT @DxPath = RTRIM(DxPath),
		@Unicode_Option = CASE UnicodeOption
			WHEN 'N' THEN 'char'
			WHEN 'Y' THEN 'widechar'
			ELSE 'char'
			END
	FROM SysCtrl
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	-- Append a '\' IF needed
	IF RIGHT(@DxPath, 1) <> '\'
	BEGIN
		SELECT @DxPath = @DxPath + '\'
	END
		
	SELECT @FileName = @DxPath + @FileName
	/* End file validation */

	-- Bulk Load the requested table
	SELECT @Cmd = 'BULK INSERT ' + @TransactionTable
		+ ' FROM ' + char(39) + @FileName + char(39) 
		+ ' WITH (FIELDTERMINATOR = ' + char(39) + @FieldDelimiter + char(39) 
		+ ', DATAFILETYPE = ' + char(39) + @Unicode_Option + char(39) 
		+ ')'
	EXECUTE (@Cmd)

	RETURN @@ERROR

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

