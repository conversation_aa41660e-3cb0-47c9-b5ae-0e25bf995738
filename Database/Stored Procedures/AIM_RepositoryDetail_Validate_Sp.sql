SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RepositoryDetail_Validate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RepositoryDetail_Validate_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_RepositoryDetail_Validate_Sp
**	Desc: Checks if data exists in AIMForecastRepositoryDetail table
**
**	Returns: 0 Not Exists 1 Exists
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 	2005-05-13  Srinivas Uddanti	Increase the variable size to bigint
*******************************************************************************/

CREATE PROCEDURE AIM_RepositoryDetail_Validate_Sp
(
	@FcstId                  nvarchar(12)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @RecordCount AS 				bigint

SET NOCOUNT ON
select @RecordCount =count(*) from
 forecastrepositorydetail FRD,forecastrepository FR
where FRD.RepositoryKey =FR.RepositoryKey
and FR.FcstId =@FcstId 

If @RecordCount =0 
  Begin
  	Return 0
  End
IF @RecordCount >0 
   Begin
	Return 1
   End

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
