SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_GetEq_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_ProductionConstraint_GetEq_Sp
**	Desc:	Gets Production Constraint data
**		Derived from AIM_Forecast_GetEq_Sp
**	Parameters:
**		@ConstraintID		alphanumeric
**	Returns: 
**		 0 --> Successful (includes a recordset -- AIMProductionConstraint.*)
**		-1 --> No Data Found
**		-2 --> SQL Error
** 		
**	Auth:	Annalakshmi Stocksdale
**	Date:	2002/12/10
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:		 Description:
**	---------- 	------------ ------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_ProductionConstraint_GetEq_Sp
(
	@ConstraintID nvarchar(30)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	DECLARE @found int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @ConstraintID IS NULL
	BEGIN
		RETURN -1
	END
	
	-- Get data    	
	SELECT ConstraintID, ConstraintType, ConstraintDesc, CycleDays, RatioType
    	FROM AIMProductionConstraint
    	WHERE ConstraintID = @ConstraintID
	
	SELECT @found = @@rowcount
	
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE IF @found <= 0
	BEGIN
		RETURN -1  -- ERROR (NO DATA FOUND)
	END
	ELSE
	BEGIN
		RETURN 0  -- SUCCESSFUL
	END		

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

