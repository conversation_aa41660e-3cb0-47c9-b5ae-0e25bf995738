SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHsDelete_sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHsDelete_sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemHsDelete_Sp
**	Desc: Delete purged History and Item records from the AIM database.
**
**	Returns: 1) 1 - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 	09/12/2003 Srinivas U	Modified code for SubsItem No Changes
*******************************************************************************/

CREATE PROCEDURE AIM_ItemHsDelete_sp
(
   @lcid 						nvarchar(12)  = 'ALL'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON
  
IF @lcid = 'ALL'
BEGIN
       -- Delete records from the history table
    	DELETE ItemHistory 
        FROM item, ItemHistory, ItStatus 
        WHERE item.lcid = ItemHistory.lcid   
        AND item.item = ItemHistory.item 
        AND item.itstat = ItStatus.Itstat
        AND ItStatus.CanPurge = 'Y'
    	AND Item.Oh + Item.OO = 0
    		
     		
        -- Delete records from the item table
       	DELETE item
        FROM item, ItStatus
        WHERE item.itstat = ItStatus.Itstat
        AND ItStatus.CanPurge = 'Y'
    	AND Item.Oh + Item.OO = 0
     
END
ELSE
BEGIN
        -- Delete records from the history table
       	DELETE ItemHistory 
        FROM item, ItemHistory, ItStatus
        WHERE item.lcid = ItemHistory.lcid   
        AND item.item = ItemHistory.item 
        AND item.itstat = ItStatus.Itstat
        AND ItStatus.CanPurge = 'Y'
        AND item.lcid = @lcid
    	AND Item.Oh + Item.OO = 0
    		
        -- Delete records from the item table
       	DELETE item
        FROM item, ItStatus
        WHERE item.itstat = ItStatus.Itstat
        AND ItStatus.CanPurge = 'Y'
        AND item.lcid = @lcid
        AND Item.Oh + Item.OO = 0
    	
END
RETURN 1		-- SUCCEED

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

