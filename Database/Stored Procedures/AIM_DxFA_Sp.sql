SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS ( SELECT * FROM dbo.sysobjects WHERE ID = object_id( N'[dbo].[AIM_DxFA_Sp]' ) AND OBJECTPROPERTY( ID, N'IsProcedure') = 1 )
	DROP PROCEDURE [dbo].[AIM_DxFA_Sp]
GO
/*******************************************************************************
**                       			NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS CONFIDENTIAL INFORMATION OF 
**	SSA GLOBAL TECHNOLOGIES, INC., AND SHALL NOT BE COPIED, USED, NOR DISCLOSED 
**	WITHOUT EXPRESS WRITTEN AUTHORIZATION.  
**	ALTHOUGH PUBLICATION IS NOT INTENDED, IN THE EVENT OF PUBLICATION, 
**	THE FOLLOWING NOTICE IS APPLICABLE:
**  	(c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**	SSA GLOBAL TECHNOLOGIES, INC.
********************************************************************************
********************************************************************************
**	Name: AIM_DxFA_Sp
**	Desc: Loads forecast adjustment records into the ForecastRepository_Log table
**	      from the transit table (AIMDxFA)
**	Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@FileLineCounter -- number of lines that were in the import file
**		@ReturnCode -- possible values:
**			1)  1 - Successful
**			2) -1 - No Data Found
**			3) -2 - SQL Error
**			4) -3 - Duplicate File Name
**			5) -4 - Invalid File Name
**	Auth:   Annalakshmi Stocksdale
**	Date:   2003/05/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
**	2004/10/25	A.Stocksdale	Updates to match Srinivas' Forecast Planner
*******************************************************************************/
 
CREATE PROCEDURE AIM_DxFA_Sp (
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT,
	@FileLineCounter int OUTPUT
)
-- WITH ENCRYPTION	-- Production use must be encrypted 
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	-- Start variable declaration 
	DECLARE @Return_Stat int

	-- Start variable initialization 
	SELECT 
		@InsertCounter = 0
		, @UpdateCounter = 0
		, @FileLineCounter = 0

	-- This is a data interface transaction <Transaction Code=FA>. 
	-- Bulk Load the <Transaction Table=AIMDxFA> from flat file 
	EXEC @Return_Stat = AIM_DxBulkInsert_Sp @FileName, 'FA', 'AIMDxFA', '\t'
		-- This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
	IF @Return_Stat <> 0
	BEGIN
 		RETURN @Return_Stat	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	SELECT @FileLineCounter = COUNT(*) FROM AIMDxFA

	BEGIN TRANSACTION	
		INSERT INTO ForecastRepository_Log (
			RepositoryKey
			, LcID
			, Item
			, IsPromoted
			, AdjustType
			, AdjustQty
			, OverrideEnabled
			, AdjustBegDate 
			, AdjustEndDate
			, AdjustUserID
			, AdjustDesc
			, AdjustDateTime
		) SELECT
			ForecastRepository.RepositoryKey
			, AIMDxFA.LcID
			, AIMDxFA.Item
			, 0			-- A new record is obviously not promoted to master yet
			, AIMDxFA.AdjustType
			, AIMDxFA.AdjustQty
			, CASE AIMDxFA.AdjustType WHEN 0 THEN 1 ELSE 0 END	--If Override, then enabled
			, AIMDxFA.AdjustBegDate
			, AIMDxFA.AdjustEndDate
			, ISNULL(AIMDxFA.UserID, 'DxFA')
			, ISNULL(AIMDxFA.RecordDesc, '')
			, GETDATE()
		FROM AIMDxFA
		INNER JOIN ForecastRepository ON
			AIMDxFA.FcstID = ForecastRepository.FcstID
		INNER JOIN ForecastRepositoryDetail ON
			ForecastRepository.RepositoryKey = ForecastRepositoryDetail.RepositoryKey AND
			AIMDxFA.Item = ForecastRepositoryDetail.Item AND
			AIMDxFA.LcID = ForecastRepositoryDetail.LcID AND
			AIMDxFA.AdjustBegDate = ForecastRepositoryDetail.FcstPdBegDate AND
			AIMDxFA.AdjustEndDate = ForecastRepositoryDetail.FcstPdEndDate
		
		SET @Return_Stat = @@ROWCOUNT
		SET @InsertCounter = @Return_Stat
		-- There are no updates for the Forecast Adjustment log.

		IF @Return_Stat <= 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN -1	-- No data found
		END

		-- Check to see if the VB app needs to generate error logs.
		IF @InsertCounter <> @FileLineCounter
		BEGIN
			-- Leave the AIMDxFA records that did not process, delete the rest
			DELETE FROM AIMDxFA
			FROM AIMDxFA
			INNER JOIN ForecastRepository ON
				AIMDxFA.FcstID = ForecastRepository.FcstID
			INNER JOIN ForecastRepositoryDetail ON
				ForecastRepository.RepositoryKey = ForecastRepositoryDetail.RepositoryKey AND
				AIMDxFA.Item = ForecastRepositoryDetail.Item AND
				AIMDxFA.LcID = ForecastRepositoryDetail.LcID AND
				AIMDxFA.AdjustBegDate = ForecastRepositoryDetail.FcstPdBegDate AND
				AIMDxFA.AdjustEndDate = ForecastRepositoryDetail.FcstPdEndDate
		END
		ELSE
		BEGIN
			-- Delete all records from the AIMDxFA Table
			TRUNCATE TABLE AIMDxFA
			DELETE FROM AIMDxFA		-- In case the user does not have Truncate auth.			
		END

	COMMIT TRANSACTION

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

