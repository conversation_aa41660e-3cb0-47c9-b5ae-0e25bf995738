SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BldItDefaults_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BldItDefaults_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BldItDefaults_Sp
**	Desc: Gets the Item Defaults
**
**	Returns: 1) 0
**	Values:  Recordset - ItDefaults
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   Author:	Description:
**    ---------- ------------	-----------------------------------------------
**  	09/24/2002 Wade Riza    Updated Class Code NVARCHAR(50)
*******************************************************************************/
     
CREATE PROCEDURE AIM_BldItDefaults_Sp
(
          @LcIdKey    						nvarchar(12) = 'All',
          @SALevel    						nvarchar(1)  = 'D'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @classoption    					int,
          @dft_velcode    					nvarchar(1),
          @lcid           					nvarchar(12),
          @lcid_f         					nvarchar(12),
          @class          					nvarchar(50),
          @optionid						nvarchar(6)
  
SET NOCOUNT ON 
  
-- Get the class option from the System Control Table
SELECT @classoption = ClassOption FROM SysCtrl
        
SELECT @dft_velcode = velcode, @optionid = optionid 
FROM item 
WHERE lcid = 'DEFAULT' 
AND item = 'DEFAULT'
  
IF @LcidKey = 'All'
BEGIN
        IF @classoption = 1
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class1 FROM item 
        END
    
        IF @classoption = 2
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class2 FROM item 
        END
        IF @classoption = 3
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class3 FROM item
        END
        IF @classoption = 4
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class4 FROM item
        END
END
ELSE
BEGIN
        IF @classoption = 1
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class1 FROM item WHERE lcid = @lcidkey
        END
        IF @classoption = 2
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class2 FROM item WHERE lcid = @lcidkey
        END
        IF @classoption = 3
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class3 FROM item WHERE lcid = @lcidkey
        END
        IF @classoption = 4
        BEGIN
            DECLARE ItemClassList CURSOR LOCAL FAST_FORWARD FOR
            SELECT distinct lcid, class4 FROM item WHERE lcid = @lcidkey
        END
        
END
OPEN ItemClassList
WHILE 1 = 1
BEGIN
        FETCH NEXT FROM ItemClassList INTO @lcid, @class
            
        IF @@FETCH_STATUS <> 0 BREAK
        SELECT @lcid_f = lcid FROM ItDefaults 
            WHERE lcid = @lcid
            AND class = @class
            
        IF @@rowcount = 0       -- Not found
        BEGIN
        
            IF @SALevel = 'D' 
            BEGIN
                INSERT INTO ItDefaults
                    (LcId, Class, SaId, VelCode, BuyStrat, ById, ReplenCost2, DSer, 
                    OptionId)
                    VALUES (@lcid, @class, rtrim(@lcid) + '.' + rtrim(@class), @dft_velcode, 'O', '' , 0.0, .95, @OptionId)
            END
            ELSE
            BEGIN
                INSERT INTO ItDefaults
                    (LcId, Class, SaId, VelCode, BuyStrat, ById, ReplenCost2, DSer, 
                    OptionId)
                    VALUES (@lcid, @class, rtrim(@class), @dft_velcode, 'O', '' , 0.0, .95, @OptionId)

            END
        
        END
        
END
CLOSE ItemClassList
DEALLOCATE ItemClassList

RETURN 0

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

