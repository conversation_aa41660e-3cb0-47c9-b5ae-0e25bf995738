if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewCycleAssort_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewCycleAssort_Sp ]
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ReviewCycleAssort_Sp 
**	Desc: Run Order Generation Process based on ReviewCycle and Assortment
**
**	Returns: 1)@rowcount - Can be Zero
**	Values:  
**              
**	Auth:   Randy Sadler	
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE   PROCEDURE AIM_ReviewCycleAssort_Sp 
(
	@RevCycleKey		varchar(8),
	@AssortKey		varchar(12)
)
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/
-- WITH ENCRYPTION	/* Production use must be encrypted */
SET NOCOUNT ON
DECLARE @RC 			int,
		@RevCycle		varchar(8),
		@ReviewTotal 	int,
		@LT_Total 		int,
		@RC_Total 		int,
		@PE_Total 		int,
		@VnId			varchar(12),
		@Assort			varchar(12),

-- Control Totals

		@x_ReviewTotal	int,
		@x_LT_Total		int,
		@x_RC_Total		int,
		@x_PE_Total 	int,
		@x_Vendors		int

-- Initialize Control Totals

select @x_Vendors = 0, @x_ReviewTotal = 0, @x_LT_Total = 0, @x_RC_Total = 0, @x_PE_Total = 0

-- Build the Cursor

DECLARE VendorList CURSOR LOCAL FAST_FORWARD FOR  
SELECT DISTINCT AIMVendors.VnId, AIMVendors.Assort, AIMVendors.RevCycle
    FROM AIMVendors with (NOLOCK)
    INNER JOIN Item WITH (NOLOCK) ON AIMVendors.VnId = Item.Vnid
	and AIMVendors.Assort = Item.Assort
    WHERE AIMVendors.Revcycle = @RevCycleKey
	AND AIMVendors.Assort = @AssortKey
    ORDER BY AIMVendors.VnId, AIMVendors.Assort

OPEN VendorList

FETCH NEXT FROM VendorList INTO @VnId, @Assort,@RevCycle

WHILE  @@FETCH_STATUS = 0 
BEGIN

	-- Initialize Parameters

	Select @RC = 0, @ReviewTotal = 0, @LT_Total = 0, @RC_Total = 0, @PE_Total = 0
 
	Exec AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', @ReviewTotal output, 
		@LT_Total output, @RC_Total output, @PE_Total output, 'Y'

	-- Update the Control Totals

	select @x_Vendors = @x_Vendors + 1, @x_ReviewTotal = @x_ReviewTotal + @ReviewTotal,
		@x_LT_Total	= @x_LT_Total + @LT_Total, @x_RC_Total = @x_RC_Total + @RC_Total,
		@x_PE_Total = @x_PE_Total + @PE_Total	 
	
	FETCH NEXT FROM VendorList INTO	@VnId, @Assort,@RevCycle

end

-- Output control Totals

select 'Vendors Reviewed' = @x_Vendors, 'Review Total' = @x_ReviewTotal, 
	'Lead Time Exceptions' = @x_LT_Total, 'Scheduled Reviews' = @x_RC_Total, 
	'Priority Exceptions' = @x_PE_Total

CLOSE VendorList
DEALLOCATE VendorList

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



