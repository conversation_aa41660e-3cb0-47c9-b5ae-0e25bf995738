SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMLocations_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMLocations_List_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMLocations_List_Sp
**	Desc: Retrieves AIMLocation data based on value passed.
**
**	Returns: 1)@rowcount - Can be Zero
**	Values:  AIMLocations
**              
**	Auth:   Randy Sadler	
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**    	Dec-07-2004 Srinivas Uddanti Modifed code as demand update is failing
*******************************************************************************/

CREATE PROCEDURE AIM_AIMLocations_List_Sp
(
          @LcIdOption						nvarchar(12) = 'All'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

IF @LcIdOption =  'All'
BEGIN
  	SELECT LcID, LName, LType, LStatus, LDivision, LRegion, LUserDefined, Last_FcstUpdCyc,
		PutAwayDays,ReplenCost,DemandSource,StkDate,LookBackPds,
		DmdScalingFactor,ScalingEffUntil,Dft_ById,LeadTimeSource
	FROM AIMLocations 
	ORDER BY LcId
END
ELSE
BEGIN
        SELECT LcID, LName, LType, LStatus, LDivision, LRegion, LUserDefined, Last_FcstUpdCyc,
		PutAwayDays,ReplenCost,DemandSource,StkDate,LookBackPds,
		DmdScalingFactor,ScalingEffUntil,Dft_ById,LeadTimeSource
	FROM AIMLocations 
	WHERE LcId = @LcIdOption
END
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

