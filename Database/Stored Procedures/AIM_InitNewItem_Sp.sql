SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InitNewItem_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_InitNewItem_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_InitNewItem_Sp
**	Desc: Intializes a new Item in the Item table.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_InitNewItem_Sp
(
        @ProcessOpt     				tinyint,  
                                                        -- 0 = Update Status Only
    	                                                -- 1 = Update Status and Insert PO Detail Record
  	@ById           				nvarchar(12),
  	@VnId           				nvarchar(12),
  	@Assort         				nvarchar(12),
  	@Lcid           				nvarchar(12),
  	@Item           				nvarchar(25),
   	@VSOQ           				int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE 
       	@ItDesc				nvarchar(30),
    	@Cost 				decimal(10,4),
    	@MDC                		nvarchar(12),
      	@RevCycle	        	nvarchar(8), 
        @POType	            		nvarchar(1), 
        @PackRounding	    		nvarchar(1), 
        @UOM		       	 	nvarchar(6), 
        @BuyingUOM	        	nvarchar(6), 
        @ConvFactor	        	smallint, 
        @IsDate	            		datetime, 
        @DuDate	            		datetime, 
        @Zone               		nvarchar(1),
        @POByZone           		nvarchar(1),
        @LeadTime       	    	int,
        @Accum_LT	           	int
	
SET NOCOUNT ON
 
-- Update the Item's Status Code
UPDATE item 
SET ItStat = 'N' 
WHERE LcId = @LcId 
AND Item = @Item
  
-- Insert a PO Detail Record
IF @ProcessOpt > 0
BEGIN
        -- Get required data from Item and Vendor Tables
        SELECT @ItDesc = ItDesc,
    		@Cost = Cost,
    		@POType = case 
        WHEN Item.MDC <> '' and Item.MDC <> Item.VnId THEN 'T'
        ELSE 'P'
        END,
            @PackRounding = Item.PackRounding,
            @UOM = Item.UOM,
            @BuyingUOM = Item.BuyingUOM,
            @ConvFactor = Item.ConvFactor,
            @Zone = substring(Item.BinLocation,1,1),
            @LeadTime = Item.LeadTime,
            @Accum_LT = Item.Accum_LT,
            @RevCycle = AIMVendors.RevCycle, 
            @POByZone = AIMVendors.POByZone 
        FROM Item
            INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId
                and Item.Assort = AIMVendors.Assort 
        WHERE Item.Lcid = @LcId 
        AND Item.Item = @Item
        
	-- Get the Issue Date and Due Date
        EXEC AIM_GetDate_Sp @IsDate output
        IF @Accum_LT = 0
            SELECT @DuDate = @IsDate + @LeadTime
        ELSE
            SELECT @DuDate = @IsDate + @Accum_Lt   
        
        EXEC AIM_POInsert_Sp  
            20,
            'R',
            @RevCycle,
            @ById,
            @VnId,
            @Assort,
            @LcId,
            @Item,
    	    @ItDesc,
            @POType,
            0,
            @PackRounding,
            @VSOQ,
            @VSOQ,
            @VSOQ,
            @UOM,
            @BuyingUOM,
            @ConvFactor,
            @IsDate,
            @DuDate,
            'N',
    	    @Cost, 
            @Zone
END
    
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

