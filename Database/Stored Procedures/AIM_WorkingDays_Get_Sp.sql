SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_WorkingDays_Get_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_WorkingDays_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_WorkingDays_Get_Sp
** 	Desc: Returns the Days status If they are Working or Non-Working.
**
** 	Returns: 1)@rowcount - Can be Zero
** 	Values:  
**              
** 	Auth:   Mohammed 
** 	Date:   2003/03/15
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:      Author:     	 Description:
** 	---------- ------------- ----------------------------------------------
** 	2004/01/26 A.Stocksdale	 Explicit declaration of SELECT columns
*******************************************************************************/
    
CREATE PROCEDURE AIM_WorkingDays_Get_Sp

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

	SELECT Day1, Day2, Day3, Day4, Day5, Day6, Day7 
	FROM AIMWorkingDays

	RETURN @@ROWCOUNT

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

