SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocationReviewSave_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocationReviewSave_Sp]
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_AllocationReviewSave_Sp
**	Desc:	Updates the AIMAODetail table with changes made from AllocationReview Screen
**				
**
**	Returns:	1)@Found - Can be Zero
**				2) 0 - No Data Found
**				3) -1 - Invalid Parameter
**				4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
**	Values:Returns number of rows updated
**              
**	Author:	Srinivas Uddnati
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	Oct-06-2003 	Srinivas Uddanti	Modifed code to add LineNbr to the
**						where clause 
*******************************************************************************/
CREATE PROCEDURE  AIM_AllocationReviewSave_Sp(
	
	@OrdNbr	nvarchar(12),
	@Item	nvarchar(25),
	@Ltype nvarchar(1),
	@Lcid nvarchar(12),
	@Qty  int,
	@LineNbr nvarchar(50)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
Declare 
@Found int
-- Validate the required parameters.
IF @OrdNbr IS NULL or
@Item is null or
@LType is null or
@Qty is null or
@Lcid is null

BEGIN
	RETURN -1	/* Invalid parameter */
END

Update AIMAODetail
set AdjustedALlocQty =@Qty
where
OrdNbr =@OrdNbr and 
Item =@Item and
LType =@LType and
Lcid =@Lcid and
LineNbr =@LineNbr

SELECT @Found = @@RowCount

-- Check for SQL Server errors. */
IF @@ERROR <> 0 
BEGIN
	RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
END
ELSE IF @Found <= 0 
BEGIN	
	RETURN -1 /* ERROR (No Valid data found in Database) */
END
ELSE
BEGIN
	RETURN @Found /* SUCCESSFUL */
END	

RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

