SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIM_FilterCriteria_Clear_Sp]') 
		AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_FilterCriteria_Clear_Sp]
GO

/*
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
** 	Name: AIM_FilterCriteria_Clear_Sp
** 	Purpose: Deletes the filters from the ForecastFilterCriteria table,
**		given the following criteria:
** 		1.  Forecast ID to match with the AIMForecast table
**		2.  Column name to filter on
**
** 		Version Number 	- 1.0
** 		Date Created 	- 2004/08/11
** 		Created By 		- Annalakshmi Stocksdale
**
********************************************************************************
** This file contains trade secrets of SSA Global. No part
** may be reproduced or transmitted in any form by any means or for any purpose
** without the express written permission of SSA Global.
********************************************************************************
**	Change History
********************************************************************************
**	Date:      	Author:      		Description:
**	---------- 	------------ 		-------------------------------------------
********************************************************************************
*/

CREATE PROCEDURE AIM_FilterCriteria_Clear_Sp (
	@FcstSetupKey int
	, @FilterColumn nvarchar (255)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @CheckValue as decimal(9, 1)
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	BEGIN
	  	RETURN -1
	END

	-- Validate the existence of the record in ItemHistory
	DELETE FROM ForecastFilterCriteria
	WHERE ForecastFilterCriteria.FcstSetupKey = @FcstSetupKey
	AND ForecastFilterCriteria.FilterColumn = CASE 
		WHEN @FilterColumn IS NULL THEN FilterColumn 
		ELSE @FilterColumn 
		END

	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	IF @Return_Stat > 0 RETURN 1
	ELSE RETURN @Return_Stat

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

