SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VelCodeCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VelCodeCtrl_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VelCodeCtrl_Sp
**	Desc: This stored procedure controls execution of the 
**          Velocity Code Update Procedure.
**
**	Returns: 1)@found - Can be Zero
**               2) 0 - No Data Found
**    
**	Values:  Recordset - Item
**              
**	Auth:   Randy Sadler
**	Date:   03/30/2000
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**      11/01/2002 Wade Riza    Added Validation
*******************************************************************************/

CREATE PROCEDURE AIM_VelCodeCtrl_Sp
(
  	@SelOpt     			nvarchar(1),
  	@LcIdOpt   	 		nvarchar(12) 
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE
    @CurLcId    			nvarchar(12),
    @Rows       			int,
    @TotalRows  			int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @SelOpt IS NULL
BEGIN
	RETURN 0
END

IF @LcIdOpt IS NULL
BEGIN
        RETURN 0
END

IF @SelOpt = 'G'
BEGIN
        EXECUTE @Rows = AIM_VelCode_Sp 'All'
        RETURN @Rows
END
IF @SelOpt = 'S'
BEGIN
        EXECUTE @Rows = AIM_VelCode_Sp @LcIdOpt
        RETURN @Rows
END
IF @SelOpt = 'A'
BEGIN
        -- Build the LcId List
        DECLARE LcId_Cursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT LcId
	FROM AIMLocations
	ORDER BY LcId
            
        OPEN LcId_Cursor
        
        -- Check for no locations
        IF @@CURSOR_ROWS = 0
            RETURN 0
        -- Clear counter
        SELECT @TotalRows = 0
        
        -- Loop through each location
        WHILE 1 = 1
        BEGIN
            FETCH NEXT FROM LcId_Cursor into @CurLcId
        
            -- Check for End-Of-Cursor
            IF @@FETCH_STATUS = -1      -- End of Cursor
                BREAK
            IF @@FETCH_STATUS = -2      -- Missing Value
                CONTINUE
        
            EXECUTE @Rows = AIM_VelCode_Sp @CurLcId
            SELECT @TotalRows = @TotalRows + @Rows
        END
        
        -- Clean Up
        CLOSE LcId_Cursor
        DEALLOCATE LcId_Cursor
        
        RETURN @TotalRows
        
END
RETURN 0
 
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

