if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_getSubRole_SP]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_getSubRole_SP]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_getSubRole_SP
**	Desc: TO GET SUBROLES
**
**	Returns: 1)  LIST OF ROLES
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/

CREATE PROCEDURE AIM_getSubRole_SP( @gRole0 as nvarchar(12),@@TOTROLES as nvarchar(4000)  output )
AS

declare @@gRole0 as nvarchar(12)
declare @@gRole1 as nvarchar(12)
declare @@gRole2 as nvarchar(12)
declare @@gRole3 as nvarchar(12)
declare @@gRole4 as nvarchar(12)
declare @@gRole5 as nvarchar(12)
declare @@gRole6 as nvarchar(12)
declare @@gRole7 as nvarchar(12)
declare @@gRole8 as nvarchar(12)
declare @@gRole9 as nvarchar(12)

SET @@gRole0 =@gRole0
SET @@TOTROLES = '''' +@gRole0 + ''','

DECLARE CUR_getRoles0 CURSOR FORWARD_ONLY READ_ONLY FOR

SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole0 and roleid<>'sa'
OPEN CUR_getRoles0
	FETCH NEXT FROM CUR_getRoles0 INTO @@gRole0
	WHILE @@FETCH_STATUS = 0
	BEGIN
		SET @@TOTROLES = @@TOTROLES + '''' +@@gRole0 + ''','

		DECLARE CUR_getRoles1 CURSOR FORWARD_ONLY READ_ONLY FOR
		SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole0
		OPEN CUR_getRoles1
			FETCH NEXT FROM CUR_getRoles1 INTO @@gRole1
			WHILE @@FETCH_STATUS = 0
			BEGIN
				SET @@TOTROLES = @@TOTROLES + '''' +@@gRole1 + ''','

				DECLARE CUR_getRoles2 CURSOR FORWARD_ONLY READ_ONLY FOR
				SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole1
				OPEN CUR_getRoles2
					FETCH NEXT FROM CUR_getRoles2 INTO @@gRole2
					WHILE @@FETCH_STATUS = 0
					BEGIN
						SET @@TOTROLES = @@TOTROLES + '''' +@@gRole2 + ''','

						DECLARE CUR_getRoles3 CURSOR FORWARD_ONLY READ_ONLY FOR
						SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole2
						OPEN CUR_getRoles3
							FETCH NEXT FROM CUR_getRoles3 INTO @@gRole3
							WHILE @@FETCH_STATUS = 0
							BEGIN
							
								SET @@TOTROLES = @@TOTROLES + '''' +@@gRole3 + ''','
								DECLARE CUR_getRoles4 CURSOR FORWARD_ONLY READ_ONLY FOR
								SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole3
								OPEN CUR_getRoles4
									FETCH NEXT FROM CUR_getRoles4 INTO @@gRole4
									WHILE @@FETCH_STATUS = 0
									BEGIN
										SET @@TOTROLES = @@TOTROLES + '''' +@@gRole4 + ''','
										DECLARE CUR_getRoles5 CURSOR FORWARD_ONLY READ_ONLY FOR
										SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole4
										OPEN CUR_getRoles5
											FETCH NEXT FROM CUR_getRoles5 INTO @@gRole5
											WHILE @@FETCH_STATUS = 0
											BEGIN
												SET @@TOTROLES = @@TOTROLES + '''' +@@gRole5 + ''','
												DECLARE CUR_getRoles6 CURSOR FORWARD_ONLY READ_ONLY FOR
												SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole5
												OPEN CUR_getRoles6
													FETCH NEXT FROM CUR_getRoles6 INTO @@gRole6
													WHILE @@FETCH_STATUS = 0
													BEGIN
														SET @@TOTROLES = @@TOTROLES + '''' +@@gRole6 + ''','
														DECLARE CUR_getRoles7 CURSOR FORWARD_ONLY READ_ONLY FOR
														SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole6
														OPEN CUR_getRoles7
															FETCH NEXT FROM CUR_getRoles7 INTO @@gRole7
															WHILE @@FETCH_STATUS = 0
															BEGIN
																SET @@TOTROLES = @@TOTROLES + '''' +@@gRole7 + ''','
																DECLARE CUR_getRoles8 CURSOR FORWARD_ONLY READ_ONLY FOR
																SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole7
																OPEN CUR_getRoles8
																	FETCH NEXT FROM CUR_getRoles8 INTO @@gRole8
																	WHILE @@FETCH_STATUS = 0
																	BEGIN
																		SET @@TOTROLES = @@TOTROLES + '''' +@@gRole8 + ''','
																		DECLARE CUR_getRoles9 CURSOR FORWARD_ONLY READ_ONLY FOR
																		SELECT  ROLEID FROM AIMROLES WHERE REPTOROLEID=@@gRole8
																		OPEN CUR_getRoles9
																			FETCH NEXT FROM CUR_getRoles9 INTO @@gRole9
																			WHILE @@FETCH_STATUS = 0
																			BEGIN
																				SET @@TOTROLES = @@TOTROLES + '''' +@@gRole9 + ''','
																			FETCH NEXT FROM CUR_getRoles9 INTO @@gRole9
																			END	
																			close CUR_getRoles9
																			DEALLOCATE CUR_getRoles9
																	FETCH NEXT FROM CUR_getRoles8 INTO @@gRole8
																	END	
																	close CUR_getRoles8
																	DEALLOCATE CUR_getRoles8
															FETCH NEXT FROM CUR_getRoles7 INTO @@gRole7
															END	
															close CUR_getRoles7
															DEALLOCATE CUR_getRoles7
													FETCH NEXT FROM CUR_getRoles6 INTO @@gRole6
													END	
													close CUR_getRoles6
													DEALLOCATE CUR_getRoles6
											FETCH NEXT FROM CUR_getRoles5 INTO @@gRole5
											END	
											close CUR_getRoles5
											DEALLOCATE CUR_getRoles5
									FETCH NEXT FROM CUR_getRoles4 INTO @@gRole4	
									END
									close CUR_getRoles4
									DEALLOCATE CUR_getRoles4
								FETCH NEXT FROM CUR_getRoles3 INTO @@gRole3			
							END
							close CUR_getRoles3
							DEALLOCATE CUR_getRoles3
					FETCH NEXT FROM CUR_getRoles2 INTO @@gRole2	
					END
					close CUR_getRoles2
					DEALLOCATE CUR_getRoles2

			FETCH NEXT FROM CUR_getRoles1 INTO @@gRole1	
			END
			close CUR_getRoles1
			DEALLOCATE CUR_getRoles1


	FETCH NEXT FROM CUR_getRoles0 INTO @@gRole0	
	END
close CUR_getRoles0
DEALLOCATE CUR_getRoles0
set @@TOTROLES = left(@@TOTROLES, len(@@TOTROLES)-1)
select @@TOTROLES
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


