SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemSubPerformance_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemSubPerformance_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemSubPerformance_Sp
**	Desc: Retrieves the Item Subs History  from Item table for current year.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   Sep-08-2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**      04/06/2004 Wade Riza    Replaced 'Main' with @ItemKey in the where clause.
*******************************************************************************/
     
CREATE PROCEDURE AIM_ItemSubPerformance_Sp
(
     	@LcIdKey 	    				nvarchar(12),
     	@ItemKey 	    				nvarchar(25),
       	@EndYear 	    				int 
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON
 
SELECT 	COALESCE (Sum(CPS01),0) sub01,COALESCE (Sum(CPS02),0) sub02,
	COALESCE (Sum(CPS03),0) sub03,COALESCE (Sum(CPS04),0) sub04, 
   	COALESCE (Sum(CPS05),0) sub05,COALESCE (Sum(CPS06),0) sub06,
	COALESCE (Sum(CPS07),0) sub07,COALESCE (Sum(CPS08),0) sub08, 
	COALESCE (Sum(CPS09),0) sub09,COALESCE (Sum(CPS10),0) sub10,
	COALESCE (Sum(CPS11),0) sub11,COALESCE (Sum(CPS12),0) sub12, 
  	COALESCE (Sum(CPS13),0) sub13,COALESCE (Sum(CPS14),0) sub14,
	COALESCE (Sum(CPS15),0) sub15,COALESCE (Sum(CPS16),0) sub16, 
    	COALESCE (Sum(CPS17),0) sub17,COALESCE (Sum(CPS18),0) sub18,
	COALESCE (Sum(CPS19),0) sub19,COALESCE (Sum(CPS20),0) sub20, 
    	COALESCE (Sum(CPS21),0) sub21,COALESCE (Sum(CPS22),0) sub22,
	COALESCE (Sum(CPS23),0) sub23,COALESCE (Sum(CPS24),0) sub24,
	COALESCE (Sum(CPS25),0) sub25,COALESCE (Sum(CPS26),0) sub26,
	COALESCE (Sum(CPS27),0) sub27,COALESCE (Sum(CPS28),0) sub28,
	COALESCE (Sum(CPS29),0) sub29,COALESCE (Sum(CPS30),0) sub30,
	COALESCE (Sum(CPS31),0) sub31,COALESCE (Sum(CPS32),0) sub32,
	COALESCE (Sum(CPS33),0) sub33,COALESCE (Sum(CPS34),0) sub34,
	COALESCE (Sum(CPS35),0) sub35,COALESCE (Sum(CPS36),0) sub36,
	COALESCE (Sum(CPS37),0) sub37,COALESCE (Sum(CPS38),0) sub38,
	COALESCE (Sum(CPS39),0) sub39,COALESCE (Sum(CPS40),0) sub40,
	COALESCE (Sum(CPS41),0) sub41,COALESCE (Sum(CPS42),0) sub42,
	COALESCE (Sum(CPS43),0) sub43,COALESCE (Sum(CPS44),0) sub44,
	COALESCE (Sum(CPS45),0) sub45,COALESCE (Sum(CPS46),0) sub46,
	COALESCE (Sum(CPS47),0) sub47,COALESCE (Sum(CPS48),0) sub48,
	COALESCE (Sum(CPS49),0) sub49,COALESCE (Sum(CPS50),0) sub50,
	COALESCE (Sum(CPS51),0) sub51,COALESCE (Sum(CPS52),0) sub52
FROM ItemHistory 
WHERE ItemHistory.LcId = @LcIdKey
AND ItemHistory.Item = @ItemKey 
AND ItemHistory.SubsItem <> @ItemKey 
AND ItemHistory.HisYear =@EndYear 
RETURN  @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

