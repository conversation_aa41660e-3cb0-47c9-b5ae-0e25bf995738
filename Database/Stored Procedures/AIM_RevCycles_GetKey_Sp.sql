SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RevCycles_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RevCycles_GetKey_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_RevCycles_GetKey_Sp
**	Desc: Retrieves the next record from the RevCycle table based on the Action.
**
**	Returns: 1) @found - Can be Zero
**               2) 0 - No Data Found
**    
**	Values:  Recordset - Item
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      11/01/2002 Wade Riza    Added Validation
*******************************************************************************/
     
CREATE PROCEDURE AIM_RevCycles_GetKey_Sp
(
  	@RevCycle   				nvarchar(8) OUTPUT,
        @Action     				tinyint
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rtncode int

SET NOCOUNT ON

SET rowcount 1
    
-- Get the key to the Review Cycle Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
        SELECT @RevCycle = RevCycle
        FROM RevCycles 
        WHERE RevCycle = @RevCycle
        
	SELECT @rtncode = @@rowcount
END
IF @action = 1		-- Get Greater Than
BEGIN
        SELECT @RevCycle = RevCycle 
        FROM RevCycles 
        WHERE RevCycle > @RevCycle
        
	SELECT @rtncode = @@rowcount
    	IF @rtncode = 0
    	BEGIN
		SELECT @action = 6	-- Get Last Record
	END
END
IF @action = 2		-- Get Less Than 
BEGIN
        SELECT @RevCycle = RevCycle 
        FROM RevCycles 
        WHERE RevCycle < @RevCycle 
        ORDER BY RevCycle DESC
       
	SELECT @rtncode = @@rowcount
        
	IF @rtncode = 0
        BEGIN
	    SELECT @action = 5      -- Get first record
	END    
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
       SELECT @RevCycle = RevCycle 
       FROM RevCycles 
       WHERE RevCycle >= @RevCycle 
       
       SELECT @rtncode = @@rowcount
       
       IF @rtncode = 0
       BEGIN
	     SELECT @action = 6      -- Get last record
       END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
       SELECT @RevCycle = RevCycle 
       FROM RevCycles 
       WHERE RevCycle <= @RevCycle 
       ORDER BY RevCycle DESC
 
       SELECT @rtncode = @@rowcount
       
       IF @rtncode = 0
       BEGIN
	     SELECT @action = 5      -- Get first record
       END
END
IF @action = 5		-- Get First
BEGIN
       SELECT @RevCycle = RevCycle 
       FROM RevCycles  
  
       SELECT @rtncode = @@rowcount
END
IF @action = 6		-- Get Last
BEGIN
       SELECT @RevCycle = RevCycle 
       FROM RevCycles 
       ORDER BY RevCycle DESC
 
       SELECT @rtncode = @@rowcount
END
IF @rtncode > 0 
BEGIN
    	SELECT * 
	FROM RevCycles 
	WHERE RevCycle = @RevCycle
        RETURN 1		-- SUCCEED
END
ELSE
BEGIN
        RETURN 0		-- FAIL
END    
    
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

