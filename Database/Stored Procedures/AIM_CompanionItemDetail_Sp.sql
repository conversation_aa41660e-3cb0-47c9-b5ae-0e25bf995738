SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_CompanionItemDetail_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_CompanionItemDetail_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_CompanionItemDetail_Sp
**	Desc:Inserts data into CompanionItemDetail table
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   03/04/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
     
  CREATE     PROCEDURE AIM_CompanionItemDetail_Sp
  (
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25),
        @StartDate       				datetime,
	@EndDate       					datetime,
        @Qty       					int
  )
  -- WITH ENCRYPTION	/* Production use must be encrypted */
  AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

  SET NOCOUNT ON
  
  INSERT INTO CompanionItemDetail
        ( LcId, Item, StartDate,EndDate,Qty) 
  VALUES 
        (@LcId, @Item, @StartDate,@EndDate, @Qty) 
  RETURN 1



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


