SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Update_RevCycle_Date_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Update_RevCycle_Date_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Update_RevCycle_Date_Sp
**	Desc: 
**
**	Returns: 1)1 - Sucessful 
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
    
CREATE PROCEDURE AIM_Update_RevCycle_Date_Sp
(
      	@RevCycle        					nvarchar(8),
        @ReviewDate				 		    datetime OUTPUT ,
    	@UpdateOption						nvarchar(1) 
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

-- Declare working variables
DECLARE
        @NextDateTime 						datetime,
        @RevFreq 						tinyint,
        @RevInterval        					smallint,
        @RevSunday          					smallint,
        @RevMonday         			 		smallint,
        @RevTuesday         					smallint,
        @RevWednesday       					smallint,
        @RevThursday        					smallint,
        @RevFriday          					smallint,
        @RevSaturday        					smallint,
        @WeekQualIFier      					tinyint,
        @RevStartDate       					datetime,
        @RevEndDate        	 				datetime,
        @InitBuyPct         					decimal(5,4),
        @InitRevDate        					datetime,
        @ReviewTime         					smallint,
        @EndOfMonth         					datetime,
        @IntervalTest       					smallint,
        @LastWeek           					bit,
        @StartofMonth       					datetime,
        @TrialRvDate 	    					datetime,
        @WeekOfMonth        					tinyint

SET NOCOUNT ON
        	
-- Retreive the Review Cycle Record
SELECT @NextDateTime = NextDateTime, @RevFreq = RevFreq, 
@RevInterval = RevInterval, @RevSunday = RevSunday, 
@RevMonday = RevMonday, @RevTuesday = RevTuesday, 
@RevWednesday = RevWednesday, @RevThursday = RevThursday, 
@RevFriday = RevFriday, @RevSaturday = RevSaturday, 
@WeekQualIFier = WeekQualIFier, @RevStartDate = RevStartDate, 
@RevEndDate = RevEndDate, @InitBuyPct = InitBuyPct, 
@InitRevDate = InitRevDate, @ReviewTime = ReviewTime
FROM RevCycles
WHERE RevCycle = @RevCycle
  
-- Initilaize variables
SELECT @TrialRvDate = @NextDateTime
WHILE 1 = 1
BEGIN
        IF @RevFreq = 0            -- Dynamic
            SELECT @TrialRvDate = dateadd(dd, 1, @TrialRvDate)
        IF @RevFreq = 1            -- Daily
            SELECT @TrialRvDate = dateadd(dd, @RevInterval, @TrialRvDate)
        IF @RevFreq = 2            -- Weekly
        BEGIN
            
            -- Check for no day SELECTed
            IF @RevSunday = 0 and @RevMonday = 0 and @RevTuesday = 0
                and @RevWednesday = 0 and @RevThursday = 0 and @RevFriday = 0
                and @RevSaturday = 0
                BREAK
            WHILE 1 = 1
            BEGIN
                -- Increment date by one day            
                SELECT @TrialRvDate = dateadd(dd, 1, @TrialRvDate)
                -- Test for Weekly Interval
                SELECT @IntervalTest = (datedIFf(dd, @RevStartDate, @TrialRvDate) / 7) % @RevInterval
                IF datepart(dw, @TrialRvDate) = 1 and @RevSunday = 1 and @IntervalTest = 0
                    BREAK
                IF datepart(dw, @TrialRvDate) = 2 and @RevMonday = 1 and @IntervalTest = 0
                    BREAK
                IF datepart(dw, @TrialRvDate) = 3 and @RevTuesday = 1 and @IntervalTest = 0
                    BREAK
                IF datepart(dw, @TrialRvDate) = 4 and @RevWednesday = 1 and @IntervalTest = 0
                    BREAK
                IF datepart(dw, @TrialRvDate) = 5 and @RevThursday = 1 and @IntervalTest = 0
                    BREAK
                IF datepart(dw, @TrialRvDate) = 6 and @RevFriday = 1 and @IntervalTest = 0
                    BREAK
                IF datepart(dw, @TrialRvDate) = 7 and @RevSaturday = 1 and @IntervalTest = 0
                    BREAK
            END
            
        END
        IF @RevFreq = 3            -- Monthly
            SELECT @TrialRvDate = dateadd(mm, @RevInterval, @TrialRvDate)
        IF @RevFreq = 4            -- Week of Month
        BEGIN
            
            WHILE 1 = 1
            BEGIN
                SELECT @TrialRvDate = dateadd(dd, 1, @TrialRvDate)
                -- Test for Week of the Month
                SELECT @StartOfMonth = cast(datename(mm, @TrialRvDate) + ' 01, ' + datename(yyyy, @TrialRvDate) AS datetime)
                SELECT @EndOfMonth = dateadd(mm, 1, @StartOfMonth) - 1
                SELECT @WeekofMonth = (datedIFf(dd, @StartofMonth, @TrialRvDate) / 7) + 1
                IF datedIFf(dd, @TrialRvDate, @EndOfMonth) / 7 = 0
                    SELECT @LastWeek = 1
                ELSE
                    SELECT @LastWeek = 0
            
                IF datepart(dw, @TrialRvDate) = 1 and @RevSunday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
                IF datepart(dw, @TrialRvDate) = 2 and @RevMonday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
                IF datepart(dw, @TrialRvDate) = 3 and @RevTuesday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
                IF datepart(dw, @TrialRvDate) = 4 and @RevWednesday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
                IF datepart(dw, @TrialRvDate) = 5 and @RevThursday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
                IF datepart(dw, @TrialRvDate) = 6 and @RevFriday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
                IF datepart(dw, @TrialRvDate) = 7 and @RevSaturday = 1 
                    and (@WeekOfMonth = @WeekQualIFier or (@LastWeek = 1 and @WeekQualIFier = 5)) 
                    BREAK
               
            END     -- End of While
        
        END     -- End of Week of Month
        
        -- Be sure the next review date is greater than the current review date
        IF @TrialRvDate > @ReviewDate 
            BREAK
END	-- End of while loop
-- Update Next Date Time
SELECT @ReviewDate = @TrialRvDate 
IF @UpdateOption = 'Y'
BEGIN
  	UPDATE RevCycles 
	SET NextDateTime = @TrialRvDate 
	WHERE revcycle = @revcycle
END
ELSE
BEGIN
  	SELECT NextDateTime = @TrialRvDate
END
  
RETURN 1		-- SUCCEED
    
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

