if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetFcstAdj_SP]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetFcstAdj_SP]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO




/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetFcstAdj_SP
**	Desc: Gets the Fcst Adj, Adj Type from  AIMFcstMaster table
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Fcst Adj, Adj Type
**              
**	Auth:   Srinivas Uddanti
**	Date:   02/26/2004
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:      Author:        Description:
**  ---------- -------------- -------------------------------------------------
**  Dec-07-2004	Srinivas Uddanti Modified code for Verision 4.6
*******************************************************************************/

CREATE     PROCEDURE AIM_GetFcstAdj_SP
(
      	@LcID      		nvarchar(12), 
      	@Item       		nvarchar(25),
      	@PeriodBegDate		DateTime     	
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
    
SET NOCOUNT ON

-- Validate the required parameters.
IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @PeriodBegDate IS NULL
BEGIN
	RETURN -1
END
  
BEGIN


-- Do not use coalesce fro AjustQty_OverRide because we check it for null
-- If it is null then there is no override

SELECT COALESCE(QtyAdjOverRide,0) "AdjustQty_OverRide",
   COALESCE(QtyAdj,0) "AdjustQty_Units",
  COALESCE(QtyAdjPct,0) "AdjustQty_Percent",
   AdjOverRide "AdjOverRide"
FROM AIMFcstMaster  
WHERE LcID = @LcID and 	
Item = @Item and
PeriodBegDate =@PeriodBegDate 

If @@rowcount =1 
BEGIN
	RETURN 1
	
END
IF @@RowCOUNT =0
BEGIN
	RETURN -1
END

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

END




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

