SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_TransferManagement_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_TransferManagement_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** Name: AIM_TransferManagement_Sp
** Desc: Gets Transfer data to populate the OverStock Mngt Tab
**
** Returns: 1)@@recordcount - Can be Zero
**          2) 0 - Failure
**             
** Values:  
**    @RptOpt = 3 Overstocks with Shortages is only valid option 
**         
** Auth: Srinivas Uddanti   
** Date: 01/14/2003  
*******************************************************************************
** Change History
*******************************************************************************
** Date:    Author: Description:
** ---------- ------------ -------------------------------------------------
** 01/17/2003 Srinivas Added @Drop_Ship_YN argument and Modified Code
** 10/06/2003 Srinivas Added TO_Lcidis arg
*******************************************************************************/

CREATE PROCEDURE AIM_TransferManagement_Sp
(
      @SQL_Overstocks         nvarchar(4000),
      @SQL_Shortages          nvarchar(4000),
      @RptOpt                 tinyint,
      @From_LcIds             nvarchar(2000),
      @Drop_Ship_YN           nvarchar(1),
      @To_Lcids		      nvarchar(2000) 	                                        
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
 
  
DECLARE @SQL                nvarchar(4000),
        @lcid               nvarchar(12),
        @sql_fromlcid       nvarchar(40),
        @sql_tolcid         nvarchar(4000),
        @Sql_tolcidAdd      nvarchar(4000),
        @Cursor_SQL         nvarchar(2500)
  
SET NOCOUNT ON
--Build a From locations cursor from the From_Lcids Argmument

Set @Cursor_SQL ='declare C cursor static for select distinct lcid from aimlocations  where lcid in ' +@From_LcIds
EXEC(@Cursor_SQL)
OPEN c
FETCH NEXT FROM C INTO @lcid
-- Validate the required parameters.
  IF @RptOpt <>3  and @RptOpt <>4
  BEGIN
 RETURN 0
  END
 
  
  IF @RptOpt =3 or @RptOpt =4
  BEGIN
 -- CREATE the temporary tables
 SELECT RcdId = 1, Item, ItDesc, ItStat, Lcid, Cost, OrderPt, OrderQty, Oh, Oo,  
 ComStk, BkOrder, BkComStk, FcstDemand,weight,cube, ConvFactor,uom,Binlocation
 INTO #overstocks
 FROM Item  
 WHERE lcid = 'XXXXXXXXXX'

            
 IF @SQL_Shortages IS NULL
 BEGIN
  RETURN 0
  END
 
 SELECT * 
 INTO #shortages 
 FROM #overstocks 

 select del =0,o.item as "oitem",o.itdesc,o.lcid as "olcid",o.oh as "ooh",FromBest =0,
 SugTransferQty =0,s.lcid as "slcid",s.oh as "soh",ToBest =0,
 s.fcstdemand as "sfcstdemand",o.fcstdemand as "ofcstdemand",
 extweight =0,extcube=0,o.ItStat,o.convfactor,o.orderqty as "oorderqty",
 s.orderpt as "sorderpt",s.orderqty as "sorderqty",o.weight,o.cube,
 o.orderpt as "oorderpt",o.cost,Substring(o.binlocation,1,1) as zone, o.comstk as "ocomstk",s.oo as "soo",o.uom
  into #TempTransfer from #overstocks as o inner join #shortages as s on
  o.item=s.item   
END
  IF @RptOpt = 3 or @RptOpt =4        -- Overstocks with Shortages or Shortages with Overstocks
  BEGIN

 WHILE @@fetch_status =0
 BEGIN
  SET @sql_fromlcid =' ' +' and lcid in ('''+@lcid +''')'
  --SET @sql_tolcid =' '+' and  item.lcid in (select lcidtransferto from aimtransferpolicy where lcid =''' +@lcid +''')'
  

  IF @To_Lcids  ='NOTHING'
  BEGIN
	  IF @Drop_Ship_YN ='Y' 
	  BEGIN
	  SET @Sql_tolcidAdd = ' select lcidtransferto from aimtransferpolicy '
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' inner join aimlocations on '
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' aimtransferpolicy.lcidtransferto =aimlocations.lcid '
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + 'where aimlocations.dropship_xdock =''N'''
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' and aimtransferpolicy.TransferType =''T'''
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' and aimtransferpolicy.EnableTransferPolicy =''1'''
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' and aimtransferpolicy.lcid = '''
	  END
	  IF @Drop_Ship_YN <>'Y' 
	  BEGIN
	  --SET @Sql_tolcidAdd =' select lcidtransferto from aimtransferpolicy where ' lcid = ''' 
	  SET @Sql_tolcidAdd =' select lcidtransferto from aimtransferpolicy where ' 
	  SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' TransferType =''T'' and EnableTransferPolicy =''1'' and lcid ='''
	  END
	SET @sql_tolcid =' '+' and  item.lcid in (' 
  	Set @sql_tolcid = @sql_tolcid  + @Sql_tolcidAdd
  	Set @sql_tolcid = @sql_tolcid   +@lcid +''')'
 END
ELSE
BEGIN
	IF @Drop_Ship_YN ='Y' 
	BEGIN
		SET @Sql_TolcidAdd = 'Select lcid from aimlocations where lcid in '
		SET @Sql_TolcidAdd =@Sql_TolcidAdd + @To_Lcids + ' and dropship_xdock =''N'''
		
	END
	IF @Drop_Ship_YN <>'Y' 
	BEGIN
		SET @Sql_TolcidAdd = 'Select lcid from aimlocations where lcid in '
		SET @Sql_TolcidAdd =@Sql_TolcidAdd + @To_Lcids 
	END
	SET @sql_tolcid =' '+' and  item.lcid in (' 
  	Set @sql_tolcid = @sql_tolcid  + @Sql_tolcidAdd
  	Set @sql_tolcid = @sql_tolcid   +')'
END
  

--print '@Overstock sql'
--Print 'insert into #overstocks '  + @sql_overstocks + @sql_fromlcid

--print '@Shortage sql'
--print 'insert into #shortages ' + @sql_Shortages + @sql_tolcid 




         -- Build the Overstock Table
         EXEC ('insert into #overstocks ' + @SQL_Overstocks +@sql_fromlcid)
         
         -- Build the Shortage Table
        EXEC ('insert into #shortages ' + @SQL_Shortages + @sql_tolcid)
         
         -- Delete Overstocks which have no shortages
 
         DELETE #overstocks
  WHERE NOT EXISTS (SELECT ITEM 
      FROM #shortages 
      WHERE #shortages.ITEM = #overstocks.ITEM) 
        
         -- Merge the overstock table with the shortage table
If @RptOpt = 3 
Begin
  INSERT INTO #temptransfer
  SELECT del =0,o.item,o.itdesc,o.lcid,o.oh -o.comstk,FromBest =0,SugTransferQty =0,
  s.lcid,s.oh +s.oo,ToBest =0,s.fcstdemand,o.fcstdemand,extweight =0,extcube=0,
  o.ItStat,o.convfactor,o.orderqty,s.orderpt,s.orderqty,o.weight,o.cube,
  o.orderpt,o.cost,Substring(o.binlocation,1,1) AS zone, o.comstk,s.oo,o.uom
  FROM #overstocks AS o INNER join #shortages AS s ON
  o.item=s.item 
End
If @RptOpt = 4 
Begin

--For this case shorages are sotred in #overstock table and
-- Overstocks are stored in Shortages table
 INSERT INTO #temptransfer
  SELECT del =0,o.item,o.itdesc,o.lcid,o.oh -o.comstk,FromBest =0,SugTransferQty =0,
  s.lcid,s.oh+s.oo,ToBest =0,s.fcstdemand,o.fcstdemand,extweight =0,extcube=0,
  o.ItStat,o.convfactor,o.orderqty,s.orderpt,s.orderqty,o.weight,o.cube,
  o.orderpt,o.cost,Substring(o.binlocation,1,1) AS zone, o.comstk,s.oo,o.uom
  FROM #overstocks AS s INNER join #shortages AS o ON
  o.item=s.item 


End

  DELETE FROM #shortages
  DELETE FROM #overstocks
 FETCH NEXT FROM C INTO @lcid
 END

  END
CLOSE C
DEALLOCATE C           
 -- Wrap Up
SELECT * FROM #temptransfer
Drop TABLE #temptransfer
DROP TABLE #overstocks
DROP TABLE #shortages 
        
RETURN
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

