
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMRoles_List_Sp_ALL]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMRoles_List_Sp_ALL]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMRoles_List_Sp_ALL
**	Desc: RETURNS ALL ROLES
**
**	Returns: 1)  ALL ROLES
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
CREATE PROCEDURE AIM_AIMRoles_List_Sp_ALL

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int

SET NOCOUNT ON

SELECT ROLEID,ROLEDESCRIPTION,ROLELEVEL,SCARRAY,REPTOROLEID,ALTSOURCEEXCPTS
FROM aimroles 
ORDER BY ROLEDESCRIPTION
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
  	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN @found  -- SUCCESSFUL
END
GO

