IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AllocDefaults_Get_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AllocDefaults_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: 	AIM_AllocDefaults_Get_Sp
**	Desc: 	Retrieves the AllocDefault record for the values passed in
**	Parameters: 
** 		@LStatus -- nvarchar
**		@LDivision -- nvarchar
**		@LRegion  -- nvarchar
**		@LUserDefined -- nvarchar
**	Returns: 
**		 @Found --> (Can be = 0) Successful
**		-1 --> No Data Found
**		-2 --> SQL Error
**		-3 --> User Not Permitted to retrieve data
**              
**	Auth:   Srinivas Uddanti
**	Date:   2003/06/24
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------	--------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_AllocDefaults_Get_Sp
(
   	@LStatus		nvarchar(1),              				                                                					
   	@LDivision		nvarchar(20) = '', 	
   	@LRegion		nvarchar(20) = '',    		                                       		        			
   	@LUserDefined           nvarchar(30) = '',    		
    	@ExceptionPct		decimal(18,2) OUTPUT,      		
    	@ReviewerId	       	nvarchar(12)  OUTPUT,       		
   	@LRank     	        tinyint       OUTPUT,
	@AllocDefaultsId	numeric(18,0) OUTPUT       		
)        	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

-- Declare working variables
DECLARE	
        
	-- Working Storage
        @Last_RevCycle          			nvarchar(8),             		-- Last Review Cycle
        @RevDate                			datetime,            			-- ReviewDate
      	@UpdateRevCycleFlag     			nvarchar(1)             		-- Update Review Cycle Flag
    
  -- Set runtime options
  SET NOCOUNT ON
    
  -- Initialize the counters
  SELECT @ExceptionPct = 0, @ReviewerId = '', @LRank = 0,@AllocDefaultsId =0

 If @LDivision ='' and @LRegion ='' and @LUserDefined ='' 
   Begin
	   Select Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			@LRank =LRank,@AllocDefaultsId	 =ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus
  Return
   End
   

If  @LRegion ='' and @LUserDefined ='' 
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId	 =ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and 
		LDivision =@LDivision
   Return
   End
   
If  @LDivision ='' and @LRegion ='' 
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId	 =ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and 
		LUserDefined =@LUserDefined
   Return
   End
 If  @LDivision ='' and @LUserDefined ='' 
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId	 =ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and 
		LRegion =@LRegion
   Return
   End
     


If   @LUserDefined ='' 
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId =ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and
		 LDivision =@LDivision and
		 LRegion =@LRegion
   Return
   End
   
If   @LDivision ='' 
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId=ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and
		 LUserDefined =@LUserDefined and
		 LRegion =@LRegion
   Return
   End
   
If   @LRegion ='' 
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId=ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and
		 LUserDefined =@LDivision and
		 LDivision =@LDivision
   Return
   End
   Begin
	   Select  Top 1 @ExceptionPct =ExceptionPct,@ReviewerId =ReviewerId,
			 @LRank =LRank,@AllocDefaultsId=ALlocDefaultsId
	   From AllocDefaults 
	   Where LStatus =@LStatus and 
		LDivision =@LDivision and 
		LRegion =@LRegion and
		LUserDefined =@LUserDefined
   Return
   End
   




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

