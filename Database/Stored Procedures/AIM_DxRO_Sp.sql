SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxRO_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxRO_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxRO_Sp
**	Desc: Loads data exchange item records into the Re-stock order tables
**          from the AIMDxRO Table. 
**
** Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**                      1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**                      4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/16
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Updated by:		Description:
**	---------- 	------------	-----------------------------------------------
**	2005/01/04	A.Stocksdale	Added inserts for AIMAODetail.AllocUOM and AIMAODetail.AllocConvFactor
**	NOTE:  The RO interface need not change for these two fields, unless so decreed by the customer.
**	These two fields are derived from the Item table.
**	It is a terrible idea to include these in the AIMAODetail, for reasons of data integrity.
**	But the structure of the AIMAODetail table was forced to suit the AllocReview user-interface 
**	and not the higher demands of good database design.  A bad idea it remains, 
**	as I called it back then, but we have to deal with the consequences now.
**    	2005/03/08 Srinivas U	Added truncate and delete before the bulk insert							
*******************************************************************************/
 
CREATE PROCEDURE AIM_DxRO_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	/* START VARIABLE DECLARATION */  
	-- System Control Table
	DECLARE @RtnCode int

	SET NOCOUNT ON

	/* Start variable initialization */
	--Initialize counters
	SELECT @InsertCounter = 0, @UpdateCounter = 0
	-- Delete records from the DxRO Table
	TRUNCATE TABLE AIMDxRO
	-- Just in case the operator doesn't have truncate priviledges
	DELETE AIMDxRO
	-- Process records
	-- This is a data interface transaction <Transaction Code=RO>. 
	-- Bulk Load the <Transaction Table=AIMDxRO> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'RO', 'AIMDxRO', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
	SELECT 'Inserts' = @InsertCounter	
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.
	
	BEGIN TRANSACTION
		-- NOTE: This is not technically a substitution with defaults 
		-- 	since these defaults are used to fill in information 
		-- 	from AIM tables into the AIMAO*tables for outbound processing
		INSERT INTO AIMAO (OrdNbr
			, OrderStatus
			, Lcid
			, OriginCode
			, UserInitials
			, LineCount
			, TotalUnits
			, LocFillPct
			, ReviewerId)
		SELECT AIMDxRO.OrdNbr
			, 0	-- Pending
			, RTRIM(AIMDxRO.LcID)
			, ISNULL(AIMDxRO.OriginCode, '')
			, ''
			, 0
			, 0
			, 0
			, ISNULL(AIMLocations.Dft_ReviewerID, 'sa')
		FROM AIMDxRO
		INNER JOIN AIMLocations
			ON AIMDxRO.LcID = AIMLocations.LcID
		GROUP BY AIMDxRO.OrdNbr, AIMDxRO.LcID, AIMDxRO.OriginCode, AIMLocations.Dft_ReviewerID

		-- AIM Allocation Order detail
		-- INSERT
		INSERT INTO AIMAODetail (OrdNbr
			, OrdType
			, LineNbr
			, LineItemStatus
			, Lcid
			, LType
			, Item
			, ItDesc
			, UOM
			, AllocUOM
			, AllocConvFactor
			, RequestedQty
			, AllocatedQty
			, AdjustedAllocQty
			, Cost
			, LineFillPct
			, TxnDate
			, TxnTimeOfDay
			, AllocationDate
			, AllocationTimeOfDay)
		SELECT AIMDxRO.OrdNbr
			, ISNULL( AIMDxRO.OrdType, '')
			, AIMDxRO.LineNbr
			, 0	-- Pending
			, RTRIM( AIMDxRO.Lcid)
			, 'D'		-- Destination
			, RTRIM( AIMDxRO.Item)
			, ISNULL(Item.ItDesc, '')
			, ISNULL(AIMDxRO.UOM, '')
			, ISNULL(Item.AllocUOM, 'EA')
			, ISNULL(Item.AllocConvFactor, 1)
			, ISNULL( AIMDxRO.OrdQty,0)
			, 0
			, 0
			, ISNULL(Item.Cost, 0)
			, 0
			, ISNULL(AIMDxRO.TxnDate, CONVERT(nvarchar(10), GETDATE(), 101))
			, ISNULL( AIMDxRO.TxnTimeOfDay, '00:00')
			, ''
			, ''
		FROM AIMDxRO
		INNER JOIN Item
		ON AIMDxRO.Item = Item.Item
		GROUP BY AIMDxRO.OrdNbr
			, AIMDxRO.ORdType
			, AIMDxRO.LineNbr
			, AIMDxRO.LcId
			, AIMDxRO.Item
			, Item.ItDesc
			, AIMDxRO.UOM
			, Item.AllocUOM
			, Item.AllocConvFactor
			, AIMDxRO.OrdQty
			, Item.Cost
			, AIMDxRO.TxnDate
			, AIMDxRO.TxnTimeOfDay

		SELECT @InsertCounter =  @@ROWCOUNT

	COMMIT TRANSACTION

	-- Delete records from the DxRO Table
	TRUNCATE TABLE AIMDxRO
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE AIMDxRO

	UPDATE STATISTICS AIMAO WITH RESAMPLE, ALL
	UPDATE STATISTICS AIMAODetail WITH RESAMPLE, ALL

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

