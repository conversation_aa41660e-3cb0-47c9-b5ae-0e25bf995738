SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AIMProductionConstraint_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AIMProductionConstraint_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMProductionConstraint_Sp
**	Desc: Gets the Needed values for the Forecast based on Location and Assortment
**
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Recordset - LcID, LName
**              
**	Auth:   Wade Riza 
**	Date:   10/09/2002
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:      Author:      Description:
**  ---------- ------------ -------------------------------------------------
**
*******************************************************************************/

CREATE PROCEDURE AIM_AIMProductionConstraint_Sp
(
	@VnID                  nvarchar(12),
	@Assort                nvarchar(12)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @found     int ,
		@ProdConstraintGrtZero	nvarchar(1)

	SET NOCOUNT ON

	-- Validate the required parameters.
	IF @VnID IS NULL
	OR @Assort IS NULL
	BEGIN
		RETURN -1  -- ERROR (No Valid Vendor ID) 
	END

	SELECT @ProdConstraintGrtZero = ProdconstraintGrtZero
	FROM SysCtrl

	IF upper(@ProdConstraintGrtZero) = 'Y' 
    BEGIN
		SELECT PC.ConstraintID,PO.Item,PO.LCID,
		 	PO.RSOQ,PO.VSOQ,PO.Original_VSOQ,COALESCE(ITEM.OrderPt,0) "OrderPt",
		 	COALESCE(ITEM.ConvFactor,1) "ConvFactor",COALESCE(PCD.MinUnits,0) "MinUnits",
		 	COALESCE(case PCD.MaxUnits when 0 then 999999999 else pcd.maxunits end,1) "MaxUnits",
		 	COALESCE(case PC.CycleDays when 0 then 1 else pc.cycledays end,1) "CycleDays",
		 	COALESCE(PC.RatioType,1) "RatioType",COALESCE (LC.DROPSHIP_XDOCK,'N') "DROPSHIP_XDOCK",PO.POSeqID
	 	FROM PODETAIL PO INNER JOIN ITEM 
	 	ON PO.ITEM =ITEM.ITEM AND
		 	PO.ASSORT =ITEM.ASSORT AND
		 	PO.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
	 	ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
	 	ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
	 	ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
	 	WHERE ITEM.ASSORT =@ASSORT AND
		 	ITEM.VNID =@VNID
		 	AND ITEM.ProductionConstraint='Y' 
			AND PC.EnableConstraint ='Y'
		 	AND PO.RSOQ >0
	 	ORDER BY PC.ConstraintID, PO.Item, PO.Lcid
	END
	ELSE 
	BEGIN
		SELECT PC.ConstraintID,PO.Item,PO.LCID,
		 	PO.RSOQ,PO.VSOQ,PO.Original_VSOQ,COALESCE(ITEM.OrderPt,0) "OrderPt",
		 	COALESCE(ITEM.ConvFactor,1) "ConvFactor",COALESCE(PCD.MinUnits,0) "MinUnits",
		 	COALESCE(case PCD.MaxUnits when 0 then 999999999 else pcd.maxunits end,1) "MaxUnits",
		 	COALESCE(case PC.CycleDays when 0 then 1 else pc.cycledays end,1) "CycleDays",
		 	COALESCE(PC.RatioType,1) "RatioType",COALESCE (LC.DROPSHIP_XDOCK,'N') "DROPSHIP_XDOCK",PO.POSeqID
	 	FROM PODETAIL PO INNER JOIN ITEM 
	 	ON PO.ITEM =ITEM.ITEM AND
		 	PO.ASSORT =ITEM.ASSORT AND
		 	PO.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
	 	ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
	 	ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
	 	ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
	 	WHERE ITEM.ASSORT =@ASSORT AND
		 	ITEM.VNID =@VNID
		 	AND ITEM.ProductionConstraint='Y' 
			AND PC.EnableConstraint ='Y'
		 	ORDER BY PC.ConstraintID, PO.Item, PO.Lcid
	END
  	
	SELECT @found = @@rowcount

 	-- Check for SQL Server errors.
 	IF @@ERROR <> 0 
 	BEGIN
  		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
 	END

 	IF @found <= 0 
 	BEGIN 
  		RETURN -1 -- ERROR (No Valid data found in Database)
 	END
 	ELSE
 	BEGIN
  		RETURN @found
 	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

