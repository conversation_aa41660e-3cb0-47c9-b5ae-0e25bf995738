SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_Alloc_HoldExceptions_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_Alloc_HoldExceptions_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:				Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:				Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp				Main Stored Procedure
** 
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**											Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_Alloc_HoldExceptions_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Called by the Allocation Stored procedure after the main transaction, based on the parameters
**		set by the user in the Job Scheduler for scheduled Allocation.
**		This stored procedure checks for exceptions based on settings in
**		AIMDestinationProfile and SysCtrl
**		and update orders that are not exceptions to have a status of 'Released'
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/21
**-----------------------------------------------------------------------------
**	Change history
**-----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------	-----------------------------------------------
**	2003/09/20	A.Stocksdale	Modified to process exceptions at Order, 
**								Location and Line Item levels	
**	2004/07/09	S.Uddanti		Modified Code and added where clause with orderstatus 
**								30 at many locations so that completed orders will not
**								be processed again						
**	2004/07/09	S.Uddanti		Set the OrderStatus to 12 if it is 11 for hold none case			
**	2005/01/07	A.Stocksdale	Modified the OrderStatus 12 to 11 to use 
**								AIM_UpdateAIMAOOrderStatus_Sp instead (updates all 3 tables)
*******************************************************************************/

CREATE PROCEDURE AIM_Alloc_HoldExceptions_Sp 
(
	  @ReleaseOrders AS NVARCHAR(1),
	  @UserID AS NVARCHAR(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @ApplyTrigger AS TINYINT
	DECLARE @AllocExceptionPct AS TINYINT
	DECLARE @UserInitials AS NVARCHAR(3)
	DECLARE @Return_Stat AS INT

	SET NOCOUNT ON

	-- Validate inputs
	IF @ReleaseOrders IS NULL
	BEGIN
		SET @ReleaseOrders = 'N'
	END
	SELECT @UserInitials = AIMUsers.UserInitials FROM AIMUsers WHERE AIMUsers.UserID = @UserID
	IF @UserInitials IS NULL
	BEGIN
		SET @UserInitials = ''
	END

	-- Get sysctrl values for trigger level (order or line item)
	SELECT @ApplyTrigger = AllocExceptionProcess
		, @AllocExceptionPct = AllocExceptionPct
	FROM SysCtrl
	IF @ApplyTrigger IS NULL 
	BEGIN
		-- If not defined, then default to order level
		SELECT @ApplyTrigger = 0
		UPDATE SysCtrl SET 
			AllocExceptionProcess = @ApplyTrigger
			, AllocExceptionPct = @AllocExceptionPct
	END

	IF @ApplyTrigger = 0 -- exceptions at order level
	BEGIN
		-- Update AIMAO.OrderStatus to 'Allocated With Exceptions' where OrderFillPct is below the trigger
		UPDATE AIMAO_OrderInfo SET 
			OrderStatus = 12	-- ALLOCATED WITH EXCEPTIONS
		WHERE AIMAO_OrderInfo.OrderFillPct <= @AllocExceptionPct
		AND AIMAO_OrderInfo.OrderStatus <> 30

		IF @ReleaseOrders = 'Y'
		BEGIN
			-- Update AIMAO.OrderStatus to 'Released' where orderfillpct is above the exception trigger
			UPDATE AIMAO_OrderInfo
			SET OrderStatus = 20	-- RELEASED
				, UserInitials = @UserInitials
			WHERE AIMAO_OrderInfo.OrderFillPct > @AllocExceptionPct
			AND AIMAO_OrderInfo.OrderStatus <> 30

		END

		-- Now update the dependent AIMAODetail records to reflect status change
		UPDATE AIMAO SET
			AIMAO.OrderStatus = AIMAO_OrderInfo.OrderStatus
		FROM AIMAO_OrderInfo
		WHERE AIMAO.OrdNbr = AIMAO_OrderInfo.OrdNbr 
			AND AIMAO_OrderInfo.OrderStatus IN (20, 12)
			AND AIMAO.OrderStatus <> 11

-- 		-- Use EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 12, 11 
-- 		UPDATE AIMAO
-- 		SET AIMAO.OrderStatus = 12
-- 		WHERE AIMAO.OrderStatus = 11
		EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 12, 11
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END

		UPDATE AIMAODetail SET 
			LineItemStatus = AIMAO.OrderStatus
		FROM AIMAO
		WHERE AIMAODetail.OrdNbr = AIMAO.OrdNbr 
			AND AIMAO.OrderStatus IN (20, 12)
			
	END
	ELSE IF @ApplyTrigger = 1 -- exceptions at location level
	BEGIN
		-- Update AIMAO.OrderStatus to 'Allocated With Exceptions' where OrderFillPct is below the trigger
		UPDATE AIMAO
		SET OrderStatus = 12	-- ALLOCATED WITH EXCEPTIONS
		FROM AIMAO
		INNER JOIN AIMLocations
			ON AIMAO.LcID = AIMLocations.LcID
		WHERE
			AIMLocations.LType = 'D' 
			AND (AIMAO.LocFillPct) <= AIMLocations.ExceptionPct
			AND AIMAO.OrderStatus <>30

		IF @ReleaseOrders = 'Y'
		BEGIN
			-- Update AIMAO.OrderStatus to 'Released' where orderfillpct is above the exception trigger
			UPDATE AIMAO
			SET OrderStatus = 20	-- RELEASED
			, UserInitials = @UserInitials
			FROM AIMAO
			INNER JOIN AIMLocations
				ON AIMAO.LcID = AIMLocations.LcID
			WHERE
				AIMLocations.LType = 'D' 
				AND (AIMAO.LocFillPct) > AIMLocations.ExceptionPct	
				AND AIMAO.OrderStatus <>30		
		END

		-- Now update the dependent AIMAODetail records to reflect status change
		UPDATE AIMAODetail
		SET LineItemStatus = AIMAO.OrderStatus
		FROM AIMAO
		WHERE AIMAODetail.OrdNbr = AIMAO.OrdNbr
			AND AIMAO.OrderStatus IN (20, 12)
		
		-- And the related AIMAO_OrderInfo records as well
		UPDATE AIMAO_OrderInfo
		SET OrderStatus = 20	-- RELEASED
			, UserInitials = @UserInitials
		WHERE NOT EXISTS
		   (SELECT *
			   FROM AIMAO
			   WHERE AIMAO.OrdNbr = AIMAO_OrderInfo.OrdNbr
				AND AIMAO.OrderStatus NOT IN (20)
			)
	END
	ELSE IF @ApplyTrigger = 2 -- exceptions at line level
	BEGIN
		-- Apply trigger per line item
		-- Update AIMAODetail.LineItemStatus to 'Allocated With Exceptions' where LineFillPct is below the trigger
		UPDATE AIMAODetail
		SET LineItemStatus = 12	-- ALLOCATED WITH EXCEPTIONS
		FROM AIMAODetail
		INNER JOIN AIMAO
			ON AIMAODetail.OrdNbr = AIMAO.OrdNbr
		INNER JOIN AIMDestinationProfile
			ON AIMAO.LcID = AIMDestinationProfile.LcID_Destination 
			AND AIMAODetail.Item = AIMDestinationProfile.Item
		WHERE (AIMAODetail.LineFillPct) <= AIMDestinationProfile.ExceptionPct
			AND AIMAODetail.LType = 'S'
			AND AIMAODetail.LineItemStatus <>30
		-- Now update AIMAO  records to reflect status change
		UPDATE AIMAO
		SET OrderStatus =AIMAODetail.LineItemStatus
		FROM AIMAODetail
		WHERE AIMAO.OrdNbr = AIMAODetail.OrdNbr
			AND AIMAODetail.LineItemStatus =12
		
		IF @ReleaseOrders = 'Y'
		BEGIN
			-- Update AIMAODetail.LineItemStatus to 'Released' where Linefillpct is above the exception trigger
			UPDATE AIMAODetail
			SET LineItemStatus = 20	-- RELEASED
			FROM AIMAODetail
			INNER JOIN AIMAO
				ON AIMAODetail.OrdNbr = AIMAO.OrdNbr
			INNER JOIN AIMDestinationProfile
				ON AIMAO.LcID = AIMDestinationProfile.LcID_Destination 
				AND AIMAODetail.Item = AIMDestinationProfile.Item
			WHERE (AIMAODetail.LineFillPct) > AIMDestinationProfile.ExceptionPct	
				AND AIMAODetail.LineItemStatus <>30	
		END

		-- Now update the related AIMAO records to reflect status change
		UPDATE AIMAO
		SET OrderStatus = 20	-- RELEASED
		, UserInitials = @UserInitials
		WHERE NOT EXISTS
		   (SELECT *
			   FROM AIMAODetail
			   WHERE AIMAODetail.OrdNbr = AIMAO.OrdNbr
				AND LineItemStatus NOT IN (20)
			)

		-- And the related AIMAO_OrderInfo records as well
		UPDATE AIMAO_OrderInfo
		SET AIMAO_OrderInfo.OrderStatus = 20	-- RELEASED
			, AIMAO_OrderInfo.UserInitials = @UserInitials
		WHERE NOT EXISTS
		   (SELECT *
			   FROM AIMAO
			   WHERE AIMAO.OrdNbr = AIMAO_OrderInfo.OrdNbr
				AND AIMAO.OrderStatus NOT IN (20)
			)
	END
	RETURN
END
GO
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO
