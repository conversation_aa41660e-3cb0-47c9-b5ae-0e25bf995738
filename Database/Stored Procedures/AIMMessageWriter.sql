SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMMessageWriter]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIMMessageWriter]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2005 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIMMessageWriter
**	Desc: Updates job status updates to the table, AIMERROR_INFO.
**
**	Return value: 1 or 0
**
**             1 =  Updated sucessfully
**             2 =  Updated was't sucessful
**              
**	Auth:   Srinivasa Gajjela
**	Date:   10/25/2005							*/
CREATE PROCEDURE AIMMessageWriter(@MessageID NVARCHAR(1024), @Description NVARCHAR(1024), @WriteStaus AS BIT Output ) 
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS
BEGIN
	DECLARE @ExistingErrorDesc AS NVARCHAR(1024)

	SELECT @ExistingErrorDesc =  ERRORDESC  FROM AIMERROR_INFO WHERE MSGID = @MessageID

	IF (@@ROWCOUNT =0)
	BEGIN
		SET @WriteStaus  =  0
		RETURN @WriteStaus 
	END

	UPDATE AIMERROR_INFO 
		SET ERRORDESC = @ExistingErrorDesc + @Description
	WHERE MSGID = @MessageID
	
	RETURN 1

END
 
GO

SET QUOTED_IDENTIFIER OFF 
GO

SET ANSI_NULLS ON 
GO

