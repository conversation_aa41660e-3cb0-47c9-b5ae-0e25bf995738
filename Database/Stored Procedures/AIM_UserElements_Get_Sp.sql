SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UserElements_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UserElements_Get_Sp]
GO


/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UserElements_Get_Sp

**	Desc: Gets User Elements for a given FcstId
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - UserElement Records
**              
**	Auth:   Srinvas Uddanti
**	Date:   10/15/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_UserElements_Get_Sp
(
      	@FcstId    		nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstId IS NULL
BEGIN
	RETURN -1
END
SELECT 1, UE.UserElementId,UE.UserElementDesc,UE.Processed_YN,UE.FcstType
FROM UserElement UE INNER JOIN AIMFcstSetUp FS  ON 
UE.FcstId =FS.FcstId AND 
FS.FcstId =@FcstId  
-- AND UE.Processed_YN =1
ORDER BY UE.UserElementId



IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END		


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

