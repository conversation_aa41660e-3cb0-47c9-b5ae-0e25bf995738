SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RepositoryOverRide_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RepositoryOverRide_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON>ES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_RepositoryOverRide_Get_Sp
**	Desc: Gets Forecast Repository data
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - Forecast Repository,
**             Recordset - Forecast Repository Detail
**              
**	Auth:   Srinivas Uddanti
**	Date:   06/16/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------

*******************************************************************************/

CREATE     PROCEDURE AIM_RepositoryOverRide_Get_Sp
(
 	@FcstId	        	nvarchar(12)       
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int
DECLARE @RepositoryKey				int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstId	 IS NULL
BEGIN
	RETURN -1
END

SELECT @RepositoryKey = RepositoryKey FROM ForecastRepository 
WHERE FcstID = @FcstID 


IF @@rowcount = 0 
BEGIN
 	RETURN -1  
END

select c.lcid,c.item,c.adjustBegdate
from forecastrepository_Log c,
(select   a.lcid,a.item,a.FcstPdBegDate,max(b.fcstadjustkey) "maxfcstadjustkey"
from ForecastRepositoryDetail a,ForecastRepository_Log b
where a.lcid =b.lcid and a.item=b.item
and a.FcstPdBegDate=b.adjustBegdate
and b.adjusttype=0
and a.RepositoryKey =b.RepositoryKey
and a.RepositoryKey =@RepositoryKey
--and a.AdjustType=1
group by a.lcid,a.item,a.FcstPdBegDate) d
where c.fcstadjustkey =d.maxfcstadjustkey
and  c.OverRideEnabled =1

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO		



