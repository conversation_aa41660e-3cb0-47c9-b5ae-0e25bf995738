SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetKitBOMItemsForALoc_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetKitBOMItemsForALoc_Sp]
GO


/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************  
**	Name: AIM_GetKitBOMItemsForALoc_Sp
**	Desc: Gets distict Dependent item from ItemKitBOM table for a given
**            Loction
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   03/03/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE               PROCEDURE AIM_GetKitBOMItemsForALoc_Sp
(
     	@LcId           	nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

SELECT DISTINCT Item AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT,
	 Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,
	 Fcst_Quarter,Fcst_Year
FROM  ITEM 
WHERE Item  IN
(SELECT DISTINCT ItemComponent 
FROM ITEMKITBOM,ITEM
WHERE ITEMKITBOM.LCID =ITEM.LCID AND
	ITEMKITBOM.ITEM =ITEM.ITEM AND
	ITEM.KITBOMFLAG ='Y' AND
	ITEMKITBOM.LCID =@LCID
)
AND LCID =@LCID
RETURN  @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
