
IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AllocDefaultsSource_Get_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AllocDefaultsSource_Get_Sp]
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND <PERSON>ALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AllocDefaultsSource_Get_Sp
**	Desc: Gets records from AllocDefaultsSource
**		Derived from AIM_ForecastMainUserAccess_Get_Sp
**	Parameters:
**		@AllocDefaultsID		numeric
**	Returns: 
**		@found --> Can be Zero
**		ErrorCodes:
**			 0 --> Successful (includes a recordset - AIMProductionConstraintDetail.*)
**			-1 --> No Data Found
**			-2 --> SQL Error  
**              
**	Auth:   Wade Riza
**	Date:   2003/06/04
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:		Description:
**	---------- 	------------	---------------------------------------
*******************************************************************************/

CREATE    PROCEDURE AIM_AllocDefaultsSource_Get_Sp 
(
       	@AllocDefaultsID		Numeric(18,0)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/


DECLARE @found		int

SET NOCOUNT ON

-- Validate the required parameters.
IF @AllocDefaultsID IS NULL
BEGIN
	RETURN -1
END	
/*
ELSE

BEGIN
	-- Make sure the header is valid.
	IF (SELECT COUNT(*) FROM AllocDefaults
	WHERE AllocDefaultsID = @AllocDefaultsID) = 0
	
	RETURN -1
END
*/
SELECT * FROM AllocDefaultsSource
WHERE AllocDefaultsSource.AllocDefaultsID =  @AllocDefaultsID
ORDER BY Src_Priority

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
    	RETURN @found
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

