IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AIMDays_Init_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AIMDays_Init_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/********************************************************************************
*********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** Name: AIM_AIMDays_Init_Sp
** Desc: Deletes AIMForecast and all children tables.
**
** Returns: 1)@RowCounter - Can be Zero
** Values:  
**              
** Auth:   Randy Sadler 
** Date:   
*******************************************************************************
** Change History
*******************************************************************************
** 	Date:    	Author:        	Description:
** 	---------- 	------------ 	-----------------------------------------------
** 	11/05/2002	Wade Riza		Added back the Insert for NON-wORKING Days
** 	03/15/2003	Mohammed		Non-Working Days are dynamic.
**	2004/01/27	A.Stocksdale	Added FiscalPeriod to AIMDays. Related updates
*******************************************************************************/

CREATE  PROCEDURE AIM_AIMDays_Init_Sp
(
	@FYStartDate as datetime,
	@FYEndDate as datetime
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON 

	DECLARE @RowCounter int
	DECLARE @Status tinyint
	DECLARE @FYDate datetime
	DECLARE @FiscalPeriod int
	DECLARE @TargetDate datetime
	DECLARE @DayOfWeek tinyint
	DECLARE @WorkingDayCount int

	SELECT @RowCounter = 0

	-- Initialize variables
	SET @TargetDate = @FYStartDate
	SET @FiscalPeriod = 1

	--For each day in the given fiscal year, add a record into the AIMDays table
	WHILE @TargetDate <= @FYEndDate 
	BEGIN
		-- Determine day of week and working status
		SET @DayOfWeek = DatePart(dw, @TargetDate)
		SELECT -- Day1, Day2, Day3, Day4, Day5, Day6, Day7
			@WorkingDayCount = COUNT(*)
		FROM AIMWorkingDays
		WHERE Day1 = CASE WHEN @DayOfWeek = 1 THEN 1
			ELSE AIMWorkingDays.Day1
			END
		AND Day2 = CASE WHEN @DayOfWeek = 2 THEN 1
			ELSE AIMWorkingDays.Day2
			END
		AND Day3 = CASE WHEN @DayOfWeek = 3 THEN 1
			ELSE AIMWorkingDays.Day3
			END
		AND Day4 = CASE WHEN @DayOfWeek = 4 THEN 1
			ELSE AIMWorkingDays.Day4
			END
		AND Day5 = CASE WHEN @DayOfWeek = 5 THEN 1
			ELSE AIMWorkingDays.Day5
			END
		AND Day6 = CASE WHEN @DayOfWeek = 6 THEN 1
			ELSE AIMWorkingDays.Day6
			END
		AND Day7 = CASE WHEN @DayOfWeek = 7 THEN 1
			ELSE AIMWorkingDays.Day7
			END

		IF @WorkingDayCount >= 1 SET @Status = 1	-- WORKING
		ELSE SET @Status = 0	-- NON WORKING

		-- Determine fiscal period
	    	SET @FiscalPeriod = (DateDiff("d", @FYStartDate, @TargetDate) / 7) + 1
		IF @FiscalPeriod > 52
		BEGIN
			SET @FiscalPeriod = 52
		END		

		-- create record
		UPDATE AIMDays SET
			DayStatus = @Status,
			FYPeriod_Days =  ( 1 + (DATEDIFF(dd, @FYStartDate, @TargetDate)) ),
			FYPeriod_Weeks = ( 1 + (DATEDIFF(dd, @FYStartDate, @TargetDate) / 7) ),
			FYPeriod_Months = ( 1 + (DATEDIFF(mm,  @FYStartDate, @TargetDate)) ),
			FYPeriod_Quarters = ( 1 + (DATEDIFF(qq,  @FYStartDate, @TargetDate)) ),
			FYPeriod_544 = CASE 
				WHEN DATEDIFF (ww, @FYStartDate, @TargetDate) IN (0, 13, 26, 39) 
				THEN ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 5, @TargetDate)) / 7) )
				ELSE ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
				END,
			FYPeriod_454 = CASE 
				WHEN DATEDIFF (ww, @FYStartDate, @TargetDate) IN (4, 17, 30, 43) 
				THEN ( 1 +(DATEDIFF(dd, @FYStartDate, DATEADD(ww, 5, @TargetDate)) / 7) )
				ELSE ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
				END,
			FYPeriod_445 = CASE 
				WHEN DATEDIFF (ww, @FYStartDate, @TargetDate) IN (8, 21, 34, 47) 
				THEN ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 5, @TargetDate)) / 7) )
				ELSE ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
				END,
			FYPeriod_4Weeks = ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
		WHERE FYDate = @TargetDate
		IF @@ROWCOUNT = 0 
		BEGIN
			INSERT INTO AIMDays (
				FYDate, 
				DayStatus,
				FYPeriod_Days,
				FYPeriod_Weeks,
				FYPeriod_Months,
				FYPeriod_Quarters,
				FYPeriod_544,
				FYPeriod_454,
				FYPeriod_445,
				FYPeriod_4Weeks
			) VALUES (
				@TargetDate, 
				@Status,
				( 1 + (DATEDIFF(dd, @FYStartDate, @TargetDate)) ),
				( 1 + (DATEDIFF(dd, @FYStartDate, @TargetDate) / 7) ),
				( 1 + (DATEDIFF(mm,  @FYStartDate, @TargetDate)) ),
				( 1 + (DATEDIFF(qq,  @FYStartDate, @TargetDate)) ),
				CASE 
					WHEN DATEDIFF (ww, @FYStartDate, @TargetDate) IN (0, 13, 26, 39) 
					THEN ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 5, @TargetDate)) / 7) )
					ELSE ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
					END,
				CASE 
					WHEN DATEDIFF (ww, @FYStartDate, @TargetDate) IN (4, 17, 30, 43) 
					THEN ( 1 +(DATEDIFF(dd, @FYStartDate, DATEADD(ww, 5, @TargetDate)) / 7) )
					ELSE ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
					END,
				CASE 
					WHEN DATEDIFF (ww, @FYStartDate, @TargetDate) IN (8, 21, 34, 47) 
					THEN ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 5, @TargetDate)) / 7) )
					ELSE ( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
					END,
				( 1 + (DATEDIFF(dd, @FYStartDate, DATEADD(ww, 4, @TargetDate)) / 7) )
			)
		END

		-- Increment Date Counter
		SELECT @TargetDate = @TargetDate + 1, 
			@RowCounter = @RowCounter + 1
	END

	UPDATE AIMDays SET
		FYPeriod_Weeks = 52 
	WHERE FYPeriod_Weeks > 52

	RETURN (@RowCounter - 1)

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

