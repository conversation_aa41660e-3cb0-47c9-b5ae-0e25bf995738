if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemSourcing_Upd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemSourcing_Upd_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemSourcing_Upd_Sp
**	Desc: Update Records on the Item Sourcing (Sourcing Tab/Buyer Review).
**
**	Returns: 
**             
**	Values:  
**              
**	Auth:   Mohammed.
**	Date:   2003/04/15
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-------------------------------------------------
**
*******************************************************************************/

CREATE Procedure AIM_ItemSourcing_Upd_Sp
(
      	@Item                        nvarchar(25),
      	@LcId                        nvarchar(25),
      	@ById                        nvarchar(12),
      	@VnId                        nvarchar(12),
      	@Assort                      nvarchar(12),
        @VSOQ                        int,
      	@OrdStatus                   nvarchar(1),
        @Action                      nvarchar(1)    -- It can be S (Save) or R (Reset)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @LineCount	             int,		
    	@PosLineCount                int,
        @PosAltLineCount             int
           
SET NOCOUNT ON
    
IF @Action = 'S' 
 Begin --    IF @Action = 'S' 
   UPDATE PODETAIL SET VSOQ = @VSOQ, SOQ = CASE POType When 'A' Then @VSOQ Else SOQ End, OrdStatus = @OrdStatus
    WHERE Item = @Item AND LcID = @LcID
      AND VnID = @VnId AND Assort = @Assort AND ByID = @ByID
 End  --    IF @Action = 'S' 

IF @Action = 'R' 
 Begin
    Update PODetail Set VSOQ = Case POType When 'A' Then 0 Else Original_VSOQ End,  
                         SOQ = Case POType When 'A' Then 0 Else SOQ End 
    Where LcID = @LcId AND Item = @Item
 End


  SELECT @LineCount = COUNT(*), 
         @PosLineCount    = Sum(Case PODetail.VSOQ WHEN 0 THEN 0  ELSE 1 END),
         @PosALTLineCount = Sum(Case POType When 'A' Then Case PODetail.VSOQ WHEN 0 THEN 0  ELSE 1 END Else 0 End)
    FROM PODetail
   WHERE ById = @ById AND VnId = @VnId AND Assort = @Assort
  
      
  Update AIMPO Set LineCount = @LineCount,  PosLineCount = @PosLineCount,
                   VndSizeFlag = Case @PosAltLineCount When 0 then 'N' Else 'Y' End
   Where ById = @ById AND VnId = @VnId AND Assort = @Assort


Return
GO
