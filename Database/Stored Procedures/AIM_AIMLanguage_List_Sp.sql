IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AIMLanguage_List_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AIMLanguage_List_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMLanguage_List_Sp
**	Desc: Returns the Language ID's that are enabled.
**	Parameters:	
**		(1) @EnabledOption - nvarchar(10) default to 'All' - Valid values:
**			Y --> Enabled
**			N --> Disabled
**			All --> select all from table, regardless of EnabledOption
**	Returns:  
**		(1) RecordCount
**		(2) A recordset containing the following fields from AIMLanguage:
**			LangID, 
**			LangDesc, 
**			HexValue, 
**			DecimalValue, 
**			Enabled
**              
**	Auth:   Wade Riza 
**	Date:   02/21/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	 		Description:
**	---------- 	------------- 	--------------------------------------------
**  2003/02/12	A. Stocksdale	Modified EnabledOption SQL to SELECT * 
**    						    instead of just LangID and Desc
*******************************************************************************/
  
CREATE PROCEDURE AIM_AIMLanguage_List_Sp
(
  	@EnabledOption	nvarchar(10) = 'All'
)		

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

/**********************************************************************
-- Parameter validation: 
-- No validation required. EnabledOption is defaulted to 'All' 
***********************************************************************/

  /* Check parameter value */
  IF @EnabledOption =  'All'
  BEGIN
	/* If @EnabledOption = 'All' Then Return all records from AIMLanguage */
  	SELECT LangID, LangDesc, HexValue, DecimalValue, Enabled 
	FROM AIMLanguage 
	ORDER BY LangID
  END
  ELSE
  BEGIN
	/* If @EnabledOption <> 'All' Then Return all records from AIMLanguage WHERE EnabledOption = @EnabledOption */
  	SELECT LangID, LangDesc, HexValue, DecimalValue, Enabled 
	FROM AIMLanguage 
	WHERE Enabled = @EnabledOption
	ORDER BY LangID
  END

  /* Return the record count */
  RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

