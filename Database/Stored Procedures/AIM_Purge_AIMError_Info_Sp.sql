SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Purge_AIMError_Info_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Purge_AIMError_Info_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Purge_AIMError_Info_Sp
**	Desc: Clean AIMError_info table Utility Procedure
**
**	Returns: @@rowcount 
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:  		Description:
**	---------- 	------------	-----------------------------------------------
**	Oct-27-05	S.Uddanti	Delete data from AIMError_info_sp table
**					which are older then today -NumOfDays
*******************************************************************************/
     
CREATE PROCEDURE AIM_Purge_AIMError_Info_Sp
(@NumOfDays	int=7)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	Delete From AIMError_info
	where DateAndTime<getdate() -@NumOFdays
	  
	RETURN @@ROWCOUNT

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

