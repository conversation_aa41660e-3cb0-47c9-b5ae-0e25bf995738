SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocationSourceQty_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocationSourceQty_Sp]
GO





/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_AllocationSourceQty_Sp
**	Desc:	Given the Item the procedure gets the Source loction and Allocated qty
**				
**
**	Returns:	1)@Found - Can be Zero
**				2) 0 - No Data Found
**				3) -1 - Invalid Parameter
**				4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
**	Values:Returns number of rows updated
**              
**	Author:	Srinivas Uddnati
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	July-13-2003	SUddanti	Added OrdNbr argument so that only current
					Order sum is returned
*******************************************************************************/
CREATE  PROCEDURE AIM_AllocationSourceQty_Sp
(
	@Item	nvarchar(25),
	@OrdNbr	 nvarchar(12)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
Declare 
@Found int
-- Validate the required parameters.
IF @Item IS NULL

BEGIN
	RETURN -1	/* Invalid parameter */
END

Select lcid,Qty =Sum(AllocatedQty) 
From AIMAODetail
where LType ='S'
and Item = @Item
and OrdNbr =@OrdNbr
Group by Lcid

SELECT @Found = @@RowCount

-- Check for SQL Server errors. */
IF @@ERROR <> 0 
BEGIN
	RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
END
ELSE IF @Found <= 0 
BEGIN	
	RETURN -1 /* ERROR (No Valid data found in Database) */
END
ELSE
BEGIN
	RETURN @Found /* SUCCESSFUL */
END	

RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

