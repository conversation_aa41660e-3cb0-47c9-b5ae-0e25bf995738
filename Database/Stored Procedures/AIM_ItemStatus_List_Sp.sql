SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemStatus_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemStatus_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemStatus_List_Sp
**	Desc: Gets the Data for Item Status
**
**	Returns: 1)@found - Can be Zero
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - ItStat, ItStatDecs 
**              
**	Auth:   Wade Riza 
**	Date:   08/14/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza    Update Return Codes and Error Handling
*******************************************************************************/

CREATE PROCEDURE AIM_ItemStatus_List_Sp
(
	@Ordgen						nvarchar(1) = NULL,
	@DmdUpd						nvarchar(1) = NULL,
	@VCAssn						nvarchar(1) = NULL,
	@CanPurge					nvarchar(1) = NULL,
	@BuyerReviewSeq				        int = NULL,
	@InclInSeasonsGen			        nvarchar(1) = NULL
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int,
	@SQL						nvarchar(4000),
	@SQLWHERE					nvarchar(4000),
	@SQLORDERBY					nvarchar(400),
	@SQLWHERELEN					int

SET NOCOUNT ON

SET @SQL = 'SELECT ItStat, ItStatDesc FROM ItStatus ' 

SET @SQLORDERBY = ' ORDER BY ItStat'

IF @Ordgen IS NULL AND @DmdUpd IS NULL AND @VCAssn IS NULL AND @CanPurge IS NULL AND
	@BuyerReviewSeq IS NULL AND @InclInSeasonsGen IS NULL 
BEGIN
	SET @SQL = @SQL + @SQLORDERBY

	EXEC(@SQL)

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	IF @found > 0 
	BEGIN
		RETURN @found
	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR  (No Item Data found)
	END
END
ELSE
BEGIN
	SET @SQLWHERE = ' WHERE '

	IF @Ordgen IS NOT NULL AND LEN(@Ordgen) <> 0
	BEGIN
		SET @SQLWHERE = @SQLWHERE + 'OrdGen = ''' + @Ordgen + ''' AND '
	END
	
	IF @DmdUpd IS NOT NULL AND LEN(@DmdUpd) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'DmdUpd = ''' + @DmdUpd + ''' AND '
	END

	IF @VCAssn IS NOT NULL AND LEN(@VCAssn) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'VCAssn = ''' + @VCAssn + ''' AND '
	END

	IF @CanPurge IS NOT NULL AND LEN(@CanPurge) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'CanPurge = ''' + @CanPurge + ''' AND '
	END

	IF @BuyerReviewSeq IS NOT NULL
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'BuyerReviewSeq = ''' + @BuyerReviewSeq + ''' AND '
	END

	IF @InclInSeasonsGen IS NOT NULL AND LEN(@InclInSeasonsGen) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'InclInSeasonsGen = ''' + @InclInSeasonsGen + ''' AND '
	END

 	SELECT @SQLWHERELEN = LEN(@SQLWHERE)

	SET @SQLWHERELEN = @SQLWHERELEN - 4

	SET @SQLWHERE = LEFT(@SQLWHERE, @SQLWHERELEN)

	SET @SQL = @SQL + @SQLWHERE

	SET @SQL = @SQL + @SQLORDERBY

	EXEC(@SQL)

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
   		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE
    	BEGIN
		RETURN @found  -- SUCCESSFUL
	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

