SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_Alloc_DeleteCompleted_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_Alloc_DeleteCompleted_Sp]
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:			Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:			Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:			Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:		Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_DeleteCompleted_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp			Main Stored Procedure
**		* AIM_Alloc_DeleteCompleted_Sp		Sub called by the VB module
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**							Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_Alloc_DeleteCompleted_Sp
**	Desc: Initial version as part of SSA DR 4.6.1
**		Called by the VB module AIMDataInterface.AllocationControl() 
**		based on the parameters set by the user in the Job Scheduler 
**		for scheduled Allocation.
**		This stored procedure deletes existing data from the AIMAO* tables
**		where status = 30 (Completed).  This is if the user wants to 
**		"Clear previously completed allocation data"
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2005/01/17
**-----------------------------------------------------------------------------
**	Change History
**----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------		-----------------------------------------------
****************************************************************************** */

CREATE PROCEDURE AIM_Alloc_DeleteCompleted_Sp 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @Return_Stat AS INT

	SET NOCOUNT ON

	BEGIN TRANSACTION
		-- Get details first.  This would be the bulk of the records.
		DELETE FROM AIMAODetail
		WHERE AIMAODetail.OrdNbr IN 
			(SELECT OrdNbr FROM AIMAO WHERE OrderStatus IN (30))
		-- Check for status.  Rollback if failed
		IF (@@ERROR <> 0)
		BEGIN
		  ROLLBACK TRANSACTION
		END
		ELSE
		BEGIN
			SET @Return_Stat = @@ROWCOUNT
		END

		-- Next, get the data in the order information table.  This is written internally.
		DELETE FROM AIMAO_OrderInfo 
		WHERE AIMAO_OrderInfo.OrderStatus IN (30)
		-- Check for status.  Rollback if failed
		IF (@@ERROR <> 0)
		BEGIN
		  ROLLBACK TRANSACTION
		END
		ELSE
		BEGIN
			SET @Return_Stat = @@ROWCOUNT
		END

		-- Finally, get the order header table
		DELETE FROM AIMAO 
		WHERE AIMAO.OrderStatus IN (30)
		-- Check for status.  Rollback if failed
		IF (@@ERROR <> 0)
		BEGIN
		  ROLLBACK TRANSACTION
		END
		ELSE
		BEGIN
			SET @Return_Stat = @@ROWCOUNT
		END

	COMMIT TRANSACTION
	
	RETURN @Return_Stat
END
GO
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

