SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIM_ItemHistoryAdjLog_Insert_Sp]') 
		AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_ItemHistoryAdjLog_Insert_Sp]
GO

/*
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
** 	Name: AIM_ItemHistoryAdjLog_Insert_Sp
** 	Purpose: Record the adjustments to the Item History,
**		as provided by the user via the Item History Adjustments screen.
** 		This procedure will perform two actions:
**		1.  insert a record into the ItemHistoryAdjLog table
** 		2.  update the relevant record in the ItemHistory table with the new value
**
** 		Version Number 	- 1.0
** 		Date Created 	- 2004/08/11
** 		Created By 		- Annalakshmi Stocksdale
**
********************************************************************************
** This file contains trade secrets of SSA Global. No part
** may be reproduced or transmitted in any form by any means or for any purpose
** without the express written permission of SSA Global.
********************************************************************************
**	Change History
********************************************************************************
**	Date:      	Author:      		Description:
**	-------- 	------------ 		-------------------------------------------
********************************************************************************
*/

CREATE PROCEDURE AIM_ItemHistoryAdjLog_Insert_Sp (
	@LcID as nvarchar (12)
	, @Item as nvarchar (25)
	, @SubsItem as nvarchar (25)
	, @HisYear as smallint
	, @DemandPeriod as tinyint
	, @DemandSource as nvarchar (1)
	, @OldValue as decimal (9, 1)
	, @NewValue as decimal (9, 1)
	, @UserID as nvarchar (12)
	, @Reason as nvarchar (255)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON
	DECLARE @Return_Stat as int
	DECLARE @CheckValue as decimal(9, 1)
	DECLARE @AdjustLogKey as bigint

	-- Validate the required parameters.
	IF @LcID IS NULL
	OR @Item IS NULL
	OR @HisYear IS NULL
	OR @DemandPeriod IS NULL
	OR @DemandSource IS NULL
	OR @OldValue IS NULL
	OR @NewValue IS NULL
	OR @UserID IS NULL
	OR @Reason IS NULL
	BEGIN
	  	RETURN -1
	END

	-- Validate the existence of the record in ItemHistory
	SELECT 1
	FROM ItemHistory
	WHERE ItemHistory.LcID = @LcId
	AND ItemHistory.Item = @Item
	AND ItemHistory.SubsItem = @SubsItem
	AND ItemHistory.HisYear = @HisYear
	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	BEGIN TRANSACTION
		IF @Return_Stat <= 0 
		BEGIN
-- -- 	Aug. 19, 2004 Annalakshmi says:
-- -- 	Mike?  What if the user tries to edit a record for which the database does not have any itemhistory record (it will show up as zeroes in the Forecast Sim.)?  Do I create a new record with zeroes for everything except the edited field(s)?  Or do I generate an error or a warning message?
-- -- 	Mike Uskert (Indianapolis) says:
-- -- 	In other words, if the user trys to enter history for a year that doesn't exist in the database? I think for this go around it should generate a warning message.
-- -- 	Annalakshmi says:
-- -- 	Ok.
-- 			INSERT INTO ItemHistory ( 
-- 				LcID, Item, SubsItem, HisYear,
-- 				QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, 
-- 				QtyOrd06, QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, 
-- 				QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, 
-- 				QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, 
-- 				QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, 
-- 				QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, 
-- 				QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, 
-- 				QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, 
-- 				QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, QtyOrd45, 
-- 				QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, 
-- 				QtyOrd51, QtyOrd52, 
-- 				CPS01, CPS02, CPS03, CPS04, CPS05,
-- 				CPS06, CPS07, CPS08, CPS09, CPS10,
-- 				CPS11, CPS12, CPS13, CPS14, CPS15,
-- 				CPS16, CPS17, CPS18, CPS19, CPS20,
-- 				CPS21, CPS22, CPS23, CPS24, CPS25,
-- 				CPS26, CPS27, CPS28, CPS29, CPS30,
-- 				CPS31, CPS32, CPS33, CPS34, CPS35,
-- 				CPS36, CPS37, CPS38, CPS39, CPS40,
-- 				CPS41, CPS42, CPS43, CPS44, CPS45,
-- 				CPS46, CPS47, CPS48, CPS49, CPS50,
-- 				CPS51, CPS52,
-- 				OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05,
-- 				OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10,
-- 				OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15,
-- 				OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, OrdCnt20,
-- 				OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25,
-- 				OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30,
-- 				OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35,
-- 				OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40,
-- 				OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45,
-- 				OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50,
-- 				OrdCnt51, OrdCnt52
-- 			) VALUES (
-- 				@LcID, @Item, @SubsItem, @HisYear,
-- 				-- Qty Ordered: 1 to 52
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 0, 0, 0, 
-- 				0, 0, 
-- 				-- Qty Shipped: 1 to 52
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0,
-- 				-- Order Count: 1 to 52
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0, 0, 0, 0,
-- 				0, 0
-- 			)
-- 			SET @Return_Stat = @@ROWCOUNT
-- 			-- Check for SQL Server errors.
-- 			IF @@ERROR <> 0 
-- 			BEGIN
-- 				ROLLBACK TRANSACTION
-- 			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
-- 			END
-- 			IF @Return_Stat <= 0
-- 			BEGIN
-- 				ROLLBACK TRANSACTION
-- 				RETURN -1
-- 			END
-- Annalakshmi: Remove the next two lines if the above insert is live.
			ROLLBACK TRANSACTION
			RETURN -9
		END
		ELSE		
		BEGIN
			IF @DemandSource = 'O'
			BEGIN
				-- Update the QtyOrdered
				UPDATE ItemHistory SET
					QtyOrd01 = CASE WHEN @DemandPeriod =  1 THEN @NewValue ELSE QtyOrd01 END,
					QtyOrd02 = CASE WHEN @DemandPeriod =  2 THEN @NewValue ELSE QtyOrd02 END,
					QtyOrd03 = CASE WHEN @DemandPeriod =  3 THEN @NewValue ELSE QtyOrd03 END,
					QtyOrd04 = CASE WHEN @DemandPeriod =  4 THEN @NewValue ELSE QtyOrd04 END,
					QtyOrd05 = CASE WHEN @DemandPeriod =  5 THEN @NewValue ELSE QtyOrd05 END,
					QtyOrd06 = CASE WHEN @DemandPeriod =  6 THEN @NewValue ELSE QtyOrd06 END,
					QtyOrd07 = CASE WHEN @DemandPeriod =  7 THEN @NewValue ELSE QtyOrd07 END,
					QtyOrd08 = CASE WHEN @DemandPeriod =  8 THEN @NewValue ELSE QtyOrd08 END,
					QtyOrd09 = CASE WHEN @DemandPeriod =  9 THEN @NewValue ELSE QtyOrd09 END,
					QtyOrd10 = CASE WHEN @DemandPeriod = 10 THEN @NewValue ELSE QtyOrd10 END,
					QtyOrd11 = CASE WHEN @DemandPeriod = 11 THEN @NewValue ELSE QtyOrd11 END,
					QtyOrd12 = CASE WHEN @DemandPeriod = 12 THEN @NewValue ELSE QtyOrd12 END,
					QtyOrd13 = CASE WHEN @DemandPeriod = 13 THEN @NewValue ELSE QtyOrd13 END,
					QtyOrd14 = CASE WHEN @DemandPeriod = 14 THEN @NewValue ELSE QtyOrd14 END,
					QtyOrd15 = CASE WHEN @DemandPeriod = 15 THEN @NewValue ELSE QtyOrd15 END,
					QtyOrd16 = CASE WHEN @DemandPeriod = 16 THEN @NewValue ELSE QtyOrd16 END,
					QtyOrd17 = CASE WHEN @DemandPeriod = 17 THEN @NewValue ELSE QtyOrd17 END,
					QtyOrd18 = CASE WHEN @DemandPeriod = 18 THEN @NewValue ELSE QtyOrd18 END,
					QtyOrd19 = CASE WHEN @DemandPeriod = 19 THEN @NewValue ELSE QtyOrd19 END,
					QtyOrd20 = CASE WHEN @DemandPeriod = 20 THEN @NewValue ELSE QtyOrd20 END,
					QtyOrd21 = CASE WHEN @DemandPeriod = 21 THEN @NewValue ELSE QtyOrd21 END,
					QtyOrd22 = CASE WHEN @DemandPeriod = 22 THEN @NewValue ELSE QtyOrd22 END,
					QtyOrd23 = CASE WHEN @DemandPeriod = 23 THEN @NewValue ELSE QtyOrd23 END,
					QtyOrd24 = CASE WHEN @DemandPeriod = 24 THEN @NewValue ELSE QtyOrd24 END,
					QtyOrd25 = CASE WHEN @DemandPeriod = 25 THEN @NewValue ELSE QtyOrd25 END,
					QtyOrd26 = CASE WHEN @DemandPeriod = 26 THEN @NewValue ELSE QtyOrd26 END,
					QtyOrd27 = CASE WHEN @DemandPeriod = 27 THEN @NewValue ELSE QtyOrd27 END,
					QtyOrd28 = CASE WHEN @DemandPeriod = 28 THEN @NewValue ELSE QtyOrd28 END,
					QtyOrd29 = CASE WHEN @DemandPeriod = 29 THEN @NewValue ELSE QtyOrd29 END,
					QtyOrd30 = CASE WHEN @DemandPeriod = 30 THEN @NewValue ELSE QtyOrd30 END,
					QtyOrd31 = CASE WHEN @DemandPeriod = 31 THEN @NewValue ELSE QtyOrd31 END,
					QtyOrd32 = CASE WHEN @DemandPeriod = 32 THEN @NewValue ELSE QtyOrd32 END,
					QtyOrd33 = CASE WHEN @DemandPeriod = 33 THEN @NewValue ELSE QtyOrd33 END,
					QtyOrd34 = CASE WHEN @DemandPeriod = 34 THEN @NewValue ELSE QtyOrd34 END,
					QtyOrd35 = CASE WHEN @DemandPeriod = 35 THEN @NewValue ELSE QtyOrd35 END,
					QtyOrd36 = CASE WHEN @DemandPeriod = 36 THEN @NewValue ELSE QtyOrd36 END,
					QtyOrd37 = CASE WHEN @DemandPeriod = 37 THEN @NewValue ELSE QtyOrd37 END,
					QtyOrd38 = CASE WHEN @DemandPeriod = 38 THEN @NewValue ELSE QtyOrd38 END,
					QtyOrd39 = CASE WHEN @DemandPeriod = 39 THEN @NewValue ELSE QtyOrd39 END,
					QtyOrd40 = CASE WHEN @DemandPeriod = 40 THEN @NewValue ELSE QtyOrd40 END,
					QtyOrd41 = CASE WHEN @DemandPeriod = 41 THEN @NewValue ELSE QtyOrd41 END,
					QtyOrd42 = CASE WHEN @DemandPeriod = 42 THEN @NewValue ELSE QtyOrd42 END,
					QtyOrd43 = CASE WHEN @DemandPeriod = 43 THEN @NewValue ELSE QtyOrd43 END,
					QtyOrd44 = CASE WHEN @DemandPeriod = 44 THEN @NewValue ELSE QtyOrd44 END,
					QtyOrd45 = CASE WHEN @DemandPeriod = 45 THEN @NewValue ELSE QtyOrd45 END,
					QtyOrd46 = CASE WHEN @DemandPeriod = 46 THEN @NewValue ELSE QtyOrd46 END,
					QtyOrd47 = CASE WHEN @DemandPeriod = 47 THEN @NewValue ELSE QtyOrd47 END,
					QtyOrd48 = CASE WHEN @DemandPeriod = 48 THEN @NewValue ELSE QtyOrd48 END,
					QtyOrd49 = CASE WHEN @DemandPeriod = 49 THEN @NewValue ELSE QtyOrd49 END,
					QtyOrd50 = CASE WHEN @DemandPeriod = 50 THEN @NewValue ELSE QtyOrd50 END,
					QtyOrd51 = CASE WHEN @DemandPeriod = 51 THEN @NewValue ELSE QtyOrd51 END,
					QtyOrd52 = CASE WHEN @DemandPeriod = 52 THEN @NewValue ELSE QtyOrd52 END
				WHERE ItemHistory.LcID = @LcId
				AND ItemHistory.Item = @Item
				AND ItemHistory.SubsItem = @SubsItem
				AND ItemHistory.HisYear = @HisYear
			END
			ELSE
			-- Update the QtyShipped 
			BEGIN
				UPDATE ItemHistory SET
					CPS01 = CASE WHEN @DemandPeriod =  1 THEN @NewValue ELSE CPS01 END,
					CPS02 = CASE WHEN @DemandPeriod =  2 THEN @NewValue ELSE CPS02 END,
					CPS03 = CASE WHEN @DemandPeriod =  3 THEN @NewValue ELSE CPS03 END,
					CPS04 = CASE WHEN @DemandPeriod =  4 THEN @NewValue ELSE CPS04 END,
					CPS05 = CASE WHEN @DemandPeriod =  5 THEN @NewValue ELSE CPS05 END,
					CPS06 = CASE WHEN @DemandPeriod =  6 THEN @NewValue ELSE CPS06 END,
					CPS07 = CASE WHEN @DemandPeriod =  7 THEN @NewValue ELSE CPS07 END,
					CPS08 = CASE WHEN @DemandPeriod =  8 THEN @NewValue ELSE CPS08 END,
					CPS09 = CASE WHEN @DemandPeriod =  9 THEN @NewValue ELSE CPS09 END,
					CPS10 = CASE WHEN @DemandPeriod = 10 THEN @NewValue ELSE CPS10 END,
					CPS11 = CASE WHEN @DemandPeriod = 11 THEN @NewValue ELSE CPS11 END,
					CPS12 = CASE WHEN @DemandPeriod = 12 THEN @NewValue ELSE CPS12 END,
					CPS13 = CASE WHEN @DemandPeriod = 13 THEN @NewValue ELSE CPS13 END,
					CPS14 = CASE WHEN @DemandPeriod = 14 THEN @NewValue ELSE CPS14 END,
					CPS15 = CASE WHEN @DemandPeriod = 15 THEN @NewValue ELSE CPS15 END,
					CPS16 = CASE WHEN @DemandPeriod = 16 THEN @NewValue ELSE CPS16 END,
					CPS17 = CASE WHEN @DemandPeriod = 17 THEN @NewValue ELSE CPS17 END,
					CPS18 = CASE WHEN @DemandPeriod = 18 THEN @NewValue ELSE CPS18 END,
					CPS19 = CASE WHEN @DemandPeriod = 19 THEN @NewValue ELSE CPS19 END,
					CPS20 = CASE WHEN @DemandPeriod = 20 THEN @NewValue ELSE CPS20 END,
					CPS21 = CASE WHEN @DemandPeriod = 21 THEN @NewValue ELSE CPS21 END,
					CPS22 = CASE WHEN @DemandPeriod = 22 THEN @NewValue ELSE CPS22 END,
					CPS23 = CASE WHEN @DemandPeriod = 23 THEN @NewValue ELSE CPS23 END,
					CPS24 = CASE WHEN @DemandPeriod = 24 THEN @NewValue ELSE CPS24 END,
					CPS25 = CASE WHEN @DemandPeriod = 25 THEN @NewValue ELSE CPS25 END,
					CPS26 = CASE WHEN @DemandPeriod = 26 THEN @NewValue ELSE CPS26 END,
					CPS27 = CASE WHEN @DemandPeriod = 27 THEN @NewValue ELSE CPS27 END,
					CPS28 = CASE WHEN @DemandPeriod = 28 THEN @NewValue ELSE CPS28 END,
					CPS29 = CASE WHEN @DemandPeriod = 29 THEN @NewValue ELSE CPS29 END,
					CPS30 = CASE WHEN @DemandPeriod = 30 THEN @NewValue ELSE CPS30 END,
					CPS31 = CASE WHEN @DemandPeriod = 31 THEN @NewValue ELSE CPS31 END,
					CPS32 = CASE WHEN @DemandPeriod = 32 THEN @NewValue ELSE CPS32 END,
					CPS33 = CASE WHEN @DemandPeriod = 33 THEN @NewValue ELSE CPS33 END,
					CPS34 = CASE WHEN @DemandPeriod = 34 THEN @NewValue ELSE CPS34 END,
					CPS35 = CASE WHEN @DemandPeriod = 35 THEN @NewValue ELSE CPS35 END,
					CPS36 = CASE WHEN @DemandPeriod = 36 THEN @NewValue ELSE CPS36 END,
					CPS37 = CASE WHEN @DemandPeriod = 37 THEN @NewValue ELSE CPS37 END,
					CPS38 = CASE WHEN @DemandPeriod = 38 THEN @NewValue ELSE CPS38 END,
					CPS39 = CASE WHEN @DemandPeriod = 39 THEN @NewValue ELSE CPS39 END,
					CPS40 = CASE WHEN @DemandPeriod = 40 THEN @NewValue ELSE CPS40 END,
					CPS41 = CASE WHEN @DemandPeriod = 41 THEN @NewValue ELSE CPS41 END,
					CPS42 = CASE WHEN @DemandPeriod = 42 THEN @NewValue ELSE CPS42 END,
					CPS43 = CASE WHEN @DemandPeriod = 43 THEN @NewValue ELSE CPS43 END,
					CPS44 = CASE WHEN @DemandPeriod = 44 THEN @NewValue ELSE CPS44 END,
					CPS45 = CASE WHEN @DemandPeriod = 45 THEN @NewValue ELSE CPS45 END,
					CPS46 = CASE WHEN @DemandPeriod = 46 THEN @NewValue ELSE CPS46 END,
					CPS47 = CASE WHEN @DemandPeriod = 47 THEN @NewValue ELSE CPS47 END,
					CPS48 = CASE WHEN @DemandPeriod = 48 THEN @NewValue ELSE CPS48 END,
					CPS49 = CASE WHEN @DemandPeriod = 49 THEN @NewValue ELSE CPS49 END,
					CPS50 = CASE WHEN @DemandPeriod = 50 THEN @NewValue ELSE CPS50 END,
					CPS51 = CASE WHEN @DemandPeriod = 51 THEN @NewValue ELSE CPS51 END,
					CPS52 = CASE WHEN @DemandPeriod = 52 THEN @NewValue ELSE CPS52 END
				WHERE ItemHistory.LcID = @LcId
				AND ItemHistory.Item = @Item
				AND ItemHistory.SubsItem = @SubsItem
				AND ItemHistory.HisYear = @HisYear
			END
			-- Check for errors
			SET @Return_Stat = @@ROWCOUNT
			IF @@ERROR <> 0 
			BEGIN
				ROLLBACK TRANSACTION
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
			IF @Return_Stat <= 0
			BEGIN
				ROLLBACK TRANSACTION
			 	RETURN -1  -- ERROR (Update failed)
			END
		
			INSERT INTO ItemHistoryAdjLog (
				LcID
				, Item
				, SubsItem
				, HisYear
				, DemandPeriod
				, DemandSource
				, OldValue
				, NewValue
				, AdjustDateTime
				, UserID
				, Reason
			) VALUES (
				@LcID
				, @Item
				, @SubsItem
				, @HisYear
				, @DemandPeriod
				, @DemandSource
				, @OldValue
				, @NewValue
				, GetDate()
				, @UserID
				, @Reason
			)
		
			SET @Return_Stat = @@ROWCOUNT
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
				ROLLBACK TRANSACTION
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
			IF @Return_Stat <= 0
			BEGIN
				ROLLBACK TRANSACTION
				RETURN -1
			END
			-- Fetch the key value (no use foreseen at this time, but if required, use this:)
			SELECT @AdjustLogKey = SCOPE_IDENTITY()
		END
	COMMIT TRANSACTION

	RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

