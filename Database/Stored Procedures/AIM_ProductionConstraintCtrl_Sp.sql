SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraintCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraintCtrl_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_ProductionConstraintCtrl_Sp
** 	Desc: Gets the  Distinct ConstrintId for VnId and Assortments or 
**            UniqueJobId to be processed and call AIM_ProductionConstraint_Sp 
**            to procress data for each ConstraintId.  The arguments VnId and 
**            Assortment  or UniqueJobId
**
** 	Returns: 	1)  1 - Success
** 			2) -1 - No Data Found
**             		3) -2 - SQL Error
** 	Values:
**              
** 	Auth:   Srinivas Uddanti
** 	Date:   03/24/2003
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:      Author:      Description:
** 	---------- ------------ -----------------------------------------------
** 
*******************************************************************************/

CREATE PROCEDURE AIM_ProductionConstraintCtrl_Sp
(
 	@VnId                   nvarchar(12) ='',
	@Assort                 nvarchar(12)='',
	@UniqueJobId	        varchar(255) =''
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
    
DECLARE @ConstId            nvarchar(30)

SET NOCOUNT ON

-- Validate the required parameters.
IF @VnId ='' and  @Assort ='' and @UniqueJobId =''
BEGIN
	RETURN -1
END

IF @VnId ='' and @Assort <>''
BEGIN
	RETURN -1
END

IF @VnId <>'' and @Assort =''
BEGIN
	RETURN -1
END

BEGIN
Create table #Prod_const_rcd 
(Lcid nvarchar(12),
Item nvarchar(25),
RSOQ  decimal(10,3),
LMin  decimal(10,3),
LMax  decimal(10,3),
Order_Qty  decimal(10,3),
XDock_YN  nvarchar(1),
OP_Split_Pct  decimal(10,3),
Item_Split_Pct  decimal(10,3),
Order_Point  decimal(10,3),
Min_Fullfill_Only  bit,
Over_Or_Below_Min decimal(10,3),
Repeat_Count int,
Multi_location  bit,
Order_Fullfilled  bit,
POSeqId  int,
ConvFactor  int,
Ratio_Type int,
Cycle_Days decimal(10,3)
)
Create table #XDoc_rcd
(Lcid nvarchar(12),
Item nvarchar(25),
RSOQ int
)
Create table #WithOut_XDoc_rcd
(Lcid nvarchar(12),
Item nvarchar(25),
Order_Point decimal(10,3),
RSOQ decimal(10,3))

Create table #Temp_rcd
(
Item nvarchar(25),
Sum_Order_Point decimal(10,3),
Sum_RSOQ decimal(10,3),
Item_Count int)
 IF @VnId <>'' and @Assort <>''
 Begin
  Declare Disct_Prod_Const Cursor
  For SELECT distinct(PC.Constraintid) "ConstraintID"
  FROM PODETAIL PO INNER JOIN ITEM 
  ON PO.ITEM =ITEM.ITEM AND
  PO.ASSORT =ITEM.ASSORT AND
  PO.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
  ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
  ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
  ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
  WHERE 
  ITEM.ASSORT =@Assort AND
 ITEM.VNID =@VnId AND 
  ITEM.ProductionConstraint='Y' 
  AND PO.RSOQ >0
End
 IF @UniqueJobId <>'' 
 Begin
  Declare Disct_Prod_Const Cursor
  For SELECT distinct(PC.Constraintid) "ConstraintID"
  FROM ProdConstraintTemp PQ INNER JOIN ITEM 
  ON PQ.ITEM =ITEM.ITEM AND
  PQ.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
  ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
  ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
  ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
  WHERE 
  PQ.UniqueJobId= @UniqueJobId AND
  ITEM.ProductionConstraint='Y' 
  AND PQ.Qty >0
End
  Open Disct_Prod_Const
  While 1 =1
  Begin
  Fetch Next from Disct_Prod_Const into @ConstId 
  If @@Fetch_Status <>0 
   Break
     EXEC AIM_ProductionConstraint_Sp @ConstId, @Vnid,@Assort,@UniqueJobId
  END
  END
Close Disct_Prod_Const
deallocate Disct_Prod_Const

Return 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

