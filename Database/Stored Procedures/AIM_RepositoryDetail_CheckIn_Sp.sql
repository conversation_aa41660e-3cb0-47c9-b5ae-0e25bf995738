SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RepositoryDetail_CheckIn_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RepositoryDetail_CheckIn_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_RepositoryDetail_CheckIn_Sp
**	Desc: Retrieves a count of records in the ForecastRepositoryDetail, given a RepositoryKey
**
**	Parameters: Forecast ID
**
**	Returns: 
**		 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/10/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	---------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_RepositoryDetail_CheckIn_Sp 
(
	@RepositoryKey as numeric(18, 0)
	, @RowCount as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON

	IF @RepositoryKey IS NULL
	BEGIN
		RETURN -1
	END

	SELECT @RowCount = COUNT(*)
	FROM ForecastRepositoryDetail
	WHERE ForecastRepositoryDetail.RepositoryKey = @RepositoryKey

	IF @@ERROR <> 0 
	BEGIN
		RETURN @@ERROR
	END
	ELSE
	BEGIN
		RETURN 1	-- Success
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


