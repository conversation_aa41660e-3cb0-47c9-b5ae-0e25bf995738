IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_AIMDays_GetFiscalPeriod_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_AIMDays_GetFiscalPeriod_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/********************************************************************************
*********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** Name: AIM_AIMDays_GetFiscalPeriod_Sp
** Desc: Returns the fiscal period for a given date
**
** Returns: 
** Values:  
**              
** Auth:   Annalakshmi Stocksdale
** Date:   2004/01/27
*******************************************************************************
** Change History
*******************************************************************************
** 	Date:    	Author:        	Description:
** 	---------- 	------------ 	-----------------------------------------------
**	12/22/04	prk		Could not double click on Days. Replaced 
					Replaced Cureent Version with Previous
*******************************************************************************/

CREATE  PROCEDURE AIM_AIMDays_GetFiscalPeriod_Sp
(
	@TargetDate datetime,
	@FiscalPeriod int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON 

	DECLARE @FYStartDate datetime

	-- Determine fiscal year and start date
	SELECT TOP 1 @FYStartDate = AIMYears.FYStartDate
	FROM AIMYears
	WHERE AIMYears.FYEndDate >= @TargetDate
	ORDER BY AIMYears.FYStartDate

	IF ISDATE(@FYStartDate) = 1
	BEGIN
		-- Determine fiscal period
	    	SET @FiscalPeriod = (DateDiff("d", @FYStartDate, @TargetDate) / 7) + 1
		IF @FiscalPeriod > 52
		BEGIN
			SET @FiscalPeriod = 52
		END		
			
		RETURN 1
	END
	ELSE
	BEGIN
		-- error
		RETURN -1
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


