SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UserElementDetail_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UserElementDetail_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UserElementDetail_Get_Sp
**	Desc: Gets UserElementDetail Data for a given UserElementId
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset -UserElementDetail Recored set
**              
**	Auth:   Srinivas Uddanti 
**	Date:   10/18/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/

CREATE    PROCEDURE AIM_UserElementDetail_Get_Sp
(
 	@UserElementId 	        nvarchar(12),       
       	@LcID     		nvarchar(12),
        @Item   		nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @UserElementId IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

SELECT FcstPdBegDate,FcstPdEndDate,COALESCE(Qty,0) Qty
FROM UserElementDetail 
WHERE 
UserElementId =@UserElementId AND
LcID = @LcID and 
Item = @Item
ORDER BY FcstPdBegDate

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END		

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
