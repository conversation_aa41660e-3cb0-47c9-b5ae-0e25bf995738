SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO


IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_Alloc_UpdateAggregates_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_Alloc_UpdateAggregates_Sp]
GO


/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:				Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:				Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp				Main Stored Procedure
**
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**											Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_Alloc_UpdateAggregates_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Called by the Allocation Stored Procedure -- AIM_AllocationCtrl_Sp
**		Updates Order information, specifically:
**			OrderStatus
**			LineCount
**			TotalUnits
**			LocFillPct
**			LineItemStatus
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/21
**-----------------------------------------------------------------------------
**	Change History
**-----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------		-----------------------------------------------
**	2003/09/25	A.Stocksdale	Modified to include AIMAO_OrderInfo	
**	2004/07/09	S.Uddanti	Modified code so that primarykey constraint 
					Error does not occur in AIMAO_OrderInfo table						
*******************************************************************************/

CREATE  PROCEDURE AIM_Alloc_UpdateAggregates_Sp
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @AIMAODetailAgg TABLE
		(OrdNbr nvarchar (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
		LineNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS,
		AllocatedQty int,
		LineFillPct decimal (12, 2)) 
		
	DECLARE @AIMAOAggregates TABLE 
		(OrdNbr nvarchar (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
		Lcid nvarchar (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
		OriginCode nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS,
		UserInitials nvarchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS,
		LineCount int,
		TotalUnits int,
		LocFillPct decimal (12, 2),
		ReviewerID nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS ) 

	DECLARE @AIMAO_OrderAggregates TABLE 
		(OrdNbr nvarchar (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
		OrderStatus tinyint,
		UserInitials nvarchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS,
		LineCount int ,
		TotalUnits int,
		OrderFillPct decimal (12, 2)
		)

	SET NOCOUNT ON

	-- LINE ITEM LEVEL
	-- SQL does not allow an update using aggregates, therefore, 
	-- get aggregates into the table variable
	INSERT INTO @AIMAODetailAgg (OrdNbr, LineNbr, AllocatedQty, LineFillPct) 
	SELECT AIMAODetail.OrdNbr
			, AIMAODetail.LineNbr
			, SUM(AIMAODetail.AllocatedQty)
			, CAST(100 * (
					CAST(SUM(AIMAODetail.AllocatedQty) AS DECIMAL (12, 2))
					/ CAST(AIMAODetail.RequestedQty AS DECIMAL (12, 2))
					) AS DECIMAL (12, 2)) 
	FROM AIMAODetail
	WHERE AIMAODetail.LType = 'S'
	GROUP BY AIMAODetail.OrdNbr
			, AIMAODetail.LineNbr
			, AIMAODetail.RequestedQty

	-- Now, update the table using the aggregates above.
	UPDATE AIMAODetail
	SET AllocatedQty  = agg.AllocatedQty
		, AdjustedAllocQty = agg.AllocatedQty
		, LineFillPct = agg.LineFillPct
	FROM @AIMAODetailAgg agg
	WHERE AIMAODetail.OrdNbr = agg.OrdNbr
		AND AIMAODetail.LineNbr = agg.LineNbr
	AND AIMAODetail.LType = 'D'

	-- LOCATION LEVEL
	-- SQL does not allow an update using aggregates, therefore, 
	-- get aggregates into the table variable
	INSERT INTO @AIMAOAggregates (OrdNbr, Lcid, OriginCode, UserInitials, 
				LineCount, TotalUnits, LocFillPct, ReviewerID) 
	SELECT AIMAO.OrdNbr
			, AIMAO.LcID
			, AIMAO.OriginCode
			, AIMAO.UserInitials
			, Count(AIMAODetail.LineNbr)
			, SUM(AIMAODetail.AllocatedQty)
			, CAST(100 * (
					CAST(SUM(AIMAODetail.AllocatedQty) AS DECIMAL (12, 2))
					/ CAST(SUM(AIMAODetail.RequestedQty) AS DECIMAL (12, 2))
					) AS DECIMAL (12, 2))
			, AIMAO.ReviewerID
	FROM AIMAO
	INNER JOIN AIMAODetail
		ON AIMAO.OrdNbr = AIMAODetail.OrdNbr
		AND AIMAO.LcID = AIMAODetail.LcID
		WHERE AIMAODetail.LType = 'D'
	GROUP BY AIMAO.OrdNbr
			, AIMAO.LcID
			, AIMAO.OriginCode
			, AIMAO.UserInitials
			, AIMAO.ReviewerID
	ORDER BY AIMAO.OrdNbr
		, AIMAO.LcID

	-- Now, update the table using the aggregates above.
	UPDATE AIMAO
	SET LineCount = agg.LineCount
	, TotalUnits = agg.TotalUnits
	, LocFillPct = agg.LocFillPct
	FROM @AIMAOAggregates agg
		WHERE AIMAO.OrdNbr = agg.OrdNbr
		AND AIMAO.LcID = agg.LcID


	-- ORDER LEVEL
	-- SQL does not allow an update using aggregates, therefore, 
	-- get aggregates into the table variable
	INSERT INTO @AIMAO_OrderAggregates (OrdNbr, OrderStatus, 
				LineCount, TotalUnits, OrderFillPct) 
	SELECT AIMAO.OrdNbr
			, AIMAO.OrderStatus
			, Count(AIMAODetail.LineNbr)
			, SUM(AIMAODetail.AllocatedQty)
			, CAST(100 * (
					CAST(SUM(AIMAODetail.AllocatedQty) AS DECIMAL (12, 2))
					/ CAST(SUM(AIMAODetail.RequestedQty) AS DECIMAL (12, 2))
					) AS DECIMAL (12, 2)) 
	FROM AIMAO
	INNER JOIN AIMAODetail
		ON AIMAO.OrdNbr = AIMAODetail.OrdNbr
		AND AIMAO.LcID = AIMAODetail.LcID
	WHERE AIMAODetail.LType = 'D'
	AND AIMAO.OrderStatus NOT IN (11, 12)
	GROUP BY AIMAO.OrdNbr
			, AIMAO.OrderStatus
	ORDER BY AIMAO.OrdNbr

	-- Now, update the table using the aggregates above.
	UPDATE AIMAO_OrderInfo
	SET LineCount = agg.LineCount
	, TotalUnits = agg.TotalUnits
	, OrderFillPct = agg.OrderFillPct
	FROM @AIMAO_OrderAggregates agg
		WHERE AIMAO_OrderInfo.OrdNbr = agg.OrdNbr
	IF @@RowCount = 0
	BEGIN
		INSERT INTO AIMAO_OrderInfo
		SELECT agg.OrdNbr
				, agg.OrderStatus
				, ''
				, agg.LineCount
				, agg.TotalUnits
				, agg.OrderFillPct
		FROM @AIMAO_OrderAggregates agg
		WHERE agg.OrdNbr NOT IN (SELECT AIMAO_OrderInfo.OrdNbr FROM AIMAO_OrderINfo)
	END

END
GO
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

