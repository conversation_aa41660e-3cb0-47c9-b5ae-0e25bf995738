SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorSizing_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorSizing_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VendorSizing_Sp
**	Desc: Verifies if the DX File has been run before.
**
**	Returns: 1)  1 - Successful
**               2)  0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**      10/30/2002 Wade Riza 	Updated validation and Error handling
**	04/20/2004 S Uddanti	Run VendorSizing for item with OrdStatus in ('R','P')
*******************************************************************************/
    
CREATE PROCEDURE AIM_VendorSizing_Sp
(
      	@VnId               				nvarchar(12),
      	@Assort             				nvarchar(12),
        @Reach_Code         				nvarchar(1),
        @Vn_Min             				decimal(9,2),
        @Vn_Best            				decimal(9,2)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE 
        -- Cursor
        @LcId           					nvarchar(12),
        @Item           					nvarchar(25),
        @Weight         					decimal(10,4),
        @Cube           					decimal(10,4),
        @Cost           					decimal(10,4),
        @POSeqId        					int,
        @RSOQ           					int,
        @SOQ            					int,
        @VSOQ           					int,
        @UOM            					nvarchar(6),
        @BuyingUOM      					nvarchar(6),
        @ConvFactor     					smallint,
        @PackRounding   					nvarchar(1),
        @IMin           					int,
        @IMax           					int,
        @Burn           					decimal(10,4),
        @MDCFlag        					nvarchar(1),
        @Original_VSOQ  					int,
            
        -- Working Storage
        @RSOQAmt        					decimal(10,4),
        @VSOQAmt        					decimal(10,4),
        @SOQAmt         					decimal(10,4),
        @BuyGoal        					decimal(10,4),
        @DailyReach     					decimal(10,4),
        @AddlDays       					float,
        @LoopCounter    					int,
        @CurVSOQ        					float,
            	
        -- System Control
        @MinRchPct      					decimal(5,3),
        @BestRchPct     					decimal(5,3)

SET NOCOUNT ON

-- Validate the required parameters.
IF @VnID IS NULL
BEGIN
	RETURN 0
END

IF @Assort IS NULL
BEGIN
	RETURN 0
END

IF @Reach_Code IS NULL
BEGIN
	RETURN 0
END

IF @Vn_Min IS NULL
BEGIN
	RETURN 0
END

IF @Vn_Best IS NULL
BEGIN
	RETURN 0
END

-- Initialize Counters/Totals
SELECT @CurVSOQ = 0, @AddlDays = 0, @DailyReach = 0, @BuyGoal = 0, @SOQAmt = 0,
        @VSOQAmt = 0, @RSOQAmt = 0
  
-- Get the Minimum Reach Percentages
SELECT @MinRchPct = Dft_MinRchPct / 100, @BestRchPct = Dft_BestRchPct / 100
FROM SysCtrl
  
-- Initialize Variables
DECLARE Sizing_Cursor CURSOR LOCAL SCROLL KEYSET OPTIMISTIC FOR
        SELECT Item.LcId, Item.Item, Item.Weight, Item.Cube, Item.Cost, PODetail.POSeqID, 
        PODetail.RSOQ, PODetail.SOQ, PODetail.VSOQ, PODetail.UOM, 
        PODetail.BuyingUOM, PODetail.ConvFactor, PODetail.PackRounding, 
        Item.IMin, Item.IMax,
        Burn = case
        WHEN Item.Accum_LT + Item.ReviewTime > 0
            THEN (Item.FcstLT + Item.FcstRT) / (Item.Accum_LT + Item.ReviewTime)
        ELSE 0
        END,
        Item.MDCFlag, PODetail.Original_VSOQ
        FROM PODetail 
        INNER JOIN Item ON PODetail.Lcid = Item.Lcid 
	AND PODetail.Item = Item.Item 
        WHERE PODetail.VnId = @VnId 
	AND PODetail.Assort = @Assort
        AND PODetail.RSOQ > 0
        AND PODetail.OrdStatus in( 'P','R')
        FOR UPDATE OF PODetail.VSOQ, PODetail.Original_VSOQ
        
  OPEN Sizing_Cursor
  -- Cursor Loop
  WHILE 1 = 1
  BEGIN
        FETCH NEXT FROM Sizing_Cursor INTO 
            @LcId, @Item, @Weight, @Cube, @Cost, @POSeqId, @RSOQ, @SOQ, @VSOQ, @UOM, 
            @BuyingUOM, @ConvFactor, @PackRounding, @IMin, @IMax, @Burn,
            @MDCFlag, @Original_VSOQ
            
        -- Check for End of Cursor
        IF @@FETCH_STATUS <> 0
            BREAK
            
        -- Is this an MDC Item
        IF @MDCFlag = 'Y'
        BEGIN
        
            SELECT @Burn = case
                WHEN MDCSummary.Accum_LT + MDCSummary.ReviewTime > 0
                THEN (MDCSummary.FcstLT + MDCSummary.FcstRT) / (MDCSummary.Accum_LT + MDCSummary.ReviewTime)
            ELSE 0
            END
            FROM MDCSummary
            WHERE MDC = @LcId
            AND Item = @Item
        END
            
        -- Calculate the Order Totals
        SELECT @RSOQAmt = @RSOQAmt + CASE @Reach_Code
            WHEN 'C' THEN @RSOQ * @Cube
            WHEN 'D' THEN @RSOQ * @Cost
            WHEN 'U' THEN @RSOQ 
            WHEN 'W' THEN @RSOQ * @Weight
            END,
            @VSOQAmt = @VSOQAmt + case @Reach_Code
            WHEN 'C' THEN @VSOQ * @Cube
            WHEN 'D' THEN @VSOQ * @Cost
            WHEN 'U' THEN @VSOQ 
            WHEN 'W' THEN @VSOQ * @Weight
            END,
            @SOQAmt = @SOQAmt + case @Reach_Code
            WHEN 'C' THEN @SOQ * @Cube
            WHEN 'D' THEN @SOQ * @Cost
            WHEN 'U' THEN @SOQ 
            WHEN 'W' THEN @SOQ * @Weight
            END,
            @DailyReach = @DailyReach + case @Reach_Code
            WHEN 'C' THEN @Burn * @Cube
            WHEN 'D' THEN @Burn * @Cost
            WHEN 'U' THEN @Burn 
            WHEN 'W' THEN @Burn * @Weight
            END
            
  END
  -- Determine the Buying Target; Either the Vendor Minimum or Best Buy 
  IF @SOQAmt > (@MinRchPct * @Vn_Min)
        SELECT @BuyGoal = @Vn_Min
  IF @SOQAmt > (@BestRchPct * @Vn_Best)
        SELECT @BuyGoal = @Vn_Best
        
  -- IF @BuyGoal = 0 and @Vn_Min > 0
  -- select @BuyGoal = @Vn_Min
        
  -- IF @BuyGoal = 0 and @Vn_Min <= 0 
  -- SELECT @BuyGoal = @Vn_Best
        
  -- IF the Buy Goal is Zero, or
  -- IF the Suggested Order Quantity Amount >= Buy Goal, or
  -- IF the Daily Reach Amount is Zero, no processing is required
  IF @BuyGoal = 0 or @SOQAmt >= @BuyGoal or @DailyReach <= 0
  BEGIN
        CLOSE Sizing_Cursor
        DEALLOCATE Sizing_Cursor
        RETURN
  END
        
  -- Calculate the number of Additional Days
  SELECT @AddlDays = (@BuyGoal - @RSOQAmt) / @DailyReach
        
  -- Initialize Loop Variables
  SELECT  @LoopCounter = 0,
            @VSOQAmt = 0
  -- Start Sizing Loop
  WHILE @VSOQAmt < @BuyGoal
  BEGIN
        -- Increment the Loop Counter
        SELECT @LoopCounter = @LoopCounter + 1
        
        -- Don't do this forever
        IF @LoopCounter > 100
            BREAK
            
        -- Iniitalize VSOQ Amount
        SELECT @VSOQAmt = 0
        
        -- Move to first row in cursor
        FETCH FIRST FROM Sizing_Cursor INTO 
            @LcId, @Item, @Weight, @Cube, @Cost, @POSeqId, @RSOQ, @SOQ, @VSOQ, @UOM, 
            @BuyingUOM, @ConvFactor, @PackRounding, @IMin, @IMax, @Burn,
            @MDCFlag, @Original_VSOQ
            
        -- Start Cursor Loop
        WHILE 1 = 1
        BEGIN
        
            -- Check for End of Cursor
            IF @@FETCH_STATUS <> 0
                BREAK
        
            -- Calculate the new VSOQ for this row
            SELECT @CurVSOQ = @RSOQ + (@AddlDays * @Burn)
            -- Pack round the new VSOQ
            EXEC @CurVSOQ = AIM_PackRounding_Sp @CurVSOQ, @UOM, @ConvFactor, @BuyingUOM, @PackRounding, @IMin, @IMax
            
            -- Accumulate Totals
            SELECT @VSOQAmt = @VSOQAmt + case @Reach_Code
                WHEN 'C' THEN @CurVSOQ * @Cube
                WHEN 'D' THEN @CurVSOQ * @Cost
                WHEN 'U' THEN @CurVSOQ 
                WHEN 'W' THEN @CurVSOQ * @Weight
                END
            
            -- Update PO Detail Table
            UPDATE PODetail
                SET VSOQ = @CurVSOQ, Original_VSOQ = @CurVSOQ
                WHERE CURRENT OF Sizing_Cursor
            
            -- Enough ?
            IF @VSOQAmt >= @BuyGoal
                BREAK
            
            -- Get the next row
            FETCH NEXT FROM Sizing_Cursor INTO 
                @LcId, @Item, @Weight, @Cube, @Cost, @POSeqId, @RSOQ, @SOQ, @VSOQ, @UOM, 
                @BuyingUOM, @ConvFactor, @PackRounding, @IMin, @IMax, @Burn,
                @MDCFlag, @Original_VSOQ
        END         -- End of Cursor Loop
        
        -- Increment additional days    
        SELECT @AddlDays = @AddlDays + .1
        
END     -- End of Sizing Loop
-- Wrap Up
CLOSE Sizing_Cursor
DEALLOCATE Sizing_Cursor

RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

