SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHistory_Copy_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHistory_Copy_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_ItemHistory_Copy_Sp
** 	Desc: Copies Item History Data to another Item.
**
** 	Returns: 1)@found - Can be Zero
**               2) -1 - No Data Found
**               3) -2 - SQL Error
** 	Values:  Recordset - AccessType
**              
** 	Auth:   Srinivas Uddanti 
** 	Date:   08/15/2002
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:    	Author:     Description:
** 	---------- ------------ -----------------------------------------------
** 	08/16/2002 Wade Riza    Updated Return Codes and Error Handling
** 	09/30/2002 Srinivas U
** 	10/09/2002 Wade Riza    Updated ScalingF declaration to support UI
** 	10/30/2002 Srinivas U   Added code for option on Item History Copy
**	09/12/2003 Srinivas U   Modified code for SubsItem
**	11/12/2004	Wade Riza	Modified code to remove references to Forecast Repository
*******************************************************************************/

CREATE PROCEDURE AIM_ItemHistory_Copy_Sp 
(
	@LocationId     		nvarchar(12),
	@OldItemId     			nvarchar(25), 
	@NewItemId      		nvarchar(25),
	@DeleteOpt     			tinyint = 0,
	@TransBudFcst     		tinyint = 0,
	@ScalingF     			decimal(5,4) =1.0,
 	@CopyHistoryData 		tinyint  
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE
	@HsCount     			tinyint,
	@CountBothExists    		tinyint,
	@LocationKey     		nvarchar(12), 
	@OptionID     			nvarchar(6),
	@VelCode     			nvarchar(1),
	@FcstMethod     		tinyint,
	@FcstDemand     		decimal(10,2),
	@UserFcst     			decimal(10,2),
	@UserFcstExpDate    		datetime,
	@MAE      			decimal(10,2),
	@MSE      			decimal(10,2),
	@Trend      			decimal(10,3),
	@FcstCycles     		int,
	@ZeroCount     			int,
	@Mean_NZ     			decimal(10,2),
	@StdDev_NZ     			decimal(10,2),
	@IntSafetyStock     		decimal(10,2),
	@IsIntermittent     		nvarchar(1),
	@DIFlag      			nvarchar(1),
	@DmdFilterFlag     		nvarchar(1),
	@TrkSignalFlag     		nvarchar(1),
	@UserDemandFlag    	 	nvarchar(1),
	@ByPassPct     			decimal(4,3),
	@FcstUpdCyc     		int,
	@PlnTT      			nvarchar(1),
	@OrderPt     			int,
	@OrderQty     			int,
	@SafetyStock     		decimal(10,2),
	@FcstRT      			decimal(10,2),
	@FcstLT      			decimal(10,2),
	@Fcst_Month     		decimal(10,2),
	@Fcst_Quarter     		decimal(10,2),
	@Fcst_Year     			decimal(10,2),
	@FcstDate     			smalldatetime,
	@VC_Amt      			nvarchar(1),
	@VC_Units_Ranking    		int,
	@VC_Amt_Ranking     		int,
	@VC_Date     			smalldatetime,
	@Freeze_Forecast    		nvarchar(1)

SET NOCOUNT ON

-- Validate the required parameters.
IF @LocationID IS NULL
BEGIN
	RETURN -1
END

IF @OldItemID IS NULL
BEGIN
	RETURN -1
END

IF @NewItemID IS NULL
BEGIN
	RETURN -1
END

IF @LocationId ='ALL'   --Do for items in all locations
BEGIN
DECLARE LocationCursor CURSOR LOCAL FORWARD_ONLY STATIC FOR  
          SELECT DISTINCT(LcID) FROM AIMLocations
          ORDER BY LcID
END
ELSE
BEGIN
	DECLARE LocationCursor CURSOR LOCAL FORWARD_ONLY STATIC FOR  
          SELECT  LcID FROM AIMLocations
          WHERE LcID = @LocationId
END
OPEN LocationCursor
 
FETCH NEXT FROM LocationCursor INTO @LocationKey
WHILE @@FETCH_STATUS = 0
BEGIN
SET @CountBothExists =0
SELECT @CountBothExists = COUNT(*) 
FROM item
WHERE item IN (@OldItemId,@NewItemId) 
AND lcid =@LocationKey
  
IF @CountBothExists =2 
BEGIN
 
	SELECT @OptionID=OptionID,@VelCode=VelCode,@FcstMethod=FcstMethod,
	@FcstDemand=FcstDemand,@UserFcst=UserFcst,
	@UserFcstExpDate=UserFcstExpDate,@MAE=MAE,@MSE=MSE,@Trend=Trend,
	@FcstCycles=FcstCycles,@ZeroCount=ZeroCount,@Mean_NZ=Mean_NZ,
	@StdDev_NZ=StdDev_NZ,@IntSafetyStock=IntSafetyStock,
	@IsIntermittent=IsIntermittent,@DIFlag=DIFlag,
	@DmdFilterFlag=DmdFilterFlag,@TrkSignalFlag=TrkSignalFlag,
	@UserDemandFlag=UserDemandFlag,@ByPassPct=ByPassPct,
	@FcstUpdCyc=FcstUpdCyc,@PlnTT=PlnTT,@OrderPt=OrderPt,
	@OrderQty=OrderQty,@SafetyStock=SafetyStock,@FcstRT=FcstRT,
	@FcstLT=FcstLT,@Fcst_Month=Fcst_Month,@Fcst_Quarter=Fcst_Quarter,
	@Fcst_Year=Fcst_Year,@FcstDate=FcstDate,@VC_Amt=VC_Amt,
	@VC_Units_Ranking=VC_Units_Ranking,@VC_Amt_Ranking=VC_Amt_Ranking,
	@VC_Date=VC_Date,@Freeze_Forecast=Freeze_Forecast
	FROM Item 
	WHERE LcId =@LocationKey AND Item =@OldItemId 
 
	-- Check for SQL Server errors.
  	IF @@ERROR <> 0 
   	BEGIN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END

	UPDATE Item 
	SET  VelCode=@VelCode,
 	FcstMethod=@FcstMethod,FcstDemand=@FcstDemand*@ScalingF,
 	UserFcst=@UserFcst*@ScalingF,UserFcstExpDate=@UserFcstExpDate,
 	MAE=@MAE,MSE=@MSE,Trend=@Trend*@ScalingF,FcstCycles=@FcstCycles,
 	ZeroCount=@ZeroCount,Mean_NZ=@Mean_NZ,StdDev_NZ=@StdDev_NZ,
 	IntSafetyStock=@IntSafetyStock,IsIntermittent=@IsIntermittent,
 	DIFlag=@DIFlag,DmdFilterFlag=@DmdFilterFlag,TrkSignalFlag=@TrkSignalFlag,
 	UserDemandFlag=@UserDemandFlag,ByPassPct=@ByPassPct,
 	FcstUpdCyc=@FcstUpdCyc,PlnTT=@PlnTT,OrderPt=@OrderPt*@ScalingF,
 	OrderQty=@OrderQty*@ScalingF,SafetyStock=@SafetyStock*@ScalingF,
 	FcstRT=@FcstRT*@ScalingF,FcstLT=@FcstLT*@ScalingF,
 	Fcst_Month=@Fcst_Month*@ScalingF,Fcst_Quarter=@Fcst_Quarter*@ScalingF,
 	Fcst_Year=@Fcst_Year*@ScalingF,FcstDate=@FcstDate,VC_Amt=@VC_Amt,
 	VC_Units_Ranking=@VC_Units_Ranking,VC_Amt_Ranking=@VC_Amt_Ranking,
 	VC_Date=@VC_Date,Freeze_Forecast=@Freeze_Forecast
 	FROM Item 
	WHERE LcId =@LocationKey AND Item =@NewItemId 
 
 	IF @@ERROR <> 0 
 	BEGIN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END
 
	IF @CopyHistoryData =1 
	BEGIN
		DELETE ItemHistory 
		WHERE LcID = @LocationKey 
		AND Item = @NewItemId

		 -- CREATE the Item History Table
   		SELECT @HsCount = 0

  		SELECT @HsCount = COUNT(*) 
		FROM ItemHistory 
 		WHERE Lcid = @LocationKey 
		AND Item = @NewItemId
		AND SubsItem =@NewItemId
		
   		IF @HsCount = 0
   		BEGIN
		-- set the cps values to zero since we will come back and update them
		-- with sum of subitem and main item afterwards sri
        	INSERT INTO ItemHistory 
            	(LcId, Item,SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, CPS10, 
            	CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, CPS23, 
            	CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, 
            	CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, 
            	CPS50, CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, 
            	QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, 
            	QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, 
            	QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, 
            	QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, 
            	QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, 
            	OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, 
            	OrdCnt10, OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, 
            	OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, 
            	OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, 
            	OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, 
            	OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52) 
            		SELECT LcId,@NewItemId,@NewItemId,HisYear,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,QtyOrd01*@ScalingF, 
     			QtyOrd02*@ScalingF, QtyOrd03*@ScalingF, QtyOrd04*@ScalingF, QtyOrd05*@ScalingF, 
     			QtyOrd06*@ScalingF, QtyOrd07*@ScalingF, QtyOrd08*@ScalingF, QtyOrd09*@ScalingF, 
     			QtyOrd10*@ScalingF, QtyOrd11*@ScalingF, QtyOrd12*@ScalingF, QtyOrd13*@ScalingF, 
     			QtyOrd14*@ScalingF, QtyOrd15*@ScalingF, QtyOrd16*@ScalingF, QtyOrd17*@ScalingF, 
     			QtyOrd18*@ScalingF, QtyOrd19*@ScalingF, QtyOrd20*@ScalingF, QtyOrd21*@ScalingF, 
     			QtyOrd22*@ScalingF, QtyOrd23*@ScalingF, QtyOrd24*@ScalingF, QtyOrd25*@ScalingF, 
     			QtyOrd26*@ScalingF, QtyOrd27*@ScalingF, QtyOrd28*@ScalingF, QtyOrd29*@ScalingF, 
     			QtyOrd30*@ScalingF, QtyOrd31*@ScalingF, QtyOrd32*@ScalingF, QtyOrd33*@ScalingF, 
     			QtyOrd34*@ScalingF, QtyOrd35*@ScalingF, QtyOrd36*@ScalingF, QtyOrd37*@ScalingF, 
     			QtyOrd38*@ScalingF, QtyOrd39*@ScalingF, QtyOrd40*@ScalingF, QtyOrd41*@ScalingF, 
     			QtyOrd42*@ScalingF, QtyOrd43*@ScalingF, QtyOrd44*@ScalingF, QtyOrd45*@ScalingF, 
     			QtyOrd46*@ScalingF, QtyOrd47*@ScalingF, QtyOrd48*@ScalingF, QtyOrd49*@ScalingF, 
     			QtyOrd50*@ScalingF, QtyOrd51*@ScalingF, QtyOrd52*@ScalingF, OrdCnt01*@ScalingF, 
     			OrdCnt02*@ScalingF, OrdCnt03*@ScalingF, OrdCnt04*@ScalingF, OrdCnt05*@ScalingF, 
     			OrdCnt06*@ScalingF, OrdCnt07*@ScalingF, OrdCnt08*@ScalingF, OrdCnt09*@ScalingF, 
     			OrdCnt10*@ScalingF, OrdCnt11*@ScalingF, OrdCnt12*@ScalingF, OrdCnt13*@ScalingF, 
     			OrdCnt14*@ScalingF, OrdCnt15*@ScalingF, OrdCnt16*@ScalingF, OrdCnt17*@ScalingF, 
     			OrdCnt18*@ScalingF, OrdCnt19*@ScalingF, OrdCnt20*@ScalingF, OrdCnt21*@ScalingF, 
     			OrdCnt22*@ScalingF, OrdCnt23*@ScalingF, OrdCnt24*@ScalingF, OrdCnt25*@ScalingF, 
     			OrdCnt26*@ScalingF, OrdCnt27*@ScalingF, OrdCnt28*@ScalingF, OrdCnt29*@ScalingF, 
     			OrdCnt30*@ScalingF, OrdCnt31*@ScalingF, OrdCnt32*@ScalingF,  OrdCnt33*@ScalingF, 
     			OrdCnt34*@ScalingF, OrdCnt35*@ScalingF, OrdCnt36*@ScalingF, OrdCnt37*@ScalingF, 
     			OrdCnt38*@ScalingF, OrdCnt39*@ScalingF, OrdCnt40*@ScalingF, OrdCnt41*@ScalingF, 
     			OrdCnt42*@ScalingF, OrdCnt43*@ScalingF, OrdCnt44*@ScalingF, OrdCnt45*@ScalingF, 
     			OrdCnt46*@ScalingF, OrdCnt47*@ScalingF, OrdCnt48*@ScalingF, OrdCnt49*@ScalingF, 
     			OrdCnt50*@ScalingF, OrdCnt51*@ScalingF, OrdCnt52*@ScalingF
        		FROM ItemHistory
         		WHERE LcId = @LocationKey 
			AND Item = @OldItemId
			AND SubsItem =@OldItemId
		
			IF @@ERROR <> 0 
   			BEGIN
 				RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  			END
		SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
		sum(ItemHistory.CPS01)*@ScalingF CPS01, 
	      	sum(ItemHistory.CPS02)*@ScalingF CPS02, sum(ItemHistory.CPS03)*@ScalingF CPS03,sum( ItemHistory.CPS04)*@ScalingF CPS04,
		sum(ItemHistory.CPS05)*@ScalingF CPS05, sum(ItemHistory.CPS06)*@ScalingF CPS06,sum(ItemHistory.CPS07)*@ScalingF CPS07, 
	      	sum(ItemHistory.CPS08)*@ScalingF CPS08, sum(ItemHistory.CPS09)*@ScalingF CPS09,sum(ItemHistory.CPS10)*@ScalingF CPS10,
		sum(ItemHistory.CPS11)*@ScalingF CPS11, sum(ItemHistory.CPS12)*@ScalingF CPS12,sum( ItemHistory.CPS13)*@ScalingF CPS13, 
	       	sum(ItemHistory.CPS14)*@ScalingF CPS14, sum(ItemHistory.CPS15)*@ScalingF CPS15,sum( ItemHistory.CPS16)*@ScalingF CPS16, 
	       	sum(ItemHistory.CPS17)*@ScalingF CPS17, sum(ItemHistory.CPS18)*@ScalingF CPS18,sum( ItemHistory.CPS19)*@ScalingF CPS19, 
	       	sum(ItemHistory.CPS20)*@ScalingF CPS20, sum(ItemHistory.CPS21)*@ScalingF CPS21,sum( ItemHistory.CPS22)*@ScalingF CPS22, 
	       	sum(ItemHistory.CPS23)*@ScalingF CPS23, sum(ItemHistory.CPS24)*@ScalingF CPS24,sum(ItemHistory.CPS25)*@ScalingF CPS25, 
	       	sum(ItemHistory.CPS26)*@ScalingF CPS26, sum(ItemHistory.CPS27)*@ScalingF CPS27,sum(ItemHistory.CPS28)*@ScalingF CPS28, 
	        sum(ItemHistory.CPS29)*@ScalingF CPS29, sum(ItemHistory.CPS30)*@ScalingF CPS30,sum(ItemHistory.CPS31)*@ScalingF CPS31, 
	       	sum(ItemHistory.CPS32)*@ScalingF CPS32, sum(ItemHistory.CPS33)*@ScalingF CPS33,sum(ItemHistory.CPS34)*@ScalingF CPS34, 
	       	sum(ItemHistory.CPS35)*@ScalingF CPS35, sum(ItemHistory.CPS36)*@ScalingF CPS36,sum(ItemHistory.CPS37)*@ScalingF CPS37, 
	       	sum(ItemHistory.CPS38)*@ScalingF CPS38, sum(ItemHistory.CPS39)*@ScalingF CPS39,sum(ItemHistory.CPS40)*@ScalingF CPS40, 
	      	sum(ItemHistory.CPS41)*@ScalingF CPS41, sum(ItemHistory.CPS42)*@ScalingF CPS42,sum(ItemHistory.CPS43)*@ScalingF CPS43, 
	        sum(ItemHistory.CPS44)*@ScalingF CPS44, sum(ItemHistory.CPS45)*@ScalingF CPS45,sum(ItemHistory.CPS46)*@ScalingF CPS46, 
	      	sum(ItemHistory.CPS47)*@ScalingF CPS47, sum(ItemHistory.CPS48)*@ScalingF CPS48,sum(ItemHistory.CPS49)*@ScalingF CPS49, 
	        sum(ItemHistory.CPS50)*@ScalingF CPS50, sum(ItemHistory.CPS51)*@ScalingF CPS51,sum( ItemHistory.CPS52)*@ScalingF CPS52
	       	INTO #TempItemHistory
	       	From ItemHistory 
		WHERE LcId =@LocationKey 
	    	AND Item = @OldItemId
		Group by ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear
	      	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc

	update ItemHistory 
	set ItemHistory.CPS01 =#TempItemHistory.cps01,ItemHistory.CPS02 =#TempItemHistory.cps02,
	    ItemHistory.CPS03 =#TempItemHistory.cps03,ItemHistory.CPS04 =#TempItemHistory.cps04,
	    ItemHistory.CPS05 =#TempItemHistory.cps05,ItemHistory.CPS06 =#TempItemHistory.cps06,
	    ItemHistory.CPS07 =#TempItemHistory.cps07,ItemHistory.CPS08 =#TempItemHistory.cps08,
	    ItemHistory.CPS09 =#TempItemHistory.cps09,ItemHistory.CPS10 =#TempItemHistory.cps10,
	    ItemHistory.CPS11 =#TempItemHistory.cps11,ItemHistory.CPS12 =#TempItemHistory.cps12,
	    ItemHistory.CPS13 =#TempItemHistory.cps13,ItemHistory.CPS14 =#TempItemHistory.cps14,
	    ItemHistory.CPS15 =#TempItemHistory.cps15,ItemHistory.CPS16 =#TempItemHistory.cps16,
            ItemHistory.CPS17 =#TempItemHistory.cps17,ItemHistory.CPS18 =#TempItemHistory.cps18,
	    ItemHistory.CPS19 =#TempItemHistory.cps19,ItemHistory.CPS20 =#TempItemHistory.cps20,
	    ItemHistory.CPS21 =#TempItemHistory.cps21,ItemHistory.CPS22 =#TempItemHistory.cps22,
	    ItemHistory.CPS23 =#TempItemHistory.cps23,ItemHistory.CPS24 =#TempItemHistory.cps24,
            ItemHistory.CPS25 =#TempItemHistory.cps25,ItemHistory.CPS26 =#TempItemHistory.cps26,
	    ItemHistory.CPS27 =#TempItemHistory.cps27,ItemHistory.CPS28 =#TempItemHistory.cps28,
	    ItemHistory.CPS29 =#TempItemHistory.cps29,ItemHistory.CPS30 =#TempItemHistory.cps30,
	    ItemHistory.CPS31 =#TempItemHistory.cps31,ItemHistory.CPS32 =#TempItemHistory.cps32,
            ItemHistory.CPS33 =#TempItemHistory.cps33,ItemHistory.CPS34 =#TempItemHistory.cps34,
	    ItemHistory.CPS35 =#TempItemHistory.cps35,ItemHistory.CPS36 =#TempItemHistory.cps36,
            ItemHistory.CPS37 =#TempItemHistory.cps37,ItemHistory.CPS38 =#TempItemHistory.cps38,
	    ItemHistory.CPS39 =#TempItemHistory.cps39,ItemHistory.CPS40 =#TempItemHistory.cps40,
	    ItemHistory.CPS41 =#TempItemHistory.cps41,ItemHistory.CPS42 =#TempItemHistory.cps42,
	    ItemHistory.CPS43 =#TempItemHistory.cps43,ItemHistory.CPS44 =#TempItemHistory.cps44,
            ItemHistory.CPS45 =#TempItemHistory.cps45,ItemHistory.CPS46 =#TempItemHistory.cps46,
            ItemHistory.CPS47 =#TempItemHistory.cps47,ItemHistory.CPS48 =#TempItemHistory.cps48,
	    ItemHistory.CPS49 =#TempItemHistory.cps49,ItemHistory.CPS50 =#TempItemHistory.cps50,
	    ItemHistory.CPS51 =#TempItemHistory.cps51,ItemHistory.CPS52 =#TempItemHistory.cps52
	    FROM ItemHistory 
            INNER JOIN #tempItemHistory 
	    ON ItemHistory.Lcid = #tempItemHistory.Lcid
	    AND ItemHistory.item =@NewItemId
	    AND ItemHistory.HisYear =#tempItemHistory.HisYear

		END
 	END
END
--IF @TransBudFcst = 1 
--BEGIN

--  	DELETE ForecastRepositoryDetail 
--	WHERE LcID = @LocationKey AND Item = @NewItemId 
--
--   	INSERT INTO ForecastRepositoryDetail
--          (RepositoryKey,Lcid,Item,FcstPds,PdSumYear,PdSumQtr,PdSumMonth,
--   	PDSumWeek,PdSumDay,QtyFcst,QtyFcstNetReq,QtyAdjust,
--   	QtyActualOrdered,QtyActualShipped) 
--    	SELECT RepositoryKey ,Lcid,@NewItemId,FcstPds,PdSumYear*@ScalingF,
--    	PdSumQtr*@ScalingF,PdSumMonth*@ScalingF,PDSumWeek*@ScalingF,PdSumDay*@ScalingF,
--    	QtyFcst*@ScalingF,QtyFcstNetReq*@ScalingF,QtyAdjust*@ScalingF,
--    	QtyActualOrdered*@ScalingF,QtyActualShipped*@ScalingF
--    	FROM ForecastRepositoryDetail WHERE
--    	LcID = @LocationKey AND 
--   	Item = @OldItemId 
 
--	IF @@ERROR <> 0 
--   	BEGIN
-- 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
--  	END
--End
    
IF @DeleteOpt = 1
BEGIN
   	IF @CountBothExists =2 
   	BEGIN
       		DELETE Item 
		WHERE LcID = @LocationKey AND Item = @OldItemId
     
       		DELETE ItemHistory 
		WHERE LcID = @LocationKey AND Item = @OldItemId
   	END
   	DELETE ForecastRepositoryDetail WHERE
   	LcID = @LocationKey AND Item = @OldItemId 
END
 
-- Get the next record
         FETCH NEXT FROM LocationCursor INTO @LocationKey
END
 
CLOSE LocationCursor
DEALLOCATE LocationCursor
     
RETURN 0 -- Successful 

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

