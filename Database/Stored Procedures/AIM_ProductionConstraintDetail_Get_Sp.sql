SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraintDetail_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraintDetail_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ProductionConstraintDetail_Get_Sp
**	Desc: Gets records from AIMProductionConstraintDetail
**		Derived from AIM_ForecastMainUserAccess_Get_Sp
**	Parameters:
**		@ConstraintID		alphanumeric
**	Returns: 
**		@found --> Can be Zero
**		ErrorCodes:
**			 0 --> Successful (includes a recordset - AIMProductionConstraintDetail.*)
**			-1 --> No Data Found
**			-2 --> SQL Error  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2002/12/09
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	  Description:
**	---------- -------------- ---------------------------------------------
*******************************************************************************/

CREATE  PROCEDURE AIM_ProductionConstraintDetail_Get_Sp 
(
       @ConstraintID nvarchar(30)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	DECLARE @found int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @ConstraintID IS NULL
	BEGIN
		RETURN -1
	END	
	
	SELECT PCD.ConstraintID, PCD.Item, PCD.MinUnits, PCD.MaxUnits
	FROM AIMProductionConstraintDetail PCD
	INNER JOIN AIMProductionConstraint PC ON
		PCD.ConstraintID = PC.ConstraintID
	WHERE PCD.ConstraintID =  @ConstraintID
	ORDER BY Item
	
	SELECT @found = @@rowcount
	
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE
	BEGIN
	    	RETURN @found
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

