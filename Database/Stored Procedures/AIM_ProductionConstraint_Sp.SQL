SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ProductionConstraint_Sp
**	Desc: Runs Production Constraint Logic and updates ProdConstraintTemp table 
** 	Or Podetail table
**
**	Returns: 1)  1 - Successful 
**		 2) -1 - No Data Found
**             
**	Auth:   Srinivas Uddanti 
**	Date:   04/03/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**	2005-04-05 SUddanti	Changed code to fix bug
******************************************************************************/

CREATE  PROCEDURE AIM_ProductionConstraint_Sp
( 
  @ConstID                  nvarchar(30),
  @VnId                     nvarchar(12) ='',
  @Assort                   nvarchar(12)='',
  @UniqueJobId	            nvarchar(255) =''
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rowcount 						int,
    	@Initial_Prod_Cycle_days				decimal(10,3),
    	@Daily_Qty						decimal(10,3),
        @Prod_Cycle_Days        				decimal(10,3), 
        @Temp_Ordqty 						decimal(10,3),					
	@Total_RSOQ						Decimal(10,3) ,
	@Total_Item_Split_Pct					decimal (10,3),
	@Prod_Cycle_Loop					decimal(10,3),
	@Prod_Const_Count					decimal(10,3),
	@Daily_Pct_Reduction					decimal(10,3),
	@Lcid 							nvarchar(12),
	@Item							nvarchar(25),
	@RSOQ							decimal(10,3),
	@LMin							decimal(10,3),
	@LMax							decimal(10,3),
	@Order_Qty						decimal(10,3),
	@XDock_YN						nvarchar(1),
	@OP_Split_Pct						decimal(10,3),
	@Item_Split_Pct						decimal(10,3),
	@Order_Point						decimal(10,3),
	@Min_Fullfill_Only					bit,
	@Over_Or_Below_Min					decimal(10,3),
	@Repeat_Count						int,
	@Multi_Location						bit,
	@Order_Fullfilled					bit,
	@POSeqId						int,
	@ConvFactor						int,
	@Ratio_Type						int,
	@Cycle_Days						decimal(10,3),
	@ProdConstraintGrtZero					nvarchar(1)

SET NOCOUNT ON 

-- Validate the required parameters.
IF @VnId ='' and  @Assort ='' and @UniqueJobId =''
BEGIN
	RETURN -1
END

IF @VnId ='' and @Assort <>''
BEGIN
	RETURN -1
END

IF @VnId <>'' and @Assort =''
BEGIN
	RETURN -1
END

SELECT @Temp_OrdQty =0
SELECT @Total_RSOQ =0
SELECT @Prod_Cycle_Days =0

DELETE FROM #Prod_const_rcd 
DELETE FROM #XDoc_rcd
DELETE FROM #WithOut_XDoc_rcd
DELETE FROM #Temp_rcd
BEGIN
	SELECT @ProdConstraintGrtZero =ProdconstraintGrtZero
	FROM SysCtrl
END
IF @VnId  <>'' and @Assort <> ''
	IF UPPER(@ProdConstraintGrtZero) = 'Y'
	BEGIN
		INSERT INTO #Prod_const_rcd 
		SELECT PO.LCID,PO.Item,PO.RSOQ,COALESCE(PCD.MinUnits,0) "LMin",
 		COALESCE(case PCD.MaxUnits when 0 then 999999999 else pcd.maxunits end,1) "LMax",
		0 "OrderQty",COALESCE(LC.DROPSHIP_XDOCK,'N')"XDock_YN" ,0 "OP_Split_Pct",
		0 "Item_Split_Pct",COALESCE(Item.OrderPt,0) "Order_Point",0 "Min_Fullfull_Only",0 "Over_Or_Below_Min",
		1 "Repeat_Count",0 "Multi_Location",0 "Order_Fullfilled",
		PO.POSeqID ,COALESCE(ITEM.ConvFactor,1) "ConvFactor",COALESCE(PC.RatioType,1) "Ratio_Type",
		COALESCE(CASE PC.CycleDays when 0 then 1 else pc.cycledays end,1) "Cycle_Days"
 		FROM PODETAIL PO INNER JOIN ITEM 
 		ON PO.ITEM =ITEM.ITEM AND
 		PO.ASSORT =ITEM.ASSORT AND
 		PO.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
 		ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
 		ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
 		ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
 		WHERE  ITEM.ProductionConstraint='Y' 
		AND PC.EnableConstraint ='Y'
 		AND PO.RSOQ >0
		AND item.vnid =@VnId
		AND item.assort =@Assort
		AND PC.ConstraintId =@ConstId
 		ORDER BY PC.ConstraintID, PO.Item, PO.Lcid
	END
	ELSE
	BEGIN
		INSERT INTO #Prod_const_rcd 
		SELECT PO.LCID,PO.Item,PO.RSOQ,COALESCE(PCD.MinUnits,0) "LMin",
 		COALESCE(CASE PCD.MaxUnits when 0 then 999999999 else pcd.maxunits end,1) "LMax",
		0 "OrderQty",COALESCE(LC.DROPSHIP_XDOCK,'N')"XDock_YN" ,0 "OP_Split_Pct",
		0 "Item_Split_Pct",COALESCE(Item.OrderPt,0) "Order_Point",0 "Min_Fullfull_Only",0 "Over_Or_Below_Min",
		1 "Repeat_Count",0 "Multi_Location",0 "Order_Fullfilled",
		PO.POSeqID ,COALESCE(ITEM.ConvFactor,1) "ConvFactor",COALESCE(PC.RatioType,1) "Ratio_Type",
		COALESCE(CASE PC.CycleDays when 0 then 1 else pc.cycledays end,1) "Cycle_Days"
 		FROM PODETAIL PO INNER JOIN ITEM 
 		ON PO.ITEM =ITEM.ITEM AND
 		PO.ASSORT =ITEM.ASSORT AND
 		PO.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
 		ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
 		ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
 		ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
 		WHERE  ITEM.ProductionConstraint='Y' 
		AND PC.EnableConstraint ='Y'
		AND item.vnid =@VnId
		AND item.assort =@Assort
		AND PC.ConstraintId =@ConstId
 		ORDER BY PC.ConstraintID, PO.Item, PO.Lcid
	END
	IF @UniqueJobId <>'' 
		IF UPPER(@ProdConstraintGrtZero) = 'Y'
		BEGIN	INSERT INTO #Prod_const_rcd 
			SELECT PQ.LCID,PQ.Item,PQ.Qty "RSOQ",COALESCE(PCD.MinUnits,0) "LMin",
	 		COALESCE(case PCD.MaxUnits when 0 then 999999999 else pcd.maxunits end,1) "LMax",
			0 "OrderQty",COALESCE(LC.DROPSHIP_XDOCK,'N')"XDock_YN" ,0 "OP_Split_Pct",
			0 "Item_Split_Pct",COALESCE(Item.OrderPt,0) "Order_Point",0 "Min_Fullfull_Only",0 "Over_Or_Below_Min",
			1 "Repeat_Count",0 "Multi_Location",0 "Order_Fullfilled",0 "POSeqID"
			,COALESCE(ITEM.ConvFactor,1) "ConvFactor",COALESCE(PC.RatioType,1) "Ratio_Type",
			COALESCE(case PC.CycleDays when 0 then 1 else pc.cycledays end,1) "Cycle_Days"
	 		FROM ProdConstraintTemp PQ INNER JOIN ITEM 
	 		ON PQ.ITEM =ITEM.ITEM AND
 		PQ.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
 		ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
 		ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
 		ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
 		WHERE  ITEM.ProductionConstraint='Y' 
		AND PC.EnableConstraint ='Y'
 		AND PQ.Qty >0
		AND PC.ConstraintId =@ConstId
		AND PQ.UniqueJobId=@UniqueJobId
 		ORDER BY PC.ConstraintID, PQ.Item, PQ.Lcid
	END
	ELSE
	BEGIN
		INSERT INTO #Prod_const_rcd 
		SELECT PQ.LCID,PQ.Item,PQ.Qty "RSOQ",COALESCE(PCD.MinUnits,0) "LMin",
		COALESCE(case PCD.MaxUnits when 0 then 999999999 else pcd.maxunits end,1) "LMax",
		0 "OrderQty",COALESCE(LC.DROPSHIP_XDOCK,'N')"XDock_YN" ,0 "OP_Split_Pct",
		0 "Item_Split_Pct",COALESCE(Item.OrderPt,0) "Order_Point",0 "Min_Fullfull_Only",0 "Over_Or_Below_Min",
		1 "Repeat_Count",0 "Multi_Location",0 "Order_Fullfilled",0 "POSeqID"
		,COALESCE(ITEM.ConvFactor,1) "ConvFactor",COALESCE(PC.RatioType,1) "Ratio_Type",
		COALESCE(case PC.CycleDays when 0 then 1 else pc.cycledays end,1) "Cycle_Days"
		FROM ProdConstraintTemp PQ INNER JOIN ITEM 
		ON PQ.ITEM =ITEM.ITEM AND
		PQ.LCID =ITEM.LCID  INNER JOIN AIMLOCATIONS LC 
		ON ITEM.LCID =LC.LCID INNER JOIN AIMPRODUCTIONCONSTRAINTDETAIL PCD
		ON ITEM.ITEM =PCD.ITEM INNER JOIN AIMPRODUCTIONCONSTRAINT PC
		ON PCD.CONSTRAINTID =PC.CONSTRAINTID 
		WHERE  ITEM.ProductionConstraint='Y' 
		AND PC.EnableConstraint ='Y'
		AND PC.ConstraintId =@ConstId
		AND PQ.UniqueJobId=@UniqueJobId
		ORDER BY PC.ConstraintID, PQ.Item, PQ.Lcid
	END

	SELECT @Prod_Const_count =COUNT(*) 
	FROM #Prod_const_rcd
	
	UPDATE #Prod_const_rcd
	SET Min_FullFill_Only =CASE WHEN RSOQ >lmin THEN 0 ELSE 1 END,
	LMax =CASE WHEN Lmax >lmin THEN LMax ELSE LMin END,
	Order_Point =CASE WHEN Ratio_Type =1 THEN Order_Point 
			 WHEN Ratio_Type =2 THEN RSOQ ELSE RSOQ
			 END
	
	UPDATE #Prod_const_rcd
	SET LMax =CASE WHEN Lmax >lmin THEN LMax ELSE LMin END
	
	UPDATE #Prod_const_rcd
	SET Order_Point =CASE WHEN Ratio_Type =1 THEN Order_Point 
			 WHEN Ratio_Type =2 THEN RSOQ ELSE RSOQ
			 END
	
	INSERT INTO #Xdoc_rcd
	SELECT lcid,item,rsoq 
	FROM #Prod_const_rcd 
	WHERE xdock_yn ='Y'
	
	INSERT INTO #WithOut_Xdoc_rcd
	SELECT lcid,item,Order_Point,RSOQ 
	FROM #Prod_const_rcd
	WHERE xdock_yn ='N'
	
	INSERT INTO #Temp_rcd
	SELECT item,SUM(Order_Point),SUM(RSOQ),COUNT(*) 
	FROM #WithOut_XDoc_rcd
	GROUP BY item
	HAVING COUNT(*)  >1
	
	UPDATE #Prod_const_rcd
	SET #Prod_const_rcd.Repeat_Count =#Temp_rcd.Item_Count,
	#Prod_const_rcd.OP_Split_Pct =#Prod_const_rcd.Order_Point/#Temp_rcd.Sum_Order_Point,
	#Prod_const_rcd.Multi_Location =1,
	#Prod_const_rcd.Over_Or_Below_Min =#Temp_rcd.Sum_RSOQ - #Prod_const_rcd.Lmin,
	#Prod_const_rcd.Min_Fullfill_Only =CASE WHEN (#Temp_rcd.Sum_RSOQ - #Prod_const_rcd.Lmin) >0 THEN
        0 ELSE #Prod_const_rcd.Min_Fullfill_Only END 
	FROM #Prod_const_rcd,#Temp_rcd
	WHERE #Prod_const_rcd.Item =#Temp_rcd.Item
	
	UPDATE #Prod_const_rcd
	SET #Prod_const_rcd.lmin =#Prod_const_rcd.Lmin -#Xdoc_rcd.RSOQ,
	#Prod_const_rcd.Over_Or_Below_Min =#Prod_const_rcd.Over_Or_Below_Min + #Xdoc_rcd.RSOQ
	FROM #Prod_const_rcd,#Xdoc_rcd
	WHERE #Prod_const_rcd.Item =#Xdoc_rcd.Item
	
	UPDATE #Prod_const_rcd
	SET #Prod_const_rcd.Min_Fullfill_Only =CASE WHEN #Prod_const_rcd.OVer_Or_Below_Min >0 THEN
	0 ELSE #Prod_const_rcd.Min_Fullfill_Only END
	FROM #Prod_const_rcd,#Xdoc_rcd
	WHERE #Prod_const_rcd.Item =#Xdoc_rcd.Item
	
	SELECT TOP 1 @Prod_Cycle_Days = Cycle_Days
	FROM #Prod_const_rcd
	
	SELECT @Initial_Prod_Cycle_Days =@Prod_Cycle_Days
	
	DECLARE Prod_Const_cur CURSOR
	For select lcid,item,RSOQ,LMin,LMax,Order_Qty,XDock_YN,OP_Split_Pct,Item_Split_Pct,
	Order_Point,Min_Fullfill_Only,Over_Or_Below_Min,Repeat_Count,Multi_Location,
	Order_Fullfilled,ConvFactor,Ratio_Type,Cycle_Days from #Prod_const_rcd
	
	OPEN Prod_const_cur
	WHILE 1 =1
	BEGIN
		FETCH NEXT FROM Prod_Const_cur INTO
		@Lcid ,@Item,@RSOQ,@LMin,@LMax,@Order_Qty,@XDock_YN,@OP_Split_Pct,
		@Item_Split_Pct,@Order_Point,@Min_Fullfill_Only,@Over_Or_Below_Min,
		@Repeat_Count,@Multi_Location,@Order_Fullfilled,@ConvFactor,
		@Ratio_Type,@Cycle_Days
		IF @@FETCH_STATUS <>0 
			BREAK

		SELECT @Daily_Qty =@Lmax/@Initial_Prod_Cycle_Days
	IF @Multi_Location =1 
	BEGIN --multi location
		IF @XDock_YN ='Y' --xdoc
		BEGIN
			UPDATE #Prod_const_rcd
			SET Order_Qty =@RSOQ, Order_FullFilled =1 
			WHERE CURRENT OF Prod_Const_cur
	
			SELECT @Order_Qty =@RSOQ
			SELECT @Prod_Cycle_Days=@Prod_Cycle_Days -@Order_Qty/@Daily_Qty
		END
		ELSE
		BEGIN --1
			If @Order_Qty <@LMin*@OP_Split_Pct 
			BEGIN --2
				SELECT @Temp_Ordqty =0
				SELECT @Temp_Ordqty =@LMin*@OP_Split_Pct
			-- 	EXEC @Temp_Ordqty =AIM_ProdPackRounding_SP @Temp_OrdQty,@ConvFactor,'U'
				SELECT @Order_Qty =@Temp_Ordqty

				UPDATE #Prod_const_rcd
				SET Order_Qty =@Temp_Ordqty
				WHERE CURRENT OF Prod_Const_cur

				IF @Order_Qty >=@RSOQ or @Min_Fullfill_Only =1 
				BEGIN --3
					UPDATE #Prod_const_rcd
					SET Order_Fullfilled =1 
					WHERE CURRENT OF Prod_Const_cur
				
					SELECT @Order_Fullfilled =1
				END --3
				ELSE
				BEGIN --4
					SELECT @Total_RSOQ =@Total_RSOQ + @RSOQ			
				END --4
				SELECT @Prod_Cycle_days =@Prod_Cycle_Days -@Order_qty/@Daily_Qty
			END --2
			ELSE
			BEGIN--5
				SELECT @Total_RSOQ =@Total_RSOQ +@RSOQ
			END ---5
		END --1
	
	END-- Multi location
	ELSE
	BEGIN
		IF @XDock_YN ='Y' 
		BEGIN
			UPDATE #Prod_const_rcd
			SET Order_Qty =@RSOQ, Order_FullFilled =1 
			WHERE CURRENT OF Prod_Const_cur
			SELECT @Order_Qty =@RSOQ
			SELECT @Prod_Cycle_Days=@Prod_Cycle_Days -@Order_Qty/@Daily_Qty
		END
		ELSE
		BEGIN
			IF @Order_Qty  <@LMin
			BEGIN
				SELECT @Temp_OrdQty =0
				SELECT @Temp_OrdQty =@lMin
				EXEC  @Temp_OrdQty = AIM_ProdPackRounding_SP @Temp_OrdQty,@ConvFactor,'U'
				SELECT @Order_Qty =@Temp_Ordqty
		
				UPDATE #Prod_const_rcd
				SET Order_Qty =@Temp_Ordqty WHERE CURRENT OF Prod_Const_cur
					
				IF @Order_Qty >=@RSOQ 
				BEGIN
					UPDATE #Prod_const_rcd
					SET Order_Fullfilled =1 WHERE CURRENT OF Prod_Const_cur
		
					SELECT @Order_Fullfilled =1
				END
				ELSE
				BEGIN
					SELECT @Total_RSOQ =@Total_RSOQ + @RSOQ
				END
				SELECT @Prod_Cycle_days =@Prod_Cycle_Days -@Order_qty/@Daily_Qty
			END
			ELSE
			BEGIN
				SELECT @Total_RSOQ =@Total_RSOQ +@RSOQ
			END
		END
	END
END

CLOSE Prod_Const_cur
DEALLOCATE Prod_Const_cur

DECLARE Prod_Const_cur1 CURSOR
FOR SELECT Order_Fullfilled,Item_Split_Pct,RSOQ,lcid,item,
RSOQ,Min_Fullfill_Only,Multi_Location,XDock_YN,Order_Qty
FROM #Prod_const_rcd
OPEN Prod_const_cur1
WHILE 1 =1
BEGIN
FETCH NEXT FROM Prod_Const_cur1 INTO
@Order_Fullfilled,@Item_Split_Pct,@RSOQ,@lcid,@item,
@RSOQ,@Min_Fullfill_Only,@Multi_Location,@XDock_YN,@Order_Qty
IF @@FETCH_STATUS <>0 
BREAK
IF @Order_Fullfilled =0  --1
BEGIN--1
	SELECT @Item_Split_Pct =@RSOQ/@Total_RSOQ
	IF @Total_Item_Split_Pct +@Item_Split_Pct >1.0 --2
	BEGIN--2
		SELECT @Item_Split_Pct =1.0 -@Total_Item_Split_Pct
	END--2
	UPDATE #Prod_const_rcd
	SET Item_Split_Pct =@Item_Split_Pct
	WHERE CURRENT OF  Prod_Const_cur1
	SELECT @Total_Item_Split_Pct =@Total_Item_Split_Pct +@Item_Split_Pct
END--1
IF @Min_Fullfill_Only =1 AND @Multi_Location=1 AND NOT(@XDock_YN) ='Y' --3
BEGIN--3
	UPDATE #Prod_const_rcd
	SET Order_Qty =0 
	WHERE CURRENT OF Prod_Const_cur1
END--3

--fetch status end
END 

CLOSE Prod_Const_cur1
DEALLOCATE Prod_Const_cur1

--check const 2
DECLARE Prod_Const_cur2 CURSOR
FOR SELECT LMax,Min_Fullfill_Only,Multi_Location,XDock_YN,Order_Qty,RSOQ,
Over_Or_Below_Min,OP_Split_Pct,ConvFactor
FROM #Prod_const_rcd
OPEN Prod_const_cur2
WHILE 1 =1
BEGIN --1
	FETCH NEXT FROM Prod_Const_cur2 INTO
	@LMax,@Min_Fullfill_Only,@Multi_Location,@XDock_YN,@Order_Qty,@RSOQ,
	@Over_Or_Below_Min,@OP_Split_Pct,@ConvFactor
	IF @@FETCH_STATUS <>0 
	BREAK
	SELECT @Daily_Qty =@LMax/@Initial_Prod_Cycle_Days
	IF @Min_Fullfill_Only =1 AND @Multi_Location =1 AND NOT @XDock_YN = 'Y' --2
	Begin--2
	SELECT 'order_qty'
	SELECT  @Order_Qty
	SELECT 'RSOQ'
	SELECT  @RSOQ
		IF @Order_Qty < @RSOQ--3
			BEGIN--3
				SELECT @Order_Qty =@RSOQ +ABS(@Over_Or_Below_Min)*@OP_Split_Pct
				SELECT @Temp_OrdQty =0
				SELECT @Temp_OrdQty =@Order_Qty
				EXEC @Temp_OrdQty =AIM_ProdPackRounding_SP @Temp_OrdQty,@ConvFactor,'U'
				--Select @Temp_OrdQty= Packeround
				SELECT @Daily_Qty =@LMax/@Initial_Prod_Cycle_Days
				SELECT @Prod_Cycle_Days = @Prod_Cycle_Days -(@Temp_OrdQty -@Order_Qty)/@Daily_Qty
				
				UPDATE #Prod_const_rcd
				SET Order_Qty =@Temp_OrdQty
				WHERE CURRENT OF Prod_Const_cur2
			END--3
	END--2
END --1
CLOSE Prod_Const_cur2
DEALLOCATE Prod_Const_cur2

SELECT @Prod_Cycle_Loop =1
WHILE @Prod_Cycle_Loop <= @Initial_Prod_Cycle_Days+1

BEGIN -- while loop 1

IF @Prod_Cycle_Days <=0
BREAK

DECLARE Prod_Const_cur3 cursor
FOR SELECT Order_FullFilled,Item_Split_Pct,RSOQ
FROM #Prod_const_rcd
OPEN Prod_const_cur3
WHILE 1 =1
BEGIN --while loop2
FETCH NEXT FROM Prod_Const_cur3 into
@Order_Fullfilled,@Item_Split_Pct,@RSOQ
IF @@FETCH_STATUS <>0 
BREAK
SELECT @Total_Item_Split_Pct =0.0
IF @Order_Fullfilled =0 
	BEGIN--3
	SELECT @Item_Split_Pct =@RSOQ/@Total_RSOQ
	IF @Total_Item_Split_Pct +@Item_Split_Pct >1.0 
		BEGIN--4
		SELECT @Item_Split_Pct = 1.0 -@Total_Item_Split_Pct
		END --4
	SELECT @Total_Item_Split_Pct =@Total_Item_Split_Pct + @Item_Split_Pct
	UPDATE #Prod_const_rcd
	SET Item_Split_Pct =@Item_Split_Pct
	WHERE CURRENT OF Prod_Const_cur3
	END--3
END-- while loop 2

IF @Prod_Cycle_Days <1.0 
	SELECT @Daily_Pct_Reduction =@Prod_Cycle_Days
ELSE
	SELECT @Daily_Pct_Reduction =1.0
CLOSE Prod_Const_cur3
DEALLOCATE Prod_Const_cur3

DECLARE Prod_Const_cur4 CURSOR
FOR SELECT Order_FullFilled,LMax,Order_Qty,RSOQ,Item_Split_Pct
FROM #Prod_const_rcd
OPEN Prod_const_cur4
WHILE 1 =1
BEGIN --while loop3
FETCH NEXT FROM Prod_Const_cur4 INTO
@Order_Fullfilled,@LMax,@Order_Qty,@RSOQ,@Item_Split_Pct
IF @@FETCH_STATUS <>0 
BREAK

IF @Order_Fullfilled= 0 
BEGIN--1
SELECT @Daily_Qty =@LMax/@Initial_Prod_Cycle_Days
	IF @Order_Qty < @RSOQ 
	BEGIN--2
		IF @Order_Qty +@Daily_Qty *@Daily_Pct_Reduction*@Item_Split_Pct >@RSOQ
		BEGIN--3
		SELECT @Prod_Cycle_Days =@Prod_Cycle_Days -(@RSOQ -@Order_Qty)/@Daily_Qty
		SELECT @Order_Qty=@RSOQ
		SELECT @Total_RSOQ =@Total_RSOQ -@RSOQ
		SELECT @Order_FullFilled =1
		UPDATE #Prod_const_rcd
		SET Order_Qty =@Order_Qty,Order_Fullfilled =@Order_Fullfilled
		WHERE CURRENT OF Prod_Const_cur4
			IF @Prod_Cycle_Days <=0
			BEGIN
				BREAK
			END
		END--3
		ELSE
		BEGIN--4
		SELECT @Prod_Cycle_Days =@Prod_Cycle_Days - @Daily_Pct_Reduction * @Item_Split_Pct
		SELECT @Order_Qty =@Order_Qty +@Daily_Qty * @Daily_Pct_Reduction * @Item_Split_Pct
		UPDATE #Prod_const_rcd
		SET Order_Qty =@Order_Qty
		WHERE CURRENT OF Prod_Const_cur4
			IF @Prod_Cycle_Days <=0 
			BEGIN
				BREAK
			END
		END--4
	END--2
END--1
END --while loop3
CLOSE Prod_Const_cur4
DEALLOCATE Prod_Const_cur4

IF @Prod_Cycle_Days <=0
BEGIN
	BREAK
END

SELECT @Prod_Cycle_Loop =@Prod_Cycle_Loop +1
END --while Loop 1
DECLARE Prod_Const_cur5 CURSOR
FOR SELECT Order_Qty,ConvFactor FROM #Prod_const_rcd
WHERE Min_FullFill_Only =0 AND NOT XDock_Yn ='Y'
OPEN Prod_const_cur5
WHILE 1 =1
BEGIN --1
FETCH NEXT FROM Prod_Const_cur5 INTO
@Order_Qty,@ConvFactor

If @@FETCH_STATUS <>0 
BREAK
	EXEC @Order_Qty =AIM_ProdPackRounding_SP @Order_Qty,@ConvFactor,'D'

	UPDATE #Prod_const_rcd
	SET Order_Qty =@Order_Qty
	WHERE current of  Prod_Const_cur5
END--end fetch

CLOSE Prod_Const_cur5
DEALLOCATE Prod_Const_cur5

IF @VnId  <>'' AND @Assort <> ''
BEGIN
	UPDATE podetail
	SET podetail .vsoq =#Prod_Const_rcd.Order_qty,
	Podetail.Original_vsoq =#Prod_Const_rcd.Order_qty
	FROM podetail,#Prod_Const_rcd
	WHERE podetail.PoSeqId =#Prod_Const_rcd.PoSeqId
END
IF @UniqueJobId <>'' 
BEGIN
	UPDATE ProdConstraintTemp
	SET ProdConstraintTemp.AdjQty =#Prod_Const_rcd.Order_qty
	FROM ProdConstraintTemp,#Prod_Const_rcd
	WHERE ProdConstraintTemp.Lcid =#Prod_Const_rcd.Lcid
	AND ProdConstraintTemp.Item =#Prod_Const_rcd.Item
	AND ProdConstraintTemp.UniqueJobId=@UniqueJobId
END
RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

