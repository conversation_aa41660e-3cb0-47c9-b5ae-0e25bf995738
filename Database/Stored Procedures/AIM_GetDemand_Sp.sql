SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetDemand_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetDemand_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetDemand_Sp 
**	Desc: Gets the Item History data for an Item.
**
**	Returns: 1) @rowcount
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:      Author:      Description:
**  ---------- ------------ ---------------------------------------------------
**  09/12/2003 Srinivas U   Changed code for SubsItem
*******************************************************************************/
     
CREATE PROCEDURE AIM_GetDemand_Sp
(
    	@SelLcId            				nvarchar(12),
      	@FcstYear           				int,
      	@FcstPeriod         				smallint,
      	@DemandSource       				nvarchar(1)
)
    			
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
 
-- Declare working variables
DECLARE
       	@StartPeriod        				smallint,
       	@StartYear	        			int

SET NOCOUNT ON

-- Calculate the Start Year and Start Period
SELECT @StartYear = @FcstYear - 3, @StartPeriod = @FcstPeriod + 1
IF @StartPeriod > 52
        SELECT @StartPeriod = @StartPeriod - 52, @StartYear = @StartYear + 1

--Create temp table #TempItemHistory
select ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	 ItemHistory.CPS01, 
      	ItemHistory.CPS02, ItemHistory.CPS03, ItemHistory.CPS04, 
       	ItemHistory.CPS05, ItemHistory.CPS06, ItemHistory.CPS07, 
      	ItemHistory.CPS08, ItemHistory.CPS09, ItemHistory.CPS10, 
       	ItemHistory.CPS11, ItemHistory.CPS12, ItemHistory.CPS13, 
       	ItemHistory.CPS14, ItemHistory.CPS15, ItemHistory.CPS16, 
       	ItemHistory.CPS17, ItemHistory.CPS18, ItemHistory.CPS19, 
       	ItemHistory.CPS20, ItemHistory.CPS21, ItemHistory.CPS22, 
       	ItemHistory.CPS23, ItemHistory.CPS24, ItemHistory.CPS25, 
       	ItemHistory.CPS26, ItemHistory.CPS27, ItemHistory.CPS28, 
       	ItemHistory.CPS29, ItemHistory.CPS30, ItemHistory.CPS31, 
       	ItemHistory.CPS32, ItemHistory.CPS33, ItemHistory.CPS34, 
       	ItemHistory.CPS35, ItemHistory.CPS36, ItemHistory.CPS37, 
       	ItemHistory.CPS38, ItemHistory.CPS39, ItemHistory.CPS40, 
      	ItemHistory.CPS41, ItemHistory.CPS42, ItemHistory.CPS43, 
       	ItemHistory.CPS44, ItemHistory.CPS45, ItemHistory.CPS46, 
      	ItemHistory.CPS47, ItemHistory.CPS48, ItemHistory.CPS49, 
       	ItemHistory.CPS50, ItemHistory.CPS51, ItemHistory.CPS52
	into #TempItemHistory 
	from itemhistory
	where item ='XXXXX'
-- Issue Appropriate SQL Statement
IF @DemandSource = 'S' and @SelLcId = 'ALL'
BEGIN
        INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	From item INNER JOIN ItemHistory ON 
	Item.Lcid = ItemHistory.Lcid
            AND Item.Item = ItemHistory.Item
            AND Item.ItStat in ('A', 'H')
            AND ItemHistory.HisYear BETWEEN @StartYear and @FcstYear
	Group by ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear
      	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc

        SELECT Item.Lcid, Item.Item, Item.ItStat, Item.ActDate, 
            Item.FcstCycles, Item.ZeroCount, 
            Item.FcstDemand, Item.MAE, Item.Dser, Item.Accum_LT,
            Item.ReviewTime, Item.OrderQty, Item.Cost,
            #tempitemhistory.HisYear, #tempItemHistory.CPS01, 
		#tempItemHistory.CPS02, #tempItemHistory.CPS03, #tempItemHistory.CPS04, 
		#tempItemHistory.CPS05, #tempItemHistory.CPS06, #tempItemHistory.CPS07, 
		#tempItemHistory.CPS08, #tempItemHistory.CPS09, #tempItemHistory.CPS10, 
		#tempItemHistory.CPS11, #tempItemHistory.CPS12, #tempItemHistory.CPS13, 
		#tempItemHistory.CPS14, #tempItemHistory.CPS15, #tempItemHistory.CPS16, 
		#tempItemHistory.CPS17, #tempItemHistory.CPS18, #tempItemHistory.CPS19, 
		#tempItemHistory.CPS20, #tempItemHistory.CPS21, #tempItemHistory.CPS22, 
		#tempItemHistory.CPS23, #tempItemHistory.CPS24, #tempItemHistory.CPS25, 
		#tempItemHistory.CPS26, #tempItemHistory.CPS27, #tempItemHistory.CPS28, 
		#tempItemHistory.CPS29, #tempItemHistory.CPS30, #tempItemHistory.CPS31, 
		#tempItemHistory.CPS32, #tempItemHistory.CPS33, #tempItemHistory.CPS34, 
		#tempItemHistory.CPS35, #tempItemHistory.CPS36, #tempItemHistory.CPS37, 
		#tempItemHistory.CPS38, #tempItemHistory.CPS39, #tempItemHistory.CPS40, 
		#tempItemHistory.CPS41, #tempItemHistory.CPS42, #tempItemHistory.CPS43, 
		#tempItemHistory.CPS44, #tempItemHistory.CPS45, #tempItemHistory.CPS46, 
		#tempItemHistory.CPS47, #tempItemHistory.CPS48, #tempItemHistory.CPS49, 
		#tempItemHistory.CPS50, #tempItemHistory.CPS51, #tempItemHistory.CPS52
            FROM Item 
            INNER JOIN #tempItemHistory ON Item.Lcid = #tempItemHistory.Lcid
                AND Item.Item = #tempItemHistory.Item
            AND Item.ItStat in ('A', 'H')
            AND #tempItemHistory.HisYear BETWEEN @StartYear and @FcstYear
            ORDER BY Item.Lcid, Item.Item, #tempItemHistory.HisYear desc
END
IF @DemandSource = 'O' and @SelLcId = 'ALL'
BEGIN
        SELECT Item.Lcid, Item.Item, Item.ItStat, Item.ActDate, 
            Item.FcstCycles, Item.ZeroCount, 
            Item.FcstDemand, Item.MAE, Item.Dser, Item.Accum_LT,
            Item.ReviewTime, Item.OrderQty, Item.Cost,
            ItemHistory.HisYear, 
            CPS01 = ItemHistory.QtyOrd01, CPS02 = ItemHistory.QtyOrd02, CPS03 = ItemHistory.QtyOrd03, 
            CPS04 = ItemHistory.QtyOrd04, CPS05 = ItemHistory.QtyOrd05, CPS06 = ItemHistory.QtyOrd06, 
            CPS07 = ItemHistory.QtyOrd07, CPS08 = ItemHistory.QtyOrd08, CPS09 = ItemHistory.QtyOrd09, 
            CPS10 = ItemHistory.QtyOrd10, CPS11 = ItemHistory.QtyOrd11, CPS12 = ItemHistory.QtyOrd12, 
            CPS13 = ItemHistory.QtyOrd13, CPS14 = ItemHistory.QtyOrd14, CPS15 = ItemHistory.QtyOrd15, 
            CPS16 = ItemHistory.QtyOrd16, CPS17 = ItemHistory.QtyOrd17, CPS18 = ItemHistory.QtyOrd18, 
            CPS19 = ItemHistory.QtyOrd19, CPS20 = ItemHistory.QtyOrd20, CPS21 = ItemHistory.QtyOrd21, 
            CPS22 = ItemHistory.QtyOrd22, CPS23 = ItemHistory.QtyOrd23, CPS24 = ItemHistory.QtyOrd24, 
            CPS25 = ItemHistory.QtyOrd25, CPS26 = ItemHistory.QtyOrd26, CPS27 = ItemHistory.QtyOrd27, 
            CPS28 = ItemHistory.QtyOrd28, CPS29 = ItemHistory.QtyOrd29, CPS30 = ItemHistory.QtyOrd30, 
            CPS31 = ItemHistory.QtyOrd31, CPS32 = ItemHistory.QtyOrd32, CPS33 = ItemHistory.QtyOrd33, 
            CPS34 = ItemHistory.QtyOrd34, CPS35 = ItemHistory.QtyOrd35, CPS36 = ItemHistory.QtyOrd36, 
            CPS37 = ItemHistory.QtyOrd37, CPS38 = ItemHistory.QtyOrd38, CPS39 = ItemHistory.QtyOrd39, 
            CPS40 = ItemHistory.QtyOrd40, CPS41 = ItemHistory.QtyOrd41, CPS42 = ItemHistory.QtyOrd42, 
            CPS43 = ItemHistory.QtyOrd43, CPS44 = ItemHistory.QtyOrd44, CPS45 = ItemHistory.QtyOrd45, 
            CPS46 = ItemHistory.QtyOrd46, CPS47 = ItemHistory.QtyOrd47, CPS48 = ItemHistory.QtyOrd48, 
            CPS49 = ItemHistory.QtyOrd49, CPS50 = ItemHistory.QtyOrd50, CPS51 = ItemHistory.QtyOrd51, 
            CPS52 = ItemHistory.QtyOrd52
            FROM Item 
            INNER JOIN ItemHistory ON Item.Lcid = ItemHistory.Lcid
            AND Item.Item = ItemHistory.Item 
	    AND Item.Item =ItemHistory.SubsItem
            AND Item.ItStat in ('A', 'H')
            AND ItemHistory.HisYear BETWEEN @StartYear and @FcstYear
            ORDER BY Item.Lcid, Item.Item, ItemHistory.HisYear desc
END
IF @DemandSource = 'S' and @SelLcId <> 'ALL'
BEGIN
        INSERT INTO #TempItemHistory
	SELECT ItemHistory.Lcid,ItemHistory.item,ItemHistory.HisYear,
	sum(ItemHistory.CPS01), 
      	sum(ItemHistory.CPS02),sum(ItemHistory.CPS03),sum( ItemHistory.CPS04),
	sum(ItemHistory.CPS05), sum(ItemHistory.CPS06), sum(ItemHistory.CPS07), 
      	sum(ItemHistory.CPS08),sum(ItemHistory.CPS09), sum(ItemHistory.CPS10),
	sum(ItemHistory.CPS11), sum(ItemHistory.CPS12),sum( ItemHistory.CPS13), 
       	sum(ItemHistory.CPS14), sum(ItemHistory.CPS15),sum( ItemHistory.CPS16), 
       	sum(ItemHistory.CPS17), sum(ItemHistory.CPS18),sum( ItemHistory.CPS19), 
       	sum(ItemHistory.CPS20), sum(ItemHistory.CPS21),sum( ItemHistory.CPS22), 
       	sum(ItemHistory.CPS23), sum(ItemHistory.CPS24), sum(ItemHistory.CPS25), 
       	sum(ItemHistory.CPS26),sum( ItemHistory.CPS27), sum(ItemHistory.CPS28), 
        sum(ItemHistory.CPS29),sum( ItemHistory.CPS30), sum(ItemHistory.CPS31), 
       	sum(ItemHistory.CPS32), sum(ItemHistory.CPS33), sum(ItemHistory.CPS34), 
       	sum(ItemHistory.CPS35), sum(ItemHistory.CPS36), sum(ItemHistory.CPS37), 
       	sum(ItemHistory.CPS38), sum(ItemHistory.CPS39), sum(ItemHistory.CPS40), 
      	sum(ItemHistory.CPS41), sum(ItemHistory.CPS42), sum(ItemHistory.CPS43), 
        sum(ItemHistory.CPS44), sum(ItemHistory.CPS45), sum(ItemHistory.CPS46), 
      	sum(ItemHistory.CPS47), sum(ItemHistory.CPS48), sum(ItemHistory.CPS49), 
        sum(ItemHistory.CPS50), sum(ItemHistory.CPS51),sum( ItemHistory.CPS52) 
       	From item INNER JOIN ItemHistory ON 
	Item.Lcid = ItemHistory.Lcid AND 
	Item.Item = ItemHistory.Item 
            WHERE Item.Lcid = @SelLcid
            AND Item.ItStat in ('A', 'H')
        Group by ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear
      	ORDER BY ItemHistory.Lcid, ItemHistory.Item, ItemHistory.HisYear desc
      


        SELECT Item.Lcid, Item.Item, Item.ItStat, Item.ActDate, 
            Item.FcstCycles, Item.ZeroCount, 
            Item.FcstDemand, Item.MAE, Item.Dser, Item.Accum_LT,
            Item.ReviewTime, Item.OrderQty, Item.Cost,
             #tempitemhistory.HisYear, #tempItemHistory.CPS01, 
		#tempItemHistory.CPS02, #tempItemHistory.CPS03, #tempItemHistory.CPS04, 
		#tempItemHistory.CPS05, #tempItemHistory.CPS06, #tempItemHistory.CPS07, 
		#tempItemHistory.CPS08, #tempItemHistory.CPS09, #tempItemHistory.CPS10, 
		#tempItemHistory.CPS11, #tempItemHistory.CPS12, #tempItemHistory.CPS13, 
		#tempItemHistory.CPS14, #tempItemHistory.CPS15, #tempItemHistory.CPS16, 
		#tempItemHistory.CPS17, #tempItemHistory.CPS18, #tempItemHistory.CPS19, 
		#tempItemHistory.CPS20, #tempItemHistory.CPS21, #tempItemHistory.CPS22, 
		#tempItemHistory.CPS23, #tempItemHistory.CPS24, #tempItemHistory.CPS25, 
		#tempItemHistory.CPS26, #tempItemHistory.CPS27, #tempItemHistory.CPS28, 
		#tempItemHistory.CPS29, #tempItemHistory.CPS30, #tempItemHistory.CPS31, 
		#tempItemHistory.CPS32, #tempItemHistory.CPS33, #tempItemHistory.CPS34, 
		#tempItemHistory.CPS35, #tempItemHistory.CPS36, #tempItemHistory.CPS37, 
		#tempItemHistory.CPS38, #tempItemHistory.CPS39, #tempItemHistory.CPS40, 
		#tempItemHistory.CPS41, #tempItemHistory.CPS42, #tempItemHistory.CPS43, 
		#tempItemHistory.CPS44, #tempItemHistory.CPS45, #tempItemHistory.CPS46, 
		#tempItemHistory.CPS47, #tempItemHistory.CPS48, #tempItemHistory.CPS49, 
		#tempItemHistory.CPS50, #tempItemHistory.CPS51, #tempItemHistory.CPS52
            FROM Item 
            INNER JOIN #tempItemHistory ON Item.Lcid = #tempItemHistory.Lcid
            AND Item.Item = #tempItemHistory.Item 
            WHERE Item.Lcid = @SelLcid
            AND Item.ItStat in ('A', 'H')
            AND #tempItemHistory.HisYear BETWEEN @StartYear and @FcstYear
            ORDER BY Item.Lcid, Item.Item, #tempItemHistory.HisYear desc
END
IF @DemandSource = 'O' and @SelLcId <> 'ALL'
BEGIN
        SELECT Item.Lcid, Item.Item, Item.ItStat, Item.ActDate, 
            Item.FcstCycles, Item.ZeroCount, 
            Item.FcstDemand, Item.MAE, Item.Dser, Item.Accum_LT,
            Item.ReviewTime, Item.OrderQty, Item.Cost,
            ItemHistory.HisYear, 
            CPS01 = ItemHistory.QtyOrd01, CPS02 = ItemHistory.QtyOrd02, CPS03 = ItemHistory.QtyOrd03, 
            CPS04 = ItemHistory.QtyOrd04, CPS05 = ItemHistory.QtyOrd05, CPS06 = ItemHistory.QtyOrd06, 
            CPS07 = ItemHistory.QtyOrd07, CPS08 = ItemHistory.QtyOrd08, CPS09 = ItemHistory.QtyOrd09, 
            CPS10 = ItemHistory.QtyOrd10, CPS11 = ItemHistory.QtyOrd11, CPS12 = ItemHistory.QtyOrd12, 
            CPS13 = ItemHistory.QtyOrd13, CPS14 = ItemHistory.QtyOrd14, CPS15 = ItemHistory.QtyOrd15, 
            CPS16 = ItemHistory.QtyOrd16, CPS17 = ItemHistory.QtyOrd17, CPS18 = ItemHistory.QtyOrd18, 
            CPS19 = ItemHistory.QtyOrd19, CPS20 = ItemHistory.QtyOrd20, CPS21 = ItemHistory.QtyOrd21, 
            CPS22 = ItemHistory.QtyOrd22, CPS23 = ItemHistory.QtyOrd23, CPS24 = ItemHistory.QtyOrd24, 
            CPS25 = ItemHistory.QtyOrd25, CPS26 = ItemHistory.QtyOrd26, CPS27 = ItemHistory.QtyOrd27, 
            CPS28 = ItemHistory.QtyOrd28, CPS29 = ItemHistory.QtyOrd29, CPS30 = ItemHistory.QtyOrd30, 
            CPS31 = ItemHistory.QtyOrd31, CPS32 = ItemHistory.QtyOrd32, CPS33 = ItemHistory.QtyOrd33, 
            CPS34 = ItemHistory.QtyOrd34, CPS35 = ItemHistory.QtyOrd35, CPS36 = ItemHistory.QtyOrd36, 
            CPS37 = ItemHistory.QtyOrd37, CPS38 = ItemHistory.QtyOrd38, CPS39 = ItemHistory.QtyOrd39, 
            CPS40 = ItemHistory.QtyOrd40, CPS41 = ItemHistory.QtyOrd41, CPS42 = ItemHistory.QtyOrd42, 
            CPS43 = ItemHistory.QtyOrd43, CPS44 = ItemHistory.QtyOrd44, CPS45 = ItemHistory.QtyOrd45, 
            CPS46 = ItemHistory.QtyOrd46, CPS47 = ItemHistory.QtyOrd47, CPS48 = ItemHistory.QtyOrd48, 
            CPS49 = ItemHistory.QtyOrd49, CPS50 = ItemHistory.QtyOrd50, CPS51 = ItemHistory.QtyOrd51, 
            CPS52 = ItemHistory.QtyOrd52
            FROM Item 
            INNER JOIN ItemHistory ON Item.Lcid = ItemHistory.Lcid
            AND Item.Item = ItemHistory.Item 
	    AND Item.Item =ItemHistory.SubsItem
            WHERE Item.Lcid = @SelLcid
            AND Item.ItStat in ('A', 'H')
            AND ItemHistory.HisYear BETWEEN @StartYear and @FcstYear
            ORDER BY Item.Lcid, Item.Item, ItemHistory.HisYear desc
END
-- Return the number of rows processed
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

