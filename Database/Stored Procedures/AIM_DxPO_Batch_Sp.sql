SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxPO_Batch_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxPO_Batch_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON>ES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxPO_Batch_Sp
**	Desc: Gets Purchase Order data from the AIMPO table.
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:	Description:
**    ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
   
CREATE PROCEDURE AIM_DxPO_Batch_Sp
(
  	@LcId nvarchar(12)
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	SET NOCOUNT ON
	
	-- Determine the scope of the select
	IF @LcId = 'All'
	BEGIN
	    SELECT PODetail.OrdStatus, PODetail.POSeqID, PODetail.POType, 
	        PODetail.VnId, PODetail.Assort, PODetail.Lcid, PODetail.Item, 
			PODetail.ItDesc, PODetail.Cost,
			PODetail.VSOQ, PODetail.UOM, PODetail.IsDate, PODetail.DuDate, 
			PODetail.Zone, PODetail.ById, PODetail.Original_VSOQ
        FROM AIMPO
		INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
			AND AIMPO.VnId = PODetail.VnId
			and AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
		AND PODetail.OrdStatus = 'R'
        ORDER BY PODetail.LcId, PODetail.VnId, 
            PODetail.Zone, PODetail.Item
	END
	ELSE
	BEGIN
	    SELECT PODetail.OrdStatus, PODetail.POSeqID, PODetail.POType, 
	        PODetail.VnId, PODetail.Assort, PODetail.Lcid, PODetail.Item, 
			PODetail.ItDesc, PODetail.Cost,
			PODetail.VSOQ, PODetail.UOM, PODetail.IsDate, PODetail.DuDate, 
			PODetail.Zone, PODetail.ById, PODetail.Original_VSOQ
        FROM AIMPO
		INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
			AND AIMPO.VnId = PODetail.VnId
			and AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
        AND PODetail.OrdStatus = 'R'
		AND PODetail.LcId = @LcId
        ORDER BY PODetail.LcId, PODetail.VnId, 
            PODetail.Zone, PODetail.Item
	END
	
	RETURN @@ROWCOUNT
END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

