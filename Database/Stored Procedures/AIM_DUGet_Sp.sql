SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DUGet_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DUGet_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DUGet_Sp
**	Desc: Updates the Cost, Price, and List Price for an Item
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Cost, Price, List Price
**              
**	Auth:   Srinivas Uddanti
**	Date:   08/19/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**	---------- ------------ -----------------------------------------------
**	08/20/2002 Wade Riza    Updated Return Codes and Error Handling
**	10/01/2002 Srinivas U.  Updated with LastDate
**	10/29/2002 Wade Riza    Updated with conparisions on Day, Month & Year
*******************************************************************************/
     
CREATE PROCEDURE AIM_DUGet_Sp
(
      @SelLcId            					nvarchar(12),
      @SelItem            					nvarchar(25),
      @FcstYear           					int,
      @FcstPeriod        					smallint,
      @ForceUpdateFlag    					nvarchar(1),
      @DemandSource       					nvarchar(1),
      @SAVersion          					smallint
)
  	
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

-- Declare working variables
DECLARE
        -- Stored Procedure Variables
        @CurFcstUpdCyc      					int,
        @StartPeriod        					smallint,
        @StartYear	        				int

SET NOCOUNT ON

-- Calculate the Current Forecast Update Cycle
SELECT @CurFcstUpdCyc = (@FcstYear * 100) + @FcstPeriod

-- Calculate the Start Year and Start Period
SELECT @StartYear = @FcstYear - 3, @StartPeriod = @FcstPeriod + 1

IF @StartPeriod > 52
BEGIN
        SELECT @StartPeriod = @StartPeriod - 52, @StartYear = @StartYear + 1
END  
     
-- Issue Appropriate SQL Statement
IF  @DemandSource = 'S'
BEGIN
	EXEC AIM_DUGet_Shipped_Sp  @SelLcId,@SelItem,@FcstYear,@FcstPeriod,@ForceUpdateFlag,@SAVersion,@CurFcstUpdCyc,@StartYear
END
IF  @DemandSource = 'O'
BEGIN
	EXEC AIM_DUGet_Ordered_Sp  @SelLcId,@SelItem,@FcstYear,@FcstPeriod,@ForceUpdateFlag,@SAVersion,@CurFcstUpdCyc,@StartYear
END
IF  @DemandSource = 'B'
BEGIN
	EXEC AIM_DUGet_ShippedWithSubs_Sp  @SelLcId,@SelItem,@FcstYear,@FcstPeriod,@ForceUpdateFlag,@SAVersion,@CurFcstUpdCyc,@StartYear
END
-- Return the number of rows processed
RETURN @@rowcount

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

