SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SASummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SASummary_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_SASummary_Sp
**	Desc: Creates Seasonlity Profiles at the Company, Location and Class Level
**
**	Returns: 1) 0 - Failure
**
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE  PROCEDURE AIM_SASummary_Sp
(
    	@SAVersion 						smallint 
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON
 
-- Validate the required parameters.
IF @SAVersion IS NULL
BEGIN
	RETURN 0
END

-- Clear the Seasonality Table
DELETE AIMSeasons 
WHERE SaVersion = @SaVersion 
AND SaLevel = 'S'
  
-- Make sure that there are Detailed Records in Seasonality Table 
SELECT COUNT(*)  
FROM AIMSeasons
WHERE SaVersion = @SaVersion 
AND SaLevel = 'D'
IF @@ROWCOUNT = 0
BEGIN
	RETURN 0
END

-- CREATE seasonality profile at Company Level
INSERT INTO AIMSeasons
(said, saversion, sadesc, 
 salevel, lcid, Class,
 bi01, bi02, bi03, bi04, bi05, bi06, bi07, bi08, bi09, bi10, bi11, bi12,
 bi13, bi14, bi15, bi16, bi17, bi18, bi19, bi20, bi21, bi22, bi23, bi24,
 bi25, bi26, bi27, bi28, bi29, bi30, bi31, bi32, bi33, bi34, bi35, bi36,
 bi37, bi38, bi39, bi40, bi41, bi42, bi43, bi44, bi45, bi46, bi47, bi48,   
 bi49, bi50, bi51, bi52,
 itmcnt, avgunits) 
  SELECT 
  rtrim('Company'),
  @SAVersion,
  'Company Average',
  'S',
  '',
  '',
  bi01 = sum(bi01 * avgunits) / sum(avgunits), bi02 = sum(bi02 * avgunits) / sum(avgunits), bi03 = sum(bi03 * avgunits) / sum(avgunits), 
  bi04 = sum(bi04 * avgunits) / sum(avgunits), bi05 = sum(bi05 * avgunits) / sum(avgunits), bi06 = sum(bi06 * avgunits) / sum(avgunits), 
  bi07 = sum(bi07 * avgunits) / sum(avgunits), bi08 = sum(bi08 * avgunits) / sum(avgunits), bi09 = sum(bi09 * avgunits) / sum(avgunits), 
  bi10 = sum(bi10 * avgunits) / sum(avgunits), bi11 = sum(bi11 * avgunits) / sum(avgunits), bi12 = sum(bi12 * avgunits) / sum(avgunits), 
  bi13 = sum(bi13 * avgunits) / sum(avgunits), bi14 = sum(bi14 * avgunits) / sum(avgunits), bi15 = sum(bi15 * avgunits) / sum(avgunits), 
  bi16 = sum(bi16 * avgunits) / sum(avgunits), bi17 = sum(bi17 * avgunits) / sum(avgunits), bi18 = sum(bi18 * avgunits) / sum(avgunits), 
  bi19 = sum(bi19 * avgunits) / sum(avgunits), bi20 = sum(bi20 * avgunits) / sum(avgunits), bi21 = sum(bi21 * avgunits) / sum(avgunits), 
  bi22 = sum(bi22 * avgunits) / sum(avgunits), bi23 = sum(bi23 * avgunits) / sum(avgunits), bi24 = sum(bi24 * avgunits) / sum(avgunits), 
  bi25 = sum(bi25 * avgunits) / sum(avgunits), bi26 = sum(bi26 * avgunits) / sum(avgunits), bi27 = sum(bi27 * avgunits) / sum(avgunits), 
  bi28 = sum(bi28 * avgunits) / sum(avgunits), bi29 = sum(bi29 * avgunits) / sum(avgunits), bi30 = sum(bi30 * avgunits) / sum(avgunits), 
  bi31 = sum(bi31 * avgunits) / sum(avgunits), bi32 = sum(bi32 * avgunits) / sum(avgunits), bi33 = sum(bi33 * avgunits) / sum(avgunits), 
  bi34 = sum(bi34 * avgunits) / sum(avgunits), bi35 = sum(bi35 * avgunits) / sum(avgunits), bi36 = sum(bi36 * avgunits) / sum(avgunits), 
  bi37 = sum(bi37 * avgunits) / sum(avgunits), bi38 = sum(bi38 * avgunits) / sum(avgunits), bi39 = sum(bi39 * avgunits) / sum(avgunits), 
  bi40 = sum(bi40 * avgunits) / sum(avgunits), bi41 = sum(bi41 * avgunits) / sum(avgunits), bi42 = sum(bi42 * avgunits) / sum(avgunits), 
  bi43 = sum(bi43 * avgunits) / sum(avgunits), bi44 = sum(bi44 * avgunits) / sum(avgunits), bi45 = sum(bi45 * avgunits) / sum(avgunits), 
  bi46 = sum(bi46 * avgunits) / sum(avgunits), bi47 = sum(bi47 * avgunits) / sum(avgunits), bi48 = sum(bi48 * avgunits) / sum(avgunits), 
  bi49 = sum(bi49 * avgunits) / sum(avgunits), bi50 = sum(bi50 * avgunits) / sum(avgunits), bi51 = sum(bi51 * avgunits) / sum(avgunits), 
  bi52 = sum(bi52 * avgunits) / sum(avgunits), 
  itmcnt = sum(itmcnt), avgunits = sum(avgunits)
  FROM AIMSeasons
  WHERE SAVersion = @SaVersion
  AND  SALevel = 'D'
  AND AvgUnits > 0
            
  -- CREATE seasonality profile at Location Level
  INSERT INTO AIMSeasons
  (said, saversion, sadesc,  
   salevel, lcid, Class,
   bi01, bi02, bi03, bi04, bi05, bi06, bi07, bi08, bi09, bi10, bi11, bi12,
   bi13, bi14, bi15, bi16, bi17, bi18, bi19, bi20, bi21, bi22, bi23, bi24,
   bi25, bi26, bi27, bi28, bi29, bi30, bi31, bi32, bi33, bi34, bi35, bi36,
   bi37, bi38, bi39, bi40, bi41, bi42, bi43, bi44, bi45, bi46, bi47, bi48,   
   bi49, bi50, bi51, bi52,
   itmcnt, avgunits) 
  SELECT 
  AIMSeasons.LcId,
  @SaVersion,
  min(AIMLocations.LName),
  'S',
  AIMSeasons.LcId,
  '',
  bi01 = sum(bi01 * avgunits) / sum(avgunits), bi02 = sum(bi02 * avgunits) / sum(avgunits), bi03 = sum(bi03 * avgunits) / sum(avgunits), 
  bi04 = sum(bi04 * avgunits) / sum(avgunits), bi05 = sum(bi05 * avgunits) / sum(avgunits), bi06 = sum(bi06 * avgunits) / sum(avgunits), 
  bi07 = sum(bi07 * avgunits) / sum(avgunits), bi08 = sum(bi08 * avgunits) / sum(avgunits), bi09 = sum(bi09 * avgunits) / sum(avgunits), 
  bi10 = sum(bi10 * avgunits) / sum(avgunits), bi11 = sum(bi11 * avgunits) / sum(avgunits), bi12 = sum(bi12 * avgunits) / sum(avgunits), 
  bi13 = sum(bi13 * avgunits) / sum(avgunits), bi14 = sum(bi14 * avgunits) / sum(avgunits), bi15 = sum(bi15 * avgunits) / sum(avgunits), 
  bi16 = sum(bi16 * avgunits) / sum(avgunits), bi17 = sum(bi17 * avgunits) / sum(avgunits), bi18 = sum(bi18 * avgunits) / sum(avgunits), 
  bi19 = sum(bi19 * avgunits) / sum(avgunits), bi20 = sum(bi20 * avgunits) / sum(avgunits), bi21 = sum(bi21 * avgunits) / sum(avgunits), 
  bi22 = sum(bi22 * avgunits) / sum(avgunits), bi23 = sum(bi23 * avgunits) / sum(avgunits), bi24 = sum(bi24 * avgunits) / sum(avgunits), 
  bi25 = sum(bi25 * avgunits) / sum(avgunits), bi26 = sum(bi26 * avgunits) / sum(avgunits), bi27 = sum(bi27 * avgunits) / sum(avgunits), 
  bi28 = sum(bi28 * avgunits) / sum(avgunits), bi29 = sum(bi29 * avgunits) / sum(avgunits), bi30 = sum(bi30 * avgunits) / sum(avgunits), 
  bi31 = sum(bi31 * avgunits) / sum(avgunits), bi32 = sum(bi32 * avgunits) / sum(avgunits), bi33 = sum(bi33 * avgunits) / sum(avgunits), 
  bi34 = sum(bi34 * avgunits) / sum(avgunits), bi35 = sum(bi35 * avgunits) / sum(avgunits), bi36 = sum(bi36 * avgunits) / sum(avgunits), 
  bi37 = sum(bi37 * avgunits) / sum(avgunits), bi38 = sum(bi38 * avgunits) / sum(avgunits), bi39 = sum(bi39 * avgunits) / sum(avgunits), 
  bi40 = sum(bi40 * avgunits) / sum(avgunits), bi41 = sum(bi41 * avgunits) / sum(avgunits), bi42 = sum(bi42 * avgunits) / sum(avgunits), 
  bi43 = sum(bi43 * avgunits) / sum(avgunits), bi44 = sum(bi44 * avgunits) / sum(avgunits), bi45 = sum(bi45 * avgunits) / sum(avgunits), 
  bi46 = sum(bi46 * avgunits) / sum(avgunits), bi47 = sum(bi47 * avgunits) / sum(avgunits), bi48 = sum(bi48 * avgunits) / sum(avgunits), 
  bi49 = sum(bi49 * avgunits) / sum(avgunits), bi50 = sum(bi50 * avgunits) / sum(avgunits), bi51 = sum(bi51 * avgunits) / sum(avgunits), 
  bi52 = sum(bi52 * avgunits) / sum(avgunits), 
  itmcnt = sum(itmcnt), avgunits = sum(avgunits)
  FROM AIMSeasons
  INNER JOIN AIMLocations ON AIMSeasons.LcId = AIMLocations.Lcid
  WHERE SAVersion = @SaVersion
  AND SaLevel = 'D'
  AND Class <> ''
  AND AvgUnits > 0
  GROUP BY AIMSeasons.LcId
  
  -- CREATE seasonality profile at Class Level
  INSERT INTO AIMSeasons
  (said, saversion, sadesc,  
   salevel, lcid, Class,
   bi01, bi02, bi03, bi04, bi05, bi06, bi07, bi08, bi09, bi10, bi11, bi12,
   bi13, bi14, bi15, bi16, bi17, bi18, bi19, bi20, bi21, bi22, bi23, bi24,
   bi25, bi26, bi27, bi28, bi29, bi30, bi31, bi32, bi33, bi34, bi35, bi36,
   bi37, bi38, bi39, bi40, bi41, bi42, bi43, bi44, bi45, bi46, bi47, bi48,   
   bi49, bi50, bi51, bi52,
   itmcnt, avgunits) 
  SELECT 
  AIMSeasons.Class,
  @SAVersion,  coalesce(min(AIMClasses.ClassDesc), 'No Description Found'),
  'S',
  '',
  AIMSeasons.Class,
  bi01 = sum(bi01 * avgunits) / sum(avgunits), bi02 = sum(bi02 * avgunits) / sum(avgunits), bi03 = sum(bi03 * avgunits) / sum(avgunits), 
  bi04 = sum(bi04 * avgunits) / sum(avgunits), bi05 = sum(bi05 * avgunits) / sum(avgunits), bi06 = sum(bi06 * avgunits) / sum(avgunits), 
  bi07 = sum(bi07 * avgunits) / sum(avgunits), bi08 = sum(bi08 * avgunits) / sum(avgunits), bi09 = sum(bi09 * avgunits) / sum(avgunits), 
  bi10 = sum(bi10 * avgunits) / sum(avgunits), bi11 = sum(bi11 * avgunits) / sum(avgunits), bi12 = sum(bi12 * avgunits) / sum(avgunits), 
  bi13 = sum(bi13 * avgunits) / sum(avgunits), bi14 = sum(bi14 * avgunits) / sum(avgunits), bi15 = sum(bi15 * avgunits) / sum(avgunits), 
  bi16 = sum(bi16 * avgunits) / sum(avgunits), bi17 = sum(bi17 * avgunits) / sum(avgunits), bi18 = sum(bi18 * avgunits) / sum(avgunits), 
  bi19 = sum(bi19 * avgunits) / sum(avgunits), bi20 = sum(bi20 * avgunits) / sum(avgunits), bi21 = sum(bi21 * avgunits) / sum(avgunits), 
  bi22 = sum(bi22 * avgunits) / sum(avgunits), bi23 = sum(bi23 * avgunits) / sum(avgunits), bi24 = sum(bi24 * avgunits) / sum(avgunits), 
  bi25 = sum(bi25 * avgunits) / sum(avgunits), bi26 = sum(bi26 * avgunits) / sum(avgunits), bi27 = sum(bi27 * avgunits) / sum(avgunits), 
  bi28 = sum(bi28 * avgunits) / sum(avgunits), bi29 = sum(bi29 * avgunits) / sum(avgunits), bi30 = sum(bi30 * avgunits) / sum(avgunits), 
  bi31 = sum(bi31 * avgunits) / sum(avgunits), bi32 = sum(bi32 * avgunits) / sum(avgunits), bi33 = sum(bi33 * avgunits) / sum(avgunits), 
  bi34 = sum(bi34 * avgunits) / sum(avgunits), bi35 = sum(bi35 * avgunits) / sum(avgunits), bi36 = sum(bi36 * avgunits) / sum(avgunits), 
  bi37 = sum(bi37 * avgunits) / sum(avgunits), bi38 = sum(bi38 * avgunits) / sum(avgunits), bi39 = sum(bi39 * avgunits) / sum(avgunits), 
  bi40 = sum(bi40 * avgunits) / sum(avgunits), bi41 = sum(bi41 * avgunits) / sum(avgunits), bi42 = sum(bi42 * avgunits) / sum(avgunits), 
  bi43 = sum(bi43 * avgunits) / sum(avgunits), bi44 = sum(bi44 * avgunits) / sum(avgunits), bi45 = sum(bi45 * avgunits) / sum(avgunits), 
  bi46 = sum(bi46 * avgunits) / sum(avgunits), bi47 = sum(bi47 * avgunits) / sum(avgunits), bi48 = sum(bi48 * avgunits) / sum(avgunits), 
  bi49 = sum(bi49 * avgunits) / sum(avgunits), bi50 = sum(bi50 * avgunits) / sum(avgunits), bi51 = sum(bi51 * avgunits) / sum(avgunits), 
  bi52 = sum(bi52 * avgunits) / sum(avgunits), 
  itmcnt = sum(itmcnt), avgunits = sum(avgunits)
  FROM AIMSeasons
  LEFT OUTER JOIN AIMClasses ON AIMSeasons.Class = AIMClasses.Class
  WHERE SAVersion = @SaVersion
  AND SaLevel = 'D'
  AND AIMSeasons.Class <> ''
  AND AvgUnits > 0
  GROUP BY AIMSeasons.Class
  
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

