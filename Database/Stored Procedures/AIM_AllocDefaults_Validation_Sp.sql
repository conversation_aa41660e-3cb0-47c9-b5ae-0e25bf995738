if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocDefaults_Validation_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocDefaults_Validation_Sp]
GO
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AllocDefaults_Validation_Sp
**	Desc: Verifies if a duplicate record exist in the table.
**
**	Returns: 1)  1 - Passed Validation Successfully
**               2)  0 - Failure, Duplicate Record exist
**             
**	Values:  
**              
**	Auth:   Wade Riza 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**	OCT-01-2003 Srinivas 	Modified code to take care of nulls
*******************************************************************************/
     
CREATE     PROCEDURE AIM_AllocDefaults_Validation_Sp
(
    @LStatus   						nvarchar(1),
    @LDivision  					nvarchar(20),
	@LRegion						nvarchar(20),
	@LUserDefined					nvarchar(30)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS  --ENCRYPT

DECLARE @AllocDefaultsID				numeric(9,0),
@SQL_STRING  							nvarchar(2500),
@Record_Count							Integer
SET NOCOUNT ON

-- Validate the required parameters.
IF @LStatus IS NULL AND @LDivision IS NULL AND @LREGION IS NULL AND @LUserDefined IS NULL
BEGIN
	RETURN 0
END


Set @SQL_STRING = 'SELECT *  FROM AllocDefaults WHERE '
Begin
IF @LStatus  is null 
Set @SQL_STRING =@SQL_STRING + 'LStatus  is null '
else

Set @SQL_STRING =@SQL_STRING + 'LStatus =''' +  @LStatus  + ''''
end
--print @SQL_STRING
Begin
IF @LDivision  is null 
Set @SQL_STRING =@SQL_STRING + ' and  LDivision  is null '
else

Set @SQL_STRING =@SQL_STRING + ' and LDivision =''' +  @LDivision  + ''''
end
--print @SQL_STRING
Begin
IF @LRegion  is null 
Set @SQL_STRING =@SQL_STRING + ' and LRegion  is null '
else

Set @SQL_STRING =@SQL_STRING + ' and LRegion =''' +  @LRegion  + ''''
end
--print @SQL_STRING
Begin
IF @LUserDefined  is null 
Set @SQL_STRING =@SQL_STRING + ' and LUserDefined  is null '
else

Set @SQL_STRING =@SQL_STRING + ' and LUserDefined =''' +  @LUserDefined  + ''''
end

--Print @SQL_STRING
EXEC (@SQL_STRING)

/*
SELECT @AllocDefaultsID = AllocDefaultsID 
FROM AllocDefaults
WHERE LStatus = @LStatus
AND LDivision = @LDivision
AND LRegion = @LRegion
AND LUserDefined = @LUserDefined
*/
IF @@rowcount = 0      -- Record Not Found

BEGIN
             
        RETURN 1            -- SUCCEED
END
ELSE                    -- Record Found
BEGIN
        RETURN 0            -- FAIL
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

