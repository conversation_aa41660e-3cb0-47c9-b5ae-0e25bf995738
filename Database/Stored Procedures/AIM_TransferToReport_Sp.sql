SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_TransferToReport_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_TransferToReport_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_TransferToReport_Sp
** 	Desc: Gets Transfer data to populate the Report for 
**      	OverStock with Shortages and Shorages with OverStock
**
** 	Returns: 1) @@recordcount - Can be Zero
**               2) 0 - Failure
**             
** 	Values: @RptOpt = 3 Overstocks with Shortages is only valid option        
**
** 	Auth:    Srinivas Uddanti    
** 	Date:    01/14/2003  
**
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:    Author:        Description:
** 	---------- ------------ -------------------------------------------------
** 	01/17/2003 Srinivas Ud 	Added @Drop_Ship_YN argument and Modified Code
*******************************************************************************/
     
CREATE    PROCEDURE AIM_TransferToReport_Sp
(
      @SQL_Overstocks         nvarchar(4000),
      @SQL_Shortages          nvarchar(4000),
      @RptOpt                 tinyint,
      @From_LcIds             nvarchar(2000),
      @Drop_Ship_YN           nvarchar(1)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE @SQL                  nvarchar(4000),
      @lcid                   nvarchar(12),
      @sql_fromlcid           nvarchar(40),
      @sql_tolcid             nvarchar(4000),
      @Sql_tolcidAdd          nvarchar(4000),
      @Cursor_SQL             nvarchar(2500)
  
SET NOCOUNT ON
--Build a From locations cursor from the From_Lcids Argmument
Set @Cursor_SQL ='declare C cursor static for select distinct lcid from aimlocations  where lcid in ' +@From_LcIds
EXEC(@Cursor_SQL)
OPEN C
FETCH NEXT FROM C INTO @lcid

-- Validate the required parameters.
IF @RptOpt <>3
BEGIN
 RETURN 0
END
IF @RptOpt > 1
BEGIN
      -- CREATE the temporary tables

  SELECT RcdId = 1, Item, ItDesc, ItStat, Lcid, Cost, OrderPt, OrderQty, Oh, Oo,  
            ComStk, BkOrder, BkComStk, FcstDemand, ConvFactor
        INTO #overstocks 
        FROM Item  
        WHERE lcid = 'XXXXXXXXXX'
           
  IF @SQL_Shortages IS NULL
  BEGIN
   RETURN 0
  END

  SELECT * 
  INTO #shortages 
  FROM #overstocks 

  SELECT * 
  INTO #TempTransfer
  FROM #overstocks 

END
IF @RptOpt = 3          -- Overstocks with Shortages
BEGIN
 WHILE @@fetch_status =0
 BEGIN
   SET @sql_fromlcid =' ' +' and lcid in ('''+@lcid +''')'
   IF @Drop_Ship_YN ='Y' 
 BEGIN
 SET @Sql_tolcidAdd = ' select lcidtransferto from aimtransferpolicy '
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' inner join aimlocations on '
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' aimtransferpolicy.lcidtransferto =aimlocations.lcid '
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + 'where aimlocations.dropship_xdock =''N'''
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' and aimtransferpolicy.TransferType =''T'''
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' and aimtransferpolicy.EnableTransferPolicy =''1'''
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' and aimtransferpolicy.lcid = '''
 END
 IF @Drop_Ship_YN <>'Y' 
 BEGIN
 --SET @Sql_tolcidAdd =' select lcidtransferto from aimtransferpolicy where ' lcid = ''' 
 SET @Sql_tolcidAdd =' select lcidtransferto from aimtransferpolicy where ' 
 SET @Sql_tolcidAdd =@Sql_tolcidAdd + ' TransferType =''T'' and EnableTransferPolicy =''1'' and lcid ='''
 END

 SET @sql_tolcid =' '+' and  item.lcid in (' 
 Set @sql_tolcid = @sql_tolcid  + @Sql_tolcidAdd
 Set @sql_tolcid = @sql_tolcid   +@lcid +''')'
 --SET @sql_tolcid =' '+' and  item.lcid in (select lcidtransferto from aimtransferpolicy where lcid =''' +@lcid +''')'
   -- Build the Overstock Table
   EXEC ('insert into #overstocks ' + @SQL_Overstocks +@sql_fromlcid)
   -- Build the Shortage Table
   EXEC ('insert into #shortages ' + @SQL_Shortages + @sql_tolcid)
   -- Delete Overstocks which have no shortages
   DELETE #overstocks
   WHERE NOT EXISTS (SELECT ITEM 
    FROM #shortages 
     WHERE #shortages.ITEM = #overstocks.ITEM) 
         -- Merge the overstock table with the shortage table
   INSERT INTO #temptransfer
   SELECT * 
    FROM #overstocks 
   UNION ALL SELECT * 
      FROM #shortages 
      ORDER BY item, rcdid, lcid
   DELETE FROM #shortages
   DELETE FROM #overstocks
   FETCH NEXT FROM C INTO @lcid
END
CLOSE C
DEALLOCATE C
END
-- Wrap Up
SELECT * fROM #temptransfer ORDER BY item ASC,rcdid ASC
DROP TABLE #temptransfer
DROP TABLE #overstocks
DROP TABLE #shortages 

RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

