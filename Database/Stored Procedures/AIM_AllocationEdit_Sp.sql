
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocationEdit_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocationEdit_Sp]
GO
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_AllocationEdit_Sp
**	Desc:	Given  the Item this procedure returns AIMAODetail records
**		
**	Returns:	1)@Found - Can be Zero
**				2) 0 - No Data Found
**				3) -1 - Invalid Parameter
**				4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
**	Values:	Recordset - AIMAODetail
**              
**	Author:	Srinivas Uddnati
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	Oct-06-2003  Srinivas Uddanti 		Modified the  from clause to 
**						account for subs
**	Oct-14-2003	Sujit			Added ordnbr in order by column
**	July-13-2004 SUddanti			Do not show substitute items on the 
						edit screen. Also add OrdNbr as input
						Argument so that items belonging to 
						the Order we are looking will be returned
*******************************************************************************/
CREATE       PROCEDURE AIM_AllocationEdit_Sp
(
	@item		nvarchar(25),
	@OrdNbr 	nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
Declare 
@Found int
-- Validate the required parameters.
IF @Item IS NULL

BEGIN
	RETURN -1	/* Invalid parameter */
END

Select Allocate =1,OrdNbr,Lcid,ltype,lcid,Item,ItDesc,
RequestedQty,AllocatedQty,AdjustedAllocQty,
Difference = AdjustedAllocQty -RequestedQty,
ItemFillPercentage = case when  requestedqty =0 then 100 
			  else 100 * (AdjustedAllocQty )/RequestedQty 
			  end,linenbr
from AIMAODetail
where Item =@item
/*
Or
linenbr in (Select distinct(Linenbr) from AIMAODetail where item =@item)
*/
and OrdNbr =@OrdNbr
order by ordnbr,linenbr,ltype 

SELECT @Found = @@RowCount

-- Check for SQL Server errors. */
IF @@ERROR <> 0 
BEGIN
	RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
END
ELSE IF @Found <= 0 
BEGIN	
	RETURN -1 /* ERROR (No Valid data found in Database) */
END
ELSE
BEGIN
	RETURN @Found /* SUCCESSFUL */
END	

RETURN @@rowcount
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
