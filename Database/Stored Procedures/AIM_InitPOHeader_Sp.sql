SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InitPOHeader_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_InitPOHeader_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_InitPOHeader_Sp
**	Desc: Creates a PO Header record.
**
**	Returns: 
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
  
CREATE PROCEDURE AIM_InitPOHeader_Sp
(	
     	@ById       				        nvarchar(12),
     	@VnId						nvarchar(12),
     	@Assort						nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE	
	-- Vendor Table
    	@Dft_LeadTime           			smallint, 
        @Vn_Min                 			decimal(9,2), 
        @Vn_Best                			decimal(9,2), 
        @Reach_Code             			nvarchar(1), 
        @POByZone               			nvarchar(1),
        @LastWeekSalesFlag      			nvarchar(1),
    	@TransmitPO					tinyint,
    	@ShipIns					nvarchar(20),
    	@VName						nvarchar(30),
    	@VAddress1					nvarchar(30),
    	@VAddress2					nvarchar(30),
    	@VCity						nvarchar(20),
    	@VState						nvarchar(10),
    	@VZip						nvarchar(10),
    
	-- Other Variables
	@UserInitials					nvarchar(3),
	@LineCount					int,
	@PosLineCount					int,
	@TotalCost					decimal(10,2)
	
SET NOCOUNT ON

-- Get the Buyer's Initials
SELECT @UserInitials = 'XXX'
  
SELECT @UserInitials = coalesce(UserInitials, left(@ById, 3))
FROM AIMUsers 
WHERE UserId = @ById

-- Get the Vendor Data
SELECT @Dft_LeadTime = AIMVendors.Dft_LeadTime, 
    	@Vn_Min= AIMVendors.Vn_Min, 
    	@Vn_Best = AIMVendors.Vn_Best, 
    	@Reach_Code = AIMVendors.Reach_Code, 
    	@POByZone = AIMVendors.POByZone, 
    	@VName = AIMVendors.VName, 
    	@VAddress1 = AIMVendors.VAddress1, 
    	@VAddress2 = AIMVendors.VAddress2,
    	@VCity = AIMVendors.VCity, 
    	@VState = AIMVendors.VState, 
    	@VZip = AIMVendors.VZip,
    	@TransmitPO = AIMVendors.TransmitPO, 
    	@ShipIns = AIMVendors.ShipIns
FROM AIMVendors 
WHERE AIMVendors.Vnid = @VnId
AND AIMVendors.Assort = @Assort

-- Calculate the Purchase Order Summary Data
SELECT @LineCount = COUNT(*), 
    		@PosLineCount = sum(case PODetail.Vsoq
WHEN 0 THEN 0
ELSE 1
END),
@TotalCost = sum(PODetail.Vsoq * PODetail.Cost)
FROM PODetail
WHERE ById = @ById
AND VnId = @VnId
AND Assort = @Assort
  
-- Insert the Purchase Order Header
EXEC AIM_POHdrInsert_Sp
        @ById, @VnId, @Assort, 'P', @TransmitPO,
        @ShipIns, @UserInitials, '', '', '', 'N',
        @VName, @VAddress1, @VAddress2, @VCity, @VState,
        @VZip, @LineCount, @PosLineCount, @Vn_Min, @Vn_Best, @Reach_Code,
        'S', @POByZone, @Dft_LeadTime, @TotalCost

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

