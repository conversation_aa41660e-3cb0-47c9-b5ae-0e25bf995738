SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetCompItemsLTRT_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetCompItemsLTRT_Sp]
GO


/******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetCompItemsLTRT_Sp
**	Desc:Gets the Accu_LT and RT for all items dependent on this item
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_GetCompItemsLTRT_Sp
(
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25)

)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

 
  
  SET NOCOUNT ON
 /*
SELECT distinct Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
startdate,enddate
 FROM  ITEM,AIMCOMPANIONITEMDETAIL WHERE ITEM.item =AIMCOMPANIONITEMDETAIL.Item
and item.lcid =AIMCOMPANIONITEMDETAIL.lcid and item.item  IN
(SELECT ITEM FROM AIMCOMPANIONITEMDETAIL WHERE 
 LCID =@LCID AND MASTERITEM =@ITEM )
 AND AIMCOMPANIONITEMDETAIL.MASTERITEM =@ITEM
 AND Item.LCID =@LCID
*/

SELECT distinct Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
startdate,enddate
 FROM  ITEM,AIMCOMPANIONITEMDETAIL,AIMCOMPANIONITEM WHERE ITEM.item =AIMCOMPANIONITEMDETAIL.Item
and item.lcid =AIMCOMPANIONITEMDETAIL.lcid 
and AIMCOMPANIONITEMDETAIL.lcid =AIMCOMPANIONITEM.lcid 
and AIMCOMPANIONITEMDETAIL.masteritem =AIMCOMPANIONITEMDETAIL.masteritem
and AIMCOMPANIONITEM.enableY_N ='Y' 
and item.item  IN
(SELECT ITEM FROM AIMCOMPANIONITEMDETAIL WHERE 
 LCID =@LCID AND MASTERITEM =@ITEM )
 AND AIMCOMPANIONITEMDETAIL.MASTERITEM =@ITEM
 AND Item.LCID =@LCID
 RETURN  @@rowcount






GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

