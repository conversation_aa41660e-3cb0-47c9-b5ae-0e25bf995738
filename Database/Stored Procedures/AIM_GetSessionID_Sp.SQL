SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_GetSessionID_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_GetSessionID_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**  THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
** 		** AllocationScratchTables: 		Table Creation scripts
** 
** 		* AIM_Alloc_Pass1_Sp:			Sub called by AllocationInventory
** 		* AIM_Alloc_Pass2_Sp:			Sub called by AllocationInventory
** 
** 		* AIM_GetSessionID_Sp:			Sub called by AllocationCtrl
** 		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
** 		* AIM_AllocateInventory_Sp:		Sub called by AllocationCtrl 
** 		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
** 		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
** 		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
** 		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
** 		* AIM_AllocationCtrl_Sp			Main Stored Procedure
** 
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
** 		Calls all the stored procedures
** 
*******************************************************************************
** 	Name: AIM_GetSessionID_Sp
** 	Desc: Initial version as part of EXceed AIM v4.4
** 		Called by the Allocation Stored Procedure ** AIM_AllocationCtrl_Sp
**  		Session ID is to be used to enable multiple instances of the Allocation
** 		task to be executed simultaneously. Each instance will be 
** 		updating the Allocation Scratch tables using its unique Session ID
** 		The session ID will be anchored in AIM's SysCtrl table, 
** 		similar to the PONumber used by Order Generation
** 
** 	Returns:
**		1) -1 = Failure
**		2) >0 = Success/Rowcount
**
** 	Author:		Annalakshmi Stocksdale
** 	Created:	2003/05/21
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:	   Updated by:	 Description:
** 	---------- ------------- -----------------------------------------------
** 	2004/03/02 A.Stocksdale	 Updated exit to either return a rowcount for 
**                               success or -1 for failure						
*******************************************************************************/

CREATE PROCEDURE AIM_GetSessionID_Sp 
(	
	@SessID As BIGINT OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	SET NOCOUNT ON
	DECLARE @Return_Stat as int

	-- Get a new session ID
	SELECT @SessID = SessionID FROM SysCtrl
	SET @Return_Stat= @@ROWCOUNT
	
	IF @SessID IS NULL 
	BEGIN
		-- Initiate session ids
		SELECT @SessID = 1
		UPDATE SysCtrl SET SessionID = @SessID
		SET @Return_Stat= @@ROWCOUNT
	END
	ELSE
	BEGIN
		-- Increment existing session 
		UPDATE SysCtrl SET SessionID = SessionID + 1
		SET @Return_Stat= @@ROWCOUNT
	END

	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END

END
GO
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

