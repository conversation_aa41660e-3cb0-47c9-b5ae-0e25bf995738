SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_Alloc_ReleaseOrders_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_Alloc_ReleaseOrders_Sp]
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:				Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:				Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp				Main Stored Procedure
**
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**											Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_Alloc_ReleaseOrders_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Called by the Allocation Stored proc based on the parameters
**		set by the user in the Job Scheduler for scheduled Allocation.
**		This stored procedure checks for exceptions based on settings in
**		AIMDestinationProfile and SysCtrl, 
**		and update orders that are above the trigger %age as 'Released'
**		Valid options:
**		0 -- Hold All (Default)
**		1 -- Hold Exceptions Only
**		2 -- Hold None
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/21
**-----------------------------------------------------------------------------
**	Change History
**----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------		-----------------------------------------------
**	2003/09/18	A.Stocksdale	Modified to include exceptions at Order,
**								Location and Line Item levels		
**	2004/07/09	S.Uddanti		Set the OrderStatus to 12 if it is 11 for 'hold none' case			
**	2005/01/07	A.Stocksdale	Modified the OrderStatus 12 to 11 to use 
**								AIM_UpdateAIMAOOrderStatus_Sp instead (updates all 3 tables)
****************************************************************************** */

CREATE PROCEDURE AIM_Alloc_ReleaseOrders_Sp 
(
	@ExceptionProcessing AS TINYINT,
	@UserID as NVARCHAR(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @Return_Stat AS INT

	SET NOCOUNT ON

	-- Validate inputs
	IF @ExceptionProcessing IS NULL
	OR @UserID IS NULL
	BEGIN	
		RETURN -1  -- ERROR
	END

	IF  @ExceptionProcessing = 0	
	BEGIN
		-- hold all => mark the exceptions, and leave the rest as allocated
		-- Update order status to  11 'allocated with exceptions' where fillpct is less than trigger
		EXEC @Return_Stat = AIM_Alloc_HoldExceptions_Sp 'N', @UserID
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END
	END
	ELSE IF @ExceptionProcessing = 1	-- hold exceptions
	BEGIN
		-- Update order status to  11 'allocated with exceptions' where fillpct is less than trigger, or 20 'released' where it's more
		EXEC @Return_Stat = AIM_Alloc_HoldExceptions_Sp 'Y', @UserID
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END
	END
	ELSE IF @ExceptionProcessing = 2	-- hold none
	BEGIN
		
		
-- 		-- Use EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 12, 11 
-- 		Update AIMAO set
-- 		OrderStatus =12
-- 		Where OrderStatus =11
		EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 12, 11
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END

		-- Update AIMAO.OrderStatus to 'Released' where current status = 'Allocated'
		EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 20, 10
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END
	END	
	
	RETURN
END
GO
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

