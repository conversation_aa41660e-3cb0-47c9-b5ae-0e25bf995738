SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMaster_Get_SP]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMaster_Get_SP]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastMaster_Get_SP
**	Desc: Gets AIMFcstMaster Data
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - AIMFcstMaster,
**       
**              
**	Auth:   Srinivas Uddanti
**	Date:   Aug/09/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastMaster_Get_SP
( 
	@RepositoryKey 	        numeric(18,0),
       	@LcID     		nvarchar(12),
        @Item   		nvarchar(25),
	@PeriodBegDate		datetime,
	@PeriodEndDate		datetime
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

SELECT M.LcID, M.Item, M.PeriodBegDate,M.PeriodEndDate,
COALESCE(F.Fcst,0) Fcst,
COALESCE(M.QtyAdj,0) QtyAdj,
M.QtyAdjPct,
COALESCE(M.QtyAdjOverRide,0) QtyAdjOverRide,
M.AdjOverRide
FROM ForecastRepositoryDetail F 
 INNER JOIN AIMFcstMaster M
ON F.Lcid =M.Lcid
and F.Item =M.item
and F.RepositoryKey =@RepositoryKey 
and M.Lcid =@LcID
and M.Item =@Item
and F.FcstPdBegDate=M.PeriodBegDate
and M.PeriodBegDate >=@PeriodBegDate
and M.PeriodEndDate <=@PeriodEndDate
Order by M.PeriodBegDate


SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO		

