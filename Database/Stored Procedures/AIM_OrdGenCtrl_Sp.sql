SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_OrdGenCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_OrdGenCtrl_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_OrdGenCtrl_Sp
**	Desc: Order Generation engine, as called from Buyer Review and/or 
**            AIMDataInterface
**	
**	Parameters:
**		@FilterOpt:- valid values:
**				--> A - All Review Cycles Scheduled for review (Default)
**				--> R - Specified Review Cycle
**				--> V - All items for a Specified vendor/assortment
**		@RevCycleKey:- valid values:
**				--> IF @FilterOpt = A or V, THEN 'ALL' (Default)
**				--> ELSEIF @FilterOpt = R THEN Review Cycle
**		@VnIdKey and 
**		@AssortKey:- valid values:
**				--> IF @FilterOpt = A or R, THEN 'ALL'
**				--> ELSEIF @FilterOpt = V Then Vendor ID/Assortment
**		@ClearPO	--> Clear PO Detail Table
**		@LT_Flag	--> Process Lead Time Exceptions
**		@PE_Flag     	--> Process Priority Exceptions
**		@OutPutOpt 	--> Output results to PO Detail Table
**		@ReviewTotal	--> Total of items reviewed (should = 0 as a parameter)
**		@LT_Total	--> Total of lead time exceptions (should = 0 as a parameter)
**		@RC_Total	--> Total of Review Cycles (should = 0 as a parameter)
**		@PE_Total	--> Total of priority exceptions (should = 0 as a parameter)
**	
**	Returns: 1) 1  - Success
**               2) 0  - Failure
**               3) -1 - Invalid parameter
**             
**	Values:  
**		@ReviewTotal	--> Total of items reviewed
**		@LT_Total	--> Total of lead time exceptions
**		@RC_Total	--> Total of Review Cycles
**		@PE_Total	--> Total of priority exceptions
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	  Description:
**	---------- -------------- --------------------------------------------
** 	2003/01/31 A. Stocksdale  Modified filter option where clauses for 
**				  specific VN/Assortment and 'All'
**	2003/06/25 Srinivas	  Remove the Quantites allocated to ALT Vendors
**				  when called from OrdGen Screen
**	2003/10/13 Srinivas	  Update LastOrderGenerationProcess field 
**	2004/04/20 Srinivas 	  Add code to run vendorsizing when filteropt is V
*******************************************************************************/
     
CREATE PROCEDURE AIM_OrdGenCtrl_Sp
(
   	@FilterOpt 		        nvarchar(1) = 'A',    /* Filter Option */
     	@RevCycleKey		        nvarchar(8) = 'ALL',  /*  Review Cycle Key */
      	@VnIdKey		        nvarchar(12) = 'ALL', /*  Vendor Id Key */
      	@AssortKey                      nvarchar(12) = 'ALL', /*  Assortment Key */
      	@ClearPO 		        nvarchar(1) = 'N',    /*  Clear PO Detail Table */
      	@LT_Flag		        nvarchar(1) = 'N',    /*  Process Lead Time Exceptions */
      	@PE_Flag     	       	        nvarchar(1) = 'N',    /*  Process Priority Exceptions */
      	@OutPutOpt 		        nvarchar(1) = 'N',    /*  Output results to PO Detail Table */
    	@ReviewTotal	     	        int 	OUTPUT,       /*  Total of items reviewed */
    	@LT_Total		        int 	OUTPUT,       /*  Total of lead time exceptions */
    	@RC_Total		        int 	OUTPUT,       /*  Total of Review Cycles */
    	@PE_Total		        int 	OUTPUT	      /*  Total of priority exceptions */
)        	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

/* Declare working variables */
DECLARE	
	-- Working Storage
	@Last_RevCycle          	nvarchar(8),	/*  Last Review Cycle */
 	@RevDate                	datetime,	/*  ReviewDate */
	@UpdateRevCycleFlag     	nvarchar(1),	/*  Update Review Cycle Flag */
    
	-- Counters
    	@ReviewCount	        		int,		/*  Count of items reviewed */
    	@LT_Count	            	int,		/*  Count of lead time exceptions */
    	@RC_Count	            	int,		/*  Count of Review Cycles */
    	@PE_Count	            	int,		/*  Count of priority exceptions */
      
	-- Processing Flags
	@RC_Flag                		nvarchar(1),	/*  Review Cycle (Y,N) */
      	@DR_Flag                		nvarchar(1),	/*  Dynamic Review (Y,N) */
    	
	-- Vendor Table
      	@VnId                   		nvarchar(12),
      	@Assort                 		nvarchar(12),
    
	-- System Control Table
  	@Vendor_Sizing          	nvarchar(1),
	
	-- Review Cycle Table
	@RevCycle               	nvarchar(8),
	@RevStatus              		tinyint,
	@RevFreq          		tinyint,
    	@NextDateTime		datetime,
      	@OrdGenStatus           	nvarchar(1),
       	@DynamicFlag            	tinyint
    	
SET NOCOUNT ON
    
-- Initialize the counters
SELECT @ReviewCount = 0, @LT_Count = 0, @RC_Count = 0, @PE_Count = 0
SELECT @ReviewTotal = 0, @LT_Total = 0, @RC_Total = 0, @PE_Total = 0
SELECT @Last_RevCycle = ''
   
-- Check for invalid parameters
IF @FilterOpt <> 'A' 
        SELECT @ClearPO = 'N', @LT_Flag = 'N', @PE_Flag = 'N'

IF @FilterOpt = 'R' 
AND (@RevCycleKey = '' Or @RevCycleKey = 'All')
	Return -1	/*  Invalid Parameter */

IF @FilterOpt = 'V' 
AND ((@VnIdKey = '' Or @VnIdKey = 'All') 
     OR (@AssortKey = '' Or @AssortKey = 'All') )
	Return -1	/*  Invalid Parameter */
  
-- Get the current date
EXEC AIM_GetDate_Sp @RevDate output
  
-- Get the System Control record
SELECT @Vendor_Sizing = Vendor_Sizing
FROM SysCtrl
  
-- Truncate the Purchase Order Tables
IF @ClearPO = 'Y'
BEGIN
    	TRUNCATE TABLE AIMPo    
    	TRUNCATE TABLE PODetail
        
        /* Just in case the user has insufficient security for truncate */
       	DELETE AIMPo
       	DELETE PODetail
END

-- Set the Review Cycle Status Codes - Probably no longer needed */
-- IF @OutPutOpt = 'Y'
-- UPDATE RevCycles SET OrdGenStatus = 'A'

-- Build the Cursor
IF @FilterOpt = 'R'         /*  Specific Review Cycle  */
BEGIN
      IF @RevCycleKey <> 'All' 
      BEGIN
      	      DECLARE VendorList CURSOR LOCAL FORWARD_ONLY STATIC FOR  
	      SELECT 	RevCycles.RevCycle, RevCycles.NextDateTime, 
	            	RevCycles.RevFreq, RevCycles.RevStatus, AIMVendors.VnId, 
	            	AIMVendors.Assort, RevCycles.DynamicFlag
	      FROM RevCycles with (NOLOCK)
	            INNER JOIN AIMVendors WITH (NOLOCK) ON RevCycles.RevCycle = AIMVendors.RevCycle
	      WHERE RevCycles.RevStatus = 1
	      AND AIMVendors.Revcycle = @RevCycleKey
	      AND RevCycles.RevFreq in (0, 1, 2, 3, 4)
	      ORDER BY RevCycles.RevCycle, AIMVendors.VnId, AIMVendors.Assort
      END
      Else
      BEGIN
      	      DECLARE VendorList CURSOR LOCAL FORWARD_ONLY STATIC FOR  
	      SELECT 	RevCycles.RevCycle, RevCycles.NextDateTime, 
	            	RevCycles.RevFreq, RevCycles.RevStatus, AIMVendors.VnId, 
	            	AIMVendors.Assort, RevCycles.DynamicFlag
	      FROM RevCycles with (NOLOCK)
	            INNER JOIN AIMVendors WITH (NOLOCK) ON RevCycles.RevCycle = AIMVendors.RevCycle
	      WHERE RevCycles.RevStatus = 1
	      -- AND AIMVendors.Revcycle = @RevCycleKey
	      AND RevCycles.RevFreq in (0, 1, 2, 3, 4)
	      ORDER BY RevCycles.RevCycle, AIMVendors.VnId, AIMVendors.Assort
      END
END
  
IF @FilterOpt = 'V'         /*  Specific Vendor/Assortment */
BEGIN
   If @VnIDKey = 'All' Or @AssortKey = 'All' 
   BEGIN
 	     DECLARE VendorList CURSOR LOCAL FORWARD_ONLY STATIC FOR  
	     SELECT RevCycles.RevCycle, RevCycles.NextDateTime, 
	            RevCycles.RevFreq, RevCycles.RevStatus, AIMVendors.VnId, 
	            AIMVendors.Assort, RevCycles.DynamicFlag
	     FROM AIMVendors WITH (NOLOCK)
	            INNER JOIN RevCycles WITH (NOLOCK) ON AIMVendors.RevCycle = RevCycles.RevCycle
	     -- WHERE AIMVendors.Vnid = @VnIdKey
	     -- AND AIMVendors.Assort = @AssortKey AND
	     WHERE RevCycles.RevStatus = 1
	     AND RevCycles.RevFreq in (0, 1, 2, 3, 4)
	     ORDER BY RevCycles.RevCycle, AIMVendors.VnId, AIMVendors.Assort
   END
   Else
   BEGIN
      	     DECLARE VendorList CURSOR LOCAL FORWARD_ONLY STATIC FOR  
	     SELECT RevCycles.RevCycle, RevCycles.NextDateTime, 
	            RevCycles.RevFreq, RevCycles.RevStatus, AIMVendors.VnId, 
	            AIMVendors.Assort, RevCycles.DynamicFlag
	     FROM AIMVendors WITH (NOLOCK)
	            INNER JOIN RevCycles WITH (NOLOCK) ON AIMVendors.RevCycle = RevCycles.RevCycle
	     WHERE AIMVendors.Vnid = @VnIdKey
	     AND AIMVendors.Assort = @AssortKey
	     AND RevCycles.RevStatus = 1
	     AND RevCycles.RevFreq in (0, 1, 2, 3, 4)
	     ORDER BY RevCycles.RevCycle, AIMVendors.VnId, AIMVendors.Assort
   END
END

IF @FilterOpt = 'A' and (@LT_Flag = 'N' and @PE_Flag = 'N')
BEGIN
  DECLARE VendorList CURSOR FORWARD_ONLY LOCAL STATIC FOR  
  SELECT RevCycles.RevCycle, RevCycles.NextDateTime, 
            RevCycles.RevFreq, RevCycles.RevStatus, AIMVendors.VnId, 
            AIMVendors.Assort, RevCycles.DynamicFlag
  FROM RevCycles WITH (NOLOCK)
            INNER JOIN AIMVendors WITH (NOLOCK) ON RevCycles.RevCycle = AIMVendors.RevCycle
  WHERE (RevCycles.NextDateTime <= @RevDate or RevCycles.DynamicFlag = 1)
  AND RevCycles.RevStatus = 1
  AND RevCycles.RevFreq IN (0, 1, 2, 3, 4)
  ORDER BY RevCycles.RevCycle, AIMVendors.VnId, AIMVendors.Assort
  END

IF @FilterOpt = 'A' and (@LT_Flag = 'Y' or @PE_Flag = 'Y')
BEGIN
   DECLARE VendorList CURSOR LOCAL FORWARD_ONLY STATIC FOR  
   SELECT RevCycles.RevCycle, RevCycles.NextDateTime, 
          RevCycles.RevFreq, RevCycles.RevStatus, AIMVendors.VnId, 
          AIMVendors.Assort, RevCycles.DynamicFlag
   FROM RevCycles WITH (NOLOCK)
            INNER JOIN AIMVendors WITH (NOLOCK) ON RevCycles.RevCycle = AIMVendors.RevCycle
            AND RevCycles.RevStatus = 1
            AND RevCycles.RevFreq in (0, 1, 2, 3, 4)
     ORDER BY RevCycles.RevCycle, AIMVendors.VnId, AIMVendors.Assort
END

OPEN VendorList
FETCH NEXT FROM VendorList INTO
        @RevCycle, @NextDateTime, @RevFreq, @RevStatus, @VnId, @Assort, @DynamicFlag

WHILE  @@FETCH_STATUS = 0 
BEGIN
        /*  Set the AIM_OrdGen_Sp Counters */
        SELECT @ReviewCount = 0, @LT_Count = 0, @RC_Count = 0, @PE_Count = 0, @UpdateRevCycleFlag = 'N'
        /*  Set the AIM_OrdGen_Sp Processing Flags */
        SELECT @RC_Flag = 'N', @DR_Flag = 'N'
        
	/******************************************************************************
	-- Call order generation for each vendor/assortment
	--    
	-- Exec AIM_OrdGen_Sp 
	-- @VnId                    nvarchar(12),        --> Vendor Id
	-- @Assort                  nvarchar(12),        --> Assortment
	-- @LT_Flag                 nvarchar(1) = 'N',   --> Process Lead Time Exceptions (Y,N)
	-- @PE_Flag                 nvarchar(1) = 'N',   --> Process Priority Exceptions (Y,N)
	-- @RC_Flag                 nvarchar(1) = 'N',   --> Review Cycle (Y,N)
	-- @DR_Flag                 nvarchar(1) = 'N',   --> Dynamic Review (Y,N)
	-- @ReviewCount	  int output, 	    --> Count of items reviewed
	-- @LT_Count	            int output,		    --> Count of lead time exceptions
	-- @RC_Count	            int output,		    --> Count of Review Cycles
	-- @PE_Count	            int output,		    --> Count of priority exceptions 
	-- @OutPutOpt 		        nvarchar(1) = 'N'    --> Output Option
	******************************************************************************/
        IF @FilterOpt = 'V'                     /*  Specific Vendor/Assortment */
        BEGIN
           /******************************************************************************
	   -- When a vendor/assortment Specific review is requested, it is treated as
            -- a Review Cycle Review to ensure an order is generated for Dynamic Review 
            -- items. Otherwise a Dynamic Review Cycle could not be forced.
	   ******************************************************************************/
           
	    /* Remove the quantites allocated to ALT Vendors */
	     UPDATE Podetail 
	     SET SOQ = 0, VSOQ =0 
	     FROM PoDetail INNER JOIN Item ON 
	    (Podetail.Item = Item.Item) AND 
	    (Podetail.lcid = Item.lcid)
	     WHERE POType = 'A'and 
	     Item.Vnid = @VnID and 
	     Item.Assort = @Assort

	   EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', 
                @ReviewCount output, @LT_Count output, @RC_Count output, @PE_Count output, @OutPutOpt
           SELECT @UpdateRevCycleFlag = 'N'
        END
  
        IF @FilterOpt = 'R'                     /*  Specific Review Cycle */
        BEGIN
            /******************************************************************************
	    -- When a Review Cycle Specific review is requested, it is treated as
            --  a Review Cycle Review to ensure an order is generated for Dynamic Review 
            --  items. Otherwise a Dynamic Review Cycle could not be forced.
	    ******************************************************************************/
            EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', 
                @ReviewCount output, @LT_Count output, @RC_Count output, @PE_Count output, @OutPutOpt
            SELECT @UpdateRevCycleFlag = 'N'
        END
            
        IF @FilterOpt = 'A' and @DynamicFlag = 1 and @NextDateTime <= @RevDate    /*  Dynamic Review Cycle */
        BEGIN
            /*  When a Dynamic Review Cycle reaches its Review Date it is treated as a Scheduled Review */
            EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', 
                @ReviewCount output, @LT_Count output, @RC_Count output, @PE_Count output, @OutPutOpt
            SELECT @UpdateRevCycleFlag = 'Y'
        END
        
        IF @FilterOpt = 'A' and @DynamicFlag = 1 and @NextDateTime > @RevDate    /*  Dynamic Review Cycle */
        BEGIN
            EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'N', 'Y', 
                @ReviewCount output, @LT_Count output, @RC_Count output, @PE_Count output, @OutPutOpt
            SELECT @UpdateRevCycleFlag = 'N'
        END
      
        IF @FilterOpt = 'A' and @DynamicFlag = 0 and @NextDateTime <= @RevDate      /*  Scheduled Review  */
        BEGIN
            EXEC AIM_OrdGen_Sp @VnId, @Assort, 'N', 'N', 'Y', 'N', 
                @ReviewCount output, @LT_Count output, @RC_Count output, @PE_Count output, @OutPutOpt
            SELECT @UpdateRevCycleFlag = 'Y'
        END
        
        IF @FilterOpt = 'A' and @DynamicFlag = 0 and @NextDateTime > @RevDate      /*  Exceptions Only  */
        BEGIN
            EXEC AIM_OrdGen_Sp @VnId, @Assort, @LT_Flag, @PE_Flag, 'N', 'N', 
                @ReviewCount output, @LT_Count output, @RC_Count output, @PE_Count output, @OutPutOpt
            SELECT @UpdateRevCycleFlag = 'N'
        END
        
        /*  Update Control Totals */
        SELECT @ReviewTotal = @ReviewTotal + @ReviewCount, @LT_Total = @LT_Total + @LT_Count,
            @RC_Total = @RC_Total + @RC_Count, @PE_Total = @PE_Total + @PE_Count
        
        /*  Update the Review Cycle records - IF required */
        IF @UpdateRevCycleFlag = 'Y' and @Last_RevCycle <> @RevCycle 
  		and @OutPutOpt = 'Y' 
        BEGIN
            EXEC AIM_Update_RevCycle_Date_Sp @RevCycle, @RevDate, 'Y'
            SELECT @Last_RevCycle = @RevCycle
        END
  
	/*  Get the next row */
        GetNextRow:
            
    	FETCH NEXT FROM VendorList INTO
    	    @RevCycle, @NextDateTime, @RevFreq, @RevStatus, @VnId, @Assort, @DynamicFlag
END

-- Close the Cursor
CLOSE VendorList
DEALLOCATE VendorList

-- Run Vendor Sizing, IF appropriate
/******************************************************************************
--  For an All Order Generation, All PODetail Lines are Vendor Sized
--  For a selected Review Cycle Order Generation, only those PODetail Lines associated with
--  the selected Review Cycle are Sized
******************************************************************************/
IF @Vendor_Sizing = 'Y' and @FilterOpt = 'A'
BEGIN
      EXEC AIM_VendorSizingCtrl_Sp 'ALL'
END
IF @Vendor_Sizing = 'Y' and @FilterOpt = 'R'
BEGIN
      EXEC AIM_VendorSizingCtrl_Sp @RevCycleKey
END
IF @Vendor_Sizing = 'Y' and @FilterOpt = 'V'
BEGIN
      EXEC AIM_VendorSizingCtrl_Sp '',@VnIdKey,@AssortKey
END
--Update the sysctrl tables LastOrderGenerationProcess column
Update sysctrl Set LastOrderGenerationProcess =getdate()
RETURN 1        /*  Successful */

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

