SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VerifyDxFile_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VerifyDxFile_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_VerifyDxFile_Sp
**	Desc: Verifies if the DX File has been run before.
**
**	Returns: 1)  1 - Successful
**               2)  0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      10/30/2002 Wade Riza 	Updated validation and Error handling
*******************************************************************************/
     
CREATE PROCEDURE AIM_VerifyDxFile_Sp
(
      	@TxnSet     				nvarchar(2),
      	@FileName   				nvarchar(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @TxnSetRtn  				nvarchar(2)

SET NOCOUNT ON

-- Validate the required parameters.
IF @TxnSet IS NULL
BEGIN
	RETURN 0
END

IF @FileName IS NULL
BEGIN
	RETURN 0
END

SELECT @TxnSetRtn = TxnSet 
FROM AIMDataExchangeCtrl
WHERE TxnSet = @TxnSet
AND FileName = @FileName

IF @@rowcount = 0       -- Record Not Found
BEGIN
        -- Insert the filename into the table
        -- Return success
        INSERT INTO AIMDataExchangeCtrl(TxnSet, FileName, TxnDate) 
        VALUES (@TxnSet, @FileName, GetDate())

  	-- Check for SQL Server errors.
  	IF @@ERROR <> 0 
  	BEGIN
 		RETURN 0  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END
            
        RETURN 1            -- SUCCEED
END
ELSE                    -- Record Found
BEGIN
        RETURN 0            -- FAIL
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

