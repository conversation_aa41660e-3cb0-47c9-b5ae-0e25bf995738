SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_AllocationCtrl_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_AllocationCtrl_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON>ES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**  	* AIM_Alloc_Pass1_Sp:				Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:				Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp				Main Stored Procedure
** 
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**											Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_AllocationCtrl_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Called by the AIMbackend executable
**		Executes a sub procedure for processes that are reused (AIM_AllocateInventory_Sp)
**
**	Parameters: level of ExceptionProcessing to be applied, as set
**		by the user in the Job Scheduler for scheduled Allocation.
**		Valid options:
**		0 -- Hold All (Default)
**		1 -- Hold Exceptions Only
**		2 -- Hold None
**	Returns: Number of destinations, items and sources used in this run of allocation
**	Values:  
**	
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/21
**-----------------------------------------------------------------------------
**	Change History
**-----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------	-----------------------------------------------
**	2003/06/09	A. Stocksdale	v4.4 Open Issues (refer to comments marked "TO DO"):
**								* Sourcing Hierarchy needs to be reviewed
**								* Item Substitution needs to be tested and reviewed for correctness
**								* The possibility of a store with multiple orders needs to be handled. 
**								  Currently, the stored procedure will rollback the entire transaction
**								  if this is encountered.
**								
**	2003/08/11	A. Stocksdale	Modified insert into AIMAODetail, per Kevin Hill's request.
**								See comments marked -- A. Stocksdale 2003/08/11 begin/end
**	2004/06/02	S.Uddanti		Commented out the main transaction for federated.
**				 				Need to review at a later date
**								Taking a long time to run
**	2004/07/09	S.Uddanti		Set the OrderStatus of all the last Allocation Process
**								To 30 before inserting results for current session
**	2005/01/07	A.Stocksdale	Modified AIMAODetail to use new fields AllocUOM and AllocConvFactor,
**								as part of the "include prepack in alloc" updates for 4.6.1
*******************************************************************************/

CREATE PROCEDURE AIM_AllocationCtrl_Sp 
(
	@ExceptionProcessing AS TINYINT
	, @NumDestinations AS INT OUTPUT
	, @NumItems AS INT OUTPUT
	, @NumSources AS INT OUTPUT
	, @UserID as NVARCHAR(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @SessID AS BIGINT
	DECLARE @Item AS NVARCHAR(25)
	DECLARE @LeftoverItem AS NVARCHAR(25)
	DECLARE @SubstItem AS NVARCHAR(25)
	DECLARE @DestinationPriority AS INT
	DECLARE @TotalRequestedQty AS INT
	DECLARE @NumAIMAOOrders AS INT
	DECLARE @NumAIMAOLcIDs AS INT
--	DECLARE @SplitAllocQty AS INT
	DECLARE @Return_Stat AS INT

	SET NOCOUNT ON
	
-- TO DO -- 2 -- split allocated qty for an item across multiple orders from the same store.
	SELECT @NumAIMAOOrders = COUNT(DISTINCT AIMAO.OrdNbr)
		FROM AIMAO
		WHERE  AIMAO.OrderStatus = 0		-- Pending
	
	SELECT @NumAIMAOLcIDs = Count(DISTINCT AIMAO.LcID)
		FROM AIMAO
		WHERE  AIMAO.OrderStatus = 0		-- Pending

	IF @NumAIMAOLcIDs < @NumAIMAOOrders
	BEGIN
		-- TO DO -- 2 -- split allocated qty for an item across multiple orders from the same store.
		-- Suggested: put aggregate requested qty and allocated qty per source on the order(s)
		-- For now, leave it all in the scratch tables for future ref.
		RETURN -3
	END
-- END TO DO -- 2 -- split allocated qty for an item across multiple orders from the same store.

	If @ExceptionProcessing IS NULL
	BEGIN
		-- default to hold for review
		SET @ExceptionProcessing = 0
	END

	-- Get a new session ID -- Call AIM_GetSessionID_Sp returning @SessID 
	EXEC @Return_Stat = AIM_GetSessionID_Sp @SessID OUTPUT
	IF @Return_Stat < 0 
	OR @@ERROR <> 0
	BEGIN
		RETURN @Return_Stat
	END

	-- MAIN TRANSACTION
	--BEGIN TRANSACTION
		-- Prepare scratch tables -- Call AIM_Alloc_ScratchInserts_Sp using @SessID
		EXEC @Return_Stat = AIM_Alloc_ScratchInserts_Sp @SessID 
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END

		-- Start processing for allocation
		DECLARE GetDestinationPriority CURSOR LOCAL FAST_FORWARD FOR
			SELECT ASRP.DestinationPriority 
			FROM AllocScratch_Req_Pri ASRP 
			WHERE ASRP.SessionID = @SessID
			GROUP BY ASRP.DestinationPriority
			ORDER BY ASRP.DestinationPriority ASC
		-- First task: allocate primary requests by priority
		-- For each (DestinationPriority) in the requirements
		OPEN GetDestinationPriority
		FETCH NEXT FROM GetDestinationPriority INTO @DestinationPriority
		WHILE (@@FETCH_STATUS = 0)
		BEGIN
			-- Get Item information -- item id, total requested qty across all destinations and the sum of their need ratios
			DECLARE GetItemInfo CURSOR LOCAL FAST_FORWARD FOR
				SELECT ItemID
				FROM AllocScratch_ItemDemand
				WHERE DestinationPriority = @DestinationPriority
					AND AllocScratch_ItemDemand.SessionID = @SessID
				ORDER BY ItemID
			OPEN GetItemInfo
			FETCH NEXT FROM GetItemInfo INTO @Item
			WHILE @@FETCH_STATUS = 0
			BEGIN
				--Run Allocation (Passes 1 and 2)
				EXEC @Return_Stat = AIM_AllocateInventory_Sp 
					@SessID, 
					@DestinationPriority, 
					@Item, 
					@Item -- No Substitution, so send item for both fields
				IF @Return_Stat < 0 
				OR @@ERROR <> 0
				BEGIN
					RETURN @Return_Stat
				END
				-- At this point, "primary" (that is, unsubstituted) allocation 
				-- is done for the given item.
				-- Update @@Fetch_Status
				FETCH NEXT FROM GetItemInfo INTO @Item
			END
			-- At this point, "primary" (that is, unsubstituted) allocation 
			-- is done for all items in the given priority.
			CLOSE GetItemInfo
			DEALLOCATE GetItemInfo
			
			-- Second task: allocate substitute requests by priority
			-- For each (DestinationPriority) in the requirements
-- -- SINCE THIS IS FOR EACH PRIORITY, INSTEAD OF AT THE END AFTER ALL PRIORITIES.
-- -- If this were to be called once all priorities have been processed 
-- -- then move the entire block outside GetDestinationPriority loop
-- -- uncomment the following, except for the deallocate command:
--
--		FETCH NEXT FROM GetDestinationPriority INTO @DestinationPriority
--		 CLOSE GetDestinationPriority
-- -- DO NOT -- DEALLOCATE GetDestinationPriority -- since the priorities need to be looped through again
--		OPEN GetDestinationPriority
--		FETCH NEXT FROM GetDestinationPriority
--			INTO @DestinationPriority
--		WHILE @@FETCH_STATUS = 0
--		BEGIN
			-- Get Item information -- item id, total requested qty across all destinations and the sum of their need ratios
			DECLARE GetLeftoverItemInfo CURSOR LOCAL FAST_FORWARD FOR
				SELECT ASID.ItemID
				FROM AllocScratch_ItemDemand ASID
				INNER JOIN AllocScratch_Req_Qty ASRQ 
					ON ASID.ItemID = ASRQ.ItemID 
				WHERE ASID.DestinationPriority = @DestinationPriority
					AND ASRQ.RequestedQty > 0
					AND ASID.TotalRequestedQty > 0
					AND ASID.SessionID = @SessID
					AND ASRQ.SessionID = @SessID
				ORDER BY ASID.ItemID
			-- NOTE: Because fully allocateds have been zeroed in ASRQ, 
			-- this should only have records that still need inventory
			OPEN GetLeftoverItemInfo
			FETCH NEXT FROM GetLeftoverItemInfo INTO @LeftoverItem
			WHILE @@FETCH_STATUS = 0
			BEGIN
				-- Get a list of (store, item, qty) FOR SUBSTITUTE ITEMS ordered by ratio
				DECLARE GetItemSubst CURSOR LOCAL FAST_FORWARD FOR
					SELECT SSUB.AllocSubstItem
					FROM AllocItemSubstitutes SSUB 
					WHERE SSUB.AllocPrimaryItem = @LeftoverItem
					ORDER BY SSUB.AllocSubstPriority, AllocSubstItem
				-- For each (Store, Item, Qty) in the requirements
				-- Same process as regular allocation
				OPEN GetItemSubst
				FETCH NEXT FROM GetItemSubst INTO @SubstItem
				WHILE @@FETCH_STATUS = 0
				BEGIN
					--Run Allocation
					EXEC @Return_Stat = AIM_AllocateInventory_Sp 
						@SessID, 
						@DestinationPriority, 
						@LeftoverItem, 
						@SubstItem
					IF @Return_Stat < 0 
					OR @@ERROR <> 0
					BEGIN
						RETURN @Return_Stat
					END
	
					-- Update @@Fetch_Status
					FETCH NEXT FROM GetItemSubst INTO @SubstItem
				END
				CLOSE GetItemSubst
				DEALLOCATE GetItemSubst
				-- Update @@Fetch_Status
				FETCH NEXT FROM GetLeftoverItemInfo INTO @LeftoverItem
			END
			-- At this point, "secondary" (that is, substituted) allocation
			-- is done for the given item.
			CLOSE GetLeftoverItemInfo
			DEALLOCATE GetLeftoverItemInfo
			-- Update Fetch_Status by trying for the next record
			FETCH NEXT FROM GetDestinationPriority INTO @DestinationPriority
		END 
		-- At this point, "secondary" allocation (that is, unsubstituted)
		-- is done for all items in the given priority.
		CLOSE GetDestinationPriority
		DEALLOCATE GetDestinationPriority
		-- At this point, both "primary" and "secondary" allocation
		-- (that is, substituted and unsubstituted) is done for all priorities.
			
	-- MAIN TRANSACTION
	--COMMIT TRANSACTION
	

	-- Start transaction to move results to AIMAODetail
	BEGIN TRANSACTION
		-- Set the status of all the items in the previous allocation process to 30
		EXEC AIM_UpdateAIMAOOrderStatus_Sp @UserID, 30, 10, 11, 12, 20, 22

		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END
		-- NOTE: There are no separate time and date data types for storing only times or only dates. If only a time is specified when setting a datetime or smalldatetime value, the date defaults to January 1, 1900. If only a date is specified, the time defaults to 12:00 A.M. (Midnight).	
		-- Kevin, also need to insert a source record if no inventory has been allocated to the store. 
		-- Otherwise user cannot change the allocated qty to that store 
		-- as no record exists in Edit Allocated quantities. 
		-- Get the valid sources from the AIMSourcingHierarchy table
		INSERT INTO AIMAODetail (OrdNbr, OrdType
			, LineNbr, LineItemStatus
			, LcID, LType
			, Item, ItDesc
			, UOM
			, AllocUOM, AllocConvFactor
			, RequestedQty
			, AllocatedQty, AdjustedAllocQty
			, Cost
			, LineFillPct
			, TxnDate, TxnTimeOfDay
			, AllocationDate, AllocationTimeOfDay) 
		SELECT AIMAODetail.OrdNbr, AIMAODetail.OrdType
			, AIMAODetail.LineNbr, 10	-- Allocated
			, AIMSourcingHierarchy.LcID_Source, 'S'
			, AIMAODetail.Item, AIMAODetail.ItDesc
			, AIMAODetail.UOM
			, AIMAODetail.AllocUOM, AIMAODetail.AllocConvFactor
			, AIMAODetail.RequestedQty
			, 0, 0
			, AIMAODetail.Cost
			, 0
			, AIMAODetail.TxnDate, AIMAODetail.TxnTimeOfDay
			, CONVERT(nvarchar(10), GETDATE(), 111) , '00:00'
		FROM AIMSourcingHierarchy 
		INNER JOIN AIMAO
			ON AIMSourcingHierarchy.LcID_Destination = AIMAO.LcID
		INNER JOIN AIMAODETAIL 
			ON AIMAO.OrdNbr = AIMAODetail.OrdNbr
			AND AIMSourcingHierarchy.LcID_Destination = AIMAODetail.LcId
		WHERE AIMAODetail.RequestedQty > 0
			AND AIMAODetail.LType = 'D' 
			AND AIMAO.Orderstatus in (0, 1) -- Only item in the new allocation process
	
		-- Insert for substitute items
		INSERT INTO AIMAODetail (OrdNbr, OrdType
			, LineNbr, LineItemStatus
			, LcID, LType
			, Item, ItDesc
			, UOM
			, AllocUOM, AllocConvFactor
			, RequestedQty
			, AllocatedQty, AdjustedAllocQty
			, Cost
			, LineFillPct
			, TxnDate, TxnTimeOfDay
			, AllocationDate, AllocationTimeOfDay) 
		SELECT AIMAODetail.OrdNbr, AIMAODetail.OrdType
			, AIMAODetail.LineNbr, 10	-- Allocated
			, AllocScratch_AllocResults.SourceLoc, 'S'
			, AllocScratch_AllocResults.ItemID, 'Substitute Item'
			, AIMAODetail.UOM
			, AIMAODetail.AllocUOM, AIMAODetail.AllocConvFactor
			, AIMAODetail.RequestedQty
			, 0, 0
			, 0
			, 0
			, AIMAODetail.TxnDate, AIMAODetail.TxnTimeOfDay
			, CONVERT(nvarchar(10), GETDATE(), 111) , '00:00'
		FROM AIMAODETAIL 
		INNER JOIN AllocScratch_AllocResults 
			ON AIMAODetail.OrdNbr = AllocScratch_AllocResults.OrdNbr
			AND AIMAODetail.LineNbr = AllocScratch_AllocResults.LineNbr
			AND AIMAODetail.Item != AllocScratch_AllocResults.ItemID
		INNER JOIN AIMAO 
			ON AIMAODetail.OrdNbr = AIMAO.OrdNbr		
			AND AllocScratch_AllocResults.DestinationLoc = AIMAO.LcID
		WHERE AIMAODetail.RequestedQty > 0
			AND AIMAODetail.LType = 'D' 

		-- Now, update AIMAODetail with what was allocated, leaving unallocateds at zero with the proper lineitemstatus
		UPDATE AIMAODetail 
		SET AllocatedQty = AllocScratch_AllocResults.AllocatedQty
			, AdjustedAllocQty = AllocScratch_AllocResults.AllocatedQty
		FROM AllocScratch_AllocResults 
		INNER JOIN AllocScratch_Req_Qty ASRQ
			ON AllocScratch_AllocResults.SessionID = ASRQ.SessionID
			AND AllocScratch_AllocResults.OrdNbr = ASRQ.OrdNbr
			AND AllocScratch_AllocResults.LineNbr = ASRQ.LineNbr
			AND AllocScratch_AllocResults.DestinationLoc = ASRQ.DestinationLoc
--			AND AllocScratch_AllocResults.ItemID = ASRQ.ItemID
		INNER JOIN AIMAO 
			ON ASRQ.OrdNbr = AIMAO.OrdNbr
			AND ASRQ.DestinationLoc = AIMAO.LcID 
		INNER JOIN AIMAODETAIL 
			ON AllocScratch_AllocResults.OrdNbr = AIMAODetail.OrdNbr
			AND AIMAO.OrdNbr = AIMAODetail.OrdNbr
			AND AllocScratch_AllocResults.LineNbr = AIMAODetail.LineNbr	
			AND AllocScratch_AllocResults.SourceLoc = AIMAODetail.LcID
			AND AllocScratch_AllocResults.ItemID = AIMAODetail.Item
		WHERE AIMAODetail.RequestedQty > 0
			AND AIMAODetail.LType = 'S' 
 
	-- NOTE: DO NOT CHANGE THE ORDER IN WHICH THESE UPDATES ARE EXECUTED!
		-- Update AIM's order tables for status
		EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 10, 0	-- set status='Allocated', where status='Pending'
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END

		EXEC @Return_Stat = AIM_UpdateAIMAOOrderStatus_Sp @UserID, 11, 1 -- set status='Not Allocated', where status='Do Not Allocate'
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END

		-- Update aggregates in AIMAO
		EXEC @Return_Stat = AIM_Alloc_UpdateAggregates_Sp
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END

		-- Update order status based on parameter passed
		EXEC @Return_Stat = AIM_Alloc_ReleaseOrders_Sp @ExceptionProcessing, @UserID
		IF @Return_Stat < 0 
		OR @@ERROR <> 0
		BEGIN
			RETURN @Return_Stat
		END
	-- END NOTE: DO NOT CHANGE THE ORDER IN WHICH THESE UPDATES ARE EXECUTED!

		-- Clear scratch tables of current set.
		DELETE FROM AllocScratch_SourceInventory WHERE SessionID = @SessID
		DELETE FROM AllocScratch_ItemDemand WHERE SessionID = @SessID
		DELETE FROM AllocScratch_Req_Pri WHERE SessionID = @SessID
		DELETE FROM AllocScratch_Req_Qty WHERE SessionID = @SessID
		DELETE FROM AllocScratch_Req_Ratio WHERE SessionID = @SessID
		DELETE FROM AllocScratch_AllocResults WHERE SessionID = @SessID

	-- End transaction to move results to AIMAODetail
	COMMIT TRANSACTION

	-- Set status indices
	SELECT @NumDestinations = Count(Distinct AIMAO.LcID) 
	, @NumItems = Count(Distinct AIMAODetail.Item) 
	, @NumSources = Count(Distinct AIMAODetail.LcID) 
	FROM AIMAO
	INNER JOIN AIMAODetail
		ON AIMAO.OrdNbr = AIMAODetail.OrdNbr
	WHERE AIMAODetail.LType = 'S'

END
GO

SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO

