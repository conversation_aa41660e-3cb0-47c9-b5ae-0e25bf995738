SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ClassByClassLevel_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ClassByClassLevel_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON>ES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ClassByClassLevel_List_Sp
**	Desc: Gets records from AIMClasses table
**
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Recordset - Class, ClassDesc 
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   08/13/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza	Updated Return codes
**      09/24/2002 Wade Riza    Added LangID 
*******************************************************************************/

CREATE PROCEDURE AIM_ClassByClassLevel_List_Sp
(
       	@ClassLevel					nvarchar(12),
    	@LangID						nvarchar(10)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int

-- Validate the required parameters.
IF @ClassLevel IS NULL 
OR @LangID IS NULL
BEGIN
	RETURN -1
END

SELECT Class, ClassDesc 
FROM AIMClasses
WHERE ClassLevel= @ClassLevel 
AND LangID = @LangID
ORDER BY Class, ClassDesc

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
   	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN @found  -- SUCCESSFUL
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

