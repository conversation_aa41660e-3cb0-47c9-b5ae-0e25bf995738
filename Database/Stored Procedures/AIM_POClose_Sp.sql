SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POClose_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POClose_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_POClose_Sp
**	Desc: Updates the current status of thePODetail and the AIMPO table.
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_POClose_Sp
(
    	@ById       					nvarchar(12),
     	@VnId       					nvarchar(12),
     	@Assort     					nvarchar(12),
     	@LcId       					nvarchar(12),
    	@SelOption  					tinyint  
				          		-- 1 = Select by ById, VnId, Assort
    	                        			-- 2 = Select by ById, VnId, Assort, LcId
    	                        			-- 3 = Select all released PO Detail Lines
    	                        			-- 4 = Select by Location
)    	                        			-- 5 = Select by ById

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

-- Validate the required parameters.
IF @ById IS NULL
BEGIN
	RETURN 0
END

IF @VnId IS NULL
BEGIN
	RETURN 0
END
 
IF @Assort IS NULL
BEGIN
	RETURN 0
END

IF @SelOption = 1                   -- Select by ById, VnId, Assort    
BEGIN
        UPDATE PODetail
	SET OrdStatus = 'C'
        FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
        AND AIMPO.ById = @ById
        AND AIMPO.VnId = @VnId
        AND AIMPO.Assort = @Assort
    	
	UPDATE AIMPO 
	SET POStatus = 'C'
        WHERE AIMPO.POStatus = 'R'
        AND AIMPO.ById = @ById
        AND AIMPO.VnId = @VnId
        AND AIMPO.Assort = @Assort

END
IF @SelOption = 2                   -- Select by ById, VnId, Assort, LcId
BEGIN
        UPDATE PODetail
	SET OrdStatus = 'C'
        FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
        AND AIMPO.ById = @ById
        AND AIMPO.VnId = @VnId
        AND AIMPO.Assort = @Assort
    	AND PODetail.LcId = @LcId
    	
	UPDATE AIMPO
	SET POStatus = 'C'
        WHERE AIMPO.POStatus = 'R'
        AND AIMPO.ById = @ById
        AND AIMPO.VnId = @VnId
        AND AIMPO.Assort = @Assort

END
IF @SelOption = 3               -- Select all released PO Detail Lines
BEGIN
        UPDATE PODetail 
	SET OrdStatus = 'C'
        FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
    	
	UPDATE AIMPO 
	SET POStatus = 'C'
        WHERE AIMPO.POStatus = 'R'

END
IF @SelOption = 4               -- Select by Location
BEGIN
        UPDATE PODetail 
	SET OrdStatus = 'C'
        FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
    	AND PODetail.LcId = @LcId
    	
	UPDATE AIMPO 
	SET POStatus = 'C'
        WHERE AIMPO.POStatus = 'R'
     
END
IF @SelOption = 5                   -- Select by ById 
BEGIN
        UPDATE PODetail 
	SET OrdStatus = 'C'
        FROM AIMPO
            INNER JOIN PODetail ON AIMPO.ById = PODetail.ById
    		AND AIMPO.VnId = PODetail.VnId
    		AND AIMPO.Assort = PODetail.Assort
        WHERE AIMPO.POStatus = 'R'
        AND AIMPO.ById = @ById
    	
	UPDATE AIMPO 
	SET POStatus = 'C'
        WHERE AIMPO.POStatus = 'R'
        AND AIMPO.ById = @ById

END

RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

