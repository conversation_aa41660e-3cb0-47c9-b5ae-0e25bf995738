SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_KitBOMSummary_Upd_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_KitBOMSummary_Upd_Sp]
GO

/******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_KitBOMSummary_Upd_Sp
**	Desc: Updates the KitBOMSummary table with data 
**
**	Returns: 1)@@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	    Author:				Description:
**	----------  ------------		-------------------------------
*******************************************************************************/
     
CREATE  PROCEDURE AIM_KitBOMSummary_Upd_Sp
(
      	@Lcid            				nvarchar(12),
  	@Item           				nvarchar(25),
    	@Accum_Lt       				smallint,
    	@ReviewTime     				smallint,
    	@OrderPt        				int,
    	@OrderQty       				int,
    	@SafetyStock    				decimal(10, 2),
	@FcstDemand					decimal(10,2),
	@DependentFcstDemand				decimal(10,2),
    	@FcstRT         				decimal(10, 2),
    	@FcstLT         				decimal(10, 2),
    	@Fcst_Month     				decimal(10, 2),
    	@Fcst_Quarter   				decimal(10, 2),
    	@Fcst_Year      				decimal(10, 2)  
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @LcidKey  		        			nvarchar(12),
        @ItemKey 		         		nvarchar(25) 
            
SET NOCOUNT ON
    
-- Check for the existance of the MDC Summary Record
SELECT @LcidKey = Lcid, @ItemKey = Item 
FROM KitBOMSummary
WHERE Lcid = @Lcid 
AND Item = @Item
        
IF @@rowcount = 0   -- Not Found
BEGIN
        INSERT INTO KitBOMSummary(Lcid, Item, Accum_Lt, ReviewTime, OrderPt, OrderQty, 
            SafetyStock,FcstDemand,DependentFcstDemand, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year) 
        VALUES (@Lcid, @Item, @Accum_Lt, @ReviewTime, @OrderPt, @OrderQty, 
            @SafetyStock,@FcstDemand,@DependentFcstDemand, @FcstRT, @FcstLT, @Fcst_Month, @Fcst_Quarter, @Fcst_Year)
END
ELSE                -- Found
BEGIN
        UPDATE KitBOMSummary
	SET Accum_Lt = @Accum_LT, ReviewTime = @ReviewTime, 
            OrderPt = @OrderPt, OrderQty = @OrderQty, SafetyStock = @SafetyStock, 
	    FcstDemand =@FcstDemand,DependentFcstDemand =@DependentFcstDemand,
            FcstRT = @FcstRT, FcstLT = @FcstLT, Fcst_Month = @Fcst_Month, 
            Fcst_Quarter = @Fcst_Quarter, Fcst_Year = @Fcst_Year
        WHERE Lcid = @Lcid and Item = @Item 
END
/*
BEGIN

UPDATE Item 
 SET  SafetyStock =@SafetyStock,
	FcstRT =@FcstRT,
	FcstLT =@FcstLT,
	Fcst_Month =@Fcst_Month,
	Fcst_Quarter=@Fcst_Quarter,
	Fcst_Year=@Fcst_Year,
	OrderPt=@OrderPt,
	OrderQty =@OrderQty,
	FcstDemand=(@Fcst_Year/52)
 WHERE Lcid =@Lcid and
 	Item =@Item
END
*/
RETURN


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



