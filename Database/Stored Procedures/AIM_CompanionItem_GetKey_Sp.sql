SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_CompanionItem_GetKey_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_CompanionItem_GetKey_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_CompanionItem_GetKey_Sp
**	Desc: Retreives CompanionItem information based on Location ID, 
**            MasterItem And Action
**
**	Returns: 1)@found - Can be Zero
**               2) 1 - No Data Found
**             
**	Values:  Recordset - AccessType
**              
**	Auth:   Srinivas Uddanti 
**	Date:   03/03/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
   
CREATE  PROCEDURE AIM_CompanionItem_GetKey_Sp
(
       	@LcId 						nvarchar(12) 	OUTPUT,		-- Location ID
       	@Item					nvarchar(25) 	OUTPUT,		-- MasterItem Number
       	@action						tinyint			        -- Action Code
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rtncode 					int

SET NOCOUNT ON

SET rowcount 1

-- Validate the required parameters.
--IF @LcID IS NULL
--BEGIN
--	RETURN 1
--END
--IF @Item IS NULL
--BEGIN
--	RETURN 1
--END

IF @action IS NULL
BEGIN
	RETURN 1
END

-- Get the key to the CompanionItem Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
  	SELECT @LcId = lcid, @Item = Masteritem
	FROM AIMCompanionItem
	WHERE lcid = @lcid 
	AND Masteritem = @Item
	ORDER BY lcid ASC, Masteritem ASC

	SELECT @rtncode = @@rowcount
END
  
IF @action = 1		-- Get Greater Than
BEGIN
  	SELECT @Lcid = lcid, @Item = MasterItem
	FROM AIMCompanionItem
	WHERE lcid = @lcid 
	AND MasterItem > @Item
	ORDER BY lcid ASC, MasterItem ASC
        
	SELECT @rtncode = @@rowcount

	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = MasterItem 
		FROM AIMCompanionItem
		WHERE lcid > @lcid
		ORDER BY lcid ASC, MasterItem ASC
 
	 	SELECT @rtncode = @@rowcount
       END
END
  
IF @action = 2		-- Get Less Than 
BEGIN
  	SELECT @Lcid = lcid, @Item = MasterItem 
	FROM AIMCompanionItem
	WHERE lcid = @lcid 
	AND MasterItem < @Item
        ORDER BY lcid DESC, MasterItem DESC

        SELECT @rtncode = @@rowcount
       
	IF @rtncode = 0
       	BEGIN
        	SELECT @Lcid = lcid, @Item = MasterItem 
		FROM AIMCompanionItem
		WHERE lcid < @lcid 
                ORDER BY lcid DESC, MasterItem DESC

           	SELECT @rtncode = @@rowcount
       END
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
       	SELECT @Lcid = lcid, @Item = MasterItem 
	FROM AIMCompanionItem
	WHERE lcid = @lcid
	AND MasterItem >= @Item
	ORDER BY lcid ASC, MasterItem ASC

       	SELECT @rtncode = @@rowcount
       
	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = MasterItem 
		FROM AIMCompanionItem 
		WHERE lcid > @lcid
		ORDER BY lcid ASC, MasterItem ASC
 
        	SELECT @rtncode = @@rowcount
	END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
  	SELECT @Lcid = lcid, @Item = MasterItem
	FROM AIMCompanionItem
	WHERE lcid = @lcid
	AND MasterItem <= @Item
        ORDER BY lcid DESC, MasterItem DESC

	SELECT @rtncode = @@rowcount

       	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = MasterItem 
		FROM AIMCompanionItem
		WHERE lcid < @lcid 
               	ORDER BY lcid DESC, MasterItem DESC
           	
		SELECT @rtncode = @@rowcount
       	END
END
IF @action = 5		-- Get First
BEGIN
	SELECT @LcId = lcid, @Item = MasterItem 
	FROM AIMCompanionItem
    	ORDER BY lcid ASC, MasterItem ASC

       	SELECT @rtncode = @@rowcount

END
IF @action = 6		-- Get Last
BEGIN
  	SELECT @LcId = lcid, @Item = MasterItem
	FROM AIMCompanionItem
        ORDER BY lcid DESC, MasterItem DESC

	SELECT @rtncode = @@rowcount

END
IF @rtncode > 0 
BEGIN
  	return 1		-- SUCCEED
END
ELSE
BEGIN
  	return 0		-- FAIL
END



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



