SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO
IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_Alloc_Pass2_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_Alloc_Pass2_Sp]
GO
/*******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** THIS SCRIPT IS PART OF THE ALLOCATION MODULE
** To ensure that dependencies are properly assigned in the system tables, 
** please create in the order described below:
** CONTENTS (In order of creation): 
**		** AllocationScratchTables: 		Table Creation scripts
**
**		* AIM_Alloc_Pass1_Sp:				Sub called by AllocationInventory
**		* AIM_Alloc_Pass2_Sp:				Sub called by AllocationInventory
**
**		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
**		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
**		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
**		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
**		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
**		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
**		* AIM_AllocationCtrl_Sp				Main Stored Procedure
**
** 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
**											Calls all the stored procedures
**
*******************************************************************************
**	Name: AIM_Alloc_Pass2_Sp
**	Desc: Initial version as part of EXceed AIM v4.4
**		Called by AIM_AllocationCtrl_Sp
**		Allocate in increments of n until inventory is exhausted. 
**
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/31
**-----------------------------------------------------------------------------
**	Change History
**-----------------------------------------------------------------------------
**	Date:		Updated by:		Description:
**	----------	------------	-----------------------------------------------
**	2003/09/25	A.Stocksdale	Modified to keep track of Order Number							
**	2005/01/07	A.Stocksdale	Apply AllocConvFactor before assigning final allocated qty.
*******************************************************************************/

CREATE PROCEDURE AIM_Alloc_Pass2_Sp 
(
	@SessID AS BIGINT
	, @DestinationPriority AS INT
	, @RequestedItem AS NVARCHAR(25)
	, @AllocatableItem AS NVARCHAR(25)
	, @AllocatableQty AS INT OUTPUT
	, @RemainingRequests AS INT OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @TotalRequestedQty AS INT
	DECLARE @DestinationLoc AS NVARCHAR (12)
	DECLARE @RequestedQty AS INT
	DECLARE @AllocatedQty AS INT
	DECLARE @SourceLoc AS Nvarchar(12)
	DECLARE @SourceQty AS INT
	DECLARE @ConvFactor AS INT
	DECLARE @TotalAllocatableQty AS INT
	DECLARE @OrdNbr AS NVARCHAR(12)
	DECLARE @LineNbr AS NVARCHAR(50)
	
	SET NOCOUNT ON

	-- Validate inputs
	IF @SessID IS NULL
	OR @DestinationPriority IS NULL
	OR @RequestedItem IS NULL
	BEGIN	
		RETURN -1  -- ERROR
	END
	IF @AllocatableItem IS NULL
	BEGIN
		-- Set it equal to the item
		SET @AllocatableItem = @RequestedItem
	END
	
-- TO DO: Join to AIMSourcingHierarchy to get only the sources that can fulfill given store's need and move total requested qty out and get this on a store-by-store basis.
	-- Get Total requested qty
	SELECT @TotalRequestedQty = TotalRequestedQty
	FROM AllocScratch_ItemDemand
	WHERE DestinationPriority = @DestinationPriority
		AND ItemID = @RequestedItem
		AND AllocScratch_ItemDemand.SessionID = @SessID
	ORDER BY ItemID
	
	IF (@TotalRequestedQty > 0)
	BEGIN	
		-- Get total Allocatable from all sources
		SELECT @TotalAllocatableQty = SUM(ASSI.SourceQty)
		FROM AllocScratch_SourceInventory ASSI
		WHERE ASSI.ItemID = @AllocatableItem
			AND ASSI.SessionID = @SessID 
			AND ASSI.SourceQty > 0
	END
	ELSE
	BEGIN
		-- No requests, therefore no processing
		SET @AllocatableQty = 0
		SET @RemainingRequests = 0
		RETURN
	END

	-- Check for no allocatable inventory
	IF (@TotalAllocatableQty <= 0 OR @TotalAllocatableQty IS NULL)
	BEGIN	
		SET @AllocatableQty = 0
		SET @RemainingRequests = 0
		RETURN
	END

	-- Get a list of (store, item, qty) ordered by ratio
-- move to a vb loop? cannot nest cursors and update a table used in the cursor.
-- if priloop is not a cursor, then the after-the-fact expensive update can be eliminated.
-- 
	DECLARE PriLoop CURSOR LOCAL FAST_FORWARD FOR
		SELECT ASRQ.DestinationLoc
			, ASRQ.RequestedQty
			, ASRR.OrdNbr
			, ASRR.LineNbr
		FROM AllocScratch_Req_Pri ASRP 
		INNER JOIN AllocScratch_Req_Ratio ASRR 
			ON ASRP.DestinationLoc = ASRR.DestinationLoc 
		INNER JOIN AllocScratch_Req_Qty ASRQ 
			ON ASRR.OrdNbr = ASRQ.OrdNbr
			AND ASRR.LineNbr = ASRQ.LineNbr
			AND ASRP.DestinationLoc = ASRQ.DestinationLoc 
			AND ASRR.ItemID = ASRQ.ItemID
		WHERE ASRP.DestinationPriority = @DestinationPriority 
			AND ASRQ.ItemID = @RequestedItem
			AND ASRQ.RequestedQty > 0
			AND	ASRP.SessionID = @SessID 
			AND	ASRQ.SessionID = @SessID 
			AND	ASRR.SessionID = @SessID
		ORDER BY ASRR.NeedRatio DESC	--ASC
-- END TO DO: Join to AIMSourcingHierarchy to get only the sources that can fulfill given store's need and move total requested qty out and get this on a store-by-store basis.


	-- For each (Store, Item, Qty) in the requirements
	OPEN PriLoop
	FETCH NEXT FROM PriLoop 
		INTO @DestinationLoc
			, @RequestedQty
			, @OrdNbr
			, @LineNbr
	WHILE (@@FETCH_STATUS = 0 
		AND @TotalRequestedQty > 0
		AND @TotalAllocatableQty > 0
		)
	BEGIN
		-- Get a list of (Source, Allocatable Quantity) ordered by priority
		DECLARE SrcLoop CURSOR LOCAL FAST_FORWARD FOR
			SELECT ASSI.SourceLoc
				, ASSI.SourceQty
				, ASSI.AllocConvFactor		-- to derive prepack qty.  NOTE: If SysCtrl.AllocPrepackEnabled is false, then this will default to EA and 1
			FROM AllocScratch_SourceInventory ASSI 
			INNER JOIN AIMSourcingHierarchy SDC 
			ON SDC.LcID_Source = ASSI.SourceLoc
			WHERE SDC.LcID_Destination = @DestinationLoc 
				AND	ASSI.ItemID = @AllocatableItem 
				AND ASSI.SessionID = @SessID
				AND ASSI.SourceQty > 0
				AND ASSI.AllocConvFactor > 0 -- avoid bad data resulting in divide-by-zero errors
				AND SDC.Src_Priority > 0	--	0 means that they are not to be considered for allocation
			ORDER BY SDC.Src_Priority
	
		-- For each source
		OPEN SrcLoop
		FETCH NEXT FROM SrcLoop 
			INTO @SourceLoc
				, @SourceQty
				, @ConvFactor
		WHILE (@@FETCH_STATUS = 0 AND @RequestedQty > 0)
		BEGIN
			-- Allocate in increments of the conversion factor until all available is used up.
			IF @RequestedQty >= @ConvFactor
			BEGIN
				SELECT @AllocatedQty = @ConvFactor
				-- Write the allocation result record using what has been granted.
				UPDATE AllocScratch_AllocResults
				SET AllocatedQty = (AllocatedQty + @AllocatedQty)
				WHERE SessionID = @SessID
					AND DestinationLoc = @DestinationLoc
					AND SourceLoc = @SourceLoc
					AND OrdNbr = @OrdNbr
					AND LineNbr = @LineNbr
					AND ItemID = @AllocatableItem
				IF @@ROWCOUNT = 0
				BEGIN
					INSERT INTO AllocScratch_AllocResults 
						(SessionID, OrdNbr, LineNbr, DestinationLoc, ItemID, SourceLoc, AllocatedQty) 
					VALUES
						(@SessID, @OrdNbr, @LineNbr, @DestinationLoc, @AllocatableItem, @SourceLoc, @AllocatedQty)
				END
	
				-- Reflect the source's allocatable inventory based on what was granted.
				SELECT @SourceQty = @SourceQty - @AllocatedQty
				-- Update the source's allocatable quantity
				UPDATE AllocScratch_SourceInventory SET
					SourceQty = @SourceQty 
				WHERE SessionID = @SessID 
					AND SourceLoc = @SourceLoc 
					AND ItemID = @AllocatableItem
	
				-- Update the destination's requested qty to reflect what has been granted.
				SELECT @RequestedQty = @RequestedQty - @AllocatedQty
	
				-- Deduct what has been given away from the total inventory for the item.
				SELECT @TotalAllocatableQty = @TotalAllocatableQty - @AllocatedQty
				SELECT @TotalRequestedQty = @TotalRequestedQty - @AllocatedQty
			END
			-- Update Fetch_Status by trying for the next record
			FETCH NEXT FROM SrcLoop INTO @SourceLoc, @SourceQty, @ConvFactor
		END
		CLOSE SrcLoop
		DEALLOCATE SrcLoop
	
		-- Done with this (Store, Item, Qty) - go on to the next
		-- Update Fetch_Status by trying for the next record
		FETCH NEXT FROM PriLoop INTO @DestinationLoc, @RequestedQty, @OrdNbr, @LineNbr
	END
	CLOSE PriLoop
	DEALLOCATE PriLoop

	-- Update the destination's requested qty
	UPDATE AllocScratch_Req_Qty 
	SET RequestedQty = AIMAODetail.RequestedQty -
			(SELECT ISNULL(SUM(AllocScratch_AllocResults.AllocatedQty), 0) 
				FROM AllocScratch_AllocResults 
				WHERE AllocScratch_Req_Qty.SessionID = AllocScratch_AllocResults.SessionID
				AND AllocScratch_Req_Qty.OrdNbr = AllocScratch_AllocResults.OrdNbr
				AND AllocScratch_Req_Qty.LineNbr = AllocScratch_AllocResults.LineNbr
				AND AllocScratch_Req_Qty.DestinationLoc = AllocScratch_AllocResults.DestinationLoc
			)
	FROM AIMAODetail 
	WHERE	AllocScratch_Req_Qty.OrdNbr = AIMAODetail.OrdNbr AND
		AllocScratch_Req_Qty.LineNbr = AIMAODetail.LineNbr AND
		AllocScratch_Req_Qty.DestinationLoc = AIMAODetail.LcID
	AND AIMAODetail.LType = 'D'
	AND AllocScratch_Req_Qty.SessionID = @SessID
	AND AllocScratch_Req_Qty.RequestedQty > 0

	-- Update total demand for the item		
	UPDATE AllocScratch_ItemDemand 
		SET TotalRequestedQty = @TotalRequestedQty
	WHERE SessionID = @SessID
		AND ItemID = @RequestedItem
		AND DestinationPriority = @DestinationPriority
	
	-- Return updated inventory 
	SELECT @AllocatableQty = @TotalAllocatableQty
	SELECT @RemainingRequests = @TotalRequestedQty
	RETURN
END
GO

SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO
SET NOCOUNT OFF
GO
