SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxDD_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxDD_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON>ES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxDD_Sp
**	Desc: Loads Demand Detail records from the AIM DxDd Table into the 
**	      Item History Table.
**
**      Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**			1)  0 - Successful
**              	2) -1 - No Data Found
**              	3) -2 - SQL Error
**              	4) -3 - Duplicate File Name
**              	5) -4 - Invalid File Name
**              
**	Auth:   Randy Sadler
**	Date:   03/21/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:	 Description:
**    ---------- --------------- -----------------------------------------------
**    2003/05/28 A. Stocksdale	 Replaced bulk insert and validation commands 
**				 with call to AIM_DxBulkInsert_Sp - common to all 
**                               Dx processes.
**    2003/09/24 Srinivas U      Modified code for SubsItem
**    2004/01/06 Srinivas U      Modified to fix issue with Inserts
**    2004/01/07 Srinivas U      Modified to fix else  
**    2005/03/08 Srinivas U	 Movied truncate and delete outside the commit
**				 Added truncate and delete before the bulk insert
*******************************************************************************/

CREATE PROCEDURE AIM_DxDd_Sp 
(
  	@FileName nvarchar(255) = 'All',
	@RowCounter int OUTPUT,
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE 
		--Misc Values
		@begofyr datetime, -- BEGINning of Year
		@hy smallint, -- Current History Year
		@itemfound int, -- Item Found Switch
		@pd tinyint, -- Current Period
		@RtnCode int, -- Return Code
		@targetdate datetime, -- Target Date
		@yearsdIFf int, -- Years DIFference
		-- Stock Detail Summary Record
		@DxDd_lcid nvarchar(12),
		@DxDd_item nvarchar(25), 
		@DxDd_txndate datetime,
		@DxDd_cps int,
		@DxDd_qtyord int,
		@DxDd_ordcnt int,
		@hs_lcid nvarchar(12),
		@hs_item nvarchar(25),
		@hs_hisyear smallint,
		-- SysCtrl Table 
		@Retain_Sd_Opt tinyint, -- Retain SD Option from SysCtrl Table
		@Calc_Perform tinyint, -- Calculate Performance from SysCtrl Table
		@Unicode_Option nvarchar(10) -- Retain Unicode Option from SysCtrl Table

	--Initialize counters
	SET @InsertCounter = 0
	SET @UpdateCounter = 0
  	-- Delete records from the DxDd Table
	TRUNCATE TABLE AIMDxDd
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxDd

	-- This is a data interface transaction <Transaction Code=DD>. 
	-- Bulk Load the <Transaction Table=AIMDxDd> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'DD', 'AIMDxDd', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
		SELECT 0 as 'Rows', 
			0 as 'Inserts', 
			0 as 'Updates'
		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- Set the sales detail save and calc perform options from the SysCtrl Table
	SELECT @Retain_Sd_Opt = Retain_Sd_Opt
		, @Calc_Perform = Calc_Perform 
	FROM SysCtrl

	-- CREATE cursor to process DxDd
	DECLARE DxDd_Cursor CURSOR LOCAL FAST_FORWARD FOR 
		SELECT lcid, item, txndate = CONVERT(datetime,txndate), cps = 0, qtyord = sum(ordqty),
			ordcnt = COUNT(OrdNbr)
		FROM AIMDxDd  
		WHERE doctype = 'DD'  
		GROUP BY lcid, item, txndate
		FOR READ ONLY
	OPEN DxDd_Cursor
	-- Check for no rows to process
	IF @@CURSOR_ROWS = 0 
	BEGIN
		CLOSE DxDd_Cursor
		DEALLOCATE DxDd_Cursor
		RETURN
	END

	-- Initialize counters and the previous location variable
	SET @InsertCounter = 0
	SET @UpdateCounter = 0
	SET @RowCounter = 0

	-- BEGIN Transaction Set -- Failure will rollback DxDd set
	BEGIN TRANSACTION
	WHILE 1 = 1
	BEGIN
		-- Get the next row from the cursor
		FETCH NEXT FROM DxDd_Cursor INTO 
			@DxDd_lcid, @DxDd_item, @DxDd_txndate, @DxDd_cps, @DxDd_qtyord, @DxDd_ordcnt
      
		-- Check for end of cursor
		IF @@FETCH_STATUS <> 0
		BEGIN
			BREAK
		END
		-- Increment Row Count
    	
		SET @RowCounter = @RowCounter + 1	
		-- Determine the current history year and period
		
		SET @targetdate = CONVERT(datetime, @DxDd_txndate)
		EXECUTE @pd = AIM_GetPd_Sp @targetdate, @hy OUTPUT
        
		-- Get the first day of the year
		
		SELECT @begofyr = FYStartDate 
		FROM AIMYears WHERE FiscalYear = @hy
		-- Check to see IF the history record exists, IF so update the record,
		-- IF not, add the record
		SELECT @hs_lcid = lcid, @hs_item = item, @hs_hisyear = hisyear 
		FROM ItemHistory
		WHERE lcid = @DxDd_lcid
		AND item = @DxDd_item
		AND hisyear = @hy
		SET @itemfound = @@rowcount
	        -- IF no history record is found, add a new record	
	        IF @itemfound = 0		-- New History Record 
	        BEGIN
	    		--Increment Add Counter
	    		SET @InsertCounter = @InsertCounter + 1
	    		-- Insert the new item into the Item History Table
	    		INSERT INTO ItemHistory(LcId, Item, SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, 
				CPS07, CPS08, 
	    			CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, 				CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, 					CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, 
				CPS46, CPS47, CPS48, CPS49, CPS50, CPS51, CPS52, 
				QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, 
	    			QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, 
	    			QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, 
	    			QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, 
	    			QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, 
	    			QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, 
	    			OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, 
	    			OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, 
	    			OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, 
	    			OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, 
	    			OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, 
	    			OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52) 
    			VALUES (@DxDd_lcid, @DxDd_item, @DxDd_item, @hy, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
	    			0, 0, 0, 0, 0, 0)
    			
		END
	    	-- Update the History Table 
		BEGIN
			IF @pd = 1 
			UPDATE ItemHistory SET cps01 = cps01 + @DxDd_cps, qtyord01 = qtyord01 + @DxDd_qtyord,     			
			ordcnt01 = ordcnt01 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 2  
			UPDATE ItemHistory SET cps02 = cps02 + @DxDd_cps, qtyord02 = qtyord02 + @DxDd_qtyord, 
			ordcnt02 = ordcnt02 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
	    			
			IF @pd = 3
			UPDATE ItemHistory SET cps03 = cps03 + @DxDd_cps, qtyord03 = qtyord03 + @DxDd_qtyord, 
			ordcnt03 = ordcnt03 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 4
			UPDATE ItemHistory SET cps04 = cps04 + @DxDd_cps, qtyord04 = qtyord04 + @DxDd_qtyord, 
			ordcnt04 = ordcnt04 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 5 
			UPDATE ItemHistory SET cps05 = cps05 + @DxDd_cps, qtyord05 = qtyord05 + @DxDd_qtyord, 
			ordcnt05 = ordcnt05 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 6 
			UPDATE ItemHistory SET cps06 = cps06 + @DxDd_cps, qtyord06 = qtyord06 + @DxDd_qtyord, 
			ordcnt06 = ordcnt06 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 7 
			UPDATE ItemHistory SET cps07 = cps07 + @DxDd_cps, qtyord07 = qtyord07 + @DxDd_qtyord, 
			ordcnt07 = ordcnt07 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 8 
			UPDATE ItemHistory SET cps08 = cps08 + @DxDd_cps, qtyord08 = qtyord08 + @DxDd_qtyord, 
			ordcnt08 = ordcnt08 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 9 
			UPDATE ItemHistory SET cps09 = cps09 + @DxDd_cps, qtyord09 = qtyord09 + @DxDd_qtyord, 
			ordcnt09 = ordcnt09 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 10 
			UPDATE ItemHistory SET cps10 = cps10 + @DxDd_cps, qtyord10 = qtyord10 + @DxDd_qtyord, 
			ordcnt10 = ordcnt10 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 11 
			UPDATE ItemHistory SET cps11 = cps11 + @DxDd_cps, qtyord11 = qtyord11 + @DxDd_qtyord, 
			ordcnt11 = ordcnt11 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 12 
			UPDATE ItemHistory SET cps12 = cps12 + @DxDd_cps, qtyord12 = qtyord12 + @DxDd_qtyord, 
			ordcnt12 = ordcnt12 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 13
			UPDATE ItemHistory SET cps13 = cps13 + @DxDd_cps, qtyord13 = qtyord13 + @DxDd_qtyord, 
			ordcnt13 = ordcnt13 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 14 
			UPDATE ItemHistory SET cps14 = cps14 + @DxDd_cps, qtyord14 = qtyord14 + @DxDd_qtyord, 
			ordcnt14 = ordcnt14 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 15
			UPDATE ItemHistory SET cps15 = cps15 + @DxDd_cps, qtyord15 = qtyord15 + @DxDd_qtyord, 
			ordcnt15 = ordcnt15 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 16
			UPDATE ItemHistory SET cps16 = cps16 + @DxDd_cps, qtyord16 = qtyord16 + @DxDd_qtyord, 
			ordcnt16 = ordcnt16 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 17
			UPDATE ItemHistory SET cps17 = cps17 + @DxDd_cps, qtyord17 = qtyord17 + @DxDd_qtyord, 
			ordcnt17 = ordcnt17 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 18 
			UPDATE ItemHistory SET cps18 = cps18 + @DxDd_cps, qtyord18 = qtyord18 + @DxDd_qtyord, 
			ordcnt18 = ordcnt18 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 19 
			UPDATE ItemHistory SET cps19 = cps19 + @DxDd_cps, qtyord19 = qtyord19 + @DxDd_qtyord, 
			ordcnt19 = ordcnt19 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 20
			UPDATE ItemHistory SET cps20 = cps20 + @DxDd_cps, qtyord20 = qtyord20 + @DxDd_qtyord, 
			ordcnt20 = ordcnt20 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 21 
			UPDATE ItemHistory SET cps21 = cps21 + @DxDd_cps, qtyord21 = qtyord21 + @DxDd_qtyord, 
			ordcnt21 = ordcnt21 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 22 
			UPDATE ItemHistory SET cps22 = cps22 + @DxDd_cps, qtyord22 = qtyord22 + @DxDd_qtyord, 
			ordcnt22 = ordcnt22 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 23 
			UPDATE ItemHistory SET cps23 = cps23 + @DxDd_cps, qtyord23 = qtyord23 + @DxDd_qtyord, 
			ordcnt23 = ordcnt23 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 24 
			UPDATE ItemHistory SET cps24 = cps24 + @DxDd_cps, qtyord24 = qtyord24 + @DxDd_qtyord, 
			ordcnt24 = ordcnt24 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 25
			UPDATE ItemHistory SET cps25 = cps25 + @DxDd_cps, qtyord25 = qtyord25 + @DxDd_qtyord, 
			ordcnt25 = ordcnt25 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 26
			UPDATE ItemHistory SET cps26 = cps26 + @DxDd_cps, qtyord26 = qtyord26 + @DxDd_qtyord, 
			ordcnt26 = ordcnt26 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 27 
			UPDATE ItemHistory SET cps27 = cps27 + @DxDd_cps, qtyord27 = qtyord27 + @DxDd_qtyord, 
			ordcnt27 = ordcnt27 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 28 
			UPDATE ItemHistory SET cps28 = cps28 + @DxDd_cps, qtyord28 = qtyord28 + @DxDd_qtyord, 
			ordcnt28 = ordcnt28 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 29 
			UPDATE ItemHistory SET cps29 = cps29 + @DxDd_cps, qtyord29 = qtyord29 + @DxDd_qtyord, 
			ordcnt29 = ordcnt29 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 30
			UPDATE ItemHistory SET cps30 = cps30 + @DxDd_cps, qtyord30 = qtyord30 + @DxDd_qtyord, 
			ordcnt30 = ordcnt30 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 31 
			UPDATE ItemHistory SET cps31 = cps31 + @DxDd_cps, qtyord31 = qtyord31 + @DxDd_qtyord, 
			ordcnt31 = ordcnt31 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 32 
			UPDATE ItemHistory SET cps32 = cps32 + @DxDd_cps, qtyord32 = qtyord32 + @DxDd_qtyord, 
			ordcnt32 = ordcnt32 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 33 
			UPDATE ItemHistory SET cps33 = cps33 + @DxDd_cps, qtyord33 = qtyord33 + @DxDd_qtyord, 
			ordcnt33 = ordcnt33 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 34 
			UPDATE ItemHistory SET cps34 = cps34 + @DxDd_cps, qtyord34 = qtyord34 + @DxDd_qtyord, 
			ordcnt34 = ordcnt34 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 35
			UPDATE ItemHistory SET cps35 = cps35 + @DxDd_cps, qtyord35 = qtyord35 + @DxDd_qtyord, 
			ordcnt35 = ordcnt35 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 36
			UPDATE ItemHistory SET cps36 = cps36 + @DxDd_cps, qtyord36 = qtyord36 + @DxDd_qtyord, 
			ordcnt36 = ordcnt33 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 37 
			UPDATE ItemHistory SET cps37 = cps37 + @DxDd_cps, qtyord37 = qtyord37 + @DxDd_qtyord, 
			ordcnt37 = ordcnt37 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 38 
			UPDATE ItemHistory SET cps38 = cps38 + @DxDd_cps, qtyord38 = qtyord38 + @DxDd_qtyord, 
			ordcnt38 = ordcnt38 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 39 
			UPDATE ItemHistory SET cps39 = cps39 + @DxDd_cps, qtyord39 = qtyord39 + @DxDd_qtyord, 
			ordcnt39 = ordcnt39 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			IF @pd = 40
			UPDATE ItemHistory SET cps40 = cps40 + @DxDd_cps, qtyord40 = qtyord40 + @DxDd_qtyord, 
			ordcnt40 = ordcnt40 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 41 
			UPDATE ItemHistory SET cps41 = cps41 + @DxDd_cps, qtyord41 = qtyord41 + @DxDd_qtyord, 
			ordcnt41 = ordcnt41 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 42 
			UPDATE ItemHistory SET cps42 = cps42 + @DxDd_cps, qtyord42 = qtyord42 + @DxDd_qtyord, 
			ordcnt42 = ordcnt42 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 43 
			UPDATE ItemHistory SET cps43 = cps43 + @DxDd_cps, qtyord43 = qtyord43 + @DxDd_qtyord, 
			ordcnt43 = ordcnt43 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 44 
			UPDATE ItemHistory SET cps44 = cps44 + @DxDd_cps, qtyord44 = qtyord44 + @DxDd_qtyord, 
			ordcnt44 = ordcnt44 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 45
			UPDATE ItemHistory SET cps45 = cps45 + @DxDd_cps, qtyord45 = qtyord45 + @DxDd_qtyord, 
			ordcnt45 = ordcnt45 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 46
			UPDATE ItemHistory SET cps46 = cps46 + @DxDd_cps, qtyord46 = qtyord46 + @DxDd_qtyord, 
			ordcnt46 = ordcnt46 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 47 
			UPDATE ItemHistory SET cps47 = cps47 + @DxDd_cps, qtyord47 = qtyord47 + @DxDd_qtyord, 
			ordcnt47 = ordcnt47 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 48 
			UPDATE ItemHistory SET cps48 = cps48 + @DxDd_cps, qtyord48 = qtyord48 + @DxDd_qtyord, 
			ordcnt48 = ordcnt48 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 49 
			UPDATE ItemHistory SET cps49 = cps49 + @DxDd_cps, qtyord49 = qtyord49 + @DxDd_qtyord, 
			ordcnt49 = ordcnt49 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 50
			UPDATE ItemHistory SET cps50 = cps50 + @DxDd_cps, qtyord50 = qtyord50 + @DxDd_qtyord, 
			ordcnt50 = ordcnt50 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 51 
			UPDATE ItemHistory SET cps51 = cps51 + @DxDd_cps, qtyord51 = qtyord51 + @DxDd_qtyord, 
			ordcnt51 = ordcnt51 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
			
			IF @pd = 52 
			UPDATE ItemHistory SET cps52 = cps52 + @DxDd_cps, qtyord52 = qtyord52 + @DxDd_qtyord, 
			ordcnt52 = ordcnt52 + @DxDd_ordcnt
			WHERE lcid = @DxDd_lcid AND item = @DxDd_item AND hisyear = @hy
	
			SET @UpdateCounter = @UpdateCounter + 1
		END
	END

	-- Deallocate the cursor
	CLOSE DxDd_Cursor
	DEALLOCATE DxDd_Cursor
	
	-- Save the current DxDd transactions to the appropriate Sales Detail Tables
	-- populate item for subsitem field 
	IF @Retain_Sd_Opt = 1 or @Calc_Perform = 1
	BEGIN
		INSERT INTO AIMSd
		SELECT
			seqnbr,	ordnbr = ltrim(ordnbr), doctype, prmprog, linenbr,
			cusnbr = case charindex('.', cusnbr)
				WHEN 0 THEN cusnbr
				ELSE left(cusnbr, charindex('.', cusnbr) - 1)  
				END, 
			shipto = case charindex('.', cusnbr)
				WHEN 0 THEN '00'
				ELSE substring(cusnbr, (charindex('.', cusnbr) + 1), 2) 
				END,
			lcid, item,item, ordqty, shpqty, uom, extcost, extprice, reason_code,
			CONVERT(smalldatetime, txndate + ' ' + timeofday), txn_type, source, shipping, handling, 
			wk_start = CASE 
				WHEN DATEDIFF(dd, @begofyr, txndate) >= 0 
				THEN DATEADD(dd, -1 * (DATEDIFF(dd, @begofyr, txndate) % 7), txndate)
				ELSE DATEADD(dd, -1 * (DATEDIFF(dd, (DATEADD(yy, -1, @begofyr)), txndate) % 7), txndate)
				END
		FROM AIMDxDd

	END
	-- End of Transaction Set
	COMMIT TRANSACTION
	-- Delete records from the DxDd Table
	TRUNCATE TABLE AIMDxDd
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxDd

	UPDATE STATISTICS AIMSd WITH RESAMPLE, ALL
	UPDATE STATISTICS ItemHistory WITH RESAMPLE, ALL
		
	SELECT @RowCounter AS 'Rows'
		, @InsertCounter AS 'Inserts' 
		, @UpdateCounter AS 'Updates' 
	
	RETURN 1	-- Succeed

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

