SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BuyerStatus_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BuyerStatus_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SH<PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BuyerStatus_Sp
**	Desc: Retrieves Buyer Status from the PODetail table.
**
**	Returns: 
**
**	Values:   
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE PROCEDURE AIM_BuyerStatus_Sp

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
    	
SELECT 'Buyer Id' = PODetail.ById,
      'Buyer Name' = AIMUsers.UserName,
      'Total Lines' = COUNT(*),
      'Complete Lines' = sum(case AIMPo.POStatus
WHEN 'C' THEN 1
ELSE 0
END),
      'Planned Lines' = sum(case AIMPo.POStatus 
WHEN 'P' THEN 1
ELSE 0
END),
      'Released Lines' = sum(case AIMPo.POStatus 
WHEN 'R' THEN 1
ELSE 0
END),
      'Total LT Excpts' = sum(case PODetail.POLineType
WHEN 10 THEN 1
ELSE 0
END),
      'Complete LT Excpts' = sum(case
WHEN PODetail.POLineType = 10 and AIMPo.POStatus = 'C' THEN 1
ELSE 0
END),
      'Planned LT Excpts' = sum(case
WHEN PODetail.POLineType = 10 and AIMPo.POStatus = 'P' THEN 1
ELSE 0
END),
      'Released LT Excpts' = sum(case
WHEN PODetail.POLineType = 10 and AIMPo.POStatus = 'R' THEN 1
ELSE 0
END),
      'Total Line Reviews' = sum(case PODetail.POLineType
WHEN 20 THEN 1
ELSE 0
END), 
      'Complete Line Reviews' = sum(case
WHEN PODetail.POLineType = 20 and AIMPo.POStatus = 'C' THEN 1
ELSE 0
END),    
      'Planned Line Reviews' = sum(case
WHEN PODetail.POLineType = 20 and AIMPO.PoStatus = 'P' THEN 1
ELSE 0
END),    
      'Released Line Reviews' = sum(case
WHEN PODetail.POLineType = 20 and AIMPO.PoStatus = 'R' THEN 1
ELSE 0
END),    
      'Total Priority Excepts' = sum(case PODetail.POLineType
WHEN 30 THEN 1
ELSE 0
END),
      'Complete Priority Excepts' = sum(case
WHEN PODetail.POLineType = 30 and AIMPO.PoStatus = 'C' THEN 1
ELSE 0
END),
      'Planned Priority Excepts' = sum(case
WHEN PODetail.POLineType = 30 and AIMPO.PoStatus = 'P' THEN 1
ELSE 0
END),
      'Released Priority Excepts' = sum(case
WHEN PODetail.POLineType = 30 and AIMPO.PoStatus = 'R' THEN 1
ELSE 0
END)
FROM PODetail
INNER JOIN AIMPo ON PODetail.ById = AIMPo.ById
AND PODetail.Vnid = AIMPo.VnId
AND PODetail.Assort = AIMPo.Assort
LEFT OUTER JOIN AIMUsers ON PODetail.ById = AIMUsers.UserId
GROUP BY PODetail.ById, AIMUsers.UserName
ORDER BY PODetail.ById

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

