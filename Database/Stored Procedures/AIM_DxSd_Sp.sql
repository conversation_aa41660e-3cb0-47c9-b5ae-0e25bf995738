SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxSD_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxSD_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxSD_Sp
**	Desc: Loads Sales Detail records from the AIM DxSd Table into the 
**		Item History Table.
**
**      Returns: 
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@ReturnCode -- possible values:
**		        1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**                      4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:	Description:
**    ---------- -------------- -----------------------------------------------
**    2003/05/28 A. Stocksdale	Replaced bulk insert and validation commands 
**				with call to AIM_DxBulkInsert_Sp - common to all
**                              Dx processes.
**    2003/09/15 Srinivas U     Modified code for SubsItem
**    2004/01/06 Srinivas U     Modified to fix issue with Inserts
**    2004/01/09 Srinivas U     Removed else clause
**    2005/03/08 Srinivas U	 Movied truncate and delete outside the commit
**				 Added truncate and delete before the bulk insert
*******************************************************************************/

CREATE PROCEDURE AIM_DxSD_Sp 
(
	@FileName nvarchar(255) = 'All',
	@RowCounter int OUTPUT,
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE 
		--Misc Values
		@begofyr					datetime,		-- BEGINning of Year
		@cmd						nvarchar(255),		-- Command String (T-SQL)
		@dxpath						nvarchar(255),		-- Data Exchange Path
		@hy						smallint,		-- Current History Year
		@itemfound					int,			-- Item Found Switch
		@pd						tinyint,		-- Current Period
		@RtnCode                			int,		        -- Return Code
		@targetdate					datetime,		-- Target Date
		@yearsdIFf					int,			-- Years DIFference
		-- Stock Detail Summary Record
		@dxsd_lcid					nvarchar(12),
		@dxsd_item					nvarchar(25),
		@dxsd_subsitem					nvarchar(25),                          
		@dxsd_txndate					datetime,
		@dxsd_cps					int,
		@dxsd_qtyord					int,
		@dxsd_ordcnt					int,
		@hs_lcid					nvarchar(12),
		@hs_item					nvarchar(25),
		@hs_subsitem					nvarchar(25),
		@hs_hisyear					smallint,
		-- SysCtrl Table
		@Retain_Sd_Opt					tinyint,		-- Retain SD Option from SysCtrl Table
		@Calc_Perform					tinyint,		-- Calculate Performance from SysCtrl Table
		@Unicode_Option					nvarchar(10)		-- Retain Unicode Option from the SysCtrl Table
		
	-- Set the Nocount option on
	SET NOCOUNT ON
    
	--Initialize counters
	SELECT @InsertCounter= 0, 
			@UpdateCounter = 0,
			@RowCounter = 0
	-- Delete records from the DXSD Table
	Truncate table AIMDxSd
	
	-- Just in case the operator doesn't have truncate priviledges
	Delete FROM AIMDxSd
	-- This is a data interface transaction <Transaction Code=SD>. 
	-- Bulk Load the <Transaction Table=AIMDxSD> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'SD', 'AIMDxSD', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
        SELECT 'Rows' = 0, 
			'Inserts' = 0, 
			'Updates' = 0
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- Set the sales detail save and calc perform options from the SysCtrl Table
	SELECT @Retain_Sd_Opt = Retain_Sd_Opt, @Calc_Perform = Calc_Perform 
	    	FROM SysCtrl
	-- CREATE cursor to process dxsd
	DECLARE DxSd_Cursor CURSOR LOCAL FAST_FORWARD FOR 
	        SELECT lcid, item,subsitem =CASE WHEN (SubsItem ='')THEN item  ElSE  SubsItem END, 
			txndate = CONVERT(datetime,txndate), cps = SUM(shpqty), qtyord = sum(ordqty),
	        	ordcnt = COUNT(OrdNbr)
	    	FROM AIMDxSd  
	     	WHERE doctype = 'SO' and txn_type IN ('IR', 'IA')
	    	GROUP BY lcid, item,subsitem, txndate
	        FOR READ ONLY
	OPEN DxSd_Cursor
	
	-- Check for no rows to process
	IF @@CURSOR_ROWS = 0 
	BEGIN
        CLOSE DxSd_Cursor
        DEALLOCATE DxSd_Cursor
        RETURN
	END

	-- Initialize counters and the previous location variable
	SELECT @InsertCounter = 0, @UpdateCounter = 0, @RowCounter = 0
	
	-- BEGIN Transaction Set -- Failure will rollback dxsd set
	BEGIN TRANSACTION
	WHILE 1 = 1
	BEGIN
        -- Get the next row from the cursor
        FETCH NEXT FROM DxSd_Cursor INTO 
    		@dxsd_lcid, @dxsd_item,@dxsd_subsitem, @dxsd_txndate, @dxsd_cps, @dxsd_qtyord, @dxsd_ordcnt
	--If SubsItem is null set it to Item value
	Select @Dxsd_SubsItem = isnull(@Dxsd_SubsItem,@Dxsd_Item)
	-- We do not want to update qtyord and ordcnt in item history table for subs item because we are not
	-- ordering subitem sri sep-16-03
	IF @dxsd_item <> @dxsd_subsitem 
	   BEGIN
	   	set @dxsd_qtyord =0
		set @dxsd_ordcnt =0
	   END
	-- Check for end of cursor
        IF @@FETCH_STATUS <> 0
		BEGIN
    		BREAK
		END
    	-- Increment Row Count
        SELECT @RowCounter = @RowCounter + 1	
        -- Determine the current history year and period
        SELECT @targetdate = CONVERT(datetime, @dxsd_txndate)
        execute @pd = AIM_GetPd_Sp @targetdate, @hy output
        
        -- Get the first day of the year
        SELECT @begofyr = FYStartDate FROM AIMYears WHERE FiscalYear = @hy
        -- Check to see IF the history record exists, IF so update the record,
        -- IF not, add the record
	
        SELECT @hs_lcid = lcid, @hs_item = item,@hs_subsitem =subsitem, @hs_hisyear = hisyear 
    	FROM ItemHistory
    	WHERE lcid = @dxsd_lcid
    	AND item = @dxsd_item
	AND subsitem =@dxsd_subsitem
    	AND hisyear = @hy

        SELECT @itemfound = @@RowCount
        -- IF no history record is found, add a new record	
        IF @itemfound = 0		-- New History Record 
        BEGIN
    		--Increment Add Counter
    		SELECT @InsertCounter = @InsertCounter + 1
    		-- Insert the new item into the Item History Table
    		INSERT INTO ItemHistory(LcId, Item,SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, 
			CPS07, CPS08,CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, 				CPS20, CPS21, CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, 				CPS33, CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, 				CPS46, CPS47, CPS48, CPS49, CPS50, CPS51, CPS52, 
			QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, 
    			QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, 
    			QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, 
    			QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, 
    			QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, 
    			QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, 
    			OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, 
    			OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, 
    			OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, 
    			OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, 
    			OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, 
    			OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52) 
    			VALUES (@dxsd_lcid, @dxsd_item, @dxsd_subsitem,@hy, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0, 0, 0, 0, 
    			0, 0, 0, 0, 0, 0)
    			
        END
    	-- Update the History Table 
	BEGIN
    	IF @pd = 1 
    	    UPDATE ItemHistory SET cps01 = cps01 + @dxsd_cps, qtyord01 = qtyord01 + @dxsd_qtyord, 
    			ordcnt01 = ordcnt01 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    			
    	IF @pd = 2  
    	    UPDATE ItemHistory SET cps02 = cps02 + @dxsd_cps, qtyord02 = qtyord02 + @dxsd_qtyord, 
    			ordcnt02 = ordcnt02 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    			
    	IF @pd = 3
    	    UPDATE ItemHistory SET cps03 = cps03 + @dxsd_cps, qtyord03 = qtyord03 + @dxsd_qtyord, 
    			ordcnt03 = ordcnt03 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    
		IF @pd = 4
    	    UPDATE ItemHistory SET cps04 = cps04 + @dxsd_cps, qtyord04 = qtyord04 + @dxsd_qtyord, 
    			ordcnt04 = ordcnt04 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    			
    	IF @pd = 5 
    	    UPDATE ItemHistory SET cps05 = cps05 + @dxsd_cps, qtyord05 = qtyord05 + @dxsd_qtyord, 
    			ordcnt05 = ordcnt05 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    	
    	IF @pd = 6 
 	      UPDATE ItemHistory SET cps06 = cps06 + @dxsd_cps, qtyord06 = qtyord06 + @dxsd_qtyord, 
    			ordcnt06 = ordcnt06 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
        
       	IF @pd = 7 
    	    UPDATE ItemHistory SET cps07 = cps07 + @dxsd_cps, qtyord07 = qtyord07 + @dxsd_qtyord, 
    			ordcnt07 = ordcnt07 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 8 
    	    UPDATE ItemHistory SET cps08 = cps08 + @dxsd_cps, qtyord08 = qtyord08 + @dxsd_qtyord, 
    			ordcnt08 = ordcnt08 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    	
		IF @pd = 9 
    	    UPDATE ItemHistory SET cps09 = cps09 + @dxsd_cps, qtyord09 = qtyord09 + @dxsd_qtyord, 
    			ordcnt09 = ordcnt09 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
		IF @pd = 10 
    	    UPDATE ItemHistory SET cps10 = cps10 + @dxsd_cps, qtyord10 = qtyord10 + @dxsd_qtyord, 
    			ordcnt10 = ordcnt10 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item  AND subsitem =@dxsd_subsitem AND hisyear = @hy
    	
    	IF @pd = 11 
    	    UPDATE ItemHistory SET cps11 = cps11 + @dxsd_cps, qtyord11 = qtyord11 + @dxsd_qtyord, 
    			ordcnt11 = ordcnt11 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 12 
    	    UPDATE ItemHistory SET cps12 = cps12 + @dxsd_cps, qtyord12 = qtyord12 + @dxsd_qtyord, 
    			ordcnt12 = ordcnt12 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 13
    	    UPDATE ItemHistory SET cps13 = cps13 + @dxsd_cps, qtyord13 = qtyord13 + @dxsd_qtyord, 
    			ordcnt13 = ordcnt13 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 14 
    	    UPDATE ItemHistory SET cps14 = cps14 + @dxsd_cps, qtyord14 = qtyord14 + @dxsd_qtyord, 
    			ordcnt14 = ordcnt14 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 15
    	    UPDATE ItemHistory SET cps15 = cps15 + @dxsd_cps, qtyord15 = qtyord15 + @dxsd_qtyord, 
    			ordcnt15 = ordcnt15 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 16
    	    UPDATE ItemHistory SET cps16 = cps16 + @dxsd_cps, qtyord16 = qtyord16 + @dxsd_qtyord, 
    			ordcnt16 = ordcnt16 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 17
    	    UPDATE ItemHistory SET cps17 = cps17 + @dxsd_cps, qtyord17 = qtyord17 + @dxsd_qtyord, 
    			ordcnt17 = ordcnt17 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 18 
    	    UPDATE ItemHistory SET cps18 = cps18 + @dxsd_cps, qtyord18 = qtyord18 + @dxsd_qtyord, 
    			ordcnt18 = ordcnt18 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 19 
    	    UPDATE ItemHistory SET cps19 = cps19 + @dxsd_cps, qtyord19 = qtyord19 + @dxsd_qtyord, 
    			ordcnt19 = ordcnt19 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 20
    	    UPDATE ItemHistory SET cps20 = cps20 + @dxsd_cps, qtyord20 = qtyord20 + @dxsd_qtyord, 
    			ordcnt20 = ordcnt20 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 21 
    	    UPDATE ItemHistory SET cps21 = cps21 + @dxsd_cps, qtyord21 = qtyord21 + @dxsd_qtyord, 
    			ordcnt21 = ordcnt21 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 22 
    	    UPDATE ItemHistory SET cps22 = cps22 + @dxsd_cps, qtyord22 = qtyord22 + @dxsd_qtyord, 
    			ordcnt22 = ordcnt22 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 23 
    	    UPDATE ItemHistory SET cps23 = cps23 + @dxsd_cps, qtyord23 = qtyord23 + @dxsd_qtyord, 
    			ordcnt23 = ordcnt23 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 24 
    	    UPDATE ItemHistory SET cps24 = cps24 + @dxsd_cps, qtyord24 = qtyord24 + @dxsd_qtyord, 
    			ordcnt24 = ordcnt24 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 25
    	    UPDATE ItemHistory SET cps25 = cps25 + @dxsd_cps, qtyord25 = qtyord25 + @dxsd_qtyord, 
    			ordcnt25 = ordcnt25 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 26
    	    UPDATE ItemHistory SET cps26 = cps26 + @dxsd_cps, qtyord26 = qtyord26 + @dxsd_qtyord, 
    			ordcnt26 = ordcnt26 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 27 
    	    UPDATE ItemHistory SET cps27 = cps27 + @dxsd_cps, qtyord27 = qtyord27 + @dxsd_qtyord, 
    			ordcnt27 = ordcnt27 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
        
        IF @pd = 28 
    	    UPDATE ItemHistory SET cps28 = cps28 + @dxsd_cps, qtyord28 = qtyord28 + @dxsd_qtyord, 
    			ordcnt28 = ordcnt28 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
        	
		IF @pd = 29 
    	    UPDATE ItemHistory SET cps29 = cps29 + @dxsd_cps, qtyord29 = qtyord29 + @dxsd_qtyord, 
    			ordcnt29 = ordcnt29 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
		IF @pd = 30
    	    UPDATE ItemHistory SET cps30 = cps30 + @dxsd_cps, qtyord30 = qtyord30 + @dxsd_qtyord, 
    			ordcnt30 = ordcnt30 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 31 
    	    UPDATE ItemHistory SET cps31 = cps31 + @dxsd_cps, qtyord31 = qtyord31 + @dxsd_qtyord, 
    			ordcnt31 = ordcnt31 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 32 
    	    UPDATE ItemHistory SET cps32 = cps32 + @dxsd_cps, qtyord32 = qtyord32 + @dxsd_qtyord, 
    			ordcnt32 = ordcnt32 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item  AND subsitem =@dxsd_subsitem AND hisyear = @hy
    	
    	IF @pd = 33 
    	    UPDATE ItemHistory SET cps33 = cps33 + @dxsd_cps, qtyord33 = qtyord33 + @dxsd_qtyord, 
    			ordcnt33 = ordcnt33 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 34 
    	    UPDATE ItemHistory SET cps34 = cps34 + @dxsd_cps, qtyord34 = qtyord34 + @dxsd_qtyord, 
    			ordcnt34 = ordcnt34 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 35
    	    UPDATE ItemHistory SET cps35 = cps35 + @dxsd_cps, qtyord35 = qtyord35 + @dxsd_qtyord, 
    			ordcnt35 = ordcnt35 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 36
    	    UPDATE ItemHistory SET cps36 = cps36 + @dxsd_cps, qtyord36 = qtyord36 + @dxsd_qtyord, 
    			ordcnt36 = ordcnt36 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 37 
    	    UPDATE ItemHistory SET cps37 = cps37 + @dxsd_cps, qtyord37 = qtyord37 + @dxsd_qtyord, 
    			ordcnt37 = ordcnt37 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
        
        IF @pd = 38 
    	    UPDATE ItemHistory SET cps38 = cps38 + @dxsd_cps, qtyord38 = qtyord38 + @dxsd_qtyord, 
    			ordcnt38 = ordcnt38 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
        
		IF @pd = 39 
    	    UPDATE ItemHistory SET cps39 = cps39 + @dxsd_cps, qtyord39 = qtyord39 + @dxsd_qtyord, 
    			ordcnt39 = ordcnt39 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
    	
		IF @pd = 40
    	    UPDATE ItemHistory SET cps40 = cps40 + @dxsd_cps, qtyord40 = qtyord40 + @dxsd_qtyord, 
    			ordcnt40 = ordcnt40 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item  AND subsitem =@dxsd_subsitem AND hisyear = @hy
    	
    	IF @pd = 41 
    	    UPDATE ItemHistory SET cps41 = cps41 + @dxsd_cps, qtyord41 = qtyord41 + @dxsd_qtyord, 
    			ordcnt41 = ordcnt41 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 42 
    	    UPDATE ItemHistory SET cps42 = cps42 + @dxsd_cps, qtyord42 = qtyord42 + @dxsd_qtyord, 
    			ordcnt42 = ordcnt42 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 43 
    	    UPDATE ItemHistory SET cps43 = cps43 + @dxsd_cps, qtyord43 = qtyord43 + @dxsd_qtyord, 
    			ordcnt43 = ordcnt43 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 44 
    	    UPDATE ItemHistory SET cps44 = cps44 + @dxsd_cps, qtyord44 = qtyord44 + @dxsd_qtyord, 
    			ordcnt44 = ordcnt44 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 45
    	    UPDATE ItemHistory SET cps45 = cps45 + @dxsd_cps, qtyord45 = qtyord45 + @dxsd_qtyord, 
    			ordcnt45 = ordcnt45 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 46
    	    UPDATE ItemHistory SET cps46 = cps46 + @dxsd_cps, qtyord46 = qtyord46 + @dxsd_qtyord, 
    			ordcnt46 = ordcnt46 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 47 
    	    UPDATE ItemHistory SET cps47 = cps47 + @dxsd_cps, qtyord47 = qtyord47 + @dxsd_qtyord, 
    			ordcnt47 = ordcnt47 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem AND hisyear = @hy
        
        IF @pd = 48 
    	    UPDATE ItemHistory SET cps48 = cps48 + @dxsd_cps, qtyord48 = qtyord48 + @dxsd_qtyord, 
    			ordcnt48 = ordcnt48 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
        	
		IF @pd = 49 
    	    UPDATE ItemHistory SET cps49 = cps49 + @dxsd_cps, qtyord49 = qtyord49 + @dxsd_qtyord, 
    			ordcnt49 = ordcnt49 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	IF @pd = 50
    	    UPDATE ItemHistory SET cps50 = cps50 + @dxsd_cps, qtyord50 = qtyord50 + @dxsd_qtyord, 
    			ordcnt50 = ordcnt50 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 51 
    	    UPDATE ItemHistory SET cps51 = cps51 + @dxsd_cps, qtyord51 = qtyord51 + @dxsd_qtyord, 
    			ordcnt51 = ordcnt51 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item AND subsitem =@dxsd_subsitem  AND hisyear = @hy
    	
    	IF @pd = 52 
    	    UPDATE ItemHistory SET cps52 = cps52 + @dxsd_cps, qtyord52 = qtyord52 + @dxsd_qtyord, 
    			ordcnt52 = ordcnt52 + @dxsd_ordcnt
    			WHERE lcid = @dxsd_lcid AND item = @dxsd_item  AND subsitem =@dxsd_subsitem AND hisyear = @hy    		

	SELECT @UpdateCounter = @UpdateCounter + 1
	END
	END
	
	-- Deallocate the cursor
	CLOSE DxSd_Cursor
	DEALLOCATE DxSd_Cursor
	
	-- Save the current dxsd transactions to the appropriate Sales Detail Tables
	IF @Retain_Sd_Opt = 1 or @Calc_Perform = 1
	BEGIN
	    	INSERT INTO AIMSd
	       	SELECT
	    		seqnbr,	ordnbr = ltrim(ordnbr), doctype, prmprog, linenbr,
	    		cusnbr = case charindex('.', cusnbr)
	    		WHEN 0 THEN cusnbr
	    		ELSE left(cusnbr, charindex('.', cusnbr) - 1)  
	    		END, 
	    		shipto = case charindex('.', cusnbr)
	    		WHEN 0 THEN '00'
	    		ELSE substring(cusnbr, (charindex('.', cusnbr) + 1), 2) 
	    		END,
	    		lcid, item,subsitem =CASE WHEN (SubsItem ='')THEN item  ElSE  SubsItem END, ordqty, shpqty, uom, 		extcost, extprice, reason_code,
	    		CONVERT(smalldatetime, txndate + ' ' + timeofday), txn_type, source, shipping, handling, 
	    		wk_start = CASE 
	    		WHEN DATEDIFF(dd, @begofyr, txndate) >= 0 
	    		THEN DATEADD(dd, -1 * (DATEDIFF(dd, @begofyr, txndate) % 7), txndate)
	    		ELSE DATEADD(dd, -1 * (DATEDIFF(dd, (DATEADD(yy, -1, @begofyr)), txndate) % 7), txndate)
	    		END
	    		FROM AIMDxSd
	END

	

	-- End of Transaction Set
	COMMIT TRANSACTION
	-- Delete records from the DXSD Table
	Truncate table AIMDxSd
	
	-- Just in case the operator doesn't have truncate priviledges
	Delete FROM AIMDxSd
	-- Update the transaction counts; Note that inserts are counted twice, the
	-- record is first inserted and then updated.
	SELECT 'Rows' = @RowCounter, 'Inserts' = @InsertCounter, 
	     'Updates' = (@UpdateCounter - @InsertCounter)
	
	UPDATE STATISTICS AIMSd WITH RESAMPLE, ALL
	UPDATE STATISTICS ItemHistory WITH RESAMPLE, ALL

	RETURN 1	-- Succeed

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

