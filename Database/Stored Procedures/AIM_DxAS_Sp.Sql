  if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxAS_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
  Drop procedure [dbo].[AIM_DxAS_Sp]
  GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxAS_Sp
**	Desc: Loads Alternate Vendor/Source Records into Alternate_Source Table
**
**	Returns: 1) @InsertCounter
**               2) @UpdateCounter
**               3) @FileLog
**              
**	Author: Mohammed
**      Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:      Description:
**    ---------- ------------ -------------------------------------------------
** March/08/05   SUddanti	Movied truncate and delete outside the commit
**				Added truncate and delete before the bulk insert
*******************************************************************************/

CREATE    Procedure AIM_DxAS_Sp
(
         @FileName					nvarchar(255) = 'All',
         @InsertCounter                                       int Output,
         @UpdateCounter                                       int Output,
      	 @FileLog					nvarchar(4000) Output 
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE 
    	-- System Control Table
     	@dxpath						nvarchar(255),
     	@cmd						nvarchar(255),
    	@continue					int,
    	@RtnCode                			int,
     	@Enabled					nvarchar(1),
     	@Item						nvarchar(25),
     	@LcId						nvarchar(12),
     	@ItemKey					nvarchar(25),
     	@LcKey 						nvarchar(12),
     	@VnId						nvarchar(12),
     	@Assort						nvarchar(12),
     	@VnKey						nvarchar(12),
     	@AssortKey					nvarchar(12),
     	@BuyingUOM					nvarchar(6),
     	@PackRounding					nvarchar(1),
     	@LeadTimeSource					nvarchar(1),
    	@LeadTime					smallint,
    	@Vn_LeadTime					smallint,
    	@ConvFactor					smallint,
    	@Cost						decimal(10,4),
	@Unicode_Option					nvarchar(10)
  
  SET NOCOUNT ON
  
  --Initialize counters
  SELECT @InsertCounter= 0, @UpdateCounter= 0
  Select @FileLog = char(13) + char(10)

  -- Check for a duplicate file name
  EXEC @RtnCode = AIM_VerIFyDxFile_Sp 'AS', @FileName
  IF @RtnCode = 0     -- Fail
  BEGIN
        SELECT @InsertCounter=0, @UpdateCounter=0
        RETURN 2        -- Duplicate File Name
  END

  -- Get the Control Variables from the System Control Table
  -- Get the Inbox Path
  SELECT @dxpath = rtrim(dxpath),
         @Unicode_Option = Case UnicodeOption WHEN 'N' THEN 'char'
					      WHEN 'Y' THEN 'widechar' ELSE 'char' END FROM SysCtrl

  -- Append a '\' IF needed
  IF right(@dxpath, 1) <> '\'
        SELECT @dxpath = @dxpath + '\'
    	
  -- Check for an invalid filename
  IF @FileName = ''
        RETURN 0
  ELSE
       SELECT @FileName = @dxpath + @FileName

  -- Delete records from the dxvn Table
  TRUNCATE TABLE AIMDxAS
  -- Just in case the operator doesn't have truncate priviledges
  DELETE AIMDxAS

  -- Bulk Load the DxAS Table
  SELECT @cmd = 'bulk insert AIMDxAS from ' + char(39) + @FileName +  char(39) + ' with (FIELDTERMINATOR = ' + char(39) + '\t' + char(39) + ', DATAFILETYPE = ' + char(39) + 	@Unicode_Option + char(39) + ')'
  EXECUTE (@cmd)

  -- CREATE cursor to process DxAS
  DECLARE DxAS_Cursor CURSOR LOCAL FAST_FORWARD FOR 
        SELECT Enabled, Item = RTrim(Item),  LcId = RTrim(LcId), VnId = RTrim(VnId), Assort = RTrim(Assort), 
               BuyingUOM = RTrim(BuyingUOM), ConvFactor, PackRounding = RTrim(PackRounding), StdCost, Leadtime 
         FROM AIMDxAS

  OPEN DxAS_Cursor
  -- Check for no rows to process
  IF @@CURSOR_ROWS = 0 
        RETURN
  -- Update Loop
alter table Alternate_Source disable trigger trAlternateSourceDel
alter table Alternate_Source disable trigger trAlternateSourceAdd
  BEGIN TRANSACTION
  WHILE 1 = 1 
  BEGIN -- WHILE 1=1 
        FETCH NEXT FROM DxAS_Cursor INTO 
        	@Enabled, @Item, @LcId, @VnId, @Assort, @BuyingUOM, @ConvFactor, @PackRounding, @Cost, @LeadTime

        -- Check Fetch Status
        IF @@FETCH_STATUS <> 0
    		break

        Select @continue = 1

        -- Check if the Location exists
        Select @LcKey=LcId, @LeadTimeSource=LeadTimeSource from AIMLocations Where LcId = @LcId

        IF @@rowcount = 0 
          Begin
           Select @FileLog = @FileLog + 'Invalid Loc: ' + @Lcid + char(13) + char(10)
           select @continue = 0
          End
        Else
          Begin
	        -- Check if the Item exists
        	Select @ItemKey=Item, @LcKey=LcId from Item where Item = @Item and LcId = @LcId
	
        	IF @@rowcount = 0 
	          Begin
        	   Select @FileLog = @FileLog + 'Invalid Item/Loc: ' + @Item + '/' + @LcId + char(13) + char(10)
	           select @continue = 0
        	  End
          End

        -- Check if the Vendor exists
        Select @VnKey=VnId, @AssortKey=Assort, @Vn_LeadTime=Dft_LeadTime from AIMVendors Where VnId = @VnId and Assort = @Assort
        
        IF @@rowcount = 0 
          Begin
           Select @FileLog = @FileLog + 'Invalid Vnd/Assort: ' + @VnId + '/' + @Assort + char(13) + char(10)
           select @continue = 0    
          End

        IF @continue = 1
          Begin --IF @continue = 1
               --- LeadTime Rules:
                 -- IF LeadTime Source (On the Loc) = S or L --> Use the LeadTime value supplied in the file
                 -- IF LeadTime Source (On the Loc) = V      --> Override the supplied value with the one in AIMVendors Table
                IF @LeadTimeSource = 'V'
                    Select @LeadTime = @Vn_LeadTime

		-- Check to see IF the row already exists in the Alternate_Source Table
        	SELECT @VnKey=VnId, @AssortKey=Assort From Alternate_Source WHERE vnid = @VnId and assort = @Assort and Item = @Item and LcId = @LcId
                
        	-- New Alternate Vendor Row  
	        IF @@rowcount = 0		
        	   BEGIN
    			-- Insert the new row into the Alternate_Source Table
	    		INSERT INTO Alternate_Source VALUES 
        	        (@Enabled, @Item, @LcId, @VnId, @Assort, @BuyingUOM, @ConvFactor, @PackRounding, @Cost, @LeadTime)
     			--Increment Add Counter
	    		SELECT @InsertCounter = @InsertCounter + 1
	           END
        	Else
	          BEGIN   -- Existing Alternate_Source
                    -- The Interface should not disable Vn/Assort if there are PO Lines against it.
        	    IF @Enabled = '0'   	
                      Begin    --- IF @Enabled = '0' 
                         
                         Select * From PODetail Where PODetail.Item = @Item AND LcId = @LcId AND VSOQ > 0 
                            AND OrdStatus in ('P','R') AND VnId = @VnKey AND Assort = @Assort
                      
                         IF @@Rowcount > 0  
                            Begin
                              Select @FileLog = @FileLog + 'Cannot disable ' + @Item + '/' + @LcId + '/' + @VnId + '/' + @Assort + ' (Pending PO) ' + char(13) + char(10) 
                              Select @Continue = 0
                            End
                       End    --- IF @Enabled = '0' 
        	    IF @continue=1   	
                          Begin -- @continue=1   	                     
                             -- Update the Alternate_Source Table
    			     UPDATE Alternate_Source SET 
	                            BuyingUOM  = @BuyingUOM, ConvFactor = @ConvFactor, PackRounding = @PackRounding, 
        	                    StdCost = @Cost, LeadTime = @LeadTime, Enabled = @Enabled
	    		       WHERE VnId = @VnId and Assort = @Assort and Item = @Item and LcId = @LcId
    			
                              -- Increment Update Count	
    			      IF @@Rowcount <> 0 
	    	  	          SELECT @UpdateCounter = @UpdateCounter + 1
                          End --@continue=1   	 
	          END  -- Existing Alternate_Source
          END --- IF @continue = 1   
  END -- WHILE 1 = 1 		

  -- Deallocate the cursor
  CLOSE DxAS_Cursor
  DEALLOCATE DxAS_Cursor

--Simulate the effect of the trigger
UPDATE ITEM SET AltVnFlag = 'N'
FROM Item I, Alternate_Source A
 WHERE I.Item = A.Item  
 AND I.LcId = A.LcId
 AND A.Enabled ='0' 

UPDATE ITEM SET AltVnFlag = 'Y' 
FROM Item I, Alternate_Source A
WHERE I.Item = A.Item  
AND I.LcId = A.LcId
AND A.Enabled ='1' 

  COMMIT TRANSACTION
-- Delete records from the dxvn Table
  TRUNCATE TABLE AIMDxAS
 -- Just in case the operator doesn't have truncate priviledges
  DELETE AIMDxAS
alter table Alternate_Source enable trigger trAlternateSourceDel
alter table Alternate_Source enable trigger trAlternateSourceAdd
  RETURN 1	-- Succeed



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


