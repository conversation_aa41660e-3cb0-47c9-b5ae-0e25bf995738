if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstAccess_Update_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstAccess_Update_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


/******************************************************************************
**	Name: AIM_DmdPlanFcstAccess_Update_Sp
**	Desc: Updates AIMFcstAccess for the given FcstID, based on the FcstLocked.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalak<PERSON><PERSON> Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstAccess_Update_Sp
(
	@FcstSetupKey as int,
	@UserID as nvarchar (12),
	@AccessCode as tinyint,
	@Save as bit = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat int
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL 
	OR @UserID IS NULL
	BEGIN
	  	RETURN -1
	END

	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	
	-- Update if not locked and has access
	-- IF @AccessCode >= 2
	-- BEGIN
		SELECT @Return_Stat = COUNT(*) 
		FROM AIMFcstAccess
		WHERE 
			AIMFcstAccess.FcstSetupKey = @FcstSetupKey AND
			AIMFcstAccess.UserID = @UserID AND
			AIMFcstAccess.AccessCode = @AccessCode
 		IF @Return_Stat = 0
		BEGIN
			IF @Save = 1
			BEGIN
				INSERT INTO AIMFcstAccess (
					FcstSetupKey,
					UserID, 
					AccessCode
				) VALUES (
					@FcstSetupKey,
					@UserID, 
					@AccessCode			
				)
			END
			ELSE
			BEGIN
				DELETE FROM AIMFcstAccess
				WHERE 
					AIMFcstAccess.FcstSetupKey = @FcstSetupKey AND
					AIMFcstAccess.UserID = @UserID AND
					AIMFcstAccess.AccessCode = @AccessCode
			END
		END
		SET @Return_Stat = @@ROWCOUNT

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

