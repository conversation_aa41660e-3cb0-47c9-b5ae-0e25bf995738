SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraintDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraintDetail_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**
**	Name: 	AIM_ProductionConstraintDetail_Save_Sp
**	Desc: 	Inserts/Updates Production Constraint Detail data
**		Derived from AIM_ForecastMainUserAccess_Save_Sp
**
**	Parameters:
**		@ConstraintID	alphanumeric (link to uniquer record in header table)
**		@Item		alphanumeric (required for saving)
**		@MinUnits	numeric (required for saving)
**		@MaxUnits	numeric (required for saving)
**
**	Returns: 
**		 0 --> Successful Save
**		-1 --> No Data Found
**		-2 --> SQL Error
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2002/12/09
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:		 Description:
**	---------- 	------------ --------------------------------------------------
*******************************************************************************/

CREATE  PROCEDURE AIM_ProductionConstraintDetail_Save_Sp
(
	@ConstraintID nvarchar(30),
	@Item nvarchar(25),
	@MinUnits decimal(12,2),
	@MaxUnits decimal(12,2)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	DECLARE @Found int,
		@ExistingConstraintID nvarchar(30)
	
	SET NOCOUNT ON

	-- Validate the required parameters.
	IF @ConstraintID IS NULL
	Or @Item IS NULL
	Or @MinUnits IS NULL
	Or @MaxUnits IS NULL
	BEGIN
		RETURN -1
	END
  	
	UPDATE AIMProductionConstraintDetail
	SET ConstraintID = @ConstraintID, 
		Item = @Item, 
		MinUnits = @MinUnits, 
		MaxUnits = @MaxUnits
	WHERE AIMProductionConstraintDetail.ConstraintID = @ConstraintID

	SELECT @Found = @@ROWCOUNT
	
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	IF @Found = 0 		-- Insert new Record
	BEGIN
	    	INSERT INTO AIMProductionConstraintDetail
		(
			ConstraintID, 
			Item, 
			MinUnits, 
			MaxUnits
		) 
		VALUES
		(
			@ConstraintID, 
			@Item, 
			@MinUnits, 
			@MaxUnits
		)
	
	
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
			RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
	
	        RETURN 0 -- Successful
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
