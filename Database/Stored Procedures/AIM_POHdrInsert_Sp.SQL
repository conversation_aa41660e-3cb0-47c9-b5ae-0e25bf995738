SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POHdrInsert_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POHdrInsert_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_POHdrInsert_Sp
**	Desc: Updates or Creates a PO Header record.
**
**	Returns: 1) @@rowcount - Can be Zero
**               2) 2 - Update
**               3) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_POHdrInsert_Sp
(
  	@ById						nvarchar(12),
     	@VnId					        nvarchar(12),
   	@Assort						nvarchar(12),
   	@POStatus					nvarchar(1),
        @TransmitPO				        tinyint,
   	@ShipIns					varchar(20),
   	@UserInitials				        nvarchar(3),
   	@Remarks1					nvarchar(50),
   	@Remarks2					nvarchar(50),
   	@Remarks3					nvarchar(50),
   	@AddrOverride			                nvarchar(1),
   	@VName						nvarchar(30),
   	@VAddress1					nvarchar(30),
   	@VAddress2					nvarchar(30),
   	@VCity						nvarchar(20),
   	@VState						nvarchar(10),
   	@VZip						nvarchar(10),
        @LineCount				        int,
        @PosLineCount			                int,
        @Vn_Min					        decimal(9,2),
        @Vn_Best				        decimal(9,2),
   	@Reach_Code					nvarchar(1),
   	@POSource					nvarchar(1),
  	@POByZone					nvarchar(1),
        @Dft_LeadTime				        smallint,
        @TotalCost				        decimal(10,2)
  )

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      	int
  
SET NOCOUNT ON 

-- Validate the required parameters.
IF @VnId IS NULL
BEGIN
	RETURN 0
END

IF @ById IS NULL
BEGIN
	RETURN 0
END

IF @Assort IS NULL
BEGIN
	RETURN 0
END

IF EXISTS (SELECT VnId, Assort 
    FROM AIMPO 
    WHERE ById = @ById 
    AND VnId = @VnId
    AND Assort = @Assort)
    
	SELECT @found = 1
  ELSE
    	SELECT @found = 0

IF @found = 0		-- Insert new header
BEGIN
    	INSERT INTO AIMPo
    		(ById, VnId, Assort, POStatus, TransmitPO, ShipIns, UserInitials, 
    		Remarks1, Remarks2, Remarks3, AddrOverride, VName, VAddress1, 
    		VAddress2, VCity, VState, VZip, LineCount, PosLineCount, Vn_Min, Vn_Best, 
    		Reach_Code, POSource, POByZone, Dft_LeadTime, TotalCost)
  	VALUES(@ById, @VnId, @Assort, @POStatus, @TransmitPO, @ShipIns, 
    		@UserInitials, @Remarks1, @Remarks2, @Remarks3, @AddrOverride,
    		@VName, @VAddress1, @VAddress2, @VCity, @VState, @VZip, @LineCount,
    		@PosLineCount, @Vn_Min, @Vn_Best, @Reach_Code, @POSource, @POByZone, 
    		@Dft_LeadTime, @TotalCost)
       
         RETURN @@rowcount
END
ELSE
BEGIN
     	UPDATE AIMPo
    	SET ById = @ById, POStatus = @POStatus, TransmitPO = @TransmitPO, 
    		ShipIns = @ShipIns, UserInitials = @UserInitials, Remarks1 = @Remarks1, 
    		Remarks2 = @Remarks2, Remarks3 = @Remarks3, AddrOverride = @AddrOverride, 
    		VName = @VName, VAddress1 = @VAddress1, VAddress2 = @VAddress2, VCity = @VCity, 
    		VState = @VState, VZip = @VZip, LineCount = @LineCount, PosLineCount = @PosLineCount,
    		Vn_Min = @Vn_Min, Vn_Best = @Vn_Best, Reach_Code = @Reach_Code,
    		POSource = @POSource, POByZone = @POByZone, Dft_LeadTime = @Dft_LeadTime,
    		TotalCost = @TotalCost
  	WHERE ById = @ById and VnId = @VnId and Assort = @Assort
  
        IF @@rowcount > 0
  	BEGIN
        	RETURN 2			-- UPDATE
        END
	ELSE
    	BEGIN
		RETURN 0			-- ERROR
	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

