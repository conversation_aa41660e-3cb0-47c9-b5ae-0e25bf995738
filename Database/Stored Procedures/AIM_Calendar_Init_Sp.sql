SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Calendar_Init_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Calendar_Init_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Calendar_Init_Sp
**	Desc: Initialize the AIMCalendar table.
**
**	Returns: 
**
**	Values:   
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE PROCEDURE AIM_Calendar_Init_Sp
(
	@FirstFiscalYear int,
	@FYStartDate datetime,
	@NbrFiscalYears int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN  

	SET NOCOUNT ON
	
	DECLARE @FYCounter int
	DECLARE @EndDate datetime

	-- Iniitialize the FY Counter
	SET @FYCounter = 1
	
	-- Delete any outlier dates
	SET @EndDate = DateAdd(yyyy, @NbrFiscalYears, @FYStartDate) - 1
	  
	DELETE AIMYears
	
	DELETE AIMDays 
	WHERE FYDate > @EndDate 
	OR FYDate < @FYStartDate
	  
	WHILE @FYCounter <=  @NbrFiscalYears
	BEGIN
	        -- Build the Fiscal Year Table
	        SELECT @EndDate = dateadd(yyyy, 1, @FYStartDate) - 1
	        EXEC AIM_AIMYears_Insert_Sp @FirstFiscalYear, @FYStartDate, @EndDate, 52
	        
		-- Update parameters
	        SELECT @FirstFiscalYear = @FirstFiscalYear + 1, 
	            @FYStartDate = dateadd(yyyy, 1, @FYStartDate)    
	        
		-- Increment the FY Counter
	        SELECT @FYCounter = @FYCounter + 1
	END
	  
	UPDATE STATISTICS AIMYears WITH RESAMPLE, ALL
	UPDATE STATISTICS AIMDays WITH RESAMPLE, ALL
	UPDATE STATISTICS AIMWorkingDays WITH RESAMPLE, ALL

	RETURN

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

