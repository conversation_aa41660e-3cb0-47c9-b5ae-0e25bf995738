SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_CodeLookup_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_CodeLookup_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_CodeLookup_Get_Sp
**	Desc: Gets the Data for a CodeType and Language ID
**
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Recordset - CodeId, CodeDesc
**              
**	Auth:   Wade Riza 
**	Date:   08/12/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza    Update Return Codes and Error Handling
*******************************************************************************/

CREATE PROCEDURE AIM_CodeLookup_Get_Sp
(
        @CodeType					nvarchar(30),
        @LangID						nvarchar(10)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int,
	@DftLangID					nvarchar(10)

SET NOCOUNT ON

-- Validate the required parameters.
IF @CodeType IS NULL OR @LangID IS NULL
BEGIN
	RETURN -1
END	
ELSE
BEGIN
	-- Make sure the title is valid.
	IF (SELECT COUNT(*) FROM AIMCodeLookUp
	WHERE CodeType = @CodeType) = 0
	
	RETURN -1
END

SELECT CodeID, CodeDesc FROM AIMCodeLookUp
WHERE CodeType = @CodeType and LangID = @LangID

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN @found  -- SUCCESSFUL
END
ELSE
BEGIN	
	SELECT @DftLangID = dft_LangID FROM SYSCTRL

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	
	IF @found < 1 
	BEGIN
		RETURN -1  -- Error (No Default Language in the SysControl Table)
	END
    
	SELECT CodeID, CodeDesc FROM AIMCodeLookUp
	WHERE CodeType = @CodeType and LangID = @DftLangID

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	IF @found > 0
	BEGIN
		RETURN @found  -- SUCCESSFUL
	END
	ELSE 
	BEGIN
		RETURN -1 -- Error (No Data found for the default Language in the CODELOOKUP Table)
	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

