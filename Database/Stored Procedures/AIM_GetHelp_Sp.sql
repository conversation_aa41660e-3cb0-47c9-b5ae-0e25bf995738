SET QUOTED_IDENTIFIER ON 
GO

SET ANSI_NULLS ON 
GO

SET NOCOUNT ON
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AI<PERSON>_GetHelp_SP]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_GetHelp_SP]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetHelp_SP
**	Desc: Retrieves the child path for the HTML help, 
**		based on Form and Control name parameters.
**		1.If Form & Control name available in the Database it will 
**                retrive the path
**		2.Else If Form name available in the Database and control name 
**                as '0' it will retrive the path
**		This will display the Form help
**		3.Else it will Return the Constant Path, which is 
**                '\WebHelp\SSA_AIM_Help.htm'
**	
**              
**	Auth:   Sujit Surve
**	Date:   21st Oct 2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**  2004-12-13	A.Stocksdale	Updated hard-coded filenames for the htm.  
**							THIS SHOULD BE REWORKED TO AVOID ERRORS IN FUTURE.
**  2004-12-13	A.Stocksdale	Previous SourceSafe change-history is irrecoverable 
**				because of binary format.  Re-saved file as plain text.  
*******************************************************************************/

CREATE PROCEDURE AIM_GetHelp_SP
(
	@Form as Varchar(100),
	@Control as Varchar(100),
	@FilePath as Varchar(200) OUTPUT
) 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @@FILEPATH AS VARCHAR(200)

	SELECT @@FILEPATH=FILEPATH FROM AIMHELP WHERE FORMNAME=@Form AND ControlName = @Control    
	IF @@FILEPATH IS NULL
	BEGIN
		SELECT @@FILEPATH=FILEPATH FROM AIMHELP WHERE FORMNAME=@Form AND ControlName = '0' 
		IF @@FILEPATH IS NULL
		BEGIN
			SELECT FilePath='\WebHelp\SSA_DR_Help.htm'
			RETURN
		END
		ELSE
		BEGIN
			SELECT FilePath= @@FILEPATH
			RETURN
		END
	END
	ELSE
	BEGIN
		SELECT FilePath= @@FILEPATH
		RETURN
	END

END
GO

SET QUOTED_IDENTIFIER OFF
GO

SET ANSI_NULLS OFF
GO

SET NOCOUNT OFF
GO
