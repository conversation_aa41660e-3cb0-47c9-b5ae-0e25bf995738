SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
SET NOCOUNT ON
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_VendorDetail_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_VendorDetail_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_VendorDetail_Sp
**	Desc:	Given Buyer ID, Vendor ID, Vendor Assortment and UserID, this 
**		procedure returns PODetail records, assuming:
**		(a) the item's OrderStatus can only be P(Pending) or R(Released)
**		(b) the item's Status record shows it set for Order Generation
**		(c) the PO is not scheduled for a type of "Transfer"
**
**	Returns: 1)  @Found - Can be Zero
**		 2)  0 - No Data Found
**		 3) -1 - Invalid Parameter
**		 4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to 
**                       return the SQL ERROR)
**	Values:	Recordset - PODetail
**              
**	Author:	Randy Sadler
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:			Description:
**	---------- -------------- ---------------------------------------
** 	2003/02/06 A. Stocksdale  Updates re returning exceptions for given 
**                                user (not buyer) + modified return values 
**                                -- added -1 and -2
**	2003/03/25 A. Stocksdale  Modified to support sorting by exceptions 
**                                in the Buyer Review UI.
**				  (1) added parameter UserID - to enable 
**                                    identification of exceptions based on 
**                                    Buyer Alerts
**				  (2) added Select column 'Excpt' for 
**                                    Exceptions. (and related variables)
**	!!! ENSURE THAT All UPDATES TO THE EXCEPTION LOGIC HERE ARE REFLECTED IN 
**                        THE VB CODE [CatchExceptions()] !!!
**				  Changes marked by /* A.S. 2003/03/25 
**                                Buyer Review Exceptions Sorting */
**      2003/06/19 Mohammed       Retrieve two new fields to be displayed on the 
**                                VendorDetail Grid: 
**                                (1) Item.AltVnFlag 
**                                (2) ASOQ: Sum(VSOQ) for ALT Lines, and It should 
**                                    be shown on the Primary Lines only.
**      2003/06/26 Mohammed       Add the POType = 'A' Exception  
**	2004/03/03 S.Uddanti 	  Add DependentFcstDemand field at the end of the select
**	2005/03/04 S.Uddanti	  Add Stkdate field so that burn can be calcuated in the 
**				  Buyer Review Screen  
**	2005/03/21 S.Uddanti      Add Excpt for VendorCost          
*******************************************************************************/

CREATE PROCEDURE AIM_VendorDetail_Sp
(
	@ById	                      nvarchar(12),
 	@VnId	                      nvarchar(12),
 	@Assort	                      nvarchar(12),
	@UserID	                      nvarchar(12) 	/* A.S. 2003/03/25 Buyer Review Exceptions Sorting */
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

-- Declare stored procedure local variables
DECLARE @Found int,
		@DmdUpdExcpts             nvarchar(1),		/* Begin -- A.S. 2003/03/25 Buyer Review Exceptions Sorting */
		@OnPromotionExcpts        nvarchar(1),
		@ExtCostExcpts            nvarchar(1),
		@ExtCostLimit             decimal(10,2),
		@PackSizeExcpts           nvarchar(1),
		@PS_PctRoundLimit         decimal(3,2),
		@PS_ExtCostLimit          decimal(10,2),
		@LastWeekSaleExcpts       nvarchar(1),
		@OverDuePO                nvarchar(1),
		@SafetyStockEroded        nvarchar(1),
		@BackOrdered              nvarchar(1),		/* End -- A.S. 2003/03/25 Buyer Review Exceptions Sorting */
                @ALTSourceExcpts          nvarchar(1),          /*Mohammed - MultiVendor*/  
		@VendorCostExcpts	  nvarchar(1)  
	
	-- Validate the required parameters.
	IF @ById IS NULL
	Or @VnId IS NULL
	Or @Assort IS NULL
	Or @UserID IS NULL	/* A.S. 2003/03/25 Buyer Review Exceptions Sorting */
	BEGIN
		RETURN -1	/* Invalid parameter */
	END
	
	-- Begin -- A.S. 2003/03/25 Buyer Review Exceptions Sorting
	-- Get AIMUser data to identify exceptions with
	SELECT @DmdUpdExcpts = AIMUsers.DmdUpdExcpts, 
		@OnPromotionExcpts = AIMUsers.OnPromotionExcpts, 
		@ExtCostExcpts = AIMUsers.ExtCostExcpts, 
		@ExtCostLimit = AIMUsers.ExtCostLimit, 
		@PackSizeExcpts = AIMUsers.PackSizeExcpts, 
		@PS_PctRoundLimit = AIMUsers.PS_PctRoundLimit, 
		@PS_ExtCostLimit = AIMUsers.PS_ExtCostLimit, 
		@LastWeekSaleExcpts = AIMUsers.LastWeekSaleExcpts, 
		@OverDuePO = AIMUsers.OverDuePO, 
		@SafetyStockEroded = AIMUsers.SafetyStockEroded, 
		@BackOrdered = AIMUsers.BackOrdered,
 	        @ALTSourceExcpts = AIMUsers.ALTSourceExcpts,
		@VendorCostExcpts =AIMUsers.VendorCostExcpts
	FROM AIMUsers
	WHERE AIMUsers.UserID = @UserID
	
	SELECT @Found = @@RowCount
	
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
	END
	ELSE IF @Found <= 0 
	BEGIN	
		RETURN -1 /* ERROR (No Valid data found in Database) */
	END
	-- End -- A.S. 2003/03/25 Buyer Review Exceptions Sorting
	
	-- Get Planned/Released PO Details for vendor/buyer */
	SELECT PODetail.POSeqID, PODetail.POLineType, PODetail.OrdStatus,
		PODetail.RevCycle, PODetail.ById, PODetail.VnId, 
		PODetail.Assort, PODetail.Lcid, PODetail.Item, 
		PODetail.PackRounding, PODetail.RSOQ, PODetail.SOQ, 
		PODetail.VSOQ, PODetail.UOM, PODetail.BuyingUOM, 
		PODetail.ConvFactor, PODetail.IsDate, PODetail.DuDate, 
		PODetail.AvailQty, PODetail.LastWeekSalesFlag, PODetail.ItDesc,
		PODetail.Cost, PODetail.Zone, PODetail.Original_VSOQ,
		ItStat=coalesce(Item.ItStat,'A'), 
		Cube=coalesce(Item.Cube,0), 
		Weight=coalesce(Item.Weight,0), 
		ReviewTime=coalesce(Item.ReviewTime,0), 
		LeadTime=coalesce(Item.LeadTime,0), 
		FcstRT=coalesce(Item.FcstRT,0), 
		FcstLT=coalesce(Item.FcstLT,0), 
		FcstDemand=coalesce(Item.FcstDemand,0),
		MAE=coalesce(Item.MAE,0), 
		OnPromotion=coalesce(Item.OnPromotion, 'N'), 
		SafetyStock=coalesce(Item.SafetyStock,0), 
		IMin=coalesce(Item.IMin,0), 
		IMax=coalesce(Item.IMax, 99999999), 
		VelCode = coalesce(Item.VelCode, 'C'), 
		BuyStrat=coalesce(Item.BuyStrat,'O'), 
		OH=coalesce(Item.OH,0), 
		Oo=coalesce(Item.Oo,0), 
		ComStk = coalesce(Item.ComStk,0), 
		BkOrder=coalesce(Item.BkOrder,0), 
		BkComStk=coalesce(Item.BkComStk,0), 
		DIFlag=coalesce(Item.DIFlag, 'N'),
		DmdFilterFlag=coalesce(Item.DmdFilterFlag,'N'),
		TrkSignalFlag=coalesce(Item.TrkSignalFlag, 'N'), 
		UserDemandFlag=coalesce(Item.UserDemandFlag,'N'), 
		OrderPt=coalesce(Item.OrderPt,0), 
		OrderQty=coalesce(Item.OrderQty,0), 
		FcstUpdCyc=coalesce(Item.FcstUpdCyc,0), 
		Dser=coalesce(Item.Dser,0), 
		CStock=coalesce(Item.Cstock,0), 
		SSadj=coalesce(Item.SSadj,0), 
		BuyerReviewSeq=coalesce(ItStatus.BuyerReviewSeq,0), 
		NextPONbr_1=coalesce(Item.NextPONbr_1,''),
		NextPODate_1=coalesce(Item.NextPODate_1,'01/01/1990'), 
		NextPOQty_1=coalesce(Item.NextPOQty_1,0), 
		NextPONbr_2=coalesce(Item.NextPONbr_2,''), 
		NextPODate_2=coalesce(Item.NextPODate_2,'01/01/1990'), 
		NextPOQty_2=coalesce(Item.NextPOQty_2,0), 
		NextPONbr_3=coalesce(Item.NextPONbr_3,''), 
		NextPODate_3=coalesce(Item.NextPODate_3, '01/01/1990'), 
		NextPOQty_3=coalesce(Item.NextPOQty_3,0), 
		UserRef1=coalesce(Item.UserRef1,''), 
		UserRef2=coalesce(Item.UserRef2,''), 
		UserRef3=coalesce(Item.UserRef3,''), 
		Accum_LT=coalesce(Item.Accum_LT, 0), 
		OUTLFlag = CASE
			WHEN PODetail.AvailQty >= (coalesce(Item.OrderPt,0) + coalesce(Item.OrderQty,0)) 
			THEN 'Y' 
			ELSE 'N' 
			END,
		VendorCostExcpts,
		ExtCost = (PODetail.VSOQ * PODetail.Cost),
		Mape = CASE	
			WHEN coalesce(Item.FcstDemand,0) > 0 
			THEN (coalesce(Item.MAE,0) / coalesce(Item.FcstDemand,1))
			ELSE 0
			END
	-- Begin -- A.S. 2003/03/25 Buyer Review Exceptions Sorting */
	-- !!! ENSURE THAT All UPDATES TO THE EXCEPTION LOGIC HERE ARE REFLECTED IN THE VB CODE [CatchExceptions()] !!! */
	, Excpt = CASE
		WHEN @DmdUpdExcpts = 'Y' And DIFlag = 'Y' Then 1	-- Dying Item.
		WHEN @DmdUpdExcpts = 'Y' And DmdFilterFlag = 'Y' Then 1	-- Demand Filter exception(s).
		WHEN @DmdUpdExcpts = 'Y' And TrkSignalFlag = 'Y' Then 1	-- Tracking Signal bias detected.
		WHEN @DmdUpdExcpts = 'Y' And UserDemandFlag = 'Y' Then 1	-- User override on Forecast Demand.
		
		-- On Promotions Exceptions
		WHEN @OnPromotionExcpts = 'Y' And OnPromotion = 'Y' Then 1	-- Item on Promotion.
		
		-- Extended Cost Exceptions
		WHEN @ExtCostExcpts = 'Y' And (PODetail.VSOQ * PODetail.Cost) >= @ExtCostLimit Then 1	-- Extended Cost exceeds user limit.
		
		-- Packsize round Exceptions
		WHEN @PackSizeExcpts = 'Y' 
		And PODetail.RSOQ < PODetail.ConvFactor 
		And PODetail.VSOQ > 0 
		AND (((PODetail.VSOQ - PODetail.RSOQ)/PODetail.VSOQ) >= @PS_PctRoundLimit 
		And ((PODetail.VSOQ - PODetail.RSOQ) * PODetail.Cost) >= @PS_ExtCostLimit)
		Then 1	-- Packrounding exception.
		
		-- Overdue PO
		WHEN @OverDuePO = 'Y' And Item.NextPOQty_1 > 0 And Item.NextPODate_1 < GetDate() Then 1	-- Overdue Purchase Order.
		
		-- Safety Stock Eroded
		WHEN @SafetyStockEroded = 'Y' And Item.OH <= Item.SafetyStock Then 1	-- Safety Stock eroded.
		
		-- Backordered
		WHEN @Backordered = 'Y' And Item.OH <= 0 Then 1	-- No On-Hand Inventory.
		WHEN @LastWeekSaleExcpts = 'Y' And LastWeekSalesFlag = 'Y' Then 1	-- Item sold last week.
              
                /* Mohamed - MultiVendor - Begin */
                When POType = 'A' and @ALTSourceExcpts = 'Y' Then 1
                /* Mohamed - MultiVendor - End */
                When VendorCostExcpts ='Y' and @VendorCostExcpts ='Y' Then 1
		ELSE 0	-- False
		END,	
	-- End -- A.S. 2003/03/25 Buyer Review Exceptions Sorting */

        -- Mohammed - MultiVendor - Begin
            Item.ALTVnFlag,
            ASOQ = Case PODetail.POType When 'A' Then 0 Else 
                   (Select IsNull(Sum(PD.VSOQ),0) From PODetail PD 
                     Where PODetail.Item = Pd.Item 
                       AND PODetail.LcId = Pd.LcId
                       AND PD.POType = 'A' AND PD.OrdStatus in ('P','R')) End,
        -- Mohammed - End
		DependentFcstDemand =coalesce(Item.DependentFcstdemand,0),
		AIMLocations.StkDate

	FROM PODetail 
	Left OUTER JOIN Item ON PODetail.Lcid = Item.Lcid 
		AND PODetail.Item = Item.Item 
	Left Outer JOIN ItStatus ON Item.Itstat = ItStatus.ItStat 
	Left Outer JOIN  AIMLocations ON PODetail.Lcid =AIMLocations.Lcid
	WHERE PODetail.ById = @ById 
		AND PODetail.VnId = @VnId 
		AND PODetail.Assort = @Assort 
		AND PODetail.OrdStatus IN ('P', 'R') 
		AND IsNull(ItStatus.OrdGen, 'Y') = 'Y'
		AND POType <> 'T'
	ORDER BY ItStatus.BuyerReviewSeq, PODetail.Item, PODetail.LcId
	SELECT @Found = @@RowCount
	-- Check for SQL Server errors. */
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
	END
	ELSE IF @Found <= 0 
	BEGIN	
		RETURN -1 /* ERROR (No Valid data found in Database) */
	END
	ELSE
	BEGIN
		RETURN @Found /* SUCCESSFUL */
	END	
	
	RETURN @@rowcount
END
GO

SET QUOTED_IDENTIFIER OFF 
GO

SET ANSI_NULLS OFF 
GO
