SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SdCopy_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SdCopy_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_SdCopy_Sp
**	Desc: 
**
**	Returns: 
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_SdCopy_Sp  

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @location 						nvarchar(12)

SET NOCOUNT ON

--determine what locations are in the AIM.dbo.sd table
DECLARE sdlcid_cursor INSENSITIVE CURSOR FOR
SELECT DISTINCT lcid 
FROM AIMSd 
ORDER BY lcid
  
OPEN sdlcid_cursor
FETCH NEXT FROM sdlcid_cursor INTO @location
WHILE (@@fetch_status = 0)
BEGIN  --BEGIN sdlcid_cursor loop
      BEGIN TRANSACTION
      INSERT INTO AIMSD.dbo.sd 
      SELECT * 
      FROM AIMSd 
      WHERE lcid = @location
      
      IF @@rowcount <> 0
      DELETE AIMSd 
      WHERE lcid = @location
      
      COMMIT TRANSACTION
      FETCH NEXT FROM sdlcid_cursor INTO @location
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

