SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_GetKey_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: 	AIM_ProductionConstraint_GetKey_Sp
**	Desc: 	Returns AIMProductionConstraint Key
** 		Derived from AIM_Forecast_GetKey_Sp
**	Parameters: 
** 		@ConstraintID -- alphanumeric
** 		@Action -- numeric, could be one of:
** 			0 --> Get Equal
** 			1 --> Get Greater Than
** 			2 --> Get Less Than 
** 			3 --> Get Greater Than or Equal
** 			4 --> Get Less Than or Equal
** 			5 --> Get First
** 			6 --> Get Last
**	Returns: 
**		 0 --> Successful get (includes a recordset AIMProductionConstraint.*)
**		-1 --> No Data Found
**		-2 --> SQL Error
**		-3 --> User Not Permitted to retrieve data
**	Values:  
**		Recordset (AIMProductionConstraint Key)
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2002/12/09
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	   Description:
**	---------- --------------- --------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_ProductionConstraint_GetKey_Sp 
(
	@ConstraintID nvarchar(30) = '', 
	@Action tinyint = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN

	DECLARE @RtnCode int
	
	SET NOCOUNT ON
	SET rowcount 1
	
	-- Validate the required parameters.
	IF @ConstraintID IS NULL
	Or @Action IS NULL
	BEGIN
		RETURN -1
	END
	
	-- Get the key to the ProductionConstraint Record based on the action code
	IF @Action = 0	--> Get Equal
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint 
		WHERE ConstraintID = @ConstraintID
		SELECT @RtnCode = @@ROWCOUNT
	END
	IF @Action = 1	--> Get Greater Than
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint 
		WHERE ConstraintID > @ConstraintID
		SELECT @RtnCode = @@ROWCOUNT
		IF @RtnCode = 0
			SELECT @Action = 6	-- Get Last Record
	END
	IF @Action = 2	--> Get Less Than 
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint 
		WHERE ConstraintID < @ConstraintID
			ORDER BY ConstraintID desc 
		SELECT @RtnCode = @@ROWCOUNT
		IF @RtnCode = 0
			SELECT @Action = 5      -- Get first record
	END
	IF @Action = 3	--> Get Greater Than or Equal
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint 
		WHERE ConstraintID >= @ConstraintID
		SELECT @RtnCode = @@ROWCOUNT
		IF @RtnCode = 0
			SELECT @Action = 6      -- Get last record
	END
	IF @Action = 4	--> Get Less Than or Equal
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint 
		WHERE ConstraintID <= @ConstraintID
			ORDER BY ConstraintID desc 
		SELECT @RtnCode = @@ROWCOUNT
		IF @RtnCode = 0
			SELECT @Action = 5      -- Get first record
	END
	IF @Action = 5	--> Get First
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint  
		SELECT @RtnCode = @@ROWCOUNT
	END
	IF @Action = 6	--> Get Last
	BEGIN
		SELECT @ConstraintID = ConstraintID FROM AIMProductionConstraint  
			ORDER BY ConstraintID desc 
		SELECT @RtnCode = @@ROWCOUNT
	END
	
	IF @RtnCode > 0 
	BEGIN
	    	SELECT * FROM AIMProductionConstraint 
		WHERE ConstraintID = @ConstraintID
		RETURN 0 --> SUCCEED
	END
	ELSE
	BEGIN
	 	SELECT * FROM AIMProductionConstraint 
		WHERE ConstraintID = @ConstraintID
		RETURN -1 --> No data found
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

