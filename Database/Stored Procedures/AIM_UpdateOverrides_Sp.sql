SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UpdateOverrides_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UpdateOverrides_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_UpdateOverrides_Sp
**	Desc: 
**
**	Returns: 1) @@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
         
CREATE PROCEDURE AIM_UpdateOverrides_Sp 
(
	@LcId			    				nvarchar(12),
   	@Item			    				nvarchar(25),
  	@UserFcst		    				decimal(10,2),
  	@UserFcstExpDate					datetime,
  	@CStock			     				int,
   	@ResetOption						nvarchar(1) = 'N'
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

-- Validate the required parameters.
IF @LcId IS NULL
BEGIN
	RETURN 0
END

IF @Item IS NULL
BEGIN
	RETURN 0
END

IF @ResetOPtion = 'N'
BEGIN
  	UPDATE item 
	SET UserFcst = @UserFcst, UserFcstExpDate = @UserFcstExpDate,
    	CStock = @CStock
    	WHERE LcId = @LcId
    	AND Item = @Item

END
ELSE
BEGIN
	-- Reset override values to default values
	UPDATE item
	SET UserFcst = 0, UserFcstExpDate = '01/01/1990',
    	CStock = 0
    	WHERE LcId = @LcId
    	AND Item = @Item
END
RETURN @@rowcount
    
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

