SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxSL_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxSL_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_DxSL_Sp
**	Desc: Loads data exchange item records into the Item Table
**            from the AIMDxSL Table. 
**	NOTE: Stock Light is a subset of the Stock Status interface. Therefore, this 
**		stored procedure has been derived from AIM_DxSS_Sp. 
**		It follows the same operations as the Stock status data interface, 
**		except for the following:
**		*	Stock Light cannot be used for Item Substitution
**		*	Stock Light does not support Vendor/Assortment
**		*	INTERFACE DESIGN DOES NOT INCLUDE UOM
**      *	INTERFACE DESIGN DOES NOT INCLUDE LeadTime
**
**	This is strictly meant to update the inventory information for existing items.
**
**	Returns:
**		@RowCounter -- number of rows updated
**		@InsertCounter -- number of rows inserted
**		@UpdateCounter -- number of rows updated
**		@PurgedCounter -- number of rows updated
**		@ErrorCounter -- number of rows updated
**		@ReturnCode -- possible values:
**		        1)  0 - Successful
**                      2) -1 - No Data Found
**                      3) -2 - SQL Error
**             	        4) -3 - Duplicate File Name
**                      5) -4 - Invalid File Name
**
**	Author:		Annalakshmi Stocksdale
**	Created:	2003/05/16
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Updated by:	Description:
**	---------- ------------	-----------------------------------------------
**	2003/09/29 SRINIVAS U   Insert a record into AIMDataExchangeCtrl
**				to be used for TransferManagement.
**     	2005/03/08 Srinivas U	Added truncate and delete before the bulk insert						
*******************************************************************************/
 
CREATE PROCEDURE AIM_DxSL_Sp
(
  	@FileName nvarchar(255) = 'All',
	@RowCounter int OUTPUT,
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT,
	@PurgedCounter int OUTPUT,
	@ErrorCounter int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	/* START VARIABLE DECLARATION */
	DECLARE 
		-- stored proc variables
		@FetchStatus int ,
	
		-- Misc Values
		@Class nvarchar(50),
		@ItemFound int,
		@Prev_LcID nvarchar(12),
		@RtnCode int,
			
		--AIMDxSL Table fields
		@SeqNbr int,
		@Status nvarchar(1),
		@LcID nvarchar(12),
		@Item nvarchar(25),
		@ActDate nvarchar(10),
		@InActDate nvarchar(10),
		@Class1 nvarchar(50),
		@Class2 nvarchar(50),
		@Class3 nvarchar(50),
		@Class4 nvarchar(50),
		@MDC nvarchar(12),
		@UPC nvarchar(22),
		@Oh int,
		@Oo int,
		@ComStk int,
		@BkOrder int,
		@BkComStk int,
		@AllocatableQTY int,
		@StkDate nvarchar(10),
		@OnPromotion nvarchar(1),
		@UserRef1 nvarchar(12),
		@UserRef2 nvarchar(12),
		@UserRef3 nvarchar(12),
		@Freeze_Forecast nvarchar(1),
		
		-- Item Table
		@Item_LcId nvarchar(12),
		@Item_Item nvarchar(25),
		@Item_ItStat nvarchar(1),
		@Item_ActDate nvarchar(10),
		@Item_InActDate nvarchar(10),
		@Item_Class1 nvarchar(50),
		@Item_Class2 nvarchar(50),
		@Item_Class3 nvarchar(50),
		@Item_Class4 nvarchar(50),
		@Item_MDC nvarchar(12),
		@Item_UPC nvarchar(22),
		@Item_Oh int,
		@Item_Oo int,
		@Item_ComStk int,
		@Item_BkOrder int,
		@Item_BkComStk int,
		@Item_AllocatableQTY int,
		@Item_StkDate nvarchar(10),
		@Item_OnPromotion nvarchar(1),
		@Item_UserRef1 nvarchar(12),
		@Item_UserRef2 nvarchar(12),
		@Item_UserRef3 nvarchar(12),
		@Item_Freeze_Forecast nvarchar(1),
		@FileTime nvarchar(24),
		
		-- SysCtrl Table
		@ByIdSource nvarchar(1),
		@Calc_Perform tinyint,
		@ClassOption tinyint,
		@AvgInvAlpha decimal(3,2),
		@Unicode_Option nvarchar(10)	
	/* END VARIABLE DECLARATION */

	-- Set the Nocount option on
	SET NOCOUNT ON
  
	/* Start variable initialization */
	-- Initialize counters
	SELECT @InsertCounter = 0,
		@UpdateCounter = 0, 
		@RowCounter = 0, 
		@PurgedCounter = 0, 
		@ErrorCounter = 0
	-- Initialize Previous Location Id
	SELECT @Prev_LcID = ''
	/* End variable initialization */
		-- Delete records from the AIMDxSL Table
	TRUNCATE TABLE AIMDxSL
	-- Just in case the operator does not have security to run the truncate 
	DELETE FROM AIMDxSL  
	
	-- This is a data interface transaction <Transaction Code=SL>. 
	-- Bulk Load the <Transaction Table=AIMDxSL> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'SL', 'AIMDxSL', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	-- Process records
	DECLARE DxSL_Cursor CURSOR LOCAL FAST_FORWARD FOR
	     SELECT SeqNbr, Status, Lcid, Item, 
			ISNULL(ActDate, convert(nvarchar(10), GETDATE(), 101)), 
            ISNULL(InActDate, '12/31/9999'), 
            ISNULL(Class1, ''), ISNULL(Class2, ''), ISNULL(Class3, ''), ISNULL(Class4, ''), 
			ISNULL(MDC, ''), ISNULL(UPC, ''), 
			ISNULL(Oh, 0), ISNULL(Oo, 0), 
			ISNULL(ComStk, 0), ISNULL(BkOrder, 0), ISNULL(BkComStk, 0), 
			ISNULL(AllocatableQty, 0), 
			ISNULL(StkDate, CONVERT(nvarchar(10), GETDATE(), 101)), 
			ISNULL(OnPromotion, ''), 
            ISNULL(UserRef1, ''), ISNULL(UserRef2, ''), ISNULL(UserRef3, ''), ISNULL(Freeze_Forecast, 'N')
	        FROM AIMDxSL
	        ORDER BY LcId, SeqNbr

	OPEN DxSL_Cursor
	BEGIN TRANSACTION
	-- Process records for updating Item with
	SELECT @FetchStatus = 0
	WHILE @FetchStatus = 0
	BEGIN  --BEGIN cursor loop
	    -- Get the next record from the cursor  
	    FETCH NEXT FROM DxSL_Cursor INTO
	        @SeqNbr, @Status, @LcID, @Item, 
			@ActDate, @InActDate, 
			@Class1, @Class2, @Class3, @Class4, 
	        @MDC, @UPC, 
			@Oh, @Oo, 
			@ComStk, @BkOrder, @BkComStk, 
			@AllocatableQty, @StkDate, 
			@OnPromotion, 
			@UserRef1, @UserRef2, @UserRef3, 
			@Freeze_Forecast	
	    -- Check for end of cursor and exit loop
		SELECT @FetchStatus = @@FETCH_STATUS
	    If @FetchStatus <> 0 
		BEGIN
	        BREAK
		END
		SET @RowCounter = @RowCounter + 1
		/* Start data validation */
	    -- Check dates
		IF ISDATE(@ActDate) = 0         -- Invalid Date
		BEGIN
			SELECT @Actdate = CONVERT(nvarchar(10), GETDATE(), 101)
		END    
		IF ISDATE(@InActDate) = 0       -- Invalid Date
		BEGIN
			SELECT @InActDate = '12/31/9999'
		END        
		IF ISDATE(@StkDate) = 0         -- Invalid Date
		BEGIN	
			SELECT @Stkdate = CONVERT(nvarchar(10), GETDATE(), 101)
		END
	    -- IF the lcid changes; update the stock date in the location table
	    IF @LcID <> @Prev_LcID
	    BEGIN
	        UPDATE AIMLocations
		    SET StkDate = @StkDate 
		    WHERE LcId = @LcID
	        
		    SELECT @Prev_LcID = @LcID
	    END

		UPDATE Item SET 
			ItStat = @Status, 
			ActDate = @ActDate,
			InActDate = @InActDate, 
			Class1 = @Class1, 
			Class2 = @Class2, 
			Class3 = @Class3, 
			Class4 = @Class4, 
			MDC = @MDC, 
			UPC = @UPC, 
			Oh = @Oh, Oo = @Oo, ComStk = @ComStk, 
			BkOrder = @BkOrder, BkComStk = @BkComStk, 
			AllocatableQty = @AllocatableQty, 
			OnPromotion = @OnPromotion,
			UserRef1 = @UserRef1, UserRef2 = @UserRef2, UserRef3 = @UserRef3,
			Freeze_Forecast = @Freeze_Forecast
		WHERE LcId = @LcID
		AND Item = @Item

		IF @@ROWCOUNT < 0 -- Update failed; increment error counter
		BEGIN
			SELECT @ErrorCounter = @ErrorCounter + 1
		END
		ELSE
		BEGIN
			SELECT @UpdateCounter = @UpdateCounter + 1
		END

	END	-- While @FetchStatus = 0

	CLOSE DxSL_Cursor 
	DEALLOCATE DxSL_Cursor
	COMMIT TRANSACTION

	-- Delete records from the AIMDxSL Table
	TRUNCATE TABLE AIMDxSL
	-- Just in case the operator does not have security to run the truncate 
	DELETE FROM AIMDxSL  
	
	--Insert into AIMDataExchageCtrl tabel when this interface has run
	-- will be used by transfermanagemet screen
	-- FileTime is used to place unique filename 
	set @FileTime =  'SL '+ str(cast(getdate() as float),12,6)
	insert into  aimdataexchangectrl
	values ('SL',@FileTime,getdate())

	UPDATE STATISTICS Item WITH RESAMPLE, ALL

	RETURN 1        -- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

