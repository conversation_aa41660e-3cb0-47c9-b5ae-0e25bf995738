SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AimSeasons_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AimSeasons_GetKey_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AimSeasons_GetKey_Sp
**	Desc: Retrieves AIMSeasons data based on value passed.
**
**	Returns: 1)@rowcount - Can be Zero
**	Values:  AIMSeasons
**              
**	Auth:   Randy Sadler	
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
 
CREATE PROCEDURE AIM_AimSeasons_GetKey_Sp
(
    	@SAVersion  						smallint	output,
     	@SaId       						nvarchar(62)	output,
    	@Action      						tinyint
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rowcount						int

SET NOCOUNT ON

SET rowcount 1

-- Get the key to the Item Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons 
        WHERE saversion = @SaVersion 
        AND said = @SaId 

        SELECT @rowcount = @@rowcount

END
IF @action = 1		-- Get Greater Than
BEGIN
  	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons 
        WHERE saversion = @SaVersion 
        AND said > @SaId 

        SELECT @rowcount = @@rowcount

        IF @rowcount = 0
        BEGIN
        	SELECT @SaVersion = saversion, @SaId = said 
               	FROM AIMSeasons 
               	WHERE saversion > @SaVersion
           
		SELECT @rowcount = @@rowcount
       	END
END
IF @action = 2		-- Get Less Than 
BEGIN
   	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons 
        WHERE saversion = @SaVersion 
        AND said < @SaId
        ORDER BY SaVersion DESC, SaId DESC
       
	SELECT @rowcount = @@rowcount
       	IF @rowcount = 0
       	BEGIN
        	SELECT @SaVersion = saversion, @SaId = said 
        	FROM AIMSeasons 
        	WHERE saversion < @SaVersion
               	ORDER BY SaVersion DESC, SaId DESC
        
		SELECT @rowcount = @@rowcount
       	END
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
  	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons 
        WHERE saversion = @SaVersion 
        AND said >= @SaId

	SELECT @rowcount = @@rowcount
       	IF @rowcount = 0
       	BEGIN
        	SELECT @SaVersion = saversion, @SaId = said 
               	FROM AIMSeasons 
               	WHERE saversion > @SaVersion  

           	SELECT @rowcount = @@rowcount
       	END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
  	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons 
        WHERE saversion = @SaVersion 
        AND said <= @SaId
        ORDER BY saversion DESC, said DESC
       
	SELECT @rowcount = @@rowcount

       	IF @rowcount = 0
       	BEGIN
        	SELECT @SaVersion = saversion, @SaId = said 
               	FROM AIMSeasons 
               	WHERE saversion < @SaVersion  
               	ORDER BY saversion DESC, said DESC
           	
		SELECT @rowcount = @@rowcount
       	END
END
IF @action = 5		-- Get First
BEGIN
  	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons  
       
	SELECT @rowcount = @@rowcount
END
IF @action = 6		-- Get Last
BEGIN
  	SELECT @SaVersion = saversion, @SaId = said 
        FROM AIMSeasons 
        ORDER BY saversion DESC, said DESC
       
	SELECT @rowcount = @@rowcount
END
IF @rowcount > 0  
  	RETURN 1		-- SUCCEED
ELSE
BEGIN
	RETURN 0		-- FAIL
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

