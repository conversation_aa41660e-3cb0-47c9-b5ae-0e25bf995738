SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocationResetOrder_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocationResetOrder_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_AllocationResetOrder_Sp
**	Desc:	Given the OrdNbr and PRE or POST AllocationStatus the procedure set the Adjusted Allocated Qty to		Allocated qty
**				
**
**	Returns:	1)@Found - Can be Zero
**				2) 0 - No Data Found
**				3) -1 - Invalid Parameter
**				4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
**	Values:Returns number of rows updated
**              
**	Author:	Srinivas Uddnati
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	
*******************************************************************************/
CREATE PROCEDURE AIM_AllocationResetOrder_Sp
(
	@OrdNbr	nvarchar(12),
	@AllocationStatus nvarchar(12)

)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
Declare 
@Found int
-- Validate the required parameters.
IF @OrdNbr IS NULL or
@AllocationStatus is NULL

BEGIN
	RETURN -1	/* Invalid parameter */
END

IF @AllocationStatus ='PRE' 
Begin
	Update AIMAODetail set AdjustedAllocQty = RequestedQty
	where ordnbr  =@OrdNbr
	and lineitemstatus in (0,1)
	and ltype ='D'
end
IF @AllocationStatus ='POST' 
Begin
	Update AIMAODetail set AdjustedAllocQty = AllocatedQty
	where ordnbr  =@OrdNbr
	and LineItemStatus in (10,11,20)
	and ltype ='D'
end


SELECT @Found = @@RowCount

-- Check for SQL Server errors. */
IF @@ERROR <> 0 
BEGIN
	RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
END
ELSE IF @Found <= 0 
BEGIN	
	RETURN -1 /* ERROR (No Valid data found in Database) */
END
ELSE
BEGIN
	RETURN @Found /* SUCCESSFUL */
END	

RETURN @@rowcount


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

