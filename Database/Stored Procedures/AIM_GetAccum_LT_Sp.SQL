SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetAccum_LT_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetAccum_LT_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_GetAccum_LT_Sp
**	Desc: Gets the accumulated lead time data for an item 
**
**	Returns: 1) @Accum_Lt
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   Author:	Description:
**  ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE PROCEDURE AIM_GetAccum_LT_Sp
(
  	@Lcid					nvarchar(12),
  	@Item					nvarchar(25),
  	@UpdateOption			        nvarchar(1) = 'N'
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON
 
DECLARE @Addl_LtDays	 	tinyint,
    	@LeadTime			smallint,
    	@PutAwayDays		tinyint,
    	@Dft_LeadTime		tinyint,
    	@Accum_LT			smallint,
     	@LeadTimeSource		nvarchar(1)
  
-- Get the additional leadtime days from the System Control Table
SELECT @Addl_LtDays = Addl_LtDays FROM SysCtrl
-- Get the key data from the Item and Location Table
SELECT @LeadTime = Item.LeadTime, 
    	@PutAwayDays = AIMLocations.PutAwayDays, 
    	@LeadTimeSource = AIMLocations.LeadTimeSource,
    	@Dft_LeadTime = AIMVendors.Dft_LeadTime
    	FROM Item
    	inner join AIMLocations on Item.Lcid = AIMLocations.Lcid
    	inner join AIMVendors on Item.Vnid = AIMVendors.VnId
    		and Item.Assort = AIMVendors.Assort
    	WHERE
    	Item.Lcid = @LcId
    	and Item.Item = @Item
-- Calculate the Accumulative Lead Time
SELECT @Accum_LT = 0
IF @LeadTimeSource = 'S' 
    	SELECT @Accum_LT = @LeadTime + @PutAwayDays + @Addl_LtDays
IF @LeadTimeSource = 'V'
    	SELECT @Accum_LT = @Dft_LeadTime + @PutAwayDays + @Addl_LtDays	
-- Update the Accumulative Lead Time
IF @UpdateOption = 'Y' and @LeadTimeSource in ('S', 'V')
    	UPDATE item SET Accum_Lt = @Accum_Lt
    		WHERE LcId = @LcId
    		and Item = @Item
-- Return the Accumulative Lead Time
RETURN @Accum_Lt

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

