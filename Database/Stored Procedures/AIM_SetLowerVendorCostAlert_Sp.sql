SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SetLowerVendorCostAlert_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SetLowerVendorCostAlert_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_SetLowerVendorCostAlert_Sp
**	Desc: Sets Alert in Podetail table for item that 
** 	have low cost venodors
**
**	Returns: 1) 
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   03/22/2005
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   Author:	Description:
**  ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE  PROCEDURE AIM_SetLowerVendorCostAlert_Sp
(
  	@VnId			nvarchar(12),
	@Assort			nvarchar(12)
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
SET NOCOUNT ON

--Set the VendorCostExcpts flag to Y for all items belonging to a primary
--Vendor and Assortment combination that have a lower cost alternate vendor
-- available
UPDATE PoDetail
SET vendorcostexcpts ='Y'
FROM Podetail,alternate_source alts
WHERE PoDetail.item =ALTS.item
and ALTS.enabled =1
and PoDetail.vnid =@VnId
and PoDetail.assort =@Assort
and podetail.cost <ALTS.stdcost


--Set the VendorCostExcpts flag to Y for all items that have a 
--PoLineType of 50 and the item exists in the PODetailTable
--for the primary Vendor Assort combination. 
UPDATE PoDetail
SET vendorcostexcpts ='Y'
FROM Podetail,alternate_source ALTS
WHERE PoDetail.item =ALTS.item
and podetail.polinetype='50'
and podetail.item in (
SELECT item 
FROM PoDetail
WHERE vnid =@VnId
and assort =@Assort
)
and ALTS.enabled =1
and podetail.cost <ALTS.stdcost

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


