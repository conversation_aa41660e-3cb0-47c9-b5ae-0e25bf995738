SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AllocationDetail_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AllocationDetail_Sp]
GO
/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name:	AIM_AllocationDetail_Sp
**	Desc:	Given ReviewerId, OrderNbr,AlloctionStatus this procedure returns AIMAODetail records, assuming:
**				(a) the item's AllocationStatus can only be PRE or POST
**				
**
**	Returns:	1)@Found - Can be Zero
**				2) 0 - No Data Found
**				3) -1 - Invalid Parameter
**				4) -2 - ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
**	Values:	Recordset - PODetail
**              
**	Author:	Srinivas Uddnati
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	Oct-06-2003	Srinivas 		Added union and other code
**						so as to sort by linenbr
*******************************************************************************/
CREATE  PROCEDURE AIM_AllocationDetail_Sp
(
 	@OrdNbr		nvarchar(12),
	@Lcid 			nvarchar(12),
	@AllocationStatus	nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON
Declare 
@Found int
-- Validate the required parameters.
IF @OrdNbr IS NULL
Or @AllocationStatus IS NULL
Or @Lcid IS NULL

BEGIN
	RETURN -1	/* Invalid parameter */
END
IF @AllocationStatus ='PRE'
Begin
	Select LineItemStatus,Exception  ='N',
	 LineNbr,Item,ItDesc,RequestedQty,AllocatedQty,AdjustedAllocQty,
	Difference =RequestedQty -AdjustedAllocQty,
	ItemFillPercentage =100 *(AdjustedAllocQty)/RequestedQty,
	cast(linenbr as int)as LineInt,1 as ord 
	 from AIMAODetail
	Where OrdNbr =@OrdNbr
	and Lcid =@Lcid
	and LType ='D'
	and LineItemStatus in (0,1)
	and cast(isnumeric(linenbr) as int)<>0
	UNION ALL
	Select LineItemStatus,Exception  ='N',
	 LineNbr,Item,ItDesc,RequestedQty,AllocatedQty,AdjustedAllocQty,
	Difference =RequestedQty -AdjustedAllocQty,
	ItemFillPercentage =100 *(AdjustedAllocQty)/RequestedQty
	, 0 as LineInt ,2 as ord 
	 from AIMAODetail
	Where OrdNbr =@OrdNbr
	and Lcid =@Lcid
	and LType ='D'
	and LineItemStatus in (0,1)
	and cast(isnumeric(linenbr) as int)=0
	order by ord,lineint asc
End
IF @AllocationStatus ='POST'
Begin
	Select LineItemStatus,Exception  =Case 
	 when  LineItemStatus in(12,22)
	Then 'Y'
	Else 'N'
	End, LineNbr,Item,ItDesc,RequestedQty,AllocatedQty,AdjustedAllocQty,
	Difference =RequestedQty -AdjustedAllocQty,
	ItemFillPercentage =100 *(AdjustedAllocQty)/RequestedQty,
	 cast(linenbr as int)as LineInt,1 as ord 
	 from AIMAODetail
	Where OrdNbr =@OrdNbr
	and Lcid =@Lcid
	and LType ='D'
	and LineItemStatus in (10,11,12,20,22)
	and cast(isnumeric(linenbr) as int)<>0
	UNION ALL
	Select LineItemStatus,Exception  =Case 
	 when  LineItemStatus in(12,22)
	Then 'Y'
	Else 'N'
	End, LineNbr,Item,ItDesc,RequestedQty,AllocatedQty,AdjustedAllocQty,
	Difference =RequestedQty -AdjustedAllocQty,
	ItemFillPercentage =100 *(AdjustedAllocQty)/RequestedQty
	, 0 as LineInt,2 as ord 
	 from AIMAODetail
	Where OrdNbr =@OrdNbr
	and Lcid =@Lcid
	and LType ='D'
	and LineItemStatus in (10,11,12,20,22)
	and cast(isnumeric(linenbr) as int)=0
	order by ord,lineint asc
End
SELECT @Found = @@RowCount

-- Check for SQL Server errors. */
IF @@ERROR <> 0 
BEGIN
	RETURN -2  /* ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR) */
END
ELSE IF @Found <= 0 
BEGIN	
	RETURN -1 /* ERROR (No Valid data found in Database) */
END
ELSE
BEGIN
	RETURN @Found /* SUCCESSFUL */
END	

RETURN @@rowcount
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

