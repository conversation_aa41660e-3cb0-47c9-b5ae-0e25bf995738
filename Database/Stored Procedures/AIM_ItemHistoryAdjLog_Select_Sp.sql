SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIM_ItemHistoryAdjLog_Select_Sp]') 
		AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_ItemHistoryAdjLog_Select_Sp]
GO

/*
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
** 	Name: AIM_ItemHistoryAdjLog_Select_Sp
** 	Purpose: Fetches the adjustments from the Item History Adjustments Log,
**		given the following criteria:
** 		1.  Location ID
**		2.  Item ID
** 		3.  History Year
**		4.  History Period
**		5.  Demand Source (O = Order Quantity; S = Shipped Quantity)
**
** 		Version Number 	- 1.0
** 		Date Created 	- 2004/08/11
** 		Created By 		- Annalakshmi Stocksdale
**
********************************************************************************
** This file contains trade secrets of SSA Global. No part
** may be reproduced or transmitted in any form by any means or for any purpose
** without the express written permission of SSA Global.
********************************************************************************
**	Change History
********************************************************************************
**	Date:      	Author:      		Description:
**	---------- 	------------ 		-------------------------------------------
********************************************************************************
*/

CREATE PROCEDURE AIM_ItemHistoryAdjLog_Select_Sp (
	@LcID nvarchar (12)
	, @Item nvarchar (25)
	, @SubsItem nvarchar (25)
	, @HisYear smallint
	, @DemandPeriod tinyint
	, @DemandSource nvarchar (1)
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @CheckValue as decimal(9, 1)
	
	-- Validate the required parameters.
	IF @LcID IS NULL
	OR @Item IS NULL
	OR @HisYear IS NULL
	OR @DemandPeriod IS NULL
	OR @DemandSource IS NULL
	BEGIN
	  	RETURN -1
	END

	-- Validate the existence of the record in ItemHistory
	SELECT 
		OldValue
		, NewValue
		, AdjustDateTime
		, UserID
		, Reason
	FROM ItemHistoryAdjLog IHAL
	WHERE IHAL.LcID = @LcID
	AND IHAL.Item = @Item
	AND IHAL.SubsItem = @SubsItem
	AND IHAL.HisYear = @HisYear
	AND IHAL.DemandPeriod = @DemandPeriod
	AND IHAL.DemandSource = @DemandSource

	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	IF @Return_Stat <= 0 RETURN -1
	ELSE RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

