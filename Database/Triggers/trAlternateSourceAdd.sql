/*******************************************************************************
  **	Name: trAlternateSourceAdd
  **	Desc: On Insert/Update Trigger - Table Name: Alternate_Source
  **          Check the Entries for that Item/LcId in Alternate_Source Table
  **          IF Enabled Entries > 0 --> Item.AltVnFlag = "Y" Else "N"
  **              
  **	Author:   Mohammed
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **    Date:	   Author:	Description:
  **    ---------- ------------	-------------------------------------------------
  *******************************************************************************/

/* Drop Object If Already Exists */
 if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[trAlternateSourceAdd]') and OBJECTPROPERTY(id, N'IsTrigger') = 1)
 DROP TRIGGER dbo.trAlternateSourceAdd
 GO

/*****************************************************************/
/* Start Create Trigger Here                                     */
/*****************************************************************/

CREATE TRIGGER trAlternateSourceAdd
ON Alternate_Source
FOR Insert, Update
AS

Declare @Enabled nvarchar(1)

BEGIN

    Declare @RcdCount int    

    Select @RcdCount = Count(*) From Alternate_Source, Inserted I
     Where Alternate_Source.Item = I.Item  
       AND Alternate_Source.LcId = I.LcId   
       AND Alternate_Source.Enabled = '1'

    IF @RcdCount > 0 
        UPDATE ITEM SET AltVnFlag = 'Y' 
          	FROM Item, Inserted I
               WHERE Item.Item = I.Item  
                 AND Item.LcId = I.LcId   

    Else
        UPDATE ITEM SET AltVnFlag = 'N' 
          	FROM Item, Inserted I 
               WHERE Item.Item = I.Item  
                 AND Item.LcId = I.LcId   

If @@ERROR <> 0
  ROLLBACK TRAN

End



/* Make sure object was created successfully */
IF OBJECT_ID('trAlternateSourceAdd') IS NULL
BEGIN
     PRINT 'Msg 61000: <<<CREATION OF TRIGGER trAlternateSourceAdd FAILED>>>'
END
ELSE
BEGIN
     PRINT '<<<CREATED TRIGGER trAlternateSourceAdd >>>'
END
/* End make sure object was created successfully */





