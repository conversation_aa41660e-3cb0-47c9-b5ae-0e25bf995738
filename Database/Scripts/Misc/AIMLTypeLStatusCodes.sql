DELETE FROM AIMCodeLookup WHERE CodeType IN ('LTYPE','LSTATUS')

INSERT INTO AIMCodeLookup (CodeID, CodeType, LangID, CodeDesc)
VALUES ('D', 'LTYPE', 'en-us', 'Destination')

INSERT INTO AIMCodeLookup (CodeID, CodeType, LangID, CodeDesc)
VALUES ('S', 'LTYPE', 'en-us', 'Source')

INSERT INTO AIMCodeLookup (CodeID, CodeType, LangID, CodeDesc)
VALUES ('A', 'LSTATUS', 'en-us', 'Active')

INSERT INTO AIMCodeLookup (CodeID, CodeType, LangID, CodeDesc)
VALUES ('I', 'LSTATUS', 'en-us', 'Inactive')


SELECT * FROM AIMCodeLookup WHERE LangID = 'en-us'