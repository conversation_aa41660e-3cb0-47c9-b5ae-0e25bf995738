SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

ALTER PROCEDURE UnmatchedInAIMDD_Sp
  
  AS  --ENCRYPT
  
  SET NOCOUNT ON 
  	
  SELECT aimdd.dataelement, syscolumns.name
  INTO #temp
  FROM aimdd
  LEFT OUTER JOIN syscolumns ON aimdd.dataelement = syscolumns.name
  	SELECT dataelement, Status = 'Unmatched' 
	FROM #temp 
	WHERE name is null  

  SELECT sysobjects.name TableName, syscolumns.name, syscolumns.id, aimdd.dataelement
  INTO #temp1
  FROM syscolumns
  LEFT OUTER JOIN aimdd ON syscolumns.name = aimdd.dataelement 
  JOIN sysobjects on syscolumns.id = sysobjects.id
  	SELECT tablename, name, Status = 'Unmatched' 
	FROM #temp1 
	WHERE dataelement is null and id in (Select id
						From Sysobjects
						Where xtype = 'U')
RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

