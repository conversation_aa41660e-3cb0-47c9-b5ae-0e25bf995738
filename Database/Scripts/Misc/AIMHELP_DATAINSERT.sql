INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','atBuyerReviewTab',
'\WebHelp\Buyer_Review_Options\About_Buyer_Review.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','atSummary',
'\WebHelp\Buyer_Review_Options\Buyer_Review_Screen\Vendor_Summary_Information.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','dcTransmitPO',
'\WebHelp\Buyer_Review_Options\About_Buyer_Review.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','dgItemSourcing',
'\WebHelp\Buyer_Review_Options\About_Buyer_Review.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','dgPerformance',
'\WebHelp\Buyer_Review_Options\Buyer_Review_Screen\Performance_Tab.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','dgPositions',
'\WebHelp\Buyer_Review_Options\Buyer_Review_Screen\On-Hand_Positions_Tab.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','dgVendorDetail',
'\WebHelp\Buyer_Review_Options\Buyer_Review_Screen\Vendor_Detail_Grid.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','dgVnList',
'\WebHelp\Buyer_Review_Options\Buyer_Review_Screen\Vendor_Summary_List.htm')
INSERT INTO AIMHELP
	(FORMNAME,FILEPATH)
	VALUES('AIM_BuyerReview',
'\WebHelp\Buyer_Review_Options\About_Buyer_Review.htm')
INSERT INTO AIMHELP
	(FORMNAME,CONTROLNAME,FILEPATH)
	VALUES('AIM_BuyerReview','tbBuyerReview',
'\WebHelp\Buyer_Review_Options\About_Buyer_Review.htm')

DELETE FROM AIMHELP

/************
Dim CNTL As Control
For Each CNTL In Controls
    If Not (UCase(Left(CNTL.Name, 3)) = "TXT" Or UCase(Left(CNTL.Name, 3)) = "FRA" Or UCase(Left(CNTL.Name, 3)) = "LAB" Or UCase(Left(CNTL.Name, 3)) = "MNU" Or UCase(Left(CNTL.Name, 3)) = "CMD" Or UCase(Left(CNTL.Name, 2)) = "SS") Then
       Debug.Print Me.Name & "," & CNTL.Name
    End If
Next
***/