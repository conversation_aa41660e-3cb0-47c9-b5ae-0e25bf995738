/*
-- Created 14 April, 2004 by <PERSON><PERSON><PERSON><PERSON>
-- Source: http://www.sql-server-performance.com/nonclustered_indexes.asp
--
-- When deciding whether or not to add a non-clustered index to a column of a table, 
-- it is useful to first find out how selective it is. By this, what we want to know 
-- is the ratio of unique rows to total rows (based on a specific column) 
-- found in the table. Generally, if a column is not more than 95% unique, 
-- then the Query Optimizer might not even use the index. If this is the case, 
-- then adding the non-clustered index may be a waste of disk space. In fact, 
-- adding a non-clustered index that is never used will hurt a table's performance.
--
-- Another useful reason to determine the selectivity of a column 
-- is to decide what is the best order to position indexes in a composite index. 
-- This is because you will get the best performance out of a composite index 
-- if the columns are arranged so that the most selective is the first one, 
-- the next most selective, the second one, and so on. 
--
-- So how do you determine the selectivity of a column? 
-- One way is to run the following script on any column 
-- you are considering for a non-clustered index. 
-- This example script is designed to be used with the Northwind database, 
-- [using Table 'Order Details' and Field 'OrderID']
-- so you will need to modify it appropriately for your use. 
*/

--Finds the Degree of Selectivity for a Specific Column in a Row
Declare @total_unique float
Declare @total_rows float
Declare @selectivity_ratio float 

SELECT @total_unique = 0
SELECT @total_rows = 0
SELECT @selectivity_ratio = 0

--Finds the Total Number of Unique Rows in a Table
--Be sure to replace OrderID below with the name of your column
--Be sure to replace [Order Details] below with your table name
SELECT @total_unique = (SELECT COUNT(DISTINCT CODETYPE) FROM [AIMCodeLookup])

--Calculates Total Number of Rows in Table
--Be sure to replace [Order Details] below with your table name
SELECT @total_rows = (SELECT COUNT(*) FROM [AIMCodeLookup])

--Calculates Selectivity Ratio for a Specific Column
SELECT @selectivity_ratio = ROUND((SELECT @total_unique/@total_rows),2,2)
SELECT @selectivity_ratio as 'Selectivity Ratio'

