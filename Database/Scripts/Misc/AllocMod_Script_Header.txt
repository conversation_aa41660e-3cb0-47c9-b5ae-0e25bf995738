/*******************************************************************************
-- THIS FILE CONTAINS ALL THE TABLE AND STORED PROCEDURE SCRIPTS FOR THE ALLOCATION MODULE
-- 		NOTE: This file is arranged such that dependencies are properly assigned in the system tables.
--			Please exercise due caution if re-arranging the order of sub-scripts.
-- CONTENTS (In order of creation): 
--		** AllocationScratchTables: 		Table Creation scripts
--
--		* AIM_Alloc_Pass1_Sp:				Sub called by AllocateInventory
--		* AIM_Alloc_Pass2_Sp:				Sub called by AllocateInventory
--
--		* AIM_GetSessionID_Sp:				Sub called by AllocationCtrl
--		* AIM_Alloc_ScratchInserts_Sp:		Sub called by AllocationCtrl
--		* AIM_AllocateInventory_Sp:			Sub called by AllocationCtrl 
--		* AIM_UpdateAIMAOOrderStatus_Sp		Sub called by AllocationCtrl 
--		* AIM_Alloc_UpdateAggregates_Sp		Sub called by AllocationCtrl 
--		* AIM_Alloc_HoldExceptions_Sp		Sub called by AIM_Alloc_ReleaseOrders_Sp
--		* AIM_Alloc_ReleaseOrders_Sp		Sub called by AllocationCtrl 
--
-- 		** AIMDataInterface.AllocationControl(): Main function for Allocation. 
--											Calls all the stored procedures
--
--	Author:		Annalakshmi Stocksdale
--	Created:	2003/05/21
-------------------------------------------------------------------------------
--	Change History
-------------------------------------------------------------------------------
--	Date:		Updated by:		Description:
--	----------	------------	-----------------------------------------------
*******************************************************************************/

