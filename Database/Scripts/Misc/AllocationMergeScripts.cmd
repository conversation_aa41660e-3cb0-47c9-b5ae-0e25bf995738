@echo off
REM Created Jan. 05 2005 by Annalakshmi Stocksdale.
REM -------------------------------------------------------------------------------
REM This script is intended to merge all Allocation table and stored procedure scripts, 
REM in order of dependencies
REM ~~~~~~~~~~~~~~~~~~
REM Required Software: 
REM Microsoft SQL Server 2000 server with Query Analyser
REM ~~~~~~~~~~
REM -------------------------------------------------------------------------------

setlocal
set prompt=()
cls

set SS_DIR=C:\SSA_DR_SS
set OUTPUT_DIR=\EXceedAIMProject\Database\Scripts\Misc\
set LOG_FILE=AllocMergeScripts.LOG

set HEADER_TXT=C:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\Misc\AllocMod_Script_Header.txt

set CREATETAB_SQL=Alloc_Tables.SQL
set CREATETAB_LOG=Alloc_Tables.LOG

set CREATESPS_SQL=Alloc_Procs.SQL
set CREATESPS_LOG=Alloc_Procs.LOG
set MYSCRIPT=C:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\Misc\AIM_AllocationModule_Script.sql

:START_BATCH
 REM -------------------------------------------------------------------------------
 echo ------ START - AllocationMergeScripts.bat ------ 
 REM -------------------------------------------------------------------------------
if exist %SS_DIR%%OUTPUT_DIR%%LOG_FILE% (del %SS_DIR%%OUTPUT_DIR%%LOG_FILE%)

:MergeTables
REM -------------------------------------------------------------------------------
echo Processing tables...
REM -------------------------------------------------------------------------------
echo ----- 1/3. Merging Alloc*.TABs ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
if exist %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL% (del %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL%)

echo ************************************************************ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo - Get the tables (in order), first. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AllocScratch_Req_Qty: Requestor Quantity - information on aggregate ordered quantity by store and item >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AllocScratch_Req_Pri: Requestor Priority - information on priorities for stores with orders >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AllocScratch_Req_Ratio: Need ratio - information on need ratios by store and item >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AllocScratch_SourceInventory: Source Inventory - information on DC On-Hand (so we don't mess up the real one) >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AllocScratch_ItemDemand: Item demand summary table - sum'd demand for an item for all destinations in a priority group. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AllocScratch_AllocResults: Output table for the allocation result record >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo ************************************************************ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

copy /V /Y ..\..\Tables\*Alloc*_Req_Qty*.TAB + ..\..\Tables\*Alloc*_Req_Pri*.TAB + ..\..\Tables\*Alloc*_Req_Ratio.TAB + ..\..\Tables\*Alloc*_SourceInventory*.TAB + ..\..\Tables\*Alloc*_ItemDemand*.TAB + ..\..\Tables\*Alloc*_AllocResults*.TAB %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

:MergeProcs
REM -------------------------------------------------------------------------------
echo Processing stored procedures...
REM -------------------------------------------------------------------------------
echo ----- 2/3. Merging *Alloc*.SQLs ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
if exist %SS_DIR%%OUTPUT_DIR%%CREATESPS_SQL% (del %SS_DIR%%OUTPUT_DIR%%CREATESPS_SQL%)

echo ************************************************************ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo - Get the stored procedures (in order), next. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_Alloc_Pass1_Sp: Sub called by AllocateInventory >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_Alloc_Pass2_Sp: Sub called by AllocateInventory >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_GetSessionID_Sp: Sub called by AllocationCtrl >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_Alloc_ScratchInserts_Sp: Sub called by AllocationCtrl >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_AllocateInventory_Sp: Sub called by AllocationCtrl >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
echo AIM_UpdateAIMAOOrderStatus_Sp: Sub called by AllocationCtrl  >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_Alloc_UpdateAggregates_Sp: Sub called by AllocationCtrl  >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_Alloc_HoldExceptions_Sp: Sub called by AIM_Alloc_ReleaseOrders_Sp >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_Alloc_ReleaseOrders_Sp: Sub called by AllocationCtrl  >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo AIM_AllocationCtrl_Sp: Main procedure called by the VB app. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo ************************************************************ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

copy /V /-Y "..\..\Stored Procedures\*Alloc*Pass*.SQL" + "..\..\Stored Procedures\*GetSessionID*.SQL" + "..\..\Stored Procedures\*Alloc*ScratchInserts*.SQL" + "..\..\Stored Procedures\*AllocateInventory*.SQL" + "..\..\Stored Procedures\*UpdateAIMAO*.SQL" + "..\..\Stored Procedures\*Alloc*Update*.SQL" + "..\..\Stored Procedures\*Alloc*Hold*.SQL" + "..\..\Stored Procedures\*Alloc*Release*.SQL" + "..\..\Stored Procedures\*AllocationCtrl*.SQL" %SS_DIR%%OUTPUT_DIR%%CREATESPS_SQL% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

:MergeFiles
REM -------------------------------------------------------------------------------
echo Merging into one file...
REM -------------------------------------------------------------------------------
echo ----- 3/3. Merging misc. files into one ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
if exist %MYSCRIPT% (del %MYSCRIPT%)

echo ************************************************************ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
copy /V /Y %HEADER_TXT% + %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL% + %SS_DIR%%OUTPUT_DIR%%CREATESPS_SQL% %MYSCRIPT% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
echo ************************************************************ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%


GOTO EXIT_BATCH

:LOW_MEMORY
 REM -------------------------------------------------------------------------------
 rem errorlevel 4
 echo Insufficient memory to copy files or 
 echo invalid drive or command-line syntax. Cancelling batch.
 REM -------------------------------------------------------------------------------
 echo Insufficient memory to copy files or invalid drive or command-line syntax. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
 GOTO EXIT_BATCH

:CANCEL_BAT
 REM -------------------------------------------------------------------------------
 rem errorlevel 2
 echo You pressed CTRL+C to end the copy operation. Cancelling batch.
 REM -------------------------------------------------------------------------------
 echo You pressed CTRL+C to end the copy operation. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
 GOTO EXIT_BATCH

:ISQLW_FAILURE
 REM -------------------------------------------------------------------------------
 echo ISQL command failed. Cancelling batch.
 REM -------------------------------------------------------------------------------
 echo ISQL command failed. Cancelling batch.>> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
 GOTO EXIT_BATCH

:EXIT_BATCH
 REM -------------------------------------------------------------------------------
 echo ------ END - AllocationMergeScripts.bat ------ 
 REM -------------------------------------------------------------------------------
 echo ------ END - AllocationMergeScripts.bat ------ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
 date /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
 time /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 

set prompt=
endlocal
REM End batch.
