--******************************************************
--   Purpose: Migration Script 
--   Product: EXceed AIM
--   Version: 4.1
--   Date:    6/06/2001
--   Author:  <PERSON>
--******************************************************

If not exists (select * from dbo.sysobjects where id = object_id(N'EXCEED_AIM..EXceed_AIM_Migration'))
Create Table EXCEED_AIM..EXceed_AIM_Migration
(ProductName nvarchar(50) Not Null,
OldVersion nvarchar(10) Not Null,
NewVersion nvarchar(10) Not Null,
TransactionCount bigint Not Null,
TotalTimeMinute int Not Null,
StartTime datetime Not Null,
EndTime datetime Not Null,
CONSTRAINT PK_EXceed_AIM_Migration PRIMARY KEY (ProductName, NewVersion))
Go

If not exists (select * from dbo.sysobjects where id = object_id(N'EXCEED_AIM..EXceed_AIM_Migration_Log')) 
Create Table EXCEED_AIM..EXceed_AIM_Migration_Log
(ProductName nvarchar(50) Not Null,
OldVersion nvarchar(10) Not Null,
NewVersion nvarchar(10) Not Null,
TableName nvarchar(30) Not Null,
DefaultCount bigint Not Null, 
OldDatabaseCount bigint Not Null,
NewDatabaseCount bigint Not Null,
TotalTimeMillisecond int Not Null,
StartTime datetime Not Null,
EndTime datetime Not Null,
CONSTRAINT PK_EXceed_AIM_Migration_Log PRIMARY KEY (ProductName, NewVersion, TableName))
Go

Declare @Default bigint,
	@OldCount bigint,
	@NewCount bigint,
	@StartTime datetime,
	@EndTime datetime,
	@Transaction bigint

--******************************************************
-- EXceed_AIM_Migration Table Insert
--******************************************************
Select @Transaction = 0
Select @StartTime = Getdate()
Insert into EXCEED_AIM..EXceed_AIM_Migration values
	('EXceed_AIM','4.0','4.1',0,@Transaction,@StartTime,@StartTime)

--******************************************************
-- AIMClasses
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMClasses
Select @OldCount =count(*) from AIM40..AIMClasses
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMClasses',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMClasses
Insert into EXCEED_AIM..AIMClasses
	Select * from AIM40..AIMClasses
	order by Class
Select @NewCount =count(*) from EXCEED_AIM..AIMClasses
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMClasses' and NewVersion = '4.1'

--******************************************************
-- AIMDataEXchangeCtrl
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMDataEXchangeCtrl
Select @OldCount =count(*) from AIM40..AIMDataEXchangeCtrl
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMDataEXchangeCtrl',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMDataEXchangeCtrl
	Select * from AIM40..AIMDataEXchangeCtrl
	order by TxnSet
Select @NewCount =count(*) from EXCEED_AIM..AIMDataEXchangeCtrl
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMDataEXchangeCtrl' and NewVersion = '4.1'

--******************************************************
-- AIMDays
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMDays
Select @OldCount =count(*) from AIM40..AIMDays
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMDays',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMDays
	Select * from AIM40..AIMDays
	order by FYDate
Select @NewCount =count(*) from EXCEED_AIM..AIMDays
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMDays' and NewVersion = '4.1'

--******************************************************
-- AIMForecast
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMForecast
Select @OldCount =count(*) from AIM40..AIMForecast
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMForecast',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMForecast
	Select * from AIM40..AIMForecast
	order by FcstId
Select @NewCount =count(*) from EXCEED_AIM..AIMForecast
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMForecast' and NewVersion = '4.1'

--******************************************************
-- AIMLeadTime
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMLeadTime
Select @OldCount =count(*) from AIM40..AIMLeadTime
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMLeadTime',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMLeadTime
	Select * from AIM40..AIMLeadTime
	order by LcId, Item
Select @NewCount =count(*) from EXCEED_AIM..AIMLeadTime
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMLeadTime' and NewVersion = '4.1'

--******************************************************
-- AIMLocations
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMLocations
Select @OldCount =count(*) from AIM40..AIMLocations
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMLocations',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMLocations
	Select * from AIM40..AIMLocations
	order by LcId
Select @NewCount =count(*) from EXCEED_AIM..AIMLocations
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMLocations' and NewVersion = '4.1'

--******************************************************
-- AIMOptions
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMOptions
Select @OldCount =count(*) from AIM40..AIMOptions
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMOptions',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMOptions
Insert into EXCEED_AIM..AIMOptions
	Select * from AIM40..AIMOptions
	order by OptionId
Select @NewCount =count(*) from EXCEED_AIM..AIMOptions
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMOptions' and NewVersion = '4.1'

--******************************************************
-- AIMPO
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMPO
Select @OldCount =count(*) from AIM40..AIMPO
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMPO',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMPO
	Select * from AIM40..AIMPO
	order by ById, VnId, Assort
Select @NewCount =count(*) from EXCEED_AIM..AIMPO
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMPO' and NewVersion = '4.1'

--******************************************************
-- AIMPromotions
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMPromotions
Select @OldCount =count(*) from AIM40..AIMPromotions
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMPromotions',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMPromotions
	Select * from AIM40..AIMPromotions
	order by PmId
Select @NewCount =count(*) from EXCEED_AIM..AIMPromotions
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMPromotions' and NewVersion = '4.1'

--******************************************************
-- AIMSd
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMSd
Select @OldCount =count(*) from AIM40..AIMSd
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMSd',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMSd
	Select * from AIM40..AIMSd
Select @NewCount =count(*) from EXCEED_AIM..AIMSd
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMSd' and NewVersion = '4.1'

--******************************************************
-- AIMSeasons
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMSeasons
Select @OldCount =count(*) from AIM40..AIMSeasons
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMSeasons',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMSeasons
Insert into EXCEED_AIM..AIMSeasons
	Select * from AIM40..AIMSeasons
	order by SaVersion, SaId
Select @NewCount =count(*) from EXCEED_AIM..AIMSeasons
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMSeasons' and NewVersion = '4.1'

--******************************************************
-- AIMUOM
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMUOM
Select @OldCount =count(*) from AIM40..AIMUOM
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMUOM',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMUOM
	Select * from AIM40..AIMUOM
	order by UOM
Select @NewCount =count(*) from EXCEED_AIM..AIMUOM
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMUOM' and NewVersion = '4.1'

--******************************************************
-- AIMUsers
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMUsers
Select @OldCount =count(*) from AIM40..AIMUsers
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMUsers',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMUsers
Insert into EXCEED_AIM..AIMUsers 
	Select UserID, UserName, SLevel, ScArray, DmdUpdExcpts,OnPromotionExcpts, ExtCostExcpts, ExtCostLimit, 
	PackSizeExcpts, PS_PctRoundLimit, PS_ExtCostLimit, LastWeekSaleExcpts, OverDuePO, SafetyStockEroded,
	BackOrdered, InvAvailRestriction, InvAvailList, UserInitials, 'en-us', 'N'
	from AIM40..AIMUsers
	order by UserId
Select @NewCount =count(*) from EXCEED_AIM..AIMUsers
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMUsers' and NewVersion = '4.1'

--******************************************************
-- AIMVendors
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMVendors
Select @OldCount =count(*) from AIM40..AIMVendors
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMVendors',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMVendors
	Select * from AIM40..AIMVendors
	order by VnId, Assort
Select @NewCount =count(*) from EXCEED_AIM..AIMVendors
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMVendors' and NewVersion = '4.1'

--******************************************************
-- AIMYears
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMYears
Select @OldCount =count(*) from AIM40..AIMYears
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','AIMYears',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMYears
	Select * from AIM40..AIMYears
	order by FiscalYear
Select @NewCount =count(*) from EXCEED_AIM..AIMYears
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMYears' and NewVersion = '4.1'

--******************************************************
-- BuyerStatusTest
--******************************************************
Select @Default =count(*) from EXCEED_AIM..BuyerStatusTest
Select @OldCount =count(*) from AIM40..BuyerStatusTest
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','BuyerStatusTest',@Default,@OldCount,0,0,@StartTime,@StartTime)
Set Identity_Insert EXCEED_AIM..BuyerStatusTest On
Insert into EXCEED_AIM..BuyerStatusTest (KeyId, [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines],
	[Released Lines],  [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts],
	[Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews], 
	[Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts])
	(Select * from AIM40..BuyerStatusTest)
Set Identity_Insert EXCEED_AIM..BuyerStatusTest Off
Select @NewCount =count(*) from EXCEED_AIM..BuyerStatusTest
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'BuyerStatusTest' and NewVersion = '4.1'

--******************************************************
-- ItDefaults
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ItDefaults
Select @OldCount =count(*) from AIM40..ItDefaults
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','ItDefaults',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..ItDefaults
	Select * from AIM40..ItDefaults
	order by LcId, Class
Select @NewCount =count(*) from EXCEED_AIM..ItDefaults
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ItDefaults' and NewVersion = '4.1'

--******************************************************
-- Item
--******************************************************
Select @Default =count(*) from EXCEED_AIM..Item
Select @OldCount =count(*) from AIM40..Item
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','Item',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..Item
Insert into EXCEED_AIM..Item
	Select * from AIM40..Item
	order by LcId, Item
Select @NewCount =count(*) from EXCEED_AIM..Item
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'Item' and NewVersion = '4.1'

--******************************************************
-- ItemHistory
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ItemHistory
Select @OldCount =count(*) from AIM40..ItemHistory
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','ItemHistory',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..ItemHistory
	Select * from AIM40..ItemHistory
	order by LcId, Item, HisYear
Select @NewCount =count(*) from EXCEED_AIM..ItemHistory
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ItemHistory' and NewVersion = '4.1'

--******************************************************
-- MDCDetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..MDCDetail
Select @OldCount =count(*) from AIM40..MDCDetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','MDCDetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..MDCDetail
	Select * from AIM40..MDCDetail
	order by LcId, Item
Select @NewCount =count(*) from EXCEED_AIM..MDCDetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'MDCDetail' and NewVersion = '4.1'

--******************************************************
-- MDCSummary
--******************************************************
Select @Default =count(*) from EXCEED_AIM..MDCSummary
Select @OldCount =count(*) from AIM40..MDCSummary
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','MDCSummary',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..MDCSummary
	Select * from AIM40..MDCSummary
	order by MDC, Item
Select @NewCount =count(*) from EXCEED_AIM..MDCSummary
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'MDCSummary' and NewVersion = '4.1'

--******************************************************
-- PODetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..PODetail
Select @OldCount =count(*) from AIM40..PODetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','PODetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Set Identity_Insert EXCEED_AIM..PODetail On
Insert into EXCEED_AIM..PODetail (POLineType, POSeqID, OrdStatus, RevCycle, ById, VnId, Assort, Lcid, Item, 
	ItDesc, POType, AvailQty, PackRounding, RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, ConvFactor, 
	IsDate, DuDate, LastWeekSalesFlag, Cost, [Zone])
	(Select * from AIM40..PODetail)
Set Identity_Insert EXCEED_AIM..PODetail Off
Select @NewCount =count(*) from EXCEED_AIM..PODetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'PODetail' and NewVersion = '4.1'

--******************************************************
-- POStatistics
--******************************************************
Select @Default =count(*) from EXCEED_AIM..POStatistics
Select @OldCount =count(*) from AIM40..POStatistics
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','POStatistics',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..POStatistics
	Select * from AIM40..POStatistics
	order by SeqNbr, ById, VnId, Assort
Select @NewCount =count(*) from EXCEED_AIM..POStatistics
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'POStatistics' and NewVersion = '4.1'

--******************************************************
-- RevCycles
--******************************************************
Select @Default =count(*) from EXCEED_AIM..RevCycles
Select @OldCount =count(*) from AIM40..RevCycles
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','RevCycles',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..RevCycles
Insert into EXCEED_AIM..RevCycles
	Select * from AIM41..RevCycles
	order by RevCycle
Select @NewCount =count(*) from EXCEED_AIM..RevCycles
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'RevCycles' and NewVersion = '4.1'

--******************************************************
-- SysCtrl
--******************************************************
Select @Default =count(*) from EXCEED_AIM..SysCtrl
Select @OldCount =count(*) from AIM40..SysCtrl
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	('EXceed_AIM','4.0','4.1','SysCtrl',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..SysCtrl
Insert into EXCEED_AIM..SysCtrl
	Select CName, Address1, Address2, Address3, City, State, CoZip, Dft_MinRchPct, Dft_BestRchPct,
	DxPath, DxPath_Out, DxPath_Backup, Dft_POStatus, DxPO_Option, KFactor, MinROAI, Vendor_Sizing,
	Priority_Min_Dser, Priority_Min_VelCode, Addl_LTDays, Lt_Filter_Pct, Retain_SD_Opt, Calc_Perform,
	LogFile, ByIdSource, SeasSmoothingPds, CurSaVersion, Last_POSeq, ClassOption, AvgInvAlpha,
	UpdateGoalsOption, UpdateVnLT, VnLTPctChgFilter, VnLTDaysChgFilter, MDCOption,
	LT_Alpha, LT_CtrlInterval_Pct, Int_Enabled, Int_MinPctZero, Int_MaxPctZero, Int_SeasonalityIndex,
	Int_SrvLvlOverrideFlag, Int_SrvLvl, Int_LookBackPds, AIMBatchPath, 'en-us', 'N', 'N', 'mm/dd/yyyy',
	'hh:nn:ss AMPM', 'N'
	from AIM40..SysCtrl
	order by CName
Select @NewCount =count(*) from EXCEED_AIM..SysCtrl
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'SysCtrl' and NewVersion = '4.1'

--******************************************************
-- EXceed_AIM_Migration Table Update
--******************************************************
Select @StartTime = StartTime from EXCEED_AIM..EXceed_AIM_Migration where NewVersion = '4.1'
Update EXCEED_AIM..EXceed_AIM_Migration set TransactionCount = @Transaction, EndTime = @EndTime, TotalTimeMinute = datediff(mi,@StartTime,@EndTime)
	where NewVersion = '4.1'

--******************************************************
-- EXceed_AIM_Migration Display
--******************************************************
Select * from EXCEED_AIM..EXceed_AIM_Migration where NewVersion = '4.1'
--******************************************************
-- EXceed_AIM_Migration_Log Display
--******************************************************
Select * from EXCEED_AIM..EXceed_AIM_Migration_Log where NewVersion = '4.1'


