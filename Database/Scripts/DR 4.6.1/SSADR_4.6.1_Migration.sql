--------------------------------------------------------
--   Product: 	SSA DR
--   Version: 	4.6.1
--   Purpose: 	Migrate data from an older version of the SSA DR database 
--				(formerly known as EXceed AIM or the SSA AIM)
--				to one compatible with the current version.
-- 	Notes:	
--		IMPORTANT! PLEASE READ!!
--		Find and replace all occurrences (of the enquoted string):
--			1. "OLD_DR_DB" with the name of the older-version database.
--			2. "NEW_DR_DB" with the name of the latest-version database.
--------------------------------------------------------
SET NOCOUNT ON

PRINT 'Starting SSA DR database migration script...'

IF NOT EXISTS (SELECT * FROM NEW_DR_DB.dbo.sysobjects WHERE ID = object_id(N'NEW_DR_DB..SSA_DR_Migration'))
BEGIN
	CREATE TABLE NEW_DR_DB..SSA_DR_Migration (
		EAM_ID uniqueidentifier Default NewID() NOT NULL,
		ProductName nvarchar(50) Not Null,
		OldVersion nvarchar(10) Not Null,
		NewVersion nvarchar(10) Not Null,
		TransactionCount bigint Not Null,
		TotalTimeMinute int Not Null,
		StartTime datetime Not Null,
		EndTime datetime Not Null,
		CONSTRAINT PK_SSA_DR_Migration PRIMARY KEY (EAM_ID)
	)
	
	CREATE INDEX EAM_ProdNewVer ON NEW_DR_DB..SSA_DR_Migration (ProductName, NewVersion)
END

IF NOT EXISTS (SELECT * FROM NEW_DR_DB.dbo.sysobjects WHERE ID = object_id(N'NEW_DR_DB..SSA_DR_Migration_Log')) 
BEGIN
	CREATE TABLE NEW_DR_DB..SSA_DR_Migration_Log (
		EAM_ID uniqueidentifier NOT NULL,
		ProductName nvarchar(50) Not Null,
		OldVersion nvarchar(10) Not Null,
		NewVersion nvarchar(10) Not Null,
		TableName nvarchar(30) Not Null,
		DefaultCount bigint Not Null,
		OldDatabaseCount bigint Not Null,
		NewDatabaseCount bigint Not Null,
		TotalTimeMillisecond int Not Null,
		StartTime datetime Not Null,
		EndTime datetime Not Null,
		CONSTRAINT PK_SSA_DR_Migration_Log PRIMARY KEY (EAM_ID, ProductName, NewVersion, TableName)
		)
END

DECLARE @Default bigint,
	@OldCount bigint,
	@NewCount bigint,
	@StartTime datetime,
	@EndTime datetime,
	@Transaction bigint,
	@OldVersion nvarchar(10),
	@NewVersion nvarchar(10),
	@ProductName nvarchar(50)
DECLARE @MessageString nvarchar(50)
DECLARE @TableName nvarchar(255)
DECLARE @TableCounter tinyint
DECLARE @EAM_ID uniqueidentifier

SET @OldVersion = '4.6'
SET @NewVersion = '4.6.1'
SET @ProductName = 'SSA DR'

--------------------------------------------------------
-- SSA_DR_Migration - fetch old
--------------------------------------------------------
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..SSA_DR_Migration
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..SSA_DR_Migration
	
	IF @OldCount > 0 
	BEGIN
		INSERT INTO NEW_DR_DB..SSA_DR_Migration ([ProductName],
				[OldVersion], [NewVersion],
				[TransactionCount],
				[TotalTimeMinute], [StartTime], [EndTime])
			SELECT [ProductName],
				[OldVersion], [NewVersion],
				[TransactionCount],
				DATEDIFF(mi, StartTime, EndTime), [StartTime], [EndTime]
			FROM OLD_DR_DB..SSA_DR_Migration
			ORDER BY ProductName, 
				NewVersion
	
		--------------------------------------------------------
		-- SSA_DR_Migration_Log - fetch old
		--------------------------------------------------------
		SELECT @Default = COUNT(*) FROM NEW_DR_DB..SSA_DR_Migration_Log
		SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..SSA_DR_Migration_Log
		
		TRUNCATE TABLE NEW_DR_DB..SSA_DR_Migration_Log
		INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log 
			(	EAM_ID,
				ProductName,
				OldVersion, 
				NewVersion,
				TableName, DefaultCount, OldDatabaseCount, NewDatabaseCount,
				TotalTimeMillisecond, StartTime, EndTime
			) SELECT 
				EAM_ID,
				ProductName,
				OldVersion, 
				NewVersion,
				TableName, DefaultCount, OldDatabaseCount, NewDatabaseCount,
				TotalTimeMillisecond, StartTime, EndTime
			FROM OLD_DR_DB..SSA_DR_Migration_Log
			ORDER BY ProductName, 
				NewVersion, 
				TableName

-- 		UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
-- 			EAM_ID = NEW_DR_DB..SSA_DR_Migration.EAM_ID
-- 		FROM NEW_DR_DB..SSA_DR_Migration
-- 		WHERE NEW_DR_DB..SSA_DR_Migration_Log.ProductName = NEW_DR_DB..SSA_DR_Migration.ProductName
-- 				AND NEW_DR_DB..SSA_DR_Migration_Log.NewVersion = NEW_DR_DB..SSA_DR_Migration.NewVersion

	END	

--------------------------------------------------------
-- SSA_DR_Migration Table Insert
--------------------------------------------------------
	SET @EAM_ID = NewID()	-- get a unique id for this run.
	SELECT @Transaction = 0
	SELECT @StartTime = Getdate()
	INSERT INTO NEW_DR_DB..SSA_DR_Migration
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, 0, @Transaction, @StartTime, @StartTime)

--------------------------------------------------------
-- Disable Constraints back to Alternate Vendor Table
--------------------------------------------------------
	IF EXISTS(SELECT * FROM NEW_DR_DB.dbo.sysobjects where id = object_id(N'[NEW_DR_DB]..[ALTERNATE_SOURCE]'))
	BEGIN
		ALTER Table NEW_DR_DB..ALTERNATE_SOURCE
		NOCHECK CONSTRAINT [FK_ALTERNATESOURCE_Item]
		
		ALTER TABLE NEW_DR_DB..ALTERNATE_SOURCE
		NOCHECK CONSTRAINT [FK_ALTERNATESOURCE_Vendor]
		PRINT 'Altered Table NEW_DR_DB..ALTERNATE_SOURCE - removed constraints.'
	END

-----------------------------------------------------------------------------------------
-- Start moving data from the temporary copy to the newly created database.
-- Tables affected by this script (version specific changes marked by *):
--	  1. AIMAO						--	  2. AIMAO_OrderInfo
--****3. AIMAODetail				--	  4. AIMClasses
--	  5. AIMCompanionItem			--	  6. AIMCompanionItemDetail
--	  7. AIMDataEXchangeCtrl		--	  8. AIMDays
--	  9. AIMDestinationProfile		--	 10. AIMFcstSetup
--	 11. AIMFcstMaster              --	 12. AIMFcstAccess
--	 13. AIMLeadTime				--	 14. AIMLocations
--	 15. AIMOptions					--	 16. AIMPO
--	 17. AIMProductionConstraint	--	 18. AIMProductionConstraintDetail
--	 19. AIMPromotions				--	 20. AIMRoles
--	 21. AIMSd						--	 22. AIMSeasons
--	 23. AIMSourcingHierarchy		--	 24. AIMTransferPolicy
--	 25. AIMUOM						--	 26. AIMUsers
--	 27. AIMVelCodePcts				--	 28. AIMVendors
--	 29. AIMWorkingDays				--	 30. AIMYears
--	 31. AllocDefaults				--	 32. AllocDefaultsSource
--	 33. AllocItemSubstitutes		--	 34. Alternate_Source
--	 35. BuyerStatusTest			--	 36. ForecastRepository
--	 37. ForecastRepository_Log		--	 38. ForecastRepositoryArch_Log
--	 39. ForecastRepositoryDetail   --	 40. ItDefaults
--***41. Item                       --	 42. ItemHistory
--	 43. ItemPricing                --	 44. ItemStatus
--	 45. MDCDetail                  --	 46. MDCSummary
--	 47. PODetail                   --	 48. POStatistics
--	 49. RevCycles                  --   50. UserElement
--	 51. UserElementDetail          --***52. SysCtrl
------------------------------------------------------------------------------------------
SET @MessageString = '- Record count: '
SET @TableCounter = 0
	--------------------------------------------------------
	-- 1. AIMAO
	--------------------------------------------------------
	SET @TableName = 'AIMAO'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMAO
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMAO
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMAO
	INSERT INTO NEW_DR_DB..AIMAO
		SELECT *
		FROM OLD_DR_DB..AIMAO
		ORDER BY ORDNBR

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMAO
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 2. AIMAO_OrderInfo
	--------------------------------------------------------
	SET @TableName =  'AIMAO_OrderInfo'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMAO_OrderInfo
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMAO_OrderInfo
	SET @OldCount = 0 
	SELECT @StartTime = Getdate()
	
	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMAO_OrderInfo
	INSERT INTO NEW_DR_DB..AIMAO_OrderInfo
		SELECT *
		FROM OLD_DR_DB..AIMAO_OrderInfo
		ORDER BY ORDNBR

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMAO_OrderInfo
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ New for this release @@@@@@@@@@@@@@@
	-- 3. AIMAODetail
	--------------------------------------------------------
	SET @TableName = 'AIMAODetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMAODetail
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMAODetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMAODetail
	INSERT INTO NEW_DR_DB..AIMAODetail (
		[OrdNbr],
		[OrdType],
		[LineNbr],
		[LineItemStatus],
		[Lcid],
		[LType],
		[Item],
		[ItDesc],
		[UOM],
		[AllocUOM],
		[AllocConvFactor],
		[RequestedQty],
		[AllocatedQty],
		[AdjustedAllocQty],
		[Cost],
		[LineFillPct],
		[TxnDate],
		[TxnTimeOfDay],
		[AllocationDate],
		[AllocationTimeOfDay]
	) SELECT 		
		[OrdNbr],
		[OrdType],
		[LineNbr],
		[LineItemStatus],
		[Lcid],
		[LType],
		[Item],
		[ItDesc],
		[UOM],
		'EA',
		1,
		[RequestedQty],
		[AllocatedQty],
		[AdjustedAllocQty],
		[Cost],
		[LineFillPct],
		[TxnDate],
		[TxnTimeOfDay],
		[AllocationDate],
		[AllocationTimeOfDay]
		FROM OLD_DR_DB..AIMAODetail
		ORDER BY ORDNBR, LINENBR

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMAODetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 4. AIMClasses
	--------------------------------------------------------
	SET @TableName = 'AIMClasses'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMClasses
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMClasses
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMClasses
	INSERT INTO NEW_DR_DB..AIMClasses
		SELECT *
		FROM OLD_DR_DB..AIMClasses
		ORDER BY Class

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMClasses
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 5. AIMCompanionItem
	--------------------------------------------------------
	SET @TableName = 'AIMCompanionItem'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMCompanionItem
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMCompanionItem
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMCompanionItem
	INSERT INTO NEW_DR_DB..AIMCompanionItem
		SELECT *
		FROM OLD_DR_DB..AIMCompanionItem
		ORDER BY MasterItem, Lcid

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMCompanionItem
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 6. AIMCompanionItemDetail
	--------------------------------------------------------
	SET @TableName = 'AIMCompanionItemDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMCompanionItemDetail
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMCompanionItemDetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMCompanionItemDetail
	INSERT INTO NEW_DR_DB..AIMCompanionItemDetail
		SELECT *
		FROM OLD_DR_DB..AIMCompanionItemDetail
		ORDER BY MasterItem, Lcid, Item

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMCompanionItemDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 7. AIMDataEXchangeCtrl
	--------------------------------------------------------
	SET @TableName = 'AIMDataExchangeCtrl'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMDataEXchangeCtrl
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMDataEXchangeCtrl
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMDataEXchangeCtrl
	INSERT INTO NEW_DR_DB..AIMDataEXchangeCtrl
		SELECT *
		FROM OLD_DR_DB..AIMDataEXchangeCtrl
		ORDER BY TxnSet

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMDataEXchangeCtrl
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 8. AIMDays
	--------------------------------------------------------
	SET @TableName = 'AIMDays(Modified)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMDays
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMDays
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMDays
	INSERT INTO NEW_DR_DB..AIMDays 
		SELECT [FYDate], [DayStatus], 0, 0, 0, 0, 0, 0, 0, 0
		FROM OLD_DR_DB..AIMDays
		ORDER BY FYDate

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMDays
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 9. AIMDestinationProfile
	--------------------------------------------------------
	SET @TableName = 'AIMDestinationProfile'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMDestinationProfile
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMDestinationProfile
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMDestinationProfile
	INSERT INTO NEW_DR_DB..AIMDestinationProfile
		SELECT * 
		FROM OLD_DR_DB..AIMDestinationProfile
		ORDER BY Lcid_Destination, Item

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMDestinationProfile
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 10. AIMFcstSetup
	--------------------------------------------------------
	SET @TableName = 'AIMFcstSetup(New)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMFcstSetup
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMFcstSetup
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMFcstSetup
	--INSERT INTO NEW_DR_DB..AIMFcstSetup
	--	SELECT * 
	--	FROM OLD_DR_DB..AIMFcstSetup
	--	ORDER BY FcstSetKey

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMFcstSetup
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 11. AIMFcstMaster
	--------------------------------------------------------
	SET @TableName = 'AIMFcstMaster(New)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMFcstMaster
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMFcstMaster
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMFcstMaster
	--INSERT INTO NEW_DR_DB..AIMFcstMaster
	--	SELECT * 
	--	FROM OLD_DR_DB..AIMFcstMaster
	--	ORDER BY Lcid, Item, PeriodBegDate

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMFcstMaster
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 12. AIMFcstAccess
	--------------------------------------------------------
	SET @TableName = 'AIMFcstAccess(New)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMFcstAccess
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMFcstAccess
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMFcstAccess
	--INSERT INTO NEW_DR_DB..AIMFcstAccess
	--	SELECT * 
	--	FROM OLD_DR_DB..AIMFcstAccess
	--	ORDER BY FcstSetupKey

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMFcstAccess
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 13. AIMLeadTime
	--------------------------------------------------------
	SET @TableName = 'AIMLeadTime'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMLeadTime
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMLeadTime
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMLeadTime
	INSERT INTO NEW_DR_DB..AIMLeadTime
		SELECT * 
		FROM OLD_DR_DB..AIMLeadTime
		ORDER BY LcId, Item

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMLeadTime
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 14. AIMLocations
	--------------------------------------------------------
	SET @TableName = 'AIMLocations'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMLocations
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMLocations
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMLocations
	INSERT INTO NEW_DR_DB..AIMLocations 
		SELECT * 
		FROM OLD_DR_DB..AIMLocations
		ORDER BY LcId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMLocations
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 15. AIMOptions
	--------------------------------------------------------
	SET @TableName = 'AIMOptions'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMOptions
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMOptions
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMOptions
	INSERT INTO NEW_DR_DB..AIMOptions
		SELECT * 
		FROM OLD_DR_DB..AIMOptions
		ORDER BY OptionId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMOptions
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 16. AIMPO
	--------------------------------------------------------
	SET @TableName = 'AIMPO'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMPO
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMPO
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMPO
	INSERT INTO NEW_DR_DB..AIMPO
		SELECT * 
		FROM OLD_DR_DB..AIMPO
		ORDER BY ById, VnId, Assort

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMPO
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 17. AIMProductionConstraint
	--------------------------------------------------------
	SET @TableName = 'AIMProductionConstraint'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMProductionConstraint
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMProductionConstraint
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	DELETE NEW_DR_DB..AIMProductionConstraintDetail
	DELETE NEW_DR_DB..AIMProductionConstraint
	INSERT INTO NEW_DR_DB..AIMProductionConstraint
		SELECT * 
		FROM OLD_DR_DB..AIMProductionConstraint
		ORDER BY ConstraintId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMProductionConstraint
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 18. AIMProductionConstraintDetail
	--------------------------------------------------------
	SET @TableName = 'AIMProductionConstraintDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMProductionConstraintDetail
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMProductionConstraintDetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)
	
	DELETE NEW_DR_DB..AIMProductionConstraintDetail
	INSERT INTO NEW_DR_DB..AIMProductionConstraintDetail
		SELECT * 
		FROM OLD_DR_DB..AIMProductionConstraintDetail
		ORDER BY ConstraintId, Item

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMProductionConstraintDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 19. AIMPromotions
	--------------------------------------------------------
	SET @TableName = 'AIMPromotions'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMPromotions
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMPromotions
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMPromotions
	INSERT INTO NEW_DR_DB..AIMPromotions
		SELECT * 
		FROM OLD_DR_DB..AIMPromotions
		ORDER BY PmId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMPromotions
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 20. AIMRoles
	--------------------------------------------------------
	SET @TableName = 'AIMRoles'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMRoles
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMRoles
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMRoles
	INSERT INTO NEW_DR_DB..AIMRoles
		SELECT * 
		FROM OLD_DR_DB..AIMRoles
		ORDER BY RoleId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMRoles
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 21. AIMSd
	--------------------------------------------------------
	SET @TableName = 'AIMSd'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMSd
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMSd
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMSd
	INSERT INTO NEW_DR_DB..AIMSd
		SELECT *
		FROM OLD_DR_DB..AIMSd

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMSd
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 22. AIMSeasons
	--------------------------------------------------------
	SET @TableName = 'AIMSeasons'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMSeasons
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMSeasons
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMSeasons
	INSERT INTO NEW_DR_DB..AIMSeasons
		SELECT * 
		FROM OLD_DR_DB..AIMSeasons
		ORDER BY SaVersion, SaId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMSeasons
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 23. AIMSourcingHierarchy
	--------------------------------------------------------
	SET @TableName = 'AIMSourcingHierarchy'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMSourcingHierarchy
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMSourcingHierarchy
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMSourcingHierarchy
	INSERT INTO NEW_DR_DB..AIMSourcingHierarchy
		SELECT * 
		FROM OLD_DR_DB..AIMSourcingHierarchy
		ORDER BY Lcid_Destination, Lcid_Source

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMSourcingHierarchy
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 24. AIMTransferPolicy
	--------------------------------------------------------
	SET @TableName = 'AIMTransferPolicy'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMTransferPolicy
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMTransferPolicy
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMTransferPolicy
	INSERT INTO NEW_DR_DB..AIMTransferPolicy
		SELECT * 
		FROM OLD_DR_DB..AIMTransferPolicy
		ORDER BY Lcid, LcidTransferTo, TransferType

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMTransferPolicy
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 25. AIMUOM
	--------------------------------------------------------
	SET @TableName = 'AIMUOM'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMUOM
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMUOM
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMUOM
	INSERT INTO NEW_DR_DB..AIMUOM
		SELECT * 
		FROM OLD_DR_DB..AIMUOM
		ORDER BY UOM

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMUOM
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 26. AIMUsers
	--------------------------------------------------------
	SET @TableName = 'AIMUsers'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMUsers
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMUsers
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMUsers
	INSERT INTO NEW_DR_DB..AIMUsers 
		SELECT [UserId], [UserName], [SLevel], [ScArray], [DmdUpdExcpts],
			[OnPromotionExcpts], [ExtCostExcpts], [ExtCostLimit], [PackSizeExcpts],
			[PS_PctRoundLimit], [PS_ExtCostLimit], [LastWeekSaleExcpts], [OverDuePO],
			[SafetyStockEroded], [BackOrdered], [InvAvailRestriction], [InvAvailList],
			[UserInitials], [LangID], [LogErrors], [AltSourceExcpts], [POExtCost], [POExtCostLimit], [RoleId],'N'
		FROM OLD_DR_DB..AIMUsers
		ORDER BY UserId

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMUsers
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 27. AIMVelCodePcts
	--------------------------------------------------------
	SET @TableName = 'AIMVelCodePcts'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMVelCodePcts
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMVelCodePcts
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMVelCodePcts
	INSERT INTO NEW_DR_DB..AIMVelCodePcts
		SELECT * 
		FROM OLD_DR_DB..AIMVelCodePcts
		ORDER BY VelCode

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMVelCodePcts
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 28. AIMVendors
	--------------------------------------------------------
	SET @TableName = 'AIMVendors'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMVendors
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMVendors
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	DELETE NEW_DR_DB..AIMVendors
	INSERT INTO NEW_DR_DB..AIMVendors
		SELECT * 
		FROM OLD_DR_DB..AIMVendors
		ORDER BY VnId, Assort

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMVendors
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 29. AIMWorkingDays
	--------------------------------------------------------
	SET @TableName = 'AIMWorkingDays'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMWorkingDays
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMWorkingDays
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMWorkingDays
	INSERT INTO NEW_DR_DB..AIMWorkingDays
		SELECT * 
		FROM OLD_DR_DB..AIMWorkingDays

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMWorkingDays
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 30. AIMYears
	--------------------------------------------------------
	SET @TableName = 'AIMYears'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AIMYears
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AIMYears
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AIMYears

	DECLARE @FirstFiscalYear as int
	DECLARE @NbrFiscalYears as int
	DECLARE @FYStartDate as datetime
	DECLARE @RC int
	DECLARE @PrnLine nvarchar(4000)

	SELECT @NbrFiscalYears  = COUNT(*)
		FROM OLD_DR_DB..AIMYears AIMY
	SELECT TOP 1 @FirstFiscalYear = AIMY.FiscalYear,
		@FYStartDate = AIMY.FYStartDate
	FROM OLD_DR_DB..AIMYears AIMY

	-- call SSAAIMCalendar init here -- this will insert into AIMYears and AIMDays
	PRINT 'Calling NEW_DR_DB.dbo.AIM_Calendar_Init_Sp with FYStartDate: ' + CONVERT(NVARCHAR(50), @FYStartDate)
	PRINT '	and FirstFiscalYear: ' + CONVERT(NVARCHAR(50), @FirstFiscalYear)
	PRINT '	and NbrFiscalYears: ' + CONVERT(NVARCHAR(50), @NbrFiscalYears)
	EXEC @RC = [NEW_DR_DB].[dbo].[AIM_Calendar_Init_Sp] @FirstFiscalYear, @FYStartDate, @NbrFiscalYears
	PRINT 'Stored Procedure: NEW_DR_DB.dbo.AIM_Calendar_Init_Sp'
	SELECT @PrnLine = '	Return Code = ' + CONVERT(nvarchar, @RC)
	PRINT @PrnLine

	--INSERT INTO NEW_DR_DB..AIMYears
	--	SELECT * 
	--	FROM OLD_DR_DB..AIMYears
	--	ORDER BY FiscalYear

	--SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AIMYears
	--SELECT @EndTime = Getdate()
	--SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 31. AllocDefaults
	--------------------------------------------------------
	SET @TableName = 'AllocDefaults'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AllocDefaults
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AllocDefaults
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AllocDefaults 
	SET IDENTITY_INSERT NEW_DR_DB..AllocDefaults ON
	INSERT INTO NEW_DR_DB..AllocDefaults ([AllocDefaultsId], [LStatus], [LDivision], [LRegion], [LUserDefined],
		[ExceptionPct],  [ReviewerId], [LRank])
		SELECT * 
		FROM OLD_DR_DB..AllocDefaults
		ORDER BY AllocDefaultsId
	SET IDENTITY_INSERT NEW_DR_DB..AllocDefaults OFF

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AllocDefaults
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 32. AllocDefaultsSource
	--------------------------------------------------------
	SET @TableName = 'AllocDefaultsSource'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AllocDefaultsSource
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AllocDefaultsSource
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AllocDefaultsSource
	INSERT INTO NEW_DR_DB..AllocDefaultsSource
		SELECT * 
		FROM OLD_DR_DB..AllocDefaultsSource
		ORDER BY AllocDefaultsId, Lcid_Source

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AllocDefaultsSource
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 33. AllocItemSubstitutes
	--------------------------------------------------------
	SET @TableName = 'AllocItemSubstitutes'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..AllocItemSubstitutes
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..AllocItemSubstitutes
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..AllocItemSubstitutes
	INSERT INTO NEW_DR_DB..AllocItemSubstitutes
		SELECT * 
		FROM OLD_DR_DB..AllocItemSubstitutes

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..AllocItemSubstitutes
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 34. Alternate_Source
	--------------------------------------------------------
	SET @TableName = 'Alternate_Source'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..Alternate_Source
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..Alternate_Source
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..Alternate_Source
	INSERT INTO NEW_DR_DB..Alternate_Source
		SELECT * 
		FROM OLD_DR_DB..Alternate_Source
		ORDER BY Item, LcID, VnID

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..Alternate_Source
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 35. BuyerStatusTest
	--------------------------------------------------------
	SET @TableName = 'BuyerStatusTest'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..BuyerStatusTest
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..BuyerStatusTest
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..BuyerStatusTest
	SET IDENTITY_INSERT NEW_DR_DB..BuyerStatusTest ON
	INSERT INTO NEW_DR_DB..BuyerStatusTest ([KeyId], [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines],
		[Released Lines],  [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts],
		[Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews],
		[Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts])
		SELECT * 
		FROM OLD_DR_DB..BuyerStatusTest
	SET IDENTITY_INSERT NEW_DR_DB..BuyerStatusTest OFF

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..BuyerStatusTest
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 36. ForecastRepository
	--------------------------------------------------------
	SET @TableName = 'ForecastRepository(Modified)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ForecastRepository
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ForecastRepository
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ForecastRepository
	--SET IDENTITY_INSERT NEW_DR_DB..ForecastRepository ON
	--INSERT INTO NEW_DR_DB..ForecastRepository ([RepositoryKey], [FcstId], [FcstDesc], 0,
	--	[FcstComment], [UserIdCreate], [DateTimeCreate],
	--	[UserIdEdit], [DateTimeEdit])
	--	SELECT * 
	--	FROM OLD_DR_DB..ForecastRepository
	--SET IDENTITY_INSERT NEW_DR_DB..ForecastRepository OFF

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ForecastRepository
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 37. ForecastRepository_Log
	--------------------------------------------------------
	SET @TableName = 'ForecastRepository_Log(Modified)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ForecastRepository_Log
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ForecastRepository_Log
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ForecastRepository_Log
	--SET IDENTITY_INSERT NEW_DR_DB..ForecastRepository_Log ON
	--INSERT INTO NEW_DR_DB..ForecastRepository_Log ([FcstRepositoyKey], [FcstId], [FcstType], [UserIdEdit], [DateTimeEdit])
	--	SELECT * 
	--	FROM OLD_DR_DB..ForecastRepository_Log
	--SET IDENTITY_INSERT NEW_DR_DB..ForecastRepository_Log OFF

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ForecastRepository_Log
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 38. ForecastRepositoryArch_Log
	--------------------------------------------------------
	SET @TableName = 'ForecastRepositoryArch_Log(New)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ForecastRepositoryArch_Log
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ForecastRepositoryArch_Log
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ForecastRepositoryArch_Log
	--SET IDENTITY_INSERT NEW_DR_DB..ForecastRepositoryArch_Log ON
	--INSERT INTO NEW_DR_DB..ForecastRepositoryArch_Log ([FcstRepositoyKey], [FcstId], [FcstType], [UserIdEdit], [DateTimeEdit])
	--	SELECT * 
	--	FROM OLD_DR_DB..ForecastRepositoryArch_Log
	--SET IDENTITY_INSERT NEW_DR_DB..ForecastRepositoryArch_Log OFF

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ForecastRepositoryArch_Log
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 39. ForecastRepositoryDetail
	--------------------------------------------------------
	SET @TableName = 'ForecastRepositoryDetail(Modified)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ForecastRepositoryDetail
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ForecastRepositoryDetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ForecastRepositoryDetail
	--INSERT INTO NEW_DR_DB..ForecastRepositoryDetail
	--	SELECT * 
	--	FROM OLD_DR_DB..ForecastRepositoryDetail
	--	ORDER BY RepositoryKey, Lcid, Item, FcstPdBegDate

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ForecastRepositoryDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 40. ItDefaults
	--------------------------------------------------------
	SET @TableName = 'ItDefaults'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ItDefaults
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ItDefaults
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ItDefaults
	INSERT INTO NEW_DR_DB..ItDefaults
		SELECT * 
		FROM OLD_DR_DB..ItDefaults
		ORDER BY LcId, Class

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ItDefaults
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ New for this release @@@@@@@@@@@@@@@
	-- 41. Item
	--------------------------------------------------------
	SET @TableName = 'Item'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..Item
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..Item
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	DELETE NEW_DR_DB..Item
	INSERT INTO NEW_DR_DB..Item (
		[Lcid], [Item], [ItDesc], [ItStat], [ActDate], [InActDate], [OptionId],
		[Class1], [Class2], [Class3], [Class4], [BinLocation], [BuyStrat],
		[VelCode], [VnId], [Assort], [ById], [MDC], [MDCFlag], [SaId], [PmId],
		[UPC], [Weight], [Cube], [ListPrice], [Price], [Cost], [BkQty01], [BkCost01],
		[BkQty02], [BkCost02], [BkQty03], [BkCost03], [BkQty04], [BkCost04],
		[BkQty05], [BkCost05], [BkQty06], [BkCost06], [BkQty07], [BkCost07],
		[BkQty08], [BkCost08], [BkQty09], [BkCost09], [BkQty10], [BkCost10],
		[UOM], [ConvFactor], [BuyingUOM], [ReplenCost2], [Oh], [Oo], [ComStk],
		[BkOrder], [BkComStk], [LeadTime], [PackRounding], [IMin], [IMax],
		[CStock], [SSAdj], [UserMin], [UserMax], [UserMethod], [FcstMethod],
		[FcstDemand], [UserFcst], [UserFcstExpDate], [MAE], [MSE], [Trend],
		[FcstCycles], [ZeroCount], [Mean_NZ], [StdDev_NZ], [IntSafetyStock],
		[IsIntermittent], [DIFlag], [DmdFilterFlag], [TrkSignalFlag], 
		[UserDemandFlag], [ByPassPct], [FcstUpdCyc], [LTVFact], [PlnTT],
		[ZOPSw], [OUTLSw], [ZSStock], [DSer], [Freeze_BuyStrat], [Freeze_Byid],
		[Freeze_Leadtime], [Freeze_OptionID], [Freeze_DSer], [OldItem], 
		[Accum_Lt], [ReviewTime], [OrderPt], [OrderQty], [SafetyStock],
		[FcstRT], [FcstLT], [Fcst_Month], [Fcst_Quarter], [Fcst_Year],
		[FcstDate], [VC_Amt], [VC_Units_Ranking], [VC_Amt_Ranking], 
		[VC_Date], [VelCode_Prev], [VC_Amt_Prev], [OnPromotion], [AvgOh],
		[NextPONbr_1], [NextPODate_1], [NextPOQty_1], 
		[NextPONbr_2], [NextPODate_2], [NextPOQty_2], 
		[NextPONbr_3], [NextPODate_3], [NextPOQty_3], 
		[UserRef1], [UserRef2], [UserRef3], [Freeze_Forecast], 
		[ProductionConstraint], [AllocatableQty], [AltVnFlag], 
		[KitBOMFlag], [DependentFcstDemand]
		, [AllocUOM], [AllocConvFactor]
	) SELECT 
		[Lcid], [Item], [ItDesc], [ItStat], [ActDate], [InActDate], [OptionId],
		[Class1], [Class2], [Class3], [Class4], [BinLocation], [BuyStrat],
		[VelCode], [VnId], [Assort], [ById], [MDC], [MDCFlag], [SaId], [PmId],
		[UPC], [Weight], [Cube], [ListPrice], [Price], [Cost], [BkQty01], [BkCost01],
		[BkQty02], [BkCost02], [BkQty03], [BkCost03], [BkQty04], [BkCost04],
		[BkQty05], [BkCost05], [BkQty06], [BkCost06], [BkQty07], [BkCost07],
		[BkQty08], [BkCost08], [BkQty09], [BkCost09], [BkQty10], [BkCost10],
		[UOM], [ConvFactor], [BuyingUOM], [ReplenCost2], [Oh], [Oo], [ComStk],
		[BkOrder], [BkComStk], [LeadTime], [PackRounding], [IMin], [IMax],
		[CStock], [SSAdj], [UserMin], [UserMax], [UserMethod], [FcstMethod],
		[FcstDemand], [UserFcst], [UserFcstExpDate], [MAE], [MSE], [Trend],
		[FcstCycles], [ZeroCount], [Mean_NZ], [StdDev_NZ], [IntSafetyStock],
		[IsIntermittent], [DIFlag], [DmdFilterFlag], [TrkSignalFlag], 
		[UserDemandFlag], [ByPassPct], [FcstUpdCyc], [LTVFact], [PlnTT],
		[ZOPSw], [OUTLSw], [ZSStock], [DSer], [Freeze_BuyStrat], [Freeze_Byid],
		[Freeze_Leadtime], [Freeze_OptionID], [Freeze_DSer], [OldItem], 
		[Accum_Lt], [ReviewTime], [OrderPt], [OrderQty], [SafetyStock],
		[FcstRT], [FcstLT], [Fcst_Month], [Fcst_Quarter], [Fcst_Year],
		[FcstDate], [VC_Amt], [VC_Units_Ranking], [VC_Amt_Ranking], 
		[VC_Date], [VelCode_Prev], [VC_Amt_Prev], [OnPromotion], [AvgOh],
		[NextPONbr_1], [NextPODate_1], [NextPOQty_1], 
		[NextPONbr_2], [NextPODate_2], [NextPOQty_2], 
		[NextPONbr_3], [NextPODate_3], [NextPOQty_3], 
		[UserRef1], [UserRef2], [UserRef3], [Freeze_Forecast], 
		[ProductionConstraint], ISNULL([AllocatableQty], 0), [AltVnFlag], 
		[KitBOMFlag], [DependentFcstDemand]
		, 'EA', 1
	FROM OLD_DR_DB..Item
	ORDER BY LcId, Item

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..Item
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 42. ItemHistory
	--------------------------------------------------------
	SET @TableName = 'ItemHistory'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ItemHistory
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ItemHistory
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ItemHistory
	INSERT INTO NEW_DR_DB..ItemHistory
		SELECT [LcId], [Item], [SubsItem], [HisYear],
		[CPS01], [CPS02], [CPS03], [CPS04], [CPS05], [CPS06], [CPS07], [CPS08], [CPS09], [CPS10],
		[CPS11], [CPS12], [CPS13], [CPS14], [CPS15], [CPS16], [CPS17], [CPS18], [CPS19], [CPS20],
		[CPS21], [CPS22], [CPS23], [CPS24], [CPS25], [CPS26], [CPS27], [CPS28], [CPS29], [CPS30],
		[CPS31], [CPS32], [CPS33], [CPS34], [CPS35], [CPS36], [CPS37], [CPS38], [CPS39], [CPS40],
		[CPS41], [CPS42], [CPS43], [CPS44], [CPS45], [CPS46], [CPS47], [CPS48], [CPS49], [CPS50],
		[CPS51], [CPS52],
		[QtyOrd01], [QtyOrd02], [QtyOrd03], [QtyOrd04], [QtyOrd05], [QtyOrd06], [QtyOrd07], [QtyOrd08], [QtyOrd09], [QtyOrd10],
		[QtyOrd11], [QtyOrd12], [QtyOrd13], [QtyOrd14], [QtyOrd15], [QtyOrd16], [QtyOrd17], [QtyOrd18], [QtyOrd19], [QtyOrd20],
		[QtyOrd21], [QtyOrd22], [QtyOrd23], [QtyOrd24], [QtyOrd25], [QtyOrd26], [QtyOrd27], [QtyOrd28], [QtyOrd29], [QtyOrd30],
		[QtyOrd31], [QtyOrd32], [QtyOrd33], [QtyOrd34], [QtyOrd35], [QtyOrd36], [QtyOrd37], [QtyOrd38], [QtyOrd39], [QtyOrd40],
		[QtyOrd41], [QtyOrd42], [QtyOrd43], [QtyOrd44], [QtyOrd45], [QtyOrd46], [QtyOrd47], [QtyOrd48], [QtyOrd49], [QtyOrd50],
		[QtyOrd51], [QtyOrd52],
		[OrdCnt01], [OrdCnt02], [OrdCnt03], [OrdCnt04], [OrdCnt05], [OrdCnt06], [OrdCnt07], [OrdCnt08], [OrdCnt09], [OrdCnt10],
		[OrdCnt11], [OrdCnt12], [OrdCnt13], [OrdCnt14], [OrdCnt15], [OrdCnt16], [OrdCnt17], [OrdCnt18], [OrdCnt19], [OrdCnt20],
		[OrdCnt21], [OrdCnt22], [OrdCnt23], [OrdCnt24], [OrdCnt25], [OrdCnt26], [OrdCnt27], [OrdCnt28], [OrdCnt29], [OrdCnt30],
		[OrdCnt31], [OrdCnt32], [OrdCnt33], [OrdCnt34], [OrdCnt35], [OrdCnt36], [OrdCnt37], [OrdCnt38], [OrdCnt39], [OrdCnt40],
		[OrdCnt41], [OrdCnt42], [OrdCnt43], [OrdCnt44], [OrdCnt45], [OrdCnt46], [OrdCnt47], [OrdCnt48], [OrdCnt49], [OrdCnt50],
		[OrdCnt51], [OrdCnt52]	  
 		FROM OLD_DR_DB..ItemHistory
		ORDER BY LcId, Item, HisYear

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ItemHistory
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 43. ItemPricing
	--------------------------------------------------------
	SET @TableName = 'ItemPricing'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ItemPricing
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ItemPricing
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ItemPricing
	INSERT INTO NEW_DR_DB..ItemPricing
		SELECT * 
		FROM OLD_DR_DB..ItemPricing
		ORDER BY LcId, Item, EffectiveDateTime

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ItemPricing
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 44. ItemStatus
	--------------------------------------------------------
	SET @TableName = 'ItStatus'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..ItStatus
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..ItStatus
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..ItStatus
	INSERT INTO NEW_DR_DB..ItStatus
		SELECT * 
		FROM OLD_DR_DB..ItStatus
	
	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..ItStatus
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	
	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 45. MDCDetail
	--------------------------------------------------------
	SET @TableName = 'MDCDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..MDCDetail
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..MDCDetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..MDCDetail
	INSERT INTO NEW_DR_DB..MDCDetail
		SELECT * 
		FROM OLD_DR_DB..MDCDetail
		ORDER BY LcId, Item
	
	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..MDCDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	
	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 46. MDCSummary
	--------------------------------------------------------
	SET @TableName = 'MDCSummary'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..MDCSummary
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..MDCSummary
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..MDCSummary
	INSERT INTO NEW_DR_DB..MDCSummary
		SELECT * FROM OLD_DR_DB..MDCSummary
		ORDER BY MDC, Item

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..MDCSummary
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 47. PODetail
	--------------------------------------------------------
	SET @TableName = 'PODetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..PODetail
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..PODetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..PODetail
	SET IDENTITY_INSERT NEW_DR_DB..PODetail ON
	INSERT INTO NEW_DR_DB..PODetail ([POLineType], [POSeqID], [OrdStatus], [RevCycle], [ById], [VnId], [Assort],
		[Lcid], [Item], [ItDesc], [POType], [AvailQty], [PackRounding], [RSOQ], [SOQ], [VSOQ], [Original_VSOQ],
		[UOM], [BuyingUOM], [ConvFactor], [IsDate], [DuDate], [LastWeekSalesFlag], [Cost], [Zone])
		SELECT * FROM OLD_DR_DB..PODetail
	SET IDENTITY_INSERT NEW_DR_DB..PODetail OFF

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..PODetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 48. POStatistics
	--------------------------------------------------------
	SET @TableName = 'POStatistics'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..POStatistics
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..POStatistics
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..POStatistics
	INSERT INTO NEW_DR_DB..POStatistics
		SELECT * FROM OLD_DR_DB..POStatistics
		ORDER BY SeqNbr, ById, VnId, Assort

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..POStatistics
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 49. RevCycles
	--------------------------------------------------------
	SET @TableName = 'RevCycles'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..RevCycles
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..RevCycles
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..RevCycles
	INSERT INTO NEW_DR_DB..RevCycles
		SELECT * FROM OLD_DR_DB..RevCycles
		ORDER BY RevCycle

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..RevCycles
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 50. User Element
	--------------------------------------------------------
	SET @TableName = 'UserElement(New)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..UserElement
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..UserElement
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..UserElement
	--INSERT INTO NEW_DR_DB..UserElement
	--	SELECT * FROM OLD_DR_DB..UserElement
	--	ORDER BY FcstID

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..UserElement
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 51. User Elements Detail
	--------------------------------------------------------
	SET @TableName = 'UserElementDetail(New)'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..UserElementDetail
	--SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..UserElementDetail
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..UserElementDetail
	--INSERT INTO NEW_DR_DB..UserElementDetail
	--	SELECT * FROM OLD_DR_DB..UserElementDetail
	--	ORDER BY FcstID

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..UserElementDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ New for this release @@@@@@@@@@@@@@@
	-- 52. SysCtrl
	--------------------------------------------------------
	SET @TableName = 'SysCtrl'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM NEW_DR_DB..SysCtrl
	SELECT @OldCount = COUNT(*) FROM OLD_DR_DB..SysCtrl
	SELECT @StartTime = Getdate()

	INSERT INTO NEW_DR_DB..SSA_DR_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE NEW_DR_DB..SysCtrl
	INSERT INTO NEW_DR_DB..SysCtrl (
		[CName], [Address1], [Address2], [Address3], [City], [State], [CoZip],
		[Dft_MinRchPct], [Dft_BestRchPct],
		[DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus],
		[DxPO_Option], [KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode],
		[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], [SeasSmoothingPds],
		[CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha],
		[UpdateGOalsOption], [UpdateVnLT], [VnLTPctChgFilter],
		[VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct],
		[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex],
		[Int_SrvLvlOverrideFlag], [Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath],
		[dft_LangID], [ColonOption], [GridAutoSizeOption], [DateFormat], [TimeFormat], [UnicodeOption],
		[ProductionConstraint], [ProdConstraintGrtZero],[AllocExceptionPct],
		[SessionId], [AllocNeedDetermination], [AllocExceptionProcess], [CarryForwardRounding],
		[LastOrderGenerationProcess], [MasterItemOption], [CreateDBUser], [UpdateDBPassword]
		, [AllocPrepackEnabled]
	) SELECT 
		[CName], [Address1], [Address2], [Address3], [City], [State], [CoZip],
		[Dft_MinRchPct], [Dft_BestRchPct],
		[DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus],
		[DxPO_Option], [KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode],
		[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], [SeasSmoothingPds],
		[CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha],
		[UpdateGOalsOption], [UpdateVnLT], [VnLTPctChgFilter],
		[VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct],
		[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex],
		[Int_SrvLvlOverrideFlag], [Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath],
		[dft_LangID], [ColonOption], [GridAutoSizeOption], [DateFormat], [TimeFormat], [UnicodeOption],
		[ProductionConstraint], [ProdConstraintGrtZero],[AllocExceptionPct],
		[SessionId], [AllocNeedDetermination], [AllocExceptionProcess], [CarryForwardRounding],
		[LastOrderGenerationProcess], [MasterItemOption], [CreateDBUser], [UpdateDBPassword]
		, 0
	FROM OLD_DR_DB..SysCtrl
	ORDER BY CName

	SELECT @NewCount = COUNT(*) FROM NEW_DR_DB..SysCtrl
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE NEW_DR_DB..SSA_DR_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

--------------------------------------------------------
-- SSA_DR_Migration Table Update
--------------------------------------------------------
	SELECT @StartTime = StartTime FROM NEW_DR_DB..SSA_DR_Migration WHERE NewVersion = @NewVersion
	UPDATE NEW_DR_DB..SSA_DR_Migration SET 
		TransactionCount = @Transaction,
		EndTime = @EndTime
	WHERE EAM_ID = @EAM_ID

	UPDATE NEW_DR_DB..SSA_DR_Migration SET 
			TotalTimeMinute = DATEDIFF(mi, StartTime, EndTime)

--------------------------------------------------------
-- SSA_DR_Migration Display
--------------------------------------------------------
	SELECT * FROM NEW_DR_DB..SSA_DR_Migration WHERE NewVersion = @NewVersion
--------------------------------------------------------
-- SSA_DR_Migration_Log Display
--------------------------------------------------------
	SELECT * FROM NEW_DR_DB..SSA_DR_Migration_Log WHERE NewVersion = @NewVersion

--------------------------------------------------------
-- Re-Enable Constraints back to Alternate Vendor Table
--------------------------------------------------------
	IF EXISTS(SELECT * FROM NEW_DR_DB.dbo.sysobjects where id = object_id(N'[NEW_DR_DB]..[ALTERNATE_SOURCE]'))
	BEGIN
		ALTER Table NEW_DR_DB..ALTERNATE_SOURCE
		CHECK CONSTRAINT [FK_ALTERNATESOURCE_Item]
		
		ALTER TABLE NEW_DR_DB..ALTERNATE_SOURCE
		CHECK CONSTRAINT [FK_ALTERNATESOURCE_Vendor]
		PRINT 'Altered Table NEW_DR_DB..ALTERNATE_SOURCE - added constraints.'
	END

PRINT 'Finished with SSA DR database migration script.'

------------------------------------------------------------------------------------------
-- Update SSA DR Database Statistics.
------------------------------------------------------------------------------------------

PRINT 'Updating SSA DR database statistics.'

USE NEW_DR_DB

Execute sp_updatestats

------------------------------------------------------------------------------------------
-- End Script.
------------------------------------------------------------------------------------------

SET NOCOUNT OFF
