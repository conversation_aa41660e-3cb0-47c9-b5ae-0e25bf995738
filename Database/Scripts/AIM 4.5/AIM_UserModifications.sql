--------*************************-----------------------*************************---------------

--		PLEASE TAKE BACK OF THE DATABASE BEFORE RUNNING THIS SCRIPT
--------*************************-----------------------*************************---------------




-----------------AUTHOR SUJIT SURVE

--ALTER TABLE [dbo].[AIMUsers] ADD [poextcost] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
--	[poextcostlimit] [decimal](18, 0) NULL ,
--	[RoleId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
--	CONSTRAINT AddROLEID
--	DEFAULT 'SA' WITH VALUES
--GO



--ALTER TABLE [dbo].[SysCtrl] ADD [CreateDBUser] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
--	[UpdateDBPassword] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
--GO

-----------------AUTHOR SUJIT SURVE
--if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Roles]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
--drop table [dbo].[AIM_Roles]
--GO

--CREATE TABLE [dbo].[AIM_Roles] (
--	[RoleId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
--	[RoleDescription] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
--	[RoleLevel] [int] NULL ,
--	[scarray] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
--	[RepToRoleID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
--	[AltSourceExcpts] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
--) ON [PRIMARY]
--GO

INSERT INTO [DBO].[AIM_Roles] VALUES(
'SA','Sys Admin',0,'YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY','SA','Y'
)

INSERT INTO [DBO].[AIM_Roles] VALUES(
'BasicUser1','Basic User1',0,'NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN','SA','Y'
)

UPDATE [dbo].[AIMUsers] SET ROLEID='BasicUser1' 

UPDATE [dbo].[AIMUsers] SET ROLEID='SA' WHERE USERID='SA'
---------------------

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMRoles_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMRoles_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMRoles_List_Sp_ALL]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMRoles_List_Sp_ALL]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VerifyAccessRole_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VerifyAccessRole_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_getSubRole_SP]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_getSubRole_SP]
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS OFF 
GO

/*******************************************************************************
**	Name: AIM_AIMRoles_List_Sp
**	Desc: RETURNS ALL ROLES AND REPORTS  TO ROLE RELATIONAL INFORMATION
**
**	Returns: 1)  ALL ROLES
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
CREATE PROCEDURE AIM_AIMRoles_List_Sp
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
DECLARE @found					int
SET NOCOUNT ON
SELECT AR.ROLEID,AR.ROLEDESCRIPTION,AR.ROLELEVEL,AR.SCARRAY,AR.REPTOROLEID,AR.ALTSOURCEEXCPTS,AR1.ROLELEVEL AS REPTOROLELEVEL
FROM aim_roles AR,AIM_ROLES AR1
WHERE AR.RepToRoleID=AR1.ROLEID
ORDER BY AR.ROLEDESCRIPTION
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
  	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN @found  -- SUCCESSFUL
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


/*******************************************************************************
**	Name: AIM_AIMRoles_List_Sp_ALL
**	Desc: RETURNS ALL ROLES
**
**	Returns: 1)  ALL ROLES
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
CREATE PROCEDURE AIM_AIMRoles_List_Sp_ALL
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
DECLARE @found					int
SET NOCOUNT ON
SELECT ROLEID,ROLEDESCRIPTION,ROLELEVEL,SCARRAY,REPTOROLEID,ALTSOURCEEXCPTS
FROM aim_roles 
ORDER BY ROLEDESCRIPTION
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
  	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
	RETURN @found  -- SUCCESSFUL
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS OFF 
GO

/*******************************************************************************
**	Name: AIM_VerifyAccess_Sp
**	Desc: Verifies a user's access level to the AIM Functions.
**
**	Returns: 1)  1 - Read Write Access
**             2)  2 - Full Access		
**             3)  0 - No Access
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
CREATE PROCEDURE AIM_VerifyAccessRole_Sp
(
      	@ROLEID             					nvarchar(12),
       	@AIMFunction        					int,
      	@AIMFunctionDesc    					nvarchar(40) OUTPUT
)    
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
DECLARE
     	@AccLvl     		    				nvarchar(1),
        @ScArray    		    				nvarchar(100),
        @SLevel     		    				tinyint,
    	@Rowcount			         			int 
            
SET NOCOUNT ON
-- Validate the required parameters.
IF @ROLEID IS NULL
BEGIN
	RETURN 0
END
IF @AIMFunction IS NULL
BEGIN
	RETURN 0
END
SELECT @ScArray = ScArray--, @SLevel = SLevel
FROM AIM_ROLES
WHERE ROLEID = @ROLEID
Select @rowcount = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN 0  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
        
IF @rowcount = 0       -- Not Found
BEGIN
       RETURN 0
END
ELSE                    -- Found
BEGIN
        -- Get the access level for this AIM function
        SELECT @AccLvl = substring(@ScArray, @AIMFunction, 1)
        
        SELECT @AIMFunctionDesc = FuncDesc 
	FROM AIMFunctions
        WHERE CtrlNbr = @AIMFunction
  	-- Check for SQL Server errors.
  	IF @@ERROR <> 0 
  	BEGIN
 		RETURN 0  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END
        
        IF @AccLvl = 'N'
            RETURN 0
        IF @AccLvl = 'R'
            RETURN 1
        IF @AccLvl = 'Y'
            RETURN 2
END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS OFF 
GO

/*******************************************************************************
**	Name: AIM_getSubRole_SP
**	Desc: TO GET SUBROLES
**
**	Returns: 1)  LIST OF ROLES
**             
**	Values:  
**              
**	AUTHOR 			SUJIT SURVE
**	Date:  			03/02/2004 
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:  	Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/

CREATE PROCEDURE AIM_getSubRole_SP( @gRole0 as nvarchar(12),@@TOTROLES as nvarchar(4000)  output )
AS

declare @@gRole0 as nvarchar(12)
declare @@gRole1 as nvarchar(12)
declare @@gRole2 as nvarchar(12)
declare @@gRole3 as nvarchar(12)
declare @@gRole4 as nvarchar(12)
declare @@gRole5 as nvarchar(12)
declare @@gRole6 as nvarchar(12)
declare @@gRole7 as nvarchar(12)
declare @@gRole8 as nvarchar(12)
declare @@gRole9 as nvarchar(12)

SET @@gRole0 =@gRole0
SET @@TOTROLES = '''' +@gRole0 + ''','

DECLARE CUR_getRoles0 CURSOR FORWARD_ONLY READ_ONLY FOR

SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole0 and roleid<>'sa'
OPEN CUR_getRoles0
	FETCH NEXT FROM CUR_getRoles0 INTO @@gRole0
	WHILE @@FETCH_STATUS = 0
	BEGIN
		SET @@TOTROLES = @@TOTROLES + '''' +@@gRole0 + ''','

		DECLARE CUR_getRoles1 CURSOR FORWARD_ONLY READ_ONLY FOR
		SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole0
		OPEN CUR_getRoles1
			FETCH NEXT FROM CUR_getRoles1 INTO @@gRole1
			WHILE @@FETCH_STATUS = 0
			BEGIN
				SET @@TOTROLES = @@TOTROLES + '''' +@@gRole1 + ''','

				DECLARE CUR_getRoles2 CURSOR FORWARD_ONLY READ_ONLY FOR
				SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole1
				OPEN CUR_getRoles2
					FETCH NEXT FROM CUR_getRoles2 INTO @@gRole2
					WHILE @@FETCH_STATUS = 0
					BEGIN
						SET @@TOTROLES = @@TOTROLES + '''' +@@gRole2 + ''','

						DECLARE CUR_getRoles3 CURSOR FORWARD_ONLY READ_ONLY FOR
						SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole2
						OPEN CUR_getRoles3
							FETCH NEXT FROM CUR_getRoles3 INTO @@gRole3
							WHILE @@FETCH_STATUS = 0
							BEGIN
							
								SET @@TOTROLES = @@TOTROLES + '''' +@@gRole3 + ''','
								DECLARE CUR_getRoles4 CURSOR FORWARD_ONLY READ_ONLY FOR
								SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole3
								OPEN CUR_getRoles4
									FETCH NEXT FROM CUR_getRoles4 INTO @@gRole4
									WHILE @@FETCH_STATUS = 0
									BEGIN
										SET @@TOTROLES = @@TOTROLES + '''' +@@gRole4 + ''','
										DECLARE CUR_getRoles5 CURSOR FORWARD_ONLY READ_ONLY FOR
										SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole4
										OPEN CUR_getRoles5
											FETCH NEXT FROM CUR_getRoles5 INTO @@gRole5
											WHILE @@FETCH_STATUS = 0
											BEGIN
												SET @@TOTROLES = @@TOTROLES + '''' +@@gRole5 + ''','
												DECLARE CUR_getRoles6 CURSOR FORWARD_ONLY READ_ONLY FOR
												SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole5
												OPEN CUR_getRoles6
													FETCH NEXT FROM CUR_getRoles6 INTO @@gRole6
													WHILE @@FETCH_STATUS = 0
													BEGIN
														SET @@TOTROLES = @@TOTROLES + '''' +@@gRole6 + ''','
														DECLARE CUR_getRoles7 CURSOR FORWARD_ONLY READ_ONLY FOR
														SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole6
														OPEN CUR_getRoles7
															FETCH NEXT FROM CUR_getRoles7 INTO @@gRole7
															WHILE @@FETCH_STATUS = 0
															BEGIN
																SET @@TOTROLES = @@TOTROLES + '''' +@@gRole7 + ''','
																DECLARE CUR_getRoles8 CURSOR FORWARD_ONLY READ_ONLY FOR
																SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole7
																OPEN CUR_getRoles8
																	FETCH NEXT FROM CUR_getRoles8 INTO @@gRole8
																	WHILE @@FETCH_STATUS = 0
																	BEGIN
																		SET @@TOTROLES = @@TOTROLES + '''' +@@gRole8 + ''','
																		DECLARE CUR_getRoles9 CURSOR FORWARD_ONLY READ_ONLY FOR
																		SELECT  ROLEID FROM AIM_ROLES WHERE REPTOROLEID=@@gRole8
																		OPEN CUR_getRoles9
																			FETCH NEXT FROM CUR_getRoles9 INTO @@gRole9
																			WHILE @@FETCH_STATUS = 0
																			BEGIN
																				SET @@TOTROLES = @@TOTROLES + '''' +@@gRole9 + ''','
																			FETCH NEXT FROM CUR_getRoles9 INTO @@gRole9
																			END	
																			close CUR_getRoles9
																			DEALLOCATE CUR_getRoles9
																	FETCH NEXT FROM CUR_getRoles8 INTO @@gRole8
																	END	
																	close CUR_getRoles8
																	DEALLOCATE CUR_getRoles8
															FETCH NEXT FROM CUR_getRoles7 INTO @@gRole7
															END	
															close CUR_getRoles7
															DEALLOCATE CUR_getRoles7
													FETCH NEXT FROM CUR_getRoles6 INTO @@gRole6
													END	
													close CUR_getRoles6
													DEALLOCATE CUR_getRoles6
											FETCH NEXT FROM CUR_getRoles5 INTO @@gRole5
											END	
											close CUR_getRoles5
											DEALLOCATE CUR_getRoles5
									FETCH NEXT FROM CUR_getRoles4 INTO @@gRole4	
									END
									close CUR_getRoles4
									DEALLOCATE CUR_getRoles4
								FETCH NEXT FROM CUR_getRoles3 INTO @@gRole3			
							END
							close CUR_getRoles3
							DEALLOCATE CUR_getRoles3
					FETCH NEXT FROM CUR_getRoles2 INTO @@gRole2	
					END
					close CUR_getRoles2
					DEALLOCATE CUR_getRoles2

			FETCH NEXT FROM CUR_getRoles1 INTO @@gRole1	
			END
			close CUR_getRoles1
			DEALLOCATE CUR_getRoles1


	FETCH NEXT FROM CUR_getRoles0 INTO @@gRole0	
	END
close CUR_getRoles0
DEALLOCATE CUR_getRoles0
set @@TOTROLES = left(@@TOTROLES, len(@@TOTROLES)-1)
select @@TOTROLES
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

