SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetFcstAdj_SP]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetFcstAdj_SP]
GO

/*******************************************************************************
**	Name: AIM_GetFcstAdj_SP
**	Desc: Gets the Fcst Adj, Adj Type from  AIMFcstMaster table
**
**	Returns: 1)  0 - Successful
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Fcst Adj, Adj Type
**              
**	Auth:   Srinivas <PERSON>ddanti
**	Date:   02/26/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**  ---------- ------------ -----------------------------------------------

*******************************************************************************/

CREATE   PROCEDURE AIM_GetFcstAdj_SP
(
      	@LcID      		nvarchar(12), 
      	@Item       		nvarchar(25),
      	@FiscalYear     	Int,
      	@FiscalPd		Int     	
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
    
DECLARE @FcstAdj 	decimal,
@AdjType as integer

SET NOCOUNT ON

-- Validate the required parameters.
IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FiscalYear IS NULL
BEGIN
	RETURN -1
END

IF @FiscalPd IS NULL
BEGIN
	RETURN -1
END
  
BEGIN




SELECT @FcstAdj =Adjmt_Qty,
  @AdjType = Adjmt_Type  
FROM AIMFcstMaster  
WHERE LcID = @LcID and 	
Item = @Item and
FiscalYear =@FiscalYear and
FiscalPeriod =@FiscalPd

If @@rowcount =1 
BEGIN
	Select @FcstAdj "FcstAdj",@AdjType "AdjType"
	RETURN 1
	
END
IF @@RowCOUNT =0
BEGIN
	RETURN -1
END

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO









SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DxKitBOM_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DxKitBOM_Sp]


/*******************************************************************************
**	Name: AIM_DxKitBOM_Sp
**	Desc: Loads data exchange item records into the ItemKitBOM Table
**          from the AIMDxKitBOM Table. 
**	NOTE: This procedure is similer to Locations Interface
**
**	Before (and after) making changes to the inbound stored procedures, 
** please check the table creation scripts and the results of the bulk insert for consistent behaviour.
**
-- Returns: 
--		@InsertCounter -- number of rows inserted
--		@UpdateCounter -- number of rows updated
--		@ReturnCode -- possible values:
--			1)  0 - Successful
--              	2) -1 - No Data Found
--              	3) -2 - SQL Error
--              	4) -3 - Duplicate File Name
--              	5) -4 - Invalid File Name
**
**	Author:		Srinivas Uddanti
**	Created:	2003/02/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:		Updated by:		Description:
**	----------	------------		-----------------------------------------------

*******************************************************************************/
 
CREATE  PROCEDURE AIM_DxKitBOM_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT	
)
-- WITH ENCRYPTION 	/* Production use must be encrypted */
AS  	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	/* START VARIABLE DECLARATION */  
	DECLARE 
		@RtnCode int

	SET NOCOUNT ON

	/* Start variable initialization */
	--Initialize counters
	SELECT @InsertCounter = 0, @UpdateCounter = 0

	-- This is a data interface transaction <Transaction Code=KB>. 
	-- Bulk Load the <Transaction Table=AIMDxKitBOM> from flat file 
	EXEC @RtnCode = AIM_DxBulkInsert_Sp @FileName, 'KB', 'AIMDxKitBOM', '\t'
		/* This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
		*/
	IF @RtnCode <> 0
	BEGIN
 		RETURN @RtnCode	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.
	
	BEGIN TRANSACTION
		UPDATE ItemKitBOM
		SET Lcid =AIMDXKitBOM.LCid,
		    Item =AIMDXKitBOM.Item,
		    ItemComponent =AIMDXKitBOM.ItemComponent,
		    ComponentUnits =AIMDXKitBOM.ComponentUnits,
		    ComponentScrap=AIMDXKitBOM.ComponentScrap,
		    StartDate =CONVERT(datetime,AIMDXKitBOM.StartDate),
		    EndDate =CONVERT(datetime,AIMDXKitBOM.EndDate)
		 FROM AIMDXKitBOM
		WHERE AIMDXKitBOM.Lcid =ItemKitBOM.Lcid
		AND   AIMDXKitBOM.Item=ItemKitBOM.Item
		AND   AIMDXKitBOM.ItemComponent =ItemKitBOM.ItemComponent

		
		BEGIN
			DELETE FROM AIMDXKitBOM
			WHERE  EXISTS ( SELECT 1 FROM ItemKitBOM WHERE
				ItemKitBOM.Item=AIMDXKitBOM.Item  AND
			 ItemKitBOM.Lcid =AIMDXKitBOM.Lcid  AND   
			ItemKitBOM.ItemComponent=AIMDXKitBOM.ItemComponent )
		END
		BEGIN
			-- INSERT
			--Substitute defaults if data not provided		
			INSERT INTO ItemKitBOM (Lcid,Item,ItemComponent,
				ComponentUnits,ComponentScrap,StartDate,EndDate
				)
	       		 SELECT RTRIM(Lcid),Item,
			ItemComponent,ComponentUnits,
			ComponentScrap,CONVERT(datetime,StartDate),
			CONVERT(datetime,EndDate)
			FROM AIMDXKitBOM
			
			SET @InsertCounter = @@ROWCOUNT 
		END -- check rowcount from update
	
	COMMIT TRANSACTION	

	-- Delete records from the DxLC Table
	TRUNCATE TABLE AIMDXKitBOM
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDXKitBOM
		
	RETURN 1	-- SUCCESS

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO







SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_CompanionItem_GetKey_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_CompanionItem_GetKey_Sp]


/*******************************************************************************
**	Name: AIM_CompanionItem_GetKey_Sp
**	Desc: Retreives CompanionItem information based on Location ID, MasterItem And Action
**
**	Returns: 1)@found - Can be Zero
**             2) 1 - No Data Found
**             
**	Values:  Recordset - AccessType
**              
**	Auth:   Srinivas Uddanti 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**  
*******************************************************************************/
   
CREATE  PROCEDURE AIM_CompanionItem_GetKey_Sp
(
       	@LcId 						nvarchar(12) 	OUTPUT,		-- Location ID
       	@Item					nvarchar(25) 	OUTPUT,		-- MasterItem Number
       	@action						tinyint			        -- Action Code
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @rtncode 					int

SET NOCOUNT ON

SET rowcount 1

-- Validate the required parameters.
--IF @LcID IS NULL
--BEGIN
--	RETURN 1
--END
--IF @Item IS NULL
--BEGIN
--	RETURN 1
--END

IF @action IS NULL
BEGIN
	RETURN 1
END

-- Get the key to the CompanionItem Record based on the action code
IF @action = 0		-- Get Equal
BEGIN
  	SELECT @LcId = lcid, @Item = Masteritem
	FROM AIMCompanionItem
	WHERE lcid = @lcid 
	AND Masteritem = @Item
	ORDER BY lcid ASC, Masteritem ASC

	SELECT @rtncode = @@rowcount
END
  
IF @action = 1		-- Get Greater Than
BEGIN
  	SELECT @Lcid = lcid, @Item = MasterItem
	FROM AIMCompanionItem
	WHERE lcid = @lcid 
	AND MasterItem > @Item
	ORDER BY lcid ASC, MasterItem ASC
        
	SELECT @rtncode = @@rowcount

	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = MasterItem 
		FROM AIMCompanionItem
		WHERE lcid > @lcid
		ORDER BY lcid ASC, MasterItem ASC
 
	 	SELECT @rtncode = @@rowcount
       END
END
  
IF @action = 2		-- Get Less Than 
BEGIN
  	SELECT @Lcid = lcid, @Item = MasterItem 
	FROM AIMCompanionItem
	WHERE lcid = @lcid 
	AND MasterItem < @Item
        ORDER BY lcid DESC, MasterItem DESC

        SELECT @rtncode = @@rowcount
       
	IF @rtncode = 0
       	BEGIN
        	SELECT @Lcid = lcid, @Item = MasterItem 
		FROM AIMCompanionItem
		WHERE lcid < @lcid 
                ORDER BY lcid DESC, MasterItem DESC

           	SELECT @rtncode = @@rowcount
       END
END
IF @action = 3		-- Get Greater Than or Equal
BEGIN
       	SELECT @Lcid = lcid, @Item = MasterItem 
	FROM AIMCompanionItem
	WHERE lcid = @lcid
	AND MasterItem >= @Item
	ORDER BY lcid ASC, MasterItem ASC

       	SELECT @rtncode = @@rowcount
       
	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = MasterItem 
		FROM AIMCompanionItem 
		WHERE lcid > @lcid
		ORDER BY lcid ASC, MasterItem ASC
 
        	SELECT @rtncode = @@rowcount
	END
END
IF @action = 4		-- Get Less Than or Equal
BEGIN
  	SELECT @Lcid = lcid, @Item = MasterItem
	FROM AIMCompanionItem
	WHERE lcid = @lcid
	AND MasterItem <= @Item
        ORDER BY lcid DESC, MasterItem DESC

	SELECT @rtncode = @@rowcount

       	IF @rtncode = 0
       	BEGIN
        	SELECT @LcId = lcid, @Item = MasterItem 
		FROM AIMCompanionItem
		WHERE lcid < @lcid 
               	ORDER BY lcid DESC, MasterItem DESC
           	
		SELECT @rtncode = @@rowcount
       	END
END
IF @action = 5		-- Get First
BEGIN
	SELECT @LcId = lcid, @Item = MasterItem 
	FROM AIMCompanionItem
    	ORDER BY lcid ASC, MasterItem ASC

       	SELECT @rtncode = @@rowcount

END
IF @action = 6		-- Get Last
BEGIN
  	SELECT @LcId = lcid, @Item = MasterItem
	FROM AIMCompanionItem
        ORDER BY lcid DESC, MasterItem DESC

	SELECT @rtncode = @@rowcount

END
IF @rtncode > 0 
BEGIN
  	return 1		-- SUCCEED
END
ELSE
BEGIN
  	return 0		-- FAIL
END



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO






SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_KitBOMSummary_Upd_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_KitBOMSummary_Upd_Sp]
GO

/******************************************************************************
**	Name: AIM_KitBOMSummary_Upd_Sp
**	Desc: Updates the KitBOMSummary table with data 
**
**	Returns: 1)@@recordcount - Can be Zero
**             2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	    Author:				Description:
**	----------  ------------		-------------------------------
*******************************************************************************/
     
CREATE  PROCEDURE AIM_KitBOMSummary_Upd_Sp
(
      	@Lcid            				nvarchar(12),
  	@Item           				nvarchar(25),
    	@Accum_Lt       				smallint,
    	@ReviewTime     				smallint,
    	@OrderPt        				int,
    	@OrderQty       				int,
    	@SafetyStock    				decimal(10, 2),
	@FcstDemand					decimal(10,2),
	@DependentFcstDemand				decimal(10,2),
    	@FcstRT         				decimal(10, 2),
    	@FcstLT         				decimal(10, 2),
    	@Fcst_Month     				decimal(10, 2),
    	@Fcst_Quarter   				decimal(10, 2),
    	@Fcst_Year      				decimal(10, 2)  
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @LcidKey  		        			nvarchar(12),
        @ItemKey 		         		nvarchar(25) 
            
SET NOCOUNT ON
    
-- Check for the existance of the MDC Summary Record
SELECT @LcidKey = Lcid, @ItemKey = Item 
FROM KitBOMSummary
WHERE Lcid = @Lcid 
AND Item = @Item
        
IF @@rowcount = 0   -- Not Found
BEGIN
        INSERT INTO KitBOMSummary(Lcid, Item, Accum_Lt, ReviewTime, OrderPt, OrderQty, 
            SafetyStock,FcstDemand,DependentFcstDemand, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year) 
        VALUES (@Lcid, @Item, @Accum_Lt, @ReviewTime, @OrderPt, @OrderQty, 
            @SafetyStock,@FcstDemand,@DependentFcstDemand, @FcstRT, @FcstLT, @Fcst_Month, @Fcst_Quarter, @Fcst_Year)
END
ELSE                -- Found
BEGIN
        UPDATE KitBOMSummary
	SET Accum_Lt = @Accum_LT, ReviewTime = @ReviewTime, 
            OrderPt = @OrderPt, OrderQty = @OrderQty, SafetyStock = @SafetyStock, 
	    FcstDemand =@FcstDemand,DependentFcstDemand =@DependentFcstDemand,
            FcstRT = @FcstRT, FcstLT = @FcstLT, Fcst_Month = @Fcst_Month, 
            Fcst_Quarter = @Fcst_Quarter, Fcst_Year = @Fcst_Year
        WHERE Lcid = @Lcid and Item = @Item 
END
/*
BEGIN

UPDATE Item 
 SET  SafetyStock =@SafetyStock,
	FcstRT =@FcstRT,
	FcstLT =@FcstLT,
	Fcst_Month =@Fcst_Month,
	Fcst_Quarter=@Fcst_Quarter,
	Fcst_Year=@Fcst_Year,
	OrderPt=@OrderPt,
	OrderQty =@OrderQty,
	FcstDemand=(@Fcst_Year/52)
 WHERE Lcid =@Lcid and
 	Item =@Item
END
*/
RETURN


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_KitBOMSummary_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_KitBOMSummary_Sp]
GO

/******************************************************************************
**	Name: AIM_KitBOMSummary_Sp
**	Desc: Retrieves the data from the KITBOM Processing table.
**
**	Returns: 1)@@recordcount - Can be Zero
**             2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
    
CREATE  PROCEDURE AIM_KitBOMSummary_Sp(
 	@Lcid            				nvarchar(12),
  	@Item           				nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @KFactor 					decimal(5,2)
  
SET NOCOUNT ON
    
-- Get The K Factor from the System Control Table
SELECT @KFactor = KFactor 
FROM SysCtrl
 
SELECT  Item.Trend,
        Item.LtvFact, Item.SSAdj, Item.ZSStock, Item.CStock, Item.ReplenCost2, Item.Dser,
        Item.Cost, Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02,
        Item.BkQty03, Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05,
        Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08,   
        Item.BkQty09, Item.BkCost09, Item.BkQty10, Item.BkCost10, Item.Accum_LT, Item.ReviewTime, 
        AIMLocations.ReplenCost,
        AIMOptions.LoMadP, AIMOptions.HiMadP, AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow,
        AIMOptions.MadExk,
        KFactor = @KFactor
FROM 	Item
        INNER JOIN AIMLocations On Item.Lcid= AIMLocations.LcId 
        INNER JOIN AIMOptions On Item.OptionId = AIMOptions.OptionId
WHERE Item.Lcid =@Lcid AND
      Item.Item =@Item
  

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_KitBOMSummary_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_KitBOMSummary_Sp]
GO

/******************************************************************************
**	Name: AIM_KitBOMSummary_Sp
**	Desc: Retrieves the data from the KITBOM Processing table.
**
**	Returns: 1)@@recordcount - Can be Zero
**             2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-------------------------------------------------
** 
*******************************************************************************/
    
CREATE  PROCEDURE AIM_KitBOMSummary_Sp(
 	@Lcid            				nvarchar(12),
  	@Item           				nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @KFactor 					decimal(5,2)
  
SET NOCOUNT ON
    
-- Get The K Factor from the System Control Table
SELECT @KFactor = KFactor 
FROM SysCtrl
 
SELECT  Item.Trend,
        Item.LtvFact, Item.SSAdj, Item.ZSStock, Item.CStock, Item.ReplenCost2, Item.Dser,
        Item.Cost, Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02,
        Item.BkQty03, Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05,
        Item.BkQty06, Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08,   
        Item.BkQty09, Item.BkCost09, Item.BkQty10, Item.BkCost10, Item.Accum_LT, Item.ReviewTime, 
        AIMLocations.ReplenCost,
        AIMOptions.LoMadP, AIMOptions.HiMadP, AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow,
        AIMOptions.MadExk,
        KFactor = @KFactor
FROM 	Item
        INNER JOIN AIMLocations On Item.Lcid= AIMLocations.LcId 
        INNER JOIN AIMOptions On Item.OptionId = AIMOptions.OptionId
WHERE Item.Lcid =@Lcid AND
      Item.Item =@Item
  

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DemandUpdateForKitBOMItems_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DemandUpdateForKitBOMItems_Sp]
GO

/*******************************************************************************
**	Name: AIM_DemandUpdateForKitBOMItems_Sp
**	Desc: Updates the DependentFcstDemanad, FcstRT,FcstLT,FcstMonth,FcstQtr,FcstYear
**	Values if the item has only KitBOM Demand

**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   Author:	Description:
**    ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
   
CREATE     PROCEDURE AIM_DemandUpdateForKitBOMItems_Sp
(
        @Lcid	            					nvarchar(12),
        @Item            					nvarchar(25),
	@FcstKitBom						decimal(10,2),
        @FcstRT          					decimal(10, 2),
        @FcstLT          					decimal(10, 2),
        @Fcst_Month      					decimal(10, 2),
        @Fcst_Quarter    					decimal(10, 2),
        @Fcst_Year       					decimal(10, 2)
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

SET NOCOUNT ON

UPDATE Item
    	SET 
        DependentFcstDemand =@FcstKitBOM,FcstRT = @FcstRT,FcstLT = @FcstLT,
	Fcst_Month = @Fcst_Month,Fcst_Quarter =@Fcst_Quarter, Fcst_Year =@Fcst_Year
        WHERE LcId = @Lcid 
    	and Item = @Item
RETURN @@rowcount




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO





SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DemandUpdateForCompItems_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DemandUpdateForCompItems_Sp]
GO

/*******************************************************************************
**	Name: AIM_DemandUpdateForCompItems_Sp
**	Desc: Updates the FcstDemanad, FcstRT,FcstLT,FcstMonth,FcstQtr,FcstYear
**	Values if the item has only Companion item demand or 
**	Campanion item demand and KitBOM Demand 
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Srinivas Uddanti
**	Date:
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	   Author:	Description:
**    ---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
   
CREATE     PROCEDURE AIM_DemandUpdateForCompItems_Sp
(
        @Lcid	            					nvarchar(12),
        @Item            					nvarchar(25),
        @FcstDemand     					decimal(10, 2),
	@FcstKitBom						decimal(10,2),
        @FcstRT          					decimal(10, 2),
        @FcstLT          					decimal(10, 2),
        @Fcst_Month      					decimal(10, 2),
        @Fcst_Quarter    					decimal(10, 2),
        @Fcst_Year       					decimal(10, 2)
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
-- Declare working variable
DECLARE	

@LineCount		Int
SET NOCOUNT ON

Select @LineCount =Count(*) FROM
AIMCOMPANIONITEMDETAIL
WHERE Lcid =@Lcid AND
Item =@Item  AND
ExcludeIndepDemY_N ='N'

If @LineCount <>0 
BEGIN
	UPDATE Item
    	SET 
        FcstDemand = FcstDemand +@FcstDemand,DependentFcstDemand =@FcstKitBOM,
	FcstRT = FcstRT +@FcstRT,FcstLT = FcstLT +@FcstLT, Fcst_Month = Fcst_Month +@Fcst_Month ,
	Fcst_Quarter = Fcst_Quarter +@Fcst_Quarter, Fcst_Year = Fcst_Year +@Fcst_Year
        WHERE LcId = @Lcid 
    	and Item = @Item
END
ELSE
BEGIN
UPDATE Item
    	SET 
        FcstDemand = @FcstDemand,DependentFcstDemand =@FcstKitBOM,
	FcstRT = @FcstRT,FcstLT = @FcstLT, Fcst_Month = @Fcst_Month,
	Fcst_Quarter =@Fcst_Quarter, Fcst_Year =@Fcst_Year
        WHERE LcId = @Lcid 
    	and Item = @Item
END
RETURN @@rowcount




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO





SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetItemsForAKitBOMItem_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetItemsForAKitBOMItem_Sp]
GO

/******************************************************************************
  **	Name: AIM_GetItemsForAKitBOMItem_Sp
  **	Desc:Gets Items for a given KitBOMItem  From
  **    ITEMKITBOM  table
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) 0 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE           PROCEDURE AIM_GetItemsForAKitBOMItem_Sp
  (
      	@KitBOMItem           			nvarchar(25),
	@LcId          				nvarchar(12)     
  )
  AS  --ENCRYPT

  SET NOCOUNT ON

 SELECT *  
 FROM ITEMKITBOM
 WHERE ItemComponent =@KitBOMItem
 AND LcId =@LcId
 RETURN  @@rowcount


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetKitBOMItemsForALoc_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetKitBOMItemsForALoc_Sp]
GO


/******************************************************************************
  **	Name: AIM_GetKitBOMItemsForALoc_Sp
  **	Desc:Gets distict Dependent item from ItemKitBOM table for a given
  **    Loction
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) 0 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE               PROCEDURE AIM_GetKitBOMItemsForALoc_Sp
  (
      	@LcId           	nvarchar(12)
      

  )
  AS  --ENCRYPT

  SET NOCOUNT ON
/*
 SELECT DISTINCT Item AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT,
	 Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,
	 Fcst_Quarter,Fcst_Year
 FROM  ITEM 
 WHERE Item  IN
(SELECT DISTINCT ItemComponent 
 FROM ITEMKITBOM 
 WHERE LCID =@LCID )
 AND LCID =@LCID
*/

 SELECT DISTINCT Item AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT,
	 Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,
	 Fcst_Quarter,Fcst_Year
 FROM  ITEM 
 WHERE Item  IN
(SELECT DISTINCT ItemComponent 
 FROM ITEMKITBOM,ITEM
 WHERE ITEMKITBOM.LCID =ITEM.LCID AND
	ITEMKITBOM.ITEM =ITEM.ITEM AND
	ITEM.KITBOMFLAG ='Y' AND
	ITEMKITBOM.LCID =@LCID
 )
 AND LCID =@LCID
 RETURN  @@rowcount



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetKitBOMItemsLTRT_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetKitBOMItemsLTRT_Sp]
GO

/******************************************************************************
  **	Name: AIM_GetKitBOMItemsLTRT_Sp
  **	Desc:Gets the Accum_LT and Accum_LT + ReviewTime for all items dependent on 
  **    on the master item
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) -1 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE             PROCEDURE AIM_GetKitBOMItemsLTRT_Sp
  (
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25)

  )
  AS  --ENCRYPT
  SET NOCOUNT ON
/*
SELECT DISTINCT Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
	StartDate,EndDate
FROM  ITEM,ITEMKITBOM 
WHERE ITEM.Item =ITEMKITBOM.ItemComponent AND 
      ITEM.LcId =ITEMKITBOM.LcId AND 
      ITEM.Item  IN
      (SELECT ITEMCOMPONENT 
      FROM ITEMKITBOM 
      WHERE LCID =@LCID AND ITEM =@ITEM )AND 
      ITEMKITBOM.Item =@ITEM AND 
      ITEM.LcId =@LCID
*/

SELECT DISTINCT Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
	StartDate,EndDate
FROM  ITEM,ITEMKITBOM 
WHERE ITEM.Item =ITEMKITBOM.ItemComponent AND 
      ITEM.LcId =ITEMKITBOM.LcId AND 
      ITEM.Item  IN
      (SELECT ITEMCOMPONENT 
      FROM ITEMKITBOM,ITEM 
      WHERE ITEMKITBOM.LCID =ITEM.LCID AND
	ITEMKITBOM.ITEM =ITEM.ITEM AND
	ITEM.KITBOMFLAG ='Y'AND
	ITEMKITBOM.LCID =@LCID AND 
	ITEMKITBOM.ITEM =@ITEM
	 )AND 
      ITEMKITBOM.Item =@ITEM AND 
      ITEM.LcId =@LCID

 RETURN  @@rowcount



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetItemsForACompItem_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetItemsForACompItem_Sp]
GO



/******************************************************************************
  **	Name: AIM_GetItemsForACompItem_Sp
  **	Desc:Gets items  from itemkitbom table for a given ComponentItem
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) 0 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE         PROCEDURE AIM_GetItemsForACompItem_Sp
  (
      	@CompItem           				nvarchar(25),
	@LcId						nvarchar(12)
      

  )
  AS  --ENCRYPT

  SET NOCOUNT ON

 SELECT *  FROM AIMCompanionItemDetail
 WHERE Item =@CompItem
 AND LcId =@LcId
 RETURN  @@rowcount






GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetCompItemsForALoc_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetCompItemsForALoc_Sp]
GO


/******************************************************************************
  **	Name: AIM_GetCompItemsForALoc_Sp
  **	Desc:Gets distict Componentitems from itemkitbom table for a given loction
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) 0 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE                PROCEDURE AIM_GetCompItemsForALoc_Sp
  (
      	@LcId           				nvarchar(12)
      

  )
  AS  --ENCRYPT

 
  
  SET NOCOUNT ON
  
 SET NOCOUNT ON
 /* 
 SELECT DISTINCT ITEM AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT, Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,Fcst_Quarter,Fcst_Year
 FROM  ITEM WHERE ITEM  IN
(SELECT DISTINCT ITEM FROM AIMCOMPANIONITEMDETAIL WHERE 
 LCID =@LCID )
 AND LCID =@LCID
*/
SELECT DISTINCT ITEM AS ITEMCOMPONENT,FcstDemand as FCST,Accum_LT AS LT, Accum_LT + ReviewTime AS RT, FcstDemand,FcstRT,FcstLT,Fcst_Month,Fcst_Quarter,Fcst_Year
 FROM  ITEM WHERE ITEM  IN
(SELECT DISTINCT ITEM FROM AIMCOMPANIONITEMDETAIL,AIMCOMPANIONITEM WHERE 
AIMCOMPANIONITEMDETAIL.LCID =AIMCOMPANIONITEM.LCID AND
AIMCOMPANIONITEMDETAIL.MASTERITEM =AIMCOMPANIONITEM.MASTERITEM AND
AIMCOMPANIONITEM.ENABLEY_N ='Y' AND
 AIMCOMPANIONITEMDETAIL.LCID =@LCID )
 AND LCID =@LCID


 RETURN  @@rowcount




GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_GetCompItemsLTRT_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_GetCompItemsLTRT_Sp]
GO


/******************************************************************************
  **	Name: AIM_GetCompItemsLTRT_Sp
  **	Desc:Gets the Accu_LT and RT for all items dependent on this item
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) 0 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE              PROCEDURE AIM_GetCompItemsLTRT_Sp
  (
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25)

  )
  AS  --ENCRYPT

 
  
  SET NOCOUNT ON
 /*
SELECT distinct Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
startdate,enddate
 FROM  ITEM,AIMCOMPANIONITEMDETAIL WHERE ITEM.item =AIMCOMPANIONITEMDETAIL.Item
and item.lcid =AIMCOMPANIONITEMDETAIL.lcid and item.item  IN
(SELECT ITEM FROM AIMCOMPANIONITEMDETAIL WHERE 
 LCID =@LCID AND MASTERITEM =@ITEM )
 AND AIMCOMPANIONITEMDETAIL.MASTERITEM =@ITEM
 AND Item.LCID =@LCID
*/

SELECT distinct Accum_LT, Accum_LT + ReviewTime AS LT_RT ,
startdate,enddate
 FROM  ITEM,AIMCOMPANIONITEMDETAIL,AIMCOMPANIONITEM WHERE ITEM.item =AIMCOMPANIONITEMDETAIL.Item
and item.lcid =AIMCOMPANIONITEMDETAIL.lcid 
and AIMCOMPANIONITEMDETAIL.lcid =AIMCOMPANIONITEM.lcid 
and AIMCOMPANIONITEMDETAIL.masteritem =AIMCOMPANIONITEMDETAIL.masteritem
and AIMCOMPANIONITEM.enableY_N ='Y' 
and item.item  IN
(SELECT ITEM FROM AIMCOMPANIONITEMDETAIL WHERE 
 LCID =@LCID AND MASTERITEM =@ITEM )
 AND AIMCOMPANIONITEMDETAIL.MASTERITEM =@ITEM
 AND Item.LCID =@LCID
 RETURN  @@rowcount






GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO


IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_CompanionItemDetail_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_CompanionItemDetail_Sp]
GO

/******************************************************************************
  **	Name: AIM_CompanionItemDetail_Sp
  **	Desc:Inserts data into CompanionItemDetail table
  **
  **	Returns: 1)@@recordcount - Can be Zero
  **             2) 0 - Failure
  **             
  **	Values:  
  **              
  **	Auth:   Srinivas Uddanti
  **	Date:   
  *******************************************************************************
  **	Change History
  *******************************************************************************
  **	Date:	   Author:	Description:
  **	---------- ------------	-------------------------------------------------
  ** 
  *******************************************************************************/
     
  CREATE     PROCEDURE AIM_CompanionItemDetail_Sp
  (
      	@LcId           				nvarchar(12),
      	@Item           				nvarchar(25),
        @StartDate       				datetime,
	@EndDate       					datetime,
        @Qty       					int
  )
  AS  --ENCRYPT

  SET NOCOUNT ON
  
  INSERT INTO CompanionItemDetail
        ( LcId, Item, StartDate,EndDate,Qty) 
  VALUES 
        (@LcId, @Item, @StartDate,@EndDate, @Qty) 
  RETURN 1



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


