if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMDays_Init_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMDays_Init_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMDays_Load_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMDays_Load_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMDays_Upd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMDays_Upd_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMForecast_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMForecast_Delete_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMForecast_GetLocations_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMForecast_GetLocations_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMForecast_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMForecast_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMLanguage_GetValue_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMLanguage_GetValue_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMLanguage_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMLanguage_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMLocations_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMLocations_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMLocations_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMLocations_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMOptions_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMOptions_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMOptions_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMOptions_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMProductionConstraintCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMProductionConstraintCtrl_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMProductionConstraint_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMProductionConstraint_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMProductionConstraint_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMProductionConstraint_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMPromotions_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMPromotions_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMSeasons_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMSeasons_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMTranslation_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMTranslation_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMUser_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMUser_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMVendors_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMVendors_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_Delete_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_Insert_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_Insert_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_Load_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_Load_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AimSeasons_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AimSeasons_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AimVendors_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AimVendors_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AssignSeasonsDefaults_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AssignSeasonsDefaults_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BldHistoryfmSDFix_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BldHistoryfmSDFix_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BldHistoryfmSD_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BldHistoryfmSD_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BldItDefaults_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BldItDefaults_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BuyerStatus_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BuyerStatus_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BuyerSummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BuyerSummary_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Calendar_Init_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Calendar_Init_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ClassByClassLevel_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ClassByClassLevel_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Class_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Class_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_CodeLookup_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_CodeLookup_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_CostPriceListPrice_Get_SP]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_CostPriceListPrice_Get_SP]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_CostPriceListPrice_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_CostPriceListPrice_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DUGet_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DUGet_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DemandUpdate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DemandUpdate_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxDd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxDd_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxHs_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxHs_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxLT_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxLT_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxPO_Batch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxPO_Batch_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxPO_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxPO_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxSS_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxSS_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxSd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxSd_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DxVn_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DxVn_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_FcstGet_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_FcstGet_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastDetail_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastDetail_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastFreezeItem_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastFreezeItem_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastFreezePds_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastFreezePds_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMainUserAccess_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMainUserAccess_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMainUserAccess_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMainUserAccess_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMainUserAccess_Validate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMainUserAccess_Validate_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepositoryDetail_GetLocked_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepositoryDetail_GetLocked_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepositoryDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepositoryDetail_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepositoryVersion_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepositoryVersion_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_Copy_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_Copy_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastUserAccess_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastUserAccess_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastUserAccess_Validate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastUserAccess_Validate_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Forecast_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Forecast_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Forecast_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Forecast_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_FySales_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_FySales_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GenerateForecastRepository_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GenerateForecastRepository_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetAccum_LT_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetAccum_LT_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetDate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetDate_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetDemand_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetDemand_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetLastWeekSales_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetLastWeekSales_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetPd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetPd_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetSaVersion_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetSaVersion_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetSeqNbr_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetSeqNbr_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InitNewItem_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_InitNewItem_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InitPOHeader_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_InitPOHeader_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_InitializeNewLocation_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_InitializeNewLocation_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemFilter_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemFilter_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHistory_Copy_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHistory_Copy_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHistory_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHistory_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemHsDelete_sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemHsDelete_sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemPerformance_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemPerformance_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemPosition_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemPosition_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemStatus_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemStatus_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemTest_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemTest_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Item_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Item_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Item_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Item_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItmChg_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItmChg_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_LocationsFilter_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_LocationsFilter_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_MDCDetail_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_MDCDetail_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_MDCSummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_MDCSummary_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_MDCSummary_Upd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_MDCSummary_Upd_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Methods_Load_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Methods_Load_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_NextPONbr_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_NextPONbr_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_OrdGenCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_OrdGenCtrl_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_OrdGen_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_OrdGen_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POClose_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POClose_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POHdrInsert_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POHdrInsert_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POInsert_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POInsert_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_POStatistics_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_POStatistics_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_PackRounding_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_PackRounding_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraintDetail_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraintDetail_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraintDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraintDetail_Save_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_Delete_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ProductionConstraint_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ProductionConstraint_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_QuickOrder_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_QuickOrder_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReleasedPurchaseOrders_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReleasedPurchaseOrders_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RevCycleRollBack_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RevCycleRollBack_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RevCycle_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RevCycle_List_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RevCycles_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RevCycles_GetEq_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RevCycles_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_RevCycles_GetKey_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewCycleById_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewCycleById_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewCycle_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewCycle_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ReviewVendor_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ReviewVendor_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SASummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SASummary_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SdCopy_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SdCopy_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SetSafetyStockAdj_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SetSafetyStockAdj_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Special_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Special_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SumMethods_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SumMethods_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_SysCtrl_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_SysCtrl_Get_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_TestLocationId_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_TestLocationId_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_TransferManagement_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_TransferManagement_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_TransferToReport_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_TransferToReport_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Transfer_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Transfer_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UOMChg_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UOMChg_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UnOrderedItems_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UnOrderedItems_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UnorderedVendors_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UnorderedVendors_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UpdateOrdStatus_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UpdateOrdStatus_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_UpdateOverrides_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_UpdateOverrides_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Update_RevCycle_Date_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Update_RevCycle_Date_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VelCodeCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VelCodeCtrl_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VelCode_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VelCode_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorDetail_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorDetail_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorSizingCtrl_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorSizingCtrl_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorSizing_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorSizing_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VendorSummary_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VendorSummary_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VerifyAccess_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VerifyAccess_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_VerifyDxFile_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_VerifyDxFile_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[UnmatchedInAIMDD_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[UnmatchedInAIMDD_Sp]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[utl_CompareTables]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[utl_CompareTables]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[x_helptext]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[x_helptext]
GO