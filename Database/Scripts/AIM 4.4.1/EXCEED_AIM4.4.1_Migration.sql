--------------------------------------------------------
--   Product: 	EXceed AIM
--   Version: 	4.4.1
--   Purpose: 	Migrate data from an existing EXceed AIM database 
--				to one compatible with the current version.
-- 	Notes:	
--		IMPORTANT! PLEASE READ!!
--		Find and replace all occurrences (of the enquoted string):
--			1. "TEMP_EXCEED_AIM.." with the name of the existing database.
--			2. "EXCEED_AIM.." with the name of the new database.
--------------------------------------------------------
SET NOCOUNT ON

PRINT 'Starting EXceed AIM database migration script...'

IF NOT EXISTS (SELECT * FROM EXceed_AIM.dbo.sysobjects WHERE ID = object_id(N'EXCEED_AIM..EXceed_AIM_Migration'))
BEGIN
	CREATE TABLE EXCEED_AIM..EXceed_AIM_Migration (
		EAM_ID uniqueidentifier Default NewID() NOT NULL,
		ProductName nvarchar(50) Not Null,
		OldVersion nvarchar(10) Not Null,
		NewVersion nvarchar(10) Not Null,
		TransactionCount bigint Not Null,
		TotalTimeMinute int Not Null,
		StartTime datetime Not Null,
		EndTime datetime Not Null,
		CONSTRAINT PK_EXceed_AIM_Migration PRIMARY KEY (EAM_ID)
	)
	
	CREATE INDEX EAM_ProdNewVer ON EXceed_AIM_Migration (ProductName, NewVersion)
END

IF NOT EXISTS (SELECT * FROM EXceed_AIM.dbo.sysobjects WHERE ID = object_id(N'EXCEED_AIM..EXceed_AIM_Migration_Log')) 
BEGIN
	CREATE TABLE EXCEED_AIM..EXceed_AIM_Migration_Log (
		EAM_ID uniqueidentifier NOT NULL,
		ProductName nvarchar(50) Not Null,
		OldVersion nvarchar(10) Not Null,
		NewVersion nvarchar(10) Not Null,
		TableName nvarchar(30) Not Null,
		DefaultCount bigint Not Null,
		OldDatabaseCount bigint Not Null,
		NewDatabaseCount bigint Not Null,
		TotalTimeMillisecond int Not Null,
		StartTime datetime Not Null,
		EndTime datetime Not Null,
		CONSTRAINT PK_EXceed_AIM_Migration_Log PRIMARY KEY (EAM_ID, ProductName, NewVersion, TableName)
		)
END

DECLARE @Default bigint,
	@OldCount bigint,
	@NewCount bigint,
	@StartTime datetime,
	@EndTime datetime,
	@Transaction bigint,
	@OldVersion nvarchar(10),
	@NewVersion nvarchar(10),
	@ProductName nvarchar(50)
DECLARE @MessageString nvarchar(50)
DECLARE @TableName nvarchar(255)
DECLARE @TableCounter tinyint
DECLARE @EAM_ID uniqueidentifier

SET @OldVersion = '4.4'
SET @NewVersion = '4.4.1'
SET @ProductName = 'EXceed_AIM'

--------------------------------------------------------
-- EXceed_AIM_Migration - fetch old
--------------------------------------------------------
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..EXceed_AIM_Migration
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..EXceed_AIM_Migration
	
	IF @OldCount > 0 
	BEGIN
		INSERT INTO EXCEED_AIM..EXceed_AIM_Migration ([ProductName],
				[OldVersion], [NewVersion],
				[TransactionCount],
				[TotalTimeMinute], [StartTime], [EndTime])
			SELECT [ProductName],
				[OldVersion], [NewVersion],
				[TransactionCount],
				DATEDIFF(mi, StartTime, EndTime), [StartTime], [EndTime]
			FROM TEMP_EXCEED_AIM..EXceed_AIM_Migration
			ORDER BY ProductName, 
				NewVersion
	
		--------------------------------------------------------
		-- EXceed_AIM_Migration_Log - fetch old
		--------------------------------------------------------
		SELECT @Default = COUNT(*) FROM EXCEED_AIM..EXceed_AIM_Migration_Log
		SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..EXceed_AIM_Migration_Log
		
		TRUNCATE TABLE EXCEED_AIM..EXceed_AIM_Migration_Log
		INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log 
			(	EAM_ID,
				ProductName,
				OldVersion, 
				NewVersion,
				TableName, DefaultCount, OldDatabaseCount, NewDatabaseCount,
				TotalTimeMillisecond, StartTime, EndTime
			) SELECT 
				NewID(),
				ProductName,
				OldVersion, 
				NewVersion,
				TableName, DefaultCount, OldDatabaseCount, NewDatabaseCount,
				TotalTimeMillisecond, StartTime, EndTime
			FROM TEMP_EXCEED_AIM..EXceed_AIM_Migration_Log
			ORDER BY ProductName, 
				NewVersion, 
				TableName

		UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
			EAM_ID = EXCEED_AIM..EXceed_AIM_Migration.EAM_ID
		FROM EXCEED_AIM..EXceed_AIM_Migration
		WHERE EXCEED_AIM..EXceed_AIM_Migration_Log.ProductName = EXCEED_AIM..EXceed_AIM_Migration.ProductName
				AND EXCEED_AIM..EXceed_AIM_Migration_Log.NewVersion = EXCEED_AIM..EXceed_AIM_Migration.NewVersion

	END	

--------------------------------------------------------
-- EXceed_AIM_Migration Table Insert
--------------------------------------------------------
	SET @EAM_ID = NewID()	-- get a unique id for this run.
	SELECT @Transaction = 0
	SELECT @StartTime = Getdate()
	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, 0, @Transaction, @StartTime, @StartTime)

--------------------------------------------------------
-- Disable Constraints back to Alternate Vendor Table
--------------------------------------------------------
	IF EXISTS(SELECT * FROM EXceed_AIM.dbo.sysobjects where id = object_id(N'[EXceed_AIM]..[ALTERNATE_SOURCE]'))
	BEGIN
		ALTER Table EXCEED_AIM..ALTERNATE_SOURCE
		NOCHECK CONSTRAINT [FK_ALTERNATESOURCE_Item]
		
		ALTER TABLE EXCEED_AIM..ALTERNATE_SOURCE
		NOCHECK CONSTRAINT [FK_ALTERNATESOURCE_Vendor]
		PRINT 'Altered Table EXCEED_AIM..ALTERNATE_SOURCE - removed constraints.'
	END

-----------------------------------------------------------------------------------------
-- Start moving data from the temporary copy to the newly created database.
-- Tables affected by this script (version specific changes marked by *):
--	 1. AIMAO							--	 2. AIMAODetail
--	*3. AIMAO_OrderInfo				--	 4. AIMClasses
--	 5. AIMDataEXchangeCtrl			--	 6. AIMDays
--	 7. AIMDestinationProfile				--	 8. AIMForecast
--	 9. AIMForecastDetail				--	 10. AIMForecastFreezePds
--	 11. AIMForecastMainUserAccess		--	 12. AIMForecastUserAccess
--	 13. AIMLeadTime					--	 14. AIMLocations
--	 15. AIMOptions						--	 16. AIMPO
--	 17. AIMProductionConstraint			--	 18. AIMProductionConstraintDetail
--	 19. AIMPromotions					--	*20. AIMSd
--	 21. AIMSeasons					--	 22. AIMSourcingHierarchy
--	 23. AIMTransferPolicy				--	 24. AIMUOM
--	 25. AIMUsers						--	 26. AIMVelCodePcts
--	 27. AIMVendors						--	 28. AIMWorkingDays
--	 29. AIMYears						--	 30. AllocDefaults
--	 31. AllocDefaultsSource				--	*32. AllocItemSubstitutes
--	 33. Alternate_Source				--	 34. BuyerStatusTest
--	 35. ForecastRepository				--	 36. ForecastRepository_Log
--	 37. ForecastRepositoryDetail		--	 38. ItDefaults
--	 39. Item							--	*40. ItemHistory
--	 41. ItemPricing						--	 42. ItemStatus
--	 43. MDCDetail						--	 44. MDCSummary
--	 45. PODetail						--	 46. POStatistics
--	 47. RevCycles						--	*48. SysCtrl
------------------------------------------------------------------------------------------
SET @MessageString = '- Record count: '
SET @TableCounter = 0
	--------------------------------------------------------
	-- 1. AIMAO
	--------------------------------------------------------
	SET @TableName = 'AIMAO'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMAO
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMAO
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMAO
	INSERT INTO EXCEED_AIM..AIMAO
		SELECT * FROM TEMP_EXCEED_AIM..AIMAO
		ORDER BY ORDNBR

	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMAO
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount

	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 2. AIMAODetail
	--------------------------------------------------------
	SET @TableName = 'AIMAODetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMAODetail
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMAODetail
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMAODetail
	INSERT INTO EXCEED_AIM..AIMAODetail
		SELECT * FROM TEMP_EXCEED_AIM..AIMAODetail
		ORDER BY ORDNBR, LINENBR

	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMAODetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@@@@@@ New Table @@@@@@@@@@@@@@@@@@@@@@@@@@
	-- 3. AIMAO_OrderInfo
	--------------------------------------------------------
	SET @TableName =  'AIMAO_OrderInfo'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMAO_OrderInfo
	--SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMAO_OrderInfo
	SET @OldCount = 0 
	SELECT @StartTime = Getdate()
	
	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMAO_OrderInfo
	--INSERT INTO EXCEED_AIM..AIMAO_OrderInfo
	--	SELECT * FROM TEMP_EXCEED_AIM..AIMAO_OrderInfo
	--	ORDER BY ORDNBR, LINENBR
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMAO_OrderInfo
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)


	--------------------------------------------------------
	-- 4. AIMClasses
	--------------------------------------------------------
	SET @TableName = 'AIMClasses'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMClasses
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMClasses
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMClasses
	INSERT INTO EXCEED_AIM..AIMClasses
		SELECT * FROM TEMP_EXCEED_AIM..AIMClasses
		ORDER BY Class
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMClasses
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 5. AIMDataEXchangeCtrl
	--------------------------------------------------------
	SET @TableName = 'AIMDataExchangeCtrl'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMDataEXchangeCtrl
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMDataEXchangeCtrl
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMDataEXchangeCtrl
	INSERT INTO EXCEED_AIM..AIMDataEXchangeCtrl
		SELECT * FROM TEMP_EXCEED_AIM..AIMDataEXchangeCtrl
		ORDER BY TxnSet
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMDataEXchangeCtrl
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 6. AIMDays
	--------------------------------------------------------
	SET @TableName = 'AIMDays'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMDays
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMDays
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMDays
	INSERT INTO EXCEED_AIM..AIMDays
		SELECT * FROM TEMP_EXCEED_AIM..AIMDays
		ORDER BY FYDate
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMDays
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 7. AIMDestinationProfile
	--------------------------------------------------------
	SET @TableName = 'AIMDestinationProfile'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMDestinationProfile
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMDestinationProfile
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMDestinationProfile
	INSERT INTO EXCEED_AIM..AIMDestinationProfile
		SELECT * FROM TEMP_EXCEED_AIM..AIMDestinationProfile
		ORDER BY Lcid_Destination, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMDestinationProfile
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 8. AIMForecast
	--------------------------------------------------------
	SET @TableName = 'AIMForecast'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMForecast
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMForecast
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMForecast
	INSERT INTO EXCEED_AIM..AIMForecast
		SELECT * FROM TEMP_EXCEED_AIM..AIMForecast
		ORDER BY FcstId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMForecast
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 9. AIMForecastDetail
	--------------------------------------------------------
	SET @TableName = 'AIMForecastDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMForecastDetail
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMForecastDetail
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMForecastDetail
	INSERT INTO EXCEED_AIM..AIMForecastDetail
		SELECT * FROM TEMP_EXCEED_AIM..AIMForecastDetail
		ORDER BY FcstId, Lcid, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMForecastDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 10. AIMForecastFreezePds
	--------------------------------------------------------
	SET @TableName = 'AIMForecastFreezePds'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMForecastFreezePds
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMForecastFreezePds
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMForecastFreezePds
	INSERT INTO EXCEED_AIM..AIMForecastFreezePds
		SELECT * FROM TEMP_EXCEED_AIM..AIMForecastFreezePds
		ORDER BY FcstId, Lcid
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMForecastFreezePds
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 11. AIMForecastMainUserAccess
	--------------------------------------------------------
	SET @TableName = 'AIMForecastMainUserAccess'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMForecastMainUserAccess
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMForecastMainUserAccess
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMForecastMainUserAccess
	INSERT INTO EXCEED_AIM..AIMForecastMainUserAccess
		SELECT * FROM TEMP_EXCEED_AIM..AIMForecastMainUserAccess
		ORDER BY Userid, FcstId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMForecastMainUserAccess
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 12. AIMForecastUserAccess
	--------------------------------------------------------
	SET @TableName = 'AIMForecastUserAccess'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMForecastUserAccess
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMForecastUserAccess
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMForecastUserAccess
	INSERT INTO EXCEED_AIM..AIMForecastUserAccess
		SELECT * FROM TEMP_EXCEED_AIM..AIMForecastUserAccess
		ORDER BY Userid, FcstId, FcstType
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMForecastUserAccess
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 13. AIMLeadTime
	--------------------------------------------------------
	SET @TableName = 'AIMLeadTime'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMLeadTime
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMLeadTime
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMLeadTime
	INSERT INTO EXCEED_AIM..AIMLeadTime
		SELECT * FROM TEMP_EXCEED_AIM..AIMLeadTime
		ORDER BY LcId, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMLeadTime
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 14. AIMLocations
	--------------------------------------------------------
	SET @TableName = 'AIMLocations'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMLocations
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMLocations
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMLocations
	INSERT INTO EXCEED_AIM..AIMLocations 
		SELECT * FROM TEMP_EXCEED_AIM..AIMLocations
		ORDER BY LcId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMLocations
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 15. AIMOptions
	--------------------------------------------------------
	SET @TableName = 'AIMOptions'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMOptions
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMOptions
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMOptions
	INSERT INTO EXCEED_AIM..AIMOptions
		SELECT * FROM TEMP_EXCEED_AIM..AIMOptions
		ORDER BY OptionId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMOptions
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 16. AIMPO
	--------------------------------------------------------
	SET @TableName = 'AIMPO'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMPO
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMPO
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMPO
	INSERT INTO EXCEED_AIM..AIMPO
		SELECT * FROM TEMP_EXCEED_AIM..AIMPO
		ORDER BY ById, VnId, Assort
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMPO
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 17. AIMProductionConstraint
	--------------------------------------------------------
	SET @TableName = 'AIMProductionConstraint'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMProductionConstraint
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMProductionConstraint
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	DELETE EXCEED_AIM..AIMProductionConstraintDetail
	DELETE EXCEED_AIM..AIMProductionConstraint
	INSERT INTO EXCEED_AIM..AIMProductionConstraint
		SELECT * FROM TEMP_EXCEED_AIM..AIMProductionConstraint
		ORDER BY ConstraintId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMProductionConstraint
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 18. AIMProductionConstraintDetail
	--------------------------------------------------------
	SET @TableName = 'AIMProductionConstraintDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMProductionConstraintDetail
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMProductionConstraintDetail
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)
	
	DELETE EXCEED_AIM..AIMProductionConstraintDetail
	INSERT INTO EXCEED_AIM..AIMProductionConstraintDetail
		SELECT * FROM TEMP_EXCEED_AIM..AIMProductionConstraintDetail
		ORDER BY ConstraintId, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMProductionConstraintDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 19. AIMPromotions
	--------------------------------------------------------
	SET @TableName = 'AIMPromotions'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMPromotions
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMPromotions
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMPromotions
	INSERT INTO EXCEED_AIM..AIMPromotions
		SELECT * FROM TEMP_EXCEED_AIM..AIMPromotions
		ORDER BY PmId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMPromotions
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 20. AIMSd
	--------------------------------------------------------
	SET @TableName = 'AIMSd'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMSd
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMSd
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMSd
	INSERT INTO EXCEED_AIM..AIMSd
		SELECT [seqnbr], [ordnbr], [doctype], [prmprog], [linenbr], [cusnbr], [shipto],
		[lcid], [item], [item], [ordqty], [shpqty], [uom], [extcost], [extprice], [reason_code],
		[txndate], [txn_type], [source], [shipping], [handling], [wk_start]
		FROM TEMP_EXCEED_AIM..AIMSd
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMSd
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 21. AIMSeasons
	--------------------------------------------------------
	SET @TableName = 'AIMSeasons'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMSeasons
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMSeasons
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMSeasons
	INSERT INTO EXCEED_AIM..AIMSeasons
		SELECT * FROM TEMP_EXCEED_AIM..AIMSeasons
		ORDER BY SaVersion, SaId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMSeasons
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 22. AIMSourcingHierarchy
	--------------------------------------------------------
	SET @TableName = 'AIMSourcingHierarchy'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMSourcingHierarchy
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMSourcingHierarchy
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMSourcingHierarchy
	INSERT INTO EXCEED_AIM..AIMSourcingHierarchy
		SELECT * FROM TEMP_EXCEED_AIM..AIMSourcingHierarchy
		ORDER BY Lcid_Destination, Lcid_Source
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMSourcingHierarchy
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 23. AIMTransferPolicy
	--------------------------------------------------------
	SET @TableName = 'AIMTransferPolicy'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMTransferPolicy
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMTransferPolicy
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMTransferPolicy
	INSERT INTO EXCEED_AIM..AIMTransferPolicy
		SELECT * FROM TEMP_EXCEED_AIM..AIMTransferPolicy
		ORDER BY Lcid, LcidTransferTo, TransferType
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMTransferPolicy
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 24. AIMUOM
	--------------------------------------------------------
	SET @TableName = 'AIMUOM'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMUOM
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMUOM
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMUOM
	INSERT INTO EXCEED_AIM..AIMUOM
		SELECT * FROM TEMP_EXCEED_AIM..AIMUOM
		ORDER BY UOM
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMUOM
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 25. AIMUsers
	--------------------------------------------------------
	SET @TableName = 'AIMUsers'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMUsers
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMUsers
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMUsers
	INSERT INTO EXCEED_AIM..AIMUsers 
		SELECT * FROM TEMP_EXCEED_AIM..AIMUsers
		ORDER BY UserId
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMUsers
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 26. AIMVelCodePcts
	--------------------------------------------------------
	SET @TableName = 'AIMVelCodePcts'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMVelCodePcts
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMVelCodePcts
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMVelCodePcts
	INSERT INTO EXCEED_AIM..AIMVelCodePcts
		SELECT * FROM TEMP_EXCEED_AIM..AIMVelCodePcts
		ORDER BY VelCode
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMVelCodePcts
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 27. AIMVendors
	--------------------------------------------------------
	SET @TableName = 'AIMVendors'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMVendors
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMVendors
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	DELETE EXCEED_AIM..AIMVendors
	INSERT INTO EXCEED_AIM..AIMVendors
		SELECT * FROM TEMP_EXCEED_AIM..AIMVendors
		ORDER BY VnId, Assort
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMVendors
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 28. AIMWorkingDays
	--------------------------------------------------------
	SET @TableName = 'AIMWorkingDays'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMWorkingDays
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMWorkingDays
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMWorkingDays
	INSERT INTO EXCEED_AIM..AIMWorkingDays
		SELECT * FROM TEMP_EXCEED_AIM..AIMWorkingDays
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMWorkingDays
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 29. AIMYears
	--------------------------------------------------------
	SET @TableName = 'AIMYears'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AIMYears
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AIMYears
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AIMYears
	INSERT INTO EXCEED_AIM..AIMYears
		SELECT * FROM TEMP_EXCEED_AIM..AIMYears
		ORDER BY FiscalYear
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AIMYears
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 30. AllocDefaults
	--------------------------------------------------------
	SET @TableName = 'AllocDefaults'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AllocDefaults
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AllocDefaults
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AllocDefaults
	SET IDENTITY_INSERT EXCEED_AIM..AllocDefaults ON
	INSERT INTO EXCEED_AIM..AllocDefaults ([AllocDefaultsId], [LStatus], [LDivision], [LRegion], [LUserDefined], [ExceptionPct], [ReviewerId], [LRank])
		SELECT * FROM TEMP_EXCEED_AIM..AllocDefaults
		ORDER BY AllocDefaultsId
	SET IDENTITY_INSERT EXCEED_AIM..AllocDefaults OFF
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AllocDefaults
	SELECT @EndTime = Getdate()

	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 31. AllocDefaultsSource
	--------------------------------------------------------
	SET @TableName = 'AllocDefaultsSource'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AllocDefaultsSource
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AllocDefaultsSource
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AllocDefaultsSource
	INSERT INTO EXCEED_AIM..AllocDefaultsSource
		SELECT * FROM TEMP_EXCEED_AIM..AllocDefaultsSource
		ORDER BY AllocDefaultsId, Lcid_Source
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AllocDefaultsSource
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	--@@@@@@@@@@@@ Old Name AIMItemSubstitute @@@@@@@@@@@@@@
	-- 32. AllocItemSubstitutes
	--------------------------------------------------------
	SET @TableName = 'AllocItemSubstitutes'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..AllocItemSubstitutes
	--SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..AllocItemSubstitutes
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..AllocItemSubstitutes
	--INSERT INTO EXCEED_AIM..AllocItemSubstitutes
	--	SELECT * FROM TEMP_EXCEED_AIM..AllocItemSubstitutes
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..AllocItemSubstitutes
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 33. Alternate_Source
	--------------------------------------------------------
	SET @TableName = 'Alternate_Source'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..Alternate_Source
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..Alternate_Source
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..Alternate_Source
	INSERT INTO EXCEED_AIM..Alternate_Source
		SELECT * FROM TEMP_EXCEED_AIM..Alternate_Source
		ORDER BY Item, LcID, VnID
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..Alternate_Source
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 34. BuyerStatusTest
	--------------------------------------------------------
	SET @TableName = 'BuyerStatusTest'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..BuyerStatusTest
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..BuyerStatusTest
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..BuyerStatusTest
	SET IDENTITY_INSERT EXCEED_AIM..BuyerStatusTest ON
	INSERT INTO EXCEED_AIM..BuyerStatusTest (KeyId, [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines],
		[Released Lines],  [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts],
		[Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews],
		[Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts])
		(SELECT * FROM TEMP_EXCEED_AIM..BuyerStatusTest)
	SET IDENTITY_INSERT EXCEED_AIM..BuyerStatusTest OFF

	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..BuyerStatusTest
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 35. ForecastRepository
	--------------------------------------------------------
	SET @TableName = 'ForecastRepository'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ForecastRepository
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ForecastRepository
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ForecastRepository
	SET IDENTITY_INSERT EXCEED_AIM..ForecastRepository ON
	INSERT INTO EXCEED_AIM..ForecastRepository ( [RepositoryKey], [FcstId], [FcstVersion], [FcstDesc], [FcstType],
		[FcstStartDate],  [FcstInterval], [FcstComment], [UserIdCreate], [DateTimeCreate],
		[UserIdEdit], [DateTimeEdit])
		(SELECT * FROM TEMP_EXCEED_AIM..ForecastRepository)
	SET IDENTITY_INSERT EXCEED_AIM..ForecastRepository OFF

	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ForecastRepository
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 36. ForecastRepository_Log
	--------------------------------------------------------
	SET @TableName = 'ForecastRepository_Log'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ForecastRepository_Log
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ForecastRepository_Log
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ForecastRepository_Log
	SET IDENTITY_INSERT EXCEED_AIM..ForecastRepository_Log ON
	INSERT INTO EXCEED_AIM..ForecastRepository_Log ([FcstRepositoyKey], [FcstId], [FcstType], [UserIdEdit], [DateTimeEdit])
		(SELECT * FROM TEMP_EXCEED_AIM..ForecastRepository_Log)
	SET IDENTITY_INSERT EXCEED_AIM..ForecastRepository_Log OFF
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ForecastRepository_Log
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 37. ForecastRepositoryDetail
	--------------------------------------------------------
	SET @TableName = 'ForecastRepositoryDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ForecastRepositoryDetail
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ForecastRepositoryDetail
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ForecastRepositoryDetail
	INSERT INTO EXCEED_AIM..ForecastRepositoryDetail
		SELECT * FROM TEMP_EXCEED_AIM..ForecastRepositoryDetail
		ORDER BY RepositoryKey, Lcid, Item, FcstPds
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ForecastRepositoryDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 38. ItDefaults
	--------------------------------------------------------
	SET @TableName = 'ItDefaults'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ItDefaults
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ItDefaults
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ItDefaults
	INSERT INTO EXCEED_AIM..ItDefaults
		SELECT * FROM TEMP_EXCEED_AIM..ItDefaults
		ORDER BY LcId, Class
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ItDefaults
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 39. Item
	--------------------------------------------------------
	SET @TableName = 'Item'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..Item
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..Item
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	DELETE EXCEED_AIM..Item
	INSERT INTO EXCEED_AIM..Item
		SELECT * FROM TEMP_EXCEED_AIM..Item
		ORDER BY LcId, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..Item
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 40. ItemHistory
	--------------------------------------------------------
	SET @TableName = 'ItemHistory'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ItemHistory
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ItemHistory
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ItemHistory
	INSERT INTO EXCEED_AIM..ItemHistory
		SELECT LcId, Item, Item, HisYear,
		CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, CPS10,
		CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20,
		CPS21, CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30,
		CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40,
		CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, CPS50,
		CPS51, CPS52,
		QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10,
		QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20,
		QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30,
		QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40,
		QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50,
		QtyOrd51, QtyOrd52,
		OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10,
		OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, OrdCnt20,
		OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30,
		OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40,
		OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50,
		OrdCnt51, OrdCnt52	  
 	FROM TEMP_EXCEED_AIM..ItemHistory
	ORDER BY LcId, Item, HisYear

	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ItemHistory
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 41. ItemPricing
	--------------------------------------------------------
	SET @TableName = 'ItemPricing'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ItemPricing
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ItemPricing
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ItemPricing
	INSERT INTO EXCEED_AIM..ItemPricing
		SELECT * FROM TEMP_EXCEED_AIM..ItemPricing
		ORDER BY LcId, Item, EffectiveDateTime
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ItemPricing
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 42. ItemStatus
	--------------------------------------------------------
	SET @TableName = 'ItStatus'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..ItStatus
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..ItStatus
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..ItStatus
	INSERT INTO EXCEED_AIM..ItStatus
		SELECT * FROM TEMP_EXCEED_AIM..ItStatus
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..ItStatus
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 43. MDCDetail
	--------------------------------------------------------
	SET @TableName = 'MDCDetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..MDCDetail
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..MDCDetail
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..MDCDetail
	INSERT INTO EXCEED_AIM..MDCDetail
		SELECT * FROM TEMP_EXCEED_AIM..MDCDetail
		ORDER BY LcId, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..MDCDetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 44. MDCSummary
	--------------------------------------------------------
	SET @TableName = 'MDCSummary'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..MDCSummary
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..MDCSummary
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..MDCSummary
	INSERT INTO EXCEED_AIM..MDCSummary
		SELECT * FROM TEMP_EXCEED_AIM..MDCSummary
		ORDER BY MDC, Item
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..MDCSummary
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 45. PODetail
	--------------------------------------------------------
	SET @TableName = 'PODetail'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..PODetail
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..PODetail
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..PODetail
	SET IDENTITY_INSERT EXCEED_AIM..PODetail ON
	INSERT INTO EXCEED_AIM..PODetail ([POLineType], [POSeqID], [OrdStatus], [RevCycle], [ById], [VnId], [Assort],
		[Lcid], [Item], [ItDesc], [POType], [AvailQty], [PackRounding], [RSOQ], [SOQ], [VSOQ], [Original_VSOQ],
		[UOM], [BuyingUOM], [ConvFactor], [IsDate], [DuDate], [LastWeekSalesFlag], [Cost], [Zone])
		SELECT * FROM TEMP_EXCEED_AIM..PODetail
	SET IDENTITY_INSERT EXCEED_AIM..PODetail OFF
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..PODetail
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 46. POStatistics
	--------------------------------------------------------
	SET @TableName = 'POStatistics'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..POStatistics
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..POStatistics
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..POStatistics
	INSERT INTO EXCEED_AIM..POStatistics
		SELECT * FROM TEMP_EXCEED_AIM..POStatistics
		ORDER BY SeqNbr, ById, VnId, Assort
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..POStatistics
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	-- 47. RevCycles
	--------------------------------------------------------
	SET @TableName = 'RevCycles'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..RevCycles
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..RevCycles
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..RevCycles
	INSERT INTO EXCEED_AIM..RevCycles
		SELECT * FROM TEMP_EXCEED_AIM..RevCycles
		ORDER BY RevCycle
	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..RevCycles
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

	--------------------------------------------------------
	--@@@@@@@@@@@@ Modified for this release @@@@@@@@@@@@@@@
	-- 48. SysCtrl
	--------------------------------------------------------
	SET @TableName = 'SysCtrl'
	SET @TableCounter = @TableCounter + 1
	SELECT @Default = COUNT(*) FROM EXCEED_AIM..SysCtrl
	SELECT @OldCount = COUNT(*) FROM TEMP_EXCEED_AIM..SysCtrl
	SELECT @StartTime = Getdate()

	INSERT INTO EXCEED_AIM..EXceed_AIM_Migration_Log
	VALUES (@EAM_ID, @ProductName, @OldVersion, @NewVersion, @TableName, @Default, @OldCount, 0, 0, @StartTime, @StartTime)

	TRUNCATE TABLE EXCEED_AIM..SysCtrl
	INSERT INTO EXCEED_AIM..SysCtrl (
		[CName], [Address1], [Address2], [Address3], [City], [State], [CoZip],
		[Dft_MinRchPct], [Dft_BestRchPct],
		[DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus],
		[DxPO_Option], [KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode],
		[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], [SeasSmoothingPds],
		[CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha],
		[UpdateGOalsOption], [UpdateVnLT], [VnLTPctChgFilter],
		[VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct],
		[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex],
		[Int_SrvLvlOverrideFlag], [Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath],
		[dft_LangID], [ColonOption], [GridAutoSizeOption], [DateFormat], [TimeFormat], [UnicodeOption],
		[ProductionConstraint], [ProdConstraintGrtZero],
		[SessionId], [AllocNeedDetermination], [AllocExceptionProcess]
	) SELECT [CName], [Address1], [Address2], [Address3], [City], [State], [CoZip],
		[Dft_MinRchPct], [Dft_BestRchPct],
		[DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus],
		[DxPO_Option], [KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode],
		[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], [SeasSmoothingPds],
		[CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha],
		[UpdateGOalsOption], [UpdateVnLT], [VnLTPctChgFilter],
		[VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct],
		[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex],
		[Int_SrvLvlOverrideFlag], [Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath],
		[dft_LangID], [ColonOption], [GridAutoSizeOption], [DateFormat], [TimeFormat], [UnicodeOption],
		[ProductionConstraint], [ProdConstraintGrtZero],
		[SessionId], [AllocNeedDetermination], [AllocExceptionProcess]
	FROM TEMP_EXCEED_AIM..SysCtrl
	ORDER BY CName

	SELECT @NewCount = COUNT(*) FROM EXCEED_AIM..SysCtrl
	SELECT @EndTime = Getdate()
	SELECT @Transaction = @Transaction + @NewCount
	UPDATE EXCEED_AIM..EXceed_AIM_Migration_Log SET 
		NewDatabaseCount = @NewCount,
		EndTime = @EndTime,
		TotalTimeMillisecond = DATEDIFF(ms, @StartTime, @EndTime)
	WHERE tablename = @TableName
	AND NewVersion = @NewVersion
	PRINT CONVERT (nvarchar, @TableCounter) + ' - ' + @TableName + @MessageString + CONVERT(nvarchar, @NewCount)

--------------------------------------------------------
-- EXceed_AIM_Migration Table Update
--------------------------------------------------------
	SELECT @StartTime = StartTime FROM EXCEED_AIM..EXceed_AIM_Migration WHERE NewVersion = @NewVersion
	UPDATE EXCEED_AIM..EXceed_AIM_Migration SET 
		TransactionCount = @Transaction,
		EndTime = @EndTime
	WHERE EAM_ID = @EAM_ID

	UPDATE EXCEED_AIM..EXceed_AIM_Migration SET 
			TotalTimeMinute = DATEDIFF(mi, StartTime, EndTime)

--------------------------------------------------------
-- EXceed_AIM_Migration Display
--------------------------------------------------------
	SELECT * FROM EXCEED_AIM..EXceed_AIM_Migration WHERE NewVersion = @NewVersion
--------------------------------------------------------
-- EXceed_AIM_Migration_Log Display
--------------------------------------------------------
	SELECT * FROM EXCEED_AIM..EXceed_AIM_Migration_Log WHERE NewVersion = @NewVersion

--------------------------------------------------------
-- Re-Enable Constraints back to Alternate Vendor Table
--------------------------------------------------------
	IF EXISTS(SELECT * FROM EXceed_AIM.dbo.sysobjects where id = object_id(N'[EXceed_AIM]..[ALTERNATE_SOURCE]'))
	BEGIN
		ALTER Table EXCEED_AIM..ALTERNATE_SOURCE
		CHECK CONSTRAINT [FK_ALTERNATESOURCE_Item]
		
		ALTER TABLE EXCEED_AIM..ALTERNATE_SOURCE
		CHECK CONSTRAINT [FK_ALTERNATESOURCE_Vendor]
		PRINT 'Altered Table EXCEED_AIM..ALTERNATE_SOURCE - added constraints.'
	END

PRINT 'Finished with EXceed AIM database migration script.'
------------------------------------------------------------------------------------------
-- End Script.
------------------------------------------------------------------------------------------

SET NOCOUNT OFF
