--******************************************************
--   Purpose: Migration Script 
--   Product: EXceed AIM
--   Version: 4.4
--   Date:    7/08/2003
--   Author:  <PERSON>
--******************************************************

If not exists (select * from dbo.sysobjects where id = object_id(N'EXCEED_AIM..EXceed_AIM_Migration'))
Create Table EXCEED_AIM..EXceed_AIM_Migration
(ProductName nvarchar(50) Not Null,
OldVersion nvarchar(10) Not Null,
NewVersion nvarchar(10) Not Null,
TransactionCount bigint Not Null,
TotalTimeMinute int Not Null,
StartTime datetime Not Null,
EndTime datetime Not Null,
CONSTRAINT PK_EXceed_AIM_Migration PRIMARY KEY (ProductName, NewVersion))
Go

If not exists (select * from dbo.sysobjects where id = object_id(N'EXCEED_AIM..EXceed_AIM_Migration_Log')) 
Create Table EXCEED_AIM..EXceed_AIM_Migration_Log
(ProductName nvarchar(50) Not Null,
OldVersion nvarchar(10) Not Null,
NewVersion nvarchar(10) Not Null,
TableName nvarchar(30) Not Null,
DefaultCount bigint Not Null, 
OldDatabaseCount bigint Not Null,
NewDatabaseCount bigint Not Null,
TotalTimeMillisecond int Not Null,
StartTime datetime Not Null,
EndTime datetime Not Null,
CONSTRAINT PK_EXceed_AIM_Migration_Log PRIMARY KEY (ProductName, NewVersion, TableName))
Go

Declare @Default bigint,
	@OldCount bigint,
	@NewCount bigint,
	@StartTime datetime,
	@EndTime datetime,
	@Transaction bigint,
	@OldVersion nvarchar(10) = '4.3',
	@NewVersion nvarchar(10) = '4.4',
	@ProductName nvarchar(50) = 'EXceed_AIM'

--******************************************************
-- EXceed_AIM_Migration Table Insert
--******************************************************
Select @Transaction = 0
Select @StartTime = Getdate()
Insert into EXCEED_AIM..EXceed_AIM_Migration values
	(@ProductName,@OldVersion,@NewVersion,0,@Transaction,@StartTime,@StartTime)

--******************************************************
-- EXceed_AIM_Migration
--******************************************************
Select @Default =count(*) from EXCEED_AIM..EXceed_AIM_Migration
Select @OldCount =count(*) from TEMP_EXCEED_AIM..EXceed_AIM_Migration

Insert into EXCEED_AIM..EXceed_AIM_Migration
	Select * from TEMP_EXCEED_AIM..EXceed_AIM_Migration
	order by ProductName, NewVersion

--******************************************************
-- EXceed_AIM_Migration_Log
--******************************************************
Select @Default =count(*) from EXCEED_AIM..EXceed_AIM_Migration_Log
Select @OldCount =count(*) from TEMP_EXCEED_AIM..EXceed_AIM_Migration_Log

Truncate Table EXCEED_AIM..EXceed_AIM_Migration_Log
Insert into EXCEED_AIM..EXceed_AIM_Migration_Log
	Select * from TEMP_EXCEED_AIM..EXceed_AIM_Migration_Log
	order by ProductName, NewVersion, TableName


--******************************************************
--******************************************************
--******************************************************

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AIMAO
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMAO
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMAO
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMAO',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMAO
--Insert into EXCEED_AIM..AIMAO
--	Select * from TEMP_EXCEED_AIM..AIMAO
--	order by ORDNBR
Select @NewCount =count(*) from EXCEED_AIM..AIMAO
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMAO' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AIMAODetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMAODetail
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMAODetail
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMAODetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMAODetail
--Insert into EXCEED_AIM..AIMAODetail
--	Select * from TEMP_EXCEED_AIM..AIMAODetail
--	order by ORDNBR, LINENBR
Select @NewCount =count(*) from EXCEED_AIM..AIMAODetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMAODetail' and NewVersion = @NewVersion

--******************************************************
-- AIMClasses
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMClasses
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMClasses
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMClasses',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMClasses
Insert into EXCEED_AIM..AIMClasses
	Select * from TEMP_EXCEED_AIM..AIMClasses
	order by Class
Select @NewCount =count(*) from EXCEED_AIM..AIMClasses
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMClasses' and NewVersion = @NewVersion

--******************************************************
-- AIMDataEXchangeCtrl
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMDataEXchangeCtrl
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMDataEXchangeCtrl
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMDataEXchangeCtrl',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMDataEXchangeCtrl
Insert into EXCEED_AIM..AIMDataEXchangeCtrl
	Select * from TEMP_EXCEED_AIM..AIMDataEXchangeCtrl
	order by TxnSet
Select @NewCount =count(*) from EXCEED_AIM..AIMDataEXchangeCtrl
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMDataEXchangeCtrl' and NewVersion = @NewVersion

--******************************************************
-- AIMDays
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMDays
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMDays
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMDays',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMDays
Insert into EXCEED_AIM..AIMDays
	Select * from TEMP_EXCEED_AIM..AIMDays
	order by FYDate
Select @NewCount =count(*) from EXCEED_AIM..AIMDays
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMDays' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AIMDestinationProfile
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMDestinationProfile
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMDestinationProfile
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMDestinationProfile',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMDestinationProfile
--Insert into EXCEED_AIM..AIMDestinationProfile
--	Select * from TEMP_EXCEED_AIM..AIMDestinationProfile
--	order by Lcid_Destination, Item
Select @NewCount =count(*) from EXCEED_AIM..AIMDestinationProfile
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMDestinationProfile' and NewVersion = @NewVersion

--******************************************************
-- AIMForecast
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMForecast
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMForecast
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMForecast',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMForecast
Insert into EXCEED_AIM..AIMForecast
	Select * from TEMP_EXCEED_AIM..AIMForecast
	order by FcstId
Select @NewCount =count(*) from EXCEED_AIM..AIMForecast
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMForecast' and NewVersion = @NewVersion

--******************************************************
-- AIMForecastDetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMForecastDetail
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMForecastDetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMForecastDetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMForecastDetail
Insert into EXCEED_AIM..AIMForecastDetail
	Select * from TEMP_EXCEED_AIM..AIMForecastDetail
	order by FcstId, Lcid, Item
Select @NewCount =count(*) from EXCEED_AIM..AIMForecastDetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMForecastDetail' and NewVersion = @NewVersion

--******************************************************
-- AIMForecastFreezePds
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMForecastFreezePds
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMForecastFreezePds
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMForecastFreezePds',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMForecastFreezePds
Insert into EXCEED_AIM..AIMForecastFreezePds
	Select * from TEMP_EXCEED_AIM..AIMForecastFreezePds
	order by FcstId, Lcid
Select @NewCount =count(*) from EXCEED_AIM..AIMForecastFreezePds
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMForecastFreezePds' and NewVersion = @NewVersion

--******************************************************
-- AIMForecastMainUserAccess
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMForecastMainUserAccess
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMForecastMainUserAccess
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMForecastMainUserAccess',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMForecastMainUserAccess
Insert into EXCEED_AIM..AIMForecastMainUserAccess
	Select * from TEMP_EXCEED_AIM..AIMForecastMainUserAccess
	order by Userid, FcstId
Select @NewCount =count(*) from EXCEED_AIM..AIMForecastMainUserAccess
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMForecastMainUserAccess' and NewVersion = @NewVersion

--******************************************************
-- AIMForecastUserAccess
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMForecastUserAccess
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMForecastUserAccess
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMForecastUserAccess',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMForecastUserAccess
Insert into EXCEED_AIM..AIMForecastUserAccess
	Select * from TEMP_EXCEED_AIM..AIMForecastUserAccess
	order by Userid, FcstId, FcstType
Select @NewCount =count(*) from EXCEED_AIM..AIMForecastUserAccess
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMForecastUserAccess' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AIMItemSubstitute
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMItemSubstitute
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMItemSubstitute
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMItemSubstitute',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMItemSubstitute
--Insert into EXCEED_AIM..AIMItemSubstitute
--	Select * from TEMP_EXCEED_AIM..AIMItemSubstitute
--	order by Lcid, Item
Select @NewCount =count(*) from EXCEED_AIM..AIMItemSubstitute
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMItemSubstitute' and NewVersion = @NewVersion

--******************************************************
-- AIMLeadTime
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMLeadTime
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMLeadTime
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMLeadTime',@Default,@OldCount,0,0,@StartTime,@StartTime)
Insert into EXCEED_AIM..AIMLeadTime
	Select * from TEMP_EXCEED_AIM..AIMLeadTime
	order by LcId, Item
Select @NewCount =count(*) from EXCEED_AIM..AIMLeadTime
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMLeadTime' and NewVersion = @NewVersion

--******************************************************
-- AIMLocations
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMLocations
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMLocations
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMLocations',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMLocations
Insert into EXCEED_AIM..AIMLocations ([Lcid], [LName], [LType], [LStatus], [LDivision], [LRegion], [LUserDefined]
	[LAddress1],  [LAddress2], [LAddress3], [LAddress4], [LCity], [LState], [LZip], [LCountry], [LContact], 
	[LPhone], [LFax], [LEMail], [LRank], [PutAwayDays], [ReplenCost], [DemandSource], [LeadTimeSource], 
	[StkDate], [Dft_Byid], [Last_FcstUpdCyc], [LookBackPds], [DmdScalingFactor], [ScalingEffUntil], 
	[Freeze_Period], [UpdateCurrentYearOption], [DropShip_XDock], [Dft_ReviewerId], [ExceptionPct])
	(Select [Lcid], [LName], 'D', 'A', '', '', ''
		[LAddress1],  [LAddress2], '', '', [LCity], [LState], [LZip], 'USA', [LContact], 
		[LPhone], [LFax], [LEMail], 1, [PutAwayDays], [ReplenCost], [DemandSource], [LeadTimeSource], 
		[StkDate], [Dft_Byid], [Last_FcstUpdCyc], [LookBackPds], [DmdScalingFactor], [ScalingEffUntil], 
		[Freeze_Period], [UpdateCurrentYearOption], [DropShip_XDock], 'sa', 95 
	from TEMP_EXCEED_AIM..AIMLocations
	order by LcId)
Select @NewCount =count(*) from EXCEED_AIM..AIMLocations
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMLocations' and NewVersion = @NewVersion

--******************************************************
-- AIMOptions
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMOptions
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMOptions
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMOptions',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMOptions
Insert into EXCEED_AIM..AIMOptions
	Select * from TEMP_EXCEED_AIM..AIMOptions
	order by OptionId
Select @NewCount =count(*) from EXCEED_AIM..AIMOptions
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMOptions' and NewVersion = @NewVersion

--******************************************************
-- AIMPO
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMPO
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMPO
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMPO',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMPO
Insert into EXCEED_AIM..AIMPO([ById], [VnId], [Assort], [POStatus], [TransmitPO], [ShipIns], [UserInitials], 
	[Remarks1], [Remarks2], [Remarks3], [AddrOverride], [VName], [VAddress1], [VAddress2], [VCity], 
	[VState], [VZip], [LineCount], [PosLineCount], [Vn_Min], [Vn_Best], [Reach_Code], [POSource], 
	[POByZone], [Dft_LeadTime], [TotalCost], [VndSizeFlag])
	Select [ById], [VnId], [Assort], [POStatus], [TransmitPO], [ShipIns], [UserInitials], [Remarks1], 
		[Remarks2], [Remarks3], [AddrOverride], [VName], [VAddress1], [VAddress2], [VCity], [VState],
		[VZip], [LineCount], [PosLineCount], [Vn_Min], [Vn_Best], [Reach_Code], [POSource], 
		[POByZone], [Dft_LeadTime], [TotalCost], 'Y') 
	from TEMP_EXCEED_AIM..AIMPO
	order by ById, VnId, Assort
Select @NewCount =count(*) from EXCEED_AIM..AIMPO
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMPO' and NewVersion = @NewVersion

--******************************************************
-- AIMProductionConstraint
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMProductionConstraint
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMProductionConstraint
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMProductionConstraint',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMProductionConstraint
Insert into EXCEED_AIM..AIMProductionConstraint
	Select * from TEMP_EXCEED_AIM..AIMProductionConstraint
	order by ConstraintId
Select @NewCount =count(*) from EXCEED_AIM..AIMProductionConstraint
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMProductionConstraint' and NewVersion = @NewVersion

--******************************************************
-- AIMProductionConstraintDetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMProductionConstraintDetail
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMProductionConstraintDetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMProductionConstraintDetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMProductionConstraintDetail
Insert into EXCEED_AIM..AIMProductionConstraintDetail
	Select * from TEMP_EXCEED_AIM..AIMProductionConstraintDetail
	order by ConstraintId, Item
Select @NewCount =count(*) from EXCEED_AIM..AIMProductionConstraintDetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMProductionConstraintDetail' and NewVersion = @NewVersion

--******************************************************
-- AIMPromotions
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMPromotions
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMPromotions
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMPromotions',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMPromotions
Insert into EXCEED_AIM..AIMPromotions
	Select * from TEMP_EXCEED_AIM..AIMPromotions
	order by PmId
Select @NewCount =count(*) from EXCEED_AIM..AIMPromotions
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMPromotions' and NewVersion = @NewVersion

--******************************************************
-- AIMSd
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMSd
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMSd
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMSd',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMSd
Insert into EXCEED_AIM..AIMSd
	Select * from TEMP_EXCEED_AIM..AIMSd
Select @NewCount =count(*) from EXCEED_AIM..AIMSd
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMSd' and NewVersion = @NewVersion

--******************************************************
-- AIMSeasons
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMSeasons
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMSeasons
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMSeasons',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMSeasons
Insert into EXCEED_AIM..AIMSeasons
	Select * from TEMP_EXCEED_AIM..AIMSeasons
	order by SaVersion, SaId
Select @NewCount =count(*) from EXCEED_AIM..AIMSeasons
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMSeasons' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AIMSourcingHierarchy
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMSourcingHierarchy
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMSourcingHierarchy
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMSourcingHierarchy',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMSourcingHierarchy
--Insert into EXCEED_AIM..AIMSourcingHierarchy
--	Select * from TEMP_EXCEED_AIM..AIMSourcingHierarchy
--	order by Lcid_Destination, Lcid_Source
Select @NewCount =count(*) from EXCEED_AIM..AIMSourcingHierarchy
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMSourcingHierarchy' and NewVersion = @NewVersion

--******************************************************
-- AIMTransferPolicy
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMTransferPolicy
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMTransferPolicy
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMTransferPolicy',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMTransferPolicy
Insert into EXCEED_AIM..AIMTransferPolicy
	Select * from TEMP_EXCEED_AIM..AIMTransferPolicy
	order by Lcid, LcidTransferTo, TransferType
Select @NewCount =count(*) from EXCEED_AIM..AIMTransferPolicy
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMTransferPolicy' and NewVersion = @NewVersion

--******************************************************
-- AIMUOM
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMUOM
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMUOM
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMUOM',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMUOM
Insert into EXCEED_AIM..AIMUOM
	Select * from TEMP_EXCEED_AIM..AIMUOM
	order by UOM
Select @NewCount =count(*) from EXCEED_AIM..AIMUOM
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMUOM' and NewVersion = @NewVersion

--******************************************************
-- AIMUsers
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMUsers
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMUsers
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMUsers',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMUsers
Insert into EXCEED_AIM..AIMUsers([UserId], [UserName], [SLevel], [ScArray], [DmdUpdExcpts], [OnPromotionExcpts],
 	[ExtCostExcpts], [ExtCostLimit], [PackSizeExcpts], [PS_PctRoundLimit], [PS_ExtCostLimit], [LastWeekSaleExcpts], 
	[OverDuePO], [SafetyStockEroded], [BackOrdered], [InvAvailRestriction], [InvAvailList], [UserInitials], [LangID], 
	[LogErrors], [AltSourceExcpts]) 
	Select [UserID], [UserName], [SLevel], [ScArray], [DmdUpdExcpts], [OnPromotionExcpts], [ExtCostExcpts], 
	[ExtCostLimit], [PackSizeExcpts], [PS_PctRoundLimit], [PS_ExtCostLimit], [LastWeekSaleExcpts], [OverDuePO], [SafetyStockEroded],
	[BackOrdered], [InvAvailRestriction], [InvAvailList], [UserInitials], [LangID], [LogErrors], 'N'
	from TEMP_EXCEED_AIM..AIMUsers
	order by UserId
Select @NewCount =count(*) from EXCEED_AIM..AIMUsers
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMUsers' and NewVersion = @NewVersion

--******************************************************
-- AIMVelCodePcts
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMVelCodePcts
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMVelCodePcts
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMVelCodePcts',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMVelCodePcts
Insert into EXCEED_AIM..AIMVelCodePcts
	Select * from TEMP_EXCEED_AIM..AIMVelCodePcts
	order by VelCode
Select @NewCount =count(*) from EXCEED_AIM..AIMVelCodePcts
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMVelCodePcts' and NewVersion = @NewVersion

--******************************************************
-- AIMVendors
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMVendors
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMVendors
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMVendors',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMVendors
Insert into EXCEED_AIM..AIMVendors
	Select * from TEMP_EXCEED_AIM..AIMVendors
	order by VnId, Assort
Select @NewCount =count(*) from EXCEED_AIM..AIMVendors
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMVendors' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AIMWorkingDays
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMWorkingDays
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMWorkingDays
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMWorkingDays',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMWorkingDays
--Insert into EXCEED_AIM..AIMWorkingDays
--	Select * from TEMP_EXCEED_AIM..AIMWorkingDays
Select @NewCount =count(*) from EXCEED_AIM..AIMWorkingDays
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMWorkingDays' and NewVersion = @NewVersion

--******************************************************
-- AIMYears
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AIMYears
Select @OldCount =count(*) from TEMP_EXCEED_AIM..AIMYears
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AIMYears',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AIMYears
Insert into EXCEED_AIM..AIMYears
	Select * from TEMP_EXCEED_AIM..AIMYears
	order by FiscalYear
Select @NewCount =count(*) from EXCEED_AIM..AIMYears
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AIMYears' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AllocDefaults
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AllocDefaults
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AllocDefaults
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AllocDefaults',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AllocDefaults
--Insert into EXCEED_AIM..AllocDefaults
--	Select * from TEMP_EXCEED_AIM..AllocDefaults
--	order by AllocDefaultsId
Select @NewCount =count(*) from EXCEED_AIM..AllocDefaults
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AllocDefaults' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- AllocDefaultsSource
--******************************************************
Select @Default =count(*) from EXCEED_AIM..AllocDefaultsSource
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..AllocDefaultsSource
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'AllocDefaultsSource',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..AllocDefaultsSource
--Insert into EXCEED_AIM..AllocDefaultsSource
--	Select * from TEMP_EXCEED_AIM..AllocDefaultsSource
--	order by AllocDefaultsId, Lcid_Source
Select @NewCount =count(*) from EXCEED_AIM..AllocDefaultsSource
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'AllocDefaultsSource' and NewVersion = @NewVersion

--******************************************************
--@@@@@@@@@@@@@@@@@@@@ N E W  T A B L E @@@@@@@@@@@@@@@@
-- Alternate_Source
--******************************************************
Select @Default =count(*) from EXCEED_AIM..Alternate_Source
--Select @OldCount =count(*) from TEMP_EXCEED_AIM..Alternate_Source
Set @OldCount = 0 
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'Alternate_Source',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..Alternate_Source
--Insert into EXCEED_AIM..Alternate_Source
--	Select * from TEMP_EXCEED_AIM..Alternate_Source
--	order by Item, Lcid, Vnid
Select @NewCount =count(*) from EXCEED_AIM..Alternate_Source
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount,EndTime = @EndTime,TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'Alternate_Source' and NewVersion = @NewVersion

--******************************************************
-- BuyerStatusTest
--******************************************************
Select @Default =count(*) from EXCEED_AIM..BuyerStatusTest
Select @OldCount =count(*) from TEMP_EXCEED_AIM..BuyerStatusTest
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'BuyerStatusTest',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..BuyerStatusTest
Set Identity_Insert EXCEED_AIM..BuyerStatusTest On
Insert into EXCEED_AIM..BuyerStatusTest (KeyId, [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines],
	[Released Lines],  [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts],
	[Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews], 
	[Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts])
	(Select * from TEMP_EXCEED_AIM..BuyerStatusTest)
Set Identity_Insert EXCEED_AIM..BuyerStatusTest Off
Select @NewCount =count(*) from EXCEED_AIM..BuyerStatusTest
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'BuyerStatusTest' and NewVersion = @NewVersion

--******************************************************
-- ForecastRepository
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ForecastRepository
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ForecastRepository
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ForecastRepository',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ForecastRepository
Set Identity_Insert EXCEED_AIM..ForecastRepository On
Insert into EXCEED_AIM..ForecastRepository (KeyId, [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines],
	[Released Lines],  [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts],
	[Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews], 
	[Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts])
	(Select * from TEMP_EXCEED_AIM..ForecastRepository)
Set Identity_Insert EXCEED_AIM..ForecastRepository Off
Select @NewCount =count(*) from EXCEED_AIM..ForecastRepository
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ForecastRepository' and NewVersion = @NewVersion

--******************************************************
-- ForecastRepository_Log
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ForecastRepository_Log
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ForecastRepository_Log
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ForecastRepository_Log',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ForecastRepository_Log
Set Identity_Insert EXCEED_AIM..ForecastRepository_Log On
Insert into EXCEED_AIM..ForecastRepository_Log (KeyId, [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines],
	[Released Lines],  [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts],
	[Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews], 
	[Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts])
	(Select * from TEMP_EXCEED_AIM..ForecastRepository_Log)
Set Identity_Insert EXCEED_AIM..ForecastRepository_Log Off
Select @NewCount =count(*) from EXCEED_AIM..ForecastRepository_Log
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ForecastRepository_Log' and NewVersion = @NewVersion

--******************************************************
-- ForecastRepositoryDetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ForecastRepositoryDetail
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ForecastRepositoryDetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ForecastRepositoryDetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ForecastRepositoryDetail
Insert into EXCEED_AIM..ForecastRepositoryDetail
	Select * from TEMP_EXCEED_AIM..ForecastRepositoryDetail
	order by RepositoryKey, Lcid, Item, FcstPds
Select @NewCount =count(*) from EXCEED_AIM..ForecastRepositoryDetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ForecastRepositoryDetail' and NewVersion = @NewVersion

--******************************************************
-- ItDefaults
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ItDefaults
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ItDefaults
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ItDefaults',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ItDefaults
Insert into EXCEED_AIM..ItDefaults
	Select * from TEMP_EXCEED_AIM..ItDefaults
	order by LcId, Class
Select @NewCount =count(*) from EXCEED_AIM..ItDefaults
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ItDefaults' and NewVersion = @NewVersion

--******************************************************
-- Item
--******************************************************
Select @Default =count(*) from EXCEED_AIM..Item
Select @OldCount =count(*) from TEMP_EXCEED_AIM..Item
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'Item',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..Item
Insert into EXCEED_AIM..Item([Lcid], [Item], [ItDesc], [ItStat], [ActDate], [InActDate], [OptionID], 
	[Class1], [Class2], [Class3], [Class4], [BinLocation], [BuyStrat], [VelCode], [VnId], 
	[Assort], [ById], [MDC], [MDCFlag], [SaId], [PmId], [UPC], [Weight], [Cube], [ListPrice], 
	[Price], [Cost], [BkQty01], [BkCost01], [BkQty02], [BkCost02], [BkQty03], [BkCost03], 
	[BkQty04], [BkCost04], [BkQty05], [BkCost05], [BkQty06], [BkCost06], [BkQty07], [BkCost07], 
	[BkQty08], [BkCost08], [BkQty09], [BkCost09], [BkQty10], [BkCost10], [UOM], [ConvFactor], 
	[BuyingUOM], [ReplenCost2], [Oh], [Oo], [ComStk], [BkOrder], [BkComStk], [LeadTime], 
	[PackRounding], [IMin], [IMax], [CStock], [SSAdj], [UserMin], [UserMax], [UserMethod], 
	[FcstMethod], [FcstDemand], [UserFcst], [UserFcstExpDate], [MAE], [MSE], [Trend], 
	[FcstCycles], [ZeroCount], [Mean_NZ], [StdDev_NZ], [IntSafetyStock], [IsIntermittent], 
	[DIFlag], [DmdFilterFlag], [TrkSignalFlag], [UserDemandFlag], [ByPassPct], [FcstUpdCyc], 
	[LTVFact], [PlnTT], [ZOPSw], [OUTLSw], [ZSStock], [DSer], [Freeze_BuyStrat], [Freeze_Byid], 
	[Freeze_LeadTime], [Freeze_OptionID], [Freeze_DSer], [OldItem], [Accum_Lt], [ReviewTime], 
	[OrderPt], [OrderQty], [SafetyStock], [FcstRT], [FcstLT], [Fcst_Month], [Fcst_Quarter], 
	[Fcst_Year], [FcstDate], [VC_Amt], [VC_Units_Ranking], [VC_Amt_Ranking], [VC_Date], 
	[VelCode_Prev], [VC_Amt_Prev], [OnPromotion], [AvgOh], 
	[NextPONbr_1], [NextPODate_1], [NextPOQty_1], [NextPONbr_2], [NextPODate_2], [NextPOQty_2], 
	[NextPONbr_3], [NextPODate_3], [NextPOQty_3], [UserRef1], [UserRef2], [UserRef3], 
	[Freeze_Forecast], [ProductionConstraint], [AllocatableQty], [ALtVnFlag])
	Select [Lcid], [Item], [ItDesc], [ItStat], [ActDate], [InActDate], [OptionID], 
		[Class1], [Class2], [Class3], [Class4], [BinLocation], [BuyStrat], [VelCode], [VnId], 
		[Assort], [ById], [MDC], [MDCFlag], [SaId], [PmId], [UPC], [Weight], [Cube], [ListPrice], 
		[Price], [Cost], [BkQty01], [BkCost01], [BkQty02], [BkCost02], [BkQty03], [BkCost03], 
		[BkQty04], [BkCost04], [BkQty05], [BkCost05], [BkQty06], [BkCost06], [BkQty07], [BkCost07], 
		[BkQty08], [BkCost08], [BkQty09], [BkCost09], [BkQty10], [BkCost10], [UOM], [ConvFactor], 
		[BuyingUOM], [ReplenCost2], [Oh], [Oo], [ComStk], [BkOrder], [BkComStk], [LeadTime], 
		[PackRounding], [IMin], [IMax], [CStock], [SSAdj], [UserMin], [UserMax], [UserMethod], 
		[FcstMethod], [FcstDemand], [UserFcst], [UserFcstExpDate], [MAE], [MSE], [Trend], 
		[FcstCycles], [ZeroCount], [Mean_NZ], [StdDev_NZ], [IntSafetyStock], [IsIntermittent], 
		[DIFlag], [DmdFilterFlag], [TrkSignalFlag], [UserDemandFlag], [ByPassPct], [FcstUpdCyc], 
		[LTVFact], [PlnTT], [ZOPSw], [OUTLSw], [ZSStock], [DSer], [Freeze_BuyStrat], [Freeze_Byid], 
		[Freeze_LeadTime], [Freeze_OptionID], [Freeze_DSer], [OldItem], [Accum_Lt], [ReviewTime], 
		[OrderPt], [OrderQty], [SafetyStock], [FcstRT], [FcstLT], [Fcst_Month], [Fcst_Quarter], 
		[Fcst_Year], [FcstDate], [VC_Amt], [VC_Units_Ranking], [VC_Amt_Ranking], [VC_Date], 
		[VelCode_Prev], [VC_Amt_Prev], [OnPromotion], [AvgOh], 
		[NextPONbr_1], [NextPODate_1], [NextPOQty_1], [NextPONbr_2], [NextPODate_2], [NextPOQty_2], 
		[NextPONbr_3], [NextPODate_3], [NextPOQty_3], [UserRef1], [UserRef2], [UserRef3], 
		[Freeze_Forecast], [ProductionConstraint], 0, 'N'
	from TEMP_EXCEED_AIM..Item
	order by LcId, Item
Select @NewCount =count(*) from EXCEED_AIM..Item
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'Item' and NewVersion = @NewVersion

--******************************************************
-- ItemHistory
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ItemHistory
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ItemHistory
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ItemHistory',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ItemHistory
Insert into EXCEED_AIM..ItemHistory
	Select * from TEMP_EXCEED_AIM..ItemHistory
	order by LcId, Item, HisYear
Select @NewCount =count(*) from EXCEED_AIM..ItemHistory
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ItemHistory' and NewVersion = @NewVersion

--******************************************************
-- ItemPricing
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ItemPricing
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ItemPricing
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ItemPricing',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ItemPricing
Insert into EXCEED_AIM..ItemPricing
	Select * from TEMP_EXCEED_AIM..ItemPricing
	order by LcId, Item, EffectiveDateTime
Select @NewCount =count(*) from EXCEED_AIM..ItemPricing
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ItemPricing' and NewVersion = @NewVersion

--******************************************************
-- ItemStatus
--******************************************************
Select @Default =count(*) from EXCEED_AIM..ItemStatus
Select @OldCount =count(*) from TEMP_EXCEED_AIM..ItemStatus
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'ItemStatus',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..ItemStatus
Insert into EXCEED_AIM..ItemStatus
	Select * from TEMP_EXCEED_AIM..ItemStatus
	order by LcId, Item, EffectiveDateTime
Select @NewCount =count(*) from EXCEED_AIM..ItemStatus
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'ItemStatus' and NewVersion = @NewVersion

--******************************************************
-- MDCDetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..MDCDetail
Select @OldCount =count(*) from TEMP_EXCEED_AIM..MDCDetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'MDCDetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..MDCDetail
Insert into EXCEED_AIM..MDCDetail
	Select * from TEMP_EXCEED_AIM..MDCDetail
	order by LcId, Item
Select @NewCount =count(*) from EXCEED_AIM..MDCDetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'MDCDetail' and NewVersion = @NewVersion

--******************************************************
-- MDCSummary
--******************************************************
Select @Default =count(*) from EXCEED_AIM..MDCSummary
Select @OldCount =count(*) from TEMP_EXCEED_AIM..MDCSummary
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'MDCSummary',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..MDCSummary
Insert into EXCEED_AIM..MDCSummary
	Select * from TEMP_EXCEED_AIM..MDCSummary
	order by MDC, Item
Select @NewCount =count(*) from EXCEED_AIM..MDCSummary
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'MDCSummary' and NewVersion = @NewVersion

--******************************************************
-- PODetail
--******************************************************
Select @Default =count(*) from EXCEED_AIM..PODetail
Select @OldCount =count(*) from TEMP_EXCEED_AIM..PODetail
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'PODetail',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..PODetail
Set Identity_Insert EXCEED_AIM..PODetail on
Insert into EXCEED_AIM..PODetail 
	(Select * from TEMP_EXCEED_AIM..PODetail)
Set Identity_Insert EXCEED_AIM..PODetail Off
Select @NewCount =count(*) from EXCEED_AIM..PODetail
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'PODetail' and NewVersion = @NewVersion

--******************************************************
-- POStatistics
--******************************************************
Select @Default =count(*) from EXCEED_AIM..POStatistics
Select @OldCount =count(*) from TEMP_EXCEED_AIM..POStatistics
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'POStatistics',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..POStatistics
Insert into EXCEED_AIM..POStatistics
	Select * from TEMP_EXCEED_AIM..POStatistics
	order by SeqNbr, ById, VnId, Assort
Select @NewCount =count(*) from EXCEED_AIM..POStatistics
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'POStatistics' and NewVersion = @NewVersion

--******************************************************
-- RevCycles
--******************************************************
Select @Default =count(*) from EXCEED_AIM..RevCycles
Select @OldCount =count(*) from TEMP_EXCEED_AIM..RevCycles
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'RevCycles',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..RevCycles
Insert into EXCEED_AIM..RevCycles
	Select * from AIM41..RevCycles
	order by RevCycle
Select @NewCount =count(*) from EXCEED_AIM..RevCycles
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'RevCycles' and NewVersion = @NewVersion

--******************************************************
-- SysCtrl
--******************************************************
Select @Default =count(*) from EXCEED_AIM..SysCtrl
Select @OldCount =count(*) from TEMP_EXCEED_AIM..SysCtrl
Select @StartTime = Getdate()

Insert into EXCEED_AIM..EXceed_AIM_Migration_Log values
	(@ProductName,@OldVersion,@NewVersion,'SysCtrl',@Default,@OldCount,0,0,@StartTime,@StartTime)
Truncate Table EXCEED_AIM..SysCtrl
Insert into EXCEED_AIM..SysCtrl([CName], [Address1], [Address2], [Address3], [City], [State], [CoZip],
	[Dft_MinRchPct], [Dft_BestRchPct], [DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus], 
	[DxPO_Option], [KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode], 
	[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], 
	[SeasSmoothingPds], [CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha], [UpdateGoalsOption], 
	[UpdateVnLT], [VnLTPctChgFilter], [VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct], 
	[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex], [Int_SrvLvlOverrideFlag], 
	[Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath], [dft_LangID], [ColonOption], [GridAutoSizeOption], 	
	[DateFormat], [TimeFormat], [UnicodeOption], [ProductionConstraint], [ProdConstraintGrtZero], [SessionID], 
	[AllocNeedDetermination], [AllocExceptionProcess])
	Select [CName], [Address1], [Address2], [Address3], [City], [State], [CoZip], [Dft_MinRchPct], 
		[Dft_BestRchPct], [DxPath], [DxPath_Out], [DxPath_Backup], [Dft_POStatus], [DxPO_Option], 
		[KFactor], [MinROAI], [Vendor_Sizing], [Priority_Min_Dser], [Priority_Min_VelCode], 
		[Addl_LTDays], [Lt_Filter_Pct], [Retain_SD_Opt], [Calc_Perform], [LogFile], [ByIdSource], 
		[SeasSmoothingPds], [CurSaVersion], [Last_POSeq], [ClassOption], [AvgInvAlpha], [UpdateGoalsOption], 
		[UpdateVnLT], [VnLTPctChgFilter], [VnLTDaysChgFilter], [MDCOption], [LT_Alpha], [LT_CtrlInterval_Pct], 
		[Int_Enabled], [Int_MinPctZero], [Int_MaxPctZero], [Int_SeasonalityIndex], [Int_SrvLvlOverrideFlag], 
		[Int_SrvLvl], [Int_LookBackPds], [AIMBatchPath], [dft_LangID], [ColonOption], [GridAutoSizeOption], 
		[DateFormat], [TimeFormat], [UnicodeOption], [ProductionConstraint], 'Y', 1, 0, 0
	from TEMP_EXCEED_AIM..SysCtrl
	order by CName
Select @NewCount =count(*) from EXCEED_AIM..SysCtrl
Select @EndTime = Getdate()
Select @Transaction = @Transaction + @NewCount
Update EXCEED_AIM..EXceed_AIM_Migration_Log set NewDatabaseCount = @NewCount, EndTime = @EndTime, TotalTimeMillisecond = datediff(ms,@StartTime,@EndTime)
	where tablename = 'SysCtrl' and NewVersion = @NewVersion

--******************************************************
-- EXceed_AIM_Migration Table Update
--******************************************************
Select @StartTime = StartTime from EXCEED_AIM..EXceed_AIM_Migration where NewVersion = @NewVersion
Update EXCEED_AIM..EXceed_AIM_Migration set TransactionCount = @Transaction, EndTime = @EndTime, TotalTimeMinute = datediff(mi,@StartTime,@EndTime)
	where NewVersion = @NewVersion

--******************************************************
-- EXceed_AIM_Migration Display
--******************************************************
Select * from EXCEED_AIM..EXceed_AIM_Migration where NewVersion = @NewVersion
--******************************************************
-- EXceed_AIM_Migration_Log Display
--******************************************************
Select * from EXCEED_AIM..EXceed_AIM_Migration_Log where NewVersion = @NewVersion


