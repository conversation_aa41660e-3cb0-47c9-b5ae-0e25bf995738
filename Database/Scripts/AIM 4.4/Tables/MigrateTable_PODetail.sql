/*

   Friday, May 16, 2003 1:51:43 PM

   User: 

   Server: WRIZ<PERSON>AP

   Database: EXCEED_AIM

   Application: MS SQLEM - Data Tools

*/



BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
ALTER TABLE dbo.PODetail
	DROP CONSTRAINT DF_PODetail_OrdStatus
GO
ALTER TABLE dbo.PODetail
	DROP CONSTRAINT DF_PODetail_ItDesc
GO
ALTER TABLE dbo.PODetail
	DROP CONSTRAINT DF_PODetail_Original_VSOQ
GO
ALTER TABLE dbo.PODetail
	DROP CONSTRAINT DF_PODetail_LastWeekSalesFlag
GO
ALTER TABLE dbo.PODetail
	DROP CONSTRAINT DF_PODetail_Cost
GO
ALTER TABLE dbo.PODetail
	DROP CONSTRAINT DF_PODetail_Zone
GO
CREATE TABLE dbo.Tmp_PODetail
	(
	POLineType smallint NOT NULL,
	POSeqID int NOT NULL IDENTITY (1, 1),
	OrdStatus nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	RevCycle nvarchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ById nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	VnId nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Assort nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Lcid nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Item nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ItDesc nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	POType nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	AvailQty int NOT NULL,
	PackRounding nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	RSOQ int NOT NULL,
	SOQ int NOT NULL,
	VSOQ int NOT NULL,
	Original_VSOQ int NOT NULL,
	UOM nvarchar(6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	BuyingUOM nvarchar(6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ConvFactor int NOT NULL,
	IsDate datetime NOT NULL,
	DuDate datetime NOT NULL,
	LastWeekSalesFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Cost numeric(10, 4) NOT NULL,
	[Zone] nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_PODetail ADD CONSTRAINT
	DF_PODetail_OrdStatus DEFAULT ('P') FOR OrdStatus
GO
ALTER TABLE dbo.Tmp_PODetail ADD CONSTRAINT
	DF_PODetail_ItDesc DEFAULT ('') FOR ItDesc
GO
ALTER TABLE dbo.Tmp_PODetail ADD CONSTRAINT
	DF_PODetail_Original_VSOQ DEFAULT (0) FOR Original_VSOQ
GO
ALTER TABLE dbo.Tmp_PODetail ADD CONSTRAINT
	DF_PODetail_LastWeekSalesFlag DEFAULT ('N') FOR LastWeekSalesFlag
GO
ALTER TABLE dbo.Tmp_PODetail ADD CONSTRAINT
	DF_PODetail_Cost DEFAULT (0) FOR Cost
GO
ALTER TABLE dbo.Tmp_PODetail ADD CONSTRAINT
	DF_PODetail_Zone DEFAULT ('') FOR [Zone]
GO
SET IDENTITY_INSERT dbo.Tmp_PODetail ON
GO
IF EXISTS(SELECT * FROM dbo.PODetail)
	 EXEC('INSERT INTO dbo.Tmp_PODetail (POLineType, POSeqID, OrdStatus, RevCycle, ById, VnId, Assort, Lcid, Item, ItDesc, POType, AvailQty, PackRounding, RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, ConvFactor, IsDate, DuDate, LastWeekSalesFlag, Cost, [Zone])
		SELECT POLineType, POSeqID, OrdStatus, RevCycle, ById, VnId, Assort, Lcid, Item, ItDesc, POType, AvailQty, PackRounding, RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, ConvFactor, IsDate, DuDate, LastWeekSalesFlag, Cost, [Zone] FROM dbo.PODetail TABLOCKX')
GO
SET IDENTITY_INSERT dbo.Tmp_PODetail OFF
GO
DROP TABLE dbo.PODetail
GO
EXECUTE sp_rename N'dbo.Tmp_PODetail', N'PODetail', 'OBJECT'
GO
ALTER TABLE dbo.PODetail ADD CONSTRAINT
	PK_PODetail PRIMARY KEY NONCLUSTERED 
	(
	POSeqID
	) ON [PRIMARY]

GO
CREATE NONCLUSTERED INDEX IX_ById ON dbo.PODetail
	(
	ById
	) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX IX_VnIdAssort ON dbo.PODetail
	(
	ById,
	VnId,
	Assort,
	Item,
	Lcid
	) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX IDX_PODet_ItemLcId ON dbo.PODetail
	(
	Lcid,
	Item
	) ON [PRIMARY]
GO
COMMIT
