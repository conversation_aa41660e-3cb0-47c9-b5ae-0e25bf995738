/*

   Friday, May 02, 2003 3:58:16 PM

   User: 

   Server: WRIZALAP

   Database: EXCEED_AIM_DEMO

   Application: MS SQLEM - Data Tools

*/



BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
CREATE TABLE dbo.Tmp_ForecastRepositoryBatch
	(
	RepositoryKey numeric(18, 0) NOT NULL,
	LcID nvarchar(12) NOT NULL,
	Item nvarchar(25) NOT NULL,
	FcstPds tinyint NOT NULL,
	SysFcst decimal(9, 2) NULL,
	SysNet decimal(9, 2) NULL,
	ModSysFcst int NULL,
	ModSysNet int NULL,
	PlannedRct decimal(9, 2) NULL
	)  ON [PRIMARY]
GO
IF EXISTS(SELECT * FROM dbo.ForecastRepositoryBatch)
	 EXEC('INSERT INTO dbo.Tmp_ForecastRepositoryBatch (RepositoryKey, LcID, Item, FcstPds, SysFcst, SysNet, ModSysFcst, ModSysNet, PlannedRct)
		SELECT RepositoryKey, LcID, Item, FcstPds, CONVERT(decimal(9, 2), SysFcst), CONVERT(decimal(9, 2), SysNet), ModSysFcst, ModSysNet, CONVERT(decimal(9, 2), PlannedRct) FROM dbo.ForecastRepositoryBatch TABLOCKX')
GO
DROP TABLE dbo.ForecastRepositoryBatch
GO
EXECUTE sp_rename N'dbo.Tmp_ForecastRepositoryBatch', N'ForecastRepositoryBatch', 'OBJECT'
GO
ALTER TABLE dbo.ForecastRepositoryBatch ADD CONSTRAINT
	PK_ForecastRepositoryBatch PRIMARY KEY CLUSTERED 
	(
	RepositoryKey,
	LcID,
	Item,
	FcstPds
	) ON [PRIMARY]

GO
COMMIT
