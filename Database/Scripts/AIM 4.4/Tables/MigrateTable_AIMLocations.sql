/*

   Friday, May 16, 2003 10:01:23 AM

   User: 

   Server: WRIZALAP

   Database: EXCEED_AIM

   Application: MS SQLEM - Data Tools

*/



BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT TRANSACTION

-- BEGIN TRANSACTION
ALTER TABLE [dbo].[AIMLocations] DROP
	CONSTRAINT DF_Locations_LName 
	, CONSTRAINT DF_Locations_LAddress1 
	, CONSTRAINT DF_Locations_LAddress2 
	, CONSTRAINT DF_Locations_LCity 
	, CONSTRAINT DF_Locations_LState 
	, CONSTRAINT DF_Locations_LZip 
	, CONSTRAINT DF_Locations_LContact 
	, CONSTRAINT DF_Locations_LPhone 
	, CONSTRAINT DF_Locations_LFax 
	, CONSTRAINT DF_Locations_LEMail 
	, CONSTRAINT DF_Locations_PutAwayDays 
	, CONSTRAINT DF_Locations_ReplenCost 
	, CONSTRAINT DF_Locations_DemandSource 
	, CONSTRAINT DF_Locations_LeadTimeSource 
	, CONSTRAINT DF_Locations_StkDate 
	, CONSTRAINT DF_Locations_Dft_Byid 
	, CONSTRAINT DF_Locations_Last_FcstUpdCyc 
	, CONSTRAINT DF_Locations_LookBackPds 
	, CONSTRAINT DF_AIMLocations_DmdScalingFactor 
	, CONSTRAINT DF_AIMLocations_ScalingEffUntil 
	, CONSTRAINT DF_AIMLocations_Freeze_Period 
	, CONSTRAINT DF_AIMLocations_UpdateCurrentYearOption 
	, CONSTRAINT DF_AIMLocations_DropShip_XDock 
GO

CREATE TABLE [dbo].[Tmp_AIMLocations]
	(
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LAddress3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LAddress4] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LCountry] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LRank] [smallint] NULL,
	[PutAwayDays] [tinyint] NOT NULL,
	[ReplenCost] [decimal] (11, 4) NOT NULL,
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LeadTimeSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[StkDate] [smalldatetime] NOT NULL,
	[Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Last_FcstUpdCyc] [int] NOT NULL,
	[LookBackPds] [smallint] NOT NULL,
	[DmdScalingFactor] [decimal] (4, 3) NOT NULL,
	[ScalingEffUntil] [datetime] NOT NULL,
	[Freeze_Period] [int] NOT NULL,
	[UpdateCurrentYearOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[DropShip_XDock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Dft_ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[ExceptionPct] [tinyint] NULL
	)  ON [PRIMARY]
GO

ALTER TABLE AIMLocations DROP
 	CONSTRAINT DF_AIMLocations_LType 
	, CONSTRAINT DF_AIMLocations_LStatus 
	, CONSTRAINT DF_AIMLocations_Dft_ReviewerId 
	, CONSTRAINT DF_AIMLocations_ExceptionPct
GO

ALTER TABLE [dbo].[Tmp_AIMLocations] ADD 
	CONSTRAINT DF_Locations_LName DEFAULT ('') FOR LName
 	, CONSTRAINT DF_AIMLocations_LType DEFAULT N'D' FOR LType
	, CONSTRAINT DF_AIMLocations_LStatus DEFAULT N'A' FOR LStatus
	, CONSTRAINT DF_Locations_LAddress1 DEFAULT ('') FOR LAddress1
	, CONSTRAINT DF_Locations_LAddress2 DEFAULT ('') FOR LAddress2
	, CONSTRAINT DF_Locations_LCity DEFAULT ('') FOR LCity
	, CONSTRAINT DF_Locations_LState DEFAULT ('') FOR LState
	, CONSTRAINT DF_Locations_LZip DEFAULT ('') FOR LZip
	, CONSTRAINT DF_Locations_LContact DEFAULT ('') FOR LContact
	, CONSTRAINT DF_Locations_LPhone DEFAULT ('') FOR LPhone
	, CONSTRAINT DF_Locations_LFax DEFAULT ('') FOR LFax
	, CONSTRAINT DF_Locations_LEMail DEFAULT ('') FOR LEMail
	, CONSTRAINT DF_Locations_PutAwayDays DEFAULT (0) FOR PutAwayDays
	, CONSTRAINT DF_Locations_ReplenCost DEFAULT (5) FOR ReplenCost
	, CONSTRAINT DF_Locations_DemandSource DEFAULT ('S') FOR DemandSource
	, CONSTRAINT DF_Locations_LeadTimeSource DEFAULT ('S') FOR LeadTimeSource
	, CONSTRAINT DF_Locations_StkDate DEFAULT ('1/1/1990') FOR StkDate
	, CONSTRAINT DF_Locations_Dft_Byid DEFAULT ('') FOR Dft_Byid
	, CONSTRAINT DF_Locations_Last_FcstUpdCyc DEFAULT (0) FOR Last_FcstUpdCyc
	, CONSTRAINT DF_Locations_LookBackPds DEFAULT (52) FOR LookBackPds
	, CONSTRAINT DF_AIMLocations_DmdScalingFactor DEFAULT (1) FOR DmdScalingFactor
	, CONSTRAINT DF_AIMLocations_ScalingEffUntil DEFAULT ('1/1/1990') FOR ScalingEffUntil
	, CONSTRAINT DF_AIMLocations_Freeze_Period DEFAULT (0) FOR Freeze_Period
	, CONSTRAINT DF_AIMLocations_UpdateCurrentYearOption DEFAULT (N'N') FOR UpdateCurrentYearOption
	, CONSTRAINT DF_AIMLocations_DropShip_XDock DEFAULT (N'N') FOR DropShip_XDock
	, CONSTRAINT DF_AIMLocations_Dft_ReviewerId DEFAULT N'sa' FOR Dft_ReviewerId
	, CONSTRAINT DF_AIMLocations_ExceptionPct DEFAULT 0 FOR ExceptionPct
GO
IF EXISTS(SELECT * FROM [dbo].[AIMLocations])
	 EXEC('INSERT INTO [dbo].[Tmp_AIMLocations] (Lcid, LName, LAddress1, LAddress2, LCity, LState, LZip, LContact, LPhone, LFax, LEMail, PutAwayDays, ReplenCost, DemandSource, LeadTimeSource, StkDate, Dft_Byid, Last_FcstUpdCyc, LookBackPds, DmdScalingFactor, ScalingEffUntil, Freeze_Period, UpdateCurrentYearOption, DropShip_XDock)
		SELECT Lcid, LName, LAddress1, LAddress2, LCity, LState, LZip, LContact, LPhone, LFax, LEMail, PutAwayDays, ReplenCost, DemandSource, LeadTimeSource, StkDate, Dft_Byid, Last_FcstUpdCyc, LookBackPds, DmdScalingFactor, ScalingEffUntil, Freeze_Period, UpdateCurrentYearOption, DropShip_XDock FROM [dbo].[AIMLocations] TABLOCKX')
GO
DROP TABLE [dbo].[AIMLocations]
GO
EXECUTE sp_rename N'Tmp_AIMLocations', N'AIMLocations', 'OBJECT'
GO
ALTER TABLE [dbo].[AIMLocations] ADD CONSTRAINT
	PK_Locations PRIMARY KEY CLUSTERED 
	(
	Lcid
	) ON [PRIMARY]

GO
GRANT REFERENCES ON [dbo].[AIMLocations] TO public  AS dbo
GRANT SELECT ON [dbo].[AIMLocations] TO public  AS dbo
GRANT UPDATE ON [dbo].[AIMLocations] TO public  AS dbo
GRANT INSERT ON [dbo].[AIMLocations] TO public  AS dbo
GRANT DELETE ON [dbo].[AIMLocations] TO public  AS dbo
GRANT REFERENCES ON [dbo].[AIMLocations] TO Administrators  AS dbo
GRANT SELECT ON [dbo].[AIMLocations] TO Administrators  AS dbo
GRANT UPDATE ON [dbo].[AIMLocations] TO Administrators  AS dbo
GRANT INSERT ON [dbo].[AIMLocations] TO Administrators  AS dbo
GRANT DELETE ON [dbo].[AIMLocations] TO Administrators  AS dbo
-- COMMIT TRANSACTION
