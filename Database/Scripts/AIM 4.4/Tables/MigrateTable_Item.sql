/*

   Friday, May 16, 2003 1:31:22 PM

   User: 

   Server: WRIZALAP

   Database: EXCEED_AIM

   Application: MS SQLEM - Data Tools

*/



BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ActDate
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_InActDate
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_OptionID
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Class1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Class2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Class3
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Class4
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BinLocation
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BuyStrat
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VelCode
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VnId
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Assort
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ById
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_MDC
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_MDCFlag
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_SaId
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_PmId
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UPC
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Weight
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Cube
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ListPrice
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Price
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Cost
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty01
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost01
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty02
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost02
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty021
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost021
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty021_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost021_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty022
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost022
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty023
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost023
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty024
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost024
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty021_2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost021_2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty022_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost022_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkQty023_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkCost023_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UOM
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ConvFactorPL3
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BuyingUOM
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_KFactor2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Oh
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Oo
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ComStk
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkOrder
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_BkComStk
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_LeadTime
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_PackRounding
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_IMin
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_IMax
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_CStock
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_SSAdj
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserMin
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserMax
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserMethod
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstMethod
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstDemand
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstUser
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstUserExpDate
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_MAPE
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_MSPE
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_TrndPct
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstCycles
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ZeroCount
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Mean_NZ
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_StdDev_NZ
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_IntSafetyStock
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_IsIntermittent
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_DIFlag
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_DmdFilterFlag
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_TrkSignalFlag
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserDemandFlag
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ByPassPct
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FctUpdCyc
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_LTVFact
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_PlnTT
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ZOPSw
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_OUTLSw
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ZSStock
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_DSer
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Freeze_BuyStrat
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Freeze_Byid
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Freeze_LeadTime
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Freeze_OptionID
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Freeze_DSer
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_OldItem
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Accum_Lt
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ReviewTime
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_OrderPt
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_OrderQty
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_SafetyStock
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstRT
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstLT
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Fcst_Month
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Fcst_Quarter
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Fcst_Year
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_FcstDate
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VC_Amt
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VC_Units_Ranking
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VC_Amt_Ranking
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VC_Date
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VelCode_Prev
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_VC_Amt_Prev
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_OnPromotion
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_AvgOh
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPONbr_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPODate_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPOQty_1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPONbr_2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPODate_2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPOQty_2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPONbr_3
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPODate_3
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_NextPOQty_3
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserRef1
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserRef2
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_UserRef3
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_Freeze_Forecast
GO
ALTER TABLE dbo.Item
	DROP CONSTRAINT DF_Item_ProductionConstraint
GO
CREATE TABLE dbo.Tmp_Item
	(
	Lcid nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Item nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ItDesc nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ItStat nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ActDate datetime NOT NULL,
	InActDate datetime NOT NULL,
	OptionID nvarchar(6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Class1 nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Class2 nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Class3 nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Class4 nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	BinLocation nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	BuyStrat nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	VelCode nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	VnId nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Assort nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ById nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	MDC nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	MDCFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	SaId nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	PmId nvarchar(6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	UPC nvarchar(22) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Weight decimal(10, 4) NOT NULL,
	Cube decimal(10, 4) NOT NULL,
	ListPrice decimal(10, 4) NOT NULL,
	Price decimal(10, 4) NOT NULL,
	Cost decimal(10, 4) NOT NULL,
	BkQty01 int NOT NULL,
	BkCost01 decimal(10, 4) NOT NULL,
	BkQty02 int NOT NULL,
	BkCost02 decimal(10, 4) NOT NULL,
	BkQty03 int NOT NULL,
	BkCost03 decimal(10, 4) NOT NULL,
	BkQty04 int NOT NULL,
	BkCost04 decimal(10, 4) NOT NULL,
	BkQty05 int NOT NULL,
	BkCost05 decimal(10, 4) NOT NULL,
	BkQty06 int NOT NULL,
	BkCost06 decimal(10, 4) NOT NULL,
	BkQty07 int NOT NULL,
	BkCost07 decimal(10, 4) NOT NULL,
	BkQty08 int NOT NULL,
	BkCost08 decimal(10, 4) NOT NULL,
	BkQty09 int NOT NULL,
	BkCost09 decimal(10, 4) NOT NULL,
	BkQty10 int NOT NULL,
	BkCost10 decimal(10, 4) NOT NULL,
	UOM nvarchar(6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ConvFactor int NOT NULL,
	BuyingUOM nvarchar(6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ReplenCost2 decimal(9, 4) NOT NULL,
	Oh int NOT NULL,
	Oo int NOT NULL,
	ComStk int NOT NULL,
	BkOrder int NOT NULL,
	BkComStk int NOT NULL,
	LeadTime smallint NOT NULL,
	PackRounding nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	IMin int NOT NULL,
	IMax int NOT NULL,
	CStock int NOT NULL,
	SSAdj decimal(9, 4) NOT NULL,
	UserMin int NOT NULL,
	UserMax int NOT NULL,
	UserMethod tinyint NOT NULL,
	FcstMethod tinyint NOT NULL,
	FcstDemand decimal(10, 2) NOT NULL,
	UserFcst decimal(10, 2) NOT NULL,
	UserFcstExpDate datetime NOT NULL,
	MAE decimal(10, 2) NOT NULL,
	MSE decimal(10, 2) NOT NULL,
	Trend decimal(10, 3) NOT NULL,
	FcstCycles int NOT NULL,
	ZeroCount int NOT NULL,
	Mean_NZ decimal(10, 2) NOT NULL,
	StdDev_NZ decimal(10, 2) NOT NULL,
	IntSafetyStock decimal(10, 2) NOT NULL,
	IsIntermittent nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DIFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DmdFilterFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	TrkSignalFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	UserDemandFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ByPassPct decimal(4, 3) NOT NULL,
	FcstUpdCyc int NOT NULL,
	LTVFact decimal(3, 2) NOT NULL,
	PlnTT nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ZOPSw nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	OUTLSw nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ZSStock nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DSer decimal(4, 3) NOT NULL,
	Freeze_BuyStrat nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Freeze_Byid nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Freeze_LeadTime nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Freeze_OptionID nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Freeze_DSer nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	OldItem nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Accum_Lt smallint NOT NULL,
	ReviewTime smallint NOT NULL,
	OrderPt int NOT NULL,
	OrderQty int NOT NULL,
	SafetyStock decimal(10, 2) NOT NULL,
	FcstRT decimal(10, 2) NOT NULL,
	FcstLT decimal(10, 2) NOT NULL,
	Fcst_Month decimal(10, 2) NOT NULL,
	Fcst_Quarter decimal(10, 2) NOT NULL,
	Fcst_Year decimal(10, 2) NOT NULL,
	FcstDate smalldatetime NOT NULL,
	VC_Amt nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	VC_Units_Ranking int NOT NULL,
	VC_Amt_Ranking int NOT NULL,
	VC_Date smalldatetime NOT NULL,
	VelCode_Prev nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	VC_Amt_Prev nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	OnPromotion nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	AvgOh decimal(10, 2) NOT NULL,
	NextPONbr_1 nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	NextPODate_1 datetime NOT NULL,
	NextPOQty_1 int NOT NULL,
	NextPONbr_2 nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	NextPODate_2 datetime NOT NULL,
	NextPOQty_2 int NOT NULL,
	NextPONbr_3 nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	NextPODate_3 datetime NOT NULL,
	NextPOQty_3 int NOT NULL,
	UserRef1 nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	UserRef2 nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	UserRef3 nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Freeze_Forecast nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ProductionConstraint nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	AllocatableQty int NULL,
	AltVnFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT DF_Item_ALtVnFlag DEFAULT N'N'
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ActDate DEFAULT ('1/1/1990') FOR ActDate
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_InActDate DEFAULT ('12/31/9999') FOR InActDate
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_OptionID DEFAULT ('DEFLT') FOR OptionID
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Class1 DEFAULT ('') FOR Class1
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Class2 DEFAULT ('') FOR Class2
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Class3 DEFAULT ('') FOR Class3
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Class4 DEFAULT ('') FOR Class4
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BinLocation DEFAULT ('') FOR BinLocation
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BuyStrat DEFAULT ('O') FOR BuyStrat
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VelCode DEFAULT ('C') FOR VelCode
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VnId DEFAULT ('') FOR VnId
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Assort DEFAULT ('') FOR Assort
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ById DEFAULT ('') FOR ById
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_MDC DEFAULT ('') FOR MDC
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_MDCFlag DEFAULT ('N') FOR MDCFlag
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_SaId DEFAULT ('') FOR SaId
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_PmId DEFAULT ('') FOR PmId
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UPC DEFAULT ('') FOR UPC
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Weight DEFAULT (0) FOR Weight
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Cube DEFAULT (0) FOR Cube
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ListPrice DEFAULT (0) FOR ListPrice
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Price DEFAULT (0) FOR Price
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Cost DEFAULT (0) FOR Cost
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty01 DEFAULT (0) FOR BkQty01
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost01 DEFAULT (0) FOR BkCost01
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty02 DEFAULT (0) FOR BkQty02
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost02 DEFAULT (0) FOR BkCost02
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty021 DEFAULT (0) FOR BkQty03
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost021 DEFAULT (0) FOR BkCost03
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty021_1 DEFAULT (0) FOR BkQty04
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost021_1 DEFAULT (0) FOR BkCost04
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty022 DEFAULT (0) FOR BkQty05
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost022 DEFAULT (0) FOR BkCost05
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty023 DEFAULT (0) FOR BkQty06
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost023 DEFAULT (0) FOR BkCost06
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty024 DEFAULT (0) FOR BkQty07
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost024 DEFAULT (0) FOR BkCost07
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty021_2 DEFAULT (0) FOR BkQty08
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost021_2 DEFAULT (0) FOR BkCost08
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty022_1 DEFAULT (0) FOR BkQty09
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost022_1 DEFAULT (0) FOR BkCost09
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkQty023_1 DEFAULT (0) FOR BkQty10
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkCost023_1 DEFAULT (0) FOR BkCost10
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UOM DEFAULT ('EA') FOR UOM
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ConvFactorPL3 DEFAULT (1) FOR ConvFactor
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BuyingUOM DEFAULT ('EA') FOR BuyingUOM
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_KFactor2 DEFAULT (0) FOR ReplenCost2
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Oh DEFAULT (0) FOR Oh
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Oo DEFAULT (0) FOR Oo
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ComStk DEFAULT (0) FOR ComStk
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkOrder DEFAULT (0) FOR BkOrder
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_BkComStk DEFAULT (0) FOR BkComStk
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_LeadTime DEFAULT (0) FOR LeadTime
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_PackRounding DEFAULT ('R') FOR PackRounding
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_IMin DEFAULT (0) FOR IMin
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_IMax DEFAULT (999999999) FOR IMax
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_CStock DEFAULT (0) FOR CStock
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_SSAdj DEFAULT (0) FOR SSAdj
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserMin DEFAULT (0) FOR UserMin
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserMax DEFAULT (999999999) FOR UserMax
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserMethod DEFAULT (0) FOR UserMethod
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstMethod DEFAULT (0) FOR FcstMethod
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstDemand DEFAULT (0) FOR FcstDemand
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstUser DEFAULT (0) FOR UserFcst
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstUserExpDate DEFAULT ('1/1/1990') FOR UserFcstExpDate
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_MAPE DEFAULT (0) FOR MAE
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_MSPE DEFAULT (0) FOR MSE
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_TrndPct DEFAULT (0) FOR Trend
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstCycles DEFAULT (0) FOR FcstCycles
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ZeroCount DEFAULT (0) FOR ZeroCount
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Mean_NZ DEFAULT (0) FOR Mean_NZ
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_StdDev_NZ DEFAULT (0) FOR StdDev_NZ
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_IntSafetyStock DEFAULT (0) FOR IntSafetyStock
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_IsIntermittent DEFAULT ('N') FOR IsIntermittent
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_DIFlag DEFAULT ('N') FOR DIFlag
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_DmdFilterFlag DEFAULT ('N') FOR DmdFilterFlag
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_TrkSignalFlag DEFAULT ('N') FOR TrkSignalFlag
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserDemandFlag DEFAULT ('N') FOR UserDemandFlag
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ByPassPct DEFAULT (0) FOR ByPassPct
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FctUpdCyc DEFAULT (0) FOR FcstUpdCyc
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_LTVFact DEFAULT (0) FOR LTVFact
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_PlnTT DEFAULT ('N') FOR PlnTT
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ZOPSw DEFAULT ('N') FOR ZOPSw
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_OUTLSw DEFAULT ('N') FOR OUTLSw
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ZSStock DEFAULT ('N') FOR ZSStock
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_DSer DEFAULT (0.95) FOR DSer
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Freeze_BuyStrat DEFAULT ('N') FOR Freeze_BuyStrat
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Freeze_Byid DEFAULT ('N') FOR Freeze_Byid
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Freeze_LeadTime DEFAULT ('N') FOR Freeze_LeadTime
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Freeze_OptionID DEFAULT ('N') FOR Freeze_OptionID
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Freeze_DSer DEFAULT ('N') FOR Freeze_DSer
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_OldItem DEFAULT ('') FOR OldItem
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Accum_Lt DEFAULT (0) FOR Accum_Lt
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ReviewTime DEFAULT (0) FOR ReviewTime
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_OrderPt DEFAULT (0) FOR OrderPt
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_OrderQty DEFAULT (0) FOR OrderQty
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_SafetyStock DEFAULT (0) FOR SafetyStock
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstRT DEFAULT (0) FOR FcstRT
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstLT DEFAULT (0) FOR FcstLT
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Fcst_Month DEFAULT (0) FOR Fcst_Month
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Fcst_Quarter DEFAULT (0) FOR Fcst_Quarter
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Fcst_Year DEFAULT (0) FOR Fcst_Year
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_FcstDate DEFAULT ('1/1/1990') FOR FcstDate
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VC_Amt DEFAULT ('C') FOR VC_Amt
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VC_Units_Ranking DEFAULT (0) FOR VC_Units_Ranking
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VC_Amt_Ranking DEFAULT (0) FOR VC_Amt_Ranking
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VC_Date DEFAULT ('1/1/1990') FOR VC_Date
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VelCode_Prev DEFAULT ('') FOR VelCode_Prev
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_VC_Amt_Prev DEFAULT ('') FOR VC_Amt_Prev
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_OnPromotion DEFAULT ('N') FOR OnPromotion
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_AvgOh DEFAULT (0) FOR AvgOh
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPONbr_1 DEFAULT ('') FOR NextPONbr_1
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPODate_1 DEFAULT ('1/1/1990') FOR NextPODate_1
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPOQty_1 DEFAULT (0) FOR NextPOQty_1
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPONbr_2 DEFAULT ('') FOR NextPONbr_2
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPODate_2 DEFAULT ('1/1/1990') FOR NextPODate_2
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPOQty_2 DEFAULT (0) FOR NextPOQty_2
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPONbr_3 DEFAULT ('') FOR NextPONbr_3
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPODate_3 DEFAULT ('1/1/1990') FOR NextPODate_3
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_NextPOQty_3 DEFAULT (0) FOR NextPOQty_3
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserRef1 DEFAULT ('') FOR UserRef1
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserRef2 DEFAULT ('') FOR UserRef2
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_UserRef3 DEFAULT ('') FOR UserRef3
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_Freeze_Forecast DEFAULT (N'N') FOR Freeze_Forecast
GO
ALTER TABLE dbo.Tmp_Item ADD CONSTRAINT
	DF_Item_ProductionConstraint DEFAULT (N'N') FOR ProductionConstraint
GO
IF EXISTS(SELECT * FROM dbo.Item)
	 EXEC('INSERT INTO dbo.Tmp_Item (Lcid, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, Class3, Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, MDCFlag, SaId, PmId, UPC, Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, ReplenCost2, Oh, Oo, ComStk, BkOrder, BkComStk, LeadTime, PackRounding, IMin, IMax, CStock, SSAdj, UserMin, UserMax, UserMethod, FcstMethod, FcstDemand, UserFcst, UserFcstExpDate, MAE, MSE, Trend, FcstCycles, ZeroCount, Mean_NZ, StdDev_NZ, IntSafetyStock, IsIntermittent, DIFlag, DmdFilterFlag, TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, PlnTT, ZOPSw, OUTLSw, ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, VC_Amt_Ranking, VC_Date, VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, NextPOQty_1, NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, UserRef1, UserRef2, UserRef3, Freeze_Forecast, ProductionConstraint)
		SELECT Lcid, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, Class3, Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, MDCFlag, SaId, PmId, UPC, Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, ReplenCost2, Oh, Oo, ComStk, BkOrder, BkComStk, LeadTime, PackRounding, IMin, IMax, CStock, SSAdj, UserMin, UserMax, UserMethod, FcstMethod, FcstDemand, UserFcst, UserFcstExpDate, MAE, MSE, Trend, FcstCycles, ZeroCount, Mean_NZ, StdDev_NZ, IntSafetyStock, IsIntermittent, DIFlag, DmdFilterFlag, TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, PlnTT, ZOPSw, OUTLSw, ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, VC_Amt_Ranking, VC_Date, VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, NextPOQty_1, NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, UserRef1, UserRef2, UserRef3, Freeze_Forecast, ProductionConstraint FROM dbo.Item TABLOCKX')
GO
DROP TABLE dbo.Item
GO
EXECUTE sp_rename N'dbo.Tmp_Item', N'Item', 'OBJECT'
GO
ALTER TABLE dbo.Item ADD CONSTRAINT
	PK_Item PRIMARY KEY NONCLUSTERED 
	(
	Lcid,
	Item
	) ON [PRIMARY]

GO
CREATE NONCLUSTERED INDEX IX_Byid ON dbo.Item
	(
	ById
	) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX IX_Item ON dbo.Item
	(
	Item
	) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX IX_ItStat ON dbo.Item
	(
	ItStat
	) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX IX_VnidAssort ON dbo.Item
	(
	VnId,
	Assort
	) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX IX_MDC ON dbo.Item
	(
	MDC
	) ON [PRIMARY]
GO
COMMIT
