/*

   Thursday, May 22, 2003 1:57:43 PM

   User: sa

   Server: (LOCAL)

   Database: EXCEED_AIM

   Application: MS SQLEM - Data Tools

*/



BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_KFactor
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_MinROAI
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_ByIdSource
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_SeasSmoothingPds
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_CurSaVersion
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Last_POSeq
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_ClassOption
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_AvgInvAlpha
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_UpdateGoalsOption
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_UpdateVnLT
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_VnLTPctChgFilter
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_VnLTDaysChgFilter
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_MDCOption
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_LT_Alpha
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_LT_CtrlInterval_Pct
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_Enabled
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_MinPctZero
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_MaxPctZero
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_SeasonalityIndex
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_SrvLvlOverrideFlag
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_SrvLvl
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_Int_LookBackPds
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_AIMBatchPath
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_dft_LangID
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_ProductionConstraint
GO
ALTER TABLE dbo.SysCtrl
	DROP CONSTRAINT DF_SysCtrl_ProdConstraintGrtZero
GO
CREATE TABLE dbo.Tmp_SysCtrl
	(
	CName nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Address1 nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Address2 nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Address3 nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	City nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	State nvarchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CoZip nvarchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Dft_MinRchPct numeric(3, 1) NOT NULL,
	Dft_BestRchPct numeric(3, 1) NOT NULL,
	DxPath nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DxPath_Out nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DxPath_Backup nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Dft_POStatus nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DxPO_Option nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	KFactor decimal(5, 2) NOT NULL,
	MinROAI decimal(5, 2) NOT NULL,
	Vendor_Sizing nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Priority_Min_Dser numeric(3, 2) NOT NULL,
	Priority_Min_VelCode nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Addl_LTDays tinyint NOT NULL,
	Lt_Filter_Pct numeric(3, 2) NOT NULL,
	Retain_SD_Opt tinyint NOT NULL,
	Calc_Perform tinyint NOT NULL,
	LogFile nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ByIdSource nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	SeasSmoothingPds tinyint NOT NULL,
	CurSaVersion smallint NOT NULL,
	Last_POSeq int NOT NULL,
	ClassOption tinyint NOT NULL,
	AvgInvAlpha decimal(3, 2) NOT NULL,
	UpdateGoalsOption nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	UpdateVnLT nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	VnLTPctChgFilter decimal(5, 2) NOT NULL,
	VnLTDaysChgFilter smallint NOT NULL,
	MDCOption nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	LT_Alpha decimal(5, 3) NOT NULL,
	LT_CtrlInterval_Pct decimal(5, 3) NOT NULL,
	Int_Enabled nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Int_MinPctZero decimal(5, 3) NOT NULL,
	Int_MaxPctZero decimal(5, 3) NOT NULL,
	Int_SeasonalityIndex decimal(5, 3) NOT NULL,
	Int_SrvLvlOverrideFlag nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	Int_SrvLvl decimal(5, 3) NOT NULL,
	Int_LookBackPds int NOT NULL,
	AIMBatchPath nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	dft_LangID nvarchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ColonOption nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	GridAutoSizeOption nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DateFormat nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	TimeFormat nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	UnicodeOption nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ProductionConstraint nvarchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ProdConstraintGrtZero nvarchar(1) NULL,
	SessionID bigint NULL,
	AllocNeedDetermination tinyint,
	AllocExceptionProcess tinyint
)  ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_KFactor DEFAULT (30) FOR KFactor
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_MinROAI DEFAULT (20) FOR MinROAI
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_ByIdSource DEFAULT ('V') FOR ByIdSource
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_SeasSmoothingPds DEFAULT (5) FOR SeasSmoothingPds
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_CurSaVersion DEFAULT (0) FOR CurSaVersion
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Last_POSeq DEFAULT (0) FOR Last_POSeq
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_ClassOption DEFAULT (1) FOR ClassOption
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_AvgInvAlpha DEFAULT (0.05) FOR AvgInvAlpha
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_UpdateGoalsOption DEFAULT ('Y') FOR UpdateGoalsOption
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_UpdateVnLT DEFAULT ('Y') FOR UpdateVnLT
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_VnLTPctChgFilter DEFAULT (0.5) FOR VnLTPctChgFilter
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_VnLTDaysChgFilter DEFAULT (1) FOR VnLTDaysChgFilter
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_MDCOption DEFAULT ('N') FOR MDCOption
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_LT_Alpha DEFAULT (0.1) FOR LT_Alpha
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_LT_CtrlInterval_Pct DEFAULT (3) FOR LT_CtrlInterval_Pct
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_Enabled DEFAULT ('N') FOR Int_Enabled
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_MinPctZero DEFAULT (0.3) FOR Int_MinPctZero
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_MaxPctZero DEFAULT (0.9) FOR Int_MaxPctZero
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_SeasonalityIndex DEFAULT (1.000) FOR Int_SeasonalityIndex
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_SrvLvlOverrideFlag DEFAULT ('Y') FOR Int_SrvLvlOverrideFlag
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_SrvLvl DEFAULT (0.85) FOR Int_SrvLvl
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_Int_LookBackPds DEFAULT (52) FOR Int_LookBackPds
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_AIMBatchPath DEFAULT ('') FOR AIMBatchPath
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_dft_LangID DEFAULT ('EN-US') FOR dft_LangID
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_ProductionConstraint DEFAULT (N'Y') FOR ProductionConstraint
GO
ALTER TABLE dbo.Tmp_SysCtrl ADD CONSTRAINT
	DF_SysCtrl_ProdConstraintGrtZero DEFAULT (N'Y') FOR ProdConstraintGrtZero
GO
IF EXISTS(SELECT * FROM dbo.SysCtrl)
	 EXEC('INSERT INTO dbo.Tmp_SysCtrl (CName, Address1, Address2, Address3, City, State, CoZip, Dft_MinRchPct, Dft_BestRchPct, DxPath, DxPath_Out, DxPath_Backup, Dft_POStatus, DxPO_Option, KFactor, MinROAI, Vendor_Sizing, Priority_Min_Dser, Priority_Min_VelCode, Addl_LTDays, Lt_Filter_Pct, Retain_SD_Opt, Calc_Perform, LogFile, ByIdSource, SeasSmoothingPds, CurSaVersion, Last_POSeq, ClassOption, AvgInvAlpha, UpdateGoalsOption, UpdateVnLT, VnLTPctChgFilter, VnLTDaysChgFilter, MDCOption, LT_Alpha, LT_CtrlInterval_Pct, Int_Enabled, Int_MinPctZero, Int_MaxPctZero, Int_SeasonalityIndex, Int_SrvLvlOverrideFlag, Int_SrvLvl, Int_LookBackPds, AIMBatchPath, dft_LangID, ColonOption, GridAutoSizeOption, DateFormat, TimeFormat, UnicodeOption, ProductionConstraint, ProdConstraintGrtZero)
		SELECT CName, Address1, Address2, Address3, City, State, CoZip, Dft_MinRchPct, Dft_BestRchPct, DxPath, DxPath_Out, DxPath_Backup, Dft_POStatus, DxPO_Option, KFactor, MinROAI, Vendor_Sizing, Priority_Min_Dser, Priority_Min_VelCode, Addl_LTDays, Lt_Filter_Pct, Retain_SD_Opt, Calc_Perform, LogFile, ByIdSource, SeasSmoothingPds, CurSaVersion, Last_POSeq, ClassOption, AvgInvAlpha, UpdateGoalsOption, UpdateVnLT, VnLTPctChgFilter, VnLTDaysChgFilter, MDCOption, LT_Alpha, LT_CtrlInterval_Pct, Int_Enabled, Int_MinPctZero, Int_MaxPctZero, Int_SeasonalityIndex, Int_SrvLvlOverrideFlag, Int_SrvLvl, Int_LookBackPds, AIMBatchPath, dft_LangID, ColonOption, GridAutoSizeOption, DateFormat, TimeFormat, UnicodeOption, ProductionConstraint, ProdConstraintGrtZero FROM dbo.SysCtrl TABLOCKX')
GO
DROP TABLE dbo.SysCtrl
GO
EXECUTE sp_rename N'dbo.Tmp_SysCtrl', N'SysCtrl', 'OBJECT'
GO
COMMIT
