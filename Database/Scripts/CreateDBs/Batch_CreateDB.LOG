----- START -- CreateDB SSA_DR_DEFAULT Script ----- 
Wed 11/30/2005 
 2:55p
----- 1/4. Merge *.TABs ----- 
..\..\Tables\dbo.AIMVendors.TAB
..\..\Tables\dbo.AIMLocations.TAB
..\..\Tables\dbo.Item.TAB
        1 file(s) copied.
..\..\Tables\dbo.AIMCompanionItem.TAB
..\..\Tables\dbo.AIMCompanionItemDetail.TAB
..\..\Tables\dbo.AllocItemSubstitutes.TAB
..\..\Tables\dbo.AllocScratch_ItemDemand.TAB
..\..\Tables\dbo.ItemHistory.TAB
..\..\Tables\dbo.ItemHistoryAdjLog.TAB
..\..\Tables\dbo.ItemKitBOM.TAB
..\..\Tables\dbo.ItemPricing.TAB
        1 file(s) copied.
..\..\Tables\A1Dependencies.TAB
..\..\Tables\A2Dependencies.TAB
..\..\Tables\dbo.AIMAO.TAB
..\..\Tables\dbo.AIMAODetail.TAB
..\..\Tables\dbo.AIMAO_OrderInfo.TAB
..\..\Tables\dbo.AIMClasses.TAB
..\..\Tables\dbo.AIMCodeLookUp.TAB
..\..\Tables\dbo.AIMDataExchangeCtrl.TAB
..\..\Tables\dbo.AIMDays.TAB
..\..\Tables\dbo.AIMDD.TAB
..\..\Tables\dbo.AIMDestinationProfile.TAB
..\..\Tables\dbo.AIMDxAS.TAB
..\..\Tables\dbo.AIMDxDD.TAB
..\..\Tables\dbo.AIMDxFA.TAB
..\..\Tables\dbo.AIMDxHS.TAB
..\..\Tables\dbo.AIMDxIS.TAB
..\..\Tables\dbo.AIMDxKB.TAB
..\..\Tables\dbo.AIMDxLC.TAB
..\..\Tables\dbo.AIMDxLT.TAB
..\..\Tables\dbo.AIMDxRO.TAB
..\..\Tables\dbo.AIMDxRP.TAB
..\..\Tables\dbo.AIMDxRS.TAB
..\..\Tables\dbo.AIMDxSd.TAB
..\..\Tables\dbo.AIMDxSL.TAB
..\..\Tables\dbo.AIMDxSS.TAB
..\..\Tables\dbo.AIMDxVn.TAB
..\..\Tables\dbo.AIMERROR_INFO.TAB
..\..\Tables\dbo.AIMFcstAccess.TAB
..\..\Tables\dbo.AIMFcstMaster.TAB
..\..\Tables\dbo.AIMFcstSetup.TAB
..\..\Tables\dbo.AIMFunctions.TAB
..\..\Tables\dbo.AIMHELP.TAB
..\..\Tables\dbo.AIMLanguage.TAB
..\..\Tables\dbo.AIMLeadTime.TAB
..\..\Tables\dbo.AIMLiterals.TAB
..\..\Tables\dbo.AIMMethods.TAB
..\..\Tables\dbo.AIMOptions.TAB
..\..\Tables\dbo.AIMPo.TAB
..\..\Tables\dbo.AIMProductionConstraint.TAB
..\..\Tables\dbo.AIMProductionConstraintDetail.TAB
..\..\Tables\dbo.AIMPromotions.TAB
..\..\Tables\dbo.AIMRoles.TAB
..\..\Tables\dbo.AIMSd.TAB
..\..\Tables\dbo.AIMSeasons.TAB
..\..\Tables\dbo.AIMSourcingHierarchy.TAB
..\..\Tables\dbo.AIMTables.TAB
..\..\Tables\dbo.AIMTransferPolicy.TAB
..\..\Tables\dbo.AIMTranslation.TAB
..\..\Tables\dbo.AIMUOM.TAB
..\..\Tables\dbo.AIMUsers.TAB
..\..\Tables\dbo.AIMVelCodePcts.TAB
..\..\Tables\dbo.AIMWorkingDays.TAB
..\..\Tables\dbo.AIMYears.TAB
..\..\Tables\dbo.AllocDefaults.TAB
..\..\Tables\dbo.AllocDefaultsSource.TAB
..\..\Tables\dbo.AllocScratch_AllocResults.TAB
..\..\Tables\dbo.AllocScratch_Req_Pri.TAB
..\..\Tables\dbo.AllocScratch_Req_Qty.TAB
..\..\Tables\dbo.AllocScratch_Req_Ratio.TAB
..\..\Tables\dbo.AllocScratch_SourceInventory.TAB
..\..\Tables\dbo.Alternate_Source.TAB
..\..\Tables\dbo.BuyerStatusTest.TAB
..\..\Tables\dbo.DXElements.TAB
..\..\Tables\dbo.DXSets.TAB
..\..\Tables\dbo.ForecastFilterCriteria.TAB
..\..\Tables\dbo.ForecastRepository.TAB
..\..\Tables\dbo.ForecastRepositoryArch_Log.TAB
..\..\Tables\dbo.ForecastRepositoryDetail.TAB
..\..\Tables\dbo.ForecastRepository_Log.TAB
..\..\Tables\dbo.ItDefaults.TAB
..\..\Tables\dbo.ItStatus.TAB
..\..\Tables\dbo.KitBOMSummary.TAB
..\..\Tables\dbo.MDCDetail.TAB
..\..\Tables\dbo.MDCSummary.TAB
..\..\Tables\dbo.PODetail.TAB
..\..\Tables\dbo.POStatistics.TAB
..\..\Tables\dbo.ProdConstraintTemp.TAB
..\..\Tables\dbo.RevCycles.TAB
..\..\Tables\dbo.SSA_DR_Migration.TAB
..\..\Tables\dbo.SSA_DR_Migration_Log.TAB
..\..\Tables\dbo.SysCtrl.TAB
..\..\Tables\dbo.UserElement.TAB
..\..\Tables\dbo.UserElementDetail.TAB
        1 file(s) copied.
------ END -- MergeTAB.bat ------ 
Wed 11/30/2005 
 2:55p
----- 2/4. Merge *sp*.SQL ----- 
..\..\Stored Procedures\AIM_VerifyDxFile_Sp.SQL
..\..\Stored Procedures\AIM_GetSaVersion_Sp.SQL
..\..\Stored Procedures\AIM_GetPd_Sp.SQL
..\..\Stored Procedures\AIM_GetDate_Sp.SQL
..\..\Stored Procedures\AIM_GetLastWeekSales_Sp.SQL
..\..\Stored Procedures\AIM_POInsert_Sp.SQL
..\..\Stored Procedures\AIM_POHdrInsert_Sp.SQL
..\..\Stored Procedures\AIM_PackRounding_Sp.SQL
..\..\Stored Procedures\AIM_GetAccum_LT_Sp.SQL
..\..\Stored Procedures\AIM_OrdGen_Sp.SQL
..\..\Stored Procedures\AIM_ItmChg_Sp.SQL
..\..\Stored Procedures\AIM_UOMChg_Sp.SQL
..\..\Stored Procedures\AIM_ForecastRepository_GetKey_Sp.SQL
..\..\Stored Procedures\AIM_ForecastRepository_Save_Sp.SQL
..\..\Stored Procedures\AIM_Update_RevCycle_Date_Sp.SQL
..\..\Stored Procedures\AIM_VendorSizing_Sp.SQL
..\..\Stored Procedures\AIM_VendorSizingCtrl_Sp.SQL
..\..\Stored Procedures\AIM_ProdPackRounding_Sp.SQL
..\..\Stored Procedures\AIM_ProductionConstraint_Sp.SQL
..\..\Stored Procedures\AIM_VelCode_Sp.SQL
..\..\Stored Procedures\AIM_Alloc_Pass1_Sp.SQL
..\..\Stored Procedures\AIM_Alloc_Pass2_Sp.SQL
..\..\Stored Procedures\AIM_GetSessionID_Sp.SQL
..\..\Stored Procedures\AIM_Alloc_ScratchInserts_Sp.SQL
..\..\Stored Procedures\AIM_AllocateInventory_Sp.SQL
..\..\Stored Procedures\AIM_UpdateAIMAOOrderStatus_Sp.SQL
..\..\Stored Procedures\AIM_Alloc_UpdateAggregates_Sp.SQL
..\..\Stored Procedures\AIM_Alloc_HoldExceptions_Sp.SQL
..\..\Stored Procedures\AIM_Alloc_ReleaseOrders_Sp.SQL
        1 file(s) copied.
..\..\Stored Procedures\A1Dependencies.SQL
..\..\Stored Procedures\AIMMessageWriter.sql
..\..\Stored Procedures\AIM_AIMCalendar_BoundaryDates_Sp.sql
..\..\Stored Procedures\AIM_AIMDays_GetFiscalPeriod_Sp.sql
..\..\Stored Procedures\AIM_AIMDays_Init_Sp.sql
..\..\Stored Procedures\AIM_AIMDays_Load_Sp.sql
..\..\Stored Procedures\AIM_AIMDays_Upd_Sp.sql
..\..\Stored Procedures\AIM_AIMLanguage_GetValue_Sp.sql
..\..\Stored Procedures\AIM_AIMLanguage_List_Sp.sql
..\..\Stored Procedures\AIM_AIMLocations_GetEq_Sp.sql
..\..\Stored Procedures\AIM_AIMLocations_List_Sp.sql
..\..\Stored Procedures\AIM_AIMOptions_GetEq_Sp.sql
..\..\Stored Procedures\AIM_AIMOptions_GetKey_Sp.sql
..\..\Stored Procedures\AIM_AIMProductionConstraintCtrl_Sp.sql
..\..\Stored Procedures\AIM_AIMProductionConstraint_Save_Sp.sql
..\..\Stored Procedures\AIM_AIMProductionConstraint_Sp.sql
..\..\Stored Procedures\AIM_AIMPromotions_GetKey_Sp.sql
..\..\Stored Procedures\AIM_AIMRoles_List_Sp.sql
..\..\Stored Procedures\AIM_AIMRoles_List_Sp_ALL.sql
..\..\Stored Procedures\AIM_AIMSeasons_GetEq_Sp.sql
..\..\Stored Procedures\AIM_AimSeasons_GetKey_Sp.sql
..\..\Stored Procedures\AIM_AIMTranslation_List_Sp.sql
..\..\Stored Procedures\AIM_AIMUser_List_Sp.sql
..\..\Stored Procedures\AIM_AIMVendors_GetEq_Sp.sql
..\..\Stored Procedures\AIM_AimVendors_GetKey_Sp.sql
..\..\Stored Procedures\AIM_AIMYears_Insert_Sp.sql
..\..\Stored Procedures\AIM_AIMYears_Load_Sp.sql
..\..\Stored Procedures\AIM_AllocationCtrl_Sp.sql
..\..\Stored Procedures\AIM_AllocationDetail_Sp.sql
..\..\Stored Procedures\AIM_AllocationEdit_Sp.sql
..\..\Stored Procedures\AIM_AllocationResetItem_Sp.sql
..\..\Stored Procedures\AIM_AllocationResetOrder_Sp.sql
..\..\Stored Procedures\AIM_AllocationReviewSave_Sp.sql
..\..\Stored Procedures\AIM_AllocationSourceQty_Sp.sql
..\..\Stored Procedures\AIM_AllocationSummary_Sp.sql
..\..\Stored Procedures\AIM_AllocDefaultsSource_Get_Sp.sql
..\..\Stored Procedures\AIM_AllocDefaultsSource_Validation_Sp.sql
..\..\Stored Procedures\AIM_AllocDefaults_Delete_Sp.sql
..\..\Stored Procedures\AIM_AllocDefaults_Get_Sp.sql
..\..\Stored Procedures\AIM_AllocDefaults_Save_Sp.sql
..\..\Stored Procedures\AIM_AllocDefaults_Validation_Sp.sql
..\..\Stored Procedures\AIM_Alloc_DeleteCompleted_Sp.SQL
..\..\Stored Procedures\AIM_AssignSeasonsDefaults_Sp.sql
..\..\Stored Procedures\AIM_BatchFcstDetail_Save_Sp.sql
..\..\Stored Procedures\AIM_BldItDefaults_Sp.sql
..\..\Stored Procedures\AIM_BuyerStatus_Sp.sql
..\..\Stored Procedures\AIM_BuyerSummary_Sp.sql
..\..\Stored Procedures\AIM_Calendar_Init_Sp.sql
..\..\Stored Procedures\AIM_ClassByClassLevel_List_Sp.sql
..\..\Stored Procedures\AIM_Class_List_Sp.sql
..\..\Stored Procedures\AIM_ClearPO_Sp.sql
..\..\Stored Procedures\AIM_CodeLookup_Get_Sp.sql
..\..\Stored Procedures\AIM_CompanionItemDetail_Sp.sql
..\..\Stored Procedures\AIM_CompanionItem_GetKey_Sp.sql
..\..\Stored Procedures\AIM_CostPriceListPrice_Get_SP.sql
..\..\Stored Procedures\AIM_CostPriceListPrice_Save_Sp.sql
..\..\Stored Procedures\AIM_DemandUpdateForCompItems_Sp.sql
..\..\Stored Procedures\AIM_DemandUpdateForKitBOMItems_Sp.sql
..\..\Stored Procedures\AIM_DemandUpdate_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstAccess_Delete_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstAccess_Fetch_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstAccess_Update_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstID_Load_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstSetup_Delete_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstSetup_Save_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstStoreAdjustLog_Save_Sp.sql
..\..\Stored Procedures\AIM_DmdPlanFcstUser_CheckAccess_Sp.sql
..\..\Stored Procedures\AIM_DmdPlan_Adjustments_Fetch_Sp.sql
..\..\Stored Procedures\AIM_DUGet_Ordered_Sp.sql
..\..\Stored Procedures\AIM_DUGet_ShippedWithSubs_Sp.sql
..\..\Stored Procedures\AIM_DUGet_Shipped_Sp.sql
..\..\Stored Procedures\AIM_DUGet_Sp.sql
..\..\Stored Procedures\AIM_DxAO_Sp.sql
..\..\Stored Procedures\AIM_DxAS_Sp.Sql
..\..\Stored Procedures\AIM_DxBulkInsert_Sp.sql
..\..\Stored Procedures\AIM_DxDd_Sp.sql
..\..\Stored Procedures\AIM_DxFA_Sp.sql
..\..\Stored Procedures\AIM_DxHs_Sp.sql
..\..\Stored Procedures\AIM_DxIS_Sp.sql
..\..\Stored Procedures\AIM_DxKB_Sp.sql
..\..\Stored Procedures\AIM_DxLC_Sp.sql
..\..\Stored Procedures\AIM_DxLT_Sp.sql
..\..\Stored Procedures\AIM_DxPO_Batch_Sp.sql
..\..\Stored Procedures\AIM_DxPO_Sp.sql
..\..\Stored Procedures\AIM_DxRO_Sp.sql
..\..\Stored Procedures\AIM_DxRP_Sp.sql
..\..\Stored Procedures\AIM_DxRS_Sp.sql
..\..\Stored Procedures\AIM_DxSd_Sp.sql
..\..\Stored Procedures\AIM_DxSL_Sp.sql
..\..\Stored Procedures\AIM_DxSS_Sp.sql
..\..\Stored Procedures\AIM_DxVn_Sp.sql
..\..\Stored Procedures\AIM_FilterCriteria_Clear_Sp.sql
..\..\Stored Procedures\AIM_FilterCriteria_Save_Sp.sql
..\..\Stored Procedures\AIM_FilterCriteria_Select_Sp.sql
..\..\Stored Procedures\AIM_ForecastMaster_Get_SP.sql
..\..\Stored Procedures\AIM_ForecastPlannerReport_Sp.sql
..\..\Stored Procedures\AIM_ForecastSetup_Fetch_Sp.sql
..\..\Stored Procedures\AIM_FySales_Get_Sp.sql
..\..\Stored Procedures\AIM_GetCompItemsForALoc_Sp.sql
..\..\Stored Procedures\AIM_GetCompItemsLTRT_Sp.sql
..\..\Stored Procedures\AIM_GetDemand_Sp.sql
..\..\Stored Procedures\AIM_GetFcstAdj_SP.sql
..\..\Stored Procedures\AIM_GetHelp_Sp.sql
..\..\Stored Procedures\AIM_GetItemsForACompItem_Sp.sql
..\..\Stored Procedures\AIM_GetItemsForAKitBOMItem_Sp.sql
..\..\Stored Procedures\AIM_GetKitBOMItemsForALoc_Sp.sql
..\..\Stored Procedures\AIM_GetKitBOMItemsLTRT_Sp.sql
..\..\Stored Procedures\AIM_GetSeqNbr_Sp.sql
..\..\Stored Procedures\AIM_GetSubRole_Sp.sql
..\..\Stored Procedures\AIM_GUID_Get_Sp.sql
..\..\Stored Procedures\AIM_Identity_Get.sql
..\..\Stored Procedures\AIM_InitializeNewLocation_Sp.sql
..\..\Stored Procedures\AIM_InitNewItem_Sp.sql
..\..\Stored Procedures\AIM_InitPOHeader_Sp.sql
..\..\Stored Procedures\AIM_InsertAlternatePOType_Sp.Sql
..\..\Stored Procedures\AIM_ItemHistoryAdjLog_Insert_Sp.sql
..\..\Stored Procedures\AIM_ItemHistoryAdjLog_Select_Sp.sql
..\..\Stored Procedures\AIM_ItemHistory_Copy_Sp.sql
..\..\Stored Procedures\AIM_ItemHistory_GetEq_Sp.sql
..\..\Stored Procedures\AIM_ItemHistory_GetSubsItems_Sp.sql
..\..\Stored Procedures\AIM_ItemHsDelete_sp.sql
..\..\Stored Procedures\AIM_ItemPerformance_Sp.sql
..\..\Stored Procedures\AIM_ItemPosition_Sp.sql
..\..\Stored Procedures\AIM_ItemPosition_Upd_Sp.sql
..\..\Stored Procedures\AIM_ItemSourcing_Upd_Sp.Sql
..\..\Stored Procedures\AIM_ItemStatus_List_Sp.sql
..\..\Stored Procedures\AIM_ItemSubPerformance_Sp.sql
..\..\Stored Procedures\AIM_Item_GetEq_Sp.sql
..\..\Stored Procedures\AIM_Item_GetKey_Sp.sql
..\..\Stored Procedures\AIM_KitBOMSummary_Sp.sql
..\..\Stored Procedures\AIM_KitBOMSummary_Upd_Sp.sql
..\..\Stored Procedures\AIM_LDivision_List_Sp.sql
..\..\Stored Procedures\AIM_Locations_List_Sp.sql
..\..\Stored Procedures\AIM_LRegion_List_Sp.sql
..\..\Stored Procedures\AIM_LUserDefined_List_Sp.sql
..\..\Stored Procedures\AIM_MDCDetail_Sp.sql
..\..\Stored Procedures\AIM_MDCSummary_Sp.sql
..\..\Stored Procedures\AIM_MDCSummary_Upd_Sp.sql
..\..\Stored Procedures\AIM_Methods_Load_Sp.sql
..\..\Stored Procedures\AIM_NextPONbr_Sp.sql
..\..\Stored Procedures\AIM_OrdGenCtrl_Sp.sql
..\..\Stored Procedures\AIM_POClose_Sp.sql
..\..\Stored Procedures\AIM_POStatistics_Sp.sql
..\..\Stored Procedures\AIM_ProdConstraintTemp_Get_Sp.sql
..\..\Stored Procedures\AIM_ProdConstraintTemp_Save_Sp.sql
..\..\Stored Procedures\AIM_ProductionConstraintCtrl_Sp.sql
..\..\Stored Procedures\AIM_ProductionConstraintDetail_Get_Sp.sql
..\..\Stored Procedures\AIM_ProductionConstraintDetail_Save_Sp.sql
..\..\Stored Procedures\AIM_ProductionConstraint_Delete_Sp.sql
..\..\Stored Procedures\AIM_ProductionConstraint_GetEq_Sp.sql
..\..\Stored Procedures\AIM_ProductionConstraint_GetKey_Sp.sql
..\..\Stored Procedures\AIM_Purge_AIMError_Info_Sp.sql
..\..\Stored Procedures\AIM_ReleasedPurchaseOrders_Sp.sql
..\..\Stored Procedures\AIM_RepositoryDetail_CheckIn_Sp.sql
..\..\Stored Procedures\AIM_RepositoryDetail_Save_Sp.sql
..\..\Stored Procedures\AIM_RepositoryDetail_Validate_Sp.sql
..\..\Stored Procedures\AIM_RepositoryItemAdj_Get_Sp.sql
..\..\Stored Procedures\AIM_RepositoryKey_Get_Sp.sql
..\..\Stored Procedures\AIM_RepositoryOverRide_Get_Sp.sql
..\..\Stored Procedures\AIM_Repository_Get_Sp.sql
..\..\Stored Procedures\AIM_Repository_RefreshAdj_Sp.sql
..\..\Stored Procedures\AIM_RevCycleRollBack_Sp.sql
..\..\Stored Procedures\AIM_RevCycles_GetEq_Sp.sql
..\..\Stored Procedures\AIM_RevCycles_GetKey_Sp.sql
..\..\Stored Procedures\AIM_RevCycle_List_Sp.sql
..\..\Stored Procedures\AIM_ReviewCycleAssort_Sp .sql
..\..\Stored Procedures\AIM_ReviewCycleById_Sp.sql
..\..\Stored Procedures\AIM_ReviewCycle_Sp.sql
..\..\Stored Procedures\AIM_ReviewVendor_Sp.sql
..\..\Stored Procedures\AIM_SASummary_Sp.sql
..\..\Stored Procedures\AIM_SdCopy_Sp.sql
..\..\Stored Procedures\AIM_SetLowerVendorCostAlert_Sp.sql
..\..\Stored Procedures\AIM_SetSafetyStockAdj_Sp.sql
..\..\Stored Procedures\AIM_Special_Sp.sql
..\..\Stored Procedures\AIM_SumMethods_Sp.sql
..\..\Stored Procedures\AIM_SysCtrl_Get_Sp.sql
..\..\Stored Procedures\AIM_TestLocationId_Sp.sql
..\..\Stored Procedures\AIM_TransferManagement_Sp.sql
..\..\Stored Procedures\AIM_TransferToReport_Sp.sql
..\..\Stored Procedures\AIM_Transfer_Sp.sql
..\..\Stored Procedures\AIM_UnOrderedItems_Sp.sql
..\..\Stored Procedures\AIM_UnorderedVendors_Sp.sql
..\..\Stored Procedures\AIM_UpdateOrdStatus_Sp.sql
..\..\Stored Procedures\AIM_UpdateOverrides_Sp.sql
..\..\Stored Procedures\AIM_UserElementDetail_Get_Sp.sql
..\..\Stored Procedures\AIM_UserElementDetail_Save_Sp.sql
..\..\Stored Procedures\AIM_UserElements_Get_Sp.sql
..\..\Stored Procedures\AIM_ValidateTransfer_Sp.sql
..\..\Stored Procedures\AIM_VelCodeCtrl_Sp.sql
..\..\Stored Procedures\AIM_VendorAssort_List_Sp.sql
..\..\Stored Procedures\AIM_VendorDetail_Sp.sql
..\..\Stored Procedures\AIM_VendorSummary_Sp.sql
..\..\Stored Procedures\AIM_Vendors_List_Sp.sql
..\..\Stored Procedures\AIM_VerifyAccessRole_Sp.sql
..\..\Stored Procedures\AIM_VnAssort_List_Sp.sql
..\..\Stored Procedures\AIM_WorkingDays_Get_Sp.sql
..\..\Stored Procedures\AIM_WorkingDays_Update_Sp.sql
..\..\Stored Procedures\DPAIM_DmdPlan_BatchUserElement_Get_Sp.sql
        1 file(s) copied.
------ END -- MergeSPs.bat ------ 
Wed 11/30/2005 
 2:55p
----- 2/4. Merge tr*.SQL ----- 
..\..\Triggers\trAlternateSourceAdd.sql
..\..\Triggers\trAlternateSourceDel.sql
        1 file(s) copied.
------ END -- MergeTRG.bat ------ 
Wed 11/30/2005 
 2:55p
----- 4/4. Start scripts to create the database ----- 
	4.a. Using MASTER, create database SSA_DR_DEFAULT. 
Dropping database "SSA_DR_DEFAULT".
Deleting database file 'D:\SSA_DR_SS\DBFiles\SSA_DR_DEFAULT.ldf'.
Deleting database file 'D:\SSA_DR_SS\DBFiles\SSA_DR_DEFAULT.mdf'.
Creating database "SSA_DR_DEFAULT".
The CREATE DATABASE process is allocating 7.00 MB on disk 'SSA_DR_DEFAULT'.
The CREATE DATABASE process is allocating 25.00 MB on disk
'SSA_DR_DEFAULT_Log'.
Setting *dboptions...
Database creation complete for database: "SSA_DR_DEFAULT".
---------- Begin ISQL results --------- 
---------- End ISQL results ---------- 
	4.b. Using SSA_DR_DEFAULT, create tables. 
---------- Begin ISQL results --------- 
Warning: The table 'ForecastRepository' has been created but its maximum row size (8210) exceeds the maximum number of bytes per row (8060). INSERT or UPDATE of a row in this table will fail if the resulting row length exceeds 8060 bytes.
---------- End ISQL results ---------- 
	4.c. Using SSA_DR_DEFAULT, create stored procedures. 
---------- Begin ISQL results --------- 
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_SetLowerVendorCostAlert_Sp'. The stored procedure will still be created.
Server: Msg 156, Level 15, State 1, Procedure AIM_ReviewCycleAssort_Sp, Line 42
Incorrect syntax near the keyword 'WITH'.
---------- End ISQL results ---------- 
	4.d. Using SSA_DR_DEFAULT, create triggers. 
---------- Begin ISQL results --------- 
The command(s) completed successfully.
---------- End ISQL results ---------- 
	4.e. Populate SSA_DR_DEFAULT with default data. 
Default data inserted into DB. 
---------- Begin ISQL results --------- 
1 - SysCtrl
Rows inserted into SysCtrl: 1
2 - AIMUsers
Rows inserted into AIMUsers: 5
3 - AIMRoles
Rows inserted into AIMRoles: 3
4 - AIMTables
Rows inserted into AIMTables: 84
5 - AIMFunctions
Rows inserted into AIMFunctions: 60
6 - AIMMethods
Rows inserted into AIMMethods: 25
7 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 1
8 - ItStatus
Rows inserted into ItStatus: 11
9 - AIMUOM
Rows inserted into AIMUOM: 20
10 - AIMVelCodePcts
Rows inserted into AIMVelCodePcts: 13
11 - AIMDD
Rows inserted into AIMDD: 763
12 - DxElements
Rows inserted into DxElements: 509
13 - DxSets
Rows inserted into DxSets: 20
14 - AIMLanguage
Rows inserted into AIMLanguage: 133
15 - ItDefaults
Rows inserted into ItDefaults: 1
16 - AIMOptions
Rows inserted into AIMOptions: 1
17 - RevCycles
Rows inserted into RevCycles: 15
18 - AIMSeasons
Rows inserted into AIMSeasons: 1
19 - AIMWorkingDays
Rows inserted into AIMWorkingDays: 1
20 - AIMHelp
Rows inserted into AIMHelp: 1
21 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 1999 12:00AM
	and FirstFiscalYear: 1999
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows inserted into AIMYears: 7
Rows inserted into AIMDays: 2557
22 - AIMTranslation
Rows inserted into AIMTranslation: 8380
23 - AIMCodeLookup
Rows inserted into AIMCodeLookup: 195
Updating statistics...
Updating dbo.AIMTables
Updating dbo.AIMFunctions
Updating dbo.AIMTransferPolicy
Updating dbo.POStatistics
Updating dbo.AIMLocations
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.ProdConstraintTemp
Updating dbo.AIMLanguage
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.RevCycles
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.AIMCompanionItem
Updating dbo.SSA_DR_Migration
Updating dbo.Item
Updating dbo.AIMVelCodePcts
Updating dbo.AIMCompanionItemDetail
Updating dbo.SSA_DR_Migration_Log
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMYears
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AllocDefaults
Updating dbo.ItemHistory
Updating dbo.AllocDefaultsSource
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AIMPo
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMProductionConstraint
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.ItemHistoryAdjLog
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.ItemKitBOM
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.ItemPricing
Updating dbo.DXSets
Updating dbo.AIMAO
Updating dbo.UserElement
Updating dbo.ForecastFilterCriteria
Updating dbo.AIMAODetail
Updating dbo.ForecastRepository
Updating dbo.UserElementDetail
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.AIMDxDD
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.ForecastRepository_Log
Updating dbo.AIMDxIS
Updating dbo.AIMDxKB
Updating dbo.ItDefaults
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.ItStatus
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.AIMERROR_INFO
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMFcstMaster
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMFcstSetup
Updating dbo.PODetail
Updating dbo.AIMSourcingHierarchy
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 1
Data defaults completed.
---------- End ISQL results ---------- 
------ END -- DR Database Creation Scripts for SSA_DR_DEFAULT ------ 
Wed 11/30/2005 
 2:57p
D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\ALL__DRStoredProcs.sql
D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\ALL__DRTables.sql
D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\ALL__DRTriggers.sql
----- 4/4. Start scripts to create the database ----- 
	4.a. Using MASTER, create database SSA_DR_DEMO. 
Dropping database "SSA_DR_DEMO".
Deleting database file 'D:\SSA_DR_SS\DBFiles\SSA_DR_DEMO.ldf'.
Deleting database file 'D:\SSA_DR_SS\DBFiles\SSA_DR_DEMO.mdf'.
Creating database "SSA_DR_DEMO".
The CREATE DATABASE process is allocating 7.00 MB on disk 'SSA_DR_DEMO'.
The CREATE DATABASE process is allocating 25.00 MB on disk 'SSA_DR_DEMO_Log'.
Setting *dboptions...
Database creation complete for database: "SSA_DR_DEMO".
---------- Begin ISQL results --------- 
---------- End ISQL results ---------- 
	4.b. Using SSA_DR_DEMO, create tables. 
---------- Begin ISQL results --------- 
Server: Msg 1767, Level 16, State 1, Line 3
Foreign key 'FK_ALTERNATESOURCE_Item' references invalid table 'dbo.Item'.
Server: Msg 1750, Level 16, State 1, Line 3
Could not create constraint. See previous errors.
Warning: The table 'ForecastRepository' has been created but its maximum row size (8210) exceeds the maximum number of bytes per row (8060). INSERT or UPDATE of a row in this table will fail if the resulting row length exceeds 8060 bytes.
---------- End ISQL results ---------- 
	4.c. Using SSA_DR_DEMO, create stored procedures. 
---------- Begin ISQL results --------- 
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Alloc_Pass1_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Alloc_Pass1_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Alloc_Pass2_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_GetSessionID_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Alloc_ScratchInserts_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UpdateAIMAOOrderStatus_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UpdateAIMAOOrderStatus_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UpdateAIMAOOrderStatus_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Alloc_UpdateAggregates_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Alloc_ReleaseOrders_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UpdateAIMAOOrderStatus_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UpdateAIMAOOrderStatus_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UpdateAIMAOOrderStatus_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VerIFyDxFile_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VerifyDxFile_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_GetPd_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_GetPd_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_ItmChg_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_UOMChg_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_POInsert_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_POHdrInsert_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_POHdrInsert_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_OrdGen_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_OrdGen_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_OrdGen_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_OrdGen_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_OrdGen_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_OrdGen_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_Update_RevCycle_Date_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VendorSizingCtrl_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VendorSizingCtrl_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VendorSizingCtrl_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_PackRounding_sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_POInsert_sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_SetLowerVendorCostAlert_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_POHdrInsert_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_ProductionConstraint_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VelCode_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VelCode_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VelCode_Sp'. The stored procedure will still be created.
Cannot add rows to sysdepends for the current stored procedure because it depends on the missing object 'AIM_VendorSizing_Sp'. The stored procedure will still be created.
---------- End ISQL results ---------- 
	4.d. Using SSA_DR_DEMO, create triggers. 
---------- Begin ISQL results --------- 
The command(s) completed successfully.
---------- End ISQL results ---------- 
	4.e. Populate SSA_DR_DEMO with default data. 
Default data inserted into DB. 
---------- Begin ISQL results --------- 
1 - SysCtrl
Server: Msg 4863, Level 16, State 1, Line 1
Bulk insert data conversion error (truncation) for row 1, column 64 (AllowNegativeAvailQty).
---------- End ISQL results ---------- 
------ END -- DR Database Creation Scripts for SSA_DR_DEMO ------ 
Wed 11/30/2005 
 2:58p
