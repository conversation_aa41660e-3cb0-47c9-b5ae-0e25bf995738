AO1	1	RcdId	Record Id	XX	2	Output; H1
AO1	2	Ordnbr	Order Number	X(12)	12	Output
AO1	3	Seqnbr	Sequence Number	999999	6	Output
AO1	4	Lcid	Location ID	X(12)	12	Output
AO1	5	LDivision	Division ID	X(20)	20	Output
AO1	6	LRegion	Region ID	X(20)	20	Output
AO1	7	TransmitPO	Transmit Purchase Order Via	99	2	"Output; 0=Fax, 1=EDI"
AO1	8	LName	Location Name	X(30)	30	Output
AO1	9	LAddress1	Location Address Line 1	X(30)	30	Optional Output
AO1	10	LAddress2	Location Address Line 2	X(30)	30	Optional Output
AO1	11	LAddress3	Location Address Line 3	X(30)	30	Optional Output
AO1	12	LAddress4	Location Address Line 4	X(30)	30	Optional Output
AO1	13	LCity	Location City	X(20)	20	Optional Output
AO1	14	LState	Location State	X(10)	10	Optional Output
AO1	15	LZip	Location Postal Code	X(10)	10	Optional Output
AO1	16	Country	Location Country	X(30)	30	Optional Output
AO1	17	UserInitials	User's Initials	XXX	3	Optional Output
AO1	18	Remarks1	Remarks - Line 1	X(50)	50	Optional Output
AO1	19	Remarks2	Remarks - Line 2	X(50)	50	Optional Output
AO1	20	Remarks3	Remarks - Line 3	X(50)	50	Optional Output
AO2	1	RcdId	Record Id	XX	2	Output; D1
AO2	2	Seqnbr	Sequence Number	999999	6	Output
AO2	3	OrdType	Allocation Order Type	X	1	"Output; P=PO, S=Special, T=Transfer"
AO2	4	LineNbr	Line Number	X(12)	12	Output
AO2	5	Lcid	Location ID	X(12)	12	Output
AO2	6	Item	Item ID	X(25)	25	Output
AO2	7	ItDesc	Item Description	X(30)	30	Output; Customer Reference for Specials
AO2	8	UOM	Unit of Measure	X(6)	6	Output
AO2	9	AllocUOM	Allocatable Unit of Measure	X(6)	6	See AIMUOM for details; The Allocatable Unit of Measure identifies the stocking unit for the pack-sized allocated quantity.  This is derived after the allocated quantity has been adjusted with the allocatable conversion factor (defined in SS and SL).    This is derived from the item detail for Items in Source locations.; Default = EA
AO2	10	AllocConvFactor	Allocatable Conversion Factor	999999999	9	"Integer (whole number) data from -2^31 (-2,147,483,648) through 2^31 - 1 (2,147,483,647).; The Allocatable Conversion Factor determines the conversion to be applied in the process of applying a pack size to the allocated quantity.  This is to be defined for Items in Source locations.  This translated to a final quantity in the allocatable unit of measure (defined below); Default = 1"
AO2	11	RequestedQty	Requested Quantity	999999999	9	Output
AO2	12	AllocatedQty	Allocated Quantity	999999999	9	Output
AO2	13	Cost	Unit Cost	999999.9999	11	Output; Unit Cost for Specials
AO2	14	TxnDate	Re-Stock Transaction Date	yyyymmdd	10	Output
AO2	15	TxnTimeOfDay	Re-Stock Time of Day	HH:MM	5	Output
AO2	16	AllocationDate	Allocation Transaction Date	yyyymmdd	10	Output
AO2	17	AllocationTimeO	Allocation Time of Day	HH:MM	5	Output
AS	1	SeqNbr	Sequence Number	999999999	9	Required
AS	2	Enabled	Enabled	X(1)	1	Required - 1 or 0
AS	3	Item	Item ID	X(25)	25	Required
AS	4	LcId	Location ID	X(12)	12	Required
AS	5	VnId	Vendor ID	X(12)	12	Required
AS	6	Assort	Assortment ID	X(12)	12	Required
AS	7	BuyingUOM	Buying Unit of Measure	X(6)	6	Required - Defaulted to EA
AS	8	ConvFactor	UOM Conversion Factor	9999999	7	Required - Defaulted to one
AS	9	PackRounding	Pack Rounding	X(1)	1	"Required - D,R or U"
AS	10	StdCost	Cost	999999.9999	11	Required - Defaulted to Zero
AS	11	LeadTime	Lead Time	999	3	Required - Defaulted to zero
DD	1	Seqnbr	Sequence Number	999999	6	Required
DD	2	Ordnbr	Order Number	X(12)	12	Required; Always 0
DD	3	Doctype	Document Type	X(2)	2	Required; Always DD=Demand Detail
DD	4	Prmprog	Promotion Program	X(3)	3	Required; INQ=Inquiry
DD	5	Linenbr	Line Number	X(3)	3	Required
DD	6	Cusnbr	Customer Number	X(12)	12	Required
DD	7	Lcid	Location ID	X(12)	12	Required
DD	8	Item	Item ID	X(25)	25	Required
DD	9	Ordqty	Order Quantity	9(9)	9	Required
DD	10	ShpQty	Ship Quantity	9(9)	9	Required; Always Zero (0)
DD	11	UOM	Unit of Measure	X(6)	6	Required
DD	12	Extcost	Extended Cost	99999999.99	11	Required; Always Zero (0)
DD	13	Extprice	Extended Price	99999999.99	11	Required; Always Zero (0)
DD	14	Reason_Code	Credit Reason Code	99	2	Required; 00=NA
DD	15	Txndate	Transaction Date	99/99/9999	10	Required
DD	16	TimeOfDay	Time-of-day for Order	HH:MM	5	Required
DD	17	Txn_Type	Transaction Type Code	X(2)	2	Required; IR=Regular Item
DD	18	Source	Source Code	X	1	Required; T=Transmitted or EDI
DD	19	Shipping	Shipping Code	99	2	Required; 00=Not Applicable
DD	20	Handling	Special Handling Code	X	1	Required; R=Regular
DD	21	OldUOM	Old Unit of Measure	X(6)	6	Optional; Not Used (Zero Length String)
DD	22	Uomfact	Unit of Measure Conversion Factor	999999.9999	11	Optional; Not Used (Zero Length String)
DD	23	Olditem	Old Item ID	X(25)	25	Optional; Not Used (Zero Length String)
FA	1	Seqnbr	Sequence Number	999999999	9	Required
FA	2	LineNbr	Line Number	999999	7	Required
FA	3	Forecast	Forecast ID	X(12)	12	Required
FA	4	Location	Location ID	X(12)	12	Required
FA	5	Item	Item ID	X(25)	25	Required
FA	6	AdjType	Adjustment Type	X(1)	1	"Required:0=Override, 1=Units, 2=Percentage"
FA	7	AdjQty	Adjustment Quantity	999999999.9	10	Required
FA	8	Period StartDate	Period Start Date	yyyymmdd	10	Required
FA	9	Period EndDate	Period End Date	yyyymmdd	10	Required
FA	10	User ID	User ID	X(12)	12	Required
FA	11	AdjDesc	Adjustment Description	X(255)	255	Required
HS	1	Seqnbr	Sequence Number	999999	6	Required
HS	2	Lcid	Location ID	X(12)	12	Required
HS	3	Item	Item ID	X(25)	25	Required
HS	4	Hisyear	History Year	9999	4	Required
HS	5	Cps01	Current Period Sales - Period 01	999999999.9	11	Required
HS	6	Cps02	Current Period Sales - Period 02	999999999.9	11	Required
HS	7	Cps03	Current Period Sales - Period 03	999999999.9	11	Required
HS	8	Cps04	Current Period Sales - Period 04	999999999.9	11	Required
HS	9	Cps05	Current Period Sales - Period 05	999999999.9	11	Required
HS	10	Cps06	Current Period Sales - Period 06	999999999.9	11	Required
HS	11	Cps07	Current Period Sales - Period 07	999999999.9	11	Required
HS	12	Cps08	Current Period Sales - Period 08	999999999.9	11	Required
HS	13	Cps09	Current Period Sales - Period 09	999999999.9	11	Required
HS	14	Cps10	Current Period Sales - Period 10	999999999.9	11	Required
HS	15	Cps11	Current Period Sales - Period 11	999999999.9	11	Required
HS	16	Cps12	Current Period Sales - Period 12	999999999.9	11	Required
HS	17	Cps13	Current Period Sales - Period 13	999999999.9	11	Required
HS	18	Cps14	Current Period Sales - Period 14	999999999.9	11	Required
HS	19	Cps15	Current Period Sales - Period 15	999999999.9	11	Required
HS	20	Cps16	Current Period Sales - Period 16	999999999.9	11	Required
HS	21	Cps17	Current Period Sales - Period 17	999999999.9	11	Required
HS	22	Cps18	Current Period Sales - Period 18	999999999.9	11	Required
HS	23	Cps19	Current Period Sales - Period 19	999999999.9	11	Required
HS	24	Cps20	Current Period Sales - Period 20	999999999.9	11	Required
HS	25	Cps21	Current Period Sales - Period 21	999999999.9	11	Required
HS	26	Cps22	Current Period Sales - Period 22	999999999.9	11	Required
HS	27	Cps23	Current Period Sales - Period 23	999999999.9	11	Required
HS	28	Cps24	Current Period Sales - Period 24	999999999.9	11	Required
HS	29	Cps25	Current Period Sales - Period 25	999999999.9	11	Required
HS	30	Cps26	Current Period Sales - Period 26	999999999.9	11	Required
HS	31	Cps27	Current Period Sales - Period 27	999999999.9	11	Required
HS	32	Cps28	Current Period Sales - Period 28	999999999.9	11	Required
HS	33	Cps29	Current Period Sales - Period 29	999999999.9	11	Required
HS	34	Cps30	Current Period Sales - Period 30	999999999.9	11	Required
HS	35	Cps31	Current Period Sales - Period 31	999999999.9	11	Required
HS	36	Cps32	Current Period Sales - Period 32	999999999.9	11	Required
HS	37	Cps33	Current Period Sales - Period 33	999999999.9	11	Required
HS	38	Cps34	Current Period Sales - Period 34	999999999.9	11	Required
HS	39	Cps35	Current Period Sales - Period 35	999999999.9	11	Required
HS	40	Cps36	Current Period Sales - Period 36	999999999.9	11	Required
HS	41	Cps37	Current Period Sales - Period 37	999999999.9	11	Required
HS	42	Cps38	Current Period Sales - Period 38	999999999.9	11	Required
HS	43	Cps39	Current Period Sales - Period 39	999999999.9	11	Required
HS	44	Cps40	Current Period Sales - Period 40	999999999.9	11	Required
HS	45	Cps41	Current Period Sales - Period 41	999999999.9	11	Required
HS	46	Cps42	Current Period Sales - Period 42	999999999.9	11	Required
HS	47	Cps43	Current Period Sales - Period 43	999999999.9	11	Required
HS	48	Cps44	Current Period Sales - Period 44	999999999.9	11	Required
HS	49	Cps45	Current Period Sales - Period 45	999999999.9	11	Required
HS	50	Cps46	Current Period Sales - Period 46	999999999.9	11	Required
HS	51	Cps47	Current Period Sales - Period 47	999999999.9	11	Required
HS	52	Cps48	Current Period Sales - Period 48	999999999.9	11	Required
HS	53	Cps49	Current Period Sales - Period 49	999999999.9	11	Required
HS	54	Cps50	Current Period Sales - Period 50	999999999.9	11	Required
HS	55	Cps51	Current Period Sales - Period 51	999999999.9	11	Required
HS	56	Cps52	Current Period Sales - Period 52	999999999.9	11	Required
HS	57	QtyOrd01	Quantity Ordered - Period 01	999999999.9	11	Required
HS	58	QtyOrd02	Quantity Ordered - Period 02	999999999.9	11	Required
HS	59	QtyOrd03	Quantity Ordered - Period 03	999999999.9	11	Required
HS	60	QtyOrd04	Quantity Ordered - Period 04	999999999.9	11	Required
HS	61	QtyOrd05	Quantity Ordered - Period 05	999999999.9	11	Required
HS	62	QtyOrd06	Quantity Ordered - Period 06	999999999.9	11	Required
HS	63	QtyOrd07	Quantity Ordered - Period 07	999999999.9	11	Required
HS	64	QtyOrd08	Quantity Ordered - Period 08	999999999.9	11	Required
HS	65	QtyOrd09	Quantity Ordered - Period 09	999999999.9	11	Required
HS	66	QtyOrd10	Quantity Ordered - Period 10	999999999.9	11	Required
HS	67	QtyOrd11	Quantity Ordered - Period 11	999999999.9	11	Required
HS	68	QtyOrd12	Quantity Ordered - Period 12	999999999.9	11	Required
HS	69	QtyOrd13	Quantity Ordered - Period 13	999999999.9	11	Required
HS	70	QtyOrd14	Quantity Ordered - Period 14	999999999.9	11	Required
HS	71	QtyOrd15	Quantity Ordered - Period 15	999999999.9	11	Required
HS	72	QtyOrd16	Quantity Ordered - Period 16	999999999.9	11	Required
HS	73	QtyOrd17	Quantity Ordered - Period 17	999999999.9	11	Required
HS	74	QtyOrd18	Quantity Ordered - Period 18	999999999.9	11	Required
HS	75	QtyOrd19	Quantity Ordered - Period 19	999999999.9	11	Required
HS	76	QtyOrd20	Quantity Ordered - Period 20	999999999.9	11	Required
HS	77	QtyOrd21	Quantity Ordered - Period 21	999999999.9	11	Required
HS	78	QtyOrd22	Quantity Ordered - Period 22	999999999.9	11	Required
HS	79	QtyOrd23	Quantity Ordered - Period 23	999999999.9	11	Required
HS	80	QtyOrd24	Quantity Ordered - Period 24	999999999.9	11	Required
HS	81	QtyOrd25	Quantity Ordered - Period 25	999999999.9	11	Required
HS	82	QtyOrd26	Quantity Ordered - Period 26	999999999.9	11	Required
HS	83	QtyOrd27	Quantity Ordered - Period 27	999999999.9	11	Required
HS	84	QtyOrd28	Quantity Ordered - Period 28	999999999.9	11	Required
HS	85	QtyOrd29	Quantity Ordered - Period 29	999999999.9	11	Required
HS	86	QtyOrd30	Quantity Ordered - Period 30	999999999.9	11	Required
HS	87	QtyOrd31	Quantity Ordered - Period 31	999999999.9	11	Required
HS	88	QtyOrd32	Quantity Ordered - Period 32	999999999.9	11	Required
HS	89	QtyOrd33	Quantity Ordered - Period 33	999999999.9	11	Required
HS	90	QtyOrd34	Quantity Ordered - Period 34	999999999.9	11	Required
HS	91	QtyOrd35	Quantity Ordered - Period 35	999999999.9	11	Required
HS	92	QtyOrd36	Quantity Ordered - Period 36	999999999.9	11	Required
HS	93	QtyOrd37	Quantity Ordered - Period 37	999999999.9	11	Required
HS	94	QtyOrd38	Quantity Ordered - Period 38	999999999.9	11	Required
HS	95	QtyOrd39	Quantity Ordered - Period 39	999999999.9	11	Required
HS	96	QtyOrd40	Quantity Ordered - Period 40	999999999.9	11	Required
HS	97	QtyOrd41	Quantity Ordered - Period 41	999999999.9	11	Required
HS	98	QtyOrd42	Quantity Ordered - Period 42	999999999.9	11	Required
HS	99	QtyOrd43	Quantity Ordered - Period 43	999999999.9	11	Required
HS	100	QtyOrd44	Quantity Ordered - Period 44	999999999.9	11	Required
HS	101	QtyOrd45	Quantity Ordered - Period 45	999999999.9	11	Required
HS	102	QtyOrd46	Quantity Ordered - Period 46	999999999.9	11	Required
HS	103	QtyOrd47	Quantity Ordered - Period 47	999999999.9	11	Required
HS	104	QtyOrd48	Quantity Ordered - Period 48	999999999.9	11	Required
HS	105	QtyOrd49	Quantity Ordered - Period 49	999999999.9	11	Required
HS	106	QtyOrd50	Quantity Ordered - Period 50	999999999.9	11	Required
HS	107	QtyOrd51	Quantity Ordered - Period 51	999999999.9	11	Required
HS	108	QtyOrd52	Quantity Ordered - Period 52	999999999.9	11	Required
HS	109	OrdCnt01	Order Count - Period 01	999999999	9	Required
HS	110	OrdCnt02	Order Count - Period 02	999999999	9	Required
HS	111	OrdCnt03	Order Count - Period 03	999999999	9	Required
HS	112	OrdCnt04	Order Count - Period 04	999999999	9	Required
HS	113	OrdCnt05	Order Count - Period 05	999999999	9	Required
HS	114	OrdCnt06	Order Count - Period 06	999999999	9	Required
HS	115	OrdCnt07	Order Count - Period 07	999999999	9	Required
HS	116	OrdCnt08	Order Count - Period 08	999999999	9	Required
HS	117	OrdCnt09	Order Count - Period 09	999999999	9	Required
HS	118	OrdCnt10	Order Count - Period 10	999999999	9	Required
HS	119	OrdCnt11	Order Count - Period 11	999999999	9	Required
HS	120	OrdCnt12	Order Count - Period 12	999999999	9	Required
HS	121	OrdCnt13	Order Count - Period 13	999999999	9	Required
HS	122	OrdCnt14	Order Count - Period 14	999999999	9	Required
HS	123	OrdCnt15	Order Count - Period 15	999999999	9	Required
HS	124	OrdCnt16	Order Count - Period 16	999999999	9	Required
HS	125	OrdCnt17	Order Count - Period 17	999999999	9	Required
HS	126	OrdCnt18	Order Count - Period 18	999999999	9	Required
HS	127	OrdCnt19	Order Count - Period 19	999999999	9	Required
HS	128	OrdCnt20	Order Count - Period 20	999999999	9	Required
HS	129	OrdCnt21	Order Count - Period 21	999999999	9	Required
HS	130	OrdCnt22	Order Count - Period 22	999999999	9	Required
HS	131	OrdCnt23	Order Count - Period 23	999999999	9	Required
HS	132	OrdCnt24	Order Count - Period 24	999999999	9	Required
HS	133	OrdCnt25	Order Count - Period 25	999999999	9	Required
HS	134	OrdCnt26	Order Count - Period 26	999999999	9	Required
HS	135	OrdCnt27	Order Count - Period 27	999999999	9	Required
HS	136	OrdCnt28	Order Count - Period 28	999999999	9	Required
HS	137	OrdCnt29	Order Count - Period 29	999999999	9	Required
HS	138	OrdCnt30	Order Count - Period 30	999999999	9	Required
HS	139	OrdCnt31	Order Count - Period 31	999999999	9	Required
HS	140	OrdCnt32	Order Count - Period 32	999999999	9	Required
HS	141	OrdCnt33	Order Count - Period 33	999999999	9	Required
HS	142	OrdCnt34	Order Count - Period 34	999999999	9	Required
HS	143	OrdCnt35	Order Count - Period 35	999999999	9	Required
HS	144	OrdCnt36	Order Count - Period 36	999999999	9	Required
HS	145	OrdCnt37	Order Count - Period 37	999999999	9	Required
HS	146	OrdCnt38	Order Count - Period 38	999999999	9	Required
HS	147	OrdCnt39	Order Count - Period 39	999999999	9	Required
HS	148	OrdCnt40	Order Count - Period 40	999999999	9	Required
HS	149	OrdCnt41	Order Count - Period 41	999999999	9	Required
HS	150	OrdCnt42	Order Count - Period 42	999999999	9	Required
HS	151	OrdCnt43	Order Count - Period 43	999999999	9	Required
HS	152	OrdCnt44	Order Count - Period 44	999999999	9	Required
HS	153	OrdCnt45	Order Count - Period 45	999999999	9	Required
HS	154	OrdCnt46	Order Count - Period 46	999999999	9	Required
HS	155	OrdCnt47	Order Count - Period 47	999999999	9	Required
HS	156	OrdCnt48	Order Count - Period 48	999999999	9	Required
HS	157	OrdCnt49	Order Count - Period 49	999999999	9	Required
HS	158	OrdCnt50	Order Count - Period 50	999999999	9	Required
HS	159	OrdCnt51	Order Count - Period 51	999999999	9	Required
HS	160	OrdCnt52	Order Count - Period 52	999999999	9	Required
IS	1	AllocPrimaryItem	Allocation Primary Item ID	X(25)	25	Required
IS	2	AllocSubsItem	Allocation Substitute Item ID	X(25)	25	Required
IS	3	AllocSubstPriority	Allocation Substitute Priority	99999	5	"Optional; 0-32,767"
KB	1	SeqNbr	Sequence Number	999999999	9	Required
KB	2	Enabled	Enabled	X(1)	1	"Required; Y=Yes, N=No"
KB	3	Lcid	Location ID	X(12)	12	Required
KB	4	Item	Master Item ID	X(25)	25	Required
KB	5	ItemComponent	Component Item ID	X(25)	25	Required
KB	6	ComponentUnits	Component Units	9999999	7	Required;1-9999999
KB	7	ComponentScrap	Component Scrap	99999999.99	10	Required
KB	8	StartDate	Start Date`	YYYYMMDD	8	Required
KB	9	EndDate	End Date	YYYYMMDD	8	Required
LC	1	SeqNbr	Sequence Number	999999	6	Required
LC	2	Lcid	Location ID	X(12)	12	Required
LC	3	LName	Location Name	X(30)	30	Required
LC	4	LType	Location Type	X(1)	1	"Required; S = Source, D = Destination"
LC	5	LStatus	Location Status	X(1)	1	"Required; N = New Location, F = Flagship, I = Inactive, A = Active"
LC	6	LDivision	Division ID	X(20)	20	Required
LC	7	LRegion	Region ID	X(20)	20	Required
LC	8	LUserDefined	User Defined field	X(30)	30	Optional
LC	9	LAddress1	Location Address 1	X(30)	30	Optional
LC	10	LAddress2	Location Address 2	X(30)	30	Optional
LC	11	LAddress3	Location Address 3	X(30)	30	Optional
LC	12	LAddress4	Location Address 4	X(30)	30	Optional
LC	13	LCity	City	X(20)	30	Optional
LC	14	LState	State	X(10)	10	Optional
LC	15	LZip	Postal Code	X(10)	10	Optional
LC	16	LCountry	Country	X(30)	30	Optional
LC	17	LContact	Location Contact	X(30)	30	Optional
LC	18	LPhone	Contact Telephone	X(20)	20	Optional
LC	19	LFax	Contact Fax Number	X(20)	20	Optional
LC	20	LEMail	Contact Email	X(30)	30	Optional
LC	21	LRank	Rank	999	3	Optional
LC	22	PutAwayDays	Put Away Days	999	3	Optional
LC	23	ReplenCost	Replenishment Cost	999999999	9	Optional
LC	24	DemandSource	Demand Source	X(1)	1	"Optional; O = Quantity Ordered, S = Quantity Shipped"
LC	25	LeadTimeSource	Lead Time Source	X(1)	1	"Optional; S = Stock Status, L = Lead-Time, V = Vendor"
LC	26	Dft_Byid	Default Buyer ID	X(12)	12	Optional
LC	27	LookBackPds	Look Back Periods	99999	5	Optional
LC	28	DmdScalingFacto	Demand Scaling Factor	99999	5	Optional
LC	29	ScalingEffUntil	Scaling Effective Until	yyyymmdd	10	Optional
LC	30	Freeze_Period	Freeze Period	999999999	9	Optional
LC	31	UpdateCurrentYe	UpdateCurrentYearOption	X(1)	1	"Optional; Y = Yes, N = No"
LC	32	DropShip_XDock	DropShip_XDock	X(1)	1	"Optional; Y = Yes, N = No"
LC	33	Dft_ReviewerId	Default Reviewer ID	X(12)	12	Optional
LC	34	ExceptionPct	Exception Percentage	999	3	Optional
LT	1	Seqnbr	Sequence Number	999999	6	Required
LT	2	POnbr	Purchase Order Number	X(12)	12	Required
LT	3	Linenbr	Line Number	999	3	Required
LT	4	Vnid	Vendor ID	X(12)	12	Required
LT	5	Lcid	Location ID	X(12)	12	Required
LT	6	Item	Item ID	X(25)	25	Required
LT	7	QtyOrd	Quantity Ordered	9(9)	9	Required
LT	8	QtyRec	Quantity Received	9(9)	9	Required
LT	9	DateOrd	Order Date	99/99/9999	10	Required
LT	10	DateRec	Received Date	99/99/9999	10	Required
LT	11	DatePutAway	Putaway Date	99/99/9999	10	Required
LT	12	Oh	On-Hand Quantity	9(9)	9	Required; On-Hand Balance after receipt
PO1	1	RcdId	Record Id	XX	2	Output; H1
PO1	2	PONbr	PO Number	9999999999	10	Output
PO1	3	Seqnbr	Sequence Number	999999	6	Output
PO1	4	Vnid	Vendor ID	X(12)	12	Output
PO1	5	Assort	Vendor Assortment	X(12)	12	Output
PO1	6	Lcid	Location ID	X(12)	12	Output
PO1	7	TransmitPO	Transmit Purchase Order Via	99	2	"Output; 0=Fax, 1=EDI"
PO1	8	ShipIns	Shipping Instructions	X(20)	20	Optional Output
PO1	9	UserInitials	User's Initials	XXX	3	Optional Output
PO1	10	Remarks1	Remarks	X(50)	50	Optional Output
PO1	11	Remarks2	Remarks	X(50)	50	Optional Output
PO1	12	Remarks3	Remarks	X(50)	50	Optional Output
PO2	1	RcdId	Record Id	XX	2	Output; H2
PO2	2	POnbr	PO Number	9999999999	10	Output
PO2	3	Seqnbr	Sequence Number	999999	6	Output
PO2	4	Vnid	Vendor ID	X(12)	12	Output
PO2	5	Assort	Vendor Assortment	X(12)	12	Output
PO2	6	LcId	Location Id	X(12)	12	Output
PO2	7	Vname	Vendor Name	X(30)	30	Output
PO2	8	VAddress1	Vendor Address 1	X(30)	30	Optional Output
PO2	9	VAddress2	Vendor Address 2	X(30)	30	Optional Output
PO2	10	Vcity	Vendor City	X(20)	20	Optional Output
PO2	11	Vstate	Vendor State	XX	2	Optional Output
PO2	12	Vzip	Vendor Zip	X(10)	10	Optional Output
PO3	1	RcdId	Record Id	XX	2	Output; D1
PO3	2	Seqnbr	Sequence Number	999999	6	Output
PO3	3	Potype	Purchase Order Type	X	1	"Output; P=PO, S=Special, T=Transfer"
PO3	4	Vnid	Vendor ID	X(12)	12	Output
PO3	5	Assort	Vendor Assortment	X(12)	12	Output
PO3	6	Lcid	Location ID	X(12)	12	Output
PO3	7	Item	Item ID	X(25)	25	Output
PO3	8	ItDesc	Item Description	X(30)	30	Output; Customer Reference for Specials
PO3	9	Cost	Unit Cost	999999.9999	11	Output; Unit Cost for Specials
PO3	10	Ordqty	Order Quantity	999999999	9	Output
PO3	11	UOM	Unit of Measure	XX	2	Output
PO3	12	BuyingUOM	Buying UOM	XX	2	Output;EA=Each
PO3	13	ConvFactor	Conversion Factor	9999999	7	Output;1
PO3	14	Isdate	Purchase Order Issue Date	yyyymmdd	10	Output
PO3	15	Dudate	Purchase Order Due Date	yyyymmdd	10	Output
PO3	16	PO_ByZone	Generate PO by Warehouse Zone	X	1	"Output; Y=Yes, N=No"
PO3	17	WarehouseZone	Warehouse Receiving Zone	X	1	Optional Output
RO	1	Seqnbr	Sequence Number	999999	6	Required
RO	2	Ordnbr	Order Number	X(12)	12	Required
RO	3	Ordtype	Order Type	X	1	"Required; S = Source, D = Destination"
RO	4	Linenbr	Line Number	X(3)	3	Required
RO	5	Lcid	Location ID	X(12)	12	Required
RO	6	Item	Item ID	X(25)	25	Required
RO	7	Ordqty	Order Quantity	9(9)	9	Required
RO	8	UOM	Unit of Measure	X(2)	2	Required
RO	9	Txndate	Transaction Date	99/99/9999	10	Required
RO	10	TimeOfDay	Time-of-day for Order	HH:MM	5	Required
RO	11	Txn_Type	Transaction Type Code	X(2)	2	"Required; IR=Regular Item, IA=Alternate Warehouse Item, IS=Special Order Item, IZ=Redistribution Center, IM=Manual Order, Catalog Item, MC=Miscellaneous Charge"
RO	12	OriginCode	Origin Code	X	1	"Optional; 0=Fax, 1=EDI"
RP	1	Seqnbr	Sequence Number	999999	6	Required
RP	2	Lcid	Location ID	X(12)	12	Required
RP	3	Item	Item ID	X(25)	25	Required
RP	4	ExceptionPct	Exception Percentage	999	3	Required
RP	5	OnHandQty	On-Hand Quantity	999999999	9	Required
RP	6	StandardQty	Standard Quantity	999999999	9	Required
RS	1	Seqnbr	Sequence Number	999999	6	Required
RS	2	Lcid_Destinatio	Re-Stock Location ID	X(12)	12	Required
RS	3	Lcid_Source	Source Location ID	X(12)	12	Required
RS	4	SrcPriority	Sourcing Priority	9999	4	Required
RS	5	ReviewerId	Reviewer ID	X(12)	12	Required
SD	1	Seqnbr	Sequence Number	999999	6	Required
SD	2	Ordnbr	Order Number	X(12)	12	Required
SD	3	Doctype	Document Type	X(2)	2	"Required; CM=Credit Memo, SO=Sales Order"
SD	4	Prmprog	Promotion Program	X(3)	3	Optional
SD	5	Linenbr	Line Number	X(3)	3	Required
SD	6	Cusnbr	Customer Number	X(12)	12	Required
SD	7	Lcid	Location ID	X(12)	12	Required
SD	8	Item	Item ID	X(25)	25	Required
SD	9	Ordqty	Order Quantity	9(9)	9	Required
SD	10	ShpQty	Ship Quantity	9(9)	9	Required
SD	11	UOM	Unit of Measure	X(2)	2	Required
SD	12	Extcost	Extended Cost	99999999.99	11	Required
SD	13	Extprice	Extended Price	99999999.99	11	Required
SD	14	Reason_Code	Credit Reason Code	99	2	"Optional; 00=N/A, 01 - 99"
SD	15	Txndate	Transaction Date	99/99/9999	10	Required
SD	16	TimeOfDay	Time-of-day for Order	HH:MM	5	Required
SD	17	Txn_Type	Transaction Type Code	X(2)	2	"Required; IR=Regular Item, IA=Alternate Warehouse Item, IS=Special Order Item, IZ=Redistribution Center, IM=Manual Order, Catalog Item, MC=Miscellaneous Charge"
SD	18	Source	Source Code	X	1	"Optional; A=Alternate Warehouse Order, R=Regular, T=Transmitted or EDI, W=Ship When In Order, X=Remote CRT"
SD	19	Shipping	Shipping Code	99	2	Optional; 01 - 09
SD	20	Handling	Special Handling Code	X	1	"Optional; D=Drop Ship, L=Wrap & Label, P=Wrap & Pack, R=Regular"
SD	21	OldUOM	Old Unit of Measure	XX	2	Optional; Not Used
SD	22	Uomfact	Unit of Measure Conversion Factor	999999.9999	11	Optional; Not Used
SD	23	Olditem	Old Item ID	X(25)	25	Optional; Not Used
SL	1	Seqnbr	Sequence Number	999999	6	Required
SL	2	Status	Item Status Code	X	1	"Required; A=Active, C=Catalog Only, D=Do Not Buy, I=Inactive, N=New-Ordered, P=Purge, U=New-Unordered"
SL	3	Lcid	Location ID	X(12)	12	Required
SL	4	Item	Item ID	X(25)	25	Required
SL	5	ActDate	Activation Date	99/99/9999	10	Required; Record insertion date
SL	6	InActDate	Inactivation Date	99/99/9999	10	Required; Default 12/31/9999
SL	7	Class1	Item Classification Code 1	X(6)	6	Required
SL	8	Class2	Item Classification Code 2	X(6)	6	Optional
SL	9	Class3	Item Classification Code 3	X(6)	6	Optional
SL	10	Class4	Item Classification Code 4	X(6)	6	Optional
SL	11	MDC	Master Distribution Center	X(12)	12	Required; Blank:         This item is purchased from a Vendor **MDC**:     This location is a Master Distribution Center Location Id: The Location Id of the Master Distribution Center
SL	12	UPC	Universal Product Code	X(22)	22	Optional
SL	13	OH	On-Hand Quantity	999999999	9	Required
SL	14	OO	On-Order Quantity	999999999	9	Required
SL	15	Comstk	Commited Stock	999999999	9	Required
SL	16	Bkorder	Back Order Quantity	999999999	9	Required
SL	17	Bkcomstk	Back Order Commited	999999999	9	Required
SL	18	Stkdate	Stock Date	99/99/9999	10	Required
SL	19	OnPromotion	On Promotion Flag	X	1	"Required; Y=Yes, N=No"
SL	20	UserRef1	User Reference 1	X(12)	12	Optional; For example: Catalog Page Number
SL	21	UserRef2	User Reference 2	X(12)	12	Optional; For example: Packing #1
SL	22	UserRef3	UserReference 3	X(12)	12	Optional; For example: Packing #2
SL	23	Freeze_Forecast	Freeze Period	X(1)	1	"Optional; Y = Yes, N = No"
SL	24	AllocatableQty	Allocatable Quantity	999999999	9	Optional
SS	1	Seqnbr	Sequence Number	999999	6	Required
SS	2	Status	Item Status Code	X	1	"Required; A=Active, C=Catalog Only, D=Do Not Buy, I=Inactive, N=New-Ordered, P=Purge, U=New-Unordered"
SS	3	Lcid	Location ID	X(12)	12	Required
SS	4	Item	Item ID	X(25)	25	Required
SS	5	Itdesc	Item Description	X(30)	30	Required
SS	6	ActDate	Activation Date	99/99/9999	10	Required; Record insertion date
SS	7	InActDate	Inactivation Date	99/99/9999	10	Required; Default 12/31/9999
SS	8	Class1	Item Classification Code 1	X(6)	6	Required
SS	9	Class2	Item Classification Code 2	X(6)	6	Optional
SS	10	Class3	Item Classification Code 3	X(6)	6	Optional
SS	11	Class4	Item Classification Code 4	X(6)	6	Optional
SS	12	Vnid	Vendor ID	X(12)	12	Required
SS	13	Assort	Vendor Assortment	X(12)	12	Required
SS	14	MDC	Master Distribution Center	X(12)	12	Required; Blank:         This item is purchased from a Vendor **MDC**:     This location is a Master Distribution Center Location Id: The Location Id of the Master Distribution Center
SS	15	Weight	Weight	999999.9999	11	Required
SS	16	Cube	Cube	999999.9999	11	Required
SS	17	ListPrice	List Price	999999.9999	11	Optional
SS	18	Price	Price	999999.9999	11	Optional
SS	19	Cost	Cost	999999.9999	11	Required
SS	20	UOM	Unit of Measure	X(2)	2	Required
SS	21	ConvFactor	UOM Conversion Factor	9999999	7	Required
SS	22	BuyingUOM	Buying Unit of Measure	XX	2	Required
SS	23	LT	Vendor Lead Time	999	3	Required
SS	24	UPC	Universal Product Code	X(22)	22	Optional
SS	25	OH	On-Hand Quantity	999999999	9	Required
SS	26	OO	On-Order Quantity	999999999	9	Required
SS	27	Comstk	Commited Stock	999999999	9	Required
SS	28	Bkorder	Back Order Quantity	999999999	9	Required
SS	29	Bkcomstk	Back Order Commited	999999999	9	Required
SS	30	Stkdate	Stock Date	99/99/9999	10	Required
SS	31	Binlocation	Bin Location	X(12)	12	Optional
SS	32	BkQty01	Break Quantity 01	9999999	7	Optional; >0
SS	33	BkCost01	Break Cost 01	999999.9999	11	Optional
SS	34	BkQty02	Break Quantity 02	9999999	7	Optional; >0
SS	35	BkCost02	Break Cost 02	999999.9999	11	Optional
SS	36	BkQty03	Break Quantity 03	9999999	7	Optional; >0
SS	37	BkCost03	Break Cost 03	999999.9999	11	Optional
SS	38	BkQty04	Break Quantity 04	9999999	7	Optional; >0
SS	39	BkCost04	Break Cost 04	999999.9999	11	Optional
SS	40	BkQty05	Break Quantity 05	9999999	7	Optional; >0
SS	41	BkCost05	Break Cost 05	999999.9999	11	Optional
SS	42	BkQty06	Break Quantity 06	9999999	7	Optional; >0
SS	43	BkCost06	Break Cost 06	999999.9999	11	Optional
SS	44	BkQty07	Break Quantity 07	9999999	7	Optional; >0
SS	45	BkCost07	Break Cost 07	999999.9999	11	Optional
SS	46	BkQty08	Break Quantity 08	9999999	7	Optional; >0
SS	47	BkCost08	Break Cost 08	999999.9999	11	Optional
SS	48	BkQty09	Break Quantity 09	9999999	7	Optional; >0
SS	49	BkCost09	Break Cost 09	999999.9999	11	Optional
SS	50	BkQty10	Break Quantity 10	9999999	7	Optional; >0
SS	51	BkCost10	Break Cost 10	999999.9999	11	Optional
SS	52	OnPromotion	On Promotion Flag	X	1	"Required; Y=Yes, N=No"
SS	53	OldUOM	Old Unit of Measure	XX	2	Optional
SS	54	Uomfact	Unit of Measure Conversion Factor	999999.9999	11	Optional
SS	55	Olditem	Old Item ID	X(25)	25	Optional
SS	56	NextPONbr_1	Next PO Number 1	X(12)	12	Optional
SS	57	NextPODate_1	Next PO Date 1	MM/DD/YYYY	10	Optional
SS	58	NextPOQty_1	Next PO Quantity 1	999999999	9	Optional
SS	59	NextPONbr_2	Next PO Number 2	X(12)	12	Optional
SS	60	NextPODate_2	Next PO Date 2	MM/DD/YYYY	10	Optional
SS	61	NextPOQty_2	Next PO Qty 2	999999999	9	Optional
SS	62	NextPONbr_3	Next PO Number 3	X(12)	12	Optional
SS	63	NextPODate_3	Next PO Date 3	MM/DD/YYYY	10	Optional
SS	64	NextPOQty_3	Next PO Qty 3	999999999	9	Optional
SS	65	UserRef1	User Reference 1	X(12)	12	Optional; For example: Catalog Page Number
SS	66	UserRef2	User Reference 2	X(12)	12	Optional; For example: Packing #1
SS	67	UserRef3	UserReference 3	X(12)	12	Optional; For example: Packing #2
SS	68	FreezeForecast	Freeze Forecast Flag	X	1	Optional
SS	69	AllocatableQty	Allocatable Quantity	999999999	9	Optional
SS	70	KitBOMFlag	Kit BOM Flag	X(1)	1	"Optional; The KitBOM Flag identifies the item as a Master Item with component items associated with it.; Y = Yes, N = No; Default = N"
SS	71	AllocConvFactor	Allocatable Conversion Factor	999999999	9	"Required; Integer (whole number) data from -2^31 (-2,147,483,648) through 2^31 - 1 (2,147,483,647).; The Allocatable Conversion Factor determines the conversion to be applied in the process of applying a pack size to the allocated quantity.  This is to be defined for Items in Source locations.  This translated to a final quantity in the allocatable unit of measure (defined below); Default = 1"
SS	72	AllocUOM	Allocatable Unit of Measure	X(6)	6	Required; The Allocatable Unit of Measure identifies the stocking unit for the pack-sized allocated quantity.  This is derived after the allocated quantity has been adjusted with the allocatable conversion factor (defined above).    This is to be defined for Items in Source locations.; See AIMUOM for details; Default = EA
VN	1	Seqnbr	Sequence Number	999999	6	Required
VN	2	Vnid	Vendor ID	X(12)	12	Required
VN	3	Assort	Assortment	X(12)	12	Required
VN	4	Vntype	Vendor Type	X(1)	1	"Required; V=Vendor, M=MDC"
VN	5	Vname	Vendor Name	X(30)	30	Required
VN	6	VAddress1	Vendor Address Line 1	X(30)	30	Optional
VN	7	VAddress2	Vendor Address Line 2	X(30)	30	Optional
VN	8	Vcity	Vendor City	X(20)	20	Optional
VN	9	Vstate	Vendor State	X(2)	2	Optional
VN	10	Vzip	Vendor Zip Code	X(10)	10	Optional
VN	11	Vcontact	Vendor Contact	X(30)	30	Optional
VN	12	Vphone	Vendor Contact Telephone	X(20)	20	Optional
VN	13	Vfax	Vendor Contact Fax	X(20)	20	Optional
VN	14	VEMail	Vendor Contact E-Mail	X(30)	30	Optional
VN	15	VDocEMail	Vendor Document E-Mail	X(30)	30	Optional
VN	16	Dft_ById	Default Buyer ID	X(12)	12	Optional
VN	17	Dft_RevCycle	Default Review Cycle	X(8)	8	Optional
VN	18	Dft_LeadTime	Default Lead Time	999	3	Required
VN	19	Vn_Min	Vendor Minimum	9999999.99	11	Optional
VN	20	Vn_Best	Vendor Best Buy	9999999.99	11	Optional
VN	21	Reach_code	Reach Code	X	1	"Optional; C=Cube, D=Dollars, U=Units, W=Weight"
VN	22	POByZone	Create PO's by Receiving Zone	X	1	"Optional; Y=Yes, N=No"
VN	23	VnComments1	Vendor Comments 1	X(255)	255	Optional
VN	24	VnComments2	Vendor Comments 2	X(255)	255	Optional
VN	25	TransmitPO	Transmit Purchase Order Via	99	2	"Optional; 0=Fax, 1=EDI"
VN	26	ShipIns	Shipping Instructions	X(20)	20	Optional
