1	1	"MovAvg(1,13)"	N	0
2	1	"MovAvg(1,26)"	N	0
3	1	"MovAvg(1,52)"	N	0
4	1	"MovAvg(53,65)"	N	0
5	1	"MovAvg(1,13) + Trend"	N	1
6	1	"MovAvg(1,26) + Trend"	N	1
7	1	"MovAvg(1,52) + Trend"	N	1
8	1	"MovAvg(53,65) + Trend"	N	1
9	1	"MovAvg(1,104)"	N	0
10	1	"[(2 * MovAvg(1,13)) + MovAvg(14,26)] / 3"	N	1
11	1	"MovAvg(1,13) + [[MovAvg(40,52) - MovAvg(53,65)] / 13]"	N	0
12	1	"[MovAvg(1,13) + MovAvg(1,13) + MovAvg(14,26) + MovAvg(27,39)] / 4 + SeaAdj"	Y	0
13	1	"[MovAvg(40,52) + MovAvg(92,104)] / 2 + Trend"	N	1
14	1	"MovAvg(1,104) + SeaAdj"	Y	0
15	1	"MovAvg(1,26) + Trend + SeaAdj"	N	0
16	1	"MovAvg(1,52) + Trend + SeaAdj"	Y	0
17	1	ExpSmooth(.1)	N	1
18	1	ExpSmooth(.2)	N	1
19	1	ExpSmooth(.3)	N	1
20	1	ExpSmooth(.1) + SeaAdj	Y	1
21	1	ExpSmooth(.2) + SeaAdj	Y	1
22	1	ExpSmooth(.3) + SeaAdj	Y	1
23	1	Adaptive Smoothing	Y	1
24	1	"MovAvg(1,52 or 1, From Hist Start - 4) + Optional Trend + Optional SeaAdj"	N	0
25	1	"MovAvg(1,26 or 1, From Hist Start - 4) + Optional Trend + Optional SeaAdj"	N	0
