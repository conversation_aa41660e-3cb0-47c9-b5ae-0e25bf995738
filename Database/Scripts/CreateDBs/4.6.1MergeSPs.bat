@echo off
REM Created Sept. 07 2003 by Annalaksh<PERSON> Stocksdale.
REM -------------------------------------------------------------------------------
echo 	This script is intended to be called from "CreateDB.bat". 
echo 	When called, it will merge all SSA DR stored procedure scripts, 
echo 	with primary ones appearing first, 
echo 	followed by the rest in alphabetical order.
REM ~~~~~~~~~~~~~~~~~~
REM Required Software: 
REM 	Microsoft SQL Server 2000 server with Query Analyser
REM ~~~~~~~~~~
REM -------------------------------------------------------------------------------

setlocal

set SS_DIR=%1
set OUTPUT_DIR=\EXceedAIMProject\Database\Scripts\CreateDBs\
set LOG_FILE=Batch_CreateDB.LOG

set CREATESPs_SQL=TEMP_DRStoredProcs.SQL
set CREATESPs_LOG=TEMP_CreateSPs.LOG

:MergeSPs
	REM -------------------------------------------------------------------------------
	echo 	Merging *_SP.sql...
	REM -------------------------------------------------------------------------------
	echo ----- 2/4. Merge *sp*.SQL ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if exist %SS_DIR%%OUTPUT_DIR%%CREATESPs_SQL% (del %SS_DIR%%OUTPUT_DIR%%CREATESPs_SQL%)

	REM ************************************************************
	REM -- Get the dependencies established, first. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	copy /V /Y "..\..\Stored Procedures\AIM_VerifyDxFile_Sp.SQL" + "..\..\Stored Procedures\AIM_GetSaVersion_Sp.SQL" + "..\..\Stored Procedures\AIM_GetPd_Sp.SQL" + "..\..\Stored Procedures\AIM_GetDate_Sp.SQL" + "..\..\Stored Procedures\AIM_GetLastWeekSales_Sp.SQL" + "..\..\Stored Procedures\AIM_POInsert_Sp.SQL" + "..\..\Stored Procedures\AIM_POHdrInsert_Sp.SQL" + "..\..\Stored Procedures\AIM_PackRounding_Sp.SQL" + "..\..\Stored Procedures\AIM_GetAccum_LT_Sp.SQL"+ "..\..\Stored Procedures\AIM_OrdGen_Sp.SQL" + "..\..\Stored Procedures\AIM_ItmChg_Sp.SQL" + "..\..\Stored Procedures\AIM_UOMChg_Sp.SQL" + "..\..\Stored Procedures\AIM_ForecastRepository_GetKey_Sp.SQL" + "..\..\Stored Procedures\AIM_ForecastRepository_Save_Sp.SQL" + "..\..\Stored Procedures\AIM_Update_RevCycle_Date_Sp.SQL" + "..\..\Stored Procedures\AIM_VendorSizing_Sp.SQL" + "..\..\Stored Procedures\AIM_VendorSizingCtrl_Sp.SQL" + "..\..\Stored Procedures\AIM_ProdPackRounding_SP.SQL" + "..\..\Stored Procedures\AIM_ProductionConstraint_Sp.SQL" + "..\..\Stored Procedures\AIM_VelCode_Sp.SQL" + "..\..\Stored Procedures\AIM_Alloc_Pass1_Sp.SQL" + "..\..\Stored Procedures\AIM_Alloc_Pass2_Sp.SQL" + "..\..\Stored Procedures\AIM_GetSessionID_Sp.SQL" + "..\..\Stored Procedures\AIM_Alloc_ScratchInserts_Sp.SQL" + "..\..\Stored Procedures\AIM_AllocateInventory_Sp.SQL" + "..\..\Stored Procedures\AIM_UpdateAIMAOOrderStatus_Sp.SQL" + "..\..\Stored Procedures\AIM_Alloc_UpdateAggregates_Sp.SQL" + "..\..\Stored Procedures\AIM_Alloc_HoldExceptions_Sp.SQL" + "..\..\Stored Procedures\AIM_Alloc_ReleaseOrders_Sp.SQL" "..\..\Stored Procedures\A1Dependencies.SQL" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Move above files to a temp name while creating the composite DRStoredProcs script. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	rename "..\..\Stored Procedures\AIM_VerifyDxFile_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetSaVersion_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetPd_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetDate_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetLastWeekSales_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_POInsert_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_POHdrInsert_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_PackRounding_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetAccum_LT_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_OrdGen_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ItmChg_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_UOMChg_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ForecastRepository_GetKey_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ForecastRepository_Save_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Update_RevCycle_Date_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_VendorSizing_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_VendorSizingCtrl_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ProdPackRounding_SP.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ProductionConstraint_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_VelCode_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_Pass1_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_Pass2_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetSessionID_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_ScratchInserts_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_AllocateInventory_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_UpdateAIMAOOrderStatus_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_UpdateAggregates_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_HoldExceptions_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_ReleaseOrders_Sp.SQL" *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Now, copy all the create-procedures into the composite DRStoredProcs.SQL script. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	copy /V /-Y "..\..\Stored Procedures\*.SQL" %SS_DIR%%OUTPUT_DIR%%CREATESPs_SQL% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Move the dependencies back to their original name. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	rename "..\..\Stored Procedures\AIM_VerifyDxFile_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetSaVersion_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetPd_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetDate_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetLastWeekSales_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_POInsert_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_POHdrInsert_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_PackRounding_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetAccum_LT_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_OrdGen_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ItmChg_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_UOMChg_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ForecastRepository_GetKey_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ForecastRepository_Save_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Update_RevCycle_Date_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_VendorSizing_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_VendorSizingCtrl_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ProdPackRounding_SP.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_ProductionConstraint_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_VelCode_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_Pass1_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_Pass2_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_GetSessionID_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_ScratchInserts_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_AllocateInventory_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_UpdateAIMAOOrderStatus_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_UpdateAggregates_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_HoldExceptions_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename "..\..\Stored Procedures\AIM_Alloc_ReleaseOrders_Sp.TXT" *.SQL >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM Delete the temporary file. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************

	del "..\..\Stored Procedures\A1Dependencies.SQL" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	call FixString.cmd


GOTO EXIT_BATCH

:LOW_MEMORY
	REM -------------------------------------------------------------------------------
	rem errorlevel 4
	echo Insufficient memory to copy files or 
	echo invalid drive or command-line syntax. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo Insufficient memory to copy files or invalid drive or command-line syntax. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:CANCEL_BAT
	REM -------------------------------------------------------------------------------
	rem errorlevel 2
	echo You pressed CTRL+C to end the copy operation. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo You pressed CTRL+C to end the copy operation. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:ISQLW_FAILURE
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.>> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:EXIT_BATCH
	REM -------------------------------------------------------------------------------
	echo ------ END -- MergeSPs.bat ------ 
	REM -------------------------------------------------------------------------------
	echo ------ END -- MergeSPs.bat ------ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	date /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
	time /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 

endlocal
REM End batch.
