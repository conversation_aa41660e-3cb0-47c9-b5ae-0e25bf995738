@echo off
set prompt=()
setlocal
cls

echo Created Jan. 31 2004 by Annalaksh<PERSON> Stocksdale.
echo Updated Nov. 22 2004 by Annalakshmi Stocksdale.
echo -------------------------------------------------------------------------------
echo This script is meant for calling the SSA DR 4.x database creation scripts.
echo Before using this script, please update the following variables:
echo 	SS_DIR
echo 	DB_NAME
echo 	CREATEDB_PATH 
echo to reflect your local settings.
echo NOTE: Be sure to update the path in the sql script file - "InsertData_Defaults.sql" - as well.
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Questions/Comments, please feel free to write to
echo 	<PERSON><PERSON>sh<PERSON>.<EMAIL>
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Required Software: 
echo 	Microsoft SQL Server 2000 server with Query Analyser
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo IMPORTANT! Please ensure that the target machine has:
echo 	(1) Microsoft Visual SourceSafe 6.0 installed and configured with a valid working directory
echo 	(2) the latest versions from SourceSafe of the SSA DR SQL scripts 
echo 		meant for the creation of tables, stored procedures and triggers
echo 	(3) Microsoft SQL Server (2000) and SQL Server agent services running
echo -------------------------------------------------------------------------------

SET SS_WORKING_DIR=D:\SSA_DR_SS\
SET SCRIPTS_DIR=EXceedAIMProject\Database\Scripts\CreateDBs\

SET CREATEDB_PATH=D:\SSA_DR_SS\DBFiles\

SET DB_DEMO=Local.InsertData_Demo.SQL
SET LG_DEMO=Local.InsertData_Demo.LOG

SET LOCAL_SCRIPTS_LOG=DRLocalScripts.LOG

echo -------------------------------------------------------------------------------
echo Start batch "Local.DBWIZARD"
echo -------------------------------------------------------------------------------
	echo Deleting existing logs...
	if exist %SS_WORKING_DIR%%SCRIPTS_DIR%ALL_*.sql (del %SS_WORKING_DIR%%SCRIPTS_DIR%ALL_*.sql)

	echo Deleting existing ALL_* files...
	if exist %SS_WORKING_DIR%%SCRIPTS_DIR%ALL_*.sql (del %SS_WORKING_DIR%%SCRIPTS_DIR%ALL_*.sql)

echo -------------------------------------------------------------------------------
	set DB_NAME=SSA_DR_DEFAULT
	set SKIPMERGE=F

	echo Creating database %DB_NAME%...
	Call CreateDB.bat %SS_WORKING_DIR% %DB_NAME% %CREATEDB_PATH% %SKIPMERGE%
echo -------------------------------------------------------------------------------

echo -------------------------------------------------------------------------------
	set DB_NAME=SSA_DR_DEMO
	set SKIPMERGE=T

	echo Creating database %DB_NAME%...
	Call CreateDB.bat %SS_WORKING_DIR% %DB_NAME% %CREATEDB_PATH% %SKIPMERGE%

	echo Inserting demo data for the %DB_NAME% database...
	isqlw -d %DB_NAME% -E -i %SS_WORKING_DIR%%SCRIPTS_DIR%%DB_DEMO% -o %SS_WORKING_DIR%%SCRIPTS_DIR%%LG_DEMO%
	echo Exec script - %DB_DEMO% for database %DB_NAME% - status: >> %SS_WORKING_DIR%%SCRIPTS_DIR%%LOCAL_SCRIPTS_LOG%
	copy %SS_WORKING_DIR%%SCRIPTS_DIR%%LOCAL_SCRIPTS_LOG% + %SS_WORKING_DIR%%SCRIPTS_DIR%%LG_DEMO% >> TEMP_Copy.log
	del %SS_WORKING_DIR%%SCRIPTS_DIR%%LG_DEMO% 
echo -------------------------------------------------------------------------------

echo -------------------------------------------------------------------------------
	del TEMP*.log 
echo End batch "Local.DBWIZARD".
echo -------------------------------------------------------------------------------

endlocal
set prompt=
