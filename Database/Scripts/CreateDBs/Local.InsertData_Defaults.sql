/*
-- Created Sept. 08, 2003. <PERSON><PERSON><PERSON><PERSON> Stocksdale
--
-- Last updated: Nov. 22, 2004
--
*/
SET NOCOUNT ON
SET XACT_ABORT ON

DECLARE @AbsPath as nvarchar(1000)
DECLARE @TargetTable as nvarchar(1000)
DECLARE @DataFile as nvarchar(1000)
DECLARE @Cmd as nvarchar(1000)
DECLARE @Unicode_Option as nvarchar(255)
DECLARE @ProcCount as tinyint

DECLARE @Resample char(8)
DECLARE @RC int
DECLARE @PrnLine nvarchar(4000)
DECLARE @WithOptions nvarchar(4000)
DECLARE @FirstFiscalYear as int
DECLARE @NbrFiscalYears as int
DECLARE @FYStartDate as datetime
DECLARE @StartTime as datetime
DECLARE @EndTime as datetime
DECLARE @Error as int
DECLARE @RowCount as bigint

SET @StartTime = GETDATE()

SET @ProcCount = 0
SET @AbsPath = 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Defaults\'
-- SET @AbsPath = '<ABSPATHVAR>'
SET @Unicode_Option = 'char'
SET @WithOptions = ' WITH (BATCHSIZE = 1000, DATAFILETYPE = ' + char(39) + @Unicode_Option + char(39) + ', KEEPIDENTITY, KEEPNULLS)'

BEGIN TRANSACTION
	SET @TargetTable = 'SysCtrl'
	SET @DataFile = @AbsPath + 'SysCtrl.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM SysCtrl
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMUsers'
	SET @DataFile = @AbsPath + 'AIMUsers.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMUsers
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMRoles'
	SET @DataFile = @AbsPath + 'AIMRoles.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMRoles
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMTables'
	SET @DataFile = @AbsPath + 'AIMTables.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMTables
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMFunctions'
	SET @DataFile = @AbsPath + 'AIMFunctions.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMFunctions
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMMethods'
	SET @DataFile = @AbsPath + 'AIMMethods.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMMethods
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'Item'
	SET @DataFile = @AbsPath + 'Item.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'DELETE FROM ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM Item
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'ItStatus'
	SET @DataFile = @AbsPath + 'ItStatus.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'DELETE FROM ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM ItStatus
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMUOM'
	SET @DataFile = @AbsPath + 'AIMUOM.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMUOM
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMVelCodePcts'
	SET @DataFile = @AbsPath + 'AIMVelCodePcts.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMVelCodePcts
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMDD'
	SET @DataFile = @AbsPath + 'AIMDD.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMDD
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'DxElements'
	SET @DataFile = @AbsPath + 'DxElements.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM DxElements
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'DxSets'
	SET @DataFile = @AbsPath + 'DxSets.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM DxSets
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMLanguage'
	SET @DataFile = @AbsPath + 'AIMLanguage.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMLanguage
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'ItDefaults'
	SET @DataFile = @AbsPath + 'ItDefaults.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM ItDefaults
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMOptions'
	SET @DataFile = @AbsPath + 'AIMOptions.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMOptions
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'RevCycles'
	SET @DataFile = @AbsPath + 'RevCycles.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM RevCycles
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMSeasons'
	SET @DataFile = @AbsPath + 'AIMSeasons.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMSeasons
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMWorkingDays'
	SET @DataFile = @AbsPath + 'AIMWorkingDays.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'DELETE FROM ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMWorkingDays
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMHelp'
	SET @DataFile = @AbsPath + 'AIMHelp.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
-- 		SELECT @Cmd = 'DELETE FROM ' + @TargetTable
-- 		EXECUTE (@Cmd)
-- 		SELECT @Error = @@ERROR
-- 		IF @Error <> 0 
-- 		BEGIN
-- 			ROLLBACK TRANSACTION
-- 			RETURN
-- 		END
-- 		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
-- 		EXECUTE (@Cmd)
INSERT INTO AIMHELP (FormName, ControlName, FilePath) VALUES ('', '', '\WebHelp\SSA_DR_Help.htm')
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMHelp
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

COMMIT TRANSACTION

SET @TargetTable = 'AIMYears'
SET @DataFile = @AbsPath + 'AIMYears.TXT'
SET @ProcCount = @ProcCount + 1
PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	
	SELECT @NbrFiscalYears = COUNT(*)
		FROM AIMYears
	SELECT TOP 1 @FirstFiscalYear = AIMY.FiscalYear,
		@FYStartDate = AIMY.FYStartDate
	FROM AIMYears AIMY

	-- call SSAAIMCalendar init here -- this will insert into AIMYears and AIMDays
	TRUNCATE TABLE AIMYears

	PRINT 'Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: ' + CONVERT(NVARCHAR(50), @FYStartDate)
	PRINT '	and FirstFiscalYear: ' + CONVERT(NVARCHAR(50), @FirstFiscalYear)
	PRINT '	and NbrFiscalYears: ' + CONVERT(NVARCHAR(50), @NbrFiscalYears)
	EXEC @RC = AIM_Calendar_Init_Sp @FirstFiscalYear, @FYStartDate, @NbrFiscalYears
	PRINT 'Stored Procedure: AIM_Calendar_Init_Sp'
	SELECT @PrnLine = '	Return Code = ' + CONVERT(nvarchar, @RC)
	PRINT @PrnLine

	SELECT @RowCount = COUNT(*) FROM AIMYears
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SELECT @RowCount = COUNT(*) FROM AIMDays
	PRINT 'Rows inserted into AIMDays: ' + CONVERT(nvarchar, @RowCount)

BEGIN TRANSACTION
	-- Process the unicode files
	SET @Unicode_Option = 'widechar'
	SET @WithOptions = ' WITH (BATCHSIZE = 1000, DATAFILETYPE = ' + char(39) + @Unicode_Option + char(39) + ', KEEPIDENTITY, KEEPNULLS)'
	
	SET @TargetTable = 'AIMTranslation'
	SET @DataFile = @AbsPath + 'AIMTranslation.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMTranslation
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMCodeLookup'
	SET @DataFile = @AbsPath + 'AIMCodeLookup.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
		SELECT @Cmd = 'TRUNCATE TABLE ' + @TargetTable
		IF @@ERROR <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		IF @@ERROR <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions 
		EXECUTE (@Cmd)
		SELECT @Error = @@ERROR
		IF @Error <> 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN
		END
		SELECT @RowCount = COUNT(*) FROM AIMCodeLookup
		PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)


COMMIT TRANSACTION

PRINT 'Updating statistics...'
	SELECT @Resample = 'Resample'
	EXEC @RC = [dbo].[sp_updatestats] @Resample
	PRINT 'Stored Procedure: dbo.sp_updatestats'
	SELECT @PrnLine = '	Return Code = ' + CONVERT(nvarchar, @RC)
	PRINT @PrnLine

CHECKPOINT
PRINT 'Clearing buffers with "DBCC DROPCLEANBUFFERS"...'
DBCC DROPCLEANBUFFERS
PRINT 'Freeing cache with "DBCC FREEPROCCACHE"...'
DBCC FREEPROCCACHE

SET @EndTime = GETDATE()
PRINT 'Total time taken (in minutes) to complete data inserts: ' + CONVERT(nvarchar, (DATEDIFF(mi, @StartTime, @EndTime)) )
PRINT 'Data defaults completed.'

SET XACT_ABORT OFF
SET NOCOUNT OFF
