@echo off
REM Created Sept. 07 2003 by Annalakshmi Stocksdale.
REM -------------------------------------------------------------------------------
echo 	This script is intended to be called from "CreateDB.bat". 
echo 	When called, it will merge all SSA DR trigger scripts, 
echo 	with primary ones  appearing first, 
echo 	followed by the rest in alphabetical order.
REM ~~~~~~~~~~~~~~~~~~
REM Required Software: 
REM 	Microsoft SQL Server 2000 server with Query Analyser
REM ~~~~~~~~~~
REM -------------------------------------------------------------------------------

setlocal

set SS_DIR=%1
set OUTPUT_DIR=\EXceedAIMProject\Database\Scripts\CreateDBs\
set LOG_FILE=Batch_CreateDB.LOG

set CREATETRG_SQL=TEMP_DRTriggers.SQL
set CREATETRG_LOG=TEMP_CreateTRG.LOG

:MergeTriggers
	REM -------------------------------------------------------------------------------
	echo 	Merging *tr*.SQL...
	REM -------------------------------------------------------------------------------
	echo ----- 2/4. Merge tr*.SQL ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if exist %SS_DIR%%OUTPUT_DIR%%CREATETRG_SQL% (del %SS_DIR%%OUTPUT_DIR%%CREATETRG_SQL%)

	REM ************************************************************
	REM -- Get the dependencies established, first. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	REM Not required yet.

	REM ************************************************************
	REM -- Move above files to a temp name while creating the composite DRTriggers script. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	REM Not required yet.

	REM ************************************************************
	REM -- Now, copy all the create-procedures into the composite DRTriggers.SQL script. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	copy /V /-Y "..\..\Triggers\*.SQL" %SS_DIR%%OUTPUT_DIR%%CREATETRG_SQL% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Move the dependencies back to their original name. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	REM Not required yet.

	REM ************************************************************
	REM Delete the temporary file. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	REM Not required yet.
GOTO EXIT_BATCH

:LOW_MEMORY
	REM -------------------------------------------------------------------------------
	rem errorlevel 4
	echo Insufficient memory to copy files or 
	echo invalid drive or command-line syntax. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo Insufficient memory to copy files or invalid drive or command-line syntax. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:CANCEL_BAT
	REM -------------------------------------------------------------------------------
	rem errorlevel 2
	echo You pressed CTRL+C to end the copy operation. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo You pressed CTRL+C to end the copy operation. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:ISQLW_FAILURE
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.>> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:EXIT_BATCH
	REM -------------------------------------------------------------------------------
	echo ------ END -- MergeTRG.bat ------ 
	REM -------------------------------------------------------------------------------
	echo ------ END -- MergeTRG.bat ------ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	date /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
	time /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 

endlocal
REM End batch.
