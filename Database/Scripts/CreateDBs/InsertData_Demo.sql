/*
-- Created Nov. 22, 2004. <PERSON><PERSON><PERSON><PERSON> Stocksdale
--
-- Last updated: Nov. 22, 2004. 
--
*/
SET NOCOUNT ON
SET XACT_ABORT ON

DECLARE @AbsPath as nvarchar(1000)
DECLARE @TargetTable as nvarchar(1000)
DECLARE @DataFile as nvarchar(1000)
DECLARE @Cmd as nvarchar(1000)
DECLARE @Unicode_Option as nvarchar(255)
DECLARE @ProcCount as tinyint

DECLARE @Resample char(8)
DECLARE @RC int
DECLARE @PrnLine nvarchar(4000)
DECLARE @WithOptions nvarchar(4000)
DECLARE @FirstFiscalYear as int
DECLARE @NbrFiscalYears as int
DECLARE @FYStartDate as datetime
DECLARE @StartTime as datetime
DECLARE @EndTime as datetime
DECLARE @RowCount as bigint
DECLARE @Error as int

SET @StartTime = GETDATE()

SET @ProcCount = 0
SET @AbsPath = '<ABSPATHVAR>'
-- SET @AbsPath = 'C:\EXceed_AIM_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\'
SET @Unicode_Option = 'char'
SET @WithOptions = ' WITH (BATCHSIZE = 1000, DATAFILETYPE = ' + char(39) + @Unicode_Option + char(39) + ', KEEPIDENTITY, KEEPNULLS)'

-- These tables are listed in order of file size, ascending.
-- <= 100 Kb
BEGIN TRANSACTION
	SET @TargetTable = 'MDCSummary'
	SET @DataFile = @AbsPath + 'MDCSummary.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM MDCSummary
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'MDCDetail'
	SET @DataFile = @AbsPath + 'MDCDetail.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM MDCDetail
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
	
	SET @TargetTable = 'AIMTransferPolicy'
	SET @DataFile = @AbsPath + 'AIMTransferPolicy.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMTransferPolicy
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
			
	SET @TargetTable = 'AIMLocations'
	SET @DataFile = @AbsPath + 'AIMLocations.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMLocations
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
			
	SET @TargetTable = 'BuyerStatusTest'
	SET @DataFile = @AbsPath + 'BuyerStatusTest.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM BuyerStatusTest
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
	
	SET @TargetTable = 'AIMPromotions'
	SET @DataFile = @AbsPath + 'AIMPromotions.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMPromotions
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
		
	SET @TargetTable = 'AIMUsers'
	SET @DataFile = @AbsPath + 'Demo_AIMUsers.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
 	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMUsers
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	
	SET @TargetTable = 'AIMLeadTime'
	SET @DataFile = @AbsPath + 'AIMLeadTime.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMLeadTime
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'ItemPricing'
	SET @DataFile = @AbsPath + 'ItemPricing.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM ItemPricing
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMClasses'
	SET @DataFile = @AbsPath + 'AIMClasses.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMClasses
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMPo'
	SET @DataFile = @AbsPath + 'AIMPo.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMPo
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'POStatistics'
	SET @DataFile = @AbsPath + 'POStatistics.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM POStatistics
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'ItDefaults'
	SET @DataFile = @AbsPath + 'Demo_ItDefaults.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM ItDefaults
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMOptions'
	SET @DataFile = @AbsPath + 'Demo_AIMOptions.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMOptions
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

COMMIT TRANSACTION

BEGIN TRANSACTION
-- <= 10000 Kb
	SET @TargetTable = 'AIMSeasons'
	SET @DataFile = @AbsPath + 'Demo_AIMSeasons.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMSeasons
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

	SET @TargetTable = 'AIMVendors'
	SET @DataFile = @AbsPath + 'AIMVendors.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM AIMVendors
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
	
	SET @TargetTable = 'PODetail'
	SET @DataFile = @AbsPath + 'PODetail.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM PODetail
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

COMMIT TRANSACTION

BEGIN TRANSACTION
	SET @TargetTable = 'Item'
	SET @DataFile = @AbsPath + 'Demo_Item.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM Item
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)

COMMIT TRANSACTION

BEGIN TRANSACTION
	SET @TargetTable = 'ItemHistory'
	SET @DataFile = @AbsPath + 'ItemHistory.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @RowCount = COUNT(*) FROM ItemHistory
	PRINT 'Rows inserted into ' + @TargetTable + ': ' + CONVERT(nvarchar, @RowCount)
	
COMMIT TRANSACTION

-- stored procedure executions
	SET @TargetTable = 'AIMYears'
	SET @DataFile = @AbsPath + 'Demo_AIMYears.TXT'
	SET @ProcCount = @ProcCount + 1
	PRINT CONVERT(nvarchar, @ProcCount) + ' - ' + @TargetTable
	SELECT @Cmd = 'DELETE FROM ' + @TargetTable
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	SELECT @Cmd = 'BULK INSERT ' + @TargetTable + ' FROM ' + char(39) + @DataFile + char(39) + @WithOptions
	EXECUTE (@Cmd)
	SELECT @Error = @@ERROR
	IF @Error <> 0 
	BEGIN
		ROLLBACK TRANSACTION
		RETURN
	END
	
	SELECT @NbrFiscalYears = COUNT(*)
		FROM AIMYears
	SELECT TOP 1 @FirstFiscalYear = AIMY.FiscalYear,
		@FYStartDate = AIMY.FYStartDate
	FROM AIMYears AIMY

	-- call SSAAIMCalendar init here -- this will insert into AIMYears and AIMDays
	TRUNCATE TABLE AIMYears

	PRINT 'Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: ' + CONVERT(NVARCHAR(50), @FYStartDate)
	PRINT '	and FirstFiscalYear: ' + CONVERT(NVARCHAR(50), @FirstFiscalYear)
	PRINT '	and NbrFiscalYears: ' + CONVERT(NVARCHAR(50), @NbrFiscalYears)
	EXEC @RC = AIM_Calendar_Init_Sp @FirstFiscalYear, @FYStartDate, @NbrFiscalYears
	PRINT 'Stored Procedure: AIM_Calendar_Init_Sp'
	SELECT @PrnLine = '	Return Code = ' + CONVERT(nvarchar, @RC)
	PRINT @PrnLine

	SELECT @RowCount = COUNT(*) FROM AIMYears
	PRINT 'Rows insert into AIMYears: ' + CONVERT(nvarchar, @RowCount)

	SELECT @RowCount = COUNT(*) FROM AIMDays
	PRINT 'Rows insert into AIMDays: ' + CONVERT(nvarchar, @RowCount)

	PRINT 'Updating statistics...'
	SELECT @Resample = 'Resample'
	EXEC @RC = [dbo].[sp_updatestats] @Resample
	PRINT 'Stored Procedure: dbo.sp_updatestats'
	SELECT @PrnLine = '	Return Code = ' + CONVERT(nvarchar, @RC)
	PRINT @PrnLine

CHECKPOINT
PRINT 'Clearing buffers with "DBCC DROPCLEANBUFFERS"...'
DBCC DROPCLEANBUFFERS
PRINT 'Freeing cache with "DBCC FREEPROCCACHE"...'
DBCC FREEPROCCACHE

SET @EndTime = GETDATE()
PRINT 'Total time taken (in minutes) to complete data inserts: ' + CONVERT(nvarchar, (DATEDIFF(mi, @StartTime, @EndTime)) )
PRINT 'Demo data inserts completed.'

SET NOCOUNT OFF
SET XACT_ABORT OFF
