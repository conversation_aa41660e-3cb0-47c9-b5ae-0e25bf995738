@echo off
set prompt=()
setlocal

echo Created Sept. 07 2003 by Annalakshmi Stocksdale.
echo Updated Nov. 22 2004 by Annalakshmi Stocksdale.
echo -------------------------------------------------------------------------------
echo In this batch file, there are four steps:
echo	the first three to gather miscellaneous script files,
echo 	the last for the execution of these.
echo This last step comprises five parts, as described below:
echo 	Part a - Drop and create database {database name}
echo 	Part b - Create SSA DR tables.
echo 	Part c - Create SSA DR stored procedures.
echo 	Part d - Create SSA DR triggers.
echo 	Part e - Populate SSA DR with default data.
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Questions/Comments, please feel free to write to
echo 	<PERSON>laksh<PERSON>.<EMAIL>
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Required Software: 
echo 	Microsoft SQL Server 2000 server with Query Analyser
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo IMPORTANT! Please ensure that the target machine has:
echo 	(1) Microsoft Visual SourceSafe 6.0 installed and configured with a valid working directory
echo 	(2) the latest versions from SourceSafe of the SSA DR SQL scripts 
echo 		meant for the creation of tables, stored procedures and triggers
echo 	(3) Microsoft SQL Server (2000) and SQL Server agent services running
echo -------------------------------------------------------------------------------

set SS_DIR=%1
set DB_NAME=%2
set CREATEDB_PATH=%3
set SKIPMERGE=%4

echo -------------------------------------------------------------------------------
echo Start batch "CREATEDB"
echo This batch file is intended to create the %DB_NAME% database.
echo This batch file accepts three parameters: 
echo 	1 = The absolute pathname of the SourceSafe working directory.
echo		Current value=%1
echo 	2 = The database name to be created (no spaces allowed.)
echo		Current value=%2
echo 	3 = The absolute pathname for the physical location of the database.
echo		Current value=%3
echo 	4 = A T or F value, where F instructs this batch to merge all sql scripts afresh,
echo		while T instructs this batch to use the existing merged files.
echo		Current value=%4
echo -------------------------------------------------------------------------------

set OUTPUT_DIR=\EXceedAIMProject\Database\Scripts\CreateDBs\
set LOG_FILE=Batch_CreateDB.LOG

set CREATEDATA_SQL=\Local.InsertData_Defaults.SQL

set CREATEDB_LOG=\TEMP_CreateDB.LOG
set CREATETAB_LOG=\TEMP_CreateTables.LOG
set CREATESPs_LOG=\TEMP_CreateSPs.LOG
set CREATETRG_LOG=\TEMP_CreateTRG.LOG
set CREATEDATA_LOG=\TEMP_InsertData_Defaults.LOG
	
if %SKIPMERGE% EQU T ( set CREATETAB_SQL=\ALL__DRTables.SQL
	set CREATESPs_SQL=\ALL__DRStoredProcs.SQL
	set CREATETRG_SQL=\ALL__DRTriggers.SQL
	goto ExecScripts
) else ( set CREATETAB_SQL=\TEMP_DRTables.SQL
	set CREATESPs_SQL=\TEMP_DRStoredProcs.SQL
	set CREATETRG_SQL=\TEMP_DRTriggers.SQL)

:START_BATCH
	echo -------------------------------------------------------------------------------
	echo ----- START -- CreateDB %DB_NAME% Script -----
	echo -------------------------------------------------------------------------------
	if exist %SS_DIR%%OUTPUT_DIR%%LOG_FILE% (del %SS_DIR%%OUTPUT_DIR%%LOG_FILE%)

	echo ----- START -- CreateDB %DB_NAME% Script ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	date /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
	time /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 

	del TEMP*.sql >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

:MergeTables
	REM -------------------------------------------------------------------------------
	echo Step 1 of 4. Merging *.TAB... 
	REM -------------------------------------------------------------------------------
	Call MergeTAB.bat %SS_DIR% 
	
:MergeSPs
	REM -------------------------------------------------------------------------------
	echo Step 2 of 4. Merging *sp*.SQL...
	REM -------------------------------------------------------------------------------
	Call MergeSPs.bat %SS_DIR%

:MergeTriggers
	REM -------------------------------------------------------------------------------
	echo Step 3 of 4. Merging *tr*.SQL...
	REM -------------------------------------------------------------------------------
	Call MergeTRG.bat %SS_DIR%

:ExecScripts
	REM -------------------------------------------------------------------------------
	echo Step 4 of 4. Executing scripts to create the database...
	REM -------------------------------------------------------------------------------
	echo ----- 4/4. Start scripts to create the database ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	echo 	4.a. Using MASTER, create database %DB_NAME%.

	REM ************************************************************
	echo 	4.a. Using MASTER, create database %DB_NAME%. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************

	osql -d master -E /Q "IF EXISTS (SELECT name FROM master.dbo.sysdatabases WHERE name = N'%DB_NAME%') BEGIN PRINT 'Dropping database \"%DB_NAME%\".' DROP DATABASE %DB_NAME% END ELSE BEGIN PRINT 'Database %DB_NAME% not found.' END PRINT 'Creating database \"%DB_NAME%\".' CREATE DATABASE %DB_NAME% ON (NAME = N'%DB_NAME%', FILENAME = N'%CREATEDB_PATH%%DB_NAME%.mdf', SIZE = 7, FILEGROWTH = 10%%) LOG ON (NAME = N'%DB_NAME%_Log', FILENAME = N'%CREATEDB_PATH%%DB_NAME%.ldf' , SIZE = 25, FILEGROWTH = 10%%) COLLATE SQL_Latin1_General_CP1_CI_AS" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	osql -d master -E /Q "PRINT 'Setting *dboptions...'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'autoclose', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'bulkcopy', N'true'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'trunc. log', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'torn page detection', N'true'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'read only', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'dbo use', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'single', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'autoshrink', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'ANSI null default', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'recursive triggers', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'ANSI nulls', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'concat null yields null', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'cursor close on commit', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'default to local cursor', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'quoted identifier', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'ANSI warnings', N'false'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'auto create statistics', N'true'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "exec sp_dboption N'%DB_NAME%',N'auto update statistics', N'true'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	osql -d master -E /Q "PRINT 'Database creation complete for database: \"%DB_NAME%\".'" >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	if not errorlevel 0 (GOTO ISQLW_FAILURE
	) else (
		echo ---------- Begin ISQL results --------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		copy %LOG_FILE% + %SS_DIR%%OUTPUT_DIR%%CREATEDB_LOG% >> TEMP_copy.log
		echo ---------- End ISQL results ---------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	)

	echo 	4.b. Using %DB_NAME%, create tables.
	REM ************************************************************
	echo 	4.b. Using %DB_NAME%, create tables. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	isqlw -d %DB_NAME% -E -i %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL% -o %SS_DIR%%OUTPUT_DIR%%CREATETAB_LOG% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if not errorlevel 0 (GOTO ISQLW_FAILURE
	) else (
		echo ---------- Begin ISQL results --------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		copy %LOG_FILE% + %SS_DIR%%OUTPUT_DIR%%CREATETAB_LOG% >> TEMP_copy.log
		echo ---------- End ISQL results ---------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	)

	echo 	4.c. Using %DB_NAME%, create stored procedures.
	REM ************************************************************
	echo 	4.c. Using %DB_NAME%, create stored procedures. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	isqlw -d %DB_NAME% -E -i %SS_DIR%%OUTPUT_DIR%%CREATESPs_SQL% -o %SS_DIR%%OUTPUT_DIR%%CREATESPs_LOG% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if not errorlevel 0 (GOTO ISQLW_FAILURE
	) else (
		echo ---------- Begin ISQL results --------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		copy %LOG_FILE% + %SS_DIR%%OUTPUT_DIR%%CREATESPs_LOG% >> TEMP_copy.log
		echo ---------- End ISQL results ---------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	)

	echo 	4.d. Using %DB_NAME%, create triggers.
	REM ************************************************************
	echo 	4.d. Using %DB_NAME%, create triggers. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	isqlw -d %DB_NAME% -E -i %SS_DIR%%OUTPUT_DIR%%CREATETRG_SQL% -o %SS_DIR%%OUTPUT_DIR%%CREATETRG_LOG% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if not errorlevel 0 (GOTO ISQLW_FAILURE
	) else (
		echo ---------- Begin ISQL results --------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		copy %LOG_FILE% + %SS_DIR%%OUTPUT_DIR%%CREATETRG_LOG% >> TEMP_copy.log
		echo ---------- End ISQL results ---------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	)

	echo 	4.e. Populate %DB_NAME% with default data.
	REM ************************************************************
	echo 	4.e. Populate %DB_NAME% with default data. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	isqlw -d %DB_NAME% -E -i %SS_DIR%%OUTPUT_DIR%%CREATEDATA_SQL% -o %SS_DIR%%OUTPUT_DIR%%CREATEDATA_LOG% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if not errorlevel 0 (GOTO ISQLW_FAILURE
	) else (
		echo Default data inserted into DB. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		echo ---------- Begin ISQL results --------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		copy %LOG_FILE% + %SS_DIR%%OUTPUT_DIR%%CREATEDATA_LOG% >> TEMP_copy.log
		echo ---------- End ISQL results ---------- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	)

GOTO EXIT_BATCH

:LOW_MEMORY
	REM -------------------------------------------------------------------------------
	rem errorlevel 4
	echo Insufficient memory to copy files or 
	echo invalid drive or command-line syntax. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo Insufficient memory to copy files or invalid drive or command-line syntax. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:CANCEL_BAT
	REM -------------------------------------------------------------------------------
	rem errorlevel 2
	echo You pressed CTRL+C to end the copy operation. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo You pressed CTRL+C to end the copy operation. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:ISQLW_FAILURE
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.>> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:EXIT_BATCH
	REM -------------------------------------------------------------------------------
	echo ------ END -- DR Database Creation Scripts for %DB_NAME% ------
	REM -------------------------------------------------------------------------------
	echo ------ END -- DR Database Creation Scripts for %DB_NAME% ------ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	date /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
	time /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 

	if %SKIPMERGE% NEQ T ( del ALL__*.sql >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
		ren TEMP*.sql ALL_*.sql >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% )
	del TEMP*.log >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	
	echo -------------------------------------------------------------------------------
	echo Results logged in %LOG_FILE%.
	echo ! Don't forget to check the files back into SourceSafe.
	echo -------------------------------------------------------------------------------

endlocal
echo End batch "CREATEDB"
set prompt=

