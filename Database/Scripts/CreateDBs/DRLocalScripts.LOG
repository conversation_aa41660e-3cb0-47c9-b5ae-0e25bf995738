Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLoc<PERSON>
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMOptions
Updating dbo.AIMLocations
Updating dbo.SSA_DR_Migration
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMVelCodePcts
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AllocDefaults
Updating dbo.AllocDefaultsSource
Updating dbo.AIMCompanionItem
Updating dbo.Item
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMCompanionItemDetail
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMPo
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AllocItemSubstitutes
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMProductionConstraint
Updating dbo.ItemHistory
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.UserElement
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.DXSets
Updating dbo.AIMSeasons
Updating dbo.UserElementDetail
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.ForecastRepository_Log
Updating dbo.AIMClasses
Updating dbo.ItDefaults
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.ItStatus
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.KitBOMSummary
Updating dbo.AIMDxDD
Updating dbo.MDCDetail
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AIMDxIS
Updating dbo.MDCSummary
Updating dbo.AIMDxKB
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.PODetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMSourcingHierarchy
Updating dbo.AIMFcstMaster
Updating dbo.AIMTables
Updating dbo.AIMFcstSetup
Updating dbo.AIMFunctions
Updating dbo.POStatistics
Updating dbo.AIMVendors
Updating dbo.AIMTransferPolicy
Updating dbo.ProdConstraintTemp
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.RevCycles
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 7
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMOptions
Updating dbo.AIMLocations
Updating dbo.SSA_DR_Migration
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMVelCodePcts
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AllocDefaults
Updating dbo.AllocDefaultsSource
Updating dbo.AIMCompanionItem
Updating dbo.Item
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMCompanionItemDetail
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMPo
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AllocItemSubstitutes
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMProductionConstraint
Updating dbo.ItemHistory
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.UserElement
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.DXSets
Updating dbo.AIMSeasons
Updating dbo.UserElementDetail
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.ForecastRepository_Log
Updating dbo.AIMClasses
Updating dbo.ItDefaults
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.ItStatus
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.KitBOMSummary
Updating dbo.AIMDxDD
Updating dbo.MDCDetail
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AIMDxIS
Updating dbo.MDCSummary
Updating dbo.AIMDxKB
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.PODetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMSourcingHierarchy
Updating dbo.AIMFcstMaster
Updating dbo.AIMTables
Updating dbo.AIMFcstSetup
Updating dbo.AIMFunctions
Updating dbo.POStatistics
Updating dbo.AIMVendors
Updating dbo.AIMTransferPolicy
Updating dbo.ProdConstraintTemp
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.RevCycles
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 7
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMOptions
Updating dbo.AIMLocations
Updating dbo.SSA_DR_Migration
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMVelCodePcts
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AllocDefaults
Updating dbo.AllocDefaultsSource
Updating dbo.AIMCompanionItem
Updating dbo.Item
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMCompanionItemDetail
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMPo
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AllocItemSubstitutes
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMProductionConstraint
Updating dbo.ItemHistory
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.UserElement
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.DXSets
Updating dbo.AIMSeasons
Updating dbo.UserElementDetail
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.ForecastRepository_Log
Updating dbo.AIMClasses
Updating dbo.ItDefaults
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.ItStatus
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.KitBOMSummary
Updating dbo.AIMDxDD
Updating dbo.MDCDetail
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AIMDxIS
Updating dbo.MDCSummary
Updating dbo.AIMDxKB
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.PODetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMSourcingHierarchy
Updating dbo.AIMFcstMaster
Updating dbo.AIMTables
Updating dbo.AIMFcstSetup
Updating dbo.AIMFunctions
Updating dbo.POStatistics
Updating dbo.AIMVendors
Updating dbo.AIMTransferPolicy
Updating dbo.ProdConstraintTemp
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.RevCycles
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 6
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMOptions
Updating dbo.AIMLocations
Updating dbo.SSA_DR_Migration
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMVelCodePcts
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AllocDefaults
Updating dbo.AllocDefaultsSource
Updating dbo.AIMCompanionItem
Updating dbo.Item
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMCompanionItemDetail
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMPo
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AllocItemSubstitutes
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMProductionConstraint
Updating dbo.ItemHistory
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.UserElement
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.DXSets
Updating dbo.AIMSeasons
Updating dbo.UserElementDetail
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.ForecastRepository_Log
Updating dbo.AIMClasses
Updating dbo.ItDefaults
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.ItStatus
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.KitBOMSummary
Updating dbo.AIMDxDD
Updating dbo.MDCDetail
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AIMDxIS
Updating dbo.MDCSummary
Updating dbo.AIMDxKB
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.PODetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMSourcingHierarchy
Updating dbo.AIMFcstMaster
Updating dbo.AIMTables
Updating dbo.AIMFcstSetup
Updating dbo.AIMFunctions
Updating dbo.POStatistics
Updating dbo.AIMVendors
Updating dbo.AIMTransferPolicy
Updating dbo.ProdConstraintTemp
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.RevCycles
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 8
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
[ M i c r o s o f t ] [ O D B C   S Q L   S e r v e r   D r i v e r ] [ S h a r e d   M e m o r y ] C o n n e c t i o n O p e n   ( C o n n e c t ( ) ) . Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMOptions
Updating dbo.AIMLocations
Updating dbo.SSA_DR_Migration
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMVelCodePcts
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AllocDefaults
Updating dbo.AllocDefaultsSource
Updating dbo.AIMCompanionItem
Updating dbo.Item
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMCompanionItemDetail
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMPo
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AllocItemSubstitutes
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMProductionConstraint
Updating dbo.ItemHistory
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.UserElement
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.DXSets
Updating dbo.AIMSeasons
Updating dbo.UserElementDetail
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.ForecastRepository_Log
Updating dbo.AIMClasses
Updating dbo.ItDefaults
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.ItStatus
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.KitBOMSummary
Updating dbo.AIMDxDD
Updating dbo.MDCDetail
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AIMDxIS
Updating dbo.MDCSummary
Updating dbo.AIMDxKB
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.PODetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMSourcingHierarchy
Updating dbo.AIMFcstMaster
Updating dbo.AIMTables
Updating dbo.AIMFcstSetup
Updating dbo.AIMFunctions
Updating dbo.POStatistics
Updating dbo.AIMVendors
Updating dbo.AIMTransferPolicy
Updating dbo.ProdConstraintTemp
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.RevCycles
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 6
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMOptions
Updating dbo.AIMLocations
Updating dbo.SSA_DR_Migration
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMVelCodePcts
Updating dbo.SysCtrl
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AllocDefaults
Updating dbo.AllocDefaultsSource
Updating dbo.AIMCompanionItem
Updating dbo.Item
Updating dbo.AllocScratch_AllocResults
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMCompanionItemDetail
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMPo
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AllocItemSubstitutes
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMProductionConstraint
Updating dbo.ItemHistory
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMPromotions
Updating dbo.BuyerStatusTest
Updating dbo.AIMRoles
Updating dbo.UserElement
Updating dbo.DXElements
Updating dbo.AIMSd
Updating dbo.DXSets
Updating dbo.AIMSeasons
Updating dbo.UserElementDetail
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.ForecastRepository_Log
Updating dbo.AIMClasses
Updating dbo.ItDefaults
Updating dbo.AIMCodeLookUp
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMDays
Updating dbo.AIMDD
Updating dbo.ItStatus
Updating dbo.AIMDestinationProfile
Updating dbo.AIMDxAS
Updating dbo.KitBOMSummary
Updating dbo.AIMDxDD
Updating dbo.MDCDetail
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AIMDxIS
Updating dbo.MDCSummary
Updating dbo.AIMDxKB
Updating dbo.AIMDxLC
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.PODetail
Updating dbo.AIMFcstAccess
Updating dbo.AIMSourcingHierarchy
Updating dbo.AIMFcstMaster
Updating dbo.AIMTables
Updating dbo.AIMFcstSetup
Updating dbo.AIMFunctions
Updating dbo.POStatistics
Updating dbo.AIMVendors
Updating dbo.AIMTransferPolicy
Updating dbo.ProdConstraintTemp
Updating dbo.AIMTranslation
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.RevCycles
Updating dbo.AIMUOM
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMUsers
Updating dbo.AIMMethods
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 10
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.ProdConstraintTemp
Updating dbo.AIMCompanionItemDetail
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AIMProductionConstraint
Updating dbo.RevCycles
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AllocDefaults
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxDD
Updating dbo.AIMDxFA
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SysCtrl
Updating dbo.AIMFcstAccess
Updating dbo.BuyerStatusTest
Updating dbo.AIMFcstMaster
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.UserElement
Updating dbo.ForecastRepository_Log
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 9
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.ProdConstraintTemp
Updating dbo.AIMCompanionItemDetail
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AIMProductionConstraint
Updating dbo.RevCycles
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AllocDefaults
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxDD
Updating dbo.AIMDxFA
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SysCtrl
Updating dbo.AIMFcstAccess
Updating dbo.BuyerStatusTest
Updating dbo.AIMFcstMaster
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.UserElement
Updating dbo.ForecastRepository_Log
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 8
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Rows inserted into PODetail: 31258
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.ProdConstraintTemp
Updating dbo.AIMCompanionItemDetail
Updating dbo.AIMWorkingDays
Updating dbo.AIMYears
Updating dbo.AIMProductionConstraint
Updating dbo.RevCycles
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AllocDefaults
Updating dbo.AIMProductionConstraintDetail
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxDD
Updating dbo.AIMDxFA
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_SourceInventory
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxLT
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.AIMDxSL
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SysCtrl
Updating dbo.AIMFcstAccess
Updating dbo.BuyerStatusTest
Updating dbo.AIMFcstMaster
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.UserElement
Updating dbo.ForecastRepository_Log
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 6
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Server: Msg 4863, Level 16, State 1, Line 1
Bulk insert data conversion error (truncation) for row 7333, column 10 (ItDesc).
Rows inserted into PODetail: 31257
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.AIMCompanionItemDetail
Updating dbo.ProdConstraintTemp
Updating dbo.AIMWorkingDays
Updating dbo.AIMProductionConstraint
Updating dbo.AIMYears
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMProductionConstraintDetail
Updating dbo.RevCycles
Updating dbo.AllocDefaults
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AIMDxDD
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AllocScratch_SourceInventory
Updating dbo.AIMDxLT
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMFcstAccess
Updating dbo.SysCtrl
Updating dbo.AIMFcstMaster
Updating dbo.BuyerStatusTest
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.ForecastRepository_Log
Updating dbo.UserElement
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 6
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Server: Msg 4863, Level 16, State 1, Line 1
Bulk insert data conversion error (truncation) for row 7333, column 10 (ItDesc).
Rows inserted into PODetail: 31257
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.AIMCompanionItemDetail
Updating dbo.ProdConstraintTemp
Updating dbo.AIMWorkingDays
Updating dbo.AIMProductionConstraint
Updating dbo.AIMYears
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMProductionConstraintDetail
Updating dbo.RevCycles
Updating dbo.AllocDefaults
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AIMDxDD
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AllocScratch_SourceInventory
Updating dbo.AIMDxLT
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMFcstAccess
Updating dbo.SysCtrl
Updating dbo.AIMFcstMaster
Updating dbo.BuyerStatusTest
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.ForecastRepository_Log
Updating dbo.UserElement
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 6
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Server: Msg 4863, Level 16, State 1, Line 1
Bulk insert data conversion error (truncation) for row 7333, column 10 (ItDesc).
Rows inserted into PODetail: 31257
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.AIMCompanionItemDetail
Updating dbo.ProdConstraintTemp
Updating dbo.AIMWorkingDays
Updating dbo.AIMProductionConstraint
Updating dbo.AIMYears
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMProductionConstraintDetail
Updating dbo.RevCycles
Updating dbo.AllocDefaults
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AIMDxDD
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AllocScratch_SourceInventory
Updating dbo.AIMDxLT
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMFcstAccess
Updating dbo.SysCtrl
Updating dbo.AIMFcstMaster
Updating dbo.BuyerStatusTest
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.ForecastRepository_Log
Updating dbo.UserElement
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 8
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Rows inserted into AIMUsers: 14
8 - AIMLeadTime
Rows inserted into AIMLeadTime: 100
9 - ItemPricing
Rows inserted into ItemPricing: 112
10 - AIMClasses
Rows inserted into AIMClasses: 514
11 - AIMPo
Rows inserted into AIMPo: 535
12 - POStatistics
Rows inserted into POStatistics: 1078
13 - ItDefaults
Rows inserted into ItDefaults: 47
14 - AIMOptions
Rows inserted into AIMOptions: 6
15 - AIMSeasons
Rows inserted into AIMSeasons: 636
16 - AIMVendors
<<<CREATED TRIGGER trAlternateSourceDel >>>
Rows inserted into AIMVendors: 2041
17 - PODetail
Server: Msg 4863, Level 16, State 1, Line 1
Bulk insert data conversion error (truncation) for row 7333, column 10 (ItDesc).
Rows inserted into PODetail: 31257
18 - Item
Rows inserted into Item: 44900
19 - ItemHistory
Rows inserted into ItemHistory: 116357
20 - AIMYears
Calling SSA_DR_SAMPLE.dbo.AIM_Calendar_Init_Sp with FYStartDate: Jul  1 2000 12:00AM
	and FirstFiscalYear: 2000
	and NbrFiscalYears: 7
Stored Procedure: AIM_Calendar_Init_Sp
	Return Code = 0
Rows insert into AIMYears: 7
Rows insert into AIMDays: 2556
Updating statistics...
Updating dbo.AIMCodeLookUp
Updating dbo.AIMCompanionItem
Updating dbo.AIMPo
Updating dbo.POStatistics
Updating dbo.AIMCompanionItemDetail
Updating dbo.ProdConstraintTemp
Updating dbo.AIMWorkingDays
Updating dbo.AIMProductionConstraint
Updating dbo.AIMYears
Updating dbo.AIMDataExchangeCtrl
Updating dbo.AIMProductionConstraintDetail
Updating dbo.RevCycles
Updating dbo.AllocDefaults
Updating dbo.AIMDays
Updating dbo.AIMPromotions
Updating dbo.AIMDD
Updating dbo.AIMRoles
Updating dbo.AIMDestinationProfile
Updating dbo.AllocDefaultsSource
Updating dbo.AIMSd
Updating dbo.AIMSeasons
Updating dbo.AIMDxAS
Updating dbo.AIMDxDD
Updating dbo.AllocItemSubstitutes
Updating dbo.AIMDxFA
Updating dbo.AIMDxHS
Updating dbo.AllocScratch_AllocResults
Updating dbo.AIMDxIS
Updating dbo.AllocScratch_ItemDemand
Updating dbo.AIMDxKB
Updating dbo.AllocScratch_Req_Pri
Updating dbo.AllocScratch_Req_Qty
Updating dbo.AIMDxLC
Updating dbo.AllocScratch_Req_Ratio
Updating dbo.AllocScratch_SourceInventory
Updating dbo.AIMDxLT
Updating dbo.ALTERNATE_SOURCE
Updating dbo.AIMDxRO
Updating dbo.AIMDxRP
Updating dbo.AIMDxRS
Updating dbo.AIMDxSd
Updating dbo.SSA_DR_Migration
Updating dbo.AIMDxSL
Updating dbo.AIMDxSS
Updating dbo.AIMDxVn
Updating dbo.SSA_DR_Migration_Log
Updating dbo.AIMFcstAccess
Updating dbo.SysCtrl
Updating dbo.AIMFcstMaster
Updating dbo.BuyerStatusTest
Updating dbo.DXElements
Updating dbo.DXSets
Updating dbo.AIMFcstSetup
Updating dbo.ForecastFilterCriteria
Updating dbo.ForecastRepository
Updating dbo.ForecastRepositoryArch_Log
Updating dbo.ForecastRepositoryDetail
Updating dbo.AIMFunctions
Updating dbo.AIMHelp
Updating dbo.AIMLanguage
Updating dbo.AIMLeadTime
Updating dbo.AIMLiterals
Updating dbo.AIMLocations
Updating dbo.ForecastRepository_Log
Updating dbo.UserElement
Updating dbo.ItDefaults
Updating dbo.UserElementDetail
Updating dbo.AIMSourcingHierarchy
Updating dbo.ItemHistory
Updating dbo.Item
Updating dbo.AIMTables
Updating dbo.AIMTransferPolicy
Updating dbo.AIMTranslation
Updating dbo.AIMUOM
Updating dbo.AIMUsers
Updating dbo.AIMMethods
Updating dbo.AIMOptions
Updating dbo.ItemHistoryAdjLog
Updating dbo.ItemKitBOM
Updating dbo.ItemPricing
Updating dbo.ItStatus
Updating dbo.KitBOMSummary
Updating dbo.MDCDetail
Updating dbo.AIMVelCodePcts
Updating dbo.MDCSummary
Updating dbo.AIMVendors
Updating dbo.AIMAO
Updating dbo.AIMAODetail
Updating dbo.PODetail
Updating dbo.AIMAO_OrderInfo
Updating dbo.AIMClasses
 
 
Statistics for all tables have been updated.
Stored Procedure: dbo.sp_updatestats
	Return Code = 0
Clearing buffers with "DBCC DROPCLEANBUFFERS"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Freeing cache with "DBCC FREEPROCCACHE"...
DBCC execution completed. If DBCC printed error messages, contact your system administrator.
Total time taken (in minutes) to complete data inserts: 6
Demo data inserts completed.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
Exec script - Local.InsertData_Demo.SQL for database SSA_DR_DEMO - status: 
1 - MDCSummary
Rows inserted into MDCSummary: 1
2 - MDCDetail
Rows inserted into MDCDetail: 3
3 - AIMTransferPolicy
Rows inserted into AIMTransferPolicy: 6
4 - AIMLocations
Rows inserted into AIMLocations: 6
5 - BuyerStatusTest
Rows inserted into BuyerStatusTest: 16
6 - AIMPromotions
Rows inserted into AIMPromotions: 2
7 - AIMUsers
Server: Msg 4860, Level 16, State 1, Line 1
Could not bulk insert. File 'D:\SSA_DR_SS\EXceedAIMProject\Database\Scripts\CreateDBs\DataFiles_Demo\AIMUsers.TXT' does not exist.
