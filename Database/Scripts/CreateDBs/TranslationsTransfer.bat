@echo off
set prompt=()
setlocal
cls

echo Created Nov. 18 2004 by Annalakshmi Stocksdale.
echo Updated Nov. 22 2004 by Annalakshmi Stocksdale.
echo -------------------------------------------------------------------------------
echo This script is meant for one-time use in copying SSA DR's demo data to flat files.
echo Before using this script, please update the following variables:
echo 	SS_WORKING_DIR
echo 	SCRIPTS_DIR
echo 	DATA_DIR
echo 	LOG_FILE
echo to reflect your local settings.
echo NOTE: Be sure to update the path in the sql script file - "InsertData_Demo.sql" - as well.
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Questions/Comments, please feel free to write to
echo 	<PERSON><PERSON><PERSON><PERSON>.<EMAIL>
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Required Software: 
echo 	Microsoft SQL Server 2000 server with Query Analyser
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo IMPORTANT! Please ensure that the target machine has:
echo 	(1) Microsoft Visual SourceSafe 6.0 installed and configured with a valid working directory
echo 	(2) the latest versions from SourceSafe of the SSA DR SQL scripts 
echo 		meant for the creation of tables, stored procedures and triggers
echo 	(3) Microsoft SQL Server (2000) and SQL Server agent services running
echo -------------------------------------------------------------------------------

SET SS_WORKING_DIR=D:\SSA_DR_SS\
SET SCRIPTS_DIR=EXceedAIMProject\Database\Scripts\CreateDBs\
SET DATA_DIR=DataFiles_Demo\
SET SOURCEDB=SSA_AIM_DEMO
SET SOURCESERVER=astocksdalelap
SET LOG_FILE=DemoDataTransfer.RPT

echo -------------------------------------------------------------------------------
echo Starting the bcp utility for the AIMTranslation table,
echo assuming that the updated strings from the translation spreadsheet* 
echo have been moved into the source database.
echo *Translation Spreadsheet = D:\SSA_DR\EXceedAIMProject\Documentation\Support Documents\Translations\SSA_DR_Translations_BaseApp.xls

bcp "SELECT LangID, ResourceID, ResourceString FROM %SOURCEDB%dbo.AIMTranslation" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%SSA_DR_Migration_Log.txt -w -k -E -S %SOURCESERVER% -T >> %LOG_FILE%


echo Finished with running the bcp utility for AIMTranslation table.  Exiting script.
echo -------------------------------------------------------------------------------

endlocal
set prompt=
