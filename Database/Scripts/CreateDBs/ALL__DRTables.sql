/****** Object:  Table [dbo].[AIMAO]    Script Date: 05/30/2003 11:04:18 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMAO]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMAO]
GO

CREATE TABLE [dbo].[AIMAO] (
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OrderStatus] [tinyint] NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OriginCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LineCount] [int] NOT NULL ,
	[TotalUnits] [int] NOT NULL ,
	[LocFillPct] [decimal](10, 2) NOT NULL ,
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	 PRIMARY KEY  CLUSTERED 
	(
		[OrdNbr]
		, [LcID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX AIMAO_LcID ON AIMAO (LcID)
GO

/****** Object:  Table [dbo].[AIMAODetail]    Script Date: 05/30/2003 11:04:21 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMAODetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMAODetail]
GO

CREATE TABLE [dbo].[AIMAODetail] (
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[OrdType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LineNbr] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LineItemStatus] [tinyint] NOT NULL,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMAODetail_UOM] DEFAULT (N'EA'),
	[AllocUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMAODetail_AllocUOM] DEFAULT (N'EA'),
	[AllocConvFactor] int CONSTRAINT [DF_AIMAODetail_AllocConvFactor] DEFAULT (1),
	[RequestedQty] [int] NOT NULL,
	[AllocatedQty] [int] NOT NULL,
	[AdjustedAllocQty] [int] NOT NULL,
	[Cost] [decimal](18, 2) NOT NULL,
	[LineFillPct] [decimal](10, 2) NOT NULL,
	[TxnDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[TxnTimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocationDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocationTimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	PRIMARY KEY  CLUSTERED 
	(
		[OrdNbr],
		[LineNbr],
		[Lcid],
		[Item] 
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX AIMAODetail_Item ON AIMAODetail (Item)
GO
/****** Object:  Table [dbo].[AIMAO_OrderInfo]    Script Date: 05/30/2003 11:04:18 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMAO_OrderInfo]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMAO_OrderInfo]
GO

CREATE TABLE [dbo].[AIMAO_OrderInfo] (
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OrderStatus] [tinyint] NOT NULL ,
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LineCount] [int] NOT NULL ,
	[TotalUnits] [int] NOT NULL ,
	[OrderFillPct] [decimal](10, 2) NOT NULL
	 PRIMARY KEY  CLUSTERED 
	(
		[OrdNbr]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[AIMClasses]    Script Date: 05/30/2003 11:04:21 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMClasses]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMClasses]
GO

CREATE TABLE [dbo].[AIMClasses] (
	[Class] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ClassDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ClassLevel] [tinyint] NOT NULL ,
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMClasses] PRIMARY KEY  CLUSTERED 
	(
		[Class],
		[ClassLevel],
		[LangID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMCodeLookUp]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMCodeLookUp]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMCodeLookUp]
GO

CREATE TABLE [dbo].[AIMCodeLookUp] (
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CodeType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CodeID] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CodeDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT [PK_AIMCodeLookUp] PRIMARY KEY NONCLUSTERED 
	(
		[LangID],
		[CodeType],
		[CodeID]
	)  ON [PRIMARY] 

) ON [PRIMARY]
GO
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMCompanionItem]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMCompanionItem]
GO
CREATE TABLE [AIMCompanionItem] (
	[MasterItem] [nvarchar] (25)  NOT NULL ,
	[Lcid] [nvarchar] (12)  NOT NULL ,
	[EnableY_N] [nvarchar] (1)  NOT NULL CONSTRAINT [DF_AIMCompanionItem_EnableY_N] DEFAULT (N'Y'),
	[CompanionDesc] [nvarchar] (255)  NOT NULL ,
	CONSTRAINT [PK_AIMCompanionItem] PRIMARY KEY  CLUSTERED 
	(
		[MasterItem],
		[Lcid]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMCompanionItemDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMCompanionItemDetail]
GO
CREATE TABLE [AIMCompanionItemDetail] (
	[MasterItem] [nvarchar] (25)  NOT NULL ,
	[Lcid] [nvarchar] (12) NOT NULL ,
	[Item] [nvarchar] (25)  NOT NULL ,
	[Qty] [int] NOT NULL CONSTRAINT [DF_CompanionItemDetail_Qty] DEFAULT (0),
	[DemandFactor] [decimal](3, 0) NOT NULL ,
	[Startdate] [datetime] NOT NULL ,
	[Enddate] [datetime] NOT NULL ,
	[ExcludeIndepDemY_N] [nvarchar] (1)  NOT NULL CONSTRAINT [DF_AIMCompanionItemDetail_ExcludeIndepDemY_N] DEFAULT (N'N'),
	CONSTRAINT [PK_CompanionItemDetail] PRIMARY KEY  CLUSTERED 
	(
		[MasterItem],
		[Lcid],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[AIMDataExchangeCtrl]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDataExchangeCtrl]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDataExchangeCtrl]
GO

CREATE TABLE [dbo].[AIMDataExchangeCtrl] (
	[TxnSet] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FileName] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TxnDate] [datetime] NOT NULL ,
	CONSTRAINT [PK_AIMDataExchangeCtrl] PRIMARY KEY  CLUSTERED 
	(
		[TxnSet],
		[FileName]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDays]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDays]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDays]
GO

CREATE TABLE [dbo].[AIMDays] (
	[FYDate] [datetime] NOT NULL ,
	[DayStatus] [tinyint] NOT NULL ,
	[FYPeriod_Days] [int] NULL,
	[FYPeriod_Weeks] [int] NULL,
	[FYPeriod_Months] [int] NULL,
	[FYPeriod_Quarters] [int] NULL,
	[FYPeriod_544] [int] NULL,
	[FYPeriod_454] [int] NULL,
	[FYPeriod_445] [int] NULL,
	[FYPeriod_4Weeks] [int] NULL,
	CONSTRAINT [PK_AIMDays] PRIMARY KEY CLUSTERED 
	(
		[FYDate]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX [IX_AIMDAYS_COVER] ON 
	[dbo].[AIMDays] (
		[FYPeriod_Days],
		[FYPeriod_Weeks],
		[FYPeriod_Months],
		[FYPeriod_Quarters],
		[FYPeriod_544],
		[FYPeriod_454],
		[FYPeriod_445],
		[FYPeriod_4Weeks]
	) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[AIMDD]    Script Date: 05/30/2003 11:04:22 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDD]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDD]
GO

CREATE TABLE [dbo].[AIMDD] (
	[DataElement] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DetDesc] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Range] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DefaultValue] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ElementName] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMDD_ElementName] DEFAULT (''),
	CONSTRAINT [PK_AIMDD] PRIMARY KEY  CLUSTERED 
	(
		[DataElement]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDestinationProfile]    Script Date: 05/30/2003 11:04:23 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDestinationProfile]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDestinationProfile]
GO

CREATE TABLE [dbo].[AIMDestinationProfile] (
	[LcID_Destination] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ExceptionPct] [tinyint] NOT NULL CONSTRAINT [DF_AIMDProf_ExceptionPct] DEFAULT (0),
	[AvailableQty] [int] NOT NULL ,
	[TargetQty] [int] NOT NULL ,
	 PRIMARY KEY  CLUSTERED 
	(
		[LcID_Destination],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/*******************************************************************************
 **	Table Name: AIMDxAS
 **	Desc: to be used in the Alternate Source Interface
 **
 **              
 **	Author: Mohammed
 *******************************************************************************
 **	Change History
 *******************************************************************************
 **    Date:	   Author:	Description:
 **    ---------- ------------	-------------------------------------------------
 *******************************************************************************/


/****** Object:  Table [dbo].[AIMDxAS]  ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxAS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
Drop Table [dbo].[AIMDxAS]
GO

/****** Object:  Table [dbo].[AIMDxAS] ******/
CREATE TABLE [dbo].[AIMDxAS] (
	[SeqNr]   [Int]  NULL,  
	[Enabled] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item]    [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcId]    [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnId]    [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort]  [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[BuyingUOM]  [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ConvFactor] [Int] NULL ,
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[StdCost]  [decimal](10,4) NULL,
	[LeadTime] [SmallInt] NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxDD]    Script Date: 05/30/2003 11:04:23 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxDD]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxDD]
GO

CREATE TABLE [dbo].[AIMDxDD] (
	[seqnbr] [int] NULL ,
	[ordnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[doctype] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[prmprog] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[linenbr] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[cusnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ordqty] [int] NULL ,
	[shpqty] [int] NULL ,
	[uom] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[extcost] [decimal](10, 2) NULL ,
	[extprice] [decimal](10, 2) NULL ,
	[reason_code] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[txndate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[timeofday] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[txn_type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[source] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[shipping] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[handling] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[olduom] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[uomfact] [decimal](11, 4) NULL ,
	[olditem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


IF EXISTS ( SELECT * FROM dbo.sysobjects WHERE ID = object_id( N'[dbo].[AIMDxFA]' ) AND OBJECTPROPERTY( ID, N'IsUserTable') = 1 )
	DROP TABLE [dbo].[AIMDxFA]
GO
/*******************************************************************************
**                       			NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS CONFIDENTIAL INFORMATION OF 
**	SSA GLOBAL TECHNOLOGIES, INC., AND SHALL NOT BE COPIED, USED, NOR DISCLOSED 
**	WITHOUT EXPRESS WRITTEN AUTHORIZATION.  
**	ALTHOUGH PUBLICATION IS NOT INTENDED, IN THE EVENT OF PUBLICATION, 
**	THE FOLLOWING NOTICE IS APPLICABLE:
**  	(c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**	SSA GLOBAL TECHNOLOGIES, INC.
********************************************************************************
**	Name: dbo.AIMDxFA.tab
**	Desc: Create transit table for importing Forecast Adjustments.
**	Auth:   Annalakshmi Stocksdale
**	Date:   2003/05/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
**	2004/10/25	A.Stocksdale	Updates to match Srinivas' Forecast Planner
*******************************************************************************/
CREATE TABLE [dbo].[AIMDxFA] (
	[SeqNbr] [int] NULL,
	[LineNbr] [int] NULL,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AdjustType] [tinyint] NULL,
	[AdjustQty] [decimal](10, 2) NULL,
	[AdjustBegDate] [datetime] NULL,
	[AdjustEndDate] [datetime] NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[RecordDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[AIMDxHS]    Script Date: 05/30/2003 11:04:23 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxHS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxHS]
GO

CREATE TABLE [dbo].[AIMDxHS] (
	[SeqNbr] [int] NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[SubsItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HisYear] [smallint] NULL ,
	[CPS01] [decimal](9, 1) NULL ,
	[CPS02] [decimal](9, 1) NULL ,
	[CPS03] [decimal](9, 1) NULL ,
	[CPS04] [decimal](9, 1) NULL ,
	[CPS05] [decimal](9, 1) NULL ,
	[CPS06] [decimal](9, 1) NULL ,
	[CPS07] [decimal](9, 1) NULL ,
	[CPS08] [decimal](9, 1) NULL ,
	[CPS09] [decimal](9, 1) NULL ,
	[CPS10] [decimal](9, 1) NULL ,
	[CPS11] [decimal](9, 1) NULL ,
	[CPS12] [decimal](9, 1) NULL ,
	[CPS13] [decimal](9, 1) NULL ,
	[CPS14] [decimal](9, 1) NULL ,
	[CPS15] [decimal](9, 1) NULL ,
	[CPS16] [decimal](9, 1) NULL ,
	[CPS17] [decimal](9, 1) NULL ,
	[CPS18] [decimal](9, 1) NULL ,
	[CPS19] [decimal](9, 1) NULL ,
	[CPS20] [decimal](9, 1) NULL ,
	[CPS21] [decimal](9, 1) NULL ,
	[CPS22] [decimal](9, 1) NULL ,
	[CPS23] [decimal](9, 1) NULL ,
	[CPS24] [decimal](9, 1) NULL ,
	[CPS25] [decimal](9, 1) NULL ,
	[CPS26] [decimal](9, 1) NULL ,
	[CPS27] [decimal](9, 1) NULL ,
	[CPS28] [decimal](9, 1) NULL ,
	[CPS29] [decimal](9, 1) NULL ,
	[CPS30] [decimal](9, 1) NULL ,
	[CPS31] [decimal](9, 1) NULL ,
	[CPS32] [decimal](9, 1) NULL ,
	[CPS33] [decimal](9, 1) NULL ,
	[CPS34] [decimal](9, 1) NULL ,
	[CPS35] [decimal](9, 1) NULL ,
	[CPS36] [decimal](9, 1) NULL ,
	[CPS37] [decimal](9, 1) NULL ,
	[CPS38] [decimal](9, 1) NULL ,
	[CPS39] [decimal](9, 1) NULL ,
	[CPS40] [decimal](9, 1) NULL ,
	[CPS41] [decimal](9, 1) NULL ,
	[CPS42] [decimal](9, 1) NULL ,
	[CPS43] [decimal](9, 1) NULL ,
	[CPS44] [decimal](9, 1) NULL ,
	[CPS45] [decimal](9, 1) NULL ,
	[CPS46] [decimal](9, 1) NULL ,
	[CPS47] [decimal](9, 1) NULL ,
	[CPS48] [decimal](9, 1) NULL ,
	[CPS49] [decimal](9, 1) NULL ,
	[CPS50] [decimal](9, 1) NULL ,
	[CPS51] [decimal](9, 1) NULL ,
	[CPS52] [decimal](9, 1) NULL ,
	[QtyOrd01] [decimal](9, 1) NULL ,
	[QtyOrd02] [decimal](9, 1) NULL ,
	[QtyOrd03] [decimal](9, 1) NULL ,
	[QtyOrd04] [decimal](9, 1) NULL ,
	[QtyOrd05] [decimal](9, 1) NULL ,
	[QtyOrd06] [decimal](9, 1) NULL ,
	[QtyOrd07] [decimal](9, 1) NULL ,
	[QtyOrd08] [decimal](9, 1) NULL ,
	[QtyOrd09] [decimal](9, 1) NULL ,
	[QtyOrd10] [decimal](9, 1) NULL ,
	[QtyOrd11] [decimal](9, 1) NULL ,
	[QtyOrd12] [decimal](9, 1) NULL ,
	[QtyOrd13] [decimal](9, 1) NULL ,
	[QtyOrd14] [decimal](9, 1) NULL ,
	[QtyOrd15] [decimal](9, 1) NULL ,
	[QtyOrd16] [decimal](9, 1) NULL ,
	[QtyOrd17] [decimal](9, 1) NULL ,
	[QtyOrd18] [decimal](9, 1) NULL ,
	[QtyOrd19] [decimal](9, 1) NULL ,
	[QtyOrd20] [decimal](9, 1) NULL ,
	[QtyOrd21] [decimal](9, 1) NULL ,
	[QtyOrd22] [decimal](9, 1) NULL ,
	[QtyOrd23] [decimal](9, 1) NULL ,
	[QtyOrd24] [decimal](9, 1) NULL ,
	[QtyOrd25] [decimal](9, 1) NULL ,
	[QtyOrd26] [decimal](9, 1) NULL ,
	[QtyOrd27] [decimal](9, 1) NULL ,
	[QtyOrd28] [decimal](9, 1) NULL ,
	[QtyOrd29] [decimal](9, 1) NULL ,
	[QtyOrd30] [decimal](9, 1) NULL ,
	[QtyOrd31] [decimal](9, 1) NULL ,
	[QtyOrd32] [decimal](9, 1) NULL ,
	[QtyOrd33] [decimal](9, 1) NULL ,
	[QtyOrd34] [decimal](9, 1) NULL ,
	[QtyOrd35] [decimal](9, 1) NULL ,
	[QtyOrd36] [decimal](9, 1) NULL ,
	[QtyOrd37] [decimal](9, 1) NULL ,
	[QtyOrd38] [decimal](9, 1) NULL ,
	[QtyOrd39] [decimal](9, 1) NULL ,
	[QtyOrd40] [decimal](9, 1) NULL ,
	[QtyOrd41] [decimal](9, 1) NULL ,
	[QtyOrd42] [decimal](9, 1) NULL ,
	[QtyOrd43] [decimal](9, 1) NULL ,
	[QtyOrd44] [decimal](9, 1) NULL ,
	[QtyOrd45] [decimal](9, 1) NULL ,
	[QtyOrd46] [decimal](9, 1) NULL ,
	[QtyOrd47] [decimal](9, 1) NULL ,
	[QtyOrd48] [decimal](9, 1) NULL ,
	[QtyOrd49] [decimal](9, 1) NULL ,
	[QtyOrd50] [decimal](9, 1) NULL ,
	[QtyOrd51] [decimal](9, 1) NULL ,
	[QtyOrd52] [decimal](9, 1) NULL ,
	[OrdCnt01] [int] NULL ,
	[OrdCnt02] [int] NULL ,
	[OrdCnt03] [int] NULL ,
	[OrdCnt04] [int] NULL ,
	[OrdCnt05] [int] NULL ,
	[OrdCnt06] [int] NULL ,
	[OrdCnt07] [int] NULL ,
	[OrdCnt08] [int] NULL ,
	[OrdCnt09] [int] NULL ,
	[OrdCnt10] [int] NULL ,
	[OrdCnt11] [int] NULL ,
	[OrdCnt12] [int] NULL ,
	[OrdCnt13] [int] NULL ,
	[OrdCnt14] [int] NULL ,
	[OrdCnt15] [int] NULL ,
	[OrdCnt16] [int] NULL ,
	[OrdCnt17] [int] NULL ,
	[OrdCnt18] [int] NULL ,
	[OrdCnt19] [int] NULL ,
	[OrdCnt20] [int] NULL ,
	[OrdCnt21] [int] NULL ,
	[OrdCnt22] [int] NULL ,
	[OrdCnt23] [int] NULL ,
	[OrdCnt24] [int] NULL ,
	[OrdCnt25] [int] NULL ,
	[OrdCnt26] [int] NULL ,
	[OrdCnt27] [int] NULL ,
	[OrdCnt28] [int] NULL ,
	[OrdCnt29] [int] NULL ,
	[OrdCnt30] [int] NULL ,
	[OrdCnt31] [int] NULL ,
	[OrdCnt32] [int] NULL ,
	[OrdCnt33] [int] NULL ,
	[OrdCnt34] [int] NULL ,
	[OrdCnt35] [int] NULL ,
	[OrdCnt36] [int] NULL ,
	[OrdCnt37] [int] NULL ,
	[OrdCnt38] [int] NULL ,
	[OrdCnt39] [int] NULL ,
	[OrdCnt40] [int] NULL ,
	[OrdCnt41] [int] NULL ,
	[OrdCnt42] [int] NULL ,
	[OrdCnt43] [int] NULL ,
	[OrdCnt44] [int] NULL ,
	[OrdCnt45] [int] NULL ,
	[OrdCnt46] [int] NULL ,
	[OrdCnt47] [int] NULL ,
	[OrdCnt48] [int] NULL ,
	[OrdCnt49] [int] NULL ,
	[OrdCnt50] [int] NULL ,
	[OrdCnt51] [int] NULL ,
	[OrdCnt52] [int] NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxIS]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxIS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxIS]
GO

CREATE TABLE [dbo].[AIMDxIS] (
	[AllocPrimaryItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocSubstItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[AllocSubstPriority] int
) ON [PRIMARY]
GO


if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxKB]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxKB]
GO
CREATE TABLE [AIMDxKB] (
	[SeqNbr] [int] NOT NULL ,
	[Enabled][nvarchar] (1) Not NULL CONSTRAINT [AIMDxKB_Enabled] DEFAULT (N'N'),
	[Lcid] [nvarchar] (12)  NOT NULL ,
	[Item] [nvarchar] (25)  NOT NULL ,
	[ItemComponent] [nvarchar] (25)  NOT NULL ,
	[ComponentUnits] [int] NOT NULL ,
	[ComponentScrap] [decimal](10, 4) NOT NULL ,
	[StartDate] [nvarchar] (10)  NOT NULL ,
	[EndDate] [nvarchar] (10)  NOT NULL  
) ON [PRIMARY]
GO



/****** Object:  Table [dbo].[AIMDxLC]    Script Date: 05/30/2003 11:04:25 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxLC]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxLC]
GO

CREATE TABLE [dbo].[AIMDxLC] (
	[SeqNbr] [int] NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress4] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LCountry] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LRank] [smallint] NULL,
	[PutAwayDays] [tinyint] NULL,
	[ReplenCost] [decimal](18, 0) NULL,
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LeadTimeSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
    [Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LookBackPds] [smallint] NULL,
	[DmdScalingFactor] [decimal](5, 0) NULL,
	[ScalingEffUntil] [datetime] NULL ,
	[Freeze_Period] [int] NULL,
	[UpdateCurrentYearOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[DropShip_XDock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Dft_ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[ExceptionPct] [tinyint] NULL ,
	 PRIMARY KEY  NONCLUSTERED 
	(
		[Lcid]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxLT]    Script Date: 05/30/2003 11:04:25 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxLT]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxLT]
GO

CREATE TABLE [dbo].[AIMDxLT] (
	[SeqNbr] [int] NULL ,
	[PONbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LineNbr] [smallint] NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[QtyOrd] [int] NULL ,
	[QtyRec] [int] NULL ,
	[DateOrd] [datetime] NULL ,
	[DateRec] [datetime] NULL ,
	[DatePutAway] [datetime] NULL ,
	[Oh] [int] NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxRO]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxRO]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxRO]
GO

CREATE TABLE [dbo].[AIMDxRO] (
	[SeqNbr] [int] NULL,
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[OrdType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[LineNbr] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[OrdQty] [int] NULL,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[TxnDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[TxnTimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[Txn_Type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[OriginCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxRP]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxRP]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxRP]
GO

CREATE TABLE [dbo].[AIMDxRP] (
	[SeqNbr] [int] NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ExceptionPct] [tinyint] NULL,
	[OnHandQty] [int] NOT NULL ,
	[StandardQty] [int] NOT NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxRS]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxRS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxRS]
GO

CREATE TABLE [dbo].[AIMDxRS] (
	[SeqNbr] [int] NOT NULL ,
	[Lcid_Destination] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Lcid_Source] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Src_Priority] [smallint] NULL ,
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxSd]    Script Date: 05/30/2003 11:04:27 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxSd]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxSd]
GO

CREATE TABLE [dbo].[AIMDxSd] (
	[SeqNbr] [int] NULL ,
	[OrdNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DocType] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[PrmProg] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LineNbr] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CusNbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[SubsItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OrdQty] [int] NULL ,
	[ShpQty] [int] NULL ,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ExtCost] [decimal](10, 2) NULL ,
	[ExtPrice] [decimal](10, 2) NULL ,
	[Reason_Code] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TxnDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TimeOfDay] [nvarchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Txn_Type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Source] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Shipping] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Handling] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OldUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UOMFact] [decimal](11, 4) NULL ,
	[OldItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxSL]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxSL]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxSL]
GO

CREATE TABLE [dbo].[AIMDxSL] (
	[SeqNbr] [int] NULL ,
	[Status] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[InActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UPC] [nvarchar] (22) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Oh] [int] NULL ,
	[Oo] [int] NULL ,
	[ComStk] [int] NULL ,
	[BkOrder] [int] NULL ,
	[BkComStk] [int] NULL ,
	[StkDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OnPromotion] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Freeze_Forecast] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[AllocatableQTY] [int] NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxSS]    Script Date: 05/30/2003 11:04:26 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxSS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxSS]
GO

CREATE TABLE [dbo].[AIMDxSS] (
	[SeqNbr] [int] NULL ,
	[Status] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[InActDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Weight] [decimal](10, 4) NULL ,
	[Cube] [decimal](10, 4) NULL ,
	[ListPrice] [decimal](10, 4) NULL ,
	[Price] [decimal](10, 4) NULL ,
	[Cost] [decimal](10, 4) NULL ,
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ConvFactor] [int] NULL ,
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LeadTime] [smallint] NULL ,
	[UPC] [nvarchar] (22) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Oh] [int] NULL ,
	[Oo] [int] NULL ,
	[ComStk] [int] NULL ,
	[BkOrder] [int] NULL ,
	[BkComStk] [int] NULL ,
	[StkDate] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[BinLocation] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[BkQty01] [int] NULL ,
	[BkCost01] [decimal](10, 4) NULL ,
	[BkQty02] [int] NULL ,
	[BkCost02] [decimal](10, 4) NULL ,
	[BkQty03] [int] NULL ,
	[BkCost03] [decimal](10, 4) NULL ,
	[BkQty04] [int] NULL ,
	[BkCost04] [decimal](10, 4) NULL ,
	[BkQty05] [int] NULL ,
	[BkCost05] [decimal](10, 4) NULL ,
	[BkQty06] [int] NULL ,
	[BkCost06] [decimal](10, 4) NULL ,
	[BkQty07] [int] NULL ,
	[BkCost07] [decimal](10, 4) NULL ,
	[BkQty08] [int] NULL ,
	[BkCost08] [decimal](10, 4) NULL ,
	[BkQty09] [int] NULL ,
	[BkCost09] [decimal](10, 4) NULL ,
	[BkQty10] [int] NULL ,
	[BkCost10] [decimal](10, 4) NULL ,
	[OnPromotion] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[OldUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UOMFact] [decimal](10, 4) NULL ,
	[OldItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPONbr_1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPODate_1] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPOQty_1] [int] NULL ,
	[NextPONbr_2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPODate_2] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPOQty_2] [int] NULL ,
	[NextPONbr_3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPODate_3] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NextPOQty_3] [int] NULL ,
	[UserRef1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserRef3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Freeze_Forecast] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AllocatableQty] [int] NULL ,
	[KitBOMFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AllocConvFactor] [int] NOT NULL, 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMDxVn]    Script Date: 05/30/2003 11:04:27 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMDxVn]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMDxVn]
GO

CREATE TABLE [dbo].[AIMDxVn] (
	[SeqNbr] [int] NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VDocEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Dft_ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Dft_LeadTime] [smallint],
	[Vn_Min] [decimal](9, 2),
	[Vn_Best] [decimal](9, 2),
	[Reach_Code] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[POByZone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VnComments1] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VnComments2] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[TransmitPO] [tinyint],
	[ShipIns] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS
) ON [PRIMARY]
GO


/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstAccess.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/01/01
--   Updated By     - Annalakshmi Stocksdale
--
--   This replaces the former AIM_DmdPlanFcstUserAccess and AIMFcstMainAccess tables
--		in allowing definitions of access permissions to the maintenance and modification screens.
--
--   The "forecast generator/modification" process is being morphed
--   into demand planning, hence the options required for forecast generator
--   are being phased out, and additional options for demand planning
--   are to be created, as of version 4.5
--   See related updates to *AIM_DmdPlanFcst*
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/
IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstAccess]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstAccess]
GO

CREATE TABLE [dbo].[AIMFcstAccess] (
	[FcstSetupKey] int NOT NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AccessCode] [tinyint] NOT NULL,

	CONSTRAINT [PK_AIMFcstAccess] PRIMARY KEY  CLUSTERED 
	(
		[FcstSetupKey],
		[UserID],
		[AccessCode]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO



if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMFcstMaster]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMFcstMaster]
GO

CREATE TABLE [dbo].[AIMFcstMaster] (
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[PeriodBegDate] [datetime] NOT NULL ,
	[PeriodEndDate] [datetime] NOT NULL ,
	[QtyAdj] [int] NOT NULL ,
	[QtyAdjOverRide] [int] NULL ,
	[QtyAdjPct] [int] NOT NULL ,
	[AdjOverRide] [bit] NOT NULL ,
	[DateTimeEdit] [datetime] NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[AIMFcstMaster] WITH NOCHECK ADD 
	CONSTRAINT [PK_AIMFcstMaster] PRIMARY KEY  CLUSTERED 
	(
		[LcID],
		[Item],
		[PeriodBegDate]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[AIMFcstMaster] ADD 
	CONSTRAINT [DF_AIMFcstMaster_AdjustQty] DEFAULT (0) FOR [QtyAdjPct],
	CONSTRAINT [DF_AIMFcstMaster_AdjOverRide] DEFAULT (0) FOR [AdjOverRide],
	CONSTRAINT [IX_AIMFcstMaster] UNIQUE  NONCLUSTERED 
	(
		[LcID],
		[Item],
		[PeriodBegDate]
	)  ON [PRIMARY] 
GO




/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstSetup.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/01/01
--   Updated By     - Annalakshmi Stocksdale
--
--   This replaces the former AIM_DmdPlanFcst maintenance table in allowing definitions of forecast criteria.
--
--	NOTE: FcstSetupKey is the PRIMARY KEY for this table, and FcstID has a UNIQUE constraint.
--
--   The "forecast generator/modification" process is being morphed
--   into demand planning, hence the options required for forecast generator
--   are being phased out, and additional options for demand planning
--   are to be created, as of version 4.5
--   See related updates to *AIM_DmdPlanFcst*
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/
IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstSetup]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstSetup]
GO

CREATE TABLE [dbo].[AIMFcstSetup] (
	[FcstSetupKey] int IDENTITY,
	[FcstId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL UNIQUE ,
	[FcstDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstDesc] DEFAULT (''),
	[FcstHierarchy] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstHierarchy] DEFAULT (1),
	[FcstEnabled] [bit],
	[FcstLocked] [bit],
	[FcstStartDate] [datetime] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstStartDate] DEFAULT (0),
	[LastUpdated] [datetime] NOT NULL CONSTRAINT [DF_AIMFcstSetup_LastUpdated] DEFAULT (0),
	[FcstRolling] [bit],
	[FcstHistory] [bit],
	[FcstPds_Future] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstPds_Future] DEFAULT (6),
	[FcstPds_Historical] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstPds_Historical] DEFAULT (6),
	[FixedPds] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FixedPds] DEFAULT (0),
	[FreezePds] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FreezePds] DEFAULT (0),
	[FcstInterval] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstInterval] DEFAULT (2),
	[FcstUnit] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstUnit] DEFAULT (0),
	[ApplyTrend] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_ApplyTrend] DEFAULT (0),
	[Calc_SysFcst] [bit] NULL ,
	[Calc_NetReq] [bit] NULL ,
	[Calc_HistDmd] [bit] NULL ,
	[Calc_MasterFcstAdj] [bit] NULL ,
	[Calc_FcstAdj] [bit] NULL ,
	[Calc_AdjNetReq] [bit] NULL ,
	[Calc_ProjInv] [bit] NULL ,
	[Calc_ProdConst] [bit] NULL ,
-- Item filter criteria
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ItStat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[VnID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ByID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
	CONSTRAINT [PK_AIMFcstSetup] PRIMARY KEY  CLUSTERED 
	(
		[FcstSetupKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX IDX_AIMFcstSetup_Cover ON AIMFcstSetup (FcstSetupKey, FcstID, FcstStartDate)
GO


/****** Object:  Table [dbo].[AIMFunctions]    Script Date: 05/30/2003 11:04:33 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMFunctions]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMFunctions]
GO

CREATE TABLE [dbo].[AIMFunctions] (
	[CtrlNbr] [int] NOT NULL ,
	[FuncDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMFunctions_FuncDesc] DEFAULT (''),
	[RWOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMFunctions_RWOption] DEFAULT ('N'),
	CONSTRAINT [PK_AIMFunctions] PRIMARY KEY  NONCLUSTERED 
	(
		[CtrlNbr]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMHelp]    Script Date: 10/12/2004 10:54:00 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMHelp]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMHelp]
GO

CREATE TABLE [dbo].[AIMHelp] (
	[HelpID] [int] IDENTITY (1, 1) NOT NULL ,
	[FormName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ControlName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Filepath] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMLanguage]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLanguage]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLanguage]
GO

CREATE TABLE [dbo].[AIMLanguage] (
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LangDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HexValue] [nvarchar] (15) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DecimalValue] [decimal](18, 2) NULL ,
	[Enabled] [nchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMLeadTime]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLeadTime]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLeadTime]
GO

CREATE TABLE [dbo].[AIMLeadTime] (
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LeadTime] [smallint] NOT NULL ,
	[Prev_LeadTime1] [smallint] NOT NULL ,
	[Prev_LeadTime2] [smallint] NOT NULL ,
	[Prev_LeadTime3] [smallint] NOT NULL ,
	[LT_Count] [int] NOT NULL ,
	[LT_MAE] [decimal](10, 2) NOT NULL ,
	[SS_Erosion_Pct] [decimal](9, 4) NOT NULL ,
	CONSTRAINT [PK_AIMLeadTimes] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_VnId] ON [dbo].[AIMLeadTime]([VnId]) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMLiterals]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLiterals]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLiterals]
GO

CREATE TABLE [dbo].[AIMLiterals] (
	[Locality] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LiteralKey] [int] NOT NULL ,
	[Literal] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMLiterals] PRIMARY KEY  CLUSTERED 
	(
		[Locality],
		[LiteralKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMLocations]    Script Date: 05/30/2003 11:04:34 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMLocations]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMLocations]
GO

CREATE TABLE [dbo].[AIMLocations] (
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LName] DEFAULT (''),
	[LType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMLocations_LType] DEFAULT (N'D'),
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMLocations_LStatus] DEFAULT (N'A'),
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LAddress1] DEFAULT (''),
	[LAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LAddress2] DEFAULT (''),
	[LAddress3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LAddress4] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LCity] DEFAULT (''),
	[LState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LState] DEFAULT (''),
	[LZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LZip] DEFAULT (''),
	[LCountry] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[LContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LContact] DEFAULT (''),
	[LPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LPhone] DEFAULT (''),
	[LFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LFax] DEFAULT (''),
	[LEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LEMail] DEFAULT (''),
	[LRank] [smallint] NULL ,
	[PutAwayDays] [tinyint] NOT NULL CONSTRAINT [DF_Locations_PutAwayDays] DEFAULT (0),
	[ReplenCost] [decimal](11, 4) NOT NULL CONSTRAINT [DF_Locations_ReplenCost] DEFAULT (5),
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_DemandSource] DEFAULT ('S'),
	[LeadTimeSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_LeadTimeSource] DEFAULT ('S'),
	[StkDate] [smalldatetime] NOT NULL CONSTRAINT [DF_Locations_StkDate] DEFAULT ('1/1/1990'),
	[Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Locations_Dft_Byid] DEFAULT (''),
	[Last_FcstUpdCyc] [int] NOT NULL CONSTRAINT [DF_Locations_Last_FcstUpdCyc] DEFAULT (0),
	[LookBackPds] [smallint] NOT NULL CONSTRAINT [DF_Locations_LookBackPds] DEFAULT (52),
	[DmdScalingFactor] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMLocations_DmdScalingFactor] DEFAULT (1),
	[ScalingEffUntil] [datetime] NOT NULL CONSTRAINT [DF_AIMLocations_ScalingEffUntil] DEFAULT ('1/1/1990'),
	[Freeze_Period] [int] NOT NULL CONSTRAINT [DF_AIMLocations_Freeze_Period] DEFAULT (0),
	[UpdateCurrentYearOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMLocations_UpdateCurrentYearOption] DEFAULT (N'N'),
	[DropShip_XDock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_AIMLocations_DropShip_XDock] DEFAULT (N'N'),
	[Dft_ReviewerID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_AIMLocations_Dft_ReviewerID] DEFAULT (N'sa'),
	[ExceptionPct] [tinyint] NULL
	CONSTRAINT [PK_Locations] PRIMARY KEY  CLUSTERED 
	(
		[Lcid]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMMethods]    Script Date: 05/30/2003 11:04:35 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMMethods]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMMethods]
GO

CREATE TABLE [dbo].[AIMMethods] (
	[MethodId] [tinyint] NOT NULL ,
	[MethodStatus] [int] NOT NULL ,
	[MethodDesc] [nvarchar] (80) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ApplySeasonsIndex] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ApplyTrend] [bit] NOT NULL ,
	CONSTRAINT [PK_AIMMethods] PRIMARY KEY  NONCLUSTERED 
	(
		[MethodId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMOptions]    Script Date: 05/30/2003 11:04:35 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMOptions]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMOptions]
GO

CREATE TABLE [dbo].[AIMOptions] (
	[OptionId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OpDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_IDFL] DEFAULT (3),
	[BypIDFL] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypIDFL] DEFAULT ('N'),
	[CADL] [smallint] NOT NULL CONSTRAINT [DF_AIMOptions_CADL] DEFAULT (5),
	[HiDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_HiDFL] DEFAULT (5),
	[LoDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_LoDFL] DEFAULT (3),
	[NDFL] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_NDFL] DEFAULT (3),
	[DDMAP] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_DDMAP] DEFAULT (6),
	[Damp] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_Damp] DEFAULT (0.7),
	[MinSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MinSmk] DEFAULT (0.1),
	[MaxSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MaxSmk] DEFAULT (0.5),
	[IntSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_IntSmk] DEFAULT (0.1),
	[TSL] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_TSL] DEFAULT (0.5),
	[TSSmK] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_TSSmK] DEFAULT (0.1),
	[BypDDU] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypDDU] DEFAULT ('N'),
	[ApplyDmdFilter] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_ApplyDmdFilter] DEFAULT ('Y'),
	[BypZSl] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypZSl] DEFAULT ('N'),
	[BypTFP] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypTFP] DEFAULT ('Y'),
	[BypZOH] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMOptions_BypZOH] DEFAULT ('N'),
	[BypBef] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_BypBef] DEFAULT (1),
	[BypAft] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_BypAft] DEFAULT (1),
	[MinBI] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MinBI] DEFAULT (0.3),
	[HiPct] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_HiPct] DEFAULT (0.5),
	[LoPct] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_LoPct] DEFAULT (0.2),
	[HiMADP] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_HiMADP] DEFAULT (9),
	[LoMADP] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_LoMADP] DEFAULT (0.1),
	[MADSmk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MADSmk] DEFAULT (0.1),
	[MADExk] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_MADExk] DEFAULT (0.5),
	[TrnSmk] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_TrnSmk] DEFAULT (0.08),
	[DIDDL] [smallint] NOT NULL CONSTRAINT [DF_AIMOptions_DIDDL] DEFAULT (3),
	[DITrnDL] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_DITrnDL] DEFAULT (0.3),
	[DITrps] [tinyint] NOT NULL CONSTRAINT [DF_AIMOptions_DITrps] DEFAULT (6),
	[DIMADP] [decimal](2, 1) NOT NULL CONSTRAINT [DF_AIMOptions_DIMADP] DEFAULT (0.3),
	[HiTrndL] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_HiTrndL] DEFAULT (1),
	[FilterSmk] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMOptions_FilterSmk] DEFAULT (0.1),
	[LumpyFilterPct] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMOptions_LumpyFilterPct] DEFAULT (0.15),
	[Dft_TurnHigh] [decimal](4, 1) NOT NULL CONSTRAINT [DF_AIMOptions_Dft_TurnHigh] DEFAULT (999.9),
	[Dft_TurnLow] [decimal](4, 1) NOT NULL CONSTRAINT [DF_AIMOptions_Dft_TurnLow] DEFAULT (0),
	[Dft_Dser] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMOptions_Dft_Dser] DEFAULT (0.95),
	CONSTRAINT [PK_AIMOptions] PRIMARY KEY  NONCLUSTERED 
	(
		[OptionId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMPo]    Script Date: 05/30/2003 11:04:36 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMPo]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMPo]
GO

CREATE TABLE [dbo].[AIMPo] (
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TransmitPO] [tinyint] NOT NULL ,
	[ShipIns] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Remarks1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Remarks2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Remarks3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AddrOverride] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LineCount] [int] NOT NULL ,
	[PosLineCount] [int] NOT NULL CONSTRAINT [DF_AIMPo_PosLineCount] DEFAULT (0),
	[Vn_Min] [numeric](9, 2) NOT NULL ,
	[Vn_Best] [numeric](9, 2) NOT NULL ,
	[Reach_Code] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POByZone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Dft_LeadTime] [smallint] NOT NULL ,
	[TotalCost] [decimal](10, 2) NOT NULL CONSTRAINT [DF_AIMPo_TotalCost] DEFAULT (0),
        [VndSizeFlag] [nvarchar] (1) NOT NULL DEFAULT 'N', 
        CONSTRAINT [PK_AIMPo] PRIMARY KEY  CLUSTERED 
	(
		[ById],
		[VnId],
		[Assort]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMProductionConstraint]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMProductionConstraint]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMProductionConstraint]
GO

CREATE TABLE [dbo].[AIMProductionConstraint] (
	[ConstraintID] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ConstraintType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ConstraintDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CycleDays] [numeric](18, 0) NOT NULL ,
	[RatioType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[EnableConstraint] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMProductionConstraint] PRIMARY KEY  CLUSTERED 
	(
		[ConstraintID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMProductionConstraintDetail]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMProductionConstraintDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMProductionConstraintDetail]
GO

CREATE TABLE [dbo].[AIMProductionConstraintDetail] (
	[ConstraintID] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[MinUnits] [numeric](18, 0) NOT NULL ,
	[MaxUnits] [numeric](18, 0) NOT NULL ,
	CONSTRAINT [PK_AIMProductionConstraintDetail] PRIMARY KEY  CLUSTERED 
	(
		[ConstraintID],
		[Item]
	)  ON [PRIMARY] ,
	CONSTRAINT [FK_AIMProductionConstraintDetail_AIMProductionConstraint] FOREIGN KEY 
	(
		[ConstraintID]
	) REFERENCES [dbo].[AIMProductionConstraint] (
		[ConstraintID]
	)
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMPromotions]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMPromotions]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMPromotions]
GO

CREATE TABLE [dbo].[AIMPromotions] (
	[PmId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[PmDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[PmStatus] [bit] NOT NULL CONSTRAINT [DF_AIMPromotions_PmStatus] DEFAULT (0),
	[PmStartDate] [smalldatetime] NOT NULL ,
	[PmEndDate] [smalldatetime] NOT NULL ,
	[PmAdj01] [decimal](5, 2) NOT NULL ,
	[PmAdj02] [decimal](5, 2) NOT NULL ,
	[PmAdj03] [decimal](5, 2) NOT NULL ,
	[PmAdj04] [decimal](5, 2) NOT NULL ,
	[PmAdj05] [decimal](5, 2) NOT NULL ,
	[PmAdj06] [decimal](5, 2) NOT NULL ,
	[PmAdj07] [decimal](5, 2) NOT NULL ,
	[PmAdj08] [decimal](5, 2) NOT NULL ,
	[PmAdj09] [decimal](5, 2) NOT NULL ,
	[PmAdj10] [decimal](5, 2) NOT NULL ,
	[PmAdj11] [decimal](5, 2) NOT NULL ,
	[PmAdj12] [decimal](5, 2) NOT NULL ,
	[PmAdj13] [decimal](5, 2) NOT NULL ,
	[PmAdj14] [decimal](5, 2) NOT NULL ,
	[PmAdj15] [decimal](5, 2) NOT NULL ,
	[PmAdj16] [decimal](5, 2) NOT NULL ,
	[PmAdj17] [decimal](5, 2) NOT NULL ,
	[PmAdj18] [decimal](5, 2) NOT NULL ,
	[PmAdj19] [decimal](5, 2) NOT NULL ,
	[PmAdj20] [decimal](5, 2) NOT NULL ,
	[PmAdj21] [decimal](5, 2) NOT NULL ,
	[PmAdj22] [decimal](5, 2) NOT NULL ,
	[PmAdj23] [decimal](5, 2) NOT NULL ,
	[PmAdj24] [decimal](5, 2) NOT NULL ,
	[PmAdj25] [decimal](5, 2) NOT NULL ,
	[PmAdj26] [decimal](5, 2) NOT NULL ,
	[PmAdj27] [decimal](5, 2) NOT NULL ,
	[PmAdj28] [decimal](5, 2) NOT NULL ,
	[PmAdj29] [decimal](5, 2) NOT NULL ,
	[PmAdj30] [decimal](5, 2) NOT NULL ,
	[PmAdj31] [decimal](5, 2) NOT NULL ,
	[PmAdj32] [decimal](5, 2) NOT NULL ,
	[PmAdj33] [decimal](5, 2) NOT NULL ,
	[PmAdj34] [decimal](5, 2) NOT NULL ,
	[PmAdj35] [decimal](5, 2) NOT NULL ,
	[PmAdj36] [decimal](5, 2) NOT NULL ,
	[PmAdj37] [decimal](5, 2) NOT NULL ,
	[PmAdj38] [decimal](5, 2) NOT NULL ,
	[PmAdj39] [decimal](5, 2) NOT NULL ,
	[PmAdj40] [decimal](5, 2) NOT NULL ,
	[PmAdj41] [decimal](5, 2) NOT NULL ,
	[PmAdj42] [decimal](5, 2) NOT NULL ,
	[PmAdj43] [decimal](5, 2) NOT NULL ,
	[PmAdj44] [decimal](5, 2) NOT NULL ,
	[PmAdj45] [decimal](5, 2) NOT NULL ,
	[PmAdj46] [decimal](5, 2) NOT NULL ,
	[PmAdj47] [decimal](5, 2) NOT NULL ,
	[PmAdj48] [decimal](5, 2) NOT NULL ,
	[PmAdj49] [decimal](5, 2) NOT NULL ,
	[PmAdj50] [decimal](5, 2) NOT NULL ,
	[PmAdj51] [decimal](5, 2) NOT NULL ,
	[PmAdj52] [decimal](5, 2) NOT NULL ,
	CONSTRAINT [PK_AIMPromotions] PRIMARY KEY  NONCLUSTERED 
	(
		[PmId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMRoles]    Script Date: 10/12/2004 10:54:03 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMRoles]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMRoles]
GO

CREATE TABLE [dbo].[AIMRoles] (
	[RoleId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RoleDescription] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[RoleLevel] [int] NOT NULL ,
	[scarray] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RepToRoleID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AltSourceExcpts] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	CONSTRAINT [PK_AIMRoles] PRIMARY KEY  CLUSTERED 
	(
		[RoleId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMSd]    Script Date: 05/30/2003 11:04:37 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMSd]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMSd]
GO

CREATE TABLE [dbo].[AIMSd] (
	[seqnbr] [int] NULL ,
	[ordnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[doctype] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[prmprog] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[linenbr] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[cusnbr] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[shipto] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Subsitem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,	
	[ordqty] [int] NULL ,
	[shpqty] [int] NULL ,
	[uom] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[extcost] [decimal](10, 2) NULL ,
	[extprice] [decimal](10, 2) NULL ,
	[reason_code] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[txndate] [smalldatetime] NULL ,
	[txn_type] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[source] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[shipping] [nvarchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[handling] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[wk_start] [smalldatetime] NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMSeasons]    Script Date: 05/30/2003 11:04:38 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMSeasons]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMSeasons]
GO

CREATE TABLE [dbo].[AIMSeasons] (
	[SaVersion] [smallint] NOT NULL CONSTRAINT [DF_AIMSeasons_SaVersion] DEFAULT (0),
	[SaId] [nvarchar] (62) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SaDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SaLevel] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMSeasons_SaLevel] DEFAULT (''),
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMSeasons_LcId] DEFAULT (''),
	[Class] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMSeasons_Class1] DEFAULT (''),
	[BI01] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI01] DEFAULT (0),
	[BI02] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI02] DEFAULT (0),
	[BI03] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI03] DEFAULT (0),
	[BI04] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI04] DEFAULT (0),
	[BI05] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI05] DEFAULT (0),
	[BI06] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI06] DEFAULT (0),
	[BI07] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI07] DEFAULT (0),
	[BI08] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI08] DEFAULT (0),
	[BI09] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI09] DEFAULT (0),
	[BI10] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI10] DEFAULT (0),
	[BI11] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI11] DEFAULT (0),
	[BI12] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI12] DEFAULT (0),
	[BI13] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI13] DEFAULT (0),
	[BI14] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI14] DEFAULT (0),
	[BI15] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI15] DEFAULT (0),
	[BI16] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI16] DEFAULT (0),
	[BI17] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI17] DEFAULT (0),
	[BI18] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI18] DEFAULT (0),
	[BI19] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI19] DEFAULT (0),
	[BI20] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI20] DEFAULT (0),
	[BI21] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI21] DEFAULT (0),
	[BI22] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI22] DEFAULT (0),
	[BI23] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI23] DEFAULT (0),
	[BI24] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI24] DEFAULT (0),
	[BI25] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI25] DEFAULT (0),
	[BI26] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI26] DEFAULT (0),
	[BI27] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI27] DEFAULT (0),
	[BI28] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI28] DEFAULT (0),
	[BI29] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI29] DEFAULT (0),
	[BI30] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI30] DEFAULT (0),
	[BI31] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI31] DEFAULT (0),
	[BI32] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI32] DEFAULT (0),
	[BI33] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI33] DEFAULT (0),
	[BI34] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI34] DEFAULT (0),
	[BI35] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI35] DEFAULT (0),
	[BI36] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI36] DEFAULT (0),
	[BI37] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI37] DEFAULT (0),
	[BI38] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI38] DEFAULT (0),
	[BI39] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI39] DEFAULT (0),
	[BI40] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI40] DEFAULT (0),
	[BI41] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI41] DEFAULT (0),
	[BI42] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI42] DEFAULT (0),
	[BI43] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI43] DEFAULT (0),
	[BI44] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI44] DEFAULT (0),
	[BI45] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI45] DEFAULT (0),
	[BI46] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI46] DEFAULT (0),
	[BI47] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI47] DEFAULT (0),
	[BI48] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI48] DEFAULT (0),
	[BI49] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI49] DEFAULT (0),
	[BI50] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI50] DEFAULT (0),
	[BI51] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI51] DEFAULT (0),
	[BI52] [decimal](5, 3) NOT NULL CONSTRAINT [DF_AIMSeasons_BI52] DEFAULT (0),
	[ItmCnt] [int] NOT NULL CONSTRAINT [DF_AIMSeasons_ItmCnt] DEFAULT (0),
	[AvgUnits] [decimal](18, 1) NOT NULL CONSTRAINT [DF_AIMSeasons_AvgUnits] DEFAULT (0),
	CONSTRAINT [PK_AIMSeasons] PRIMARY KEY  NONCLUSTERED 
	(
		[SaVersion],
		[SaId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMSourcingHierarchy]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMSourcingHierarchy]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMSourcingHierarchy]
GO

CREATE TABLE [dbo].[AIMSourcingHierarchy] (
	[LcID_Destination] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcID_Source] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Src_Priority] [smallint] NOT NULL CONSTRAINT [DF__AIMSourci__Src_P__20245954] DEFAULT (0),
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF__AIMSourci__Revie__21187D8D] DEFAULT (N'sa' COLLATE SQL_Latin1_General_CP1_CI_AS),
	 PRIMARY KEY  CLUSTERED 
	(
		[LcID_Destination],
		[LcID_Source]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMTables]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMTables]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMTables]
GO

CREATE TABLE [dbo].[AIMTables] (
	[TableName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TableDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMTables_TableDesc] DEFAULT (''),
	[TableEnabled] [bit] NOT NULL CONSTRAINT [DF_AIMTables_TableEnabled] DEFAULT (1),
	CONSTRAINT [PK_AIMTables] PRIMARY KEY  CLUSTERED 
	(
		[TableName]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMTransferPolicy]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMTransferPolicy]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMTransferPolicy]
GO

CREATE TABLE [dbo].[AIMTransferPolicy] (
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcidTransferTo] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TransferType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[EnableTransferPolicy] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMTransferPolicy] PRIMARY KEY  CLUSTERED 
	(
		[Lcid],
		[LcidTransferTo],
		[TransferType]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMTranslation]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMTranslation]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMTranslation]
GO

CREATE TABLE [dbo].[AIMTranslation] (
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ResourceID] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ResourceString] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	CONSTRAINT [PK_AIMTranslation] PRIMARY KEY  CLUSTERED 
	(
		[LangID],
		[ResourceID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMUOM]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMUOM]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMUOM]
GO

CREATE TABLE [dbo].[AIMUOM] (
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UOMDesc] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUOM_UOMDesc] DEFAULT (''),
	CONSTRAINT [PK_AIMUOM] PRIMARY KEY  CLUSTERED 
	(
		[UOM]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMUsers]    Script Date: 05/30/2003 11:04:39 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMUsers]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMUsers]
GO

CREATE TABLE [dbo].[AIMUsers] (
	[UserId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SLevel] [tinyint] NOT NULL CONSTRAINT [DF_AIMUsers_SLevel] DEFAULT (0),
	[ScArray] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DmdUpdExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_DmdUpdExcpts] DEFAULT ('Y'),
	[OnPromotionExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_OnPromotion] DEFAULT ('N'),
	[ExtCostExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_ExtCostExcpts] DEFAULT ('N'),
	[ExtCostLimit] [decimal](10, 2) NOT NULL CONSTRAINT [DF_AIMUsers_ExtCostLimit] DEFAULT (1000),
	[PackSizeExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_PackSizeExcpts] DEFAULT ('N'),
	[PS_PctRoundLimit] [decimal](3, 2) NOT NULL CONSTRAINT [DF_AIMUsers_PS_PctRoundLimit] DEFAULT (0.7),
	[PS_ExtCostLimit] [decimal](10, 2) NOT NULL CONSTRAINT [DF_AIMUsers_PS_ExtCostLimit] DEFAULT (100),
	[LastWeekSaleExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_LastWeekSaleExcepts] DEFAULT ('N'),
	[OverDuePO] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_OverDuePO] DEFAULT ('N'),
	[SafetyStockEroded] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_SafetyStockEroded] DEFAULT ('N'),
	[BackOrdered] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_BackOrdered] DEFAULT ('N'),
	[InvAvailRestriction] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_InvAvailRestriction] DEFAULT ('N'),
	[InvAvailList] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_InvAvailList] DEFAULT (''),
	[UserInitials] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_UserInitials] DEFAULT (''),
	[LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_LangID] DEFAULT ('en-us'),
	[LogErrors] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_LogErrors] DEFAULT ('N'),
    	[AltSourceExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL DEFAULT ('N'),
	[POExtCost] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL DEFAULT ('N'),
	[POExtCostLimit] [decimal](18, 0)  DEFAULT (0) ,
	[RoleId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL DEFAULT ('BasicUser1'),
	[VendorCostExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMUsers_VendorCostExcpts] DEFAULT ('N'),
	CONSTRAINT [PK_AIMUsers] PRIMARY KEY  CLUSTERED 
	(
		[UserId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[AIMVelCodePcts]    Script Date: 05/30/2003 11:04:40 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMVelCodePcts]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMVelCodePcts]
GO

CREATE TABLE [dbo].[AIMVelCodePcts] (
	[VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VCPct] [decimal](4, 3) NOT NULL CONSTRAINT [DF_AIMVelCodePcts_VCPct] DEFAULT (1),
	[Dft_OptionId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVelCodePcts_Dft_OptionId] DEFAULT (''),
	CONSTRAINT [PK_AIMVelCodePcts] PRIMARY KEY  CLUSTERED 
	(
		[VelCode]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMVendors]    Script Date: 05/30/2003 11:04:40 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMVendors]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMVendors]
GO

CREATE TABLE [dbo].[AIMVendors] (
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[MDCFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VAddress1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VAddress1] DEFAULT (''),
	[VAddress2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VAddress2] DEFAULT (''),
	[VCity] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VCity] DEFAULT (''),
	[VState] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VState] DEFAULT (''),
	[VZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VZip] DEFAULT (''),
	[VContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VContact] DEFAULT (''),
	[VPhone] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VPhone] DEFAULT (''),
	[VFax] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VFax] DEFAULT (''),
	[VEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VEMail] DEFAULT (''),
	[VDocEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VDocEMail] DEFAULT (''),
	[Dft_Byid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_Dft_Byid] DEFAULT (''),
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_RevCycle] DEFAULT (''),
	[Dft_LeadTime] [smallint] NOT NULL ,
	[Last_Dft_LeadTime] [smallint] NOT NULL CONSTRAINT [DF_AIMVendors_Last_Dft_LeadTime] DEFAULT (0),
	[Vn_Min] [decimal](9, 2) NOT NULL ,
	[Vn_Best] [decimal](9, 2) NOT NULL ,
	[Reach_Code] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[POByZone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnComments1] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VnComments1] DEFAULT (''),
	[VnComments2] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_VnComments2] DEFAULT (''),
	[LastWeekSalesFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_LastWeekSalesFlag] DEFAULT ('N'),
	[TransmitPO] [tinyint] NOT NULL CONSTRAINT [DF_AIMVendors_TransmitPO] DEFAULT (0),
	[ShipIns] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMVendors_ShipIns] DEFAULT (''),
	CONSTRAINT [PK_AIMVendors] PRIMARY KEY  NONCLUSTERED 
	(
		[VnId],
		[Assort]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_Dft_RevCycle] ON [dbo].[AIMVendors]([RevCycle]) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMWorkingDays]    Script Date: 05/30/2003 11:04:40 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMWorkingDays]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMWorkingDays]
GO

CREATE TABLE [dbo].[AIMWorkingDays] (
	[Day1] [bit] NOT NULL ,
	[Day2] [bit] NOT NULL ,
	[Day3] [bit] NOT NULL ,
	[Day4] [bit] NOT NULL ,
	[Day5] [bit] NOT NULL ,
	[Day6] [bit] NOT NULL ,
	[Day7] [bit] NOT NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AIMYears]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMYears]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMYears]
GO

CREATE TABLE [dbo].[AIMYears] (
	[FiscalYear] [int] NOT NULL ,
	[FYStartDate] [datetime] NOT NULL ,
	[FYEndDate] [datetime] NOT NULL ,
	[NbrWeeks] [int] NOT NULL ,
	CONSTRAINT [PK_AimYears] PRIMARY KEY  CLUSTERED 
	(
		[FiscalYear]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


CREATE INDEX [IX_AIMYEARS_COVER] ON [dbo].[AIMYears]([FYStartDate], [FYEndDate]) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[AllocDefaults]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AllocDefaults]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AllocDefaults]
GO

CREATE TABLE [dbo].[AllocDefaults] (
	[AllocDefaultsId] [numeric](18, 0) IDENTITY (1, 1) NOT NULL ,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LStatus] DEFAULT (N''),
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LDivision] DEFAULT (N''),
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LRegion] DEFAULT (N''),
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AllocDefaults_LUserDefined] DEFAULT (N''),
	[ExceptionPct] [decimal](18, 2) NULL ,
	[ReviewerId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_AllocDefaults_ReviewerId] DEFAULT (N'sa'),
	[LRank] [tinyint] NOT NULL ,
	CONSTRAINT [IX_AllocDefaults] UNIQUE  NONCLUSTERED 
	(
		[LStatus],
		[LDivision],
		[LRegion],
		[LUserDefined]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AllocDefaultsSource]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AllocDefaultsSource]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AllocDefaultsSource]
GO

CREATE TABLE [dbo].[AllocDefaultsSource] (
	[AllocDefaultsId] [numeric](18, 0) NOT NULL ,
	[LcId_Source] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Src_Priority] [tinyint] NOT NULL CONSTRAINT [DF_AllocDefaultsSource_Src_Priority] DEFAULT (5),
	 PRIMARY KEY  CLUSTERED 
	(
		[AllocDefaultsId],
		[LcId_Source]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[AllocItemSubstitutes]    Script Date: 05/30/2003 11:04:33 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AllocItemSubstitutes]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AllocItemSubstitutes]
GO

CREATE TABLE [dbo].[AllocItemSubstitutes] (
	[AllocPrimaryItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocSubstItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AllocSubstPriority] smallint
		 PRIMARY KEY  CLUSTERED 
	(
		[AllocPrimaryItem],
		[AllocSubstItem]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

	/* Output table for  the allocation result record */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_AllocResults]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_AllocResults]
	GO
	CREATE TABLE AllocScratch_AllocResults (
		SessionID BIGINT NOT NULL,
		OrdNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		LineNbr nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		SourceLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		AllocatedQty INT NOT NULL
	);
	GO
	CREATE CLUSTERED INDEX ASAR_SESS ON AllocScratch_AllocResults (SessionID)
	GO

	/* AllocScratch_ItemDemand -- stores total demand for an item at a priority level */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_ItemDemand]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_ItemDemand]
	GO
	CREATE TABLE AllocScratch_ItemDemand (
		SessionID BIGINT NOT NULL,
		DestinationPriority INT NOT NULL, 
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		TotalRequestedQty INT NOT NULL, 
		SumNeedRatios DECIMAL(15, 5) NOT NULL
	);
	GO
	CREATE CLUSTERED INDEX ASID_SESS ON AllocScratch_ItemDemand (SessionID)
	GO

	/* Requestor Priority -- this will hold information on priorities for stores with orders */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_Req_Pri]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_Req_Pri]
	GO
	CREATE TABLE AllocScratch_Req_Pri (
		SessionID BIGINT NOT NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		DestinationPriority INT NOT NULL
	);
	CREATE CLUSTERED INDEX ASRP_SESS ON AllocScratch_Req_Pri (SessionID)
	CREATE INDEX ASRP_PRI ON AllocScratch_Req_Pri (DestinationPriority)
	GO
	
/*******************************************************************************
--	Name: AllocationScratchTables	
--	Desc: Creates work-tables for use by the Allocation Stored procedure. 
--		The Allocation Scratch tables are:
-- 		AllocScratch_Req_Qty:  
--			Requestor Quantity -- this will hold information on aggregate ordered quantity by store and item 
-- 		AllocScratch_Req_Pri:  
--			Requestor Priority -- this will hold information on priorities for stores with orders 
--		AllocScratch_Req_Ratio:
--			Need ratio -- this will hold information on need ratios by store and item 
-- 		AllocScratch_SourceInventory:  
--			Source Inventory -- this will hold information on DC On-Hand (so we don't mess up the real one) 
--		AllocScratch_ItemDemand:
-- 			Item demand summary table -- this will hold summarised demand data for an item for all destinations in a priority group.
-- 		AllocScratch_AllocResults:  
--			Output table for  the allocation result record 
--
--	Author:		Annalakshmi Stocksdale
--	Created:	2003/05/21
-------------------------------------------------------------------------------
--	Change History
-------------------------------------------------------------------------------
--	Date:		Updated by:		Description:
--	----------	------------	-----------------------------------------------
--								
*******************************************************************************/

	/* Requestor Quantity -- this will hold information on aggregate ordered quantity by store and item */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_Req_Qty]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_Req_Qty]
	GO
	CREATE TABLE AllocScratch_Req_Qty (
		SessionID BIGINT NOT NULL,
		OrdNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		LineNbr nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		RequestedQty INT NOT NULL
	);
	CREATE CLUSTERED INDEX ASRQ_SESS ON AllocScratch_Req_Qty (SessionID)
	CREATE INDEX ASRQ_ITEM ON AllocScratch_Req_Qty (ItemID)
	CREATE INDEX ASRQ_STORE ON AllocScratch_Req_Qty (DestinationLoc)
	GO
	
	/* Need ratio -- this will hold information on need ratios	*/
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_Req_Ratio]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_Req_Ratio]
	GO
	CREATE TABLE AllocScratch_Req_Ratio (
		SessionID BIGINT NOT NULL,
		OrdNbr nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		LineNbr nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
		DestinationLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		NeedRatio DECIMAL(15, 5) NOT NULL,
		WeightedNeed DECIMAL(15, 5) NOT NULL
	);
	CREATE CLUSTERED INDEX ASRR_SESS ON AllocScratch_Req_Ratio (SessionID)
	CREATE INDEX ASRR_RATIO ON AllocScratch_Req_Ratio (NeedRatio)
	CREATE INDEX ASRR_WEIGHTEDNEED ON AllocScratch_Req_Ratio (WeightedNeed)
	GO

	/* Source Inventory -- this will hold information on DC On-Hand (so we don't mess up the real one) */
	IF EXISTS (SELECT id 
			FROM sysobjects 
			WHERE id = object_id(N'[dbo].[AllocScratch_SourceInventory]') 
			and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	    DROP TABLE [dbo].[AllocScratch_SourceInventory]
	GO	
	CREATE TABLE AllocScratch_SourceInventory (
		SessionID BIGINT NOT NULL,
		SourceLoc nvarchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		ItemID nvarchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		SourceQty INT NOT NULL,
		AllocUOM nvarchar (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
		AllocConvFactor int,

	);
	GO
	CREATE CLUSTERED INDEX ASSI_SESS ON AllocScratch_SourceInventory (SessionID)
	GO

/*******************************************************************************
 **	Table Name: ALTERNATE_SOURCE
 **	Desc: Contains Alternate Vendors for each Item/Loc
 **
 **              
 **	Author: Mohammed
 *******************************************************************************
 **	Change History
 *******************************************************************************
 **    Date:	   Author:	Description:
 **    ---------- ------------	-------------------------------------------------
 *******************************************************************************/


/****** Object:  Table [dbo].[ALTERNATE_SOURCE]  ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ALTERNATE_SOURCE]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
Drop Table [dbo].[ALTERNATE_SOURCE]
GO

/****** Object:  Table [dbo].[ALTERNATE_SOURCE] ******/
CREATE TABLE [dbo].[ALTERNATE_SOURCE] (
	[Enabled] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT ('1') NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT ('EA') NOT NULL ,
	[ConvFactor] [Int] DEFAULT 1 NOT NULL ,
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT ('R') NOT NULL ,
	[StdCost]   [decimal](10,4) DEFAULT 0 NOT NULL,
	[LeadTime] [SmallInt] DEFAULT 0 NOT NULL 

) ON [PRIMARY]
GO


ALTER TABLE [dbo].[ALTERNATE_SOURCE] WITH NOCHECK ADD 
	CONSTRAINT [PK_AlternateSource] PRIMARY KEY NONCLUSTERED 
	(	[VnId],[LcId],[Item]
	)  ON [PRIMARY] 
Go


ALTER TABLE [dbo].[ALTERNATE_SOURCE] ADD 
	CONSTRAINT [FK_ALTERNATESOURCE_Item] FOREIGN KEY 
	(       [LcId],
		[Item]
	) REFERENCES [dbo].[Item] (
		[Lcid],
		[Item]
	) ON DELETE CASCADE 
GO


ALTER TABLE [dbo].[ALTERNATE_SOURCE] ADD 
	CONSTRAINT [FK_ALTERNATESOURCE_Vendor] FOREIGN KEY 
	(       [VnId],
		[Assort]
	) REFERENCES [dbo].[AIMVendors] (
		[VnId],
		[Assort]
	) ON DELETE CASCADE 
GO
/****** Object:  Table [dbo].[BuyerStatusTest]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[BuyerStatusTest]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[BuyerStatusTest]
GO

CREATE TABLE [dbo].[BuyerStatusTest] (
	[KeyId] [int] IDENTITY (1, 1) NOT NULL ,
	[Buyer Name] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Total Lines] [int] NULL ,
	[Complete Lines] [int] NULL ,
	[Planned Lines] [int] NULL ,
	[Released Lines] [int] NULL ,
	[Total LT Excpts] [int] NULL ,
	[Complete LT Excpts] [int] NULL ,
	[Planned LT Excpts] [int] NULL ,
	[Released LT Excpts] [int] NULL ,
	[Total Line Reviews] [int] NULL ,
	[Complete Line Reviews] [int] NULL ,
	[Planned Line Reviews] [int] NULL ,
	[Released Line Reviews] [int] NULL ,
	[Total Priority Excepts] [int] NULL ,
	[Complete Priority Excepts] [int] NULL ,
	[Planned Priority Excepts] [int] NULL ,
	[Released Priority Excepts] [int] NULL ,
	CONSTRAINT [PK_BuyerStatusTest] PRIMARY KEY  NONCLUSTERED 
	(
		[KeyId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[DXElements]    Script Date: 05/30/2003 11:04:41 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DXElements]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[DXElements]
GO

CREATE TABLE [dbo].[DXElements] (
	[TxnSet] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Seq] [smallint] NOT NULL ,
	[Element] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Description] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Mask] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Length] [smallint] NOT NULL ,
	[Notes] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	CONSTRAINT [PK_DXElements] PRIMARY KEY  CLUSTERED 
	(
		[TxnSet],
		[Seq]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[DXSets]    Script Date: 05/30/2003 11:04:42 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DXSets]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[DXSets]
GO

CREATE TABLE [dbo].[DXSets] (
	[TxnSet] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Description] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FileFormat] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_DXSets] PRIMARY KEY  CLUSTERED 
	(
		[TxnSet]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   ForecastFilterCriteria.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/09/01
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/

SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[ForecastFilterCriteria]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[ForecastFilterCriteria]
GO

-- location, item, history year, and forecast period (1 to 52)
-- previous value, new value, user, date/time, reason,
CREATE TABLE [dbo].[ForecastFilterCriteria] (
	[FcstSetupKey] int NOT NULL,
	[FilterColumn] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[SearchCondition] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[FilterIndex] int NOT NULL,
	[FilterValue] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT [PK_ForecastFilterCriteria] PRIMARY KEY NONCLUSTERED 
	(
		[FcstSetupKey],
		[FilterColumn],
		[FilterIndex]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ForecastRepository]    Script Date: 05/30/2003 11:04:42 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepository]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepository]
GO

CREATE TABLE [dbo].[ForecastRepository] (
	[RepositoryKey] [numeric](18, 0) IDENTITY (1, 1) NOT NULL ,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserElement] [bit] NOT NULL ,
	[FcstComment] [nvarchar] (4000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserIDCreate] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateTimeCreate] [datetime] NOT NULL ,
	[UserIDEdit] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateTimeEdit] [datetime] NOT NULL, 
	CONSTRAINT [PK_ForecastRepository] PRIMARY KEY  CLUSTERED 
	(
		[RepositoryKey]
	)  ON [PRIMARY] ,
	CONSTRAINT [IX_ForecastRepository] UNIQUE  NONCLUSTERED 
	(
		[FcstID],
		[UserElement]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[ForecastRepositoryArch_Log]    Script Date: 10/12/2004 10:54:10 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepositoryArch_Log]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepositoryArch_Log]
GO

CREATE TABLE [dbo].[ForecastRepositoryArch_Log] (
	[FcstAdjustKey] [int] IDENTITY (1, 1) NOT NULL ,
	[RepositoryKey] [numeric](12, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IsPromoted] [bit] NOT NULL CONSTRAINT [DF_AIMFcstStoreAdjustLogArch_IsPromoted] DEFAULT (0),
	[AdjustType] [tinyint] NULL ,
	[AdjustQty] [decimal](10, 2) NULL ,
	[OverRideEnabled] [smallint] NOT NULL CONSTRAINT [DF_ForecastRepository_LogArch_OverRideEanbled] DEFAULT (0),
	[AdjustBegDate] [datetime] NULL ,
	[AdjustEndDate] [datetime] NULL ,
	[AdjustUserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDateTime] [datetime] NULL ,
	CONSTRAINT [PK_AIMFcstAdjustLogArch] PRIMARY KEY  NONCLUSTERED 
	(
		[FcstAdjustKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[ForecastRepositoryDetail]    Script Date: 11/16/04 1:16:29 PM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepositoryDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepositoryDetail]
GO

/****** Object:  Table [dbo].[ForecastRepositoryDetail]    Script Date: 11/16/04 1:16:30 PM ******/
CREATE TABLE [dbo].[ForecastRepositoryDetail] (
	[RepositoryKey] [numeric](18, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstPdBegDate] [datetime] NOT NULL ,
	[FcstPdEndDate] [datetime] NOT NULL ,
	[Fcst] [int] NOT NULL ,
	[FcstAdj] [int] NOT NULL ,
	[MasterFcstAdj] [int] NOT NULL ,
	[FcstNetReq] [int] NOT NULL ,
	[FcstAdjNetReq] [int] NOT NULL ,
	[QtyAdj] [int] NOT NULL ,
	[QtyAdjOverRide] [int] NULL ,
	[QtyAdjPct] [int] NOT NULL ,
	[AdjOverRide] [smallint] NOT NULL ,
	[MasterQtyAdj] [int] NOT NULL ,
	[MasterQtyAdjOverRide] [int] NULL ,
	[MasterQtyAdjPct] [int] NOT NULL ,
	[MasterAdjOverRide] [smallint] NOT NULL ,
	[HistDmd] [int] NULL ,
	[QtyActualShipped] [int] NULL ,
	[QtyProjectedInventory] [int] NULL ,
	[ProdConst] [int] NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ForecastRepositoryDetail] WITH NOCHECK ADD 
	CONSTRAINT [PK_ForecastRepositoryDetail] PRIMARY KEY  CLUSTERED 
	(
		[RepositoryKey],
		[LcID],
		[Item],
		[FcstPdBegDate]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[ForecastRepositoryDetail] ADD 
	CONSTRAINT [DF_ForecastRepositoryDetail_Fcst] DEFAULT (0) FOR [Fcst],
	CONSTRAINT [DF_ForecastRepositoryDetail_FcstAdj] DEFAULT (0) FOR [FcstAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterFcstAdj] DEFAULT (0) FOR [MasterFcstAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_FcstNetReq] DEFAULT (0) FOR [FcstNetReq],
	CONSTRAINT [DF_ForecastRepositoryDetail_FcstAdjNetReq] DEFAULT (0) FOR [FcstAdjNetReq],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyAdjust] DEFAULT (0) FOR [QtyAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyAdjustPct] DEFAULT (0) FOR [QtyAdjPct],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyAdjOverRide] DEFAULT (0) FOR [AdjOverRide],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterQtyAdj] DEFAULT (0) FOR [MasterQtyAdj],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterQtyAdjPct] DEFAULT (0) FOR [MasterQtyAdjPct],
	CONSTRAINT [DF_ForecastRepositoryDetail_MasterAdjOverRide] DEFAULT (0) FOR [MasterAdjOverRide],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyActualOrdered] DEFAULT (0) FOR [HistDmd],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyActualShipped] DEFAULT (0) FOR [QtyActualShipped],
	CONSTRAINT [DF_ForecastRepositoryDetail_QtyProjectedInventory] DEFAULT (0) FOR [QtyProjectedInventory],
	CONSTRAINT [DF_ForecastRepositoryDetail_ProdConst] DEFAULT (0) FOR [ProdConst]
GO

 CREATE  INDEX [IX_ForecastRepositoryDetail] ON [dbo].[ForecastRepositoryDetail]([RepositoryKey], [LcID], [Item], [FcstPdBegDate], [FcstPdEndDate]) ON [PRIMARY]
GO



if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepository_Log]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepository_Log]
GO

CREATE TABLE [dbo].[ForecastRepository_Log] (
	[FcstAdjustKey] [int] IDENTITY (1, 1) NOT NULL ,
	[RepositoryKey] [numeric](12, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IsPromoted] [bit] NOT NULL ,
	[AdjustType] [tinyint] NULL ,
	[AdjustQty] [decimal](10, 2) NULL ,
	[OverRideEnabled] [smallint] NOT NULL ,
	[AdjustBegDate] [datetime] NULL ,
	[AdjustEndDate] [datetime] NULL ,
	[AdjustUserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AdjustDateTime] [datetime] NULL,
	CONSTRAINT [PK_ForecastRepository_Log] PRIMARY KEY  CLUSTERED 
	(
		[FcstAdjustKey]
	)  ON [PRIMARY] 

) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[ItDefaults]    Script Date: 05/30/2003 11:04:43 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItDefaults]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItDefaults]
GO

CREATE TABLE [dbo].[ItDefaults] (
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Class] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SaId] [nvarchar] (62) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_SaId] DEFAULT ('DEFAULT'),
	[VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_VelCode] DEFAULT ('C'),
	[BuyStrat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_BuyStrat] DEFAULT ('O'),
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_ById] DEFAULT (''),
	[ReplenCost2] [decimal](5, 2) NOT NULL CONSTRAINT [DF_ItDefaults_KFactor2] DEFAULT (0),
	[DSer] [decimal](3, 2) NOT NULL CONSTRAINT [DF_ItDefaults_DSer] DEFAULT (0.95),
	[OptionId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItDefaults_OptionId] DEFAULT (''),
	CONSTRAINT [PK_ItDefaults] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Class]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[Item]    Script Date: 05/30/2003 11:04:44 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Item]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Item]
GO

CREATE TABLE [dbo].[Item] (
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItStat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ActDate] [datetime] NOT NULL CONSTRAINT [DF_Item_ActDate] DEFAULT ('1/1/1990'),
	[InActDate] [datetime] NOT NULL CONSTRAINT [DF_Item_InActDate] DEFAULT ('12/31/9999'),
	[OptionID] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OptionID] DEFAULT ('DEFLT'),
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class1] DEFAULT (''),
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class2] DEFAULT (''),
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class3] DEFAULT (''),
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Class4] DEFAULT (''),
	[BinLocation] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_BinLocation] DEFAULT (''),
	[BuyStrat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_BuyStrat] DEFAULT ('O'),
	[VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VelCode] DEFAULT ('C'),
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VnId] DEFAULT (''),
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Assort] DEFAULT (''),
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_ById] DEFAULT (''),
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_MDC] DEFAULT (''),
	[MDCFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_MDCFlag] DEFAULT ('N'),
	[SaId] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_SaId] DEFAULT (''),
	[PmId] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_PmId] DEFAULT (''),
	[UPC] [nvarchar] (22) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UPC] DEFAULT (''),
	[Weight] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Weight] DEFAULT (0),
	[Cube] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Cube] DEFAULT (0),
	[ListPrice] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_ListPrice] DEFAULT (0),
	[Price] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Price] DEFAULT (0),
	[Cost] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_Cost] DEFAULT (0),
	[BkQty01] [int] NOT NULL CONSTRAINT [DF_Item_BkQty01] DEFAULT (0),
	[BkCost01] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost01] DEFAULT (0),
	[BkQty02] [int] NOT NULL CONSTRAINT [DF_Item_BkQty02] DEFAULT (0),
	[BkCost02] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost02] DEFAULT (0),
	[BkQty03] [int] NOT NULL CONSTRAINT [DF_Item_BkQty021] DEFAULT (0),
	[BkCost03] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost021] DEFAULT (0),
	[BkQty04] [int] NOT NULL CONSTRAINT [DF_Item_BkQty021_1] DEFAULT (0),
	[BkCost04] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost021_1] DEFAULT (0),
	[BkQty05] [int] NOT NULL CONSTRAINT [DF_Item_BkQty022] DEFAULT (0),
	[BkCost05] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost022] DEFAULT (0),
	[BkQty06] [int] NOT NULL CONSTRAINT [DF_Item_BkQty023] DEFAULT (0),
	[BkCost06] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost023] DEFAULT (0),
	[BkQty07] [int] NOT NULL CONSTRAINT [DF_Item_BkQty024] DEFAULT (0),
	[BkCost07] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost024] DEFAULT (0),
	[BkQty08] [int] NOT NULL CONSTRAINT [DF_Item_BkQty021_2] DEFAULT (0),
	[BkCost08] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost021_2] DEFAULT (0),
	[BkQty09] [int] NOT NULL CONSTRAINT [DF_Item_BkQty022_1] DEFAULT (0),
	[BkCost09] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost022_1] DEFAULT (0),
	[BkQty10] [int] NOT NULL CONSTRAINT [DF_Item_BkQty023_1] DEFAULT (0),
	[BkCost10] [decimal](10, 4) NOT NULL CONSTRAINT [DF_Item_BkCost023_1] DEFAULT (0),
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UOM] DEFAULT ('EA'),
	[ConvFactor] [int] NOT NULL CONSTRAINT [DF_Item_ConvFactorPL3] DEFAULT (1),
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_BuyingUOM] DEFAULT ('EA'),
	[ReplenCost2] [decimal](9, 4) NOT NULL CONSTRAINT [DF_Item_KFactor2] DEFAULT (0),
	[Oh] [int] NOT NULL CONSTRAINT [DF_Item_Oh] DEFAULT (0),
	[Oo] [int] NOT NULL CONSTRAINT [DF_Item_Oo] DEFAULT (0),
	[ComStk] [int] NOT NULL CONSTRAINT [DF_Item_ComStk] DEFAULT (0),
	[BkOrder] [int] NOT NULL CONSTRAINT [DF_Item_BkOrder] DEFAULT (0),
	[BkComStk] [int] NOT NULL CONSTRAINT [DF_Item_BkComStk] DEFAULT (0),
	[LeadTime] [smallint] NOT NULL CONSTRAINT [DF_Item_LeadTime] DEFAULT (0),
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_PackRounding] DEFAULT ('R'),
	[IMin] [int] NOT NULL CONSTRAINT [DF_Item_IMin] DEFAULT (0),
	[IMax] [int] NOT NULL CONSTRAINT [DF_Item_IMax] DEFAULT (999999999),
	[CStock] [int] NOT NULL CONSTRAINT [DF_Item_CStock] DEFAULT (0),
	[SSAdj] [decimal](9, 4) NOT NULL CONSTRAINT [DF_Item_SSAdj] DEFAULT (0),
	[UserMin] [int] NOT NULL CONSTRAINT [DF_Item_UserMin] DEFAULT (0),
	[UserMax] [int] NOT NULL CONSTRAINT [DF_Item_UserMax] DEFAULT (999999999),
	[UserMethod] [tinyint] NOT NULL CONSTRAINT [DF_Item_UserMethod] DEFAULT (0),
	[FcstMethod] [tinyint] NOT NULL CONSTRAINT [DF_Item_FcstMethod] DEFAULT (0),
	[FcstDemand] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstDemand] DEFAULT (0),
	[UserFcst] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstUser] DEFAULT (0),
	[UserFcstExpDate] [datetime] NOT NULL CONSTRAINT [DF_Item_FcstUserExpDate] DEFAULT ('1/1/1990'),
	[MAE] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_MAPE] DEFAULT (0),
	[MSE] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_MSPE] DEFAULT (0),
	[Trend] [decimal](10, 3) NOT NULL CONSTRAINT [DF_Item_TrndPct] DEFAULT (0),
	[FcstCycles] [int] NOT NULL CONSTRAINT [DF_Item_FcstCycles] DEFAULT (0),
	[ZeroCount] [int] NOT NULL CONSTRAINT [DF_Item_ZeroCount] DEFAULT (0),
	[Mean_NZ] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Mean_NZ] DEFAULT (0),
	[StdDev_NZ] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_StdDev_NZ] DEFAULT (0),
	[IntSafetyStock] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_IntSafetyStock] DEFAULT (0),
	[IsIntermittent] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_IsIntermittent] DEFAULT ('N'),
	[DIFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_DIFlag] DEFAULT ('N'),
	[DmdFilterFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_DmdFilterFlag] DEFAULT ('N'),
	[TrkSignalFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_TrkSignalFlag] DEFAULT ('N'),
	[UserDemandFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserDemandFlag] DEFAULT ('N'),
	[ByPassPct] [decimal](4, 3) NOT NULL CONSTRAINT [DF_Item_ByPassPct] DEFAULT (0),
	[FcstUpdCyc] [int] NOT NULL CONSTRAINT [DF_Item_FctUpdCyc] DEFAULT (0),
	[LTVFact] [decimal](3, 2) NOT NULL CONSTRAINT [DF_Item_LTVFact] DEFAULT (0),
	[PlnTT] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_PlnTT] DEFAULT ('N'),
	[ZOPSw] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_ZOPSw] DEFAULT ('N'),
	[OUTLSw] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OUTLSw] DEFAULT ('N'),
	[ZSStock] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_ZSStock] DEFAULT ('N'),
	[DSer] [decimal](4, 3) NOT NULL CONSTRAINT [DF_Item_DSer] DEFAULT (0.95),
	[Freeze_BuyStrat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_BuyStrat] DEFAULT ('N'),
	[Freeze_Byid] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_Byid] DEFAULT ('N'),
	[Freeze_LeadTime] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_LeadTime] DEFAULT ('N'),
	[Freeze_OptionID] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_OptionID] DEFAULT ('N'),
	[Freeze_DSer] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_DSer] DEFAULT ('N'),
	[OldItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OldItem] DEFAULT (''),
	[Accum_Lt] [smallint] NOT NULL CONSTRAINT [DF_Item_Accum_Lt] DEFAULT (0),
	[ReviewTime] [smallint] NOT NULL CONSTRAINT [DF_Item_ReviewTime] DEFAULT (0),
	[OrderPt] [int] NOT NULL CONSTRAINT [DF_Item_OrderPt] DEFAULT (0),
	[OrderQty] [int] NOT NULL CONSTRAINT [DF_Item_OrderQty] DEFAULT (0),
	[SafetyStock] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_SafetyStock] DEFAULT (0),
	[FcstRT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstRT] DEFAULT (0),
	[FcstLT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_FcstLT] DEFAULT (0),
	[Fcst_Month] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Fcst_Month] DEFAULT (0),
	[Fcst_Quarter] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Fcst_Quarter] DEFAULT (0),
	[Fcst_Year] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_Fcst_Year] DEFAULT (0),
	[FcstDate] [smalldatetime] NOT NULL CONSTRAINT [DF_Item_FcstDate] DEFAULT ('1/1/1990'),
	[VC_Amt] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VC_Amt] DEFAULT ('C'),
	[VC_Units_Ranking] [int] NOT NULL CONSTRAINT [DF_Item_VC_Units_Ranking] DEFAULT (0),
	[VC_Amt_Ranking] [int] NOT NULL CONSTRAINT [DF_Item_VC_Amt_Ranking] DEFAULT (0),
	[VC_Date] [smalldatetime] NOT NULL CONSTRAINT [DF_Item_VC_Date] DEFAULT ('1/1/1990'),
	[VelCode_Prev] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VelCode_Prev] DEFAULT (''),
	[VC_Amt_Prev] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_VC_Amt_Prev] DEFAULT (''),
	[OnPromotion] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_OnPromotion] DEFAULT ('N'),
	[AvgOh] [decimal](10, 2) NOT NULL CONSTRAINT [DF_Item_AvgOh] DEFAULT (0),
	[NextPONbr_1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_NextPONbr_1] DEFAULT (''),
	[NextPODate_1] [datetime] NOT NULL CONSTRAINT [DF_Item_NextPODate_1] DEFAULT ('1/1/1990'),
	[NextPOQty_1] [int] NOT NULL CONSTRAINT [DF_Item_NextPOQty_1] DEFAULT (0),
	[NextPONbr_2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_NextPONbr_2] DEFAULT (''),
	[NextPODate_2] [datetime] NOT NULL CONSTRAINT [DF_Item_NextPODate_2] DEFAULT ('1/1/1990'),
	[NextPOQty_2] [int] NOT NULL CONSTRAINT [DF_Item_NextPOQty_2] DEFAULT (0),
	[NextPONbr_3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_NextPONbr_3] DEFAULT (''),
	[NextPODate_3] [datetime] NOT NULL CONSTRAINT [DF_Item_NextPODate_3] DEFAULT ('1/1/1990'),
	[NextPOQty_3] [int] NOT NULL CONSTRAINT [DF_Item_NextPOQty_3] DEFAULT (0),
	[UserRef1] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserRef1] DEFAULT (''),
	[UserRef2] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserRef2] DEFAULT (''),
	[UserRef3] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_UserRef3] DEFAULT (''),
	[Freeze_Forecast] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_Freeze_Forecast] DEFAULT (N'N'),
	[ProductionConstraint] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_Item_ProductionConstraint] DEFAULT (N'N'),
	[AllocatableQty] [int] NOT NULL CONSTRAINT [DF_Item_AllocatableQty] DEFAULT (0),
	[AltVnFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS Not Null DEFAULT (N'N'),
	[KitBOMFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS Not Null DEFAULT (N'N'),
    [DependentFcstDemand] [decimal](10,2) NOT NULL CONSTRAINT [DF_Item_DependentFcstDemand] DEFAULT (0),
	[AllocUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_Item_AllocUOM] DEFAULT ('EA'),
	[AllocConvFactor] [int] NOT NULL CONSTRAINT [DF_Item_AllocConvFactor] DEFAULT (1),
	CONSTRAINT [PK_Item] PRIMARY KEY  NONCLUSTERED 
	(
		[Lcid],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_Byid] ON [dbo].[Item]([ById]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_Item] ON [dbo].[Item]([Item]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_ItStat] ON [dbo].[Item]([ItStat]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_VnidAssort] ON [dbo].[Item]([VnId], [Assort]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_MDC] ON [dbo].[Item]([MDC]) ON [PRIMARY]
GO

CREATE INDEX [IX_COVER_ITEM] ON [dbo].[Item]([Class1], [Class2], [Class3], [Class4], [VnId], [Assort], [ById], [SaId], [PmId]) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[ItemHistory]    Script Date: 05/30/2003 11:04:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItemHistory]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItemHistory]
GO

CREATE TABLE [dbo].[ItemHistory] (
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SubsItem][nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[HisYear] [smallint] NOT NULL ,
	[CPS01] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps01] DEFAULT (0),
	[CPS02] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps02] DEFAULT (0),
	[CPS03] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps03] DEFAULT (0),
	[CPS04] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps04] DEFAULT (0),
	[CPS05] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps05] DEFAULT (0),
	[CPS06] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps06] DEFAULT (0),
	[CPS07] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps07] DEFAULT (0),
	[CPS08] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps08] DEFAULT (0),
	[CPS09] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps09] DEFAULT (0),
	[CPS10] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps10] DEFAULT (0),
	[CPS11] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps11] DEFAULT (0),
	[CPS12] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps12] DEFAULT (0),
	[CPS13] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps13] DEFAULT (0),
	[CPS14] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps14] DEFAULT (0),
	[CPS15] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps15] DEFAULT (0),
	[CPS16] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps16] DEFAULT (0),
	[CPS17] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps17] DEFAULT (0),
	[CPS18] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps18] DEFAULT (0),
	[CPS19] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps19] DEFAULT (0),
	[CPS20] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps20] DEFAULT (0),
	[CPS21] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps21] DEFAULT (0),
	[CPS22] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps22] DEFAULT (0),
	[CPS23] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps23] DEFAULT (0),
	[CPS24] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps24] DEFAULT (0),
	[CPS25] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps25] DEFAULT (0),
	[CPS26] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps26] DEFAULT (0),
	[CPS27] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps27] DEFAULT (0),
	[CPS28] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps28] DEFAULT (0),
	[CPS29] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps29] DEFAULT (0),
	[CPS30] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps30] DEFAULT (0),
	[CPS31] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps31] DEFAULT (0),
	[CPS32] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps32] DEFAULT (0),
	[CPS33] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps33] DEFAULT (0),
	[CPS34] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps34] DEFAULT (0),
	[CPS35] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps35] DEFAULT (0),
	[CPS36] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps36] DEFAULT (0),
	[CPS37] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps37] DEFAULT (0),
	[CPS38] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps38] DEFAULT (0),
	[CPS39] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps39] DEFAULT (0),
	[CPS40] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps40] DEFAULT (0),
	[CPS41] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps41] DEFAULT (0),
	[CPS42] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps42] DEFAULT (0),
	[CPS43] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps43] DEFAULT (0),
	[CPS44] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps44] DEFAULT (0),
	[CPS45] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps45] DEFAULT (0),
	[CPS46] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps46] DEFAULT (0),
	[CPS47] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps47] DEFAULT (0),
	[CPS48] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps48] DEFAULT (0),
	[CPS49] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps49] DEFAULT (0),
	[CPS50] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps50] DEFAULT (0),
	[CPS51] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps51] DEFAULT (0),
	[CPS52] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_cps52] DEFAULT (0),
	[QtyOrd01] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord01] DEFAULT (0),
	[QtyOrd02] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord02] DEFAULT (0),
	[QtyOrd03] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord021] DEFAULT (0),
	[QtyOrd04] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord022] DEFAULT (0),
	[QtyOrd05] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord023] DEFAULT (0),
	[QtyOrd06] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord024] DEFAULT (0),
	[QtyOrd07] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord025] DEFAULT (0),
	[QtyOrd08] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord026] DEFAULT (0),
	[QtyOrd09] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord027] DEFAULT (0),
	[QtyOrd10] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord028] DEFAULT (0),
	[QtyOrd11] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord029] DEFAULT (0),
	[QtyOrd12] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0210] DEFAULT (0),
	[QtyOrd13] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0211] DEFAULT (0),
	[QtyOrd14] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0212] DEFAULT (0),
	[QtyOrd15] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0213] DEFAULT (0),
	[QtyOrd16] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0214] DEFAULT (0),
	[QtyOrd17] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0215] DEFAULT (0),
	[QtyOrd18] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0216] DEFAULT (0),
	[QtyOrd19] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0217] DEFAULT (0),
	[QtyOrd20] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0218] DEFAULT (0),
	[QtyOrd21] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0219] DEFAULT (0),
	[QtyOrd22] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0220] DEFAULT (0),
	[QtyOrd23] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0221] DEFAULT (0),
	[QtyOrd24] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0222] DEFAULT (0),
	[QtyOrd25] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0223] DEFAULT (0),
	[QtyOrd26] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0224] DEFAULT (0),
	[QtyOrd27] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0225] DEFAULT (0),
	[QtyOrd28] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0226] DEFAULT (0),
	[QtyOrd29] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0227] DEFAULT (0),
	[QtyOrd30] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0228] DEFAULT (0),
	[QtyOrd31] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0229] DEFAULT (0),
	[QtyOrd32] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0230] DEFAULT (0),
	[QtyOrd33] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0231] DEFAULT (0),
	[QtyOrd34] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0232] DEFAULT (0),
	[QtyOrd35] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0233] DEFAULT (0),
	[QtyOrd36] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0234] DEFAULT (0),
	[QtyOrd37] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0235] DEFAULT (0),
	[QtyOrd38] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0236] DEFAULT (0),
	[QtyOrd39] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0237] DEFAULT (0),
	[QtyOrd40] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0238] DEFAULT (0),
	[QtyOrd41] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0239] DEFAULT (0),
	[QtyOrd42] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0240] DEFAULT (0),
	[QtyOrd43] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0241] DEFAULT (0),
	[QtyOrd44] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0242] DEFAULT (0),
	[QtyOrd45] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0243] DEFAULT (0),
	[QtyOrd46] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0244] DEFAULT (0),
	[QtyOrd47] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0245] DEFAULT (0),
	[QtyOrd48] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0246] DEFAULT (0),
	[QtyOrd49] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0247] DEFAULT (0),
	[QtyOrd50] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0248] DEFAULT (0),
	[QtyOrd51] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0249] DEFAULT (0),
	[QtyOrd52] [decimal](9, 1) NOT NULL CONSTRAINT [DF_ItemHistory_qtyord0250] DEFAULT (0),
	[OrdCnt01] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt01] DEFAULT (0),
	[OrdCnt02] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011] DEFAULT (0),
	[OrdCnt03] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011_1] DEFAULT (0),
	[OrdCnt04] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt012] DEFAULT (0),
	[OrdCnt05] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt013] DEFAULT (0),
	[OrdCnt06] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011_2] DEFAULT (0),
	[OrdCnt07] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt012_1] DEFAULT (0),
	[OrdCnt08] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt013_1] DEFAULT (0),
	[OrdCnt09] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt014] DEFAULT (0),
	[OrdCnt10] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt015] DEFAULT (0),
	[OrdCnt11] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt016] DEFAULT (0),
	[OrdCnt12] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt017] DEFAULT (0),
	[OrdCnt13] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt018] DEFAULT (0),
	[OrdCnt14] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt019] DEFAULT (0),
	[OrdCnt15] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt0110] DEFAULT (0),
	[OrdCnt16] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt011_3] DEFAULT (0),
	[OrdCnt17] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt012_2] DEFAULT (0),
	[OrdCnt18] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt013_2] DEFAULT (0),
	[OrdCnt19] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt014_1] DEFAULT (0),
	[OrdCnt20] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt015_1] DEFAULT (0),
	[OrdCnt21] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt016_1] DEFAULT (0),
	[OrdCnt22] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt017_1] DEFAULT (0),
	[OrdCnt23] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt018_1] DEFAULT (0),
	[OrdCnt24] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt241] DEFAULT (0),
	[OrdCnt25] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt242] DEFAULT (0),
	[OrdCnt26] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt243] DEFAULT (0),
	[OrdCnt27] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt244] DEFAULT (0),
	[OrdCnt28] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt245] DEFAULT (0),
	[OrdCnt29] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt291] DEFAULT (0),
	[OrdCnt30] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt301] DEFAULT (0),
	[OrdCnt31] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt302] DEFAULT (0),
	[OrdCnt32] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt303] DEFAULT (0),
	[OrdCnt33] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt304] DEFAULT (0),
	[OrdCnt34] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt305] DEFAULT (0),
	[OrdCnt35] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt306] DEFAULT (0),
	[OrdCnt36] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt307] DEFAULT (0),
	[OrdCnt37] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt308] DEFAULT (0),
	[OrdCnt38] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt309] DEFAULT (0),
	[OrdCnt39] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt3010] DEFAULT (0),
	[OrdCnt40] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt401] DEFAULT (0),
	[OrdCnt41] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt402] DEFAULT (0),
	[OrdCnt42] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt403] DEFAULT (0),
	[OrdCnt43] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt404] DEFAULT (0),
	[OrdCnt44] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt405] DEFAULT (0),
	[OrdCnt45] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt406] DEFAULT (0),
	[OrdCnt46] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt407] DEFAULT (0),
	[OrdCnt47] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt408] DEFAULT (0),
	[OrdCnt48] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt409] DEFAULT (0),
	[OrdCnt49] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt4010] DEFAULT (0),
	[OrdCnt50] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt501] DEFAULT (0),
	[OrdCnt51] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt502] DEFAULT (0),
	[OrdCnt52] [int] NOT NULL CONSTRAINT [DF_ItemHistory_OrdCnt511] DEFAULT (0),
	CONSTRAINT [PK_ItemHistory] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Item],
		[SubsItem],
		[HisYear]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   ItemHistoryAdjLog.TAB
--
--   Version Number - 1.0
--   Last Updated   - 2004/08/05
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/

SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[ItemHistoryAdjLog]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[ItemHistoryAdjLog]
GO

-- location, item, history year, and forecast period (1 to 52)
-- previous value, new value, user, date/time, reason,
CREATE TABLE [dbo].[ItemHistoryAdjLog] (
	[AdjustLogKey] [bigint] IDENTITY,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[SubsItem] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[HisYear] [smallint] NOT NULL,
	[DemandPeriod] [tinyint] NOT NULL,
	[DemandSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[OldValue] [decimal] (9, 1) NOT NULL,
	[NewValue] [decimal] (9, 1) NOT NULL,
	[AdjustDateTime] [datetime] NOT NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[Reason] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT [PK_ItemHistoryAdjLog] PRIMARY KEY  NONCLUSTERED 
	(
		[AdjustLogKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX IDX_AIMItemHistoryAdjLog_Cover ON ItemHistoryAdjLog (
	[LcID],
	[Item],
	[HisYear],
	[DemandSource],
	[DemandPeriod],
	[AdjustDateTime]
)
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItemKitBOM]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItemKitBOM]
GO

CREATE TABLE [ItemKitBOM] (
	[Lcid] [nvarchar] (12)  NOT NULL ,
	[Item] [nvarchar] (25)  NOT NULL ,
	[ItemComponent] [nvarchar] (25) NOT NULL ,
	[ComponentUnits] [int] NOT NULL ,
	[ComponentScrap] [decimal](10, 4) NOT NULL ,
	[StartDate] [datetime] NOT NULL ,
	[EndDate] [datetime] NOT NULL ,
	[Enabled][nvarchar] (1) Not NULL CONSTRAINT [ItemKitBOM_Enabled] DEFAULT (N'N')
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ItemPricing]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItemPricing]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItemPricing]
GO

CREATE TABLE [dbo].[ItemPricing] (
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[EffectiveDatetime] [datetime] NOT NULL ,
	[Cost] [decimal](10, 4) NULL ,
	[Price] [decimal](10, 4) NULL ,
	[ListPrice] [decimal](10, 4) NULL ,
	CONSTRAINT [PK_ItemPricing] PRIMARY KEY  CLUSTERED 
	(
		[LcID],
		[Item],
		[EffectiveDatetime]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[ItStatus]    Script Date: 05/30/2003 11:04:44 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ItStatus]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ItStatus]
GO

CREATE TABLE [dbo].[ItStatus] (
	[ItStat] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItStatDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Ordgen] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DmdUpd] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VCAssn] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CanPurge] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_ItStatus_CanPurge] DEFAULT ('N'),
	[BuyerReviewSeq] [tinyint] NOT NULL CONSTRAINT [DF_ItStatus_BuyerReviewSeq] DEFAULT (0),
	[InclInSeasonsGen] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_ItStat] PRIMARY KEY  CLUSTERED 
	(
		[ItStat]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[KitBOMSummary]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[KitBOMSummary]
GO
CREATE TABLE [KitBOMSummary] (
	[LcId] [nvarchar] (12)  NOT NULL ,
	[Item] [nvarchar] (25)  NOT NULL ,
	[Accum_Lt] [smallint] NOT NULL ,
	[ReviewTime] [smallint] NOT NULL ,
	[OrderPt] [int] NOT NULL ,
	[OrderQty] [int] NOT NULL ,
	[SafetyStock] [decimal](10, 2) NOT NULL ,
	[FcstDemand] [decimal](10, 2) NOT NULL ,
	[DependentFcstDemand] [decimal](10, 2) NOT NULL ,
	[FcstRT] [decimal](10, 2) NOT NULL ,
	[FcstLT] [decimal](10, 2) NOT NULL ,
	[Fcst_Month] [decimal](10, 2) NOT NULL ,
	[Fcst_Quarter] [decimal](10, 2) NOT NULL ,
	[Fcst_Year] [decimal](10, 2) NOT NULL 
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[MDCDetail]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[MDCDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[MDCDetail]
GO

CREATE TABLE [dbo].[MDCDetail] (
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstDate] [datetime] NOT NULL ,
	[AvailQty] [int] NOT NULL ,
	[FcstDemand] [decimal](10, 2) NOT NULL ,
	[MAE] [decimal](10, 2) NOT NULL ,
	[Trend] [decimal](10, 3) NOT NULL ,
	[RqQty01] [int] NOT NULL ,
	[RqQty02] [int] NOT NULL ,
	[RqQty03] [int] NOT NULL ,
	[RqQty04] [int] NOT NULL ,
	[RqQty05] [int] NOT NULL ,
	[RqQty06] [int] NOT NULL ,
	[RqQty07] [int] NOT NULL ,
	[RqQty08] [int] NOT NULL ,
	[RqQty09] [int] NOT NULL ,
	[RqQty10] [int] NOT NULL ,
	[RqQty11] [int] NOT NULL ,
	[RqQty12] [int] NOT NULL ,
	[RqQty13] [int] NOT NULL ,
	[RqQty14] [int] NOT NULL ,
	[RqQty15] [int] NOT NULL ,
	[RqQty16] [int] NOT NULL ,
	[RqQty17] [int] NOT NULL ,
	[RqQty18] [int] NOT NULL ,
	[RqQty19] [int] NOT NULL ,
	[RqQty20] [int] NOT NULL ,
	[RqQty21] [int] NOT NULL ,
	[RqQty22] [int] NOT NULL ,
	[RqQty23] [int] NOT NULL ,
	[RqQty24] [int] NOT NULL ,
	[RqQty25] [int] NOT NULL ,
	[RqQty26] [int] NOT NULL ,
	CONSTRAINT [PK_MDCDetail] PRIMARY KEY  NONCLUSTERED 
	(
		[LcId],
		[Item]
	)  ON [PRIMARY] ,
	CONSTRAINT [IX_MDCItem] UNIQUE  NONCLUSTERED 
	(
		[MDC],
		[Item],
		[LcId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[MDCSummary]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[MDCSummary]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[MDCSummary]
GO

CREATE TABLE [dbo].[MDCSummary] (
	[MDC] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Accum_Lt] [smallint] NOT NULL CONSTRAINT [DF_MDCSummary_Accum_Lt] DEFAULT (0),
	[ReviewTime] [smallint] NOT NULL CONSTRAINT [DF_MDCSummary_ReviewTime] DEFAULT (0),
	[OrderPt] [int] NOT NULL CONSTRAINT [DF_MDCSummary_OrderPt] DEFAULT (0),
	[OrderQty] [int] NOT NULL CONSTRAINT [DF_MDCSummary_OrderQty] DEFAULT (0),
	[SafetyStock] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_SafetyStock] DEFAULT (0),
	[FcstRT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_FcstRT] DEFAULT (0),
	[FcstLT] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_FcstLT] DEFAULT (0),
	[Fcst_Month] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_Fcst_Month] DEFAULT (0),
	[Fcst_Quarter] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_Fcst_Quarter] DEFAULT (0),
	[Fcst_Year] [decimal](10, 2) NOT NULL CONSTRAINT [DF_MDCSummary_Fcst_Year] DEFAULT (0),
	CONSTRAINT [PK_MDCSummary] PRIMARY KEY  NONCLUSTERED 
	(
		[MDC],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[PODetail]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[PODetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[PODetail]
GO

CREATE TABLE [dbo].[PODetail] (
	[POLineType] [smallint] NOT NULL ,
	[POSeqID] [int] IDENTITY (1, 1) NOT NULL ,
	[OrdStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_OrdStatus] DEFAULT ('P'),
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItDesc] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_ItDesc] DEFAULT (''),
	[POType] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AvailQty] [int] NOT NULL ,
	[PackRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RSOQ] [int] NOT NULL ,
	[SOQ] [int] NOT NULL ,
	[VSOQ] [int] NOT NULL ,
	[Original_VSOQ] [int] NOT NULL CONSTRAINT [DF_PODetail_Original_VSOQ] DEFAULT (0),
	[UOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[BuyingUOM] [nvarchar] (6) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ConvFactor] [int] NOT NULL ,
	[IsDate] [datetime] NOT NULL ,
	[DuDate] [datetime] NOT NULL ,
	[LastWeekSalesFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_LastWeekSalesFlag] DEFAULT ('N'),
	[Cost] [numeric](10, 4) NOT NULL CONSTRAINT [DF_PODetail_Cost] DEFAULT (0),
	[Zone] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_Zone] DEFAULT (''),
	[VendorCostExcpts] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_PODetail_VendorCostExcpts] DEFAULT ('N'),
	CONSTRAINT [PK_PODetail] PRIMARY KEY  NONCLUSTERED 
	(
		[POSeqID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_ById] ON [dbo].[PODetail]([ById]) ON [PRIMARY]
GO

 CREATE  UNIQUE  INDEX [IX_VnIdAssort] ON [dbo].[PODetail]([ById], [VnId], [Assort], [Item], [Lcid]) ON [PRIMARY]
GO

CREATE INDEX [IDX_PODet_ItemLcId] ON [dbo].[PODetail]([LcId],[Item]) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POStatistics]    Script Date: 05/30/2003 11:04:53 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[POStatistics]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[POStatistics]
GO

CREATE TABLE [dbo].[POStatistics] (
	[SeqNbr] [int] NOT NULL ,
	[ById] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[VnId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[IsDate] [datetime] NOT NULL ,
	[Original_NbrLines] [int] NOT NULL ,
	[NbrLines] [int] NOT NULL ,
	[Original_VSOQ] [int] NOT NULL ,
	[VSOQ] [int] NOT NULL ,
	[Original_ExtCost] [decimal](10, 2) NOT NULL ,
	[ExtCost] [decimal](10, 2) NOT NULL ,
	CONSTRAINT [PK_POStatistics] PRIMARY KEY  CLUSTERED 
	(
		[ById],
		[VnId],
		[Assort],
		[SeqNbr]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

 CREATE  INDEX [IX_ById] ON [dbo].[POStatistics]([ById], [IsDate]) ON [PRIMARY]
GO

 CREATE  INDEX [IX_VnIdAssort] ON [dbo].[POStatistics]([VnId], [Assort], [IsDate]) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[ProdConstraintTemp]    Script Date: 05/30/2003 11:04:54 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ProdConstraintTemp]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ProdConstraintTemp]
GO

CREATE TABLE [dbo].[ProdConstraintTemp] (
	[UniqueJobId] [uniqueidentifier] NOT NULL ,
	[Lcid] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Qty] [int] NOT NULL CONSTRAINT [DF_Prodqty_Qty] DEFAULT (0),
	[AdjQty] [int] NOT NULL CONSTRAINT [DF_Prodqty_AdjQty] DEFAULT (0),
	[DateCreated] [datetime] NOT NULL 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[RevCycles]    Script Date: 05/30/2003 11:04:54 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RevCycles]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RevCycles]
GO

CREATE TABLE [dbo].[RevCycles] (
	[RevCycle] [nvarchar] (8) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RevCycleDesc] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_RevCycle_RevCycleDesc] DEFAULT (''),
	[NextDateTime] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_NextDateTime] DEFAULT ('1/1/1990'),
	[RevStatus] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_RevStatus] DEFAULT (1),
	[DynamicFlag] [tinyint] NOT NULL CONSTRAINT [DF_RevCycles_DynamicFlag] DEFAULT (0),
	[RevFreq] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_RevFreq] DEFAULT (0),
	[RevInterval] [smallint] NOT NULL CONSTRAINT [DF_RevCycle_RevInterval] DEFAULT (1),
	[RevSunday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Sunday] DEFAULT (0),
	[RevMonday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Monday] DEFAULT (1),
	[RevTuesday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Tuesday] DEFAULT (1),
	[RevWednesday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Wednesday] DEFAULT (1),
	[RevThursday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Thursday] DEFAULT (1),
	[RevFriday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Friday] DEFAULT (1),
	[RevSaturday] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_Saturday] DEFAULT (1),
	[WeekQualifier] [tinyint] NOT NULL CONSTRAINT [DF_RevCycle_WeekQualifier] DEFAULT (0),
	[RevStartDate] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_StartDate] DEFAULT ('1/1/1990'),
	[RevEndDate] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_EndDate] DEFAULT ('1/1/1990'),
	[InitBuyPct] [decimal](5, 4) NOT NULL CONSTRAINT [DF_RevCycle_InitBuyPct] DEFAULT (1),
	[InitRevDate] [datetime] NOT NULL CONSTRAINT [DF_RevCycle_InitRevDate] DEFAULT ('01/01/1990'),
	[SeasonalReview] [tinyint] NOT NULL CONSTRAINT [DF_RevCycles_SeasonalReview] DEFAULT (0),
	[ReviewTime] [smallint] NOT NULL CONSTRAINT [DF_RevCycles_ReviewTime] DEFAULT (1),
	[OrdGenStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_RevCycles_OrdGenStatus] DEFAULT (''),
	CONSTRAINT [PK_RevCycle] PRIMARY KEY  NONCLUSTERED 
	(
		[RevCycle]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


/****** Object:  Table [dbo].[SSA_DR_Migration]    Script Date: 05/30/2003 11:04:42 AM ******/
-- Global uniqueness would be necessary for these Migration_Log tables
--  because the database migration script copies the contents of these as well.
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[SSA_DR_Migration]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[SSA_DR_Migration]
GO

CREATE TABLE [dbo].[SSA_DR_Migration] (
	[EAM_ID] uniqueidentifier Default NewID() NOT NULL,
	[ProductName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OldVersion] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[NewVersion] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TransactionCount] [bigint] NOT NULL ,
	[TotalTimeMinute] [int] NOT NULL ,
	[StartTime] [datetime] NOT NULL ,
	[EndTime] [datetime] NOT NULL ,
	CONSTRAINT [PK_SSA_DR_Migration] PRIMARY KEY  CLUSTERED 
	(
		[EAM_ID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX EAM_ProdNewVer ON SSA_DR_Migration (ProductName, NewVersion)
GO
/****** Object:  Table [dbo].[SSA_DR_Migration_Log]    Script Date: 05/30/2003 11:04:42 AM ******/
-- Global uniqueness would be necessary for these Migration_Log tables
--  because the database migration script copies the contents of these as well.
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[SSA_DR_Migration_Log]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[SSA_DR_Migration_Log]
GO

CREATE TABLE [dbo].[SSA_DR_Migration_Log] (
	[EAM_ID] uniqueidentifier NOT NULL,
	[ProductName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[OldVersion] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[NewVersion] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TableName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DefaultCount] [bigint] NOT NULL ,
	[OldDatabaseCount] [bigint] NOT NULL ,
	[NewDatabaseCount] [bigint] NOT NULL ,
	[TotalTimeMillisecond] [int] NOT NULL ,
	[StartTime] [datetime] NOT NULL ,
	[EndTime] [datetime] NOT NULL ,
	CONSTRAINT [PK_SSA_DR_Migration_Log] PRIMARY KEY  CLUSTERED 
	(
		[EAM_ID],
		[ProductName],
		[NewVersion],
		[TableName]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO



/****** Object:  Table [dbo].[SysCtrl]    Script Date: 05/30/2003 11:04:54 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[SysCtrl]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[SysCtrl]
GO

CREATE TABLE [dbo].[SysCtrl] (
	[CName] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Address1] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Address2] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Address3] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[City] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[State] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CoZip] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Dft_MinRchPct] [numeric](3, 1) NOT NULL ,
	[Dft_BestRchPct] [numeric](3, 1) NOT NULL ,
	[DxPath] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DxPath_Out] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DxPath_Backup] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Dft_POStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DxPO_Option] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[KFactor] [decimal](5, 2) NOT NULL CONSTRAINT [DF_SysCtrl_KFactor] DEFAULT (30),
	[MinROAI] [decimal](5, 2) NOT NULL CONSTRAINT [DF_SysCtrl_MinROAI] DEFAULT (20),
	[Vendor_Sizing] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Priority_Min_Dser] [numeric](3, 2) NOT NULL ,
	[Priority_Min_VelCode] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Addl_LTDays] [tinyint] NOT NULL ,
	[Lt_Filter_Pct] [numeric](3, 2) NOT NULL ,
	[Retain_SD_Opt] [tinyint] NOT NULL ,
	[Calc_Perform] [tinyint] NOT NULL ,
	[LogFile] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ByIdSource] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_ByIdSource] DEFAULT ('V'),
	[SeasSmoothingPds] [tinyint] NOT NULL CONSTRAINT [DF_SysCtrl_SeasSmoothingPds] DEFAULT (5),
	[CurSaVersion] [smallint] NOT NULL CONSTRAINT [DF_SysCtrl_CurSaVersion] DEFAULT (0),
	[Last_POSeq] [int] NOT NULL CONSTRAINT [DF_SysCtrl_Last_POSeq] DEFAULT (0),
	[ClassOption] [tinyint] NOT NULL CONSTRAINT [DF_SysCtrl_ClassOption] DEFAULT (1),
	[AvgInvAlpha] [decimal](3, 2) NOT NULL CONSTRAINT [DF_SysCtrl_AvgInvAlpha] DEFAULT (0.05),
	[UpdateGoalsOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_UpdateGoalsOption] DEFAULT ('Y'),
	[UpdateVnLT] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_UpdateVnLT] DEFAULT ('Y'),
	[VnLTPctChgFilter] [decimal](5, 2) NOT NULL CONSTRAINT [DF_SysCtrl_VnLTPctChgFilter] DEFAULT (0.5),
	[VnLTDaysChgFilter] [smallint] NOT NULL CONSTRAINT [DF_SysCtrl_VnLTDaysChgFilter] DEFAULT (1),
	[MDCOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_MDCOption] DEFAULT ('N'),
	[LT_Alpha] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_LT_Alpha] DEFAULT (0.1),
	[LT_CtrlInterval_Pct] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_LT_CtrlInterval_Pct] DEFAULT (3),
	[Int_Enabled] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_Int_Enabled] DEFAULT ('N'),
	[Int_MinPctZero] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_MinPctZero] DEFAULT (0.3),
	[Int_MaxPctZero] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_MaxPctZero] DEFAULT (0.9),
	[Int_SeasonalityIndex] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_SeasonalityIndex] DEFAULT (1.000),
	[Int_SrvLvlOverrideFlag] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_Int_SrvLvlOverrideFlag] DEFAULT ('Y'),
	[Int_SrvLvl] [decimal](5, 3) NOT NULL CONSTRAINT [DF_SysCtrl_Int_SrvLvl] DEFAULT (0.85),
	[Int_LookBackPds] [int] NOT NULL CONSTRAINT [DF_SysCtrl_Int_LookBackPds] DEFAULT (52),
	[AIMBatchPath] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_AIMBatchPath] DEFAULT (''),
	[dft_LangID] [nvarchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_dft_LangID] DEFAULT ('EN-US'),
	[ColonOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[GridAutoSizeOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateFormat] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TimeFormat] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UnicodeOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ProductionConstraint] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_ProductionConstraint] DEFAULT (N'Y'),
	[ProdConstraintGrtZero] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL CONSTRAINT [DF_SysCtrl_ProdConstraintGrtZero] DEFAULT (N'Y'),
	[AllocExceptionPct] [int] NULL,
	[SessionID] [bigint] NULL,
	[AllocNeedDetermination] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AllocExceptionProcess] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CarryForwardRounding] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_CarryForwardRounding] DEFAULT (N'Y'),
	[LastOrderGenerationProcess] [datetime] NULL,
	[MasterItemOption] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_MasterItemOption] DEFAULT (N'N'),
	[CreateDBUser] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_CreateDBUser] DEFAULT (N'Y'),
	[UpdateDBPassword] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_UpdateDBPassword] DEFAULT (N'Y'),
	[AllocPrepackEnabled] [bit] NOT NULL DEFAULT 0,
	[AllowNegativeAvailQty] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_SysCtrl_AllowNegativeAvailQty] DEFAULT (N'Y')
	
) ON [PRIMARY]
GO



if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[UserElement]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[UserElement]
GO

CREATE TABLE [dbo].[UserElement] (
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserElementId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserElementDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstType] [nchar] (15) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Processed_YN] [bit] NOT NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[UserElement] WITH NOCHECK ADD 
	CONSTRAINT [PK_UserElement] PRIMARY KEY  CLUSTERED 
	(
		[UserElementId]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[UserElement] ADD 
	CONSTRAINT [DF_UserElement_Processed_YN] DEFAULT (0) FOR [Processed_YN]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[UserElementDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[UserElementDetail]
GO

CREATE TABLE [dbo].[UserElementDetail] (
	[UserElementId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstPdBegDate] [datetime] NOT NULL ,
	[FcstPdEndDate] [datetime] NOT NULL ,
	[Qty] [int] NOT NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[UserElementDetail] WITH NOCHECK ADD 
	CONSTRAINT [PK_UserElementDetail] PRIMARY KEY  CLUSTERED 
	(
		[UserElementId],
		[LcId],
		[Item],
		[FcstPdBegDate]
	)  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[UserElementDetail] ADD 
	CONSTRAINT [DF_UserElementDetail_Qty] DEFAULT (0) FOR [Qty]
GO



