@echo off
set prompt=()
setlocal
cls

echo Created Nov. 18 2004 by Annalakshmi Stocksdale.
echo Updated Nov. 22 2004 by Annalakshmi Stocksdale.
echo -------------------------------------------------------------------------------
echo This script is meant for one-time use in copying SSA DR's demo data to flat files.
echo Before using this script, please update the following variables:
echo 	SS_WORKING_DIR
echo 	SCRIPTS_DIR
echo 	DATA_DIR
echo 	LOG_FILE
echo to reflect your local settings.
echo NOTE: Be sure to update the path in the sql script file - "InsertData_Demo.sql" - as well.
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Questions/Comments, please feel free to write to
echo 	<PERSON><PERSON><PERSON><PERSON>.<EMAIL>
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo Required Software: 
echo 	Microsoft SQL Server 2000 server with Query Analyser
echo ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
echo IMPORTANT! Please ensure that the target machine has:
echo 	(1) Microsoft Visual SourceSafe 6.0 installed and configured with a valid working directory
echo 	(2) the latest versions from SourceSafe of the SSA DR SQL scripts 
echo 		meant for the creation of tables, stored procedures and triggers
echo 	(3) Microsoft SQL Server (2000) and SQL Server agent services running
echo -------------------------------------------------------------------------------

SET SS_WORKING_DIR=C:\SSA_DR\
SET SCRIPTS_DIR=EXceedAIMProject\Database\Scripts\CreateDBs\
SET DATA_DIR=DataFiles_Demo\
SET SOURCEDB=SSA_DR_4_6_DEMO.
SET SOURCESERVER=usdadastocksd1
SET LOG_FILE=DemoDataTransfer.RPT

echo -------------------------------------------------------------------------------
echo Starting the bcp utility for forty-nine (49) tables...

echo "Table: 1" > %LOG_FILE%
bcp "SELECT VnId, Assort, MDCFlag, VName, VAddress1, VAddress2, VCity, VState, VZip, VContact, VPhone, VFax, VEMail, VDocEMail, Dft_Byid, RevCycle, Dft_LeadTime, Last_Dft_LeadTime, Vn_Min, Vn_Best, Reach_Code, POByZone, VnComments1, VnComments2, LastWeekSalesFlag, TransmitPO, ShipIns FROM %SOURCEDB%dbo.AIMVendors" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMVendors.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%


echo "Table: 2" >> %LOG_FILE%
bcp "SELECT Lcid, LName, LType, LStatus, LDivision, LRegion, LUserDefined, LAddress1, LAddress2, LAddress3, LAddress4, LCity, LState, LZip, LCountry, LContact, LPhone, LFax, LEMail, LRank, PutAwayDays, ReplenCost, DemandSource, LeadTimeSource, StkDate, Dft_Byid, Last_FcstUpdCyc, LookBackPds, DmdScalingFactor, ScalingEffUntil, Freeze_Period, UpdateCurrentYearOption, DropShip_XDock, Dft_ReviewerID, ExceptionPct FROM %SOURCEDB%dbo.AIMLocations" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMLocations.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 3 -- The columns are too wide for bcp, this table's to be handled via DTS." >> %LOG_FILE%
REM bcp "SELECT Lcid, Item, ItDesc, ItStat, ActDate, InActDate, OptionID, Class1, Class2, Class3, Class4, BinLocation, BuyStrat, VelCode, VnId, Assort, ById, MDC, MDCFlag, SaId, PmId, UPC, Weight, Cube, ListPrice, Price, Cost, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, UOM, ConvFactor, BuyingUOM, ReplenCost2, Oh, Oo, ComStk, BkOrder, BkComStk, LeadTime, PackRounding, IMin, IMax, CStock, SSAdj, UserMin, UserMax, UserMethod, FcstMethod, FcstDemand, UserFcst, UserFcstExpDate, MAE, MSE, Trend, FcstCycles, ZeroCount, Mean_NZ, StdDev_NZ, IntSafetyStock, IsIntermittent, DIFlag, DmdFilterFlag, TrkSignalFlag, UserDemandFlag, ByPassPct, FcstUpdCyc, LTVFact, PlnTT, ZOPSw, OUTLSw, ZSStock, DSer, Freeze_BuyStrat, Freeze_Byid, Freeze_LeadTime, Freeze_OptionID, Freeze_DSer, OldItem, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year, FcstDate, VC_Amt, VC_Units_Ranking, VC_Amt_Ranking, VC_Date, VelCode_Prev, VC_Amt_Prev, OnPromotion, AvgOh, NextPONbr_1, NextPODate_1, NextPOQty_1, NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, UserRef1, UserRef2, UserRef3, Freeze_Forecast, ProductionConstraint, AllocatableQty, AltVnFlag, KitBOMFlag, DependentFcstDemand, 'EA', 1 FROM %SOURCEDB%dbo.Item" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%Item.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 4" >> %LOG_FILE%
bcp "SELECT MasterItem, Lcid, EnableY_N, CompanionDesc FROM %SOURCEDB%dbo.AIMCompanionItem" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMCompanionItem.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 5" >> %LOG_FILE%
bcp "SELECT MasterItem, Lcid, Item, Qty, DemandFactor, Startdate, Enddate, ExcludeIndepDemY_N FROM %SOURCEDB%dbo.AIMCompanionItemDetail" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMCompanionItemDetail.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 6" >> %LOG_FILE%
bcp "SELECT AllocPrimaryItem, AllocSubstItem, AllocSubstPriority FROM %SOURCEDB%dbo.AllocItemSubstitutes" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocItemSubstitutes.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 7" >> %LOG_FILE%
bcp "SELECT SessionID, DestinationPriority, ItemID, TotalRequestedQty, SumNeedRatios FROM %SOURCEDB%dbo.AllocScratch_ItemDemand" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocScratch_ItemDemand.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 8 -- The columns are too wide for bcp, this table's to be handled via DTS." >> %LOG_FILE%
REM bcp "SELECT LcId, Item, SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, CPS50, CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52 FROM %SOURCEDB%dbo.ItemHistory OPTION ROBUST PLAN" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ItemHistory.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 9 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT AdjustLogKey, LcID, Item, SubsItem, HisYear, DemandPeriod, DemandSource, OldValue, NewValue, AdjustDateTime, UserID, Reason FROM %SOURCEDB%dbo.ItemHistoryAdjLog" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ItemHistoryAdjLog.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 10" >> %LOG_FILE%
bcp "SELECT Lcid, Item, ItemComponent, ComponentUnits, ComponentScrap, StartDate, EndDate, Enabled FROM %SOURCEDB%dbo.ItemKitBOM" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ItemKitBOM.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 11" >> %LOG_FILE%
bcp "SELECT LcID, Item, EffectiveDatetime, Cost, Price, ListPrice FROM %SOURCEDB%dbo.ItemPricing" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ItemPricing.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 12" >> %LOG_FILE%
bcp "SELECT OrdNbr, OrderStatus, Lcid, OriginCode, UserInitials, LineCount, TotalUnits, LocFillPct, ReviewerId FROM %SOURCEDB%dbo.AIMAO" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMAO.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 13" >> %LOG_FILE%
bcp "SELECT OrdNbr, OrdType, LineNbr, LineItemStatus, Lcid, LType, Item, ItDesc, UOM, RequestedQty, AllocatedQty, AdjustedAllocQty, Cost, LineFillPct, TxnDate, TxnTimeOfDay, AllocationDate, AllocationTimeOfDay FROM %SOURCEDB%dbo.AIMAODetail" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMAODetail.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 14" >> %LOG_FILE%
bcp "SELECT OrdNbr, OrderStatus, UserInitials, LineCount, TotalUnits, OrderFillPct FROM %SOURCEDB%dbo.AIMAO_OrderInfo" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMAO_OrderInfo.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 15" >> %LOG_FILE%
bcp "SELECT Class, ClassDesc, ClassLevel, LangID FROM %SOURCEDB%dbo.AIMClasses" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMClasses.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 16" >> %LOG_FILE%
bcp "SELECT LangID, CodeType, CodeID, CodeDesc FROM %SOURCEDB%dbo.AIMCodeLookUp" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMCodeLookUp.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 17" >> %LOG_FILE%
bcp "SELECT TxnSet, FileName, TxnDate FROM %SOURCEDB%dbo.AIMDataExchangeCtrl" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDataExchangeCtrl.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 18" >> %LOG_FILE%
bcp "SELECT FYDate, DayStatus FROM %SOURCEDB%dbo.AIMDays" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDays.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 19" >> %LOG_FILE%
bcp "SELECT DataElement, DetDesc, Range, DefaultValue, ElementName FROM %SOURCEDB%dbo.AIMDD" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDD.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 20" >> %LOG_FILE%
bcp "SELECT LcID_Destination, Item, ExceptionPct, AvailableQty, TargetQty FROM %SOURCEDB%dbo.AIMDestinationProfile" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDestinationProfile.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 21" >> %LOG_FILE%
bcp "SELECT SeqNr, Enabled, Item, LcId, VnId, Assort, BuyingUOM, ConvFactor, PackRounding, StdCost, LeadTime FROM %SOURCEDB%dbo.AIMDxAS" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxAS.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 22" >> %LOG_FILE%
bcp "SELECT seqnbr, ordnbr, doctype, prmprog, linenbr, cusnbr, lcid, item, ordqty, shpqty, uom, extcost, extprice, reason_code, txndate, timeofday, txn_type, source, shipping, handling, olduom, uomfact, olditem FROM %SOURCEDB%dbo.AIMDxDD" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxDD.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 23 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT SeqNbr, LineNbr, FcstID, LcID, Item, AdjustType, AdjustQty, AdjustBegDate, AdjustEndDate, UserID, RecordDesc FROM %SOURCEDB%dbo.AIMDxFA" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxFA.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 24 -- Ignore this one, it's not required." >> %LOG_FILE%
REM bcp "SELECT SeqNbr, LcId, Item, SubsItem, HisYear, CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, CPS23, CPS24, CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, CPS33, CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, CPS49, CPS50, CPS51, CPS52, QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, QtyOrd43, QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, OrdCnt11, OrdCnt12, OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30, OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52 FROM %SOURCEDB%dbo.AIMDxHS" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxHS.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 25" >> %LOG_FILE%
bcp "SELECT AllocPrimaryItem, AllocSubstItem, AllocSubstPriority FROM %SOURCEDB%dbo.AIMDxIS" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxIS.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 26" >> %LOG_FILE%
bcp "SELECT SeqNbr, Enabled, Lcid, Item, ItemComponent, ComponentUnits, ComponentScrap, StartDate, EndDate FROM %SOURCEDB%dbo.AIMDxKB" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxKB.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 27" >> %LOG_FILE%
bcp "SELECT SeqNbr, Lcid, LName, LType, LStatus, LDivision, LRegion, LUserDefined, LAddress1, LAddress2, LAddress3, LAddress4, LCity, LState, LZip, LCountry, LContact, LPhone, LFax, LEMail, LRank, PutAwayDays, ReplenCost, DemandSource, LeadTimeSource, Dft_Byid, LookBackPds, DmdScalingFactor, ScalingEffUntil, Freeze_Period, UpdateCurrentYearOption, DropShip_XDock, Dft_ReviewerId, ExceptionPct FROM %SOURCEDB%dbo.AIMDxLC" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxLC.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 28" >> %LOG_FILE%
bcp "SELECT SeqNbr, PONbr, LineNbr, VnId, LcId, Item, QtyOrd, QtyRec, DateOrd, DateRec, DatePutAway, Oh FROM %SOURCEDB%dbo.AIMDxLT" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxLT.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 29" >> %LOG_FILE%
bcp "SELECT SeqNbr, OrdNbr, OrdType, LineNbr, Lcid, Item, OrdQty, UOM, TxnDate, TxnTimeOfDay, Txn_Type, OriginCode FROM %SOURCEDB%dbo.AIMDxRO" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxRO.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 30" >> %LOG_FILE%
bcp "SELECT SeqNbr, Lcid, Item, ExceptionPct, OnHandQty, StandardQty FROM %SOURCEDB%dbo.AIMDxRP" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxRP.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 31" >> %LOG_FILE%
bcp "SELECT SeqNbr, Lcid_Destination, Lcid_Source, Src_Priority, ReviewerId FROM %SOURCEDB%dbo.AIMDxRS" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxRS.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 32" >> %LOG_FILE%
bcp "SELECT SeqNbr, OrdNbr, DocType, PrmProg, LineNbr, CusNbr, LcId, Item, SubsItem, OrdQty, ShpQty, UOM, ExtCost, ExtPrice, Reason_Code, TxnDate, TimeOfDay, Txn_Type, Source, Shipping, Handling, OldUOM, UOMFact, OldItem FROM %SOURCEDB%dbo.AIMDxSd" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxSd.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 33" >> %LOG_FILE%
bcp "SELECT SeqNbr, Status, Lcid, Item, ActDate, InActDate, Class1, Class2, Class3, Class4, MDC, UPC, Oh, Oo, ComStk, BkOrder, BkComStk, StkDate, OnPromotion, UserRef1, UserRef2, UserRef3, Freeze_Forecast, AllocatableQTY FROM %SOURCEDB%dbo.AIMDxSL" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxSL.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 34" >> %LOG_FILE%
bcp "SELECT SeqNbr, Status, Lcid, Item, ItDesc, ActDate, InActDate, Class1, Class2, Class3, Class4, VnId, Assort, MDC, Weight, Cube, ListPrice, Price, Cost, UOM, ConvFactor, BuyingUOM, LeadTime, UPC, Oh, Oo, ComStk, BkOrder, BkComStk, StkDate, BinLocation, BkQty01, BkCost01, BkQty02, BkCost02, BkQty03, BkCost03, BkQty04, BkCost04, BkQty05, BkCost05, BkQty06, BkCost06, BkQty07, BkCost07, BkQty08, BkCost08, BkQty09, BkCost09, BkQty10, BkCost10, OnPromotion, OldUOM, UOMFact, OldItem, NextPONbr_1, NextPODate_1, NextPOQty_1, NextPONbr_2, NextPODate_2, NextPOQty_2, NextPONbr_3, NextPODate_3, NextPOQty_3, UserRef1, UserRef2, UserRef3, Freeze_Forecast, AllocatableQty, KitBOMFlag FROM %SOURCEDB%dbo.AIMDxSS" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxSS.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 35" >> %LOG_FILE%
bcp "SELECT SeqNbr, VnId, Assort, VnType, VName, VAddress1, VAddress2, VCity, VState, VZip, VContact, VPhone, VFax, VEMail, VDocEMail, Dft_ById, RevCycle, Dft_LeadTime, Vn_Min, Vn_Best, Reach_Code, POByZone, VnComments1, VnComments2, TransmitPO, ShipIns FROM %SOURCEDB%dbo.AIMDxVn" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMDxVn.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 36 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT FcstSetupKey, UserID, AccessCode FROM %SOURCEDB%dbo.AIMFcstAccess" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMFcstAccess.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 37 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT LcID, Item, PeriodBegDate, PeriodEndDate, QtyAdj, QtyAdjOverRide, QtyAdjPct, AdjOverRide, DateTimeEdit FROM %SOURCEDB%dbo.AIMFcstMaster" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMFcstMaster.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 38 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT FcstSetupKey, FcstID, FcstDesc, FcstHierarchy, FcstEnabled, FcstLocked, FcstStartDate, LastUpdated, FcstRolling, FcstHistory, FcstPds_Future, FcstPds_Historical, FixedPds, FreezePds, FcstInterval, FcstUnit, ApplyTrend, Calc_SysFcst, Calc_NetReq, Calc_HistDmd, Calc_MasterFcstAdj, Calc_FcstAdj, Calc_AdjNetReq, Calc_ProjInv, Calc_ProdConst, Item, ItStat, VnID, Assort, ByID, Class1, Class2, Class3, Class4, LcID, LStatus, LDivision, LRegion, LUserDefined FROM %SOURCEDB%dbo.AIMFcstSetup" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMFcstSetup.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 39" >> %LOG_FILE%
bcp "SELECT CtrlNbr, FuncDesc, RWOption FROM %SOURCEDB%dbo.AIMFunctions" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMFunctions.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 40" >> %LOG_FILE%
bcp "SELECT HelpID, FormName, ControlName, Filepath FROM %SOURCEDB%dbo.AIMHelp" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMHelp.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 41" >> %LOG_FILE%
bcp "SELECT LangID, LangDesc, HexValue, DecimalValue, Enabled FROM %SOURCEDB%dbo.AIMLanguage" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMLanguage.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 42" >> %LOG_FILE%
bcp "SELECT LcId, Item, VnId, LeadTime, Prev_LeadTime1, Prev_LeadTime2, Prev_LeadTime3, LT_Count, LT_MAE, SS_Erosion_Pct FROM %SOURCEDB%dbo.AIMLeadTime" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMLeadTime.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 43" >> %LOG_FILE%
bcp "SELECT Locality, LiteralKey, Literal FROM %SOURCEDB%dbo.AIMLiterals" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMLiterals.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 44" >> %LOG_FILE%
bcp "SELECT MethodId, MethodStatus, MethodDesc, ApplySeasonsIndex, ApplyTrend FROM %SOURCEDB%dbo.AIMMethods" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMMethods.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 45" >> %LOG_FILE%
bcp "SELECT OptionId, OpDesc, IDFL, BypIDFL, CADL, HiDFL, LoDFL, NDFL, DDMAP, Damp, MinSmk, MaxSmk, IntSmk, TSL, TSSmK, BypDDU, ApplyDmdFilter, BypZSl, BypTFP, BypZOH, BypBef, BypAft, MinBI, HiPct, LoPct, HiMADP, LoMADP, MADSmk, MADExk, TrnSmk, DIDDL, DITrnDL, DITrps, DIMADP, HiTrndL, FilterSmk, LumpyFilterPct, Dft_TurnHigh, Dft_TurnLow, Dft_Dser FROM %SOURCEDB%dbo.AIMOptions" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMOptions.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 46" >> %LOG_FILE%
bcp "SELECT ById, VnId, Assort, POStatus, TransmitPO, ShipIns, UserInitials, Remarks1, Remarks2, Remarks3, AddrOverride, VName, VAddress1, VAddress2, VCity, VState, VZip, LineCount, PosLineCount, Vn_Min, Vn_Best, Reach_Code, POSource, POByZone, Dft_LeadTime, TotalCost, VndSizeFlag FROM %SOURCEDB%dbo.AIMPo" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMPo.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 47" >> %LOG_FILE%
bcp "SELECT ConstraintID, ConstraintType, ConstraintDesc, CycleDays, RatioType, EnableConstraint FROM %SOURCEDB%dbo.AIMProductionConstraint" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMProductionConstraint.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 48" >> %LOG_FILE%
bcp "SELECT ConstraintID, Item, MinUnits, MaxUnits FROM %SOURCEDB%dbo.AIMProductionConstraintDetail" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMProductionConstraintDetail.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 49" >> %LOG_FILE%
bcp "SELECT PmId, PmDesc, PmStatus, PmStartDate, PmEndDate, PmAdj01, PmAdj02, PmAdj03, PmAdj04, PmAdj05, PmAdj06, PmAdj07, PmAdj08, PmAdj09, PmAdj10, PmAdj11, PmAdj12, PmAdj13, PmAdj14, PmAdj15, PmAdj16, PmAdj17, PmAdj18, PmAdj19, PmAdj20, PmAdj21, PmAdj22, PmAdj23, PmAdj24, PmAdj25, PmAdj26, PmAdj27, PmAdj28, PmAdj29, PmAdj30, PmAdj31, PmAdj32, PmAdj33, PmAdj34, PmAdj35, PmAdj36, PmAdj37, PmAdj38, PmAdj39, PmAdj40, PmAdj41, PmAdj42, PmAdj43, PmAdj44, PmAdj45, PmAdj46, PmAdj47, PmAdj48, PmAdj49, PmAdj50, PmAdj51, PmAdj52 FROM %SOURCEDB%dbo.AIMPromotions" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMPromotions.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 50" >> %LOG_FILE%
bcp "SELECT RoleId, RoleDescription, RoleLevel, scarray, RepToRoleID, AltSourceExcpts FROM %SOURCEDB%dbo.AIMRoles" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMRoles.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 51" >> %LOG_FILE%
bcp "SELECT seqnbr, ordnbr, doctype, prmprog, linenbr, cusnbr, shipto, lcid, item, Subsitem, ordqty, shpqty, uom, extcost, extprice, reason_code, txndate, txn_type, source, shipping, handling, wk_start FROM %SOURCEDB%dbo.AIMSd" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMSd.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 52" >> %LOG_FILE%
bcp "SELECT SaVersion, SaId, SaDesc, SaLevel, LcId, Class, BI01, BI02, BI03, BI04, BI05, BI06, BI07, BI08, BI09, BI10, BI11, BI12, BI13, BI14, BI15, BI16, BI17, BI18, BI19, BI20, BI21, BI22, BI23, BI24, BI25, BI26, BI27, BI28, BI29, BI30, BI31, BI32, BI33, BI34, BI35, BI36, BI37, BI38, BI39, BI40, BI41, BI42, BI43, BI44, BI45, BI46, BI47, BI48, BI49, BI50, BI51, BI52, ItmCnt, AvgUnits FROM %SOURCEDB%dbo.AIMSeasons" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMSeasons.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 53" >> %LOG_FILE%
bcp "SELECT LcID_Destination, LcID_Source, Src_Priority, ReviewerId FROM %SOURCEDB%dbo.AIMSourcingHierarchy" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMSourcingHierarchy.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 54" >> %LOG_FILE%
bcp "SELECT TableName, TableDesc, TableEnabled FROM %SOURCEDB%dbo.AIMTables" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMTables.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 55" >> %LOG_FILE%
bcp "SELECT Lcid, LcidTransferTo, TransferType, EnableTransferPolicy FROM %SOURCEDB%dbo.AIMTransferPolicy" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMTransferPolicy.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 56 -- Ignore this, the translations are loaded with the defaults." >> %LOG_FILE%
REM bcp "SELECT LangID, ResourceID, ResourceString FROM %SOURCEDB%dbo.AIMTranslation" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMTranslation.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 57" >> %LOG_FILE%
bcp "SELECT UOM, UOMDesc FROM %SOURCEDB%dbo.AIMUOM" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMUOM.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 58" >> %LOG_FILE%
bcp "SELECT UserId, UserName, SLevel, ScArray, DmdUpdExcpts, OnPromotionExcpts, ExtCostExcpts, ExtCostLimit, PackSizeExcpts, PS_PctRoundLimit, PS_ExtCostLimit, LastWeekSaleExcpts, OverDuePO, SafetyStockEroded, BackOrdered, InvAvailRestriction, InvAvailList, UserInitials, LangID, LogErrors, AltSourceExcpts, POExtCost, POExtCostLimit, RoleId FROM %SOURCEDB%dbo.AIMUsers" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMUsers.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 59" >> %LOG_FILE%
bcp "SELECT VelCode, VCPct, Dft_OptionId FROM %SOURCEDB%dbo.AIMVelCodePcts" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMVelCodePcts.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 60" >> %LOG_FILE%
bcp "SELECT Day1, Day2, Day3, Day4, Day5, Day6, Day7 FROM %SOURCEDB%dbo.AIMWorkingDays" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMWorkingDays.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 61" >> %LOG_FILE%
bcp "SELECT FiscalYear, FYStartDate, FYEndDate, NbrWeeks FROM %SOURCEDB%dbo.AIMYears" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AIMYears.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 62" >> %LOG_FILE%
bcp "SELECT AllocDefaultsId, LStatus, LDivision, LRegion, LUserDefined, ExceptionPct, ReviewerId, LRank FROM %SOURCEDB%dbo.AllocDefaults" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocDefaults.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 63" >> %LOG_FILE%
bcp "SELECT AllocDefaultsId, LcId_Source, Src_Priority FROM %SOURCEDB%dbo.AllocDefaultsSource" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocDefaultsSource.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 64" >> %LOG_FILE%
bcp "SELECT SessionID, OrdNbr, LineNbr, DestinationLoc, ItemID, SourceLoc, AllocatedQty FROM %SOURCEDB%dbo.AllocScratch_AllocResults" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocScratch_AllocResults.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 65" >> %LOG_FILE%
bcp "SELECT SessionID, DestinationLoc, DestinationPriority FROM %SOURCEDB%dbo.AllocScratch_Req_Pri" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocScratch_Req_Pri.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 66" >> %LOG_FILE%
bcp "SELECT SessionID, OrdNbr, LineNbr, DestinationLoc, ItemID, RequestedQty FROM %SOURCEDB%dbo.AllocScratch_Req_Qty" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocScratch_Req_Qty.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 67" >> %LOG_FILE%
bcp "SELECT SessionID, OrdNbr, LineNbr, DestinationLoc, ItemID, NeedRatio, WeightedNeed FROM %SOURCEDB%dbo.AllocScratch_Req_Ratio" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocScratch_Req_Ratio.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 68" >> %LOG_FILE%
bcp "SELECT SessionID, SourceLoc, ItemID, SourceQty FROM %SOURCEDB%dbo.AllocScratch_SourceInventory" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%AllocScratch_SourceInventory.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 69" >> %LOG_FILE%
bcp "SELECT Enabled, Item, LcId, VnId, Assort, BuyingUOM, ConvFactor, PackRounding, StdCost, LeadTime FROM %SOURCEDB%dbo.ALTERNATE_SOURCE" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ALTERNATE_SOURCE.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 70" >> %LOG_FILE%
bcp "SELECT [KeyId], [Buyer Name], [Total Lines], [Complete Lines], [Planned Lines], [Released Lines], [Total LT Excpts], [Complete LT Excpts], [Planned LT Excpts], [Released LT Excpts], [Total Line Reviews], [Complete Line Reviews], [Planned Line Reviews], [Released Line Reviews], [Total Priority Excepts], [Complete Priority Excepts], [Planned Priority Excepts], [Released Priority Excepts] FROM %SOURCEDB%dbo.BuyerStatusTest" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%BuyerStatusTest.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 71" >> %LOG_FILE%
bcp "SELECT TxnSet, Seq, Element, Description, Mask, Length, Notes FROM %SOURCEDB%dbo.DXElements" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%DXElements.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 72" >> %LOG_FILE%
bcp "SELECT TxnSet, Description, FileFormat FROM %SOURCEDB%dbo.DXSets" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%DXSets.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 73 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT FcstSetupKey, FilterColumn, SearchCondition, FilterIndex, FilterValue FROM %SOURCEDB%dbo.ForecastFilterCriteria" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ForecastFilterCriteria.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 74 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT RepositoryKey, FcstID, FcstDesc, UserElement, FcstComment, UserIDCreate, DateTimeCreate, UserIDEdit, DateTimeEdit FROM %SOURCEDB%dbo.ForecastRepository" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ForecastRepository.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 75 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT FcstAdjustKey, RepositoryKey, LcID, Item, IsPromoted, AdjustType, AdjustQty, OverRideEnabled, AdjustBegDate, AdjustEndDate, AdjustUserID, AdjustDesc, AdjustDateTime FROM %SOURCEDB%dbo.ForecastRepositoryArch_Log" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ForecastRepositoryArch_Log.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 76 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT RepositoryKey, LcID, Item, FcstPdBegDate, FcstPdEndDate, Fcst, FcstAdj, MasterFcstAdj, FcstNetReq, FcstAdjNetReq, QtyAdj, QtyAdjOverRide, QtyAdjPct, AdjOverRide, MasterQtyAdj, MasterQtyAdjOverRide, MasterQtyAdjPct, MasterAdjOverRide, HistDmd, QtyActualShipped, QtyProjectedInventory, ProdConst FROM %SOURCEDB%dbo." ForecastRepositoryDetail" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ForecastRepositoryDetail.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 77 -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT FcstAdjustKey, RepositoryKey, LcID, Item, IsPromoted, AdjustType, AdjustQty, OverRideEnabled, AdjustBegDate, AdjustEndDate, AdjustUserID, AdjustDesc, AdjustDateTime FROM %SOURCEDB%dbo.ForecastRepository_Log" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ForecastRepository_Log.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 78 - ItDefaults" >> %LOG_FILE%
bcp "SELECT LcId, Class, SaId, VelCode, BuyStrat, ById, ReplenCost2, DSer, OptionId FROM %SOURCEDB%dbo.ItDefaults" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ItDefaults.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 79 - ItStatus" >> %LOG_FILE%
bcp "SELECT ItStat, ItStatDesc, Ordgen, DmdUpd, VCAssn, CanPurge, BuyerReviewSeq, InclInSeasonsGen FROM %SOURCEDB%dbo.ItStatus" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ItStatus.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 80 - KitBOMSummary" >> %LOG_FILE%
bcp "SELECT LcId, Item, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstDemand, DependentFcstDemand, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year FROM %SOURCEDB%dbo.KitBOMSummary" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%KitBOMSummary.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 81 - MDCDetail" >> %LOG_FILE%
bcp "SELECT MDC, LcId, Item, FcstDate, AvailQty, FcstDemand, MAE, Trend, RqQty01, RqQty02, RqQty03, RqQty04, RqQty05, RqQty06, RqQty07, RqQty08, RqQty09, RqQty10, RqQty11, RqQty12, RqQty13, RqQty14, RqQty15, RqQty16, RqQty17, RqQty18, RqQty19, RqQty20, RqQty21, RqQty22, RqQty23, RqQty24, RqQty25, RqQty26 FROM %SOURCEDB%dbo.MDCDetail" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%MDCDetail.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 82 - MDCSummary" >> %LOG_FILE%
bcp "SELECT MDC, Item, Accum_Lt, ReviewTime, OrderPt, OrderQty, SafetyStock, FcstRT, FcstLT, Fcst_Month, Fcst_Quarter, Fcst_Year FROM %SOURCEDB%dbo.MDCSummary" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%MDCSummary.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 83 - PODetail" >> %LOG_FILE%
bcp "SELECT POLineType, POSeqID, OrdStatus, RevCycle, ById, VnId, Assort, Lcid, Item, ItDesc, POType, AvailQty, PackRounding, RSOQ, SOQ, VSOQ, Original_VSOQ, UOM, BuyingUOM, ConvFactor, IsDate, DuDate, LastWeekSalesFlag, Cost, Zone FROM %SOURCEDB%dbo.PODetail" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%PODetail.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 84 - POStatistics" >> %LOG_FILE%
bcp "SELECT SeqNbr, ById, VnId, Assort, IsDate, Original_NbrLines, NbrLines, Original_VSOQ, VSOQ, Original_ExtCost, ExtCost FROM %SOURCEDB%dbo.POStatistics" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%POStatistics.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 85 - ProdConstraintTemp" >> %LOG_FILE%
bcp "SELECT UniqueJobId, Lcid, Item, Qty, AdjQty, DateCreated FROM %SOURCEDB%dbo.ProdConstraintTemp" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%ProdConstraintTemp.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 86 - RevCycles" >> %LOG_FILE%
bcp "SELECT RevCycle, RevCycleDesc, NextDateTime, RevStatus, DynamicFlag, RevFreq, RevInterval, RevSunday, RevMonday, RevTuesday, RevWednesday, RevThursday, RevFriday, RevSaturday, WeekQualifier, RevStartDate, RevEndDate, InitBuyPct, InitRevDate, SeasonalReview, ReviewTime, OrdGenStatus FROM %SOURCEDB%dbo.RevCycles" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%RevCycles.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 87 - SSA_DR_Migration -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT EAM_ID, ProductName, OldVersion, NewVersion, TransactionCount, TotalTimeMinute, StartTime, EndTime FROM %SOURCEDB%dbo.SSA_DR_Migration" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%SSA_DR_Migration.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 88 - SSA_DR_Migration_Log -- Ignore this one, it's not in the older set." >> %LOG_FILE%
REM bcp "SELECT EAM_ID, ProductName, OldVersion, NewVersion, TableName, DefaultCount, OldDatabaseCount, NewDatabaseCount, TotalTimeMillisecond, StartTime, EndTime FROM %SOURCEDB%dbo.SSA_DR_Migration_Log" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%SSA_DR_Migration_Log.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo "Table: 89 - SysCtrl" >> %LOG_FILE%
bcp "SELECT CName, Address1, Address2, Address3, City, State, CoZip, Dft_MinRchPct, Dft_BestRchPct, DxPath, DxPath_Out, DxPath_Backup, Dft_POStatus, DxPO_Option, KFactor, MinROAI, Vendor_Sizing, Priority_Min_Dser, Priority_Min_VelCode, Addl_LTDays, Lt_Filter_Pct, Retain_SD_Opt, Calc_Perform, LogFile, ByIdSource, SeasSmoothingPds, CurSaVersion, Last_POSeq, ClassOption, AvgInvAlpha, UpdateGoalsOption, UpdateVnLT, VnLTPctChgFilter, VnLTDaysChgFilter, MDCOption, LT_Alpha, LT_CtrlInterval_Pct, Int_Enabled, Int_MinPctZero, Int_MaxPctZero, Int_SeasonalityIndex, Int_SrvLvlOverrideFlag, Int_SrvLvl, Int_LookBackPds, AIMBatchPath, dft_LangID, ColonOption, GridAutoSizeOption, DateFormat, TimeFormat, UnicodeOption, ProductionConstraint, ProdConstraintGrtZero, AllocExceptionPct, SessionID, AllocNeedDetermination, AllocExceptionProcess, CarryForwardRounding, LastOrderGenerationProcess, MasterItemOption, CreateDBUser, UpdateDBPassword, 0, 'S' FROM %SOURCEDB%dbo.SysCtrl" queryout %SS_WORKING_DIR%%SCRIPTS_DIR%%DATA_DIR%SysCtrl.txt -c -k -E -S %SOURCESERVER% -T >> %LOG_FILE%

echo Finished with running the bcp utility for forty-nine (49) tables.  Exiting script.
echo -------------------------------------------------------------------------------

endlocal
set prompt=
