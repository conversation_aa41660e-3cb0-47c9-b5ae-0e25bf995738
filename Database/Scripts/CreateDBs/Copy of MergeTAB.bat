@echo off
REM Created Sept. 07 2003 by Annalaksh<PERSON> Stocksdale.
REM -------------------------------------------------------------------------------
echo 	This script is intended to be called from "CreateDB.bat". 
echo 	When called, it will merge all SSA DR table scripts, 
echo 	with primary tables appearing first,
echo 	followed by the rest in alphabetical order.
REM ~~~~~~~~~~~~~~~~~~
REM Required Software: 
REM 	Microsoft SQL Server 2000 server with Query Analyser
REM ~~~~~~~~~~
REM -------------------------------------------------------------------------------

setlocal

set SS_DIR=%1
set OUTPUT_DIR=\EXceedAIMProject\Database\Scripts\CreateDBs\
set LOG_FILE=Batch_CreateDB.LOG

set CREATETAB_SQL=TEMP_DRTables.SQL
set CREATETAB_LOG=TEMP_CreateTables.LOG

:MergeTables
	REM -------------------------------------------------------------------------------
	echo 	Merging *.TAB...
	REM -------------------------------------------------------------------------------
	echo ----- 1/4. Merge *.TABs ----- >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	if exist %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL% (del %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL%)

	REM ************************************************************
	REM -- Get the dependencies established, first. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	copy /V /Y ..\..\Tables\*AIMVendors*.TAB + ..\..\Tables\*AIMLocations*.TAB + ..\..\Tables\*.Item.TAB ..\..\Tables\A1Dependencies.TAB >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Move above files to a temp name while creating the composite DRTables script. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	rename ..\..\Tables\*AIMVendors*.TAB *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename ..\..\Tables\*AIMLocations*.TAB *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename ..\..\Tables\*.Item.TAB *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Copy and move the rest of the *item* tables
	REM ************************************************************
	copy /V /Y ..\..\Tables\*Item*.TAB ..\..\Tables\A2Dependencies.TAB >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename ..\..\Tables\*Item*.TAB *.TXT >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%


	REM ************************************************************
	REM -- Now, copy all the create-tables into the composite DRTables.SQL script. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	copy /V /-Y ..\..\Tables\*.TAB %SS_DIR%%OUTPUT_DIR%%CREATETAB_SQL% >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM -- Move the dependencies back to their original name. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	rename ..\..\Tables\*AIMVendors*.TXT *.TAB >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename ..\..\Tables\*AIMLocations*.TXT *.TAB >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	rename ..\..\Tables\*Item*.TXT *.TAB >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%

	REM ************************************************************
	REM Delete the temporary file. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	REM ************************************************************
	del ..\..\Tables\A*Dependencies.TAB >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%


GOTO EXIT_BATCH

:LOW_MEMORY
	REM -------------------------------------------------------------------------------
	rem errorlevel 4
	echo Insufficient memory to copy files or 
	echo invalid drive or command-line syntax. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo Insufficient memory to copy files or invalid drive or command-line syntax. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:CANCEL_BAT
	REM -------------------------------------------------------------------------------
	rem errorlevel 2
	echo You pressed CTRL+C to end the copy operation. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo You pressed CTRL+C to end the copy operation. Cancelling batch. >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:ISQLW_FAILURE
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.
	REM -------------------------------------------------------------------------------
	echo ISQL command failed. Cancelling batch.>> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	GOTO EXIT_BATCH

:EXIT_BATCH
	REM -------------------------------------------------------------------------------
	echo ------ END -- MergeTAB.bat ------ 
	REM -------------------------------------------------------------------------------
	echo ------ END -- MergeTAB.bat ------ >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE%
	date /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 
	time /T >> %SS_DIR%%OUTPUT_DIR%%LOG_FILE% 

endlocal
REM End batch.
