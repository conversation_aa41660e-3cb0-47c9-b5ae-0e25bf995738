SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepositoryVersion_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepositoryVersion_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepositoryVersion_Sp
**	Desc: Returns current Forecast Repository Versioning data
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  IncrementFcst Flag, Current FcstVersion
**              
**	Auth:   Wade Riza 
**	Date:   07/31/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:   	Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza	Updated Return Codes and Error handling
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastRepositoryVersion_Sp
(
	@FcstID					nvarchar(12),
	@FcstType				nvarchar(30),
	@EnableFcstVersions			nvarchar(1) OUTPUT,  -- Versioning 'ON' or 'OFF'
	@FcstVersion				bigint OUTPUT  -- Current Version number
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int,
	@records					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

IF @FcstType IS NULL
BEGIN
	RETURN -1
END

SELECT @EnableFcstVersions = EnableFcstVersions FROM AIMForecast
WHERE FcstID = @FcstID
SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0
BEGIN
	SET @EnableFcstVersions = 'N'    
END

SELECT @records = COUNT(*) FROM ForecastRepository
WHERE FcstID = @FcstID and FcstType = @FcstType

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
 
IF @records < 1 OR @records IS NULL
BEGIN
 	SET @FcstVersion = 0
END
ELSE
BEGIN
	SELECT @FcstVersion = MAX(FcstVersion) FROM ForecastRepository
	WHERE FcstID = @FcstID AND FcstType = @FcstType

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	RETURN 0                          -- Successful 
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

