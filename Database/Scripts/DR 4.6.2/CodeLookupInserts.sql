SET NOCOUNT ON


INSERT INTO aimcodelookup
values('en-us','DEMANDSOURCE','O','Ordered Quantity')
GO

INSERT INTO aimcodelookup
values('en-us','DEMANDSOURCE','S','Shipped Quantity')

GO

INSERT INTO aimcodelookup
values('en-us','DEMANDSOURCE','B','Shipped With Substitute Quantity')
GO

INSERT INTO aimcodelookup
values('en-us','LEADTIMESOURCE','L','Lead Time Transaction')

GO

INSERT INTO aimcodelookup
values('en-us','LEADTIMESOURCE','S','Stock Status Transaction')

GO


INSERT INTO aimcodelookup
values('en-us','LEADTIMESOURCE','V',''Vendor Table Default Lead Time')

GO


INSERT INTO aimcodelookup
values('en-us','ERRORCODES','DRGE','DR General Error')

GO

INSERT INTO aimcodelookup
values('en-us','ERRORCODES','DRJ<PERSON>','DR Job Updates')

GO


INSERT INTO aimcodelookup
values('en-us','ERRORCODES','DRJE','DR Job Error')

GO


