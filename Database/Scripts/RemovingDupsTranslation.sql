Select count(*) from AIMTranslation

Truncate Table AIMTranslation

Select count(*) from AIMTranslation

-- Remove Primary Key from AIMTranslation Table.
ALTER TABLE dbo.AIMTranslation
	DROP CONSTRAINT PK_AIMTranslation
GO
--*******************************************************************
-- Import Data from the Excel Spreadsheet for all LangID's using DTS.
--*******************************************************************

Select count(*) from AIMTranslation

Create Table #AIMTRANSLATION
(LangID nvarchar(10),
ResourceID nvarchar(255),
ResourceString nvarchar(4000))
GO

CREATE UNIQUE INDEX RemoveDups ON #AIMTRANSLATION(LangID, ResourceID) WITH IGNORE_DUP_KEY
Go

INSERT #AIMT<PERSON><PERSON>LATION
SELECT * FROM AIMTRANSLATION
GO

Select count(*) from AIMTranslation
Select count(*) from #AIMTRANSLATION

Truncate Table AIMTranslation
GO

-- Add back Primary Key to the AIMTranslation Table.
ALTER TABLE dbo.AIMTranslation ADD CONSTRAINT
	PK_AIMTranslation PRIMARY KEY CLUSTERED 
	(
	LangID,
	ResourceID
	) ON [PRIMARY]
GO

INSERT AIMTRANSLATION
SELECT * FROM #AIMTRANSLATION
GO

Select count(*) from AIMTranslation
Select count(*) from #AIMTRANSLATION

DROP INDEX #AIMTRANSLATION.RemoveDups
GO

TRUNCATE TABLE #AIMTRANSLATION
GO

Select count(*) from #AIMTRANSLATION

DROP TABLE #AIMTRANSLATION
GO

