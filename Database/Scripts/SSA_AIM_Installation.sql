Use master
GO

Create Database <>
on Primary (Filename = <>)
for Attach
Go

Use <>
Go

Declare @CollationName Nvarchar(50)

Select @CollationName = (Convert(<PERSON><PERSON><PERSON><PERSON>(50),ServerProperty(N'Collation')))

	If @CollationName = 'Latin1_General_BIN'
	   Begin
		Alter Database <>
		Collate Latin1_General_BIN
	   End
	Else If  @CollationName = 'Latin1_General_CS_AS' 
	   Begin
		Alter Database <>
		Collate Latin1_General_CS_AS
	   End
	Else If @CollationName =  'Latin1_General_CI_AS'
	   Begin
		Alter Database <>
		Collate Latin1_General_CI_AS
	   End
	Else If @CollationName =  'Danish_Norwegian_CS_AS'
	   Begin
		Alter Database <>
		Collate Danish_Norwegian_CS_AS
	   End
	Else If @CollationName =  'Finnish_Swedish_CS_AS'
	   Begin
		Alter Database <>
		Collate Finnish_Swedish_CS_AS
	   End
	Else If @CollationName =  'Icelandic_CS_AS'
	   Begin
		Alter Database <>
		Collate Icelandic_CS_AS
	   End
	Else If @CollationName =  'Icelandic_CS_AS'
	   Begin
		Alter Database <>
		Collate Icelandic_CS_AS
	   End
	Else If @CollationName =  'Japanese_CS_AS'
	   Begin
		Alter Database <>
		Collate Japanese_CS_AS
	   End
	Else If @CollationName =  'Japanese_CI_AS'
	   Begin
		Alter Database <>
		Collate Japanese_CI_AS
	   End
	Else If @CollationName =  'Japanese_BIN'
	   Begin
		Alter Database <>
		Collate Japanese_BIN
	   End
	Else If @CollationName =  'Korean_Wansung_BIN'
	   Begin
		Alter Database <>
		Collate Korean_Wansung_BIN
	   End
	Else If @CollationName =  'Korean_Wansung_CI_AS'
	   Begin
		Alter Database <>
		Collate Korean_Wansung_CI_AS
	   End
	Else If @CollationName =  'Korean_Wansung_CS_AS'
	   Begin
		Alter Database <>
		Collate Korean_Wansung_CS_AS
	   End
	Else If @CollationName =  'Chinese_Taiwan_Stroke_BIN'
	   Begin
		Alter Database <>
		Collate Chinese_Taiwan_Stroke_BIN
	   End
	Else If @CollationName =  'Chinese_Taiwan_Stroke_CS_AS'
	   Begin
		Alter Database <>
		Collate Chinese_Taiwan_Stroke_CS_AS
	   End
	Else If @CollationName =  'Chinese_Taiwan_Stroke_CI_AS'
	   Begin
		Alter Database <>
		Collate Chinese_Taiwan_Stroke_CI_AS
	   End
	Else If @CollationName =  'Chinese_PRC_BIN'
	   Begin
		Alter Database <>
		Collate Chinese_PRC_BIN
	   End
	Else If @CollationName =  'Chinese_PRC_CS_AS'
	   Begin
		Alter Database <>
		Collate Chinese_PRC_CS_AS
	   End
	Else If @CollationName =  'Chinese_PRC_CI_AS'
	   Begin
		Alter Database <>
		Collate Chinese_PRC_CI_AS
	   End
	Else If @CollationName =  'Thai_BIN'
	   Begin
		Alter Database <>
		Collate Thai_BIN
	   End
	Else If @CollationName =  'Thai_CS_AS'
	   Begin
		Alter Database <>
		Collate Thai_CS_AS
	   End
	Else If @CollationName =  'Thai_CI_AS'
	   Begin
		Alter Database <>
		Collate Thai_CI_AS
	   End
	Else If @CollationName =  'Arabic_BIN'
	   Begin
		Alter Database <>
		Collate Arabic_BIN
	   End
	Else If @CollationName =  'Hebrew_BIN'
	   Begin
		Alter Database <>
		Collate Hebrew_BIN
	   End
	Else If @CollationName =  'Turkish_BIN'
	   Begin
		Alter Database <>
		Collate Turkish_BIN
	   End
	Else If @CollationName =  'Greek_BIN'
	   Begin
		Alter Database <>
		Collate Greek_BIN
	   End
	Else If @CollationName =  'Cyrillic_General_BIN'
	   Begin
		Alter Database <>
		Collate Cyrillic_General_BIN
	   End
	Else If @CollationName =  'Ukrainian_BIN'
	   Begin
		Alter Database <>
		Collate Ukrainian_BIN
	   End
	Else If @CollationName =  'Macedonian_BIN'
	   Begin
		Alter Database <>
		Collate Macedonian_BIN
	   End
	Else If @CollationName =  'Hungarian_BIN'
	   Begin
		Alter Database <>
		Collate Hungarian_BIN
	   End
	Else If @CollationName =  'Albanian_BIN'
	   Begin
		Alter Database <>
		Collate Albanian_BIN
	   End
	Else If @CollationName =  'Croatian_BIN'
	   Begin
		Alter Database <>
		Collate Croatian_BIN
	   End
	Else If @CollationName =  'Czech_BIN'
	   Begin
		Alter Database <>
		Collate Czech_BIN
	   End
	Else If @CollationName =  'Romanian_BIN'
	   Begin
		Alter Database <>
		Collate Romanian_BIN
	   End
	Else If @CollationName =  'Slovak_BIN'
	   Begin
		Alter Database <>
		Collate Slovak_BIN
	   End
	Else If @CollationName =  'Slovenian_BIN'
	   Begin
		Alter Database <>
		Collate Slovenian_BIN
	   End
	Else
	   Begin
		Alter Database <>
		Collate Latin1_General_CI_AS
	   End
GO

	


	





	
	
		
