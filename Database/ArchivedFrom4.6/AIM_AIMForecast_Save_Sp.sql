SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMForecast_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMForecast_Save_Sp]
GO

/******************************************************************************
*******************************************************************************
**
** NOTICE
**
** THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
** CONFIDENTIAL INFORMATION OF SSA GLOBAL 
** TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
** USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
** AUTHORIZATION. ALTHOUGH PUBLICATION IS NOT
** INTENDED, IN THE EVENT OF PUBLICATION, THE
** FOLLOWING NOTICE IS APPLICABLE:
**
** (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
** SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** Name: AIM_AIMForecast_Save_Sp
** Desc: Updates/Inserts to AIMForecast, based on the InsertUpdate flag
**
** Returns: 1)@found - Can be Zero
** 2) -1 - No Data Found
** 3) -2 - SQL Error
** Values: 
** 
** Auth: Annalakshmi Stocksdale
** Date: 09/17/2002
*******************************************************************************
** Change History
*******************************************************************************
** Date: Author: Description:
** ---------- ------------ -------------------------------------------------
** 09/18/2001 Wade Riza Update 
*******************************************************************************/

CREATE PROCEDURE AIM_AIMForecast_Save_Sp
(
	@FcstId nvarchar(12),
	@FcstDesc nvarchar(40),
	@FcstStatus tinyint = 0,
	@FcstLevel tinyint = 0,
	@FcstInterval tinyint = 0,
	@FcstUnit tinyint = 0,
	@NetRqmts tinyint = 0,
	@FcstPds tinyint = 0,
	@VnId nvarchar(12) = " ",
	@Assort nvarchar(12) = " ",
	@LcId nvarchar(12) = " ",
	@Item nvarchar(25) = " ",
	@Class1 nvarchar(50) = " ",
	@Class2 nvarchar(50) = " ",
	@Class3 nvarchar(50) = " ",
	@Class4 nvarchar(50) = " ",
	@LDivision as nvarchar(20),
	@LRegion as nvarchar(20),
	@LStatus as nvarchar(1),
	@LUserDefined as nvarchar(30),
	@ById nvarchar(12) = " ",
	@FcstStartDate datetime = '1900-01-01',
	@AdjustPct decimal (5) = 0,
	@ApplyTrend tinyint = 0,
	@RevFreq tinyint = 0,
	@RevInterval tinyint = 0,
	@NextRevDate datetime = '1900-01-01',
	@FcstContact nvarchar(30) = " ",
	@FcstEMail nvarchar(30) = " ",
	@FcstLinkId nvarchar(12) = " ",
	@FcstOutput nvarchar(1) = "N",
	@FcstLocked nvarchar(1) = "N",
	@FuturePdsUpdate nvarchar(1) = "Y",
	@ItStat nvarchar(1) = " ",
	@EnableFcstVersions nvarchar(1) = "N",
	@InsertUpdate nvarchar(1)
)

-- WITH ENCRYPTION /* Production use must be encrypted */
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found int

SET NOCOUNT ON

-- Validate the required parameters.
	IF @FcstId IS Null
	OR @FcstStatus IS Null
	OR @FcstLevel IS Null
	OR @FcstInterval IS Null
	OR @FcstUnit IS Null
	OR @NetRqmts IS Null
	OR @FcstPds IS Null
	OR @FcstStartDate IS Null
	OR @ApplyTrend IS Null
	OR @RevFreq IS Null
	OR @RevInterval IS Null
	OR @NextRevDate IS Null
	OR @FcstLocked IS Null
	OR @FuturePdsUpdate IS Null
	OR @ItStat IS Null
	OR @EnableFcstVersions IS Null
	OR @InsertUpdate IS Null
	BEGIN
		RETURN -1
	END

	IF @InsertUpdate = 'I'
	BEGIN
		INSERT INTO AIMForecast (FcstId, FcstDesc, FcstStatus, 
			FcstLevel, FcstInterval, FcstUnit, NetRqmts, FcstPds,
			VnId, Assort, LcId, Item, Class1, Class2, Class3, Class4,
			LDivision, LRegion, LStatus, LUserDefined,
			ById, FcstStartDate, AdjustPct, ApplyTrend, RevFreq, 
			RevInterval, NextRevDate, FcstContact, FcstEMail, 
			FcstLinkId, FcstOutput, FcstLocked, FuturePdsUpdate,
			ItStat, EnableFcstVersions)
		VALUES (@FcstId, @FcstDesc, @FcstStatus, @FcstLevel, @FcstInterval, 
			@FcstUnit, @NetRqmts, @FcstPds, @VnId, @Assort, @LcId, @Item, 
			@Class1, @Class2, @Class3, @Class4, 
			@LDivision, @LRegion, @LStatus, @LUserDefined,
			@ById, @FcstStartDate, 
			@AdjustPct, @ApplyTrend, @RevFreq, @RevInterval, @NextRevDate,
			@FcstContact, @FcstEMail, @FcstLinkId, @FcstOutput, @FcstLocked,
			@FuturePdsUpdate, @ItStat, @EnableFcstVersions)
	END
	ELSE IF @InsertUpdate = 'U'
	BEGIN
		UPDATE AIMForecast
		SET FcstDesc = @FcstDesc,
		FcstStatus = @FcstStatus,
		FcstLevel = @FcstLevel,
		FcstInterval = @FcstInterval,
		FcstUnit = @FcstUnit,
		NetRqmts = @NetRqmts,
		FcstPds = @FcstPds,
		VnId = @VnId,
		Assort = @Assort,
		LcId = @LcId,
		Item = @Item,
		Class1 = @Class1,
		Class2 = @Class2,
		Class3 = @Class3,
		Class4 = @Class4,
		LDivision = @LDivision, 
		LRegion = @LRegion, 
		LStatus = @LStatus,
		LUserDefined = @LUserDefined,
		ById = @ById,
		FcstStartDate = @FcstStartDate,
		AdjustPct = @AdjustPct,
		ApplyTrend = @ApplyTrend,
		RevFreq = @RevFreq,
		RevInterval = @RevInterval,
		NextRevDate = @NextRevDate,
		FcstContact = @FcstContact,
		FcstEMail = @FcstEMail,
		FcstLinkId = @FcstLinkId,
		FcstOutput = @FcstOutput,
		FcstLocked = @FcstLocked,
		FuturePdsUpdate = @FuturePdsUpdate,
		ItStat = @ItStat,
		EnableFcstVersions = @EnableFcstVersions
		WHERE AIMForecast.FcstID = @FcstID
		AND AIMForecast.FcstLocked <> 'Y'
	END

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2 -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE
	BEGIN
		RETURN 0
	END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

