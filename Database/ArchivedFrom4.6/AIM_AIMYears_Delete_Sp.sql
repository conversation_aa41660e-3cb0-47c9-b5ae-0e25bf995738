SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_Delete_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SH<PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMYears_Delete_Sp
**	Desc: Deletes AIMYears based on a passed in value for Fiscal year.
**
**	Returns: 1)  @RowCounter - Successful
** 
**	Values:  Recordset - AIMYears
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
** 	Change History
*******************************************************************************
**	Date:      Author:      Description:
**	---------- ------------ -----------------------------------------------
**    
*******************************************************************************/
   
CREATE PROCEDURE AIM_AIMYears_Delete_Sp
(
	@FiscalYear int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	
	DECLARE @FYStartDate datetime,
            @FYEndDate datetime,
            @RowCounter int
            	
	SELECT @FYStartDate = FYStartDate, 
		@FYEndDate = FYEndDate
	FROM AIMYears
	WHERE FiscalYear = @FiscalYear
        
	SET @RowCounter = @@ROWCOUNT

	-- Was the Fiscal Year found?
	IF @RowCounter = 0
	BEGIN
		RETURN 0
	END      

	IF @RowCounter < 0
	BEGIN
		BEGIN TRANSACTION
		DELETE AIMDays
		WHERE FYDate BETWEEN @FYStartDate AND @FYEndDate

		SET @RowCounter = @@ROWCOUNT
		IF @RowCounter < 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN @RowCounter
		END

		-- Delete the Fiscal Year Record
		DELETE AIMYears 
		WHERE FiscalYear = @FiscalYear

		SET @RowCounter = @@ROWCOUNT
		IF @RowCounter < 0 
		BEGIN
			ROLLBACK TRANSACTION
			RETURN @RowCounter
		END
						
		COMMIT TRANSACTION
	END

	RETURN @RowCounter

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
