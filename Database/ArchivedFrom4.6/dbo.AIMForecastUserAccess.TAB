/****** Object:  Table [dbo].[AIMForecastUserAccess]    Script Date: 05/30/2003 11:04:33 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMForecastUserAccess]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMForecastUserAccess]
GO

CREATE TABLE [dbo].[AIMForecastUserAccess] (
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AccessType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_ForecastUserAccess] PRIMARY KEY  CLUSTERED 
	(
		[UserID],
		[FcstID],
		[FcstType]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


