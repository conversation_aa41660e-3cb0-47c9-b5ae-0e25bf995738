SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GetRepositoryKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GetRepositoryKey_Sp]
GO

/*******************************************************************************
**	Name: AIM_GetRepositoryKey_Sp
**	Desc: Retrieves the Forecast Repository Key, given the Forecast ID
**
**	Parameters: Forecast ID
**
**	Returns: 
**		 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   <PERSON><PERSON><PERSON><PERSON>
**	Date:   2004/10/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_GetRepositoryKey_Sp 
(
	@FcstID as nvarchar(12),
	@RepositoryKey as numeric(18, 0) OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON

	IF @FcstID IS NULL
	BEGIN
		RETURN -1
	END

	SELECT @RepositoryKey = ForecastRepository.RepositoryKey
	FROM ForecastRepository
	WHERE ForecastRepository.FcstID = @FcstID
	GROUP BY ForecastRepository.RepositoryKey

	IF @@ROWCOUNT > 0 
	BEGIN
		RETURN @@ROWCOUNT
	END
	ELSE
	BEGIN
		RETURN -1
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


