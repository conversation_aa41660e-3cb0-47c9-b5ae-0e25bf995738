SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_Update_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_Update_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepository_Update_Sp
**	Desc: Inserts Forecast Repository data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Retrived Key
**               3) -1 - Input Data Missing
**             
**	Values:  Record - Forecast RepositoryKey
**              
**	Auth:   Srinivas Uddanti
**	Date:   03/06/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**   
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastRepository_Update_Sp
(
	@FcstID                                     nvarchar(12),
	@FcstType                                   nvarchar(30),
	@RepositoryKey		                    int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @Found					    int,
  @FcstDesc					    	nvarchar(40),
  @FcstStartDate					datetime,
  @FcstInterval						tinyint
	
SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

IF @FcstType IS NULL
BEGIN
	RETURN -1
END

SELECT @RepositoryKey = RepositoryKey
FROM ForecastRepository
WHERE  FcstType =@FcstType
AND FcstId =@FcstId
SELECT @found =@@rowcount
IF @found =0 
BEGIN
	SELECT @FcstDesc= FcstDesc,
	@FcstStartDate =FcstStartDate,
        @FcstInterval=FcstInterval
	FROM AimForecast
	WHERE FcstId =@FcstId
	SELECT @found =@@rowcount
	IF @found =0 
	BEGIN
		RETURN -1
	END

	INSERT INTO ForecastRepository
    		(FcstID, FcstVersion, FcstDesc, FcstType, FcstStartDate, FcstInterval,
		FcstComment, UserIDCreate, DateTimeCreate, UserIDEdit, DatetimeEdit )
    		VALUES(@FcstID,1,'', @FcstType, @FcstStartDate, @FcstInterval,
		'','', GETDATE(), '', GETDATE())

	SELECT @RepositoryKey =@@Identity
	RETURN 0
END
ELSE
BEGIN
	RETURN 1
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


