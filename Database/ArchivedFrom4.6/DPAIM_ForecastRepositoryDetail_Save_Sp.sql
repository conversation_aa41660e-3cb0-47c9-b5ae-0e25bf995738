SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_ForecastRepositoryDetail_Save_Sp    Script Date: 10/12/2004 10:54:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_ForecastRepositoryDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_ForecastRepositoryDetail_Save_Sp]
GO





/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: DPAIM_ForecastRepositoryDetail_Save_Sp
**	Desc: Inserts Forecast Repository Detail data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**		 3) -1 - No Data Found
**               4) -2 - SQL Error
**             
**	Auth:   Wade Riza 
**	Date:   07/31/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza	Updated Return codes and Error handling
**      09/27/2002 Wade Riza	Updated SP for two new columns 
**                              (FcstPdStartDate & QtyProjectedInventory) 
*******************************************************************************/
     
CREATE    PROCEDURE DPAIM_ForecastRepositoryDetail_Save_Sp
(
    @RepositoryKey					int,
    @LcID		        			nvarchar(12), 
    @Item		        			nvarchar(25), 
    @FcstType						nvarchar(25),
    @FcstPdBegDate		        	        datetime, 
    @FcstPdEndDate		        	        datetime, 
    @Qty_1					        int=0,
    @Qty_2					        int=0,
    @Qty_3						int = 0,
    @Qty_4						int = 0
)	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      					int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdBegDate IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdEndDate IS NULL
BEGIN
	RETURN -1
END



SELECT @RepositoryKey = RepositoryKey FROM ForecastRepositoryDetail
WHERE RepositoryKey = @RepositoryKey
AND Item = @Item
AND LcId = @LcId
AND FcstPdBegDate = @FcstPdBegDate
        
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0		-- Insert new Repository Detail record
BEGIN
	IF @FcstType ='1234' 
	BEGIN
	   INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    FcstAdj,FcstAdjNetReq,QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1,@Qty_2,@Qty_3, @Qty_4)
       
	END
	ELSE IF @FcstType ='123'
	BEGIN 
	   INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    FcstAdj,FcstAdjNetReq)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1,@Qty_2,@Qty_3)
	
	END
	ELSE IF @FcstType ='124' 
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    FcstAdj,QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1,@Qty_2,@Qty_4)
	END
	ELSE IF @FcstType ='134'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    FcstAdjNetReq,QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1,@Qty_3, @Qty_4)
	END
	ELSE IF @FcstType ='234'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,
	    FcstAdj,FcstAdjNetReq,QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_2,@Qty_3, @Qty_4)
	END
	ELSE IF @FcstType ='12'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    FcstAdj)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1,@Qty_2)
	END
	ELSE IF @FcstType ='13'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    FcstAdjNetReq)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1,@Qty_3)
	END
	ELSE IF @FcstType ='14'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj,
	    QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1, @Qty_4)
	END
	ELSE IF @FcstType ='23'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,
	    FcstAdj,FcstAdjNetReq)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_2,@Qty_3)
	END
	ELSE IF @FcstType ='24'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item,FcstPdBegDate,FcstPdEndDate,
	    FcstAdj,QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_2, @Qty_4)
	END
	ELSE IF @FcstType ='34'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,FcstAdjNetReq,QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_3, @Qty_4)
	END
	ELSE IF @FcstType ='1'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,MasterFcstAdj
	    )
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_1)
	END
	ELSE IF @FcstType ='2'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,
	    FcstAdj)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_2)
	END
	ELSE IF @FcstType ='3'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,FcstAdjNetReq)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
            @Qty_3)
	END
	ELSE IF @FcstType ='4'
	BEGIN
	    INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,
	    QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPdBegDate,@FcstPdendDate, 
             @Qty_4)
	END

	SELECT @found = @@rowcount

        -- Check for SQL Server errors.
        IF @@ERROR <> 0 
        BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
        END
        ELSE
	BEGIN
		RETURN 0 -- SUCCESSFUL
	END
END
ELSE			-- Update existing Repository Detail record
BEGIN	
        

	IF @FcstType ='1234' 
	BEGIN
	   UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
            FcstAdj =@Qty_2,FcstAdjNetReq = @Qty_3,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate
       
	END
	ELSE IF @FcstType ='123'
	BEGIN 
	   UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
            FcstAdj =@Qty_2,FcstAdjNetReq = @Qty_3
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='124' 
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
            FcstAdj =@Qty_2,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='134'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
            FcstAdjNetReq = @Qty_3,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='234'
	BEGIN
	   UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
            FcstAdj =@Qty_2,FcstAdjNetReq = @Qty_3,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='12'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
            FcstAdj =@Qty_2      
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='13'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
            FcstAdjNetReq = @Qty_3        
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='14'
	BEGIN
	   UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='23'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
            FcstAdj =@Qty_2,FcstAdjNetReq = @Qty_3
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='24'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    FcstAdj =@Qty_2,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate
	END
	ELSE IF @FcstType ='34'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    FcstAdjNetReq = @Qty_3,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='1'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    MasterFcstAdj =@Qty_1
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='2'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
            FcstAdj =@Qty_2        
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='3'
	BEGIN
	   UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    FcstAdjNetReq = @Qty_3        
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate

	END
	ELSE IF @FcstType ='4'
	BEGIN
	    UPDATE ForecastRepositoryDetail
            SET FcstPdEndDate=@FcstPdEndDate,
	    QtyProjectedInventory =@Qty_4         
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPdBegDate = @FcstPdBegDate
	END
    	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

    	IF @found > 0
    	BEGIN
		RETURN 1 -- UPDATE Record Updated
    	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR No Data Found
	END
END



GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

