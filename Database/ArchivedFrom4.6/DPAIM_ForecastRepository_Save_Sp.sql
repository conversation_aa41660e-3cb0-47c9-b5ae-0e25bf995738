SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_ForecastRepository_Save_Sp    Script Date: 10/12/2004 10:54:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_ForecastRepository_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_ForecastRepository_Save_Sp]
GO


/*******************************************************************************
--	Name: DPAIM_ForecastRepository_Save_Sp
--	Desc: Save Record to ForecastRepository Table
--	Returns: 1)  0 - Successful
--			2) -1 - No Data Found
--			3) -2 - SQL Error
--			4) -3 - Invalid parameters
--
--
--	Auth:   Srinivas Uddanti
--	Date:   2004/07/30
*******************************************************************************
--	Change History
*******************************************************************************
--	Date:      	Author:      		Description:
--	---------- 	------------ 		-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE DPAIM_ForecastRepository_Save_Sp (
	@UserID as nvarchar(12),
	@FcstID as nvarchar(12),
	@UserElement as bit,
	@RepositoryKey as numeric OUTPUT
	-- FcstComment is stored separately
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON
	DECLARE @Return_Stat as int
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	BEGIN
		BEGIN TRANSACTION
			SELECT @RepositoryKey = ForecastRepository.FcstId 
			FROM ForecastRepository
			WHERE 	ForecastRepository.FcstId = @FcstId 
			SET @Return_Stat = @@ROWCOUNT	
			IF @Return_Stat <= 0 
			BEGIN		
				INSERT INTO ForecastRepository (
					FcstId,
					UserElement,
					UserIdCreate,
					UserIdEdit,
					DateTimeCreate,
					DateTimeEdit

					-- FcstComment gets its own stored procedure for chunk-and-save processing from the UI
				) VALUES (
					@FcstId,
					@UserElement,
					@UserID, 
					@UserID,
					GetDate(),
					GetDate()			
				)			
				SET @RepositoryKey = @@IDENTITY
				SET @Return_Stat = @@ROWCOUNT
			END
		COMMIT TRANSACTION
	END

	-- Return status to calling mod.
	IF @Return_Stat > 0 RETURN @Return_Stat
	ELSE RETURN -1

-- 	END


GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

