/*******************************************************************************
-- THIS FILE CONTAINS ALL THE TABLE CREATION SCRIPTS 
--	FOR THE DEMAND PLANNER MODULE 
--	(See the companion script AIMDemandPlanner_StoredProcs.sql for all related stored procedures)
-- 	NOTE: This file is arranged such that dependencies are properly assigned in the system tables.
--			Please exercise due caution if re-arranging the order of sub-scripts.
-- CONTENTS (In order of creation): 
--	TABLES
-- 	* AIMFcstSetup -- replaces AIMForecast
-- 	* AIMFcstAccess -- replaces AIMForecast<PERSON>serAccess and AIMForecastMainUserAccess (something like that)
-- 	* AIMFcstFreeze -- replaces AIMForecastFreezePds
-- 	* AIMFcstMaster -- new 
-- 	* AIMFcstStore -- replaces ForecastRepository
-- 	* AIMFcstStoreDetail -- replaces ForecastRepositoryDetail and ForecastRepositoryBatch
--	* AIMFcstStoreAdjustLog -- new
--
-- 	* AIMDxFA	-- inbound temp table
--	* View -- AIM_DmdPlan_Adjustments
-- 
--	Author:		Annalakshmi Stocksdale
--	Created:	2004/01/01
-------------------------------------------------------------------------------
--	Change History
-------------------------------------------------------------------------------
--	Date:		Updated by:		Description:
--	----------	------------		-----------------------------------------------
*******************************************************************************/

/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstSetup.TAB
--
--   Version Number - 2.0
--   Last Updated   - 2004/03/31
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/

SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstSetup]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstSetup]
GO

CREATE TABLE [dbo].[AIMFcstSetup] (
	[FcstSetupKey] int IDENTITY,	-- primary key
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL UNIQUE ,	-- alternate key
	[FcstDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[FcstHierarchy] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstHierarchy] DEFAULT (1),
	[FcstEnabled] [bit] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstEnabled] DEFAULT (1),
	[FcstLocked] [bit]  NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstLocked] DEFAULT (0),
	[FcstStartDate] [datetime] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstStartDate] DEFAULT (0),
	[LastUpdated] [datetime] NOT NULL CONSTRAINT [DF_AIMFcstSetup_LastUpdated] DEFAULT (0),
	[FcstRolling] [bit],
	[FcstHistory] [bit],
	[FcstPds_Future] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstPds_Future] DEFAULT (1),
	[FcstPds_Historical] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstPds_Historical] DEFAULT (1),
	[FcstInterval] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstInterval] DEFAULT (2),
	[FcstUnit] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_FcstUnit] DEFAULT (0),
	[ApplyTrend] [tinyint] NOT NULL CONSTRAINT [DF_AIMFcstSetup_ApplyTrend] DEFAULT (0),
	[Calc_SysRqmt] [bit],
	[Calc_NetRqmt] [bit],
	[Calc_PrjInvt] [bit],
	[Calc_HstDmnd] [bit],
	[Calc_NetRqmtAndPlnRcpt] [bit],
	[Calc_NetRqmtAndPrdCons] [bit],
	[Calc_PrjInvtAndPrdCons] [bit],
-- Item filter criteria
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[ItStat] [nvarchar] (1)COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VnID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[ByID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	CONSTRAINT [PK_AIMFcstSetup] PRIMARY KEY NONCLUSTERED 
	(
		[FcstSetupKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO

CREATE INDEX IDX_AIMFcstSetup_Cover ON AIMFcstSetup (FcstSetupKey, FcstID, FcstStartDate)
GO


/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstAccess.TAB
--
--   Version Number - 2.0
--   Last Updated   - 2004/03/31
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstAccess]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstAccess]
GO

CREATE TABLE [dbo].[AIMFcstAccess] (
	[FcstSetupKey] int NOT NULL,
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[AccessCode] [tinyint] NOT NULL,

	CONSTRAINT [PK_AIMFcstAccess] PRIMARY KEY NONCLUSTERED 
	(
		[FcstSetupKey],
		[UserID],
		[AccessCode]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIMFcstFreeze.TAB
-- --
-- --   Version Number - 2.0
-- --   Last Updated   - 2004/03/31
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT * FROM dbo.sysobjects 
-- 		WHERE ID = object_id(N'[dbo].[AIMFcstFreeze]') 
-- 		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
-- 	DROP TABLE [dbo].[AIMFcstFreeze]
-- GO
-- 
-- CREATE TABLE [dbo].[AIMFcstFreeze] 
-- 	(
-- 		[FcstFreezeKey] int IDENTITY,	
-- 		[FcstSetupKey] int NOT NULL,	
-- 		[FreezeType] tinyint NOT NULL,
-- 		[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
-- 		CONSTRAINT [PK_AIMFcstFreeze] PRIMARY KEY NONCLUSTERED 
-- 		(
-- 			[FcstFreezeKey]
-- 		)  ON [PRIMARY] 
-- 	) ON [PRIMARY]
-- GO
-- 
-- CREATE INDEX IDX_FcstFreeze_Cover ON AIMFcstFreeze ([FcstSetupKey], [FreezeType], [LcID])
-- GO
-- 
-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIMFcstFreezeLimit.TAB
-- --
-- --   Version Number - 2.0
-- --   Last Updated   - 2004/03/31
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT * FROM dbo.sysobjects 
-- 		WHERE ID = object_id(N'[dbo].[AIMFcstFreezeLimit]') 
-- 		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
-- 	DROP TABLE [dbo].[AIMFcstFreezeLimit]
-- GO
-- 
-- CREATE TABLE [dbo].[AIMFcstFreezeLimit] 
-- 	(
-- 		[FcstFreezeKey] int ,	
-- 		[FreezeLimit] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS
-- 
-- 	) ON [PRIMARY]
-- GO
-- 
-- 
-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIMFcstStore.TAB
-- --
-- --   Version Number - 2.0
-- --   Last Updated   - 2004/03/31
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT * FROM dbo.sysobjects 
-- 		WHERE ID = object_id(N'[dbo].[AIMFcstStore]') 
-- 		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
-- 	DROP TABLE [dbo].[AIMFcstStore]
-- GO
-- 
-- CREATE TABLE [dbo].[AIMFcstStore] 
-- 	(
-- 		[FcstStoreKey] int IDENTITY,
-- 		[FcstSetupKey] int NOT NULL ,
-- 		[FcstStoreID] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL UNIQUE,
-- 		[UserElement] [bit]  CONSTRAINT [DF_AIMFcstRep_UserElement] DEFAULT (0),
-- 		[FcstComment] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
-- 		CONSTRAINT [PK_AIMFcstStore] PRIMARY KEY NONCLUSTERED 
-- 		(
-- 			[FcstStoreKey]
-- 		)  ON [PRIMARY] 
-- 	) ON [PRIMARY]
-- 
-- GO
-- 
-- CREATE CLUSTERED INDEX IDX_AIMFcstStore_Cover 
-- 	ON AIMFcstStore (FcstSetupKey, FcstStoreID, UserElement)
-- GO
-- 
-- 
-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIMFcstStoreDetail.TAB
-- --
-- --   Version Number - 2.0
-- --   Last Updated   - 2004/03/31
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- --	NOTES:
-- --	'FcstStoreKey' links this detail table with the generic information contained in the header.
-- --	'LcID' and 'Item' link this data to the entities defined in AIM's 'Item' table.
-- --	The actual date boundaries for the forecast are marked by 'PeriodStartDate' and 'PeriodEndDate'
-- --	'TargetPeriod' marks the AIMDays.FYPeriod that this forecast corresponds to.
-- -- 	'DataCalc_Type' would associate calculated quanties (in 'DataCalc_Value') for the following:
-- --		 1 = System Forecast/Requirements
-- -- 		 2 = Adjusted System Forecast
-- -- 		 3 = Net Requirements
-- --		 4 = Adjusted Net Requirements
-- --		 5 = Net Requirements and Production Constraints
-- --		 6 = Net Requirements and Planned Receipts
-- --		 7 = Projected Inventory
-- --		 8 = Projected Inventory and Production Constraints
-- -- 		 9 = Historical Demand
-- -- 		10 = Cost Adjustments
-- -- 		11 = Price Adjustments
-- --	'DataCalc_Value' would contain the forecast quantity as calculated for the given Item/Location/Period, 
-- --	using the criteria defined in the Forecast Setup record associated with the FcstStoreKey.
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT * FROM dbo.sysobjects 
-- 		WHERE ID = object_id(N'[dbo].[AIMFcstStoreDetail]') 
-- 		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
-- 	DROP TABLE [dbo].[AIMFcstStoreDetail]
-- GO
-- 
-- CREATE TABLE [dbo].[AIMFcstStoreDetail] 
-- 	(
-- 		[FcstStoreKey] int NOT NULL,
-- 		[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- 		[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- 		[PeriodStartDate] [datetime] NOT NULL ,
-- 		[DataCalc_Type] [tinyint] NOT NULL,
-- 		[DataCalc_Value] [decimal] (10, 2),
-- 		[PeriodEndDate] [datetime], 
-- 		[TargetPeriod] [int],
-- 		CONSTRAINT [PK_AIMFcstStoreDetail] PRIMARY KEY NONCLUSTERED 
-- 		(
-- 			[FcstStoreKey],
-- 			[LcID],
-- 			[Item],
-- 			[PeriodStartDate],
-- 			[DataCalc_Type]
-- 		)  ON [PRIMARY] 
-- 	) ON [PRIMARY]
-- GO
-- 
-- CREATE INDEX IDX_AIMFcstStoreDetail_Cover 
-- 	ON AIMFcstStoreDetail (			
-- 		[FcstStoreKey],
-- 		[LcID],
-- 		[Item],
-- 		[PeriodStartDate],
-- 		[DataCalc_Type],
-- 		[PeriodEndDate]
-- --		, [TargetPeriod]
-- 	)
-- GO
-- 
-- 
-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIMFcstStoreAdjustLog.TAB
-- --
-- --   Version Number - 2.0
-- --   Last Updated   - 2004/03/31
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT * FROM dbo.sysobjects 
-- 		WHERE ID = object_id(N'[dbo].[AIMFcstStoreAdjustLog]') 
-- 		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
-- 	DROP TABLE [dbo].[AIMFcstStoreAdjustLog]
-- GO
-- 
-- CREATE TABLE [dbo].[AIMFcstStoreAdjustLog] 
-- 	(
-- 		[FcstAdjustKey] int IDENTITY, 	-- primary key
-- 		[FcstStoreKey] int NOT NULL,
-- 		[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- 		[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- 		[IsPromoted] [bit],
-- 		[AdjustType] [tinyint],
-- 		[AdjustQty] [decimal](10, 2),
-- 		[AdjustStartDate] [datetime],
-- 		[AdjustEndDate] [datetime],
-- 		[AdjustUserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
-- 		[AdjustDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
-- 		[AdjustDateTime] [datetime]
-- 		CONSTRAINT [PK_AIMFcstAdjustLog] PRIMARY KEY NONCLUSTERED 
-- 		(
-- 			[FcstAdjustKey]
-- 		)  ON [PRIMARY] 
-- 	) ON [PRIMARY]
-- GO
-- 
-- CREATE INDEX IDX_AIMFcstStoreAdjustLog_Cover 
-- 	ON AIMFcstStoreAdjustLog ([FcstStoreKey], [LcID], [Item], [AdjustType], [AdjustStartDate], [AdjustEndDate])
-- 
-- CREATE INDEX IDX_AIMFcstStoreAdjustLog_Dates 
-- 	ON AIMFcstStoreAdjustLog ([AdjustStartDate], [AdjustEndDate], [AdjustDateTime])
-- 
-- CREATE INDEX IDX_AIMFcstStoreAdjustLog_Promo
-- 	ON AIMFcstStoreAdjustLog ([IsPromoted])
-- 
-- GO
-- 

/*
-------------------------------------------------------------------------------------------------------------
-- Copyright (c) 2004 SSA Global. All rights reserved.
-------------------------------------------------------------------------------------------------------------
--
--   AIMFcstMaster.TAB
--
--   Version Number - 2.0
--   Last Updated   - 2004/03/31
--   Updated By     - Annalakshmi Stocksdale
--
-------------------------------------------------------------------------------------------------------------
-- This file contains trade secrets of SSA Global. No part
-- may be reproduced or transmitted in any form by any means or for any purpose
-- without the express written permission of SSA Global.
-------------------------------------------------------------------------------------------------------------
*/
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
SET ARITHABORT ON
SET CONCAT_NULL_YIELDS_NULL ON
SET QUOTED_IDENTIFIER ON
SET NOCOUNT ON
GO
SET NUMERIC_ROUNDABORT OFF
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
		WHERE ID = object_id(N'[dbo].[AIMFcstMaster]') 
		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
	DROP TABLE [dbo].[AIMFcstMaster]
GO

CREATE TABLE [dbo].[AIMFcstMaster] 
	(
		[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
		[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
		[PeriodStartDate] [datetime],
		[PeriodEndDate] [datetime],
		[AdjustType] [tinyint],
		[AdjustQty] [decimal](10, 2),
		[FiscalYear] [int],
		[FiscalPeriod] [int],
		[DateTimeEdit] [datetime]
		CONSTRAINT [PK_AIMFcstMaster] PRIMARY KEY NONCLUSTERED 
		(
			[LcID],
			[Item],
			[PeriodStartDate],
			[PeriodEndDate],
			[AdjustType]
		)  ON [PRIMARY] 
	) ON [PRIMARY]

CREATE INDEX IDX_AIMFcstMaster_Edit
	ON AIMFcstMaster ([DateTimeEdit])

GO

-- 
-- 
-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIMDxFA.TAB
-- --
-- --   Version Number - 2.0
-- --   Last Updated   - 2004/03/31
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT * FROM dbo.sysobjects 
-- 		WHERE ID = object_id(N'[dbo].[AIMDxFA]') 
-- 		AND OBJECTPROPERTY(ID, N'IsUserTable') = 1)
-- 	DROP TABLE [dbo].[AIMDxFA]
-- GO
-- 
-- CREATE TABLE [dbo].[AIMDxFA] 
-- 	(
-- 		[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
-- 		[FcstStoreID] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
-- 		[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- 		[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- 		[AdjustType] [tinyint],
-- 		[AdjustQty] [decimal](10, 2),
-- 		[AdjustStartDate] [datetime],
-- 		[AdjustEndDate] [datetime],
-- 		[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
-- 		[RecordDesc] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
-- 	) ON [PRIMARY]
-- 
-- GO
-- 
-- 
-- /*****************************************************************************/
--  -- VIEW DEFINITIONS --
-- /*****************************************************************************/
-- 
-- /*
-- -------------------------------------------------------------------------------------------------------------
-- -- Copyright (c) 2004 SSA Global. All rights reserved.
-- -------------------------------------------------------------------------------------------------------------
-- --
-- --   AIM_DmdPlan_Adjustments.SQL
-- --
-- --   Version Number - 1.0
-- --   Last Updated   - 2004/04/22
-- --   Updated By     - Annalakshmi Stocksdale
-- --
-- -------------------------------------------------------------------------------------------------------------
-- -- This file contains trade secrets of SSA Global. No part
-- -- may be reproduced or transmitted in any form by any means or for any purpose
-- -- without the express written permission of SSA Global.
-- -------------------------------------------------------------------------------------------------------------
-- */
-- 
-- SET ANSI_NULLS ON
-- SET ANSI_PADDING ON
-- SET ANSI_WARNINGS ON
-- SET ARITHABORT ON
-- SET CONCAT_NULL_YIELDS_NULL ON
-- SET QUOTED_IDENTIFIER ON
-- SET NOCOUNT ON
-- GO
-- SET NUMERIC_ROUNDABORT OFF
-- GO
-- 
-- IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.VIEWS
--       WHERE TABLE_NAME = 'AIM_DmdPlan_Adjustments')
--    DROP VIEW AIM_DmdPlan_Adjustments
-- GO
-- 
-- CREATE VIEW AIM_DmdPlan_Adjustments
-- (
-- 	FcstStoreKey, 
-- 	LcID, 
-- 	Item,
-- 	AdjustType,
-- 	AdjustQty,
-- 	Author,
-- 	Notes,
-- 	EditStamp,
-- 	AdjustStartDate,
-- 	AdjustEndDate
-- )
-- -- WITH ENCRYPTION	/* Production use must be encrypted */
-- AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
-- 
-- 	SELECT 
-- 		AIMFcstStoreDetail.FcstStoreKey, 
-- 		AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
-- 		, STOREADJ.AdjustType
-- 		, STOREADJ.AdjustQty
-- 		, STOREADJ.AdjustUserID, STOREADJ.AdjustDesc, STOREADJ.AdjustDateTime
-- 		, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
-- 	FROM AIMFcstStoreDetail 
-- 	INNER JOIN AIMFcstStoreAdjustLog STOREADJ ON
-- 		AIMFcstStoreDetail.FcstStoreKey = STOREADJ.FcstStoreKey AND
-- 		AIMFcstStoreDetail.LcID = STOREADJ.LcID AND
-- 		AIMFcstStoreDetail.Item = STOREADJ.Item
-- 	WHERE
-- 		STOREADJ.IsPromoted = 0 -- FALSE
-- 	GROUP BY 
-- 		AIMFcstStoreDetail.FcstStoreKey, 
-- 		AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
-- 		, STOREADJ.AdjustType
-- 		, STOREADJ.AdjustQty
-- 		, STOREADJ.AdjustUserID, STOREADJ.AdjustDesc, STOREADJ.AdjustDateTime
-- 		, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
-- UNION ALL
-- 	SELECT
-- 		FcstStoreKey, 
-- 		MASTER.LcID, MASTER.Item
-- 		, MASTER.AdjustType
-- 		, MASTER.AdjustQty
-- 		, 'MASTER', 'MASTER'
-- 		, MASTER.DateTimeEdit
-- 		, MASTER.PeriodStartDate
-- 		, MASTER.PeriodEndDate
-- 	FROM AIMFcstMaster MASTER
-- 	INNER JOIN AIMFcstStoreDetail ON
-- 		MASTER.LcID = AIMFcstStoreDetail.LcID AND
-- 		MASTER.Item = AIMFcstStoreDetail.Item
-- 	GROUP BY 
-- 		FcstStoreKey, 
-- 		MASTER.LcID, MASTER.Item
-- 		, MASTER.AdjustType
-- 		, MASTER.AdjustQty
-- 		, MASTER.DateTimeEdit
-- 		, MASTER.PeriodStartDate
-- 		, MASTER.PeriodEndDate
-- 		
-- GO
