SET NOCOUNT ON
DECLARE @IDKEY as int

TRUNCATE TABLE AIMFcstSetup
TRUNCATE TABLE AIMFcstAccess

INSERT INTO AIMFcstSetup (
	FcstID, FcstDesc, 
	FcstHierarchy, FcstEnabled, FcstLocked, 
	FcstStartDate, LastUpdated, 
	FcstRolling, FcstHistory, FcstPds_Future, FcstPds_Historical, 
	FcstInterval, FcstUnit, ApplyTrend, 
	Calc_SysRqmt, Calc_NetRqmt, Calc_PrjInvt, Calc_HstDmnd, 
	Calc_NetRqmtAndPlnRcpt, Calc_NetRqmtAndPrdCons, Calc_PrjInvtAndPrdCons, 
	Item, ItStat, VnID, Assort, ByID, Class1, Class2, Class3, Class4, LcID, LStatus, LDivision, LRegion, LUserDefined
) VALUES (
	'TestAnItem', 'Test one item, all locations, all calcs.',
	1, 1, 0, 
	'2004-05-01', '2004-05-01',
	1, 1, 6, 2, 
	2, 0, 2,
	1, 0, 0, 0, 0, 0, 0,
	'HEW51626A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
)

SET @IDKEY = @@IDENTITY
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 0)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 1)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 2)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 3)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 4)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 5)

INSERT INTO AIMFcstSetup (
	FcstID, FcstDesc, 
	FcstHierarchy, FcstEnabled, FcstLocked, 
	FcstStartDate, LastUpdated, 
	FcstRolling, FcstHistory, FcstPds_Future, FcstPds_Historical, 
	FcstInterval, FcstUnit, ApplyTrend, 
	Calc_SysRqmt, Calc_NetRqmt, Calc_PrjInvt, Calc_HstDmnd, 
	Calc_NetRqmtAndPlnRcpt, Calc_NetRqmtAndPrdCons, Calc_PrjInvtAndPrdCons, 
	Item, ItStat, VnID, Assort, ByID, Class1, Class2, Class3, Class4, LcID, LStatus, LDivision, LRegion, LUserDefined
) VALUES (
	'TestBulk', 'Test with ~1000 items',
	1, 1, 0, 
	'2004-07-29', '2004-07-29',
	1, 1, 6, 1, 
	1, 0, 2,
	1, 0, 0, 0, 0, 0, 0,
	NULL, NULL, NULL, NULL, 'Raleigh', 'P', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
)

SET @IDKEY = @@IDENTITY
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 0)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 1)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 2)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 3)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 4)
INSERT INTO AIMFcstAccess VALUES (@IDKEY, 'sa', 5)

TRUNCATE TABLE AIMFcstMaster
 INSERT INTO AIMFcstMaster (
	LcID, Item, PeriodStartDate, PeriodEndDate, 
	AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
 ) VALUES (
	'MDC', 'HEW51626A', '2004-07-29 00:00:00.000', '2004-08-04 00:00:00.000',
	1, 60, 2004, 5, DATEADD(wk, -1, GETDATE())
 )
 INSERT INTO AIMFcstMaster (
	LcID, Item, PeriodStartDate, PeriodEndDate, 
	AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
 ) VALUES (
	'MDC', 'HEW51626A', '2004-08-05 00:00:00.000', '2004-08-11 00:00:00.000',
	0, 100, 2004, 5, DATEADD(wk, -1, GETDATE())
 )
 INSERT INTO AIMFcstMaster (
	LcID, Item, PeriodStartDate, PeriodEndDate, 
	AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
 ) VALUES (
	'MDC', 'HEW51626A', '2004-08-12 00:00:00.000', '2004-08-18 00:00:00.000',
	0, 100, 2004, 5, DATEADD(wk, -1, GETDATE())
 )
 INSERT INTO AIMFcstMaster (
	LcID, Item, PeriodStartDate, PeriodEndDate, 
	AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
 ) VALUES (
	'MDC', 'HEW51626A', '2004-08-19 00:00:00.000', '2004-08-25 00:00:00.000',
	0, 100, 2004, 5, DATEADD(wk, -1, GETDATE())
 )

 INSERT INTO AIMFcstMaster (
	LcID, Item, PeriodStartDate, PeriodEndDate, 
	AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
 ) VALUES (
	'MDC', 'HEW51626A', '2004-08-26 00:00:00.000', '2004-09-01 00:00:00.000',
	1, 80, 2004, 9, DATEADD(wk, -1, GETDATE())
 )

 INSERT INTO AIMFcstMaster (
	LcID, Item, PeriodStartDate, PeriodEndDate, 
	AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
 ) VALUES (
	'MDC', 'HEW51626A', '2004-09-02 00:00:00.000', '2004-09-08 00:00:00.000',
	1, 30, 2004, 10, DATEADD(wk, -1, GETDATE())
 )

UPDATE STATISTICS AIMFcstSetup WITH RESAMPLE, ALL
UPDATE STATISTICS AIMFcstAccess WITH RESAMPLE, ALL
UPDATE STATISTICS AIMFcstMaster WITH RESAMPLE, ALL

TRUNCATE TABLE AIMFcstStoreAdjustLog
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc19', 'HEW51626A', 0, 0, 10, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc19', 'HEW51626A', 0, 1, 20, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc19', 'HEW51626A', 0, 2, 30, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc19', 'HEW51626A', 0, 1, 40, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc19', 'HEW51626A', 0, 1, 50, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc19', 'HEW51626A', 0, 2, -5, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())

 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'MDC', 'HEW51626A', 0, 0, 324, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'MDC', 'HEW51626A', 0, 1, 10, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'MDC', 'HEW51626A', 0, 1, 20, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'MDC', 'HEW51626A', 0, 2, 30, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc37', 'HEW51626A', 0, 1, 40, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc37', 'HEW51626A', 0, 0, 50, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())
 INSERT INTO AIMFcstStoreAdjustLog VALUES (1, 'dc37', 'HEW51626A', 0, 2, -5, '2004-08-01 00:00:00.000', '2004-08-31 00:00:00.000', 'sa', 'test', getdate())

UPDATE STATISTICS AIMFcstStoreAdjustLog WITH RESAMPLE, ALL

SELECT * FROM AIMFcstSetup
SELECT * FROM AIMFcstAccess
SELECT * FROM AIMFcstStoreAdjustLog
SELECT * FROM AIMFcstMaster

