-- -- -- -- SUMMARY
-- SELECT 
--     AIMFcstStore.FcstStoreKey,
--     Item.LcID
--     , 'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type, 'DataCalcDesc' = CODE_DCT.CodeDesc
--     , 'Totals' = SUM (CASE WHEN
--     AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
--     AND AIMFcstStoreDetail.PeriodEndDate < '2004/11/01'
--     THEN DataCalc_Value ELSE 0 END)
--     , '2004/03/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/04/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/04/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/04/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/05/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/05/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/05/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/06/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/06/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/06/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/07/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/07/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/07/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/08/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/08/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/08/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/09/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/09/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/09/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/10/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/10/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/10/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/11/01'THEN DataCalc_Value ELSE 0 END)
-- FROM AIMFcstStore
-- INNER JOIN AIMFcstStoreDetail ON AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
-- INNER JOIN ITEM ON AIMFcstStoreDetail.Item = Item.Item AND AIMFcstStoreDetail.LcId = Item.LcId
-- INNER JOIN AIMLocations ON AIMFcstStoreDetail.LcId = AIMLocations.LcID
-- INNER JOIN AIMCodeLookup CODE_DCT ON AIMFcstStoreDetail.DataCalc_Type = CODE_DCT.CodeID
-- WHERE AIMFcstStore.FcstSetupKey = 1
--     AND AIMFcstStore.UserElement = 0
--     AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
--     AND AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
--     AND AIMFcstStoreDetail.PeriodEndDate <= '2004/10/31'
--     AND CODE_DCT.LangID = 'en-us' AND CODE_DCT.CodeType = 'DATACALCTYPE'
--     AND  DataCalc_Type IN (1, 2, 9 )
--     AND Item.Item = N'HEW51626A'
-- GROUP BY 
--     AIMFcstStore.FcstStoreKey, Item.LcID, AIMFcstStoreDetail.DataCalc_Type, CODE_DCT.CodeDesc
-- 
-- 
-- -- -- -- DETAIL
-- SELECT 
--     AIMFcstStore.FcstStoreKey,
--     AIMFcstStore.FcstStoreID, AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
--     , Item.ItDesc
--     , 'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type, 'DataCalcDesc' = CODE_DCT.CodeDesc
--     , 'AdjustType' = NULL, 'AdjustTypeDesc' = ''
--     , 'Totals' = SUM (CASE WHEN
--     AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
--     AND AIMFcstStoreDetail.PeriodEndDate < '2004/11/01'
--     THEN DataCalc_Value ELSE 0 END)
--     , '2004/03/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/04/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/04/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/04/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/05/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/05/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/05/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/06/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/06/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/06/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/07/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/07/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/07/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/08/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/08/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/08/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/09/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/09/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/09/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/10/01'THEN DataCalc_Value ELSE 0 END)
--     , '2004/10/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/10/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/11/01'THEN DataCalc_Value ELSE 0 END)
--     , Item.ItStat, Item.Class1, Item.Class2, Item.Class3, Item.Class4
--     , Item.VelCode, Item.VnID, Item.Assort, Item.ByID
--     , AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined
-- FROM AIMFcstStore
-- INNER JOIN AIMFcstStoreDetail ON AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
-- INNER JOIN ITEM ON AIMFcstStoreDetail.Item = Item.Item AND AIMFcstStoreDetail.LcId = Item.LcId
-- INNER JOIN AIMLocations ON AIMFcstStoreDetail.LcId = AIMLocations.LcID
-- INNER JOIN AIMCodeLookup CODE_DCT ON AIMFcstStoreDetail.DataCalc_Type = CODE_DCT.CodeID
-- WHERE AIMFcstStore.FcstSetupKey = 1
--     AND AIMFcstStore.UserElement = 0
--     AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
--     AND AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
--     AND AIMFcstStoreDetail.PeriodEndDate <= '2004/10/31'
--     AND CODE_DCT.LangID = 'en-us' AND CODE_DCT.CodeType = 'DATACALCTYPE'
--     AND  DataCalc_Type IN (1, 2, 9 )
--     AND Item.Item = N'HEW51626A'
-- GROUP BY 
--     AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID, AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
--     , Item.ItDesc, Item.ItStat
--     , AIMFcstStoreDetail.DataCalc_Type, CODE_DCT.CodeDesc
--     , Item.Class1, Item.Class2, Item.Class3, Item.Class4
--     , Item.VelCode, Item.VnID, Item.Assort, Item.ByID
--     , AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined
-- 
-- 
-- -- -- -- Detail with adjustments
SELECT 
    AIMFcstStore.FcstStoreKey,
    AIMFcstStore.FcstStoreID
    , AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item, Item.ItDesc
    , 'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type, 'DataCalcDesc' = CODE_DCT.CodeDesc
    , 'AdjustType' = NULL, 'AdjustTypeDesc' = ''
    , 'Totals' = SUM (CASE WHEN
    AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
    AND AIMFcstStoreDetail.PeriodEndDate < '2004/11/01'
    THEN DataCalc_Value ELSE 0 END)
    , '2004/03/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/04/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/04/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/04/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/05/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/05/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/05/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/06/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/06/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/06/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/07/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/07/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/07/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/08/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/08/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/08/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/09/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/09/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/09/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/10/01'THEN DataCalc_Value ELSE 0 END)
    , '2004/10/01' = SUM (CASE WHEN AIMFcstStoreDetail.PeriodStartDate >= '2004/10/01' AND AIMFcstStoreDetail.PeriodEndDate < '2004/11/01'THEN DataCalc_Value ELSE 0 END)
    , Item.ItStat, Item.Class1, Item.Class2, Item.Class3, Item.Class4
    , Item.VelCode, Item.VnID, Item.Assort, Item.ByID
    , AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined
FROM AIMFcstStore
INNER JOIN AIMFcstStoreDetail ON AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
INNER JOIN ITEM ON AIMFcstStoreDetail.Item = Item.Item AND AIMFcstStoreDetail.LcId = Item.LcId
INNER JOIN AIMLocations ON AIMFcstStoreDetail.LcId = AIMLocations.LcID
INNER JOIN AIMCodeLookup CODE_DCT ON AIMFcstStoreDetail.DataCalc_Type = CODE_DCT.CodeID
WHERE AIMFcstStore.FcstSetupKey = 1
    AND AIMFcstStore.UserElement = 0
    AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
    AND AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
    AND AIMFcstStoreDetail.PeriodEndDate <= '2004/10/31'
    AND CODE_DCT.LangID = 'en-us' AND CODE_DCT.CodeType = 'DATACALCTYPE'
    AND  DataCalc_Type IN (1, 2, 9 )
    AND Item.Item = N'HEW51626A'
GROUP BY 
    AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID, AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
    , Item.ItDesc, Item.ItStat
    , AIMFcstStoreDetail.DataCalc_Type, CODE_DCT.CodeDesc
    , Item.ItStat, Item.Class1, Item.Class2, Item.Class3, Item.Class4
    , Item.VelCode, Item.VnID, Item.Assort, Item.ByID
    , AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined
UNION ALL
SELECT 
    AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID, AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
    , ''
    , 'DataCalc_Type' = AIMFcstStoreDetail.DataCalc_Type, 'DataCalcDesc' = CODE_DCT.CodeDesc
    , 'AdjustType' = VW_Adj.AdjustType, 'AdjustTypeDesc' = CODE_AT.CodeDesc, 'Totals' = CASE WHEN AdjustType <> 0 THEN
        SUM(CASE WHEN VW_Adj.AdjustStartDate >= '2004/03/01' AND VW_Adj.AdjustEndDate < '2004/11/01' THEN AdjustQty ELSE 0 END)
    ELSE MAX(CASE WHEN VW_Adj.AdjustStartDate >= '2004/03/01' AND VW_Adj.AdjustEndDate < '2004/11/01' THEN AdjustQty ELSE 0 END)
    END
    , '2004/03/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/03/01' AND VW_Adj.AdjustEndDate < '2004/04/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/03/01' AND VW_Adj.AdjustEndDate < '2004/04/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/04/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/04/01' AND VW_Adj.AdjustEndDate < '2004/05/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/04/01' AND VW_Adj.AdjustEndDate < '2004/05/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/05/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/05/01' AND VW_Adj.AdjustEndDate < '2004/06/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/05/01' AND VW_Adj.AdjustEndDate < '2004/06/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/06/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/06/01' AND VW_Adj.AdjustEndDate < '2004/07/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/06/01' AND VW_Adj.AdjustEndDate < '2004/07/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/07/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/07/01' AND VW_Adj.AdjustEndDate < '2004/08/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/07/01' AND VW_Adj.AdjustEndDate < '2004/08/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/08/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/08/01' AND VW_Adj.AdjustEndDate < '2004/09/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/08/01' AND VW_Adj.AdjustEndDate < '2004/09/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/09/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/09/01' AND VW_Adj.AdjustEndDate < '2004/10/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/09/01' AND VW_Adj.AdjustEndDate < '2004/10/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '2004/10/01' = CASE WHEN AdjustType <> 0 THEN 
        SUM (CASE WHEN VW_Adj.AdjustStartDate >= '2004/10/01' AND VW_Adj.AdjustEndDate < '2004/11/01' THEN VW_Adj.AdjustQty ELSE 0 END)
    ELSE MAX (CASE WHEN VW_Adj.AdjustStartDate >= '2004/10/01' AND VW_Adj.AdjustEndDate < '2004/11/01' THEN VW_Adj.AdjustQty  ELSE 0 END)
    END
    , '', '', '', '', '', '', '', '', '', '', '', '', ''
FROM AIMFcstStore
INNER JOIN AIMFcstStoreDetail ON AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
INNER JOIN AIM_DmdPlan_Adjustments VW_Adj ON AIMFcstStoreDetail.FcstStoreKey = VW_Adj.FcstStoreKey
    AND AIMFcstStoreDetail.LcID = VW_Adj.LcID
    AND AIMFcstStoreDetail.Item = VW_Adj.Item
INNER JOIN ITEM ON AIMFcstStoreDetail.Item = Item.Item AND AIMFcstStoreDetail.LcId = Item.LcId
INNER JOIN AIMLocations ON AIMFcstStoreDetail.LcId = AIMLocations.LcID
INNER JOIN AIMCodeLookup CODE_DCT ON AIMFcstStoreDetail.DataCalc_Type = CODE_DCT.CodeID
INNER JOIN AIMCodeLookup CODE_AT ON VW_Adj.AdjustType = CODE_AT.CodeID
WHERE AIMFcstStore.FcstSetupKey = 1
    AND AIMFcstStore.UserElement = 0
    AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
    AND AIMFcstStoreDetail.PeriodStartDate >= '2004/03/01'
    AND AIMFcstStoreDetail.PeriodEndDate <= '2004/10/31'
    AND VW_Adj.AdjustStartDate >= '2004/03/01'
    AND VW_Adj.AdjustEndDate <= '2004/10/31'
    AND CODE_DCT.LangID = 'en-us' AND CODE_DCT.CodeType = 'DATACALCTYPE'
    AND CODE_AT.LangID = 'en-us' AND CODE_AT.CodeType = 'ADJUSTTYPE'
    AND VW_Adj.EditStamp = CASE WHEN VW_Adj.AdjustType = 0 THEN (SELECT MAX(VW_Adj_Time.EditStamp)
        FROM AIM_DmdPlan_Adjustments VW_Adj_Time
        WHERE VW_Adj_Time.FcstStoreKey = VW_Adj.FcstStoreKey
        AND VW_Adj_Time.LcID = VW_Adj.LcID
        AND VW_Adj_Time.Item = VW_Adj.Item
        AND VW_Adj_Time.AdjustStartDate = VW_Adj.AdjustStartDate
        AND VW_Adj_Time.AdjustEndDate = VW_Adj.AdjustEndDate
        AND VW_Adj_Time.AdjustType = 0)
    ELSE VW_Adj.EditStamp END
    AND  DataCalc_Type = 2
    AND Item.Item = N'HEW51626A'
GROUP BY 
    AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID, AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
    , AIMFcstStoreDetail.DataCalc_Type, VW_Adj.AdjustType, CODE_DCT.CodeDesc, CODE_AT.CodeDesc
ORDER BY
    AIMFcstStore.FcstStoreID, AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item, AIMFcstStoreDetail.DataCalc_Type, VW_Adj.AdjustType
