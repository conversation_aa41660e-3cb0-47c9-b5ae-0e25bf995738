/* Created by: Index Tuning Wizard 	*/
/* Date: 4/1/2004 			*/
/* Time: 2:53:29 PM 			*/
/* Server Name: astocksdalelap 			*/
/* Database Name: SSA_AIM_DEMO 			*/
/* Workload File Name: C:\EXceed_AIM_SS\EXceedAIMProject\Code\AIMDemandPlanner\Scripts\DemandPlannerProfTrace.trc */

SET QUOTED_IDENTIFIER ON 
SET ARITHABORT ON 
SET CONCAT_NULL_YIELDS_NULL ON 
SET ANSI_NULLS ON 
SET ANSI_PADDING ON 
SET ANSI_WARNINGS ON 
SET NUMERIC_ROUNDABORT OFF 
go

DECLARE @bErrors as bit

BEGIN TRANSACTION
SET @bErrors = 0

CREATE CLUSTERED INDEX [ItemHistory1] ON [dbo].[ItemHistory] ([Item] ASC )
IF( @@error <> 0 ) SET @bErrors = 1

IF( @bErrors = 0 )
  COMMIT TRANSACTION
ELSE
  ROLLBACK TRANSACTION

BEGIN TRANSACTION
SET @bErrors = 0

CREATE CLUSTERED INDEX [Item2] ON [dbo].[Item] ([Lcid] ASC )
IF( @@error <> 0 ) SET @bErrors = 1

IF( @bErrors = 0 )
  COMMIT TRANSACTION
ELSE
  ROLLBACK TRANSACTION


BEGIN TRANSACTION
SET @bErrors = 0

CREATE CLUSTERED INDEX [AIMVendors4] ON [dbo].[AIMVendors] ([VnId] ASC )
IF( @@error <> 0 ) SET @bErrors = 1

IF( @bErrors = 0 )
  COMMIT TRANSACTION
ELSE
  ROLLBACK TRANSACTION


/* Statistics to support recommendations */

CREATE STATISTICS [hind_706101556_2A_1A] ON [dbo].[itemhistory] ([item], [lcid])
CREATE STATISTICS [hind_757577737_15A] ON [dbo].[item] ([vnid])
CREATE STATISTICS [hind_1977058079_1A_2A_4A_16A] ON [dbo].[aimvendors] ([vnid], [assort], [vname], [revcycle])
