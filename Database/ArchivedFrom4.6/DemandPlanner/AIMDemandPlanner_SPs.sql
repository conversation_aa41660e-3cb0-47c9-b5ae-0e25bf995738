/*******************************************************************************
-- THIS FILE CONTAINS ALL THE TABLE CREATION SCRIPTS 
--	FOR THE DEMAND PLANNER MODULE 
--	(See the companion script AI<PERSON>emandPlanner_StoredProcs.sql for all related stored procedures)
-- 	NOTE: This file is arranged such that dependencies are properly assigned in the system tables.
--			Please exercise due caution if re-arranging the order of sub-scripts.
--
--	STORED PROCEDURES
--	* AIM_DmdPlanFcstFreeze_Save_Sp
--	* AIM_DmdPlanFcstFreeze_Fetch_Sp
--	* AIM_DmdPlanFcstFreeze_Delete_Sp
--	
--	* AIM_DmdPlanFcstAccess_Update_Sp
--	* AIM_DmdPlanFcstAccess_Fetch_Sp
--	* AIM_DmdPlanFcstAccess_Delete_Sp
--	
--	* AIM_DmdPlan_FcstStoreKey_Fetch_Sp
--	
--	(Note, to save a user element, call the FcstStore_Save from the UI command of "Copy Forecast" with UserElement = 1)
-- 	* AIM_DmdPlanFcstUserElement_Fetch_Sp
-- 	* AIM_DmdPlanFcstUserElement_Delete_Sp   
--
-- 	* AIM_DmdPlanFcstSetup_Save_Sp     
-- 	* AIM_DmdPlanFcstSetup_Fetch_Sp
-- 	* AIM_DmdPlanFcstSetup_Delete_Sp   
--
--	* AIM_DxFA_Sp
--	
--	* AIM_DmdPlanFcstID_Load_Sp
--	* AIM_DmdPlanFcstUser_CheckAccess_Sp
--
--	* AIM_DmdPlan_ItemList_Sp
--	* AIM_DmdPlan_LocationAttribs_Sp
--	* AIM_DmdPlan_OptionAttribs_Sp
--	* AIM_DmdPlan_PromotionAttribs_Sp
--	* AIM_DmdPlan_SeasonsAttribs_Sp
--
--	* AIM_DmdPlan_ItemPricing_Sp
--	* AIM_DmdPlan_HstDmnd_Sp
--	* AIM_AIMCalendar_Calcs_SP
-- 	* AIM_AIMCalendar_BoundaryDates_Sp
--	* AIM_CalcPartialPeriodWeights_Sp
--	
--	* AIM_DmdPlanFcstStore_Save_Sp
-- 	* AIM_DmdPlanFcstStoreDetail_Save_Sp
--	* AIM_DmdPlanFcstComment_Save_Sp
--
--	* AIM_DmdPlan_Adjustments_Fetch_Sp
--	* AIM_DmdPlanAdjust_FetchGroupByItems_Sp	*TO BE COMMENTED OUT
--	* AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
--	
--	* AIM_DmdPlan_FcstDetail_Fetch_Sp	*TO BE COMMENTED OUT
--	* AIM_DmdPlan_FcstSummary_Fetch_Sp	*TO BE COMMENTED OUT
--
--	* AIM_DmdPlanFcstMaster_Fetch_Sp
-- 
--	* AIM_ItemAttributes_Fetch_Sp	*TO BE COMMENTED OUT
--	* AIM_DmdPlanAdjust_PromoteToMaster_Sp
--
--
--	Author:		Annalakshmi Stocksdale
--	Created:	2004/01/01
-------------------------------------------------------------------------------
--	Change History
-------------------------------------------------------------------------------
--	Date:		Updated by:		Description:
--	----------	------------		-----------------------------------------------
*******************************************************************************/

/******************************************************************************/


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstFreeze_Save_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstFreeze_Save_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstFreeze_Save_Sp
**	Desc: Updates AIMFcstFreeze for the given FcstID, based on the FcstLocked.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstFreeze_Save_Sp
(
	-- Conditions
	@UserID as nvarchar(12),
	-- Fields
	@FcstSetupKey as int,
	@FreezeType as tinyint,
	@LcID as nvarchar(12),
	@FreezeLimit as nvarchar(255),
	-- Return parameters
 	@FcstFreezeKey int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat int
	DECLARE @AccessCode int
	
	-- Validate the required parameters.
	IF @UserID IS NULL
	OR @FcstSetupKey IS NULL 
	OR @FreezeType IS NULL
	OR @LcID IS NULL
	BEGIN
	  	RETURN -1
	END

	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	
	-- Update if not locked and has access
	-- IF @AccessCode >= 2
	-- BEGIN
		SELECT @FcstFreezeKey = AIMFcstFreeze.FcstFreezeKey 
		FROM AIMFcstFreeze 
		WHERE AIMFcstFreeze.FcstSetupKey = @FcstSetupKey AND
			AIMFcstFreeze.FreezeType = @FreezeType AND
			AIMFcstFreeze.LcID = @LcID

		IF @@ROWCOUNT = 0 
		BEGIN
			INSERT INTO AIMFcstFreeze (
				FcstSetupKey,
				FreezeType, 
				LcID
			) VALUES (
				@FcstSetupKey,
				@FreezeType, 
				@LcID
			)
			SET @FcstFreezeKey = @@IDENTITY
		END

	  	UPDATE AIMFcstFreezeLimit SET 
			FreezeLimit = ISNULL(@FreezeLimit, AIMFcstFreezeLimit.FreezeLimit)
	  	WHERE AIMFcstFreezeLimit.FcstFreezeKey = @FcstFreezeKey
		SET @Return_Stat = @@ROWCOUNT

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstFreeze_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstFreeze_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlanFcstFreeze_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**		FcstID == a valid AIMFcst.Forecast ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstFreeze_Fetch_Sp 
(
	@FcstSetupKey as int,
	@UserID nvarchar(12),
	@LangID nvarchar(10)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat int
	DECLARE @FcstFreezeKey int
	DECLARE @AccessCode int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	BEGIN
		RETURN -1
	END
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
--	BEGIN
		SELECT 
			AIMFcstFreeze.FcstFreezeKey
			, AIMFcstFreeze.FcstSetupKey
			, AIMFcstFreeze.FreezeType
			, AIMFcstFreeze.LcID
			, FreezeLimit = AIMFcstFreezeLimit.FreezeLimit
			, FreezeDescription = CASE 
				WHEN RTRIM(CONVERT(nvarchar(50), AIMFcstFreeze.FreezeType))  = '0' 
					THEN CONVERT(nvarchar(50), AIMFcstSetup.FcstInterval)
				WHEN RTRIM(CONVERT(nvarchar(50), AIMFcstFreeze.FreezeType))  = 1 
					THEN (	SELECT Item.ItDesc 
							FROM Item 
							WHERE Item.Item = AIMFcstFreezeLimit.FreezeLimit AND 
								Item.LcID = AIMFcstFreeze.LcID)
				END
		FROM AIMFcstFreeze
		INNER JOIN AIMFcstFreezeLimit ON
			AIMFcstFreeze.FcstFreezeKey = AIMFcstFreezeLimit.FcstFreezeKey
		INNER JOIN AIMFcstSetup ON
			AIMFcstFreeze.FcstSetupKey = AIMFcstSetup.FcstSetupKey
		WHERE AIMFcstFreeze.FcstSetupKey = @FcstSetupKey 

		RETURN 1		-- SUCCEED	
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstFreeze_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstFreeze_Delete_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstFreeze_Delete_Sp
**	Desc: Deletes AIMFcstFreeze records
**
**	Returns: 1)@Return_Stat - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstFreeze_Delete_Sp
(
	@FcstSetupKey as int,
	@UserID nvarchar(12),
 	@FcstFreezeKey int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @AccessCode int
	DECLARE @Return_Stat int

	-- Validate the required parameters.
	IF @FcstSetupKey Is Null
	OR @UserID Is Null
	BEGIN
	  	RETURN -1
	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	AND @FcstLocked = 'N'
--	BEGIN
		BEGIN TRAN --Start of Transaction
		
		DELETE FROM AIMFcstFreezeLimit
		WHERE 
			AIMFcstFreezeLimit.FcstFreezeKey = CASE 
				WHEN @FcstFreezeKey IS NULL THEN (SELECT FcstFreezeKey FROM AIMFcstFreeze WHERE FcstSetupKey = @FcstSetupKey)
				ELSE @FcstFreezeKey
				END
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		DELETE FROM AIMFcstFreeze
		WHERE AIMFcstFreeze.FcstSetupKey = @FcstSetupKey
			AND AIMFcstFreeze.FcstFreezeKey = CASE 
				WHEN @FcstFreezeKey IS NULL THEN AIMFcstFreeze.FcstFreezeKey
				ELSE @FcstFreezeKey
				END
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		COMMIT TRAN  -- End of Transaction
	
		RETURN 1
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO




/******************************************************************************/

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstAccess_Update_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstAccess_Update_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstAccess_Update_Sp
**	Desc: Updates AIMFcstAccess for the given FcstID, based on the FcstLocked.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstAccess_Update_Sp
(
	@FcstSetupKey as int,
	@UserID as nvarchar (12),
	@AccessCode as tinyint,
	@Save as bit = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat int
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL 
	OR @UserID IS NULL
	BEGIN
	  	RETURN -1
	END

	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	
	-- Update if not locked and has access
	-- IF @AccessCode >= 2
	-- BEGIN
		SELECT @Return_Stat = COUNT(*) 
		FROM AIMFcstAccess
		WHERE 
			AIMFcstAccess.FcstSetupKey = @FcstSetupKey AND
			AIMFcstAccess.UserID = @UserID AND
			AIMFcstAccess.AccessCode = @AccessCode
 		IF @Return_Stat = 0
		BEGIN
			IF @Save = 1
			BEGIN
				INSERT INTO AIMFcstAccess (
					FcstSetupKey,
					UserID, 
					AccessCode
				) VALUES (
					@FcstSetupKey,
					@UserID, 
					@AccessCode			
				)
			END
			ELSE
			BEGIN
				DELETE FROM AIMFcstAccess
				WHERE 
					AIMFcstAccess.FcstSetupKey = @FcstSetupKey AND
					AIMFcstAccess.UserID = @UserID AND
					AIMFcstAccess.AccessCode = @AccessCode
			END
		END
		SET @Return_Stat = @@ROWCOUNT

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstAccess_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstAccess_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlanFcstAccess_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**		FcstID == a valid AIMFcst.Forecast ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstAccess_Fetch_Sp 
(
	@FcstSetupKey as int,
	@UserID as nvarchar(12) = NULL
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	OR @UserID IS NULL
	BEGIN
		RETURN -1
	END
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
--	BEGIN

		SELECT 
			AIMFcstAccess.FcstSetupKey,
			UserID = CASE 
				WHEN UPPER(@UserID) = 'SA' THEN 'SA'
				ELSE AIMFcstAccess.UserID END, 
			UserName = CASE 
				WHEN AIMFcstAccess.UserID IS NOT NULL THEN (SELECT AIMUsers.UserName FROM AIMUsers WHERE AIMUsers.UserID = AIMFcstAccess.UserID) 
				ELSE '' END,
			AccessCode = CASE WHEN UPPER(@UserID) = 'SA' AND AIMFcstAccess.AccessCode IS NULL THEN 0 ELSE AIMFcstAccess.AccessCode END
		FROM AIMFcstAccess
		WHERE AIMFcstAccess.FcstSetupKey = @FcstSetupKey 
		AND AIMFcstAccess.UserID  = CASE 
			WHEN UPPER(@UserID) = 'ALL' THEN AIMFcstAccess.UserID
			WHEN (UPPER(@UserID) = 'SA' AND AIMFcstAccess.AccessCode IS NOT NULL) THEN @UserID 
			WHEN (UPPER(@UserID) = 'SA' AND AIMFcstAccess.AccessCode IS NULL) THEN AIMFcstAccess.UserID
			ELSE @UserID
			END
		GROUP BY
			FcstSetupKey,
			UserID,
			AccessCode
		ORDER BY
			FcstSetupKey,
			UserID,
			AccessCode

		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat > 0 
		BEGIN
			RETURN @Return_Stat
		END
		ELSE
		BEGIN
			RETURN -1
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstAccess_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstAccess_Delete_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstAccess_Delete_Sp
**	Desc: Deletes AIMFcstAccess records
**
**	Returns: 1)@Return_Stat - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstAccess_Delete_Sp
(
	@FcstSetupKey as int,
	@UserID as nvarchar (12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @Return_Stat int

	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL 
	OR @UserID IS NULL
	BEGIN
	  	RETURN -1
	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	AND @FcstLocked = 'N'
--	BEGIN
		DELETE FROM AIMFcstAccess
		WHERE 
			AIMFcstAccess.FcstSetupKey = @FcstSetupKey AND
			AIMFcstAccess.UserID = @UserID 

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		RETURN 1
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_FcstStoreKey_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_FcstStoreKey_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_FcstStoreKey_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_FcstStoreKey_Fetch_Sp 
(
    	@FcstSetupKey as int, 
	@UserElement as bit = 0,
	@FetchComment as bit = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat int
	DECLARE @FcstStoreKey int
	
	SET NOCOUNT ON

	-- Validate required parameters
	IF @FcstSetupKey IS NULL
	BEGIN
		RETURN -1
	END

	SELECT AIMFcstStore.FcstStoreKey, 
		AIMFcstStore.FcstStoreID, 
		FcstComment = CASE WHEN @FetchComment = 1 THEN AIMFcstStore.FcstComment ELSE 'NULL' END
	FROM AIMFcstStore
	WHERE
		AIMFcstStore.FcstSetupKey = @FcstSetupKey
		AND AIMFcstStore.UserElement = @UserElement

	SET @Return_Stat = @@ROWCOUNT
	
	IF @Return_Stat > 0 RETURN 1		-- SUCCEED	
	ELSE RETURN 0		-- FAIL

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstUserElement_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstUserElement_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlanFcstUserElement_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**		FcstStoreID == a valid AIMFcstStore.Forecast Store ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**			7 == Get All for given AIMFcstSetup.Forecast ID
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstUserElement_Fetch_Sp 
(
    	@FcstSetupKey as int, 
	@UserID as nvarchar(12),
	@FetchDetails as bit = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat int
	DECLARE @FcstStoreKey int
	
	SET NOCOUNT ON
	
	IF @FetchDetails = 1
	BEGIN
		SELECT -- HEADER
			AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID, 
			AIMFcstStore.FcstComment,
			-- DETAIL
			AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item,
			AIMFcstStoreDetail.PeriodStartDate, AIMFcstStoreDetail.PeriodEndDate,
			AIMFcstStoreDetail.TargetPeriod, 
			AIMFcstStoreDetail.DataCalc_Type, AIMFcstStoreDetail.DataCalc_Value
		FROM AIMFcstStore
		INNER JOIN AIMFcstStoreDetail ON 
			AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
		WHERE 
			AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
			AIMFcstStore.UserElement = 1 
		ORDER BY 
			AIMFcstStoreDetail.Item, 
			AIMFcstStoreDetail.LcID,
			AIMFcstStoreDetail.PeriodStartDate, 
			AIMFcstStoreDetail.DataCalc_Type,
			AIMFcstStoreDetail.PeriodEndDate,
			AIMFcstStoreDetail.TargetPeriod
			
		SET @Return_Stat = @@ROWCOUNT
	END
	ELSE
	BEGIN
	    	EXEC @Return_Stat = AIM_DmdPlan_FcstStoreKey_Fetch_Sp @FcstSetupKey, 1, 1
	END

	IF @Return_Stat > 0 RETURN 1		-- SUCCEED	
	ELSE RETURN 0		-- FAIL

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstUserElement_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstUserElement_Delete_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstUserElement_Delete_Sp
**	Desc: Deletes AIMFcst and all dependent tables.
**
**	Returns: 1)@Return_Stat - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstUserElement_Delete_Sp
(
	@FcstStoreID as nvarchar(50),
	@UserID as nvarchar(12) = ''
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @AccessCode int
	DECLARE @FcstSetupKey int
	DECLARE @FcstStoreKey int
	DECLARE @Return_Stat int

	-- Validate the required parameters.
	IF @FcstStoreID Is Null
	OR @UserID Is Null
	BEGIN
	  	RETURN -1
	END

	SELECT 
		@FcstSetupKey = AIMFcstStore.FcstSetupKey,
		@FcstStoreKey = AIMFcstStore.FcstStoreKey
	FROM AIMFcstStore
	WHERE AIMFcstStore.FcstStoreID = @FcstStoreID AND
		AIMFcstStore.UserElement = 1

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
	BEGIN
		BEGIN TRAN --Start of Transaction
		
		DELETE FROM AIMFcstStore
		WHERE FcstStoreKey = @FcstStoreKey
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
		
		COMMIT TRAN  -- End of Transaction
	
		RETURN 1
	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstSetup_Save_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstSetup_Save_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstSetup_Save_Sp
**	Desc: Updates AIMFcstSetup for the given FcstID, based on the FcstLocked.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstSetup_Save_Sp
(
	-- Conditions
	@UserID as nvarchar(12),
	@UpdateForRolling as bit = 0,
	-- Fields
	@FcstID as nvarchar(12) = '',
	@FcstDesc as nvarchar(40),
	@FcstHierarchy as tinyint,
	@FcstEnabled as bit,
	@FcstLocked as bit,
	@FcstStartDate as datetime,
	@LastUpdated as datetime, 
	@FcstRolling as bit,
	@FcstHistory as bit,
	@FcstPds_Future as tinyint,
	@FcstPds_Historical as tinyint,
	@FcstInterval as tinyint,
	@FcstUnit as tinyint,
	@ApplyTrend as tinyint,
	@Calc_SysRqmt as bit,
	@Calc_NetRqmt as bit,
	@Calc_PrjInvt as bit,
	@Calc_HstDmnd as bit,
	@Calc_NetRqmtAndPlnRcpt as bit,
	@Calc_NetRqmtAndPrdCons as bit,
	@Calc_PrjInvtAndPrdCons as bit,
	-- Item filter criteria
	@Item as nvarchar(25),
	@ItStat as nvarchar(1),
	@VnID as nvarchar(12),
	@Assort as nvarchar(12),
	@ByID as nvarchar(12),
	@Class1 as nvarchar(50),
	@Class2 as nvarchar(50),
	@Class3 as nvarchar(50),
	@Class4 as nvarchar(50),
	@LcID as nvarchar(12),
	@LStatus as nvarchar(1),
	@LDivision as nvarchar(20),
	@LRegion as nvarchar(20),
	@LUserDefined as nvarchar(30),
	-- Return parameters
 	@FcstSetupKey as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @CkFcstLocked as bit
	DECLARE @AccessCode as int
	
	-- Validate the required parameters.
	IF @FcstID IS Null
	OR @UserID IS NULL
	BEGIN
	  	RETURN -1
	END

	IF @UpdateForRolling = 1
	BEGIN
		UPDATE AIMFcstSetup SET
			LastUpdated = ISNULL(@LastUpdated, AIMFcstSetup.LastUpdated)
		WHERE AIMFcstSetup.FcstSetupKey = @FcstSetupKey
	
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat > 0 
		BEGIN
			RETURN 1
		END
		ELSE 
		BEGIN
			RETURN -1	
		END
	END
	ELSE
	BEGIN
		SELECT 
			@FcstSetupKey = FcstSetupKey,
			@CkFcstLocked = FcstLocked 
		FROM AIMFcstSetup 
		WHERE AIMFcstSetup.FcstID = @FcstID 
		SET @Return_Stat = @@ROWCOUNT

	END

	IF @Return_Stat > 0
	BEGIN
	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	

	--	-- Update if not locked and has access
	-- 	IF @CkFcstLocked = 0
	-- 	AND @AccessCode >= 2
	-- 	BEGIN
		  	UPDATE AIMFcstSetup SET 
		  		FcstDesc = ISNULL(@FcstDesc, AIMFcstSetup.FcstDesc),
		   		FcstHierarchy = ISNULL(@FcstHierarchy, AIMFcstSetup.FcstHierarchy),
		   		FcstEnabled = ISNULL(@FcstEnabled, AIMFcstSetup.FcstEnabled),
		   		FcstLocked = ISNULL(@FcstLocked, AIMFcstSetup.FcstLocked),
		   		FcstStartDate = ISNULL(@FcstStartDate, AIMFcstSetup.FcstStartDate),
				LastUpdated = ISNULL(@LastUpdated, AIMFcstSetup.LastUpdated),	
		   		FcstRolling = ISNULL(@FcstRolling, AIMFcstSetup.FcstRolling),
		   		FcstHistory = ISNULL(@FcstHistory, AIMFcstSetup.FcstHistory),
		   		FcstPds_Future = ISNULL(@FcstPds_Future, AIMFcstSetup.FcstPds_Future),
		   		FcstPds_Historical = ISNULL(@FcstPds_Historical, AIMFcstSetup.FcstPds_Historical),
		   		FcstInterval = ISNULL(@FcstInterval, AIMFcstSetup.FcstInterval),
				FcstUnit = ISNULL(@FcstUnit, AIMFcstSetup.FcstUnit),
		   		ApplyTrend = ISNULL(@ApplyTrend, AIMFcstSetup.ApplyTrend),
				Calc_SysRqmt = ISNULL(@Calc_SysRqmt, AIMFcstSetup.Calc_SysRqmt),
				Calc_NetRqmt = ISNULL(@Calc_NetRqmt, AIMFcstSetup.Calc_NetRqmt),
				Calc_PrjInvt = ISNULL(@Calc_PrjInvt, AIMFcstSetup.Calc_PrjInvt),
				Calc_HstDmnd = ISNULL(@Calc_HstDmnd, AIMFcstSetup.Calc_HstDmnd),
				Calc_NetRqmtAndPlnRcpt = ISNULL(@Calc_NetRqmtAndPlnRcpt, AIMFcstSetup.Calc_NetRqmtAndPlnRcpt),
				Calc_NetRqmtAndPrdCons = ISNULL( @Calc_NetRqmtAndPrdCons, AIMFcstSetup.Calc_NetRqmtAndPrdCons),
				Calc_PrjInvtAndPrdCons = ISNULL( @Calc_PrjInvtAndPrdCons, AIMFcstSetup.Calc_PrjInvtAndPrdCons),
		   		Item = ISNULL(@Item, AIMFcstSetup.Item),
		   		ItStat = ISNULL(@ItStat, AIMFcstSetup.ItStat),
		   		VnID = ISNULL(@VnID, AIMFcstSetup.VnID),
		   		Assort = ISNULL(@Assort, AIMFcstSetup.Assort),
		   		ByID = ISNULL(@ByID, AIMFcstSetup.ByID),
		   		Class1 = ISNULL(@Class1, AIMFcstSetup.Class1),
		   		Class2 = ISNULL(@Class2, AIMFcstSetup.Class2),
		   		Class3 = ISNULL(@Class3, AIMFcstSetup.Class3),
		   		Class4 = ISNULL(@Class4, AIMFcstSetup.Class4),
		   		LcID = ISNULL(@LcID, AIMFcstSetup.LcID),
		   		LStatus = ISNULL(@LStatus, AIMFcstSetup.LStatus),
		   		LDivision = ISNULL(@LDivision, AIMFcstSetup.LDivision),
		   		LRegion = ISNULL(@LRegion, AIMFcstSetup.LRegion),
		   		LUserDefined = ISNULL(@LUserDefined, AIMFcstSetup.LUserDefined)
		  	WHERE AIMFcstSetup.FcstSetupKey = @FcstSetupKey
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
	--	END
	-- 	ELSE
	-- 	BEGIN
	-- 	 	RETURN -3 -- Not Permitted Premissions
	--	END
	END
	ELSE
	BEGIN	
		-- Validate the required parameters.
		IF @FcstID IS Null
		OR @UserID IS NULL
		OR @FcstDesc IS NULL
		OR @FcstHierarchy IS NULL
		OR @FcstEnabled IS NULL
		OR @FcstLocked IS NULL
		OR @FcstStartDate IS NULL
		OR @FcstRolling IS NULL
		OR @FcstHistory IS NULL
		OR @FcstPds_Future IS NULL
		OR @FcstPds_Historical IS NULL
		OR @FcstInterval IS NULL
		OR @FcstUnit IS NULL
		OR @ApplyTrend IS NULL
		BEGIN
		  	RETURN -1
		END

		-- Insert
		INSERT INTO AIMFcstSetup (
			FcstID, FcstDesc,
		   	FcstHierarchy, FcstEnabled, FcstLocked,
		   	FcstStartDate, LastUpdated, 
			FcstRolling, FcstHistory,
		   	FcstPds_Future, FcstPds_Historical,
		   	FcstInterval, FcstUnit, ApplyTrend,
			Calc_SysRqmt, Calc_NetRqmt,
			Calc_PrjInvt, Calc_HstDmnd,
			Calc_NetRqmtAndPlnRcpt, Calc_NetRqmtAndPrdCons,
			Calc_PrjInvtAndPrdCons,
		   	Item, ItStat, VnID, Assort, ByID,
		   	Class1, Class2, Class3, Class4,
		   	LcID, LStatus, LDivision, LRegion, LUserDefined
		) VALUES (
			@FcstID, @FcstDesc,
		   	@FcstHierarchy, @FcstEnabled, @FcstLocked,
		   	@FcstStartDate, @FcstStartDate, 	-- The first time a setup is saved, we want the LastUpdated to be the same as or earlier than the StartDate
			@FcstRolling, @FcstHistory,	
		   	@FcstPds_Future, @FcstPds_Historical,
		   	@FcstInterval, @FcstUnit, @ApplyTrend,
			ISNULL(@Calc_SysRqmt, 0), ISNULL(@Calc_NetRqmt, 0),
			ISNULL(@Calc_PrjInvt, 0), ISNULL(@Calc_HstDmnd, 0),
			ISNULL(@Calc_NetRqmtAndPlnRcpt, 0), ISNULL( @Calc_NetRqmtAndPrdCons, 0),
			ISNULL( @Calc_PrjInvtAndPrdCons, 0),
		   	ISNULL(@Item, ''), ISNULL(@ItStat, ''), ISNULL(@VnID, ''), ISNULL(@Assort, ''), ISNULL(@ByID, ''),
			ISNULL(@Class1, ''), ISNULL(@Class2, ''), ISNULL(@Class3, ''), ISNULL(@Class4, ''),
			ISNULL(@LcID, ''), ISNULL(@LStatus, ''), ISNULL(@LDivision, ''), ISNULL(@LRegion, ''), ISNULL(@LUserDefined, '')
		  )

		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
		ELSE
		BEGIN
			SET @FcstSetupKey = @@IDENTITY
		END
	END

	RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstSetup_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstSetup_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlanFcstSetup_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**		FcstID == a valid AIMFcst.Forecast ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**		Related == Determines of the stored procedure should return related forecast setup information
**			0 = False -- only fetch FROM AIMFcstSetup
**			1 = True -- fetch FROM AIMFcstSetup and dependents
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstSetup_Fetch_Sp 
(
    	@FcstID as nvarchar(12) = '', 
	@UserID as nvarchar(12),
    	@Action as tinyint = 0,
	@Related as bit, 
	@LangID as nvarchar(20)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	DECLARE @Return_Stat as int
	DECLARE @FcstSetupKey as int
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
	IF @Action IS NULL
	BEGIN
		RETURN -1
	END
	
	
	-- Get the key to the Forecast Record based on the action code
	IF @Action = 0		-- Get Equal
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID = @FcstID
		ORDER BY FcstSetupKey

		SET @Return_Stat = @@ROWCOUNT
	END
	IF @Action = 1		-- Get Greater Than
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID > @FcstID
		
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
		BEGIN
			SET @Action = 6	-- Get Last Record
		END
	END
	IF @Action = 2		-- Get Less Than 
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID < @FcstID
		ORDER BY FcstID DESC
		
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
		BEGIN
			SET @Action = 5      -- Get first record
		END
	END
	IF @Action = 3		-- Get Greater Than or Equal
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID >= @FcstID
	    	
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
	      BEGIN
		      SET @Action = 6      -- Get last record
		END
	END
	IF @Action = 4		-- Get Less Than or Equal
	BEGIN
		SELECT TOP 1 
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup 
		WHERE FcstID <= @FcstID
		ORDER BY FcstID DESC

		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat < 1
	      BEGIN
		      SET @Action = 5      -- Get first record
		END
	END

	IF @Action = 5		-- Get First
	BEGIN
		SELECT TOP 1
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup  
		ORDER BY FcstID, FcstSetupKey

		SET @Return_Stat = @@ROWCOUNT
	END
	IF @Action = 6		-- Get Last
	BEGIN
		SELECT TOP 1
			@FcstID = FcstID, 
			@FcstSetupKey = FcstSetupKey 
		FROM AIMFcstSetup  
		ORDER BY FcstID DESC 
		
		SET @Return_Stat = @@ROWCOUNT
	END


	-- set return recordset(s) and status value
	IF @Return_Stat > 0 
	BEGIN
	    	SELECT FcstSetupKey, 
			FcstID, FcstDesc, FcstHierarchy,
			FcstEnabled, FcstLocked, 
			FcstStartDate, LastUpdated, 
			FcstRolling, FcstHistory,
			FcstPds_Future, FcstPds_Historical,
			FcstInterval,
			FcstUnit, 
			ApplyTrend,
			Calc_SysRqmt,
			Calc_NetRqmt,
			Calc_PrjInvt,
			Calc_HstDmnd,
			Calc_NetRqmtAndPlnRcpt,
			Calc_NetRqmtAndPrdCons,
			Calc_PrjInvtAndPrdCons,
			Item = ISNULL(Item, ''),
			ItStat = ISNULL(ItStat, ''),
			VnID = ISNULL(VnID, ''),
			Assort = ISNULL(Assort, ''),
			ByID = ISNULL(ByID, ''),
			Class1 = ISNULL(Class1, ''),
			Class2 = ISNULL(Class2, ''),
			Class3 = ISNULL(Class3, ''),
			Class4 = ISNULL(Class4, ''),
			LcID = ISNULL(LcID, ''),
			LStatus = ISNULL(LStatus, ''),
			LDivision = ISNULL(LDivision, ''),
			LRegion = ISNULL(LRegion, ''),
			LUserDefined = ISNULL(LUserDefined, '')
		FROM AIMFcstSetup 
		WHERE FcstSetupKey = @FcstSetupKey
	
		IF @Related = 1
		BEGIN
			-- Return more recordsets with data from ForecastAccess and UserElement
			EXEC @Return_Stat = AIM_DmdPlanFcstAccess_Fetch_Sp @FcstSetupKey, @LangID

			-- User Elements are stored in the forecast Store, with a field identifying them.
			EXEC @Return_Stat = AIM_DmdPlanFcstUserElement_Fetch_Sp @FcstSetupKey, @UserID, @Action
		END

		RETURN 1		-- SUCCEED	
	END
	ELSE
	BEGIN
		RETURN 0		-- FAIL
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstSetup_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstSetup_Delete_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstSetup_Delete_Sp
**	Desc: Deletes AIMFcst and all dependent tables.
**
**	Returns: 1)@Return_Stat - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstSetup_Delete_Sp
(
	@FcstID as nvarchar(12),
	@UserID as nvarchar(12) = ''
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @AccessCode as int
	DECLARE @FcstSetupKey as int
	DECLARE @FcstLocked as bit
	DECLARE @Return_Stat as int

	-- Validate the required parameters.
	IF @FcstID Is Null
	OR @UserID Is Null
	BEGIN
	  	RETURN -1
	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	
	SELECT 
		@FcstSetupKey = FcstSetupKey,
		@FcstLocked = FcstLocked 
	FROM AIMFcstSetup
	WHERE FcstID = @FcstID

-- 	IF @AccessCode >= 2 
-- 	AND @FcstLocked = 'N'
	IF @FcstLocked = 0
	BEGIN
		BEGIN TRAN --Start of Transaction
		
		DELETE FROM AIMFcstAccess
		WHERE FcstSetupKey = @FcstSetupKey
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		DELETE FROM AIMFcstFreeze
		WHERE FcstSetupKey = @FcstSetupKey
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
	 	DELETE FROM AIMFcstSetup
		WHERE FcstSetupKey = @FcstSetupKey
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
	  	BEGIN
			ROLLBACK TRAN
	 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	  	END
	
		COMMIT TRAN  -- End of Transaction
	
		RETURN 1
	END
	ELSE
	BEGIN
	 	RETURN -3 -- Not Permitted Premissions
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_DxFA_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_DxFA_Sp]
GO
/***********************************************************************
-- Name: AIM_DxFA_Sp
-- Desc: Loads substitute item records into the AllocItemSubstitutes table
--           from the AIMDxFA Table. 
-- 
-- Returns: 
--		@InsertCounter -- number of rows inserted
--		@UpdateCounter -- number of rows updated
--		@ReturnCode -- possible values:
--			1)  0 - Successful
--              	2) -1 - No Data Found
--              	3) -2 - SQL Error
--              	4) -3 - Duplicate File Name
--              	5) -4 - Invalid File Name
-- 
-- Author:		Annalakshmi Stocksdale
-- Created:	2003/05/28
---------------------------------------------------------------------------------------------------- 
-- Change History
----------------------------------------------------------------------------------------------------
-- Date:		Updated by:		Description:
-- ----------	------------		-----------------------------------------------
***********************************************************************/
 
CREATE PROCEDURE AIM_DxFA_Sp
(
  	@FileName nvarchar(255) = 'All',
	@InsertCounter int OUTPUT,
	@UpdateCounter int OUTPUT	
)

-- WITH ENCRYPTION	-- Production use must be encrypted 
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	-- Start variable declaration 
	DECLARE @Return_Stat int

	SET NOCOUNT ON

	-- Start variable initialization 
	SELECT @InsertCounter = 0
		, @UpdateCounter = 0

	-- This is a data interface transaction <Transaction Code=FA>. 
	-- Bulk Load the <Transaction Table=AIMDxFA> from flat file 
	EXEC @Return_Stat = AIM_DxBulkInsert_Sp @FileName, 'FA', 'AIMDxFA', '\t'
		-- This procedure 
		--	(a) validates file names  
		--	(b)transfers data from file to table.
		-- and returns the following status codes: 
		--	1)  0 - Successful
		--	2) -1 - No Data Found
		--	3) -2 - SQL Error
		--	4) -3 - Duplicate File Name
		--	5) -4 - Invalid File Name
	IF @Return_Stat <> 0
	BEGIN
 		RETURN @Return_Stat	-- Exit procedure
	END
	-- ELSE, continue with moving data from bulk inserts to AIM's regular tables.

	BEGIN TRANSACTION	
		INSERT INTO AIMFcstStoreAdjustLog (
			FcstStoreKey
			, LcID
			, Item
			, IsPromoted
			, AdjustType
			, AdjustQty
			, AdjustStartDate 
			, AdjustEndDate
			, AdjustUserID
			, AdjustDesc
			, AdjustDateTime
		) SELECT
			AIMFcstStore.FcstStoreKey
			, AIMDxFA.LcID
			, AIMDxFA.Item
			, 0			-- A new record is obviously not promoted to master yet
			, AIMDxFA.AdjustType
			, AIMDxFA.AdjustQty
			, AIMDxFA.AdjustStartDate
			, AIMDxFA.AdjustEndDate
			, ISNULL(AIMDxFA.UserID, '')
			, ISNULL(AIMDxFA.RecordDesc, '')
			, GETDATE()
		FROM AIMDxFA
		INNER JOIN AIMFcstSetup ON
			AIMDxFA.FcstID = AIMFcstSetup.FcstID
		INNER JOIN AIMFcstStore ON
			AIMDxFA.FcstStoreID = AIMFcstStore.FcstStoreID AND
			AIMFcstSetup.FcstSetupKey = AIMFcstStore.FcstSetupKey
		INNER JOIN AIMFcstStoreDetail ON
			AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey AND
			AIMDxFA.Item = AIMFcstStoreDetail.Item AND
			AIMDxFA.LcID = AIMFcstStoreDetail.LcID

		SET @Return_Stat = @@ROWCOUNT

		IF @Return_Stat < 0 
		BEGIN
			ROLLBACK TRANSACTION
		END
	COMMIT TRANSACTION

	-- Delete records from the DxFA Table
	TRUNCATE TABLE AIMDxFA
	
	-- Just in case the operator doesn't have truncate priviledges
	DELETE FROM AIMDxFA

	RETURN 1	-- SUCCESS

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


/*******************************************************************************/
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstID_Load_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstID_Load_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstID_Load_Sp
**	Desc: Retrieves Forecast ID and Description for use in the UI dropdowns
**	Derived from AIM_GetList_FcstID_Sp. Should replace that stored procedure once AIMFcst.tab is deleted.
**	Returns: 1)@rowcount - Can be Zero
**	Values:  AIMDays
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/     
CREATE PROCEDURE AIM_DmdPlanFcstID_Load_Sp
(
	@FcstEnabled as bit,
	@UserID as nvarchar(12),
	@RecordCount as int OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	  
	SELECT 
		AIMFcstSetup.FcstID,
		AIMFcstSetup.FcstDesc
	FROM AIMFcstSetup
	WHERE AIMFcstSetup.FcstEnabled = CASE
		WHEN @FcstEnabled IS NOT NULL THEN @FcstEnabled ELSE AIMFcstSetup.FcstEnabled END
	AND AIMFcstSetup.FcstSetupKey IN 
		(SELECT FcstSetupKey FROM AIMFcstAccess WHERE AIMFcstAccess.UserID = CASE UPPER(@UserID) WHEN 'SA' THEN AIMFcstAccess.UserID ELSE @UserID END GROUP BY AIMFcstAccess.FcstSetupKey) 
	GROUP BY
		AIMFcstSetup.FcstID, 
		AIMFcstSetup.FcstDesc
	ORDER BY 
		AIMFcstSetup.FcstID, 
		AIMFcstSetup.FcstDesc

	SELECT @RecordCount = @@ROWCOUNT
	
	IF @RecordCount > 0
	BEGIN
		RETURN 1
	END
	ELSE
	BEGIN
		RETURN @@ERROR
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

/*******************************************************************************/
SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstUser_CheckAccess_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstUser_CheckAccess_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstUser_CheckAccess_Sp
**	Desc: Returns AIMCodeLookup.CodeID and CodeDesc for the relevant AccessCode, 
**		given a forecast record and a user ID.
**	Derived from AIM_DmdPlanFcstUserAccess_Validate_Sp and AIM_DmdPlanFcstMainUserAccess_Validate_Sp. 
**	Should replace these stored procedures once AIMFcst.tab is deleted.
**
**	Returns: 
**		1)@rowcount - Can be Zero
**		2) AccessCode/AccessDesc - valid values:
**			0/No Access
**			1/Full Control
-- TO BE DONE!!!
**	Values:  AIMDays
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/
     
CREATE PROCEDURE AIM_DmdPlanFcstUser_CheckAccess_Sp
(
	@FcstSetupKey as int,
	@UserID as nvarchar(12),
	@AccessCode as tinyint,
	@HasAccess as bit OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	
	DECLARE @Return_Stat as int

	-- Validate parameters
	IF @FcstSetupKey IS NULL
	OR @UserID IS NULL
	OR @AccessCode IS NULL
	BEGIN
		RETURN -1
	END

	-- Check for SA -- grant complete access	
	IF Upper(@UserID) = 'SA'
	BEGIN
		SET @HasAccess = 1	-- Yes
	END
	ELSE

	-- Get Access Type from AIMFcstAccess
	BEGIN
		SELECT @Return_Stat = COUNT(*)
		FROM AIMFcstAccess
		WHERE 
			AIMFcstAccess.FcstSetupKey =  @FcstSetupKey AND
			AIMFcstAccess.UserID = @UserID AND
		  	AIMFcstAccess.AccessCode = @AccessCode 

		IF @Return_Stat > 0 SET @HasAccess = 1		--True
		ELSE SET @HasAccess = 0	-- False
	END

	IF @@ERROR = 0 RETURN 1
	ELSE RETURN @@ERROR

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_ItemList_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_ItemList_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_ItemList_Sp
**	Desc: Inserts into the demand planning scratch table given item filters
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/23
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_ItemList_Sp 
(
	@FcstID as nvarchar(12),
	@VnID as nvarchar(12) = '',
	@Assort as nvarchar(12) = '',
	@LcID as nvarchar(12) = '',
	@Item as nvarchar(25) = '',
	@ItStat as nvarchar(1) = '',
	@Class1 as nvarchar(50) = '',
	@Class2 as nvarchar(50) = '',
	@Class3 as nvarchar(50) = '',
	@Class4 as nvarchar(50) = '',
	@ByID as nvarchar(12) = '',
	@LStatus as nvarchar(1) = '',
	@LDivision as nvarchar(20) = '',
	@LRegion as nvarchar(20) = '',
	@LUserDefined as nvarchar(30) = '',
	@RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @Return_Stat as int

	IF @FcstID IS NOT NULL
	BEGIN
		SELECT 
			@VnID = CASE WHEN (AIMFcstSetup.VnID IS NULL) THEN @VnID
				WHEN RTRIM(AIMFcstSetup.VnID) = '' THEN @VnID
				ELSE AIMFcstSetup.VnID
				END, 
			@Assort =  CASE WHEN (AIMFcstSetup.Assort IS NULL) THEN @Assort
				WHEN RTRIM(AIMFcstSetup.Assort) = '' THEN @Assort
				ELSE AIMFcstSetup.Assort
				END, 
			@LcID =  CASE WHEN (AIMFcstSetup.LcID IS NULL) THEN @LcID
				WHEN RTRIM(AIMFcstSetup.LcID) = '' THEN @LcID
				ELSE AIMFcstSetup.LcID
				END, 
			@Item =  CASE WHEN (AIMFcstSetup.Item IS NULL) THEN @Item
				WHEN RTRIM(AIMFcstSetup.Item) = '' THEN @Item
				ELSE AIMFcstSetup.Item
				END, 
			@ItStat =  CASE WHEN (AIMFcstSetup.ItStat IS NULL) THEN @ItStat
				WHEN RTRIM(AIMFcstSetup.ItStat) = '' THEN @ItStat
				ELSE AIMFcstSetup.ItStat
				END, 
			@Class1 =  CASE WHEN (AIMFcstSetup.Class1 IS NULL) THEN @Class1
				WHEN RTRIM(AIMFcstSetup.Class1) = '' THEN @Class1
				ELSE AIMFcstSetup.Class1
				END, 
			@Class2 =  CASE WHEN (AIMFcstSetup.Class2 IS NULL) THEN @Class2
				WHEN RTRIM(AIMFcstSetup.Class2) = '' THEN @Class2
				ELSE AIMFcstSetup.Class2
				END, 
			@Class3 =  CASE WHEN (AIMFcstSetup.Class3 IS NULL) THEN @Class3
				WHEN RTRIM(AIMFcstSetup.Class3) = '' THEN @Class3
				ELSE AIMFcstSetup.Class3
				END, 
			@Class4 =  CASE WHEN (AIMFcstSetup.Class4 IS NULL) THEN @Class4
				WHEN RTRIM(AIMFcstSetup.Class4) = '' THEN @Class4
				ELSE AIMFcstSetup.Class4
				END, 
			@ByID =  CASE WHEN (AIMFcstSetup.ByID IS NULL) THEN @ByID
				WHEN RTRIM(AIMFcstSetup.ByID) = '' THEN @ByID
				ELSE AIMFcstSetup.ByID
				END, 
			@LStatus =  CASE WHEN (AIMFcstSetup.LStatus IS NULL) THEN @LStatus
				WHEN RTRIM(AIMFcstSetup.LStatus) = '' THEN @LStatus
				ELSE AIMFcstSetup.LStatus
				END, 
			@LDivision =  CASE WHEN (AIMFcstSetup.LDivision IS NULL) THEN @LDivision
				WHEN RTRIM(AIMFcstSetup.LDivision) = '' THEN @LDivision
				ELSE AIMFcstSetup.LDivision
				END, 
			@LRegion =  CASE WHEN (AIMFcstSetup.LRegion IS NULL) THEN @LRegion
				WHEN RTRIM(AIMFcstSetup.LRegion) = '' THEN @LRegion
				ELSE AIMFcstSetup.LRegion
				END, 
			@LUserDefined =  CASE WHEN (AIMFcstSetup.LUserDefined IS NULL) THEN @LUserDefined
				WHEN RTRIM(AIMFcstSetup.LUserDefined) = '' THEN @LUserDefined
				ELSE AIMFcstSetup.LUserDefined
				END
		FROM AIMFcstSetup
		WHERE AIMFcstSetup.FcstID = @FcstID
	END

	SELECT 
		Item.Item, Item.LcID, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
		Item.VelCode, Item.VnID, Item.Assort, Item.ByID, Item.MDC, Item.MDCFlag, 
		Item.SaID, Item.PmID, Item.Weight, Item.Cube, Item.Price, Item.Cost, Item.UOM, 
		Item.ConvFactor, Item.BuyingUOM, Item.Oh, Item.Oo, Item.ComStk, 
		Item.BkOrder, Item.BkComStk, Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
		Item.UserFcstExpDate, Item.MAE, Item.MSE, Item.TrkSignalFlag, Item.Trend, Item.FcstUpdCyc, Item.PlnTT, 
		Item.Accum_Lt, Item.ReviewTime, Item.OrderPt, Item.OrderQty, Item.SafetyStock, 
		Item.FcstRT, Item.FcstLT, Item.Fcst_Month, Item.Fcst_Quarter, Item.Fcst_Year, 
		Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, Item.IntSafetyStock, Item.IsIntermittent, 
		Item.Mean_NZ, Item.StdDev_NZ, Item.ReplenCost2, Item.SSAdj, Item.ZSStock, Item.LTVFact, 
		Item.Dser, Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02, Item.BkQty03, 
		Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, Item.BkQty06, 
		Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08, Item.BkQty09, 
		Item.BkCost09 , Item.BkQty10, Item.BkCost10, 
		Item.Class1,Item.Class2,Item.Class3,Item.Class4, 
		Item.BuyStrat, Item.UserMin, Item.UserMax, Item.DIFlag, 
		ItStatus.DmdUpd, 
		Item.OptionID,
		AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined,
		AIMVendors.VName, RevCycles.NextDateTime, RevCycles.RevFreq, RevCycles.RevInterval, 
		RevCycles.RevSunday, RevCycles.RevMonday, RevCycles.RevTuesday, 
		RevCycles.RevWednesday, RevCycles.RevThursday, RevCycles.RevFriday, 
		RevCycles.RevSaturday, RevCycles.WeekQualifier, 
		RevCycles.RevStartDate, RevCycles.RevEndDate, 
		RevCycles.InitBuyPct, RevCycles.InitRevDate, 
		RevCycles.ReviewTime
		, ApplySeasonsIndex = CASE  AIMMethods.ApplySeasonsIndex WHEN 'Y' THEN 1 ELSE 0 END
		, AIMMethods.ApplyTrend
	From Item 
	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
	INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID
 	INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID
		AND Item.Assort = AIMVendors.Assort
	INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
	INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodID 
	WHERE Item.VnID = 
		CASE WHEN (@VnID IS NULL) THEN Item.VnID
		WHEN RTRIM(@VnID) = '' THEN Item.VnID
		ELSE @VnID
		END
	AND Item.Assort = 
		CASE WHEN (@Assort IS NULL) THEN Item.Assort
		WHEN RTRIM(@Assort) = '' THEN Item.Assort
		ELSE @Assort
		END
	AND Item.LcID = 
		CASE WHEN (@LcID IS NULL) THEN Item.LcID
		WHEN RTRIM(@LcID) = '' THEN Item.LcID
		ELSE @LcID
		END
	AND Item.Item = 
		CASE WHEN (@Item IS NULL) THEN Item.Item
		WHEN RTRIM(@Item) = '' THEN Item.Item
		ELSE @Item
		END
	AND Item.ItStat = 
		CASE WHEN (@ItStat IS NULL) THEN Item.ItStat
		WHEN RTRIM(@ItStat) = '' THEN Item.ItStat
		ELSE @ItStat
		END
	AND Item.Class1 = 
		CASE WHEN (@Class1 IS NULL) THEN Item.Class1
		WHEN RTRIM(@Class1) = '' THEN Item.Class1
		ELSE @Class1
		END
	AND Item.Class2 = 
		CASE WHEN (@Class2 IS NULL) THEN Item.Class2
		WHEN RTRIM(@Class2) = '' THEN Item.Class2
		ELSE @Class2
		END
	AND Item.Class3 = 
		CASE WHEN (@Class3 IS NULL) THEN Item.Class3
		WHEN RTRIM(@Class3) = '' THEN Item.Class3
		ELSE @Class3
		END
	AND Item.Class4 = 
		CASE WHEN (@Class4 IS NULL) THEN Item.Class4
		WHEN RTRIM(@Class4) = '' THEN Item.Class4
		ELSE @Class4
		END
	AND Item.ByID = 
		CASE WHEN (@ByID IS NULL) THEN Item.ByID
		WHEN RTRIM(@ByID) = '' THEN Item.ByID
		ELSE @ByID
		END
	AND AIMLocations.LStatus = 
		CASE WHEN (@LStatus IS NULL) THEN AIMLocations.LStatus
		WHEN RTRIM(@LStatus) = '' THEN AIMLocations.LStatus
		ELSE @LStatus
		END
	AND AIMLocations.LDivision = 
		CASE WHEN (@LDivision IS NULL) THEN AIMLocations.LDivision
		WHEN RTRIM(@LDivision) = '' THEN AIMLocations.LDivision
		ELSE @LDivision
		END
	AND AIMLocations.LRegion = 
		CASE WHEN (@LRegion IS NULL) THEN AIMLocations.LRegion
		WHEN RTRIM(@LRegion) = '' THEN AIMLocations.LRegion
		ELSE @LRegion
		END
	AND AIMLocations.LUserDefined = 
		CASE WHEN (@LUserDefined IS NULL) THEN AIMLocations.LUserDefined
		WHEN RTRIM(@LUserDefined) = '' THEN AIMLocations.LUserDefined
		ELSE @LUserDefined
		END
	AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
	ORDER BY Item.Item, Item.LcID 
	
	SET @RowCounter = @@ROWCOUNT
	IF @RowCounter > 0 
	BEGIN
		RETURN 1
	END
	ELSE
	BEGIN
		RETURN -1
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_LocationAttribs_Sp  ]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_LocationAttribs_Sp  ]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_LocationAttribs_Sp  
**	Desc: Returns the location attributes for a given item's LcID
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/23
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_LocationAttribs_Sp   
(
	@DPIL_LcID as nvarchar(12),
	@DmdScalingFactor as decimal (4, 3) OUTPUT, 
	@ScalingEffUntil as datetime OUTPUT,
	@ReplenCost as decimal(11, 4) OUTPUT,
	@DemandSource as nvarchar(1) OUTPUT,
	@StkDate as smalldatetime OUTPUT
	--, @RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @Return_Stat as int

	SELECT
		@DmdScalingFactor = AIMLocations.DmdScalingFactor,
		@ScalingEffUntil = AIMLocations.ScalingEffUntil, 
		@ReplenCost = AIMLocations.ReplenCost, 
		@DemandSource = AIMLocations.DemandSource, 
		@StkDate = AIMLocations.StkDate
	FROM AIMLocations
	WHERE AIMLocations.LcID = @DPIL_LcID

	SET @Return_Stat = @@ROWCOUNT
	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_OptionAttribs_Sp ]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_OptionAttribs_Sp ]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_OptionAttribs_Sp 
**	Desc: Returns the AIMOptions attributes for a given item's OptionID
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/23
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_OptionAttribs_Sp  
(
	@DPIL_OptionID as nvarchar(6),
	@HiTrndL as decimal(3, 2) OUTPUT, 
	@Dft_TurnHigh as decimal(4, 1) OUTPUT, 
	@Dft_TurnLow as decimal(4, 1) OUTPUT, 
	@HiMadP as decimal(3, 2) OUTPUT, 
	@LoMadP as decimal(3, 2) OUTPUT, 
	@MadExK as decimal(2, 1) OUTPUT, 
	@DIMADP as decimal(2, 1) OUTPUT
	--, @RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @Return_Stat as int

	SELECT 
		@HiTrndL = COALESCE(AIMOptions.HiTrndL, 0),
		@Dft_TurnHigh = COALESCE(AIMOptions.Dft_TurnHigh, 0),
		@Dft_TurnLow = COALESCE(AIMOptions.Dft_TurnLow, 0),
		@HiMadP = COALESCE(AIMOptions.HiMadP, 0),
		@LoMadP = COALESCE(AIMOptions.LoMadP, 0),
		@MadExK = COALESCE(AIMOptions.MadExK, 0),
		@DIMADP = COALESCE(AIMOptions.DIMADP, 0)
	FROM AIMOptions	
	WHERE AIMOptions.OptionID = @DPIL_OptionID

	SET @Return_Stat = @@ROWCOUNT
	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_PromotionAttribs_Sp ]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_PromotionAttribs_Sp ]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_PromotionAttribs_Sp 
**	Desc: Returns the AIMPromotions attributes for a given item's PmID
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/23
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_PromotionAttribs_Sp  
(
	@DPIL_PmID as nvarchar(6),
	@PmStatus as bit OUTPUT,
	@PmStartDate as smalldatetime OUTPUT,
	@PmEndDate as smalldatetime OUTPUT,
	@PmAdj01 as decimal(5, 2) OUTPUT, @PmAdj02 as decimal(5, 2) OUTPUT,
	@PmAdj03 as decimal(5, 2) OUTPUT, @PmAdj04 as decimal(5, 2) OUTPUT,
	@PmAdj05 as decimal(5, 2) OUTPUT, @PmAdj06 as decimal(5, 2) OUTPUT,
	@PmAdj07 as decimal(5, 2) OUTPUT, @PmAdj08 as decimal(5, 2) OUTPUT,
	@PmAdj09 as decimal(5, 2) OUTPUT, @PmAdj10 as decimal(5, 2) OUTPUT,
	@PmAdj11 as decimal(5, 2) OUTPUT, @PmAdj12 as decimal(5, 2) OUTPUT,
	@PmAdj13 as decimal(5, 2) OUTPUT, @PmAdj14 as decimal(5, 2) OUTPUT,
	@PmAdj15 as decimal(5, 2) OUTPUT, @PmAdj16 as decimal(5, 2) OUTPUT,
	@PmAdj17 as decimal(5, 2) OUTPUT, @PmAdj18 as decimal(5, 2) OUTPUT,
	@PmAdj19 as decimal(5, 2) OUTPUT, @PmAdj20 as decimal(5, 2) OUTPUT,
	@PmAdj21 as decimal(5, 2) OUTPUT, @PmAdj22 as decimal(5, 2) OUTPUT,
	@PmAdj23 as decimal(5, 2) OUTPUT, @PmAdj24 as decimal(5, 2) OUTPUT,
	@PmAdj25 as decimal(5, 2) OUTPUT, @PmAdj26 as decimal(5, 2) OUTPUT,
	@PmAdj27 as decimal(5, 2) OUTPUT, @PmAdj28 as decimal(5, 2) OUTPUT,
	@PmAdj29 as decimal(5, 2) OUTPUT, @PmAdj30 as decimal(5, 2) OUTPUT,
	@PmAdj31 as decimal(5, 2) OUTPUT, @PmAdj32 as decimal(5, 2) OUTPUT,
	@PmAdj33 as decimal(5, 2) OUTPUT, @PmAdj34 as decimal(5, 2) OUTPUT,
	@PmAdj35 as decimal(5, 2) OUTPUT, @PmAdj36 as decimal(5, 2) OUTPUT,
	@PmAdj37 as decimal(5, 2) OUTPUT, @PmAdj38 as decimal(5, 2) OUTPUT,
	@PmAdj39 as decimal(5, 2) OUTPUT, @PmAdj40 as decimal(5, 2) OUTPUT,
	@PmAdj41 as decimal(5, 2) OUTPUT, @PmAdj42 as decimal(5, 2) OUTPUT,
	@PmAdj43 as decimal(5, 2) OUTPUT, @PmAdj44 as decimal(5, 2) OUTPUT,
	@PmAdj45 as decimal(5, 2) OUTPUT, @PmAdj46 as decimal(5, 2) OUTPUT,
	@PmAdj47 as decimal(5, 2) OUTPUT, @PmAdj48 as decimal(5, 2) OUTPUT,
	@PmAdj49 as decimal(5, 2) OUTPUT, @PmAdj50 as decimal(5, 2) OUTPUT,
	@PmAdj51 as decimal(5, 2) OUTPUT, @PmAdj52 as decimal(5, 2) OUTPUT
	--, @RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @Return_Stat as int

	SELECT
		@PmStatus = isnull(AIMPromotions.PmStatus, 0),
		@PmStartDate = isnull(AIMPromotions.PmStartDate, '01/01/1990'),
		@PmEndDate = isnull(AIMPromotions.PmEndDate, '01/01/1990'),
		@PmAdj01 = isnull(AIMPromotions.PmAdj01,1), @PmAdj02 = isnull(AIMPromotions.PmAdj02,1),
		@PmAdj03 = isnull(AIMPromotions.PmAdj03,1), @PmAdj04 = isnull(AIMPromotions.PmAdj04,1),
		@PmAdj05 = isnull(AIMPromotions.PmAdj05,1), @PmAdj06 = isnull(AIMPromotions.PmAdj06,1),
		@PmAdj07 = isnull(AIMPromotions.PmAdj07,1), @PmAdj08 = isnull(AIMPromotions.PmAdj08,1),
		@PmAdj09 = isnull(AIMPromotions.PmAdj09,1), @PmAdj10 = isnull(AIMPromotions.PmAdj10,1),
		@PmAdj11 = isnull(AIMPromotions.PmAdj11,1), @PmAdj12 = isnull(AIMPromotions.PmAdj12,1),
		@PmAdj13 = isnull(AIMPromotions.PmAdj13,1), @PmAdj14 = isnull(AIMPromotions.PmAdj14,1),
		@PmAdj15 = isnull(AIMPromotions.PmAdj15,1), @PmAdj16 = isnull(AIMPromotions.PmAdj16,1),
		@PmAdj17 = isnull(AIMPromotions.PmAdj17,1), @PmAdj18 = isnull(AIMPromotions.PmAdj18,1),
		@PmAdj19 = isnull(AIMPromotions.PmAdj19,1), @PmAdj20 = isnull(AIMPromotions.PmAdj20,1),
		@PmAdj21 = isnull(AIMPromotions.PmAdj21,1), @PmAdj22 = isnull(AIMPromotions.PmAdj22,1),
		@PmAdj23 = isnull(AIMPromotions.PmAdj23,1), @PmAdj24 = isnull(AIMPromotions.PmAdj24,1),
		@PmAdj25 = isnull(AIMPromotions.PmAdj25,1), @PmAdj26 = isnull(AIMPromotions.PmAdj26,1),
		@PmAdj27 = isnull(AIMPromotions.PmAdj27,1), @PmAdj28 = isnull(AIMPromotions.PmAdj28,1),
		@PmAdj29 = isnull(AIMPromotions.PmAdj29,1), @PmAdj30 = isnull(AIMPromotions.PmAdj30,1),
		@PmAdj31 = isnull(AIMPromotions.PmAdj31,1), @PmAdj32 = isnull(AIMPromotions.PmAdj32,1),
		@PmAdj33 = isnull(AIMPromotions.PmAdj33,1), @PmAdj34 = isnull(AIMPromotions.PmAdj34,1),
		@PmAdj35 = isnull(AIMPromotions.PmAdj35,1), @PmAdj36 = isnull(AIMPromotions.PmAdj36,1),
		@PmAdj37 = isnull(AIMPromotions.PmAdj37,1), @PmAdj38 = isnull(AIMPromotions.PmAdj38,1),
		@PmAdj39 = isnull(AIMPromotions.PmAdj39,1), @PmAdj40 = isnull(AIMPromotions.PmAdj40,1),
		@PmAdj41 = isnull(AIMPromotions.PmAdj41,1), @PmAdj42 = isnull(AIMPromotions.PmAdj42,1),
		@PmAdj43 = isnull(AIMPromotions.PmAdj43,1), @PmAdj44 = isnull(AIMPromotions.PmAdj44,1),
		@PmAdj45 = isnull(AIMPromotions.PmAdj45,1), @PmAdj46 = isnull(AIMPromotions.PmAdj46,1),
		@PmAdj47 = isnull(AIMPromotions.PmAdj47,1), @PmAdj48 = isnull(AIMPromotions.PmAdj48,1),
		@PmAdj49 = isnull(AIMPromotions.PmAdj49,1), @PmAdj50 = isnull(AIMPromotions.PmAdj50,1),
		@PmAdj51 = isnull(AIMPromotions.PmAdj51,1), @PmAdj52 = isnull(AIMPromotions.PmAdj52,1)
	FROM AIMPromotions 
	WHERE AIMPromotions.PmID = @DPIL_PmID

	SET @Return_Stat = @@ROWCOUNT
	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_SeasonsAttribs_Sp ]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_SeasonsAttribs_Sp ]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_SeasonsAttribs_Sp 
**	Desc: Returns the AIMSeasons attributes for a given item's SaID
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/23
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_SeasonsAttribs_Sp  
(
	@DPIL_SaID as nvarchar(20),
	@BI01 as decimal(5, 3) OUTPUT, @BI02 as decimal(5, 3) OUTPUT, @BI03 as decimal(5, 3) OUTPUT,
	@BI04 as decimal(5, 3) OUTPUT, @BI05 as decimal(5, 3) OUTPUT, @BI06 as decimal(5, 3) OUTPUT,
	@BI07 as decimal(5, 3) OUTPUT, @BI08 as decimal(5, 3) OUTPUT, @BI09 as decimal(5, 3) OUTPUT,
	@BI10 as decimal(5, 3) OUTPUT, @BI11 as decimal(5, 3) OUTPUT, @BI12 as decimal(5, 3) OUTPUT,
	@BI13 as decimal(5, 3) OUTPUT, @BI14 as decimal(5, 3) OUTPUT, @BI15 as decimal(5, 3) OUTPUT,
	@BI16 as decimal(5, 3) OUTPUT, @BI17 as decimal(5, 3) OUTPUT, @BI18 as decimal(5, 3) OUTPUT,
	@BI19 as decimal(5, 3) OUTPUT, @BI20 as decimal(5, 3) OUTPUT, @BI21 as decimal(5, 3) OUTPUT,
	@BI22 as decimal(5, 3) OUTPUT, @BI23 as decimal(5, 3) OUTPUT, @BI24 as decimal(5, 3) OUTPUT,
	@BI25 as decimal(5, 3) OUTPUT, @BI26 as decimal(5, 3) OUTPUT, @BI27 as decimal(5, 3) OUTPUT,
	@BI28 as decimal(5, 3) OUTPUT, @BI29 as decimal(5, 3) OUTPUT, @BI30 as decimal(5, 3) OUTPUT,
	@BI31 as decimal(5, 3) OUTPUT, @BI32 as decimal(5, 3) OUTPUT, @BI33 as decimal(5, 3) OUTPUT,
	@BI34 as decimal(5, 3) OUTPUT, @BI35 as decimal(5, 3) OUTPUT, @BI36 as decimal(5, 3) OUTPUT,
	@BI37 as decimal(5, 3) OUTPUT, @BI38 as decimal(5, 3) OUTPUT, @BI39 as decimal(5, 3) OUTPUT,
	@BI40 as decimal(5, 3) OUTPUT, @BI41 as decimal(5, 3) OUTPUT, @BI42 as decimal(5, 3) OUTPUT,
	@BI43 as decimal(5, 3) OUTPUT, @BI44 as decimal(5, 3) OUTPUT, @BI45 as decimal(5, 3) OUTPUT,
	@BI46 as decimal(5, 3) OUTPUT, @BI47 as decimal(5, 3) OUTPUT, @BI48 as decimal(5, 3) OUTPUT,
	@BI49 as decimal(5, 3) OUTPUT, @BI50 as decimal(5, 3) OUTPUT, @BI51 as decimal(5, 3) OUTPUT,
	@BI52 as decimal(5, 3) OUTPUT
	--, @RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @Return_Stat as int

	SELECT
		@BI01 = AIMSeasons.BI01, @BI02 = AIMSeasons.BI02, @BI03 = AIMSeasons.BI03,
		@BI04 = AIMSeasons.BI04, @BI05 = AIMSeasons.BI05, @BI06 = AIMSeasons.BI06,
		@BI07 = AIMSeasons.BI07, @BI08 = AIMSeasons.BI08, @BI09 = AIMSeasons.BI09,
		@BI10 = AIMSeasons.BI10, @BI11 = AIMSeasons.BI11, @BI12 = AIMSeasons.BI12,
		@BI13 = AIMSeasons.BI13, @BI14 = AIMSeasons.BI14, @BI15 = AIMSeasons.BI15,
		@BI16 = AIMSeasons.BI16, @BI17 = AIMSeasons.BI17, @BI18 = AIMSeasons.BI18,
		@BI19 = AIMSeasons.BI19, @BI20 = AIMSeasons.BI20, @BI21 = AIMSeasons.BI21,
		@BI22 = AIMSeasons.BI22, @BI23 = AIMSeasons.BI23, @BI24 = AIMSeasons.BI24,
		@BI25 = AIMSeasons.BI25, @BI26 = AIMSeasons.BI26, @BI27 = AIMSeasons.BI27,
		@BI28 = AIMSeasons.BI28, @BI29 = AIMSeasons.BI29, @BI30 = AIMSeasons.BI30,
		@BI31 = AIMSeasons.BI31, @BI32 = AIMSeasons.BI32, @BI33 = AIMSeasons.BI33,
		@BI34 = AIMSeasons.BI34, @BI35 = AIMSeasons.BI35, @BI36 = AIMSeasons.BI36,
		@BI37 = AIMSeasons.BI37, @BI38 = AIMSeasons.BI38, @BI39 = AIMSeasons.BI39,
		@BI40 = AIMSeasons.BI40, @BI41 = AIMSeasons.BI41, @BI42 = AIMSeasons.BI42,
		@BI43 = AIMSeasons.BI43, @BI44 = AIMSeasons.BI44, @BI45 = AIMSeasons.BI45,
		@BI46 = AIMSeasons.BI46, @BI47 = AIMSeasons.BI47, @BI48 = AIMSeasons.BI48,
		@BI49 = AIMSeasons.BI49, @BI50 = AIMSeasons.BI50, @BI51 = AIMSeasons.BI51,
		@BI52 = AIMSeasons.BI52
	FROM AIMSeasons 
	INNER JOIN SysCtrl ON AIMSeasons.SAVersion = SysCtrl.CurSAVersion
	WHERE AIMSeasons.SaID = @DPIL_SaID

	SET @Return_Stat = @@ROWCOUNT
	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END


END
GO
SET QUOTED_IDENTIFIER OFF 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_ItemPricing_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_ItemPricing_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_ItemPricing_Sp
**	Desc: Gets the Cost, Price, and List Price for an Item
**	Derived from AIM_CostPriceListPrice_Get_SP.sql
**
**	Returns: 1)  0 - Successful
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Cost, Price, List Price
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/31
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:      Author:      Description:
**  ---------- ------------ -----------------------------------------------
**  08/16/2002 Wade Riza    Updated RETURNCodes and Error Handling
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_ItemPricing_Sp
(
  	@LcID as nvarchar(12), 
  	@Item as nvarchar(25),
	@FiscalYear as int OUTPUT,
  	@FY_Cost as decimal(10, 4) OUTPUT,
  	@FY_Price as decimal(10, 4) OUTPUT,
  	@FY_ListPrice as decimal(10, 4) OUTPUT,
  	@PFY_Cost as decimal(10, 4) OUTPUT,
  	@PFY_Price as decimal(10, 4) OUTPUT,
  	@PFY_ListPrice as decimal(10, 4) OUTPUT,
  	@NFY_Cost as decimal(10, 4) OUTPUT,
  	@NFY_Price as decimal(10, 4) OUTPUT,
  	@NFY_ListPrice as decimal(10, 4) OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
    
	SET NOCOUNT ON
	DECLARE @AvgCost as decimal(10, 4)
	DECLARE @AvgPrice as decimal(10, 4)
	DECLARE @AvgListPrice as decimal(10, 4)
	DECLARE @Cost as decimal(10, 4)
	DECLARE @Price as decimal(10, 4)
	DECLARE @ListPrice as decimal(10, 4)
	DECLARE @FYStartDate as datetime
	DECLARE @FYEndDate as datetime
	DECLARE @TargetDate as datetime
	DECLARE @NextFiscalYear as int
	DECLARE @Return_Stat as int	

	-- Validate the required parameters.
	IF @LcID IS NULL
	OR @Item IS NULL
	BEGIN
		RETURN-1
	END
	
	-- Starting from the previous year, get pricing data for previous, current and next fiscal years
	SET @TargetDate = DATEADD( yyyy, -1, GETDATE() )
	SET @NextFiscalYear = YEAR( DATEADD( yyyy, 1, GETDATE() ) )	

	WHILE YEAR( @TargetDate ) <= @NextFiscalYear
	BEGIN
		-- Determine fiscal year boundaries from AIM's calendar
		SELECT 
			@FYStartDate = AIMYears.FYStartDate,
			@FYEndDate = AIMYears.FYEndDate
		FROM AIMYears
		WHERE AIMYears.FYStartDate <= @TargetDate AND
			AIMYears.FYEndDate >= @TargetDate
		IF @@ROWCOUNT > 0 
		BEGIN
			-- Get pricing
			SELECT 
				@AvgCost = AVG(Cost), 
				@AvgPrice = AVG(Price), 
				@AvgListPrice = AVG(ListPrice)
			FROM ItemPricing 
			WHERE EffectiveDatetime > @FYStartDate AND
				EffectiveDatetime <= @FYEndDate AND
				LcID = @LcID AND
				Item = @Item
			SET @Return_Stat = @@ROWCOUNT
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
				RETURN-2  -- ERROR (Undetermined SQL ERROR, Add code in future to RETURNthe SQL ERROR)
			END
			-- Check for nulls
			SELECT 
				@AvgCost = CASE WHEN (@AvgCost IS NULL) THEN 0
					ELSE @AvgCost
					END, 
				@AvgPrice = CASE WHEN (@AvgPrice IS NULL) THEN 0
					ELSE @AvgPrice
					END, 
				@AvgListPrice = CASE WHEN (@AvgListPrice IS NULL) THEN 0
					ELSE @AvgListPrice
					END

			-- Double-check a zero average with the last set of values before given fiscal year
			SELECT TOP 1
				@Cost = CASE WHEN @AvgCost = 0 AND (ItemPricing.Cost IS NOT NULL AND ItemPricing.Cost <> 0) THEN ItemPricing.Cost 
						WHEN @AvgCost <> 0 AND (ItemPricing.Cost IS NOT NULL AND ItemPricing.Cost <> 0) THEN ( ItemPricing.Cost + @AvgCost ) / 2
						WHEN @AvgCost <> 0 AND (ItemPricing.Cost IS NULL OR ItemPricing.Cost = 0) THEN @AvgCost
						END
				, @Price = CASE WHEN @AvgPrice = 0 AND (ItemPricing.Price IS NOT NULL AND ItemPricing.Price <> 0) THEN ItemPricing.Price 
						WHEN @AvgPrice <> 0 AND (ItemPricing.Price IS NOT NULL AND ItemPricing.Price <> 0) THEN ( ItemPricing.Price + @AvgPrice ) / 2
						WHEN @AvgPrice <> 0 AND (ItemPricing.Price IS NULL AND ItemPricing.Price = 0) THEN @AvgPrice
						END
				, @ListPrice = CASE WHEN @AvgListPrice = 0 AND (ItemPricing.ListPrice IS NOT NULL AND ItemPricing.ListPrice <> 0) THEN ItemPricing.ListPrice 
						WHEN @AvgListPrice <> 0 AND (ItemPricing.ListPrice IS NOT NULL AND ItemPricing.ListPrice <> 0) THEN ( ItemPricing.ListPrice + @AvgListPrice ) / 2
						WHEN @AvgListPrice <> 0 AND (ItemPricing.ListPrice IS NULL AND ItemPricing.ListPrice = 0) THEN @AvgListPrice
						END
			FROM ItemPricing 
			WHERE EffectiveDateTime <= @FYStartDate AND
				LcID = @LcID AND
				Item = @Item
			ORDER BY EffectiveDatetime DESC
			SET @Return_Stat = @@ROWCOUNT
			IF @Return_Stat <= 0
			BEGIN
				SET @Cost = 0
				SET @Price = 0
				SET @ListPrice = 0
			END
			
			-- Validate
			IF @Cost = 0
			Or @Price = 0
			Or @ListPrice = 0 
			BEGIN
				SELECT @Cost = CASE WHEN @Cost IS NOT NULL THEN @Cost
						WHEN (@Cost IS NULL) AND Item.Cost IS NOT NULL THEN Item.Cost
						WHEN @Cost IS NULL AND Item.Cost IS NULL THEN 0
						ELSE 0
						END, 
					@Price = CASE WHEN @Price IS NOT NULL THEN @Price
						WHEN (@Price IS NULL) AND Item.Price IS NOT NULL THEN Item.Price
						WHEN @Price IS NULL AND Item.Price IS NULL THEN 0
						ELSE 0
						END, 
					@ListPrice = CASE WHEN @ListPrice IS NOT NULL THEN @ListPrice
						WHEN (@ListPrice IS NULL) AND Item.ListPrice IS NOT NULL THEN Item.ListPrice
						WHEN @ListPrice IS NULL AND Item.ListPrice IS NULL THEN 0
						ELSE 0
						END
				FROM Item 
				WHERE Item.LcID = @LcID AND
					Item.Item = @Item
				SET @Return_Stat = @@ROWCOUNT
				-- Check for SQL Server errors.
				IF @@ERROR <> 0 
				BEGIN
					RETURN-2  -- ERROR (Undetermined SQL ERROR, Add code in future to RETURNthe SQL ERROR)
				END
				IF @Return_Stat <= 0
				BEGIN
					SET @Cost = 0
					SET @Price = 0
					SET @ListPrice = 0
				END			
			END
	
			IF YEAR( @TargetDate ) = YEAR( DATEADD( yyyy, -1, GETDATE() ) )	-- Previous Fiscal Year
			BEGIN
				SET @PFY_Cost = @Cost
				SET @PFY_Price = @Price
				SET @PFY_ListPrice = @ListPrice
			END
			ELSE IF YEAR( @TargetDate ) = YEAR( GETDATE() )		-- Current Fiscal Year
			BEGIN
				SET @FiscalYear = YEAR( @TargetDate )
				SET @FY_Cost = @Cost
				SET @FY_Price = @Price
				SET @FY_ListPrice = @ListPrice
			END
			ELSE IF YEAR( @TargetDate ) = YEAR( DATEADD( yyyy, 1, GETDATE() ) )		-- Next Fiscal Year
			BEGIN
				SELECT @NFY_Cost = @Cost, @NFY_Price = @Price, @NFY_ListPrice = @ListPrice
			END
		END
		-- Increment date by an year.
		SET @TargetDate = DATEADD( yyyy, 1, @TargetDate )
	END

	RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_HstDmnd_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_HstDmnd_Sp]
GO

/*------------------------------------------------------------------------------
-- Name: AIM_DmdPlan_HstDmnd_Sp
-- Desc: Updates the Item table with data from the override.
--
-- Returns: 1)@@recordcount - Can be Zero
--  2) 0 - Failure
--  
-- Values: 
--  
-- Auth: Randy Sadler
-- Date: 
-------------------------------------------------------------------------------
-- Change History
-------------------------------------------------------------------------------
-- Date: Author: Description:
-- ---------- ------------ -----------------------------------------------
-- 09/12/2003 Srinivas Modified code for SubsItem
------------------------------------------------------------------------------*/

CREATE PROCEDURE AIM_DmdPlan_HstDmnd_Sp
(
	@LcID as nvarchar(12),
	@Item as nvarchar(25),
	@PeriodStart as datetime,
	@PeriodEnd as datetime
)

-- WITH ENCRYPTION -- Production use must be encrypted 
AS -- Internal use only - Unencrypted. Switch with Production use clause before release
BEGIN

	DECLARE @Return_Stat int
	DECLARE @SumSales as decimal(9, 1)
	DECLARE @SumOrders as decimal(9, 1)

	SET NOCOUNT ON

	SELECT 
		HisYear, LcID, Item, 
		AIMDays.FYPeriod_Weeks,
		MIN(AIMDays.FYDate) AS FYPeriodStartDate,
		MAX(AIMDays.FYDate) AS FYPeriodEndDate,
		PeriodSales = CASE AIMDays.FYPeriod_Weeks 
			WHEN 1 THEN CPS01 WHEN 2 THEN CPS02
			WHEN 3 THEN CPS03 WHEN 4 THEN CPS04
			WHEN 5 THEN CPS05 WHEN 6 THEN CPS06
			WHEN 7 THEN CPS07 WHEN 8 THEN CPS08
			WHEN 9 THEN CPS09 WHEN 10 THEN CPS10
			WHEN 11 THEN CPS11 WHEN 12 THEN CPS12
			WHEN 13 THEN CPS13 WHEN 14 THEN CPS14
			WHEN 15 THEN CPS15 WHEN 16 THEN CPS16
			WHEN 17 THEN CPS17 WHEN 18 THEN CPS18
			WHEN 19 THEN CPS19 WHEN 10 THEN CPS20
			WHEN 21 THEN CPS21 WHEN 22 THEN CPS22
			WHEN 23 THEN CPS23 WHEN 24 THEN CPS24
			WHEN 25 THEN CPS25 WHEN 26 THEN CPS26
			WHEN 27 THEN CPS27 WHEN 28 THEN CPS28
			WHEN 29 THEN CPS29 WHEN 30 THEN CPS30
			WHEN 31 THEN CPS31 WHEN 32 THEN CPS32
			WHEN 33 THEN CPS33 WHEN 34 THEN CPS34
			WHEN 35 THEN CPS35 WHEN 36 THEN CPS36
			WHEN 37 THEN CPS37 WHEN 38 THEN CPS38
			WHEN 39 THEN CPS39 WHEN 40 THEN CPS40
			WHEN 41 THEN CPS41 WHEN 42 THEN CPS42
			WHEN 43 THEN CPS43 WHEN 44 THEN CPS44
			WHEN 45 THEN CPS45 WHEN 46 THEN CPS46
			WHEN 47 THEN CPS47 WHEN 48 THEN CPS48
			WHEN 49 THEN CPS49 WHEN 50 THEN CPS50
			WHEN 51 THEN CPS51 WHEN 52 THEN CPS52
		END
		, PeriodOrders = CASE AIMDays.FYPeriod_Weeks
			WHEN 1 THEN QtyOrd01 WHEN 2 THEN QtyOrd02
			WHEN 3 THEN QtyOrd03 WHEN 4 THEN QtyOrd04
			WHEN 5 THEN QtyOrd05 WHEN 6 THEN QtyOrd06
			WHEN 7 THEN QtyOrd07 WHEN 8 THEN QtyOrd08
			WHEN 9 THEN QtyOrd09 WHEN 10 THEN QtyOrd10
			WHEN 11 THEN QtyOrd11 WHEN 12 THEN QtyOrd12
			WHEN 13 THEN QtyOrd13 WHEN 14 THEN QtyOrd14
			WHEN 15 THEN QtyOrd15 WHEN 16 THEN QtyOrd16
			WHEN 17 THEN QtyOrd17 WHEN 18 THEN QtyOrd18
			WHEN 19 THEN QtyOrd19 WHEN 10 THEN QtyOrd20
			WHEN 21 THEN QtyOrd21 WHEN 22 THEN QtyOrd22
			WHEN 23 THEN QtyOrd23 WHEN 24 THEN QtyOrd24
			WHEN 25 THEN QtyOrd25 WHEN 26 THEN QtyOrd26
			WHEN 27 THEN QtyOrd27 WHEN 28 THEN QtyOrd28
			WHEN 29 THEN QtyOrd29 WHEN 30 THEN QtyOrd30
			WHEN 31 THEN QtyOrd31 WHEN 32 THEN QtyOrd32
			WHEN 33 THEN QtyOrd33 WHEN 34 THEN QtyOrd34
			WHEN 35 THEN QtyOrd35 WHEN 36 THEN QtyOrd36
			WHEN 37 THEN QtyOrd37 WHEN 38 THEN QtyOrd38
			WHEN 39 THEN QtyOrd39 WHEN 40 THEN QtyOrd40
			WHEN 41 THEN QtyOrd41 WHEN 42 THEN QtyOrd42
			WHEN 43 THEN QtyOrd43 WHEN 44 THEN QtyOrd44
			WHEN 45 THEN QtyOrd45 WHEN 46 THEN QtyOrd46
			WHEN 47 THEN QtyOrd47 WHEN 48 THEN QtyOrd48
			WHEN 49 THEN QtyOrd49 WHEN 50 THEN QtyOrd50
			WHEN 51 THEN QtyOrd51 WHEN 52 THEN QtyOrd52
		END
	FROM ItemHistory
	INNER JOIN AIMYears ON ItemHistory.HisYear = AIMYears.FiscalYear
	INNER JOIN AIMDays ON 
		(AIMYears.FYStartDate <= AIMDays.FYDate AND AIMYears.FYEndDate >= AIMDays.FYDate )
	WHERE LcID = @LcID 
	AND Item = @Item
	AND SubsItem = @Item 
	AND (AIMDays.FYDate BETWEEN @PeriodStart AND @PeriodEnd )
	GROUP BY 
		HisYear, AIMDays.FYPeriod_Weeks, LcID, Item,
		CASE AIMDays.FYPeriod_Weeks 
			WHEN 1 THEN CPS01 WHEN 2 THEN CPS02
			WHEN 3 THEN CPS03 WHEN 4 THEN CPS04
			WHEN 5 THEN CPS05 WHEN 6 THEN CPS06
			WHEN 7 THEN CPS07 WHEN 8 THEN CPS08
			WHEN 9 THEN CPS09 WHEN 10 THEN CPS10
			WHEN 11 THEN CPS11 WHEN 12 THEN CPS12
			WHEN 13 THEN CPS13 WHEN 14 THEN CPS14
			WHEN 15 THEN CPS15 WHEN 16 THEN CPS16
			WHEN 17 THEN CPS17 WHEN 18 THEN CPS18
			WHEN 19 THEN CPS19 WHEN 10 THEN CPS20
			WHEN 21 THEN CPS21 WHEN 22 THEN CPS22
			WHEN 23 THEN CPS23 WHEN 24 THEN CPS24
			WHEN 25 THEN CPS25 WHEN 26 THEN CPS26
			WHEN 27 THEN CPS27 WHEN 28 THEN CPS28
			WHEN 29 THEN CPS29 WHEN 30 THEN CPS30
			WHEN 31 THEN CPS31 WHEN 32 THEN CPS32
			WHEN 33 THEN CPS33 WHEN 34 THEN CPS34
			WHEN 35 THEN CPS35 WHEN 36 THEN CPS36
			WHEN 37 THEN CPS37 WHEN 38 THEN CPS38
			WHEN 39 THEN CPS39 WHEN 40 THEN CPS40
			WHEN 41 THEN CPS41 WHEN 42 THEN CPS42
			WHEN 43 THEN CPS43 WHEN 44 THEN CPS44
			WHEN 45 THEN CPS45 WHEN 46 THEN CPS46
			WHEN 47 THEN CPS47 WHEN 48 THEN CPS48
			WHEN 49 THEN CPS49 WHEN 50 THEN CPS50
			WHEN 51 THEN CPS51 WHEN 52 THEN CPS52
		END
		, CASE AIMDays.FYPeriod_Weeks
			WHEN 1 THEN QtyOrd01 WHEN 2 THEN QtyOrd02
			WHEN 3 THEN QtyOrd03 WHEN 4 THEN QtyOrd04
			WHEN 5 THEN QtyOrd05 WHEN 6 THEN QtyOrd06
			WHEN 7 THEN QtyOrd07 WHEN 8 THEN QtyOrd08
			WHEN 9 THEN QtyOrd09 WHEN 10 THEN QtyOrd10
			WHEN 11 THEN QtyOrd11 WHEN 12 THEN QtyOrd12
			WHEN 13 THEN QtyOrd13 WHEN 14 THEN QtyOrd14
			WHEN 15 THEN QtyOrd15 WHEN 16 THEN QtyOrd16
			WHEN 17 THEN QtyOrd17 WHEN 18 THEN QtyOrd18
			WHEN 19 THEN QtyOrd19 WHEN 10 THEN QtyOrd20
			WHEN 21 THEN QtyOrd21 WHEN 22 THEN QtyOrd22
			WHEN 23 THEN QtyOrd23 WHEN 24 THEN QtyOrd24
			WHEN 25 THEN QtyOrd25 WHEN 26 THEN QtyOrd26
			WHEN 27 THEN QtyOrd27 WHEN 28 THEN QtyOrd28
			WHEN 29 THEN QtyOrd29 WHEN 30 THEN QtyOrd30
			WHEN 31 THEN QtyOrd31 WHEN 32 THEN QtyOrd32
			WHEN 33 THEN QtyOrd33 WHEN 34 THEN QtyOrd34
			WHEN 35 THEN QtyOrd35 WHEN 36 THEN QtyOrd36
			WHEN 37 THEN QtyOrd37 WHEN 38 THEN QtyOrd38
			WHEN 39 THEN QtyOrd39 WHEN 40 THEN QtyOrd40
			WHEN 41 THEN QtyOrd41 WHEN 42 THEN QtyOrd42
			WHEN 43 THEN QtyOrd43 WHEN 44 THEN QtyOrd44
			WHEN 45 THEN QtyOrd45 WHEN 46 THEN QtyOrd46
			WHEN 47 THEN QtyOrd47 WHEN 48 THEN QtyOrd48
			WHEN 49 THEN QtyOrd49 WHEN 50 THEN QtyOrd50
			WHEN 51 THEN QtyOrd51 WHEN 52 THEN QtyOrd52
		END
	ORDER BY 
		HisYear, AIMDays.FYPeriod_Weeks, LcID, Item,
		CASE AIMDays.FYPeriod_Weeks 
			WHEN 1 THEN CPS01 WHEN 2 THEN CPS02
			WHEN 3 THEN CPS03 WHEN 4 THEN CPS04
			WHEN 5 THEN CPS05 WHEN 6 THEN CPS06
			WHEN 7 THEN CPS07 WHEN 8 THEN CPS08
			WHEN 9 THEN CPS09 WHEN 10 THEN CPS10
			WHEN 11 THEN CPS11 WHEN 12 THEN CPS12
			WHEN 13 THEN CPS13 WHEN 14 THEN CPS14
			WHEN 15 THEN CPS15 WHEN 16 THEN CPS16
			WHEN 17 THEN CPS17 WHEN 18 THEN CPS18
			WHEN 19 THEN CPS19 WHEN 10 THEN CPS20
			WHEN 21 THEN CPS21 WHEN 22 THEN CPS22
			WHEN 23 THEN CPS23 WHEN 24 THEN CPS24
			WHEN 25 THEN CPS25 WHEN 26 THEN CPS26
			WHEN 27 THEN CPS27 WHEN 28 THEN CPS28
			WHEN 29 THEN CPS29 WHEN 30 THEN CPS30
			WHEN 31 THEN CPS31 WHEN 32 THEN CPS32
			WHEN 33 THEN CPS33 WHEN 34 THEN CPS34
			WHEN 35 THEN CPS35 WHEN 36 THEN CPS36
			WHEN 37 THEN CPS37 WHEN 38 THEN CPS38
			WHEN 39 THEN CPS39 WHEN 40 THEN CPS40
			WHEN 41 THEN CPS41 WHEN 42 THEN CPS42
			WHEN 43 THEN CPS43 WHEN 44 THEN CPS44
			WHEN 45 THEN CPS45 WHEN 46 THEN CPS46
			WHEN 47 THEN CPS47 WHEN 48 THEN CPS48
			WHEN 49 THEN CPS49 WHEN 50 THEN CPS50
			WHEN 51 THEN CPS51 WHEN 52 THEN CPS52
		END
		, CASE AIMDays.FYPeriod_Weeks
			WHEN 1 THEN QtyOrd01 WHEN 2 THEN QtyOrd02
			WHEN 3 THEN QtyOrd03 WHEN 4 THEN QtyOrd04
			WHEN 5 THEN QtyOrd05 WHEN 6 THEN QtyOrd06
			WHEN 7 THEN QtyOrd07 WHEN 8 THEN QtyOrd08
			WHEN 9 THEN QtyOrd09 WHEN 10 THEN QtyOrd10
			WHEN 11 THEN QtyOrd11 WHEN 12 THEN QtyOrd12
			WHEN 13 THEN QtyOrd13 WHEN 14 THEN QtyOrd14
			WHEN 15 THEN QtyOrd15 WHEN 16 THEN QtyOrd16
			WHEN 17 THEN QtyOrd17 WHEN 18 THEN QtyOrd18
			WHEN 19 THEN QtyOrd19 WHEN 10 THEN QtyOrd20
			WHEN 21 THEN QtyOrd21 WHEN 22 THEN QtyOrd22
			WHEN 23 THEN QtyOrd23 WHEN 24 THEN QtyOrd24
			WHEN 25 THEN QtyOrd25 WHEN 26 THEN QtyOrd26
			WHEN 27 THEN QtyOrd27 WHEN 28 THEN QtyOrd28
			WHEN 29 THEN QtyOrd29 WHEN 30 THEN QtyOrd30
			WHEN 31 THEN QtyOrd31 WHEN 32 THEN QtyOrd32
			WHEN 33 THEN QtyOrd33 WHEN 34 THEN QtyOrd34
			WHEN 35 THEN QtyOrd35 WHEN 36 THEN QtyOrd36
			WHEN 37 THEN QtyOrd37 WHEN 38 THEN QtyOrd38
			WHEN 39 THEN QtyOrd39 WHEN 40 THEN QtyOrd40
			WHEN 41 THEN QtyOrd41 WHEN 42 THEN QtyOrd42
			WHEN 43 THEN QtyOrd43 WHEN 44 THEN QtyOrd44
			WHEN 45 THEN QtyOrd45 WHEN 46 THEN QtyOrd46
			WHEN 47 THEN QtyOrd47 WHEN 48 THEN QtyOrd48
			WHEN 49 THEN QtyOrd49 WHEN 50 THEN QtyOrd50
			WHEN 51 THEN QtyOrd51 WHEN 52 THEN QtyOrd52
		END

	SELECT @Return_Stat = @@ROWCOUNT

	RETURN @Return_Stat

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_AIMCalendar_Calcs_SP]') AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_AIMCalendar_Calcs_SP]
GO

/*******************************************************************************
--	Name: AIM_AIMCalendar_Calcs_SP
--	Desc: Derived from AIMAdvCalendar.cls
--	This stored procedure is meant to retrieve misc. values from the AIMCalendar, 
--		depending upon the following parameters (and related dependencies):
--	@Action - determines the type of data to be returned. Valid values and meanings:
-- 	Depends on parameter(s): @TargetDate (Forecast Start Date, typically)
--		@Action = 1	=> Period 
--		@Action = 2	=> FiscalYear
--		@Action = 3 => StartDate[FromDate] 
--		@Action = 4 => EndDate[FromDate]
--		@Action = 5 => WorkingDaysInWeek
--
-- 	 Depends on parameter(s): @LowerDate; @UpperDate (Start and End dates for a period)
--		@Action = 11 => WorkingDays
--
-- 	 Depends on parameter(s): @TargetPeriod; @TargetFiscalYear (A given period within a given fiscal year)
--		@Action = 21 => StartDateFromPeriod  
--		@Action = 22 => EndDateFromPeriod
--
--	Returns: 1)  0 - Successful
--			2) -1 - No Data Found
--			3) -2 - SQL Error
--			4) -3 - Invalid parameters
--
--	Values:  RETURNDate and/OR RETURNInt
--
--	Auth:   Annalakshmi Stocksdale
--	Date:   2004/02/11
*******************************************************************************
--	Change History
*******************************************************************************
--	Date:      	Author:      		Description:
--	---------- 	------------ 		-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_AIMCalendar_Calcs_SP (
	@Action as tinyint
	, @FcstInterval as tinyint
	, @TargetDate as datetime
	, @LowerDate as datetime
	, @UpperDate as datetime
	, @TargetPeriod as int
	, @TargetFiscalYear as int
	, @RETURNDATE as datetime OUTPUT
	, @RETURNNUMBER as int OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @Period as int
	DECLARE @FiscalYear as int
-- 	DECLARE @FYStartDate as datetime
-- 	DECLARE @FYEndDate as datetime
-- 	DECLARE @NbrYears as int
-- 	DECLARE @NbrDays as int

	-- Validate inputs
	IF @Action = 1	-- => Period
	OR @Action = 2	-- => FiscalYear
	OR @Action = 3 -- => StartDate[FromDate] 
	OR @Action = 4 -- => EndDate[FromDate]
	OR @Action = 5 -- => WorkingDaysInWeek[Period]
	BEGIN
		IF @TargetDate IS NULL RETURN -3
	END
	ELSE IF @Action = 11 -- => WorkingDays
	BEGIN
		IF @LowerDate IS NULL 
		OR @UpperDate IS NULL
		BEGIN
			RETURN -3
		END
	END
	ELSE IF @Action = 21 -- => StartDateFromPeriod  
	OR @Action = 22 -- => EndDateFromPeriod
	BEGIN
		IF @TargetPeriod IS NULL 
		OR @TargetFiscalYear IS NULL
		BEGIN
			RETURN -3
		END
	END
	ELSE
	BEGIN
		RETURN -3	-- Invalid action code
	END
	
	-- Instantiate period to 0
	SET @Period = 0
	IF @TargetDate IS NOT NULL
	BEGIN
		-- Period	
-- 		SELECT
-- -- 			MIN(AIMDays.FYDate), 
-- -- 			MAX(AIMDays.FYDate),
-- 			@Period = CASE 
-- 				WHEN @FcstInterval = 0 THEN AIMDays.FYPeriod_Days
-- 				WHEN @FcstInterval = 1 THEN AIMDays.FYPeriod_Weeks
-- 				WHEN @FcstInterval = 2 THEN AIMDays.FYPeriod_Months
-- 				WHEN @FcstInterval = 3 THEN AIMDays.FYPeriod_Quarters
-- 				WHEN @FcstInterval = 4 THEN AIMDays.FYPeriod_544
-- 				WHEN @FcstInterval = 5 THEN AIMDays.FYPeriod_454
-- 				WHEN @FcstInterval = 6 THEN AIMDays.FYPeriod_445
-- 				WHEN @FcstInterval = 7 THEN AIMDays.FYPeriod_4Weeks
-- 			END 
-- 		FROM AIMDays 
-- 		INNER JOIN AIMYears ON (AIMDays.FYDate BETWEEN AIMYears.FYStartDate AND AIMYears.FYEndDate)
-- --		WHERE AIMDays.FYDate BETWEEN @TargetDate_Lower AND @TargetDate_Upper
-- 		WHERE AIMDays.FYDate = @TargetDate
-- 		GROUP BY 
-- 	 		AIMYears.FYStartDate
-- 			, CASE 
-- 				WHEN @FcstInterval = 0 THEN AIMDays.FYPeriod_Days
-- 				WHEN @FcstInterval = 1 THEN AIMDays.FYPeriod_Weeks
-- 				WHEN @FcstInterval = 2 THEN AIMDays.FYPeriod_Months
-- 				WHEN @FcstInterval = 3 THEN AIMDays.FYPeriod_Quarters
-- 				WHEN @FcstInterval = 4 THEN AIMDays.FYPeriod_544
-- 				WHEN @FcstInterval = 5 THEN AIMDays.FYPeriod_454
-- 				WHEN @FcstInterval = 6 THEN AIMDays.FYPeriod_445
-- 				WHEN @FcstInterval = 7 THEN AIMDays.FYPeriod_4Weeks
-- 			END 
-- 		ORDER BY 
-- 	 		AIMYears.FYStartDate

		SELECT
			@Period = AIMDays.FYPeriod_Weeks
		FROM AIMDays 
		INNER JOIN AIMYears ON (AIMDays.FYDate BETWEEN AIMYears.FYStartDate AND AIMYears.FYEndDate)
		WHERE AIMDays.FYDate = @TargetDate

		IF @Period = 0 
		BEGIN
			RETURN -1
		END
		ELSE
		BEGIN
			SET @Period = CASE WHEN @Period > 52 THEN 52 ELSE @Period END
		END
	END

	---- Using parameter(s): TargetDate----
	IF @Action = 1 
	BEGIN
		-- Period
		SET @RETURNNUMBER = @Period
	END 
	ELSE IF @Action = 2 
	BEGIN
		-- FiscalYear
		SELECT @RETURNNUMBER = AIMYears.FiscalYear
		FROM AIMYears 
		WHERE (AIMYears.FYStartDate <= ISNULL(@TargetDate, 0) AND
				AIMYears.FYEndDate >= ISNULL(@TargetDate, 0))
	END 
	ELSE IF @Action = 3 
	BEGIN
		-- StartDate[FromDate] 
		SELECT @RETURNDATE = DATEADD(wk, @Period, AIMYears.FYStartDate) - 7
		FROM AIMYears
		WHERE (AIMYears.FYStartDate <= ISNULL(@TargetDate, 0) AND
				AIMYears.FYEndDate >= ISNULL(@TargetDate, 0))
	END 
	ELSE IF @Action = 4 
	BEGIN
		-- EndDate[FromDate]
		SELECT @RETURNDATE = CASE 
			WHEN @Period = 1 THEN AIMYears.FYStartDate + 6
			WHEN @Period BETWEEN 2 AND 51 THEN 
				DATEADD( dd, (@Period * 7) - 1, AIMYears.FYStartDate )
			WHEN @Period >=52 THEN AIMYears.FYEndDate END
		FROM AIMYears
		WHERE (AIMYears.FYStartDate <= ISNULL(@TargetDate, 0) AND
				AIMYears.FYEndDate >= ISNULL(@TargetDate, 0))
	END 
	ELSE IF @Action = 5 
	BEGIN	
		-- WorkingDaysInWeek[Period]
		SELECT @RETURNNUMBER = COUNT(*)
		FROM AIMDays
		INNER JOIN AIMYears ON 
			(AIMDays.FYDate >= AIMYears.FYStartDate AND
			 AIMDays.FYDate <= AIMYears.FYEndDate)
		WHERE (AIMYears.FYStartDate <= ISNULL(@TargetDate, 0) AND
				 AIMYears.FYEndDate >= ISNULL(@TargetDate, 0)) AND
	 		AIMDays.DayStatus = 1 AND
			AIMDays.FYPeriod_Weeks = (SELECT ADSUB.FYPeriod_Weeks
									FROM AIMDays ADSUB 
									WHERE ADSUB.FYDate = ISNULL(@TargetDate, 0))
	END 
	ELSE  ---- Using parameter(s): Start/End or Lower/Upper dates----
	IF @Action = 11 	
	BEGIN	
		-- WorkingDays
		SELECT @RETURNNUMBER = 	COUNT(*)
		FROM AIMDays
		WHERE AIMDays.DayStatus = 1 AND
			AIMDays.FYDate BETWEEN ISNULL(@LowerDate, 0) AND ISNULL(@UpperDate, 0)
	END 
	ELSE ---- Using parameter(s): Period and FiscalYear----
	IF @Action = 21 	
	BEGIN
	    	-- StartDateFromPeriod  
		SELECT @RETURNDATE = DATEADD(wk, @TargetPeriod, AIMYears.FYStartDate) - 7
		FROM AIMYears
		WHERE AIMYears.FiscalYear = ISNULL(@TargetFiscalYear, 0)
	END 
	ELSE IF @Action = 22 
	BEGIN
		-- EndDateFromPeriod
		-- This is used to calculate Base Date, which is 
		-- the date following the last day of the last forecast update period
		SELECT @RETURNDATE = CASE 
			WHEN @TargetPeriod = 1 THEN AIMYears.FYStartDate + 6
			WHEN @TargetPeriod BETWEEN 2 AND 51 THEN 
				DATEADD( dd, (@TargetPeriod * 7) - 1, AIMYears.FYStartDate )
			WHEN @TargetPeriod >= 52 THEN AIMYears.FYEndDate END
		FROM AIMYears
		WHERE AIMYears.FiscalYear = ISNULL(@TargetFiscalYear, 0)
	END

	RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_AIMCalendar_BoundaryDates_Sp]') AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_AIMCalendar_BoundaryDates_Sp]
GO

/*******************************************************************************
--	Name: AIM_AIMCalendar_BoundaryDates_Sp
--	This stored procedure is meant to retrieve the start and end dates from the AIMCalendar, 
--		depending upon the following parameters (and related dependencies):
--
--	Returns: 1)  0 - Successful
--			2) -1 - No Data Found
--			3) -2 - SQL Error
--			4) -3 - Invalid parameters
--
--	Values:  RETURNDate and/OR RETURNInt
--
--	Auth:   Annalakshmi Stocksdale
--	Date:   2004/02/11
*******************************************************************************
--	Change History
*******************************************************************************
--	Date:      	Author:      		Description:
--	---------- 	------------ 		-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_AIMCalendar_BoundaryDates_Sp (
	@TargetDate as datetime
	, @PeriodInterval as tinyint
	, @PeriodStartDate as datetime OUTPUT
	, @PeriodEndDate as datetime OUTPUT
	, @PeriodIndex as int OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @FiscalYear as int
	SELECT
		@PeriodIndex = CASE 
			WHEN @PeriodInterval = 0 THEN AIMDays.FYPeriod_Days
			WHEN @PeriodInterval = 1 THEN AIMDays.FYPeriod_Weeks
			WHEN @PeriodInterval = 2 THEN AIMDays.FYPeriod_Months
			WHEN @PeriodInterval = 3 THEN AIMDays.FYPeriod_Quarters
			WHEN @PeriodInterval = 4 THEN AIMDays.FYPeriod_544
			WHEN @PeriodInterval = 5 THEN AIMDays.FYPeriod_454
			WHEN @PeriodInterval = 6 THEN AIMDays.FYPeriod_445
			WHEN @PeriodInterval = 7 THEN AIMDays.FYPeriod_4Weeks
		END
		, @FiscalYear = AIMYears.FiscalYear
	FROM AIMDays 
	INNER JOIN AIMYears ON (AIMDays.FYDate BETWEEN AIMYears.FYStartDate AND AIMYears.FYEndDate)
	WHERE CONVERT ( nvarchar, AIMDays.FYDate, 101 ) = CONVERT (nvarchar, @TargetDate, 101 )
	GROUP BY 
 		AIMYears.FiscalYear
		, CASE 
			WHEN @PeriodInterval = 0 THEN AIMDays.FYPeriod_Days
			WHEN @PeriodInterval = 1 THEN AIMDays.FYPeriod_Weeks
			WHEN @PeriodInterval = 2 THEN AIMDays.FYPeriod_Months
			WHEN @PeriodInterval = 3 THEN AIMDays.FYPeriod_Quarters
			WHEN @PeriodInterval = 4 THEN AIMDays.FYPeriod_544
			WHEN @PeriodInterval = 5 THEN AIMDays.FYPeriod_454
			WHEN @PeriodInterval = 6 THEN AIMDays.FYPeriod_445
			WHEN @PeriodInterval = 7 THEN AIMDays.FYPeriod_4Weeks
		END 
	ORDER BY 
 		AIMYears.FiscalYear

	SELECT 		
		@PeriodStartDate = MIN(AIMDays.FYDate), 
		@PeriodEndDate = MAX(AIMDays.FYDate)
	FROM AIMDays 
	INNER JOIN AIMYears ON (AIMDays.FYDate BETWEEN AIMYears.FYStartDate AND AIMYears.FYEndDate)
	WHERE 
		AIMDays.FYPeriod_Days = CASE WHEN @PeriodInterval = 0 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Days END
	AND	AIMDays.FYPeriod_Weeks = CASE WHEN @PeriodInterval = 1 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Weeks END
	AND	AIMDays.FYPeriod_Months = CASE WHEN @PeriodInterval = 2 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Months END
	AND	AIMDays.FYPeriod_Quarters = CASE WHEN @PeriodInterval = 3 THEN @PeriodIndex ELSE AIMDays.FYPeriod_Quarters END
	AND	AIMDays.FYPeriod_544 = CASE WHEN @PeriodInterval = 4 THEN @PeriodIndex ELSE AIMDays.FYPeriod_544 END
	AND	AIMDays.FYPeriod_454 = CASE WHEN @PeriodInterval = 5 THEN @PeriodIndex ELSE AIMDays.FYPeriod_454 END
	AND	AIMDays.FYPeriod_445 = CASE WHEN @PeriodInterval = 6 THEN @PeriodIndex ELSE AIMDays.FYPeriod_445 END
	AND	AIMDays.FYPeriod_4Weeks = CASE WHEN @PeriodInterval = 7 THEN @PeriodIndex ELSE AIMDays.FYPeriod_4Weeks END
	AND AIMYears.FiscalYear = @FiscalYear

	RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_CalcPartialPeriodWeights_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_CalcPartialPeriodWeights_Sp]
GO

/******************************************************************************
**	Name: AIM_CalcPartialPeriodWeights_Sp
**	Desc: Calculates percentage weights for partial periods
**		If not found, then inserts a new record
**
**	Returns: 
** 			1) 0 - Success
** 			2) -1 - Failure
** 			3) <-1 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/03/04
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_CalcPartialPeriodWeights_Sp
(
	@FcstInterval As int,
	@PeriodStart As datetime, 
	@PeriodEnd As datetime,
	@FirstStartDate As datetime OUTPUT, 
	@LastStartDate As datetime OUTPUT, 
    	@StartWeekPct As decimal(5, 2) OUTPUT, 
	@EndWeekPct As decimal(5, 2) OUTPUT,
	@DailyPct As decimal(5, 2)  OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @StartWeekDays As int
	DECLARE @EndWeekDays As int
	DECLARE @RETURNDATE datetime
	DECLARE @RETURNNUMBER int
	DECLARE @TempDate as datetime

	SET @RETURNDATE = NULL
    	SET @RETURNNUMBER = NULL
    	-- Calculate the First Date of the First Period
	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 3, @PeriodStart, NULL, NULL, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
	SET @FirstStartDate = @RETURNDATE
    	IF @FirstStartDate IS NULL RETURN -1
    
	SET @RETURNDATE = NULL
    	SET @RETURNNUMBER = NULL
    	-- Calculate the First Date of the Last Period
    	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 3, @PeriodEnd, NULL, NULL, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
	SET @LastStartDate = @RETURNDATE
	IF @LastStartDate IS NULL RETURN -1
    
	SET @RETURNDATE = NULL
    	SET @RETURNNUMBER = NULL
    	-- Calculate the number of working days in the first week
	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 5, @PeriodStart, NULL, NULL, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
	SET @StartWeekDays = @RETURNNUMBER
    
	--Calculate the number of working days in the last period
	SET @RETURNDATE = NULL
    	SET @RETURNNUMBER = NULL
    	-- Calculate the number of working days in the first week
	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 5, @PeriodEnd, NULL, NULL, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
	SET @EndWeekDays = @RETURNNUMBER

	-- Calc percents based on forecast interval
	SET @RETURNDATE = NULL
    	SET @RETURNNUMBER = NULL
	IF @FcstInterval = 0 	-- DAYS
	BEGIN
		-- Get number of working days
	    	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 11, NULL, @PeriodStart, @PeriodEnd, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
		IF @EndWeekDays > 0 SET @DailyPct = @RETURNNUMBER / @ENDWEEKDAYS
		ELSE SET @DailyPct = 0
	END
	ELSE
	BEGIN
		-- Calculate the Start Week Percentages
		IF @StartWeekDays > 0
		BEGIN
			-- If the lead time is less than 7 days then we need to calculate the
			-- Pcnt from the startdate to the enddate
			IF @PeriodEnd < (@FirstStartDate + 6) 
			BEGIN
				-- get number of working days
			    	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 11, NULL, @PeriodStart, @PeriodEnd, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
				SET @StartWeekPct = @RETURNNUMBER / @StartWeekDays
			END
			ELSE
			BEGIN
				-- get number of working days
				SET @TempDate = DATEADD(day, 6, @FirstStartDate)
			    	EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 11, NULL, @PeriodStart, @TempDate, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
				SET @StartWeekPct = @RETURNNUMBER / @StartWeekDays
			END
		END
		ELSE
		BEGIN
			SET @StartWeekPct = 0
		END

		--Calculate the End Week Percentages
		IF @EndWeekDays > 0
		BEGIN
			EXEC @Return_Stat = AIM_AIMCalendar_Calcs_Sp 11, NULL, @LastStartDate, @PeriodEnd, NULL, NULL, @RETURNDATE OUTPUT, @RETURNNUMBER OUTPUT
			SET @EndWeekPct = @RETURNNUMBER / @EndWeekDays
		END
		ELSE
		BEGIN
			SET @EndWeekPct = 0
		END
	END

	RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF
GO
SET ANSI_NULLS OFF
GO



SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstStore_Save_Sp]') AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstStore_Save_Sp]
GO
/*******************************************************************************
--	Name: AIM_DmdPlanFcstStore_Save_Sp
--	Desc: Derived from AIMAdvCalendar.cls
--
--	Returns: 1)  0 - Successful
--			2) -1 - No Data Found
--			3) -2 - SQL Error
--			4) -3 - Invalid parameters
--
--
--	Auth:   Annalakshmi Stocksdale
--	Date:   2004/02/11
*******************************************************************************
--	Change History
*******************************************************************************
--	Date:      	Author:      		Description:
--	---------- 	------------ 		-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstStore_Save_Sp (
	@UserID as nvarchar(12),
	@FcstSetupKey as int,
	@FcstStoreID as nvarchar(50),
	@UserElement as bit,
	@FcstStoreKey as int OUTPUT
	-- FcstComment is stored separately
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON
	DECLARE @Return_Stat as int
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	BEGIN
		BEGIN TRANSACTION
			SELECT @FcstStoreKey = AIMFcstStore.FcstStoreKey 
			FROM AIMFcstStore
			WHERE 	AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
				AIMFcstStore.FcstStoreID = @FcstStoreID AND
				AIMFcstStore.UserElement = @UserElement
			SET @Return_Stat = @@ROWCOUNT	
			IF @Return_Stat <= 0 
			BEGIN		
				INSERT INTO AIMFcstStore (
					FcstSetupKey, 
					FcstStoreID,
					UserElement
					-- FcstComment gets its own stored procedure for chunk-and-save processing from the UI
				) VALUES (
					@FcstSetupKey, 
					@FcstStoreID,
					@UserElement
				)			
				SET @FcstStoreKey = @@IDENTITY
				SET @Return_Stat = @@ROWCOUNT
			END
		COMMIT TRANSACTION
	END

	-- Return status to calling mod.
	IF @Return_Stat > 0 RETURN @Return_Stat
	ELSE RETURN -1

-- 	END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstStoreDetail_Save_Sp]') AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstStoreDetail_Save_Sp]
GO
/*******************************************************************************
--	Name: AIM_DmdPlanFcstStore_Save_Sp
--	Desc: Derived from AIMAdvCalendar.cls
--
--	Returns: 1)  0 - Successful
--			2) -1 - No Data Found
--			3) -2 - SQL Error
--			4) -3 - Invalid parameters
--
--
--	Auth:   Annalakshmi Stocksdale
--	Date:   2004/02/11
*******************************************************************************
--	Change History
*******************************************************************************
--	Date:      	Author:      		Description:
--	---------- 	------------ 		-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstStoreDetail_Save_Sp (
	-- Conditions
	@UserID as nvarchar(12),
	@FcstSetupKey as int,
	@FcstStoreKey as int,
	-- Fields
	@LcID as nvarchar(12),
	@Item as nvarchar(25),
	@PeriodStartDate as datetime,
	@DataCalc_Type as tinyint,
	@DataCalc_Value as decimal(10, 2),
	@PeriodEndDate as datetime, 
	@TargetPeriod as int
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON
	DECLARE @Return_Stat as int
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	BEGIN
		-- AIMFcstStoreDetail
		UPDATE AIMFcstStoreDetail SET
			DataCalc_Value = ISNULL( @DataCalc_Value, AIMFcstStoreDetail.DataCalc_Value ),
			PeriodEndDate = @PeriodEndDate,
			TargetPeriod = @TargetPeriod
		WHERE
			AIMFcstStoreDetail.FcstStoreKey = @FcstStoreKey AND
			AIMFcstStoreDetail.LcID = @LcID AND
			AIMFcstStoreDetail.Item = @Item AND
			AIMFcstStoreDetail.PeriodStartDate = @PeriodStartDate AND
			AIMFcstStoreDetail.DataCalc_Type = @DataCalc_Type
 
		SET @Return_Stat = @@ROWCOUNT
		IF @Return_Stat <= 0
		BEGIN
			INSERT INTO AIMFcstStoreDetail (
				FcstStoreKey, LcID, Item, 
				PeriodStartDate, 
				DataCalc_Type,
				DataCalc_Value,
				PeriodEndDate, 
				TargetPeriod
			) VALUES (
				@FcstStoreKey, @LcID, @Item, 
				@PeriodStartDate, 
				@DataCalc_Type,
				ISNULL( @DataCalc_Value, 0),
				@PeriodEndDate, 
				@TargetPeriod
			)				
			
			SET @Return_Stat = @@ROWCOUNT
		END
-- 	END

	-- Return status to calling mod.
	IF @Return_Stat > 0 RETURN @Return_Stat
	ELSE RETURN -1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstComment_Save_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstComment_Save_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstComment_Save_Sp
**	Desc: Updates AIMFcstSetup for the given FcstID, based on the FcstLocked.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstComment_Save_Sp
(
	-- Conditions
	@FcstStoreKey as int,
	-- Fields
    	@FcstComment as nvarchar(4000)
	-- Return parameters
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @AccessCode as int
	
	-- Validate the required parameters.
	IF @FcstStoreKey IS Null
	BEGIN
	  	RETURN -1
	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

	-- Update if not locked and has access
-- 	IF @AccessCode >= 2	-- correct this after enabling the checkuser sp
-- 	BEGIN
		UPDATE AIMFcstStore SET
			FcstComment = CAST(AIMFcstStore.FcstComment as nvarchar(4000)) + '\n' + RTRIM(@FcstComment)
		WHERE FcstStoreKey = @FcstStoreKey
	
		SET @Return_Stat= @@ROWCOUNT

-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END
	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlan_Adjustments_Fetch_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlan_Adjustments_Fetch_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlan_Adjustments_Fetch_Sp
**	Desc: Updates AIMFcstStoreAdjustLog for the given FcstID.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_Adjustments_Fetch_Sp
(
	@FcstStoreKey as int,
	@LcID as nvarchar(12) = NULL,
	@Item as nvarchar(25) = NULL,
	@AdjustStartDate as datetime = NULL,
	@AdjustEndDate as datetime = NULL
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat int
	DECLARE @AccessCode int	
	-- Validate the required parameters.
-- 	IF @UserID IS NULL
-- 	OR @FcstSetupKey IS NULL 
-- 	OR @LcID IS NULL
-- 	BEGIN
-- 	  	RETURN -1
-- 	END

	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	
	-- Update if not locked and has access
	-- IF @AccessCode >= 2
	-- BEGIN

-- -- -- 		SELECT 
-- -- -- 			STOREADJ.LcID, STOREADJ.Item
-- -- -- 			, STOREADJ.AdjustType
-- -- -- 			, STOREADJ.AdjustQty
-- -- -- 			, STOREADJ.AdjustUserID, STOREADJ.AdjustDesc, STOREADJ.AdjustDateTime
-- -- -- 			, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
-- -- -- 		FROM AIMFcstStoreAdjustLog STOREADJ
-- -- -- 		WHERE
-- -- -- 			STOREADJ.FcstStoreKey = @FcstStoreKey AND
-- -- -- 			STOREADJ.IsPromoted = 0 AND -- False
-- -- -- 			STOREADJ.LcID = CASE WHEN @LcID IS NOT NULL THEN @LcID ELSE STOREADJ.LcID END AND
-- -- -- 			STOREADJ.Item = CASE WHEN @Item IS NOT NULL THEN @Item ELSE STOREADJ.Item END AND
-- -- -- 			STOREADJ.AdjustStartDate <= CASE WHEN @AdjustStartDate IS NOT NULL THEN @AdjustStartDate  ELSE STOREADJ.AdjustStartDate END AND
-- -- -- 			STOREADJ.AdjustEndDate >= CASE WHEN @AdjustEndDate IS NOT NULL THEN @AdjustEndDate  ELSE STOREADJ.AdjustEndDate END 
-- -- -- 		ORDER BY
-- -- -- 			STOREADJ.LcID, 
-- -- -- 			STOREADJ.Item,
-- -- -- 			 STOREADJ.AdjustType
-- -- -- 
-- -- -- 		SELECT @Return_Stat = COUNT(MASTER.Item) FROM AIMFcstMaster MASTER
-- -- -- 		INNER JOIN AIMFcstStoreAdjustLog STOREADJ ON
-- -- -- 			MASTER.LcID = STOREADJ.LcID AND
-- -- -- 			MASTER.Item = STOREADJ.Item AND
-- -- -- 			MASTER.AdjustType  = STOREADJ.AdjustType AND
-- -- -- 			MASTER.PeriodStartDate >= STOREADJ.AdjustStartDate AND
-- -- -- 			MASTER.PeriodEndDate <= STOREADJ.AdjustEndDate
-- -- -- 		WHERE
-- -- -- 			STOREADJ.FcstStoreKey = @FcstStoreKey AND
-- -- -- 			STOREADJ.LcID = CASE WHEN @LcID IS NOT NULL THEN @LcID ELSE STOREADJ.LcID END AND
-- -- -- 			STOREADJ.Item = CASE WHEN @Item IS NOT NULL THEN @Item ELSE STOREADJ.Item END AND
-- -- -- 			STOREADJ.AdjustStartDate <= CASE WHEN @AdjustStartDate IS NOT NULL THEN @AdjustStartDate  ELSE STOREADJ.AdjustStartDate END AND
-- -- -- 			STOREADJ.AdjustEndDate >= CASE WHEN @AdjustEndDate IS NOT NULL THEN @AdjustEndDate  ELSE STOREADJ.AdjustEndDate END 
-- -- -- 		GROUP BY
-- -- -- 			MASTER.Item
-- -- -- 
-- -- -- 		IF @Return_Stat > 0
-- -- -- 		BEGIN
-- -- -- 			SELECT 
-- -- -- 				MASTER.LcID, MASTER.Item
-- -- -- 				, MASTER.AdjustType
-- -- -- 				, MASTER.AdjustQty
-- -- -- 				, 'MASTER', 'MASTER', MASTER.DateTimeEdit
-- -- -- 				, MASTER.PeriodStartDate, MASTER.PeriodEndDate
-- -- -- 			FROM AIMFcstMaster MASTER
-- -- -- 			INNER JOIN AIMFcstStoreAdjustLog STOREADJ ON
-- -- -- 				MASTER.LcID = STOREADJ.LcID AND
-- -- -- 				MASTER.Item = STOREADJ.Item AND
-- -- -- 				MASTER.AdjustType  = STOREADJ.AdjustType AND
-- -- -- 				MASTER.PeriodStartDate >= STOREADJ.AdjustStartDate AND
-- -- -- 				MASTER.PeriodEndDate <= STOREADJ.AdjustEndDate
-- -- -- 			WHERE
-- -- -- 				STOREADJ.FcstStoreKey = @FcstStoreKey AND
-- -- -- 				STOREADJ.LcID = CASE WHEN @LcID IS NOT NULL THEN @LcID ELSE STOREADJ.LcID END AND
-- -- -- 				STOREADJ.Item = CASE WHEN @Item IS NOT NULL THEN @Item ELSE STOREADJ.Item END AND
-- -- -- 				STOREADJ.AdjustStartDate <= CASE WHEN @AdjustStartDate IS NOT NULL THEN @AdjustStartDate  ELSE STOREADJ.AdjustStartDate END AND
-- -- -- 				STOREADJ.AdjustEndDate >= CASE WHEN @AdjustEndDate IS NOT NULL THEN @AdjustEndDate  ELSE STOREADJ.AdjustEndDate END 
-- -- -- 			GROUP BY
-- -- -- 				MASTER.LcID, MASTER.Item
-- -- -- 				, MASTER.AdjustType
-- -- -- 				, MASTER.AdjustQty
-- -- -- 				, MASTER.DateTimeEdit
-- -- -- 				, MASTER.PeriodStartDate, MASTER.PeriodEndDate
-- -- -- 			ORDER BY
-- -- -- 				MASTER.LcID, 
-- -- -- 				MASTER.Item,
-- -- -- 				 MASTER.AdjustType
-- -- -- 		END
-- -- -- 		ELSE
-- -- -- 		BEGIN
-- -- -- 			SELECT 
-- -- -- 				MASTER.LcID, MASTER.Item
-- -- -- 				, MASTER.AdjustType
-- -- -- 				, MASTER.AdjustQty
-- -- -- 				, 'MASTER', 'MASTER', MASTER.DateTimeEdit
-- -- -- 				, MASTER.PeriodStartDate, MASTER.PeriodEndDate
-- -- -- 			FROM AIMFcstMaster MASTER
-- -- -- 			WHERE
-- -- -- 				MASTER.LcID = CASE WHEN @LcID IS NOT NULL THEN @LcID ELSE MASTER.LcID END AND
-- -- -- 				MASTER.Item = CASE WHEN @Item IS NOT NULL THEN @Item ELSE MASTER.Item END AND
-- -- -- 				MASTER.PeriodStartDate >= CASE WHEN @AdjustStartDate IS NOT NULL THEN @AdjustStartDate ELSE MASTER.PeriodStartDate END AND
-- -- -- 				MASTER.PeriodEndDate <= CASE WHEN @AdjustEndDate IS NOT NULL THEN @AdjustEndDate ELSE MASTER.PeriodEndDate END 
-- -- -- 			GROUP BY
-- -- -- 				MASTER.LcID, MASTER.Item
-- -- -- 				, MASTER.AdjustType
-- -- -- 				, MASTER.AdjustQty
-- -- -- 				, MASTER.DateTimeEdit
-- -- -- 				, MASTER.PeriodStartDate, MASTER.PeriodEndDate	
-- -- -- 			ORDER BY
-- -- -- 				MASTER.LcID, 
-- -- -- 				MASTER.Item,
-- -- -- 				 MASTER.AdjustType
-- -- -- 
-- -- -- 		END

		SET @Return_Stat = @@ROWCOUNT
		-- Check for SQL Server errors.
		IF @@ERROR <> 0 
		BEGIN
		 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

	RETURN 1
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanAdjust_FetchGroupByItems_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanAdjust_FetchGroupByItems_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanAdjust_FetchGroupByItems_Sp
**	Desc: Updates AIMFcstStoreAdjustLog for the given FcstID.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanAdjust_FetchGroupByItems_Sp
(
	@FcstSetupKey as int,
	@FcstStoreKey as int,
	@GroupBy_FieldName as nvarchar(255),
	@GroupBy_FieldValue as nvarchar(255),
	@RecordCount as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @AccessCode as int
	

	SELECT  AIMFcstStoreDetail.LcID,
			AIMFcstStoreDetail.Item
	FROM AIMFcstStoreDetail
	INNER JOIN AIMFcstStore ON 
		AIMFcstStoreDetail.FcstStoreKey = AIMFcstStore.FcstStoreKey
	INNER JOIN ITEM ON
		AIMFcstStoreDetail.Item = Item.Item AND
		AIMFcstStoreDetail.LcID = Item.LcID
	INNER JOIN AIMLocations ON
		AIMFcstStoreDetail.LcID = AIMLocations.LcID
	WHERE
		AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
		AIMFcstStore.UserElement = 0 AND
		Item.VnID = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'VNID' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.VnID END
	AND Item.Assort = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'ASSORT' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.Assort END
	AND Item.LcID = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'LCID' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.LcID END
	AND Item.Item = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'ITEM' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.Item END
	AND Item.ItStat = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'ITSTAT' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.ItStat END
	AND Item.Class1 = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'CLASS1' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.Class1 END
	AND Item.Class2 = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'CLASS2' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.Class2 END
	AND Item.Class3 = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'CLASS3' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.Class3 END
	AND Item.Class4 = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'CLASS4' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.Class4 END
	AND Item.ByID = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'BYID' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.ByID END
	AND Item.VelCode = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'VELCODE' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE Item.VelCode END
	AND AIMLocations.LStatus = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'LSTATUS' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE AIMLocations.LStatus END
	AND AIMLocations.LDivision = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'LDIVISION' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE AIMLocations.LDivision END
	AND AIMLocations.LRegion = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'LREGION' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE AIMLocations.LRegion END
	AND AIMLocations.LUserDefined = 
		CASE WHEN UPPER(RTRIM(@GroupBy_FieldName)) = 'LUSERDEFINED' AND @GroupBy_FieldValue IS NOT NULL THEN RTRIM(@GroupBy_FieldValue)
		ELSE AIMLocations.LUserDefined END
	AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
	GROUP BY 
		CASE WHEN UPPER(@GroupBy_FieldName ) = 'ASSORT' THEN Item.Assort WHEN UPPER(@GroupBy_FieldName ) = 'ByID' THEN Item.ByID
			WHEN UPPER(@GroupBy_FieldName ) = 'CLASS1' THEN Item.Class1 WHEN UPPER(@GroupBy_FieldName ) = 'CLASS2' THEN Item.Class2
			WHEN UPPER(@GroupBy_FieldName ) = 'CLASS3' THEN Item.Class3 WHEN UPPER(@GroupBy_FieldName ) = 'CLASS4' THEN Item.Class4
			WHEN UPPER(@GroupBy_FieldName ) = 'ITEM' THEN Item.Item 
			WHEN UPPER(@GroupBy_FieldName ) = 'LcID' THEN Item.LcID
			WHEN UPPER(@GroupBy_FieldName ) = 'LDIVISION' THEN AIMLocations.LDivision WHEN UPPER(@GroupBy_FieldName ) = 'LREGION' THEN AIMLocations.LRegion
			WHEN UPPER(@GroupBy_FieldName ) = 'LSTATUS' THEN AIMLocations.LStatus WHEN UPPER(@GroupBy_FieldName ) = 'LUSERDEFINED' THEN AIMLocations.LUserDefined
			WHEN UPPER(@GroupBy_FieldName ) = 'VELCODE' THEN Item.VelCode WHEN UPPER(@GroupBy_FieldName ) = 'VnID' THEN Item.VnID
			END,
		AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
	ORDER BY
		CASE WHEN UPPER(@GroupBy_FieldName ) = 'ASSORT' THEN Item.Assort WHEN UPPER(@GroupBy_FieldName ) = 'ByID' THEN Item.ByID
			WHEN UPPER(@GroupBy_FieldName ) = 'CLASS1' THEN Item.Class1 WHEN UPPER(@GroupBy_FieldName ) = 'CLASS2' THEN Item.Class2
			WHEN UPPER(@GroupBy_FieldName ) = 'CLASS3' THEN Item.Class3 WHEN UPPER(@GroupBy_FieldName ) = 'CLASS4' THEN Item.Class4
			WHEN UPPER(@GroupBy_FieldName ) = 'ITEM' THEN Item.Item 
			WHEN UPPER(@GroupBy_FieldName ) = 'LcID' THEN Item.LcID
			WHEN UPPER(@GroupBy_FieldName ) = 'LDIVISION' THEN AIMLocations.LDivision WHEN UPPER(@GroupBy_FieldName ) = 'LREGION' THEN AIMLocations.LRegion
			WHEN UPPER(@GroupBy_FieldName ) = 'LSTATUS' THEN AIMLocations.LStatus WHEN UPPER(@GroupBy_FieldName ) = 'LUSERDEFINED' THEN AIMLocations.LUserDefined
			WHEN UPPER(@GroupBy_FieldName ) = 'VELCODE' THEN Item.VelCode WHEN UPPER(@GroupBy_FieldName ) = 'VnID' THEN Item.VnID
			END,
		AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item
		
	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	SET @RecordCount = @Return_Stat
	
	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanFcstStoreAdjustLog_Save_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanFcstStoreAdjustLog_Save_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
**	Desc: Updates AIMFcstStoreAdjustLog for the given FcstID.
**		If not found, then inserts a new record
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/01/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstStoreAdjustLog_Save_Sp
(
	@FcstSetupKey as int,
	@FcstStoreKey as int,
	@LcID as nvarchar(12),
	@Item as nvarchar(25),
	@AdjustType as tinyint,
	@AdjustQty as decimal(10, 2),
	@AdjustStartDate as datetime,
	@AdjustEndDate as datetime,
	@AdjustUserID as nvarchar(12),
	@AdjustDesc as nvarchar(255)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @AccessCode as int
	
	-- Validate the required parameters.
-- 	IF @UserID IS NULL
-- 	OR @FcstSetupKey IS NULL 
-- 	OR @StoreAdjustLogType IS NULL
-- 	OR @LcID IS NULL
-- 	BEGIN
-- 	  	RETURN -1
-- 	END

	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	
	-- Update if not locked and has access
	-- IF @AccessCode >= 2
	-- BEGIN			
			INSERT INTO AIMFcstStoreAdjustLog (
				FcstStoreKey, LcID, Item, IsPromoted
				, AdjustType, AdjustQty
				, AdjustStartDate , AdjustEndDate
				, AdjustDesc, AdjustUserID, AdjustDateTime
			) VALUES (
				@FcstStoreKey, @LcID, @Item, 0	-- A new record is obviously not promoted to master yet
				, @AdjustType, @AdjustQty
				, @AdjustStartDate, @AdjustEndDate
				, @AdjustDesc, @AdjustUserID, GetDate()
			)
	
			SET @Return_Stat = @@ROWCOUNT
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
	-- 	END
	-- 	ELSE
	-- 	BEGIN
	-- 	 	RETURN -3 -- Not Permitted Premissions
	-- 	END

	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlan_FcstDetail_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlan_FcstDetail_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlan_FcstDetail_Fetch_Sp
**	Desc: Returns Forecast repository record (Based on AIM_DmdPlanFcstSetup_GetKey_Sp)
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Failure
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_FcstDetail_Fetch_Sp 
(
    	@FcstSetupKey as int, 
	@UserID as nvarchar(12),
	@PeriodStartDate as datetime,
	@PeriodEndDate as datetime, 
	-- Item filter criteria
	@Item as nvarchar(25),
	@ItStat as nvarchar(1),
	@VnID as nvarchar(12),
	@Assort as nvarchar(12),
	@ByID as nvarchar(12),
	@Class1 as nvarchar(50),
	@Class2 as nvarchar(50),
	@Class3 as nvarchar(50),
	@Class4 as nvarchar(50),
	@LcID as nvarchar(12),
	@LStatus as nvarchar(1),
	@LDivision as nvarchar(20),
	@LRegion as nvarchar(20),
	@LUserDefined as nvarchar(30),
	-- Output
	@PeriodCount as int OUTPUT
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	
	DECLARE @Return_Stat as int
	DECLARE @AccessCode as int
	DECLARE @FcstStoreKey as int

	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	OR @UserID IS NULL
	BEGIN
		RETURN -1
	END
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	BEGIN
		SELECT -- HEADER
			AIMFcstStore.FcstStoreKey, AIMFcstStore.FcstStoreID, 
			AIMFcstStore.FcstComment,
			-- DETAIL
			AIMFcstStoreDetail.LcID, AIMFcstStoreDetail.Item,
			AIMFcstStoreDetail.PeriodStartDate, 
			AIMFcstStoreDetail.DataCalc_Type, 
			AIMFcstStoreDetail.DataCalc_Value, 
			AIMFcstStoreDetail.PeriodEndDate,
			AIMFcstStoreDetail.TargetPeriod
		FROM AIMFcstStore
		INNER JOIN AIMFcstStoreDetail ON 
			AIMFcstStore.FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
		INNER JOIN ITEM ON
			AIMFcstStoreDetail.Item = Item.Item AND
			AIMFcstStoreDetail.LcID = Item.LcID
		INNER JOIN AIMLocations ON
			AIMFcstStoreDetail.LcID = AIMLocations.LcID
		WHERE 
			AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
			AIMFcstStore.UserElement = 0 AND
			AIMFcstStoreDetail.PeriodStartDate >= CASE WHEN @PeriodStartDate IS NOT NULL THEN @PeriodStartDate  ELSE AIMFcstStoreDetail.PeriodStartDate END AND
			AIMFcstStoreDetail.PeriodEndDate <= CASE WHEN @PeriodEndDate IS NOT NULL THEN @PeriodEndDate  ELSE AIMFcstStoreDetail.PeriodEndDate END  AND
			Item.VnID = 
				CASE WHEN (@VnID IS NULL) THEN Item.VnID
				WHEN RTRIM(@VnID) = '' THEN Item.VnID
				ELSE @VnID
				END
			AND Item.Assort = 
				CASE WHEN (@Assort IS NULL) THEN Item.Assort
				WHEN RTRIM(@Assort) = '' THEN Item.Assort
				ELSE @Assort
				END
			AND Item.LcID = 
				CASE WHEN (@LcID IS NULL) THEN Item.LcID
				WHEN RTRIM(@LcID) = '' THEN Item.LcID
				ELSE @LcID
				END
			AND Item.Item = 
				CASE WHEN (@Item IS NULL) THEN Item.Item
				WHEN RTRIM(@Item) = '' THEN Item.Item
				ELSE @Item
				END
			AND Item.ItStat = 
				CASE WHEN (@ItStat IS NULL) THEN Item.ItStat
				WHEN RTRIM(@ItStat) = '' THEN Item.ItStat
				ELSE @ItStat
				END
			AND Item.Class1 = 
				CASE WHEN (@Class1 IS NULL) THEN Item.Class1
				WHEN RTRIM(@Class1) = '' THEN Item.Class1
				ELSE @Class1
				END
			AND Item.Class2 = 
				CASE WHEN (@Class2 IS NULL) THEN Item.Class2
				WHEN RTRIM(@Class2) = '' THEN Item.Class2
				ELSE @Class2
				END
			AND Item.Class3 = 
				CASE WHEN (@Class3 IS NULL) THEN Item.Class3
				WHEN RTRIM(@Class3) = '' THEN Item.Class3
				ELSE @Class3
				END
			AND Item.Class4 = 
				CASE WHEN (@Class4 IS NULL) THEN Item.Class4
				WHEN RTRIM(@Class4) = '' THEN Item.Class4
				ELSE @Class4
				END
			AND Item.ByID = 
				CASE WHEN (@ByID IS NULL) THEN Item.ByID
				WHEN RTRIM(@ByID) = '' THEN Item.ByID
				ELSE @ByID
				END
			AND AIMLocations.LStatus = 
				CASE WHEN (@LStatus IS NULL) THEN AIMLocations.LStatus
				WHEN RTRIM(@LStatus) = '' THEN AIMLocations.LStatus
				ELSE @LStatus
				END
			AND AIMLocations.LDivision = 
				CASE WHEN (@LDivision IS NULL) THEN AIMLocations.LDivision
				WHEN RTRIM(@LDivision) = '' THEN AIMLocations.LDivision
				ELSE @LDivision
				END
			AND AIMLocations.LRegion = 
				CASE WHEN (@LRegion IS NULL) THEN AIMLocations.LRegion
				WHEN RTRIM(@LRegion) = '' THEN AIMLocations.LRegion
				ELSE @LRegion
				END
			AND AIMLocations.LUserDefined = 
				CASE WHEN (@LUserDefined IS NULL) THEN AIMLocations.LUserDefined
				WHEN RTRIM(@LUserDefined) = '' THEN AIMLocations.LUserDefined
				ELSE @LUserDefined
				END
			AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
			ORDER BY 
				AIMFcstStoreDetail.Item, 
				AIMFcstStoreDetail.LcID,
				AIMFcstStoreDetail.PeriodStartDate, 
				AIMFcstStoreDetail.PeriodEndDate,
				AIMFcstStoreDetail.TargetPeriod


		SET @Return_Stat = @@ROWCOUNT
		IF @@ERROR <> 0
		BEGIN
			RETURN @@ERROR
		END
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

	IF @Return_Stat > 0
	BEGIN	
		SELECT 
			@PeriodCount = COUNT(AIMFcstStoreDetail.TargetPeriod)
			, @FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
		FROM AIMFcstStoreDetail
		INNER JOIN AIMFcstStore ON 
			AIMFcstStoreDetail.FcstStoreKey = AIMFcstStore.FcstStoreKey
		WHERE 
			AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
			AIMFcstStore.UserElement = 0	-- fetch the main repository record first.
			AND AIMFcstStoreDetail.PeriodStartDate >= CASE WHEN @PeriodStartDate IS NOT NULL THEN @PeriodStartDate  ELSE AIMFcstStoreDetail.PeriodStartDate END AND
			AIMFcstStoreDetail.PeriodEndDate <= CASE WHEN @PeriodEndDate IS NOT NULL THEN @PeriodEndDate  ELSE AIMFcstStoreDetail.PeriodEndDate END 
		GROUP BY AIMFcstStoreDetail.FcstStoreKey
			, AIMFcstStoreDetail.LcID
			, AIMFcstStoreDetail.Item

		-- This will return a compound recordset
		-- Fetch available adjustments
		EXEC @Return_Stat = AIM_DmdPlan_Adjustments_Fetch_Sp @FcstStoreKey, NULL, NULL, NULL, NULL
			
		-- Fetch user elements
--		EXEC @Return_Stat = AIM_DmdPlanFcstUserElement_Fetch_Sp @FcstSetupKey, @UserID, 0	-- we do not want details yet

		RETURN 1		-- SUCCEED	
	END
	ELSE
	BEGIN
		RETURN -1		-- FAIL
	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlan_FcstSummary_Fetch_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlan_FcstSummary_Fetch_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlan_FcstSummary_Fetch_Sp
**
**	Returns: 1)5 - Forecast Locked
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**			4) -3 Invalid parameters
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/02/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlan_FcstSummary_Fetch_Sp
(
	-- Conditions
	@FcstSetupKey as int,
	@UserID as nvarchar(12),
	@GroupBy_1 as nvarchar(255),
	@PeriodStartDate as datetime,
	@PeriodEndDate as datetime, 
	-- Item filter criteria
	@Item as nvarchar(25),
	@ItStat as nvarchar(1),
	@VnID as nvarchar(12),
	@Assort as nvarchar(12),
	@ByID as nvarchar(12),
	@Class1 as nvarchar(50),
	@Class2 as nvarchar(50),
	@Class3 as nvarchar(50),
	@Class4 as nvarchar(50),
	@LcID as nvarchar(12),
	@LStatus as nvarchar(1),
	@LDivision as nvarchar(20),
	@LRegion as nvarchar(20),
	@LUserDefined as nvarchar(30),
	-- Return parameters
	@PeriodCount as int OUTPUT

)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	-- Validate the required parameters.
	IF @GroupBy_1 IS Null
	BEGIN
	  	RETURN -1
 	END

-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
-- 	BEGIN
		SELECT AIMFcstStore.FcstStoreKey, 
			GroupBy_1 = CASE WHEN UPPER(@GroupBy_1 ) = 'ASSORT' THEN Item.Assort WHEN UPPER(@GroupBy_1 ) = 'ByID' THEN Item.ByID
				WHEN UPPER(@GroupBy_1 ) = 'CLASS1' THEN Item.Class1 WHEN UPPER(@GroupBy_1 ) = 'CLASS2' THEN Item.Class2
				WHEN UPPER(@GroupBy_1 ) = 'CLASS3' THEN Item.Class3 WHEN UPPER(@GroupBy_1 ) = 'CLASS4' THEN Item.Class4
				WHEN UPPER(@GroupBy_1 ) = 'ITEM' THEN Item.Item 
				WHEN UPPER(@GroupBy_1 ) = 'LcID' THEN Item.LcID
				WHEN UPPER(@GroupBy_1 ) = 'LDIVISION' THEN AIMLocations.LDivision WHEN UPPER(@GroupBy_1 ) = 'LREGION' THEN AIMLocations.LRegion
				WHEN UPPER(@GroupBy_1 ) = 'LSTATUS' THEN AIMLocations.LStatus WHEN UPPER(@GroupBy_1 ) = 'LUSERDEFINED' THEN AIMLocations.LUserDefined
				WHEN UPPER(@GroupBy_1 ) = 'VELCODE' THEN Item.VelCode WHEN UPPER(@GroupBy_1 ) = 'VnID' THEN Item.VnID
			ELSE AIMFcstStoreDetail.LcID END,
			PeriodStartDate, 
			DataCalc_Type, 
			SUM( DataCalc_Value ) as DataCalc_Value,
			PeriodEndDate,
			TargetPeriod
		FROM AIMFcstStoreDetail
		INNER JOIN AIMFcstStore ON 
			AIMFcstStoreDetail.FcstStoreKey = AIMFcstStore.FcstStoreKey
		INNER JOIN ITEM ON
			AIMFcstStoreDetail.Item = Item.Item AND
			AIMFcstStoreDetail.LcID = Item.LcID
		INNER JOIN AIMLocations ON
			AIMFcstStoreDetail.LcID = AIMLocations.LcID
		WHERE
			AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
			AIMFcstStore.UserElement = 0 AND
			AIMFcstStoreDetail.PeriodStartDate >= CASE WHEN @PeriodStartDate IS NOT NULL THEN @PeriodStartDate  ELSE AIMFcstStoreDetail.PeriodStartDate END AND
			AIMFcstStoreDetail.PeriodEndDate <= CASE WHEN @PeriodEndDate IS NOT NULL THEN @PeriodEndDate  ELSE AIMFcstStoreDetail.PeriodEndDate END  AND
			Item.VnID = 
			CASE WHEN (@VnID IS NULL) THEN Item.VnID
			WHEN RTRIM(@VnID) = '' THEN Item.VnID
			ELSE @VnID
			END
		AND Item.Assort = 
			CASE WHEN (@Assort IS NULL) THEN Item.Assort
			WHEN RTRIM(@Assort) = '' THEN Item.Assort
			ELSE @Assort
			END
		AND Item.LcID = 
			CASE WHEN (@LcID IS NULL) THEN Item.LcID
			WHEN RTRIM(@LcID) = '' THEN Item.LcID
			ELSE @LcID
			END
		AND Item.Item = 
			CASE WHEN (@Item IS NULL) THEN Item.Item
			WHEN RTRIM(@Item) = '' THEN Item.Item
			ELSE @Item
			END
		AND Item.ItStat = 
			CASE WHEN (@ItStat IS NULL) THEN Item.ItStat
			WHEN RTRIM(@ItStat) = '' THEN Item.ItStat
			ELSE @ItStat
			END
		AND Item.Class1 = 
			CASE WHEN (@Class1 IS NULL) THEN Item.Class1
			WHEN RTRIM(@Class1) = '' THEN Item.Class1
			ELSE @Class1
			END
		AND Item.Class2 = 
			CASE WHEN (@Class2 IS NULL) THEN Item.Class2
			WHEN RTRIM(@Class2) = '' THEN Item.Class2
			ELSE @Class2
			END
		AND Item.Class3 = 
			CASE WHEN (@Class3 IS NULL) THEN Item.Class3
			WHEN RTRIM(@Class3) = '' THEN Item.Class3
			ELSE @Class3
			END
		AND Item.Class4 = 
			CASE WHEN (@Class4 IS NULL) THEN Item.Class4
			WHEN RTRIM(@Class4) = '' THEN Item.Class4
			ELSE @Class4
			END
		AND Item.ByID = 
			CASE WHEN (@ByID IS NULL) THEN Item.ByID
			WHEN RTRIM(@ByID) = '' THEN Item.ByID
			ELSE @ByID
			END
		AND AIMLocations.LStatus = 
			CASE WHEN (@LStatus IS NULL) THEN AIMLocations.LStatus
			WHEN RTRIM(@LStatus) = '' THEN AIMLocations.LStatus
			ELSE @LStatus
			END
		AND AIMLocations.LDivision = 
			CASE WHEN (@LDivision IS NULL) THEN AIMLocations.LDivision
			WHEN RTRIM(@LDivision) = '' THEN AIMLocations.LDivision
			ELSE @LDivision
			END
		AND AIMLocations.LRegion = 
			CASE WHEN (@LRegion IS NULL) THEN AIMLocations.LRegion
			WHEN RTRIM(@LRegion) = '' THEN AIMLocations.LRegion
			ELSE @LRegion
			END
		AND AIMLocations.LUserDefined = 
			CASE WHEN (@LUserDefined IS NULL) THEN AIMLocations.LUserDefined
			WHEN RTRIM(@LUserDefined) = '' THEN AIMLocations.LUserDefined
			ELSE @LUserDefined
			END
		AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
		GROUP BY 
			AIMFcstStore.FcstStoreKey, 
			CASE WHEN UPPER(@GroupBy_1 ) = 'ASSORT' THEN Item.Assort WHEN UPPER(@GroupBy_1 ) = 'ByID' THEN Item.ByID
				WHEN UPPER(@GroupBy_1 ) = 'CLASS1' THEN Item.Class1 WHEN UPPER(@GroupBy_1 ) = 'CLASS2' THEN Item.Class2
				WHEN UPPER(@GroupBy_1 ) = 'CLASS3' THEN Item.Class3 WHEN UPPER(@GroupBy_1 ) = 'CLASS4' THEN Item.Class4
				WHEN UPPER(@GroupBy_1 ) = 'ITEM' THEN Item.Item 
				WHEN UPPER(@GroupBy_1 ) = 'LcID' THEN Item.LcID
				WHEN UPPER(@GroupBy_1 ) = 'LDIVISION' THEN AIMLocations.LDivision WHEN UPPER(@GroupBy_1 ) = 'LREGION' THEN AIMLocations.LRegion
				WHEN UPPER(@GroupBy_1 ) = 'LSTATUS' THEN AIMLocations.LStatus WHEN UPPER(@GroupBy_1 ) = 'LUSERDEFINED' THEN AIMLocations.LUserDefined
				WHEN UPPER(@GroupBy_1 ) = 'VELCODE' THEN Item.VelCode WHEN UPPER(@GroupBy_1 ) = 'VnID' THEN Item.VnID
			ELSE AIMFcstStoreDetail.LcID END,
			PeriodStartDate,
			DataCalc_Type,
			PeriodEndDate,
			TargetPeriod
	
		SET @Return_Stat= @@ROWCOUNT
--	END
	IF @Return_Stat > 0
	BEGIN	
		SELECT 
			@PeriodCount = COUNT(AIMFcstStoreDetail.TargetPeriod)
-- 			, @FcstStoreKey = AIMFcstStoreDetail.FcstStoreKey
		FROM AIMFcstStoreDetail
		INNER JOIN AIMFcstStore ON 
			AIMFcstStoreDetail.FcstStoreKey = AIMFcstStore.FcstStoreKey
		WHERE 
			AIMFcstStore.FcstSetupKey = @FcstSetupKey AND
			AIMFcstStore.UserElement = 0	-- fetch the main repository record first.
			AND AIMFcstStoreDetail.PeriodStartDate >= CASE WHEN @PeriodStartDate IS NOT NULL THEN @PeriodStartDate  ELSE AIMFcstStoreDetail.PeriodStartDate END AND
			AIMFcstStoreDetail.PeriodEndDate <= CASE WHEN @PeriodEndDate IS NOT NULL THEN @PeriodEndDate  ELSE AIMFcstStoreDetail.PeriodEndDate END 
		GROUP BY AIMFcstStoreDetail.FcstStoreKey
			, AIMFcstStoreDetail.LcID
			, AIMFcstStoreDetail.Item


-- 		-- This will return a compound recordset
-- 		-- Fetch available adjustments
-- 		EXEC @Return_Stat = AIM_DmdPlan_Adjustments_Fetch_Sp @FcstStoreKey, NULL, NULL, NULL, NULL
	END

	IF @Return_Stat > 0 
	BEGIN
		RETURN @Return_Stat
	END
	ELSE
	BEGIN
		RETURN -1
	END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_DmdPlanFcstMaster_Fetch_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_DmdPlanFcstMaster_Fetch_Sp]
GO

/*******************************************************************************
**	Name: AIM_DmdPlanFcstMaster_Fetch_Sp
**	Desc: Returns Forecast Key (Based on AIM_DmdPlanFcst_GetKey_Sp)
**
**	Parameters:
**		FcstID == a valid AIMFcst.Forecast ID 
**		Action == one of the following SQL Action Codes
**    		0 = Get Equal
**			1 == Get Greater Than
**			2 == Get Lesser Than
**			3 == Get Greater Than or Equal To
**			4 == Get Lesser  Than or Equal To
**			5 == Get First
**			6 == Get Last
**
**	Returns: 
**			 1 - Successful Insert
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/01
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanFcstMaster_Fetch_Sp 
(
	@LcID as nvarchar (12) OUTPUT,
	@Item as nvarchar (25) OUTPUT,
	@FiscalYear as int OUTPUT,
	@FiscalPeriod as int OUTPUT,
	@AdjustQty_Override decimal (10, 2),
	@AdjustQty_Units decimal (10, 2),
	@AdjustQty_Percent decimal (10, 2),
	@PeriodStartDate as datetime OUTPUT,
	@PeriodEndDate as datetime OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	
	SET NOCOUNT ON
	
	-- Validate the required parameters.
-- 	IF @FcstSetupKey IS NULL
-- 	BEGIN
-- 		RETURN -1
-- 	END
	
-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
-- 	IF @Return_Stat < 0 
-- 	BEGIN
-- 		SET @AccessCode = 0
-- 	END	

-- 	IF @AccessCode >= 2 
--	BEGIN
		SELECT 
			@PeriodStartDate = AIMFcstMaster.PeriodStartDate
			, @PeriodEndDate = AIMFcstMaster.PeriodEndDate
			, @AdjustQty_Override = CASE AIMFcstMaster.AdjustType 
				WHEN 0 THEN AIMFcstMaster.AdjustQty
				ELSE NULL END
			, @AdjustQty_Units = CASE AIMFcstMaster.AdjustType
				WHEN 1 THEN AIMFcstMaster.AdjustQty
				ELSE NULL END
			, @AdjustQty_Percent = CASE AIMFcstMaster.AdjustType
				WHEN 2 THEN AIMFcstMaster.AdjustQty
				ELSE NULL END
		FROM AIMFcstMaster
		WHERE 
			AIMFcstMaster.LcID = @LcID AND
			AIMFcstMaster.Item = @Item AND
			AIMFcstMaster.FiscalYear = @FiscalYear AND
			AIMFcstMaster.FiscalPeriod = @FiscalPeriod
		ORDER BY 
			LcID, Item,
			FiscalYear, FiscalPeriod, 
			PeriodStartDate, PeriodEndDate

		RETURN 1		-- SUCCEED	
-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
	WHERE ID = object_id(N'[dbo].[AIM_ItemAttributes_Fetch_Sp]') 
	AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_ItemAttributes_Fetch_Sp]
GO

/*******************************************************************************
-- 	Name: AIM_ItemAttributes_Fetch_Sp
-- 	Desc: Gets Item data based on the Selection Criteria selected
-- 	
-- 	Returns: 1)@Return_Stat - Can be Zero
-- 	2) -1 - No Data Found
-- 	3) -2 - SQL Error
-- 	
-- 	Auth: Wade Riza 
-- 	Date: 08/13/2002
*******************************************************************************
-- 	Change History
*******************************************************************************
-- 	Date: 		Author:			Description:
-- 	---------- 	------------		-----------------------------------------------
-- 	08/15/2002	Wade Riza		Update Return COdes and Error Handling
-- 	09/23/2002	Wade Riza		Removed Hard Coded value for SAVersion 
-- 	09/24/2002	Wade Riza		Updated Class Codes to NVARCHAR(50)
-- 	10/01/2002	Osama Riyahi	Updated Dynamic where to not use ItStatus.DmdUpd
-- 	2004/01/24	A.Stocksdale	1. Added new parameters from AIMLocations 
-- 									(LStatus, LDivision, LRegion, LUserDefined)
-- 	2004/01/24	A.Stocksdale	2. Removed dynamic where clause variables with a condition-based query.
*******************************************************************************/
CREATE PROCEDURE AIM_ItemAttributes_Fetch_Sp
(
	@Item as nvarchar(25),
	@LcID as nvarchar(12),
	@ItDesc as nvarchar(50) OUTPUT,
	@ItStat as nvarchar(1) OUTPUT,
	@Class1 as nvarchar(50) OUTPUT,
	@Class2 as nvarchar(50) OUTPUT,
	@Class3 as nvarchar(50) OUTPUT,
	@Class4 as nvarchar(50) OUTPUT,
	@VelCode as nvarchar(1) OUTPUT,
	@VnID as nvarchar(12) OUTPUT,
	@Assort as nvarchar(12) OUTPUT,
	@ByID as nvarchar(12) OUTPUT,
	@LStatus as nvarchar(1) OUTPUT,
	@LDivision as nvarchar(20) OUTPUT,
	@LRegion as nvarchar(20) OUTPUT,
	@LUserDefined as nvarchar(30) OUTPUT,
	@FcstDemand as decimal(10, 2) OUTPUT,
	@MAE as decimal(10, 2) OUTPUT,
	@Trend as decimal(10, 3) OUTPUT,
	@UOM as nvarchar(6) OUTPUT
)

-- 	WITH ENCRYPTION /* Production use must be encrypted */
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @Return_Stat as int
	
	SET NOCOUNT ON
	
	
	SELECT 
		@Item = Item.Item,
		@LcID = Item.LcID,
		@ItDesc = Item.ItDesc,
		@ItStat = Item.ItStat,
		@Class1 = Item.Class1,
		@Class2 = Item.Class2,
		@Class3 = Item.Class3,
		@Class4 = Item.Class4,
		@VelCode = Item.VelCode,
		@VnID = Item.VnID,
		@Assort = Item.Assort,
		@ByID = Item.ByID,
		@LStatus = AIMLocations.LStatus,
		@LDivision = AIMLocations.LDivision,
		@LRegion = AIMLocations.LRegion,
		@LUserDefined = AIMLocations.LUserDefined,
		@FcstDemand = Item.FcstDemand,
		@MAE = Item.MAE,
		@Trend = Item.Trend,
		@UOM = Item.UOM
	FROM Item 
	INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID 
	WHERE Item.LcID = @LcID
	AND Item.Item = @Item
	
	SELECT @Return_Stat = @@rowcount
	
	-- 	Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2 -- 	ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE
	BEGIN
		RETURN @Return_Stat -- 	SUCCESSFUL
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO



SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects WHERE ID = object_id(N'[dbo].[AIM_DmdPlanAdjust_PromoteToMaster_Sp]') AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
	DROP PROCEDURE [dbo].[AIM_DmdPlanAdjust_PromoteToMaster_Sp]
GO

/******************************************************************************
**	Name: AIM_DmdPlanAdjust_PromoteToMaster_Sp
**	Desc: Updates AIMFcstMaster from AIMFcstStoreAdjustLog for the given FcstID.
**		If not found, then inserts a new record
**
**	Returns: 
** 			1) 0 - Success
** 			2) -1 - Failure
** 			3) <-1 - SQL Error
**	Values:  
**              
**	Auth:	Annalakshmi Stocksdale
**	Date:	2004/03/04
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:			Description:
**	---------- 	------------		-------------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_DmdPlanAdjust_PromoteToMaster_Sp
(
	@UserID as nvarchar(12),	-- Required for authorization
	@FcstSetupKey as int,
	@FcstStoreKey as int,
	@LcID as nvarchar(12) = NULL,
	@Item as nvarchar(25) = NULL,
	@AdjustType as tinyint = NULL,
	@AdjustQty as decimal(10, 2) = NULL,
	@AdjustStartDate as datetime = NULL,
	@AdjustEndDate as datetime = NULL,
	@AdjustUserID as nvarchar(12) = NULL
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @FiscalYear as int
	DECLARE @FiscalPeriod as int
	DECLARE @PeriodStartDate as datetime
	DECLARE @PeriodEndDate as datetime
	DECLARE @Action tinyint
	DECLARE @TargetDate datetime
	DECLARE @LowerDate datetime
	DECLARE @UpperDate datetime
	DECLARE @TargetPeriod int
	DECLARE @TargetFiscalYear int
	DECLARE @RETURNDATE datetime
	DECLARE @RETURNNUMBER int
	
	-- Validate the required parameters.
-- 	IF @UserID IS NULL
-- 	OR @FcstSetupKey IS NULL 
-- 	OR @StoreAdjustLogType IS NULL
-- 	OR @LcID IS NULL
-- 	BEGIN
-- 	  	RETURN -1
-- 	END

	-- 	EXEC @Return_Stat = AIM_DmdPlanFcstUser_CheckAccess_Sp @UserID, @FcstSetupKey, @AccessCode OUTPUT
	-- 	IF @Return_Stat < 0 
	-- 	BEGIN
	-- 		SET @AccessCode = 0
	-- 	END	
	-- Update if not locked and has access
	-- IF @AccessCode >= 2
	-- BEGIN
		BEGIN TRANSACTION

			-- OVERRIDES, First	
			UPDATE AIMFcstMaster SET 
				AdjustQty = CASE STOREADJ.AdjustType WHEN 0 THEN COALESCE (STOREADJ.AdjustQty, AIMFcstMaster.AdjustQty)
					ELSE AIMFcstMaster.AdjustQty END
				, AIMFcstMaster.DateTimeEdit = GETDATE()
			FROM AIMFcstMaster
			INNER JOIN AIMFcstStoreAdjustLog STOREADJ ON
				AIMFcstMaster.LcID = STOREADJ.LcID AND AIMFcstMaster.Item = STOREADJ.Item AND
				AIMFcstMaster.AdjustType = STOREADJ.AdjustType AND
				AIMFcstMaster.PeriodStartDate >= STOREADJ.AdjustStartDate AND AIMFcstMaster.PeriodEndDate <= STOREADJ.AdjustEndDate
			WHERE
				STOREADJ.AdjustDateTime =(SELECT MAX( AIMFcstStoreAdjustLog.AdjustDateTime) 
												FROM AIMFcstStoreAdjustLog 
												WHERE AIMFcstStoreAdjustLog.AdjustType = 0 
												AND AIMFcstStoreAdjustLog.IsPromoted = 0)
				AND STOREADJ.FcstStoreKey = @FcstStoreKey
	-- what about partial periods?
			IF @Return_Stat > 0
			BEGIN
				INSERT INTO AIMFcstMaster (
					LcID, Item, PeriodStartDate, PeriodEndDate, 
					AdjustType, AdjustQty, FiscalYear, FiscalPeriod, DateTimeEdit
				) SELECT TOP 1
					STOREADJ.LcID, STOREADJ.Item, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
					, AdjustType, AdjustQty , NULL, NULL, GETDATE()
				FROM AIMFcstStoreAdjustLog STOREADJ
				WHERE
					STOREADJ.AdjustDateTime =(SELECT MAX( AIMFcstStoreAdjustLog.AdjustDateTime) 
													FROM AIMFcstStoreAdjustLog 
													WHERE AIMFcstStoreAdjustLog.AdjustType = 0 	-- Adj_Override
													AND AIMFcstStoreAdjustLog.IsPromoted = 0)
					AND STOREADJ.FcstStoreKey = @FcstStoreKey
					AND STOREADJ.IsPromoted = 0 		--False
		
				SET @Return_Stat = @@ROWCOUNT
				-- Check for SQL Server errors.
				IF @@ERROR <> 0 
				BEGIN
					ROLLBACK TRANSACTION
				 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
				END
			END

			UPDATE AIMFcstStoreAdjustLog SET IsPromoted = 1	-- True
			WHERE AIMFcstStoreAdjustLog.AdjustDateTime =(SELECT MAX( AIMFcstStoreAdjustLog.AdjustDateTime) 
											FROM AIMFcstStoreAdjustLog 
											WHERE AIMFcstStoreAdjustLog.AdjustType = 0 
											AND AIMFcstStoreAdjustLog.IsPromoted = 0)
				AND AIMFcstStoreAdjustLog.FcstStoreKey = @FcstStoreKey
				AND AIMFcstStoreAdjustLog.IsPromoted = 0 	-- False
				AND AIMFcstStoreAdjustLog.AdjustType = 0	-- Adj_Override
			SET @Return_Stat = @@ROWCOUNT
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
				ROLLBACK TRANSACTION
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
		COMMIT TRANSACTION

		BEGIN TRANSACTION
			-- Then Adj_UNITS
			UPDATE AIMFcstMaster SET
				AdjustQty = AIMFcstMaster.AdjustQty + 
							(SELECT ISNULL(SUM(STOREADJ.AdjustQty), 0) 
							FROM AIMFcstStoreAdjustLog STOREADJ, AIMFcstMaster
							WHERE
								AIMFcstMaster.LcID = STOREADJ.LcID AND
								AIMFcstMaster.Item = STOREADJ.Item AND
								AIMFcstMaster.AdjustType = STOREADJ.AdjustType AND
								AIMFcstMaster.PeriodStartDate >= STOREADJ.AdjustStartDate AND
								AIMFcstMaster.PeriodEndDate <= STOREADJ.AdjustEndDate
								AND STOREADJ.FcstStoreKey = @FcstStoreKey
								AND STOREADJ.IsPromoted = 0		--False
								AND AIMFcstMaster.AdjustType  = 1	-- Adj_Units
							GROUP BY 
								STOREADJ.LcID, STOREADJ.Item
				 				, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
								, STOREADJ.AdjustType
								)
				, AIMFcstMaster.DateTimeEdit = GETDATE()
			FROM AIMFcstMaster
			INNER JOIN AIMFcstStoreAdjustLog STOREADJ ON
				AIMFcstMaster.LcID = STOREADJ.LcID AND AIMFcstMaster.Item = STOREADJ.Item AND
				AIMFcstMaster.AdjustType = STOREADJ.AdjustType AND
				AIMFcstMaster.PeriodStartDate >= STOREADJ.AdjustStartDate AND AIMFcstMaster.PeriodEndDate <= STOREADJ.AdjustEndDate
			WHERE
				STOREADJ.FcstStoreKey = @FcstStoreKey
				AND STOREADJ.IsPromoted = 0	-- False
				AND AIMFcstMaster.AdjustType  = 1	-- Adj_Units
	-- what about partial periods?
	
			IF @Return_Stat > 0
			BEGIN
				INSERT INTO AIMFcstMaster (
					LcID, Item
					, PeriodStartDate, PeriodEndDate
					, AdjustType, AdjustQty
					, FiscalYear, FiscalPeriod
					, DateTimeEdit
				) SELECT 
					STOREADJ.LcID, STOREADJ.Item
	 				, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
					, STOREADJ.AdjustType, ISNULL(SUM(STOREADJ.AdjustQty), 0) 
					, NULL, NULL
					, GETDATE()
					FROM AIMFcstStoreAdjustLog STOREADJ
					INNER JOIN AIMFcstMaster ON
						STOREADJ.LcID = AIMFcstMaster.LcID AND
						STOREADJ.Item = AIMFcstMaster.Item AND
						STOREADJ.AdjustType = AIMFcstMaster.AdjustType AND
						STOREADJ.AdjustStartDate <= AIMFcstMaster.PeriodStartDate AND
						STOREADJ.AdjustEndDate >= AIMFcstMaster.PeriodEndDate 
					WHERE
						STOREADJ.FcstStoreKey = @FcstStoreKey AND
						STOREADJ.IsPromoted = 0 AND	-- False
						STOREADJ.AdjustType = 1	-- Adj_Units
					GROUP BY 
						STOREADJ.LcID, STOREADJ.Item
		 				, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
						, STOREADJ.AdjustType

				SET @Return_Stat = @@ROWCOUNT
				-- Check for SQL Server errors.
				IF @@ERROR <> 0 
				BEGIN
					ROLLBACK TRANSACTION
				 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
				END
			END

			UPDATE AIMFcstStoreAdjustLog SET 
				IsPromoted = 1	-- True
			WHERE 
				AIMFcstStoreAdjustLog.FcstStoreKey = @FcstStoreKey
				AND AIMFcstStoreAdjustLog.IsPromoted = 0 	-- False
				AND AIMFcstStoreAdjustLog.AdjustType = 1	-- Adj_Units
			SET @Return_Stat = @@ROWCOUNT
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
				ROLLBACK TRANSACTION
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
		COMMIT TRANSACTION

		BEGIN TRANSACTION
			-- Then Percent
			UPDATE AIMFcstMaster SET
				AdjustQty = AIMFcstMaster.AdjustQty + 
							(SELECT ISNULL(SUM(STOREADJ.AdjustQty), 0) 
							FROM AIMFcstStoreAdjustLog STOREADJ, AIMFcstMaster
							WHERE
								AIMFcstMaster.LcID = STOREADJ.LcID AND
								AIMFcstMaster.Item = STOREADJ.Item AND
								AIMFcstMaster.AdjustType = STOREADJ.AdjustType AND
								AIMFcstMaster.PeriodStartDate >= STOREADJ.AdjustStartDate AND
								AIMFcstMaster.PeriodEndDate <= STOREADJ.AdjustEndDate
								AND STOREADJ.FcstStoreKey = @FcstStoreKey
								AND STOREADJ.IsPromoted = 0		--False
								AND AIMFcstMaster.AdjustType  = 2	-- Adj_Percent
							GROUP BY 
								STOREADJ.LcID, STOREADJ.Item
				 				, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
								, STOREADJ.AdjustType
								)
				, AIMFcstMaster.DateTimeEdit = GETDATE()
			FROM AIMFcstMaster
			INNER JOIN AIMFcstStoreAdjustLog STOREADJ ON
				AIMFcstMaster.LcID = STOREADJ.LcID AND AIMFcstMaster.Item = STOREADJ.Item AND
				AIMFcstMaster.AdjustType = STOREADJ.AdjustType AND
				AIMFcstMaster.PeriodStartDate >= STOREADJ.AdjustStartDate AND AIMFcstMaster.PeriodEndDate <= STOREADJ.AdjustEndDate
			WHERE
				STOREADJ.FcstStoreKey = @FcstStoreKey
				AND STOREADJ.IsPromoted = 0	-- False
				AND AIMFcstMaster.AdjustType  = 2	-- Adj_Percent

	-- what about partial periods?
	
			IF @Return_Stat > 0
			BEGIN
				INSERT INTO AIMFcstMaster (
					LcID, Item
					, PeriodStartDate, PeriodEndDate
					, AdjustType, AdjustQty
					, FiscalYear, FiscalPeriod
					, DateTimeEdit
				) SELECT 
					STOREADJ.LcID, STOREADJ.Item
	 				, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
					, STOREADJ.AdjustType, ISNULL(SUM(STOREADJ.AdjustQty), 0) 
					, NULL, NULL
					, GETDATE()		
					FROM AIMFcstStoreAdjustLog STOREADJ
					INNER JOIN AIMFcstMaster ON
						STOREADJ.LcID = AIMFcstMaster.LcID AND
						STOREADJ.Item = AIMFcstMaster.Item AND
						STOREADJ.AdjustType = AIMFcstMaster.AdjustType AND
						STOREADJ.AdjustStartDate <= AIMFcstMaster.PeriodStartDate AND
						STOREADJ.AdjustEndDate >= AIMFcstMaster.PeriodEndDate 
					WHERE
						STOREADJ.FcstStoreKey = @FcstStoreKey AND
						STOREADJ.IsPromoted = 0 AND
						STOREADJ.AdjustType = 2	-- Adj_Percent
					GROUP BY 
						STOREADJ.LcID, STOREADJ.Item
		 				, STOREADJ.AdjustStartDate, STOREADJ.AdjustEndDate
						, STOREADJ.AdjustType


				SET @Return_Stat = @@ROWCOUNT
				-- Check for SQL Server errors.
				IF @@ERROR <> 0 
				BEGIN
					ROLLBACK TRANSACTION
				 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
				END
			END

			UPDATE AIMFcstStoreAdjustLog SET 
				IsPromoted = 1	-- True
			WHERE 
				AIMFcstStoreAdjustLog.FcstStoreKey = @FcstStoreKey
				AND AIMFcstStoreAdjustLog.IsPromoted = 0 
				AND AIMFcstStoreAdjustLog.AdjustType = 2	-- Adj_Percent

			SET @Return_Stat = @@ROWCOUNT
			-- Check for SQL Server errors.
			IF @@ERROR <> 0 
			BEGIN
				ROLLBACK TRANSACTION
			 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
			END
		COMMIT TRANSACTION

-- 	END
-- 	ELSE
-- 	BEGIN
-- 	 	RETURN -3 -- Not Permitted Premissions
-- 	END

	IF @Return_Stat = 0 RETURN -1
	ELSE RETURN 1

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
