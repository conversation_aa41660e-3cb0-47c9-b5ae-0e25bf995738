SET NOCOUNT ON

DELETE FROM AIMCodeLookup WHERE CODETYPE = 'ADJUSTTYPE' AND LangID = 'en-us'

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'ADJUSTTYPE', '0', 'Override')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'ADJUSTTYPE', '1', 'Units')


DELETE FROM AIMCodeLookup WHERE CODETYPE = 'DATACALCTYPE' AND LangID = 'en-us'

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'ADJUSTTYPE', '2', 'Percentage')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '1', 'System Requirements')

INSERT INTO AIMCodeLookup (Lang<PERSON>, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '2', 'Adjusted System Requirements')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '3', 'Net Requirements')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '4', 'Adjusted Net Requirements')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '5', 'Net Reqt. + Production Constraints')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '6', 'Net Req + Planned Receipts')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '7', 'Projected Inventory')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '8', 'Projected Inventory + Production Constraints')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '9', 'Historical Demand')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '10', 'Cost Adjustment')

INSERT INTO AIMCodeLookup (LangID, CodeType, CodeID, CodeDesc) 
VALUES ('en-us', 'DATACALCTYPE', '11', 'Price Adjustment')



UPDATE STATISTICS AIMCodeLookup WITH RESAMPLE, ALL

SELECT * FROM AIMCodeLookup 
WHERE LangID = 'en-us' 
AND CODETYPE IN ( 'ADJUSTTYPE', 'DATACALCTYPE')
