SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BatchFcst_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BatchFcst_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BatchFcst_Get_Sp
**	Desc: Gets Forecast data from BatchFcst
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - Forecast Repository,
**               Recordset - Forecast Repository Detail
**              
**	Auth:   Srinivas Uddanti 
**	Date:   03/06/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_BatchFcst_Get_Sp
(
 	@RepositoryKey      				numeric(18,0),       
       	@LcID     					nvarchar(12),
        @Item   					nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

SELECT RepositoryKey, LcID, Item, FcstPds, 
COALESCE(SysFcst,0) SysFcst,
COALESCE(SysNet,0) SysNet,
COALESCE(ModSysFcst,0)ModSysFcst,
COALESCE(ModSysNet,0) ModSysNet,
COALESCE(PlannedRct,0) PlannedRct
FROM ForecastRepositoryBatch 
WHERE 
RepositoryKey =@RepositoryKey AND
LcID = @LcID AND 
Item = @Item
ORDER BY FcstPds

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END		

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


