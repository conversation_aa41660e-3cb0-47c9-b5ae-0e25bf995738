SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastFreezePds_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastFreezePds_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastFreezePds_Get_Sp
**	Desc: Gets the number of frozen periods for a Forecast and Location
**
**	Returns: 1)@found - Can be Zero
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - AIMForecastDetail
**              
**	Auth:   Wade Riza 
**	Date:   08/01/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 	08/13/2002 A.Stocksdale	Added validation for LcID = 'ALL' to 
** 				return all records for a FcstID
**      08/15/2002 Wade Riza    Updated Return Codes
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastFreezePds_Get_Sp
(
    @FcstID						nvarchar(12),
    @LcID						nvarchar(12),
	@FreezePds					int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1  -- ERROR (No Valid Forecast ID) 
END	
ELSE
BEGIN
	-- Make sure the title is valid.
	IF (SELECT COUNT(*) FROM AIMForecast
	WHERE FcstID = @FcstID) = 0

	RETURN -1 -- ERROR (No Valid data found in Database)
END

IF @LcID = 'ALL'
BEGIN
	SELECT * FROM AIMForecastFreezePds
    	WHERE AIMForecastFreezePds.FcstID = @FcstID 
    	ORDER BY LcID

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
END
ELSE
BEGIN
	SELECT @FreezePds = FreezePds FROM AIMForecastFreezePds
	WHERE FcstID = @FcstID and LcID = @LcID

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
 
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
END

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
   	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END	
ELSE
BEGIN
	IF @found > 0 
	BEGIN
		RETURN 0  -- SUCCESSFUL
	END
	ELSE
	BEGIN
		RETURN -1  -- ERROR (NO DATA FOUND)
	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

