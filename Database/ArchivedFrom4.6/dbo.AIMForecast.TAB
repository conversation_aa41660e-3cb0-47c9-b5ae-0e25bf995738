/****** Object:  Table [dbo].[AIMForecast]    Script Date: 05/30/2003 11:04:29 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMForecast]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMForecast]
GO

CREATE TABLE [dbo].[AIMForecast] (
	[FcstId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstDesc] [nvarchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMForecast_FcstDesc] DEFAULT (''),
	[FcstStatus] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_FcstStatus] DEFAULT (0),
	[FcstLevel] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_FcstLevel] DEFAULT (0),
	[FcstInterval] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_FcstInterval] DEFAULT (2),
	[FcstUnit] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_FcstUnit] DEFAULT (0),
	[NetRqmts] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_NetRqmts] DEFAULT (0),
	[FcstPds] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_FcstPds] DEFAULT (12),
	[FcstStartDate] [datetime] NOT NULL CONSTRAINT [DF_AIMForecast_FcstStartDate] DEFAULT (0),
	[AdjustPct] [decimal](5, 2) NOT NULL CONSTRAINT [DF_AIMForecast_AdjustPct] DEFAULT (0),
	[ApplyTrend] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_ApplyTrend] DEFAULT (0),
	[RevFreq] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_RevFreq] DEFAULT (2),
	[RevInterval] [tinyint] NOT NULL CONSTRAINT [DF_AIMForecast_RevInterval] DEFAULT (1),
	[NextRevDate] [datetime] NOT NULL CONSTRAINT [DF_AIMForecast_NextRevDate] DEFAULT ('01/01/1990'),
	[FcstContact] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMForecast_FcstContact] DEFAULT (''),
	[FcstEMail] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMForecast_FcstEMail] DEFAULT (''),
	[FcstLinkId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[FcstOutput] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL CONSTRAINT [DF_AIMForecast_FcstOutputFile] DEFAULT (N'N'),
	[FcstLocked] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FuturePdsUpdate] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
-- Item filter criteria
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[ItStat] [nvarchar] (1)COLLATE SQL_Latin1_General_CP1_CI_AS,
	[VnID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Assort] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[ByID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class1] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class2] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class3] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[Class4] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LStatus] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LDivision] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LRegion] [nvarchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[LUserDefined] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS,
	[EnableFcstVersions] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMForecast] PRIMARY KEY  CLUSTERED 
	(
		[FcstId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


