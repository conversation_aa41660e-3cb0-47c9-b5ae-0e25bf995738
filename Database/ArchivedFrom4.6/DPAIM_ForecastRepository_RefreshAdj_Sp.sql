SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_ForecastRepository_RefreshAdj_Sp    Script Date: 10/12/2004 10:54:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_ForecastRepository_RefreshAdj_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_ForecastRepository_RefreshAdj_Sp]
GO









/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  TH<PERSON> SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: DPAIM_ForecastRepository_RefreshAdj_Sp
**	Desc: Gets Forecast Repository data
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  
**            
**              
**	Auth:   Srinivas Uddanti
**	Date:   06/14/2004
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------

*******************************************************************************/

CREATE            PROCEDURE DPAIM_ForecastRepository_RefreshAdj_Sp
(
 	@RepositoryKey 	        numeric(18,0)    
       
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END


--Refresh the Adjustment from the adj log into the ForecastReposiotyDetail table
--Clear any old adjustments

UPDATE ForecastRepositoryDetail 
SET QtyAdj =0,QtyAdjPct =0,QtyAdjOverRide =0,AdjOverRide =0,
MasterQtyAdj=0,MasterQtyAdjPct=0,MasterQtyAdjOverRide =0,MasterAdjOverRide =0
WHERE RepositoryKey =@RepositoryKey 


--Update the QtyAdj and QtyAdjPct fields

UPDATE forecastrepositorydetail 
set qtyadj =d.qtyadj,
qtyadjpct =d.qtyadjpct
from forecastrepositorydetail inner join 
(select a.RepositoryKey,a.lcid,a.item,a.FcstPdBegDate,
sum(case when adjusttype =1 then adjustqty else 0 end) "QtyAdj",
sum(case when adjusttype =2 then adjustqty else 0 end) "QtyAdjPct"
from ForeCastRepositoryDetail a,forecastRepository_Log b
where a.lcid =b.lcid 
and a.item=b.item
and a.FcstPdBegDate=b.adjustBegdate
and b.adjusttype in(1,2)
and a.repositorykey =b.repositorykey
and a.repositorykey =@RepositoryKey 
and b.ispromoted =0  
group by a.RepositoryKey, a.lcid,a.item,a.FcstPdBegDate ) d
ON forecastrepositorydetail.repositorykey = d.repositorykey
and forecastrepositorydetail.lcid =d.lcid 
and forecastrepositorydetail.item=d.item
and forecastrepositorydetail.FcstPdBegDate =d.FcstPdBegDate

--Update the AdjOverRide field
/*
update forecastrepositorydetail 
set qtyadj =e.adjustqty,
AdjOverRide =1
from forecastrepositorydetail inner join 
(select c.RepositoryKey,c.lcid,c.item,c.adjustBegdate,c.adjustqty, c.OverRideEnabled
from forecastrepository_Log c,
(select   a.lcid,a.item,a.FcstPdBegDate,max(b.fcstadjustkey) "maxfcstadjustkey"
from ForecastRepositoryDetail a,ForecastRepository_Log b
where a.lcid =b.lcid and a.item=b.item
and a.FcstPdBegDate=b.adjustBegdate
and b.adjusttype=0
and a.RepositoryKey =b.RepositoryKey
and a.RepositoryKey =@RepositoryKey 
and b.ispromoted =0
--and a.AdjustType=1
group by a.lcid,a.item,a.FcstPdBegDate) d
where c.fcstadjustkey =d.maxfcstadjustkey
and  c.OverRideEnabled =1) e
ON forecastrepositorydetail.repositorykey = e.repositorykey
and forecastrepositorydetail.lcid =e.lcid 
and forecastrepositorydetail.item=e.item
and forecastrepositorydetail.FcstPdBegDate =e.adjustBegdate


update forecastrepositorydetail 
set
AdjOverRide =2
from forecastrepositorydetail inner join 
(select c.RepositoryKey,c.lcid,c.item,c.adjustBegdate,c.adjustqty, c.OverRideEnabled
from forecastrepository_Log c,
(select   a.lcid,a.item,a.FcstPdBegDate,max(b.fcstadjustkey) "maxfcstadjustkey"
from ForecastRepositoryDetail a,ForecastRepository_Log b
where a.lcid =b.lcid and a.item=b.item
and a.FcstPdBegDate=b.adjustBegdate
and b.adjusttype=0
and a.RepositoryKey =b.RepositoryKey
and a.RepositoryKey =@RepositoryKey 
and b.ispromoted =0
--and a.AdjustType=1
group by a.lcid,a.item,a.FcstPdBegDate) d
where c.fcstadjustkey =d.maxfcstadjustkey
and  c.OverRideEnabled =2) e
ON forecastrepositorydetail.repositorykey = e.repositorykey
and forecastrepositorydetail.lcid =e.lcid 
and forecastrepositorydetail.item=e.item
and forecastrepositorydetail.FcstPdBegDate =e.adjustBegdate
*/

--Update the AdjOverRide field

update forecastrepositorydetail 
set qtyadjoverride =e.adjustqty,
AdjOverRide =e.overrideenabled
from forecastrepositorydetail inner join 
(select c.RepositoryKey,c.lcid,c.item,c.adjustBegdate,c.adjustqty, c.OverRideEnabled
from forecastrepository_Log c,
(select   a.lcid,a.item,a.FcstPdBegDate,max(b.fcstadjustkey) "maxfcstadjustkey"
from ForecastRepositoryDetail a,ForecastRepository_Log b
where a.lcid =b.lcid and a.item=b.item
and a.FcstPdBegDate=b.adjustBegdate
and b.adjusttype=0
and a.RepositoryKey =b.RepositoryKey
and a.RepositoryKey =@RepositoryKey 
and b.ispromoted =0
--and b.OverRideEnabled=1
group by a.lcid,a.item,a.FcstPdBegDate) d
where c.fcstadjustkey =d.maxfcstadjustkey) e
--and  c.OverRideEnabled in(1)) e
ON forecastrepositorydetail.repositorykey = e.repositorykey
and forecastrepositorydetail.lcid =e.lcid 
and forecastrepositorydetail.item=e.item
and forecastrepositorydetail.FcstPdBegDate =e.adjustBegdate



Update forecastrepositorydetail 
set MasterQtyAdj=AIMFcstMaster.QtyAdj,
MasterQtyAdjPct =AIMFcstMaster.QtyAdjPct,
MasterQtyAdjOverRide =AIMFcstMaster.QtyAdjOverRide,
MasterAdjOverRide=AIMFcstMaster.AdjOverRide
From forecastrepositorydetail,AIMFcstMaster
WHERE forecastrepositorydetail.Lcid = AIMFcstMaster.Lcid and
forecastrepositorydetail.Item =AIMFcstMaster.Item and
forecastrepositorydetail.FcstPdBegDate=AIMFcstMaster.PeriodBegDate and
forecastrepositorydetail.RepositoryKey =@RepositoryKey 



update forecastrepositorydetail 
set MasterAdjOverRide =e.overrideenabled
from forecastrepositorydetail inner join 
(select c.RepositoryKey,c.lcid,c.item,c.adjustBegdate,c.adjustqty, c.OverRideEnabled
from forecastrepository_Log c,
(select   a.lcid,a.item,a.FcstPdBegDate,max(b.fcstadjustkey) "maxfcstadjustkey"
from ForecastRepositoryDetail a,ForecastRepository_Log b
where a.lcid =b.lcid and a.item=b.item
and a.FcstPdBegDate=b.adjustBegdate
and b.adjusttype=0
and a.RepositoryKey =b.RepositoryKey
and a.RepositoryKey =@RepositoryKey 
and b.ispromoted =0
and b.OverRideEnabled=2
--and a.AdjustType=1
group by a.lcid,a.item,a.FcstPdBegDate) d
where c.fcstadjustkey =d.maxfcstadjustkey
and  c.OverRideEnabled in(2)) e
ON forecastrepositorydetail.repositorykey = e.repositorykey
and forecastrepositorydetail.lcid =e.lcid 
and forecastrepositorydetail.item=e.item
and forecastrepositorydetail.FcstPdBegDate =e.adjustBegdate



SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

	RETURN 1  -- SUCCESSFUL








GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

