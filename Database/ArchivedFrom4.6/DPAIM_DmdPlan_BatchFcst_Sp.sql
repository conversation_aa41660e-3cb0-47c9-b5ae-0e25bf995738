SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_DmdPlan_BatchFcst_Sp    Script Date: 10/12/2004 10:54:51 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_DmdPlan_BatchFcst_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_DmdPlan_BatchFcst_Sp]
GO









/*******************************************************************************
**	Name: DPAIM_DmdPlan_BatchFcst_Sp
**	Desc: Gets BatchFcst Values
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Srinivas Uddanti
**	Date:   2004/06/28
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE        PROCEDURE DPAIM_DmdPlan_BatchFcst_Sp 
(
	@FcstID as nvarchar(12),
	@FcstPdBegDate as datetime,
	@VnID as nvarchar(12) = '',
	@Assort as nvarchar(12) = '',
	@LcID as nvarchar(12) = '',
	@Item as nvarchar(25) = '',
	@ItStat as nvarchar(1) = '',
	@Class1 as nvarchar(50) = '',
	@Class2 as nvarchar(50) = '',
	@Class3 as nvarchar(50) = '',
	@Class4 as nvarchar(50) = '',
	@ByID as nvarchar(12) = '',
	@LStatus as nvarchar(1) = '',
	@LDivision as nvarchar(20) = '',
	@LRegion as nvarchar(20) = '',
	@LUserDefined as nvarchar(30) = '',
	@RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @Return_Stat as int
	DECLARE @RepositoryKey as Numeric

	IF @FcstID IS NOT NULL
	BEGIN
		SELECT 
			@VnID = CASE WHEN (AIMFcstSetup.VnID IS NULL) THEN @VnID
				WHEN RTRIM(AIMFcstSetup.VnID) = '' THEN @VnID
				ELSE AIMFcstSetup.VnID
				END, 
			@Assort =  CASE WHEN (AIMFcstSetup.Assort IS NULL) THEN @Assort
				WHEN RTRIM(AIMFcstSetup.Assort) = '' THEN @Assort
				ELSE AIMFcstSetup.Assort
				END, 
			@LcID =  CASE WHEN (AIMFcstSetup.LcID IS NULL) THEN @LcID
				WHEN RTRIM(AIMFcstSetup.LcID) = '' THEN @LcID
				ELSE AIMFcstSetup.LcID
				END, 
			@Item =  CASE WHEN (AIMFcstSetup.Item IS NULL) THEN @Item
				WHEN RTRIM(AIMFcstSetup.Item) = '' THEN @Item
				ELSE AIMFcstSetup.Item
				END, 
			@ItStat =  CASE WHEN (AIMFcstSetup.ItStat IS NULL) THEN @ItStat
				WHEN RTRIM(AIMFcstSetup.ItStat) = '' THEN @ItStat
				ELSE AIMFcstSetup.ItStat
				END, 
			@Class1 =  CASE WHEN (AIMFcstSetup.Class1 IS NULL) THEN @Class1
				WHEN RTRIM(AIMFcstSetup.Class1) = '' THEN @Class1
				ELSE AIMFcstSetup.Class1
				END, 
			@Class2 =  CASE WHEN (AIMFcstSetup.Class2 IS NULL) THEN @Class2
				WHEN RTRIM(AIMFcstSetup.Class2) = '' THEN @Class2
				ELSE AIMFcstSetup.Class2
				END, 
			@Class3 =  CASE WHEN (AIMFcstSetup.Class3 IS NULL) THEN @Class3
				WHEN RTRIM(AIMFcstSetup.Class3) = '' THEN @Class3
				ELSE AIMFcstSetup.Class3
				END, 
			@Class4 =  CASE WHEN (AIMFcstSetup.Class4 IS NULL) THEN @Class4
				WHEN RTRIM(AIMFcstSetup.Class4) = '' THEN @Class4
				ELSE AIMFcstSetup.Class4
				END, 
			@ByID =  CASE WHEN (AIMFcstSetup.ByID IS NULL) THEN @ByID
				WHEN RTRIM(AIMFcstSetup.ByID) = '' THEN @ByID
				ELSE AIMFcstSetup.ByID
				END, 
			@LStatus =  CASE WHEN (AIMFcstSetup.LStatus IS NULL) THEN @LStatus
				WHEN RTRIM(AIMFcstSetup.LStatus) = '' THEN @LStatus
				ELSE AIMFcstSetup.LStatus
				END, 
			@LDivision =  CASE WHEN (AIMFcstSetup.LDivision IS NULL) THEN @LDivision
				WHEN RTRIM(AIMFcstSetup.LDivision) = '' THEN @LDivision
				ELSE AIMFcstSetup.LDivision
				END, 
			@LRegion =  CASE WHEN (AIMFcstSetup.LRegion IS NULL) THEN @LRegion
				WHEN RTRIM(AIMFcstSetup.LRegion) = '' THEN @LRegion
				ELSE AIMFcstSetup.LRegion
				END, 
			@LUserDefined =  CASE WHEN (AIMFcstSetup.LUserDefined IS NULL) THEN @LUserDefined
				WHEN RTRIM(AIMFcstSetup.LUserDefined) = '' THEN @LUserDefined
				ELSE AIMFcstSetup.LUserDefined
				END
		FROM AIMFcstSetup
		WHERE AIMFcstSetup.FcstID = @FcstID

		SELECT @RepositoryKey =  ForecastRepository.RepositoryKey
		FROM ForeCastRepository
		WHERE ForeCastRepository.FcstId =@FcstID
	
	END

	
/*
	SELECT 
		Item.LcID,Item.Item,Item.ItDesc,
		Item.Class1,Item.Class2,Item.Class3,Item.Class4,
		Item.ItStat,Item.VelCode,Item.VnID,Item.Assort,Item.ByID, 
		Item.Cube,Item.Weight, Item.Price, Item.Cost,
		FRD.FcstPdBegDate,FRD.Fcst,FRD.FcstAdj,
		FRD.FcstNetReq,FRD.FcstAdjNetReq,FRD.QtyActualOrdered,
		FRD.QtyActualShipped,FRD.QtyProjectedInventory
	From Item 

*/
	
	SELECT 
		Item.LcID,Item.Item,Item.ItDesc,
		Item.Class1,Item.Class2,Item.Class3,Item.Class4,
		Item.ItStat,Item.VelCode,Item.VnID,Item.Assort,Item.ByID, 
		Item.Cube,Item.Weight, Item.Price, Item.Cost,
		FRD.FcstPdBegDate,FRD.Fcst,FRD.MasterFcstAdj,FRD.FcstAdj,
		FRD.FcstNetReq,FRD.FcstAdjNetReq,FRD.QtyProjectedInventory
		,FRD.HistDmd,FRD.QtyActualShipped,FRD.ProdConst
	From Item 
	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
	INNER JOIN AIMLocations ON Item.LcID = AIMLocations.LcID
 	INNER JOIN AIMVendors ON Item.VnID = AIMVendors.VnID
		AND Item.Assort = AIMVendors.Assort
	INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
	INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodID 
	Inner join ForecastRepositoryDetail FRD on
	Item.Lcid =FRD.Lcid and
	Item.Item =FRD.Item 
	WHERE Item.VnID = 
		CASE WHEN (@VnID IS NULL) THEN Item.VnID
		WHEN RTRIM(@VnID) = '' THEN Item.VnID
		ELSE @VnID
		END
	AND Item.Assort = 
		CASE WHEN (@Assort IS NULL) THEN Item.Assort
		WHEN RTRIM(@Assort) = '' THEN Item.Assort
		ELSE @Assort
		END
	AND Item.LcID = 
		CASE WHEN (@LcID IS NULL) THEN Item.LcID
		WHEN RTRIM(@LcID) = '' THEN Item.LcID
		ELSE @LcID
		END
	AND Item.Item = 
		CASE WHEN (@Item IS NULL) THEN Item.Item
		WHEN RTRIM(@Item) = '' THEN Item.Item
		ELSE @Item
		END
	AND Item.ItStat = 
		CASE WHEN (@ItStat IS NULL) THEN Item.ItStat
		WHEN RTRIM(@ItStat) = '' THEN Item.ItStat
		ELSE @ItStat
		END
	AND Item.Class1 = 
		CASE WHEN (@Class1 IS NULL) THEN Item.Class1
		WHEN RTRIM(@Class1) = '' THEN Item.Class1
		ELSE @Class1
		END
	AND Item.Class2 = 
		CASE WHEN (@Class2 IS NULL) THEN Item.Class2
		WHEN RTRIM(@Class2) = '' THEN Item.Class2
		ELSE @Class2
		END
	AND Item.Class3 = 
		CASE WHEN (@Class3 IS NULL) THEN Item.Class3
		WHEN RTRIM(@Class3) = '' THEN Item.Class3
		ELSE @Class3
		END
	AND Item.Class4 = 
		CASE WHEN (@Class4 IS NULL) THEN Item.Class4
		WHEN RTRIM(@Class4) = '' THEN Item.Class4
		ELSE @Class4
		END
	AND Item.ByID = 
		CASE WHEN (@ByID IS NULL) THEN Item.ByID
		WHEN RTRIM(@ByID) = '' THEN Item.ByID
		ELSE @ByID
		END
	AND AIMLocations.LStatus = 
		CASE WHEN (@LStatus IS NULL) THEN AIMLocations.LStatus
		WHEN RTRIM(@LStatus) = '' THEN AIMLocations.LStatus
		ELSE @LStatus
		END
	AND AIMLocations.LDivision = 
		CASE WHEN (@LDivision IS NULL) THEN AIMLocations.LDivision
		WHEN RTRIM(@LDivision) = '' THEN AIMLocations.LDivision
		ELSE @LDivision
		END
	AND AIMLocations.LRegion = 
		CASE WHEN (@LRegion IS NULL) THEN AIMLocations.LRegion
		WHEN RTRIM(@LRegion) = '' THEN AIMLocations.LRegion
		ELSE @LRegion
		END
	AND AIMLocations.LUserDefined = 
		CASE WHEN (@LUserDefined IS NULL) THEN AIMLocations.LUserDefined
		WHEN RTRIM(@LUserDefined) = '' THEN AIMLocations.LUserDefined
		ELSE @LUserDefined
		END
	AND UPPER(Item.ItStat) NOT IN ('I', 'P', 'X')
	AND FRD.RepositoryKey =@RepositoryKey
	And FRD.FcstPdBegDate >=@FcstPdBegDate
	ORDER BY Item.Item, Item.LcID,FRD.FcstPdBegDate
	
	--SET @RowCounter = @@ROWCOUNT
	exec AIM_DmdPlan_ItemList_Sp  @FcstID ,@VnID,
	@Assort,@LcID,@Item,@ItStat,@Class1,@Class2 ,@Class3,@Class4,
	@ByID,@LStatus,@LDivision,@LRegion,@LUserDefined,@RowCounter =@rowcounter output
	
	IF @RowCounter > 0 
	BEGIN
		RETURN 1
	END
	ELSE
	BEGIN
		RETURN -1
	END


END










GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

