SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_RepositoryUserElement_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastUserElement_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_RepositoryUserElement_Get_Sp
**	Desc: Gets UserElement Data from UserElementDetail table
**
**	Parameters:
**
**	Returns: 
**		 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Srinivas Uddanti
**	Date:   2004/10/20
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	---------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_RepositoryUserElement_Get_Sp
(
	@UserElementID as nvarchar(12),
	@RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON

	Select FRD.Item,FRD.Lcid,UED.FcstPdBegDate,UED.FcstPdEndDate,UED.Qty
	From ForecastRepositoryDetail FRD inner Join ForecastRepository FR
	On FRD.RepositoryKey =FR.RepositoryKey
 	Inner Join  UserElement UE On
	FR.FcstID =UE.FcstId 
 	inner join UserElementDetail UED on
	UE.UserElementId =UED.UserElementId
	and FRD.Item =UED.Item and
	FRD.Lcid =UED.Lcid
	and FRD.FcstPdBegDate =UED.FcstPdBegDate
	and UED.UserElementId =@UserElementId
	Order By FRD.lcid,FRD.Item,FRD.FcstPdBegDate
	
	Select @RowCounter =@@RowCount
	IF @RowCounter > 0 
	BEGIN
		RETURN 1
	END
	ELSE
	BEGIN
		RETURN -1
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
