SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_CheckInRepositoryDetail_Sp    Script Date: 10/12/2004 10:54:35 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_CheckInRepositoryDetail_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_CheckInRepositoryDetail_Sp]
GO


/*******************************************************************************
**	Name: DPAIM_CheckInRepositoryDetail_Sp
**	Desc: Retrieves a count of records in the ForecastRepositoryDetail, given a RepositoryKey
**
**	Parameters: Forecast ID
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/10/19
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   	Author:	    	Description:
**	---------- 	------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE DPAIM_CheckInRepositoryDetail_Sp 
(
	@RepositoryKey as numeric(18, 0)
	, @RowCount as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON

	IF @RepositoryKey IS NULL
	BEGIN
		RETURN -1
	END

	SELECT @RowCount = COUNT(*)
	FROM ForecastRepositoryDetail
	WHERE ForecastRepositoryDetail.RepositoryKey = @RepositoryKey

	IF @@ERROR <> 0 
	BEGIN
		RETURN @@ERROR
	END
	ELSE
	BEGIN
		RETURN 1	-- Success
	END

END
GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

	