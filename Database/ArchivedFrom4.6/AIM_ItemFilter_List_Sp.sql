SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT * FROM dbo.sysobjects 
 WHERE ID = object_id(N'[dbo].[AIM_ItemFilter_List_Sp]') 
 AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
 DROP PROCEDURE[dbo].[AIM_ItemFilter_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_ItemFilter_List_Sp
** 	Desc: Gets Item data based on the Selection Criteria selected
** 	
** 	Returns: 1)@Return_Stat - Can be Zero
** 	2) -1 - No Data Found
** 	3) -2 - SQL Error
** 	
** 	Auth: Wade Riza 
** 	Date: 08/13/2002
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date: 		Author:	     Description:
** 	---------- 	------------ ------------------------------------------
** 	08/15/2002	Wade Riza    Update Return COdes and Error Handling
** 	09/23/2002	Wade Riza    Removed Hard Coded value for SAVersion 
** 	09/24/2002	Wade Riza    Updated Class Codes to NVARCHAR(50)
** 	10/01/2002	Osama Riyahi Updated Dynamic where to not use 
**                                   ItStatus.DmdUpd
** 	2004/01/24	A.Stocksdale Added new parameters from AIMLocations 
** 				     (LStatus, LDivision, LRegion, LUserDefined)
** 	2004/01/24	A.Stocksdale Removed dynamic where clause variables 
**                                   with a condition-based query.
*******************************************************************************/
CREATE PROCEDURE AIM_ItemFilter_List_Sp
(
	@Item as nvarchar(25) = NULL,
	@VnID as nvarchar(12) = NULL,
	@Assort as nvarchar(12) = NULL,
	@LcID as nvarchar(12) = NULL,
	@Class1 as nvarchar(50) = NULL,
	@Class2 as nvarchar(50) = NULL,
	@Class3 as nvarchar(50) = NULL,
	@Class4 as nvarchar(50) = NULL,
	@ByID as nvarchar(12) = NULL,
	@ItStat as nvarchar(1) = NULL,
	@LStatus as nvarchar(1) = NULL,
	@LDivision as nvarchar(20) = NULL,
	@LRegion as nvarchar(20) = NULL,
	@LUserDefined as nvarchar(30) = NULL
)

-- 	WITH ENCRYPTION /* Production use must be encrypted */
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	DECLARE @Return_Stat as int
	DECLARE @SAVersion as int
	
	SET NOCOUNT ON
	
	EXEC @SAVersion = AIM_GetSaVersion_Sp 
	IF @SAVersion IS NULL
	BEGIN
		SET @SAVersion = 1
	END 
	
	SELECT Item.Item, Item.LcID, 
		Item.ItDesc, Item.ItStat, 
		Item.VnID, Item.Assort, Item.ByID, 
		Item.Class1, Item.Class2, Item.Class3, Item.Class4,
		AIMLocations.LStatus, AIMLocations.LDivision, 
		AIMLocations.LRegion, AIMLocations.LUserDefined
	FROM Item 
	INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
	INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId AND AIMSeasons.SAVersion = @SAVersion
	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
	INNER JOIN AIMLocations ON Item.Lcid = AIMLocations.Lcid 
	INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId AND Item.Assort = AIMVendors.Assort 
	INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
	INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodId 
	WHERE Item.VnID = 
		CASE WHEN (@VNID IS NULL) THEN Item.VnID
		WHEN RTRIM(@VnID) = '' THEN Item.VnID
		ELSE @VnID
		END
	AND Item.Assort = 
		CASE WHEN (@Assort IS NULL) THEN Item.Assort
		WHEN RTRIM(@Assort) = '' THEN Item.Assort
		ELSE @Assort
		END
	AND Item.LcID = 
		CASE WHEN (@LcID IS NULL) THEN Item.LcID
		WHEN RTRIM(@LcID) = '' THEN Item.LcID
		ELSE @LcID
		END
	AND Item.Item = 
		CASE WHEN (@Item IS NULL) THEN Item.Item
		WHEN RTRIM(@Item) = '' THEN Item.Item
		ELSE @Item
		END
	AND Item.ItStat = 
		CASE WHEN (@ItStat IS NULL) THEN Item.ItStat
		WHEN RTRIM(@ItStat) = '' THEN Item.ItStat
		ELSE @ItStat
		END
	AND Item.Class1 = 
		CASE WHEN (@Class1 IS NULL) THEN Item.Class1
		WHEN RTRIM(@Class1) = '' THEN Item.Class1
		ELSE @Class1
		END
	AND Item.Class2 = 
		CASE WHEN (@Class2 IS NULL) THEN Item.Class2
		WHEN RTRIM(@Class2) = '' THEN Item.Class2
		ELSE @Class2
		END
	AND Item.Class3 = 
		CASE WHEN (@Class3 IS NULL) THEN Item.Class3
		WHEN RTRIM(@Class3) = '' THEN Item.Class3
		ELSE @Class3
		END
	AND Item.Class4 = 
		CASE WHEN (@Class4 IS NULL) THEN Item.Class4
		WHEN RTRIM(@Class4) = '' THEN Item.Class4
		ELSE @Class4
		END
	AND Item.ByID = 
		CASE WHEN (@ByID IS NULL) THEN Item.ByID
		WHEN RTRIM(@ByID) = '' THEN Item.ByID
		ELSE @ByID
		END
	AND AIMLocations.LStatus = 
		CASE WHEN (@LStatus IS NULL) THEN AIMLocations.LStatus
		WHEN RTRIM(@LStatus) = '' THEN AIMLocations.LStatus
		ELSE @LStatus
		END
	AND AIMLocations.LDivision = 
		CASE WHEN (@LDivision IS NULL) THEN AIMLocations.LDivision
		WHEN RTRIM(@LDivision) = '' THEN AIMLocations.LDivision
		ELSE @LDivision
		END
	AND AIMLocations.LRegion = 
		CASE WHEN (@LRegion IS NULL) THEN AIMLocations.LRegion
		WHEN RTRIM(@LRegion) = '' THEN AIMLocations.LRegion
		ELSE @LRegion
		END
	AND AIMLocations.LUserDefined = 
		CASE WHEN (@LUserDefined IS NULL) THEN AIMLocations.LUserDefined
		WHEN RTRIM(@LUserDefined) = '' THEN AIMLocations.LUserDefined
		ELSE @LUserDefined
		END
	ORDER BY Item.Item, Item.Lcid 
	
	SELECT @Return_Stat = @@rowcount
	
	-- 	Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2 -- 	ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	ELSE
	BEGIN
		RETURN @Return_Stat -- 	SUCCESSFUL
	END

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


