SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_Forecast_GetKey_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_Forecast_GetKey_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_Forecast_GetKey_Sp
**	Desc: Returns Forecast Key
**
**	Returns: 1)  1 - Successful Insert
**             2)  0 - Fail
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**             4) -3 - User Not Permitted to retrieve data
**	Values:  Record - Forecast Key
**              
**	Auth:   Randy Sadler 
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/30/2002 Wade Riza    Updated validation
*******************************************************************************/

CREATE PROCEDURE AIM_Forecast_GetKey_Sp 
(
    	@FcstId nvarchar(12) = '', 
    	@Action tinyint = 0
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	
	DECLARE @RtnCode int
	
	SET NOCOUNT ON
	SET ROWCOUNT 1	-- will fetch just the first record it finds.
	
	-- Validate the required parameters.
	IF @Action IS NULL
	BEGIN
		RETURN -1
	END
	
	
	-- Get the key to the Forecast Record based on the action code
	IF @Action = 0		-- Get Equal
	BEGIN
		SELECT @FcstId = FcstId FROM AIMForecast WHERE FcstId = @FcstId
		SELECT @RtnCode = @@ROWCOUNT
	END
	IF @Action = 1		-- Get Greater Than
	BEGIN
		SELECT @FcstId = FcstId FROM AIMForecast WHERE FcstId > @FcstId
		SELECT @RtnCode = @@ROWCOUNT
		IF @RtnCode = 0
			SELECT @Action = 6	-- Get Last Record
	END
	IF @Action = 2		-- Get Less Than 
	BEGIN
		SELECT @FcstId = FcstId FROM AIMForecast WHERE FcstId < @FcstId
			ORDER BY FcstID DESC
		SELECT @RtnCode = @@ROWCOUNT
		IF @RtnCode = 0
			SELECT @Action = 5      -- Get first record
	END
	IF @Action = 3		-- Get Greater Than or Equal
	BEGIN
		SELECT @FcstId = FcstId FROM AIMForecast WHERE FcstId >= @FcstId
	    	SELECT @RtnCode = @@ROWCOUNT
	        IF @RtnCode = 0
	            SELECT @Action = 6      -- Get last record
	END
	IF @Action = 4		-- Get Less Than or Equal
	BEGIN
	       SELECT @FcstId = FcstId FROM AIMForecast WHERE FcstId <= @FcstId
			ORDER BY FcstID DESC
	       SELECT @RtnCode = @@ROWCOUNT
	       IF @RtnCode = 0
	            SELECT @Action = 5      -- Get first record
	END

	IF @Action = 5		-- Get First
	BEGIN
		SELECT @FcstId = FcstId FROM AIMForecast  
		SELECT @RtnCode = @@ROWCOUNT
	END
	IF @Action = 6		-- Get Last
	BEGIN
	        SELECT @FcstId = FcstId FROM AIMForecast  
	        	ORDER BY FcstId desc 
	       	SELECT @RtnCode = @@ROWCOUNT
	END


	-- set return recordset and status value
    	SELECT * FROM AIMForecast WHERE FcstId = @FcstId
	IF @RtnCode > 0 
		RETURN 1		-- SUCCEED
	ELSE
		RETURN 0		-- FAIL

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

