SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_FilterCriteria_Select_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_FilterCriteria_Select_Sp]
GO

/*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
** 	Name: AIM_FilterCriteria_Select_Sp
** 	Purpose: Fetches the filters from the Fcst_FilterCriteria table,
**		given the following criteria:
** 		1.  ForecastSetupKey to match with the AIMFcstSetup table
**		2.  Filter Column Name (Optional)
**
** 		Version Number 	- 1.0
** 		Date Created 	- 2004/08/11
** 		Created By 	- Annalakshmi Stocksdale
**
********************************************************************************
** This file contains trade secrets of SSA Global. No part
** may be reproduced or transmitted in any form by any means or for any purpose
** without the express written permission of SSA Global.
********************************************************************************
**	Change History
********************************************************************************
**	Date:      	Author:      Description:
**	---------- 	------------ -------------------------------------------
********************************************************************************
*/

CREATE PROCEDURE AIM_FilterCriteria_Select_Sp (
	@FcstSetupKey int
	, @FilterColumn nvarchar (255) = ''
)
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
	SET NOCOUNT ON

	DECLARE @Return_Stat as int
	DECLARE @CheckValue as decimal(9, 1)
	
	-- Validate the required parameters.
	IF @FcstSetupKey IS NULL
	BEGIN
	  	RETURN -1
	END

	-- Validate the existence of the record in ItemHistory
	SELECT 
		FilterColumn
		, SearchCondition
		, FilterValue
	FROM Fcst_FilterCriteria FFC
	WHERE FFC.FcstSetupKey = @FcstSetupKey
	AND FFC.FilterColumn = CASE 
		WHEN @FilterColumn IS NULL THEN FFC.FilterColumn 
		ELSE @FilterColumn 
		END
	ORDER BY FilterColumn

	SET @Return_Stat = @@ROWCOUNT
	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
	 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
	IF @Return_Stat <= 0 RETURN -1
	ELSE RETURN 1

END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO
