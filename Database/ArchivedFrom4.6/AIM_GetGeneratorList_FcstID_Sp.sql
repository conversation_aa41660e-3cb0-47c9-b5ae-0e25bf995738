SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

IF EXISTS (SELECT name 
		FROM sysobjects 
		WHERE id = object_id(N'[dbo].[AIM_GetGeneratorList_FcstID_Sp]') 
		AND OBJECTPROPERTY(id, N'IsProcedure') = 1)
    DROP PROCEDURE [dbo].[AIM_GetGeneratorList_FcstID_Sp]
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
*******************************************************************************
--	Name: AIM_GetGeneratorList_FcstID_Sp
--	Desc: Returns a list of Forecast IDs (with corres. descriptions) available in the AIMFcstSetUp table
--
--	Returns:	Recordset containing FcstID, FcstDesc
--			a numeric RecordCount indicating the number of records fetched
--	Author:		Srinivas Uddanti
--	Created:	2004/03/05
-------------------------------------------------------------------------------
--	Change History
-------------------------------------------------------------------------------
--	Date:		Updated by:		Description:
--	----------	------------	-----------------------------------------------
--								
*******************************************************************************/

CREATE  PROCEDURE AIM_GetGeneratorList_FcstID_Sp
(	
	@RecordCount As Int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

BEGIN
	SET NOCOUNT ON

	-- Fetch from the table
	Select FcstId,FcstDesc
	From AIMFcstSetup
	ORDER BY FcstId, FcstDesc

	SET @RecordCount = @@ROWCOUNT 

	RETURN
END


GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO


