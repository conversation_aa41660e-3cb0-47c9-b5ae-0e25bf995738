SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastFreezeItem_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastFreezeItem_GetEq_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastFreezeItem_GetEq_Sp
**	Desc: Gets Frozen Items for a Given Forecast
**
**	Returns: 1)  0 - Successful
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Recordset - AIM ForecastDetail
**              
**	Auth:   Osama Riyahi
**	Date:   09/24/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**
*******************************************************************************/

 Create PROCEDURE AIM_ForecastFreezeItem_GetEq_Sp
(
	  @FcstId						nvarchar(12),
	  @LcID     					nvarchar(12),
      @Item   				    	nvarchar(25)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int
SET NOCOUNT ON
 -- Validate the required parameters.
IF @FcstId IS NULL
BEGIN
	RETURN -1
END
    	
IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

SELECT *
    	FROM 	AIMForecastDetail
    	WHERE 	FcstId 	= @FcstId
	AND   	Item   	= @Item
	AND	LcID	= @LcID
	ANd 	ItemLocked = 'Y'

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END		

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

