SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BldForecastSQL_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BldForecastSQL_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON>L NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BldForecastSQL_Sp
**	Desc: Returns the VnItemList recordset
**
**	Parameters:
**
**	Returns: 
**			 1 - Success
**             	 0 - Fail
**             	-1 - No Data Found
**             	-2 - SQL Error
**             	-3 - User Not Permitted to retrieve data
**
**	Values:  Recordset
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   2004/01/23
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/

CREATE PROCEDURE AIM_BldForecastSQL_Sp 
(
	@VnId As nvarchar(12) = '',
	@Assort As nvarchar(12) = '',
	@LcId As nvarchar(12) = '',
	@Item As nvarchar(25) = '',
	@ItStat As nvarchar(1) = '',
	@Class1 As nvarchar(50) = '',
	@Class2 As nvarchar(50) = '',
	@Class3 As nvarchar(50) = '',
	@Class4 As nvarchar(50) = '',
	@ById As nvarchar(12) = '',
	@LStatus as nvarchar(1) = '',
	@LDivision as nvarchar(20) = '',
	@LRegion as nvarchar(20) = '',
	@LUserDefined as nvarchar(30) = '',
	@SAVersion As smallint,
	@RowCounter as int OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
		
	SET NOCOUNT ON
	DECLARE @RtnCode As Int

	IF @VnID = ''
		SET @VnID = NULL
	IF @Assort = ''
		SET @Assort = NULL
	IF @LcID = ''
		SET @LcID = NULL
	IF @Item = ''
		SET @Item = NULL
	IF @ItStat = ''
		SET @ItStat = NULL
	IF @Class1 = ''
		SET @Class1 = NULL
	IF @Class2 = ''
		SET @Class2 = NULL
	IF @Class3 = ''
		SET @Class3 = NULL
	IF @Class4 = ''
		SET @Class4 = NULL
	IF @ByID = ''
		SET @ByID = NULL

	SELECT Item.Item, Item.Lcid, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
		Item.VelCode, Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag, 
		Item.SaId, Item.PmId, Item.Weight, Item.Cube, Item.Price, Item.Cost, Item.UOM, 
		Item.ConvFactor, Item.BuyingUOM, Item.Oh, Item.Oo, Item.ComStk, 
		Item.BkOrder, Item.BkComStk, Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
		Item.UserFcstExpDate, Item.MAE, Item.Trend, Item.FcstUpdCyc, Item.PlnTT, 
		Item.Accum_Lt, Item.ReviewTime, Item.OrderPt, Item.OrderQty, Item.SafetyStock, 
		Item.FcstRT, Item.FcstLT, Item.Fcst_Month, Item.Fcst_Quarter, Item.Fcst_Year, 
		AIMOptions.HiTrndL, 
		AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, 
		AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, 
		AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, 
		AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, 
		AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, 
		AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, 
		AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, 
		AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, 
		AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, 
		AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, 
		AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, 
		AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, 
		AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, 
		AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42, 
		AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, 
		AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, 
		AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, 
		AIMSeasons.BI52, 
		AIMVendors.VName, 
		AIMLocations.DmdScalingFactor, AIMLocations.ScalingEffUntil, 
		PmStatus = isnull(AIMPromotions.PmStatus, 0), 
		PmStartDate = isnull(AIMPromotions.PmStartDate, '01/01/1990'), 
		PmEndDate = isnull(AIMPromotions.PmEndDate, '01/01/1990'), 
		PmAdj01 = isnull(AIMPromotions.PmAdj01,1), PmAdj02 = isnull(AIMPromotions.PmAdj02,1), 
		PmAdj03 = isnull(AIMPromotions.PmAdj03,1), PmAdj04 = isnull(AIMPromotions.PmAdj04,1), 
		PmAdj05 = isnull(AIMPromotions.PmAdj05,1), PmAdj06 = isnull(AIMPromotions.PmAdj06,1), 
		PmAdj07 = isnull(AIMPromotions.PmAdj07,1), PmAdj08 = isnull(AIMPromotions.PmAdj08,1), 
		PmAdj09 = isnull(AIMPromotions.PmAdj09,1), PmAdj10 = isnull(AIMPromotions.PmAdj10,1), 
		PmAdj11 = isnull(AIMPromotions.PmAdj11,1), PmAdj12 = isnull(AIMPromotions.PmAdj12,1), 
		PmAdj13 = isnull(AIMPromotions.PmAdj13,1), PmAdj14 = isnull(AIMPromotions.PmAdj14,1), 
		PmAdj15 = isnull(AIMPromotions.PmAdj15,1), PmAdj16 = isnull(AIMPromotions.PmAdj16,1), 
		PmAdj17 = isnull(AIMPromotions.PmAdj17,1), PmAdj18 = isnull(AIMPromotions.PmAdj18,1), 
		PmAdj19 = isnull(AIMPromotions.PmAdj19,1), PmAdj20 = isnull(AIMPromotions.PmAdj20,1), 
		PmAdj21 = isnull(AIMPromotions.PmAdj21,1), PmAdj22 = isnull(AIMPromotions.PmAdj22,1), 
		PmAdj23 = isnull(AIMPromotions.PmAdj23,1), PmAdj24 = isnull(AIMPromotions.PmAdj24,1), 
		PmAdj25 = isnull(AIMPromotions.PmAdj25,1), PmAdj26 = isnull(AIMPromotions.PmAdj26,1), 
		PmAdj27 = isnull(AIMPromotions.PmAdj27,1), PmAdj28 = isnull(AIMPromotions.PmAdj28,1), 
		PmAdj29 = isnull(AIMPromotions.PmAdj29,1), PmAdj30 = isnull(AIMPromotions.PmAdj30,1), 
		PmAdj31 = isnull(AIMPromotions.PmAdj31,1), PmAdj32 = isnull(AIMPromotions.PmAdj32,1), 
		PmAdj33 = isnull(AIMPromotions.PmAdj33,1), PmAdj34 = isnull(AIMPromotions.PmAdj34,1), 
		PmAdj35 = isnull(AIMPromotions.PmAdj35,1), PmAdj36 = isnull(AIMPromotions.PmAdj36,1), 
		PmAdj37 = isnull(AIMPromotions.PmAdj37,1), PmAdj38 = isnull(AIMPromotions.PmAdj38,1), 
		PmAdj39 = isnull(AIMPromotions.PmAdj39,1), PmAdj40 = isnull(AIMPromotions.PmAdj40,1), 
		PmAdj41 = isnull(AIMPromotions.PmAdj41,1), PmAdj42 = isnull(AIMPromotions.PmAdj42,1), 
		PmAdj43 = isnull(AIMPromotions.PmAdj43,1), PmAdj44 = isnull(AIMPromotions.PmAdj44,1), 
		PmAdj45 = isnull(AIMPromotions.PmAdj45,1), PmAdj46 = isnull(AIMPromotions.PmAdj46,1), 
		PmAdj47 = isnull(AIMPromotions.PmAdj47,1), PmAdj48 = isnull(AIMPromotions.PmAdj48,1), 
		PmAdj49 = isnull(AIMPromotions.PmAdj49,1), PmAdj50 = isnull(AIMPromotions.PmAdj50,1), 
		PmAdj51 = isnull(AIMPromotions.PmAdj51,1), PmAdj52 = isnull(AIMPromotions.PmAdj52,1), 
		AIMMethods.ApplySeasonsIndex , AIMMethods.ApplyTrend, 
		Item.PackRounding, Item.IMin, Item.IMax, Item.CStock, Item.IntSafetyStock, Item.IsIntermittent, 
		Item.Mean_NZ, Item.StdDev_NZ, Item.ReplenCost2, Item.SSAdj, Item.ZSStock, Item.LTVFact, 
		Item.Dser, 
		RevCycles.NextDateTime, RevCycles.RevFreq, RevCycles.RevInterval, RevCycles.RevSunday, 
		RevCycles.RevMonday, RevCycles.RevTuesday, RevCycles.RevWednesday, RevCycles.RevThursday, 
		RevCycles.RevFriday, RevCycles.RevSaturday, RevCycles.WeekQualifier, RevCycles.RevStartDate, 
		RevCycles.RevEndDate, RevCycles.InitBuyPct, RevCycles.InitRevDate, RevCycles.ReviewTime, 
		AIMOptions.Dft_TurnHigh, AIMOptions.Dft_TurnLow, AIMOptions.HiMadP, AIMOptions.LoMadP, 
		AIMOptions.MadExK, 
		AIMLocations.ReplenCost, 
		Item.BkQty01, Item.BkCost01, Item.BkQty02, Item.BkCost02, Item.BkQty03, 
		Item.BkCost03, Item.BkQty04, Item.BkCost04, Item.BkQty05, Item.BkCost05, Item.BkQty06, 
		Item.BkCost06, Item.BkQty07, Item.BkCost07, Item.BkQty08, Item.BkCost08, Item.BkQty09, 
		Item.BkCost09 , Item.BkQty10, Item.BkCost10, 
		AIMLocations.StkDate,  AIMLocations.DemandSource, 
		Item.VelCode,Item.Class1,Item.Class2,Item.Class3,Item.Class4, 
		Item.BuyStrat, Item.UserMin, Item.UserMax, Item.DIFlag, AIMOptions.DIMADP,ItStatus.DmdUpd 
		, AIMLocations.LStatus, AIMLocations.LDivision, AIMLocations.LRegion, AIMLocations.LUserDefined
	From Item 
	INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
	INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId 
		AND AIMSeasons.SAVersion =  @SAVersion
	INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
	INNER JOIN AIMLocations on Item.Lcid = AIMLocations.Lcid 
	INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId 
		AND Item.Assort = AIMVendors.Assort 
	INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
	LEFT OUTER JOIN AIMPromotions ON Item.PmId = AIMPromotions.PmId 
	INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodId 
	WHERE Item.VnID = 
		CASE WHEN (@VNID IS NULL) THEN Item.VnID
		WHEN RTRIM(@VnID) = '' THEN Item.VnID
		ELSE @VnID
		END
	AND Item.Assort = 
		CASE WHEN (@Assort IS NULL) THEN Item.Assort
		WHEN RTRIM(@Assort) = '' THEN Item.Assort
		ELSE @Assort
		END
	AND Item.LcID = 
		CASE WHEN (@LcID IS NULL) THEN Item.LcID
		WHEN RTRIM(@LcID) = '' THEN Item.LcID
		ELSE @LcID
		END
	AND Item.Item = 
		CASE WHEN (@Item IS NULL) THEN Item.Item
		WHEN RTRIM(@Item) = '' THEN Item.Item
		ELSE @Item
		END
	AND Item.ItStat = 
		CASE WHEN (@ItStat IS NULL) THEN Item.ItStat
		WHEN RTRIM(@ItStat) = '' THEN Item.ItStat
		ELSE @ItStat
		END
	AND Item.Class1 = 
		CASE WHEN (@Class1 IS NULL) THEN Item.Class1
		WHEN RTRIM(@Class1) = '' THEN Item.Class1
		ELSE @Class1
		END
	AND Item.Class2 = 
		CASE WHEN (@Class2 IS NULL) THEN Item.Class2
		WHEN RTRIM(@Class2) = '' THEN Item.Class2
		ELSE @Class2
		END
	AND Item.Class3 = 
		CASE WHEN (@Class3 IS NULL) THEN Item.Class3
		WHEN RTRIM(@Class3) = '' THEN Item.Class3
		ELSE @Class3
		END
	AND Item.Class4 = 
		CASE WHEN (@Class4 IS NULL) THEN Item.Class4
		WHEN RTRIM(@Class4) = '' THEN Item.Class4
		ELSE @Class4
		END
	AND Item.ByID = 
		CASE WHEN (@ByID IS NULL) THEN Item.ByID
		WHEN RTRIM(@ByID) = '' THEN Item.ByID
		ELSE @ByID
		END
	AND AIMLocations.LStatus = 
		CASE WHEN (@LStatus IS NULL) THEN AIMLocations.LStatus
		WHEN RTRIM(@LStatus) = '' THEN AIMLocations.LStatus
		ELSE @LStatus
		END
	AND AIMLocations.LDivision = 
		CASE WHEN (@LDivision IS NULL) THEN AIMLocations.LDivision
		WHEN RTRIM(@LDivision) = '' THEN AIMLocations.LDivision
		ELSE @LDivision
		END
	AND AIMLocations.LRegion = 
		CASE WHEN (@LRegion IS NULL) THEN AIMLocations.LRegion
		WHEN RTRIM(@LRegion) = '' THEN AIMLocations.LRegion
		ELSE @LRegion
		END
	AND AIMLocations.LUserDefined = 
		CASE WHEN (@LUserDefined IS NULL) THEN AIMLocations.LUserDefined
		WHEN RTRIM(@LUserDefined) = '' THEN AIMLocations.LUserDefined
		ELSE @LUserDefined
		END
	ORDER BY Item.Item, Item.Lcid 
	
	SET @RowCounter = @@ROWCOUNT

	SET @RtnCode = CASE 
		WHEN @RowCounter > 0 THEN  1		-- SUCCEED	
		WHEN @@ERROR <> 0 THEN @@ERROR
		ELSE  0	-- FAIL
	END	

	RETURN @RtnCode

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

