/****** Object:  Table [dbo].[AIMForecastDetail]    Script Date: 05/30/2003 11:04:30 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMForecastDetail]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMForecastDetail]
GO

CREATE TABLE [dbo].[AIMForecastDetail] (
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[ItemLocked] [nvarchar] (1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMForecastDetail] PRIMARY KEY  CLUSTERED 
	(
		[FcstID],
		[LcID],
		[Item]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


