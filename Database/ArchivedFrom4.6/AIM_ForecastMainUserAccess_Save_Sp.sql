SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMainUserAccess_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMainUserAccess_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastMainUserAccess_Save_Sp
**	Desc: Inserts Forecast Maintenance User Access data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Updated Access Type
**               3) -1 - No Data Found
**               4) -2 - SQL Error
**               5) -3 - Error Multiple Access Types Exists
**	Values:  Record - AccessLevel
**              
**	Auth:   Wade Riza 
**	Date:   08/29/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastMainUserAccess_Save_Sp
(
	@FcstID						nvarchar(12),
	@UserID						nvarchar(12),
	@AccessType					nvarchar(30)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int,
	    @ExistingAccessType		nvarchar(30)

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

IF @UserID IS NULL
BEGIN
	RETURN -1
END

IF @AccessType IS NULL
BEGIN
	RETURN -1
END
  	
BEGIN
	SELECT @ExistingAccessType FROM AIMForecastMainUserAccess 
	WHERE FcstID = @FcstID AND UserID = @UserID and AccessType = @AccessType
    	
	SELECT @found = @@rowcount
END
	
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0 		-- Insert new Record
BEGIN
    	INSERT INTO AIMForecastMainUserAccess
    		(UserID, FcstID, AccessType )
    		VALUES(@UserID, @FcstID, @AccessType)

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

        RETURN 0 -- Successful
END
ELSE IF @found >= 2
BEGIN
        RETURN -3 -- Error (Multiple Access Types Exists
END
ELSE IF @found = 1 and @ExistingAccessType = @AccessType
BEGIN
	RETURN 1 -- Record Already Exists
END
ELSE
BEGIN	
	UPDATE AIMForecastMainUserAccess
    	SET AccessType = @AccessType
    		WHERE UserID = @UserID and FcstID = @FcstID and AccessType = @ExistingAccessType

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END
        
	RETURN 1 -- Successful
END	

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

