IF EXISTS (SELECT * FROM dbo.sysobjects 
			WHERE ID = object_id(N'[dbo].[AIM_PopulateRepositoryActauls_Sp]') 
			AND OBJECTPROPERTY(ID, N'IsProcedure') = 1)
	DROP PROCEDURE[dbo].[AIM_PopulateRepositoryActauls_Sp]
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON>L NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_PopulateRepositoryActauls_Sp
**	Desc: Updates actualordered and actualshipped  data 
**
**	Returns: 1)  1 - Successful Update
**		 2) -1 - No Data Found
**               3) -2 - SQL Error
**             
**	Auth:   Srinivas Uddanti 
**	Date:   Oct/09/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-------------------------------------------------
**	
*******************************************************************************/
    
CREATE   PROCEDURE AIM_PopulateRepositoryActauls_Sp
(
	@RepositoryKey						bigint,
   	@LcID		        				nvarchar(12), 
   	@Item		        				nvarchar(25), 
   	@FcstPds			     			tinyint,
	@QtyActualShipped			     		int,
	@QtyActualOrdered				     	int
)	 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      						int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FcstPds IS NULL
BEGIN
	RETURN -1
END
BEGIN
	
    UPDATE ForecastRepositorydetail
    SET 
    QtyActualShipped =@QtyActualShipped,
    QtyActualOrdered = @QtyActualOrdered
    WHERE RepositoryKey = @RepositoryKey
    AND LcId = @LcId
    AND Item = @Item
    AND FcstPds = @FcstPds
	  
End

    	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

    	IF @found > 0
    	BEGIN
		RETURN 1 -- UPDATE Record Updated
    	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR No Data Found
	END



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

