/****** Object:  Table [dbo].[AIMForecastFreezePds]    Script Date: 05/30/2003 11:04:30 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMForecastFreezePds]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMForecastFreezePds]
GO

CREATE TABLE [dbo].[AIMForecastFreezePds] (
	[FcstId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[LcId] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FreezePds] [tinyint] NOT NULL ,
	CONSTRAINT [PK_AIMForecastFreezePds] PRIMARY KEY  CLUSTERED 
	(
		[FcstId],
		[LcId]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


