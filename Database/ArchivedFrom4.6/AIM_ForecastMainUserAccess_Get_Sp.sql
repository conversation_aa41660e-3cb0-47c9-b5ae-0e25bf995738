SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMainUserAccess_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMainUserAccess_Get_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastMainUserAccess_Get_Sp
**	Desc: Gets records from AIMForecastMainUserAccess
**
**	Returns: 1)@found - Can be Zero
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - AIMForecastUserAccess
**              
**	Auth:   Annalakshmi Stocksdale
**	Date:   08/29/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastMainUserAccess_Get_Sp 
(
       	@FcstID					nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END	
  ELSE
  BEGIN
	-- Make sure the title is valid.
	IF (SELECT COUNT(*) FROM AIMForecast
	WHERE FcstID = @FcstID) = 0
	
	RETURN -1
END

SELECT * FROM AIMForecastMainUserAccess
WHERE AIMForecastMainUserAccess.FcstID =  @FcstID
ORDER BY UserID

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
ELSE
BEGIN
    	RETURN @found
  END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

