SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BatchFcst_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BatchFcst_Save_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BatchFcst_Save_Sp
**	Desc: Inserts Forecast Repository Detail data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**		 3) -1 - No Data Found
**               4) -2 - SQL Error
**             
**	Auth:   Srinivas Uddanti 
**	Date:   03/06/2003
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-------------------------------------------------
**	04/24/2003 Srinivas U   Add different Update and Insert statement
** 			        Based on the FcstType
**	09/18/2003 Srinivas U   Modified the datetype from Decimal to int
			        For the arguments sysfcst,sysnet,plannedrct
*******************************************************************************/
    
CREATE PROCEDURE AIM_BatchFcst_Save_Sp
(
	@RepositoryKey						bigint,
   	@LcID		        				nvarchar(12), 
   	@Item		        				nvarchar(25), 
    @FcstPds			     			tinyint,
	@SysFcst			     			int,
	@SysNet				     			int,
	@ModSysFcst			      			int,
	@ModSysNet			     			int,
	@PlannedRct			      			int=0,
	@FcstType			    			nvarchar(25)
)	 

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      						int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FcstPds IS NULL
BEGIN
	RETURN -1
END

IF @FcstType IS NULL
BEGIN
	RETURN -1
END

SELECT @RepositoryKey = RepositoryKey FROM ForecastRepositoryBatch
WHERE RepositoryKey = @RepositoryKey
AND Item = @Item
AND LcId = @LcId
AND FcstPds = @FcstPds
        
SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0		-- Insert new Batch forecast Detail record
BEGIN
IF @FcstType ='1'
	   Begin
	      INSERT INTO ForecastRepositoryBatch
	            (RepositoryKey, LcID, Item, FcstPds,
	            sysFcst)
	            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
	            @sysFcst)
	   End
	Else 
	if @FcstType ='2'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            sysNet)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysNet)
	   End
	Else 
	if @FcstType ='3'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            ModSysFcst)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @ModSysFcst)
	   End
	Else 
	if @FcstType ='4'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @ModSysNet,@PlannedRct)
	   End
	
	Else 
	if @FcstType ='12'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,SysNet)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysFcst,@SysNet)
	   End
	Else 
	if @FcstType ='13'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,ModSysFcst)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysFcst,@ModSysFcst)
	   End

	if @FcstType ='14'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysFcst,@ModSysNet,@PlannedRct)
	   End

	if @FcstType ='23'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysNet,ModSysFcst)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysNet,@ModSysFcst)
	   End

	if @FcstType ='24'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysNet,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysNet,@ModSysNet,@PlannedRct)
	   End
	if @FcstType ='34'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            ModSysFcst,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @ModSysFcst,@ModSysNet,@PlannedRct)
	   End

	if @FcstType ='123'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,SysNet,ModSysFcst)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysFcst,@SysNet,@ModSysFcst)
	   End

	if @FcstType ='234'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysNet,ModSysFcst,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysNet,@ModSysFcst,@ModSysNet,@PlannedRct)
	   End
	if @FcstType ='134'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,ModSysFcst,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		            @SysFcst,@ModSysFcst,@ModSysNet,@PlannedRct)
	   End

	if @FcstType ='124'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,SysNet,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		           @SysFcst,@SysNet,@ModSysNet,@PlannedRct)
	   	End
	if @FcstType ='1234'
		Begin
		      INSERT INTO ForecastRepositoryBatch
		            (RepositoryKey, LcID, Item, FcstPds,
		            SysFcst,SysNet,ModSysFcst,ModSysNet,PlannedRct)
		            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, 
		           @SysFcst,@SysNet,@ModSysFcst,@ModSysNet,@PlannedRct)
	   	End


	SELECT @found = @@rowcount

        -- Check for SQL Server errors.
        IF @@ERROR <> 0 
        BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
        END
        ELSE
	BEGIN
		RETURN 0 -- SUCCESSFUL
	END
END
ELSE			-- Update existing batch forecast Detail record
BEGIN
	If @FcstType ='1'
	   Begin
		      UPDATE ForecastRepositoryBatch
	            SET 
	            SysFcst = @SysFcst
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
	    	    AND FcstPds = @FcstPds
	   End
	Else 
        If @FcstType ='2'
	   Begin
		      UPDATE ForecastRepositoryBatch
	            SET 
	            SysNet = @SysNet
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
	    	    AND FcstPds = @FcstPds
	   End
	If @FcstType ='3'
	   Begin
		      UPDATE ForecastRepositoryBatch
	            SET 
	            ModSysFcst = @ModSysFcst
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
	    	    AND FcstPds = @FcstPds
	   End
	If @FcstType ='4'
	   Begin
		      UPDATE ForecastRepositoryBatch
	            SET 
	            ModSysNet = @ModSysNet,
		    PlannedRct =@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
	    	    AND FcstPds = @FcstPds
	   End

	Else 
	if @FcstType ='12'
		Begin


			 UPDATE ForecastRepositoryBatch
	            SET 
		    SysFcst = @SysFcst,
	            SysNet =@SysNet
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
		     
	   End
	Else 
	if @FcstType ='13'
		Begin
		     
			 UPDATE ForecastRepositoryBatch
	            SET 
		    SysFcst = @SysFcst,
	            ModSysFcst =@ModSysFcst
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End

	if @FcstType ='14'
		Begin
		       UPDATE ForecastRepositoryBatch
	            SET 
		    SysFcst = @SysFcst,
	            ModSysNet =@ModSysNet,
		    PlannedRct=@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End

	if @FcstType ='23'
		Begin
		       UPDATE ForecastRepositoryBatch
	            SET 
		    SysNet = @SysNet,
	            ModSysFcst =@ModSysFcst
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End

	if @FcstType ='24'
		Begin
		         UPDATE ForecastRepositoryBatch
	            SET 
		    SysNet = @SysNet,
	            ModSysNet =@ModSysNet,
		    PlannedRct=@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End
	if @FcstType ='34'
		Begin
		        UPDATE ForecastRepositoryBatch
	            SET 
		    ModSysFcst = @ModSysFcst,
	            ModSysNet =@ModSysNet,
	            PlannedRct=@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End

	if @FcstType ='123'
		Begin
		         UPDATE ForecastRepositoryBatch
	            SET 
		    SysFcst =@SysFcst,
		    SysNet = @SysNet,
	            ModSysFcst =@ModSysFcst
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End

	if @FcstType ='234'
		Begin
		         UPDATE ForecastRepositoryBatch
	            SET 
		    SysNet =@SysNet,
		    ModSysFcst = @ModSysFcst,
	            ModSysNet =@ModSysNet,
		    PlannedRct=@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End
	if @FcstType ='134'
		Begin
		        UPDATE ForecastRepositoryBatch
	            SET
		    SysFcst =@SysFcst, 
		    ModSysFcst = @ModSysFcst,
	            ModSysNet =@ModSysNet,
		    PlannedRct=@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   End

	if @FcstType ='124'
		Begin
		          UPDATE ForecastRepositoryBatch
	            SET 
		    SysFcst =@SysFcst,
		    SysNet =@SysNet,
	            ModSysNet =@ModSysNet,
		    PlannedRct=@PlannedRct
		    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   	End
	if @FcstType ='1234'
		Begin
		            UPDATE ForecastRepositoryBatch
	            SET 
		    SysFcst =@SysFcst,
		    SysNet =@SysNet,
		    ModSysFcst =@ModSysFcst,
		    ModSysNet =@ModSysnet,
		    PlannedRct=@PlannedRct
	    	    WHERE RepositoryKey = @RepositoryKey
	            AND LcId = @LcId
	    	    AND Item = @Item
		    AND FcstPds = @FcstPds
	   	End

    	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

    	IF @found > 0
    	BEGIN
		RETURN 1 -- UPDATE Record Updated
    	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR No Data Found
	END
END

GO

