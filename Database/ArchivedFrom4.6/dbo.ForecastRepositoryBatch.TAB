/****** Object:  Table [dbo].[ForecastRepositoryBatch]    Script Date: 05/30/2003 11:04:42 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepositoryBatch]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepositoryBatch]
GO

CREATE TABLE [dbo].[ForecastRepositoryBatch] (
	[RepositoryKey] [numeric](18, 0) NOT NULL ,
	[LcID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Item] [nvarchar] (25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstPds] [tinyint] NOT NULL ,
	[SysFcst] [int] NULL ,
	[SysNet] [int] NULL ,
	[ModSysFcst] [int] NULL ,
	[ModSysNet] [int] NULL ,
	[PlannedRct] [int] NULL ,
	CONSTRAINT [PK_ForecastRepositoryBatch] PRIMARY KEY  CLUSTERED 
	(
		[RepositoryKey],
		[LcID],
		[Item],
		[FcstPds]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


