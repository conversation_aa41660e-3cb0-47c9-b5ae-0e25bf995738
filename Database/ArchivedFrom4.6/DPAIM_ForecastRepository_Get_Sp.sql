SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_ForecastRepository_Get_Sp    Script Date: 10/12/2004 10:54:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_ForecastRepository_Get_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_ForecastRepository_Get_Sp]
GO















/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  TH<PERSON> SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: DPAIM_ForecastRepository_Get_Sp
**	Desc: Gets Forecast Repository data
**
**	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - Forecast Repository,
**             Recordset - Forecast Repository Detail
**              
**	Auth:   Wade Riza 
**	Date:   08/01/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza	Update Return Codes and Error handling
**      09/27/2002 Wade Riza	Updated SP for two new columns 
**                              (FcstPdStartDate & QtyProjectedInventory) 
*******************************************************************************/

CREATE              PROCEDURE DPAIM_ForecastRepository_Get_Sp
(
 	@RepositoryKey 	        numeric(18,0),       
       	@LcID     		nvarchar(12),
        @Item   		nvarchar(25),
	@PdStartDate		datetime,
	@PdEndDate		datetime
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

--Refresh the Adjustment from the adj log into the ForecastReposiotyDetail table
--Clear any old adjustments and Populate with new adjustment
--exec DPAIM_ForecastRepository_RefreshAdj_Sp @RepositoryKey

SELECT RepositoryKey, LcID, Item, FcstPdBegDate,FcstPdEndDate,
COALESCE(Fcst,0) Fcst,COALESCE(FcstAdj,0) FcstAdj,
COALESCE(FcstNetReq,0) FcstNetReq,COALESCE(FcstAdjNetReq,0) FcstAdjNetReq,
COALESCE(QtyAdj,0)QtyAdj,COALESCE(QtyAdjOverRide,0) QtyAdjOverRide,
QtyAdjPct,
AdjOverRide,
MasterQtyAdj,
MasterAdjOverRide,
MasterQtyAdjPct,
COALESCE(MasterQtyAdjOverRide,0) MasterQtyAdjOverRide,
COALESCE(HistDmd,0) HistDmd,
COALESCE(QtyActualShipped,0) QtyActualShipped,
COALESCE(QtyProjectedInventory,0) QtyProjectedInventory
FROM ForecastRepositoryDetail 
WHERE 
RepositoryKey =@RepositoryKey AND
LcID = @LcID and 
Item = @Item and
FcstPdBegDate >=@PdStartDate and
FcstPdBegdate <=@PdEndDate
ORDER BY FcstPdBegDate

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0
BEGIN
	RETURN 0  -- SUCCESSFUL
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END		















GO

SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

