SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMForecast_GetLocations_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMForecast_GetLocations_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMForecast_GetLocations_Sp
**	Desc: Gets the Locations for a Forecast based on it's Selection Criteria
**
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**	Values:  Recordset - LcID, LName
**              
**	Auth:   Wade Riza 
**	Date:   08/13/2002
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   Author:	     Description:
**  ---------- ------------- -----------------------------------------------
**  08/16/2002 Wade Riza     Update Return Codes and Error Handling
**  09/24/2002 Wade Riza	 Updated Class Codes to NVARCHAR(50)
**  09/25/2002 Wade Riza	 Removed hardcoded SAVersion data 
*******************************************************************************/

CREATE PROCEDURE AIM_AIMForecast_GetLocations_Sp
(
     @FcstID						nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found					int,
	@Item						nvarchar(25),
	@VnID						nvarchar(12),
	@Assort						nvarchar(12),
	@LcID						nvarchar(12),
	@Class1						nvarchar(50),
	@Class2						nvarchar(50),
	@Class3						nvarchar(50),
	@Class4						nvarchar(50),
	@ByID						nvarchar(12),
	@SQL						nvarchar(4000),
	@SQLWHERE					nvarchar(4000),
	@SQLWHERELEN					int,
	@rtrn						int	

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
	RETURN -1  -- ERROR (No Valid Forecast ID) 
ELSE
BEGIN
	SELECT @Item = Item, @VnID = VnID, @Assort = Assort, @LcID = LcID,
	@Class1 = Class1, @Class2 = Class2, @Class3 = Class3, @Class4 = Class4 FROM AIMForecast
	WHERE FcstID = @FcstID

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	IF @found <= 0 
	BEGIN	
		RETURN -1 -- ERROR (No Valid data found in Database)
	END
END
BEGIN
	EXEC @rtrn = AIM_GetSaVersion_Sp 

	SELECT @rtrn

	IF @rtrn IS NULL
	BEGIN
		SET @rtrn = 1
	END	
END

SET @SQL = 'SELECT DISTINCT(Item.LcID), AIMLocations.LName From Item '  
SET @SQL = @SQL + 'INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId ' 
SET @SQL = @SQL + 'INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId AND AIMSeasons.SAVersion = '
SET @SQL = @SQL + '''' + STR(@rtrn) + ''''  
SET @SQL = @SQL + 'INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat '
SET @SQL = @SQL + 'INNER JOIN AIMLocations on Item.Lcid = AIMLocations.Lcid ' 
SET @SQL = @SQL + 'INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId AND Item.Assort = AIMVendors.Assort '
SET @SQL = @SQL + 'INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle '
SET @SQL = @SQL + 'LEFT OUTER JOIN AIMPromotions ON Item.PmId = AIMPromotions.PmId '
SET @SQL = @SQL + 'INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodId '

IF @Item IS NULL AND @VnID IS NULL AND @ASSORT IS NULL AND @LcID IS NULL AND
	@Class1 IS NULL AND @Class2 IS NULL AND @Class3 IS NULL AND @Class4 IS NULL
BEGIN
	EXEC(@SQL)

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

	IF @found > 0 
	BEGIN
		RETURN @found
	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR  (No Item Data found)
	END
END
ELSE
BEGIN
	SET @SQLWHERE = ' WHERE ItStatus.DmdUpd = ''Y'' AND '

	IF @Item IS NOT NULL AND LEN(@Item) <> 0
	BEGIN
		SET @SQLWHERE = @SQLWHERE + 'Item.Item = ''' + @Item + ''' AND '
	END
	
	IF @VnID IS NOT NULL AND LEN(@VnID) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.VnID = ''' + @VnID + ''' AND '
	END
 
	IF @Assort IS NOT NULL AND LEN(@Assort) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.Assort = ''' + @Assort + ''' AND '
	END
			
	IF @LcID IS NOT NULL AND LEN(@LcID) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.LcID = ''' + @LcID + ''' AND '
	END

	IF @Class1 IS NOT NULL AND LEN(@Class1) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.Class1 = ''' + @Class1 + ''' AND '
	END
 
	IF @Class2 IS NOT NULL AND LEN(@Class2) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.Class2 = ''' + @Class2 + ''' AND '
	END

	IF @Class3 IS NOT NULL AND LEN(@Class3) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.Class3 = ''' + @Class3 + ''' AND '
	END

	IF @Class4 IS NOT NULL AND LEN(@Class4) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.Class4 = ''' + @Class4 + ''' AND '
	END

	IF @ByID IS NOT NULL AND LEN(@ByID) <> 0
	BEGIN 
		SET @SQLWHERE = @SQLWHERE + 'Item.ByID = ''' + @ByID + ''' AND '
	END

	SELECT @SQLWHERELEN = LEN(@SQLWHERE)

	SET @SQLWHERELEN = @SQLWHERELEN - 4

	SET @SQLWHERE = LEFT(@SQLWHERE, @SQLWHERELEN)

	SET @SQL = @SQL + @SQLWHERE

	EXEC(@SQL)

	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
   		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	ELSE
    	BEGIN
	    IF @found > 0 
		RETURN @found  -- SUCCESSFUL
	    ELSE
		RETURN -1  -- ERROR (NO DATA FOUND)
	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

