SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMYears_GetEq_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMYears_GetEq_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMYears_GetEq_Sp
**	Desc: Retrieves the AIMYears data based on the Fiscal Year
**
**	Returns: 1)  @rowcount - Successful
**
**	Values:  Recordset - AIMYears
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**  Date:	   	Author:	    	Description:
**  ---------- 	------------	----------------------------------------------
**  2004/01/27	A.Stocksdale	This stored procedure is no longer required. 
**								Moved the where clause to AIM_AIMYears_Load_Sp. 
*******************************************************************************/     
  
CREATE PROCEDURE AIM_AIMYears_GetEq_Sp
(
        @FiscalYear int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
BEGIN
  
	SET NOCOUNT ON
	
	SELECT 
		FiscalYear,
		FYStartDate,
		FYEndDate,
		NbrWeeks
		FROM AIMYears 
	WHERE FiscalYear = @FiscalYear
	        
	RETURN @@ROWCOUNT

END
GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

