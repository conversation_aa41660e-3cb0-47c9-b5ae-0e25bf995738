/****** Object:  Table [dbo].[AIMForecastMainUserAccess]    Script Date: 05/30/2003 11:04:31 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIMForecastMainUserAccess]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AIMForecastMainUserAccess]
GO

CREATE TABLE [dbo].[AIMForecastMainUserAccess] (
	[UserID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[AccessType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	CONSTRAINT [PK_AIMForecastMainUserAccess] PRIMARY KEY  CLUSTERED 
	(
		[UserID],
		[FcstID]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


