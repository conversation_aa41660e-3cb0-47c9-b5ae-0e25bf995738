SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_BldHistoryfmSD_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_BldHistoryfmSD_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_BldHistoryfmSD_Sp
**	Desc: Creates Item History from Item Default values
**
**	Returns: 1)  @rowcount - Successful
**
**	Values:  Recordset - Items
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:	Description:
**    ---------- ------------	-----------------------------------------------
**  	
*******************************************************************************/       

CREATE PROCEDURE AIM_BldHistoryfmSD_Sp
(
  	@LcIdKey    						nvarchar(12),
    	@StartDate  						datetime,
    	@EndDate   						datetime
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE 
	@txndate        					smalldatetime,
        @lcid           					nvarchar(12),
        @item           					nvarchar(25),
	@SubsItem						nvarchar(25),
        @qtyord         					int,
        @cps            					int,
        @ordcnt         					int,
            
        @fy             					int,
        @found          					int,
        @lcid_f        						nvarchar(12),
        @pd             					int,
        @FYStartDate    					datetime,
        
	-- Item History Table
        @CPS01                      				decimal(9, 1),
        @CPS02                      				decimal(9, 1),
        @CPS03                      				decimal(9, 1),
        @CPS04                      				decimal(9, 1),
        @CPS05                      				decimal(9, 1),
        @CPS06                      				decimal(9, 1),
        @CPS07                      				decimal(9, 1),
        @CPS08                      				decimal(9, 1),
        @CPS09                      				decimal(9, 1),
        @CPS10                      				decimal(9, 1),
        @CPS11                      				decimal(9, 1),
        @CPS12                      				decimal(9, 1),
        @CPS13                      				decimal(9, 1),
        @CPS14                      				decimal(9, 1),
        @CPS15                      				decimal(9, 1),
        @CPS16                      				decimal(9, 1),
        @CPS17                      				decimal(9, 1),
        @CPS18                      				decimal(9, 1),
        @CPS19                      				decimal(9, 1),
        @CPS20                      				decimal(9, 1),
        @CPS21                      				decimal(9, 1),
        @CPS22                      				decimal(9, 1),
        @CPS23                      				decimal(9, 1),
        @CPS24                      				decimal(9, 1),
        @CPS25                      				decimal(9, 1),
        @CPS26                      				decimal(9, 1),
        @CPS27                      				decimal(9, 1),
        @CPS28                      				decimal(9, 1),
        @CPS29                      				decimal(9, 1),
        @CPS30                      				decimal(9, 1),
        @CPS31                      				decimal(9, 1),
        @CPS32                      				decimal(9, 1),
        @CPS33                      				decimal(9, 1),
        @CPS34                      				decimal(9, 1),
        @CPS35                      				decimal(9, 1),
        @CPS36                      				decimal(9, 1),
        @CPS37                      				decimal(9, 1),
        @CPS38                      				decimal(9, 1),
        @CPS39                      				decimal(9, 1),
        @CPS40                      				decimal(9, 1),
        @CPS41                      				decimal(9, 1),
        @CPS42                      				decimal(9, 1),
        @CPS43                      				decimal(9, 1),
        @CPS44                      				decimal(9, 1),
        @CPS45                      				decimal(9, 1),
        @CPS46                      				decimal(9, 1), 
        @CPS47                      				decimal(9, 1), 
        @CPS48                      				decimal(9, 1), 
        @CPS49                      				decimal(9, 1), 
        @CPS50                      				decimal(9, 1), 
        @CPS51                      				decimal(9, 1), 
        @CPS52                      				decimal(9, 1), 
        @QtyOrd01                  				decimal(9, 1),
        @QtyOrd02                  				decimal(9, 1),
        @QtyOrd03                  				decimal(9, 1),
        @QtyOrd04                  				decimal(9, 1),
        @QtyOrd05                  				decimal(9, 1),
        @QtyOrd06                  				decimal(9, 1),
        @QtyOrd07                  				decimal(9, 1),
        @QtyOrd08                  				decimal(9, 1),
        @QtyOrd09                  				decimal(9, 1),
        @QtyOrd10                  				decimal(9, 1),
        @QtyOrd11                  				decimal(9, 1),
        @QtyOrd12                  				decimal(9, 1),
        @QtyOrd13                  				decimal(9, 1),
        @QtyOrd14                  				decimal(9, 1),
        @QtyOrd15                  				decimal(9, 1),
        @QtyOrd16                  				decimal(9, 1),
        @QtyOrd17                  				decimal(9, 1),
        @QtyOrd18                  				decimal(9, 1),
        @QtyOrd19                  				decimal(9, 1),
        @QtyOrd20                  				decimal(9, 1),
        @QtyOrd21                  				decimal(9, 1),
        @QtyOrd22                  				decimal(9, 1),
        @QtyOrd23                  				decimal(9, 1),
        @QtyOrd24                  				decimal(9, 1),
        @QtyOrd25                  				decimal(9, 1),
        @QtyOrd26                  				decimal(9, 1),
        @QtyOrd27                  				decimal(9, 1),
        @QtyOrd28                  				decimal(9, 1),
        @QtyOrd29                  				decimal(9, 1),
        @QtyOrd30                  				decimal(9, 1),
        @QtyOrd31                  				decimal(9, 1),
        @QtyOrd32                  				decimal(9, 1),
        @QtyOrd33                  				decimal(9, 1),
        @QtyOrd34                  				decimal(9, 1),
        @QtyOrd35                  				decimal(9, 1),
        @QtyOrd36                  				decimal(9, 1),
        @QtyOrd37                  				decimal(9, 1),
        @QtyOrd38                  				decimal(9, 1),
        @QtyOrd39                  				decimal(9, 1),
        @QtyOrd40                  				decimal(9, 1),
        @QtyOrd41                  				decimal(9, 1),
        @QtyOrd42                  				decimal(9, 1),
        @QtyOrd43                  				decimal(9, 1),
        @QtyOrd44                  				decimal(9, 1),
        @QtyOrd45                  				decimal(9, 1),
        @QtyOrd46                  				decimal(9, 1),
        @QtyOrd47                  				decimal(9, 1),
        @QtyOrd48                  				decimal(9, 1),
        @QtyOrd49                  				decimal(9, 1),
        @QtyOrd50                  				decimal(9, 1),
        @QtyOrd51                  				decimal(9, 1),
        @QtyOrd52                  				decimal(9, 1),
        @OrdCnt01                  				int,
        @OrdCnt02                  				int,
        @OrdCnt03                  				int,
        @OrdCnt04                  				int,
        @OrdCnt05                  				int,
        @OrdCnt06                  				int,
        @OrdCnt07                  				int,
        @OrdCnt08                  				int,
        @OrdCnt09                  				int,
        @OrdCnt10                  				int,
        @OrdCnt11                  				int,
        @OrdCnt12                  				int,
        @OrdCnt13                  				int,
        @OrdCnt14                  				int,
        @OrdCnt15                  				int,
        @OrdCnt16                  				int,
        @OrdCnt17                 				int,
        @OrdCnt18             					int,
        @OrdCnt19                 				int,
        @OrdCnt20                 				int,
        @OrdCnt21                  				int,
        @OrdCnt22                  				int,
        @OrdCnt23                  				int,
        @OrdCnt24                 	 			int,
        @OrdCnt25                  				int,
        @OrdCnt26                  				int,
        @OrdCnt27                 				int,
        @OrdCnt28                 				int,
        @OrdCnt29                  				int,
        @OrdCnt30                 				int,
        @OrdCnt31                  				int,
        @OrdCnt32                  				int,
        @OrdCnt33                  				int,
        @OrdCnt34                 	 			int,
        @OrdCnt35                  				int,
        @OrdCnt36                  				int,
        @OrdCnt37                  				int,
        @OrdCnt38                  				int,
        @OrdCnt39                  				int,
        @OrdCnt40                  				int,
        @OrdCnt41                  				int,
        @OrdCnt42                  				int,
        @OrdCnt43                  				int,
        @OrdCnt44                  				int,
        @OrdCnt45                  				int,
        @OrdCnt46                  				int,
        @OrdCnt47                  				int,
        @OrdCnt48                  				int,
        @OrdCnt49                  				int,
        @OrdCnt50                  				int,
        @OrdCnt51                  				int,
        @OrdCnt52                  				int
            
SET NOCOUNT ON
 
DECLARE ItemList CURSOR LOCAL FAST_FORWARD FOR
	SELECT txndate, lcid, item,subsitem =CASE WHEN (SubsItem ='')THEN item  ElSE  SubsItem END , qtyord = ordqty, shpqty = shpqty, OrdCnt = 1
        FROM AIMSd
        WHERE LcId = @LcIdKey
        AND txndate BETWEEN @StartDate AND @EndDate
     	AND doctype = 'SO' AND txn_type IN ('IR', 'IA')
  
OPEN ItemList
WHILE 1 = 1
BEGIN
  	FETCH NEXT FROM ItemList 
	INTO @txndate, @lcid, @item,@subsitem, @qtyord, @cps, @ordcnt
        
        IF @@FETCH_STATUS <> 0
        BREAK
        -- Get the Fiscal Year
	--WGR - fix this        
        --SELECT @fy = case  
        --    WHEN @txndate between '1/1/1996' and '12/31/1996' THEN 1996
        --    WHEN @txndate between '1/1/1997' and '12/31/1997' THEN 1997
        --    WHEN @txndate between '1/1/1998' and '12/31/1998' THEN 1998
        --    WHEN @txndate between '1/1/1999' and '12/31/1999' THEN 1999
        --    WHEN @txndate between '1/1/2000' and '12/31/2000' THEN 2000
        --    WHEN @txndate between '1/1/2001' and '12/31/2001' THEN 2001
        --    END,
        --    @FYStartDate = case
        --    WHEN @txndate between '1/1/1996' and '12/31/1996' THEN '1/1/1996'
        --    WHEN @txndate between '1/1/1997' and '12/31/1997' THEN '1/1/1997'
        --    WHEN @txndate between '1/1/1998' and '12/31/1998' THEN '1/1/1998'
        --    WHEN @txndate between '1/1/1999' and '12/31/1999' THEN '1/1/1999'
        --    WHEN @txndate between '1/1/2000' and '12/31/2000' THEN '1/1/2000'
        --    WHEN @txndate between '1/1/2001' and '12/31/2001' THEN '1/1/2001'
        --    END
        
        ---- Calculate the period
        --SELECT @Pd = (DateDIFf(dd, @FYStartDate, @txndate) / 7) + 1
        --IF @Pd > 52 
        --    SELECT @Pd = 52
        
        EXEC @pd = AIM_GetPd_Sp @txndate, @fy OUTPUT
	--If SubsItem is null set it to Item value
	Select @Subsitem = isnull( @Subsitem,@Item)
        
        -- Is this item already in the Item History Table
        SELECT @lcid_f = lcid
	FROM ItemHistory
        WHERE lcid = @lcid 
        AND item = @item
	and Subsitem =@SubsItem       
        AND HisYear = @fy
            
        SELECT @found = @@rowcount
        
        IF @found = 0       -- Not in file; insert record
        BEGIN
        
            -- Inititalize variables
            SELECT @CPS01 = 0, @CPS02 = 0, @CPS03 = 0, @CPS04 = 0, @CPS05 = 0, @CPS06 = 0, 
                @CPS07 = 0, @CPS08 = 0, @CPS09 = 0, @CPS10 = 0, @CPS11 = 0, @CPS12 = 0, 
                @CPS13 = 0, @CPS14 = 0, @CPS15 = 0, @CPS16 = 0, @CPS17 = 0, @CPS18 = 0,                 @CPS19 = 0, @CPS20 = 0, @CPS21 = 0, @CPS22 = 0, @CPS23 = 0, @CPS24 = 0, 
                @CPS25 = 0, @CPS26 = 0, @CPS27 = 0, @CPS28 = 0, @CPS29 = 0, @CPS30 = 0, 
                @CPS31 = 0, @CPS32 = 0, @CPS33 = 0, @CPS34 = 0, @CPS35 = 0, @CPS36 = 0, 
                @CPS37 = 0, @CPS38 = 0, @CPS39 = 0, @CPS40 = 0, @CPS41 = 0, @CPS42 = 0, 
                @CPS43 = 0, @CPS44 = 0, @CPS45 = 0, @CPS46 = 0, @CPS47 = 0, @CPS48 = 0, 
                @CPS49 = 0, @CPS50 = 0, @CPS51 = 0, @CPS52 = 0, 
                @QtyOrd01 = 0, @QtyOrd02 = 0, @QtyOrd03 = 0, @QtyOrd04 = 0, @QtyOrd05 = 0, @QtyOrd06 = 0, 
                @QtyOrd07 = 0, @QtyOrd08 = 0, @QtyOrd09 = 0, @QtyOrd10 = 0, @QtyOrd11 = 0, @QtyOrd12 = 0, 
                @QtyOrd13 = 0, @QtyOrd14 = 0, @QtyOrd15 = 0, @QtyOrd16 = 0, @QtyOrd17 = 0, @QtyOrd18 = 0, 
                @QtyOrd19 = 0, @QtyOrd20 = 0, @QtyOrd21 = 0, @QtyOrd22 = 0, @QtyOrd23 = 0, @QtyOrd24 = 0, 
                @QtyOrd25 = 0, @QtyOrd26 = 0, @QtyOrd27 = 0, @QtyOrd28 = 0, @QtyOrd29 = 0, @QtyOrd30 = 0, 
                @QtyOrd31 = 0, @QtyOrd32 = 0, @QtyOrd33 = 0, @QtyOrd34 = 0, @QtyOrd35 = 0, @QtyOrd36 = 0, 
                @QtyOrd37 = 0, @QtyOrd38 = 0, @QtyOrd39 = 0, @QtyOrd40 = 0, @QtyOrd41 = 0, @QtyOrd42 = 0, 
                @QtyOrd43 = 0, @QtyOrd44 = 0, @QtyOrd45 = 0, @QtyOrd46 = 0, @QtyOrd47 = 0, @QtyOrd48 = 0, 
                @QtyOrd49 = 0, @QtyOrd50 = 0, @QtyOrd51 = 0, @QtyOrd52 = 0, 
                @OrdCnt01 = 0, @OrdCnt02 = 0, @OrdCnt03 = 0, @OrdCnt04 = 0, @OrdCnt05 = 0, @OrdCnt06 = 0, 
                @OrdCnt07 = 0, @OrdCnt08 = 0, @OrdCnt09 = 0, @OrdCnt10 = 0, @OrdCnt11 = 0, @OrdCnt12 = 0, 
                @OrdCnt13 = 0, @OrdCnt14 = 0, @OrdCnt15 = 0, @OrdCnt16 = 0, @OrdCnt17 = 0, @OrdCnt18 = 0, 
                @OrdCnt19 = 0, @OrdCnt20 = 0, @OrdCnt21 = 0, @OrdCnt22 = 0, @OrdCnt23 = 0, @OrdCnt24 = 0, 
                @OrdCnt25 = 0, @OrdCnt26 = 0, @OrdCnt27 = 0, @OrdCnt28 = 0, @OrdCnt29 = 0, @OrdCnt30 = 0, 
                @OrdCnt31 = 0, @OrdCnt32 = 0, @OrdCnt33 = 0, @OrdCnt34 = 0, @OrdCnt35 = 0, @OrdCnt36 = 0, 
                @OrdCnt37 = 0, @OrdCnt38 = 0, @OrdCnt39 = 0, @OrdCnt40 = 0, @OrdCnt41 = 0, @OrdCnt42 = 0, 
                @OrdCnt43 = 0, @OrdCnt44 = 0, @OrdCnt45 = 0, @OrdCnt46 = 0, @OrdCnt47 = 0, @OrdCnt48 = 0, 
                @OrdCnt49 = 0, @OrdCnt50 = 0, @OrdCnt51 = 0, @OrdCnt52 = 0 
            IF @pd = 1
                SELECT @CPS01 = @cps, @QtyOrd01 = @qtyord, @OrdCnt01 = @ordcnt
            IF @pd = 2
                SELECT @CPS02 = @cps, @QtyOrd02 = @qtyord, @OrdCnt02 = @ordcnt
            IF @pd = 3
                SELECT @CPS03 = @cps, @QtyOrd03 = @qtyord, @OrdCnt03 = @ordcnt
            IF @pd = 4
                SELECT @CPS04 = @cps, @QtyOrd04 = @qtyord, @OrdCnt04 = @ordcnt
            IF @pd = 5
                SELECT @CPS05 = @cps, @QtyOrd05 = @qtyord, @OrdCnt05 = @ordcnt
            IF @pd = 6
                SELECT @CPS06 = @cps, @QtyOrd06 = @qtyord, @OrdCnt06 = @ordcnt
            IF @pd = 7
                SELECT @CPS07 = @cps, @QtyOrd07 = @qtyord, @OrdCnt07 = @ordcnt
            IF @pd = 8
                SELECT @CPS08 = @cps, @QtyOrd08 = @qtyord, @OrdCnt08 = @ordcnt
            IF @pd = 9
                SELECT @CPS09 = @cps, @QtyOrd09 = @qtyord, @OrdCnt09 = @ordcnt
            IF @pd = 10
                SELECT @CPS10 = @cps, @QtyOrd10 = @qtyord, @OrdCnt10 = @ordcnt
            IF @pd = 11
                SELECT @CPS11 = @cps, @QtyOrd11 = @qtyord, @OrdCnt11 = @ordcnt
            IF @pd = 12
                SELECT @CPS12 = @cps, @QtyOrd12 = @qtyord, @OrdCnt12 = @ordcnt
            IF @pd = 13
                SELECT @CPS13 = @cps, @QtyOrd13 = @qtyord, @OrdCnt13 = @ordcnt
            IF @pd = 14
                SELECT @CPS14 = @cps, @QtyOrd14 = @qtyord, @OrdCnt14 = @ordcnt
            IF @pd = 15
                SELECT @CPS15 = @cps, @QtyOrd15 = @qtyord, @OrdCnt15 = @ordcnt
            IF @pd = 16
                SELECT @CPS16 = @cps, @QtyOrd16 = @qtyord, @OrdCnt16 = @ordcnt
            IF @pd = 17
                SELECT @CPS17 = @cps, @QtyOrd17 = @qtyord, @OrdCnt17 = @ordcnt
            IF @pd = 18
                SELECT @CPS18 = @cps, @QtyOrd18 = @qtyord, @OrdCnt18 = @ordcnt
            IF @pd = 19
                SELECT @CPS19 = @cps, @QtyOrd19 = @qtyord, @OrdCnt19 = @ordcnt
            IF @pd = 20
                SELECT @CPS20 = @cps, @QtyOrd20 = @qtyord, @OrdCnt20 = @ordcnt
            IF @pd = 21
                SELECT @CPS21 = @cps, @QtyOrd21 = @qtyord, @OrdCnt21 = @ordcnt
            IF @pd = 22
                SELECT @CPS22 = @cps, @QtyOrd22 = @qtyord, @OrdCnt22 = @ordcnt
            IF @pd = 23
                SELECT @CPS23 = @cps, @QtyOrd23 = @qtyord, @OrdCnt23 = @ordcnt
            IF @pd = 24
                SELECT @CPS24 = @cps, @QtyOrd24 = @qtyord, @OrdCnt24 = @ordcnt
            IF @pd = 25
                SELECT @CPS25 = @cps, @QtyOrd25 = @qtyord, @OrdCnt25 = @ordcnt
            IF @pd = 26
                SELECT @CPS26 = @cps, @QtyOrd26 = @qtyord, @OrdCnt26 = @ordcnt
            IF @pd = 27
                SELECT @CPS27 = @cps, @QtyOrd27 = @qtyord, @OrdCnt27 = @ordcnt
            IF @pd = 28
                SELECT @CPS28 = @cps, @QtyOrd28 = @qtyord, @OrdCnt28 = @ordcnt
            IF @pd = 29
                SELECT @CPS29 = @cps, @QtyOrd29 = @qtyord, @OrdCnt29 = @ordcnt
            IF @pd = 30
                SELECT @CPS30 = @cps, @QtyOrd30 = @qtyord, @OrdCnt30 = @ordcnt
            IF @pd = 31
                SELECT @CPS31 = @cps, @QtyOrd31 = @qtyord, @OrdCnt31 = @ordcnt
            IF @pd = 32
                SELECT @CPS32 = @cps, @QtyOrd32 = @qtyord, @OrdCnt32 = @ordcnt
            IF @pd = 33
                SELECT @CPS33 = @cps, @QtyOrd33 = @qtyord, @OrdCnt33 = @ordcnt
            IF @pd = 34
                SELECT @CPS34 = @cps, @QtyOrd34 = @qtyord, @OrdCnt34 = @ordcnt
            IF @pd = 35
                SELECT @CPS35 = @cps, @QtyOrd35 = @qtyord, @OrdCnt35 = @ordcnt
            IF @pd = 36
                SELECT @CPS36 = @cps, @QtyOrd36 = @qtyord, @OrdCnt36 = @ordcnt
            IF @pd = 37
                SELECT @CPS37 = @cps, @QtyOrd37 = @qtyord, @OrdCnt37 = @ordcnt
            IF @pd = 38
                SELECT @CPS38 = @cps, @QtyOrd38 = @qtyord, @OrdCnt38 = @ordcnt
            IF @pd = 39
                SELECT @CPS39 = @cps, @QtyOrd39 = @qtyord, @OrdCnt39 = @ordcnt
            IF @pd = 40
                SELECT @CPS40 = @cps, @QtyOrd40 = @qtyord, @OrdCnt40 = @ordcnt
            IF @pd = 41
                SELECT @CPS41 = @cps, @QtyOrd41 = @qtyord, @OrdCnt41 = @ordcnt
            IF @pd = 42
                SELECT @CPS42 = @cps, @QtyOrd42 = @qtyord, @OrdCnt42 = @ordcnt
            IF @pd = 43
                SELECT @CPS43 = @cps, @QtyOrd43 = @qtyord, @OrdCnt43 = @ordcnt
            IF @pd = 44
                SELECT @CPS44 = @cps, @QtyOrd44 = @qtyord, @OrdCnt44 = @ordcnt
            IF @pd = 45
                SELECT @CPS45 = @cps, @QtyOrd45 = @qtyord, @OrdCnt45 = @ordcnt
            IF @pd = 46
                SELECT @CPS46 = @cps, @QtyOrd46 = @qtyord, @OrdCnt46 = @ordcnt
            IF @pd = 47
                SELECT @CPS47 = @cps, @QtyOrd47 = @qtyord, @OrdCnt47 = @ordcnt
            IF @pd = 48
                SELECT @CPS48 = @cps, @QtyOrd48 = @qtyord, @OrdCnt48 = @ordcnt
            IF @pd = 49
                SELECT @CPS49 = @cps, @QtyOrd49 = @qtyord, @OrdCnt49 = @ordcnt
            IF @pd = 50
                SELECT @CPS50 = @cps, @QtyOrd50 = @qtyord, @OrdCnt50 = @ordcnt
            IF @pd = 51
                SELECT @CPS51 = @cps, @QtyOrd51 = @qtyord, @OrdCnt51 = @ordcnt
            IF @pd = 52
                SELECT @CPS52 = @cps, @QtyOrd52 = @qtyord, @OrdCnt52 = @ordcnt

            INSERT INTO ItemHistory
                (LcId, Item,subsitem, HisYear, 
                CPS01, CPS02, CPS03, CPS04, CPS05, CPS06, CPS07, CPS08, 
                CPS09, CPS10, CPS11, CPS12, CPS13, CPS14, CPS15, CPS16, 
                CPS17, CPS18, CPS19, CPS20, CPS21, CPS22, CPS23, CPS24, 
                CPS25, CPS26, CPS27, CPS28, CPS29, CPS30, CPS31, CPS32, 
                CPS33, CPS34, CPS35, CPS36, CPS37, CPS38, CPS39, CPS40, 
                CPS41, CPS42, CPS43, CPS44, CPS45, CPS46, CPS47, CPS48, 
                CPS49, CPS50, CPS51, CPS52, 
                QtyOrd01, QtyOrd02, QtyOrd03, QtyOrd04, QtyOrd05, QtyOrd06, 
                QtyOrd07, QtyOrd08, QtyOrd09, QtyOrd10, QtyOrd11, QtyOrd12, 
                QtyOrd13, QtyOrd14, QtyOrd15, QtyOrd16, QtyOrd17, QtyOrd18, 
                QtyOrd19, QtyOrd20, QtyOrd21, QtyOrd22, QtyOrd23, QtyOrd24, 
                QtyOrd25, QtyOrd26, QtyOrd27, QtyOrd28, QtyOrd29, QtyOrd30, 
                QtyOrd31, QtyOrd32, QtyOrd33, QtyOrd34, QtyOrd35, QtyOrd36, 
                QtyOrd37, QtyOrd38, QtyOrd39, QtyOrd40, QtyOrd41, QtyOrd42, 
                QtyOrd43, QtyOrd44, QtyOrd45, QtyOrd46, QtyOrd47, QtyOrd48, 
                QtyOrd49, QtyOrd50, QtyOrd51, QtyOrd52, 
                OrdCnt01, OrdCnt02, OrdCnt03, OrdCnt04, OrdCnt05, OrdCnt06, 
                OrdCnt07, OrdCnt08, OrdCnt09, OrdCnt10, OrdCnt11, OrdCnt12, 
                OrdCnt13, OrdCnt14, OrdCnt15, OrdCnt16, OrdCnt17, OrdCnt18, 
                OrdCnt19, OrdCnt20, OrdCnt21, OrdCnt22, OrdCnt23, OrdCnt24, 
                OrdCnt25, OrdCnt26, OrdCnt27, OrdCnt28, OrdCnt29, OrdCnt30, 
                OrdCnt31, OrdCnt32, OrdCnt33, OrdCnt34, OrdCnt35, OrdCnt36, 
                OrdCnt37, OrdCnt38, OrdCnt39, OrdCnt40, OrdCnt41, OrdCnt42, 
                OrdCnt43, OrdCnt44, OrdCnt45, OrdCnt46, OrdCnt47, OrdCnt48, 
                OrdCnt49, OrdCnt50, OrdCnt51, OrdCnt52) 
                VALUES 
                (@lcid,@item,@subsitem,@fy,
                @CPS01, @CPS02, @CPS03, @CPS04, @CPS05, @CPS06, @CPS07, @CPS08, 
                @CPS09, @CPS10, @CPS11, @CPS12, @CPS13, @CPS14, @CPS15, @CPS16, 
                @CPS17, @CPS18, @CPS19, @CPS20, @CPS21, @CPS22, @CPS23, @CPS24, 
                @CPS25, @CPS26, @CPS27, @CPS28, @CPS29, @CPS30, @CPS31, @CPS32, 
                @CPS33, @CPS34, @CPS35, @CPS36, @CPS37, @CPS38, @CPS39, @CPS40, 
                @CPS41, @CPS42, @CPS43, @CPS44, @CPS45, @CPS46, @CPS47, @CPS48, 
                @CPS49, @CPS50, @CPS51, @CPS52, 
                @QtyOrd01, @QtyOrd02, @QtyOrd03, @QtyOrd04, @QtyOrd05, @QtyOrd06, 
                @QtyOrd07, @QtyOrd08, @QtyOrd09, @QtyOrd10, @QtyOrd11, @QtyOrd12, 
                @QtyOrd13, @QtyOrd14, @QtyOrd15, @QtyOrd16, @QtyOrd17, @QtyOrd18, 
                @QtyOrd19, @QtyOrd20, @QtyOrd21, @QtyOrd22, @QtyOrd23, @QtyOrd24, 
                @QtyOrd25, @QtyOrd26, @QtyOrd27, @QtyOrd28, @QtyOrd29, @QtyOrd30, 
                @QtyOrd31, @QtyOrd32, @QtyOrd33, @QtyOrd34, @QtyOrd35, @QtyOrd36, 
                @QtyOrd37, @QtyOrd38, @QtyOrd39, @QtyOrd40, @QtyOrd41, @QtyOrd42, 
                @QtyOrd43, @QtyOrd44, @QtyOrd45, @QtyOrd46, @QtyOrd47, @QtyOrd48, 
                @QtyOrd49, @QtyOrd50, @QtyOrd51, @QtyOrd52, 
                @OrdCnt01, @OrdCnt02, @OrdCnt03, @OrdCnt04, @OrdCnt05, @OrdCnt06, 
                @OrdCnt07, @OrdCnt08, @OrdCnt09, @OrdCnt10, @OrdCnt11, @OrdCnt12, 
                @OrdCnt13, @OrdCnt14, @OrdCnt15, @OrdCnt16, @OrdCnt17, @OrdCnt18, 
                @OrdCnt19, @OrdCnt20, @OrdCnt21, @OrdCnt22, @OrdCnt23, @OrdCnt24, 
                @OrdCnt25, @OrdCnt26, @OrdCnt27, @OrdCnt28, @OrdCnt29, @OrdCnt30, 
                @OrdCnt31, @OrdCnt32, @OrdCnt33, @OrdCnt34, @OrdCnt35, @OrdCnt36, 
                @OrdCnt37, @OrdCnt38, @OrdCnt39, @OrdCnt40, @OrdCnt41, @OrdCnt42, 
                @OrdCnt43, @OrdCnt44, @OrdCnt45, @OrdCnt46, @OrdCnt47, @OrdCnt48, 
                @OrdCnt49, @OrdCnt50, @OrdCnt51, @OrdCnt52) 
        END
        ELSE
        BEGIN
        -- Update history record
                
        	IF @pd = 1
		BEGIN
                	UPDATE ItemHistory 
			SET cps01 = cps01 + @cps,  
                    	qtyord01 = qtyord01 + @qtyord,
                    	ordcnt01 = ordcnt01 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
        		AND hisyear = @fy
                END        
            	IF @pd = 2
		BEGIN
                	UPDATE ItemHistory 
			SET cps02 = cps02 + @cps,  
                    	qtyord02 = qtyord02 + @qtyord,
                    	ordcnt02 = ordcnt02 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 3
		BEGIN
                	UPDATE ItemHistory 
			SET cps03 = cps03 + @cps,  
                    	qtyord03 = qtyord03 + @qtyord,
                    	ordcnt03 = ordcnt03 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 4
 		BEGIN
                	UPDATE ItemHistory 
			SET cps04 = cps04 + @cps,  
                    	qtyord04 = qtyord04 + @qtyord,
                    	ordcnt04 = ordcnt04 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 5
		BEGIN
                	UPDATE ItemHistory 
			SET cps05 = cps05 + @cps,  
	                qtyord05 = qtyord05 + @qtyord,
                    	ordcnt05 = ordcnt05 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 6
 		BEGIN
                	UPDATE ItemHistory 
			SET cps06 = cps06 + @cps,  
                    	qtyord06 = qtyord06 + @qtyord,
                    	ordcnt06 = ordcnt06 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 7
		BEGIN
                	UPDATE ItemHistory 
			SET cps07 = cps07 + @cps, 
                    	qtyord07 = qtyord07 + @qtyord,
                    	ordcnt07 = ordcnt07 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 8
		BEGIN
                	UPDATE ItemHistory 
			SET cps08 = cps08 + @cps,  
                    	qtyord08 = qtyord08 + @qtyord,
                    	ordcnt08 = ordcnt08 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 9
		BEGIN
                	UPDATE ItemHistory 
			SET cps09 = cps09 + @cps,  
                    	qtyord09 = qtyord09 + @qtyord,
                    	ordcnt09 = ordcnt09 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 10
		BEGIN
                	UPDATE ItemHistory 
			SET cps10 = cps10 + @cps, 
               		qtyord10 = qtyord10 + @qtyord,
                    	ordcnt10 = ordcnt10 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END 	       
            	IF @pd = 11
		BEGIN
                	UPDATE ItemHistory 
			SET cps11 = cps11 + @cps,  
                    	qtyord11 = qtyord11 + @qtyord,
                    	ordcnt11 = ordcnt11 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 12
		BEGIN
                	UPDATE ItemHistory 
			SET cps12 = cps12 + @cps,  
                    	qtyord12 = qtyord12 + @qtyord,
                    	ordcnt12 = ordcnt12 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 13
		BEGIN
                	UPDATE ItemHistory 
			SET cps13 = cps13 + @cps,  
                    	qtyord13 = qtyord13 + @qtyord,
                    	ordcnt13 = ordcnt13 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 14
		BEGIN
                	UPDATE ItemHistory 
			SET cps14 = cps14 + @cps,  
                    	qtyord14 = qtyord14 + @qtyord,
                    	ordcnt14 = ordcnt14 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 15
		BEGIN
                	UPDATE ItemHistory 
			SET cps15 = cps15 + @cps, 
                    	qtyord15 = qtyord15 + @qtyord,
                    	ordcnt15 = ordcnt15 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 16
		BEGIN
                	UPDATE ItemHistory 
			SET cps16 = cps16 + @cps,  
                    	qtyord16 = qtyord16 + @qtyord,
                    	ordcnt16 = ordcnt16 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 17
		BEGIN
                	UPDATE ItemHistory 
			SET cps17 = cps17 + @cps,
                    	qtyord17 = qtyord17 + @qtyord,
                    	ordcnt17 = ordcnt17 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 18
		BEGIN
                	UPDATE ItemHistory 
			SET cps18 = cps18 + @cps,
                    	qtyord18 = qtyord18 + @qtyord,
                    	ordcnt18 = ordcnt18 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item	
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 19
		BEGIN
                	UPDATE ItemHistory 
			SET cps19 = cps19 + @cps,
                    	qtyord19 = qtyord19 + @qtyord,
                    	ordcnt19 = ordcnt19 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 20
		BEGIN
                	UPDATE ItemHistory 
			SET cps20 = cps20 + @cps,
                    	qtyord20 = qtyord20 + @qtyord,
                    	ordcnt20 = ordcnt20 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 21
		BEGIN
                	UPDATE ItemHistory 
			SET cps21 = cps21 + @cps,
                    	qtyord21 = qtyord21 + @qtyord,
                    	ordcnt21 = ordcnt21 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 22
		BEGIN
                	UPDATE ItemHistory 
			SET cps22 = cps22 + @cps,
                    	qtyord22 = qtyord22 + @qtyord,
                    	ordcnt22 = ordcnt22 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 23
		BEGIN
                	UPDATE ItemHistory 
			SET cps23 = cps23 + @cps,
                    	qtyord23 = qtyord23 + @qtyord,
                    	ordcnt23 = ordcnt23 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 24
		BEGIN
                	UPDATE ItemHistory 
			SET cps24 = cps24 + @cps,
                    	qtyord24 = qtyord24 + @qtyord,
                    	ordcnt24 = ordcnt24 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 25
		BEGIN
                	UPDATE ItemHistory 
			SET cps25 = cps25 + @cps,
                    	qtyord25 = qtyord25 + @qtyord,
                    	ordcnt25 = ordcnt25 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 26
		BEGIN
                	UPDATE ItemHistory 
			SET cps26 = cps26 + @cps,
                    	qtyord26 = qtyord26 + @qtyord,
                    	ordcnt26 = ordcnt26 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 27
		BEGIN
                	UPDATE ItemHistory 
			SET cps27 = cps27 + @cps,  
                   	qtyord27= qtyord27 + @qtyord,
                   	ordcnt27 = ordcnt27 + @ordcnt
                   	WHERE lcid = @lcid
                   	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 28
		BEGIN
                	UPDATE ItemHistory 
			SET cps28 = cps28 + @cps,  
                    	qtyord28 = qtyord28 + @qtyord,
                    	ordcnt28 = ordcnt28 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 29
		BEGIN
                	UPDATE ItemHistory 
			SET cps29 = cps29 + @cps,  
                    	qtyord29 = qtyord29 + @qtyord,
                    	ordcnt29 = ordcnt29 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 30
		BEGIN
                	UPDATE ItemHistory 
			SET cps30 = cps30 + @cps,
                    	qtyord30 = qtyord30 + @qtyord,
                    	ordcnt30 = ordcnt30 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 31
		BEGIN
                	UPDATE ItemHistory 
			SET cps31 = cps31 + @cps,
                    	qtyord31 = qtyord31 + @qtyord,
                    	ordcnt31 = ordcnt31 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 32
		BEGIN
                	UPDATE ItemHistory 
			SET cps32 = cps32 + @cps,
                    	qtyord32 = qtyord32 + @qtyord,
                    	ordcnt32 = ordcnt32 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 33
 		BEGIN
                	UPDATE ItemHistory 
			SET cps33 = cps33 + @cps,
                    	qtyord33 = qtyord33 + @qtyord,
                    	ordcnt33 = ordcnt33 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 34
		BEGIN
                	UPDATE ItemHistory 
			SET cps34 = cps34 + @cps,
                    	qtyord34 = qtyord34 + @qtyord,
                    	ordcnt34 = ordcnt34 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 35
 		BEGIN
                	UPDATE ItemHistory 
			SET cps35 = cps35 + @cps,
                    	qtyord35 = qtyord35 + @qtyord,
                    	ordcnt35 = ordcnt35 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 36
		BEGIN
                	UPDATE ItemHistory 
			SET cps36 = cps36 + @cps,
                    	qtyord36 = qtyord36 + @qtyord,
                    	ordcnt36 = ordcnt36 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                  	AND hisyear = @fy
                END        
            	IF @pd = 37
		BEGIN
                	UPDATE ItemHistory 
			SET cps37 = cps37 + @cps,  
                    	qtyord37= qtyord37 + @qtyord,
                    	ordcnt37 = ordcnt37 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 38
		BEGIN
                	UPDATE ItemHistory 
			SET cps38 = cps38 + @cps,  
                    	qtyord38 = qtyord38 + @qtyord,
                    	ordcnt38 = ordcnt38 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 39
		BEGIN
                	UPDATE ItemHistory 
			SET cps39 = cps39 + @cps,  
                    	qtyord39 = qtyord39 + @qtyord,
                    	ordcnt39 = ordcnt39 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 40
 		BEGIN
                	UPDATE ItemHistory 
			SET cps40 = cps40 + @cps,
                    	qtyord40 = qtyord40 + @qtyord,
                    	ordcnt40 = ordcnt40 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 41
 		BEGIN
                	UPDATE ItemHistory 
			SET cps41 = cps41 + @cps,
                    	qtyord41 = qtyord41 + @qtyord,
                    	ordcnt41 = ordcnt41 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 42
 		BEGIN
                	UPDATE ItemHistory 
			SET cps42 = cps42 + @cps,
                    	qtyord42 = qtyord42 + @qtyord,
                    	ordcnt42 = ordcnt42 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 43
 		BEGIN
                	UPDATE ItemHistory 
			SET cps43 = cps43 + @cps,
                    	qtyord43 = qtyord43 + @qtyord,
                    	ordcnt43 = ordcnt43 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 44
 		BEGIN
                	UPDATE ItemHistory 
			SET cps44 = cps44 + @cps,
                    	qtyord44 = qtyord44 + @qtyord,
                    	ordcnt44 = ordcnt44 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 45
 		BEGIN
                	UPDATE ItemHistory 
			SET cps45 = cps45 + @cps,
                   	qtyord45 = qtyord45 + @qtyord,
                   	ordcnt45 = ordcnt45 + @ordcnt
                   	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 46
 		BEGIN
                	UPDATE ItemHistory 
			SET cps46 = cps46 + @cps,
                    	qtyord46 = qtyord46 + @qtyord,
                    	ordcnt46 = ordcnt46 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 47
 		BEGIN
                	UPDATE ItemHistory 
			SET cps47 = cps47 + @cps,  
                    	qtyord47= qtyord47 + @qtyord,
                    	ordcnt47 = ordcnt47 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 48
 		BEGIN
                	UPDATE ItemHistory 
			SET cps48 = cps48 + @cps,  
                    	qtyord48 = qtyord48 + @qtyord,
                    	ordcnt48 = ordcnt48 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 49
 		BEGIN
                	UPDATE ItemHistory 
			SET cps49 = cps49 + @cps,  
                    	qtyord49 = qtyord49 + @qtyord,
                    	ordcnt49 = ordcnt49 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 50
 		BEGIN
                	UPDATE ItemHistory 
			SET cps50 = cps50 + @cps,
                    	qtyord50 = qtyord50 + @qtyord,
                    	ordcnt50 = ordcnt50 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 51
 		BEGIN
                	UPDATE ItemHistory 
			SET cps51 = cps51 + @cps,
                    	qtyord51 = qtyord51 + @qtyord,
                    	ordcnt51 = ordcnt51 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
                END        
            	IF @pd = 52
 		BEGIN
                	UPDATE ItemHistory 
			SET cps52 = cps52 + @cps,
                    	qtyord52 = qtyord52 + @qtyord,
                    	ordcnt52 = ordcnt52 + @ordcnt
                    	WHERE lcid = @lcid
                    	AND item = @item
			AND subsitem =@subsitem
                    	AND hisyear = @fy
		END
  	END
END
CLOSE ItemList
DEALLOCATE ItemList

RETURN

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

