SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

/****** Object:  Stored Procedure dbo.DPAIM_GetFcstTypes_Sp    Script Date: 10/18/2004 11:31:48 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[DPAIM_GetFcstTypes_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[DPAIM_GetFcstTypes_Sp]
GO





/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: DPAIM_GetFcstTypes_Sp
**	Desc: Retrieves the FcstTypes that are active
**
**	Returns: 1)A recordset 
**             
**	Values:  
**              
**	Auth:  Srinivas Uddanti
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/

CREATE    PROCEDURE DPAIM_GetFcstTypes_Sp
@FcstId           		nvarchar(12)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/



SET NOCOUNT ON

select RepositoryKey,FcstStartDate,FcstInterval,FcstPds_Future,FcstPds_Historical,calc_sysfcst,calc_masterfcstadj,calc_fcstadj,calc_netreq,
calc_adjnetreq,calc_projinv,calc_histdmd,Calc_ProdConst
From AIMFcstSetup,forecastrepository
Where AIMFcstSetup.FcstId=forecastrepository.FcstId
and AIMFcstSetup.FcstId =@FcstId
  

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

	RETURN 0  -- SUCCESSFUL





GO


SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

