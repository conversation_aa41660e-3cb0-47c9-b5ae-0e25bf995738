SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_GenerateForecastRepository_List_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_GenerateForecastRepository_List_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON><PERSON><PERSON>, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
** 	Name: AIM_GenerateForecastRepository_List_Sp
** 	Desc: Gets Forecast Repository for the VB function to retrieve to get Item,
**       LocationID's
**
** 	Returns: 1)  0 - Successful
**               2) -1 - No Data Found
**               3) -2 - SQL Error
** 	Values:  Recordset - Item and Location for a Forecast ID
**              
** 	Auth:   Srinivas Uddanti
** 	Date:   08/23/2002
*******************************************************************************
** 	Change History
*******************************************************************************
** 	Date:      Author:      Description:
** 	---------- ------------ -----------------------------------------------
**      08/23/2002 Wade Riza    Update Return Codes and Error handling
*******************************************************************************/

CREATE PROCEDURE AIM_GenerateForecastRepository_List_Sp
(
    @FcstID                 nvarchar(12),
    @FcstType               nvarchar(30)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found              int,
	@RepositoryKey          int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

IF @FcstType IS NULL
BEGIN
	RETURN -1
END

SELECT @RepositoryKey = RepositoryKey FROM ForecastRepository
WHERE FcstID = @FcstID and FcstType = @FcstType

SELECT @found = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	  RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found > 0 
BEGIN
	SELECT DISTINCT forecastrepositorydetail.RepositoryKey,item.item,item.lcid,item.itdesc,
	COALESCE(item.class1,'')class1,COALESCE(item.class2,'')class2,
	COALESCE(item.class3,'') class3,COALESCE(item.class4,'')class4,
	COALESCE(item.itstat,'') itstat,COALESCE(item.velcode,'') velcode,
	COALESCE(item.Cube,0) cube,COALESCE(item.weight,0) weight,
	COALESCE(item.Vnid,'') VnID,COALESCE(item.Assort,'') Assort,
	COALESCE(item.ById,'') ById
	FROM forecastrepositorydetail INNER JOIN item ON
	forecastrepositorydetail.item =item.item AND
	forecastrepositorydetail.lcid =item.lcid
	WHERE forecastrepositorydetail.repositorykey =@RepositoryKey
	ORDER BY item.lcid,item.item

  	-- Check for SQL Server errors.
  	IF @@ERROR <> 0 
  	BEGIN
  		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END
  	ELSE
  	BEGIN
  		RETURN 0  -- SUCCESSFUL
  	END
END
ELSE
BEGIN
	RETURN -1  -- ERROR (NO DATA FOUND)
END  

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

