SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_AIMForecast_Delete_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_AIMForecast_Delete_Sp]
GO

/******************************************************************************
*******************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_AIMForecast_Delete_Sp
**	Desc: Deletes AIMForecast and all children tables.
**
**	Returns: 1)@found - Can be Zero
**             2) -1 - No Data Found
**             3) -2 - SQL Error
**		 4) -3 - No Access
**	Values:  
**              
**	Auth:   Wade Riza	
**	Date:   09/19/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**    
*******************************************************************************/

CREATE PROCEDURE AIM_AIMForecast_Delete_Sp
(
 	@FcstID  			nvarchar(12),
 	@UserID   			nvarchar(12) = " "
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found        		int,
 	@AccessType 			int,
	@FcstLocked			nvarchar(1),
	@rtrn				int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID Is Null
OR @UserID Is Null
BEGIN
  	RETURN -1
END

BEGIN
	EXEC @rtrn = AIM_ForecastMainUserAccess_Validate_Sp @UserID, @FcstID, @AccessType OUTPUT

	SELECT @AccessType

	IF @rtrn < 0 
	BEGIN
		SET @AccessType = 0
	END	
END

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

SELECT @FcstLocked = FcstLocked FROM AIMForecast
WHERE FcstID = @FcstID

SELECT @FcstLocked

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @AccessType >= 2 AND @FcstLocked = 'N'
BEGIN

	BEGIN TRAN --Start of Transaction
	
	Delete FROM AIMForecastUserAccess
	WHERE FcstID = @FcstID

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
  	BEGIN
		ROLLBACK TRAN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END

  	DELETE FROM AIMForecastMainUserAccess
	WHERE FcstID = @FcstID

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
  	BEGIN
		ROLLBACK TRAN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END

	DELETE FROM AIMForecastFreezePds
	WHERE FcstID = @FcstID

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
  	BEGIN
		ROLLBACK TRAN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END

	DELETE FROM AIMForecastDetail
	WHERE FcstID = @FcstID

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
  	BEGIN
		ROLLBACK TRAN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END

 	DELETE FROM AIMForecast
	WHERE FcstID = @FcstID

		-- Check for SQL Server errors.
	IF @@ERROR <> 0 
  	BEGIN
		ROLLBACK TRAN
 		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
  	END

	COMMIT TRAN  -- End of Transaction

	RETURN 0
END
ELSE
BEGIN
 	RETURN -3 -- Not Permitted Premissions
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

