SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_QuickOrder_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_QuickOrder_Sp]
GO

/******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_QuickOrder_Sp
**	Desc: Updates the Item table with data from the override.
**
**	Returns: 1) @@recordcount - Can be Zero
**               2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/
     
CREATE PROCEDURE AIM_QuickOrder_Sp 
(
	@VnIdKey			nvarchar(12),
     	@AssortKey			nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @RC 							int, 
    	@ReviewTotal 					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @VnIdKey IS NULL
BEGIN
	RETURN 0
END

IF @AssortKey IS NULL
BEGIN
	RETURN 0
END

-- Set parameter values
SELECT @RC = 0, @ReviewTotal = 0 

EXEC @RC = AIM_OrdGenCtrl_Sp 'V', 'ALL', @VnIdKey, @AssortKey, 'N', 'N', 'N', 'Y', 
    	@ReviewTotal OUTPUT , 0, 0, 0

SELECT 'Lines Reviewed' = @ReviewTotal

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

