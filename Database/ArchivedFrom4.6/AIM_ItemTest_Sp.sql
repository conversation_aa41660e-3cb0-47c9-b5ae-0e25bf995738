SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ItemTest_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ItemTest_Sp]
GO


/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ItemTest_Sp
**	Desc: 
**
**	Returns: 1)@@recordcount - Can be Zero
**             2) 0 - Failure
**             
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 	Aug-13-2003 Srinivas U  Made it more generic
** 	Sep-12-2003 Srinivas U	Modified code for SubsItem
*******************************************************************************/

CREATE PROCEDURE AIM_ItemTest_Sp 
(	
     	@LcId 						nvarchar(12) ='', 
      	@Item 						nvarchar(25)='' 
)
  
-- 	WITH ENCRYPTION /* Production use must be encrypted */
AS /* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @OptionId AS 					nvarchar(6),
    	@SaId AS					nvarchar(20),
    	@SaVersion AS 					int,
    	@ItStat AS 					nvarchar(1),
    	@DmdUpd AS 					nvarchar(1),
    	@VnId AS 					nvarchar(12),
    	@Assort AS 					nvarchar(12),
    	@RevCycle AS 					nvarchar(8),
    	@HisCount AS 					int
  
SET NOCOUNT ON
     
  -- Get the Current Seasonality Version from System Control

  If @lcid ='' and @Item =''
begin
	declare List cursor local forward_only Static for
	select distinct lcid, item
	from item
	group by lcid,item
	order by lcid,item
end
If @lcid =''and @Item <> ''
begin
	declare List cursor local forward_only Static for
	select lcid,item from item
	where item =@Item
end
If @lcid <>'' and @Item =''
begin
	declare List cursor local forward_only Static for
	select lcid,item from item
	where lcid =@lcid
end

open list
fetch next from list into
@lcid,@Item
while @@fetch_Status =0
Begin

  SELECT @SaVersion = CurSaVersion 
  FROM SysCtrl
      
  SELECT @LcId = Lcid, @Item = Item, @OptionId = OptionId, @SaId = SaId,
      	@ItStat = ItStat, @VnId = VnId, @Assort = Assort
  FROM Item 
  WHERE Item.Lcid = @Lcid and Item.Item = @Item
      
  IF @@rowcount = 0
  BEGIN
    	PRINT 'Item Not in Database: ' + @LcId + '-' + @Item
      	--RETURN
	Goto GetNextRow
  END
  ELSE
  BEGIN
      	PRINT 'Item Found: ' + @LcId + '-' + @Item
  END
      
  SELECT @OptionId = OptionId 
  FROM AIMOptions 
  WHERE OptionId = @OptionId
      
  IF @@rowcount = 0
  BEGIN
    	PRINT 'Invalid Option Id: ' + @OptionId
  END
  ELSE
  BEGIN
      	PRINT 'Option Id Ok: ' + @OptionId
  END
      
  SELECT @SaId = SaId 
  FROM AIMSeasons 
  WHERE SaId = @SaId
      
  IF @@rowcount = 0
  BEGIN
    	PRINT 'Invalid Seasonality Id: ' + @SaId + ' (' + ltrim(str(@SaVersion)) + ')'
  END
  ELSE
  BEGIN
    	PRINT 'Seasonality Id Ok: ' + @SaId + ' (' + ltrim(str(@SaVersion)) + ')'
  END    
  
  SELECT @DmdUpd = 'N'
  
  SELECT @ItStat = ItStat, @DmdUpd = DmdUpd 
  FROM ItStatus 
  WHERE ItStat = @ItStat
      
  IF @@rowcount = 0
  BEGIN
    	PRINT 'Invalid Status Code: ' + @ItStat  
  END
  ELSE
  BEGIN
      	PRINT 'Status Code Ok: ' + @ItStat
      	IF @DmdUpd = 'Y'
      	BEGIN
		PRINT ' Item included in Demand Update'
      	END
	ELSE
      	BEGIN
		PRINT ' Item excluded from Demand Update'
      	END
  END
      
  SELECT @VnId = VnId, @Assort = Assort, @RevCycle = RevCycle 
  FROM AIMVendors 
  WHERE VnId = @VnId
  AND Assort = @Assort
      
  IF @@rowcount = 0
  BEGIN
    	PRINT 'Invalid Vendor/Assortment: ' + rtrim(@VnId) + '/' + @Assort
  END
  ELSE
  BEGIN
     	PRINT 'Vendor/Assortment Ok: ' + rtrim(@VnId) + '/' + @Assort
       
      	SELECT @RevCycle = RevCycle 
      	FROM RevCycles
      	WHERE RevCycle = @RevCycle
      
      	IF @@rowcount = 0
      	BEGIN
		PRINT 'Invalid Review Cycle: ' + @RevCycle
      	END
	ELSE
      	BEGIN
		PRINT 'Review Cycle Ok: ' + @RevCycle
        END
  END
      
  SELECT @HisCount = 0
  
  SELECT @HisCount = COUNT(*) 
  FROM ItemHistory 
  WHERE LcId = @LcId 
  AND Item = @Item
  ANd SubsItem =@Item
      
  PRINT 'History Records: ' + ltrim(str(@HisCount))
      
  PRINT ''
GetNextRow:

 Fetch next from list into @Lcid,@Item
End 



GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

