SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepositoryDetail_Save_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepositoryDetail_Save_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON><PERSON>GIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepositoryDetail_Save_Sp
**	Desc: Inserts Forecast Repository Detail data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**		 3) -1 - No Data Found
**               4) -2 - SQL Error
**             
**	Auth:   Wade Riza 
**	Date:   07/31/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza	Updated Return codes and Error handling
**      09/27/2002 Wade Riza	Updated SP for two new columns 
**                              (FcstPdStartDate & QtyProjectedInventory) 
*******************************************************************************/
     
CREATE PROCEDURE AIM_ForecastRepositoryDetail_Save_Sp
(
    @RepositoryKey					int,
    @LcID		        			nvarchar(12), 
    @Item		        			nvarchar(25), 
    @FcstPds						tinyint,
    @FcstPdStartDate		        	        datetime, 
    @PdSumYear				                smallint,
    @PdSumQtr						tinyint = NULL,
    @PdSumMonth 					tinyint = NULL,
    @PdSumWeek					        tinyint = NULL,
    @PdSumDay					        tinyint = NULL,
    @QtyFcst					        int,
    @QtyFcstNetReq					int = NULL,
    @QtyAdjust					        int = NULL,
    @QtyActualOrdered				        int = NULL,
    @QtyActualShipped				        int = NULL,
    @QtyProjectedInventory				int = NULL
)	

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      					int
            
SET NOCOUNT ON 

-- Validate the required parameters.
IF @RepositoryKey IS NULL
BEGIN
	RETURN -1
END

IF @LcID IS NULL
BEGIN
	RETURN -1
END

IF @Item IS NULL
BEGIN
	RETURN -1
END

IF @FcstPds IS NULL
BEGIN
	RETURN -1
END

IF @FcstPdStartDate IS NULL
BEGIN
	RETURN -1
END

IF @PdSumYear IS NULL
BEGIN
	RETURN -1
END

IF @QtyFcst IS NULL
BEGIN
	RETURN -1
END

SELECT @RepositoryKey = RepositoryKey FROM ForecastRepositoryDetail
WHERE RepositoryKey = @RepositoryKey
AND Item = @Item
AND LcId = @LcId
AND FcstPds = @FcstPds
        
SELECT @found = @@rowcount
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0		-- Insert new Repository Detail record
BEGIN
      INSERT INTO ForecastRepositoryDetail
            (RepositoryKey, LcID, Item, FcstPds, FcstPdStartDate, 
            PdSumYear, PdSumQtr,PdSumMonth, PdSumWeek, PdSumDay,
            QtyFcst, QtyFcstNetReq, QtyAdjust, 
	    QtyActualOrdered, QtyActualShipped, QtyProjectedInventory)
            VALUES (@RepositoryKey, @LcID, @Item, @FcstPds, @FcstPdStartDate, 
            @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay,  
            @QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
	    @QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory)
       
	SELECT @found = @@rowcount

        -- Check for SQL Server errors.
        IF @@ERROR <> 0 
        BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
        END
        ELSE
	BEGIN
		RETURN 0 -- SUCCESSFUL
	END
END
ELSE			-- Update existing Repository Detail record
BEGIN
        UPDATE ForecastRepositoryDetail
            SET FcstPdStartDate = @FcstPdStartDate, PdSUmYear = @PdSumYear, 
            PdSumQtr = @PdSumQtr, PdSumMonth = @PdSumMonth,
            PdSumWeek = @PdSumWeek, PdSumDay = @PdSumDay,
            QtyFcst = @QtyFcst, QtyFcstNetReq = @QtyFcstNetReq,
            QtyAdjust = @QtyAdjust, QtyActualOrdered = @QtyActualOrdered,
            QtyActualShipped = @QtyActualShipped, QtyProjectedInventory = @QtyProjectedInventory            
    	    WHERE RepositoryKey = @RepositoryKey
            AND LcId = @LcId
    	    AND Item = @Item
    	    AND FcstPds = @FcstPds

    	SELECT @found = @@rowcount

	-- Check for SQL Server errors.
	IF @@ERROR <> 0 
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

    	IF @found > 0
    	BEGIN
		RETURN 1 -- UPDATE Record Updated
    	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR No Data Found
	END
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

