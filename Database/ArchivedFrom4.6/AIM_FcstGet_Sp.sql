SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_FcstGet_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_FcstGet_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND <PERSON><PERSON><PERSON> NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_FcstGet_Sp
**	Desc: Gets the data needed for forecasting 
**
**	Returns: 1) @@rowcount
**	Values:  
**              
**	Auth:   Randy Sadler
**	Date:   03/20/2002
*******************************************************************************
**	Change History
*******************************************************************************
**    Date:	 Author:      Description:
**    ---------- ------------ -----------------------------------------------
**    
*******************************************************************************/
     
CREATE PROCEDURE AIM_FcstGet_Sp 
(
  	@VnId						nvarchar(12),
  	@Assort						nvarchar(12)
)
  
-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/
  
DECLARE @SAVersion 					smallint

SET NOCOUNT ON

-- Get the Current Seasonality Version Number
SELECT @SAVersion = CurSaVersion FROM SysCtrl
  -- Retrieve the data needed for forecasting
SELECT Item.Item, Item.Lcid, Item.ItDesc, Item.ItStat, Item.ActDate, Item.InActDate, 
    	Item.VelCode, Item.VnId, Item.Assort, Item.ById, Item.MDC, Item.MDCFlag,
       	Item.SaId, Item.PmId, Item.Weight, Item.Cube, Item.Price, Item.Cost, Item.UOM, 
    	Item.ConvFactor, Item.BuyingUOM, Item.Oh, Item.Oo, Item.ComStk, 
       	Item.BkOrder, Item.BkComStk, Item.FcstMethod, Item.FcstDemand, Item.UserFcst, 
    	Item.UserFcstExpDate, Item.MAE, Item.Trend, Item.FcstUpdCyc, Item.PlnTT, 
    	Item.Accum_Lt, Item.ReviewTime, Item.OrderPt, Item.OrderQty, Item.SafetyStock, 
    	Item.FcstRT, Item.FcstLT, Item.Fcst_Month, Item.Fcst_Quarter, Item.Fcst_Year, 
      	AIMOptions.HiTrndL, 
       	AIMSeasons.BI01, AIMSeasons.BI02, AIMSeasons.BI03, 
       	AIMSeasons.BI04, AIMSeasons.BI05, AIMSeasons.BI06, 
       	AIMSeasons.BI07, AIMSeasons.BI08, AIMSeasons.BI09, 
       	AIMSeasons.BI10, AIMSeasons.BI11, AIMSeasons.BI12, 
       	AIMSeasons.BI13, AIMSeasons.BI14, AIMSeasons.BI15, 
       	AIMSeasons.BI16, AIMSeasons.BI17, AIMSeasons.BI18, 
       	AIMSeasons.BI19, AIMSeasons.BI20, AIMSeasons.BI21, 
       	AIMSeasons.BI22, AIMSeasons.BI23, AIMSeasons.BI24, 
       	AIMSeasons.BI25, AIMSeasons.BI26, AIMSeasons.BI27, 
       	AIMSeasons.BI28, AIMSeasons.BI29, AIMSeasons.BI30, 
       	AIMSeasons.BI31, AIMSeasons.BI32, AIMSeasons.BI33, 
       	AIMSeasons.BI34, AIMSeasons.BI35, AIMSeasons.BI36, 
       	AIMSeasons.BI37, AIMSeasons.BI38, AIMSeasons.BI39, 
       	AIMSeasons.BI40, AIMSeasons.BI41, AIMSeasons.BI42, 
       	AIMSeasons.BI43, AIMSeasons.BI44, AIMSeasons.BI45, 
       	AIMSeasons.BI46, AIMSeasons.BI47, AIMSeasons.BI48, 
       	AIMSeasons.BI49, AIMSeasons.BI50, AIMSeasons.BI51, 
       	AIMSeasons.BI52, 
    	AIMVendors.VName,
    	AIMLocations.DmdScalingFactor, AIMLocations.ScalingEffUntil,
    	PmStatus = isnull(AIMPromotions.PmStatus, 0), 
    	PmStartDate = isnull(AIMPromotions.PmStartDate, '01/01/1990'), 
    	PmEndDate = isnull(AIMPromotions.PmEndDate, '01/01/1990'), 
    	PmAdj01 = isnull(AIMPromotions.PmAdj01,1), PmAdj02 = isnull(AIMPromotions.PmAdj02,1), 
    	PmAdj03 = isnull(AIMPromotions.PmAdj03,1), PmAdj04 = isnull(AIMPromotions.PmAdj04,1), 
    	PmAdj05 = isnull(AIMPromotions.PmAdj05,1), PmAdj06 = isnull(AIMPromotions.PmAdj06,1), 
    	PmAdj07 = isnull(AIMPromotions.PmAdj07,1), PmAdj08 = isnull(AIMPromotions.PmAdj08,1), 
    	PmAdj09 = isnull(AIMPromotions.PmAdj09,1), PmAdj10 = isnull(AIMPromotions.PmAdj10,1), 
    	PmAdj11 = isnull(AIMPromotions.PmAdj11,1), PmAdj12 = isnull(AIMPromotions.PmAdj12,1), 
    	PmAdj13 = isnull(AIMPromotions.PmAdj13,1), PmAdj14 = isnull(AIMPromotions.PmAdj14,1), 
    	PmAdj15 = isnull(AIMPromotions.PmAdj15,1), PmAdj16 = isnull(AIMPromotions.PmAdj16,1), 
    	PmAdj17 = isnull(AIMPromotions.PmAdj17,1), PmAdj18 = isnull(AIMPromotions.PmAdj18,1), 
    	PmAdj19 = isnull(AIMPromotions.PmAdj19,1), PmAdj20 = isnull(AIMPromotions.PmAdj20,1), 
    	PmAdj21 = isnull(AIMPromotions.PmAdj21,1), PmAdj22 = isnull(AIMPromotions.PmAdj22,1), 
    	PmAdj23 = isnull(AIMPromotions.PmAdj23,1), PmAdj24 = isnull(AIMPromotions.PmAdj24,1), 
    	PmAdj25 = isnull(AIMPromotions.PmAdj25,1), PmAdj26 = isnull(AIMPromotions.PmAdj26,1), 
    	PmAdj27 = isnull(AIMPromotions.PmAdj27,1), PmAdj28 = isnull(AIMPromotions.PmAdj28,1), 
    	PmAdj29 = isnull(AIMPromotions.PmAdj29,1), PmAdj30 = isnull(AIMPromotions.PmAdj30,1), 
    	PmAdj31 = isnull(AIMPromotions.PmAdj31,1), PmAdj32 = isnull(AIMPromotions.PmAdj32,1), 
    	PmAdj33 = isnull(AIMPromotions.PmAdj33,1), PmAdj34 = isnull(AIMPromotions.PmAdj34,1), 
    	PmAdj35 = isnull(AIMPromotions.PmAdj35,1), PmAdj36 = isnull(AIMPromotions.PmAdj36,1), 
    	PmAdj37 = isnull(AIMPromotions.PmAdj37,1), PmAdj38 = isnull(AIMPromotions.PmAdj38,1), 
    	PmAdj39 = isnull(AIMPromotions.PmAdj39,1), PmAdj40 = isnull(AIMPromotions.PmAdj40,1), 
    	PmAdj41 = isnull(AIMPromotions.PmAdj41,1), PmAdj42 = isnull(AIMPromotions.PmAdj42,1), 
    	PmAdj43 = isnull(AIMPromotions.PmAdj43,1), PmAdj44 = isnull(AIMPromotions.PmAdj44,1), 
    	PmAdj45 = isnull(AIMPromotions.PmAdj45,1), PmAdj46 = isnull(AIMPromotions.PmAdj46,1), 
    	PmAdj47 = isnull(AIMPromotions.PmAdj47,1), PmAdj48 = isnull(AIMPromotions.PmAdj48,1), 
    	PmAdj49 = isnull(AIMPromotions.PmAdj49,1), PmAdj50 = isnull(AIMPromotions.PmAdj50,1), 
    	PmAdj51 = isnull(AIMPromotions.PmAdj51,1), PmAdj52 = isnull(AIMPromotions.PmAdj52,1),
    	AIMMethods.ApplySeasonsIndex, AIMMethods.ApplyTrend
        FROM Item 
        INNER JOIN AIMOptions ON Item.OptionID = AIMOptions.OptionId 
        INNER JOIN AIMSeasons ON Item.SaId = AIMSeasons.SaId
            AND AIMSeasons.SAVersion = @SAVersion 
        INNER JOIN ItStatus ON Item.ItStat = ItStatus.ItStat 
    	INNER JOIN AIMLocations on Item.Lcid = AIMLocations.Lcid
        INNER JOIN AIMVendors ON Item.VnId = AIMVendors.VnId
            AND Item.Assort = AIMVendors.Assort
        INNER JOIN RevCycles ON AIMVendors.RevCycle = RevCycles.RevCycle 
    	LEFT OUTER JOIN AIMPromotions ON Item.PmId = AIMPromotions.PmId
    	INNER JOIN AIMMethods ON Item.FcstMethod = AIMMethods.MethodId
        WHERE Item.VnId = @VnId
    	AND Item.Assort = @Assort
        AND ItStatus.DmdUpd = 'Y'
        ORDER BY Item.Item, Item.Lcid

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

