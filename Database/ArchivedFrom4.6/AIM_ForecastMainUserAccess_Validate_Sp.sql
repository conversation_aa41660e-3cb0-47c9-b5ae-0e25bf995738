SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastMainUserAccess_Validate_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastMainUserAccess_Validate_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastMainUserAccess_Validate_Sp
**	Desc: Validates the Users Update rights for Forecast Maintenance.
**
**	Returns: 1)@found - Can be Zero
**               2) -1 - No Data Found
**               3) -2 - SQL Error
**	Values:  Recordset - AccessType
**              
**	Auth:   Wade Riza 
**	Date:   08/29/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
** 
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastMainUserAccess_Validate_Sp
(
	@UserID						nvarchar(12),
	@FcstID						nvarchar(12),
	@AccessType					nvarchar(30) OUTPUT
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						

DECLARE @found					int

SET NOCOUNT ON

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

IF @UserID IS NULL
BEGIN
	RETURN -1
END

IF @UserID = 'sa'
BEGIN
  	SET @AccessType = 2  -- Create & Modify	
  	RETURN 0  -- Successful  
END

SELECT FcstID FROM AIMForecast
WHERE FcstID = @FcstID

SELECT @found = @@rowcount
  
-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found = 0 
BEGIN
	SET @AccessType = 2  -- Create & Modify
	RETURN 0  -- Successful
END

SET @Found = 0
	
SELECT @AccessType = AccessType FROM AIMForecastMainUserAccess
WHERE FcstID = @FcstID and UserID = @UserID

SELECT @found = @@rowcount
SELECT @AccessType

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @found < 1 
BEGIN
	SET @AccessType = 0  -- No Access	
	RETURN 0  -- Successful
END
ELSE IF @AccessType = 1  -- Read Only Access
BEGIN
	RETURN 0  -- Successful
END
ELSE IF @AccessType = 2  -- Create & Modify	
BEGIN
	RETURN 0  -- Successful  
END
ELSE IF @AccessType = 3  -- Create	
BEGIN
	RETURN 0  -- Successful  
END
ELSE IF @AccessType = 4  -- Modify	
BEGIN
	RETURN 0  -- Successful  
END
ELSE
BEGIN
	RETURN -1  -- Error (Unknown Access)
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

