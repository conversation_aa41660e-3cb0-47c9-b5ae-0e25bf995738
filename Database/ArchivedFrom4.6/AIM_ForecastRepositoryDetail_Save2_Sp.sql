SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS OFF 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepositoryDetail_Save2_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepositoryDetail_Save2_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECHN<PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepositoryDetail_Save2_Sp
**	Desc: Inserts Forecast Repository Detail data
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**		 3) -1 - No Data Found
**               4) -2 - SQL Error
**             
**	Auth:   Osama Riyahi
**	Date:   07/31/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	Description:
**	---------- ------------	-----------------------------------------------
*******************************************************************************/
CREATE PROCEDURE AIM_ForecastRepositoryDetail_Save2_Sp
(
	@RepositoryKey						int,
        @LcID		        				nvarchar(12),
        @Item		        				nvarchar(25),
	@FcstPdStartDate					nvarchar(400),
	@QtyFcst					        nvarchar(250),
	@QtyFcstNetReq						nvarchar(250),
	@QtyAdjust					        nvarchar(250),
	@QtyActualOrdered					nvarchar(250),
	@QtyActualShipped					nvarchar(250),
	@QtyProjectedInventory				        nvarchar(250),
	@BatchSysFcst						nvarchar(1),
	@PlannedRct						nvarchar(250),
	@FcstPds						nvarchar(250),
	@CurrentPd						int
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/

DECLARE @found      					int

  SET nocount ON

  -- Validate the required parameters.
  IF @RepositoryKey IS NULL
  BEGIN
	RETURN -1
  END

  IF @LcID IS NULL
  BEGIN
	RETURN -1
  END

  IF @Item IS NULL
  BEGIN
	RETURN -1
  END

  IF @FcstPdStartDate IS NULL
  BEGIN
	RETURN -1
  END

  IF @QtyFcst IS NULL
  BEGIN
	RETURN -1
  END

SELECT Forecastrepositorydetail.*,ForecastrepositoryBatch.PlannedRct
into
#RepDetailRec
from Forecastrepositorydetail,ForecastrepositoryBatch
where 1 = 2

declare @pos int
declare @cnt int
declare @StartPds int
select @StartPds = convert(int,substring(@FcstPds,1, CHARINDEX ( ',' , @FcstPds , 1 ) -1))
select @cnt = @StartPds
while @FcstPdStartDate <> ''
	begin
		select substring(@FcstPdStartDate,1, CHARINDEX ( ',' , @FcstPdStartDate , 1 ) -1)
		insert into #RepDetailRec
		values(@Repositorykey,@Lcid,@item,
			@cnt,
			substring(@FcstPdStartDate,1, CHARINDEX ( ',' , @FcstPdStartDate , 1 ) -1),
			0,0,0,0,0,0,0,0,0,0,0,0)
		select @FcstPdStartDate = substring(@FcstPdStartDate,CHARINDEX ( ',' , @FcstPdStartDate , 1 )+1,len(@FcstPdStartDate)-CHARINDEX ( ',' , @FcstPdStartDate , 1 ))
		select @pos = CHARINDEX ( ',' , @FcstPdStartDate , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @FcstPdStartDate = ''
		end
	end 
select @cnt = @StartPds
while @QtyFcst <> ''
	begin 
		select substring(@QtyFcst,1, CHARINDEX ( ',' , @QtyFcst , 1 ) -1)
		update #RepDetailRec set QtyFcst = substring(@QtyFcst,1, CHARINDEX ( ',' , @QtyFcst , 1 ) -1)
		where fcstpds = @cnt
		select @QtyFcst = substring(@QtyFcst,CHARINDEX ( ',' , @QtyFcst , 1 )+1,len(@QtyFcst)-CHARINDEX ( ',' , @QtyFcst , 1 ))
		select @pos = CHARINDEX ( ',' , @QtyFcst , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @QtyFcst = ''
		end
	end 
select @cnt = @StartPds
while @QtyFcstNetReq <> ''
	begin 
		select substring(@QtyFcstNetReq,1, CHARINDEX ( ',' , @QtyFcstNetReq , 1 ) -1)
		update #RepDetailRec set QtyFcstNetReq = substring(@QtyFcstNetReq,1, CHARINDEX ( ',' , @QtyFcstNetReq , 1 ) -1)
		where fcstpds = @cnt
		select @QtyFcstNetReq = substring(@QtyFcstNetReq,CHARINDEX ( ',' , @QtyFcstNetReq , 1 )+1,len(@QtyFcstNetReq)-CHARINDEX ( ',' , @QtyFcstNetReq , 1 ))
		select @pos = CHARINDEX ( ',' , @QtyFcstNetReq , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @QtyFcstNetReq = ''
		end
	end 

select @cnt = @StartPds
while @QtyAdjust <> ''
	begin 
		select substring(@QtyAdjust,1, CHARINDEX ( ',' , @QtyAdjust , 1 ) -1)
		update #RepDetailRec set QtyAdjust = substring(@QtyAdjust,1, CHARINDEX ( ',' , @QtyAdjust , 1 ) -1)
		where fcstpds = @cnt
		select @QtyAdjust = substring(@QtyAdjust,CHARINDEX ( ',' , @QtyAdjust , 1 )+1,len(@QtyAdjust)-CHARINDEX ( ',' , @QtyAdjust , 1 ))
		select @pos = CHARINDEX ( ',' , @QtyAdjust , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @QtyAdjust = ''
		end
	end 
select @cnt = @StartPds
while @QtyActualOrdered <> ''
	begin 
		select substring(@QtyActualOrdered,1, CHARINDEX ( ',' , @QtyActualOrdered , 1 ) -1)
		update #RepDetailRec set QtyActualOrdered = substring(@QtyActualOrdered,1, CHARINDEX ( ',' , @QtyActualOrdered , 1 ) -1)
		where fcstpds = @cnt
		select @QtyActualOrdered = substring(@QtyActualOrdered,CHARINDEX ( ',' , @QtyActualOrdered , 1 )+1,len(@QtyActualOrdered)-CHARINDEX ( ',' , @QtyActualOrdered , 1 ))
		select @pos = CHARINDEX ( ',' , @QtyActualOrdered , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @QtyActualOrdered = ''
		end
	end 
select @cnt = @StartPds
while @QtyActualShipped <> ''
	begin 
		select substring(@QtyActualShipped,1, CHARINDEX ( ',' , @QtyActualShipped , 1 ) -1)
		update #RepDetailRec set QtyActualShipped = substring(@QtyActualShipped,1, CHARINDEX ( ',' , @QtyActualShipped , 1 ) -1)
		where fcstpds = @cnt
		select @QtyActualShipped = substring(@QtyActualShipped,CHARINDEX ( ',' , @QtyActualShipped , 1 )+1,len(@QtyActualShipped)-CHARINDEX ( ',' , @QtyActualShipped , 1 ))
		select @pos = CHARINDEX ( ',' , @QtyActualShipped , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @QtyActualShipped = ''
		end
	end 
select @cnt = @StartPds
while @QtyProjectedInventory <> ''
	begin 
		select substring(@QtyProjectedInventory,1, CHARINDEX ( ',' , @QtyProjectedInventory , 1 ) -1)
		update #RepDetailRec set QtyProjectedInventory = substring(@QtyProjectedInventory,1, CHARINDEX ( ',' , @QtyProjectedInventory , 1 ) -1)
		where fcstpds = @cnt
		select @QtyProjectedInventory = substring(@QtyProjectedInventory,CHARINDEX ( ',' , @QtyProjectedInventory , 1 )+1,len(@QtyProjectedInventory)-CHARINDEX ( ',' , @QtyProjectedInventory , 1 ))
		select @pos = CHARINDEX ( ',' , @QtyProjectedInventory , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
		select @QtyProjectedInventory = ''
		end
	end 
select @cnt = @StartPds
while @PlannedRct <> ''
	begin 
		select substring(@PlannedRct,1, CHARINDEX ( ',' , @PlannedRct , 1 ) -1)
		update #RepDetailRec set PlannedRct = substring(@PlannedRct,1, CHARINDEX ( ',' , @PlannedRct , 1 ) -1)
		where fcstpds = @cnt
		select @PlannedRct = substring(@PlannedRct,CHARINDEX ( ',' , @PlannedRct , 1 )+1,len(@PlannedRct)-CHARINDEX ( ',' , @PlannedRct , 1 ))
		select @pos = CHARINDEX ( ',' , @PlannedRct , 1 )
		select @cnt = @cnt + 1
		if @pos = 0
		begin
			select @PlannedRct = ''
		end
	end 

 
	insert into ForecastRepositoryDetail(RepositoryKey, LcID, Item, FcstPds, FcstPdStartDate,
            				     PdSumYear, PdSumQtr,PdSumMonth, PdSumWeek, PdSumDay,
     					     QtyFcst, QtyFcstNetReq, QtyAdjust,QtyActualOrdered, 
					     QtyActualShipped, QtyProjectedInventory)
	select RepositoryKey, LcID, Item, FcstPds, FcstPdStartDate,
               PdSumYear, PdSumQtr,PdSumMonth, PdSumWeek, PdSumDay,
  	       QtyFcst, QtyFcstNetReq, QtyAdjust,QtyActualOrdered, 
	       QtyActualShipped, QtyProjectedInventory 
	from #RepDetailRec R1
	where not exists (select 'x' from ForecastRepositoryDetail F1
		  	where R1.RepositoryKey = F1.RepositoryKey
		  	and	R1.Lcid = F1.Lcid
		  	and	R1.Item = F1.Item
		  	and	R1.FcstPds = f1.FcstPds)

 

        UPDATE ForecastRepositoryDetail
            SET QtyFcst = R1.QtyFcst, 
		QtyFcstNetReq = R1.QtyFcstNetReq,
            	QtyAdjust = R1.QtyAdjust, 
		QtyActualOrdered = R1.QtyActualOrdered,
            	QtyActualShipped = R1.QtyActualShipped, 
		QtyProjectedInventory = R1.QtyProjectedInventory
	    From #RepDetailRec R1
    	    WHERE ForecastRepositoryDetail.RepositoryKey = R1.RepositoryKey
            AND ForecastRepositoryDetail.LcId = R1.LcId
    	    AND ForecastRepositoryDetail.Item = R1.Item
	    AND ForecastRepositoryDetail.FcstPds = R1.FcstPds
	    AND ForecastRepositoryDetail.FcstPds > @CurrentPd




	insert into ForecastRepositoryBatch(RepositoryKey, LcID, Item, FcstPds, ModSysFcst, ModSysNet, PlannedRct)
	select RepositoryKey, LcID, Item, FcstPds, QtyFcst, QtyFcstNetReq, PlannedRct
	from #RepDetailRec R1
	where not exists (select 'x' from ForecastRepositoryBatch F1
		  	where R1.RepositoryKey = F1.RepositoryKey
		  	and	R1.Lcid = F1.Lcid
		  	and	R1.Item = F1.Item
		  	and	R1.FcstPds = f1.FcstPds)


	UPDATE ForecastRepositoryBatch
	   SET ModSysFcst = R1.QtyFcst,
	            ModSysNet =R1.QtyFcstNetReq,
	            PlannedRct=R1.PlannedRct
	    From #RepDetailRec R1
    	    WHERE ForecastRepositoryBatch.RepositoryKey = R1.RepositoryKey
            AND ForecastRepositoryBatch.LcId = R1.LcId
    	    AND ForecastRepositoryBatch.Item = R1.Item
  	    AND ForecastRepositoryBatch.FcstPds = R1.FcstPds
	    AND ForecastRepositoryBatch.FcstPds > @CurrentPd

	-- Check for SQL Server errors.
	IF @@ERROR <> 0
	BEGIN
		RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
	END

    	IF @found > 0
    	BEGIN
		RETURN 1 -- UPDATE Record Updated
    	END
	ELSE
	BEGIN
		RETURN -1 -- ERROR No Data Found
	END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

