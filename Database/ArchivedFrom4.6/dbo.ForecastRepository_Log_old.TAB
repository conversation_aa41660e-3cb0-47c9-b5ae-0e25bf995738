/****** Object:  Table [dbo].[ForecastRepository_Log_old]    Script Date: 10/12/2004 10:54:09 AM ******/
if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[ForecastRepository_Log_old]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[ForecastRepository_Log_old]
GO

CREATE TABLE [dbo].[ForecastRepository_Log_old] (
	[FcstRepositoyKey] [numeric](18, 0) IDENTITY (1, 1) NOT NULL ,
	[FcstID] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[FcstType] [nvarchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[UserIDEdit] [nvarchar] (12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DateTimeEdit] [datetime] NOT NULL ,
	CONSTRAINT [PK_ForecastRepository_Log] PRIMARY KEY  CLUSTERED 
	(
		[FcstRepositoyKey]
	)  ON [PRIMARY] 
) ON [PRIMARY]
GO


