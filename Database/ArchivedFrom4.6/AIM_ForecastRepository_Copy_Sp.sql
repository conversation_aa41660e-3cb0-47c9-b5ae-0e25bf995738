SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AIM_ForecastRepository_Copy_Sp]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
drop procedure [dbo].[AIM_ForecastRepository_Copy_Sp]
GO

/*******************************************************************************
********************************************************************************
**
**                       NOTICE
**
**  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
**  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
**  TECH<PERSON><PERSON>OGIES, INC., AND SHALL NOT BE COPIED,
**  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
**  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
**  INTENDED, IN THE EVENT OF PUBLICATION, THE
**  FOLLOWING NOTICE IS APPLICABLE:
**
**  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
**
**           SSA GLOBAL TECHNOLOGIES, INC.
**
********************************************************************************
********************************************************************************
**	Name: AIM_ForecastRepository_Copy_Sp
**	Desc: Copies Forecast Repository data from one record to another.
**
**	Returns: 1)  0 - Successful Insert
**               2)  1 - Successful Update
**               3) -1 - No Data Found
**               4) -2 - SQL Error
**               5) -3 - Error Data does not match in Header
**               6) -4 - Error Unable to ALTER  Header
**              
**	Auth:   Wade Riza 
**	Date:   08/08/2002
*******************************************************************************
**	Change History
*******************************************************************************
**	Date:	   Author:	    Description:
**	---------- ------------	-----------------------------------------------
**      08/15/2002 Wade Riza    Updated Return Codes and Error Handling
**      09/24/2002 Wade Riza	Fixed error in Update logic
**      09/27/2002 Wade Riza	Updated SP for two new columns 
**                              (FcstPdStartDate & QtyProjectedInventory) 
*******************************************************************************/

CREATE PROCEDURE AIM_ForecastRepository_Copy_Sp
(
	@FcstID						nvarchar(12),
	@FcstTypeFrom					nvarchar(30),
	@FcstTypeTo					nvarchar(30),
	@CurrentPd					int,
	@FcstDesc					nvarchar(40) = NULL,
	@FcstComment					nvarchar(4000) = NULL,
	@UserID						nvarchar(12)
)

-- WITH ENCRYPTION	/* Production use must be encrypted */
AS	/* Internal use only - Unencrypted. Switch with Production use clause before release*/						
 
DECLARE @foundfrom					int,
	@foundto					int,
	@RepositoryKeyFrom				int,
	@RepositoryKeyOrg				int,
	@RepositoryKey					int,
	@FcstVersionOrg					bigint,
	@FcstVersion					bigint,
	@FcstStartDateOrg				datetime,
	@FcstStartDate					datetime,
	@FcstIntervalOrg				int,
	@FcstInterval					int,
	@CurrentDate					datetime,
	@FcstDescOrg					nvarchar(40),
	@FcstCommentOrg					nvarchar(4000),
	@FcstPeriod					int,
	@Return						int,
	@LcID						nvarchar(12),
	@Item						nvarchar(25),
	@FcstPds					int,
	@FcstPdStartDate				datetime,
	@PdSumYear					int,
	@PdSumQtr					int,
	@PdSumMonth					int,
	@PdSumWeek					int,
	@PdSumDay					int,
	@QtyFcst					int,
	@QtyFcstNetReq					int,
	@QtyAdjust					int,
	@QtyActualOrdered				int,
	@QtyActualShipped				int,
	@QtyProjectedInventory				int

SET NOCOUNT ON

SELECT @CurrentDate = GetDate()

SELECT @RepositoryKey = Null
SELECT @RepositoryKeyOrg = Null
SELECT @RepositoryKeyFrom = Null

-- Validate the required parameters.
IF @FcstID IS NULL
BEGIN
	RETURN -1
END

IF @FcstTypeFrom IS NULL
BEGIN
	RETURN -1
END

IF @FcstTypeTo IS NULL
BEGIN
	RETURN -1
END

IF @CurrentPd IS NULL
BEGIN
	RETURN -1
END

IF @UserID IS NULL
BEGIN
	RETURN -1
END

-- Validating the existance of the From record.
SELECT @RepositoryKeyFrom = RepositoryKey, @FcstStartDate = FcstStartDate, @FcstInterval = FcstInterval
FROM ForecastRepository
WHERE FcstID = @FcstID and FcstType = @FcstTypeFrom and FcstVersion = 
(SELECT Max(FcstVersion) FROM ForecastRepository WHERE FcstID = @FcstID and FcstType = @FcstTypeFrom)
SELECT @foundfrom = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
  	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

IF @foundfrom = 0 OR @foundfrom IS NULL OR @RepositoryKeyFrom IS NULL
BEGIN
RETURN -1 -- ERROR (No FROM TYPE Data found for this Forecast ID.)
END	

SELECT @RepositoryKeyOrg = RepositoryKey, @FcstStartDateOrg = FcstStartDate, @FcstIntervalOrg = FcstInterval,
@FcstDescOrg = FcstDesc, @FcstCommentOrg = FcstComment, @FcstVersionOrg = FcstVersion
FROM ForecastRepository
WHERE FcstID = @FcstID and FcstType = @FcstTypeTo and FcstVersion = 
	(SELECT Max(FcstVersion) FROM ForecastRepository WHERE FcstID = @FcstID and FcstType = @FcstTypeTo)

SELECT @foundto = @@rowcount

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END

-- Validating that the parameters match between the FROM and TO records
IF @foundto > 0 AND @foundto IS NOT NULL
BEGIN
	IF (@FcstStartDate <> @FcstStartDateOrg) OR (@FcstInterval <> @FcstIntervalOrg) 
	BEGIN
		RETURN -3 -- ERROR (Forecast Data does not match.)
	END
	ELSE
	BEGIN
		SELECT @FcstDesc = @FcstDescOrg
		SELECT @FcstComment = @FcstCommentOrg + ' ' + @FcstComment
	END
END
ELSE 
BEGIN 
	SELECT @RepositoryKeyOrg = 0
	SELECT @FcstVersionOrg = 0
END

BEGIN TRAN  --Start of Transaction	

-- Sending the Header Data to Stored Procedure for it to determine what to do.
EXEC AIM_ForecastRepository_Save_Sp @FcstID, @FcstDesc, @FcstTypeTo, @FcstStartDate, @FcstInterval,
  @FcstComment, @UserID, @RepositoryKey OUTPUT

SELECT @RepositoryKey

-- Check for SQL Server errors.
IF @@ERROR <> 0 
BEGIN
	ROLLBACK TRAN
 	RETURN -2  -- ERROR (Undetermined SQL ERROR, Add code in future to return the SQL ERROR)
END
	
IF @RepositoryKey IS NULL
BEGIN
	ROLLBACK TRAN
	RETURN -4  -- ERROR (Unable to create record or update the Header data.)
END

-- Validating if this is a creation of a new Header or an update of an existing Header record.
IF @RepositoryKey = @RepositoryKeyOrg
BEGIN
	IF @CurrentPd > 1
	BEGIN
		DECLARE cForecastRepositoryDetailNewOrg CURSOR
		LOCAL
		FOR SELECT LcID, Item, FcstPds, FcstPdStartDate, PdSumYear, PdSumQtr, PdSumMonth, PdSumWeek, PdSumDay, 
		QtyFcst, QtyFcstNetReq, QtyAdjust, QtyActualOrdered, QtyActualShipped, QtyProjectedInventory
		FROM ForecastRepositoryDetail
		WHERE RepositoryKey = @RepositoryKeyFrom and FcstPds >= @CurrentPd

		OPEN cForecastRepositoryDetailNewOrg 

		FETCH cForecastRepositoryDetailNewOrg INTO @LcID, @Item, @FcstPds, @FcstPdStartDate, 
		@PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
		@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
		@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
		
		BEGIN TRAN NESTED --Start of Transaction
		
		WHILE (@@FETCH_STATUS = 0) 
		BEGIN
			EXEC AIM_ForecastRepositoryDetail_Save_Sp @RepositoryKey, @LcID, @Item, @FcstPds, 
				@FcstPdStartDate, @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
				@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
				@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
			
			FETCH NEXT FROM cForecastRepositoryDetailNewOrg INTO @LcID, @Item, @FcstPds, 
				@FcstPdStartDate, @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
				@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
				@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
		END
			
		COMMIT TRAN NESTED -- End of Transaction

		CLOSE cForecastRepositoryDetailNewOrg
		DEALLOCATE cForecastRepositoryDetailNewOrg

		COMMIT TRAN -- End of Transaction

		RETURN 0 -- SUCCESSFUL (Created new Forecast Repository Version with current and future data from the 'FROM TYPE' record.)

	END
	ELSE
	BEGIN
		DECLARE cForecastRepositoryDetailNew CURSOR
		LOCAL
		FOR SELECT LcID, Item, FcstPds, FcstPdStartDate, PdSumYear, PdSumQtr, PdSumMonth, PdSumWeek, PdSumDay, 
		QtyFcst, QtyFcstNetReq, QtyAdjust, QtyActualOrdered, QtyActualShipped, QtyProjectedInventory 
		FROM ForecastRepositoryDetail
		WHERE RepositoryKey = @RepositoryKeyFrom
	
		OPEN cForecastRepositoryDetailNew 
	
		FETCH cForecastRepositoryDetailNew INTO @LcID, @Item, @FcstPds, @FcstPdStartDate, 
		@PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
		@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
		@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
	
		BEGIN TRAN NESTED --Start of Transaction

		WHILE (@@FETCH_STATUS = 0) 
		BEGIN
			EXEC AIM_ForecastRepositoryDetail_Save_Sp @RepositoryKey, @LcID, @Item, @FcstPds, 
				@FcstPdStartDate, @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
				@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
				@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
			
			FETCH NEXT FROM cForecastRepositoryDetailNew INTO @LcID, @Item, @FcstPds, 
				@FcstPdStartDate, @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
				@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
				@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
		END

		COMMIT TRAN NESTED -- End of Transaction
   			
		CLOSE cForecastRepositoryDetailNew
		DEALLOCATE cForecastRepositoryDetailNew

		COMMIT TRAN -- End of Transaction
		
		RETURN 0 -- SUCCESSFUL (Created new Forecast Repository record with future data from the 'FROM TYPE' record.)
	END
END	
ELSE
BEGIN
	DECLARE cForecastRepositoryDetailUpdate CURSOR
	LOCAL
	FOR SELECT LcID, Item, FcstPds, FcstPdStartDate, PdSumYear, PdSumQtr, PdSumMonth, PdSumWeek, PdSumDay,
	QtyFcst, QtyFcstNetReq, QtyAdjust, QtyActualOrdered, QtyActualShipped, QtyProjectedInventory
	FROM ForecastRepositoryDetail
	WHERE RepositoryKey = @RepositoryKeyFrom

	OPEN cForecastRepositoryDetailUpdate

	FETCH cForecastRepositoryDetailUPdate INTO @LcID, @Item, @FcstPds, @FcstPdStartDate, 
	@PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
	@QtyFcst, @QtyFcstNetReq, @QtyAdjust, 
	@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory

	BEGIN TRAN NESTED --Start of Transaction
	
	WHILE (@@FETCH_STATUS = 0) 
	BEGIN
		EXEC AIM_ForecastRepositoryDetail_Save_Sp @RepositoryKey, @LcID, @Item, @FcstPds, 
			@FcstPdStartDate, @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
			@QtyFcst, @QtyFcstNetReq, @QtyAdjust,  
			@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
			
		FETCH NEXT FROM cForecastRepositoryDetailUPdate INTO @LcID, @Item, @FcstPds, 
			@FcstPdStartDate, @PdSumYear, @PdSumQtr, @PdSumMonth, @PdSumWeek, @PdSumDay, 
			@QtyFcst, @QtyFcstNetReq, @QtyAdjust,  
			@QtyActualOrdered, @QtyActualShipped, @QtyProjectedInventory
	END

	COMMIT TRAN NESTED -- End of Transaction
   			
	CLOSE cForecastRepositoryDetailUpdate
	DEALLOCATE cForecastRepositoryDetailUpdate

	COMMIT TRAN -- End of Transaction

	RETURN 1 -- SUCCESSFUL (Updated EXisting Forecast Repository Version with current and future data from the 'FROM TYPE' record.)
END

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

